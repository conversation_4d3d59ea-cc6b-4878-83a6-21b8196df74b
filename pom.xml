<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<artifactId>pa-impl</artifactId>
	<packaging>jar</packaging>
	<groupId>com.nci.core.pa</groupId>
	<version>0.5.0</version>
	<parent>
		<groupId>com.nci.udmp</groupId>
		<artifactId>udmp-parent</artifactId>
		<version>0.5.5.5</version>
	</parent>

	<properties>
		<udmp.version>0.5.5.5</udmp.version>
		<common.version>0.5.5.3</common.version>
		<cap.version>0.5.0</cap.version>
		<clm.version>0.5.0</clm.version>
		<nb.version>0.5.4</nb.version>
		<uw.version>0.5.4</uw.version>
		<prd.version>0.0.9</prd.version>
		<!-- <bpm.version>0.1.0</bpm.version> -->
		<css.version>0.5.0</css.version>
		<qry.version>0.5.5.3</qry.version>
		<udmptool.version>0.8.0</udmptool.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>pa-interface</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nci.tunan.cap</groupId>
			<artifactId>cap-impl</artifactId>
			<version>${cap.version}</version>
		</dependency>
		<dependency>
			<artifactId>clm-impl</artifactId>
			<groupId>com.nci.core.clm</groupId>
			<version>${clm.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nci.core.nb</groupId>
			<artifactId>nb-interface</artifactId>
			<version>${nb.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nci.core.uw</groupId>
			<artifactId>uw-interface</artifactId>
			<version>${uw.version}</version>
		</dependency>

		<!-- 产品 -->
		<dependency>
			<groupId>com.nci.tunan.prd</groupId>
			<artifactId>prd-impl</artifactId>
			<version>${prd.version}</version>
		</dependency>

		<!-- UDMP工具类 -->
		<dependency>
			<groupId>com.nci.udmp</groupId>
			<artifactId>udmp-tools</artifactId>
			<version>${udmptool.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- 业务公共 -->
		<dependency>
			<groupId>com.nci.core.common</groupId>
			<artifactId>commonbiz-impl</artifactId>
			<version>${common.version}</version>
		</dependency>

		<!-- 柜面 -->
  		<dependency>
			<groupId>com.nci.core.css</groupId>
			<artifactId>css-interface</artifactId>
			<version>${css.version}</version>
		</dependency> 
		<!-- 综合查询 -->
  		<dependency>
			<groupId>com.nci.core.qry</groupId>
			<artifactId>qry-interface</artifactId>
			<version>${qry.version}</version>
		</dependency> 
		<!-- <dependency> -->
		<!-- <groupId>com.nci.core.prd</groupId> -->
		<!-- <artifactId>fms-biz</artifactId> -->
		<!-- <version>1.7.1</version> -->
		<!-- </dependency> -->

		<!-- 工作流 -->
		<!-- <dependency> -->
		<!-- <groupId>com.nci.core.bpm</groupId> -->
		<!-- <artifactId>bpm-Service-Stub</artifactId> -->
		<!-- <version>${bpm.version}</version> -->
		<!-- </dependency> -->

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- <dependency> -->
		<!-- <groupId>com.nci.tunan.prd</groupId> -->
		<!-- <artifactId>prd-impl</artifactId> -->
		<!-- <version>0.4.6</version> -->
		<!-- </dependency> -->

		<!-- <dependency> -->
		<!-- <groupId>com.nci.core.nb</groupId> -->
		<!-- <artifactId>gson</artifactId> -->
		<!-- <version>2.2.4</version> -->
		<!-- </dependency> -->


		<!-- <dependency> -->
		<!-- <groupId>ecss.easyclient</groupId> -->
		<!-- <artifactId>ecss-easyclient</artifactId> -->
		<!-- <version>0.8.5</version> -->
		<!-- </dependency> -->
		<!-- <dependency> -->
		<!-- <groupId>com.google.code.gson</groupId> -->
		<!-- <artifactId>gson</artifactId> -->
		<!-- <version>2.1</version> -->
		<!-- </dependency> -->
		<!-- 业务公共 -->
		<!-- <dependency> -->
		<!-- <groupId>com.nci.core.common</groupId> -->
		<!-- <artifactId>UW-adviceInput</artifactId> -->
		<!-- <version>0.0.1</version> -->
		<!-- </dependency> -->

		<!-- <dependency> -->
		<!-- <groupId>com.nci.core.pa</groupId> -->
		<!-- <artifactId>jsch</artifactId> -->
		<!-- <version>0.1</version> -->
		<!-- </dependency> -->
		<!-- 影像实例初始化依赖 -->
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>2.2</version>
		</dependency>

		<!-- <dependency>
			<groupId>com.nci.core.pa</groupId>
			<artifactId>PA-Import-Service-Stub</artifactId>
			<version>0.3.8.9</version>
		</dependency> -->

		<dependency>
			<groupId>com.nci.core.pa</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.51</version>
		</dependency>
	<dependency>
		<groupId>com.nci.core.drq</groupId>
		<artifactId>drqqt</artifactId>
		<version>0.0.1</version>
	</dependency>	
		<!-- <dependency> -->
		<!-- <groupId>com.nci.core.common</groupId> -->
		<!-- <artifactId>UW-adviceInput</artifactId> -->
		<!-- <version>0.0.1</version> -->
		<!-- </dependency> -->

		<!-- <dependency> -->
		<!-- <groupId>com.nci.core.css</groupId> -->
		<!-- <artifactId>CSS_EXPORT</artifactId> -->
		<!-- <version>0.2.2</version> -->
		<!-- </dependency> -->
		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpmime -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.3.1</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/com.rabbitmq/amqp-client  -->
		<dependency>
    		<groupId>com.rabbitmq</groupId>
    		<artifactId>amqp-client</artifactId>
   			 <version>4.8.0</version>
		</dependency> 
		
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>1.7</source>
					<target>1.7</target>
					<proc>none</proc>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
