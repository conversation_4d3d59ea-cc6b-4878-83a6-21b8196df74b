package com.nci.tunan.clm.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.clm.dao.IClaimHospitalServiceDao;
import com.nci.tunan.clm.interfaces.model.po.ClaimHospitalServicePO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * @description 理赔医院信息参数表DaoImpl
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统/责任明细
 * @date 2015-05-15 下午4:36:24
 */
public class ClaimHospitalServiceDaoImpl extends BaseDaoImpl implements IClaimHospitalServiceDao {
    /**
     * @Fields logger : 日志工具
     */
    private static Logger logger = LoggerFactory.getLogger();

    /**
     * @description 增加理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return ClaimHospitalServicePO 添加理赔医院信息参数结果
     */
    public ClaimHospitalServicePO addClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--addClaimHospitalService======>");
        return createObject("addClaimHospitalService", claimHospitalServicePO);
    }

    /**
     * @description 删除理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return boolean 删除理赔医院信息参数是否成功
     */
    public boolean deleteClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--deleteClaimHospitalService======>");
        return deleteObject("deleteClaimHospitalService", claimHospitalServicePO);
    }

    /**
     * @description 查询单条理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 医院信息参数对象
     * @return ClaimPhoneServicePO 查询理赔医院信息参数结果对象
     */
    public ClaimHospitalServicePO findClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimPhoneServiceDaoImpl--findClaimPhoneService======>");
        //1. zhangdong修改
        return findObject("findClaimHospitalServiceByHospitalId", claimHospitalServicePO);
    }
    /**
     * @description 查询单条理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR> <EMAIL> 
     * @param claimHospitalServicePO 医院信息参数
     * @return 医院信息参数
     */
    public ClaimHospitalServicePO queryClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimPhoneServiceDaoImpl--queryClaimHospitalService======>");
        return findObject("queryClaimHospitalService", claimHospitalServicePO);
    }

    /**
     * @description 修改理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return ClaimHospitalServicePO 修改理赔医院信息参数结果
     */
    public ClaimHospitalServicePO updateClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--updateClaimHospitalService======>");
        return updateObject("updateClaimHospitalService", claimHospitalServicePO);
    }

    /**
     * @description 查询所有理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return List<ClaimHospitalServicePO> 查询理赔医院信息参数结果List
     */
    public List<ClaimHospitalServicePO> findAllClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--findAllClaimHospitalService======>");
        return findAll("clm-impl.src.main.resources.META-INF.clm.mybatis.hospital.findAllClaimHospitalService", claimHospitalServicePO);
    }

    /**
     * @description 查询理赔医院信息参数数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return int 查询理赔医院信息参数结果条数
     */
    public int findClaimHospitalServiceTotal(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--findClaimHospitalServiceTotal======>");
        return findCount("findClaimHospitalServiceTotal", claimHospitalServicePO);
    }

    /**
     * @description 分页查询理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页理赔医院信息参数对象
     * @return CurrentPage<ClaimHospitalServicePO> 查询理赔医院信息参数结果的当前页对象
     */
    public CurrentPage<ClaimHospitalServicePO> queryClaimHospitalServiceForPage(
            ClaimHospitalServicePO claimHospitalServicePO, CurrentPage<ClaimHospitalServicePO> currentPage) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--queryClaimHospitalServiceForPage======>");
        currentPage.setParamObject(claimHospitalServicePO);
        return queryForPage("findClaimHospitalServiceTotal", "queryClaimHospitalServiceForPage", currentPage);
    }

    /**
     * @description 批量增加理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePOList 医院信息参数对象列表
     * @return boolean 批量添加理赔医院信息参数是否成功
     */
    public boolean batchSaveClaimHospitalService(List<ClaimHospitalServicePO> claimHospitalServicePOList) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--batchSaveClaimHospitalService======>");
        return batchSave("addClaimHospitalService", claimHospitalServicePOList);
    }

    /**
     * @description 批量修改理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePOList 医院信息参数对象列表
     * @return boolean 批量修改理赔医院信息参数是否成功
     */
    public boolean batchUpdateClaimHospitalService(List<ClaimHospitalServicePO> claimHospitalServicePOList) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--batchUpdateClaimHospitalService======>");
        return batchUpdate("updateClaimHospitalService", claimHospitalServicePOList);
    }

    /**
     * @description 批量删除理赔医院信息参数数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePOList 医院信息参数对象列表
     * @return boolean 批量删除理赔医院信息参数是否成功
     */
    public boolean batchDeleteClaimHospitalService(List<ClaimHospitalServicePO> claimHospitalServicePOList) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--batchDeleteClaimHospitalService======>");
        return batchDelete("deleteClaimHospitalService", claimHospitalServicePOList);
    }

    /**
     * @description 查询所有理赔医院信息参数数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return List<Map<String, Object>> 查询理赔医院信息参数结果存放到map中
     */
    public List<Map<String, Object>> findAllMapClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO) {
        logger.debug("<======ClaimHospitalServiceDaoImpl--findAllMapClaimHospitalService======>");
        return findAllMap("findAllMapClaimHospitalService", claimHospitalServicePO);
    }
}
