package com.nci.tunan.clm.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.clm.dao.IClaimSubCaseDao;
import com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description ClaimSubCaseDaoImpl赔案子表实现类
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-子赔案信息
 * @date 2015-07-23 14:03:04  
 */
public class ClaimSubCaseDaoImpl  extends BaseDaoImpl  implements IClaimSubCaseDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePO 赔案子表对象
     * @return ClaimSubCasePO 添加子赔案信息结果
     */
	 public ClaimSubCasePO addClaimSubCase(ClaimSubCasePO claimSubCasePO) {
	 	 logger.debug("<======ClaimSubCaseDaoImpl--addClaimSubCase======>");
		 return createObject("addClaimSubCase",  claimSubCasePO) ;
	 }
    
     /**
     * @description 删除子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePO 赔案子表对象
     * @return boolean 删除子赔案信息是否成功
     */
 	 public boolean deleteClaimSubCase(ClaimSubCasePO claimSubCasePO) {
 	 	 logger.debug("<======ClaimSubCaseDaoImpl--deleteClaimSubCase======>");
		 return deleteObject("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.deleteClaimSubCase",  claimSubCasePO) ;
	 }
	/**
     * @description 修改子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePO 赔案子表对象
     * @return ClaimSubCasePO 修改子赔案信息结果
     */
 	 public ClaimSubCasePO updateClaimSubCase(ClaimSubCasePO claimSubCasePO) {
 	 	 logger.debug("<======ClaimSubCaseDaoImpl--updateClaimSubCase======>");
		 return updateObject("updateClaimSubCase",  claimSubCasePO) ;
 	 }	

    /**
     * @description 根据主键查询子赔案信息单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePO 赔案子表对象
     * @return ClaimSubCasePO 查询子赔案信息结果对象
     */
	 public ClaimSubCasePO findClaimSubCase(ClaimSubCasePO claimSubCasePO) {
	  	logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCase======>");
		return findObject("findClaimSubCaseBySubCaseId",  claimSubCasePO) ;
	 }
	 
	 
	  /**
	     * @description 根据case_id和理赔类型查询子赔案信息单条数据
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimSubCasePO 赔案子表对象
	     * @return ClaimSubCasePO 查询子赔案信息结果对象
	     */
		 public ClaimSubCasePO findClaimSubCaseByCondition(ClaimSubCasePO claimSubCasePO) {
		  	logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseByCondition======>");
			return findObject("findClaimSubCaseByCondition",  claimSubCasePO) ;
		 }
	
	 /**
     * @description 查询所有子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePO 赔案子表对象
     * @return List<ClaimSubCasePO> 查询子赔案信息结果List
     */
	 public List<ClaimSubCasePO> findAllClaimSubCase(ClaimSubCasePO claimSubCasePO) {
	 	logger.debug("<======ClaimSubCaseDaoImpl--findAllClaimSubCase======>");
		return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findAllClaimSubCase",  claimSubCasePO) ;
	 }
	
	 /**
     * @description 查询子赔案信息数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePO 赔案子表对象
     * @return int 查询子赔案信息结果条数
     */
	 public int findClaimSubCaseTotal(ClaimSubCasePO claimSubCasePO) {
	 	logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseTotal======>");
		return findCount("findClaimSubCaseTotal",  claimSubCasePO) ;
	 }
	
	 /**
     * @description 分页查询子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页子赔案信息对象
     * @return CurrentPage<ClaimSubCasePO> 查询结果的当前页子赔案信息对象
     */
	 public CurrentPage<ClaimSubCasePO> queryClaimSubCaseForPage(ClaimSubCasePO claimSubCasePO, CurrentPage<ClaimSubCasePO> currentPage) {
		logger.debug("<======ClaimSubCaseDaoImpl--queryClaimSubCaseForPage======>");
		currentPage.setParamObject(claimSubCasePO);
		return queryForPage("findClaimSubCaseTotal", "queryClaimSubCaseForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePOList 赔案子表子赔案信息对象列表
     * @return boolean 批量添加子赔案信息是否成功
     */
	 public boolean batchSaveClaimSubCase(List<ClaimSubCasePO> claimSubCasePOList) {
	 	logger.debug("<======ClaimSubCaseDaoImpl--batchSaveClaimSubCase======>");
		return batchSave("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.addClaimSubCase", claimSubCasePOList) ;
	 }
	
	 /**
     * @description 批量修改子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePOList 赔案子表对象列表
     * @return boolean 批量修改子赔案信息是否成功
     */
	 public boolean batchUpdateClaimSubCase(List<ClaimSubCasePO> claimSubCasePOList) {
	 	logger.debug("<======ClaimSubCaseDaoImpl--batchUpdateClaimSubCase======>");
		return batchUpdate("updateClaimSubCase", claimSubCasePOList) ;
	 }
	
	/**
     * @description 批量删除子赔案信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePOList 赔案子表对象列表
     * @return boolean 批量删除子赔案信息是否成功
     */
	 public boolean batchDeleteClaimSubCase(List<ClaimSubCasePO> claimSubCasePOList) {
	 	logger.debug("<======ClaimSubCaseDaoImpl--batchDeleteClaimSubCase======>");
		return batchDelete("deleteClaimSubCase", claimSubCasePOList) ;
	 }
	
	 /**
     * @description 查询所有子赔案信息数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCasePO 赔案子表对象
     * @return List<Map<String, Object>> 查询子赔案信息结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapClaimSubCase(ClaimSubCasePO claimSubCasePO) {
	 	logger.debug("<======ClaimSubCaseDaoImpl--findAllMapClaimSubCase======>");
		return findAllMap("findAllMapClaimSubCase", claimSubCasePO) ;
	 }
	 /**
	     * @description 查询所有子赔案信息数据通过caseId
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimSubCasePO 赔案子表对象
	     * @return List<ClaimSubCasePO> 查询子赔案信息结果List
	     */

	  public List<ClaimSubCasePO> findAllClaimSubCaseByCaseId(ClaimSubCasePO claimSubCasePO) {
         logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseByCaseId======>");
         return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findClaimSubCaseByCaseId",  claimSubCasePO) ;
      }
	  /**
       * @description 查询所有子赔案信息数据通过caseId
       * @version
       * @title
       * <AUTHOR>
       * @param claimSubCasePO 赔案子表对象
       * @return List<ClaimSubCasePO> 查询子赔案信息结果List
       */
	  public List<ClaimSubCasePO> findDistinctClaimTypeByCaseId(ClaimSubCasePO claimSubCasePO) {
	         logger.debug("<======ClaimSubCaseDaoImpl--findDistinctClaimTypeByCaseId======>");
	         return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findDistinctClaimTypeByCaseId",  claimSubCasePO) ;
	  }
	  
	  /**
	   * 根据理赔类型和CaseId查询数据。并且按照出险日期排序
	   * @description
	   * @version V1.0.0
	   * @title
	   * <AUTHOR> <EMAIL>
	   * @param claimSubCasePO 赔案子表
	   * @return  赔案子表
	   */
	  public List<ClaimSubCasePO> findAllClaimSubCaseByCaseIdAndClaimType(ClaimSubCasePO claimSubCasePO) {
	      logger.debug("<======ClaimSubCaseDaoImpl--findAllClaimSubCaseByCaseIdAndClaimType======>");
	      return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findAllClaimSubCaseByCaseIdAndClaimType",  claimSubCasePO) ;
	  }
	  /**
	     * @description 删除子赔案信息数据
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimSubCasePO 赔案子表对象
	     */
	     public void deleteClaimSubCaseByCaseId(ClaimSubCasePO claimSubCasePO) {
	         logger.debug("<======ClaimSubCaseDaoImpl--deleteClaimSubCaseByCaseId======>");
	         deleteObject("deleteClaimSubCaseByCaseId",  claimSubCasePO) ;
	     }
	     /**
	      * @description 增加子赔案信息数据
	      * @version
	      * @title
	      * <AUTHOR>
	      * @param claimSubCasePO 赔案子表对象
	      */
	      public void addClaimSubCaseInfo(List<ClaimSubCasePO> addClaimSubCasePOs) {
	          logger.debug("<======ClaimSubCaseDaoImpl--addClaimSubCase======>");
	          batchSave("addClaimSubCase",  addClaimSubCasePOs) ;
	      }
	      /**
	         * 
	         * @description   删除子赔案信息数据
	         * @version V1.0.0
	         * @title
	         * <AUTHOR> <EMAIL>
	         * @date 2015-05-15 上午9:53:57 
	         * @param claimSubCasePO  赔案子表赔案子表
	         */
        @Override
        public void deleteClaimSubCaseByConditions(ClaimSubCasePO claimSubCasePO) {
            logger.debug("<======ClaimSubCaseDaoImpl--deleteClaimSubCaseByConditions======>");
            deleteObject("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.deleteClaimSubCaseByConditions",  claimSubCasePO) ;
        }
        /**
	      * @description 索引查询子赔案信息数据
	      * @version
	      * @title
	      * <AUTHOR>
	      * @param claimSubCasePO 赔案子表对象
	      * @return ClaimSubCasePO 赔案子表添加结果
	      */
        public ClaimSubCasePO findClaimSubCaseBySubCaseId(ClaimSubCasePO claimSubCasePO) {
    	  	logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseBySubCaseId======>");
    		return findObject("findClaimSubCaseBySubCaseId",  claimSubCasePO) ;
    	 }
        /**
	      * @description 根据赔案id查询
	      * @version
	      * @title
	      * <AUTHOR>
	      * @param claimSubCasePO 赔案子表对象
	      * @return ClaimSubCasePO 赔案子表集合
	      */
        public List<ClaimSubCasePO> findClaimSubCaseByCaseId(ClaimSubCasePO claimSubCasePO) {
        	logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseBySubCaseId======>");
        	return findAll("findByCaseId",  claimSubCasePO) ;
        }
        /**
         * 
         * @description   查询既往子赔案根据caseid
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @date 2015-05-15 上午9:59:42 
         * @see com.nci.tunan.clm.dao.IClaimSubCaseDao#findRedoSubCaseByCaseId(com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO)
         * @param claimSubCasePO  赔案子表
         * @return  赔案子表集合
         */
        public List<ClaimSubCasePO> findRedoSubCaseByCaseId(ClaimSubCasePO claimSubCasePO) {
        	logger.debug("<======ClaimSubCaseDaoImpl--findRedoSubCaseByCaseId======>");
        	return findAll("findRedoSubCaseByCaseId",  claimSubCasePO) ;
        }
        /**
	      * @description 索引查询多条数据并按照插入日期排序
	      * @version
	      * @title
	      * <AUTHOR>
	      * @param claimSubCasePO 赔案子表对象
	      * @return ClaimSubCasePO 赔案子表集合
	      */
		@Override
		public List<ClaimSubCasePO> findAllClaimSubCaseEnd(ClaimSubCasePO subPO) {
			logger.debug("<======ClaimSubCaseDaoImpl--findAllClaimSubCaseEnd======>");
    		return findAll("findAllClaimSubCaseEnd",  subPO) ;
		}
		/**
         * 查询出险日期
         * @description
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @date 2015-05-15 上午9:54:31 
         * @param claimSubCasePO  赔案子表
         * @return  赔案子表集合
         */
		public List<ClaimSubCasePO> findClaimDatesByCaseId(ClaimSubCasePO claimSubCasePO) {
	         logger.debug("<======ClaimSubCaseDaoImpl--findClaimDatesByCaseId======>");
	         return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findClaimDatesByCaseId",  claimSubCasePO) ;
	      }
		/**
         * 
         * @description 根据caseID查询出险日期
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @date 2015-05-15 上午9:56:02 
         * @param claimSubCasePO  赔案子表
         * @return  赔案子表集合
         */
		@Override
		public List<ClaimSubCasePO> findClaimSubCaseDistinct(
				ClaimSubCasePO claimSubCasePO) {
			logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseDistinct======>");
	         return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findClaimSubCaseDistinct",  claimSubCasePO) ;
		}
		/**
		 * 
		 * @description   根据赔案ID查询最大的出险日期
		 * @version V1.0.0
		 * @title
		 * <AUTHOR> <EMAIL>
		 * @date 2015-05-15 上午9:55:00 
		 * @param claimCaseSubPO  赔案子表
		 * @return  赔案子表集合
		 */
		@Override
		public List<ClaimSubCasePO> findMaxClaimDateByCaseId(ClaimSubCasePO claimCaseSubPO) {
			logger.debug("<======ClaimSubCaseDaoImpl--findMaxClaimDateByCaseId======>");
	        return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findMaxClaimDateByCaseId",  claimCaseSubPO) ;
		}
		/**
	       * @description 查询所有数据通过caseId
	       * @version
	       * @title
	       * <AUTHOR>
	       * @param claimSubCasePO 赔案子表对象
	       * @return List<ClaimSubCasePO> 查询结果List
	       */
		  @Override
	        public List<ClaimSubCasePO> findClaimSubCaseTypeByCaseId(
	                ClaimSubCasePO claimCaseSubPO) {
	            logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseTypeByCaseId======>");
	            return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findClaimSubCaseTypeByCaseId",  claimCaseSubPO) ;
	        }
		  /**
	       * @description 查询所有数据通过caseId
	       * @version
	       * @title
	       * <AUTHOR>
	       * @param claimSubCasePO 赔案子表对象
	       * @return List<ClaimSubCasePO> 查询结果List
	       */
		@Override
		public List<ClaimSubCasePO> findAllClaimSubCaseByCaseId1(ClaimSubCasePO claimSubCasePO) {
	         logger.debug("<======ClaimSubCaseDaoImpl--findClaimSubCaseByCaseId1======>");
	         return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findClaimSubCaseByCaseId1",  claimSubCasePO) ;
	      }

		/**
		 * 
		 * @description   根据赔案ID查询最早的出险日期
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param claimCaseSubPO  赔案子表
		 * @return  赔案子表集合
		 */
		@Override
		public List<ClaimSubCasePO> findMinClaimDateByCaseId(
				ClaimSubCasePO claimCaseSubPO) {
			logger.debug("<======ClaimSubCaseDaoImpl--findMinClaimDateByCaseId======>");
	        return findAll("com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO.findMinClaimDateByCaseId",  claimCaseSubPO) ;
		}}
