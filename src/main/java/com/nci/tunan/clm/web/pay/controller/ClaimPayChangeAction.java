package com.nci.tunan.clm.web.pay.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import com.nci.core.common.interfaces.vo.LockResultVO;
import com.nci.core.common.interfaces.vo.LockServiceSetVO;
import com.nci.tunan.clm.impl.pay.ucc.IClaimPayChangeLogUCC;
import com.nci.tunan.clm.impl.pay.ucc.IClaimPayChangeUCC;
import com.nci.tunan.clm.impl.sign.ucc.ICheckOperateUCC;
import com.nci.tunan.clm.impl.util.CodeUtils;
import com.nci.tunan.clm.interfaces.model.vo.BankWayVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimBpmRequestVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimChecklistVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimPayChangeLogVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimPayChangeVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimTaskVO;
import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.tunan.clm.interfaces.model.vo.PremArapVO;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.presentation.FrameworkBaseAction;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.DateUtilsEx;


/**
 * 
 * @description 付费变更申请Action 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/付费变更申请
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimPayChangeAction extends FrameworkBaseAction {
	/**
	 * 序列化ID
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * ClaimPayChangeUCC
	 */
	private IClaimPayChangeUCC claimPayChangeUCC;
	/**
	 * claimPayChangeLogUCC
	 */
	private IClaimPayChangeLogUCC claimPayChangeLogUCC;
	/**
	 * 影像信息UCC
	 */
	private ICheckOperateUCC checkOperateUCC;
//@invalid	/*private IClaimCaseUCC claimCaseUCC;
//@invalid	
//@invalid	//短信邮件
//@invalid	private ISignConfirmationUCC signConfirmationUCC;
//@invalid	private IQueryTaskTraceUCC queryTaskTraceUCC;
//@invalid	private ISendMessageUCC sendMessageUCC;*/
	/**
	 * map
	 */
	private Map<String, Object> map ;
	
	
	/**
	 * 定义付费变更申请VO
	 */
	private ClaimPayChangeVO claimPayChangeVO;
	
	/**
	 * 定义查询结果VO
	 */
	private List<ClaimPayChangeVO> listPayChangeVO;

	
	/**
	 * 变更前vo
	 */
	private ClaimPayChangeLogVO bfPayChangeVO;
	
	/**
	 * 变更后vo
	 */
	private ClaimPayChangeVO afPayChangeVO;

	
	/**
	 * 变更轨迹vo
	 */
	private ClaimPayChangeLogVO claimPayChangeTraceVO;
	
	
	/**
	 * 收付费vo
	 */
	private PremArapVO premArapVO;
	/**
	 * 赔案主表VO
	 */
	private ClaimCaseVO claimCaseVO ;
	
	/**
	 * 工作流任务号
	 */
	private String taskId = "";
	/**
	 * 赔案号标识
	 */
	private String caseNoFlag = "";
	/**
	 * 审核状态标识
	 */
	private String auditStatusFlag = "";
	/**
	 * businesscode
	 */
	private String businesscode;
	/**
	 * 赔案号
	 */
	private String caseNo;
	/**
	 * 赔案号
	 */
	private String caseNo2;
	
	/**
	 * 页面提示语标识
	 */
	private String imageFlag;
	
	
	/**
	 * 操作轨迹
	 */
	private List<ClaimTaskVO> claimTaskTraceList;
	

	/**
	 * 
	 * @description 付费变更申请页面展示
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 页面跳转
	 */
	public String showClaimPayChangeInit() {
		//付费变更申请页面展示
        if (claimPayChangeVO == null) { 
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		caseNoFlag = getRequest().getParameter("caseNo");
		claimPayChangeVO.setChangeBy(BigDecimal.valueOf(AppUserContext.getCurrentUser().getUserId()));
		claimPayChangeVO.setChangeByName(AppUserContext.getCurrentUser().getUserName());
		claimPayChangeVO.setChangeTime(WorkDateUtil.getWorkDate());
		imageFlag = "1";
		return "showClaimPayChange";
	}
	/**
	 * 
	 * @description 付费变更审核初始化
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 页面跳转
	 */
	public String showClaimPayChangeCheckInit() {
		//付费变更审核初始化
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		claimPayChangeVO.setAuditByName(AppUserContext.getCurrentUser().getUserName());
		claimPayChangeVO.setAuditBy(BigDecimal.valueOf(AppUserContext.getCurrentUser().getUserId()));
		claimPayChangeVO.setAuditTime(WorkDateUtil.getWorkDate());
		imageFlag = "1";
		return "showClaimPayChangeCheck";
	}

	/**
	 * 
	 * @description 付费变更轨迹查询页面展示
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 页面跳转
	 */
	public String showPayChangeTraceInit() {	
		//付费变更轨迹查询页面展示
		caseNoFlag = getRequest().getParameter("caseNo");
		imageFlag = "1";
		return "showPayChangeTrace";
	}

	/**
	 * 
	 * @description 付费变更申请查询
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 页面跳转
	 */
	public String findClaimPayChange() {
		ClaimPayChangeVO claimPayChangeVO1 = new ClaimPayChangeVO();
		//付费变更申请查询
		String caseNo = getRequest().getParameter("caseNo");
		if(caseNo!=null&&!"".equals(caseNo)){
			claimPayChangeVO1.setBusinessCode(caseNo);
		}
		String auditStatus = getRequest().getParameter("auditStatus");
		caseNoFlag = caseNo;
		auditStatusFlag = auditStatus;
		if(auditStatus!=null&&!"".equals(auditStatus)){
			claimPayChangeVO1.setAuditStatus(new BigDecimal(auditStatus));
			
		}
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		currentPage = claimPayChangeUCC.queryPayChangeForPage(claimPayChangeVO1, getCurrentPage());
		claimPayChangeVO.setChangeBy(BigDecimal.valueOf(AppUserContext.getCurrentUser().getUserId()));
		claimPayChangeVO.setChangeByName(AppUserContext.getCurrentUser().getUserName());
		claimPayChangeVO.setChangeTime(WorkDateUtil.getWorkDate());
	    return "findClaimPayChange";
	}
    /**
     * 
     * @description 查询付费变更
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @return 页面跳转
     */
	public String findClaimPayChangeCheck() {
		//查询付费变更
		ClaimBpmRequestVO claimBpmRequestVO = new ClaimBpmRequestVO();
		String caseNo = getRequest().getParameter("caseNo");
		caseNoFlag = caseNo;
		String organCode = AppUserContext.getCurrentUser().getOrganCode();
		claimBpmRequestVO.setCaseNo(caseNo);
		claimBpmRequestVO.setOrganCode(organCode);
		CommonMapVO commonMapVO=new CommonMapVO();
        commonMapVO.put("claimBpmRequestVO", claimBpmRequestVO);
        commonMapVO.put("caseNo", caseNo);
        commonMapVO.put("currentPage", currentPage);
        commonMapVO.put("getCurrentPage", getCurrentPage());
        CommonMapVO CommonMapVO2 = claimPayChangeUCC.findClaimPayChangeCheckToService(commonMapVO);
        currentPage= (CurrentPage) CommonMapVO2.get("currentPage");
        claimCaseVO=(ClaimCaseVO)CommonMapVO2.get("claimCase");
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		claimPayChangeVO.setAuditByName(AppUserContext.getCurrentUser().getUserName());
		claimPayChangeVO.setAuditBy(BigDecimal.valueOf(AppUserContext.getCurrentUser().getUserId()));
		claimPayChangeVO.setAuditTime(WorkDateUtil.getWorkDate());
		claimPayChangeVO.setCaseId(claimCaseVO.getCaseId());
	    return "findClaimPayChangeCheck";
	}
 
	
	
	/**
	 * 
	 * @description 付费变更提交验证该记录是否正在复核中 验证该案件是否在银行在途(跟银行的划账中)
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void viladeChangeCheck(){
		//付费变更提交验证该记录是否正在复核中 验证该案件是否在银行在途(跟银行的划账中)
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		ClaimBpmRequestVO claimBpmRequestVO = new ClaimBpmRequestVO();
		String organCode = AppUserContext.getCurrentUser().getOrganCode();
		claimBpmRequestVO.setCaseNo(claimPayChangeVO.getCaseNo());
		claimBpmRequestVO.setOrganCode(organCode);
		
		CommonMapVO commonMapVO=new CommonMapVO();
        commonMapVO.put("claimPayChangeVO", claimPayChangeVO);
        commonMapVO.put("claimBpmRequestVO", claimBpmRequestVO);
        CommonMapVO commonMapVO2=claimPayChangeUCC.viladeChangeCheckToService(commonMapVO);
        
        taskId=(String) commonMapVO2.get("taskId");
        claimPayChangeVO=(ClaimPayChangeVO)commonMapVO2.get("claimPayChangeVO");
		
		if(taskId != null){
			outAjax(Constants.DWZ_STATUSCODE_300, "该案件正在付费复核", "", "", "");
		}else{
			if("Y".equals(claimPayChangeVO.getResultCode())){
				outAjax(Constants.DWZ_STATUSCODE_300, "付费记录银行在途，不能变更", "", "", "");
			}else{
				outAjax(Constants.DWZ_STATUSCODE_200, "正常发起", "", "", "");
			}
		}
	}
	 
	/**
	 * 
	 * @description 查询付费变更轨迹
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 页面跳转
	 */
	public String findClaimPayChangeTrace(){
		//查询付费变更轨迹
		ClaimPayChangeVO claimPayChangeTraceVO = new ClaimPayChangeVO();
		String caseNo = getRequest().getParameter("caseNo");
		caseNoFlag = caseNo;
		claimPayChangeTraceVO.setBusinessCode(caseNo);
		currentPage = claimPayChangeUCC.queryPayChangeTraceForPage(claimPayChangeTraceVO, getCurrentPage());
		return "showPayChangeTrace";
	}
	/**
	 * 
	 * @description 付费变更申请确认/保存
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void saveChangeInfo() {
		//付费变更申请确认/保存
        if (map == null) {
            map = new HashMap<String, Object>();
        }
        if (bfPayChangeVO == null) {
            bfPayChangeVO = new ClaimPayChangeLogVO();
        }
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
        try{
			BigDecimal applyId = claimPayChangeVO.getApplyId();
			
			 CommonMapVO commonMapVO=new CommonMapVO();
	         commonMapVO.put("claimPayChangeVO", claimPayChangeVO);
	         commonMapVO.put("bfPayChangeVO", bfPayChangeVO);
	         commonMapVO.put("applyId", applyId);
	         commonMapVO.put("claimCaseVO", claimCaseVO);
	         commonMapVO.put("userId", getCurrentUser().getUserId().toString());
	         commonMapVO.put("organCode", getCurrentUser().getOrganCode());
	         commonMapVO.put("map", map);
	         CommonMapVO commonMapVO2=claimPayChangeUCC.saveChangeInfoToService(commonMapVO);
	         String flag = (String) commonMapVO2.get("flag");
			 if(flag != null && flag.equals("222")){
				 String message2 ="";
				 if(commonMapVO2.get("message")!=null) {
					 message2 = (String) commonMapVO2.get("message");
				 }
//					outAjax(Constants.DWZ_STATUSCODE_300, message2, "", "", "");
					JSONObject json = new JSONObject();
					json.put("APPLY_ID", claimPayChangeVO.getApplyId());
			        map.put("data", json);
			        map.put("statusCode", Constants.DWZ_STATUSCODE_300);
			        map.put("message", message2);
			        outJson(map);
			        logger.info("反洗钱黑名单接口！message："+message2);
			        logger.info("反洗钱黑名单接口！map："+map);
			 } else {
	         	map= (Map<String, Object>) commonMapVO2.get("map");
	         	outJson(map);
			 }
		}catch(Exception e){
			JSONObject json = new JSONObject();
			json.put("APPLY_ID", claimPayChangeVO.getApplyId());
	        map.put("data", json);
	        map.put("statusCode", Constants.DWZ_STATUSCODE_300);
	        map.put("message", e.getMessage());
	        outJson(map);
			e.printStackTrace();
		}
		
	}
	 
	
	/**
     * @description 单个加锁/解锁方法
     * @version
     * @title
     * <AUTHOR>
     * @param serviceCode 业务锁代码
     * @param policyId 保单ID
     * @param caseNo 赔案号
     * @param lockProcessCode 加/解业务锁流程
     * @return 成功/失败标志
     */
    public int setLock(String serviceCode, BigDecimal policyId, String policyCode, String caseNo, String lockProcessCode,String exceptGroupId1,String exceptGroupId2) {
    	//单个加锁/解锁方法
        LockServiceSetVO lockServiceSetVO = new LockServiceSetVO();
        lockServiceSetVO.setServiceCode(serviceCode);
        lockServiceSetVO.setPolicyId(policyId);
        lockServiceSetVO.setBusinessCode(caseNo);
        lockServiceSetVO.setPolicyCode(policyCode);
        lockServiceSetVO.setLockProcessCode(lockProcessCode);
        lockServiceSetVO.setExceptGroup1(exceptGroupId1);
        lockServiceSetVO.setExecptGroup2(exceptGroupId2);
        LockResultVO lockResultVO = com.nci.core.common.factory.BOServiceFactory.setLockOne(lockServiceSetVO);
        if (lockResultVO.isSucFlag()) {
            return 1;
        } else {
            return -1;
        }
    }
	
	/**
	 * 
	 * @description 查询变更信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void findPayChangeTrace(){ 
		String applyId = getRequest().getParameter("applyId");
		ClaimPayChangeLogVO payChangeTrace = new ClaimPayChangeLogVO();
		payChangeTrace.setApplyId(new BigDecimal(applyId));
		
		claimPayChangeTraceVO = claimPayChangeLogUCC.findClaimPayChangeLog(payChangeTrace);
		//查询最近的一次变更数据
		
//@invalid 		Map dataMap = new HashMap();
//@invalid 		dataMap.put("trace", claimPayChangeTraceVO);
		if(claimPayChangeTraceVO.getAfAccountNo()!=null){
			claimPayChangeTraceVO.setAfAccountNo1(claimPayChangeTraceVO.getAfAccountNo().toString());
		}
		if(claimPayChangeTraceVO.getBfAccountNo()!=null){
			claimPayChangeTraceVO.setBfAccountNo1(claimPayChangeTraceVO.getBfAccountNo().toString());
		}
		claimPayChangeTraceVO.setChangeBy(BigDecimal.valueOf(AppUserContext.getCurrentUser().getUserId()));
		outJsonCon(claimPayChangeTraceVO);
	}
	
	
	/**
	 * 
	 * @description 保存审核信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void saveCheckData(){
		//保存审核信息
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		try{
			ClaimPayChangeLogVO payChangeLogVO = new ClaimPayChangeLogVO();
			BigDecimal check = claimPayChangeVO.getPayAuditCon();  //@invalid 复核结论
			
			 CommonMapVO commonMapVO=new CommonMapVO();
	         commonMapVO.put("claimPayChangeVO", claimPayChangeVO);
	         commonMapVO.put("payChangeLogVO", payChangeLogVO);
	         commonMapVO.put("claimPayChangeTraceVO", claimPayChangeTraceVO);
	         commonMapVO.put("caseNo2", caseNo2);
	         CommonMapVO commonMapVO2=claimPayChangeUCC.saveCheckDataToService(commonMapVO);
	         outAjax(Constants.DWZ_STATUSCODE_200, Constants.DWZ_MESSAGE_200, "", "", "");
	         logger.info("保存复核规则方法结束");
		}catch(Exception e){
			e.printStackTrace();
			outAjax(Constants.DWZ_STATUSCODE_300, Constants.DWZ_MESSAGE_300, "", "", "");
		}
	} 
	/**
	 * 
	 * @description 查询轨迹
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void getTrace(){
		//查询轨迹
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		String feeId = getRequest().getParameter("feeId");
		String applyId = getRequest().getParameter("applyId");
		String caseNo = getRequest().getParameter("caseNo");
		String logId = getRequest().getParameter("logId");
	
		ClaimPayChangeVO payChangeVO1 = new ClaimPayChangeVO();
		payChangeVO1.setApplyId(new BigDecimal(applyId));
		payChangeVO1.setBusinessCode(caseNo);
		ClaimPayChangeLogVO payChangeLogVO = new ClaimPayChangeLogVO();
		payChangeLogVO.setApplyId(new BigDecimal(applyId));
		payChangeLogVO.setFeeId(new BigDecimal(feeId));
		payChangeLogVO.setLogId(new BigDecimal(logId));
		
		Map dataMap = new HashMap();
		
		try{
			 CommonMapVO commonMapVO=new CommonMapVO();
	         commonMapVO.put("payChangeVO1", payChangeVO1);
	         commonMapVO.put("claimPayChangeVO", claimPayChangeVO);
	         commonMapVO.put("payChangeLogVO", payChangeLogVO);
	         commonMapVO.put("claimPayChangeTraceVO", claimPayChangeTraceVO);
	         commonMapVO.put("caseNo", caseNo);
	         commonMapVO.put("claimTaskTraceList", claimTaskTraceList);
	         commonMapVO.put("dataMap", dataMap);
	         CommonMapVO commonMapVO2= claimPayChangeUCC.getTraceToService(commonMapVO);
	         dataMap= (Map<String, Object>) commonMapVO2.get("dataMap");
		}catch(Exception e){
			e.printStackTrace();
			outAjax(Constants.DWZ_STATUSCODE_300, Constants.DWZ_MESSAGE_300, "", "", "");
		}
		outJsonCon(dataMap);
	}
	
	 
	 
	/**
	 * 
	 * @description 变更申请转扫描
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void invoicesScan(){
		//变更申请转扫描
        com.nci.udmp.framework.presentation.vo.UserVO user = getCurrentUser();
        ClaimChecklistVO claimChecklistVO = new ClaimChecklistVO();
        claimChecklistVO.setCaseNo(businesscode);
        claimChecklistVO.setUserId(user.getUserId());
        claimChecklistVO.setOrganCode(user.getOrganCode());
        try {
            String scanPageUrl = checkOperateUCC.invoicesScan(claimChecklistVO);

            outAjax(Constants.DWZ_STATUSCODE_200, scanPageUrl, "", "", "");
        } catch (BizException e) {
            outAjax(Constants.DWZ_STATUSCODE_300, "调用影像扫描失败", "", "", "");
            LOGGER.debug(e.getMessage());
        }
    }

	/**
	 * 
	 * @description 变更申请转验真
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 页面跳转
	 */
	public String toConfirm() {	
		//变更申请转验真
	    return "toConfirm";
	}
	
	/**
	 * 
	 * @description 查询付款方式类型
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void findBankWay(){
		//查询付款方式类型
		BankWayVO bankWayVO = new BankWayVO();
		List<BankWayVO> bankWayVOList = claimPayChangeUCC.findBankWay(bankWayVO);
		outJsonCon(bankWayVOList);
	}
	
	/**
	 * 
	 * @description 查询修改后的数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public void queryUpdateAfter(){
		//查询修改后的数据
        if (claimPayChangeVO == null) {
            claimPayChangeVO = new ClaimPayChangeVO();
        }
		ClaimPayChangeVO claimPayChange = claimPayChangeUCC.findClaimPayChange(claimPayChangeVO);
		//@invalid 把Date转换为String，前台不用转换了。
		if(claimPayChange.getApplyId() != null && claimPayChange.getAuditTime() != null){
			claimPayChange.setAuditTimeStr(DateUtilsEx.date2String(claimPayChange.getAuditTime(), "yyyy-MM-dd"));
		}
		String userName = null;
		if(claimPayChange.getAuditBy() != null){
			userName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_UDMP_USER", claimPayChange.getAuditBy().toString());
			claimPayChange.setAuditByName(userName);
		}
		if(claimPayChange.getChangeBy()!=null){
		    userName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_UDMP_USER", claimPayChange.getChangeBy().toString());
            claimPayChange.setChangeByName(userName);
		}
		outJson(claimPayChange);
	}
	
	@Override
	public String getBizId() {
		return null;
	}

	public ClaimPayChangeVO getClaimPayChangeVO() {
		return claimPayChangeVO;
	}	
	public void setClaimPayChangeVO(ClaimPayChangeVO claimPayChangeVO) {
		this.claimPayChangeVO = claimPayChangeVO;
	}

	public List<ClaimPayChangeVO> getListPayChangeVO() {
		return listPayChangeVO;
	}

	public void setListPayChangeVO(List<ClaimPayChangeVO> listPayChangeVO) {
		this.listPayChangeVO = listPayChangeVO;
	}

	

	public IClaimPayChangeUCC getClaimPayChangeUCC() {
		return claimPayChangeUCC;
	}

	public void setClaimPayChangeUCC(IClaimPayChangeUCC claimPayChangeUCC) {
		this.claimPayChangeUCC = claimPayChangeUCC;
	}

	public ClaimPayChangeLogVO getClaimPayChangeTraceVO() {
		return claimPayChangeTraceVO;
	}

	public void setClaimPayChangeTraceVO(ClaimPayChangeLogVO claimPayChangeTraceVO) {
		this.claimPayChangeTraceVO = claimPayChangeTraceVO;
	}

	public ClaimPayChangeLogVO getBfPayChangeVO() {
		return bfPayChangeVO;
	}

	public void setBfPayChangeVO(ClaimPayChangeLogVO bfPayChangeVO) {
		this.bfPayChangeVO = bfPayChangeVO;
	}

	public ClaimPayChangeVO getAfPayChangeVO() {
		return afPayChangeVO;
	}

	public void setAfPayChangeVO(ClaimPayChangeVO afPayChangeVO) {
		this.afPayChangeVO = afPayChangeVO;
	}

	public IClaimPayChangeLogUCC getClaimPayChangeLogUCC() {
		return claimPayChangeLogUCC;
	}

	public void setClaimPayChangeLogUCC(IClaimPayChangeLogUCC claimPayChangeLogUCC) {
		this.claimPayChangeLogUCC = claimPayChangeLogUCC;
	}

    public ClaimCaseVO getClaimCaseVO() {
        return claimCaseVO;
    }

    public void setClaimCaseVO(ClaimCaseVO claimCaseVO) {
        this.claimCaseVO = claimCaseVO;
    }

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public ICheckOperateUCC getCheckOperateUCC() {
		return checkOperateUCC;
	}

	public void setCheckOperateUCC(ICheckOperateUCC checkOperateUCC) {
		this.checkOperateUCC = checkOperateUCC;
	}


	public String getCaseNoFlag() {
		return caseNoFlag;
	}

	public void setCaseNoFlag(String caseNoFlag) {
		this.caseNoFlag = caseNoFlag;
	}

	public String getAuditStatusFlag() {
		return auditStatusFlag;
	}

	public void setAuditStatusFlag(String auditStatusFlag) {
		this.auditStatusFlag = auditStatusFlag;
	}

	public Map<String, Object> getMap() {
		return map;
	}

	public void setMap(Map<String, Object> map) {
		this.map = map;
	}

	public String getBusinesscode() {
		return businesscode;
	}

	public void setBusinesscode(String businesscode) {
		this.businesscode = businesscode;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getCaseNo2() {
		return caseNo2;
	}

	public void setCaseNo2(String caseNo2) {
		this.caseNo2 = caseNo2;
	}


	public List<ClaimTaskVO> getClaimTaskTraceList() {
		return claimTaskTraceList;
	}

	public void setClaimTaskTraceList(List<ClaimTaskVO> claimTaskTraceList) {
		this.claimTaskTraceList = claimTaskTraceList;
	}

	public String getImageFlag() {
		return imageFlag;
	}

	public void setImageFlag(String imageFlag) {
		this.imageFlag = imageFlag;
	}
	
}
