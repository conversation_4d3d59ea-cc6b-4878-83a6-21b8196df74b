package com.nci.tunan.clm.impl.register.ucc.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

import com.git.ecss.dm.fp.PluginRegisterInfo;
import com.git.ecss.easyclient.fp.IPageRegisterServiceProcess;
import com.git.ecss.easyclient.fp.impl.PageRegisterProcessImpl;
import com.git.ecss.sysobject.system.BaseBusiObject;
import com.git.ecss.sysobject.system.BaseDocObject;
import com.nci.tunan.clm.impl.calc.service.IClaimMatchCalcService;
import com.nci.tunan.clm.impl.calc.ucc.IClaimMatchCalcUCC;
import com.nci.tunan.clm.impl.inspect.service.IClaimCaseService;
import com.nci.tunan.clm.impl.register.service.IClaimMatchResultService;
import com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC;
import com.nci.tunan.clm.impl.report.service.IClaimAccidentService;
import com.nci.tunan.clm.impl.report.service.IContractProductService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.interfaces.model.bo.ClaimAccidentBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimBusiProdBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseCheatBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimLiabBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimLiabCloseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimProductBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimViewBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimViewVO;
import com.nci.tunan.clm.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractProductBO;
import com.nci.tunan.clm.interfaces.model.bo.MisrepresentationsFactorBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimTaskStaffBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimAccidentVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimBusiProdVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimCaseCheatVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimLiabVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimMatchJournaVO;
import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.tunan.clm.interfaces.model.vo.ContractMasterVO;
import com.nci.tunan.clm.interfaces.model.vo.ContractProductVO;
import com.nci.tunan.clm.interfaces.model.vo.MisrepresentationsFactorVO;
import com.nci.udmp.app.ucc.vo.UserPermissionVO;
import com.nci.udmp.framework.bizservice.IBizService;
import com.nci.udmp.framework.context.AppUser;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.component.datapermission.DataPermissionUtil;
import com.nci.tunan.clm.impl.taskmanage.service.IClaimTaskStaffService;
import com.nci.udmp.util.logging.LoggerFactory;
/** 
 * @description 匹配理算结果
 * <AUTHOR> <EMAIL>
 * @date 2015年8月29日   上午10:20:08
 * @.belongToModule CLM-理赔系统-匹配理算
*/
public class ClaimMatchResultUCCImpl implements IClaimMatchResultUCC , IBizService {
	/** 
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();
    /** 
    * @Fields claimMatchResultService : 匹配理算结果业务处理类
    */ 
    private IClaimMatchResultService claimMatchResultService;
    /** 
    * @Fields contractProductService : 责任组抄单表数据处理
    */ 
    private IContractProductService contractProductService;
 
    /** 
    * @Fields claimAccidentService : 事故处理类
    */ 
    private  IClaimAccidentService claimAccidentService;
    /** 
    * @Fields claimMatchCalcService : 匹配理算业务处理类 
    */ 
    private IClaimMatchCalcService claimMatchCalcService;
	/** 
	* @Fields claimCaseService : 赔案信息处理类
	*/ 
	private IClaimCaseService claimCaseService;
    /** 
    * @Fields claimMatchCalcUCC : 匹配理算UCC
    */ 
    private IClaimMatchCalcUCC claimMatchCalcUCC;
    /**
     * 作业参数Service
     */
    private IClaimTaskStaffService claimTaskStaffService;
 
    
    
    
    public IClaimTaskStaffService getClaimTaskStaffService() {
		return claimTaskStaffService;
	}

	public void setClaimTaskStaffService(
			IClaimTaskStaffService claimTaskStaffService) {
		this.claimTaskStaffService = claimTaskStaffService;
	}

	public IClaimCaseService getClaimCaseService() {
		return claimCaseService;
	}

	public void setClaimCaseService(IClaimCaseService claimCaseService) {
		this.claimCaseService = claimCaseService;
	}

	public IClaimMatchCalcService getClaimMatchCalcService() {
		return claimMatchCalcService;
	}

	public void setClaimMatchCalcService(
			IClaimMatchCalcService claimMatchCalcService) {
		this.claimMatchCalcService = claimMatchCalcService;
	}

    public IClaimMatchCalcUCC getClaimMatchCalcUCC() {
		return claimMatchCalcUCC;
	}

	public void setClaimMatchCalcUCC(IClaimMatchCalcUCC claimMatchCalcUCC) {
		this.claimMatchCalcUCC = claimMatchCalcUCC;
	}

	public IClaimAccidentService getClaimAccidentService() {
		return claimAccidentService;
	}

	public void setClaimAccidentService(IClaimAccidentService claimAccidentService) {
		this.claimAccidentService = claimAccidentService;
	}

	public IClaimMatchResultService getClaimMatchResultService() {
 
        return claimMatchResultService;
    }

    public void setClaimMatchResultService(
            IClaimMatchResultService claimMatchResultService) {
        this.claimMatchResultService = claimMatchResultService;
    }
	public IContractProductService getContractProductService() {
		return contractProductService;
	}

	public void setContractProductService(
			IContractProductService contractProductService) {
		this.contractProductService = contractProductService;
	}
	
    /**
     * 
     * @description 查询理赔计算信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimCalc(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param claimCaseVO 理赔赔案主表VO对象
     * @return 计算信息查询结果list集合
     * @throws BizException
     */
    @Override
    public List<ClaimCaseVO> queryClaimCalc(ClaimCaseVO claimCaseVO) throws BizException {
    	//@invalid 据入参查询理赔计算信息
        ClaimCaseBO claimCaseBO = BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO);
        //调用匹配理算service方法查询理赔计算信息
        return BeanUtils.copyList(ClaimCaseVO.class, claimMatchResultService.queryClaimCalc(claimCaseBO));
    }
    /**
     * 
     * @description 查询理赔类型层理算信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimTypeCalc(com.nci.tunan.clm.interfaces.model.vo.ClaimLiabVO)
     * @param claimLiabVO 给付责任信息VO对象
     * @return 责任信息集合
     * @throws BizException
     */
    @Override
    public List<ClaimLiabVO> queryClaimTypeCalc(ClaimLiabVO claimLiabVO) throws BizException {
    	//@invalid 查询理赔类型层理算信息
        ClaimLiabBO claimLiabBO = BeanUtils.copyProperties(ClaimLiabBO.class, claimLiabVO);
        //调用匹配理算service方法查询理赔类型层理算信息
        return BeanUtils.copyList(ClaimLiabVO.class, claimMatchResultService.queryClaimTypeCalc(claimLiabBO));
    }
    /**
     * 
     * @description 查询保单计算信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimBusiProd(com.nci.tunan.clm.interfaces.model.vo.ClaimBusiProdVO, java.lang.String)
     * @param claimBusiProdVO 险种理算信息VO对象
     * @param flag 标识
     * @return 理算信息集合
     * @throws BizException
     */
    @Override
    public List<ClaimBusiProdVO> queryClaimBusiProd(ClaimBusiProdVO claimBusiProdVO , String flag) throws BizException {
    	//@invalid 查询保单计算信息
        ClaimBusiProdBO claimBusiProdBO = BeanUtils.copyProperties(ClaimBusiProdBO.class, claimBusiProdVO);
        //调用匹配理算service方法查询查询保单计算信息
        return BeanUtils.copyList(ClaimBusiProdVO.class, claimMatchResultService.queryClaimBusiProd(claimBusiProdBO , flag));
    }

    /**
     * 
     * @description 查询保项计算信息（分页）
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimLiab(com.nci.tunan.clm.interfaces.model.vo.ClaimLiabVO, com.nci.udmp.framework.model.CurrentPage)
     * @param claimLiabVO 给付责任信息VO对象
     * @param currentPage 分页查询入参
     * @return 给付责任集合
     * @throws BizException
     */
    @Override
    public CurrentPage<ClaimLiabVO> queryClaimLiab(ClaimLiabVO claimLiabVO , CurrentPage<ClaimLiabVO> currentPage) throws BizException {
        ClaimLiabBO claimLiabBO = BeanUtils.copyProperties(ClaimLiabBO.class, claimLiabVO);
        //@invalid 分页查询保项计算信息
        //调用匹配理算service方法查询保项计算信息
        return BeanUtils.copyCurrentPage(ClaimLiabVO.class, claimMatchResultService.queryClaimLiab(claimLiabBO , BeanUtils.copyCurrentPage(ClaimLiabBO.class, currentPage)));
    }
    /**
     * 
     * @description 查询保项计算信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimLiab(com.nci.tunan.clm.interfaces.model.vo.ClaimLiabVO)
     * @param claimLiabVO 给付责任信息VO对象
     * @return 给付责任信息VOlist集合
     * @throws BizException
     */
    @Override
	public List<ClaimLiabVO> queryClaimLiab(ClaimLiabVO claimLiabVO)
			throws BizException {
    	ClaimLiabBO claimLiabBO = BeanUtils.copyProperties(ClaimLiabBO.class, claimLiabVO);
    	//@invalid 保项计算信息查询
    	//调用匹配理算service方法查询保项计算信息
        return BeanUtils.copyList(ClaimLiabVO.class, claimMatchResultService.queryClaimLiab(claimLiabBO));
	}
    /**
     * 
     * @description 查询赔案信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimCase(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param tClaimCaseVO 理赔赔案主表VO对象
     * @return 赔案信息结果
     * @throws BizException
     */
    @Override
    public ClaimCaseVO queryClaimCase(ClaimCaseVO tClaimCaseVO) throws BizException {
    	//@invalid 设置查询入参对象
        ClaimCaseVO tClaimCaseBackVO = new ClaimCaseVO();
        ClaimCaseBO tClaimCaseBackBO = new ClaimCaseBO();
        ClaimCaseBO tClaimCaseBO = new ClaimCaseBO();
        BeanUtils.copyProperties(tClaimCaseBO, tClaimCaseVO);
        //调用匹配理算service方法根据查询赔案信息
        tClaimCaseBackBO = claimMatchResultService.queryClaimCase(tClaimCaseBO);
        BeanUtils.copyProperties(tClaimCaseBackVO, tClaimCaseBackBO);
        return tClaimCaseBackVO;
    }
    /**
     * 
     * @description 查询事件信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimAccident(com.nci.tunan.clm.interfaces.model.vo.ClaimAccidentVO)
     * @param tClaimAccidentVO 理赔事件VO对象
     * @return 事故理算信息
     * @throws BizException
     */
    @Override
    public ClaimAccidentVO queryClaimAccident(ClaimAccidentVO tClaimAccidentVO) throws BizException {
    	//@invalid 设置查询入参对象
        ClaimAccidentVO tClaimAccidentBackVO = new ClaimAccidentVO();
        ClaimAccidentBO tClaimAccidentBackBO = new ClaimAccidentBO();
        ClaimAccidentBO tClaimAccidentBO = new ClaimAccidentBO();
        BeanUtils.copyProperties(tClaimAccidentBO, tClaimAccidentVO);
        //调用匹配理算service方法查询事件信息
        tClaimAccidentBackBO = claimMatchResultService.queryClaimAccident(tClaimAccidentBO);
        BeanUtils.copyProperties(tClaimAccidentBackVO, tClaimAccidentBackBO);
        return tClaimAccidentBackVO;
    }
    
    /**
     * 
     * @description 更新保项赔付结论
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#updateClaimMatchResult(com.nci.tunan.clm.interfaces.model.vo.ClaimLiabVO)
     * @param claimLiabVO 给付责任信息VO对象
     * @return 给付责任理算信息
     * @throws BizException
     */
    @Override
    public ClaimLiabVO updateClaimMatchResult(ClaimLiabVO claimLiabVO) throws BizException {
    	//@invalid 设置保项结论对象入参
        ClaimLiabBO claimLiabBO = BeanUtils.copyProperties(ClaimLiabBO.class, claimLiabVO);   
        //调用匹配理算service方法更新保项赔付结论
        claimMatchResultService.updateClaimMatchResult(claimLiabBO);
                
        ClaimLiabVO claimLiabVONew = BeanUtils.copyProperties(ClaimLiabVO.class, claimLiabBO);
        
        return claimLiabVONew;
    }
    /**
     * 
     * @description 根据保单号获取保单条款地址 
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#getPolicyAddrs(java.lang.String)
     * @param policyCode 保单号
     * @return 影像地址
     * @throws Exception
     */
    @Override
    public String getPolicyAddrs(String policyCode) throws Exception {
    	//@invalid 设置相应查询入参
    	String[] url = Constants.SERVICEENVPARAMAP.get("ECS").split("/ecs");
        IPageRegisterServiceProcess process = PageRegisterProcessImpl.getInstance();
        AppUser user = AppUserContext.getCurrentUser();
        List<String> roles = Arrays.asList("8a889cf64784fab9014785021954003e");
        List<BaseBusiObject<BaseDocObject>> businessObjects = new ArrayList<BaseBusiObject<BaseDocObject>>();
        BaseBusiObject<BaseDocObject> baseBusiObject = new BaseBusiObject<BaseDocObject>();
        baseBusiObject.setEcmBusiCode(policyCode);
        baseBusiObject.setEcmBusiType("lp");
        //@invalid 86机构显示“理赔调查影音资料”类型
        String isVideoRead = ClaimConstant.STRING_ZERO;
        if(ClaimConstant.ZONG_ORG_CODE.equals(user.getOrganCode())) {
        	isVideoRead = ClaimConstant.STRING_ONE;
        }
        baseBusiObject.getAttributes().put("isVideoRead",isVideoRead);
        businessObjects.add(baseBusiObject);
        String orgCode = user.getOrganCode();
        String scanner = String.valueOf(user.getUserId());
        //调用影像方法获取影像扫描页面URL
        PluginRegisterInfo registerInfo = process.imageViewRegister(roles, businessObjects, orgCode, scanner);
        return registerInfo.getAppUrl();
    }

    /**
     * 
     * @description 根据终止结果返回消息提醒
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryStopReasonReturnMessage(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param ClaimCaseVO 理赔赔案主表VO对象
     * @return 消息集合
     * @throws BizException
     */
    public List<String> queryStopReasonReturnMessage(ClaimCaseVO ClaimCaseVO) throws BizException {
    	//@invalid 设置查询入参VO对象
        ClaimCaseBO claimCaseBO = new ClaimCaseBO();
        claimCaseBO.setCaseId(ClaimCaseVO.getCaseId());
        //调用匹配理算service方法根据终止结果查询消息提醒
        List<String> message = claimMatchResultService.queryStopReasonReturnMessage(claimCaseBO);
        return message;
    }
    /** 重新抄单
     * @description
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param ClaimCaseVO 赔案信息
    */
    public void copyPolicyData(ClaimCaseVO ClaimCaseVO){
    	ClaimCaseBO claimCaseBO = new ClaimCaseBO();
    	claimCaseBO.setCaseId(ClaimCaseVO.getCaseId());
    	claimMatchResultService.copyPolicyData(claimCaseBO);
    }
    /**
     * 
     * @description 根据赔案id查询给付责任理算表
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimLiabByCaseId(com.nci.tunan.clm.interfaces.model.vo.ClaimLiabVO)
     * @param claimLiabVO 给付责任信息VO对象
     * @return 责任信息集合
     * @throws BizException
     */
    public List<ClaimLiabVO> queryClaimLiabByCaseId(ClaimLiabVO claimLiabVO) throws BizException {
    	//@invalid 设置查询VO对象
        ClaimLiabBO claimLiabBO = new ClaimLiabBO();
        claimLiabBO.setCaseId(claimLiabVO.getCaseId());
        //调用匹配理算service方法根据入参对象查询给付责任理算
        List<ClaimLiabVO> claimLiabVOs = BeanUtils.copyList(ClaimLiabVO.class, claimMatchResultService.queryClaimLiabByCaseId(claimLiabBO)); 
        return claimLiabVOs;
    }
    /**
     * 
     * @description 审核匹配理算保存方法
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimLiabByCaseId(com.nci.tunan.clm.interfaces.model.vo.ClaimLiabVO)
     * @param claimLiabVO 给付责任信息VO对象
     * @return 责任信息集合
     * @throws BizException
     */
    public CommonMapVO matchResultSubmit(CommonMapVO commonMapVO) throws BizException {
    	ClaimCaseVO claimCaseVO = (ClaimCaseVO)commonMapVO.get("claimCaseVO");
    	//保存审核匹配理算赔案层信息
    	claimMatchResultService.savaAuditClaimCaseData(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO));
    	//@invalid 设置查询VO对象
    	ClaimLiabBO claimLiabBO = new ClaimLiabBO();
    	claimLiabBO.setCaseId(claimCaseVO.getCaseId());
    	//调用匹配理算service方法根据入参对象查询给付责任理算
    	List<ClaimLiabVO> claimLiabVOs = BeanUtils.copyList(ClaimLiabVO.class, claimMatchResultService.queryClaimLiabByCaseId(claimLiabBO)); 
    	commonMapVO.put("claimLiabVOs", claimLiabVOs);
    	return commonMapVO;
    }
    /**
     * 
     * @description 根据责任ID查询责任详细信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryLiabConclusion(java.math.BigDecimal)
     * @param claimLiabId 给付责任id
     * @return 给付责任信息
     */
    public ClaimLiabVO queryLiabConclusion(BigDecimal claimLiabId){
    	//调用匹配理算service方法根据责任ID查询责任详细信息
    	ClaimLiabBO claimLiabBO = claimMatchResultService.queryLiabConclusion(claimLiabId);
    	ClaimLiabVO claimLiabVO = BeanUtils.copyProperties(ClaimLiabVO.class, claimLiabBO);
    	return claimLiabVO;
    }
    /**
     * 
     * @description 汇总赔案金额
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#updateClaimCaseActualPay(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param claimCaseVO 理赔赔案主表VO对象
     */
    public void updateClaimCaseActualPay(ClaimCaseVO claimCaseVO){
    	//调用匹配理算service方法根据入参对象汇总赔案金额
    	ClaimCaseBO claimCaseBO= BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO);
    	claimMatchResultService.updateClaimCaseActualPay(claimCaseBO);
    }
    /**
     * 
     * @description 查询匹配理算日志
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryClaimBusiProdLog(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param claimCaseVO 理赔赔案主表VO对象
     * @return 责任信息集合
     */
    public List<ClaimLiabVO> queryClaimBusiProdLog(ClaimCaseVO claimCaseVO){
    	//@invalid 设置查询入参对象
    	ClaimCaseBO claimCaseBO = new ClaimCaseBO();
    	claimCaseBO.setCaseId(claimCaseVO.getCaseId());
    	//调用匹配理算service方法查询匹配理算日志
    	List<ClaimLiabBO> claimLiabBOs = claimMatchResultService.queryClaimBusiProdLog(claimCaseBO);
    	List<ClaimLiabVO> claimLiabVOs = new ArrayList<ClaimLiabVO>();
    	//@invalid 根据理算信息循环判断并处理底色标识
    	for(ClaimLiabBO claimLiabBO : claimLiabBOs){
    		ClaimLiabVO claimLiabVO = BeanUtils.copyProperties(ClaimLiabVO.class, claimLiabBO);
    		claimLiabVO.setClaimMatchJournaVOs(BeanUtils.copyList(ClaimMatchJournaVO.class, claimLiabBO.getClaimMatchJournaBOs()));
    		for (ClaimMatchJournaVO claimMatchJournaVO : claimLiabVO.getClaimMatchJournaVOs()) {
    			if("1".equals(claimMatchJournaVO.getDsFlag())){
					claimLiabVO.setDsFlag("1");
				}
			}
    		claimLiabVOs.add(claimLiabVO);
    	}
    	return claimLiabVOs;
    }
	/**
	 * 
	 * @description 自动分配
	 * @version V1.0.0 
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#reAssignTask(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
	 * @param claimCaseVO 理赔赔案主表VO对象
	 * @return 分配成功标识
	 */
	@Override
	public boolean reAssignTask(ClaimCaseVO claimCaseVO) {
		//调用匹配理算service方法重新分配超权限任务
		return claimMatchResultService.reAssignTask(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO));
	}
    /**
     * 
     * @description 根据caseId查询险种信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryContractProd(com.nci.tunan.clm.interfaces.model.vo.ContractProductVO)
     * @param contractProductVO 责任组信息VO对象
     * @return 责任组信息VOlist集合
     */
    public List<ContractProductVO> queryContractProd(ContractProductVO contractProductVO){
    	//调用匹配理算service方法根据入参查询险种信息
    	return BeanUtils.copyList(ContractProductVO.class, contractProductService.findAllContractProduct(BeanUtils.copyProperties(ContractProductBO.class, contractProductVO)));
    }
    /**
     * 
     * @description 查询匹配理算提示信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#judgeIfInfo(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param claimCaseVO 理赔赔案主表VO对象
     * @return 提示信息集合
     */
    public Map<String,String> judgeIfInfo(ClaimCaseVO claimCaseVO){
    	//@invalid 设置查询入参VO对象
    	ClaimCaseBO claimCaseBO = new ClaimCaseBO();
    	claimCaseBO.setCaseId(claimCaseVO.getCaseId());
    	//调用匹配理算service方法查询匹配理算提示信息
    	Map<String,String> messageMap = claimMatchResultService.judgeIfInfo(claimCaseBO,false);
    	return messageMap;
    }
    /**
     * 
     * @description 查询流程通过提示信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#confirmIfInfo(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param claimCaseVO 理赔赔案主表VO对象
     * @return 提示信息集合
     */
    public Map<String,String> confirmIfInfo(ClaimCaseVO claimCaseVO){
    	//@invalid 设置查询入参BO对象
    	ClaimCaseBO claimCaseBO = new ClaimCaseBO();
    	claimCaseBO.setCaseId(claimCaseVO.getCaseId());
    	//调用匹配理算service方法查询流程通过提示信息
    	Map<String,String> messageMap = claimMatchResultService.confirmIfInfo(claimCaseBO);
    	return messageMap;
    }
 
    
    
    /**
     * 
     * @description 查询匹配理算信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#findClaimMatchResult(com.nci.tunan.clm.interfaces.model.vo.CommonMapVO)
     * @param commonMapVO 组合入参对象
     * @return 理赔事故信息
     * @throws BizException
     */
        @Override
        public CommonMapVO findClaimMatchResult(CommonMapVO commonMapVO) throws BizException {
        	CommonMapVO resultMapVO  =new  CommonMapVO();
        	//@invalid 1查询赔案流程页面记录数据
        	ClaimViewBO claimViewBO = new ClaimViewBO();
			claimViewBO.setCaseId(new BigDecimal(commonMapVO.get("caseId")+"")); 
			//@invalid 审核阶段标签点击做查看操作不记录页面轨迹
			String readOnly = (String) commonMapVO.get("readOnly");
        	 List<ClaimViewBO> claimViewList = claimAccidentService.findAllClaimView(claimViewBO);
 			for(ClaimViewBO claimView:claimViewList){
 				if("4".equals(claimView.getCode())){
 					claimView.setViewNum("3");						
 				}
 				//@invalid 审核阶段标签点击做查看操作不记录页面轨迹
 				if("3".equals(claimView.getCode()) && !"1".equals(readOnly)){
 					claimView.setViewNum("3");	
 				}
 				//@invalid 2对页面的轨迹进行修改(根据赔案id来更新数据)
 				claimAccidentService.updateClaimView(claimView);
 			}
 			//1. 调用匹配理算service方法查询理赔赔案的信息
 	        ClaimCaseBO tClaimCaseBO = new ClaimCaseBO();
 	        tClaimCaseBO.setCaseId(new BigDecimal(commonMapVO.get("caseId")+"")); 
 	        tClaimCaseBO = claimMatchResultService.queryClaimCase(tClaimCaseBO);
 	        ClaimCaseVO claimCaseVO=new ClaimCaseVO();
 	        claimCaseVO= BeanUtils.copyProperties(ClaimCaseVO.class, tClaimCaseBO);
 	        resultMapVO.put("claimCaseVO", claimCaseVO);
 	       //2. 调用匹配理算service方法查询理赔事件信息
 	       ClaimAccidentVO claimAccidentVO=new ClaimAccidentVO(); 
 	       ClaimAccidentBO claimAccidentBO=new ClaimAccidentBO();
 	       claimAccidentBO.setAccidentId(claimCaseVO.getAccidentId());
 	       claimAccidentBO=claimMatchResultService.queryClaimAccident(claimAccidentBO);
 	       claimAccidentVO= BeanUtils.copyProperties(ClaimAccidentVO.class, claimAccidentBO);
 	       resultMapVO.put("claimAccidentVO", claimAccidentVO);
 	       //3. 调用匹配理算service方法查询出要理算之前的理算金额
 	      ClaimLiabBO claimLiabBo = new ClaimLiabBO();
 	     claimLiabBo.setCaseId(tClaimCaseBO.getCaseId());
 	      List<ClaimLiabBO> claimLiabBOList = claimMatchResultService.queryClaimLiabByCaseId(claimLiabBo);
 	      String policyCode = "";
 	      String busiProdCode = "";
 	      String liabCode = "";
 	      String calcPayBefore = "";
 	      String liabStartDate = "";
 	      for(ClaimLiabBO claimLiabBO: claimLiabBOList){
 	        policyCode += claimLiabBO.getPolicyCode() +",";
 	        busiProdCode += claimLiabBO.getBusiProdCode() +",";
 	        liabCode += claimLiabBO.getLiabCode() +",";
 	        calcPayBefore += claimLiabBO.getCalcPay() +",";
 	        SimpleDateFormat sf  = new SimpleDateFormat("yyyy-MM-dd");
 	        String liabStartDat = sf.format(claimLiabBO.getLiabStartDate());
 	       liabStartDate += liabStartDat +",";
 	      }
 	      if(StringUtils.isNotBlank(policyCode)){
 	          claimLiabBo.setPolicyCode(policyCode.substring(ClaimConstant.ZERO,policyCode.length()-1));
 	      }
 	      if(StringUtils.isNotBlank(busiProdCode)){
 	          claimLiabBo.setBusiProdCode(busiProdCode.substring(ClaimConstant.ZERO,busiProdCode.length()-1));
 	      }
 	      if(StringUtils.isNotBlank(liabCode)){
 	          claimLiabBo.setLiabCode(liabCode.substring(ClaimConstant.ZERO,liabCode.length()-1));
 	      }
 	      if(StringUtils.isNotBlank(liabStartDate)){
 	          claimLiabBo.setLiabStartDateStr(liabStartDate.substring(ClaimConstant.ZERO,liabStartDate.length()-1));
 	      }
 	      resultMapVO.put("claimLiabVOBefore", BeanUtils.copyProperties(ClaimLiabVO.class, claimLiabBo));
 	      resultMapVO.put("calcPayBefore", calcPayBefore);
 	      ClaimCaseBO claimCaseBO = claimCaseVO.convertToBO();
 	      if(commonMapVO.get("isCalc")==null||!"0".equals(commonMapVO.get("isCalc"))){
			    if(commonMapVO.get("isApprove")==null||"".equals(commonMapVO.get("isApprove"))){
				    	
						try {
							  claimMatchCalcService.calcClaim(claimCaseBO);
						} catch (Exception e) {
							e.printStackTrace(); 
							throw new BizException("匹配理算失败");
						}
			    }
		  }
 	    //4. 调用匹配理算service方法汇总赔案金额 
     	claimMatchResultService.updateClaimCaseActualPay(claimCaseBO);
     	//5. 调用匹配理算service方法查询赔案计算信息
     	ClaimCaseBO claimCaseBO2 = new ClaimCaseBO();
        BeanUtils.copyProperties(claimCaseBO2, claimCaseVO);
        resultMapVO.put("claimCaseList",  BeanUtils.copyList(ClaimCaseVO.class, claimMatchResultService.queryClaimCalc(claimCaseBO2)));
 	    
        //6. 调用匹配理算service方法查询理赔类型计算信息
        ClaimLiabBO claimLiabBO = new ClaimLiabBO();
        claimLiabBO.setCaseId(claimCaseVO.getCaseId());
        resultMapVO.put("claimTypeCalcList",  BeanUtils.copyList(ClaimLiabVO.class, claimMatchResultService.queryClaimTypeCalc(claimLiabBO)));
       
        //7. 调用匹配理算service方法查询保单计算信息
        ClaimBusiProdBO claimBusiProdBO = new ClaimBusiProdBO(); 
        claimBusiProdBO.setCaseId(claimCaseVO.getCaseId());
        resultMapVO.put("claimProductList", BeanUtils.copyList(ClaimBusiProdVO.class, claimMatchResultService.queryClaimBusiProd(claimBusiProdBO , "one")));
        //8. 调用匹配理算service方法查询保项计算信息
        resultMapVO.put("queryClaimLiabList",BeanUtils.copyList(ClaimLiabVO.class, claimMatchResultService.queryClaimLiab(claimLiabBO)));
        resultMapVO.put("dataMessage", claimCaseBO.getMessage());
        return resultMapVO;
        }
        
        /**
         * 
         * @description 判断保单出险日距离保单生效日期是否在两年内 
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#claimCheckValidDate(com.nci.tunan.clm.interfaces.model.vo.ContractMasterVO)
         * @param contractMasterVO 保单抄单信息VO对象
         * @return 判断结果
         */
        @Override
        public boolean claimCheckValidDate(ContractMasterVO contractMasterVO) {
        	boolean flag = false;
        	//调用匹配理算service方法判断保单出险日距离保单生效日期是否在两年内
        	flag = claimMatchResultService.claimCheckValidDate(BeanUtils.copyProperties(ContractMasterBO.class, contractMasterVO));
        	return flag;
        }
        
        /**
         *  
         * 合同/险种满足：
         * （1）保险期间在1年（不含）以上；
         * （2）保险期间1年（含）且可保证续保，检验案件出险日期距离险种生效日超过两年，非阻断提示
		 * 特殊情况：
		 * （1）涉及复效的险种以复效日与出险日期的比较确定是否超过2年；
		 * （2）涉及新增附加险的以新增附加险承保日与出险日期的比较确定是否超过2年；
		 * （3）保险期间在1年以下（含）且保证续保的险种，以最早一期保单生效日与与出险日期的比较确定是否超过2年。
         */
        /**
         * 
         * @description 检验案件出险日期距离险种生效日超过两年
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#claimCheckMisrepresentations(com.nci.tunan.clm.interfaces.model.vo.MisrepresentationsFactorVO)
         * @param misrepresentationsFactorVO 不如实告知规则VO对象
         * @return 判断标识
         */
        @Override
        public boolean claimCheckMisrepresentations(MisrepresentationsFactorVO misrepresentationsFactorVO) {
        	boolean flag = false;
        	if(misrepresentationsFactorVO.getCaseId()!=null&&misrepresentationsFactorVO.getPolicyCode()==null){
        		ClaimBusiProdBO claimBusiProdBO = new ClaimBusiProdBO(); 
        		claimBusiProdBO.setCaseId(misrepresentationsFactorVO.getCaseId());
        		//1. 调用匹配理算service方法查询理算险种集合
        		List<ClaimBusiProdBO> claimProductList =claimMatchResultService.queryClaimBusiProd(claimBusiProdBO , "one");
        		for(ClaimBusiProdBO claimBusiProd:claimProductList){
        			misrepresentationsFactorVO.setPolicyCode(claimBusiProd.getPolicyCode());
        			misrepresentationsFactorVO.setBusiProdCode(claimBusiProd.getBusiProdCode());
        			
        			//1.1调用匹配理算service方法循环检验案件出险日期距离险种生效日超过两年
        			flag = claimMatchResultService.claimCheckMisrepresentations(BeanUtils.copyProperties(MisrepresentationsFactorBO.class, misrepresentationsFactorVO));
        			
        			if(flag){
        				break;
        			}else{
        				continue;
        			}
        		}
        	}else{
        		flag = claimMatchResultService.claimCheckMisrepresentations(BeanUtils.copyProperties(MisrepresentationsFactorBO.class, misrepresentationsFactorVO));
        	}
        	return flag;
        }
        
        /**
         * 
         * @description 重算案件权限
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#findReComputeCaseLevel(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
         * @param claimCaseVO 理赔赔案主表VO对象
         * @return 案件权限
         */
        public String  findReComputeCaseLevel(ClaimCaseVO claimCaseVO){ 
        	//调用匹配理算service方法根据据入参对象重算案件权限
        	return  claimMatchResultService.findReComputeCaseLevel(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO)); 
        }
        
        
        /**
         * 
         * @description 重算案件权限
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#updateComputePermission(com.nci.tunan.clm.interfaces.model.vo.CommonMapVO)
         * @param commonMapVO 组合入参
         * @return 提示信息集合
         */
        public CommonMapVO  updateComputePermission(CommonMapVO commonMapVO){ 
        	CommonMapVO resultMapVO=new CommonMapVO();
        	//@invalid 判断页面提交赔案信息是否为空
        	if(commonMapVO.get("claimCaseVO")!=null){ 
                 ClaimCaseVO claimCaseVO=(ClaimCaseVO)commonMapVO.get("claimCaseVO");
                 //1. 调用匹配理算service方法查询赔案详细信息
                 ClaimCaseVO resultclaimcase = BeanUtils.copyProperties( ClaimCaseVO.class,claimCaseService.findClaimCase(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO)));
            	 String resultLevel = findReComputeCaseLevel(claimCaseVO);
            	 
        		 String claimLevel = resultclaimcase.getAuditPermissionName();
        		 //@invalid 判断权限是否高于之前的权限，并给出提示信息
        		 long userNameId = AppUserContext.getCurrentUser().getUserId();
                 ClaimTaskStaffBO claimTaskStaffBO = new ClaimTaskStaffBO();
                 claimTaskStaffBO.setUserId(new BigDecimal(userNameId));
                 claimTaskStaffBO= claimTaskStaffService.queryClaimTaskStaffByUserId(claimTaskStaffBO);
                 //2. 审核最大权限是否为空
                 Integer maxAudit =ClaimConstant.ZERO;
                 if (claimTaskStaffBO.getAuditTo() != null && !"".equals(claimTaskStaffBO.getAuditTo())) {
                	 maxAudit = Integer.valueOf(claimTaskStaffBO.getAuditTo().substring(claimTaskStaffBO.getAuditTo().length()-2, claimTaskStaffBO.getAuditTo().length()));
				}else{
					UserPermissionVO userPer = new UserPermissionVO();
	                userPer = DataPermissionUtil.getInstance().getPermissionLevel(userNameId,"审核权限");
					if (userPer.getMaxPermissionLevel()!= null && userPer.getMaxPermissionLevel() > 0) {
						maxAudit=userPer.getMaxPermissionLevel();
					}
				}
        		 //3. 判断权限是否高于之前的权限
        		 if (!resultLevel.equals("") && claimLevel != null && 
        		         (Integer.valueOf(resultLevel.substring(resultLevel.length()-2, resultLevel.length())) > Integer.valueOf(claimLevel.substring(claimLevel.length()-2, claimLevel.length())))) {
        			  
        			 if (maxAudit != null && maxAudit> 0 && 
        			         (Integer.valueOf(resultLevel.substring(resultLevel.length()-2, resultLevel.length())) > maxAudit)) {
        				 logger.debug("案件ID:"+claimCaseVO.getCaseId()+"权限等级:"+resultLevel+",用户userNameId最大权限等级:"+maxAudit+",需要重新分配。");
        				 resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_300);
                    	 resultMapVO.put("Msg", "案件超权限，需要进行重新分配。");
    				 }else{
    				     resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_200);
    	                 resultMapVO.put("Msg", "");
    				 }
                 }else{
                	 resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_200);
                	 resultMapVO.put("Msg", "");
                 }
        	}else {
        		 resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_200);
            	 resultMapVO.put("Msg", "");
        	}
        	  
    		return resultMapVO;
        }
        
        /**
         * 
         * @description 重新分配任务
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#updateAssignTask(com.nci.tunan.clm.interfaces.model.vo.CommonMapVO)
         * @param commonMapVO 组合入参
         * @return 提示信息集合
         */
        public CommonMapVO  updateAssignTask(CommonMapVO commonMapVO){ 
        	CommonMapVO resultMapVO=new CommonMapVO(); 
        	//@invalid 获取页面传值
    		if(commonMapVO.get("claimCaseVO")!=null){
    			ClaimCaseVO claimCaseVO = (ClaimCaseVO)commonMapVO.get("claimCaseVO");
    			String taskId = claimCaseVO.getTaskId();
    			 claimCaseVO =BeanUtils.copyProperties( ClaimCaseVO.class,claimCaseService.findClaimCase(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO)));
        		 claimCaseVO.setTaskId(taskId);
        		//调用匹配理算service方法重新分配任务，并给出提示信息 
        		boolean flag =  reAssignTask(claimCaseVO);
                if (flag) {
                	
                	 resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_200);
                	 resultMapVO.put("Msg", Constants.DWZ_MESSAGE_200); 
                } else {
                	 resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_300);
                	 resultMapVO.put("Msg", "重新分配任务失败,请主管重新分配。"); 
                }
    			
    		}
    		 
    		 return resultMapVO;
        }
 
    /**
     * 
     * @description 匹配理算，页面初始化方法
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#updateClaimMatchCalcInit(com.nci.tunan.clm.interfaces.model.vo.CommonMapVO)
     * @param commonMapVO 组合入参对象
     * @return 查询信息集合
     * @throws BizException
     */
    public CommonMapVO updateClaimMatchCalcInit(CommonMapVO commonMapVO) throws BizException{
    	//@invalid 1设置入参对象
    	ClaimCaseVO claimCaseVO = (ClaimCaseVO)commonMapVO.get("claimCaseVO");
    	ClaimCaseCheatVO claimCaseCheatVO = (ClaimCaseCheatVO)commonMapVO.get("claimCaseCheatVO");
    	ClaimBusiProdVO claimBusiProdVO = (ClaimBusiProdVO)commonMapVO.get("claimBusiProdVO");
    	ClaimAccidentVO claimAccidentVO = (ClaimAccidentVO)commonMapVO.get("claimAccidentVO");
    	ClaimLiabVO claimLiabVO = (ClaimLiabVO)commonMapVO.get("claimLiabVO");
    	BigDecimal caseId = (BigDecimal)commonMapVO.get("caseId");
    	String isCalc = (String)commonMapVO.get("isCalc");
    	List<ClaimCaseVO> claimCaseList = (List<ClaimCaseVO>)commonMapVO.get("claimCaseList");
    	List<ClaimLiabVO> claimTyepCalcList = (List<ClaimLiabVO>)commonMapVO.get("claimTyepCalcList");
    	List<ClaimBusiProdVO> claimProductList = (List<ClaimBusiProdVO>)commonMapVO.get("claimProductList");
    	List<ClaimLiabVO> queryClaimLiabList = (List<ClaimLiabVO>)commonMapVO.get("queryClaimLiabList");
    	
    	if (claimCaseVO == null) {
            claimCaseVO = new ClaimCaseVO();
        }
    	if (claimCaseCheatVO == null) {
    	    claimCaseCheatVO = new ClaimCaseCheatVO();
    	}
        if (claimBusiProdVO == null) {
            claimBusiProdVO = new ClaimBusiProdVO();
        }
        if (claimAccidentVO == null) {
            claimAccidentVO = new ClaimAccidentVO();
        }
        if (claimLiabVO == null) {
            claimLiabVO = new ClaimLiabVO();
        }
        //@invalid 修改页面轨迹(根据赔案id来更新数据)
        ClaimViewVO claimViewVO = new ClaimViewVO();
        claimViewVO.setCaseId(caseId);
        ClaimViewVO viewVO = BeanUtils.copyProperties(ClaimViewVO.class, claimAccidentService.findViewCode(
        		BeanUtils.copyProperties(ClaimViewBO.class, claimViewVO)));
        if (!viewVO.getViewNum().equals("4")) {
            viewVO.setViewNum("4");
            claimAccidentService.updateClaimView(BeanUtils.copyProperties(ClaimViewBO.class, viewVO));
        }
        
        claimBusiProdVO.setCaseId(caseId);
        claimLiabVO.setCaseId(caseId);
        claimCaseVO.setCaseId(caseId);
        ClaimCaseVO tClaimCaseVO = new ClaimCaseVO();
        tClaimCaseVO.setCaseId(caseId);
        //1. 查询理赔赔案信息
        claimCaseVO = queryClaimCase(tClaimCaseVO);
        ClaimAccidentVO tClaimAccidentVO = new ClaimAccidentVO();
        tClaimAccidentVO.setAccidentId(claimCaseVO.getAccidentId());
        //2. 查询理赔事件信息
        claimAccidentVO = queryClaimAccident(tClaimAccidentVO);
        //3. 根据入参标识和赔案信息判断是否调用自动匹配理算
        if (isCalc == null || !"0".equals(isCalc)) {
            claimMatchCalcUCC.findMatchCalc(claimCaseVO);
        }
        //4. 进行赔案理算金额合计并更新赔案主表
        updateClaimCaseActualPay(claimCaseVO);
        //5. 查询理赔类型计算信息
        claimTyepCalcList = queryClaimTypeCalc(claimLiabVO);
        //6. 查询赔案计算信息
        claimCaseList = queryClaimCalc(claimCaseVO);
        //7. 查询保单计算信息
        claimProductList = queryClaimBusiProd(claimBusiProdVO, "one");
        //8. 查询保项计算信息
        queryClaimLiabList = queryClaimLiab(claimLiabVO);
        //9. 查询欺诈案件信息
        claimCaseCheatVO.setCaseId(caseId);
        ClaimCaseCheatBO claimCaseCheatBO = claimMatchCalcService.queryClaimCaseCheat(BeanUtils.copyProperties(ClaimCaseCheatBO.class, claimCaseCheatVO));
        //10. 查询是否是业务员自保件
        boolean selfInsuranceFlag = claimMatchResultService.querySalesmanSelfInsurance(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO), 
                BeanUtils.copyList(ClaimProductBO.class, claimProductList));
        if(selfInsuranceFlag){
            claimCaseVO.setSalesmanSelfInsurance(ClaimConstant.BIGDECIMAL_ONE);
        } else {
            claimCaseVO.setSalesmanSelfInsurance(ClaimConstant.BIGDECIMAL_ZERO);
        }
        //11.存在税优长期险且出险日处于缴费宽限期
        String taxpremiumProduct = claimMatchResultService.queryTaxpremiumArrearsProducts(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO),false);
        if(StringUtils.isNotBlank(taxpremiumProduct)){
        	claimCaseVO.setTaxPremiumFlag(ClaimConstant.BIGDECIMAL_ONE);
        }else{
        	claimCaseVO.setTaxPremiumFlag(ClaimConstant.BIGDECIMAL_ZERO);
        }
        //@invalid 13组合返回初始化所需查询信息
        commonMapVO.put("dataMessage", claimCaseVO.getMessage());
        commonMapVO.put("claimCaseVO", claimCaseVO);
    	commonMapVO.put("claimBusiProdVO", claimBusiProdVO);
    	commonMapVO.put("claimAccidentVO", claimAccidentVO);
    	commonMapVO.put("claimLiabVO", claimLiabVO);
    	commonMapVO.put("caseId", caseId);
    	commonMapVO.put("isCalc", isCalc);
    	commonMapVO.put("claimCaseList", claimCaseList);
    	commonMapVO.put("claimTyepCalcList", claimTyepCalcList);
    	commonMapVO.put("claimProductList", claimProductList);
    	commonMapVO.put("queryClaimLiabList", queryClaimLiabList);
    	commonMapVO.put("claimCaseCheatVO", BeanUtils.copyProperties(ClaimCaseCheatVO.class,claimCaseCheatBO));
        return commonMapVO;
    }
    
    /**
     * 
     * @description 查询立案理算信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 理赔赔案主表VO对象
     * @param claimTyepCalcList 理赔类型层理算信息
     * @return 返回赔案理算信息
     */
    private List<ClaimCaseVO> queryClaimCalcRegister(ClaimCaseVO claimCaseVO, List<ClaimLiabVO> claimTyepCalcList) {
        //@invalid 设置查询入参对象
        ClaimCaseBO claimCaseBO = BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO);
        List<ClaimLiabBO> claimTyepCalcListBO = BeanUtils.copyList(ClaimLiabBO.class, claimTyepCalcList);
        //调用匹配理算service方法查询赔案理算信息
        return BeanUtils.copyList(ClaimCaseVO.class, claimMatchResultService.queryClaimCalcRegister(claimCaseBO,claimTyepCalcListBO));
    }
    
    /**
	 * 
	 * @description 审核任务释放回共享池
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseVO 理赔赔案主表VO对象
	 * @return 释放成功标识
	 */
	public boolean releaseAuditTaskExecute(ClaimCaseVO claimCaseVO){
		//根据入参释放任务
		return claimMatchResultService.releaseAuditTaskExecute(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO));
	}
	
	        
        /**
         * 
         * @description 审核任务释放回共享池
         * @version V1.0.0
         * @title
         * <AUTHOR> <EMAIL>
         * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#releaseAuditTask(com.nci.tunan.clm.interfaces.model.vo.CommonMapVO)
         * @param commonMapVO 组合入参
         * @return 提示信息集合
         */
    	@Override
    	public CommonMapVO releaseAuditTask(CommonMapVO commonMapVO) {
    		//设置释放任务所需参数,进行任务释放
    		CommonMapVO resultMapVO=new CommonMapVO(); 
    		if(commonMapVO.get("claimCaseVO")!=null){
    			ClaimCaseVO claimCaseVO = (ClaimCaseVO)commonMapVO.get("claimCaseVO");
    			String taskId = claimCaseVO.getTaskId();
    			claimCaseVO =BeanUtils.copyProperties( ClaimCaseVO.class,claimCaseService.findClaimCase(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO)));
    			claimCaseVO.setTaskId(taskId);
    			//@invalid 进行任务释放
    			boolean flag =  releaseAuditTaskExecute(claimCaseVO);
    			if(flag){
    				resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_200);
    				resultMapVO.put("Msg", Constants.DWZ_MESSAGE_200); 
    			}else{
    				resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_STATUSCODE_300);
    				resultMapVO.put("DWZ_STATUSCODE", Constants.DWZ_MESSAGE_300);
    			}
    			
    		}
    		return resultMapVO;
    	}

    /**
     * 
     * @description 匹配理算，页面初始化方法
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#updateClaimMatchResult(com.nci.tunan.clm.interfaces.model.vo.CommonMapVO)
     * @param commonMapVO 组合入参对象
     * @return 返回页面信息
     * @throws BizException
     */
    public CommonMapVO updateClaimMatchResult(CommonMapVO commonMapVO) throws BizException{
        CommonMapVO resultMapVO=new CommonMapVO();
        
        ClaimLiabVO claimLiabVO  = (ClaimLiabVO)commonMapVO.get("claimLiabVO");
        //@invalid 查询liab表和关闭表
        ClaimLiabBO claimLiabBO = claimMatchResultService.queryLiabByCaseIdAndClaimLiab(claimLiabVO.getCaseId(),claimLiabVO.getClaimLiabId());
        ClaimLiabCloseBO claimLiabCloseBO = claimMatchResultService.queryLiabCloseByCaseIdAndClaimLiab(claimLiabVO.getCaseId(),claimLiabVO.getClaimLiabId());
        //@invalid 1据入参更新保项赔付结论
        updateClaimMatchResult(claimLiabVO);
        //@invalid 查询修改后的claim_liab_id
        BigDecimal claimLiabId = claimMatchResultService.queryClaimLiabId(claimLiabBO,claimLiabCloseBO,claimLiabVO.getLiabConclusion());
        List<ClaimLiabVO> claimTypeCalcList =  queryClaimTypeCalc(claimLiabVO);
        ClaimBusiProdBO claimBusiProdBO = new ClaimBusiProdBO(); 
        claimBusiProdBO.setCaseId(claimLiabVO.getCaseId());
        //1. 调用匹配理算service方法查询险种理算信息
        List<ClaimBusiProdBO> claimBusiProdBOs = claimMatchResultService.queryClaimBusiProd(claimBusiProdBO , "one");
       
        ClaimCaseBO claimCaseBO = new ClaimCaseBO();
        claimCaseBO.setCaseId(claimLiabVO.getCaseId());
        //2. 调用匹配理算service方法查询赔案理算信息
        ClaimCaseVO claimCaseVO = new ClaimCaseVO();
        List<ClaimCaseBO> claimBOs = claimMatchResultService.queryClaimCalc(claimCaseBO);
        if(claimBOs==null || claimBOs.size()==0){
        	claimCaseVO = BeanUtils.copyProperties(ClaimCaseVO.class,claimBOs);
        }
        if(claimBOs.size() > 0){
        	claimCaseVO = BeanUtils.copyProperties(ClaimCaseVO.class,claimBOs.get(0));
        }
        
        claimCaseVO.setClaimTypeCalcList(claimTypeCalcList);
        claimCaseVO.setClaimPolicyCalcList(BeanUtils.copyList(ClaimBusiProdVO.class, claimBusiProdBOs));
        claimCaseVO.setClaimLiabId(claimLiabId);
        //3存在税优长期险且出险日处于缴费宽限期
        if(claimMatchCalcService.queryTaxpremiumFlag(claimCaseBO)){
        	claimCaseVO.setTaxPremiumFlag(ClaimConstant.BIGDECIMAL_ONE);
        }else{
        	claimCaseVO.setTaxPremiumFlag(ClaimConstant.BIGDECIMAL_ZERO);
        }
        resultMapVO.put("claimCase",  claimCaseVO);
       
       return resultMapVO;
    }
    /**
     * 
     * @description 查询产品规则消息提醒
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#queryProductRuleMessage(java.math.BigDecimal)
     * @param caseId 赔案id
     * @return 提示信息
     */
    public Map<String,String> queryProductRuleMessage(ClaimCaseVO claimCaseVO){
    	//@invalid 调用匹配理算service方法根据赔案id查询消息提醒
    	return claimMatchResultService.queryProductRuleMessage(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO));
    } 
    


    /**
     * 
     * @description 还原审核权限
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#reChangePermission(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
     * @param claimCaseVO 赔案主表VO对象
     */
    @Override
	public void reChangePermission(ClaimCaseVO claimCaseVO) {
    	//还原审核权限
		claimMatchResultService.reChangePermission(BeanUtils.copyProperties(ClaimCaseBO.class,claimCaseVO));
	}

	@Override
	public void updateClaimPolicyBusiProdAndProduct(ClaimCaseVO claimCaseVO) {
		ClaimCaseBO claimCaseBO = new ClaimCaseBO();
    	claimCaseBO.setCaseId(claimCaseVO.getCaseId());
    	claimMatchCalcService.updateClaimPolicyBusiProdAndProduct(claimCaseBO);
	}

	/**
	 * @description 重置理算表（保单，险种，责任组）数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimMatchResultUCC#updateClaimPolicyBusiProdAndProductNew(com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO)
	 * @param claimCaseVO 
	*/
	@Override
	public void updateClaimPolicyBusiProdAndProductNew(ClaimCaseVO claimCaseVO) {
		ClaimCaseBO claimCaseBO = new ClaimCaseBO();
		claimCaseBO.setCaseId(claimCaseVO.getCaseId());
		claimMatchCalcService.updateClaimPolicyBusiProdAndProductNew(claimCaseBO);
	}
	
	@Override
	public boolean queryLockUw(ClaimCaseVO claimCaseVO) {
		// TODO Auto-generated method stub
		//查询当前赔案得涉案保单
		ClaimCaseBO claimCaseBO = BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO);
		
		List<String> policyList = claimMatchCalcService.queryRelatedCasePolicyByCaseNo(claimCaseBO);
		
		boolean policyResult = claimMatchCalcService.queryLockUw(policyList);

		return policyResult;
	}
}
