package com.nci.tunan.clm.impl.register.service.impl;

import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import javax.xml.namespace.QName;

import com.nci.tunan.clm.dao.IClaimHospitalServiceDao;
import com.nci.tunan.clm.dao.ISurveyConclusionDao;
import com.nci.tunan.clm.interfaces.model.po.ClaimHospitalServicePO;
import com.nci.tunan.clm.interfaces.model.po.SurveyConclusionPO;
import com.nci.tunan.clm.simplecase.service.bd.ClaimFunctionMain;
import com.nci.tunan.clm.simplecase.service.bd.ClaimFunctionMainDetailItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.slf4j.Logger;

import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.impl.util.CodeMapperUtils;
import com.nci.core.common.interfaces.vo.BlacklistCommonVO;
import com.nci.core.common.interfaces.vo.BlacklistReqVo;
import com.nci.core.common.interfaces.vo.BlacklistRspVo;
import com.nci.core.common.interfaces.vo.CustomerBaseInfoVO;
import com.nci.tunan.clm.BillQuerySrvPortType.BillQuerySrvBindingQSService;
import com.nci.tunan.clm.BillQuerySrvPortType.BillQuerySrvPortType;
import com.nci.tunan.clm.BillQuerySrvPortType.service.bd.Bill;
import com.nci.tunan.clm.BillQuerySrvPortType.service.bd.BillList;
import com.nci.tunan.clm.BillQuerySrvPortType.service.bd.PastClaim;
import com.nci.tunan.clm.BillQuerySrvPortType.service.bd.Rule;
import com.nci.tunan.clm.dao.IAgentDao;
import com.nci.tunan.clm.dao.IBusinessProductDao;
import com.nci.tunan.clm.dao.IClaimAccidentDao;
import com.nci.tunan.clm.dao.IClaimAccidentResultDao;
import com.nci.tunan.clm.dao.IClaimAuditConclusionDao;
import com.nci.tunan.clm.dao.IClaimAutoRuleResultDao;
import com.nci.tunan.clm.dao.IClaimBillDao;
import com.nci.tunan.clm.dao.IClaimBillItemDao;
import com.nci.tunan.clm.dao.IClaimBillPaidDao;
import com.nci.tunan.clm.dao.IClaimBlackNameDao;
import com.nci.tunan.clm.dao.IClaimBusiProdDao;
import com.nci.tunan.clm.dao.IClaimCaseDao;
import com.nci.tunan.clm.dao.IClaimCheatAtlasDao;
import com.nci.tunan.clm.dao.IClaimDoctorDao;
import com.nci.tunan.clm.dao.IClaimImageScanDao;
import com.nci.tunan.clm.dao.IClaimLiabDao;
import com.nci.tunan.clm.dao.IClaimMemoDao;
import com.nci.tunan.clm.dao.IClaimPayeeDao;
import com.nci.tunan.clm.dao.IClaimPolicyDao;
import com.nci.tunan.clm.dao.IClaimProductDao;
import com.nci.tunan.clm.dao.IClaimRiskDoubtfulInfoDao;
import com.nci.tunan.clm.dao.IClaimRiskLevelLiabDao;
import com.nci.tunan.clm.dao.IClaimRiskLownumDao;
import com.nci.tunan.clm.dao.IClaimSubCaseDao;
import com.nci.tunan.clm.dao.IContractAgentDao;
import com.nci.tunan.clm.dao.IContractBeneDao;
import com.nci.tunan.clm.dao.IContractBusiProdDao;
import com.nci.tunan.clm.dao.IContractMasterDao;
import com.nci.tunan.clm.dao.IContractProductDao;
import com.nci.tunan.clm.dao.ICustomerDao;
import com.nci.tunan.clm.dao.IDataCollectDao;
import com.nci.tunan.clm.dao.IInsuredListDao;
import com.nci.tunan.clm.dao.ILegalPersonBeneInfoDao;
import com.nci.tunan.clm.dao.ILegalPersonInfoDao;
import com.nci.tunan.clm.dao.IPolicyHolderDao;
import com.nci.tunan.clm.dao.ISurveyApplyDao;
import com.nci.tunan.clm.dao.IUserDao;
import com.nci.tunan.clm.impl.calc.service.IClaimMatchCalcService;
import com.nci.tunan.clm.impl.common.ucc.IClaimRealNameCheckUCC;
import com.nci.tunan.clm.impl.direct.service.IDirectClaimPortsService;
import com.nci.tunan.clm.impl.register.service.ICheckRuleVerifyService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.impl.util.ClaimIDCardValidity;
import com.nci.tunan.clm.impl.util.CodeUtils;
import com.nci.tunan.clm.imports.ICLMServiceUtil;
import com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.ClaimMedicalAntiFraudSrvBindingQSService;
import com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.ClaimMedicalAntiFraudSrvPortType;
import com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.ModelResult;
import com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.ModelResult1;
import com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.OutputData;
import com.nci.tunan.clm.interfaces.model.bo.BeneAndPayeeBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimAccidentResultBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimBeneBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimLiabBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPayeeBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPolicyHolderBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimSubCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.clm.interfaces.model.bo.LegalPersonBeneInfoBO;
import com.nci.tunan.clm.interfaces.model.bo.LegalPersonInfoBO;
import com.nci.tunan.clm.interfaces.model.po.AgentPO;
import com.nci.tunan.clm.interfaces.model.po.BeneAndPayeePO;
import com.nci.tunan.clm.interfaces.model.po.BusinessProductPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimAccidentPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimAccidentResultPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimAutoRuleResultPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBenePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBillItemPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBillPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBillPaidPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBlackNamePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCheatAtlasPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimDoctorPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimImageScanPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimMemoPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPayPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPayeePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPolicyHolderPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPolicyPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskDoubtfulInfoPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLevelLiabPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLownumPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO;
import com.nci.tunan.clm.interfaces.model.po.ContractAgentPO;
import com.nci.tunan.clm.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.clm.interfaces.model.po.ContractProductPO;
import com.nci.tunan.clm.interfaces.model.po.CustomerPO;
import com.nci.tunan.clm.interfaces.model.po.HospitalPO;
import com.nci.tunan.clm.interfaces.model.po.InsuredListPO;
import com.nci.tunan.clm.interfaces.model.po.LegalPersonBeneInfoPO;
import com.nci.tunan.clm.interfaces.model.po.LegalPersonInfoPO;
import com.nci.tunan.clm.interfaces.model.po.PolicyHolderPO;
import com.nci.tunan.clm.interfaces.model.po.SurveyApplyPO;
import com.nci.tunan.clm.interfaces.model.po.UserPO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO;
import com.nci.tunan.clm.interfaces.queryCaseFrms.ClaimRuleQuerySrvBindingQSService;
import com.nci.tunan.clm.interfaces.queryCaseFrms.ClaimRuleQuerySrvPortType;
import com.nci.tunan.clm.simplecase.CLSimpleCaseBRS;
import com.nci.tunan.clm.simplecase.CLSimpleCaseBRSService;
import com.nci.tunan.clm.simplecase.common.header.in.SysMsgHeader;
import com.nci.tunan.clm.simplecase.service.bd.Agent;
import com.nci.tunan.clm.simplecase.service.bd.Applicant;
import com.nci.tunan.clm.simplecase.service.bd.BusinessFunctionMain;
import com.nci.tunan.clm.simplecase.service.bd.BusinessFunctionMainDetailItem;
import com.nci.tunan.clm.simplecase.service.bd.BusinessProduct;
import com.nci.tunan.clm.simplecase.service.bd.ClaimModelList;
import com.nci.tunan.clm.simplecase.service.bd.HaveEndLiabilityCode;
import com.nci.tunan.clm.simplecase.service.bd.Image;
import com.nci.tunan.clm.simplecase.service.bd.ImageList;
import com.nci.tunan.clm.simplecase.service.bd.LiabilityGroup;
import com.nci.tunan.clm.simplecase.service.bd.Model;
import com.nci.tunan.clm.simplecase.service.bd.ModelList1;
import com.nci.tunan.clm.simplecase.service.bd.Payee;
import com.nci.tunan.clm.simplecase.service.bd.Policy;
import com.nci.tunan.clm.simplecase.service.bd.RuleInfo;
import com.nci.tunan.clm.simplecase.service.bd.SrvResBody;
import com.nci.tunan.clm.simplecase.service.bd.SubClaim;
import com.nci.tunan.clm.simplecase.service.bd.SurveyReport;
import com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData;
import com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData;
import com.nci.udmp.component.serviceinvoke.ServiceCommonMethod;
import com.nci.udmp.framework.consts.PubConstants;
import com.nci.udmp.framework.context.AppUser;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;

/** 
 * @description 简易自核
 * <AUTHOR> <EMAIL>
 * @date 2015年8月29日 下午7:17:13
 * @.belongToModule CLM-理赔系统/简易自核
*/
public class CheckRuleVerifyServiceImpl implements ICheckRuleVerifyService {
    
    /** 
    * @Fields SERVICE_NAMEBRS : 接口QName
    */ 
    private static final QName SERVICE_NAMEBRS = new QName("http://www.newchinalife.com", "CLSimpleCaseBRSService");
    
    /** 
    * @Fields SERVICE_NAMEBill : 接口QName
    */ 
    private static final QName SERVICE_NAMEBill = new QName("http://www.newchinalife.com", "BillQuerySrvBindingQSService");
    
    /**
	 * 法人信息配置Dao
	 */
	private ILegalPersonInfoDao legalPersonInfoDao;
	/**
	 * 法人信息关联受益人信息Dao
	 */
	private ILegalPersonBeneInfoDao legalPersonBeneInfoDao;
    
    /** 
    * @Fields claimSubCaseDao : 赔案子信息
    */ 
    private IClaimSubCaseDao claimSubCaseDao;
    
    /** 
    * @Fields claimAccidentResultDao : 出险结果
    */ 
    private IClaimAccidentResultDao claimAccidentResultDao;
    
    /** 
    * @Fields contractMasterDao : 保单基本信息抄单
    */ 
    private IContractMasterDao contractMasterDao;
    
    /** 
    * @Fields contractBusiProdDao : 保单险种抄单表 
    */ 
    private IContractBusiProdDao contractBusiProdDao;
    
    /** 
    * @Fields customerDao : 保单险种抄单表 
    */ 
    private ICustomerDao customerDao;
    
    /** 
    * @Fields claimCaseDao : 赔案主表
    */ 
    private IClaimCaseDao claimCaseDao;
    
    /** 
    * @Fields surveyApplyDao : 调查申请
    */ 
    private ISurveyApplyDao surveyApplyDao;
    
    /** 
    * @Fields auditConclusionDao : 出具审核结论
    */ 
    private IClaimAuditConclusionDao auditConclusionDao;
    
    /** 
    * @Fields userDao : 用户dao
    */ 
    private IUserDao userDao;
    
    /** 
    * @Fields claimAutoRuleResultDao :  赔案自核不通过原因信息结果表
    */ 
    private IClaimAutoRuleResultDao claimAutoRuleResultDao;
    
    /** 
    * @Fields claimPolicyDao : 赔案保单信息
    */ 
    private IClaimPolicyDao claimPolicyDao;
    /** 
    * @Fields contractAgentDao :  保单代理人抄单表
    */ 
    private IContractAgentDao contractAgentDao;
    /** 
    * @Fields claimLiabDao : 理赔给付责任理算
    */ 
    private IClaimLiabDao claimLiabDao;
    /** 
    * @Fields contractBeneDao : 保单受益人抄单
    */ 
    private IContractBeneDao contractBeneDao;
    /** 
    * @Fields claimBillPaidDao : 社保/第三方支付信息
    */ 
    private IClaimBillPaidDao claimBillPaidDao;
    
    /** 
    * @Fields claimBlackNameDao : 黑名单
    */ 
    private IClaimBlackNameDao claimBlackNameDao;
    
    /** 
    * @Fields policyHolderDao : 投保人
    */ 
    private IPolicyHolderDao policyHolderDao;
    
    /** 
    * @Fields claimAccidentDao : 事件
    */ 
    private IClaimAccidentDao claimAccidentDao;
    
    /** 
    * @Fields insuredListDao : 保单被保人
    */ 
    private IInsuredListDao insuredListDao;
    
    /** 
    * @Fields claimBusiProdDao : 保单被保人
    */ 
    private IClaimBusiProdDao claimBusiProdDao;
    
    /** 
    * @Fields businessProductDao : 业务产品
    */ 
    private IBusinessProductDao businessProductDao;
    
    /** 
    * @Fields contractProductDao : 险种责任组抄单表
    */ 
    private IContractProductDao contractProductDao;
    
    /** 
    * @Fields claimBillDao : 账单信息
    */ 
    private IClaimBillDao claimBillDao;
    
    /** 
    * @Fields claimMemoDao : 问题件
    */ 
    private IClaimMemoDao claimMemoDao;
    
    /** 
    * @Fields claimCheatAtlasDao : 图谱dao
    */ 
    private IClaimCheatAtlasDao claimCheatAtlasDao;
    
    /**
     * 责任组理算信息DAO层定义
     */
	private IClaimProductDao claimProductDao;
	/**
	 * 保项风险等级DAO层定义
	 */
	private IClaimRiskLevelLiabDao claimRiskLevelLiabDao;
	/** 
    * 案件风险疑点信息DAO层定义
    */ 
    private IClaimRiskDoubtfulInfoDao claimRiskDoubtfulInfoDao;
	
    
    /** 
     * @Fields agentDao : 注入 dao 
     */
    private IAgentDao agentDao;
    
    /** 
     * @Fields dataCollectDao : 定义Service层的数据采集接口类型的实例供调用使用 
    */ 
    private IDataCollectDao dataCollectDao;
    
    /** 
    * @Fields claimPayeeDao : 领款人dao
    */ 
    private IClaimPayeeDao claimPayeeDao;
    
    /**
	 * 实名查验的Ucc
	 */
    private IClaimRealNameCheckUCC claimRealNameCheckUCC;
    /**
	 * 医生信息Dao
	 */
    private IClaimDoctorDao claimDoctorDao;
    /**
	 * 医疗费用明细表Dao
	 */
    private IClaimBillItemDao claimBillItemDao;
    /**
	 * 扫描记录信息表Dao
	 */
    private IClaimImageScanDao claimImageScanDao;
    /**
	 * 案件低风险查考值Dao
	 */
    private IClaimRiskLownumDao claimRiskLownumDao;
    /**
     * 服务总接口
     */
    private ICLMServiceUtil clmServiceUtil;
    /** 
    * @Fields  : 直赔接口处理service
    */ 
   private IDirectClaimPortsService directClaimPortsService;
    /**
     * 理算工具类
     */
    private IClaimMatchCalcService claimMatchCalcService;
	/**
	 * @Fields claimHospitalServiceDao : 理赔医院信息参数dao 
	 */
	private IClaimHospitalServiceDao claimHospitalServiceDao;
	/**
	 *  调查结论
	 */
	private ISurveyConclusionDao surveyConclusionDao;
    
    public IClaimRiskLownumDao getClaimRiskLownumDao() {
		return claimRiskLownumDao;
	}

	public void setClaimRiskLownumDao(IClaimRiskLownumDao claimRiskLownumDao) {
		this.claimRiskLownumDao = claimRiskLownumDao;
	}

	public IClaimImageScanDao getClaimImageScanDao() {
		return claimImageScanDao;
	}

	public void setClaimImageScanDao(IClaimImageScanDao claimImageScanDao) {
		this.claimImageScanDao = claimImageScanDao;
	}

	public IClaimBillItemDao getClaimBillItemDao() {
		return claimBillItemDao;
	}

	public void setClaimBillItemDao(IClaimBillItemDao claimBillItemDao) {
		this.claimBillItemDao = claimBillItemDao;
	}

	public IClaimDoctorDao getClaimDoctorDao() {
		return claimDoctorDao;
	}

	public void setClaimDoctorDao(IClaimDoctorDao claimDoctorDao) {
		this.claimDoctorDao = claimDoctorDao;
	}

	public IClaimPayeeDao getClaimPayeeDao() {
		return claimPayeeDao;
	}

	public void setClaimPayeeDao(IClaimPayeeDao claimPayeeDao) {
		this.claimPayeeDao = claimPayeeDao;
	}

	public IDataCollectDao getDataCollectDao() {
		return dataCollectDao;
	}

	public void setDataCollectDao(IDataCollectDao dataCollectDao) {
		this.dataCollectDao = dataCollectDao;
	}

	public IAgentDao getAgentDao() {
        return agentDao;
    }

    public void setAgentDao(IAgentDao agentDao) {
        this.agentDao = agentDao;
    }

    public IClaimLiabDao getClaimLiabDao() {
		return claimLiabDao;
	}

	public void setClaimLiabDao(IClaimLiabDao claimLiabDao) {
		this.claimLiabDao = claimLiabDao;
	}

	
	public IClaimProductDao getClaimProductDao() {
		return claimProductDao;
	}

	public void setClaimProductDao(IClaimProductDao claimProductDao) {
		this.claimProductDao = claimProductDao;
	}

	
	public IClaimRiskLevelLiabDao getClaimRiskLevelLiabDao() {
		return claimRiskLevelLiabDao;
	}

	public void setClaimRiskLevelLiabDao(
			IClaimRiskLevelLiabDao claimRiskLevelLiabDao) {
		this.claimRiskLevelLiabDao = claimRiskLevelLiabDao;
	}


	public IClaimRiskDoubtfulInfoDao getClaimRiskDoubtfulInfoDao() {
		return claimRiskDoubtfulInfoDao;
	}

	public void setClaimRiskDoubtfulInfoDao(
			IClaimRiskDoubtfulInfoDao claimRiskDoubtfulInfoDao) {
		this.claimRiskDoubtfulInfoDao = claimRiskDoubtfulInfoDao;
	}




	public IClaimRealNameCheckUCC getClaimRealNameCheckUCC() {
		return claimRealNameCheckUCC;
	}

	public void setClaimRealNameCheckUCC(
			IClaimRealNameCheckUCC claimRealNameCheckUCC) {
		this.claimRealNameCheckUCC = claimRealNameCheckUCC;
	}




	public ICLMServiceUtil getClmServiceUtil() {
		return clmServiceUtil;
	}

	public void setClmServiceUtil(ICLMServiceUtil clmServiceUtil) {
		this.clmServiceUtil = clmServiceUtil;
	}

    public IClaimMatchCalcService getClaimMatchCalcService() {
        return claimMatchCalcService;
    }

    public void setClaimMatchCalcService(IClaimMatchCalcService claimMatchCalcService) {
        this.claimMatchCalcService = claimMatchCalcService;
    }

	
	/** 
     * @Fields logger :  日志工具
     */
     private static Logger logger = LoggerFactory.getLogger();
    
    /**
     * 简易案件
     * @description
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.service.ICheckRuleVerifyService#checkRuleVerify(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 赔案信息
     * @return
     * @throws BizException 异常处理
     */
    @Override
    public String checkRuleVerify(ClaimCaseBO claimCaseBO) throws BizException {
        String status = "V01"; //@invalid V01：通过 V02：未通过
    	//@invalid 实名查验，不通过简易自核规则不通过，走普通审核
        ClaimCaseVO claimCaseVO = new ClaimCaseVO();
        claimCaseVO.setCaseId(claimCaseBO.getCaseId());
        Map<String,Object> realnameMap = claimRealNameCheckUCC.findRealNameChecks(claimCaseVO);        
//@invalid        //@invalid  测试图规则
        //1.调用图规则接口查询信息
//        queryCaseFrmsInfo(claimCaseBO);
        com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.OutputData outputData1 = queryCaseFrmsInfo(claimCaseBO);
//        com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.OutputData outputData1 = null;
        ///@invalid 增加校验规则，如果案件重复则直接进入审核
        String caseCheck=checkRepeatedCase(claimCaseBO);
        if(caseCheck.length()>0){
               status = "repeated";   
//@invalid               /* ClaimAutoRuleResultPO claimAutoRuleResultPO = new ClaimAutoRuleResultPO();
//@invalid                claimAutoRuleResultPO.setCaseId(claimCaseBO.getCaseId());
//@invalid                claimAutoRuleResultPO.setRuleStatus(ClaimConstant.STRING_TWO);//@invalid @invalid 1 通过 2 未通过
//@invalid                claimAutoRuleResultPO.setTypeCode("01");
//@invalid                claimAutoRuleResultPO.setRuleCode("LP");
//@invalid                claimAutoRuleResultPO.setRuleDesc(caseCheck);
//@invalid               claimAutoRuleResultPO.setRuleSetName("LP"); 
//@invalid               claimAutoRuleResultDao.addClaimAutoRuleResult(claimAutoRuleResultPO);
//@invalid                 */
        }
        
        try {
        	if ("50".equals(claimCaseBO.getCaseStatus()) || "51".equals(claimCaseBO.getCaseStatus()) 
        			|| "52".equals(claimCaseBO.getCaseStatus()) || "53".equals(claimCaseBO.getCaseStatus())
        			|| "54".equals(claimCaseBO.getCaseStatus())) {
        		status = "JJJJJJ";
        	} else {
            URL wsdlURL = CLSimpleCaseBRSService.WSDL_LOCATION;
            String wsdlString = wsdlURL.toString();
//            wsdlString = "http://**********:8111/services/P00002002628?wsdl"; 
                
			logger.info("***************************简易案件wsdlString:" + wsdlString + "*********************************");
//@invalid               wsdlString = "http://10.1.160.12:8111/services/P00002002628?wsdl";
                wsdlString = "http://*********:8111/services/P00002002628?wsdl"; // AP1
//                wsdlString = "http://**********:8111/services/P00002002628?wsdl"; //AP2
                JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdlString, "com.nci.tunan.clm.simplecase.CLSimpleCaseBRS");
            CLSimpleCaseBRS port = (CLSimpleCaseBRS)soapFactory.create();
            //@invalid CLSimpleCaseBRS port = uWNewContractBRS.getCLSimpleCaseBRSServicePort();
          //@invalid            /*CLSimpleCaseBRSService ss = new CLSimpleCaseBRSService(wsdlURL, SERVICE_NAMEBRS);
          //@invalid           CLSimpleCaseBRS port = ss.getCLSimpleCaseBRSServicePort();*/
            //@invalid  定义系统报文头
            SysMsgHeader parametersReqHeader = new SysMsgHeader();
            logger.info("***************************调简易案件进来喽*********************************");
            logger.info("***************************日期:"+WorkDateUtil.getWorkDate()+"*********************************");
			parametersReqHeader.setMsgId(UUID.randomUUID().toString());
			parametersReqHeader.setMsgDate(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), PubConstants.DATE_FORMAT));
			parametersReqHeader.setMsgTime(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), PubConstants.TIME_FORMAT_ESB));
			//@invalid parametersReqHeader.setServCd("P00002001747 ");
			parametersReqHeader.setServCd("P00002002628");
			parametersReqHeader.setSysCd("067"); 
			parametersReqHeader.setBizId("00000");
			parametersReqHeader.setBizType("1");
			parametersReqHeader.setOrgCd("86");
			parametersReqHeader.setResCd("1");
			parametersReqHeader.setResText("1");
			parametersReqHeader.setBizResCd("1");
			parametersReqHeader.setBizResText("1");
			parametersReqHeader.setVer("110.100.000");
			logger.info("***************************简易案件报文头赋值成功啦*********************************");
            //@invalid  定义系统报文体
            com.nci.tunan.clm.simplecase.service.bd.SrvReqBody parametersReqBody = new com.nci.tunan.clm.simplecase.service.bd.SrvReqBody();
            javax.xml.ws.Holder<SysMsgHeader> parametersResHeader = new javax.xml.ws.Holder<SysMsgHeader>();
            javax.xml.ws.Holder<SrvResBody> parametersResBody = new javax.xml.ws.Holder<SrvResBody>();
            //@invalid  定义业务报文体
            com.nci.tunan.clm.simplecase.service.bd.SrvReqBizBody srvReqBizBody = new com.nci.tunan.clm.simplecase.service.bd.SrvReqBizBody();
            parametersReqBody.setBizBody(srvReqBizBody);
            com.nci.tunan.clm.simplecase.service.hd.SRVReqHead ReqHead = new com.nci.tunan.clm.simplecase.service.hd.SRVReqHead();
            parametersReqBody.setBizHeader(ReqHead);
            com.nci.tunan.clm.simplecase.service.bd.Currentclaim currentclaim = new com.nci.tunan.clm.simplecase.service.bd.Currentclaim();
            List<BlacklistReqVo> blacklistReqVoList = new ArrayList<>(); 
            BlacklistReqVo blacklistReqVo1 = new BlacklistReqVo();
            BlacklistReqVo blacklistReqVo2 = new BlacklistReqVo();
            BlacklistReqVo blacklistReqVo3 = new BlacklistReqVo();
            BlacklistReqVo blacklistReqVo4 = new BlacklistReqVo();
            currentclaim.setSourceSys(true); 
            currentclaim.setModelResult(false);//@invalid 36577暂时写防止上生产掉Ilog报错
            //@invalid 查询赔案案件描述（出险信息中的最新事故描述）
            ClaimCasePO casePO = new ClaimCasePO();
            casePO.setCaseId(claimCaseBO.getCaseId());
            casePO = claimCaseDao.findClaimAccidentByCaseId(casePO);
            if(casePO.getData().get("acc_desc") != null && !"".equals(casePO.getData().get("acc_desc"))){
            	currentclaim.setCaseDiscription(casePO.getData().get("acc_desc").toString());
            }else{
            	currentclaim.setCaseDiscription(null);
            }
            ClaimBillPO claimBillPO = new ClaimBillPO();
            claimBillPO.setCaseId(claimCaseBO.getCaseId());
            //@invalid 查询单个票据最大住院天数 
            ClaimBillPO claimBill = claimBillDao.findClaimBillMaxDay(claimBillPO);
            if(claimBill.getDayTotal() != null){
            	currentclaim.setReceiptAmount(claimBill.getDayTotal().intValue());
            }
            //@invalid 查询赔案账单金额合计  
            claimBillPO = claimBillDao.findAllClaimBillSumAmount(claimBillPO);
            if(claimBillPO.getSumAmount() != null){
            	currentclaim.setCaseBillAmount(claimBillPO.getSumAmount().doubleValue());
            }
            //@invalid 查询理赔事故证明类单证影像页数
            ClaimImageScanPO claimImageScanPO = new ClaimImageScanPO();
            claimImageScanPO.setBussCode(casePO.getCaseNo());
            claimImageScanPO.setBillcardCode("CLM600");
            claimImageScanPO = claimImageScanDao.findClaimImageScan(claimImageScanPO);
            if(claimImageScanPO.getPages() != null){
            	currentclaim.setImageAmount(claimImageScanPO.getPages().intValue());
            }
            //@invalid 查询社保报销服务机构
            String applyOrgan = "";
            ClaimBillPaidPO claimBillPaidPO = new ClaimBillPaidPO();
            claimBillPaidPO.setCaseId(claimCaseBO.getCaseId());
            List<ClaimBillPaidPO> claimBillPaidPOList = claimBillPaidDao.findAllClaimBillPaid(claimBillPaidPO);
            for (ClaimBillPaidPO claimBillPaidPO2 : claimBillPaidPOList) {
				applyOrgan = applyOrgan + claimBillPaidPO2.getServiceOrgName() + ClaimConstant.COMMA;
			}
            if(applyOrgan != null && !"".equals(applyOrgan)){
            	applyOrgan = applyOrgan.substring(ClaimConstant.ZERO, applyOrgan.lastIndexOf(ClaimConstant.COMMA));
            }
            currentclaim.setApplyOrgan(applyOrgan);
            //@invalid 查询赔案特定手术代码
            String opCode = "";
            ClaimBillItemPO claimBillItemPO = new ClaimBillItemPO();
            claimBillItemPO.setCaseId(claimCaseBO.getCaseId());
            List<ClaimBillItemPO> claimBillItemPOList = claimBillItemDao.findAllClaimBillItem(claimBillItemPO);
            for (ClaimBillItemPO claimBillItemPO2 : claimBillItemPOList) {
            	if(claimBillItemPO2.getOperationCode() != null && !"".equals(claimBillItemPO2.getOperationCode())){
            		opCode = opCode + claimBillItemPO2.getOperationCode() + ClaimConstant.COMMA;
            	}
			}
            if(opCode != null && !"".equals(opCode)){
            	opCode = opCode.substring(ClaimConstant.ZERO, opCode.lastIndexOf(ClaimConstant.COMMA));
            }
            currentclaim.setOpCode(opCode);
            
            //@invalid 查询赔案层就诊科室
            String visitDepartment = "";
            ClaimDoctorPO claimDoctorPO = new ClaimDoctorPO();
            claimDoctorPO.setCaseId(claimCaseBO.getCaseId());
            List<ClaimDoctorPO> claimDoctorPOs = claimDoctorDao.findAllClaimDoctorByCaseId(claimDoctorPO);
            for(int i = 0; i < claimDoctorPOs.size(); i++){
            	visitDepartment=visitDepartment+claimDoctorPOs.get(i).getCode()+",";
            }
        	//@invalid 查询账单表就诊科室
        	ClaimBillPO medicalDeptCode = new ClaimBillPO();
        	medicalDeptCode.setCaseId(claimCaseBO.getCaseId());
            List<ClaimBillPO> medicalDeptCodes = claimBillDao.findAllClaimBillCalcByCaseId(medicalDeptCode);
            if(medicalDeptCodes.size()>0) {
            	for (ClaimBillPO billPO : medicalDeptCodes) {
            		if(billPO.getMedicalDeptCode()!=null) {
                		visitDepartment=visitDepartment+billPO.getMedicalDeptCode()+",";
            		}
            	}
            }
            if(visitDepartment!=null && !"".equals(visitDepartment)) {
            	visitDepartment = visitDepartment.substring(0,visitDepartment.length()-1); 
            }
            currentclaim.setVisitDepartment(visitDepartment);
          //@invalid 实名查验返回结果result含义：0-实名请求查验通过 ，2-实名查验请求不通过查验不通过 ，1-实名请求通过查验不通过，3-实名查验超出请求查验时间范围
            if(realnameMap.get("result")!= null && realnameMap.get("result").equals(ClaimConstant.STRING_ZERO)){
            	currentclaim.setRealNameCheck(ClaimConstant.STRING_TRUE);
            }else{
            	currentclaim.setRealNameCheck(ClaimConstant.STRING_FALSE);
            }
            UserPO userPO = new UserPO();
//@invalid           currentclaim.setBranchCode(""); //@invalid 机构代码(根据新华BA要求机构改成签收的机构,bug为3352)
//@invalid           if (claimCaseBO.getSignerId() != null) { 
//@invalid               userPO.setUserId(claimCaseBO.getSignerId());
//@invalid               userPO = userDao.findUserByUserId(userPO);
//@invalid               currentclaim.setBranchCode(userPO.getOrganCode()); //@invalid 机构代码(根据新华BA要求机构改成签收的机构,bug为3352)
//@invalid           }
            if(claimCaseBO.getOrganCode() != null && !"".equals(claimCaseBO.getOrganCode())){
            	currentclaim.setBranchCode(claimCaseBO.getOrganCode());//@invalid  签收机构直接取主表签收机构，不能取签收人所在机构（异地报案签收时未传签收人）
            }else{
            	throw new BizException("入参的签收机构不允许为空！");
            }
            //2.查询赔案信息、收益分配信息、理算信息信息、客户信息进行规则入参
            if (claimCaseBO.getRegisterId() != null) {
                userPO.setUserId(claimCaseBO.getRegisterId());
                userPO = userDao.findUserByUserId(userPO);
                if(userPO.getOrganCode()!=null) {
                	currentclaim.setLandbranchCode(userPO.getOrganCode()); //@invalid 机构代码(根据新华BA要求机构改成签收的机构,bug为3352)
                }else {
                	currentclaim.setLandbranchCode("86");
                }
            }else {
            	currentclaim.setLandbranchCode("86");
            }
            
            
            if (claimCaseBO.getActualPay() != null) {
                currentclaim.setBenefitAmntAdjust(claimCaseBO.getActualPay().doubleValue()); //@invalid  给付金额调整
            }else {
            	currentclaim.setBenefitAmntAdjust(0); //@invalid  给付金额调整
            }
          //@invalid         /*if(claimCaseBO.getCalcPay() != null){
          //@invalid              currentclaim.setPayment(claimCaseBO.getCalcPay().doubleValue());//@invalid 给付金额
              //@invalid          }else {
          //@invalid    	currentclaim.setPayment(0);
   //@invalid          }*/
            if (claimCaseBO.getAdvanceFlag() != null) {
                currentclaim.setPrepayFlag(claimCaseBO.getAdvanceFlag().toString()); //@invalid  预付标志
            }else {
            	currentclaim.setPrepayFlag(""); //@invalid  预付标志
            }
            if (claimCaseBO.getRegisteTime() != null) {
                String date = DateUtilsEx.formatToString(claimCaseBO.getRegisteTime(), PubConstants.DATE_TIME_FORMAT);
                currentclaim.setRgtDate(date); //@invalid  立案日期
            }else {
            	currentclaim.setRgtDate(""); //@invalid  立案日期
            }
            if (claimCaseBO.getCaseFlag() != null) {
                currentclaim.setRgtFlag(claimCaseBO.getCaseFlag().toString()); //@invalid  案件标识
            }else {
            	currentclaim.setRgtFlag("");
            }
            if(claimCaseBO.getCaseNo() != null){
                currentclaim.setClaimNo(claimCaseBO.getCaseNo());//@invalid  赔案号
            }else {
            	currentclaim.setClaimNo("");
            }
            //@invalid  获取出险原因
            ClaimSubCasePO po = new ClaimSubCasePO();
            po.setCaseId(claimCaseBO.getCaseId());
            List<ClaimSubCasePO> listPo = claimSubCaseDao.findAllClaimSubCaseByCaseId(po);
            List<ClaimSubCaseBO> listBo = new ArrayList<ClaimSubCaseBO>();
            listBo = BeanUtils.copyList(ClaimSubCaseBO.class, listPo);
            String accReason = "";
            if (listBo != null && listBo.size() > 0) {
                for (ClaimSubCaseBO subCase : listBo) {
                    if (subCase.getAccReason() != null && !subCase.getAccReason().equals("")) {
                        if (!accReason.equals("")) {
                            int a = 0;
                            for (int i = 0; i < accReason.split(ClaimConstant.COMMA).length; i++) {
                                if (accReason.split(ClaimConstant.COMMA)[i].equals(subCase.getAccReason())) {
                                    a++;
                                }
                            }
                            if (a == 0) {
                                accReason = accReason + subCase.getAccReason() + ClaimConstant.COMMA;
                            }
                        } else {
                            accReason = accReason + subCase.getAccReason() + ClaimConstant.COMMA;
                        }
                    }
                }
            }
            if (!accReason.equals("")) {
                accReason = accReason.substring(0, accReason.trim().lastIndexOf(ClaimConstant.COMMA));
            }
            currentclaim.setAccidentReason(accReason); //@invalid  出险原因
            //@invalid  查询出险结果2信息
            ClaimAccidentResultPO claimAccidentResultPO = new ClaimAccidentResultPO();
            claimAccidentResultPO.setCaseId(claimCaseBO.getCaseId());
            List<ClaimAccidentResultPO> resultListPo = claimAccidentResultDao
                    .findAllClaimAccidentResult(claimAccidentResultPO);
            List<ClaimAccidentResultBO> resultListBo = BeanUtils.copyList(ClaimAccidentResultBO.class, resultListPo);
            String accResult2 = "";
            if (resultListBo != null && resultListBo.size() > 0) {
                for (ClaimAccidentResultBO accResult : resultListBo) {
                    if (accResult.getAccResult2() != null && !accResult.getAccResult2().equals("")) {
                        if (!accResult2.equals("")) {
                            int a = 0;
                            for (int i = 0; i < accResult2.split(ClaimConstant.COMMA).length; i++) {
                                if (accResult2.split(ClaimConstant.COMMA)[i].equals(accResult.getAccResult2())) {
                                    a++;
                                }
                            }
                            if (a == 0) {
                                accResult2 = accResult2 + accResult.getAccResult2() + ClaimConstant.COMMA;
                            }
                        } else {
                            accResult2 = accResult2 + accResult.getAccResult2() + ClaimConstant.COMMA;
                        }
                    }
                }
            }
            if (!"".equals(accResult2)) {
                accResult2 = accResult2.substring(0, accResult2.trim().lastIndexOf(ClaimConstant.COMMA));
          
                if(claimCaseBO.getAccidentDetail()!=null) {
                    accResult2=accResult2+ClaimConstant.COMMA+ claimCaseBO.getAccidentDetail().trim();
                } 
            }else {
                if(claimCaseBO.getAccidentDetail()!=null) {
                    accResult2= claimCaseBO.getAccidentDetail().trim();
                }
            } 
            currentclaim.setAccResult2(accResult2); //@invalid  出险结果2 可能有多个
            //@invalid #TC5879，理赔总集缺陷5879，理赔总集至少有一条代码语句代码检查2
//@invalid             if(claimBillPaidPOList != null && claimBillPaidPOList.size() > 0){/**报文节点中无该节点**/
//@invalid                 //@invalid currentclaim.setThirdPay(true);//@invalid 存在社保第三方给付
//@invalid             }else{
//@invalid                //@invalid currentclaim.setThirdPay(false);//@invalid 不存在社保第三方给付
//@invalid             }
            //@invalid  subClaimList 出险信息
            if (listBo != null && listBo.size() > 0) {
                for (int i = 0; i < listBo.size(); i++) {
                    SubClaim subClaim = new SubClaim();
                    if (listBo.get(i).getClaimType() != null) {
                        subClaim.setClaimType(listBo.get(i).getClaimType()); //@invalid  理赔类型
                    }
                    if (listBo.get(i).getClaimDate() != null) {
                        subClaim.setAccidentDate(DateUtilsEx.formatToString(listBo.get(i).getClaimDate(),
                                PubConstants.DATE_TIME_FORMAT)); //@invalid  出险日期
                    }
                    //@invalid 78511 关于更新核心系统医院信息并支持认可医院标识显示的需求
                    ClaimCasePO claimCasePO = new ClaimCasePO();
                	claimCasePO.setCaseId(claimCaseBO.getCaseId());
                	ClaimCasePO findClaimCaseByCaseId = claimCaseDao.findClaimCaseByCaseId(claimCasePO);
                	if(StringUtils.isNotEmpty(findClaimCaseByCaseId.getCureHospital())){
                		//@invalid 认可医院标识”显示为是，则向“认可医院”传值为true
                		HospitalPO hospitalPO =new HospitalPO();
                		hospitalPO.setHospitalCode(findClaimCaseByCaseId.getCureHospital());
                		//@invalid 1 查询治疗医院信息
                		HospitalPO claimHospitalPO = dataCollectDao.queryHospitalInfoByCode(hospitalPO);
                		if(claimHospitalPO.getData().get("is_designated")!=null){
                			BigDecimal isDesignated = (BigDecimal)claimHospitalPO.getData().get("is_designated");
                			if(isDesignated.compareTo(ClaimConstant.BIGDECIMALYES)==0){
                				subClaim.setAcceptHospital("true");
                			}else{
                				subClaim.setAcceptHospital("false");
                			}
                		}else{
                			subClaim.setAcceptHospital("false");
                		}
                	}else{
                		subClaim.setAcceptHospital("false");
                	}
//                	//@invalid 确诊日期
//                	if(findClaimCaseByCaseId.getDiagnosisTime()!=null) {
//                		subClaim.setRealDate(DateUtilsEx.formatToString(findClaimCaseByCaseId.getDiagnosisTime(),
//                                PubConstants.DATE_FORMAT));
//                	}else{
//                		subClaim.setRealDate("");
//                	}
                    
                    currentclaim.getSubClaimList().add(subClaim);
                }
            }
            //@invalid  --------------保单-----------
            ContractMasterPO contractMasterPO = new ContractMasterPO();
            contractMasterPO.setCaseId(claimCaseBO.getCaseId());
            contractMasterPO.setCurFlag(ClaimConstant.CURFLAG_ONE); //@invalid 当前抄单标记 1 代表当前的 0代表和出险日相同的
            List<ContractMasterPO> contractMasterPOList = contractMasterDao
                    .findContractMasterByList(contractMasterPO);
            List<ContractMasterBO> contractMasterBOList = BeanUtils.copyList(ContractMasterBO.class,
                    contractMasterPOList);
            logger.info("***************************保单数据****"+contractMasterBOList.size());
            //@invalid ---------------险种-------------
            ClaimLiabPO claimLiabPOParam = new ClaimLiabPO();
            claimLiabPOParam.setCaseId(claimCaseBO.getCaseId());
        	List<ClaimLiabPO>  claimLiabPOList  = claimLiabDao.findAllClaimLiab(claimLiabPOParam);
        	List<ClaimLiabBO>  claimLiabBOList  = BeanUtils.copyList(ClaimLiabBO.class,
        			claimLiabPOList);
        	logger.info("***************************险种数据****"+claimLiabBOList.size());
        	//@invalid 没有匹配上责任的保单
            List<Policy>  removePolicyList=new ArrayList<Policy>();
            String uwSpecialHisoty ="0";
            if(claimLiabBOList != null && claimLiabBOList.size() > 0){
            	for(int k = 0; k < claimLiabBOList.size(); k++){
            		Policy policy = new Policy();
            		if (contractMasterBOList != null && contractMasterBOList.size() > 0) {
                        for (int i = 0; i < contractMasterBOList.size(); i++) {
                        	if(contractMasterBOList.get(i).getPolicyCode().equals(claimLiabBOList.get(k).getPolicyCode())){
                        		
                                if(StringUtils.isNotBlank(contractMasterBOList.get(i).getPolicyType())){
                                	if(contractMasterBOList.get(i).getPolicyType().equals("1")){ //@invalid 个人
                                		policy.setSalesChannel("1");
                                	} else if(contractMasterBOList.get(i).getPolicyType().equals("2")){ //@invalid 团体
                                		policy.setSalesChannel("2");
                                	} else {
                                		policy.setSalesChannel("9"); //@invalid 其他
                                	}
                                } else {
                                	policy.setSalesChannel("");
                                }
                                if (claimCaseBO.getSignerId() != null) { 
                                	userPO = new UserPO();
                                	userPO.setUserId(claimCaseBO.getSignerId());
                                	userPO = userDao.findUserByUserId(userPO);
                                	policy.setBranchCode(userPO.getOrganCode()); //@invalid 机构代码(根据新华BA要求机构改成签收的机构,bug为3352)
                                }else {
                                	policy.setBranchCode(""); 
                                }
                                if(claimLiabBOList.get(k).getBusiItemId() != null && !"".equals(claimLiabBOList.get(k).getBusiItemId())){
                                	ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
                                	contractBusiProdPO.setCaseId(claimCaseBO.getCaseId());
                                	contractBusiProdPO.setPolicyCode(claimLiabBOList.get(k).getPolicyCode());
                                  	contractBusiProdPO.setBusiItemId(claimLiabBOList.get(k).getBusiItemId());
                                	contractBusiProdPO.setCurFlag(ClaimConstant.CURFLAG_ONE);
                                	ContractBusiProdPO contractBusiProdPOByIssueDate = contractBusiProdDao.findContractBusiProd(contractBusiProdPO);
                                	//“signyTime-承保时间”字段传值规则调整。续/转保前险种首次生效日存在新规则不存在原有规则
                                	if(contractBusiProdPOByIssueDate != null){
                                		if(contractBusiProdPOByIssueDate.getFirstValidateDate() !=null && !"".equals(contractBusiProdPOByIssueDate.getFirstValidateDate())){
                                				policy.setSignyTime(DateUtilsEx.formatToString(contractBusiProdPOByIssueDate.getFirstValidateDate(),
                                                        PubConstants.DATE_TIME_FORMAT)); //@invalid  承保时间	
                                		}else if(contractBusiProdPOByIssueDate.getInitialValidateDate() != null && !"".equals(contractBusiProdPOByIssueDate.getInitialValidateDate())){
                                			policy.setSignyTime(DateUtilsEx.formatToString(contractBusiProdPOByIssueDate.getInitialValidateDate(),
                                                    PubConstants.DATE_TIME_FORMAT)); //@invalid  生效时间
                                		}
//                                		else if(contractBusiProdPOByIssueDate.getIssueDate() != null && !"".equals(contractBusiProdPOByIssueDate.getIssueDate())){
//                                			policy.setSignyTime(DateUtilsEx.formatToString(contractBusiProdPOByIssueDate.getIssueDate(),
//                                                    PubConstants.DATE_TIME_FORMAT)); //@invalid  承保时间
//                                		}else{
//                                			policy.setSignyTime(DateUtilsEx.formatToString(contractBusiProdPOByIssueDate.getValidateDate(),
//                                                    PubConstants.DATE_TIME_FORMAT)); //@invalid  承保时间
//                                		}
                                		
                                	}
                                }
                                //@invalid 是否是涉案保单
                                ClaimPolicyPO claimPolicyPO = new ClaimPolicyPO();
                                claimPolicyPO.setCaseId(claimCaseBO.getCaseId());
                                List<ClaimPolicyPO> claimPolicyList = claimPolicyDao.findAllClaimPolicy(claimPolicyPO);
                                String isClaimFlag = "0";
                                for (int j = 0; j < claimPolicyList.size(); j++) {
            						if(claimPolicyList.get(j).getPolicyCode().equals(contractMasterBOList.get(i).getPolicyCode())){
            							isClaimFlag = "1";
            							break;
            						}
            					}
                                policy.setIsClaim(isClaimFlag); //@invalid 1（涉案），0（非涉案）
                                /*if(isClaimFlag.equals("1")){
                                	//当保单号为涉案保单时查询对应保单号是否存在和报特约记录若存在将特约记录标识置为是
                                    com.nci.tunan.uw.interfaces.vo.contributing.InputDataVo inputData  =new com.nci.tunan.uw.interfaces.vo.contributing.InputDataVo();
                                    inputData.setPolicyCode(contractMasterBOList.get(i).getPolicyCode());
                                    com.nci.tunan.uw.interfaces.vo.contributing.OutputDataVo outputDataVo = clmServiceUtil.queryUWSpecialHisoty(inputData);
                                    if(outputDataVo.getFlag()!=null && outputDataVo.getFlag().equals("是")){
                                    	uwSpecialHisoty="1";
                                    }
                                }*/
                                //@invalid  查询险种代码
                                ClaimLiabPO claimLiabP = new ClaimLiabPO();
//@invalid                               ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
                              claimLiabP.setCaseId(claimCaseBO.getCaseId());
                              claimLiabP.setPolicyId(contractMasterBOList.get(i).getPolicyId());
                              List<ClaimLiabPO> poList = claimLiabDao.findClaimLiaByCaseIdAndByPolicyCode(claimLiabP);
//@invalid                               List<ContractBusiProdPO> poList = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPO);
                              logger.info("***************************保单数据****poList"+poList.size());
                              List<ClaimLiabBO> boList = BeanUtils.copyList(ClaimLiabBO.class, poList);
                              logger.info("***************************保单数据****boList"+boList.size());
                               //@invalid com.nci.tunan.clm.simplecase.service.bd 
                              if (boList != null && boList.size() > 0) {
                                    for (int j = 0; j < boList.size(); j++) {
                                        if(boList.get(j).getBusiProdCode().equals(claimLiabBOList.get(k).getBusiProdCode())){
                                        	BusinessProduct businessProduct = new BusinessProduct();
                                            LiabilityGroup liabilityGroup = new LiabilityGroup();
                                            List<LiabilityGroup> liabilityGroups = new ArrayList<LiabilityGroup>();
                                            List<Model> modelLists =  new ArrayList<Model>();
                                            Model model = new Model();
                                            if (boList.get(j).getBusiProdCode() != null) {
                                                businessProduct.setBpCode(boList.get(j).getBusiProdCode());
                                            }
//                                            Boolean firstClaim = false; // @invalid 是否是881首次理赔
//                                            Boolean outRealDate = false; //@invalid 是否有超出确诊日期前30天费用
//                                            //判断是否是881险种
//                                            if(boList.get(j).getBusiProdCode().equals(ClaimConstant.BUSI_PRO_CODE_ENGITONE)) {
//                                				ClaimCasePO claimCasePO = new ClaimCasePO();
//                                				claimCasePO.setCaseId(claimCaseBO.getCaseId());
//                                				//@invalid 查询赔案id查询出险人信息
//                                				claimCasePO = claimCaseDao.findClaimCase(claimCasePO);
//                                				//@invalid 查询881险种是否存在既往赔案
//                                				List<ClaimCasePO> claimCasePOList =  claimCaseDao.findAllCaseId(claimCasePO);
//                                				if(claimCasePOList.size()==0) {//@invalid  不存在既往赔案，是首次理赔
//                                					firstClaim=true;
//                                				}
//
//                                				//881产品下理算责任为 7107-癌症住院医疗费用保险金责任 时，查询是否有超出确诊日期前30天的费用
//	                                   			ClaimLiabPO claimLiabPO = new ClaimLiabPO();
//	                                 		    claimLiabPO.setCaseId(claimCaseBO.getCaseId());
//	                                 		    claimLiabPO.setBusiProdCode(ClaimConstant.BUSI_PRO_CODE_ENGITONE);
//	                                 		    List<ClaimLiabPO> claimLiabPoList = claimLiabDao.findAllClaimLiab(claimLiabPO);//@invalid 查询险种责任信息
//	                                 		    for(ClaimLiabPO claimLiabPo : claimLiabPoList) {
//	                                 		    	//invalid 进行险种责任校验
//	                                     		    if( claimLiabPo.getLiabId()!=null && ClaimConstant.LIAB_ID_ENGHT_ONE.compareTo(claimLiabPo.getLiabId())== ClaimConstant.ZERO) {
//	                                     		    	ClaimBillPO claimBillPo = new ClaimBillPO();
//	                                     		    	claimBillPo.setCaseId(claimLiabPo.getCaseId()); 
//	                                     		    	List<ClaimBillPO> claimBillPoList = claimBillDao.findAllClaimBill(claimBillPo);//@invalid 查询门诊住院开始时间
//	                                     		    	for(ClaimBillPO claimBillPO :claimBillPoList) {
//	                                     		    	    if(DateUtilsEx.addDay(claimBillPO.getTreatStart(),ClaimConstant.TWOTY_NINE).getTime()<(claimCasePO.getDiagnosisTime()).getTime()) {
//	                                     		    			outRealDate=true;//@invalid 有超出确诊日期前30天的费用
//	                                         		    	 } 
//	                                     		    	 } 
//	                                     		     }
//	                                 		     }
//                                            }
//                                            businessProduct.setFirstClaim(firstClaim);
//                                            businessProduct.setOutRealDate(outRealDate);
//                                            //@invalid 首次投保生效日期
//                                            ContractMasterPO contractMasterPo = new ContractMasterPO();
//                                            contractMasterPo.setPolicyCode(boList.get(j).getPolicyCode());
//                                            contractMasterPo = contractMasterDao.findFirstInsureDate(contractMasterPo);
//                                            businessProduct.setBpFirstValidateDate(DateUtilsEx.formatToString(contractMasterPo.getInitialValidateDate(),
//                                                    PubConstants.DATE_FORMAT));
                                            //@invalid businessProduct.setBusiItemId(boList.get(j).getBusiItemId());
                                            //@invalid 36577暂时写防止上生产掉Ilog报错
                                            liabilityGroup.setLgCode("notNull");
                                            model.setModelName("notNull");
                                            model.setProbability(Double.valueOf(0));
                                            modelLists.add(model);
                                            liabilityGroup.setModelList(modelLists);
                                            liabilityGroups.add(liabilityGroup);
                                            businessProduct.setLiabilityGroupList(liabilityGroups);
                                            //@invalid 判断本险种匹配出了津贴责任
                                            ClaimLiabPO newClaimLiabPO = new ClaimLiabPO();
                                            newClaimLiabPO.setCaseId(claimCaseBO.getCaseId());
                                            newClaimLiabPO.setBusiItemId(boList.get(j).getBusiItemId());
                                            newClaimLiabPO.setPolicyId(boList.get(j).getPolicyId());
                                            newClaimLiabPO.setIsSubsidy(ClaimConstant.BIGDECIMALYES);
                                            List<ClaimLiabPO> newClaimLiabPOs = claimLiabDao.findAllClaimLiab(newClaimLiabPO);
                                            if(newClaimLiabPOs.size()>0) {
                                            	//@invalid 既往津贴赔付个数
                                            	String hisPerkClaimNo = "";
                                                ClaimCasePO newCasePO = new ClaimCasePO();
                                                newCasePO.setInsuredId(claimCaseBO.getInsuredId());
                                                newCasePO.getData().put("policy_id", boList.get(j).getPolicyId());
                                                newCasePO.getData().put("busi_item_id", boList.get(j).getBusiItemId());
                                                int no = claimCaseDao.findHisPerkClaimNo(newCasePO);
                                                hisPerkClaimNo = hisPerkClaimNo+no;
                                                businessProduct.setHisPerkClaimNo(hisPerkClaimNo);                                            //@invalid end
                                            } else {
                                            	businessProduct.setHisPerkClaimNo("");
                                            }
                                            policy.getBusinessProductList().add(businessProduct);
                                             
                                            if(boList.get(j).getBusiProdCode().equals(ClaimConstant.CLM_BUSI_PRODCODE_TWELVE) || boList.get(j).getBusiProdCode().equals(ClaimConstant.CLM_BUSI_PRODCODE_THIRTEEN)){
                                            	ClaimLiabPO claimLiabPO = new ClaimLiabPO();
//@invalid                                             	claimLiabPO.setCaseId(claimCaseBO.getCaseId());
                                            	claimLiabPO.setPolicyCode(boList.get(j).getPolicyCode());
                                            	claimLiabPO.setBusiProdCode(boList.get(j).getBusiProdCode());
                                            	List<ClaimLiabPO> liabList = claimLiabDao.findAllClaimLiab(claimLiabPO);
                                            	BigDecimal money = new BigDecimal(ClaimConstant.ZERO);
                                            	for(ClaimLiabPO claimLiab : liabList){
                                            		if(claimLiab.getActualPay() != null) {
                                            			money = money.add(claimLiab.getActualPay());
                                            		}
                                            	}
                                            	policy.setSumAmount(money.toString());
                                            	policy.setPolicyApplyCode(boList.get(j).getPolicyCode());
                                            }else {
                                            	policy.setSumAmount("0");
                                            	policy.setPolicyApplyCode(boList.get(j).getPolicyCode());
                                            }
                                        }
                                    }
                                }else{ 
                                	removePolicyList.add(policy);
                                }
                              //@invalid 查询是否全为指定生存受益人，全是传true，不全传false,#18966需求变更取消
                              /*ClaimPayPO claimPayPO1 = new ClaimPayPO();
                              claimPayPO1.setCaseId(claimCaseBO.getCaseId());
                              List<BeneAndPayeePO> beneAndPayeePOList1 = auditConclusionDao.queryBeneAndPayeeMsg(claimPayPO1);
                              if (beneAndPayeePOList1 != null && beneAndPayeePOList1.size() > 0) {
                                  int beneSum = 0;
                                  for(int  a= 0; a < beneAndPayeePOList1.size(); a++){
                                      ClaimBenePO claimBenePO = beneAndPayeePOList1.get(a).getClaimbene();
                                      //@invalid 查询是否有指定受益人
                                      ContractBenePO contractBenePO = new ContractBenePO();
                                      contractBenePO.setCaseId(claimCaseBO.getCaseId());
                                      contractBenePO.setPolicyId(beneAndPayeePOList1.get(a).getClaimpay().getPolicyId());
                                      contractBenePO.setBusiItemId(beneAndPayeePOList1.get(a).getClaimpay().getBusiItemId());
                                      contractBenePO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
                                      contractBenePO.setCustomerName(claimBenePO.getBeneName());
                                      contractBenePO.setCustomerGender(claimBenePO.getBeneSex());
                                      contractBenePO.setCustomerBirthday(claimBenePO.getBeneBirth());
                                      contractBenePO.setCustomerCertType(claimBenePO.getBeneCertiNo());
                                      contractBenePO.setCustomerCertType(claimBenePO.getBeneCertiType());
                                      contractBenePO = contractBeneDao.findContractBene(contractBenePO);
                                    //@invalid 判断理赔受益人是否为指定生存金领取受益人
                                      if(contractBenePO.getLogId() != null){
                                          if(contractBenePO.getBeneType().toString().equals("0")){
                                              beneSum ++;
                                          }
                                      }
                                  }
                                  if(beneSum == beneAndPayeePOList1.size() - 1){
                                      policy.setIsBeneficiary(true);
                                  }
                              }else{
                                  policy.setIsBeneficiary(false);
                              }*/
                                //@invalid ----------业务员  该保单涉及的业务员----------
                                ContractAgentPO contractAgentPO = new ContractAgentPO();
                                contractAgentPO.setCaseId(claimCaseBO.getCaseId());
                                contractAgentPO.setCurFlag(BigDecimal.valueOf(ClaimConstant.ONE)); //@invalid 当前抄单标记 1 代表当前的 0代表和出险日相同的
                                contractAgentPO.setPolicyCode(contractMasterBOList.get(i).getPolicyCode());
                                List<ContractAgentPO> agentList = contractAgentDao.findAllContractAgent(contractAgentPO);
                                if(agentList != null && agentList.size()>0){
                                	for (int j = 0; j < agentList.size(); j++) {
                                    	Agent agent = new Agent(); 
                                    	if(agentList.get(j).getAgentCode() != null){
                                    		agent.setAgentNo(agentList.get(j).getAgentCode()); //@invalid 编码
                                    	}
                                    	if(agentList.get(j).getAgentName() != null){
                                    		agent.setAgentName(agentList.get(j).getAgentName()); //@invalid 姓名
                                    	}
                                    	//@invalid 得到当前日期对应的一年前的日期
                                    	Calendar c= Calendar.getInstance();
                                    	c.setTime(WorkDateUtil.getWorkDate());
                                    	c.add(Calendar.YEAR, -1);
                                    	Date date= c.getTime();
                                    	//@invalid 业务员涉及的从当前申请时点开始1年内存在既往赔案（不含本案）
                                    	ContractAgentPO contractAgent = new ContractAgentPO();
                                    	contractAgent.setAgentCode(agentList.get(j).getAgentCode()); //@invalid 业务员号
                                    	contractAgent.setCaseId(claimCaseBO.getCaseId()); //@invalid 排除本赔案
                                    	contractAgent.getData().put("startcopydate", date); //@invalid 一年前的日期
                                    	contractAgent.getData().put("endcopydate", WorkDateUtil.getWorkDate()); //@invalid 当前日期
                                    	int num = contractAgentDao.findAgentCaseHistoryNumInYear(contractAgent);
                                    	agent.setHisClaimNo(num); //@invalid 既往赔案个数
                                        agent.setAgentBlackList(false); 
                                        //@invalid 是否是黑名单
                                    	if(agentList.get(j).getAgentCode() != null) {
                                    	//@invalid 校验黑名单   业务员查询 放入01 非业务员放入02
                                            agent.setAgentBlackList(checkAgentBlackList(agentList.get(j).getAgentCode(), ClaimConstant.STRING_SORT_ONE)); //@invalid 是否是黑名单
                                    	   }
                                        policy.getAagentList().add(agent);
                                    }
                                }else{
                                	Agent agent = new Agent();
                                	agent.setAgentNo(""); //@invalid 编码
                                	agent.setAgentName(""); //@invalid 姓名
                                	agent.setHisClaimNo(0);
                                	agent.setAgentBlackList(false); //@invalid 是否是黑名单
                                	policy.getAagentList().add(agent);
                                }
                                //@invalid /满足规则字段
                                //@invalid 查询投保人 
                                Applicant applicant=new Applicant();
                                applicant.setApplicantID("");
                                applicant.setApplicantName("");
                                applicant.setApplicantBlackList(false);
                                policy.getApplicant().add(applicant);
                                 
                                PolicyHolderPO policyHolderPO = new PolicyHolderPO();
                                policyHolderPO.setPolicyCode(contractMasterBOList.get(i).getPolicyCode());
                                policyHolderPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
                                policyHolderPO.setCaseId(claimCaseBO.getCaseId());
                                PolicyHolderPO findPolicyHolder = policyHolderDao.findPolicyHolder(policyHolderPO);
                                if (findPolicyHolder != null && findPolicyHolder.getCustomerId() != null) {
                                    CustomerPO customerPO = new CustomerPO();
                                    customerPO.setCustomerId(findPolicyHolder.getCustomerId());
                                    CustomerPO claimCustomer = customerDao.findCustomerByCustomerId(customerPO);
                                    applicant.setApplicantID(findPolicyHolder.getCustomerId().toString());
                                    applicant.setApplicantName(claimCustomer.getCustomerName());
                                    if (claimCustomer.getCustomerName() != null&&claimCustomer.getCustomerCertiCode()!=null) {
                                    	boolean blackListFlag = false;
                                        boolean  blackListFlag1= checkBlackList(claimCustomer.getCustomerName(),claimCustomer.getCustomerCertiCode(),ClaimConstant.STRING_SORT_TWO);
                                        boolean  blackListFlag3= checkCommonBlackList(claimCustomer);
                                        if(blackListFlag1||blackListFlag3) {
                                        	blackListFlag = true;
                                        }
            							applicant.setApplicantBlackList(blackListFlag );  
            							
            							//@invalid 高风险客户
            							boolean applicantHighRiskCustomer = false;
                                        boolean  applicantHighRiskCustomer1= checkHighRiskLevelCustomer(claimCustomer.getCustomerId());
                                        if(applicantHighRiskCustomer1){
                                            applicantHighRiskCustomer = true; 
                                        } 
                                        currentclaim.setApplicantHighRiskCustomer(applicantHighRiskCustomer);
            							
                                    } 
                                } else{
                                    applicant.setApplicantID("");
                                    applicant.setApplicantName("");
                                    applicant.setApplicantBlackList(false);
                                    policy.getApplicant().add(applicant);
                                }
                                //@invalid /
                                currentclaim.getPolicyList().add(policy);
                        	}
                        }
                    }
            	}
            }
            logger.info("***************************保单数据****removePolicyList"+removePolicyList.size());
            //@invalid 删除没匹配上责任的保单
             currentclaim.getPolicyList().removeAll(removePolicyList) ;
            //@invalid  --------------出险人---------- 
             
            CustomerPO customerPO = new CustomerPO();
            customerPO.setCustomerId(claimCaseBO.getInsuredId());
            CustomerPO claimCustomer = customerDao.findCustomerByCustomerId(customerPO);
            com.nci.tunan.clm.simplecase.service.bd.Customer customer = new com.nci.tunan.clm.simplecase.service.bd.Customer();
            if (claimCustomer.getCustomerName() != null) {
                customer.setCustomerName(claimCustomer.getCustomerName()); //@invalid  姓名
            }
            //传给自核接口是重复账单未否
            if("repeated".equals(status) ){
            	currentclaim.setReBill(ClaimConstant.YES);
            }else {
            	currentclaim.setReBill(ClaimConstant.NO);
			}
            //是否存在特约核保记录
            currentclaim.setUwSpecialHisoty(uwSpecialHisoty);
            if (claimCustomer.getCustomerId() != null) {
                customer.setCustomerNo(claimCustomer.getCustomerId().toString()); //@invalid  客户号
            }
            if (claimCustomer.getCustomerBirthday() != null) {
                customer.setBirthDay(DateUtilsEx.formatToString(claimCustomer.getCustomerBirthday(),
                        PubConstants.DATE_TIME_FORMAT)); //@invalid  出生日期
            }
            //@invalid  当前此出险人下已立案确认未结案的案件数
            ClaimCasePO newClaimCasePO = new ClaimCasePO();
            newClaimCasePO.setInsuredId(claimCaseBO.getInsuredId());
            List<ClaimCasePO> claimCasePOlist = claimCaseDao.findAllClaimCase(newClaimCasePO);
            List<ClaimCaseBO> claimCaseBOlist = BeanUtils.copyList(ClaimCaseBO.class, claimCasePOlist);
            int rgtNumber = 0;
            if (claimCaseBOlist != null && claimCaseBOlist.size() > 0) {
                for (ClaimCaseBO caseBO : claimCaseBOlist) {
                    if (caseBO.getCaseStatus() != null) {
                        if (caseBO.getCaseStatus().equals("40") || caseBO.getCaseStatus().equals("41") || caseBO.getCaseStatus().equals("50")|| caseBO.getCaseStatus().equals("51")
                        		|| caseBO.getCaseStatus().equals("52") || caseBO.getCaseStatus().equals("53") || caseBO.getCaseStatus().equals("54") || caseBO.getCaseStatus().equals("60")
                        		|| caseBO.getCaseStatus().equals("61") || caseBO.getCaseStatus().equals("70") || caseBO.getCaseStatus().equals("71")) {
                            rgtNumber++;
                        }
                    }
                }
            }
            customer.setRgtNumberMissedCases(rgtNumber);
            //@invalid  从当前立案日期起向前计算一年内此出险人下已结简易案件数
            Date yearone = claimCaseBO.getRegisteTime();
            int hasSimpleCases = 0;
            if (yearone != null && !yearone.equals("")) {
                Date yeartwo = DateUtilsEx.addYear(claimCaseBO.getRegisteTime(), -1);
                if (claimCaseBOlist != null && claimCaseBOlist.size() > 0) {
                    for (ClaimCaseBO caseBO : claimCaseBOlist) {
                    	//@invalid 老核心迁移过来赔案为简易案件的案件标识为4，新核心简易案件的案件标识为空
                        if (caseBO.getCaseStatus() != null && caseBO.getCaseStatus().equals("80") && (caseBO.getCaseFlag()==null || caseBO.getCaseFlag().compareTo(ClaimConstant.CASE_FLAG_EAZY) == ClaimConstant.ZERO)) {
                            if (caseBO.getRegisteTime() != null) {
                                if (DateUtilsEx.getYearAmount(caseBO.getRegisteTime(), yearone) <= 1) {
                                    hasSimpleCases++;
                                }
                            }
                        }
                    }
                }
            }
            customer.setHasSimpleCases(hasSimpleCases);
            //@invalid  黑名单
             if (claimCustomer.getCustomerName() != null&&claimCustomer.getCustomerCertiCode()!=null) {
                boolean  blackListFlag= false;
                boolean  blackListFlag1= checkBlackList(claimCustomer.getCustomerName(),claimCustomer.getCustomerCertiCode(),ClaimConstant.STRING_SORT_TWO);
                boolean  blackListFlag2= checkHighRiskLevelCustomer(claimCustomer.getCustomerId());
                boolean  blackListFlag3= checkCommonBlackList(claimCustomer);
                if(blackListFlag1||blackListFlag2||blackListFlag3) {
                	blackListFlag = true;
                }
                customer.setCustomerBlackList(blackListFlag);  
            } 
            //@invalid 查询已结案的给付责任类型为重大疾病或特种疾病的案件数
            ClaimCasePO claimCase = new ClaimCasePO();
            claimCase.setInsuredId(claimCaseBO.getInsuredId());
            claimCase.setCaseStatus(ClaimConstant.CLOSECASESTATUSE);
            int accidentSickAmount = claimCaseDao.findClaimCaseCountByInsuredId(claimCase);
            customer.setAccidentSickAmount(accidentSickAmount);
            //@invalid 查询案件出险人的历史案件中案件状态为结案且涉及理算的责任代码，该责任理算金额大于0
            List<HaveEndLiabilityCode> haveEndCaseLiabilityList = new ArrayList<HaveEndLiabilityCode>();
            List<ClaimCasePO> claimCasePOs = claimCaseDao.findEndLiabCodeByInsuredId(claimCase);
            for (ClaimCasePO claimCasePO : claimCasePOs) {
				HaveEndLiabilityCode haveEndLiabilityCode = new HaveEndLiabilityCode();
				haveEndLiabilityCode.setHaveEndLiabilityCode(claimCasePO.getData().get("liab_code").toString());
				haveEndCaseLiabilityList.add(haveEndLiabilityCode);
			}
            if(haveEndCaseLiabilityList.size() == 0){
				haveEndCaseLiabilityList.add(new HaveEndLiabilityCode());
            }
            customer.setHaveEndCaseLiabilityList(haveEndCaseLiabilityList);
            currentclaim.setCustomer(customer);
			//@invalid  --------------领款人---------
            ClaimPayPO claimPayPO = new ClaimPayPO();
            claimPayPO.setCaseId(claimCaseBO.getCaseId());
            List<BeneAndPayeePO> beneAndPayeePOList = auditConclusionDao.queryBeneAndPayeeMsg(claimPayPO);
            List<BeneAndPayeeBO> beneAndPayeeBOList = BeanUtils.copyList(BeneAndPayeeBO.class, beneAndPayeePOList);
    		//保单集合
    		ClaimPolicyHolderBO claimPolicyHolderBOCondi = new ClaimPolicyHolderBO();
    		claimPolicyHolderBOCondi.setCaseId(claimCaseBO.getCaseId());
    		List<ClaimPolicyHolderBO> claimPolicyHolderBOs = this.findPolicyListByCaseNo(claimPolicyHolderBOCondi);
    		//被保人集合
    		InsuredListPO insuredListDataPO = new InsuredListPO();
    		insuredListDataPO.setCaseId(claimCaseBO.getCaseId());
    		List<InsuredListPO> insuredListData = claimCaseDao.findInsuredCustomerDataByCaseId(insuredListDataPO);
    		//受益人集合
    		ClaimBenePO claimBeneDataPO = new ClaimBenePO();
    		claimBeneDataPO.setCaseId(claimCaseBO.getCaseId());
    		List<ClaimBenePO> claimBeneData = claimCaseDao.findBeneDataByCaseId(claimBeneDataPO);
    		//领款人集合
    		ClaimPayeePO claimPayeeDataPO = new ClaimPayeePO();
    		claimPayeeDataPO.setCaseId(claimCaseBO.getCaseId());
    		List<ClaimPayeePO> claimPayeeData = claimCaseDao.findPayeeDataByCaseId(claimPayeeDataPO);
    		//法人配置表查询
    		List<LegalPersonInfoBO> legalPersonInfoBOList = null;
          //@invalid --------------受益人---------
            if (beneAndPayeeBOList != null && beneAndPayeeBOList.size() > 0) {
            	for(int i = 0; i < beneAndPayeeBOList.size(); i++){
                    ClaimBeneBO claimBeneBO = beneAndPayeeBOList.get(i).getClaimbene();
                    blacklistReqVo2 = new BlacklistReqVo();
                    blacklistReqVo2.setCoreFlag(ClaimConstant.CORE_FLAG);
                    blacklistReqVo2.setState(claimBeneBO.getBeneProvince());//州、省
                    blacklistReqVo2.setCustomerName(claimBeneBO.getBeneName());//客户名称
                    blacklistReqVo2.setRemarks("");
                    blacklistReqVo2.setIdType(claimBeneBO.getBeneCertiType());
                    blacklistReqVo2.setNationality(claimBeneBO.getBeneNation());//国籍
                    blacklistReqVo2.setCustomerType(ClaimConstant.CUSTOMER_FLAG);//客户类型
                    if(claimBeneBO.getBeneId()!=null) {
                    	blacklistReqVo2.setCustomerNo(claimBeneBO.getBeneId().toString());
                    }
                    blacklistReqVo2.setOtherNoType(ClaimConstant.CLAIM_FLAG);//业务号码类型
                    blacklistReqVo2.setOtherNo(claimCaseBO.getCaseNo());
                    blacklistReqVo2.setBirthday(claimBeneBO.getBeneBirth());
                    blacklistReqVo2.setDistrict(claimBeneBO.getBeneDistrict());
                    blacklistReqVo2.setOccupation(claimBeneBO.getBeneJobCode());
                    blacklistReqVo2.setOtherNoType(ClaimConstant.CLAIM_FLAG);
                    blacklistReqVo2.setAddress(claimBeneBO.getBeneCity());
//                    blacklistReqVo2.setPolicyNo(claimBeneBO.getPolicyCode());
                    blacklistReqVo2.setIdNo(claimBeneBO.getBeneCertiNo());
                    if(claimBeneBO.getBeneSex()!=null) {
                    blacklistReqVo2.setGender(claimBeneBO.getBeneSex()==null?null:claimBeneBO.getBeneSex().toString());
                    }
        			String applyCode = "";//投保单号
        			String policyCode = "";//保单号
        			String customerId = "";//客户号
        			String customerName = "";//客户姓名
        			String customerRole = "";
        			if(CollectionUtilEx.isNotEmpty(claimPolicyHolderBOs)){
        				for(ClaimPolicyHolderBO claimPolicyHolderBO:claimPolicyHolderBOs){
        					applyCode = applyCode+claimPolicyHolderBO.getApplyCode()+",";
        					policyCode = policyCode+claimPolicyHolderBO.getPolicyCode()+",";
        					customerId = customerId+claimPolicyHolderBO.getCustomerId()+",";
        					customerName = customerName+claimPolicyHolderBO.getCustomerName()+",";
        					customerRole = this.getBeneRoleNew(claimBeneBO, claimPolicyHolderBO,customerRole,insuredListData,claimBeneData,claimPayeeData);    					
        				}
        			}
        			if(applyCode.endsWith(",")){
        				applyCode = applyCode.substring(0,applyCode.length()-1);
        			}
        			if(policyCode.endsWith(",")){
        				policyCode = policyCode.substring(0,policyCode.length()-1);
        			}
        			if(customerId.endsWith(",")){
        				customerId = customerId.substring(0,customerId.length()-1);
        			}
        			if(customerName.endsWith(",")){
        				customerName = customerName.substring(0,customerName.length()-1);
        			}
        			if(customerRole.endsWith(",")){
        				customerRole = customerRole.substring(0,customerRole.length()-1);
        			}
        			blacklistReqVo2.setPrtNo(applyCode);//投保单号
        			blacklistReqVo2.setPolicyNo(policyCode);//保单号
        			blacklistReqVo2.setHolderName(customerName);//客户姓名
        			blacklistReqVo2.setHolderCustomerId(customerId);//客户
        			blacklistReqVo2.setCustomerRoleName(customerRole);//客户角色
                    blacklistReqVo2.setBussType(ClaimConstant.STRING_THREE);
                    blacklistReqVo2.setCity(claimBeneBO.getBeneAddress());
                    blacklistReqVo2.setForeignPoliticians(ClaimConstant.FOREIGN_FLAG);
                    blacklistReqVo2.setNotSendFlag(true);
                    blacklistReqVo2.setPolicyOrg(claimCaseBO.getOrganCode());
                    blacklistReqVoList.add(blacklistReqVo2);
                    logger.debug("反洗钱检测名单结果 受益人："+blacklistReqVoList);
            	}
            }
            if (beneAndPayeeBOList != null && beneAndPayeeBOList.size() > 0) {
            	for(int i = 0; i < beneAndPayeeBOList.size(); i++){
            		ClaimPayeeBO claimPayeeBO = beneAndPayeeBOList.get(i).getClaimpayee();
            		blacklistReqVo1 = new BlacklistReqVo();
                    blacklistReqVo1.setCoreFlag(ClaimConstant.CORE_FLAG);
                    blacklistReqVo1.setState(claimPayeeBO.getPayeeState());//州、省
                    blacklistReqVo1.setCustomerName(claimPayeeBO.getPayeeName());//客户名称
                    blacklistReqVo1.setRemarks("");
                    blacklistReqVo1.setIdType(claimPayeeBO.getPayeeCertiType());
                    blacklistReqVo1.setNationality(claimPayeeBO.getPayeeNation());//国籍
                    blacklistReqVo1.setCustomerType(ClaimConstant.PAYEE_FLAG);//客户类型
                    if(claimPayeeBO.getPayeeId()!=null) {
                    blacklistReqVo1.setCustomerNo(claimPayeeBO.getPayeeId().toString());
                    }
                    blacklistReqVo1.setOtherNo(claimCaseBO.getCaseNo());
                    blacklistReqVo1.setBirthday(claimPayeeBO.getPayeeBirth());
                    blacklistReqVo1.setDistrict(claimPayeeBO.getPayeeDistrict());
                    blacklistReqVo1.setOccupation(claimPayeeBO.getPayeeJobCode());
                    blacklistReqVo1.setOtherNoType(ClaimConstant.CLAIM_FLAG);
                    blacklistReqVo1.setAddress(claimPayeeBO.getPayeeCity());
//                    blacklistReqVo1.setPolicyNo(claimPayeeBO.getPolicyCode());
                    blacklistReqVo1.setIdNo(claimPayeeBO.getPayeeCertiNo());
                    if(claimPayeeBO.getPayeeSex()!=null) {
                    blacklistReqVo1.setGender(claimPayeeBO.getPayeeSex().toString());
                    }
        			String applyCode = "";//投保单号
        			String policyCode = "";//保单号
        			String customerId = "";//客户号
        			String customerName = "";//客户姓名
        			String customerRole = "";
        			if(CollectionUtilEx.isNotEmpty(claimPolicyHolderBOs)){
        				for(ClaimPolicyHolderBO claimPolicyHolderBO:claimPolicyHolderBOs){
        					applyCode = applyCode+claimPolicyHolderBO.getApplyCode()+",";
        					policyCode = policyCode+claimPolicyHolderBO.getPolicyCode()+",";
        					customerId = customerId+claimPolicyHolderBO.getCustomerId()+",";
        					customerName = customerName+claimPolicyHolderBO.getCustomerName()+",";
        					customerRole = this.getPayeeRoleNew(claimPayeeBO, claimPolicyHolderBO,customerRole,insuredListData,claimBeneData,claimPayeeData);
        				}
        			}
        			if(applyCode.endsWith(",")){
        				applyCode = applyCode.substring(0,applyCode.length()-1);
        			}
        			if(policyCode.endsWith(",")){
        				policyCode = policyCode.substring(0,policyCode.length()-1);
        			}
        			if(customerId.endsWith(",")){
        				customerId = customerId.substring(0,customerId.length()-1);
        			}
        			if(customerName.endsWith(",")){
        				customerName = customerName.substring(0,customerName.length()-1);
        			}
        			if(customerRole.endsWith(",")){
        				customerRole = customerRole.substring(0,customerRole.length()-1);
        			}
        			blacklistReqVo1.setPrtNo(applyCode);//投保单号
        			blacklistReqVo1.setPolicyNo(policyCode);//保单号
        			blacklistReqVo1.setHolderName(customerName);//客户姓名
        			blacklistReqVo1.setHolderCustomerId(customerId);//客户
        			blacklistReqVo1.setCustomerRoleName(customerRole);//客户角色
                    blacklistReqVo1.setBussType(ClaimConstant.STRING_THREE);
                    blacklistReqVo1.setCity(claimPayeeBO.getPayeeCity());
                    blacklistReqVo1.setForeignPoliticians(ClaimConstant.FOREIGN_FLAG);
                    blacklistReqVo1.setPolicyOrg(claimCaseBO.getOrganCode());
                    blacklistReqVo1.setNotSendFlag(true);
                    blacklistReqVoList.add(blacklistReqVo1);
                    logger.debug("********************************开始反洗钱检测-领款人："+blacklistReqVoList);
                    //领款人的受益所有人、法定代表人、实际控制人
                    if(claimPayeeBO.getLegalPersonId()!=null) {
                    	//如果前边没有查询法人信息，则在这里查询
                    	if(legalPersonInfoBOList==null){
                    		LegalPersonInfoPO legalPersonInfoPO = new LegalPersonInfoPO();
                            legalPersonInfoBOList = BeanUtils.copyList(LegalPersonInfoBO.class,legalPersonInfoDao.findAllLegalPersonInfo(legalPersonInfoPO));
                            //法人受益所有人信息配置表
                            for(LegalPersonInfoBO legalPersonInfo: legalPersonInfoBOList){
                            	LegalPersonBeneInfoPO legalPersonBeneInfoPO = new LegalPersonBeneInfoPO();
                            	legalPersonBeneInfoPO.setLegalPersonId(legalPersonInfo.getListId());
                            	List<LegalPersonBeneInfoBO> legalPersonBeneInfoBOs = BeanUtils.copyList(LegalPersonBeneInfoBO.class,legalPersonBeneInfoDao.findLegalPersonBeneInfoByLegalPersonId(legalPersonBeneInfoPO));
                            	legalPersonInfo.setLegalPersonBeneInfoBOList(legalPersonBeneInfoBOs);
                            }
                    	}
                    	
                    	LegalPersonInfoBO legalPersonInfoBO = null;
                    	for(LegalPersonInfoBO legalPersonInfo : legalPersonInfoBOList){
                    		if(claimPayeeBO.getLegalPersonId().compareTo(legalPersonInfo.getListId())==0){
                    			legalPersonInfoBO = legalPersonInfo;
                    		}
                    	}
                    	if(legalPersonInfoBO!=null){
                    		//法定代表人
                    		logger.debug("********开始反洗钱检测-法定代表人："+legalPersonInfoBO+claimPayeeBO);
                    		blacklistReqVo3 = new BlacklistReqVo();
                    		blacklistReqVo3.setCustomerName(legalPersonInfoBO.getLegalName());
                    		blacklistReqVo3.setIdType(legalPersonInfoBO.getLegalCertiType());
                    		blacklistReqVo3.setCustomerType(ClaimConstant.LEGAL_FLAG);//客户类型:法定代表人
                    		blacklistReqVo3.setIdNo(legalPersonInfoBO.getLegalCertiCode());
                    		String applyCode3 = "";//投保单号
                    		String policyCode3 = "";//保单号
                    		String customerId3 = "";//客户号
                    		String customerName3 = "";//客户姓名
                    		String customerRole3 = "";
                    		if(CollectionUtilEx.isNotEmpty(claimPolicyHolderBOs)){
                    			for(ClaimPolicyHolderBO claimPolicyHolderBO:claimPolicyHolderBOs){
                    				applyCode3 = applyCode3+claimPolicyHolderBO.getApplyCode()+",";
                    				policyCode3 = policyCode3+claimPolicyHolderBO.getPolicyCode()+",";
                    				customerId3 = customerId3+claimPolicyHolderBO.getCustomerId()+",";
                    				customerName3 = customerName3+claimPolicyHolderBO.getCustomerId()+",";
                    				customerRole3 = this.getLegalRoleNew(legalPersonInfoBO, claimPolicyHolderBO,customerRole3,insuredListData,claimBeneData,claimPayeeData);
                    			}
                    		}
                    		if(applyCode3.endsWith(",")){
                    			applyCode3 = applyCode3.substring(0,applyCode3.length()-1);
                    		}
                    		if(policyCode3.endsWith(",")){
                    			policyCode3 = policyCode3.substring(0,policyCode3.length()-1);
                    		}
                    		if(customerId3.endsWith(",")){
                    			customerId3 = customerId3.substring(0,customerId3.length()-1);
                    		}
                    		if(customerName3.endsWith(",")){
                    			customerName3 = customerName3.substring(0,customerName3.length()-1);
                    		}
                    		if(customerRole3.endsWith(",")){
                    			customerRole3 = customerRole3.substring(0,customerRole3.length()-1);
                    		}
                    		blacklistReqVo3.setPrtNo(applyCode3);//投保单号
                    		blacklistReqVo3.setPolicyNo(policyCode3);//保单号
                    		blacklistReqVo3.setHolderName(customerName3);//客户姓名
                    		blacklistReqVo3.setHolderCustomerId(customerId3);//客户
                    		blacklistReqVo3.setCustomerRoleName(customerRole3);//客户角色
                    		blacklistReqVo3.setBussType(ClaimConstant.STRING_THREE);//业务类型
                    		blacklistReqVo3.setPolicyOrg(claimCaseBO.getOrganCode());
                    		blacklistReqVo3.setNotSendFlag(true);
                    		blacklistReqVoList.add(blacklistReqVo3);
                    		logger.debug("********************************开始反洗钱检测-法定代表人："+blacklistReqVoList);
                    		//实际控制人
                    		logger.debug("********开始反洗钱检测-实际控制人："+legalPersonInfoBO);
                    		blacklistReqVo4 =  new BlacklistReqVo();
                    		blacklistReqVo4.setCustomerName(legalPersonInfoBO.getControllingName());
                    		blacklistReqVo4.setIdType(legalPersonInfoBO.getControllingCertiType());
                    		blacklistReqVo4.setCustomerType(ClaimConstant.CONTROLLING_FLAG);//客户类型:实际控制人
                    		blacklistReqVo4.setIdNo(legalPersonInfoBO.getControllingCertiCode());
                    		String applyCode4 = "";//投保单号
                    		String policyCode4 = "";//保单号
                    		String customerId4 = "";//客户号
                    		String customerName4 = "";//客户姓名
                    		String customerRole4 = "";
                    		if(CollectionUtilEx.isNotEmpty(claimPolicyHolderBOs)){
                    			for(ClaimPolicyHolderBO claimPolicyHolderBO:claimPolicyHolderBOs){
                    				applyCode4 = applyCode4+claimPolicyHolderBO.getApplyCode()+",";
                    				policyCode4 = policyCode4+claimPolicyHolderBO.getPolicyCode()+",";
                    				customerId4 = customerId4+claimPolicyHolderBO.getCustomerId()+",";
                    				customerName4 = customerName4+claimPolicyHolderBO.getCustomerId()+",";
                    				customerRole4 = this.getControllingRoleNew(legalPersonInfoBO, claimPolicyHolderBO,customerRole4,insuredListData,claimBeneData,claimPayeeData);
                    			}
                    		}
                    		if(applyCode4.endsWith(",")){
                    			applyCode4 = applyCode4.substring(0,applyCode4.length()-1);
                    		}
                    		if(policyCode4.endsWith(",")){
                    			policyCode4 = policyCode4.substring(0,policyCode4.length()-1);
                    		}
                    		if(customerId4.endsWith(",")){
                    			customerId4 = customerId4.substring(0,customerId4.length()-1);
                    		}
                    		if(customerName4.endsWith(",")){
                    			customerName4 = customerName4.substring(0,customerName4.length()-1);
                    		}
                    		if(customerRole4.endsWith(",")){
                    			customerRole4 = customerRole4.substring(0,customerRole4.length()-1);
                    		}
                    		blacklistReqVo4.setPrtNo(applyCode4);//投保单号
                    		blacklistReqVo4.setPolicyNo(policyCode4);//保单号
                    		blacklistReqVo4.setHolderName(customerName4);//客户姓名
                    		blacklistReqVo4.setHolderCustomerId(customerId4);//客户
                    		blacklistReqVo4.setCustomerRoleName(customerRole);//客户角色
                    		blacklistReqVo4.setBussType(ClaimConstant.STRING_THREE);//业务类型
                    		blacklistReqVo4.setPolicyOrg(claimCaseBO.getOrganCode());
                    		blacklistReqVo4.setNotSendFlag(true);
                    		blacklistReqVoList.add(blacklistReqVo4);
                    		logger.debug("********************************开始反洗钱检测-实际控制人："+blacklistReqVoList);
                    		List<LegalPersonBeneInfoBO> legalPersonBeneInfoBOs = legalPersonInfoBO.getLegalPersonBeneInfoBOList();
                    		//受益所有人
                    		logger.debug("开始反洗钱检测-受益所有人："+legalPersonBeneInfoBOs);
                    		if(legalPersonBeneInfoBOs.size()>0) {
                    			for(LegalPersonBeneInfoBO legalPersonBeneInfoBO : legalPersonBeneInfoBOs) {
                    				BlacklistReqVo blacklistReqVo5 = new BlacklistReqVo();
                    				blacklistReqVo5.setCustomerName(legalPersonBeneInfoBO.getBeneficialName());
                    				blacklistReqVo5.setIdType(legalPersonBeneInfoBO.getBeneficialCertiType());
                    				blacklistReqVo5.setCustomerType(ClaimConstant.BENEFICIAL_FLAG);//客户类型:受益所有人
                    				blacklistReqVo5.setIdNo(legalPersonBeneInfoBO.getBeneficialCertiCode());
                    				String applyCode5 = "";//投保单号
                    				String policyCode5 = "";//保单号
                    				String customerId5 = "";//客户号
                    				String customerName5 = "";//客户姓名
                    				String customerRole5 = "";
                    				if(CollectionUtilEx.isNotEmpty(claimPolicyHolderBOs)){
                    					for(ClaimPolicyHolderBO claimPolicyHolderBO:claimPolicyHolderBOs){
                    						applyCode5 = applyCode5+claimPolicyHolderBO.getApplyCode()+",";
                    						policyCode5 = policyCode5+claimPolicyHolderBO.getPolicyCode()+",";
                    						customerId5 = customerId5+claimPolicyHolderBO.getCustomerId()+",";
                    						customerName5 = customerName5+claimPolicyHolderBO.getCustomerId()+",";
                    						customerRole5 = this.getBeneficialRoleNew(legalPersonBeneInfoBO, claimPolicyHolderBO,customerRole5,insuredListData,claimBeneData,claimPayeeData);
                    					}
                    				}
                    				if(applyCode5.endsWith(",")){
                    					applyCode5 = applyCode5.substring(0,applyCode5.length()-1);
                    				}
                    				if(policyCode5.endsWith(",")){
                    					policyCode5 = policyCode5.substring(0,policyCode5.length()-1);
                    				}
                    				if(customerId5.endsWith(",")){
                    					customerId5 = customerId5.substring(0,customerId5.length()-1);
                    				}
                    				if(customerName5.endsWith(",")){
                    					customerName5 = customerName5.substring(0,customerName5.length()-1);
                    				}
                    				if(customerRole5.endsWith(",")){
                    					customerRole5 = customerRole5.substring(0,customerRole5.length()-1);
                    				}
                    				blacklistReqVo5.setPrtNo(applyCode5);//投保单号
                    				blacklistReqVo5.setPolicyNo(policyCode5);//保单号
                    				blacklistReqVo5.setHolderName(customerName5);//客户姓名
                    				blacklistReqVo5.setHolderCustomerId(customerId5);//客户
                    				blacklistReqVo5.setCustomerRoleName(customerRole5);//客户角色
                    				blacklistReqVo5.setBussType(ClaimConstant.STRING_THREE);//业务类型
                    				blacklistReqVo5.setPolicyOrg(claimCaseBO.getOrganCode());
                    				blacklistReqVo5.setNotSendFlag(true);
                    				blacklistReqVoList.add(blacklistReqVo5);
                    				logger.debug("********************************开始反洗钱检测-受益所有人："+blacklistReqVoList);
                    			}
                    		}
                    	}
                    }
            	}
            }
            ClaimPayeePO claimPayeePO = new ClaimPayeePO();
            claimPayeePO.setCaseId(claimCaseBO.getCaseId());
            List<ClaimPayeePO> findAllClaimPayee = claimPayeeDao.findAllClaimPayee(claimPayeePO);
            //@invalid 直赔案件并且存在收益分配，领款人只为申请人
            if(beneAndPayeeBOList != null && beneAndPayeeBOList.size() > 0 
            		&& (ClaimConstant.CHANNEL_CODE_TEN.equals(claimCaseBO.getChannelCode())||ClaimConstant.REPORT_MODE_THIRTEEN.equals(claimCaseBO.getReportMode()))){
            	for (ClaimPayeePO claimPayeePO2 : findAllClaimPayee) {
    				if(!directClaimPortsService.directPayeeCheck(claimPayeePO2.getPayeeName())){
    					Payee payee = new Payee();
    	                payee.setPayeeName(claimPayeePO2.getPayeeName()); //@invalid  领款人姓名
    	                payee.setPayeeRecordCount(1); //@invalid  领款人记录数
    	                if(claimPayeePO2.getAccountName()!=null){
    	                	payee.setBankAccname(claimPayeePO2.getAccountName()); //@invalid  银行帐户名	
    	                }else{
    	                	payee.setBankAccname("");
    	                }
    	                payee.setFacoreeRelation(claimPayeePO2.getPayeeRelation()); //@invalid  与受益人关系
    	                payee.setCustomerRelation(claimPayeePO2.getPayeeRelation());//@invalid  与受益人关系
    	                currentclaim.getPayeeList().add(payee);
    				}
    			}
            } else if (beneAndPayeeBOList != null && beneAndPayeeBOList.size() > 0) {
                for (int i = 0; i < beneAndPayeeBOList.size(); i++) {
                    Payee payee = new Payee();
                    if (beneAndPayeeBOList.get(i).getClaimpayee().getPayeeName() != null) {
                        payee.setPayeeName(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeName()); //@invalid  领款人姓名
                    }
/*                    if(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeId() != null){
                        payee.setPayeeId(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeId().toString());//@invalid 领款人ID
                    }
                    if(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeSex() != null){
                        payee.setpayeeSex(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeSex().toString());//@invalid 领款人性别
                    }
                     if(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeBirth() != null){
                         payee.setPayeeBirthday(DateUtilsEx.formatToString(
                                 beneAndPayeeBOList.get(i).getClaimpayee().getPayeeBirth(), PubConstants.DATE_TIME_FORMAT));//@invalid 领款人出生日期
                     }
                     if(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeCertiType() != null){
                         payee.setPayeeIdType(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeCertiType());//@invalid 领款人证件类型
                     }
                     if(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeCertiNo() != null){
                         payee.setPayeeIdNo(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeCertiNo());//@invalid 领款人证件号码
                     }
                     
                    
                     payee.setCorresbeneficiaryIsd(beneAndPayeeBOList.get(i).getClaimbene().getBeneId().toString());*/ //@invalid对应受益人物理ID
                    //@invalid 领款人次数
                    int beneNumber =1;
                    for (BeneAndPayeeBO beneAndPayeeBO : beneAndPayeeBOList) {
                    	if(beneAndPayeeBOList.get(i).getClaimpayee()!=null&&beneAndPayeeBO.getClaimbene()!=null
                    			&&beneAndPayeeBOList.get(i).getClaimpayee().getPayeeId()!=null&&beneAndPayeeBO.getClaimpayee().getPayeeId()!=null
                    			&&!beneAndPayeeBOList.get(i).getClaimpayee().getPayeeId().equals(beneAndPayeeBO.getClaimpayee().getPayeeId())) {
                    		beneNumber++; 
                    	} 
					}
                    payee.setPayeeRecordCount(beneNumber); //@invalid  领款人记录数
                   
                    if (beneAndPayeeBOList.get(i).getClaimpayee().getAccountName() != null) {
                        payee.setBankAccname(beneAndPayeeBOList.get(i).getClaimpayee().getAccountName()); //@invalid  银行帐户名
                    }else {
                    	payee.setBankAccname("");
                    }
                    if (beneAndPayeeBOList.get(i).getClaimpayee().getPayeeRelation() != null) {
                        payee.setFacoreeRelation(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeRelation()); //@invalid  与受益人关系
                        payee.setCustomerRelation(beneAndPayeeBOList.get(i).getClaimpayee().getPayeeRelation());
                    }
                    
                    
                    //@invalid 如果现金的则把银行账户名清空  校验规则
                    if(beneAndPayeeBOList.get(i).getClaimpayee().getPayMode()!=null&&beneAndPayeeBOList.get(i).getClaimpayee().getPayMode().equals(ClaimConstant.PAY_MODE_10)){
                        payee.setPayeeName(""); //@invalid  领款人姓名
                    }
                    currentclaim.getPayeeList().add(payee);
                }
            }else{
            	Payee payee = new Payee();
                payee.setPayeeName(""); //@invalid  领款人姓名
                /*payee.setPayeeId("");//@invalid 领款人ID
                payee.setpayeeSex("");//@invalid 领款人性别
                payee.setPayeeBirthday("");//@invalid 领款人出生日期
                payee.setPayeeIdType("");//@invalid 领款人证件类型
                payee.setPayeeIdNo("");//@invalid 领款人证件号码
                payee.setCorresbeneficiaryIsd("");//@invalid 对应受益人物理ID
*/                payee.setPayeeRecordCount(0); //@invalid  领款人记录数
                payee.setBankAccname(""); //@invalid  银行帐户名
                payee.setFacoreeRelation(""); //@invalid  与受益人关系
                payee.setCustomerRelation("");
                currentclaim.getPayeeList().add(payee);
            }
            
            
            	                 
            
//@invalid             if (beneAndPayeeBOList != null && beneAndPayeeBOList.size() > 0) {
//@invalid                 for(int i = 0; i < beneAndPayeeBOList.size(); i++){
//@invalid                     ClaimBeneBO claimBeneBO = beneAndPayeeBOList.get(i).getClaimbene();
//@invalid                     Beneficiary beneficiary = new Beneficiary();
//@invalid                     beneficiary.setBeneficiaryId("");//@invalid 受益人物理ID
//@invalid                     beneficiary.setBeneficiaryName("");//@invalid 受益人姓名
//@invalid                     beneficiary.setBeneficiarySex("");//@invalid 受益人性别
//@invalid                     beneficiary.setBeneficiaryBirthday("");//@invalid 受益人出生日期
//@invalid                     beneficiary.setBeneficiaryIdType("");//@invalid 受益人证件类别
//@invalid                     beneficiary.setBeneficiaryIdNo("");//@invalid 受益人证件号码
//@invalid                     beneficiary.setBeneficiarySurvival(false);//@invalid 是否指定生存受益人
//@invalid                     //@invalid 查询是否有指定受益人
//@invalid                     ContractBenePO contractBenePO = new ContractBenePO();
//@invalid                     contractBenePO.setCaseId(claimCaseBO.getCaseId());
//@invalid                     contractBenePO.setPolicyId(beneAndPayeeBOList.get(i).getClaimpay().getPolicyId());
//@invalid                     contractBenePO.setBusiItemId(beneAndPayeeBOList.get(i).getClaimpay().getBusiItemId());
//@invalid                     contractBenePO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
//@invalid                     contractBenePO.setCustomerName(claimBeneBO.getBeneName());
//@invalid                     contractBenePO.setCustomerGender(claimBeneBO.getBeneSex());
//@invalid                     contractBenePO.setCustomerBirthday(claimBeneBO.getBeneBirth());
//@invalid                     contractBenePO.setCustomerCertType(claimBeneBO.getBeneCertiNo());
//@invalid                     contractBenePO.setCustomerCertType(claimBeneBO.getBeneCertiType());
//@invalid                     contractBenePO = contractBeneDao.findContractBene(contractBenePO);
//@invalid                     
//@invalid                     beneficiary.setBeneficiaryId(beneAndPayeeBOList.get(i).getClaimbene().getBeneId().toString());//@invalid 受益人ID
//@invalid                     if(beneAndPayeeBOList.get(i).getClaimbene().getBeneName() != null){
//@invalid                         beneficiary.setBeneficiaryName(beneAndPayeeBOList.get(i).getClaimbene().getBeneName());//@invalid 受益人姓名
//@invalid                     }
//@invalid                     if(beneAndPayeeBOList.get(i).getClaimbene().getBeneSex() != null){
//@invalid                         beneficiary.setBeneficiarySex(beneAndPayeeBOList.get(i).getClaimbene().getBeneSex().toString());//@invalid 受益人性别
//@invalid                     }
//@invalid                     if(beneAndPayeeBOList.get(i).getClaimbene().getBeneBirth() != null){
//@invalid                         beneficiary.setBeneficiaryBirthday(DateUtilsEx.formatToString
//@invalid                                 (beneAndPayeeBOList.get(i).getClaimbene().getBeneBirth(), PubConstants.DATE_TIME_FORMAT));//@invalid 受益人出生日期
//@invalid                     }
//@invalid                     if(beneAndPayeeBOList.get(i).getClaimbene().getBeneCertiType() != null){
//@invalid                         beneficiary.setBeneficiaryIdType(beneAndPayeeBOList.get(i).getClaimbene().getBeneCertiType());//@invalid 受益人证件类型
//@invalid                     }
//@invalid                     if(beneAndPayeeBOList.get(i).getClaimbene().getBeneCertiNo() != null){
//@invalid                         beneficiary.setBeneficiaryIdNo(beneAndPayeeBOList.get(i).getClaimbene().getBeneCertiNo());//@invalid 受益人证件号码
//@invalid                     }
//@invalid                     //@invalid 判断理赔受益人是否为指定生存金领取受益人
//@invalid                     if(contractBenePO.getLogId() != null){
//@invalid                         if(contractBenePO.getBeneType().toString().equals("0")){
//@invalid                             beneficiary.setBeneficiarySurvival(true);//@invalid 受益人为指定生存金领取受益人
//@invalid                             
//@invalid                         }else{
//@invalid                             beneficiary.setBeneficiarySurvival(false);//@invalid 受益人不为指定生存金领取受益人
//@invalid                         }
//@invalid                     }else{
//@invalid                         beneficiary.setBeneficiarySurvival(false);//@invalid 受益人不为指定生存金领取受益人
//@invalid                     }                    
//@invalid                     
//@invalid                     //@invalid 如果涉案险种和t_claim_pay涉案险种相同  则把受益人列表存放到涉案险种下
//@invalid                     for (Policy   policy:  currentclaim.getPolicyList()) {
//@invalid                         for (BusinessProduct businessProduct : policy.getBusinessProductList()) {
//@invalid                              
//@invalid                             if(businessProduct.getBusiItemId()!=null&&beneAndPayeeBOList.get(i).getClaimpay()!=null&&
//@invalid                                     beneAndPayeeBOList.get(i).getClaimpay().getBusiItemId()!=null
//@invalid                                     &&businessProduct.getBusiItemId().equals(beneAndPayeeBOList.get(i).getClaimpay().getBusiItemId())){
//@invalid                                 //@invalid 把受益人列表存放到涉案险种下
//@invalid                                 businessProduct.getBeneficiaryList().add(beneficiary);
//@invalid                             }else{
//@invalid                                 Beneficiary beneficiary2 = new Beneficiary();
//@invalid                                 beneficiary2.setBeneficiaryId("");//@invalid 受益人物理ID
//@invalid                                 beneficiary2.setBeneficiaryName("");//@invalid 受益人姓名
//@invalid                                 beneficiary2.setBeneficiarySex("");//@invalid 受益人性别
//@invalid                                 beneficiary2.setBeneficiaryBirthday("");//@invalid 受益人出生日期
//@invalid                                 beneficiary2.setBeneficiaryIdType("");//@invalid 受益人证件类别
//@invalid                                 beneficiary2.setBeneficiaryIdNo("");//@invalid 受益人证件号码
//@invalid                                 beneficiary2.setBeneficiarySurvival(false);//@invalid 是否指定生存受益人
//@invalid                                 businessProduct.getBeneficiaryList().add(beneficiary2);
//@invalid                             } 
//@invalid                         }
//@invalid                     }  
//@invalid                     
//@invalid                 }
//@invalid             }else{
//@invalid                 for (Policy   policy:  currentclaim.getPolicyList()) {
//@invalid                     for (BusinessProduct businessProduct : policy.getBusinessProductList()) {
//@invalid                             Beneficiary beneficiary2 = new Beneficiary();
//@invalid                             beneficiary2.setBeneficiaryId("");//@invalid 受益人物理ID
//@invalid                             beneficiary2.setBeneficiaryName("");//@invalid 受益人姓名
//@invalid                             beneficiary2.setBeneficiarySex("");//@invalid 受益人性别
//@invalid                             beneficiary2.setBeneficiaryBirthday("");//@invalid 受益人出生日期
//@invalid                             beneficiary2.setBeneficiaryIdType("");//@invalid 受益人证件类别
//@invalid                             beneficiary2.setBeneficiaryIdNo("");//@invalid 受益人证件号码
//@invalid                             beneficiary2.setBeneficiarySurvival(false);//@invalid 是否指定生存受益人
//@invalid                             businessProduct.getBeneficiaryList().add(beneficiary2);
//@invalid                     }
//@invalid                 }  
//@invalid             }  
            
            try {
            	//@invalid -------反欺诈-------
            	boolean accReasonFlag = false;//@invalid 出险原因为意外不传规则逆风险
            	boolean claimTypeFlag = true;//@invalid 只存在医疗理赔类型才传规则 风险内容
            	for (ClaimSubCasePO claimSubCasePO : listPo) {
    				//@invalid 判断出险原因是否是意外 		true不是意外
    				if(claimSubCasePO.getAccReason().compareTo(ClaimConstant.ACC_REASON_TWO) != ClaimConstant.ZERO){
    					accReasonFlag = true;
    				}
    				if(!ClaimConstant.CLAIM_TYPE_EIGHT.equals(claimSubCasePO.getClaimType())){
    					claimTypeFlag = false;
    					break;
    				}
    			}
            	//调搜索引擎接口 案件返回信息拼接入参
            	//@invalid 调搜索引擎接口   测试阶段超时处理暂时注释
//@invalid             	final ClaimCaseBO claimCaseNewBO = BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseBO);
//@invalid             	//@invalid 模型引擎返回
//@invalid             	OutputData outPutData = null;
//@invalid          	   ExecutorService executorService = Executors.newFixedThreadPool(5);
//@invalid          	   Future<OutputData> future = executorService.submit(new Callable<OutputData>(){
//@invalid 
//@invalid      			@Override
//@invalid      			public OutputData call() throws Exception {
//@invalid      				OutputData outResult= queryClaimMedicalAntiFraud(claimCaseNewBO);
//@invalid      				return outResult;
//@invalid      			}
//@invalid          		   
//@invalid          	   	});
//@invalid          	   try {
//@invalid          		   //@invalid 只等待搜索引擎接口//@invalid 超时后不在处理搜索引擎数据
//@invalid          		   outPutData = (OutputData) future.get(ClaimConstant.THIRTY, TimeUnit.SECONDS);
//@invalid      			} catch (TimeoutException e) {
//@invalid      				logger.debug("搜索引擎接口接口返回超时,赔案号为：" + claimCaseNewBO.getCaseNo());
//@invalid      			} catch (InterruptedException e) {
//@invalid      				e.printStackTrace();
//@invalid      			} catch (ExecutionException e) {
//@invalid      				e.printStackTrace();
//@invalid      			}
            	//@invalid 正常调用
//            	OutputData outPutData= queryClaimMedicalAntiFraud(claimCaseBO);
            	OutputData outPutData=null;
            	//@invalid 准备入参数据#36577
            	if(outPutData!=null&&outPutData.getModelResult()!=null&&outPutData.getModelResult().size()>0&&claimTypeFlag){
            		//@invalid 有结果说明存在模型
            		currentclaim.setModelResult(true);
            		if(currentclaim.getPolicyList().size()>0){
            			boolean riskNameTwoFlag = false;
            			//@invalid 查询是否有虚假发票(存在赔案层虚假发票 按照规则报文在保项中传过去)
            			ClaimRiskLevelLiabPO claimRiskLevelLiabRiskTwoPO = new ClaimRiskLevelLiabPO();
            			claimRiskLevelLiabRiskTwoPO.setCaseId(claimCaseBO.getCaseId());
            			claimRiskLevelLiabRiskTwoPO.setRiskName(ClaimConstant.CLAIM_RISK_MODE_TWO_STR);//@invalid 虚假发票
            			List<ClaimRiskLevelLiabPO> findAllClaimRiskLevelLiabTwo = claimRiskLevelLiabDao.findAllClaimRiskLevelLiab(claimRiskLevelLiabRiskTwoPO);
            			//@invalid 保单层
            			for (Policy policy : currentclaim.getPolicyList()) {
            				//@invalid 险种列表
            				if(policy.getBusinessProductList().size()>0){
            					for (BusinessProduct businessProduct : policy.getBusinessProductList()) {//@invalid  险种层
            						List<LiabilityGroup> liabilityGroupList =  new ArrayList<LiabilityGroup>();
            						logger.debug("********************************开始拼接险种下保项风险数据咯*******************************");
            						//@invalid 查询险种下的所有责任信息
            						//3.拼接险种下保项风险数据
        							ClaimLiabPO claimLiabRisk = new ClaimLiabPO();
        							claimLiabRisk.setCaseId(claimCaseBO.getCaseId());
        							claimLiabRisk.setPolicyCode(policy.getPolicyApplyCode());//@invalid 保单号
        							claimLiabRisk.setBusiProdCode(businessProduct.getBpCode());//@invalid 险种编码
        							List<ClaimLiabPO> findAllClaimLiab = claimLiabDao.findAllClaimLiab(claimLiabRisk);
            						if(accReasonFlag){//@invalid 出险原因不是意外
            							for (ClaimLiabPO claimLiabPO2 : findAllClaimLiab) {
            								ClaimRiskLevelLiabPO claimRiskLevelLiabPO = new ClaimRiskLevelLiabPO();
                    						claimRiskLevelLiabPO.setCaseId(claimCaseBO.getCaseId());
                    						claimRiskLevelLiabPO.setPolicyCode(policy.getPolicyApplyCode());//@invalid 保单号
                    						claimRiskLevelLiabPO.setLiabCode(claimLiabPO2.getLiabCode());//@invalid 责任编码
                    						//@invalid 查询保单下的保项风险(模型（评分）返回)
                    						List<ClaimRiskLevelLiabPO> findAllClaimRiskLevelLiab = claimRiskLevelLiabDao.findAllClaimRiskLevelLiab(claimRiskLevelLiabPO);
            								for (ClaimRiskLevelLiabPO claimRiskLevelLiabPO2 : findAllClaimRiskLevelLiab) {
	                							if(findAllClaimRiskLevelLiabTwo.size()>0){
	                								riskNameTwoFlag = true;
	                							}
	                							if(claimLiabPO2.getLiabCode().equals(claimRiskLevelLiabPO2.getLiabCode())){
	                								//@invalid 循环险种下的责任信息
	                								//@invalid 责任模型
	                								List<Model> modelList = new ArrayList<Model>();
	                								//@invalid 责任对象
	                								LiabilityGroup liabilityGroup = new LiabilityGroup();
	                								liabilityGroup.setLgCode(claimLiabPO2.getLiabCode());//@invalid 责任编码
	                								if(claimLiabPO2.getLiabCode().equals(claimRiskLevelLiabPO2.getLiabCode())){
	                									Model model = new Model();//@invalid  责任  
	                									//@invalid 险种/责任匹配
	                									model.setModelName(ClaimConstant.CLAIM_RISK_MODE_ONE);//@invalid 模型名称
	                									if(claimRiskLevelLiabPO2.getRiskFraction()!=null){
	                										model.setProbability(claimRiskLevelLiabPO2.getRiskFraction().doubleValue());//@invalid 模型黑样本分数
	                									}else{
	                										model.setProbability(Double.valueOf(0));//@invalid 模型黑样本分数
	                									}
	                									modelList.add(model);
	                								}
	                								if(riskNameTwoFlag){ //@invalid 存在赔案层虚假发票 按照规则报文在保项中传过去
	                									Model modelTwo = new Model();//@invalid  责任  
	                									modelTwo.setModelName(ClaimConstant.CLAIM_RISK_MODE_TWO);//@invalid 模型名称
	                									if(findAllClaimRiskLevelLiabTwo.get(0).getRiskFraction()!=null){
	                										modelTwo.setProbability(findAllClaimRiskLevelLiabTwo.get(0).getRiskFraction().doubleValue());//@invalid 模型黑样本分数
	                									}else{
	                										modelTwo.setProbability(Double.valueOf(0));//@invalid 模型黑样本分数
	                									}
	                									modelList.add(modelTwo);
	                									riskNameTwoFlag = false;
	                								}
	                								//@invalid 防止规则报错
	                								if(modelList.size()==0){
	                									 Model model = new Model();//@invalid  责任  
	                									 model.setModelName("notNull");
	                                                     model.setProbability(Double.valueOf(0));
	                                                     modelList.add(model);
	                								}
	                								liabilityGroup.setModelList(modelList);
	                								liabilityGroupList.add(liabilityGroup);//@invalid 责任对象赋值完成
	                							}
                							}
                							
                						}
            						}else if(findAllClaimRiskLevelLiabTwo.size()>0){ //@invalid 出险原因不是意外但是存在虚假发票
            							//@invalid 循环险种下的责任信息
            							for (ClaimLiabPO claimLiabPO2 : findAllClaimLiab) {
            								riskNameTwoFlag = true;
            								//@invalid 责任模型
            								List<Model> modelList = new ArrayList<Model>();
            								//@invalid 责任对象
            								LiabilityGroup liabilityGroup = new LiabilityGroup();
            								liabilityGroup.setLgCode(claimLiabPO2.getLiabCode());//@invalid 责任编码
            								if(riskNameTwoFlag){ //@invalid 存在赔案层虚假发票 按照规则报文在保项中传过去
	        									Model modelTwo = new Model();//@invalid  责任  
	        									modelTwo.setModelName(ClaimConstant.CLAIM_RISK_MODE_TWO);//@invalid 模型名称
	        									if(findAllClaimRiskLevelLiabTwo.get(0).getRiskFraction()!=null){
	        										modelTwo.setProbability(findAllClaimRiskLevelLiabTwo.get(0).getRiskFraction().doubleValue());//@invalid 模型黑样本分数
	        									}else{
	        										modelTwo.setProbability(Double.valueOf(0));//@invalid 模型黑样本分数
	        									}
	        									modelList.add(modelTwo);
            									riskNameTwoFlag = false;
            								}
        									//@invalid 防止规则报错
            								if(modelList.size()==0){
            									 Model model = new Model();//@invalid  责任  
            									 model.setModelName("notNull");
                                                 model.setProbability(Double.valueOf(0));
                                                 modelList.add(model);
            								}
            								liabilityGroup.setModelList(modelList);
            								liabilityGroupList.add(liabilityGroup);//@invalid 责任对象赋值完成
            							}
            							
            						}else{ //@invalid 防止规则报错
            							//@invalid 责任模型
        								List<Model> modelList = new ArrayList<Model>();
        								//@invalid 责任对象
        								LiabilityGroup liabilityGroup = new LiabilityGroup();
        								liabilityGroup.setLgCode("notNull");//@invalid 责任编码
        								Model model = new Model();//@invalid  责任  
   									 	model.setModelName("notNull");
                                        model.setProbability(Double.valueOf(0));
                                        modelList.add(model);
                                        liabilityGroup.setModelList(modelList);
                                        liabilityGroupList.add(liabilityGroup);//@invalid 责任对象赋值完成
            						}
            						logger.debug("********************************拼接险种下保项风险数据结束*******************************");
            						businessProduct.setLiabilityGroupList(liabilityGroupList);//@invalid 险种下的责任组集合
            					}
            				}  
            			}
            			
            		}
            		
            	}
            	//@invalid新增图谱运行结果和图谱列表
	            if(outputData1 != null && outputData1.getClaim()!=null && !outputData1.getClaim().getFrmsClmno().equals("")
	            		&& outputData1.getClaim().getRules().getRule()!=null && outputData1.getClaim().getRules().getRule().size() > 0){
	            	currentclaim.setImageResult(ClaimConstant.STRING_ONE);
	            	if(currentclaim.getPolicyList().size()>0){
	            		for (Policy policy : currentclaim.getPolicyList()) {
            				ClaimCheatAtlasPO cheatAtlasPO = new ClaimCheatAtlasPO();
                            cheatAtlasPO.setCaseNo(claimCaseBO.getCaseNo());
                            cheatAtlasPO.setPolicyCode(policy.getPolicyApplyCode());
                            List<ClaimCheatAtlasPO> cheatAtlasPOList = claimCheatAtlasDao.findAllClaimCheatAtlas(cheatAtlasPO);
                            List<Image> imageList = new ArrayList<Image>();
                            if(cheatAtlasPOList.size() > 0){
                            	for (ClaimCheatAtlasPO claimCheatAtlasPO : cheatAtlasPOList) {
                            		Image image = new Image();
                                	image.setImageRuleId(claimCheatAtlasPO.getFrmsRuleCode());
                                	image.setImageScore(new Double(claimCheatAtlasPO.getFrmsRuleScore()));
                                	imageList.add(image);
    							}
                            }else{
                            	Image image = new Image();
                            	image.setImageRuleId("");
                            	image.setImageScore(Double.valueOf(0));
                            	imageList.add(image);
                            }
                            policy.setImageList(imageList);
	            		}
	            	}
	            }else{
	            	currentclaim.setImageResult(ClaimConstant.STRING_ZERO);
	            	if(currentclaim.getPolicyList().size()>0){
            			//@invalid 保单层
            			for (Policy policy : currentclaim.getPolicyList()) {
                            List<Image> imageList = new ArrayList<Image>();
                            Image image = new Image();
                            image.setImageRuleId("");
                        	image.setImageScore(Double.valueOf(0));
                        	imageList.add(image);
                            policy.setImageList(imageList);
            			}
            		}
	            }
			} catch (Exception e) {
				logger.debug("********************************反欺诈模型引擎处理报错*******************************");
				e.printStackTrace();
			}
            
            
            //@invalid  ------------调查呈报-------------
            SurveyApplyPO surveyApplyPO = new SurveyApplyPO();
            surveyApplyPO.setCaseId(claimCaseBO.getCaseId());
            boolean noInquiry = false;
            String positiveStr = "";//@invalid 阳性标识
            String inqDate = "";//@invalid 调查日期
            List<SurveyApplyPO> queryAllSurveyApplyByCaseNO = surveyApplyDao.queryAllSurveyApplyByCaseNO(surveyApplyPO);
            //@invalid 循环查询
            if(queryAllSurveyApplyByCaseNO.size()>0){
            	noInquiry = true;//@invalid 是否存在调查任务
            	//@invalid 按照插入时间升序，最早的调查时间却第一条
            	if(queryAllSurveyApplyByCaseNO.get(0).getApplyDate()!=null){
            		inqDate = DateUtilsEx.formatToString(queryAllSurveyApplyByCaseNO.get(0).getApplyDate(), ClaimConstant.DEFAULT_DATE_FORMAT_STR) ;
            	}
            	for (SurveyApplyPO surveyApplyPO2 : queryAllSurveyApplyByCaseNO) {
            		//@invalid 多个调查任务，其中包含阳性结论，则传值“1”
            		if(surveyApplyPO2.getData().get("positive_flag")!=null){
            			BigDecimal  big = (BigDecimal) surveyApplyPO2.getData().get("positive_flag");
            			positiveStr = big.toString();
            			if(ClaimConstant.YES.equals(positiveStr)){
            				break;
            			}
            		}
            	}
            	
            }else{
            	noInquiry = false;
            }
            
//@invalid             int applysize = surveyApplyDao.findSurveyApplyTotal(surveyApplyPO);
//@invalid             if (applysize > 0) {
//@invalid                 noInquiry = true;
//@invalid             } else {
//@invalid                 noInquiry = false;
//@invalid             }
            SurveyReport surveyReport = new SurveyReport();
            surveyReport.setNoInquiry(noInquiry); //@invalid  无调查
            surveyReport.setSubTaskRecord(false); //@invalid  呈报任务记录
            surveyReport.setMasFlag(positiveStr); //@invalid  阳性结论
            surveyReport.setInqDate(inqDate); //@invalid  调查日期
            currentclaim.getSurveyReportList().add(surveyReport);
            //反洗钱监测名单黑名单范围校验
    		logger.debug("********************************开始反洗钱检测："+blacklistReqVoList);
            List<BlacklistRspVo> blacklistRspVos = BOServiceFactory.getBlacklistInfo().queryBlacklistInfo(blacklistReqVoList);
            logger.debug("反洗钱检测名单结果："+blacklistRspVos);
            Boolean flag = false;
            for(BlacklistRspVo blacklistRspVo : blacklistRspVos) {
            	if(blacklistRspVo.getBlacklistState().compareTo(new BigDecimal(2))==0
            			|| blacklistRspVo.getBlacklistState().compareTo(new BigDecimal(1))==0) {
            		flag = true;
            	}
            }
            logger.debug("反洗钱检测名单结果 ："+blacklistRspVos+",flag:"+flag);
            if(flag) {
            	currentclaim.setBeneficiaryBlackList("1");
            }else {
            	currentclaim.setBeneficiaryBlackList("0");
            }
            // 险种循环节点下新增通用规则场景列表(businessFunctionMainList)
            if (CollectionUtilEx.isNotEmpty(currentclaim.getPolicyList())) {
				// 创建缓存 Map 用于存储 DebtPremResData
				Map<String, DebtPremResData> debtPremCache = new HashMap<>();
                for (Policy policy : currentclaim.getPolicyList()) {
                    List<BusinessProduct> businessProductList = policy.getBusinessProductList();
                    if (CollectionUtilEx.isNotEmpty(businessProductList)) {
                        for (BusinessProduct businessProduct : businessProductList) {
                            BusinessFunctionMainDetailItem businessFunctionMainDetailItem = new BusinessFunctionMainDetailItem();
                            businessFunctionMainDetailItem.setBusinessFunctionDetailCode(ClaimConstant.IS_GRACE_ACCIDENT);
                            businessFunctionMainDetailItem.setBusinessFunctionValue(ClaimConstant.STRING_ZERO);

                            ClaimLiabPO claimliab = new ClaimLiabPO();
                            claimliab.setCaseId(claimCaseBO.getCaseId());
                            claimliab.setPolicyCode(policy.getPolicyApplyCode());
                            claimliab.setBusiProdCode(businessProduct.getBpCode());
                            List<ClaimLiabPO> resultClaimLiab = claimLiabDao.findAllCalcClaimLiab(claimliab);
                            if(CollectionUtilEx.isNotEmpty(resultClaimLiab)) {
                                //给付责任保单下
                                for(ClaimLiabPO claimLiab : resultClaimLiab){
                                    //判断是否欠缴保费并且在宽限期内出险
                                    EndCaseReqData inputData = new EndCaseReqData();
                                    inputData.setPolicyCode(claimLiab.getPolicyCode());
                                    inputData.setDateFlag(claimLiab.getUtilDate("claim_date"));
                                    inputData.setBusiItemId(claimLiab.getBusiItemId());
									String cacheKey = inputData.getPolicyCode() + "-" + inputData.getDateFlag().getTime() + "-" + inputData.getBusiItemId();
									DebtPremResData debtPremResData;
									// 检查缓存
									if (debtPremCache.containsKey(cacheKey)) {
										debtPremResData = debtPremCache.get(cacheKey);
									} else {
										// 调保单接口查询是否欠缴的保费
										debtPremResData = clmServiceUtil.paidebtpremuccqueryDebtPrem(inputData);
										debtPremCache.put(cacheKey,debtPremResData);
									}
                                    // 查询宽限期
                                    BigDecimal days = new BigDecimal(0);
                                    if(claimCaseBO.getPeriodList().get(claimLiab.getBusiItemId())==null){
                                        claimMatchCalcService.queryBusiProdPeriod(claimLiab.getPolicyId(),claimLiab.getCaseId(),claimCaseBO.getPeriodList());
                                    }
                                    days = claimCaseBO.getPeriodList().get(claimLiab.getBusiItemId());

                                    // 根据接口返还的下次缴费日判断是否在宽限期内出险
                                    if(debtPremResData != null
                                            && debtPremResData.getDebtValue() != null
                                            && debtPremResData.getDebtValue().intValue() != 0
                                            && debtPremResData.getPayDueDate() != null
                                            && claimLiab.getUtilDate("claim_date").compareTo(debtPremResData.getPayDueDate()) >= 0 
                                            && claimLiab.getUtilDate("claim_date").compareTo(DateUtilsEx.addDay(debtPremResData.getPayDueDate(), days.intValue())) <= 0){
                                        businessFunctionMainDetailItem.setBusinessFunctionValue(ClaimConstant.STRING_ONE);
                                        break;
                                    }
                                }
                            }

                            List<BusinessFunctionMainDetailItem> businessFunctionMainDetailItemList = new ArrayList<>();
                            businessFunctionMainDetailItemList.add(businessFunctionMainDetailItem);

                            BusinessFunctionMain businessFunctionMain = new BusinessFunctionMain();
                            businessFunctionMain.setBusinessFunctionMainCode(ClaimConstant.CLS_BUSS_ACC);
                            businessFunctionMain.setBusinessFunctionMainDetailItemList(businessFunctionMainDetailItemList);

                            List<BusinessFunctionMain> businessFunctionMainList = new ArrayList<>();
                            businessFunctionMainList.add(businessFunctionMain);
                            // todo tes加上判断逻辑, 不直接set  分批次提交
                            businessProduct.setBusinessFunctionMainList(businessFunctionMainList);
                        }
                    }
                }
            }

            //赔案层级currentclaim下新增赔案通用规则场景列表(claimFunctionMainList) 
            if(currentclaim !=null){
                ClaimCasePO claimCasePO = new ClaimCasePO();
                claimCasePO.setCaseId(claimCaseBO.getCaseId());
                ClaimCasePO findClaimCaseByCaseId = claimCaseDao.findClaimCaseByCaseId(claimCasePO);
                String applyChannelValue = findClaimCaseByCaseId.getChannelCode();
                String servComlValue=findClaimCaseByCaseId.getServCom();
                List<ClaimFunctionMain> ruleList = new ArrayList<>();
                ClaimFunctionMain applyChannelRule = new ClaimFunctionMain();
                ClaimFunctionMainDetailItem channelDetail = new ClaimFunctionMainDetailItem();
                if(applyChannelValue != null && !"".equals(applyChannelValue)){
                    // 添加申请渠道规则
                    applyChannelRule.setClainFunctionMainCode(ClaimConstant.CLS_CLAIM_ALL);
                    channelDetail.setClaimFunctionDetailCode(ClaimConstant.APPLY_CHANNEL);
                    channelDetail.setClaimFunctionValue(applyChannelValue);
                    List<ClaimFunctionMainDetailItem> channelDetails = new ArrayList<>();
                    channelDetails.add(channelDetail);
                    applyChannelRule.setClaimFunctionMainDetailItemList(channelDetails);
                    ruleList.add(applyChannelRule);
                } else {
                    applyChannelRule.setClainFunctionMainCode("");
                    channelDetail.setClaimFunctionDetailCode("");
                    channelDetail.setClaimFunctionValue("");
                    List<ClaimFunctionMainDetailItem> channelDetails = new ArrayList<>();
                    channelDetails.add(channelDetail);
                    applyChannelRule.setClaimFunctionMainDetailItemList(channelDetails);
                    ruleList.add(applyChannelRule);
                }
                if(ClaimConstant.CHANNEL_CODE_TEN.equals(applyChannelValue) && servComlValue != null && !"".equals(servComlValue)){
                    // 添加直赔服务商规则
                    ClaimFunctionMain directServiceRule = new ClaimFunctionMain();
                    directServiceRule.setClainFunctionMainCode(ClaimConstant.CLS_BUSS_ALL);
    
                    ClaimFunctionMainDetailItem serviceDetail = new ClaimFunctionMainDetailItem();
                    serviceDetail.setClaimFunctionDetailCode(ClaimConstant.DIRECT_COMPANY);
                    serviceDetail.setClaimFunctionValue(servComlValue);
                    List<ClaimFunctionMainDetailItem> serviceDetails = new ArrayList<>();
                    serviceDetails.add(serviceDetail);
                    directServiceRule.setClaimFunctionMainDetailItemList(serviceDetails);
                    ruleList.add(directServiceRule);
                }
                //  todo tes加上判断逻辑, 不直接set  分批次提交
                currentclaim.setClaimFunctionMainList(ruleList);
            }
			
            if(currentclaim !=null){
                List<ClaimFunctionMainDetailItem> detailItems = new ArrayList<>();
				// 备注/问题件类型&选项
                ClaimMemoPO claimMemoPO = new ClaimMemoPO();
                claimMemoPO.setCaseId(claimCaseBO.getCaseId());
                List<ClaimMemoPO> memoPOList = claimMemoDao.findAllClaimMemo(claimMemoPO);
                if (CollectionUtilEx.isNotEmpty(memoPOList)) {
                    List<String> memoTypeList = new ArrayList<>();
                    List<String> memoOptionList = new ArrayList<>();
                    for (ClaimMemoPO memoPO : memoPOList) {
                        //类型
                        String memoType = memoPO.getMemoType();
                        memoTypeList.add(memoType);
                        //选项
                        String memoOption = memoPO.getMemoOption();
                        memoOptionList.add(memoOption);
                    }
                    String memoType = StringUtils.join(memoTypeList, ",");
                    String memoOption = StringUtils.join(memoOptionList, ",");
                    // todo 是否必传
                    ClaimFunctionMainDetailItem memoTypeDetailItem = new ClaimFunctionMainDetailItem();
					memoTypeDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_NOTES_TYPE);
					memoTypeDetailItem.setClaimFunctionValue(memoType);
                    detailItems.add(memoTypeDetailItem);

					ClaimFunctionMainDetailItem memoOptionDetailItem = new ClaimFunctionMainDetailItem();
					memoOptionDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_NOTES_CODE);
					memoOptionDetailItem.setClaimFunctionValue(memoOption);
                    detailItems.add(memoOptionDetailItem);
                }

                //查询出险结果信息
				ClaimAccidentResultPO claimAccidentResultPO1 = new ClaimAccidentResultPO();
				claimAccidentResultPO1.setCaseId(claimCaseBO.getCaseId());
				List<ClaimAccidentResultPO> allClaimAccidentResult = claimAccidentResultDao.findAllClaimAccidentResult(claimAccidentResultPO1);
				if (CollectionUtils.isNotEmpty(allClaimAccidentResult)) {
					List<String> accidentResul1List = new ArrayList<>();
					for (ClaimAccidentResultPO accidentResultPO : allClaimAccidentResult) {
						accidentResul1List.add(accidentResultPO.getAccResult1());
					}
					String accidentResul1 = StringUtils.join(accidentResul1List, ",");
					ClaimFunctionMainDetailItem accResultDetailItem = new ClaimFunctionMainDetailItem();
					accResultDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_ACC_RESULT1);
					accResultDetailItem.setClaimFunctionValue(accidentResul1);
					detailItems.add(accResultDetailItem);
				}
				// 医生
				ClaimDoctorPO claimDoctorPO1 = new ClaimDoctorPO();
				claimDoctorPO1.setCaseId(claimCaseBO.getCaseId());
				List<ClaimDoctorPO> claimDoctorPOList = claimDoctorDao.findAllClaimDoctor(claimDoctorPO1);
				if (CollectionUtils.isNotEmpty(claimDoctorPOList)) {
					List<String> doctorNamelist = new ArrayList<>();
					for (ClaimDoctorPO doctorPO : claimDoctorPOList) {
						String doctorName = doctorPO.getDoctorName();
						doctorNamelist.add(doctorName);  
					}
					String doctorName = StringUtils.join(doctorNamelist, ",");
					ClaimFunctionMainDetailItem doctorDetailItem = new ClaimFunctionMainDetailItem();
					doctorDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_DOCTOR_NAME);
					doctorDetailItem.setClaimFunctionValue(doctorName);
					detailItems.add(doctorDetailItem);
				}
				
				ClaimCasePO claimCasePO = new ClaimCasePO();
				claimCasePO.setCaseId(claimCaseBO.getCaseId());
				ClaimCasePO findClaimCaseByCaseId = claimCaseDao.findClaimCaseByCaseId(claimCasePO);
				ClaimHospitalServicePO claimHospitalServicePO = new ClaimHospitalServicePO();
				claimHospitalServicePO.setHospitalCode(findClaimCaseByCaseId.getCureHospital());
				claimHospitalServicePO = claimHospitalServiceDao.findClaimHospitalService(claimHospitalServicePO);
				String hospitalLevel = claimHospitalServicePO.getHospitalLevel();
				BigDecimal auxiliaryFlag = claimHospitalServicePO.getAuxiliaryFlag();
				// 医院代码
				ClaimFunctionMainDetailItem hospitalCodeDetailItem = new ClaimFunctionMainDetailItem();
				hospitalCodeDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_HOSPITAL_CODE);
				hospitalCodeDetailItem.setClaimFunctionValue(findClaimCaseByCaseId.getCureHospital());
				detailItems.add(hospitalCodeDetailItem);
				// 医院等级
				ClaimFunctionMainDetailItem hospitaLevelDetailItem = new ClaimFunctionMainDetailItem();
				hospitaLevelDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_HOSPITAL_LEVEL);
				hospitaLevelDetailItem.setClaimFunctionValue(hospitalLevel);
				detailItems.add(hospitaLevelDetailItem);
				// “以疗养、护理、戒酒或戒毒、精神心理治疗或类似功能为主标识”：案件层，是、否、空
				ClaimFunctionMainDetailItem projectTypeDetailItem = new ClaimFunctionMainDetailItem();
				projectTypeDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_PROJECT_TYPE);
				if (auxiliaryFlag == null ) {
					projectTypeDetailItem.setClaimFunctionValue("");
				} else {
					projectTypeDetailItem.setClaimFunctionValue(auxiliaryFlag.toString());
				}
				detailItems.add(projectTypeDetailItem);

				// todo 门诊治疗类型
				ClaimCasePO claimCasePO1 = new ClaimCasePO();
				claimCasePO1.setCaseId(claimCaseBO.getCaseId());
				claimCasePO1 = claimCaseDao.findClaimCaseByCaseId(claimCasePO1);
				List<ClaimCasePO> historicalCaseList = claimCaseDao.queryHistoricalCase2YearsPrior(claimCasePO1);

				List<String> hisCaseIdList = new ArrayList<>();
				for (ClaimCasePO historicalCasePO : historicalCaseList) {
					hisCaseIdList.add(historicalCasePO.getCaseId().toString());
				}
				hisCaseIdList = new ArrayList<>(new HashSet<>(hisCaseIdList));

				// 常见慢性病ICD代码集
				List<ClaimCasePO> findChronicDiseaseInfoCode = claimCaseDao.findChronicDiseaseInfoCode(new ClaimCasePO());
				List<String> chronicDiseaseInfoCodeList = new ArrayList<>();
				for (ClaimCasePO chronicDiseaseInfoCode : findChronicDiseaseInfoCode) {
					if (chronicDiseaseInfoCode.getData().get("disease_code") != null) {
						chronicDiseaseInfoCodeList.add(chronicDiseaseInfoCode.getData().get("disease_code").toString());
					}
				}
				
				// 首位出险结果1
				ClaimAccidentResultPO claimAccidentResultPO4 = new ClaimAccidentResultPO();
				claimAccidentResultPO4.getData().put("case_id_list",hisCaseIdList);
				List<ClaimAccidentResultPO> firstAccidentResult1List = claimAccidentResultDao.findFirstAccidentResult1(claimAccidentResultPO4);
				
				// 调查
				SurveyApplyPO surveyApplyPO2 = new SurveyApplyPO();
				surveyApplyPO2.getData().put("case_id_list",hisCaseIdList);
				List<SurveyApplyPO> surveyApplyByCaseIdsList = surveyApplyDao.findSurveyApplyByCaseIds(surveyApplyPO2);
				
				// 医生/科室
				ClaimDoctorPO claimDoctorPO2 = new ClaimDoctorPO();
				claimDoctorPO2.getData().put("case_id_list",hisCaseIdList);
				List<ClaimDoctorPO> hisClaimDoctorList = claimDoctorDao.findAllClaimDoctorByCaseIds(claimDoctorPO2);
				List<String> claimDepartmentList = new ArrayList<>();
				List<String> claimDoctorNameList = new ArrayList<>();
				for (ClaimDoctorPO doctorPO : claimDoctorPOList) {
					claimDepartmentList.add(doctorPO.getCode());
					claimDoctorNameList.add(doctorPO.getCode() + "_" +doctorPO.getDoctorName());
				}
				
				// 如客户因同种疾病2年内出险次数
				int sameIllTimes = 0;
				// 如客户因同一医院2年内出险次数
				int sameHospitalTimes = 0;
				// 如客户因同一科室2年内出险次数
				int sameDepartmentTimes = 0;
				// 如客户因同一医生2年内出险次数
				int sameDoctorTimes = 0;
				// 如客户因同一意外细节2年内出险次数
				int sameActionTimes = 0;
				// 如客户因猫抓狗咬2年内出险次数
				int petActionTimes = 0;
				Set<String> sameActionSet = new HashSet<String>();
				Set<String> petActionSet = new HashSet<String>();
				String currCaseFirstAccResult1 = allClaimAccidentResult.get(0).getAccResult1();
				for (ClaimCasePO historicalCasePO : historicalCaseList) {
					// 首位出险结果1与本案首位出险结果1相同
					boolean samefirstAccidentResult1Flag = false;
					// 未发起过审核阶段调查或发起过审核阶段调查但至少有一条结论为阳性
					boolean surveyApplyFlag = false;
					// 治疗医院的医院等级不属于"三级医院"
					boolean hospitalLevelFlag = false;
					// 首位出险结果1不属于“常见慢性病ICD代码集”
					boolean chronicDiseaseFlag = false;
					// 与本案治疗医院相同
					boolean sameHospitalFlag = false;
					// 与本案治疗医院相同且科室相同
					boolean sameDepartmentFlag = false;
					// 科室相同且医生相同
					boolean sameDoctorFlag = false;
					// 【最终给付金额+拒付金额（保项层拒付金额之和）】属于（0，20000】区间
					boolean payAmountFlag = false;
					
					
					for (ClaimAccidentResultPO firstAccidentResult1PO : firstAccidentResult1List) {
						if (historicalCasePO.getCaseId().equals(firstAccidentResult1PO.getCaseId())) {
							if (currCaseFirstAccResult1.equals(firstAccidentResult1PO.getAccResult1())) {
								samefirstAccidentResult1Flag = true;
								break;
							}
						}
					}
					
					if (surveyApplyByCaseIdsList != null && !surveyApplyByCaseIdsList.isEmpty()) {
						for (SurveyApplyPO applyPO : surveyApplyByCaseIdsList) {
							if (historicalCasePO.getCaseId().equals(applyPO.getCaseId())) {
								Object positiveFlag = applyPO.getData().get("positive_flag");
								if (positiveFlag != null && (((BigDecimal) positiveFlag).compareTo(new BigDecimal("1")) == 0)) {
									surveyApplyFlag = true;
									break;
								}
							}
						}
					} else {
						surveyApplyFlag = true;
					}
					
					Object historicalHospitalLevel = historicalCasePO.getData().get("hospital_level");
					if (historicalHospitalLevel != null) {
						if (!ClaimConstant.GRADE_3_HOSPITAL_LEVELS.contains(historicalHospitalLevel.toString())) {
							hospitalLevelFlag = true;
						}
					} else {
						hospitalLevelFlag = true;
					}

					for (ClaimAccidentResultPO firstAccidentResult1PO : firstAccidentResult1List) {
						if (historicalCasePO.getCaseId().equals(firstAccidentResult1PO.getCaseId())) {
							String firsAccResult1 = firstAccidentResult1PO.getAccResult1();
							if (!chronicDiseaseInfoCodeList.contains(firsAccResult1)) {
								chronicDiseaseFlag = true;
								break;
							}
						}
					}

					if (historicalCasePO.getCureHospital().equals(findClaimCaseByCaseId.getCureHospital())) {
						sameHospitalFlag = true;
					}
					
					for (ClaimDoctorPO hisClaimDoctor : hisClaimDoctorList) {
						if (historicalCasePO.getCaseId().equals(hisClaimDoctor.getCaseId())) {
							String historicalDepartmentcode = hisClaimDoctor.getCode();
							if (claimDepartmentList.contains(historicalDepartmentcode)) {
								sameDepartmentFlag = true;
								break;
							}
						}
					}
					
					for (ClaimDoctorPO hisClaimDoctor : hisClaimDoctorList) {
						if (historicalCasePO.getCaseId().equals(hisClaimDoctor.getCaseId())) {
							String historicalPair = hisClaimDoctor.getCode() + "_" + hisClaimDoctor.getDoctorName();
							if (claimDoctorNameList.contains(historicalPair)) {
								sameDoctorFlag = true;
								break;
							}
						}
					}

					
					if (historicalCasePO.getCalcPay() == null) {
						historicalCasePO.setCalcPay(new BigDecimal(ClaimConstant.ZERO));
					}
                    if (historicalCasePO.getAdvancePay() == null) {
                    	historicalCasePO.setAdvancePay(new BigDecimal(ClaimConstant.ZERO));
                    }
					if (historicalCasePO.getBalancePay() == null) {
						historicalCasePO.setBalancePay(new BigDecimal(ClaimConstant.ZERO));
					}
					if (historicalCasePO.getActualPay() == null) {
						historicalCasePO.setActualPay(historicalCasePO.getCalcPay().add(historicalCasePO.getBalancePay().subtract(historicalCasePO.getAdvancePay())));
					}
					if (historicalCasePO.getRejectPay() == null) {
						historicalCasePO.setRejectPay(new BigDecimal(ClaimConstant.ZERO));
					}


					BigDecimal totalPay = historicalCasePO.getActualPay().add(historicalCasePO.getRejectPay());
					if (totalPay.compareTo(new BigDecimal("1000")) >0 && totalPay.compareTo(new BigDecimal("20000")) <=0) {
						payAmountFlag = true;
					}


					if (samefirstAccidentResult1Flag && payAmountFlag && surveyApplyFlag && hospitalLevelFlag) {
						sameIllTimes++;
					}

					if (sameHospitalFlag && payAmountFlag && surveyApplyFlag && chronicDiseaseFlag) {
						sameHospitalTimes++;
					}
					
					if (sameHospitalFlag && payAmountFlag && sameDepartmentFlag && surveyApplyFlag && chronicDiseaseFlag) {
						sameDepartmentTimes++;
					}
					
					if (sameHospitalFlag && payAmountFlag && sameDoctorFlag && surveyApplyFlag && chronicDiseaseFlag) {
						sameDoctorTimes++;
					}
					
					if (historicalCasePO.getAccidentDetail().equals(findClaimCaseByCaseId.getAccidentDetail()) 
							&& (totalPay.compareTo(BigDecimal.ZERO) >0 && totalPay.compareTo(new BigDecimal("20000")) <=0)
							&& surveyApplyFlag) {
						sameActionSet.add(String.valueOf(historicalCasePO.getData().get("accident_no")));
					}
					
					if ("W53".equals(historicalCasePO.getAccidentDetail()) 
							|| "W54".equals(historicalCasePO.getAccidentDetail()) 
							|| "W55".equals(historicalCasePO.getAccidentDetail())) {
						petActionSet.add(String.valueOf(historicalCasePO.getData().get("accident_no")));
					}
				}
				
				sameActionTimes = sameActionSet.size();
				petActionTimes = petActionSet.size();
				
				// 如客户因同种疾病2年内出险次数
				ClaimFunctionMainDetailItem sameIllTimesDetailItem = new ClaimFunctionMainDetailItem();
				sameIllTimesDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_SAME_ILL_TIMES);
				sameIllTimesDetailItem.setClaimFunctionValue(String.valueOf(sameIllTimes));
				detailItems.add(sameIllTimesDetailItem);
				// 如客户因同一医院2年内出险次数
				ClaimFunctionMainDetailItem sameHospitalTimesDetailItem = new ClaimFunctionMainDetailItem();
				sameHospitalTimesDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_SAME_HOSPITAL_TIMES);
				sameHospitalTimesDetailItem.setClaimFunctionValue(String.valueOf(sameHospitalTimes));
				detailItems.add(sameHospitalTimesDetailItem);
				// 如客户因同一科室2年内出险次数
				ClaimFunctionMainDetailItem sameDepartmentTimesDetailItem = new ClaimFunctionMainDetailItem();
				sameDepartmentTimesDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_SAME_DEPARTMENT_TIMES);
				sameDepartmentTimesDetailItem.setClaimFunctionValue(String.valueOf(sameDepartmentTimes));
				detailItems.add(sameDepartmentTimesDetailItem);
				// 如客户因同一医生2年内出险次数
				ClaimFunctionMainDetailItem sameDoctorTimesDetailItem = new ClaimFunctionMainDetailItem();
				sameDoctorTimesDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_SAME_DOCTOR_TIMES);
				sameDoctorTimesDetailItem.setClaimFunctionValue(String.valueOf(sameDoctorTimes));
				detailItems.add(sameDoctorTimesDetailItem);
				// 如客户因同一意外细节2年内出险次数
				ClaimFunctionMainDetailItem sameActionTimesDetailItem = new ClaimFunctionMainDetailItem();
				sameActionTimesDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_SAME_ACTION_TIMES);
				sameActionTimesDetailItem.setClaimFunctionValue(String.valueOf(sameActionTimes));
				detailItems.add(sameActionTimesDetailItem);
				// 如客户因猫抓狗咬2年内出险次数
				// dd
				ClaimFunctionMainDetailItem petActionTimesDetailItem = new ClaimFunctionMainDetailItem();
				petActionTimesDetailItem.setClaimFunctionDetailCode(ClaimConstant.CLAIM_FUNCTION_DETAIL_PET_ACTION_TIMES);
				petActionTimesDetailItem.setClaimFunctionValue(String.valueOf(petActionTimes));
				detailItems.add(petActionTimesDetailItem);
				
				// 处理赋值
                if(currentclaim.getClaimFunctionMainList()!= null && !currentclaim.getClaimFunctionMainList().isEmpty()) {
					ClaimFunctionMain targetFunctionMain = null;
					for (ClaimFunctionMain claimFunctionMain : currentclaim.getClaimFunctionMainList()) {
                        if (ClaimConstant.CLS_BUSS_ALL.equals(claimFunctionMain.getClainFunctionMainCode())) {
							targetFunctionMain = claimFunctionMain;
                            break;
                        }
                    }
					if (targetFunctionMain != null) {
						if (targetFunctionMain.getClaimFunctionMainDetailItemList() != null) {
							targetFunctionMain.getClaimFunctionMainDetailItemList().addAll(detailItems);
						} else {
							targetFunctionMain.setClaimFunctionMainDetailItemList(detailItems);
						}
					} else {
						ClaimFunctionMain claimFunctionMain = new ClaimFunctionMain();
						claimFunctionMain.setClainFunctionMainCode(ClaimConstant.CLS_BUSS_ALL);
						claimFunctionMain.setClaimFunctionMainDetailItemList(detailItems);
						currentclaim.getClaimFunctionMainList().add(claimFunctionMain);
					}
                } else {
					List<ClaimFunctionMain> claimFunctionMainList = new ArrayList<>();
					ClaimFunctionMain claimFunctionMain = new ClaimFunctionMain();
					claimFunctionMain.setClainFunctionMainCode(ClaimConstant.CLS_BUSS_ALL);
					claimFunctionMain.setClaimFunctionMainDetailItemList(detailItems);
					claimFunctionMainList.add(claimFunctionMain);
					currentclaim.setClaimFunctionMainList(claimFunctionMainList);
                }
            }
			
			
            // todo test
//            List<Payee> payeeList = currentclaim.getPayeeList();
//            for (Payee payee : payeeList) {
//                payee.setPayeeName("赵鄂");
//                payee.setBankAccname("赵鄂");
//            }
//            srvReqBizBody.setCurrentclaim(currentclaim);
//            currentclaim.setBenefitAmntAdjust(10001);
//            currentclaim.setBranchCode("86");
				
				
            //@invalid  执行审批自核发送数据的方法
            /*XmlHelper.classToXml(parametersReqHeader);
            XmlHelper.classToXml(parametersReqBody);*/
           port.execute(parametersReqHeader, parametersReqBody, parametersResHeader, parametersResBody);
           //4.调用规则自核接口，处理自核不通过原因
           //@invalid  获取发送数据的返回值 _execute_parametersResHeader.value
            status = parametersResBody.value.getBizBody().getOutputData().getResult().getStatus();
            List<RuleInfo> autoRuleInfos = parametersResBody.value.getBizBody().getOutputData().getResult().getRuleInfoList();
            if(autoRuleInfos.size()>0){
	            if(status.equals(ClaimConstant.CHECK_RULE_PASS_TWO)||status.equals(ClaimConstant.CHECK_RULE_PASS_THREE)){
	            	//@invalid 为了返回给ucc层判断是否为简易案件更改案件标识使用
//@invalid 	            	status = status + autoRuleInfos.get(0).getRuleId();
                    List<ClaimAutoRuleResultPO> claimAutoRuleResultPOs = new ArrayList<ClaimAutoRuleResultPO>();
	            	for (RuleInfo autoRuleInfo : autoRuleInfos) {
	            	    ClaimAutoRuleResultPO claimAutoRuleResultPO = new ClaimAutoRuleResultPO();
	                    claimAutoRuleResultPO.setCaseId(claimCaseBO.getCaseId());
	                    claimAutoRuleResultPO.setRuleStatus(ClaimConstant.STRING_TWO);//@invalid 1 通过 2 未通过
	                    claimAutoRuleResultPO.setTypeCode("01");
	            		logger.debug("********************************开始存简易自核未通过原因咯*******************************");
	            		if(autoRuleInfo.getRuleId()!=null&&autoRuleInfo.getRuleMessage()!=null&&autoRuleInfo.getRuleSetName()!=null){
							claimAutoRuleResultPO.setRuleCode(autoRuleInfo.getRuleId());
							claimAutoRuleResultPO.setRuleDesc(autoRuleInfo.getRuleMessage());
							claimAutoRuleResultPO.setRuleSetName(autoRuleInfo.getRuleSetName());
//@invalid 							claimAutoRuleResultPO.setRuleParam(autoRuleInfo.getRuleParameter());
							claimAutoRuleResultPOs.add(claimAutoRuleResultPO);
	            		}
					}
	             	if(claimAutoRuleResultPOs.size()>0){
	            		claimAutoRuleResultDao.batchSaveClaimAutoRuleResult(claimAutoRuleResultPOs);
	            	}
            	}
            }
            //@invalid 处理模型规则返回的模型风险,模型风险为true 才返回
            List<ClaimModelList> claimModelList = parametersResBody.value.getBizBody().getOutputData().getResult().getClaimModelList();
            //@invalid ClaimModelList对象 	例赔案为逆风险，其中只有保项逆风险的数据
            if(claimModelList.size()>0){
            	ClaimCasePO claimCasePO = new ClaimCasePO();
            	claimCasePO.setCaseId(claimCaseBO.getCaseId());
            	ClaimCasePO findClaimCaseByCaseId = claimCaseDao.findClaimCaseByCaseId(claimCasePO);
            	//@invalid 记录所有修改的保项风险表数据
            	List<ClaimRiskLevelLiabPO> claimRiskLevelLiabPOList = new ArrayList<ClaimRiskLevelLiabPO>();
            	List<ClaimCheatAtlasPO> claimCheatAtlasPOList = new ArrayList<ClaimCheatAtlasPO>();
            	boolean claimRiskNameTwo = true;
            	logger.debug("********************************开始处理规则返回的模型风险*******************************");
            	for (ClaimModelList claimModelList2 : claimModelList) {
            		//@invalid 保项模型数据 逆选择
            		List<ModelList1> modelList = claimModelList2.getModelList();
            		if(findClaimCaseByCaseId.getCaseId()!=null&&claimModelList2.getModelName().equals(ClaimConstant.CLAIM_RISK_MODE_ONE)){
            			//@invalid 逆选择风险等级 赔案层只存等级
            			findClaimCaseByCaseId.setInverseRiskLevel(modelLevelChange(claimModelList2.getClaimModelLevel()));
            			for (ModelList1 modelList1 : modelList) {
            				//@invalid 模型引擎（评分）落库，规则返回更新分数，等级  逆风险
            				ClaimRiskLevelLiabPO claimRiskLevelLiabPO = new ClaimRiskLevelLiabPO();
            				claimRiskLevelLiabPO.setCaseId(claimCaseBO.getCaseId());//@invalid 赔案id
            				claimRiskLevelLiabPO.setPolicyCode(modelList1.getPolicyApplyCode());//@invalid 保单号
            				claimRiskLevelLiabPO.setLiabCode(modelList1.getLgCode());//@invalid 责任编码
            				claimRiskLevelLiabPO.setRiskFraction(new BigDecimal(modelList1.getModelScore()));//@invalid 校验俩个保单年度
            				List<ClaimRiskLevelLiabPO> findAllClaimRiskLevelLiab = claimRiskLevelLiabDao.findAllClaimRiskLevelLiab(claimRiskLevelLiabPO);
            				for (ClaimRiskLevelLiabPO claimRiskLevelLiabPO2 : findAllClaimRiskLevelLiab) {
            					claimRiskLevelLiabPO2.setRiskLevelLiab(modelList1.getModelLevel());//@invalid 保项风险等级
            					claimRiskLevelLiabPO2.setRiskFraction(new BigDecimal(modelList1.getModelScore()));//@invalid 模型黑样本分数
            					claimRiskLevelLiabPOList.add(claimRiskLevelLiabPO2);
            				}
            			}
            		}
            		//@invalid 虚假发票风险等级 赔案层只存等级
            		if(findClaimCaseByCaseId.getCaseId()!=null&&claimModelList2.getModelName().equals(ClaimConstant.CLAIM_RISK_MODE_TWO)){
            			//@invalid 虚假发票风险等级 赔案层只存等级
            			findClaimCaseByCaseId.setInvoiceRiskLevel(modelLevelChange(claimModelList2.getClaimModelLevel()));
            			if(claimRiskNameTwo){
            				//@invalid 模型引擎（评分）落库，规则返回更新分数，等级   虚假发票
            				ClaimRiskLevelLiabPO claimRiskLevelLiabPO = new ClaimRiskLevelLiabPO();
            				claimRiskLevelLiabPO.setCaseId(claimCaseBO.getCaseId());//@invalid 赔案id
            				claimRiskLevelLiabPO.setRiskName(ClaimConstant.CLAIM_RISK_MODE_TWO_STR);//@invalid 虚假发票
            				List<ClaimRiskLevelLiabPO> findAllClaimRiskLevelLiab = claimRiskLevelLiabDao.findAllClaimRiskLevelLiab(claimRiskLevelLiabPO);
            				if(findAllClaimRiskLevelLiab.size()>0){
            					findAllClaimRiskLevelLiab.get(0).setRiskLevelLiab(claimModelList2.getClaimModelLevel());//@invalid 虚假发票风险等级
            					findAllClaimRiskLevelLiab.get(0).setRiskFraction(new BigDecimal(claimModelList2.getModelScore()));//@invalid 模型黑样本分数
            					ClaimRiskLevelLiabPO claimRiskLevelLiabPO2 = findAllClaimRiskLevelLiab.get(0);
            					claimRiskLevelLiabPOList.add(claimRiskLevelLiabPO2);
            					claimRiskNameTwo = false;
            				}
            			}
            		}
            		//@invalid 图谱等级风险列表
            		List<ImageList> imageList = claimModelList2.getImageList();
            		if(findClaimCaseByCaseId.getCaseId()!=null && 
            				(ClaimConstant.DANGER_TYPE_THREE.equals(claimModelList2.getModelName()) || ClaimConstant.DANGER_TYPE_FOUR.equals(claimModelList2.getModelName()))){
            			//@invalid 代理人层/客户层风险聚集等级 赔案层只存等级
            			if(ClaimConstant.DANGER_TYPE_THREE.equals(claimModelList2.getModelName())){
            				findClaimCaseByCaseId.setAgentRiskLevel(modelLevelChange(claimModelList2.getClaimModelLevel()));
            			}else if(ClaimConstant.DANGER_TYPE_FOUR.equals(claimModelList2.getModelName())){
            				findClaimCaseByCaseId.setCustomerRiskLevel(modelLevelChange(claimModelList2.getClaimModelLevel()));
            			}
            			for (ImageList imageList1 : imageList) {
            				//@invalid 图谱等级落库，规则返回更新类型，等级  
            				ClaimCheatAtlasPO cheatAtlasPO = new ClaimCheatAtlasPO();
            				cheatAtlasPO.setCaseNo(findClaimCaseByCaseId.getCaseNo());//@invalid 赔案号
            				cheatAtlasPO.setPolicyCode(imageList1.getPolicyApplyCode());//@invalid 保单号
            				cheatAtlasPO.setFrmsRuleCode(imageList1.getRuleRiskId());//@invalid 规则代码
            				List<ClaimCheatAtlasPO> cheatAtlasPOs = claimCheatAtlasDao.findAllClaimCheatAtlas(cheatAtlasPO);
            				for (ClaimCheatAtlasPO claimCheatAtlasPO : cheatAtlasPOs) {
            					claimCheatAtlasPO.setDangerType(imageList1.getImageName());//@invalid 图谱类型
            					claimCheatAtlasPO.setDangerLevel(imageList1.getImageLevel());//@invalid 规则层风险等级
            					claimCheatAtlasPO.setFrmsRuleScore(imageList1.getImageScore());//@invalid 模型黑样本分数
            					claimCheatAtlasPOList.add(claimCheatAtlasPO);
							}
            			}
            		}
				}
            	//@invalid 修改记录保项风险数据
            	if(claimRiskLevelLiabPOList.size()>0){
            		claimRiskLevelLiabDao.batchUpdateClaimRiskLevelLiab(claimRiskLevelLiabPOList);
            	}
            	if(claimCheatAtlasPOList.size() > 0){
            		claimCheatAtlasDao.batchUpdateClaimCheatAtlas(claimCheatAtlasPOList);
            	}
            	//@invalid 整案风险等级
            	if(parametersResBody.value.getBizBody().getOutputData().getResult().getAllModelLevel()!=null
            			&&!ClaimConstant.NULL_STR.equals(parametersResBody.value.getBizBody().getOutputData().getResult().getAllModelLevel())){
            		findClaimCaseByCaseId.setCaseRiskLevel(modelLevelChange(parametersResBody.value.getBizBody().getOutputData().getResult().getAllModelLevel()));//@invalid 虚假发票风险等级 赔案层只存等级
            		//@invalid 53418 “代理人层风险聚集” “客户层风险聚集”默认为低风险,“大案预警标识”默认为否
//            		findClaimCaseByCaseId.setAgentRiskLevel("低");
//            		findClaimCaseByCaseId.setCustomerRiskLevel("低");
            		findClaimCaseByCaseId.setEarlyWarning(ClaimConstant.BIGDECIMALNO);
            	}
            	if(findClaimCaseByCaseId.getCaseId()!=null){
            		claimCaseDao.updateClaimCase(findClaimCaseByCaseId);
            	}
            	logger.debug("********************************处理规则返回的模型风险结束*******************************");
            	
            }
            //@invalid 低风险参考值
            String lowLimite1 = parametersResBody.value.getBizBody().getOutputData().getResult().getLowLimite1();
            String lowLimite2 = parametersResBody.value.getBizBody().getOutputData().getResult().getLowLimite2();
            String lowLimite3 = parametersResBody.value.getBizBody().getOutputData().getResult().getLowLimite3();
            String lowLimite4 = parametersResBody.value.getBizBody().getOutputData().getResult().getLowLimite4();
            //@invalid 根据赔案id和风险类型查看是否已存在低风险参考值，有的话先删除然后再插入
            if(lowLimite1 != null && !"".equals(lowLimite1)){
            	ClaimRiskLownumPO claimRiskLownumPO = new ClaimRiskLownumPO();
                claimRiskLownumPO.setCaseId(claimCaseBO.getCaseId());
            	claimRiskLownumPO.setDangerType(ClaimConstant.DANGER_TYPE_ONE);
            	List<ClaimRiskLownumPO> claimRiskLownumPOs = claimRiskLownumDao.findAllClaimRiskLownum(claimRiskLownumPO);
            	if(claimRiskLownumPOs.size() > 0){
            		claimRiskLownumDao.batchDeleteClaimRiskLownum(claimRiskLownumPOs);
            	}
            	claimRiskLownumPO.setDangerNum(lowLimite1);
            	claimRiskLownumDao.addClaimRiskLownum(claimRiskLownumPO);
            }
            if(lowLimite2 != null && !"".equals(lowLimite2)){
            	ClaimRiskLownumPO claimRiskLownumPO = new ClaimRiskLownumPO();
                claimRiskLownumPO.setCaseId(claimCaseBO.getCaseId());
            	claimRiskLownumPO.setDangerType(ClaimConstant.DANGER_TYPE_TWO);
            	List<ClaimRiskLownumPO> claimRiskLownumPOs = claimRiskLownumDao.findAllClaimRiskLownum(claimRiskLownumPO);
            	if(claimRiskLownumPOs.size() > 0){
            		claimRiskLownumDao.batchDeleteClaimRiskLownum(claimRiskLownumPOs);
            	}
            	claimRiskLownumPO.setDangerNum(lowLimite2);
            	claimRiskLownumDao.addClaimRiskLownum(claimRiskLownumPO);
            }
            if(lowLimite3 != null && !"".equals(lowLimite3)){
            	ClaimRiskLownumPO claimRiskLownumPO = new ClaimRiskLownumPO();
                claimRiskLownumPO.setCaseId(claimCaseBO.getCaseId());
            	claimRiskLownumPO.setDangerType(ClaimConstant.DANGER_TYPE_THREE);
            	List<ClaimRiskLownumPO> claimRiskLownumPOs = claimRiskLownumDao.findAllClaimRiskLownum(claimRiskLownumPO);
            	if(claimRiskLownumPOs.size() > 0){
            		claimRiskLownumDao.batchDeleteClaimRiskLownum(claimRiskLownumPOs);
            	}
            	claimRiskLownumPO.setDangerNum(lowLimite3);
            	claimRiskLownumDao.addClaimRiskLownum(claimRiskLownumPO);
            }
            if(lowLimite4 != null && !"".equals(lowLimite4)){
            	ClaimRiskLownumPO claimRiskLownumPO = new ClaimRiskLownumPO();
                claimRiskLownumPO.setCaseId(claimCaseBO.getCaseId());
            	claimRiskLownumPO.setDangerType(ClaimConstant.DANGER_TYPE_FOUR);
            	List<ClaimRiskLownumPO> claimRiskLownumPOs = claimRiskLownumDao.findAllClaimRiskLownum(claimRiskLownumPO);
            	if(claimRiskLownumPOs.size() > 0){
            		claimRiskLownumDao.batchDeleteClaimRiskLownum(claimRiskLownumPOs);
            	}
            	claimRiskLownumPO.setDangerNum(lowLimite4);
            	claimRiskLownumDao.addClaimRiskLownum(claimRiskLownumPO);
            }
        	}
        } catch (BizException e) {
            e.getStackTrace();
            throw new BizException("系统异常");
        } catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new BizException("系统异常");
		}
        logger.info("***************************status返回值="+status+"*********************************");
        

        
        return status;
    }
    
    /**
     * @description 风险等级规则返回码值，进行转换 01 02 03 高 中 低
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param modelLevel 规则返回的码值
     * @return 转换后的值
    */
    public String modelLevelChange(String modelLevel){
    	//风险等级规则返回码值，进行转换 01 02 03 高 中 低
    	if(modelLevel!=null&&ClaimConstant.NULL_STR.equals(modelLevel)&&modelLevel.equals(ClaimConstant.MODEL_LEVEL_ONE)){
    		modelLevel = "高";
    	}else if(modelLevel!=null&&ClaimConstant.NULL_STR.equals(modelLevel)&&modelLevel.equals(ClaimConstant.MODEL_LEVEL_TWO)){
    		modelLevel = "中";
    	}else if(modelLevel!=null&&ClaimConstant.NULL_STR.equals(modelLevel)&&modelLevel.equals(ClaimConstant.MODEL_LEVEL_THREE)){
    		modelLevel = "低";
    	}
		return modelLevel;
    	
    }
    
    
    /**
     * @description 需求变更34482 关于在案件处理过程增加重复案件校验的优化
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案信息
     * @return  校验结果
    */
    public String checkRepeatedCase(ClaimCaseBO claimCaseBO){
        
        //1.查询医疗账单信息
    	//2.按照医疗账单进行规则校验
    	//3.记录重复账单备注
        if(claimCaseBO.getCaseId()==null){
            return "";
        }
        //@invalid  重复案件校验标识 true 重复 false 未重复
        String repeatedCase = ""; 
        String claimTypeStr = "";
        
       //@invalid 既有医疗又有非医疗的情况，视为理赔类型为医疗
        ClaimSubCasePO claimSubCasePO =new ClaimSubCasePO();
        claimSubCasePO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimSubCasePO> claimSubCasePOs= claimSubCaseDao.findAllClaimSubCase( claimSubCasePO);
        ClaimCasePO claimCaseInsuredIdPO = new ClaimCasePO();
        claimCaseInsuredIdPO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimCasePO> findAllClaimCase2 = claimCaseDao.findAllClaimCase(claimCaseInsuredIdPO);
        if(findAllClaimCase2.size()>0){
        	claimCaseBO.setInsuredId(findAllClaimCase2.get(0).getInsuredId());
        }
        boolean claimTypeMedical=false;
        for (ClaimSubCasePO claimSubCasePO2 : claimSubCasePOs){
        	
        	if(ClaimConstant.CLAIM_TYPE_ONG.equals(claimSubCasePO2.getClaimType()) ){//@invalid 身故
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_ONG + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_TWO.equals(claimSubCasePO2.getClaimType()) ){//@invalid 伤残
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_TWO + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_THREE.equals(claimSubCasePO2.getClaimType()) ){//@invalid 重大疾病
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_THREE + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_FOUR.equals(claimSubCasePO2.getClaimType()) ){//@invalid 高残
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_FOUR + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_SIX.equals(claimSubCasePO2.getClaimType()) ){//@invalid 一般失能
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_SIX + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_SEVEN.equals(claimSubCasePO2.getClaimType()) ){//@invalid 重度失能
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_SEVEN + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_EIGHT.equals(claimSubCasePO2.getClaimType()) ){//@invalid 医疗
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_EIGHT + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_TEN.equals(claimSubCasePO2.getClaimType()) ){//@invalid 特种疾病
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_TEN + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_ELEVEN.equals(claimSubCasePO2.getClaimType()) ){//@invalid 豁免
        		claimTypeStr = claimTypeStr + ClaimConstant.CLAIM_TYPE_ELEVEN + ",";
        	}
            if(ClaimConstant.CLAIM_TYPE_EIGHT.equals(claimSubCasePO2.getClaimType()) ){
                claimTypeMedical=true;
            }
        } 
        
        
       if(claimTypeMedical){//@invalid 医疗类案件
    	   boolean isRepeat = false;
           //@invalid 查询本赔案的账单列表
           ClaimBillPO claimBillPO = new ClaimBillPO();
           claimBillPO.setCaseId(claimCaseBO.getCaseId());
           List<ClaimBillPO> claimBillPOs = claimBillDao.findClaimBillOtherByCaseId(claimBillPO);
           
            
           
         //@invalid 调用老核心接口,超过30秒未回复则不再继续等待,直接用新核心的数据做校验
    	   //@invalid 定义接口入参
    	   final com.nci.tunan.clm.BillQuerySrvPortType.service.bd.InputData inputData = new com.nci.tunan.clm.BillQuerySrvPortType.service.bd.InputData();
    	   ClaimCasePO claimCasePO = new ClaimCasePO();
    	   claimCasePO.setCaseId(claimCaseBO.getCaseId());
    	   List<ClaimCasePO> findAllClaimCase = claimCaseDao.findAllClaimCase(claimCasePO);
    	   //@invalid 新核心出险人信息，出险原因入参
    	   if(findAllClaimCase.size()>0){
    		   CustomerPO customerPO = new CustomerPO();
    		   customerPO.setCustomerId(findAllClaimCase.get(0).getInsuredId());
    		   List<CustomerPO> findAllCustomer = customerDao.findAllCustomer(customerPO);
    		   if(findAllCustomer.size()>0){
    			   inputData.setName(findAllCustomer.get(0).getCustomerName());
    			   if(findAllCustomer.get(0).getCustomerGender().compareTo(ClaimConstant.SEX_MALE)==0){
    				   inputData.setSex(ClaimConstant.SEX_MALE.toString());
    			   }else if(findAllCustomer.get(0).getCustomerGender().compareTo(ClaimConstant.SEX_FEMALE)==0){
    				   inputData.setSex(ClaimConstant.SEX_FEMALE.toString());
    			   }else{
    				   inputData.setSex(ClaimConstant.SEX_OTHER.toString());
    			   }
    			   inputData.setBirthday(DateUtilsEx.formatToString(findAllCustomer.get(0).getCustomerBirthday(), ClaimConstant.DEFAULT_DATE_FORMAT_STR));
    			   inputData.setIdType(findAllCustomer.get(0).getCustomerCertType());
    			   inputData.setIdNo(findAllCustomer.get(0).getCustomerCertiCode());
    			   inputData.setReasonCode(claimTypeStr.substring(0, claimTypeStr.length()-1));
    		   }
    	   }
    	   //@invalid 核心账单信息入参
    	   BillList billList = new BillList();
    	   int billCount = 0;
    	   List<Bill> billLists = billList.getBill();
    	   for (ClaimBillPO claimBillPO2 : claimBillPOs) {
    		   billCount++;
    		   Bill bill = new Bill();
    		   bill.setMainFeeNo(claimBillPO2.getBillNo());
    		   bill.setFeeItemType(claimBillPO2.getTreatType());
    		   bill.setStartDate(DateUtilsEx.date2String(claimBillPO2.getTreatStart(), ClaimConstant.DEFAULT_DATE_FORMAT_STR));
    		   bill.setHospitalCode(claimBillPO2.getHospitalCode());
    		   billLists.add(bill);
    		   
    	   }
    	   billList.setBillCount(String.valueOf(billCount));
    	   inputData.setBillList(billList);
    	   com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData outPutData = null;
    	   boolean oldCoreBillFlag = true;
    	   if(oldCoreBillFlag){
    		   //@invalid 调老核心重复账单接口
    		   ExecutorService executorService = Executors.newFixedThreadPool(5);
    		   Future<com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData> future = executorService.submit(new Callable<com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData>(){
    			   
    			   @Override
    			   public com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData call() throws Exception {
    				   com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData queryBillQuerySrvPortType = queryBillQuerySrvPortType(inputData);
    				   return queryBillQuerySrvPortType;
    			   }
    			   
    		   });
    		   try {
    			   //@invalid 只等待老核心接口30秒//@invalid 超时后不在处理老核心数据，直接返回
    			   outPutData = (com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData) future.get(ClaimConstant.THIRTY, TimeUnit.SECONDS);
//    			   outPutData = (com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData) future.get(ClaimConstant.ONE, TimeUnit.MILLISECONDS);
    		   } catch (TimeoutException e) {
    			   logger.debug("老核心重复账单接口返回超时,赔案号为：" + findAllClaimCase.get(0).getCaseNo());
    		   } catch (InterruptedException e) {
    			   e.printStackTrace();
    		   } catch (ExecutionException e) {
    			   e.printStackTrace();
    		   }finally{
    			   //@invalid 关闭当前线程线程池
    			   if (executorService != null) {
    				   executorService.shutdown();
    			   }
    		   }
    	   }
    	   //@invalid ruleId对应关系
    	   //@invalid 0无重复账单
    	   //@invalid 1触发“该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核”规则
    	   //@invalid 2触发“该客户【帐单号】下的于【开始日期YYYY-MM-DD】发生的门诊发票已经超过30张（其他赔案包括【赔案号1】、【赔案号2】…），请关注审核”规则
    	   //@invalid 3触发“该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。”规则
    	   //@invalid 4触发“该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。”规则
    	   //@invalid 5【当前赔案号】该客户【帐单号】与本人既往赔案【赔案号】下存在相同账单号发票，请关注审核。
    	   //当前赔案【帐单号】与同一出险人的既往赔案【帐单号】相同时视为重复发票号重复赔案
    	   //【触发本人既往赔案】显示当前赔案号
    	   //@invalid 保存已经校验的重复账单号（底下新核心判断重复账单的不在处理，老核心outPutData正常返回数据本次账单的数据已经处理新核心数据）
    	   List<String> billNoOldStr= new ArrayList<String>();
    	   if(outPutData!=null){
    		   for (Rule rule : outPutData.getRuleList().getRule()) {
    			   
    			 //@invalid 1.该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核
                   String billSameCases = "";
                   
                   //@invalid 2该客户【帐单号】下的于【开始日期YYYY-MM-DD】发生的门诊发票已经超过30张（其他赔案包括【赔案号1】、【赔案号2】…）请关注审核
                   int caseCount = 0;
                   String insuredSameCases = "";
                   //@invalid 3该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。
                   String billTreatSame = ""; 
                   //@invalid 4该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。
                   int count3 = 1;
             	   String billTreatDif = "";
             	//@invalid 5【触发本人既往赔案】该客户【帐单号】与本人既往赔案【赔案号】下存在相同账单号发票，请关注审核
             	   String billTreatDif5 = "";
    			   
        		   //@invalid 1触发“该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核”规则
        		   if("1".equals(rule.getRuleId())){
        			   for (ClaimBillPO claimBill : claimBillPOs) {
        				 //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        				   if (claimBill.getTreatType().equals(ClaimConstant.NO)&&rule.getMainFeeNo().equals(claimBill.getBillNo())) { //@invalid 门诊账单
        	        		   //@invalid 查询新核心相同的门诊账单赔案
        	        		   ClaimCasePO claimCasePO1 = new ClaimCasePO();
        	        		   claimCasePO1.setCaseId(claimCaseBO.getCaseId());
        	        		   if(claimBill.getBillType().compareTo(ClaimConstant.CLM_BILL_ONE)==ClaimConstant.ZERO){
        	        			   claimCasePO1.getData().put("bill_type", claimBill.getBillType());
        	        		   }
        	        		   claimCasePO1.getData().put("bill_no", claimBill.getBillNo());
        	        		   List<ClaimCasePO> claimCasePOs = claimCaseDao.findCaseNoByBillNo(claimCasePO1);
        	        		   //@invalid 医院为未维护医院特殊校验同一人
        	        		   if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
        	        			 //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        	        			   for (ClaimCasePO claimCase : claimCasePOs) {
        	        				   if(claimCase.getInsuredId().compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
        	        					   billSameCases = billSameCases + "["+claimCase.getCaseNo()+"]" + "、";
        	        				   }
        	        			   }
        	        		   }else{
        	        			   //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        	        			   for (ClaimCasePO claimCase : claimCasePOs) {
        	        				   billSameCases = billSameCases + "["+claimCase.getCaseNo()+"]" + "、";
        	        			   }
        	        			   //@invalid 拼接老核心相同账单号的赔案，在老核心接口返回的循环中
        	        		   }
        	        	   }
        			   }
        			   
        			   
        			   if(rule.getPastClaims().getPastClaim().size()>0){
        				   //@invalid 拼接老核心赔案
        				   for (PastClaim pastClaim2 : rule.getPastClaims().getPastClaim()) {
        					   billSameCases = billSameCases + "["+pastClaim2.getCaseNo()+"]" + "、";
        				   }
        			   }
        			   //@invalid 处理备注问题件
        			   //@invalid #TC6232，理赔总集缺陷6232，理赔总集CheckRuleVerifyServiceImpl字符串比较未使用equals()
        			   if(rule.getMainFeeNo() != null && !"".equals(rule.getMainFeeNo())){
        			   //@invalid if(rule.getMainFeeNo()!=""&&rule.getMainFeeNo()!=null){
        				   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
        				   claimMemoOnePO.setMemoType("4");
        				   claimMemoOnePO.setMemoOption("403");
        				   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
        				   claimMemoOnePO.setMemoContent("【"+ rule.getMainFeeNo()+ "】");
        				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
//@invalid         				   if(findBillNoClaimMemo.getMemoId()!=null){
//@invalid         					   String memoContent = "该赔案的账单号【"+rule.getMainFeeNo()+"】与既往赔案【"+billSameCases.substring(0, billSameCases.length()-1)+"】的账单号相同，请关注审核。";
//@invalid         					   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
//@invalid     	        			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
//@invalid     	        			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
//@invalid     	        			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
//@invalid     	        			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
//@invalid         					   claimMemoDao.updateClaimMemo(claimMemoPO);
//@invalid         				   }else{//@invalid 新核心数据没有但是老核心有此规则
        					   //@invalid 备注/问题件类型为：4-其他备注类，备注问题件选项”疑似重复帐单”，备注内容：该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核。
        	        		   if (!billSameCases.equals("")&&findBillNoClaimMemoOne.size()>0) {//@invalid 如果存在，则记录备注信息
        	        			   isRepeat = true;
        	        			   String memoContent = "该赔案的账单号【"+rule.getMainFeeNo()+"】与既往赔案【"+billSameCases.substring(0, billSameCases.length()-1)+"】的账单号相同，请关注审核。";
        	        			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
        	        			   claimMemoPO1.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
        	        			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
        	        			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
        	        			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
        	        			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
        	        			   claimMemoDao.updateClaimMemo(claimMemoPO1);
        	        			   billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
        	        		   }else if(!billSameCases.equals("")){
        	        			   isRepeat = true;
        	        			   String memoContent = "该赔案的账单号【"+rule.getMainFeeNo()+"】与既往赔案【"+billSameCases.substring(0, billSameCases.length()-1)+"】的账单号相同，请关注审核。";
        	        			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
        	        			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
        	        			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
        	        			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
        	        			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
        	        			   claimMemoDao.addClaimMemo(claimMemoPO1);
        	        			   billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
        	        		   }
//@invalid         				   }
        			   }
        		   }
        		 //@invalid 2触发“该客户【帐单号】下的于【开始日期YYYY-MM-DD】发生的门诊发票已经超过30张（其他赔案包括【赔案号1】、【赔案号2】…），请关注审核”规则
        		   if("2".equals(rule.getRuleId())){
        			   
        			   for (ClaimBillPO claimBill : claimBillPOs) {
        				 //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        				   if (claimBill.getTreatType().equals(ClaimConstant.NO)&&rule.getMainFeeNo().equals(claimBill.getBillNo())) { //@invalid 门诊账单
        					 //@invalid 查询新核心符合条件的既往赔案
                			   ClaimCasePO claimCase = new ClaimCasePO();
                			   claimCase.setCaseId(claimCaseBO.getCaseId());
                			   claimCase.getData().put("treat_start", claimBill.getTreatStart());
                			   if(claimBill.getBillType().compareTo(ClaimConstant.CLM_BILL_ONE)==ClaimConstant.ZERO){
                				   claimCase.getData().put("bill_type", claimBill.getBillType());
                    		   }
                			   claimCase.setInsuredId(claimCaseBO.getInsuredId());
                			   //@invalid 查询本次赔案的客户信息，判断四要素（姓名、性别、出生日期、证件号码，且证件号码符合身份证号校验规则）
                			   CustomerPO customerPO = new CustomerPO();
                			   customerPO.setCustomerId(claimCaseBO.getInsuredId());
                			   CustomerPO findCustomerByCustomerId = customerDao.findCustomerByCustomerId(customerPO);
                			   //@invalid 查询同一出险人们发票是否超过30,四要素通过条件查询防止无客户四要素信息查询数据过多。
                			   if(StringUtils.isNotEmpty(findCustomerByCustomerId.getCustomerName())){
                				   claimCase.set("customer_name",findCustomerByCustomerId.getCustomerName());
                			   }
                			   if(findCustomerByCustomerId.getCustomerGender() != null){
                				   claimCase.set("customer_gender",findCustomerByCustomerId.getCustomerGender());
                			   }
                			   if(findCustomerByCustomerId.getCustomerBirthday() != null){
                				   claimCase.set("customer_birthday",findCustomerByCustomerId.getCustomerBirthday());
                			   }
                			   if(StringUtils.isNotEmpty(findCustomerByCustomerId.getCustomerCertiCode())){
                				   claimCase.set("customer_certi_code",findCustomerByCustomerId.getCustomerCertiCode());
                			   }
                			   List<ClaimCasePO> claimCases = claimCaseDao.findCasesByBillTreat(claimCase);
                			   
                			   if (claimCases.size() > 0) {
                				   for (ClaimCasePO casepo : claimCases) {
                					   //@invalid 判断是否同一人（四要素）
                					   CustomerPO customerNewPO = new CustomerPO();
                					   if(casepo.getData().get("customer_name")!=null){//客户姓名
                						   customerNewPO.setCustomerName(casepo.getData().get("customer_name").toString());
                					   }
                					   if(casepo.getData().get("customer_id")!=null){//客户id
                						   customerNewPO.setCustomerId((BigDecimal)casepo.getData().get("customer_id"));
                					   }
                					   if(casepo.getData().get("customer_gender")!=null){
                						   customerNewPO.setCustomerGender((BigDecimal)casepo.getData().get("customer_gender"));
                					   }
                					   if(casepo.getData().get("customer_birthday")!=null){
                						   customerNewPO.setCustomerBirthday((Date)casepo.getData().get("customer_birthday"));
                					   }
                					   if(casepo.getData().get("customer_certi_code")!=null){
                						   customerNewPO.setCustomerCertiCode(casepo.getData().get("customer_certi_code").toString());
                					   }
                					   boolean fourElements = fourElements(findCustomerByCustomerId,customerNewPO); 
                					   if(casepo.getData().get("billno_count")!=null&&fourElements){
                						   caseCount = caseCount + Integer.valueOf(casepo.getData().get("billno_count").toString());
                						   insuredSameCases = insuredSameCases + "["+casepo.getCaseNo()+"]" + "、";
                					   }
                				   }
                			   }
        	        	   }
        			   }
        			   
        			   if(rule.getPastClaims().getPastClaim().size()>0){
        				   //@invalid 拼接老核心赔案
        				   for (PastClaim pastClaim2 : rule.getPastClaims().getPastClaim()) {
        					   caseCount = caseCount + Integer.valueOf(pastClaim2.getMainFeenumber());
        					   insuredSameCases = insuredSameCases + "["+pastClaim2.getCaseNo()+"]" + "、";
        				   }
        			   }
        			   //@invalid 处理备注问题件
        			   //@invalid #TC6232，理赔总集缺陷6232，理赔总集CheckRuleVerifyServiceImpl字符串比较未使用equals()
        			   if(rule.getMainFeeNo()!=null && !"".equals(rule.getMainFeeNo())){
        			   //@invalid if(rule.getMainFeeNo()!=""&&rule.getMainFeeNo()!=null){
        				   String reatStartStr = "1";
        				   for (ClaimBillPO claimBill : claimBillPOs) {
        					   if(claimBill.getBillNo().equals(rule.getMainFeeNo())){
        						   logger.info("账单开始时间" +claimBill.getTreatStart());
//        						   reatStartStr = DateUtilsEx.formatToString(claimBill.getTreatStart(), ClaimConstant.DEFAULT_DATE_FORMAT_STR);
        						   break;
        					   }
        				   }
        					   //@invalid 备注/问题件类型为：4-其他备注类，备注问题件选项”疑似重复帐单”，备注内容：该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核。
        				   //@invalid 记录备注问题件
        				   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
        				   claimMemoOnePO.setMemoType("4");
        				   claimMemoOnePO.setMemoOption("403");
        				   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
        				   claimMemoOnePO.setMemoContent("【"+ rule.getMainFeeNo()+ "】");
        				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
	    				  if (caseCount > 30&&findBillNoClaimMemoOne.size()>0) {//@invalid 如果大于30张，则记录备注问题件(没写，则认为不包括本赔案)
	    					  isRepeat = true;
	    					  String memoContent = "该客户【"+rule.getMainFeeNo()+"】下的于【开始日期"+reatStartStr+"】发生的门诊发票已经超过30张（其他赔案包括"+insuredSameCases.substring(0, insuredSameCases.length()-1)+"），请关注审核。";
	    				      ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
	    				      claimMemoPO1.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
	    				      claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
	    				      claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
	    				      claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
	    				      claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
	    				      claimMemoDao.updateClaimMemo(claimMemoPO1);
	    				      billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
	    				  }else if(caseCount > 30){
	    					  isRepeat = true;
	    					  String memoContent = "该客户【"+rule.getMainFeeNo()+"】下的于【开始日期"+reatStartStr+"】发生的门诊发票已经超过30张（其他赔案包括"+insuredSameCases.substring(0, insuredSameCases.length()-1)+"），请关注审核。";
	    				      ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
	    				      claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
	    				      claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
	    				      claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
	    				      claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
	    				      claimMemoDao.addClaimMemo(claimMemoPO1);
	    				      billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
	    				  }
        			   }
        		   }
        		 //@invalid 3触发“该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。”规则
        		   if("3".equals(rule.getRuleId())){
        			   String reatStartStr = "";
        			   for (ClaimBillPO claimBill : claimBillPOs) {
    					   if(claimBill.getBillNo().equals(rule.getMainFeeNo())){
    						   reatStartStr = DateUtilsEx.formatToString(claimBill.getTreatStart(), ClaimConstant.DEFAULT_DATE_FORMAT_STR);
    						   break;
    					   }
    				   }

        			   //@invalid 拼接新核心赔案
        			   for (ClaimBillPO claimBill : claimBillPOs) {
        				 //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        				   if (!claimBill.getTreatType().equals(ClaimConstant.NO)&&rule.getMainFeeNo().equals(claimBill.getBillNo())) { //@invalid 住院账单
        					 //@invalid 1、账单号相同，开始日期相同、治疗医院代码相同、理赔类型相同 则记录：备注内容：该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。
        	        		   //@invalid 查询本赔案的理赔类型
        	        		   String claimTypes = "";
        	        		   for (ClaimSubCasePO claimSubCasePO2 : claimSubCasePOs){
        	        			   claimTypes = claimTypes + claimSubCasePO2.getClaimType() + ClaimConstant.COMMA;
        	        	       } 
        	        		   claimTypes =  claimTypes.substring(0, claimTypes.length()-ClaimConstant.ONE);
        	        		   ClaimCasePO casePO = new ClaimCasePO();
        	        		   casePO.setCaseId(claimCaseBO.getCaseId());
        	        		   casePO.getData().put("bill_no", claimBill.getBillNo());
        	        		   casePO.getData().put("treat_start", claimBill.getTreatStart());
        	        		   casePO.getData().put("hospital_code", claimBill.getHospitalCode());
        	        		   casePO.getData().put("claim_type", claimTypes);
        	        		   List<ClaimCasePO> casePOs = claimCaseDao.findCasesByBillOther(casePO);
        	        		   //@invalid 医院为未维护医院特殊校验同一人
        	        		   if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
        	        			   if (casePOs.size() > 0) {
        	        				   for (ClaimCasePO casepo : casePOs) {
        	        					   //@invalid 判断是否同一人
        	        					   if(casepo.getInsuredId().compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
        	        						   casepo.setCaseNo(casepo.getCaseNo()+ "、");
        	        						   billTreatSame = billTreatSame + casepo.getCaseNo();
        	        					   }
        	        				   }
        	        			   }  
        	        		   }else{
        	        			   if (casePOs.size() > 0) {
        	        				   for (ClaimCasePO casepo : casePOs) {
        	        					   casepo.setCaseNo(casepo.getCaseNo()+ "、");
        	        					   billTreatSame = billTreatSame + casepo.getCaseNo() ;
        	        				   }
        	        			   }
        	        		   }
        	        	   }
        			   }
        			   
        			   if(rule.getPastClaims().getPastClaim().size()>0){
        				   //@invalid 拼接老核心赔案
        				   for (PastClaim pastClaim2 : rule.getPastClaims().getPastClaim()) {
        					   pastClaim2.setCaseNo(pastClaim2.getCaseNo()+ "、");
        					   billTreatSame = billTreatSame + pastClaim2.getCaseNo() ;
        				   }
        			   }
        			   //@invalid 处理备注问题件
        			   //@invalid #TC6232，理赔总集缺陷6232，理赔总集CheckRuleVerifyServiceImpl字符串比较未使用equals()
        			   if(rule.getMainFeeNo() != null && !"".equals(rule.getMainFeeNo())){
        			   //@invalid if(rule.getMainFeeNo()!=""&&rule.getMainFeeNo()!=null){
        				   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
        				   claimMemoOnePO.setMemoType("4");
        				   claimMemoOnePO.setMemoOption("403");
        				   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
        				   claimMemoOnePO.setMemoContent("【"+ rule.getMainFeeNo()+ "】");
        				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
    					   //@invalid 记录备注
    	           		   if (!billTreatSame.equals("")&&findBillNoClaimMemoOne.size()>0) {
    	           			   if("、".equals(String.valueOf(billTreatSame.charAt(billTreatSame.length()-1)))) {
    	           				billTreatSame =  billTreatSame.substring(0,billTreatSame.length()-1);
    	           			   }
    	           			   isRepeat = true;
    	           			   String memoContent = "该赔案的收据编号【"+rule.getMainFeeNo()+"】、【"+reatStartStr+"】与既往赔案["+billTreatSame+"]相同，请关注审核。";
    	           			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
    	           			   claimMemoPO1.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
    	           			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
    	           			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
    	           			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
    	           			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
    	           			   claimMemoDao.updateClaimMemo(claimMemoPO1);
    	           			   billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
    	           		   }else if(!billTreatSame.equals("")){
    	           			if("、".equals(String.valueOf(billTreatSame.charAt(billTreatSame.length()-1)))) {
    	           				billTreatSame =  billTreatSame.substring(0,billTreatSame.length()-1);
    	           			   }
    	           			   isRepeat = true;
	  	           			   String memoContent = "该赔案的收据编号【"+rule.getMainFeeNo()+"】、【"+reatStartStr+"】与既往赔案["+billTreatSame+"]相同，请关注审核。";
	  	           			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
	  	           			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
	  	           			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
	  	           			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
	  	           			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
	  	           			   claimMemoDao.addClaimMemo(claimMemoPO1);
	  	           			   billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
    	           		   }
        			   }
        		   }
        		 //@invalid 4触发“该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。”规则
        		   if("4".equals(rule.getRuleId())){
        			   //@invalid 拼接新核心
        			   for (ClaimBillPO claimBill : claimBillPOs) {
        				 //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        				   if (!claimBill.getTreatType().equals(ClaimConstant.NO)&&rule.getMainFeeNo().equals(claimBill.getBillNo())) { //@invalid 住院账单
        					   	//@invalid (1)账单号出现大于等于三次(包括本次)、则记录
        	            		  //@invalid (2)账单号出现小于等于二次(包括本次),出险日期不同则记录；
        	            		  //@invalid                           出险日期相同并且是个险，则记录；
        	            		  //@invalid                                       团险，未录入B002-第三方给付并且给付类型是否是6（其他）或7(自费报销)，则记录
        	            		  //@invalid 备注内容：该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。
        	                	  //@invalid 查询账单号相同，开始日期均不同，治疗医院相同的赔案信息
        	                	  ClaimBillPO claimBillN = new ClaimBillPO();
        	                	  claimBillN.setBillNo(claimBill.getBillNo());
        	                	  claimBillN.setTreatStart(claimBill.getTreatStart());
        	                	  claimBillN.setHospitalCode(claimBill.getHospitalCode());
        	                	  claimBillN.setCaseId(claimBill.getCaseId());
        	                	  List<ClaimBillPO> claimBillNs = claimBillDao.findallClaimOtherNation(claimBillN);
        	                	  //@invalid 医院为未维护医院特殊校验同一人
        	                	  if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
        		        			   if (claimBillNs.size() > 0) {
        		        				   for (ClaimBillPO claimBil : claimBillNs) {
        		        					   //@invalid 判断是否同一人 
        		        					   if(claimBil.getData().get("billno_count")!=null&&claimBil.getData().get("insured_id")!=null
        		        							   &&new BigDecimal(claimBil.getData().get("insured_id").toString()).compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
        		        						   count3 = count3 + Integer.valueOf(claimBil.getData().get("billno_count").toString());
        			        					   billTreatDif =  billTreatDif + claimBil.getData().get("case_no").toString() + "、";
        		        					   }
        		        				   }
        		        			   }  
        		        		   }else{
        		        			   for (ClaimBillPO claimBil : claimBillNs) {
        		        				   if(claimBil.getData().get("billno_count")!=null){
        		        					   count3 = count3 + Integer.valueOf(claimBil.getData().get("billno_count").toString());
        		        					   billTreatDif =  billTreatDif + claimBil.getData().get("case_no").toString() + "、";
        		        				   }
        		        			   }
        		        		   }
        	                	  
        	        	   }
        			   }
        			   
        			   if(rule.getPastClaims().getPastClaim().size()>0){
        				   //@invalid 拼接老核心赔案
        				   for (PastClaim pastClaim2 : rule.getPastClaims().getPastClaim()) {
        					   count3 = count3 + Integer.valueOf(pastClaim2.getMainFeenumber());
        					   billTreatDif =  billTreatDif + pastClaim2.getCaseNo() + "、";
        				   }
        			   }
        			   //@invalid 处理备注问题件
        			   //@invalid #TC6232，理赔总集缺陷6232，理赔总集CheckRuleVerifyServiceImpl字符串比较未使用equals()
        			   if(rule.getMainFeeNo() != null && !"".equals(rule.getMainFeeNo())){
        			   //@invalid if(rule.getMainFeeNo()!=""&&rule.getMainFeeNo()!=null){
	    					 //@invalid 记录备注
        				   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
        				   claimMemoOnePO.setMemoType("4");
        				   claimMemoOnePO.setMemoOption("403");
        				   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
        				   claimMemoOnePO.setMemoContent("【"+ rule.getMainFeeNo()+ "】");
        				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
	                  		   if (count3  >= 3&&findBillNoClaimMemoOne.size()>0) {//@invalid 账单号出现大于等于三次(包括本次)，则记录以下备注
	               	  		  isRepeat = true;
	               	  		  String memoContent = "该赔案的收据编号【"+rule.getMainFeeNo()+"】与既往赔案["+billTreatDif.substring(0, billTreatDif.length()-1)+"]相同，请关注审核。";
	               		      ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
	               		      claimMemoPO1.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
	               		      claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
	               		      claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
	               		      claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
	               		      claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
	               		      claimMemoDao.updateClaimMemo(claimMemoPO1);
	               		      billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
	               	   		}else if(count3  >= 3){
	               	   			isRepeat = true;
		               	  		  String memoContent = "该赔案的收据编号【"+rule.getMainFeeNo()+"】与既往赔案["+billTreatDif.substring(0, billTreatDif.length()-1)+"]相同，请关注审核。";
		               		      ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
		               		      claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
		               		      claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
		               		      claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
		               		      claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
		               		      claimMemoDao.addClaimMemo(claimMemoPO1);
		               		      billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
	               	   		}else if(count3  <= 2 && (!ClaimConstant.NULL_STR.equals(billTreatDif))){
		           	   			if(rule.getPastClaims().getPastClaim().size()>0){//@invalid 查询医疗的出险时间
		           	   				ClaimSubCasePO claimSubCaseTwoPO = new ClaimSubCasePO();
			           	   			//@invalid 本次赔案
		           	   				claimSubCaseTwoPO.setCaseId(claimCaseBO.getCaseId());
		           	   				claimSubCaseTwoPO.setClaimType(ClaimConstant.CLAIM_TYPE_EIGHT);//@invalid 医疗
		           	   				List<ClaimSubCasePO> findAllClaimSubCase2 = claimSubCaseDao.findAllClaimSubCase(claimSubCaseTwoPO);
		           	   				//@invalid 查询是否录入第三方给付 并且给付类型是否是6（其他）或7(自费报销)
		           	   				boolean claimBillPaidFlag = false;
		           	   				ClaimBillPaidPO claimBillPaidPO = new ClaimBillPaidPO();
		           	   				claimBillPaidPO.setCaseId(findAllClaimSubCase2.get(0).getCaseId());
		           	   				List<ClaimBillPaidPO> findAllClaimBillPaid = claimBillPaidDao.findAllClaimBillPaid(claimBillPaidPO);
		           	   				for (ClaimBillPaidPO claimBillPaidPO2 : findAllClaimBillPaid) {
										if(claimBillPaidPO2.getPaidType().compareTo(ClaimConstant.EXPENSE_REIMBURSE_SIX)==ClaimConstant.ZERO
											||claimBillPaidPO2.getPaidType().compareTo(ClaimConstant.EXPENSE_REIMBURSE)==ClaimConstant.ZERO){
											claimBillPaidFlag = true;
											break;
										}
									}
		           	   				if(DateUtilsEx.formatToDate(rule.getPastClaims().getPastClaim().get(0).getAccDate(), ClaimConstant.DEFAULT_DATE_FORMAT_STR).compareTo(DateUtilsEx.formatDate(findAllClaimSubCase2.get(0).getClaimDate(), ClaimConstant.DEFAULT_DATE_FORMAT_STR))!=ClaimConstant.ZERO){
		           	   					isRepeat = true;
		           	   					if(findBillNoClaimMemoOne.size()>0){
		           	   						claimMemoDao.batchDeleteClaimMemo(findBillNoClaimMemoOne);
		           	   					}
			               	  		  String memoContent = "该赔案的收据编号【"+rule.getMainFeeNo()+"】与既往赔案["+billTreatDif.substring(0, billTreatDif.length()-1)+"]相同，请关注审核。";
			               		      ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
			               		      claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
			               		      claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
			               		      claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
			               		      claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
			               		      claimMemoDao.addClaimMemo(claimMemoPO1);
			               		      billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
		           	   				}else{
		           	   					//@invalid 是否是团险赔案
		           	   					String polType = "";
		           	   					if(rule.getPastClaims().getPastClaim().size()>0){
		           	   						polType = rule.getPastClaims().getPastClaim().get(0).getPolType();
		           	   					}
		           	   					if(!(ClaimConstant.NULL_STR).equals(polType)){
		           	   						if(polType.equals(ClaimConstant.PEO_CASE)){
		           	   						isRepeat = true;
			           	   					if(findBillNoClaimMemoOne.size()>0){
			           	   						claimMemoDao.batchDeleteClaimMemo(findBillNoClaimMemoOne);
			           	   					}
					               	  		  String memoContent = "该赔案的收据编号【"+rule.getMainFeeNo()+"】与既往赔案["+billTreatDif.substring(0, billTreatDif.length()-1)+"]相同，请关注审核。";
					               		      ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
					               		      claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
					               		      claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
					               		      claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
					               		      claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
					               		      claimMemoDao.addClaimMemo(claimMemoPO1);
					               		      billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
		           	   						}else{
		           	   							if(!claimBillPaidFlag){
		           	   							isRepeat = true;
			           	   						if(findBillNoClaimMemoOne.size()>0){
			           	   							claimMemoDao.batchDeleteClaimMemo(findBillNoClaimMemoOne);
				           	   					}
						               	  		  String memoContent = "该赔案的收据编号【"+rule.getMainFeeNo()+"】与既往赔案["+billTreatDif.substring(0, billTreatDif.length()-1)+"]相同，请关注审核。";
						               		      ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
						               		      claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
						               		      claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
						               		      claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
						               		      claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
						               		      claimMemoDao.addClaimMemo(claimMemoPO1);
						               		      billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
		           	   							}
		           	   						}
		           	   					}
		           	   				}
		           	   				
		           	   			}
	               	   		}
        			   }
        		   }
        		   //5【触发本人既往赔案】该客户【帐单号】与本人既往赔案【赔案号】下存在相同账单号发票，请关注审核
        		   //当前赔案【帐单号】与同一出险人的既往赔案【帐单号】相同时视为重复发票号重复赔案
             	   //String billTreatDif5 = "";
        		   if("5".equals(rule.getRuleId())){
        			   logger.info("团险重复账单接口ruleid=：" +rule.getRuleId());
        			   ClaimCasePO casePO = new ClaimCasePO();
        			   if(claimCaseBO.getCaseId()!=null) {
        				   casePO.setCaseId(claimCaseBO.getCaseId());
        				   casePO = claimCaseDao.findClaimCaseByCaseId(casePO);
        			   }
        			   if(rule.getPastClaims().getPastClaim().size()>0){
        				   //@invalid 拼接老核心赔案
        				   for (PastClaim pastClaim2 : rule.getPastClaims().getPastClaim()) {
        					   billTreatDif5 = billTreatDif5 + "["+pastClaim2.getCaseNo()+"]" + "、";
        				   }
        			   }
        			   logger.info("团险重复账单接口ruleid=5" +billTreatDif5);
        			   //@invalid 处理备注问题件
        			   if(rule.getMainFeeNo() != null && !"".equals(rule.getMainFeeNo())){
        				   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
        				   claimMemoOnePO.setMemoType("4");
        				   claimMemoOnePO.setMemoOption("403");
        				   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
        				   claimMemoOnePO.setMemoContent("【"+ rule.getMainFeeNo()+ "】");
        				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
        	        		   if (!billTreatDif5.equals("")&&findBillNoClaimMemoOne.size()>0) {//@invalid 如果存在，则记录备注信息
        	        			   isRepeat = true;
        	        			   String memoContent = "【"+casePO.getCaseNo()+"】该客户【"+rule.getMainFeeNo()+"】与本人既往赔案【"+billTreatDif5.substring(0, billTreatDif5.length()-1)+"】下存在相同账单号发票，请关注审核。";
        	        			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
        	        			   claimMemoPO1.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
        	        			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
        	        			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
        	        			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
        	        			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
        	        			   claimMemoDao.updateClaimMemo(claimMemoPO1);
        	        			   billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
        	        			   logger.info("团险重复账单接口ruleid：5,备注信息1：" +memoContent);
        	        		   }else if(!billTreatDif5.equals("")){
        	        			   isRepeat = true;
        	        			   String memoContent = "【"+casePO.getCaseNo()+"】该客户【"+rule.getMainFeeNo()+"】与本人既往赔案【"+billTreatDif5.substring(0, billTreatDif5.length()-1)+"】下存在相同账单号发票，请关注审核。";
        	        			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
        	        			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
        	        			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
        	        			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
        	        			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
        	        			   claimMemoDao.addClaimMemo(claimMemoPO1);
        	        			   billNoOldStr.add(rule.getMainFeeNo());//@invalid 添加已经处理的账单信息
        	        			   logger.info("团险重复账单接口ruleid=5,备注信息2：" +memoContent);
        	        			   
        	        		}
        			   }
        		   }
        		   
        	   }
    	   }
    	   //@invalid 只判断新核心重复（老核心30秒未返回或返回无重复）
		   for (ClaimBillPO claimBill : claimBillPOs) {
			   //@invalid 如果已经处理，不再处理
			   if(billNoOldStr.contains(claimBill.getBillNo())){
				   break;
			   }
	        	 //@invalid 1.该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核
	               String billSameCases = "";
	               
	               //@invalid 2该客户【帐单号】下的于【开始日期YYYY-MM-DD】发生的门诊发票已经超过30张（其他赔案包括【赔案号1】、【赔案号2】…）请关注审核
	               int caseCount = 0;
	               String insuredSameCases = "";
	               //@invalid 3该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。
	               String billTreatSame = ""; 
	               //@invalid 4该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。
	               int count3 = 1;
	         	   String billTreatDif = "";
	         	   //5【触发本人既往赔案】该客户【帐单号】与本人既往赔案【赔案号】下存在相同账单号发票，请关注审核
        		   //当前赔案【帐单号】与同一出险人的既往赔案【帐单号】相同时视为重复发票号重复赔案
	         	  String billSameCases5 = "";
	        	   if (claimBill.getTreatType().equals(ClaimConstant.NO)) { //@invalid 门诊账单
	        		   //@invalid 查询新核心相同的门诊账单赔案
	        		   ClaimCasePO claimCasePO1 = new ClaimCasePO();
	        		   claimCasePO1.setCaseId(claimCaseBO.getCaseId());
	        		   if(claimBill.getBillType().compareTo(ClaimConstant.CLM_BILL_ONE)==ClaimConstant.ZERO){
	        			   claimCasePO1.getData().put("bill_type", claimBill.getBillType());
	        		   }
	        		   claimCasePO1.getData().put("bill_no", claimBill.getBillNo());
	        		   List<ClaimCasePO> claimCasePOs = claimCaseDao.findCaseNoByBillNo(claimCasePO1);
	        		 //@invalid 医院为未维护医院特殊校验同一人
	        		   if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
	        			 //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
	        			   for (ClaimCasePO claimCase : claimCasePOs) {
	        				   if(claimCase.getInsuredId().compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
	        					   billSameCases = billSameCases + "["+claimCase.getCaseNo()+"]" + "、";
	        				   }
	        			   }
	        		   }else{
	        			   for (ClaimCasePO claimCase : claimCasePOs) {
	        				   billSameCases = billSameCases + "["+claimCase.getCaseNo()+"]" + "、";
	        			   }
	        			   //@invalid 拼接老核心相同账单号的赔案，在老核心接口返回的循环中
	        		   }
	        		 //@invalid 备注/问题件类型为：4-其他备注类，备注问题件选项”疑似重复帐单”，备注内容：该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核。
	        		   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
	        		   claimMemoOnePO.setMemoType("4");
	        		   claimMemoOnePO.setMemoOption("403");
	        		   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
	        		   claimMemoOnePO.setMemoContent("【"+ claimBill.getBillNo()+ "】");
    				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
	        		   if (!billSameCases.equals("")&&findBillNoClaimMemoOne.size()>0) {//@invalid 如果存在，则记录备注信息
	        			   isRepeat = true;
	        			   String memoContent = "该赔案的账单号【"+claimBill.getBillNo()+"】与既往赔案【"+billSameCases.substring(0, billSameCases.length()-1)+"】的账单号相同，请关注审核。";
	        			   ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	        			   claimMemoPO.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
	        			   claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	        			   claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	        			   claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	        			   claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	        			   claimMemoDao.updateClaimMemo(claimMemoPO);
	        		   }else if(!billSameCases.equals("")){
	        			   isRepeat = true;
	        			   String memoContent = "该赔案的账单号【"+claimBill.getBillNo()+"】与既往赔案【"+billSameCases.substring(0, billSameCases.length()-1)+"】的账单号相同，请关注审核。";
	        			   ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	        			   claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	        			   claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	        			   claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	        			   claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	        			   claimMemoDao.addClaimMemo(claimMemoPO);
	        		   }
	        		   //@invalid 2.如果本赔案的【帐单号】不存在与既往赔案相同的【帐单号】，则查询出险人相同的赔案
	        		   if (billSameCases.equals("")) {
	        			  //@invalid 查询新核心符合条件的既往赔案
	        			   ClaimCasePO claimCase = new ClaimCasePO();
	        			   claimCase.setCaseId(claimCaseBO.getCaseId());
	        			   claimCase.getData().put("treat_start", claimBill.getTreatStart());
	        			   if(claimBill.getBillType().compareTo(ClaimConstant.CLM_BILL_ONE)==ClaimConstant.ZERO){
	            			   claimCasePO1.getData().put("bill_type", claimBill.getBillType());
	            		   }
	        			   claimCase.setInsuredId(claimCaseBO.getInsuredId());
	        			   //@invalid 查询本次赔案的客户信息，判断四要素（姓名、性别、出生日期、证件号码，且证件号码符合身份证号校验规则）
	        			   CustomerPO customerPO = new CustomerPO();
	        			   customerPO.setCustomerId(claimCaseBO.getInsuredId());
	        			   CustomerPO findCustomerByCustomerId = customerDao.findCustomerByCustomerId(customerPO);
	        			   //@invalid 查询同一出险人们发票是否超过30,四要素通过条件查询防止无客户四要素信息查询数据过多。
	        			   if(StringUtils.isNotEmpty(findCustomerByCustomerId.getCustomerName())){
            				   claimCase.set("customer_name",findCustomerByCustomerId.getCustomerName());
            			   }
            			   if(findCustomerByCustomerId.getCustomerGender() != null){
            				   claimCase.set("customer_gender",findCustomerByCustomerId.getCustomerGender());
            			   }
            			   if(findCustomerByCustomerId.getCustomerBirthday() != null){
            				   claimCase.set("customer_birthday",findCustomerByCustomerId.getCustomerBirthday());
            			   }
            			   if(StringUtils.isNotEmpty(findCustomerByCustomerId.getCustomerCertiCode())){
            				   claimCase.set("customer_certi_code",findCustomerByCustomerId.getCustomerCertiCode());
            			   }
	        			   List<ClaimCasePO> claimCases = claimCaseDao.findCasesByBillTreat(claimCase);
	        			   
	        			   
	        			   if (claimCases.size() > 0) {
	        				   for (ClaimCasePO casepo : claimCases) {
	        					   //@invalid 判断是否同一人（四要素）
            					   CustomerPO customerNewPO = new CustomerPO();
            					   if(casepo.getData().get("customer_name")!=null){//客户姓名
            						   customerNewPO.setCustomerName(casepo.getData().get("customer_name").toString());
            					   }
            					   if(casepo.getData().get("customer_id")!=null){//客户id
            						   customerNewPO.setCustomerId((BigDecimal)casepo.getData().get("customer_id"));
            					   }
            					   if(casepo.getData().get("customer_gender")!=null){
            						   customerNewPO.setCustomerGender((BigDecimal)casepo.getData().get("customer_gender"));
            					   }
            					   if(casepo.getData().get("customer_birthday")!=null){
            						   customerNewPO.setCustomerBirthday((Date)casepo.getData().get("customer_birthday"));
            					   }
            					   if(casepo.getData().get("customer_certi_code")!=null){
            						   customerNewPO.setCustomerCertiCode(casepo.getData().get("customer_certi_code").toString());
            					   }
	        					   boolean fourElements = fourElements(findCustomerByCustomerId,customerNewPO); 
	        					   if(casepo.getData().get("billno_count")!=null&&fourElements){
	        						   caseCount = caseCount + Integer.valueOf(casepo.getData().get("billno_count").toString());
	        						   insuredSameCases = insuredSameCases + "["+casepo.getCaseNo()+"]" + "、";
	        					   }
	        				   }
	        			   }
	        			  //@invalid 拼接老核心的既往赔案和总数，在老核心接口返回的循环中
	        			   
	        			   ClaimMemoPO claimMemoTwoPO = new ClaimMemoPO();
	        			   claimMemoTwoPO.setMemoType("4");
	        			   claimMemoTwoPO.setMemoOption("403");
	        			   claimMemoTwoPO.setCaseId(claimCaseBO.getCaseId());
	        			   claimMemoTwoPO.setMemoContent("【"+ claimBill.getBillNo()+ "】");
	    				   List<ClaimMemoPO> findBillNoClaimMemoTwo = claimMemoDao.findBillNoClaimMemo(claimMemoTwoPO);
	        			   //@invalid 记录备注问题件
	    				  if (caseCount > 30&&findBillNoClaimMemoTwo.size()>0) {//@invalid 如果大于30张，则记录备注问题件(没写，则认为不包括本赔案)
	    					  isRepeat = true;
	    					  String memoContent = "该客户【"+claimBill.getBillNo()+"】下的于【开始日期"+ DateUtilsEx.formatToString(claimBill.getTreatStart(), ClaimConstant.DEFAULT_DATE_FORMAT_STR) +"】发生的门诊发票已经超过30张（其他赔案包括"+insuredSameCases.substring(0, insuredSameCases.length()-1)+"），请关注审核。";
	    				      ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	    				      claimMemoPO.setMemoId(findBillNoClaimMemoTwo.get(0).getMemoId());
	    				      claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	    				      claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	    				      claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	    				      claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	    				      claimMemoDao.updateClaimMemo(claimMemoPO);
	    				  }else if(caseCount > 30){
	    					  isRepeat = true;
	    					  String memoContent = "该客户【"+claimBill.getBillNo()+"】下的于【开始日期"+ DateUtilsEx.formatToString(claimBill.getTreatStart(), ClaimConstant.DEFAULT_DATE_FORMAT_STR) +"】发生的门诊发票已经超过30张（其他赔案包括"+insuredSameCases.substring(0, insuredSameCases.length()-1)+"），请关注审核。";
	    				      ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	    				      claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	    				      claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	    				      claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	    				      claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	    				      claimMemoDao.addClaimMemo(claimMemoPO);
	    				  }
	        		   }
	        	   } else { //@invalid 住院账单
	        		   //@invalid 1、账单号相同，开始日期相同、治疗医院代码相同、理赔类型相同 则记录：备注内容：该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。
	        		   //@invalid 查询本赔案的理赔类型
	        		   String claimTypes = "";
	        		   for (ClaimSubCasePO claimSubCasePO2 : claimSubCasePOs){
	        			   claimTypes = claimTypes + claimSubCasePO2.getClaimType() + ClaimConstant.COMMA;
	        	       } 
	        		   claimTypes =  claimTypes.substring(0, claimTypes.length()-ClaimConstant.ONE);
	        		   ClaimCasePO casePO = new ClaimCasePO();
	        		   casePO.setCaseId(claimCaseBO.getCaseId());
	        		   casePO.getData().put("bill_no", claimBill.getBillNo());
	        		   casePO.getData().put("treat_start", claimBill.getTreatStart());
	        		   casePO.getData().put("hospital_code", claimBill.getHospitalCode());
	        		   casePO.getData().put("claim_type", claimTypes);
	        		   List<ClaimCasePO> casePOs = claimCaseDao.findCasesByBillOther(casePO);
	        		   //@invalid 医院为未维护医院特殊校验同一人
	        		   if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
	        			   if (casePOs.size() > 0) {
	        				   for (ClaimCasePO casepo : casePOs) {
	        					   //@invalid 判断是否同一人
	        					   if(casepo.getInsuredId().compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
	        						   billTreatSame = billTreatSame + casepo.getCaseNo() + "、";
	        					   }
	        				   }
	        			   }  
	        		   }else{
	        			   if (casePOs.size() > 0) {
	        				   for (ClaimCasePO casepo : casePOs) {
	        					   billTreatSame = billTreatSame + casepo.getCaseNo() + "、";
	        				   }
	        			   }
	        		   }
	        		   
	        		 //@invalid 记录备注
	        		   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
	        		   claimMemoOnePO.setMemoType("4");
	        		   claimMemoOnePO.setMemoOption("403");
	        		   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
	        		   claimMemoOnePO.setMemoContent("【"+ claimBill.getBillNo()+ "】");
    				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
	           		   if (!billTreatSame.equals("")&&findBillNoClaimMemoOne.size()>0) {
	           			   isRepeat = true;
	           			   String memoContent = "该赔案的收据编号【"+claimBill.getBillNo()+"】、【"+ DateUtilsEx.formatToString(claimBill.getTreatStart(), ClaimConstant.DEFAULT_DATE_FORMAT_STR) +"】与既往赔案["+ billTreatSame.substring(0, billTreatSame.length()-1) +"]相同，请关注审核。";
	           			   ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	           			   claimMemoPO.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
	           			   claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	           			   claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	           			   claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	           			   claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	           			   claimMemoDao.updateClaimMemo(claimMemoPO);
	           		   }else if(!billTreatSame.equals("")){
	           			   isRepeat = true;
	           			   String memoContent = "该赔案的收据编号【"+claimBill.getBillNo()+"】、【"+ DateUtilsEx.formatToString(claimBill.getTreatStart(), ClaimConstant.DEFAULT_DATE_FORMAT_STR) +"】与既往赔案["+ billTreatSame.substring(0, billTreatSame.length()-1) +"]相同，请关注审核。";
	           			   ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	           			   claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	           			   claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	           			   claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	           			   claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	           			   claimMemoDao.addClaimMemo(claimMemoPO);
	           		   }
	        		   
	        		   //@invalid 2、账单号相同，开始日期均不同 ,治疗医院相同
	                   if (billTreatSame.equals("")) {
	                	  //@invalid (1)账单号出现大于等于三次(包括本次)、则记录
	            		  //@invalid (2)账单号出现小于等于二次(包括本次),出险日期不同则记录；
	            		  //@invalid                           出险日期相同并且是个险，则记录；
	            		  //@invalid                                       团险，未录入B002-第三方给付并且给付类型是否是6（其他）或7(自费报销)，则记录
	            		  //@invalid 备注内容：该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。
	                	  //@invalid 查询账单号相同，开始日期均不同，治疗医院相同的赔案信息
	                	  ClaimBillPO claimBillN = new ClaimBillPO();
	                	  claimBillN.setBillNo(claimBill.getBillNo());
	                	  claimBillN.setTreatStart(claimBill.getTreatStart());
	                	  claimBillN.setHospitalCode(claimBill.getHospitalCode());
	                	  claimBillN.setCaseId(claimBill.getCaseId());
	                	  List<ClaimBillPO> claimBillNs = claimBillDao.findallClaimOtherNation(claimBillN);
	                	  //@invalid 医院为未维护医院特殊校验同一人
	                	  if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
		        			   if (claimBillNs.size() > 0) {
		        				   for (ClaimBillPO claimBil : claimBillNs) {
		        					   //@invalid 判断是否同一人 
		        					   if(claimBil.getData().get("billno_count")!=null&&claimBil.getData().get("insured_id")!=null
		        							   &&new BigDecimal(claimBil.getData().get("insured_id").toString()).compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
		        						   count3 = count3 + Integer.valueOf(claimBil.getData().get("billno_count").toString());
			        					   billTreatDif =  billTreatDif + claimBil.getData().get("case_no").toString() + "、";
		        					   }
		        				   }
		        			   }  
		        		   }else{
		        			   for (ClaimBillPO claimBil : claimBillNs) {
		        				   if(claimBil.getData().get("billno_count")!=null){
		        					   count3 = count3 + Integer.valueOf(claimBil.getData().get("billno_count").toString());
		        					   billTreatDif =  billTreatDif + claimBil.getData().get("case_no").toString() + "、";
		        				   }
		        			   }
		        		   }
	                	  

	                	   
	                	//@invalid 记录备注
	                	  ClaimMemoPO claimMemoTwoPO = new ClaimMemoPO();
	        			   claimMemoTwoPO.setMemoType("4");
	        			   claimMemoTwoPO.setMemoOption("403");
	        			   claimMemoTwoPO.setCaseId(claimCaseBO.getCaseId());
	        			   claimMemoTwoPO.setMemoContent("【"+ claimBill.getBillNo()+ "】");
	    				   List<ClaimMemoPO> findBillNoClaimMemoTwo = claimMemoDao.findBillNoClaimMemo(claimMemoTwoPO);
	              		   if (count3  >= 3&&findBillNoClaimMemoTwo.size()>0) {//@invalid 账单号出现大于等于三次(包括本次)，则记录以下备注
	           	  		  isRepeat = true;
	           	  		  String memoContent = "该赔案的收据编号【"+claimBill.getBillNo()+"】与既往赔案["+ billTreatDif.substring(0, billTreatDif.length()-1) +"]相同，请关注审核。";
	           		      ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	           		      claimMemoPO.setMemoId(findBillNoClaimMemoTwo.get(0).getMemoId());
	           		      claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	           		      claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	           		      claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	           		      claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	           		      claimMemoDao.updateClaimMemo(claimMemoPO);
	           	   		}else if(count3  >= 3){
	           	   			isRepeat = true;
	           	  		  String memoContent = "该赔案的收据编号【"+claimBill.getBillNo()+"】与既往赔案["+ billTreatDif.substring(0, billTreatDif.length()-1) +"]相同，请关注审核。";
	           		      ClaimMemoPO claimMemoPO = new ClaimMemoPO();
	           		      claimMemoPO.setCaseId(claimCaseBO.getCaseId());
	           		      claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
	           		      claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
	           		      claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
	           		      claimMemoDao.addClaimMemo(claimMemoPO);
	           	   		}else if(count3  <= 2 && (!ClaimConstant.NULL_STR.equals(billTreatDif))){
	           	   			//@invalid (2)账单号出现小于等于二次(包括本次),出险日期不同则记录；
	           	   			//@invalid 此次循环处理新核心，新核心查出来只有个险， 出险时间相同记录，出险时间不同既往赔案是个险也要记录
		           	   		isRepeat = true;
			           	   	if(findBillNoClaimMemoTwo.size()>0){
			           	   		claimMemoDao.batchDeleteClaimMemo(findBillNoClaimMemoTwo);
	   	   					}
		           	  		  String memoContent = "该赔案的收据编号【"+claimBill.getBillNo()+"】与既往赔案["+ billTreatDif.substring(0, billTreatDif.length()-1) +"]相同，请关注审核。";
		           		      ClaimMemoPO claimMemoPO = new ClaimMemoPO();
		           		      claimMemoPO.setCaseId(claimCaseBO.getCaseId());
		           		      claimMemoPO.setMemoType("4"); //@invalid 备注/问题件类型 
		           		      claimMemoPO.setMemoOption("403"); //@invalid 备注问题件选项
		           		      claimMemoPO.setMemoContent(memoContent); //@invalid 备注内容
		           		      claimMemoDao.addClaimMemo(claimMemoPO);
		           		      
		           		      
	           	   		}
	                	  
	        		   } 
	        	   }
	        	 //5【触发本人既往赔案】该客户【帐单号】与本人既往赔案【赔案号】下存在相同账单号发票，请关注审核
        		   //当前赔案【帐单号】与同一出险人的既往赔案【帐单号】相同时视为重复发票号重复赔案
        			   ClaimCasePO casePO = new ClaimCasePO();
        			   if(claimCaseBO.getCaseId()!=null) {
        				   casePO.setCaseId(claimCaseBO.getCaseId());
        				   casePO = claimCaseDao.findClaimCaseByCaseId(casePO);
        			   }
        				 //@invalid 拼接新核心赔案
        	        		   //@invalid 查询新核心同一出险人的既往赔案
        	        		   ClaimCasePO claimCasePO1 = new ClaimCasePO();
        	        		   claimCasePO1.setCaseId(claimCaseBO.getCaseId());
        	        		   claimCasePO1.setInsuredId(claimCaseBO.getInsuredId());
        	        		   claimCasePO1.getData().put("bill_no", claimBill.getBillNo());
        	        		   List<ClaimCasePO> claimCasePOs = claimCaseDao.findCaseNoByBillNoAndInsuredId(claimCasePO1);
        	        			   //@invalid 拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        	        			   for (ClaimCasePO claimCase : claimCasePOs) {
        	        				   billSameCases5 = billSameCases5 + "["+claimCase.getCaseNo()+"]" + "、";
        	        			   }
        			   logger.info("个险重复账单5" +billSameCases5);
        			   //@invalid 处理备注问题件
        				   ClaimMemoPO claimMemoOnePO = new ClaimMemoPO();
        				   claimMemoOnePO.setMemoType("4");
        				   claimMemoOnePO.setMemoOption("403");
        				   claimMemoOnePO.setCaseId(claimCaseBO.getCaseId());
        				   claimMemoOnePO.setMemoContent("【"+ claimBill.getBillNo()+ "】");
        				   List<ClaimMemoPO> findBillNoClaimMemoOne = claimMemoDao.findBillNoClaimMemo(claimMemoOnePO);
        	        		   if (!billSameCases5.equals("")&&findBillNoClaimMemoOne.size()>0) {//@invalid 如果存在，则记录备注信息
        	        			   isRepeat = true;
        	        			   String memoContent = "【"+casePO.getCaseNo()+"】该客户【"+claimBill.getBillNo()+"】与本人既往赔案【"+billSameCases5.substring(0, billSameCases5.length()-1)+"】下存在相同账单号发票，请关注审核。";
        	        			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
        	        			   claimMemoPO1.setMemoId(findBillNoClaimMemoOne.get(0).getMemoId());
        	        			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
        	        			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
        	        			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
        	        			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
        	        			   claimMemoDao.updateClaimMemo(claimMemoPO1);
        	        			   billNoOldStr.add(claimBill.getBillNo());//@invalid 添加已经处理的账单信息
        	        			   logger.info("个险重复账单5,备注信息1：" +memoContent);
        	        		   }else if(!billSameCases5.equals("")){
        	        			   isRepeat = true;
        	        			   String memoContent = "【"+casePO.getCaseNo()+"】该客户【"+claimBill.getBillNo()+"】与本人既往赔案【"+billSameCases5.substring(0, billSameCases5.length()-1)+"】下存在相同账单号发票，请关注审核。";
        	        			   ClaimMemoPO claimMemoPO1 = new ClaimMemoPO();
        	        			   claimMemoPO1.setCaseId(claimCaseBO.getCaseId());
        	        			   claimMemoPO1.setMemoType("4"); //@invalid 备注/问题件类型 
        	        			   claimMemoPO1.setMemoOption("403"); //@invalid 备注问题件选项
        	        			   claimMemoPO1.setMemoContent(memoContent); //@invalid 备注内容
        	        			   claimMemoDao.addClaimMemo(claimMemoPO1);
        	        			   billNoOldStr.add(claimBill.getBillNo());//@invalid 添加已经处理的账单信息
        	        			   logger.info("个险重复账单5,备注信息2：" +memoContent);
        	        			   
        	        		}
	        	   
	        	   
	           }
    	   
    	   
		   
    	   //@invalid 存在重复账单，更新赔案主表 “重复账单号标识”为是,赔案置为“普通案件”
           if (isRepeat) {
        	   repeatedCase = "true"; //@invalid 代表重复赔案
        	   ClaimCasePO claimCasePO2 = new ClaimCasePO();
        	   claimCasePO2.setCaseId(claimCaseBO.getCaseId());      
        	   claimCasePO2.setRepeatNumberFlag(ClaimConstant.BIGDECIMALYES);
        	   claimCasePO2 = claimCaseDao.updateRepeatAndCaseFalg(claimCasePO2);
           }
       }else{
           //@invalid 1.非医疗案件  不存在医疗案件
           //@invalid  同一出险人, 存在相同的出险日期，and,出险原因相同，and,存在相同的理赔类型
           //@invalid  赔案状态包括“结案”和“未结案”，且不能为“关闭”，and赔案结论非“全部拒付”
           ClaimCasePO claimCasePO =new ClaimCasePO();
           claimCasePO.setCaseId(claimCaseBO.getCaseId());
           List<ClaimCasePO>  claimCasePORepList= claimCaseDao.findRepCase(claimCasePO);
           for (ClaimCasePO claimCasePO2 : claimCasePORepList){
               repeatedCase+=claimCasePO2.getCaseNo()+"、";
           }   
           if(repeatedCase.length()>0){
               repeatedCase=repeatedCase.substring(0, repeatedCase.length()-1);
               repeatedCase="该赔案存在与赔案号为"+repeatedCase+"的案件发生重复赔案的风险，请核实";
           } 
           
       }
       

        return repeatedCase;
    }
    /**
     * @description 校验账单号数据是否超过指定数量
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.service.ICheckRuleVerifyService#modifyRepeatedCase(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 赔案信息
     * @return 提示信息
    */
    @Override
    public Map<String, String> modifyRepeatedCase(ClaimCaseBO claimCaseBO) {
    	//1 为阻断提示,2为不提示
    	Map<String, String> map = new HashMap<String, String>();
    	if(claimCaseBO.getCaseId()==null){
    		map.put("1", "请重新初始化界面");
            return map;
        }
        String claimTypeStr = "";
        
       //既有医疗又有非医疗的情况，视为理赔类型为医疗
        ClaimSubCasePO claimSubCasePO =new ClaimSubCasePO();
        claimSubCasePO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimSubCasePO> claimSubCasePOs= claimSubCaseDao.findAllClaimSubCase( claimSubCasePO);
        ClaimCasePO claimCaseInsuredIdPO = new ClaimCasePO();
        claimCaseInsuredIdPO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimCasePO> findAllClaimCase2 = claimCaseDao.findAllClaimCase(claimCaseInsuredIdPO);
        if(findAllClaimCase2.size()>0){
        	claimCaseBO.setInsuredId(findAllClaimCase2.get(0).getInsuredId());
        }
        boolean claimTypeMedical=false;
        for (ClaimSubCasePO claimSubCasePO2 : claimSubCasePOs)
        {
        	
        	if(ClaimConstant.CLAIM_TYPE_ONG.equals(claimSubCasePO2.getClaimType()) ){//身故
        		claimTypeStr = claimTypeStr + "02" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_TWO.equals(claimSubCasePO2.getClaimType()) ){//伤残
        		claimTypeStr = claimTypeStr + "01" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_THREE.equals(claimSubCasePO2.getClaimType()) ){//重大疾病
        		claimTypeStr = claimTypeStr + "04" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_FOUR.equals(claimSubCasePO2.getClaimType()) ){//高残
        		claimTypeStr = claimTypeStr + "03" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_SIX.equals(claimSubCasePO2.getClaimType()) ){//一般失能
        		claimTypeStr = claimTypeStr + "08" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_SEVEN.equals(claimSubCasePO2.getClaimType()) ){//重度失能
        		claimTypeStr = claimTypeStr + "08" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_EIGHT.equals(claimSubCasePO2.getClaimType()) ){//医疗
        		claimTypeStr = claimTypeStr + "00" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_TEN.equals(claimSubCasePO2.getClaimType()) ){//特种疾病
        		claimTypeStr = claimTypeStr + "07" + ",";
        	}
        	if(ClaimConstant.CLAIM_TYPE_ELEVEN.equals(claimSubCasePO2.getClaimType()) ){//豁免
        		claimTypeStr = claimTypeStr + "09" + ",";
        	}
            if(ClaimConstant.CLAIM_TYPE_EIGHT.equals(claimSubCasePO2.getClaimType()) ){
                claimTypeMedical=true;
            }
        }
        
        if(claimTypeMedical){//医疗类案件
            //查询本赔案的账单列表
            ClaimBillPO claimBillPO = new ClaimBillPO();
            claimBillPO.setCaseId(claimCaseBO.getCaseId());
            List<ClaimBillPO> claimBillPOs = claimBillDao.findClaimBillOtherByCaseId(claimBillPO);
            
            if(claimBillPOs.size()<1){
            	map.put("2", "");
                return map;
            }
            
          //只判断新核心重复（老核心30秒未返回或返回无重复）
     	   for (ClaimBillPO claimBill : claimBillPOs) {
     		   	//1.该赔案的账单号【帐单号】与既往赔案【赔案号】的账单号相同，请关注审核
                String billSameCases = "";
                List<String> billSameCaseList = new ArrayList<>();
                
                //2该客户【帐单号】下的于【开始日期YYYY-MM-DD】发生的门诊发票已经超过30张（其他赔案包括【赔案号1】、【赔案号2】…）请关注审核
                int caseCount = 0;
                String insuredSameCases = "";
                List<String> insuredSameCasesList = new ArrayList<>();
                //3该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。
                String billTreatSame = ""; 
                List<String> billTreatSameList = new ArrayList<>();
                //4该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。
                int count3 = 1;
          	   	String billTreatDif = "";
                List<String> billTreatDifList = new ArrayList<>();
          	   	if (claimBill.getTreatType().equals(ClaimConstant.NO)) { //门诊账单
         		   //查询新核心相同的门诊账单赔案
         		   ClaimCasePO claimCasePO1 = new ClaimCasePO();
         		   claimCasePO1.setCaseId(claimCaseBO.getCaseId());
         		   if(claimBill.getBillType().compareTo(ClaimConstant.CLM_BILL_ONE)==ClaimConstant.ZERO){
         			   claimCasePO1.getData().put("bill_type", claimBill.getBillType());
         		   }
         		   claimCasePO1.getData().put("bill_no", claimBill.getBillNo());
         		   List<ClaimCasePO> claimCasePOs = claimCaseDao.findCaseNoByBillNo(claimCasePO1);
         		   //医院为未维护医院特殊校验同一人
        		   if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
        			 //拼接新核心赔案(老核心返回一条账单返回一条)，只拼接本次账单
        			   for (ClaimCasePO claimCase : claimCasePOs) {
        				   if(claimCase.getInsuredId().compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
        					   billSameCases = billSameCases + "["+claimCase.getCaseNo()+"]" + "、";
        					   billSameCaseList.add(claimCase.getCaseNo());
        				   }
        			   }
        		   }else{
        			   for (ClaimCasePO claimCase : claimCasePOs) {
        				   billSameCases = billSameCases + "["+claimCase.getCaseNo()+"]" + "、";
        				   billSameCaseList.add(claimCase.getCaseNo());
        			   }
        			   //拼接老核心相同账单号的赔案，在老核心接口返回的循环中
        		   }
     			   //超出150个赔案进行错误提示
     			  if(billSameCaseList.size()>150){
     				 map.put("1", "账单号" +  claimBill.getBillNo() + "的重复账单超150个赔案，请重新录入");
     				 break;
     			  }
     			   
         		   //2.如果本赔案的【帐单号】不存在与既往赔案相同的【帐单号】，则查询出险人相同的赔案
         		   if (billSameCases.equals("")) {
         			  //查询新核心符合条件的既往赔案
         			   ClaimCasePO claimCase = new ClaimCasePO();
         			   claimCase.setCaseId(claimCaseBO.getCaseId());
         			   claimCase.getData().put("treat_start", claimBill.getTreatStart());
         			   if(claimBill.getBillType().compareTo(ClaimConstant.CLM_BILL_ONE)==ClaimConstant.ZERO){
             			   claimCasePO1.getData().put("bill_type", claimBill.getBillType());
             		   }
         			   claimCase.setInsuredId(claimCaseBO.getInsuredId());
         			   //查询本次赔案的客户信息，判断四要素（姓名、性别、出生日期、证件号码，且证件号码符合身份证号校验规则）
         			   CustomerPO customerPO = new CustomerPO();
         			   customerPO.setCustomerId(claimCaseBO.getInsuredId());
         			   CustomerPO findCustomerByCustomerId = customerDao.findCustomerByCustomerId(customerPO);
         			   //查询同一出险人们发票是否超过30,四要素通过条件查询防止无客户四要素信息查询数据过多。
         			   if(StringUtils.isNotEmpty(findCustomerByCustomerId.getCustomerName())){
         				   claimCase.set("customer_name",findCustomerByCustomerId.getCustomerName());
	      			   }
	      			   if(findCustomerByCustomerId.getCustomerGender() != null){
	      				   claimCase.set("customer_gender",findCustomerByCustomerId.getCustomerGender());
	      			   }
	      			   if(findCustomerByCustomerId.getCustomerBirthday() != null){
	      				   claimCase.set("customer_birthday",findCustomerByCustomerId.getCustomerBirthday());
	      			   }
	      			   if(StringUtils.isNotEmpty(findCustomerByCustomerId.getCustomerCertiCode())){
	      				   claimCase.set("customer_certi_code",findCustomerByCustomerId.getCustomerCertiCode());
	      			   }
         			   List<ClaimCasePO> claimCases = claimCaseDao.findCasesByBillTreat(claimCase);
         			   
         			   
         			   if (claimCases.size() > 0) {
         				   for (ClaimCasePO casepo : claimCases) {
         					   //判断是否同一人（四要素）
         					   CustomerPO customerNewPO = new CustomerPO();
      					   if(casepo.getData().get("customer_name")!=null){//客户姓名
      						   customerNewPO.setCustomerName(casepo.getData().get("customer_name").toString());
      					   }
      					   if(casepo.getData().get("customer_id")!=null){//客户id
      						   customerNewPO.setCustomerId((BigDecimal)casepo.getData().get("customer_id"));
      					   }
      					   if(casepo.getData().get("customer_gender")!=null){//客户性别
      						   customerNewPO.setCustomerGender((BigDecimal)casepo.getData().get("customer_gender"));
      					   }
      					   if(casepo.getData().get("customer_birthday")!=null){//客户生日
      						   customerNewPO.setCustomerBirthday((Date)casepo.getData().get("customer_birthday"));
      					   }
      					   if(casepo.getData().get("customer_certi_code")!=null){//客户证件号码
      						   customerNewPO.setCustomerCertiCode(casepo.getData().get("customer_certi_code").toString());
      					   }
         					   boolean fourElements = fourElements(findCustomerByCustomerId,customerNewPO); 
         					   if(casepo.getData().get("billno_count")!=null&&fourElements){
         						   caseCount = caseCount + Integer.valueOf(casepo.getData().get("billno_count").toString());
         						   insuredSameCases = insuredSameCases + "["+casepo.getCaseNo()+"]" + "、";
         						   insuredSameCasesList.add(casepo.getCaseNo());
         					   }
         				   }
         			   }
         			   //拼接老核心的既往赔案和总数，在老核心接口返回的循环中
         			   if(insuredSameCasesList.size()>150){
         				   //超出150个赔案进行错误提示
         				   map.put("1", "账单号" +  claimBill.getBillNo() + "的重复账单超150个赔案，请重新录入");
         				   break;
         			   }
         		   }
         	   } else { //住院账单
         		   //1、账单号相同，开始日期相同、治疗医院代码相同、理赔类型相同 则记录：备注内容：该赔案的收据编号【帐单号】、【开始日期】与既往赔案【赔案号】相同，请关注审核。
         		   //查询本赔案的理赔类型
         		   String claimTypes = "";
         		   for (ClaimSubCasePO claimSubCasePO2 : claimSubCasePOs){
         			   claimTypes = claimTypes + claimSubCasePO2.getClaimType() + ClaimConstant.COMMA;
         	       } 
         		   claimTypes =  claimTypes.substring(0, claimTypes.length()-ClaimConstant.ONE);
         		   ClaimCasePO casePO = new ClaimCasePO();
         		   casePO.setCaseId(claimCaseBO.getCaseId());
         		   casePO.getData().put("bill_no", claimBill.getBillNo());
         		   casePO.getData().put("treat_start", claimBill.getTreatStart());
         		   casePO.getData().put("hospital_code", claimBill.getHospitalCode());
         		   casePO.getData().put("claim_type", claimTypes);
         		   List<ClaimCasePO> casePOs = claimCaseDao.findCasesByBillOther(casePO);
         		   //医院为未维护医院特殊校验同一人
         		   if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
         			   if (casePOs.size() > 0) {
         				   for (ClaimCasePO casepo : casePOs) {
         					   //判断是否同一人
         					   if(casepo.getInsuredId().compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
         						   billTreatSame = billTreatSame + casepo.getCaseNo() + "、";
         						  billTreatSameList.add(casepo.getCaseNo());
         					   }
         				   }
         			   }  
         		   }else{
         			   if (casePOs.size() > 0) {
         				   for (ClaimCasePO casepo : casePOs) {
         					   billTreatSame = billTreatSame + casepo.getCaseNo() + "、";
         					   billTreatSameList.add(casepo.getCaseNo());
         				   }
         			   }
         		   }
         		   
         		   if(billTreatSameList.size()>150){
         			   //超出150个赔案进行错误提示
     				   map.put("1", "账单号" +  claimBill.getBillNo() + "的重复账单超150个赔案，请重新录入");
     				   break;
         		   }
         		   
         		   //2、账单号相同，开始日期均不同 ,治疗医院相同
                    if (billTreatSame.equals("")) {
                 	  //(1)账单号出现大于等于三次(包括本次)、则记录
             		  //(2)账单号出现小于等于二次(包括本次),出险日期不同则记录；
             		  //                          出险日期相同并且是个险，则记录；
             		  //                                      团险，未录入B002-第三方给付并且给付类型是否是6（其他）或7(自费报销)，则记录
             		  //备注内容：该赔案的收据编号【帐单号】与既往赔案【赔案号】的收据编号相同，请关注审核。
                 	  //查询账单号相同，开始日期均不同，治疗医院相同的赔案信息
                 	  ClaimBillPO claimBillN = new ClaimBillPO();
                 	  claimBillN.setBillNo(claimBill.getBillNo());
                 	  claimBillN.setTreatStart(claimBill.getTreatStart());
                 	  claimBillN.setHospitalCode(claimBill.getHospitalCode());
                 	  claimBillN.setCaseId(claimBill.getCaseId());
                 	  List<ClaimBillPO> claimBillNs = claimBillDao.findallClaimOtherNation(claimBillN);
                 	  //医院为未维护医院特殊校验同一人
                 	  if(ClaimConstant.HOSPITAL_CODE_UNMAINTAINED.equals(claimBill.getHospitalCode())){
 	        			   if (claimBillNs.size() > 0) {
 	        				   for (ClaimBillPO claimBil : claimBillNs) {
 	        					   //判断是否同一人 
 	        					   if(claimBil.getData().get("billno_count")!=null&&claimBil.getData().get("insured_id")!=null
 	        							   &&new BigDecimal(claimBil.getData().get("insured_id").toString()).compareTo(claimCaseBO.getInsuredId())==ClaimConstant.ZERO){
 	        						   count3 = count3 + Integer.valueOf(claimBil.getData().get("billno_count").toString());
 		        					   billTreatDif =  billTreatDif + claimBil.getData().get("case_no").toString() + "、";
 		        					   billTreatDifList.add(claimBil.getData().get("case_no").toString());
 	        					   }
 	        				   }
 	        			   }  
 	        		   }else{
 	        			   for (ClaimBillPO claimBil : claimBillNs) {
 	        				   if(claimBil.getData().get("billno_count")!=null){
 	        					   count3 = count3 + Integer.valueOf(claimBil.getData().get("billno_count").toString());
 	        					   billTreatDif =  billTreatDif + claimBil.getData().get("case_no").toString() + "、";
 	        					   billTreatDifList.add(claimBil.getData().get("case_no").toString());
 	        				   }
 	        			   }
 	        		   }
                 	  
                 	 if(billTreatDifList.size()>150){
                 		//超出150个赔案进行错误提示
       				   	map.put("1", "账单号" +  claimBill.getBillNo() + "的重复账单超150个赔案，请重新录入");
       				   	break;
                 	 }
                 	   
                 	  
         		   }
         	   }
            }
            
        }
        if(map.isEmpty()){
        	map.put("2", "");
        }
        return map;
    }
    
    /**
     * @description 判断是否同一出险人
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param customerPO 本次赔案信息
     * @param customerNewCasePO 所要对比的客户信息
     * @return 
    */
    public boolean  fourElements(CustomerPO customerPO,CustomerPO customerNewCasePO){
    	//1.按照四要素判断是否同一出险人
    	boolean IDCardFlag = false;
    	//@invalid 根据客户id查询客户信息
//@invalid     	CustomerPO customerCasePO = new CustomerPO();
//@invalid     	customerCasePO.setCustomerId(casepo.getInsuredId());
//@invalid     	CustomerPO customerNewCasePO = customerDao.findCustomerByCustomerId(customerCasePO);
    	if(customerPO!=null&&customerNewCasePO!=null){
    		if(customerPO.getCustomerId().compareTo(customerNewCasePO.getCustomerId())==ClaimConstant.ZERO){
    			return true;
    		}
    		//@invalid 首先判断四要素是否相同
    		if(customerPO.getCustomerName().equals(customerNewCasePO.getCustomerName())
    			&&customerPO.getCustomerGender().compareTo(customerNewCasePO.getCustomerGender())==0
    			&&customerPO.getCustomerBirthday().equals(customerNewCasePO.getCustomerBirthday())
    			&&customerPO.getCustomerCertiCode().equals(customerNewCasePO.getCustomerCertiCode())){
    			//@invalid 身份证号校验规则
    			boolean validatedAllIdCaseCard = ClaimIDCardValidity.isValidatedAllIdcard(customerNewCasePO.getCustomerCertiCode());
    			boolean validatedAllIdCard = ClaimIDCardValidity.isValidatedAllIdcard(customerPO.getCustomerCertiCode());
    			if(validatedAllIdCard&&validatedAllIdCaseCard){
    				IDCardFlag = true;
    			}else{
    				IDCardFlag = false;
    			}
    		}
    		
    	}
    	
		return IDCardFlag;
    }
    
    /**
     * @description 调用老核心接口
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param inputData 老核心接口入参（拼接完成的）
     * @return 老核心重复账单接口返回的信息
     * @throws ClassNotFoundException  异常信息
    */
    public com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData queryBillQuerySrvPortType(com.nci.tunan.clm.BillQuerySrvPortType.service.bd.InputData inputData) throws ClassNotFoundException{
    	//1.调用老核心接口查询重复账单数据
    	//@invalid 老核心接口esb转接的地址
    	URL wsdlURL = BillQuerySrvBindingQSService.WSDL_LOCATION;
 	   String wsdlURLStr = wsdlURL.toString();
// 	  String wsdlURLStr = "http://*********:8111/services/P00001003074?wsdl";  
 	  logger.info("老核心重复账单接口" +wsdlURLStr);
 	   JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdlURLStr, "com.nci.tunan.clm.BillQuerySrvPortType.BillQuerySrvPortType");
 	   BillQuerySrvPortType port = (BillQuerySrvPortType)soapFactory.create();
//@invalid  	   BillQuerySrvBindingQSService ss = new BillQuerySrvBindingQSService(wsdlURL, SERVICE_NAMEBill);
//@invalid      BillQuerySrvPortType port = ss.getBillQuerySrvBindingQSPort();
        
        com.nci.tunan.clm.BillQuerySrvPortType.common.header.in.SysMsgHeader parametersReqHeader = new com.nci.tunan.clm.BillQuerySrvPortType.common.header.in.SysMsgHeader();
        parametersReqHeader.setMsgId(UUID.randomUUID().toString());
        parametersReqHeader
                .setMsgDate(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), PubConstants.DATE_FORMAT));
        parametersReqHeader.setMsgTime(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(),
                PubConstants.TIME_FORMAT_ESB));
        parametersReqHeader.setServCd("P00001003074");
        parametersReqHeader.setSysCd("067");
        parametersReqHeader.setBizId("32_01_02_I01");
        parametersReqHeader.setBizType("01");
        parametersReqHeader.setOrgCd("86");
        parametersReqHeader.setResCd("");
        parametersReqHeader.setResText("");
        parametersReqHeader.setBizResCd("");
        parametersReqHeader.setBizResText("");
        parametersReqHeader.setVer("110.100.000");
        
        com.nci.tunan.clm.BillQuerySrvPortType.service.bd.SrvReqBody parametersReqBody = new com.nci.tunan.clm.BillQuerySrvPortType.service.bd.SrvReqBody();
        com.nci.tunan.clm.BillQuerySrvPortType.service.hd.SRVReqHead SRVReqHead = new com.nci.tunan.clm.BillQuerySrvPortType.service.hd.SRVReqHead();
        com.nci.tunan.clm.BillQuerySrvPortType.service.bd.SrvReqBizBody srvReqBizBody = new com.nci.tunan.clm.BillQuerySrvPortType.service.bd.SrvReqBizBody();
        srvReqBizBody.setInputData(inputData);
        parametersReqBody.setBizHeader(SRVReqHead);
        parametersReqBody.setBizBody(srvReqBizBody);
        javax.xml.ws.Holder<com.nci.tunan.clm.BillQuerySrvPortType.common.header.in.SysMsgHeader> parametersResHeader = new javax.xml.ws.Holder<com.nci.tunan.clm.BillQuerySrvPortType.common.header.in.SysMsgHeader>();
        javax.xml.ws.Holder<com.nci.tunan.clm.BillQuerySrvPortType.service.bd.SrvResBody> parametersResBody = new javax.xml.ws.Holder<com.nci.tunan.clm.BillQuerySrvPortType.service.bd.SrvResBody>();
        logger.info("老核心重复账单parametersReqHeader" + XmlHelper.classToXml(parametersReqHeader));
        logger.info("老核心重复账单接口parametersReqBody" + XmlHelper.classToXml(parametersReqBody));
        //@invalid 调用接口方法
        port.billQuery(parametersReqHeader, parametersReqBody, parametersResHeader, parametersResBody);
		
        com.nci.tunan.clm.BillQuerySrvPortType.service.bd.OutputData outputData = null;
        if(parametersResBody.value.getBizBody()!=null){
        	outputData = parametersResBody.value.getBizBody().getOutputData();
        }
        logger.info("老核心重复账单接口parametersResBody" + XmlHelper.classToXml(parametersResBody));
        return outputData;

    }
    
    
    /**
     * @description 查询客户是否为黑名单
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param blackName 客户姓名
     * @param blackCertiCode 证件号码
     * @return 
    */
    public boolean  checkBlackList(String  blackName,String blackCertiCode,String distinguish){
    	//1.查询客户是否为黑名单
    	//@invalid 默认false
        boolean blackFlag=false;  //@invalid 默认false
        ClaimBlackNamePO claimBlackName = new ClaimBlackNamePO();
        claimBlackName.setBlackName(blackName);
        claimBlackName.setBlackCertiCode(blackCertiCode);
        claimBlackName.setBlackNameStatus(ClaimConstant.CLAIM_BLACK_FLAG);
        if(distinguish.equals(ClaimConstant.STRING_SORT_ONE)) {
//          //黑名单类型为业务员
            claimBlackName.setBlackNameType(ClaimConstant.STRING_SORT_TWO);
        }
        List<ClaimBlackNamePO> claimBlackNameList =claimBlackNameDao.findAllClaimBlackName(claimBlackName);
        if(claimBlackNameList.size()>0){
            blackFlag=true;
        } 
        return  blackFlag;
    }
    
    /**
     * @description 查询客户是否为黑名单
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param agentCode 客户id
     * @return 结果
    */                
    private boolean  checkAgentBlackList(String agentCode,String distinguish){
    	//1.查询客户是否为黑名单
        boolean blackFlag=false;  //@invalid 默认false
        if(agentCode!=null&&!"".equals(agentCode)){
            AgentPO agentPO =new AgentPO();
            agentPO.setAgentCode(agentCode);
            agentPO=  agentDao.findAgent(agentPO);
            if(agentPO.getCertiCode()!=null&&agentPO.getAgentName()!=null&&distinguish!=null){
                boolean blackFlag1 = checkBlackList(agentPO.getAgentName(),agentPO.getCertiCode(),distinguish);
                //@invalid 风控黑名单验证
                boolean blackFlag2 = checkAgentBlackList(agentPO);
                //@invalid 业务员高风险客户验证
                boolean blackFlag3 = checkHighRiskAgentBlackList(agentPO);
                if(blackFlag1||blackFlag2||blackFlag3) {
                	blackFlag = true;
                }
            }
        } 
        return  blackFlag;
    }

	/**
	 * @description 查询高风险客户
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param customerId  高风险客户id
	 * @return 结果
	*/
	@Override
	public boolean checkHighRiskLevelCustomer(BigDecimal customerId) {
		//1.查询高风险客户
		boolean highRiskFlag = false;
		com.nci.core.common.interfaces.vo.CustomerVO customerVO1 = new com.nci.core.common.interfaces.vo.CustomerVO();
		CustomerBaseInfoVO customerBaseInfo = new CustomerBaseInfoVO();
		customerBaseInfo.setCustomerId(customerId);
		customerVO1.setCustomerBaseInfo(customerBaseInfo);
 		com.nci.core.common.interfaces.vo.CustomerVO queryCustomerBaseInfo = BOServiceFactory.getCustomerUCC().queryCustomerBaseInfo(customerVO1);
		if(queryCustomerBaseInfo!=null&&queryCustomerBaseInfo.getCustomerBaseInfo()!=null&&"A".equals(queryCustomerBaseInfo.getCustomerBaseInfo().getCustomerRiskLevel())) {
			highRiskFlag = true;
		};
		return highRiskFlag;
	}

	/**
	 * @description 不疑似黑名单"，1-"疑似黑名单"，2-"不冻结"，3-"冻结"
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param customerNew 客户
	 * @return  结果
	*/
	@Override
	public boolean checkCommonBlackList(CustomerPO customerNew) {
		//1.不疑似黑名单"，1-"疑似黑名单"，2-"不冻结"，3-"冻结"
		boolean commonBlackFlag = false;
  		BlacklistCommonVO  blacklistCommonVO =new BlacklistCommonVO();
  		blacklistCommonVO.setName(customerNew.getCustomerName());
  		blacklistCommonVO.setCountry(customerNew.getCountryCode());
  		blacklistCommonVO.setCertType(customerNew.getCustomerCertType());
  		blacklistCommonVO.setCertiCode(customerNew.getCustomerCertiCode());
  		blacklistCommonVO.setBirthday(customerNew.getCustomerBirthday()); 
  		blacklistCommonVO.setCustomerId(customerNew.getCustomerId());
//@invalid   		blacklistCommonVO.setEventIdentity("1");//@invalid 受益人
//@invalid   		blacklistCommonVO.setMoney(String.valueOf(customerNew.getActualPay()));
  		blacklistCommonVO=com.nci.core.common.factory.BOServiceFactory.isBlackList(blacklistCommonVO);
  		/** 0-"不疑似黑名单"，1-"疑似黑名单"，2-"不冻结"，3-"冻结" **/
		if(!"0".equals(blacklistCommonVO.getFlag())){ 	
			commonBlackFlag = true;
    	}
		return commonBlackFlag;
	}

	/**
	 * @description 判断是否是黑名单客户  start
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param agentPO 客户信息
	 * @return 结果
	*/
	@Override
	public boolean checkAgentBlackList(AgentPO agentPO) {
        //判断是否是黑名单客户  start
   	   boolean isBlack=false; 
   	 
   		BlacklistCommonVO  blacklistCommonVO =new BlacklistCommonVO();
   		blacklistCommonVO.setName(agentPO.getAgentName());
//@invalid    		blacklistCommonVO.setCountry();
   		blacklistCommonVO.setCertType(agentPO.getCertType());
   		blacklistCommonVO.setCertiCode(agentPO.getCertiCode());
   		blacklistCommonVO.setBirthday(agentPO.getBirthday()); 
   		blacklistCommonVO.setCustomerGender(agentPO.getAgentGender());
//@invalid    		blacklistCommonVO.setCustomerId(customerVO.getCustomerId());
//@invalid    		blacklistCommonVO.setEventIdentity("1");//@invalid 受益人
//@invalid    		blacklistCommonVO.setMoney(String.valueOf(claimCaseVO.getActualPay()));
   		blacklistCommonVO=com.nci.core.common.factory.BOServiceFactory.isBlackList(blacklistCommonVO);
			/** 0-"不疑似黑名单"，1-"疑似黑名单"，2-"不冻结"，3-"冻结" **/
			if(!blacklistCommonVO.getFlag().equals("0")){ 	
				isBlack = true;
      	}
		return isBlack;
	}
	/**
	 * @description //判断是否是黑名单客户
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param agentPO 客户
	 * @return 结果
	*/
	@Override
	public boolean checkHighRiskAgentBlackList(AgentPO agentPO) {
		//判断是否是黑名单客户  start
		boolean isBlack=false; 
		
		BlacklistCommonVO  blacklistCommonVO =new BlacklistCommonVO();
		blacklistCommonVO.setName(agentPO.getAgentName());
//@invalid    		blacklistCommonVO.setCountry();
		blacklistCommonVO.setCertType(agentPO.getCertType());
		blacklistCommonVO.setCertiCode(agentPO.getCertiCode());
		blacklistCommonVO.setBirthday(agentPO.getBirthday()); 
		blacklistCommonVO.setCustomerGender(agentPO.getAgentGender());
//@invalid    		blacklistCommonVO.setCustomerId(customerVO.getCustomerId());
//@invalid    		blacklistCommonVO.setEventIdentity("1");//@invalid 受益人
//@invalid    		blacklistCommonVO.setMoney(String.valueOf(claimCaseVO.getActualPay()));
		blacklistCommonVO=com.nci.core.common.factory.BOServiceFactory.isBlackList(blacklistCommonVO);
		/** 0-"不疑似黑名单"，1-"疑似黑名单"，2-"不冻结"，3-"冻结" **/
		if(!blacklistCommonVO.getFlag().equals("0")){ 	
			isBlack = true;
		}
		return isBlack;
	}
	
	/**
	 * 搜索引擎接口(36577变更,接口变更36485)
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseBO 赔案信息
	 * @return 接口返回
	 */
	public OutputData queryClaimMedicalAntiFraud(ClaimCaseBO claimCaseBO){ 
		
		//调用搜索引擎接口
		ClaimLiabPO claimLiabPO = new ClaimLiabPO();
        claimLiabPO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimLiabPO> claimLiabPOList = claimLiabDao.findAllClaimLiab(claimLiabPO);
        //@invalid  53418 去掉“只有医疗类型”的限制，所有理赔类型均需要调用模型引擎接口
        //@invalid 只有医疗的个人理赔业务（理算后）
        if(claimLiabPOList.size()==0){
        	return null;
        }
        
		
        try{
            
        URL wsdlURL = ClaimMedicalAntiFraudSrvBindingQSService.WSDL_LOCATION;
        String wsdlString = wsdlURL.toString();
 
//@invalid         wsdlString = "http://**********:8111/services/P00002002893?wsdl";
 
        JaxWsProxyFactoryBean soapFactory;
        soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdlString,
                "com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.ClaimMedicalAntiFraudSrvPortType");
        ClaimMedicalAntiFraudSrvPortType port = (ClaimMedicalAntiFraudSrvPortType) soapFactory.create();
        //@invalid  定义系统报文头
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SysMsgHeader parametersReqHeader = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SysMsgHeader();
        parametersReqHeader.setMsgId(UUID.randomUUID().toString());
        parametersReqHeader
                .setMsgDate(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), PubConstants.DATE_FORMAT));
        parametersReqHeader.setMsgTime(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(),
                PubConstants.TIME_FORMAT_ESB));
        parametersReqHeader.setServCd("P00002002893");
        parametersReqHeader.setSysCd("067");
        parametersReqHeader.setBizId("00000");
        parametersReqHeader.setBizType("");
        parametersReqHeader.setOrgCd("86");
        parametersReqHeader.setResCd("");
        parametersReqHeader.setResText("");
        parametersReqHeader.setBizResCd("");
        parametersReqHeader.setBizResText("");
        parametersReqHeader.setVer("110.100.000");

        //@invalid  定义系统报文体
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvReqBody parametersReqBody = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvReqBody();
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SysMsgHeader> parametersResHeader = 
                new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SysMsgHeader>();
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvResBody> parametersResBody = 
                new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvResBody>();
        //@invalid  业务报文头
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SRVReqHead SRVReqHead = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SRVReqHead();
        String value = "";
        //@invalid  定义业务报文体
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvReqBizBody srvReqBizBody = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvReqBizBody();
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.InputDataType inputData = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.InputDataType();
        //@invalid 逆选择
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Claim claim = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Claim();
        //@invalid 逆选择保项层节点
        List<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Duty> dutyList = new ArrayList<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Duty>();
        //@invalid 伪造发票节点的账单票据
        List<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Invoice> invoiceList = new ArrayList<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Invoice>();
        //@invalid 虚假发票
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Claim1 claim1 = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Claim1();
        //@invalid 查询理赔类型。
        ClaimSubCasePO claimSubCasePO = new ClaimSubCasePO();
        claimSubCasePO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimSubCasePO> claimSubCasePOs = claimSubCaseDao.findClaimSubCaseByCaseId(claimSubCasePO);
        //@invalid 53418 出险原因为“意外”时不保存“逆选择”得分
        boolean accReasonFlag = false;
        for (ClaimSubCasePO claimSubCasePO2 : claimSubCasePOs) {
        	//@invalid 判断出险原因是否是意外
			if(claimSubCasePO2.getAccReason().compareTo(ClaimConstant.ACC_REASON_TWO) != ClaimConstant.ZERO){
				accReasonFlag = true;
			}
		}
        //@invalid 查询出险结果。
        ClaimAccidentResultPO claimAccidentResultPO = new ClaimAccidentResultPO();
        claimAccidentResultPO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimAccidentResultPO> claimAccidentResultPOs = claimAccidentResultDao.findAllClaimAccidentResult(claimAccidentResultPO);
        //@invalid 查询责任理算表
        
        List<ClaimLiabPO> claimLiabPOs = claimLiabPOList;
        //@invalid 查询事故表
        ClaimAccidentPO claimAccidentPO = new ClaimAccidentPO();
        claimAccidentPO.setAccidentId(claimCaseBO.getAccidentId());
        claimAccidentPO = claimAccidentDao.findClaimAccidentByAccidentId(claimAccidentPO);
        //@invalid 账单天数
        ClaimBillPO claimBillPO = new ClaimBillPO();
        claimBillPO.setCaseId(claimCaseBO.getCaseId());
        ClaimBillPO claimBill = claimBillDao.findClaimBillMaxDay(claimBillPO);
        claim1.setDaycount_max(claimBill.getDayTotal()+"");
        List<ClaimBillPO> claimBillPOs = claimBillDao.findAllClaimBillCount(claimBillPO);
        
        //@invalid 汇总赔案层的调整金额
        BigDecimal adjsum = new BigDecimal(ClaimConstant.ZERO);
        //@invalid 汇总核算赔付金额的和
        BigDecimal sumStandpay = new BigDecimal(ClaimConstant.ZERO);
        //@invalid 汇总保额赔案层的
        BigDecimal beforeSurplusEffectAmount = new BigDecimal(ClaimConstant.ZERO);
        //@invalid 用于日期转化。
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        
        claim.setClmno(claimCaseBO.getCaseNo()); //@invalid 赔案号
        claim.setMngcom(claimCaseBO.getOrganCode()); //@invalid 管理机构
        if(claimCaseBO.getRegisteTime()!=null){
        	claim.setRgtdate(sf.format(claimCaseBO.getRegisteTime()));//@invalid 立案日期
        }else{
        	claim.setRgtdate(sf.format(WorkDateUtil.getWorkDate()));//@invalid 立案日期
        }
        //@invalid 出险日期 取当前赔案最早的
        ClaimSubCasePO claimSubCaseClaimDatePO = new ClaimSubCasePO();
        claimSubCaseClaimDatePO.setCaseId(claimCaseBO.getCaseId());
        List<ClaimSubCasePO> claimSubCaseClaimDatePOs = claimSubCaseDao.findClaimDatesByCaseId(claimSubCasePO);
        claim.setAccdate(sf.format(claimSubCaseClaimDatePOs.get(0).getClaimDate()));//@invalid 出险日期
        if(claimAccidentResultPOs.size() > 0){
            //@invalid 出险结果取最新的一条数据
            claim.setAccresult1(claimAccidentResultPOs.get(0).getAccResult1());//@invalid 出险结果1
            claim.setAccresult2(claimAccidentResultPOs.get(0).getAccResult2());//@invalid 出险结果2
        }
        if(claimCaseBO.getCureHospital() !=null){
            claim.setHospitalcode(claimCaseBO.getCureHospital());//@invalid 医院代码  赔案页面上的唯一一条
        }else{
        	claim.setHospitalcode("");//@invalid 医院代码  赔案页面上的唯一一条
        }
//@invalid         claim.setHospitalcode(claimCaseBO.getCureHospital());//@invalid 医院代码
        ClaimCasePO claimCasePO = new ClaimCasePO();
        claimCasePO.setCaseId(claimCaseBO.getCaseId());
        claimCasePO = claimCaseDao.findClaimCaseByCaseId(claimCasePO);
        if(claimCasePO.getData().get("insert_time") != null){
        	claim.setRptdate(sf.format((Date)claimCasePO.getData().get("insert_time")));//@invalid 报案日期
        }else{
        	claim.setRptdate(sf.format(claimCaseBO.getAcceptTime()));//@invalid 报案日期
        }
        claim.setFrms_biz_code("个险理赔");//@invalid 应用渠道(接口需求要求传固定值)
        claim.setFrms_biz_type("疾病医疗");//@invalid 业务类型(接口需求要求传固定值)
        List<String> riskList = new ArrayList<String>();
        //@invalid 工单INC-20210719-0189保额只要涉案险种
        List<BigDecimal> busiItemIdList = new ArrayList<BigDecimal>();
        //@invalid 逆选择保项层信息
        for(ClaimLiabPO claimLiab : claimLiabPOs){
        	//@invalid 保额到险种   需要进行险种去重
        	sumStandpay = sumStandpay.add(claimLiab.getCalcPay());
            if(!busiItemIdList.contains(claimLiab.getBusiItemId())){
            	busiItemIdList.add(claimLiab.getBusiItemId());
            }
            
        }
        
      //@invalid ************取赔案层下所有保单的保额取和，此处确认基本保额到责任组层，所以将责任组层的基本保额求和--工单 INC-20201127-0074**********
        ContractProductPO contractProductNewPO = new ContractProductPO();
        contractProductNewPO.setCaseId(claimCaseBO.getCaseId());
        contractProductNewPO.setCurFlag(new BigDecimal(ClaimConstant.ONE));
        List<ContractProductPO> contractProductNewPOs = contractProductDao.findAllContractProduct(contractProductNewPO);
        for (ContractProductPO contractProductPO2 : contractProductNewPOs) {
        	//@invalid 张春梦要求sum_amnt取当前时点抄单的Amoun  工单 INC-20201127-0074
        	//@invalid 计算涉案险种的保额***INC-20210719-0189保额只要涉案险种
    		if(busiItemIdList.contains(contractProductPO2.getBusiItemId())){
    			beforeSurplusEffectAmount = beforeSurplusEffectAmount.add(contractProductPO2.getAmount());
    		}
		}
        //@invalid claim和claim1需要的 账单汇总同一处理
        //@invalid 获取赔案层的最大实际天数
        //@invalid 汇总赔案层的总天数
        BigDecimal dayCount = new BigDecimal(ClaimConstant.ZERO);
        BigDecimal fee = new BigDecimal(ClaimConstant.ZERO);
        //@invalid 张春梦要求adjsum（claim节点）账单信息取理算金额  工单 INC-20201127-0074
        BigDecimal adjsum1 = new BigDecimal(ClaimConstant.ZERO);
        for(int i = 0; i < claimBillPOs.size(); i++){
            if(claimBillPOs.get(i).getDayTotal() != null){
                dayCount = dayCount.add(claimBillPOs.get(i).getDayTotal());
            }
            if(claimBillPOs.get(i).getSumAmount() != null){
                fee = fee.add(claimBillPOs.get(i).getSumAmount());
            }
            if(claimBillPOs.get(i).getCalcAmount() != null){
                adjsum1 = adjsum1.add(claimBillPOs.get(i).getCalcAmount());
            }
           
        }
        
        //@invalid 逆选择保项层信息
        for(ClaimLiabPO claimLiab : claimLiabPOs){
            //@invalid 计算调整金额
//@invalid             adjsum = adjsum.add(claimLiab.getAdjustPay()); //@invalid 赔案层调整金额的和
            com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Duty duty = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Duty();
            duty.setCaseno(claimCaseBO.getCaseNo());//@invalid 分案号
            duty.setPolno(claimLiab.getBusiItemId().toString());//@invalid 险种号
            //@invalid  暂定前六位 张春梦确认
            if(!ClaimConstant.NULL_STR.equals(claimLiab.getLiabCode())){
            	duty.setDutycode(claimLiab.getLiabCode().substring(0, 6));//@invalid 责任编码
            	duty.setGetdutycode(claimLiab.getLiabCode().substring(0, 6));//@invalid 给付责任编码
            }
            //@invalid 只有医疗的才可以进入此接口，所以理赔类型默认为医疗00   53418修改只有医疗调用
            if(claimSubCasePOs.get(0).getAccReason() != null){
                duty.setGetdutykind(ClaimConstant.claimTypeMarray(claimSubCasePOs.get(0).getAccReason().toString(), claimLiab.getClaimType()));//@invalid 给付责任类型
            }
            
            duty.setCaserelano(claimLiab.getClaimLiabId().toString()); //@invalid 受理事故号   claimLianID
            duty.setContno(claimLiab.getPolicyCode()); //@invalid 个单合同号
            duty.setRiskcode(claimLiab.getBusiProdCode());//@invalid 险种代码
            duty.setStandpay(claimLiab.getCalcPay());//@invalid 核算赔付金额
            duty.setSum_standpay(sumStandpay);//@invalid 核算赔付金额的和 
            //@invalid 查询代理人代码
            ContractAgentPO contractAgentPO = new ContractAgentPO();
            contractAgentPO.setCaseId(claimCaseBO.getCaseId());
            contractAgentPO.setCurFlag(new BigDecimal(ClaimConstant.ONE));
            contractAgentPO.setPolicyCode(claimLiab.getPolicyCode());
            List<ContractAgentPO> contractAgentPOs = contractAgentDao.findAllContractAgent(contractAgentPO);
            if(contractAgentPOs.size() > 0){
                duty.setAgentcode(contractAgentPOs.get(0).getAgentCode()); //@invalid 代理人编码
            }
            duty.setSum_amnt(beforeSurplusEffectAmount); //@invalid 保额
            //@invalid 查询投保人客户号
            PolicyHolderPO  policyHolderPO = new PolicyHolderPO();
            policyHolderPO.setCaseId(claimCaseBO.getCaseId());
            policyHolderPO.setCurFlag(new BigDecimal(ClaimConstant.ONE));
            policyHolderPO.setPolicyCode(claimLiab.getPolicyCode());
            List<PolicyHolderPO> policyHolderPOs = policyHolderDao.findAllPolicyHolder(policyHolderPO);
            if(policyHolderPOs.size() > 0){
                duty.setAppntno(policyHolderPOs.get(0).getCustomerId()+"");
            }
            //@invalid 被保人客户号
            InsuredListPO insuredListPO = new InsuredListPO();
            insuredListPO.setCaseId(claimCaseBO.getCaseId());
            insuredListPO.setCurFlag(new BigDecimal(ClaimConstant.ONE));
            insuredListPO.setPolicyCode(claimLiab.getPolicyCode());
            List<InsuredListPO> insuredListPOs = insuredListDao.findAllInsuredList(insuredListPO);
            if(insuredListPOs.size() > 0){
                duty.setInsuredno(insuredListPOs.get(0).getCustomerId()+"");
            }
            //@invalid 险种生效日期
            ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
            contractBusiProdPO.setCaseId(claimLiab.getCaseId());
            contractBusiProdPO.setPolicyCode(claimLiab.getPolicyCode());
            contractBusiProdPO.setBusiProdCode(claimLiab.getBusiProdCode());
            contractBusiProdPO.setCurFlag(new BigDecimal(ClaimConstant.ONE));
            List<ContractBusiProdPO> contractBusiProdPOs = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPO);
            if(contractBusiProdPOs.size() > 0){
            	if(contractBusiProdPOs.get(0).getFirstValidateDate() != null){
                    duty.setCvalidate(sf.format(contractBusiProdPOs.get(0).getFirstValidateDate()));
            		}else if(contractBusiProdPOs.get(0).getInitialValidateDate() != null){
                    //@invalid 险种生效日期
                		duty.setCvalidate(sf.format(contractBusiProdPOs.get(0).getInitialValidateDate()));
                		}
                if(contractBusiProdPOs.get(0).getApplyDate() != null){
                    //@invalid 签单日期
                    duty.setSigndate(sf.format(contractBusiProdPOs.get(0).getApplyDate()));
                }
                if(contractBusiProdPOs.get(0).getRerinstateDate() != null){
                  //@invalid 复效日期
                    duty.setLastrevdate(sf.format(contractBusiProdPOs.get(0).getRerinstateDate()));
                } else {
                    duty.setLastrevdate("");
                }
                if(contractBusiProdPOs.get(0).getExpiryDate() != null){
                  //@invalid 保险责任终止日期
                    duty.setEnddate(sf.format(contractBusiProdPOs.get(0).getExpiryDate()));
                }
                
                //@invalid 查询险种是否是意外险，如果是赋值，如果不是为空
                BusinessProductPO businessProductPO = new BusinessProductPO();
                businessProductPO.setProductCodeSys(claimLiab.getBusiProdCode());
                businessProductPO = businessProductDao.findBusinessProductByProductCodeSYS(businessProductPO);
                if(businessProductPO.getProductNameSys().indexOf("意外") != -1){
                    if(contractBusiProdPOs.get(0).getExpiryDate() != null){
                        duty.setAccienddate(sf.format(contractBusiProdPOs.get(0).getExpiryDate()));
                    } else {
                        duty.setAccienddate("");
                    }
                } else {
                    duty.setAccienddate("");
                }
            }
            //@invalid 查询总基本保额   险种层
            ContractProductPO contractProductPO = new ContractProductPO();
            contractProductPO.setCaseId(claimLiab.getCaseId());
            contractProductPO.setCurFlag(new BigDecimal(ClaimConstant.ONE));
            contractProductPO.setPolicyCode(claimLiab.getPolicyCode());
            contractProductPO.setBusiItemId(claimLiab.getBusiItemId());
            contractProductPO.setItemId(claimLiab.getItemId());
            List<ContractProductPO> contractProductPOs = contractProductDao.findAllContractProduct(contractProductPO);
            if(contractProductPOs.size() > 0){
                duty.setAmnt(contractProductPOs.get(0).getAmount());
            }
            dutyList.add(duty);
        }
        claim.setAdjsum(adjsum1);//@invalid 调整金额
        claim.setDuty(dutyList);
        //@invalid --------------------------------------------逆选择保项层信息--------------------------------------

        BigDecimal dayCountZy = new BigDecimal(ClaimConstant.ZERO);
        BigDecimal feeZy = new BigDecimal(ClaimConstant.ZERO);
        BigDecimal adjsum1Zy = new BigDecimal(ClaimConstant.ZERO);
        //@invalid 虚假发票
        claim1.setClmno(claimCaseBO.getCaseNo()); //@invalid 赔案号
        claim1.setCaseno(claimCaseBO.getCaseNo()); //@invalid 分案号

        claim1.setDaycount(dayCount+"");//@invalid 实际天数
        //@invalid 汇总赔案层住院的天数
        claimBillPO.setTreatType(ClaimConstant.TREAT_TYPE_HOSPITALIZATION);
        List<ClaimBillPO> claimBillPOList = claimBillDao.findAllClaimBillCount(claimBillPO);
        for(int i = 0; i < claimBillPOList.size(); i++){
            if(claimBillPOList.get(i).getDayTotal() != null){
                dayCountZy = dayCountZy.add(claimBillPOList.get(i).getDayTotal()); 
            }
            if(claimBillPOs.get(i).getSumAmount() != null){
                feeZy = feeZy.add(claimBillPOs.get(i).getSumAmount()); 
            }
            if(claimBillPOs.get(i).getCalcAmount() != null){
                adjsum1Zy = adjsum1Zy.add(claimBillPOs.get(i).getCalcAmount()); 
            }
        }
        claim1.setDaycount_zy(dayCountZy+"");//@invalid 住院实际天数
        claim1.setFee(fee); //@invalid 费用金额
        claim1.setAdjsum(adjsum1); //@invalid 调整金额
        claim1.setFee_zy(feeZy); //@invalid 住院费用金额
        claim1.setAdjsum_zy(adjsum1Zy);  //@invalid 住院调整金额
        if(claimAccidentResultPOs.size() > 0){
            //@invalid 出险结果取最新的一条数据
            claim1.setAccresult1(claimAccidentResultPOs.get(0).getAccResult1());//@invalid 出险结果1
            claim1.setAccresult2(claimAccidentResultPOs.get(0).getAccResult2());//@invalid 出险结果2
        }
        if(claimCaseBO.getRegisteTime()!=null){
        	claim1.setRgtdate(sf.format(claimCaseBO.getRegisteTime())); //@invalid 立案日期
        }else{
        	claim1.setRgtdate(sf.format(WorkDateUtil.getWorkDate())); //@invalid 立案日期
        }
        if(claimCaseBO.getCureHospital()!=null){
            claim1.setHospitalcode(claimCaseBO.getCureHospital()); //@invalid 医院代码  核对案件信息录入的医院代码
        }else{
        	claim1.setHospitalcode("");//@invalid 医院代码  赔案页面上的唯一一条
        }
//@invalid             claim1.setHospitalcode(claimCaseBO.getCureHospital()); //@invalid 医院代码
        claim1.setConttype(ClaimConstant.SHAREQUERYTASKTYPE); //@invalid 总单类型(1-个人总投保单)
        claim1.setFrms_biz_code("个险理赔");//@invalid 应用渠道(接口需求要求传固定值)
        claim1.setFrms_biz_type("疾病医疗");//@invalid 业务类型(接口需求要求传固定值)
        //@invalid 发票节点
        for(int i = 0; i < claimBillPOs.size(); i++){
            
            com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Invoice invoice = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Invoice();
            invoice.setMainfeeno(claimBillPOs.get(i).getBillNo()); //@invalid 账单号码
            invoice.setCustomerno(claimCaseBO.getInsuredId()+""); //@invalid 出险人客户号
            //@invalid 转换成老核心的码值   不转换老核心
            if(StringUtils.isNotBlank(claimCaseBO.getCaseStatus())){
                String caseStatus = CodeMapperUtils.getOldCodeByNewCode("CASE_STATUS", claimCaseBO.getCaseStatus(), "CLM");
                invoice.setClmstate(caseStatus); //@invalid 赔案状态 
            }
            invoice.setHospitalcode(claimBillPOs.get(i).getHospitalCode()); //@invalid 医院代码
            //@invalid 查询医院名称
            if(StringUtils.isNotBlank(claimBillPOs.get(i).getHospitalCode())){
                String hospitalname = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_HOSPITAL", claimBillPOs.get(i).getHospitalCode());
                invoice.setHospitalname(hospitalname); //@invalid 医院名称
            }
            //@invalid 查询出险人名称
            CustomerPO customerPO = new CustomerPO();
            customerPO.setCustomerId(claimCaseBO.getInsuredId());
            customerPO = customerDao.findCustomerByCustomerId(customerPO);
            invoice.setCustomername(customerPO.getCustomerName()); //@invalid 出险人名称
            if("0".equals(claimBillPOs.get(i).getTreatType())){ //@invalid 门诊0-A   住院1-B
            	invoice.setFeetype("A"); //@invalid 账单种类
            }else{
            	invoice.setFeetype("B"); //@invalid 账单种类
            }
            invoice.setHospstartdate(sf.format(claimBillPOs.get(i).getTreatStart())); //@invalid 住院起始日期
            invoice.setHospenddate(sf.format(claimBillPOs.get(i).getTreatEnd())); //@invalid 住院结束日期
            //@invalid 获取账单的操作人员
            if(claimBillPOs.get(i).get("update_by") != null){
                BigDecimal userId = (BigDecimal)claimBillPOs.get(i).get("update_by");
                String username = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_UDMP_USER", userId+"");
                invoice.setOperator(username); //@invalid 操作人员
            }
            invoice.setIdno(customerPO.getCustomerCertiCode()); //@invalid 出险人身份证号码
            //@invalid 转换成老核心的码值
            if(StringUtils.isNotBlank(customerPO.getCustomerCertType())){
                String certiType = CodeMapperUtils.getOldCodeByNewCode("CERTI_TYPE", customerPO.getCustomerCertType(), "CLM");
                invoice.setIdtype(certiType); //@invalid 出险人身份证类型
            }
            
            invoiceList.add(invoice);
        }
        //@invalid esb报文校验
        if(claimBillPOs.size()==0){
        	//@invalid 默认赋值空
        	com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Invoice invoice = new com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.Invoice();
            invoice.setMainfeeno("0"); //@invalid 账单号码  esb校验 如果无值，请传默认值0，跟反欺诈确认（张春梦）
            if(claimCaseBO.getInsuredId()!=null){
            	invoice.setCustomerno(claimCaseBO.getInsuredId().toString()); //@invalid 出险人客户号
            }else{
            	invoice.setCustomerno("000"); //@invalid 出险人客户号 esb校验防止为空
            }
            invoice.setClmstate(""); //@invalid 赔案状态 
            invoice.setHospitalcode(""); //@invalid 医院代码
            //@invalid 查询医院名称
            invoice.setHospitalname(""); //@invalid 医院名称
            //@invalid 查询出险人名称
            invoice.setCustomername(""); //@invalid 出险人名称
        	invoice.setFeetype(""); //@invalid 账单种类
            invoice.setHospstartdate(""); //@invalid 住院起始日期
            invoice.setHospenddate(""); //@invalid 住院结束日期
            invoice.setOperator(""); //@invalid 操作人员
            invoice.setIdno(""); //@invalid 出险人身份证号码
            invoice.setIdtype(""); //@invalid 出险人身份证类型
            
            invoiceList.add(invoice);
        }
        
        claim1.setInvoice(invoiceList);
        inputData.setClaim1(claim1);
        inputData.setClaim(claim);
        
        
        srvReqBizBody.setInputData(inputData);
        parametersReqBody.setBizBody(srvReqBizBody);

        parametersReqBody.setBizHeader(SRVReqHead);
        port.claimListQuery(parametersReqHeader, parametersReqBody, parametersResHeader, parametersResBody);
        OutputData outputData = null;
        if(parametersResBody.value.getBizBody()!=null){
        	outputData = parametersResBody.value.getBizBody().getOutputData();
        }else{
        	return null;
        }
        //@invalid 逆选择模型结果
        List<ModelResult> modelResult = new ArrayList<ModelResult>();
        if(outputData.getModelResult() !=null){
        	modelResult = outputData.getModelResult();
        }
        //@invalid 伪造发票模型结果
        List<ModelResult1> modelResult1 = outputData.getModelResult1();
        if(outputData.getModelResult1() != null){
            modelResult1 = outputData.getModelResult1();
        }
        //@invalid 风险疑点汇总集合save
        List<ClaimRiskDoubtfulInfoPO> claimRiskDoubtfulInfoPOList = new ArrayList<ClaimRiskDoubtfulInfoPO>();
        //@invalid 风险疑点汇总集合update
        List<ClaimRiskDoubtfulInfoPO> claimRiskDoubtfulInfoPOUpdateList = new ArrayList<ClaimRiskDoubtfulInfoPO>();
        //@invalid 出险原因是意外不处理逆选择
        if(accReasonFlag){
        	//@invalid 逆选择模型结果
        	for (ModelResult modelResult2 : modelResult) {
        		//@invalid 查询责任名称 责任编码
        		ClaimLiabPO claimLiabPOResult = new ClaimLiabPO();
        		//@invalid 赔案id
        		claimLiabPOResult.setCaseId(claimCaseBO.getCaseId());
        		//@invalid 险种号（险种id）
        		claimLiabPOResult.setBusiItemId(new BigDecimal(modelResult2.getPolno()));
        		//@invalid 受理事故号caserelano为新核心的claimLiabID
        		claimLiabPOResult.setClaimLiabId(new BigDecimal(modelResult2.getCaserelano()));
        		List<ClaimLiabPO> findAllClaimLiab = claimLiabDao.findAllClaimLiab(claimLiabPOResult);
        		ClaimRiskLevelLiabPO claimRiskLevelLiabPO = new ClaimRiskLevelLiabPO();
        		//@invalid 赔案id
        		claimRiskLevelLiabPO.setCaseId(claimCaseBO.getCaseId());
        		//@invalid 保单号
        		claimRiskLevelLiabPO.setPolicyCode(findAllClaimLiab.get(0).getPolicyCode());
        		//@invalid 责任编码
        		claimRiskLevelLiabPO.setLiabCode(findAllClaimLiab.get(0).getLiabCode());
        		if(findAllClaimLiab.size()>0){
        			claimRiskLevelLiabPO.setLiabName(findAllClaimLiab.get(0).getLiabName());//@invalid 责任名称
        		}
        		//@invalid 风险名称
        		claimRiskLevelLiabPO.setRiskName(modelResult2.getModelName());
        		claimRiskLevelLiabPO.setRiskBlackFraction(new BigDecimal(modelResult2.getProbability()));
        		//@invalid 查询此保项风险数据是否存在
        		List<ClaimRiskLevelLiabPO> findAllClaimRiskLevelLiab = claimRiskLevelLiabDao.findAllClaimRiskLevelLiab(claimRiskLevelLiabPO);
        		if(findAllClaimRiskLevelLiab.size()==0){
        			//@invalid 引擎返回概率 数据落库时处理*100
        			claimRiskLevelLiabPO.setRiskBlackFraction(new BigDecimal(modelResult2.getProbability()));
        			claimRiskLevelLiabPO.setRiskFraction(new BigDecimal(modelResult2.getProbability()).multiply(ClaimConstant.BIGDECIMAL_ONE_HUNDRED).setScale(2, BigDecimal.ROUND_HALF_UP));
        			claimRiskLevelLiabDao.addClaimRiskLevelLiab(claimRiskLevelLiabPO);
        			//@invalid 处理疑点数据
        			//@invalid 由于addClaimRiskLevelLiab方法返回的RiskLiabId是Integer但是ClaimRiskLevelLiabPO定义的是BigDecimal
        			ClaimRiskLevelLiabPO findClaimRiskLevelLiab = claimRiskLevelLiabDao.findClaimRiskLevelLiab(claimRiskLevelLiabPO);
        			String modelExplanation = modelResult2.getModelExplanation(); //@invalid 模型解释代码  逗号拼接的
        			if(!ClaimConstant.NULL_STR.equals(modelExplanation)){
        				//@invalid 按照逗号分割
        				String[] split = modelExplanation.split(ClaimConstant.COMMA);
        				for (String string : split) {
        					ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO = new ClaimRiskDoubtfulInfoPO();
        					claimRiskDoubtfulInfoPO.setCaseId(claimCaseBO.getCaseId());
        					claimRiskDoubtfulInfoPO.setRiskLiabId(findClaimRiskLevelLiab.getRiskLiabId());
        					//@invalid 报文返回存在空格
        					claimRiskDoubtfulInfoPO.setDoubtfulCode(string.trim());
        					claimRiskDoubtfulInfoPOList.add(claimRiskDoubtfulInfoPO);
        				}
        			}
        		}else{  //@invalid 如责任在风险表中存在作修改操作
        			//@invalid 引擎返回概率 数据落库时处理*100
        			findAllClaimRiskLevelLiab.get(0).setRiskBlackFraction(new BigDecimal(modelResult2.getProbability()));
        			findAllClaimRiskLevelLiab.get(0).setRiskFraction(new BigDecimal(modelResult2.getProbability()).multiply(ClaimConstant.BIGDECIMAL_ONE_HUNDRED).setScale(2, BigDecimal.ROUND_HALF_UP));
        			claimRiskLevelLiabDao.updateClaimRiskLevelLiab(findAllClaimRiskLevelLiab.get(0));
        			//@invalid 处理疑点数据
        			String modelExplanation = modelResult2.getModelExplanation(); //@invalid 模型解释代码  逗号拼接的
        			if(!ClaimConstant.NULL_STR.equals(modelExplanation)){
        				//@invalid 按照逗号分割
        				String[] split = modelExplanation.split(ClaimConstant.COMMA);
        				for (String string : split) {
        					ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO = new ClaimRiskDoubtfulInfoPO();
        					claimRiskDoubtfulInfoPO.setCaseId(claimCaseBO.getCaseId());
        					claimRiskDoubtfulInfoPO.setRiskLiabId(findAllClaimRiskLevelLiab.get(0).getRiskLiabId());
        					//@invalid 报文返回存在空格
        					claimRiskDoubtfulInfoPO.setDoubtfulCode(string.trim());
        					List<ClaimRiskDoubtfulInfoPO> findAllClaimRiskDoubtfulInfo = claimRiskDoubtfulInfoDao.findAllClaimRiskDoubtfulInfo(claimRiskDoubtfulInfoPO);
        					if(findAllClaimRiskDoubtfulInfo.size()>0){
        						claimRiskDoubtfulInfoPO.setDoubtfulInfoId(findAllClaimRiskDoubtfulInfo.get(0).getDoubtfulInfoId());
        						claimRiskDoubtfulInfoPO.setCheatAtlasId(null);
        						claimRiskDoubtfulInfoPO.setOrderNumber(null);
        						claimRiskDoubtfulInfoPO.setRecognizedFlag(null);
        						claimRiskDoubtfulInfoPOUpdateList.add(claimRiskDoubtfulInfoPO);
        					}else{
        						claimRiskDoubtfulInfoPOList.add(claimRiskDoubtfulInfoPO);
        					}
        				}
        			}
        		}
        	}
        }
        //@invalid 伪造发票模型结果
        for (ModelResult1 modelResult12 : modelResult1) {
        	ClaimRiskLevelLiabPO claimRiskLevelLiabPO = new ClaimRiskLevelLiabPO();
        	claimRiskLevelLiabPO.setCaseId(claimCaseBO.getCaseId());//@invalid 赔案id
        	claimRiskLevelLiabPO.setRiskName(modelResult12.getModelName());//@invalid 风险名称
        	//@invalid 伪造发票引擎返回概率 就是百分制
			//@invalid 查询此保项风险数据是否存在
			List<ClaimRiskLevelLiabPO> findAllClaimRiskLevelLiab = claimRiskLevelLiabDao.findAllClaimRiskLevelLiab(claimRiskLevelLiabPO);
			if(findAllClaimRiskLevelLiab.size()==0){
				//@invalid 添加数据返回
				claimRiskLevelLiabPO.setRiskBlackFraction(new BigDecimal(modelResult12.getProbability()));//@invalid 引擎返回概率
				claimRiskLevelLiabPO.setRiskFraction(new BigDecimal(modelResult12.getProbability()));//@invalid 引擎返回概率
				claimRiskLevelLiabDao.addClaimRiskLevelLiab(claimRiskLevelLiabPO);
				//@invalid 处理疑点数据
				//@invalid 由于addClaimRiskLevelLiab方法返回的RiskLiabId是Integer但是ClaimRiskLevelLiabPO定义的是BigDecimal
				ClaimRiskLevelLiabPO findClaimRiskLevelLiab = claimRiskLevelLiabDao.findClaimRiskLevelLiab(claimRiskLevelLiabPO);
				String modelExplanation = modelResult12.getModelExplanation(); //@invalid 模型解释代码  逗号拼接的
				if(!ClaimConstant.NULL_STR.equals(modelExplanation)){
					//@invalid 按照逗号分割
					String[] split = modelExplanation.split(ClaimConstant.COMMA);
					for (String string : split) {
						ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO = new ClaimRiskDoubtfulInfoPO();
						claimRiskDoubtfulInfoPO.setCaseId(claimCaseBO.getCaseId());
						claimRiskDoubtfulInfoPO.setRiskLiabId(findClaimRiskLevelLiab.getRiskLiabId());
						//@invalid 报文返回存在空格
						claimRiskDoubtfulInfoPO.setDoubtfulCode(string.trim());
						claimRiskDoubtfulInfoPOList.add(claimRiskDoubtfulInfoPO);
					}
				}
			}else{  //@invalid 如虚假发票在风险表中存在作修改操作
				//@invalid 引擎返回概率 数据落库时处理*100
				findAllClaimRiskLevelLiab.get(0).setRiskBlackFraction(new BigDecimal(modelResult12.getProbability()));//@invalid 引擎返回概率
				findAllClaimRiskLevelLiab.get(0).setRiskFraction(new BigDecimal(modelResult12.getProbability()));//@invalid 引擎返回概率
				claimRiskLevelLiabDao.updateClaimRiskLevelLiab(findAllClaimRiskLevelLiab.get(0));
				//@invalid 处理疑点数据
				String modelExplanation = modelResult12.getModelExplanation(); //@invalid 模型解释代码  逗号拼接的
				if(!ClaimConstant.NULL_STR.equals(modelExplanation)){
					//@invalid 按照逗号分割
					String[] split = modelExplanation.split(ClaimConstant.COMMA);
					for (String string : split) {
						ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO = new ClaimRiskDoubtfulInfoPO();
						claimRiskDoubtfulInfoPO.setCaseId(claimCaseBO.getCaseId());
						claimRiskDoubtfulInfoPO.setRiskLiabId(findAllClaimRiskLevelLiab.get(0).getRiskLiabId());
						//@invalid 报文返回存在空格
						claimRiskDoubtfulInfoPO.setDoubtfulCode(string.trim());
						List<ClaimRiskDoubtfulInfoPO> findAllClaimRiskDoubtfulInfo = claimRiskDoubtfulInfoDao.findAllClaimRiskDoubtfulInfo(claimRiskDoubtfulInfoPO);
						if(findAllClaimRiskDoubtfulInfo.size()>0){
							claimRiskDoubtfulInfoPO.setDoubtfulInfoId(findAllClaimRiskDoubtfulInfo.get(0).getDoubtfulInfoId());
							claimRiskDoubtfulInfoPO.setCheatAtlasId(null);
							claimRiskDoubtfulInfoPO.setOrderNumber(null);
							claimRiskDoubtfulInfoPO.setRecognizedFlag(null);
							claimRiskDoubtfulInfoPOUpdateList.add(claimRiskDoubtfulInfoPO);
						}else{
							claimRiskDoubtfulInfoPOList.add(claimRiskDoubtfulInfoPO);
						}
					}
				}
			}
		}
        //@invalid 新增风险疑点
        if(claimRiskDoubtfulInfoPOList.size()>0){
        	claimRiskDoubtfulInfoDao.batchSaveClaimRiskDoubtfulInfo(claimRiskDoubtfulInfoPOList);
        }else if(claimRiskDoubtfulInfoPOUpdateList.size()>0){//@invalid 如果存在就修改
        	claimRiskDoubtfulInfoDao.batchUpdateClaimRiskDoubtfulInfo(claimRiskDoubtfulInfoPOUpdateList);
        }
        logger.debug("********************************模型引擎接口处理完成*******************************");
        return parametersResBody.value.getBizBody().getOutputData();
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("模型引擎接口处理异常赔案id" + claimCaseBO.getCaseId());
        }
        return null;
       
    }
	
	/**
	 * 赔案触发图规则查询
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseBO 赔案基本信息
	 */
	public com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.OutputData queryCaseFrmsInfo(ClaimCaseBO claimCaseBO){
		try{
			//调用图规则接口查询
			URL wsdlURL = ClaimRuleQuerySrvBindingQSService.WSDL_LOCATION;
            String wsdlString = wsdlURL.toString();
//@invalid             wsdlString = "http://**********:8111/services/P00001900098?wsdl";
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdlString, "com.nci.tunan.clm.interfaces.queryCaseFrms.ClaimRuleQuerySrvPortType");
            ClaimRuleQuerySrvPortType port = (ClaimRuleQuerySrvPortType)soapFactory.create();
            //@invalid  定义系统报文头
            com.nci.tunan.clm.interfaces.queryCaseFrms.common.header.in.SysMsgHeader parametersReqHeader = new com.nci.tunan.clm.interfaces.queryCaseFrms.common.header.in.SysMsgHeader();
			parametersReqHeader.setMsgId(UUID.randomUUID().toString());
			parametersReqHeader.setMsgDate(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), PubConstants.DATE_FORMAT));
			parametersReqHeader.setMsgTime(DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), PubConstants.TIME_FORMAT_ESB));
			parametersReqHeader.setServCd("P00001900098");
			parametersReqHeader.setSysCd("067"); 
			parametersReqHeader.setBizId("00000");
			parametersReqHeader.setBizType("");
			parametersReqHeader.setOrgCd("86");
			parametersReqHeader.setResCd("");
			parametersReqHeader.setResText("");
			parametersReqHeader.setBizResCd("");
			parametersReqHeader.setBizResText("");
			parametersReqHeader.setVer("110.100.000");
			//@invalid  定义系统报文体
			com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvReqBody parametersReqBody = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvReqBody();
			javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.common.header.in.SysMsgHeader> parametersResHeader = new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.common.header.in.SysMsgHeader>();
			javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvResBody> parametersResBody = new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvResBody>();
            //@invalid  定义业务报文体
			com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvReqBizBody srvReqBizBody = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvReqBizBody();
            parametersReqBody.setBizBody(srvReqBizBody);
            //@invalid 定义业务报文头
            com.nci.tunan.clm.interfaces.queryCaseFrms.service.hd.SRVReqHead ReqHead = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.hd.SRVReqHead();
            parametersReqBody.setBizHeader(ReqHead);
            
            com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.InputData inputdate= new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.InputData();
			//@invalid 入参赔案信息
            com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Claim claim = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Claim();
		    //@invalid 入参医院信息列表
            com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Hops hoslist = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Hops();
		    List<com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Hop> hops = hoslist.getHop(); 
		    //@invalid 入参保单信息列表
		    com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Conts contlist = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Conts();
		    List<com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Cont> conts = contlist.getCont();
		    //@invalid 理赔类型列表
		    com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.SubcaseList subcaseList = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.SubcaseList();
		    List<com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Subcase> subcases = subcaseList.getSubcase();

			
			//@invalid 查询出险日期(多个取最早的出险日期)
			ClaimSubCasePO claimSubCasePO = new ClaimSubCasePO();
			claimSubCasePO.setCaseId(claimCaseBO.getCaseId());
			List<ClaimSubCasePO> claimSubCasePOs = claimSubCaseDao.findClaimSubCaseByCaseId(claimSubCasePO);
			//@invalid 查询赔案信息
			ClaimCasePO claimCasePO = new ClaimCasePO();
			claimCasePO.setCaseId(claimCaseBO.getCaseId());
			claimCasePO = claimCaseDao.findCaseFrmsInfo(claimCasePO);
			
			//@invalid 查询医院信息List
			ClaimBillPO claimBillPO = new ClaimBillPO();
			claimBillPO.setCaseId(claimCaseBO.getCaseId());
		    List<ClaimBillPO> claimBillPOs = claimBillDao.findCaseFrmsHospInfo(claimBillPO);
			//@invalid 查询保单信息List
		    ClaimBusiProdPO claimBusiProdPO = new ClaimBusiProdPO();
		    claimBusiProdPO.setCaseId(claimCaseBO.getCaseId());
		    List<ClaimBusiProdPO> claimBusiProdPOs = claimBusiProdDao.findCaseFrmsProdInfo(claimBusiProdPO);
		    //@invalid 给入参赋值
		    //@invalid 赔案号
		    claim.setFrmsClmno(claimCasePO.getCaseNo());
		    //@invalid 账单金额
		    if (claimCasePO.getData().get("sum_amount") != null) {
		    	claim.setFrmsTabfeemoney(claimCasePO.getData().get("sum_amount").toString());
		    } else {
		    	claim.setFrmsTabfeemoney("");
		    }
		    //@invalid 理算金额
		    if (claimCasePO.getCalcPay() != null) {
            	logger.info("图谱规则接口理算金额{}",claimCaseBO.getActualPay());
            	 claim.setFrmsClaimStandpay(claimCasePO.getCalcPay().toString());
            }else {
            	ClaimLiabPO claimLiabPO = new ClaimLiabPO();
            	claimLiabPO.setCaseId(claimCaseBO.getCaseId());
				List<ClaimLiabPO> findAllClaimLiabByCaseID = claimLiabDao.findAllClaimLiab(claimLiabPO);
				BigDecimal benefitAmntAdjust = BigDecimal.ZERO;
				//如果赔案层金额为空则汇总责任层金额
				for (ClaimLiabPO claimLiabPO2 : findAllClaimLiabByCaseID) {
					if(claimLiabPO2.getCalcPay()!=null){
						benefitAmntAdjust = benefitAmntAdjust.add(claimLiabPO2.getCalcPay());
					}
				}
				logger.info("图谱规则接口理算金额{}",benefitAmntAdjust);
				claim.setFrmsClaimStandpay(benefitAmntAdjust.toString()); // 给付金额调整
            }
		    
		    
		   
		    //@invalid 报案手机号(如果是11位直接获取，如果不是11位，则取空)
		    if (claimCasePO.getRptrMp() != null) {
		    	claim.setFrmsPhone(claimCasePO.getRptrMp());
		    } else {
		    	claim.setFrmsPhone("");
		    }
		    //@invalid 申请人手机号(多个用英文分号分隔)
		    if (claimCasePO.getData().get("clmt_mp") != null) {
		    	claim.setFrmsRgtantphone((String)claimCasePO.getData().get("clmt_mp"));
		    } else {
		    	claim.setFrmsRgtantphone("");
		    	}
		    //@invalid 出险人号
		    claim.setFrmsClaimUser(claimCasePO.getInsuredId().toString());
		    //@invalid 出险人年龄
		    if (claimCasePO.getData().get("customer_age") != null) {
		    	claim.setFrmsAccidentAge(claimCasePO.getData().get("customer_age").toString());
		    } else {
		    	claim.setFrmsAccidentAge("");
		    }
		    //@invalid 住院时长
		    if (claimCasePO.getData().get("day_total") != null) {
		    	claim.setFrmsInHospitalTime(String.valueOf(claimCasePO.getData().get("day_total")));
		    } else {
		    	claim.setFrmsInHospitalTime("");
		    }
		    //@invalid 意外细节代码
		    if (claimCasePO.getAccidentDetail() != null) {
		    	claim.setFrmsAccident((String)claimCasePO.getAccidentDetail());
		    } else {
		    	claim.setFrmsAccident("");
		    }
		    //@invalid 出险结果1代码
		    if (claimCasePO.getData().get("acc_result1") != null) {
		    	claim.setFrmsIcd1((String)claimCasePO.getData().get("acc_result1"));
		    } else {
		    	claim.setFrmsIcd1("");
		    }
		    //@invalid 出险结果2代码
		    if (claimCasePO.getData().get("acc_result2") != null) {
		    	claim.setFrmsIcd2((String)claimCasePO.getData().get("acc_result2"));
		    } else {
		    	claim.setFrmsIcd2("");
		    }
		    //@invalid 出险日期(取赔案的最早出险日期)
		    SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");  
		    claim.setFrmsClaimAccdate(dateformat.format(claimSubCasePOs.get(0).getClaimDate()));
		    //@invalid 事故日期
		    claim.setFrmsAccidentdate(dateformat.format(claimCasePO.getData().get("acc_date")));
		    //@invalid 立案日期
		    if(claimCasePO.getRegisteTime()!=null){
		    	claim.setFrmsRgtdate(dateformat.format(claimCasePO.getRegisteTime()));
		    }else{
		    	claim.setFrmsRgtdate(dateformat.format(WorkDateUtil.getWorkDate()));
		    }
		    //@invalid 出险人身份证前6位
		    if (claimCasePO.getData().get("customer_id_code") != null) {
		    	claim.setFrmsIdno6((String)claimCasePO.getData().get("customer_id_code"));
		    } else {
		    	claim.setFrmsIdno6("");
		    }
		    //@invalid 当前日期往前两年时间
//@invalid 		    claim.setFrmsClaimTime(dateformat.format(DateUtilsEx.addYear(new Date(), -2)));
		    //@invalid 测试环境获取用户时间  测试完成要注释
		    AppUser appUser = new AppUser();
		    appUser.setUserName("SYSADMIN");
		    claim.setFrmsClaimTime(dateformat.format(DateUtilsEx.addYear(WorkDateUtil.getWorkDate(appUser), -2)));
		    //@invalid 赔案核赔赔付金额
		    if (claimCasePO.getActualPay() != null) {
		    	claim.setFrmsRealpay(claimCasePO.getActualPay().toString());
		    } else {
		    	claim.setFrmsRealpay("");
		    }
		    //@invalid 理赔结论
		    if (claimCasePO.getAuditDecision() != null) {
		    	claim.setFrmsClaimGivetype(claimCasePO.getAuditDecision().toString());
		    } else {
		    	claim.setFrmsClaimGivetype("");
		    }
		    //@invalid 渠道
//@invalid 		    if (claimCasePO.getChannelCode() != null) {
	    	claim.setFrmsClaimConttype("个单");
//@invalid 		    } else {
//@invalid 		    	claim.setFrmsClaimConttype("");
//@invalid 		    }
		    //@invalid 出险人出生日期
		    if (claimCasePO.getData().get("customer_birthday") != null) {
		    	claim.setFrmsPersonbirthday((String)claimCasePO.getData().get("customer_birthday"));
		    } else {
		    	claim.setFrmsPersonbirthday("");
		    }
		    //@invalid 受理机构
		    claim.setFrmsComCode(claimCasePO.getOrganCode());
		    //@invalid 客户2年内理赔调查标识 
		    String positiveResult = getPositiveResult(claimCasePO);
		    claim.setFrmsIsPositiveresult(positiveResult);
		    
		    //@invalid 入参医院信息
		    for (ClaimBillPO claimBill : claimBillPOs) {
		    	com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Hop hop = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Hop();
			    //@invalid 医院名称
		    	if (claimBill.getData().get("hospital_name") != null) {
		    		hop.setFrmsHospitalname((String)claimBill.getData().get("hospital_name"));
		    	} else {
		    		hop.setFrmsHospitalname("");
		    	}
			    //@invalid 医院等级
			    if (claimBill.getData().get("level_name") != null) {
			    	hop.setFrmsHospitalGrade((String)claimBill.getData().get("level_name"));
			    } else {
			    	hop.setFrmsHospitalGrade("");
			    }
			    //@invalid 医院科室
			    if (claimBill.getData().get("hospital_name") != null&&claimBill.getData().get("medical_dept_name")!=null&&claimBill.getData().get("doctor_name")!=null) {
			    	hop.setFrmsHospcode((String)claimBill.getData().get("hospital_name") + "_" +(String)claimBill.getData().get("medical_dept_name") + "_" + claimBill.getData().get("doctor_name"));
			    } else {
			    	hop.setFrmsHospcode("");
			    }
			    //@invalid 医院代码
			    if (claimBill.getData().get("hospital_code") != null) {
			    	hop.setFrmsHospitalno((String)claimBill.getData().get("hospital_code"));
			    } else {
			    	hop.setFrmsHospitalno("");
			    }
			    hops.add(hop);
		    }
		    //@invalid esb报文校验
		    if(claimBillPOs.size()==0){
		    	com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Hop hop = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Hop();
		    	hop.setFrmsHospitalname("");
		    	hop.setFrmsHospitalGrade("");
		    	hop.setFrmsHospcode("");
		    	hop.setFrmsHospitalno("");
		    	hops.add(hop);
		    }
		    
		    claim.setHops(hoslist);
		    //@invalid 入参保单信息
		    for (ClaimBusiProdPO claimBusiPord : claimBusiProdPOs) {
		    	com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Cont cont = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Cont();
		    	//@invalid 保单号
		    	cont.setFrmsContno(claimBusiPord.getPolicyCode());
		    	//@invalid 保单险种号 BUSI_ITEM_ID 跟张春梦确认
		    	cont.setFrmsPolno(claimBusiPord.getBusiItemId().toString());
		    	//@invalid 保单类型
		    	cont.setFrmsContnoConttype("个单");
		    	//@invalid 险种名称
		    	cont.setFrmsRiskname((String)claimBusiPord.getData().get("busi_prod_name"));
		    	//@invalid 险种类别
		    	cont.setFrmsRiskperiod((String)claimBusiPord.getData().get("prod_type_name"));
		    	//@invalid 险种3位码
		    	cont.setFrmsInsuranceKind((String)claimBusiPord.getData().get("busi_prod3"));
		    	//@invalid 给付责任类型
		    	String dutykind = "";
		    	if (claimCasePO.getData().get("acc_reason") != null) {
		    		if ("1".equals(String.valueOf(claimBusiPord.getData().get("acc_reason")))) {
		    			dutykind = "2";
		    		} else {
		    			dutykind = "1";
		    		}
		    		if (claimBusiPord.getData().get("is_waived") != null && claimBusiPord.getData().get("is_waived") == "1") {
		    			//@invalid 豁免
		    			dutykind = dutykind+"09";
		    		} else {//@invalid 换成接口需要的码值
		    			String claimtype = String.valueOf(claimBusiPord.getData().get("claim_type")).trim();
		    			if("01".equals(claimtype)){	//@invalid 身故--死亡
		    				dutykind = dutykind + "02";
		    			}else if("02".equals(claimtype)){//@invalid 伤残
		    				dutykind = dutykind + "01";
		    			}else if("03".equals(claimtype)){//@invalid 重大疾病--大病
		    				dutykind = dutykind + "04";
		    			}else if("04".equals(claimtype)){//@invalid 高残
		    				dutykind = dutykind + "03";
		    			}else if("08".equals(claimtype)){//@invalid 医疗
		    				dutykind = dutykind + "00";
		    			}else if("10".equals(claimtype)){//@invalid 特种疾病
		    				dutykind = dutykind + "05";
		    			}else {
		    				dutykind = dutykind + claimtype;
		    			}
		    		}
		    		
		    	}
		    	cont.setFrmsGetdutykind(dutykind);
		    	//@invalid 理赔类型(“给付责任类型”字段对应的中文名称)
		    	String kindname = "";
		    	if ("2".equals(String.valueOf(claimBusiPord.getData().get("acc_reason")))) { //@invalid 查出来是新核心码值
		    		kindname = "意外";
	    		} else {
	    			kindname = "疾病";
	    		}
		    	if (!dutykind.equals("")) {
		    		kindname = kindname + getNameByOldClaimType(dutykind.substring(1, dutykind.length()));
		    	}
		    	cont.setFrmsKindname(kindname);
		    	//@invalid 赔付结论依据(赔付结论为“全部拒付”时录入的拒付原因)
		    	String givereason = "";
		    	if (claimCasePO.getAuditRejectReason() != null) {
		    		givereason = claimCasePO.getAuditRejectReason();
		    	}
		    	cont.setFrmsGivereason(givereason);
		    	//@invalid 代理人
		    	if (claimBusiPord.getData().get("agent_code") != null) {
		    		cont.setFrmsAgentcode((String)claimBusiPord.getData().get("agent_code"));
		    	} else {
		    		cont.setFrmsAgentcode("");
		    	}
		    	//@invalid 代理人姓名
		    	if (claimBusiPord.getData().get("agent_name") != null) {
		    		cont.setFrmsAgentname((String)claimBusiPord.getData().get("agent_name"));
		    	} else {
		    		cont.setFrmsAgentname("");
		    	}
		    	//@invalid 育成代理人号(接口要求置空)
		    	cont.setFrmsRearagentcode("");
		    	//@invalid 投保手机号
		    	if (claimBusiPord.getData().get("mobile_tel") != null) {
		    		cont.setFrmsAppntmobile((String)claimBusiPord.getData().get("mobile_tel"));
		    	} else {
		    		cont.setFrmsAppntmobile("");
		    	}
		    	//@invalid 		    	cont.setFrmsContnoActiveTime(String.valueOf("40"));
		    	double activeTime = 0;
		    	//@invalid 保单生效日期
		    	ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
	            contractBusiProdPO.setCaseId(claimBusiPord.getCaseId());
	            contractBusiProdPO.setPolicyCode(claimBusiPord.getPolicyCode());
	            contractBusiProdPO.setBusiProdCode(claimBusiPord.getBusiProdCode());
	            contractBusiProdPO.setCurFlag(new BigDecimal(ClaimConstant.ONE));
	            List<ContractBusiProdPO> contractBusiProdPOs = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPO);
	            if(contractBusiProdPOs.size()>0){
		    		if(contractBusiProdPOs.get(0).getFirstValidateDate() !=null){
				    	cont.setFrmsContnoCvalidate(dateformat.format(contractBusiProdPOs.get(0).getFirstValidateDate()));
				    	activeTime = DateUtilsEx.getDayAmount(contractBusiProdPOs.get(0).getFirstValidateDate(), claimSubCasePOs.get(0).getClaimDate());
		    		}else {
		    			cont.setFrmsContnoCvalidate(dateformat.format(contractBusiProdPOs.get(0).getInitialValidateDate()));
				    	activeTime = DateUtilsEx.getDayAmount(contractBusiProdPOs.get(0).getInitialValidateDate(), claimSubCasePOs.get(0).getClaimDate());
		    		}	
		    	}else {
			    	cont.setFrmsContnoCvalidate(dateformat.format((Date)claimBusiPord.getData().get("initial_validdate_date")));
			    	activeTime = DateUtilsEx.getDayAmount((Date)claimBusiPord.getData().get("initial_validdate_date"), claimSubCasePOs.get(0).getClaimDate());
		    	}
		    	//@invalid 保单生效时长(出险日期-生效日期=天数)
		    	cont.setFrmsContnoActiveTime(String.valueOf((int)activeTime));
		    	//@invalid 投保人号
		    	if (claimBusiPord.getData().get("customer_id") !=null) {
		    		cont.setFrmsInsurerCode(claimBusiPord.getData().get("customer_id").toString());
		    	}
		    	//@invalid 投保人姓名
		    	cont.setFrmsInsurer((String)claimBusiPord.getData().get("customer_name"));
		    	//@invalid 被保人号
//@invalid 		    	if (claimBusiPord.getData().get("insured_id") != null) {
//@invalid 		    		cont.setFrmsInsuredCode(claimBusiPord.getData().get("insured_id").toString());
//@invalid 		    	}
		    	String insuredIdList = "";
		    	InsuredListPO insuredListPO = new InsuredListPO();
		    	insuredListPO.setCaseId(claimBusiPord.getCaseId());
		    	insuredListPO.setPolicyId(claimBusiPord.getPolicyId());
		    	insuredListPO.setCurFlag(new BigDecimal(1));
		    	List<InsuredListPO> insuredListPOs = insuredListDao.findCustomerIdByPolicyIdAndCaseNo(insuredListPO);
		    	for (InsuredListPO insuredL : insuredListPOs) {
		    		insuredIdList = insuredIdList + insuredL.getCustomerId() + ";";
		    	}
		    	cont.setFrmsInsuredCode(insuredIdList.substring(0, insuredIdList.length()-1));
		    	//@invalid 被保人姓名
		    	String insureName = "";
		        if (!insuredIdList.equals("")) {
		        	String[] str = insuredIdList.split(";");
		            for (int i = 0; i < str.length; i++) {
		                String strbus = str[i].trim();
		                CustomerPO customerPO = new CustomerPO();
		                customerPO.setCustomerId(new BigDecimal(strbus));
		                customerPO = customerDao.findCustomerByCustomerId(customerPO);
		                String customerName = "";
		                if (customerPO != null) {
		                    if (customerPO.getCustomerName() != null) {
		                        customerName = customerPO.getCustomerName();
		                    }
		                }
		                insureName = insureName + customerName + ";";
		            }
		        }
		        if (!insureName.equals("")) {
		        	 cont.setFrmsInsured(insureName.substring(0, insureName.length()-1));
		        }
		    	//@invalid 保单管理机构
		        cont.setFrmsManage8Code((String)claimBusiPord.getData().get("organ_code"));
		    	conts.add(cont);
            
		    }
		    claim.setConts(contlist);
		    
		    //@invalid 入参理赔类型信息
		    for(ClaimSubCasePO claimSubCase : claimSubCasePOs){
		    	com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Subcase subcase = new com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Subcase();
		    	//@invalid 理赔类型
		    	subcase.setClaimType(claimSubCase.getClaimType());
		    	subcases.add(subcase);
		    }
		    claim.setSubcaseList(subcaseList);
		    inputdate.setClaim(claim);
	    	
	    	srvReqBizBody.setInputData(inputdate);
	    	parametersReqBody.setBizHeader(ReqHead);
	        parametersReqBody.setBizBody(srvReqBizBody);
	        
	        //@invalid 调用
	        port.claimRuleQuery(parametersReqHeader, parametersReqBody, parametersResHeader, parametersResBody);
	        //@invalid 返回参数入库
	        com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.ClaimType claimres = null;
		    if(parametersResBody.value.getBizBody()!=null){
		    	claimres = parametersResBody.value.getBizBody().getOutputData().getClaim();
		    }else{
		    	claimres = null;
		    }
	        
	        if (claimres!=null && claimres.getRules().getRule().size() > 0 && !claimres.getFrmsClmno().equals("")) {
		    	//@invalid 风险疑点汇总集合save
		        List<ClaimRiskDoubtfulInfoPO> claimRiskDoubtfulInfoPOList = new ArrayList<ClaimRiskDoubtfulInfoPO>();
		        List<ClaimRiskDoubtfulInfoPO> claimRiskDoubtfulInfoPOUpdateList = new ArrayList<ClaimRiskDoubtfulInfoPO>();
		    	for (com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.Rule rule : claimres.getRules().getRule()) {
		    		ClaimCheatAtlasPO claimCheatAtlasPO = new ClaimCheatAtlasPO();
		    		claimCheatAtlasPO.setCaseNo(claimres.getFrmsClmno());
		    		claimCheatAtlasPO.setPolicyCode(rule.getFrmsContno());
		    		claimCheatAtlasPO.setFrmsRuleCode(rule.getFrmsRuleCode());
		    		claimCheatAtlasPO.setFrmsRuleScore(rule.getFrmsRuleScore());
		    		claimCheatAtlasPO.setFrmsCustomerCount(rule.getFrmsCustomerCount());
		    		claimCheatAtlasPO.setFrmsClaimPay(new BigDecimal(rule.getFrmsClaimPay()));
		    		List<ClaimCheatAtlasPO> findAllClaimCheatAtlas = claimCheatAtlasDao.findAllClaimCheatAtlas(claimCheatAtlasPO);
		    		if(findAllClaimCheatAtlas.size()==0){//@invalid 如果不存在新增处理
		    			claimCheatAtlasDao.addClaimCheatAtlas(claimCheatAtlasPO);
		    			//@invalid 处理关联图谱疑点数据
		    			List<ClaimCheatAtlasPO> findAllClaimCheatAtlas2 = claimCheatAtlasDao.findAllClaimCheatAtlas(claimCheatAtlasPO);
		    			if(findAllClaimCheatAtlas2.size()>0){
		    				ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO = new ClaimRiskDoubtfulInfoPO();
        					claimRiskDoubtfulInfoPO.setCaseId(claimCaseBO.getCaseId());
        					//@invalid 获取关联图谱主键
        					claimRiskDoubtfulInfoPO.setCheatAtlasId(findAllClaimCheatAtlas2.get(0).getCheatAtlasId());
        					//@invalid 报文返回存在空格
//@invalid         					claimRiskDoubtfulInfoPO.setDoubtfulCode(rule.getFrmsRuleCode());//@invalid 规则代码 图规则代码只存Claim_Cheat_Atlas
        					claimRiskDoubtfulInfoPOList.add(claimRiskDoubtfulInfoPO);
		    			}
		    		}else{
		    			//@invalid 获取已存在数据主键
		    			claimCheatAtlasPO.setCheatAtlasId(findAllClaimCheatAtlas.get(0).getCheatAtlasId());
		    			claimCheatAtlasDao.updateClaimCheatAtlas(claimCheatAtlasPO);
		    			ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO = new ClaimRiskDoubtfulInfoPO();
    					claimRiskDoubtfulInfoPO.setCaseId(claimCaseBO.getCaseId());
    					claimRiskDoubtfulInfoPO.setCheatAtlasId(findAllClaimCheatAtlas.get(0).getCheatAtlasId());
    					List<ClaimRiskDoubtfulInfoPO> findAllClaimRiskDoubtfulInfo = claimRiskDoubtfulInfoDao.findAllClaimRiskDoubtfulInfo(claimRiskDoubtfulInfoPO);
    					if(findAllClaimRiskDoubtfulInfo.size()>0){
    						claimRiskDoubtfulInfoPO.setDoubtfulInfoId(findAllClaimRiskDoubtfulInfo.get(0).getDoubtfulInfoId());
    						claimRiskDoubtfulInfoPO.setRiskLiabId(null);
    						claimRiskDoubtfulInfoPO.setOrderNumber(null);
    						claimRiskDoubtfulInfoPO.setRecognizedFlag(null);
    						claimRiskDoubtfulInfoPOUpdateList.add(claimRiskDoubtfulInfoPO);
    					}else{
    						claimRiskDoubtfulInfoPOList.add(claimRiskDoubtfulInfoPO);
    					}

		    		}
		    	}
		    	
		    	//@invalid 新增风险疑点
		        if(claimRiskDoubtfulInfoPOList.size()>0){
		        	claimRiskDoubtfulInfoDao.batchSaveClaimRiskDoubtfulInfo(claimRiskDoubtfulInfoPOList);
		        }else if(claimRiskDoubtfulInfoPOUpdateList.size()>0){//@invalid 如果存在就修改
		        	claimRiskDoubtfulInfoDao.batchUpdateClaimRiskDoubtfulInfo(claimRiskDoubtfulInfoPOUpdateList);
		        }
		        return parametersResBody.value.getBizBody().getOutputData();
		    }
		} catch (Exception e) {
            e.printStackTrace();
            logger.debug("图规则接口处理异常赔案id" + claimCaseBO.getCaseId());
        }
		return null;
	}
	
	/**
	 * @description 转换老核心
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param oldClaimType 理赔类型
	 * @return 转换后的值
	*/
	public String getNameByOldClaimType(String oldClaimType) {
		//转换老核心理赔类型
		String claimTypeName = "";
		if(oldClaimType.equals("00")){
			claimTypeName = "医疗";
		}else if(oldClaimType.equals("01")){
			claimTypeName = "伤残";
		}else if(oldClaimType.equals("02")){
			claimTypeName = "死亡";
		}else if(oldClaimType.equals("03")){
			claimTypeName = "高残";
		}else if(oldClaimType.equals("04")){
			claimTypeName = "大病";
		}else if(oldClaimType.equals("05")){
			claimTypeName = "特种疾病";
		}else if(oldClaimType.equals("06")){
			claimTypeName = "失业失能";
		}
		return claimTypeName;
	}
    /**
     * 
     * @description 通过赔案查询涉案保单及投保人信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    private List<ClaimPolicyHolderBO> findPolicyListByCaseNo(ClaimPolicyHolderBO claimPolicyHolderBO) throws BizException {
    	//1.通过赔案查询涉案保单及投保人信息
    	ClaimPolicyHolderPO claimPolicyHolderPO = new ClaimPolicyHolderPO();
    	claimPolicyHolderPO.setCaseId(claimPolicyHolderBO.getCaseId());
    	List<ClaimPolicyHolderPO> claimPolicyHolderPOList = claimCaseDao.findPolicyListByCaseNo(claimPolicyHolderPO); 
    	List<ClaimPolicyHolderBO> claimPolicyHolderBOList = BeanUtils.copyList(ClaimPolicyHolderBO.class, claimPolicyHolderPOList);
        return claimPolicyHolderBOList;
    }
    /**
     * 
     * @description 查询被保人数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public int findInsuredCountById(ClaimPolicyHolderBO claimPolicyHolderBO) throws BizException {
    	//1.查询被保人数据条数
    	int count = 0;
    	count = claimCaseDao.findInsuredCountById(BeanUtils.copyProperties(ClaimPolicyHolderPO.class, claimPolicyHolderBO));
        return count;
    }
    /**
     * 
     * @description 查询受益人数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public int findBeneCountById(ClaimPolicyHolderBO claimPolicyHolderBO) throws BizException {
    	//1.查询被保人数据条数
    	int count = 0;
    	count = claimCaseDao.findBeneCountById(BeanUtils.copyProperties(ClaimPolicyHolderPO.class, claimPolicyHolderBO));
        return count;
    }
    /**
     * 
     * @description 查询领款人数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public int findPayeeCountById(ClaimPolicyHolderBO claimPolicyHolderBO) throws BizException {
    	//1.查询被保人数据条数
    	int count = 0;
    	count = claimCaseDao.findPayeeCountById(BeanUtils.copyProperties(ClaimPolicyHolderPO.class, claimPolicyHolderBO));
        return count;
    }
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
    @Deprecated
	private String getBeneRole(ClaimBeneBO claimBeneBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		BigDecimal customerId = claimBeneBO.getBeneNo();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(claimBeneBO.getBeneName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(claimBeneBO.getBeneCertiNo())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			ClaimPolicyHolderBO claimPolicyHolderBOCondi = new ClaimPolicyHolderBO();
			claimPolicyHolderBOCondi.setCustomerId(customerId);
			claimPolicyHolderBOCondi.setCaseId(claimPolicyHolderBO.getCaseId());
			claimPolicyHolderBOCondi.setPolicyCode(policyCode);
			claimPolicyHolderBOCondi.setCustomerName(claimBeneBO.getBeneName());
			claimPolicyHolderBOCondi.setCustomerBirthday(claimBeneBO.getBeneBirth());
			claimPolicyHolderBOCondi.setCustomerGender(claimBeneBO.getBeneSex());
			claimPolicyHolderBOCondi.setCustomerCertiCode(claimBeneBO.getBeneCertiNo());
			int insuredcount = this.findInsuredCountById(claimPolicyHolderBOCondi);
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			int beneCount = this.findBeneCountById(claimPolicyHolderBOCondi);
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			int payeeCount = this.findPayeeCountById(claimPolicyHolderBOCondi);
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
    /**
     * 
     * @description 获取指定客户在保单中的客户角色--剔除数据库访问
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	private String getBeneRoleNew(ClaimBeneBO claimBeneBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole
			,List<InsuredListPO> insuredCustomerData,List<ClaimBenePO> claimBeneData,List<ClaimPayeePO> claimPayeeData){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		BigDecimal customerId = claimBeneBO.getBeneNo();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(claimBeneBO.getBeneName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(claimBeneBO.getBeneCertiNo())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			int insuredcount = 0;
			for(InsuredListPO insuredListPO : insuredCustomerData){
				if((customerId==null||(insuredListPO.getData().get("customer_id")!=null
						&&customerId.compareTo((BigDecimal)insuredListPO.getData().get("customer_id"))==0))
						
						&&insuredListPO.getData().get("policy_code")!=null
						&&policyCode.equals((String)insuredListPO.getData().get("policy_code"))
						
						&&(claimBeneBO.getBeneName()==null||(insuredListPO.getData().get("customer_name")!=null
						&&claimBeneBO.getBeneName().equals((String)insuredListPO.getData().get("customer_name"))))
						
						&&(claimBeneBO.getBeneBirth()==null||(insuredListPO.getData().get("customer_birthday")!=null
						&&claimBeneBO.getBeneBirth().compareTo((Date)insuredListPO.getData().get("customer_birthday"))==0))
						
						&&(claimBeneBO.getBeneSex()==null||(insuredListPO.getData().get("customer_gender")!=null
						&&claimBeneBO.getBeneSex().compareTo((BigDecimal)insuredListPO.getData().get("customer_gender"))==0))
						
						&&(claimBeneBO.getBeneCertiNo()==null||(insuredListPO.getData().get("customer_certi_code")!=null
						&&claimBeneBO.getBeneCertiNo().equals((String)insuredListPO.getData().get("customer_certi_code"))))){
					insuredcount++;
				}
			}
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			 
			int beneCount = 0;
			for(ClaimBenePO claimBenePO : claimBeneData){
				if((customerId==null||(claimBenePO.getBeneNo()!=null
						&&customerId.toString().equals(claimBenePO.getBeneNo())))
						
						&&claimBenePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimBenePO.getData().get("policy_code"))
						
						&&(claimBeneBO.getBeneName()==null||(claimBenePO.getBeneName()!=null
						&&claimBeneBO.getBeneName().equals(claimBenePO.getBeneName())))
						
						&&(claimBeneBO.getBeneBirth()==null||(claimBenePO.getBeneBirth()!=null
						&&claimBeneBO.getBeneBirth().compareTo(claimBenePO.getBeneBirth())==0))
						
						&&(claimBeneBO.getBeneSex()==null||(claimBenePO.getBeneSex()!=null
						&&claimBeneBO.getBeneSex().compareTo(claimBenePO.getBeneSex())==0))
						
						&&(claimBeneBO.getBeneCertiNo()==null||(claimBenePO.getBeneCertiNo()!=null
						&&claimBeneBO.getBeneCertiNo().equals(claimBenePO.getBeneCertiNo())))){
					beneCount++;
				}
			}
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			
			
			
			int payeeCount = 0;
			for(ClaimPayeePO claimPayeePO : claimPayeeData){
				if((customerId==null||(claimPayeePO.getPayeeNo()!=null
						&&customerId.toString().equals(claimPayeePO.getPayeeNo())))
						
						&&claimPayeePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimPayeePO.getData().get("policy_code"))
						
						&&(claimBeneBO.getBeneName()==null||(claimPayeePO.getPayeeName()!=null
						&&claimBeneBO.getBeneName().equals(claimPayeePO.getPayeeName())))
						
						&&(claimBeneBO.getBeneBirth()==null||(claimPayeePO.getPayeeBirth()!=null
						&&claimBeneBO.getBeneBirth().compareTo(claimPayeePO.getPayeeBirth())==0))
						
						&&(claimBeneBO.getBeneSex()==null||(claimPayeePO.getPayeeSex()!=null
						&&claimBeneBO.getBeneSex().compareTo(claimPayeePO.getPayeeSex())==0))
						
						&&(claimBeneBO.getBeneCertiNo()==null||(claimPayeePO.getPayeeCertiNo()!=null
						&&claimBeneBO.getBeneCertiNo().equals(claimPayeePO.getPayeeCertiNo())))){
					payeeCount++;
				}
			}
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	@Deprecated
	private String getPayeeRole(ClaimPayeeBO claimPayeeBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		BigDecimal customerId = claimPayeeBO.getPayeeNo();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(claimPayeeBO.getPayeeName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(claimPayeeBO.getPayeeCertiNo())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			ClaimPolicyHolderBO claimPolicyHolderBOCondi = new ClaimPolicyHolderBO();
			claimPolicyHolderBOCondi.setCustomerId(customerId);
			claimPolicyHolderBOCondi.setCaseId(claimPolicyHolderBO.getCaseId());
			claimPolicyHolderBOCondi.setPolicyCode(policyCode);
			claimPolicyHolderBOCondi.setCustomerName(claimPayeeBO.getPayeeName());
			claimPolicyHolderBOCondi.setCustomerBirthday(claimPayeeBO.getPayeeBirth());
			claimPolicyHolderBOCondi.setCustomerGender(claimPayeeBO.getPayeeSex());
			claimPolicyHolderBOCondi.setCustomerCertiCode(claimPayeeBO.getPayeeCertiNo());
			int insuredcount = this.findInsuredCountById(claimPolicyHolderBOCondi);
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			int beneCount = this.findBeneCountById(claimPolicyHolderBOCondi);
			if(beneCount>0 && !customerRole.contains("收益人")){
				customerRole = customerRole+"受益人,";
			}
			int payeeCount = this.findPayeeCountById(claimPolicyHolderBOCondi);
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	private String getPayeeRoleNew(ClaimPayeeBO claimPayeeBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole
			,List<InsuredListPO> insuredCustomerData,List<ClaimBenePO> claimBeneData,List<ClaimPayeePO> claimPayeeData){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		BigDecimal customerId = claimPayeeBO.getPayeeNo();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(claimPayeeBO.getPayeeName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(claimPayeeBO.getPayeeCertiNo())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			int insuredcount = 0;
			for(InsuredListPO insuredListPO : insuredCustomerData){
				if((customerId==null||(insuredListPO.getData().get("customer_id")!=null
						&&customerId.compareTo((BigDecimal)insuredListPO.getData().get("customer_id"))==0))
						
						&&insuredListPO.getData().get("policy_code")!=null
						&&policyCode.equals((String)insuredListPO.getData().get("policy_code"))
						
						&&(claimPayeeBO.getPayeeName()==null||(insuredListPO.getData().get("customer_name")!=null
						&&claimPayeeBO.getPayeeName().equals((String)insuredListPO.getData().get("customer_name"))))
						
						&&(claimPayeeBO.getPayeeBirth()==null||(insuredListPO.getData().get("customer_birthday")!=null
						&&claimPayeeBO.getPayeeBirth().compareTo((Date)insuredListPO.getData().get("customer_birthday"))==0))
						
						&&(claimPayeeBO.getPayeeSex()==null||(insuredListPO.getData().get("customer_gender")!=null
						&&claimPayeeBO.getPayeeSex().compareTo((BigDecimal)insuredListPO.getData().get("customer_gender"))==0))
						
						&&(claimPayeeBO.getPayeeCertiNo()==null||(insuredListPO.getData().get("customer_certi_code")!=null
						&&claimPayeeBO.getPayeeCertiNo().equals((String)insuredListPO.getData().get("customer_certi_code"))))){
					insuredcount++;
				}
			}
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			
			int beneCount = 0;
			for(ClaimBenePO claimBenePO : claimBeneData){
				if((customerId==null||(claimBenePO.getBeneNo()!=null
						&&customerId.toString().equals(claimBenePO.getBeneNo())))
						
						&&claimBenePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimBenePO.getData().get("policy_code"))
						
						&&(claimPayeeBO.getPayeeName()==null||(claimBenePO.getBeneName()!=null
						&&claimPayeeBO.getPayeeName().equals(claimBenePO.getBeneName())))
						
						&&(claimPayeeBO.getPayeeBirth()==null||(claimBenePO.getBeneBirth()!=null
						&&claimPayeeBO.getPayeeBirth().compareTo(claimBenePO.getBeneBirth())==0))
						
						&&(claimPayeeBO.getPayeeSex()==null||(claimBenePO.getBeneSex()!=null
						&&claimPayeeBO.getPayeeSex().compareTo(claimBenePO.getBeneSex())==0))
						
						&&(claimPayeeBO.getPayeeCertiNo()==null||(claimBenePO.getBeneCertiNo()!=null
						&&claimPayeeBO.getPayeeCertiNo().equals(claimBenePO.getBeneCertiNo())))){
					beneCount++;
				}
			}
			if(beneCount>0 && !customerRole.contains("收益人")){
				customerRole = customerRole+"受益人,";
			}
			
			
			int payeeCount = 0;
			for(ClaimPayeePO claimPayeePO : claimPayeeData){
				if((customerId==null||(claimPayeePO.getPayeeNo()!=null
						&&customerId.toString().equals(claimPayeePO.getPayeeNo())))
						
						&&claimPayeePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimPayeePO.getData().get("policy_code"))
						
						&&(claimPayeeBO.getPayeeName()==null||(claimPayeePO.getPayeeName()!=null
						&&claimPayeeBO.getPayeeName().equals(claimPayeePO.getPayeeName())))
						
						&&(claimPayeeBO.getPayeeBirth()==null||(claimPayeePO.getPayeeBirth()!=null
						&&claimPayeeBO.getPayeeBirth().compareTo(claimPayeePO.getPayeeBirth())==0))
						
						&&(claimPayeeBO.getPayeeSex()==null||(claimPayeePO.getPayeeSex()!=null
						&&claimPayeeBO.getPayeeSex().compareTo(claimPayeePO.getPayeeSex())==0))
						
						&&(claimPayeeBO.getPayeeCertiNo()==null||(claimPayeePO.getPayeeCertiNo()!=null
						&&claimPayeeBO.getPayeeCertiNo().equals(claimPayeePO.getPayeeCertiNo())))){
					payeeCount++;
				}
			}
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	@Deprecated
	private String getLegalRole(LegalPersonInfoBO legalPersonInfoBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(legalPersonInfoBO.getLegalName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(legalPersonInfoBO.getLegalCertiCode())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			ClaimPolicyHolderBO claimPolicyHolderBOCondi = new ClaimPolicyHolderBO();
			claimPolicyHolderBOCondi.setCaseId(claimPolicyHolderBO.getCaseId());
			claimPolicyHolderBOCondi.setPolicyCode(policyCode);
			claimPolicyHolderBOCondi.setCustomerName(legalPersonInfoBO.getLegalName());
			claimPolicyHolderBOCondi.setCustomerCertiCode(legalPersonInfoBO.getLegalCertiCode());
			int insuredcount = this.findInsuredCountById(claimPolicyHolderBOCondi);
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			int beneCount = this.findBeneCountById(claimPolicyHolderBOCondi);
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			int payeeCount = this.findPayeeCountById(claimPolicyHolderBOCondi);
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	private String getLegalRoleNew(LegalPersonInfoBO legalPersonInfoBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole
			,List<InsuredListPO> insuredCustomerData,List<ClaimBenePO> claimBeneData,List<ClaimPayeePO> claimPayeeData){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(legalPersonInfoBO.getLegalName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(legalPersonInfoBO.getLegalCertiCode())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			int insuredcount = 0;
			for(InsuredListPO insuredListPO : insuredCustomerData){
				if(insuredListPO.getData().get("policy_code")!=null
						&&policyCode.equals((String)insuredListPO.getData().get("policy_code"))
						
						&&(legalPersonInfoBO.getLegalName()==null||(insuredListPO.getData().get("customer_name")!=null
						&&legalPersonInfoBO.getLegalName().equals((String)insuredListPO.getData().get("customer_name"))))
						
						&&(legalPersonInfoBO.getLegalCertiCode()==null||(insuredListPO.getData().get("customer_certi_code")!=null
						&&legalPersonInfoBO.getLegalCertiCode().equals((String)insuredListPO.getData().get("customer_certi_code"))))){
					insuredcount++;
				}
			}
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			
			int beneCount = 0;
			for(ClaimBenePO claimBenePO : claimBeneData){
				if(claimBenePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimBenePO.getData().get("policy_code"))
						
						&&(legalPersonInfoBO.getLegalName()==null||(claimBenePO.getBeneName()!=null
						&&legalPersonInfoBO.getLegalName().equals(claimBenePO.getBeneName())))
						
						&&(legalPersonInfoBO.getLegalCertiCode()==null||(claimBenePO.getBeneCertiNo()!=null
						&&legalPersonInfoBO.getLegalCertiCode().equals(claimBenePO.getBeneCertiNo())))){
					beneCount++;
				}
			}
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			
			int payeeCount = 0;
			for(ClaimPayeePO claimPayeePO : claimPayeeData){
				if(claimPayeePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimPayeePO.getData().get("policy_code"))
						
						&&(legalPersonInfoBO.getLegalName()==null||(claimPayeePO.getPayeeName()!=null
						&&legalPersonInfoBO.getLegalName().equals(claimPayeePO.getPayeeName())))
						
						&&(legalPersonInfoBO.getLegalCertiCode()==null||(claimPayeePO.getPayeeCertiNo()!=null
						&&legalPersonInfoBO.getLegalCertiCode().equals(claimPayeePO.getPayeeCertiNo())))){
					payeeCount++;
				}
			}
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	@Deprecated
	private String getControllingRole(LegalPersonInfoBO legalPersonInfoBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(legalPersonInfoBO.getControllingName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(legalPersonInfoBO.getControllingCertiCode())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			ClaimPolicyHolderBO claimPolicyHolderBOCondi = new ClaimPolicyHolderBO();
			claimPolicyHolderBOCondi.setCaseId(claimPolicyHolderBO.getCaseId());
			claimPolicyHolderBOCondi.setPolicyCode(policyCode);
			claimPolicyHolderBOCondi.setCustomerName(legalPersonInfoBO.getControllingName());
			claimPolicyHolderBOCondi.setCustomerCertiCode(legalPersonInfoBO.getControllingCertiCode());
			int insuredcount = this.findInsuredCountById(claimPolicyHolderBOCondi);
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			int beneCount = this.findBeneCountById(claimPolicyHolderBOCondi);
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			int payeeCount = this.findPayeeCountById(claimPolicyHolderBOCondi);
			if(payeeCount>0  && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
	 * 
	 * @description 获取指定客户在保单中的客户角色
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseVO 入参
	 * @return 返回值
	 */
	private String getControllingRoleNew(LegalPersonInfoBO legalPersonInfoBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole
			,List<InsuredListPO> insuredCustomerData,List<ClaimBenePO> claimBeneData,List<ClaimPayeePO> claimPayeeData){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(legalPersonInfoBO.getControllingName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(legalPersonInfoBO.getControllingCertiCode())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			
			int insuredcount = 0;
			for(InsuredListPO insuredListPO : insuredCustomerData){
				if(insuredListPO.getData().get("policy_code")!=null
						&&policyCode.equals((String)insuredListPO.getData().get("policy_code"))
						
						&&(legalPersonInfoBO.getControllingName()==null||(insuredListPO.getData().get("customer_name")!=null
						&&legalPersonInfoBO.getControllingName().equals((String)insuredListPO.getData().get("customer_name"))))
						
						&&(legalPersonInfoBO.getControllingCertiCode()==null||(insuredListPO.getData().get("customer_certi_code")!=null
						&&legalPersonInfoBO.getControllingCertiCode().equals((String)insuredListPO.getData().get("customer_certi_code"))))){
					insuredcount++;
				}
			}
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			
			int beneCount = 0;
			for(ClaimBenePO claimBenePO : claimBeneData){
				if(claimBenePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimBenePO.getData().get("policy_code"))
						
						&&(legalPersonInfoBO.getControllingName()==null||(claimBenePO.getBeneName()!=null
						&&legalPersonInfoBO.getControllingName().equals(claimBenePO.getBeneName())))
						
						&&(legalPersonInfoBO.getControllingCertiCode()==null||(claimBenePO.getBeneCertiNo()!=null
						&&legalPersonInfoBO.getControllingCertiCode().equals(claimBenePO.getBeneCertiNo())))){
					beneCount++;
				}
			}
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			
			int payeeCount = 0;
			for(ClaimPayeePO claimPayeePO : claimPayeeData){
				if(claimPayeePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimPayeePO.getData().get("policy_code"))
						
						&&(legalPersonInfoBO.getControllingName()==null||(claimPayeePO.getPayeeName()!=null
						&&legalPersonInfoBO.getControllingName().equals(claimPayeePO.getPayeeName())))
						
						&&(legalPersonInfoBO.getControllingCertiCode()==null||(claimPayeePO.getPayeeCertiNo()!=null
						&&legalPersonInfoBO.getControllingCertiCode().equals(claimPayeePO.getPayeeCertiNo())))){
					payeeCount++;
				}
			}
			if(payeeCount>0  && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	@Deprecated
	private String getBeneficialRole(LegalPersonBeneInfoBO legalPersonBeneInfoBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(legalPersonBeneInfoBO.getBeneficialName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(legalPersonBeneInfoBO.getBeneficialCertiCode())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			ClaimPolicyHolderBO claimPolicyHolderBOCondi = new ClaimPolicyHolderBO();
			claimPolicyHolderBOCondi.setCaseId(claimPolicyHolderBO.getCaseId());
			claimPolicyHolderBOCondi.setPolicyCode(policyCode);
			claimPolicyHolderBOCondi.setCustomerName(legalPersonBeneInfoBO.getBeneficialName());
			claimPolicyHolderBOCondi.setCustomerCertiCode(legalPersonBeneInfoBO.getBeneficialCertiCode());
			int insuredcount = this.findInsuredCountById(claimPolicyHolderBOCondi);
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			int beneCount = this.findBeneCountById(claimPolicyHolderBOCondi);
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			int payeeCount = this.findPayeeCountById(claimPolicyHolderBOCondi);
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	private String getBeneficialRoleNew(LegalPersonBeneInfoBO legalPersonBeneInfoBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole
			,List<InsuredListPO> insuredCustomerData,List<ClaimBenePO> claimBeneData,List<ClaimPayeePO> claimPayeeData){
		String policyCode = claimPolicyHolderBO.getPolicyCode();
		if(!StringUtilsEx.isNullOrEmpty(policyCode)){
			if(claimPolicyHolderBO.getCustomerName()!=null && claimPolicyHolderBO.getCustomerCertiCode()!=null
					&&claimPolicyHolderBO.getCustomerName().equals(legalPersonBeneInfoBO.getBeneficialName())
					&&claimPolicyHolderBO.getCustomerCertiCode().equals(legalPersonBeneInfoBO.getBeneficialCertiCode())
					&&!customerRole.contains("投保人")){
				customerRole = customerRole+"投保人,";
			}
			
			int insuredcount = 0;
			for(InsuredListPO insuredListPO : insuredCustomerData){
				if(insuredListPO.getData().get("policy_code")!=null
						&&policyCode.equals((String)insuredListPO.getData().get("policy_code"))
						
						&&(legalPersonBeneInfoBO.getBeneficialName()==null||(insuredListPO.getData().get("customer_name")!=null
						&&legalPersonBeneInfoBO.getBeneficialName().equals((String)insuredListPO.getData().get("customer_name"))))
						
						&&(legalPersonBeneInfoBO.getBeneficialCertiCode()==null||(insuredListPO.getData().get("customer_certi_code")!=null
						&&legalPersonBeneInfoBO.getBeneficialCertiCode().equals((String)insuredListPO.getData().get("customer_certi_code"))))){
					insuredcount++;
				}
			}
			if(insuredcount>0 && !customerRole.contains("被保人")){
				customerRole = customerRole+"被保人,";
			}
			
			int beneCount = 0;
			for(ClaimBenePO claimBenePO : claimBeneData){
				if(claimBenePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimBenePO.getData().get("policy_code"))
						
						&&(legalPersonBeneInfoBO.getBeneficialName()==null||(claimBenePO.getBeneName()!=null
						&&legalPersonBeneInfoBO.getBeneficialName().equals(claimBenePO.getBeneName())))
						
						&&(legalPersonBeneInfoBO.getBeneficialCertiCode()==null||(claimBenePO.getBeneCertiNo()!=null
						&&legalPersonBeneInfoBO.getBeneficialCertiCode().equals(claimBenePO.getBeneCertiNo())))){
					beneCount++;
				}
			}
			if(beneCount>0 && !customerRole.contains("受益人")){
				customerRole = customerRole+"受益人,";
			}
			
			int payeeCount = 0;
			for(ClaimPayeePO claimPayeePO : claimPayeeData){
				if(claimPayeePO.getData().get("policy_code")!=null
						&&policyCode.equals((String)claimPayeePO.getData().get("policy_code"))
						
						&&(legalPersonBeneInfoBO.getBeneficialName()==null||(claimPayeePO.getPayeeName()!=null
						&&legalPersonBeneInfoBO.getBeneficialName().equals(claimPayeePO.getPayeeName())))
						
						&&(legalPersonBeneInfoBO.getBeneficialCertiCode()==null||(claimPayeePO.getPayeeCertiNo()!=null
						&&legalPersonBeneInfoBO.getBeneficialCertiCode().equals(claimPayeePO.getPayeeCertiNo())))){
					payeeCount++;
				}
			}
			if(payeeCount>0 && !customerRole.contains("领款人")){
				customerRole = customerRole+"领款人,";
			}
			if(customerRole.endsWith(",")){
				customerRole = customerRole.substring(0,customerRole.length()-1);
			}
		}
		return customerRole;
	}
	
	/**
     * 
     * @description 获取客户2年内理赔调查标识
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCasePO 入参
     * @return 返回值
     */
	private String getPositiveResult(ClaimCasePO claimCasePO){
		//@invalid 符合如下1、2任一条件的返回1，否则返回0
		//1、2年内存在阳性标识为“是”的理赔调查
		//2、2年内未进行过调查(未发起过理赔调查、已撤销的理赔调查均算作未进行过调查)
		String positiveResult = "0";
        int i = 0;
		SurveyApplyPO surveyApplyPO = new SurveyApplyPO();
		surveyApplyPO.getData().put("insured_id", claimCasePO.getInsuredId());
		List<SurveyApplyPO> surveyApplyPOs = surveyApplyDao.querySurveyApplyByCustomerId(surveyApplyPO);
        if (surveyApplyPOs.isEmpty()) {
            return positiveResult = "1";
        }
        
        for(SurveyApplyPO surveyApply : surveyApplyPOs){
        	double monthAmount = DateUtilsEx.getMonthAmount(surveyApply.getApplyDate(), WorkDateUtil.getWorkDate());
            //@invalid 在2年内
            if (monthAmount<24) {
                //@invalid 2年内存在阳性标识为"是"的理赔调查
                if (!new BigDecimal(3).equals(surveyApply.getSurveyStatus()) && new BigDecimal(ClaimConstant.YES).equals(surveyApply.getBigDecimal("positive_flag"))) {
                	return positiveResult = "1";
                }
                //@invalid 2年内存在有效的理赔调查
                if(!new BigDecimal(3).equals(surveyApply.getSurveyStatus()) && !new BigDecimal(ClaimConstant.YES).equals(surveyApply.getBigDecimal("positive_flag"))){
                	i++;
                }    
            }
        }
        //@invalid 2年内未进行过调查
        if(i==0){
        	return positiveResult = "1";
        }
		return positiveResult;
	}
    public IClaimBlackNameDao getClaimBlackNameDao() {
        return claimBlackNameDao;
    }

    public void setClaimBlackNameDao(IClaimBlackNameDao claimBlackNameDao) {
        this.claimBlackNameDao = claimBlackNameDao;
    }

    public IClaimSubCaseDao getClaimSubCaseDao() {
        return claimSubCaseDao;
    }

    public void setClaimSubCaseDao(IClaimSubCaseDao claimSubCaseDao) {
        this.claimSubCaseDao = claimSubCaseDao;
    }

    public IClaimAccidentResultDao getClaimAccidentResultDao() {
        return claimAccidentResultDao;
    }

    public void setClaimAccidentResultDao(IClaimAccidentResultDao claimAccidentResultDao) {
        this.claimAccidentResultDao = claimAccidentResultDao;
    }

    public IContractMasterDao getContractMasterDao() {
        return contractMasterDao;
    }

    public void setContractMasterDao(IContractMasterDao contractMasterDao) {
        this.contractMasterDao = contractMasterDao;
    }

    public IContractBusiProdDao getContractBusiProdDao() {
        return contractBusiProdDao;
    }

    public void setContractBusiProdDao(IContractBusiProdDao contractBusiProdDao) {
        this.contractBusiProdDao = contractBusiProdDao;
    }

    public ICustomerDao getCustomerDao() {
        return customerDao;
    }

    public void setCustomerDao(ICustomerDao customerDao) {
        this.customerDao = customerDao;
    }

    public IClaimCaseDao getClaimCaseDao() {
        return claimCaseDao;
    }

    public void setClaimCaseDao(IClaimCaseDao claimCaseDao) {
        this.claimCaseDao = claimCaseDao;
    }

    public ISurveyApplyDao getSurveyApplyDao() {
        return surveyApplyDao;
    }

    public void setSurveyApplyDao(ISurveyApplyDao surveyApplyDao) {
        this.surveyApplyDao = surveyApplyDao;
    }

    /**
     * 
     * @description 获取服务数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @return 返回值
     */
    public static QName getServiceNamebrs() {
        return SERVICE_NAMEBRS;
    }

    public IClaimAuditConclusionDao getAuditConclusionDao() {
        return auditConclusionDao;
    }

    public void setAuditConclusionDao(IClaimAuditConclusionDao auditConclusionDao) {
        this.auditConclusionDao = auditConclusionDao;
    }

	public IUserDao getUserDao() {
		return userDao;
	}

	public void setUserDao(IUserDao userDao) {
		this.userDao = userDao;
	}

	public IClaimAutoRuleResultDao getClaimAutoRuleResultDao() {
		return claimAutoRuleResultDao;
	}

	public void setClaimAutoRuleResultDao(
			IClaimAutoRuleResultDao claimAutoRuleResultDao) {
		this.claimAutoRuleResultDao = claimAutoRuleResultDao;
	}

	public IClaimPolicyDao getClaimPolicyDao() {
		return claimPolicyDao;
	}

	public void setClaimPolicyDao(IClaimPolicyDao claimPolicyDao) {
		this.claimPolicyDao = claimPolicyDao;
	}

	public IContractAgentDao getContractAgentDao() {
		return contractAgentDao;
	}

	public void setContractAgentDao(IContractAgentDao contractAgentDao) {
		this.contractAgentDao = contractAgentDao;
	}

	public static Logger getLogger() {
		return logger;
	}

	public static void setLogger(Logger logger) {
		CheckRuleVerifyServiceImpl.logger = logger;
	}

    public IContractBeneDao getContractBeneDao() {
        return contractBeneDao;
    }

    public void setContractBeneDao(IContractBeneDao contractBeneDao) {
        this.contractBeneDao = contractBeneDao;
    }

    public IClaimBillPaidDao getClaimBillPaidDao() {
        return claimBillPaidDao;
    }

    public void setClaimBillPaidDao(IClaimBillPaidDao claimBillPaidDao) {
        this.claimBillPaidDao = claimBillPaidDao;
    }

    public IPolicyHolderDao getPolicyHolderDao() {
        return policyHolderDao;
    }

    public void setPolicyHolderDao(IPolicyHolderDao policyHolderDao) {
        this.policyHolderDao = policyHolderDao;
    }

    public IClaimAccidentDao getClaimAccidentDao() {
        return claimAccidentDao;
    }

    public void setClaimAccidentDao(IClaimAccidentDao claimAccidentDao) {
        this.claimAccidentDao = claimAccidentDao;
    }

    public IInsuredListDao getInsuredListDao() {
        return insuredListDao;
    }

    public IClaimBusiProdDao getClaimBusiProdDao() {
        return claimBusiProdDao;
    }

    public IBusinessProductDao getBusinessProductDao() {
        return businessProductDao;
    }

    public IContractProductDao getContractProductDao() {
        return contractProductDao;
    }

    public IClaimBillDao getClaimBillDao() {
        return claimBillDao;
    }

    public void setInsuredListDao(IInsuredListDao insuredListDao) {
        this.insuredListDao = insuredListDao;
    }

    public void setClaimBusiProdDao(IClaimBusiProdDao claimBusiProdDao) {
        this.claimBusiProdDao = claimBusiProdDao;
    }

    public void setBusinessProductDao(IBusinessProductDao businessProductDao) {
        this.businessProductDao = businessProductDao;
    }

    public void setContractProductDao(IContractProductDao contractProductDao) {
        this.contractProductDao = contractProductDao;
    }

    public void setClaimBillDao(IClaimBillDao claimBillDao) {
        this.claimBillDao = claimBillDao;
    }

	public IClaimMemoDao getClaimMemoDao() {
		return claimMemoDao;
	}

	public void setClaimMemoDao(IClaimMemoDao claimMemoDao) {
		this.claimMemoDao = claimMemoDao;
	}

	public IClaimCheatAtlasDao getClaimCheatAtlasDao() {
		return claimCheatAtlasDao;
	}

	public void setClaimCheatAtlasDao(IClaimCheatAtlasDao claimCheatAtlasDao) {
		this.claimCheatAtlasDao = claimCheatAtlasDao;
	}

	public ILegalPersonInfoDao getLegalPersonInfoDao() {
		return legalPersonInfoDao;
	}

	public void setLegalPersonInfoDao(ILegalPersonInfoDao legalPersonInfoDao) {
		this.legalPersonInfoDao = legalPersonInfoDao;
	}

	public ILegalPersonBeneInfoDao getLegalPersonBeneInfoDao() {
		return legalPersonBeneInfoDao;
	}

	public void setLegalPersonBeneInfoDao(ILegalPersonBeneInfoDao legalPersonBeneInfoDao) {
		this.legalPersonBeneInfoDao = legalPersonBeneInfoDao;
	}

	public IDirectClaimPortsService getDirectClaimPortsService() {
		return directClaimPortsService;
	}

	public void setDirectClaimPortsService(
			IDirectClaimPortsService directClaimPortsService) {
		this.directClaimPortsService = directClaimPortsService;
	}

	public IClaimHospitalServiceDao getClaimHospitalServiceDao() {
		return claimHospitalServiceDao;
	}

    public void setClaimHospitalServiceDao(IClaimHospitalServiceDao claimHospitalServiceDao) {
        this.claimHospitalServiceDao = claimHospitalServiceDao;
    }

	public ISurveyConclusionDao getSurveyConclusionDao() {
		return surveyConclusionDao;
	}

	public void setSurveyConclusionDao(ISurveyConclusionDao surveyConclusionDao) {
		this.surveyConclusionDao = surveyConclusionDao;
	}
}
