package com.nci.tunan.clm.impl.pay.ucc.impl;


import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.dao.IClaimPayChangeDao;
import com.nci.tunan.clm.interfaces.model.po.ClaimPayChangePO;
import net.sf.json.JSONObject;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.vo.BlacklistReqVo;
import com.nci.core.common.interfaces.vo.BlacklistRspVo;
import com.nci.tunan.clm.impl.approve.service.IApproveService;
import com.nci.tunan.clm.impl.inspect.service.IClaimCaseService;
import com.nci.tunan.clm.impl.pay.service.IClaimPayChangeLogService;
import com.nci.tunan.clm.impl.pay.service.IClaimPayChangeService;
import com.nci.tunan.clm.impl.pay.ucc.IClaimPayChangeUCC;
import com.nci.tunan.clm.impl.sendmessage.service.ISendMessageService;
import com.nci.tunan.clm.impl.taskmanage.service.IQueryTaskTraceService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.impl.util.CodeUtils;
import com.nci.tunan.clm.interfaces.model.bo.BankWayBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimBpmRequestBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPayChangeBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPayChangeLogBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPolicyHolderBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimTaskBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.clm.interfaces.model.bo.MessageTemplateParamBO;
import com.nci.tunan.clm.interfaces.model.bo.PremArapBO;
import com.nci.tunan.clm.interfaces.model.bo.UserBO;
import com.nci.tunan.clm.interfaces.model.vo.BankWayVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimBpmRequestVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimPayChangeLogVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimPayChangeVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimTaskVO;
import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.tunan.clm.interfaces.model.vo.ContractMasterVO;
import com.nci.tunan.clm.interfaces.model.vo.OperateTraceVO;
import com.nci.tunan.clm.interfaces.model.vo.PremArapVO;
import com.nci.tunan.clm.interfaces.model.vo.UserVO;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

/** 
 * @description 付费变更UCC
 * <AUTHOR> <EMAIL>
 * @date 2015年8月29日 下午3:25:53
 * @.belongToModule CLM-理赔系统/付费变更UCC
*/
public class ClaimPayChangeUCC   implements IClaimPayChangeUCC  {
    /** 
     * @Fields claimPayChangeService : 注入service  
     */
	private IClaimPayChangeService claimPayChangeService;
	/** 
	* @Fields claimPayChangeLogService : 付费变更log处理
	*/ 
	private IClaimPayChangeLogService claimPayChangeLogService;
	/** 
	* @Fields claimCaseService : 赔案主表
	*/ 
	private IClaimCaseService claimCaseService;
	/** 
	* @Fields sendMessageService : 发送邮件
	*/ 
	private ISendMessageService sendMessageService;
	/** 
	* @Fields queryTaskTraceService : 查询任务轨迹
	*/ 
	private IQueryTaskTraceService queryTaskTraceService;
    /** 
    * @Fields approveService : 审批处理service
    */ 
    private IApproveService approveService;

	/**
	 * @Fields claimPayChangeDao : 注入 dao 
	 */
	private IClaimPayChangeDao claimPayChangeDao;
	
	/** 
	 * @Fields logger :  日志工具
 	 */
	private static Logger logger = LoggerFactory.getLogger();
	
	/**
     * @description SERVICE-getter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @return IClaimPayChangeService service接口对象
     */
	public IClaimPayChangeService getClaimPayChangeService() {
		return claimPayChangeService;
	}
	
	/**
     * @description SERVICE-setter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeService service对象
     */
	public void setClaimPayChangeService(IClaimPayChangeService claimPayChangeService) {
		this.claimPayChangeService = claimPayChangeService;
	}
	
	public IClaimPayChangeLogService getClaimPayChangeLogService() {
		return claimPayChangeLogService;
	}

	public void setClaimPayChangeLogService(
			IClaimPayChangeLogService claimPayChangeLogService) {
		this.claimPayChangeLogService = claimPayChangeLogService;
	}

	public IClaimCaseService getClaimCaseService() {
		return claimCaseService;
	}

	public void setClaimCaseService(IClaimCaseService claimCaseService) {
		this.claimCaseService = claimCaseService;
	}

	public ISendMessageService getSendMessageService() {
		return sendMessageService;
	}

	public void setSendMessageService(ISendMessageService sendMessageService) {
		this.sendMessageService = sendMessageService;
	}

	public IQueryTaskTraceService getQueryTaskTraceService() {
		return queryTaskTraceService;
	}

	public void setQueryTaskTraceService(
			IQueryTaskTraceService queryTaskTraceService) {
		this.queryTaskTraceService = queryTaskTraceService;
	}

	public IApproveService getApproveService() {
		return approveService;
	}

	public void setApproveService(IApproveService approveService) {
		this.approveService = approveService;
	}

	public IClaimPayChangeDao getClaimPayChangeDao() {
		return claimPayChangeDao;
	}

	public void setClaimPayChangeDao(IClaimPayChangeDao claimPayChangeDao) {
		this.claimPayChangeDao = claimPayChangeDao;
	}

	/**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @return ClaimPayChangeVO 添加结果
     */
	public ClaimPayChangeVO addClaimPayChange(ClaimPayChangeVO claimPayChangeVO) {
		logger.debug("<======ClaimPayChangeUCC--addClaimPayChange======>");
		//1 增加数据
		ClaimPayChangeBO claimPayChangeBO = claimPayChangeService.addClaimPayChange(BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO));
		return BeanUtils.copyProperties(ClaimPayChangeVO.class, claimPayChangeBO);
	}
	
	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @return ClaimPayChangeVO 修改结果
     */
	public ClaimPayChangeVO updateClaimPayChange(ClaimPayChangeVO claimPayChangeVO)  {
		logger.debug("<======ClaimPayChangeUCC--updateClaimPayChange======>");
		//1 修改数据
		ClaimPayChangeBO claimPayChangeBO = claimPayChangeService.updateClaimPayChange(BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO));
		return BeanUtils.copyProperties(ClaimPayChangeVO.class, claimPayChangeBO);
	}

	 /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @return boolean 删除是否成功
     */
	public boolean deleteClaimPayChange(ClaimPayChangeVO claimPayChangeVO)  {
		logger.debug("<======ClaimPayChangeUCC--deleteClaimPayChange======>");
		//1 删除数据
		return claimPayChangeService.deleteClaimPayChange(BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO));
	}
	
	/**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @return ClaimPayChangeVO 查询结果对象
     */
	public ClaimPayChangeVO findClaimPayChange(ClaimPayChangeVO claimPayChangeVO) {
		logger.debug("<======ClaimPayChangeUCC--findClaimPayChange======>");
		//1 查询单条数据
		ClaimPayChangeBO claimPayChangeBackBO = claimPayChangeService.findClaimPayChange(BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO));
		ClaimPayChangeVO claimPayChangeBackVO = BeanUtils.copyProperties(ClaimPayChangeVO.class, claimPayChangeBackBO);
		return claimPayChangeBackVO;
	}  
	
	/**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @return List<ClaimPayChangeVO> 查询结果List
     */
	public List<ClaimPayChangeVO> findAllClaimPayChange(ClaimPayChangeVO claimPayChangeVO) {
		logger.debug("<======ClaimPayChangeUCC--findAllClaimPayChange======>");
		//1 查询所有数据
		ClaimPayChangeBO claimPayChangeBO = BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO);
		return BeanUtils.copyList(ClaimPayChangeVO.class, claimPayChangeService.findAllClaimPayChange(claimPayChangeBO));
	} 
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @return int 查询结果条数
     */
	public int findClaimPayChangeTotal(ClaimPayChangeVO claimPayChangeVO) {
		logger.debug("<======ClaimPayChangeUCC--findClaimPayChangeTotal======>");
		//1 查询数据条数
		ClaimPayChangeBO claimPayChangeBO = BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO);
		return claimPayChangeService.findClaimPayChangeTotal(claimPayChangeBO);
	}	
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPayChangeVO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimPayChangeVO> queryClaimPayChangeForPage(ClaimPayChangeVO claimPayChangeVO, CurrentPage<ClaimPayChangeVO> currentPage) {
		logger.debug("<======ClaimPayChangeUCC--queryClaimPayChangeForPage======>");
		//1 分页查询数据
		ClaimPayChangeBO claimPayChangeBO = BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO);
		return BeanUtils.copyCurrentPage(ClaimPayChangeVO.class, claimPayChangeService.queryClaimPayChangeForPage(claimPayChangeBO, BeanUtils.copyCurrentPage(ClaimPayChangeBO.class, currentPage)));
	}
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVOList 对象列表
     * @return boolean 批量添加是否成功
     */
	public boolean batchSaveClaimPayChange(List<ClaimPayChangeVO> claimPayChangeVOList) {
		logger.debug("<======ClaimPayChangeUCC--batchSaveClaimPayChange======>");
		//1 批量增加数据
		return claimPayChangeService.batchSaveClaimPayChange(BeanUtils.copyList(ClaimPayChangeBO.class, claimPayChangeVOList));
	}
	
	/**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVOList 对象列表
     * @return boolean 批量修改是否成功
     */
	public boolean batchUpdateClaimPayChange(List<ClaimPayChangeVO> claimPayChangeVOList) {
		logger.debug("<======ClaimPayChangeUCC--batchUpdateClaimPayChange======>");
		//1 批量修改数据
		return claimPayChangeService.batchUpdateClaimPayChange(BeanUtils.copyList(ClaimPayChangeBO.class, claimPayChangeVOList));
	}
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVOList 对象列表
     * @return boolean 批量删除是否成功
     */
	public boolean batchDeleteClaimPayChange(List<ClaimPayChangeVO> claimPayChangeVOList) {
		logger.debug("<======ClaimPayChangeUCC--batchDeleteClaimPayChange======>");
		//1 批量删除数据
		return claimPayChangeService.batchDeleteClaimPayChange(BeanUtils.copyList(ClaimPayChangeBO.class, claimPayChangeVOList));
	}
	
	/**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayChangeVO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public List<Map<String, Object>> findAllMapClaimPayChange(ClaimPayChangeVO claimPayChangeVO) {
		logger.debug("<======ClaimPayChangeUCC--findAllMapClaimPayChange======>");
		//1 查询所有数据，重新组装为MAP
		ClaimPayChangeBO claimPayChangeBO = BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO);
		return claimPayChangeService.findAllMapClaimPayChange(claimPayChangeBO);
	}
	
	/**
	 * @description 查询分页数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimPayChangeVO 付费变更数据
	 * @param currentPage 分页数据
	 * @return 分页数据
	*/
	public CurrentPage<ClaimPayChangeVO> queryPayChangeForPage(ClaimPayChangeVO claimPayChangeVO, CurrentPage<PremArapVO> currentPage) {
		logger.debug("<======ClaimPayChangeUCC--queryClaimPayChangeForPage======>");
		ClaimPayChangeBO claimPayChangeBO = BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO);
		//1 查询分页数据
		CurrentPage<ClaimPayChangeVO> copyCurrentPage = BeanUtils.copyCurrentPage(ClaimPayChangeVO.class, claimPayChangeService.queryPayChangeForPage(claimPayChangeBO, BeanUtils.copyCurrentPage(ClaimPayChangeBO.class, currentPage)));
		SimpleDateFormat sdf2=new SimpleDateFormat("yyyy-MM-dd");
		for (ClaimPayChangeVO claimPayChange : copyCurrentPage.getPageItems()) {
			if(claimPayChange.getPayeeBirth()!=null){
				claimPayChange.setPayeeBirthStr(sdf2.format(claimPayChange.getPayeeBirth()));
			}
			if(claimPayChange.getPayeeCertiEnd()!=null){
				claimPayChange.setPayeeCertiEndStr(sdf2.format(claimPayChange.getPayeeCertiEnd()));
			}
			if(claimPayChange.getPayeeCertiStart()!=null){
				claimPayChange.setPayeeCertiStartStr(sdf2.format(claimPayChange.getPayeeCertiStart()));
			}
		}
		return copyCurrentPage;
	}
	/**
	 * @description 付费变更轨迹查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimPayChangeVO 付费变更数据
	 * @param currentPage 分页数据
	 * @return 分页数据
	*/
	public CurrentPage<ClaimPayChangeVO> queryPayChangeTraceForPage(ClaimPayChangeVO claimPayChangeVO, CurrentPage<ClaimPayChangeVO> currentPage) {
		logger.debug("<======ClaimPayChangeUCC--queryClaimPayChangeForPage======>");
		//1 付费变更轨迹查询
		ClaimPayChangeBO claimPayChangeBO = BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO);
		return BeanUtils.copyCurrentPage(ClaimPayChangeVO.class, claimPayChangeService.queryPayChangeTraceForPage(claimPayChangeBO, BeanUtils.copyCurrentPage(ClaimPayChangeBO.class, currentPage)));
	}
	
	/**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 对象
     * @return ClaimCaseVO 查询结果对象
     */
	public ClaimCaseVO findClaimCase(ClaimCaseVO claimCaseVO) {
		logger.debug("<======ClaimCaseUCC--findClaimCase======>");
		//1 查询单挑数据
		ClaimCaseBO claimCaseBackBO = claimPayChangeService.queryCaseId(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO));
		ClaimCaseVO claimCaseBackVO = BeanUtils.copyProperties(ClaimCaseVO.class, claimCaseBackBO);
		return claimCaseBackVO;
	}
	
	/**
	 * @description 工作流调用
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBpmRequestVO 工作流入参
	 * @return 返回结果
	*/
	public String createPayChangeBpm(ClaimBpmRequestVO claimBpmRequestVO){
		//1 工作流调用
		ClaimBpmRequestBO claimBpmRequestBO = BeanUtils.copyProperties(ClaimBpmRequestBO.class, claimBpmRequestVO);
		return claimPayChangeService.createPayChangeBpm(claimBpmRequestBO);
	}
	
	/**
	 * @description 查询用户信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param userVO 用户信息
	 * @return 
	*/
	public UserVO findUser(UserVO userVO){
		UserBO userBO = BeanUtils.copyProperties(UserBO.class, userVO);
		//1 查询用户信息
		return BeanUtils.copyProperties(UserVO.class, claimPayChangeService.findUser(userBO));
	}
	/**
	 * @description 调用收付费接口
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param payChangeVO 付费变更数据
	 * @return 返回结果
	*/
	public String payChangeForCap(ClaimPayChangeLogVO payChangeVO){
		ClaimPayChangeLogBO claimPayChangeLogBO = BeanUtils.copyProperties(ClaimPayChangeLogBO.class, payChangeVO);
		//1 调用收付费接口
		return claimPayChangeService.payChangeForCap(claimPayChangeLogBO);	
	}
	/**
	 * @description 查询任务号
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBpmRequestVO 工作流入参
	 * @return 返回结果
	 * @throws Exception 异常信息
	*/
	public String queryTaskId(ClaimBpmRequestVO claimBpmRequestVO) throws Exception{
		//1 查询任务号
		return claimPayChangeService.queryTaskId(BeanUtils.copyProperties(ClaimBpmRequestBO.class, claimBpmRequestVO));	
	}
	/**
	 * @description 提交审核工作流
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBpmRequestVO 工作流入参
	 * @return 返回结果
	 * @throws Exception 异常处理 
	*/
	public String submitBpmTask(ClaimBpmRequestVO claimBpmRequestVO)throws Exception{
		//1 提交审核工作流
		return claimPayChangeService.submitBpmTask(BeanUtils.copyProperties(ClaimBpmRequestBO.class, claimBpmRequestVO));
	}
	/**
	 * @description 提交申请工作流
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBpmRequestVO 工作流入参
	 * @return 返回结果
	 * @throws Exception 异常信息
	*/
	public String submitBpmTaskApply(ClaimBpmRequestVO claimBpmRequestVO)throws Exception{
		//1 提交申请工作流
		return claimPayChangeService.submitBpmTaskApply(BeanUtils.copyProperties(ClaimBpmRequestBO.class, claimBpmRequestVO));
	}
	
	/**
	 * @description 撤销工作流
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBpmRequestVO 工作流数据
	 * @return 返回结果
	 * @throws Exception 异常处理 
	*/
	public String cancelProcess(ClaimBpmRequestVO claimBpmRequestVO)throws Exception{
		//1 撤销工作流
		return claimPayChangeService.cancelProcess(BeanUtils.copyProperties(ClaimBpmRequestBO.class, claimBpmRequestVO));
	}
	/**
	 * @description 查询操作轨迹
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimPayChangeVO 付费变更数据
	 * @return 返回集合
	*/
	public List<OperateTraceVO> queryTaskInstance(ClaimPayChangeVO claimPayChangeVO){
		//1 查询操作轨迹
		return BeanUtils.copyList(OperateTraceVO.class, claimPayChangeService.queryTaskInstance(BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO)));	
	}
	
	/**
	 * @description 查询审核节点的所有任务id
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBpmRequestVO 工作里入参
	 * @return  返回结果
	 * @throws Exception 异常处理
	*/
	@Override
	public String queryAllTaskIdOfCheck(ClaimBpmRequestVO claimBpmRequestVO) throws Exception {
		//1  查询审核节点的所有任务id
		return claimPayChangeService.queryAllTaskIdOfCheck(BeanUtils.copyProperties(ClaimBpmRequestBO.class, claimBpmRequestVO));	
	}
	
	/**
	 * @description 查询该案件是否银行在途
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimPayChangeVO 付费变更数据
	 * @return  返回结果
	*/
	public ClaimPayChangeVO reconciliation(ClaimPayChangeVO claimPayChangeVO){
		//1 查询该案件是否银行在途
		return BeanUtils.copyProperties(ClaimPayChangeVO.class, claimPayChangeService.reconciliation(
				BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO)));
	}

	/**
	 * @description 查询支付方式
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bankWayVO 银行信息
	 * @return 返回结果
	*/
	public List<BankWayVO> findBankWay(BankWayVO bankWayVO) {
		//1 查询支付方式
		return BeanUtils.copyList(BankWayVO.class,claimPayChangeService.findBankWay(BeanUtils.copyProperties(BankWayBO.class,bankWayVO)));
	}

	/**
	 * @description 查询保单数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractMasterVO 保单抄单数据
	 * @return 返回抄单数据
	*/
	@Override
	public List<ContractMasterVO> findAllContractMaster(ContractMasterVO contractMasterVO) {
		//1 查询保单数据
		return BeanUtils.copyList(ContractMasterVO.class, claimPayChangeService.findAllContractMaster(
				BeanUtils.copyProperties(ContractMasterBO.class, contractMasterVO)));
	}

	/**
	 * @description 修改收付信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param premArapVO 收付信息
	*/
	@Override
	public void updatePremArapWait(PremArapVO premArapVO) {
		//1 修改收付信息
		claimPayChangeService.updatePremArapWait(
				BeanUtils.copyProperties(PremArapBO.class, premArapVO));
		
	}
	
	/**
	 * @description 查询收费信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param premArapVO 收付信息
	 * @return 收付信息
	*/
	public PremArapVO findPremArap(PremArapVO premArapVO){
		//1 查询收费信息
		return BeanUtils.copyProperties(PremArapVO.class, claimPayChangeService.findPremArap(
				BeanUtils.copyProperties(PremArapBO.class, premArapVO)));
	}

	/**
	 * @description 修改收付信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param premArapVO 收付信息
	 * @return 返回结果
	*/
	@Override
	public PremArapVO updatePremArap(PremArapVO premArapVO) {
		//1 修改收付信息
		return BeanUtils.copyProperties(PremArapVO.class, claimPayChangeService.updatePremArap(
				BeanUtils.copyProperties(PremArapBO.class, premArapVO)));
	}

	/**
	 * @description 保单挂起解挂
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimPayChangeVO 收付信息
	 * @return 返回结果
	*/
	@Override
	public boolean lockOrClean(ClaimPayChangeVO claimPayChangeVO) {
		//1 保单挂起解挂
		return claimPayChangeService.lockOrClean(BeanUtils.copyProperties(ClaimPayChangeBO.class, claimPayChangeVO));
	}

	/**
	 * @description 修改状态
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 付费变更集合
	 * @return  返回map
	*/
	@Override
	public CommonMapVO findClaimPayChangeCheckToService(CommonMapVO commonMapVO) {
		ClaimBpmRequestVO claimBpmRequestVO=(ClaimBpmRequestVO)commonMapVO.get("claimBpmRequestVO");
		String caseNo=(String)commonMapVO.get("caseNo");
		BigDecimal caseId=(BigDecimal) commonMapVO.get("caseId");
		CurrentPage currentPage=(CurrentPage)commonMapVO.get("currentPage");
		CurrentPage getCurrentPage=(CurrentPage)commonMapVO.get("getCurrentPage");
		String taskId="";
		ClaimCaseVO claimCaseVO = new ClaimCaseVO();
		try {
			 taskId = queryTaskId(claimBpmRequestVO);
		} catch (Exception e) {
			e.printStackTrace();
			throw new BizException("系统异常");
		}
			
		ClaimPayChangeVO claimPayChangeVO1 = new ClaimPayChangeVO();
		//1.根据赔案号查询赔案id
		if(caseNo!=null&&!"".equals(caseNo)){
			claimCaseVO.setCaseNo(caseNo);
			claimCaseVO = findClaimCase(claimCaseVO);
			caseId = claimCaseVO.getCaseId();
			claimPayChangeVO1.setCaseId(caseId);
		}
		claimPayChangeVO1.setAuditStatus(ClaimConstant.REAUDIT_STATUS);	//待处理
		currentPage = queryClaimPayChangeForPage(claimPayChangeVO1, getCurrentPage);
		CommonMapVO commonMapVO2=new CommonMapVO();
        commonMapVO2.put("currentPage", currentPage);
        commonMapVO2.put("claimCase",claimCaseVO);
		return commonMapVO2;
	}

	/**
	 * @description 查询工作流
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 查询map
	 * @return 返回结果
	*/
	@Override
	public CommonMapVO viladeChangeCheckToService(CommonMapVO commonMapVO) {
		ClaimPayChangeVO claimPayChangeVO=(ClaimPayChangeVO)commonMapVO.get("claimPayChangeVO");
		ClaimBpmRequestVO claimBpmRequestVO=(ClaimBpmRequestVO)commonMapVO.get("claimBpmRequestVO");
		
		claimPayChangeVO = reconciliation(claimPayChangeVO);
		String taskId = "";
		try {
			//1 查询taskID
			taskId = queryAllTaskIdOfCheck(claimBpmRequestVO);
		} catch (Exception e) {
			e.printStackTrace();
			throw new BizException("系统异常");
		}
		CommonMapVO commonMapVO2=new CommonMapVO();
        commonMapVO2.put("taskId", taskId);
        commonMapVO2.put("claimPayChangeVO", claimPayChangeVO);
		return commonMapVO2;
	}

	/**
	 * @description 保存收付信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 付费变更map
	 * @return 返回map
	*/
	@Override
	public CommonMapVO saveChangeInfoToService(CommonMapVO commonMapVO) {
		CommonMapVO commonMapVO2=new CommonMapVO();
		ClaimPayChangeVO claimPayChangeVO=(ClaimPayChangeVO)commonMapVO.get("claimPayChangeVO");
		ClaimPayChangeLogVO bfPayChangeVO=(ClaimPayChangeLogVO)commonMapVO.get("bfPayChangeVO");
		ClaimCaseVO claimCaseVO=(ClaimCaseVO)commonMapVO.get("claimCaseVO");
		String userId=(String)commonMapVO.get("userId");
		String organCode=(String)commonMapVO.get("organCode");
		Map map= (Map<String, Object>)commonMapVO.get("map");
		BigDecimal applyId = claimPayChangeVO.getApplyId();
		boolean flag2 = true;
		try {
			//反洗钱名单检测
		    boolean flag = false;
		    if(bfPayChangeVO.getBfPayeeName()!=null && !bfPayChangeVO.getBfPayeeName().equals(claimPayChangeVO.getPayeeName())) {
		    	flag = true;
		    }else if(bfPayChangeVO.getBfPayeeNation()!=null && !bfPayChangeVO.getBfPayeeNation().equals(claimPayChangeVO.getPayeeNation())) {
		    	flag = true;
		    }else if(bfPayChangeVO.getBfPayeeGender()!=null && !bfPayChangeVO.getBfPayeeGender().equals(claimPayChangeVO.getPayeeGender())) {
		    	flag = true;
		    }else if(bfPayChangeVO.getBfPayeeCertiNo()!=null && !bfPayChangeVO.getBfPayeeCertiNo().equals(claimPayChangeVO.getPayeeCertiNo())) {
		    	flag = true;
		    }else if(bfPayChangeVO.getBfPayeeBirth()!=null && !bfPayChangeVO.getBfPayeeBirth().equals(claimPayChangeVO.getPayeeBirth())) {
		    	flag = true;
		    }
		    if(flag) {
		    	//3 查询赔案对象
				if(claimCaseVO==null){
				    claimCaseVO= new ClaimCaseVO();
				}
				claimCaseVO.setCaseId(claimPayChangeVO.getCaseId());
				ClaimCaseBO claimCaseBO = claimCaseService.findClaimCase(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO));
				
				//保单集合
				ClaimPolicyHolderBO claimPolicyHolderBOCondi = new ClaimPolicyHolderBO();
				claimPolicyHolderBOCondi.setCaseId(claimCaseBO.getCaseId());
				List<ClaimPolicyHolderBO> claimPolicyHolderBOs = claimCaseService.findPolicyListByCaseNo(claimPolicyHolderBOCondi);
				
		    	List<BlacklistReqVo> blacklistReqVoList = new ArrayList<>(); 
	            BlacklistReqVo blacklistReqVo = new BlacklistReqVo();
	            blacklistReqVo.setCoreFlag(ClaimConstant.CORE_FLAG);
	            //blacklistReqVo.setState(claimPayChangeVO.getPayeeState());//州、省
	            blacklistReqVo.setCustomerName(claimPayChangeVO.getPayeeName());//客户名称
	            blacklistReqVo.setRemarks("");
	            blacklistReqVo.setIdType(claimPayChangeVO.getPayeeCertiType());
	            blacklistReqVo.setNationality(claimPayChangeVO.getPayeeNation());//国籍
	            blacklistReqVo.setCustomerType(ClaimConstant.PAYEE_FLAG);//客户类型
	            if(claimPayChangeVO.getPayeeId()!=null) {
	            blacklistReqVo.setCustomerNo(claimPayChangeVO.getPayeeId().toString());
	            }
				String applyCode = "";//投保单号
				String policyCode = "";//保单号
				String customerId = "";//客户号
				String customerName = "";//客户姓名
				String customerRole = "";
				if(CollectionUtilEx.isNotEmpty(claimPolicyHolderBOs)){
					for(ClaimPolicyHolderBO claimPolicyHolderBO:claimPolicyHolderBOs){
						applyCode = applyCode+claimPolicyHolderBO.getApplyCode()+",";
						policyCode = policyCode+claimPolicyHolderBO.getPolicyCode()+",";
						customerId = customerId+claimPolicyHolderBO.getCustomerId()+",";
						customerName = customerName+claimPolicyHolderBO.getCustomerName()+",";
						customerRole = claimCaseService.getPayChangeRole(claimPayChangeVO, claimPolicyHolderBO,customerRole);
					}
				}
				if(applyCode.endsWith(",")){
					applyCode = applyCode.substring(0,applyCode.length()-1);
				}
				if(policyCode.endsWith(",")){
					policyCode = policyCode.substring(0,policyCode.length()-1);
				}
				if(customerId.endsWith(",")){
					customerId = customerId.substring(0,customerId.length()-1);
				}
				if(customerName.endsWith(",")){
					customerName = customerName.substring(0,customerName.length()-1);
				}
				if(customerRole.endsWith(",")){
					customerRole = customerRole.substring(0,customerRole.length()-1);
				}
				blacklistReqVo.setPrtNo(applyCode);//投保单号
				blacklistReqVo.setPolicyNo(policyCode);//保单号
				blacklistReqVo.setHolderName(customerName);//客户姓名
				blacklistReqVo.setHolderCustomerId(customerId);//客户
				blacklistReqVo.setCustomerRoleName(customerRole);//客户角色
				blacklistReqVo.setOtherNoType(ClaimConstant.CLAIM_FLAG);//业务号码类型
	            blacklistReqVo.setOtherNo(claimCaseBO.getCaseNo());
	            blacklistReqVo.setBirthday(claimPayChangeVO.getPayeeBirth());//出生日期
	            blacklistReqVo.setDistrict(claimPayChangeVO.getPayeeDistrict());
	            blacklistReqVo.setOccupation(claimPayChangeVO.getPayeeJobCode());
	            blacklistReqVo.setAddress(claimPayChangeVO.getPayeeAddress());
	            //blacklistReqVo.setPolicyNo(claimPayChangeVO.getPolicyCode());
                blacklistReqVo.setIdNo(claimPayChangeVO.getPayeeCertiNo());//证件号码
                blacklistReqVo.setGender(claimPayChangeVO.getPayeeGender()==null?null:claimPayChangeVO.getPayeeGender().toString());//性别
                blacklistReqVo.setBussType(ClaimConstant.STRING_THREE);
                //blacklistReqVo.setCity(claimPayChangeVO.getPayeeCity());
                blacklistReqVo.setForeignPoliticians(ClaimConstant.FOREIGN_FLAG);
                blacklistReqVo.setPolicyOrg(claimCaseBO.getOrganCode());
                //blacklistReqVo.setNotSendFlag(true);
                blacklistReqVoList.add(blacklistReqVo);
                logger.info("反洗钱黑名单接口入参！blacklistReqVoList："+blacklistReqVoList);
                List<BlacklistRspVo> blacklistRspVos = BOServiceFactory.getBlacklistInfo().queryBlacklistInfo(blacklistReqVoList);
                if(blacklistRspVos!=null && blacklistRspVos.size()>0) {
                	for(BlacklistRspVo blacklistRspVo : blacklistRspVos) {
                    	//非真实匹配 继续流程  否则阻断
                    	if(blacklistRspVo.getBlacklistState().compareTo(new BigDecimal(3))!=0
                    			&&blacklistRspVo.getBlacklistState().compareTo(new BigDecimal(0))!=0) {
                    		commonMapVO2.put("flag", "222");
            		        commonMapVO2.put("message", blacklistRspVo.getMessage());
            		        flag2 = false;
            				logger.info("反洗钱黑名单接口调用异常！message："+blacklistRspVo.getMessage());
                    	}
                    }
                }
		    }
		    if(flag2) {
		    	//1 业务流水号
				String unitNumber = claimPayChangeVO.getUnitNumber();
				claimPayChangeVO.setAuditStatus(ClaimConstant.REAUDIT_STATUS);		//待复核
				if(claimPayChangeVO.getAccountNo()==null || "".equals(claimPayChangeVO.getAccountNo())) {
					if(bfPayChangeVO.getBfAccountNo()!=null  && "".equals(bfPayChangeVO.getBfAccountNo())) {
						claimPayChangeVO.setAccountNo(bfPayChangeVO.getBfAccountNo());
					}
				}
				//变更法人信息赋值			
				bfPayChangeVO.setAfLegalPersonId(claimPayChangeVO.getAfLegalPersonId());
				bfPayChangeVO.setBfLegalPersonId(claimPayChangeVO.getBfLegalPersonId());
				bfPayChangeVO.setRelaListId(claimPayChangeVO.getRelaListId());
				bfPayChangeVO.setBfPersonFlag(claimPayChangeVO.getPersonFlag());
				bfPayChangeVO.setLegalPersonChangeFlag(claimPayChangeVO.getLegalPersonChangeFlag());
				bfPayChangeVO.setAfPayMode(claimPayChangeVO.getPayMode());
				bfPayChangeVO.setAfBankCode(claimPayChangeVO.getBankCode());
				bfPayChangeVO.setAfAccountName(claimPayChangeVO.getAccountName());
				if(claimPayChangeVO.getAccountNo() != null && !"".equals(claimPayChangeVO.getAccountNo())){
					bfPayChangeVO.setAfAccountNo(claimPayChangeVO.getAccountNo());
				}
				 if(claimPayChangeVO.getBankOfDeposit() != null && !"".equals(claimPayChangeVO.getBankOfDeposit())){
					bfPayChangeVO.setAfBankOfDeposit(claimPayChangeVO.getBankOfDeposit());
	             }
				bfPayChangeVO.setAfPayeeName(claimPayChangeVO.getPayeeName());
				bfPayChangeVO.setAfPayeeBirth(claimPayChangeVO.getPayeeBirth());
				bfPayChangeVO.setAfPayeeCertiType(claimPayChangeVO.getPayeeCertiType());
				bfPayChangeVO.setAfPayeeCertiNo(claimPayChangeVO.getPayeeCertiNo());
				bfPayChangeVO.setAfPayeeCertiStart(claimPayChangeVO.getPayeeCertiStart());
				bfPayChangeVO.setAfPayeeCertiEnd(claimPayChangeVO.getPayeeCertiEnd());
				bfPayChangeVO.setAfPayeeGender(claimPayChangeVO.getPayeeGender());
				bfPayChangeVO.setAfPayeeNation(claimPayChangeVO.getPayeeNation());
				
				bfPayChangeVO.setAfPayeeRelation(claimPayChangeVO.getPayeeRelation());
				bfPayChangeVO.setAfPayeePhone(claimPayChangeVO.getPayeePhone());
				bfPayChangeVO.setAfPayeeJobCode(claimPayChangeVO.getPayeeJobCode());
				bfPayChangeVO.setAfPayeeState(claimPayChangeVO.getPayeeState());
				bfPayChangeVO.setAfPayeeCity(claimPayChangeVO.getPayeeCity());
				bfPayChangeVO.setAfPayeeDistrict(claimPayChangeVO.getPayeeDistrict());
				bfPayChangeVO.setAfPayeeAddress(claimPayChangeVO.getPayeeAddress());
				
				ClaimPayChangeVO myclaimPayChangeVO = new ClaimPayChangeVO(); 
				ClaimPayChangeVO oldClaimPayChangeVO = new ClaimPayChangeVO(); 
				
				//2 查询同一unitNumer的收付费信息
				PremArapVO premArapVO1 = new PremArapVO();
				premArapVO1.setUnitNumber(unitNumber);
				List<PremArapBO> premArapBOList =  claimPayChangeService.findPremArapAndApplyId(
				        BeanUtils.copyProperties(PremArapBO.class, premArapVO1));
				
				for(int t = 0; t < premArapBOList.size(); t++){
				    if(applyId!=null){
				        ClaimPayChangeVO payChangVO = new ClaimPayChangeVO();
				        payChangVO.setUnitNumber(premArapBOList.get(t).getUnitNumber());
				        payChangVO.setFeeId(premArapBOList.get(t).getListId());
				        payChangVO.setCaseId(claimPayChangeVO.getCaseId());
				        oldClaimPayChangeVO = findClaimPayChange(payChangVO);
				        //2.1 变更历史--变更之前
		                bfPayChangeVO.setBfPayMode(oldClaimPayChangeVO.getPayMode());
		                bfPayChangeVO.setBfBankCode(oldClaimPayChangeVO.getBankCode());
		                bfPayChangeVO.setBfAccountName(oldClaimPayChangeVO.getAccountName());
		                if(oldClaimPayChangeVO.getAccountNo() != null){
		                    bfPayChangeVO.setBfAccountNo(oldClaimPayChangeVO.getAccountNo());
		                }
		                if(oldClaimPayChangeVO.getBankOfDeposit() != null){
		                    bfPayChangeVO.setBfBankOfDeposit(oldClaimPayChangeVO.getBankOfDeposit());
		                }
		                bfPayChangeVO.setBfPayeeName(oldClaimPayChangeVO.getPayeeName());
		                bfPayChangeVO.setBfPayeeBirth(oldClaimPayChangeVO.getPayeeBirth());
		                bfPayChangeVO.setBfPayeeCertiType(oldClaimPayChangeVO.getPayeeCertiType());
		                bfPayChangeVO.setBfPayeeCertiNo(oldClaimPayChangeVO.getPayeeCertiNo());
		                bfPayChangeVO.setBfPayeeCertiStart(oldClaimPayChangeVO.getPayeeCertiStart());
		                bfPayChangeVO.setBfPayeeCertiEnd(oldClaimPayChangeVO.getPayeeCertiEnd());
		                bfPayChangeVO.setBfPayeeGender(oldClaimPayChangeVO.getPayeeGender());
		                bfPayChangeVO.setBfPayeeNation(oldClaimPayChangeVO.getPayeeNation());
		                
						bfPayChangeVO.setBfPayeePhone(oldClaimPayChangeVO.getPayeePhone());
		                bfPayChangeVO.setBfPayeeRelation(oldClaimPayChangeVO.getPayeeRelation());
						bfPayChangeVO.setBfPayeeJobCode(oldClaimPayChangeVO.getPayeeJobCode());
						bfPayChangeVO.setBfPayeeState(oldClaimPayChangeVO.getPayeeState());
						bfPayChangeVO.setBfPayeeCity(oldClaimPayChangeVO.getPayeeCity());
						bfPayChangeVO.setBfPayeeDistrict(oldClaimPayChangeVO.getPayeeDistrict());
						bfPayChangeVO.setBfPayeeAddress(oldClaimPayChangeVO.getPayeeAddress());
						// 未选中的不要更新领款人与受益人关系
						if (StringUtils.isNotEmpty(claimPayChangeVO.getSelectFeeIds()) 
								&& !claimPayChangeVO.getSelectFeeIds().contains(premArapBOList.get(t).getListId().toString())) {
							bfPayChangeVO.setAfPayeeRelation(oldClaimPayChangeVO.getPayeeRelation());
						}
		                bfPayChangeVO.setApplyId(premArapBOList.get(t).getApplyId());
						// 保存付费变更日志
		                claimPayChangeLogService.addClaimPayChangeLog(BeanUtils.copyProperties(ClaimPayChangeLogBO.class, bfPayChangeVO));
		                logger.debug("保存付费变更日志保存方法结束renxiaodi");
		                //2.2 修改付费变更
		                oldClaimPayChangeVO.setUnitNumber(claimPayChangeVO.getUnitNumber());
		                oldClaimPayChangeVO.setAuditStatus(ClaimConstant.REAUDIT_STATUS);   //待复核
		                oldClaimPayChangeVO.setPayMode(claimPayChangeVO.getPayMode());
		                oldClaimPayChangeVO.setBankCode(claimPayChangeVO.getBankCode());
		                if(claimPayChangeVO.getBankOfDeposit() != null){
		                	oldClaimPayChangeVO.setBankOfDeposit(claimPayChangeVO.getBankOfDeposit());
		                 }
		                oldClaimPayChangeVO.setAccountName(claimPayChangeVO.getAccountName());
		                oldClaimPayChangeVO.setAccountNo(claimPayChangeVO.getAccountNo());
		                oldClaimPayChangeVO.setPayeeName(claimPayChangeVO.getPayeeName());
		                oldClaimPayChangeVO.setPayeeBirth(claimPayChangeVO.getPayeeBirth());
		                oldClaimPayChangeVO.setPayeeCertiType(claimPayChangeVO.getPayeeCertiType());
		                oldClaimPayChangeVO.setPayeeCertiNo(claimPayChangeVO.getPayeeCertiNo());
		                oldClaimPayChangeVO.setPayeeCertiStart(claimPayChangeVO.getPayeeCertiStart());
		                oldClaimPayChangeVO.setPayeeCertiEnd(claimPayChangeVO.getPayeeCertiEnd());
		                oldClaimPayChangeVO.setPayeeGender(claimPayChangeVO.getPayeeGender());
		                oldClaimPayChangeVO.setPayeeNation(claimPayChangeVO.getPayeeNation());
		                // 选中的才更新领款人与受益人关系
						if (StringUtils.isNotEmpty(claimPayChangeVO.getSelectFeeIds()) 
								&& claimPayChangeVO.getSelectFeeIds().contains(premArapBOList.get(t).getListId().toString())) {
		                	oldClaimPayChangeVO.setPayeeRelation(claimPayChangeVO.getPayeeRelation());
						}
						oldClaimPayChangeVO.setPayeePhone(claimPayChangeVO.getPayeePhone());
						oldClaimPayChangeVO.setPayeeJobCode(claimPayChangeVO.getPayeeJobCode());
						oldClaimPayChangeVO.setPayeeState(claimPayChangeVO.getPayeeState());
						oldClaimPayChangeVO.setPayeeCity(claimPayChangeVO.getPayeeCity());
						oldClaimPayChangeVO.setPayeeDistrict(claimPayChangeVO.getPayeeDistrict());
						oldClaimPayChangeVO.setPayeeAddress(claimPayChangeVO.getPayeeAddress());
		                oldClaimPayChangeVO.setChangeBy(claimPayChangeVO.getChangeBy());
		                //110494 申请时审核人原不清空
		                //oldClaimPayChangeVO.setAuditBy(null);
		                //oldClaimPayChangeVO.setAuditTime(null);
		                oldClaimPayChangeVO.setChangeDesc(claimPayChangeVO.getChangeDesc());
		                oldClaimPayChangeVO.setPayChangeReason(claimPayChangeVO.getPayChangeReason());
		                updateClaimPayChange(oldClaimPayChangeVO);
		                logger.debug("保存付费变更申请信息保存方法结束renxiaodi");
						// 还原 payeeRelation，避免影响被选中的
						bfPayChangeVO.setAfPayeeRelation(claimPayChangeVO.getPayeeRelation());
				    }else{
						String originalPayeeRelation = claimPayChangeVO.getPayeeRelation();
						String bfPayeeRelation = bfPayChangeVO.getBfPayeeRelation();
						boolean isNotSelected = StringUtils.isNotEmpty(claimPayChangeVO.getSelectFeeIds())
								&& !claimPayChangeVO.getSelectFeeIds().contains(premArapBOList.get(t).getListId().toString());
						// 未被选中的不要更新领款人与受益人关系
						if (isNotSelected) {
							ClaimPayChangePO claimPayChangePOTemp = new ClaimPayChangePO();
							claimPayChangePOTemp.setFeeId(premArapBOList.get(t).getListId());
							CurrentPage<ClaimPayChangePO> currentPageTemp = new CurrentPage<>();
							currentPageTemp.setPageNo(ClaimConstant.ONE);
							currentPageTemp.setPageSize(ClaimConstant.TWENTY);
							CurrentPage<ClaimPayChangePO> payChangePage = claimPayChangeDao.queryPayChangeForPage(claimPayChangePOTemp, BeanUtils.copyCurrentPage(ClaimPayChangePO.class, currentPageTemp));
							if (!payChangePage.getPageItems().isEmpty()) {
								String notSelectedPayeeRelation = payChangePage.getPageItems().get(0).getPayeeRelation();
								claimPayChangeVO.setPayeeRelation(notSelectedPayeeRelation);
							}
						}
				        claimPayChangeVO.setFeeId(premArapBOList.get(t).getListId());
				        claimPayChangeVO.setUnitNumber(premArapBOList.get(t).getUnitNumber());
						// 保存申请数据
	                    myclaimPayChangeVO = addClaimPayChange(claimPayChangeVO);
	                    claimPayChangeVO.setApplyId(myclaimPayChangeVO.getApplyId());
	                    bfPayChangeVO.setApplyId(myclaimPayChangeVO.getApplyId());
	                    bfPayChangeVO.setFeeId(myclaimPayChangeVO.getFeeId());
						if (isNotSelected) {
							bfPayChangeVO.setBfPayeeRelation(claimPayChangeVO.getPayeeRelation());
							bfPayChangeVO.setAfPayeeRelation(claimPayChangeVO.getPayeeRelation());
						}
						// 保存轨迹
	                    claimPayChangeLogService.addClaimPayChangeLog(BeanUtils.copyProperties(ClaimPayChangeLogBO.class, bfPayChangeVO));
	                    logger.debug("保存付费变更轨迹保存方法结束renxiaodi");
						// 还原 payeeRelation，避免影响被选中的
						claimPayChangeVO.setPayeeRelation(originalPayeeRelation);
						bfPayChangeVO.setAfPayeeRelation(originalPayeeRelation);
						bfPayChangeVO.setBfPayeeRelation(bfPayeeRelation);
				    }
				    
				    //2.3 查询并更新收付费信息
				    PremArapVO premArapVO = BeanUtils.copyProperties(PremArapVO.class, premArapBOList.get(t));
	                premArapVO.setPayMode(claimPayChangeVO.getPayMode());
	                premArapVO.setBankCode(claimPayChangeVO.getBankCode());
	                if(claimPayChangeVO.getBankOfDeposit() != null){
	                	 premArapVO.setCipBranchBankCode(claimPayChangeVO.getBankOfDeposit());
	                 }
	                premArapVO.setBankAccount(claimPayChangeVO.getAccountNo());
	                premArapVO.setBankUserName(claimPayChangeVO.getAccountName());
	                premArapVO.setPayeeName(claimPayChangeVO.getPayeeName());
	                premArapVO.setCertiType(claimPayChangeVO.getPayeeCertiType());
	                premArapVO.setCertiCode(claimPayChangeVO.getPayeeCertiNo());
		            //@invalid 判断领款人姓名是否包含"法院" 
		       	    if(claimPayChangeVO.getPayeeName().contains("法院")){
		       	    	premArapVO.setIsCorporate(ClaimConstant.YES);
		       	    }
	                updatePremArap(premArapVO);
				}
				//3 查询赔案对象
				if(claimCaseVO==null){
				    claimCaseVO= new ClaimCaseVO();
				}
				claimCaseVO.setCaseId(claimPayChangeVO.getCaseId());
				ClaimCaseBO claimCaseBO = claimCaseService.findClaimCase(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO));
				claimCaseVO=BeanUtils.copyProperties(ClaimCaseVO.class, claimCaseBO);
				
				ClaimBpmRequestVO claimBpmRequestVO = new ClaimBpmRequestVO();
				//4 付费申请主键
				if(applyId!=null){
					claimBpmRequestVO.setApplyId(applyId);
				}else{
					claimBpmRequestVO.setApplyId(myclaimPayChangeVO.getApplyId());
				}
				claimBpmRequestVO.setOperator(userId);
				claimBpmRequestVO.setOrganCode(organCode);
				claimBpmRequestVO.setCaseNo(claimCaseVO.getCaseNo());
				claimBpmRequestVO.setGreenFlag(claimCaseVO.getGreenFlag());
				claimBpmRequestVO.setUnitNumber(unitNumber);
				logger.debug("工作流提交开始renxiaodi");
				String bizResCd = "1";
				if(oldClaimPayChangeVO.getPayAuditCon() != null && new BigDecimal(1).compareTo(oldClaimPayChangeVO.getPayAuditCon()) == 0){
					bizResCd = submitBpmTaskApply(claimBpmRequestVO);
					logger.debug("工作流提交完成renxiaodi");
				}else{
					bizResCd = createPayChangeBpm(claimBpmRequestVO);
					logger.debug("工作流创建完成renxiaodi");
				}
				//5 判断接口处理情况，更新付费变更记录复核状态(1:带复核   2 复核退回)
				if("0".equals(bizResCd)){
				 //5.1 加续期锁
	                ContractMasterVO contractMasterVO = new ContractMasterVO();
	                contractMasterVO.setCaseId(claimPayChangeVO.getCaseId());
	                //5.2 付费记录收付费挂起、解挂
	                ClaimPayChangeVO lockPayChange = new ClaimPayChangeVO();
	                lockPayChange.setUnitNumber(unitNumber);
	                lockPayChange.setOperateType("03");
	                logger.debug("付费记录收付费挂起、解挂开始renxiaodi");
	                lockOrClean(lockPayChange);
	                logger.debug("付费记录收付费挂起、解挂完成renxiaodi");
	                JSONObject json = new JSONObject();
	                json.put("APPLY_ID", claimPayChangeVO.getApplyId());
	                map.put("data", json);
	                map.put("statusCode", Constants.DWZ_STATUSCODE_200);
	                map.put("message", Constants.DWZ_MESSAGE_200);
	                logger.info("保存付费申请方法结束");
				}else{ 
					//5.3 接口调用异常
					JSONObject json = new JSONObject();
					json.put("APPLY_ID", claimPayChangeVO.getApplyId());
			        map.put("data", json);
			        map.put("statusCode", Constants.DWZ_STATUSCODE_300);
			        map.put("message", "接口调用异常！");
					logger.info("接口调用异常！");
				}
		    }
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("付费变更申请确认renxiaodi："+claimCaseVO.getCaseNo()+" 付费变更异常异常 "+e);
			throw new BizException("该赔案任务不在当前用户下！");
		}
        commonMapVO2.put("map", map);
		return commonMapVO2;
	}

	/**
	 * @description 修改付费变更状态
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 查询map
	 * @return 返回map
	*/
	@Override
	public CommonMapVO saveCheckDataToService(CommonMapVO commonMapVO) {

		ClaimPayChangeVO claimPayChangeVO=(ClaimPayChangeVO)commonMapVO.get("claimPayChangeVO");
		ClaimPayChangeLogVO payChangeLogVO=(ClaimPayChangeLogVO)commonMapVO.get("payChangeLogVO");
		ClaimPayChangeLogVO claimPayChangeTraceVO=(ClaimPayChangeLogVO)commonMapVO.get("claimPayChangeTraceVO");
		//1 修改账号
		if(claimPayChangeTraceVO.getAfBankCode()!=null&&
		        claimPayChangeTraceVO.getAfBankCode().split("-").length>0&&
		         !"".equals(claimPayChangeTraceVO.getAfBankCode().split("-")[0])){
		    claimPayChangeTraceVO.setAfBankCode(claimPayChangeTraceVO.getAfBankCode().split("-")[0]);
		}else{
	        claimPayChangeTraceVO.setAfBankCode(null);
		}
		String caseNo2=(String)commonMapVO.get("caseNo2");
		BigDecimal check = claimPayChangeVO.getPayAuditCon();
		
		
		try {
			//2 根据主键查询数据
			ClaimPayChangeBO claimPayChangeBO = new ClaimPayChangeBO();
			claimPayChangeBO.setUnitNumber(claimPayChangeVO.getUnitNumber());
			List<ClaimPayChangeVO> claimPayChangeVOList = BeanUtils.copyList(ClaimPayChangeVO.class, 
			        claimPayChangeService.findAllClaimPayChange(claimPayChangeBO));
			
			if(CollectionUtilEx.isNotEmpty(claimPayChangeVOList)){
			    for(int i = 0; i < claimPayChangeVOList.size(); i++){
			        ClaimPayChangeVO laimPayChangeVO = claimPayChangeVOList.get(i);
			        laimPayChangeVO.setAuditBy(claimPayChangeVO.getAuditBy());
		            laimPayChangeVO.setAuditTime(claimPayChangeVO.getAuditTime());
		            laimPayChangeVO.setPayAuditCon(claimPayChangeVO.getPayAuditCon());
		            laimPayChangeVO.setAuditDesc(claimPayChangeVO.getAuditDesc());
		            payChangeLogVO.setApplyId(claimPayChangeVO.getApplyId());
		            payChangeLogVO.setPayAuditCon(claimPayChangeVO.getPayAuditCon());
		            //2.1 复核结论为修改设置复核状态为1（复核退回）
		            BigDecimal status1 = ClaimConstant.REAUDIT_STATUS_ONE;
		            BigDecimal status2 = ClaimConstant.REAUDIT_STATUS_TWO;
		            BigDecimal status3 = ClaimConstant.REAUDIT_STATUS_THREE;
		            if(status1.equals(check)){
		                claimPayChangeVO.setAuditStatus(status1);
		                claimPayChangeVO.setPayAuditCon(status1);
		                laimPayChangeVO.setAuditStatus(status1);
		                laimPayChangeVO.setPayAuditCon(status1);
		            }else if(status2.equals(check)){
		                claimPayChangeVO.setAuditStatus(status2);
		                claimPayChangeVO.setPayAuditCon(status2);
		                laimPayChangeVO.setAuditStatus(status2);
		                laimPayChangeVO.setPayAuditCon(status2);
		            }else if(status3.equals(check)){
		                claimPayChangeVO.setAuditStatus(status3);
		                claimPayChangeVO.setPayAuditCon(status3);
		                laimPayChangeVO.setAuditStatus(status3);
		                laimPayChangeVO.setPayAuditCon(status3);
		            }
		            //2.2 修改数据
		            updateClaimPayChange(laimPayChangeVO);
		            claimPayChangeTraceVO.setApplyId(laimPayChangeVO.getApplyId());
		            claimPayChangeLogService.updateClaimPayChangeLogByApplyId(BeanUtils.copyProperties(ClaimPayChangeLogBO.class, payChangeLogVO));
			    }
			}
			//3调用收付费接口
			if(claimPayChangeVO.getPayAuditCon().toString().equals("2")){
				//3.1 调用收付费接口
			    payChangeForCap(claimPayChangeTraceVO);
				BigDecimal userId = claimPayChangeVO.getChangeBy();
				UserVO userVO = new UserVO();
				userVO.setUserId(userId);
				userVO = findUser(userVO);
				
				if(userVO != null){
					MessageTemplateParamBO messageTemplateParamBO = new MessageTemplateParamBO();
	                messageTemplateParamBO.setAgentName(userVO.getUserName());
	                messageTemplateParamBO.setCaseNo(caseNo2);
	                //3.2 发短信  需求变更#40613
	                //3.3 发邮件
	                if(!StringUtilsEx.isBlank(userVO.getEmail())){
	                	sendMessageService.executeTimeControllMethod(ClaimConstant.SENDER_ADDRESS, userVO.getEmail(), userVO.getUserName(),  ClaimConstant.MESSAGE_MODE_TWOFIVEONE, 
	                			userVO.getOrganId(), caseNo2, "收付费变更提醒", 0 , messageTemplateParamBO);
	                }
				}
	            ClaimCaseVO claimCaseVO1 = new ClaimCaseVO();
				claimCaseVO1.setCaseNo(caseNo2);
				ClaimCaseBO claimCaseBO1 = claimCaseService.findClaimCase(BeanUtils.copyProperties(ClaimCaseBO.class, claimCaseVO1));
				claimCaseVO1=BeanUtils.copyProperties(ClaimCaseVO.class, claimCaseBO1);
				
				//4 调用结束工作流
	            ClaimBpmRequestVO claimBpmRequestVO = new ClaimBpmRequestVO();
				String organCode = AppUserContext.getCurrentUser().getOrganCode();
				claimBpmRequestVO.setCaseNo(caseNo2);
				claimBpmRequestVO.setOrganCode(organCode);
				claimBpmRequestVO.setRecheckState("3");
				claimBpmRequestVO.setGreenFlag(claimCaseVO1.getGreenFlag());
				claimBpmRequestVO.setApplyId(claimPayChangeVO.getApplyId());
				String flag = submitBpmTask(claimBpmRequestVO);
                if("1".equals(flag)) {
                    throw new Exception("提交工作流失败");
                }
				//5 付费记录收付费挂起、解挂
				ClaimPayChangeVO lockPayChange = new ClaimPayChangeVO();
				lockPayChange.setUnitNumber(claimPayChangeVO.getUnitNumber());
				lockPayChange.setOperateType("01");
				lockOrClean(lockPayChange);
				
				//6 更新应收应付状态
				PremArapVO premArapVO = new PremArapVO();
				premArapVO.setBusinessCode(caseNo2);
				premArapVO.setUnitNumber(claimPayChangeVO.getUnitNumber());
				updatePremArapWait(premArapVO);
				//7 复核终止
			}else if(claimPayChangeVO.getPayAuditCon().toString().equals("3")){
				ClaimBpmRequestVO claimBpmRequestVO = new ClaimBpmRequestVO();
				String organCode = AppUserContext.getCurrentUser().getOrganCode();
				claimBpmRequestVO.setCaseNo(caseNo2);
				claimBpmRequestVO.setOrganCode(organCode);
				claimPayChangeVO.setApplyId(claimPayChangeVO.getApplyId());
				cancelProcess(claimBpmRequestVO);
				
				//8 付费记录收付费挂起、解挂
				ClaimPayChangeVO lockPayChange = new ClaimPayChangeVO();
				lockPayChange.setUnitNumber(claimPayChangeVO.getUnitNumber());
				lockPayChange.setOperateType("01");
				lockOrClean(lockPayChange);
				//9 复核修改
			}else if(claimPayChangeVO.getPayAuditCon().toString().equals("1")){
				ClaimBpmRequestVO claimBpmRequestVO = new ClaimBpmRequestVO();
				String organCode = AppUserContext.getCurrentUser().getOrganCode();
				claimBpmRequestVO.setCaseNo(caseNo2);
				claimBpmRequestVO.setOrganCode(organCode);
				claimBpmRequestVO.setRecheckState("2");
				claimBpmRequestVO.setApplyId(claimPayChangeVO.getApplyId());
				String flag = submitBpmTask(claimBpmRequestVO);
				if("1".equals(flag)) {
				    throw new Exception("提交工作流失败");
				}
			}
			//10 付费变更复核结论为2-复核通过或者3-复核终止时，将拉取表中该赔案的拉取状态置为0，以便批处理重新拉取工作流轨迹落库并展示。
			if(check !=null && (check.compareTo(new BigDecimal(ClaimConstant.TWO)) ==0 || check.compareTo(new BigDecimal(ClaimConstant.THREE))==0)){
				if(caseNo2 !=null && !ClaimConstant.NULL_STR.equals(caseNo2)){
					ClaimCaseBO claimCaseBO = new ClaimCaseBO();
					claimCaseBO.setCaseNo(caseNo2);
					approveService.saveClaimTaskBatch(claimCaseBO);
				}
			}
			
		} catch (Exception e) {
		    e.printStackTrace();
			throw new BizException("系统异常");
		}
		return null;
	}

	/**
	 * @description 修改付费变更
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 付费变更map
	 * @return 返回map
	*/
	@Override
	public CommonMapVO getTraceToService(CommonMapVO commonMapVO) {

		ClaimPayChangeVO payChangeVO1=(ClaimPayChangeVO)commonMapVO.get("payChangeVO1");
		ClaimPayChangeVO claimPayChangeVO=(ClaimPayChangeVO)commonMapVO.get("claimPayChangeVO");
		ClaimPayChangeLogVO payChangeLogVO=(ClaimPayChangeLogVO)commonMapVO.get("payChangeLogVO");
		ClaimPayChangeLogVO claimPayChangeTraceVO=(ClaimPayChangeLogVO)commonMapVO.get("claimPayChangeTraceVO");
		String caseNo=(String)commonMapVO.get("caseNo");
		List<ClaimTaskVO> claimTaskTraceList=(List<ClaimTaskVO>)commonMapVO.get("claimTaskTraceList");
		Map<String, Object> dataMap=(Map<String, Object>)commonMapVO.get("dataMap");
		
		try {
			
			claimPayChangeVO = findClaimPayChange(payChangeVO1);
			if(claimPayChangeVO.getAuditBy() != null){
				String auditName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_UDMP_USER", claimPayChangeVO.getAuditBy().toString());
				claimPayChangeVO.setAuditByName(auditName);
			}
			if(claimPayChangeVO.getPayAuditCon() != null){
				String payAuditConName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_PAY_CHANGE_DECI", claimPayChangeVO.getPayAuditCon().toString());
				claimPayChangeVO.setPayAuditConName(payAuditConName); 
			}
			if(claimPayChangeVO.getPayChangeReason() != null){
				String payChangeReason = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_PAY_CHANGE_REASON", claimPayChangeVO.getPayChangeReason());
				claimPayChangeVO.setPayChangeReason(payChangeReason);
			}
			
			//1 修改之后的值。
			ClaimPayChangeLogBO claimPayChangeLogBO2=claimPayChangeLogService.findClaimPayChangeLog(BeanUtils.copyProperties(ClaimPayChangeLogBO.class, payChangeLogVO));
			claimPayChangeTraceVO=BeanUtils.copyProperties(ClaimPayChangeLogVO.class, claimPayChangeLogBO2);
			if(claimPayChangeTraceVO.getAfPayMode() != null){
				String afPayMode = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_PAY_MODE", claimPayChangeTraceVO.getAfPayMode());
				claimPayChangeTraceVO.setAfPayMode(afPayMode);
			}
			if(claimPayChangeTraceVO.getAfBankCode() != null){
				String bankName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_BANK", claimPayChangeTraceVO.getAfBankCode());
				claimPayChangeTraceVO.setAfBankCode(bankName);
			}
			if(claimPayChangeTraceVO.getAfPayeeCertiType() != null){
				String afPayeeCertiType = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_CERTI_TYPE", claimPayChangeTraceVO.getAfPayeeCertiType());
				claimPayChangeTraceVO.setAfPayeeCertiType(afPayeeCertiType);
			}
			if(claimPayChangeTraceVO.getAfPayeeNation() != null){
				String afPayeeNation = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_COUNTRY", claimPayChangeTraceVO.getAfPayeeNation());
				claimPayChangeTraceVO.setAfPayeeNation(afPayeeNation);
			}
			
			//2 修改之前的值。
			if(claimPayChangeTraceVO.getBfPayMode() != null){
				String bfPayMode = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_PAY_MODE", claimPayChangeTraceVO.getBfPayMode());
				claimPayChangeTraceVO.setBfPayMode(bfPayMode);
			}
			if(claimPayChangeTraceVO.getBfBankCode() != null){
				String bankName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_BANK", claimPayChangeTraceVO.getBfBankCode());
				claimPayChangeTraceVO.setBfBankCode(bankName);
			}
			if(claimPayChangeTraceVO.getBfPayeeCertiType() != null){
				String bfPayeeCertiType = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_CERTI_TYPE", claimPayChangeTraceVO.getBfPayeeCertiType());
				claimPayChangeTraceVO.setBfPayeeCertiType(bfPayeeCertiType);
			}
			if(claimPayChangeTraceVO.getBfPayeeNation() != null){
				String afPayeeNation = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_COUNTRY", claimPayChangeTraceVO.getBfPayeeNation());
				claimPayChangeTraceVO.setBfPayeeNation(afPayeeNation);
			}
			ClaimTaskVO claimTaskVO = new ClaimTaskVO();
	        claimTaskVO.setCaseNo(caseNo);
	        if(claimTaskTraceList==null){
	            claimTaskTraceList = new ArrayList<ClaimTaskVO>();
	        }
			claimTaskTraceList= BeanUtils.copyList(ClaimTaskVO.class, queryTaskTraceService.queryTaskInstance1(BeanUtils.copyProperties(ClaimTaskBO.class, claimTaskVO)));
					
			dataMap.put("trace", claimPayChangeTraceVO);
			dataMap.put("apply",claimPayChangeVO);
			dataMap.put("operateTrace", claimTaskTraceList);
		} catch (Exception e) {
			throw new BizException("系统异常");
		}
		
		CommonMapVO commonMapVO2=new CommonMapVO();
        commonMapVO2.put("dataMap", dataMap);
		return commonMapVO2;
	}

}
