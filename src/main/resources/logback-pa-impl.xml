<?xml version="1.0" encoding="UTF-8"?>

<!-- For assistance related to logback-translator or configuration -->
<!-- files in general, please contact the logback user mailing list -->
<!-- at http://www.qos.ch/mailman/listinfo/logback-user -->
<!-- -->
<!-- For professional support please see -->
<!-- http://www.qos.ch/shop/products/professionalSupport -->
<!-- -->
<configuration scan="true" scanPeriod="1 seconds">
	<appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern> %d{yyyy-M-d
				HH:mm:ss}|%t|%p|%X{bizSeq}|%X{transCode}|%X{systemNo}|%X{invokeSeq}|%m|%F|%L|%n
			</pattern>
		</encoder>
	</appender>
	<appender name="stdout2" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
	</appender>
	<appender name="file"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>/logstan/logtest-%d{yyyy-M-d}.log</FileNamePattern>
			<MaxHistory> 30 </MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d
				HH:mm:ss}|%t|%p|%X{bizSeq}|%X{transCode}|%X{systemNo}|%X{invokeSeq}|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>1000MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>

	<!-- 异步输出 -->
	<appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender 
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="file" />
	</appender>

	<logger name="com.atomikos" level="ERROR" />
	<logger name="com.nci.udmp" level="ERROR" />


	<root level="info">
		<appender-ref ref="ASYNC" />
	</root>

</configuration>