<ehcache updateCheck="false" name="cus-cache-manager">
    <!-- 指定数据存储位置，可指定磁盘中的文件夹位置 -->
    <diskStore path="cus-ehcache" />

    <!-- 默认的管理策略 -->
    <defaultCache maxEntriesLocalHeap="10000" eternal="false"
        timeToIdleSeconds="1200" timeToLiveSeconds="1200" overflowToDisk="false"
        maxEntriesLocalDisk="10000000" diskPersistent="false"
        diskExpiryThreadIntervalSeconds="120" memoryStoreEvictionPolicy="LRU">
    </defaultCache>

    <!-- 存放从不或是很少刷新的缓存内容 -->
    <cache name="cus-InmutableCache" maxElementsInMemory="10000"
        eternal="true" overflowToDisk="false" diskPersistent="true" memoryStoreEvictionPolicy="LRU"/>

    <cache name="cus-EasyServiceCache" maxElementsInMemory="10000"
        eternal="true" overflowToDisk="false" diskPersistent="true" memoryStoreEvictionPolicy="LRU"/>

    <!-- 存放中等频度刷新的缓存内容 -->
    <cache name="cus-MidtableCache" maxElementsInMemory="10000"
        eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="3600"
        overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

    <!-- 存放可以随时变化的缓存内容 -->
    <cache name="cus-MutableCache" maxElementsInMemory="10000"
        eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="600"
        overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

    <!-- 注册相应的缓存监听类，用于处理缓存事件：put、remove、update和expire -->
    <!-- 指定相应的bootstrapCacheLoader，用于初始化缓存，以及自动设置-->
    <!-- 缓存业务主键的内容 -->  
    <cache name="udmp-BizKeyCache" maxElementsInMemory="1000"
        eternal="true" overflowToDisk="true"  diskPersistent="true" memoryStoreEvictionPolicy="LRU">
        <cacheEventListenerFactory class="com.nci.udmp.util.generatekey.UdmpCacheEventlistenerFactory" />
        <bootstrapCacheLoaderFactory class="com.nci.udmp.util.generatekey.UdmpBootstrapCacheLoaderFactory" 
            properties="ASYNCHRONOUS=true"/>
    </cache>    

</ehcache>