<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.mobcss.impl.po.CsPreFilledPolicyInfoPO">
	<select id="css_queryPolicyContractBeneList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		SELECT CB.POLICY_CODE, /*保单号*/
	       CB.LIST_ID, /*受益人主键ID*/
	       PEE.PLAN_ID, /*支付计划ID*/       
	       PEE.PAYEE_ACCOUNT_ID ACCOUNT_ID, /*账户ID*/
	       CB.SHARE_ORDER, /*受益顺位*/
	       CB.DESIGNATION, /*与被保人关系*/
	       CB.SHARE_RATE, /*受益比例*/
	       CB.BENE_TYPE, /*受益类型*/
	       (SELECT c.CUSTOMER_NAME FROM APP___PAS__DBUSER.t_Insured_List il
	               inner join APP___PAS__DBUSER.t_customer c on il.customer_Id = c.customer_id
	        WHERE il.list_id = CB.INSURED_ID) AS INSURED_NAME,/*被保人姓名*/      
	       CB.INSURED_ID, /*被保人ID*/    
	       CB.BUSI_ITEM_ID, /*险种ID*/
	       CB.PRODUCT_CODE, /*险种编码*/
	       (SELECT BP.PRODUCT_NAME_SYS
	          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
	         WHERE BP.PRODUCT_CODE_SYS = CB.PRODUCT_CODE) AS PRODUCT_NAME, /*险种名称*/
	       NVL(CU.CUSTOMER_ID, CB.CUSTOMER_ID) AS CUSTOMER_ID, /*客户ID*/
	       NVL(CU.CUSTOMER_NAME, CB.CUSTOMER_NAME) AS CUSTOMER_NAME, /*客户姓名*/
	       NVL(CU.CUSTOMER_GENDER, CB.CUSTOMER_GENDER) AS CUSTOMER_GENDER, /*客户性别*/
	       to_char(NVL(CU.CUSTOMER_BIRTHDAY, CB.CUSTOMER_BIRTHDAY),'yyyy-MM-dd') AS CUSTOMER_BIRTHDAY_STR, /*客户出生日期*/
	       NVL(CU.CUSTOMER_CERT_TYPE, CB.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE, /*客户类型*/
	       NVL(CU.CUSTOMER_CERTI_CODE, CB.CUSTOMER_CERTI_CODE) AS CUSTOMER_CERTI_CODE, /*客户证件号码*/
	       CU.JOB_CODE, /*客户职业编码*/
	       to_char(CU.CUST_CERT_STAR_DATE,'yyyy-MM-dd') AS CERT_START_DATE, /*客户证件有效期起始日期*/
	       to_char(CU.CUST_CERT_END_DATE,'yyyy-MM-dd') AS CERT_END_DATE, /*客户证件有效期结束日期*/
	       CU.COUNTRY_CODE, /*国籍代码*/
	       NVL(AD.MOBILE_TEL, CU.MOBILE_TEL) AS MOBILE_TEL, /*手机号码*/
	       NVL(AD.FIXED_TEL, CU.OFFEN_USE_TEL) AS OFFEN_USE_TEL, /*固定电话号码*/
	       AD.ADDRESS_ID, /*地址主键*/
	       AD.POST_CODE, /*邮编*/
	       AD.STATE, /*省*/
	       AD.CITY, /*市*/
	       AD.DISTRICT, /*县镇*/
	       AD.ADDRESS, /*地址*/
	       PP.SURVIVAL_MODE, /*生存领取形式*/
	       TBA.BANK_ACCOUNT, /*银行账户*/
	       TBA.ACCO_NAME, /*账户名*/
	       TBA.BANK_CODE /*开户行 对应码表T_BANK*/
	  FROM  APP___PAS__DBUSER.T_CONTRACT_BENE CB         
	  INNER JOIN APP___PAS__DBUSER.T_CUSTOMER CU
	    ON CB.CUSTOMER_ID = CU.CUSTOMER_ID
	  INNER JOIN APP___PAS__DBUSER.T_PAY_PLAN_PAYEE PEE
	    ON CB.POLICY_ID = PEE.POLICY_ID
	   AND CB.BUSI_ITEM_ID = PEE.BUSI_ITEM_ID
	   AND CB.CUSTOMER_ID = PEE.CUSTOMER_ID
	  INNER JOIN APP___PAS__DBUSER.T_PAY_PLAN PP
	    ON PP.PLAN_ID = PEE.PLAN_ID
	  INNER JOIN APP___PAS__DBUSER.T_BANK_ACCOUNT TBA
	    ON PEE.PAYEE_ACCOUNT_ID = TBA.ACCOUNT_ID
	  INNER JOIN  APP___PAS__DBUSER.T_ADDRESS AD
	    ON CB.ADDRESS_ID = AD.ADDRESS_ID
	   AND CB.CUSTOMER_ID = AD.CUSTOMER_ID
	   AND CU.CUSTOMER_ID = AD.CUSTOMER_ID
	   AND AD.ADDRESS_STATUS = '1'
	 WHERE CB.POLICY_CODE = #{policy_code} /*保单号*/
 	]]>
 	<if test="list_id != null and list_id != '' ">
 	 	<![CDATA[ AND CB.LIST_ID = #{list_id} ]]>
 	</if>
 	<if test="product_code != null and product_code != '' ">
 	 	<![CDATA[ AND CB.PRODUCT_CODE = #{product_code} ]]>
 	</if>
 	<if test="plan_id != null and plan_id != '' ">
 	 	<![CDATA[ AND PP.PLAN_ID = #{plan_id} ]]>
 	</if>
	</select>
	<!-- 缴费信息查询 -->
	<select id="css_queryPolicyPaymentInfo" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			SELECT 
				  CM.POLICY_CODE,/*保单号*/
				  C.CUSTOMER_NAME,/*本次保全申请的投保人姓名*/
				  PA.NEXT_ACCOUNT_NAME ACCOUNT_NAME,/*续期交费账户名*/
				  PA.NEXT_ACCOUNT_BANK ACCOUNT_BANK,/*续期交费开户银行*/
				  PA.NEXT_ACCOUNT ACCOUNT,/*续期交费银行账户*/  
				  PA.PAY_MODE/*续期交费形式*/
			FROM DEV_PAS.T_PAYER_ACCOUNT PA
				  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
				         ON PA.POLICY_ID = CM.POLICY_ID
				  LEFT JOIN DEV_PAS.T_POLICY_HOLDER PH
				         ON CM.POLICY_ID = PH.POLICY_ID
				  LEFT JOIN DEV_PAS.T_CUSTOMER C
				         ON PH.CUSTOMER_ID = C.CUSTOMER_ID
			WHERE CM.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 更新缴费信息 -->
	<update id="css_updatePreFillPCInfo" parameterType="java.util.Map">
		Update APP___PAS__DBUSER.T_CS_PRE_FILLED_PAYER_ACCOUNT
		  SET    
		   PAY_MODE=#{pay_mode},/*交费形式*/ 
		   UPDATE_BY=#{update_by},/*修改人*/
		   ACCOUNT_NAME= #{account_name},/*户名*/
		   ACCOUNT_BANK=#{account_bank},/*开户银行*/
		   UPDATE_TIMESTAMP=CURRENT_TIMESTAMP,/*修改时间戳*/
		   UPDATE_TIME=SYSDATE,/*修改时间 */
		   ACCOUNT=#{account}/*账号*/
		   WHERE PRE_FILLED_ID=#{pre_filled_id}/*预填单主键*/
	</update>
	<!-- 保存缴费信息 -->
	<insert id="css_savePreFillPCInfo" parameterType="java.util.Map">
		INSERT INTO APP___PAS__DBUSER.T_CS_PRE_FILLED_PAYER_ACCOUNT
		  (INSERT_TIMESTAMP,/*插入时间戳*/
		   POLICY_CODE,/*保单号*/
		   PAY_MODE,/*交费形式*/
		   PRE_FILLED_ID,/*预填单主键*/
		   UPDATE_BY,/*修改人*/
		   INSERT_TIME,/*插入时间*/
		   ACCOUNT_NAME,/*户名*/
		   ACCOUNT_BANK,/*开户银行*/
		   UPDATE_TIMESTAMP,/*修改时间戳*/
		   UPDATE_TIME,/*修改时间 */
		   INSERT_BY,/*插入人*/
		   ACCOUNT/*账号*/)
		VALUES
		  (CURRENT_TIMESTAMP,
		   #{policy_code},
		   #{pay_mode},
		   #{pre_filled_id},
		   #{update_by},
		   SYSDATE,
		   #{account_name},
		   #{account_bank},
		   CURRENT_TIMESTAMP,
		   SYSDATE,
		   #{insert_by},
		   #{account})
	</insert>
	<!-- 缴费信息变更快查 -->
	<select id="findCsPreFilledPCCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		    SELECT count(1) FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_PAYER_ACCOUNT PFA 
		    	WHERE PFA.PRE_FILLED_ID = #{pre_filled_id}
	</select>
	
	<!-- 缴费信息变更快查 -->
	<select id="findCsPreFilledBeneCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		    SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE FCB 
		    	WHERE FCB.PRE_FILLED_ID = #{pre_filled_id}
	</select>
	
	
	<!-- 更新受益人变更信息 -->
	<insert id="css_savePreFillBCInfo" useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="pre_filled_bene_id">
            SELECT APP___PAS__DBUSER.S_CS_PRE_FILLED_CONTRACT_BENE.NEXTVAL FROM DUAL
        </selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE (
		        PRE_FILLED_BENE_ID,PRE_FILLED_ID,POLICY_CODE,LIST_ID,BUSI_ITEM_ID,INSURED_ID,
		        CUSTOMER_NAME,CUSTOMER_BIRTHDAY,CUSTOMER_GENDER,CUSTOMER_CERT_TYPE,CUSTOMER_CERTI_CODE,
		        SHARE_ORDER,CUSTOMER_ID,DESIGNATION,SHARE_RATE,BENE_TYPE,CUST_CERT_STAR_DATE,
		        CUST_CERT_END_DATE,COUNTRY_CODE,JOB_CODE,MOBILE_TEL,FIX_TEL,STATE,CITY,DISTRICT,ADDRESS,
		        POST_CODE,BANK_CODE,BANK_ACCOUNT,ACCO_NAME,SURVIVAL_W_MODE,INSERT_BY,INSERT_TIME,INSERT_TIMESTAMP,
		        UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP) 
		      VALUES (
		        #{pre_filled_bene_id, jdbcType=NUMERIC},#{pre_filled_id, jdbcType=NUMERIC},#{policy_code, jdbcType=VARCHAR},#{list_id, jdbcType=NUMERIC},#{busi_item_id, jdbcType=NUMERIC},#{insured_id, jdbcType=NUMERIC},
		        #{customer_name, jdbcType=VARCHAR},#{customer_birthday, jdbcType=DATE},#{customer_gender, jdbcType=NUMERIC},#{customer_cert_type, jdbcType=VARCHAR},#{customer_certi_code, jdbcType=VARCHAR},
		        #{share_order, jdbcType=NUMERIC},#{customer_id, jdbcType=NUMERIC},#{designation, jdbcType=VARCHAR},#{share_rate, jdbcType=NUMERIC},#{bene_type, jdbcType=NUMERIC},#{cust_cert_star_date, jdbcType=DATE},
		        #{cust_cert_end_date, jdbcType=DATE},#{country_code, jdbcType=VARCHAR},#{job_code, jdbcType=VARCHAR},#{mobile_tel, jdbcType=VARCHAR},#{offen_use_tel, jdbcType=VARCHAR},#{state, jdbcType=VARCHAR},
		        #{city, jdbcType=VARCHAR},#{district, jdbcType=VARCHAR},#{address, jdbcType=VARCHAR},#{post_code, jdbcType=VARCHAR},#{bank_code, jdbcType=VARCHAR},#{bank_account, jdbcType=VARCHAR},
		        #{acco_name, jdbcType=VARCHAR},#{survival_mode, jdbcType=NUMERIC},#{insert_by, jdbcType=NUMERIC},SYSDATE,CURRENT_TIMESTAMP,#{update_by, jdbcType=NUMERIC},SYSDATE,CURRENT_TIMESTAMP)
		 ]]>
	</insert>
	<!-- 初始化险种信息 -->
	<select id="css_findProductCode" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		 SELECT BP.PRODUCT_CODE_SYS PRODUCT_CODE,/*险种编码*/
			    BP.PRODUCT_NAME_SYS PRODUCT_NAME/*险种名称*/
			FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP 
	    		INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP 
	    			ON BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
			WHERE CBP.LIABILITY_STATE = '1' AND CBP.POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	
</mapper>