<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.model.po.CsPreFilledPO">
	<sql id="csPreFilledWhereCondition">
		
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" accept_type != null and accept_type != ''  "><![CDATA[ AND A.ACCEPT_TYPE = #{accept_type} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" pre_filled_id  != null "><![CDATA[ AND A.PRE_FILLED_ID = #{pre_filled_id} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" pre_filled_time  != null  and  pre_filled_time  != ''  "><![CDATA[ AND A.PRE_FILLED_TIME = #{pre_filled_time} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		
		<if test=" agent_cert_code  != null "><![CDATA[ AND A.AGENT_CERT_CODE = #{agent_cert_code} ]]></if>
		<if test=" agent_cert_type != null and agent_cert_type != ''  "><![CDATA[ AND A.AGENT_CERT_TYPE = #{agent_cert_type} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</sql>

	
<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsPreFilledByPreFilledIdCondition">
		<if test=" pre_filled_id  != null "><![CDATA[ AND A.PRE_FILLED_ID = #{pre_filled_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" work_code != null and work_code != ''  "><![CDATA[ AND A.WORK_CODE = #{work_code} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
	</sql>	
	<!-- 快速查询客户变更信息 -->
	<select id="findCsPreFilledCustomerCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		    SELECT count(1) FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER PFC 
		    	WHERE PFC.PRE_FILLED_ID = #{pre_filled_id}
	</select>
	<!-- 查询客户基本信息 -->
	<select id="css_queryCustomerBaseInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CU.EMAIL,/*邮箱*/
				   CU.MOBILE_TEL,/*电话*/
				   CU.OFFEN_USE_TEL/*固定电话*/
			FROM APP___PAS__DBUSER.T_CUSTOMER CU
			INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER PH ON CU.CUSTOMER_ID = PH.CUSTOMER_ID
			WHERE CU.CUSTOMER_ID = #{customer_id} AND PH.POLICY_CODE = #{policy_code}/*客户ID*/
		]]>
	</select>
	<!-- 预填单客户基本信息变更更新 -->
	<update id="css_updatePreFillCCInfo" parameterType="java.util.Map">
		UPDATE APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER
			   SET STATE=  #{state, jdbcType=VARCHAR},/*州、省*/
			   DISTRICT= #{district, jdbcType=VARCHAR},/*区县*/
			   HOUSE_TEL= #{house_tel, jdbcType=VARCHAR},/*固定电话,住宅电话*/
			   ADDRESS_ID = #{address_id, jdbcType=NUMERIC},/*地址ID*/
			   ADDRESS= #{address, jdbcType=VARCHAR},/*地址*/
			   COUNTRY_CODE = #{country_code, jdbcType=VARCHAR},/*国家代码*/
			   UPDATE_TIME=SYSDATE,/*修改时间*/
			   POLICY_CODE= #{policy_code, jdbcType=VARCHAR},/*保单号*/
			   UPDATE_BY= #{update_by, jdbcType=NUMERIC},/*修改人的工号*/
			   POST_CODE= #{post_code, jdbcType=VARCHAR},/*邮编*/
			   MOBILE_TEL= #{mobile_tel, jdbcType=VARCHAR},/*移动电话*/
			   EMAIL= #{email, jdbcType=VARCHAR},/*电子邮箱*/
			   UPDATE_TIMESTAMP=CURRENT_TIMESTAMP,/*修改时间戳*/
			   CITY= #{city, jdbcType=VARCHAR}/*城市名称*/   
			WHERE  PRE_FILLED_ID= #{pre_filled_id, jdbcType=NUMERIC}/*预填单主键*/
	</update>
	<!-- 预填单客户基本信息变更保存 -->
	<insert id="css_savePreFillCCInfo" parameterType="java.util.Map">
		INSERT INTO APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER
			  (ADDRESS_ID,/*地址编号*/
			   STATE,/*州、省*/
			   DISTRICT,/*区县*/
			   PRE_FILLED_ID,/*预填单主键*/
			   HOUSE_TEL,/*固定电话,住宅电话*/
			   COUNTRY_CODE,/*国家代码*/
			   INSERT_TIME,/*创建时间*/
			   ADDRESS,/*地址*/
			   UPDATE_TIME,/*修改时间*/
			   INSERT_TIMESTAMP,/*创建时间戳*/
			   POLICY_CODE,/*保单号*/
			   UPDATE_BY,/*修改人的工号*/
			   POST_CODE,/*邮编*/
			   MOBILE_TEL,/*移动电话*/
			   EMAIL,/*电子邮箱*/
			   UPDATE_TIMESTAMP,/*修改时间戳*/
			   CITY,/*城市名称*/
			   INSERT_BY/*创建人的工号*/)
			VALUES
			  (#{address_id, jdbcType=NUMERIC},
			   #{state, jdbcType=VARCHAR},
			   #{district, jdbcType=VARCHAR},
			   #{pre_filled_id, jdbcType=NUMERIC},
			   #{house_tel, jdbcType=VARCHAR},
			   #{country_code, jdbcType=VARCHAR},
			   SYSDATE,
			   #{address, jdbcType=VARCHAR},
			   SYSDATE,
			   CURRENT_TIMESTAMP,
			   #{policy_code, jdbcType=VARCHAR},
			   #{update_by, jdbcType=NUMERIC},
			   #{post_code, jdbcType=VARCHAR},
			   #{mobile_tel, jdbcType=VARCHAR},
			   #{email, jdbcType=VARCHAR},
			   CURRENT_TIMESTAMP,
			   #{city, jdbcType=VARCHAR},
			   #{insert_by, jdbcType=NUMERIC}) 
	</insert>
	<!-- 快速查询预填单是否已预填 -->
	<select id="css_queryPolicyPreFilled" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT count(1) FROM APP___PAS__DBUSER.T_CS_PRE_FILLED CP 
		WHERE 1=1 
		]]>
		<if test=" work_code != null and work_code != '' "><![CDATA[ AND CP.WORK_CODE = #{work_code} ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND CP.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" service_code != null and service_code != '' "><![CDATA[ AND CP.BUSINESS_CODE = #{service_code} ]]></if>
	</select>
	<!-- 查询保单地址基本信息 -->
	<select id="css_queryPolicyAddressInfo" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT A.POLICY_CODE, /*保单号*/
			   A.ADDRESS_ID, /*地址编号*/
		       AD.STATE, /*省*/
		       AD.CITY, /*市*/
		       AD.DISTRICT, /*区县*/
		       AD.ADDRESS, /*地址*/
		       AD.POST_CODE, /*邮编*/
		       AD.COUNTRY_CODE /*国籍编码*/
	  	FROM (SELECT PH.POLICY_CODE,
	               PH.ADDRESS_ID　FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH 
	               WHERE PH.POLICY_CODE = #{policy_code} /*保单号*/ AND PH.CUSTOMER_ID = #{customer_id} /*客户ID*/ 	               
	        ) A
	 	INNER JOIN (SELECT *
	               FROM APP___PAS__DBUSER.T_ADDRESS AD
	              WHERE AD.ADDRESS_STATUS = '1') AD
	    ON A.ADDRESS_ID = AD.ADDRESS_ID
	</select>
	<!-- 按照客户号、保单号、保全受理项目、排队号查询预填数据 -->
	<select id="queryPreFillInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT PF.PRE_FILLED_ID,/*主键*/
				  PF.CUSTOMER_ID,/*客户号*/
				  PF.CUSTOMER_NAME,/*客户姓名*/
				  PF.CUSTOMER_BIRTHDAY,/*客户出生日期*/
				  PF.CUSTOMER_GENDER,/*客户性别，关联到性别字典表*/
				  PF.CUSTOMER_CERT_TYPE,/*客户证件类型 关联到证件类型表*/
				  PF.CUSTOMER_CERTI_CODE,/*客户证件号码*/
				  PF.POLICY_CODE,/*保单号*/
				  PF.ACCEPT_TYPE,/*申请方式1客户上门办理2其他人代办*/
				  PF.AGENT_NAME,/*代办人姓名*/
				  PF.AGENT_CERT_TYPE,/*代办人证件类型*/
				  PF.AGENT_CERT_CODE,/*代办人证件号码*/
				  PF.AGENT_TEL/*代理人电话*/
		  FROM APP___PAS__DBUSER.T_CS_PRE_FILLED PF
		  LEFT JOIN APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER PC ON　PF.PRE_FILLED_ID = PC.PRE_FILLED_ID
		  WHERE 1=1
		 ]]>
		   <if test=" work_code != null and work_code != '' "><![CDATA[ AND PF.WORK_CODE = #{work_code} ]]></if>
		   <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND PF.POLICY_CODE= #{policy_code} ]]></if>
   		   <if test=" service_code != null and service_code != ''"><![CDATA[AND PF.BUSINESS_CODE = #{service_code}]]></if>
   		   <if test=" customer_id != null and customer_id != ''"><![CDATA[AND PF.CUSTOMER_ID= #{customer_id}]]></if>
	</select>

	<select id="queryCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT 
		      C.CUSTOMER_ID,--客户ID
		      C.CUSTOMER_NAME,--客户姓名
		      C.CUSTOMER_BIRTHDAY,--客户生日
		      C.CUSTOMER_GENDER,--客户性别
		      C.CUSTOMER_CERT_TYPE,--证件类型
		      C.CUSTOMER_CERTI_CODE--证件号
		FROM DEV_PAS.T_CUSTOMER C WHERE C.CUSTOMER_ID = #{customer_id}
	</select>
<!-- 添加操作 -->
	<insert id="addCsPreFilled"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="pre_filled_id">
            SELECT APP___PAS__DBUSER.S_CS_PRE_FILLED.NEXTVAL FROM DUAL
        </selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PRE_FILLED(
				BUSINESS_CODE, CUSTOMER_CERT_TYPE, ACCEPT_TYPE, CUSTOMER_NAME, PRE_FILLED_ID, INSERT_TIME, AGENT_NAME, 
				CUSTOMER_ID, UPDATE_TIME, PRE_FILLED_TIME, CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, 
				AGENT_CERT_CODE, AGENT_CERT_TYPE, BRANCH_CODE, CUSTOMER_BIRTHDAY, UPDATE_TIMESTAMP, INSERT_BY, WORK_CODE, 
				CUSTOMER_GENDER ) 
			VALUES (
				#{business_code, jdbcType=VARCHAR}, #{customer_cert_type, jdbcType=VARCHAR} , #{accept_type, jdbcType=VARCHAR} , #{customer_name, jdbcType=VARCHAR} , #{pre_filled_id, jdbcType=NUMERIC} , SYSDATE , #{agent_name, jdbcType=VARCHAR} 
				, #{customer_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{agent_cert_code, jdbcType=NUMERIC} , #{agent_cert_type, jdbcType=VARCHAR} , #{branch_code, jdbcType=VARCHAR} , #{customer_birthday, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{work_code, jdbcType=VARCHAR} 
				, #{customer_gender, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
<!-- 	添加预填单客户资料变更 -->
	<insert id="addCsPreFilledCustomer"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER(
				ADDRESS_ID, STATE, DISTRICT, PRE_FILLED_ID, HOUSE_TEL, INSERT_TIME, ADDRESS, 
				COUNTRY_CODE, UPDATE_TIME, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, POST_CODE, MOBILE_TEL, 
				EMAIL, UPDATE_TIMESTAMP, CITY, INSERT_BY ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{state, jdbcType=VARCHAR} , #{district, jdbcType=VARCHAR} , #{pre_filled_id, jdbcType=NUMERIC} , #{house_tel, jdbcType=VARCHAR} , SYSDATE , #{address, jdbcType=VARCHAR} 
				, #{country_code, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{post_code, jdbcType=VARCHAR} , #{mobile_tel, jdbcType=VARCHAR} 
				, #{email, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{city, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
	<!-- 添加预填单缴费信息录入 -->
	<insert id="addCsPreFilledPayerAccount"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PRE_FILLED_PAYER_ACCOUNT(
				INSERT_TIMESTAMP, POLICY_CODE, PAY_MODE, PRE_FILLED_ID, UPDATE_BY, INSERT_TIME, ACCOUNT_NAME, 
				ACCOUNT_BANK, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, ACCOUNT ) 
			VALUES (
				CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR} , #{pre_filled_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{account_name, jdbcType=VARCHAR} 
				, #{account_bank, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{account, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>
	
	<!-- 添加受益人变更录入 -->
	<insert id="addCsPreFilledContractBene"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="pre_filled_bene_id">
            SELECT APP___PAS__DBUSER.S_CS_PRE_FILLED_CONTRACT_BENE.NEXTVAL FROM DUAL
        </selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE(
				CUSTOMER_CERT_TYPE, CUSTOMER_NAME, PRE_FILLED_ID, SHARE_ORDER, INSERT_TIME, BENE_TYPE, CUSTOMER_ID, 
				UPDATE_TIME, INSURED_ID, CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, PRE_FILLED_BENE_ID, POLICY_CODE, UPDATE_BY, 
				DESIGNATION, LIST_ID, CUSTOMER_BIRTHDAY, UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, CUSTOMER_GENDER, 
				SHARE_RATE ) 
			VALUES (
				#{customer_cert_type, jdbcType=VARCHAR}, #{customer_name, jdbcType=VARCHAR} , #{pre_filled_id, jdbcType=NUMERIC} , #{share_order, jdbcType=NUMERIC} , SYSDATE , #{bene_type, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} 
				, SYSDATE , #{insured_id, jdbcType=NUMERIC} , #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{pre_filled_bene_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{designation, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{customer_birthday, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC} 
				, #{share_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 按索引查询操作 -->	
	<select id="findCsPreFilledByPreFilledId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.CUSTOMER_CERT_TYPE, A.ACCEPT_TYPE, A.CUSTOMER_NAME, A.PRE_FILLED_ID, A.AGENT_NAME, 
			A.CUSTOMER_ID, A.PRE_FILLED_TIME, A.CUSTOMER_CERTI_CODE, A.POLICY_CODE, 
			A.AGENT_CERT_CODE, A.AGENT_CERT_TYPE, A.BRANCH_CODE, A.CUSTOMER_BIRTHDAY, A.WORK_CODE, 
			A.CUSTOMER_GENDER FROM APP___PAS__DBUSER.T_CS_PRE_FILLED A WHERE 1 = 1  ]]>
		<include refid="queryCsPreFilledByPreFilledIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsPreFilled" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.CUSTOMER_CERT_TYPE, A.ACCEPT_TYPE, A.CUSTOMER_NAME, A.PRE_FILLED_ID, A.AGENT_NAME, 
			A.CUSTOMER_ID, A.PRE_FILLED_TIME, A.CUSTOMER_CERTI_CODE, A.POLICY_CODE, 
			A.AGENT_CERT_CODE, A.AGENT_CERT_TYPE, A.BRANCH_CODE, A.CUSTOMER_BIRTHDAY, A.WORK_CODE, 
			A.CUSTOMER_GENDER FROM T_CS_PRE_FILLED A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsPreFilled" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.CUSTOMER_CERT_TYPE, A.ACCEPT_TYPE, A.CUSTOMER_NAME, A.PRE_FILLED_ID, A.AGENT_NAME, 
			A.CUSTOMER_ID, A.PRE_FILLED_TIME, A.CUSTOMER_CERTI_CODE, A.POLICY_CODE, 
			A.AGENT_CERT_CODE, A.AGENT_CERT_TYPE, A.BRANCH_CODE, A.CUSTOMER_BIRTHDAY, A.WORK_CODE, 
			A.CUSTOMER_GENDER FROM T_CS_PRE_FILLED A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="findCsPreFilledTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CS_PRE_FILLED A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsPreFilledForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSINESS_CODE, B.CUSTOMER_CERT_TYPE, B.ACCEPT_TYPE, B.CUSTOMER_NAME, B.PRE_FILLED_ID, B.AGENT_NAME, 
			B.CUSTOMER_ID, B.PRE_FILLED_TIME, B.CUSTOMER_CERTI_CODE, B.POLICY_CODE, 
			B.AGENT_CERT_CODE, B.AGENT_CERT_TYPE, B.BRANCH_CODE, B.CUSTOMER_BIRTHDAY, B.WORK_CODE, 
			B.CUSTOMER_GENDER FROM (
					SELECT ROWNUM RN, A.BUSINESS_CODE, A.CUSTOMER_CERT_TYPE, A.ACCEPT_TYPE, A.CUSTOMER_NAME, A.PRE_FILLED_ID, A.AGENT_NAME, 
			A.CUSTOMER_ID, A.PRE_FILLED_TIME, A.CUSTOMER_CERTI_CODE, A.POLICY_CODE, 
			A.AGENT_CERT_CODE, A.AGENT_CERT_TYPE, A.BRANCH_CODE, A.CUSTOMER_BIRTHDAY, A.WORK_CODE, 
			A.CUSTOMER_GENDER FROM T_CS_PRE_FILLED A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 投保人 -->
	<select id="queryCsPreFillHolder" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT PH.POLICY_CODE,
	       PH.POLICY_ID,
       CM.VALIDATE_DATE,CM.LIABILITY_STATE,CM.END_CAUSE,
       CB.BUSI_ITEM_ID,
       BP.PRODUCT_NAME_SYS BUSI_ITEM_NAME,
       TO_CHAR(NPA.ACKNOWLEDGE_DATE,'yyyy-MM-dd') ACKNOWLEDGE_DATE,
       DECODE(C.WORK_CODE,'','待预填','已预填') AS PRE_FILLED_STATUS
  FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH
 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
    ON CM.POLICY_CODE = PH.POLICY_CODE
 INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT NPA
    ON CM.POLICY_ID = NPA.POLICY_ID
 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CB
    ON CM.POLICY_CODE = CB.POLICY_CODE
 INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
    ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
 LEFT JOIN (SELECT CP.WORK_CODE,CP.POLICY_CODE FROM APP___PAS__DBUSER.T_CS_PRE_FILLED CP 
                  WHERE CP.WORK_CODE = #{work_code} AND CP.BUSINESS_CODE = #{service_code}) C
   ON C.POLICY_CODE = CM.POLICY_CODE 
 WHERE PH.CUSTOMER_ID = ${customer_id}
	]]>
	</select>
	<!-- 被保人  -->
	<select id="queryCsPreFillInsure" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT IL.POLICY_CODE,
	       IL.POLICY_ID,
       CM.VALIDATE_DATE,CM.LIABILITY_STATE,CM.END_CAUSE,
       CB.BUSI_ITEM_ID,
       BP.PRODUCT_NAME_SYS BUSI_ITEM_NAME,
       TO_CHAR(NPA.ACKNOWLEDGE_DATE,'yyyy-MM-dd') ACKNOWLEDGE_DATE,
       DECODE(C.WORK_CODE,'','待预填','已预填') AS PRE_FILLED_STATUS
  FROM APP___PAS__DBUSER.T_INSURED_LIST IL
 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
    ON CM.POLICY_CODE = IL.POLICY_CODE
 INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT NPA
    ON CM.POLICY_ID = NPA.POLICY_ID
 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CB
    ON CM.POLICY_CODE = CB.POLICY_CODE
 INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
    ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
 LEFT JOIN (SELECT CP.WORK_CODE,CP.POLICY_CODE FROM APP___PAS__DBUSER.T_CS_PRE_FILLED CP 
                  WHERE CP.WORK_CODE = #{work_code} AND CP.BUSINESS_CODE = #{service_code}) C
   ON C.POLICY_CODE = CM.POLICY_CODE 
 WHERE IL.CUSTOMER_ID = ${customer_id}
	]]>
	</select>
	<!-- 根据客户权限查询保单列表 -->
	<select id="css_queryCsPreFillPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<!-- 投保人 -->
		<![CDATA[ SELECT * FROM ( ]]>
		<if test="customer_role == 1 or customer_role == 3 ">
		<![CDATA[
		  SELECT PH.POLICY_CODE,
		       PH.POLICY_ID,
		       CM.VALIDATE_DATE,CM.LIABILITY_STATE,CM.END_CAUSE,
		       CB.BUSI_ITEM_ID,
		       BP.PRODUCT_NAME_SYS BUSI_ITEM_NAME,
		       TO_CHAR(NPA.ACKNOWLEDGE_DATE,'yyyy-MM-dd') ACKNOWLEDGE_DATE,
		       DECODE(C.WORK_CODE,'','待预填','已预填') AS PRE_FILLED_STATUS
		  FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH
		 	INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		    	ON CM.POLICY_CODE = PH.POLICY_CODE
		 	INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT NPA
		    	ON CM.POLICY_ID = NPA.POLICY_ID
			INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CB
		    	ON CM.POLICY_CODE = CB.POLICY_CODE
			INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
		    	ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
			LEFT JOIN (SELECT CP.WORK_CODE,CP.POLICY_CODE FROM APP___PAS__DBUSER.T_CS_PRE_FILLED CP 
		                  WHERE CP.WORK_CODE = #{work_code} AND CP.BUSINESS_CODE = #{service_code}) C
		   		ON C.POLICY_CODE = CM.POLICY_CODE 
		   WHERE PH.CUSTOMER_ID = ${customer_id}
		]]>
		</if>
		<if test="customer_role != null and customer_role == 3 ">
			<![CDATA[
				union
			]]>
		</if>
		<if test="customer_role == 2 or customer_role == 3 ">
			<!-- 被保人 -->
			<![CDATA[
			  SELECT IL.POLICY_CODE,
				       IL.POLICY_ID,
				       CM.VALIDATE_DATE,CM.LIABILITY_STATE,CM.END_CAUSE,
				       CB.BUSI_ITEM_ID,
				       BP.PRODUCT_NAME_SYS BUSI_ITEM_NAME,
				       TO_CHAR(NPA.ACKNOWLEDGE_DATE,'yyyy-MM-dd') ACKNOWLEDGE_DATE,
				       DECODE(C.WORK_CODE,'','待预填','已预填') AS PRE_FILLED_STATUS
			  FROM APP___PAS__DBUSER.T_INSURED_LIST IL
			 	INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
			    	ON CM.POLICY_CODE = IL.POLICY_CODE
			 	INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT NPA
			    	ON CM.POLICY_ID = NPA.POLICY_ID
			 	INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CB
		    		ON CM.POLICY_CODE = CB.POLICY_CODE
				INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
			   		ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
				LEFT JOIN (SELECT CP.WORK_CODE,CP.POLICY_CODE FROM APP___PAS__DBUSER.T_CS_PRE_FILLED CP 
			                  WHERE CP.WORK_CODE = #{work_code} AND CP.BUSINESS_CODE = #{service_code}) C
			   		ON C.POLICY_CODE = CM.POLICY_CODE 
			   WHERE IL.CUSTOMER_ID = ${customer_id}
			]]>
			<![CDATA[ ) ]]>
		</if>
	</select>
	<select id="queryCsPreFillCCInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT C.EMAIL,
       C.MOBILE_TEL,
       C.HOUSE_TEL,
       PH.POLICY_CODE,
       A.ADDRESS_ID,
       A.ADDRESS,
       A.COUNTRY_CODE,
       A.STATE,
       A.CITY,
       A.DISTRICT,
       A.ADDRESS,
       A.POST_CODE
  FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH
 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
    ON C.CUSTOMER_ID = PH.CUSTOMER_ID
 INNER JOIN APP___PAS__DBUSER.T_ADDRESS A
    ON A.ADDRESS_ID = PH.ADDRESS_ID
 WHERE PH.POLICY_CODE = '${policy_code}'
	]]>
	</select>
	
	<select id="queryCsPreFillPCInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
 SELECT CM.POLICY_CODE,
        PA.PAY_MODE,
        PA.ACCOUNT,
        PA.ACCOUNT_BANK,
        C.CUSTOMER_NAME,
        PA.ACCOUNT_NAME
   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER CM
  INNER JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT PA
     ON PA.POLICY_ID = CM.POLICY_ID
  INNER JOIN APP___PAS__DBUSER.T_PAYER P
     ON P.POLICY_ID = CM.POLICY_ID
  INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER PH
    ON PH.POLICY_ID = CM.POLICY_ID
 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
    ON C.CUSTOMER_ID = PH.CUSTOMER_ID
 WHERE CM.POLICY_CODE = '${policy_code}'
	]]>
	</select>
	
	<select id="queryCsPreFillBCInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
SELECT CBP.POLICY_CODE,
       CBP.BUSI_ITEM_ID,
       BI.INSURED_ID,
       IC.CUSTOMER_NAME AS INSURED_NAME,
       CB.LIST_ID,
       CB.CUSTOMER_ID,
       C.CUSTOMER_NAME,
       C.CUSTOMER_CERT_TYPE,
       C.CUSTOMER_CERTI_CODE,
       C.CUSTOMER_BIRTHDAY,
       C.CUSTOMER_GENDER,
       CB.DESIGNATION,
       CB.SHARE_ORDER,
       CB.SHARE_RATE,
       CB.BENE_TYPE
  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
 INNER JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED BI
    ON BI.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BENE CB
    ON CB.INSURED_ID = BI.INSURED_ID
 INNER JOIN (SELECT IL.LIST_ID, C.CUSTOMER_NAME
               FROM APP___PAS__DBUSER.T_INSURED_LIST IL
              INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
                 ON IL.CUSTOMER_ID = C.CUSTOMER_ID) IC
    ON IC.LIST_ID = BI.INSURED_ID
 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
    ON C.CUSTOMER_ID = CB.CUSTOMER_ID
 WHERE CBP.POLICY_CODE = #{policy_code}
 ORDER BY CBP.BUSI_ITEM_ID, BI.INSURED_ID, CB.CUSTOMER_ID
	]]>
	</select>
	
	<!-- 续期保费 -->
	<select id="prefillrenewalPrem_info" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			SELECT TPA.POLICY_CODE
			             ,(CASE WHEN MIN(TPA.FEE_STATUS) ='00' THEN '3'
			                    WHEN MIN(TPA.FEE_STATUS) = '01' THEN '3'
			                      WHEN MIN(TPA.FEE_STATUS) = '19' THEN '2'
			                     ELSE '1' END) AS RENEWAL_PREM
			      FROM APP___PAS__DBUSER.T_PREM_ARAP TPA ,APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
			      WHERE TPA.POLICY_CODE = TCM.POLICY_CODE   AND TPA.FEE_TYPE IN ('G003030100', 'G003010000')
			      AND TPA.FEE_STATUS IN ('00','01','19')
			       AND TCM.POLICY_ID = #{policy_id}
			        AND TPA.DUE_TIME >= TO_DATE(TO_CHAR((sysdate), 'yyyy/MM/dd'), 'yyyy/MM/dd')
			       GROUP BY TPA.POLICY_CODE
			       HAVING SUM(TPA.FEE_AMOUNT)  > 0
	]]>
	</select>
	
	<select id="prefillQueryCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT C.CUSTOMER_NAME,C.CUSTOMER_BIRTHDAY,C.CUSTOMER_GENDER,C.CUSTOMER_CERT_TYPE,C.CUSTOMER_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER C WHERE C.CUSTOMER_ID=${customer_id}
	]]>
	</select>
	
	<select id="prefillBusinessItem" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT CBP.BUSI_ITEM_ID,CBP.BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP  WHERE CBP.POLICY_CODE = #{policy_code}
	]]>
	</select>
	
	<select id="prefillInsuredName" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	 SELECT IL.LIST_ID AS INSURED_ID, C.CUSTOMER_NAME,BI.BUSI_ITEM_ID
   FROM APP___PAS__DBUSER.T_INSURED_LIST IL
  INNER JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED BI
     ON BI.INSURED_ID = IL.LIST_ID
  INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
     ON IL.CUSTOMER_ID = C.CUSTOMER_ID
  WHERE BI.PRODUCT_CODE = #{product_code} AND BI.POLICY_CODE = #{policy_code}
	]]>
	</select>
	
	<!-- 删除操作 -->	
	<delete id="deleteCsPreFilledContractBene" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE WHERE 1=1 ]]>
		<if test="pre_filled_bene_id != null and pre_filled_bene_id != '' "><![CDATA[ AND PRE_FILLED_BENE_ID = #{pre_filled_bene_id} ]]></if>
		<if test="pre_filled_id != null and pre_filled_id != '' "><![CDATA[ AND PRE_FILLED_ID = #{pre_filled_id} ]]></if>
	</delete>
	
	<!-- 查询个数操作 -->
	<select id="findCsPreFilledPayerAccountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_PAYER_ACCOUNT A WHERE 1 = 1 AND A.PRE_FILLED_ID = #{pre_filled_id}  ]]>
	</select>
	<!-- 根据预填单主键查询受益人变更信息列表 -->
	<select id="css_findContractBeneList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT CB.PRE_FILLED_BENE_ID,CB.PRE_FILLED_ID,CB.POLICY_CODE,CB.LIST_ID,CB.BUSI_ITEM_ID,
			       CB.INSURED_ID,CB.CUSTOMER_NAME,CB.CUSTOMER_BIRTHDAY,CB.CUSTOMER_GENDER,
			       CB.CUSTOMER_CERT_TYPE,CB.CUSTOMER_CERTI_CODE,CB.SHARE_ORDER,CB.CUSTOMER_ID,
			       CB.DESIGNATION,CB.SHARE_RATE,CB.BENE_TYPE,CB.CUST_CERT_STAR_DATE,
			       CB.CUST_CERT_END_DATE,CB.COUNTRY_CODE,CB.JOB_CODE,CB.MOBILE_TEL,CB.FIX_TEL,
			       CB.STATE,CB.CITY,CB.DISTRICT,CB.ADDRESS,CB.POST_CODE,CB.BANK_CODE,CB.BANK_ACCOUNT,
			       CB.ACCO_NAME,CB.SURVIVAL_W_MODE 
			FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE CB--受益人信息
			WHERE CB.PRE_FILLED_ID = #{pre_filled_id}
		 ]]>
	</select>
	
	<select id="css_findPreFilledInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT PF.PRE_FILLED_ID,PF.WORK_CODE,PF.POLICY_CODE FROM APP___PAS__DBUSER.T_CS_PRE_FILLED PF 
				WHERE PF.WORK_CODE = #{work_code } AND PF.POLICY_CODE = #{policy_code } 
					  AND PF.BUSINESS_CODE = #{service_code } AND PF.CUSTOMER_ID = #{customer_id }
		 ]]>
	</select>
	
	<!-- 根据五要素查询受益人信息 -->
	<select id="css_findBeneInfoByCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT CB.PRE_FILLED_BENE_ID,/*主键ID*/
				   CB.PRE_FILLED_ID,/*预填单主键*/
				   CB.POLICY_CODE /*保单号*/
			FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE CB--受益人信息
       		WHERE 1 = 1 
		 ]]>
		 <if test="customer_name != null and customer_name != '' "><![CDATA[ AND CB.CUSTOMER_NAME = #{customer_name }]]></if>
		 <if test="customer_birthday_str != null and customer_birthday_str != '' "><![CDATA[ AND to_char(CB.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') = #{customer_birthday_str }]]></if>
		 <if test="customer_gender != null and customer_gender != '' "><![CDATA[ AND CB.CUSTOMER_GENDER = #{customer_gender }]]></if>
		 <if test="customer_cert_type != null and customer_cert_type != '' "><![CDATA[ AND CB.CUSTOMER_CERT_TYPE = #{customer_cert_type }]]></if>
		 <if test="customer_certi_code != null and customer_certi_code != '' "><![CDATA[ AND CB.CUSTOMER_CERTI_CODE = #{customer_certi_code }]]></if>
	</select>
	
	<!-- 修改操作 -->
	<update id="updateCsPreFilledPayerAccount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PRE_FILLED_PAYER_ACCOUNT ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    PRE_FILLED_ID = #{pre_filled_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			ACCOUNT_NAME = #{account_name, jdbcType=VARCHAR} ,
			ACCOUNT_BANK = #{account_bank, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT = #{account, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE PRE_FILLED_ID = #{pre_filled_id} ]]>
	</update>
	
	<!-- 查询个数操作 -->
	<select id="findCsPreFilledCustomerTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER A WHERE 1 = 1  AND A.PRE_FILLED_ID = #{pre_filled_id}]]>
	</select>
	
	<!-- 修改操作 -->
	<update id="updateCsPreFilledCustomer" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
			STATE = #{state, jdbcType=VARCHAR} ,
			DISTRICT = #{district, jdbcType=VARCHAR} ,
		    PRE_FILLED_ID = #{pre_filled_id, jdbcType=NUMERIC} ,
			HOUSE_TEL = #{house_tel, jdbcType=VARCHAR} ,
			ADDRESS = #{address, jdbcType=VARCHAR} ,
			COUNTRY_CODE = #{country_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			POST_CODE = #{post_code, jdbcType=VARCHAR} ,
			MOBILE_TEL = #{mobile_tel, jdbcType=VARCHAR} ,
			EMAIL = #{email, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CITY = #{city, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE PRE_FILLED_ID = #{pre_filled_id} ]]>
	</update>
	
	<!-- 修改操作 -->
	<update id="updateCsPreFilled" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PRE_FILLED ]]>
		<set>
		<trim suffixOverrides=",">
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			ACCEPT_TYPE = #{accept_type, jdbcType=VARCHAR} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    PRE_FILLED_ID = #{pre_filled_id, jdbcType=NUMERIC} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PRE_FILLED_TIME = #{pre_filled_time, jdbcType=DATE} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    AGENT_CERT_CODE = #{agent_cert_code, jdbcType=NUMERIC} ,
			AGENT_CERT_TYPE = #{agent_cert_type, jdbcType=VARCHAR} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
		    CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			WORK_CODE = #{work_code, jdbcType=VARCHAR} ,
		    CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE PRE_FILLED_ID = #{pre_filled_id} ]]>
	</update>
</mapper>
