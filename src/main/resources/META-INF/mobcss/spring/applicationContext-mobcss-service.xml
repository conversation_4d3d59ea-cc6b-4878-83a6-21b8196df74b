<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springfraleihongmework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	<!--预填单 -->
  <bean
    class="com.nci.tunan.mobcss.impl.prefillBill.service.impl.MobPrefillBillServiceImpl"
    id="PA_prefillBillService">
    <property name="prefillBillPasDao" ref="PA_prefillBillPasDao" />
    <property name="csAcceptanceDao" ref="PA_csAcceptanceDao" />
    <property name="loanPolicyCfgDao" ref="PA_loanPolicyCfgDao" />
    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao" />
    <property name="clmService" ref="PA_clmService" />
    <property name="csEndorseTAService" ref="PA_csEndorseTAService" />
    <property name="csBonusDistributeDao" ref="PA_csBonusDistributeDao" />
    <property name="csPolicyStatusService" ref="PA_csPolicyStatusService" />
    <property name="csPolicyStateDao" ref="PA_csPolicyStateDao" />
    <property name="csBusinessLockService" ref="PA_csBusinessLockService" />
    <property name="cSCalculateCashValueService" ref="PA_cSCalculateCashValueService" />
    <property name="pQueryService" ref="PA_prdService" />
  </bean>
  <bean class="com.nci.tunan.mobcss.impl.prefillBill.service.impl.MobPrefillBillCCEntryServiceImpl" 
        id="PA_prefillBillCCEntryService">
    <property name="prefillBillPasDao" ref="PA_prefillBillPasDao" />
  	<property name="prefillBillCCEntryDao" ref="PA_prefillBillCCEntryDao"/>
  	<property name="mobPrefillBillService" ref="PA_prefillBillService"/>
  </bean>
  <bean class="com.nci.tunan.mobcss.impl.prefillBill.service.impl.MobPrefillBillBCEntryServiceImpl" 
        id="PA_prefillBillBCEntryService">
    <property name="prefillBillPasDao" ref="PA_prefillBillPasDao" />
  	<property name="prefillBillBCEntryDao" ref="PA_prefillBillBCEntryDao"/>
  	<property name="mobPrefillBillService" ref="PA_prefillBillService"/>
  </bean>
  <bean class="com.nci.tunan.mobcss.impl.prefillBill.service.impl.MobPrefillBillPCEntryServiceImpl" 
        id="PA_prefillBillPCEntryService">
    <property name="prefillBillPasDao" ref="PA_prefillBillPasDao" />
  	<property name="prefillBillPCEntryDao" ref="PA_prefillBillPCEntryDao"/>
  	<property name="mobPrefillBillService" ref="PA_prefillBillService"/>
  </bean>
</beans>