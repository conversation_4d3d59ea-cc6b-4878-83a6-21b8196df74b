<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springfraleihongmework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	<!-- 	预填单 -->
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.dao.impl.MobPrefillBillPasDaoImpl" id="PA_prefillBillPasDao" parent="baseDao"/>
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.dao.impl.MobPrefillBillCCEntryDaoImpl" id="PA_prefillBillCCEntryDao" parent="baseDao"/>
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.dao.impl.MobPrefillBillBCEntryDaoImpl" id="PA_prefillBillBCEntryDao" parent="baseDao"/>
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.dao.impl.MobPrefillBillPCEntryDaoImpl" id="PA_prefillBillPCEntryDao" parent="baseDao"/>
</beans>