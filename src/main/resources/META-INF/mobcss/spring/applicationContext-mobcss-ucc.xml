<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springfraleihongmework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.ucc.impl.MobPrefillBillUCCImpl" id="PA_prefillBillPasUCC">
		<property name="prefillBillService" ref="PA_prefillBillService"/>
		<property name="csPolicyStateDao" ref="PA_csPolicyStateDao"/>
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="lockService" ref="PA_csBusinessLockService"/>
		<property name="prdProductQueryService" ref="PA_prdService"/>
		<property name="cSCalculateCashValueService" ref="PA_cSCalculateCashValueService"/>
		<property name="cusApplicationService" ref="PA_cusApplicationService"/>
		<property name="csEndorseTRService" ref="PA_csEndorseTRService"/>
		<property name="csCodeTableService" ref="PA_csCodeTableService"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="cusAcceptService" ref="PA_cusAcceptService"/>
	</bean>
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.ucc.impl.MobPrefillBillCCEntryUCCImpl" id="PA_prefillBillCCEntryUCC">
		<property name="prefillBillCCEntryService" ref="PA_prefillBillCCEntryService"></property>
		<property name="mobPrefillBillService" ref="PA_prefillBillService"></property>
		<property name="cusApplicationUcc" ref="PA_cusApplicationUCC"></property>
		<property name="csEndorseForOutterService" ref="PA_csEndorseForOutterService"></property>
		<property name="csEndorseCCService" ref="PA_custBaseInfoChgService"></property>
		<property name="csPolicyChangeService" ref="PA_csPolicyChangeService"></property>
		<property name="csEffectUcc" ref="PA_csEffectUCC"></property>
	</bean>
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.ucc.impl.MobPrefillBillBCEntryUCCImpl" 
		  id="PA_prefillBillBCEntryUCC">
		<property name="prefillBillBCEntryService" ref="PA_prefillBillBCEntryService"></property>
		<property name="prefillBillCCEntryService" ref="PA_prefillBillCCEntryService"></property>
		<property name="mobPrefillBillService" ref="PA_prefillBillService"></property>
		<property name="csEndorseForOutterService" ref="PA_csEndorseForOutterService"></property>
		<property name="csEndorseBCUCC" ref="PA_csEndorseBCUCC"></property>
		<property name="customerDao" ref="PA_customerDao"></property>
		<property name="csInsuredListDao" ref="PA_csInsuredListDao"></property>
	</bean>
	<bean class="com.nci.tunan.mobcss.impl.prefillBill.ucc.impl.MobPrefillBillPCEntryUCCImpl" 
		  id="PA_prefillBillPCEntryUCC">
		<property name="prefillBillPCEntryService" ref="PA_prefillBillPCEntryService"></property>
		<property name="prefillBillCCEntryService" ref="PA_prefillBillCCEntryService"></property>
		<property name="mobPrefillBillService" ref="PA_prefillBillService"></property>
		<property name="cusApplicationUcc" ref="PA_cusApplicationUCC"></property>
		<property name="csEndorseForOutterService" ref="PA_csEndorseForOutterService"></property>
		<property name="csEndorsePCService" ref="PA_paymentInfoChgService"></property>
		<property name="csPolicyChangeService" ref="PA_csPolicyChangeService"></property>
		<property name="csEffectUcc" ref="PA_csEffectUCC"></property>
	</bean>
</beans>