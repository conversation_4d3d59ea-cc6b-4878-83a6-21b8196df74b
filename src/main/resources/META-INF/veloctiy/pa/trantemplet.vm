<soapenv:Envelope
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:web="http://webservice.controller.fis.sinosoft.com">
    <soapenv:Header/>
    <soapenv:Body>
        <web:${convertParam.getMethodName()} soapenv:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
            <input xsi:type="soapenc:string"
                xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">
                <![CDATA[
						<soapenv:Envelope
                            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                            xmlns:heal=${convertParam.getHealSpace()}>
                            <soapenv:Header>
                                <heal:header>
                                    <heal:userName>${convertParam.getUserName()}</heal:userName>
                                    <heal:password>${convertParam.getPassword()}</heal:password>
                                    <heal:transType>${convertParam.getTransType()}</heal:transType>
                                    <heal:transNo>${convertParam.getTransNo()}</heal:transNo>
                                    <heal:transDate>${convertParam.getTransDate()}</heal:transDate>
                                    <heal:areaCode>${convertParam.getAreaCode()}</heal:areaCode>
                                    <heal:recordNum>${convertParam.getRecordNum()}</heal:recordNum>
                                    <heal:signature>${convertParam.getSignature()}</heal:signature>
                                    <heal:version>${convertParam.getVersion()}</heal:version>
                                </heal:header>
                            </soapenv:Header>
                            <soapenv:Body>
                                <heal:request>
                                    <heal:jsonString>${convertParam.getJsonStr()}</heal:jsonString>
                                </heal:request>
                            </soapenv:Body>
                        </soapenv:Envelope>	
                ]]>
            </input>
        </web:${convertParam.getMethodName()}>
    </soapenv:Body>
</soapenv:Envelope>