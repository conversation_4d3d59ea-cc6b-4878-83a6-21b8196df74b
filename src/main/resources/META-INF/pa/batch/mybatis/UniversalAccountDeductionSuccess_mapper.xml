<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.universalAccountDeductionSuccess.dao.IUniversalAccountDeductionSuccessDao">
	<select id="PA_universalAccountDeductionSuccessQueryData" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		SELECT T.POLICY_ID, T.POLICY_CODE, T.RELATION_POLICY_CODE ,ROWNUM RN FROM (
			select distinct TCBP.POLICY_ID,TCBP.POLICY_CODE,TCM.RELATION_POLICY_CODE
			from APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP,
	     	APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
	     	APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
	     	APP___PAS__DBUSER.T_PREM_ARAP          TPA
			where TBP.BUSINESS_PRD_ID=TCBP.BUSI_PRD_ID
			and TCM.POLICY_CODE = TCBP.POLICY_CODE
			and TCM.RELATION_POLICY_CODE is not null  /*保单为关联保单*/
			and TCM.POLICY_CODE = TPA.POLICY_CODE
			AND TPA.BUSINESS_TYPE IN ('4003', '1005')/*业务类型代码*/
			AND TPA.FEE_STATUS IN ('01', '19') /*收付状态*/ 
			AND TPA.ARAP_FLAG=1 /*收费*/ 
			and TPA.PAY_MODE='53']]>
			<if test=" policy_code != null and policy_code !='' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code}]]></if>
			<if test=" start_date != null "><![CDATA[ AND TPA.FINISH_TIME >= #{start_date,jdbcType=DATE} ]]></if>
			<if test=" end_date != null "><![CDATA[ AND TPA.FINISH_TIME < #{end_date,jdbcType=DATE}]]></if>
			<![CDATA[) T WHERE 1=1]]>
	<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.POLICY_ID , #{modnum}) = #{start} ]]></if>
	</select>
	
	<!-- 查询保单基础信息 -->
	<select id="PA_findUniversalAccountDeductionSuccessBasicData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.* FROM (
		        SELECT (SELECT A.OLD_CODE FROM APP___PAS__DBUSER.T_CODE_MAPPER A  WHERE A.CODETYPE='CHANNEL_TYPE' 
                           AND A.FROM_MODLE='PAS' AND A.NEW_CODE = TBC.CHANNEL_TYPE) CHANNEL_TYPE,
			           TN.BUSI_PRD_ID,
			           TCM.POLICY_CODE,
			           TC.CUSTOMER_NAME,
			           TCM.LIABILITY_STATE,
			           SYSDATE VALIDATE_DATE,
			           TC.CUSTOMER_GENDER,
			           TAAC.MOBILE_TEL MOBILE_TEL_BC,
			           TC.MOBILE_TEL MOBILE_TEL_TC,
			           TCM.ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 6) ZHI_ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 6)) ZHI_ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 4) BRANCH_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) BRANCH_CODE_NAME,
			           TC.OLD_CUSTOMER_ID CUSTOMER_ID,
			           TC.WECHAT_NO,
			           TC.EMAIL,
			           (SELECT A.OLD_CODE FROM APP___PAS__DBUSER.T_CODE_MAPPER A  WHERE A.CODETYPE='CHANNEL_TYPE' AND A.FROM_MODLE='PAS' AND A.NEW_CODE = TAC.AGENT_CHANNEL) AGENT_CHANNEL,
			           TAC.AGENT_CODE,
			           TAC.AGENT_NAME,
			           TAC.AGENT_MOBILE,
			           TAC.AGENT_STATUS
			      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
			           APP___PAS__DBUSER.T_CUSTOMER           TC,
			           APP___PAS__DBUSER.T_ADDRESS            TAAC,
			           APP___PAS__DBUSER.T_POLICY_HOLDER      TIL,
			           APP___PAS__DBUSER.T_CONTRACT_AGENT     TBC,
			           APP___PAS__DBUSER.T_AGENT              TAC
			     WHERE TCM.POLICY_CODE = TN.POLICY_CODE
			       AND TCM.POLICY_CODE = TIL.POLICY_CODE
			       AND TCM.POLICY_ID = TBC.POLICY_ID
			       AND TBC.AGENT_CODE = TAC.AGENT_CODE
			       AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			       AND TIL.ADDRESS_ID = TAAC.ADDRESS_ID
			       AND TBC.IS_CURRENT_AGENT = '1' 
			       AND TN.MASTER_BUSI_ITEM_ID IS NULL   ]]>
   		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if> 
   		<![CDATA[ GROUP BY TBC.CHANNEL_TYPE, 
					  TC.CUSTOMER_NAME, 
					  TCM.POLICY_CODE,
					  TC.CUSTOMER_GENDER, 
					  TAAC.MOBILE_TEL, 
					  TC.MOBILE_TEL, 
					  TCM.ORGAN_CODE, 
					  TC.OLD_CUSTOMER_ID, 
					  TC.WECHAT_NO, 
					  TC.EMAIL, 
					  TN.BUSI_PRD_ID, 
					  TCM.LIABILITY_STATE, 
					  TAC.AGENT_CHANNEL, 
					  TAC.AGENT_CODE, 
					  TAC.AGENT_NAME, 
					  TAC.AGENT_MOBILE, 
					  TAC.AGENT_STATUS) A  ]]>
	</select>
	
	<select  id="PA_findBasicData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT TBP.PRODUCT_ABBR_NAME,TCBP.BUSI_PROD_CODE
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
               APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP,
               APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
         WHERE TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
           AND TCBP.POLICY_CODE = TCM.RELATION_POLICY_CODE
           AND TCM.POLICY_CODE = #{policy_code}  ]]>
	</select>

	<!-- 查询保单发送短信的个性字段-万能险保单手机号 -->
	<select  id="PA_findRelationPolicyMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT TAAC.MOBILE_TEL
				  FROM APP___PAS__DBUSER.T_ADDRESS       TAAC,
				       APP___PAS__DBUSER.T_POLICY_HOLDER TIL
				 WHERE 1 = 1
				   AND TIL.ADDRESS_ID = TAAC.ADDRESS_ID
				   AND TIL.POLICY_CODE = #{policy_code}  ]]>
	</select>
</mapper>