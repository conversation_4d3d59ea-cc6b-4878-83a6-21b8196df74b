<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.monthlyProductRenewalFeeReminder.dao.IMonthlyProductRenewalFeeReminderDao">

	<select id="PA_monthlyProductRenewalFeeReminderQueryData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT G.POLICY_CODE,G.POLICY_ID,ROWNUM RN FROM(
				SELECT DISTINCT A.POLICY_CODE, A.POLICY_ID
		        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    A,
		             APP___PAS__DBUSER.T_PREM_ARAP          B,
		             APP___PAS__DBUSER.T_CONTRACT_PRODUCT   C,
		             APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD D
		       WHERE A.POLICY_CODE = B.POLICY_CODE
		         AND A.POLICY_CODE = C.POLICY_CODE
		         AND B.PRODUCT_CODE = C.PRODUCT_CODE
		         AND C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
		         AND C.POLICY_CODE = D.POLICY_CODE
		         AND A.LIABILITY_STATE = 1 /*保单状态有效*/
		         AND D.LIABILITY_STATE = 1 /*险种状态：有效*/
		         AND C.PREM_FREQ = '2' /*月缴*/
		         AND B.ARAP_FLAG = 1 /*应收*/
		         AND B.FEE_AMOUNT > 0 /*金额大于0*/
		         AND B.BUSINESS_TYPE IN ('4003', '1005') /*续期*/
		         AND B.FEE_STATUS IN ('00','03')
		         AND EXISTS (SELECT 'X'
		                FROM DEV_PAS.T_BUSINESS_PRODUCT TBP
		               WHERE TBP.PRODUCT_CODE_SYS = D.BUSI_PROD_CODE
		                 AND TBP.COVER_PERIOD_TYPE = 0) /*主险为长期险，不包括M-一年期险；*/
		      		AND B.DUE_TIME = #{batchDate} + 7      /*交费对应日前7天*/
			]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code}]]></if>
		<![CDATA[) G WHERE 1=1]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(G.POLICY_ID , #{modnum}) = #{start}]]></if>
	</select>
	
	<!-- 查询保单基础信息 -->
	<select id="PA_findMonthlyProductRenewalFeeReminderBasicData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.* FROM (
		        SELECT (SELECT CA.CHANNEL_TYPE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT CA WHERE CA.POLICY_CODE = TCM.POLICY_CODE AND CA.IS_NB_AGENT = '1' AND ROWNUM = 1) AS CHANNEL_TYPE,
			           TN.BUSI_PRD_ID,
			           TCM.POLICY_CODE,
			           TC.CUSTOMER_NAME,
			           TN.LIABILITY_STATE,
			           SYSDATE VALIDATE_DATE,
			           TC.CUSTOMER_GENDER,
			           TAAC.MOBILE_TEL MOBILE_TEL_BC,
			           TC.MOBILE_TEL MOBILE_TEL_TC,
			           TCM.ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 6) ZHI_ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 6)) ZHI_ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 4) BRANCH_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) BRANCH_CODE_NAME,
			           TC.OLD_CUSTOMER_ID CUSTOMER_ID,
			           TC.WECHAT_NO,
			           TC.EMAIL,
			           (SELECT A.OLD_CODE FROM APP___PAS__DBUSER.T_CODE_MAPPER A  WHERE A.CODETYPE='CHANNEL_TYPE' AND A.FROM_MODLE='PAS' AND A.NEW_CODE = TAC.AGENT_CHANNEL) AGENT_CHANNEL,
			           TAC.AGENT_CODE,
			           TAC.AGENT_NAME,
			           TAC.AGENT_MOBILE,
			           TAC.AGENT_STATUS,
			           TCM.Multi_Mainrisk_Flag
			      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
			           APP___PAS__DBUSER.T_CUSTOMER           TC,
			           APP___PAS__DBUSER.T_ADDRESS            TAAC,
			           APP___PAS__DBUSER.T_POLICY_HOLDER      TIL,
			           APP___PAS__DBUSER.T_CONTRACT_AGENT     TBC,
			           APP___PAS__DBUSER.T_AGENT              TAC
			     WHERE TCM.POLICY_CODE = TN.POLICY_CODE
			       AND TCM.POLICY_CODE = TIL.POLICY_CODE
			       AND TCM.POLICY_ID = TBC.POLICY_ID
			       AND TBC.AGENT_CODE = TAC.AGENT_CODE
			       AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			       AND TIL.ADDRESS_ID = TAAC.ADDRESS_ID
			       AND TBC.IS_CURRENT_AGENT = '1' 
			       AND TN.MASTER_BUSI_ITEM_ID IS NULL   ]]>
   		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if> 
   		<![CDATA[ GROUP BY TBC.CHANNEL_TYPE, 
					  TC.CUSTOMER_NAME, 
					  TCM.POLICY_CODE,
					  TC.CUSTOMER_GENDER, 
					  TAAC.MOBILE_TEL, 
					  TC.MOBILE_TEL, 
					  TCM.ORGAN_CODE, 
					  TC.OLD_CUSTOMER_ID, 
					  TC.WECHAT_NO, 
					  TC.EMAIL, 
					  TN.BUSI_PRD_ID, 
					  TN.LIABILITY_STATE, 
					  TAC.AGENT_CHANNEL, 
					  TAC.AGENT_CODE, 
					  TAC.AGENT_NAME, 
					  TAC.AGENT_MOBILE, 
					  TAC.AGENT_STATUS,
					  TCM.Multi_Mainrisk_Flag) A  ]]>
	</select>
	<select  id="PA_findMonthlyProductRenewalFeeReminderIndividualityData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 
				(SELECT DISTINCT PA.DUE_TIME FROM DEV_PAS.T_PREM_ARAP PA WHERE PA.POLICY_CODE = A.POLICY_CODE AND PA.FEE_STATUS IN('00','03')  AND PA.BUSINESS_TYPE IN ('4003', '1005')
		           		   AND PA.DUE_TIME = #{batch_date} +7  AND ROWNUM=1 ) PAY_DUE_DATE,
				(SELECT MAX(PA.PAY_END_DATE) FROM DEV_PAS.T_PREM_ARAP PA WHERE PA.POLICY_CODE = A.POLICY_CODE AND PA.FEE_STATUS  IN('00','03') AND PA.BUSINESS_TYPE IN ('4003', '1005')
		         AND PA.DUE_TIME = #{batch_date}+7  AND ROWNUM=1 ) PAIDUP_DATE,
				 (SELECT H.BANK_NAME FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA,APP___PAS__DBUSER.T_BANK H WHERE 
				TPA.POLICY_ID = C.POLICY_ID AND TPA.NEXT_ACCOUNT_BANK = H.BANK_CODE) BANK_NAME,
				SUBSTR((SELECT TPA.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA WHERE 
										        TPA.POLICY_ID = C.POLICY_ID  ),-4) LASTFOUR	
			FROM APP___PAS__DBUSER.T_POLICY_HOLDER   A,
				APP___PAS__DBUSER.T_CONTRACT_MASTER C
			where A.POLICY_CODE = C.POLICY_CODE 
				AND A.POLICY_CODE = #{policy_code}  ]]>
	</select>
	
	<!-- 查询险种简称险种代码和险种保费 -->
	<select id="PA_selectBusiInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.Product_Code_Sys AS BUSI_PROD_CODE, A.MASTER_BUSI_ITEM_ID, 
			CASE WHEN B.PRODUCT_ABBR_NAME IS NULL THEN '' ELSE B.PRODUCT_ABBR_NAME END PRODUCT_ABBR_NAME,
			(SELECT BP.PRODUCT_ABBR_NAME FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CB,APP___PDS__DBUSER.T_BUSINESS_PRODUCT BP 
				WHERE CB.BUSI_PRD_ID = BP.BUSINESS_PRD_ID AND CB.BUSI_ITEM_ID = DECODE(A.MASTER_BUSI_ITEM_ID,NULL,A.BUSI_ITEM_ID,A.MASTER_BUSI_ITEM_ID)) MASTERNAME  
		]]>
			
		<if test=" pay_due_date != null ">
			<![CDATA[
				, (SELECT SUM(P.FEE_AMOUNT) FROM APP___PAS__DBUSER.T_PREM P WHERE P.POLICY_CODE = A.POLICY_CODE AND P.Item_Id = C.Item_Id
			AND P.DUE_TIME = #{pay_due_date} AND P.FEE_SCENE_CODE = 'RN' AND P.FEE_STATUS != 02 ) RISKPREM			
			]]>
		</if>

		<![CDATA[
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
			 left join APP___PAS__DBUSER.T_CONTRACT_PRODUCT C
		             on A.Policy_Code = C.Policy_Code
		            AND A.Busi_Item_Id = c.Busi_Item_Id
		     left join Dev_pas.t_Renew_Change TRC
		           on TRC.Policy_Code = a.policy_code
		          and TRC.Item_Id = c.item_id
		          and TRC.Valid_Time = c.maturity_date
		      left join APP___PAS__DBUSER.T_BUSINESS_PRODUCT B 
		         on  B.BUSINESS_PRD_ID = decode(TRC.New_Busi_Prd_Id,null,a.busi_prd_id,trc.new_busi_prd_id)
		       WHERE 1 = 1 
		       and a.LIABILITY_STATE <> 4
            and  a.LIABILITY_STATE <> 3
				 AND A.POLICY_CODE = #{policy_code}
		]]>	
	</select>
</mapper>