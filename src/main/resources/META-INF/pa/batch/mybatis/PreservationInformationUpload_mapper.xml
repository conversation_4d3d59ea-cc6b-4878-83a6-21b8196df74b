<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.taxpreserverinfosend.dao.IPreservationInformationUploadDao">

    <sql id="csContractBusiProdWhereCondition">
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND <PERSON>.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>
	
	<sql id="csAddressWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND A.STATE = #{state} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
	</sql>
	
	<sql id="csContractAgentWhereCondition">
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" agent_type  != null "><![CDATA[ AND A.AGENT_TYPE = #{agent_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" is_nb_agent != null and is_nb_agent != ''  "><![CDATA[ AND A.IS_NB_AGENT = #{is_nb_agent} ]]></if>
	</sql>
	
	<sql id="csContractBeneCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" bene_type  != null "><![CDATA[ AND A.BENE_TYPE = #{bene_type} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
	</sql>
    
    <sql id="csIsImitionA">
       <if test="cs_send_status != null and cs_send_status != ''"><![CDATA[AND A.CS_SEND_STATUS = #{cs_send_status}]]></if>
       <if test="accept_code"><![CDATA[AND  A.ACCEPT_CODE = #{accept_code} ]]></if>
    </sql>
    
    <sql id="csIsImitionB">
       <if test="cs_send_status != null and cs_send_status != ''"><![CDATA[AND B.CS_SEND_STATUS = #{cs_send_status}]]></if>
       <if test="accept_code"><![CDATA[AND B.ACCEPT_CODE = #{accept_code} ]]></if>
    </sql>
    
    <!--税优 按索引查询操作 -->	
	<select id="findTAXCsPolicyChangeById" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.ENDORSE_CODE,
                       A.APPLY_TIME,
                       A.FINISH_TIME,
                       A.VALIDATE_TIME,
                       A.CHANGE_ID,
                       A.POLICY_CHG_ID,
                       A.SERVICE_CODE,
                       A.POLICY_ID,
                       A.ACCEPT_ID,
                       A.POLICY_CODE,
                       TCM.VALIDATE_DATE,
                       TCM.EXPIRY_DATE,
                       TCM.SUSPEND_DATE,
                       TCM.RERINSTATE_DATE,
					   tm.END_CAUSE,
                       tm.LIABILITY_STATE,
                       TCM.APPLY_CODE,
                       TCM.Total_Policy_Sequence_No,
                       TCM.LAPSE_DATE,
                       SUBSTR(TCM.ORGAN_CODE, 0, 6) ZHI_ORGAN_CODE,
                       SUBSTR(TCM.ORGAN_CODE, 0, 4) BRANCH_CODE,
                       (SELECT TUO.ORGAN_NAME
                          FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
                         WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) BRANCH_CODE_NAME,
                       (SELECT TUO.ORGAN_PROVINCE
                          FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
                         WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) ORGAN_PROVINCE
                  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE   A,
                       APP___PAS__DBUSER.T_CS_CONTRACT_MASTER TCM,
                       APP___PAS__DBUSER.T_CONTRACT_MASTER tm
                 WHERE TCM.CHANGE_ID = A.CHANGE_ID
                   AND A.Policy_Chg_Id = TCM.Policy_Chg_Id
                   AND TCM.policy_code = tm.policy_code
                   AND TCM.policy_id = tm.policy_id
                   AND TCM.Old_New = '1'
                   AND TCM.policy_Code = #{policy_code}
                   AND A.accept_Id = #{accept_id}
             ]]>
    </select>
    
    <!--税优 针对多主线的情况查询保单主表信息 -->
	<select id="findTAXAllCsContractBusiProdMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.Renew,
		              A.BUSI_ITEM_ID,
		              TBP.BUSINESS_PRD_ID,
                      TBP.PRODUCT_NAME_SYS,
                      CPC.SERVICE_CODE,
                      tcm.FORMER_ID,
                      tcm.POLICY_RELATION_TYPE,
                      TBP.Cover_Period_Type
                 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE      CPC,
                      APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A,
                      APP___PAS__DBUSER.T_BUSINESS_PRODUCT      TBP,
                      APP___PAS__DBUSER.T_CS_CONTRACT_MASTER tcm
                WHERE CPC.POLICY_CHG_ID = A.POLICY_CHG_ID
                AND CPC.POLICY_CHG_ID = tcm.POLICY_CHG_ID
                  AND A.CHANGE_ID = CPC.CHANGE_ID
                  AND  A.CHANGE_ID = tcm.CHANGE_ID
                  AND TBP.BUSINESS_PRD_ID = A.BUSI_PRD_ID
                  AND A.OLD_NEW = '1'
                  AND tcm.OLD_NEW = '1']]>
		<include refid="csContractBusiProdWhereCondition" />
		<![CDATA[ AND A.MASTER_BUSI_ITEM_ID IS NULL ORDER BY A.MASTER_BUSI_ITEM_ID DESC]]>
	</select>
    
    <!-- 根据受理号查询受理信息 -->
	<select id="findTAXCsAcceptChangeByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE,
                     A.ACCEPT_ID,
                     A.ACCEPT_TIME,
                     A.VALIDATE_TIME,
                     A.SERVICE_CODE,
                     (SELECT SERVICE_NAME
                        FROM APP___PAS__DBUSER.T_SERVICE ts
                       WHERE ts.SERVICE_CODE = A.SERVICE_CODE) as service_name,
                     B.APPLY_TIME
                FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A,
                     APP___PAS__DBUSER.T_CS_APPLICATION   B
               WHERE 1 = 1
                 and A.CHANGE_ID = B.CHANGE_ID]]>
		<if test=" accept_code !=null and accept_code !='' ">
	   		<![CDATA[ 
	   			and A.ACCEPT_CODE = #{accept_code}
	   		]]>
	    </if>
	    <if test="accept_id !=null and accept_id !='' ">
		    <![CDATA[ 
		   		and A.ACCEPT_ID = #{accept_id}
	   		]]>
	    </if>
	</select>
	
	<!-- 根据受理号,保全变更id查询退保信息 -->
	<select id="findTAXTSurrender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select ts.HESITATE_FLAG
                  from APP___PAS__DBUSER.T_surrender ts
                 where ts.policy_chg_id = #{policy_chg_id}
                   and ts.accept_code = #{accept_code}
                   and rownum = 1]]>
	</select>
	
	<!-- 关于新核心系统税优保单的需求  判断当前受理号是收费还是付费即收集其收付费的信息-->
	<select id="findTAXPremArapByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      SELECT T.UNIT_NUMBER,
	         T.PAY_MODE,
	         TcT.Is_Prem,
             T.Finish_Time,
             NVL(SUM(CASE
                   WHEN T.ARAP_FLAG = 1 THEN
                    T.FEE_AMOUNT
                   ELSE
                    -T.FEE_AMOUNT
                 END),0) FEE_AMOUNT
        FROM APP___PAS__DBUSER.T_PREM_ARAP T , APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK TcT
       WHERE T.Business_Code = TcT.Accept_Code
       and T.Policy_Code = TcT.Policy_Code
       and T.BUSINESS_CODE = #{business_code}
       and T.Policy_Code = #{policy_code}
       and T.FEE_STATUS not in ('02','03')
       GROUP BY T.UNIT_NUMBER,T.PAY_MODE,TcT.Is_Prem,T.Finish_Time
	]]>
	</select>
	
	<!-- 关于新核心系统税优保单的需求  判断当前受理号是收费还是付费即收集其收付费的信息-->
	<select id="findTAXPremArapByBusinessCode1" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      SELECT T.UNIT_NUMBER,
	         T.PAY_MODE,
	         T.PREM_FREQ,
	         TcT.Is_Prem,
	         T.INSERT_TIME,
             T.Finish_Time,
             NVL(SUM(CASE
                   WHEN T.ARAP_FLAG = 1 THEN
                    T.FEE_AMOUNT
                   ELSE
                    -T.FEE_AMOUNT
                 END),0) FEE_AMOUNT
        FROM APP___PAS__DBUSER.T_PREM_ARAP T , APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK TcT
       WHERE T.Business_Code = TcT.Accept_Code
       and T.Policy_Code = TcT.Policy_Code
       and T.BUSINESS_CODE =#{business_code}
       and T.Policy_Code = #{policy_code}
       and T.FEE_STATUS !='03'
       and T.FEE_TYPE NOT IN ('G004430000','G004440000','G004460000','G004450000')
       GROUP BY T.UNIT_NUMBER,T.UNIT_NUMBER,T.PAY_MODE,T.PREM_FREQ, T.INSERT_TIME,TcT.Is_Prem,T.Finish_Time
	]]>
	</select>
	
	<!-- 关于新核心系统税优保单的需求  收集核销时间-->
	<select id="findTAXFinishTimeByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      SELECT T.UNIT_NUMBER,
	         T.PAY_MODE,
	         T.PREM_FREQ,
	         T.FINISH_TIME
        FROM APP___PAS__DBUSER.T_PREM_ARAP T
       WHERE T.BUSINESS_CODE =#{business_code} and rownum = 1
	]]>
	</select>
	
	<!-- 关于新核心系统税优保单的需求  查询报送产品退保金额-->
	<select id="findTAXPremArapMoneyByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      SELECT T.UNIT_NUMBER,
                            abs(NVL(SUM(CASE
                                      WHEN T.ARAP_FLAG = 1 THEN
                                       T.FEE_AMOUNT
                                      ELSE
                                       -T.FEE_AMOUNT
                                    END),
                                0)) FEE_AMOUNT
                       FROM APP___PAS__DBUSER.T_PREM_ARAP T
                      WHERE T.BUSINESS_CODE = #{business_code}
                        AND T.Busi_Prod_Code = #{busi_prod_code}
                      GROUP BY T.UNIT_NUMBER
	]]>
	</select>
	
	<!-- 税优 通过policyCode 查询投保人 -->
	 <select id="PA_findTAXCustomerByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.CUSTOMER_NAME,A.CUSTOMER_GENDER,A.TAX_CUSTOMER_NO,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_ID,
		                 A.CUSTOMER_CERT_TYPE,A.CUSTOMER_CERTI_CODE, A.MOBILE_TEL,A.COMPANY_NAME,
		                 (SELECT tc.COUNTRY_NUM FROM DEV_PAS.T_COUNTRY tc WHERE  tc.COUNTRY_CODE = A.COUNTRY_CODE ) AS COUNTRY_NUM
		                 ,il.ADDRESS_ID
                  from dev_pas.T_CS_POLICY_HOLDER il, dev_pas.T_CS_CUSTOMER A
                 where A.Change_Id = il.change_id
                   and il.customer_id = A.customer_id
                   and il.policy_chg_id = #{policy_chg_id}
                   and il.policy_code = #{policy_code}
                   and il.old_new = '1'
                   and A.Old_New = '1' ]]>
	</select>
	
	<!-- 税优 通过policyCode 查询投保人 -->
	 <select id="PA_findTAXCustomerByPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.CUSTOMER_NAME,A.CUSTOMER_GENDER,A.TAX_CUSTOMER_NO,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_ID,
                		                 A.CUSTOMER_CERT_TYPE,A.CUSTOMER_CERTI_CODE, A.MOBILE_TEL,A.COMPANY_NAME,
                		                 (SELECT tc.COUNTRY_NUM FROM DEV_PAS.T_COUNTRY tc WHERE  tc.COUNTRY_CODE = A.COUNTRY_CODE ) AS COUNTRY_NUM
                		                 ,il.ADDRESS_ID
                                  from dev_pas.T_POLICY_HOLDER il, dev_pas.T_CUSTOMER A
                                 where il.customer_id = A.customer_id
                                   and il.policy_code = #{policy_code}]]>
	</select>
	
	<!--税优 通过保单号和保单id获取被保人信息，提供个外围系统 -->
	<select id="queryTAXInsuredListBypolicyCodeAndPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
            SELECT TIL.POLICY_CODE,TIL.POLICY_ID,TC.CUSTOMER_ID,
					       TIL.LIST_ID AS INSURED_ID,
					       TIL.INSURED_AGE,
					       TIL.ADDRESS_ID,
					       TC.CUSTOMER_NAME,
					       TC.CUSTOMER_GENDER,
					       TC.CUSTOMER_BIRTHDAY,
					       TC.CUSTOMER_CERT_TYPE,
					       TC.CUSTOMER_CERTI_CODE,
					       TC.COUNTRY_CODE,
					       TC.TAX_CUSTOMER_NO,
					       (SELECT A.COUNTRY_NUM FROM DEV_PAS.T_COUNTRY A WHERE  A.COUNTRY_CODE = TC.COUNTRY_CODE ) AS COUNTRY_NUM,
					       TC.MOBILE_TEL,
					       TC.JOB_CODE,
					       TIL.RELATION_TO_PH,
					       TIL.SOCI_SECU
					  FROM DEV_PAS.T_INSURED_LIST TIL, DEV_PAS.T_CUSTOMER TC
					 WHERE 1 = 1
					   AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
					   AND TIL.POLICY_CODE =  #{policy_code}        
		]]>
					   <if test=" list_id  != null and list_id != '' "><![CDATA[ AND TIL.LIST_ID = #{list_id} ]]></if> 
	</select>
	
	<!-- 税优查询受益人所有信息 -->
	<select id="PA_findTAXAllContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID,A.COMPANY_ID,
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE,A.Customer_Name,A.CUSTOMER_BIRTHDAY,
			A.CUSTOMER_GENDER,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_CERTI_CODE , 
			A.BENE_KIND , A.COMPANY_ID, AGENT_RELATION  FROM APP___PAS__DBUSER.T_CS_CONTRACT_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="csContractBeneCondition" />
	</select>
	
	<!-- 最新地址信息 -->
	<select id="findTAXNewCsAddress" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select *  from (select a.*, row_number() over(order by a.address_id desc) rn   
      from APP___PAS__DBUSER.T_ADDRESS a WHERE 1 = 1 ]]>
      <include refid="csAddressWhereCondition" />
	<![CDATA[ order by a.address_id desc) r  where r.rn <= 1 ]]>
	</select>
	
  <!--税优 保单号和保单id获取保单受益人信息-->
  <select id="PA_findTAXContractBeneInfoByCodeAndId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
		   SELECT TCB.POLICY_CODE,
					       TCB.INSURED_ID,
					       TCB.BENE_TYPE,
					       TC.CUSTOMER_NAME,
					       TC.CUSTOMER_GENDER,
					       TC.CUSTOMER_BIRTHDAY, 
					       TC.CUSTOMER_CERT_TYPE,
					       TC.CUSTOMER_CERTI_CODE,
					       TC.COUNTRY_CODE,
					       (SELECT A.COUNTRY_NUM FROM DEV_PAS.T_COUNTRY A WHERE  A.COUNTRY_CODE = TC.COUNTRY_CODE ) AS COUNTRY_NUM,
					       TCB.DESIGNATION,
					       TCB.SHARE_ORDER,
					       TCB.SHARE_RATE
					  FROM DEV_PAS.T_CONTRACT_BENE TCB,
					       DEV_PAS.T_CUSTOMER TC
					 WHERE 1 = 1
					   AND TCB.CUSTOMER_ID = TC.CUSTOMER_ID
					   AND TCB.POLICY_CODE = #{policy_code}
					   AND TCB.INSURED_ID = #{insured_id}
		 ]]>
	</select>
   
   <!-- 税优 查询产品所有操作 -->
	<select id="PA_findTAXAllContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCBP.BUSI_ITEM_ID,
                               TCBP.POLICY_ID,
                               TCBP.POLICY_CODE,
						       TTB.End_Cause,
                               TCBP.BUSI_PROD_CODE,
                               TCBP.Expiry_Date,
                               TCBP.Suspend_Date,
                               TCBP.Rerinstate_Date,
                               TCBP.Renew,
                               TCP.Amount,
                               TCP.PREM_FREQ,
                               TCP.POLICY_CHG_ID,
                               TCP.Item_Id,
                               TBP.PRODUCT_CATEGORY1,
                               TBP.PRODUCT_ABBR_NAME,
                               TIL.INSURED_AGE,
                               TCBP.APPLY_DATE,
                               TCBP.VALIDATE_DATE,
                               TCBP.MATURITY_DATE,
                               TCBP.MASTER_BUSI_ITEM_ID,
                               TTB.LIABILITY_STATE,
                               TCP.STD_PREM_AF,
                               TPB.PRODUCT_CODE_BOCIC, --险种ID
                               TPB.PRODUCT_NAME_BOCIC, --险种名称
                               TCB.COVERAGE_PACK_CODE --产品组代码
                          FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD TCBP,
                               DEV_PAS.T_CS_CONTRACT_PRODUCT   TCP,
                               DEV_PAS.T_CS_BENEFIT_INSURED    TBI,
                               DEV_PAS.T_CS_INSURED_LIST       TIL,
                               DEV_PDS.T_BUSINESS_PRODUCT      TBP,
                               DEV_PDS.T_PRODUCT_BOCIC         TPB,
                               DEV_PDS.T_COVERAGE_BOCIC        TCB,
                               DEV_PAS.T_CONTRACT_BUSI_PROD  TTB
                         WHERE tcbp.policy_chg_id = tcp.policy_chg_id
                           and tcp.policy_chg_id = tbi.policy_chg_id
                           and tbi.policy_chg_id = til.policy_chg_id
                           and tcbp.busi_item_id = tcp.busi_item_id
                           and TCBP.Busi_Item_Id  = TTB.Busi_Item_Id
                           and TCBP.Busi_Prd_Id = tbp.business_prd_id
                           and tcbp.old_new = '1'
                           and tcp.old_new = '1'
                           and tbi.old_new = '1'
                           and til.old_new = '1'
                           and tbp.business_prd_id = tcbp.busi_prd_id
                           and tbi.busi_item_id = tcbp.busi_item_id
                           and TBP.Business_Prd_Id = TPB.Business_Prd_Id
                           and TCB.BOCIC_COVERAGE_ID = TPB.BOCIC_COVERAGE_ID
                           and TCP.Policy_Chg_Id = #{policy_chg_id}
                           and TCBP.POLICY_CODE = #{policy_code}
                           and TBI.INSURED_ID = #{insured_id}
       ]]>
	</select>
	
	<!--税优 查询责任组所有操作 -->
	<select id="findTAXAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select d.DUTY_CODE_BOCIC,e.liability_state,e.amount,b.product_category1,e.initial_discnt_prem_af
                      from dev_pas.t_contract_busi_prod a,
                           DEV_PDS.T_BUSINESS_PRODUCT   b,
                           DEV_PDS.T_PRODUCT_BOCIC      c,
                           DEV_PDS.T_DUTY_BOCIC         d,
                           dev_pas.T_CONTRACT_PRODUCT e
                     where a.busi_prd_id = b.business_prd_id
                       and b.business_prd_id = c.business_prd_id
                       and c.bocic_prd_id = d.bocic_prd_id
                       and a.busi_item_id = e.busi_item_id
                       and a.policy_code = #{policy_code}
                       and a.busi_item_id = #{busi_item_id}
                       order by d.liab_id]]>
	</select>
	
	<!--税优 根据保单号及被保人id查询被保人顺序 -->
	<select id="findTAXInsuredOrder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID
                          FROM DEV_PAS.T_BENEFIT_INSURED A
                         WHERE A.POLICY_CODE = #{policy_code}
                           AND A.INSURED_ID = #{insured_id} AND rownum = 1]]>
	</select>
	
	<!--税优 查询核保信息 -->
	<select id="PA_findTAXAllUwPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.UW_POLICY_ID, A.UW_LEVEL_CODE, A.UW_STATUS, A.UW_FINISH_TIME, A.APPLY_CODE, A.ORGAN_CODE, 
			A.POLICY_CHG_ID, A.POLICY_ID, A.HOLDER_ID, A.APPLY_LEVEL_CODE, 
			A.CLM_TYPE_LIST, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.POLICY_CODE, A.RI_FALG, A.UW_SOURCE_TYPE, 
			A.POLICY_DECISION, A.UW_USER_ID FROM APP___PAS__DBUSER.T_UW_POLICY A WHERE ROWNUM <=  1000  ]]>
		<include refid="csContractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.UW_POLICY_ID ]]> 
	</select>
	
	 <!-- 税优 根据受理号查询保单相关信息 -->
  <select id="findTAXPolicyInfoByAccCode" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		 select cpc.policy_code, cac.accept_code,cpc.service_code,cpc.POLICY_CHG_ID
		 from APP___PAS__DBUSER.t_cs_accept_change cac
		 left join APP___PAS__DBUSER.t_cs_policy_change cpc
		   on cac.accept_id = cpc.accept_id
		where cac.accept_code = #{accept_code}
    ]]>
  </select>
  
  <!-- 税优 根据受理号查询应收应付相关信息 -->
  <select id="findTAXARAPByAccCode" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
	select cpa.is_prem,cp.FEE_STATUS
	  from APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK cpa , APP___PAS__DBUSER.T_PREM_ARAP cp
		where cp.policy_code = cpa.policy_code
         AND cp.business_code = cpa.accept_code
		 AND cpa.POLICY_CODE = #{policy_code}
		 AND cpa.accept_code = #{business_code}
		 AND cp.fee_status != '03'
    ]]>
  </select>
  
  <!-- 税优 查询单个代理人 -->
	<select id="findTAXContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM (
			<![CDATA[ SELECT T.AGENT_NAME, A.AGENT_START_DATE, T.AGENT_MOBILE, A.RELATION_TO_PH, T.AGENT_NORMAL_TYPE AS AGENT_TYPE,T.AGENT_LEVEL,
			A.APPLY_CODE, A.POLICY_CODE,A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE, A.IS_CURRENT_AGENT ,A.LAST_AGENT_CODE,
       A.LAST_AGENT_NAME, A.IS_NB_AGENT,A.ORGAN_CODE,A.CHANNEL_TYPE  FROM APP___PAS__DBUSER.T_CS_CONTRACT_AGENT A ,APP___PAS__DBUSER.T_AGENT T 
	       WHERE A.AGENT_CODE=T.AGENT_CODE  AND A.IS_CURRENT_AGENT=1 ]]>
		<include refid="csContractAgentWhereCondition" />
		<![CDATA[ ORDER BY  t.employment_date DESC ]]>
		)WHERE rownum =1
	</select>
	
	<!-- 税优 根据查询下次应交日相关信息 -->
  <select id="findTAXCsContractExtend" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		 SELECT PAY_DUE_DATE
                  FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND CE
                 WHERE CE.Policy_Code = #{policy_code}
                   and CE.Item_Id = #{item_id}
    ]]>
  </select>
  
  <!-- 税优 根据查询产品所有加费信息 -->
  <select id="findTAXCsExtraPrem" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		 select TCEP.POLICY_CODE,TCEP.busi_item_id,TCEP.Extra_Prem
                      from DEV_PAS.T_CS_EXTRA_PREM TCEP
                     where tcep.busi_item_id = #{busi_item_id}
                       and tcep.policy_chg_id = #{policy_chg_id}
                       and tcep.policy_code = #{policy_code}
                       and tcep.extra_type in ('1','2')
                       and tcep.old_new = '1'
    ]]>
  </select>
  
	<!-- 税优 批处理收集报送数据 -->
	<select id="findTAXCsCollectBatchInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
		          A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
		          A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO
		  FROM DEV_PAS.T_CS_TAX_TRANS_TASK A
		 WHERE (A.CS_SEND_STATUS IN ('01', '03') OR A.PH_SEND_STATUS IN ('01', '03') OR A.INS_SEND_STATUS IN ('01', '03') OR A.CS_SEND_STATUS IS NULL)
		   AND NOT EXISTS (SELECT 1/*这里要排除掉付费类的待报送任务，交给付费到账通过时报送*/
		          FROM DEV_PAS.T_CS_TAX_TRANS_TASK TCTT
		         WHERE (TCTT.CS_SEND_STATUS = '01' OR TCTT.PH_SEND_STATUS = '01' OR TCTT.INS_SEND_STATUS = '01')
		           AND TCTT.ACCEPT_CODE = A.ACCEPT_CODE
		           AND (SELECT SUM(CASE
		                             WHEN T.ARAP_FLAG = 1 THEN
		                              T.FEE_AMOUNT
		                             ELSE
		                              -T.FEE_AMOUNT
		                           END) FEE_AMOUNT
		                  FROM APP___PAS__DBUSER.T_PREM_ARAP  T
		                 WHERE T.FEE_STATUS NOT IN ('16')
		                   AND T.BUSINESS_CODE = TCTT.ACCEPT_CODE) < 0)]]>
           <if test=" accept_code != null and accept_code != ''"><![CDATA[AND A.ACCEPT_CODE = #{accept_code} ]]></if>
           <if test=" modnum != null and start != null "><![CDATA[ AND MOD(A.LIST_ID, #{modnum}) = #{start} ]]></if>
	</select>
  
  <select id="findTAXCsPrecontProductInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREC_POLICY_CHG_ID, A.PRECONT_STATUS, A.NEW_ACCOUNT_ID, A.NEW_UNIT, A.NEW_PREM, A.NEW_PLAN_ID, A.NEW_AMOUNT, 
			A.TRIGGER_MODULE, A.OLD_AMOUNT, A.ITEM_ID, A.OLD_CHARGE_MODE, A.OLD_PLAN_ID, A.OLD_ACCOUNT_ID, 
			A.OLD_NEW, A.PRECONT_SA_INC, A.CHANGE_ID, A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.NEW_PAY_MODE, A.OLD_PREM, A.POLICY_ID, A.OLD_PAY_MODE, A.OLD_STAND_AMOUNT, A.NEW_STAND_AMOUNT, 
			A.ACCEPT_ID, A.NEW_COUNT_WAY, A.OLD_UNIT, A.OPERATION_TYPE, A.NEW_CHARGE_MODE, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_BENEFIT_LEVEL, A.LOG_ID, A.NEW_BENEFIT_LEVEL, 
			A.OLD_COUNT_WAY FROM APP___PAS__DBUSER.T_CS_PRECONT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="csAddressWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="findTAXContractProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select tcp.std_prem_af
                  from dev_pas.T_CONTRACT_PRODUCT tcp
                 where tcp.busi_item_id = #{busi_item_id}
                   and tcp.policy_code = #{policy_code}
                   and tcp.item_id = #{item_id}]]>
	</select>
	<!-- 针对复核完成 生成税优任务多保单的情况 -->
	<select id="findTAXPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select P.Policy_Code,b.accept_code,b.accept_id
                  from dev_pas.t_Cs_Policy_Change P
                  inner join dev_pas.t_cs_accept_change b on b.accept_id = P.accept_id
                 where 1 = 1
                 and P.accept_Id = #{accept_id}]]>
	</select>

	<!-- 关于新核心系统税优保单的需求  判断当前受理号是收费还是付费即收集其收付费的信息-->
	<select id="findTAXCsPremArapByBusinessCode1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	      SELECT T.UNIT_NUMBER,
	         T.PAY_MODE,
	         TcT.Is_Prem,
             T.Finish_Time,
             NVL(SUM(CASE
                   WHEN T.ARAP_FLAG = 1 THEN
                    T.FEE_AMOUNT
                   ELSE
                    -T.FEE_AMOUNT
                 END),0) FEE_AMOUNT
        FROM APP___PAS__DBUSER.T_CS_PREM_ARAP T , APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK TcT
       WHERE T.Business_Code = TcT.Accept_Code
       and T.Policy_Code = TcT.Policy_Code
       and T.BUSINESS_CODE =#{business_code}
       and T.Policy_Code = #{policy_code}
       and T.FEE_STATUS !='03'
       and T.FEE_TYPE NOT IN ('G004430000','G004440000','G004460000','G004450000')
       GROUP BY T.UNIT_NUMBER,T.PAY_MODE,TcT.Is_Prem,T.Finish_Time
	]]>
	</select>
	<!-- 关于新核心系统税优保单的需求  判断当前受理号是收费还是付费即收集其收付费的信息-->
	<select id="findTAXCsPremArapByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	      SELECT T.UNIT_NUMBER,
	         T.PAY_MODE,
	         TcT.Is_Prem,
             T.Finish_Time,
             NVL(SUM(CASE
                   WHEN T.ARAP_FLAG = 1 THEN
                    T.FEE_AMOUNT
                   ELSE
                    -T.FEE_AMOUNT
                 END),0) FEE_AMOUNT
        FROM APP___PAS__DBUSER.T_CS_PREM_ARAP T , APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK TcT
       WHERE T.Business_Code = TcT.Accept_Code
       and T.Policy_Code = TcT.Policy_Code
       and T.BUSINESS_CODE = #{business_code}
       and T.Policy_Code = #{policy_code}
       and T.FEE_STATUS not in ('02','03')
       GROUP BY T.UNIT_NUMBER,T.PAY_MODE,TcT.Is_Prem,T.Finish_Time
	]]>
	</select>
</mapper>