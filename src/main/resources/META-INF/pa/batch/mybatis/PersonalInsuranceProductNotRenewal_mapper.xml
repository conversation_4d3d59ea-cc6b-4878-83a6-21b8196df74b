<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.batch.personalInsuranceProductNotRenewal.dao.Impl.PersonalInsuranceProductNotRenewalDaoImpl">

	<select id="PA_policyNotRenewalQueryData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT G.POLICY_CODE,G.POLICY_ID,ROWNUM RN FROM(
				SELECT DISTINCT A.POLICY_CODE, A.POLICY_ID
		        FROM APP___PAS__DBUSER.T_NO_RENEW_LOG    A
		       WHERE A.NORENEW_REASON IN ('4', '5') /*不续保原因*/
		         AND A.NORENEW_DATE = #{batchTime} /*不续保原因下达时间*/
			]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code}]]></if>
		<![CDATA[) G WHERE 1=1]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(G.POLICY_ID , #{modnum}) = #{start}]]></if>
	</select>
	
	<select id="PA_productNotRenewalQueryData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, A.POLICY_CODE, A.BUSI_ITEM_ID, A.POLICY_ID,
			(select c.product_name_std
			  from dev_pas.t_contract_busi_prod b, dev_pds.T_BUSINESS_PRODUCT c
			 where b.policy_code = a.policy_code
			   and b.busi_item_id = a.busi_item_id
			   and b.busi_prd_id = c.BUSINESS_PRD_ID) as product_name_std
		        FROM APP___PAS__DBUSER.T_NO_RENEW_LOG    A
		       WHERE A.NORENEW_REASON IN ('4', '5') /*不续保原因*/
		         AND A.NORENEW_DATE = #{norenew_date} /*不续保原因下达时间*/
				 AND A.POLICY_CODE = #{policy_code}]]>
	</select>
	<!-- 查询产品停售信息 -->
	<select id="PA_findProductNotSellInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select distinct e.product_name_std,/*产品*/
                	a.sale_end as EXPIRY_DATE,/*停售日期*/
                	a.UPDATE_TIME as validate_date /*配置日期*/
           from dev_pds.t_mms_auth_list_info a,
                dev_pds.t_mms_auth_main_info b,
                dev_pas.t_contract_master c,
                dev_pas.t_contract_busi_prod d,
                dev_pds.T_BUSINESS_PRODUCT e
          where a.auth_minfo_id = b.auth_main_id
            and c.policy_code = d.policy_code
            and d.busi_prod_code = b.autho_code
            and c.ORGAN_CODE IN (SELECT T.ORGAN_CODE
                                  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
                                 START WITH T.ORGAN_CODE = a.organ_code
                                CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE
                                 GROUP BY ORGAN_CODE)
            and c.channel_type = a.channel_code
            and d.busi_prd_id = e.BUSINESS_PRD_ID
            and c.policy_code = #{policy_code}]]>
          <if test=" list_not_sell  != null and list_not_sell">
			<![CDATA[ and b.autho_code in (]]>
			<foreach collection="list_not_sell" item="autho_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{autho_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</select>
	
	<!-- 查询保单基础信息 -->
	<select id="PA_findProductNotRenewalrBasicData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.* FROM (
		        SELECT TBC.CHANNEL_TYPE,
			           TN.BUSI_PRD_ID,
			           TCM.POLICY_CODE,
			           TC.CUSTOMER_NAME,
			           TCM.LIABILITY_STATE,
			           SYSDATE VALIDATE_DATE,
			           TC.CUSTOMER_GENDER,
			           TAAC.MOBILE_TEL MOBILE_TEL_BC,
			           TC.MOBILE_TEL MOBILE_TEL_TC,
			           TCM.ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 6) ZHI_ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 6)) ZHI_ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 4) BRANCH_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) BRANCH_CODE_NAME,
			           TC.OLD_CUSTOMER_ID CUSTOMER_ID,
			           TC.WECHAT_NO,
			           TC.EMAIL,
			           (SELECT A.OLD_CODE FROM APP___PAS__DBUSER.T_CODE_MAPPER A  WHERE A.CODETYPE='CHANNEL_TYPE' AND A.FROM_MODLE='PAS' AND A.NEW_CODE = TAC.AGENT_CHANNEL) AGENT_CHANNEL,
			           TAC.AGENT_CODE,
			           TAC.AGENT_NAME,
			           TAC.AGENT_MOBILE,
			           TAC.AGENT_STATUS
			      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
			           APP___PAS__DBUSER.T_CUSTOMER           TC,
			           APP___PAS__DBUSER.T_ADDRESS            TAAC,
			           APP___PAS__DBUSER.T_POLICY_HOLDER      TIL,
			           APP___PAS__DBUSER.T_CONTRACT_AGENT     TBC,
			           APP___PAS__DBUSER.T_AGENT              TAC
			     WHERE TCM.POLICY_CODE = TN.POLICY_CODE
			       AND TCM.POLICY_CODE = TIL.POLICY_CODE
			       AND TCM.POLICY_ID = TBC.POLICY_ID
			       AND TBC.AGENT_CODE = TAC.AGENT_CODE
			       AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			       AND TIL.ADDRESS_ID = TAAC.ADDRESS_ID
			       AND TBC.IS_CURRENT_AGENT = '1' 
			       AND TN.MASTER_BUSI_ITEM_ID IS NULL   ]]>
   		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if> 
   		<![CDATA[ GROUP BY TBC.CHANNEL_TYPE, 
					  TC.CUSTOMER_NAME, 
					  TCM.POLICY_CODE,
					  TC.CUSTOMER_GENDER, 
					  TAAC.MOBILE_TEL, 
					  TC.MOBILE_TEL, 
					  TCM.ORGAN_CODE, 
					  TC.OLD_CUSTOMER_ID, 
					  TC.WECHAT_NO, 
					  TC.EMAIL, 
					  TN.BUSI_PRD_ID, 
					  TCM.LIABILITY_STATE, 
					  TAC.AGENT_CHANNEL, 
					  TAC.AGENT_CODE, 
					  TAC.AGENT_NAME, 
					  TAC.AGENT_MOBILE, 
					  TAC.AGENT_STATUS) A  ]]>
	</select>
</mapper>