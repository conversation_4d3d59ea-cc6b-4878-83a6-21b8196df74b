<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.core.pa.dao.impl.RenewExtraDaoImplForTest.AutoExtraTest">
<!-- drop 表 -->
	<update id="dropTable1" >
        <![CDATA[  
            drop table pa_T_CONTRACT_EXTEND purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable2" >
        <![CDATA[  
            drop table pa_policy_id purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable3" >
        <![CDATA[  
            drop table pa_T_CONTRACT_MASTER purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable4" >
        <![CDATA[  
            drop table pa_T_CONTRACT_BUSI_PROD purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable5" >
        <![CDATA[  
            drop table pa_T_CONTRACT_PRODUCT purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable6" >
        <![CDATA[  
            drop table pa_t_prem_arap purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable7" >
        <![CDATA[  
            drop table pa_T_PAYER purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable8" >
        <![CDATA[  
            drop table pa_T_PAYER_ACCOUNT purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable9" >
        <![CDATA[  
            drop table pa_T_CONTRACT_AGENT purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable10" >
        <![CDATA[  
            drop table pa_T_POLICY_HOLDER purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable11" >
        <![CDATA[  
            drop table pa_T_BENEFIT_INSURED purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable12" >
        <![CDATA[  
            drop table pa_T_INSURED_LIST purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable13" >
        <![CDATA[  
            drop table pa_T_EXTRA_PREM purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable14" >
        <![CDATA[  
            drop table pa_delete_busi_item_id_01 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable15" >
        <![CDATA[  
            drop table pa_lock_policy_yes purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable16" >
        <![CDATA[  
            drop table pa_delete_policy_id_01 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable17" >
        <![CDATA[  
            drop table pa_business_id_01 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable18" >
        <![CDATA[  
            drop table pa_business_id purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable19" >
        <![CDATA[  
            drop table pa_t_prem_01 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable20" >
        <![CDATA[  
            drop table pa_t_prem_02 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable21" >
        <![CDATA[  
            drop table pa_t_prem_03 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable22" >
        <![CDATA[  
            drop table pa_t_prem_04 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable23" >
        <![CDATA[  
            drop table pa_customer_id purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable24" >
        <![CDATA[  
            drop table pa_t_customer purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable25" >
        <![CDATA[  
            drop table pa_t_prem_05 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable26" >
        <![CDATA[  
            drop table pa_t_prem_06 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable27" >
        <![CDATA[  
            drop table pa_t_prem_07 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable28" >
        <![CDATA[  
            drop table pa_t_prem_100 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable29" >
        <![CDATA[  
            drop table pa_t_prem_200 purge
        ]]>
    </update>
     <!-- drop 表 -->
	<update id="dropTable30" >
        <![CDATA[  
            drop table pa_t_prem purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable31" >
        <![CDATA[  
            drop table pa_unit_number purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable32" >
        <![CDATA[  
            drop sequence pa_unit_number_sequence
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable33" >
        <![CDATA[  
            drop table pa_t_prem_arap_pas_01 purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable34" >
        <![CDATA[  
            drop table pa_t_prem_arap_pas purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable35" >
        <![CDATA[  
            drop table pa_extraction_due_date purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable36" >
        <![CDATA[  
            drop table pa_T_CONTRACT_EXTEND_01 purge
        ]]>
    </update>
    <!-- drop 表 -->
	<update id="dropTable37" >
        <![CDATA[  
            drop table pa_t_prem_pas purge
        ]]>
    </update>
 <!-- drop 表 -->
	<update id="dropTable38" >
        <![CDATA[  
            drop index idx_a
        ]]>
    </update>
<!--createPaTContractExtend01 -->    
    <update id = "createPaTContractExtend01" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_CONTRACT_EXTEND_01 (POLICY_CODE varchar2(20),ORGAN_CODE varchar2(20),EXTRACTION_DUE_DATE date)
          
 ]]>
    </update>
    
    <!--insertPaTContractExtend01 -->    
    <insert id = "insertPaTContractExtend01" parameterType="java.util.Map">
        <![CDATA[ insert into pa_T_CONTRACT_EXTEND_01 (POLICY_CODE,EXTRACTION_DUE_DATE,ORGAN_CODE) values(
        				#{policy_code,jdbcType=VARCHAR}, #{extraction_due_date, jdbcType=DATE} ,#{organ_code,jdbcType=VARCHAR})
          
 ]]>
    </insert>
    
    <!--pa_T_CONTRACT_EXTEND -->    
    <update id = "createPaTContractExtend" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_CONTRACT_EXTEND
            as
          select a.*
          from APP___PAS__DBUSER.T_CONTRACT_EXTEND a 
          where 
          a.policy_code = (select a.policy_code from pa_T_CONTRACT_EXTEND_01 a) and 
          a.extraction_due_date < (select a.extraction_due_date from pa_T_CONTRACT_EXTEND_01 a)
          AND a.organ_code in (select t.organ_code
                            from APP___PAS__DBUSER.t_udmp_org_rel t
                           start with t.organ_code = (select a.organ_code from pa_T_CONTRACT_EXTEND_01 a)
                          connect by prior t.organ_code = t.uporgan_code
                           GROUP BY organ_code)
 ]]>
    </update>
    
      <!--pa_T_CONTRACT_EXTEND -->    
    <update id = "createPaTContractExtendNoPolicyCode" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_CONTRACT_EXTEND
            as
          select a.*
          from APP___PAS__DBUSER.T_CONTRACT_EXTEND a 
          where  
          a.extraction_due_date < (select a.extraction_due_date from pa_T_CONTRACT_EXTEND_01 a)
          AND a.organ_code in (select t.organ_code
                            from APP___PAS__DBUSER.t_udmp_org_rel t
                           start with t.organ_code = (select a.organ_code from pa_T_CONTRACT_EXTEND_01 a)
                          connect by prior t.organ_code = t.uporgan_code
                           GROUP BY organ_code)
 ]]>
    </update>
    
    <!--createPaTPolicyCode -->    
    <update id = "createPaTPolicyCode" parameterType="java.util.Map">
        <![CDATA[ create table pa_policy_id as
					select distinct a.policy_id,a.policy_code
  						from pa_T_CONTRACT_EXTEND a
 ]]>
    </update>
    
    <!--pa_T_CONTRACT_MASTER -->    
    <update id = "createpaTCONTRACTMASTER" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_CONTRACT_MASTER as
					select b.*
  					from pa_policy_id a
 					inner join APP___PAS__DBUSER.T_CONTRACT_MASTER b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_CONTRACT_BUSI_PROD -->    
    <update id = "createpaTCONTRACTBUSIPROD" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_CONTRACT_BUSI_PROD as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_CONTRACT_PRODUCT -->    
    <update id = "paTCONTRACTPRODUCT" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_CONTRACT_PRODUCT as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_CONTRACT_PRODUCT b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_t_prem_arap -->    
    <update id = "patpremarap" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_arap as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.t_prem_arap b on a.policy_code = b.policy_code
 ]]>
    </update>
    
    <!--pa_T_PAYER -->    
    <update id = "paTPAYER" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_PAYER as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_PAYER b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_PAYER_ACCOUNT -->    
    <update id = "paTPAYERACCOUNT" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_PAYER_ACCOUNT as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_PAYER_ACCOUNT b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_CONTRACT_AGENT -->    
    <update id = "paTCONTRACTAGENT" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_CONTRACT_AGENT as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_CONTRACT_AGENT b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_POLICY_HOLDER -->    
    <update id = "paTPOLICYHOLDER" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_POLICY_HOLDER as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_POLICY_HOLDER b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_BENEFIT_INSURED -->    
    <update id = "paTBENEFITINSURED" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_BENEFIT_INSURED as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_BENEFIT_INSURED b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_INSURED_LIST -->    
    <update id = "paTINSUREDLIST" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_INSURED_LIST as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_INSURED_LIST b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_T_EXTRA_PREM -->    
    <update id = "paTEXTRAPREM" parameterType="java.util.Map">
        <![CDATA[ create table pa_T_EXTRA_PREM as
select b.*
  from pa_policy_id a
 inner join APP___PAS__DBUSER.T_EXTRA_PREM b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_delete_busi_item_id_01 -->    
    <update id = "padeletebusiitemid01" parameterType="java.util.Map">
        <![CDATA[ create table pa_delete_busi_item_id_01 as
select distinct b.busi_item_id
  from pa_policy_id a
 inner join pa_T_CONTRACT_BUSI_PROD b on a.policy_id = b.policy_id
 inner join APP___PAS__DBUSER.T_BUSINESS_PRODUCT c on b.busi_prd_id = c.business_prd_id and c.product_category1 = '20003'
 inner join pa_t_prem_arap d on a.policy_code = d.policy_code and d.fee_status = '00'
 ]]>
    </update>
    
    <!--pa_lock_policy_yes -->    
    <update id = "palockpolicyyes" parameterType="java.util.Map">
        <![CDATA[ create table pa_lock_policy_yes as 
select *
  from APP___PAS__DBUSER.t_lock_policy a
 where a.lock_service_id in
       (select b.lock_service_id
          from APP___PAS__DBUSER.t_lock_service_def b
         where b.service_group like '%06402%')
         ]]>
    </update>
    
<!-- pa_delete_policy_id_01 -->
<update id = "padeletepolicyid01" parameterType="java.util.Map">
        <![CDATA[ 
create table pa_delete_policy_id_01 as
select distinct a.policy_id
  from pa_policy_id a
 inner join pa_lock_policy_yes b on a.policy_id = b.policy_id
 ]]>
    </update>
    
    <!--pa_business_id_01 -->    
    <update id = "pabusinessid01" parameterType="java.util.Map">
        <![CDATA[ create table pa_business_id_01 as
select distinct a.policy_id,a.policy_code,c.busi_item_id,d.item_id
  from pa_policy_id a
 inner join pa_T_CONTRACT_MASTER b on a.policy_id = b.policy_id and b.liability_state = '1'
 inner join pa_T_CONTRACT_BUSI_PROD c on a.policy_id = c.policy_id and c.liability_state = '1'
 inner join pa_T_CONTRACT_PRODUCT d on c.busi_item_id = d.busi_item_id and d.liability_state = '1' 
                                                                 and (d.decision_code IS NULL OR d.decision_code NOT IN ('40', '50'))
 inner join APP___PAS__DBUSER.T_BUSINESS_PRODUCT e on c.busi_prd_id = e.business_prd_id
 where (d.paidup_date > (select a.extraction_due_date from pa_T_CONTRACT_EXTEND_01 a) ) 
    or (c.renew = '2' and c.renew_decision = '2' and c.maturity_date <= (select a.extraction_due_date from pa_T_CONTRACT_EXTEND_01 a) and c.busi_prod_code not in '00609000') 
    or (c.renew = '1' and c.renew_decision = '2' and c.maturity_date <= (select a.extraction_due_date from pa_T_CONTRACT_EXTEND_01 a) and c.busi_prod_code not in '00609000' 
                                                                                          and e.renew_option != 0)
 ]]>
    </update>
    
    <!--pa_business_id -->    
    <update id = "pabusinessid" parameterType="java.util.Map">
        <![CDATA[ create table pa_business_id as
select a.policy_id,a.policy_code,a.busi_item_id,a.item_id
  from pa_business_id_01 a
  left join pa_delete_busi_item_id_01 b on a.busi_item_id = b.busi_item_id
  left join pa_delete_policy_id_01 c on a.policy_id = c.policy_id
 where b.busi_item_id is null and c.policy_id is null
 ]]>
    </update>
    
    <!--pa_t_prem_01 -->    
    <update id = "patprem01" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_01 as
select t.item_id,t.policy_year,t.PAID_COUNT,t.DUE_TIME,list_id
  from (select a.item_id,
               b.list_id,
               b.policy_year, 
               b.policy_period PAID_COUNT, 
               b.Extraction_Due_Date DUE_TIME, 
               row_number() over(partition by a.item_id order by b.list_id desc) rn
          from pa_business_id a
         inner join pa_T_CONTRACT_EXTEND b
            on a.item_id = b.item_id) t where t.rn = 1
 ]]>
    </update>
    
    <!--pa_t_prem_02 -->    
    <update id = "patprem02" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_02 as
select t.policy_id,t.customer_id,t.PAY_MODE,t.PAY_LOCATION,t.BANK_CODE,t.BANK_ACCOUNT,t.BANK_USER_NAME
  from (
select a.policy_id,
       b.customer_id,                           
       c.pay_next PAY_MODE,                     
       c.pay_location PAY_LOCATION,             
       c.next_account_bank BANK_CODE,           
       c.next_account BANK_ACCOUNT,             
       c.next_account_name BANK_USER_NAME,      
       row_number() over(partition by a.policy_id order by b.list_id desc) rn
  from pa_business_id a
 inner join pa_T_PAYER b on a.policy_id = b.policy_id
 inner join pa_T_PAYER_ACCOUNT c on b.policy_id = c.policy_id and b.list_id = c.payer_id) t where t.rn = 1
 ]]>
    </update>
    
    <!--pa_t_prem_03 -->    
    <update id = "patprem03" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_03 as
select t.policy_id,t.channel_org_code
  from (
select a.policy_id,
       b.agent_organ_code channel_org_code,       
       row_number() over(partition by a.policy_id order by b.list_id asc) rn
  from pa_business_id a
 inner join pa_T_CONTRACT_AGENT b on a.policy_id = b.policy_id) t where t.rn = 1
  ]]>
    </update>


    <!--pa_t_prem_04 -->    
    <update id = "patprem04" parameterType="java.util.Map">
        <![CDATA[ 
create table pa_t_prem_04 as
select t.policy_id,
       t.AGENT_ORGAN_CODE,
       t.agent_code,
       t.agent_name,
       t.last_agent_code,
       t.last_agent_name,
       t.agent_start_date,
       t.AGENT_RELATION_TO_PH
  from (select a.policy_id,
               b.agent_organ_code AGENT_ORGAN_CODE, 
               b.agent_code, 
               b.agent_name, 
               b.last_agent_code, 
               b.last_agent_name, 
               b.agent_start_date, 
               b.RELATION_TO_PH AGENT_RELATION_TO_PH, 
               row_number() over(partition by a.policy_id order by b.list_id desc) rn
          from pa_business_id a
         inner join pa_T_CONTRACT_AGENT b
            on a.policy_id = b.policy_id
           and b.is_current_agent = '1') t
 where t.rn = 1
 ]]>
    </update>
    
    <!--pa_customer_id -->    
    <update id = "pacustomerid" parameterType="java.util.Map">
        <![CDATA[ create table pa_customer_id as
select b.customer_id customer_id
  from pa_business_id a
 inner join pa_T_POLICY_HOLDER b on a.policy_id = b.policy_id
 union 
select b.INSURED_ID customer_id
  from pa_business_id a
 inner join pa_T_BENEFIT_INSURED b on a.policy_id = b.policy_id and b.ORDER_ID = 1
 ]]>
    </update>
    
    <!--pa_t_customer -->    
    <update id = "patcustomer" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_customer as
select a.customer_id,b.customer_name,b.customer_gender,b.company_name,
       b.customer_cert_type,b.customer_certi_code,b.customer_id_code,
       nvl(b.mobile_tel,b.offen_use_tel) customer_TEL,b.customer_birthday
  from pa_customer_id a
 inner join  APP___PAS__DBUSER.t_customer b on a.customer_id = b.customer_id
 ]]>
    </update>
    
    <!--pa_t_prem_05 -->    
    <update id = "patprem05" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_05 as
select t.policy_id,
       t.HOLDER_ID,
       t.HOLDER_NAME,
       t.HOLDER_GENDER,
       t.HOLDER_COMPANY_NAME,
       t.HOLDER_CERT_TYPE,
       t.HOLDER_CERTI_CODE,
       t.HOLDER_ID_CODE,
       t.HOLDER_TEL
  from (select a.policy_id,
               c.customer_id HOLDER_ID, 
               c.customer_name HOLDER_NAME, 
               c.customer_gender HOLDER_GENDER, 
               c.company_name HOLDER_COMPANY_NAME, 
               c.customer_cert_type HOLDER_CERT_TYPE, 
               c.customer_certi_code HOLDER_CERTI_CODE, 
               c.customer_id_code HOLDER_ID_CODE, 
               c.customer_tel HOLDER_TEL, 
               row_number() over(partition by a.policy_id order by b.list_id desc) rn
          from pa_business_id a
         inner join pa_T_POLICY_HOLDER b
            on a.policy_id = b.policy_id
         inner join pa_t_customer c
            on b.customer_id = c.customer_id) t
 where t.rn = 1
 ]]>
    </update>
    
    <!--pa_t_prem_06 -->    
    <update id = "patprem06" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_06 as
select t.policy_id,
       t.INSURED_TEL,
       t.INSURED_RELATION_TO_PH,
       t.INSURED_BIRTHDAY,
       t.INSURED_ID,
       t.INSURED_NAME
  from (select a.policy_id,
               c.customer_tel INSURED_TEL,
               d.relation_to_ph INSURED_RELATION_TO_PH, 
               c.customer_birthday INSURED_BIRTHDAY,
               c.customer_id INSURED_ID, 
               c.customer_name INSURED_NAME, 
               row_number() over(partition by a.policy_id order by d.list_id desc) rn
          from pa_business_id a
         inner join pa_T_BENEFIT_INSURED b
            on a.policy_id = b.policy_id
         inner join pa_T_INSURED_LIST d
            on b.insured_id = d.list_id
         inner join pa_t_customer c
            on d.customer_id = c.customer_id
            where b.ORDER_ID = 1) t
 where t.rn = 1
 ]]>
    </update>
    
    <!--pa_t_prem_07 -->    
    <update id = "patprem07" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_07 as
select t.policy_id,t.POLICY_STATUS
  from (
select a.policy_id,
       case when c.dismissal_date is null then '1'else '0' end POLICY_STATUS， 
       row_number() over(partition by a.policy_id order by b.list_id desc) rn
  from pa_business_id a
 inner join pa_T_CONTRACT_AGENT b on a.policy_id = b.policy_id and b.is_current_agent = '1'
 inner join APP___PAS__DBUSER.t_Agent c on b.agent_code = c.agent_code) t where t.rn = 1
 ]]>
    </update>
    
    <!--pa_t_prem_100 -->    
    <update id = "patprem100" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_100 as
select a.policy_code,            
       t1.policy_year,           
       c.busi_prod_code,         
       e.product_name_std as BUSI_PROD_NAME,       
       case when c.master_busi_item_id is null then '1' else '0' end IS_RISK_MAIN,       
       a.item_id,                
       d.amount,                 
       '1' DUE_FEE_TYPE,         
       nvl(d.STD_PREM_AF,0) + nvl(d.additional_prem_af,0) FEE_AMOUNT, 
       t1.paid_count,            
       d.prem_freq,              
       t2.pay_mode,              
       t2.pay_location,          
       d.charge_period,          
       d.charge_year,            
       t1.due_time,              
       b.organ_code,             
       t3.channel_org_code,      
       t5.holder_id,             
       t5.holder_name,           
       t5.holder_gender,         
       t5.holder_company_name,   
       t5.holder_cert_type,      
       t5.holder_certi_code,     
       t5.holder_id_code,        
       t5.holder_tel,            
       t4.agent_organ_code,      
       t4.agent_code,            
       t4.agent_name,           
       t4.last_agent_code,      
       t4.last_agent_name,       
       t4.agent_start_date,      
       t4.agent_relation_to_ph,  
       t6.insured_tel,           
       t6.insured_relation_to_ph,
       t6.insured_birthday,      
       t6.insured_id,            
       t6.insured_name,          
       t2.bank_code,             
       t2.bank_account,          
       t2.bank_user_name,        
       '00' FEE_STATUS,          
       t7.policy_status,         
       sum(case when t1.due_time between d.waiver_start and d.waiver_end then '1' else '0' end) WAIVER_FLAG       
  from pa_business_id a
 inner join pa_T_CONTRACT_MASTER b on a.policy_id = b.policy_id
 inner join pa_T_CONTRACT_BUSI_PROD c on b.policy_id = c.policy_id
 inner join pa_T_CONTRACT_PRODUCT d on c.busi_item_id = d.busi_item_id
 inner join APP___PAS__DBUSER.T_BUSINESS_PRODUCT e on c.busi_prd_id = e.business_prd_id
  left join pa_t_prem_01 t1 on d.item_id = t1.item_id
  left join pa_t_prem_02 t2 on a.policy_id = t2.policy_id
  left join pa_t_prem_03 t3 on a.policy_id = t3.policy_id
  left join pa_t_prem_04 t4 on a.policy_id = t4.policy_id
  left join pa_t_prem_05 t5 on a.policy_id = t5.policy_id
  left join pa_t_prem_06 t6 on a.policy_id = t6.policy_id
  left join pa_t_prem_07 t7 on a.policy_id = t7.policy_id
  group by 
  a.policy_code,            
       t1.policy_year,           
       c.busi_prod_code,         
       e.product_name_std,       
        c.master_busi_item_id,     
       a.item_id,                
       d.amount,                 
       /*DUE_FEE_TYPE,*/         
      /* FEE_AMOUNT,*/ 
      d.STD_PREM_AF,
      d.additional_prem_af,
       t1.paid_count,            
       d.prem_freq,              
       t2.pay_mode,              
       t2.pay_location,          
       d.charge_period,          
       d.charge_year,            
       t1.due_time,              
       b.organ_code,             
       t3.channel_org_code,      
       t5.holder_id,             
       t5.holder_name,           
       t5.holder_gender,         
       t5.holder_company_name,   
       t5.holder_cert_type,      
       t5.holder_certi_code,     
       t5.holder_id_code,        
       t5.holder_tel,            
       t4.agent_organ_code,      
       t4.agent_code,            
       t4.agent_name,            
       t4.last_agent_code,       
       t4.last_agent_name,       
       t4.agent_start_date,      
       t4.agent_relation_to_ph,  
       t6.insured_tel,           
       t6.insured_relation_to_ph,
       t6.insured_birthday,      
       t6.insured_id,            
       t6.insured_name,          
       t2.bank_code,             
       t2.bank_account,          
       t2.bank_user_name,        
      /* FEE_STATUS,*/          
       t7.policy_status,         
       WAIVER_FLAG
  
 ]]>
    </update>
    
    <!--pa_t_prem_200 -->    
    <update id = "patprem200" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_200 as
select a.policy_code,            
       a.policy_year,           
       a.busi_prod_code,         
       a.BUSI_PROD_NAME,       
       a.IS_RISK_MAIN,        
       a.item_id,                
       a.AMOUNT,                 
       case when b.extra_type = '1' then '2' when b.extra_type = '2' then '3' end DUE_FEE_TYPE, 
       b.extra_prem FEE_AMOUNT,  
       a.paid_count,            
       a.prem_freq,              
       a.pay_mode,              
       a.pay_location,          
       a.charge_period,         
       a.charge_year,            
       a.due_time,              
       a.organ_code,             
       a.channel_org_code,      
       a.holder_id,             
       a.holder_name,           
       a.holder_gender,         
       a.holder_company_name,   
       a.holder_cert_type,      
       a.holder_certi_code,     
       a.holder_id_code,        
       a.holder_tel,            
       a.agent_organ_code,      
       a.agent_code,            
       a.agent_name,            
       a.last_agent_code,       
       a.last_agent_name,       
       a.agent_start_date,      
       a.agent_relation_to_ph,  
       a.insured_tel,           
       a.insured_relation_to_ph,
       a.insured_birthday,      
       a.insured_id,            
       a.insured_name,          
       a.bank_code,
       a.bank_account,          
       a.bank_user_name,        
       a.FEE_STATUS,            
       a.policy_status,         
       a.WAIVER_FLAG            
  from pa_t_prem_100 a
 inner join pa_T_EXTRA_PREM b on a.item_id = b.item_id and a.due_time between b.start_date and b.end_date
 ]]>
    </update>
    
    <!--pa_t_prem -->    
    <update id = "patprem" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem as
select policy_code,policy_year,busi_prod_code,busi_prod_name,is_risk_main,item_id,amount,due_fee_type,fee_amount,paid_count,
       prem_freq,pay_mode,pay_location,charge_period,charge_year,due_time,organ_code,channel_org_code,holder_id,holder_name,
       holder_gender,holder_company_name,holder_cert_type,holder_certi_code,holder_id_code,holder_tel,agent_organ_code,agent_code,
       agent_name,last_agent_code,last_agent_name,agent_start_date,agent_relation_to_ph,insured_tel,insured_relation_to_ph,
       insured_birthday,insured_id,insured_name,bank_code,bank_account,bank_user_name,fee_status,policy_status,waiver_flag
  from pa_t_prem_100
 union all
select policy_code,policy_year,busi_prod_code,busi_prod_name,is_risk_main,item_id,amount,due_fee_type,fee_amount,paid_count,
       prem_freq,pay_mode,pay_location,charge_period,charge_year,due_time,organ_code,channel_org_code,holder_id,holder_name,
       holder_gender,holder_company_name,holder_cert_type,holder_certi_code,holder_id_code,holder_tel,agent_organ_code,agent_code,
       agent_name,last_agent_code,last_agent_name,agent_start_date,agent_relation_to_ph,insured_tel,insured_relation_to_ph,
       insured_birthday,insured_id,insured_name,bank_code,bank_account,bank_user_name,fee_status,policy_status,waiver_flag
  from pa_t_prem_200 a
 ]]>
    </update>
    
    <!--pa_unit_number -->    
    <update id = "paunitnumber" parameterType="java.util.Map">
        <![CDATA[ create table pa_unit_number as 
       select a.biz_key_count ,a.biz_key_type,a.biz_key_name 
       from  APP___PAS__DBUSER.t_udmp_biz_key_table a 
       where a.biz_key_type = 'PAS_UNITNUMBER'
 ]]>
    </update>
    
    <!--updatepaunitnumber -->    
    <update id = "updatepaunitnumber" parameterType="java.util.Map">
        <![CDATA[ update  APP___PAS__DBUSER.t_udmp_biz_key_table w
   set w.biz_key_count =
       ((select to_char(a.biz_key_count) from pa_unit_number a) +
       (select count(*) from pa_T_CONTRACT_EXTEND) * 10)
 where w.biz_key_type = 'PAS_UNITNUMBER'
 ]]>
    </update>
    
    <!--pa_unit_number_sequence -->    
    <update id = "paunitnumbersequence" parameterType="java.util.Map">
        <![CDATA[ create sequence pa_unit_number_sequence
increment by 1 
start with 1000000001036001
nomaxvalue
nocycle
cache 10
 ]]>
    </update>
    
    <!--pa_t_prem_arap_pas_01 -->    
    <update id = "patpremarappas01" parameterType="java.util.Map">
        <![CDATA[  create table pa_t_prem_arap_pas_01 as
select (APP___PAS__DBUSER.S_PREM_ARAP.NEXTVAL) as list_id,        
       to_char(pa_unit_number_sequence.nextval) as unit_number,   
               a.policy_code as business_code, 
               a.policy_code as policy_code, 
               '0' as batch_no, 
               '003' as deriv_type, 
               b.apply_code as apply_code,
               b.branch_code as branch_code, 
               b.organ_code as organ_code, 
               b.organ_code as policy_organ_code, 
               t4.agent_code as agent_code, 
               b.policy_type as policy_type, 
               b.channel_type as channel_type, 
               b.channel_type as product_channel, 
               (select to_char(sysdate, 'yyyy-MM-dd') from dual) as busi_apply_date, 
               t1.due_time as validate_date, 
               (t1.due_time + 60) as pay_end_date, 
               t1.policy_year as policy_year, 
               d.prem_freq as prem_freq, 
               d.charge_year as charge_year, 
               t1.paid_count as paid_count, 
               t5.holder_id as holder_id, 
               t5.holder_name as holder_name, 
               t6.insured_id as insured_id, 
               t6.insured_name as insured_name, 
               (case when c.master_busi_item_id is null then 1 else 0 end) as is_risk_main,
               0 as is_item_main, 
               /* c.prd_pkg_code as gropu_code,           
               null as group_name,    */
               c.busi_prod_code as busi_prod_code, 
               e.product_name_sys as BUSI_PROD_NAME, 
               'G003010000' as fee_type,
               '**********' as withdraw_type, 
               t1.due_time as due_time, 
               '1' as arap_flag, 
               t2.customer_id as customer_id, 
               t2.bank_user_name as payee_name,
               tc.customer_cert_type   as certi_type,
               tc.customer_certi_code  as certi_code,
               tc.mobile_tel           as payee_phone, 
               /* null as certi_type,                     
               null as certi_type,                        
               null as payee_phone,   */
               t2.bank_code as bank_code, 
               t2.bank_account as bank_account, 
               t2.bank_user_name as bank_user_name, 
               0 as customer_account_flag, 
               1 as is_bank_account, 
               b.money_code as money_code, 
               nvl(d.STD_PREM_AF,0) + nvl(d.additional_prem_af,0) as FEE_AMOUNT, 
               t2.pay_mode as pay_mode, 
               '00' as fee_status, 
               1 as bookkeeping_flag, 
               '0' as refeflag, 
               '01' as posted, 
               b.policy_id as policy_id, 
               0 as is_split, 
               0 as is_send, 
               '20980' as operator_by,
               '20980' as insert_by,
               '20980' as update_by,
               sysdate as insert_time,
               sysdate as update_time,
               sysdate as insert_timestamp,
               sysdate as update_timestamp,
               '4003' as business_type,
               '01' as frozen_status,
               '20980' as frozen_status_by,
                t1.due_time as frozen_status_date,
                '1' as is_bank_text,
                e.product_abbr_name,
                d.coverage_period,
                d.coverage_year
          from pa_business_id a
         inner join pa_T_CONTRACT_MASTER b
            on a.policy_id = b.policy_id  
         inner join pa_T_CONTRACT_BUSI_PROD c
            on b.policy_id = c.policy_id
         inner join pa_T_CONTRACT_PRODUCT d
            on c.busi_item_id = d.busi_item_id
         inner join APP___PAS__DBUSER.T_BUSINESS_PRODUCT e
            on c.busi_prd_id = e.business_prd_id
          left join pa_t_prem_01 t1
            on d.item_id = t1.item_id
          left join pa_t_prem_02 t2
            on a.policy_id = t2.policy_id
          left join pa_t_prem_03 t3
            on a.policy_id = t3.policy_id
          left join pa_t_prem_04 t4
            on a.policy_id = t4.policy_id
          left join pa_t_prem_05 t5
            on a.policy_id = t5.policy_id
          left join pa_t_prem_06 t6
            on a.policy_id = t6.policy_id
          left join pa_t_prem_07 t7
            on a.policy_id = t7.policy_id
          left join APP___PAS__DBUSER.t_customer tc
            on t2.customer_id = tc.customer_id
            
 ]]>
    </update>
    
    <!--pa_t_prem_arap_pas -->    
    <update id = "patpremarappas" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_arap_pas as 
select         max(list_id) as list_id,       
               max(unit_number) as unit_number, 
               apply_code, 
               business_code, 
               policy_code, 
               batch_no, 
               deriv_type, 
               branch_code, 
               organ_code, 
               policy_organ_code,
               agent_code, 
               policy_type, 
               channel_type, 
               product_channel, 
               busi_apply_date, 
               validate_date, 
               pay_end_date, 
               policy_year, 
               prem_freq, 
               charge_year, 
               paid_count, 
               holder_id, 
               holder_name, 
               insured_id, 
               insured_name, 
               is_risk_main, 
               is_item_main, 
               /* c.prd_pkg_code as gropu_code,           
               null as group_name,  */
               busi_prod_code,
               BUSI_PROD_NAME, 
               fee_type, 
               withdraw_type,
               due_time, 
               arap_flag, 
               customer_id, 
               payee_name, 
               certi_type,                      
               certi_code,                       
               payee_phone,
               bank_code, 
               bank_account, 
               bank_user_name, 
               customer_account_flag, 
               is_bank_account, 
               money_code, 
               FEE_AMOUNT, 
               pay_mode,
               fee_status, 
               bookkeeping_flag, 
               refeflag, 
               posted, 
               policy_id, 
               is_split, 
               is_send, 
               operator_by,
               insert_by,
               update_by,
               insert_time,
               update_time,
               insert_timestamp,
               update_timestamp,
               business_type,
               frozen_status,
               frozen_status_by,
               frozen_status_date,
               is_bank_text,
               product_abbr_name,
               coverage_period,
               coverage_year
  from pa_t_prem_arap_pas_01
  where due_time is not null
 group by
               /* list_id,
               unit_number, */
               apply_code,
               business_code, 
               policy_code, 
               batch_no, 
               deriv_type, 
               branch_code, 
               organ_code, 
               policy_organ_code, 
               agent_code, 
               policy_type, 
               channel_type, 
               product_channel, 
               busi_apply_date, 
               validate_date, 
               pay_end_date, 
               policy_year, 
               prem_freq, 
               charge_year, 
               paid_count, 
               holder_id, 
               holder_name, 
               insured_id, 
               insured_name, 
               is_risk_main, 
               is_item_main,
               /* c.prd_pkg_code as gropu_code,          
               null as group_name,  */
               busi_prod_code, 
               BUSI_PROD_NAME, 
               fee_type, 
               withdraw_type, 
               due_time, 
               arap_flag, 
               customer_id, 
               payee_name, 
               certi_type,                        
               certi_code,                   
               payee_phone,  
               bank_code, 
               bank_account, 
               bank_user_name, 
               customer_account_flag, 
               is_bank_account, 
               money_code, 
               FEE_AMOUNT, 
               pay_mode, 
               fee_status, 
               bookkeeping_flag, 
               refeflag, 
               posted, 
               policy_id, 
               is_split, 
               is_send, 
               operator_by,
               insert_by,
               update_by,
               insert_time,
               update_time,
               insert_timestamp,
               update_timestamp,
               business_type,
               frozen_status,
               frozen_status_by,
               frozen_status_date,
               is_bank_text,
               product_abbr_name,
               coverage_period,
               coverage_year
  
 ]]>
    </update>
    
    <update id = "patpremNew" parameterType="java.util.Map">
        <![CDATA[ create table pa_t_prem_pas as 
select  (APP___PAS__DBUSER.S_PREM__LIST_ID.NEXTVAL) as list_id,
		p.unit_number,
      	a.policy_code,
      	a.policy_year,
      	a.busi_prod_code,
      	a.busi_prod_name,
      	a.is_risk_main,
      	a.item_id,
      	a.amount,
      	a.due_fee_type,
      	a.fee_amount,
      	a.paid_count,
        a.prem_freq,
        a.pay_mode,
        a.pay_location,
        a.charge_period,
        a.charge_year,
        a.due_time,
        a.organ_code,
        a.channel_org_code,
        a.holder_id,
        a.holder_name,
        a.holder_gender,
        a.holder_company_name,a.holder_cert_type,
        a.holder_certi_code,a.holder_id_code,
        a.holder_tel,a.agent_organ_code,
        a.agent_code,
        a.agent_name,a.last_agent_code,
        a.last_agent_name,a.agent_start_date,
        a.agent_relation_to_ph,a.insured_tel,
        a.insured_relation_to_ph,
        a.insured_birthday,a.insured_id,
        a.insured_name,a.bank_code,
        a.bank_account,a.bank_user_name,
        a.fee_status,a.policy_status,
        a.waiver_flag,
        '20980' as insert_by,
        '20980' as update_by
  from pa_t_prem a 
  left join pa_t_prem_arap_pas p 
  on a.policy_code = p.policy_code 
  where a.due_time = p.due_time    
     ]]>
    </update>
    
    <update id = "insertTprem" parameterType="java.util.Map">
        <![CDATA[ insert into  APP___PAS__DBUSER.t_prem
        (
        list_id,
        unit_number,
      	policy_code,
      	policy_year,
      	busi_prod_code,
      	busi_prod_name,
      	is_risk_main,
      	item_id,
      	amount,
      	due_fee_type,
      	fee_amount,
      	paid_count,
        prem_freq,
        pay_mode,
        pay_location,
        charge_period,
        charge_year,
        due_time,
        organ_code,
        channel_org_code,
        holder_id,
        holder_name,
        holder_gender,
        holder_company_name,holder_cert_type,
        holder_certi_code,holder_id_code,
        holder_tel,agent_organ_code,
        agent_code,
        agent_name,last_agent_code,
        last_agent_name,agent_start_date,
        agent_relation_to_ph,insured_tel,
        insured_relation_to_ph,
        insured_birthday,insured_id,
        insured_name,bank_code,
        bank_account,bank_user_name,
        fee_status,policy_status,
        waiver_flag,
        insert_by,
        update_by,
        insert_time,
        INSERT_TIMESTAMP,
        update_time,
        UPDATE_TIMESTAMP
        )
      select
        list_id,
        unit_number,
      	policy_code,
      	policy_year,
      	busi_prod_code,
      	busi_prod_name,
      	is_risk_main,
      	item_id,
      	amount,
      	due_fee_type,
      	fee_amount,
      	paid_count,
        prem_freq,
        pay_mode,
        pay_location,
        charge_period,
        charge_year,
        due_time,
        organ_code,
        channel_org_code,
        holder_id,
        holder_name,
        holder_gender,
        holder_company_name,holder_cert_type,
        holder_certi_code,holder_id_code,
        holder_tel,agent_organ_code,
        agent_code,
        agent_name,last_agent_code,
        last_agent_name,agent_start_date,
        agent_relation_to_ph,insured_tel,
        insured_relation_to_ph,
        insured_birthday,insured_id,
        insured_name,bank_code,
        bank_account,bank_user_name,
        fee_status,policy_status,
        waiver_flag,
         insert_by,
        update_by,
        SYSDATE as insert_time,
        CURRENT_TIMESTAMP as INSERT_TIMESTAMP,
        SYSDATE as update_time,
        CURRENT_TIMESTAMP as UPDATE_TIMESTAMP
    from  pa_t_prem_pas
    
     ]]>
    </update>
    <!--t_prem_arap -->    
    <update id = "insertTpremArapNew" parameterType="java.util.Map">
        <![CDATA[insert into  APP___PAS__DBUSER.t_prem_arap 
  (    list_id,                      
       unit_number, 
       apply_code,                
       business_code,            
       policy_code,              
       batch_no,                           
       deriv_type,                       
       branch_code,              
       organ_code,                
       policy_organ_code,         
       agent_code,               
       policy_type,              
       channel_type,            
       product_channel,         
       /* busi_apppa_date,*/
       busi_apply_date,
       validate_date,         
       pay_end_date,       
       policy_year,            
       prem_freq,                 
       charge_year,             
       paid_count,              
       holder_id,                
       holder_name,            
       insured_id,              
       insured_name,          
       is_risk_main,    
       is_item_main,                        
      /* c.prd_pkg_code as gropu_code,             
       null as group_name, */
       busi_prod_code,       
       BUSI_PROD_NAME,     
       fee_type,                 
       withdraw_type,            
       due_time,                  
       arap_flag,                         
       customer_id,            
       payee_name,          
       certi_type,                         
       certi_code,                         
       payee_phone,
       bank_code,                
       bank_account,          
       bank_user_name,      
       customer_account_flag,               
       is_bank_account,                     
       money_code,               
       FEE_AMOUNT, 
       pay_mode,                  
       fee_status,                       
       bookkeeping_flag,                    
       refeflag,                         
       posted,                           
       
       is_split,                            
       is_send,
       operator_by,
       insert_by,
       update_by,
       insert_time,
       update_time,
       insert_timestamp,
       update_timestamp,
       business_type,
       frozen_status,
       frozen_status_by,
       frozen_status_date,
       is_bank_text,
       product_abbr_name,
      coverage_period,
      coverage_year                           
       )
  
  select  
       list_id,                      
       unit_number, 
       apply_code,                
       business_code,            
       policy_code,              
       batch_no,                           
       deriv_type,                       
       branch_code,              
       organ_code,                
       policy_organ_code,         
       agent_code,               
       policy_type,              
       channel_type,            
       product_channel,         
       to_date(busi_apply_date,'yyyy-MM-dd'),
       /*busi_apply_date,*/
       validate_date,         
       pay_end_date,       
       policy_year,            
       prem_freq,                 
       charge_year,             
       paid_count,              
       holder_id,                
       holder_name,            
       insured_id,              
       insured_name,          
       is_risk_main,    
       is_item_main,                        
      /* c.prd_pkg_code as gropu_code,             
       null as group_name, */
       busi_prod_code,       
       BUSI_PROD_NAME,     
       fee_type,                 
       withdraw_type,            
       due_time,                  
       arap_flag,                         
       customer_id,            
       payee_name,          
       certi_type,                         
       certi_code,                         
       payee_phone,
       bank_code,                
       bank_account,          
       bank_user_name,      
       customer_account_flag,               
       is_bank_account,                     
       money_code,               
       FEE_AMOUNT, 
       pay_mode,                  
       fee_status,                       
       bookkeeping_flag,                    
       refeflag,                         
       posted,                           
       is_split,                            
       is_send,
       operator_by,
       insert_by,
       update_by,
       insert_time,
       update_time,
       insert_timestamp,
       update_timestamp,
       business_type,
       frozen_status,
       frozen_status_by,
       frozen_status_date,
       is_bank_text,
       product_abbr_name,
       coverage_period,
      coverage_year                                   
  from pa_t_prem_arap_pas
 ]]>
    </update>
    
    <!--t_prem_arap -->    
    <update id = "insertTpremArapForCap" parameterType="java.util.Map">
        <![CDATA[ insert into  APP___CAP__DBUSER.t_prem_arap 
  (    list_id,                      
       unit_number, 
       apply_code,                
       business_code,            
       policy_code,              
       batch_no,                           
       deriv_type,                       
       branch_code,              
       organ_code,                
       policy_organ_code,         
       agent_code,               
       policy_type,              
       channel_type,            
       product_channel,         
       /* busi_apppa_date,*/
       busi_apply_date,
       validate_date,         
       pay_end_date,       
       policy_year,            
       prem_freq,                 
       charge_year,             
       paid_count,              
       holder_id,                
       holder_name,            
       insured_id,              
       insured_name,          
       is_risk_main,    
       is_item_main,                        
      /* c.prd_pkg_code as gropu_code,             
       null as group_name, */
       busi_prod_code,       
       BUSI_PROD_NAME,     
       fee_type,                 
       withdraw_type,            
       due_time,                  
       arap_flag,                         
       customer_id,            
       payee_name,          
       certi_type,                         
       certi_code,                         
       payee_phone,
       bank_code,                
       bank_account,          
       bank_user_name,      
       customer_account_flag,               
       is_bank_account,                     
       money_code,               
       FEE_AMOUNT, 
       pay_mode,                  
       fee_status,                       
       bookkeeping_flag,                    
       refeflag,                         
       posted,                           
       
       is_split,                            
       is_send,
       operator_by,
       insert_by,
       update_by,
       insert_time,
       update_time,
       insert_timestamp,
       update_timestamp,
       business_type,
       frozen_status,
       frozen_status_by,
       frozen_status_date,
       is_bank_text,
       product_abbr_name,
      coverage_period,
      coverage_year                          
       )
  
  select  
       (APP___CAP__DBUSER.S_PREM_ARAP.NEXTVAL),                      
      unit_number, 
       apply_code,                
       business_code,            
       policy_code,              
       batch_no,                           
       deriv_type,                       
       branch_code,              
       organ_code,                
       policy_organ_code,         
       agent_code,               
       policy_type,              
       channel_type,            
       product_channel,         
       to_date(busi_apply_date,'yyyy-MM-dd'),
       /*busi_apply_date,*/
       validate_date,         
       pay_end_date,       
       policy_year,            
       prem_freq,                 
       charge_year,             
       paid_count,              
       holder_id,                
       holder_name,            
       insured_id,              
       insured_name,          
       is_risk_main,    
       is_item_main,                        
      /* c.prd_pkg_code as gropu_code,             
       null as group_name, */
       busi_prod_code,       
       BUSI_PROD_NAME,     
       fee_type,                 
       withdraw_type,            
       due_time,                  
       arap_flag,                         
       customer_id,            
       payee_name,          
       certi_type,                         
       certi_code,                         
       payee_phone,
       bank_code,                
       bank_account,          
       bank_user_name,      
       customer_account_flag,               
       is_bank_account,                     
       money_code,               
       FEE_AMOUNT, 
       pay_mode,                  
       fee_status,                       
       bookkeeping_flag,                    
       refeflag,                         
       posted,                           
       is_split,                            
       is_send,
       operator_by,
       insert_by,
       update_by,
       insert_time,
       update_time,
       insert_timestamp,
       update_timestamp,
       business_type,
       frozen_status,
       frozen_status_by,
       frozen_status_date,
       is_bank_text,
       product_abbr_name,
       coverage_period,
      coverage_year                                 
  from pa_t_prem_arap_pas
 ]]>
    </update>
    
    <!--pa_extraction_due_date -->    
    <update id = "paextractionduedate" parameterType="java.util.Map">
        <![CDATA[ create table pa_extraction_due_date as 
select t.list_id, t.Extraction_Due_Date
  from (select distinct a.list_id,
                        (case
                          when b.prem_freq = 2 then
                           add_months(b.due_time, 1)
                          when b.prem_freq = 3 then
                           add_months(b.due_time, 3)
                          when b.prem_freq = 4 then
                           add_months(b.due_time, 6)
                          when b.prem_freq = 5 then
                           add_months(b.due_time, 12)
                          else
                           add_months(b.due_time, 12)
                        end) as Extraction_Due_Date
          from pa_t_prem_01 a, pa_t_prem_100 b
         where a.item_id = b.item_id) t
         where t.Extraction_Due_Date is not null
 ]]>
    </update>
    
    <update id = "paextractionduedateindex" parameterType="java.util.Map">
        <![CDATA[ 
        create index idx_a on pa_extraction_due_date (list_id)
    
    ]]>
    </update>
    <!--t_contract_extend -->    
    <update id = "updatetcontractextendnew" parameterType="java.util.Map">
        <![CDATA[ update  APP___PAS__DBUSER.t_contract_extend a
   set a.extraction_due_date =
       (select distinct b.extraction_due_date
          from pa_extraction_due_date b
         where a.list_id = b.list_id and rownum = 1)
 where a.list_id in (select distinct b.list_id from pa_extraction_due_date b)
 ]]>
    </update>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <update id="test2" 
        parameterType="String">
        <![CDATA[  
            create table ly_T_CONTRACT_EXTEND
            as
        	select a.*
        	from  APP___PAS__DBUSER.T_CONTRACT_EXTEND a where a.policy_id = '1'
        ]]>
    </update>
    
    <!-- 自动抽档-按启动参数查询符合条件的保单条数 -->
    <select id="queryRenewExtraCount11975" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[  
            SELECT COUNT(1) FROM
            (SELECT tcm.policy_id
            FROM 
            APP___PAS__DBUSER.t_contract_master tcm, 
            APP___PAS__DBUSER.t_contract_extend tce,  
            APP___PAS__DBUSER.t_contract_agent tca 
            WHERE tcm.liability_state = '1' 
            AND tcm.organ_code in (
                    select t.organ_code
                    from APP___PAS__DBUSER.t_udmp_org_rel t
                    start with t.organ_code = #{organ_code}
                    connect by prior t.organ_code = t.uporgan_code
                    GROUP BY organ_code
                ) 
            AND tce.extraction_due_date <= #{extraction_due_date} 
            ]]>
        <!--核保决定判断条件先屏蔽 -->
        <!-- AND tcm.policy_id NOT IN ( SELECT policy_id FROM t_contract_busi_prod 
            WHERE decision_code IS NULL OR decision_code = '' GROUP BY policy_id ) -->

        <!-- 自动抽档-按启动参数查询符合条件的保单条数 -->
        <include refid="optionalQueryCriteria" />
        <![CDATA[ 
            AND tcm.policy_id = tce.policy_id and tce.policy_id = tca.policy_id 
            GROUP BY tcm.policy_id
            ) t
        ]]>
    </select>
    
    
    <!-- 自动抽档-按启动参数查询符合条件的保单 -->
    <select id="queryRenewExtraPolicy" resultType="java.util.Map"
        parameterType="java.util.Map">
        <!-- <include refid="queryForPageStart" /> -->
        <![CDATA[ 
            SELECT tcm.policy_id FROM 
            APP___PAS__DBUSER.t_contract_master tcm, 
            APP___PAS__DBUSER.t_contract_extend tce,  
            APP___PAS__DBUSER.t_contract_agent tca 
            WHERE tcm.liability_state = '1' 
            AND tcm.organ_code in (
                    select t.organ_code
                    from APP___PAS__DBUSER.t_udmp_org_rel t
                    start with t.organ_code = #{organ_code}
                    connect by prior t.organ_code = t.uporgan_code
                    GROUP BY organ_code
                ) 
            AND tce.extraction_due_date <= #{extraction_due_date} 
            ]]>
        <!--核保决定判断条件先屏蔽 -->
        <!-- AND tcm.policy_id NOT IN ( SELECT policy_id FROM t_contract_busi_prod 
            WHERE decision_code IS NULL OR decision_code = '' GROUP BY policy_id ) -->

        <include refid="optionalQueryCriteria" />
        <![CDATA[ 
            AND tcm.policy_id = tce.policy_id and tce.policy_id = tca.policy_id 
            GROUP BY tcm.policy_id
            ORDER BY tcm.policy_id
        ]]>
        <!-- <include refid="queryForPageEnd" /> -->
    </select>
    
    <!-- 可选查询条件 -->
    <sql id="optionalQueryCriteria">
        <if
            test="channel_type != null and channel_type != '' and channel_type 
		!='null'">
			<![CDATA[ AND tcm.channel_type = #{channel_type} ]]>
        </if>
        <if
            test="prem_freq != null and prem_freq != '' and prem_freq !='null'">
			<![CDATA[ AND tcm.policy_id IN (
						SELECT policy_id FROM 
						APP___PAS__DBUSER.t_contract_product 
						WHERE prem_freq = #{prem_freq} 
						GROUP BY policy_id) 
			]]>
        </if>
        <if
            test="agent_code != null and agent_code != '' and agent_code !='null'">
			<![CDATA[ AND tca.policy_id = tcm.policy_id 
					AND tca.agent_code = #{agent_code} 
			]]>
        </if>

        <if test="busi_item_type != null and busi_item_type != ''">
			<![CDATA[  ]]>
        </if>

        <if
            test="policy_code != null and policy_code != '' and policy_code !='null'">
			<![CDATA[ AND tcm.policy_code = #{policy_code} ]]>
        </if>

    </sql>
    
</mapper>