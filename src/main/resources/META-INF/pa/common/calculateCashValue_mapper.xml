<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.common.dao.ICalculateCashValueDao">
	<sql id="PA_premArapWhereCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" product_code  != null "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" start_date  != null "><![CDATA[ AND to_char(A.finish_time,'yyyy-MM-dd') >= to_char(#{start_date},'yyyy-MM-dd') ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND to_char(A.finish_time,'yyyy-MM-dd') <= to_char(#{end_date},'yyyy-MM-dd') ]]></if> 
	</sql>
	<!-- 根据责任组ID查找被保人客户信息 -->
	<select id="findInsuredListCusInfoByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select *
	    from (select *
	            from APP___PAS__DBUSER.t_insured_list til
	           where til.list_id = (select tbi.insured_id
	                                  from APP___PAS__DBUSER.t_benefit_insured tbi
	                                 where tbi.busi_item_id =
	                                       (select busi_item_id
	                                          from APP___PAS__DBUSER.t_contract_product
	                                         where item_id = #{item_id})
	                                   and tbi.order_id = 1)) a
	    left join APP___PAS__DBUSER.t_customer tc
	      on tc.customer_id = a.customer_id
		]]>
	</select>
	
	<!-- 查询时间段内，续期缴费次数 -->
	<select id="findCurYearPayCountsByTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select count(1) as count
		  from (select t.fee_type, t.deriv_type, t.*
		          from APP___PAS__DBUSER.t_prem_arap t
		         where 1 = 1
		          and ((t.fee_type = 'G003020100') or (t.fee_type = 'G003010000') or (t.fee_type = 'T004010000')) 
		           and t.deriv_type = '003'
		           and t.policy_code = #{policy_code}
		           and ((t.fee_status = '01') or (t.fee_status = '19'))
		           and t.busi_prod_code in
		               (select tcbp.busi_prod_code
		                  from APP___PAS__DBUSER.t_contract_busi_prod tcbp
		                 where tcbp.busi_item_id =
		                       (select t.busi_item_id
		                          from APP___PAS__DBUSER.t_contract_product t
		                         where t.item_id = #{item_id}))
		     ]]>		     
		    <if test=" start_date  != null "><![CDATA[ and t.due_time >= #{start_date} ]]></if>                     
		    <if test=" end_date  != null "><![CDATA[  and t.due_time <#{end_date} ]]></if>      
		    <![CDATA[   )  ]]> 
	</select>
	
	<!-- 查询当期缴费信息 -->
	<select id="curPremiumPaid" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 		 
		 	SELECT COUNT(t.unit_number)
          FROM (SELECT T.FEE_TYPE, T.DERIV_TYPE,T.*
                  FROM APP___PAS__DBUSER.T_PREM_ARAP T
                 WHERE 1 < 1000
                  AND ((T.FEE_TYPE = 'G003020100') OR (T.FEE_TYPE = 'G003010000') OR (T.FEE_TYPE = 'T003010000'))
                   AND (( T.FEE_STATUS = '01') OR (T.FEE_STATUS = '19') OR (T.FEE_STATUS = '16'))]]>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[AND T.POLICY_CODE = #{policy_code} ]]></if>		           
		<if test=" business_code  != null and business_code !='' "><![CDATA[AND T.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" due_time  != null and due_time !='' "><![CDATA[AND T.DUE_TIME = #{due_time} ]]></if>		           
		<![CDATA[ ) t ]]>           			          			    		   
	</select>
	
</mapper>