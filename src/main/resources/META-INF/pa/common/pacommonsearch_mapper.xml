<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.common.dao.IPACommonSearchDao">
	<sql id="PA_paAcceptChangeDao">
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" accept_status != null and accept_status != ''  "><![CDATA[ AND A.ACCEPT_STATUS = #{accept_status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
		<!-- 查询责任组的被保人年龄 -->
	<select id="findInsuredAgeByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select til.insured_age
			  from APP___PAS__DBUSER.t_insured_list til
			 where til.list_id = (select tbi.insured_id
			                        from APP___PAS__DBUSER.t_benefit_insured tbi
			                       where tbi.order_id = 1
			                         and tbi.busi_item_id = #{busi_item_id}
			                             )]]>
	</select>
	
	<select id="findPagecfgElementValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			 select tpev.code2 as code_name,tpev.code
			   from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT   tppe,
			        APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA tppcr,
			        APP___PAS__DBUSER.t_product_life          tpl,
			        APP___PAS__DBUSER.t_pagecfg_unit          tpu,
			        APP___PAS__DBUSER.t_pagecfg_element_value tpev
			  where tpl.product_id = tppcr.product_id 
			    and tppcr.relation_id = tppe.relation_id
			    and tppe.unit_code = tpu.unit_code
			    and tppe.default_value=tpev.code
			    and tppe.list_value = tpev.list_value_code
			    and tppe.unit_code =  #{unit_code} 
			    and tpl.internal_id = #{internal_id}
     ]]>
     <if test=" code != null and code != ''  "><![CDATA[ and tpev.code = #{code} ]]></if>
     <if test=" coverage_type != null and coverage_type != ''  "><![CDATA[ and tpev.code2 like '%'||#{coverage_type}||'%' ]]></if>
	</select>
	
	<!-- modify by huangcc 2016-12-14 -->
	<select id="findCoverageValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 		select tpev.code2 as code_name, tpev.code
				  from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT   tppe,
				       APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA tppcr,
				       APP___PAS__DBUSER.t_product_life          tpl,
				       APP___PAS__DBUSER.t_pagecfg_unit          tpu,
				       APP___PAS__DBUSER.t_pagecfg_element_value tpev
				 where tpl.product_id = tppcr.product_id
				   and tppcr.relation_id = tppe.relation_id
				   and tppe.unit_code = tpu.unit_code
				   and tppe.list_value = tpev.list_value_code
				   and tppe.unit_code = '015'
				   and tpl.internal_id = #{internal_id}
				   and tpev.code in (
                     select *
                       from (select (case
                                       when tpev.code2 = '终身' then 'W'
                                       when tpev.code2 like '%年%' then 'Y'
                                       when tpev.code2 like '%岁%' then 'A'
                                       ELSE
                                        tpev.code2
                                     end) as code
                                from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT   tppe,
                                     APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA tppcr,
                                     APP___PAS__DBUSER.t_product_life          tpl,
                                     APP___PAS__DBUSER.t_pagecfg_unit          tpu,
                                     APP___PAS__DBUSER.t_pagecfg_element_value tpev
                               where tpl.product_id = tppcr.product_id
                                 and tppcr.relation_id = tppe.relation_id
                                 and tppe.unit_code = tpu.unit_code
                                 and tppe.list_value = tpev.list_value_code
                                 and tppe.default_value=tpev.code
                                 and tppe.unit_code = '006'
                                 and tpl.internal_id = #{internal_id}) a
                      where 1 = 1
     ]]>
     <if test=" code != null and code != ''  "><![CDATA[ and tpev.code = #{code} ]]></if>
	<![CDATA[)]]>
	</select>

</mapper>