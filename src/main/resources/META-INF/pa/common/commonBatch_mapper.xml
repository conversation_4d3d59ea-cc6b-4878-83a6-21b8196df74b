<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.common.dao">
	
	<!--查询数据库临时表是否存在 -->
	<select id="PA_findTableCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[		
			 SELECT count(1) FROM all_tables a WHERE a.table_name= #{table_name}
		]]>			
	</select>
	
	<!--删除指定表名的临时表-->
	<update id="dropTable" parameterType="java.util.Map">
        <![CDATA[
        	DROP TABLE ${table_name}
        ]]>
	</update>
</mapper>

