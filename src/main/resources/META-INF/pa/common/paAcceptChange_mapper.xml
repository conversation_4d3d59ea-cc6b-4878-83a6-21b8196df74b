<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.common.dao.IPaAcceptChangeDao">
	<sql id="PA_paAcceptChangeDao">
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" accept_status != null and accept_status != ''  "><![CDATA[ AND A.ACCEPT_STATUS = #{accept_status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<!-- 是否发生某个保全项 -->
	<select id="findAllChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT *
  			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A, APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
 			WHERE A.ACCEPT_ID = B.ACCEPT_ID
   				AND A.SERVICE_CODE = B.SERVICE_CODE]]>
   		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" accept_status != null and accept_status != ''  "><![CDATA[ AND A.ACCEPT_STATUS = #{accept_status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	<!-- 查询所有数据通过受理号查询 -->
	<select id="findAllChangeByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.SERVICE_CODE,A.ACCEPT_CODE,A.ACCEPT_STATUS,A.ACCEPT_ID
  			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
 			WHERE 1 = 1
 		]]>
   		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</select>
</mapper>