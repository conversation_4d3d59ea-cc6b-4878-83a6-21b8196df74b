<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.common.dao.IFindCodeValueDao">
	<sql id="PA_premArapWhereCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" product_code  != null "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" start_date  != null "><![CDATA[ AND to_char(A.finish_time,'yyyy-MM-dd') >= to_char(#{start_date},'yyyy-MM-dd') ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND to_char(A.finish_time,'yyyy-MM-dd') <= to_char(#{end_date},'yyyy-MM-dd') ]]></if>
	</sql>
	<!-- 查询所有交易金额累计合计 -->
	<select id="findValueByCode" resultType="java.util.Map" parameterType="java.util.Map">
<!-- 		${sql} -->
		<![CDATA[ 
		select ${value_name}  as value from ${table_name} where ${code_name} = #{code}
		]]>
	</select>
	
	<select id="findTableInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select t.id_column as code_name ,t.desc_column as value_name from APP___PAS__DBUSER.t_udmp_code t where upper(t.table_name) = upper(#{table_name})
		]]>
	</select>
</mapper>