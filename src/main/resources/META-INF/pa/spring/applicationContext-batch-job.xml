<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:jaxws="http://cxf.apache.org/jaxws" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">


	<!-- 
	<bean id="agentServerInit" class="com.nci.udmp.component.batch.core.ServerInit">
		<property name="initFlag" value="true"/>
		<property name="booters">
			<list>
				<ref bean="agentLoaderFactory"/>
			</list>
		</property>
	</bean> 
 -->
	<!-- 跨月自动抽档批处理 -->
	<bean class = "com.nci.tunan.pa.batch.automaticexrkuayue.RenewalAutomaticExtraKuayueJob" id = "PA_RenewalAutomaticExtraKuayueJob" >
		<property name="extraKyService" ref="PA_RenewAutomaticExtraKyService"></property>	
	</bean>
		<bean class="com.nci.tunan.pa.batch.automaticexrkuayue.service.impl.RenewAutomaticExtraKyServiceImpl" id = "PA_RenewAutomaticExtraKyService" >
			<property name="contractBusiProdDao" ref = "PA_contractBusiProdDao"></property>
			<property name="policyHolderDao" ref = "PA_policyHolderDao" ></property>	
			<property name="autoextraServiece" ref = "PA_autoextraServiece"></property>
			<property name="contractMarketActDao" ref = "PA_contractMarketActDao"></property>
			<property name="extraPremDao" ref = "PA_extraPremDao" ></property>
			<property name="automaticExtraKyDao" ref = "PA_renewAutomaticExtraKyDaoImpl"></property>
			<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	<bean class = "com.nci.tunan.pa.batch.automaticexrkuayue.dao.impl.RenewAutomaticExtraKyDaoImpl" id = "PA_renewAutomaticExtraKyDaoImpl" parent = "baseDao" ></bean>
	
	<!-- 批量孤儿单 add by lvkai-->
	<bean class="com.nci.tunan.pa.batch.orphanpolicy.BatchOrphanPolicyListDealJob" id="PA_batchOrphanPolicyListDealJob" scope="prototype">
		<property name="orphanPolicyDealService" ref="PA_orphanPolicyDealService"/>		
	</bean>
	
	<!-- 孤儿单分配结果读取保存 add by lvkai-->
	<bean class="com.nci.tunan.pa.batch.orphanpolicy.OrphanPolicyListDealJob" id="PA_orphanPolicyListDealJob" scope="prototype">
		<property name="orphanPolicyDealService" ref="PA_orphanPolicyDealService"/>		
	</bean>
	<!-- 内部转岗和人员异动批处理 -->
	<bean class="com.nci.tunan.pa.batch.Internaltransfer.InternalTransferJob" id="PA_internalTransferJob" scope="prototype">
		<property name="orphanPolicyDealService" ref="PA_orphanPolicyDealService"/>		
		<property name="orphanFtpServerConfig" ref="PA_orphanFtpServerConfig"/>
	</bean>

	<!-- 营销员放弃保单信息同步批处理 -->
	<bean class="com.nci.tunan.pa.batch.marketersabandonpolicy.marketersAbandonPolicyJob" id="PA_marketersAbandonPolicyJob" scope="prototype">
		<property name="marketersAbandonPolicyService" ref="PA_marketersAbandonPolicyService"/>			
		<property name="marketersabandonftpServerConfig" ref="PA_marketersabandonftpServerConfig"/>
	</bean>
	<!-- 渠道系统向新核心同步保单信息变更需求批处理 -->
	<bean class="com.nci.tunan.pa.batch.synchronizationPolicyCodeInfo.SynchronizationPolicyCodeInfoJob" id="PA_synchronizationPolicyCodeInfoJob" scope="prototype">
		<property name="synchronizationPolicyCodeInfoService" ref="PA_synchronizationPolicyCodeInfoService"/>			
		<!-- <property name="synchronizationPolicyCodeInfoftpServerConfig" ref="PA_synchronizationPolicyCodeInfoftpServerConfig"/> -->
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.orphanpolicy.service.impl.OrphanPolicyDealServiceImpl" id="PA_orphanPolicyDealService" scope="prototype">
		<property name="orphanPolicyNoticeService" ref="PA_orphanPolicyNoticeService"/>	
		<property name="orphanFtpServerConfig" ref="PA_orphanFtpServerConfig"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>		
		<property name="agentDao" ref="PA_agentDaoTunan"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.orphanpolicy.service.impl.OrphanParameterServiceImpl" id="PA_orphanParameterService" scope="prototype">
        <property name="orphanPolicyDao" ref="PA_orphanPolicyDao" />
        <property name="orphanParameterDao" ref="PA_orphanParameterDao" />
  		 <property name="orphanFtpServerConfig" ref="PA_orphanFtpServerConfig"/>
  		 <property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
  		 <property name="csContractMasterDao" ref="PA_csContractMasterDao"/>
  		 <property name="commonBatchDao" ref="PA_commonBatchDao" />
  		 <property name="addressDao" ref="PA_addressDao"/>  
	     <property name="orphanParameterDetailDao" ref="PA_orphanParameterDetailDao"/>
	     <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	
	<!-- 个险三次催缴-->
	<bean class="com.nci.tunan.pa.batch.sendnote.RenewalAdvanceByChannelJob" id="PA_renewalAdvanceByChannelJob" scope="prototype">
		<property name="renewalMissionWarnService" ref="PA_renewalMissionWarnService"/>		
	</bean>
	
	<!-- 续期保单催缴批处理-->
	<bean class="com.nci.tunan.pa.batch.sendnote.RenewPolicyChargeJob" id="PA_renewPolicyChargeJob" scope="prototype">
		<property name="renewalMissionWarnService" ref="PA_renewalMissionWarnService"/>		
	</bean>
	
	<!-- 续期自动抽档批作业 -->
	<bean class="com.nci.tunan.pa.batch.automaticextra.RenewalAutomaticExtraJob" id="PA_renewalAutomaticExtra" scope="prototype">
		<property name="extraService" ref="PA_extraService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticextra.service.impl.RenewAutomaticExtraServiceImpl" id="PA_extraService">
		<property name="automaticExtraDao" ref="PA_automaticExtraDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>	
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="salesOrganDao" ref="PA_salesOrganDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="withdrawTypeService" ref="WithdrawTypeService"/>
        <property name="iRenewalBillingDao" ref="PA_iRenewalBillingDao"/>
        <property name="iNoticeService" ref="PA_noticeService"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
        <property name="contractMasterDao" ref="PA_contractMasterDao"/>
        <property name="extraPremDao" ref="PA_extraPremDao"/>
        <property name="renewCollectionService" ref="PA_renewCollectionService"/>
        <property name="autoextraServiece" ref="PA_autoextraServiece"/>
        <property name="contractMarketActDao" ref="PA_contractMarketActDao"/> 
        <property name="extendGracePeriodDao" ref="PA_extendGracePeriodDao"/>
        <property name="premBankCfgDao" ref="PA_premBankCfgDao"/>
        <property name="renewChangeDao" ref="PA_renewChangeDao"/>
        <property name="renewalBillingDao" ref="PA_renewalBillingDao"/>
        <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
        <property name="insuredListDao" ref="PA_insuredListDao"/>
        <property name="shortPremArapDao" ref="PA_shortPremArapDao"/>
        <property name="busiProdFlagTraceDao" ref="PA_busiProdFlagTraceDao"/>
        <property name="payDueDao" ref="PA_payDueDao"/>
        <property name="prdService" ref="PA_prdIAS"/>
        <property name="taxPremiumPolicyTaskDao" ref="PA_taxPremiumPolicyTaskDao"></property>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticextra.dao.impl.RenewAutomaticExtraDaoImpl" id="PA_automaticExtraDao" parent="baseDao" scope="prototype">
	</bean>
    <bean class="com.nci.tunan.pa.dao.impl.RenewalBillingDaoImpl" id="PA_iRenewalBillingDao" parent="baseDao" scope="prototype">
    </bean>
	
	<!-- guyy_wb 约定年金领取批处理  begin -->
	<bean class="com.nci.tunan.pa.batch.unipolicyinstallment.UniPolicyInstallmentJob" id="PA_uniPolicyInstallmentJob" scope="prototype">
		<property name="uniPolicyInstallmentService" ref="PA_uniPolicyInstallmentService">
		</property>
	</bean>
	<!-- 约定年金领取service -->
	<bean class="com.nci.tunan.pa.batch.unipolicyinstallment.service.impl.UniPolicyInstalmentServiceImpl" id="PA_uniPolicyInstallmentService">
		<property name="uniPolicyInstallmentDao" ref="PA_uniPolicyInstallmentDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="payDueListDao" ref="PA_payDueListDao"/>
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
	</bean>
	<!-- 约定年金领取dao -->
	<bean class="com.nci.tunan.pa.batch.unipolicyinstallment.dao.impl.UniPolicyInstallmentDaoImpl" id="PA_uniPolicyInstallmentDao" parent="baseDao">
	</bean>
	<!-- guyy_wb 约定年金领取批处理  end -->
	
	<!-- guyy_wb 投资单位价格管理批处理  begin -->
	<bean class="com.nci.tunan.pa.batch.calinterestmargin.CalInvestUnitPriceJob" id="PA_calInvestUnitPriceJob" scope="prototype">
		<property name="calInvestUnitPriceService" ref="PA_calInvestUnitPriceService">
		</property>
	</bean>
	<!-- 投资单位价格管理service -->
	<bean class="com.nci.tunan.pa.batch.calinterestmargin.service.impl.CalInvestUnitPriceServiceImpl" id="PA_calInvestUnitPriceService">
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
		<property name="assetsDepositConfigDao" ref="PA_assetsDepositConfigDao"/>
		<property name="fundAssetsDetailDao" ref="PA_fundAssetsDetailDao"/>
		<property name="investAccountInfoDao" ref="PA_investAccountInfoDao"/>
		<property name="fundAssetsDao" ref="PA_fundAssetsDao"/>
		<property name="investUnitsDaysumDao" ref="PA_investUnitsDaysumDao"/>
		<property name="fundPriceNoticeMailDao" ref="PA_fundPriceNoticeMailDao"/>
		
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		
		<property name="sendMailService" ref="PA_sendMailService"/>
	</bean>
	<!-- 投资单位价格管理dao -->
	<bean class="com.nci.tunan.pa.batch.calinterestmargin.dao.impl.CalInvestUnitPriceDaoImpl" id="PA_calInvestUnitPriceDao" parent="baseDao">
	</bean>
	<!-- guyy_wb 投资单位价格管理批处理  end -->
	
	<!-- guyy_wb 万能保单结算批处理  begin -->
	<bean class="com.nci.tunan.pa.batch.universalsettlement.UniversalSettlementJob" id="PA_universalSettlementJob" scope="prototype">
		<property name="universalSettlementService" ref="PA_universalSettlementService"/>
	</bean>
	<!-- 万能保单结算service -->
	<bean class="com.nci.tunan.pa.batch.universalsettlement.service.impl.UniversalSettlementServiceImpl" id="PA_universalSettlementService">
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="universalSettlementDao" ref="PA_universalSettlementDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="continueBonusDService" ref="PA_continueBonusDService"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="formulaDao" ref="PA_formulaDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="fundGroupTransDao" ref="PA_FundGroupTransDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="submitMessageBocicDao" ref="PA_submitMessageBocicDao"></property>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<!-- 万能保单结算dao -->
	<bean class="com.nci.tunan.pa.batch.universalsettlement.dao.impl.UniversalSettlementDaoImpl" id="PA_universalSettlementDao" parent="baseDao">
	</bean>
	<!-- guyy_wb 万能保单结算批处理  end -->
	
	<!-- 万能保单补跑结算和扣费批处理 -->
	<bean class="com.nci.tunan.pa.batch.universalsettlement.MakeUpRunUniSetJob" id="PA_makeUpRunUniSetJob" scope="prototype">
		<property name="makeUpRunUniSetService" ref="PA_makeUpRunUniSetService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.universalsettlement.service.impl.MakeUpRunUniSetServiceImpl" id="PA_makeUpRunUniSetService">
		<property name="universalSettlementService" ref="PA_universalSettlementService"/>
		<property name="ilpuChargeDeductionService" ref="PA_ilpuChargeDeductionService"/>
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>	
	
	<!-- lipeng1_wb 万能险结算报告清单提取批处理  begin -->
	<bean class="com.nci.tunan.pa.batch.universallist.UniversalListJob" id="PA_universalListJob" scope="prototype">
		<property name="universalListService" ref="PA_universalListService"/>
	</bean>
	<!-- 万能险结算报告清单提取批处理service -->
	<bean class="com.nci.tunan.pa.batch.universallist.service.impl.UniversalListServiceImpl" id="PA_universalListService">
		<property name="univRepListFileDao" ref="PA_univRepListFileDao"/>
		<property name="udmpUserDao" ref="PA_udmpUserDao"/>
		<property name="universalListQueryForBatchJobDao" ref="PA_UniversalListQueryForBatchJobDaoImpl"/>
	</bean>
	<!-- 万能险结算报告清单提取批处理dao -->
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.UniversalSmsBDaoImpl" id="PA_universalSmsBDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.batch.universallist.dao.impl.UnivRepListFileDaoImpl" id="PA_univRepListFileDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.batch.universallist.dao.impl.UdmpUserDaoImpl" id="PA_udmpUserDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.batch.universallist.dao.impl.UniversalListQueryForBatchJobDaoImpl" id="PA_UniversalListQueryForBatchJobDaoImpl" parent="baseDao">
	</bean>
	<!-- lipeng1_wb 万能险结算报告清单提取批处理  end -->
	
	<!-- guyy_wb  秒杀保单承包 start -->
	<bean class="com.nci.tunan.pa.batch.seckillpolicydatesyn.SeckillPolicyDataSynJob" id="PA_seckillPolicyDataSynJob" scope="prototype">
		<property name="seckillBatchDSService" ref="PA_seckillBatchDSService"/>
	</bean>
	<!-- 秒杀保单承包service -->
	<bean class="com.nci.tunan.pa.batch.seckillpolicydatesyn.service.impl.SeckillBatchDSServiceImpl" id="PA_seckillBatchDSService">
		<property name="seckillPolicyDataSynDao" ref="PA_seckillPolicyDataSynDao"/>
		<property name="seckillFtpServerConfig" ref="PA_seckillFtpServerConfig"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="createPolicyService" ref="PA_createPolicyService"/>
		<property name="interestMarginService" ref="PA_interestMarginService"/>
		<property name="riskAmountService" ref="PA_riskAmountService"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="detailFeeService" ref="PA_detailFeeService"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="pACommonSearchDao" ref="PA_pACommonSearchDao"/>
		<property name="uwIAS" ref="PA_uwIAS"/>
		
		<property name="questionaireInfoDao" ref="PA_paquestionaireInfoDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
		<property name="universalSettlementService" ref="PA_universalSettlementService"/>
		<property name="questionaireCustomerParamDao" ref="PA_questionaireCustomerParamDao"/>
		<property name="dataBackFileUrl" value="${seckillFtpServerConfig.dataBackFileUrl}"/>
	</bean>	
	<!-- 秒杀保单承包dao -->
	<bean class="com.nci.tunan.pa.batch.seckillpolicydatesyn.dao.impl.SeckillPolicyDataSynDaoImpl" id="PA_seckillPolicyDataSynDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.batch.seckillpolicydatesyn.service.impl.SeckillPolicyDataThread" id="PA_seckillPolicyDataThread">
		<property name="seckillBatchDSService" ref="PA_seckillBatchDSService"/>
	</bean>
	<!-- guyy_wb 秒杀保单承包 end -->
	
	<!-- guyy_wb 秒杀保单终止 start -->
		<bean class="com.nci.tunan.pa.batch.seckillpolicydatesyn.SeckillPolicyExpiryDataSynJob" id="PA_seckillPolicyExpiryDataSynJob" scope="prototype">
			<property name="seckillExpiryPolicyService" ref="PA_seckillExpiryPolicyService"/>
		</bean>
		
		<bean class="com.nci.tunan.pa.batch.seckillpolicydatesyn.service.impl.SeckillExpiryPolicyServiceImpl" id="PA_seckillExpiryPolicyService" scope="prototype">
			<property name="seckillExpiryPolicyDao" ref="PA_seckillExpiryPolicyDao"/>
			<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
			<property name="ftpServerConfig" ref="PA_seckillFtpServerConfig"/>
			<property name="ftpUpFileUrl" value="${seckillExpiryPolicy.upFileUrl}"/>
		</bean>
		
		<bean class="com.nci.tunan.pa.batch.seckillpolicydatesyn.dao.impl.SeckillExpiryPolicyDaoImpl" id="PA_seckillExpiryPolicyDao" parent="baseDao">
		</bean>
	<!-- guyy_wb 秒杀保单终止 end -->
	
	
	<!-- guyy_wb 持续奖金批批处理  begin -->
	<bean class="com.nci.tunan.pa.batch.continuebonus.ContinueBonusJob" id="PA_continueBonusJob" scope="prototype">
		<property name="continueBonusDService" ref="PA_continueBonusDService"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.continuebonus.ContinueBonusInitPlanJob" id="PA_ContinueBonusInitPlanJob" scope="prototype">
		<property name="continueBonusDService" ref="PA_continueBonusDService">
		</property>
	</bean>
	<!-- 持续奖金批service -->
	<bean class="com.nci.tunan.pa.batch.continuebonus.service.impl.ContinueBonusDServiceImpl" id="PA_continueBonusDService">
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="continueBonusDao" ref="PA_continueBonusDao"/>
		<property name="persistenceBonusDao" ref="PA_persistenceBonusDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="commonTaskDao" ref="PA_paCommonTaskDao"/>
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
	</bean>
	<!-- 持续奖金批dao -->
	<bean class="com.nci.tunan.pa.batch.continuebonus.dao.impl.ContinueBonusDaoImpl" id="PA_continueBonusDao" parent="baseDao">
	</bean>
	<!-- guyy_wb 持续奖金批处理  end -->
	
<!-- guyy_wb 利差返回批处理  begin -->
	<bean class="com.nci.tunan.pa.batch.interestmargin.InterestMarginJob" id="PA_interestMarginJob" scope="prototype">
		<property name="interestMarginService" ref="PA_interestMarginService">
		</property>
	</bean>
	<!-- 持续奖金批service -->
	<bean class="com.nci.tunan.pa.batch.interestmargin.service.impl.InterestMarginServiceImpl" id="PA_interestMarginService">
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="interestMarginDao" ref="PA_interestMarginDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="interestMarginAllocateDao" ref="PA_interestMarginAllocateDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="interestMarginUseIAService" ref="PA_interestMarginUseIAService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.interestmargin.service.impl.InterestMarginUseIAServiceImpl" id="PA_interestMarginUseIAService">
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="interestMarginDao" ref="PA_interestMarginDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		
	</bean>
	<!-- 持续奖金批dao -->
	<bean class="com.nci.tunan.pa.batch.interestmargin.dao.impl.InterestMarginDaoImpl" id="PA_interestMarginDao" parent="baseDao">
	</bean>
	<!-- guyy_wb 利差返回批处理  end -->	
	
	
	<!-- leihong 生存金年金 抽档批处理 job -->
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.SurvivalAnnuityExtraJob" id="PA_survivalAnnuityExtraJob" scope="prototype">
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean>

	<!-- leihong 生存金年金 发放批处理job -->
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.SurvivalAnnuityIssueJob" id="PA_survivalAnnuityBatchJob" scope="prototype">
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean>
	<!-- songdf 生存金年金 抽档批处理 jobs 新模板测试 -->
	<!--  
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.SurvivalAnnuityExtraJobs" id="PA_survivalAnnuityExtraJobs" scope="prototype">
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean>
	-->
	<!-- songdf 生存金年金 发放批处理jobs  新模板测试-->
	<!-- 
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.SurvivalAnnuityIssueJobs" id="PA_survivalAnnuityBatchJobs" scope="prototype">
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean>
 	-->
	<!-- leihong 满期金抽档job -->
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.MaturityBenefitNoticeJob" id="PA_maturityNoticeJob" scope="prototype">
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean>
	
	<!-- leihong 满期金发放job -->
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.MaturityBenefitJob" id="PA_maturityBenefitJob" scope="prototype">
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean>
		
	<!-- leihong 生存金年金、满期金 service -->
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.service.impl.SurvialAnnuityMaturityServiceImpl" id="PA_surAnnuityMaturityService" scope="prototype">
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="calcSAMService" ref="PA_calcSAMService"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="payDueListDao" ref="PA_payDueListDao"/>
		<property name="releValidationService" ref="PA_releValidationService"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="bankBranchDao" ref="PA_bankBranchDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="agentDao" ref="PA_agentDaoTunan"/>
 		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>	
 		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
 		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
 		<property name="ipolicyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
 		<property name="contractRelationDao" ref="PA_contractRelationDao" />
	</bean>
	
	<!-- 生存金年金、满期金dao -->
	<bean class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.dao.impl.SurvialAnnuityMaturityDaoImpl" id="PA_survialAnnuityMaturityDao" parent="baseDao">
	</bean>
	
	<!--leihong  计算生存金年金、满期金 [批处理专用]-->
	<bean class="com.nci.tunan.pa.common.service.impl.CalcSAMServiceImpl" id="PA_calcSAMService">
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="queryCalcParam" ref="PA_queryCalcParam"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="universalSettlementDao" ref="PA_universalSettlementDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="calCulateCashValueDao" ref="PA_paCalculateCashValueDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>	
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="csEndorseCTCalculateService" ref="PA_csEndorseCTCalculateService"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="calcOptBonusSAService" ref="PA_calcOptBonusSAService"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractProductTaxDao" ref="PA_contractProductTaxDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>	
	</bean>
	
	<!-- leihong 计算生存金年金满期金工具类 -->
	<bean class="com.nci.tunan.pa.common.service.impl.CalculateSAMServiceImpl" id="PA_calculateSAMService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>	
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>		
		<property name="queryCalcParam" ref="PA_queryCalcParam"/>		
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>	
		<property name="calcSAMService" ref="PA_calcSAMService"/>	
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="calcOptBonusSAService" ref="PA_calcOptBonusSAService"/>
		<property name="contractProductTaxDao" ref="PA_contractProductTaxDao"/>
		<property name="liabSurveryRuleDao" ref="PA_liabSurveryRuleDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean> 
	
	<!-- 短期险自动续保抽档 -->
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.AutomaticRenewalExtractionJob" id="PA_automaticrenewalextraJob" scope="prototype">
		<property name="autoextraServiece" ref="PA_autoextraServiece"/>
		<property name="prdService" ref="PA_prdIAS"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.service.impl.AutomaticRenewalExtractionServiceimpl" id="PA_autoextraServiece">
		<property name="precontProductDao" ref="PA_precontProductDao"/>
		<property name="autoextraDao" ref="PA_autoextraDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="renewalBillingDao" ref="PA_renewalBillingDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="customerSurveyDao" ref="PA_customerSurveyDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>		
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="uwService" ref="PA_uwIAS"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="bpmService" ref="PA_bpmIAS"/>
		<property name="clmService" ref="PA_clmService"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="renewChangeDao" ref="PA_renewChangeDao"/>
        <property name="saChangeDao" ref="PA_saChangeDao"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
        <property name="pagecfgPrdCateRelaDao" ref="PA_pagecfgPrdCateRelaDao"/>
        <property name="productLifeDao" ref="PA_productLifeDao"/>
        <property name="renewalExtraUCC" ref="PA_renewalExtraUCC"/>
        <property name="nbService" ref="PA_nbIAS"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.dao.impl.AutomaticRenewalExtractionDaoImpl" id="PA_autoextraDao" parent="baseDao">
	</bean>
	<!-- 短期险自动续保抽档 -->
	
	<!-- 续保处理 -->
	<bean class="com.nci.tunan.pa.batch.renewalprocess.RenewalProcessJob" id="PA_renewalprocessJob" scope="prototype">
		<property name="renewalprocessService" ref="PA_renewalprocessService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalprocess.service.impl.RenewalProcessServiceImpl" id="PA_renewalprocessService">
		<property name="pagecfgPrdCateRelaDao" ref="PA_pagecfgPrdCateRelaDao"/>
		<property name="precontProductDao" ref="PA_precontProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="autoextraDao" ref="PA_autoextraDao"/>
		<property name="renewalprocessDao" ref="PA_renewalprocessDao"/>
		<property name="renewalBillingDao" ref="PA_renewalBillingDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductLogDao" ref="PA_contractProductLogDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractExtendLogDao" ref="PA_contractExtendLogDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBusiProdLogDao" ref="PA_contractBusiProdLogDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="insuredListLogDao" ref="PA_insuredListLogDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="renewChangeDao" ref="PA_renewChangeDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="prdService" ref="PA_prdIAS"/>
        <property name="pasIAS" ref="PA_pasIAS"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
        <property name="policyHolderDao" ref="PA_policyHolderDao"/>
        
        <property name="payerDao" ref="PA_payerDao"/>
        <property name="payerAccountDao" ref="PA_payerAccountDao"/>
        <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
        <property name="contractBeneDao" ref="PA_contractBeneDao"/>
        <property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
        <property name="contractAgentDao" ref="PA_contractAgentDao"/>
        <property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
        <property name="interestMarginService" ref="PA_interestMarginService"/>
        <property name="createPolicyService" ref="PA_createPolicyService"/>
        <property name="calculateSAMService" ref="PA_calculateSAMService"/>
        <property name="riskAmountService" ref="PA_riskAmountService"/>
        <property name="autoRenewService" ref="PA_autoextraServiece"/>
        <property name="uwService" ref="PA_uwIAS"/>
        <property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao" />
        <property name="renewalLogDao" ref="PA_renewalLogDao" />
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalprocess.dao.impl.RenewalProcessDaoImpl" id="PA_renewalprocessDao" parent="baseDao">
	</bean>
	<!-- 续保处理 -->
	
	<!-- 续保转投 -->
	<bean class="com.nci.tunan.pa.batch.renewalprocess.RenewalSwitchJob" id="PA_renewalSwitchJob" scope="prototype">
		<property name="renewalSwitchService" ref="PA_renewalSwitchService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalprocess.service.impl.RenewalSwitchServiceImpl" id="PA_renewalSwitchService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="renewalSwitchDao" ref="PA_renewalSwitchDao"/>
		<property name="autoRenService" ref="PA_autoextraServiece"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="renewalBillingDao" ref="PA_renewalBillingDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="clmService" ref="PA_pa_clmService" />
		<property name="nbService" ref="PA_nbIAS"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="imageScanDao" ref="PA_imageScanDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="questionaireInfoDao" ref="PA_paquestionaireInfoDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="irenewChangeDao" ref="PA_renewChangeDao"/>
		<property name="iprecontProductDao" ref="PA_precontProductDao"/>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
		<property name="busiProdFlagTraceDao" ref="PA_busiProdFlagTraceDao"/>
		<property name="autoextraDao" ref="PA_autoextraDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalprocess.dao.impl.RenewalSwitchDaoImpl" id="PA_renewalSwitchDao" parent="baseDao">
	</bean>

	
	<!-- 长期险种续保 -->
	<bean class="com.nci.tunan.pa.batch.secularAutomaticRenewal.SecularAutomaticRenewalJob" id="PA_secularautomaticrenewalJob" scope="prototype">
		<property name="secularautomaticrenewalServiceImpl" ref="PA_secularautomaticrenewalServiceImpl"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.secularAutomaticRenewal.service.impl.SecularAutomaticRenewalServiceImpl" id="PA_secularautomaticrenewalServiceImpl">
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="secularautomaticrenewalDaoImpl" ref="PA_secularautomaticrenewalDaoImpl"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBusiProdLogDao" ref="PA_contractBusiProdLogDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductLogDao" ref="PA_contractProductLogDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="autoextraDao" ref="PA_autoextraDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
        <property name="pasIAS" ref="PA_pasIAS"/>
        <property name="renewRiskAmountService" ref="PA_renewRiskAmountService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.secularAutomaticRenewal.dao.impl.SecularAutomaticRenewalDaoImpl" id="PA_secularautomaticrenewalDaoImpl" parent="baseDao">
	</bean>

	<!--leihong 现金价值计算 -->
	<bean class="com.nci.tunan.pa.common.service.impl.CalculateCashValueServiceImpl" id="PA_calcCashValueService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="calCulateCashValueDao" ref="PA_paCalculateCashValueDao"/>
		<property name="contractProductLogDao" ref="PA_contractProductLogDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="advanceDao" ref="PA_advanceDao"/>
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="cusApplicationService" ref="PA_cusApplicationService"/>		
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="queryCalcParam" ref="PA_queryCalcParam"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAccountStreamRateDao" ref="PA_policyAccountStreamRateDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="calculateIlpuDueFee" ref="PA_calculateIlpuDueFee"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="clmService" ref="PA_clmService"/>
		<property name="calcOptBonusSAService" ref="PA_calcOptBonusSAService"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="csPolicyAccountStreamDao" ref="PA_csPolicyAccountStreamDao"/>
		<property name="csPolicyAccountStreamRateDao" ref="PA_cs_PolicyAccountStreamRateDao"/>
		<property name="loanFactorPeriodCodeDao" ref="PA_loanFactorPeriodCodeDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="cSCalculateCashValueService" ref="PA_cSCalculateCashValueService"/>
	</bean>
	<bean class="com.nci.tunan.pa.common.service.impl.QueryCalcParamImpl" id="PA_queryCalcParam">
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
	</bean>
	<!-- 自垫批作业 -->
	<bean class="com.nci.tunan.pa.batch.automaticpayment.AutomaticPaymentJob" id="PA_automaticPaymentJob" scope="prototype">
		<property name="paymentService" ref="PA_paymentService"/>		
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticpayment.service.impl.AutomaticPaymentServiceImpl" id="PA_paymentService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="autoPaymentDao" ref="PA_autoPaymentDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="renewCollectionService" ref="PA_renewCollectionService"/>
		<property name="premDao" ref="PA_premDao"/>
		<!-- 计算现价 -->
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticpayment.dao.impl.AutomaticPaymentDaoImpl" id="PA_autoPaymentDao" parent="baseDao">
	</bean>

	<!-- 贷款自垫预终止批作业 -->
 	<bean class="com.nci.tunan.pa.batch.loanpaymentadvance.LoanPaymentAdvance" id="PA_loanPaymentAdvance" scope="prototype">
		<property name="advanceService" ref="PA_advanceService"/>
	</bean> 
	<bean class="com.nci.tunan.pa.batch.loanpaymentadvance.service.impl.LoanPaymentAdvanceServiceImpl" id="PA_advanceService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="advanceDao" ref="PA_advanceDao"/>
		<!-- 计算现价 -->
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.loanpaymentadvance.dao.impl.LoanPaymentAdvanceDaoImpl" id="PA_advanceDao" parent="baseDao">
	</bean>

	<!-- 贷款预终止、终止通知书历史数据处理 start -->
 	<bean class="com.nci.tunan.pa.batch.loanDocument.LoanDocumentJob" id="PA_loanDocumentJob" scope="prototype">
 		<property name="loanDocumentService" ref="PA_loanDocumentService"/>
 	</bean> 
 	<bean class="com.nci.tunan.pa.batch.loanDocument.service.impl.LoanDocumentServiceImpl" id="PA_loanDocumentService">
 		<property name="documentDao" ref="PA_paDocumentDao"/>
 		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
 		<property name="clobDao" ref="PA_clobDao" />
 		<property name="noticeService" ref="PA_noticeService" />
 		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
 	</bean>

	<!-- 贷款预终止、终止通知书历史数据处理 end -->
	<!-- 贷款自垫终止 -->
	<bean class="com.nci.tunan.pa.batch.loanpaymentstop.LoanPaymentStop" id="PA_loanPaymentStop" scope="prototype">
		<property name="stopLoanService" ref="PA_stopLoanService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.loanpaymentstop.service.impl.LoanPaymentStopServiceImpl" id="PA_stopLoanService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="stopLoanDao" ref="PA_stopLoanDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="normalLapseDao" ref="PA_normalLapseDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<!-- 计算现价 -->
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.loanpaymentstop.dao.impl.LoanPaymentStopDaoImpl" id="PA_stopLoanDao" parent="baseDao">
	</bean>	
	
	<!-- yangnn 保单暂缓批作业Job -->
	<bean class="com.nci.tunan.pa.batch.policypause.PolicyPauseJob" id="PA_policyPauseJob" scope="prototype">
		<property name="policyPauseJobService" ref="PA_policyPauseJobService"/>
	</bean>
	
	<!-- yangnn 保单暂缓批作业service -->
	<bean class="com.nci.tunan.pa.batch.policypause.service.impl.PolicyPauseJobServiceImpl" id="PA_policyPauseJobService">
		<property name="policyPauseJobDao" ref="PA_policyPauseJobDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
		<property name="capIAS" ref="PA_capIAS"/>
        <property name="premDao" ref="PA_premDao"/>
	</bean>
	
	<!-- yangnn 保单暂缓批作业DAO -->
	<bean class="com.nci.tunan.pa.batch.policypause.dao.impl.PolicyPauseJobDaoImpl" id="PA_policyPauseJobDao" parent="baseDao">
	</bean>
	<!-- zhuyi 派发特殊红利 -->
	<bean class="com.nci.tunan.pa.batch.allocation.SpecialBonusJob" id="PA_specialBonusJob" scope="prototype">
		<property name="specialBonusService" ref="PA_specialBonusService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.allocation.service.impl.SpecialBonusServiceImpl" id="PA_specialBonusService" scope="prototype">
		<property name="specialBonusDao" ref="PA_specialBonusDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="allocationDao" ref="PA_allocationDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.allocation.dao.impl.SpecialBonusDaoImpl" id="PA_specialBonusDao" parent="baseDao">
	</bean>

	<!-- zhuyi 派发年度红利 -->
	<bean class="com.nci.tunan.pa.batch.allocation.BonusAllocationJob" id="PA_bonusAllcoationJob" scope="prototype">
		<property name="allocationService" ref="PA_allocationService"/>
	</bean>

	<bean class="com.nci.tunan.pa.batch.allocation.service.impl.BounsAllocationServiceImpl" id="PA_allocationService" scope="prototype">
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="allocationDao" ref="PA_allocationDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="specialBonusDao" ref="PA_specialBonusDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="cashallocationService" ref="PA_cashallocationService"/>
		<property name="commonBatchDao" ref="PA_commonBatchDao" />
		<property name="payDueDao" ref="PA_payDueDao" />
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao" />
	</bean>

	<bean class="com.nci.tunan.pa.batch.allocation.dao.impl.BonusAllocationDaoimpl" id="PA_allocationDao" parent="baseDao">
	</bean>
	
	<!--zhuyi_wb 现金分红 -->
	<bean class="com.nci.tunan.pa.batch.allocation.CashBonusAllocationJob" id="PA_CashBonusAllcoationJob" scope="prototype">
		<property name="cashallocationService" ref="PA_cashallocationService"/>
	</bean>

	<bean class="com.nci.tunan.pa.batch.allocation.service.impl.CashBonusAllocationServiceImpl" id="PA_cashallocationService" scope="prototype">
		<property name="cashallocationDao" ref="PA_cashallocationDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="allocateDao" ref="PA_bonusallocateDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/> 
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="specialBonusDao" ref="PA_specialBonusDao"/>
		<property name="bonusallocateDao" ref="PA_bonusallocateDao"/>
		<property name="allocationDao" ref="PA_allocationDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
		<property name="payDueListDao" ref="PA_payDueListDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="contractRelationDao" ref="PA_contractRelationDao" />
		<property name="agentDao" ref="PA_agentDaoTunan"/>
		<property name="bankBranchDao" ref="PA_bankBranchDao"/>
	</bean>

	<bean class="com.nci.tunan.pa.batch.allocation.dao.impl.CashBonusAllocationDaoImpl" id="PA_cashallocationDao" parent="baseDao">
	</bean>

	
	<!-- yangnn 满期终止批作业Job -->
	<bean class="com.nci.tunan.pa.batch.maturityoperation.MaturityJob" id="PA_maturityJob" scope="prototype">
		<property name="maturityJobService" ref="PA_maturityJobService"/>
	</bean>
	
	<!-- yangnn 满期终止批作业service -->
	<bean class="com.nci.tunan.pa.batch.maturityoperation.service.impl.MaturityJobServiceImpl" id="PA_maturityJobService">
		<property name="maturityJobDao" ref="PA_maturityJobDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
        <property name="iNoticeService" ref="PA_noticeService"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
        <property name="policyHolderDao" ref="PA_policyHolderDao"/>
        <property name="customerDao" ref="PA_customerDao"/>
        <property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
        <property name="capServiceSaveArap" ref="PA_caparapuccsaveArap"></property>
        <property name="businessProductDao" ref="PA_businessProductDao"/>
        <property name="riskAmountDao" ref="PA_riskAmountDao"/>
        <property name="contractProductDao" ref="PA_contractProductDao"/>
        <property name="capIAS" ref="PA_capIAS"/>
        <property name="prdIAS" ref="PA_prdIAS"/>
        <property name="premDao" ref="PA_premDao"/>
        <property name="autoextraDao" ref="PA_autoextraDao"/>
        <property name="shortPremArapDao" ref="PA_shortPremArapDao"/>
        <property name="renewChangeDao" ref="PA_renewChangeDao"/>
        <property name="busiProdFlagTraceDao" ref="PA_busiProdFlagTraceDao"/>
        <property name="payDueDao" ref="PA_payDueDao"/>
        <property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>
	
	<!-- yangnn 满期终止批作业DAO -->
	<bean class="com.nci.tunan.pa.batch.maturityoperation.dao.impl.MaturityJobDaoImpl" id="PA_maturityJobDao" parent="baseDao">
	</bean>
	
	<!-- yangnn 永久失效批作业Job -->
	<bean class="com.nci.tunan.pa.batch.permanentlapse.PermanentLapseJob" id="PA_permanentLapseJob" scope="prototype">
		<property name="permanentLapseService" ref="PA_permanentLapseService"/>
	</bean>
	
	<!-- yangnn 永久失效批作业service -->
	<bean class="com.nci.tunan.pa.batch.permanentlapse.service.impl.PermanentLapseServiceImpl" id="PA_permanentLapseService">
		<property name="permanentLapseDao" ref="PA_permanentLapseDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
        <property name="iNoticeService" ref="PA_noticeService"/>
        <property name="calcCashValueService" ref="PA_calcCashValueService"/>
        <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
        <property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
        <property name="advanceDao" ref="PA_advanceDao"/>
        <property name="calculateSAMService" ref="PA_calculateSAMService"/>
        <property name="contractProductDao" ref="PA_contractProductDao"/>
        <property name="businessProductDao" ref="PA_businessProductDao"/>
        <property name="sendMailService" ref="PA_sendMailServiceImpl"/>
	</bean>
	
	<!-- yangnn 永久失效批作业DAO -->
	<bean class="com.nci.tunan.pa.batch.permanentlapse.dao.impl.PermanentLapseDaoImpl" id="PA_permanentLapseDao" parent="baseDao">
	</bean>
	
	<!-- yangnn 普通失效批作业Job -->
	<bean class="com.nci.tunan.pa.batch.normallapse.NormalLapseJob" id="PA_normalLapseJob" scope="prototype">
		<property name="normalLapseService" ref="PA_normalLapseService"/>
	</bean>
	
	<!-- yangnn 普通失效批作业service -->
	<bean class="com.nci.tunan.pa.batch.normallapse.service.impl.NormalLapseServiceImpl" id="PA_normalLapseService">
		<property name="capServiceSaveArap" ref="PA_caparapuccsaveArap"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="normalLapseDao" ref="PA_normalLapseDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
        <property name="iNoticeService" ref="PA_noticeService"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
        <property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
        <property name="riskAmountDao" ref="PA_riskAmountDao"/>
        <property name="premDao" ref="PA_premDao"/>
        <property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>
	
    <!-- zhangyuefeng 通知书打印service -->
<!--     <bean id="iNoticeService"  -->
<!--         class="com.nci.tunan.pa.impl.notice.service.impl.NoticeServiceImpl"  -->
<!--         scope="prototype"> -->
<!--     </bean>  -->
    
	<!-- yangnn 普通失效批作业DAO -->
	<bean class="com.nci.tunan.pa.batch.normallapse.dao.impl.NormalLapseDaoImpl" id="PA_normalLapseDao" parent="baseDao">
	</bean>
	
	<!--  自动撤销批处理 ====start=====-->
	<bean class="com.nci.tunan.pa.batch.cancellapse.CancelLapseJob" id="PA_cancelLapseJob">
		<property name="cancelLapseService" ref="PA_cancelLapseService"/>
	</bean>
	<bean class=" com.nci.tunan.pa.batch.cancellapse.service.imp.CancelLapseServiceImpl" id="PA_cancelLapseService">
		<property name="cancelLapseDao" ref="PA_cancelLapseDao"/>
		<property name="csApplicationDao" ref="PA_csApplicationDao"/>
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
		<property name="cPAServiceImpl" ref="PA_caparapuccsaveArap"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="csRemarkDao" ref="PA_csRemarkDao"/>
		<property name="csBusinessLockService" ref="PA_csBusinessLockService"/>
		<property name="uwMasterDao" ref="PA_uwMasterDao"/>
		<property name="sendMailToOperatorByService" ref="PA_sendMailToOperatorByService"/>
		<property name="csSendMessageForLoanService" ref="PA_csSendMessageForLoanService"/>
		<property name="capIAS" ref="PA_capIAS"/>
	</bean>
	<bean class=" com.nci.tunan.pa.batch.cancellapse.dao.impl.CancelLapseDaoImpl" id="PA_cancelLapseDao" parent="baseDao">
	</bean>
	<!--  自动撤销批处理  ====end==== -->
	
	
	
	
	<!-- fengwz 账户结息批作业Job -->
	<bean class="com.nci.tunan.pa.batch.accountinterest.AccountInterestJob" id="PA_accountInterestJob" scope="prototype">
		<property name="accountInterestService" ref="PA_accountInterestService"/>
	</bean>
	<!-- fengwz 账户结息service -->
	<bean class="com.nci.tunan.pa.batch.accountinterest.service.impl.AccountInterestServiceImpl" id="PA_accountInterestService" scope="prototype">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
	</bean>	
	<!-- fengwz 投连万能失效批作业Job -->
	<bean class="com.nci.tunan.pa.batch.ilpulapse.ILPULapseJob" id="PA_ilpulapseJob" scope="prototype">
		<property name="iLPULapseService" ref="PA_iLPULapseService"/>
	</bean>
	<!-- yangnn 投连万能失效批作业service -->
	<bean class="com.nci.tunan.pa.batch.ilpulapse.service.impl.ILPULapseServiceImpl" id="PA_iLPULapseService">
		<property name="iLPULapseDao" ref="PA_iLPULapseDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
        <property name="policyLogService" ref="PA_policyLogService"/>
        <property name="iNoticeService" ref="PA_noticeService"/>
        <property name="riskAmountDao" ref="PA_riskAmountDao"/>
        <property name="contractMasterDao" ref="PA_contractMasterDao"/>
        <property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>
	<!-- yangnn 投连万能失效批作业DAO -->
	<bean class="com.nci.tunan.pa.batch.ilpulapse.dao.impl.ILPUapseDaoImpl" id="PA_iLPULapseDao" parent="baseDao">
	</bean>
	
	
	<!--leihong 投连计价批处理 -->
	<bean class="com.nci.tunan.pa.batch.ilpdealdelivery.dao.impl.ILPDealDeliveryDaoImpl" id="PA_ilpDealDeliveryDao" parent="baseDao" scope="prototype"/>	
		
	<bean class="com.nci.tunan.pa.batch.ilpdealdelivery.service.impl.ILPDealDeliveryServiceImpl" id="PA_ilpDealDeliveryService" scope="prototype">
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractInvestStreamDao" ref="PA_contractInvestStreamDao"/>
		<property name="ilpDealDeliveryDao" ref="PA_ilpDealDeliveryDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="csEndorseCTService" ref="PA_csEndorseCTService"/>
		<property name="cusAcceptService" ref="PA_cusAcceptService"/>
		<property name="investFeeUtils" ref="PA_investFeeUtils" />
		<property name="csPremArapDao" ref="PA_csPremArapDao"/>
		<property name="csILPDealService" ref="PA_csILPDealService"/>
		<property name="csEndorseCTCalculateService" ref="PA_csEndorseCTCalculateService"></property>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="investPriceDayDao" ref="PA_investPriceDayDaoDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.ilpdealdelivery.ILPDealDeliveryJob" id="PA_iLPDealDeliveryJob" scope="prototype">
		<property name="ilpDealDeliveryService" ref="PA_ilpDealDeliveryService"/>
		<property name="csILPDealService" ref="PA_csILPDealService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.ilpdealdelivery.IDailyValuationJob" id="PA_iDailyValuationJob" scope="prototype">
		<property name="ilpDealDeliveryService" ref="PA_ilpDealDeliveryService"/>
	</bean>
	
	<!-- 计算投连万能费用扣除金额 -->
	<bean class="com.nci.tunan.pa.common.service.impl.CalculateIlpuDueFee" id="PA_calculateIlpuDueFee" scope="prototype">
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"></property>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"></property>
	</bean>
	
	<!-- leihong 投连万能费用扣除 -->
	<bean class="com.nci.tunan.pa.batch.ilpuchargededuction.dao.impl.ILPUChargeDeductionDaoImpl" id="PA_ilpuChargeDeductionDao" parent="baseDao" scope="prototype"/>

	<bean class="com.nci.tunan.pa.batch.ilpuchargededuction.service.impl.ILPUChargeDeductionServiceImpl" id="PA_ilpuChargeDeductionService" scope="prototype">		
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>	
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>	
		<property name="calculateIlpuDueFee" ref="PA_calculateIlpuDueFee"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		 <property name="noticeService" ref="PA_noticeService"/>
		 <property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		 <property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
		<property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>

	<bean class="com.nci.tunan.pa.batch.ilpuchargededuction.ILPUChargeDeductionJob" id="PA_iLPUChargeDeductionJob" scope="prototype">
		<property name="ilpuChargeDeductionService" ref="PA_ilpuChargeDeductionService"/>
	</bean>
	
	<!-- yangnn 投资账户单位数日终汇总批作业Job -->
	<bean class="com.nci.tunan.pa.batch.unitsdaysum.InvestUnitsDaysumJob" id="PA_investUnitsDaysumJob" scope="prototype">
		<property name="investUnitsDaysumJobService" ref="PA_investUnitsDaysumJobService"/>
	</bean>
	
	<!-- yangnn 投资账户单位数日终汇总批作业service  -->
	<bean class="com.nci.tunan.pa.batch.unitsdaysum.service.impl.InvestUnitsDaysumJobServiceImpl" id="PA_investUnitsDaysumJobService">
		<property name="investUnitsDaysumJobDao" ref="PA_investUnitsDaysumJobDao"/>
		<property name="investUnitsDaysumDao" ref="PA_investUnitsDaysumDao"/>
		<property name="investAccountInfoDao" ref="PA_investAccountInfoDao"/>
	</bean>
	
	<!-- yangnn 投资账户单位数日终汇总批作业DAO -->
	<bean class="com.nci.tunan.pa.batch.unitsdaysum.dao.impl.InvestUnitsDaysumJobDaoImpl" id="PA_investUnitsDaysumJobDao" parent="baseDao">
	</bean>
	
	<!-- leihong 修改客户风险等级 -->
	<bean class="com.nci.tunan.pa.batch.updatecustrisklevel.UpdateCustRiskLevelJob" id="PA_updateCustRiskLevelJob">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="ftpServerConfig" ref="PA_custRiskFtpServerConfig"/>
	</bean>
	
		<!-- lvkai 日终明细对账批作业Job -->
	<bean class="com.nci.tunan.pa.batch.daydetailcheck.DayDetailCheckJob" id="PA_dayDetailCheckJob" scope="prototype">
		<property name="dayDetailCheckJobService" ref="PA_dayDetailCheckJobService"/>
	</bean>
	
	<!-- lvkai 日终明细对账批作业service -->
	<bean class="com.nci.tunan.pa.batch.daydetailcheck.service.impl.DayDetailCheckJobServiceImpl" id="PA_dayDetailCheckJobService">
		<property name="dayDetailCheckJobDao" ref="PA_dayDetailCheckJobDao"/>
		<property name="ftpServerConfig" ref="ftpServerConfig"/>
	</bean>
	
	<!-- lvkai 日终明细对账批作业DAO -->
	<bean class="com.nci.tunan.pa.batch.daydetailcheck.dao.impl.DayDetailCheckJobDaoImpl" id="PA_dayDetailCheckJobDao" parent="baseDao">
	</bean>
	

        <!-- zhangyuefeng 接入渠道-保单状态同步批作业Job -->
    <bean class="com.nci.tunan.pa.batch.batchforchannel.PolicyStatusSynchroJob" id="PA_iPolicyStatusSynchroJob" scope="prototype">
        <property name="iPolicyStatusSynchroService" ref="PA_iPolicyStatusSynchroService"/>
    </bean>
    <!-- wanggk_wb 接入渠道-保单状态同步批作业Service -->
    <bean class="com.nci.tunan.pa.batch.batchforchannel.service.impl.PolicyStatusSynchroServiceImpl" id="PA_iPolicyStatusSynchroService" scope="prototype">
        <property name="iPolicyStatusSynchroDao" ref="PA_iPolicyStatusSynchroDao"/>
        <property name="prdIAS" ref="PA_prdIAS"/>
 		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
 		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
 		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
 		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
 		<property name="fssDetailResponseDao" ref="PA_fssDetailResponseDao"/>
    </bean>
     <!-- zhangyuefeng 接入渠道-保单状态同步批作业Dao -->
    <bean class="com.nci.tunan.pa.batch.batchforchannel.dao.impl.PolicyStatusSynchroDaoImpl" id="PA_iPolicyStatusSynchroDao" parent="baseDao" scope="prototype">
    </bean>
    
     <!-- wanggk_wb 接入渠道-保单犹退批作业job -->
     <bean class="com.nci.tunan.pa.batch.batchforchannel.HesitationSurrenderSynJob" id="PA_hesitationSurrenderSynJob" scope="prototype">
        <property name="hesitationSurrenderSynService" ref="PA_hesitationSurrenderSynService"/>
    </bean>
     <!-- wanggk_wb 接入渠道-保单犹退批作业Service -->
    <bean class="com.nci.tunan.pa.batch.batchforchannel.service.impl.HesitationSurrenderSynServiceImpl" id="PA_hesitationSurrenderSynService" scope="prototype">
        <property name="hesitationSurrenderSynDao" ref="PA_hesitationSurrenderSynDao"/>
 		<property name="prdIAS" ref="PA_prdIAS"/>
 		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
 		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
 		<property name="fssDetailResponseDao" ref="PA_fssDetailResponseDao"/>
 		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
    </bean>
     <!-- wanggk_wb 接入渠道-保单犹退批作业Dao -->
    <bean class="com.nci.tunan.pa.batch.batchforchannel.dao.impl.HesitationSurrenderSynDaoImpl" id="PA_hesitationSurrenderSynDao" parent="baseDao" scope="prototype">
    </bean>
    
     <!-- wanggk_wb 接入渠道-保单详情查询批处理job -->
     <bean class="com.nci.tunan.pa.batch.batchforchannel.PolicyDetailJob" id="PA_policyDetailJob" scope="prototype">
        <property name="iPolicyDetailBeachService" ref="PA_iPolicyDetailBeachService"/>
    </bean>
     <!-- wanggk_wb 接入渠道-保单详情查询批处理Service -->
    <bean class="com.nci.tunan.pa.batch.batchforchannel.service.impl.PolicyDetailBeachServiceImpl" id="PA_iPolicyDetailBeachService" scope="prototype">
    	<property name="iPolicyDetailBeachDao" ref="PA_iPolicyDetailBeachDao"/>
    	<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
    	<property name="policyHolderDao" ref="PA_policyHolderDao"/>
    	<property name="addressDao" ref="PA_addressDao"/>
    	<property name="insuredListDao" ref="PA_insuredListDao"/>
    	<property name="customerDao" ref="PA_customerDao"/>
    	<property name="contractBeneDao" ref="PA_contractBeneDao"/>
    	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
    	<property name="businessProductDao" ref="PA_businessProductDao"/>
    	<property name="contractProductDao" ref="PA_contractProductDao"/>
    	<property name="payerAccountDao" ref="PA_payerAccountDao" />
    	<property name="premDao" ref="PA_premDao"></property>
    </bean>
    <!-- wanggk_wb 接入渠道-保单详情查询批处理Dao -->
    <bean class="com.nci.tunan.pa.batch.batchforchannel.dao.impl.PolicyDetailBeachDaoImpl" id="PA_iPolicyDetailBeachDao" parent="baseDao" scope="prototype">
    </bean>
    

    
    
    <!-- 银行划款对账通知书批处理 -->
    <bean class="com.nci.tunan.pa.batch.bankchecknotice.BankCheckNoticeJob" id="PA_bankCheckNoticeSendJob" scope="prototype">
       <property name="bankCheckNoticeService" ref="PA_bankCheckNoticeService"/>
    </bean>
    <!-- 银行划款对账通知书批处理service -->
    <bean class="com.nci.tunan.pa.batch.bankchecknotice.service.impl.BankCheckNoticeServiceImpl" id="PA_bankCheckNoticeService">
         <property name="bankCheckNoticeDao" ref="PA_bankCheckNoticeDao"/>
         <property name="noticeService" ref="PA_noticeService"/>
    </bean>
    <!--银行划款对账通知书批处理Dao  -->
    <bean class="com.nci.tunan.pa.batch.bankchecknotice.dao.impl.BankCheckNoticeDaoImpl" id="PA_bankCheckNoticeDao" parent="baseDao" scope="prototype"> 
    </bean>
    
    <!-- 风险保额批处理 addd by liangpl start  -->
    <bean class="com.nci.tunan.pa.batch.risk.RiskBatch" id="PA_riskBatch" scope="prototype">   
    	 <property name="riskBatchService" ref="PA_riskBatchService"/>
    </bean>
    <bean class="com.nci.tunan.pa.batch.risk.service.impl.RiskBatchServiceImpl" id="PA_riskBatchService">
    	<property name="insuredListDao" ref="PA_insuredListDao"/>
    	<property name="contractProductDao" ref="PA_contractProductDao"/>
    	<property name="riskAmountDao" ref="PA_riskAmountDao"/>
    	<property name="customerDao" ref="PA_customerDao"/>
    	<property name="saveRiskDao" ref="PA_saveRiskDao"/>
    	<property name="contractExtendDao" ref="PA_contractExtendDao"/>
    	<property name="prdIAS" ref="PA_prdIAS"/>
    	<property name="payPlanDao" ref="PA_payPlanDao"/>
    	<property name="premArapDao" ref="PA_premArapDao"/>
    	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
    </bean>
     <bean class="com.nci.tunan.pa.batch.risk.dao.impl.SaveRiskDaoImpl" id="PA_saveRiskDao" parent="baseDao" scope="prototype"> 
    </bean>
     <!-- 风险保额批处理 addd by liangpl end  -->
     
     <!-- 风险保额批处理（按照保单号）add by liangpl start -->
      <bean class="com.nci.tunan.pa.batch.policyrisk.PolicyRiskBatch" id="PA_policyRiskBatch" scope="prototype">   
    	 <property name="policyRiskService" ref="PA_policyRiskService"/>
    </bean>
    <bean class="com.nci.tunan.pa.batch.policyrisk.ucc.impl.PolicyRiskServiceImpl" id="PA_policyRiskService">
    	<property name="riskAmountDao" ref="PA_riskAmountDao"/>
    	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
    	<property name="prdIAS" ref="PA_prdIAS"/>
    	<property name="riskAmountQueueDao" ref="PA_riskAmountQueueDao"/>
    	<property name="policyRiskDao" ref="PA_policyRiskDao"/>
    	<property name="fundTransDao" ref="PA_fundTransDao"></property>
    	<property name="contractProductDao" ref="PA_contractProductDao"/>
    	<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
    	<property name="contractLiabAmoutService" ref="PA_contractLiabAmoutService"/>
    	<property name="payPlanDao" ref="PA_payPlanDao"/>
    </bean>
    <bean class="com.nci.tunan.pa.batch.policyrisk.dao.impl.PolicyRiskDaoImpl" id="PA_policyRiskDao" parent="baseDao" scope="prototype">		
	</bean>
     
      <!-- 风险保额批处理（按照保单号）add by liangpl end -->
    
    <!-- leihong 保单余额转保费批处理-->
   <bean class="com.nci.tunan.pa.batch.balancetransprem.PaBalanceToPremJob" id="PA_paBalanceToPremJob" scope="prototype">
		<property name="paBalanceToPremService" ref="PA_paBalanceToPremService"/>		
	</bean>
	<bean class="com.nci.tunan.pa.batch.balancetransprem.service.impl.PaBalanceToPremServiceImpl" id="PA_paBalanceToPremService" scope="prototype">
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="paBalanceToPremDao" ref="PA_paBalanceToPremDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="capIAS" ref="PA_capIAS"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.balancetransprem.dao.impl.PaBalanceToPremDaoImpl" id="PA_paBalanceToPremDao" parent="baseDao" scope="prototype">		
	</bean>
	
	<!-- 续期任务提醒批处理-根据保单类型 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.RenewalMissionByChannelJob" id="PA_renewalMissionByChannelJob" scope="prototype">
		<property name="renewalMissionWarnService" ref="PA_renewalMissionWarnService"/>		
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.RenewalMissionWarnServiceImpl" id="PA_renewalMissionWarnService" scope="prototype">
		<property name="renewalMissionWarnDao" ref="PA_renewalMissionWarnDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
        <property name="commonBatchDao" ref="PA_commonBatchDao" />
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.RenewalMissionWarnDaoImpl" id="PA_renewalMissionWarnDao" parent="baseDao" scope="prototype">		
	</bean>
	
	<!-- 续期任务提醒批处理-费用状态为续保 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.RenewalMissionWarnByJob" id="PA_renewalMissionWarnByJob" scope="prototype">
		<property name="renewalMissionWarnService" ref="PA_renewalMissionWarnService"/>		
	</bean>
	
	<!-- 续期任务提醒批处理-宽限期结束提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.RenewalMissionByPayJob" id="PA_renewalMissionByPayJob" scope="prototype">
		<property name="renewalMissionWarnService" ref="PA_renewalMissionWarnService"/>		
	</bean>
	
	<!-- 续期任务提醒批处理-缴费成功 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.RenewalMissionBySuccessJob" id="PA_renewalMissionBySuccessJob" scope="prototype">
		<property name="renewalMissionWarnService" ref="PA_renewalMissionWarnService"/>		
	</bean>
	
	<!-- 年金、满期金领取到期前30天提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.MaturityAdvanceSMSJob" id="PA_maturityAdvanceSMSJob" scope="prototype">
		<property name="maturityAdvanceSMSService" ref="PA_maturityAdvanceSMSServiceImpl"/>	
	</bean>
	
	<!-- 互联网渠道短信提醒Start-->
	<!-- 互联网渠道续期交费提醒-->
	<bean class="com.nci.tunan.pa.batch.renewalSMS.IntCnlRenewalPayMsgJob" id="PA_intCnlRenewalPayMsgJob" scope="prototype">
	</bean>
	<!-- 互联网渠道续期交费成功提醒-->
	<bean class="com.nci.tunan.pa.batch.renewalSMS.IntCnlRePaySuccessMsgJob" id="PA_intCnlRePaySuccessMsgJob" scope="prototype">
	</bean>
	<!-- 互联网渠道宽限期提醒-->
	<bean class="com.nci.tunan.pa.batch.renewalSMS.IntCnlGracePeriodPayMsgJob" id="PA_intCnlGracePeriodPayMsgJob" scope="prototype">
	</bean>
	<!-- 互联网渠道短信提醒end-->
	
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.MaturityAdvanceSMSServiceImpl" id="PA_maturityAdvanceSMSServiceImpl" scope="prototype">
		<property name="annuityDao" ref="sendNoteAnnuityDao"/>
	</bean>
	
	<!-- 投连险保单状态报告(短信) -->
	<bean class="com.nci.tunan.pa.batch.sendnote.CastAndConnectWithPDFNoticeJob" id="PA_castAndConnectWithPDFNoticeJob" scope="prototype">
	</bean>
	
	<!-- 投连险保单状态报告(短信) -->
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.CastAndConnectWithPDFServiceImpl" id="PA_castAndConnectWithPDFService" scope="prototype">
	</bean>
	
	<!-- 投连险保单状态报告(短信) -->
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.CastAndConnectWithPDFDaoImpl" id="PA_castAndConnectWithPDFDao" parent="baseDao"  scope="prototype">
	</bean>
	
	<!-- 上传FTP文件的Service -->
    <bean class = "com.nci.tunan.pa.common.service.impl.CreateFtpTxtServiceImpl" id = "PA_createFtpTxtService">
    </bean>
    
	<!-- 保单短信提醒-->
   <bean class="com.nci.tunan.pa.batch.sendnote.SendNoteJob" id="PA_sendNoteJob" scope="prototype">
		<property name="sendNoteService" ref="PA_sendNoteServiceImpl"/>		
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.SendNoteServiceImpl" id="PA_sendNoteServiceImpl" scope="prototype">
		<property name="sendNoteDao" ref="PA_sendNoteDaoImpl"/>
			<property name="rossInfoDao" ref="PA_rossInfoDao"/>
			<property name="cSCalPolicyValueService" ref="PA_cSCalPolicyValueService"/>
			<property name="contractProductDao" ref="PA_contractProductDao"/>
			<property name="bonusErrorLogDao" ref="PA_bonusErrorLogDao"/>
			<property name="addressDao" ref="PA_addressDao"/>
			<property name="bankDao" ref="PA_bankDao"/>
			<property name="renewalSwitchService" ref="PA_renewalSwitchService"/>
			<property name="clmService" ref="PA_pa_clmService"/>
			<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
			<property name="insuredListDao" ref="PA_insuredListDao"/>
			<property name="autoextraDao" ref="PA_autoextraDao"/>
			<property name="prdIAS" ref="PA_prdIAS"/>
			<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
			<property name="contractMasterDao" ref="PA_contractMasterDao"/>
			<property name="pasIAS" ref="PA_pasIAS"/>
			<property name="contractInvestDao" ref="PA_contractInvestDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.SendNoteDaoImpl" id="PA_sendNoteDaoImpl" parent="baseDao" scope="prototype">		
	</bean>
	
	<!-- 分红日志推送dao -->
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.BonusErrorLogDaoImpl" id="PA_bonusErrorLogDao" parent="baseDao">
		
	</bean>
	
	<!-- song 保单  发送生日祝福邮件 -->
	<bean class="com.nci.tunan.pa.batch.policymanagement.BirthdayManagementJob" id="PA_birthdayManagementJob" scope="prototype">
		<property name="birthdayManagementService" ref="PA_birthdayManagementService"/>		
	</bean>
	<bean class="com.nci.tunan.pa.batch.policymanagement.service.impl.BirthdayManagementServiceImpl" id="PA_birthdayManagementService" scope="prototype">
		<property name="birthdayManagementDao" ref="PA_birthdayManagementDao"/>
	</bean> 
	<bean class="com.nci.tunan.pa.batch.policymanagement.dao.impl.BirthdayManagementDaoImpl" id="PA_birthdayManagementDao" parent="baseDao" scope="prototype">		
	</bean>
	
	<!-- 投保风险短信提示 start, add by liangpl-->
	<bean class="com.nci.tunan.pa.batch.alertmsg.AlertMsgJob" id="PA_alertMsgJob" scope="prototype">
		<property name="alertMsgServiceImpl" ref="PA_alertMsgServiceImpl"/>		
	</bean>
	<bean class="com.nci.tunan.pa.batch.alertmsg.service.impl.AlertMsgServiceImpl" id="PA_alertMsgServiceImpl" scope="prototype">
		<property name="alertMsgDao" ref="PA_alertMsgDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.alertmsg.dao.impl.AlertMsgDaoImpl" id="PA_alertMsgDao" parent="baseDao" scope="prototype">
	</bean>
	<!-- 发放投连万能状态报告书-->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.service.impl.ILpuchargeNoticeServiceImpl" id="PA_ilpuchargeNoticeService" scope="prototype">		
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="iNoticeService" ref="PA_noticeService"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.ILPUChargeDeductionNoticeJob" id="PA_iLPUChargeDeductionNoticeJob" scope="prototype">
		<property name="ilpuchargeNoticeService" ref="PA_ilpuchargeNoticeService"/>
	</bean>
	<!-- 888投连状态报告书-->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.NewCastAndConnectNoticeJob" id="PA_newCastAndConnectNoticeJob" scope="prototype">
		<property name="newCastAndConnectNoticeService" ref="PA_newCastAndConnectNoticeService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.service.impl.NewCastAndConnectNoticeServiceImpl" id="PA_newCastAndConnectNoticeService" scope="prototype">
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="noticeService" ref="PA_noticeService"/>
	</bean>
	<!-- 保单迁移轨迹批处理Job -->
	<bean class="com.nci.tunan.pa.batch.policymigration.PolicyRemoveLogJob" id="PA_policyRemoveLogJob" scope="prototype">
		<property name="policyRemoveLogService" ref="PA_policyRemoveLogService"/>
	</bean>
	
	<!-- 保单迁移轨迹批处理Service -->
	<bean class="com.nci.tunan.pa.batch.policymigration.service.impl.PolicyRemoveLogServiceImpl" id="PA_policyRemoveLogService" scope="prototype">
		<property name="policyRemoveLogDao" ref="PA_policyRemoveLogDao"/>
		<property name="csEffectPRService" ref="PA_csEffectPRService"/>
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>
	
	<!-- 保单迁移轨迹批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.policymigration.dao.impl.PolicyRemoveLogDaoImpl" id="PA_policyRemoveLogDao" parent="baseDao" scope="prototype">
	</bean>
	
	<!-- 银代双主险补发轨迹操作 -->
	<bean class="com.nci.tunan.pa.batch.douMainRiskLROrbit.DoubleMainRiskLROrbitJob" id="PA_doubleMainRiskLROrbitJob" scope="prototype">
		<property name="doubleMainRiskLROrbitService" ref="PA_doubleMainRiskLROrbitService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.douMainRiskLROrbit.service.impl.DoubleMainRiskLROrbitServiceImpl" id="PA_doubleMainRiskLROrbitService" scope="prototype">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="csContractMasterDao" ref="PA_csContractMasterDao"/>
		<property name="csContractBusiProdDao" ref="PA_csContractBusiProdDao"/>
		<property name="doubleMainRiskLROrbitDao" ref="PA_doubleMainRiskLROrbitDao"/>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
		<property name="policyReissueDao" ref="PA_policyReissueDao"/>
		<property name="policyLoseDao" ref="PA_policyLoseDao"/>
		<property name="clmService" ref="PA_clmService" />
		<property name="documentDao" ref="PA_documentDao" />
		<property name="csEndorseRNDao" ref="PA_csEndorseRNDao" />
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
	
	</bean>	
	<bean class="com.nci.tunan.pa.batch.douMainRiskLROrbit.dao.impl.DoubleMainRiskLROrbitDaoImpl" id="PA_doubleMainRiskLROrbitDao" parent="baseDao" scope="prototype">		
	</bean>
	<!-- song 续期年金给付客户生调批处理 -->
	<bean class="com.nci.tunan.pa.batch.checklivestatus.CheckLiveStatusJob" id="PA_checkLiveStatusJob" scope="prototype">
		<property name="checkLiveStatusService" ref="PA_checkLiveStatusService"/>	
	</bean>
	<bean class="com.nci.tunan.pa.batch.checklivestatus.service.impl.CheckLiveStatusServiceImpl" id="PA_checkLiveStatusService" scope="prototype">
		<property name="checkLiveStatusDao" ref="PA_checkLiveStatusDao"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.checklivestatus.dao.impl.ICheckLiveStatusDaoImpl" id="PA_checkLiveStatusDao" parent="baseDao" scope="prototype">		
	</bean>

	<!-- 保单质押贷款还款通知书 -->
	<bean class="com.nci.tunan.pa.batch.policypledge.PolicyPledgeRepayNoteJob" id="PA_policyPledgeRepayNoteJob" scope="prototype">
		<property name="policyPledgeRepayNoteService" ref="PA_policyPledgeRepayNoteService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.policypledge.service.impl.PolicyPledgeRepayNoteServiceImpl" id="PA_policyPledgeRepayNoteService" scope="prototype">
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="iPolicyPledgeRepayNoteDao" ref="PA_iPolicyPledgeRepayNoteDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.policypledge.dao.impl.PolicyPledgeRepayNoteDaoImpl" id="PA_iPolicyPledgeRepayNoteDao" parent="baseDao" scope="prototype">
	</bean>
    <!-- 保单贷款还款通知书(逾期后) -->
	<bean class="com.nci.tunan.pa.batch.LoanOverdueRepayment.LoanOverdueRepaymentNoteJob" id="PA_loanOverdueRepaymentNoteJob" scope="prototype">
		<property name="loanOverdueRepaymentNoteService" ref="PA_loanOverdueRepaymentNoteService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.LoanOverdueRepayment.service.impl.LoanOverdueRepaymentNoteServiceImpl" id="PA_loanOverdueRepaymentNoteService" scope="prototype">
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="loanOverdueRepaymentNoteDao" ref="PA_loanOverdueRepaymentNoteDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.LoanOverdueRepayment.dao.impl.LoanOverdueRepaymentNoteDaoImpl" id="PA_loanOverdueRepaymentNoteDao" parent="baseDao" scope="prototype">
	</bean>
	<!-- add by yangyl_wb 生成分红通知书批处理 -->
	<bean class="com.nci.tunan.pa.batch.allocation.BonusNoticeJob" id="PA_bonusNoticeJob">
		<property name="bonusNoticeService" ref="PA_bonusNoticeService"/>
		<property name="noticeService" ref="PA_noticeService"/>
	</bean>
	<!-- add by yangyl_wb 生成分红通知书 -->
	<bean class="com.nci.tunan.pa.batch.allocation.service.impl.BonusNoticeServiceImpl" id="PA_bonusNoticeService">
	    <property name="documentDao" ref="PA_paDocumentDao"/>
	</bean>
	
		<!-- add by lvkai 续期核销 -->
	<bean id="PA_renewCollectJob" class="com.nci.tunan.pa.batch.renewcollect.RenewCollectJob">
		<property name="renewCollectService" ref="PA_renewCollectService"></property>
	</bean>

	<!-- add by lvkai 续期核销 -->
	<bean id ="PA_renewCollectService" class="com.nci.tunan.pa.batch.renewcollect.service.impl.RenewCollectServiceImpl">
		<property name="capIAS" ref="PA_capIAS"></property>
		<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
		<property name="contractProductDao" ref="PA_contractProductDao"></property>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"></property>
		<property name="contractExtendDao" ref="PA_contractExtendDao"></property>
		<property name="premDao" ref="PA_premDao"></property>
		<property name="businessProductDao" ref="PA_businessProductDao"></property>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"></property>
		<property name="contractInvestDao" ref="PA_contractInvestDao"></property>
		<property name="fundTransDao" ref="PA_fundTransDao"></property>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"></property>
		<property name="continueBonusDService" ref="PA_continueBonusDService"></property>
		<property name="renewRiskAmountService" ref="PA_renewRiskAmountService"></property>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"></property>
		<property name="policyAccountDao" ref="PA_policyAccountDao"></property>
		<property name="noticeService" ref="PA_noticeService"></property>
		<property name="policyLogService" ref="PA_policyLogService"></property>
		<property name="renewalprocessService" ref="PA_renewalprocessService"/>
		<property name="renewCollectDao" ref="PA_renewCollectDaoImpl"></property>
		<property name="fundGroupTransDao" ref="PA_FundGroupTransDao"></property>
		<property name="submitMessageBocicDao" ref="PA_submitMessageBocicDao"></property>
		<property name="taxPremiumPolicyTaskDao" ref="PA_taxPremiumPolicyTaskDao"></property>
		<property name="contractAgentDao" ref="PA_contractAgentDao"></property>
	</bean>
	<bean id="PA_renewCollectDaoImpl" class="com.nci.tunan.pa.batch.renewcollect.dao.impl.RenewCollectDaoImpl" parent="baseDao" scope="prototype">
	</bean>
    
    <!-- 孤儿单上传批处理 -->
    <bean id="PA_uploadOrphanPolicyJob" class="com.nci.tunan.pa.batch.orphanpolicy.UploadOrphanPolicyJob" scope="prototype">
         <property name="orphanPolicyDao" ref="PA_orphanPolicyDao" />
   		 <property name="orphanFtpServerConfig" ref="PA_orphanFtpServerConfig"/>
   		 <property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
   		 <property name="csContractMasterDao" ref="PA_csContractMasterDao"/>
   		 <property name="commonBatchDao" ref="PA_commonBatchDao" />
   		 <property name="addressDao" ref="PA_addressDao"/>     
   		 <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>     
   		 <property name="orphanParameterService" ref="PA_orphanParameterService"/>     
    </bean>
    
    <!-- 孤儿单查询结果通知批处理 -->
    <bean id="PA_noticeOrphanResultJob" class="com.nci.tunan.pa.batch.orphanpolicy.NoticeOrphanResultJob" scope="prototype">
    </bean>
    
    
    <!-- 保单任务短信（客户证件过期次日、客户证件过期前1个月、功能上线前历史已过期客户证件、补发客户证件过期）提醒  -->
  <!--  <bean class="com.nci.tunan.pa.batch.sendnote.CertiExpiredNoteJob" id="PA_certiExpiredNoteJob" scope="prototype">
		<property name="certiExpiredNoteService" ref="PA_certiExpiredNoteServiceImpl"/>		
	</bean>
	
	
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.CertiExpiredNoteServiceImpl" id="PA_certiExpiredNoteServiceImpl" scope="prototype">
		<property name="certiExpiredNoteDao" ref="PA_certiExpiredNoteDaoImpl"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.CertiExpiredNoteDaoImpl" id="PA_certiExpiredNoteDaoImpl" parent="baseDao" scope="prototype">		
	</bean> -->


	<!-- 保单邮件提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendmail.SendMailJob" id="PA_sendMailJob">
		<property name="sendMailService" ref="PA_sendMailServiceImpl"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendmail.service.impl.SendMailServiceImpl" id="PA_sendMailServiceImpl">
		<property name="sendMailDao" ref="PA_sendMailDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
		<property name="rossInfoDao" ref="PA_rossInfoDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendmail.dao.impl.SendMailDaoImpl" id="PA_sendMailDao" parent="baseDao" scope="prototype">
		
	</bean>

	<!-- 保单资产变动信息同步交易批处理start -->
	<bean id="PA_capitalChangeSyncJob" class="com.nci.tunan.pa.batch.batchforchannel.CapitalChangeSyncJob" scope="prototype">
		<property name="capitalChangeSyncService" ref="PA_capitalChangeSyncService" />
	</bean>
	
	<bean id="PA_capitalChangeSyncService" class="com.nci.tunan.pa.batch.batchforchannel.service.impl.CapitalChangeSyncServiceImpl">
		<property name="capitalChangeSyncDao" ref="PA_capitalChangeSyncDao" />
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="fssDetailResponseDao" ref="PA_fssDetailResponseDao"/>
	</bean>
	
	<bean id="PA_capitalChangeSyncDao" class="com.nci.tunan.pa.batch.batchforchannel.dao.impl.CapitalChangeSyncDaoImpl" parent="CIP_BaseDao">
	</bean>
	<!-- 保单资产变动信息同步交易批处理end -->
	
	<!-- 超限终止批处理 - job -->
	<bean class="com.nci.tunan.pa.batch.terminate.OverrunTerminateJob" id="PA_overrunTerminateJob">
		<property name="overrunTerminateService" ref="PA_overrunTerminateService"/>
	</bean>
	
	<!-- 超限终止service -->
	<bean class="com.nci.tunan.pa.batch.terminate.service.impl.OverrunTerminateServiceImpl" id="PA_overrunTerminateService">
		<property name="overrunTerminateDao" ref="PA_overrunTerminateDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="iPolicyLogService" ref="PA_policyLogService"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="sendMailService" ref="PA_sendMailServiceImpl"/>
	</bean>
	
	<!-- 超限终止dao层 -->
	<bean class="com.nci.tunan.pa.batch.terminate.dao.impl.OverrunTerminateDaoImpl" id="PA_overrunTerminateDao" parent="baseDao">
	</bean>
	
	<!-- 非实时保全数据批处理(兴业银行) -->
    <bean id="PA_nonRealTimeCsDataJob" class="com.nci.tunan.pa.batch.batchforchannel.NonRealTimeCsDataJob" scope="prototype">
		<property name="nonRealTimeCsDataService" ref="PA_nonRealTimeCsDataService" />
    </bean>
    <bean class="com.nci.tunan.pa.batch.batchforchannel.service.impl.NonRealTimeCsDataServiceImpl" id="PA_nonRealTimeCsDataService" scope="prototype">
		<property name="policyStatusChangeDao" ref="PA_policyStatusChangeDao" />
		<property name="policyChangeDao" ref="PA_policyChangeDao" />
		<property name="contractMasterDao" ref="PA_contractMasterDao" />
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao" />
		<property name="contractProductDao" ref="PA_contractProductDao" />
		<property name="payerAccountDao" ref="PA_payerAccountDao" />
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="fssDetailResponseDao" ref="PA_fssDetailResponseDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	
	<!-- 接入渠道数据dao -->
	<bean class="com.nci.tunan.pa.batch.batchforchannel.dao.impl.FssDetailRequestDaoImpl" id="PA_fssDetailRequestDao" parent="CIP_BaseDao">
	</bean>
	<bean class="com.nci.tunan.pa.batch.batchforchannel.dao.impl.FssDetailResponseDaoImpl" id="PA_fssDetailResponseDao" parent="CIP_BaseDao">
	</bean>
	
		<!-- 贷款中止(预)通知短信提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.LoanAbeyanceNoticeJob" id="PA_loanAbeyanceNoticeJob">
		<property name="loanAbeyanceService" ref="PA_loanAbeyanceService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.LoanAbeyanceWarnNoticeJob" id="PA_loanAbeyanceWarnNoticeJob">
		<property name="loanAbeyanceService" ref="PA_loanAbeyanceService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.LoanAbeyanceServiceImpl" id="PA_loanAbeyanceService">
		<property name="loanAbeyanceDao" ref="PA_loanAbeyanceDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.LoanAbeyanceDaoImpl" id="PA_loanAbeyanceDao" parent="baseDao">
	</bean>
	<!-- 推送保单投连险信息批处理 -->
	<bean class="com.nci.tunan.pa.batch.batchforenci.AccountValueJob" id="PA_AccountValueJob">
		<property name="accountValueService" ref="PA_AccountValueService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.batchforenci.service.impl.AccountValueServiceImpl" id="PA_AccountValueService">
		<property name="accountValueDao" ref="PA_AccountValueDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="accountValueftpServerConfig" ref="PA_ftpServerConfig"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.batchforenci.dao.impl.AccountValueDaoImpl" id="PA_AccountValueDao" parent="baseDao"></bean>
	
	<!-- START  31683 并行开发任务 -->
	<!-- /**针对并行开发的代码内容*/ -->
	<!-- 老核心孤儿单查询结果上传批处理 -->
    <bean id="PA_uploadOrphanPolicyListDealJob" class="com.nci.tunan.pa.batch.orphanpolicy.UploadOrphanPolicyListDealJob" scope="prototype">
    	<property name="oldOrphanPolicyDao" ref="PA_oldOrphanPolicyDao" />
   		 <property name="orphanFtpServerConfig" ref="PA_orphanFtpServerConfig"/>
    </bean>
	<!-- END     31683 并行开发任务 -->
	
	<!-- 贷款利息计提未提类数据向收付费推送 start-->
	<bean class = "com.nci.tunan.pa.batch.loaninterest.LoanInterestJob" id = "PA_LoanInterestJob">
		<property name="iLoanInterestService" ref = "PA_LoanInterestServiceImpl"/>
	</bean>
	<bean class = "com.nci.tunan.pa.batch.loaninterest.service.impl.LoanInterestServiceImpl" id = "PA_LoanInterestServiceImpl">
		<property name="loanInterestDao" ref="PA_LoanInterestDaoImpl"/>
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<bean class = "com.nci.tunan.pa.batch.loaninterest.dao.impl.LoanInterestDaoImpl" id = "PA_LoanInterestDaoImpl" parent="baseDao"></bean>
	<!-- 贷款利息计提未提 end-->
	
	<!--自动升位批处理  -->
		<bean class="com.nci.tunan.pa.batch.automaticlifting.AutomaticliftingJob" id="PA_AutomaticliftingJob">
		<property name="automaticliftingService" ref="PA_AutomaticliftingService"/>
	</bean>
		<bean class="com.nci.tunan.pa.batch.automaticlifting.service.impl.AutomaticliftingSeviceImpl" id="PA_AutomaticliftingService">
		<property name="automaticliftingDao" ref="PA_AutomaticliftingDao"/>
		<property name="iCustomerDao" ref="PA_customerDao"></property>
		<property name="certicodePromoteLogDao" ref="PA_CerticodePromoteLogDao"></property>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticlifting.dao.impl.AutomaticliftingDaoImpl" id="PA_AutomaticliftingDao" parent="baseDao">
	</bean>
	<!-- ODS保单现价计算批处理 -->
    <bean id="PA_cashValueCalculateJob" class="com.nci.tunan.pa.batch.cashvaluecalculate.CashValueCalculateJob" scope="prototype">
    	<property name="cashValueCalculateService" ref="PA_CashValueCalculateService" />
    </bean>
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.service.impl.CashValueCalculateServiceImpl" id="PA_CashValueCalculateService">
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
		<property name="itemCashvalueDao" ref="PA_itemCashvalueDao"/>
		<property name="cashvalueFlagDao" ref="PA_cashvalueFlagDao"/>
		<property name="ftpServerConfig" ref="PA_cashValueFtpServerConfig"/>
		<property name="cvTxInfoDetailDao" ref="PA_cvTxInfoDetailDao"/>
		<property name="cvTxInfoMainDao" ref="PA_cvTxInfoMainDao"/>
	</bean>
	
	<!-- 收付费计提未提批处理开发  start-->
	<bean class="com.nci.tunan.pa.batch.accruedcharge.AccruedChargeJob" id="PA_accruedChargeJob">
		<property name="accruedChargeService" ref="PA_accruedChargeService"/>
	</bean>
	
	<!-- 收付费计提未提批处理开发  start-->
	<bean class="com.nci.tunan.pa.batch.accruedcharge.AccruedCharge628Job" id="PA_accruedCharge628Job">
		<property name="accruedChargeService" ref="PA_accruedChargeService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.accruedcharge.service.impl.AccruedChargeServiceImpl" id="PA_accruedChargeService">
		<property name="accruedChargeDao" ref="PA_AccruedChargeDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
	</bean>
	<bean class= "com.nci.tunan.pa.batch.accruedcharge.dao.impl.AccruedChargeDaoImpl" id="PA_AccruedChargeDao" parent="baseDao"/>
	<!-- 收付费计提未提批处理开发  end-->
	
	<bean class="com.nci.tunan.pa.batch.netpinprem.NetpinPremJob" id="PA_NetpinPremJob">
		<property name="netpinPremService" ref="PA_NetpinPremService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.netpinprem.service.impl.NetpinPremServiceImpl" id = "PA_NetpinPremService">
		<property name="netpinPremDao" ref="PA_NetpinPremDao"/>
		<property name="ftpServerConfigNetpinPrem" ref="PA_netftpServerConfig"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.netpinprem.dao.impl.NetpinPremDaoImpl" id="PA_NetpinPremDao" parent="baseDao" />
	<!--新增短信  -->
	   <bean class="com.nci.tunan.pa.batch.sendnote.SendNoteStateJob" id="PA_sendNoteStateJob" scope="prototype">
		<property name="sendNoteStateService" ref="PA_sendNoteStateServiceImpl"/>		
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.SendNoteStateServiceImpl" id="PA_sendNoteStateServiceImpl" scope="prototype">
		<property name="sendNoteStateDao" ref="PA_sendNoteStateDaoImpl"/>
				<property name="renewalSMSService" ref="PA_renewalSMSService"/>
				<property name="policyAccountStreamRateDao" ref="PA_policyAccountStreamRateDao"/>
		
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.SendNoteStateDaoImpl" id="PA_sendNoteStateDaoImpl" parent="baseDao" scope="prototype">		
	</bean>

	<!-- 生存金及投保人生日祝福短信提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.SendNoteAnnuityJob" id="PA_sendNoteAnnuityJob">
		<property name="noteAnnuityService" ref="SendNoteAnnuityService"/>
	</bean>
	<!-- 柜面直销渠道保单服务人员变更通知短信提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.PlySerPeoChgNoteJob" id="PA_sendNoteSerPeoChgJob">
		<property name="noteAnnuityService" ref="SendNoteAnnuityService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.SendNoteAnnuityServiceImpl" id="SendNoteAnnuityService">
		<property name="annuityDao" ref="sendNoteAnnuityDao"/>
		<property name="bonusErrorLogDao" ref="PA_bonusErrorLogDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.dao.impl.SendNoteAnnuityDaoImpl" id="sendNoteAnnuityDao"  parent="baseDao"/>
	
	<!-- 续期续保短信start -->
	<bean class="com.nci.tunan.pa.batch.renewalSMS.RenewalSMSJob" id="PA_renewalSMSJob" scope="prototype">
		<property name="renewalSMSService" ref="PA_renewalSMSService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalSMS.service.impl.RenewalSMSServiceImpl" id="PA_renewalSMSService">
		<property name="renewalSMSDao" ref="PA_renewalSMSDao"/>
		<property name="sendNoteStateDao" ref="PA_sendNoteStateDaoImpl"/>
		<property name="icaps" ref="PA_capIAS"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalSMS.dao.impl.RenewalSMSDaoImpl" id="PA_renewalSMSDao" parent="baseDao"/>
	<!-- 续期续保短信end -->
	
	<!-- 短期险续保撤销批处理start -->
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.AutomaticRenewalRevokeJob" id="PA_automaticrenewalrevokeJob" scope="prototype">
		<property name="renewalService" ref="PA_autoextraServiece"/>
		<property name="prdService" ref="PA_prdIAS"/>
	</bean>
	<!-- 短期险续保撤销批处理end -->
	
	<!-- 新续保流程历史数据撤销批处理start -->
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.RenewalHisProcessRevokeJob" id="PA_renewalHisProcessRevokeJob" scope="prototype"></bean>
	<!-- 短期险续保撤销批处理end -->
	
	<!-- 日现价计算批处理start -->
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.DayCashValueCalcJob" id="PA_dayCashValueCalcJob">
		<property name="dayCashValueCalcService" ref="PA_dayCashValueCalcService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.service.impl.DayCashValueCalcServiceImpl" id="PA_dayCashValueCalcService">
		<property name="dayCashValueCalcDao" ref="PA_dayCashValueCalcDao"/>
		<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/> 
		<property name="policyCashvalueDao" ref="PA_policyCashvalueDao"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		
	</bean>
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.dao.impl.DayCashValueCalcDaoImpl" id="PA_dayCashValueCalcDao" parent="baseDao"/>
	<!-- 日现价计算批处理end -->

	<!-- CRS报送信息批处理 start -->
	<bean class="com.nci.tunan.pa.batch.crssubmitted.CrssubmittedJob" id="PA_crssubmittedJob">
		<property name="crssubmittedService" ref="PA_crssubmittedService"/>
	</bean>
	
	<!-- CRS报送信息批处理 start -->
	<bean class="com.nci.tunan.pa.batch.crsCustomerPolicySubmit.CrsCustomerPolicySubmitJob" id="PA_crsCustomerPolicySubmitJob">
		<property name="crsCustomerPolicySubmitService" ref="PA_crsCustomerPolicySubmitService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.crsCustomerPolicySubmit.service.impl.CrsCustomerPolicySubmitServiceImpl" id="PA_crsCustomerPolicySubmitService"></bean>
	
	<bean class="com.nci.tunan.pa.batch.crssubmitted.service.impl.CrssubmittedServiceImpl" id="PA_crssubmittedService">
		<property name="crssubmittedDao" ref="PA_crssubmittedDao"/>
		<property name="taxValueDao" ref="PA_taxValueDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="calCulateCashValueDao" ref="PA_paCalculateCashValueDao"/>
		<property name="clmService" ref="PA_clmService"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="advanceDao" ref="PA_advanceDao"/>
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="taxInfoDao" ref="PAS_taxInfoDao"/>
		<property name="customerTaxDao" ref="PAS_customerTaxDao"/>
	</bean>
	<bean class="com.nci.core.common.dao.impl.CustomerTaxDaoImpl" id="PAS_customerTaxDao" parent="baseDao"/>
    <bean class="com.nci.core.common.dao.impl.TaxInfoDaoImpl" id="PAS_taxInfoDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.TaxValueDaoImpl" id="PA_taxValueDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.batch.crssubmitted.dao.impl.CrssubmittedDaoImpl" id="PA_crssubmittedDao" parent="baseDao"/>
	<!-- CRS报送信息批处理 end -->
	<!-- 营销员放弃保单信息同步 -->
	<bean class="com.nci.tunan.pa.batch.marketersabandonpolicy.service.impl.MarketersAbandonPolicyServiceimpl" id="PA_marketersAbandonPolicyService">
	     <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	     <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	</bean>
	<!-- 渠道系统向新核心同步保单信息变更需求 -->
	<bean class="com.nci.tunan.pa.batch.synchronizationPolicyCodeInfo.service.impl.SynchronizationPolicyCodeInfoServiceImpl" id="PA_synchronizationPolicyCodeInfoService">
	     <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	     <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	</bean>
	<!-- 贷款非自动清偿续贷逾期提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.OverdueReminderJob" id="PA_OverdueReminderJob">
	    <property name="sendNoteStateService" ref="PA_sendNoteStateServiceImpl"/>	
	</bean>
	
	<!-- 逾期未中止短信提醒批 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.LoanOverdueNotSuspendJob" id="PA_LoanOverdueNotSuspendJob">
	    <property name="sendNoteStateService" ref="PA_sendNoteStateServiceImpl"/>	
	</bean>
	
	<!-- 贷款逾期保单预终止提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.LoanOverDueNoticeJob" id="PA_LoanOverDueNoticeJob">
	    <property name="sendNoteStateService" ref="PA_sendNoteStateServiceImpl"/>	
	</bean>
	
	<!-- 贷款逾期保单终止提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.LoanTerminateNoteJob" id="PA_LoanTerminateNoteJob">
	    <property name="sendNoteStateService" ref="PA_sendNoteStateServiceImpl"/>	
	</bean>
	<!-- 累计生息账户结算告知提醒 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.AccountInterestNoticeJob" id="PA_AccountInterestNoticeJob">
	    <property name="sendNoteStateService" ref="PA_sendNoteStateServiceImpl"/>	
	</bean>
	
	<!-- 费率调整后撤销应收费用批处理 -->
	<bean class="com.nci.tunan.pa.batch.rateadjustrevokeprem.RateAdjustRevokePremJob" id="PA_RateAdjustRevokePremJob">
	    <property name="rateAdjustRevokPmService" ref="PA_RateAdjustRevokPmService"/>	
	</bean>
	
	<!-- 费率调整后撤销应收费用批处理Service -->
	<bean class="com.nci.tunan.pa.batch.rateadjustrevokeprem.service.impl.RateAdjustRevokPmServiceImpl" id="PA_RateAdjustRevokPmService">
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="contractProductDao" ref="PA_contractProductDao"/>
	    <property name="contractExtendDao" ref="PA_contractExtendDao"/>
	    <property name="renewalBillingDao" ref="PA_iRenewalBillingDao"/>
	    <property name="premDao" ref="PA_premDao"/>
	    <property name="premArapDao" ref="PA_premArapDao"/>
	    <property name="prdRateEffectiveDateQueryUcc" ref="IPrdRateEffectiveDateQueryUcc"/>
	    <property name="rateAdjustRevokPmDao" ref="PA_rateAdjustRevokPmDao"/>
	    <property name="iPolicyLogService" ref="PA_policyLogService"/>
	    <property name="capIAS" ref="PA_capIAS"/>
	    <property name="documentDao" ref="PA_paDocumentDao"/>
	    <property name="renewChangeDao" ref="PA_renewChangeDao"/>
	    <property name="renewExtraService" ref="PA_extraService"/>
	</bean>
	
	<!-- 万能险链接短信批处理-->
	<bean class="com.nci.tunan.pa.batch.sendnote.ProduceUniversalSMSJob" id="PA_ProduceUniversalSMSJob">
	    <property name="produceUniversalSMSService" ref="PA_ProduceUniversalSMSService"/>	
	</bean>
	
	<!-- 万能险链接短信批处理Service -->
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.ProduceUniversalSMSServiceImpl" id="PA_ProduceUniversalSMSService">
	    <property name="universalSmsDao" ref="PA_universalSmsDao"/>
	    <property name="universalBusiInfoDao" ref="PA_universalBusiInfoDao"/>
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	
	<!-- 万能险链接短信批处理-->
	<bean class="com.nci.tunan.pa.batch.sendnote.SendUniversalSMSJob" id="PA_sendUniversalSMSJob">
	    <property name="updateUniversalSMSService" ref="PA_updateUniversalSMSServiceImpl"/>	
	</bean>
	
    <!-- 万能险链接短信批处理-->
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.UpdateUniversalSMSServiceImpl" id="PA_updateUniversalSMSServiceImpl">
	    <property name="universalSmsDao" ref="PA_universalSmsDao"/>	
	</bean>
	
	<!-- 贷款起息日批处理 -->
	<bean class="com.nci.tunan.pa.batch.intereststartdatebatch.InterestStartDateJob" id="PA_InterestStartDateJob">
	    <property name="interestStartDateService" ref="PA_InterestStartDateService"/>	
	</bean>
	
	<!-- 贷款起息日批处理Service -->
	<bean class="com.nci.tunan.pa.batch.intereststartdatebatch.service.impl.InterestStartDateServiceImpl" id="PA_InterestStartDateService">
	    <property name="interestStartDateDao" ref="PA_interestStartDateDao"/>	
	    <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>	
	    <property name="calculateCashValueService" ref="PA_calcCashValueService"/>	
	    <property name="csPolicyAccountStreamDao" ref="PA_csPolicyAccountStreamDao"/>	
	</bean>
	
	<!-- 万能险初始化批处理 -->
	<bean class="com.nci.tunan.pa.batch.universalinsuranceinitialize.UniversalInsuranceInitializeJob" id="PA_UniversalInsuranceInitializeJob">
	    <property name="universalInsuranceInitializeService" ref="PA_UniversalInsuranceInitializeService"/>	
	</bean>
	
	<!-- 万能险初始化批处理Service -->
	<bean class="com.nci.tunan.pa.batch.universalinsuranceinitialize.service.impl.UniversalInsuranceInitializeServiceImpl" id="PA_UniversalInsuranceInitializeService">
	    <property name="universalInsuranceInitializeDao" ref="PA_universalInsuranceInitializeDao"/>	
	    <property name="fundSettlementDao" ref="PA_fundSettlementDao"/>	
	    <property name="noticeDao" ref="PA_noticeDao"/>	
	    <property name="noticeService" ref="PA_noticeService"/>	
	</bean>
	
	<!-- ODS批量保单文件读取批处理 -->
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.ODSBatchPolicyFileReadingJob" id="PA_ODSBatchPolicyFileReadingJob">
	    <property name="odsBatchPolicyFileReadingService" ref="PA_ODSBatchPolicyFileReadingService"/>	
	</bean>
	
	<!-- ODS批量保单文件读取批处理Service -->
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.service.impl.ODSBatchPolicyFileReadingServiceImpl" id="PA_ODSBatchPolicyFileReadingService">
	    <property name="ftpServerConfig" ref="PA_cashValueFtpServerConfig"/>
	    <property name="taskCashValueDao" ref="PA_taskCashValueDao"/>
	    <property name="cvTxInfoMainDao" ref="PA_cvTxInfoMainDao"/>
	    <property name="cashvalueFlagDao" ref="PA_cashvalueFlagDao"/>
	</bean>	
	
	<!-- ODS批量保单现价计算批处理 -->
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.ODSBatchPolicyCashValueJob" id="PA_ODSBatchPolicyCashValueJob">
	    <property name="odsBatchPolicyCashValueService" ref="PA_ODSBatchPolicyCashValueService"/>	
	</bean>
	
	<!-- ODS批量保单现价计算批处理Service -->
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.service.impl.ODSBatchPolicyCashValueServiceImpl" id="PA_ODSBatchPolicyCashValueService">
	    <property name="taskCashValueDao" ref="PA_taskCashValueDao"/>
	    <property name="taskCashValueBDao" ref="PA_taskCashValueBDao"/>
	    <property name="itemCashvalueDao" ref="PA_itemCashvalueDao"/>
	    <property name="calculateCashValueService" ref="PA_calcCashValueService"/>
	</bean>	
	
	<!-- ABS现价计算请求文件读取批处理 -->
	<bean class="com.nci.tunan.pa.batch.abscashval.ABSCashValFileDealJob" id="PA_ABSCashValFileDealJob">
	    <property name="absCashValFileDealService" ref="PA_ABSCashValFileDealService"/>	
	</bean>
	
	<!-- ABS现价计算请求文件读取批处理Service -->
	<bean class="com.nci.tunan.pa.batch.abscashval.service.impl.ABSCashValFileDealServiceImpl" id="PA_ABSCashValFileDealService">
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="absCashTaskMainDao" ref="PA_absCashTaskMainDao"/>
	    <property name="absCashTaskDetailDao" ref="PA_absCashTaskDetailDao"/>
	</bean>
	
	<!-- ABS现价计算批处理 -->
	<bean class="com.nci.tunan.pa.batch.abscashval.ABSCashValCalob" id="PA_ABSCashValCalob">
	    <property name="absCashValCalService" ref="PA_absCashValCalService"/>	
	</bean>
	
	<!-- ABS现价计算批处理Service -->
	<bean class="com.nci.tunan.pa.batch.abscashval.service.impl.ABSCashValCalServiceImpl" id="PA_absCashValCalService">
		<property name="absCashTaskDetailDao" ref="PA_absCashTaskDetailDao"/>
		<property name="absCashValueDao" ref="PA_absCashValueDao"/>
		<property name="userotherDao" ref="PA_userUdmpDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
	</bean>	
	
	<!-- 签单日生成生存金-年金计划批处理 -->
	<bean class="com.nci.tunan.pa.batch.createpayplan.CreatePayPlanJob" id="PA_CreatePayPlanJob">
	    <property name="createPayPlanService" ref="PA_createPayPlanService"/>	
	</bean>
	
	<!-- 签单日生成生存金-年金计划批处理Service -->
	<bean class="com.nci.tunan.pa.batch.createpayplan.service.impl.CreatePayPlanServiceImpl" id="PA_createPayPlanService">
		<property name="payPlanCreateTaskDao" ref="PA_payPlanCreateTaskDao"/>
		<property name="calcSAMService" ref="PA_calcSAMService"/>	
		<property name="surAnnuityMaturityService" ref="PA_surAnnuityMaturityService"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>	
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>		
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>	
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="liabSurveryRuleDao" ref="PA_liabSurveryRuleDao"/>
	</bean>	
	
	<!-- 暂缓状态批处理 -->
	<bean class="com.nci.tunan.pa.batch.pausestauts.PauseStatusJob" id="PA_PauseStatusJob">
	    <property name="pauseStatusService" ref="PA_pauseStatusService"/>	
	</bean>
	<!-- 暂缓状态批处理Service -->
	<bean class="com.nci.tunan.pa.batch.pausestauts.service.impl.PauseStatusServiceImpl" id="PA_pauseStatusService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>	
	
			<!-- 柜面渠道保单自动推送收展部批作业 -->
	<bean class="com.nci.tunan.pa.batch.websiteToCusService.websiteToCusServiceJob" id="PA_websiteToCusServiceJob" scope="prototype">
		<property name="websiteToCusServiceService" ref="PA_websiteToCusServiceService"/>
		<property name="websiteToCusServiceftpServerConfig" ref="PA_websiteToCusServiceftpServerConfig"/>
	</bean>	
		
		<!-- 大地和产代寿分配批处理 -->
	<bean class="com.nci.tunan.pa.batch.IntermedOrphPolDeal.IntermedOrphPolDealJob" id="PA_intermedOrphPolDealJob" scope="prototype">
		<property name="orphanPolicyDealService" ref="PA_orphanPolicyDealService"/>		
		<property name="intermedOrphPolDealFtpServerConfig" ref="PA_intermedOrphPolDealFtpServerConfig"/>
	</bean>
<!-- qinhb BOX批处理job -->
	<bean class="com.nci.tunan.pa.batch.BoxPositionBatch.BoxPositionJob" id="BOX_boxPositionJob" scope="prototype">
		<property name="boxPositionBatchService" ref="BOX_boxPositionBatchService"/>
	</bean>
		<!-- qinhb BOX批处理Service -->
	<bean class="com.nci.tunan.pa.batch.BoxPositionBatch.service.impl.BoxPositionBatchServiceImpl" id="BOX_boxPositionBatchService">
	    <property name="boxPositionBatchDao" ref="BOX_boxPositionBatchDao"/>
	    <property name="boxPositionDao" ref="PA_BoxPositionDao"></property>
	    <property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
	</bean>
		<!-- qinhb BOX客户批处理job -->
	<bean class="com.nci.tunan.pa.batch.BoxCustomer.BoxCustomerJob" id="BOX_boxCustomerJob" scope="prototype">
		<property name="boxCustomerBatchService" ref="BOX_boxCustomerBatchService"/>
	</bean>
		<!-- qinhb BOX批处理Service -->
	<bean class="com.nci.tunan.pa.batch.BoxCustomer.service.impl.BoxCustomerBatchServiceImpl" id="BOX_boxCustomerBatchService">
	    <property name="boxCustomerBatchDao" ref="BOX_boxCustomerBatchDao"/>
	    <property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
	    <property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	    <property name="fundTransDao" ref="PA_fundTransDao"/>
	    <property name="boxPositionDao" ref="PA_BoxPositionDao"></property>
	    <property name="fundAssetsDao" ref="PA_fundAssetsDao"/>
	</bean>
	<!-- 新核心万能状态报告书批处理Job -->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.NewUniversalInsuranceStatusReportJob" id="PA_NewUniversalInsuranceStatusReportJob">
	    <property name="newUniversalInsuranceService" ref="PA_NewUniversalInsuranceService"/>
	</bean>
	<!-- 新核心万能状态报告书批处理Service -->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.service.impl.NewUniversalInsuranceServiceImpl" id="PA_NewUniversalInsuranceService">
	    <property name="universalSmsDao" ref="PA_universalSmsDao"/>
	    <property name="universalBusiInfoDao" ref="PA_universalBusiInfoDao"/>
	    <property name="noticeService" ref="PA_noticeService"/>
	</bean>	
	<!-- 万能险多账户报告书批处理Job -->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.MultiAccountReportJob" id="PA_MultiAccountReportJob">
	    <property name="multiAccountReportService" ref="PA_MultiAccountReportService"/>
	    <property name="newUniversalInsuranceService" ref="PA_NewUniversalInsuranceService"/>
	</bean>
	<!-- 万能险多账户报告书批处理Service -->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.service.impl.MultiAccountReportServiceImpl" id="PA_MultiAccountReportService">
	    <property name="universalSmsDao" ref="PA_universalSmsDao"/>
	    <property name="noticeService" ref="PA_noticeService"/>
	    <property name="payDueDao" ref="PA_payDueDao"/>
	</bean>	
	<!-- 万能险终止结算状态报告书批处理Job -->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.EndAccountReportJob" id="PA_EndAccountReportJob">
	    <property name="endAccountReportService" ref="PA_EndAccountReportService"/>
	</bean>
	<!-- 万能险终止结算状态报告书批处理Service -->
	<bean class="com.nci.tunan.pa.batch.ilpuchargedeductionnotice.service.impl.EndAccountReportServiceImpl" id="PA_EndAccountReportService">
	    <property name="multiAccountReportService" ref="PA_MultiAccountReportService"/>
	    <property name="settleReportDao" ref="PA_settleReportDao"/>
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<!-- 4.3.32.1 广州市社会救助的申请人核对查询测试类  -->
	<bean class="com.nci.tunan.pa.batch.SocialAssistanceCheckQuery.SocialAssistanceCheckQueryTestJob" id="PA_socialAssistanceCheckQueryTestJob" scope="prototype">
	</bean>	
	<!-- 4.3.32.1 广州市社会救助的申请人核对查询  -->
	<bean class="com.nci.tunan.pa.batch.SocialAssistanceCheckQuery.SocialAssistanceCheckQueryJob" id="PA_socialAssistanceCheckQueryJob" scope="prototype">
	    <property name="socialAssistanceCheckService" ref="PA_socialAssistanceCheckService"/>
	</bean>	
	<!-- 4.3.32.2 广州市社会救助的申请人核对响应 -->
	<bean class="com.nci.tunan.pa.batch.SocialAssistanceCheckQuery.SocialAssistanceCheckResponseJob" id="PA_socialAssistanceCheckResponseJob" scope="prototype">
	    <property name="socialAssistanceCheckService" ref="PA_socialAssistanceCheckService"/>
	</bean>	
	<bean class="com.nci.tunan.pa.batch.SocialAssistanceCheckQuery.Service.impl.SocialAssistanceCheckServiceImpl" id="PA_socialAssistanceCheckService">
	     <property name="socialAssistanceCheckDao" ref="PA_socialAssistanceCheckDao"/>
	</bean>
		<bean class="com.nci.tunan.pa.batch.CommisionStatusFetch.CommisionStatusFetchJob" id="PA_commisionStatusFetchJob" scope="prototype">
	    <property name="commisionStatusFetchService" ref="PA_commisionStatusFetchService"/>
	</bean>	
	
    <!-- 续期失效保单补跑贷款中止 -->
    <bean class="com.nci.tunan.pa.batch.RenewalExpireAbort.RenewalExpireAbortJob" id="PA_renewalExpireAbortJob" scope="prototype">
	    <property name="renewalExpireAbortService" ref="PA_renewalExpireAbortService"/>
	</bean>	
	<bean class="com.nci.tunan.pa.batch.RenewalExpireAbort.service.impl.RenewalExpireAbortServiceImpl" id="PA_renewalExpireAbortService">
	     <property name="renewalExpireAbortDao" ref="PA_renewalExpireAbortDao"/>
	     <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	     <property name="calcCashValueService" ref="PA_calcCashValueService"/>
	     <property name="contractProductDao" ref="PA_contractProductDao"/>
	     <property name="noticeService" ref="PA_noticeService"/>
	     <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
	     <property name="policyLogService" ref="PA_policyLogService"/>
	     <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	     <property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
	</bean>
	<!-- 人员异动文件读取批处理Job -->
	<bean class="com.nci.tunan.pa.batch.Internaltransfer.PersonnelTurnoverFileReadingJob" id="PA_personnelTurnoverFileReadingJob" scope="prototype">
	    <property name="personnelTurnoverService" ref="PA_personnelTurnoverService"/>
	</bean>	
	<bean class="com.nci.tunan.pa.batch.Internaltransfer.service.impl.PersonnelTurnoverServiceImpl" id="PA_personnelTurnoverService" scope="prototype">
	    <property name="personnelTurnoverDao" ref="PA_personnelTurnoverDao"/>
	    <property name="orphanFtpServerConfig" ref="PA_orphanFtpServerConfig"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.Internaltransfer.dao.impl.PersonnelTurnoverDaoImpl" id="PA_personnelTurnoverDao" parent="baseDao" scope="prototype"></bean>	
	<!-- 渠道人员异动批处理Job -->
	<bean class="com.nci.tunan.pa.batch.Internaltransfer.PersonnelTurnoverJob" id="PA_personnelTurnoverJob" scope="prototype">
	    <property name="personnelTurnoverService" ref="PA_personnelTurnoverService"/>
	    <property name="orphanPolicyDealService" ref="PA_orphanPolicyDealService"/>	
	</bean>	
	<!-- 个险续期缴费成功通知批处理Job -->
	<bean class="com.nci.tunan.pa.batch.renewalSMS.SendRenewalSuccARiskJob" id="PA_SendRenewalSuccARiskJob" scope="prototype">
	    <property name="renewalSMSService" ref="PA_renewalSMSService"/>
	</bean>
	
	<!-- 投保人生日祝福短信批处理Job -->
	<bean class="com.nci.tunan.pa.batch.sendnote.SendHolderBirthdaySMSJob" id="PA_SendHolderBirthdaySMSJob" scope="prototype">
	    <property name="sendNoteAnnuityService" ref="SendNoteAnnuityService"/>
	</bean>	
	
	<!-- 个险短信四次催缴通知批处理Job -->
	<bean class="com.nci.tunan.pa.batch.renewalSMS.SendRenewalCallARiskFourJob" id="PA_SendRenewalCallARiskFourJob" scope="prototype">
	    <property name="renewalSMSService" ref="PA_renewalSMSService"/>
	</bean>	
	<!-- 保单服务人员变更短信提醒批处理Job -->
	<bean class="com.nci.tunan.pa.batch.sendnote.SendPlySerPeoChgNoteJob" id="PA_SendPlySerPeoChgNoteJob" scope="prototype">
	    <property name="noteAnnuityService" ref="SendNoteAnnuityService"/>
	</bean>		
	<!-- 保单预失效提醒批处理Job -->
	<bean class="com.nci.tunan.pa.batch.sendnote.SendLapseRemindNoteJob" id="PA_SendLapseRemindNoteJob" scope="prototype">
	    <property name="sendNoteStateService" ref="PA_sendNoteStateServiceImpl"/>
	</bean>	
    
    <!--短期险自助重投首次提醒短信个险Job -->
    <bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.StPrdReinsuredMsgFirstPerJob" id="PA_stPrdReinsuredMsgFirstPerJob" scope="prototype">
		<property name="stPrdReinsuredMsgFirstPerService" ref="PA_stPrdReinsuredMsgFirstPerService"/>
	</bean>
	<!-- 短期险自助重投首次提醒短信个险Service -->
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.service.impl.StPrdReinsuredMsgFirstPerServiceImpl" id="PA_stPrdReinsuredMsgFirstPerService">
	    <property name="stPrdReinsuredMsgDao" ref="PA_stPrdReinsuredMsgDao"/>
	    <property name="prdIAS" ref="PA_prdIAS"/>
	    <property name="businessProductDao" ref="PA_businessProductDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	    <property name="insuredListDao" ref="PA_insuredListDao"/>
	    <property name="customerDao" ref="PA_customerDao"/>
	</bean>
	
	 <!--短期险自助重投二次提醒Job类 -->
    <bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.StPrdReinsuredMsgSecondPerJob" id="PA_stPrdReinsuredMsgSecondPerJob" scope="prototype">
		<property name="stPrdReinsuredMsgFirstPerService" ref="PA_stPrdReinsuredMsgFirstPerService"/>
	</bean>
	 <!--短期险自助重投首次提醒短信银代Job类 -->
    <bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.StBankReinsuredMsgFirstPerJob" id="PA_stBankReinsuredMsgFirstPerJob" scope="prototype">
		<property name="stPrdReinsuredMsgFirstPerService" ref="PA_stPrdReinsuredMsgFirstPerService"/>
	</bean>
	<!-- 续保险种转换到期划款提醒Job -->
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.RenewalRiskSwitchTransferMsgJob" id="PA_renewalRiskSwitchTransferMsgJob">
		<property name="renewalRiskSwitchTransferMsgService" ref="PA_renewalRiskSwitchTransferMsgService"/>
	</bean>
	<!-- 续保险种转换到期划款提醒Service -->
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.service.impl.RenewalRiskSwitchTransferMsgServiceImpl" id="PA_renewalRiskSwitchTransferMsgService">
		<property name="renewalRiskSwitchTransferMsgDao" ref="PA_renewalRiskSwitchTransferMsgDao"/>
	</bean>
	<!-- 宽限期内未确认重新投保/转保险种预失效提醒短信 -->
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.GracePeriodPreLoseEffeJob" id="PA_gracePeriodPreLoseEffeJob">
		<property name="gracePeriodPreLoseEffeService" ref="PA_gracePeriodPreLoseEffeService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.service.impl.GracePeriodPreLoseEffeServiceImpl" id="PA_gracePeriodPreLoseEffeService">
		<property name="gracePeriodSMSDao" ref="PA_gracePeriodSMSDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	<!-- 宽限期内未确认重新投保/转保险种预失效提醒短信 -->
	<!-- 宽限期内未确认重新投保/转保险种终止通知短信 -->
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.GracePeriodTerminJob" id="PA_gracePeriodTerminJob">
		<property name="gracePeriodTerminService" ref="PA_gracePeriodTerminService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.service.impl.GracePeriodTerminServiceImpl" id="PA_gracePeriodTerminService">
		<property name="gracePeriodSMSDao" ref="PA_gracePeriodSMSDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	<!-- 宽限期内未确认重新投保/转保险种终止通知短信 -->
	
	<!-- 取消短期健康险应收批处理 -->
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.CancelHealthInsuranceReceivableJob" id="PA_cancelHealthInsuranceReceivableJob">
		<property name="cancelHealthInsuranceService" ref="PA_cancelHealthInsuranceService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.service.impl.CancelHealthInsuranceServiceImpl" id="PA_cancelHealthInsuranceService">
		<property name="automaticRenewalExtractionDao" ref="PA_autoextraDao"/>
		<property name="renewExtraService" ref="PA_extraService"/>
	</bean>
	
	<!-- 保单已失效一年提醒Job -->
	<bean class="com.nci.tunan.pa.batch.policyReminderForOneYear.PolicyReminderForOneYearJob" id="PA_policyReminderForOneYearJob" scope="prototype">
		<property name="policyReminderForOneYearService" ref="PA_policyReminderForOneYearService"/>
	</bean>
	<!-- 保单已失效一年提醒Service -->
	<bean class="com.nci.tunan.pa.batch.policyReminderForOneYear.service.impl.PolicyReminderForOneYearServiceImpl" id="PA_policyReminderForOneYearService">
		<property name="policyReminderForOneYearDao" ref="PA_policyReminderForOneYearDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	
	<!-- 万能账户扣费进入划款期后提醒Job -->
	<bean class="com.nci.tunan.pa.batch.universalAccountEntersPeriodAfter.UniversalAccountEntersPeriodAfterJob" id="PA_universalAccountEntersPeriodAfterJob" scope="prototype">
		<property name="universalAccountEntersPeriodAfterService" ref="PA_universalAccountEntersPeriodAfterService"/>
	</bean>

	<!-- 万能账户扣费进入划款期后提醒Service -->
	<bean class="com.nci.tunan.pa.batch.universalAccountEntersPeriodAfter.service.impl.UniversalAccountEntersPeriodAfterServiceImpl" id="PA_universalAccountEntersPeriodAfterService">
		<property name="universalAccountEntersPeriodAfterDao" ref="PA_universalAccountEntersPeriodAfterDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>

	<!-- 万能账户扣费成功后，次日发送短信提示Job -->
	<bean class="com.nci.tunan.pa.batch.universalAccountDeductionSuccess.UniversalAccountDeductionSuccessJob" id="PA_universalAccountDeductionSuccessJob" scope="prototype">
		<property name="universalAccountDeductionSuccessService" ref="PA_universalAccountDeductionSuccessService"/>
	</bean>
	<!-- 万能账户扣费成功后，次日发送短信提示Service -->
	<bean class="com.nci.tunan.pa.batch.universalAccountDeductionSuccess.service.impl.UniversalAccountDeductionSuccessServiceImpl" id="PA_universalAccountDeductionSuccessService">
		<property name="universalAccountDeductionSuccessDao" ref="PA_universalAccountDeductionSuccessDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	
	<!--上海医保账户重投首次提醒短信Job -->
	<bean class="com.nci.tunan.pa.batch.medicalMsg.MedicalMsgFirstJob" id="PA_medicalMsgFirstJob" scope="prototype">
		<property name="medicalMsgService" ref="PA_medicalMsgService"/>
	</bean>
	<!--上海医保账户重投二次提醒短信Job -->
	<bean class="com.nci.tunan.pa.batch.medicalMsg.MedicalMsgSecondJob" id="PA_medicalMsgSecondJob" scope="prototype">
		<property name="medicalMsgService" ref="PA_medicalMsgService"/>
	</bean>
	<!--上海医保账户重投失败提醒短信Job -->
	<bean class="com.nci.tunan.pa.batch.medicalMsg.ReMedicalFaildMsgJob" id="PA_reMedicalFaildMsgJob" scope="prototype">
		<property name="medicalMsgService" ref="PA_medicalMsgService"/>
	</bean>
	<!--上海医保续保提醒短信优化批处理Job -->
	<bean class="com.nci.tunan.pa.batch.medicalMsg.MedicalMsgOptimizeJob" id="PA_medicalMsgOptimizeJob" scope="prototype">
		<property name="medicalMsgService" ref="PA_medicalMsgService"/>
	</bean>
	<!-- 上海医保账户重投提醒短信Service -->
	<bean class="com.nci.tunan.pa.batch.medicalMsg.service.impl.MedicalMsgServiceImpl" id="PA_medicalMsgService">
		<property name="medicalMsgDao" ref="PA_medicalMsgDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="prdService" ref="PA_prdIAS"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<!--上海医保重新投保请求批处理Job -->
	<bean class="com.nci.tunan.pa.batch.canReinsure.CanReinsureJob" id="PA_canReinsureJob" scope="prototype">
		<property name="canReinsureService" ref="PA_canReinsureService"/>
	</bean>
	<!-- 上海医保重新投保请求批处理Service -->
	<bean class="com.nci.tunan.pa.batch.canReinsure.service.impl.CanReinsureServiceImpl" id="PA_canReinsureService">
		<property name="canReinsureDao" ref="PA_canReinsureDao"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="nbService" ref="PA_nbIAS"/>
		<property name="medicalMsgService" ref="PA_medicalMsgService"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
		<property name="questionaireInfoDao" ref="PA_paquestionaireInfoDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="medicalMsgDao" ref="PA_medicalMsgDao"/>
		<property name="shMedicalLogDao" ref="CS_shMedicalLogDao"/>
	</bean>
	<!--上海医保续保确认信息同步上传（E04-保险公司与平台交互接口）Job -->
	<bean class="com.nci.tunan.pa.batch.shrenewalUploadE04.ShRenewalUploadJob" id="PA_shRenewalUploadJob" scope="prototype">
		<property name="shRenewalUploadService" ref="PA_shRenewalUploadService"/>
	</bean>
	<!--上海医保续保确认信息同步上传（E04-保险公司与平台交互接口）Service -->
	<bean class="com.nci.tunan.pa.batch.shrenewalUploadE04.service.impl.ShRenewalUploadServiceImpl" id="PA_shRenewalUploadService" scope="prototype">
		<property name="canReinsureDao" ref="PA_canReinsureDao"/>
		<property name="shMedicalLogDao" ref="CS_shMedicalLogDao"/>
	</bean>
	<!--划款期结束-养老型保险批处理 Job -->
	<bean class="com.nci.tunan.pa.batch.automaticextra.EndOfTransferPeriodJob" id="PA_endOfTransferPeriodJob" scope="prototype">
		<property name="endOfTransferPeriodService" ref="PA_endOfTransferPeriodService"/>
		<property name="normalLapseService" ref="PA_normalLapseService"/>
	</bean>
	<!--划款期结束-养老型保险批处理 Service -->
	<bean class="com.nci.tunan.pa.batch.automaticextra.service.impl.EndOfTransferPeriodServiceImpl" id="PA_endOfTransferPeriodService" scope="prototype">
		<property name="renewAutomaticExtraDao" ref="PA_automaticExtraDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>
	</bean>
	<!--险种销售额度累计批处理Job -->
	<bean class="com.nci.tunan.pa.batch.busiProdSalesAmountAcc.BusiProdSalesAmountAccJob" id="PA_busiProdSalesAmountAccJob" scope="prototype">
		<property name="busiProdSalesAmountAccService" ref="PA_busiProdSalesAmountAccService"/>
	</bean>
	<!--险种销售额度累计批处理 -->
	<bean class="com.nci.tunan.pa.batch.busiProdSalesAmountAcc.service.impl.BusiProdSalesAmountAccServiceImpl" id="PA_busiProdSalesAmountAccService" scope="prototype">
		<property name="salseAmountCfgDao" ref="PA_salseAmountCfgDao"/>
		<property name="salesAmountDao" ref="PA_salesAmountDao"/>
		<property name="busiProdSalesAmountAccDao" ref="PA_busiProdSalesAmountAccDao"/>
		<property name="warnMailInfoDao" ref="PA_warnMailInfoDao" />
	</bean>
	<!--关联保单抵扣保费批处理Job -->
	<bean class="com.nci.tunan.pa.batch.automaticextra.AccountValueTORenewalPremJob" id="PA_accountValueTORenewalPremJob" scope="prototype">
		<property name="relationPolicyDeductionPremiumService" ref="PA_accountValueTORenewalPremService"/>
	</bean>
	<!--关联保单抵扣保费批处理 -->
	<bean class="com.nci.tunan.pa.batch.automaticextra.service.impl.AccountValueTORenewalPremServiceImpl" id="PA_accountValueTORenewalPremService" scope="prototype">
		<property name="accountValueTORenewalPremDao" ref="PA_accountValueTORenewalPremDao"/>
		<property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="renewAutomaticExtraDao" ref="PA_automaticExtraDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="accValToPremTraceDao" ref="PA_accValToPremTraceDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	<!--广州市社会救助的申请人保单的险种维度现价计算批处理 -->
	<bean class="com.nci.tunan.pa.batch.SocialAssistanceCheckQuery.DatacenterCashTaskJob" id="PA_datacenterCashTaskJob" scope="prototype">
	    <property name="socialAssistanceCheckService" ref="PA_socialAssistanceCheckService"/>
	</bean>
	<!--普通失效后现价计算批处理 -->
	<bean class="com.nci.tunan.pa.batch.cashvaluecalculate.CalCashValueJob" id="PA_calCashValueJob" scope="prototype">
	    <property name="cashValueCalculateService" ref="PA_CashValueCalculateService"/>
	</bean>
	<!--失效预终止通知书批处理Job -->
	<bean class="com.nci.tunan.pa.batch.sendnote.PreTerminationNoticeJob" id="PA_preTerminationNoticeJob" scope="prototype">
		<property name="preTerminationNoticeService" ref="PA_preTerminationNoticeService"/>
	</bean>
	<!--失效预终止通知书批处理 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.PreTerminationNoticeServiceImpl" id="PA_preTerminationNoticeService" scope="prototype">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="noticeService" ref="PA_noticeService"/>
	</bean>	
	<!--信托获取令牌提交保单信息批作业Job-->
	<bean class="com.nci.tunan.pa.batch.InsuranceInformationSubmitted.SubmissionOftrustJob" id="PA_SubmissionOftrustJob" scope="prototype">
		<property name="SubmissionOftrustservice" ref="PA_SubmissionOftrustservice"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="csContractBeneDao" ref="PA_csContractBeneDao"/>
		<property name="csContractMasterDao" ref="PA_csContractMasterDao"/>
		<property name="jobCodeDao" ref="PA_jobCodeDao"/>
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
		
		<property name="XTHQLPJK" value="${XTHQLPJK}"/>
		<property name="XTPLSSBDXX" value="${XTPLSSBDXX}"/>
		<property name="TOKENNAME" value="${TOKENNAME}"/>
		<property name="TOKENPASSWORD" value="${TOKENPASSWORD}"/>
		<property name="XHBXGSBH" value="${XHBXGSBH}"/>
	</bean>
	
	
	<!--信托发送短信批处理-->
	
	<bean class="com.nci.tunan.pa.batch.TrustSMSsending.TrustSMSsendingJob" id="PA_TrustSMSsendingJob" scope="prototype">
		<property name="TrustSMSsendingservice" ref="PA_TrustSMSsendingservice"/>
		
	</bean>
	
	<!--信托发送短信批处理service -->
	<bean class="com.nci.tunan.pa.batch.TrustSMSsending.service.impl.TrustSMSsendingserviceImpl" id="PA_TrustSMSsendingservice">
		<property name="icstrustsubmittaskdao" ref="PA_CsTrustSubmitTaskDao"/>
	</bean>
	
	<!--托获取令牌提交保单信息批作业service -->
	<bean class="com.nci.tunan.pa.batch.InsuranceInformationSubmitted.service.impl.SubmissionOftrustserviceImpl" id="PA_SubmissionOftrustservice">
		<property name="icstrustsubmittaskdao" ref="PA_CsTrustSubmitTaskDao"/>
	</bean>
	
	 	<!--信托获取令牌上传文件批作业Job-->
	<bean class="com.nci.tunan.pa.batch.trustSubmittedbatchUpload.trustSubmittedbatchUploadJob" id="PA_trustSubmittedbatchUploadJob" scope="prototype">
		<property name="trustsubmittedbatchuploadservice" ref="PA_trustsubmittedbatchuploadservice"/>
		<property name="cssSignInfomationDao" ref="PA_cssSignInfomationDao"/>
		<property name="cssInvoicesPDPrintService" ref="PA_cssInvoicesPDPrintService"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/> 
		
		<property name="XTHQLPJK" value="${XTHQLPJK}"/>
		<property name="XTPLSSBDWJ" value="${XTPLSSBDWJ}"/>
		<property name="TOKENNAME" value="${TOKENNAME}"/>
		<property name="TOKENPASSWORD" value="${TOKENPASSWORD}"/>
		<property name="XHBXGSBH" value="${XHBXGSBH}"/>
	</bean>
	
	<!--托获取令牌上传文件批作业service -->
	<bean class="com.nci.tunan.pa.batch.trustSubmittedbatchUpload.service.Impl.trustSubmittedbatchUploadserviceImpl" id="PA_trustsubmittedbatchuploadservice">
		<property name="icstrustsubmittaskdao" ref="PA_CsTrustSubmitTaskDao"/>
	</bean>
	
	<!--信托service -->
	<bean class="com.nci.tunan.cs.impl.cssprint.service.impl.CssInvoicesPDPrintServiceImpl" id="PA_cssInvoicesPDPrintService">
		<property name="cssSignInfomationDao" ref="PA_cssSignInfomationDao"/>
	</bean>
	
	<!-- 保后调查上传批处理 -->
	<bean class="com.nci.tunan.pa.batch.riskScoreConfig.RiskScoreConfigJob" id="PA_riskScoreConfigJob">
	</bean>
	
	<!-- 保后调查下载批处理 -->
	<bean class="com.nci.tunan.pa.batch.riskScoreConfig.RiskScoreConfigReadJob" id="PA_riskScoreConfigReadJob">
	</bean>
	<!-- 团险续期缴费短信提醒Job -->
	<bean class="com.nci.tunan.pa.batch.groupRenewalPayMsg.GroupRenewalPayMsgJob"  id="PA_groupRenewalPayMsgJob" scope = "prototype">
	   <property name="groupRenPayMsgService" ref="PA_groupRenPayMsgService"/>
	</bean>
	<!-- 团险续期缴费成功短信提醒Job -->
	<bean class="com.nci.tunan.pa.batch.groupRenewalPayMsg.GroupRenewalPayMsgSuccJob"  id="PA_groupRenewalPaySuccMsgJob" scope = "prototype">
	   <property name="groupRenPayMsgService" ref="PA_groupRenPayMsgService"/>
	</bean>
	<!-- 团险保单宽限期交费提醒Job -->
	<bean class="com.nci.tunan.pa.batch.groupRenewalPayMsg.GroupRenewalGracePayMsgJob"  id="PA_groupRenewalGracePayMsgJob" scope = "prototype">
	   <property name="groupRenPayMsgService" ref="PA_groupRenPayMsgService"/>
	</bean>
	<!-- 团险续期缴费短信提醒Service -->
	<bean class="com.nci.tunan.pa.batch.groupRenewalPayMsg.service.impl.GroupRenPayMsgServiceImpl" id="PA_groupRenPayMsgService" scope ="prototype" >
	   <property name="groupRenPayMsgDao" ref = "PA_groupRenPayMsgDao"></property>
	   <property name="medicalMsgDao" ref="PA_medicalMsgDao"/>
	   <property name="clmService" ref="PA_pa_clmService"/>
	   <property name="noticeDao" ref="PA_noticeDao"/>
	   <property name="businessProductDao" ref="PA_businessProductDao"/>
	   <property name="prdService" ref="PA_prdIAS"/>
	   <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	   <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	   <property name="icaps" ref="PA_capIAS"/>
	</bean>
	<!-- 团险续期缴费短信提醒Dao  -->
	<bean class="com.nci.tunan.pa.batch.groupRenewalPayMsg.dao.impl.GroupRenPayMsgDaoImpl" id="PA_groupRenPayMsgDao" parent="baseDao" />	
	
	<!-- 意外险加保办理提醒Job -->
	<bean class="com.nci.tunan.pa.batch.paPolicyRemindNote.PaPolicyRemindNoteJob" id="PA_paPolicyRemindNoteJob" scope="prototype">
		<property name="paPolicyRemindNoteService" ref="PA_paPolicyRemindNoteService"/>
	</bean>
	<!-- 意外险加保办理提醒Service -->
	<bean class="com.nci.tunan.pa.batch.paPolicyRemindNote.service.impl.PaPolicyRemindNoteServiceImpl" id="PA_paPolicyRemindNoteService">
		<property name="paPolicyRemindNoteDao" ref="PA_paPolicyRemindNoteDao"/>
		<property name="ywxBusiItemIdDao" ref="PA_ywxBusiItemIdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	<!-- 月交产品续期交费提醒Job -->
	<bean class="com.nci.tunan.pa.batch.monthlyProductRenewalFeeReminder.MonthlyProductRenewalFeeReminderJob" id="PA_monthlyProductRenewalFeeReminderJob" scope="prototype">
		<property name="monthlyProductRenewalFeeReminderService" ref="PA_monthlyProductRenewalFeeReminderService"/>
	</bean>
	<!-- 月交产品续期交费提醒Service -->
	<bean class="com.nci.tunan.pa.batch.monthlyProductRenewalFeeReminder.service.impl.MonthlyProductRenewalFeeReminderServiceImpl" id="PA_monthlyProductRenewalFeeReminderService">
		<property name="monthlyProductRenewalFeeReminderDao" ref="PA_monthlyProductRenewalFeeReminderDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	
	<!-- 一年期可自动续保主险续保交费提醒Job -->
	<bean class="com.nci.tunan.pa.batch.annualautorenewalprepaynote.AnnualAutoRenewalPrePayNoteJob" id="PA_annualAutoRenewalPrePayNoteJob" scope="prototype">
		<property name="annualAutoRenewalPrePayNoteService" ref="PA_annualAutoRenewalPrePayNoteService"/>
	</bean>
	<!-- 一年期可自动续保主险续保交费提醒Service -->
	<bean class="com.nci.tunan.pa.batch.annualautorenewalprepaynote.service.impl.AnnualAutoRenewalPrePayNoteServiceImpl" id="PA_annualAutoRenewalPrePayNoteService">
		<property name="annualAutoRenewalPrePayNoteDao" ref="PA_annualAutoRenewalPrePayNoteDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	<!-- 一年期可自动续保主险续保成功告知Job -->
	<bean class="com.nci.tunan.pa.batch.annualautorenewalpresuccenote.AnnualAutoRenewalPreSucceNoteJob" id="PA_annualAutoRenewalPreSucceNoteJob" scope="prototype">
		<property name="annualAutoRenewalPreSucceNoteService" ref="PA_annualAutoRenewalPreSucceNoteService"/>
	</bean>
	<!-- 一年期可自动续保主险续保成功告知Service -->
	<bean class="com.nci.tunan.pa.batch.annualautorenewalpresuccenote.service.impl.AnnualAutoRenewalPreSucceNoteServiceImpl" id="PA_annualAutoRenewalPreSucceNoteService">
		<property name="annualAutoRenewalPrePayNoteDao" ref="PA_annualAutoRenewalPrePayNoteDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	<!-- 一年期可自动续保主险宽限期提醒Job -->
	<bean class="com.nci.tunan.pa.batch.annualautorenewalpreperiodnote.AnnualAutoRenewalPrePeriodNoteJob" id="PA_annualAutoRenewalPrePeriodNoteJob" scope="prototype">
		<property name="annualAutoRenewalPrePeriodNoteService" ref="PA_annualAutoRenewalPrePeriodNoteService"/>
	</bean>
	<!-- 一年期可自动续保主险宽限期提醒Service -->
	<bean class="com.nci.tunan.pa.batch.annualautorenewalpreperiodnote.service.impl.AnnualAutoRenewalPrePeriodNoteServiceImpl" id="PA_annualAutoRenewalPrePeriodNoteService">
		<property name="annualAutoRenewalPrePayNoteDao" ref="PA_annualAutoRenewalPrePayNoteDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	
	<!-- 累积生息账户状态报告Job -->
	<bean class="com.nci.tunan.pa.batch.accountintereststarep.AccountInterestStaRepJob" id="PA_accountInterestStaRepJob" scope="prototype">
		<property name="accountInterestStaRepService" ref="PA_accountInterestStaRepService"/>
	</bean>
	<!-- 累积生息账户状态报告Service -->
	<bean class="com.nci.tunan.pa.batch.accountintereststarep.service.impl.AccountInterestStaRepServiceImpl" id="PA_accountInterestStaRepService">
		<property name="accountInterestStaRepDao" ref="PA_accountInterestStaRepDao"/>
	</bean>
	<!-- 累积生息账户状态报告Dao -->
	<bean class="com.nci.tunan.pa.batch.accountintereststarep.dao.impl.AccountInterestStaRepDaoImpl" id="PA_accountInterestStaRepDao" parent="baseDao"/>
	<!-- 分红险红利通知书清单提取批处理  begin -->
	<bean class="com.nci.tunan.pa.batch.bonusdoclist.dao.impl.BonusDocListQueryForBatchJobDaoImpl" id="PA_bonusDocListQueryForBatchJobDaoImpl" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.batch.bonusdoclist.dao.impl.BonusUdmpUserDaoImpl" id="PA_bonusUdmpUserDaoImpl" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.batch.bonusdoclist.BonusDocListJob" id="PA_bonusDocListJob" scope="prototype">
		<property name="bonusDocListService" ref="PA_bonusDocListService"/>
	</bean>
	<!-- 分红险红利通知书清单提取批处理service -->
	<bean class="com.nci.tunan.pa.batch.bonusdoclist.service.impl.BonusDocListServiceImpl" id="PA_bonusDocListService">
	    <property name="univRepListFileDao" ref="PA_univRepListFileDao"/>
		<property name="bonusUdmpUserDao" ref="PA_bonusUdmpUserDaoImpl"/>
		<property name="bonusDocListQueryForBatchJobDao" ref="PA_bonusDocListQueryForBatchJobDaoImpl"/>
	</bean>

	<!-- 个人养老业务承保报送Job -->
	<bean class="com.nci.tunan.pa.batch.bocicpolicyinfosend.BOCICPolicyInfoSendJob" id="PA_bocicPolicyInfoSendJob" scope="prototype">
		<property name="bocicPolicyInfoSendService" ref="PA_bocicPolicyInfoSendService"/>
	</bean>
	<!-- 个人养老业务承保报送Service -->
	<bean class="com.nci.tunan.pa.batch.bocicpolicyinfosend.service.impl.BOCICPolicyInfoSendServiceImpl" id="PA_bocicPolicyInfoSendService">
	</bean>
	<!-- 个人养老业务承保报送查询报送数据Service -->
	<bean class="com.nci.tunan.pa.batch.bocicpolicyinfosend.service.impl.GRYLPolicyInfoSendServiceImpl" id="PA_grylPolicyInfoSendService">
	</bean>
	<!-- 个人养老业务承保报送Dao -->
	<bean class="com.nci.tunan.pa.batch.bocicpolicyinfosend.dao.impl.BOCICPolicyInfoSendDaoImpl" id="PA_bocicPolicyInfoSendDao" parent="baseDao"/>

    <!-- 税优 交易核对信息查询请求TaxTransLogDao  -->
	<bean class="com.nci.tunan.pa.batch.cstaxtranslog.dao.impl.TaxTransLogDaoImpl" id="PA_csTaxTransLogDao" parent="baseDao"/>
    
    <!-- 税优 交易核对信息查询请求业务实现 -->
	<bean class="com.nci.tunan.pa.batch.cstaxcomparisonresult.service.impl.CSTaxComparisonServiceImpl" id="PA_csTaxComparisonService"/>
    
    <!-- 税优 保全信息业务报送Dao-->
    <bean class="com.nci.tunan.pa.batch.taxpreserverinfosend.dao.impl.PreservationInformationUploadDaoImpl" id="PA_preservationInformationUploadDao" parent="baseDao"/>
    
    <!--税优 保全信息业务报送 -->
	<bean class="com.nci.tunan.pa.batch.taxpreserverinfosend.service.impl.PreservationInformationUploadImpl" id="PA_preservationInformationUpload"/>

	<!-- 险种首期生效日补数批处理Job -->
	<bean class="com.nci.tunan.pa.batch.busisuppdata.BusiSuppDataJob" id="PA_busiSuppDataJob" scope="prototype">
		<property name="busiSuppDataService" ref="PA_busiSuppDataService"/>
	</bean>
	<!-- 险种首期生效日补数批处理Service -->
	<bean class="com.nci.tunan.pa.batch.busisuppdata.service.impl.BusiSuppDataServiceImpl" id="PA_busiSuppDataService">
		<property name="busiSuppDataDao" ref="PA_busiSuppDataDao"/>
	</bean>
	<!-- 险种首期生效日补数批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.busisuppdata.dao.impl.BusiSuppDataDaoImpl" id="PA_busiSuppDataDao" parent="baseDao"/>
	
   <!-- 需求分析任务 #131083 投连万能险账户价值不足提醒-保单管理 start -->
   	<!-- 投连万能险账户价值不足短信提醒批处理 -->
	<bean class="com.nci.tunan.pa.batch.accountvaluelessnotice.AccountValueLessNoticeJob" id="PA_AccountValueLessNoticeJob">
	    <property name="accountValueLessNoticeService" ref="PA_accountValueLessNoticeService" />
	</bean>
	
	<!-- 投连万能险账户价值不足短信提醒批处理Service -->
	<bean class="com.nci.tunan.pa.batch.accountvaluelessnotice.service.impl.AccountValueLessNoticeServiceImpl" id="PA_accountValueLessNoticeService">
	    <property name="documentDao" ref="PA_paDocumentDao" />
	    <property name="universalAccountEntersPeriodAfterDao" ref="PA_universalAccountEntersPeriodAfterDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>
	<!-- 需求分析任务 #131083 投连万能险账户价值不足提醒-保单管理 end -->
   
   	<!-- 新型产品通知接收方式补数批处理Job -->
	<bean class="com.nci.tunan.pa.batch.receiveMethodSuppData.ReceiveMethodSuppDataJob" id="PA_receiveMethodSuppDataJob" scope="prototype">
		<property name="receiveMethodSuppDataService" ref="PA_receiveMethodSuppDataService"/>
	</bean>
	<!-- 新型产品通知接收方式补数批处理Service -->
	<bean class="com.nci.tunan.pa.batch.receiveMethodSuppData.service.impl.ReceiveMethodSuppDataServiceImpl" id="PA_receiveMethodSuppDataService">
		<property name="receiveMethodSuppDataDao" ref="PA_receiveMethodSuppDataDao"/>
	</bean>
	<!-- 新型产品通知接收方式补数批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.receiveMethodSuppData.dao.impl.ReceiveMethodSuppDataDaoImpl" id="PA_receiveMethodSuppDataDao" parent="baseDao"/>
 
 	<!-- 税优平台交易核对批处理Job -->
	<bean class="com.nci.tunan.pa.batch.checkTaxTransTask.CheckTaxTransTaskJob" id="PA_checkTaxTransTaskJob" scope="prototype">
		<property name="checkTaxTransTaskService" ref="PA_checkTaxTransTaskService"/>
	</bean>
	<!-- 税优平台交易核对批处理Service -->
	<bean class="com.nci.tunan.pa.batch.checkTaxTransTask.service.impl.CheckTaxTransTaskServiceImpl" id="PA_checkTaxTransTaskService">
	</bean>
 
 	<!-- 税优保单续期报送批处理Job -->
	<bean class="com.nci.tunan.pa.batch.taxPremiumPolicyTask.TaxPremiumPolicyTaskJob" id="PA_taxPremiumPolicyTaskJob" scope="prototype">
		<property name="taxPremiumPolicyTaskService" ref="PA_taxPremiumPolicyTaskService"/>
	</bean>
	<!-- 税优保单续期报送批处理Service -->
	<bean class="com.nci.tunan.pa.batch.taxPremiumPolicyTask.service.impl.TaxPremiumPolicyTaskServiceImpl" id="PA_taxPremiumPolicyTaskService">
	</bean>
	
 	<!-- 税优保单续期应收撤销报送批处理Job -->
	<bean class="com.nci.tunan.pa.batch.taxPremiumPolicyRenewalCancel.TaxPremiumPolicyRenewalCancelJob" id="PA_taxPremiumPolicyRenewalCancelJob" scope="prototype">
		<property name="taxPremiumPolicyRenewalCancelServiceImpl" ref="PA_taxPremiumPolicyRenewalCancelServiceImpl"/>
	</bean>
	<!-- 税优保单续期应收撤销报送批处理Service -->
	<bean class="com.nci.tunan.pa.batch.taxPremiumPolicyRenewalCancel.service.Impl.TaxPremiumPolicyRenewalCancelServiceImpl" id="PA_taxPremiumPolicyRenewalCancelServiceImpl">
	</bean>
	
	<!-- 税优保单续期应收撤销报送批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.taxPremiumPolicyRenewalCancel.dao.Impl.TaxPremiumPolicyRenewalCancelDaoImpl" id="PA_taxPremiumPolicyRenewalCancelDao"  parent="baseDao">
	</bean>
	
	
	
	 <!-- 税延保单分红批处理Job -->
	<bean class="com.nci.tunan.pa.batch.taxextensionbonus.TaxExtensionBAJob" id="PA_taxExtensionBAJob" scope="prototype">
		<property name="taxExtensionBAService" ref="PA_taxExtensionBAServiceImpl"/>
	</bean>
	<!-- 税延保单分红报送批处理Service -->
	<bean class="com.nci.tunan.pa.batch.taxextensionbonus.service.impl.TaxExtensionBAServiceImpl" id="PA_taxExtensionBAServiceImpl">
		<property name="taxExtensionBADao" ref="PA_taxExtensionBADao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="universalSettlementDao" ref="PA_universalSettlementDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="fundGroupTransDao" ref="PA_FundGroupTransDao"/>
	</bean>
	<!-- 税延保单分红报送批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.taxextensionbonus.dao.impl.TaxExtensionBADaoImpl" id="PA_taxExtensionBADao" parent="baseDao"/>
 	
     <!-- 需求分析任务 #144167: 个人税收优惠型健康保险保全需求-保单状态修改上传 start-->
    <bean class="com.nci.tunan.pa.batch.policystatussubmitEND023.PolicyStatusSubmitJob" id="PA_policyStatusSubmitJob">
    	<property name="policyStatusSubmitService" ref="PA_policyStatusSubmitService"/>
	</bean>
	
    <bean class="com.nci.tunan.pa.batch.policystatussubmitEND023.service.impl.PolicyStatusSubmitServiceImpl" id="PA_policyStatusSubmitService">
	    <property name="policyStatusSubmitDao" ref="PA_policyStatusSubmitDao"/>
	    <property name="taxBusitStatusLogDao" ref="PA_taxBusitStatusLogDao"/>
	    <property name="taxProductStatusLogDao" ref="PA_taxProductStatusLogDao"/>
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="contractProductDao" ref="PA_contractProductDao"/>
	    <property name="policyChangeDao" ref="PA_policyChangeDao"/>
	    <property name="contractBusiProdLogDao" ref="PA_contractBusiProdLogDao"/>
	    <property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
	</bean>
    
    <!-- 需求分析任务 #144167: 个人税收优惠型健康保险保全需求-保单状态修改上传 end-->
    
    
   <!-- 需求分析任务 #141148 个险年金续领生调提醒需求-保单 start -->
	   	<!-- 个险年金续领生调提醒批处理 -->
		<bean class="com.nci.tunan.pa.batch.renewalcolarannuitynotice.RenewalColarAnnuityNoticeJob" id="PA_renewalColarAnnuityNoticeJob">
		    <property name="renewalColarAnnuityNoticeService" ref="PA_renewalColarAnnuityNoticeService" />
		</bean>
		
		<!-- 个险年金续领生调提醒批处理Service -->
		<bean class="com.nci.tunan.pa.batch.renewalcolarannuitynotice.service.impl.RenewalColarAnnuityNoticeServiceImpl" id="PA_renewalColarAnnuityNoticeService">
		    <property name="renewalColarAnnuityNoticeDao" ref="PA_renewalColarAnnuityNoticeDao" />
			<property name="businessProductDao" ref="PA_businessProductDao"/>
			<property name="noticeDao" ref="PA_noticeDao"/>
		</bean>
		
		<bean class="com.nci.tunan.pa.batch.renewalcolarannuitynotice.dao.impl.RenewalColarAnnuityNoticeDaoImpl" id="PA_renewalColarAnnuityNoticeDao"  parent="baseDao">
		</bean>
	<!-- 需求分析任务 #141148 个险年金续领生调提醒需求-保单 end -->
	
	
	<!-- 一年期保证续保产品保证期满前提醒——老条款 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.OneYearAssureRenewOldNoticeJob" id="PA_OneYearAssureRenewOldNoticeJob">
	    <property name="oneYearAssureRenewOldNoticeService" ref="PA_oneYearAssureRenewOldNoticeServiceImpl"/>	
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.OneYearAssureRenewOldNoticeServiceImpl" id="PA_oneYearAssureRenewOldNoticeServiceImpl" scope="prototype">
		<property name="sendNoteStateDao" ref="PA_sendNoteStateDaoImpl"/>
		<property name="ywxBusiItemIdDao" ref="PA_ywxBusiItemIdDao"/>
	</bean>
	
	<!-- 一年期保证续保产品保证期满前提醒——新条款 -->
	<bean class="com.nci.tunan.pa.batch.sendnote.OneYearAssureRenewNewNoticeJob" id="PA_OneYearAssureRenewNewNoticeJob">
	    <property name="oneYearAssureRenewNewNoticeService" ref="PA_oneYearAssureRenewNewNoticeServiceImpl"/>	
	</bean>
	<bean class="com.nci.tunan.pa.batch.sendnote.service.impl.OneYearAssureRenewNewNoticeServiceImpl" id="PA_oneYearAssureRenewNewNoticeServiceImpl" scope="prototype">
		<property name="sendNoteStateDao" ref="PA_sendNoteStateDaoImpl"/>
		<property name="ywxBusiItemIdDao" ref="PA_ywxBusiItemIdDao"/>
	</bean>
	
	
	  <!-- 需求分析任务 #144278 年金满期金应领未领定期提醒需求-保单 start -->
	   	<!-- 年金满期金应领未领定期提醒批处理 -->
		<bean class="com.nci.tunan.pa.batch.paypolicyreceivednotice.PayPolicyReceivedNoticeJob" id="PA_payPolicyReceivedNoticeJob" scope="prototype">
		    <property name="payPolicyReceivedNoticeService" ref="PA_payPolicyReceivedNoticeService" />
		</bean>
		
		<!-- 年金满期金应领未领定期提醒批处理Service -->
		<bean class="com.nci.tunan.pa.batch.paypolicyreceivednotice.service.impl.PayPolicyReceivedNoticeServiceImpl" id="PA_payPolicyReceivedNoticeService">
		    <property name="payPolicyReceivedNoticeDao" ref="PA_payPolicyReceivedNoticeDao" />
			<property name="businessProductDao" ref="PA_businessProductDao"/>
			<property name="noticeDao" ref="PA_noticeDao"/>
		</bean>
		
		<bean class="com.nci.tunan.pa.batch.paypolicyreceivednotice.dao.impl.PayPolicyReceivedNoticeDaoImpl" id="PA_payPolicyReceivedNoticeDao"  parent="baseDao">
		</bean>
	<!-- 需求分析任务 #144278 个险年金续领生调提醒需求-保单 end -->
	
		<!-- 营销部保单服务人员变更告知批处理job -->
	<bean class="com.nci.tunan.pa.batch.marketcontractagentchangenotice.MarketContractAgentChangeNoticeJob" id="PA_marketContractAgentChangeNoticeJob">
		<property name="marketContractAgentChangeNoticeService" ref="PA_marketContractAgentChangeNoticeService" />
	</bean>
	<!-- 营销部保单服务人员变更告知批处理service -->
	<bean class="com.nci.tunan.pa.batch.marketcontractagentchangenotice.service.impl.MarketContractAgentChangeNoticeServiceImpl" id="PA_marketContractAgentChangeNoticeService">
		<property name="marketContractAgentChangeNoticeDao" ref="PA_marketContractAgentChangeNoticeDao" />
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	<!-- 营销部保单服务人员变更告知批处理dao -->
	<bean class="com.nci.tunan.pa.batch.marketcontractagentchangenotice.dao.impl.MarketContractAgentChangeNoticeDaoImpl" id="PA_marketContractAgentChangeNoticeDao"  parent="baseDao">
	</bean>
	
	<!-- 保险信托2.0续期交费提醒 -->
	<bean class="com.nci.tunan.pa.batch.trustSenRenewalNotice.TrustSenRenewalNoticeJob" id="PA_TrustSenRenewalNoticeJob">
		<property name="trustSenRenewalNoticeService" ref="PA_trustSenRenewalNoticeService"/>	
	</bean>
	<bean class="com.nci.tunan.pa.batch.trustSenRenewalNotice.service.impl.TrustSenRenewalNoticeServiceImpl" id="PA_trustSenRenewalNoticeService" scope="prototype">
		<property name="trustSenRenewalNoticeDao" ref="PA_trustSenRenewalNoticeDao"/>
		<property name="ftpServerConfig" ref="PA_trustSenRenewalFtpServerConfig"/>
		<property name="capIAS" ref="PA_capIAS"/>
	</bean>
	<!-- 个险产品停止续保短信通知Job -->
	<bean class="com.nci.tunan.pa.batch.personalInsuranceProductNotRenewal.PersonalInsuranceProductNotRenewalJob" id="PA_personalInsuranceProductNotRenewalJob" scope="prototype">
		<property name="personalInsuranceProductNotRenewalService" ref="PA_personalInsuranceProductNotRenewalService"/>
	</bean>
	<!-- 个险产品停止续保短信通知Service -->
	<bean class="com.nci.tunan.pa.batch.personalInsuranceProductNotRenewal.service.Impl.PersonalInsuranceProductNotRenewalServiceImpl" id="PA_personalInsuranceProductNotRenewalService">
		<property name="personalInsuranceProductNotRenewalDao" ref="PA_personalInsuranceProductNotRenewalDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.trustSenRenewalNotice.dao.impl.TrustSenRenewalNoticeDaoImpl" id="PA_trustSenRenewalNoticeDao"  parent="baseDao"/>
	<!-- 新增保单“分配单”标识批处理job -->
	<bean class="com.nci.tunan.pa.batch.policyFlagWithAllocation.PolicyFlagWithAllocationJob" id="PA_policyFlagWithAllocationJob">
		<property name="policyFlagWithAllocationService" ref="PA_policyFlagWithAllocationService" />
	</bean>
	<!-- 新增保单“分配单”标识批处理service -->
	<bean class="com.nci.tunan.pa.batch.policyFlagWithAllocation.service.impl.PolicyFlagWithAllocationServiceImpl" id="PA_policyFlagWithAllocationService">
		<property name="contractMasterDao" ref="PA_contractMasterDao" />
		<property name="policyLogService" ref="PA_policyLogService"/>
	</bean>

		
	<!-- 需求分析任务 #147285 睡眠保单项目需求-核心系统睡眠保单标识更新需求保单 start -->
	   	<!--  睡眠保单提醒批处理 -->
		<bean class="com.nci.tunan.pa.batch.sleepypolicymark.SleepyPolicyMarkJob" id="PA_sleepyPolicyMarkJob" scope="prototype">
		    <property name="sleepyPolicyMarkService" ref="PA_sleepyPolicyMarkService" />
		</bean>
		
		<!--  睡眠保单提醒批处理Service -->
		<bean class="com.nci.tunan.pa.batch.sleepypolicymark.service.impl.SleepyPolicyMarkServiceImpl" id="PA_sleepyPolicyMarkService">
		    <property name="sleepyPolicyMarkDao" ref="PA_sleepyPolicyMarkDao" />
		    <property name="policyMarkingInfoDao" ref="PA_policyMarkingInfoDao" />
		    <property name="policyMarkingLogDao" ref="PA_policyMarkingLogDao" />
		    <property name="policyMarkingBacthInfoDao" ref="PA_policyMarkingBacthInfoDao" />
		    <!-- 计算现价 -->
		    <property name="calcCashValueService" ref="PA_calcCashValueService"/>
		    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		</bean>
		
		<bean class="com.nci.tunan.pa.batch.sleepypolicymark.dao.impl.SleepyPolicyMarkDaoImpl" id="PA_sleepyPolicyMarkDao"  parent="baseDao">
		</bean>
		
		
		<!--  睡眠保单状态更新批处理 -->
		<bean class="com.nci.tunan.pa.batch.sleepypolicystatusupdate.SleepyPolicyStatusUpdateJob" id="PA_sleepyPolicyStatusUpdateJob" scope="prototype">
		    <property name="sleepyPolicyStatusUpdateService" ref="PA_sleepyPolicyStatusUpdateService" />
		</bean>
		
		<!--  睡眠保单状态更新批处理Service -->
		<bean class="com.nci.tunan.pa.batch.sleepypolicystatusupdate.service.impl.SleepyPolicyStatusUpdateServiceImpl" id="PA_sleepyPolicyStatusUpdateService">
		    <property name="sleepyPolicyStatusUpdateDao" ref="PA_sleepyPolicyStatusUpdateDao" />
		    <property name="sleepyPolicyMarkDao" ref="PA_sleepyPolicyMarkDao" />
		    <property name="policyMarkingInfoDao" ref="PA_policyMarkingInfoDao" />
		    <property name="policyMarkingLogDao" ref="PA_policyMarkingLogDao" />
		    <property name="policyMarkingBacthInfoDao" ref="PA_policyMarkingBacthInfoDao" />
		    <!-- 计算现价 -->
		    <property name="calcCashValueService" ref="PA_calcCashValueService"/>
		    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		</bean>
		
		<bean class="com.nci.tunan.pa.batch.sleepypolicystatusupdate.dao.impl.SleepyPolicyStatusUpdateDaoImpl" id="PA_sleepyPolicyStatusUpdateDao"  parent="baseDao">
		</bean>
		
		
		
       <!--  睡眠保单数据初始化批处理 -->
		<bean class="com.nci.tunan.pa.batch.sleepypolicydatainit.SleepyPolicyDataInitJob" id="PA_sleepyPolicyDataInitJob" scope="prototype">
		    <property name="sleepyPolicyDataInitService" ref="PA_sleepyPolicyDataInitService" />
		</bean>
		
		<!-- 睡眠保单数据初始化批处理Service -->
		<bean class="com.nci.tunan.pa.batch.sleepypolicydatainit.service.impl.SleepyPolicyDataInitServiceImpl" id="PA_sleepyPolicyDataInitService">
		    <property name="sleepyPolicyDataInitDao" ref="PA_sleepyPolicyDataInitDao" />
		    <property name="policyMarkingInfoDao" ref="PA_policyMarkingInfoDao" />
		    <property name="policyMarkingLogDao" ref="PA_policyMarkingLogDao" />
		</bean>
		
		<bean class="com.nci.tunan.pa.batch.sleepypolicydatainit.dao.impl.SleepyPolicyDataInitDaoImpl" id="PA_sleepyPolicyDataInitDao"  parent="baseDao">
		</bean>
		
	<!-- 需求分析任务 #147285 睡眠保单项目需求-核心系统睡眠保单标识更新需求保单 end -->
	     
	   	<!-- 年金满期金自动累积成功告知批处理job -->
	    <bean class="com.nci.tunan.pa.batch.annuityautotransfertoaccnotice.AnnuityAutoTransferToAccNoticeJob" id="PA_annuityAutoTransferToAccNoticeJob">
		    <property name="annuityAutoTransferToAccNoticeService" ref="PA_annuityAutoTransferToAccNoticeService" />
	    </bean>  
	    <!-- 年金满期金自动累积成功告知批处理service -->
		<bean class="com.nci.tunan.pa.batch.annuityautotransfertoaccnotice.service.impl.AnnuityAutoTransferToAccNoticeServiceImpl" id="PA_annuityAutoTransferToAccNoticeService">
			<property name="annuityAutoTransferToAccNoticeDao" ref="PA_annuityAutoTransferToAccNoticeDao" />
			<property name="insuredListDao" ref="PA_insuredListDao"/>
			<property name="noticeDao" ref="PA_noticeDao"/>
		</bean>
		<!-- 年金满期金自动累积成功告知批处理dao -->
		<bean class="com.nci.tunan.pa.batch.annuityautotransfertoaccnotice.dao.impl.AnnuityAutoTransferToAccNoticeDaoImpl" id="PA_annuityAutoTransferToAccNoticeDao"  parent="baseDao">
		</bean>
	
		<!-- 续保成功通知书批处理job -->
	<bean class="com.nci.tunan.pa.batch.renewalsuccessful.RenewalSuccessfulJob" id="PA_renewalSuccessfulJob" scope="prototype">
	    <property name="renewalSuccessfulService" ref="PA_renewalSuccessfulService"/>
	</bean>	
	
	<!-- 需求分析任务 #160209 续保核保审核（保单管理） -->
	<bean class="com.nci.tunan.pa.batch.renewalunderwriting.RenewalUnderWritingJob" id="PA_RenewalUnderWritingJob" scope="prototype">
	    <property name="renewalUnderWritingService" ref="PA_renewalUnderWritingService"/>
	</bean>
	
	<!-- 非实时提数Job-->
	<bean class="com.nci.tunan.pa.batch.policyquerydelayed.PolicyQueryDelayedJob" id="PA_policyQueryDelayedJob">
	</bean>
	<!-- 非实时提数上传FTP文件的Service -->
    <bean class = "com.nci.tunan.pa.batch.policyquerydelayed.CreateFtpTxtServiceImpl" id = "PA_pqdCreateFtpTxtService">
    </bean>
    
    <!--  睡眠保单历史数据更新批处理 -->
	<bean class="com.nci.tunan.pa.batch.sleepypolicyupdatehisJob.SleepyPolicyUpdateHisJob" id="PA_sleepyPolicyUpdateHisJob" scope="prototype">
	    <property name="sleepyPolicyUpdateHisService" ref="PA_sleepyPolicyUpdateHisService" />
	</bean>
	
	<!--  睡眠保单历史数据更新批处理Service -->
	<bean class="com.nci.tunan.pa.batch.sleepypolicyupdatehisJob.service.impl.SleepyPolicyUpdateHisServiceImpl" id="PA_sleepyPolicyUpdateHisService">
	    <property name="sleepyPolicyUpdateHisDao" ref="PA_sleepyPolicyUpdateHisDao" />
	    <property name="sleepyPolicyMarkDao" ref="PA_sleepyPolicyMarkDao" />
	    <property name="policyMarkingInfoDao" ref="PA_policyMarkingInfoDao" />
	    <property name="policyMarkingLogDao" ref="PA_policyMarkingLogDao" />
	    <property name="policyMarkingBacthInfoDao" ref="PA_policyMarkingBacthInfoDao" />
	    <!-- 计算现价 -->
	    <property name="calcCashValueService" ref="PA_calcCashValueService"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<!--  睡眠保单历史数据更新批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.sleepypolicyupdatehisJob.dao.impl.SleepyPolicyUpdateHisDaoImpl" id="PA_sleepyPolicyUpdateHisDao"  parent="baseDao">
	</bean>
    
    
    <!-- 中行非实时提数Job-->
	<bean class="com.nci.tunan.pa.batch.querypolicystatus.PolicyStatusJob" id="PA_policyStatusJob">
	</bean>
	<!-- 中行非实时提数上传FTP文件的Service -->
    <bean class = "com.nci.tunan.pa.batch.querypolicystatus.CreateFtpTxtServiceImpl" id = "PA_qpsCreateFtpTxtService"></bean>
    
    <!-- 修改保单锁定标识处理 -->
    <bean class = "com.nci.tunan.pa.batch.updatepolicylockflag.UpdatePolicyLockFlagJob" id = "PA_updatePolicyLockFlagJob"></bean>

  	<!-- 贷款抵扣任务批处理 -->
    <bean class = "com.nci.tunan.pa.batch.survivalAnnuityMaturity.GenerateDeductionTaskJob" id = "PA_generateDeductionTaskJob"></bean>
    
    <!-- 派发年度红利后重新发放生存金满期金 -->
    <!-- 派发年度红利后重新发放生存金满期金Job -->
    <bean class = "com.nci.tunan.pa.batch.survivalAnnuityMaturity.AnnuityReIssueJob" id = "PA_annuityReIssueJob"></bean>
    <!-- 派发年度红利后重新发放生存金满期金Service -->
    <bean class = "com.nci.tunan.pa.batch.survivalAnnuityMaturity.service.impl.AnnuityReIssueServiceImpl" id = "PA_annuityReIssueService"></bean>

    <!-- 个险保单服务人员离职提醒短信批处理Job -->
    <bean class = "com.nci.tunan.pa.batch.agentdismissaledmsg.AgentDismissaledMsgJob" id = "PA_agentDismissaledMsgJob"></bean>
    <!-- 个险保单服务人员离职提醒短信批处理Service -->
	<bean class="com.nci.tunan.pa.batch.agentdismissaledmsg.service.impl.AgentDismissaledMsgServiceImpl" id="PA_agentDismissaledMsgService"></bean>
	<!-- 个险保单服务人员离职提醒短信批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.agentdismissaledmsg.dao.impl.AgentDismissaledMsgDaoImpl" id="PA_agentDismissaledMsgDao"  parent="baseDao"></bean>
	
	<!-- 需求分析任务 #173698银代服务人员离职告知通知需求-保单管理 start -->
   	<!-- 银代服务人员离职告知通知批处理 -->
	<bean class="com.nci.tunan.pa.batch.bankagentdismissaledmsgjob.BankAgentDismissaledMsgJob" id="PA_bankAgentDismissaledMsgJob" scope="prototype">
	    <property name="bankAgentDismissaledMsgService" ref="PA_bankAgentDismissaledMsgService" />
	</bean>
	
	<!-- 银代服务人员离职告知通知Service -->
	<bean class="com.nci.tunan.pa.batch.bankagentdismissaledmsgjob.service.impl.BankAgentDismissaledMsgServiceImpl" id="PA_bankAgentDismissaledMsgService">
	    <property name="bankAgentDismissaledMsgDao" ref="PA_bankAgentDismissaledMsgDao" />
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.batch.bankagentdismissaledmsgjob.dao.impl.BankAgentDismissaledMsgDaoImpl" id="PA_bankAgentDismissaledMsgDao"  parent="baseDao">
	</bean>
	<!-- 需求分析任务 #173698银代服务人员离职告知通知需求-保单管理 end -->
	
    <!-- 红利年金转入万能账户历史数据处理批处理Job -->
    <bean class = "com.nci.tunan.pa.batch.hispaydueupdate.HisPayDueUpdateJob" id = "PA_hisPayDueUpdateJob"></bean>
    <!-- 红利年金转入万能账户历史数据处理批处理Service -->
	<bean class="com.nci.tunan.pa.batch.hispaydueupdate.service.impl.HisPayDueUpdateServiceImpl" id="PA_hisPayDueUpdateService"></bean>
	<!-- 红利年金转入万能账户历史数据处理批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.hispaydueupdate.dao.impl.HisPayDueUpdateDaoImpl" id="PA_hisPayDueUpdateDao"  parent="baseDao"></bean>
	
</beans>