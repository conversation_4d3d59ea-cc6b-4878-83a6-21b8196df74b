<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task    http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans    http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch    http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache    http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx    http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context    http://www.springframework.org/schema/context/spring-context-3.0.xsd   http://www.springframework.org/schema/aop    http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

	
	<!-- <import resource="classpath:META-INF/pa/spring/applicationContext-dataSource.xml" /> -->
	
	
	<context:component-scan base-package="com.nci.tunan.pa"/>

<!-- 	<BEAN ID="TRANSACTIONMANAGER" -->
<!-- 		CLASS="ORG.SPRINGFRAMEWORK.JDBC.DATASOURCE.DATASOURCETRANSACTIONMANAGER"> -->
<!-- 		<PROPERTY NAME="DATASOURCE" REF="DATASOURCE" /> -->
<!-- 	</BEAN> -->

<!-- 	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean"> -->
<!-- 		<property name="dataSource" ref="dataSource" /> -->
<!-- 		<property name="configLocation" -->
<!-- 			value="classpath:/META-INF/mybatis/mybatis_configuration.xml" /> -->
<!-- 		<property name="mapperLocations"> -->
<!-- 			<list> -->
<!-- 				<value>classpath*:META-INF/**/*mapper.xml</value> -->
<!-- 			</list> -->
<!-- 		</property> -->
<!-- 	</bean> -->
	
 	<!-- 配置事务传播特性 start --> 
<!-- 	<tx:advice id="PAAdvice" transaction-manager="transactionManager"> -->
<!-- 		<tx:attributes> -->
<!-- 			<tx:method name="save*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="add*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="insert*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="update*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="modify*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="edit*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="remove*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="delete*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="find*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="get*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="query*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="load*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="manul*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 			<tx:method name="create*" propagation="REQUIRED" read-only="false" -->
<!-- 				rollback-for="java.lang.Exception" /> -->
<!-- 		</tx:attributes> -->
<!-- 	</tx:advice> -->
	<!-- 配置参与事务的类 end -->

	<!--切面 start -->
<!-- 	<aop:config> -->
<!-- 		<aop:pointcut id="allPAServiceMethod" -->
<!-- 			expression="execution(* com.nci.tunan.pa.impl.*.ucc.*.*(..))" /> -->

<!-- 		<aop:advisor pointcut-ref="allPAServiceMethod" advice-ref="PAAdvice" /> -->
<!-- 	</aop:config> -->
	<!--切面 end -->

<!-- 	<bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate" -->
<!-- 		scope="prototype"> -->
<!-- 		<constructor-arg index="0" ref="sqlSessionFactory" /> -->
<!-- 	</bean> -->
	<!-- <bean id="sqlSession" class="org.mybatis.spring.SqlSessionTemplate"> 
		<constructor-arg index="0" ref="sqlSessionFactory" /> </bean> -->
		
		
		<!-- <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="configLocation" value="classpath:META-INF/mybatis/configuration.xml"></property>
		<property name="mapperLocations">
			<list>
				<value>classpath:META-INF/common/mybatis/*mapper.xml</value>
				<value>classpath:META-INF/mybatis/*mapper.xml</value>
				<value>classpath:META-INF/mybatis/*/*mapper.xml</value>
				<value>classpath:META-INF/mybatis/*/*/*mapper.xml</value>
				<value>classpath:META-INF/mybatis/*/*/*/*mapper.xml</value>
				<value>classpath:META-INF/mybatis/*/*/*/*/*mapper.xml</value>
				<value>classpath:META-INF/mybatis/*/*/*/*/*/*mapper.xml</value>
				<value>classpath:META-INF/mybatis/*/*/*/*/*/*/*mapper.xml</value>
				<value>classpath:META-INF/pa/mybatis/*mapper.xml</value>
				<value>classpath:META-INF/pa/mybatis/*/*mapper.xml</value>
				<value>classpath:META-INF/pa/mybatis/*/*/*mapper.xml</value>
				<value>classpath:META-INF/pa/mybatis/*/*/*/*mapper.xml</value>
				<value>classpath:META-INF/pa/mybatis/*/*/*/*/*mapper.xml</value>
				<value>classpath:META-INF/pa/mybatis/*/*/*/*/*/*mapper.xml</value>
				<value>classpath:META-INF/pa/mybatis/*/*/*/*/*/*/*mapper.xml</value>
			</list>
		</property>
	</bean> -->

<!-- 	<bean class="org.springframework.beans.factory.config.PropertiesFactoryBean" id="PA_propertiesReader"> -->
<!-- 		<property name="locations"> -->
<!-- 			<list> -->
<!-- 				<value>classpath:META-INF/conf/commonbiz_common.properties</value> -->
<!-- 			</list> -->
<!-- 		</property> -->
<!-- 	</bean> -->
	
</beans>