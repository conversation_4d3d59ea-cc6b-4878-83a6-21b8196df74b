<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task    http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans    http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch    http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache    http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx    http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context    http://www.springframework.org/schema/context/spring-context-3.0.xsd   http://www.springframework.org/schema/aop    http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">
	
	<!-- 集成接口新老客户号转换Aop配置 -->
	<aop:config>
		<!-- 定义一个切入点表达式： 拦截哪些方法 -->
		<!-- #126623 入参客户号更改为身份证号不需要校验老核心客户id 
		     modify by cuiqi_wb 2023-02-02 
		|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06801001915.impl.CsHisQueryByCustomerUccImpl.*(..)) 
		-->
		<aop:pointcut id="peripheral_pt_pa"
			expression="
					execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101003119.impl.QueryLoanCountUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06801900405.impl.QueryPremUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101000494.impl.QueryContractBeneListUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101000626.impl.YDZFAnnuityPayQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101000656.impl.CustomerAccountDrawTrialUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101000688.impl.PolicyRecoveryUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101000911.impl.ProfessionChangeUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101002685.impl.CsEdorItemInfoQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00101002689.impl.CsBusiProdInfUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000053.impl.HolderAndInsuredInfoChangeUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000163.impl.OnlineChangeUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000495.impl.UpdateBenefitInformationUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000543.impl.TelephoneCenterUpdatePasswordUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000658.impl.CsEndorseAISubmitUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000690.impl.PolicyResetValidUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000692.impl.SetCustomerPasswordUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000912.impl.InsuredCateGoryUpdateUCCImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102001041.impl.HolderAndInsuredInfoChangeUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06801001918.impl.CsDetailQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06801001922.impl.CsPremArapUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06801001924.impl.CsUWconfirmQueryUCCImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06801001925.impl.CsUwQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000009.impl.QueryPolicyHolderUCCImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000011.impl.QueryPersonalPolicyDetailUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000014.impl.QueryPolicyInsuredUCCImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000015.impl.BeneInfoQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000031.impl.UniversalAccountInfoQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000042.impl.CustomerPolicyMsgQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000061.impl.InsuredPolicyQueryListUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000068.impl.HealthImpartQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000151.impl.QueryYearBonusReportUCCImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000654.impl.PolicyAccountReceiveUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000910.impl.GetInsuredOccQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000964.impl.PaPassWordIfResetUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101002246.impl.TelephoneCenterQueryPolicyInfoImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101002255.impl.PersonalPolicyInfoQueryForMMSUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101002280.impl.AdditionRiskQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101002547.impl.peopleIncomeQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101002633.impl.CustomerPolicyInfoUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101002683.impl.JointInsurerInfUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r04301000337.impl.QueryInsuredInfoByPolicyCodeUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r06401001856.impl.QueryCustomerHealthInfoUCCImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r06401001871.impl.ZBBenefitInfoQueryUccImpl .*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r06401001887.impl.QueryPolicyCustomerHealthImpartUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102002839.impl.ImportantDataChangesUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000696.impl.RemoteAuthenticationStatusWriteBackUccImpl.*(..))
				
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r06401003111.impl.QueryAllPolicysOfTheCustomerUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r00102000057.impl.GetFormEdorUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06401003223.impl.QueryHighCustomerInfoUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06802003154.impl.DeathBenefitChangeUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06802003108.impl.CsEndorseCCSubmitUccImpl.*(..))
				|| execution(public * com.nci.tunan.cs.impl.peripheral.ucc.r06802003155.impl.CsBeneficiaryChangedUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r06801900399.impl.PlicyLoanRLUccImpl.*(..))
				|| execution(public * com.nci.tunan.pa.impl.peripheral.ucc.r00101000042.impl.CustomerPolicyMsgQueryUccImpl.*(..))
        	
        	" />

		<!-- 切面 -->
		<aop:aspect ref="PA_outter_customer_aop">
			<!-- 前置通知： 在目标方法调用前执行 -->
			<aop:before method="beginMethod" pointcut-ref="peripheral_pt_pa" />
			<!-- 返回后通知 -->
			<aop:after-returning method="afterReturning"
				pointcut-ref="peripheral_pt_pa" arg-names="joinPoint,returning"
				returning="returning" />
		</aop:aspect>
	</aop:config>
	
</beans>