<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
    xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
    xmlns:tx="http://www.springframework.org/schema/tx" xmlns:batch="http://www.springframework.org/schema/batch"
    xmlns:cache="http://www.springframework.org/schema/cache" xmlns:c="http://www.springframework.org/schema/c"
    xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
        http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
    <!-- serviceInterface属性对应着发布服务的接口-->
    <!-- service对应着服务发布的实现类在spring中注入的bean-->
    <bean name="/PA_queryCsRiskAmountDetailedUCCqueryAllCsRiskAmountAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
        <property name="service" ref="QueryCsRiskAmountDetailedUCCHSImplqueryAllCsRiskAmount" />
        <property name="serviceInterface" value="com.nci.tunan.cs.interfaces.querycsriskamountdetailed.exports.iquerycsriskamountdetaileducc.queryallcsriskamount.hs.IQueryCsRiskAmountDetailedUCCHS"/>
    </bean>
    <!-- 注入定义接口服务实现类的bean -->
    <bean id="QueryCsRiskAmountDetailedUCCHSImplqueryAllCsRiskAmount" class="com.nci.tunan.cs.impl.querycsriskamountdetailed.exports.iquerycsriskamountdetaileducc.queryallcsriskamount.hs.QueryCsRiskAmountDetailedUCCHSImpl" scope="prototype">
        <property name="ucc">
            <ref bean="PA_queryCsRiskAmountDetailedUCC" />
        </property>
    </bean>
    
    <!-- 合并客户ID接口 -->
    <bean name="/PA_isUpdateCustomerUccIsUpdateMergeCustomerIdAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
        <property name="service" ref="IsUpdateCustomerUccHSImplIsUpdateMergeCustomerId" />
        <property name="serviceInterface" value="com.nci.tunan.pa.interfaces.isupdatecustomer.exports.iisupdatecustomerucc.isupdatemergecustomerid.hs.IIsUpdateCustomerUccHS"/>
    </bean>
    <!-- 注入定义接口服务实现类的bean -->
    <bean id="IsUpdateCustomerUccHSImplIsUpdateMergeCustomerId" class="com.nci.tunan.pa.impl.isupdatecustomer.exports.iisupdatecustomerucc.isupdatemergecustomerid.hs.IsUpdateCustomerUccHSImpl" scope="prototype">
        <property name="ucc">
            <ref bean="PA_isUpdateCustomerUcc" />
        </property>
    </bean>
</beans> 

