<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 理赔回退 -->
	<bean class="com.nci.tunan.pa.impl.clmrollback.ucc.impl.CLMRollBackUCCImpl" id="PA_clmRollBackUCC">
		<property name="clmRollBackService" ref="PA_clmRollBackService"/>
	</bean>
	<!-- 理赔回退（迁移数据） -->
	<bean class="com.nci.tunan.pa.impl.clmrollbackforolddata.ucc.impl.ClmRollBackForOldDataUCCImpl" id="PA_clmRollBackForOldDataUCC">
		<property name="clmRollBackForOldDataService" ref="PA_clmRollBackForOldDataService"/>
	</bean>

	<!-- 续期核销重算风险保额 -->
	<bean class="com.nci.tunan.pa.impl.renewriskamount.ucc.impl.RenewRiskAmountUCCImpl" id="PA_renewRiskAmountUCC">
		<property name="renewRiskAmountService" ref="PA_renewRiskAmountService"/>
	</bean>

	<!-- 复核账户 start liucmit 2015/08/20 -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.ucc.impl.ReviewFundAssetsUccImpl" id="PA_reviewFundAssetsUcc">
		<property name="reviewFundAssetsService" ref="PA_reviewFundAssetsService"/>
		<property name="calInvestUnitPriceService" ref="PA_calInvestUnitPriceService"/>
	</bean>
	<!--  复核账户 end -->
	
	<!-- 实时抽档 -->
	<bean class="com.nci.tunan.pa.impl.renewal.ucc.impl.RenewExtraUCCImpl" id="PA_renewalExtraUCC">
		<property name="renewExtraService" ref="PA_extraService"/>
		<property name="autoextraServiece" ref="PA_autoextraServiece"/>
	</bean>

	<!-- 查询投保人信息 -->
	<bean class="com.nci.tunan.pa.impl.querypolicyholderinfo.ucc.impl.QueryPolicyHolderInfoUCCImpl" id="PA_queryPolicyHolderInfoUCC">
		<property name="queryPolicyHolderInfoService" ref="PA_queryPolicyHolderInfoService"/>
	</bean>

	<!-- 新契约转保单 -->
	<bean class="com.nci.tunan.pa.impl.createpolicy.ucc.impl.CreatePolicyUCCImpl" id="PA_createPolicyUCC">
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="createPolicyService" ref="PA_createPolicyService"/>
		<property name="interestMarginService" ref="PA_interestMarginService"/>
		<property name="riskAmountService" ref="PA_riskAmountService"/>
		<property name="universalSettlementService" ref="PA_universalSettlementService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
	</bean>
	
	<!-- 新契约转保单（上海医保） -->
	<bean class="com.nci.tunan.pa.impl.createmedical.ucc.impl.CreateMedicalUCCImpl" id="PA_createMedicalUCC">
		<property name="createMedicalService" ref="PA_createMedicalService" />
		<property name="medicalLogService" ref="PA_medicalLogService" />
	</bean>

	<!-- 手工核销 start niuyu_wb 2014/12/03 -->
	<bean class="com.nci.tunan.pa.impl.renewal.ucc.impl.RenewCollectionUCCImpl" id="PA_renewCollectionUCC">
		<property name="renewCollectionService" ref="PA_renewCollectionService"/>
		<property name="policyLockService" ref="PA_policyLockService"/>
	</bean>
	<!-- 手工核销 end -->
	
	<!-- 实时孤儿单分配结果通知接口 start lvkai_wb 2017/06/29 -->
	<bean class="com.nci.tunan.pa.impl.orphannoticepolicy.ucc.impl.OrphanPolicyNoticeUCCImpl" id="PA_orphanPolicyNoticeUCC">
		<property name="orphanPolicyNoticeService" ref="PA_orphanPolicyNoticeService"/>
	</bean>
	<!-- 手工核销 end -->
	

	<!-- 续期冲正 start niuyu_wb 2014/12/09 -->
	<bean class="com.nci.tunan.pa.impl.renewal.ucc.impl.RenewCollectionOffsetUCCImpl" id="PA_renewCollectionOffsetUCC">
		<property name="renewCollectionOffsetService" ref="PA_renewCollectionOffsetService"/>
	</bean>
	<!-- 续期冲正 end -->

	<!-- liangpl_wb start -->
	<bean class="com.nci.tunan.pa.impl.querypolicylist.ucc.impl.QueryPolicyListUCCImpl" id="PA_queryPolicyListUCC">
		<property name="queryPolicyListService" ref="PA_queryPolicyListService"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.querypolicyinfo.ucc.impl.QueryPolicyInfoUCCImpl" id="PA_queryPolicyInfoUCC">
		<property name="queryPolicyInfoService" ref="PA_queryPolicyInfoService"/>
	</bean>

	<!-- liangpl_wb end -->

	<!-- guyy_wb 风险累计ucc start -->
	<bean class="com.nci.tunan.pa.impl.risk.ucc.impl.RiskAmountUCCImpl" id="PA_riskAmountUCC">
		<property name="riskAmountService" ref="PA_riskAmountService"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractLiabAmoutService" ref="PA_contractLiabAmoutService"/>
	</bean>
	<!-- guyy_wb 风险累计ucc end -->
	
	<!-- guyy_wb 秒杀回执信息ucc  -->
	<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.ucc.impl.SeckillPolicyDateSyncUCCImpl" id="PA_seckillPolicyDateSyncUCC">
		<property name="seckillPolicyDataThread" ref="PA_seckillPolicyDataThread"/>
		<property name="seckillBatchDSService" ref="PA_seckillBatchDSService"/>
<!-- 		<property name="queryProductRiskDetailService" ref="queryRiskAmountService" /> -->
<!-- 		<property name="queryRiskAmountService" ref="queryRiskAmountService" /> -->
<!-- 		<property name="prdIAS" ref="prdIAS" /> -->
	</bean>
	<!-- guyy_wb 秒杀终止保单信息ucc  -->
	<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.ucc.impl.SecKillExpiryPolicySyncUCCImpl" id="PA_secKillExpiryPolicySyncUCC">
		<property name="seckillExpiryPolicyService" ref="PA_seckillExpiryPolicyService"/>
<!-- 		<property name="seckillBatchDSService" ref="seckillBatchDSService"/> -->
<!-- 		<property name="queryProductRiskDetailService" ref="queryRiskAmountService" /> -->
<!-- 		<property name="queryRiskAmountService" ref="queryRiskAmountService" /> -->
<!-- 		<property name="prdIAS" ref="prdIAS" /> -->
	</bean>
	<!-- guyy_wb 秒杀回执信息ucc  -->
	<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.ucc.impl.SecKillAcknowledgeSyncUCCImpl" id="PA_secKillAcknowledgeSyncUCC">
		<property name="secKillAcknowledgeSyncService" ref="PA_secKillAcknowledgeSyncService"/>
		<property name="policyAcknowledgementUpdateService" ref="PA_policyAcknowledgementUpdateService"/>
	</bean>


	<!-- 保单回执处理 -->
	<bean class="com.nci.tunan.pa.impl.policyacknowledgementupdate.ucc.impl.PolicyAcknowledgementUpdateUCCImpl" id="PA_policyAcknowledgementUpdateUCC">
		<property name="policyAcknowledgementUpdateService" ref="PA_policyAcknowledgementUpdateService"/>
	</bean>
	<!-- 查询保单是否有过保全理赔 -->
	<bean class="com.nci.tunan.pa.impl.queryexistcsorcl.ucc.impl.QueryExistCSorCLUCCImpl" id="PA_queryExistCSorCLUCC">
		<property name="queryExistCSorCLService" ref="PA_queryExistCSorCLService"/>
	</bean>

	<!-- 客户告知 start niuyu_wb 2015/01/06 -->
	<bean class="com.nci.tunan.pa.impl.customerSurvey.ucc.impl.CustomerSurveyQueryUCCImpl" id="PA_customerSurveyQueryUCC">
		<property name="customerSurveyQueryService" ref="PA_customerSurveyQueryService"/>
	</bean>
	<!-- 客户告知 end -->
	<!-- 客户保单查询 start niuyu_wb 2015/01/16 -->
	<bean class="com.nci.tunan.pa.impl.customerPolicy.ucc.impl.CustomerPolicyQueryUCCImpl" id="PA_customerPolicyQueryUCC">
		<property name="customerPolicyQueryService" ref="PA_customerPolicyQueryService"/>
	</bean>
	<!-- 客户保单查询 end -->

	<!-- 保单打印查询 -->
	<bean class="com.nci.tunan.pa.impl.queryforprint.ucc.impl.QueryForPrintUCCImpl" id="PA_queryForPrintUCC">
		<property name="contractMasterCompService" ref="PA_contractMasterCompService"/>
		<property name="calculateSAMService" ref="PA_calculateSAMService"></property>
	</bean>

	<!-- 综合查询 start niuyu_wb 2015-01-23 -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.CommonQueryUCCImpl" id="PA_commonQueryUCC">
		<property name="commonQueryService" ref="PA_commonQueryService"/>
		<property name="holderInsuredBeneCustCompService" ref="PA_holderInsuredBeneCustCompService"/>
		<property name="nbIAS" ref="PA_nbIAS"/>
	</bean>
	<!-- 综合查询 end -->

	<!--客户详细信息查询 start -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.CustomerDetailInfoUCCImpl" id="PA_customerDetailInfoUCC">
		<property name="customerDetailInfoService" ref="PA_customerDetailInfoService"/>
	</bean>
	<!--客户详细信息查询 end -->
	<!-- 责任组详细信息查询 start -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.BusiItemDetailInfoUCCImpl" id="PA_busiItemDetailInfoUCC">
		<property name="busiItemDetailInfoService" ref="PA_busiItemDetailInfoService"/>
	</bean>
	<!-- 责任组详细信息查询 end -->
	<!-- 外部系统信息（HOST、URI、IP）查询 start -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.ExternalSystemInfoUCCImpl" id="PA_externalSystemInfoUCC">
		<property name="externalSystemInfoService" ref="PA_externalSystemInfoService"/>
	</bean>
	<!-- 外部系统信息（HOST、URI、IP）查询 end -->
	
	<!-- 	guyy_wb 省市县 -->
	<bean class="com.nci.tunan.cs.impl.commonTool.ucc.impl.DistrictUCCImpl" id="PA_districtUCC">
		<property name="districtService" ref="PA_districtService"/>
	</bean>

	<!-- 出险人查询 -->
	<bean class="com.nci.tunan.pa.impl.queryinjured.ucc.impl.QueryInjuredUCCImpl" id="PA_queryInjuredUCC">
		<property name="service" ref="PA_queryInjuredService"/>
	</bean>
	<!-- 抄单接口 -->
	<bean class="com.nci.tunan.pa.impl.querypolicy.ucc.impl.QueryPolicyUCCImpl" id="PA_queryPolicyUCC">
		<property name="queryPolicyService" ref="PA_queryPolicyService"/>
		<property name="allocationService" ref="PA_allocationService"/>
	</bean>
	<!-- 合并客户ID接口 -->
	<bean class="com.nci.tunan.pa.impl.isupdatecustomer.ucc.impl.IsUpdateCustomerUccImpl" id="PA_isUpdateCustomerUcc">
		<property name="isUpdateCustomerService" ref="PA_isUpdateCustomerService"></property>	
	</bean>
	<!-- 查询续保及加费历史 -->
	<bean class="com.nci.tunan.pa.impl.queryfeehistory.ucc.impl.QueryFeeHistoryUCCImpl" id="PA_queryFeeHistoryUCC">
		<property name="queryFeeHistoryService" ref="PA_queryFeeHistoryService"/>
	</bean>
	<!-- 保单角色信息查询 -->
	<bean class="com.nci.tunan.pa.impl.queryroleinfo.ucc.impl.QueryRoleInfoUCCImpl" id="PA_queryRoleInfoUCC">
		<property name="queryRoleInfoService" ref="PA_queryRoleInfoService"/>
	</bean>

	<!-- add by fengwz  2015.7.30 start -->
	<bean class="com.nci.tunan.pa.impl.renewcollection.ucc.impl.RenewCollectionFeeUCCImpl" id="PA_renewCollectionFeeUCC">
		<property name="renewCollectionFeeService" ref="PA_renewCollectionFeeService"/>
		<property name="capIAS" ref="PA_capIAS"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.renewcollection.ucc.impl.RenewCollectionOffsetUCCImpl" id="PA_renewcollectionoffsetUCC">
		<property name="renewcollectionoffsetService" ref="PA_renewcollectionoffsetService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.renewcollection.ucc.impl.RenewCollectionUndoUCCImpl" id="PA_renewCollectionUndoUCC">
		<property name="renewCollectionUndoService" ref="PA_renewCollectionUndoService"/>
	</bean>
	
	<!-- 账户资产录入 -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.ucc.impl.EditInvestAccountInfoUCC" id="PA_investAccountInfoUCC">
		<property name="investAccountInfoService" ref="PA_investAccountInfoService"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.ucc.impl.EditFundAssetsUCC" id="PA_fundAssetsUCC">
		<property name="fundAssetsService" ref="PA_fundAssetsService"/>
		<property name="investAccountInfoService" ref="PA_investAccountInfoService"/>
	</bean>
	
	<!-- 账户资产录入 -->
	
	<!-- 既往保单查询 add by liangpl -->
	<bean class="com.nci.tunan.pa.impl.queryunderwrite.ucc.impl.QueryUnderwriteUCCImpl" id="PA_queryUnderwriteUCC">
		<property name="queryUnderwriteService" ref="PA_queryUnderwriteService"/>
	</bean>
	<!-- 更新保单号 -->
	<bean class="com.nci.tunan.pa.impl.updatepolicycode.ucc.impl.UpdatePolicyCodeUCCImpl" id="PA_updatePolicyCodeUCC">
		<property name="updatePolicyCodeService" ref="PA_updatePolicyCodeService"/>
	</bean>
	<!-- 更新保单信息 -->
	<bean class="com.nci.tunan.pa.impl.updatepolicyinfo.ucc.impl.UpdatePolicyInfoUCCImpl" id="PA_updatePolicyInfoUCC">
		<property name="updatePolicyInfoService" ref="PA_updatePolicyInfoService"/>
	</bean>
	
	<!--单位价格初审UCC start add by xuyp and gums-->
	<bean class="com.nci.tunan.pa.impl.icheckFundprice.ucc.impl.CheckFundPriceUccImpl" id="PA_iCheckFundPriceUcc">
		<property name="iCheckFundPriceService" ref="PA_iCheckFundPriceService"/>
	</bean>
	<!--单位价格初审UCC end-->	
	<!-- 单位价格复核 -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.ucc.impl.ReviewFundPriceUccImpl" id="PA_reviewFundPriceUcc">
		<property name="reviewFundPriceService" ref="PA_reviewFundPriceService"/>
	</bean>
	
	<!-- 查询投资单位价格信息  -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.ucc.impl.InvestUnitPriceUCCImpl" id="PA_investUnitPriceUCC">
		<property name="investUnitPriceService" ref="PA_investUnitPriceService"/>
	</bean>
	
	<!-- 设置邮件  -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.ucc.impl.FundPriceNoticeMailUCCImpl" id="PA_fundPriceNoticeMailUCC">
		<property name="fundPriceNoticeMailService" ref="PA_fundPriceNoticeMailService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.cssquerypolicy.ucc.impl.QueryPolicyUCCImpl" id="PA_cssQueryPolicyUCC">
		<property name="queryPolicyService" ref="PA_cssQueryPolicyService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.capresult.ucc.impl.CAPResultUCCImpl" id="PA_capResultUCC">
 		<property name="CAPResultService" ref="PA_CAPResultService"/> 
	</bean>
	
	<!-- 满期给付查询接口 -->
	<bean class="com.nci.tunan.pa.impl.maturitybenefit.ucc.impl.MaturityBenefitUCCImpl" id="PA_maturityBenefitUCC">
		<property name="service" ref="PA_maturityBenefitService"/>
	</bean>
	
	<!-- 银保通出单情况查询 -->
	<bean class="com.nci.tunan.pa.impl.bankpolicy.ucc.impl.BankPolicyUCCImpl" id="PA_bankPolicyUCC">
		<property name="service" ref="PA_bankPolicyService"/>
	</bean>

	
	<!-- 保单险种账户信息查询接口UCC -->
	<bean class="com.nci.tunan.pa.impl.queryinsurancepolicy.ucc.impl.QueryInsurancePolicyUCCImpl" id="PA_queryInsurancePolicyUCC">
	   <property name="insurancePolicyService" ref="PA_insurancePolicyService"/>
	</bean>
	
	<!-- 校验欠缴保费接口UCC-->
	<bean class="com.nci.tunan.pa.impl.checksignedprem.ucc.impl.CheckSignedPremUCCImpl" id="PA_checkSignedPremUCC">
	   <property name="checkSignedPremService" ref="PA_checkSignedPremService"/>
	</bean>

	
	<!-- 查询已领生存金年金 -->
	<bean class="com.nci.tunan.pa.impl.drawdetail.ucc.impl.DrawDetailUCCImpl" id="PA_drawDetailUCC">
		<property name="drawDetailService" ref="PA_drawDetailService"/>
	</bean>
	
	<!-- 核保信息回传接口UCC -->
	<bean class="com.nci.tunan.pa.impl.underwriting.ucc.impl.UnderWritingUCCImpl" id="PA_underWritingUCC">
	   <property name="underWritingService" ref="PA_underWritingService"/>
	</bean>
	<!-- 查询未领生存金年金 -->
	<bean class="com.nci.tunan.pa.impl.notreceivedrawdetail.ucc.impl.NotReceiveDrawDetailUCCImpl" id="PA_notReceiveDrawDetailUCC">
		<property name="notReceiveDrawDetailService" ref="PA_notReceiveDrawDetailService"/>
	</bean>
	
	<!-- 理赔结口UCC 上次分红日/出险日是否在首次年金领取日之前 add by yangyl -->
	<bean class="com.nci.tunan.pa.impl.claimquerypolicyinfo.ucc.impl.ClaimQueryPolicyInfoUCCImpl" id="PA_claimQueryPolicyInfo">
	   <property name="claimQueryPolicyInfoService" ref="PA_claimQueryPolicyInfoService"/>
	</bean>
	
	<!-- 理赔结案接口UCC -->
	<bean class="com.nci.tunan.pa.impl.claimsettlement.ucc.impl.ClaimSettlementUCCImpl" id="PA_claimSettlementUCC">
	   <property name="claimSettlementService" ref="PA_claimSettlementService"/>
	</bean>
<!-- 理赔结案相关接口 -->
	<!-- 理赔结案—投连万能账户价值查询 -->
	<bean class="com.nci.tunan.pa.impl.investvalue.ucc.impl.InvestValueUCCImpl" id="PA_investValueUCC">
		<property name="investValueService" ref="PA_investValueService"/>
	</bean>
	<!-- 理赔结案—自垫贷款本息计算 -->
	<bean class="com.nci.tunan.pa.impl.loanselfpay.ucc.impl.LoanSelfpayUCCImpl" id="PA_loanSelfpayUCC">
		<property name="loanSelfpayService" ref="PA_loanSelfpayService"/>
	</bean>
	<!-- 理赔结案—出险日之前欠缴保费查询 -->
	<bean class="com.nci.tunan.pa.impl.debtprem.ucc.impl.DebtPremUCCImpl" id="PA_debtPremUCC">
		<property name="debtPremService" ref="PA_debtPremService"/>
	</bean>
	<!-- 理赔结案—出险日之后多交保费查询 -->
	<bean class="com.nci.tunan.pa.impl.overprem.ucc.impl.OverPremUCCImpl" id="PA_overPremUCC">
		<property name="overPremService" ref="PA_overPremService"/>
	</bean>
	<!-- 理赔结案—利差账户本息计算 -->
	<bean class="com.nci.tunan.pa.impl.accountvalue.ucc.impl.AccountValueUCCImpl" id="PA_accountValueUCC">
		<property name="accountValueService" ref="PA_accountValueService"/>
	</bean>
	<!-- 理赔结案—保单管理费及风险保费欠缴金额计算 -->
	<bean class="com.nci.tunan.pa.impl.aboutfee.ucc.impl.AboutFeeUCCImpl" id="PA_aboutFeeUCC">
		<property name="aboutFeeService" ref="PA_aboutFeeService"/>
	</bean>
	<!-- 理赔结案—应领未领保证领取年金查询 -->
	<bean class="com.nci.tunan.pa.impl.ensureannual.ucc.impl.EnsureAnnualUCC" id="PA_ensureAnnualUCC">
		<property name="ensureAnnualService" ref="PA_ensureAnnualService"/>
	</bean>
	<!-- 理赔结案—出险日之后已领的生存金年金满期金 -->
	<bean class="com.nci.tunan.pa.impl.backprem.ucc.impl.BackPremUCCImpl" id="PA_backPremUCC">
		<property name="backPremService" ref="PA_backPremService"/>
	</bean>
	<!-- 保全生调 规则验证接口 -->
	<bean class="com.nci.tunan.pa.impl.rulevalidation.ucc.impl.RuleValidationUCCImpl" id="PA_ruleValidationUCC">
		<property name="releValidationService" ref="PA_releValidationService"/>
	</bean>
	<!-- 更新豁免止期 -->
	<bean class="com.nci.tunan.pa.impl.contractproduct.ucc.impl.ContractProductUCCImpl" id="PA_contractProductUCC">
		<property name="contractProductService" ref="PA_contractProductService"/>
	</bean>
	<!-- 查询保单简要信息（原柜面使用，报文经过修改） -->
	<bean class="com.nci.tunan.pa.impl.querybriefinfo.ucc.impl.QueryBriefInfoUCCImpl" id="PA_queryBriefInfoUCC">
		<property name="queryBriefInfoService" ref="PA_queryBriefInfoService"/>
	</bean>
	<!-- 客户保单简要查询接口 -->
	<bean class="com.nci.tunan.pa.impl.customerpolicygeneral.ucc.impl.CustomerPolicyGeneralUCCImpl" id="PA_customerPolicyGeneralUCC">
		<property name="customerPolicyGeneralService" ref="PA_customerPolicyGeneralService"/>
	</bean>
	<!-- 保单/险种终止接口 -->
	<bean class="com.nci.tunan.pa.impl.busiprodstop.ucc.impl.BusiProdStopUCCImpl" id="PA_busiProdStopUCC">
		<property name="busiProdStopService" ref="PA_busiProdStopService"/>
	</bean>
	<!-- 查询保单服务信息接口 -->
	<bean class="com.nci.tunan.pa.impl.querypolicyserviceinfo.ucc.impl.QueryPolicyServiceInfoUCCImpl" id="PA_queryPolicyServiceInfoUCC">
		<property name="queryPolicyServiceInfoService" ref="PA_queryPolicyServiceInfoService"/>
	</bean>
	
	<!-- 保单状态同步接口 -->
	<bean class="com.nci.tunan.pa.impl.policystatussynchro.ucc.impl.PolicyStatusSynchroUCCimpl" id="PA_policyStatusSynchroUCC">
		<property name="policyStatusSynchroService" ref="PA_policyStatusSynchroService"/>
	</bean>
	<!-- 豁免状态更新接口 -->  
	<bean class="com.nci.tunan.pa.impl.updatewaiver.ucc.impl.UpdateWaiverUCCImpl" id="PA_updateWaiverUCC">
		<property name="updateWaiverService" ref="PA_updateWaiverService"/>
	</bean>
	<!-- 应收应付接口  zhangjy_wb-->  
	<bean class="com.nci.tunan.pa.impl.querypremarap.ucc.impl.QueryPremArapUCCImpl" id="PA_queryPremArapUCC">
		<property name="queryPremArapService" ref="PA_queryPremArapService"/>
	</bean>
	<!-- 新契约查询保单历史接口 -->
	<bean class="com.nci.tunan.pa.impl.nbquerypolicy.ucc.impl.NBQueryPolicyUCCImpl" id="PA_nbQueryPolicyUCC">
		<property name="nbQueryPolicyDao" ref="PA_nbQueryPolicyDao"/>
		<property name="nbQueryPolicyService" ref="PA_nbQueryPolicyService"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
<!-- 新契约查询投被受历史保单接口 -->
	<bean class="com.nci.tunan.pa.impl.nbqueryhistorypolicy.ucc.impl.NBQueryHistoryPolicyUccImpl" id="PA_nbQueryHistoryPolicyUCC">
	
	</bean>
	<!-- 查询生存给付应领记录 -->
	<bean class="com.nci.tunan.pa.impl.querypaydue.ucc.impl.QueryPayDueUCCImpl" id="PA_queryPayDueUCCImpl">
		<property name="queryPayDueSerivce" ref="PA_queryPayDueSerivce"/>
	</bean>

	<bean class="com.nci.tunan.pa.impl.getinvest.ucc.impl.GetInvestUCCImpl" id="PA_getInvestUCC">
		<property name="getInvestService" ref="PA_getInvestService"/>
	</bean>
	
	
	
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000040.impl.QueryPolicyProductUCCImpl" id="PA_queryPolicyProductUCC">
		<property name="queryPolicyProductService" ref="PA_queryPolicyProductService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000225.impl.AddBankBranchUCCImpl" id="PA_addBankBranchUCC">
		<property name="addBankBranchService" ref="PA_addBankBranchService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000602.impl.AgentOperationUCCImpl" id="PA_agentOperationUCC">
		<property name="agentOperationService" ref="PA_agentOperationService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000548.impl.QueryPolicyPasswordUCCImpl" id="PA_queryPolicyPasswordUCC">
		<property name="queryPolicyPasswordService" ref="PA_queryPolicyPasswordService"/>
	</bean>
	<!-- 保额保费等额查询UCC -->
	<bean class="com.nci.tunan.pa.impl.countfee.ucc.impl.CountFeeUCCImpl" id="PA_countFeeUCC">
	   <property name="countFeeService" ref="PA_countFeeService"/> 
	</bean>
	<!-- 查询客户 -->
	<bean class="com.nci.tunan.pa.impl.IsHesitationPeriod.ucc.impl.IsHesitationPeriodUCCImpl" id="PA_isHesitationPeriodUCC">
	   <property name="isHesitationPeriodService" ref="PA_isHesitationPeriodService"/> 
	</bean>
	<!-- 保单状态更新接口 -->
	<bean class="com.nci.tunan.pa.impl.updatepolicystate.ucc.impl.UpdatePolicyStateUCCImpl" id="PA_updatePolicyStateUCC">
		<property name="updatePolicyStateService" ref="PA_updatePolicyStateService"/>
	</bean>
	<!-- 修改关联保单 -->
	<bean class="com.nci.tunan.pa.impl.updaterelationpolicycode.ucc.impl.updateRelationPolicyCodeUCCimpl" id="PA_updateRelationPolicyCodeUCC">
	   <property name="updateRelationPolicyCodeservice" ref="PA_updateRelationPolicyCodeservice"/> 
	</bean>
    <!-- add by sunjl 上载保单列表 -->
    <bean class="com.nci.tunan.pa.impl.survey.ucc.impl.UploadPolicySurveyUCCImpl" id="PA_uploadPolicySurveyUCC">
        <property name="uploadPolicySurveyService" ref="PA_uploadPolicySurveyService"/>
    </bean>
    <!-- add by daizheng1 维护前置调查计划 -->
    <bean class="com.nci.tunan.pa.impl.survey.ucc.impl.ClaimBfSurveyPlanUCCImpl" id="PA_claimBfSurveyPlanUCC">
        <property name="claimBfSurveyPlanService" ref="PA_claimBfSurveyPlanService"/>
        <property name="claimBfSurveyRamntService" ref="PA_claimBfSurveyRamntService"/>
        <property name="claimBfSurveyOrgService" ref="PA_claimBfSurveyOrgService"/>
        <property name="claimBfSurveyChannelService" ref="PA_claimBfSurveyChannelService"/>
    </bean>

    <!-- 日终汇总对账 -->
	<bean class="com.nci.tunan.pa.impl.dayendreconciliation.ucc.impl.DayEndReconciliationUCCImpl" id="PA_dayEndReconciliationUCC">
	   <property name="dayEndReconciliationService" ref="PA_dayEndReconciliationService"/> 
	   </bean>

    <!--心跳交易-->
    <bean class="com.nci.tunan.pa.impl.hearttransaction.ucc.impl.HeartTransactionUCCImpl" id="PA_heartTransactionUCC">	
	     <property name="heartTransactionService" ref="PA_heartTransactionService"/>	
	</bean>
    <!-- end -->
    
    <!--接入渠道-批量提数处理结果返盘-->
    <bean class="com.nci.tunan.pa.impl.batchnumresultsreturn.ucc.impl.BatchNumResultsReturnUCCImpl" id="PA_iBatchNumResultsReturnUCC">    
         <property name="iBatchNumResultsReturnService" ref="PA_iBatchNumResultsReturnService"/> 
    </bean>
    <!-- end -->
    
    <!-- 投连试算、交易接口 add by liangpl -->
     <bean class="com.nci.tunan.pa.impl.sellinvest.ucc.impl.SellInvestUCCImpl" id="PA_sellInvestUCC">    
         <property name="sellInvestService" ref="PA_sellInvestService"/> 
    </bean>
    <!-- 日常交易查询UCC -->
	<bean class="com.nci.tunan.pa.impl.dailytradingquery.ucc.impl.DailyTradingQueryUCCImpl" id="PA_dailyTradingQueryUCC">
	   <property name="dailyTradingQueryService" ref="PA_dailyTradingQueryService"/> 
	</bean>
    <!-- lvkai 保单账户校验 -->
	<bean class="com.nci.tunan.pa.impl.accountcheck.ucc.impl.AccountCheckUCCImpl" id="PA_accountCheckUCC">
		<property name="accountCheckService" ref="PA_accountCheckService"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
	</bean>

	
	  <!-- lvkai 日终明细对账 -->
	<bean class="com.nci.tunan.pa.impl.daydetailcheck.ucc.impl.DayDetailCheckUCCImpl" id="PA_dayDetailCheckUCC">
		<property name="dayDetailCheckService" ref="PA_dayDetailCheckService"/>
	</bean>
    

    <!-- 计算保单年度生存金 -->
    <bean class="com.nci.tunan.pa.impl.calcsurvival.ucc.impl.CalcSurvivalAmountUCCImpl" id="PA_calcSurvivalAmountUCC">
    	<property name="calculateSAMService" ref="PA_calculateSAMService"/>
    </bean>
    <!-- 出险日后投连、万能险领取查询接口UCC -->
	<bean class="com.nci.tunan.pa.impl.operatequery.ucc.impl.OperateQueryUCCImpl" id="PA_operateQueryUCC">
	   <property name="operateQueryService" ref="PA_operateQueryService"/> 
	</bean>
	
	<!-- 接入渠道保单查询 -->
	<bean class="com.nci.tunan.pa.impl.querypolicydetailinfo.ucc.impl.QueryPolicyDetailInfoUCCImpl" id="PA_queryPolicyDetailInfoUCC">
		<property name="queryPolicyDetailInfoService" ref="PA_queryPolicyDetailInfoService"/>
	</bean>
	
	<!-- ESB服务 判断保单号是否存在 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.judgepolicyexist.impl.JudgePolicyExistUccImpl" id="PA_judgePolicyExistUCC">
	     <property name="judgePolicyExistService" ref="PA_judgePolicyExistService"/>
	</bean>
	
	<!-- 投保人和代理人是否是同一人 -->
	<bean class="com.nci.tunan.pa.impl.customerisagent.ucc.impl.CustomerIsAgentUCCImpl" id="PA_customerIsAgentUCC">	
	     <property name="customerIsAgentService" ref="PA_customerIsAgentService"/>	
	</bean>
	<!-- 投保人或被保人是否存在期满保单 -->
	<bean class="com.nci.tunan.pa.impl.policyexpiration.ucc.impl.PolicyExpirationUCCImpl" id="PA_policyExpirationUCC">	
	     <property name="policyExpirationService" ref="PA_policyExpirationService"/>	
	</bean>
	<bean class="com.nci.tunan.pa.impl.autoCollection.ucc.impl.AutoCollectionUCCImpl" id="PA_autoCollectionUCC">	
	     <property name="policyLockService" ref="PA_policyLockService"/>
	     <property name="premArapDao" ref="PA_premArapDao"/>
	     <property name="renewCollectionService" ref="PA_renewCollectionService"/>
	     <property name="contractExtendDao" ref="PA_contractExtendDao"/>
	     <property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.policyrisk.ucc.impl.PolicyRiskUCCImpl" id="PA_policyRiskUCC">	
	     <property name="claPolicyRiskService" ref="PA_claPolicyRiskService"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.querymobile.ucc.impl.QueryMobileUCCImpl" id="PA_queryMobileUCC">	
	     <property name="queryMobileService" ref="PA_queryMobileService"/>
	</bean>
	
	<!-- leihong -->
	<bean class="com.nci.tunan.pa.impl.guaranteePeriod.impl.GuaranteePeriodParamUCCImpl" id="PA_guaranteePeriodParamUCC">
    	<property name="calculateSAMService" ref="PA_calculateSAMService"/>
    </bean>
	<!-- 变更履历查询 -->
	<bean class="com.nci.tunan.pa.impl.transdata.ucc.impl.TransDataUCCImpl" id="PA_transDataUCC">	
	     <property name="transDataService" ref="PA_transDataService"/>
	</bean>
	<!-- 查询责任组最新加费评点 -->
	<bean class="com.nci.tunan.pa.impl.extrapremem.ucc.impl.ExtraPremEmUccImpl" id="PA_extraPremEmUcc">	
	     <property name="extraPremEmService" ref="PA_extraPremEmService"/>
	</bean>
	<!-- 根据输入的移动电话及固定电话，查询出使用此了该号码的投保人集合 -->
	<bean class="com.nci.tunan.pa.impl.querycusfivebasicbytel.ucc.impl.QueryCusFiveBasicByTelUCCImpl" id="PA_queryCusFiveBasicByTelUCC">	
	     <property name="queryCusFiveBasicByTelService" ref="PA_queryCusFiveBasicByTelService"/>
	</bean>
	<!-- 可选责任信息查询列表 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001594.impl.OptionLiabInfoQueryUccImpl" id="PA_optionLiabInfoQueryUcc">	
	     <property name="optionLiabInfoQueryService" ref="PA_optionLiabInfoQueryService"/>
	</bean>				  
	<!-- 当日撤单接口 -->
	<bean class="com.nci.tunan.pa.impl.dayTradeRevoke.ucc.impl.DayTradeRevokeUCCImpl" id="PA_dayTradeRevokeUCC">
		 <property name="dayTradeRevokeService" ref="PA_dayTradeRevokeService"/>
	</bean>
    <bean class="com.nci.tunan.pa.impl.boxpa.ucc.impl.PAinvestUnitPriceUCCImpl" id="PA_InvestUnitPriceUCC">
         <property name="investUnitPriceService" ref="PA_investUnitPriceService"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.boxpa.ucc.impl.InvestUnitsDaysumUccImpl" id="PA_InvestUnitsDaysumUcc">
         <property name="investUnitsDaysumService" ref="PA_investUnitsDaysumService"/>
    </bean>
    
    <bean class="com.nci.tunan.pa.impl.boxpa.ucc.impl.InvestAccountDataQueryUCCImpl" id="PA_InvestAccountDataQueryUCC">
         <property name="investUnitsDaysumService" ref="PA_investUnitsDaysumService"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.policyAccount.ucc.impl.PolicyAccountUCCImpl" id="PA_policyAccountUCC">
         <property name="policyAccountService" ref="PA_policyAccountService"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.hospital.ucc.impl.HospitalBenefitLimitUCC" id="PA_hospitalBenefitLimitUCC">
		<property name="hospitalBenefitLimitService" ref="PA_hospitalBenefitLimitService"/>
	</bean>
    <bean class="com.nci.tunan.pa.imports.impl.BatchInvoke" id="PA_batchInvoke"/>
    <bean class="com.nci.tunan.pa.impl.orphanpolicy.ucc.impl.OrphanPolicyUCCImpl" id="PA_orphanPolicyUCC">
     	<property name="batchInvoke" ref="PA_batchInvoke"/>
     	<property name="orphanPolicyService" ref="PA_orphanPolicyService"/>
     	<property name="orphanParameterDao" ref="PA_orphanParameterDao"/>
     	<property name="orphanParameterService" ref="PA_orphanParameterService"/>
     	<property name="orphanParameterDetailDao" ref="PA_orphanParameterDetailDao"/>
    </bean>
    
    <!-- 查询保单下的责任组现金价值接口 -->
    <bean class="com.nci.tunan.pa.impl.querycashvaluelist.ucc.impl.QueryCashValueListUCCImpl" id="PA_queryCashValueListUCC">
         <property name="queryCashValueListService" ref="PA_queryCashValueListService"/>
    </bean>
    

     <!--  修改保单纠错数据ucc -->
    <bean class="com.nci.tunan.pa.impl.nbdatadeal.ucc.impl.ErrorCorrectDealUCCImpl" id="PA_errorCorrectDealUCC">
    	<property name="errorCorrectDealService" ref="PA_errorCorrectDealService"/>
    </bean>
    
    <!-- 物流丢失保单标记ucc -->
    <bean class="com.nci.tunan.pa.impl.nbdatadeal.ucc.impl.PostLostDealUCCImpl" id="PA_postLostDealUCC">
    	<property name="postLostDealService" ref="PA_postLostDealService"/>
    </bean>

    <!-- 查询是否做过续期 -->
    <bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.QueryWhetherRenewUCCImpl" id="PA_queryWhetherRenewUCC">
         <property name="queryWhetherRenewService" ref="PA_queryWhetherRenewService"/>
    </bean>

    <!-- 可选责任分红计算接口 -->
    <bean class="com.nci.tunan.pa.impl.calc.ucc.impl.CalcOptBonusSAUCCImpl" id="PA_calcOptBonusSAUCC">
         <property name="calcOptBonusSAService" ref="PA_calcOptBonusSAService"/>
    </bean>
    
    
     <!--  操作员是否操作过保单号的复核接口实-->
    <bean class="com.nci.tunan.cs.impl.querypolicyreview.ucc.impl.QueryPolicyReviewUCCImpl" id="PA_queryPolicyReviewUCC">
         <property name="queryPolicyReviewService" ref="PA_queryPolicyReviewService"/>
    </bean>

	<!-- 申请登记台账接口 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.RegistLedgerUCCImpl" id="PA_registLedgerUCC">
         <property name="registLedgerService" ref="PA_registLedgerService"/>
    </bean>
    
    <!-- 查询客户保单 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.CustomerPolicyUCCImpl" id="PA_customerPolicyUCC">
         <property name="cuPoService" ref="PA_customerPolicyService"/>
    </bean>
    
    <!-- 农行网银渠道向保险公司查询保单详情 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyDetailUCCImpl" id="PA_policyDetailUCC">
         <property name="policyDetailService" ref="PA_policyDetailService"/>
    </bean>
    
        <!-- 续期冲正 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.RenewalAmendPAUCCImpl" id="PA_renewalAmendPAUCC">
         <property name="renewalAmendPAService" ref="PA_renewalAmendPAService"/>
    </bean>
    
    <!-- 校验投保人联系方式接口 -->
    <bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.CustomerInfoCheckUccImpl" id="PA_customerInfoCheckUcc">
    	<property name="customerInfoCheckService" ref="PA_customerInfoCheckService"/>
    </bean>

      <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.CiticBankDataUCCImpl" id="PA_citicBankDataUCC">
       <property name="citicBankDataService" ref="PA_citicBankDataService"/>
    </bean>
    
    <!-- 农行保全状态同步 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyStatusSynForAbcUCCImpl" id="PA_policyStatusSynForAbcUCC">
         <property name="policyStatusSynForAbcService" ref="PA_policyStatusSynForAbcService"/>
    </bean>
    <!-- 建行保单详情查询 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyDetailForCCBUCCImpl" id="PA_policyDetailForCCBUCC">
         <property name="policyDetailForCCBService" ref="PA_policyDetailForCCBService"/>
    </bean>
    <!--工商银行数据回传 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.ICBankDataUCCImpl" id="PA_iCBankDataUCC">
       <property name="iCBankDataService" ref="PA_iCBankDataService"/>
    </bean>
    
    <!-- 接入渠道犹豫期退保数据查询接口 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.QueryHesitationSurrenderUCCImpl" id="PA_queryHesitationSurrenderUCC">
    	<property name="queryHesitationSurrenderService" ref="PA_queryHesitationSurrenderService"/>
    </bean>
    
    <!-- 招商银行数据回传 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyStatusReturnUccImpl" id="PA_policyStatusReturnUcc">
    	<property name="policyStatusReturnService" ref="PA_policyStatusReturnService"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyStatusReturnServiceImpl" id="PA_policyStatusReturnService">
    	<property name="policyStatusChangeDao" ref="PA_policyStatusChangeDao" />
		<property name="policyChangeDao" ref="PA_policyChangeDao" />
		<property name="contractMasterDao" ref="PA_contractMasterDao" />
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao" />
		<property name="contractProductDao" ref="PA_contractProductDao" />
		<property name="payerAccountDao" ref="PA_payerAccountDao" />
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyStatusChangeDaoImpl" id="PA_policyStatusChangeDao" parent="baseDao">
    </bean>
    
     <!-- 北京银行保单资产变动信息同步交易 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyCapitalChgSynUCCImpl" id="PA_policyCapitalChgSynUCC">
         <property name="policyCapitalChgSynService" ref="PA_policyCapitalChgSynService"/>
    </bean>
        <!-- 注入查询客户关系的接口 -->
    <bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.CustomerRelanUccImpl" id="PA_customerRelanUcc">
    	<property name="customerInfoRelaService" ref="PA_customerInfoRelaService"></property>
    </bean>
    <!-- 注入service -->
    <bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.CustomerInfoRelaServiceImpl" id="PA_customerInfoRelaService">
    	<property name="customerDao" ref="PA_customerDao"/>
    </bean>
    <!-- 配置dao -->
    <bean class="com.nci.tunan.pa.dao.impl.CustomerDaoImpl" id="PA_customerDao" parent="baseDao"></bean>
    
    <!-- 非实时保单查询 start -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyQueryDelayedUccImpl" id="PA_policyQueryDelayedUcc">
    	<property name="policyQueryDelayedService" ref="PA_policyQueryDelayedService"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyQueryDelayedServiceImpl" id="PA_policyQueryDelayedService">
    	<property name="policyQueryDelayedDao" ref="PA_policyQueryDelayedDao" />
		<property name="policyHolderDao" ref="PA_policyHolderDao" />
		<property name="addressDao" ref="PA_addressDao" />
		<property name="insuredListDao" ref="PA_insuredListDao" />
		<property name="customerDao" ref="PA_customerDao" />
		<property name="contractBeneDao" ref="PA_contractBeneDao" />
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="uWService" ref="PA_uwIAS"/>
        <property name="queryCashValueListService" ref="PA_queryCashValueListService"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao" />
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao" />
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao" />
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		
    </bean>
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyQueryDelayedDaoImpl" id="PA_policyQueryDelayedDao" parent="baseDao">
    </bean>
    <!-- 非实时保单查询 end -->
    
    <!-- 续期缴费start -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PaRenewalPaymentUCCImpl" id="PA_paRenewalPaymentUCC">
         <property name="paRenewalPaymentService" ref="PA_paRenewalPaymentService"/>
    </bean>
    <!-- 续期缴费end -->
    
    	 <!--建行查询保单历史变动信息  -->
	 <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.QueryPolicyHisForCCBUCCImpl" id="PA_QueryPolicyHisForCCBUCC">
	 	<property name="queryPolicyHisForCCBService" ref="PA_queryPolicyHisForCCBService"/>
	 </bean>
	 
	<!-- 保费同步接口 -->
    <bean class="com.nci.tunan.pa.impl.premsynchronize.ucc.impl.PremSynchronizeUccImpl" id="PA_premSynchronizeUcc">
         <property name="premSynchronizeService" ref="PA_premSynchronizeService"/>
    </bean>
    
    <!-- 延长宽限期天数查询接口 -->
    <bean class="com.nci.tunan.pa.impl.queryExtendGracePeriodDays.ucc.impl.QueryExtendGracePeriodDaysUCCImpl" id="PA_queryExtendGracePeriodDaysUCC">
         <property name="queryExtendDaysService" ref="PA_queryExtendGracePeriodDaysService"/>
    </bean>
    
    <!-- 查询保单金额接口 -->
    <bean class="com.nci.tunan.pa.impl.queryPolicyAmount.ucc.impl.QueryPolicyAmountUccImpl" id="PA_queryPolicyAmountUCC">
         <property name="queryPolicyAmountService" ref="PA_queryPolicyAmountService"/>
    </bean>
    
    <!-- 更新保单标识 -->
    <bean class="com.nci.tunan.pa.impl.updatepolicyflag.ucc.impl.UpdatePolicyFlagUccImpl" id="PA_updatePolicyFlagUCC">
         <property name="updatePolicyFlagService" ref="PA_updatePolicyFlagService"/>
    </bean>
    <!-- 开门红撤单接口-->
    <bean class="com.nci.tunan.pa.impl.aGoodStart.ucc.impl.AGoodStartUCCImpl" id="PA_aGoodStartUCC">
         <property name="aGoodStartService" ref="PA_aGoodStartService"/>
    </bean>

    <!-- CRS客户校验 -->
    <bean class="com.nci.tunan.pa.impl.nbquerypolicy.ucc.impl.NBQueryCrsCustomerUCCImpl" id="PA_nbQueryCrsCustomerUCC">   
    <property name="queryCrsCustomerService" ref="PA_nbQueryCrsCustomerService"></property>
    </bean>
    
    <!-- 非银保通查询 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyQueryNonYbtUccImpl" id="PA_policyQueryNonYbtUcc">
    	<property name="policyQueryNonYbtService" ref="PA_policyQueryNonYbtService"/>
    </bean>
    <!-- 查询投保人银行账号的其他使用投保人 add by hourui  -->
    <bean class="com.nci.tunan.pa.impl.querybankaccounts.ucc.impl.QueryBankAccountsUCCImpl" id="PA_queryBankAccountsUCC">
    	<property name="queryBankAccountsService" ref="PA_queryBankAccountsService"></property>
    </bean>
    
     <!--投被保人关系查询接口-->
    <bean class="com.nci.tunan.cs.impl.peripheral.ucc.r06401003110.impl.HolderRelationInsuredUccImpl" id="PA_holderRelationInsuredUcc">
    	<property name="holderRelationInsuredService" ref="PA_holderRelationInsuredService"></property>
    </bean>
    
    <!-- 根据投保单号查询风险保额ucc-->
    <bean id="PA_PolicyRiskInfo" class="com.nci.tunan.pa.impl.policyinfo.ucc.impl.PolicyRiskInfoUccImpl">
        <property name="policyRiskInfoService" ref="PA_PolicyRiskInfoService"></property>
    </bean>
    
    <!-- 根据保单号更新主表保单发送形式ucc-->
    <bean id="PA_policyMediaTypeUcc" class="com.nci.tunan.pa.impl.policymediatype.ucc.impl.PolicyMediaTypeUccImpl">
        <property name="policyMediaTypeService" ref="PA_policyMediaTypeService"></property>
    </bean>
    <!-- 保单编码查询（中保信） -->
	<bean id="PA_policyCodeQueryFromPAUCC" class="com.nci.tunan.pa.impl.policyCodeQueryFromPA.ucc.impl.PolicyCodeQueryFromPAUCCImpl">
        <property name="policyCodeQueryFromPAService" ref="PA_policyCodeQueryFromPAService"></property>
    </bean>
    
    <!-- 保单编码查询（中保信）出险人信息 -->
	<bean id="PA_phoneInfoQueryFromPAUCC" class="com.nci.tunan.pa.impl.phoneInfoQueryFromPA.ucc.impl.PhoneInfoQueryFromPAUCCImpl">
		<property name="phoneInfoQueryFromPAService" ref="PA_phoneInfoQueryFromPAService"></property>
	</bean>
    
    <!-- box account ucc -->
    <bean id="PA_createBoxAccountUcc" class="com.nci.tunan.pa.impl.box.ucc.impl.CreateBoxAccountUccImpl">
        <property name="createBoxAccountService" ref="PA_createBoxAccountService"></property>
    </bean>
    <!-- BOX监管限额录入ucc -->
    <bean id="PA_createBoxSuperviseLimitUcc" class="com.nci.tunan.pa.impl.box.ucc.impl.CreateBoxSuperviseLimitUccImpl">
       <property name="createBoxSuperviseLimitService" ref="PA_createBoxSuperviseLimitService"></property>
    </bean>
    <!-- 资金划拨 ucc -->
    <bean id="PA_boxPositionUcc" class="com.nci.tunan.pa.impl.box.ucc.impl.BoxPositionUccImpl">
        <property name="boxPositionService" ref="PA_BoxPositionService"></property>
    </bean> 
    <!-- box内部限额录入/修改 -->
    <bean id="PA_createBoxInternalLimitUcc" class="com.nci.tunan.pa.impl.box.ucc.impl.CreateBoxInternalLimitUccImpl">
       <property name="createBoxInternalLimitService" ref="PA_createBoxInternalLimitService"></property>
    </bean> 
    <!-- BOX限额查询、BOX头寸查询、BOX资金划拨查询、BOX账户单位查询 ucc -->
    <bean id="PA_boxAccountQueryUcc" class="com.nci.tunan.pa.impl.box.ucc.impl.BoxAccountQueryUccImpl">
        <property name="boxAccountQueryService" ref="PA_boxAccountQueryService"></property>
    </bean>
     <!-- 计价报告打印 ucc -->
    <bean id="PA_boxValuationReportUcc" class="com.nci.tunan.pa.impl.box.ucc.impl.BoxValuationReportUccImpl">
    	<property name="boxValuationReportService" ref="PA_boxValuationReportService"></property>
    </bean>
    
    
    <!-- 保单风险累计查询接口 -->
    <bean id="PA_RiskAmountInterUcc" class="com.nci.tunan.pa.impl.riskamount.ucc.impl.RiskAmountInterUCCImpl">
         <property name="riskAmountInterService" ref="PA_riskAmountInterService"></property>
    </bean>
    
    <!-- 根据投被保人五要素查询保单信息ucc -->
    <bean id="PA_queryPolicyByFiveElementsUcc" class="com.nci.tunan.pa.impl.queryPolicyByFiveElementsInfo.ucc.impl.QueryPolicyByFiveElementsUccImpl">
    
          <property name="queryPolicyByFiveElementsService" ref="PA_queryPolicyByFiveElementsService"></property>
    </bean>
    <!-- 续期查询（接入渠道） -->
    <bean id="PA_queryRenewalPolicyUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.QueryRenewalPolicyUCCImpl">   
          <property name="queryRenewalPolicyService" ref="PA_queryRenewalPolicyService"></property>
    </bean>
    <!-- 承保保单统计接口 -->
    <bean id="PA_UnderwritingPolicyStatisticsUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.UnderwritingPolicyStatisticsUCCImpl">   
          <property name="underwritingPolicyStatisticsService" ref="PA_underwritingPolicyStatisticsService"></property>
    </bean>
    
        <!-- 保单状态数据ucc -->
    <bean id="PA_policyStatusUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyStatusUccImpl">
            <property name="policyStatusService" ref="PA_policyStatusChannelService"></property>
    </bean>
   
    <!-- 契约-保单状态数据ucc -->
    <bean id="PA_queryPolicyStatusUCC" class="com.nci.tunan.pa.impl.querypolicystatus.ucc.impl.QueryPolicyStatusUCCImpl">
            <property name="queryPolicyStatusService" ref="PA_queryPolicyStatusService"></property>
    </bean>
    
    <!-- 理赔-根据附加险单独投保保单查询主保单信息接口ucc -->
    <bean id="PA_claimQueryMasterPolicyUCC" class="com.nci.tunan.pa.impl.claimquerymasterpolicy.ucc.impl.ClaimQueryMasterPolicyUCCImpl">
            <property name="claimQueryMasterPolicyService" ref="PA_claimQueryMasterPolicyService"></property>
    </bean>

	<!--住院津贴限额校验UCC接口ucc -->
    <bean id="PA_accumulatedhospitalizationUCC" class="com.nci.tunan.pa.impl.accumulatedhospitalization.ucc.impl.AccumulatedhospitalizationUCCImpl">
            <property name="accumulatedhospitalizationService" ref="PA_accumulatedhospitalizationService"></property>
    </bean>
    
    <!-- 接入渠道交通银行-非实时保单查询接口ucc -->
    <bean class="com.nci.tunan.pa.impl.accesschannelquerypolicy.ucc.impl.PolicyQueryDelayedJTYHUccImpl" id="PA_policyQueryDelayedJTYHUcc">
    		<property name="policyQueryDelayedJTYHService" ref="PA_policyQueryDelayedJTYHService"/>
    </bean>
    
    <!-- 保单承保信息更新接口 -->
	<bean class="com.nci.tunan.pa.impl.updatePolicyForPA.ucc.impl.UpdatePolicyInfoUCCImpl" id="PA_updatePolicyPAInfoUCC">
		<property name="updatePolicyInfoService" ref="PA_updatePolicyPAInfoService"/>
	</bean>
	<!-- 销售限额配置 -->
	<bean class="com.nci.tunan.pa.impl.salesAmountAccCfg.ucc.impl.SalesAmountAccCfgUCCImpl" id="PA_salesAmountAccCfgUCC">
		<property name="salesAmountAccCfgService" ref="PA_salesAmountAccCfgService"/>
	</bean>
	<!-- 销售限额配置轨迹 -->
	<bean class="com.nci.tunan.pa.impl.salesAmountAccCfgLog.ucc.impl.SalesAmountAccCfgLogUCCImpl" id="PA_salesAmountAccCfgLogUCC">
		<property name="salesAmountAccCfgLogService" ref="PA_salesAmountAccCfgLogService"/>
	</bean>
	 <!--险种销售额度同步接口-->
    <bean id="PA_saveSalesAmountUcc" class="com.nci.tunan.pa.impl.saveSalesAmount.ucc.impl.SaveSalesAmountUCCUCCImpl">
    	<property name="saveSalesAmountService" ref="PA_saveSalesAmountService"/>
    </bean>
    
    <!-- 万能险合同账户价值交纳续期保险费 -->
	<bean class="com.nci.tunan.pa.impl.renewal.ucc.impl.AccountValueTORenewalPremUCCImpl" id="PA_accountValueTORenewalPremUCC">
	    <property name="accountValueTORenewalPremService" ref="PA_accountValueTORenewalPremService"/>
	    <property name="accValToPremTraceDao" ref="PA_accValToPremTraceDao"/>
	</bean>
	<!-- 投保人手机号及首期银行账号重复查询UCC接口 -->
	<bean class="com.nci.tunan.pa.impl.queryAbnormalCustomer.ucc.impl.QueryAbnormalCustomerUCCImpl" id="PA_queryAbnormalCustomerUCC">
	    <property name="queryAbnormalCustomerService" ref="PA_queryAbnormalCustomerService"/>
	</bean>
	
	<!-- 重疾险险种风险级别设置ucc -->
	<bean class="com.nci.tunan.pa.impl.riskScoreConfig.ucc.impl.RiskScoreConfigUccImpl" id="PA_riskScoreConfigUccImpl">
	    <property name="riskScoreConfigService" ref="PA_riskScoreConfigServiceImpl"/>
	</bean>
	
	<!--团险风险保额累计接口ucc -->
    <bean id="PA_groupRiskAccUCC" class="com.nci.tunan.pa.impl.groupRisk.ucc.impl.GroupRiskAccUCCImpl">
            <property name="groupRiskAccService" ref="PA_groupRiskAccService"></property>
    </bean>
    <!--暂收退费录入ucc-->
    <bean id="PA_tempPremReturnUCC" class="com.nci.tunan.pa.impl.renewal.ucc.impl.TempPremReturnUCCImpl">
    	<property name="tempPremReturnService" ref="PA_tempPremReturnService"></property>
    	<property name="csDocumentService" ref="PA_csDocumentService"></property>
    	<property name="documentDao" ref="PA_paDocumentDao"></property>
    	<property name="clobDao" ref="PA_paClobDao"></property>
    	<property name="capIAS" ref="PA_capIAS"></property>
    </bean>
      <!--保单-保单状态数据ucc -->
    <bean id="PA_queryPolicyStatusByPolicyCodeUCC" class="com.nci.tunan.pa.impl.querypolicystatusbypolicycode.ucc.impl.QueryPolicyStatusByPolicyCodeUCCImpl">
            <property name="queryPolicyStatusPolicyCodeService" ref="PA_queryPolicyStatusByPolicyCodeService"></property>
    </bean>
      <!--保单-投保人及账户变更查询ucc -->
    <bean id="PA_policyHolderInfoQueryUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyHolderInfoQueryUccImpl">
            <property name="policyHolderInfoQueryService" ref="PA_policyHolderInfoQueryService"></property>
    </bean>
     <!--保单-保单收益查询ucc -->
    <bean id="PA_policyIncomeQueryUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyIncomeQueryUccImpl">
            <property name="policyIncomeQueryService" ref="PA_policyIncomeQueryService"></property>
    </bean>
    <!-- 保单查询ucc -->
    <bean id="PA_policyInfoPsbcUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyInfoPsbcUccImpl">
            <property name="policyInfoPsbcService" ref="PA_policyInfoPsbcService"></property>
    </bean>
    
    <!-- 理赔养老金投资信息查询接口 -->
	<bean class="com.nci.tunan.pa.impl.queryBOCICPolicyInfoForClm.ucc.impl.QueryBOCICPolicyInfoForClmUccImpl" id="PA_queryBOCICPolicyInfoForClmUcc">
		<property name="queryBOCICPolicyInfoForClmService" ref="PA_queryBOCICPolicyInfoForClmService"/>
	</bean>
    
    <!-- 收付费查询保单信息接口 Ucc -->
    <bean id="PA_queryPolicyInfoForCapUcc" class="com.nci.tunan.pa.impl.queryPolicyInfoForCap.ucc.impl.QueryPolicyInfoForCapUccImpl">
    	<property name="queryPolicyInfoForCapService" ref="PA_queryPolicyInfoForCapService"></property>
    </bean>

	<!-- 银保通插入个人养老金信息报送表UCC接口 -->
	<bean class="com.nci.tunan.pa.impl.submitMessageBocicYBT.ucc.impl.SubmitMessageBocicYBTUCCImpl" id="PA_submitMessageBocicYBTUCC">
	    <property name="submitMessageBocicYBTService" ref="PA_submitMessageBocicYBTService"/>
	</bean>
	
	<!-- 需求分析任务 #137751 新核心功能优化（7）-保单管理start -->
	<!-- 查询投被关系集合接口UCC接口 -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.QueryRelation2PhInfoUCCImpl" id="PA_queryRelation2PhInfoUCC">
	 <property name="queryRelation2PhInfoService" ref="PA_queryRelation2PhInfoService"/>
	</bean>
	
	<!-- 查询多个投保人信息接口UCC接口 -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.ucc.impl.QueryMultiPolicyHoldersInfoUCCImpl" id="PA_queryMultiPolicyHoldersInfoUCC">
	 <property name="queryMultiPolicyHoldersInfoService" ref="PA_queryMultiPolicyHoldersInfoService"/>
	</bean>
    <!-- 需求分析任务 #137751 新核心功能优化（7）-保单管理end -->
    
    <!-- 业务公共调用插入个人养老金信息报送表UCC接口 -->
    <bean class="com.nci.tunan.pa.impl.annuityexpensehx.ucc.impl.AnnuityExpenseHXUCCImpl" id="PA_annuityExpenseHXUCC">
	 <property name="annuityExpenseHXService" ref="PA_annuityExpenseHXService"/>
	</bean>
	
	<!-- 初始化续期年金生存调查结果确认ucc -->
    <bean id="PA_payConfirmListUCC" class="com.nci.tunan.pa.impl.payConfirmList.ucc.impl.PayConfirmListUCCImpl">
            <property name="payConfirmListService" ref="PA_payConfirmListService"></property>
    </bean>
    	
    <!-- 需求分析任务 #147285: 睡眠保单项目需求-核心系统睡眠保单标识更新需求 add by liwei 睡眠保单通知情况更新UCC接口 -->
    <bean class="com.nci.tunan.pa.impl.sleepypolicy.ucc.impl.SleepyPolicyStatusUpdateUCCImpl" id="PA_sleepyPolicyStatusUpdateUCC">
	 <property name="sleepyPolicyStatusService" ref="PA_sleepyPolicyStatusService"/>
	</bean>
	
	    
    <bean class="com.nci.tunan.pa.impl.querynextpricedateaftersigndate.ucc.impl.QueryNextPricingDateUCCImpl" id="PA_queryNextPricingDateUCC">
		<property name="queryNextPricingDateService" ref="PA_queryNextPricingDateService"/>
	</bean>
	
    <!-- 商业养老保险账户信息同步查询Ucc  -->
    <bean id="PA_policyInfoPaqdUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.PolicyInfoPaqdUccImpl">
    </bean>
    
    <!-- 手续费对账查询接口Ucc -->
    <bean id="PA_serviceChargeReconciliationUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.ServiceChargeReconciliationUccImpl">
    </bean>
    
    <!-- 身故受益人详情查询接口Ucc -->
    <bean id="PA_dieBenefitInfoUcc" class="com.nci.tunan.pa.impl.interfaceforchannel.ucc.impl.DieBenefitInfoUccImpl">
    </bean>
    
    <!-- 提供客户信息给契约做银保通业务功能Ucc -->
    <bean id="PA_provideCustomerInfosForBancasBusinessForNBUcc" class="com.nci.tunan.pa.impl.hessianinterface.ucc.impl.ProvideCustomerInfosForBancasBusinessForNBUccImpl">
    </bean>
    
   <!--需求分析任务 #172886: 关于新增银代渠道分公司佣金率管理等相关功能的需求-二期 保单 -->
   <bean class="com.nci.tunan.pa.impl.bankorgancommratecfg.ucc.impl.BankOrganCommRateCfgUCCImpl" id="PA_bankOrganCommRateCfgUCC">
	 <property name="bankOrganCommRateCfgService" ref="PA_bankOrganCommRateCfgService"/>
	</bean>
	
     <bean class="com.nci.tunan.pa.impl.laratecommisionrate.ucc.impl.LaratecommisionRateUCCImpl" id="PA_laratecommisionRateUCC">
	 <property name="laratecommisionRateService" ref="PA_laratecommisionRateService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.discountpremrate.ucc.impl.DiscountPremRateCfgUCCImpl" id="PA_discountPremRateCfgUCC">
	 <property name="discountPremRateCfgService" ref="PA_discountPremRateCfgService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.querypapersupplement.ucc.impl.QueryPaperSupplementUCCImpl" id="PA_queryPaperSupplementUCC">
	   <property name="queryPaperSupplementService" ref="PA_queryPaperSupplementService"/> 
	</bean>
 </beans>