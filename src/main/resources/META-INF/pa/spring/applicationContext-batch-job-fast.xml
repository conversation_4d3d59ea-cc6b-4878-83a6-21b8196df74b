<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c"
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jaxws="http://cxf.apache.org/jaxws" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    
    <!-- 公共Dao -->
	<bean class="com.nci.tunan.pa.common.dao.impl.CommonBatchDaoImpl" id="PA_commonBatchDao" parent="baseDao">
	</bean>
	
	<!-- 新短期险续保抽档start-->
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.AutomaticRenewalExtractionFastJob" id="PA_newAutomaticrenewalextraJob" scope="prototype">
		<property name="autoRenExtrFastService" ref="PA_newAutoextraService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.service.impl.AutomaticRenewalExtractionFastServiceImpl" id="PA_newAutoextraService">
		<property name="precontProductDao" ref="PA_precontProductDao"/>
		<property name="autoRenExtrFastDao" ref="PA_newAutoextraDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="renewalBillingDao" ref="PA_renewalBillingDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="customerSurveyDao" ref="PA_customerSurveyDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>		
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="uwService" ref="PA_uwIAS"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="bpmService" ref="PA_bpmIAS"/>
		<property name="clmService" ref="PA_clmService"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="renewChangeDao" ref="PA_renewChangeDao"/>
        <property name="saChangeDao" ref="PA_saChangeDao"/>
        <property name="iPolicyLogService" ref="PA_policyLogService"/>
        <property name="commonBatchDao" ref="PA_commonBatchDao" />
	</bean>
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.dao.impl.AutomaticRenewalExtractionFastDaoImpl" id="PA_newAutoextraDao" parent="baseDao">
	</bean>
    <!-- 新短期险续保抽档end -->
    
    <!-- 新续保处理start -->
	<bean class="com.nci.tunan.pa.batch.renewalprocess.RenewalProcessFastJob" id="PA_newRenewalprocessJob" scope="prototype">
		<property name="renewalprocessFastService" ref="PA_newRenewalprocessService"/>
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalprocess.service.impl.RenewalProcessFastServiceImpl" id="PA_newRenewalprocessService">
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="interestMarginService" ref="PA_interestMarginService"/>
		<property name="createPolicyService" ref="PA_createPolicyService"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="iPolicyLogService" ref="PA_policyLogService"/>
		<property name="renewalProcessFastDao" ref="PA_newRenewalProcessFastDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="precontProductDao" ref="PA_precontProductDao"/>
		<property name="pagecfgPrdCateRelaDao" ref="PA_pagecfgPrdCateRelaDao"/>
		<property name="renewalBillingDao" ref="PA_renewalBillingDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="renewChangeDao" ref="PA_renewChangeDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="autoextraDao" ref="PA_newAutoextraDao"/>
		<property name="commonBatchDao" ref="PA_commonBatchDao" />
	</bean>
	<bean class="com.nci.tunan.pa.batch.renewalprocess.dao.impl.RenewalProcessFastDaoImpl" id="PA_newRenewalProcessFastDao" parent="baseDao">
	</bean>
	<!-- 新续保处理end -->
	
	<!-- 新生存金抽档发放start -->
	<bean
		class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.dao.impl.SurvialAnnuityMaturityFastDaoImpl"
		id="PA_survialAnnuityMaturityFastDao" parent="baseDao">
	</bean>

	<bean
		class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.service.impl.SurvialAnnuityMaturityServiceFastImpl"
		id="PA_surAnnuityMaturityServiceFast" scope="prototype">
		<property name="survialAnnuityMaturityFastDao" ref="PA_survialAnnuityMaturityFastDao" />
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao" />
		<property name="releValidationService" ref="PA_releValidationService"/>
		<property name="calcSAMService" ref="PA_calcSAMService" />
		<property name="noticeService" ref="PA_noticeService" />
		<property name="policyLogService" ref="PA_policyLogService" />
		<property name="policyAccountDao" ref="PA_policyAccountDao" />
		<property name="capIAS" ref="PA_capIAS" />
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao" />
		<property name="businessProductDao" ref="PA_businessProductDao" />
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao" />
		<property name="contractAgentDao" ref="PA_contractAgentDao" />
		<property name="contractExtendDao" ref="PA_contractExtendDao" />
		<property name="premArapDao" ref="PA_premArapDao" />
		<property name="contractInvestDao" ref="PA_contractInvestDao" />
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao" />
		<property name="payDueListDao" ref="PA_payDueListDao" />
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao" />
		<property name="commonBatchDao" ref="PA_commonBatchDao" />
	</bean>
	
	<!-- 生存金抽档 -->
	<bean
		class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.SurvivalAnnuityExtraJobFast"
		id="PA_survivalAnnuityExtraJobFast" scope="prototype">
		<property name="surAnnuityMaturityServiceFast" ref="PA_surAnnuityMaturityServiceFast" />
	</bean>
	
	<!-- 生存金发放 -->
	<bean
		class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.SurvivalAnnuityIssueJobFast"
		id="PA_survivalAnnuityIssueJobFast" scope="prototype">
		<property name="surAnnuityMaturityServiceFast" ref="PA_surAnnuityMaturityServiceFast" />
	</bean>
	<!-- 新生存金抽档发放end -->
	
	<!-- 新续期抽档start -->
		<!-- 续期自动抽档批作业(底层数据实现方式：新优化方案测试方法) -->
    <bean id="PA_renewalAutomaticExtraFast"
        class="com.nci.tunan.pa.batch.automaticextraTest.RenewalAutomaticExtraFastJob"
        scope="prototype">
        <property name="extraServiceTest" ref="PA_extraServiceFast"></property>
    </bean>
    <bean id="PA_extraServiceFast"
        class="com.nci.tunan.pa.batch.automaticextraTest.service.impl.RenewAutomaticExtraFastServiceImpl" >
         <property name="renewAutomaticExtraDaoTest" ref="PA_renewAutomaticExtraDaoFast" />
         <property name="commonBatchDao" ref="PA_commonBatchDao" />
    </bean>
    <bean id="PA_renewAutomaticExtraDaoFast"
        class="com.nci.tunan.pa.batch.automaticextraTest.dao.impl.RenewAutomaticExtraFastDaoImpl" parent="baseDao">
    </bean>
    <!-- 新续期抽档end -->
    
</beans>