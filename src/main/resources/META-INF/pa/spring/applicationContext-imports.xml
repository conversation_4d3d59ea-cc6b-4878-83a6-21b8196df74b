<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 产品工厂客户端 -->
	<bean class="com.nci.tunan.pa.imports.impl.PRDServiceImpl" id="PA_prdIAS"/>
	<!-- 调用收付费平台 niuyu_wb 2014/12/03 -->
	<bean class="com.nci.tunan.pa.imports.impl.CAPServiceImpl" id="PA_capIAS"/>
	<bean class="com.nci.tunan.pa.imports.impl.CAPImportsAdapterServiceImpl" id="PA_capIAdapter"/>

	<!--营销支持客户端 -->
	<bean class="com.nci.tunan.pa.imports.impl.PRDServiceImpl" id="PA_mmsIAS"/>
	<!-- 新契约客户端 -->
	<bean class="com.nci.tunan.pa.imports.impl.NBServiceImpl" id="PA_nbIAS"/>
	<!-- 核保客户端 -->
	<bean class="com.nci.tunan.pa.imports.impl.UWServiceImpl" id="PA_uwIAS"/>
	
</beans>