<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<bean class="com.nci.tunan.pa.impl.common.boservice.impl.ContractMasterCompServiceImpl" id="PA_contractMasterCompService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdCompService" ref="PA_contractBusiProdCompService"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="payerCompService" ref="PA_payerCompService"/>
		<property name="customerCompService" ref="PA_customerCompService"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
		<property name="arapService" ref="CAP_premArapService"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="iCustomerDao" ref="PA_customerDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.common.boservice.impl.ContractBusiProdCompServiceImpl" id="PA_contractBusiProdCompService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractProductCompService" ref="PA_contractProductCompService"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.common.boservice.impl.ContractProductCompServiceImpl" id="PA_contractProductCompService">
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="payPlanCompService" ref="PA_payPlanCompService"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.common.boservice.impl.PayPlanCompServiceImpl" id="PA_payPlanCompService">
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.common.boservice.impl.PayerCompServiceImpl" id="PA_payerCompService">
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.common.boservice.impl.CustomerCompServiceImpl" id="PA_customerCompService">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
	</bean>
	<!-- 投保人、被保人、受益人信息（包含Customer所有信息）查询 start niuyu_wb 2015-01-28 -->
	<bean class="com.nci.tunan.pa.impl.common.boservice.impl.HolderInsuredBeneCustCompServiceImpl" id="PA_holderInsuredBeneCustCompService">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
	</bean>
	<!-- 投保人、被保人、受益人信息（包含Customer所有信息）查询 end -->
	<!-- 住院津贴限额配置  -->
	<bean class="com.nci.tunan.pa.impl.hospital.service.impl.HospitalBenefitLimitServiceImpl" id="PA_hospitalBenefitLimitService">
		<property name="hospitalBenefitLimitDao" ref="PA_hospitalBenefitLimitDao"/>
	</bean>
</beans>