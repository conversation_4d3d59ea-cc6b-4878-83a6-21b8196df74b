<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 投连单位价格查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000079.impl.QueryPriceListUccImpl" id="PA_iQueryPriceListUcc">
		<property name="queryPriceListService" ref="PA_queryPriceListService"/>
	</bean>
	<!-- 万能保单最新月度结算信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000088.impl.QueryMessageInfoListUccImpl" id="PA_iQueryMessageInfoListUcc">
		<property name="queryMessageListServiceImpl" ref="PA_queryMessageListServiceImpl"/>
	</bean>

	<!-- 投连信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000081.impl.InvestAccInfoQueryUccImpl" id="PA_investAccInfoQueryUcc">
		<property name="investAccInfoQueryService" ref="PA_investAccInfoQueryService"/>
	</bean>
	<!-- 保单挂起状态查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001000.impl.QueryGuaQiUccImpl" id="PA_IQueryGuaQiUcc">
		<property name="queryGuaQiService" ref="PA_queryGuaQiService"/>
	</bean>
	<!-- 投连退保查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000851.impl.QueryTuiBaoUccImpl" id="PA_IQueryTuiBaoUcc">
		<property name="iQueryTuiBaoService" ref="PA_iQueryTuiBaoService"/>
	</bean>
	<!-- 保单投资单位数及价格 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000843.impl.QueryUuitPriceListUccImpl" id="PA_IQueryUuitPriceListUcc">
		<property name="queryUuitPriceListService" ref="PA_queryUuitPriceListService"/>
	</bean>
	<!-- 投连价格查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000939.impl.QueryCowPriceListUccImpl" id="PA_IQueryCowPriceListUcc">
		<property name="iQueryCowPriceListService" ref="PA_iQueryCowPriceListService"/>
	</bean>
	<!-- 金钱柜信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000092.impl.AccInfoQueryUccImpl" id="PA_accInfoQueryUcc">
		<property name="accInfoQueryService" ref="PA_accInfoQueryService"/>
	</bean>
	<!-- 客户信息认证 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000041.impl.CustomerMsgQueryUccImpl" id="PA_ICustomerMsgQueryUcc">
		<property name="customerMsgQueryService" ref="PA_customerMsgQueryService"/>
	</bean>

	<!-- 追加保费查询（至尊双利） -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000030.impl.QueryDateMoneyListUccImpl" id="PA_queryDateMoneyListUCCImpl">
		<property name="queryMoneyService" ref="PA_queryMoneyService"/>
	</bean>
	<!-- 万能险账户价值结算查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000032.impl.QueryAccountCostUccImpl" id="PA_IQueryAccountCostUcc">
		<property name="queryAccountCostService" ref="PA_queryAccountCostService"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
	</bean>
	<!-- 累计升息基本信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000093.impl.QueryBaseMessageUccImpl" id="PA_IQueryBaseMessageUcc">
		<property name="queryBaseMessageService" ref="PA_queryBaseMessageService"/>
	</bean>
	<!-- 累计升息基本信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000694.impl.QueryDealListUccImpl" id="PA_IQueryDealListUcc">
		<property name="queryDealPasswordService" ref="PA_queryDealPasswordService"/>
	</bean>
	<!-- 人员新增 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000604.impl.AgentPersonAddUccImpl" id="PA_IAgentPersonAddUcc">
		<property name="agentPersonService" ref="PA_agentPersonService"/>
	</bean>
	<!-- 网点服务人员变化 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000230.impl.NetServePersonUccImpl" id="PA_INetServePersonUcc">
		<property name="netServePersonService" ref="PA_netServePersonService"/>
	</bean>
	<!-- 尊享人生险种可选责任信息  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000098.impl.SelectableDutyInfoQuery652UccImpl" id="PA_ISelectableDutyInfoQuery652Ucc">
		<property name="selectableDutyInfoQuery652Service" ref="PA_selectableDutyInfoQuery652Service"/>
	</bean>
	<!-- E事历-保单提醒功能查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000899.impl.PolicyRemindQueryUccImpl" id="PA_IPolicyRemindQueryUcc">
		<property name="policyRemindQueryService" ref="PA_policyRemindQueryService"/>
	</bean>


	<!-- 累积升息历史信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000094.impl.AccHisInfoQueryUccImpl" id="PA_IAccHisInfoQueryUcc">
		<property name="accHisInfoQueryService" ref="PA_accHisInfoQueryService"/>
	</bean>
	<!-- 追加保费查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000988.impl.AddPremQueryUccImpl" id="PA_addPremQueryUcc">
		<property name="addPremQueryService" ref="PA_addPremQueryService"/>
	</bean>
	<!-- 保单历史服务人员详细信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000836.impl.ComDetailQueryUccImpl" id="PA_comDetailQueryUcc">
		<property name="comDetailQueryService" ref="PA_comDetailQueryService"/>
	</bean>

	<!-- 满期给付通知书查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000103.impl.WebLPEdorGetNoticeQueryUccImpl" id="PA_webLPEdorGetNoticeQueryUcc">
		<property name="webLPEdorGetNoticeQueryService" ref="PA_webLPEdorGetNoticeQueryService"/>
	</bean>
	<!-- 网点授权 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000233.Impl.OrganGrantInsertUccImpl" id="PA_organGrantInsertUcc">
		<property name="organGrantInsertService" ref="PA_organGrantInsertService"/>
	</bean>
	<!-- 网点修改及开停业 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000624.impl.OrganAndStartBusiUpdateUccImpl" id="PA_organAndStartBusiUpdateUcc">
		<property name="organAndStartBusiUpdateService" ref="PA_organAndStartBusiUpdateService"/>
	</bean>
	<!-- 生日提醒 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000150.impl.CustBirthDayQueryUccImpl" id="PA_custBirthDayQueryUcc">
		<property name="custBirthDayQueryService" ref="PA_custBirthDayQueryService"/>
	</bean>

	<!-- 证书导入 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000847.impl.QualifInInsertUccImpl" id="PA_qualifInInsertUcc">
		<property name="qualifInInsertService" ref="PA_qualifInInsertService"/>
	</bean>
	
	
	<!-- 续期信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000043.impl.RenewInfoQueryUccImpl" id="PA_renewInfoQueryUcc">
		<property name="renewInfoQueryService" ref="PA_renewInfoQueryService"/>
	</bean>
	
	<!-- 保单受益人信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000015.impl.BeneInfoQueryUccImpl" id="PA_beneInfoQueryUcc">
		<property name="beneInfoQueryService" ref="PA_beneInfoQueryService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000014.impl.QueryPolicyInsuredUCCImpl" id="PA_queryPolicyInsuredUCC">
		<property name="queryPolicyInsuredService" ref="PA_queryPolicyInsuredService"/>
	</bean>

	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000061.impl.InsuredPolicyQueryListUccImpl" id="PA_queryInsuredPolicyListUCC">
		<property name="queryInsuredPolicyListService" ref="PA_queryInsuredPolicyListService"/>
	</bean>
	<!-- 被保险人职业类别查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000910.impl.GetInsuredOccQueryUccImpl" id="PA_IGetInsuredOccQueryUcc">
		<property name="getInsuredOccQueryService" ref="PA_getInsuredOccQueryService"/>
	</bean>
	<!-- 客户告知查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000068.impl.HealthImpartQueryUccImpl" id="PA_healthImpartQueryUcc">
		<property name="healthImpartQueryService" ref="PA_healthImpartQueryService"/>
	</bean>
	
	<!-- 团队新增  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000585.impl.AddNewTeamUccImpl" id="PA_IAddNewTeamUcc">
		<property name="addNewTeamService" ref="PA_addNewTeamService"/>
	</bean>
	
	<!--客户账号领取查询  -->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000654.impl.PolicyAccountReceiveUccImpl" id="PA_IPolicyAccountReceiveUcc">
         <property name="policyAccountReceiveService" ref="PA_policyAccountReceiveService"/>
   </bean>
	

		<!-- 领取信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000606.impl.LiveGetQueryUccImpl" id="PA_liveGetQueryUcc">
		<property name="liveGetQueryService" ref="PA_liveGetQueryService"/>
	</bean>


	<!-- 团队修改及开停业 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000586.impl.SalesOrganUpdateUccImpl" id="PA_updateSalesOrganUCC">
		<property name="salesOrganUpdateService" ref="PA_salesOrganUpdateService"/>
	</bean>
	
	<!-- 保单生存领取信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000022.impl.PayDueQueryListUccImpl" id="PA_queryPayDueListUCC">
		<property name="queryPayDueListService" ref="PA_queryPayDueListService"/>
	</bean>

	<!-- 投连保险账户信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000082.impl.ContractInvestQueryListUccImpl" id="PA_queryContractInvestListUCC">
		<property name="queryContractInvestListService" ref="PA_queryContractInvestListService"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000599.impl.AgentCancleUCCImpl" id="PA_agentCancleUCC">
		<property name="agentCancleService" ref="PA_agentCancleService"/>
	</bean>
	<!-- 生存领取历史划款信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000028.impl.PayDueHistoryTransferQueryUccImpl" id="PA_IPayDueHistoryTransferQueryUcc">
		<property name="payDueHistoryTransferQueryService" ref="PA_payDueHistoryTransferQueryService"/>
	</bean>	
	

		<!-- 投连保单信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000175.impl.InvestPolicyInfoQueryUccImpl" id="PA_IInvestPolicyInfoQueryUcc">
	    <property name="investPolicyInfoQueryService" ref="PA_investPolicyInfoQueryService"/>
	</bean>
	

	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000049.impl.QueryPolicyCashValueListUCCImpl" id="PA_queryPolicyCashValueListUCC">
		<property name="queryPolicyCashValueListService" ref="PA_queryPolicyCashValueListService"/>
	</bean>

	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000007.impl.BonusInfoQueryCountUCCImpl" id="PA_IBonusInfoQueryCountUCC">
		<property name="bonusInfoQueryCountService" ref="PA_bonusInfoQueryCountService"/>
	</bean>

	<!-- 续期交费查询与校检接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000684.impl.PremArapQueryListUccImpl" id="PA_queryPremArapListUCC">
		<property name="queryPremArapListService" ref="PA_queryPremArapListService"/>
	</bean>
	
	<!-- 保单投保人信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000009.impl.QueryPolicyHolderUCCImpl" id="PA_IQueryPolicyHolderUCC">
		<property name="queryPolicyHolderService" ref="PA_queryPolicyHolderService"/>
	</bean>

	<!-- 投连保单状态报告查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000109.impl.QueryFundTransHisUCCImpl" id="PA_IQueryFundTransHisUCC">
		<property name="queryFundTransHisService" ref="PA_queryFundTransHisService"/>
	</bean>
	
	<!-- i添财投资连结接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000986.impl.QueryRenturnRateUCCImpl" id="PA_iQueryRenturnRateUCC">
		<property name="iQueryRenturnRateService" ref="PA_iQueryRenturnRateService"/>
	</bean>
	<!--万能险新契约账户价值查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000029.impl.UniversalNBAcountQueryUccImpl" id="PA_IUniversalNBAcountQueryUcc">
	 <property name="universalNBAcountQueryService" ref="PA_universalNBAcountQueryService"/>	
	</bean>
    <!-- 投连账户结算历史信息查询 -->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000083.impl.InvestAccountHistoryQueryUccImpl" id="PA_IInvestAccountHistoryQueryUcc">	
	<property name="investAccountHistoryQueryService" ref="PA_investAccountHistoryQueryService"/>	
	</bean>
	 
	    <!-- 领取信息汇总查询 -->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000686.impl.ReceiveInfoSummaryQueryUccImpl" id="PA_IReceiveInfoSummaryQueryUcc">	
	 <property name="receiveInfoSummaryQueryService" ref="PA_receiveInfoSummaryQueryService"/>	
	</bean>

	<!-- 保单密码校验  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000923.impl.PwValidUccImpl" id="PA_IPwValidUcc">
		<property name="pwValidService" ref="PA_pwValidService"/>
		<property name="commonService" ref="PA_commonService" />
		<property name="outterPasIAS" ref="PA_pasIAS"/>
	</bean>

	<!-- 保单信息查询接口（万能险账户信息） -->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001127.impl.ContUniversalAccountQueryUccImpl" id="PA_contUniversalAccountQueryUcc">
 		<property name="contUniversalAccountQueryService" ref="PA_contUniversalAccountQueryService"/>
 	</bean>
	

		<!-- 红利历史信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000020.impl.BonusHisInfoQueryUccImpl" id="PA_bonusHisInfoQueryUcc">
		<property name="bonusHisInfoQueryService" ref="PA_bonusHisInfoQueryService"/>
	</bean>

	<!-- 万能险实时账户查询接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001406.impl.QueryUniversalAccountUccImpl" id="PA_iQueryUniversalAccountUcc">
		<property name="iQueryUniversalAccountService" ref="PA_iQueryUniversalAccountService"/>
	</bean>

	    <!-- 保单受益人信息查询接口 -->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001398.impl.BenefitInfoQueryUccImpl" id="PA_IBenefitInfoQueryUcc">	
	  <property name="benefitInfoQueryService" ref="PA_benefitInfoQueryService"/>	 
	</bean> 
	<!--保单查询接口(查询保单详细内容 )-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000550.impl.ViewPolicyInfoUccImpl" id="PA_iviewPolicyInfoUcc">	
	     <property name="iviewPolicyInfoService" ref="PA_iviewPolicyInfoService"/>	
	</bean>
	<!--查询保单投保人信息-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001394.impl.QueryPolicyHolderBasicInfoUCCImpl" id="PA_queryPolicyHolderBasicInfoUCC">	
	     <property name="queryPolicyHolderBasicInfoService" ref="PA_queryPolicyHolderBasicInfoService"/>	
	</bean>
	
	<!-- 保单险种责任信息查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001402.impl.PolicyBusiDutyUccImpl" id="PA_policyBusiDutyUcc">
		<property name="policyBusiDutyService" ref="PA_policyBusiDutyService"/>
	</bean>
	    <!-- 投保人和被保险人信息变更接口 -->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000053.impl.InformationModifyUccImpl" id="PA_IInformationModifyUcc">	
	  <property name="csCustomerServiceImpl" ref="PA_csCustomerServiceImpl"/>	 
	  <property name="csMainStreamService" ref="PA_csMainStreamService"/>	 
	  <property name="pasIAS" ref="PA_pasIAS"/>	 
	  <property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>	 
	  <property name="cusAcceptUCC" ref="PA_cusAcceptUCC"/>	 
	  <property name="csEndorseCCUCC" ref="PA_custBaseInfoChgUCC"/>	 
	  <property name="ICsEndorsePLUCC" ref="PA_ICsEndorsePLUCC"/>	 
	  <property name="cateGoryUpdateUCC" ref="PA_cateGoryUpdateUCC"/>	 
	  <property name="cusApplicationService" ref="PA_cusApplicationService"/>
	  <property name="autoUWUCC" ref="PA_autoUWUCC"/>
	  <property name="autoInspectUCC" ref="PA_autoInspectUCC"/>
	  <property name="csEffectUCC" ref="PA_csEffectUCC"/>
	  <property name="cusAcceptService" ref="PA_cusAcceptService"/>
	</bean>
	<!-- 保单被保险人信息查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001396.impl.QueryInsuredMessageUccImpl" id="PA_queryInsuredMessageUcc">
		<property name="queryInsuredMessageService" ref="PA_queryInsuredMessageService"/>
	</bean>
	<!-- 保单险种信息查询接口   -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001400.impl.PolResikMessageUccImpl" id="PA_IPolResikMessageUcc">
		<property name="polResikMessageService" ref="PA_polResikMessageService"/>
	</bean>
	
	
	<!-- 保单基本信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001392.impl.BusinessCentreQueryUccImpl" id="PA_businessCentreQueryUcc">
		<property name="businessCentreQueryService" ref="PA_businessCentreQueryService"/>
	</bean>	
	<!-- 续期缴费账号查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000838.impl.RenewalPaymentIDQueryUccImpl" id="PA_renewalPaymentIDQueryUcc">
		<property name="renewalPaymentIDQueryService" ref="PA_renewalPaymentIDQueryService"/>
	</bean>	
	<!-- 查询个人保单详细信息 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000011.impl.QueryPersonalPolicyDetailUccImpl" id="PA_queryPersonalPolicyDetailUcc">
		<property name="iQueryPersonalPolicyDetailService" ref="PA_iQueryPersonalPolicyDetailService"/>
		<property name="prdProductQueryService" ref="PA_prdService"/>
	</bean>	

	<!-- 保单列表查询接口 -->
	 <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001390.impl.ContractMasterQueryListUccImpl" id="PA_IContractMasterQueryListUcc">
		<property name="contractMasterQueryListService" ref="PA_contractMasterQueryListService"/>
	</bean>	 
	<!-- 账户部分查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000857.impl.BalanceQueryUccImpl" id="PA_balanceQueryUcc">
		<property name="balanceQueryService" ref="PA_balanceQueryService"/>
	</bean>
	<!--查询年度分红业绩报告书-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000151.impl.QueryYearBonusReportUCCImpl" id="PA_queryYearBonusReportUCC">	
	     <property name="queryYearBonusReportService" ref="PA_queryYearBonusReportService"/>	
	</bean>
	<!--查询年度分红业绩报告书_现金分红接口-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002806.impl.QueryYearBonusReportUCCImpl" id="PA_queryYearBonusReportForCashUCC">	
	     <property name="queryYearBonusReportService" ref="PA_queryYearBonusReportForCashService"/>	
	</bean>
	<!-- 追加保费提交接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000863.impl.ApsEdorUccImpl" id="PA_IApsEdorUcc">
		<property name="apsEdorService" ref="PA_apsEdorService"/>
	</bean>
	
	<!-- 续收查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000184.impl.RenewalMessageQueryUccImpl" id="PA_renewalMessageQueryUcc">
		<property name="renewalMessageQueryService" ref="PA_renewalMessageQueryService"/>
	</bean>
	<!-- 保单到期领取提醒 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000144.impl.PolicyDueToReceiveReminderUccImpl" id="PA_policyDueToReceiveReminderUcc">
		<property name="policyDueToReceiveReminderService" ref="PA_policyDueToReceiveReminderService"/>
	</bean>
	<!-- R00101000142生存领取(给付)信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000142.impl.PayDueQueryInfoUccImpl" id="PA_iPayDueQueryInfoUcc">
		<property name="iPayDueQueryInfoService" ref="PA_iPayDueQueryInfoService"/>
	</bean>
	<!-- 再保险 影响查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r06401001853.impl.NoteInfoQueryUccImpl" id="PA_noteInfoQueryUcc">
		<property name="noteInfoQueryService" ref="PA_noteInfoQueryService"/>
	</bean>
	
	<!--健康告知查询-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r06401001856.impl.QueryCustomerHealthInfoUCCImpl" id="PA_queryCustomerHealthInfoUCC">	
	     <property name="queryCustomerHealthInfoService" ref="PA_queryCustomerHealthInfoService"/>	
	</bean>
	
	<!--特约查询-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r06401001861.impl.QueryPolicyConditionUCCImpl" id="PA_queryPolicyConditionUCC">	
	     <property name="queryPolicyConditionService" ref="PA_queryPolicyConditionService"/>	
	</bean>
	
	<!-- 再保险 受益人查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r06401001871.impl.ZBBenefitInfoQueryUccImpl" id="PA_zbBenefitInfoQueryUcc">
	<property name="zbBenefitInfoQueryService" ref="PA_zbBenefitInfoQueryService"/>
	</bean>
	
	<!--各保单健康告知查询（新核心）-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r06401001887.impl.QueryPolicyCustomerHealthImpartUccImpl" id="PA_queryPolicyCustomerHealthImpartUcc">	
	     <property name="queryPolicyCustomerHealthImpartService" ref="PA_queryPolicyCustomerHealthImpartService"/>	
	</bean>
	
	<!--保单挂起状态修改（理赔保单挂起接口）-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102000090.impl.LockCSAndREForCLMUccImpl" id="PA_lockCSAndREForCLMUcc">	
	     <property name="lockCSAndREForCLMService" ref="PA_lockCSAndREForCLMService"/>	
	</bean>
	
	<!-- 短期意外险被保人信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r04301000337.impl.QueryInsuredInfoByPolicyCodeUccImpl" id="PA_queryInsuredInfoByPolicyCodeUcc">
		<property name="queryInsuredInfoByPolicyCodeService" ref="PA_queryInsuredInfoByPolicyCodeService"/>
	</bean>
	
	<!--操作履历下生调结果查询（新核心）-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r06401001842.impl.SurveyResultQueryUccImpl" id="PA_surveyResultQueryUccImpl">	
	     <property name="surveyResultQueryService" ref="PA_surveyResultQueryService"/>	
	</bean>
	
	<!--操作履历下生调结果查询（新核心）-->
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000070.impl.ContractFundSettlementUccImpl" id="PA_contractFundSettlementUcc">	
	     <property name="contractFundSettlementService" ref="PA_contractFundSettlementService"/>	
	      <property name="prdIAS" ref="PA_prdIAS"/>	
	</bean>
	
	<!-- 申请分红通知书补打接口 -->
	 <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102001169.impl.ApplyDividendPrintUccImpl" id="PA_applyDividendPrintUcc">	
		<property name="applyDividendPrintService" ref="PA_applyDividendPrintService"/>
	</bean>

	<!-- 当日年化收益查询接口 -->
	 <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000960.impl.QueryAnnualYieldInfoUccImpl" id="PA_IQueryAnnualYieldInfoUcc">	
		<property name="queryAnnualYieldInfoService" ref="PA_queryAnnualYieldInfoService"/>
	</bean>

	
	<!--投保单号快速查询是否存在服务 -->
	<bean class="com.nci.tunan.pa.impl.judgeapplycodeexit.ucc.impl.JudgeApplyCodeExitUccImpl" id="PA_judgeApplyCodeExitUcc">	
	     <property name="judgeApplyCodeExitService" ref="PA_judgeApplyCodeExitService"/>	
	</bean>
	
	<!-- 交退费编号快速查询是否存在服务 -->
<!-- 	<bean class="com.nci.tunan.pa.impl.judgeunitnumberExist.ucc.impl.JudgeUnitNumberExistUccImpl" id="PA_judgeUnitNumberExistUcc">	
	     <property name="judgeUnitNumberExistService" ref="PA_judgeUnitNumberExistService"/>	
	</bean> -->
	
	<!-- 保单信息服务人员轨迹查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000787.impl.PolicyServiceTrackQueryUccImpl" id="PA_IPolicyServiceTrackQueryUcc">	
	     <property name="policyServiceTrackQueryService" ref="PA_policyServiceTrackQueryService"/>	
	</bean>



	
	<!-- 客户地址信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001167.impl.CustAddressQueryUccImpl" id="PA_ICustAddressQueryUcc">
	     <property name="custAddressQueryService" ref="PA_custAddressQueryService"/>
	</bean>    
	<!-- 团体个人单和个人保单特约内容查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r064********.impl.QueryPaContentUccImpl" id="PA_IQueryPaContentUcc">	
	     <property name="queryPaContentService" ref="PA_queryPaContentService"/>	
	</bean>

	<!-- 工行纸质保单补充接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000984.impl.PaperSupplementUccImpl" id="PA_IPaperSupplementUcc">	
	     <property name="paperSupplementService" ref="PA_paperSupplementService"/>	
	</bean>

    <!-- 账户注销查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000498.impl.AccountCancelQueryUccImpl" id="PA_accountCancelQueryUcc">	
	     <property name="accountCancelQueryService" ref="PA_accountCancelQueryService"/>	
	</bean>
	<!-- 渠道同步查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001183.impl.ChannelSynchroQueryUccImpl" id="PA_channelSynchroQueryUcc">	
	     <property name="channelSynchroQueryService" ref="PA_channelSynchroQueryService"/>	
	</bean>
	<!-- 管理費同步查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001205.impl.ManagementCostSynchroQueryUccImpl" id="PA_managementCostSynchroQueryUcc">	
	     <property name="managementCostSynchroQueryService" ref="PA_managementCostSynchroQueryService"/>	
	</bean>
	<!--保单相关影像文件查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000100.impl.QueryImageFileUccImpl" id="PA_queryImageFileUcc">	
	     <property name="queryImageFileService" ref="PA_queryImageFileService"/>	
	</bean>
    <!-- 保单红利查询 -->
     <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000869.impl.PolicyBonusQueryUccImpl" id="PA_policyBonusQueryUcc">
      <property name="policyBonusService" ref="PA_policyBonusService"/> 
     </bean>
<!-- 意外险产品信息查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000792.impl.BusiProdQueryUccImpl" id="PA_busiProdQueryUcc">	
	     <property name="busiProdQueryService" ref="PA_busiProdQueryService"/>	
	</bean>

<!-- 产品是否续保 -->	
    <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001019.impl.PolicyRenewQueryUccImpl" id="PA_policyRenewQueryUccImpl">
    <property name="iPolicyRenewQueryService" ref="PA_iPolicyRenewQueryService"/>
    </bean>

	<!-- 万能险结算接口  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001015.impl.QueryInsuAccInfoUccImpl" id="PA_queryInsuAccInfoUcc">	
	     <property name="queryInsuAccInfoService" ref="PA_queryInsuAccInfoService"/>	
	</bean>
<!-- 分红信息查询cjk -->

 <bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001109.impl.AllotBonusQueryUccImpl" id="PA_iAllotBonusQueryUcc">
 
   <property name="iAllocatBnusQueryService" ref="PA_iAllocatBnusQueryService"/>
 </bean>
  <!-- 客户保单信息查询接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000042.impl.CustomerPolicyMsgQueryUccImpl" id="PA_customerPolicyMsgQueryUcc"> 
	 	<property name="customerPolicyMsgQueryService" ref="PA_customerPolicyMsgQueryService"/>
	</bean>
  <!-- 客户身份信息验真开关-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002126.impl.AuthenticationSwitchUccImpl" id="PA_AuthenticationSwitchUcc"> 
	 	<property name="authenticationSwitchService" ref="PA_authenticationSwitchService"/>
	</bean>
	
	<!-- 电话中心保单信息查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002246.impl.TelephoneCenterQueryPolicyInfoImpl" id="PA_telephoneCenterQueryPolicyInfoUcc">
		<property name="telephoneCenterQueryPolicyInfoService" ref="PA_telephoneCenterQueryPolicyInfoService"/>
	</bean> 
	<!-- 合同机构同步接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102002243.impl.CooperationStructureSynchronizationUccImpl" id="PA_CooperationStructureSynchronizationUcc">
		<property name="cooperationStructureSynchronizationService" ref="PA_cooperationStructureSynchronizationService"/>
	</bean> 
	<!-- 业务员重复手机甄别接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002283.impl.AgentRepeatPhoneIdentityUccImpl" id="PA_agentRepeatPhoneIdentityUcc">
		<property name="agentRepeatPhoneIdentityService" ref="PA_agentRepeatPhoneIdentityService"/>
	</bean>
	
	<!-- 个人保单信息查询（MMS）-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002255.impl.PersonalPolicyInfoQueryForMMSUccImpl" id="PA_personalPolicyInfoQueryForMMSUcc">
		<property name="personalPolicyInfoQueryForMMSService" ref="PA_personalPolicyInfoQueryForMMSService"/>
	</bean>
	
	<!-- 订单号快速查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.ordernoquery.impl.OrderNoQueryUccImpl" id="PA_orderNoQueryUcc">
		<property name="orderNoQueryService" ref="PA_orderNoQueryService"/>
	</bean>
	<!--附加险万能险新契约账户价值查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000034.impl.AccountWorthQueryUccImpl" id="PA_accountWorthQueryUcc">	
	     <property name="accountWorthQueryService" ref="PA_accountWorthQueryService"/>	
	</bean>
	<!--续期缴费提交接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102002323.impl.RenewalPaymentSubmittedUccImpl" id="PA_renewalPaymentSubmittedUcc">	
		<property name="renewalPaymentSubmittedService" ref="PA_renewalPaymentSubmittedService"/>
	</bean>
	<!-- 续期缴费支付失败回写核心接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00102002324.impl.ReneFeePayFailBackCoreUCCImpl" id="PA_reneFeePayFailBackCoreUCC">	
		<property name="reneFeePayFailBackCoreService" ref="PA_reneFeePayFailBackCoreService"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="renewCollectionFeeService" ref="PA_renewCollectionFeeService"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
	</bean>
	
	<!-- 电话中心客户层交易密码修改接口 -->
	<bean class="com.nci.tunan.cs.impl.peripheral.ucc.r00102000543.impl.TelephoneCenterUpdatePasswordUccImpl" id="PA_telephoneCenterUpdatePasswordUcc" parent="PA_commonOutterUcc">
		<property name="csEndorseFKUCC" ref="PA_csEndorseFKUCC"/>
	</bean>
	<!-- 贷款续贷 -->
	<bean id="policyLoanRenewal_cs" class="com.nci.tunan.cs.impl.peripheral.ucc.r00102002295.impl.PolicyLoanRenewalUccImpl" >
	<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>
		<property name="cusAcceptUCC" ref="PA_cusAcceptUCC"/>
	    <property name="csEndorseRLUCC" ref="PA_csEndorseRLUCC"/>
		<property name="cusApplicationService" ref="PA_cusApplicationService"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="outterDealUCC" ref="PA_outterDealUcc"/>
		<property name="iBankAccountDao" ref="PA_bankAccountDao"></property>
		<property name="csPremArapDao" ref="PA_csPremArapDao"/>
	</bean>
	
	<!-- 附加险险种信息查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001691.impl.RiderProductRiskInfoQueryUccImpl" id="PA_riderProductRiskInfoQueryUcc">
		<property name="riderProductRiskInfoQueryService" ref="PA_riderProductRiskInfoQueryService"/>
	</bean>
	
	<!-- 附加险查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002280.impl.AdditionRiskQueryUccImpl" id="PA_additionRiskQueryUcc">
		<property name="additionRiskService" ref="PA_additionRiskService"/>
	</bean>
	
	<!-- 附加险代码查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001716.impl.AdditionRiskCodeQueryUccImpl" id="PA_additionRiskCodeQueryUcc">
		<property name="additionRiskCodeQueryService" ref="PA_additionRiskCodeQueryService"/>
	</bean>
	<!--交易密码是否重置  -->
	<bean id="passWordIfReset" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000964.impl.PaPassWordIfResetUccImpl">
	<property name="iPaPassWordIfResetService" ref="iPaPassWordIfResetService"></property>
	</bean>
	<!--  -->
	<bean id="iSignBackUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101001118.impl.SignBackUccImpl">
	 <property name="iPaSignBackService" ref="iPaSignBackService"></property>
	</bean>

	<!-- 保单验真查询接口 -->
	<bean  class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002484.impl.QueryPolicyVerifyUccImpl" id="PA_queryPolicyVerifyUcc">
	   <property name="queryPolicyVerifyService" ref="PA_queryPolicyVerifyService"></property>
	</bean>
	<!-- 保单类型查询 -->
	<bean id="queryPolicyTypes" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002522.impl.PolicyTypeUccImpl">
	<property name="iPolicyTypeService" ref="iPolicyTypeService"></property>
	</bean>

	<!-- 电子函件服务查询接口  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002512.impl.IsChooseEmailUccImpl" id="PA_isChooseEmailUcc">
		<property name="isChooseEmailService" ref="PA_isChooseEmailService"/>
	</bean>
	
	<!-- 居民税收查询 -->
	
	<bean id="ipeopleIncomeQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002547.impl.peopleIncomeQueryUccImpl">
	<property name="ipeopleIncomeQueryService" ref="ipeopleIncomeQueryService"></property>
	
	
	</bean>
	
	<!-- 红利信息查询 -->
	<bean id="PA_queryBonusInfoUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002466.impl.QueryBonusInfoUccImpl">
		<property name="bonusInfoService" ref="PA_queryBonusInfoServiceImpl"/>
	</bean>
	
	<!-- 万能险结算状态信息查询 -->
	<bean id="PA_settleAccountsOfOmnipotentInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002468.impl.SettleAccountsOfOmnipotentInfoUccImpl">
		<property name="settleAccountsOfOmnipotentInfoService" ref="PA_settleAccountsOfOmnipotentInfoService"/>
	</bean>
	<!--by zhaoyoan_wb 万能险账户当前信息查询 -->
	<bean id="PA_universalAccountInfoQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101000031.impl.UniversalAccountInfoQueryUccImpl">
		<property name="universalAccountInfoQueryService" ref="PA_universalAccountInfoQueryService"></property>
	</bean>
	<!-- 红利信息查询接口 -->
	<bean id="PA_dividendInformationQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002568.impl.DividendInformationQueryUccImpl" >
		<property name="iDividendInformationQueryService" ref="PA_dividendInformationQueryService"/>
	</bean>
	<!-- 万能产品最新信息查询 -->
	<bean id="PA_universalPolicyAccountUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.universalPolicyAccount.impl.UniversalPolicyAccountUccImpl" >
		<property name="csPolicyInfoChangeUCC" ref="PA_csPolicyInfoChangeUCC"/>
		<property name="universalPolicyAccountService" ref="PA_universalPolicyAccountService"/>
		<property name="csPolicyInfoChangeOneService" ref="PA_csPolicyInfoChangeOneService"/>
	</bean>
	
	<!-- 个人税收居民身份信息提交接口 -->
	<bean id="PA_peopleIncomeCmitUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00102002548.impl.PeopleIncomeCmitUccImpl" >
	    <property name="peopleIncomeCmitService" ref="PA_peopleIncomeCmitService"/>
	</bean>
	
	<!-- 险种责任接口 -->
	<bean id="PA_busiItemDutyUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002681.impl.BusiItemDutyUccImpl" >
	    <property name="busiItemDutyService" ref="PA_busiItemDutyService"/>
	</bean>
	
	<!-- 连带被保险人信息接口 -->
	<bean id="PA_jointInsurerInfUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002683.impl.JointInsurerInfUccImpl" >
	    <property name="jointInsurerInfService" ref="PA_jointInsurerInfService"/>
	</bean>
	

	<!-- 客户保单查询  -->
	<bean id="PA_customerPolicyInfoUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002633.impl.CustomerPolicyInfoUccImpl" >
	    <property name="customerPolicyInfoService" ref="PA_customerPolicyInfoService"/>
	</bean>
	
	<!-- 保单状态接口 -->
	<bean id="PA_policyStateInfUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002677.impl.PolicyStateInfUccImpl" >
	    <property name="policyStateInfService" ref="PA_policyStateInfService"/>
	</bean>

	<!--保险账户其他信息轨迹信息接口  -->
	 <bean id="iPolicyAcountOherInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002705.impl.PolicyAcountOherInfoUccImpl">
	     <property name="iPolicyAcountOherInfoService" ref="PA_iPolicyAcountOherInfoService"></property>  
	 </bean>

	<!-- 保险帐户信息接口 -->
	<bean id="PA_insuAccountInfoQueryForRIUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002707.impl.InsuAccountInfoQueryForRIUccImpl" >
	    <property name="insuAccountInfoQueryForRIService" ref="PA_insuAccountInfoQueryForRIService"/>
	</bean>
	<!-- 个单被保人信息接口 -->
	<bean id="PA_contInsuredInfoQueryForRIUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002709.impl.ContInsuredInfoQueryForRIUccImpl" >
	    <property name="contInsuredInfoQueryForRIService" ref="PA_contInsuredInfoQueryForRIService"/>
	</bean>

	
	<!-- 保单未领生存金查询 -->
	<bean id="PA_queryUnclaimLiGoldUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002647.impl.QueryUnclaimLiGoldUccImpl" >
	    <property name="queryUnclaimLiGoldService" ref="PA_queryUnclaimLiGoldService"/>
	</bean>
	
	<!-- 保费缴费信息接口 -->
	<bean id="PA_premPaymentInfUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002697.impl.PremPaymentInfUccImpl" >
	    <property name="premPaymentInfService" ref="PA_premPaymentInfService"/>
	</bean>
	<!-- 保险帐户表记价履历接口 -->
	 <bean id="queryPolicyHistoryAcountRecord" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002679.impl.PolicyAcountUccImpl" >
	  <property name="iqueryacountservice" ref="PA_iqueryacountservice"></property>
	 </bean>
	 
	<!-- 关联保单信息查询接口 -->
	 <bean id="PA_associationPolicyUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002630.impl.AssociationPolicyUccImpl" >
	  <property name="associationPolicyService" ref="PA_associationPolicyService"></property>
	 </bean>
	 
	<!-- 投被保人基本信息校验及电话重复性校验接口 -->
	<bean id="PA_custBaseInfoAndTelValidateUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00102002743.impl.CustBaseInfoAndTelValidateUccImpl" >
	    <property name="custBaseInfoAndTelValidateService" ref="PA_custBaseInfoAndTelValidateService"/>
	</bean>
	 <!--重要信息和关系信息校验接口  -->
	 <bean id="importentInfoQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00102002741.impl.ImportentInfoQueryUccImpl">
	 <property name="PA_importemtInfoQueryService" ref="PA_importemtInfoQueryService"></property>
	 </bean>
	 <!-- 社保状态变更查询接口 -->
	 <bean id="PA_IYDBQContQuerySrvBindingQSPortUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002758.impl.YDBQContQuerySrvBindingQSPortUccImpl">
	 <property name="yDBQContQuerySrvBindingQSPortService" ref="PA_iYDBQContQuerySrvBindingQSPortService"></property>
	 </bean>
	 <!--税收居民身份采集校验接口-->
	 <bean id="PA_identityCollectionCheckUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002764.impl.IdentityCollectionCheckUccImpl">
	 <property name="identityCollectionCheckService" ref="PA_identityCollectionCheckService"></property>
	 </bean>
	 
	  <!-- 根据保单号查询业务人员 -->
	 <bean id="PA_QueryAgentInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r04301000286.impl.QueryAgentInfoUccImpl">
	    <property name="queryAgentService" ref="PA_QueryAgentServiceImpl"></property>
	 </bean>
	 
	 <!-- 健康无忧（尊享版）老客户校验接口 -->
	 <bean id="PA_OldCustomerVerifyUCC" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002797.impl.OldCustomerVerifyUCCImpl">
	    <!-- <property name="oldCustomerVerifyService" ref="PA_OldCustomerVerifyServiceImpl"></property> -->
	 </bean>
	 <!-- 查询现金分红历史信息 -->
	 <bean id="PA_historyDivedendUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101002808.impl.QueryCashDividendUccImpl">
	      <property name="queryCashDividendService" ref="PA_HisttoryDivedendService"></property>
	 </bean>
	 
	 <!--分红类型判断接  -->
	 <bean id="PA_IJudgeBonusTypeUcc" class="com.nci.tunan.cs.impl.peripheral.ucc.r00101002810.impl.JudgeBonusTypeUccImpl">
		<property name="judgeBonusTypeService" ref="PA_judgeBonusTypeServiceImpl"></property>
	 </bean>
	 
	 <!-- 双录自保件 -->
	 <bean id="PA_SelfInsuredPartUcc" class="com.nci.tunan.cs.impl.peripheral.ucc.r00101002795.impl.QuerySelfInsuredPartUccImpl">
	      <property name="querySelfInsuredPartService" ref="PA_SelfInsuredPartService"></property>
	 </bean>
	 
	 <!-- 证件类型与年龄校验接口 -->
	 <bean id="PA_IdocumentTypeAndAgeCheckUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00102002992.impl.documentTypeAndAgeCheckUccImpl">
	      <property name="documentTypeAndAgeCheckService" ref="PA_IdocumentTypeAndAgeCheckService"></property>
	 </bean>

	 <!-- 投保人保单缴费信息查询接口 -->
	 <bean id="paqueryFeeAmount" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101003048.impl.PolicyHolderQueryPayFeeAmountUccImpl">
	 <property name="policyHolderQueryPayFeeAmountService" ref="policyHolderQueryPayFeeAmountService"></property>
	 </bean>

	 <!-- 客户九要素校验  -->
	 <bean id="PA_PolicyNineFaceotyCheckUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101003049.impl.PolicyNineFactoryCheckUccImpl">
	        <property name="policyNineFactoryCheckService" ref="PA_PolicyNineFactoryCheckService"></property>
	 </bean>

	 
	 <!-- 核心系统保全规则校验接口 -->
	 <bean id="PA_ICoreSystemRescueRulecheckUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101003050.impl.coreSystemRescueRulecheckUccImpl">
	      <property name="rulecheckService" ref="PA_ICoreSystemRescueRulecheckService"></property>
	 </bean>
	
	
	 
	 <!-- 客户信息真实性校验接口 -->
	 <bean id="ICustomerInfoAuthenticityCheckUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101003066.impl.CustomerInfoAuthenticityCheckUccImpl">
	      <property name="customerInfoAuthenticityCheckService" ref="PA_CustomerInfoAuthenticityCheckService"></property>
	 </bean>
	  <!-- IO客户职业类别变更试算 -->
	 <bean id="PA_InsuredJobChangeQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06802003102.impl.InsuredJobChangeQueryUccImpl">
	 </bean>
	 
	 <!-- IO客户职业类别变更试算 -->
	 <bean id="PA_InsuredJobChangeUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06802003103.impl.InsuredJobChangeUccImpl">
	 </bean>
	 
	 <!--同步该客户下查询保单接口（同步范围查询接口）  -->
	  <bean id="IQueryAllPolicysOfTheCustomerUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003111.impl.QueryAllPolicysOfTheCustomerUccImpl">
	      <property name="queryallpolicysOftheCustomerService" ref="PA_iQueryallpolicysOftheCustomerService"></property>
	 </bean>
	 
	 <!-- 保单列表信息查询接口 -->
	 <bean id="PA_IQueryPolicyListInformationUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003109.impl.QueryPolicyListInformationUccImpl">
	      <property name="service" ref="PA_IQueryPolicyListInformationService"></property>
	 </bean>
	 
	 <!-- 保单复效查询接口 -->
	 <bean id="PA_CSContReinstateQueryUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003160.impl.CSContReinstateQueryUccImpl">
	 </bean>
	
	 <!-- 心圆福职域保单列表查询接口 -->
	 <bean id="PA_XYFZPolicyListUccUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900086.impl.XYFZPolicyListUccUccImpl">
	      <property name="ixyfzPolicyListService" ref="PA_XYFZPolicyListService"></property>
	 </bean>
	 
	 <!-- 心圆福职域保单详情查询接口 -->
	 <bean id="PA_XYFZPolicyDetailsUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900087.impl.XYFZPolicyDetailsUccImpl">
	      <property name="ixyfzPolicyDetailsService" ref="PA_XYFZPolicyDetailsService"></property>
	 </bean>
	 
	  <!--新增附加险- 保单列表信息查询接口 -->
	 <bean id="PA_QueryNsPolicyUcc" class="com.nci.tunan.cs.impl.peripheral.ucc.r06401003137.impl.QueryNsPolicyUccImpl">
	      <property name="iQueryNsPolicyService" ref="PA_IQueryNsPolicyService"></property>
	 </bean>
	 
	  <!--柜面自助终端  红利信息查询(打印通知书类)-->
	 <bean id="IBonusInfoQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003161.impl.BonusInfoQueryUccImpl">
	      <property name="bonusInfoQueryService" ref="PA_IBonusInfoQueryService"></property>
	 </bean>
	 <!--可续保转换险种清单列表  -->
	 <bean id="PA_eableRenewRisk" class="com.nci.tunan.pa.impl.peripheral.ucc.r06802003146.impl.EableRenewRiskUccImpl"/>
	 
	 <!-- 续期交费信息变更-保单列表查询接口 -->
	 <bean id="PA_IQueryRenewalPoliciesListUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003158.impl.QueryRenewalPoliciesListUccImpl">
	      <property name="service" ref="PA_IQueryRenewalPoliciesListService"></property>
	 </bean>
	  <!-- 续保险种转换-保单列表查询接口 -->
	 <bean id="PA_IQueryTransformationPolicyListUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003147.impl.QueryTransformationPolicyListUccImpl"/>
	 
	 
     	 <!-- 受理号查询受理详情 -->
	 <bean id="PA_QueryAgentDetailUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06801003144.impl.QueryAgentDetailUccImpl">
	      <property name="queryAgentDetailService" ref="PA_QueryAgentDetailService"></property>
	 </bean>
	 
	 <!-- 微信续投保表单信息查询接口 -->
	 <bean id="PA_IQueryWechatRenewalUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900020.impl.QueryWechatRenewalUccImpl">
	      <property name="queryWechatRenewalService" ref="PA_IQueryWechatRenewalService"></property>
	 </bean>
	 
	 <!-- 续收微天下保单查询接口 -->
	 <bean id="PA_IWTXQueryPolicyCodeUCC" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101003215.impl.WTXQueryPolicyCodeUCCImpl">
	      <property name="wtxQueryPolicyCodeService" ref="PA_IWTXQueryPolicyCodeService"></property>
	 </bean>
	 
	 <!-- 缴费信息变更接口变更 -投保人保单信息查询接口 -->
	 <bean id="PA_IQueryPolicyHolderContNoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101003201.impl.QueryPolicyHolderContNoUccImpl">
	      <property name="queryPolicyHolderContNoService" ref="PA_IQueryPolicyHolderContNoService"></property>
	 </bean>
	 
	 <!-- 柜面自助：贷款续贷详情查询接口   -->
	 <bean id="PA_QueryLoanContinuedLoanUCC" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003255.impl.QueryLoanContinuedLoanUCCImpl">
          <property name="queryLoanContinuedLoanService" ref="PA_QueryLoanContinuedLoanService"></property>	 
	 </bean>
	 
	 <!-- 查询个人收费信息 -->
	 <bean id="PA_QueryPerArapUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101003033.impl.QueryPerPremArapUccImpl">
	      <property name="queryPerPremArapService" ref="PA_QueryPerPremService"></property>
	 </bean>
	 <!-- 查询受益人变更-保单列表信息 -->
	 <bean id="PA_QueryBeneChangeUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003159.impl.QueryBeneChangePolicyUccImpl"/>
	 
	 <!-- 柜面自助：贷款清偿客户保单查询接口   -->
	 <bean id="IQueryPolicyInfoOfCustomerLoanAndPayOffUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003240.impl.QueryPolicyInfoOfCustomerLoanAndPayOffUccImpl">
          <property name="queryPolicyInfoOfCustomerLoanAndPayOffService" ref="PA_QueryPolicyInfoOfCustomerLoanAndPayOffService"></property>	 
	 </bean>
	 
	  <!-- 移动保全2.0 年金满期金查询接口 -->
	 <bean id="PA_CsendorseAGQueryUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401003288.impl.CsendorseAGQueryUccImpl">
		 <property name="prdIAS" ref="PA_prdIAS"/>
	     <property name="bonusAllocateDao" ref="PA_bonusallocateDao"></property>
	 </bean>
	 
	 <!-- 客户保单基本信息查询接口 -->
	 <bean id="PA_IQueryCusPlyBasicInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900056.impl.QueryCusPlyBasicInfoUccImpl">
	      <property name="queryCusPlyBasicInfoService" ref="PA_IQueryCusPlyBasicInfoService"></property>
	 </bean>
	<!--柜面自助查询保单原账号信息接口 -->
	<bean id="queryPolicyBankAccountInfos" class="com.nci.tunan.pa.impl.peripheral.ucc.r06801900084.impl.PolicyBankAccountInfoUccImpl">
	
	</bean>
	<!-- 贷款清偿-保单列表查询 -->
	<bean id="PA_queryPolicyListLoanRepaymentUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r00101900106.impl.QueryPolicyListLoanRepaymentUccImpl">
	      <property name="queryPolicyListLoanRepayMentService" ref="PA_queryPolicyListLoanRepaymentService"></property>
	</bean>

	
		<!-- 主险附加险查询UCC -->
	<bean id="PA_additionBusiInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900318.impl.AdditionBusiInfoUccImpl">
	      <property name="additionBusiInfoService" ref="PA_additionBusiInfoService"></property>
	</bean>
	
	<bean id="PA_hjbPolicyRelationQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900321.impl.HJBPolicyRelationQueryUccImpl">
	     <property name="HJBPolicyRelationQueryService" ref="PA_hjbPolicyRelationQueryService"></property>
	</bean>
	<!-- 个人渠道绩优人业务员信息查询 -->
	<bean id="PA_AgentInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900354.impl.QueryAgentInfoUccImpl">
	   <property name="queryAgentInfoService" ref="PA_queryAgentInfoService"></property>
	</bean>
	<!-- 实时孤儿单查询通知接口B -->
	<bean id="PA_queryAndUploadOrphanListUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.queryAndUploadOrphan.impl.QueryAndUploadOrphanListUccImpl">
	   <property name="queryAndUploadOrphanListService" ref="PA_queryAndUploadOrphanListService"></property>
	   <property name="orphanParameterDao" ref="PA_orphanParameterDao"></property>
	   <property name="orphanPolicyDao" ref="PA_orphanPolicyDao"></property>
	</bean>
    <!-- 保单贷款续贷列表查询:P00001900382 -->
	<bean id="PA_yunyiqueryPlolcyLoanInfo" class="com.nci.tunan.pa.impl.peripheral.ucc.r06801900399.impl.PlicyLoanRLUccImpl">
	
	</bean>

	<!-- 追加保费-保单列表查询接口 -->
	<bean id="PA_queryAdditionalpremiumUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900379.impl.QueryAdditionalpremiumUccImpl">
		<property name="queryAdditionalpremiumService" ref="PA_queryAdditionalpremiumServiceImpl"/>
	</bean> 

	<!-- 双录互保件查询接口 -->
	<bean id="PA_dualRecordingMutualGuaranteeUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900407.impl.DualRecordingMutualGuaranteeUccImpl">
		<property name="dualRecordingMutualGuaranteeService" ref="PA_dualRecordingMutualGuaranteeServiceImpl"/>
	</bean>
	
	 <!--中保信保单编码路由快查  -->
    <bean id="PA_PolicySequenceNoQueryUCCImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.judgepolicysequencenoexist.impl.PolicySequenceNoQueryUccImpl">
        <property name="queryService" ref="PA_PolicySequenceNoQueryServiceImpl"/>
    </bean>
    <!--银代业务客户资料采集优化-保单详情查询  -->
    <bean id="PA_QueryPolicyDetailsUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900454.impl.QueryPolicyDetailsUccImpl">
        <property name="queryPolicyDetailsService" ref="PA_QueryPolicyDetailsServiceImpl"/>
    </bean>
    <!--银保通客户资料采集需求（补充资料）-保单查询  -->
    <bean id="PA_WXPolicyQueryUccImpl" class="com.nci.tunan.pa.impl.querywxpolicyinfo.exports.iwxpolicyqueryucc.wxpolicyquery.ucc.impl.WXPolicyQueryUccImpl">
        <property name="wxPolicyQueryService" ref="PA_WXPolicyQueryServiceImpl"/>
    </bean>
    
    <!-- 第二年末现金价值查询接口UCC -->
	<bean id="PA_pasPrintCVQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900468.impl.PasPrintCVQueryUccImpl">
	      <property name="pasPrintCVQueryService" ref="PA_pasPrintCVQueryService"></property>
	</bean>
	
	<!-- 保单贷款保单查询接口UCC -->
	
	<bean id="PA_LNPolicyQueryYDBQUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900483.impl.LNPolicyQueryYDBQUccImpl">
	      <property name="LNPolicyQueryYDBQService" ref="PA_LNPolicyQueryYDBQService"></property>
	</bean>
	
	<!-- 保单续贷保单列表查询接口UCC -->
	<bean id="PA_RLPolicyQueryYDBQUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900472.impl.RLPolicyQueryYDBQUccImpl">
	      <property name="RLPolicyQueryYDBQService" ref="PA_RLPolicyQueryYDBQService"></property>
	</bean>
	
	<!-- 累积生息账户领取详情查询接口UCC -->
	<bean id="PA_QueryAIDeitailUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900554.impl.QueryAIDeitailUccImpl">
	      <property name="queryAIDeitailService" ref="PA_QueryAIDeitailService"></property>
	</bean>

	<!-- 核心险种信息查询接口UCC -->
	<bean id="PA_WXPolicyQueryHXXZUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900495.impl.WXPolicyQueryHXXZUccImpl">
	      <property name="WXPolicyQueryHXXZService" ref="PA_WXPolicyQueryHXXZService"></property>
	</bean>

	<!-- 年金满期金给付详情查询接口UCC -->
	<bean id="PA_QueryPolicyAGInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900545.impl.QueryPolicyAGInfoUccImpl">
	      <property name="QueryPolicyAGInfoService" ref="PA_QueryPolicyAGInfoService"></property>
	</bean>

	<!-- 万能险部分领取详情查询接口UCC -->
	<bean id="PA_QueryPGDeitailUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900564.impl.QueryPGDeitailUccImpl">
	      <property name="queryPGDeitailService" ref="PA_QueryPGDeitailService"></property>
	</bean>
	
	<!-- 身故受益人详情查询接口UCC -->
	<bean id="PA_DeceasedBeneficiaryQueryUCC" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900546.impl.DeceasedBeneficiaryQueryUCCImpl">
	      <property name="DeceasedBeneficiaryQueryService" ref="PA_DeceasedBeneficiaryQueryService"></property>
	</bean>

	<!-- ABS现价计算请求通知接口UCC -->
	<bean id="PA_ABSCashValNoticeUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06402900572.impl.ABSCashValNoticeUccImpl">
	      <property name="absCashValNoticeService" ref="PA_absCashValNoticeService"></property>
	</bean>

	<!-- 随信通续期缴费信息查询接口UCC -->
	<bean id="PA_QueryPCInfoForSXTUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900592.impl.QueryPCInfoForSXTUccImpl">
	      <property name="queryPCInfoForSXTService" ref="PA_queryPCInfoForSXTService"></property>
	</bean>

	<!-- 客户真实性校验接口UCC -->
	<bean id="PA_CheckCustomerUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900558.impl.CheckCustomerUccImpl">
	      <property name="checkCustomerService" ref="PA_checkCustomerService"></property>
	</bean>

	<!-- 满期不续保保单查询接口UCC -->
	<bean id="PA_NoRenewalExpirationQueryUCC" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900549.impl.NoRenewalExpirationQueryUCCImpl">
	      <property name="noRenewalExpirationQueryService" ref="PA_NoRenewalExpirationQueryService"></property>
	</bean>
	
	<!-- 退保保单查询接口UCC -->
	<bean id="PA_PolicyListQuerySurrenderUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900621.impl.PolicyListQuerySurrenderUccImpl">
	      <property name="policyListQuerySurrenderService" ref="PA_PolicyListQuerySurrenderService"></property>
	</bean>
	
	<!-- 退保保单查询接口UCC -->
	<bean id="PA_QueryCTPolicyForYdbq2Ucc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900620.impl.QueryCTPolicyForYdbq2UccImpl">
	      <property name="queryCTPolicyForYdbq2Service" ref="PA_QueryCTPolicyForYdbq2Service"></property>
	</bean>
	
	<!-- 累积生息账户领取-保单列表查询接口 -->
	<bean id="PA_QueryAIPolicyUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900560.impl.QueryAIPolicyUccImpl">
	      <property name="queryAIPolicyService" ref="PA_QueryAIPolicyService"></property>
	</bean>

	<!-- 保单列表查询接口-万能险账户领取 -->
	<bean id="PA_QueryPGPolicyForYdbq2Ucc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900627.impl.QueryPGPolicyForYdbq2UccImpl">
	      <property name="queryPGPolicyForYdbq2Service" ref="PA_QueryPGPolicyForYdbq2Service"></property>
	</bean>

	<!-- 万能投连账户信息查询接口 -->
	<bean id="PA_QueryPGPolicyForGwUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900653.impl.QueryPGPolicyForGwUccImpl">
	      <property name="queryPGPolicyForGwService" ref="PA_QueryPGPolicyForGwService"></property>
	</bean>

	<!-- 投连万能账户信息详情查询接口 -->
	<bean id="PA_QueryPGPolicyDeitailForGwUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900654.impl.QueryPGPolicyDeitailForGwUccImpl">
	      <property name="queryPGPolicyDeitailForGwService" ref="PA_QueryPGPolicyDeitailForGwService"></property>
	</bean>
	
	<!-- 续期缴费变更同步列表查询接口 -->
    <bean id="renewalFeeInfoQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900540.impl.RenewalFeeInfoQueryUccImpl" >
    	<property name="renewalFeeInfoQueryService" ref="PA_RenewalFeeInfoQueryService"></property>
    </bean>
    
    <!--退保保全记录查询接口  -->
    <bean id="PA_PolicyCancellationKeepIntactRecordQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06801900672.impl.PolicyCancellationKeepIntactRecordQueryUccImpl">
        <!-- <property name="policyCancellationKeepIntactQueryService" ref="PA_PolicyCancellationKeepIntactQueryService"/> -->
    </bean>
    
    <!--追加保费保全记录查询接口  -->
    <bean id="PA_AddToPremiumPreserveQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06801900673.impl.AddToPremiumPreserveQueryUccImpl">
        <!-- <property name="addToPremiumQueryService" ref="PA_AddToPremiumQueryService"/> -->
    </bean>
    
    <!-- 智慧柜员机_北京地区人身险销售人员自保件和互保件管理接口 -->
    <bean id = "PA_QrySelfInsFlagAndMutualInsFlagUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06801900662.impl.QrySelfInsFlagAndMutualInsFlagUccImpl">
         <property name="qrySelfInsFlagAndMutualInsFlagService" ref="PA_QrySelfInsFlagAndMutualInsFlagService"></property>
    </bean>
    <!-- 万能险账户抵扣续期保费轨迹接口 -->
    <bean id="queryUIAccountDeductionPremiumLocusUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900668.impl.QueryUIAccountDeductionPremiumLocusUccImpl" >
    	<property name="queryUIAccountDeductionPremiumLocusService" ref="PA_QueryUIAccountDeductionPremiumLocusService"></property>
    </bean>
    <!-- 加保保单列表查询接口  -->
    <bean id="PA_queryPolicyListIncreaseOfPremiumUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900707.impl.QueryPolicyListIncreaseOfPremiumUccImpl" >
    	<property name="queryPolicyListIncreaseOfPremiumService" ref="PA_QueryPolicyListIncreaseOfPremiumService"></property>
    </bean>
    <!-- 特殊人员类型校验接口  -->
    <bean id="PA_SpecialPerTypeCheckUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900718.impl.SpecialPerTypeCheckUccImpl" >
    	<property name="specialPerTypeCheckService" ref="PA_SpecialPerTypeCheckService"></property>
    </bean>
    <bean id="PA_PersonalinsurancePolicyUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900708.impl.PersonalinsurancePolicyUccImpl" >
    	<!-- <property name="personalinsurancePolicyService" ref="PA_PersonalinsurancePolicyService"></property> -->
    </bean>
    
    <bean id="PA_IssueValidPolicyQueryUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900715.impl.IssueValidPolicyQueryUccImpl" >
    </bean>
    
    <!-- 根据客户id查询保单层，险种层信息接口 -->
    <bean id="IQryPolicyAndContractUCC" class="com.nci.tunan.pa.impl.qrypolicyandcontract.ucc.impl.QryPolicyAndContractUCCImpl" >
    	<property name="qryPolicyAndContractService" ref="PA_IQryPolicyAndContractService"></property>
    </bean>
    
    <!-- 根据客户ID 查询客户的九要素 -->
    <bean id="PA_QueryCustomerInformationByNineElementUccImpl" class="com.nci.tunan.pa.impl.customerinformation.ucc.impl.QueryCustomerInformationByNineElementUccImpl"></bean>
    
     <!-- 客户五要素查询保单服务人员信息 -->
    <bean id = "PA_QueryPolicyServicePersonInfoUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900761.impl.QueryPolicyServicePersonInfoUccImpl">
    
    <property name="queryPolicyServicePersonInfoService" ref="PA_QueryPolicyServicePersonInfoService"></property>
    </bean>
    
    
    <!-- 根据客户五要素查询是否为业务员 -->
    <bean id = "PA_queryisSalespersonFlagUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900757.impl.QueryisSalespersonFlagUccImpl">
    <property name="queryisSalespersonFlagService" ref="PA_queryisSalespersonFlagService"></property>
    </bean>
    
    <!-- 未成年人死亡风险保额累计查询接口 -->
    <bean id = "PA_minorsRiskOfDeathCoverageQueryUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900747.impl.MinorsRiskOfDeathCoverageQueryUccImpl">
    <property name="minorsRiskOfDeathCoverageQueryService" ref="PA_minorsRiskOfDeathCoverageQueryService"></property>
    </bean>
    
      <!-- 累积生息账户信息查询接口 -->
    <bean id = "PA_cumuInteAccInfoQueryUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900790.impl.CumuInteAccInfoQueryUccImpl">
    </bean>
    
    <!-- 通知书列表查询接口 -->
    <bean id = "PA_documentListQueryUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900789.impl.DocumentListQueryUccImpl">
    </bean>
    <!-- 分红信息保单列表查询接口 -->
    <bean id = "PA_queryBonusInfoListUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900826.impl.QueryBonusInfoListUccImpl">
    </bean>
    <bean id = "PA_queryAccumulateCapitalListUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900827.impl.QueryAccumulateCapitalListUccImpl">
    </bean>
    <!-- 代理人信息查询接口 -->
    <bean id = "PA_queryContractAgentInfoUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900832.impl.QueryContractAgentInfoUccImpl">
    </bean>

	 <!-- 通知书报告书查看下载信息回传接口 -->
	 <bean id="PA_NoticeReportInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06402900835.impl.INoticeReportInfoUccImpl">
	      <property name="iNoticeReportViewAndDownloadService" ref="PA_INoticeReportViewAndDownloadService"></property>
	 </bean>
	 
	  <!-- 通知书报告书保单列表查询接口 -->
    <bean id="PA_queryNotificationReportPolicyListUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900834.impl.QueryNotificationReportPolicyListUccImpl"></bean>
	 
	 
	<!-- 通知书信息查询接口 -->
    <bean id = "PA_queryDocumentInfoUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900848.impl.QueryDocumentInfoUccImpl">
    </bean>
    
        
     <!-- 保单详细信息查询接口 -->
    <bean id = "PA_queryPolicyDetailsInfoUCC" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900830.impl.QueryPolicyDetailsInfoUCCImpl">
    </bean>
    
	 <!-- 保单原交费账户信息查询接口 -->
	 <bean id="PA_queryPolicyPrimaryAccountUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900813.impl.QueryPolicyPrimaryAccountUccImpl">
	 </bean>
    
	<!-- 银保信账户查询接口 -->
	<bean id="PA_queryBankInsurAccountInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900874.impl.QueryBankInsurAccountInfoUccImpl">
	</bean>
	
	<bean id="PA_accountBindingConfirmationUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06402900875.impl.AccountBindingConfirmationUccImpl">
		<property name="accountBindingConfirmationService" ref="PA_accountBindingConfirmationService"></property>
	</bean>
	<!-- 根据客户信息查询保单信息接口 -->
    <bean id = "PA_queryPolicyInfoByCustomInfoUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900870.impl.QueryPolicyInfoByCustomInfoUccImpl">
    </bean>
    
    <!-- 贷款账户查询接口技术需求任务 #144538 掌上新华二期-贷款信息查询功能（需求15）-个险新核心-保单 -->
    <bean id = "PA_queryLoanAccountInfoUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900887.impl.QueryLoanAccountInfoUccImpl">
    </bean>
    
    <!-- 客户信息查询保单号列表接口 -->
	<bean id="PA_customerInformationQueryPolicyCodeUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900924.impl.CustomerInformationQueryPolicyCodeUccImpl">
	</bean>
	
	 <!-- 续领年金生存认证提交接口 -->
	<bean id="PA_renewalOfPensionSurvivalSubUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06402900922.impl.RenewalOfPensionSurvivalSubUccImpl">
	</bean>
	
	<!-- 个险被保险人信息查询接口 -->
	<bean id="PA_queryInsuredlistByCusnameAndCusNoAndAgentCodeUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900928.impl.QueryInsuredlistByCusnameAndCusNoAndAgentCodeUccImpl">
	</bean>
	
    <!-- 保费信息查询接口 -->
	<bean id="PA_premiumInformationInquireUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900882.impl.PremiumInformationInquireUccImpl">
	</bean>
	
	<!-- 续领年金生存认证查询接口 -->
	<bean id="PA_renewalOfAnnuitySurvivalCertificationUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900920.impl.RenewalOfAnnuitySurvivalCertificationUccImpl">
	</bean>
	
			<!-- 睡眠保单查询接口 -->
	<bean id="PA_querySleepPolicyInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900934.impl.QuerySleepPolicyInfoUccImpl">
		<property name="querySleepPolicyInfoService" ref="PA_querySleepPolicyInfoService"></property>
	</bean>
			<!-- 保单年金满期金信息查询接口 -->
	<bean id="PA_queryAnnuAndExpireInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900942.impl.QueryAnnuAndExpireInfoUccImpl">
		<property name="queryAnnuAndExpireInfoService" ref="PA_queryAnnuAndExpireInfoService"></property>
	</bean>
	<!-- 睡眠保单通知情况更新接口 -->
	<bean id="PA_updateSleepPolicyInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06402900936.impl.UpdateSleepPolicyInfoUccImpl">
		<property name="updateSleepPolicyInfoService" ref="PA_updateSleepPolicyInfoService"></property>
	</bean>
	
	<!-- 保单列表信息分页查询接口 -->
	<bean id="PA_paginatePolicyListInformationUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900932.impl.PaginatePolicyListInformationUccImpl">
	</bean>
	
	<!-- 投保人保单列表查询接口  -->
	<bean id="PA_queryPolicyHolderChangeInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900974.impl.QueryPolicyHolderChangeInfoUccImpl">
		<property name="queryPolicyHolderChangeInfoService" ref="PA_queryPolicyHolderChangeInfoService"></property>
	</bean>
	
    <!-- 客户三要素查询保单信息列表接口 -->
	<bean id="PA_queryPolicyInfosByThreeElementsOfCustomerUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900976.impl.QueryPolicyInfosByThreeElementsOfCustomerUccImpl">
	</bean>
	
	<!-- 客户号查询保单号列表接口 -->
	<bean id="PA_queryPolicyInfoListUccImpl" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401901046.impl.QueryPolicyInfoListUccImpl">
	</bean>
	
	<!-- 减保保单列表查询接口 -->
	<bean id="PA_queryReductionPolicyInformationListUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900960.impl.QueryReductionPolicyInformationListUccImpl">
	</bean>
	
	<!-- 保单补发保单列表查询接口 -->
	<bean id="PA_queryPolicyReplacementInformationUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401901074.impl.QueryPolicyReplacementInformationUccImpl">
	</bean>
	
	<!-- 保单关联保单列表查询接口 -->
	<bean id="PA_queryPolicyRelatedPolicyListUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401901085.impl.QueryPolicyRelatedPolicyListUccImpl">
	</bean>
	
	<!-- 领取形式变更-保单列表查询接口 -->
	<bean id="PA_queryPolicyInfosForChangeOfClaimFormUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900978.impl.QueryPolicyInfosForChangeOfClaimFormUccImpl">
	</bean>
	
    <!-- 查询客户名下保单号接口  -->
	<bean id="PA_queryCustomerPolicycodeUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900980.impl.QueryCustomerPolicycodeUccImpl">
	</bean>
	
	<!-- 职业类别变更-保单列表查询接口  -->
	<bean id="PA_queryJobUwLevelChangesPolicyListUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401900982.impl.QueryJobUwLevelChangesPolicyListUccImpl">
	</bean>
	
	<!-- 保单号集合查询保单信息接口 -->
    <bean id = "PA_queryPolicyInfoByPolicyListUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401900986.impl.QueryPolicyInfoByPolicyListUccImpl">
    </bean>
    
     <!-- 查询保全受理号下客户信息（空中柜面） -->
	 <bean id="PA_queryCustomerInfoByAcceptCodeUcc" class=" com.nci.tunan.pa.impl.peripheral.ucc.r06801901056.impl.QueryCustomerInfoUccImpl">
	      <property name="queryCustomerInfoService" ref="PA_QueryCustomerInfoService"></property>
	 </bean>
	 
	 	<!-- 投保人保单列表查询接口  -->
	<bean id="PA_queryBonuspayPolicyListInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401901075.impl.QueryBonuspayPolicyListInfoUccImpl">
		<property name="queryBonuspayPolicyListInfoService" ref="PA_queryBonuspayPolicyListInfoService"></property>
	</bean>
	   <!-- 技术需求任务 #190097 新核心-接口需求-移动保全2.0-新增红利领取领取形式变更功能需求-PAS-2/3  -->
	<bean id="PA_queryBonusClaimFromChangeInfoUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401901105.impl.QueryBonusClaimFromChangeInfoUccImpl">
		<property name="queryBonusClaimFromChangeInfoService" ref="PA_queryBonusClaimFromChangeInfoService"></property>
	</bean>
	
	<!-- TR-20250704-015214 掌上新华攻坚组问题单-PRM-20250703-0006_自助理赔申请时，出险人信息查询缓慢-个险新核心 -->
 	<bean id="PA_queryPolListsByCmFiveFactorUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401901141.impl.QueryPolListsByCmFiveFactorUccImpl">
		<property name="queryPolListsByCmFiveFactorService" ref="PA_queryPolListsByCmFiveFactorService"></property>
	</bean>
	
	
	 <!-- 客户保单手机号查询接口 -->
    <bean id = "PA_queryPolicyLastMobileTelUcc" class = "com.nci.tunan.pa.impl.peripheral.ucc.r06401901069.impl.QueryPolicyLastMobileTelUccImpl">
    </bean>
    
    <!-- 客户号查询其保单下的客户信息接口 -->
	<bean id="PA_queryAllCustomerByNewCusidUcc" class="com.nci.tunan.pa.impl.peripheral.ucc.r06401901097.impl.QueryAllCustomerByNewCusidUccImpl">
	</bean>
</beans>