<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">



	<bean class="com.nci.tunan.cs.dao.impl.CalDebtPremDaoImpl" id="PA_calDebtPremDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.FundTransDaoImpl" id="PA_iFundTransDao" parent="baseDao"/>
		
	<bean class="com.nci.tunan.pa.dao.impl.LogScopeDaoImpl" id="PA_logScopeDao" parent="baseDao"/>
		
	<bean class="com.nci.tunan.pa.dao.impl.RenewCollectionUndoDaoImpl" id="PA_renewCollectionUndoDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyLogDaoImpl" id="PA_policyLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractProductCxDaoImpl" id="PA_contractProductCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractBusiProdCxDaoImpl" id="PA_contractBusiProdCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyHolderCxDaoImpl" id="PA_policyHolderCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.InsuredListCxDaoImpl" id="PA_insuredListCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractBeneCxDaoImpl" id="PA_contractBeneCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.BenefitInsuredCxDaoImpl" id="PA_benefitInsuredCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayerCxDaoImpl" id="PA_payerCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ExtraPremCxDaoImpl" id="PA_extraPremCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayerAccountCxDaoImpl" id="PA_payerAccountCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.common.dao.impl.FindCodeValueDaoImpl" id="PA_findCodeValueDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.DocumentDaoImpl" id="PA_paDocumentDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ClobDaoImpl" id="PA_paClobDao" parent="baseDao"/>
	<bean class="com.nci.tunan.cs.dao.impl.CsPolicyChangeDaoImpl" id="PA_csPolicyChangeDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.common.dao.impl.CalculateCashValueDaoImpl" id="PA_paCalculateCashValueDao" parent="baseDao"/>
	<bean class="com.nci.tunan.cs.dao.impl.CsAcceptChangeDaoImpl" id="PA_csAcceptChangeDao" parent="baseDao"/>
	<bean class="com.nci.tunan.cs.dao.impl.CsDocumentRegenerationDaoImpl" id="PA_CsDocumentRegenerationDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.InvestUnitPriceDaoImpl" id="PA_investUnitPriceDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.InvestUnitPriceLogDaoImpl" id="PA_investUnitPriceLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.InvestUnitPriceCxDaoImpl" id="PA_investUnitPriceCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.common.dao.impl.FormulaDaoImpl" id="PA_formulaDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.BenefitInsuredDaoImpl" id="PA_benefitInsuredDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractBeneDaoImpl" id="PA_contractBeneDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractBusiProdDaoImpl" id="PA_contractBusiProdDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractExtendDaoImpl" id="PA_contractExtendDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractMasterDaoImpl" id="PA_contractMasterDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractMasterCxDaoImpl" id="PA_contractMasterCxDao" parent="baseDao"/>
	<!-- RM:168477 老年人线上贷款事后回访结果轨迹表DAO -->
    <bean class="com.nci.tunan.pa.dao.impl.OldpeopleLoanFollowupLogDaoImpl" id="PA_OldpeopleLoanFollowupLogDao" parent="baseDao"/>
	<!-- 责任组责任状态变更表 -->
	<bean class="com.nci.tunan.pa.dao.impl.ProductLiabilityChangeDaoImpl" id="PA_productLiabilityChangeDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractProductDaoImpl" id="PA_contractProductDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ExtraPremDaoImpl" id="PA_extraPremDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.InsuredListDaoImpl" id="PA_insuredListDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ShortPremArapDaoImpl" id="PA_shortPremArapDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayerAccountDaoImpl" id="PA_payerAccountDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayerDaoImpl" id="PA_payerDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PersistenceBonusDaoImpl" id="PA_persistenceBonusDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountDaoImpl" id="PA_policyAccountDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyHolderDaoImpl" id="PA_policyHolderDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.SecondPolicyHolderDaoImpl" id="PA_secondPolicyHolderDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PremArapDaoImpl" id="PA_premArapDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyChangeDaoImpl" id="PA_policyChangeDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractMasterLogDaoImpl" id="PA_contractMasterLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractExtendCxDaoImpl" id="PA_contractExtendCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractExtendLogDaoImpl" id="PA_contractExtendLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayerLogDaoImpl" id="PA_payerLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayerAccountLogDaoImpl" id="PA_payerAccountLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.RiskAmountDaoImpl" id="PA_riskAmountDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.RiskAmountLogDaoImpl" id="PA_riskAmountLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.RiskAmountCxDaoImpl" id="PA_riskAmountCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.CashDaoImpl" id="PA_cashDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractLiabAmoutDaoImpl" id="PA_contractLiabAmoutDao" parent="baseDao"/>
	<!--<bean id="ilpRegularPremDao" class="com.nci.tunan.pa.dao.impl.IlpRegularPremDaoImpl"
		parent="baseDao" />-->
	<bean class="com.nci.tunan.pa.dao.impl.IlpRegularPremCxDaoImpl" id="PA_ilpRegularPremCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.IlpRegularPremLogDaoImpl" id="PA_ilpRegularPremLogDao" parent="baseDao"/>
	<!--续期收费/实时抽档 start -->
	<bean class="com.nci.tunan.pa.dao.impl.RenewExtraDaoImpl" id="PA_renewExtraDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.policyDueDateDaoImpl" id="PA_policyDueDateDao" parent="baseDao"/>
	<!--续期收费/实时抽档 end -->
	<bean class="com.nci.tunan.pa.dao.impl.PayPlanDaoImpl" id="PA_payPlanDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayPlanLogDaoImpl" id="PA_payPlanLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayPlanCxDaoImpl" id="PA_payPlanCxDao" parent="baseDao"/>
	<!-- 续期冲正 start niuyu_wb 2014/12/09 -->
	<bean class="com.nci.tunan.pa.dao.impl.RenewCollectionOffsetCompDaoImpl" id="PA_renewCollectionOffsetCompDao" parent="baseDao">
	</bean>
	<!-- 启用新续保流程撤销批处理Dao -->
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.dao.impl.RenewalHisProcessRevokeDaoImpl" id="PA_renewalHisProcessRevokeDao" parent="baseDao">
	</bean>
	<!-- 续期冲正 end -->
	<!-- add by liangpl start -->
	<bean class="com.nci.tunan.pa.dao.impl.CustomerDaoImpl" id="PA_customerDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractAgentDaoImpl" id="PA_contractAgentDao" parent="baseDao"/>
	<!-- add by liangpl end -->

	<bean class="com.nci.tunan.pa.dao.impl.PolicyAcknowledgementDaoImpl" id="PA_policyAcknowledgementDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractInvestDaoImpl" id="PA_contractInvestDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractInvestLogDaoImpl" id="PA_contractInvestLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractInvestCxDaoImpl" id="PA_contractInvestCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyFundChargeDaoImpl" id="PA_policyFundChargeDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyFundChargeLogDaoImpl" id="PA_policyFundChargeLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyFundChargeCxDaoImpl" id="PA_policyFundChargeCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayPlanPayeeDaoImpl" id="PA_payPlanPayeeDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayPlanPayeeLogDaoImpl" id="PA_payPlanPayeeLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayPlanPayeeCxDaoImpl" id="PA_payPlanPayeeCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ServiceDaoImpl" id="PA_serviceDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.CustomerSurveyDaoImpl" id="PA_customerSurveyDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.SurveyQuestionDaoImpl" id="PA_surveyQuestionDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.SurveyQuestionOptionDaoImpl" id="PA_surveyQuestionOptionDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.CustomerSurveyDetailDaoImpl" id="PA_customerSurveyDetailDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.SurveyTemplateDaoImpl" id="PA_surveyTemplateDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractInvestRateDaoImpl" id="PA_contractInvestRateDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractInvestRateLogDaoImpl" id="PA_contractInvestRateLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ContractInvestRateCxDaoImpl" id="PA_contractInvestRateCxDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.FundTransDaoImpl" id="PA_fundTransDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.AddressDaoImpl" id="PA_addressDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.BankAccountDaoImpl" id="PA_bankAccountDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.CustomerDetailInfoDaoImpl" id="PA_customerDetailInfoDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.BankDaoImpl" id="PA_bankDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.ContractInvestStreamDaoImpl" id="PA_contractInvestStreamDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyConditionDaoImpl" id="PA_policyConditionDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyConditionLogDaoImpl" id="PA_policyConditionLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyConditionCxDaoImpl" id="PA_policyConditionCxDao" parent="baseDao"/>	
	<bean class="com.nci.tunan.pa.dao.impl.CommonQueryDaoImpl" id="PA_commonQueryDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyOperationDaoImpl" id="PA_policyOperationDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyStatusDaoImpl" id="PA_policyStatusDao" parent="baseDao"/>
<!--	<bean id="addInvestDao" class="com.nci.tunan.pa.dao.impl.AddInvestDaoImpl"
		parent="baseDao" />-->	
	<bean class="com.nci.tunan.pa.dao.impl.ExternalSystemInfoDaoImpl" id="PA_externalSystemInfoDao" parent="baseDao"/>
<!--  <bean id="capitalDistributeDao" class="com.nci.tunan.pa.dao.impl.CapitalDistributeDaoImpl"
		parent="baseDao" />-->	
	<bean class="com.nci.tunan.pa.dao.impl.BusinessProductDaoImpl" id="PA_businessProductDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.IlpRecurringTopupDaoImpl" id="PA_iIlpRecurringTopupDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.FundTransApplyDaoImpl" id="PA_fundTransApplyDao" parent="baseDao"/>
	<bean class="com.nci.tunan.cs.dao.impl.DistrictDaoImpl" id="PA_districtDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayDueDaoImpl" id="PA_payDueDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PayDueListDaoImpl" id="PA_payDueListDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.InterestMarginAllocateDaoImpl" id="PA_interestMarginAllocateDao" parent="baseDao"/>
	<!-- sunliduan 保单账户交易详细记录表 -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountTransListDaoImpl" id="PA_policyAccountTransListDao" parent="baseDao"/>
	<!-- sunliduan 保单账户基本信息表 -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountStreamDaoImpl" id="PA_policyAccountStreamDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountStreamLogDaoImpl" id="PA_policyAccountStreamLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountStreamCxDaoImpl" id="PA_policyAccountStreamCxDao" parent="baseDao"/>
	<!-- 分红 -->
	<bean class="com.nci.tunan.pa.dao.impl.BonusAllocateDaoImpl" id="PA_bonusallocateDao" parent="baseDao">
	</bean>

	<bean class="com.nci.tunan.pa.dao.impl.RenewReversalApplyDaoImpl" id="PA_renewReversalApplyDao" parent="baseDao"/>

	<!-- 账户资产录入 -->
	<bean class="com.nci.tunan.pa.dao.impl.InvestAccountInfoDaoImpl" id="PA_investAccountInfoDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.FundAssetsDaoImpl" id="PA_fundAssetsDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.FundAssetsLogDaoImpl" id="PA_fundAssetsLogDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.FundAssetsCxDaoImpl" id="PA_fundAssetsCxDao" parent="baseDao">
	</bean>
	<!-- 账户资产录入 -->
	<!-- 更新保单号 -->
	<bean class="com.nci.tunan.pa.dao.impl.UpdatePolicyCodeDaoImpl" id="PA_updatePolicyCodeDao" parent="baseDao"/>
	<!-- 更新保单信息 -->
	<bean class="com.nci.tunan.pa.dao.impl.UpdatePolicyInfoDaoImpl" id="PA_updatePolicyInfoDao" parent="baseDao"/>


	<!-- 设置邮件 -->
	<bean class="com.nci.tunan.pa.dao.impl.FundPriceNoticeMailDaoImpl" id="PA_fundPriceNoticeMailDao" parent="baseDao"/>

	<!-- 续保抽档记录表 -->
	<bean class="com.nci.tunan.pa.dao.impl.RenewalBillingDaoImpl" id="PA_renewalBillingDao" parent="baseDao"/>
	<!-- 下个续保期间切换产品配置表 -->
	<bean class="com.nci.tunan.pa.dao.impl.EnNextDateCfgDaoImpl" id="PA_enNextDateCfgDao" parent="baseDao"/>
	<!-- 启用新续保流程配置表 -->
	<bean class="com.nci.tunan.pa.dao.impl.EnStartDateCfgDaoImpl" id="PA_enStartDateCfgDao" parent="baseDao"/>
	<!-- 新续保流程保费表 -->
	<bean class="com.nci.tunan.pa.dao.impl.PremNewRenewalDaoImpl" id="PA_premNewRenewalDao" parent="baseDao"/>
	<!-- 保单费用记录表 -->
	<bean class="com.nci.tunan.pa.dao.impl.PremDaoImpl" id="PA_premDao" parent="baseDao"/>
	<!-- 险种log表 -->
	<bean class="com.nci.tunan.pa.dao.impl.ContractBusiProdLogDaoImpl" id="PA_contractBusiProdLogDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.ExtraPremLogDaoImpl" id="PA_extraPremLogDao" parent="baseDao"/>
	<!-- 保单被保人log表 -->
	<bean class="com.nci.tunan.pa.dao.impl.InsuredListLogDaoImpl" id="PA_insuredListLogDao" parent="baseDao"/>
		
	<bean class="com.nci.tunan.pa.dao.impl.ContractBeneLogDaoImpl" id="PA_contractBeneLogDao" parent="baseDao"/>
		
	<bean class="com.nci.tunan.pa.dao.impl.BenefitInsuredLogDaoImpl" id="PA_benefitInsuredLogDao" parent="baseDao"/>
	<!-- 责任组log -->
	<bean class="com.nci.tunan.pa.dao.impl.ContractProductLogDaoImpl" id="PA_contractProductLogDao" parent="baseDao"/>
		
	<bean class="com.nci.tunan.pa.dao.impl.PolicyHolderLogDaoImpl" id="PA_policyHolderLogDao" parent="baseDao"/>
	<!-- 风险保额归档数据归档表 -->
	<bean class="com.nci.tunan.pa.dao.impl.RiskAmountArchiveDaoImpl" id="PA_riskAmountArchiveDao" parent="baseDao"/>
	<!-- 风险类型码表 -->
	<bean class="com.nci.tunan.pa.dao.impl.AggregationRiskTypeDaoImpl" id="PA_aggregationRiskTypeDao" parent="baseDao"/>


	<!-- 投资账户单位数日终汇总信息表 -->
	<bean class="com.nci.tunan.pa.dao.impl.InvestUnitsDaysumDaoImpl" id="PA_investUnitsDaysumDao" parent="baseDao"/>
	
	<!-- 投资账户信息表 -->
	<bean class="com.nci.tunan.pa.dao.impl.FundDaoImpl" id="PA_fundDaoImpl" parent="baseDao"/>

	<!-- yuzw预约险种信息 -->
	<bean class="com.nci.tunan.pa.dao.impl.PrecontProductDaoImpl" id="PA_precontProductDao" parent="baseDao">
	</bean>

	<!-- yuzw保障计划信息 -->
	<bean class="com.nci.tunan.pa.dao.impl.ContractProductOtherDaoImpl" id="PA_contractProductOtherDao" parent="baseDao">
	</bean>

	<bean class="com.nci.tunan.pa.dao.impl.AssetsDepositConfigDaoImpl" id="PA_assetsDepositConfigDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.FundAssetsDetailDaoImpl" id="PA_fundAssetsDetailDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.FundSettlementDaoImpl" id="PA_fundSettlementDao" parent="baseDao"/>
	<!-- liuch保单关联 -->
	<bean class="com.nci.tunan.cs.dao.impl.CsContractRelationDaoImpl" id="CS_ContractRelationDao" parent="baseDao"/>
	<!-- 客户告知相关 -->
	<bean class="com.nci.tunan.pa.dao.impl.QuestionaireCustomerDaoImpl" id="PA_questionaireCustomerDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.QuestionaireCustomerParamDaoImpl" id="PA_questionaireCustomerParamDao" parent="baseDao">
	</bean>

	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountStreamRateDaoImpl" id="PA_policyAccountStreamRateDao" parent="baseDao">
	</bean>

	<!-- 客户保单简要查询接口 -->
	<bean class="com.nci.tunan.pa.dao.impl.CustomerPolicyGeneralDaoImpl" id="PA_customerPolicyGeneralDao" parent="baseDao">

	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.CommonTaskDaoImpl" id="PA_paCommonTaskDao" parent="baseDao"/>
	<!-- 人员新增 -->
	<bean class="com.nci.core.common.dao.impl.AgentDaoImpl" id="PA_agentDao" parent="baseDao"/>
	<bean class="com.nci.core.common.dao.impl.SalesOrganDaoImpl" id="PA_salesOrganDao" parent="baseDao"/>
	<!-- 网点服务人员变化 -->
	<bean class="com.nci.core.common.dao.impl.BankBranchHandlerDaoImpl" id="PA_bankBranchHandlerDao" parent="baseDao"/>
	<bean class="com.nci.core.common.dao.impl.BankBranchAgentDaoImpl" id="PA_bankBranchAgentDao" parent="baseDao"/>	
	<!-- 险种转换 -->
	<bean class="com.nci.tunan.pa.dao.impl.RenewChangeDaoImpl" id="PA_renewChangeDao" parent="baseDao"/>
	<!-- 追加保费查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.AppendPremListDaoImpl" id="PA_appendPremListDao" parent="baseDao">
	</bean>
	<!-- 保单账户基本信息变更履历表 -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountLogDaoImpl" id="PA_policyAccountLogDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.PolicyAccountCxDaoImpl" id="PA_policyAccountCxDao" parent="baseDao">
	</bean>
	<!-- 银行网点表 -->
	<bean class="com.nci.core.common.dao.impl.BankBranchDaoImpl" id="PA_bankBranchDao" parent="baseDao">
	</bean>
	<!-- 代理人产品授权表 -->
	<bean class="com.nci.core.common.dao.impl.AgentProductDaoImpl" id="PA_agentProductDao" parent="baseDao">
	</bean>
	<!-- 代理人资格证表 -->
	<bean class="com.nci.core.common.dao.impl.AgentLicenseDaoImpl" id="PA_agentLicenseDao" parent="baseDao">
	</bean>
	<!-- 保额变更表 -->
	<bean class="com.nci.tunan.pa.dao.impl.SaChangeDaoImpl" id="PA_saChangeDao" parent="baseDao">
	</bean>
	<!-- 保单被保人查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.QueryPolicyInsuredDaoImpl" id="PA_queryPolicyInsuredDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.QueryInjuredDaoImpl" id="PA_queryInjuredDao" parent="baseDao">
	</bean>
	<!--保额保费累计查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.CountFeeDaoImpl" id="PA_countFeeDao" parent="baseDao">
	</bean>
<!--查询客户是否存在生效且过犹豫期的保单 -->
	<bean class="com.nci.tunan.pa.dao.impl.IsHesitationPeriodDaoImpl" id="PA_isHesitationPeriodDao" parent="baseDao">
	</bean>
	<!-- 人员解约 -->
	<bean class="com.nci.tunan.pa.dao.impl.AgentDaoImpl" id="PA_agentPADao" parent="baseDao"/>

	<!-- add by sunjl_wb 查询保单历史上载记录 -->
	<bean class="com.nci.tunan.pa.dao.impl.QueryPolicyUploadHistoryDaoImpl" id="PA_queryPolicyUploadHistoryDao" parent="baseDao">
	</bean>
	<!-- add by zhangjy_wb 维护前置调查计划 -->
	<bean class="com.nci.tunan.pa.dao.impl.ClaimBfSurveyPlanDaoImpl" id="PA_claimBfSurveyPlanDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.ClaimBfSurveyOrgDaoImpl" id="PA_claimBfSurveyOrgDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.ClaimBfSurveyChannelDaoImpl" id="PA_claimBfSurveyChannelDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.ClaimBfSurveyRamntDaoImpl" id="PA_claimBfSurveyRamntDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.ClaimSurveyTaskDaoImpl" id="PA_claimSurveyTaskDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.ClaimSurveyBatchDaoImpl" id="PA_claimSurveyBatchDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.SurveyApplyDaoImpl" id="PA_claimSurveyApplyDao" parent="baseDao">
	</bean>

	<!-- 日终汇总对账 -->
	<bean class="com.nci.tunan.pa.dao.impl.DayEndReconciliationDaoImpl" id="PA_dayEndReconciliationDao" parent="baseDao">
	</bean>

	<!-- add by gaojun_wb 维护前置调查计划 -->
	<bean class="com.nci.tunan.pa.dao.impl.YesNoDaoImpl" id="PA_yesNoDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.dao.impl.SalesChannelDaoImpl" id="PA_salesChannelDao" parent="baseDao"/>

	<bean class="com.nci.tunan.pa.dao.impl.PolicyBusiDutyDaoImpl" id="PA_policyBusiDutyDao" parent="baseDao"/>

	<!--保单基础信息查询查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.BusinessCentreDaoImpl" id="PA_businessCentreDao" parent="baseDao"/>
	<!--续期缴费账号信息查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.RenewalPaymentIdDaoImpl" id="PA_renewalPaymentIdDao" parent="baseDao"/>

	<!-- add by lvkai 保单账户校验 -->
	<bean class="com.nci.tunan.pa.dao.impl.AccountCheckDaoImpl" id="PA_accountCheckDao" parent="baseDao">
	</bean>

	<!-- add by lvkai 日终明细对账 -->
	<bean class="com.nci.tunan.pa.dao.impl.DayDetailCheckDaoImpl" id="PA_dayDetailCheckDao" parent="baseDao">
	</bean>
	
	<bean class="com.nci.core.common.dao.impl.CustomerDaoImpl" id="PA_commonCustomerDao" parent="baseDao"/>
	<!--日常交易查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.DailyTradingQueryDaoImpl" id="PA_dailyTradingQueryDao" parent="baseDao">
	</bean>
    <!--出险日后投连、万能险领取查询接口 -->
	<bean class="com.nci.tunan.pa.dao.impl.OperateQueryDaoImpl" id="PA_operateQueryDao" parent="baseDao">
	</bean>

	<!--健康告知查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.QuestionaireInfoDaoImpl" id="PA_paquestionaireInfoDao" parent="baseDao"/>

    <!--保单状态同步 -->
    
    <bean class="com.nci.core.common.dao.impl.NoteDaoImpl" id="PA_noteDao" parent="baseDao"/>
		<!-- add by lvkai 贷款、自垫终止通知书 -->
	<bean class="com.nci.tunan.pa.dao.impl.NoticeDaoImpl" id="PA_noticeDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.BusiProdQueryDaoImpl" id="PA_busiProdQueryDao" parent="baseDao">
	</bean>
	
	<bean class="com.nci.tunan.pa.dao.impl.PolicyPrintDaoImpl" id="PA_policyPrintDao" parent="baseDao">
	</bean>
	<!--add by yaodl_wb 投保人或被保人是否存在期满保单  -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyExpirationDaoImpl" id="PA_policyExpirationDao" parent="baseDao">
	</bean>
	<!-- 保单红利 -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyBonusDaoImpl" id="PA_policyBonusDao" parent="baseDao">
	</bean>
		<!-- 保单红利 -->
	<bean class="com.nci.tunan.pa.dao.impl.QueryInsuAccInfoDaoImpl" id="PA_queryInsuAccInfoDao" parent="baseDao">
	</bean>
	<!-- 分红信息查询cjk -->
	<bean class="com.nci.tunan.pa.dao.impl.AllocatBnusDaoImpl" id="PA_iAllocatBnusDao" parent="baseDao">
	</bean>
	<!-- 风险保额队列表 -->
	<bean class="com.nci.tunan.pa.dao.impl.RiskAmountQueueDaoImpl" id="PA_riskAmountQueueDao" parent="baseDao">
	</bean>
	<!-- 发生保全项查询 -->
	<bean class="com.nci.tunan.pa.common.dao.impl.PaAcceptChangeDaoImpl" id="PA_paAcceptChangeDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.common.dao.impl.PACommonSearchDao" id="PA_pACommonSearchDao" parent="baseDao">
	</bean>
	<!-- 业务变更履历查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.TransDataDaoImpl" id="PA_transDataDao" parent="baseDao">
	</bean>

	<!-- 产品是否可续保 -->
	<bean class="com.nci.tunan.pa.dao.impl.ProductRenewDaoImpl" id="PA_iProductRenewDao" parent="baseDao"/>

	<!--  -->
	<bean class="com.nci.tunan.pa.dao.impl.LiabilityDaoImpl" id="PA_pa_liabilityDao" parent="baseDao">
	</bean>
	<!-- 查询责任组最新加费评点 -->
	<bean class="com.nci.tunan.pa.dao.impl.ExtraPremEmDaoImpl" id="PA_extraPremEmDao" parent="baseDao">
	</bean>
	
	<!-- 根据输入的移动电话及固定电话，查询出使用此了该号码的投保人集合 -->
	<bean class="com.nci.tunan.pa.dao.impl.QueryCusFiveBasicByTelDaoImpl" id="PA_queryCusFiveBasicByTelDao" parent="baseDao">
	</bean>
	<!-- 客户身份信息验真开关 -->
	<bean class="com.nci.tunan.pa.dao.impl.IdentityCheckSwitchDaoImpl" id="PA_identitycheckswitchDao" parent="baseDao">
	</bean>
	
	<!-- 年度分红业绩报告书查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.QueryYearBonusReportDaoImpl" id="PA_queryYearBonusReportDao" parent="baseDao">
	</bean>
	<!-- 合作机构信息表-->
	<bean class="com.nci.tunan.pa.dao.impl.SaleComDaoImpl" id="PA_saleComDao" parent="baseDao">
	</bean>
	
	<!-- 个人保单信息查询  -->
	<bean class="com.nci.tunan.pa.dao.impl.QueryPersonalPolicyDetailDaoImpl" id="PA_queryPersonalPolicyDetailDao" parent="baseDao">
	</bean>
	<bean class="com.nci.tunan.pa.dao.impl.AgentDaoImpl" id="PA_agentDaoTunan" parent="baseDao">
	</bean>
	<!-- 查询披露信息dao -->
	<bean class="com.nci.tunan.pa.dao.impl.RossInfoDaoImpl" id="PA_rossInfoDao" parent="baseDao">
	</bean>
	<!-- 查询保全生调验证规则-->
	<bean class="com.nci.tunan.pa.dao.impl.LiabSurveryRuleDaoImpl" id="PA_liabSurveryRuleDao" parent="baseDao"/>
	
	<!-- 订单号快速查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.OrderServiceDaoImpl" id="PA_PAorderServiceDao" parent="baseDao">
	</bean>
	<!-- 新契约查询保单历史接口优化  add by yangyl_wb -->
	<bean class="com.nci.tunan.pa.impl.nbquerypolicy.dao.impl.NbQueryPolicyDaoImpl" id="PA_nbQueryPolicyDao" parent="baseDao"/>
	<!-- 新契约查询投被受历史保单接口-->
	<bean class="com.nci.tunan.pa.impl.nbqueryhistorypolicy.dao.impl.NBQueryHistoryPolicyDaoImpl" id="PA_nbQueryHistoryPolicyDao" parent="baseDao"/>
	<!-- 查询营销活动 -->
	<bean class="com.nci.tunan.pa.dao.impl.ContractMarketActDaoImpl" id="PA_contractMarketActDao" parent="baseDao"/>
 	<!-- 纠错接口dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.ErrorFieldCfgDaoImpl" id="PA_errorFieldCfgDao" parent="baseDao"></bean>
	<!-- 孤儿单上传dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.OrphanPolicyDaoImpl" id="PA_orphanPolicyDao" parent="baseDao"></bean>
 	<!-- 实时孤儿单查询通知-->
 	<bean class="com.nci.tunan.pa.dao.impl.OrphanParameterDaoImpl" id="PA_orphanParameterDao" parent="baseDao"></bean>
 	<bean class="com.nci.tunan.pa.dao.impl.OrphanParameterDetailDaoImpl" id="PA_orphanParameterDetailDao" parent="baseDao"></bean>
 	<!-- 查询用户是否对保单进行过核保操作dao -->
 	<bean class="com.nci.tunan.cs.dao.impl.QueryPolicyReviewDaoImpl" id="PA_queryPolicyReviewDao" parent="baseDao"></bean>
	 	
	 <!-- 物流丢失保单标记dao -->
	 <bean class="com.nci.tunan.pa.impl.nbdatadeal.dao.impl.PostLostDealDaoImpl" id="PA_postLostDealDao" parent="baseDao">
	 	
	 </bean>
	 <!-- 农行保全状态同步dao -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyStatusSynForAbcDaoImpl" id="PA_policyStatusSynForAbcDao" parent="baseDao">
	</bean>
	<!-- 建行保单详情查询 -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyDetailForCCBDaoImpl" id="PA_policyDetailForCCBDao" parent="baseDao">
	</bean>
	<!-- 接入渠道犹豫期退保数据查询接口 -->
    <bean class="com.nci.tunan.pa.common.dao.impl.QueryHesitationSurrenderDaoImpl" id="PA_queryHesitationSurrenderDao"  parent="baseDao">
    </bean>
    
     <!-- 北京银行保单资产变动信息同步交易dao -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyCapticalChgSynDaoImpl" id="PA_policyCapticalChgSynDao" parent="baseDao">
	</bean>
	 
	<!-- 老核心孤儿单查询dao -->
 	<bean class="com.nci.tunan.pa.batch.orphanpolicy.dao.impl.OldOrphanPolicyDaoImpl" id="PA_oldOrphanPolicyDao">
 		<property name ="dataSource" ref="PA_oldDataSource" />
 	</bean>
 	
 	<!-- 创建数据源 c3p0 设置驱动，数据库连接，账号，密码-->
	<bean id="PA_oldDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
		<property name="driverClass" value="oracle.jdbc.driver.OracleDriver"></property>
		<property name="jdbcUrl" value="***************************************"></property>
		<property name="user" value="ncl_1"></property>
		<property name="password" value="ncl_1"></property>
	</bean>
	
	<!-- 老核心总保费查询 -->
 	<bean class="com.nci.tunan.pa.dao.impl.LppremDaoImpl" id="PA_lppremDao" parent="baseDao" ></bean>
 	<bean class="com.nci.tunan.pa.dao.impl.LcpremDaoImpl" id="PA_lcpremDao" parent="baseDao" ></bean> 
 				 <!--建行查询保单历史变动信息  -->
	 <bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.QueryPolicyHisForCCBDaoImpl" id="PA_queryPolicyHisForCCBDao" parent="baseDao">	 </bean>
	 <bean class="com.nci.tunan.pa.dao.impl.CerticodePromoteLogDaoImpl" id="PA_CerticodePromoteLogDao" parent="baseDao" ></bean> 


 	
 	<!-- ODS保单现价dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.ItemCashvalueDaoImpl" id="PA_itemCashvalueDao" parent="baseDao" ></bean>
 	<bean class="com.nci.tunan.pa.dao.impl.CashvalueFlagDaoImpl" id="PA_cashvalueFlagDao" parent="baseDao" ></bean> 
 	
 	<!--ODS计算现价保单记录表  -->
	<bean class="com.nci.tunan.pa.dao.impl.CvTxInfoDetailDaoImpl" id="PA_cvTxInfoDetailDao" parent="baseDao"/>
	<!--ODS计算现价保单批次表  -->
	<bean class="com.nci.tunan.pa.dao.impl.CvTxInfoMainDaoImpl" id="PA_cvTxInfoMainDao" parent="baseDao"/>
 	
 	<!-- 保单宽限期延长天数表dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.ExtendGracePeriodDaoImpl" id="PA_extendGracePeriodDao" parent="baseDao" ></bean>
 	
 	<!-- 保单宽限期延长天数配置表dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.ExtendGracePeriodRuleDaoImpl" id="PA_extendGracePeriodRuleDao" parent="baseDao" ></bean>
 	
 	<!-- 查询保单金额接口dao -->
 	<bean class=" com.nci.tunan.pa.impl.queryPolicyAmount.dao.impl.QueryPolicyAmountDaoImpl" id="PA_queryPolicyAmountDao" parent="baseDao" ></bean>

 	<!-- 非银保通查询接口dao -->
 	<bean class=" com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyQueryNonYbtDaoImpl" id="PA_policyQueryNonYbtDao" parent="baseDao" ></bean>
 	
 	<!-- 合并客户ID接口Dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.IsUpdateCustomerDaoImpl" id="PA_isUpdateCustomerDao" parent="baseDao"></bean>
 	
 	<!-- 银行编码转换配置表Dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.PremBankCfgDaoImpl" id="PA_premBankCfgDao" parent="baseDao"></bean>
 	
 	<bean class="com.nci.tunan.pa.dao.impl.ElectronicEdorDaoImpl" id="PA_electronicEdorDao" parent="baseDao"/>
 	
	<!-- 投被保人关系查询配置表Dao -->
 	<bean class="com.nci.tunan.cs.dao.impl.HolderRelationInsuredDaoImpl" id="PA_holderRelationInsuredDao" parent="baseDao"></bean>

	<!-- 上海医保转保单Dao -->
	 <bean class="com.nci.tunan.pa.dao.impl.CreateMedicalDaoImpl" id="PA_createMedicalDao" parent="baseDao"></bean>

	<!-- 新增附加险 -保单列表查询接口 -->
	<bean class="com.nci.tunan.cs.dao.impl.QueryNsPolicyDaoImpl" id="PA_iQueryNsPolicyDao" parent="baseDao"></bean>
	 <!--一键查询 -->
	 <bean class="com.nci.tunan.cs.dao.impl.CsQueryCustomerInfoDaoImpl" id="PA_csQueryCustomerInfoDao" parent="baseDao"></bean>

	 <!-- CRS客户查询 -->
	 <bean class="com.nci.tunan.pa.dao.impl.NBQueryCrsCustomerDaoImpl" id="PA_nbQueryCrsCustomerDao" parent="baseDao"></bean>

	 <!-- 查询客户保单(建行) -->
	 <bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.CustomerPolicyDaoImpl" id="PA_customerPolicyDao" parent="baseDao"></bean>
	 
	 <!-- 税延产品表Dao -->
	 <bean class="com.nci.tunan.pa.dao.impl.TaxExtensionDaoImpl" id="PA_taxExtensionDao" parent="baseDao"></bean>
	 <!-- 税延产品客户税收信息表Dao -->
	 <bean class="com.nci.tunan.pa.dao.impl.CustomerTaxInfoDaoImpl" id="PA_customerTaxInfoDao" parent="baseDao"></bean>
	 <bean class="com.nci.tunan.pa.dao.impl.TaxCustomerInfoDaoImpl" id="PA_taxCustomerInfoDao" parent="baseDao"></bean>
	 <!-- 税延责任组表Dao -->
	 <bean class="com.nci.tunan.pa.dao.impl.ContractProductTaxDaoImpl" id="PA_contractProductTaxDao" parent="baseDao"></bean>
	 
	 <!-- 保单编码查询（中保信） -->
	 <bean id="PA_policyCodeQueryFromPADao" class="com.nci.tunan.pa.dao.impl.PolicyCodeQueryFromPADaoImpl" parent="baseDao"></bean>
     
     <!-- 保单编码查询（中保信）出险人信息 -->
	<bean id="PA_phoneInfoQueryFromPADao" class="com.nci.tunan.pa.dao.impl.PhoneInfoQueryFromPADaoImpl" parent="baseDao"></bean>
     
     <!-- box dao -->
     <bean id="PA_createBoxAccountDao" class="com.nci.tunan.pa.dao.impl.CreateBoxAccountDaoImpl" parent="baseDao"></bean>
     <bean id="PA_boxAccountDao" class="com.nci.tunan.pa.dao.impl.BoxAccountDaoImpl" parent="baseDao"></bean>
     <!-- BOX监管限额录入dao -->
     <bean id="PA_boxSuperviseLimitDao" class="com.nci.tunan.pa.dao.impl.BoxSuperviseLimitDaoImpl" parent="baseDao"></bean>
     <!-- 资金划拨dao -->
     <bean id="PA_BoxPositionDao" class="com.nci.tunan.pa.dao.impl.BoxPositionDaoImpl" parent="baseDao"></bean> 
	 <!-- box内部限额录入修改dao -->
	 <bean id="PA_createBoxInternalLimitDao" class="com.nci.tunan.pa.dao.impl.CreateBoxInternalLimitDaoImpl" parent="baseDao"></bean> 
    <!-- 计价报告打印 -->
	<bean id="PA_boxValuationReportDao" class="com.nci.tunan.pa.dao.impl.BoxValuationReportDaoImpl" parent="baseDao"></bean> 
	<!-- box 内部限额DaoImpl -->
	<bean id="PA_BoxInternalLimitDao" class="com.nci.tunan.pa.dao.impl.BoxInternalLimitDaoImpl" parent="baseDao"></bean> 
	
    <!-- 保单关联关系表 -->
    <bean id="PA_contractRelationDao" class="com.nci.tunan.pa.dao.impl.ContractRelationDaoImpl" parent="baseDao"></bean>
    <!-- 费率调整后撤销应收费用dao -->
    <bean id="PA_rateAdjustRevokPmDao" class="com.nci.tunan.pa.batch.rateadjustrevokeprem.dao.impl.RateAdjustRevokPmDaoImpl" parent="baseDao"></bean>
    <!-- 万能险链接短信表dao -->
    <bean id="PA_universalSmsDao" class="com.nci.tunan.pa.batch.sendnote.dao.impl.UniversalSmsDaoImpl" parent="baseDao"></bean>
    <!-- 万能险链接短信险种配置信息表dao -->
    <bean id="PA_universalBusiInfoDao" class="com.nci.tunan.pa.batch.sendnote.dao.impl.UniversalBusiInfoDaoImpl" parent="baseDao"></bean>
    <!-- 住院津贴限额配置信息表dao -->
    <bean id="PA_hospitalBenefitLimitDao" class="com.nci.tunan.pa.dao.impl.HospitalBenefitLimitDaoImpl" parent="baseDao"></bean>
    <!-- 贷款起息日批处理dao -->
    <bean id="PA_interestStartDateDao" class="com.nci.tunan.pa.batch.intereststartdatebatch.dao.impl.InterestStartDateDaoImpl" parent="baseDao"></bean>
    <!-- 万能险初始化批处理dao -->
    <bean id="PA_universalInsuranceInitializeDao" class="com.nci.tunan.pa.batch.universalinsuranceinitialize.dao.impl.UniversalInsuranceInitializeDaoImpl" parent="baseDao"></bean>
    <!-- 保单现价报送任务表dao -->
    <bean id="PA_taskCashValueDao" class="com.nci.tunan.pa.dao.impl.TaskCashValueDaoImpl" parent="baseDao"></bean>
    <!-- 保单现价报送任务备份表dao -->
    <bean id="PA_taskCashValueBDao" class="com.nci.tunan.pa.dao.impl.TaskCashValueBDaoImpl" parent="baseDao"></bean>
    <!-- 柜面渠道保单自动推送收展部批处理dao -->
    <bean id="PA_websiteToCusServiceDao" class="com.nci.tunan.pa.batch.websiteToCusService.dao.impl.websiteToCusServiceDaoImpl" parent="baseDao"></bean>
	<!--工作流自动节点-->
	<bean id="CS_TaskBpmAutoNodeDao" class="com.nci.tunan.cs.dao.impl.TaskBpmAutoNodeDaoImpl" parent="baseDao"></bean>
    <!-- BOX批处理dao -->
    <bean id="BOX_boxPositionBatchDao" class="com.nci.tunan.pa.batch.BoxPositionBatch.dao.impl.BoxPositionBatchDaoImpl" parent="baseDao"></bean>
    <!-- BOX批处理dao -->
    <bean id="BOX_boxCustomerBatchDao" class="com.nci.tunan.pa.batch.BoxCustomer.dao.impl.BoxCustomerBatchDaoImpl" parent="baseDao"></bean>
	<!--通知书打印记录信息表-->
	<bean id="Cs_PrintClmDocumentDao" class="com.nci.tunan.cs.dao.impl.PrintClmDocumentDaoImpl" parent="baseDao"></bean>
	<!-- 停止新单业务险种配置表 -->
	<bean id="PA_stopBusiConfigDao" class="com.nci.tunan.cs.dao.impl.StopBusiConfigDaoImpl" parent="baseDao"></bean>
	<!-- 停止新单产品配置表  -->
	<bean id="PA_stopBusiProdCfgDao" class="com.nci.tunan.cs.dao.impl.StopBusiProdCfgDaoImpl" parent="baseDao"></bean>
	<!-- 薪资状态读取Dao -->
    <bean id="PA_commisionStatusFetchDao" class="com.nci.tunan.pa.batch.CommisionStatusFetch.dao.impl.CommisionStatusFetchDaoImpl" parent="baseDao"></bean>
	<!-- 广州市社会救助的申请人核对Dao -->
    <bean id="PA_socialAssistanceCheckDao" class="com.nci.tunan.pa.batch.SocialAssistanceCheckQuery.dao.impl.SocialAssistanceCheckDaoImpl" parent="baseDao"></bean>
    <!--银保通客户资料采集需求（补充资料）-保单查询-->
	<bean id="PA_WXPolicyQueryDao" class="com.nci.tunan.pa.dao.impl.WXPolicyQueryDaoImpl" parent="baseDao"></bean>

	<!--深圳医保数据推送页面查询-->
	<bean id="CS_SZYBDataSendDaoImpl" class="com.nci.tunan.cs.dao.impl.SZYBDataSendDaoImpl" parent="baseDao"/>

	<!--接入渠道交通银行-非实时保单查询接口-->
	<bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyQueryDelayedJTYHDaoImpl" id="PA_policyQueryDelayedJTYHDao" parent="baseDao"></bean>
	<!-- 续期失效保单补跑贷款中止 -->
	<bean id="PA_renewalExpireAbortDao" class="com.nci.tunan.pa.batch.RenewalExpireAbort.dao.impl.RenewalExpireAbortDaoImpl" parent="baseDao"></bean>
	<!-- 保单与医保卡关联信息表 -->
	<bean class="com.nci.tunan.pa.dao.impl.MedicalCardDaoImpl" id="PA_medicalCardDao" parent="baseDao"/>
    <!--险种转保标识变更轨迹表 -->
    <bean class="com.nci.tunan.pa.dao.impl.BusiProdFlagTraceDaoImpl" id="PA_busiProdFlagTraceDao" parent="baseDao"/>
    <!-- 短期险自助重投提醒短信 -->
    <bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.dao.impl.StPrdReinsuredMsgDaoImpl" id="PA_stPrdReinsuredMsgDao" parent="baseDao"/>
	<!-- 宽限期内未确认重新投保/转保险种预失效/终止短信 -->
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.dao.impl.GracePeriodSMSDaoImpl" id="PA_gracePeriodSMSDao" parent="baseDao"/>
	<!-- 建行查询客户保单接口新增dao -->
	<bean id="PA_queryBankBranchMapping" class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.QueryBankBranchMappingDaoImpl" parent="baseDao"></bean>
	<!-- 保单已失效一年提醒Dao -->
	<bean class="com.nci.tunan.pa.batch.policyReminderForOneYear.dao.impl.PolicyReminderForOneYearDaoImpl" id="PA_policyReminderForOneYearDao" parent="baseDao"/>
	<!-- 万能账户扣费进入划款期后提醒Dao -->
	<bean class="com.nci.tunan.pa.batch.universalAccountEntersPeriodAfter.dao.impl.UniversalAccountEntersPeriodAfterDaoImpl" id="PA_universalAccountEntersPeriodAfterDao" parent="baseDao" />
	<!-- 万能账户扣费成功后，次日发送短信提示Dao -->
	<bean class="com.nci.tunan.pa.batch.universalAccountDeductionSuccess.dao.impl.UniversalAccountDeductionSuccessDaoImpl" id="PA_universalAccountDeductionSuccessDao" parent="baseDao" />
	<!-- 续保险种转换到期划款提醒 -->
	<bean class="com.nci.tunan.pa.batch.stPrdReinsuredMsg.dao.impl.RenewalRiskSwitchTransferMsgDaoImpl" id="PA_renewalRiskSwitchTransferMsgDao" parent="baseDao"/>
	<!-- 上海医保账户重投提醒短信 -->
	<bean class="com.nci.tunan.pa.batch.medicalMsg.dao.impl.MedicalMsgDaoImpl" id="PA_medicalMsgDao" parent="baseDao"/>
	<!-- 上海医保重新投保请求 -->
	<bean class="com.nci.tunan.pa.batch.canReinsure.dao.impl.CanReinsureDaoImpl" id="PA_canReinsureDao" parent="baseDao"/>
	<!-- 上海医保重新投保请求 -->
	<bean class="com.nci.tunan.pa.dao.impl.RenewConfirmTraceDaoImpl" id="PA_RenewConfirmTraceDao" parent="baseDao"/>
	<!-- ABS现价计算任务主表 -->
	<bean class="com.nci.tunan.pa.dao.impl.AbsCashTaskMainDaoImpl" id="PA_absCashTaskMainDao" parent="baseDao"/>
	<!-- ABS现价计算任务详情表 -->
	<bean class="com.nci.tunan.pa.dao.impl.AbsCashTaskDetailDaoImpl" id="PA_absCashTaskDetailDao" parent="baseDao"/>
	<!-- ABS现价表 -->
	<bean class="com.nci.tunan.pa.dao.impl.AbsCashValueDaoImpl" id="PA_absCashValueDao" parent="baseDao"/>
	<!-- 投资账户组合交易表接口 -->
	<bean class="com.nci.tunan.pa.dao.impl.FundGroupTransDaoImpl" id="PA_FundGroupTransDao" parent="baseDao"/>
	<!-- 销售限额配置表接口 -->
	<bean class="com.nci.tunan.pa.dao.impl.SalseAmountCfgDaoImpl" id="PA_salseAmountCfgDao" parent="baseDao"/>
	
	<bean class="com.nci.tunan.pa.dao.impl.WarnMailInfoDaoImpl" id="PA_warnMailInfoDao" parent="baseDao"/>
	
	<!-- 销售限额配置轨迹表接口 -->
	<bean class="com.nci.tunan.pa.dao.impl.SalseAmountCfgLogDaoImpl" id="PA_salseAmountCfgLogDao" parent="baseDao"/>
	<!-- 险种销售额度信息表 -->
	<bean class="com.nci.tunan.pa.dao.impl.SalesAmountDaoImpl" id="PA_salesAmountDao" parent="baseDao"/>
	<!-- 险种销售额度累计批处理Dao-->
	<bean class="com.nci.tunan.pa.batch.busiProdSalesAmountAcc.dao.impl.BusiProdSalesAmountAccDaoImpl" id="PA_busiProdSalesAmountAccDao" parent="baseDao"/>
	<!-- CRS报送客户信息批处理Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.CrsCustPolDaoImpl" id="PA_crsCustPolDao" parent="baseDao"/>
	<!-- 关联保单抵扣保费批处理Dao-->
	<bean class="com.nci.tunan.pa.batch.automaticextra.dao.impl.AccountValueTORenewalPremDaoImpl" id="PA_accountValueTORenewalPremDao" parent="baseDao"/>
	<!-- 关联保单抵扣保费记录表接口Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.AccValToPremTraceDaoImpl" id="PA_accValToPremTraceDao" parent="baseDao"/>
	<!-- 大数据平台现价计算表接口Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.DatacenterCashTaskDaoImpl" id="PA_datacenterCashTaskDao" parent="baseDao"/>
	<!-- 现价计算Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.CashvalueDaoImpl" id="PA_cashvalueDao" parent="baseDao"/>
	<!-- 信托签收Dao-->
	<bean class="com.nci.tunan.cs.dao.impl.CssSignInfomationDaoImpl" id="PA_cssSignInfomationDao" parent="baseDao"/>
	<!-- 日计价 Dao-->
 	<bean class="com.nci.tunan.pa.dao.impl.InvestPriceDayDaoImpl" id="PA_investPriceDayDaoDao" parent="baseDao"/>
	<!-- 续期缴费变更同步列表查询接口dao-->
	<bean class="com.nci.tunan.pa.dao.impl.RenewalFeeInfoQueryDaoImpl" id="PA_renewalFeeInfoQueryDao" parent="baseDao"/>
	
	<!-- 保后调查上传批处理Dao-->
	<bean class="com.nci.tunan.pa.batch.riskScoreConfig.dao.impl.RiskScoreConfigDaoImpl" id="PA_riskScoreConfigDao" parent="baseDao"/>
	<!-- 保后调查上传批处理Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.RiskLevelConfigDaoImpl" id="PA_riskLevelConfigDao" parent="baseDao"/>
	<!-- 暂交退费Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.TempPremReturnDaoImpl" id="PA_tempPremReturnDao" parent="baseDao"/>
    <!-- 928终止报告书Dao-->
 	<bean class="com.nci.tunan.pa.dao.impl.SettleReportDaoImpl" id="PA_settleReportDao" parent="baseDao"/>
 	<!-- 账户单位数表Dao-->
 	<bean class="com.nci.tunan.pa.dao.impl.CustomerUnitOtherDaoImpl" id="PA_customerUnitOtherDao" parent="baseDao"/>
    <!-- 生存给付计划生成任务表 Dao-->
 	<bean class="com.nci.tunan.pa.dao.impl.PayPlanCreateTaskDaoImpl" id="PA_payPlanCreateTaskDao" parent="baseDao"/>
 	<!-- 短信发送记录表 Dao-->
 	<bean class="com.nci.tunan.pa.dao.impl.YwxBusiItemIdDaoImpl" id="PA_ywxBusiItemIdDao" parent="baseDao"/>
	<!--退保保全记录查询接口Dao-->
	<bean id="PA_PolicyCancellationKeepIntactQueryDao" class="com.nci.tunan.pa.dao.impl.PolicyCancellationKeepIntactRecordQueryDaoImpl" parent="baseDao"></bean>
	<!--追加保费保全记录查询接口Dao-->
	<bean id="PA_AddToPremiumQueryDao" class="com.nci.tunan.pa.dao.impl.AddToPremiumPreserveQueryDaoImpl" parent="baseDao"></bean>
	<!-- 意外险加保办理提醒Dao -->
	<bean class="com.nci.tunan.pa.batch.paPolicyRemindNote.dao.impl.PaPolicyRemindNoteDaoImpl" id="PA_paPolicyRemindNoteDao" parent="baseDao"/>
	<!-- 月交产品续期交费提醒Dao -->
	<bean class="com.nci.tunan.pa.batch.monthlyProductRenewalFeeReminder.dao.impl.MonthlyProductRenewalFeeReminderDaoImpl" id="PA_monthlyProductRenewalFeeReminderDao" parent="baseDao" />
	<!-- 是否符合扶贫标准轨迹表Dao  -->
	<bean class="com.nci.tunan.pa.dao.impl.MeetPovStandardFlagTrackDaoImpl" id="PA_meetPovStandardFlagTrackDao" parent="baseDao"/>
	<!-- 保单状态轨迹信息表Dao  -->
	<bean class="com.nci.tunan.pa.dao.impl.PolicyStatusTraceDaoImpl" id="PA_policyStatusTraceDao" parent="baseDao"/>
	<!-- 保单状态轨迹信息表Dao  -->
	<bean class="com.nci.tunan.pa.batch.annualautorenewalprepaynote.dao.impl.AnnualAutoRenewalPrePayNoteDaoImpl" id="PA_annualAutoRenewalPrePayNoteDao" parent="baseDao"/>
	<!-- 保全外包打印站点配置信息Dao  -->
	<bean class="com.nci.tunan.cs.dao.impl.PrintConfigDaoImpl" id="CS_printConfigDao" parent="baseDao"/>
	<!-- 保单第二投保人履历表Dao -->
	<bean class="com.nci.tunan.pa.dao.impl.SecondPolicyHolderLogDaoImpl" id="PA_secondPolicyHolderLogDao" parent="baseDao"/>
	<!-- 保单第二投保人索引表 Dao -->
	<bean class="com.nci.tunan.pa.dao.impl.SecondPolicyHolderCxDaoImpl" id="PA_secondPolicyHolderCxDao" parent="baseDao"/>
	<!-- 保单收益查询 -->
 	<bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyIncomeQueryDaoImpl" id="PA_policyIncomeQueryDao" parent="baseDao"/>
 	<!-- 投保人及账户变更 -->
 	<bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyHolderInfoQueryDaoImpl" id="PA_policyHolderInfoQueryDao" parent="baseDao"/>
	<!-- 保全重点帮扶配置表 -->
	<bean class="com.nci.tunan.cs.dao.impl.CsPovCfgDaoImpl" id="PA_cspovcfgdao" parent="baseDao"/>
 	<!-- 中银保账户信息表 -->
 	<bean class="com.nci.tunan.pa.dao.impl.SpecialAccountInfoDaoImpl" id="PA_specialAccountInfoDao" parent="baseDao"/>
 	<!-- 中银保账户与保单关联索引表 -->
 	<bean class="com.nci.tunan.pa.dao.impl.SpecialAccountRelationCxDaoImpl" id="PA_specialAccountRelationCxDao" parent="baseDao"/>
 	<!-- 中银保账户与保单关联表 -->
 	<bean class="com.nci.tunan.pa.dao.impl.SpecialAccountRelationDaoImpl" id="PA_specialAccountRelationDao" parent="baseDao"/>
 	<!-- 中银保账户与保单关联变更履历表 -->
 	<bean class="com.nci.tunan.pa.dao.impl.SpecialAccountRelationLogDaoImpl" id="PA_specialAccountRelationLogDao" parent="baseDao"/>
 	<!-- 个人养老金信息报送表 -->
 	<bean class="com.nci.tunan.pa.dao.impl.SubmitMessageBocicDaoImpl" id="PA_submitMessageBocicDao" parent="baseDao"/>
	<!-- 邮储银行联机保单查询 -->
 	<bean class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyInfoPsbcDaoImpl" id="PA_policyInfoPsbcDao" parent="baseDao"/>
 	
 	<!-- 保单税优报送任务表Dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.TaxPremiumPolicyTaskDaoImpl" id="PA_taxPremiumPolicyTaskDao" parent="baseDao"/>
 	
 	<!-- 税优保单报送轨迹表Dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.TaxPolicySubmittedLogDaoImpl" id="PA_taxPolicySubmittedLogDao" parent="baseDao"/>
 	
 	<!-- 保单详细信息查询DAO R06401900830-->
 	<bean class="com.nci.tunan.pa.dao.impl.QueryPolicyDetailsInfoDaoImpl" id="PA_queryPolicyDetailsInfoDao" parent="baseDao"/>
 	<!-- 139453 -->
 	<bean class="com.nci.tunan.pa.dao.impl.PlatInvestAccBindTraceDaoImpl" id="PA_platInvestAccBindTraceDao" parent="baseDao"/>
 	<!-- 保证续保轨迹表Dao RM:136996 -->
 	<bean class="com.nci.tunan.pa.dao.impl.RenewalLogDaoImpl" id="PA_renewalLogDao" parent="baseDao"/>
 	
 	<!-- 需求分析任务 #144167: 个人税收优惠型健康保险保全需求-保单状态修改上传 start-->
     	<bean class="com.nci.tunan.pa.dao.impl.PolicyStatusSubmitDaoImpl" id="PA_policyStatusSubmitDao" parent="baseDao"/>
        <bean class="com.nci.tunan.pa.dao.impl.TaxBusitStatusLogDaoImpl" id="PA_taxBusitStatusLogDao" parent="baseDao"/>
        <bean class="com.nci.tunan.pa.dao.impl.TaxProductStatusLogDaoImpl" id="PA_taxProductStatusLogDao" parent="baseDao"/>
    <!-- 需求分析任务 #144167: 个人税收优惠型健康保险保全需求-保单状态修改上传 end-->
 	
 	
 	<!-- 需求分析任务 #147285 睡眠保单项目需求-核心系统睡眠保单标识更新需求保单 start -->
     	<bean class="com.nci.tunan.pa.dao.impl.PolicyMarkingInfoDaoImpl" id="PA_policyMarkingInfoDao" parent="baseDao"/>
        <bean class="com.nci.tunan.pa.dao.impl.PolicyMarkingLogDaoImpl" id="PA_policyMarkingLogDao" parent="baseDao"/>
        <bean class="com.nci.tunan.pa.dao.impl.PolicyMarkingBacthInfoDaoImpl" id="PA_policyMarkingBacthInfoDao" parent="baseDao"/>
        <bean class="com.nci.tunan.pa.dao.impl.SleepyPolicyStatusUpdateDaoImpl" id="PA_sleepyPolicyStatusDao" parent="baseDao"/>
    <!-- 需求分析任务 #147285 睡眠保单项目需求-核心系统睡眠保单标识更新需求保单 end -->
 	<!-- 个险产品停止续保短信通知Dao -->
	<bean class="com.nci.tunan.pa.batch.personalInsuranceProductNotRenewal.dao.Impl.PersonalInsuranceProductNotRenewalDaoImpl" id="PA_personalInsuranceProductNotRenewalDao" parent="baseDao" />
	<bean class="com.nci.tunan.pa.dao.impl.NoRenewLogDaoImpl" id="PA_noRenewLogDao" parent="baseDao" />
	
 	<!-- 初始化续期年金生存调查结果确认Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.PayConfirmListDaoImpl" id="PA_payConfirmListDao" parent="baseDao"/>
	
	<!-- 客户确认生存信息表Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.CusSurvivalConfirmDaoImpl" id="PA_cusSurvivalConfirmDao" parent="baseDao"/>
	
	<!-- 保单业务短信信息表Dao-->
	<bean class="com.nci.tunan.pa.dao.impl.PolicySmsTaskDaoImpl" id="PA_policySmsTaskDao" parent="baseDao"/>
	
	<!-- 商业养老保险账户信息同步查询Dao  -->
    <bean id="PA_policyInfoPaqdDao" class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.PolicyInfoPaqdDaoImpl" parent="baseDao"></bean>
    
    <!-- 手续费对账查询接口Dao -->
    <bean id="PA_serviceChargeReconciliationDaoImpl" class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.ServiceChargeReconciliationDaoImpl" parent="baseDao"></bean>
    
    <!-- 身故受益人详情查询接口Dao -->
    <bean id="PA_dieBenefitInfoDao" class="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.DieBenefitInfoDaoImpl" parent="baseDao"></bean>
    
    <!-- 提供客户信息给契约做银保通业务功能Dao -->
    <bean id="PA_provideCustomerInfosForBancasBusinessForNBDao" class="com.nci.tunan.pa.impl.hessianinterface.dao.impl.ProvideCustomerInfosForBancasBusinessForNBDaoImpl" parent="baseDao"></bean>
    
	
	<!-- 保费核销失败信息表Dao -->
    <bean id="PA_renewCollectionFailDao" class="com.nci.tunan.pa.dao.impl.RenewCollectionFailDaoImpl" parent="baseDao"></bean>
	
	<!-- 需求分析任务 #160209 续保核保审核（保单管理）-->
    <bean id="PA_renewalUnderWritingDAO" class="com.nci.tunan.pa.batch.renewalunderwriting.dao.impl.RenewalUnderWritingDAOImpl" parent="baseDao"></bean>
    <bean id="PA_uwRenewalInfoDao" class="com.nci.tunan.pa.dao.impl.UwRenewalInfoDaoImpl" parent="baseDao"></bean>
    <bean id="PA_renewalLimitDao" class="com.nci.tunan.pa.dao.impl.RenewalLimitDaoImpl" parent="baseDao"></bean>
	
	<!-- 续保成功通知书-推送任务表Dao -->
    <bean id="PA_noticeMsgTaskDao" class="com.nci.tunan.pa.dao.impl.NoticeMsgTaskDaoImpl" parent="baseDao"></bean>
    <bean id="PA_renewalSuccessfulDAO" class="com.nci.tunan.pa.batch.renewalsuccessful.dao.impl.RenewalSuccessfulDAOImpl" parent="baseDao"></bean>
	<bean class="com.nci.tunan.cs.dao.impl.SurrenderSendInfoDaoImpl" id="CS_surrenderSendInfoDao" parent="baseDao"/>
	
	<!--需求分析任务 #172886: 关于新增银代渠道分公司佣金率管理等相关功能的需求-二期 保单 -->
   <bean class="com.nci.tunan.pa.dao.impl.FycrateComDaoImpl" id="PA_FycrateComDao" parent="baseDao">
   </bean>
   
   <bean class="com.nci.tunan.pa.dao.impl.DiscountPremRateDaoImpl" id="PA_DiscountPremRateDao" parent="baseDao">
   </bean>
	
	<!-- 贷款抵扣信息任务表Dao -->
    <bean id="PA_survivalDeductionTaskDao" class="com.nci.tunan.pa.dao.impl.SurvivalDeductionTaskDaoImpl" parent="baseDao"></bean>
    
    <!-- 派发年度红利后重新发放生存金满期金任务表Dao -->
    <bean id="PA_annuityReissueInfoDao" class="com.nci.tunan.pa.dao.impl.AnnuityReissueInfoDaoImpl" parent="baseDao"></bean>
    
    <!-- 需求分析任务 #189195 盛世安盈养老年金保险（分红型）保全业务系统需求（598/5981）-二期- 保单管理 -->
	<bean class="com.nci.tunan.pa.dao.impl.PrecontPayDaoImpl" id="PA_precontPayDao" parent="baseDao"></bean>
    
    <!-- 工行纸质保单补充Dao -->
	<bean class="com.nci.tunan.pa.dao.impl.QueryPaperSupplementDaoImpl" id="PA_queryPaperSupplementDao" parent="baseDao"></bean>
	
	<!-- 需求分析任务 #188646: 需求意向单：个险新核心核心万能险抵交保费功能优化-保单 -->
	<bean class="com.nci.tunan.pa.common.dao.impl.UniverInsuranceOffsetPremDaoImpl" id="PA_univerInsuranceOffsetPremDao" parent="baseDao"></bean>
</beans>
