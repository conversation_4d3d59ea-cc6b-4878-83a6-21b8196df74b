<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyListUCCgetPolicyListAddr">
		<property name="service" ref="PA_QueryPolicyListUCCHSImplgetPolicyList"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicylist.exports.iquerypolicylistucc.getpolicylist.hs.IQueryPolicyListUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypolicylist.exports.iquerypolicylistucc.getpolicylist.hs.QueryPolicyListUCCHSImpl" id="PA_QueryPolicyListUCCHSImplgetPolicyList" scope="prototype">
		<property name="ucc" ref="PA_queryPolicyListUCC">
			
		</property>
	</bean>

<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyInfoUCCgetPolicyInfoAddr">
		<property name="service" ref="PA_QueryPolicyInfoUCCHSImplgetPolicyInfo"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicyinfo.exports.iquerypolicyinfoucc.getpolicyinfo.hs.IQueryPolicyInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypolicyinfo.exports.iquerypolicyinfoucc.getpolicyinfo.hs.QueryPolicyInfoUCCHSImpl" id="PA_QueryPolicyInfoUCCHSImplgetPolicyInfo" scope="prototype">
		<property name="ucc" ref="PA_queryPolicyInfoUCC">
			
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryExistCSorCLUCCqueryExistCSorCLAddr">
		<property name="service" ref="PA_QueryExistCSorCLUCCHSImplqueryExistCSorCL"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryexistcsorcl.exports.iqueryexistcsorclucc.queryexistcsorcl.hs.IQueryExistCSorCLUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.queryexistcsorcl.exports.iqueryexistcsorclucc.queryexistcsorcl.hs.QueryExistCSorCLUCCHSImpl" id="PA_QueryExistCSorCLUCCHSImplqueryExistCSorCL" scope="prototype">
		<property name="ucc" ref="PA_queryExistCSorCLUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyHolderInfoUCCqueryPolicyHolderInfoAddr">
		<property name="service" ref="PA_QueryPolicyHolderInfoUCCHSImplqueryPolicyHolderInfo"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicyholderinfo.exports.iquerypolicyholderinfoucc.querypolicyholderinfo.hs.IQueryPolicyHolderInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypolicyholderinfo.exports.iquerypolicyholderinfoucc.querypolicyholderinfo.hs.QueryPolicyHolderInfoUCCHSImpl" id="PA_QueryPolicyHolderInfoUCCHSImplqueryPolicyHolderInfo" scope="prototype">
		<property name="ucc" ref="PA_queryPolicyHolderInfoUCC">
			
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/createPolicyUCCinsertPolicyAddr">
		<property name="service" ref="PA_CreatePolicyUCCHSImplinsertPolicy"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.hs.ICreatePolicyUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.createpolicy.exports.icreatepolicyucc.insertpolicy.hs.CreatePolicyUCCHSImpl" id="PA_CreatePolicyUCCHSImplinsertPolicy" scope="prototype">
		<property name="ucc" ref="PA_createPolicyUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/customerSurveyQueryUCCfindCustomerSurveyAddr">
		<property name="service" ref="PA_CustomerSurveyQueryUCCHSImplfindCustomerSurvey"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.customersurvey.exports.icustomersurveyqueryucc.findcustomersurvey.hs.ICustomerSurveyQueryUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->									
	<bean class="com.nci.tunan.pa.impl.customerSurvey.exports.icustomersurveyqueryucc.findcustomersurvey.hs.CustomerSurveyQueryUCCHSImpl" id="PA_CustomerSurveyQueryUCCHSImplfindCustomerSurvey" scope="prototype">
		<property name="ucc" ref="PA_customerSurveyQueryUCC">
			
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryForPrintUCCqueryForPrintAddr">
		<property name="service" ref="PA_QueryForPrintUCCHSImplqueryForPrint"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.hs.IQueryForPrintUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.queryforprint.exports.iqueryforprintucc.queryforprint.hs.QueryForPrintUCCHSImpl" id="PA_QueryForPrintUCCHSImplqueryForPrint" scope="prototype">
		<property name="ucc" ref="PA_queryForPrintUCC">
			
		</property>
	</bean>

	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/customerPolicyQueryUCCfindCustomerPolicyAddr">
		<property name="service" ref="PA_CustomerPolicyQueryUCCHSImplfindCustomerPolicy"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.customerpolicy.exports.icustomerpolicyqueryucc.findcustomerpolicy.hs.ICustomerPolicyQueryUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.customerPolicy.exports.icustomerpolicyqueryucc.findcustomerpolicy.hs.CustomerPolicyQueryUCCHSImpl" id="PA_CustomerPolicyQueryUCCHSImplfindCustomerPolicy" scope="prototype">
		<property name="ucc" ref="PA_customerPolicyQueryUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/policyAcknowledgementUpdateUCCpolicyAcknowledgementUpdateAddr">
		<property name="service" ref="PA_PolicyAcknowledgementUpdateUCCHSImplpolicyAcknowledgementUpdate"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.hs.IPolicyAcknowledgementUpdateUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.hs.PolicyAcknowledgementUpdateUCCHSImpl" id="PA_PolicyAcknowledgementUpdateUCCHSImplpolicyAcknowledgementUpdate" scope="prototype">
		<property name="ucc" ref="PA_policyAcknowledgementUpdateUCC">
			
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/renewCollectionUCCmanulBatchExtAddr">
		<property name="service" ref="PA_RenewCollectionUCCHSImplmanulBatchExt"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.renewal.exports.irenewcollectionucc.manulbatchext.hs.IRenewCollectionUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.renewal.exports.irenewcollectionucc.manulbatchext.hs.RenewCollectionUCCHSImpl" id="PA_RenewCollectionUCCHSImplmanulBatchExt" scope="prototype">
		<property name="ucc" ref="PA_renewCollectionUCC">
			
		</property>
	</bean>
	
	
 
   <!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
 
    <!-- serviceInterface属性对应着发布服务的接口-->
 
    <!-- service对应着服务发布的实现类在spring中注入的bean-->
    <bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/PA_queryPolicyReviewUCCqueryReviewerForCodeAddr"> 
        <property name="service" ref="QueryPolicyReviewUCCHSImplqueryReviewerForCode"/> 
        <property name="serviceInterface" value="com.nci.tunan.cs.interfaces.querypolicyreview.exports.iquerypolicyreviewucc.queryreviewerforcode.hs.IQueryPolicyReviewUCCHS"/> 
    </bean> 
    <!-- 注入定义接口服务实现类的bean -->
    <bean class="com.nci.tunan.cs.impl.querypolicyreview.exports.iquerypolicyreviewucc.queryreviewerforcode.hs.QueryPolicyReviewUCCHSImpl" scope="prototype" id="QueryPolicyReviewUCCHSImplqueryReviewerForCode">
        <property name="ucc"> <ref bean="PA_queryPolicyReviewUCC"/> 
        </property> 
    </bean>
	
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/createMedicalUCCinsertPolicyAddr">
		<property name="service" ref="PA_CreateMedicalUCCHSImplinsertPolicy"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.hs.ICreateMedicalUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.createmedical.exports.icreatemedicalucc.insertmedical.hs.CreateMedicalUCCHSImpl" id="PA_CreateMedicalUCCHSImplinsertPolicy" scope="prototype">
		<property name="ucc" ref="PA_createMedicalUCC">
			
		</property>
	</bean>
	
</beans>