<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:jaxws="http://cxf.apache.org/jaxws" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd   http://www.springframework.org/schema/beans         http://www.springframework.org/schema/beans/spring-beans-4.0.xsd         http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd         http://www.springframework.org/schema/context         http://www.springframework.org/schema/context/spring-context-4.0.xsd">

	

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryPriceListUccqueryPriceListAddr" id="PA_queryPriceList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000079.iquerypricelistucc.querypricelist.ws.IQueryPriceListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000079.iquerypricelistucc.querypricelist.ws.QueryPriceListUccWSImpl" id="PA_QueryPriceListUccWSImplqueryPriceList">
				<property name="ucc" ref="PA_iQueryPriceListUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->  
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryMessageInfoListUccqueryMessageListAddr" id="PA_queryMessageList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000088.iquerymessageinfolistucc.querymessagelist.ws.IQueryMessageInfoListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000088.iquerymessageinfolistucc.querymessagelist.ws.QueryMessageInfoListUccWSImpl" id="PA_QueryMessageInfoListUccWSImplqueryMessageList">
				<property name="ucc" ref="PA_iQueryMessageInfoListUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IinvestAccInfoQueryUccqueryInvestAccInfoAddr" id="PA_queryInvestAccInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000081.iinvestaccinfoqueryucc.queryinvestaccinfo.ws.IinvestAccInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000081.iinvestaccinfoqueryucc.queryinvestaccinfo.ws.investAccInfoQueryUccWSImpl" id="PA_investAccInfoQueryUccWSImplqueryInvestAccInfo">
				<property name="ucc" ref="PA_investAccInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IAccInfoQueryUccqueryAccInfoAddr" id="PA_queryAccInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000092.iaccinfoqueryucc.queryaccinfo.ws.IAccInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000092.iaccinfoqueryucc.queryaccinfo.ws.AccInfoQueryUccWSImpl" id="PA_AccInfoQueryUccWSImplqueryAccInfo">
				<property name="ucc" ref="PA_accInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 追加保费查询（至尊双利）  -->
	<jaxws:endpoint address="/IQueryDateMoneyListUccqueryMoneyDateListAddr" id="PA_queryMoneyDateList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000030.iquerydatemoneylistucc.querymoneydatelist.ws.IQueryDateMoneyListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000030.iquerydatemoneylistucc.querymoneydatelist.ws.QueryDateMoneyListUccWSImpl" id="PA_QueryDateMoneyListUccWSImplqueryMoneyDateList">
				<property name="ucc" ref="PA_queryDateMoneyListUCCImpl">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 万能险账户价值结算查询  -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryAccountCostUccqueryAccountListAddr" id="PA_queryAccountList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000032.iqueryaccountcostucc.queryaccountlist.ws.IQueryAccountCostUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000032.iqueryaccountcostucc.queryaccountlist.ws.QueryAccountCostUccWSImpl" id="PA_QueryAccountCostUccWSImplqueryAccountList">
				<property name="ucc" ref="PA_IQueryAccountCostUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 累计升息基本信息查询 -->	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryBaseMessageUccqueryBaseListAddr" id="PA_queryBaseList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000093.iquerybasemessageucc.querybaselist.ws.IQueryBaseMessageUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000093.iquerybasemessageucc.querybaselist.ws.QueryBaseMessageUccWSImpl" id="PA_QueryBaseMessageUccWSImplqueryBaseList">
				<property name="ucc" ref="PA_IQueryBaseMessageUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 交易密码查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryDealListUccqueryDealPassWordAddr" id="PA_queryDealPassWord" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000694.iquerydeallistucc.querydealpassword.ws.IQueryDealListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000694.iquerydeallistucc.querydealpassword.ws.QueryDealListUccWSImpl" id="PA_QueryDealListUccWSImplqueryDealPassWord">
				<property name="ucc" ref="PA_IQueryDealListUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 人员新增 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IAgentPersonAddUccqueryAgentPersonAddr" id="PA_queryAgentPerson" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000604.iagentpersonadducc.queryagentperson.ws.IAgentPersonAddUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000604.iagentpersonadducc.queryagentperson.ws.AgentPersonAddUccWSImpl" id="PA_AgentPersonAddUccWSImplqueryAgentPerson">
				<property name="ucc" ref="PA_IAgentPersonAddUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 网点 服务人员变化-->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/INetServePersonUccqueryNetPersonAddr" id="PA_queryNetPerson" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000230.inetservepersonucc.querynetperson.ws.INetServePersonUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000230.inetservepersonucc.querynetperson.ws.NetServePersonUccWSImpl" id="PA_NetServePersonUccWSImplqueryNetPerson">
				<property name="ucc" ref="PA_INetServePersonUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 尊享人生险种可选责任信息 -->	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/ISelectableDutyInfoQuery652UccQuerySelectTableAddr" id="PA_QuerySelectTable" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000098.iselectabledutyinfoquery652ucc.queryselecttable.ws.ISelectableDutyInfoQuery652UccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000098.iselectabledutyinfoquery652ucc.queryselecttable.ws.SelectableDutyInfoQuery652UccWSImpl" id="PA_SelectableDutyInfoQuery652UccWSImplQuerySelectTable">
				<property name="ucc" ref="PA_ISelectableDutyInfoQuery652Ucc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- E事历-保单提醒功能查询 -->	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IPolicyRemindQueryUccpolicyRemindListAddr" id="PA_policyRemindList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000899.ipolicyremindqueryucc.policyremindlist.ws.IPolicyRemindQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000899.ipolicyremindqueryucc.policyremindlist.ws.PolicyRemindQueryUccWSImpl" id="PA_PolicyRemindQueryUccWSImplpolicyRemindList">
				<property name="ucc" ref="PA_IPolicyRemindQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

		<!-- 保单续期信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IRenewInfoQueryUccqueryRenewInfoAddr" id="PA_queryRenewInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000043.irenewinfoqueryucc.queryrenewinfo.ws.IRenewInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000043.irenewinfoqueryucc.queryrenewinfo.ws.RenewInfoQueryUccWSImpl" id="PA_RenewInfoQueryUccWSImplqueryRenewInfo">
				<property name="ucc" ref="PA_renewInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单受益人信息查询 -->
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IBeneInfoQueryUccqueryBeneInfoAddr" id="PA_queryBeneInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000015.ibeneinfoqueryucc.querybeneinfo.ws.IBeneInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000015.ibeneinfoqueryucc.querybeneinfo.ws.BeneInfoQueryUccWSImpl" id="PA_BeneInfoQueryUccWSImplqueryBeneInfo">
				<property name="ucc" ref="PA_beneInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>


	<!-- 查询保单被保人接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/ICustomerMsgQueryUccqueryCustomerMsgAddr" id="PA_queryCustomerMsg" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000041.icustomermsgqueryucc.querycustomermsg.ws.ICustomerMsgQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000041.icustomermsgqueryucc.querycustomermsg.ws.CustomerMsgQueryUccWSImpl" id="PA_CustomerMsgQueryUccWSImplqueryCustomerMsg">
				<property name="ucc" ref="PA_ICustomerMsgQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IAccHisInfoQueryUccqueryAccHisInfoAddr" id="PA_queryAccHisInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000094.iacchisinfoqueryucc.queryacchisinfo.ws.IAccHisInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000094.iacchisinfoqueryucc.queryacchisinfo.ws.AccHisInfoQueryUccWSImpl" id="PA_AccHisInfoQueryUccWSImplqueryAccHisInfo">
				<property name="ucc" ref="PA_IAccHisInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/addPremQueryUccqueryAddPremAddr" id="PA_queryAddPrem" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000988.iaddpremqueryucc.queryaddprem.ws.IAddPremQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000988.iaddpremqueryucc.queryaddprem.ws.AddPremQueryUccWSImpl" id="PA_AddPremQueryUccWSImplqueryAddPrem">
				<property name="ucc" ref="PA_addPremQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/comDetailQueryUccqueryComDetailAddr" id="PA_queryComDetail" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000836.icomdetailqueryucc.querycomdetail.ws.IComDetailQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000836.icomdetailqueryucc.querycomdetail.ws.ComDetailQueryUccWSImpl" id="PA_ComDetailQueryUccWSImplqueryComDetail">
				<property name="ucc" ref="PA_comDetailQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/webLPEdorGetNoticeQueryUccqueryWebLPEdorGetNoticeAddr" id="PA_queryWebLPEdorGetNotice" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000103.iweblpedorgetnoticequeryucc.queryweblpedorgetnotice.ws.IWebLPEdorGetNoticeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000103.iweblpedorgetnoticequeryucc.queryweblpedorgetnotice.ws.WebLPEdorGetNoticeQueryUccWSImpl" id="PA_WebLPEdorGetNoticeQueryUccWSImplqueryWebLPEdorGetNotice">
				<property name="ucc" ref="PA_webLPEdorGetNoticeQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/organGrantInsertUccinsertOrganGrantAddr" id="PA_insertOrganGrant" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000233.iorgangrantinsertucc.insertorgangrant.ws.IOrganGrantInsertUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000233.iorgangrantinsertucc.insertorgangrant.ws.OrganGrantInsertUccWSImpl" id="PA_OrganGrantInsertUccWSImplinsertOrganGrant">
				<property name="ucc" ref="PA_organGrantInsertUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/organAndStartBusiUpdateUccupdateOrganAndStartBusiAddr" id="PA_updateOrganAndStartBusi" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000624.iorganandstartbusiupdateucc.updateorganandstartbusi.ws.IOrganAndStartBusiUpdateUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000624.iorganandstartbusiupdateucc.updateorganandstartbusi.ws.OrganAndStartBusiUpdateUccWSImpl" id="PA_OrganAndStartBusiUpdateUccWSImplupdateOrganAndStartBusi">
				<property name="ucc" ref="PA_organAndStartBusiUpdateUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/custBirthDayQueryUccqueryCustBirthDayAddr" id="PA_queryCustBirthDay" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000150.icustbirthdayqueryucc.querycustbirthday.ws.ICustBirthDayQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000150.icustbirthdayqueryucc.querycustbirthday.ws.CustBirthDayQueryUccWSImpl" id="PA_CustBirthDayQueryUccWSImplqueryCustBirthDay">
				<property name="ucc" ref="PA_custBirthDayQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/qualifInInsertUccinsertQualifInAddr" id="PA_insertQualifIn" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000847.iqualifininsertucc.insertqualifin.ws.IQualifInInsertUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000847.iqualifininsertucc.insertqualifin.ws.QualifInInsertUccWSImpl" id="PA_QualifInInsertUccWSImplinsertQualifIn">
				<property name="ucc" ref="PA_qualifInInsertUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!--出险人保单查询 接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryInsuredPolicyListUCCqueryInsuredPolicyListAddr" id="PA_queryInsuredPolicyList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000061.iinsuredpolicyquerylistucc.queryinsuredpolicylist.ws.IInsuredPolicyQueryListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000061.iinsuredpolicyquerylistucc.queryinsuredpolicylist.ws.InsuredPolicyQueryListUccWSImpl" id="PA_InsuredPolicyQueryListUccWSImplqueryInsuredPolicyList">
				<property name="ucc" ref="PA_queryInsuredPolicyListUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 被保险人职业类别查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IGetInsuredOccQueryUccqueryInsuredGetAddr" id="PA_queryInsuredGet" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000910.igetinsuredoccqueryucc.queryinsuredget.ws.IGetInsuredOccQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000910.igetinsuredoccqueryucc.queryinsuredget.ws.GetInsuredOccQueryUccWSImpl" id="PA_GetInsuredOccQueryUccWSImplqueryInsuredGet">
				<property name="ucc" ref="PA_IGetInsuredOccQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	
<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IHealthImpartQueryUccqueryHealthImpartAddr" id="PA_queryHealthImpart" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000068.ihealthimpartqueryucc.queryhealthimpart.ws.IHealthImpartQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000068.ihealthimpartqueryucc.queryhealthimpart.ws.HealthImpartQueryUccWSImpl" id="PA_HealthImpartQueryUccWSImplqueryHealthImpart">
				<property name="ucc" ref="PA_healthImpartQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	  	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/ILiveGetQueryUccqueryLiveGetAddr" id="PA_queryLiveGet" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000606.ilivegetqueryucc.queryliveget.ws.ILiveGetQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000606.ilivegetqueryucc.queryliveget.ws.LiveGetQueryUccWSImpl" id="PA_LiveGetQueryUccWSImplqueryLiveGet">
				<property name="ucc" ref="PA_liveGetQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--团队修改及开停业接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/updateSalesOrganUCCupdateSalesOrganAddr" id="PA_updateSalesOrgan" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000586.isalesorganupdateucc.updatesalesorgan.ws.ISalesOrganUpdateUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000586.isalesorganupdateucc.updatesalesorgan.ws.SalesOrganUpdateUccWSImpl" id="PA_SalesOrganUpdateUccWSImplupdateSalesOrgan">
				<property name="ucc" ref="PA_updateSalesOrganUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>


	<!-- 团队新增服务 -->
    <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IAddNewTeamUccaddNewTeamAddr" id="PA_addNewTeam" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000585.iaddnewteamucc.addnewteam.ws.IAddNewTeamUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000585.iaddnewteamucc.addnewteam.ws.AddNewTeamUccWSImpl" id="PA_AddNewTeamUccWSImpladdNewTeam">
				<property name="ucc" ref="PA_IAddNewTeamUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
		
	<!--客户账号领取查询  -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IPolicyAccountReceiveUccqueryPolicyAccountReceiveAddr" id="PA_queryPolicyAccountReceive" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000654.ipolicyaccountreceiveucc.querypolicyaccountreceive.ws.IPolicyAccountReceiveUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000654.ipolicyaccountreceiveucc.querypolicyaccountreceive.ws.PolicyAccountReceiveUccWSImpl" id="PA_PolicyAccountReceiveUccWSImplqueryPolicyAccountReceive">
				<property name="ucc" ref="PA_IPolicyAccountReceiveUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	


	<!--保单生存领取信息查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPayDueListUCCqueryPayDueListAddr" id="PA_queryPayDueList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000022.ipayduequerylistucc.querypayduelist.ws.IPayDueQueryListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000022.ipayduequerylistucc.querypayduelist.ws.PayDueQueryListUccWSImpl" id="PA_PayDueQueryListUccWSImplqueryPayDueList">
				<property name="ucc" ref="PA_queryPayDueListUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>	

	<!--投连保险账户信息查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryContractInvestListUCCqueryContractInvestListAddr" id="PA_queryContractInvestList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000082.icontractinvestquerylistucc.querycontractinvestlist.ws.IContractInvestQueryListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000082.icontractinvestquerylistucc.querycontractinvestlist.ws.ContractInvestQueryListUccWSImpl" id="PA_ContractInvestQueryListUccWSImplqueryContractInvestList">
				<property name="ucc" ref="PA_queryContractInvestListUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/agentCancleUCCagentCancleAddr" id="PA_agentCancle" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000599.iagentcancleucc.agentcancle.ws.IAgentCancleUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000599.iagentcancleucc.agentcancle.ws.AgentCancleUCCWSImpl" id="PA_AgentCancleUCCWSImplagentCancle">
				<property name="ucc" ref="PA_agentCancleUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	

	<!-- 生存领取历史划款信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IPayDueHistoryTransferQueryUccqueryPayDueHistoryTransferListAddr" id="PA_queryPayDueHistoryTransferList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000028.ipayduehistorytransferqueryucc.querypayduehistorytransferlist.ws.IPayDueHistoryTransferQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000028.ipayduehistorytransferqueryucc.querypayduehistorytransferlist.ws.PayDueHistoryTransferQueryUccWSImpl" id="PA_PayDueHistoryTransferQueryUccWSImplqueryPayDueHistoryTransferList">
				<property name="ucc" ref="PA_IPayDueHistoryTransferQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	
	<!-- 投连保单信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IInvestPolicyInfoQueryUccqueryInvestPolicyInfoAddr" id="PA_queryInvestPolicyInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000175.iinvestpolicyinfoqueryucc.queryinvestpolicyinfo.ws.IInvestPolicyInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000175.iinvestpolicyinfoqueryucc.queryinvestpolicyinfo.ws.InvestPolicyInfoQueryUccWSImpl" id="PA_InvestPolicyInfoQueryUccWSImplqueryInvestPolicyInfo">
				<property name="ucc" ref="PA_IInvestPolicyInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	


	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyCashValueListUCCQueryPolicyCashValueListAddr" id="PA_QueryPolicyCashValueList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000049.iquerypolicycashvaluelistucc.querypolicycashvaluelist.ws.IQueryPolicyCashValueListUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000049.iquerypolicycashvaluelistucc.querypolicycashvaluelist.ws.QueryPolicyCashValueListUCCWSImpl" id="PA_QueryPolicyCashValueListUCCWSImplQueryPolicyCashValueList">
				<property name="ucc" ref="PA_queryPolicyCashValueListUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	

	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IBonusInfoQueryCountUCCqueryBonusInfoAddr" id="PA_queryBonusInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000007.ibonusinfoquerycountucc.querybonusinfo.ws.IBonusInfoQueryCountUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000007.ibonusinfoquerycountucc.querybonusinfo.ws.BonusInfoQueryCountUCCWSImpl" id="PA_BonusInfoQueryCountUCCWSImplqueryBonusInfo">
				<property name="ucc" ref="PA_IBonusInfoQueryCountUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!--续期交费查询与校检接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPremArapListUCCqueryPremArapListAddr" id="PA_queryPremArapList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000684.ipremarapquerylistucc.querypremaraplist.ws.IPremArapQueryListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000684.ipremarapquerylistucc.querypremaraplist.ws.PremArapQueryListUccWSImpl" id="PA_PremArapQueryListUccWSImplqueryPremArapList">
				<property name="ucc" ref="PA_queryPremArapListUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--保单投保人信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryPolicyHolderUCCqueryPolicyHolderInfoAddr" id="PA_queryPolicyHolderInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000009.iquerypolicyholderucc.querypolicyholderinfo.ws.IQueryPolicyHolderUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000009.iquerypolicyholderucc.querypolicyholderinfo.ws.QueryPolicyHolderUCCWSImpl" id="PA_QueryPolicyHolderUCCWSImplqueryPolicyHolderInfo">
				<property name="ucc" ref="PA_IQueryPolicyHolderUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--投连保单状态报告查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryFundTransHisUCCqueryFundTransHisAddr" id="PA_queryFundTransHis" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000109.iqueryfundtranshisucc.queryfundtranshis.ws.IQueryFundTransHisUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000109.iqueryfundtranshisucc.queryfundtranshis.ws.QueryFundTransHisUCCWSImpl" id="PA_QueryFundTransHisUCCWSImplqueryFundTransHis">
				<property name="ucc" ref="PA_IQueryFundTransHisUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	

   <!-- i添财投资连结接口(产品) -->
   <jaxws:endpoint address="/iQueryRenturnRateUCCqueryRenturnRateAddr" id="PA_queryRenturnRate" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000986.iqueryrenturnrateucc.queryrenturnrate.ws.IQueryRenturnRateUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000986.iqueryrenturnrateucc.queryrenturnrate.ws.QueryRenturnRateUCCWSImpl" id="PA_QueryRenturnRateUCCWSImplqueryRenturnRate">
				<property name="ucc" ref="PA_iQueryRenturnRateUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!--万能险新契约账户价值查询-->
	<jaxws:endpoint address="/IUniversalNBAcountQueryUccqueryUniversalNBAcountAddr" id="PA_queryUniversalNBAcount" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000029.iuniversalnbacountqueryucc.queryuniversalnbacount.ws.IUniversalNBAcountQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000029.iuniversalnbacountqueryucc.queryuniversalnbacount.ws.UniversalNBAcountQueryUccWSImpl" id="PA_UniversalNBAcountQueryUccWSImplqueryUniversalNBAcount">
				<property name="ucc" ref="PA_IUniversalNBAcountQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 投连账户结算历史信息查询 -->
	<jaxws:endpoint address="/IInvestAccountHistoryQueryUccqueryInvestAccountHistoryAddr" id="PA_queryInvestAccountHistory" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000083.iinvestaccounthistoryqueryucc.queryinvestaccounthistory.ws.IInvestAccountHistoryQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000083.iinvestaccounthistoryqueryucc.queryinvestaccounthistory.ws.InvestAccountHistoryQueryUccWSImpl" id="PA_InvestAccountHistoryQueryUccWSImplqueryInvestAccountHistory">
				<property name="ucc" ref="PA_IInvestAccountHistoryQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IReceiveInfoSummaryQueryUccqueryReceiveInfoSummaryAddr" id="PA_queryReceiveInfoSummary" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000686.ireceiveinfosummaryqueryucc.queryreceiveinfosummary.ws.IReceiveInfoSummaryQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000686.ireceiveinfosummaryqueryucc.queryreceiveinfosummary.ws.ReceiveInfoSummaryQueryUccWSImpl" id="PA_ReceiveInfoSummaryQueryUccWSImplqueryReceiveInfoSummary">
				<property name="ucc" ref="PA_IReceiveInfoSummaryQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单密码校验 -->	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IPwValidUccvalidPasswordAddr" id="PA_validPassword" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00*********.ipwvaliducc.validpassword.ws.IPwValidUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00*********.ipwvaliducc.validpassword.ws.PwValidUccWSImpl" id="PA_PwValidUccWSImplvalidPassword">
				<property name="ucc" ref="PA_IPwValidUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单信息查询接（万能险账户信息） -->
	<jaxws:endpoint address="/contUniversalAccountQueryUccqueryContUniversalAccountAddr" id="PA_queryContUniversalAccount" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001127.icontuniversalaccountqueryucc.querycontuniversalaccount.ws.IContUniversalAccountQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001127.icontuniversalaccountqueryucc.querycontuniversalaccount.ws.ContUniversalAccountQueryUccWSImpl" id="PA_ContUniversalAccountQueryUccWSImplqueryContUniversalAccount">
				<property name="ucc" ref="PA_contUniversalAccountQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!--保单受益人信息查询接口-->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IBenefitInfoQueryUccqueryBenefitListAddr" id="PA_queryBenefitList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001398.ibenefitinfoqueryucc.querybenefitlist.ws.IBenefitInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001398.ibenefitinfoqueryucc.querybenefitlist.ws.BenefitInfoQueryUccWSImpl" id="PA_BenefitInfoQueryUccWSImplqueryBenefitList">
				<property name="ucc" ref="PA_IBenefitInfoQueryUcc">
					
				</property>
			</bean> 
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--投保人和被保人信息变更  -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IInformationModifyUccinformationModifyAddr" id="PA_informationModify" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000053.interfaces.informationmodifyuccimpl.informationmodify.ws.InformationModifyUccImplWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000053.impl.informationmodifyuccimpl.informationmodify.ws.nformationModifyUccImplWSImpl" id="PA_nformationModifyUccImplWSImplinformationModify">
				<property name="ucc" ref="PA_IInformationModifyUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单查询接口(查询保单详细内容 )-->
	<jaxws:endpoint address="/iviewPolicyInfoUccviewPolicyInfoAddr" id="PA_viewPolicyInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000550.iviewpolicyinfoucc.viewpolicyinfo.ws.IviewPolicyInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000550.iviewpolicyinfoucc.viewpolicyinfo.ws.viewPolicyInfoUccWSImpl" id="PA_viewPolicyInfoUccWSImplviewPolicyInfo">
				<property name="ucc" ref="PA_iviewPolicyInfoUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	

		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IBonusHisInfoQueryUccqueryUccBonusHisInfoAddr" id="PA_queryUccBonusHisInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000020.ibonushisinfoqueryucc.queryuccbonushisinfo.ws.IBonusHisInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000020.ibonushisinfoqueryucc.queryuccbonushisinfo.ws.BonusHisInfoQueryUccWSImpl" id="PA_BonusHisInfoQueryUccWSImplqueryUccBonusHisInfo">
				<property name="ucc" ref="PA_bonusHisInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>


	<!--查询保单投保人信息-->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyHolderBasicInfoUCCqueryPolicyHolderBasicInfoAddr" id="PA_queryPolicyHolderBasicInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001394.iquerypolicyholderbasicinfoucc.querypolicyholderbasicinfo.ws.IQueryPolicyHolderBasicInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001394.iquerypolicyholderbasicinfoucc.querypolicyholderbasicinfo.ws.QueryPolicyHolderBasicInfoUCCWSImpl" id="PA_QueryPolicyHolderBasicInfoUCCWSImplqueryPolicyHolderBasicInfo">
				<property name="ucc" ref="PA_queryPolicyHolderBasicInfoUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 万能险实时账户查询接口 是定义服务的接口类 -->
	<jaxws:endpoint address="/iQueryUniversalAccountUccqueryUniversalAccountAddr" id="PA_queryUniversalAccount" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001406.iqueryuniversalaccountucc.queryuniversalaccount.ws.IQueryUniversalAccountUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001406.iqueryuniversalaccountucc.queryuniversalaccount.ws.QueryUniversalAccountUccWSImpl" id="PA_QueryUniversalAccountUccWSImplqueryUniversalAccount">
				<property name="ucc" ref="PA_iQueryUniversalAccountUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单险种责任信息查询接口 -->
	<jaxws:endpoint address="/policyBusiDutyUccqueryPolicyBusiDutyInfoAddr" id="PA_queryPolicyBusiDutyInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001402.ipolicybusidutyucc.querypolicybusidutyinfo.ws.IPolicyBusiDutyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001402.ipolicybusidutyucc.querypolicybusidutyinfo.ws.PolicyBusiDutyUccWSImpl" id="PA_PolicyBusiDutyUccWSImplqueryPolicyBusiDutyInfo">
				<property name="ucc" ref="PA_policyBusiDutyUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryInsuredMessageUccqueryInsuredMessageAddr" id="PA_queryInsuredMessage" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001396.iqueryinsuredmessageucc.queryinsuredmessage.ws.IQueryInsuredMessageUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001396.iqueryinsuredmessageucc.queryinsuredmessage.ws.QueryInsuredMessageUccWSImpl" id="PA_QueryInsuredMessageUccWSImplqueryInsuredMessage">
				<property name="ucc" ref="PA_queryInsuredMessageUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 保单险种信息查询接口  -->	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IPolResikMessageUccresikMessageAddr" id="PA_resikMessage" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001400.ipolresikmessageucc.resikmessage.ws.IPolResikMessageUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001400.ipolresikmessageucc.resikmessage.ws.PolResikMessageUccWSImpl" id="PA_PolResikMessageUccWSImplresikMessage">
				<property name="ucc" ref="PA_IPolResikMessageUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单基本信息查询 -->
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->

	<jaxws:endpoint address="/IBusinessCentreUccqueryBusinessCentreAddr" id="PA_queryBusinessCentre" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001392.ibusinesscentrequeryucc.querybusinesscentrelist.ws.IBusinessCentreQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001392.ibusinesscentrequeryucc.querybusinesscentrelist.ws.BusinessCentreQueryUccWSImpl" id="PA_BusinessCentreQueryUccWSImplqueryBusinessCentreList">
				<property name="ucc" ref="PA_businessCentreQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 续期缴费账号查询接口 -->
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->

	<jaxws:endpoint address="/renewalPaymentIDQueryUccqueryRenewalPaymentIDListAddr" id="PA_queryRenewalPaymentIDList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000838.irenewalpaymentidqueryucc.queryrenewalpaymentidlist.ws.IRenewalPaymentIDQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000838.irenewalpaymentidqueryucc.queryrenewalpaymentidlist.ws.RenewalPaymentIDQueryUccWSImpl" id="PA_RenewalPaymentIDQueryUccWSImplqueryRenewalPaymentIDList">
				<property name="ucc" ref="PA_renewalPaymentIDQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单列表查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
 	<jaxws:endpoint address="/IContractMasterQueryListUccqueryContractMasterListAddr" id="PA_queryContractMasterList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001390.icontractmasterquerylistucc.querycontractmasterlist.ws.IContractMasterQueryListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001390.icontractmasterquerylistucc.querycontractmasterlist.ws.ContractMasterQueryListUccWSImpl" id="PA_ContractMasterQueryListUccWSImplqueryContractMasterList">
				<property name="ucc" ref="PA_IContractMasterQueryListUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/balanceQueryUccqueryBalanceAddr" id="PA_queryBalance" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000857.ibalancequeryucc.querybalance.ws.IBalanceQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000857.ibalancequeryucc.querybalance.ws.BalanceQueryUccWSImpl" id="PA_BalanceQueryUccWSImplqueryBalance">
				<property name="ucc" ref="PA_balanceQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 个人保单信息查询 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPersonalPolicyDetailUccqueryPersonalPolicyAddr" id="PA_queryPersonalPolicy" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000011.iquerypersonalpolicydetailucc.querypersonalpolicy.ws.IQueryPersonalPolicyDetailUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000011.iquerypersonalpolicydetailucc.querypersonalpolicy.ws.QueryPersonalPolicyDetailUCCWSImpl" id="PA_QueryPersonalPolicyDetailUCCWSImplqueryPersonalPolicy">
				<property name="ucc" ref="PA_queryPersonalPolicyDetailUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--查询年度分红业绩报告书-->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryYearBonusReportUCCqueryYearBonusReportAddr" id="PA_queryYearBonusReport" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000151.iqueryyearbonusreportucc.queryyearbonusreport.ws.IQueryYearBonusReportUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000151.iqueryyearbonusreportucc.queryyearbonusreport.ws.QueryYearBonusReportUCCWSImpl" id="PA_QueryYearBonusReportUCCWSImplqueryYearBonusReport">
				<property name="ucc" ref="PA_queryYearBonusReportUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--查询年度分红业绩报告书_现金分红接口-->
	<jaxws:endpoint address="/queryYearBonusReportUCCqueryYearBonusReportForCashAddr" id="PA_queryYearBonusReportForCash" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002806.iqueryyearbonusreportucc.queryyearbonusreport.ws.IQueryYearBonusReportUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101002806.iqueryyearbonusreportucc.queryyearbonusreport.ws.QueryYearBonusReportUCCWSImpl" id="PA_QueryYearBonusReportUCCWSImplqueryYearBonusReportForCash">
				<property name="ucc" ref="PA_queryYearBonusReportForCashUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IApsEdorUccitExitApsAddr" id="PA_itExitAps" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000863.iapsedorucc.itexitaps.ws.IApsEdorUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000863.iapsedorucc.itexitaps.ws.ApsEdorUccWSImpl" id="PA_ApsEdorUccWSImplitExitAps">
				<property name="ucc" ref="PA_IApsEdorUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单到期领取提醒  是定义服务的接口类 -->
	<jaxws:endpoint address="/policyDueToReceiveReminderUccpolicyDueToReceiveReminderAddr" id="PA_policyDueToReceiveReminder" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000144.ipolicyduetoreceivereminderucc.policyduetoreceivereminder.ws.IPolicyDueToReceiveReminderUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000144.ipolicyduetoreceivereminderucc.policyduetoreceivereminder.ws.PolicyDueToReceiveReminderUccWSImpl" id="PA_PolicyDueToReceiveReminderUccWSImplpolicyDueToReceiveReminder">
				<property name="ucc" ref="PA_policyDueToReceiveReminderUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- R00101000142生存领取(给付)信息查询 -->
   <jaxws:endpoint address="/iPayDueQueryInfoUccqueryPayDueInfoAddr" id="PA_queryPayDueInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000142.ipayduequeryinfoucc.querypaydueinfo.ws.IPayDueQueryInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000142.ipayduequeryinfoucc.querypaydueinfo.ws.PayDueQueryInfoUccWSImpl" id="PA_PayDueQueryInfoUccWSImplqueryPayDueInfo">
				<property name="ucc" ref="PA_iPayDueQueryInfoUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/renewalMessageQueryUccrenewalMessageQueryAddr" id="PA_renewalMessageQuery" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000184.irenewalmessagequeryucc.renewalmessagequery.ws.IRenewalMessageQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000184.irenewalmessagequeryucc.renewalmessagequery.ws.RenewalMessageQueryUccWSImpl" id="PA_RenewalMessageQueryUccWSImplrenewalMessageQuery">
				<property name="ucc" ref="PA_renewalMessageQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/noteInfoQueryUccnoteInfoQueryAddr" id="PA_noteInfoQuery" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401001853.inoteinfoqueryucc.noteinfoquery.ws.INoteInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r06401001853.inoteinfoqueryucc.noteinfoquery.ws.NoteInfoQueryUccWSImpl" id="PA_NoteInfoQueryUccWSImplnoteInfoQuery">
				<property name="ucc" ref="PA_noteInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/judgePolicyExistUCCjudegPolicyExistAddr" id="PA_judegPolicyExist" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.judgepolicyexist.ijudgepolicyexistucc.judegpolicyexist.ws.IjudgePolicyExistUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.judgepolicyexist.ijudgepolicyexistucc.judegpolicyexist.ws.judgePolicyExistUccWSImpl" id="PA_judgePolicyExistUccWSImpljudegPolicyExist">
				<property name="ucc" ref="PA_judgePolicyExistUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryCustomerHealthInfoUCCqueryCustomerHealthInfoAddr" id="PA_queryCustomerHealthInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401001856.iquerycustomerhealthinfoucc.querycustomerhealthinfo.ws.IQueryCustomerHealthInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r06401001856.iquerycustomerhealthinfoucc.querycustomerhealthinfo.ws.QueryCustomerHealthInfoUCCWSImpl" id="PA_QueryCustomerHealthInfoUCCWSImplqueryCustomerHealthInfo">
				<property name="ucc" ref="PA_queryCustomerHealthInfoUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyConditionUCCqueryPolicyConditionAddr" id="PA_queryPolicyCondition" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401001861.iquerypolicyconditionucc.querypolicycondition.ws.IQueryPolicyConditionUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r06401001861.iquerypolicyconditionucc.querypolicycondition.ws.QueryPolicyConditionUCCWSImpl" id="PA_QueryPolicyConditionUCCWSImplqueryPolicyCondition">
				<property name="ucc" ref="PA_queryPolicyConditionUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/zbBenefitInfoQueryUcczbBenefitInfoQueryAddr" id="PA_zbBenefitInfoQuery" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401001871.izbbenefitinfoqueryucc.zbbenefitinfoquery.ws.IZBBenefitInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r06401001871.izbbenefitinfoqueryucc.zbbenefitinfoquery.ws.ZBBenefitInfoQueryUccWSImpl" id="PA_ZBBenefitInfoQueryUccWSImplzbBenefitInfoQuery">
				<property name="ucc" ref="PA_zbBenefitInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 各保单健康告知查询（新核心） 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyCustomerHealthImpartUccqueryPolicyCustomerHealthImpartAddr" id="PA_queryPolicyCustomerHealthImpart" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401001887.iquerypolicycustomerhealthimpartucc.querypolicycustomerhealthimpart.ws.IQueryPolicyCustomerHealthImpartUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r06401001887.iquerypolicycustomerhealthimpartucc.querypolicycustomerhealthimpart.ws.QueryPolicyCustomerHealthImpartUccWSImpl" id="PA_QueryPolicyCustomerHealthImpartUccWSImplqueryPolicyCustomerHealthImpart">
				<property name="ucc" ref="PA_queryPolicyCustomerHealthImpartUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单挂起状态修改(理赔保单挂起接口) 是定义服务的接口类 -->
	<jaxws:endpoint address="/lockCSAndREForCLMUcclockCSAndREForCLMAddr" id="PA_lockCSAndREForCLM" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000090.ilockcsandreforclmucc.lockcsandreforclm.ws.ILockCSAndREForCLMUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000090.ilockcsandreforclmucc.lockcsandreforclm.ws.LockCSAndREForCLMUccWSImpl" id="PA_LockCSAndREForCLMUccWSImpllockCSAndREForCLM">
				<property name="ucc" ref="PA_lockCSAndREForCLMUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 短期意外险被保人信息查询 -->
	<jaxws:endpoint address="/queryInsuredInfoByPolicyCodeUccqueryInsuredInfoByPolicyCodeAddr" id="PA_queryInsuredInfoByPolicyCode" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r04301000337.iqueryinsuredinfobypolicycodeucc.queryinsuredinfobypolicycode.ws.IQueryInsuredInfoByPolicyCodeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r04301000337.iqueryinsuredinfobypolicycodeucc.queryinsuredinfobypolicycode.ws.QueryInsuredInfoByPolicyCodeUccWSImpl" id="PA_QueryInsuredInfoByPolicyCodeUccWSImplqueryInsuredInfoByPolicyCode">
				<property name="ucc" ref="PA_queryInsuredInfoByPolicyCodeUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/ISurveyResultQueryUccquerySurveyResultAddr" id="PA_querySurveyResult" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401001842.isurveyresultqueryucc.querysurveyresult.ws.ISurveyResultQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r06401001842.isurveyresultqueryucc.querysurveyresult.ws.SurveyResultQueryUccWSImpl" id="PA_SurveyResultQueryUccWSImplquerySurveyResult">
				<property name="ucc" ref="PA_surveyResultQueryUccImpl">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 万能险利率查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
<!-- 	<jaxws:endpoint address="/contractFundSettlementUccqueryFundSettlementAddr" id="PA_queryFundSettlement" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000070.icontractfundsettlementucc.queryfundsettlement.ws.IContractFundSettlementUccWS">		
		<jaxws:implementor>
			class对应服务发布接口的实现类 
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000070.icontractfundsettlementucc.queryfundsettlement.ws.ContractFundSettlementUccWSImpl" id="PA_ContractFundSettlementUccWSImplqueryFundSettlement">
				<property name="ucc" ref="PA_contractFundSettlementUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> -->
	
	<!-- 申请分红通知书补打接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/applyDividendPrintUccapplyDividendPrintAddr" id="PA_applyDividendPrint" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102001169.iapplydividendprintucc.applydividendprint.ws.IApplyDividendPrintUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102001169.iapplydividendprintucc.applydividendprint.ws.ApplyDividendPrintUccWSImpl" id="PA_ApplyDividendPrintUccWSImplapplyDividendPrint">
				<property name="ucc" ref="PA_applyDividendPrintUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 当日年化收益查询接口 -->
	<jaxws:endpoint address="/IQueryAnnualYieldInfoUccqueryAnnualYieldInfoAddr" id="PA_queryAnnualYieldInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000960.iqueryannualyieldinfoucc.queryannualyieldinfo.ws.IQueryAnnualYieldInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000960.iqueryannualyieldinfoucc.queryannualyieldinfo.ws.QueryAnnualYieldInfoUccWSImpl" id="PA_QueryAnnualYieldInfoUccWSImplqueryAnnualYieldInfo">
				<property name="ucc" ref="PA_IQueryAnnualYieldInfoUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	
	<!--交退费编号是否存在判断服务 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
<!-- 	<jaxws:endpoint address="/judgeUnitNumberExistUccjudgeUnitnumberExistAddr" id="PA_judgeUnitnumberExist" implementorClass="com.nci.tunan.pa.interfaces.judgeunitnumberExist.exports.ijudgeunitnumberexistucc.judgeunitnumberexist.ws.IJudgeUnitNumberExistUccWS">		
		<jaxws:implementor>
			class对应服务发布接口的实现类 
			<bean class="com.nci.tunan.pa.impl.judgeunitnumberExist.exports.ijudgeunitnumberexistucc.judgeunitnumberexist.ws.JudgeUnitNumberExistUccWSImpl" id="PA_JudgeUnitNumberExistUccWSImpljudgeUnitnumberExist">
				<property name="ucc" ref="PA_judgeUnitNumberExistUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> -->
	<!-- 团体个人单和个人保单特约内容查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
 	 <jaxws:endpoint address="/IQueryPaContentUccQueryPaContentAddr" id="PA_QueryPaContent" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401001775.iquerypacontentucc.querypacontent.ws.IQueryPaContentUccWS">		
		<jaxws:implementor>
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r06401001775.iquerypacontentucc.querypacontent.ws.QueryPaContentUccWSImpl" id="PA_QueryPaContentUccWSImplQueryPaContent">
				<property name="ucc" ref="PA_IQueryPaContentUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>  
	<!-- 工行纸质保单补充接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IPaperSupplementUccpaperSupplementAddr" id="PA_paperSupplement" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000984.ipapersupplementucc.papersupplement.ws.IPaperSupplementUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000984.ipapersupplementucc.papersupplement.ws.PaperSupplementUccWSImpl" id="PA_PaperSupplementUccWSImplpaperSupplement">
				<property name="ucc" ref="PA_IPaperSupplementUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	
	<!-- 客户地址信息查询 -->
	<jaxws:endpoint address="/ICustAdressQueryUcccustAdressQueryAddr" id="PA_custAdressQuery" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001167.icustaddressqueryucc.custaddressquery.ws.ICustAddressQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001167.icustaddressqueryucc.custaddressquery.ws.CustAddressQueryUccWSImpl" id="PA_CustAddressQueryUccWSImplcustAddressQuery">
				<property name="ucc" ref="PA_ICustAddressQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	

	<!-- 保单信息服务人员轨迹查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IPolicyServiceTrackQueryUccPolicyServiceTrackQueryUccAddr" id="PA_PolicyServiceTrackQueryUcc" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000787.ipolicyservicetrackqueryucc.policyservicetrackqueryucc.ws.IPolicyServiceTrackQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000787.ipolicyservicetrackqueryucc.policyservicetrackqueryucc.ws.PolicyServiceTrackQueryUccWSImpl" id="PA_PolicyServiceTrackQueryUccWSImplPolicyServiceTrackQueryUcc">
				<property name="ucc" ref="PA_IPolicyServiceTrackQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 保单红利查询接口 "该接口移至qry"-->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- <jaxws:endpoint id="queryPolicyBonus" address="/policyBonusQueryUccqueryPolicyBonusAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000869.ipolicybonusqueryucc.querypolicybonus.ws.IPolicyBonusQueryUccWS">		
		<jaxws:implementor>
			class对应服务发布接口的实现类 
			<bean id="PolicyBonusQueryUccWSImplqueryPolicyBonus" class="com.nci.tunan.pa.impl.peripheral.exports.r00101000869.ipolicybonusqueryucc.querypolicybonus.ws.PolicyBonusQueryUccWSImpl">
				<property name="ucc">
					<ref bean="policyBonusQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> -->
	
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/accountCancelQueryUccqueryAccountCancelAddr" id="PA_queryAccountCancel" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000498.iaccountcancelqueryucc.queryaccountcancel.ws.IAccountCancelQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000498.iaccountcancelqueryucc.queryaccountcancel.ws.AccountCancelQueryUccWSImpl" id="PA_AccountCancelQueryUccWSImplqueryAccountCancel">
				<property name="ucc" ref="PA_accountCancelQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单相关影像文件查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryImageFileUccqueryImageFileAddr" id="PA_queryImageFile" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000100.iqueryimagefileucc.queryimagefile.ws.IQueryImageFileUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000100.iqueryimagefileucc.queryimagefile.ws.QueryImageFileUccWSImpl" id="PA_QueryImageFileUccWSImplqueryImageFile">
				<property name="ucc" ref="PA_queryImageFileUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

    <!-- 渠道同步查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/channelSynchroQueryUccchannelSynchroQueryAddr" id="PA_channelSynchroQuery" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001183.ichannelsynchroqueryucc.channelsynchroquery.ws.IChannelSynchroQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001183.ichannelsynchroqueryucc.channelsynchroquery.ws.ChannelSynchroQueryUccWSImpl" id="PA_ChannelSynchroQueryUccWSImplchannelSynchroQuery">
				<property name="ucc" ref="PA_channelSynchroQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 管理費同步查询接口 该接口在产品子系统中实现-->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- <jaxws:endpoint id="managementCostSynchroQuery" address="/managementCostSynchroQueryUccmanagementCostSynchroQueryAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001205.imanagementcostsynchroqueryucc.managementcostsynchroquery.ws.IManagementCostSynchroQueryUccWS">		
		<jaxws:implementor>
			class对应服务发布接口的实现类 
			<bean id="ManagementCostSynchroQueryUccWSImplmanagementCostSynchroQuery" class="com.nci.tunan.pa.impl.peripheral.exports.r00101001205.imanagementcostsynchroqueryucc.managementcostsynchroquery.ws.ManagementCostSynchroQueryUccWSImpl">
				<property name="ucc">
					<ref bean="managementCostSynchroQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> -->
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/busiProdQueryUccqueryBusiProdAddr" id="PA_queryBusiProd" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000792.ibusiprodqueryucc.querybusiprod.ws.IBusiProdQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000792.ibusiprodqueryucc.querybusiprod.ws.BusiProdQueryUccWSImpl" id="PA_BusiProdQueryUccWSImplqueryBusiProd">
				<property name="ucc" ref="PA_busiProdQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 产品是否续保 -->
	<jaxws:endpoint address="/policyRenewQueryUccImplqueryPolicyRenewAddr" id="PA_queryPolicyRenew" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001019.ipolicyrenewqueryucc.querypolicyrenew.ws.IPolicyRenewQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001019.ipolicyrenewqueryucc.querypolicyrenew.ws.PolicyRenewQueryUccWSImpl" id="PA_PolicyRenewQueryUccWSImplqueryPolicyRenew">
				<property name="ucc" ref="PA_policyRenewQueryUccImpl">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryInsuAccInfoUccqueryInsuAccInfoAddr" id="PA_queryInsuAccInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001015.iqueryinsuaccinfoucc.queryinsuaccinfo.ws.IQueryInsuAccInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001015.iqueryinsuaccinfoucc.queryinsuaccinfo.ws.QueryInsuAccInfoUccWSImpl" id="PA_QueryInsuAccInfoUccWSImplqueryInsuAccInfo">
				<property name="ucc" ref="PA_queryInsuAccInfoUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 分红信息查询cjk -->
<jaxws:endpoint address="/iAllotBonusQueryUccqueryAllotBonusAddr" id="PA_queryAllotBonus" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001109.iallotbonusqueryucc.queryallotbonus.ws.IAllotBonusQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001109.iallotbonusqueryucc.queryallotbonus.ws.AllotBonusQueryUccWSImpl" id="PA_AllotBonusQueryUccWSImplqueryAllotBonus">
				<property name="ucc" ref="PA_iAllotBonusQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户保单信息查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/customerPolicyMsgQueryUcccustomerPolicyMsgQueryAddr" id="PA_customerPolicyMsgQuery" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000042.icustomerpolicymsgqueryucc.customerpolicymsgquery.ws.ICustomerPolicyMsgQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000042.icustomerpolicymsgqueryucc.customerpolicymsgquery.ws.CustomerPolicyMsgQueryUccWSImpl" id="PA_CustomerPolicyMsgQueryUccWSImplcustomerPolicyMsgQuery">
				<property name="ucc" ref="PA_customerPolicyMsgQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 客户身份信息验真开关  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/AuthenticationSwitchUccQueryAuthenticationSwitchAddr" id="PA_QueryAuthenticationSwitch" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002126.iauthenticationswitchucc.queryauthenticationswitch.ws.IAuthenticationSwitchUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101002126.iauthenticationswitchucc.queryauthenticationswitch.ws.AuthenticationSwitchUccWSImpl" id="PA_AuthenticationSwitchUccWSImplQueryAuthenticationSwitch">
				<property name="ucc" ref="PA_AuthenticationSwitchUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 电话中心保单信息查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/telephoneCenterQueryPolicyInfoUccqueryPolicyInfoAddr" id="PA_queryPolicyInfo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002246.itelephonecenterquerypolicyinfo.querypolicyinfo.ws.ITelephoneCenterQueryPolicyInfoWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101002246.itelephonecenterquerypolicyinfo.querypolicyinfo.ws.TelephoneCenterQueryPolicyInfoWSImpl" id="PA_TelephoneCenterQueryPolicyInfoWSImplqueryPolicyInfo">
				<property name="ucc" ref="PA_telephoneCenterQueryPolicyInfoUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 合作机构同步接口  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/CooperationStructureSynchronizationUccsynchronizationOperationAddr" id="PA_synchronizationOperation" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102002243.icooperationstructuresynchronizationucc.synchronizationoperation.ws.ICooperationStructureSynchronizationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102002243.icooperationstructuresynchronizationucc.synchronizationoperation.ws.CooperationStructureSynchronizationUccWSImpl" id="PA_CooperationStructureSynchronizationUccWSImplsynchronizationOperation">
				<property name="ucc" ref="PA_CooperationStructureSynchronizationUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 可选责任信息查询列表 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/optionLiabInfoQueryUccqueryOptionLiabListAddr" id="PA_queryOptionLiabList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001594.ioptionliabinfoqueryucc.queryoptionliablist.ws.IOptionLiabInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001594.ioptionliabinfoqueryucc.queryoptionliablist.ws.OptionLiabInfoQueryUccWSImpl" id="PA_OptionLiabInfoQueryUccWSImplqueryOptionLiabList">
				<property name="ucc" ref="PA_optionLiabInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 业务员重复电话甄别接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/agentRepeatPhoneIdentityUccagentRepeatPhoneIdentityAddr" id="PA_agentRepeatPhoneIdentity" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002283.iagentrepeatphoneidentityucc.agentrepeatphoneidentity.ws.IAgentRepeatPhoneIdentityUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101002283.iagentrepeatphoneidentityucc.agentrepeatphoneidentity.ws.AgentRepeatPhoneIdentityUccWSImpl" id="PA_AgentRepeatPhoneIdentityUccWSImplagentRepeatPhoneIdentity">
				<property name="ucc" ref="PA_agentRepeatPhoneIdentityUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 个人保单信息查询（MMS） -->
	<jaxws:endpoint address="/personalPolicyInfoQueryForMMSUccqueryPersonalPolicyInfoForMMSAddr" id="PA_queryPersonalPolicyInfoForMMS" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002255.ipersonalpolicyinfoqueryformmsucc.querypersonalpolicyinfoformms.ws.IPersonalPolicyInfoQueryForMMSUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101002255.ipersonalpolicyinfoqueryformmsucc.querypersonalpolicyinfoformms.ws.PersonalPolicyInfoQueryForMMSUccWSImpl" id="PA_PersonalPolicyInfoQueryForMMSUccWSImplqueryPersonalPolicyInfoForMMS">
				<property name="ucc" ref="PA_personalPolicyInfoQueryForMMSUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址 订单号快速查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/orderNoQueryUccqueryOrderNoAddr" id="PA_queryOrderNo" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.ordernoquery.iordernoqueryucc.queryorderno.ws.IOrderNoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.ordernoquery.iordernoqueryucc.queryorderno.ws.OrderNoQueryUccWSImpl" id="PA_OrderNoQueryUccWSImplqueryOrderNo">
				<property name="ucc" ref="PA_orderNoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 附加险万能险新契约账户价值查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/accountWorthQueryUccaccountWorthQueryAddr" id="PA_accountWorthQuery" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000034.iaccountworthqueryucc.accountworthquery.ws.IAccountWorthQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000034.iaccountworthqueryucc.accountworthquery.ws.AccountWorthQueryUccWSImpl" id="PA_AccountWorthQueryUccWSImplaccountWorthQuery">
				<property name="ucc" ref="PA_accountWorthQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 续期缴费提交接口 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/renewalPaymentSubmittedUccrenewalPaymentSubmittedAddr" id="PA_renewalPaymentSubmitted" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102002323.irenewalpaymentsubmitteducc.renewalpaymentsubmitted.ws.IRenewalPaymentSubmittedUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102002323.irenewalpaymentsubmitteducc.renewalpaymentsubmitted.ws.RenewalPaymentSubmittedUccWSImpl" id="PA_RenewalPaymentSubmittedUccWSImplrenewalPaymentSubmitted">
				<property name="ucc" ref="PA_renewalPaymentSubmittedUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 续期缴费支付失败回写核心接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/reneFeePayFailBackCoreUCCsolutionHangAddr" id="PA_solutionHang" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102002324.irenefeepayfailbackcoreucc.solutionhang.ws.IReneFeePayFailBackCoreUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102002324.irenefeepayfailbackcoreucc.solutionhang.ws.ReneFeePayFailBackCoreUCCWSImpl" id="PA_ReneFeePayFailBackCoreUCCWSImplsolutionHang">
				<property name="ucc" ref="PA_reneFeePayFailBackCoreUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 电话中心客户层交易密码修改接口 -->
	<jaxws:endpoint address="/telephoneCenterUpdatePasswordUccupdatePasswordAddr" id="PA_updatePassword" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r00102000543.itelephonecenterupdatepassworducc.updatepassword.ws.ITelephoneCenterUpdatePasswordUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.cs.impl.peripheral.exports.r00102000543.itelephonecenterupdatepassworducc.updatepassword.ws.TelephoneCenterUpdatePasswordUccWSImpl" id="PA_TelephoneCenterUpdatePasswordUccWSImplupdatePassword">
				<property name="ucc" ref="PA_telephoneCenterUpdatePasswordUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 贷款续贷提交 -->
	<jaxws:endpoint id="policyLoanRenewal" address="/policyLoanRenewal_cspolicyLoanRenewalAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r00102002295.ipolicyloanrenewalucc.policyloanrenewal.ws.IPolicyLoanRenewalUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyLoanRenewalUccWSImplpolicyLoanRenewal" class="com.nci.tunan.cs.impl.peripheral.exports.r00102002295.ipolicyloanrenewalucc.policyloanrenewal.ws.PolicyLoanRenewalUccWSImpl">
				<property name="ucc">
					<ref bean="policyLoanRenewal_cs" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 附加险险种信息查询接口 -->
	<jaxws:endpoint id="queryRiderProductRiskInfo" address="/riderProductRiskInfoQueryUccqueryRiderProductRiskInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001691.iriderproductriskinfoqueryucc.queryriderproductriskinfo.ws.IRiderProductRiskInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RiderProductRiskInfoQueryUccWSImplqueryRiderProductRiskInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101001691.iriderproductriskinfoqueryucc.queryriderproductriskinfo.ws.RiderProductRiskInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_riderProductRiskInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 附加险查询接口 -->
	<jaxws:endpoint id="queryAdditionRisk" address="/PA_additionRiskQueryUccqueryAdditionRiskAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002280.iadditionriskqueryucc.queryadditionrisk.ws.IAdditionRiskQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AdditionRiskQueryUccWSImplqueryAdditionRisk" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002280.iadditionriskqueryucc.queryadditionrisk.ws.AdditionRiskQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_additionRiskQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 附加险代码查询接口 -->
	<jaxws:endpoint id="queryAdditionRiskCode" address="/PA_additionRiskCodeQueryUccqueryAdditionRiskCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001716.iadditionriskcodequeryucc.queryadditionriskcode.ws.IAdditionRiskCodeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AdditionRiskCodeQueryUccWSImplqueryAdditionRiskCode" class="com.nci.tunan.pa.impl.peripheral.exports.r00101001716.iadditionriskcodequeryucc.queryadditionriskcode.ws.AdditionRiskCodeQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_additionRiskCodeQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 交易密码是否重置 -->
		<jaxws:endpoint id="queryPassWordIfReset" address="/passWordIfResetqueryPassWordIfResetAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000964.ipapasswordifresetucc.querypasswordifreset.ws.IPaPassWordIfResetUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PaPassWordIfResetUccWSImplqueryPassWordIfReset" class="com.nci.tunan.pa.impl.peripheral.exports.r00101000964.ipapasswordifresetucc.querypasswordifreset.ws.PaPassWordIfResetUccWSImpl">
				<property name="ucc">
					<ref bean="passWordIfReset" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 回执签收接口 -->
	<jaxws:endpoint id="saveSignBack" address="/iSignBackUccsaveSignBackAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001118.isignbackucc.savesignback.ws.ISignBackUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="SignBackUccWSImplsaveSignBack" class="com.nci.tunan.pa.impl.peripheral.exports.r00101001118.isignbackucc.savesignback.ws.SignBackUccWSImpl">
				<property name="ucc">
					<ref bean="iSignBackUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!--保单类型查询  -->
	  
	<jaxws:endpoint id="queryPolicyType" address="/queryPolicyTypequeryPolicyTypeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002522.ipolicytypeucc.querypolicytype.ws.IPolicyTypeUccWS">		
		<jaxws:implementor>
		
			<bean id="PolicyTypeUccWSImplqueryPolicyType" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002522.ipolicytypeucc.querypolicytype.ws.PolicyTypeUccWSImpl">
				<property name="ucc">
					<ref bean="queryPolicyTypes" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> 
	
	<!-- 电子函件服务查询接口-->
	<jaxws:endpoint id="queryIsChooseEmail" address="/PA_isChooseEmailUccqueryIsChooseEmailAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002512.iischooseemailucc.queryischooseemail.ws.IIsChooseEmailUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="IsChooseEmailUccWSImplqueryIsChooseEmail" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002512.iischooseemailucc.queryischooseemail.ws.IsChooseEmailUccWSImpl">
				<property name="ucc">
					<ref bean="PA_isChooseEmailUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 居民税收查询 -->
	<jaxws:endpoint id="queryPeopleIncome" address="/ipeopleIncomeQueryUccqueryPeopleIncomeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002547.ipeopleincomequeryucc.querypeopleincome.ws.IpeopleIncomeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="peopleIncomeQueryUccWSImplqueryPeopleIncome" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002547.ipeopleincomequeryucc.querypeopleincome.ws.peopleIncomeQueryUccWSImpl">
				<property name="ucc">
					<ref bean="ipeopleIncomeQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 红利信息查询 -->
	<jaxws:endpoint id="queryBonusInfo" address="/PA_queryBonusInfoUccImplqueryBonusInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002466.iquerybonusinfoucc.querybonusinfo.ws.IQueryBonusInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryBonusInfoUccWSImplqueryBonusInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002466.iquerybonusinfoucc.querybonusinfo.ws.QueryBonusInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryBonusInfoUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 万能险结算状态信息查询  -->
	<jaxws:endpoint id="querySettleAccountsInfo" address="/PA_settleAccountsOfOmnipotentInfoUccquerySettleAccountsInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002468.isettleaccountsofomnipotentinfoucc.querysettleaccountsinfo.ws.ISettleAccountsOfOmnipotentInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="SettleAccountsOfOmnipotentInfoUccWSImplquerySettleAccountsInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002468.isettleaccountsofomnipotentinfoucc.querysettleaccountsinfo.ws.SettleAccountsOfOmnipotentInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_settleAccountsOfOmnipotentInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--by zhaoyoan_wb 万能险账户当前信息查询 -->
	<jaxws:endpoint id="queryUniversalAccountInfo" address="/universalAccountInfoQueryUccqueryUniversalAccountInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000031.iuniversalaccountinfoqueryucc.queryuniversalaccountinfo.ws.IUniversalAccountInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UniversalAccountInfoQueryUccWSImplqueryUniversalAccountInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101000031.iuniversalaccountinfoqueryucc.queryuniversalaccountinfo.ws.UniversalAccountInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_universalAccountInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 红利信息查询接口 -->
	<jaxws:endpoint id="queryDividendInfo" address="/PA_dividendInformationQueryUccqueryDividendInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002568.idividendinformationqueryucc.querydividendinfo.ws.IDividendInformationQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="DividendInformationQueryUccWSImplqueryDividendInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002568.idividendinformationqueryucc.querydividendinfo.ws.DividendInformationQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_dividendInformationQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 查询客户保单(建行) -->
	<jaxws:endpoint id="queryCustomerPolicy" address="/PA_customerPolicyUCCqueryCustomerPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.icustomerpolicyucc.querycustomerpolicy.ws.ICustomerPolicyUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustomerPolicyUCCWSImplqueryCustomerPolicy" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.icustomerpolicyucc.querycustomerpolicy.ws.CustomerPolicyUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_customerPolicyUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 变更数据回传(中信) -->
	<jaxws:endpoint id="changeQuery" address="/PA_citicBankDataUCCchangeQueryAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iciticbankdataucc.changequery.ws.ICiticBankDataUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CiticBankDataUCCWSImplchangeQuery" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iciticbankdataucc.changeQuery.ws.CiticBankDataUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_citicBankDataUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 农行保全日终文件状态同步需求 -->
	<jaxws:endpoint id="policyStatusSyn" address="/PA_policyStatusSynForAbcUCCpolicyStatusSynAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicystatussynforabcucc.policystatussyn.ws.IPolicyStatusSynForAbcUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyStatusSynForAbcUCCWSImplpolicyStatusSyn" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicystatussynforabcucc.policystatussyn.ws.PolicyStatusSynForAbcUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_policyStatusSynForAbcUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 建行保单详情获取 -->
	<jaxws:endpoint id="queryPolicyDetailForCCB" address="/PA_policyDetailForCCBUCCqueryPolicyDetailForCCBAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicydetailforccbucc.querypolicydetailforccb.ws.IPolicyDetailForCCBUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyDetailForCCBUCCWSImplqueryPolicyDetailForCCB" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicydetailforccbucc.querypolicydetailforccb.ws.PolicyDetailForCCBUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_policyDetailForCCBUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="iCChangeQuery" address="/PA_iCBankDataUCCiCChangeQueryAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iicbankdataucc.pa_icbankdataucc.ws.IICBankDataUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ICBankDataUCCWSImpliCChangeQuery" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iicbankdataucc.pa_icbankdataucc.ws.ICBankDataUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_iCBankDataUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 万能账户实时查询接口（电商用） -->
	<jaxws:endpoint id="queryPolicyAccount" address="/PA_universalPolicyAccountUccqueryPolicyAccountAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.universalPolicyAccount.iuniversalpolicyaccountucc.querypolicyaccount.ws.IUniversalPolicyAccountUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UniversalPolicyAccountUccWSImplqueryPolicyAccount" class="com.nci.tunan.pa.impl.peripheral.exports.universalPolicyAccount.iuniversalpolicyaccountucc.querypolicyaccount.ws.UniversalPolicyAccountUccWSImpl">
				<property name="ucc">
					<ref bean="PA_universalPolicyAccountUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 个人税收居民身份信息提交接口 -->
    <jaxws:endpoint id="peopleIncomeCmit" address="/PA_peopleIncomeCmitUccpeopleIncomeCmitAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102002548.ipeopleincomecmitucc.peopleincomecmit.ws.IpeopleIncomeCmitUccWS">   
		   <jaxws:implementor>
		     <!-- class对应服务发布接口的实现类  -->
		     <bean id="peopleIncomeCmitUccWSImplpeopleIncomeCmit" class="com.nci.tunan.pa.impl.peripheral.exports.r00102002548.ipeopleincomecmitucc.peopleincomecmit.ws.peopleIncomeCmitUccWSImpl">
		       <property name="ucc">
		         <ref bean="PA_peopleIncomeCmitUcc" />
		       </property>
		     </bean>
		    </jaxws:implementor>
    </jaxws:endpoint>
    
    	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryHesitationSurrenderXmlList" address="/PA_queryHesitationSurrenderUCCqueryHesitationSurrenderXmlListAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iqueryhesitationsurrenderucc.queryhesitationsurrenderxmllist.ws.IQueryHesitationSurrenderUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryHesitationSurrenderUCCWSImplqueryHesitationSurrenderXmlList" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iqueryhesitationsurrenderucc.queryhesitationsurrenderxmllist.ws.QueryHesitationSurrenderUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryHesitationSurrenderUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 北京银行保单资产变动信息同步交易 -->
	<jaxws:endpoint id="policyCapitalChgSyn" address="/PA_policyCapitalChgSynUCCpolicyCapitalChgSynAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicycapitalchgsynucc.policycapitalchgsyn.ws.IPolicyCapitalChgSynUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyCapitalChgSynUCCWSImplpolicyCapitalChgSyn" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicycapitalchgsynucc.policycapitalchgsyn.ws.PolicyCapitalChgSynUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_policyCapitalChgSynUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 险种责任接口-->
	<jaxws:endpoint id="busiItemDuty" address="/PA_busiItemDutyUccbusiItemDutyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002681.ibusiitemdutyucc.busiitemduty.ws.IBusiItemDutyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="BusiItemDutyUccWSImplbusiItemDuty" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002681.ibusiitemdutyucc.busiitemduty.ws.BusiItemDutyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_busiItemDutyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 连带被保险人信息接口 -->
	<jaxws:endpoint id="queryJointInsurer" address="/PA_jointInsurerInfUccqueryJointInsurerAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002683.ijointinsurerinfucc.queryjointinsurer.ws.IJointInsurerInfUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="JointInsurerInfUccWSImplqueryJointInsurer" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002683.ijointinsurerinfucc.queryjointinsurer.ws.JointInsurerInfUccWSImpl">
				<property name="ucc">
					<ref bean="PA_jointInsurerInfUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户保单查询-->
	<jaxws:endpoint id="queryPolicyInfo" address="/customerPolicyInfoUccImplqueryPolicyInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002633.icustomerpolicyinfoucc.querypolicyinfo.ws.ICustomerPolicyInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustomerPolicyInfoUccWSImplqueryPolicyInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002633.icustomerpolicyinfoucc.querypolicyinfo.ws.CustomerPolicyInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_customerPolicyInfoUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单状态接口 -->
	<jaxws:endpoint id="policyStateInf" address="/PA_policyStateInfUccpolicyStateInfAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002677.ipolicystateinfucc.policystateinf.ws.IPolicyStateInfUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyStateInfUccWSImplpolicyStateInf" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002677.ipolicystateinfucc.policystateinf.ws.PolicyStateInfUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyStateInfUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!--保险账户其他信息轨迹信息接口  -->
	<jaxws:endpoint id="queryPolicyAcountOherInfo" address="/iPolicyAcountOherInfoUccqueryPolicyAcountOherInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002705.ipolicyacountoherinfoucc.querypolicyacountoherinfo.ws.IPolicyAcountOherInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyAcountOherInfoUccWSImplqueryPolicyAcountOherInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002705.ipolicyacountoherinfoucc.querypolicyacountoherinfo.ws.PolicyAcountOherInfoUccWSImpl">
				<property name="ucc">
					<ref bean="iPolicyAcountOherInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保险帐户信息接口 -->
	<jaxws:endpoint id="queryInsuAccountInfo" address="/insuAccountInfoQueryForRIUccqueryInsuAccountInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002707.iinsuaccountinfoqueryforriucc.queryinsuaccountinfo.ws.IInsuAccountInfoQueryForRIUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="InsuAccountInfoQueryForRIUccWSImplqueryInsuAccountInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002707.iinsuaccountinfoqueryforriucc.queryinsuaccountinfo.ws.InsuAccountInfoQueryForRIUccWSImpl">
				<property name="ucc">
					<ref bean="PA_insuAccountInfoQueryForRIUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 个单被保人信息接口 -->
	<jaxws:endpoint id="queryContInsuredInfo" address="/contInsuredInfoQueryForRIUccqueryContInsuredInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002709.icontinsuredinfoqueryforriucc.querycontinsuredinfo.ws.IContInsuredInfoQueryForRIUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ContInsuredInfoQueryForRIUccWSImplqueryContInsuredInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002709.icontinsuredinfoqueryforriucc.querycontinsuredinfo.ws.ContInsuredInfoQueryForRIUccWSImpl">
				<property name="ucc">
					<ref bean="PA_contInsuredInfoQueryForRIUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	
	<!-- 保单未领生存金查询 -->
	<jaxws:endpoint id="queryUnclaimLiGold" address="/PA_queryUnclaimLiGoldUccqueryUnclaimLiGoldAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002647.iqueryunclaimligolducc.queryunclaimligold.ws.IQueryUnclaimLiGoldUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryUnclaimLiGoldUccWSImplqueryUnclaimLiGold" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002647.iqueryunclaimligolducc.queryunclaimligold.ws.QueryUnclaimLiGoldUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryUnclaimLiGoldUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保费缴费信息接口 -->
	<jaxws:endpoint id="queryPremPaymentInf" address="/PA_premPaymentInfUccqueryPremPaymentInfAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002697.iprempaymentinfucc.queryprempaymentinf.ws.IPremPaymentInfUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PremPaymentInfUccWSImplqueryPremPaymentInf" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002697.iprempaymentinfucc.queryprempaymentinf.ws.PremPaymentInfUccWSImpl">
				<property name="ucc">
					<ref bean="PA_premPaymentInfUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<jaxws:endpoint id="queryPolicyAcount" address="/queryPolicyHistoryAcountRecordqueryPolicyAcountAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002679.ipolicyacountucc.querypolicyacount.ws.IPolicyAcountUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyAcountUccWSImplqueryPolicyAcount" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002679.ipolicyacountucc.querypolicyacount.ws.PolicyAcountUccWSImpl">
				<property name="ucc">
					<ref bean="queryPolicyHistoryAcountRecord" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 关联保单信息查询接口 -->
	<jaxws:endpoint id="queryFindAssociationPolicy" address="/associationPolicyUccqueryFindAssociationPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002630.iassociationpolicyucc.queryfindassociationpolicy.ws.IAssociationPolicyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AssociationPolicyUccWSImplqueryFindAssociationPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002630.iassociationpolicyucc.queryfindassociationpolicy.ws.AssociationPolicyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_associationPolicyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 投被保人基本信息校验及电话重复性校验接口 -->
	<jaxws:endpoint id="checkCustBaseInfoAndTelRepeat" address="/custBaseInfoAndTelValidateUcccheckCustBaseInfoAndTelRepeatAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102002743.icustbaseinfoandtelvalidateucc.checkcustbaseinfoandtelrepeat.ws.ICustBaseInfoAndTelValidateUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustBaseInfoAndTelValidateUccWSImplcheckCustBaseInfoAndTelRepeat" class="com.nci.tunan.pa.impl.peripheral.exports.r00102002743.icustbaseinfoandtelvalidateucc.checkcustbaseinfoandtelrepeat.ws.CustBaseInfoAndTelValidateUccWSImpl">
				<property name="ucc">
					<ref bean="PA_custBaseInfoAndTelValidateUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 重要信息和关系信息校验接口 -->
	<jaxws:endpoint id="importentInfoQuery" address="/importentInfoQueryUccimportentInfoQueryAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102002741.iimportentinfoqueryucc.importentinfoquery.ws.IImportentInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ImportentInfoQueryUccWSImplimportentInfoQuery" class="com.nci.tunan.pa.impl.peripheral.exports.r00102002741.iimportentinfoqueryucc.importentinfoquery.ws.ImportentInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="importentInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 社保状态变更查询接口 -->
	<jaxws:endpoint id="queryYDBQContQuerySrvBindingQSPort" address="/IYDBQContQuerySrvBindingQSPortUccqueryYDBQContQuerySrvBindingQSPortAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002758.iydbqcontquerysrvbindingqsportucc.queryydbqcontquerysrvbindingqsport.ws.IYDBQContQuerySrvBindingQSPortUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="YDBQContQuerySrvBindingQSPortUccWSImplqueryYDBQContQuerySrvBindingQSPort" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002758.iydbqcontquerysrvbindingqsportucc.queryydbqcontquerysrvbindingqsport.ws.YDBQContQuerySrvBindingQSPortUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IYDBQContQuerySrvBindingQSPortUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--税收居民身份采集校验接口-->
	<jaxws:endpoint id="identityCheck" address="/PA_identityCollectionCheckUccidentityCheckAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002764.iidentitycollectioncheckucc.identitycheck.ws.IIdentityCollectionCheckUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="IdentityCollectionCheckUCCWSImplidentityCheck" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002764.iidentitycollectioncheckucc.identitycheck.ws.IdentityCollectionCheckUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_identityCollectionCheckUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 根据保单查询业务人员 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryAgent" address="/PA_QueryAgentInfoUccqueryAgentAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r04301000286.iqueryagentinfoucc.queryagent.ws.IQueryAgentInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAgentInfoUccWSImplqueryAgent" class="com.nci.tunan.pa.impl.peripheral.exports.r04301000286.iqueryagentinfoucc.queryagent.ws.QueryAgentInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryAgentInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 健康无忧（尊享版）老客户校验接口  -->
	<!-- 健康无忧（尊享版）老客户校验接口 -->
	<jaxws:endpoint id="verifyOldCustomer" address="/oldCustomerVerifyUCCverifyOldCustomerAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002797.ioldcustomerverifyucc.verifyoldcustomer.ws.IOldCustomerVerifyUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="OldCustomerVerifyUCCWSImplverifyOldCustomer" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002797.ioldcustomerverifyucc.verifyoldcustomer.ws.OldCustomerVerifyUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_OldCustomerVerifyUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<jaxws:endpoint id="judgeBounusType" address="/IJudgeBonusTypeUccjudgeBounusTypeAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r00101002810.ijudgebonustypeucc.judgebounustype.ws.IJudgeBonusTypeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="JudgeBonusTypeUccWSImpljudgeBounusType" class="com.nci.tunan.cs.impl.peripheral.exports.r00101002810.ijudgebonustypeucc.judgebounustype.ws.JudgeBonusTypeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IJudgeBonusTypeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 现金分红历史信息查询接口 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCashDive" address="/PA_historyDivedendUccqueryCashDiveAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002808.iquerycashdividenducc.querycashdive.ws.IQueryCashDividendUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCashDividendUccWSImplqueryCashDive" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002808.iquerycashdividenducc.querycashdive.ws.QueryCashDividendUccWSImpl">
				<property name="ucc">
					<ref bean="PA_historyDivedendUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 双录自保件 -->
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="findSelfInsuredPart" address="/PA_SelfInsuredPartUccfindSelfInsuredPartAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r00101002795.iqueryselfinsuredpartucc.findselfinsuredpart.ws.IQuerySelfInsuredPartUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QuerySelfInsuredPartUccWSImplfindSelfInsuredPart" class="com.nci.tunan.cs.impl.peripheral.exports.r00101002795.iqueryselfinsuredpartucc.findselfinsuredpart.ws.QuerySelfInsuredPartUccWSImpl">
				<property name="ucc">
					<ref bean="PA_SelfInsuredPartUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 证件类型与年龄校验接口 -->
	<jaxws:endpoint id="querydocTypeAndAge" address="/PA_IdocumentTypeAndAgeCheckUccquerydocTypeAndAgeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102002992.idocumenttypeandagecheckucc.querydoctypeandage.ws.IdocumentTypeAndAgeCheckUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="documentTypeAndAgeCheckUccWSImplquerydocTypeAndAge" class="com.nci.tunan.pa.impl.peripheral.exports.r00102002992.idocumenttypeandagecheckucc.querydoctypeandage.ws.documentTypeAndAgeCheckUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IdocumentTypeAndAgeCheckUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 投保人保单缴费信息查询接口 -->
	<jaxws:endpoint id="queryFeeAmount" address="/paqueryFeeAmountqueryFeeAmountAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101003048.ipolicyholderquerypayfeeamountucc.queryfeeamount.ws.IPolicyHolderQueryPayFeeAmountUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyHolderQueryPayFeeAmountUccWSImplqueryFeeAmount" class="com.nci.tunan.pa.impl.peripheral.exports.r00101003048.ipolicyholderquerypayfeeamountucc.queryfeeamount.ws.PolicyHolderQueryPayFeeAmountUccWSImpl">
				<property name="ucc">
					<ref bean="paqueryFeeAmount" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单九要素查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="policyCheck" address="/PA_PolicyNineFaceotyCheckUccpolicyCheckAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101003049.ipolicyninefactorycheckucc.policycheck.ws.IPolicyNineFactoryCheckUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyNineFactoryCheckUccWSImplpolicyCheck" class="com.nci.tunan.pa.impl.peripheral.exports.r00101003049.ipolicyninefactorycheckucc.policycheck.ws.PolicyNineFactoryCheckUccWSImpl">
				<property name="ucc">
					<ref bean="PA_PolicyNineFaceotyCheckUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 核心系统保全规则校验接口 -->
	<jaxws:endpoint id="checkPreservation" address="/PA_ICoreSystemRescueRulecheckUcccheckPreservationAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101003050.icoresystemrescuerulecheckucc.checkpreservation.ws.ICoreSystemRescueRulecheckUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CoreSystemRescueRulecheckUccWSImplcheckPreservation" class="com.nci.tunan.pa.impl.peripheral.exports.r00101003050.icoresystemrescuerulecheckucc.checkpreservation.ws.CoreSystemRescueRulecheckUccWSImpl">
				<property name="ucc">
					<ref bean="PA_ICoreSystemRescueRulecheckUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户信息真实性校验接口  -->
	<jaxws:endpoint id="checkCustomerInfoAuthenticity" address="/ICustomerInfoAuthenticityCheckUcccheckCustomerInfoAuthenticityAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101003066.icustomerinfoauthenticitycheckucc.checkcustomerinfoauthenticity.ws.ICustomerInfoAuthenticityCheckUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustomerInfoAuthenticityCheckUccWSImplcheckCustomerInfoAuthenticity" class="com.nci.tunan.pa.impl.peripheral.exports.r00101003066.icustomerinfoauthenticitycheckucc.checkcustomerinfoauthenticity.ws.CustomerInfoAuthenticityCheckUccWSImpl">
				<property name="ucc">
					<ref bean="ICustomerInfoAuthenticityCheckUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="dealInsuredJobChangeQuery" address="/insuredJobChangeQueryUccdealInsuredJobChangeQueryAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06802003102.iinsuredjobchangequeryucc.dealinsuredjobchangequery.ws.IInsuredJobChangeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="InsuredJobChangeQueryUccWSImpldealInsuredJobChangeQuery" class="com.nci.tunan.pa.impl.peripheral.exports.r06802003102.iinsuredjobchangequeryucc.dealinsuredjobchangequery.ws.InsuredJobChangeQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_InsuredJobChangeQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="dealInsuredJobChange" address="/InsuredJobChangeUccdealInsuredJobChangeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06802003103.iinsuredjobchangeucc.dealinsuredjobchange.ws.IInsuredJobChangeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="InsuredJobChangeUccWSImpldealInsuredJobChange" class="com.nci.tunan.pa.impl.peripheral.exports.r06802003103.iinsuredjobchangeucc.dealinsuredjobchange.ws.InsuredJobChangeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_InsuredJobChangeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--同步该客户下查询保单接口（同步范围查询接口）  -->
	<jaxws:endpoint id="queryAllPolicysOfTheCustomer" address="/IQueryAllPolicysOfTheCustomerUccqueryAllPolicysOfTheCustomerAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003111.iqueryallpolicysofthecustomerucc.queryallpolicysofthecustomer.ws.IQueryAllPolicysOfTheCustomerUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAllPolicysOfTheCustomerUccWSImplqueryAllPolicysOfTheCustomer" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003111.iqueryallpolicysofthecustomerucc.queryallpolicysofthecustomer.ws.QueryAllPolicysOfTheCustomerUccWSImpl">
				<property name="ucc">
					<ref bean="IQueryAllPolicysOfTheCustomerUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<!-- 保单列表信息查询接口 -->
	<jaxws:endpoint id="queryPolicyList" address="/IQueryPolicyListInformationUccqueryPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003109.iquerypolicylistinformationucc.querypolicylist.ws.IQueryPolicyListInformationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyListInformationUccWSImplqueryPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003109.iquerypolicylistinformationucc.querypolicylist.ws.QueryPolicyListInformationUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IQueryPolicyListInformationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
			
	<!-- 重要资料变更提交接口 ucc -->
	<jaxws:endpoint id="customerUpdateForCM" address="/PA_csEndorseCMSubmitUcccustomerUpdateForCMAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802003106.icsendorsecmsubmitucc.customerupdateforcm.ws.ICsEndorseCMSubmitUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsEndorseCMSubmitUccWSImplcustomerUpdateForCM" class="com.nci.tunan.cs.impl.peripheral.exports.r06802003106.icsendorsecmsubmitucc.customerupdateforcm.ws.CsEndorseCMSubmitUccWSImpl">
				<property name="ucc">
					<ref bean="PA_csEndorseCMSubmitUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--客户重要资料变更试算接口 ucc-->
	<jaxws:endpoint id="customerChangeTrialForCM" address="/PA_csEndorseCMTrialUcccustomerChangeTrialForCMAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802003107.icsendorsecmtrialucc.customerchangetrialforcm.ws.ICsEndorseCMTrialUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsEndorseCMTrialUccWSImplcustomerChangeTrialForCM" class="com.nci.tunan.cs.impl.peripheral.exports.r06802003107.icsendorsecmtrialucc.customerchangetrialforcm.ws.CsEndorseCMTrialUccWSImpl">
				<property name="ucc">
					<ref bean="PA_csEndorseCMTrialUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 续收微天下保单查询接口 -->
	<jaxws:endpoint id="queryPolicyCode" address="/IWTXQueryPolicyCodeUCCqueryPolicyCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101003215.iwtxquerypolicycodeucc.querypolicycode.ws.IWTXQueryPolicyCodeUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="WTXQueryPolicyCodeUCCWSImplqueryPolicyCode" class="com.nci.tunan.pa.impl.peripheral.exports.r00101003215.iwtxquerypolicycodeucc.querypolicycode.ws.WTXQueryPolicyCodeUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_IWTXQueryPolicyCodeUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户基本资料变更修改提交接口   ucc-->
	<jaxws:endpoint id="customerChangeForCC" address="/PA_csEndorseCCSubmitUcccustomerChangeForCCAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802003108.icsendorseccsubmitucc.customerchangeforcc.ws.ICsEndorseCCSubmitUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsEndorseCCSubmitUccWSImplcustomerChangeForCC" class="com.nci.tunan.cs.impl.peripheral.exports.r06802003108.icsendorseccsubmitucc.customerchangeforcc.ws.CsEndorseCCSubmitUccWSImpl">
				<property name="ucc">
					<ref bean="PA_csEndorseCCSubmitUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 移动保全2.0 复效查询接口 -->
	<jaxws:endpoint id="CSContReinstateQueryUcc" address="/CSContReinstateQueryUccImplCSContReinstateQueryUccAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003160.icscontreinstatequeryucc.cscontreinstatequeryucc.ws.ICSContReinstateQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类-->
			<bean id="CSContReinstateQueryUccWSImplCSContReinstateQueryUcc" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003160.icscontreinstatequeryucc.cscontreinstatequeryucc.ws.CSContReinstateQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_CSContReinstateQueryUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
  
	<!-- 移动保全2.0新增附加险补退费试算   ucc-->
	<jaxws:endpoint id="calCsNsPremArap" address="/CSPremarapCalUcccalCsNsPremArapAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802003134.icspremarapcalucc.calcsnspremarap.ws.ICSPremarapCalUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CSPremarapCalUccWSImplcalCsNsPremArap" class="com.nci.tunan.cs.impl.peripheral.exports.r06802003134.icspremarapcalucc.calcsnspremarap.ws.CSPremarapCalUccWSImpl">
				<property name="ucc">
					<ref bean="ICSPremarapCalUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> 
	<!-- 移动保全2.0新增附加险社保状态校验   ucc-->
	<jaxws:endpoint id="sociSecuCheckAndCalPrem" address="/CSSociSecuCheckAndCalPremUccsociSecuCheckAndCalPremAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802003135.icssocisecucheckandcalpremucc.socisecucheckandcalprem.ws.ICSSociSecuCheckAndCalPremUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CSSociSecuCheckAndCalPremUccWSImplsociSecuCheckAndCalPrem" class="com.nci.tunan.cs.impl.peripheral.exports.r06802003135.icssocisecucheckandcalpremucc.socisecucheckandcalprem.ws.CSSociSecuCheckAndCalPremUccWSImpl">
				<property name="ucc">
					<ref bean="CSSociSecuCheckAndCalPremUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> 
	<!-- 新增附加险提交接口2.0-->
	<jaxws:endpoint id="addNsSubBusiProd" address="/CsNsSubBusiProdSubmitUCCImpladdNsSubBusiProdAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06801003136.icsnssubbusiprodsubmitucc.addnssubbusiprod.ws.ICsNsSubBusiProdSubmitUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsNsSubBusiProdSubmitUCCWSImpladdNsSubBusiProd" class="com.nci.tunan.cs.impl.peripheral.exports.r06801003136.icsnssubbusiprodsubmitucc.addnssubbusiprod.ws.CsNsSubBusiProdSubmitUCCWSImpl">
				<property name="ucc">
					<ref bean="CsNsSubBusiProdSubmitUCCImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--新增附加险-保单列表查询接口-->
	<jaxws:endpoint id="queryNsPolicy" address="/iQueryNsPolicyServicequeryNsPolicyAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06401003137.iquerynspolicyucc.querynspolicy.ws.IQueryNsPolicyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryNsPolicyUccWSImplqueryNsPolicy" class="com.nci.tunan.cs.impl.peripheral.exports.r06401003137.iquerynspolicyucc.querynspolicy.ws.QueryNsPolicyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryNsPolicyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--柜面自助终端  红利信息查询(打印通知书类)    -->
	<jaxws:endpoint id="bonusInfoQuery" address="/IBonusInfoQueryUccbonusInfoQueryAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003161.ibonusinfoqueryucc.bonusinfoquery.ws.IBonusInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="BonusInfoQueryUccWSImplbonusInfoQuery" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003161.ibonusinfoqueryucc.bonusinfoquery.ws.BonusInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="IBonusInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		<!-- 可续保转换险种清单列表 -->
		<jaxws:endpoint id="queryEableRenewRisk" address="/eableRenewRiskqueryEableRenewRiskAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06802003146.ieablerenewriskucc.queryeablerenewrisk.ws.IEableRenewRiskUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="EableRenewRiskUccWSImplqueryEableRenewRisk" class="com.nci.tunan.pa.impl.peripheral.exports.r06802003146.ieablerenewriskucc.queryeablerenewrisk.ws.EableRenewRiskUccWSImpl">
				<property name="ucc">
					<ref bean="PA_eableRenewRisk" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 交费方式变更试算接口-->
	<jaxws:endpoint id="payModelChangeTail" address="/PA_csPayModelChangeTrialUccpayModelChangeTailAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802003152.icspaymodechangetrialucc.paymodelchangetail.ws.ICsPayModeChangeTrialUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsPayModeChangeTrialUccWSImplpayModelChangeTail" class="com.nci.tunan.cs.impl.peripheral.exports.r06802003152.icspaymodechangetrialucc.paymodelchangetail.ws.CsPayModeChangeTrialUccWSImpl">
				<property name="ucc">
					<ref bean="PA_csPayModelChangeTrialUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 交费方式变更提交接口-->
    <jaxws:endpoint id="payModelChangeSub" address="/PA_csPayModelChangeTrialUccpayModelChangeSubAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802003153.icspaymodechangesubucc.paymodelchangesub.ws.ICsPayModeChangeSubUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsPayModeChangeSubUccWSImplpayModelChangeSub" class="com.nci.tunan.cs.impl.peripheral.exports.r06802003153.icspaymodechangesubucc.paymodelchangesub.ws.CsPayModeChangeSubUccWSImpl">
				<property name="ucc">
					<ref bean="PA_csPayModeChangeSubUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
    <!--续期交费信息变更-保单列表查询接口-->
    <jaxws:endpoint id="queryRenewalPolicyList" address="/IQueryRenewalPoliciesListUccqueryPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003158.iqueryrenewalpolicieslistucc.querypolicylist.ws.IQueryRenewalPoliciesListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryRenewalPoliciesListUccWSImplqueryPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003158.iqueryrenewalpolicieslistucc.querypolicylist.ws.QueryRenewalPoliciesListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IQueryRenewalPoliciesListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 续保险种转换-保单列表查询接口 -->
	<jaxws:endpoint id="queryTransformationPolicyList" address="/IQueryTransformationPolicyListUccqueryPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003147.iquerytransformationpolicylistucc.querypolicylist.ws.IQueryTransformationPolicyListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryTransformationPolicyListUccWSImplqueryPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003147.iquerytransformationpolicylistucc.querypolicylist.ws.QueryTransformationPolicyListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IQueryTransformationPolicyListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
		<!-- 受理号查询受理详情接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="findAcceptDetailByAcceptCode" address="/PA_QueryAgentDetailUccfindAcceptDetailByAcceptCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06801003144.iqueryagentdetailucc.findacceptdetailbyacceptcode.ws.IQueryAgentDetailUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAgentDetailUCCWSImplfindAcceptDetailByAcceptCode" class="com.nci.tunan.pa.impl.peripheral.exports.r06801003144.iqueryagentdetailucc.findacceptdetailbyacceptcode.ws.QueryAgentDetailUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryAgentDetailUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 缴费信息变更接口变更 -投保人保单信息查询接口-->
	<jaxws:endpoint id="queryPolicyHolderContNo" address="/IQueryPolicyHolderContNoUccqueryPolicyHolderContNoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101003201.iquerypolicyholdercontnoucc.querypolicyholdercontno.ws.IQueryPolicyHolderContNoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyHolderContNoUccWSImplqueryPolicyHolderContNo" class="com.nci.tunan.pa.impl.peripheral.exports.r00101003201.iquerypolicyholdercontnoucc.querypolicyholdercontno.ws.QueryPolicyHolderContNoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IQueryPolicyHolderContNoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 微信续投保表单信息查询接口  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryQueryWechatRenewal" address="/IQueryWechatRenewalUccqueryQueryWechatRenewalAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900020.iquerywechatrenewalucc.queryquerywechatrenewal.ws.IQueryWechatRenewalUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryWechatRenewalUccWSImplqueryQueryWechatRenewal" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900020.iquerywechatrenewalucc.queryquerywechatrenewal.ws.QueryWechatRenewalUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IQueryWechatRenewalUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 个人交费信息查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPerPremArap" address="/PA_QueryPerArapUccqueryPerPremArapAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101003033.iqueryperpremarapucc.queryperpremarap.ws.IQueryPerPremArapUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPerPremArapUCCWSImplqueryPerPremArap" class="com.nci.tunan.pa.impl.peripheral.exports.r00101003033.iqueryperpremarapucc.queryperpremarap.ws.QueryPerPremArapUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPerArapUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 受益人变更保单列表信息查询 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryBeneChange" address="/PA_QueryBeneChangeUccqueryBeneChangeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003159.iquerybenechangepolicyucc.querybenechange.ws.IQueryBeneChangePolicyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryBeneChangePolicyUccWSImplqueryBeneChange" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003159.iquerybenechangepolicyucc.querybenechange.ws.QueryBeneChangePolicyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryBeneChangeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 柜面自助：贷款清偿客户保单查询接口   -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryAllPolicysOfTheCustomerLoanAndPayOff" address="/IQueryPolicyInfoOfCustomerLoanAndPayOffUccqueryAllPolicysOfTheCustomerLoanAndPayOffAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003240.iquerypolicyinfoofcustomerloanandpayoffucc.queryallpolicysofthecustomerloanandpayoff.ws.IQueryPolicyInfoOfCustomerLoanAndPayOffUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyInfoOfCustomerLoanAndPayOffUccWSImplqueryAllPolicysOfTheCustomerLoanAndPayOff" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003240.iquerypolicyinfoofcustomerloanandpayoffucc.queryallpolicysofthecustomerloanandpayoff.ws.QueryPolicyInfoOfCustomerLoanAndPayOffUccWSImpl">
				<property name="ucc">
					<ref bean="IQueryPolicyInfoOfCustomerLoanAndPayOffUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="CsendorseAGQueryUcc" address="/PA_CsendorseAGQueryUccImplCsendorseAGQueryUccAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003288.icsendorseagqueryucc.csendorseagqueryucc.ws.ICsendorseAGQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsendorseAGQueryUccWSImplCsendorseAGQueryUcc" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003288.icsendorseagqueryucc.csendorseagqueryucc.ws.CsendorseAGQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_CsendorseAGQueryUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCusPlyBasicInfo" address="/PA_IQueryCusPlyBasicInfoUccqueryCusPlyBasicInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900056.iquerycusplybasicinfoucc.querycusplybasicinfo.ws.IQueryCusPlyBasicInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCusPlyBasicInfoUccWSImplqueryCusPlyBasicInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900056.iquerycusplybasicinfoucc.querycusplybasicinfo.ws.QueryCusPlyBasicInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IQueryCusPlyBasicInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 远程柜面身份识别回传接口接口类 -->
	<jaxws:endpoint id="saveIdentityCheckInfo" address="/PA_identityDistinguishUccsaveIdentityCheckInfoAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802900059.iidentitydistinguishucc.saveidentitycheckinfo.ws.IIdentityDistinguishUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="IdentityDistinguishUccWSImplsaveIdentityCheckInfo" class="com.nci.tunan.cs.impl.peripheral.exports.r06802900059.iidentitydistinguishucc.saveidentitycheckinfo.ws.IdentityDistinguishUccWSImpl">
				<property name="ucc">
					<ref bean="PA_identityDistinguishUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 心圆福职域保单列表查询接口 -->
	<jaxws:endpoint id="policyList" address="/IXYFZPolicyListUccpolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900086.ixyfzpolicylistucc.policylist.ws.IXYFZPolicyListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="XYFZPolicyListUccWSImplpolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900086.ixyfzpolicylistucc.policylist.ws.XYFZPolicyListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_XYFZPolicyListUccUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 心圆福职域保单详情查询接口 -->
	<jaxws:endpoint id="queryPolicyDetails" address="/IXYFZPolicyDetailsUccqueryPolicyDetailsAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900087.ixyfzpolicydetailsucc.querypolicydetails.ws.IXYFZPolicyDetailsUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="XYFZPolicyDetailsUccWSImplqueryPolicyDetails" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900087.ixyfzpolicydetailsucc.querypolicydetails.ws.XYFZPolicyDetailsUccWSImpl">
				<property name="ucc">
					<ref bean="PA_XYFZPolicyDetailsUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--远程柜面- 保全状态查询接口接口类 -->
	<jaxws:endpoint id="queryAcceptStatus" address="/PA_queryAcceptStatusUccqueryAcceptStatusAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06801900057.iqueryacceptstatusucc.queryacceptstatus.ws.IQueryAcceptStatusUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAcceptStatusUccWSImplqueryAcceptStatus" class="com.nci.tunan.cs.impl.peripheral.exports.r06801900057.iqueryacceptstatusucc.queryacceptstatus.ws.QueryAcceptStatusUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryAcceptStatusUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--远程柜面- 保全业务受理申请接口实现类 -->
	<jaxws:endpoint id="saveAcceptApplyInfo" address="/PA_csAcceptApplyUccsaveAcceptApplyInfoAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802900060.exports.icsacceptapplyucc.saveacceptapplyinfo.ws.ICsAcceptApplyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsAcceptApplyUccWSImplsaveAcceptApplyInfo" class="com.nci.tunan.cs.impl.peripheral.exports.r06802900060.exports.icsacceptapplyucc.saveacceptapplyinfo.ws.CsAcceptApplyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_csAcceptApplyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--P00001900085柜面自助查询保单原账号信息接口 -->
	<jaxws:endpoint id="queryPolicyBankAccountNo" address="/queryPolicyBankAccountInfosqueryPolicyBankAccountNoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06801900084.ipolicybankaccountinfoucc.querypolicybankaccountno.ws.IPolicyBankAccountInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyBankAccountInfoUccWSImplqueryPolicyBankAccountNo" class="com.nci.tunan.pa.impl.peripheral.exports.r06801900084.ipolicybankaccountinfoucc.querypolicybankaccountno.ws.PolicyBankAccountInfoUccWSImpl">
				<property name="ucc">
					<ref bean="queryPolicyBankAccountInfos" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 贷款清偿-保单列表信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyListLoan" address="/PA_queryPolicyListLoanRepaymentUccqueryPolicyListLoanAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101900106.iquerypolicylistloanrepaymentucc.querypolicylistloan.ws.IQueryPolicyListLoanRepaymentUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyListLoanRepaymentUccWSImplqueryPolicyListLoan" class="com.nci.tunan.pa.impl.peripheral.exports.r00101900106.iquerypolicylistloanrepaymentucc.querypolicylistloan.ws.QueryPolicyListLoanRepaymentUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyListLoanRepaymentUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 主险附加险信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="findAllAdditionBusi" address="/PA_additionBusiInfoUccfindAllAdditionBusiAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900318.iadditionbusiinfoucc.findalladditionbusi.ws.IAdditionBusiInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AdditionBusiInfoUccWSImplfindAllAdditionBusi" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900318.iadditionbusiinfoucc.findalladditionbusi.ws.AdditionBusiInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_additionBusiInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyRelation" address="/PA_hjbPolicyRelationQueryUccqueryPolicyRelationAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900321.ihjbpolicyrelationqueryucc.querypolicyrelation.ws.IHJBPolicyRelationQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="HJBPolicyRelationQueryUccWSImplqueryPolicyRelation" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900321.ihjbpolicyrelationqueryucc.querypolicyrelation.ws.HJBPolicyRelationQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_hjbPolicyRelationQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 个人渠道绩优业务员信息查询呢 -->
    <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="findAllAgentInfo" address="/PA_queryAgentInfoUccfindAllAgentInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900354.iqueryagentinfoucc.findallagentinfo.ws.IQueryAgentInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAgentInfoUCCWSImplfindAllAgentInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900354.iqueryagentinfoucc.findallagentinfo.ws.QueryAgentInfoUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_AgentInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 实时孤儿单查询通知接口B-->
	<jaxws:endpoint id="queryAndUploadOrphan" address="/IQueryAndUploadOrphanListUccqueryAndUploadOrphanAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.queryAndUploadOrphan.iqueryanduploadorphanlistucc.queryanduploadorphan.ws.IQueryAndUploadOrphanListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAndUploadOrphanListUccWSImplqueryAndUploadOrphan" class="com.nci.tunan.pa.impl.peripheral.exports.queryAndUploadOrphan.iqueryanduploadorphanlistucc.queryanduploadorphan.ws.QueryAndUploadOrphanListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryAndUploadOrphanListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 随信通保全信息提交接口 定义服务的接口类 -->
	<jaxws:endpoint id="custCertiEndDateOrJobCodeSave" address="/CS_CsCustCertiEndDateOrJobCodeSaveUcccustCertiEndDateOrJobCodeSaveAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06802900362.icscustcertienddateorjobcodesaveucc.custcertienddateorjobcodesave.ws.ICsCustCertiEndDateOrJobCodeSaveUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsCustCertiEndDateOrJobCodeSaveUccWSImplcustCertiEndDateOrJobCodeSave" class="com.nci.tunan.cs.impl.peripheral.exports.r06802900362.icscustcertienddateorjobcodesaveucc.custcertienddateorjobcodesave.ws.CsCustCertiEndDateOrJobCodeSaveUccWSImpl">
				<property name="ucc">
					<ref bean="CS_CsCustCertiEndDateOrJobCodeSaveUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单贷款续贷列表查询:P00001900382 -->
	<jaxws:endpoint id="queryPlolcyLoanInfos" address="/queryPlolcyLoanInfoqueryPlolcyLoanInfosAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06801900399.iplicyloanrlucc.queryplolcyloaninfos.ws.IPlicyLoanRLUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PlicyLoanRLUccWSImplqueryPlolcyLoanInfos" class="com.nci.tunan.pa.impl.peripheral.exports.r06801900399.iplicyloanrlucc.queryplolcyloaninfos.ws.PlicyLoanRLUccWSImpl">
				<property name="ucc">
					<ref bean="PA_yunyiqueryPlolcyLoanInfo" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 追加保费-保单列表查询  -->
	<jaxws:endpoint id="queryAdditionalpremiumList" address="/QueryAdditionalpremiumUccImplqueryAdditionalpremiumListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900379.iqueryadditionalpremiumucc.queryadditionalpremiumlist.ws.IQueryAdditionalpremiumUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAdditionalpremiumUccWSImplqueryAdditionalpremiumList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900379.iqueryadditionalpremiumucc.queryadditionalpremiumlist.ws.QueryAdditionalpremiumUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryAdditionalpremiumUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 双录互保件查询接口 -->
	<jaxws:endpoint id="IDualRecordingMutualGuaranteeUcc" address="/IDualRecordingMutualGuaranteeUccIDualRecordingMutualGuaranteeUccAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900407.idualrecordingmutualguaranteeucc.idualrecordingmutualguaranteeucc.ws.IDualRecordingMutualGuaranteeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="DualRecordingMutualGuaranteeUccWSImplIDualRecordingMutualGuaranteeUcc" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900407.idualrecordingmutualguaranteeucc.idualrecordingmutualguaranteeucc.ws.DualRecordingMutualGuaranteeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_dualRecordingMutualGuaranteeUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 中保信保单编码路由快查   -->
	<jaxws:endpoint id="PA_JudgePolicySequenceNoQuery" address="/IJudgePolicySequenceNoExistUccPolicySequenceNoQueryPaAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.judgepolicysequencenoexist.ipolicysequencenoqueryucc.policysequencenoquery.ws.IPolicySequenceNoExistQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicySequenceNoQueryUccWSImplPolicySequenceNoPaQuery" class="com.nci.tunan.pa.impl.peripheral.exports.judgepolicysequencenoexist.ipolicysequencenoqueryucc.policysequencenoquery.ws.PolicySequenceNoExistQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_PolicySequenceNoQueryUCCImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 银代业务客户资料采集优化-保单详情查询   -->
	<jaxws:endpoint id="PA_QueryPolicyDetailsUccWS" address="/IQueryPolicyDetailsUccAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900454.iquerypolicydetailsucc.querypolicydetailslist.ws.IQueryPolicyDetailsUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PA_QueryPolicyDetailsUccWSImpl" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900454.iquerypolicydetailsucc.querypolicydetailslist.ws.QueryPolicyDetailsUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPolicyDetailsUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 银保通客户资料采集需求（补充资料）-保单查询   -->
	<jaxws:endpoint id="PA_WXPolicyQueryUCCWS" address="/IWXPolicyQueryUccQueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.querywxpolicyinfo.exports.iwxpolicyqueryucc.wxpolicyquery.ws.IWXPolicyQueryUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="WXPolicyQueryUCCWSImplQueryPolicy" class="com.nci.tunan.pa.impl.querywxpolicyinfo.exports.iwxpolicyqueryucc.wxpolicyquery.ws.WXPolicyQueryUCCWSImpl">
				<property name="wxPolicyQueryUcc">
					<ref bean="PA_WXPolicyQueryUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		
	<!-- 移动保全2.0保单续贷试算接口 -->
	<jaxws:endpoint id="policyLoanTrila" address="/PA_csEndorseRLTrialUccpolicyLoanTrilaAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r00102900476.icsendorserltrialucc.policyloantrila.ws.ICsEndorseRLTrialUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsEndorseRLTrialUccWSImplpolicyLoanTrila" class="com.nci.tunan.cs.impl.peripheral.exports.r00102900476.icsendorserltrialucc.policyloantrila.ws.CsEndorseRLTrialUccWSImpl">
				<property name="ucc">
					<ref bean="PA_csEndorseRLTrialUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址 
	implementorClass 是定义服务的接口类
	核心险种信息查询接口 -->
	<jaxws:endpoint id="PA_WXPolicyQueryHXXZUccWS" address="/WXPolicyQueryHXXZUccImplqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900495.iwxpolicyqueryhxxzucc.querypolicy.ws.IWXPolicyQueryHXXZUccWS">		
		<jaxws:implementor>
		<!-- 	class对应服务发布接口的实现类  -->
			<bean id="WXPolicyQueryHXXZUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900495.iwxpolicyqueryhxxzucc.querypolicy.ws.WXPolicyQueryHXXZUccWSImpl">
				<property name="ucc">
					<ref bean="PA_WXPolicyQueryHXXZUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 万能险部分领取详情查询接口 -->
	<jaxws:endpoint id="PA_QueryPGDeitailUccWS" address="/IQueryPGDeitailUccqueryDetailAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900564.iquerypgdeitailucc.querydetail.ws.IQueryPGDeitailUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPGDeitailUccWSImplqueryDetail" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900564.iquerypgdeitailucc.querydetail.ws.QueryPGDeitailUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPGDeitailUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 累积生息账户领取详情查询接口 -->
	<jaxws:endpoint id="PA_QueryAIDeitailUccWS" address="/QueryAIDeitailUccImplqueryDetailAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900554.iqueryaideitailucc.querydetail.ws.IQueryAIDeitailUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAIDeitailUccWSImplqueryDetail" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900554.iqueryaideitailucc.querydetail.ws.QueryAIDeitailUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryAIDeitailUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 年金满期金给付详情查询接口 -->
	<jaxws:endpoint id="PA_QueryPolicyAGInfoUccWS" address="/QueryPolicyAGInfoUccImplqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900545.iquerypolicyaginfoucc.querypolicy.ws.IQueryPolicyAGInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyAGInfoUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900545.iquerypolicyaginfoucc.querypolicy.ws.QueryPolicyAGInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPolicyAGInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="PA_ABSCashValNoticeUccWS" address="/ABSCashValNoticeUccImpldealNoticeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06402900572.iabscashvalnoticeucc.dealnotice.ws.IABSCashValNoticeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ABSCashValNoticeUccWSImpldealNotice" class="com.nci.tunan.pa.impl.peripheral.exports.r06402900572.iabscashvalnoticeucc.dealnotice.ws.ABSCashValNoticeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_ABSCashValNoticeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户真实性校验接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="PA_CheckCustomerUccWS" address="/ICheckCustomerUcccheckCustomerAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900558.icheckcustomerucc.checkcustomer.ws.ICheckCustomerUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CheckCustomerUccWSImplcheckCustomer" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900558.icheckcustomerucc.checkcustomer.ws.CheckCustomerUccWSImpl">
				<property name="ucc">
					<ref bean="PA_CheckCustomerUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 随信通续期缴费信息查询接口 -->
	<jaxws:endpoint id="PA_QueryPCInfoForSXTUccWS" address="/QueryPCInfoForSXTUccImplqueryPolicyInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900592.iquerypcinfoforsxtucc.querypolicyinfo.ws.IQueryPCInfoForSXTUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPCInfoForSXTUccWSImplqueryPolicyInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900592.iquerypcinfoforsxtucc.querypolicyinfo.ws.QueryPCInfoForSXTUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPCInfoForSXTUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 身故受益人详情查询接口 -->
	<jaxws:endpoint id="PA_QueryDeceasedBeneficiary" address="/DeceasedBeneficiaryQueryUCCImplqueryDeceasedBeneficiaryAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900546.ideceasedbeneficiaryqueryucc.querydeceasedbeneficiary.ws.IDeceasedBeneficiaryQueryUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="DeceasedBeneficiaryQueryUCCWSImplqueryDeceasedBeneficiary" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900546.ideceasedbeneficiaryqueryucc.querydeceasedbeneficiary.ws.DeceasedBeneficiaryQueryUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_DeceasedBeneficiaryQueryUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 满期不续保保单查询接口 -->
	<jaxws:endpoint id="queryNoRenewalExpiration" address="/NoRenewalExpirationQueryUCCImplqueryNoRenewalExpirationAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900549.inorenewalexpirationqueryucc.querynorenewalexpiration.ws.INoRenewalExpirationQueryUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="NoRenewalExpirationQueryUCCWSImplqueryNoRenewalExpiration" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900549.inorenewalexpirationqueryucc.querynorenewalexpiration.ws.NoRenewalExpirationQueryUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_NoRenewalExpirationQueryUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 退保保单查询接口 -->
	<jaxws:endpoint id="QuerySurrender" address="/PolicyListQuerySurrenderUccImplQuerySurrenderAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900621.ipolicylistquerysurrenderucc.querysurrender.ws.IPolicyListQuerySurrenderUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyListQuerySurrenderUccWSImplQuerySurrender" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900621.ipolicylistquerysurrenderucc.querysurrender.ws.PolicyListQuerySurrenderUccWSImpl">
				<property name="ucc">
					<ref bean="PA_PolicyListQuerySurrenderUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 退保保单查询接口 -->
	<jaxws:endpoint id="queryCTPolicy" address="/QueryCTPolicyForYdbq2UccImplqueryCTPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900620.iqueryctpolicyforydbq2ucc.queryctpolicy.ws.IQueryCTPolicyForYdbq2UccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCTPolicyForYdbq2UccWSImplqueryCTPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900620.iqueryctpolicyforydbq2ucc.queryctpolicy.ws.QueryCTPolicyForYdbq2UccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryCTPolicyForYdbq2Ucc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 续期缴费变更同步列表查询接口 -->
	<jaxws:endpoint id="query" address="/renewalFeeInfoQueryUccqueryAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900540.irenewalfeeinfoqueryucc.query.ws.IRenewalFeeInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RenewalFeeInfoQueryUccWSImplquery" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900540.irenewalfeeinfoqueryucc.query.ws.RenewalFeeInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="renewalFeeInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 累积生息账户领取-保单列表查询接口 -->
	<jaxws:endpoint id="queryAIPolicy" address="/PA_QueryAIPolicyUccqueryAIPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900560.iqueryaipolicyucc.queryaipolicy.ws.IQueryAIPolicyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAIPolicyUccWSImplqueryAIPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900560.iqueryaipolicyucc.queryaipolicy.ws.QueryAIPolicyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryAIPolicyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 保单列表查询接口-万能险账户领取 -->
	<jaxws:endpoint id="queryPGPolicyForYdbq" address="/QueryPGPolicyForYdbq2UccqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900627.iquerypgpolicyforydbq2ucc.querypolicy.ws.IQueryPGPolicyForYdbq2UccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPGPolicyForYdbq2UccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900627.iquerypgpolicyforydbq2ucc.querypolicy.ws.QueryPGPolicyForYdbq2UccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPGPolicyForYdbq2Ucc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 万能投连账户信息查询接口 -->
	<jaxws:endpoint id="queryPGPolicyForGw" address="/PA_QueryPGPolicyForGwUccqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900653.iquerypgpolicyforgwucc.querypolicy.ws.IQueryPGPolicyForGwUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPGPolicyForGwUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900653.iquerypgpolicyforgwucc.querypolicy.ws.QueryPGPolicyForGwUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPGPolicyForGwUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 投连万能账户信息详情查询接口 -->
	<jaxws:endpoint id="queryPGPolicyDeitailForGw" address="/PA_QueryPGPolicyDeitailForGwUccqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900654.iquerypgpolicydeitailforgwucc.querypolicy.ws.IQueryPGPolicyDeitailForGwUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPGPolicyDeitailForGwUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900654.iquerypgpolicydeitailforgwucc.querypolicy.ws.QueryPGPolicyDeitailForGwUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPGPolicyDeitailForGwUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 新掌上新华对接核心保全项实付金额查询接口 -->
	<jaxws:endpoint id="csPreservationPaymantMoneyItemUcc" address="/csPrePaymantMoneyItemUcccsPreservationPaymantMoneyItemUccAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06801900660.icspreservationpaymantmoneyitemucc.cspreservationpaymantmoneyitemucc.ws.IcsPreservationPaymantMoneyItemUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="csPreservationPaymantMoneyItemUccWSImplcsPreservationPaymantMoneyItemUcc" class="com.nci.tunan.cs.impl.peripheral.exports.r06801900660.icspreservationpaymantmoneyitemucc.cspreservationpaymantmoneyitemucc.ws.csPreservationPaymantMoneyItemUccWSImpl">
				<property name="ucc">
					<ref bean="csPrePaymantMoneyItemUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 追加保费保全记录查询接口   -->
	<jaxws:endpoint id="PA_QueryAddToPremium" address="/AddToPremiumQueryUccQueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06801900673.addtopremiumpreservequery.querypremium.ws.IAddToPremiumPreserveQueryUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AddToPremiumQueryUccWSImpladdToPremiumPreserveQuery" class="com.nci.tunan.pa.impl.peripheral.exports.r06801900673.addPremiumRecordQueryUcc.queryaddpremiumRecord.ws.AddToPremiumQueryUccWSImpl">
				<!-- <property name="addToPremiumPreserveQueryUcc">
					<ref bean="PA_AddToPremiumPreserveQueryUcc" />
				</property>-->
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
			<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 退保保全记录查询接口   -->
	<jaxws:endpoint id="PA_QueryPolicyCancellationKeepIntactRecord" address="/PolicyCancellationKeepIntactRecordQueryUccQueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06801900672.policyCancellationKeepIntactRecordQueryUcc.policyCancellationKeepIntact.ws.IPolicyCancellationKeepIntactRecordQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyCancellationKeepIntactRecordQueryUccWSImplpolicyCancellationKeepIntactRecordQuery" class="com.nci.tunan.pa.impl.peripheral.exports.r06801900672.policyCancellationKeepIntactRecordQueryUcc.querypolicyCancellationKeepIntactRecord.ws.PolicyCancellationKeepIntactRecordQueryUccWSImpl">
				<!-- <property name="policyCancellationKeepIntactRecordQueryUcc">
					<ref bean="PA_PolicyCancellationKeepIntactRecordQueryUcc" />
				</property> -->
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 保单自互保件查询接口 -->
	<jaxws:endpoint id="querySelfInsFlagAndMutualInsFlag" address="/QrySelfInsFlagAndMutualInsFlagUccImplquerySelfInsFlagAndMutualInsFlagAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06801900662.iqryselfinsflagandmutualinsflagucc.queryselfinsflagandmutualinsflag.ws.IQrySelfInsFlagAndMutualInsFlagUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QrySelfInsFlagAndMutualInsFlagUccWSImplquerySelfInsFlagAndMutualInsFlag" class="com.nci.tunan.pa.impl.peripheral.exports.r06801900662.iqryselfinsflagandmutualinsflagucc.queryselfinsflagandmutualinsflag.ws.QrySelfInsFlagAndMutualInsFlagUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QrySelfInsFlagAndMutualInsFlagUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 万能险账户抵扣续期保费轨迹接口 -->
	<jaxws:endpoint id="queryUIAccountQueryPolicy" address="/queryUIAccountDeductionPremiumLocusUccqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900668.iqueryuiaccountdeductionpremiumlocusucc.querypolicy.ws.IQueryUIAccountDeductionPremiumLocusUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryUIAccountDeductionPremiumLocusUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900668.iqueryuiaccountdeductionpremiumlocusucc.querypolicy.ws.QueryUIAccountDeductionPremiumLocusUccWSImpl">
				<property name="ucc">
					<ref bean="queryUIAccountDeductionPremiumLocusUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryIncreaseOfPremiumPolicy" address="/PA_queryPolicyListIncreaseOfPremiumUccqueryIncreaseOfPremiumPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900707.iquerypolicylistincreaseofpremiumucc.queryincreaseofpremiumpolicy.ws.IQueryPolicyListIncreaseOfPremiumUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyListIncreaseOfPremiumUccWSImplqueryIncreaseOfPremiumPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900707.iquerypolicylistincreaseofpremiumucc.queryincreaseofpremiumpolicy.ws.QueryPolicyListIncreaseOfPremiumUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyListIncreaseOfPremiumUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
    <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryValidPolInfoByCustmoerFiveInfo" address="/PA_IssueValidPolicyQueryUccqueryValidPolInfoByCustmoerFiveInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900715.iissuevalidpolicyqueryucc.queryvalidpolinfobycustmoerfiveinfo.ws.IIssueValidPolicyQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="IssueValidPolicyQueryUccWSImplqueryValidPolInfoByCustmoerFiveInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900715.iissuevalidpolicyqueryucc.queryvalidpolinfobycustmoerfiveinfo.ws.IssueValidPolicyQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_IssueValidPolicyQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="qryPolicyAndContract" address="/IQryPolicyAndContractUCCqryPolicyAndContractAddr" implementorClass="com.nci.tunan.pa.interfaces.qrypolicyandcontract.exports.iqrypolicyandcontractucc.qrypolicyandcontract.ws.IQryPolicyAndContractUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QryPolicyAndContractUCCWSImplqryPolicyAndContract" class="com.nci.tunan.pa.impl.qrypolicyandcontract.exports.iqrypolicyandcontractucc.qrypolicyandcontract.ws.QryPolicyAndContractUCCWSImpl">
				<property name="ucc">
					<ref bean="IQryPolicyAndContractUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
 
 <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCustomerInformationByNineElement" address="/PA_QueryCustomerInformationByNineElementUccImplqueryCustomerInformationByNineElementAddr" implementorClass="com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.ws.IQueryCustomerInformationByNineElementUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCustomerInformationByNineElementUccWSImplqueryCustomerInformationByNineElement" class="com.nci.tunan.pa.impl.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.ws.QueryCustomerInformationByNineElementUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryCustomerInformationByNineElementUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCustomerMobilePhone" address="/PA_QueryMobilePhoneAccordingFiveElementsUccImplqueryCustomerMobilePhoneAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900753.iquerymobilephoneaccordingfiveelementsucc.querycustomermobilephone.ws.IQueryMobilePhoneAccordingFiveElementsUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryMobilePhoneAccordingFiveElementsUccWSImplqueryCustomerMobilePhone" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900753.iquerymobilephoneaccordingfiveelementsucc.querycustomermobilephone.ws.QueryMobilePhoneAccordingFiveElementsUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryMobilePhoneAccordingFiveElementsUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
			
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyServicePersonInfo" address="/PA_QueryPolicyServicePersonInfoUccqueryPolicyServicePersonInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900761.iquerypolicyservicepersoninfoucc.querypolicyservicepersoninfo.ws.IQueryPolicyServicePersonInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyServicePersonInfoUccWSImplqueryPolicyServicePersonInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900761.iquerypolicyservicepersoninfoucc.querypolicyservicepersoninfo.ws.QueryPolicyServicePersonInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPolicyServicePersonInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryisSalespersonFlag" address="/PA_queryisSalespersonFlagUccqueryisSalespersonFlagAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900757.iqueryissalespersonflagucc.queryissalespersonflag.ws.IQueryisSalespersonFlagUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryisSalespersonFlagUccWSImplqueryisSalespersonFlag" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900757.iqueryissalespersonflagucc.queryissalespersonflag.ws.QueryisSalespersonFlagUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryisSalespersonFlagUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 未成年人死亡风险保额累计查询接口 -->
	<jaxws:endpoint id="paMinorsRiskOfDeathCoverageQuery" address="/PA_minorsRiskOfDeathCoverageQueryUccqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900747.iminorsriskofdeathcoveragequeryucc.querypolicy.ws.IMinorsRiskOfDeathCoverageQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="MinorsRiskOfDeathCoverageQueryUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900747.iminorsriskofdeathcoveragequeryucc.querypolicy.ws.MinorsRiskOfDeathCoverageQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_minorsRiskOfDeathCoverageQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 邮储银行-保单查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfoPsbc" address="/PA_policyInfoPsbcUccqueryPolicyInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyinfopsbcucc.querypolicyinfo.ws.IPolicyInfoPsbcUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyInfoPsbcUccWSImplqueryPolicyInfo" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyinfopsbcucc.querypolicyinfo.ws.PolicyInfoPsbcUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyInfoPsbcUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCumuInteAccInfo" address="/PA_cumuInteAccInfoQueryUccqueryCumuInteAccInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900790.icumuinteaccinfoqueryucc.querycumuinteaccinfo.ws.ICumuInteAccInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CumuInteAccInfoQueryUccWSImplqueryCumuInteAccInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900790.icumuinteaccinfoqueryucc.querycumuinteaccinfo.ws.CumuInteAccInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_cumuInteAccInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryDocumentList" address="/PA_documentListQueryUccqueryDocumentListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900789.idocumentlistqueryucc.querydocumentlist.ws.IDocumentListQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="DocumentListQueryUccWSImplqueryDocumentList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900789.idocumentlistqueryucc.querydocumentlist.ws.DocumentListQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_documentListQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 代理人信息查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryContractAgentInfo" address="/PA_queryContractAgentInfoUccqueryContractAgentInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900832.iquerycontractagentinfoucc.querycontractagentinfo.ws.IQueryContractAgentInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryContractAgentInfoUccWSImplqueryContractAgentInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900832.iquerycontractagentinfoucc.querycontractagentinfo.ws.QueryContractAgentInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryContractAgentInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 通知书报告书查看下载信息回传接口 -->
	<jaxws:endpoint id="querynoticereportinfo" address="/INoticeReportViewAndDownloadAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06402900835.inoticereportviewanddownloaducc.querynoticereportinfo.ws.NoticeReportViewAndDownloadUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="NoticeReportViewAndDownloadUccWSImplquerynoticereportinfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06402900835.inoticereportviewanddownloaducc.querynoticereportinfo.ws.NoticeReportViewAndDownloadUccWSImpl">
				<property name="ucc">
					<ref bean="PA_NoticeReportInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 通知书报告书保单列表查询接口-->
	<jaxws:endpoint id="queryNotificationReportPolicyList" address="/PA_queryNotificationReportPolicyListUccqueryNotificationReportPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900834.iquerynotificationreportpolicylistucc.querynotificationreportpolicylist.ws.IQueryNotificationReportPolicyListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryNotificationReportPolicyListUccWSImplqueryNotificationReportPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900834.iquerynotificationreportpolicylistucc.querynotificationreportpolicylist.ws.QueryNotificationReportPolicyListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryNotificationReportPolicyListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	

	<!-- 通知书信息查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryDocumentInfo" address="/PA_queryDocumentInfoUccImplUccqueryDocumentInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900848.iquerydocumentinfoucc.querydocumentinfo.ws.IQueryDocumentInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryDocumentInfoUccWSImplqueryDocumentInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900848.iquerydocumentinfoucc.querydocumentinfo.ws.QueryDocumentInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryDocumentInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyDetailsInfo" address="/queryPolicyDetailsInfoUCCqueryPolicyDetailsInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900830.iquerypolicydetailsinfoucc.querypolicydetailsinfo.ws.IQueryPolicyDetailsInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyDetailsInfoUCCWSImplqueryPolicyDetailsInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900830.iquerypolicydetailsinfoucc.querypolicydetailsinfo.ws.QueryPolicyDetailsInfoUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyDetailsInfoUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 保单原交费账户信息查询接口 -->
	<jaxws:endpoint id="queryPolicyPrimaryPaymentAccountInfo" address="/PA_queryPolicyPrimaryPaymentAccountInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900813.iquerypolicyprimarypaymentaccountinfoucc.querypolicyprimaryaccount.ws.QueryPolicyPrimaryPaymentAccountInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyPrimaryPaymentAccountInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900813.iquerypolicyprimarypaymentaccountinfoucc.querypolicyprimaryaccount.ws.QueryPolicyPrimaryPaymentAccountInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyPrimaryAccountUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 银保信账户查询接口 -->
	<jaxws:endpoint id="queryBankInsurAccountInfo" address="/PA_queryBankInsurAccountInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900874.iquerybankinsuraccountinfoucc.querybankinsuraccount.ws.IQueryBankInsurAccountInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="BankInsurAccountInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900874.iquerybankinsuraccountinfoucc.querybankinsuraccount.ws.QueryBankInsurAccountInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryBankInsurAccountInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryBindingSuccess" address="/PA_accountBindingConfirmationUccqueryBindingSuccessAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06402900875.iaccountbindingconfirmationucc.querybindingsuccess.ws.IAccountBindingConfirmationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AccountBindingConfirmationUccWSImplqueryBindingSuccess" class="com.nci.tunan.pa.impl.peripheral.exports.r06402900875.iaccountbindingconfirmationucc.querybindingsuccess.ws.AccountBindingConfirmationUccWSImpl">
				<property name="ucc">
					<ref bean="PA_accountBindingConfirmationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryLoanAccountInfo" address="/PA_queryLoanAccountInfoUccqueryLoanAccountInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900887.iqueryloanaccountinfoucc.queryLoanAccountInfo.ws.IQueryLoanAccountInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryLoanAccountInfoUccWSImplqueryLoanAccountInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900887.iqueryloanaccountinfoucc.queryLoanAccountInfo.ws.QueryLoanAccountInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryLoanAccountInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 客户信息查询保单号列表接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="customerInformationQueryPolicyCode" address="/ICustomerInformationQueryPolicyCodeUcccustomerInformationQueryPolicyCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900924.icustomerinformationquerypolicycodeucc.customerinformationquerypolicycode.ws.ICustomerInformationQueryPolicyCodeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustomerInformationQueryPolicyCodeUccWSImplcustomerInformationQueryPolicyCode" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900924.icustomerinformationquerypolicycodeucc.customerinformationquerypolicycode.ws.CustomerInformationQueryPolicyCodeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_customerInformationQueryPolicyCodeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户信息查询保单号列表接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryInsuredlistByCusnameAndCusNoAndAgentCode" address="/IQueryInsuredlistByCusnameAndCusNoAndAgentCodeUccqueryInsuredlistByCusnameAndCusNoAndAgentCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900928.iqueryinsuredlistbycusnameandcusnoandagentcodeucc.queryinsuredlistbycusnameandcusnoandagentcode.ws.IQueryInsuredlistByCusnameAndCusNoAndAgentCodeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryInsuredlistByCusnameAndCusNoAndAgentCodeUccWSImplqueryInsuredlistByCusnameAndCusNoAndAgentCode" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900928.iqueryinsuredlistbycusnameandcusnoandagentcodeucc.queryinsuredlistbycusnameandcusnoandagentcode.ws.QueryInsuredlistByCusnameAndCusNoAndAgentCodeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryInsuredlistByCusnameAndCusNoAndAgentCodeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 商业养老保险账户信息同步查询接口 (接入渠道)   -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfo" address="/IPolicyInfoPaqdUccqueryPolicyInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyinfopaqducc.querypolicyinfo.ws.IPolicyInfoPaqdUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyInfoPaqdUccWSImplqueryPolicyInfo" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyinfopaqducc.querypolicyinfo.ws.PolicyInfoPaqdUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyInfoPaqdUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 保费信息查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPremiumInformation" address="/IPremiumInformationInquireUccqueryPremiumInformationAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900882.ipremiuminformationinquireucc.querypremiuminformation.ws.IPremiumInformationInquireUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PremiumInformationInquireUccWSImplqueryPremiumInformation" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900882.ipremiuminformationinquireucc.querypremiuminformation.ws.PremiumInformationInquireUccWSImpl">
				<property name="ucc">
					<ref bean="PA_premiumInformationInquireUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 续领年金生存认证查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryRenewalOfAnnuitySurvivalCertification" address="/IRenewalOfAnnuitySurvivalCertificationUccqueryRenewalOfAnnuitySurvivalCertificationAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900920.irenewalofannuitysurvivalcertificationucc.queryrenewalofannuitysurvivalcertification.ws.IRenewalOfAnnuitySurvivalCertificationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RenewalOfAnnuitySurvivalCertificationUccWSImplqueryRenewalOfAnnuitySurvivalCertification" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900920.irenewalofannuitysurvivalcertificationucc.queryrenewalofannuitysurvivalcertification.ws.RenewalOfAnnuitySurvivalCertificationUccWSImpl">
				<property name="ucc">
					<ref bean="PA_renewalOfAnnuitySurvivalCertificationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	 <!-- 睡眠保单查询接口  -->
   <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="querySleepPolicyInfo" address="/PA_querySleepPolicyInfoUccquerySleepPolicyInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900934.iquerysleeppolicyinfoucc.querySleepPolicyInfo.ws.IQuerySleepPolicyInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QuerySleepPolicyInfoUccWSImplquerySleepPolicyInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900934.iquerysleeppolicyinfoucc.querySleepPolicyInfo.ws.QuerySleepPolicyInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_querySleepPolicyInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单年金满期金信息查询接口  -->
   <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryAnnuAndExpireInfo" address="/PA_queryAnnuAndExpireInfoUccqueryAnnuAndExpireInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900942.iqueryannuandexpireinfoucc.queryannuandexpireinfo.ws.IQueryAnnuAndExpireInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAnnuAndExpireInfoUccWSImplqueryAnnuAndExpireInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900942.iqueryannuandexpireinfoucc.queryannuandexpireinfo.ws.QueryAnnuAndExpireInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryAnnuAndExpireInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 睡眠保单查询接口  -->
   <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="updateSleepPolicyInfo" address="/PA_updateSleepPolicyInfoUccupdateSleepPolicyInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06402900936.iupdatesleeppolicyinfoucc.updateSleepPolicyInfo.ws.IUpdateSleepPolicyInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UpdateSleepPolicyInfoUccWSImplupdateSleepPolicyInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06402900936.iupdatesleeppolicyinfoucc.updateSleepPolicyInfo.ws.UpdateSleepPolicyInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_updateSleepPolicyInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单列表信息分页查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyListInfo" address="/IPaginatePolicyListInformationUccqueryPolicyListInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900932.ipaginatepolicylistinformationucc.querypolicylistinfo.ws.IPaginatePolicyListInformationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PaginatePolicyListInformationUccWSImplqueryPolicyListInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900932.ipaginatepolicylistinformationucc.querypolicylistinfo.ws.PaginatePolicyListInformationUccWSImpl">
				<property name="ucc">
					<ref bean="PA_paginatePolicyListInformationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 投保人保单列表查询接口  -->
   <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyHolderChangeInfo" address="/PA_queryPolicyHolderChangeInfoUccqueryPolicyHolderChangeInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900974.iquerypolicyholderchangeinfoucc.querypolicyholderchangeinfo.ws.IQueryPolicyHolderChangeInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyHolderChangeInfoUccWSImplqueryPolicyHolderChangeInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900974.iquerypolicyholderchangeinfoucc.querypolicyholderchangeinfo.ws.QueryPolicyHolderChangeInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyHolderChangeInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户三要素查询保单信息列表接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfos" address="/PA_queryPolicyInfosByThreeElementsOfCustomerUccqueryPolicyInfosAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900976.iquerypolicyinfosbythreeelementsofcustomerucc.querypolicyinfos.ws.IQueryPolicyInfosByThreeElementsOfCustomerUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyInfosByThreeElementsOfCustomerUccWSImplqueryPolicyInfos" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900976.iquerypolicyinfosbythreeelementsofcustomerucc.querypolicyinfos.ws.QueryPolicyInfosByThreeElementsOfCustomerUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyInfosByThreeElementsOfCustomerUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 减保保单列表查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryReductionPolicyInfos" address="/PA_queryReductionPolicyInformationListUccqueryReductionPolicyInfosAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900960.iqueryreductionpolicyinformationlistucc.queryreductionpolicyinfos.ws.IQueryReductionPolicyInformationListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryReductionPolicyInformationListUccWSImplqueryReductionPolicyInfos" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900960.iqueryreductionpolicyinformationlistucc.queryreductionpolicyinfos.ws.QueryReductionPolicyInformationListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryReductionPolicyInformationListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单补发保单列表查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyReplacementInformation" address="/PA_queryPolicyReplacementInformationUccqueryPolicyReplacementInformationAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901074.iquerypolicyreplacementinformationucc.querypolicyreplacementinformation.ws.IQueryPolicyReplacementInformationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyReplacementInformationUccWSImplqueryPolicyReplacementInformation" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901074.iquerypolicyreplacementinformationucc.querypolicyreplacementinformation.ws.QueryPolicyReplacementInformationUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyReplacementInformationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单关联保单列表查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyRelatedPolicyList" address="/PA_queryPolicyRelatedPolicyListUccqueryPolicyRelatedPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901085.iquerypolicyrelatedpolicylistucc.querypolicyrelatedpolicylist.ws.IQueryPolicyRelatedPolicyListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyRelatedPolicyListUccWSImplqueryPolicyRelatedPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901085.iquerypolicyrelatedpolicylistucc.querypolicyrelatedpolicylist.ws.QueryPolicyRelatedPolicyListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyRelatedPolicyListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 领取形式变更-保单列表查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryChangeOfClaimForm" address="/PA_queryPolicyInfosForChangeOfClaimFormUccqueryChangeOfClaimFormAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900978.iquerypolicyinfosforchangeofclaimformucc.querychangeofclaimform.ws.IQueryPolicyInfosForChangeOfClaimFormUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyInfosForChangeOfClaimFormUccWSImplqueryChangeOfClaimForm" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900978.iquerypolicyinfosforchangeofclaimformucc.querychangeofclaimform.ws.QueryPolicyInfosForChangeOfClaimFormUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyInfosForChangeOfClaimFormUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 查询客户名下保单号接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCustomerPolicycode" address="/IQueryCustomerPolicycodeUccquerycustomerpolicycodeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900980.iquerycustomerpolicycodeucc.querycustomerpolicycode.ws.IQueryCustomerPolicycodeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCustomerPolicycodeUccWSImplQueryCustomerPolicycode" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900980.iquerycustomerpolicycodeucc.querycustomerpolicycode.ws.QueryCustomerPolicycodeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryCustomerPolicycodeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<!-- 职业类别变更-保单列表查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryJobUwLevelChangesPolicyList" address="/IQueryJobUwLevelChangesPolicyListUccqueryJobUwLevelChangesPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900982.iqueryjobuwlevelchangespolicylistucc.queryjobuwlevelchangespolicylist.ws.IQueryJobUwLevelChangesPolicyListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryJobUwLevelChangesPolicyListUccWSImplQueryJobUwLevelChangesPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900982.iqueryjobuwlevelchangespolicylistucc.queryjobuwlevelchangespolicylist.ws.QueryJobUwLevelChangesPolicyListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryJobUwLevelChangesPolicyListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
			<!-- 保单号集合查询保单信息接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfoByPolicyList" address="/IQueryPolicyInfoByPolicyListUccqueryPolicyInfoByPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900986.iquerypolicyinfobypolicylistucc.querypolicyinfobypolicylist.ws.IQueryPolicyInfoByPolicyListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyInfoByPolicyListUccWSImplQueryPolicyInfoByPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900986.iquerypolicyinfobypolicylistucc.querypolicyinfobypolicylist.ws.QueryPolicyInfoByPolicyListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyInfoByPolicyListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户号查询保单号列表接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfoByCustomerOfPolicyList" address="/PA_queryPolicyInfoListUccImplqueryPolicyInfoByPolicyListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901046.iquerypolicyinfolistucc.querypolicyinfobypolicylist.ws.IQueryPolicyInfoListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyInfoListUccWSImplqueryPolicyInfoByPolicyList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901046.iquerypolicyinfolistucc.querypolicyinfobypolicylist.ws.QueryPolicyInfoListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyInfoListUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 查询保全受理号下客户信息接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCustomerInfoByAcceptCode" address="/IQueryCustomerInfoByAcceptCodeUccqueryCustomerInfoByAcceptCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06801901056.iquerycustomerinfoucc.findcustomerinfobyacceptcode.ws.IQueryCustomerInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCustomerInfoByAcceptCodeUccWSImplQueryCustomerInfoByAcceptCode" class="com.nci.tunan.pa.impl.peripheral.exports.r06801901056.findcustomerinfobyacceptcode.ws.QueryCustomerInfoUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryCustomerInfoByAcceptCodeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
   <!-- 红利领取保单列表查询接口  -->
   <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryBonuspayPolicyListInfo" address="/PA_queryBonuspayPolicyListInfoUccQueryBonuspayPolicyListInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901075.iquerybonuspaypolicylistinfoucc.querybonuspaypolicylistinfo.ws.IQueryBonuspayPolicyListInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryBonuspayPolicyListInfoUccWSImplqueryBonuspayPolicyListInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901075.iquerybonuspaypolicylistinfoucc.querybonuspaypolicylistinfo.ws.QueryBonuspayPolicyListInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryBonuspayPolicyListInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
   <!-- 技术需求任务 #190097 新核心-接口需求-移动保全2.0-新增红利领取领取形式变更功能需求-PAS-2/3  -->
   <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryBonusClaimFromChangeInfo" address="/PA_queryBonusClaimFromChangeInfoUccQueryBonusClaimFromChangeInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901105.iquerybonusclaimformchangeinfoucc.querybonusclaimformchangeinfo.ws.IQueryBonusClaimFromChangeInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryBonusClaimFromChangeInfoUccWSImplqueryBonusClaimFromChangeInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901105.iquerybonusclaimformchangeinfoucc.querybonusclaimformchangeinfo.ws.QueryBonusClaimFromChangeInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryBonusClaimFromChangeInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- TR-20250704-015214 掌上新华攻坚组问题单-PRM-20250703-0006_自助理赔申请时，出险人信息查询缓慢-个险新核心 -->
	   <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolListsByCmFiveFactor" address="/PA_QueryPolListsByCmFiveFactorUccQueryPolListsByCmFiveFactorAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901141.iquerypollistsbycmfivefactorucc.querypollistsbycmfivefactor.ws.IQueryPolListsByCmFiveFactorUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolListsByCmFiveFactorUccWSImplqueryPolListsByCmFiveFactor" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901141.iquerypollistsbycmfivefactorucc.querypollistsbycmfivefactor.ws.QueryPolListsByCmFiveFactorUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolListsByCmFiveFactorUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户保单手机号查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyLastMobileTel" address="/IQueryPolicyLastMobileTelUccqueryPolicyLastMobileTelAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901069.iquerypolicylastmobiletelucc.querypolicylastmobiletel.ws.IQueryPolicyLastMobileTelUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyLastMobileTelUccWSImplQueryPolicyLastMobileTel" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901069.iquerypolicylastmobiletelucc.querypolicylastmobiletel.ws.QueryPolicyLastMobileTelUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyLastMobileTelUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户号查询其保单下的客户信息接口-->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryAllCustomerByNewCusid" address="/PA_queryAllCustomerByNewCusidUccqueryAllCustomerByNewCusidAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401901097.iqueryallcustomerbynewcusiducc.queryallcustomerbynewcusid.ws.IQueryAllCustomerByNewCusidUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAllCustomerByNewCusidUccWSImplqueryAllCustomerByNewCusid" class="com.nci.tunan.pa.impl.peripheral.exports.r06401901097.iqueryallcustomerbynewcusiducc.queryallcustomerbynewcusid.ws.QueryAllCustomerByNewCusidUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryAllCustomerByNewCusidUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
</beans>