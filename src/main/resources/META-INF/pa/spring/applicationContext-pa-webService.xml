<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:jaxws="http://cxf.apache.org/jaxws" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd   http://www.springframework.org/schema/beans         http://www.springframework.org/schema/beans/spring-beans-4.0.xsd         http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd         http://www.springframework.org/schema/context         http://www.springframework.org/schema/context/spring-context-4.0.xsd">

	

	<!-- 自核接口 start niuyu_wb 2014-12-09 -->
	<jaxws:endpoint address="/renewalAutoOffsetAddr" id="PA_renewalAutoOffset" implementor="#PA_renewalAutoOffsetByWSAdapter"/>
	<bean class="com.nci.tunan.pa.impl.renewal.adapter.ws.RenewalAutoOffsetByWSAdapter" id="PA_renewalAutoOffsetByWSAdapter">
		<property name="renewCollectionService" ref="PA_renewCollectionService"/>
	</bean>
	<!-- 自核接口 start -->

	<!-- add by liangpl 2015.6.28 start -->
	<!-- 保单打印接口 start -->
	<!-- <jaxws:endpoint id="queryForPrint" implementor="#queryForPrintWSAdapter" -->
	<!-- address="/queryForPrintAddr" /> -->
	<!-- <bean id="queryForPrintWSAdapter" class="com.nci.tunan.pa.impl.queryforprint.adapter.ws.QueryForPrintWSImpl"> -->
	<!-- <property ref="queryForPrintUCC" name="queryForPrintUCC" /> -->
	<!-- </bean> -->
	<!-- 保单打印接口 end -->

	<!-- 保单信息查询接口 start -->
	<!-- <jaxws:endpoint id="queryPolicyInfo" implementor="#queryPolicyInfoWSAdapter" 
		address="/queryPolicyInfoAddr" /> -->
	<!-- <bean id="queryPolicyInfoWSAdapter" class="com.nci.tunan.pa.impl.querypolicyinfo.adapter.QueryPolicyInfoWSImpl"> -->
	<!-- <property ref="queryPolicyInfoUCC" name="queryPolicyInfoUCC" /> -->
	<!-- </bean> -->
	<!-- 保单信息查询接口 end -->

	<!-- 保单列表查询接口 start -->
	<!-- <jaxws:endpoint id="queryPolicyList" implementor="#queryPolicyListWSAdapter" 
		address="/queryPolicyListAddr" /> -->
	<!-- <bean id="queryPolicyListWSAdapter" class="com.nci.tunan.pa.impl.querypolicylist.adapter.QueryPolicyListWSImpl"> -->
	<!-- <property ref="queryPolicyListUCC" name="queryPolicyListUCC" /> -->
	<!-- </bean> -->
	<!-- 保单列表查询接口 end -->
	<!-- add by liangpl 2015.6.28 end -->

	<!-- 客户告知接口 start niuyu_wb 2015-01-07 -->
	<bean class="com.nci.tunan.pa.impl.customerSurvey.adapter.ws.CustomerSurveyQueryByWSAdapter" id="PA_customerSurveyQueryByWSAdapter">
		<property name="customerSurveyQueryUCC" ref="PA_customerSurveyQueryUCC"/>
	</bean>
	<jaxws:endpoint address="/customerSurveyQueryAddr" id="PA_customerSurveyQuery" implementor="#PA_customerSurveyQueryByWSAdapter"/>
	<!-- 客户告知接口 start -->

	<!-- 客户保单查询接口 start niuyu_wb 2015-01-16 -->
	<bean class="com.nci.tunan.pa.impl.customerPolicy.adapter.ws.CustomerPolicyQueryByWSAdapter" id="PA_customerPolicyQueryByWSAdapter">
		<property name="customerPolicyQueryUCC" ref="PA_customerPolicyQueryUCC"/>
	</bean>
	<jaxws:endpoint address="/customerPolicyQueryAddr" id="PA_customerPolicyQuery" implementor="#PA_customerPolicyQueryByWSAdapter"/>
	<!-- 客户保单查询接口 start -->
	<!-- <jaxws:endpoint id="terminateAccept" implementor="#terminateAcceptWSImpl" 
		address="/terminateAcceptAddr" /> <bean id="terminateAcceptWSImpl" class="com.nci.tunan.cs.impl.terminateAccept.adapter.TerminateAcceptWSImpl"> 
		</bean> -->

	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryRoleInfoUCCqueryRoleInfoAddr" id="PA_queryRoleInfo" implementorClass="com.nci.tunan.pa.interfaces.queryroleinfo.exports.iqueryroleinfoucc.queryroleinfo.ws.IQueryRoleInfoUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.queryroleinfo.exports.iqueryroleinfoucc.queryroleinfo.ws.QueryRoleInfoUCCWSImpl" id="PA_QueryRoleInfoUCCWSImplqueryRoleInfo">
				<property name="ucc" ref="PA_queryRoleInfoUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/seckillPolicyDateSyncUCCaddPolicyOnlineAddr" id="PA_addPolicyOnline" implementorClass="com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.addpolicyonline.ws.ISeckillPolicyDateSyncUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.addpolicyonline.ws.SeckillPolicyDateSyncUCCWSImpl" id="PA_SeckillPolicyDateSyncUCCWSImpladdPolicyOnline">
				<property name="ucc" ref="PA_seckillPolicyDateSyncUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- <jaxws:endpoint id="proposalRuleWs" address="/proposalRuleWsImplAddr" 
		implementorClass="com.nci.tunan.cs.interfaces.rule.proposal.IProposalRuleWs"> 
		<jaxws:implementor> class对应服务发布接口的实现类 <bean id="proposalRuleWsImpl" class="com.nci.tunan.cs.imports.rule.proposal.ProposalRuleWsImpl"> 
		<property name="ucc"> <ref bean="cusApplicationUCC" /> </property> </bean> 
		</jaxws:implementor> </jaxws:endpoint> -->
	<!-- 秒杀保单承保信息同步 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/seckillPolicyDateSyncUCCseckillPolicyDateInfoAddr" id="PA_seckillPolicyDateInfo" implementorClass="com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.ws.ISeckillPolicyDateSyncUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.ws.SeckillPolicyDateSyncUCCWSImpl" id="PA_SeckillPolicyDateSyncUCCWSImplseckillPolicyDateInfo">
				<property name="ucc" ref="PA_seckillPolicyDateSyncUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/secKillAcknowledgeSyncUCCdataSyncInfoAddr" id="PA_dataSyncInfo" implementorClass="com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.ws.ISecKillAcknowledgeSyncUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.ws.SecKillAcknowledgeSyncUCCWSImpl" id="PA_SecKillAcknowledgeSyncUCCWSImpldataSyncInfo">
				<property name="ucc" ref="PA_secKillAcknowledgeSyncUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 查询保单被保人接口 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyInsuredUCCqueryPolicyInsuredAddr" id="PA_queryPolicyInsured" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000014.iquerypolicyinsureducc.querypolicyinsured.ws.IQueryPolicyInsuredUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000014.iquerypolicyinsureducc.querypolicyinsured.ws.QueryPolicyInsuredUCCWSImpl" id="PA_QueryPolicyInsuredUCCWSImplqueryPolicyInsured">
				<property name="ucc" ref="PA_queryPolicyInsuredUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 个人客户产品信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyProductUCCqueryPolicyProductAddr" id="PA_queryPolicyProduct" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000040.iquerypolicyproductucc.querypolicyproduct.ws.IQueryPolicyProductUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000040.iquerypolicyproductucc.querypolicyproduct.ws.QueryPolicyProductUCCWSImpl" id="PA_QueryPolicyProductUCCWSImplqueryPolicyProduct">
				<property name="ucc" ref="PA_queryPolicyProductUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 网点新增 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/addBankBranchUCCaddBankBranchAddr" id="PA_addBankBranch" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000225.iaddbankbranchucc.addbankbranch.ws.IAddBankBranchUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000225.iaddbankbranchucc.addbankbranch.ws.AddBankBranchUCCWSImpl" id="PA_AddBankBranchUCCWSImpladdBankBranch">
				<property name="ucc" ref="PA_addBankBranchUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 人员修改 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/agentOperationUCCagentOperationAddr" id="PA_agentOperation" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00102000602.iagentoperationucc.agentoperation.ws.IAgentOperationUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00102000602.iagentoperationucc.agentoperation.ws.AgentOperationUCCWSImpl" id="PA_AgentOperationUCCWSImplagentOperation">
				<property name="ucc" ref="PA_agentOperationUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单密码查询 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyPasswordUCCqueryPolicyPasswordAddr" id="PA_queryPolicyPassword" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000548.iquerypolicypassworducc.querypolicypassword.ws.IQueryPolicyPasswordUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000548.iquerypolicypassworducc.querypolicypassword.ws.QueryPolicyPasswordUCCWSImpl" id="PA_QueryPolicyPasswordUCCWSImplqueryPolicyPassword">
				<property name="ucc" ref="PA_queryPolicyPasswordUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<jaxws:endpoint address="/queryPolicyListUCCqueryPolicyListAddr" id="PA_queryPolicyList" implementorClass="com.nci.tunan.pa.interfaces.querypolicylist.exports.iquerypolicylistucc.getpolicylist.ws.IQueryPolicyListUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.querypolicylist.exports.iquerypolicylistucc.getpolicylist.ws.QueryPolicyListUCCWSImpl" id="PA_QueryPolicyListUCCWSImplqueryPolicyList">
				<property name="ucc" ref="PA_queryPolicyListUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/capResultUCCcapResultAddr" id="PA_capResult" implementorClass="com.nci.tunan.pa.interfaces.capresult.exports.icapresultucc.capresult.ws.ICAPResultUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.capresult.exports.icapresultucc.capresult.ws.CAPResultUCCWSImpl" id="PA_CAPResultUCCWSImplcapResult">
				<property name="ucc" ref="PA_capResultUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- <jaxws:endpoint id="synchro" address="/policyStatusSynchroUCCsynchroAddr" 
		implementorClass="com.nci.tunan.pa.interfaces.policystatussynchro.exports.ipolicystatussynchroucc.synchro.ws.IPolicyStatusSynchroUCCWS"> -->
	<!-- <jaxws:implementor> -->
	<!-- class对应服务发布接口的实现类 -->
	<!-- <bean id="PolicyStatusSynchroUCCWSImplsynchro" class="com.nci.tunan.pa.impl.policystatussynchro.exports.ipolicystatussynchroucc.synchro.ws.PolicyStatusSynchroUCCWSImpl"> -->
	<!-- <property name="ucc"> -->
	<!-- <ref bean="policyStatusSynchroUCC" /> -->
	<!-- </property> -->
	<!-- </bean> -->
	<!-- </jaxws:implementor> -->
	<!-- </jaxws:endpoint> -->

	<!-- address属性是调用webservice时需要使用的地址(满期给付查询接口) -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/maturityBenefitUCCqueryMaturityAddr" id="PA_queryMaturity" implementorClass="com.nci.tunan.pa.interfaces.maturitybenefit.exports.imaturitybenefitucc.querymaturity.ws.IMaturityBenefitUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.maturitybenefit.exports.imaturitybenefitucc.querymaturity.ws.MaturityBenefitUCCWSImpl" id="PA_MaturityBenefitUCCWSImplqueryMaturity">
				<property name="ucc" ref="PA_maturityBenefitUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/bankPolicyUCCqueryBankPolicyAddr" id="PA_queryBankPolicy" implementorClass="com.nci.tunan.pa.interfaces.bankpolicy.exports.ibankpolicyucc.querybankpolicy.ws.IBankPolicyUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.bankpolicy.exports.ibankpolicyucc.querybankpolicy.ws.BankPolicyUCCWSImpl" id="PA_BankPolicyUCCWSImplqueryBankPolicy">
				<property name="ucc" ref="PA_bankPolicyUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryInsurancePolicyUCCqueryInsurancePolicyAddr" id="PA_queryInsurancePolicy" implementorClass="com.nci.tunan.pa.interfaces.queryinsurancepolicy.exports.iqueryinsurancepolicyucc.queryinsurancepolicy.ws.IQueryInsurancePolicyUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.queryinsurancepolicy.exports.iqueryinsurancepolicyucc.queryinsurancepolicy.ws.QueryInsurancePolicyUCCWSImpl" id="PA_QueryInsurancePolicyUCCWSImplqueryInsurancePolicy">
				<property name="ucc" ref="PA_queryInsurancePolicyUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 理赔结案相关接口 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/investValueUCCqueryInvestValueAddr" id="PA_queryInvestValue" implementorClass="com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.ws.IInvestValueUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.investvalue.exports.iinvestvalueucc.queryinvestvalue.ws.InvestValueUCCWSImpl" id="PA_InvestValueUCCWSImplqueryInvestValue">
				<property name="ucc" ref="PA_investValueUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/loanSelfpayUCCqueryLoanSelfpayAddr" id="PA_queryLoanSelfpay" implementorClass="com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.ws.ILoanSelfpayUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.ws.LoanSelfpayUCCWSImpl" id="PA_LoanSelfpayUCCWSImplqueryLoanSelfpay">
				<property name="ucc" ref="PA_loanSelfpayUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/debtPremUCCqueryDebtPremAddr" id="PA_queryDebtPrem" implementorClass="com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.ws.IDebtPremUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.debtprem.exports.idebtpremucc.querydebtprem.ws.DebtPremUCCWSImpl" id="PA_DebtPremUCCWSImplqueryDebtPrem">
				<property name="ucc" ref="PA_debtPremUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/overPremUCCqueryOverPremAddr" id="PA_queryOverPrem" implementorClass="com.nci.tunan.pa.interfaces.overprem.exports.ioverpremucc.queryoverprem.ws.IOverPremUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.overprem.exports.ioverpremucc.queryoverprem.ws.OverPremUCCWSImpl" id="PA_OverPremUCCWSImplqueryOverPrem">
				<property name="ucc" ref="PA_overPremUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/accountValueUCCqueryAccountValueAddr" id="PA_queryAccountValue" implementorClass="com.nci.tunan.pa.interfaces.accountvalue.exports.iaccountvalueucc.queryaccountvalue.ws.IAccountValueUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.accountvalue.exports.iaccountvalueucc.queryaccountvalue.ws.AccountValueUCCWSImpl" id="PA_AccountValueUCCWSImplqueryAccountValue">
				<property name="ucc" ref="PA_accountValueUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/aboutFeeUCCqueryAboutFeeAddr" id="PA_queryAboutFee" implementorClass="com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.ws.IAboutFeeUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.aboutfee.exports.iaboutfeeucc.queryaboutfee.ws.AboutFeeUCCWSImpl" id="PA_AboutFeeUCCWSImplqueryAboutFee">
				<property name="ucc" ref="PA_aboutFeeUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/ensureAnnualUCCqueryEnsureAnnualAddr" id="PA_queryEnsureAnnual" implementorClass="com.nci.tunan.pa.interfaces.ensureannual.exports.iensureannualucc.queryensureannual.ws.IEnsureAnnualUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.ensureannual.exports.iensureannualucc.queryensureannual.ws.EnsureAnnualUCCWSImpl" id="PA_EnsureAnnualUCCWSImplqueryEnsureAnnual">
				<property name="ucc" ref="PA_ensureAnnualUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/backPremUCCqueryBackPremAddr" id="PA_queryBackPrem" implementorClass="com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.ws.IBackPremUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.backprem.exports.ibackpremucc.querybackprem.ws.BackPremUCCWSImpl" id="PA_BackPremUCCWSImplqueryBackPrem">
				<property name="ucc" ref="PA_backPremUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryBriefInfoUCCqueryBriefInfoAddr" id="PA_queryBriefInfo" implementorClass="com.nci.tunan.pa.interfaces.querybriefinfo.exports.iquerybriefinfoucc.querybriefinfo.ws.IQueryBriefInfoUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.querybriefinfo.exports.iquerybriefinfoucc.querybriefinfo.ws.QueryBriefInfoUCCWSImpl" id="PA_QueryBriefInfoUCCWSImplqueryBriefInfo">
				<property name="ucc" ref="PA_queryBriefInfoUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/customerPolicyGeneralUCCqueryCustomerPolicyGeneralAddr" id="PA_queryCustomerPolicyGeneral" implementorClass="com.nci.tunan.pa.interfaces.customerpolicygeneral.exports.icustomerpolicygeneralucc.querycustomerpolicygeneral.ws.ICustomerPolicyGeneralUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.customerpolicygeneral.exports.icustomerpolicygeneralucc.querycustomerpolicygeneral.ws.CustomerPolicyGeneralUCCWSImpl" id="PA_CustomerPolicyGeneralUCCWSImplqueryCustomerPolicyGeneral">
				<property name="ucc" ref="PA_customerPolicyGeneralUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 保单状态同步 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/policyStatusSynchroUCCsynchroAddr" id="PA_synchro" implementorClass="com.nci.tunan.pa.interfaces.policystatussynchro.exports.ipolicystatussynchroucc.synchro.ws.IPolicyStatusSynchroUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.policystatussynchro.exports.ipolicystatussynchroucc.synchro.ws.PolicyStatusSynchroUCCWSImpl" id="PA_PolicyStatusSynchroUCCWSImplsynchro">
				<property name="ucc" ref="PA_policyStatusSynchroUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- <jaxws:endpoint id="proposalRuleWs" address="/proposalRuleWsImplAddr" 
		implementorClass="com.nci.tunan.cs.interfaces.rule.proposal.IProposalRuleWs"> 
		<jaxws:implementor> class对应服务发布接口的实现类 <bean id="proposalRuleWsImpl" class="com.nci.tunan.cs.imports.rule.proposal.ProposalRuleWsImpl"> 
		<property name="ucc"> <ref bean="cusApplicationUCC" /> </property> </bean> 
		</jaxws:implementor> </jaxws:endpoint> -->
	<!-- 秒杀保单承保信息同步 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	

	<!-- 查询保单被保人接口 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	

	<!-- 个人客户产品信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	

	<!-- 网点新增 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	

	<!-- 人员修改 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	

	<!-- 保单密码查询 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	

	<!-- 心跳交易 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/heartTransactionUCCgetTransactionSearchAddr" id="PA_getTransactionSearch" implementorClass="com.nci.tunan.pa.interfaces.hearttransaction.exports.ihearttransactionucc.gettransactionsearch.ws.IHeartTransactionUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.hearttransaction.exports.ihearttransactionucc.gettransactionsearch.ws.HeartTransactionUCCWSImpl" id="PA_HeartTransactionUCCWSImplgetTransactionSearch">
				<property name="ucc" ref="PA_heartTransactionUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 接入渠道-批量提数处理结果返盘 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/iBatchNumResultsReturnUCCbatchNumResultsReturnAddr" id="PA_batchNumResultsReturn" implementorClass="com.nci.tunan.pa.interfaces.batchnumresultsreturn.exports.ibatchnumresultsreturnucc.batchnumresultsreturn.ws.IBatchNumResultsReturnUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.batchnumresultsreturn.exports.ibatchnumresultsreturnucc.batchnumresultsreturn.ws.BatchNumResultsReturnUCCWSImpl" id="PA_BatchNumResultsReturnUCCWSImplbatchNumResultsReturn">
				<property name="ucc" ref="PA_iBatchNumResultsReturnUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 日常交易查询 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/dailyTradingQueryUCCquerydailytradingAddr" id="PA_querydailytrading" implementorClass="com.nci.tunan.pa.interfaces.dailytradingquery.exports.idailytradingqueryucc.querydailytrading.ws.IDailyTradingQueryUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.dailytradingquery.exports.idailytradingqueryucc.querydailytrading.ws.DailyTradingQueryUCCWSImpl" id="PA_DailyTradingQueryUCCWSImplquerydailytrading">
				<property name="ucc" ref="PA_dailyTradingQueryUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 接入渠道-日终汇总对账 -->
	<!-- address属性是调用webservice时需要使用的地址 -->

	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/dayEndReconciliationUCCqueryByTransRefAddr" id="PA_queryByTransRef" implementorClass="com.nci.tunan.pa.interfaces.dayendreconciliation.exports.idayendreconciliationucc.querybytransref.ws.IDayEndReconciliationUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.dayendreconciliation.exports.idayendreconciliationucc.querybytransref.ws.DayEndReconciliationUCCWSImpl" id="PA_DayEndReconciliationUCCWSImplqueryByTransRef">
				<property name="ucc" ref="PA_dayEndReconciliationUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/dayDetailCheckUCCdayDetailCheckAddr" id="PA_dayDetailCheck" implementorClass="com.nci.tunan.pa.interfaces.daydetailcheck.exports.idaydetailcheckucc.daydetailcheck.ws.IDayDetailCheckUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.pa.impl.daydetailcheck.exports.idaydetailcheckucc.daydetailcheck.ws.DayDetailCheckUCCWSImpl" id="PA_DayDetailCheckUCCWSImpldayDetailCheck">
				<property name="ucc" ref="PA_dayDetailCheckUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 投连价格查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryCowPriceListUccqueryCowPriceListAddr" id="PA_queryCowPriceList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000939.iquerycowpricelistucc.querycowpricelist.ws.IQueryCowPriceListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000939.iquerycowpricelistucc.querycowpricelist.ws.QueryCowPriceListUccWSImpl" id="PA_QueryCowPriceListUccWSImplqueryCowPriceList">
				<property name="ucc" ref="PA_IQueryCowPriceListUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 保单投资单位数及价格 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryUuitPriceListUccqueryUuitPriceListAddr" id="PA_queryUuitPriceList" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000843.iqueryuuitpricelistucc.queryuuitpricelist.ws.IQueryUuitPriceListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000843.iqueryuuitpricelistucc.queryuuitpricelist.ws.QueryUuitPriceListUccWSImpl" id="PA_QueryUuitPriceListUccWSImplqueryUuitPriceList">
				<property name="ucc" ref="PA_IQueryUuitPriceListUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 投连退保查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryTuiBaoUccqueryTuiBaoAddr" id="PA_queryTuiBao" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101000851.iquerytuibaoucc.querytuibao.ws.IQueryTuiBaoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101000851.iquerytuibaoucc.querytuibao.ws.QueryTuiBaoUccWSImpl" id="PA_QueryTuiBaoUccWSImplqueryTuiBao">
				<property name="ucc" ref="PA_IQueryTuiBaoUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 保单挂起状态查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryGuaQiUccqueryGuaQiAddr" id="PA_queryGuaQi" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101001000.iqueryguaqiucc.queryguaqi.ws.IQueryGuaQiUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.peripheral.exports.r00101001000.iqueryguaqiucc.queryguaqi.ws.QueryGuaQiUccWSImpl" id="PA_QueryGuaQiUccWSImplqueryGuaQi">
				<property name="ucc" ref="PA_IQueryGuaQiUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 接入渠道保单查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyDetailInfoUCCqueryPolicyDetailInfoAddr" id="PA_queryPolicyDetailInfo" implementorClass="com.nci.tunan.pa.interfaces.querypolicydetailinfo.exports.iquerypolicydetailinfoucc.querypolicydetailinfo.ws.IQueryPolicyDetailInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.querypolicydetailinfo.exports.iquerypolicydetailinfoucc.querypolicydetailinfo.ws.QueryPolicyDetailInfoUCCWSImpl" id="PA_QueryPolicyDetailInfoUCCWSImplqueryPolicyDetailInfo">
				<property name="ucc" ref="PA_queryPolicyDetailInfoUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 投保单号快速查询是否存在服务 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/judgeApplyCodeExitUccjudgeApplyCodeExitAddr" id="PA_judgeApplyCodeExit" implementorClass="com.nci.tunan.pa.interfaces.judgeapplycodeexit.exports.ijudgeapplycodeexitucc.judgeapplycodeexit.ws.IJudgeApplyCodeExitUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.judgeapplycodeexit.exports.ijudgeapplycodeexitucc.judgeapplycodeexit.ws.JudgeApplyCodeExitUccWSImpl" id="PA_JudgeApplyCodeExitUccWSImpljudgeApplyCodeExit">
				<property name="ucc" ref="PA_judgeApplyCodeExitUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/secKillExpiryPolicySyncUCCgetExpiryPolicyDataInfoAddr" id="PA_getExpiryPolicyDataInfo" implementorClass="com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.ws.ISecKillExpiryPolicySyncUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.ws.SecKillExpiryPolicySyncUCCWSImpl" id="PA_SecKillExpiryPolicySyncUCCWSImplgetExpiryPolicyDataInfo">
				<property name="ucc" ref="PA_secKillExpiryPolicySyncUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- (银行)犹豫期内退保 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/policyRiskUCCcalRiskAddr" id="PA_calRisk" implementorClass="com.nci.tunan.pa.interfaces.policyrisk.exports.ipolicyriskucc.calrisk.ws.IPolicyRiskUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.pa.impl.policyrisk.exports.ipolicyriskucc.calrisk.ws.PolicyRiskUCCWSImpl" id="PA_PolicyRiskUCCWSImplcalRisk">
				<property name="ucc" ref="PA_policyRiskUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 外围系统调用调用保全接口保存影像返回值信息，并关联影像 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/csScanImageUCCImplDealsavaScanceImageInfoAddr" id="PA_savaScanceImageInfo" implementorClass="com.nci.tunan.cs.interfaces.saveImageInfo.exports.icsscanimageuccdeal.savascanceimageinfo.ws.ICsScanImageUCCDealWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.cs.impl.saveImageInfo.exports.icsscanimageuccdeal.savascanceimageinfo.ws.CsScanImageUCCDealWSImpl" id="PA_CsScanImageUCCDealWSImplsavaScanceImageInfo">
				<property name="ucc" ref="PA_csScanImageUCCImplDeal">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 核心质检查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/cssInsQualicyUCCqueryCssInsResultByCsAddr" id="PA_queryCssInsResultByCs" implementorClass="com.nci.tunan.cs.interfaces.CssInsQuality.exports.icssinsqualicyucc.querycssinsresultbycs.ws.ICssInsQualicyUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.cs.impl.CssInsQuality.exports.icssinsqualicyucc.querycssinsresultbycs.ws.CssInsQualicyUCCWSImpl" id="PA_CssInsQualicyUCCWSImplqueryCssInsResultByCs">
				<property name="ucc" ref="PA_cssInsQualicyUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
    
    <jaxws:endpoint address="/pAInvestUnitPriceUCCImplsynInvestUnitPriceDataAddr" id="PA_synInvestUnitPriceData" implementorClass="com.nci.tunan.pa.interfaces.boxpa.ipainvestunitpriceucc.syninvestunitpricedata.ws.IPAInvestUnitPriceUCCWS">
        <jaxws:implementor>
            <!-- class对应服务发布接口的实现类 -->
            <bean class="com.nci.tunan.pa.impl.boxpa.ipainvestunitpriceucc.syninvestunitpricedata.ws.PAInvestUnitPriceUCCWSImpl" id="PA_synInvestUnitPriceDataImpl">
                <property name="ucc" ref="PA_InvestUnitPriceUCC">
                    
                </property>
            </bean>
        </jaxws:implementor>
    </jaxws:endpoint>
    
    
    <!-- implementorClass 是定义服务的接口类 -->
    <jaxws:endpoint id="findInvestUnitsDaysum" address="/PA_InvestUnitsDaysumUccfindInvestUnitsDaysumAddr" implementorClass="com.nci.tunan.pa.interfaces.boxpa.exports.iinvestunitsdaysumucc.findinvestunitsdaysum.ws.IInvestUnitsDaysumUCCWS">     
        <jaxws:implementor>
            <!-- class对应服务发布接口的实现类  -->
            <bean id="InvestUnitsDaysumUCCWSImplfindInvestUnitsDaysum" class="com.nci.tunan.pa.impl.boxpa.exports.iinvestunitsdaysumucc.findinvestunitsdaysum.ws.InvestUnitsDaysumUCCWSImpl">
                <property name="ucc">
                    <ref bean="PA_InvestUnitsDaysumUcc" />
                </property>
            </bean>
        </jaxws:implementor>
    </jaxws:endpoint>
    
    <!-- address属性是调用webservice时需要使用的地址  -->
    <!-- implementorClass 是定义服务的接口类 -->
    <jaxws:endpoint id="investAccountDataQueryUCC" address="/IInvestAccountDataQueryUCCinvestAccountDataQueryUCCAddr" implementorClass="com.nci.tunan.pa.interfaces.boxpa.exports.iinvestaccountdataqueryucc.investaccountdataqueryucc.ws.IInvestAccountDataQueryUCCWS">        
        <jaxws:implementor>
            <!-- class对应服务发布接口的实现类  -->
            <bean id="InvestAccountDataQueryUCCWSImplinvestAccountDataQueryUCC" class="com.nci.tunan.pa.impl.boxpa.exports.iinvestaccountdataqueryucc.investaccountdataqueryucc.ws.InvestAccountDataQueryUCCWSImpl">
                <property name="ucc">
                    <ref bean="PA_InvestAccountDataQueryUCC" />
                </property>
            </bean>
        </jaxws:implementor>
    </jaxws:endpoint>
      
    <!-- 保单验真查询接口 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyVerify" address="/PA_queryPolicyVerifyUccqueryPolicyVerifyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r00101002484.iquerypolicyverifyucc.querypolicyverify.ws.IQueryPolicyVerifyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyVerifyUccWSImplqueryPolicyVerify" class="com.nci.tunan.pa.impl.peripheral.exports.r00101002484.iquerypolicyverifyucc.querypolicyverify.ws.QueryPolicyVerifyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyVerifyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
    
    <!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryOrphanPolicy" address="/orphanPolicyUCCqueryOrphanPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.orphanpolicy.exports.iorphanpolicyucc.queryorphanpolicy.ws.IOrphanPolicyUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="OrphanPolicyUCCWSImplqueryOrphanPolicy" class="com.nci.tunan.pa.impl.orphanpolicy.exports.iorphanpolicyucc.queryorphanpolicy.ws.OrphanPolicyUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_orphanPolicyUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="getRetureNotice" address="/PA_orphanPolicyNoticeUCCgetRetureNoticeAddr" implementorClass="com.nci.tunan.pa.interfaces.orphannoticepolicy.exports.iorphanpolicynoticeucc.getreturenotice.ws.IOrphanPolicyNoticeUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="OrphanPolicyNoticeUCCWSImplgetRetureNotice" class="com.nci.tunan.pa.impl.orphannoticepolicy.exports.iorphanpolicynoticeucc.getreturenotice.ws.OrphanPolicyNoticeUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_orphanPolicyNoticeUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCashValueList" address="/PA_queryCashValueListUCCqueryCashValueListAddr" implementorClass="com.nci.tunan.pa.interfaces.querycashvaluelist.exports.iquerycashvaluelistucc.querycashvaluelist.ws.IQueryCashValueListUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCashValueListUCCWSImplqueryCashValueList" class="com.nci.tunan.pa.impl.querycashvaluelist.exports.iquerycashvaluelistucc.querycashvaluelist.ws.QueryCashValueListUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryCashValueListUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="errorCorrectDeal" address="/PA_errorCorrectDealUCCerrorCorrectDealAddr" implementorClass="com.nci.tunan.pa.interfaces.errorcorrectdeal.exports.ierrorcorrectdealucc.errorcorrectdeal.ws.IErrorCorrectDealUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ErrorCorrectDealUCCWSImplerrorCorrectDeal" class="com.nci.tunan.pa.impl.errorcorrectdeal.exports.ierrorcorrectdealucc.errorcorrectdeal.ws.ErrorCorrectDealUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_errorCorrectDealUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="postLostDeal" address="/PA_postLostDealUCCpostLostDealAddr" implementorClass="com.nci.tunan.pa.interfaces.nbdatadeal.exports.ipostlostdealucc.postlostdeal.ws.IPostLostDealUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PostLostDealUCCWSImplpostLostDeal" class="com.nci.tunan.pa.impl.nbdatadeal.exports.ipostlostdealucc.postlostdeal.ws.PostLostDealUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_postLostDealUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="calcOptBonusSA" address="/PA_calcOptBonusSAUCCcalcOptBonusSAAddr" implementorClass="com.nci.tunan.pa.interfaces.calc.exports.icalcoptbonussaucc.calcoptbonussa.ws.ICalcOptBonusSAUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CalcOptBonusSAUCCWSImplcalcOptBonusSA" class="com.nci.tunan.pa.impl.calc.exports.icalcoptbonussaucc.calcoptbonussa.ws.CalcOptBonusSAUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_calcOptBonusSAUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="registLedger" address="/PA_registLedgerUCCregistLedgerAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iregistledgerucc.registledger.ws.IRegistLedgerUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RegistLedgerUCCWSImplregistLedger" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iregistledgerucc.registledger.ws.RegistLedgerUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_registLedgerUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="confirmRegistLedger" address="/PA_registLedgerUCCconfirmRegistLedgerAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iregistledgerucc.confirmregistledger.ws.IRegistLedgerUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RegistLedgerUCCWSImplconfirmRegistLedger" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iregistledgerucc.confirmregistledger.ws.RegistLedgerUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_registLedgerUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="revokeRegistLedger" address="/PA_registLedgerUCCrevokeRegistLedgerAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iregistledgerucc.revokeregistledger.ws.IRegistLedgerUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RegistLedgerUCCWSImplrevokeRegistLedger" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iregistledgerucc.revokeregistledger.ws.RegistLedgerUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_registLedgerUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="resultRenewalAmend" address="/PA_renewalAmendPAUCCresultRenewalAmendAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.irenewalamendpaucc.resultrenewalamend.ws.IRenewalAmendPAUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RenewalAmendPAUCCWSImplresultRenewalAmend" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.irenewalamendpaucc.resultrenewalamend.ws.RenewalAmendPAUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_renewalAmendPAUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="checkHolderMessage" address="/PA_customerInfoCheckUcccheckHolderMessageAddr" implementorClass="com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.ws.ICustomerInfoCheckUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustomerInfoCheckUccWSImplcheckHolderMessage" class="com.nci.tunan.pa.impl.commonQuery.exports.icustomerinfocheckucc.checkholdermessage.ws.CustomerInfoCheckUccWSImpl">
				<property name="ucc">
					<ref bean="PA_customerInfoCheckUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="statusReturn" address="/PA_policyStatusReturnUccstatusReturnAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicystatusreturnucc.statusreturn.ws.IPolicyStatusReturnUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyStatusReturnUccWSImplstatusReturn" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicystatusreturnucc.statusreturn.ws.PolicyStatusReturnUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyStatusReturnUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="policyDetailQuery" address="/PA_policyDetailUCCpolicyDetailQueryAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicydetailucc.policydetailquery.ws.IPolicyDetailUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyDetailUCCWSImplpolicyDetailQuery" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicydetailucc.policydetailquery.ws.PolicyDetailUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_policyDetailUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCustemRelation" address="/PA_customerRelanUccqueryCustemRelationAddr" implementorClass="com.nci.tunan.pa.interfaces.commonquery.exports.icustemrrelanucc.querycustemrelation.ws.ICustemrRelanUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustemrRelanUccWSImplqueryCustemRelation" class="com.nci.tunan.pa.impl.commonQuery.exports.icustemrrelanucc.querycustemrelation.ws.CustemrRelanUccWSImpl">
				<property name="ucc">
					<ref bean="PA_customerRelanUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

    <!-- 非实时保单查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="policyQueryDelayed" address="/PA_policyQueryDelayedUccpolicyQueryDelayedAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.ws.IPolicyQueryDelayedUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyQueryDelayedUccWSImplpolicyQueryDelayed" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.ws.PolicyQueryDelayedUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyQueryDelayedUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="policyAcknowledgementUpdate" address="/policyAcknowledgementUpdateUCCpolicyAcknowledgementUpdateWSAddr" implementorClass="com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.ws.IPolicyAcknowledgementUpdateUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyAcknowledgementUpdateUCCWSImplpolicyAcknowledgementUpdate" class="com.nci.tunan.pa.impl.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.ws.PolicyAcknowledgementUpdateUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_policyAcknowledgementUpdateUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="insertPolicy" address="/createPolicyUCCinsertPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.ws.ICreatePolicyUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CreatePolicyUCCWSImplinsertPolicy" class="com.nci.tunan.pa.impl.createpolicy.exports.icreatepolicyucc.insertpolicy.ws.CreatePolicyUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_createPolicyUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 柜面人力接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCssWarningConfigCount" address="/PA_cssWarningConfigUCCqueryCssWarningConfigCountAddr" implementorClass="com.nci.tunan.cs.interfaces.cssWarningConfig.exports.icscwarningconfigucc.querycsswarningconfigcount.ws.ICscWarningConfigUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CscWarningConfigUCCWSImplqueryCssWarningConfigCount" class="com.nci.tunan.cs.impl.cssWarningConfig.exports.icscwarningconfigucc.querycsswarningconfigcount.ws.CscWarningConfigUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_cssWarningConfigUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 续期缴费接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="renewalPayment" address="/PA_paRenewalPaymentUCCrenewalPaymentAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iparenewalpaymentucc.renewalpayment.ws.IPaRenewalPaymentUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PaRenewalPaymentUCCWSImplrenewalPayment" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iparenewalpaymentucc.renewalpayment.ws.PaRenewalPaymentUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_paRenewalPaymentUCC" />
				</property>
			</bean>
		</jaxws:implementor>
		<!--建行查询保单历史变动信息接口  -->
	</jaxws:endpoint>
		<jaxws:endpoint id="queryPolicyHisForCCB" address="/PA_QueryPolicyHisForCCBUCCqueryPolicyHisForCCBAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iquerypolicyhisforccbucc.querypolicyhisforccb.ws.IQueryPolicyHisForCCBUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyHisForCCBUCCWSImplqueryPolicyHisForCCB" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iquerypolicyhisforccbucc.querypolicyhisforccb.ws.QueryPolicyHisForCCBUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryPolicyHisForCCBUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 延长宽限期天数查询接口 -->
	<jaxws:endpoint id="queryExtendDays" address="/PA_queryExtendGracePeriodDaysUCCqueryExtendDaysAddr" implementorClass="com.nci.tunan.pa.interfaces.queryextendgraceperioddays.exports.iqueryextendgraceperioddaysucc.queryextenddays.ws.IQueryExtendGracePeriodDaysUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryExtendGracePeriodDaysUCCWSImplqueryExtendDays" class="com.nci.tunan.pa.impl.queryExtendGracePeriodDays.exports.iqueryextendgraceperioddaysucc.queryextenddays.ws.QueryExtendGracePeriodDaysUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryExtendGracePeriodDaysUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单金额查询接口 -->
	<jaxws:endpoint id="queryAmount" address="/PA_queryPolicyAmountUCCqueryAmountAddr" implementorClass="com.nci.tunan.pa.interfaces.querypolicyamount.exports.iquerypolicyamountucc.queryamount.ws.IQueryPolicyAmountUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyAmountUccWSImplqueryAmount" class="com.nci.tunan.pa.impl.queryPolicyAmount.exports.iquerypolicyamountucc.queryamount.ws.QueryPolicyAmountUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyAmountUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 查询客户是否存在有效保单 -->
	<jaxws:endpoint id="queryIsExitPolicy" address="/PA_isHesitationPeriodUCCqueryIsExitPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.ws.IIsHesitationPeriodUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="IsHesitationPeriodUCCWSImplqueryIsExitPolicy" class="com.nci.tunan.pa.impl.IsHesitationPeriod.exports.iishesitationperioducc.queryisexitpolicy.ws.IsHesitationPeriodUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_isHesitationPeriodUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 开门红撤单 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="updatePasList" address="/aGoodStartUCCupdatePasListAddr" implementorClass="com.nci.tunan.pa.interfaces.agoodstart.exports.iagoodstartucc.updatepaslist.ws.IAGoodStartUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AGoodStartUCCWSImplupdatePasList" class="com.nci.tunan.pa.impl.aGoodStart.exports.iagoodstartucc.updatepaslist.ws.AGoodStartUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_aGoodStartUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

    <!-- 非银保通出单查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="policyQueryNonYbt" address="/PA_policyQueryNonYbtUccpolicyQueryNonYbtAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerynonybtucc.policyquerynonybt.ws.IPolicyQueryNonYbtUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyQueryNonYbtUccWSImplpolicyQueryNonYbt" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyquerynonybtucc.policyquerynonybt.ws.PolicyQueryNonYbtUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyQueryNonYbtUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint> 
	
	<!-- 柜面自助：贷款续贷详情查询接口  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryContinuedLoan" address="/IQueryLoanContinuedLoanUCCqueryContinuedLoanAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401003255.iqueryloancontinuedloanucc.querycontinuedloan.ws.IQueryLoanContinuedLoanUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryLoanContinuedLoanUCCWSImplqueryContinuedLoan" class="com.nci.tunan.pa.impl.peripheral.exports.r06401003255.iqueryloancontinuedloanucc.querycontinuedloan.ws.QueryLoanContinuedLoanUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_QueryLoanContinuedLoanUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="updateRelationPolicyCodeservice" address="/PA_updateRelationPolicyCodeUCCupdateRelationPolicyCodeserviceAddr" implementorClass="com.nci.tunan.pa.interfaces.updaterelationpolicycode.exports.ws.IupdateRelationPolicyCodeUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="updateRelationPolicyCodeUCCWSImplupdateRelationPolicyCodeservice" class="com.nci.tunan.pa.impl.updaterelationpolicycode.exports.ws.updateRelationPolicyCodeUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_updateRelationPolicyCodeUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--投被保人关系查询接口 -->
	<jaxws:endpoint id="queryHolderRelationInsured" address="/holderRelationInsuredServicequeryHolderRelationInsuredAddr" implementorClass="com.nci.tunan.cs.interfaces.peripheral.exports.r06401003110.iholderrelationinsureducc.queryholderrelationinsured.ws.IHolderRelationInsuredUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="HolderRelationInsuredUccWSImplqueryHolderRelationInsured" class="com.nci.tunan.cs.impl.peripheral.exports.r06401003110.iholderrelationinsureducc.queryholderrelationinsured.ws.HolderRelationInsuredUccWSImpl">
				<property name="ucc">
					<ref bean="PA_holderRelationInsuredUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 根据投保单号查询风险保额 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 根据投保单号查询风险保额-->
	<jaxws:endpoint id="policyRiskAmountByApplyCode" address="/PA_PolicyRiskInfopolicyRiskAmountByApplyCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.policyinfo.exports.ipolicyriskinfoucc.policyriskamountbyapplycode.ws.IPolicyRiskInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyRiskInfoUccWSImplpolicyRiskAmountByApplyCode" class="com.nci.tunan.pa.impl.policyinfo.exports.ipolicyriskinfoucc.policyriskamountbyapplycode.ws.PolicyRiskInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_PolicyRiskInfo" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 风险保额累计信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="findRiskAmountByApplyCode" address="/PA_RiskAmountInterUccfindRiskAmountByApplyCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.riskamount.exports.iriskamountinterucc.findriskamountbyapplycode.ws.IRiskAmountInterUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RiskAmountInterUCCWSImplfindRiskAmountByApplyCode" class="com.nci.tunan.pa.impl.riskamount.exports.iriskamountinterucc.findriskamountbyapplycode.ws.RiskAmountInterUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_RiskAmountInterUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	 
	 	<!-- 根据投被保人五要素查询保单信息 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!--  -->
	<jaxws:endpoint id="queryPolicyByFiveElements" address="/PA_queryPolicyByFiveElementsUccqueryPolicyByFiveElementsAddr" implementorClass="com.nci.tunan.pa.interfaces.querypolicybyfiveelementsinfo.exports.iquerypolicybyfiveelementsucc.querypolicybyfiveelements.ws.IQueryPolicyByFiveElementsUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyByFiveElementsUccWSImplqueryPolicyByFiveElements" class="com.nci.tunan.pa.impl.queryPolicyByFiveElementsInfo.exports.iquerypolicybyfiveelementsucc.querypolicybyfiveelements.ws.QueryPolicyByFiveElementsUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyByFiveElementsUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<!-- 续期查询（接入渠道）-->
	<jaxws:endpoint id="queryRenewalPolicy" address="/IQueryRenewalPolicyUCCqueryRenewalPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iqueryrenewalpolicyucc.queryrenewalpolicy.ws.IQueryRenewalPolicyUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryRenewalPolicyUCCWSImplqueryRenewalPolicy" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iqueryrenewalpolicyucc.queryrenewalpolicy.ws.QueryRenewalPolicyUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryRenewalPolicyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 承保保单统计接口 -->
	<jaxws:endpoint id="underwritingPolicyStatistics" address="/IUnderwritingPolicyStatisticsUCCunderwritingPolicyStatisticsAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iunderwritingpolicystatisticsucc.underwritingpolicystatistics.ws.IUnderwritingPolicyStatisticsUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UnderwritingPolicyStatisticsUCCWSImplunderwritingPolicyStatistics" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iunderwritingpolicystatisticsucc.underwritingpolicystatistics.ws.UnderwritingPolicyStatisticsUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_UnderwritingPolicyStatisticsUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 保单状态更新接口 -->
	<jaxws:endpoint id="updatePolicyState" address="/IUpdatePolicyStateUCCupdatePolicyStateAddr" implementorClass="com.nci.tunan.pa.interfaces.updatepolicystate.exports.iupdatepolicystateucc.updatepolicystate.ws.IUpdatePolicyStateUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UpdatePolicyStateUCCWSImplupdatePolicyState" class="com.nci.tunan.pa.impl.updatepolicystate.exports.iupdatepolicystateucc.updatepolicystate.ws.UpdatePolicyStateUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_updatePolicyStateUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		<!-- 保单状态数据信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyState" address="/PA_policyStatusUccqueryPolicyStateAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicystatusucc.querypolicystate.ws.IPolicyStatusUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyStatusUccWSImplqueryPolicyState" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicystatusucc.querypolicystate.ws.PolicyStatusUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyStatusUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 契约-保单状态查询接口 -->
	<jaxws:endpoint id="queryPolicyStatu" address="/IQueryPolicyStatusUCCqueryPolicyStatuAddr" implementorClass="com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.ws.IQueryPolicyStatusUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyStatusUCCWSImplqueryPolicyStatu" class="com.nci.tunan.pa.impl.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.ws.QueryPolicyStatusUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyStatusUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 住院津贴限额校验接口 -->
	<jaxws:endpoint id="checkAccumulatedhospitalization" address="/AccumulatedhospitalizationUCCImplcheckAccumulatedhospitalizationAddr" implementorClass="com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.ws.IAccumulatedhospitalizationUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AccumulatedhospitalizationUCCWSImplcheckAccumulatedhospitalization" class="com.nci.tunan.pa.impl.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.ws.AccumulatedhospitalizationUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_accumulatedhospitalizationUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 接入渠道交通银行-非实时保单查询 -->
	<jaxws:endpoint id="policyQueryDelayedJTYH" address="/PolicyQueryDelayedJTYHUccImplpolicyQueryDelayedAddr" implementorClass="com.nci.tunan.pa.interfaces.accesschannelquerypolicy.exports.ipolicyquerydelayedjtyhucc.policyquerydelayed.ws.IPolicyQueryDelayedJTYHUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyQueryDelayedJTYHUccWSImplpolicyQueryDelayed" class="com.nci.tunan.pa.impl.accesschannelquerypolicy.exports.ipolicyquerydelayedjtyhucc.policyquerydelayed.ws.PolicyQueryDelayedJTYHUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyQueryDelayedJTYHUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 第二年末现金价值查询接口 -->
	<jaxws:endpoint id="printCVQueryTwo" address="/PasPrintCVQueryUccImplprintCVQueryTwoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900468.ipasprintcvqueryucc.printcvquerytwo.ws.IPasPrintCVQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PasPrintCVQueryUccWSImplprintCVQueryTwo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900468.ipasprintcvqueryucc.printcvquerytwo.ws.PasPrintCVQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_pasPrintCVQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 保单贷款保单查询接口 -->
	<jaxws:endpoint id="queryLNPolicyQueryYDBQ" address="/LNPolicyQueryYDBQUccImplqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900483.ilnpolicyqueryydbqucc.querypolicy.ws.ILNPolicyQueryYDBQUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="LNPolicyQueryYDBQUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900483.ilnpolicyqueryydbqucc.querypolicy.ws.LNPolicyQueryYDBQUccWSImpl">
				<property name="ucc">
					<ref bean="PA_LNPolicyQueryYDBQUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 保单续贷保单列表查询接口 -->
	<jaxws:endpoint id="queryRLPolicyQueryQDBQ" address="/RLPolicyQueryYDBQUccImplqueryPolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900472.irlpolicyqueryydbqucc.querypolicy.ws.IRLPolicyQueryYDBQUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RLPolicyQueryYDBQUccWSImplqueryPolicy" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900472.irlpolicyqueryydbqucc.querypolicy.ws.RLPolicyQueryYDBQUccWSImpl">
				<property name="ucc">
					<ref bean="PA_RLPolicyQueryYDBQUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 保单承保信息更新接口-->
	<jaxws:endpoint id="updatePolicy" address="/UpdatePolicyInfoUCCImplupdatePolicyAddr" implementorClass="com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.ws.IUpdatePolicyInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UpdatePolicyInfoUCCWSImplupdatePolicy" class="com.nci.tunan.pa.impl.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.ws.UpdatePolicyInfoUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_updatePolicyPAInfoUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 投保人手机号及首期银行账号重复查询接口-->
	<jaxws:endpoint id="queryAbnormalCustomer" address="/QueryAbnormalCustomerUCCImplqueryAbnormalCustomerAddr" implementorClass="com.nci.tunan.pa.interfaces.queryAbnormalCustomer.exports.iqueryabnormalcustomerucc.queryabnormalcustomer.ws.IQueryAbnormalCustomerUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAbnormalCustomerUCCWSImplqueryAbnormalCustomer" class="com.nci.tunan.pa.impl.queryAbnormalCustomer.exports.iqueryabnormalcustomerucc.queryabnormalcustomer.ws.QueryAbnormalCustomerUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryAbnormalCustomerUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 团险风险保额累计接口-->
	<jaxws:endpoint id="findGroupRiskAcc" address="/GroupRiskAccUCCImplfindGroupRiskAccAddr" implementorClass="com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.ws.IGroupRiskAccUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="GroupRiskAccUCCWSImplfindGroupRiskAcc" class="com.nci.tunan.pa.impl.groupRisk.exports.igroupriskaccucc.findgroupriskacc.ws.GroupRiskAccUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_groupRiskAccUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 保单状态查询接口 -->
	<jaxws:endpoint id="queryPolicyStatuByPolicyCode" address="/PA_queryPolicyStatusByPolicyCodeUCCqueryPolicyStatuByPolicyCodeAddr" implementorClass="com.nci.tunan.pa.interfaces.querypolicystatusbypolicycode.exports.iquerypolicystatusbypolicycodeucc.querypolicystatubypolicycode.ws.IQueryPolicyStatusByPolicyCodeUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyStatusByPolicyCodeUCCWSImplqueryPolicyStatuByPolicyCode" class="com.nci.tunan.pa.impl.querypolicystatusbypolicycode.exports.iquerypolicystatusbypolicycodeucc.querypolicystatubypolicycode.ws.QueryPolicyStatusByPolicyCodeUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyStatusByPolicyCodeUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 特殊人员类型校验接口 -->
	<jaxws:endpoint id="checkSpecial" address="/PA_SpecialPerTypeCheckUcccheckSpecialAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900718.ispecialpertypecheckucc.checkspecial.ws.ISpecialPerTypeCheckUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="SpecialPerTypeCheckUccWSImplcheckSpecial" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900718.ispecialpertypecheckucc.checkspecial.ws.SpecialPerTypeCheckUccWSImpl">
				<property name="ucc">
					<ref bean="PA_SpecialPerTypeCheckUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 个人（投保人）保单信息查询接口 -->
	<jaxws:endpoint id="queryPolicayInfoByCustmoerInfo" address="/PA_PersonalinsurancePolicyUccqueryPolicayInfoByCustmoerInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900708.ipersonalinsurancepolicyucc.querypolicayinfobycustmoerinfo.ws.IpersonalinsurancePolicyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="personalinsurancePolicyUccWSImplqueryPolicayInfoByCustmoerInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900708.ipersonalinsurancepolicyucc.querypolicayinfobycustmoerinfo.ws.PersonalinsurancePolicyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_PersonalinsurancePolicyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>	
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyHolderInfo" address="/PA_policyHolderInfoQueryUccqueryPolicyHolderInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyholderinfoqueryucc.querypolicyholderinfo.ws.IPolicyHolderInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyHolderInfoQueryUccWSImplqueryPolicyHolderInfo" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyholderinfoqueryucc.querypolicyholderinfo.ws.PolicyHolderInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyHolderInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyIncome" address="/PA_policyIncomeQueryUccqueryPolicyIncomeAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyincomequeryucc.querypolicyincome.ws.IPolicyIncomeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyIncomeQueryUccWSImplqueryPolicyIncome" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyincomequeryucc.querypolicyincome.ws.PolicyIncomeQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyIncomeQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="updataContractMasterMediaType" address="/PA_policyMediaTypeUccupdataContractMasterMediaTypeAddr" implementorClass="com.nci.tunan.pa.interfaces.policymediatype.exports.ipolicymediatypeucc.updatacontractmastermediatype.ws.IPolicyMediaTypeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyMediaTypeUccWSImplupdataContractMasterMediaType" class="com.nci.tunan.pa.impl.policymediatype.exports.ipolicymediatypeucc.updatacontractmastermediatype.ws.PolicyMediaTypeUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyMediaTypeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 银保通插入个人养老金信息报送表接口 -->
	<jaxws:endpoint id="insertSubmitMessageBocic" address="/PA_submitMessageBocicYBTUCCinsertSubmitMessageBocicAddr" implementorClass="com.nci.tunan.pa.interfaces.submitMessageBocicYBT.exports.isubmitmessagebocicybtucc.insertsubmitmessagebocic.ws.ISubmitMessageBocicYBTUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="SubmitMessageBocicYBTUCCWSImplinsertSubmitMessageBocic" class="com.nci.tunan.pa.impl.submitMessageBocicYBT.exports.isubmitmessagebocicybtucc.insertsubmitmessagebocic.ws.SubmitMessageBocicYBTUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_submitMessageBocicYBTUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryBonusInfoList" address="/PA_queryBonusInfoListUccqueryBonusInfoListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900826.iquerybonusinfolistucc.querybonusinfolist.ws.IQueryBonusInfoListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryBonusInfoListUccWSImplqueryBonusInfoList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900826.iquerybonusinfolistucc.querybonusinfolist.ws.QueryBonusInfoListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryBonusInfoListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryAccumulateCapitalList" address="/PA_queryAccumulateCapitalListUccqueryAccumulateCapitalListAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900827.iqueryaccumulatecapitallistucc.queryaccumulatecapitallist.ws.IQueryAccumulateCapitalListUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAccumulateCapitalListUccWSImplqueryAccumulateCapitalList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900827.iqueryaccumulatecapitallistucc.queryaccumulatecapitallist.ws.QueryAccumulateCapitalListUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryAccumulateCapitalListUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfoByCustomInfo" address="/PA_queryPolicyInfoByCustomInfoUccqueryPolicyInfoByCustomInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900870.iquerypolicyinfobycustominfoucc.querypolicyinfobycustominfo.ws.IQueryPolicyInfoByCustomInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyInfoByCustomInfoUccWSImplqueryPolicyInfoByCustomInfo" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900870.iquerypolicyinfobycustominfoucc.querypolicyinfobycustominfo.ws.QueryPolicyInfoByCustomInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_queryPolicyInfoByCustomInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 手续费对账查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="serviceChargeReconciliation" address="/PA_serviceChargeReconciliationUccserviceChargeReconciliationAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.iservicechargereconciliationucc.servicechargereconciliation.ws.IServiceChargeReconciliationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ServiceChargeReconciliationUccWSImplserviceChargeReconciliation" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.iservicechargereconciliationucc.servicechargereconciliation.ws.ServiceChargeReconciliationUccWSImpl">
				<property name="ucc">
					<ref bean="PA_serviceChargeReconciliationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 身故受益人详情查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="dieBenefitInfo" address="/PA_dieBenefitInfoUccdieBenefitInfoAddr" implementorClass="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.idiebenefitinfoucc.diebenefitinfo.ws.IDieBenefitInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="DieBenefitInfoUccWSImpldieBenefitInfo" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.idiebenefitinfoucc.diebenefitinfo.ws.DieBenefitInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_dieBenefitInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="renewalOfPensionSurvivalSub" address="/PA_renewalOfPensionSurvivalSubUccrenewalOfPensionSurvivalSubAddr" implementorClass="com.nci.tunan.pa.interfaces.peripheral.exports.r06402900922.irenewalofpensionsurvivalsubucc.renewalofpensionsurvivalsub.ws.IRenewalOfPensionSurvivalSubUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RenewalOfPensionSurvivalSubUccWSImplrenewalOfPensionSurvivalSub" class="com.nci.tunan.pa.impl.peripheral.exports.r06402900922.irenewalofpensionsurvivalsubucc.renewalofpensionsurvivalsub.ws.RenewalOfPensionSurvivalSubUccWSImpl">
				<property name="ucc">
					<ref bean="PA_renewalOfPensionSurvivalSubUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
</beans>