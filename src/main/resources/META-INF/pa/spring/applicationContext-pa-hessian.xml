<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">


	<!-- 理赔查询出险日服务 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/claimQueryPolicyInfoqueryPolicyInfoAddr">
		<property name="service" ref="PA_ClaimQueryPolicyInfoUCCHSImplqueryPolicyInfo"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.hs.IClaimQueryPolicyInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.hs.ClaimQueryPolicyInfoUCCHSImpl" id="PA_ClaimQueryPolicyInfoUCCHSImplqueryPolicyInfo" scope="prototype">
		<property name="ucc" ref="PA_claimQueryPolicyInfo">
			
		</property>
	</bean>

	<!-- 理赔回退 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/clmRollBackUCCrollBackAllDataAddr">
		<property name="service" ref="PA_CLMRollBackUCCHSImplrollBackAllData"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.clmrollback.exports.iclmrollbackucc.rollbackalldata.hs.ICLMRollBackUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.clmrollback.exports.iclmrollbackucc.rollbackalldata.hs.CLMRollBackUCCHSImpl" id="PA_CLMRollBackUCCHSImplrollBackAllData" scope="prototype">
		<property name="ucc" ref="PA_clmRollBackUCC">
			
		</property>
	</bean>

	<!-- 查询投保人信息 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyHolderInfoHSAddr">
		<property name="service" ref="PA_queryPolicyHolderInfoHS"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.query.exports.querypolicyholderinfo.hs.IQueryPolicyHolderInfoHS"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.querypolicyholderinfo.adapter.impl.QueryPolicyHolderInfoHSImpl" id="PA_queryPolicyHolderInfoHS">
		<property name="queryPolicyHolderInfoUCC" ref="PA_queryPolicyHolderInfoUCC"/>
	</bean>

	<!-- 新契约转保单 -->
	<!-- <bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/createPolicyHSAddr">
		<property name="service" ref="PA_createPolicyHS"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.add.exports.createpolicy.hs.ICreatePolicyHS"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.createpolicy.adapter.hs.CreatePolicyHSImpl" id="PA_createPolicyHS">
		<property name="createPolicyUCC" ref="PA_createPolicyUCC"/>
	</bean> -->
	<!-- by liangpl start -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyListHSAddr">
		<property name="service" ref="PA_queryPolicyListHS"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.query.exports.querypolicylist.hs.IQueryPolicyListHS"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.querypolicylist.adapter.QueryPolicyListHSImpl" id="PA_queryPolicyListHS">
		<property name="queryPolicyListUCC" ref="PA_queryPolicyListUCC"/>
	</bean>
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyInfoHSAddr">
		<property name="service" ref="PA_queryPolicyInfoHS"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.query.exports.querypolicyinfo.hs.IQueryPolicyInfoHS"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.querypolicyinfo.adapter.QueryPolicyInfoHSImpl" id="PA_queryPolicyInfoHS">
		<property name="queryPolicyInfoUCC" ref="PA_queryPolicyInfoUCC"/>
	</bean>

	<!-- by liangpl end -->

	<!-- 自核接口 start niuyu_wb 2014-12-09 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/renewalAutoOffsetHSAddr">
		<property name="service" ref="PA_renewalAutoOffsetByHSAdapter"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.hs.IRenewalAutoOffsetByHS"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.renewal.adapter.hs.RenewalAutoOffsetByHSAdapter" id="PA_renewalAutoOffsetByHSAdapter">
		<property name="renewCollectionUCC" ref="PA_renewCollectionUCC"/>
	</bean>
	<!-- 自核接口 start -->

	<!-- 保单回执处理 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/policyAcknowledgementUpdate">
		<property name="service" ref="PA_policyAcknowledgementUpdateHS"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.update.exports.policyacknowledgementupdate.hs.IPolicyAcknowledgementUpdateHS"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.policyacknowledgementupdate.adapter.hs.PolicyAcknowledgementUpdateAdapterHS" id="PA_policyAcknowledgementUpdateHS">
		<property name="policyAcknowledgementUpdateUCC" ref="PA_policyAcknowledgementUpdateUCC"/>
	</bean>
	<!-- 客户告知 start niuyu_wb 2015-01-07 -->
	<bean class="com.nci.tunan.pa.impl.customerSurvey.adapter.hs.CustomerSurveyQueryByHSAdapter" id="PA_customerSurveyQueryByHSAdapter">
		<property name="customerSurveyQueryService" ref="PA_customerSurveyQueryService"/>
	</bean>
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/customerSurveyQueryHSAddr">
		<property name="service" ref="PA_customerSurveyQueryByHSAdapter"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.customerSurveyQuery.exports.hs.ICustomerSurveyQueryByHS"/>
	</bean>
	<!-- 客户告知 end -->
	<!-- 客户保单查询 start niuyu_wb 2015-01-16 -->
	<bean class="com.nci.tunan.pa.impl.customerPolicy.adapter.hs.CustomerPolicyQueryByHSAdapter" id="PA_customerPolicyQueryByHSAdapter">
		<property name="customerPolicyQueryService" ref="PA_customerPolicyQueryService"/>
	</bean>
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/customerPolicyQueryHSAddr">
		<property name="service" ref="PA_customerPolicyQueryByHSAdapter"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.customerPolicyQuery.exports.hs.ICustomerPolicyQueryByHS"/>
	</bean>
	<!-- 客户保单查询 end -->

	<!-- 出险人查询 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyByInsuredHSAddr">
		<property name="service" ref="PA_queryPolicyByInsuredHSAdapter"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.query.exports.queryPolicyByInsured.hs.IQueryPolicyByInsuredHS"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.querypolicybyinsured.adapter.hs.QueryPolicyByInsuredHSImpl" id="PA_queryPolicyByInsuredHSAdapter">
		<property name="queryPolicyListUCC" ref="PA_queryPolicyListUCC"/>
	</bean>

	<!-- 出险人查询 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryInjuredUCCqueryInjuredAddr">
		<property name="service" ref="PA_QueryInjuredUCCHSImplqueryInjured"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.hs.IQueryInjuredUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.queryinjured.exports.iqueryinjureducc.queryinjured.hs.QueryInjuredUCCHSImpl" id="PA_QueryInjuredUCCHSImplqueryInjured" scope="prototype">
		<property name="ucc" ref="PA_queryInjuredUCC">
			
		</property>
	</bean>

	<!-- 抄单接口 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyUCCqueryPolicyAddr">
		<property name="service" ref="PA_QueryPolicyUCCHSImplqueryPolicy"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.hs.IQueryPolicyUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypolicy.exports.iquerypolicyucc.querypolicy.hs.QueryPolicyUCCHSImpl" id="PA_QueryPolicyUCCHSImplqueryPolicy" scope="prototype">
		<property name="ucc" ref="PA_queryPolicyUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryFeeHistoryUCCqueryFeeHistoryAddr">
		<property name="service" ref="PA_QueryFeeHistoryUCCHSImplqueryFeeHistory"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryfeehistory.exports.iqueryfeehistoryucc.queryfeehistory.hs.IQueryFeeHistoryUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.queryfeehistory.exports.iqueryfeehistoryucc.queryfeehistory.hs.QueryFeeHistoryUCCHSImpl" id="PA_QueryFeeHistoryUCCHSImplqueryFeeHistory" scope="prototype">
		<property name="ucc" ref="PA_queryFeeHistoryUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryRoleInfoUCCqueryRoleInfoAddr">
		<property name="service" ref="PA_QueryRoleInfoUCCHSImplqueryRoleInfo"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryroleinfo.exports.iqueryroleinfoucc.queryroleinfo.hs.IQueryRoleInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.queryroleinfo.exports.iqueryroleinfoucc.queryroleinfo.hs.QueryRoleInfoUCCHSImpl" id="PA_QueryRoleInfoUCCHSImplqueryRoleInfo" scope="prototype">
		<property name="ucc" ref="PA_queryRoleInfoUCC">
			
		</property>
	</bean>


	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryUnderwriteUCCqueryUnderwriteAddr">
		<property name="service" ref="PA_QueryUnderwriteUCCHSImplqueryUnderwrite"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.hs.IQueryUnderwriteUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.hs.QueryUnderwriteUCCHSImpl" id="PA_QueryUnderwriteUCCHSImplqueryUnderwrite" scope="prototype">
		<property name="ucc" ref="PA_queryUnderwriteUCC">
			
		</property>
	</bean>


	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/updatePolicyCodeUCCupdatePolicyCodeAddr">
		<property name="service" ref="PA_UpdatePolicyCodeUCCHSImplupdatePolicyCode"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.updatepolicycode.exports.iupdatepolicycodeucc.updatepolicycode.hs.IUpdatePolicyCodeUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.updatepolicycode.exports.iupdatepolicycodeucc.updatepolicycode.hs.UpdatePolicyCodeUCCHSImpl" id="PA_UpdatePolicyCodeUCCHSImplupdatePolicyCode" scope="prototype">
		<property name="ucc" ref="PA_updatePolicyCodeUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/riskAmountUCCcalcRiskAmountAddr">
		<property name="service" ref="PA_RiskAmountUCCHSImplcalcRiskAmount"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.calcriskamount.hs.IRiskAmountUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.risk.exports.iriskamountucc.calcriskamount.hs.RiskAmountUCCHSImpl" id="PA_RiskAmountUCCHSImplcalcRiskAmount" scope="prototype">
		<property name="ucc" ref="PA_riskAmountUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/riskAmountUCCqueryProductRiskDetailAddr">
		<property name="service" ref="PA_RiskAmountUCCHSImplqueryProductRiskDetail"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.hs.IRiskAmountUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.risk.exports.iriskamountucc.queryproductriskdetail.hs.RiskAmountUCCHSImpl" id="PA_RiskAmountUCCHSImplqueryProductRiskDetail" scope="prototype">
		<property name="ucc" ref="PA_riskAmountUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/riskAmountUCCqueryRiskAmountAddr">
		<property name="service" ref="PA_RiskAmountUCCHSImplqueryRiskAmount"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.hs.IRiskAmountUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.risk.exports.iriskamountucc.queryriskamount.hs.RiskAmountUCCHSImpl" id="PA_RiskAmountUCCHSImplqueryRiskAmount" scope="prototype">
		<property name="ucc" ref="PA_riskAmountUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/riskAmountUCCriskClearAddr">
		<property name="service" ref="PA_RiskAmountUCCHSImplriskClear"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.riskclear.hs.IRiskAmountUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.risk.exports.iriskamountucc.riskclear.hs.RiskAmountUCCHSImpl" id="PA_RiskAmountUCCHSImplriskClear" scope="prototype">
		<property name="ucc" ref="PA_riskAmountUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryInsurancePolicyUCCqueryInsurancePolicyAddr">
		<property name="service" ref="PA_QueryInsurancePolicyUCCHSImplqueryInsurancePolicy"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryinsurancepolicy.exports.iqueryinsurancepolicyucc.queryinsurancepolicy.hs.IQueryInsurancePolicyUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.queryinsurancepolicy.exports.iqueryinsurancepolicyucc.queryinsurancepolicy.hs.QueryInsurancePolicyUCCHSImpl" id="PA_QueryInsurancePolicyUCCHSImplqueryInsurancePolicy" scope="prototype">
		<property name="ucc" ref="PA_queryInsurancePolicyUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/checkSignedPremUCCcheckSignedPremAddr">
		<property name="service" ref="PA_CheckSignedPremUCCHSImplcheckSignedPrem"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.checksignedprem.exports.ichecksignedpremucc.checksignedprem.hs.ICheckSignedPremUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.checksignedprem.exports.ichecksignedpremucc.checksignedprem.hs.CheckSignedPremUCCHSImpl" id="PA_CheckSignedPremUCCHSImplcheckSignedPrem" scope="prototype">
		<property name="ucc" ref="PA_checkSignedPremUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/drawDetailUCCqueryDrawDetailListAddr">
		<property name="service" ref="PA_DrawDetailUCCHSImplqueryDrawDetailList"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.drawdetail.exports.idrawdetailucc.querydrawdetaillist.hs.IDrawDetailUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.drawdetail.exports.idrawdetailucc.querydrawdetaillist.hs.DrawDetailUCCHSImpl" id="PA_DrawDetailUCCHSImplqueryDrawDetailList" scope="prototype">
		<property name="ucc" ref="PA_drawDetailUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/underWritingUCCunderWritingAddr">
		<property name="service" ref="PA_UnderWritingUCCHSImplunderWriting"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.underwriting.exports.iunderwritingucc.underwriting.hs.IUnderWritingUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.underwriting.exports.iunderwritingucc.underwriting.hs.UnderWritingUCCHSImpl" id="PA_UnderWritingUCCHSImplunderWriting" scope="prototype">
		<property name="ucc" ref="PA_underWritingUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/notReceiveDrawDetailUCCoutPutDateAddr">
		<property name="service" ref="PA_NotReceiveDrawDetailUCCHSImploutPutDate"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.notreceivedrawdetail.exports.inotreceivedrawdetailucc.outputdate.hs.INotReceiveDrawDetailUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.notreceivedrawdetail.exports.inotreceivedrawdetailucc.outputdate.hs.NotReceiveDrawDetailUCCHSImpl" id="PA_NotReceiveDrawDetailUCCHSImploutPutDate" scope="prototype">
		<property name="ucc" ref="PA_notReceiveDrawDetailUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/claimSettlementUCCclaimSettelmentAddr">
		<property name="service" ref="PA_ClaimSettlementUCCHSImplclaimSettelment"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.claimsettlement.exports.iclaimsettlementucc.claimsettelment.hs.IClaimSettlementUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.claimsettlement.exports.iclaimsettlementucc.claimsettelment.hs.ClaimSettlementUCCHSImpl" id="PA_ClaimSettlementUCCHSImplclaimSettelment" scope="prototype">
		<property name="ucc" ref="PA_claimSettlementUCC">
			
		</property>
	</bean>

	<!-- 理赔相关接口 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/investValueUCCqueryInvestValueAddr">
		<property name="service" ref="PA_InvestValueUCCHSImplqueryInvestValue"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.hs.IInvestValueUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.investvalue.exports.iinvestvalueucc.queryinvestvalue.hs.InvestValueUCCHSImpl" id="PA_InvestValueUCCHSImplqueryInvestValue" scope="prototype">
		<property name="ucc" ref="PA_investValueUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/loanSelfpayUCCqueryLoanSelfpayAddr">
		<property name="service" ref="PA_LoanSelfpayUCCHSImplqueryLoanSelfpay"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.hs.ILoanSelfpayUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.hs.LoanSelfpayUCCHSImpl" id="PA_LoanSelfpayUCCHSImplqueryLoanSelfpay" scope="prototype">
		<property name="ucc" ref="PA_loanSelfpayUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/debtPremUCCqueryDebtPremAddr">
		<property name="service" ref="PA_DebtPremUCCHSImplqueryDebtPrem"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.hs.IDebtPremUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.debtprem.exports.idebtpremucc.querydebtprem.hs.DebtPremUCCHSImpl" id="PA_DebtPremUCCHSImplqueryDebtPrem" scope="prototype">
		<property name="ucc" ref="PA_debtPremUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/overPremUCCqueryOverPremAddr">
		<property name="service" ref="PA_OverPremUCCHSImplqueryOverPrem"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.overprem.exports.ioverpremucc.queryoverprem.hs.IOverPremUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.overprem.exports.ioverpremucc.queryoverprem.hs.OverPremUCCHSImpl" id="PA_OverPremUCCHSImplqueryOverPrem" scope="prototype">
		<property name="ucc" ref="PA_overPremUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/accountValueUCCqueryAccountValueAddr">
		<property name="service" ref="PA_AccountValueUCCHSImplqueryAccountValue"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.accountvalue.exports.iaccountvalueucc.queryaccountvalue.hs.IAccountValueUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.accountvalue.exports.iaccountvalueucc.queryaccountvalue.hs.AccountValueUCCHSImpl" id="PA_AccountValueUCCHSImplqueryAccountValue" scope="prototype">
		<property name="ucc" ref="PA_accountValueUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/aboutFeeUCCqueryAboutFeeAddr">
		<property name="service" ref="PA_AboutFeeUCCHSImplqueryAboutFee"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.hs.IAboutFeeUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.aboutfee.exports.iaboutfeeucc.queryaboutfee.hs.AboutFeeUCCHSImpl" id="PA_AboutFeeUCCHSImplqueryAboutFee" scope="prototype">
		<property name="ucc" ref="PA_aboutFeeUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/ensureAnnualUCCqueryEnsureAnnualAddr">
		<property name="service" ref="PA_EnsureAnnualUCCHSImplqueryEnsureAnnual"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.ensureannual.exports.iensureannualucc.queryensureannual.hs.IEnsureAnnualUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.ensureannual.exports.iensureannualucc.queryensureannual.hs.EnsureAnnualUCCHSImpl" id="PA_EnsureAnnualUCCHSImplqueryEnsureAnnual" scope="prototype">
		<property name="ucc" ref="PA_ensureAnnualUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/backPremUCCqueryBackPremAddr">
		<property name="service" ref="PA_BackPremUCCHSImplqueryBackPrem"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.hs.IBackPremUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.backprem.exports.ibackpremucc.querybackprem.hs.BackPremUCCHSImpl" id="PA_BackPremUCCHSImplqueryBackPrem" scope="prototype">
		<property name="ucc" ref="PA_backPremUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/ruleValidationUCCqueryValidationAddr">
		<property name="service" ref="PA_ruleValidationUCCHSImplqueryValidation"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.rulevalidation.exports.irulevalidationucc.validation.hs.IRuleValidationUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.rulevalidation.exports.irulevalidationucc.queryvalidation.hs.validationUCCHSImpl" id="PA_ruleValidationUCCHSImplqueryValidation" scope="prototype">
		<property name="ucc" ref="PA_ruleValidationUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/contractProductUCCmodifyWaiverEndAddr">
		<property name="service" ref="PA_contractProductUCCHSImplmodifyWaiverEnd"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.contractproduct.exports.icontractproductucc.modifywaiverend.hs.IContractProductUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.contractproduct.exports.icontractproductucc.modifyWaiverEnd.hs.modifyWaiverEndUCCHSImpl" id="PA_contractProductUCCHSImplmodifyWaiverEnd" scope="prototype">
		<property name="ucc" ref="PA_contractProductUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/clmRollBackForOldDataUCCrollBackForOldDataAddr">
		<property name="service" ref="PA_clmRollBackForOldDataUCCImpl"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.clmrollbackforolddata.exports.iclmrollbackforolddataucc.rollbackolddata.hs.IClmRollBackForOldDataUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class=" com.nci.tunan.pa.impl.clmrollbackforolddata.exports.iclmrollbackforolddataucc.rollbackforolddata.hs.ClmRollBackForOldDataUCCHSImpl" id="PA_clmRollBackForOldDataUCCImpl" scope="prototype">
		<property name="ucc" ref="PA_clmRollBackForOldDataUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryBriefInfoUCCqueryBriefInfoAddr">
		<property name="service" ref="PA_QueryBriefInfoUCCHSImplqueryBriefInfo"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querybriefinfo.exports.iquerybriefinfoucc.querybriefinfo.hs.IQueryBriefInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querybriefinfo.exports.iquerybriefinfoucc.querybriefinfo.hs.QueryBriefInfoUCCHSImpl" id="PA_QueryBriefInfoUCCHSImplqueryBriefInfo" scope="prototype">
		<property name="ucc" ref="PA_queryBriefInfoUCC">
			
		</property>
	</bean>

	<!-- 客户保单概要查询 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/customerPolicyGeneralUCCqueryCustomerPolicyGeneralAddr">
		<property name="service" ref="PA_CustomerPolicyGeneralUCCHSImplqueryCustomerPolicyGeneral"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.customerpolicygeneral.exports.icustomerpolicygeneralucc.querycustomerpolicygeneral.hs.ICustomerPolicyGeneralUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.customerpolicygeneral.exports.icustomerpolicygeneralucc.querycustomerpolicygeneral.hs.CustomerPolicyGeneralUCCHSImpl" id="PA_CustomerPolicyGeneralUCCHSImplqueryCustomerPolicyGeneral" scope="prototype">
		<property name="ucc" ref="PA_customerPolicyGeneralUCC">
			
		</property>
	</bean>
	<!-- 查询保单服务信息 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPolicyServiceInfoUCCqueryPolicyInfoAddr">
		<property name="service" ref="PA_QueryPolicyServiceInfoUCCHSImplqueryPolicyInfo"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicyserviceinfo.exports.iquerypolicyserviceinfoucc.querypolicyinfo.hs.IQueryPolicyServiceInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypolicyserviceinfo.exports.iquerypolicyserviceinfoucc.querypolicyinfo.hs.QueryPolicyServiceInfoUCCHSImpl" id="PA_QueryPolicyServiceInfoUCCHSImplqueryPolicyInfo" scope="prototype">
		<property name="ucc" ref="PA_queryPolicyServiceInfoUCC">
			
		</property>
	</bean>
	<!-- 终止保单、险种 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/busiProdStopUCCupdatePolicyOrBusiProdAddr">
		<property name="service" ref="PA_BusiProdStopUCCHSImplupdatePolicyOrBusiProd"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.busiprodstop.exports.ibusiprodstopucc.updatepolicyorbusiprod.hs.IBusiProdStopUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.busiprodstop.exports.ibusiprodstopucc.updatepolicyorbusiprod.hs.BusiProdStopUCCHSImpl" id="PA_BusiProdStopUCCHSImplupdatePolicyOrBusiProd" scope="prototype">
		<property name="ucc" ref="PA_busiProdStopUCC">
			
		</property>
	</bean>
	<!-- 豁免状态更新 -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/updateWaiverUCCupdateWaiverInfoAddr">
		<property name="service" ref="PA_UpdateWaiverUCCHSImplupdateWaiverInfo"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.updatewaiver.exports.iupdatewaiverucc.updatewaiverinfo.hs.IUpdateWaiverUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.updatewaiver.exports.iupdatewaiverucc.updatewaiverinfo.hs.UpdateWaiverUCCHSImpl" id="PA_UpdateWaiverUCCHSImplupdateWaiverInfo" scope="prototype">
		<property name="ucc" ref="PA_updateWaiverUCC">
			
		</property>
	</bean>


	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/nbQueryPolicyUCCqueryPolicyAddr">
		<property name="service" ref="PA_NBQueryPolicyUCCHSImplqueryPolicy"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerypolicyucc.querypolicy.hs.INBQueryPolicyUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.nbquerypolicy.exports.inbquerypolicyucc.querypolicy.hs.NBQueryPolicyUCCHSImpl" id="PA_NBQueryPolicyUCCHSImplqueryPolicy" scope="prototype">
		<property name="ucc" ref="PA_nbQueryPolicyUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPremArapUCCqueryPremArapMsgAddr">
		<property name="service" ref="PA_QueryPremArapUCCHSImplqueryPremArapMsg"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.hs.IQueryPremArapUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypremarap.exports.iquerypremarapucc.querypremarapmsg.hs.QueryPremArapUCCHSImpl" id="PA_QueryPremArapUCCHSImplqueryPremArapMsg" scope="prototype">
		<property name="ucc" ref="PA_queryPremArapUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPayDueUCCImplqueryPayDueAddr">
		<property name="service" ref="PA_QueryPayDueUCCHSImplqueryPayDue"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypaydue.exports.iquerypaydueucc.querypaydue.hs.IQueryPayDueUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypaydue.exports.iquerypaydueucc.querypaydue.hs.QueryPayDueUCCHSImpl" id="PA_QueryPayDueUCCHSImplqueryPayDue" scope="prototype">
		<property name="ucc" ref="PA_queryPayDueUCCImpl">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/getInvestUCCgetInvestAddr">
		<property name="service" ref="PA_GetInvestUCCHSImplgetInvest"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.getinvest.exports.igetinvestucc.getinvest.hs.IGetInvestUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.getinvest.exports.igetinvestucc.getinvest.hs.GetInvestUCCHSImpl" id="PA_GetInvestUCCHSImplgetInvest" scope="prototype">
		<property name="ucc" ref="PA_getInvestUCC">
			
		</property>
	</bean>
	<!-- 保额保费等额查询 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/countFeeUCCcountFeeAddr">
		<property name="service" ref="PA_CountFeeUCCHSImplcountFee"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.hs.ICountFeeUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.countfee.exports.icountfeeucc.countfee.hs.CountFeeUCCHSImpl" id="PA_CountFeeUCCHSImplcountFee" scope="prototype">
		<property name="ucc" ref="PA_countFeeUCC">
			
		</property>
	</bean>

	<!--投连险试算交易接口（消费方：理赔子系统） -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/sellInvestUCCsellInvestAddr">
		<property name="service" ref="PA_SellInvestUCCHSImplsellInvest"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.hs.ISellInvestUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.sellinvest.exports.isellinvestucc.sellinvest.hs.SellInvestUCCHSImpl" id="PA_SellInvestUCCHSImplsellInvest" scope="prototype">
		<property name="ucc" ref="PA_sellInvestUCC">
			
		</property>
	</bean>

	<!--出险日后投连、万能险领取查询接口 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/operateQueryUCCoperateQueryAddr">
		<property name="service" ref="PA_OperateQueryUCCHSImploperateQuery"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.operatequery.exports.ioperatequeryucc.operatequery.hs.IOperateQueryUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.operatequery.exports.ioperatequeryucc.operatequery.hs.OperateQueryUCCHSImpl" id="PA_OperateQueryUCCHSImploperateQuery" scope="prototype">
		<property name="ucc" ref="PA_operateQueryUCC">
			
		</property>
	</bean>

	<!-- 保单账户校验 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/accountCheckUCCaccountCheckUCCAddr">
		<property name="service" ref="PA_AccountCheckUCCHSImplaccountCheckUCC"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.hs.IAccountCheckUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.accountcheck.exports.iaccountcheckucc.accountcheckucc.hs.AccountCheckUCCHSImpl" id="PA_AccountCheckUCCHSImplaccountCheckUCC" scope="prototype">
		<property name="ucc" ref="PA_accountCheckUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/calcSurvivalAmountUCCcalculateNexAmountAddr">
		<property name="service" ref="PA_CalcSurvivalAmountUCCHSImplcalculateNexAmount"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.calcsurvival.exports.icalcsurvivalamountucc.calculatenexamount.hs.ICalcSurvivalAmountUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.calcsurvival.exports.icalcsurvivalamountucc.calculatenexamount.hs.CalcSurvivalAmountUCCHSImpl" id="PA_CalcSurvivalAmountUCCHSImplcalculateNexAmount" scope="prototype">
		<property name="ucc" ref="PA_calcSurvivalAmountUCC">
			
		</property>
	</bean>

	<!-- 投保人和代理人是否是同一人 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/customerIsAgentUCCcustomerisagentAddr">
		<property name="service" ref="PA_CustomerIsAgentUCCHSImplcustomerisagent"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.customerisagent.exports.icustomerisagentucc.customerisagent.hs.ICustomerIsAgentUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.customerisagent.exports.icustomerisagentucc.customerisagent.hs.CustomerIsAgentUCCHSImpl" id="PA_CustomerIsAgentUCCHSImplcustomerisagent" scope="prototype">
		<property name="ucc" ref="PA_customerIsAgentUCC">
			
		</property>
	</bean>

	<!--判断被保人或投保人是否存在期满保单 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/policyExpirationUCCpolicyexpirationAddr">
		<property name="service" ref="PA_PolicyExpirationUCCHSImplpolicyexpiration"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.policyexpiration.exports.ipolicyexpirationucc.policyexpiration.hs.IPolicyExpirationUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.policyexpiration.exports.ipolicyexpirationucc.policyexpiration.hs.PolicyExpirationUCCHSImpl" id="PA_PolicyExpirationUCCHSImplpolicyexpiration" scope="prototype">
		<property name="ucc" ref="PA_policyExpirationUCC">
			
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<!--<bean name="/autoCollectionUCCautoManulBatchExtAddr"
		class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="AutoCollectionUCCHSImplautoManulBatchExt" />
		<property name="serviceInterface"
			value="com.nci.tunan.pa.interfaces.autoCollection.exports.iautocollectionucc.automanulbatchext.hs.IAutoCollectionUCCHS" />
	</bean>
	 注入定义接口服务实现类的bean
	<bean id="AutoCollectionUCCHSImplautoManulBatchExt"
		class="com.nci.tunan.pa.impl.autoCollection.exports.iautocollectionucc.automanulbatchext.hs.AutoCollectionUCCHSImpl"
		scope="prototype">
		<property name="ucc">
			<ref bean="autoCollectionUCC" />
		</property>
	</bean>-->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryMobileUCCqueryMobileAddr">
		<property name="service" ref="PA_QueryMobileUCCHSImplqueryMobile"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querymobile.exports.iquerymobileucc.querymobile.hs.IQueryMobileUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querymobile.exports.iquerymobileucc.querymobile.hs.QueryMobileUCCHSImpl" id="PA_QueryMobileUCCHSImplqueryMobile" scope="prototype">
		<property name="ucc" ref="PA_queryMobileUCC">
			
		</property>
	</bean>



	<!-- 查询保证接口参数 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/guaranteePeriodParamUCCqueryGuaranteePeriodParamAddr">
		<property name="service" ref="PA_GuaranteePeriodParamUCCHSImplqueryGuaranteePeriodParam"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.hs.IGuaranteePeriodParamUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.hs.GuaranteePeriodParamUCCHSImpl" id="PA_GuaranteePeriodParamUCCHSImplqueryGuaranteePeriodParam" scope="prototype">
		<property name="ucc" ref="PA_guaranteePeriodParamUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/transDataUCCtransDataUCCAddr">
		<property name="service" ref="PA_TransDataUCCHSImpltransDataUCC"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.transdata.exports.itransdataucc.transdataucc.hs.ITransDataUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.transdata.exports.itransdataucc.transdataucc.hs.TransDataUCCHSImpl" id="PA_TransDataUCCHSImpltransDataUCC" scope="prototype">
		<property name="ucc" ref="PA_transDataUCC">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/extraPremEmUccextraPremEmUccAddr">
		<property name="service" ref="PA_ExtraPremEmUccHSImplextraPremEmUcc"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.extrapremem.exports.iextraprememucc.extraprememucc.hs.IExtraPremEmUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.extrapremem.exports.iextraprememucc.extraprememucc.hs.ExtraPremEmUccHSImpl" id="PA_ExtraPremEmUccHSImplextraPremEmUcc" scope="prototype">
		<property name="ucc" ref="PA_extraPremEmUcc">
			
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryCusFiveBasicByTelUCCqueryCusFiveBasicByTelAddr">
		<property name="service" ref="PA_QueryCusFiveBasicByTelUCCHSImplqueryCusFiveBasicByTel"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.hs.IQueryCusFiveBasicByTelUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.hs.QueryCusFiveBasicByTelUCCHSImpl" id="PA_QueryCusFiveBasicByTelUCCHSImplqueryCusFiveBasicByTel" scope="prototype">
		<property name="ucc" ref="PA_queryCusFiveBasicByTelUCC">
			
		</property>
	</bean>
	
	
<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
<!-- serviceInterface属性对应着发布服务的接口-->
<!-- service对应着服务发布的实现类在spring中注入的bean-->
	 <bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/dayTradeRevokeUCCdayTradeRevokeAddr"> 
	 	<property name="service" ref="PA_DayTradeRevokeUCCHSImpldayTradeRevoke"/> 
	 	<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.hs.IDayTradeRevokeUCCHS"/>
	 </bean> 
<!-- 注入定义接口服务实现类的bean -->
 <bean class="com.nci.tunan.pa.impl.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.hs.DayTradeRevokeUCCHSImpl" id="PA_DayTradeRevokeUCCHSImpldayTradeRevoke" scope="prototype"> 
 	<property name="ucc" ref="PA_dayTradeRevokeUCC"> 
 		 
 	</property>
 </bean> 

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/queryCashValueListUCCqueryCashValueListAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PA_QueryCashValueListUCCHSImplqueryCashValueList" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querycashvaluelist.exports.iquerycashvaluelistucc.querycashvaluelist.hs.IQueryCashValueListUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PA_QueryCashValueListUCCHSImplqueryCashValueList" class="com.nci.tunan.pa.impl.querycashvaluelist.exports.iquerycashvaluelistucc.querycashvaluelist.hs.QueryCashValueListUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryCashValueListUCC" />
		</property>
	</bean>

	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_errorCorrectDealUCCerrorCorrectDealAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="ErrorCorrectDealUCCHSImplerrorCorrectDeal" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.errorcorrectdeal.exports.ierrorcorrectdealucc.errorcorrectdeal.hs.IErrorCorrectDealUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="ErrorCorrectDealUCCHSImplerrorCorrectDeal" class="com.nci.tunan.pa.impl.errorcorrectdeal.exports.ierrorcorrectdealucc.errorcorrectdeal.hs.ErrorCorrectDealUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_errorCorrectDealUCC" />
		</property>
	</bean>
		<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/queryWhetherRenewUCCqueryWhetherRenewAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryWhetherRenewUCCHSImplqueryWhetherRenew" />
		<property name="serviceInterface" 

value="com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.hs.IQueryWhetherRenewUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryWhetherRenewUCCHSImplqueryWhetherRenew" 

class="com.nci.tunan.pa.impl.commonQuery.exports.iquerywhetherrenewucc.querywhetherrenew.hs.QueryWhetherRenewUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryWhetherRenewUCC" />
		</property>
	</bean>

<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_postLostDealUCCpostLostDealAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PostLostDealUCCHSImplpostLostDeal" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.nbdatadeal.exports.ipostlostdealucc.postlostdeal.hs.IPostLostDealUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PostLostDealUCCHSImplpostLostDeal" class="com.nci.tunan.pa.impl.nbdatadeal.exports.ipostlostdealucc.postlostdeal.hs.PostLostDealUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_postLostDealUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_calcOptBonusSAUCCcalcOptBonusSAAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="CalcOptBonusSAUCCHSImplcalcOptBonusSA" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.calc.exports.icalcoptbonussaucc.calcoptbonussa.hs.ICalcOptBonusSAUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="CalcOptBonusSAUCCHSImplcalcOptBonusSA" class="com.nci.tunan.pa.impl.calc.exports.icalcoptbonussaucc.calcoptbonussa.hs.CalcOptBonusSAUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_calcOptBonusSAUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_riskAmountUCCquerySpecialRiskAmountAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="RiskAmountUCCHSImplquerySpecialRiskAmount" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.hs.IRiskAmountUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="RiskAmountUCCHSImplquerySpecialRiskAmount" class="com.nci.tunan.pa.impl.risk.exports.iriskamountucc.queryspecialriskamount.hs.RiskAmountUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_riskAmountUCC" />
		</property>
	</bean>

	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_customerInfoCheckUcccheckHolderMessageAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="CustomerInfoCheckUccHSImplcheckHolderMessage" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.hs.ICustomerInfoCheckUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="CustomerInfoCheckUccHSImplcheckHolderMessage" class="com.nci.tunan.pa.impl.commonQuery.exports.icustomerinfocheckucc.checkholdermessage.hs.CustomerInfoCheckUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_customerInfoCheckUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_policyStatusReturnUccstatusReturnAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PolicyStatusReturnUccHSImplstatusReturn" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicystatusreturnucc.statusreturn.hs.IPolicyStatusReturnUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PolicyStatusReturnUccHSImplstatusReturn" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicystatusreturnucc.statusreturn.hs.PolicyStatusReturnUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_policyStatusReturnUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_policyDetailUCCpolicyDetailQueryAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PolicyDetailUCCHSImplpolicyDetailQuery" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicydetailucc.policydetailquery.hs.IPolicyDetailUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PolicyDetailUCCHSImplpolicyDetailQuery" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicydetailucc.policydetailquery.hs.PolicyDetailUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_policyDetailUCC" />
		</property>
	</bean>
		<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_customerRelanUccqueryCustemRelationAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="CustemrRelanUccHSImplqueryCustemRelation" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.commonquery.exports.icustemrrelanucc.querycustemrelation.hs.ICustemrRelanUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="CustemrRelanUccHSImplqueryCustemRelation" class="com.nci.tunan.pa.impl.commonQuery.exports.icustemrrelanucc.querycustemrelation.hs.CustemrRelanUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_customerRelanUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_premSynchronizeUccpremSynchronizeAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PremSynchronizeUccHSImplpremSynchronize" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.premsynchronize.exports.ipremsynchronizeucc.premsynchronize.hs.IPremSynchronizeUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PremSynchronizeUccHSImplpremSynchronize" class="com.nci.tunan.pa.impl.premsynchronize.exports.ipremsynchronizeucc.premsynchronize.hs.PremSynchronizeUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_premSynchronizeUcc" />
		</property>
	</bean>
	
	<!-- 延长宽限期天数查询接口 -->
	<bean name="/PA_queryExtendGracePeriodDaysUCCqueryExtendDaysAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryExtendGracePeriodDaysUCCHSImplqueryExtendDays" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryextendgraceperioddays.exports.iqueryextendgraceperioddaysucc.queryextenddays.hs.IQueryExtendGracePeriodDaysUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryExtendGracePeriodDaysUCCHSImplqueryExtendDays" class="com.nci.tunan.pa.impl.queryExtendGracePeriodDays.exports.iqueryextendgraceperioddaysucc.queryextenddays.hs.QueryExtendGracePeriodDaysUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryExtendGracePeriodDaysUCC" />
		</property>
	</bean>
	<!-- 查询客户是否存在有效保单 -->
		<bean name="/PA_isHesitationPeriodUCCqueryIsExitPolicyAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="IsHesitationPeriodUCCHSImplqueryIsExitPolicy" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.hs.IIsHesitationPeriodUCCHS"/>
	</bean>   
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="IsHesitationPeriodUCCHSImplqueryIsExitPolicy" class="com.nci.tunan.pa.impl.IsHesitationPeriod.exports.iishesitationperioducc.queryisexitpolicy.hs.IsHesitationPeriodUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_isHesitationPeriodUCC" />
		</property>
	</bean>
	
	<!-- 更新客户标识 -->
	<bean name="/PA_updatePolicyFlagUCCupdatePolicyFlagAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="UpdatePolicyFlagUccHSImplupdatePolicyFlag" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.updatepolicyflag.exports.iupdatepolicyflagucc.updatepolicyflag.hs.IUpdatePolicyFlagUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="UpdatePolicyFlagUccHSImplupdatePolicyFlag" class="com.nci.tunan.pa.impl.updatepolicyflag.exports.iupdatepolicyflagucc.updatepolicyflag.hs.UpdatePolicyFlagUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_updatePolicyFlagUCC" />
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/aGoodStartUCCupdatePasListAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="AGoodStartUCCHSImplupdatePasList" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.agoodstart.exports.iagoodstartucc.updatepaslist.hs.IAGoodStartUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="AGoodStartUCCHSImplupdatePasList" class="com.nci.tunan.pa.impl.aGoodStart.exports.iagoodstartucc.updatepaslist.hs.AGoodStartUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_aGoodStartUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_INBQueryCrsCustomerUCCcheckCustomerAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="NBQueryCrsCustomerUCCHSImplcheckCustomer" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.hs.INBQueryCrsCustomerUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="NBQueryCrsCustomerUCCHSImplcheckCustomer" class="com.nci.tunan.pa.impl.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.hs.NBQueryCrsCustomerUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_nbQueryCrsCustomerUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_updateRelationPolicyCodeUCCupdateRelationPolicyCodeserviceAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="updateRelationPolicyCodeUCCHSImplupdateRelationPolicyCodeservice" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.updaterelationpolicycode.exports.hs.IupdateRelationPolicyCodeUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="updateRelationPolicyCodeUCCHSImplupdateRelationPolicyCodeservice" class="com.nci.tunan.pa.impl.updaterelationpolicycode.exports.hs.updateRelationPolicyCodeUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_updateRelationPolicyCodeUCC" />
		</property>
	</bean>
	
	<!-- 查询投保人银行账号的其他使用投保人 add by hourui-->
	<bean name="/PA_queryBankAccountsUCCqueryBankAccountsAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryBankAccountsUCCHSImplqueryBankAccounts" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querybankaccounts.exports.iquerybankaccountsucc.querybankaccounts.hs.IQueryBankAccountsUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryBankAccountsUCCHSImplqueryBankAccounts" class="com.nci.tunan.pa.impl.querybankaccounts.exports.iquerybankaccountsucc.querybankaccounts.hs.QueryBankAccountsUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryBankAccountsUCC" />
		</property>
	</bean>
	
	<!-- 根据投保单号查询风险保额基本信息-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_RiskAmountInterUccfindRiskAmountByApplyCodeAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="RiskAmountInterUCCHSImplfindRiskAmountByApplyCode" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.riskamount.exports.iriskamountinterucc.findriskamountbyapplycode.hs.IRiskAmountInterUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="RiskAmountInterUCCHSImplfindRiskAmountByApplyCode" class="com.nci.tunan.pa.impl.riskamount.exports.iriskamountinterucc.findriskamountbyapplycode.hs.RiskAmountInterUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_RiskAmountInterUcc" />
		</property>
	</bean>
	
	<!-- 根据投被保人五要素查询保单信息-->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_queryPolicyByFiveElementsUccqueryPolicyByFiveElementsAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryPolicyByFiveElementsUccHSImplqueryPolicyByFiveElements" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicybyfiveelementsinfo.exports.iquerypolicybyfiveelementsucc.querypolicybyfiveelements.hs.IQueryPolicyByFiveElementsUccHS"/>
	</bean>
	
	<!-- 注入定义接口服务实现类的bean-->
	<bean id="QueryPolicyByFiveElementsUccHSImplqueryPolicyByFiveElements" class="com.nci.tunan.pa.impl.queryPolicyByFiveElementsInfo.exports.iquerypolicybyfiveelementsucc.querypolicybyfiveelements.hs.QueryPolicyByFiveElementsUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryPolicyByFiveElementsUcc" />
		</property>
	</bean>
	
	<!-- 保单状态数据查询 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_policyStatusUccqueryPolicyStateAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PolicyStatusUccHSImplqueryPolicyState" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicystatusucc.querypolicystate.hs.IPolicyStatusUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PolicyStatusUccHSImplqueryPolicyState" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicystatusucc.querypolicystate.hs.PolicyStatusUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_policyStatusUcc" />
		</property>
	</bean>
	
	<!-- 契约-保单状态查询接口-->
	<bean name="/PA_queryPolicyStatusUCCqueryPolicyStatuAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryPolicyStatusUCCHSImplqueryPolicyStatu" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.hs.IQueryPolicyStatusUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryPolicyStatusUCCHSImplqueryPolicyStatu" class="com.nci.tunan.pa.impl.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.hs.QueryPolicyStatusUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryPolicyStatusUCC" />
		</property>
	</bean>
	
	 <!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<!-- 住院津贴限额校验接口 -->
	<bean name="/AccumulatedhospitalizationUCCImplcheckAccumulatedhospitalizationAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="AccumulatedhospitalizationUCCHSImplcheckAccumulatedhospitalization" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.hs.IAccumulatedhospitalizationUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="AccumulatedhospitalizationUCCHSImplcheckAccumulatedhospitalization" class="com.nci.tunan.pa.impl.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.hs.AccumulatedhospitalizationUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_accumulatedhospitalizationUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<!-- 接入渠道交通银行-非实时保单查询-->
	<bean name="/PolicyQueryDelayedJTYHUccImplpolicyQueryDelayedAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PolicyQueryDelayedJTYHUccHSImplpolicyQueryDelayed" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.accesschannelquerypolicy.exports.ipolicyquerydelayedjtyhucc.policyquerydelayed.hs.IPolicyQueryDelayedJTYHUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PolicyQueryDelayedJTYHUccHSImplpolicyQueryDelayed" class="com.nci.tunan.pa.impl.accesschannelquerypolicy.exports.ipolicyquerydelayedjtyhucc.policyquerydelayed.hs.PolicyQueryDelayedJTYHUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_policyQueryDelayedJTYHUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<!-- 保单承保信息更新接口-->
	<bean name="/UpdatePolicyInfoUCCImplupdatePolicyAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="UpdatePolicyInfoUCCHSImplupdatePolicy" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.hs.IUpdatePolicyInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="UpdatePolicyInfoUCCHSImplupdatePolicy" class="com.nci.tunan.pa.impl.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.hs.UpdatePolicyInfoUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_updatePolicyPAInfoUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/saveSalesAmountUccsaveSalesAmountAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="SaveSalesAmountUCCHSImplsaveSalesAmount" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.hs.ISaveSalesAmountUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="SaveSalesAmountUCCHSImplsaveSalesAmount" class="com.nci.tunan.pa.impl.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.hs.SaveSalesAmountUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_saveSalesAmountUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<!-- 投保人手机号及首期银行账号重复查询接口-->
	<bean name="/QueryAbnormalCustomerUCCImplqueryAbnormalCustomerAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryAbnormalCustomerUCCHSImplqueryAbnormalCustomer" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.queryAbnormalCustomer.exports.iqueryabnormalcustomerucc.queryabnormalcustomer.hs.IQueryAbnormalCustomerUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryAbnormalCustomerUCCHSImplqueryAbnormalCustomer" class="com.nci.tunan.pa.impl.queryAbnormalCustomer.exports.iqueryabnormalcustomerucc.queryabnormalcustomer.hs.QueryAbnormalCustomerUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryAbnormalCustomerUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<!-- 团险风险保额累计接口-->
	<bean name="/GroupRiskAccUCCImplfindGroupRiskAccAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="GroupRiskAccUCCHSImplfindGroupRiskAcc" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.hs.IGroupRiskAccUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="GroupRiskAccUCCHSImplfindGroupRiskAcc" class="com.nci.tunan.pa.impl.groupRisk.exports.igroupriskaccucc.findgroupriskacc.hs.GroupRiskAccUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_groupRiskAccUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<!-- 保单状态查询接口 -->
	<bean name="/PA_queryPolicyStatusByPolicyCodeUCCqueryPolicyStatuByPolicyCodeAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryPolicyStatusByPolicyCodeUCCHSImplqueryPolicyStatuByPolicyCode" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypolicystatusbypolicycode.exports.iquerypolicystatusbypolicycodeucc.querypolicystatubypolicycode.hs.IQueryPolicyStatusByPolicyCodeUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryPolicyStatusByPolicyCodeUCCHSImplqueryPolicyStatuByPolicyCode" class="com.nci.tunan.pa.impl.querypolicystatusbypolicycode.exports.iquerypolicystatusbypolicycodeucc.querypolicystatubypolicycode.hs.QueryPolicyStatusByPolicyCodeUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryPolicyStatusByPolicyCodeUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/IQryPolicyAndContractUCCqryPolicyAndContractAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QryPolicyAndContractUCCHSImplqryPolicyAndContract" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.qrypolicyandcontract.exports.iqrypolicyandcontractucc.qrypolicyandcontract.hs.IQryPolicyAndContractUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QryPolicyAndContractUCCHSImplqryPolicyAndContract" class="com.nci.tunan.pa.impl.qrypolicyandcontract.exports.iqrypolicyandcontractucc.qrypolicyandcontract.hs.QryPolicyAndContractUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="IQryPolicyAndContractUCC" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_QueryCustomerInformationByNineElementUccImplqueryCustomerInformationByNineElementAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryCustomerInformationByNineElementUccHSImplqueryCustomerInformationByNineElement" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.hs.IQueryCustomerInformationByNineElementUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryCustomerInformationByNineElementUccHSImplqueryCustomerInformationByNineElement" class="com.nci.tunan.pa.impl.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.hs.QueryCustomerInformationByNineElementUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_QueryCustomerInformationByNineElementUccImpl" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_policyHolderInfoQueryUccqueryPolicyHolderInfoAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PolicyHolderInfoQueryUccHSImplqueryPolicyHolderInfo" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyholderinfoqueryucc.querypolicyholderinfo.hs.IPolicyHolderInfoQueryUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PolicyHolderInfoQueryUccHSImplqueryPolicyHolderInfo" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyholderinfoqueryucc.querypolicyholderinfo.hs.PolicyHolderInfoQueryUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_policyHolderInfoQueryUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_policyIncomeQueryUccqueryPolicyIncomeAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PolicyIncomeQueryUccHSImplqueryPolicyIncome" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyincomequeryucc.querypolicyincome.hs.IPolicyIncomeQueryUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PolicyIncomeQueryUccHSImplqueryPolicyIncome" class="com.nci.tunan.pa.impl.interfaceforchannel.exports.ipolicyincomequeryucc.querypolicyincome.hs.PolicyIncomeQueryUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_policyIncomeQueryUcc" />
		</property>
	</bean>
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_policyMediaTypeUccupdataContractMasterMediaTypeAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="PolicyMediaTypeUccHSImplupdataContractMasterMediaType" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.policymediatype.exports.ipolicymediatypeucc.updatacontractmastermediatype.hs.IPolicyMediaTypeUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="PolicyMediaTypeUccHSImplupdataContractMasterMediaType" class="com.nci.tunan.pa.impl.policymediatype.exports.ipolicymediatypeucc.updatacontractmastermediatype.hs.PolicyMediaTypeUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_policyMediaTypeUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<!-- 银保通插入个人养老金信息报送表接口 -->
	<bean name="/PA_submitMessageBocicYBTUCCinsertSubmitMessageBocicAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="SubmitMessageBocicYBTUCCHSImplinsertSubmitMessageBocic" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.submitMessageBocicYBT.exports.isubmitmessagebocicybtucc.insertsubmitmessagebocic.hs.ISubmitMessageBocicYBTUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="SubmitMessageBocicYBTUCCHSImplinsertSubmitMessageBocic" class="com.nci.tunan.pa.impl.submitMessageBocicYBT.exports.isubmitmessagebocicybtucc.insertsubmitmessagebocic.hs.SubmitMessageBocicYBTUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_submitMessageBocicYBTUCC" />
		</property>
	</bean>
	
		<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_queryBonusInfoListUccqueryBonusInfoListAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryBonusInfoListUccHSImplqueryBonusInfoList" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900826.iquerybonusinfolistucc.querybonusinfolist.hs.IQueryBonusInfoListUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryBonusInfoListUccHSImplqueryBonusInfoList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900826.iquerybonusinfolistucc.querybonusinfolist.hs.QueryBonusInfoListUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryBonusInfoListUcc" />
		</property>
	</bean>
		<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_queryAccumulateCapitalListUccqueryAccumulateCapitalListAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryAccumulateCapitalListUccHSImplqueryAccumulateCapitalList" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.peripheral.exports.r06401900827.iqueryaccumulatecapitallistucc.queryaccumulatecapitallist.hs.IQueryAccumulateCapitalListUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryAccumulateCapitalListUccHSImplqueryAccumulateCapitalList" class="com.nci.tunan.pa.impl.peripheral.exports.r06401900827.iqueryaccumulatecapitallistucc.queryaccumulatecapitallist.hs.QueryAccumulateCapitalListUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryAccumulateCapitalListUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_queryRelation2PhInfoUCCqueryRelation2PhInfoAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryRelation2PhInfoUCCHSImplqueryRelation2PhInfo" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.commonquery.exports.iqueryrelation2phinfoucc.queryrelation2phinfo.hs.IQueryRelation2PhInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryRelation2PhInfoUCCHSImplqueryRelation2PhInfo" class="com.nci.tunan.pa.impl.commonQuery.exports.iqueryrelation2phinfoucc.queryrelation2phinfo.hs.QueryRelation2PhInfoUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryRelation2PhInfoUCC" />
		</property>
	</bean>
	
	<!-- 提供客户信息给契约做银保通业务功能Hessian -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_provideCustomerInfosForBancasBusinessForNBUccqueryCustomerInfoOfBancasPolicyAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="ProvideCustomerInfosForBancasBusinessForNBUccHSImplqueryCustomerInfoOfBancasPolicy" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.hessianinterface.exports.iprovidecustomerinfosforbancasbusinessfornbucc.querycustomerinfoofbancaspolicy.hs.IProvideCustomerInfosForBancasBusinessForNBUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="ProvideCustomerInfosForBancasBusinessForNBUccHSImplqueryCustomerInfoOfBancasPolicy" class="com.nci.tunan.pa.impl.hessianinterface.exports.iprovidecustomerinfosforbancasbusinessfornbucc.querycustomerinfoofbancaspolicy.hs.ProvideCustomerInfosForBancasBusinessForNBUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_provideCustomerInfosForBancasBusinessForNBUcc" />
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_queryMultiPolicyHoldersInfoUCCqueryMultiPolicyHoldersInfoAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="QueryMultiPolicyHoldersInfoUCCHSImplqueryMultiPolicyHoldersInfo" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.commonquery.exports.iquerymultipolicyholdersinfoucc.querymultipolicyholdersinfo.hs.IQueryMultiPolicyHoldersInfoUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="QueryMultiPolicyHoldersInfoUCCHSImplqueryMultiPolicyHoldersInfo" class="com.nci.tunan.pa.impl.commonQuery.exports.iquerymultipolicyholdersinfoucc.querymultipolicyholdersinfo.hs.QueryMultiPolicyHoldersInfoUCCHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_queryMultiPolicyHoldersInfoUCC" />
		</property>
	</bean>
	
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryNextPricingDateUCCqueryNextPricingDateAddr">
		<property name="service" ref="PA_QueryNextPricingDateUCCHSImplqueryNextPricingDate"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querynextpricedate.exports.iquerynextpricingdateucc.querynextpricingdate.hs.IQueryNextPricingDateUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querynextpricedateaftersigndate.exports.iquerynextpricingdateucc.querynextpricingdate.hs.QueryNextPricingDateUCCHSImpl" id="PA_QueryNextPricingDateUCCHSImplqueryNextPricingDate" scope="prototype">
		<property name="ucc" ref="PA_queryNextPricingDateUCC">
		</property>
	</bean>
	
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址-->
	<!-- serviceInterface属性对应着发布服务的接口-->
	<!-- service对应着服务发布的实现类在spring中注入的bean-->
	<bean name="/PA_nbQueryHistoryPolicyUCCqueryHistoryPolicyAddr" class="org.springframework.remoting.caucho.HessianServiceExporter">
		<property name="service" ref="NBQueryHistoryPolicyUccHSImplqueryHistoryPolicy" />
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.nbqueryhistorypolicy.exports.inbqueryhistorypolicyucc.queryhistorypolicy.hs.INBQueryHistoryPolicyUccHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean id="NBQueryHistoryPolicyUccHSImplqueryHistoryPolicy" class="com.nci.tunan.pa.impl.nbqueryhistorypolicy.exports.inbqueryhistorypolicyucc.queryhistorypolicy.hs.NBQueryHistoryPolicyUccHSImpl" scope="prototype">
		<property name="ucc">
			<ref bean="PA_nbQueryHistoryPolicyUCC" />
		</property>
	</bean>
	
	<!-- 针对工行纸质保单补充接口 -->
	<!-- 定义Hessian服务发布bean name属性对应发布服务的地址 -->
	<!-- serviceInterface属性对应着发布服务的接口 -->
	<!-- service对应着服务发布的实现类在spring中注入的bean -->
	<bean class="org.springframework.remoting.caucho.HessianServiceExporter" name="/queryPaperSupplementUCCquerypapersupplementAddr">
		<property name="service" ref="PA_QueryPaperSupplementUCCHSImplquerypaperpupplement"/>
		<property name="serviceInterface" value="com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.hs.IQueryPaperSupplementUCCHS"/>
	</bean>
	<!-- 注入定义接口服务实现类的bean -->
	<bean class="com.nci.tunan.pa.impl.querypapersupplement.exports.iquerypapersupplementucc.querypaperpupplement.hs.QueryPaperSupplementUCCHSImpl" id="PA_QueryPaperSupplementUCCHSImplquerypaperpupplement" scope="prototype">
		<property name="ucc" ref="PA_queryPaperSupplementUCC">
		</property>
	</bean>
</beans> 