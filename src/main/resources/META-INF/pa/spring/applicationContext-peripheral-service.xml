<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 投连单位价格查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000079.Impl.QueryPriceListServiceImpl" id="PA_queryPriceListService">
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	<!-- 万能保单最新月度结算信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000088.Impl.QueryMessageListServiceImpl" id="PA_queryMessageListServiceImpl">
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>

	<!-- 投连信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000081.impl.InvestAccInfoQueryServiceImpl" id="PA_investAccInfoQueryService">
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
	</bean>
	<!-- 保单挂起状态查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001000.impl.QueryGuaQiServiceImpl" id="PA_queryGuaQiService">
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
	</bean>
	<!-- 投连退保查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000851.impl.QueryTuiBaoServiceImpl" id="PA_iQueryTuiBaoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="costFeeCfgDao" ref="PA_costFeeCfgDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="outterDealUCC" ref="PA_outterDealUcc"/>
	</bean>
	<!-- 保单投资单位数及价格-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000843.impl.QueryUuitPriceListServiceImpl" id="PA_queryUuitPriceListService">
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	
	<!-- 投连价格查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000939.impl.QueryCowPriceListServiceImpl" id="PA_iQueryCowPriceListService">
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	<!-- 金钱柜信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000092.impl.AccInfoQueryServiceImpl" id="PA_accInfoQueryService">
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
	</bean>

	<!-- 追加保费查询（至尊双利） -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000030.Impl.QueryMoneyServiceImpl" id="PA_queryMoneyService">
		<property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>
	<!-- 万能险账户价值结算查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000032.impl.QueryAccountCostServiceImpl" id="PA_queryAccountCostService">
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>
	<!-- 累计升息基本信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000093.impl.QueryBaseMessageServiceImpl" id="PA_queryBaseMessageService">
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
	</bean>
	<!-- 查询交易密码 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000694.impl.QueryDealPasswordServiceImpl" id="PA_queryDealPasswordService">
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>
	<!-- 人员新增 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000604.impl.AgentPersonAddServiceImpl" id="PA_agentPersonService">
		<property name="agentPADao" ref="PA_agentPADao"/>
		<property name="agentLicenseDao" ref="commonAgentLicenseDao"/>
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
		<property name="agentProductDao" ref="commonAgentProductDao"/>
	</bean>
	<!-- 网点服务人员变化 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000230.impl.NetServePersonServiceImpl" id="PA_netServePersonService">
		<property name="bankBranchHandlerDao" ref="commonBranchHandlerDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="bankBranchDao" ref="commonBankBranchDao"/>
		<property name="bankBranchAgentDao" ref="commonbankBranchAgentDao"></property>
		<property name="bankDao" ref="PA_bankDao"></property>
	</bean>
	<!-- 尊享人生险种可选责任信息  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000098.impl.SelectableDutyInfoQuery652ServiceImpl" id="PA_selectableDutyInfoQuery652Service">
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="bonusRateDao" ref="PRD_revBonusRateDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		
	</bean>
	<!-- 事历-保单提醒功能查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000899.impl.PolicyRemindQueryServiceImpl" id="PA_policyRemindQueryService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>

	<!-- 查询保单被保人接口Service -->
<!-- 	<bean id="queryPolicyInsuredService" class="com.nci.tunan.pa.impl.peripheral.service.r00101000014.impl.QueryPolicyInsuredServiceImpl"> -->
<!-- 	</bean> -->
	

	<!-- 续期信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000043.impl.RenewInfoQueryServiceImpl" id="PA_renewInfoQueryService">
		<property name="premArapDao" ref="PA_premArapDao"/>
	</bean>
	
	<!-- 保单受益人信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000015.impl.BeneInfoQueryServiceImpl" id="PA_beneInfoQueryService">
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>

	<!-- 客户信息认证 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000041.impl.CustomerMsgQueryServiceImpl" id="PA_customerMsgQueryService">
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>

	<!-- 累积升息历史信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000094.impl.AccHisInfoQueryServiceImpl" id="PA_accHisInfoQueryService">
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="prdProductQueryService" ref="PA_prdService" />
	</bean>
	<!-- 追加保费查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000988.impl.AddPremQueryServiceImpl" id="PA_addPremQueryService">
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
	</bean>
	<!-- 保单历史服务人员详细信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000836.impl.ComDetailQueryServiceImpl" id="PA_comDetailQueryService">
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
	</bean>

	<!-- 满期给付通知书查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000103.impl.WebLPEdorGetNoticeQueryServiceImpl" id="PA_webLPEdorGetNoticeQueryService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
		<property name="orgDao" ref="commonOrgDao"/>
	</bean>
	<!-- 网点授权 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000233.impl.OrganGrantInsertServiceImpl" id="PA_organGrantInsertService">
		<property name="agentProductDao" ref="commonAgentProductDao"/>
	</bean>
	<!-- 网点修改及开停业 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000624.impl.OrganAndStartBusiUpdateServiceImpl" id="PA_organAndStartBusiUpdateService">
		<property name="bankBranchDao" ref="commonBankBranchDao"/>
		<property name="bankBranchHandlerDao" ref="commonBranchHandlerDao"/>
	</bean>
	<!-- 生日提醒 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000150.impl.CustBirthDayQueryServiceImpl" id="PA_custBirthDayQueryService">
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>
	
	<!-- 证书导入 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000847.impl.QualifInInsertServiceImpl" id="PA_qualifInInsertService">
		<property name="agentLicenseDao" ref="PA_agentLicenseDao"/>
	</bean>

	<!-- 出险人保单查询接口Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000061.impl.InsuredPolicyQueryListServiceImpl" id="PA_queryInsuredPolicyListService">
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<!-- 被保险人职业类别查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000910.impl.GetInsuredOccQueryServiceImpl" id="PA_getInsuredOccQueryService">
		<property name="insuredListDao" ref="PA_insuredListDao"/>
	</bean>

	<!-- 团队修改及开停业接口Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000586.impl.SalesOrganUpdateServiceImpl" id="PA_salesOrganUpdateService">
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
	</bean>

	<!-- 保单生存领取信息查询接口Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000022.impl.PayDueQueryListServiceImpl" id="PA_queryPayDueListService">
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="capPremArapDao" ref="CAP_premArapDao"/>
	</bean>	

	<!-- 投连保险账户信息查询接口Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000082.impl.ContractInvestQueryListServiceImpl" id="PA_queryContractInvestListService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundDao" ref="fundDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	

	<!-- 团队新增 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000585.impl.AddNewTeamServiceImpl" id="PA_addNewTeamService">
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
	</bean>
	<!-- 客户账户领取查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000654.impl.PolicyAccountReceiveServiceImpl" id="PA_policyAccountReceiveService">
	</bean>
	 
	<!-- 投连保单信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000175.impl.InvestPolicyInfoQueryServiceImpl" id="PA_investPolicyInfoQueryService">
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
	</bean>
	
	
	

	<!-- 人员解约Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000599.impl.AgentCancleServiceImpl" id="PA_agentCancleService">
		<property name="agentPADao" ref="PA_agentPADao"/>
	</bean>
	
	<!-- 保单现价查询Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000049.impl.QueryPolicyCashValueListServiceImpl" id="PA_queryPolicyCashValueListService">
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
	</bean>
	
	<!-- 分红信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000007.impl.BonusInfoQueryCountServiceImpl" id="PA_bonusInfoQueryCountService">
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="iContractBusiProdDao" ref="PA_contractBusiProdDao"></property>
		<property name="iBusinessProductDao" ref="PA_businessProductDao"></property>
		<property name="allocatBnusDao" ref="PA_iAllocatBnusDao"></property>
	</bean>

	<!-- 续期交费查询与校检接口Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000684.impl.PremArapQueryListServiceImpl" id="PA_queryPremArapListService">
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
		<property name="policyAccountDao" ref="PA_policyAccountDao"></property>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
		<property name="contractProductDao" ref="PA_contractProductDao"></property>
		<property name="capService" ref="PA_capIAS"></property>
	</bean>
	
	<!-- 保单投保人信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000009.impl.QueryPolicyHolderServiceImpl" id="PA_queryPolicyHolderService">
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="addressDao" ref="PA_addressDao"/> 
	</bean>
	
	<!-- 投连保单状态报告查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000109.impl.QueryFundTransHisServiceImpl" id="PA_queryFundTransHisService">
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
	</bean>
	
	<!-- i添财投资连结接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000986.impl.QueryRenturnRateServiceImpl" id="PA_iQueryRenturnRateService">
	   <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	   <property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>
	<!-- 万能险实时账户查询接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001406.impl.QueryUniversalAccountServiceImpl" id="PA_iQueryUniversalAccountService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
	</bean>

	<!-- 客户告知查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000068.impl.HealthImpartQueryServiceImpl" id="PA_healthImpartQueryService">
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
	</bean>
	
		<!-- 领取信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000606.impl.LiveGetQueryServiceImpl" id="PA_liveGetQueryService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
	</bean>
	

	
	
	<!-- 万能险新契约账户价值查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000029.impl.UniversalNBAcountQueryServiceImpl" id="PA_universalNBAcountQueryService">	
	<property name="contractInvestDao" ref="PA_contractInvestDao"/>
	<property name="prdIAS" ref="PA_prdIAS"/>
	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	
		<!-- 投连账户结算历史信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000083.impl.InvestAccountHistoryQueryServiceImpl" id="PA_investAccountHistoryQueryService">	
	  <property name="fundTransDao" ref="PA_fundTransDao"/>
	  <property name="formulaService" ref="PA_formulaService"/>
	  <property name="contractInvestDao" ref="PA_contractInvestDao"/>
	  <property name="fundAssetsDetailDao" ref="PA_fundAssetsDetailDao"/>
	</bean>
	
	<!-- 领取信息汇总查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000686.impl.ReceiveInfoSummaryQueryServiceImpl" id="PA_receiveInfoSummaryQueryService">	
	<property name="payDueDao" ref="PA_payDueDao"/>
	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	<property name="payPlanDao" ref="PA_payPlanDao"/>
	<property name="policyAccountDao" ref="PA_policyAccountDao"/>
	<property name="contractInvestDao" ref="PA_contractInvestDao"/>
	</bean>
	<!-- 保单密码校验接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000923.impl.PwValidServiceImpl" id="PA_pwValidService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
		<!--红利历史信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000020.impl.BonusHisInfoQueryServiceImpl" id="PA_bonusHisInfoQueryService">
		<property name="bonusallocateDao" ref="PA_bonusallocateDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>

	<!-- 保单信息查询接口（万能险账户信息） -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001127.impl.ContUniversalAccountQueryServiceImpl" id="PA_contUniversalAccountQueryService">	
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
	</bean>

	<!--保单受益人信息查询接口-->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001398.impl.BenefitInfoQueryServiceImpl" id="PA_benefitInfoQueryService">	
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean> 
	<!-- 保单查询接口(查询保单详细内容 ) -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000550.impl.ViewPolicyInfoServiceImpl" id="PA_iviewPolicyInfoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
	</bean>
	<!--查询保单投保人信息-->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001394.impl.QueryPolicyHolderBasicInfoServiceImpl" id="PA_queryPolicyHolderBasicInfoService">	
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
	</bean>

	<!-- 保单险种责任信息查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001402.impl.PolicyBusiDutyServiceImpl" id="PA_policyBusiDutyService">
		<property name="policyBusiDutyDao" ref="PA_policyBusiDutyDao"/>
	</bean>
	<!-- r00101001396 保单被保险人查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001396.impl.QueryInsuredMessageServiceImpl" id="PA_queryInsuredMessageService">
		<property name="insuredListDao" ref="PA_insuredListDao"/>
	</bean>
	<!-- 保单险种信息查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001400.impl.PolResikMessageServiceImpl" id="PA_polResikMessageService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="riskLevelConfigDao" ref="PA_riskLevelConfigDao"/>
	</bean>
	
	<!-- 保单基础信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001392.impl.BusinessCentreQueryServiceImpl" id="PA_businessCentreQueryService">
		<property name="businessCentreDao" ref="PA_businessCentreDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<!-- 续期缴费账号查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000838.impl.RenewalPaymentIDQueryServiceImpl" id="PA_renewalPaymentIDQueryService">
		<property name="renewalPaymentIdDao" ref="PA_renewalPaymentIdDao"/>
	</bean>
	<!-- 查询个人保单详细信息 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000011.impl.QueryPersonalPolicyDetailServiceImpl" id="PA_iQueryPersonalPolicyDetailService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="certiTypeDao" ref="PA_certiTypeDao"/>
		<property name="orgDao" ref="PA_orgDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="bankDao" ref="PA_bankDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="agentPADao" ref="PA_agentPADao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="policyLoseDao" ref="PA_policyLoseDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="appendPremListDao" ref="PA_appendPremListDao"/>
		<property name="salesChannelDao" ref="PA_salesChannelDao"/>
		<property name="queryYearBonusReportDao" ref="PA_queryYearBonusReportDao"/>
		<property name="bankBranchDao" ref="commonBankBranchDao"/>
		<property name="acceptChangeDao" ref="PA_csAcceptChangeDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="queryPersonalPolicyDetailDao" ref="PA_queryPersonalPolicyDetailDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="clmService" ref="PA_clmService"/>
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="capPremArapDao" ref="CAP_premArapDao"/>
		<property name="productLifeBonusDao" ref="PRD_productLifeBonusDao"/>
		<property name="prdService" ref="PA_mmsIAS"/>
		<property name="calimLiabDao" ref="CLM_claimLiabDao"/>
		<property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
	</bean>

	<!-- 保单列表查询接口 -->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001390.impl.ContractMasterQueryListServiceImpl" id="PA_contractMasterQueryListService">
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>		
		<property name="commonCustomerDao" ref="PA_commonCustomerDao"/>
	</bean> 
		<!-- 账户部分查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000857.impl.BalanceQueryServiceImpl" id="PA_balanceQueryService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<!--查询年度分红业绩报告书-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000151.impl.QueryYearBonusReportServiceImpl" id="PA_queryYearBonusReportService">	
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
		<property name="bankBranchDao" ref="commonBankBranchDao"/>
		<property name="queryYearBonusReportDao" ref="PA_queryYearBonusReportDao"/>
		<property name="orgDao" ref="commonOrgDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="documentDao" ref="PA_paDocumentDao"/>
		<property name="clobDao" ref="PA_paClobDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="rossInfoDao" ref="PA_rossInfoDao"/>
	</bean>
	<!--查询年度分红业绩报告书_现金分红接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002806.impl.QueryYearBonusReportServiceImpl" id="PA_queryYearBonusReportForCashService">	
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
		<property name="bankBranchDao" ref="commonBankBranchDao"/>
		<property name="queryYearBonusReportDao" ref="PA_queryYearBonusReportDao"/>
		<property name="orgDao" ref="commonOrgDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="documentDao" ref="PA_paDocumentDao"/>
		<property name="clobDao" ref="PA_paClobDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="rossInfoDao" ref="PA_rossInfoDao"/>
	</bean>
	<!-- 追加保费提交接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000863.impl.ApsEdorServiceImpl" id="PA_apsEdorService">
		<!-- <property name="contractMasterDao" ref="contractMasterDao"></property>
		<property name="cusAccountDrawTrialDao" ref="cusAccountDrawTrialDao"></property> -->
		<property name="csCustomerServiceImpl" ref="PA_csCustomerServiceImpl"/>
		<property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>
		<property name="cusAcceptUCC" ref="PA_cusAcceptUCC"/>
		<property name="csMainStreamService" ref="PA_csMainStreamService"/>
		<property name="cusApplicationService" ref="PA_cusApplicationService"/>
		<property name="outterDealUCC" ref="PA_outterDealUcc"/>
		<property name="csEndorseAMUCC" ref="PA_csEndorseAMUCC"/>
		<property name="cateGoryUpdateUCC" ref="PA_cateGoryUpdateUCC"/>
	</bean>
	
	<!-- 保单到期领取提醒 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000144.impl.PolicyDueToReceiveReminderServiceImpl" id="PA_policyDueToReceiveReminderService">
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<!-- R00101000142生存领取(给付)信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000142.impl.PayDueQueryInfoServiceImpl" id="PA_iPayDueQueryInfoService">
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
	</bean>
	
	<!-- 续收查询 -->
	<bean class=" com.nci.tunan.pa.impl.peripheral.service.r00101000184.impl.RenewalMessageQueryServiceImpl" id="PA_renewalMessageQueryService">
	<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	
	<!-- R00101000028 生存领取历史划款信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000028.impl.PayDueHistoryTransferQueryServiceImpl" id="PA_payDueHistoryTransferQueryService">
	<property name="premArapDao" ref="PA_premArapDao"/>
	</bean>
	
	<!-- 再保险  操作履历下记事本查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r06401001853.impl.NoteInfoQueryServiceImpl" id="PA_noteInfoQueryService">
	<property name="csNoteDao" ref="PA_csNoteDao"/>
	<property name="noteDao" ref="PA_noteDao"/>
	</bean>
	
	<!--健康告知查询-->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r06401001856.impl.QueryCustomerHealthInfoServiceImpl" id="PA_queryCustomerHealthInfoService">	
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
		<property name="questionaireInfoDao" ref="PA_paquestionaireInfoDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	
	<!--特约查询-->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r06401001861.impl.QueryPolicyConditionServiceImpl" id="PA_queryPolicyConditionService">	
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
	</bean>
	<!-- 再保险 受益人查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r06401001871.impl.ZBBenefitInfoQueryServiceImpl" id="PA_zbBenefitInfoQueryService">
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insurdListDao" ref="PA_insuredListDao"/>
	</bean>
	
	<!--各保单健康告知查询（新核心）-->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r06401001887.impl.QueryPolicyCustomerHealthImpartServiceImpl" id="PA_queryPolicyCustomerHealthImpartService">	
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
	</bean>
	
	<!-- 保单挂起状态修改（理赔保单挂起接口） -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000090.impl.LockCSAndREForCLMServiceImpl" id="PA_lockCSAndREForCLMService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="pa_clmService" ref="PA_pa_clmService"/>
	</bean>
	<!-- 短期意外险被保人信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r04301000337.impl.QueryInsuredInfoByPolicyCodeServiceImpl" id="PA_queryInsuredInfoByPolicyCodeService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>
	
	<!--操作履历下生调结果查询（新核心）-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r06401001842.impl.SurveyResultQueryServiceImpl" id="PA_surveyResultQueryService">	
		<property name="surveyApplyDao" ref="PA_claimSurveyApplyDao"/>
	</bean>
	
	<!-- 万能险利率查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000070.impl.ContractFundSettlementServiceImpl" id="PA_contractFundSettlementService">	
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<!-- 申请分红通知书补打接口 -->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r00102001169.impl.ApplyDividendPrintServiceImpl" id="PA_applyDividendPrintService">	
		<property name="paDocumentDao" ref="PA_paDocumentDao"/>
	</bean>
	<!-- 当日年化收益查询接口 -->
	 <bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000960.impl.QueryAnnualYieldInfoServiceImpl" id="PA_queryAnnualYieldInfoService">	
	 	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	 </bean>
	
	<!-- 投保单号快速查询是否存在服务 -->
	<bean class="com.nci.tunan.pa.impl.judgeapplycodeexit.service.impl.JudgeApplyCodeExitServiceImpl" id="PA_judgeApplyCodeExitService">
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="nbIAS" ref="PA_nbIAS"/>
	</bean>
	
	<!-- 交退费编号快速查询是否存在服务  -->
	<bean class="com.nci.tunan.pa.impl.judgeunitnumberExist.service.impl.JudgeUnitNumberExistServiceImpl" id="PA_judgeUnitNumberExistService">
	    <property name="premArapDao" ref="PA_premArapDao"/>
	    <property name="renewReversalApplyDao" ref="PA_renewReversalApplyDao"/>
	</bean>
	<!-- 团体个人单和个人保单特约内容查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r06401001775.impl.QueryPaContentServiceImpl" id="PA_queryPaContentService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<!-- 工行纸质保单补充接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000984.impl.PaperSupplementServiceImpl" id="PA_paperSupplementService">
		 <property name="policyPrintDao" ref="PA_policyPrintDao"/>
		 <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<!-- 账户注销查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000498.impl.AccountCancelQueryServiceImpl" id="PA_accountCancelQueryService">
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="policyAccountDao" ref="PA_policyAccountDao"/>
	</bean>
	<!-- 保单信息服务人员轨迹查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000787.impl.PolicyServiceTrackQueryServiceImpl" id="PA_policyServiceTrackQueryService">
	    <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	</bean>	
	<!-- 客户地址信息查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001167.impl.CustAddressQueryServiceImpl" id="PA_custAddressQueryService">
	    <property name="customerDao" ref="PA_customerDao"/>	
	    <property name="addressDao" ref="PA_addressDao"/>
	</bean>
	<!-- 渠道同步查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001183.impl.ChannelSynchroQueryServiceImpl" id="PA_channelSynchroQueryService">	
		<property name="salesChannelDao" ref="PA_salesChannelDao"/>
	</bean>
	<!-- 管理費同步查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001205.impl.ManagementCostSynchroQueryServiceImpl" id="PA_managementCostSynchroQueryService">	
	</bean>
	<!--保单相关影像文件查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000100.impl.QueryImageFileServiceImpl" id="PA_queryImageFileService">	
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="ecsService" ref="PA_eCSServiceImpl"/>
	</bean>
	<bean class="com.nci.tunan.pa.imports.impl.ECSServiceImpl" id="PA_eCSServiceImpl">	
	</bean>

	<!-- 保单红利查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000869.impl.PolicyBonusServiceImpl" id="PA_policyBonusService">
	<property name="policyBonusDao" ref="PA_policyBonusDao"/>
	<property name="prdIAS" ref="PA_prdIAS"/>
	<property name="allocationDao" ref="PA_allocationDao"/>
	</bean>

		<!-- 意外险 产品信息查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000792.impl.BusiProdQueryServiceImpl" id="PA_busiProdQueryService">
	    <property name="busiProdQueryDao" ref="PA_busiProdQueryDao"/>
	    <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	    <property name="csInsuredListDao" ref="PA_csInsuredListDao"/>
	    <property name="csCustomerDao" ref="PA_csCustomerDao"/>
	    <property name="csContractBusiProdDao" ref="PA_csContractBusiProdDao"/>
	</bean>
	<!-- 万能险结算数据接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001015.impl.QueryInsuAccInfoServiceImpl" id="PA_queryInsuAccInfoService">
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	    <property name="insuredListDao" ref="PA_insuredListDao"/>
	    <property name="customerDao" ref="PA_customerDao"/>
	    <property name="queryInsuAccInfoDao" ref="PA_queryInsuAccInfoDao"/>
	    <property name="queryAnnualYieldInfoService" ref="PA_queryAnnualYieldInfoService"/>
	</bean>
	<!-- 分红信息查询cjk -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001109.impl.AllocatBnusServiceImpl" id="PA_iAllocatBnusQueryService"> 
	 <property name="iAllocatBnusDao" ref="PA_iAllocatBnusDao"/>
	
	</bean>
	<!-- 客户保单信息查询接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000042.impl.CustomerPolicyMsgQueryServiceImpl" id="PA_customerPolicyMsgQueryService"> 
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>
	<!-- 产品是否可续保判断 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001019.impl.PolicyRenewQueryServiceImpl" id="PA_iPolicyRenewQueryService">
	<property name="iProductRenewDao" ref="PA_iProductRenewDao"/>
	</bean>
	<!-- 客户身份信息验真开关-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002126.impl.AuthenticationSwitchServiceImpl" id="PA_authenticationSwitchService">
	<property name="identitycheckswitchDao" ref="PA_identitycheckswitchDao"/>
	</bean>
	
	<!-- 电话中心保单信息查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002246.impl.TelephoneCenterQueryPolicyInfoServiceImpl" id="PA_telephoneCenterQueryPolicyInfoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		
	</bean>
	<!-- 合同机构同步接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102002243.impl.CooperationStructureSynchronizationServiceImpl" id="PA_cooperationStructureSynchronizationService">
		<property name="saleComDao" ref="PA_saleComDao"/>
	</bean>
	<!-- 业务员重复电话甄别接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002283.impl.AgentRepeatPhoneIdentityServiceImpl" id="PA_agentRepeatPhoneIdentityService">
		<property name="agentDao" ref="PA_agentDaoTunan"/>
	</bean>
	
	<!-- 个人保单信息查询（MMS）-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002255.impl.PersonalPolicyInfoQueryForMMSServiceImpl" id="PA_personalPolicyInfoQueryForMMSService">
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="salesOrganDao" ref="commonSalesOrganDao"/>
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="prdService" ref="PA_mmsIAS"/>
	</bean>
	
	<!-- 订单号快速查询-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.ordernoquery.impl.OrderNoQueryServiceImpl" id="PA_orderNoQueryService">
		<property name="orderServiceDao" ref="PA_PAorderServiceDao"/>
	</bean>
	<!-- 附加险万能险新契约账户价值查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000034.impl.AccountWorthQueryServiceImpl" id="PA_accountWorthQueryService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
	</bean>
	<!--续期缴费提交接口 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102002323.impl.RenewalPaymentSubmittedServiceImpl" id="PA_renewalPaymentSubmittedService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="capService" ref="PA_capIAS"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="renewCollectionFeeService" ref="PA_renewCollectionFeeService"/>
	</bean>
	<!--续期缴费支付失败回写核心接口  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102002324.impl.ReneFeePayFailBackCoreServiceImpl" id="PA_reneFeePayFailBackCoreService">
		<property name="premArapDao" ref="PA_premArapDao"/>
	</bean> 
	<!-- 附加险险种信息查询接口  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001691.impl.RiderProductRiskInfoQueryServiceImpl" id="PA_riderProductRiskInfoQueryService">
		<property name="csEndorseNSService" ref="PA_csEndorseNSService"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="prdProductQueryService" ref="PA_prdService"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"></property>
	</bean> 
	
	<!-- 附加险查询接口  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002280.impl.AdditionRiskServiceImpl" id="PA_additionRiskService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean> 
	
	<!-- 附加险代码查询接口  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001716.impl.AdditionRiskCodeQueryServiceImpl" id="PA_additionRiskCodeQueryService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="prdProductQueryService" ref="PA_prdService" />
		<property name="contractMasterDao" ref="PA_contractMasterDao" />
		<property name="stopBusiConfigDao" ref="PA_stopBusiConfigDao" />
		<property name="stopBusiProdCfgDao" ref="PA_stopBusiProdCfgDao" />
		
		
	</bean> 
	<!-- 交易密码是否重置 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000964.impl.PaPassWordIfResetServiceImpl" id="iPaPassWordIfResetService">
	  <property name="iCsAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
	</bean>
	
	<bean id="iPaSignBackService" class="com.nci.tunan.pa.impl.peripheral.service.r00101001118.impl.PaSignBackServiceImp">
	 <property name="iPolicyAcknowledgementDao" ref="PA_policyAcknowledgementDao"></property>
	 <property name="iContractMasterDao" ref="PA_contractMasterDao"></property>
	</bean>

<!-- 保单验真查询接口  -->
	<bean id="PA_queryPolicyVerifyService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002484.impl.QueryPolicyVerifyServiceImpl">
	   <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
	   <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
	   <property name="insuredListDao" ref="PA_insuredListDao"></property>
	   <property name="customerDao" ref="PA_customerDao"></property>
	   <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
	   <property name="businessProductDao" ref="PA_businessProductDao"></property>
	   <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"></property>
	   <property name="premArapDao" ref="PA_premArapDao"></property>
	   <property name="contractExtendDao" ref="PA_contractExtendDao"></property>
	   <property name="contractProductDao" ref="PA_contractProductDao"></property>
	</bean>
	
<!-- 保单类型查询 -->
<bean id="iPolicyTypeService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002522.impl.PolicyTypeServiceImpl">
<property name="iContractMasterDao" ref="PA_contractMasterDao"></property>
</bean>

	<!-- 电子函件服务查询接口  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002512.impl.IsChooseEmailServiceImpl" id="PA_isChooseEmailService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	
	<!-- 红利信息查询 -->
	<bean id="PA_queryBonusInfoServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.r00101002466.impl.QueryBonusInfoServiceImpl" >
		<property name="documentDao" ref="PA_paDocumentDao"/>
		<property name="clobDao" ref="PA_paClobDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="productLifeBonusDao" ref="PRD_productLifeBonusDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<!-- 注入投保人和被保人 -->
        <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
        <property name="insuredListDao" ref="PA_insuredListDao"></property>
        <property name="contractAgentDao" ref="PA_contractAgentDao"></property>
        <property name="addressDao" ref="PA_addressDao"></property>
        <property name="agentDao" ref="PA_agentDaoTunan"></property>
        <property name="customerDao" ref="PA_customerDao"></property>
        <property name="bonusAllocateDao" ref="PA_bonusallocateDao"></property>
        <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"></property>
        <property name="contractExtendDao" ref="PA_contractExtendDao"></property>
        <property name="rossInfoDao" ref="PA_rossInfoDao"></property>
        <property name="payDueDao" ref="PA_payDueDao"></property>
        <property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="salesChannelDao" ref="PA_salesChannelDao"/>
		<property name="noticeDao" ref="PA_noticeDao"/>
		<property name="findCodeValueService" ref="PA_findCodeValueService"/>
   		
	</bean>
	
	<!-- 万能险结算状态信息查询 -->
	<bean id="PA_settleAccountsOfOmnipotentInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002468.impl.SettleAccountsOfOmnipotentInfoServiceImpl">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>

	<!--by zhaoyoan_wb 万能险账户当前信息查询  -->
	<bean id="PA_universalAccountInfoQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r00101000031.impl.UniversalAccountInfoQueryServiceImpl">
		<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
		<property name="contractInvestDao" ref="PA_contractInvestDao"></property>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"></property>
		<property name="fundTransDao" ref="PA_fundTransDao"></property>
		<property name="policyHolderDao" ref="PA_policyHolderDao"></property>
		<property name="premArapDao" ref="PA_premArapDao"></property>
		<property name="formulaService" ref="PA_formulaService"></property>
		<property name="prdProductQueryService" ref="PA_prdService" />
	</bean>
	<!-- 红利信息查询接口 -->
	<bean id="PA_dividendInformationQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002568.impl.DividendInformationQueryServiceImpl" >
		<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
		<property name="businessProductDao" ref="PA_businessProductDao"></property>
		<property name="productLifeDao" ref="PA_productLifeDao"></property>
		<property name="productLifeBonusDao" ref="PRD_productLifeBonusDao"></property>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"></property>
		<property name="contractProductDao" ref="PA_contractProductDao"></property>
		<property name="iprdService" ref="PA_prdIAS"></property>
	</bean>
	<!-- 个人税收信息 -->
	<bean  id="ipeopleIncomeQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002547.impl.peopleIncomeQueryServiceImpl" >
	 <property name="iCustomerDao" ref="PA_customerDao"></property>
	

	</bean>

	<!--万能产品最新信息查询  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.universalPolicyAccount.impl.UniversalPolicyAccountServiceImpl" id="PA_universalPolicyAccountService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	
	<!-- 个人税收居民身份信息提交接口 -->
    <bean id="PA_peopleIncomeCmitService" class="com.nci.tunan.pa.impl.peripheral.service.r00102002548.impl.PeopleIncomeCmitServiceImpl" >
		<property name="customerDao" ref="PA_customerDao"/>
	    <property name="customerTaxDao" ref="comm_customerTaxDao"/>
	    <property name="customerTaxTrackDao" ref="comm_customerTaxTrackDao"/>
	    <property name="addressTaxDao" ref="comm_addressTaxDao"/>
	    <property name="addressTaxTrackDao" ref="comm_addressTaxTrackDao"/>
	    <property name="taxInfoDao" ref="comm_taxInfoDao"/>
	    <property name="taxInfoTrackDao" ref="comm_taxInfoTrackDao"/>
	    <property name="taxCauseDao" ref="comm_taxCauseDao"/>
	    <property name="taxCauseTrackDao" ref="comm_taxCauseTrackDao"/>
	    <property name="customerTaxInfoService" ref="comm_customerTaxInfoService"/>
	</bean>
	
	<!-- 险种责任接口 -->
	<bean id="PA_busiItemDutyService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002681.impl.BusiItemDutyServiceImpl" >
		<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	
	<!-- 连带被保险人信息接口 -->
	<bean id="PA_jointInsurerInfService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002683.impl.JointInsurerInfServiceImpl" >
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	
	<!-- 客户保单查询  -->
	<bean id="PA_customerPolicyInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002633.impl.CustomerPolicyInfoServiceImpl" >
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="bankDao" ref="PA_bankDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao" />
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
	</bean>
	
	<!-- 保单状态接口 -->
	<bean id="PA_policyStateInfService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002677.impl.PolicyStateInfServiceImpl" >
	    <property name="businessProductDao" ref="PA_businessProductDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
	</bean>

	<!--保险账户其他信息轨迹信息接口 -->
	 <bean id="PA_iPolicyAcountOherInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002705.impl.PolicyAcountOherInfoServiceImpl">
	  <property name="iPolicyAccountDao" ref="PA_policyAccountDao"></property>
	 </bean>

	<!-- 保险帐户信息接口 -->
	<bean id="PA_insuAccountInfoQueryForRIService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002707.impl.InsuAccountInfoQueryForRIServiceImpl" >
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="policyAccountDao" ref="PA_policyAccountDao"/>
	</bean>
	<!-- 个单被保人信息接口 -->
	<bean id="PA_contInsuredInfoQueryForRIService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002709.impl.ContInsuredInfoQueryForRIServiceImpl" >
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	</bean>

	
	<!-- 保单未领生存金查询 -->
	<bean id="PA_queryUnclaimLiGoldService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002647.impl.QueryUnclaimLiGoldServiceImpl" >
	    <property name="payPlanDao" ref="PA_payPlanDao"/>
	</bean>
	
	<!-- 保费缴费信息接口  -->
	<bean id="PA_premPaymentInfService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002697.impl.PremPaymentInfServiceImpl" >
	    <property name="premArapDao" ref="PA_premArapDao"/>
	</bean>
	<!-- 保险帐户表记价履历接口 -->
	<bean id="PA_iqueryacountservice" class="com.nci.tunan.pa.impl.peripheral.service.r00101002679.impl.QueryAcountServiceImpl">
	  <property name="ipolicyaccounttranslistdao" ref="PA_policyAccountTransListDao"></property>
	</bean>
	
	<!-- 关联保单信息查询接口  -->
	<bean id="PA_associationPolicyService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002630.impl.AssociationPolicyService">
	
		 <property name="contractMasterDao" ref="PA_contractMasterDao"/>
		 <property name="csContractMasterDao" ref="PA_csContractMasterDao"/>
		 <property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
		 <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		 <property name="businessProductDao" ref="PA_businessProductDao"/>
		 <property name="contractRelationDao" ref="PA_contractRelationDao"/>
	</bean>
	
	<!-- 投被保人基本信息校验及电话重复性校验接口 -->
	<bean id="PA_custBaseInfoAndTelValidateService" class="com.nci.tunan.pa.impl.peripheral.service.r00102002743.impl.CustBaseInfoAndTelValidateServiceImpl" >
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="districtDao" ref="PA_districtDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
	</bean>
	<!--重要信息和关系信息校验接口  -->
	<bean id="PA_importemtInfoQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r00102002741.impl.ImportemtInfoQueryServiceImpl">
		<property name="iCustomerDao" ref="PA_customerDao"></property>
		<property name="iPolicyHolderDao" ref="PA_policyHolderDao"></property> 
		<property name="iInsuredListDao" ref="PA_insuredListDao"></property>
		<property name="iContractMasterDao" ref="PA_contractMasterDao"></property>
	</bean>	
	<!-- 社保状态变更查询接口 -->
	<bean id="PA_iYDBQContQuerySrvBindingQSPortService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002758.impl.YDBQContQuerySrvBindingQSPortServiceImpl">
		<property name="contractAgentDao" ref="PA_contractAgentDao"></property>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"></property>
		<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
	</bean>
    <!--税收居民身份采集校验接口-->
	<bean id="PA_identityCollectionCheckService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002764.impl.IdentityCollectionCheckServiceImpl">
         <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
         <property name="insuredListDao" ref="PA_insuredListDao"></property>
         <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
         <property name="customerDao" ref="PA_customerDao"></property>
         <property name="beneService" ref="PA_beneService"/>
         <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
         <property name="districtDao" ref="PA_districtDao"/>
	</bean>	
	
		<!-- 保单业务人员查询 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r04301000286.impl.QueryAgentServiceImpl" id="PA_QueryAgentServiceImpl">
	   <property name="contractAgentDao" ref="PA_contractAgentDao"></property>
	</bean>
	
	<!-- 健康无忧（尊享版）老客户校验接口-->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101002797.impl.OldCustomerVerifyServiceImpl" id="PA_oldCustomerVerifyService">
	</bean>
	
	<!-- 分红类型判断接口--> 
	<bean class="com.nci.tunan.cs.impl.peripheral.service.r00101002810.impl.JudgeBonusTypeServiceImpl" id="PA_judgeBonusTypeServiceImpl">
		 <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
		 <property name="bonusAllocateDao" ref="PA_bonusallocateDao"></property>
		 <property name="prdProductQueryService" ref="PA_prdService" />
		 <property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	
	<!-- 查询现金分红历史信息 -->
    <bean id="PA_HisttoryDivedendService" class="com.nci.tunan.pa.impl.peripheral.service.r00101002808.iml.QueryCashDividendServiceImpl">
       <property name="bonusAllocateDao" ref="PA_bonusallocateDao"></property>
    </bean>	
    
	
	<!-- 双录自保件 -->
	<bean id="PA_SelfInsuredPartService" class="com.nci.tunan.cs.impl.peripheral.service.r00101002795.impl.QuerySelfInsuredServiceImpl">
	     <property name="agentDao" ref="PA_agentPADao"></property>
	</bean>
	
	<!-- 证件类型与年龄校验接口 -->
	<bean id="PA_IdocumentTypeAndAgeCheckService" class="com.nci.tunan.pa.impl.peripheral.service.r00102002992.impl.documentTypeAndAgeCheckServiceImpl">
	     <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"></property>
	     <property name="insuredListDao" ref="PA_insuredListDao"></property>
	     <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
	     <property name="customerDao" ref="PA_customerDao"></property>
	</bean>

    <!-- 投保人保单缴费信息查询接口 -->
    <bean id="policyHolderQueryPayFeeAmountService" class="com.nci.tunan.pa.impl.peripheral.service.r00101003048.impl.PolicyHolderQueryPayFeeAmountServiceImpl">
    	<property name="payerAccountDao" ref="PA_payerAccountDao"></property>
    	<property name="policyholderCustomerDao" ref="PA_customerDao"></property>
    	<property name="policyholderDao" ref="PA_policyHolderDao"></property>
    	<property name="premArapDao" ref="PA_premArapDao"></property>
    	<property name="capService" ref="PA_capIAS"></property>
    </bean>

	
	
	<!-- 核心系统保全规则校验接口 -->
	<bean id="PA_ICoreSystemRescueRulecheckService" class="com.nci.tunan.pa.impl.peripheral.service.r00101003050.impl.coreSystemRescueRulecheckServiceImpl">
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="customerDao" ref="PA_customerDao"/>
	    <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	    <property name="insuredListDao" ref="PA_insuredListDao"/>
	    <property name="addressDao" ref="PA_addressDao"/>
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="paCusotmerService" ref="PA_paCustomerService"/>
	    <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	</bean>
	
	<!-- 客户九要素校验 -->
	<bean id="PA_PolicyNineFactoryCheckService" class="com.nci.tunan.pa.impl.peripheral.service.r00101003049.impl.PolicyNineFactoryCheckServiceImpl">
	   <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
	   <property name="addressDao" ref="PA_addressDao"></property>
	   <property name="customerDao" ref="PA_customerDao"></property>
	</bean>

	

	<!-- 客户信息真实性校验接口  -->  
	<bean id="PA_CustomerInfoAuthenticityCheckService" class="com.nci.tunan.pa.impl.peripheral.service.r00101003066.impl.CustomerInfoAuthenticityCheckServiceImpl">
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
	    <property name="insuredListDao" ref="PA_insuredListDao"/>
	    <property name="addressDao" ref="PA_addressDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	    <property name="customerDao" ref="PA_customerDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
		<!-- #105170 add -->
		<property name="csEntryDao" ref="PA_csEntryDao"/>
    	<property name="csOrgDao" ref="PA_orgDao"/>
	</bean>
	<!-- IO客户职业类别变更试算-->
	 <bean id="PA_InsuredJobChangeQueryImpl" class="com.nci.tunan.pa.impl.peripheral.service.r06802003102.impl.InsuredJobChangeQueryServiceImpl">
	 
	 </bean>
	 
	  <!--同步该客户下查询保单接口（同步范围查询接口）  -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r06401003111.impl.QueryallpolicysOftheCustomerServiceImpl" id="PA_iQueryallpolicysOftheCustomerService">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="csCodeTableService" ref="PA_csCodeTableService"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
	</bean>
    
     <!-- common -->
	<bean id="PA_IPolicyStatusServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.common.impl.PolicyStatusServiceImpl">
    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
    <property name="policyAccountDao" ref="PA_policyAccountDao"/>
    <property name="autoRFandRLDao" ref="PA_autoRFandRLDao"/>
    <property name="premArapDao" ref="PA_premArapDao"></property>
	<property name="capIAS" ref="PA_capIAS"/>
	<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"></property>
	<property name="clmService" ref="PA_pa_clmService"/>
	<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
	</bean>
	<!-- 保单列表信息查询 -->
	<bean id="PA_IQueryPolicyListInformationService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003109.impl.QueryPolicyListInformationServiceImpl">
    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
    <property name="customerDao" ref="PA_customerDao" />
    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
    <property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
    <property name="contractRelationDao" ref="PA_contractRelationDao"/>
	</bean>
		<!-- 移动保全2.0 复效查询接口 -->
	<bean id="PA_CSContReinstateQueryUccService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003160.impl.CSContReinstateQueryServiceImpl">
   	</bean>

	 <!-- 微信续投保表单信息查询接口 -->
	 <bean id="PA_IQueryWechatRenewalService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900020.impl.QueryWechatRenewalServiceImpl">
	      <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
	      <property name="customerDao" ref="PA_customerDao"></property>
	 </bean>
	 
	<!-- 续收微天下保单查询接口 -->
	<bean id="PA_IWTXQueryPolicyCodeService" class="com.nci.tunan.pa.impl.peripheral.service.r00101003215.impl.WTXQueryPolicyCodeServiceImpl">
   	<property name="customerDao" ref="PA_customerDao"/>
   	</bean>

    <!-- 心圆福职域保单列表查询接口 -->
	<bean id="PA_XYFZPolicyListService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900086.impl.XYFZPolicyListServiceImpl">
	<property name="customerDao" ref="PA_customerDao"/>
	<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
   	</bean>
   	
   	<!-- 心圆福职域保单详情查询接口 -->
   	<bean id="PA_XYFZPolicyDetailsService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900087.impl.XYFZPolicyDetailsServiceImpl">
   	<property name="customerDao" ref="PA_customerDao"/>
   	</bean>

	<!-- 新增附加险 -保单列表查询接口 -->
	<bean id="PA_IQueryNsPolicyService" class="com.nci.tunan.cs.impl.peripheral.service.r06401003137.Impl.QueryNsPolicyServiceImpl">
    <property name="iQueryNsPolicyDao" ref="PA_iQueryNsPolicyDao"/>
    <property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
    <property name="cspovcfgdao" ref="PA_cspovcfgdao"/>
	</bean>
	
	<!--柜面自助终端  红利信息查询(打印通知书类) -->
	<bean id="PA_IBonusInfoQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003161.impl.BonusInfoQueryServiceImpl">
    <property name="documentDao" ref="PA_paDocumentDao"/>
   	<property name="csPrintEndorseService" ref="PA_csPrintEndorseService"/>
	<property name="customerDao" ref="PA_customerDao"/>
	<property name="policyHolderDao" ref="PA_policyHolderDao"/>
	<property name="outterDealUCC" ref="PA_outterDealUcc"/>
	<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
	</bean>

	<!--可续保转换险种清单列表  -->
	<bean id="PA_eableRenewRiskService" class="com.nci.tunan.pa.impl.peripheral.service.r06802003146.impl.EableRenewRiskServiceImpl"/>

	
	<!--续期交费信息变更-保单列表查询接口-->
	<bean id="PA_IQueryRenewalPoliciesListService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003158.impl.QueryRenewalPoliciesListServiceImpl">
    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
    <property name="customerDao" ref="PA_customerDao" />
    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
    <property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
    <property name="contractRelationDao" ref="PA_contractRelationDao"></property>
    <property name="businessProductDao" ref="PA_businessProductDao"></property>
	</bean>
	
	<!--续保险种转换-保单列表查询接口-->
	<bean id="PA_IQueryTransformationPolicyListService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003147.impl.QueryTransformationPolicyListServiceImpl"/>
	
	<!-- 受理号查询受理信息 -->
	<bean id="PA_QueryAgentDetailService" class="com.nci.tunan.pa.impl.peripheral.service.r06801003144.impl.QueryAgentDetailServiceImpl">
	     <property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"></property>
	     <property name="customerDao" ref="PA_customerDao"></property>
	     <property name="bankAccountDao" ref="PA_bankAccountDao"></property>
	     <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
	     <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
	</bean>
	
	<!-- 缴费信息变更接口变更 -投保人保单信息查询接口-->
	<bean id="PA_IQueryPolicyHolderContNoService" class="com.nci.tunan.pa.impl.peripheral.service.r00101003201.impl.QueryPolicyHolderContNoServiceImpl">
    	<property name="contractMasterDao" ref="PA_contractMasterDao"/>
    	<property name="customerDao" ref="PA_customerDao" />
    	<property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
    	<property name="payerAccountDao" ref="PA_payerAccountDao"/>
    	<property name="acknowledgementDao" ref="PA_policyAcknowledgementDao"/>
    	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
    	<property name="businessProductDao" ref="PA_businessProductDao"/>
    	<property name="insuredListDao" ref="PA_insuredListDao"></property>
	</bean>
	
	 <!-- 柜面自助：贷款续贷详情查询接口   -->
	 <bean id="PA_QueryLoanContinuedLoanService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003255.impl.QueryLoanContinuedLoanServiceImpl">
          <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"></property> 
          <property name="calculateCashValueService" ref="PA_calcCashValueService"></property> 
          <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property> 
	 </bean>
	
	<!-- 查询个人收费信息 -->
	<bean id="PA_QueryPerPremService" class="com.nci.tunan.pa.impl.peripheral.service.r00101003033.impl.QueryPerPremArapServiceImpl">
	      <property name="premArapDao" ref="PA_premArapDao"></property>
	      <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
	</bean>
	<!-- 受益人变更-保单列表信息查询 -->
	<bean id="PA_QueryBeneChangeService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003159.impl.QueryBeneChangePolicyServiceImpl"/>
	
	<!-- 柜面自助：贷款清偿客户保单查询接口   -->
	<bean id="PA_QueryPolicyInfoOfCustomerLoanAndPayOffService" class="com.nci.tunan.pa.impl.peripheral.service.r06401003240.impl.QueryPolicyInfoOfCustomerLoanAndPayOffServiceImpl">
          <property name="customerDao" ref="PA_customerDao" />
          <property name="contractMasterDao" ref="PA_contractMasterDao"/>
          <property name="insuredListDao" ref="PA_insuredListDao"></property>
          <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
          <property name="policyAccountDao" ref="PA_policyAccountDao"/>
          <property name="businessProductDao" ref="PA_businessProductDao"/>
          <property name="contractExtendDao" ref="PA_contractExtendDao"/>
          <property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
          <property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
          <property name="payPlanDao" ref="PA_payPlanDao"/>
          <property name="premArapDao" ref="PA_premArapDao"></property>
	      <property name="capIAS" ref="PA_capIAS"/>
	</bean>
<!-- 移动保全2.0 年金满期金领取查询接口 -->
	<bean id="PA_CsendorseAGQueryServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.r06401003288.impl.CsendorseAGQueryServiceImpl">
   	</bean>
   	
   	<!-- 客户保单基本信息查询接口 -->
	<bean id="PA_IQueryCusPlyBasicInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900056.impl.QueryCusPlyBasicInfoServiceImpl">
	      <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
	      <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
	      <property name="contractProductDao" ref="PA_contractProductDao"></property>
	      <property name="insuredListDao" ref="PA_insuredListDao"></property>
	      <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
	      <property name="inbService" ref="PA_nbIAS"></property>
	</bean>
	<!-- 贷款清偿-保单列表信息查询 -->
	<bean id="PA_queryPolicyListLoanRepaymentService" class="com.nci.tunan.pa.impl.peripheral.service.r00101900106.impl.QueryPolicyListLoanRepaymentServiceImpl">
	    <property name="policyInfoByPolicyCodeService" ref="PA_policyInfoByPolicyCodeService"></property>
	    <property name="policyInfoByIdNoService" ref="PA_policyInfoByIdNoService"></property>
	    <property name="policyInfoService" ref="PA_policyInfoServiceImpl"></property>
	      <property name="customerDao" ref="PA_customerDao"></property>
	
	</bean>

  
    <!-- PolicyInfoByPolicyCodeService -->
    <bean id="PA_policyInfoByPolicyCodeService" class="com.nci.tunan.pa.impl.peripheral.service.r00101900106.impl.PolicyLoanInfoByPolicyCodeService">
        <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"></property>
        <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
        <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
        <property name="insuredListDao" ref="PA_insuredListDao"></property>
        <property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"></property>
        <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
        <property name="customerDao" ref="PA_customerDao"></property>
        <property name="clmService" ref="PA_pa_clmService"/>
        <property name="businessProductDao" ref="PA_businessProductDao"></property>
    </bean>
    
    <bean id="PA_policyInfoByIdNoService" class="com.nci.tunan.pa.impl.peripheral.service.r00101900106.impl.PolicyInfoByIdNoServiceImpl">
        <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"></property>
        <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
        <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
        <property name="insuredListDao" ref="PA_insuredListDao"></property>
        <property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"></property>
        <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
        <property name="customerDao" ref="PA_customerDao"></property>
        <property name="clmService" ref="PA_pa_clmService"/>
        <property name="businessProductDao" ref="PA_businessProductDao"></property>
    </bean>
    
    <bean id="PA_policyInfoServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.r00101900106.impl.PolicyInfoServiceImpl">
        <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"></property>
        <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
        <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
        <property name="insuredListDao" ref="PA_insuredListDao"></property>
        <property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"></property>
        <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
        <property name="customerDao" ref="PA_customerDao"></property>
        <property name="clmService" ref="PA_pa_clmService"/>
        <property name="csPremArapDao" ref="PA_csPremArapDao"/>
        <property name="businessProductDao" ref="PA_businessProductDao"></property>
    </bean>
    
    
	   <!-- 主险附加险险service -->
    <bean id="PA_additionBusiInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900318.impl.AdditionBusiInfoServiceImpl">
        <property name="contractRelationDao" ref="PA_contractRelationDao"></property>
        <property name="policyHolderDao" ref="PA_policyHolderDao"></property>
        <property name="insuredListDao" ref="PA_insuredListDao"></property>
   		<property name="contractMasterDao" ref="PA_contractMasterDao"></property>
   		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
    </bean>
    
    <bean id="PA_hjbPolicyRelationQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900321.impl.HJBPolicyRelationQueryService">
       <property name="contractRelatioinDao" ref="PA_contractRelationDao"></property>
       <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
       <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
       <property name="businessProductDao" ref="PA_businessProductDao"></property>
    </bean>
    
    <!-- 个人渠道绩优业务员信息查询 -->
    <bean id="PA_queryAgentInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900354.impl.QueryAgentInfoServiceImpl">
         <property name="contractAgentDao" ref="PA_contractAgentDao"></property>
    </bean>
    
    <!-- 实时孤儿单查询通知接口B -->
    <bean id="PA_queryAndUploadOrphanListService" class="com.nci.tunan.pa.impl.peripheral.service.queryAndUploadOrphan.impl.QueryAndUploadOrphanListServiceImpl">
         <property name="orphanParameterDao" ref="PA_orphanParameterDao"></property>
         <property name="orphanPolicyDao" ref="PA_orphanPolicyDao"></property>
         <property name="orphanParameterDetailDao" ref="PA_orphanParameterDetailDao"></property>
         <property name="orphanParameterService" ref="PA_orphanParameterService"></property>
    </bean>
    
    <!-- 追加保费-保单列表查询接口 -->
	<bean id="PA_queryAdditionalpremiumServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.r06401900379.impl.QueryAdditionalpremiumServiceImpl">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
		<property name="prdService" ref="PA_prdService"/> 
		<property name="premArapDao" ref="PA_premArapDao"/> 
	</bean> 
	<!--保单贷款续贷列表查询:P0000190038 -->
	<bean id="PA_PolicyLoanRLServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.r06801900399.impl.PolicyLoanRLServiceImpl">
	</bean>
	
	<!-- 双录互保件查询接口 -->
	<bean id="PA_dualRecordingMutualGuaranteeServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.r06401900407.impl.DualRecordingMutualGuaranteeServiceImpl">
		<property name="agentDao" ref="PA_agentPADao"/> 
	</bean> 
	
	<!--中保信保单编码路由快查  -->
    <bean id="PA_PolicySequenceNoQueryServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.judgepolicysequencenoexist.impl.PolicySequenceNoQueryServiceImpl">
        <property name="medicalDao" ref="PA_shMedicalDao"/>
    </bean>
    <!--银代业务客户资料采集优化-保单详情查询  -->
    <bean id="PA_QueryPolicyDetailsServiceImpl" class="com.nci.tunan.pa.impl.peripheral.service.r06401900454.impl.QueryPolicyDetailsServiceImpl">
    	<property name="contractMasterDao" ref="PA_contractMasterDao"/>
    	<property name="contractProductDao" ref="PA_contractProductDao"/>
    </bean>
    <!--银保通客户资料采集需求（补充资料）-保单查询  -->
    <bean id="PA_WXPolicyQueryServiceImpl" class="com.nci.tunan.pa.impl.querywxpolicyinfo.exports.iwxpolicyqueryucc.wxpolicyquery.service.impl.WXPolicyQueryServiceImpl">
        <property name="wxPolicyQueryDao" ref="PA_WXPolicyQueryDao"/>
    </bean>
    
    <!--第二年末现金价值查询接口 -->
    <bean id="PA_pasPrintCVQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900468.impl.PasPrintCVQueryServiceImpl">
        <property name="prdIAS" ref="PA_prdIAS"/>
    </bean>
    
    <!--保单贷款保单查询接口 -->
    <bean id="PA_LNPolicyQueryYDBQService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900483.impl.LNPolicyQueryYDBQServiceImpl">
    </bean>
    
    <!--保单续贷保单列表查询接口 -->
    <bean id="PA_RLPolicyQueryYDBQService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900472.impl.RLPolicyQueryYDBQServiceImpl">
    </bean>
    
    <!--累积生息账户领取详情查询接口 -->
    <bean id="PA_QueryAIDeitailService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900554.impl.QueryAIDeitailServiceImpl">
    </bean> 

    <!--核心险种信息查询接口 -->
    <bean id="PA_WXPolicyQueryHXXZService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900495.impl.WXPolicyQueryHXXZServiceImpl">
    </bean> 

    <!--年金满期金给付详情查询接口 -->
    <bean id="PA_QueryPolicyAGInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900545.impl.QueryPolicyAGInfoServiceImpl">
    </bean> 
    
    <!--万能险部分领取详情查询接口 -->
    <bean id="PA_QueryPGDeitailService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900564.impl.QueryPGDeitailServiceImpl">
    </bean> 

    <!--随信通续期缴费信息查询接口 -->
    <bean id="PA_queryPCInfoForSXTService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900592.impl.QueryPCInfoForSXTServiceImpl">
    </bean> 

    <!--客户真实性校验接口 -->
    <bean id="PA_checkCustomerService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900558.impl.CheckCustomerServiceImpl">
    <!--#111547 云翼项目新掌上新华一号一人需求
    //add by cuiqi_wb
    //2022-4-22
    -->
        <property name="csEntryDao" ref="PA_csEntryDao"/>
    	<property name="csOrgDao" ref="PA_orgDao"/>
    </bean> 

    <!--ABS现价计算请求通知接口 -->
    <bean id="PA_absCashValNoticeService" class="com.nci.tunan.pa.impl.peripheral.service.r06402900572.impl.ABSCashValNoticeServiceImpl">
    </bean> 
    
    <!--身故受益人详情查询接口 -->
    <bean id="PA_DeceasedBeneficiaryQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900546.impl.DeceasedBeneficiaryQueryServiceImpl">
    </bean> 
    
    <!--满期不续保保单查询接口-->
    <bean id="PA_NoRenewalExpirationQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900549.impl.NoRenewalExpirationQueryServiceImpl">
    <property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
    </bean> 
    
    <!--退保保单查询接口-->
    <bean id="PA_PolicyListQuerySurrenderService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900621.impl.PolicyListQuerySurrenderServiceImpl">
    </bean> 
    
    <!--退保保单查询接口-->
    <bean id="PA_QueryCTPolicyForYdbq2Service" class="com.nci.tunan.pa.impl.peripheral.service.r06401900620.impl.QueryCTPolicyForYdbq2ServiceImpl">
    </bean>
    
    <!--累积生息账户领取-保单列表查询接口-->
    <bean id="PA_QueryAIPolicyService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900560.impl.QueryAIPolicyServiceImpl">
    </bean>
    
    <!--保单列表查询接口-万能险账户领取-->
    <bean id="PA_QueryPGPolicyForYdbq2Service" class="com.nci.tunan.pa.impl.peripheral.service.r06401900627.impl.QueryPGPolicyForYdbq2ServiceImpl">
    </bean>

    <!--万能投连账户信息查询接口-->
    <bean id="PA_QueryPGPolicyForGwService" class="com.nci.tunan.pa.impl.peripheral.service.R06401900653.impl.QueryPGPolicyForGwServiceImpl">
    </bean>

    <!--投连万能账户信息详情查询接口-->
    <bean id="PA_QueryPGPolicyDeitailForGwService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900654.impl.QueryPGPolicyDeitailForGwServiceImpl">
    </bean>
    
    <!-- 续期缴费变更同步列表查询接口 -->
    <bean id="PA_RenewalFeeInfoQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900540.impl.RenewalFeeInfoQueryServiceImpl">
    	<property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
    </bean>
    
    <!--退保保全记录查询接口  -->
    <bean id="PA_PolicyCancellationKeepIntactQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06801900672.impl.PolicyCancellationKeepIntactRecordQueryServiceImpl">
    </bean>
    
    <!--追加保费保全记录查询接口  -->
    <bean id="PA_AddToPremiumQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06801900673.impl.AddToPremiumPreserveQueryServiceImpl">
    </bean>
    
    <!-- 智慧柜员机_北京地区人身险销售人员自保件和互保件管理接口 -->
    <bean id = "PA_QrySelfInsFlagAndMutualInsFlagService" class = "com.nci.tunan.pa.impl.peripheral.service.r06801900662.impl.QrySelfInsFlagAndMutualInsFlagServiceImpl">
    </bean> 
    <!-- 万能险账户抵扣续期保费轨迹接口 -->
    <bean id="PA_QueryUIAccountDeductionPremiumLocusService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900668.impl.QueryUIAccountDeductionPremiumLocusServiceImpl">
    </bean>
    <!-- 加保保单列表查询接口  -->
    <bean id="PA_QueryPolicyListIncreaseOfPremiumService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900707.impl.QueryPolicyListIncreaseOfPremiumServiceImpl">
    </bean>
    
    <!-- 特殊人员类型校验接口 -->
    <bean id="PA_SpecialPerTypeCheckService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900718.impl.SpecialPerTypeCheckServiceImpl">
    </bean> 
    
    <!--根据客户五要素查询保单信息接口-->
   <bean id="PA_PersonalinsurancePolicyService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900708.impl.PersonalinsurancePolicyServiceImpl">
   		<!--  <property name="contractMasterDao" ref="PA_contractMasterDao"/> -->
   </bean> 
   
   <!--险种保单查询接口-->
   <bean id="PA_issueValidPolicyQueryService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900715.impl.IssueValidPolicyQueryServiceImpl">
   </bean>  
    
    <!--根据客户id查询保单层，险种层信息接口-->
   <bean id="PA_IQryPolicyAndContractService" class="com.nci.tunan.pa.impl.qrypolicyandcontract.service.impl.QryPolicyAndContractServiceImpl">
   		<!-- <property name="qryPolicyAndContractDao" ref="PA_qryPolicyAndContractDao"/> -->
   </bean> 
   <!--根据客户Id 查询客户九要素-->
   <bean id="PA_QueryCustomerInformationByNineElementsService" class="com.nci.tunan.pa.impl.customerinformation.service.impl.QueryCustomerInformationByNineElementsServiceImpl"></bean>
   
   
   <!--根据客户Id 查询客户九要素-->
   <bean id="PA_QueryPolicyServicePersonInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900761.impl.QueryPolicyServicePersonInfoServiceImpl">
   <property name="agentDao" ref="PA_agentPADao"/>
   </bean>
    
    <!-- 根据客户五要素查询是否为业务员 -->
    <bean id = "PA_queryisSalespersonFlagService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900757.impl.QueryisSalespersonFlagServiceImpl">
    <property name="agentDao" ref="PA_agentPADao"/>
    </bean>
    
    <!-- 未成年人死亡风险保额累计查询接口 -->
    <bean id = "PA_minorsRiskOfDeathCoverageQueryService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900747.impl.MinorsRiskOfDeathCoverageQueryServiceImpl">
    </bean>
    
    
     <!-- 累积生息账户信息查询接口 -->
    <bean id = "PA_cumuInteAccInfoQueryService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900790.impl.CumuInteAccInfoQueryServiceImpl">
    </bean>
    
    <!-- 通知书列表查询接口 -->
    <bean id = "PA_documentListQueryService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900789.impl.DocumentListQueryServiceImpl">
    </bean>
     <!-- 分红信息保单列表查询接口 -->
    <bean id = "PA_queryBonusInfoListService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900826.impl.QueryBonusInfoListServiceImpl">
    </bean>
    <!--  官微累积生息账户信息查询接口 -->
    <bean id = "PA_queryAccumulateCapitalListService" class = " com.nci.tunan.pa.impl.peripheral.service.r06401900827.impl.QueryAccumulateCapitalListServiceImpl">
    </bean>
    <!-- 代理人信息查询接口 -->
    <bean id = "PA_queryContractAgentInfoService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900832.impl.QueryContractAgentInfoServiceImpl">
    </bean>
    
    <!-- 通知书报告书查看下载信息回传接口 -->
   	<bean id="PA_INoticeReportViewAndDownloadService" class="com.nci.tunan.pa.impl.peripheral.service.r06402900835.impl.NoticeReportViewAndDownloadServiceImpl">
   	<property name="documentDao"  ref="PA_paDocumentDao"/>
   	</bean>
   	
   	    <!-- 通知书列表查询接口 -->
    <bean id = "PA_queryNotificationReportPolicyListService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900834.impl.QueryNotificationReportPolicyListServiceImpl">
    </bean>
   	
   	<!-- 通知书信息查询接口 -->
    <bean id = "PA_queryDocumentInfoService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900848.impl.QueryDocumentInfoServiceImpl">
    </bean>
    
        <!-- 保单详细信息查询接口 -->
    <bean id = "PA_queryPolicyDetailsInfoService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900830.impl.QueryPolicyDetailsInfoServiceImpl">
    </bean>
    
   	<!-- 保单原交费账户信息查询接口 -->
   	<bean id="PA_queryPolicyPrimaryPaymentAccountInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900813.impl.IQueryPolicyPrimaryPaymentAccountInfoServiceImpl">
   	</bean>

	<!-- 银保信账户查询接口 -->
	<bean id="PA_queryBankInsurAccountInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900874.impl.QueryBankInsurAccountInfoServiceImpl">
		<property name="specialAccountInfoDao" ref="PA_specialAccountInfoDao"/>
		<property name="specialAccountRelationDao" ref="PA_specialAccountRelationDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="platInvestAccBindTraceDao" ref="PA_platInvestAccBindTraceDao"/>
		<property name="bankDao" ref="PA_bankDao"/>
	</bean>
	<!-- 资金账户绑定成功确认接口 -->
	<bean id="PA_accountBindingConfirmationService" class="com.nci.tunan.pa.impl.peripheral.service.r06402900875.impl.AccountBindingConfirmationServiceImpl">
	</bean>
	<!-- 根据客户信息查询保单信息接口 -->
    <bean id = "PA_queryPolicyInfoByCustomInfoService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900870.impl.QueryPolicyInfoByCustomInfoServiceImpl">
    </bean>
    
    <!-- 贷款账户查询接口技术需求任务 #144538 掌上新华二期-贷款信息查询功能（需求15）-个险新核心-保单 -->
    <bean id = "PA_queryLoanAccountInfoService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900887.impl.QueryLoanAccountInfoServiceImpl">
    </bean>
    
     <!-- 贷款账户查询接口技术需求任务 #144538 掌上新华二期-贷款信息查询功能（需求15）-个险新核心-保单 -->
    <bean id = "PA_customerInformationQueryPolicyCodeService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900924.impl.CustomerInformationQueryPolicyCodeServiceImpl">
    </bean>
    <!-- 续领年金生存认证提交接口 -->
    <bean id = "PA_renewalOfPensionSurvivalSubService" class = "com.nci.tunan.pa.impl.peripheral.service.r06402900922.impl.RenewalOfPensionSurvivalSubServiceImpl">
    </bean>
    
    <!-- 个险被保险人信息查询接口 -->
    <bean id = "PA_queryInsuredlistByCusnameAndCusNoAndAgentCodeService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900928.impl.QueryInsuredlistByCusnameAndCusNoAndAgentCodeServiceImpl">
    </bean>
    
    <!-- 保费信息查询接口 -->
    <bean id = "PA_premiumInformationInquireService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900882.impl.PremiumInformationInquireServiceImpl">
    </bean>
    
    <!-- 续领年金生存认证查询接口 -->
    <bean id = "PA_renewalOfAnnuitySurvivalCertificationService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900920.impl.RenewalOfAnnuitySurvivalCertificationServiceImpl">
    </bean> 
    
    	    <!-- 睡眠保单查询接口 -->
	<bean id="PA_querySleepPolicyInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900934.impl.QuerySleepPolicyInfoServiceImpl">
		<property name="policyMarkingInfoDao" ref="PA_policyMarkingInfoDao"></property>
		<property name="contractMasterDao" ref="PA_contractMasterDao" />
		<property name="insuredListDao" ref="PA_insuredListDao" />
	</bean>
	
	<!-- 睡眠保单通知情况更新接口 -->
	<bean id="PA_updateSleepPolicyInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06402900936.impl.UpdateSleepPolicyInfoServiceImpl">
			<property name="policyMarkingInfoDao" ref="PA_policyMarkingInfoDao"></property>
			<property name="policyMarkingLogDao" ref="PA_policyMarkingLogDao"></property>
	</bean>
			<!-- 保单年金满期金信息查询接口 -->
	<bean id="PA_queryAnnuAndExpireInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900942.impl.QueryAnnuAndExpireInfoServiceImpl">
	</bean>
	
	<!-- 保单列表信息分页查询接口 -->
    <bean id = "PA_paginatePolicyListInformationService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900932.impl.PaginatePolicyListInformationServiceImpl">
    </bean>
    
    <!-- 投保人保单列表查询接口  -->
	<bean id="PA_queryPolicyHolderChangeInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401900974.impl.QueryPolicyHolderChangeInfoServiceImpl">
	</bean>
    
    <!-- 客户三要素查询保单信息列表接口 -->
    <bean id = "PA_queryPolicyInfosByThreeElementsOfCustomerService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900976.impl.QueryPolicyInfosByThreeElementsOfCustomerServiceImpl">
    </bean>
    
    <!-- 客户号查询保单号列表接口 -->
    <bean id = "PA_queryPolicyInfoListByPolicyListService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401901046.impl.QueryPolicyInfoListServiceImpl">
    </bean>
    
    <!-- 减保保单列表查询接口 -->
    <bean id = "PA_queryReductionPolicyInformationListService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900960.impl.QueryReductionPolicyInformationListServiceImpl">
    </bean>
    
    <!-- 保单补发保单列表查询接口 -->
    <bean id = "PA_queryPolicyReplacementInformationService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401901074.impl.QueryPolicyReplacementInformationServiceImpl">
    </bean>
    
    <!-- 保单关联保单列表查询接口 -->
    <bean id = "PA_queryPolicyRelatedPolicyListService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401901085.impl.QueryPolicyRelatedPolicyListServiceImpl">
    </bean>
    
    <!-- 领取形式变更-保单列表查询接口 -->
    <bean id = "PA_queryPolicyInfosForChangeOfClaimFormService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900978.impl.QueryPolicyInfosForChangeOfClaimFormServiceImpl">
    </bean>
    
     <!-- 查询客户名下保单号接口 -->
    <bean id = "PA_queryCustomerPolicycodeService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900980.impl.QueryCustomerPolicycodeServiceImpl">
    </bean>
    
     <!-- 职业类别变更-保单列表查询接口 -->
    <bean id = "PA_queryJobUwLevelChangesPolicyListService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900982.impl.QueryJobUwLevelChangesPolicyListServiceImpl">
    </bean>
    
    <!-- 保单号集合查询保单信息接口 -->
    <bean id = "PA_queryPolicyInfoByPolicyListService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401900986.impl.QueryPolicyInfoByPolicyListServiceImpl">
    </bean>
    
    <!-- 查询保全受理号下客户信息（空中柜面）  -->
	<bean id= "PA_QueryCustomerInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06801901056.impl.QueryCustomerInfoServiceImpl">
	</bean>
	
	    <!-- 投保人保单列表查询接口  -->
	<bean id="PA_queryBonuspayPolicyListInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401901075.impl.QueryBonuspayPolicyListInfoServiceImpl">
	</bean>
	<!-- 技术需求任务 #190097 新核心-接口需求-移动保全2.0-新增红利领取领取形式变更功能需求-PAS-2/3  -->
	<bean id="PA_queryBonusClaimFromChangeInfoService" class="com.nci.tunan.pa.impl.peripheral.service.r06401901105.impl.QueryBonusClaimFromChangeInfoServiceImpl">
	</bean>
	
	<!-- TR-20250704-015214 掌上新华攻坚组问题单-PRM-20250703-0006_自助理赔申请时，出险人信息查询缓慢-个险新核心 -->
	<bean id="PA_queryPolListsByCmFiveFactorService" class="com.nci.tunan.pa.impl.peripheral.service.r06401901141.impl.QueryPolListsByCmFiveFactorServiceImpl">
	</bean>
	
	
	 <!-- 客户保单手机号查询接口 -->
    <bean id = "PA_queryPolicyLastMobileTelService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401901069.impl.QueryPolicyLastMobileTelServiceImpl">
    </bean>
    
    <!-- 客户号查询其保单下的客户信息接口-->
    <bean id = "PA_queryAllCustomerByNewCusidService" class = "com.nci.tunan.pa.impl.peripheral.service.r06401901097.impl.QueryAllCustomerByNewCusidServiceImpl">
    </bean>
</beans>