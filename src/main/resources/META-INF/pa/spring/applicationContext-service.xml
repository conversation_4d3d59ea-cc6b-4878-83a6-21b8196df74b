<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 理赔回退 -->
	<bean class="com.nci.tunan.pa.impl.clmrollback.service.impl.CLMRollBackServiceImpl" id="PA_clmRollBackService">
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="extraPremLogDao" ref="PA_extraPremLogDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="extraPremCxDao" ref="PA_extraPremCxDao"/>
		<property name="benefitInsuredLogDao" ref="PA_benefitInsuredLogDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="benefitInsuredCxDao" ref="PA_benefitInsuredCxDao"/>
		<property name="insuredListLogDao" ref="PA_insuredListLogDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="insuredListCxDao" ref="PA_insuredListCxDao"/>
		<property name="contractBeneLogDao" ref="PA_contractBeneLogDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractBeneCxDao" ref="PA_contractBeneCxDao"/>
		<property name="policyHolderLogDao" ref="PA_policyHolderLogDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyHolderCxDao" ref="PA_policyHolderCxDao"/>
		<property name="payerAccountLogDao" ref="PA_payerAccountLogDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="payerAccountCxDao" ref="PA_payerAccountCxDao"/>
		<property name="payerLogDao" ref="PA_payerLogDao"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerCxDao" ref="PA_payerCxDao"/>
		<property name="contractProductLogDao" ref="PA_contractProductLogDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductCxDao" ref="PA_contractProductCxDao"/>
		<property name="contractExtendLogDao" ref="PA_contractExtendLogDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractExtendCxDao" ref="PA_contractExtendCxDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractMasterCxDao" ref="PA_contractMasterCxDao"/>
		<property name="payPlanLogDao" ref="PA_payPlanLogDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanCxDao" ref="PA_payPlanCxDao"/>
		<property name="payPlanPayeeLogDao" ref="PA_payPlanPayeeLogDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="payPlanPayeeCxDao" ref="PA_payPlanPayeeCxDao"/>
		<property name="contractBusiProdLogDao" ref="PA_contractBusiProdLogDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBusiProdCxDao" ref="PA_contractBusiProdCxDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractInvestLogDao" ref="PA_contractInvestLogDao"/>
		<property name="contractInvestCxDao" ref="PA_contractInvestCxDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/> 
		<property name="contractInvestRateLogDao" ref="PA_contractInvestRateLogDao"/>
		<property name="contractInvestRateCxDao" ref="PA_contractInvestRateCxDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="policyFundChargeLogDao" ref="PA_policyFundChargeLogDao"/>
		<property name="policyFundChargeCxDao" ref="PA_policyFundChargeCxDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountLogDao" ref="PA_policyAccountLogDao"/>
		<property name="policyAccountCxDao" ref="PA_policyAccountCxDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAccountStreamLogDao" ref="PA_policyAccountStreamLogDao"/>
		<property name="policyAccountStreamCxDao" ref="PA_policyAccountStreamCxDao"/>
		<property name="fundAssetsDao" ref="PA_fundAssetsDao"/>
		<property name="fundAssetsLogDao" ref="PA_fundAssetsLogDao"/>
		<property name="fundAssetsCxDao" ref="PA_fundAssetsCxDao"/>
		<property name="investUnitPriceCxDao" ref="PA_investUnitPriceCxDao"/>
		<property name="investUnitPriceLogDao" ref="PA_investUnitPriceLogDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="riskAmountLogDao" ref="PA_riskAmountLogDao"/>
		<property name="riskAmountCxDao" ref="PA_riskAmountCxDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="logScopeDao" ref="PA_logScopeDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="capService" ref="PA_capIAS"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="settleReportDao" ref="PA_settleReportDao"/>
		<property name="documentDao" ref="PA_paDocumentDao"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="paBaseAccountService" ref="PA_paBaseAccountService"/>
		<property name="csPolicyAccountDao" ref="PA_csPolicyAccountDao"/>
		<property name="premDao" ref="PA_premDao"/> 
	</bean>
	<!-- 理赔回退（迁移数据） -->
	<bean class="com.nci.tunan.pa.impl.clmrollbackforolddata.service.impl.ClmRollBackForOldDataServiceImpl" id="PA_clmRollBackForOldDataService">
		<property name="proDuctLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
	</bean>
	<!-- 续期核销重算风险保额 -->
	<bean class="com.nci.tunan.pa.impl.renewriskamount.service.impl.RenewRiskAmountServiceImpl" id="PA_renewRiskAmountService">
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="riskAmountArchiveDao" ref="PA_riskAmountArchiveDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
	</bean>

	<!-- 电商小核心-秒杀承保批处理文件服务器 -->
	<bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_seckillFtpServerConfig">
		<property name="ftpServerIp" value="${seckillFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${seckillFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${seckillFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${seckillFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${seckillFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpServerWorkingDirectory" value="${seckillFtpServerConfig.ftpServerWorkingDirectory}"/>
	</bean>
	
	
	<bean class="com.nci.tunan.pa.common.service.impl.SendMailServiceImpl" id="PA_sendMailService">
		<property name="mailServerConfig" ref="mailServerConfig"/>
		<property name="commonBizNoticeLocationDao" ref="commonBizNoticeLocationDao"/>
		<property name="commonBizNoticeDetailDao" ref="commonBizNoticeDetailDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.common.service.impl.FindCodeCalueServiceImpl" id="PA_findCodeValueService">
		<property name="findCodeValueDao" ref="PA_findCodeValueDao"/>
	</bean>
	<!-- 保单履历记录service -->
	<bean class="com.nci.tunan.pa.impl.policyLog.service.impl.PolicyLogServiceImpl" id="PA_policyLogService">
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductLogDao" ref="PA_contractProductLogDao"/>
		<property name="contractProductCxDao" ref="PA_contractProductCxDao"/>
		<property name="policyLogDao" ref="PA_policyLogDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="contractMasterCxDao" ref="PA_contractMasterCxDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBusiProdLogDao" ref="PA_contractBusiProdLogDao"/>
		<property name="contractBusiProdCxDao" ref="PA_contractBusiProdCxDao"/>
		
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractExtendLogDao" ref="PA_contractExtendLogDao"/>
		<property name="contractExtendCxDao" ref="PA_contractExtendCxDao"/>
		
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyHolderLogDao" ref="PA_policyHolderLogDao"/>
		<property name="policyHolderCxDao" ref="PA_policyHolderCxDao"/>
		
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="insuredListLogDao" ref="PA_insuredListLogDao"/>
		<property name="insuredListCxDao" ref="PA_insuredListCxDao"/>
		
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractBeneLogDao" ref="PA_contractBeneLogDao"/>
		<property name="contractBeneCxDao" ref="PA_contractBeneCxDao"/>
		
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="benefitInsuredLogDao" ref="PA_benefitInsuredLogDao"/>
		<property name="benefitInsuredCxDao" ref="PA_benefitInsuredCxDao"/>
		
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerLogDao" ref="PA_payerLogDao"/>
		<property name="payerCxDao" ref="PA_payerCxDao"/>
		
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountLogDao" ref="PA_policyAccountLogDao"/>
		<property name="policyAccountCxDao" ref="PA_policyAccountCxDao"/>
		
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="payerAccountLogDao" ref="PA_payerAccountLogDao"/>
		<property name="payerAccountCxDao" ref="PA_payerAccountCxDao"/>
		
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="extraPremLogDao" ref="PA_extraPremLogDao"/>
		<property name="extraPremCxDao" ref="PA_extraPremCxDao"/>
		
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractInvestLogDao" ref="PA_contractInvestLogDao"/>
		<property name="contractInvestCxDao" ref="PA_contractInvestCxDao"/>
		
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="contractInvestRateLogDao" ref="PA_contractInvestRateLogDao"/>
		<property name="contractInvestRateCxDao" ref="PA_contractInvestRateCxDao"/>
		
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="policyFundChargeLogDao" ref="PA_policyFundChargeLogDao"/>
		<property name="policyFundChargeCxDao" ref="PA_policyFundChargeCxDao"/>
		
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanLogDao" ref="PA_payPlanLogDao"/>
		<property name="payPlanCxDao" ref="PA_payPlanCxDao"/>
		
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="payPlanPayeeLogDao" ref="PA_payPlanPayeeLogDao"/>
		<property name="payPlanPayeeCxDao" ref="PA_payPlanPayeeCxDao"/>
		
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAccountStreamLogDao" ref="PA_policyAccountStreamLogDao"/>
		<property name="policyAccountStreamCxDao" ref="PA_policyAccountStreamCxDao"/>
		
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="riskAmountLogDao" ref="PA_riskAmountLogDao"/>
		<property name="riskAmountCxDao" ref="PA_riskAmountCxDao"/>
		
		<property name="logScopeDao" ref="PA_logScopeDao"/>
		
		<property name="specialAccountRelationDao" ref="PA_specialAccountRelationDao"></property>
		<property name="specialAccountRelationLogDao" ref="PA_specialAccountRelationLogDao"></property>
		<property name="specialAccountRelationCxDao" ref="PA_specialAccountRelationCxDao"></property>
		
<!-- 		<property name="fundAssetsDao" ref="fundAssetsDao" /> -->
<!-- 		<property name="fundAssetsLogDao" ref="fundAssetsLogDao" /> -->
<!-- 		<property name="fundAssetsCxDao" ref="fundAssetsCxDao" /> -->
		
<!-- 		<property name="investUnitPriceDao" ref="investUnitPriceDao" /> -->
<!-- 		<property name="investUnitPriceLogDao" ref="investUnitPriceLogDao" /> -->
<!-- 		<property name="investUnitPriceCxDao" ref="investUnitPriceCxDao" /> -->
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.notice.service.impl.NoticeServiceImpl" id="PA_noticeService">
		<property name="calCashValueService" ref="PA_calcCashValueService"/>
		<property name="contractProductLogDao" ref="PA_contractProductLogDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="findCodeValueService" ref="PA_findCodeValueService"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/> 
		<property name="documentDao" ref="PA_paDocumentDao"/>
		<property name="findCodeValueDao" ref="PA_findCodeValueDao"/>
		<property name="clobDao" ref="PA_paClobDao"/> 
	    <property name="xmlHelperService" ref="PA_xmlHelperService"/>
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="contractProductDao" ref="PA_contractProductDao"/>
	    <property name="businessProductDao" ref="PA_businessProductDao"/>
	    <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	    <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	    <property name="insuredListDao" ref="PA_insuredListDao"/>
	    <property name="contractBeneDao" ref="PA_contractBeneDao"/>
	    <property name="customerDao" ref="PA_customerDao"/>
	    <property name="addressDao" ref="PA_addressDao"/> 
	    <property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
	    <property name="noticeDao" ref="PA_noticeDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="cusApplicationService" ref="PA_cusApplicationService"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
		<property name="organDao" ref="organDao"/>
		<property name="specialBonusDao" ref="PA_specialBonusDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="rossInfoDao" ref="PA_rossInfoDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="bankDao" ref="PA_bankDao"/>
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="calculateIlpuDueFee" ref="PA_calculateIlpuDueFee"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="calICalculateCashValueService" ref="PA_calcCashValueService"/>
			<!-- 计算保单价值 -->
		<property name="cSCalPolicyValueService" ref="PA_cSCalPolicyValueService"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="prdMethodService" ref="PA_prdMethodService"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="calculateEndInterestService" ref="PA_calculateEndInterestService"/>
		<!-- 计算万能险现价 -->
		<property name="csEndorseCTCalculateService" ref="PA_csEndorseCTCalculateService"></property>
		<!-- 查询上次扣费日 -->
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="salesChannelDao" ref="PA_salesChannelDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="queryPolicyService" ref="PA_queryPolicyService"/>
		<property name="survialAnnuityMaturityDao" ref="PA_survialAnnuityMaturityDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="fundGroupTransDao" ref="PA_FundGroupTransDao"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		
		<property name="renewalSuccessfulDAO" ref="PA_renewalSuccessfulDAO"/>
		
	</bean>
	<bean class="com.nci.tunan.pa.impl.notice.service.impl.PrdMethodReloadImpl" id="PA_prdMethodService">
			<property name="prdProductQueryService" ref="PA_prdService"/>
	</bean>
	<bean class="com.nci.tunan.pa.common.service.impl.XMLHelperServiceImpl" id="PA_xmlHelperService">
	        
	</bean>
	
	<bean class="com.nci.tunan.pa.common.service.impl.DetailFeeServiceImpl" id="PA_detailFeeService">
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.common.service.impl.FormulaServiceImpl" id="PA_formulaService">
		<property name="formulaDao" ref="PA_formulaDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="universalSettlementService" ref="PA_universalSettlementService"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
	</bean>
	<!-- 复核账户 start liucmit 2015/08/20 -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.service.impl.ReviewFundAssetsServiceImpl" id="PA_reviewFundAssetsService">
		<property name="fundAssetsDao" ref="PA_fundAssetsDao"/>
		<property name="investAccountInfoDao" ref="PA_investAccountInfoDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
		<property name="boxPositionDao" ref="PA_BoxPositionDao"></property>
	</bean>
	<!-- 复核账户 end -->
	<!-- 实时抽档start -->
	<bean class="com.nci.tunan.pa.impl.renewal.service.impl.RenewExtBathJob" id="PA_renewExtBathJob">
		<property name="renewExtraDao" ref="PA_renewExtraDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="renewExtraRulesService" ref="PA_renewExtraRulesService"/>
<!-- 		<property name="prdIAS" ref="prdIAS" /> -->
<!-- 		<property name="ilpRegularPremDao" ref="ilpRegularPremDao"></property>-->
		<property name="policyDueDateDao" ref="PA_policyDueDateDao"/>
		<property name="cashDao" ref="PA_cashDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
	</bean>
	
	<!-- 启动新续保流程撤销批处理 -->
	<bean class="com.nci.tunan.pa.batch.automaticrenewalextraction.service.impl.RenewalHisProcessRevokeServiceImpl" id="PA_renewalHisProcessRevokeServiceImpl"></bean>
	
	<bean class="com.nci.tunan.pa.impl.renewal.service.impl.RenewExtraRulesImpl" id="PA_renewExtraRulesService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="renewExtraDao" ref="PA_renewExtraDao"/>
<!-- 	<property name="ilpRegularPremDao" ref="ilpRegularPremDao"></property>-->
		<property name="policyDueDateDao" ref="PA_policyDueDateDao"/>
<!-- 		<property name="prdIAS" ref="prdIAS" /> -->
	</bean>
	<!-- 实时抽档end -->

	<!-- 查询投保人信息 -->
	<bean class="com.nci.tunan.pa.impl.querypolicyholderinfo.service.impl.QueryPolicyHolderInfoServiceImpl" id="PA_queryPolicyHolderInfoService">
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>

	<!-- 新契约转保单 -->
	<bean class="com.nci.tunan.pa.impl.createpolicy.service.impl.CreatePolicyServiceImpl" id="PA_createPolicyService">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="persistenceBonusDao" ref="PA_persistenceBonusDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
<!--  	<property name="ilpRegularPremDao" ref="ilpRegularPremDao" />-->	
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="customerSurveyDao" ref="PA_customerSurveyDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="contractInvestStreamDao" ref="PA_contractInvestStreamDao"/>
<!--  	<property name="capitalDistributeDao" ref="capitalDistributeDao" />-->
<!--  	<property name="addInvestDao" ref="addInvestDao" />-->
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="iIlpRecurringTopupDao" ref="PA_iIlpRecurringTopupDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
		<property name="questionaireCustomerParamDao" ref="PA_questionaireCustomerParamDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="precontProductDao" ref="PA_precontProductDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="bankBranchDao" ref="PA_bankBranchDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<!-- 税延表dao注入 -->
		<property name="taxExtensionDao" ref="PA_taxExtensionDao"/>
		<property name="customerTaxInfoDao" ref="PA_customerTaxInfoDao"/>
		<property name="contractProductTaxDao" ref="PA_contractProductTaxDao"/>
		<property name="contractRelationDao" ref="PA_contractRelationDao"/>
		<property name="medicalCardDao" ref="PA_medicalCardDao"/>
		<property name="fundGroupTransDao" ref="PA_FundGroupTransDao"/>
		<property name="payPlanCreateTaskDao" ref="PA_payPlanCreateTaskDao"/>
		<property name="taxCustomerInfoDao" ref="PA_taxCustomerInfoDao"></property>
		<!--是否符合扶贫标准轨迹表dao注入  -->
		<property name="meetPovStandardFlagTrackDao" ref="PA_meetPovStandardFlagTrackDao"></property>
		<property name="specialAccountInfoDao" ref="PA_specialAccountInfoDao"></property>
		<property name="specialAccountRelationDao" ref="PA_specialAccountRelationDao"></property>
		<property name="bankAccountDao" ref="PA_bankAccountDao"></property>
		<property name="submitMessageBocicDao" ref="PA_submitMessageBocicDao"></property>
	</bean>
	
	<!-- 新契约转保单（上海医保） -->
	<bean class="com.nci.tunan.pa.impl.createmedical.service.impl.CreateMedicalServiceImpl" id="PA_createMedicalService" >
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="persistenceBonusDao" ref="PA_persistenceBonusDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="customerSurveyDao" ref="PA_customerSurveyDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="contractInvestStreamDao" ref="PA_contractInvestStreamDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="iIlpRecurringTopupDao" ref="PA_iIlpRecurringTopupDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
		<property name="questionaireCustomerParamDao" ref="PA_questionaireCustomerParamDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="precontProductDao" ref="PA_precontProductDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="bankBranchDao" ref="PA_bankBranchDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="createMedicalDao" ref="PA_createMedicalDao" />
	</bean>
	<!-- 新契约转保单Log（上海医保） -->
	<bean class="com.nci.tunan.pa.impl.createmedical.service.impl.MedicalLogServiceImpl" id="PA_medicalLogService">
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductLogDao" ref="PA_contractProductLogDao"/>
		<property name="contractProductCxDao" ref="PA_contractProductCxDao"/>
		<property name="policyLogDao" ref="PA_policyLogDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="contractMasterCxDao" ref="PA_contractMasterCxDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractBusiProdLogDao" ref="PA_contractBusiProdLogDao"/>
		<property name="contractBusiProdCxDao" ref="PA_contractBusiProdCxDao"/>
		
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractExtendLogDao" ref="PA_contractExtendLogDao"/>
		<property name="contractExtendCxDao" ref="PA_contractExtendCxDao"/>
		
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyHolderLogDao" ref="PA_policyHolderLogDao"/>
		<property name="policyHolderCxDao" ref="PA_policyHolderCxDao"/>
		
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="insuredListLogDao" ref="PA_insuredListLogDao"/>
		<property name="insuredListCxDao" ref="PA_insuredListCxDao"/>
		
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractBeneLogDao" ref="PA_contractBeneLogDao"/>
		<property name="contractBeneCxDao" ref="PA_contractBeneCxDao"/>
		
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="benefitInsuredLogDao" ref="PA_benefitInsuredLogDao"/>
		<property name="benefitInsuredCxDao" ref="PA_benefitInsuredCxDao"/>
		
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payerLogDao" ref="PA_payerLogDao"/>
		<property name="payerCxDao" ref="PA_payerCxDao"/>
		
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountLogDao" ref="PA_policyAccountLogDao"/>
		<property name="policyAccountCxDao" ref="PA_policyAccountCxDao"/>
		
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="payerAccountLogDao" ref="PA_payerAccountLogDao"/>
		<property name="payerAccountCxDao" ref="PA_payerAccountCxDao"/>
		
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="extraPremLogDao" ref="PA_extraPremLogDao"/>
		<property name="extraPremCxDao" ref="PA_extraPremCxDao"/>
		
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractInvestLogDao" ref="PA_contractInvestLogDao"/>
		<property name="contractInvestCxDao" ref="PA_contractInvestCxDao"/>
		
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="contractInvestRateLogDao" ref="PA_contractInvestRateLogDao"/>
		<property name="contractInvestRateCxDao" ref="PA_contractInvestRateCxDao"/>
		
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="policyFundChargeLogDao" ref="PA_policyFundChargeLogDao"/>
		<property name="policyFundChargeCxDao" ref="PA_policyFundChargeCxDao"/>
		
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payPlanLogDao" ref="PA_payPlanLogDao"/>
		<property name="payPlanCxDao" ref="PA_payPlanCxDao"/>
		
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="payPlanPayeeLogDao" ref="PA_payPlanPayeeLogDao"/>
		<property name="payPlanPayeeCxDao" ref="PA_payPlanPayeeCxDao"/>
		
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAccountStreamLogDao" ref="PA_policyAccountStreamLogDao"/>
		<property name="policyAccountStreamCxDao" ref="PA_policyAccountStreamCxDao"/>
		
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="riskAmountLogDao" ref="PA_riskAmountLogDao"/>
		<property name="riskAmountCxDao" ref="PA_riskAmountCxDao"/>
		
		<property name="logScopeDao" ref="PA_logScopeDao"/>
		<property name="createMedicalDao" ref="PA_createMedicalDao" />
	</bean>

	<!-- 保单锁接口 start niuyu_wb 2014/12/03 -->
	<bean class="com.nci.tunan.pa.impl.policyLock.service.impl.PolicyLockServiceImpl" id="PA_policyLockService">
	</bean>
	<!-- 保单锁接口 end -->

	<!-- 手工核销 start niuyu_wb 2014/12/03 -->
	<bean class="com.nci.tunan.pa.impl.renewal.service.impl.RenewCollectBathJob" id="PA_renewCollectionService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractExtendLogDao" ref="PA_contractExtendLogDao"/>
		<property name="contractExtendCxDao" ref="PA_contractExtendCxDao"/>
<!--		<property name="ilpRegularPremDao" ref="ilpRegularPremDao" />-->
		<property name="ilpRegularPremCxDao" ref="PA_ilpRegularPremCxDao"/>
		<property name="ilpRegularPremLogDao" ref="PA_ilpRegularPremLogDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
<!-- 		<property name="prdIAS" ref="prdIAS" /> -->
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="continueBonusDService" ref="PA_continueBonusDService"/>
		<property name="renewRiskAmountService" ref="PA_renewRiskAmountService"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="noticeService" ref="PA_noticeService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="renewalprocessService" ref="PA_renewalprocessService"/>
		<property name="fundGroupTransDao" ref="PA_FundGroupTransDao"/>
		<property name="submitMessageBocicDao" ref="PA_submitMessageBocicDao"></property>
		<property name="taxPremiumPolicyTaskDao" ref="PA_taxPremiumPolicyTaskDao"></property>
		<property name="contractAgentDao" ref="PA_contractAgentDao"></property>
	</bean>
	<!-- 手工核销 end -->

	<!-- 续期冲正 start niuyu_wb 2014/12/09 -->
	<bean class="com.nci.tunan.pa.impl.renewal.service.impl.RenewCollectionOffsetServiceImpl" id="PA_renewCollectionOffsetService">
		<property name="capIAS" ref="PA_capIAdapter"/>
		<property name="policyLockService" ref="PA_policyLockService"/>
		<property name="renewCollectionOffsetCompDao" ref="PA_renewCollectionOffsetCompDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
	</bean>
	<!-- 续期冲正 end -->

	<!-- add by liangpl_wb start -->
	<bean class="com.nci.tunan.pa.impl.querypolicylist.dao.impl.QueryPolicyListDaoImpl" id="PA_queryPolicyListDao" parent="baseDao"/>
	<bean class="com.nci.tunan.pa.impl.querypolicylist.service.impl.QueryPolicyListServiceImpl" id="PA_queryPolicyListService">
		<property name="queryPolicyListDao" ref="PA_queryPolicyListDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.querypolicyinfo.service.impl.QueryPolicyInfoServiceImpl" id="PA_queryPolicyInfoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>

	<!-- add by liangpl_wb end -->

	<!-- guyy_wb 风险保额 start -->
	<bean class="com.nci.tunan.pa.impl.risk.service.impl.RiskAmountServiceImpl" id="PA_riskAmountService">
		<property name="aggregationRiskTypeDao" ref="PA_aggregationRiskTypeDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="riskAmountArchiveDao" ref="PA_riskAmountArchiveDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="nbIAS" ref="PA_nbIAS"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="prdService" ref="PA_prdIAS"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="contractLiabAmoutDao" ref="PA_contractLiabAmoutDao"/>
		<property name="countFeeService" ref="PA_countFeeService"/>
	</bean>
	<!-- guyy_wb 风险保额  end -->
	<!-- guyy_wb 秒杀回执信息 start -->
	<bean class="com.nci.tunan.pa.impl.seckillpolicydatesync.service.impl.SecKillAcknowledgeSyncServiceImpl" id="PA_secKillAcknowledgeSyncService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
	</bean>
<!-- guyy_wb 秒杀回执信息 end -->
	<!-- 保单回执处理 -->
	<bean class="com.nci.tunan.pa.impl.policyacknowledgementupdate.service.impl.PolicyAcknowledgementUpdateServiceImpl" id="PA_policyAcknowledgementUpdateService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="calPremUtils" ref="PA_calPremUtils"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.util.CalPremUtils" id="PA_calPremUtils">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	
	<!-- 查询保单是否有过保全理赔 -->
	<bean class="com.nci.tunan.pa.impl.queryexistcsorcl.service.impl.QueryExistCSorCLServiceImpl" id="PA_queryExistCSorCLService">
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="serviceDao" ref="PA_serviceDao"/>
	</bean>

	<!-- 客户告知 start niuyu_wb 2015/01/06 -->
	<bean class="com.nci.tunan.pa.impl.customerSurvey.service.impl.CustomerSurveyQueryServiceImpl" id="PA_customerSurveyQueryService">
		<property name="customerSurveyDao" ref="PA_customerSurveyDao"/>
		<property name="surveyTemplateDao" ref="PA_surveyTemplateDao"/>
		<property name="customerSurveyDetailDao" ref="PA_customerSurveyDetailDao"/>
		<property name="surveyQuestionOptionDao" ref="PA_surveyQuestionOptionDao"/>
		<property name="surveyQuestionDao" ref="PA_surveyQuestionDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>
	<!-- 客户告知 end -->
	<!-- 客户保单查询 start niuyu_wb 2015/01/16 -->
	<bean class="com.nci.tunan.pa.impl.customerPolicy.service.impl.CustomerPolicyQueryServiceImpl" id="PA_customerPolicyQueryService">
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="secondPolicyHolderDao" ref="PA_secondPolicyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>		
	</bean>
	<!-- 客户保单查询 end -->

	<!-- 综合查询 start niuyu_wb 2015-01-23 -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.CommonQueryServiceImpl" id="PA_commonQueryService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="commonQueryDao" ref="PA_commonQueryDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="policyOperationDao" ref="PA_policyOperationDao"/>
		<property name="policyStatusDao" ref="PA_policyStatusDao"/>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
	</bean>
	<!-- 综合查询 end -->

	<!--客户详细信息查询 start -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.CustomerDetailInfoServiceImpl" id="PA_customerDetailInfoService">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="customerDetailInfoDao" ref="PA_customerDetailInfoDao"/>
		<property name="bankDao" ref="PA_bankDao"/>
	</bean>
	<!--客户详细信息查询 end -->
	<!-- 责任组详细信息查询 start -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.BusiItemDetailInfoServiceImpl" id="PA_busiItemDetailInfoService">
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
	</bean>
	<!-- 责任组详细信息查询 end -->
	<!-- 外部系统信息（HOST、URI、IP）查询 start -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.ExternalSystemInfoServiceImpl" id="PA_externalSystemInfoService">
		<property name="externalSystemInfoDao" ref="PA_externalSystemInfoDao"/>
	</bean>
	<!-- 外部系统信息（HOST、URI、IP）查询 end -->
	
	<!-- 客户查询service接口，区别于客户公共 -->
	<bean class="com.nci.tunan.pa.impl.customer.service.impl.CustomerServiceImpl" id="PA_pas_customerService">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
	</bean>
	
	<!-- add by liangpl  2015.6.20 start -->
	<bean class="com.nci.tunan.pa.impl.csrolepolicyinfolist.service.CSRolePolicyInfoListServiceImpl" id="PA_csRolePolicyInfoListService">
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>	
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
	</bean>
	
	<!-- add by liangpl  2015.6.20 end -->
<!-- 	guyy_wb 省市县 -->
	<bean class="com.nci.tunan.cs.impl.commonTool.service.impl.DistrictServiceImpl" id="PA_districtService">
		<property name="districtDao" ref="PA_districtDao"/>
	</bean>
	<!-- add by fengwz  2015.7.30 start -->
	<bean class="com.nci.tunan.pa.impl.renewcollection.service.impl.RenewCollectionFeeServiceImpl" id="PA_renewCollectionFeeService">
		<property name="premArapDao" ref="PA_premArapDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.renewcollection.service.impl.RenewCollectionOffsetServiceImpl" id="PA_renewcollectionoffsetService">
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="capService" ref="PA_capIAS"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.renewcollection.service.impl.RenewCollectionUndoServiceImpl" id="PA_renewCollectionUndoService">
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="renewReversalApplyDao" ref="PA_renewReversalApplyDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="capIAS" ref="PA_capIAS"/>
		<property name="premDao" ref="PA_premDao"/> 
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="renewCollectionUndoDao" ref="PA_renewCollectionUndoDao"/>
		<property name="paAcceptChangeDao" ref="PA_paAcceptChangeDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.queryinjured.service.impl.QueryInjuredServiceImpl" id="PA_queryInjuredService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="queryInjuredDao" ref="PA_queryInjuredDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	<!-- 查单接口 -->
	<bean class="com.nci.tunan.pa.impl.querypolicy.service.impl.QueryPolicyServiceImpl" id="PA_queryPolicyService">
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="allocationDao" ref="PA_allocationDao"/>
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="premDao" ref="PA_premDao"/>
 		<property name="csContractProductDao" ref="PA_csContractProductDao"/>
 		<property name="csPolicyHolderDao" ref="PA_csPolicyHolderDao"/>
 		<property name="csContractProductOtherDao" ref="PA_csContractProductOtherDao"/>
 		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="lppremDao" ref="PA_lppremDao"></property>
		<property name="lcpremDao" ref="PA_lcpremDao"></property>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		
	</bean>
	<!--查询保单历史信息 -->
	<bean class="com.nci.tunan.pa.impl.queryfeehistory.service.impl.QueryFeeHistoryServiceImpl" id="PA_queryFeeHistoryService">
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
	</bean>
	<!-- 保单角色信息查询 -->
	<bean class="com.nci.tunan.pa.impl.queryroleinfo.service.impl.QueryRoleInfoServiceImpl" id="PA_queryRoleInfoService">
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
	</bean>
	
	<!-- 既往保单查询 add by liangpl -->
	<bean class="com.nci.tunan.pa.impl.queryunderwrite.service.impl.QueryUnderwriteServiceImpl" id="PA_queryUnderwriteService">
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
	</bean>
	

	<!-- 账户资产录入start -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.service.impl.EditInvestAccountInfoServiceImpl" id="PA_investAccountInfoService">
		<property name="investAccountInfoDao" ref="PA_investAccountInfoDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.service.impl.EditFundAssetsServiceImpl" id="PA_fundAssetsService">
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="fundAssetsDao" ref="PA_fundAssetsDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
	</bean>
	<!-- 账户资产录入 end-->
    <!-- 单位价格初审service start add by xuyp and gums-->
	<bean class="com.nci.tunan.pa.impl.icheckFundprice.service.impl.CheckFundPriceServiceImpl" id="PA_iCheckFundPriceService">
	    <property name="investAccountInfoDao" ref="PA_investAccountInfoDao"/>
	    <property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	<!-- 单位价格初审service end -->
	<!-- 单位价格复核 -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.service.impl.ReviewFundPriceServiceImpl" id="PA_reviewFundPriceService">
		<property name="investAccountInfoDao" ref="PA_investAccountInfoDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	
	<!-- 更新保单号 -->
	<bean class="com.nci.tunan.pa.impl.updatepolicycode.service.impl.UpdatePolicyCodeServiceImpl" id="PA_updatePolicyCodeService">
		<property name="updatePolicyCodeDao" ref="PA_updatePolicyCodeDao"/>
	</bean>
	<!-- 更新保单信息 -->
	<bean class="com.nci.tunan.pa.impl.updatepolicyinfo.service.impl.UpdatePolicyInfoServiceImpl" id="PA_updatePolicyInfoService">
		<property name="updatePolicyInfoDao" ref="PA_updatePolicyInfoDao"/>
	</bean>
	
	<!-- 查询投资单位价格信息 -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.service.impl.InvestUnitPriceServiceImpl" id="PA_investUnitPriceService">
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>	
	
	<!-- 设置邮件 -->
	<bean class="com.nci.tunan.pa.impl.investUnitPriceManage.service.impl.FundPriceNoticeMailServiceImpl" id="PA_fundPriceNoticeMailService">
		<property name="fundPriceNoticeMailDao" ref="PA_fundPriceNoticeMailDao"/>
	</bean>	
	
	
	<bean class="com.nci.tunan.pa.impl.cssquerypolicy.service.impl.QueryPolicyServiceImpl" id="PA_cssQueryPolicyService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
	</bean>	
	

	<!-- 收付费接口 -->
	<bean class="com.nci.tunan.pa.impl.capresult.service.impl.CAPResultServiceImpl" id="PA_CAPResultService">
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
	</bean>	
	
	<!-- 满期给付查询接口 -->
	<bean class="com.nci.tunan.pa.impl.maturitybenefit.service.impl.MaturityBenefitServiceImpl" id="PA_maturityBenefitService">
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="payDueDao" ref="PA_payDueDao"/>
	</bean>
	

	<!-- 银保通出单情况查询 -->
	<bean class="com.nci.tunan.pa.impl.bankpolicy.service.impl.BankPolicyServiceImpl" id="PA_bankPolicyService">
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	
	<!-- 保单险种账户信息查询接口 service-->
	<bean class="com.nci.tunan.pa.impl.queryinsurancepolicy.service.impl.QueryInsurancePolicyServiceImpl" id="PA_insurancePolicyService">
	   <property name="policyAccountDao" ref="PA_policyAccountDao"/>
	   <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>

	<!-- 校验欠缴保费接口 service-->
	<bean class="com.nci.tunan.pa.impl.checksignedprem.service.impl.CheckSignedPremServiceImpl" id="PA_checkSignedPremService">
	   <property name="premArapDao" ref="PA_premArapDao"/>
	</bean>
	
	<!-- 查询已领生存金年金接口 -->
	<bean class="com.nci.tunan.pa.impl.drawdetail.service.impl.DrawDetailServiceImpl" id="PA_drawDetailService">
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
	</bean>
	<!-- 核保信息回传接口service  -->
	<bean class="com.nci.tunan.pa.impl.underwriting.service.impl.UnderWritingServiceImpl" id="PA_underWritingService">
	   <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	   <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	   <property name="contractProductDao" ref="PA_contractProductDao"/>
	   <property name="iPolicyLogService" ref="PA_policyLogService"/>
	   <property name="extraPremDao" ref="PA_extraPremDao"/>
	   <property name="uwRenewalInfoDao" ref="PA_uwRenewalInfoDao"/>
	   <property name="renewalLimitDao" ref="PA_renewalLimitDao"/>
	</bean>
	<!-- 查询未领生存金年金接口 -->
	<bean class="com.nci.tunan.pa.impl.notreceivedrawdetail.service.impl.NotReceiveDrawDetailServiceImpl" id="PA_notReceiveDrawDetailService">
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>

	
	<!-- 理赔结案接口 service-->
	<bean class="com.nci.tunan.pa.impl.claimsettlement.service.impl.ClaimSettlementServiceImpl" id="PA_claimSettlementService">
	   <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/> 
	   <property name="insuredListDao" ref="PA_insuredListDao"/>
	   <property name="contractProductDao" ref="PA_contractProductDao"/>
	   <property name="customerDao" ref="PA_customerDao"/>
	   <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	   <property name="payPlanDao" ref="PA_payPlanDao"/>
	   <property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
	   <property name="contractBeneDao" ref="PA_contractBeneDao"/>
	   <property name="addressDao" ref="PA_addressDao"/>
	   <property name="policyLogService" ref="PA_policyLogService"/>
	   <property name="riskAmountDao" ref="PA_riskAmountDao"/>
	   <property name="bankAccountDao" ref="PA_bankAccountDao"/>
	   <property name="policyAccountDao" ref="PA_policyAccountDao"/>
	   <property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
	   <property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
	   <property name="premArapDao" ref="PA_premArapDao"/>
	   <property name="capServiceSaveArap" ref="PA_capIAS"/>
	   <property name="prdService" ref="PA_prdIAS"/>
	   <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	   <property name="bussinessProDao" ref="PA_businessProductDao"/>
	   <property name="payerAccountDao" ref="PA_payerAccountDao"/>
	   <property name="aboutFeeService" ref="PA_aboutFeeService"/>
	   <property name="fundTransDao" ref="PA_fundTransDao"/>
	   <property name="withdrawTypeService" ref="WithdrawTypeService"/>
	   <property name="premDao" ref="PA_premDao"/>
	   <property name="extraPremDao" ref="PA_extraPremDao"/>
	   <property name="payDueDao" ref="PA_payDueDao"/>
	   <property name="contractInvestDao" ref="PA_contractInvestDao"/>
	   <property name="contractExtendDao" ref="PA_contractExtendDao"/>
	   <property name="renewSwitchService" ref="PA_renewalSwitchService"/>
	   <property name="normalLapseDao" ref="PA_normalLapseDao"/>
	   <property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	   <property name="settleReportDao" ref="PA_settleReportDao"/>
	   <property name="policyStatusTraceDao" ref="PA_policyStatusTraceDao"/>
	   <property name="saChangeDao" ref="PA_saChangeDao"/>
	   <property name="sendMailService" ref="PA_sendMailServiceImpl"/>
	</bean>
	
	<!-- 理赔接口 service  上次分红日/出险日是否在首次年金领取日之前 add by yangyl-->
	<bean class="com.nci.tunan.pa.impl.claimquerypolicyinfo.service.impl.ClaimQueryPolicyInfoServiceImpl" id="PA_claimQueryPolicyInfoService">
	   <property name="allocationDao" ref="PA_allocationDao"/>
	   <property name="payPlanDao" ref="PA_payPlanDao"/>
	   <property name="contractInvestDao" ref="PA_contractInvestDao"/>
	   <property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	   <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	   <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	   <property name="calculateEndInterestService" ref="PA_calculateEndInterestService"/>
	   <property name="contractProductDao" ref="PA_contractProductDao"/>
	   <property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
	   <property name="premArapDao" ref="PA_premArapDao"/>
	   <property name="businessProductDao" ref="PA_businessProductDao"/>
	   <property name="advanceDao" ref="PA_advanceDao"/>
	   <property name="policyChangeDao" ref="PA_policyChangeDao"/>
	   <property name="bounsAllocationService" ref="PA_allocationService"/>
	   <property name="cashBonusAllocationService" ref="PA_cashallocationService"></property>
	   <property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
	   <property name="contractInvestLogDao" ref="PA_contractInvestLogDao"/>
	   <property name="fundTransDao" ref="PA_fundTransDao"/>
	   <property name="capIAS" ref="PA_capIAS"/>
	   <property name="premDao" ref="PA_premDao"/>
	   <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	   <property name="fundGroupTransDao" ref="PA_FundGroupTransDao"/>
	</bean>
	<!-- 理赔结案—保单管理费及风险保费欠缴金额计算 -->
	<bean class="com.nci.tunan.pa.impl.aboutfee.service.impl.AboutFeeServiceImpl" id="PA_aboutFeeService">
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
		<property name="calculateIlpuDueFee" ref="PA_calculateIlpuDueFee"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
	</bean>
	<!-- 理赔结案-应领未领的保证领取查询 -->
	<bean class="com.nci.tunan.pa.impl.ensureannual.service.impl.EnsureAnnualServiceImpl" id="PA_ensureAnnualService">
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
	</bean>

	<!-- 理赔结案接口—投连万能账户价值查询 -->
	<bean class="com.nci.tunan.pa.impl.investvalue.service.impl.InvestValueServiceImpl" id="PA_investValueService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="pasIAS" ref="PA_pasIAS"/>
		<property name="formulaDao" ref="PA_formulaDao"/>
		<property name="universalSettlementDao" ref="PA_universalSettlementDao"/>
		<property name="calculateIlpuDueFee" ref="PA_calculateIlpuDueFee"/>
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="calculateEndInterestService" ref="PA_calculateEndInterestService"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
	</bean>
	<!-- 理赔结案接口—自垫/贷款本息计算 -->
	<bean class="com.nci.tunan.pa.impl.loanselfpay.service.impl.LoanSelfpayServiceImpl" id="PA_loanSelfpayService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="cs_loanBusiCfgDao" ref="PA_cs_loanBusiCfgDao"/>
		<property name="cusApplicationService" ref="PA_cusApplicationService"/>
		<property name="loanFactorPeriodCodeDao" ref="PA_loanFactorPeriodCodeDao"/>
		<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
	</bean>
	<!-- 理赔结案接口—出险日之前的欠缴保费查询 -->
	<bean class="com.nci.tunan.pa.impl.debtprem.service.impl.DebtPremServiceImpl" id="PA_debtPremService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="renewalBillingDao" ref="PA_renewalBillingDao"/>
		<property name="autoextraServiece" ref="PA_autoextraServiece"/>
		<property name="premDao" ref="PA_premDao"/>
	</bean>
	<!-- 理赔结案接口—出险日之后多交的保费查询 -->
	<bean class="com.nci.tunan.pa.impl.overprem.service.impl.OverPremServiceImpl" id="PA_overPremService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
	</bean>

	<!-- 出险日之后已领的生存金/年金查询/满期金 -->
	<bean class="com.nci.tunan.pa.impl.backprem.service.impl.BackPremServiceImpl" id="PA_backPremService">
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="premArapDao" ref="PA_premArapDao"></property>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.impl.rulevalidation.service.impl.RuleValidationServiceImpl" id="PA_releValidationService">
		<property name="liabSurveryRuleDao" ref="PA_liabSurveryRuleDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="calcCashValueService" ref="PA_calcCashValueService"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="calculateSAMService" ref="PA_calculateSAMService"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="clmService" ref="PA_clmService"/>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
		<property name="payPlanPayeeDao" ref="PA_payPlanPayeeDao"/>
		<property name="bankAccountDao" ref="PA_bankAccountDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.contractproduct.service.impl.ContractProductServiceImpl" id="PA_contractProductService">
			<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	<!-- 理赔结案接口-利差账户本息计算接口 -->
	<bean class="com.nci.tunan.pa.impl.accountvalue.service.impl.AccountValueServiceImpl" id="PA_accountValueService">
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyLogService" ref="PA_policyLogService"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.querybriefinfo.service.impl.QueryBriefInfoServiceImpl" id="PA_queryBriefInfoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	</bean>
	<!-- 客户保单简要查询 -->
	<bean class="com.nci.tunan.pa.impl.customerpolicygeneral.service.impl.CustomerPolicyGeneralServiceImpl" id="PA_customerPolicyGeneralService">
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="customerPolicyGeneralDao" ref="PA_customerPolicyGeneralDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="agentDao" ref="commonAgentDao"/>
	</bean>
	<!-- 保单状态更新接口 -->
	<bean class="com.nci.tunan.pa.impl.updatepolicystate.service.impl.UpdatePolicyStateServiceImpl" id="PA_updatePolicyStateService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="renewChangeDao" ref="PA_renewChangeDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>		
	</bean>
	<!-- 险种终止接口 -->
	<bean class="com.nci.tunan.pa.impl.busiprodstop.service.impl.BusiProdStopServiceImpl" id="PA_busiProdStopService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	<!-- 查询保单服务信息接口 -->
	<bean class="com.nci.tunan.pa.impl.querypolicyserviceinfo.service.impl.QueryPolicyServiceInfoServiceImpl" id="PA_queryPolicyServiceInfoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
	</bean>

	<!-- 保单状态同步 -->
	<bean class="com.nci.tunan.pa.impl.policystatussynchro.service.impl.PolicyStatusSynchroServiceImpl" id="PA_policyStatusSynchroService">
		
	</bean>
	<!-- 豁免状态更新 -->
	<bean class="com.nci.tunan.pa.impl.updatewaiver.service.impl.UpdateWaiverServiceImpl" id="PA_updateWaiverService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<!-- 应收应付接口  zhangjy_wb-->
	<bean class="com.nci.tunan.pa.impl.querypremarap.service.impl.QueryPremArapServiceImpl" id="PA_queryPremArapService">
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>
	</bean>
	<!-- 新契约查询保单历史接口 -->
	<bean class="com.nci.tunan.pa.impl.nbquerypolicy.service.impl.NBQueryPolicyServiceImpl" id="PA_nbQueryPolicyService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="calPremUtils" ref="PA_calPremUtils"/>
	</bean>
	<!-- 新契约查询投被受历史保单接口 -->
	<bean class="com.nci.tunan.pa.impl.nbqueryhistorypolicy.service.impl.NBQueryHistoryPolicyServiceImpl" id="PA_nbQueryHistoryPolicyService">
	</bean>
	<!-- 查询生存给付应领记录 -->
	<bean class="com.nci.tunan.pa.impl.querypaydue.service.impl.QueryPayDueServiceImpl" id="PA_queryPayDueSerivce">
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
	</bean>

	<bean class="com.nci.tunan.pa.impl.getinvest.service.impl.GetInvestServiceImpl" id="PA_getInvestService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="fundTransDao" ref="PA_fundTransDao"/>
	</bean>

	<!-- 调用理赔接口service -->
	<bean class=" com.nci.tunan.pa.imports.impl.CLMServiceImpl" id="PA_pa_clmService">
	</bean>
	
	<!-- 查询保单被保人接口Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000014.impl.QueryPolicyInsuredServiceImpl" id="PA_queryPolicyInsuredService">
		<property name="queryPolicyInsuredDao" ref="PA_queryPolicyInsuredDao"/>
		<property name="insuredListLogDao" ref="PA_insuredListLogDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
	</bean>
	
	<!-- 个人客户产品信息查询Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000040.impl.QueryPolicyProductServiceImpl" id="PA_queryPolicyProductService">
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	
	<!-- 网点新增Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000225.impl.AddBankBranchServiceImpl" id="PA_addBankBranchService">
		<property name="bankBranchDao" ref="commonBankBranchDao"/>
	</bean>
	
	<!-- 人员修改Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00102000602.impl.AgentOperationServiceImpl" id="PA_agentOperationService">
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="agentLicenseDao" ref="commonAgentLicenseDao"/>
	</bean>
	
	<!-- 保单密码查询Service -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101000548.impl.QueryPolicyPasswordServiceImpl" id="PA_queryPolicyPasswordService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>


		<!-- 保额保费等额查询接口 service-->
	<bean class="com.nci.tunan.pa.impl.countfee.service.impl.CountFeeServiceImpl" id="PA_countFeeService">
	  <property name="countFeeDao" ref="PA_countFeeDao"/>
	  <property name="customerDao" ref="PA_customerDao"/>
	  <property name="nbIAS" ref="PA_nbIAS"/>
	  <property name="contractProductDao" ref="PA_contractProductDao"/>
	  <property name="csContractProductDao" ref="PA_csContractProductDao"/>
	  <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	<!-- 查询客户是否存在生效且过犹豫期保单 -->
	<bean class="com.nci.tunan.pa.impl.IsHesitationPeriod.service.impl.IsHesitationPeriodServiceImpl" id="PA_isHesitationPeriodService">
	  <property name="isHesitationPeriodDao" ref="PA_isHesitationPeriodDao"/>
	</bean>
	<!-- 修改关联保单 -->
	<bean class="com.nci.tunan.pa.impl.updaterelationpolicycode.service.impl.updateRelationPolicyCodeserviceimpl" id="PA_updateRelationPolicyCodeservice">
	  <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	</bean>
    <!-- add by sunjl 上载保单列表 -->
    <bean class="com.nci.tunan.pa.impl.survey.service.impl.UploadPolicySurveyServiceImpl" id="PA_uploadPolicySurveyService">
        <property name="surveyApplyDao" ref="PA_csSurveyApplyDao"/>
        <property name="queryPolicyUploadHistoryDao" ref="PA_queryPolicyUploadHistoryDao"/>
        <property name="contractMasterDao" ref="PA_contractMasterDao"/>
        <property name="policyHolderDao" ref="PA_policyHolderDao"/>
        <property name="customerDao" ref="PA_customerDao"/>
    	<property name="insuredListDao" ref="PA_insuredListDao"/>
        <property name="riskAmountDao" ref="PA_riskAmountDao"/>
        <property name="claimSurveyBatchDao" ref="PA_claimSurveyBatchDao"/>
        <property name="claimSurveyTaskDao" ref="PA_claimSurveyTaskDao"/>
        <property name="csSurveyObjectDao" ref="PA_csSurveyObjectDao"/>
	    <property name="csSurveyItemDao" ref="PA_csSurveyItemDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	    <property name="contractProductDao" ref="PA_contractProductDao"/>
	    <property name="riskLevelConfigDao" ref="PA_riskLevelConfigDao"/>
	    <property name="beforeSurveyManageUCCimpl" ref="CLM_iBeforeSurveyManageUCC"/>
    </bean>
    <!-- add by zhangjy 维护前置调查计划 -->
    <bean class="com.nci.tunan.pa.impl.survey.service.impl.ClaimBfSurveyPlanServiceImpl" id="PA_claimBfSurveyPlanService">
        <property name="claimBfSurveyPlanDao" ref="PA_claimBfSurveyPlanDao"/>
       <property name="aggregationRiskTypeDao" ref="PA_aggregationRiskTypeDao"/>
        <property name="salesChannelDao" ref="PA_salesChannelDao"/>
        <property name="yesNoDao" ref="PA_yesNoDao"/>
        <!-- <property name="claimCaseDao" ref="claimCaseDao"></property>
        <property name="insuredListDao" ref="insuredListDao"></property>
        <property name="claimCommonQueryService" ref="claimCommonQueryService"></property>-->
        <property name="claimBfSurveyChannelDao" ref="PA_claimBfSurveyChannelDao"/> 
        <property name="claimBfSurveyOrgDao" ref="PA_claimBfSurveyOrgDao"/>
       	<property name="claimSurveyTaskDao" ref="PA_claimSurveyTaskDao"/>
	    <property name="claimSurveyBatchDao" ref="PA_claimSurveyBatchDao"/>
	    <property name="surveyApplyDao" ref="PA_claimSurveyApplyDao"/>
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	    <property name="claimBfSurveyRamntDao" ref="PA_claimBfSurveyRamntDao"/>
	    <property name="insuredListDao" ref="PA_insuredListDao"/>
	    <property name="riskAmountDao" ref="PA_riskAmountDao"/>
	    <property name="customerDao" ref="PA_customerDao"/>
	    <property name="csSurveyObjectDao" ref="PA_csSurveyObjectDao"/>
	    <property name="csSurveyItemDao" ref="PA_csSurveyItemDao"/>
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	    <property name="contractProductDao" ref="PA_contractProductDao"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.survey.service.impl.ClaimBfSurveyChannelServiceImpl" id="PA_claimBfSurveyChannelService">
        <property name="claimBfSurveyChannelDao" ref="PA_claimBfSurveyChannelDao"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.survey.service.impl.ClaimBfSurveyOrgServiceImpl" id="PA_claimBfSurveyOrgService">
        <property name="claimBfSurveyOrgDao" ref="PA_claimBfSurveyOrgDao"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.survey.service.impl.ClaimBfSurveyRamntServiceImpl" id="PA_claimBfSurveyRamntService">
        <property name="claimBfSurveyRamntDao" ref="PA_claimBfSurveyRamntDao"/>
    </bean>

    <!-- 日终汇总对账 -->
    <bean class="com.nci.tunan.pa.impl.dayendreconciliation.service.impl.DayEndReconciliationServiceImpl" id="PA_dayEndReconciliationService">
        <property name="dayEndReconciliationDao" ref="PA_dayEndReconciliationDao"/>
    </bean>

    <!--心跳交易-->
	 <bean class="com.nci.tunan.pa.impl.hearttransaction.service.impl.HeartTransactionServiceImpl" id="PA_heartTransactionService">	
	</bean> 

    <!-- end -->
    
    <!--接入渠道-批量提数处理结果返盘-->
     <bean class="com.nci.tunan.pa.impl.batchnumresultsreturn.service.impl.BatchNumResultsReturnServiceImpl" id="PA_iBatchNumResultsReturnService">    
    </bean> 
    <!-- end -->
    
    <!--投连试算交易 add by liangpl -->
     <bean class="com.nci.tunan.pa.impl.sellinvest.service.impl.SellInvestServiceImpl" id="PA_sellInvestService">
     	  <property name="contractProductDao" ref="PA_contractProductDao"/>
     	  <property name="calculateCashValueService" ref="PA_calcCashValueService"/>
     	  <property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
     	  <property name="contractInvestDao" ref="PA_contractInvestDao"/>
     	  <property name="iPayPlanDao" ref="PA_payPlanDao"/>
     	  <property name="fundTransDao" ref="PA_fundTransDao"/>
    </bean>
    <!-- end -->
    
    <!--日常交易查询-->
    <bean class="com.nci.tunan.pa.impl.dailytradingquery.service.impl.DailyTradingQueryServiceImpl" id="PA_dailyTradingQueryService">
	<property name="dailyTradingQueryDao" ref="PA_dailyTradingQueryDao"/>
	</bean> 
	
    <!-- lvkai 保单账户校验  -->
	<bean class="com.nci.tunan.pa.impl.accountcheck.service.impl.AccountCheckServiceImpl" id="PA_accountCheckService">
		<property name="accountCheckDao" ref="PA_accountCheckDao"/>
	</bean> 
	
	<!-- lvkai 日终明细对账 -->
	<bean class="com.nci.tunan.pa.impl.daydetailcheck.service.impl.DayDetailCheckServiceImpl" id="PA_dayDetailCheckService">
		<property name="dayDetailCheckDao" ref="PA_dayDetailCheckDao"/>
	</bean> 
    <!-- 出险日后投连、万能险领取查询接口 service-->
	<bean class="com.nci.tunan.pa.impl.operatequery.service.impl.OpearateQueryServiceImpl" id="PA_operateQueryService">
	  <property name="operateQueryDao" ref="PA_operateQueryDao"/>
	</bean>
	
	<!--接入渠道保单查询 -->
	<bean class="com.nci.tunan.pa.impl.querypolicydetailinfo.service.impl.QueryPolicyDetailInfoServiceImpl" id="PA_queryPolicyDetailInfoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="questionaireCustomerDao" ref="PA_questionaireCustomerDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="orgDao" ref="commonOrgDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>
		<property name="payerDao" ref="PA_payerDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="extraPremDao" ref="PA_extraPremDao"/>
		<property name="calculateCashValueService" ref="PA_calcCashValueService" />
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
		<property name="surrenderDao" ref="PA_surrenderDao"/>
		<property name="costFeeCfgDao" ref = "PA_costFeeCfgDao"/>
		<property name="fundDao" ref="PA_fundDaoImpl"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="contractMasterLogDao" ref="PA_contractMasterLogDao" />
		<property name="contractInvestRateDao" ref="PA_contractInvestRateDao" />
	</bean>
	
	<!-- ESB服务 判断保单号是否存在 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.judgepolicyexist.impl.JudgePolicyExistServiceImpl" id="PA_judgePolicyExistService">
	    <property name="contractMasterDao" ref="PA_contractMasterDao"/>	
	</bean>
	
	<!--判断投保人和代理人是否是同一人  -->
	<bean class="com.nci.tunan.pa.impl.customerisagent.service.impl.CustomerIsAgentServiceImpl" id="PA_customerIsAgentService">
	     <property name="customerDao" ref="PA_customerDao"/>
	     <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	     <property name="agentDao" ref="PA_agentPADao"/>
	</bean>
	
	<!-- 投保人或被保人是否存在期满保单 -->
	<bean class="com.nci.tunan.pa.impl.policyexpiration.service.impl.PolicyExpirationServiceImpl" id="PA_policyExpirationService">
	     <property name="insuredListDao" ref="PA_insuredListDao"/>
	     <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	     <property name="policyExpirationDao" ref="PA_policyExpirationDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.policyrisk.service.impl.PolicyRiskSerivceImpl" id="PA_claPolicyRiskService">
    	<property name="insuredListDao" ref="PA_insuredListDao"/>
    	<property name="contractProductDao" ref="PA_contractProductDao"/>
    	<property name="riskAmountDao" ref="PA_riskAmountDao"/>
    	<property name="customerDao" ref="PA_customerDao"/>
    	<property name="contractExtendDao" ref="PA_contractExtendDao"/>
    	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
    	<property name="prdIAS" ref="PA_prdIAS"/>
    	<property name="payPlanDao" ref="PA_payPlanDao"/>
    	<property name="premArapDao" ref="PA_premArapDao"/>
    	<property name="riskAmountQueueDao" ref="PA_riskAmountQueueDao"/>
    	<property name="contractInvestDao" ref="PA_contractInvestDao"/>
    	<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
    	<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
    </bean>
    
    <bean class="com.nci.tunan.pa.impl.querymobile.service.impl.QueryMobileServiceImpl" id="PA_queryMobileService">
	     <property name="agentDao" ref="PA_agentPADao"/>
	</bean>
	
	<!-- 变更履历查询 -->
    <bean class="com.nci.tunan.pa.impl.transdata.service.impl.TransDataServiceImpl" id="PA_transDataService">
	     <property name="transDataDao" ref="PA_transDataDao"/>
	</bean>
	
	<!-- 查询责任组最新加费评点 -->
    <bean class="com.nci.tunan.pa.impl.extrapremem.service.impl.ExtraPremEmServiceImpl" id="PA_extraPremEmService">
	     <property name="extraPremEmDao" ref="PA_extraPremEmDao"/>
	</bean>
	
	<!-- 根据输入的移动电话及固定电话，查询出使用此了该号码的投保人集合 -->
    <bean class="com.nci.tunan.pa.impl.querycusfivebasicbytel.service.impl.QueryCusFiveBasicByTelServiceImpl" id="PA_queryCusFiveBasicByTelService">
	     <property name="queryCusFiveBasicByTelDao" ref="PA_queryCusFiveBasicByTelDao"/>
	</bean>
	

	<bean class="com.nci.tunan.pa.common.service.impl.CommonTool" id="PA_commonTool">
	     <property name="businessProductDao" ref="PA_businessProductDao"/>
	     <property name="pACommonSearchDao" ref="PA_pACommonSearchDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.wbnoticeprint.service.impl.WBNoticePrintServiceImpl" id="PA_wbNoticePrintService">
	     <property name="documentDao" ref="PA_paDocumentDao"/>
	     <property name="clobDao" ref="PA_paClobDao"/>
	     <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	     <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	     <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	     <property name="addressDao" ref="PA_addressDao"/>
	     <property name="customerDao" ref="PA_customerDao"/>
	     <property name="contractAgentDao" ref="PA_contractAgentDao"/>
	     <property name="contractInvestDao" ref="PA_contractInvestDao"/>
	     <property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
	     <property name="premArapDao" ref="PA_premArapDao"/>
	     <property name="policyAccountDao" ref="PA_policyAccountDao"/>
	     <property name="fundTransDao" ref="PA_fundTransDao"/>
	     <property name="persistenceBonusDao" ref="PA_persistenceBonusDao"/>
	     <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	     <property name="contractExtendDao" ref="PA_contractExtendDao"/>
	     <property name="contractProductDao" ref="PA_contractProductDao"/>
	     <property name="appendPremListDao" ref="PA_appendPremListDao"/>
	     <property name="calculateCashValueService" ref="PA_calcCashValueService"/>
	     <property name="csContractMasterDao" ref="PA_csContractMasterDao"/>
	     <property name="csContractBusiProdDao" ref="PA_csContractBusiProdDao"/>
	     <property name="insuredListDao" ref="PA_insuredListDao"/>
	     <property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
	     <property name="salesChannelDao" ref="PA_salesChannelDao"/>
	     <property name="districtDao" ref="PA_districtDao"/>
	     <property name="agentDao" ref="PA_agentPADao"/>
	     <property name="businessProductDao" ref="PA_businessProductDao"/>
	</bean>
	<!-- 可选责任信息查询列表 -->
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r00101001594.impl.OptionLiabInfoQueryServiceImpl" id="PA_optionLiabInfoQueryService">
	     <property name="contractProductDao" ref="PA_contractProductDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.common.service.impl.ChgIdRollBackServiceImpl" id="PA_chgIdRollBackService">
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="ilpuChargeDeductionDao" ref="PA_ilpuChargeDeductionDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="payDueListDao" ref="PA_payDueListDao"/>
		<property name="capServiceSaveArap" ref="PA_caparapuccsaveArap"/>
		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
		<property name="csPremArapDao" ref="PA_csPremArapDao"/>
		<property name="fundSettlementDao" ref="PA_fundSettlementDao"/>
		<property name="policyFundChargeDao" ref="PA_policyFundChargeDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.common.service.impl.ChgIdGetListServiceImpl" id="PA_chgIdGetListService">
		<property name="fundTransDao" ref="PA_fundTransDao"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
		<property name="policyChangeDao" ref="PA_policyChangeDao"/>
		<property name="policyAccountTransListDao" ref="PA_policyAccountTransListDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.common.service.impl.IChgIdBackInfoServiceImpl" id="PA_chgIdBackInfoService">
		<property name="chgIdGetListService" ref="PA_chgIdGetListService"/>
		<property name="chgIdRollBackService" ref="PA_chgIdRollBackService"/>
	</bean>
	<!-- 新增   当日撤单接口 -->
	<bean class="com.nci.tunan.pa.impl.dayTradeRevoke.service.impl.DayTradeRevokeServiceImpl" id="PA_dayTradeRevokeService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="riskAmountDao" ref="PA_riskAmountDao"/>
	</bean>
	<bean class="com.nci.tunan.pa.common.service.impl.CalculateEndInterestImpl" id="PA_calculateEndInterestService">
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="formulaService" ref="PA_formulaService"/>
		<property name="prdProductQueryService" ref="PA_prdService"/>
		
	</bean>
    <!-- box单位数接口 -->
    <bean class="com.nci.tunan.pa.impl.boxpa.service.impl.InvestUnitsDaysumServiceImpl" id="PA_investUnitsDaysumService">
        <property name="investUnitsDaysumDao" ref="PA_investUnitsDaysumDao"/>
    </bean>
    <bean class="com.nci.tunan.pa.impl.policyAccount.service.imp.PolicyAccountServiceImpl" id="PA_policyAccountService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="policyAccountDao"  ref="PA_policyAccountDao"/>
	</bean>
	
	<!-- 实时孤儿单分配结果通知接口 -->
     <bean class="com.nci.tunan.pa.impl.orphannoticepolicy.service.impl.OrphanPolicyNoticeServiceImpl" id="PA_orphanPolicyNoticeService">
   		<property name="orphanFtpServerConfig" ref="PA_orphanFtpServerConfig"/>
    </bean>
    
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="PA_orphanFtpServerConfig">
		<property name="ftpServerIp" value="${orphanFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${orphanFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${orphanFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${orphanFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${orphanFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpServerWorkingDirectory" value="${orphanFtpServerConfig.ftpServerWorkingDirectory}"/>
		<property name="ftpServerUploadDirectory" value="${orphanFtpServerConfig.ftpServerUploadDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${orphanFtpServerConfig.ftpServerBackupsDirectory}"/>
		<property name="ftpServerReadDirectory" value="${orphanFtpServerConfig.ftpServerReadDirectory}"/>
		<property name="ftpServerWarningDirectory" value="${orphanFtpServerConfig.ftpServerWarningDirectory}"/>
		<property name="ftpServerWarningBackDirectory" value="${orphanFtpServerConfig.ftpServerWarningBackDirectory}"/>
	</bean>
	<!-- 营销员放弃保单信息同步FTP配置 -->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="PA_marketersabandonftpServerConfig">
		<property name="ftpServerIp" value="${marketersabandonFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${marketersabandonFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${marketersabandonFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${marketersabandonFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${marketersabandonFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${marketersabandonFtpServerConfig.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${marketersabandonFtpServerConfig.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${marketersabandonFtpServerConfig.ftpServerBackupsDirectory}"/>
	</bean>
	<bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_ftpServerConfig">
		<property name="ftpServerIp" value="${accountValueftpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${accountValueftpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${accountValueftpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${accountValueftpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${accountValueftpServerConfig.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${accountValueftpServerConfig.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${accountValueftpServerConfig.ftpServerWorkingDirectory}"/>
	</bean>
	
		<bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_netftpServerConfig">
		<property name="ftpServerIp" value="${ftpServerConfigNetpinPrem.ftpServerIp}"/>
		<property name="ftpServerPort" value="${ftpServerConfigNetpinPrem.ftpServerPort}"/>
		<property name="ftpUserName" value="${ftpServerConfigNetpinPrem.ftpUserName}"/>
		<property name="ftpPassword" value="${ftpServerConfigNetpinPrem.ftpPassword}"/>
		<property name="ftpBufferSize" value="${ftpServerConfigNetpinPrem.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${ftpServerConfigNetpinPrem.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${ftpServerConfigNetpinPrem.ftpServerWorkingDirectory}"/>
	</bean>
    
        <!-- 查询保单下的责任组现金价值接口 -->
    <bean class="com.nci.tunan.pa.impl.querycashvaluelist.service.impl.QueryCashValueListServiceImpl" id="PA_queryCashValueListService">
         <property name="calculateCashValueService" ref="PA_calcCashValueService"/>
         <property name="contractProductDao" ref="PA_contractProductDao"/>
    </bean>

    <!-- 修改保单纠错数据service接口 -->
    <bean class="com.nci.tunan.pa.impl.nbdatadeal.service.impl.ErrorCorrectDealServiceImpl" id="PA_errorCorrectDealService">
    	<property name="errorFieldCfgDao" ref="PA_errorFieldCfgDao"/>
    	<property name="customerDao" ref="PA_customerDao"/>
    </bean>

        <!-- 查询是否做过续期 -->
     <bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.QueryWhetherRenewServiceImpl" id="PA_queryWhetherRenewService">
		<property name="premArapDao" ref="PA_premArapDao"/>	
    </bean>
    
    <!-- 可选责任分红计算接口 -->
    <bean class="com.nci.tunan.pa.impl.calc.service.impl.CalcOptBonusSAServiceImpl" id="PA_calcOptBonusSAService">
		<property name="saChangeDao" ref="PA_saChangeDao"/>
		<property name="specialBonusDao" ref="PA_specialBonusDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="allocationDao" ref="PA_allocationDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="payPlanDao" ref="PA_payPlanDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="queryCalcParam" ref="PA_queryCalcParam"/>
		<property name="bonusAllocateDao" ref="PA_bonusallocateDao"/>
    </bean>
     
     <!-- 实时孤儿单查询结果通知接口 -->
    <bean class="com.nci.tunan.pa.impl.orphanpolicy.service.impl.OrphanPolicyServiceImpl" id="PA_orphanPolicyService">
         <property name="orphanPolicyDao" ref="PA_orphanPolicyDao" />
    </bean>
    
	<bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_custRiskFtpServerConfig">
		<property name="ftpServerIp" value="${custRiskFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${custRiskFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${custRiskFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${custRiskFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${custRiskFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpServerWorkingDirectory" value="${custRiskFtpServerConfig.ftpServerWorkingDirectory}"/>
	</bean> 
	<!--  查询用户是否对该保单进行过复核操作service  -->
	             
	<bean class="com.nci.tunan.cs.impl.querypolicyreview.service.impl.QueryPolicyReviewServiceImpl" id="PA_queryPolicyReviewService">
		<property name="queryPolicyReviewDao" ref="PA_queryPolicyReviewDao"/>
	</bean>  
	
	<!-- 申请登记台账接口 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.RegistLedgerServiceImpl" id="PA_registLedgerService">
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="agentDao" ref="PA_agentPADao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
    </bean>

    
    <!-- 查询客户保单 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.CustomerPolicyServiceImpl" id="PA_customerPolicyService">
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="orgRelDao" ref="orgrelDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="bankDao" ref="PA_bankDao"/>
		<property name="policyAcknowledgementDao" ref="PA_policyAcknowledgementDao"/>
    	<property name="policyQueryDelayedDao" ref="PA_policyQueryDelayedDao" />
    	<property name="customerPolicyDao" ref="PA_customerPolicyDao" />
		<property name="bankBranchMappingDao" ref="PA_queryBankBranchMapping" />
    </bean>

    
    <!-- 农行网银渠道查询保单详情 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyDetailServiceImpl" id="PA_policyDetailService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="addressDao" ref="PA_addressDao"/>
		<property name="payDueDao" ref="PA_payDueDao"/>
		<property name="contractInvestDao" ref="PA_contractInvestDao"/>
		<property name="cSCalPolicyValueService" ref="PA_cSCalPolicyValueService"/>
		<property name="premDao" ref="PA_premDao"/>
		<property name="policyAccountStreamDao" ref="PA_policyAccountStreamDao"/>
		<property name="payerAccountDao" ref="PA_payerAccountDao"/>	
		<!-- 需求分析任务 #175126 各银行保单详情查询支持范围的调整（保单管理） start-->
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<!-- 需求分析任务 #175126 各银行保单详情查询支持范围的调整（保单管理） end-->
    </bean>
	 <!-- 物流丢失保单标记service -->
	 <bean class="com.nci.tunan.pa.impl.nbdatadeal.service.impl.PostLostDealServiceImpl" id="PA_postLostDealService">
	 	<property name="postLostDealDao" ref="PA_postLostDealDao"/>
	 </bean>
	 
	     <!-- 续期冲正 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.RenewalAmendPAServiceImpl" id="PA_renewalAmendPAService">
		<property name="capService" ref="PA_capIAS"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
    </bean>

    
    <!-- 校验投保人联系方式接口 -->
    <bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.CustomerInfoCheckServiceImpl" id="PA_customerInfoCheckService">
    	<property name="agentDao" ref="PA_agentPADao"/>
    	<property name="customerDao" ref="PA_customerDao"/>
    </bean>

        <!-- 中信银行数据回传service -->
	  <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.CiticBankDataServiceImpl" id="PA_citicBankDataService">
	 	<property name="csContractMasterDao" ref="PA_csContractMasterDao"/>
	 	<property name="csCustomerDao" ref="PA_csCustomerDao"/>
	 	<property name="csPayDueDao" ref="PA_csPayDueDao"/>
	 	<property name="csInsuredListDao" ref="PA_csInsuredListDao"/>
		<property name="csPolicyHolderDao" ref="PA_csPolicyHolderDao"/>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	 </bean>

	<!-- 农行保全状态同步 -->
	 <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyStatusSynForAbcServiceImpl" id="PA_policyStatusSynForAbcService">
	 	<property name="policyStatusSynForAbcDao" ref="PA_policyStatusSynForAbcDao"/>
	 </bean>	
	  <!-- 工商银行数据回传service -->
	  <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.ICBankDataServiceImpl" id="PA_iCBankDataService">
	 	<property name="csCustomerDao" ref="PA_csCustomerDao"/>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	 </bean>
	 <!-- 建行保单详情查询-->
	 <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyDetailForCCBServiceImpl" id="PA_policyDetailForCCBService">
	  <property name="policyDetailForCCBDao" ref="PA_policyDetailForCCBDao"/>	
	  <property name="policyHolderDao" ref="PA_policyHolderDao"/>
	  <property name="addressDao" ref="PA_addressDao"/>
	  <property name="insuredListDao" ref="PA_insuredListDao"/>
	  <property name="customerDao" ref="PA_customerDao"/>
	  <property name="contractBeneDao" ref="PA_contractBeneDao"/>
	  <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	  <property name="businessProductDao" ref="PA_businessProductDao"/>
	  <property name="contractProductDao" ref="PA_contractProductDao"/>
	  <property name="premDao" ref="PA_premDao"/>
	  <property name="payerAccountDao" ref="PA_payerAccountDao"/>	  
	 </bean>
	 <!-- 接入渠道犹豫期退保数据查询接口 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.QueryHesitationSurrenderServiceImpl" id="PA_queryHesitationSurrenderService">
    	<property name="queryHesitationSurrenderDao" ref="PA_queryHesitationSurrenderDao"/>
    </bean>
    <!-- 北京银行保单资产变动信息同步交易 -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyCapitalChgSynServiceImpl" id="PA_policyCapitalChgSynService">
	 	<property name="policyCapticalChgSynDao" ref="PA_policyCapticalChgSynDao"/>
	 </bean>
	 
	<!-- 保单续期缴费接口 -->
	<bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PaRenewalPaymentServiceImpl" id="PA_paRenewalPaymentService">
	  <property name="capService" ref="PA_capIAS"/>	
	  <property name="customerDao" ref="PA_customerDao"/>
	  <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	  <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	  <property name="payerAccountDao" ref="PA_payerAccountDao"/>
	  <property name="prdService" ref="PA_prdIAS"/>
	  <property name="contractProductDao" ref="PA_contractProductDao"/>
	  <property name="contractExtendDao" ref="PA_contractExtendDao"/>
	  <property name="premArapDao" ref="PA_premArapDao"/>
	 </bean> 
	 	 <!--建行查询保单历史变动信息  -->
	 <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.QueryPolicyHisForCCBServiceImpl" id="PA_queryPolicyHisForCCBService">
	 	<property name="queryPolicyHisForCCBDao" ref="PA_queryPolicyHisForCCBDao"/>
	    <property name="customerService" ref="PA_pas_customerService"/>
	 	<property name="csInformationQueryAllService" ref="PA_csInformationQueryAllService"/>
	 	<property name="calcCashValueService" ref="PA_calcCashValueService"/>
	 	<property name="clmService" ref="PA_pa_clmService" />
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	 </bean>
	<bean class="com.nci.tunan.pa.common.service.impl.ContractLiabAmoutServiceImpl" id="PA_contractLiabAmoutService">
		<property name="contractLiabAmoutDao" ref="PA_contractLiabAmoutDao"/>
	</bean>
	
	<!-- 保费同步接口 -->
    <bean class="com.nci.tunan.pa.impl.premsynchronize.service.impl.PremSynchronizeServiceImpl" id="PA_premSynchronizeService">
	 	<property name="premDao" ref="PA_premDao"/>
	 	<property name="contractProductDao" ref="PA_contractProductDao"/>
	 	<property name="contractMasterDao" ref="PA_contractMasterDao"/>
	 	<property name="policyHolderDao" ref="PA_policyHolderDao"/>
	 	<property name="contractAgentDao" ref="PA_contractAgentDao"/>
	 	<property name="insuredListDao" ref="PA_insuredListDao"/>
	 	<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	 	<property name="customerDao" ref="PA_customerDao"/>
	 	<property name="bankBranchDao" ref="commonBankBranchDao"/>
	 	<property name="agentDao" ref="PA_agentPADao"/>
	 	<!-- <property name="contractExtendDao" ref="PA_contractExtendDao"/> -->
	</bean>
	
	<!-- 保单业务人员查询 -->
	
	<bean class="com.nci.tunan.pa.impl.peripheral.service.r04301000286.impl.QueryAgentServiceImpl" id="PA_QueryAgentServiceImpl">
	   <property name="contractAgentDao" ref="PA_contractAgentDao"></property>
	</bean>
	
	<!-- ODS保单现价计算ftp配置 -->
	<bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_cashValueFtpServerConfig">
		<property name="ftpServerIp" value="${cashValueFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${cashValueFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${cashValueFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${cashValueFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${cashValueFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${cashValueFtpServerConfig.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${cashValueFtpServerConfig.ftpServerWorkingDirectory}"/>
	</bean>
	<!-- 影像ftp配置 -->
	<bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_policyReissueServerConfig">
		<property name="ftpServerIp" value="${policyReissueServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${policyReissueServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${policyReissueServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${policyReissueServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${policyReissueServerConfig.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${policyReissueServerConfig.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${policyReissueServerConfig.ftpServerWorkingDirectory}"/>
	</bean>
	
	<!-- 网销接口ftp配置 -->
	<bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_networkSaleQueryServerConfig">
		<property name="ftpServerIp" value="${networkSaleQueryServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${networkSaleQueryServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${networkSaleQueryServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${networkSaleQueryServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${networkSaleQueryServerConfig.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${networkSaleQueryServerConfig.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${networkSaleQueryServerConfig.ftpServerWorkingDirectory}"/>
	</bean>
	
	<!-- 延长宽限期天数查询接口 -->
	<bean class="com.nci.tunan.pa.impl.queryExtendGracePeriodDays.service.impl.QueryExtendGracePeriodDaysServiceImpl" id="PA_queryExtendGracePeriodDaysService">
		<property name="extendGracePeriodDao" ref="PA_extendGracePeriodDao"/>
	</bean>
	
	<!-- 查询保单金额接口 -->
    <bean class="com.nci.tunan.pa.impl.queryPolicyAmount.service.impl.QueryPolicyAmountServiceImpl" id="PA_queryPolicyAmountService">
    	<property name="contractMasterDao" ref="PA_contractMasterDao"/>
        <property name="payDueDao" ref="PA_payDueDao"/>
    	<property name="contractProductDao" ref="PA_contractProductDao"/>
    	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
    	<property name="calculateCashValueService" ref="PA_calcCashValueService"/>
    </bean>
    
    <!-- 更新保单标识 -->
    <bean class="com.nci.tunan.pa.impl.updatepolicyflag.service.impl.UpdatePolicyFlagServiceImpl" id="PA_updatePolicyFlagService">
         <property name="contractMasterDao" ref="PA_contractMasterDao"/>
    </bean>
    
    
    <!-- 开门红撤单接口 service-->
	<bean class="com.nci.tunan.pa.impl.aGoodStart.service.impl.AGoodStartServiceImpl" id="PA_aGoodStartService">
	  <property name="contractProductDao" ref="PA_contractProductDao"/>
	  <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	  <property name="contractMasterDao" ref="PA_contractMasterDao"/>
	  <property name="riskAmountDao" ref="PA_riskAmountDao"/>
	</bean>

    <!-- 非银保通查询 service -->
    <bean class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyQueryNonYbtServiceImpl" id="PA_policyQueryNonYbtService">
    	<property name="policyQueryNonYbtDao" ref="PA_policyQueryNonYbtDao" />
		<property name="policyHolderDao" ref="PA_policyHolderDao" />
		<property name="addressDao" ref="PA_addressDao" />
		<property name="insuredListDao" ref="PA_insuredListDao" />
		<property name="customerDao" ref="PA_customerDao" />
		<property name="contractBeneDao" ref="PA_contractBeneDao" />
    </bean>
    
	 <!-- 新增保单险种责任组变更表service -->
    <bean class="com.nci.tunan.pa.impl.ProductLiabilityChange.service.impl.ProductLiabilityChangeServiceimpl" id="PA_productLiabilityChangeService">
		<property name="productLiabilityChangeDao" ref="PA_productLiabilityChangeDao"/>
		
	</bean>
	
	<!-- 查询投保人银行账号的其他使用投保人 add by hourui -->
	<bean class="com.nci.tunan.pa.impl.querybankaccounts.service.impl.QueryBankAccountsServiceImpl" id="PA_queryBankAccountsService">
		<property name="customerDao" ref="PA_customerDao"></property>
	</bean>
	
		<!-- 合并客户ID service -->
	<bean class="com.nci.tunan.pa.impl.isupdatecustomer.service.impl.IsUpdateCustomerServiceImpl" id="PA_isUpdateCustomerService">
		<property name="isUpdateCustomerDao" ref = "PA_isUpdateCustomerDao"></property>
	</bean>
	<!-- 投被保人关系查询接口 -->
	<bean class="com.nci.tunan.cs.impl.peripheral.service.r06401003110.impl.HolderRelationInsuredServiceImpl" id="PA_holderRelationInsuredService">
		<property name="holderRelationInsuredDao" ref = "PA_holderRelationInsuredDao"></property>
	</bean>	
	<!-- CRS查询 -->
    <bean id="PA_nbQueryCrsCustomerService" class="com.nci.tunan.pa.impl.nbquerypolicy.service.impl.NBQueryCrsCustomerServiceImpl"> 
        <property name="nbQueryCrsCustomerDao" ref = "PA_nbQueryCrsCustomerDao"></property>
    </bean>
	<!-- 	上海医保的service -->
   <bean class="com.nci.tunan.pa.impl.shMedical.ShMedicalServiceImpl" id="shMedicalServiceImpl">
       	<property name="contractMasterDao" ref="PA_contractMasterDao"/>
       	<property name="payerAccountDao" ref="PA_payerAccountDao"/>
       	<property name="shMedicalDao" ref="PA_shMedicalDao"/>
    </bean>
    <!-- 根据投保单号查询风险保额service -->
    <bean id="PA_PolicyRiskInfoService" class="com.nci.tunan.pa.impl.policyinfo.service.impl.PolicyRiskInfoServiceImpl"> 
       <property name="riskAmountDao" ref="PA_riskAmountDao"></property>    
    </bean>
    <!-- 根据投保单号查询风险保额service -->
    <bean id="PA_policyMediaTypeService" class="com.nci.tunan.pa.impl.policymediatype.service.impl.PolicyMediaTypeServiceImpl"> 
       <property name="contractMasterDao" ref="PA_contractMasterDao"></property>    
    </bean>
    <!-- 保单编码查询（中保信） -->
	<bean id="PA_policyCodeQueryFromPAService" class="com.nci.tunan.pa.impl.policyCodeQueryFromPA.service.impl.PolicyCodeQueryFromPAServiceImpl">
        <property name="policyCodeQueryFromPADao" ref="PA_policyCodeQueryFromPADao"></property>
    </bean>
    
        <!-- 保单编码查询（中保信）出险人信息 -->
    <bean id="PA_phoneInfoQueryFromPAService" class="com.nci.tunan.pa.impl.phoneInfoQueryFromPA.service.impl.PhoneInfoQueryFromPAServiceImpl">
    	<property name="phoneInfoQueryFromPADao" ref="PA_phoneInfoQueryFromPADao"></property>
    </bean>
    
    <!-- box Account Service -->
    
    <bean id="PA_createBoxAccountService" class="com.nci.tunan.pa.impl.box.service.impl.CreateBoxAccountServiceImpl">
        <property name="createBoxAccountDao" ref="PA_createBoxAccountDao"></property>
        <property name="boxAccountDao" ref="PA_boxAccountDao"></property>
        <property name="investAccountInfoDao" ref="PA_investAccountInfoDao"/>
    </bean>
    <!-- BOX监管限额录入service -->
    <bean id="PA_createBoxSuperviseLimitService" class="com.nci.tunan.pa.impl.box.service.impl.CreateBoxSuperviseLimitServiceImpl">
        <property name="boxSuperviseLimitDao" ref="PA_boxSuperviseLimitDao"></property>
    </bean> 
    <!-- 资金划拨service -->
    <bean id="PA_BoxPositionService" class="com.nci.tunan.pa.impl.box.service.impl.BoxPositionServiceImpl">
        <property name="boxPositionDao" ref="PA_BoxPositionDao"></property>
        <property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
        <property name="createBoxInternalLimitDao" ref="PA_createBoxInternalLimitDao"></property>
        <property name="boxAccountDao" ref="PA_boxAccountDao"></property>
    </bean> 
    <!-- box 内部限额录入修改service -->
    <bean id="PA_createBoxInternalLimitService" class="com.nci.tunan.pa.impl.box.service.impl.CreateBoxInternalLimitServiceImpl">
       <property name="createBoxInternalLimitDao" ref="PA_createBoxInternalLimitDao"></property>
       <property name="boxInternalLimitDao" ref="PA_BoxInternalLimitDao"></property>
       <property name="boxSuperviseLimitDao" ref="PA_boxSuperviseLimitDao"></property>
    </bean> 
    <!-- BOX限额查询、BOX头寸查询、BOX资金划拨查询、BOX账户单位查询 service -->
    <bean id="PA_boxAccountQueryService" class="com.nci.tunan.pa.impl.box.service.impl.BoxAccountQueryServiceImpl">
    	<property name="createBoxAccountDao" ref="PA_createBoxAccountDao"></property>
    	<property name="boxSuperviseLimitDao" ref="PA_boxSuperviseLimitDao"></property>
    </bean>
    <!-- 计价报告打印service -->
   <bean id="PA_boxValuationReportService" class="com.nci.tunan.pa.impl.box.service.impl.BoxValuationReportServiceImpl">
   		<property name="boxValuationReportDao" ref="PA_boxValuationReportDao"></property>
   		<property name="fundTransApplyDao" ref="PA_fundTransApplyDao"/>
   		<property name="calInvestUnitPriceDao" ref="PA_calInvestUnitPriceDao"/>
   		<property name="boxPositionDao" ref="PA_BoxPositionDao"/>
    </bean> 
    
    <!-- 保单风险累计service -->
    <bean id="PA_riskAmountInterService" class="com.nci.tunan.pa.impl.riskamount.service.impl.RiskAmountInterServiceImpl">
       <property name="riskAmountDao" ref="PA_riskAmountDao"/>
    </bean>
    
     
    <!-- 根据投被保人五要素查询保单信息 service -->
    <bean id="PA_queryPolicyByFiveElementsService" class="com.nci.tunan.pa.impl.queryPolicyByFiveElementsInfo.service.impl.QueryPolicyByFiveElementsServiceImpl">
         <property name="customerDao" ref="PA_customerDao"></property>
         <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
    
    </bean>
    
    <!-- 日现价计算表dao -->
 	<bean class="com.nci.tunan.pa.dao.impl.PolicyCashvalueDaoImpl" id="PA_policyCashvalueDao" parent="baseDao" ></bean>
 	
 	<!-- 续期查询（接入渠道）-->
    <bean id="PA_queryRenewalPolicyService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.QueryRenewalPolicyServiceImpl">
    <property name="premArapDao" ref="PA_premArapDao"></property>
    <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
    </bean>
    
    <!-- 承保保单统计接口-->
    <bean id="PA_underwritingPolicyStatisticsService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.UnderwritingPolicyStatisticsServiceImpl">
    <property name="contractMasterDao" ref="PA_contractMasterDao"/>
    </bean>
       <!-- 保单状态数据查询service -->
    <bean id="PA_policyStatusChannelService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyStatusServiceImpl">
         <property name="premDao" ref="PA_premDao"></property>
         <property name="policyChangeDao" ref="PA_policyChangeDao"></property>
    </bean>

     <!-- 保单状态数据查询service -->
    <bean id="PA_queryPolicyStatusService" class="com.nci.tunan.pa.impl.querypolicystatus.service.impl.QueryPolicyStatusServiceImpl">
         <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
         <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
         <property name="premArapDao" ref="PA_premArapDao"></property>
         <property name="inbService" ref="PA_nbIAS"></property>
    </bean>
    
    <!-- 理赔-根据附加险单独投保保单查询主保单信息接口service -->
    <bean id="PA_claimQueryMasterPolicyService" class="com.nci.tunan.pa.impl.claimquerymasterpolicy.service.impl.ClaimQueryMasterPolicyServiceImpl">
         <property name="contractRelationDao" ref="PA_contractRelationDao"></property>
    </bean>
	
	<!--住院津贴限额校验接口service -->
    <bean id="PA_accumulatedhospitalizationService" class="com.nci.tunan.pa.impl.accumulatedhospitalization.service.impl.AccumulatedhospitalizationServiceImpl">
         <property name="insuredListDao" ref="PA_insuredListDao"/>
         <property name="inbService" ref="PA_nbIAS"></property>
    </bean>
    		<!-- 柜面渠道保单自动推送收展部FTP配置 -->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="PA_websiteToCusServiceftpServerConfig">
		<property name="ftpServerIp" value="${websiteToCusServiceftpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${websiteToCusServiceftpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${websiteToCusServiceftpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${websiteToCusServiceftpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${websiteToCusServiceftpServerConfig.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${websiteToCusServiceftpServerConfig.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${websiteToCusServiceftpServerConfig.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${websiteToCusServiceftpServerConfig.ftpServerBackupsDirectory}"/>
	</bean>	
	
	<!--柜面渠道保单自动推送收展部service -->
    <bean id="PA_websiteToCusServiceService" class="com.nci.tunan.pa.batch.websiteToCusService.service.impl.websiteToCusServiceServiceImpl">
         <property name="websiteToCusServiceDao" ref="PA_websiteToCusServiceDao"/>
         <property name="contractAgentDao" ref="PA_contractAgentDao"/>
         
    </bean>
        		<!-- 大地和产代寿FTP配置 -->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="PA_intermedOrphPolDealFtpServerConfig">
		<property name="ftpServerIp" value="${intermedOrphPolDealFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${intermedOrphPolDealFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${intermedOrphPolDealFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${intermedOrphPolDealFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${intermedOrphPolDealFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${intermedOrphPolDealFtpServerConfig.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${intermedOrphPolDealFtpServerConfig.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${intermedOrphPolDealFtpServerConfig.ftpServerBackupsDirectory}"/>
	</bean>
<!--移动保全2.0资料补传推送-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_csMobileReviewPushFtpServerConfig">
		<property name="ftpServerIp" value="${CsMobileReviewPushJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${CsMobileReviewPushJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${CsMobileReviewPushJob.ftpUserName}"/>
		<property name="ftpPassword" value="${CsMobileReviewPushJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${CsMobileReviewPushJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${DRQTaskExtractJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${CsMobileReviewPushJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${CsMobileReviewPushJob.ftpServerBackupsDirectory}"/>
	</bean>
	
<!--新掌上新华资料补传推送-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_ZSXHcsMobileReviewPushFtpServerConfig">
		<property name="ftpServerIp" value="${ZSXHCsMobileReviewPushJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${ZSXHCsMobileReviewPushJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${ZSXHCsMobileReviewPushJob.ftpUserName}"/>
		<property name="ftpPassword" value="${ZSXHCsMobileReviewPushJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${ZSXHCsMobileReviewPushJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${DRQTaskExtractJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${ZSXHCsMobileReviewPushJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${ZSXHCsMobileReviewPushJob.ftpServerBackupsDirectory}"/>
	</bean>	
	
<!--143327 随信通生效告知短信提醒推送-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_sendCSEffectMsg">
		<property name="ftpServerIp" value="${CSSendCSEffectMsg.ftpServerIp}"/>
		<property name="ftpServerPort" value="${CSSendCSEffectMsg.ftpServerPort}"/>
		<property name="ftpUserName" value="${CSSendCSEffectMsg.ftpUserName}"/>
		<property name="ftpPassword" value="${CSSendCSEffectMsg.ftpPassword}"/>
		<property name="ftpBufferSize" value="${CSSendCSEffectMsg.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${CSSendCSEffectMsg.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${CSSendCSEffectMsg.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${CSSendCSEffectMsg.ftpServerBackupsDirectory}"/>
	</bean>
<!--143327 随信通生效告知短信提醒推送-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_sendCSEffectMsgTextFtp">
		<property name="ftpUserName" value="${CSSendCSEffectMsgTextFtp.ftpUserName}"/>
		<property name="ftpPassword" value="${CSSendCSEffectMsgTextFtp.ftpPassword}"/>
	</bean>
<!--143327 打印ftp连接-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_csPrintResultFtp">
		<property name="ftpServerIp" value="${CsPrintResultFtp.ftpServerIp}"/>
		<property name="ftpServerPort" value="${CsPrintResultFtp.ftpServerPort}"/>
		<property name="ftpUserName" value="${CsPrintResultFtp.ftpUserName}"/>
		<property name="ftpPassword" value="${CsPrintResultFtp.ftpPassword}"/>
		<property name="ftpBufferSize" value="${CsPrintResultFtp.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${CsPrintResultFtp.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${CsPrintResultFtp.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${CsPrintResultFtp.ftpServerBackupsDirectory}"/>
	</bean>
	<!--173483_175535 蚂蚁保退生效同步批处理-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_csSurrenderMybFTP">
		<property name="ftpServerIp" value="${CsSurrenderMybFTP.ftpServerIp}"/>
		<property name="ftpServerPort" value="${CsSurrenderMybFTP.ftpServerPort}"/>
		<property name="ftpUserName" value="${CsSurrenderMybFTP.ftpUserName}"/>
		<property name="ftpPassword" value="${CsSurrenderMybFTP.ftpPassword}"/>
		<property name="ftpBufferSize" value="${CsSurrenderMybFTP.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${CsSurrenderMybFTP.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${CsSurrenderMybFTP.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${CsSurrenderMybFTP.ftpServerBackupsDirectory}"/>
	</bean>
	<!--保全双录质检资料推送-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_DRQTaskExtractJobFtpServerConfig">
		<property name="ftpServerIp" value="${DRQTaskExtractJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${DRQTaskExtractJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${DRQTaskExtractJob.ftpUserName}"/>
		<property name="ftpPassword" value="${DRQTaskExtractJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${DRQTaskExtractJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${DRQTaskExtractJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${DRQTaskExtractJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${DRQTaskExtractJob.ftpServerBackupsDirectory}"/>
	</bean>
	<!--保全双录质检资料推送结果返回-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_DRQSendReslutDealJobFtpServerConfig">
		<property name="ftpServerIp" value="${DRQSendReslutDealJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${DRQSendReslutDealJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${DRQSendReslutDealJob.ftpUserName}"/>
		<property name="ftpPassword" value="${DRQSendReslutDealJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${DRQSendReslutDealJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${DRQSendReslutDealJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${DRQSendReslutDealJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${DRQSendReslutDealJob.ftpServerBackupsDirectory}"/>
	</bean>

	<!--保全影音资料数据上传-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_CsDRVideoReturnJobFtpServerConfig">
		<property name="ftpServerIp" value="${CsDRVideoReturnJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${CsDRVideoReturnJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${CsDRVideoReturnJob.ftpUserName}"/>
		<property name="ftpPassword" value="${CsDRVideoReturnJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${CsDRVideoReturnJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${CsDRVideoReturnJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${CsDRVideoReturnJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${CsDRVideoReturnJob.ftpServerBackupsDirectory}"/>
	</bean>

	<!--保全异常影音数据回传-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_AbnormalDRInfoReturnJobFtpServerConfig">
		<property name="ftpServerIp" value="${AbnormalDRInfoReturnJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${AbnormalDRInfoReturnJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${AbnormalDRInfoReturnJob.ftpUserName}"/>
		<property name="ftpPassword" value="${AbnormalDRInfoReturnJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${AbnormalDRInfoReturnJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${AbnormalDRInfoReturnJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${AbnormalDRInfoReturnJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${AbnormalDRInfoReturnJob.ftpServerBackupsDirectory}"/>
	</bean>

	<!--核心保全质检结果回传补跑批处理-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_DRQResultSendJobFtpServerConfig">
		<property name="ftpServerIp" value="${DRQResultSendJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${DRQResultSendJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${DRQResultSendJob.ftpUserName}"/>
		<property name="ftpPassword" value="${DRQResultSendJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${DRQResultSendJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${DRQResultSendJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${DRQResultSendJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${DRQResultSendJob.ftpServerBackupsDirectory}"/>
	</bean>
	
    <!--保险合同外包打印批处理上传-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_BPOPolicyPrintTaskFtpUploadServerConfig">
		<property name="ftpServerIp" value="${CsBPOPolicyPrintJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${CsBPOPolicyPrintJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${CsBPOPolicyPrintJob.ftpUserName}"/>
		<property name="ftpPassword" value="${CsBPOPolicyPrintJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${CsBPOPolicyPrintJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${CsBPOPolicyPrintJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${CsBPOPolicyPrintJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${CsBPOPolicyPrintJob.ftpServerBackupsDirectory}"/>
	</bean>
	
    <!--保险合同外包打印批处理回传-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_PrintReturnJobFtpServerConfig">
		<property name="ftpServerIp" value="${CsPrintReturnJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${CsPrintReturnJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${CsPrintReturnJob.ftpUserName}"/>
		<property name="ftpPassword" value="${CsPrintReturnJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${CsPrintReturnJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${CsPrintReturnJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${CsPrintReturnJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${CsPrintReturnJob.ftpServerBackupsDirectory}"/>
	</bean>
	
    <!--万能险结算报告清单上传下载-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_UniversalListJobFtpServerConfig">
		<property name="ftpServerIp" value="${UniversalListJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${UniversalListJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${UniversalListJob.ftpUserName}"/>
		<property name="ftpPassword" value="${UniversalListJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${UniversalListJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${UniversalListJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${UniversalListJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${UniversalListJob.ftpServerBackupsDirectory}"/>
	</bean>
	
		<!--薪资状态读取service -->
    <bean id="PA_commisionStatusFetchService" class="com.nci.tunan.pa.batch.CommisionStatusFetch.service.impl.CommisionStatusFetchServiceImpl">
         <property name="commisionStatusFetchDao" ref="PA_commisionStatusFetchDao"/>
         
    </bean>
    
    <!--接入渠道交通银行-非实时保单查询接口service -->
    <bean id="PA_policyQueryDelayedJTYHService" class="com.nci.tunan.pa.impl.accesschannelquerypolicy.service.impl.PolicyQueryDelayedJTYHServiceImpl">
        <property name="policyQueryDelayedJTYHDao" ref="PA_policyQueryDelayedJTYHDao" />
    </bean>
    
    <!-- 保单承保信息更新接口 -->
	<bean class="com.nci.tunan.pa.impl.updatePolicyForPA.service.impl.UpdatePolicyInfoServiceImpl" id="PA_updatePolicyPAInfoService">
		<property name="medicalCardDao" ref="PA_medicalCardDao"/>
		<property name="busiProdFlagTraceDao" ref="PA_busiProdFlagTraceDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="shortPremArapDao" ref="PA_shortPremArapDao"/>
		<property name="renewAutomaticExtraService" ref="PA_extraService"/>
		<property name="renewAutomaticExtraDao" ref="PA_automaticExtraDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="prdIAS" ref="PA_prdIAS"/>
	</bean>
	 <!--销售限额配置-->
    <bean id="PA_salesAmountAccCfgService" class="com.nci.tunan.pa.impl.salesAmountAccCfg.service.impl.SalesAmountAccCfgServiceImpl">
    	<property name="salseAmountCfgDao" ref="PA_salseAmountCfgDao"/>
    	<property name="salseAmountCfgLogDao" ref="PA_salseAmountCfgLogDao"/>
    	 <property name="businessProductDao" ref="PA_businessProductDao"/>
    	 <property name="bankDao" ref="PA_bankDao"/>
    	 <property name="userUdmpDao" ref="PA_userUdmpDao" />
    	 <property name="warnMailInfoDao" ref="PA_warnMailInfoDao" />
    </bean>
    <!--销售限额配置轨迹-->
    <bean id="PA_salesAmountAccCfgLogService" class="com.nci.tunan.pa.impl.salesAmountAccCfgLog.service.impl.SalesAmountAccCfgLogServiceImpl">
    	<property name="salseAmountCfgLogDao" ref="PA_salseAmountCfgLogDao"/>
    	 <property name="businessProductDao" ref="PA_businessProductDao"/>
    	 <property name="userUdmpDao" ref="PA_userUdmpDao" />
    	 <property name="warnMailInfoDao" ref="PA_warnMailInfoDao" />
    </bean>
    <!--险种销售额度同步接口-->
    <bean id="PA_saveSalesAmountService" class="com.nci.tunan.pa.impl.saveSalesAmount.service.impl.SaveSalesAmountServiceImpl">
    	<property name="salesAmountDao" ref="PA_salesAmountDao"/>
    </bean>
    
    <!--投保人手机号及首期银行账号重复查询UCC接口-->
    <bean id="PA_queryAbnormalCustomerService" class="com.nci.tunan.pa.impl.queryAbnormalCustomer.service.impl.QueryAbnormalCustomerServiceImpl">
    </bean>
    
    <!--信托上传文件批处理-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="PA_trustSubmittedbatchUploadConfig">
		<property name="ftpServerIp" value="${trustSubmittedbatchUploadJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${trustSubmittedbatchUploadJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${trustSubmittedbatchUploadJob.ftpUserName}"/>
		<property name="ftpPassword" value="${trustSubmittedbatchUploadJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${trustSubmittedbatchUploadJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${trustSubmittedbatchUploadJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${trustSubmittedbatchUploadJob.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${trustSubmittedbatchUploadJob.ftpServerBackupsDirectory}"/>
	</bean>
	
	<!-- 保后调查上传批处理 -->
	<bean class="com.nci.tunan.pa.batch.riskScoreConfig.service.impl.RiskScoreConfigServiceImpl" id="PA_riskScoreConfigService"></bean>
	
	<!-- 重疾险险种风险级别设置 -->
	<bean class="com.nci.tunan.pa.impl.riskScoreConfig.service.impl.RiskScoreConfigServiceImpl" id="PA_riskScoreConfigServiceImpl">
		<property name="riskLevelConfigDao" ref="PA_riskLevelConfigDao"/>
	</bean>
	
	<!--团险风险保额累计Service-->
    <bean id="PA_groupRiskAccService" class="com.nci.tunan.pa.impl.groupRisk.service.impl.GroupRiskAccServiceImpl">
    </bean>
   	<!--暂收退费录入Service-->
    <bean id="PA_tempPremReturnService" class="com.nci.tunan.pa.impl.renewal.service.impl.TempPremReturnServiceImpl">
    	<property name="premArapDao" ref="PA_premArapDao"/>
    	<property name="policyStatusService" ref="PA_IPolicyStatusServiceImpl"/>
    	<property name="capIAS" ref="PA_capIAS"/>
    	<property name="renewExtraUCC" ref="PA_renewalExtraUCC"/>
    	<property name="renewAutomaticExtraService" ref="PA_extraService"/>
    	<property name="policyLogService" ref="PA_policyLogService"/>
        <property name="noticeService" ref="PA_noticeService"/>    	
        <property name="premDao" ref="PA_premDao"/>    	
    </bean> 
    
     <!-- 保单状态数据查询2service -->
    <bean id="PA_queryPolicyStatusByPolicyCodeService" class="com.nci.tunan.pa.impl.querypolicystatusbypolicycode.service.impl.QueryPolicyStatusByPolicyCodeServiceImpl">
         <property name="contractMasterDao" ref="PA_contractMasterDao"></property>
    </bean>
    <!-- 投保人及账户变更查询service -->
    <bean id="PA_policyHolderInfoQueryService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyHolderInfoQueryServiceImpl">
    </bean>
    <!-- 保单收益查询service -->
    <bean id="PA_policyIncomeQueryService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyIncomeQueryServiceImpl">
    </bean>
    <!-- 保单查询service -->
    <bean id="PA_policyInfoPsbcService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyInfoPsbcServiceImpl">
    	<property name="clmService" ref="PA_pa_clmService"/>
    </bean>
    
    <!-- 理赔养老金投资信息查询接口service -->
    <bean class="com.nci.tunan.pa.impl.queryBOCICPolicyInfoForClm.service.impl.QueryBOCICPolicyInfoForClmServiceImpl" id="PA_queryBOCICPolicyInfoForClmService">
	</bean>
	
	<!-- 收付费查询保单信息接口 Service -->
	<bean id="PA_queryPolicyInfoForCapService" class="com.nci.tunan.pa.impl.queryPolicyInfoForCap.service.impl.QueryPolicyInfoForCapServiceImpl">
	</bean>
	
	<!-- 银保通插入个人养老金信息报送表 Service接口 -->
    <bean id="PA_submitMessageBocicYBTService" class="com.nci.tunan.pa.impl.submitMessageBocicYBT.service.impl.SubmitMessageBocicYBTServiceImpl">
    <property name="submitMessageBocicDao" ref="PA_submitMessageBocicDao"></property>
    </bean>
	
	<!-- 需求分析任务 #137751 新核心功能优化（7）-保单管理start -->
	<!-- 查询投被关系集合接口UCC接口 -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.QueryRelation2PhInfoServiceImpl" id="PA_queryRelation2PhInfoService">
	<property name="customerDao" ref="PA_customerDao"/>
	</bean>
	
	<!-- 查询多个投保人信息接口UCC接口 -->
	<bean class="com.nci.tunan.pa.impl.commonQuery.service.impl.QueryMultiPolicyHoldersInfoServiceImpl" id="PA_queryMultiPolicyHoldersInfoService">
	<property name="customerDao" ref="PA_customerDao"/>
	</bean>
    <!-- 需求分析任务 #137751 新核心功能优化（7）-保单管理end -->
    
    <!-- 业务公共调用插入个人养老金信息报送表Service接口 -->
    <bean id="PA_annuityExpenseHXService" class="com.nci.tunan.pa.impl.annuityexpensehx.service.impl.AnnuityExpenseHXServiceImpl">
    <property name="submitMessageBocicDao" ref="PA_submitMessageBocicDao"></property>
    </bean>

	<!-- 保险信托2.0续期交费提醒 -->
    <bean class="com.nci.udmp.component.ftp.config.FtpServerConfig" id="PA_trustSenRenewalFtpServerConfig">
		<property name="ftpServerIp" value="${trustSenRenewalFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${trustSenRenewalFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${trustSenRenewalFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${trustSenRenewalFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${trustSenRenewalFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpServerWorkingDirectory" value="${trustSenRenewalFtpServerConfig.ftpServerWorkingDirectory}"/>
	</bean>
	
	<!-- 初始化续期年金生存调查结果确认ucc -->
    <bean id="PA_payConfirmListService" class="com.nci.tunan.pa.impl.payConfirmList.service.impl.PayConfirmListServiceImpl">
         <property name="payConfirmListDao" ref="PA_payConfirmListDao"></property>
    </bean>
    
        
    <!-- 需求分析任务 #147285: 睡眠保单项目需求-核心系统睡眠保单标识更新需求 add by liwei 睡眠保单通知情况更新UCC接口 -->
    <bean id="PA_sleepyPolicyStatusService" class="com.nci.tunan.pa.impl.sleepypolicy.service.impl.SleepyPolicyStatusServiceImpl">
        <property name="sleepyPolicyStatusDao" ref="PA_sleepyPolicyStatusDao"></property>
    	<property name="contractMasterDao" ref="PA_contractMasterDao"/>
    	<property name="policyMarkingInfoDao" ref="PA_policyMarkingInfoDao" />
		<property name="policyMarkingLogDao" ref="PA_policyMarkingLogDao" />
    </bean>
        <!-- 续保成功通知书批处理Service -->
    <bean id="PA_renewalSuccessfulService" class="com.nci.tunan.pa.batch.renewalsuccessful.service.impl.RenewalSuccessfulServiceImpl">
     	<property name="noticeService" ref="PA_noticeService"></property>
     	<property name="renewalSuccessfulDAO" ref="PA_renewalSuccessfulDAO"></property>
     	<property name="noticeMsgTaskDao" ref="PA_noticeMsgTaskDao"/>
    </bean>
    
    <!-- 需求分析任务 #160209 续保核保审核（保单管理）-->
    <bean id="PA_renewalUnderWritingService" class="com.nci.tunan.pa.batch.renewalunderwriting.service.impl.RenewalUnderWritingServiceImpl">
     	<property name="renewalUnderWritingDAO" ref="PA_renewalUnderWritingDAO"/>
     	<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="PA_contractProductDao"/>
		<property name="insuredListDao" ref="PA_insuredListDao"/>
		<property name="policyHolderDao" ref="PA_policyHolderDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="contractAgentDao" ref="PA_contractAgentDao"/>
		<property name="customerSurveyDao" ref="PA_customerSurveyDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractProductOtherDao" ref="PA_contractProductOtherDao"/>
		<property name="customerDao" ref="PA_customerDao"/>
		<property name="uwRenewalInfoDao" ref="PA_uwRenewalInfoDao"/>
		<property name="iPolicyLogService" ref="PA_policyLogService"/>
		<property name="uwService" ref="PA_uwIAS"/>
		<property name="bpmService" ref="PA_bpmIAS"/>
		<property name="clmService" ref="PA_pa_clmService"/>
		<property name="capService" ref="PA_capIAS"/>
		<property name="premArapDao" ref="PA_premArapDao"/>
    </bean>
    
    <!-- 商业养老保险账户信息同步查询Service  -->
    <bean id="PA_policyInfoPaqdService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.PolicyInfoPaqdServiceImpl">
    </bean>
    
    <!-- 手续费对账查询接口Service -->
    <bean id="PA_serviceChargeReconciliationService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.ServiceChargeReconciliationServiceImpl">
    </bean>
    
    <!-- 身故受益人详情查询接口Service -->
    <bean id="PA_dieBenefitInfoService" class="com.nci.tunan.pa.impl.interfaceforchannel.service.impl.DieBenefitInfoServiceImpl">
    </bean>
    
    <!-- 提供客户信息给契约做银保通业务功能Service -->
    <bean id="PA_provideCustomerInfosForBancasBusinessForNBService" class="com.nci.tunan.pa.impl.hessianinterface.service.impl.ProvideCustomerInfosForBancasBusinessForNBServiceImpl">
    </bean>
    
    <!--非实时提数文件上传-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="PA_pqdSendJson">
		<property name="ftpServerIp" value="${pqdSendJson.ftpServerIp}"/>
		<property name="ftpServerPort" value="${pqdSendJson.ftpServerPort}"/>
		<property name="ftpUserName" value="${pqdSendJson.ftpUserName}"/>
		<property name="ftpPassword" value="${pqdSendJson.ftpPassword}"/>
		<property name="ftpBufferSize" value="${pqdSendJson.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${pqdSendJson.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${pqdSendJson.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${pqdSendJson.ftpServerBackupsDirectory}"/>
	</bean>
	
		<bean class="com.nci.tunan.pa.impl.querynextpricedateaftersigndate.service.impl.QueryNextPricingDateServiceImpl" id="PA_queryNextPricingDateService">
		<property name="investUnitPriceDao" ref="PA_investUnitPriceDao"/>
	</bean>
	
	 <bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="PA_policyLockFlagFtpServerConfig">
		<property name="ftpServerIp" value="${policyLockFlagFtpServerConfig.ftpServerIp}"/>
		<property name="ftpServerPort" value="${policyLockFlagFtpServerConfig.ftpServerPort}"/>
		<property name="ftpUserName" value="${policyLockFlagFtpServerConfig.ftpUserName}"/>
		<property name="ftpPassword" value="${policyLockFlagFtpServerConfig.ftpPassword}"/>
		<property name="ftpBufferSize" value="${policyLockFlagFtpServerConfig.ftpBufferSize}"/>
		<property name="ftpServerWorkingDirectory" value="${policyLockFlagFtpServerConfig.ftpServerWorkingDirectory}"/>
		<property name="ftpServerBackupsDirectory" value="${policyLockFlagFtpServerConfig.ftpServerBackupsDirectory}"/>
	</bean>
    <!-- 修改保单锁定标识处理Service -->
    <bean id="PA_updatePolicyLockFlagService" class="com.nci.tunan.pa.batch.updatepolicylockflag.service.impl.UpdatePolicyLockFlagServiceImpl"></bean>
    
    <!-- 年金、生存金、满期金贷款抵扣Service -->
    <bean id="PA_survivalDeductionTaskService" class="com.nci.tunan.pa.batch.survivalAnnuityMaturity.service.impl.SurvivalDeductionTaskServiceImpl"></bean>
    
    
       <!--需求分析任务 #172886: 关于新增银代渠道分公司佣金率管理等相关功能的需求-二期 保单 -->
   <bean class="com.nci.tunan.pa.impl.bankorgancommratecfg.service.impl.BankOrganCommRateCfgServiceImpl" id="PA_bankOrganCommRateCfgService">
	 <property name="fycrateComDao" ref="PA_FycrateComDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.laratecommisionrate.service.impl.LaratecommisionRateServiceImpl" id="PA_laratecommisionRateService">
	 <property name="fycrateComDao" ref="PA_FycrateComDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.discountpremrate.service.impl.DiscountPremRateCfgServiceImpl" id="PA_discountPremRateCfgService">
	 <property name="discountPremRateDao" ref="PA_DiscountPremRateDao"/>
	</bean>
	
	<bean class="com.nci.tunan.pa.impl.querypapersupplement.service.impl.QueryPaperSupplementServiceImpl" id="PA_queryPaperSupplementService">
	 <property name="queryPaperSupplementDao" ref="PA_queryPaperSupplementDao"/>
	</bean>
	
	<!-- 需求分析任务 #188646: 需求意向单：个险新核心核心万能险抵交保费功能优化-保单 -->
		<bean class="com.nci.tunan.pa.common.service.impl.UniverInsuranceOffsetPremServiceImpl" id="PA_univerInsuranceOffsetPremService">
	</bean>
	
	 <!--保全质检明细清单上传下载-->
	<bean class="com.nci.tunan.pa.common.PaFtpServerConfig" id="CS_QualitDetalListJobFtpServerConfig">
		<property name="ftpServerIp" value="${QualitDetalListJob.ftpServerIp}"/>
		<property name="ftpServerPort" value="${QualitDetalListJob.ftpServerPort}"/>
		<property name="ftpUserName" value="${QualitDetalListJob.ftpUserName}"/>
		<property name="ftpPassword" value="${QualitDetalListJob.ftpPassword}"/>
		<property name="ftpBufferSize" value="${QualitDetalListJob.ftpBufferSize}"/>
		<property name="ftpEncoding" value="${QualitDetalListJob.ftpEncoding}"/>
		<property name="ftpServerWorkingDirectory" value="${QualitDetalListJob.ftpServerWorkingDirectory}"/>
	</bean>
</beans>