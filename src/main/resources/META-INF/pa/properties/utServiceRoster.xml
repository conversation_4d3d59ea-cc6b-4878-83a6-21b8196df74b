<?xml version="1.0" encoding="UTF-8"?>
<xml>
	<enviroment module="MMS" ip="***********" port="9080" context="prd">
		<service   hsService="remoting/mmsProductQueryHSAddr" wsService="webservice/productQueryWSAddr" interfaceName="com.nci.tunan.mms.interfaces.query.exports.productquery.ws.IProductQueryWSAdapter" serviceName="productQueryAddr"   methodName="productQuery" />
		<service   hsService="remoting/mmsProductCSSVHSAddr" wsService="webservice/productCSSVWSAddr" interfaceName="com.nci.tunan.mms.interfaces.calc.exports.csgrosssv.ws.IProductCsGrossSvWSAdapter" serviceName="productCSSVAddr"   methodName="csGrossSv" />
		<service   hsService="remoting/mmsCalProductRiskAmountHSAddr" wsService="webservice/calProductRiskAmountWSAddr" interfaceName="com.nci.tunan.mms.interfaces.calc.exports.calriskamount.hs.IProductCalRiskAmountHSAdapter" serviceName="calProductRiskAmountHSAddr"   methodName="calProductRiskAmount" />
		<service   hsService="remoting/mmsCalcBasePremHSAddr" wsService="webservice/calcBasePremHSAddr" interfaceName="com.nci.tunan.mms.interfaces.calc.exports.calcbaseprem.hs.ICalcBasePremHSAdapter" serviceName="calcBasePremHSAddr"   methodName="calcBasePrem" />
		<service   hsService="remoting/mmsCalcAccountFeeHSAddr" wsService="webservice/calcAccountFeeHSAddr" interfaceName="com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.hs.ICalcAccountFeeHSAdapter" serviceName="calcAccountFeeHSAddr"   methodName="calcAccountFee" />
	</enviroment>
	<enviroment module="NB" ip="***********" port="9080" context="nb">
		<service   hsService="remoting/nbUnsignHSAddr" wsService="webservice/nbUnsignHSAddr" interfaceName="com.nci.tunan.nb.interfaces.unsign.exports.queryUnsign.hs.INBQueryUnsignHS" serviceName="INBQueryUnsignHS"   methodName="queryPolicyList" />
	</enviroment>
	<enviroment module="PRD" ip="***********" port="9080" context="prd">
		<service   hsService="remoting/productBaseInfoQueryHSAddr" wsService="webservice/productBaseInfoQueryWSAddr" interfaceName="com.nci.tunan.prd.interfaces.query.exports.productbaseinfoquery.hs.IProductBaseInfoQueryHSAdapter" serviceName="productBaseInfoQueryHSAddr"   methodName="productQuery" />
		<service   hsService="remoting/dutyGroupQueryHSAddr" wsService="webservice/dutyGroupQueryWSAddr" interfaceName="com.nci.tunan.prd.interfaces.query.exports.dutygroupquery.hs.IDutyGroupQueryHSAdapter" serviceName="dutyGroupQueryHSAddr"   methodName="dutyGroupQuery" />
	</enviroment>
	<enviroment module="BPM" ip="**********" port="7001" context="BPM_API">
		<service hsService="remoting/ICreateProcessWebServiceAddr" wsService="webservice/ICreateProcessWebServiceAddr"
				interfaceName="com.nci.tunan.bpm.ws.process.create.ICreateProcessWebService"
				serviceName="ICreateProcessWebService" methodName="createProcess" />
		<service hsService="remoting/IQueryProcessWebServiceAddr" wsService="webservice/IQueryProcessWebServiceAddr"
				interfaceName="com.nci.tunan.bpm.ws.process.query.IQueryProcessWebService"
				serviceName="IQueryProcessWebService" methodName="queryTask" />
		<service hsService="remoting/IAcquireTaskWebServiceAddr" wsService="webservice/IAcquireTaskWebServiceAddr"
				interfaceName="com.nci.tunan.bpm.ws.task.acquire.IAcquireTaskWebService"
				serviceName="IAcquireTaskWebService" methodName="acquireTask" />
		<service hsService="remoting/ISubmitProcessWebServiceAddr" wsService="webservice/ISubmitProcessWebServiceAddr"
			interfaceName="com.nci.tunan.bpm.ws.process.submit.ISubmitProcessWebService"
			serviceName="ISubmitProcessWebService" methodName="submitTask" />
	</enviroment>
	<enviroment module="cap" ip="***********" port="9080" context="cap">
       <service   hsService="$serviceinfo.hsService" wsService="webservice/arapSaveInterfaceServiceAddr?wsdl" interfaceName="com.nci.tunan.cap.interfaces.wsinterfaces.exports.ws.IArapSaveInterfaceService" serviceName="arapSaveInterfaceService"   methodName="saveArap" />
       <service   hsService="$serviceinfo.hsService" wsService="webservice/arapForPasServiceddr" interfaceName="com.nci.tunan.cap.interfaces.wsinterfaces.exports.ws.IArapForPasService" serviceName="arapForPasService" methodName="ArapForPas" />
	</enviroment>
</xml>