accountValueftpServerConfig.ftpServerIp = ***********
accountValueftpServerConfig.ftpUserName = xhdstest
accountValueftpServerConfig.ftpPassword = ncl123
accountValueftpServerConfig.ftpServerPort = 21
accountValueftpServerConfig.ftpEncoding = UTF-8
accountValueftpServerConfig.ftpBufferSize = ********
accountValueftpServerConfig.ftpServerWorkingDirectory = /pa/batch/

ftpServerConfigNetpinPrem.ftpServerIp = ***********
ftpServerConfigNetpinPrem.ftpUserName = cashdata
ftpServerConfigNetpinPrem.ftpPassword = ccc@123
ftpServerConfigNetpinPrem.ftpServerPort = 21
ftpServerConfigNetpinPrem.ftpEncoding = UTF-8
ftpServerConfigNetpinPrem.ftpBufferSize = ********
ftpServerConfigNetpinPrem.ftpServerWorkingDirectory = /pa/batch/

hlFtpServerConfig.ftpServerIp = **********
hlFtpServerConfig.ftpServerPort = 21
hlFtpServerConfig.ftpUserName = extftp
hlFtpServerConfig.ftpPassword = ext@1234
hlFtpServerConfig.ftpEncoding = UTF-8
hlFtpServerConfig.ftpBufferSize = ********

hlzip.ftpServerIp = **********
hlzip.ftpUserName = noticezj
hlzip.ftpPassword = LsqMM7zdCC
hlzip.ftpServerPort = 1358
hlzip.ftpEncoding = UTF-8
hlzip.ftpBufferSize = ********
hlzip.ftpServerWorkingDirectory = /wbdy

orphanFtpServerConfig.ftpServerIp = **********
orphanFtpServerConfig.ftpServerPort = 1358
orphanFtpServerConfig.ftpUserName = channelzj
orphanFtpServerConfig.ftpPassword = Mfr4v6mLW6
orphanFtpServerConfig.ftpEncoding = UTF-8
orphanFtpServerConfig.ftpBufferSize = ********
orphanFtpServerConfig.ftpServerWorkingDirectory = /pa/batch/
orphanFtpServerConfig.ftpServerUploadDirectory = /pa/orphanPolicy/
orphanFtpServerConfig.ftpServerBackupsDirectory = /pa/actualBatchCompar/
orphanFtpServerConfig.ftpServerReadDirectory = /pa/allocateResult/
orphanFtpServerConfig.ftpServerWarningDirectory = /pa/channel/salesmanInfo/send/
orphanFtpServerConfig.ftpServerWarningBackDirectory = /pa/channel/salesmanInfo/send_bak/

 
ftpServerConfig.ftpServerIp = **********
ftpServerConfig.ftpServerPort = 21
ftpServerConfig.ftpUserName = extftp
ftpServerConfig.ftpPassword = ext@1234
ftpServerConfig.ftpEncoding = UTF-8
ftpServerConfig.ftpBufferSize = ********
 
cashValueFtpServerConfig.ftpServerIp = ***********
cashValueFtpServerConfig.ftpServerPort = 19239
cashValueFtpServerConfig.ftpUserName = ncs
cashValueFtpServerConfig.ftpPassword = Nci@2025
cashValueFtpServerConfig.ftpEncoding = UTF-8
cashValueFtpServerConfig.ftpBufferSize = ********
cashValueFtpServerConfig.ftpServerWorkingDirectory = /send/ods/

absCashValueFtpServerConfig.ftpServerIp = **********
absCashValueFtpServerConfig.ftpServerPort = 1358
absCashValueFtpServerConfig.ftpUserName = abszj
absCashValueFtpServerConfig.ftpPassword = a2bQaz7ek9
absCashValueFtpServerConfig.ftpEncoding = UTF-8
absCashValueFtpServerConfig.ftpBufferSize = ********
absCashValueFtpServerConfig.ftpServerWorkingDirectory = /pa/abs/policy/
absCashValueFtpServerConfig.ftpServerBackupsDirectory = /pa/abs/policybak/
 
seckillFtpServerConfig.ftpServerIp = **********
seckillFtpServerConfig.ftpServerPort = 21
seckillFtpServerConfig.ftpUserName = extftp
seckillFtpServerConfig.ftpPassword = ext@1234
seckillFtpServerConfig.ftpEncoding = UTF-8
seckillFtpServerConfig.ftpBufferSize = ********
seckillFtpServerConfig.ftpServerWorkingDirectory = /home/<USER>/netsale/policyWriteback/back/
seckillFtpServerConfig.dataBackFileUrl = /home/<USER>/newcore/resyn/back/

custRiskFtpServerConfig.ftpServerIp = **********
custRiskFtpServerConfig.ftpServerPort = 1358
custRiskFtpServerConfig.ftpUserName = mdmzj
custRiskFtpServerConfig.ftpPassword = FzdqlNI4vP
custRiskFtpServerConfig.ftpEncoding = UTF-8
custRiskFtpServerConfig.ftpBufferSize = ********
custRiskFtpServerConfig.ftpServerWorkingDirectory = /pa/MDMInfo/


policyReissueServerConfig.ftpServerIp = **********
policyReissueServerConfig.ftpServerPort = 1358
policyReissueServerConfig.ftpUserName = imagezj
policyReissueServerConfig.ftpPassword = H4RFUkCH4V
policyReissueServerConfig.ftpEncoding = UTF-8
policyReissueServerConfig.ftpBufferSize = ********
policyReissueServerConfig.ftpServerWorkingDirectory = policyPrintPic

networkSaleQueryServerConfig.ftpServerIp = ***********
networkSaleQueryServerConfig.ftpServerPort = 21
networkSaleQueryServerConfig.ftpUserName = core_baoquan
networkSaleQueryServerConfig.ftpPassword = core_bq@123
networkSaleQueryServerConfig.ftpEncoding = UTF-8
networkSaleQueryServerConfig.ftpBufferSize = ********
networkSaleQueryServerConfig.ftpServerWorkingDirectory = /


wbpdfFtpServerConfig.ftpServerIp = **********
wbpdfFtpServerConfig.ftpServerPort = 21
wbpdfFtpServerConfig.ftpUserName = extftp
wbpdfFtpServerConfig.ftpPassword = ext@1234
wbpdfFtpServerConfig.ftpEncoding = UTF-8
wbpdfFtpServerConfig.ftpBufferSize = ********
wbpdfFtpServerConfig.ftpServerWorkingDirectory = /PrintSystem/LtrNORT/
wbpdfFtpServerConfig.ftpServerUploadDirectory = /home/<USER>/was/AppServer/profiles/AppSrv01/wbNoticePrint/


cswbpdfFtpServerConfig.ftpServerIp = **********
cswbpdfFtpServerConfig.ftpServerPort = 1358
cswbpdfFtpServerConfig.ftpUserName = bpoprintzj
cswbpdfFtpServerConfig.ftpPassword = uqp0OJ1vrL
cswbpdfFtpServerConfig.ftpEncoding = UTF-8
cswbpdfFtpServerConfig.ftpBufferSize = ********
cswbpdfFtpServerConfig.ftpServerWorkingDirectory = /cs/print/outsourced/
cswbpdfFtpServerConfig.ftpServerUploadDirectory = /cs/print/outsourced/



marketersabandonFtpServerConfig.ftpServerIp = **********
marketersabandonFtpServerConfig.ftpServerPort = 1358
marketersabandonFtpServerConfig.ftpUserName = channelzj
marketersabandonFtpServerConfig.ftpPassword = Mfr4v6mLW6
marketersabandonFtpServerConfig.ftpEncoding = UTF-8
marketersabandonFtpServerConfig.ftpBufferSize = ********
marketersabandonFtpServerConfig.ftpServerWorkingDirectory = /pa/channel/givepolicy/send/
marketersabandonFtpServerConfig.ftpServerBackupsDirectory = /pa/channel/givepolicy/send_bak/

synchronizationPolicyCodeInfoFtpServerConfig.ftpServerIp = **********
synchronizationPolicyCodeInfoFtpServerConfig.ftpServerPort = 1358
synchronizationPolicyCodeInfoFtpServerConfig.ftpUserName = channelzj
synchronizationPolicyCodeInfoFtpServerConfig.ftpPassword = Mfr4v6mLW6
synchronizationPolicyCodeInfoFtpServerConfig.ftpEncoding = UTF-8
synchronizationPolicyCodeInfoFtpServerConfig.ftpBufferSize = ********
synchronizationPolicyCodeInfoFtpServerConfig.ftpServerWorkingDirectory = /pa/channel/policyinfo/send/
synchronizationPolicyCodeInfoFtpServerConfig.ftpServerBackupsDirectory = /pa/channel/policyinfo/send_bak/


PAS00010.ftpServerIp = **********
PAS00010.ftpUserName = channelzj
PAS00010.ftpPassword = Mfr4v6mLW6
PAS00010.ftpServerPort = 1358
PAS00010.ftpEncoding = UTF-8
PAS00010.ftpBufferSize = ********
PAS00010.ftpServerWorkingDirectory = /pa/batch/

websiteToCusServiceftpServerConfig.ftpServerIp = **********
websiteToCusServiceftpServerConfig.ftpUserName = channelzj
websiteToCusServiceftpServerConfig.ftpPassword = Mfr4v6mLW6
websiteToCusServiceftpServerConfig.ftpServerPort = 1358
websiteToCusServiceftpServerConfig.ftpEncoding = UTF-8
websiteToCusServiceftpServerConfig.ftpBufferSize = ********
websiteToCusServiceftpServerConfig.ftpServerWorkingDirectory = /pa/channel/10toapolicy/send/
websiteToCusServiceftpServerConfig.ftpServerBackupsDirectory = /pa/channel/10toapolicy/send_bak/

intermedOrphPolDealFtpServerConfig.ftpServerIp = ************
intermedOrphPolDealFtpServerConfig.ftpUserName = user01
intermedOrphPolDealFtpServerConfig.ftpPassword = ncl@1q2w
intermedOrphPolDealFtpServerConfig.ftpServerPort = 22
intermedOrphPolDealFtpServerConfig.ftpEncoding = UTF-8
intermedOrphPolDealFtpServerConfig.ftpBufferSize = ********
intermedOrphPolDealFtpServerConfig.ftpServerWorkingDirectory = /home/<USER>/sftpshare/chandaishou/jishu/
intermedOrphPolDealFtpServerConfig.ftpServerBackupsDirectory = /data01/ftp_ceshi2/ftp/CdsPsnStateDdUpload/js_bak

SocialAssistanceCheckQueryURL = http://***********/wsproxy/services/WS_mzsybx?wsdl
bondCode=904
password=123456
key:1Qaz2Wsx=-09][po
ticket:0851273f-448a-44e5-9602-485835447082


commisionStatusFetchFtpServerConfig.ftpServerIp = **********
commisionStatusFetchFtpServerConfig.ftpUserName = channelzj
commisionStatusFetchFtpServerConfig.ftpPassword = Mfr4v6mLW6
commisionStatusFetchFtpServerConfig.ftpServerPort = 1358
commisionStatusFetchFtpServerConfig.ftpEncoding = UTF-8
commisionStatusFetchFtpServerConfig.ftpBufferSize = ********
commisionStatusFetchFtpServerConfig.ftpServerWorkingDirectory = /pa/channel/WAGENO/send/
commisionStatusFetchFtpServerConfig.ftpServerBackupsDirectory = /pa/channel/WAGENO/send_bak/

#ï¿½ï¿½È«Ë«Â¼ï¿½Ê¼ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
DRQTaskExtractJob.ftpServerIp = **********
DRQTaskExtractJob.ftpUserName = ncivcszj
DRQTaskExtractJob.ftpPassword = nyvSOuqF2P
DRQTaskExtractJob.ftpServerPort = 1358
DRQTaskExtractJob.ftpEncoding = UTF-8
DRQTaskExtractJob.ftpBufferSize = ********
DRQTaskExtractJob.ftpServerWorkingDirectory = /pas/ncivcs/policyInfo/senddata
DRQTaskExtractJob.ftpServerBackupsDirectory = /pas/ncivcs/policyInfo/senddata_bak
#ï¿½ï¿½È«Ë«Â¼ï¿½Ê¼ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Í½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
DRQSendReslutDealJob.ftpServerIp = **********
DRQSendReslutDealJob.ftpUserName = ncivcszj
DRQSendReslutDealJob.ftpPassword = nyvSOuqF2P
DRQSendReslutDealJob.ftpServerPort = 1358
DRQSendReslutDealJob.ftpEncoding = UTF-8
DRQSendReslutDealJob.ftpBufferSize = ********
DRQSendReslutDealJob.ftpServerWorkingDirectory = /pas/ncivcs/policyInfo/returndata
DRQSendReslutDealJob.ftpServerBackupsDirectory = /pas/ncivcs/policyInfo/returndata_bak
#ï¿½ï¿½È«Ó°ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ï´ï¿½
CsDRVideoReturnJob.ftpServerIp = **********
CsDRVideoReturnJob.ftpUserName = ncivcszj
CsDRVideoReturnJob.ftpPassword = nyvSOuqF2P
CsDRVideoReturnJob.ftpServerPort = 1358
CsDRVideoReturnJob.ftpEncoding = UTF-8
CsDRVideoReturnJob.ftpBufferSize = ********
CsDRVideoReturnJob.ftpServerWorkingDirectory = /pas/ncivcs/upload
CsDRVideoReturnJob.ftpServerBackupsDirectory = /pas/ncivcs/upload_bak
#ï¿½ï¿½È«ï¿½ì³£Ó°ï¿½ï¿½ï¿½ï¿½ï¿½Ý»Ø´ï¿½
AbnormalDRInfoReturnJob.ftpServerIp = **********
AbnormalDRInfoReturnJob.ftpUserName = ncivcszj
AbnormalDRInfoReturnJob.ftpPassword = nyvSOuqF2P
AbnormalDRInfoReturnJob.ftpServerPort = 1358
AbnormalDRInfoReturnJob.ftpEncoding = UTF-8
AbnormalDRInfoReturnJob.ftpBufferSize = ********
AbnormalDRInfoReturnJob.ftpServerWorkingDirectory = /pas/ncivcs/feedbackNew
AbnormalDRInfoReturnJob.ftpServerBackupsDirectory = /pas/ncivcs/feedbackNew_bak
#ï¿½ï¿½ï¿½Ä±ï¿½È«ï¿½Ê¼ï¿½ï¿½ï¿½ï¿½Ø´ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
DRQResultSendJob.ftpServerIp = **********
DRQResultSendJob.ftpUserName = ncivcszj
DRQResultSendJob.ftpPassword = nyvSOuqF2P
DRQResultSendJob.ftpServerPort = 1358
DRQResultSendJob.ftpEncoding = UTF-8
DRQResultSendJob.ftpBufferSize = ********
DRQResultSendJob.ftpServerWorkingDirectory = /pas/ncivcs/feedbackNew
DRQResultSendJob.ftpServerBackupsDirectory = /pas/ncivcs/feedbackNew_bak
#ä¿é©ååæå°æ¹å¤çæ¨éFTPéç½®
CsBPOPolicyPrintJob.ftpServerIp = **********
CsBPOPolicyPrintJob.ftpUserName = print_zj
CsBPOPolicyPrintJob.ftpPassword = 5L0eHWzaNy
CsBPOPolicyPrintJob.ftpServerPort = 1358
CsBPOPolicyPrintJob.ftpEncoding = UTF-8
CsBPOPolicyPrintJob.ftpBufferSize = ********
CsBPOPolicyPrintJob.ftpServerWorkingDirectory = /pa/nclprint/newmissons/newpolicy
CsBPOPolicyPrintJob.ftpServerBackupsDirectory = /pa/nclprint/newmissons/newpolicy_bak
#æå°ç³»ç»ä¿¡æ¯åä¼ æ¹å¤çFTPéç½®
CsPrintReturnJob.ftpServerIp = **********
CsPrintReturnJob.ftpUserName = print_zj
CsPrintReturnJob.ftpPassword = 5L0eHWzaNy
CsPrintReturnJob.ftpServerPort = 1358
CsPrintReturnJob.ftpEncoding = UTF-8
CsPrintReturnJob.ftpBufferSize = ********
CsPrintReturnJob.ftpServerWorkingDirectory = /pa/nclprint/returndata/newpolicy
CsPrintReturnJob.ftpServerBackupsDirectory = /pa/nclprint/returndata/newpolicy_bak
#ä¸è½é©ç»ç®æ¥åæ¸åä¸ä¼ ä¸è½½FTPéç½®
UniversalListJob.ftpServerIp = **********
UniversalListJob.ftpUserName = listuser
UniversalListJob.ftpPassword = mMpYH8bWj3
UniversalListJob.ftpServerPort = 1358
UniversalListJob.ftpEncoding = UTF-8
UniversalListJob.ftpBufferSize = ********
UniversalListJob.ftpServerWorkingDirectory = /PasReportFile/universal/
UniversalListJob.ftpServerBackupsDirectory = /PasReportFile/universal_bak/

SzybRefund.ftpServerIp = ***************:8080

SzybToken.ftpServerIp = http://*************:8085/wx/token/getAccessToken.gsp

SzybID.ftpServerIp = XHLC_PHBM

Szybmd5.ftpServerIp = 4O78wBKWgtZTax

SzybAESkey.ftpServerIp = GDFOGICOXTJYTQWVKLZXSRMMCXZAYZRB


#ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
DRQVideoURLLinux = http://**************:8001/vcs/api/transaction/checkRiskBQ
DRQVideoQueryURLLinux = http://**************:8001/vcs/api/transaction/get_url



trustSubmittedbatchUploadJob.ftpServerIp = **********
trustSubmittedbatchUploadJob.ftpUserName = ftpadmin
trustSubmittedbatchUploadJob.ftpPassword = admin123
trustSubmittedbatchUploadJob.ftpServerPort = 1358
trustSubmittedbatchUploadJob.ftpEncoding = UTF-8
trustSubmittedbatchUploadJob.ftpBufferSize = ********
trustSubmittedbatchUploadJob.ftpServerWorkingDirectory = /PA_XT/
trustSubmittedbatchUploadJob.ftpServerBackupsDirectory = /PA_XT/

XTHQLPJK = http://***************:8080/teamwork/provide/doLogin
XTPLSSBDXX = http://***************:8080/teamwork/policy/provide/submitPoliciesMti
XTPLSSBDWJ = http://***************:8080/teamwork/policy/provide/uploadPoliciesMti
TOKENNAME = SYS-XINHUA-TEST
TOKENPASSWORD = Citict@xinhua-test!
XHBXGSBH = ************
DZBDPTURL = http://**********/epolicy_services/service/epolicyUrl?wsdl

csVideoInquire.serverIp = http://**************:8081/#/linkView
#csVideoInquire.serverIp = http://**************/#/linkView
csVideoInquire.userName = newcore
csVideoInquire.password = newcore12345

openAccountApplicationInfoVerify.serverIp = http://1***********:8080/SYGRYLJ/OpenYbxAccController/openAccountApplicationInfoVerify
#openAccountApplicationInfoVerify.serverIp = http://************:8080/SYGRYLJ/OpenYbxAccController/openAccountApplicationInfoVerify
#openAccountApplicationInfoVerify.serverIp = http://**********:8080/SYGRYLJ/OpenYbxAccController/openAccountApplicationInfoVerify
openAccountApplicationInfoVerify.userName = 001LT_XHRSBX
openAccountApplicationInfoVerify.password = 001LT_XHRSBXPWD

codeInfoVerify.serverIp = http://1***********:8080/SYGRYLJ/OpenYbxAccController/codeInfoVerify
#codeInfoVerify.serverIp = http://************:8080/SYGRYLJ/OpenYbxAccController/codeInfoVerify
#codeInfoVerify.serverIp = http://**********:8080/SYGRYLJ/OpenYbxAccController/codeInfoVerify
codeInfoVerify.userName = 001LT_XHRSBX
codeInfoVerify.password = 001LT_XHRSBXPWD

queryCsInfo.serverIp = http://1***********:8080/SYGRYLJ/InsuranceProductController/insurancePreseInfoVerify
#queryCsInfo.serverIp = http://************:8080/SYGRYLJ/InsuranceProductController/insurancePreseInfoVerify
#queryCsInfo.serverIp = http://**********:8080/SYGRYLJ/InsuranceProductController/insurancePreseInfoVerify
queryCsInfo.userName = 001LT_XHRSBX
queryCsInfo.password = 001LT_XHRSBXPWD

riskScoreConfigFtpServerConfig.ftpServerIp = **********
riskScoreConfigFtpServerConfig.ftpServerPort = 1358
riskScoreConfigFtpServerConfig.ftpUserName = ncivcszj
riskScoreConfigFtpServerConfig.ftpPassword = nyvSOuqF2P
riskScoreConfigFtpServerConfig.ftpEncoding = UTF-8
riskScoreConfigFtpServerConfig.ftpBufferSize = ********
riskScoreConfigFtpServerConfig.ftpServerWorkingDirectory = /app/bsfit/processData
riskScoreConfigFtpServerConfig.ftpServerReadDirectory = /app/bsfit/result
riskScoreConfigFtpServerConfig.ftpServerBackupsDirectory = /app/bsfit/result_bak


PAS00010PDF.ftpServerIp = **********
PAS00010PDF.ftpUserName = ncivcszj
PAS00010PDF.ftpPassword = nyvSOuqF2P
PAS00010PDF.ftpServerPort = 1358
PAS00010PDF.ftpEncoding = UTF-8
PAS00010PDF.ftpBufferSize = ********
PAS00010PDF.ftpServerWorkingDirectory = /pas/ncivcs/policyInfo
PAS00010PDF.ftpServerBackupsDirectory = /pas/ncivcs/policyInfo

TestPDF.ftpServerIp = **********
TestPDF.ftpUserName = ftpadmin
TestPDF.ftpPassword = uoPz8Mmegv
TestPDF.ftpServerPort = 1358
TestPDF.ftpEncoding = UTF-8
TestPDF.ftpBufferSize = ********
TestPDF.ftpServerUploadDirectory =  /PrintSystem/LtrORTTXT
TestPDF.ftpServerBackDirectory   =  /PrintSystem//LtrORTTXTBK
TestPDF.ftpServerWorkingDirectory = /PrintSystem/LtrORTPDF
TestPDF.ftpServerBackupsDirectory = /PrintSystem/LtrORTPDFZip

#yidongbaoquan2.0 ftp 
CsMobileReviewPushJob.ftpServerIp = ***********
CsMobileReviewPushJob.ftpUserName = ydbq2ftp
CsMobileReviewPushJob.ftpPassword = ydbq2ftp
CsMobileReviewPushJob.ftpServerPort = 21
CsMobileReviewPushJob.ftpEncoding = UTF-8
CsMobileReviewPushJob.ftpBufferSize = ********
CsMobileReviewPushJob.ftpServerWorkingDirectory = /SUP
CsMobileReviewPushJob.ftpServerBackupsDirectory = /SUP

#ZSXH ftp 
ZSXHCsMobileReviewPushJob.ftpServerIp = **********
ZSXHCsMobileReviewPushJob.ftpUserName = xzsxhzj
ZSXHCsMobileReviewPushJob.ftpPassword = eingee9eiV
ZSXHCsMobileReviewPushJob.ftpServerPort = 1358
ZSXHCsMobileReviewPushJob.ftpEncoding = UTF-8
ZSXHCsMobileReviewPushJob.ftpBufferSize = ********
ZSXHCsMobileReviewPushJob.ftpServerWorkingDirectory = /XZSXH/ZLBC/SENDFILES/
ZSXHCsMobileReviewPushJob.ftpServerBackupsDirectory = /XZSXH/ZLBC/BACKUPS/ 

#143327 suixintong msg ftp 
CSSendCSEffectMsg.ftpServerIp = ***********
CSSendCSEffectMsg.ftpUserName = ftpuser
CSSendCSEffectMsg.ftpPassword = FTP@user135
CSSendCSEffectMsg.ftpServerPort = 21
CSSendCSEffectMsg.ftpEncoding = UTF-8
CSSendCSEffectMsg.ftpBufferSize = ********
CSSendCSEffectMsg.ftpServerWorkingDirectory = /
CSSendCSEffectMsg.ftpServerBackupsDirectory = /
CSSendCSEffectMsgTextFtp.ftpUserName = YYGLGXHX
CSSendCSEffectMsgTextFtp.ftpPassword = YYGLGXHX
CsPrintResultFtp.ftpServerIp = **********
CsPrintResultFtp.ftpUserName = ftpadmin
CsPrintResultFtp.ftpPassword = uoPz8Mmegv
CsPrintResultFtp.ftpServerPort = 1358
CsPrintResultFtp.ftpEncoding = UTF-8
CsPrintResultFtp.ftpBufferSize = ********
CsPrintResultFtp.ftpServerWorkingDirectory = /ReturnMessage/
CsPrintResultFtp.ftpServerBackupsDirectory = /ReturnMessage_Backup/

#yanglaojinchengbaosong
bocicpolicyinfosendservice.ServerIp = http://1***********:8080/SYGRYLJ/OpenYbxAccController/insuranceAcceptance
bocicpolicyinfosendservice.UserName = 001LT_XHRSBX
bocicpolicyinfosendservice.Password = 001LT_XHRSBXPWD


TelCenterPolicyInfoJobConfig.ftpServerIp = **********
TelCenterPolicyInfoJobConfig.ftpUserName = phonecenterzj
TelCenterPolicyInfoJobConfig.ftpPassword = KTSkWHL634
TelCenterPolicyInfoJobConfig.ftpServerPort = 1358
TelCenterPolicyInfoJobConfig.ftpEncoding = UTF-8
TelCenterPolicyInfoJobConfig.ftpBufferSize = ********
TelCenterPolicyInfoJobConfig.ftpServerWorkingDirectory = /policyLoanOverdueInfo/send
TelCenterPolicyInfoJobConfig.ftpServerBackupsDirectory = /policyLoanOverdueInfo/bak_send

## 142944 
documentPDFFile.ftpServerIp = **********
documentPDFFile.ftpUserName = ftpadmin
documentPDFFile.ftpPassword = uoPz8Mmegv
documentPDFFile.ftpServerPort = 1358
documentPDFFile.ftpEncoding = UTF-8
documentPDFFile.ftpBufferSize = ********
documentPDFFile.ftpServerUploadDirectory =  /PrintSystem/LtrORTIMG/


trustSenRenewalFtpServerConfig.ftpServerIp = **********
trustSenRenewalFtpServerConfig.ftpServerPort = 1358
trustSenRenewalFtpServerConfig.ftpUserName = channelzj
trustSenRenewalFtpServerConfig.ftpPassword = Mfr4v6mLW6
trustSenRenewalFtpServerConfig.ftpEncoding = UTF-8
trustSenRenewalFtpServerConfig.ftpBufferSize = ********
trustSenRenewalFtpServerConfig.ftpServerWorkingDirectory = /PA_TSRN/

#END023
PolicyStatusSubmitServiceEnd023.url = http://1***********:8080/datahub.ws/services/policyStatusChange
PolicyStatusSubmitServiceEnd023.namespace = http://www.ciitc.com.cn/healthy
PolicyStatusSubmitServiceEnd023.method = statusChange

#END024
PolicyStatusSubmitServiceEnd024.url = http://1***********:8080/datahub.ws/services/asynResultQuery
PolicyStatusSubmitServiceEnd024.namespace = http://www.ciitc.com.cn/healthy
PolicyStatusSubmitServiceEnd024.method = asynResultQuery


#143338 
policyFlagWithAllocationFtpServerConfig.ftpServerIp = **********
policyFlagWithAllocationFtpServerConfig.ftpServerPort = 1358
policyFlagWithAllocationFtpServerConfig.ftpUserName = channelzj
policyFlagWithAllocationFtpServerConfig.ftpPassword = Mfr4v6mLW6
policyFlagWithAllocationFtpServerConfig.ftpEncoding = UTF-8
policyFlagWithAllocationFtpServerConfig.ftpBufferSize = ********
policyFlagWithAllocationFtpServerConfig.ftpServerWorkingDirectory = /pa/channel/FpdPolicy/send/
policyFlagWithAllocationFtpServerConfig.ftpServerBackupsDirectory = /pa/channel/FpdPolicy/send_bak/

#END021
PreservationInformationUpload.url = http://1***********:8080/datahub.ws/services/endorsement
PreservationInformationUpload.namespace = http://www.ciitc.com.cn/healthy
PreservationInformationUpload.method = endorsementService

#END025
endoObtainConfirmNo.url = http://1***********:8080/datahub.ws/services/endoObtainConfirmNoService
endoObtainConfirmNo.namespace = http://www.ciitc.com.cn/healthy
endoObtainConfirmNo.method = transformOut

#END026
policyHolderChange.url = http://1***********:8080/datahub.ws/services/policyHolderChange
policyHolderChange.namespace = http://www.ciitc.com.cn/healthy
policyHolderChange.method = policyHolderChangeService

#CHK011
CSTaxComparisonService.url = http://1***********:8080/datahub.ws/services/businessCheck
CSTaxComparisonService.namespace = http://www.ciitc.com.cn/healthy
CSTaxComparisonService.method = businessCheck

#PRM013\#PRM014
TaxPremiumPolicyRenewalCancelService.url = http://1***********:8080/datahub.ws/services/asynResultQuery
TaxPremiumPolicyRenewalCancelService.namespace = http://www.ciitc.com.cn/healthy
TaxPremiumPolicyRenewalCancelService.method = asynResultQuery

#END022
#ç¨ä¼ä¿åä¿å¨æ¤éä¸ä¼ 
CancelUpload.url = http://1***********:8080/datahub.ws/services/endorsementCancel
CancelUpload.namespace = http://www.ciitc.com.cn/healthy
CancelUpload.method = endorsementCancel

#PTY011
#ç¨ä¼ä¿åè¢«ä¿äººå®¢æ·èº«ä»½éªè¯æ¥é
CsINSChgUpload.url = http://1***********:8080/datahub.ws.customer/services/customerVerify
CsINSChgUpload.namespace = http://www.ciitc.com.cn/healthy
CsINSChgUpload.method = verify

#PTY012
#è¢«ä¿äººå®¢æ·èº«ä»½éªè¯ç»æå¼æ­¥æ¥è¯¢
CsINSChgQueryUpload.url = http://1***********:8080/datahub.ws/services/asynResultQuery
CsINSChgQueryUpload.namespace = http://www.ciitc.com.cn/healthy
CsINSChgQueryUpload.method = asynResultQuery

#PA-PRM011
PAPRM011policyPremium.url = http://1***********:8080/datahub.ws/services/policyPremium
PAPRM011policyPremium.namespace = http://www.ciitc.com.cn/healthy
PAPRM011policyPremium.method = premium

#PA-RNW011
PARNW011policyRenewal.url = http://1***********:8080/datahub.ws/services/policyRenewal
PARNW011policyRenewal.namespace = http://www.ciitc.com.cn/healthy
PARNW011policyRenewal.method = renewal

#PA-CHK011
PACHK011businessCheck.url = http://1***********:8080/datahub.ws/services/businessCheck
PACHK011businessCheck.namespace = http://www.ciitc.com.cn/healthy
PACHK011businessCheck.method = businessCheck

#PA-PRM012+RNW012
PAasynResultQuery.url = http://1***********:8080/datahub.ws/services/asynResultQuery
PAasynResultQuery.namespace = http://www.ciitc.com.cn/healthy
PAasynResultQuery.method = asynResultQuery
    
#PRM013\#PRM014
TaxPremiumPolicyRenewalCancelService.asynurl = http://1***********:8080/datahub.ws/services/asynResultQuery
TaxPremiumPolicyRenewalCancelService.namespace = http://www.ciitc.com.cn/healthy
TaxPremiumPolicyRenewalCancelService.asynmethod = asynResultQuery
TaxPremiumPolicyRenewalCancelService.url = http://1***********:8080/datahub.ws/services/renewalCancel
TaxPremiumPolicyRenewalCancelService.method = renewal
TaxPremiumPolicyRenewalCancelService.username = syXHRS001
TaxPremiumPolicyRenewalCancelService.password = Xhrs#240104

#feiShiShiTiShu
pqdSendJson.ftpServerIp = **********
pqdSendJson.ftpUserName = pqdzj
pqdSendJson.ftpPassword = cJOPD7GIa2
pqdSendJson.ftpServerPort = 1358
pqdSendJson.ftpEncoding = UTF-8
pqdSendJson.ftpBufferSize = ********
pqdSendJson.ftpServerWorkingDirectory = /pa/policyquerydelayed/
pqdSendJson.ftpServerBackupsDirectory = /pa/policyquerydelayed/

#rm:162358
imageInfoBatch.ftpServerIp = ***************
imageInfoBatch.ftpServerPort = 21
imageInfoBatch.ftpUserName = imagezj
imageInfoBatch.ftpPassword = imagezj123
imageInfoBatch.ftpEncoding = UTF-8
#å½±åä¿¡æ¯è¯»åç®å½
imageInfoBatch.bqsend = /image/bqsend
#å½±åä¿¡æ¯å¤ä»½ç®å½
imageInfoBatch.bqreturn = /image/bqreturn

policyLockFlagFtpServerConfig.ftpServerIp = **********
policyLockFlagFtpServerConfig.ftpServerPort = 1358
policyLockFlagFtpServerConfig.ftpUserName = channelzj
policyLockFlagFtpServerConfig.ftpPassword = Mfr4v6mLW6
policyLockFlagFtpServerConfig.ftpEncoding = UTF-8
policyLockFlagFtpServerConfig.ftpBufferSize = ********
policyLockFlagFtpServerConfig.ftpServerWorkingDirectory = /pa/channel/SdPolicy/send
policyLockFlagFtpServerConfig.ftpServerBackupsDirectory = /pa/channel/SdPolicy/send_bak

#173483_175535 myb ct effect ftp 
CsSurrenderMybFTP.ftpServerIp = **********
CsSurrenderMybFTP.ftpUserName = zjywptzj
CsSurrenderMybFTP.ftpPassword = aLoo2Cae
CsSurrenderMybFTP.ftpServerPort = 1358
CsSurrenderMybFTP.ftpEncoding = UTF-8
CsSurrenderMybFTP.ftpBufferSize = ********
CsSurrenderMybFTP.ftpServerWorkingDirectory = /pa/zjywpt/myb/send/
CsSurrenderMybFTP.ftpServerBackupsDirectory = /pa/zjywpt/myb/bak/

#rm:164150
#éä¿ä¿¡æ¯åæ­¥æ¥å£å°å
csSurrenderInfo.url = http://***************:8080/hzins-scheduler-api/ds/newCancelSync/xhrs
#éä¿ä¿¡æ¯åæ­¥æ¥å£å å¯å¯é¥
csSurrenderInfo.encryptKey = f12Ra30UBn13fPTI

#èèä¿éä¿ä¿¡æ¯åæ­¥æ¥å£å°å
csSurrenderInfoForMYB.url = http://***************:8080/ANTVBX41/policyEndorseOperateInfoSync
#èèä¿éä¿ä¿¡æ¯åæ­¥cid
csSurrenderInfoForMYB.cid = OYFXGFYXGSSZFGS
#èèä¿éä¿ä¿¡æ¯åæ­¥appid
csSurrenderInfoForMYB.appId = OYFXGFYXGSSZFGS41
#å¯ç äºæå¡å¹³å° çæéå¯¹ç§°å¯é¥ è°ç¨å°å
passwordService.generateKeyPair = http://***********:10006/chiron/v1/system/generateKeyPair
#å¯ç äºæå¡å¹³å° PKCS1 åæç­¾å è°ç¨å°å
passwordService_PKCS1.signData = http://***********:10006/dsvs/v1/pkcs1/signData
#å¯ç äºæå¡å¹³å° PKCS1 åæéªç­¾ è°ç¨å°å
passwordService_PKCS1.verifyDataSign = http://***********:10006/dsvs/v1/pkcs1/verifyDataSign

#rm176781 KZGM URL, MMY IP
passwordcloudService.generateMasterKey = http://***********:10006/chiron/v1/system/createKey

passwordcloudService.hostedDataKey = http://***********:10006/dataKey/enroll

passwordcloudService.hostedDataKeyEncryption = http://***********:10006/dataKey/encrypt

passwordcloudService.appID = APP_FD20E201DA9941C39869A0544447D121


passwordcloudService.kzgmUrl = https://***************:443/vpm/policyNumVideo.html?param=

#179532
#æ¢§æ¡æ éåä¿å¯é¥
CsSurrenderWtsServiceImpl.encryptKeyForWTS = D88B3477BDA02B3A
#æ¢§æ¡æ éä¿å°å
CsSurrenderWtsServiceImpl.urlForCT = http://***************:8080/bus/return/xhrs/drop
#æ¢§æ¡æ åä¿å°å
CsSurrenderWtsServiceImpl.urlForPT = http://***************:8080/bus/return/xhrs/edor

QualitDetalListJob.ftpServerIp = **********
QualitDetalListJob.ftpUserName = listuser
QualitDetalListJob.ftpPassword = mMpYH8bWj3
QualitDetalListJob.ftpServerPort = 1358
QualitDetalListJob.ftpEncoding = UTF-8
QualitDetalListJob.ftpBufferSize = ********
QualitDetalListJob.ftpServerWorkingDirectory = /ReportFile/

#184302
#ä¿ééåä¿å¯é¥
CsSurrenderWtsServiceImpl.encryptKeyForBT = D88B3477BDA02B3A
#ä¿ééä¿å°å
CsSurrenderWtsServiceImpl.urlForBTCT = http://***************:8080/open/v1/xhrsSurrender/surrender
#ä¿éåä¿å°å
CsSurrenderWtsServiceImpl.urlForBTPT = http://***************:8080/open/v1/xhrsjbSurrender/surrender

#ç¬¬ä¸æ¹å¹³å°éä¿å¯é¥
CsSurrenderWtsServiceImpl.encryptKeyForCT = f12Ra30UBn13fPTI
#ç¬¬ä¸æ¹å¹³å°åä¿å¯é¥
CsSurrenderWtsServiceImpl.encryptKeyForPT = D88B3477BDA02B3A

#å¾®æéä¿å°å
CsSurrenderWtsServiceImpl.urlForWYCT = http://***************:8080/thridparty/callback/xinHuaRevokeNotify
#å¾®æåä¿å°å
CsSurrenderWtsServiceImpl.urlForWYPT = http://***************:8080/thridparty/callback/xinHuaReductionRevokeNotify

#åä¿¡éåä¿å°å
CsSurrenderWtsServiceImpl.urlForCXCPT = http://***************:8080/Callback/NewChinaLifeRefundNotify

CsPermissionCancelService.pageQueryUsersByTime = http://10.1.102.64:81/ngiam-rst/v1/api/nclid/pageQueryUsersByTime
CsPermissionCancelService.messagesThirdNotify = http://10.1.102.64:81/ngiam-rst/v1/sdk/messages/third/notify
CsPermissionCancelService.authorization = MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCoDZIY5cVum+eXi8enFw/eB80jGY0OYFlqeYURtzv+Vyg4q6mCSxRlQY3toa84KvwWG3WkpNYCIJIQAnPUvLcrO4liSEYVRCCIYvZe1gt8QDwpqGIVxFjr/rwPnohJCowUqPD5olglMEL4K7ynnlK9agKMzeCLiT49glx/+Hr6EQIDAQAB
