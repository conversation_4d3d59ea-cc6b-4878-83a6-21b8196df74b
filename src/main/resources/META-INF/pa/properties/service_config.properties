#éæ©æ¨¡å¼
#service.mode=service.default
#service.mode=service.mode2
service.mode=service.mode1
#service.mode=service.mode3
#service.mode=service.mode4
#service.mode=service.mode5

#service.defaultä¸ºæ¬å°å¼åç¯å¢ï¼ServiceRosterå½åä¸ºlocalServiceRoster.xml
service.default=classpath*:META-INF/pa/properties/localServiceRoster.xml
#service.mode1ä¸ºå¼åèè°ç¯å¢ï¼ServiceRosterå½åä¸ºutServiceRoster.xml
service.mode1=classpath*:META-INF/pa/properties/utServiceRoster.xml
#service.mode2ä¸ºSITç¯å¢ï¼ServiceRosterå½åä¸ºsitServiceRoster.xml
service.mode2=classpath*:META-INF/pa/properties/sitServiceRoster.xml
#service.mode3ä¸ºçäº§ç¯å¢ï¼ServiceRosterå½åä¸ºprdServiceRoster.xml
service.mode3=classpath*:META-INF/pa/properties/prdServiceRoster.xml
#service.mode4ä¸ºç¯å¢ï¼ServiceRosterå½åä¸ºdeServiceRoster.xml
service.mode4=classpath*:META-INF/pa/properties/deServiceRoster.xml
#service.mode5ä¸ºç¯å¢ï¼ServiceRosterå½åä¸ºdfServiceRoster.xml
service.mode5=classpath*:META-INF/pa/properties/dfServiceRoster.xml

