<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.InvestPriceDayDaoImpl">

	<sql id="PA_investPriceDayWhereCondition">
		<if test=" cost_policy_unit  != null "><![CDATA[ AND A.COST_POLICY_UNIT = #{cost_policy_unit} ]]></if>
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" price_num  != null "><![CDATA[ AND A.PRICE_NUM = #{price_num} ]]></if>
		<if test=" init_invest_unit  != null "><![CDATA[ AND A.INIT_INVEST_UNIT = #{init_invest_unit} ]]></if>
		<if test=" cost_invest_unit  != null "><![CDATA[ AND A.COST_INVEST_UNIT = #{cost_invest_unit} ]]></if>
		<if test=" curr_balance  != null "><![CDATA[ AND A.CURR_BALANCE = #{curr_balance} ]]></if>
		<if test=" print_date  != null  and  print_date  != ''  "><![CDATA[ AND A.PRINT_DATE = #{print_date} ]]></if>
		<if test=" last_invest_unit  != null "><![CDATA[ AND A.LAST_INVEST_UNIT = #{last_invest_unit} ]]></if>
		<if test=" off_price  != null "><![CDATA[ AND A.OFF_PRICE = #{off_price} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" price_date  != null  and  price_date  != ''  "><![CDATA[ AND A.PRICE_DATE = #{price_date} ]]></if>
		<if test=" bid_price  != null "><![CDATA[ AND A.BID_PRICE = #{bid_price} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.PRICE_DATE = 
		(SELECT MAX(Z.PRICE_DATE) FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY Z WHERE   
		Z.PRICE_DATE <= #{end_date} ]]>
		 <if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND Z.FUND_CODE = #{fund_code} ]]></if>
		 <if test=" invest_id  != null "><![CDATA[ AND Z.INVEST_ID = #{invest_id} ]]></if>
		      <![CDATA[ )  ]]>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryInvestPriceDayByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryInvestPriceDayByInvestIdCondition">
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
	</sql>	
	<sql id="PA_queryInvestPriceDayByPriceNumCondition">
		<if test=" price_num  != null "><![CDATA[ AND A.PRICE_NUM = #{price_num} ]]></if>
	</sql>	
	<sql id="PA_queryInvestPriceDayByPrintDateCondition">
		<if test=" print_date  != null "><![CDATA[ AND A.PRINT_DATE = #{print_date} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addInvestPriceDay"  useGeneratedKeys="true"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_INVEST_PRICE_DAY__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INVEST_PRICE_DAY(
				COST_POLICY_UNIT, INVEST_ID, INSERT_TIME, FUND_CODE, UPDATE_TIME, PRICE_NUM, INIT_INVEST_UNIT, 
				COST_INVEST_UNIT, INSERT_TIMESTAMP, CURR_BALANCE, PRINT_DATE, LAST_INVEST_UNIT, UPDATE_BY, OFF_PRICE, 
				LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, PRICE_DATE, BID_PRICE ) 
			VALUES (
				#{cost_policy_unit, jdbcType=NUMERIC}, #{invest_id, jdbcType=NUMERIC} , SYSDATE , #{fund_code, jdbcType=VARCHAR} , SYSDATE , #{price_num, jdbcType=NUMERIC} , #{init_invest_unit, jdbcType=NUMERIC} 
				, #{cost_invest_unit, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{curr_balance, jdbcType=NUMERIC} , #{print_date, jdbcType=DATE} , #{last_invest_unit, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{off_price, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{price_date, jdbcType=DATE} , #{bid_price, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteInvestPriceDay" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateInvestPriceDay" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_PRICE_DAY ]]>
		<set>
		<trim suffixOverrides=",">
		    COST_POLICY_UNIT = #{cost_policy_unit, jdbcType=NUMERIC} ,
		    INVEST_ID = #{invest_id, jdbcType=NUMERIC} ,
			FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    PRICE_NUM = #{price_num, jdbcType=NUMERIC} ,
		    INIT_INVEST_UNIT = #{init_invest_unit, jdbcType=NUMERIC} ,
		    COST_INVEST_UNIT = #{cost_invest_unit, jdbcType=NUMERIC} ,
		    CURR_BALANCE = #{curr_balance, jdbcType=NUMERIC} ,
		    PRINT_DATE = #{print_date, jdbcType=DATE} ,
		    LAST_INVEST_UNIT = #{last_invest_unit, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    OFF_PRICE = #{off_price, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    PRICE_DATE = #{price_date, jdbcType=DATE} ,
		    BID_PRICE = #{bid_price, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findInvestPriceDayByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_POLICY_UNIT, A.INVEST_ID, A.FUND_CODE, A.PRICE_NUM, A.INIT_INVEST_UNIT, 
			A.COST_INVEST_UNIT, A.CURR_BALANCE, A.PRINT_DATE, A.LAST_INVEST_UNIT, A.OFF_PRICE, 
			A.LIST_ID, A.PRICE_DATE, A.BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE 1 = 1  ]]>
		<include refid="PA_queryInvestPriceDayByListIdCondition" />
		<![CDATA[ ORDER BY A.PRICE_DATE DESC ]]>
	</select>
	
	<select id="PA_findInvestPriceDayByInvestId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_POLICY_UNIT, A.INVEST_ID, A.FUND_CODE, A.PRICE_NUM, A.INIT_INVEST_UNIT, 
			A.COST_INVEST_UNIT, A.CURR_BALANCE, A.PRINT_DATE, A.LAST_INVEST_UNIT, A.OFF_PRICE, 
			A.LIST_ID, A.PRICE_DATE, A.BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE 1 = 1  ]]>
		<include refid="PA_queryInvestPriceDayByInvestIdCondition" />
		<![CDATA[ ORDER BY A.PRICE_DATE DESC ]]>
	</select>
	
	<select id="PA_findInvestPriceDayByPriceNum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_POLICY_UNIT, A.INVEST_ID, A.FUND_CODE, A.PRICE_NUM, A.INIT_INVEST_UNIT, 
			A.COST_INVEST_UNIT, A.CURR_BALANCE, A.PRINT_DATE, A.LAST_INVEST_UNIT, A.OFF_PRICE, 
			A.LIST_ID, A.PRICE_DATE, A.BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE 1 = 1  ]]>
		<include refid="PA_queryInvestPriceDayByPriceNumCondition" />
		<![CDATA[ ORDER BY A.PRICE_DATE DESC ]]>
	</select>
	
	<select id="PA_findInvestPriceDayByPrintDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_POLICY_UNIT, A.INVEST_ID, A.FUND_CODE, A.PRICE_NUM, A.INIT_INVEST_UNIT, 
			A.COST_INVEST_UNIT, A.CURR_BALANCE, A.PRINT_DATE, A.LAST_INVEST_UNIT, A.OFF_PRICE, 
			A.LIST_ID, A.PRICE_DATE, A.BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE 1 = 1  ]]>
		<include refid="PA_queryInvestPriceDayByPrintDateCondition" />
		<![CDATA[ ORDER BY A.PRICE_DATE DESC ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapInvestPriceDay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_POLICY_UNIT, A.INVEST_ID, A.FUND_CODE, A.PRICE_NUM, A.INIT_INVEST_UNIT, 
			A.COST_INVEST_UNIT, A.CURR_BALANCE, A.PRINT_DATE, A.LAST_INVEST_UNIT, A.OFF_PRICE, 
			A.LIST_ID, A.PRICE_DATE, A.BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_investPriceDayWhereCondition" />
		<![CDATA[ ORDER BY A.PRICE_DATE DESC ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllInvestPriceDay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_POLICY_UNIT, A.INVEST_ID, A.FUND_CODE, A.PRICE_NUM, A.INIT_INVEST_UNIT, 
			A.COST_INVEST_UNIT, A.CURR_BALANCE, A.PRINT_DATE, A.LAST_INVEST_UNIT, A.OFF_PRICE, 
			A.LIST_ID, A.PRICE_DATE, A.BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE 1 = 1  ]]>
		<include refid="PA_investPriceDayWhereCondition" />
		<![CDATA[ ORDER BY A.PRICE_DATE DESC ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findInvestPriceDayTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE 1 = 1  ]]>
		<include refid="PA_investPriceDayWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryInvestPriceDayForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.COST_POLICY_UNIT, B.INVEST_ID, B.FUND_CODE, B.PRICE_NUM, B.INIT_INVEST_UNIT, 
			B.COST_INVEST_UNIT, B.CURR_BALANCE, B.PRINT_DATE, B.LAST_INVEST_UNIT, B.OFF_PRICE, 
			B.LIST_ID, B.PRICE_DATE, B.BID_PRICE FROM (
					SELECT ROWNUM RN, A.COST_POLICY_UNIT, A.INVEST_ID, A.FUND_CODE, A.PRICE_NUM, A.INIT_INVEST_UNIT, 
			A.COST_INVEST_UNIT, A.CURR_BALANCE, A.PRINT_DATE, A.LAST_INVEST_UNIT, A.OFF_PRICE, 
			A.LIST_ID, A.PRICE_DATE, A.BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_PRICE_DAY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_investPriceDayWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
