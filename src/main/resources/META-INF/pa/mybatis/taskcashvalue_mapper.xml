<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.TaskCashValueDaoImpl">

    <!-- 添加操作 -->
	<insert id="PA_addTaskCashValue"  useGeneratedKeys="false"  parameterType="java.util.Map">
				<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TASK_CASH_VALUE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TASK_CASH_VALUE(
				LIST_ID,BATCH_NO,FILE_NAME,BUSI_TYPE,BUSI_NO,ORGAN_CODE,APPLY_CODE,POLICY_CODE,PROCESS_STATUS,PROCESS_TIMES,
				FIELD_1,FIELD_2,FIELD_3,FIELD_4,FIELD_5,FIELD_6,FIELD_7,FIELD_8,FIELD_9,FIELD_10,INSERT_BY,UPDATE_BY,
				INSERT_TIME,INSERT_TIMESTAMP,UPDATE_TIME,UPDATE_TIMESTAMP) 
			VALUES (
			    #{list_id, jdbcType=NUMERIC},#{batch_no, jdbcType=VARCHAR},#{file_name, jdbcType=VARCHAR},#{busi_type, jdbcType=VARCHAR},
			    #{busi_no, jdbcType=VARCHAR},#{organ_code, jdbcType=VARCHAR},#{apply_code, jdbcType=VARCHAR},#{policy_code, jdbcType=VARCHAR},
			    #{process_status, jdbcType=VARCHAR},#{process_times, jdbcType=NUMERIC},#{field_1, jdbcType=VARCHAR},#{field_2, jdbcType=VARCHAR},
			    #{field_3, jdbcType=VARCHAR},#{field_4, jdbcType=VARCHAR},#{field_5, jdbcType=VARCHAR},#{field_6, jdbcType=VARCHAR},
			    #{field_7, jdbcType=VARCHAR},#{field_8, jdbcType=VARCHAR},#{field_9, jdbcType=VARCHAR},#{field_10, jdbcType=VARCHAR},
				#{insert_by, jdbcType=NUMERIC},
				#{update_by, jdbcType=NUMERIC},
				SYSDATE,
				CURRENT_TIMESTAMP,
				SYSDATE,
				CURRENT_TIMESTAMP
				)
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="PA_deleteTaskCashValue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TASK_CASH_VALUE WHERE LIST_ID=#{list_id} ]]>
	</delete>
	
	<!-- 修改操作 -->
	<update id="PA_updateTaskCashProcessStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TASK_CASH_VALUE ]]>
		<set>
			<trim suffixOverrides=",">
			PROCESS_STATUS = #{process_status, jdbcType=VARCHAR} ,
			PROCESS_TIMES = #{process_times, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID=#{list_id} ]]>
	</update>
	
	<!-- 批处理查询 总记录数 -->
	<select id="PA_queryCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TASK_CASH_VALUE
        ]]>
	</select>
	
	<!-- 取模查询保单 -->
	<select id="PA_queryPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT ROWNUM,
		   TTCV.LIST_ID,
	       TTCV.BATCH_NO,
	       TTCV.FILE_NAME,
	       TTCV.BUSI_TYPE,
	       TTCV.BUSI_NO,
	       TTCV.ORGAN_CODE,
	       TTCV.APPLY_CODE,
	       TTCV.POLICY_CODE,
	       TTCV.PROCESS_STATUS,
	       TTCV.PROCESS_TIMES,
	       TTCV.FIELD_1,
	       TTCV.FIELD_2
	  FROM APP___PAS__DBUSER.T_TASK_CASH_VALUE TTCV
	  WHERE  1=1 AND MOD(TTCV.LIST_ID,#{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
	</select>	
	
	<select id="PA_queryTaskCashValueList" resultType="java.util.Map" parameterType="java.util.Map">
		  SELECT A.POLICY_CODE,
                 A.IS_MASTER_ITEM,
                 A.ANNU_PAY_TYPE,
                 A.IS_PAUSE,
                 A.IS_SRP,
                 A.PROD_PKG_PLAN_CODE,
                 A.APPEND_PREM_AF,
                 A.IS_WAIVED,
                 A.INTEREST_MODE,
                 A.APPLY_CODE,
                 A.NORENEW_REASON,
                 A.ORGAN_CODE,
                 A.CHARGE_YEAR,
                 A.EXTRA_PREM_AF,
                 A.POLICY_ID,
                 A.INITIAL_AMOUNT,
                 A.CC_SA,
                 A.INITIAL_EXTRA_PREM_AF,
                 A.PAYOUT_RATE,
                 A.AMOUNT,
                 A.PAIDUP_DATE,
                 A.PAY_YEAR,
                 A.EXPIRY_DATE,
                 A.PAY_PERIOD,
                 A.LIABILITY_STATE,
                 A.COUNT_WAY,
                 A.IS_GIFT,
                 A.RERINSTATE_DATE,
                 A.ADDITIONAL_PREM_AF,
                 A.RENEW_DECISION,
                 A.VALIDATE_DATE,
                 A.BONUS_MODE,
                 A.BENEFIT_LEVEL,
                 A.PRODUCT_ID,
                 A.BONUS_SA,
                 A.PRODUCT_CODE,
                 A.APPLY_DATE,
                 A.INTEREST_FLAG,
                 A.COVERAGE_PERIOD,
                 A.ITEM_ID,
                 A.RENEWAL_EXTRA_PREM_AF,
                 A.WAIVER_END,
                 A.MATURITY_DATE,
                 A.BUSI_ITEM_ID,
                 A.HEALTH_SERVICE_FLAG,
                 A.LAPSE_DATE,
                 A.PAY_FREQ,
                 A.UNIT,
                 A.COVERAGE_YEAR,
                 A.END_CAUSE,
                 A.LAPSE_CAUSE,
                 A.RENEWAL_DISCNTED_PREM_AF,
                 A.TOTAL_PREM_AF,
                 A.DECISION_CODE,
                 A.PAUSE_DATE,
                 A.STD_PREM_AF,
                 A.CHARGE_PERIOD,
                 A.SUSPEND_DATE,
                 A.SUSPEND_CAUSE,
                 A.DEDUCTIBLE_FRANCHISE,
                 A.WAIVER_START,
                 A.PREM_FREQ,
                 A.INITIAL_DISCNT_PREM_AF,
                 A.BONUS_W_MODE,
                 A.LAST_BONUS_DATE,
                 TTCV.BATCH_NO,
                 TTCV.FIELD_2,
                 TTCV.FIELD_1,
                 TTCV.FILE_NAME,
                 TBP.PRODUCT_CATEGORY1,
                 ROWNUM
            FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A,
                 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBD,
                 APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP,
                 APP___PAS__DBUSER.T_TASK_CASH_VALUE TTCV
                 WHERE 1=1 
                 AND A.BUSI_ITEM_ID = TCBD.BUSI_ITEM_ID
                 AND TCBD.BUSI_PRD_ID=TBP.BUSINESS_PRD_ID
                 AND A.POLICY_CODE=TTCV.POLICY_CODE
				 AND TTCV.POLICY_CODE=#{policy_code}
				 AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CV_TX_INFO_MAIN TCTIM WHERE TCTIM.FILE_NAME = TTCV.FILE_NAME)
	</select>	
	
</mapper>
