<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IDocumentDao">

	<sql id="PA_documentWhereCondition">
		<if test=" buss_source_code != null and buss_source_code != ''  "><![CDATA[ AND A.BUSS_SOURCE_CODE = #{buss_source_code} ]]></if>
		<if test=" clob_id  != null "><![CDATA[ AND A.CLOB_ID = #{clob_id} ]]></if>
		<if test=" scan_by  != null "><![CDATA[ AND A.SCAN_BY = #{scan_by} ]]></if>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" print_time  != null  and  print_time  != ''  "><![CDATA[ AND A.PRINT_TIME = #{print_time} ]]></if>
		<if test=" create_time  != null  and  create_time  != ''  "><![CDATA[ AND A.CREATE_TIME = #{create_time} ]]></if>
		<if test=" send_by  != null "><![CDATA[ AND A.SEND_BY = #{send_by} ]]></if>
		<if test=" supplement_flag  != null "><![CDATA[ AND A.SUPPLEMENT_FLAG = #{supplement_flag} ]]></if>
		<if test=" send_time  != null  and  send_time  != ''  "><![CDATA[ AND A.SEND_TIME = #{send_time} ]]></if>
		<if test=" create_by  != null "><![CDATA[ AND A.CREATE_BY = #{create_by} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" is_link  != null "><![CDATA[ AND A.IS_LINK = #{is_link} ]]></if>
		<if test=" buss_id  != null "><![CDATA[ AND A.BUSS_ID = #{buss_id} ]]></if>
		<if test=" print_by  != null "><![CDATA[ AND A.PRINT_BY = #{print_by} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" overdue_time  != null  and  overdue_time  != ''  "><![CDATA[ AND A.OVERDUE_TIME = #{overdue_time} ]]></if>
		<if test=" close_by  != null "><![CDATA[ AND A.CLOSE_BY = #{close_by} ]]></if>
		<if test=" reply_time  != null  and  reply_time  != ''  "><![CDATA[ AND A.REPLY_TIME = #{reply_time} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" send_obj_type != null and send_obj_type != ''  "><![CDATA[ AND A.SEND_OBJ_TYPE = #{send_obj_type} ]]></if>
		<if test=" reply_remark != null and reply_remark != ''  "><![CDATA[ AND A.REPLY_REMARK = #{reply_remark} ]]></if>
		<if test=" document_name != null and document_name != ''  "><![CDATA[ AND A.DOCUMENT_NAME = #{document_name} ]]></if>
		<if test=" overdue_document_no != null and overdue_document_no != ''  "><![CDATA[ AND A.OVERDUE_DOCUMENT_NO = #{overdue_document_no} ]]></if>
		<if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" close_time  != null  and  close_time  != ''  "><![CDATA[ AND A.CLOSE_TIME = #{close_time} ]]></if>
		<if test=" send_obj_id != null and send_obj_id != ''  "><![CDATA[ AND A.SEND_OBJ_ID = #{send_obj_id} ]]></if>
		<if test=" reprint_times  != null "><![CDATA[ AND A.REPRINT_TIMES = #{reprint_times} ]]></if>
		<if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" is_merger  != null "><![CDATA[ AND A.IS_MERGER = #{is_merger} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" scan_time  != null  and  scan_time  != ''  "><![CDATA[ AND A.SCAN_TIME = #{scan_time} ]]></if>
		<if test=" reply_conclusion  != null "><![CDATA[ AND A.REPLY_CONCLUSION = #{reply_conclusion} ]]></if>
		<if test=" buss_code != null and buss_code != ''  "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
		<if test=" reply_by  != null "><![CDATA[ AND A.REPLY_BY = #{reply_by} ]]></if>
		<if test=" reply_days  != null "><![CDATA[ AND A.REPLY_DAYS = #{reply_days} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryDocumentByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</sql>	
	<sql id="PA_queryDocumentByDocumentNoCondition">
		<if test=" document_no != null and document_no != '' "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
	</sql>	
	<sql id="PA_queryDocumentByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryDocumentByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryDocumentByStatusCondition">
		<if test=" status != null and status != '' "><![CDATA[ AND A.STATUS = #{status} ]]></if>
	</sql>
	<sql id="PA_queryNoticeDataCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" print_time != null and print_time != '' "><![CDATA[ AND A.CREATE_TIME <=TO_DATE(TO_CHAR(#{print_time} ,'yyyy-MM-dd'),'yyyy-MM-dd')  ]]></if>
		<if test= "busi_prod_code != null and  busi_prod_code  != '' "><![CDATA[ AND  exists 
		(select t.policy_code from  APP___PAS__DBUSER.t_contract_busi_prod t where t.busi_prod_code  like '%${busi_prod_code}%' 
           and t.policy_code = A.policy_code) ]]></if>
        <if test=" modnum != null and modnum != '' and start != null and start != '' ">
		              <![CDATA[ and mod(A.doc_list_id,#{modnum}) = #{start} ]]></if>
	</sql>	
	<sql id="PA_queryNDocumentDataCondition">
		<!-- <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if> -->
		<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[AND A.ORGAN_CODE in (
		select t.organ_code
			  from APP___PAS__DBUSER.t_udmp_org_rel t
			 start with t.organ_code = #{organ_code}
			connect by prior t.organ_code = t.uporgan_code)  ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" buss_code != null and buss_code != ''  "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
		<if test=" send_time  != null  and  send_time  != ''  "><![CDATA[ AND A.SEND_TIME <= #{send_time} ]]></if>
		<if test=" status != null and status != '' "><![CDATA[ AND A.STATUS = #{status} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addDocument"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="doc_list_id">
			SELECT APP___PAS__DBUSER.S_DOCUMENT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_DOCUMENT(
				BUSS_SOURCE_CODE, CLOB_ID, SCAN_BY, DOC_LIST_ID, PRINT_TIME, CREATE_TIME, SEND_BY, 
				SUPPLEMENT_FLAG, SEND_TIME, CREATE_BY, STATUS, IS_LINK, BUSS_ID, PRINT_BY, 
				INSERT_TIMESTAMP, ORGAN_CODE, OVERDUE_TIME, UPDATE_BY, CLOSE_BY, REPLY_TIME, POLICY_ID, 
				SEND_OBJ_TYPE, REPLY_REMARK, DOCUMENT_NAME, OVERDUE_DOCUMENT_NO, INSERT_TIME, TEMPLATE_CODE, UPDATE_TIME, 
				CLOSE_TIME, SEND_OBJ_ID, REPRINT_TIMES, DOCUMENT_NO, IS_MERGER, POLICY_CODE, SCAN_TIME, 
				REPLY_CONCLUSION, BUSS_CODE, UPDATE_TIMESTAMP, REPLY_BY, INSERT_BY, REPLY_DAYS, BUSI_ITEM_ID, BUSI_PROD_CODE ) 
			VALUES (
				#{buss_source_code, jdbcType=VARCHAR}, #{clob_id, jdbcType=NUMERIC} , #{scan_by, jdbcType=NUMERIC} , #{doc_list_id, jdbcType=NUMERIC} , #{print_time, jdbcType=DATE} , #{create_time, jdbcType=DATE} , #{send_by, jdbcType=NUMERIC} 
				, #{supplement_flag, jdbcType=NUMERIC} , #{send_time, jdbcType=DATE} , #{create_by, jdbcType=NUMERIC} , #{status, jdbcType=VARCHAR} , #{is_link, jdbcType=NUMERIC} , #{buss_id, jdbcType=NUMERIC} , #{print_by, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{overdue_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{close_by, jdbcType=NUMERIC} , #{reply_time, jdbcType=DATE} , #{policy_id, jdbcType=NUMERIC} 
				, #{send_obj_type, jdbcType=VARCHAR} , #{reply_remark, jdbcType=VARCHAR} , #{document_name, jdbcType=VARCHAR} , #{overdue_document_no, jdbcType=VARCHAR} , SYSDATE , #{template_code, jdbcType=VARCHAR} , SYSDATE 
				, #{close_time, jdbcType=DATE} , #{send_obj_id, jdbcType=VARCHAR} , #{reprint_times, jdbcType=NUMERIC} , #{document_no, jdbcType=VARCHAR} , #{is_merger, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{scan_time, jdbcType=DATE} 
				, #{reply_conclusion, jdbcType=NUMERIC} , #{buss_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{reply_by, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{reply_days, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteDocument" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_DOCUMENT WHERE DOC_LIST_ID = #{doc_list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateDocument" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_DOCUMENT ]]>
		<set>
		<trim suffixOverrides=",">
			BUSS_SOURCE_CODE = #{buss_source_code, jdbcType=VARCHAR} ,
		    CLOB_ID = #{clob_id, jdbcType=NUMERIC} ,
		    SCAN_BY = #{scan_by, jdbcType=NUMERIC} ,
		    PRINT_TIME = #{print_time, jdbcType=DATE} ,
		    CREATE_TIME = #{create_time, jdbcType=DATE} ,
		    SEND_BY = #{send_by, jdbcType=NUMERIC} ,
		    SUPPLEMENT_FLAG = #{supplement_flag, jdbcType=NUMERIC} ,
		    SEND_TIME = #{send_time, jdbcType=DATE} ,
		    CREATE_BY = #{create_by, jdbcType=NUMERIC} ,
			STATUS = #{status, jdbcType=VARCHAR} ,
		    IS_LINK = #{is_link, jdbcType=NUMERIC} ,
		    BUSS_ID = #{buss_id, jdbcType=NUMERIC} ,
		    PRINT_BY = #{print_by, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    OVERDUE_TIME = #{overdue_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CLOSE_BY = #{close_by, jdbcType=NUMERIC} ,
		    REPLY_TIME = #{reply_time, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			SEND_OBJ_TYPE = #{send_obj_type, jdbcType=VARCHAR} ,
			REPLY_REMARK = #{reply_remark, jdbcType=VARCHAR} ,
			DOCUMENT_NAME = #{document_name, jdbcType=VARCHAR} ,
			OVERDUE_DOCUMENT_NO = #{overdue_document_no, jdbcType=VARCHAR} ,
			TEMPLATE_CODE = #{template_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    CLOSE_TIME = #{close_time, jdbcType=DATE} ,
			SEND_OBJ_ID = #{send_obj_id, jdbcType=VARCHAR} ,
		    REPRINT_TIMES = #{reprint_times, jdbcType=NUMERIC} ,
			DOCUMENT_NO = #{document_no, jdbcType=VARCHAR} ,
		    IS_MERGER = #{is_merger, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SCAN_TIME = #{scan_time, jdbcType=DATE} ,
		    REPLY_CONCLUSION = #{reply_conclusion, jdbcType=NUMERIC} ,
			BUSS_CODE = #{buss_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    REPLY_BY = #{reply_by, jdbcType=NUMERIC} ,
		    REPLY_DAYS = #{reply_days, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID =  #{busi_item_id, jdbcType=NUMERIC} , 
		    BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE DOC_LIST_ID = #{doc_list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findDocumentByDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryDocumentByDocListIdCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<!-- 查询全部 -->
	<select id="findAllDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE 
			FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="PA_documentWhereCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="PA_findDocumentByDocumentNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE 
			FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryDocumentByDocumentNoCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="PA_findDocumentByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE 
			FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryDocumentByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="PA_findDocumentByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE 
			FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryDocumentByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="PA_findDocumentByStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE 
			FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryDocumentByStatusCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE 
			FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE 
			FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]> 
	</select>


	<!-- 年度分红业绩报告书查询 接口 -->
	<select id="findDocumentByQueryYearBonusReport" resultType="java.util.Map"
		parameterType="java.util.Map">
	
	<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
	<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	<if test=" create_strat_time != null and create_end_time != null" >
		<![CDATA[ AND A.create_time > #{create_strat_time} and A.create_time < #{create_end_time} ]]>
	</if>	
	<if test=" template_code != null and template_code != '' "><![CDATA[ AND A.template_code = #{template_code} ]]></if>	
	
	</select>
<!-- 查询个数操作 -->
	<select id="PA_findDocumentTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="PA_documentWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryDocumentForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSS_SOURCE_CODE, B.CLOB_ID, B.SCAN_BY, B.DOC_LIST_ID, B.PRINT_TIME, B.CREATE_TIME, B.SEND_BY, 
			B.SUPPLEMENT_FLAG, B.SEND_TIME, B.CREATE_BY, B.STATUS, B.IS_LINK, B.BUSS_ID, B.PRINT_BY, 
			B.ORGAN_CODE, B.OVERDUE_TIME, B.CLOSE_BY, B.REPLY_TIME, B.POLICY_ID, 
			B.SEND_OBJ_TYPE, B.REPLY_REMARK, B.DOCUMENT_NAME, B.OVERDUE_DOCUMENT_NO, B.TEMPLATE_CODE, 
			B.CLOSE_TIME, B.SEND_OBJ_ID, B.REPRINT_TIMES, B.DOCUMENT_NO, B.IS_MERGER, B.POLICY_CODE, B.SCAN_TIME, 
			B.REPLY_CONCLUSION, B.BUSS_CODE, B.REPLY_BY, B.REPLY_DAYS FROM (
					SELECT ROWNUM RN, A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="queryDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.policy_code,a.CLOB_ID,a.BUSS_ID,a.DOCUMENT_NO,a.ORGAN_CODE 
			from APP___PAS__DBUSER.T_document a 
			where a.policy_code=#{policy_code} and a.template_code='PAS_00010'
			order by a.CLOB_ID DESC
		]]>
	</select>
	
	<select id="queryClobContent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.clob_id,a.content from APP___PAS__DBUSER.T_clob a where a.clob_id=#{clob_id}
		]]>
	</select>
	
	<select id="queryNoticeData" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	  SELECT ROWNUM RN ,A.CREATE_TIME,A.ORGAN_CODE,A.CLOB_ID ,A.POLICY_CODE
	  FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE A.CREATE_TIME IS NOT NULL
      AND A.CREATE_TIME >= TO_DATE(TO_CHAR(#{send_time} ,'yyyy-MM-dd'),'yyyy-MM-dd')
      AND A.TEMPLATE_CODE = #{template_code}
      AND A.CLOB_ID is not null
      ]]>
      <include refid="PA_queryNoticeDataCondition" />
	</select>
	<!-- 失效 queryNoticeDataSX-->
	<select id="queryNoticeDataSX" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	  SELECT A.CREATE_TIME,A.ORGAN_CODE,A.POLICY_CODE ,MAX(A.CLOB_ID) AS clob_id
	  FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE A.CREATE_TIME IS NOT NULL
	  AND A.CLOB_ID is not null
      AND A.CREATE_TIME >= #{send_time}
      AND A.TEMPLATE_CODE = #{template_code}
      ]]>
      <if test=" modnum != null and modnum != '' and start != null and start != '' ">
		              <![CDATA[ and mod(A.doc_list_id,#{modnum}) = #{start} ]]></if>
      <include refid="PA_queryNoticeDataCondition" />
     <![CDATA[  group by A.POLICY_CODE, A.CREATE_TIME, A.ORGAN_CODE ]]>
	</select>
	
	 <!--yangyl_wb 获得下一个通知书流水号生成给付通知书号 -->
	<select id="getDocumentSerialNum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT APP___PAS__DBUSER.S_DOCUMENT.NEXTVAL AS serial_number FROM DUAL ]]>
	</select>
	
	<!-- yangyl_wb查询可生成分红通知书数据条数 -->
	<select id="queryDocumentCounts" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT count(A.doc_list_id) as endnum FROM APP___PAS__DBUSER.T_DOCUMENT A   left join APP___PAS__DBUSER.t_contract_master tcm
			    on A.policy_code = tcm.policy_code WHERE 1 = 1
		<include refid="PA_queryNDocumentDataCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	<!-- yangyl_wb查询可生成分红通知书数据条数 -->
	<select id="queryDocumentList" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT ROWNUM RN, A.policy_code,
				(select to_char(tcbp.busi_item_id) from APP___PAS__DBUSER.T_Contract_busi_prod tcbp where tcbp.policy_code = A.policy_code  and (a.buss_code = tcbp.busi_item_id or a.buss_code = tcbp.master_busi_item_id) and tcbp.master_busi_item_id is null) buss_code,
				A.doc_list_id, A.send_time ,A.clob_id FROM APP___PAS__DBUSER.T_DOCUMENT A   left join APP___PAS__DBUSER.t_contract_master tcm
			    on A.policy_code = tcm.policy_code WHERE 1 = 1 and MOD(A.DOC_LIST_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
		<include refid="PA_queryNDocumentDataCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="queryOrganThridLvevlInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT O.ORGAN_CODE, O.ORGAN_NAME, O.ORGAN_ZIPCODE
		  FROM DEV_PAS.T_UDMP_ORG O
		 WHERE 1=1
		]]>
		<if test=" organ_code  != null and organ_code  != '' "><![CDATA[AND O.ORGAN_CODE = '${organ_code}']]></if> 
	</select>
	
	
	<select id="PA_findAllBonusDocumentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			
		  SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.SIGN_SOURCE
           FROM APP___PAS__DBUSER.T_DOCUMENT A 
          WHERE 1=1 
            
		]]>
		<if test=" template_code  != null and template_code  != '' "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code}]]></if> 
		<if test=" policy_code  != null and policy_code  != '' "><![CDATA[AND A.POLICY_CODE = #{policy_code}]]></if> 
		<if test=" buss_code  != null and buss_code  != '' "><![CDATA[ AND A.BUSS_CODE = #{buss_code,jdbcType=VARCHAR}]]></if> 
	    <if test=" year  != null and year  != '' "><![CDATA[AND TO_CHAR(A.CREATE_TIME,'YYYY') = #{year}]]></if> 
	    <if test=" document_no  != null and document_no  != '' "><![CDATA[AND A.DOCUMENT_NAME = #{document_name}]]></if> 
		
	</select>
	
	
		<select id="PA_findDocumentListInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		
	<![CDATA[ 
       SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
      A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
      A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
      A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
      A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
      A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS, A.SIGN_SOURCE,M.product_code_sys,M.product_name_sys
          FROM APP___PAS__DBUSER.V_DOCUMENT_ALL A ,
          APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD P, 
          APP___PAS__DBUSER.T_BUSINESS_PRODUCT M 
          WHERE 1=1 
          AND A.POLICY_ID = P.POLICY_ID
          AND M.BUSINESS_PRD_ID = P.BUSI_PRD_ID
    ]]>
    <if test=" template_code  != null and template_code  != '' and template_code  == 'PAS_00006'.toString()"><![CDATA[
     AND A.TEMPLATE_CODE = #{template_code}
     AND M.PRODUCT_CATEGORY1 IN ('20003','20004')
     ]]></if>
      <if test=" template_code  != null and template_code  != '' and template_code  == 'PAS_00014'.toString() "><![CDATA[
     AND A.TEMPLATE_CODE = #{template_code}
     AND M.PRODUCT_CATEGORY1 IN ('20003','20004')
     ]]></if>
     <if test=" template_code  != null and template_code  != '' and template_code  == 'PAS_00010'.toString()"><![CDATA[
     AND A.TEMPLATE_CODE = #{template_code}
     AND M.PRODUCT_CATEGORY1 IN ('20002')
     ]]></if>
     <if test=" template_code  != null and template_code  != '' and template_code  == 'PAS_00017'.toString() "><![CDATA[
     AND A.TEMPLATE_CODE = #{template_code}
     AND M.PRODUCT_CATEGORY1 IN ('20002')
     ]]></if> 
    <if test=" policy_code  != null and policy_code  != '' "><![CDATA[AND A.POLICY_CODE = #{policy_code}]]></if> 
    <if test=" doc_create_start_date  != null and doc_create_start_date  != '' "><![CDATA[AND A.CREATE_TIME >= TO_DATE(#{doc_create_start_date},'yyyy-MM-dd')]]></if> 
    <if test=" doc_create_end_date  != null and doc_create_end_date  != '' "><![CDATA[AND A.CREATE_TIME <= TO_DATE(#{doc_create_end_date},'yyyy-MM-dd')]]></if> 
	</select>
	
	
	<select id="PA_queryAccountValueLessNotice" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
    selECT ROWNUM RN, B.BUSI_PROD_CODE AS DOCUMENT_NAME,
       A.CREATE_TIME,
       A.POLICY_ID,
       A.POLICY_CODE
       FROM (SELECT A.*
          FROM DEV_PAS.T_DOCUMENT A
         WHERE 1 = 1 ]]>
           <if test=" template_code  != null and template_code  != '' "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code}]]></if> 
    <if test=" batch_date  != null and batch_date  != '' "><![CDATA[AND A.CREATE_TIME = TO_DATE(#{batch_date},'yyyy-MM-dd')]]></if>
    <if test=" modnum != null and modnum != '' and start != null and start != '' "><![CDATA[ and mod(A.POLICY_ID,#{modnum}) = #{start} ]]></if>
        <![CDATA[   ) A ]]>
  <![CDATA[ LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD B
    ON A.POLICY_CODE = B.POLICY_CODE
  LEFT JOIN DEV_PAS.T_CONTRACT_INVEST C
    ON A.POLICY_ID = C.POLICY_ID
 LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT D
    ON B.BUSI_PRD_ID = D.BUSINESS_PRD_ID
 WHERE 1 = 1
   AND D.PRODUCT_CATEGORY1 IN ('20003','20004')
   AND B.DUE_LAPSE_DATE IS NOT NULL
   AND (C.INTEREST_CAPITAL <= 0 OR C.ACCUM_UNITS <= 0) ]]>
	</select>
	
	
	
	<!-- 查询保单基础信息 -->
	<select id="PA_findAccountValueLessNoticeBasicData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.* FROM (
		        SELECT (SELECT A.OLD_CODE FROM APP___PAS__DBUSER.T_CODE_MAPPER A  WHERE A.CODETYPE='CHANNEL_TYPE' 
                           AND A.FROM_MODLE='PAS' AND A.NEW_CODE = TBC.CHANNEL_TYPE) CHANNEL_TYPE,
			           TN.BUSI_PRD_ID,
			           TCM.POLICY_CODE,
			           TC.CUSTOMER_NAME,
			           TCM.LIABILITY_STATE,
			           SYSDATE VALIDATE_DATE,
			           TC.CUSTOMER_GENDER,
			           TAAC.MOBILE_TEL MOBILE_TEL_BC,
			           TC.MOBILE_TEL MOBILE_TEL_TC,
			           TCM.ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 6) ZHI_ORGAN_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 6)) ZHI_ORGAN_CODE_NAME,
			           SUBSTR(TCM.ORGAN_CODE, 0, 4) BRANCH_CODE,
			           (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) BRANCH_CODE_NAME,
			           TC.OLD_CUSTOMER_ID CUSTOMER_ID,
			           TC.WECHAT_NO,
			           TC.EMAIL,
			           (SELECT A.OLD_CODE FROM APP___PAS__DBUSER.T_CODE_MAPPER A  WHERE A.CODETYPE='CHANNEL_TYPE' AND A.FROM_MODLE='PAS' AND A.NEW_CODE = TAC.AGENT_CHANNEL) AGENT_CHANNEL,
			           TAC.AGENT_CODE,
			           TAC.AGENT_NAME,
			           TAC.AGENT_MOBILE,
			           TAC.AGENT_STATUS
			      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
                 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
                 APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP,
                 APP___PAS__DBUSER.T_CUSTOMER           TC,
                 APP___PAS__DBUSER.T_ADDRESS            TAAC,
                 APP___PAS__DBUSER.T_POLICY_HOLDER      TIL,
                 APP___PAS__DBUSER.T_CONTRACT_AGENT     TBC,
                 APP___PAS__DBUSER.T_AGENT              TAC
           WHERE TCM.POLICY_CODE = TN.POLICY_CODE
             AND TCM.POLICY_CODE = TIL.POLICY_CODE
             AND TCM.POLICY_ID = TBC.POLICY_ID
             AND TBC.AGENT_CODE = TAC.AGENT_CODE
             AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
             AND TIL.ADDRESS_ID = TAAC.ADDRESS_ID
             AND TBC.IS_CURRENT_AGENT = '1' 
             AND TN.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
             AND BP.PRODUCT_CATEGORY='10001'   ]]>
   		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if> 
   		<![CDATA[ GROUP BY TBC.CHANNEL_TYPE, 
					  TC.CUSTOMER_NAME, 
					  TCM.POLICY_CODE,
					  TC.CUSTOMER_GENDER, 
					  TAAC.MOBILE_TEL, 
					  TC.MOBILE_TEL, 
					  TCM.ORGAN_CODE, 
					  TC.OLD_CUSTOMER_ID, 
					  TC.WECHAT_NO, 
					  TC.EMAIL, 
					  TN.BUSI_PRD_ID, 
					  TCM.LIABILITY_STATE, 
					  TAC.AGENT_CHANNEL, 
					  TAC.AGENT_CODE, 
					  TAC.AGENT_NAME, 
					  TAC.AGENT_MOBILE, 
					  TAC.AGENT_STATUS) A  ]]>
	</select>
	
	<!-- rm133929 根据保单号 客户号 保单状态 通知书类型 保单年度 查询通知书 -->
	<select id="PA_findAllDocumentByPolicyAndTemCode"  resultType="java.util.Map" parameterType="java.util.Map">
	        SELECT VD.POLICY_CODE,
              VD.TEMPLATE_CODE,
              VD.DOCUMENT_NO,
              VD.DOCUMENT_NAME,
              VD.CREATE_TIME,
              TCM.LIABILITY_STATE,
              TCM.VALIDATE_DATE,
              (SELECT TC.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER TC JOIN DEV_PAS.T_POLICY_HOLDER TPH ON  
              TC.CUSTOMER_ID = TPH.CUSTOMER_ID WHERE TPH.POLICY_CODE=TCM.POLICY_CODE) AS POLICYHOLDER_NAME   
         FROM DEV_PAS.V_DOCUMENT_ALL VD
        INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM  ON VD.POLICY_ID = TCM.POLICY_ID
        WHERE 1=1
       AND VD.POLICY_CODE= #{policy_code} 
       AND TCM.LIABILITY_STATE= #{policyState}
       <if test=" templatecode_list != null and templatecode_list != ''  ">
		<![CDATA[ AND VD.TEMPLATE_CODE IN  ]]>
				<foreach collection="templatecode_list" item="templatecode_list" 
					index="index" open="(" close=")" separator=",">#{templatecode_list}
				</foreach>
		</if>
       ORDER BY VD.CREATE_TIME DESC,VD.DOC_LIST_ID DESC
	</select>
	
	<!-- 查询满足条件的保单数量 -->
	<select id="PA_findCountDocumentByPolicyAndTemCodeToTal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  SELECT COUNT(1) FROM (
			SELECT VD.POLICY_CODE FROM DEV_PAS.V_DOCUMENT_ALL VD
          					INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
             						ON VD.POLICY_ID = TCM.POLICY_ID  WHERE  1=1 ]]>
          <![CDATA[  AND VD.POLICY_CODE IN  ]]>
           <if test=" policyCode_list != null and policyCode_list != ''  ">
           		<foreach collection="policyCode_list" item="policyCode_list" 
					index="index" open="(" close=")" separator=",">#{policyCode_list}
		  		</foreach>
		   </if>
            AND TCM.LIABILITY_STATE = #{policyState}
           <if test=" templatecode_list != null and templatecode_list != ''  ">
				<![CDATA[ AND VD.TEMPLATE_CODE IN  ]]>
				<foreach collection="templatecode_list" item="templatecode_list" 
					index="index" open="(" close=")" separator=",">#{templatecode_list}
				</foreach>
		  </if>
         <![CDATA[ GROUP BY VD.POLICY_CODE)  ]]>
		<include refid="PA_documentWhereCondition" />
	</select>
	
	
	<!-- 通知书信息查询接口查询通知书信息 -->	
	<select id="findAllDocumentForQueryDocumentInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[   SELECT A.POLICY_CODE,
				       A.BUSI_PROD_CODE,
				       (SELECT TCBP.BUSI_PROD_CODE
				          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				         WHERE TCBP.MASTER_BUSI_ITEM_ID IS NULL
				           AND TCBP.POLICY_CODE = A.POLICY_CODE
				           AND ROWNUM = 1) MASTER_BUSI_PROD_CODE,
				       A.TEMPLATE_CODE,
				       A.CREATE_TIME,
				       A.DOCUMENT_NO,
				       A.CLOB_ID
				  FROM DEV_PAS.T_DOCUMENT A 
				 WHERE 1 = 1
				   AND A.TEMPLATE_CODE IN ('PAS_00010', 'PAS_00017', 'PAS_00014', 'PAS_00006', 'PAS_00023')  ]]>
    <if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
    <if test=" policy_code  != null and policy_code  != '' "><![CDATA[AND A.POLICY_CODE = #{policy_code}]]></if> 
    <if test=" template_code  != null and template_code  != '' "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code}]]></if> 
    <if test=" year != null and year != '' "><![CDATA[AND TO_CHAR(A.CREATE_TIME, 'yyyy') = #{year} ]]></if> 
	<![CDATA[    ORDER BY A.CREATE_TIME DESC, A.DOCUMENT_NO DESC ]]>
	</select>
	
	<select id="findAllVDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS
			FROM APP___PAS__DBUSER.V_DOCUMENT_ALL A WHERE 1 = 1  ]]>
		<if test=" document_no != null and document_no != '' "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	
	<select id="PAS_findBusinessCodeByDocuNO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT p.busi_item_id, p.busi_prod_code
  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD P
 WHERE P.BUSI_ITEM_ID IN
       (SELECT T.BUSI_ITEM_ID
          from APP___PAS__DBUSER.T_DOCUMENT T
         WHERE 1=1   ]]>
		<if test=" document_no != null and document_no != '' "><![CDATA[ AND T.DOCUMENT_NO = #{document_no} ]]></if>
		<![CDATA[ ) ]]>
	</select>
	
	<!-- 根据通知书号和保单号查询保单对应分红险信息 -->	
		<select id="PA_findBonusForQueryDocumentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ ]]>
		       SELECT A.BUSI_PROD_CODE AS BUSI_PROD_CODE, 
		              A.BUSI_ITEM_ID AS BUSI_ITEM_ID,
		              D.PRODUCT_ABBR_NAME AS BUSI_PROD_NAME
                      FROM DEV_PAS.T_CONTRACT_BUSI_PROD A,
                           DEV_PAS.T_DOCUMENT           B,
                           DEV_PAS.T_BONUS_ALLOCATE     C,
                           DEV_PAS.T_BUSINESS_PRODUCT D
                      WHERE B.POLICY_CODE = #{policy_code} 
                           AND  B.DOCUMENT_NO = #{document_no} 
                           AND C.BONUS_ALLOT = '4'
                           AND C.ALLOCATE_TYPE = '02'
                           AND TO_CHAR(B.SEND_TIME, 'yyyy') = TO_CHAR(C.ALLOCATE_DUE_DATE, 'yyyy')
                           AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
                           AND B.POLICY_CODE = C.POLICY_CODE
                           AND A.BUSI_PROD_CODE = D.PRODUCT_CODE_SYS
	</select>
	
		<!-- 查询保单贷款未清偿的贷款通知书数据 -->	
		<select id="PA_findAllDocumentInf" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			Select a.policy_id, a.policy_code, a.template_code, a.clob_id, c.busi_item_id, a.document_no
			  From dev_pas.t_Document              a,
			       dev_pas.t_Policy_Account        b,
			       dev_pas.t_Policy_Account_Stream c
			 where 1 = 1
			   and a.policy_id = b.policy_id
			   and b.account_id = c.account_id
			   and a.clob_id is not null
			   and a.create_time > c.repay_due_date
			   and b.account_type = '4'
			   and c.regular_repay = '0'
			   	and not exists (Select 'X'
			          From dev_pas.T_NOTICE_MSG_TASK n
			         where n.policy_code = a.policy_code
			           and a.template_code = n.template_code)
			   and a.template_code in ('PAS_00007', 'PAS_00018') ]]>
			  <if test=" create_time  != null  and  create_time  != ''  "><![CDATA[ AND A.CREATE_TIME < #{create_time} ]]></if>
			  <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
			  <if test=" template_code_list != null and template_code_list != ''  ">
				<![CDATA[ AND A.TEMPLATE_CODE IN  ]]>
				<foreach collection="template_code_list" item="template_code" 
					index="index" open="(" close=")" separator=",">#{template_code}
				</foreach>
			  </if>
			  <if test=" modnum != null and modnum != '' and start != null and start != '' ">
		        <![CDATA[ and mod(A.policy_id,#{modnum}) = #{start} ]]>
		      </if>
		 
	</select>
	
</mapper>