<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.QueryBankBranchMappingDaoImpl">
    <select id="queryBankBranchMapping" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[
            select ZONE_NO
            FROM DEV_PAS.T_BANK_BRANCH_MAPPING
            WHERE 1=1
        ]]>
        <if test=" BANK_CODE != null and BANK_CODE != ''  "><![CDATA[ AND BANK_CODE = #{BANK_CODE} ]]></if>
        <if test=" BANK_BRANCH_CODE != null and BANK_BRANCH_CODE != ''  "><![CDAT<PERSON>[ AND BANK_BRANCH_CODE = #{BANK_BRANCH_CODE} ]]></if>
</select>

</mapper>