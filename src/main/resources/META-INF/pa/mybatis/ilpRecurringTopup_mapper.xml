<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ilpRecurringTopup">
<!--
	<sql id="PA_ilpRecurringTopupWhereCondition">
		<if test=" active_indi  != null "><![CDATA[ AND A.ACTIVE_INDI = #{active_indi} ]]></if>
		<if test=" add_prem  != null "><![CDATA[ AND A.ADD_PREM = #{add_prem} ]]></if>
		<if test=" add_period  != null "><![CDATA[ AND A.ADD_PERIOD = #{add_period} ]]></if>
		<if test=" add_start_date  != null  and  add_start_date  != ''  "><![CDATA[ AND A.ADD_START_DATE = #{add_start_date} ]]></if>
		<if test=" operator_id  != null "><![CDATA[ AND A.OPERATOR_ID = #{operator_id} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" initial_start_date  != null  and  initial_start_date  != ''  "><![CDATA[ AND A.INITIAL_START_DATE = #{initial_start_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" add_prem_an  != null "><![CDATA[ AND A.ADD_PREM_AN = #{add_prem_an} ]]></if>
		<if test=" charge_type != null and charge_type != ''  "><![CDATA[ AND A.CHARGE_TYPE = #{charge_type} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" topup_chg_id  != null "><![CDATA[ AND A.TOPUP_CHG_ID = #{topup_chg_id} ]]></if>
		<if test=" add_year  != null "><![CDATA[ AND A.ADD_YEAR = #{add_year} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" sa_factor  != null "><![CDATA[ AND A.SA_FACTOR = #{sa_factor} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_to_date  != null  and  pay_to_date  != ''  "><![CDATA[ AND A.PAY_TO_DATE = #{pay_to_date} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryIlpRecurringTopupByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryIlpRecurringTopupByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PA_queryIlpRecurringTopupByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryIlpRecurringTopupByTopupChgIdCondition">
		<if test=" topup_chg_id  != null "><![CDATA[ AND A.TOPUP_CHG_ID = #{topup_chg_id} ]]></if>
	</sql>	
	<sql id="PA_queryIlpRecurringTopupByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addIlpRecurringTopup"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ILP_RECURRING_TOPUP__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP(
				ACTIVE_INDI, ADD_PREM, ADD_PERIOD, ADD_START_DATE, OPERATOR_ID, ACCOUNT_ID, INSERT_TIME, 
				INITIAL_START_DATE, UPDATE_TIME, ITEM_ID, ADD_PREM_AN, CHARGE_TYPE, PAIDUP_DATE, INSERT_TIMESTAMP, 
				TOPUP_CHG_ID, ADD_YEAR, PAY_MODE, UPDATE_BY, SA_FACTOR, LIST_ID, UPDATE_TIMESTAMP, 
				PAY_TO_DATE, INSERT_BY, POLICY_ID ) 
			VALUES (
				#{active_indi, jdbcType=NUMERIC}, #{add_prem, jdbcType=NUMERIC} , #{add_period, jdbcType=NUMERIC} , #{add_start_date, jdbcType=DATE} , #{operator_id, jdbcType=NUMERIC} , #{account_id, jdbcType=NUMERIC} , SYSDATE 
				, #{initial_start_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{add_prem_an, jdbcType=NUMERIC} , #{charge_type, jdbcType=VARCHAR} , #{paidup_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{topup_chg_id, jdbcType=NUMERIC} , #{add_year, jdbcType=NUMERIC} , #{pay_mode, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{sa_factor, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{pay_to_date, jdbcType=DATE} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteIlpRecurringTopup" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP WHERE LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateIlpRecurringTopup" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP ]]>
		<set>
		<trim suffixOverrides=",">
		    ACTIVE_INDI = #{active_indi, jdbcType=NUMERIC} ,
		    ADD_PREM = #{add_prem, jdbcType=NUMERIC} ,
		    ADD_PERIOD = #{add_period, jdbcType=NUMERIC} ,
		    ADD_START_DATE = #{add_start_date, jdbcType=DATE} ,
		    OPERATOR_ID = #{operator_id, jdbcType=NUMERIC} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
		    INITIAL_START_DATE = #{initial_start_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    ADD_PREM_AN = #{add_prem_an, jdbcType=NUMERIC} ,
			CHARGE_TYPE = #{charge_type, jdbcType=VARCHAR} ,
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    TOPUP_CHG_ID = #{topup_chg_id, jdbcType=NUMERIC} ,
		    ADD_YEAR = #{add_year, jdbcType=NUMERIC} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SA_FACTOR = #{sa_factor, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    PAY_TO_DATE = #{pay_to_date, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findIlpRecurringTopupByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRecurringTopupByListIdCondition" />
	</select>
	
	<select id="PA_findIlpRecurringTopupByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRecurringTopupByItemIdCondition" />
	</select>
	
	<select id="PA_findIlpRecurringTopupByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRecurringTopupByPolicyIdCondition" />
	</select>
	
	<select id="PA_findIlpRecurringTopupByTopupChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRecurringTopupByTopupChgIdCondition" />
	</select>
	
	<select id="PA_findIlpRecurringTopupByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRecurringTopupByAccountIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapIlpRecurringTopup" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllIlpRecurringTopup" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findIlpRecurringTopupTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryIlpRecurringTopupForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACTIVE_INDI, B.ADD_PREM, B.ADD_PERIOD, B.ADD_START_DATE, B.OPERATOR_ID, B.ACCOUNT_ID, 
			B.INITIAL_START_DATE, B.ITEM_ID, B.ADD_PREM_AN, B.CHARGE_TYPE, B.PAIDUP_DATE, 
			B.TOPUP_CHG_ID, B.ADD_YEAR, B.PAY_MODE, B.SA_FACTOR, B.LIST_ID, 
			B.PAY_TO_DATE, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ACTIVE_INDI, A.ADD_PREM, A.ADD_PERIOD, A.ADD_START_DATE, A.OPERATOR_ID, A.ACCOUNT_ID, 
			A.INITIAL_START_DATE, A.ITEM_ID, A.ADD_PREM_AN, A.CHARGE_TYPE, A.PAIDUP_DATE, 
			A.TOPUP_CHG_ID, A.ADD_YEAR, A.PAY_MODE, A.SA_FACTOR, A.LIST_ID, 
			A.PAY_TO_DATE, A.POLICY_ID FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
