<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISalseAmountDao">

	<sql id="salesAmountWhereCondition">
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" scense_code != null and scense_code != ''  "><![CDATA[ AND A.SCENSE_CODE = #{scense_code} ]]></if>
		<if test=" accum_date  != null  and  accum_date  != ''  "><![CDATA[ AND A.ACCUM_DATE = #{accum_date} ]]></if>
		<if test=" prem_flag  != null "><![CDATA[ AND <PERSON>.PREM_FLAG = #{prem_flag} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" acc_status  != null "><![CDATA[ AND A.ACC_STATUS = #{acc_status} ]]></if>
		<if test=" batch_time  != null  and  batch_time  != ''  "><![CDATA[ AND A.ACCUM_DATE <= #{batch_time} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="querySalesAmountByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="querySalesAmountByBankCodeCondition">
		<if test=" bank_code != null and bank_code != '' "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
	</sql>	
	<sql id="querySalesAmountByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="querySalesAmountByChannelTypeCondition">
		<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
	</sql>	
	<sql id="querySalesAmountByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSalesAmount"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SALES_AMOUNT__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SALES_AMOUNT(
				BUSINESS_CODE, SCENSE_CODE, ACCUM_DATE, INSERT_TIME, UPDATE_TIME, PREM_FLAG, BUSI_PROD_CODE, 
				APPLY_CODE, INSERT_TIMESTAMP, ORGAN_CODE, 
				]]><if test="policy_code != null and policy_code != ''">POLICY_CODE, </if><![CDATA[ CHANNEL_TYPE, UPDATE_BY, BANK_CODE, 
				LIST_ID, FEE_AMOUNT, ACC_STATUS, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{business_code, jdbcType=VARCHAR}, #{scense_code, jdbcType=VARCHAR} , #{accum_date, jdbcType=TIMESTAMP} , SYSDATE , SYSDATE , #{prem_flag, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} ,
				]]><if test="policy_code != null and policy_code != ''">#{policy_code, jdbcType=VARCHAR} ,</if><![CDATA[   #{channel_type, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{bank_code, jdbcType=VARCHAR} 
				, #{list_id, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{acc_status, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSalesAmount" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SALES_AMOUNT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSalesAmount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SALES_AMOUNT ]]>
		<set>
		<trim suffixOverrides=",">
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
			SCENSE_CODE = #{scense_code, jdbcType=VARCHAR} ,
		    ACCUM_DATE = #{accum_date, jdbcType=TIMESTAMP} ,
			UPDATE_TIME = SYSDATE , 
		    PREM_FLAG = #{prem_flag, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    ACC_STATUS = #{acc_status, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSalesAmountByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="querySalesAmountByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalesAmountByBankCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="querySalesAmountByBankCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalesAmountByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="querySalesAmountByBusiProdCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalesAmountByChannelType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="querySalesAmountByChannelTypeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalesAmountByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="querySalesAmountByOrganCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSalesAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSalesAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS,ROWNUM RN FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE 1=1  ]]>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" scense_code != null and scense_code != ''  "><![CDATA[ AND A.SCENSE_CODE = #{scense_code} ]]></if>
		<if test=" accum_date  != null  and  accum_date  != ''  "><![CDATA[ AND A.ACCUM_DATE = #{accum_date} ]]></if>
		<if test=" prem_flag  != null "><![CDATA[ AND A.PREM_FLAG = #{prem_flag} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code}, '%') ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" acc_status  != null "><![CDATA[ AND A.ACC_STATUS = #{acc_status} ]]></if>
		<if test=" batch_time  != null  and  batch_time  != ''  "><![CDATA[ AND A.ACCUM_DATE <= #{batch_time} ]]></if>
		<if test=" state_time  != null  and  state_time  != ''  "><![CDATA[ AND A.ACCUM_DATE >= #{state_time} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSalesAmountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="querySalesAmountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSINESS_CODE, B.SCENSE_CODE, B.ACCUM_DATE, B.PREM_FLAG, B.BUSI_PROD_CODE, 
			B.APPLY_CODE, B.ORGAN_CODE, B.POLICY_CODE, B.CHANNEL_TYPE, B.BANK_CODE, 
			B.LIST_ID, B.FEE_AMOUNT, B.ACC_STATUS FROM (
					SELECT ROWNUM RN, A.BUSINESS_CODE, A.SCENSE_CODE, A.ACCUM_DATE, A.PREM_FLAG, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.CHANNEL_TYPE, A.BANK_CODE, 
			A.LIST_ID, A.FEE_AMOUNT, A.ACC_STATUS FROM APP___PAS__DBUSER.T_SALES_AMOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findFirstSalesAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE,
       A.SCENSE_CODE,
       A.ACCUM_DATE,
       A.PREM_FLAG,
       A.BUSI_PROD_CODE,
       A.APPLY_CODE,
       A.ORGAN_CODE,
       A.POLICY_CODE,
       A.CHANNEL_TYPE,
       A.BANK_CODE,
       A.LIST_ID,
       A.FEE_AMOUNT,
       A.ACC_STATUS
  FROM APP___PAS__DBUSER.T_SALES_AMOUNT A
WHERE A.APPLY_CODE = #{apply_code}
         AND A.BUSI_PROD_CODE = #{busi_prod_code}
         AND A.SCENSE_CODE = '1'
         AND A.ACCUM_DATE <= #{batch_time}
		AND A.ACCUM_DATE >= #{state_time}
 ORDER BY A.UPDATE_TIME DESC ]]>
	</select>
</mapper>
