<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="IPolicyAccountStreamDao">

	<sql id="PA_policyAccountStreamWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" capitalized_date  != null  and  capitalized_date  != ''  "><![CDATA[ AND A.CAPITALIZED_DATE = #{capitalized_date} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" capital_balance  != null "><![CDATA[ AND A.CAPITAL_BALANCE = #{capital_balance} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_type  != null "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
		<if test=" balance_date  != null  and  balance_date  != ''  "><![CDATA[ AND A.BALANCE_DATE = #{balance_date} ]]></if>
		<if test=" regular_repay  != null "><![CDATA[ AND A.REGULAR_REPAY = #{regular_repay} ]]></if>
		<if test=" repay_due_date  != null  and  repay_due_date  != ''  "><![CDATA[ AND A.REPAY_DUE_DATE = #{repay_due_date} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" loan_start_date  != null  and  loan_start_date  != ''  "><![CDATA[ AND A.LOAN_START_DATE = #{loan_start_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" stream_pol_chg_id  != null "><![CDATA[ AND A.STREAM_POL_CHG_ID = #{stream_pol_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
		<if test=" policy_code  != null  and  policy_code  != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" repay_buss_no  != null  and  repay_buss_no  != ''  "><![CDATA[ AND A.REPAY_BUSS_NO = #{repay_buss_no} ]]></if>
		<if test=" dedu_loan_flag  != null  and  dedu_loan_flag  != ''  "><![CDATA[ AND A.DEDU_LOAN_FLAG = #{dedu_loan_flag} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyAccountStreamByStreamIdCondition">
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountStreamByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountStreamByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountStreamByStreamPolChgIdCondition">
		<if test=" stream_pol_chg_id  != null "><![CDATA[ AND A.STREAM_POL_CHG_ID = #{stream_pol_chg_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountStreamByBusiItemIdCondition">
		<if test=" busi_item_id != null"><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id}]]></if>
	</sql>
<!-- 添加操作 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<insert id="PA_addPolicyAccountStream"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="stream_id">
				SELECT APP___PAS__DBUSER.S_PAY_PLAN__PLAN_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM (
				DEDU_LOAN_FLAG,MONEY_CODE, INTEREST_CAPITAL, CAPITALIZED_DATE, ACCOUNT_ID, INSERT_TIME, CAPITAL_BALANCE, INTEREST_SUM, 
				UPDATE_TIME, ITEM_ID, ACCOUNT_TYPE, BALANCE_DATE, REGULAR_REPAY, INSERT_TIMESTAMP, REPAY_DUE_DATE, 
				UPDATE_BY, INTEREST_BALANCE, INTEREST_START_DATE, LOAN_START_DATE, UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, STREAM_POL_CHG_ID, 
				POLICY_ID, STREAM_ID,
				CASH_VALUE,ALLOW_LOAN_RATE,LOAN_LIMIT,IS_AUTO_RENEW	,AUTO_ACCOUNT_ID,IS_NEW_ACCOUNT,IS_AUTHORITY,AUTHORITY_ACCEPT_CODE
				,LOAN_REASON,REASON_DESCRIPTION) 
			VALUES (
				#{dedu_loan_flag, jdbcType=NUMERIC} ,#{money_code, jdbcType=VARCHAR}, #{interest_capital, jdbcType=NUMERIC} , #{capitalized_date, jdbcType=DATE} , #{account_id, jdbcType=NUMERIC} , SYSDATE , #{capital_balance, jdbcType=NUMERIC} , #{interest_sum, jdbcType=NUMERIC} 
				, SYSDATE , #{item_id, jdbcType=NUMERIC} , #{account_type, jdbcType=NUMERIC} , #{balance_date, jdbcType=DATE} , #{regular_repay, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{repay_due_date, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{interest_balance, jdbcType=NUMERIC} , #{interest_start_date, jdbcType=DATE} , #{loan_start_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{stream_pol_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{stream_id, jdbcType=NUMERIC} 
				, #{cash_value, jdbcType=NUMERIC}, #{allow_loan_rate, jdbcType=NUMERIC}, #{loan_limit, jdbcType=NUMERIC}
				, #{is_auto_renew, jdbcType=NUMERIC}, #{auto_account_id, jdbcType=NUMERIC}, #{is_new_account, jdbcType=NUMERIC}, #{is_authority, jdbcType=NUMERIC},#{authority_accept_code, jdbcType=VARCHAR}
				, #{loan_reason, jdbcType=VARCHAR}, #{reason_description, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	 
	<delete id="PA_deletePolicyAccountStream" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM WHERE STREAM_ID = #{stream_id} ]]>
	</delete>

	<delete id="PA_deletePolicyStreamRollBack" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM WHERE ACCOUNT_ID = #{account_id} and STREAM_POL_CHG_ID >= #{stream_pol_chg_id}]]>
	</delete>

<!-- 修改操作 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<update id="PA_updatePolicyAccountStream" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM ]]>
		<set>
		<trim suffixOverrides=",">
			DEDU_LOAN_FLAG = #{dedu_loan_flag, jdbcType=NUMERIC} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    CAPITALIZED_DATE = #{capitalized_date, jdbcType=DATE} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
		    CAPITAL_BALANCE = #{capital_balance, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    ACCOUNT_TYPE = #{account_type, jdbcType=NUMERIC} ,
		    BALANCE_DATE = #{balance_date, jdbcType=DATE} ,
		    REGULAR_REPAY = #{regular_repay, jdbcType=NUMERIC} ,
		    REPAY_DUE_DATE = #{repay_due_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    LOAN_START_DATE = #{loan_start_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    STREAM_POL_CHG_ID = #{stream_pol_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    STREAM_ID = #{stream_id, jdbcType=NUMERIC} ,
		    
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    ALLOW_LOAN_RATE = #{allow_loan_rate, jdbcType=NUMERIC} ,
		    LOAN_LIMIT = #{loan_limit, jdbcType=NUMERIC} ,
		    IS_AUTO_RENEW = #{is_auto_renew, jdbcType=NUMERIC} ,
		    AUTO_ACCOUNT_ID = #{auto_account_id, jdbcType=NUMERIC} ,
		    IS_NEW_ACCOUNT = #{is_new_account, jdbcType=NUMERIC} ,
		    CASH_VALUE_NET = #{cash_value_net, jdbcType=NUMERIC} ,
		    IS_AUTHORITY=#{is_authority, jdbcType=NUMERIC},
		    <if test=" act_repay_date != null"><![CDATA[  ACT_REPAY_DATE = #{act_repay_date, jdbcType=DATE} ,]]></if>
		    
		    AUTHORITY_ACCEPT_CODE=#{authority_accept_code, jdbcType=VARCHAR},
		    REPAY_BUSS_NO=#{repay_buss_no, jdbcType=VARCHAR},
		    INTEREST_START_DATE =#{interest_start_date, jdbcType=DATE},
		    LOAN_REASON = #{loan_reason, jdbcType=VARCHAR}, 
		    REASON_DESCRIPTION = #{reason_description, jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE STREAM_ID = #{stream_id} ]]>
	</update>
	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	
	
	<!-- 修改操作 (目前仅供AE投保人变更使用，只修改是否自动续贷、自动还款账户这两个字段信息)-->
	<update id="PA_updatePolicyAccountStreamForAE" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    IS_AUTO_RENEW = #{is_auto_renew, jdbcType=NUMERIC} ,
		    AUTO_ACCOUNT_ID = #{auto_account_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE STREAM_ID = #{stream_id} ]]>
	</update>
	
	
	
  <select id="PA_findPolicyAccountStream" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.CASH_VALUE,A.CASH_VALUE_NET,A.IS_AUTO_RENEW,A.AUTO_ACCOUNT_ID,A.INTEREST_EXPECT,A.ALLOW_LOAN_RATE,A.LOAN_LIMIT,
			A.IS_AUTO_FAILED,A.IS_NEW_ACCOUNT,A.INTEREST_START_DATE,A.REPAY_BUSS_NO,
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE 1 = 1 
			 ]]>
		<include refid="PA_policyAccountStreamWhereCondition" />
	</select>
<!-- 按索引查询操作 -->	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findPolicyAccountStreamByStreamId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamByStreamIdCondition" />
	</select>
	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findPolicyAccountStreamByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamByAccountIdCondition" />
	</select>
	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findPolicyAccountStreamByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamByItemIdCondition" />
	</select>
	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findPolicyAccountStreamByStreamPolChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamByStreamPolChgIdCondition" />
	</select>
	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findPolicyAccountStreamByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamByBusiItemIdCondition" />
	</select>
	

<!-- 按map查询操作 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findAllMapPolicyAccountStream" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findAllPolicyAccountStream" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID ,
			A.CASH_VALUE,A.ALLOW_LOAN_RATE,A.LOAN_LIMIT,
			A.IS_AUTO_RENEW,A.AUTO_ACCOUNT_ID,A.IS_NEW_ACCOUNT ,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION
			FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyAccountStreamWhereCondition" />
		<![CDATA[ ORDER BY A.STREAM_ID DESC ]]> 
	</select>

<!-- 查询所有操作 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findAllPolicyAccountStream1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyAccountStreamWhereCondition" />
		<![CDATA[ ORDER BY A.STREAM_ID ]]> 
	</select>
<!-- 查询个数操作 -->
	<select id="PA_findPolicyAccountStreamTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM A WHERE 1 = 1  ]]>
		<include refid="PA_policyAccountStreamWhereCondition" />
	</select>

<!-- 分页查询操作 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_queryPolicyAccountStreamForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.DEDU_LOAN_FLAG,B.MONEY_CODE, B.INTEREST_CAPITAL, B.CAPITALIZED_DATE, B.ACCOUNT_ID, B.CAPITAL_BALANCE, B.INTEREST_SUM, 
			B.ITEM_ID, B.ACCOUNT_TYPE, B.BALANCE_DATE, B.REGULAR_REPAY, B.REPAY_DUE_DATE, 
			B.INTEREST_BALANCE, B.LOAN_START_DATE, B.BUSI_ITEM_ID, B.STREAM_POL_CHG_ID, 
			B.POLICY_ID, B.STREAM_ID,B.IS_AUTHORITY,B.AUTHORITY_ACCEPT_CODE,B.ACT_REPAY_DATE,B.INTEREST_START_DATE,B.LOAN_REASON,B.REASON_DESCRIPTION FROM (
					SELECT ROWNUM RN, A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findPolicyAS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select pasr.LOAN_RATE
		   from APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM pas
		   left join APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE pasr
		     on pas.stream_id = pasr.stream_id
		  where pas.policy_id = #{policy_id}]]>
		  <if test=" busi_item_id  != null "><![CDATA[  and pas.busi_item_id = #{busi_item_id} ]]></if>
		   <![CDATA[  and pasr.LOAN_RATE 
		    is not null order by pasr.Time_Perido_Code]]>
		
	</select>
	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findPolicyAccountStreamByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	    SELECT A.ACCOUNT_ID,
	      A.POLICY_ID,
          A.BUSI_ITEM_ID,
          A.ITEM_ID,
          (SELECT SUM(H.CAPITAL_BALANCE) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM H 
          WHERE H.POLICY_ID=B.POLICY_ID  AND H.ACCOUNT_TYPE = 4
          AND H.REGULAR_REPAY = 0) SUN_CAPITAL_BALANCE,
          (SELECT Z.BUSI_PROD_CODE
             FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z
            WHERE Z.BUSI_ITEM_ID = A.BUSI_ITEM_ID) AS BUSI_PROD_CODE,
            (SELECT Y.PRODUCT_ABBR_NAME
             FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT Y
            WHERE Z.BUSI_ITEM_ID = A.BUSI_ITEM_ID AND Z.BUSI_PRD_ID=Y.BUSINESS_PRD_ID) AS BUSI_PROD_CODE_NAME,
          A.CAPITAL_BALANCE,
          B.LOAN_START_DATE,
          B.CASH_VALUE,
          B.LOAN_LIMIT,
          B.REPAY_DUE_DATE,
          B.STREAM_ID,B.LOAN_REASON,B.REASON_DESCRIPTION,
          B.INTEREST_START_DATE,
          B.REGULAR_REPAY,
          C.MATURITY_DATE,
          C.EXPIRY_DATE,
          (SELECT TRANS_AMOUNT
	         FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST PATL
	        WHERE PATL.ACCOUNT_ID = A.ACCOUNT_ID
	          AND PATL.TRANS_CODE = '35'
	          AND PATL.STREAM_ID = B.STREAM_ID
	          AND ROWNUM = 1) STAMP_RATE,
            C.LIABILITY_STATE
     FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        A,
          APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM B,
          APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
    WHERE A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
      AND A.ACCOUNT_ID = B.ACCOUNT_ID
      AND B.ACCOUNT_TYPE = 4
      AND B.REGULAR_REPAY = 0
      AND A.BUSI_ITEM_ID=C.BUSI_ITEM_ID
      AND A.POLICY_ID=(SELECT D.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER D WHERE D.POLICY_CODE=#{policy_code, jdbcType=VARCHAR})]]>		
	</select>
	
	<select id="PA_findLoanRateByStreamId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	    SELECT A.TIME_PERIDO_CODE, A.LOAN_RATE, A.STREAM_ID
        FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE A
        WHERE A.STREAM_ID = #{stream_id, jdbcType=NUMERIC}]]>		
	</select>
	<!-- 根据险种id查询贷款信息并按照贷款开始日期进行排序-->
	<!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findLoanByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ 
	    SELECT 
        TCBP.BUSI_PROD_CODE,
        TBP.PRODUCT_NAME_SYS,
        TBP.PRODUCT_ABBR_NAME,
        TM.CAPITAL_BALANCE,
        TM.LOAN_START_DATE,
        TM.REGULAR_REPAY,
        TM.REPAY_DUE_DATE,
        TM.LOAN_REASON,TM.REASON_DESCRIPTION,TCBP.MASTER_BUSI_ITEM_ID,TCBP.BUSI_ITEM_ID,TPA.ACCOUNT_ID
      FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
      left join APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
      on TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
      left join APP___PAS__DBUSER.T_POLICY_ACCOUNT TPA
      on TPA.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
      left join APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TM
      on TPA.ACCOUNT_ID = TM.ACCOUNT_ID
        WHERE 1=1  AND TPA.ACCOUNT_TYPE = '4' ]]>
         <if test=" busi_item_id  != null "><![CDATA[ AND TCBP.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	     <if test=" policy_id  != null "><![CDATA[ AND TCBP.POLICY_ID = #{policy_id} ]]></if>
	     <if test=" loan_start_date  != null  and  loan_start_date  != ''  "><![CDATA[ AND TM.LOAN_START_DATE = #{loan_start_date} ]]></if>
	      ORDER BY TM.LOAN_START_DATE DESC,TM.INSERT_TIME DESC
	</select>
	<!-- 根据险种id查询附加险贷款信息 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findAddBusiByBusiId" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT
	TCBP.BUSI_PROD_CODE,
	TBP.PRODUCT_NAME_SYS,
	TBP.PRODUCT_ABBR_NAME,
	TM.CAPITAL_BALANCE,
	TM.LOAN_START_DATE,
	TM.REPAY_DUE_DATE,
	TM.INTEREST_START_DATE,
	TM.REGULAR_REPAY,TM.LOAN_REASON,TM.REASON_DESCRIPTION,
	TCBP.master_busi_item_id,tcbp.busi_item_id
	FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TM,
	APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
	APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP,
	APP___PAS__DBUSER.T_POLICY_ACCOUNT TPA
	WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
	AND TPA.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
	AND TPA.ACCOUNT_ID = TM.ACCOUNT_ID
	AND TPA.BUSI_ITEM_ID = #{busi_item_id}
	AND TM.REGULAR_REPAY = '0'
	ORDER BY TM.LOAN_START_DATE DESC
	
	</select>
	<!-- 根据险种id查询附加险贷款信息 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="findAllPolicyAccountStreamForOut" resultType="java.util.Map" parameterType="java.util.Map">
	    
	    SELECT 
	   B.ACCOUNT_ID,
       C.STREAM_ID,
       A.POLICY_ID,
       C.CAPITAL_BALANCE,
       C.INTEREST_START_DATE,
       C.BUSI_ITEM_ID,C.LOAN_REASON,C.REASON_DESCRIPTION
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER       A,
       APP___PAS__DBUSER.T_POLICY_ACCOUNT        B,
       APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM C
 WHERE 1 = 1
   AND A.POLICY_ID = B.POLICY_ID
   AND B.ACCOUNT_ID = C.ACCOUNT_ID
   AND B.POLICY_ID = C.POLICY_ID
   AND C.REGULAR_REPAY = '0'
   AND C.INTEREST_START_DATE IS NULL
   AND a.policy_code =#{policy_code}
	
	</select>
	
	<!-- 查询未清偿贷款 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_queryLoanOutstanding" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ 
	    select A.STREAM_ID,A.STREAM_POL_CHG_ID,A.ACCOUNT_ID,A.ACCOUNT_TYPE,A.POLICY_ID,
	    A.BUSI_ITEM_ID,A.ITEM_ID,A.CAPITAL_BALANCE,A.INTEREST_CAPITAL,A.INTEREST_BALANCE,A.INTEREST_SUM,
	    A.BALANCE_DATE,A.CAPITALIZED_DATE,A.MONEY_CODE,A.LOAN_START_DATE,A.REPAY_DUE_DATE,A.REGULAR_REPAY,
		A.INTEREST_EXPECT,A.CASH_VALUE,A.CASH_VALUE_NET,A.ALLOW_LOAN_RATE,A.LOAN_LIMIT,A.IS_AUTO_RENEW,A.IS_AUTO_FAILED,
		A.AUTO_ACCOUNT_ID,A.IS_NEW_ACCOUNT,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.UPDATE_BY,A.INTEREST_START_DATE,A.REPAY_BUSS_NO,A.LOAN_REASON,A.REASON_DESCRIPTION
	  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
	      APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM A
	 WHERE 1 = 1
	   AND TCM.POLICY_ID = A.POLICY_ID 
	      ]]>		
	      <include refid="PA_policyAccountStreamWhereCondition" />
	</select>
	<select id="findPolicyAccountStreamForRB" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	    SELECT C.STREAM_ID
  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE         A,
       APP___PAS__DBUSER.T_CS_POLICY_CHANGE         B,
       APP___PAS__DBUSER.T_CS_POLICY_ACCOUNT_STREAM C
 WHERE A.ACCEPT_ID = B.ACCEPT_ID
   AND C.POLICY_CHG_ID = B.POLICY_CHG_ID
   AND A.ACCEPT_CODE = #{accept_code}
   AND C.ACCOUNT_ID = #{account_id}
   AND C.BUSI_ITEM_ID = #{busi_item_id}
   AND C.OPERATION_TYPE = '2'
   AND C.ACCOUNT_TYPE = #{account_type}
 ORDER BY C.REPAY_DUE_DATE DESC

]]>		
	</select>
	
	<!-- 续贷查询所有数据 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="PA_findRLPolicyAccountStream" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.MONEY_CODE, A.INTEREST_CAPITAL,A.CAPITALIZED_DATE, A.ACCOUNT_ID,
		 (SELECT SUM(T.CAPITAL_BALANCE) FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM T WHERE T.POLICY_ID = A.POLICY_ID and t.REGULAR_REPAY = 0 )CAPITAL_BALANCE, 
		 A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID ,
			A.CASH_VALUE,A.ALLOW_LOAN_RATE,A.LOAN_LIMIT,
			A.IS_AUTO_RENEW,A.AUTO_ACCOUNT_ID,A.IS_NEW_ACCOUNT ,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION
			FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  A WHERE ROWNUM <=  1000
		  ]]>
		<include refid="PA_policyAccountStreamWhereCondition" />
		<![CDATA[ ORDER BY A.STREAM_ID DESC ]]> 
	</select>
	
	<!-- 根据赔案号查询未清偿前的数据信息 --><!-- 110633 add LOAN_REASON,REASON_DESCRIPTION -->
	<select id="findPolicyAccountStreamByRepayBussNo" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ 
		    select  tpa.capital_balance,
			        tpa.interest_capital,
			        tpa.interest_balance,
			        tpa.interest_sum,
			        tpa.balance_date,
			        tpa.Account_Id ,
			        tpa.LOAN_REASON,tpa.REASON_DESCRIPTION
			   from (select a.capital_balance,
			                a.interest_capital,
			                a.interest_balance,
			                a.interest_sum,
			                a.balance_date,
			                a.Account_Id,
			                a.LOAN_REASON,a.REASON_DESCRIPTION
			           from dev_pas.T_POLICY_ACCOUNT_STREAM    t,
			                dev_pas.T_CS_POLICY_ACCOUNT_STREAM a,
			                dev_pas.t_cs_policy_change         tc,
			                dev_pas.t_cs_accept_change         ta
			          where t.policy_id = a.policy_id
			            and t.stream_id = a.stream_id
			            and t.policy_id = tc.policy_id
			            and a.policy_chg_id = tc.policy_chg_id
			            and ta.accept_id = tc.accept_id
			            and ta.service_code in ('LN', 'RL')
			            and ta.accept_status = '18'
			            and a.old_new = '1'
			            and a.operation_type = 1
			            and t.regular_repay = '1'
			            and t.stream_id = #{stream_id}
			            and t.busi_item_id = #{busi_item_id}
			            and t.repay_buss_no = #{repay_buss_no}
			          order by a.loan_start_date desc) tpa
			  where rownum = 1
	      ]]>		
	</select>
	
	<select id="selectAllLoanReason" resultType="java.util.HashMap" parameterType="java.util.Map">
		SELECT CODE loan_reason_code, NAME loan_reason_name FROM APP___PAS__DBUSER.T_LOAN_REASON
	 </select>
	
	<update id="updatePolicyAccountStreamForRePay" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM  set 
			 ACT_REPAY_DATE = #{act_repay_date, jdbcType=DATE}  WHERE STREAM_ID = #{stream_id} ]]>
	</update>
	
	
	<!-- 技术需求任务 #144538  掌上新华二期-贷款信息查询功能（需求15）-个险新核心-保单 -->
	<select id="PA_findPolicyAccountInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	    SELECT A.ACCOUNT_ID,
	      A.POLICY_ID,
          A.BUSI_ITEM_ID,
          A.ITEM_ID,
          (SELECT SUM(H.CAPITAL_BALANCE) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM H 
          WHERE H.POLICY_ID=B.POLICY_ID  AND H.ACCOUNT_TYPE = 4
          AND H.REGULAR_REPAY = 0) SUN_CAPITAL_BALANCE,
          (SELECT Z.BUSI_PROD_CODE
             FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z
            WHERE Z.BUSI_ITEM_ID = A.BUSI_ITEM_ID) AS BUSI_PROD_CODE,
            (SELECT Y.PRODUCT_ABBR_NAME
             FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT Y
            WHERE Z.BUSI_ITEM_ID = A.BUSI_ITEM_ID AND Z.BUSI_PRD_ID=Y.BUSINESS_PRD_ID) AS BUSI_PROD_CODE_NAME,
          B.CAPITAL_BALANCE,
          B.INTEREST_CAPITAL,
          B.LOAN_START_DATE,
          B.CASH_VALUE,
          B.LOAN_LIMIT,
          B.REPAY_DUE_DATE,
          B.STREAM_ID,B.LOAN_REASON,B.REASON_DESCRIPTION,
          B.INTEREST_START_DATE,
          B.REGULAR_REPAY,
          B.IS_AUTO_RENEW,
          B.AUTO_ACCOUNT_ID
     FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        A,
          APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM B,
          APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
    WHERE A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
      AND A.ACCOUNT_ID = B.ACCOUNT_ID
      AND B.ACCOUNT_TYPE = 4
      AND B.REGULAR_REPAY = 0
      AND A.BUSI_ITEM_ID=C.BUSI_ITEM_ID
      AND A.POLICY_ID=(SELECT D.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER D WHERE D.POLICY_CODE=#{policy_code, jdbcType=VARCHAR})]]>		
	</select>
	
	<select id="PA_selectPolicyAccountStreamByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEDU_LOAN_FLAG,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.REPAY_DUE_DATE, 
			A.INTEREST_BALANCE, A.LOAN_START_DATE, A.BUSI_ITEM_ID, A.STREAM_POL_CHG_ID, 
			A.POLICY_ID, A.STREAM_ID ,
			A.CASH_VALUE,A.ALLOW_LOAN_RATE,A.LOAN_LIMIT,
			A.IS_AUTO_RENEW,A.AUTO_ACCOUNT_ID,A.IS_NEW_ACCOUNT ,A.IS_AUTHORITY,A.AUTHORITY_ACCEPT_CODE,A.ACT_REPAY_DATE,A.INTEREST_START_DATE,A.LOAN_REASON,A.REASON_DESCRIPTION
			FROM  APP___PAS__DBUSER.T_POLICY_ACCOUNT        B,APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM A
            WHERE B.ACCOUNT_ID = A.ACCOUNT_ID
            AND B.ACCOUNT_TYPE = '4'
            AND A.REGULAR_REPAY = '0'
            AND B.POLICY_ID =(SELECT D.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER D WHERE D.POLICY_CODE=#{policy_code, jdbcType=VARCHAR})
            AND B.POLICY_ACCOUNT_STATUS IN ('1', '3')]]>
	</select>
	
	<!-- 需求任务 #165896  查询保单主险组下是否存在未清偿险种信息-->
	<select id="PA_findAllPolicyAccountStreamInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT B.*
				  FROM DEV_PAS.T_POLICY_ACCOUNT        A,
				       DEV_PAS.T_POLICY_ACCOUNT_STREAM B,
				             (SELECT CP.BUSI_ITEM_ID
						          FROM DEV_PAS.T_CONTRACT_BUSI_PROD CP
						         WHERE CP.POLICY_ID = #{policy_id}
						           AND (CP.BUSI_ITEM_ID = #{busi_item_id} OR
						                CP.MASTER_BUSI_ITEM_ID = #{busi_item_id})) C
				 WHERE 1 = 1
				   AND A.ACCOUNT_ID = B.ACCOUNT_ID
				   AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
				   AND B.REGULAR_REPAY = '0'
				   AND A.ACCOUNT_TYPE = '4'
				   AND A.POLICY_ID = #{policy_id}
		]]>
		<if test=" dedu_loan_flag != null "><![CDATA[ AND B.DEDU_LOAN_FLAG = #{dedu_loan_flag} ]]></if>
		<if test=" dueDate != null "><![CDATA[ AND B.LOAN_START_DATE < #{dueDate} ]]></if>
		<![CDATA[ 
		 ORDER BY B.LOAN_START_DATE
		]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="findLoanPolicyAccountLapsedTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(B.BUSI_ITEM_ID)
			  FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM A, DEV_PAS.T_CONTRACT_BUSI_PROD B
			 WHERE A.POLICY_ID = B.POLICY_ID
			   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			   AND A.ACCOUNT_TYPE = '4'
			   AND A.REGULAR_REPAY = '0'
			   AND B.LIABILITY_STATE = '4'
			   AND B.POLICY_CODE = #{POLICY_CODE}
		]]>
	</select>
</mapper>