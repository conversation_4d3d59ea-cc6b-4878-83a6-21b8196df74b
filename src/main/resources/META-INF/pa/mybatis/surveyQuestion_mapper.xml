<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="surveyQuestion">
<!--
	<sql id="PA_surveyQuestionWhereCondition">
		<if test=" survey_question != null and survey_question != ''  "><![CDATA[ AND A.SURVEY_QUESTION = #{survey_question} ]]></if>
		<if test=" top_question_flag  != null "><![CDATA[ AND A.TOP_QUESTION_FLAG = #{top_question_flag} ]]></if>
		<if test=" question_order  != null "><![CDATA[ AND A.QUESTION_ORDER = #{question_order} ]]></if>
		<if test=" question_id  != null "><![CDATA[ AND A.QUESTION_ID = #{question_id} ]]></if>
		<if test=" question_code != null and question_code != ''  "><![CDATA[ AND A.QUESTION_CODE = #{question_code} ]]></if>
		<if test=" survey_template_id  != null "><![CDATA[ AND A.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
		<if test=" question_type  != null "><![CDATA[ AND A.QUESTION_TYPE = #{question_type} ]]></if>
		<if test=" question_answer_num  != null "><![CDATA[ AND A.QUESTION_ANSWER_NUM = #{question_answer_num} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySurveyQuestionByQuestionIdCondition">
		<if test=" question_id  != null "><![CDATA[ AND A.QUESTION_ID = #{question_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSurveyQuestion"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVEY_QUESTION(
				SURVEY_QUESTION, TOP_QUESTION_FLAG, INSERT_TIME, QUESTION_ORDER, UPDATE_TIME, QUESTION_ID, QUESTION_CODE, 
				INSERT_TIMESTAMP, UPDATE_BY, SURVEY_TEMPLATE_ID, QUESTION_TYPE, QUESTION_ANSWER_NUM, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{survey_question, jdbcType=VARCHAR}, #{top_question_flag, jdbcType=NUMERIC} , SYSDATE , #{question_order, jdbcType=NUMERIC} , SYSDATE , #{question_id, jdbcType=NUMERIC} , #{question_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{survey_template_id, jdbcType=NUMERIC} , #{question_type, jdbcType=NUMERIC} , #{question_answer_num, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSurveyQuestion" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SURVEY_QUESTION WHERE QUESTION_ID=#{question_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSurveyQuestion" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURVEY_QUESTION ]]>
		<set>
		<trim suffixOverrides=",">
			SURVEY_QUESTION = #{survey_question, jdbcType=VARCHAR} ,
		    TOP_QUESTION_FLAG = #{top_question_flag, jdbcType=NUMERIC} ,
		    QUESTION_ORDER = #{question_order, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    QUESTION_ID = #{question_id, jdbcType=NUMERIC} ,
			QUESTION_CODE = #{question_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SURVEY_TEMPLATE_ID = #{survey_template_id, jdbcType=NUMERIC} ,
		    QUESTION_TYPE = #{question_type, jdbcType=NUMERIC} ,
		    QUESTION_ANSWER_NUM = #{question_answer_num, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSurveyQuestionByQuestionId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_QUESTION, A.TOP_QUESTION_FLAG, A.QUESTION_ORDER, A.QUESTION_ID, A.QUESTION_CODE, 
			A.SURVEY_TEMPLATE_ID, A.QUESTION_TYPE, A.QUESTION_ANSWER_NUM FROM APP___PAS__DBUSER.T_SURVEY_QUESTION A WHERE 1 = 1  ]]>
		<include refid="PA_querySurveyQuestionByQuestionIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSurveyQuestion" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_QUESTION, A.TOP_QUESTION_FLAG, A.QUESTION_ORDER, A.QUESTION_ID, A.QUESTION_CODE, 
			A.SURVEY_TEMPLATE_ID, A.QUESTION_TYPE, A.QUESTION_ANSWER_NUM FROM APP___PAS__DBUSER.T_SURVEY_QUESTION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSurveyQuestion" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_QUESTION, A.TOP_QUESTION_FLAG, A.QUESTION_ORDER, A.QUESTION_ID, A.QUESTION_CODE, 
			A.SURVEY_TEMPLATE_ID, A.QUESTION_TYPE, A.QUESTION_ANSWER_NUM FROM APP___PAS__DBUSER.T_SURVEY_QUESTION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSurveyQuestionTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SURVEY_QUESTION A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySurveyQuestionForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SURVEY_QUESTION, B.TOP_QUESTION_FLAG, B.QUESTION_ORDER, B.QUESTION_ID, B.QUESTION_CODE, 
			B.SURVEY_TEMPLATE_ID, B.QUESTION_TYPE, B.QUESTION_ANSWER_NUM FROM (
					SELECT ROWNUM RN, A.SURVEY_QUESTION, A.TOP_QUESTION_FLAG, A.QUESTION_ORDER, A.QUESTION_ID, A.QUESTION_CODE, 
			A.SURVEY_TEMPLATE_ID, A.QUESTION_TYPE, A.QUESTION_ANSWER_NUM FROM APP___PAS__DBUSER.T_SURVEY_QUESTION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
