<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBankAccountDao">

	<sql id="PA_bankAccountWhereCondition">
		<if test=" account_status != null and account_status != ''  "><![CDATA[ AND A.ACCOUNT_STATUS = #{account_status} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" issue_bank_name != null and issue_bank_name != ''  "><![CDATA[ AND <PERSON>.ISSUE_BANK_NAME = #{issue_bank_name} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" account_type  != null "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
		<if test=" verified_flag  != null "><![CDATA[ AND A.VERIFIED_FLAG = #{verified_flag} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" verified_pass_flag  != null "><![CDATA[ AND A.VERIFIED_PASS_FLAG = #{verified_pass_flag} ]]></if>
		<if test=" certi_end_date  != null  and  certi_end_date  != ''  "><![CDATA[ AND A.CERTI_END_DATE = #{certi_end_date} ]]></if>
		<if test=" certi_start_date  != null  and  certi_start_date  != ''  "><![CDATA[ AND A.CERTI_START_DATE = #{certi_start_date} ]]></if>
		<if test=" cause_desc != null and cause_desc != ''  "><![CDATA[ AND A.CAUSE_DESC = #{cause_desc} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" confirmed  != null "><![CDATA[ AND A.CONFIRMED = #{confirmed} ]]></if>
		<if test=" bank_account_city != null and bank_account_city != ''  "><![CDATA[ AND A.BANK_ACCOUNT_CITY = #{bank_account_city} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" acco_name != null and acco_name != ''  "><![CDATA[ AND A.ACCO_NAME = #{acco_name} ]]></if>
		<if test=" correspondent_no != null and correspondent_no != ''  "><![CDATA[ AND A.CORRESPONDENT_NO = #{correspondent_no} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryBankAccountByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByBankCodeCondition">
		<if test=" bank_code != null and bank_code != '' "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByBankAccountCondition">
		<if test=" bank_account != null and bank_account != '' "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByAccountTypeCondition">
		<if test=" account_type != null and account_type != '' "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByAccoNameCondition">
		<if test=" acco_name != null and acco_name != '' "><![CDATA[ AND A.ACCO_NAME = #{acco_name} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByCertiCodeCondition">
		<if test=" certi_code != null and certi_code != '' "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" modnum != null and modnum != '' and start != null and start != '' ">
		              <![CDATA[ and mod(A.account_id,#{modnum}) = #{start} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByAccountStatusCondition">
		<if test=" account_status != null and account_status != '' "><![CDATA[ AND A.ACCOUNT_STATUS = #{account_status} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addBankAccount"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="account_id">
         select APP___PAS__DBUSER.S_BANK_ACCOUNT.NEXTVAL FROM DUAL
      </selectKey>
      <![CDATA[
        INSERT INTO APP___PAS__DBUSER.T_BANK_ACCOUNT             (
           ACCOUNT_STATUS, BANK_ACCOUNT, ACCOUNT_ID, ISSUE_BANK_NAME, CUSTOMER_ID, ACCOUNT_TYPE, VERIFIED_FLAG, 
           INSERT_TIMESTAMP, CERTI_CODE, UPDATE_BY, VERIFIED_PASS_FLAG, INSERT_TIME, CERTI_END_DATE, CERTI_START_DATE, 
           UPDATE_TIME, CAUSE_DESC, SUSPEND_CAUSE, BANK_CODE, BRANCH_CODE, CONFIRMED, BANK_ACCOUNT_CITY, 
           UPDATE_TIMESTAMP, INSERT_BY, CERTI_TYPE, ACCO_NAME, SAFE_ACCOUNT_TYPE,CORRESPONDENT_NO) 
        VALUES (
           #{account_status, jdbcType=VARCHAR}, #{bank_account, jdbcType=VARCHAR} ,#{account_id, jdbcType=NUMERIC} , #{issue_bank_name, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{account_type, jdbcType=NUMERIC} , #{verified_flag, jdbcType=NUMERIC} 
          , CURRENT_TIMESTAMP, #{certi_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{verified_pass_flag, jdbcType=NUMERIC} , SYSDATE , #{certi_end_date, jdbcType=DATE} , #{certi_start_date, jdbcType=DATE} 
           , SYSDATE , #{cause_desc, jdbcType=VARCHAR} , #{suspend_cause, jdbcType=VARCHAR} , #{bank_code, jdbcType=VARCHAR} , #{branch_code, jdbcType=VARCHAR} , #{confirmed, jdbcType=NUMERIC} , #{bank_account_city, jdbcType=VARCHAR} 
          , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{certi_type, jdbcType=VARCHAR} , #{acco_name, jdbcType=VARCHAR} , #{safe_account_type, jdbcType=VARCHAR}, #{correspondent_no, jdbcType=VARCHAR} ) 
       ]]>
   </insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteBankAccount" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BANK_ACCOUNT WHERE ACCOUNT_ID = #{account_id} ]]>
	</delete>
<!-- 删除操作by：BANK_CODE, BANK_ACCOUNT, CUSTOMER_ID联合主键 -->	
	<delete id="PA_deleteBankAccountByUnique" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BANK_ACCOUNT WHERE BANK_CODE = #{bank_code} AND  
					BANK_ACCOUNT = #{bank_account} AND CUSTOMER_ID = #{customer_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateBankAccount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BANK_ACCOUNT ]]>
		<set>
		<trim suffixOverrides=",">
			ACCOUNT_STATUS = #{account_status, jdbcType=VARCHAR} ,
			BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
			ISSUE_BANK_NAME = #{issue_bank_name, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    ACCOUNT_TYPE = #{account_type, jdbcType=NUMERIC} ,
		    VERIFIED_FLAG = #{verified_flag, jdbcType=NUMERIC} ,
			CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    VERIFIED_PASS_FLAG = #{verified_pass_flag, jdbcType=NUMERIC} ,
		    CERTI_END_DATE = #{certi_end_date, jdbcType=DATE} ,
		    CERTI_START_DATE = #{certi_start_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			CAUSE_DESC = #{cause_desc, jdbcType=VARCHAR} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
		    CONFIRMED = #{confirmed, jdbcType=NUMERIC} ,
			BANK_ACCOUNT_CITY = #{bank_account_city, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CERTI_TYPE = #{certi_type, jdbcType=VARCHAR} ,
			ACCO_NAME = #{acco_name, jdbcType=VARCHAR} ,
			SAFE_ACCOUNT_TYPE = #{safe_account_type, jdbcType=VARCHAR},
			CORRESPONDENT_NO = #{correspondent_no, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE ACCOUNT_ID = #{account_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findBankAccountByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccountIdCondition" />
	</select>
	
	<select id="findBankAccountByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A where A.CERTI_START_DATE is not null and rownum = 1  ]]>
		<include refid="PA_queryBankAccountByCustomerIdCondition" />
	</select>
	
	<select id="PA_findBankAccountByBankCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByBankCodeCondition" />
	</select>
	
	<select id="PA_findBankAccountByBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByBankAccountCondition" />
	</select>
	
	<select id="PA_findBankAccountByAccountType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccountTypeCondition" />
	</select>
	
	<select id="PA_findBankAccountByAccoName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccoNameCondition" />
	</select>
	
	<select id="PA_findBankAccountByCertiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByCertiCodeCondition" />
	</select>
	
	<select id="PA_findBankAccountByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByCustomerIdCondition" />
	</select>
	
	<select id="PA_findBankAccountByAccountStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccountStatusCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findBankAccountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryBankAccountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCOUNT_STATUS, B.BANK_ACCOUNT, B.ACCOUNT_ID, B.ISSUE_BANK_NAME, B.CUSTOMER_ID, B.ACCOUNT_TYPE, B.VERIFIED_FLAG, 
			B.CERTI_CODE, B.VERIFIED_PASS_FLAG, B.CERTI_END_DATE, B.CERTI_START_DATE, 
			B.CAUSE_DESC, B.SUSPEND_CAUSE, B.BANK_CODE, B.BRANCH_CODE, B.CONFIRMED, B.BANK_ACCOUNT_CITY, 
			B.CERTI_TYPE, B.ACCO_NAME , B.SAFE_ACCOUNT_TYPE  FROM (
					SELECT ROWNUM RN, A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME  , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_bankAccountWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 	查找满足条件的对象 -->
	<select id="PA_findBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE ROWNUM = 1  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>
	<select id="PA_findBankAccount1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME , A.SAFE_ACCOUNT_TYPE,A.CORRESPONDENT_NO  FROM APP___PAS__DBUSER.T_BANK_ACCOUNT A WHERE ROWNUM <=  1000  and a.bank_account=#{bank_account} and a.bank_code=#{bank_code}]]>
		
	</select>
	<select id="findBankAccountByCustomerId_cjk" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[	SELECT A.ACCOUNT_BANK,A.NEXT_ACCOUNT_BANK,A.ACCOUNT_NAME,
              A.NEXT_ACCOUNT_NAME,A.ACCOUNT,A.NEXT_ACCOUNT FROM DEV_PAS.T_PAYER_ACCOUNT 
               A WHERE A.POLICY_ID=#{policy_id}

 ]]>

  </select>
	
	<!-- 账户安全等级相关SQL  start-->
	<!-- 客户作为投保人的保单，首期保费划款成功的银行账号 ,最后一行Cause_Desc以后修改为安全等级字段-->
	<select id="findBankAccountLevel1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME
  from APP___PAS__DBUSER.t_bank_account A
 where exists (select tpa.account from APP___PAS__DBUSER.t_payer_account tpa where tpa.account=A.BANK_ACCOUNT  
        and tpa.insert_time >= #{certi_end_date,jdbcType = DATE}) 
        AND ( A.SAFE_ACCOUNT_TYPE is null or A.SAFE_ACCOUNT_TYPE<1)  
        AND ROWNUM <=  2000
           ]]>
           <include refid="PA_queryBankAccountByCustomerIdCondition" />
	</select>
	
	<!-- 通过柜面认证绑定的银行账号 -->
	<select id="findBankAccountLevel2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.ACCOUNT_STATUS,
			       A.BANK_ACCOUNT,
			       A.ACCOUNT_ID,
			       A.ISSUE_BANK_NAME,
			       A.CUSTOMER_ID,
			       A.ACCOUNT_TYPE,
			       A.VERIFIED_FLAG,
			       A.CERTI_CODE,
			       A.VERIFIED_PASS_FLAG,
			       A.CERTI_END_DATE,
			       A.CERTI_START_DATE,
			       A.CAUSE_DESC,
			       A.SUSPEND_CAUSE,
			       A.BANK_CODE,
			       A.BRANCH_CODE,
			       A.CONFIRMED,
			       A.BANK_ACCOUNT_CITY,
			       A.CERTI_TYPE,
			       A.ACCO_NAME
			  FROM APP___PAS__DBUSER.t_bank_account A
			 WHERE 1=1 AND (A.SAFE_ACCOUNT_TYPE is null or A.SAFE_ACCOUNT_TYPE<2)
			 AND EXISTS
     		(select 1 from APP___PAS__DBUSER.T_CS_AUTHENTICATION AU
              where AU.bank_account=A.bank_account and AU.customer_id=A.customer_id and exists
              (select 1 from APP___PAS__DBUSER.t_Cs_Accept_Change ta where ta.accept_code=AU.accept_code AND ta.accept_status = 18
                                        AND ta.validate_time >= #{certi_end_date,jdbcType = DATE}))
                                        AND ROWNUM <=  2000
           ]]>
           <include refid="PA_queryBankAccountByCustomerIdCondition" />
	</select>
	<!-- 该客户作为投保人的保单，两次（含）以上非首期（续期）保费划款成功且户名与投保人一致的银行账号 -->
	<select id="findBankAccountLevel3" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME
  from APP___PAS__DBUSER.t_bank_account A
 where 1=1 AND ( A.SAFE_ACCOUNT_TYPE is null or A.SAFE_ACCOUNT_TYPE<3 ) 
 AND  exists (select 1
  from APP___PAS__DBUSER.t_prem_arap t
 where 1=1
   and t.bank_account = A.bank_account
   and t.customer_id = A.customer_id
   and t.deriv_type = 003
   and t.fee_type ='G003010000'
   and t.fee_status in ('01','19')
   and t.finish_time >= #{certi_end_date,jdbcType = DATE}
   and exists(
       select 1 
    from APP___PAS__DBUSER.t_prem_arap t1
     where t1.deriv_type = 003
     and t1.fee_status in ('01','19')
      and t1.fee_type ='G003010000'
     and t1.bank_account = t.bank_account 
     and t1.customer_id = t.customer_id
     and t1.unit_number != t.unit_number   
   )   
 )  
 AND ROWNUM <=  2000
 ]]>
 <include refid="PA_queryBankAccountByCustomerIdCondition" />
	</select>
	
	<!-- 客户作为申请资格人，亲办业务发生过保全付费两次以上，且均付费成功的银行账号。
	付费保全项目包括：退保、年金满期金领取、贷款、续贷（收费和付费）、客户账户领取、账户部分领取 -->
	<select id="findBankAccountLevel4" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME
  from APP___PAS__DBUSER.t_bank_account A
 where exists (select 1 from APP___PAS__DBUSER.t_prem_arap t
         where  t.bank_account = A.bank_account
         	and 1 = 1
           and (t.fee_status='01' or t.fee_status='19')
           and t.arap_flag='2'
           and t.bank_account is not null
           and  exists(select 1 from APP___PAS__DBUSER.t_cs_accept_change ac where ac.accept_code= t.business_code and exists(select 1 from APP___PAS__DBUSER.t_cs_application ap where ap.service_type='1'))
           and t.finish_time >=  #{certi_end_date,jdbcType = DATE}
           and exists (
               select 1 from APP___PAS__DBUSER.t_prem_arap t1 where
                1 = 1
               and (t1.fee_status='01' or t1.fee_status='19')
               and t1.arap_flag='2'
               and t1.bank_account is not null
               and  exists(select 1 from APP___PAS__DBUSER.t_cs_accept_change ac1 where ac1.accept_code= t1.business_code and exists(select 1 from APP___PAS__DBUSER.t_cs_application ap1 where ap1.service_type='1'))
               and t1.bank_account = t.bank_account 
               and t1.business_code != t.business_code 
           ) ) 
           and ( A.SAFE_ACCOUNT_TYPE is null or A.SAFE_ACCOUNT_TYPE<4)
           AND ROWNUM <=  2000
             ]]>
           <include refid="PA_queryBankAccountByCustomerIdCondition" />
       </select>
    <!-- 通过远程认证时绑定的银行账号 -->
     <select id="findBankAccountLevel5" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME
  from APP___PAS__DBUSER.t_bank_account A
 where exists (select tp.customer_id
          from APP___PAS__DBUSER.t_policy_holder tp
         where tp.customer_id = A.customer_id)
   and exists (select ta.bank_account
          from APP___PAS__DBUSER.t_prem_arap ta
         where ta.fee_status = 01
           and ta.deriv_type = 001
           and ta.bank_account = A.bank_account 
           and ta.finish_time= #{certi_end_date, jdbcType=DATE})  ]]>
           <include refid="PA_queryBankAccountByCustomerIdCondition" />
	</select>
	
	<!-- 批量更新客户风险等级 -->
	<update id="PA_updateBankAccountLevel" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BANK_ACCOUNT ]]>
		<set>
		<trim suffixOverrides=",">
			SAFE_ACCOUNT_TYPE = #{safe_account_type, jdbcType=VARCHAR} ,
			UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
	        UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE ACCOUNT_ID = #{account_id} ]]>
	</update>
	
	<!-- 账户安全等级相关SQL  end-->
	
	    <!-- 技术需求任务 #144538掌上新华二期-贷款信息查询功能（需求15）-个险新核心-保单 start-->
     <select id="PA_queryBankAccountInfoByAutoAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT L.BANK_CODE, L.ACCO_NAME, L.BANK_ACCOUNT
  FROM DEV_PAS.t_Bank_Account L, DEV_PAS.t_Bank B
 WHERE 1 = 1
   AND L.BANK_CODE = B.BANK_CODE  ]]>
          <if test=" account_id  != null "><![CDATA[ AND L.ACCOUNT_ID = #{account_id} ]]></if>
	</select>
	   <!-- 技术需求任务 #144538掌上新华二期-贷款信息查询功能（需求15）-个险新核心-保单 end-->
	
	
	
</mapper>
