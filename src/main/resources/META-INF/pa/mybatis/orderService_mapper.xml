<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IOrderServiceDao">
    
    <!-- 查询操作 -->
	<select id="PA_orderNoQuery" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM APP___PAS__DBUSER.T_ORDER_SERVICE T 
		WHERE T.SERVICE_ORDER_ID =#{service_order_id} ]]>
	</select>
</mapper>