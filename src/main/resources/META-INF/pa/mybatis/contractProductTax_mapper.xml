<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ContractProductTaxDaoImpl">

	<sql id="contractProductTaxWhereCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" tax_next_totalprem  != null "><![CDATA[ AND A.TAX_NEXT_TOTALPREM = #{tax_next_totalprem} ]]></if>
		<if test=" tax_init_totalprem  != null "><![CDATA[ AND A.TAX_INIT_TOTALPREM = #{tax_init_totalprem} ]]></if>
		<if test=" appoint_date != null and appoint_date != ''  "><![CDATA[ AND A.APPOINT_DATE = #{appoint_date} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" paydate_type != null and paydate_type != ''  "><![CDATA[ AND A.PAYDATE_TYPE = #{paydate_type} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" product_rate  != null "><![CDATA[ AND A.PRODUCT_RATE = #{product_rate} ]]></if>
		<if test=" tax_init_prem  != null "><![CDATA[ AND A.TAX_INIT_PREM = #{tax_init_prem} ]]></if>
		<if test=" charge_rate  != null "><![CDATA[ AND A.CHARGE_RATE = #{charge_rate} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryContractProductTaxByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="queryContractProductTaxByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="queryContractProductTaxByBusiPrdIdCondition">
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
	</sql>	
	<sql id="queryContractProductTaxByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryContractProductTaxByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addContractProductTax"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX(
				PRODUCT_ID, TAX_NEXT_TOTALPREM, TAX_INIT_TOTALPREM, INSERT_TIME, APPOINT_DATE, BUSI_PRD_ID, UPDATE_TIME, 
				ITEM_ID, APPLY_CODE, INSERT_TIMESTAMP, PAYDATE_TYPE, POLICY_CODE, PRODUCT_RATE, UPDATE_BY, 
				TAX_INIT_PREM, CHARGE_RATE, UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, POLICY_ID ) 
			VALUES (
				#{product_id, jdbcType=NUMERIC}, #{tax_next_totalprem, jdbcType=NUMERIC} , #{tax_init_totalprem, jdbcType=NUMERIC} , SYSDATE , #{appoint_date, jdbcType=VARCHAR} , #{busi_prd_id, jdbcType=NUMERIC} , SYSDATE 
				, #{item_id, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{paydate_type, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{product_rate, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} 
				, #{tax_init_prem, jdbcType=NUMERIC} , #{charge_rate, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteContractProductTax" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX WHERE ITEM_ID = #{item_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateContractProductTax" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX ]]>
		<set>
		<trim suffixOverrides=",">
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    TAX_NEXT_TOTALPREM = #{tax_next_totalprem, jdbcType=NUMERIC} ,
		    TAX_INIT_TOTALPREM = #{tax_init_totalprem, jdbcType=NUMERIC} ,
			APPOINT_DATE = #{appoint_date, jdbcType=VARCHAR} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			PAYDATE_TYPE = #{paydate_type, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    PRODUCT_RATE = #{product_rate, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    TAX_INIT_PREM = #{tax_init_prem, jdbcType=NUMERIC} ,
		    CHARGE_RATE = #{charge_rate, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findContractProductTaxByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE 1 = 1  ]]>
		<include refid="queryContractProductTaxByItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	
	<select id="findContractProductTaxByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE 1 = 1  ]]>
		<include refid="queryContractProductTaxByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	
	<select id="findContractProductTaxByBusiPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE 1 = 1  ]]>
		<include refid="queryContractProductTaxByBusiPrdIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	
	<select id="findContractProductTaxByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE 1 = 1  ]]>
		<include refid="queryContractProductTaxByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	
	<select id="findContractProductTaxByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE 1 = 1  ]]>
		<include refid="queryContractProductTaxByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapContractProductTax" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE ROWNUM <=  1000  ]]>
		 <include refid="contractProductTaxWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllContractProductTax" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE ROWNUM <=  1000  ]]>
		 <include refid="contractProductTaxWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]> 
	</select>
<!-- 查询单条 -->
	<select id="findContractProductTax" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE ROWNUM <=  1000  ]]>
		 <include refid="contractProductTaxWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]> 
	</select>
<!-- 查询个数操作 -->
	<select id="findContractProductTaxTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE 1 = 1  ]]>
		 <include refid="contractProductTaxWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryContractProductTaxForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRODUCT_ID, B.TAX_NEXT_TOTALPREM, B.TAX_INIT_TOTALPREM, B.APPOINT_DATE, B.BUSI_PRD_ID, 
			B.ITEM_ID, B.APPLY_CODE, B.PAYDATE_TYPE, B.POLICY_CODE, B.PRODUCT_RATE, 
			B.TAX_INIT_PREM, B.CHARGE_RATE, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.PRODUCT_ID, A.TAX_NEXT_TOTALPREM, A.TAX_INIT_TOTALPREM, A.APPOINT_DATE, A.BUSI_PRD_ID, 
			A.ITEM_ID, A.APPLY_CODE, A.PAYDATE_TYPE, A.POLICY_CODE, A.PRODUCT_RATE, 
			A.TAX_INIT_PREM, A.CHARGE_RATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_TAX A WHERE ROWNUM <= #{LESS_NUM} ]]>
		 <include refid="contractProductTaxWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
