<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.batch.sendnote.dao.impl.SendNoteAnnuityDaoImpl">
	<sql id="findOrganCodeInfo">
		<if test="organ_code != null and organ_code != ''">
        	AND TCM.ORGAN_CODE IN (
			    	SELECT T.ORGAN_CODE
				    FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
				    START WITH T.ORGAN_CODE = #{organ_code}
				    CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE
				    GROUP BY ORGAN_CODE)
		</if>
	</sql>
	<!-- 查询生存金首次领取待处理的数据 -->
	<select id="PA_findAllAnnuityBegin" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM RN,T.* FROM (SELECT DISTINCT TPP.POLICY_CODE  AS POLICY_NUM,
       TC.CUSTOMER_NAME,
       TC.CUSTOMER_GENDER,
       NVL(TC.MOBILE_TEL,
           (SELECT T.MOBILE_TEL
              FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_ADDRESS T
             WHERE TPH.POLICY_CODE = TCM.POLICY_CODE
               AND TPH.ADDRESS_ID = T.ADDRESS_ID)) AS MOBILE,
			       TCM.ORGAN_CODE ORGAN_CODE,
			       SUBSTR(TCM.ORGAN_CODE,0,6) ZHI_ORGAN_CODE,
			       SUBSTR(TCM.ORGAN_CODE,0,4) BRANCH_CODE,
			       TC.CUSTOMER_ID
			  FROM DEV_PAS.T_PAY_PLAN        TPP,
			  	   DEV_PAS.T_PAY_DUE TPD,
			       DEV_PAS.T_CONTRACT_MASTER TCM,
			       DEV_PAS.T_INSURED_LIST    TIL,
			       DEV_PAS.T_CUSTOMER        TC
			 WHERE TPP.POLICY_ID = TPD.POLICY_ID
			   AND TPP.POLICY_CODE = TCM.POLICY_CODE
			   AND TIL.POLICY_CODE = TCM.POLICY_CODE
			   AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			   AND TCM.LIABILITY_STATE = '1'
			   AND TPP.BEGIN_DATE=TPP.PAY_DUE_DATE
			   AND TPP.PAY_PLAN_TYPE IN('2','3','5','8','10','11') 
			   AND TPD.FEE_STATUS = '00'
			   AND TPP.SURVIVAL_INVEST_FLAG='1'
   ]]>
   <if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TPP.BEGIN_DATE = #{batch_time,jdbcType=DATE}]]></if>
   <![CDATA[) T]]>
	</select>
	
	<!-- 查询年金、满期金领取提醒的数据 -->
	<select id="PA_findAllAnnuityfullterpaumentBeginPolicy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM RN, A.* FROM (SELECT TPD.POLICY_CODE AS POLICY_NUM
           FROM APP___PAS__DBUSER.T_PAY_DUE            TPD,
                APP___PAS__DBUSER.T_PAY_PLAN           TPP,
                APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
                APP___PAS__DBUSER.T_CUSTOMER           TC,
                APP___PAS__DBUSER.T_ADDRESS            TAAC,
                APP___PAS__DBUSER.T_POLICY_HOLDER       TIL,
                APP___PAS__DBUSER.T_CONTRACT_AGENT     TBC,
                APP___PAS__DBUSER.T_AGENT              TAC
          WHERE TPD.POLICY_ID = TCM.POLICY_ID
            AND TPD.PLAN_ID = TPP.PLAN_ID
            AND TPD.POLICY_ID = TIL.POLICY_ID
            AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
            AND TAAC.ADDRESS_ID = TIL.ADDRESS_ID
			AND TN.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
            AND TCM.POLICY_CODE = TN.POLICY_CODE
            AND TPD.POLICY_ID = TBC.POLICY_ID
            AND TBC.AGENT_CODE = TAC.AGENT_CODE
            AND TBC.IS_CURRENT_AGENT = '1'
            AND (TPD.DEDUCTION_FLAG IS NULL OR TPD.DEDUCTION_FLAG = '1')
            AND TPD.FEE_STATUS = '00' ]]>
   <if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TPD.Pay_Due_Date = #{batch_time,jdbcType=DATE}]]></if> 
   <if test=" ORGAN_CODE != null and ORGAN_CODE != ''  "><![CDATA[AND TCM.ORGAN_CODE LIKE #{ORGAN_CODE,jdbcType=VARCHAR}]]></if>
   <![CDATA[
			AND EXISTS
							 (SELECT 1
												FROM DEV_PAS.T_PAY_PLAN TPP
											 WHERE TPP.POLICY_ID = TCM.POLICY_ID
												 AND TPP.PLAN_ID = TPD.PLAN_ID
												 AND TPP.PAY_PLAN_TYPE IN ('2', '4', '3', '5', '8', '10', '11'))
							 GROUP BY TPD.POLICY_CODE)A 			 
   ]]>
	</select>
	
	<!-- 查询年金、满期金领取提醒的数据 -->
	<select id="PA_findAllAnnuityfullterpaumentBegin" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM RN, A.* FROM (SELECT TPD.POLICY_CODE AS POLICY_NUM,
               MAX(TPD.PAY_ID) payID,
               SUBSTR(TPD.POLICY_CODE, -4) POLICYFOUR,
               TCM.CHANNEL_TYPE,
               TC.CUSTOMER_NAME,
               TC.CUSTOMER_GENDER,
               TAAC.MOBILE_TEL MOBILE_TEL_BC,
               TC.MOBILE_TEL MOBILE_TEL_TC,
               TCM.ORGAN_CODE,
               SUBSTR(TCM.ORGAN_CODE, 0, 6) ZHI_ORGAN_CODE,
               SUBSTR(TCM.ORGAN_CODE, 0, 4) BRANCH_CODE,
               TC.CUSTOMER_ID,
               TC.WECHAT_NO,
               TC.EMAIL,
               TAC.AGENT_CHANNEL,
               TAC.AGENT_CODE,
               TAC.AGENT_NAME,
               TAC.AGENT_MOBILE,
               TAC.AGENT_STATUS,
               (Select max(tt.busi_prd_id)
                       From dev_pas.t_Contract_Busi_Prod a,
                            dev_pas.t_Contract_Busi_Prod tt
                      where a.busi_item_id = TPD.busi_item_id
                        and tt.busi_item_id =
                            decode(A.master_busi_item_id,
                                   null,
                                   A.busi_item_id,
                                   A.master_busi_item_id)) BUSIPRDID,
               TPD.Plan_Id,
               (CASE
                 WHEN TCM.Multi_Mainrisk_Flag = '1' then
                 decode((Select b.product_category1
                     From dev_pas.t_Contract_Busi_Prod t,
                          dev_pds.t_Business_Product   b,
                          dev_pas.t_Pay_Due p
                    where 1 = 1  
                      AND p.policy_code = tpd.policy_code
                      AND P.Pay_Due_Date = tpd.pay_due_date
                      AND P.Fee_Status = '00'
                      AND P.Survival_Invest_Flag = 1
                      and p.busi_item_id = t.busi_item_id
                      and T.policy_code = TPD.Policy_Code
                      and t.busi_prd_id = b.business_prd_id
                      and b.product_category1 = '20002'  and rownum = 1), null, '否', '是')
                 ELSE
                  CASE
                    WHEN (SELECT P.PRODUCT_CATEGORY1
                            FROM DEV_PAS.T_BUSINESS_PRODUCT P
                           WHERE P.PRODUCT_CODE_SYS = TN.BUSI_PROD_CODE) =
                         '20002' THEN
                     '是'
                    ELSE
                     '否'
                  END
               END) AS div_ins_flag, /*分红险标识*/
               (CASE
                 WHEN (SELECT count(*)
                         FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        PA,
                              APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM AST
                        WHERE PA.ACCOUNT_ID = AST.ACCOUNT_ID
                          AND PA.ACCOUNT_TYPE = '4'
                          AND PA.INTEREST_CAPITAL > '0'
                          AND AST.REGULAR_REPAY = '0'
                          AND PA.POLICY_ID = TPD.POLICY_ID
                          AND PA.POLICY_ACCOUNT_STATUS IN ('1', '3')) > 0 THEN
                  '是'
                 ELSE
                  '否'
               END) AS unli_flag, /*未清偿标识*/
               (SELECT sum(PA.INTEREST_CAPITAL)
                         FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        PA,
                              APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM AST
                        WHERE PA.ACCOUNT_ID = AST.ACCOUNT_ID
                          AND PA.ACCOUNT_TYPE = '4'
                          AND PA.INTEREST_CAPITAL > '0'
                          AND AST.REGULAR_REPAY = '0'
                          AND PA.POLICY_ID = TPD.POLICY_ID
                          AND PA.POLICY_ACCOUNT_STATUS IN ('1', '3')) as unli_money,
               (case
                      when (Select tt.busi_prod_code
                              From dev_pas.t_Contract_Busi_Prod a,
                                   dev_pas.t_Contract_Busi_Prod tt,
                                   dev_pas.t_Constants_Info     b
                             where a.busi_item_id = TPD.busi_item_id
                               and tt.busi_item_id =
                                   decode(a.master_busi_item_id,
                                          null,
                                          a.busi_item_id,
                                          a.master_busi_item_id)
                               and tt.busi_prod_code = b.constants_value
                               and b.constants_key = 'BENE_IS_HOLDER_PROD' ) is null or TPP.Pay_Plan_Type = 4 then
                       '0'
                      else
                       '1'
                    end) getnameflag,
                    TPP.Pay_Plan_Type,
                     Decode(TCM.Multi_Mainrisk_Flag,
		                       null,
		                       0,
		                       TCM.Multi_Mainrisk_Flag) multi_mainrisk_lag,
		            (Select c.product_code_sys From dev_pds.t_Business_Product c where c.business_prd_id in (
                    	(Select Max(a.busi_prd_id) From dev_pas.t_Contract_Busi_Prod a where a.policy_code = TPD.Policy_code and a.master_busi_item_id is null ))) maincode,
                    (Select c.product_name_std From dev_pds.t_Business_Product c where c.business_prd_id in (
                    	(Select Max(a.busi_prd_id) From dev_pas.t_Contract_Busi_Prod a where a.policy_code = TPD.Policy_code and a.master_busi_item_id is null ))) mainsysname
		            
           FROM APP___PAS__DBUSER.T_PAY_DUE            TPD,
                APP___PAS__DBUSER.T_PAY_PLAN           TPP,
                APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
                APP___PAS__DBUSER.T_CUSTOMER           TC,
                APP___PAS__DBUSER.T_ADDRESS            TAAC,
                APP___PAS__DBUSER.T_POLICY_HOLDER       TIL,
                APP___PAS__DBUSER.T_CONTRACT_AGENT     TBC,
                APP___PAS__DBUSER.T_AGENT              TAC
          WHERE TPD.POLICY_ID = TCM.POLICY_ID
            AND TPD.PLAN_ID = TPP.PLAN_ID
            AND TPD.POLICY_ID = TIL.POLICY_ID
            AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
            AND TAAC.ADDRESS_ID = TIL.ADDRESS_ID
			AND TN.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
            AND TCM.POLICY_CODE = TN.POLICY_CODE
            AND TPD.POLICY_ID = TBC.POLICY_ID
            AND TBC.AGENT_CODE = TAC.AGENT_CODE
            AND TBC.IS_CURRENT_AGENT = '1'
            AND (TPD.DEDUCTION_FLAG IS NULL OR TPD.DEDUCTION_FLAG = '1')
            AND TPD.FEE_STATUS = '00' ]]>
   <if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TPD.Pay_Due_Date = #{batch_time,jdbcType=DATE}]]></if> 
   <if test=" policy_code != null and policy_code != ''  "><![CDATA[AND TPD.POLICY_CODE = #{policy_code}]]></if>
   <if test=" ORGAN_CODE != null and ORGAN_CODE != ''  "><![CDATA[AND TCM.ORGAN_CODE LIKE #{ORGAN_CODE,jdbcType=VARCHAR}]]></if>
   <![CDATA[
			AND EXISTS
							 (SELECT 1
												FROM DEV_PAS.T_PAY_PLAN TPP
											 WHERE TPP.POLICY_ID = TCM.POLICY_ID
												 AND TPP.PLAN_ID = TPD.PLAN_ID
												 AND TPP.PAY_PLAN_TYPE IN ('2', '4', '3', '5', '8', '10', '11'))
							 GROUP BY TPD.POLICY_CODE,TCM.CHANNEL_TYPE,TC.CUSTOMER_NAME,TC.CUSTOMER_GENDER,
							 TAAC.MOBILE_TEL,TC.MOBILE_TEL,TCM.ORGAN_CODE,TC.CUSTOMER_ID,TC.WECHAT_NO,TC.EMAIL,TAC.AGENT_CHANNEL,TAC.AGENT_CODE,
							 TAC.AGENT_NAME,TAC.AGENT_MOBILE,TAC.AGENT_STATUS,TN.BUSI_PROD_CODE,TCM.Multi_Mainrisk_Flag,TPP.Pay_Plan_Type,
                  			 TPD.pay_due_date,TPD.survival_invest_flag,TPD.busi_item_id,TPD.Plan_Id,TPD.POLICY_ID)A 			 
   ]]>
	</select>
	
	<!-- 生存金自动划款失败短信提醒 -->
	<select id="PA_sendNoteDelimiFail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT ROWNUM RN,T.* FROM (SELECT B.POLICY_CODE,
			       SUM(B.FEE_AMOUNT) AS FEE_AMOUNT,
			       B.CUSTOMER_NAME,
			       B.CUSTOMER_ID,
			       B.CUSTOMER_GENDER,
			       B.MOBILE_TEL,
			       B.HOLDER_MOBILE_TEL,
			       B.BANK_RET_NAME,
			       B.BANK_NAME,
			       B.BANK_ACCOUNT,
			       SUBSTR(B.BANK_ACCOUNT,-4) LAST_FOUR,
			       B.BRANCH_CODE,
			       B.ZHI_ORGAN_CODE,
			       B.ORGAN_CODE
			  FROM (SELECT A.POLICY_CODE,
			               A.FEE_AMOUNT,
			               TC.CUSTOMER_NAME,
			               TC.CUSTOMER_ID,
			               TC.CUSTOMER_GENDER,
			               (SELECT TA1.MOBILE_TEL FROM DEV_PAS.T_ADDRESS TA1 WHERE TA1.ADDRESS_ID = TIL.ADDRESS_ID) MOBILE_TEL,
			               (SELECT TA2.MOBILE_TEL FROM DEV_PAS.T_ADDRESS TA2, DEV_PAS.T_POLICY_HOLDER TPH 
			               WHERE TPH.POLICY_CODE = TIL.POLICY_CODE AND TA2.ADDRESS_ID = TPH.ADDRESS_ID) HOLDER_MOBILE_TEL,
			               BC.BANK_RET_NAME,
			               TB.BANK_NAME,
			               A.BANK_ACCOUNT,
			               A.ORGAN_CODE,
			               SUBSTR(A.ORGAN_CODE, 0, 4) AS BRANCH_CODE,
			               SUBSTR(A.ORGAN_CODE, 0, 6) AS ZHI_ORGAN_CODE
			          FROM DEV_PAS.T_PREM_ARAP     A,
			               DEV_PAS.T_INSURED_LIST  TIL,
			               DEV_PAS.T_CUSTOMER      TC,
			               DEV_PAS.T_BANK_RET_CONF BC,
			               DEV_PAS.T_PAY_DUE TPA,
           				   DEV_PAS.T_PAY_PLAN TPP,
			               DEV_PAS.T_BANK  TB,
			               DEV_PAS.T_BENEFIT_INSURED TBI
			         WHERE 
			           A.UNIT_NUMBER = TPA.UNIT_NUMBER
			           AND TPA.PLAN_ID = TPP.PLAN_ID
			           AND A.POLICY_CODE = TIL.POLICY_CODE
			           AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			           AND BC.BANK_RET_CODE = A.FUNDS_RTN_CODE
			           AND A.BANK_CODE = TB.BANK_CODE
			           AND TBI.INSURED_ID = TIL.LIST_ID
			           AND TBI.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
				       AND A.DERIV_TYPE !='004'
			           and A.FEE_STATUS = '03'
			           AND A.BUSINESS_TYPE = '2002'
				       AND TPP.SURVIVAL_INVEST_FLAG = '0' 
				       AND TPP.PAY_NUM>1 
			           AND A.PAY_MODE='32'    
				       AND A.FAIL_TIMES = '1'
				       AND TBI.ORDER_ID = '1'
				       AND TPP.PAY_PLAN_TYPE IN('2','3','5','8','10','11') 
			           ]]>
					<if test=" batch_time != null and batch_time != ''  ">
						<![CDATA[
						and  ((SELECT min(af.update_time)
                          		FROM dev_cap.t_bank_text_group af
                        	 where af.bank_text_status = 8
                           	   and af.unit_number = a.unit_number
                         	) >= to_date(#{batch_time,jdbcType=DATE},'yyyy-MM-dd')
                        and  (SELECT min(af.update_time)
                          		FROM dev_cap.t_bank_text_group af
                         		where af.bank_text_status = 8
                           			and af.unit_number = a.unit_number
                         ) <  to_date(#{batch_time,jdbcType=DATE},'yyyy-MM-dd')+1)
						]]>
					</if>
					<![CDATA[
			           ) B
			 GROUP BY B.POLICY_CODE,
			          B.CUSTOMER_NAME,
			          B.CUSTOMER_ID,
			          B.CUSTOMER_GENDER,
			          B.MOBILE_TEL,
			          B.BANK_RET_NAME,
			          B.BANK_NAME,
			          B.BANK_ACCOUNT,
			          B.BRANCH_CODE,
			          B.ZHI_ORGAN_CODE,
			          B.HOLDER_MOBILE_TEL,
			          B.ORGAN_CODE) T
		]]>
	</select>
	
	<!-- 生存金自动划款成功短信提醒 -->
	<select id="PA_senNoteDelimitSuccess" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM RN,T.* FROM (SELECT B.POLICY_CODE,
		       B.CUSTOMER_NAME,
		       B.CUSTOMER_GENDER,
		       B.MOBILE_TEL,
		       B.HOLDER_MOBILE_TEL,
		       SUM(B.FEE_AMOUNT) AS FEE_AMOUNT,
		       B.BANK_NAME,
		       B.BANK_ACCOUNT,
		       SUBSTR(B.BANK_ACCOUNT,-4) LAST_FOUR,
		       B.CUSTOMER_ID,
		       B.BRANCH_CODE,
		       B.SUPPORT_ORGCODE,
		       B.ORGAN_CODE
		  FROM (SELECT A.POLICY_CODE,
		               TC.CUSTOMER_NAME,
		               TC.CUSTOMER_GENDER,
		               (SELECT TA1.MOBILE_TEL FROM DEV_PAS.T_ADDRESS TA1 WHERE TA1.ADDRESS_ID = TIL.ADDRESS_ID) MOBILE_TEL,
		               (SELECT TA2.MOBILE_TEL FROM DEV_PAS.T_ADDRESS TA2, DEV_PAS.T_POLICY_HOLDER TPH 
		               WHERE TPH.POLICY_CODE = TIL.POLICY_CODE AND TA2.ADDRESS_ID = TPH.ADDRESS_ID) HOLDER_MOBILE_TEL,
		               A.FEE_AMOUNT,
		               TB.BANK_NAME,
		               TC.CUSTOMER_ID,
		               A.BANK_ACCOUNT,
		               A.ORGAN_CODE,
		               SUBSTR(A.ORGAN_CODE, 0, 4) AS BRANCH_CODE,
		               SUBSTR(A.ORGAN_CODE, 0, 6) AS SUPPORT_ORGCODE
		          FROM DEV_PAS.T_PREM_ARAP    A,
                   DEV_PAS.T_PAY_DUE TPD, 
                   DEV_PAS.T_PAY_PLAN TPP, 
                   DEV_PAS.T_INSURED_LIST TIL,
                   DEV_PAS.T_CUSTOMER     TC,
                   DEV_PAS.T_BANK         TB,
                   DEV_PAS.T_BENEFIT_INSURED TBI
             	WHERE 
                   A.UNIT_NUMBER = TPD.UNIT_NUMBER
                   AND TPD.PLAN_ID = TPP.PLAN_ID
                   AND TIL.POLICY_CODE = A.POLICY_CODE
                   AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                   AND TBI.INSURED_ID = TIL.LIST_ID
                   AND A.BANK_CODE = TB.BANK_CODE
                   AND TBI.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
                   AND TBI.ORDER_ID = '1'
                   and A.BUSINESS_TYPE = '2002'
                   AND A.FEE_STATUS  in('01','19')
                   AND A.PAY_MODE = '32' 
                   AND A.DERIV_TYPE !='004' 
                   AND TPP.SURVIVAL_INVEST_FLAG = '0'
                   AND TPP.PAY_NUM>1
                   AND TPP.PAY_PLAN_TYPE IN('2','3','5','8','10','11') 
                   and a.arap_flag = 2 
				]]>
				<if test=" batch_time != null and batch_time != ''  ">
					<![CDATA[
						AND A.FINISH_TIME>=#{batch_time}
						AND A.FINISH_TIME < #{batch_time}+1
					]]>
				</if>
				<![CDATA[
				 ) B
		 GROUP BY B.POLICY_CODE,
		          B.CUSTOMER_NAME,
		          B.CUSTOMER_GENDER,
		          B.MOBILE_TEL,
		          B.BANK_NAME,
		          B.BANK_ACCOUNT,
		          B.CUSTOMER_ID,
		          B.BRANCH_CODE,
		          B.SUPPORT_ORGCODE,
		          B.HOLDER_MOBILE_TEL,
		          B.ORGAN_CODE) T
		]]>
	</select>
	
	<!-- 未指定续期领取账户短信 -->
	<select id="PA_findPayDueDateAmount" resultType="java.util.Map"
		parameterType="java.util.Map"> 
		<![CDATA[
		SELECT ROWNUM RN,T.* FROM (SELECT B.POLICY_CODE,
        SUM(B.FEE_AMOUNT) AS FEE_AMOUNT,
        B.CUSTOMER_NAME,
        B.CUSTOMER_GENDER,
        B.CUSTOMER_ID,
        B.MOBILE_TEL,
       	B.HOLDER_MOBILE_TEL,
        B.BRANCH_CODE,
        B.SUPPORT_ORGCODE,
        B.MOBILE_BENE,
        B.ORGAN_CODE
   FROM (SELECT A.POLICY_CODE,
                A.FEE_AMOUNT,
                TC.CUSTOMER_NAME,
                TC.CUSTOMER_GENDER,
                TC.CUSTOMER_ID,
                TCM.ORGAN_CODE,
                NVL((SELECT T.MOBILE_TEL
                  FROM DEV_PAS.T_CONTRACT_BENE TCB, DEV_PAS.T_ADDRESS T
                  WHERE TCB.POLICY_CODE = TCM.POLICY_CODE AND TCB.SHARE_ORDER='1' AND ROWNUM=1
                  AND TCB.ADDRESS_ID = T.ADDRESS_ID),TC.MOBILE_TEL) AS MOBILE_BENE,
                (SELECT TA1.MOBILE_TEL FROM DEV_PAS.T_ADDRESS TA1 WHERE TA1.ADDRESS_ID = TIL.ADDRESS_ID) MOBILE_TEL,
               (SELECT TA2.MOBILE_TEL FROM DEV_PAS.T_ADDRESS TA2, DEV_PAS.T_POLICY_HOLDER TPH 
               WHERE TPH.POLICY_CODE = TIL.POLICY_CODE AND TA2.ADDRESS_ID = TPH.ADDRESS_ID) HOLDER_MOBILE_TEL,
                SUBSTR(TCM.ORGAN_CODE, 0, 4) AS BRANCH_CODE ,SUBSTR(TCM.ORGAN_CODE, 0, 6) AS SUPPORT_ORGCODE
           FROM DEV_PAS.T_PAY_DUE         A,
                dev_pas.t_insured_list    til,
                dev_pas.t_customer        TC,
                DEV_PAS.T_CONTRACT_MASTER TCM,
                dev_pas.T_BENEFIT_INSURED tbi
          WHERE A.SURVIVAL_MODE = '1'
            AND A.SURVIVAL_INVEST_FLAG = '0'
            AND A.FEE_STATUS = '00'
            AND A.POLICY_CODE = TCM.POLICY_CODE
            AND A.POLICY_CODE = TIL.POLICY_CODE
            AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
            AND TBI.INSURED_ID = TIL.LIST_ID
            AND TBI.BUSI_ITEM_ID = A.BUSI_ITEM_ID
            AND TBI.ORDER_ID = '1'
		]]>
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND A.PAY_DUE_DATE = #{batch_time,jdbcType=DATE}]]></if>
		<![CDATA[
		   ) B
  group by B.policy_code,
           B.CUSTOMER_NAME,
           B.CUSTOMER_GENDER,
           B.CUSTOMER_ID,
           B.MOBILE_TEL,
           B.BRANCH_CODE,
           B.SUPPORT_ORGCODE,
           B.HOLDER_MOBILE_TEL,
           B.MOBILE_BENE,
           B.ORGAN_CODE) T
		]]>
		</select>
		
		<!-- 投保人生日祝福短信提醒 -->
		<select id="PA_findHolderBirthday"  resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[
			SELECT ROWNUM RN,T.* FROM (SELECT TC.CUSTOMER_NAME,
               MAX(TC.CUSTOMER_BIRTHDAY) CUSTOMER_BIRTHDAY,
               MAX(TC.CUSTOMER_ID) CUSTOMER_ID,
               TA.MOBILE_TEL MOBILE_TEL,
               MAX(CASE WHEN TC.CUSTOMER_GENDER = 1 THEN 0 WHEN TC.CUSTOMER_GENDER = 2 THEN 1 ELSE 2  END ) CUSTOMER_GENDER,
               MAX(TPH.POLICY_CODE) POLICY_CODE
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH, 
          	   APP___PAS__DBUSER.T_CUSTOMER TC,
          	   APP___PAS__DBUSER.T_ADDRESS TA,
          	   APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
         WHERE TPH.POLICY_CODE = TCM.POLICY_CODE AND TPH.ADDRESS_ID = TA.ADDRESS_ID AND TA.CUSTOMER_ID = TC.CUSTOMER_ID
         AND TCM.LIABILITY_STATE = 1
			]]>
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND to_char(TC.CUSTOMER_BIRTHDAY,'mm-dd') = 
			to_char(#{batch_time,jdbcType=DATE},'mm-dd')]]></if>
		<if test=" policy_code != null and policy_code != ''  ">
			<![CDATA[
				AND TCM.POLICY_CODE = #{policy_code}
			]]>
		</if>
		<if test=" organ_code != null and organ_code != ''  ">
			<![CDATA[
				AND TCM.ORGAN_CODE LIKE #{organ_code} || '%'
			]]>
		</if>
		<![CDATA[
			   GROUP BY TC.CUSTOMER_NAME,
                  TA.MOBILE_TEL) T WHERE 1=1
		]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.CUSTOMER_ID , #{modnum}) = #{start}]]></if>
		</select>
		
		<!-- 保额分红短信 -->
		<select id="PA_sendInsuredDividend" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				SELECT TCM.POLICY_CODE,TBA.ALLOCATE_DATE,
				  TCM.ORGAN_CODE,
				  TC.CUSTOMER_ID,
				  TA.MOBILE_TEL,
				  TC.CUSTOMER_NAME,
				  TBA.VALID_AMOUNT VALID_AMOUNT,
				  TBA.STAND_AMOUNT STAND_AMOUNT,
			       TBA.BONUS_RATE BONUS_RATE,
			       (SELECT M.AGENT_CHANNEL FROM DEV_PAS.T_CONTRACT_AGENT T,DEV_PAS.T_AGENT M WHERE T.POLICY_CODE= TCM.POLICY_CODE
						    AND T.AGENT_CODE=M.AGENT_CODE AND T.IS_CURRENT_AGENT=1 AND ROWNUM=1
						 ) AGENT_CHANNEL,
			       (SELECT M.AGENT_CODE FROM DEV_PAS.T_CONTRACT_AGENT T,DEV_PAS.T_AGENT M WHERE T.POLICY_CODE= TCM.POLICY_CODE
	                  AND T.AGENT_CODE=M.AGENT_CODE AND T.IS_CURRENT_AGENT=1 AND ROWNUM=1
	               ) AGENT_CODE,
	               (SELECT M.GROUP_CODE FROM DEV_PAS.T_CONTRACT_AGENT T,DEV_PAS.T_AGENT M WHERE T.POLICY_CODE= TCM.POLICY_CODE
                      AND T.AGENT_CODE=M.AGENT_CODE AND T.IS_CURRENT_AGENT=1 AND ROWNUM=1
                     ) GROUP_CODE,
                     (SELECT (CASE
                 WHEN A.ORGAN_LEVEL_CODE = '1' THEN
                  NULL
                 WHEN A.ORGAN_LEVEL_CODE = '2' THEN
                  A.ORGAN_LEVEL_CODE
                 WHEN A.ORGAN_LEVEL_CODE = '3' THEN
                  (SELECT TSO.ORGAN_LEVEL_CODE
                     FROM DEV_PAS.T_SALES_ORGAN TSO
                    WHERE TSO.ORGAN_LEVEL_CODE = '2'
                      AND ROWNUM = 1
                    START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE
                   CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                 ELSE
                  A.ORGAN_LEVEL_CODE
               END) AREA
          FROM DEV_PAS.T_SALES_ORGAN    A,
               DEV_PAS.T_AGENT          TA,
               dev_pas.t_contract_agent tca
         WHERE TA.AGENT_CODE = tca.AGENT_CODE
           AND A.SALES_ORGAN_CODE = TA.SALES_ORGAN_CODE
           and tca.is_current_agent = '1'
           and tca.policy_id = tcm.policy_id
           AND ROWNUM = 1) DEPARTMENT,
				       
		   NVL((select SUM(APDTA.STAND_AMOUNT)+sum(APDTA.ORIGIN_BONUS_SA) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
              APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
              WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
              AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE
              AND APD.ITEM_ID=APDTA.ITEM_ID),0) KXZRFHQYRYXBE,
				      
		   NVL((select APDTA.Bonus_Rate from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
              APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
              WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
              AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE
              AND APD.ITEM_ID=APDTA.ITEM_ID and rownum=1),0) KXZRNDFHL,
				  TC.CUSTOMER_GENDER,
		          (
					(SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE=TA.STATE) ||
					(SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE=TA.CITY)  ||
					(SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE=TA.DISTRICT)  ||	
					 TA.ADDRESS				
				  ) APPNTADDRESS,    
		          TA.POST_CODE APPNTZIPCODE,  
		          TO_CHAR(TBA.RATE_RELEASE_DATE,'YYYY')-1 FISCALYEAR,
		          		          (SELECT TCC.CUSTOMER_NAME FROM APP___PAS__DBUSER.T_CUSTOMER TCC WHERE TCC.CUSTOMER_ID=TIL.CUSTOMER_ID) INSUREDNAME,
		          TO_CHAR(TCBP.VALIDATE_DATE,'yyyy"年"mm"月"dd"日"') CVALIDATE, 
		          (CASE WHEN (SELECT COUNT(*) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT STCP WHERE STCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
		                      AND STCP.IS_MASTER_ITEM=0)>0 THEN '是' ELSE '否' END) KXFLAG,
		          TCP.AMOUNT BASEAMNT1,
		          TCP.INITIAL_DISCNT_PREM_AF PREM,
		          TCE.POLICY_PERIOD PREMCOUNT,
		          TO_CHAR(TBA.ALLOCATE_DUE_DATE-1,'yyyy"年"mm"月"dd"日"') TPREDISPATCHDATE,  
		          TBA.ORIGIN_BONUS_SA LASTYEARYUMBONUSAMNT,
				  TBA.ORIGIN_BONUS_CV LASTYEARCV,
				  (TBA.ORIGIN_BONUS_SA+TBA.STAND_AMOUNT) LASTAMNT,
		          TO_CHAR(TBA.ALLOCATE_DUE_DATE,'yyyy"年"mm"月"dd"日"') TRISKDATE,
		          TBA.BONUS_SA+NVL((select SUM(APDTA.BONUS_SA) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
		              APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
		              WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
		              AND APD.ITEM_ID=APDTA.ITEM_ID AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE),0) ADDAMNTJB,
				  TBA.BONUS_CV VALUEJB1,
                  (SELECT SUM(TBA1.BONUS_SA) FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE TBA1 WHERE TBA1.ITEM_ID = TCP.ITEM_ID AND TBA1.ALLOCATE_DATE<=TBA.ALLOCATE_DATE) ACCADDAMNT,
				  TBA.TOTAL_BONUS_CV VALUE2,        
		          TBA.BONUS_SA BASEAMNT,
		          NVL((SELECT SUM(APD.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD 
					 WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0),0) DUTYBASEAMNT,
		          NVL((select SUM(APD.INITIAL_DISCNT_PREM_AF) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
		              APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
		              WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
		              AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE
		              AND APD.ITEM_ID=APDTA.ITEM_ID),0) DUTYPREM,
		          NVL((select APDTCE.POLICY_PERIOD
		             from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
		                  APP___PAS__DBUSER.T_CONTRACT_EXTEND  APDTCE
		            WHERE APD.IS_MASTER_ITEM = 0
		              AND TBA.BUSI_ITEM_ID = APD.BUSI_ITEM_ID AND ROWNUM=1
		              AND APD.ITEM_ID = APDTCE.ITEM_ID),0) DUTYPREMCOUNT,
		          NVL((select SUM(APDTA.BONUS_SA) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
							APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
							WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
							AND APD.ITEM_ID=APDTA.ITEM_ID AND APDTA.ALLOCATE_DATE < TBA.ALLOCATE_DATE),0) LASTDUTYYEARSUMBONUSAMNT,
		          (  NVL((select SUM(APDTA.ORIGIN_BONUS_SA)
	                  from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
	                       APP___PAS__DBUSER.T_BONUS_ALLOCATE   APDTA
	                 WHERE APD.POLICY_CODE = TCM.POLICY_CODE
	                   AND APD.IS_MASTER_ITEM = 0
	                   AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE
	                   AND APD.ITEM_ID = APDTA.ITEM_ID) ,0) +
	                NVL((select SUM(APDTA.STAND_AMOUNT)
	                  from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
	                       APP___PAS__DBUSER.T_BONUS_ALLOCATE   APDTA
	                 WHERE APD.POLICY_CODE = TCM.POLICY_CODE
	                   AND APD.IS_MASTER_ITEM = 0
	                   AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE
	                   AND APD.ITEM_ID = APDTA.ITEM_ID) ,0)   ) DUTYAMNT,
		          NVL((select SUM(APDTA.BONUS_SA) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
							APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
							WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
							AND APD.ITEM_ID=APDTA.ITEM_ID   AND APDTA.ALLOCATE_DATE = TBA.ALLOCATE_DATE),0) DUTYBONUSVALUE,
				  (SELECT COUNT( APD1.LIABILITY_STATE) 
				     FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD1            
               		WHERE APD1.POLICY_CODE = TCM.POLICY_CODE 
               		  AND APD1.IS_MASTER_ITEM = 0 
               		  AND APD1.LIABILITY_STATE = 1 ) KXEFFECTIVECOUNT,					
		          NVL((select SUM(APDTA.BONUS_CV) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
							APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
							WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
							AND APD.ITEM_ID=APDTA.ITEM_ID AND APDTA.ALLOCATE_DATE = TBA.ALLOCATE_DATE),0) DUTYBONUSAMNT,
		          NVL((select SUM(APDTA.BONUS_SA) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
							APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
							WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
							AND APD.ITEM_ID=APDTA.ITEM_ID AND APDTA.ALLOCATE_DATE<=TBA.ALLOCATE_DATE),0) SUMDUTYBONUSAMNT,
							
		          NVL((select SUM(APDTA.TOTAL_BONUS_CV) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
		              APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
		              WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
		              AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE
		              AND APD.ITEM_ID=APDTA.ITEM_ID),0) SUMDUTYBONUSVALUE,
		          NVL((SELECT SUM(STCP.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT STCP WHERE STCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID),0) KXAMNT,
		          TBA.VALID_AMOUNT BONUSAMNT,
		          (SELECT NVL(sum(TBA1.BONUS_CV),0) FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE TBA1 WHERE 1=1 
						AND TBA1.POLICY_CODE = TCP.POLICY_CODE AND TBA1.ALLOCATE_DATE=TBA.ALLOCATE_DATE) SUMBONUSVALUE,
		          (SELECT NVL(sum(TBA1.TERMINAL_BONUS),0) FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE TBA1 WHERE 1=1 
						AND TBA1.POLICY_CODE = TCP.POLICY_CODE AND TBA1.ALLOCATE_DATE=TBA.ALLOCATE_DATE) TERMINATEBONUS,
		          (select NVL(SUM(APDTA.VALID_AMOUNT),0)
          from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
               APP___PAS__DBUSER.T_BONUS_ALLOCATE   APDTA
         WHERE APD.POLICY_CODE = TCM.POLICY_CODE
           AND APD.IS_MASTER_ITEM = 0
           AND APD.ITEM_ID = APDTA.ITEM_ID
           AND APDTA.ALLOCATE_DATE = TBA.ALLOCATE_DATE) KXZRFHRYXBE,
					 (select NVL(SUM(APDTA.STAND_AMOUNT),0)
          from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
               APP___PAS__DBUSER.T_BONUS_ALLOCATE   APDTA
         WHERE APD.POLICY_CODE = TCM.POLICY_CODE
           AND APD.IS_MASTER_ITEM = 0
           AND APD.ITEM_ID = APDTA.ITEM_ID
           AND APDTA.ALLOCATE_DATE = TBA.ALLOCATE_DATE) KXZRFHRJBBE,
		          (SELECT '公司'|| A.YEAR ||'年'|| DECODE(A.SEASON,1,'一',2,'二',3,'三','四') ||'季度的综合偿付能力充足率为'|| A.SOLVENCY_RATIO*100
			              ||'%、' ||  CASE WHEN A.YEAR=B.YEAR AND A.SEASON = B.SEASON 
			              THEN '风险综合评级为'|| B.RISK_LEVEL ||'类，偿付能力充足率达到监管要求。'
			              ELSE '公司'|| B.YEAR ||'年'|| DECODE(B.SEASON,1,'一',2,'二',3,'三','四') 
			                ||'季度风险综合评级为'|| B.RISK_LEVEL ||'类，偿付能力充足率达到监管要求。' END
					FROM 
					(SELECT M.YEAR,M.SEASON,M.SOLVENCY_RATIO FROM 
					  (SELECT T.YEAR,T.SEASON,T.SOLVENCY_RATIO FROM DEV_PAS.T_C_ROSS_INFO T WHERE T.SOLVENCY_RATIO IS NOT NULL AND 
					   T.YEAR=(SELECT MAX(TC.YEAR) FROM  APP___PAS__DBUSER.T_C_ROSS_INFO TC WHERE TC.SOLVENCY_RATIO IS NOT NULL) ORDER BY T.SEASON DESC
					  ) M WHERE ROWNUM=1
					) A,
					(SELECT M.YEAR,M.SEASON,M.RISK_LEVEL FROM 
					  (SELECT T.YEAR,T.SEASON,T.RISK_LEVEL FROM DEV_PAS.T_C_ROSS_INFO T WHERE T.RISK_LEVEL IS NOT NULL AND 
					   T.YEAR=(SELECT MAX(TC.YEAR) FROM  APP___PAS__DBUSER.T_C_ROSS_INFO TC WHERE TC.RISK_LEVEL IS NOT NULL) ORDER BY T.SEASON DESC
					  ) M WHERE ROWNUM=1
					) B) PAYRISKINFO,
		          (SELECT TUO.ORGAN_ADDRESS FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE=SUBSTR(TCM.ORGAN_CODE,0,6)) COMPANYADDRESS,
		          (SELECT TUO.ORGAN_ZIPCODE FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE=SUBSTR(TCM.ORGAN_CODE,0,6)) COMPANYPOST,
		          (SELECT a.bank_branch_name FROM dev_pas.t_bank_branch a where a.bank_branch_code = tcm.service_bank_branch) AGENTCOMNAME,
		          TCM.SERVICE_BANK_BRANCH AGENTCOMCODE,
		          '1' BONUSTYPE,
		          TBP.PRODUCT_NAME_STD RISKNAME,
		          NVL((select SUM(APDTA.VALID_AMOUNT) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT APD,
		              APP___PAS__DBUSER.T_BONUS_ALLOCATE APDTA  
		              WHERE APD.POLICY_CODE=TCM.POLICY_CODE AND APD.IS_MASTER_ITEM=0
		              AND APD.ITEM_ID=APDTA.ITEM_ID AND APDTA.ALLOCATE_DATE=TBA.ALLOCATE_DATE),0) SELECTAMNT,
		          SUBSTR(TC.CUSTOMER_CERTI_CODE,-4) CERTICODEFOUR,
		          ROWNUM RN
		          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM             
		           INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER   TPH      
		                 ON TCM.POLICY_CODE = TPH.POLICY_CODE
		           INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP   
		                 ON TCM.POLICY_CODE = TCBP.POLICY_CODE
		           INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL          
		                 ON TCM.POLICY_CODE = TIL.POLICY_CODE
		           INNER JOIN APP___PAS__DBUSER.T_ADDRESS TA                
		                 ON TA.ADDRESS_ID = TPH.ADDRESS_ID
		           INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TC               
		                 ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
		           INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP      
		                 ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
		           INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP      
		                 ON TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
		           INNER JOIN APP___PAS__DBUSER.T_BONUS_ALLOCATE TBA        
		                 ON TBA.ITEM_ID = TCP.ITEM_ID
		           INNER JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
		                 ON TCP.ITEM_ID = TCE.ITEM_ID
   		           INNER JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT TCAT
		                 ON TCAT.POLICY_CODE = TCM.POLICY_CODE   
		           WHERE TCM.LIABILITY_STATE = '1'    
		            AND  TBP.COVER_PERIOD_TYPE = 0   
		            AND  TCBP.MASTER_BUSI_ITEM_ID IS NULL 
		            AND  TBA.BONUS_ALLOT = '1' AND TBA.ALLOCATE_TYPE = '02' 
		            AND  TCP.IS_MASTER_ITEM=1 
		            AND  TCAT.IS_NB_AGENT =1
			]]>

			<if test="batch_time != null and batch_time != ''">
				 <![CDATA[ AND TBA.ALLOCATE_DATE = #{batch_time,jdbcType=DATE} ]]>
			</if>
			<if test="policy_code != null and policy_code != ''">
				 <![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]>
			</if>
			<if test="noti_method  != null  and  noti_method  != ''  "><![CDATA[AND TCM.NOTIFICATION_RECEIVE_METHOD = #{noti_method,jdbcType=VARCHAR}  ]]></if>
			<if test=" noti_method  == null or noti_method == '' "><![CDATA[AND (TCM.NOTIFICATION_RECEIVE_METHOD IS NULL OR TCM.NOTIFICATION_RECEIVE_METHOD = '1') ]]></if>
			<include refid="findOrganCodeInfo" />
		</select>
		<!-- 现金分红短信 -->
		<select id="PA_sendCashDividend" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				SELECT 
		            (
						(SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE=TA.STATE) ||
						(SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE=TA.CITY) ||
						(SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE=TA.DISTRICT) ||  
						 TA.ADDRESS        
          			) NADDRESS,  
		            TA.POST_CODE NZIPCODE,
		            TBP.BUSI_PRD_ID,
					TBA.ALLOCATE_DATE,
		            TBA.CASH_BONUS CASH_BONUS,
		            TBA.BONUS_SA BONUS_SA,
		            (SELECT M.AGENT_CHANNEL FROM DEV_PAS.T_CONTRACT_AGENT T,DEV_PAS.T_AGENT M WHERE T.POLICY_CODE= TCM.POLICY_CODE
							    AND T.AGENT_CODE=M.AGENT_CODE AND T.IS_CURRENT_AGENT=1 AND ROWNUM=1
							  ) AGENT_CHANNEL,
				    (SELECT M.AGENT_CODE FROM DEV_PAS.T_CONTRACT_AGENT T,DEV_PAS.T_AGENT M WHERE T.POLICY_CODE= TCM.POLICY_CODE
                      AND T.AGENT_CODE=M.AGENT_CODE AND T.IS_CURRENT_AGENT=1 AND ROWNUM=1
                    ) AGENT_CODE,
                    (SELECT M.GROUP_CODE FROM DEV_PAS.T_CONTRACT_AGENT T,DEV_PAS.T_AGENT M WHERE T.POLICY_CODE= TCM.POLICY_CODE
                      AND T.AGENT_CODE=M.AGENT_CODE AND T.IS_CURRENT_AGENT=1 AND ROWNUM=1
                    ) GROUP_CODE,
		            NVL(TBA.VALID_AMOUNT,0) VALID_AMOUNT,
		            TO_CHAR(TBA.RATE_RELEASE_DATE,'YYYY')-1 FISCALYEAR,
		            (SELECT TCC.CUSTOMER_NAME FROM APP___PAS__DBUSER.T_CUSTOMER TCC WHERE TCC.CUSTOMER_ID=TIL.CUSTOMER_ID AND ROWNUM=1) INSUREDNAME,
		            TO_CHAR(TBP.VALIDATE_DATE,'yyyy-mm-dd') CVALIDATE,
		            TCPP.STD_PREM_AF PREM,              
		            TCE.POLICY_PERIOD PREMCOUNT,
		            DECODE((SELECT TSM.MODE_CODE FROM DEV_PAS.T_SURVIVAL_MODE TSM WHERE TSM.MODE_CODE = (CASE
			             WHEN TPP.PAY_NUM >= 2 THEN
			              (SELECT T.SURVIVAL_MODE
			                 FROM APP___PAS__DBUSER.T_PAY_DUE T
			                WHERE T.PLAN_ID = TPP.PLAN_ID
			                  AND T.PAY_NUM = TPP.PAY_NUM - 1 AND ROWNUM=1)
			             ELSE
			              TPP.SURVIVAL_MODE
			          END)),1,'现金领取',2,'累积生息',4,'万能账户','其他') PAYFORM2,
		            TBA.BONUS_SUM LASTSUMCASHBONUS,   
		            TBA.INTEREST_SUM LASTSUMLX,
		            TBA.ORIGIN_BONUS_SA ALREADYGETSUMMONEY,
		            NVL((SELECT SUM(T.FEE_AMOUNT)
								 FROM DEV_PAS.T_PAY_DUE T  
								WHERE T.PLAN_ID = TPP.PLAN_ID AND T.PAY_DUE_DATE<TBA.ALLOCATE_DUE_DATE),0) LASTSUMMONEY,                 
		            DECODE(TPP.SURVIVAL_MODE,1,'现金领取',2,'累积生息',4,'万能账户','其他') PAYFORM1,
		            (TO_CHAR(TBA.ALLOCATE_DUE_DATE,'yyyy') - TO_CHAR(TBP.VALIDATE_DATE,'yyyy')) PAYINTV,
		            TO_CHAR(TBA.ALLOCATE_DUE_DATE,'yyyy-mm-dd') SDISPATCHDATE, 
		            TO_CHAR(TBA.ALLOCATE_DATE,'yyyy-mm-dd') ADISPATCHDATE, 
		            TBA.CASH_BONUS CASHBONUS,   
		            TBA.REISSUE_INTEREST BONUSINTEREST,
		            TBA.BONUS_RATE RATE,
		            (SELECT '公司'|| A.YEAR ||'年'|| DECODE(A.SEASON,1,'一',2,'二',3,'三','四') ||'季度的综合偿付能力充足率为'|| A.SOLVENCY_RATIO*100
			              ||'%、' ||  CASE WHEN A.YEAR=B.YEAR AND A.SEASON = B.SEASON 
			              THEN '风险综合评级为'|| B.RISK_LEVEL ||'类，偿付能力充足率达到监管要求。'
			              ELSE '公司'|| B.YEAR ||'年'|| DECODE(B.SEASON,1,'一',2,'二',3,'三','四') 
			                ||'季度风险综合评级为'|| B.RISK_LEVEL ||'类，偿付能力充足率达到监管要求。' END
					FROM 
					(SELECT M.YEAR,M.SEASON,M.SOLVENCY_RATIO FROM 
					  (SELECT T.YEAR,T.SEASON,T.SOLVENCY_RATIO FROM DEV_PAS.T_C_ROSS_INFO T WHERE T.SOLVENCY_RATIO IS NOT NULL AND 
					   T.YEAR=(SELECT MAX(TC.YEAR) FROM  APP___PAS__DBUSER.T_C_ROSS_INFO TC WHERE TC.SOLVENCY_RATIO IS NOT NULL) ORDER BY T.SEASON DESC
					  ) M WHERE ROWNUM=1
					) A,
					(SELECT M.YEAR,M.SEASON,M.RISK_LEVEL FROM 
					  (SELECT T.YEAR,T.SEASON,T.RISK_LEVEL FROM DEV_PAS.T_C_ROSS_INFO T WHERE T.RISK_LEVEL IS NOT NULL AND 
					   T.YEAR=(SELECT MAX(TC.YEAR) FROM  APP___PAS__DBUSER.T_C_ROSS_INFO TC WHERE TC.RISK_LEVEL IS NOT NULL) ORDER BY T.SEASON DESC
					  ) M WHERE ROWNUM=1
					) B) PAYRISKINFO,
		            (SELECT TUO.ORGAN_ADDRESS FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE=SUBSTR(TCM.ORGAN_CODE,0,6)) BRANCH,
		            (SELECT TUO.ORGAN_ZIPCODE FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE=SUBSTR(TCM.ORGAN_CODE,0,6)) COMPANYPOST,
		            (CASE WHEN TCM.CHANNEL_TYPE IN('03','08') THEN 
		          (SELECT TBB.BANK_BRANCH_NAME FROM APP___PAS__DBUSER.T_BANK_BRANCH TBB WHERE TBB.BANK_BRANCH_CODE= TCM.SERVICE_BANK_BRANCH)   
		              ELSE '' END ) AGENTCOMNAME,
		            (CASE WHEN TCM.CHANNEL_TYPE IN('03','08') THEN TCM.SERVICE_BANK_BRANCH 
		              ELSE '' END ) AGENTCOMCODE,               
		            '2' BONUSTYPE,
		            TTBP.PRODUCT_NAME_STD RISKNAME,
		            
		            (SELECT CASE WHEN TC.CUSTOMER_GENDER = 1 THEN 0 WHEN TC.CUSTOMER_GENDER = 2 THEN 1 END 
		            FROM APP___PAS__DBUSER.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID) AS CUSTOMER_GENDER,
		          (SELECT TC.CUSTOMER_NAME
		                  FROM APP___PAS__DBUSER.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID) AS CUSTOMER_NAME,
		                SUBSTR((SELECT TC.CUSTOMER_CERTI_CODE
		                  FROM APP___PAS__DBUSER.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID),-4) AS CERTICODEFOUR,
		                TA.CUSTOMER_ID CUSTOMER_ID,
		                TA.MOBILE_TEL MOBILE_TEL,
		            TCM.ORGAN_CODE ORGAN_CODE, 
		            SUBSTR(TCM.ORGAN_CODE,0,4) BRANCH_ORG_CODE, 
		            SUBSTR(TCM.ORGAN_CODE,0,6) SUPPORT_ORG_CODE,                 
		            TPH.POLICY_CODE  POLICY_CODE,
		            TBA.ALLOCATE_DUE_DATE,
      				TBA.POLICY_ID,
					ROWNUM RN
		            FROM 
		            APP___PAS__DBUSER.T_CONTRACT_MASTER TCM              
		           INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP 
		              ON TCM.POLICY_CODE = TBP.POLICY_CODE
		           INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCPP  
		              ON TBP.BUSI_ITEM_ID = TCPP.BUSI_ITEM_ID
		           INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH      
		              ON TBP.POLICY_ID = TPH.POLICY_ID
		           INNER JOIN APP___PAS__DBUSER.T_PAY_PLAN TPP           
		              ON TBP.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
		           INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL       
		              ON TBP.POLICY_ID = TIL.POLICY_ID
		           INNER JOIN APP___PAS__DBUSER.T_ADDRESS TA             
		              ON TPH.ADDRESS_ID = TA.ADDRESS_ID
		           INNER JOIN APP___PAS__DBUSER.T_BONUS_ALLOCATE TBA     
		              ON TBP.BUSI_ITEM_ID = TBA.BUSI_ITEM_ID
		           INNER JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE    
		               ON TCE.BUSI_ITEM_ID = TBP.BUSI_ITEM_ID
		           INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT TTBP  
		               ON TTBP.BUSINESS_PRD_ID = TBP.BUSI_PRD_ID
		           WHERE 
		                                 
		             TBP.LIABILITY_STATE = '1'       
		             AND TBP.MASTER_BUSI_ITEM_ID IS NULL 
		             AND TPP.PAY_PLAN_TYPE = '1'
		             AND TBA.BONUS_ALLOT='4'
					 AND TBA.ALLOCATE_TYPE = '02'  
					 AND  TCM.CHANNEL_TYPE IN('01','02','03','04','05','06','07','08','09','10','11','12')       
			]]>
			<if test="batch_time != null and batch_time != ''">
				 <![CDATA[ AND TBA.ALLOCATE_DATE = #{batch_time,jdbcType=DATE} ]]>
			</if>
			<if test="policy_code != null and policy_code != ''">
				 <![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]>
			</if>
			<if test="noti_method  != null  and  noti_method  != ''  "><![CDATA[AND TCM.NOTIFICATION_RECEIVE_METHOD = #{noti_method,jdbcType=VARCHAR}  ]]></if>
			<if test=" noti_method  == null or noti_method == '' "><![CDATA[AND (TCM.NOTIFICATION_RECEIVE_METHOD IS NULL OR TCM.NOTIFICATION_RECEIVE_METHOD = '1') ]]></if>
			<include refid="findOrganCodeInfo" />
		</select>
		
		<!-- 保单服务人员变更 -->
		<select id="PA_sendPolicyServerPeopChange" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				SELECT ROWNUM RN, 
					 TO_CHAR(T.INSERT_TIME, 'yyyy-mm-dd') SALEMANCHANGEDATE,
		             TA.AGENT_CHANNEL OLDBRANTCHTYPE,
		             (SELECT TSO.SALES_ORGAN_NAME
		                FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		               WHERE TSO.SALES_ORGAN_CODE =
		                     (SELECT TSO.PARENT_CODE
		                        FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		                       WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE)) OLDSALEMANCOMNAME, 
		             (SELECT TSO.SALES_ORGAN_NAME
		                FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		               WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE) OLDSALEGROUPNAME,
		             TA.AGENT_CODE OLDAGENTCODE,
		             TA.AGENT_NAME OLDAGENTNAME,
		             TA.AGENT_MOBILE OLDAGENTMOBILE,
		             TA.AGENT_STATUS OLDAGENTSTATE, 
		             
		             (SELECT TAA.MOBILE_TEL
		                FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
		                     APP___PAS__DBUSER.T_ADDRESS       TAA
		               WHERE TPH.POLICY_CODE = TCM.POLICY_CODE
		                 AND TPH.ADDRESS_ID = TAA.ADDRESS_ID) MOBILE_TEL,
		             TCM.POLICY_CODE POLICY_CODE,
		             TC.CUSTOMER_NAME CUSTOMER_NAME, 
		             TC.CUSTOMER_GENDER CUSTOMER_GENDER,
		             TC.CUSTOMER_ID CUSTOMER_ID, 
		             TCM.ORGAN_CODE ORGAN_CODE,
		             TO_CHAR(T.INSERT_TIME,'yyyymmdd') CHGDATE,
		             TO_CHAR(TCM.ISSUE_DATE,'yyyymmdd') ISDATE,
		             NVL((SELECT SUM(M.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT M WHERE M.POLICY_CODE=TCM.POLICY_CODE),0) AMOUNT 
		             
		        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
		             APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
		             APP___PAS__DBUSER.T_CUSTOMER        TC,
		             APP___PAS__DBUSER.T_CONTRACT_AGENT  T,
		             APP___PAS__DBUSER.T_AGENT           TA
		       WHERE T.POLICY_CODE = TCM.POLICY_CODE
		         AND TCM.POLICY_ID = TPH.POLICY_ID
		         AND T.LAST_AGENT_CODE = TA.AGENT_CODE
		         AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		         AND T.IS_CURRENT_AGENT = '1'
		         AND T.INSERT_TIME >= #{preMonth_time}
		         AND T.INSERT_TIME < #{preDay_time}+1
		         AND EXISTS
		             (SELECT TCM1.POLICY_CODE
		                                FROM DEV_PAS.T_CONTRACT_MASTER TCM1,
		                                     DEV_PAS.T_CONTRACT_AGENT  TCA,
		                                     DEV_PAS.T_AGENT           TA
		                               WHERE TCM1.POLICY_CODE = TCA.POLICY_CODE
		                                 AND TCA.AGENT_CODE = TA.AGENT_CODE
		                                 AND TCM1.LIABILITY_STATE IN ('1', '4')
		                                 AND TCA.IS_CURRENT_AGENT = 1
		                                 AND TCA.LAST_AGENT_CODE IS NOT NULL 
		                                 AND (SELECT T.AGENT_STATUS
		                                        FROM DEV_PAS.T_AGENT T
		                                       WHERE T.AGENT_CODE = TCA.LAST_AGENT_CODE) IN
		                                     ('3', '4') 
		                                 AND TA.AGENT_STATUS IN('1','2')
		                                 AND TCA.LAST_AGENT_NAME != TCA.AGENT_NAME 
		                                 AND TCA.INSERT_TIME >= #{preMonth_time}
		                                 AND TCA.INSERT_TIME < #{preDay_time}+1
		                                 AND TCM.POLICY_CODE  =TCM1.POLICY_CODE 
		                                )
		     ]]>                           
	         <include refid="findOrganCodeInfo" />
		</select>
		
		<!-- 柜面直销渠道保单服务人员变更通知短信提醒查询客户总数-->
		<select id="PA_queryCountPlySerPeoChgInfo" resultType="java.lang.Integer" parameterType="java.util.Map">
			<![CDATA[
				 SELECT COUNT(*) FROM  ( 
				     SELECT TPH.CUSTOMER_ID
				          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
				               APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
				               APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
				               APP___PAS__DBUSER.T_AGENT           TA
				         WHERE TCM.POLICY_CODE = TCA.POLICY_CODE
				           AND TPH.POLICY_ID = TCM.POLICY_ID
				           AND TCA.AGENT_CODE = TA.AGENT_CODE
				           AND TCM.LIABILITY_STATE IN ('1', '4')    
				           AND TCA.IS_CURRENT_AGENT = 1             
				           AND TCA.LAST_AGENT_CODE IS NOT NULL      
				           AND TA.AGENT_STATUS ='1'          
				           AND TCA.LAST_AGENT_NAME != TCA.AGENT_NAME
				           AND TCA.INSERT_TIME >= #{preMonthCurTime}
				           AND TCA.INSERT_TIME <= #{curDayTime}
			]]>	  
			<if test="channelType !=null and channelType !='' ">
				AND TCM.CHANNEL_TYPE = #{channelType}
			</if>
			<if test="policyCode !=null and policyCode !='' ">
				AND TCM.POLICY_CODE = #{policyCode}
			</if>
			<include refid="findOrganCodeInfo" />
			<![CDATA[         
				           GROUP BY TPH.CUSTOMER_ID 
				  )  G where 1=1
			]]>  
		</select>
		
		<!-- 柜面直销渠道保单服务人员变更通知短信提醒查询客户id总集合信息 -->
		<select id="PA_queryPlySerPeoChgInfo" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				 SELECT G.CUSTOMER_ID,ROWNUM RN FROM  ( 
				     SELECT TPH.CUSTOMER_ID
				          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
				               APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
				               APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
				               APP___PAS__DBUSER.T_AGENT           TA
				         WHERE TCM.POLICY_CODE = TCA.POLICY_CODE
				           AND TPH.POLICY_ID = TCM.POLICY_ID
				           AND TCA.AGENT_CODE = TA.AGENT_CODE
				           AND TCM.LIABILITY_STATE IN ('1', '4')    
				           AND TCA.IS_CURRENT_AGENT = 1             
				           AND TCA.LAST_AGENT_CODE IS NOT NULL      
				           AND TA.AGENT_STATUS ='1'         
				           AND TCA.LAST_AGENT_NAME != TCA.AGENT_NAME
				           AND TCA.INSERT_TIME >= #{preMonthCurTime}
				           AND TCA.INSERT_TIME <= #{curDayTime}
			]]>	  
			<if test="channelType !=null and channelType !='' ">
				AND TCM.CHANNEL_TYPE = #{channelType}
			</if>
			<if test="policyCode !=null and policyCode !='' ">
				AND TCM.POLICY_CODE = #{policyCode}
			</if>
			<include refid="findOrganCodeInfo" />
			<![CDATA[         
				           GROUP BY TPH.CUSTOMER_ID 
				  )  G where 1=1 AND MOD(G.CUSTOMER_ID,#{modNum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
			]]>  
		</select>
		
		<!-- 柜面直销渠道保单服务人员变更通知短信提醒exec方法执行查询sql -->
		<select id="PA_execPlySerPeoChgInfo" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				SELECT ROWNUM RN, 
					 TO_CHAR(T.INSERT_TIME, 'yyyy-mm-dd') SALEMANCHANGEDATE,
		             TA.AGENT_CHANNEL OLDBRANTCHTYPE,
		             (SELECT TSO.SALES_ORGAN_NAME
		                FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		               WHERE TSO.SALES_ORGAN_CODE =
		                     (SELECT TSO.PARENT_CODE
		                        FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		                       WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE)) OLDSALEMANCOMNAME, 
		             (SELECT TSO.SALES_ORGAN_NAME
		                FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		               WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE) OLDSALEGROUPNAME,
		             TA.AGENT_CODE OLDAGENTCODE,
		             TA.AGENT_NAME OLDAGENTNAME,
		             TA.AGENT_MOBILE OLDAGENTMOBILE,
		             TA.AGENT_STATUS OLDAGENTSTATE, 
		             
		             (SELECT TAA.MOBILE_TEL
		                FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
		                     APP___PAS__DBUSER.T_ADDRESS       TAA
		               WHERE TPH.POLICY_CODE = TCM.POLICY_CODE
		                 AND TPH.ADDRESS_ID = TAA.ADDRESS_ID) MOBILE_TEL,
		             TCM.POLICY_CODE POLICY_CODE,
		             TC.CUSTOMER_NAME CUSTOMER_NAME, 
		             TC.CUSTOMER_GENDER CUSTOMER_GENDER,
		             TC.CUSTOMER_ID CUSTOMER_ID, 
		             TCM.ORGAN_CODE ORGAN_CODE,
		             TO_CHAR(T.INSERT_TIME,'yyyymmdd') CHGDATE,
		             TO_CHAR(TCM.ISSUE_DATE,'yyyymmdd') ISDATE,
		             NVL((SELECT SUM(M.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT M WHERE M.POLICY_CODE=TCM.POLICY_CODE),0) AMOUNT 
		        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
		             APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
		             APP___PAS__DBUSER.T_CUSTOMER        TC,
		             APP___PAS__DBUSER.T_CONTRACT_AGENT  T,
		             APP___PAS__DBUSER.T_AGENT           TA
		       WHERE T.POLICY_CODE = TCM.POLICY_CODE
		         AND TCM.POLICY_ID = TPH.POLICY_ID
		         AND T.LAST_AGENT_CODE = TA.AGENT_CODE
		         AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		         AND T.IS_CURRENT_AGENT = '1'
		         AND T.INSERT_TIME >= #{preMonthCurTime}
				 AND T.INSERT_TIME <= #{curDayTime}
		         AND EXISTS
		             (SELECT TCM1.POLICY_CODE
		                                FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM1,
		                                     APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
		                                     APP___PAS__DBUSER.T_AGENT           TA
		                               WHERE TCM1.POLICY_CODE = TCA.POLICY_CODE
		                                 AND TCA.AGENT_CODE = TA.AGENT_CODE
		                                 AND TCM1.LIABILITY_STATE IN ('1', '4')
		                                 AND TCA.IS_CURRENT_AGENT = 1
		                                 AND TCA.LAST_AGENT_CODE IS NOT NULL 
		                                 AND TA.AGENT_STATUS ='1'
		                                 AND TCA.LAST_AGENT_NAME != TCA.AGENT_NAME 
		      ]]> 
		      <if test="channelType !=null and channelType !='' ">
		      		AND TCM1.CHANNEL_TYPE = #{channelType}
		      </if>
		      <![CDATA[                            
		                                 AND TCA.INSERT_TIME >= #{preMonthCurTime}
				                         AND TCA.INSERT_TIME <= #{curDayTime}
		                                 AND TCM.POLICY_CODE  =TCM1.POLICY_CODE 
		                                )
		     ]]>
		     <if test=" customerIdList != null and customerIdList.size() != 0  ">
				 <![CDATA[ AND TPH.CUSTOMER_ID IN ]]>
				 <foreach collection="customerIdList" item="customer_Id_List" index="indexs" open="(" close=")" separator=",">
				 	#{customer_Id_List}
				 </foreach>
			 </if>                           
	         <include refid="findOrganCodeInfo" />
		</select>
		
		
		<!-- 新柜面直销渠道保单服务人员变更通知短信提醒查询客集合信息 -->
		<select id="PA_queryPlySerPeoChgNote" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				 SELECT ROWNUM RN,
       TO_CHAR(T.AGENT_START_DATE, 'yyyy-mm-dd') SALEMANCHANGEDATE,
       TA.AGENT_CHANNEL OLDBRANTCHTYPE,
       (SELECT TSO.SALES_ORGAN_NAME
          FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
         WHERE TSO.SALES_ORGAN_CODE =
               (SELECT TSO.PARENT_CODE
                  FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
                 WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE)) OLDSALEMANCOMNAME,
       (SELECT TSO.SALES_ORGAN_NAME
          FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
         WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE) OLDSALEGROUPNAME,
       TA.AGENT_CODE OLDAGENTCODE,
       TA.AGENT_NAME OLDAGENTNAME,
       TA.AGENT_MOBILE OLDAGENTMOBILE,
       TA.AGENT_STATUS OLDAGENTSTATE,
       (SELECT DT.AGENT_CHANNEL FROM DEV_PAS.T_AGENT DT WHERE DT.AGENT_CODE = T.AGENT_CODE)SERVICECONTENT,
       (SELECT TAA.MOBILE_TEL
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
               APP___PAS__DBUSER.T_ADDRESS       TAA
         WHERE TPH.POLICY_CODE = TCM.POLICY_CODE
           AND TPH.ADDRESS_ID = TAA.ADDRESS_ID) MOBILE_TEL,
       TCM.POLICY_CODE POLICY_CODE,
       TC.CUSTOMER_NAME CUSTOMER_NAME,
       TC.CUSTOMER_GENDER CUSTOMER_GENDER,
       TC.CUSTOMER_ID CUSTOMER_ID,
       TCM.ORGAN_CODE ORGAN_CODE,
       TO_CHAR(T.AGENT_START_DATE, 'yyyymmdd') CHGDATE,
       TO_CHAR(TCM.ISSUE_DATE, 'yyyymmdd') ISDATE,
       NVL((SELECT SUM(M.AMOUNT)
             FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT M
            WHERE M.POLICY_CODE = TCM.POLICY_CODE),
           0) AMOUNT
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
       APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
       APP___PAS__DBUSER.T_CUSTOMER        TC,
       APP___PAS__DBUSER.T_CONTRACT_AGENT  T,
       APP___PAS__DBUSER.T_AGENT           TA
 WHERE T.POLICY_CODE = TCM.POLICY_CODE
   AND TCM.POLICY_ID = TPH.POLICY_ID
   AND T.LAST_AGENT_CODE = TA.AGENT_CODE
   AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
   AND T.IS_CURRENT_AGENT = '1'
   AND TCM.LIABILITY_STATE IN ('1', '4')
   AND T.LAST_AGENT_CODE IS NOT NULL
   AND TA.AGENT_CHANNEL = '11'
   AND T.SEND_FLAG ='1'
   AND EXISTS
 (SELECT 'X'
          FROM DEV_PAS.T_CONTRACT_AGENT TCA, DEV_PAS.T_AGENT TAT
         WHERE TCA.AGENT_CODE = TAT.AGENT_CODE
           AND TAT.AGENT_STATUS = '1'
           AND TAT.AGENT_CHANNEL = '12'
           AND (TCA.AGENT_NAME != TCA.LAST_AGENT_NAME OR TCA.AGENT_CODE != TCA.LAST_AGENT_CODE)
           AND TCA.LIST_ID = T.LIST_ID)
           AND T.AGENT_START_DATE <= #{curDayTime}
			]]>	  
			<if test="preMonthCurTime !=null and preMonthCurTime !='' ">
				AND T.AGENT_START_DATE >= #{preMonthCurTime}
			</if>
			<if test="policyCode !=null and policyCode !='' ">
				AND TCM.POLICY_CODE = #{policyCode}
			</if>
			<include refid="findOrganCodeInfo" />
			<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(TC.CUSTOMER_ID , #{modnum}) = #{start}]]></if> 
		</select>
		
	<!-- 查询年金、满期金领取到期前30天提醒Count查询 -->
	<select id="PA_queryMaturityAdvanceCount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(A.POLICY_NUM) FROM (
			SELECT Distinct TPP.POLICY_CODE AS POLICY_NUM
			           FROM APP___PAS__DBUSER.T_PAY_PLAN           TPP,
			                APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
			                APP___PAS__DBUSER.T_CUSTOMER           TC,
			                APP___PAS__DBUSER.T_ADDRESS            TAAC,
			                APP___PAS__DBUSER.T_POLICY_HOLDER      TIL
			          WHERE TPP.POLICY_ID = TCM.POLICY_ID
			            AND TPP.POLICY_ID = TIL.POLICY_ID
			            AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			            AND TAAC.ADDRESS_ID = TIL.ADDRESS_ID
			            AND TN.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
			            AND TCM.POLICY_CODE = TN.POLICY_CODE
			            AND TPP.PAY_STATUS = '2'
			            AND TPP.PAY_PLAN_TYPE IN ('2', '4', '3', '5', '8', '10', '11') ]]> 
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TPP.Pay_Due_Date = #{batch_time,jdbcType=DATE} + #{advancedDay, jdbcType=NUMERIC}]]></if>
		<if test="policyCode !=null and policyCode !='' "><![CDATA[ AND TPP.POLICY_CODE = #{policyCode}]]></if>
		<if test=" organCode != null and organCode != ''  "><![CDATA[AND TCM.ORGAN_CODE LIKE #{organ_code} || '%']]></if>
   <![CDATA[
			)A WHERE 1 = 1
         ]]>      
	</select>
	
	<!-- 查询符合年金、满期金领取到期前30天提醒待处理数据 -->
	<select id="PA_queryMaturityAdvance" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ROWNUM RN, A.* FROM (SELECT Distinct TPP.POLICY_CODE AS POLICY_NUM,TPP.POLICY_ID
			           FROM APP___PAS__DBUSER.T_PAY_PLAN           TPP,
			                APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
			                APP___PAS__DBUSER.T_CUSTOMER           TC,
			                APP___PAS__DBUSER.T_ADDRESS            TAAC,
			                APP___PAS__DBUSER.T_POLICY_HOLDER      TIL
			          WHERE TPP.POLICY_ID = TCM.POLICY_ID
			            AND TPP.POLICY_ID = TIL.POLICY_ID
			            AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			            AND TAAC.ADDRESS_ID = TIL.ADDRESS_ID
			            AND TN.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
			            AND TCM.POLICY_CODE = TN.POLICY_CODE
			            AND TPP.PAY_STATUS = '2'
			            AND TPP.PAY_PLAN_TYPE IN ('2', '4', '3', '5', '8', '10', '11')  ]]>
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TPP.Pay_Due_Date = #{batch_time,jdbcType=DATE} + #{advancedDay, jdbcType=NUMERIC}]]></if>
		<if test="policyCode !=null and policyCode !='' "><![CDATA[ AND TPP.POLICY_CODE = #{policyCode}]]></if>
	  	<if test=" organCode != null and organCode != ''  "><![CDATA[AND TCM.ORGAN_CODE LIKE #{organ_code} || '%']]></if>
   <![CDATA[
           )A WHERE 1 = 1
		]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.POLICY_ID , #{modnum}) = #{start}]]></if> 
	</select>	

	<!-- 查询符合年金、满期金领取到期前30天提醒待处理数据信息-->
	<select id="PA_findMaturityAdvanceSMSInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT Distinct TPP.POLICY_CODE AS POLICY_NUM,
						TPP.POLICY_ID,
						TPP.SURVIVAL_INVEST_FLAG,
						TPP.SURVIVAL_MODE,
						TPP.SURVIVAL_W_MODE,
		                SUBSTR(TPP.POLICY_CODE, -4) POLICYFOUR,
		                TC.CUSTOMER_NAME,
		                TC.CUSTOMER_GENDER,
		                TAAC.MOBILE_TEL MOBILE_TEL_BC,
		                TC.MOBILE_TEL MOBILE_TEL_TC,
		                TC.CUSTOMER_ID,
		                tpp.busi_item_id,
		                tpp.plan_id,
		                (Select max(tt.busi_prd_id)
		                   From dev_pas.t_Contract_Busi_Prod a,
		                        dev_pas.t_Contract_Busi_Prod tt
		                  where a.busi_item_id = tpp.busi_item_id
		                    and tt.busi_item_id =
		                        decode(A.master_busi_item_id,
		                               null,
		                               A.busi_item_id,
		                               A.master_busi_item_id)) BUSIPRDID,
		                (case
		                  when TCM.Multi_Mainrisk_Flag = '1' then
		                   decode ((Select b.product_category1
                          From dev_pas.t_Contract_Busi_Prod t,
                               dev_pds.t_Business_Product   b,
                               dev_pas.t_Pay_Plan           c
                         where t.busi_item_id = C.busi_item_id
                           and C.policy_code = Tpp.Policy_Code
                           and c.pay_due_date = tpp.pay_due_date
                           and t.busi_prd_id = b.business_prd_id
                           and c.survival_invest_flag = tpp.survival_invest_flag
                           and c.pay_plan_type in
                               ('2', '4', '3', '5', '8', '10', '11')
                           and b.product_category1 = '20002'  and rownum = 1),null,'否','是')
		                  else
		                   CASE
		                     WHEN (SELECT P.PRODUCT_CATEGORY1
		                             FROM DEV_PAS.T_BUSINESS_PRODUCT P
		                            WHERE P.PRODUCT_CODE_SYS = TN.BUSI_PROD_CODE) =
		                          '20002' THEN
		                      '是'
		                     ELSE
		                      '否'
		                   END
		                end) AS div_ins_flag, /*分红险标识*/
		                (CASE
		                  WHEN (SELECT count(*)
		                          FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        PA,
		                               APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM AST
		                         WHERE PA.ACCOUNT_ID = AST.ACCOUNT_ID
		                           AND PA.ACCOUNT_TYPE = '4'
		                           AND PA.INTEREST_CAPITAL > '0'
		                           AND AST.REGULAR_REPAY = '0'
		                           AND PA.POLICY_ID = TPP.POLICY_ID
		                           AND PA.POLICY_ACCOUNT_STATUS IN ('1', '3')) > 0 THEN
		                   '是'
		                  ELSE
		                   '否'
		                END) AS unli_flag, /*未清偿标识*/
		                (SELECT sum(PA.INTEREST_CAPITAL)
		                          FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        PA,
		                               APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM AST
		                         WHERE PA.ACCOUNT_ID = AST.ACCOUNT_ID
		                           AND PA.ACCOUNT_TYPE = '4'
		                           AND PA.INTEREST_CAPITAL > '0'
		                           AND AST.REGULAR_REPAY = '0'
		                           AND PA.POLICY_ID = TPP.POLICY_ID
		                           AND PA.POLICY_ACCOUNT_STATUS IN ('1', '3')) as unli_money,
		                Decode(TCM.Multi_Mainrisk_Flag,
		                       null,
		                       0,
		                       TCM.Multi_Mainrisk_Flag) multi_mainrisk_lag,
		                (case
		                  when (Select tt.busi_prod_code
		                          From dev_pas.t_Contract_Busi_Prod a,
		                               dev_pas.t_Contract_Busi_Prod tt,
		                               dev_pas.t_Constants_Info     b
		                         where a.busi_item_id = tpp.busi_item_id
		                           and tt.busi_item_id =
		                               decode(a.master_busi_item_id,
		                                      null,
		                                      a.busi_item_id,
		                                      a.master_busi_item_id)
		                           and tt.busi_prod_code = b.constants_value
		                           and b.constants_key = 'BENE_IS_HOLDER_PROD') is null or TPP.Pay_Plan_Type = 4 then
		                   '0'
		                  else
		                   '1'
		                end) getnameflag,
		                TPP.Pay_Plan_Type,
		                TPP.pay_Due_Date,
		                TCM.ORGAN_CODE,
		                TCM.CHANNEL_TYPE,
		                (Select c.product_code_sys From dev_pds.t_Business_Product c where c.business_prd_id in (
                    			(Select Max(a.busi_prd_id) From dev_pas.t_Contract_Busi_Prod a where a.policy_code = TPP.Policy_code and a.master_busi_item_id is null ))) maincode,
                    	(Select c.product_name_std From dev_pds.t_Business_Product c where c.business_prd_id in (
                    			(Select Max(a.busi_prd_id) From dev_pas.t_Contract_Busi_Prod a where a.policy_code = TPP.Policy_code and a.master_busi_item_id is null ))) mainsysname
		  FROM APP___PAS__DBUSER.T_PAY_PLAN           TPP,
		       APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
		       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
		       APP___PAS__DBUSER.T_CUSTOMER           TC,
		       APP___PAS__DBUSER.T_ADDRESS            TAAC,
		       APP___PAS__DBUSER.T_POLICY_HOLDER      TIL
		 WHERE TPP.POLICY_ID = TCM.POLICY_ID
		   AND TPP.POLICY_ID = TIL.POLICY_ID
		   AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
		   AND TAAC.ADDRESS_ID = TIL.ADDRESS_ID
		   AND TN.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
		   AND TCM.POLICY_CODE = TN.POLICY_CODE
		   AND TPP.PAY_STATUS = '2'
		   AND TPP.PAY_PLAN_TYPE IN ('2', '4', '3', '5', '8', '10', '11') ]]>
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TPP.Pay_Due_Date = #{batch_time,jdbcType=DATE} + #{advancedDay, jdbcType=NUMERIC}]]></if>
		<if test="policyCode !=null and policyCode !='' "><![CDATA[ AND TPP.POLICY_CODE = #{policyCode}]]></if>
	  	<if test=" organCode != null and organCode != ''  "><![CDATA[AND TCM.ORGAN_CODE LIKE #{organ_code} || '%']]></if>
   <![CDATA[
	     GROUP BY TPP.POLICY_CODE,
          TC.CUSTOMER_NAME,
          TC.CUSTOMER_GENDER,
          TAAC.MOBILE_TEL,
          TC.MOBILE_TEL,
          TC.CUSTOMER_ID,
          TN.BUSI_PROD_CODE,
          TPP.POLICY_ID,
          tpp.busi_item_id,
          tpp.pay_due_date,
          tpp.plan_id,
          TPP.Pay_Plan_Type,
          tpp.survival_invest_flag,
          TCM.Multi_Mainrisk_Flag,
		  TCM.ORGAN_CODE,
		  TCM.CHANNEL_TYPE,
          TPP.SURVIVAL_MODE,
		  TPP.SURVIVAL_W_MODE
		]]>
	</select>
		<!-- 查询符合年金、满期金领取到期前30天提醒待处理给付计划信息 -->
	<select id="PA_queryMaturityAdvancePayplanInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TPP.POLICY_CODE AS POLICY_NUM,TPP.POLICY_ID,TPP.PLAN_ID,TPP.SURVIVAL_INVEST_FLAG,TPP.SURVIVAL_W_MODE,TPP.SURVIVAL_MODE
			           FROM APP___PAS__DBUSER.T_PAY_PLAN           TPP,
			                APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TN,
			                APP___PAS__DBUSER.T_CUSTOMER           TC,
			                APP___PAS__DBUSER.T_ADDRESS            TAAC,
			                APP___PAS__DBUSER.T_POLICY_HOLDER      TIL
			          WHERE TPP.POLICY_ID = TCM.POLICY_ID
			            AND TPP.POLICY_ID = TIL.POLICY_ID
			            AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			            AND TAAC.ADDRESS_ID = TIL.ADDRESS_ID
			            AND TN.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
			            AND TCM.POLICY_CODE = TN.POLICY_CODE
			            AND TPP.PAY_STATUS = '2'
			            AND TPP.PAY_PLAN_TYPE IN ('2', '4', '3', '5', '8', '10', '11')  ]]>
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TPP.Pay_Due_Date = #{batch_time,jdbcType=DATE} + #{advancedDay, jdbcType=NUMERIC}]]></if>
		<if test="policyCode !=null and policyCode !='' "><![CDATA[ AND TPP.POLICY_CODE = #{policyCode}]]></if>
	</select>
	
	<!-- 查询符合投连险保单状态报告数据(短信) -->
	<select id="PA_queryCastAndConnectList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM AS RN,T.* FROM (
			SELECT D.*
				  FROM DEV_PAS.T_CONTRACT_MASTER    A,
				       DEV_PAS.T_CONTRACT_BUSI_PROD B,
				       DEV_PDS.T_BUSINESS_PRODUCT   C,
				       DEV_PAS.T_DOCUMENT           D
				 WHERE 1 = 1
				   AND A.POLICY_ID = B.POLICY_ID
				   AND B.BUSI_PRD_ID = C.BUSINESS_PRD_ID
				   AND A.POLICY_CODE = D.POLICY_CODE
				   AND D.TEMPLATE_CODE = 'PAS_00006'
				   AND C.PRODUCT_CATEGORY1 = '20004'
				   AND D.INSERT_TIME >= #{batch_time,jdbcType=DATE} -1
           		   AND D.INSERT_TIME < #{batch_time,jdbcType=DATE}
				   AND (A.NOTIFICATION_RECEIVE_METHOD = '1' OR
				       A.NOTIFICATION_RECEIVE_METHOD IS NULL)
		]]>
		<if test="policyCode !=null and policyCode !='' "><![CDATA[ AND A.POLICY_CODE = #{policyCode}]]></if>
		<![CDATA[ ) T WHERE 1 = 1  ]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.POLICY_ID , #{modnum}) = #{start}]]></if>
	</select>
	
	<!-- 投连险保单状态报告内容信息 -->
	<select id="PA_queryCastAndConnectInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TCM.CHANNEL_TYPE, /*销售渠道*/
		       TCM.POLICY_CODE, /*保单号*/
		       SUBSTR(TCM.POLICY_CODE, -4) POLICYNO,
		       BP.PRODUCT_CODE_SYS AS BUSI_PROD_CODE, /*主险代码*/
		       BP.PRODUCT_ABBR_NAME BUSI_PROD_NAME, /*主险名称*/
		       TC.CUSTOMER_ID, /*客户号*/
		       TC.CUSTOMER_NAME, /*客户名称*/
		       TC.CUSTOMER_GENDER, /*性别*/
		       TC.MOBILE_TEL, /*手机号码*/
		       NULL WECHAT_NO, /*微信号*/
		       NULL APPACCOUNT, /*APP账户*/
		       TC.EMAIL, /*电子邮箱*/
		       TA.AGENT_CHANNEL, /*现服务人员渠道*/
		       (SELECT TSO.SALES_ORGAN_NAME
		          FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		         WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE
		           AND ROWNUM = 1) GROUPS, /*现服务人员组*/
		       (SELECT B.SALES_ORGAN_NAME
		          FROM DEV_PAS.T_SALES_ORGAN B
		         WHERE B.SALES_ORGAN_CODE =
		               (SELECT DECODE(TSO.ORGAN_LEVEL_CODE,
		                              2,
		                              TSO.SALES_ORGAN_CODE,
		                              TSO.PARENT_CODE)
		                  FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO
		                 WHERE TSO.SALES_ORGAN_CODE = TA.GROUP_CODE
		                   AND ROWNUM = 1)) PART, /*现服务人员部*/
		       TA.AGENT_CODE, /*代理人工号*/
		       TA.AGENT_NAME, /*代理人名称*/
		       TA.AGENT_MOBILE, /*代理人电话*/
		       TA.AGENT_STATUS AGENT_STATUS_NAME, /*代理人状态*/
		       SUBSTR(TCM.ORGAN_CODE, 0, 4) BRANCH_CODE, /*分公司ID*/
		       (SELECT TUO.ORGAN_NAME
		          FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
		         WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) BRANCH_CODE_NAME, /*分公司名称*/
		       SUBSTR(TCM.ORGAN_CODE, 0, 6) ZHI_ORGAN_CODE, /*支公司ID*/
		       (SELECT TUO.ORGAN_NAME
		          FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
		         WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 6)) ZHI_ORGAN_CODE_NAME, /*支公司名称*/
		       TCM.ORGAN_CODE, /*支公司/营销服务部ID*/
		       (SELECT TUO.ORGAN_NAME
		          FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
		         WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME, /*支公司/营销服务部名称*/
		       (CASE
		         WHEN LENGTH(TC.CUSTOMER_CERTI_CODE) < 4 THEN
		          TC.CUSTOMER_CERTI_CODE
		         ELSE
		          SUBSTR(TC.CUSTOMER_CERTI_CODE, -4)
		       END) SUBSTRFOUR
		  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
		       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
		       APP___PDS__DBUSER.T_BUSINESS_PRODUCT   BP,
		       APP___PAS__DBUSER.T_POLICY_HOLDER      TPH,
		       APP___PAS__DBUSER.T_CUSTOMER           TC,
		       APP___PAS__DBUSER.T_CONTRACT_AGENT     TCA,
		       APP___PAS__DBUSER.T_AGENT              TA
		 WHERE 1 = 1
		   AND TCM.POLICY_ID = TCBP.POLICY_ID
		   AND TCBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
		   AND TCM.POLICY_CODE = TPH.POLICY_CODE
		   AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		   AND TCM.POLICY_CODE = TCA.POLICY_CODE
		   AND TCA.AGENT_CODE = TA.AGENT_CODE
		   AND TCA.IS_CURRENT_AGENT = 1
		   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
		   AND TCM.POLICY_CODE = #{policyCode}
		]]>
	</select>
</mapper>