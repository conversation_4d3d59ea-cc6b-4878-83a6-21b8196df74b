<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsDrRelationDao">

	<sql id="datacenterCashTaskWhereCondition">
		<if test=" cal_date  != null  and  cal_date  != ''  "><![CDATA[ AND A.CAL_DATE = #{cal_date} ]]></if>
		<if test=" buss_no != null and buss_no != ''  "><![CDATA[ AND A.BUSS_NO = #{buss_no} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND <PERSON>.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" calc_cash_value_status != null and calc_cash_value_status != ''  "><![CDATA[ AND A.CALC_CASH_VALUE_STATUS = #{calc_cash_value_status} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryDatacenterCashTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryDatacenterCashTaskByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="queryDatacenterCashTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addDatacenterCashTask"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_DC_CASH_TASK__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_DATACENTER_CASH_TASK(
				INSERT_TIME, CAL_DATE, UPDATE_TIME, BUSS_NO, BUSI_PROD_CODE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, CALC_CASH_VALUE_STATUS, LIST_ID, CASH_VALUE, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				SYSDATE, #{cal_date, jdbcType=DATE} , SYSDATE , #{buss_no, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{calc_cash_value_status, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{cash_value, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteDatacenterCashTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateDatacenterCashTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_DATACENTER_CASH_TASK ]]>
		<set>
		<trim suffixOverrides=",">
		    CAL_DATE = #{cal_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			BUSS_NO = #{buss_no, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			CALC_CASH_VALUE_STATUS = #{calc_cash_value_status, jdbcType=VARCHAR} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id, jdbcType=NUMERIC}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findDatacenterCashTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAL_DATE, A.BUSS_NO, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.CALC_CASH_VALUE_STATUS, A.LIST_ID, A.CASH_VALUE FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK A WHERE 1 = 1  ]]>
		<include refid="datacenterCashTaskWhereCondition" />
	</select>
	
	<select id="findDatacenterCashTaskByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAL_DATE, A.BUSS_NO, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.CALC_CASH_VALUE_STATUS, A.LIST_ID, A.CASH_VALUE FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK A WHERE 1 = 1  ]]>
		<include refid="datacenterCashTaskWhereCondition" />
	</select>
	
	<select id="findDatacenterCashTaskByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAL_DATE, A.BUSS_NO, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.CALC_CASH_VALUE_STATUS, A.LIST_ID, A.CASH_VALUE FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK A WHERE 1 = 1  ]]>
		<include refid="datacenterCashTaskWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapDatacenterCashTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAL_DATE, A.BUSS_NO, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.CALC_CASH_VALUE_STATUS, A.LIST_ID, A.CASH_VALUE FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="datacenterCashTaskWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllDatacenterCashTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAL_DATE, A.BUSS_NO, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.CALC_CASH_VALUE_STATUS, A.LIST_ID, A.CASH_VALUE FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK A WHERE ROWNUM <=  2000 
			AND A.CALC_CASH_VALUE_STATUS IN ('0','2') ]]>
		<include refid="datacenterCashTaskWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findDatacenterCashTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK A WHERE 1 = 1  ]]>
		<include refid="datacenterCashTaskWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryDatacenterCashTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CAL_DATE, B.BUSS_NO, B.BUSI_PROD_CODE, B.POLICY_CODE, 
			B.CALC_CASH_VALUE_STATUS, B.LIST_ID, B.CASH_VALUE FROM (
					SELECT ROWNUM RN, A.CAL_DATE, A.BUSS_NO, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.CALC_CASH_VALUE_STATUS, A.LIST_ID, A.CASH_VALUE FROM APP___PAS__DBUSER.T_DATACENTER_CASH_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="datacenterCashTaskWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
