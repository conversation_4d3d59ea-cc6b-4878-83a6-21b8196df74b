<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyFundChargeDao">

	<sql id="PA_policyFundChargeWhereCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" charge_due_date  != null  and  charge_due_date  != ''  "><![CDATA[ AND A.CHARGE_DUE_DATE = #{charge_due_date} ]]></if>
		<if test=" charge_code != null and charge_code != ''  "><![CDATA[ AND A.CHARGE_CODE = #{charge_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" last_charge_date  != null  and  last_charge_date  != ''  "><![CDATA[ AND A.LAST_CHARGE_DATE = #{last_charge_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test="charge_code_list != null and charge_code_list.size()!=0"><![CDATA[ AND A.CHARGE_CODE  in]]>
			<foreach collection="charge_code_list" item="charge_code_list"
				index="index" open="(" close=")" separator=",">
				#{charge_code_list}
			</foreach>
		</if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="PA_queryPolicyFundChargeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	<sql id="PA_queryPolicyFundChargeByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="PA_queryPolicyFundChargeByProductIdCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
	</sql>
	<sql id="PA_queryPolicyFundChargeByChargeCodeCondition">
		<if test=" charge_code != null and charge_code != '' "><![CDATA[ AND A.CHARGE_CODE = #{charge_code} ]]></if>
	</sql>
	<sql id="PA_queryPolicyFundChargeByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="PA_addPolicyFundCharge" useGeneratedKeys="true"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_FUND_CHARGE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_FUND_CHARGE(
				PRODUCT_ID, CHARGE_DUE_DATE, INSERT_TIME, CHARGE_CODE, UPDATE_TIME, ITEM_ID, LAST_CHARGE_DATE, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, POLICY_ID ) 
			VALUES (
				#{product_id, jdbcType=NUMERIC}, #{charge_due_date, jdbcType=DATE} , SYSDATE , #{charge_code, jdbcType=VARCHAR} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{last_charge_date, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="PA_deletePolicyFundCharge" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="PA_updatePolicyFundCharge" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_FUND_CHARGE ]]>
		<set>
			<trim suffixOverrides=",">
				PRODUCT_ID = #{product_id,
				jdbcType=NUMERIC} ,
				CHARGE_DUE_DATE = #{charge_due_date,
				jdbcType=DATE} ,
				CHARGE_CODE = #{charge_code, jdbcType=VARCHAR} ,
				UPDATE_TIME = SYSDATE ,
				ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
				LAST_CHARGE_DATE = #{last_charge_date, jdbcType=DATE} ,
				UPDATE_BY =
				#{update_by, jdbcType=NUMERIC} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
				,
				BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
				POLICY_ID =
				#{policy_id, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="PA_findPolicyFundChargeByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="PA_findPolicyFundChargeByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="PA_findPolicyFundChargeByProductId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeByProductIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="PA_findPolicyFundChargeByChargeCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeByChargeCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="PA_findPolicyFundChargeByItemId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeByItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询单条数据 -->
	<select id="PA_findPolicyFundCharge" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE 1 = 1  ]]>
		<include refid="PA_policyFundChargeWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyFundCharge" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyFundCharge" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyFundChargeWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="PA_findPolicyFundChargeTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="PA_queryPolicyFundChargeForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRODUCT_ID, B.CHARGE_DUE_DATE, B.CHARGE_CODE, B.ITEM_ID, B.LAST_CHARGE_DATE, 
			B.LIST_ID, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.CHARGE_CODE, A.ITEM_ID, A.LAST_CHARGE_DATE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

	<!-- 更新上次扣费日为复效日 -->
	<update id="PA_updateLastChargeDate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_FUND_CHARGE A ]]>
		<set>
			<trim suffixOverrides=",">
				A.UPDATE_TIME = SYSDATE ,
				A.LAST_CHARGE_DATE = #{last_charge_date, jdbcType=DATE} ,
				A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
			</trim>
		</set>
		<![CDATA[ 	WHERE A.POLICY_ID = #{policy_id} AND A.BUSI_ITEM_ID = #{busi_item_id} ]]>
	</update>
</mapper>
