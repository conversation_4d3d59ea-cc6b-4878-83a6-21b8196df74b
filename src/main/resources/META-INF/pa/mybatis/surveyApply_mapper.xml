<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.SurveyApplyPO">

	<sql id="surveyApplyWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" survey_type  != null "><![CDATA[ AND A.SURVEY_TYPE = #{survey_type} ]]></if>
		<if test=" apply_section  != null "><![CDATA[ AND A.APPLY_SECTION = #{apply_section} ]]></if>
		<if test=" internal_resualt != null and internal_resualt != ''  "><![CDATA[ AND A.INTERNAL_RESUALT = #{internal_resualt} ]]></if>
		<if test=" survey_rule_id  != null "><![CDATA[ AND A.SURVEY_RULE_ID = #{survey_rule_id} ]]></if>
		<if test=" survey_doc_id  != null "><![CDATA[ AND A.SURVEY_DOC_ID = #{survey_doc_id} ]]></if>
		<if test=" survey_per  != null "><![CDATA[ AND A.SURVEY_PER = #{survey_per} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" survey_status  != null "><![CDATA[ AND A.SURVEY_STATUS = #{survey_status} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" survey_mode != null and survey_mode != ''  "><![CDATA[ AND A.SURVEY_MODE = #{survey_mode} ]]></if>
		<if test=" repeal_reason != null and repeal_reason != ''  "><![CDATA[ AND A.REPEAL_REASON = #{repeal_reason} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_desc != null and survey_desc != ''  "><![CDATA[ AND A.SURVEY_DESC = #{survey_desc} ]]></if>
		<if test=" apply_org != null and apply_org != ''  "><![CDATA[ AND A.APPLY_ORG = #{apply_org} ]]></if>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_advice != null and survey_advice != ''  "><![CDATA[ AND A.SURVEY_ADVICE = #{survey_advice} ]]></if>
		<if test=" survey_reason != null and survey_reason != ''  "><![CDATA[ AND A.SURVEY_REASON = #{survey_reason} ]]></if>
		<if test=" cs_apply_code != null and cs_apply_code != ''  "><![CDATA[ AND A.CS_APPLY_CODE = #{cs_apply_code} ]]></if>
		<if test=" survey_org != null and survey_org != ''  "><![CDATA[ AND A.SURVEY_ORG = #{survey_org} ]]></if>
		<if test=" cs_background != null and cs_background != ''  "><![CDATA[ AND A.CS_BACKGROUND = #{cs_background} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id, jdbcType=NUMERIC} ]]></if>
		<if test=" cs_accept_code != null and cs_accept_code != ''  "><![CDATA[ AND A.CS_ACCEPT_CODE = #{cs_accept_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survey_by_level  != null "><![CDATA[ AND A.SURVEY_BY_LEVEL = #{survey_by_level} ]]></if>
		<if test=" survey_code != null and survey_code != ''  "><![CDATA[ AND A.SURVEY_CODE = #{survey_code} ]]></if>
		<if test=" apply_per  != null "><![CDATA[ AND A.APPLY_PER = #{apply_per} ]]></if>
		<if test=" cs_item != null and cs_item != ''  "><![CDATA[ AND A.CS_ITEM = #{cs_item} ]]></if>
	</sql>

    <sql id="surveyApplyWhereConditionByCaseId">
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="querySurveyApplyByApplyIdCondition">
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSurveyApplyLocal"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="apply_id">
			SELECT APP___PAS__DBUSER.S_SURVEY_APPLY.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVEY_APPLY(
				RELATED_ID, SURVEY_TYPE, APPLY_SECTION, INTERNAL_RESUALT, SURVEY_RULE_ID, SURVEY_DOC_ID, SURVEY_PER, 
				REMARK, SURVEY_STATUS, APPLY_DATE, SURVEY_MODE, REPEAL_REASON, CASE_NO, APPLY_CODE, 
				SURVEY_DESC, INSERT_TIMESTAMP, UPDATE_BY, APPLY_ORG, APPLY_ID, BIZ_TYPE, SURVEY_ADVICE, 
				SURVEY_REASON, INSERT_TIME, CS_APPLY_CODE, SURVEY_ORG, UPDATE_TIME, CS_BACKGROUND, CASE_ID, 
				CS_ACCEPT_CODE, POLICY_CODE, SURVEY_BY_LEVEL, SURVEY_CODE, APPLY_PER, UPDATE_TIMESTAMP, INSERT_BY, 
				CS_ITEM ) 
			VALUES (
				#{related_id, jdbcType=NUMERIC}, #{survey_type, jdbcType=NUMERIC} , #{apply_section, jdbcType=NUMERIC} , #{internal_resualt, jdbcType=VARCHAR} , #{survey_rule_id, jdbcType=NUMERIC} , #{survey_doc_id, jdbcType=NUMERIC} , #{survey_per, jdbcType=NUMERIC} 
				, #{remark, jdbcType=VARCHAR} , #{survey_status, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} , #{survey_mode, jdbcType=VARCHAR} , #{repeal_reason, jdbcType=VARCHAR} , #{case_no, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} 
				, #{survey_desc, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{apply_org, jdbcType=VARCHAR} , #{apply_id, jdbcType=NUMERIC} , #{biz_type, jdbcType=NUMERIC} , #{survey_advice, jdbcType=VARCHAR} 
				, #{survey_reason, jdbcType=VARCHAR} , SYSDATE , #{cs_apply_code, jdbcType=VARCHAR} , #{survey_org, jdbcType=VARCHAR} , SYSDATE , #{cs_background, jdbcType=VARCHAR} , #{case_id, jdbcType=NUMERIC} 
				, #{cs_accept_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{survey_by_level, jdbcType=NUMERIC} , #{survey_code, jdbcType=VARCHAR} , #{apply_per, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{cs_item, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSurveyApply" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SURVEY_APPLY WHERE APPLY_ID = #{apply_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSurveyApply" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURVEY_APPLY ]]>
		<set>
		<trim suffixOverrides=",">
		    RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
		    SURVEY_TYPE = #{survey_type, jdbcType=NUMERIC} ,
		    APPLY_SECTION = #{apply_section, jdbcType=NUMERIC} ,
			INTERNAL_RESUALT = #{internal_resualt, jdbcType=VARCHAR} ,
		    SURVEY_RULE_ID = #{survey_rule_id, jdbcType=NUMERIC} ,
		    SURVEY_DOC_ID = #{survey_doc_id, jdbcType=NUMERIC} ,
		    SURVEY_PER = #{survey_per, jdbcType=NUMERIC} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
		    SURVEY_STATUS = #{survey_status, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
			SURVEY_MODE = #{survey_mode, jdbcType=VARCHAR} ,
			REPEAL_REASON = #{repeal_reason, jdbcType=VARCHAR} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			SURVEY_DESC = #{survey_desc, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			APPLY_ORG = #{apply_org, jdbcType=VARCHAR} ,
		    BIZ_TYPE = #{biz_type, jdbcType=NUMERIC} ,
			SURVEY_ADVICE = #{survey_advice, jdbcType=VARCHAR} ,
			SURVEY_REASON = #{survey_reason, jdbcType=VARCHAR} ,
			CS_APPLY_CODE = #{cs_apply_code, jdbcType=VARCHAR} ,
			SURVEY_ORG = #{survey_org, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			CS_BACKGROUND = #{cs_background, jdbcType=VARCHAR} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			CS_ACCEPT_CODE = #{cs_accept_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVEY_BY_LEVEL = #{survey_by_level, jdbcType=NUMERIC} ,
			SURVEY_CODE = #{survey_code, jdbcType=VARCHAR} ,
		    APPLY_PER = #{apply_per, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CS_ITEM = #{cs_item, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_ID = #{apply_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="claimFindSurveyApplyByApplyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1  ]]>
		<include refid="querySurveyApplyByApplyIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	<select id="findSurveyApplyByConditions" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1 ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>	
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_status  != null "><![CDATA[ AND A.SURVEY_STATUS = #{survey_status} ]]></if>
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="findAllMapSurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="claimFindAllSurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<include refid="surveyApplyWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
	</select>
	<!-- 查询是否有未完成的调查 -->
	<select id="claimFindNotFinishSurvey" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT
			A.CASE_NO
			FROM APP___PAS__DBUSER.T_SURVEY_APPLY A,APP___PAS__DBUSER.T_SURVEY_CONCLUSION B WHERE A.apply_id = B.apply_id AND A.BIZ_TYPE=1 AND B.FINISH_DATE IS NULL ]]>
		<if test=" case_no != null and case_no != '' ">
		    <![CDATA[AND A.CASE_NO = #{case_no }]]>
		</if>
		
	</select>

<!-- 查询个数操作 -->
	<select id="findSurveyApplyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1  ]]>
		<include refid="surveyApplyWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="querySurveyApplyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATED_ID, B.SURVEY_TYPE, B.APPLY_SECTION, B.INTERNAL_RESUALT, B.SURVEY_RULE_ID, B.SURVEY_DOC_ID, B.SURVEY_PER, 
			B.REMARK, B.SURVEY_STATUS, B.APPLY_DATE, B.SURVEY_MODE, B.REPEAL_REASON, B.CASE_NO, B.APPLY_CODE, 
			B.SURVEY_DESC, B.APPLY_ORG, B.APPLY_ID, B.BIZ_TYPE, B.SURVEY_ADVICE, 
			B.SURVEY_REASON, B.CS_APPLY_CODE, B.SURVEY_ORG, B.CS_BACKGROUND, B.CASE_ID, 
			B.CS_ACCEPT_CODE, B.POLICY_CODE, B.SURVEY_BY_LEVEL, B.SURVEY_CODE, B.APPLY_PER, 
			B.CS_ITEM FROM (
					SELECT ROWNUM RN, A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="surveyApplyWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- and by caoyy_wb 风险评估报告页面 -->
	<select id="querySurveyApplyByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<include refid="surveyApplyWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
	</select>
	<!-- 查询单条数据 -->
	<select id="findSurveyApply" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM APP___PAS__DBUSER.T_SURVEY_APPLY  A WHERE 1 = 1]]>
		<include refid="surveyApplyWhereCondition" />
	</select>
	<!-- add by zhaoyq 自动分期给付支付批处理-->
	<select id="findSurveyApplyByRelatedId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE A.RELATED_ID = #{related_id}  ]]>
	</select>
	<!-- add by zhangjy_wb 查询调查任务-->
	<select id="queryAllSurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___PAS__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1 AND A.BIZ_TYPE = 1 AND A.SURVEY_STATUS NOT IN (2,3)]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>	
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<!-- 发起调查信息查询-->
	<select id="queryClaimResearch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.CASE_NO,A.SURVEY_RULE_ID,A.APPLY_ORG,A.SURVEY_REASON,A.SURVEY_ORG,A.SURVEY_STATUS,
				       A.SURVEY_DESC,A.SURVEY_TYPE,
				       B.CUSTOMER_ID,C.NAME AS SURVEY_REASON_NAME, D.ORGAN_NAME,E.SURVEY_ITEM,F.CUSTOMER_VIP,
				       G.NAME AS SURVEY_STATUS_NAME, H.NAME AS SURVEY_TYPE_NAME
				FROM 
				       APP___PAS__DBUSER.T_SURVEY_APPLY A, APP___PAS__DBUSER.T_SURVEY_OBJECT B, APP___PAS__DBUSER.T_SURVEY_REASON C, APP___PAS__DBUSER.T_UDMP_ORG D, APP___PAS__DBUSER.T_SURVEY_ITEM E,
				       APP___PAS__DBUSER.T_CUSTOMER F, APP___PAS__DBUSER.T_SURVEY_STATUS G, APP___PAS__DBUSER.T_SURVEY_TYPE H
				WHERE 
				       A.Case_No ='111'
				       AND B.APPLY_ID = A.Apply_Id
				       AND C.CODE = A.SURVEY_REASON
				       AND D.ORGAN_CODE = A.Survey_Org
				       AND E.APPLY_ID = A.APPLY_ID 
				       AND G.CODE = A.SURVEY_STATUS
				       AND H.CODE = A.SURVEY_TYPE]]>
		<![CDATA[ ORDER BY A.SURVEY_RULE_ID, B.CUSTOMER_ID]]>
	</select>
	
	<!-- 操作履历下生调结果查询-->
	<select id="pa_querySurveyApply1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  
						(SELECT CM.APPLY_CODE
				         FROM T_CONTRACT_MASTER CM
				        WHERE CM.POLICY_CODE = APP.POLICY_CODE)  AS APPLY_CODE , 
       					APP.SURVEY_PER,
				        APP.APPLY_PER,
				        APP.APPLY_DATE,
				        T.MAXFINISHDATE,
				        APP.SURVEY_STATUS,
				        APP.APPLY_ID,
					    APP.SURVEY_REASON
				 FROM 	APP___PAS__DBUSER.T_SURVEY_APPLY APP,
					     (SELECT APP.APPLY_ID ,
					           MAX(CON.FINISH_DATE) MAXFINISHDATE
					      FROM APP___PAS__DBUSER.T_SURVEY_APPLY APP,
					           APP___PAS__DBUSER.T_SURVEY_CONCLUSION CON
					      WHERE APP.APPLY_ID = CON.APPLY_ID
					      GROUP BY APP.APPLY_ID) T
				 WHERE 	T.APPLY_ID=APP.APPLY_ID
				  AND EXISTS (SELECT 1
                 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER M
                WHERE APP.POLICY_CODE = M.POLICY_CODE
                  ]]>
		<if test=" apply_code  != null "><![CDATA[ AND M.APPLY_CODE = #{apply_code} ]]></if>
		<![CDATA[)]]>
	</select>
	
	<!-- 操作履历下生调结果查询-->
	<select id="pa_querySurveyApply2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  
						M.APPLY_CODE,
       					APP.SURVEY_PER,
				        APP.APPLY_PER,
				        APP.APPLY_DATE,
				        APP.SURVEY_STATUS,
				        APP.APPLY_ID,
					    APP.SURVEY_REASON
				 FROM 	APP___PAS__DBUSER.T_SURVEY_APPLY APP,
				 		APP___PAS__DBUSER.T_CONTRACT_MASTER M
				 WHERE  APP.POLICY_CODE = M.POLICY_CODE 		
					     
                  ]]>
		<if test=" apply_code  != null "><![CDATA[ AND M.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	
	<!-- 查询回复日期 -->
	<select id="pa_queryReplydate2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT APP.APPLY_ID ,
                     MAX(CON.FINISH_DATE) MAXFINISHDATE
                FROM APP___PAS__DBUSER.T_SURVEY_APPLY APP,
                     APP___PAS__DBUSER.T_SURVEY_CONCLUSION CON
                WHERE APP.APPLY_ID = CON.APPLY_ID
                  ]]>
		<if test=" apply_id  != null "><![CDATA[ AND APP.APPLY_ID = #{apply_id} ]]></if>
		<![CDATA[GROUP BY APP.APPLY_ID   ]]>
	</select>
	
	<!-- 操作履历下生调结果查询,查询回复人-->
	<select id="pa_querySurveyApply_orgOpr1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT C.ORG_OPR,
						A.APPLY_ID
				 FROM   APP___PAS__DBUSER.T_SURVEY_APPLY A,
					    APP___PAS__DBUSER.T_SURVEY_CONCLUSION C
				 WHERE  A.APPLY_ID = C.APPLY_ID]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>
	<!-- 操作履历下生调结果查询SurveyConclusion-->
	<select id="pa_querySurveyApplyCon1" resultType="java.util.Map" parameterType="java.util.Map">
		
		<![CDATA[   select A.SURVEY_PER,A.APPLY_PER,A.APPLY_DATE,A.SURVEY_STATUS,A.APPLY_ID,A.SURVEY_REASON,CON.FINISHDATE,FEE.SURVEYFEE,B.REMARK
               ,B.SURVEY_CONCLUSION
               ,B.POSITIVE_FLAG
        
        from  APP___PAS__DBUSER.T_SURVEY_APPLY A
        left join  APP___PAS__DBUSER.T_CONTRACT_MASTER M on A.POLICY_CODE = M.POLICY_CODE
        LEFT JOIN APP___PAS__DBUSER.T_SURVEY_CONCLUSION B  ON A.APPLY_ID=B.APPLY_ID
        LEFT JOIN (SELECT A.APPLY_ID , MAX(C.FINISH_DATE) FINISHDATE
            FROM APP___PAS__DBUSER.T_SURVEY_APPLY A ,
                 APP___PAS__DBUSER.T_SURVEY_CONCLUSION C
            WHERE A.APPLY_ID = C.APPLY_ID
            GROUP BY A.APPLY_ID) CON
         on  CON.APPLY_ID=A.APPLY_ID
         left join  (SELECT A.APPLY_ID,SUM(F.SURVEY_FEE) SURVEYFEE
            FROM APP___PAS__DBUSER.T_SURVEY_APPLY A
            ,APP___PAS__DBUSER.T_SURVEY_FEE F
            WHERE A.APPLY_ID = F.APPLY_ID
            GROUP BY A.APPLY_ID) FEE on A.APPLY_ID = FEE.APPLY_ID where M.APPLY_CODE=#{apply_code} ]]>
	</select>
	
	<!-- 操作履历下生调结果查询-->
	<select id="pa_querySurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
				       APP.POLICY_CODE
				       ,APP.APPLY_CODE
				       ,APP.SURVEY_PER
				       ,APP.APPLY_PER
				       ,APP.APPLY_DATE
				       ,CON.ORG_OPR
				       ,CON.FINISH_DATE
				       ,APP.SURVEY_STATUS
				       ,APP.APPLY_ID
				       ,APP.SURVEY_REASON
				       ,APP.APPLY_PER
				       ,APP.APPLY_DATE
				       ,FEE.SURVEYFEE
				       ,CON.REMARK
				       ,CON.SURVEY_CONCLUSION
				       ,CON.POSITIVE_FLAG
				FROM   APP___PAS__DBUSER.T_SURVEY_APPLY APP 
				      ,APP___PAS__DBUSER.T_SURVEY_CONCLUSION CON
				      ,(SELECT A.APPLY_ID,SUM(F.SURVEY_FEE) SURVEYFEE
									  FROM APP___PAS__DBUSER.T_SURVEY_APPLY A
									  ,APP___PAS__DBUSER.T_SURVEY_FEE F
									  WHERE A.APPLY_ID = F.APPLY_ID
									  GROUP BY A.APPLY_ID) FEE 
				
				WHERE APP.APPLY_ID = CON.APPLY_ID
				AND APP.APPLY_ID = FEE.APPLY_ID
				AND EXISTS (SELECT 1
				                 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER M
				                WHERE APP.POLICY_CODE = M.POLICY_CODE
                ]]>
		<if test=" apply_code  != null "><![CDATA[ AND M.APPLY_CODE = #{apply_code} ]]></if>
		<![CDATA[)]]>	
	</select>	
	
	<!-- 操作履历下生调结果查询SurveyFee-->
	<select id="pa_querySurveyApplyFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT ITEM.SURVEY_ITEM 
				       ,ITYPE.VALUE
				       ,ITEM.REMARK
				FROM APP___PAS__DBUSER.T_SURVEY_ITEM ITEM,
				     APP___PAS__DBUSER.T_SURVEY_ITEM_TYPE ITYPE,
				     APP___PAS__DBUSER.T_SURVEY_APPLY A
				WHERE A.APPLY_ID = ITEM.APPLY_ID
				AND   ITEM.SURVEY_ITEM = ITYPE.CODE]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>
	
	
	<!-- 添加操作 -->
	<insert id="addSurveyObject"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="object_id">
			SELECT APP___PAS__DBUSER.S_SURVEY_OBJECT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVEY_OBJECT(
				SURVEY_RESUALT, SURVEY_REASON, LIFE_FLAG, INSERT_TIME, OBJECT_ID, REMARK, CUSTOMER_ID, 
				UPDATE_TIME, CUS_NAME, CERTI_NO, INSERT_TIMESTAMP, CHECK_FLAG, UPDATE_BY, UPDATE_TIMESTAMP, 
				INSERT_BY, SURVEY_OBJ_TYPE, CERTI_TYPE, APPLY_ID ) 
			VALUES (
				#{survey_resualt, jdbcType=VARCHAR}, #{survey_reason, jdbcType=VARCHAR} , #{life_flag, jdbcType=NUMERIC} , SYSDATE , #{object_id, jdbcType=NUMERIC} , #{remark, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} 
				, SYSDATE , #{cus_name, jdbcType=VARCHAR} , #{certi_no, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{check_flag, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{survey_obj_type, jdbcType=VARCHAR} , #{certi_type, jdbcType=VARCHAR} , #{apply_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
	<!-- 添加操作 -->
	<insert id="addSurveyItem"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="item_id">
			SELECT APP___PAS__DBUSER.S_SURVEY_ITEM.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVEY_ITEM(
				INSERT_TIMESTAMP, UPDATE_BY, SURVEY_ITEM, INSERT_TIME, REMARK, UPDATE_TIME, UPDATE_TIMESTAMP, 
				ITEM_ID, INSERT_BY, APPLY_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{survey_item, jdbcType=VARCHAR} , SYSDATE , #{remark, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP
				, #{item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{apply_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
</mapper>
