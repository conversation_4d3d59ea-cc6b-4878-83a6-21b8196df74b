<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="orphanPolicy">
	
	<sql id="findOrphanPolicyWhereCondition">
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND T.ORGAN_CODE LIKE '%'|| LTRIM(RTRIM(#{organ_code}, ' ') , ' ') ||'%'  ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND T.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" sales_organ_code != null and sales_organ_code != ''  "><![CDATA[ AND C.SALES_ORGAN_CODE = #{sales_organ_code} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TCU.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND TCU.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND T.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" prem_max != null and prem_max != ''  "><![CDATA[ AND tcp.std_prem_af <= #{prem_max} ]]></if>
		<if test=" prem_min != null and prem_min != ''  "><![CDATA[ AND tcp.std_prem_af >= #{prem_min} ]]></if>
		<if test="list != null and list.size()!=0">
		<![CDATA[ AND T.POLICY_CODE IN ]]>
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
       </foreach>
		</if>
		<if test="policy_code_list != null and policy_code_list.size()!=0">
			<![CDATA[ AND T.POLICY_CODE IN (SELECT POLICY_CODE FROM T_POLICY_CODE_LIST)]]>
		</if>
		<if test="date != null and date != '' and date == 'mm-dd' ">
			<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'mm-dd') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'mm-dd') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'mm-dd') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'mm-dd') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'mm-dd') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'mm-dd') <= #{expiry_date_end} ]]></if>
		</if>
		<if test="date != null and date != '' and date == 'dd' ">
		<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'dd') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'dd') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'dd') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'dd') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'dd') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'dd') <= #{expiry_date_end} ]]></if>
		</if>
		<if test="date != null and date != '' and date == 'mm' ">
		<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'mm') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'mm') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'mm') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'mm') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'mm') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'mm') <= #{expiry_date_end} ]]></if>
		</if>
		<if test="date != null and date != '' and date == 'yyyy' ">
		<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy') <= #{expiry_date_end} ]]></if>
		</if>
		<if test="date != null and date != '' and date == 'yyyy-dd' ">
		<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-dd') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-dd') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-dd') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-dd') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-dd') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-dd') <= #{expiry_date_end} ]]></if>
		</if>
		<if test="date != null and date != '' and date == 'yyyy-mm' ">
		<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-mm') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-mm') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-mm') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-mm') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-mm') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-mm') <= #{expiry_date_end} ]]></if>
		</if>
		<if test="date != null and date != '' and date == 'yyyy-mm-dd' ">
		<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-mm-dd') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-mm-dd') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-mm-dd') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-mm-dd') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-mm-dd') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-mm-dd') <= #{expiry_date_end} ]]></if>
		</if>
		<if test=" serial_no != null and serial_no != ''  "><![CDATA[ 
		 AND EXISTS (
           SELECT '1' FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL TOPL,DEV_PAS.T_ORPHAN_PARAMETER TOPM WHERE TOPL.POLICY_CODE = T.POLICY_CODE AND   TOPM.LIST_ID = TOPL.MAIN_ID AND TOPM.SERIAL_NO = #{serial_no}) ]]></if>
	</sql>
<sql id="PA_orphanParameterWhereCondition"> 
		<if test=" serial_no != null and serial_no != ''  "><![CDATA[ AND A.SERIAL_NO = #{serial_no} ]]></if>
		<if test=" list_id != null and list_id != ''  "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" notice_status != null and notice_status != ''  "><![CDATA[ AND A.NOTICE_STATUS = #{notice_status} ]]></if>
	</sql>
	<select id="PA_findAllOrphanPolicyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
            SELECT COUNT(1)
              FROM (SELECT DISTINCT POLICY_CODE
  FROM (SELECT DISTINCT T.POLICY_CODE
          FROM DEV_PAS.T_CONTRACT_MASTER T
          JOIN DEV_PAS.T_CONTRACT_AGENT TCA
            ON T.POLICY_ID = TCA.POLICY_ID
          JOIN DEV_PAS.T_AGENT C
            ON TCA.AGENT_CODE = C.AGENT_CODE
          LEFT JOIN (SELECT TC.CUSTOMER_ID, TC.CUSTOMER_NAME, TPH.POLICY_CODE
                      FROM DEV_PAS.T_CUSTOMER TC
                      LEFT JOIN DEV_PAS.T_POLICY_HOLDER TPH
                        ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID) TCU
            ON TCU.POLICY_CODE = T.POLICY_CODE
          LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
            ON TCBP.POLICY_ID = T.POLICY_ID
          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
            ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
         WHERE 1 = 1
           AND TCA.IS_CURRENT_AGENT = '1'
           AND TCBP.MASTER_BUSI_ITEM_ID IS NULL]]>
           <include refid="findOrphanPolicyWhereCondition" />
           <![CDATA[
           AND EXISTS
         (SELECT 'x'
                  FROM DEV_PAS.T_POLICY_CHANGE TPC
                 WHERE TPC.INSERT_TIME >= TRUNC(SYSDATE)
                   AND TPC.INSERT_TIME <= SYSDATE
                   AND T.POLICY_ID = TPC.POLICY_ID
                   AND TPC.SERVICE_CODE IN ('RE',
                                            'RS',
                                            'PU',
                                            'NS',
                                            'PBA',
                                            'EA',
                                            'DA',
                                            'PAPL',
                                            'RA',
                                            'PR',
                                            'DT',
                                            'FK',
                                            'IO',
                                            'CA',
                                            'AM',
                                            'CLMEND',
                                            'PPL',
                                            'PME',
                                            'YS',
                                            'EN',
                                            'PREF',
                                            'MR',
                                            'PLPE',
                                            'PNL',
                                            'PC',
                                            'CM',
                                            'IT',
                                            'MD',
                                            'LR',
                                            'PPD',
                                            'PILPI',
                                            'MC',
                                            'PA',
                                            'GB',
                                            'AE',
                                            'XD',
                                            'PIM',
                                            'PLE',
                                            'PICD',
                                            'PRE',
                                            'PRL',
                                            'CT',
                                            'XT',
                                            'AP',
                                            'PT',
                                            'FM',
                                            'CB',
                                            '00' ))
        UNION
        SELECT T.POLICY_CODE
          FROM DEV_PAS.T_CONTRACT_MASTER T
          JOIN DEV_PAS.T_CONTRACT_AGENT TCA
            ON T.POLICY_ID = TCA.POLICY_ID
          JOIN DEV_PAS.T_AGENT C
            ON TCA.AGENT_CODE = C.AGENT_CODE
          LEFT JOIN (SELECT TC.CUSTOMER_ID, TC.CUSTOMER_NAME, TPH.POLICY_CODE
                      FROM DEV_PAS.T_CUSTOMER TC
                      LEFT JOIN DEV_PAS.T_POLICY_HOLDER TPH
                        ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID) TCU
            ON TCU.POLICY_CODE = T.POLICY_CODE
          LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
            ON TCBP.POLICY_ID = T.POLICY_ID
          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
            ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
         WHERE 1 = 1
           AND TCA.IS_CURRENT_AGENT = '1'
           AND TCBP.MASTER_BUSI_ITEM_ID IS NULL              
           AND TCA.INSERT_TIME >= TRUNC(SYSDATE)
           AND TCA.INSERT_TIME < =SYSDATE              
                  ]]>
			<include refid="findOrphanPolicyWhereCondition" />
         <![CDATA[ ) ) ]]>
	</select>
	<select id="findOrphanPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT ROWNUM RN,
             T.POLICY_ID,
             T.POLICY_CODE,
             T.ORGAN_CODE,
             T.SALE_AGENT_CODE,
             T.APPLY_CODE,
             T.INITIAL_VALIDATE_DATE,
             T.LIABILITY_STATE,
             T.INITIAL_PREM_DATE,
             T.VALIDATE_DATE,
             T.CHANNEL_TYPE,
             TCA.AGENT_ORGAN_CODE,
             TCA.AGENT_CODE,
             TCU.CUSTOMER_ID,
             TCU.Old_Customer_Id,
             TCU.CUSTOMER_NAME,
             TCU.CUSTOMER_CERT_TYPE,
             TCU.CUSTOMER_CERTI_CODE,
             TCU.CUSTOMER_BIRTHDAY,
             TCU.MARRIAGE_DATE,
             TCU.MARRIAGE_STATUS,
             TCU.MOBILE_TEL,
             TFEE.PREM,
             TFEE.AMOUNT,
             TPA.NEXT_ACCOUNT,
             TFEE.TOTAL_PREM_AF,
             TCED.PAY_DUE_DATE,
             TCED.POLICY_PERIOD,
             TCED.PREM_FREQ,
             TCED.INITIAL_PREM_DATE,
             TCED.CHARGE_PERIOD,
             TPA.PAY_LOCATION,
             TPA.PAY_NEXT,
             C.SALES_ORGAN_CODE,
             TCU.ADDRESS_ID
        FROM DEV_PAS.T_CONTRACT_MASTER T
        JOIN DEV_PAS.T_CONTRACT_AGENT TCA
          ON T.POLICY_ID = TCA.POLICY_ID
        JOIN DEV_PAS.T_AGENT C
          ON TCA.AGENT_CODE = C.AGENT_CODE
        LEFT JOIN (SELECT TC.Old_Customer_Id,
        tc.customer_id,
                          TC.CUSTOMER_NAME,
                          TC.CUSTOMER_BIRTHDAY,
                          TC.CUSTOMER_CERT_TYPE,
                          TC.CUSTOMER_CERTI_CODE,
                          TC.MARRIAGE_DATE,
                          TC.MARRIAGE_STATUS,
                          TC.MOBILE_TEL,
                          TPH.POLICY_CODE,
                          (SELECT MAX(TBP1.ADDRESS_ID)
                             FROM DEV_PAS.T_ADDRESS TBP1
                            WHERE TBP1.CUSTOMER_ID = TPH.CUSTOMER_ID) ADDRESS_ID
                     FROM DEV_PAS.T_CUSTOMER TC
                     LEFT JOIN DEV_PAS.T_POLICY_HOLDER TPH
                       ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID) TCU
          ON TCU.POLICY_CODE = T.POLICY_CODE
        LEFT JOIN (SELECT SUM(NVL(T.STD_PREM_AF, 0)) PREM,
                          SUM(NVL(T.AMOUNT, 0)) AMOUNT,
                          SUM(NVL(T.TOTAL_PREM_AF, 0)) TOTAL_PREM_AF,
                          T.POLICY_CODE
                     FROM DEV_PAS.T_CONTRACT_PRODUCT T
                    GROUP BY T.POLICY_CODE) TFEE
          ON T.POLICY_CODE = TFEE.POLICY_CODE
        LEFT JOIN (SELECT TCE.PAY_DUE_DATE,
                          T.POLICY_ID TCEID,
                          TCE.POLICY_PERIOD,
                          TCP.PREM_FREQ,
                          T.INITIAL_PREM_DATE,
                          TCP.CHARGE_PERIOD
                     FROM DEV_PAS.T_CONTRACT_BUSI_PROD T
                     LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
                       ON TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                     LEFT JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
                       ON TCE.ITEM_ID = TCP.ITEM_ID
                    WHERE T.MASTER_BUSI_ITEM_ID IS NULL) TCED
          ON TCED.TCEID = T.POLICY_ID
        LEFT JOIN DEV_PAS.T_PAYER_ACCOUNT TPA
          ON TPA.POLICY_ID = T.POLICY_ID
        LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
          ON TCBP.POLICY_ID = T.POLICY_ID
        LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
          ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
       WHERE 1 = 1
         AND TCA.IS_CURRENT_AGENT = '1'
         AND TCBP.MASTER_BUSI_ITEM_ID IS NULL  ]]>
         <include refid="findOrphanPolicyWhereCondition" />  
       <![CDATA[  AND  EXISTS (SELECT 'x'
                FROM DEV_PAS.T_POLICY_CHANGE TPC
               WHERE TPC.INSERT_TIME >= TRUNC(sysdate)
                 and TPC.INSERT_TIME <= sysdate
                 AND T.POLICY_ID = TPC.POLICY_ID
                 and TPC.Service_Code in (
                'RE',
              'RS',
              'PU',
              'NS',
              'PBA',
              'EA',
              'DA',
              'PAPL',
              'RA',
              'PR',
              'DT',
              'FK',
              'IO',
              'CA',
              'AM',
              'CLMEND',
              'PPL',
              'PME',
              'YS',
              'EN',
              'PREF',
              'MR',
              'PLPE',
              'PNL',
              'PC',
              'CM',
              'IT',
              'MD',
              'LR',
              'PPD',
              'PILPI',
              'MC',
              'PA',
              'GB',
              'AE',
              'XD',
              'PIM',
              'PLE',
              'PICD',
              'PRE',
              'PRL',
              'CT',
              'XT',
              'AP',
              'PT',
              'FM',
              'CB',
              '00'      
              )
                 ) union 
                 
                       SELECT ROWNUM RN,
             T.POLICY_ID,
             T.POLICY_CODE,
             T.ORGAN_CODE,
             T.SALE_AGENT_CODE,
             T.APPLY_CODE,
             T.INITIAL_VALIDATE_DATE,
             T.LIABILITY_STATE,
             T.INITIAL_PREM_DATE,
             T.VALIDATE_DATE,
             T.CHANNEL_TYPE,
             TCA.AGENT_ORGAN_CODE,
             TCA.AGENT_CODE,
             TCU.CUSTOMER_ID,
             TCU.OLD_CUSTOMER_ID,
             TCU.CUSTOMER_NAME,
             TCU.CUSTOMER_CERT_TYPE,
             TCU.CUSTOMER_CERTI_CODE,
             TCU.CUSTOMER_BIRTHDAY,
             TCU.MARRIAGE_DATE,
             TCU.MARRIAGE_STATUS,
             TCU.MOBILE_TEL,
             TFEE.PREM,
             TFEE.AMOUNT,
             TPA.NEXT_ACCOUNT,
             TFEE.TOTAL_PREM_AF,
             TCED.PAY_DUE_DATE,
             TCED.POLICY_PERIOD,
             TCED.PREM_FREQ,
             TCED.INITIAL_PREM_DATE,
             TCED.CHARGE_PERIOD,
             TPA.PAY_LOCATION,
             TPA.PAY_NEXT,
             C.SALES_ORGAN_CODE,
             TCU.ADDRESS_ID
        FROM DEV_PAS.T_CONTRACT_MASTER T
        JOIN DEV_PAS.T_CONTRACT_AGENT TCA
          ON T.POLICY_ID = TCA.POLICY_ID
        JOIN DEV_PAS.T_AGENT C
          ON TCA.AGENT_CODE = C.AGENT_CODE
        LEFT JOIN (SELECT TC.Old_Customer_Id,
        				  tc.CUSTOMER_ID,
                          TC.CUSTOMER_NAME,
                          TC.CUSTOMER_BIRTHDAY,
                          TC.CUSTOMER_CERT_TYPE,
                          TC.CUSTOMER_CERTI_CODE,
                          TC.MARRIAGE_DATE,
                          TC.MARRIAGE_STATUS,
                          TC.MOBILE_TEL,
                          TPH.POLICY_CODE,
                          (SELECT MAX(TBP1.ADDRESS_ID)
                             FROM DEV_PAS.T_ADDRESS TBP1
                            WHERE TBP1.CUSTOMER_ID = TPH.CUSTOMER_ID) ADDRESS_ID
                     FROM DEV_PAS.T_CUSTOMER TC
                     LEFT JOIN DEV_PAS.T_POLICY_HOLDER TPH
                       ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID) TCU
          ON TCU.POLICY_CODE = T.POLICY_CODE
        LEFT JOIN (SELECT SUM(NVL(T.STD_PREM_AF, 0)) PREM,
                          SUM(NVL(T.AMOUNT, 0)) AMOUNT,
                          SUM(NVL(T.TOTAL_PREM_AF, 0)) TOTAL_PREM_AF,
                          T.POLICY_CODE
                     FROM DEV_PAS.T_CONTRACT_PRODUCT T
                    GROUP BY T.POLICY_CODE) TFEE
          ON T.POLICY_CODE = TFEE.POLICY_CODE
        LEFT JOIN (SELECT TCE.PAY_DUE_DATE,
                          T.POLICY_ID TCEID,
                          TCE.POLICY_PERIOD,
                          TCP.PREM_FREQ,
                          T.INITIAL_PREM_DATE,
                          TCP.CHARGE_PERIOD
                     FROM DEV_PAS.T_CONTRACT_BUSI_PROD T
                     LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
                       ON TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                     LEFT JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
                       ON TCE.ITEM_ID = TCP.ITEM_ID
                    WHERE T.MASTER_BUSI_ITEM_ID IS NULL) TCED
          ON TCED.TCEID = T.POLICY_ID
        LEFT JOIN DEV_PAS.T_PAYER_ACCOUNT TPA
          ON TPA.POLICY_ID = T.POLICY_ID
        LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
          ON TCBP.POLICY_ID = T.POLICY_ID
        LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
          ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
       WHERE 1 = 1
         AND TCA.IS_CURRENT_AGENT = '1'
         AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
                    AND TCA.INSERT_TIME >= TRUNC(SYSDATE)
           AND TCA.INSERT_TIME < =SYSDATE             
			   AND MOD(T.POLICY_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}]]>
			<include refid="findOrphanPolicyWhereCondition" />
		</select>
		
		<select id="queryOrphanRisk" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
			            SELECT DISTINCT 
               T.POLICY_CODE,
               T.BUSI_PRD_ID,
               T.BUSI_ITEM_ID,
               T.OLD_POL_NO,
               T.BUSI_PROD_CODE,
               T.INITIAL_PREM_DATE,
               T.PAIDUP_DATE,
               TCE.PAY_DUE_DATE,
               TCP.PREM_FREQ,
               (SELECT TCM.CHARGE_NAME FROM APP___PAS__DBUSER.T_CHARGE_MODE TCM WHERE TCP.PREM_FREQ = TCM.CHARGE_TYPE) CHARGE_NAME,
               TCP.CHARGE_YEAR,
               TCP.COVERAGE_YEAR,
               (SELECT SUM(TFEE.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TFEE WHERE TFEE.BUSI_ITEM_ID=T.BUSI_ITEM_ID) PREM,
               (SELECT SUM(nvl(TFEE.STD_PREM_AF, 0) + nvl(TFEE.ADDITIONAL_PREM_AF, 0) + nvl(TFEE.APPEND_PREM_AF, 0)) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TFEE WHERE TFEE.BUSI_ITEM_ID=T.BUSI_ITEM_ID) ALL_PREM,
               (SELECT SUM(TFEE.TOTAL_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TFEE WHERE TFEE.BUSI_ITEM_ID=T.BUSI_ITEM_ID) TOTAL_PREM_AF,
               (SELECT SUM(TFEE.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TFEE WHERE TFEE.BUSI_ITEM_ID=T.BUSI_ITEM_ID) AMOUNT,
               T.END_CAUSE,
               T.LIABILITY_STATE,
               T.EXPIRY_DATE,
               TBP.PRODUCT_NAME_STD,
               TBP.PRODUCT_CATEGORY2,
               TBP.PRIMARY_SALES_CHANNEL,
               TBP.COVER_PERIOD_TYPE,
               T.MASTER_BUSI_ITEM_ID,
               TCE.POLICY_PERIOD,
               TCP.PRODUCT_CODE,
               TCE.LIST_ID,
               TCP.CHARGE_PERIOD,
               (SELECT TPA.PAY_NEXT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA WHERE TPA.POLICY_ID=T.POLICY_ID) PAY_NEXT,
               (SELECT SUM(TRAF.RISK_AMOUNT) FROM APP___PAS__DBUSER.T_RISK_AMOUNT TRAF WHERE TRAF.POLICY_CODE=T.POLICY_CODE AND TRAF.BUSI_ITEM_ID=T.BUSI_ITEM_ID) RISK_AMOUNT,
               TBP.PRODUCT_CATEGORY1,
               (SELECT TPBV.VERSION_NUMBER FROM APP___PDS__DBUSER.T_BUSI_PRD_BUILD_VERSION TPBV WHERE TPBV.BUSINESS_PRD_ID=T.BUSI_PRD_ID) VERSION_NUMBER,
               (SELECT MAX(TALI.SALE_START) FROM APP___PDS__DBUSER.T_MMS_AUTH_MAIN_INFO TAMI,APP___PDS__DBUSER.T_MMS_AUTH_LIST_INFO TALI 
               WHERE TAMI.AUTH_MAIN_ID = TALI.AUTH_MINFO_ID AND TAMI.AUTHO_ID=T.BUSI_PRD_ID) SALE_START,
               (SELECT MAX(TALI.SALE_END) FROM APP___PDS__DBUSER.T_MMS_AUTH_MAIN_INFO TAMI,APP___PDS__DBUSER.T_MMS_AUTH_LIST_INFO TALI 
               WHERE TAMI.AUTH_MAIN_ID = TALI.AUTH_MINFO_ID AND TAMI.AUTHO_ID=T.BUSI_PRD_ID) SALE_END,
               TCD.ARAP_BELNR,
               TCP.LAPSE_CAUSE,
               TCP.LAPSE_DATE,
               TCP.VALIDATE_DATE
         from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T
         left join APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
         on T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID
         left join APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
         on TCP.BUSI_ITEM_ID=TCE.BUSI_ITEM_ID
         and TCP.ITEM_ID=TCE.ITEM_ID
         left join APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP
         on T.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
         left join APP___CAP__DBUSER.T_CASH_DETAIL TCD
          ON TCD.POLICY_CODE = T.POLICY_CODE
           AND TCD.BUSI_PROD_CODE = T.BUSI_PROD_CODE
         WHERE 1=1
			]]>
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
		</select>  
    
    <!-- 查询保单变更信息 -->
	<select id="PA_findProductLiabilityChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		         SELECT TPLC.BUSI_ITEM_ID,
		                TPLC.LIABILITY_STATUS,
		                TPLC.LIABILITY_CHANGED,
		                TPLC.LAPSE_CAUSE,
		                TPLC.END_CAUSE,
		                TPLC.CHANGE_DATE
		           FROM DEV_PAS.T_PRODUCT_LIABILITY_CHANGE TPLC
		          WHERE 1= 1 ]]>
			<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND tplc.policy_id = #{policy_id} ]]></if>
			<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND tplc.busi_item_id = #{busi_item_id} ]]></if>
		<![CDATA[ ORDER BY TPLC.CHANGE_DATE DESC ]]>
	</select>
	
	<!-- 保单号临时表 -->
	<update id="PA_createTablePolicyCode" parameterType="java.util.Map">
        <![CDATA[
        	CREATE TABLE T_POLICY_CODE_LIST (POLICY_CODE VARCHAR2(20))          
 		]]>
	</update>
	
	<insert id="PA_insertPolicyCode" parameterType="java.util.Map">
	    INSERT INTO T_POLICY_CODE_LIST (POLICY_CODE) VALUES (#{policy_code})
	</insert>
	
	    <!-- 查询保单变化 -->
	<select id="PA_findPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT DISTINCT B.POLICY_CODE
				  FROM DEV_PAS.T_POLICY_CHANGE A
				  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER B
				    ON A.POLICY_ID = B.POLICY_ID
				 WHERE TO_CHAR(A.INSERT_TIME, 'yyyy-MM-dd') = #{batchTime}]]>
			<if test="policy_code_list != null and policy_code_list.size()!=0"><![CDATA[ AND B.POLICY_CODE IN (SELECT POLICY_CODE FROM T_POLICY_CODE_LIST ]]></if>
	</select>
	<!--查询被保人信息  -->
	<select id="PA_findOrphanInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
						SELECT TIL.POLICY_ID PID,
			                    TIL.CUSTOMER_ID INSURED_CUSTOMER_ID,
			                    TC.CUSTOMER_NAME INSURED_CUSTOMER_NAME,
			                    TC.CUSTOMER_BIRTHDAY INSURED_CUSTOMER_BIRTHDAY,
			                    TC.CUSTOMER_CERT_TYPE INSURED_CUSTOMER_CERT_TYPE,
			                    TC.CUSTOMER_CERTI_CODE INSURED_CUSTOMER_CERT_CODE,
			                    (SELECT MAX(TBP.ADDRESS_ID)
			                       FROM DEV_PAS.T_ADDRESS TBP
			                      WHERE TBP.CUSTOMER_ID = TIL.CUSTOMER_ID) INSURED_ADDRESS_ID
			               FROM DEV_PAS.T_INSURED_LIST TIL
			               LEFT JOIN DEV_PAS.T_CUSTOMER TC
			                 ON TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			                 WHERE 1=1
			                 ]]>
			<if test="policy_code != null and policy_code !=''"><![CDATA[ AND TIL.POLICY_CODE =#{policy_code} ]]></if>
	</select>
	
		<select id="PA_queryOrphanPolicyCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				SELECT COUNT(1)
				  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T, 
				  APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,
				  APP___PAS__DBUSER.T_POLICY_CHANGE TPC
				 WHERE T.POLICY_ID = TPC.POLICY_ID
				 AND T.POLICY_ID = TCA.POLICY_ID
				 AND TCA.IS_CURRENT_AGENT = '1'
                 AND TPC.INSERT_TIME BETWEEN TRUNC(SYSDATE) AND SYSDATE
				  ]]>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND T.ORGAN_CODE LIKE '%'|| LTRIM(RTRIM(#{organ_code}, ' ') , ' ') ||'%'  ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND T.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" allocate_date_start != null "><![CDATA[ AND TCA.AGENT_START_DATE >= #{allocate_date_start} ]]></if>
        <if test=" allocate_date_end != null "><![CDATA[ AND TCA.AGENT_START_DATE <= #{allocate_date_end} ]]></if>
		<if test="list != null and list.size()!=0">
		<![CDATA[ AND T.POLICY_CODE IN ]]>
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
       </foreach>
		</if>
       <![CDATA[  AND TPC.SERVICE_CODE IN ('RE',
                            'RS',
                            'PU',
                            'NS',
                            'PBA',
                            'EA',
                            'DA',
                            'PAPL',
                            'RA',
                            'PR',
                            'DT',
                            'FK',
                            'IO',
                            'CA',
                            'AM',
                            'CLMEND',
                            'PPL',
                            'PME',
                            'YS',
                            'EN',
                            'PREF',
                            'MR',
                            'PLPE',
                            'PNL',
                            'PC',
                            'CM',
                            'IT',
                            'MD',
                            'LR',
                            'PPD',
                            'PILPI',
                            'MC',
                            'PA',
                            'GB',
                            'AE',
                            'XD',
                            'PIM',
                            'PLE',
                            'PICD',
                            'PRE',
                            'PRL',
                            'CT',
                            'XT',
                            'AP',
                            'PT',
                            'FM',
                            'CB')]]>
		</select>
	
		<select id="PA_queryOrphanPolicyByModnumAndStart" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT ROWNUM RN,
             T.POLICY_ID,
             T.POLICY_CODE,
             T.ORGAN_CODE,
             T.SALE_AGENT_CODE,
             T.APPLY_CODE,
             T.INITIAL_VALIDATE_DATE,
             T.LIABILITY_STATE,
             T.INITIAL_PREM_DATE,
             T.VALIDATE_DATE,
             T.CHANNEL_TYPE,
             TCA.AGENT_ORGAN_CODE,
             TCA.AGENT_CODE,
             TCU.CUSTOMER_ID,
             TCU.OLD_CUSTOMER_ID,
             TCU.CUSTOMER_NAME,
             TCU.CUSTOMER_CERT_TYPE,
             TCU.CUSTOMER_CERTI_CODE,
             TCU.CUSTOMER_BIRTHDAY,
             TCU.MARRIAGE_DATE,
             TCU.MARRIAGE_STATUS,
             TCU.MOBILE_TEL,
             TFEE.PREM,
             TFEE.AMOUNT,
             TPA.NEXT_ACCOUNT,
             TFEE.TOTAL_PREM_AF,
             TCED.PAY_DUE_DATE,
             TCED.POLICY_PERIOD,
             TCED.PREM_FREQ,
             TCED.INITIAL_PREM_DATE,
             TCED.CHARGE_PERIOD,
             TPA.PAY_LOCATION,
             TPA.PAY_NEXT,
             C.SALES_ORGAN_CODE,
             TBP1.ADDRESS_ID
        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T
        JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT TCA
          ON T.POLICY_ID = TCA.POLICY_ID
        JOIN APP___PAS__DBUSER.T_AGENT C
          ON TCA.AGENT_CODE = C.AGENT_CODE
        LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH ON TPH.POLICY_CODE = T.POLICY_CODE
        LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TCU ON TCU.CUSTOMER_ID = TPH.CUSTOMER_ID
        LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TBP1 ON TBP1.ADDRESS_ID = TPH.ADDRESS_ID
        LEFT JOIN (SELECT SUM(NVL(T.STD_PREM_AF, 0)) PREM,
                          SUM(NVL(T.AMOUNT, 0)) AMOUNT,
                          SUM(NVL(T.TOTAL_PREM_AF, 0)) TOTAL_PREM_AF,
                          T.POLICY_CODE
                     FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT T
                    GROUP BY T.POLICY_CODE) TFEE
          ON T.POLICY_CODE = TFEE.POLICY_CODE
        LEFT JOIN (SELECT TCE.PAY_DUE_DATE,
                          T.POLICY_ID TCEID,
                          TCE.POLICY_PERIOD,
                          TCP.PREM_FREQ,
                          T.INITIAL_PREM_DATE,
                          TCP.CHARGE_PERIOD
                     FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T
                     LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
                       ON TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                     LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
                       ON TCE.ITEM_ID = TCP.ITEM_ID
                    WHERE T.MASTER_BUSI_ITEM_ID IS NULL) TCED
          ON TCED.TCEID = T.POLICY_ID
        LEFT JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA
          ON TPA.POLICY_ID = T.POLICY_ID
        LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
          ON TCBP.POLICY_ID = T.POLICY_ID
        LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
          ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
       WHERE 1 = 1
         AND TCA.IS_CURRENT_AGENT = '1'
         AND TCBP.MASTER_BUSI_ITEM_ID IS NULL  ]]>
         <if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND TCBP.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
         <if test=" validate_date_start != null "><![CDATA[ AND T.VALIDATE_DATE >= #{validate_date_start} ]]></if>
         <if test=" validate_date_end != null "><![CDATA[ AND T.VALIDATE_DATE <= #{validate_date_end} ]]></if>
         <if test=" allocate_date_start != null "><![CDATA[ AND TCA.AGENT_START_DATE >= #{allocate_date_start} ]]></if>
         <if test=" allocate_date_end != null "><![CDATA[ AND TCA.AGENT_START_DATE <= #{allocate_date_end} ]]></if>
         <if test=" issue_date_start != null "><![CDATA[ AND T.ISSUE_DATE >= #{issue_date_start} ]]></if>
		 <if test=" issue_date_end != null "><![CDATA[ AND T.ISSUE_DATE <= #{issue_date_end} ]]></if>
		 <if test=" expiry_date_start != null "><![CDATA[ AND T.EXPIRY_DATE >= #{expiry_date_start} ]]></if>
		 <if test=" expiry_date_end != null  "><![CDATA[ AND T.EXPIRY_DATE <= #{expiry_date_end} ]]></if>
		 <if test=" policy_period != null  "><![CDATA[ AND TCED.POLICY_PERIOD <= #{policy_period} ]]></if>
        <![CDATA[ AND MOD(T.POLICY_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}]]>
         <include refid="findOrphanPolicyWhereCondition" />  
       <![CDATA[  AND  EXISTS (SELECT 'x'
                FROM APP___PAS__DBUSER.T_POLICY_CHANGE TPC
               WHERE TPC.INSERT_TIME BETWEEN TRUNC(SYSDATE) AND SYSDATE
                 AND T.POLICY_ID = TPC.POLICY_ID
                 and TPC.Service_Code in (
                'RE',
              'RS',
              'PU',
              'NS',
              'PBA',
              'EA',
              'DA',
              'PAPL',
              'RA',
              'PR',
              'DT',
              'FK',
              'IO',
              'CA',
              'AM',
              'CLMEND',
              'PPL',
              'PME',
              'YS',
              'EN',
              'PREF',
              'MR',
              'PLPE',
              'PNL',
              'PC',
              'CM',
              'IT',
              'MD',
              'LR',
              'PPD',
              'PILPI',
              'MC',
              'PA',
              'GB',
              'AE',
              'XD',
              'PIM',
              'PLE',
              'PICD',
              'PRE',
              'PRL',
              'CT',
              'XT',
              'AP',
              'PT',
              'FM',
              'CB')) ]]>
		</select>
	
	<!--查询保单是否理赔或者保全挂起  -->
	<select id="PA_queryLockBySubId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
						SELECT A.Policy_Code
					FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                          LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                            ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                         WHERE B.SUB_ID IN ('068', '067')
               	AND B.LOCK_SERVICE_TYPE = 1
			                 ]]>
			<if test="policy_code != null and policy_code !=''"><![CDATA[ AND A.POLICY_CODE =#{policy_code} ]]></if>
	</select>
		<!-- 添加操作 -->
	<insert id="PA_addOrphanParameter" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT DEV_PAS.S_ORPHAN_PARAMETER.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_ORPHAN_PARAMETER(LIST_ID,
				SERIAL_NO, MANAGE_COM, AGENT_GROUP, CHANNEL_TYPE, AGENT_CODE, VALIDATE_DATE_START, VALIDATE_DATE_END, 
				DISTRIBUTE_FLAG, HOLDER_CUSTOMER_NAME, HOLDER_CUSTOMER_ID, PREM_MAX, PREM_MIN, PREM_STATUS, 
				POLICY_PERIOD, SERVICE_BANK_BRANCH, POLICY_CODE, BUSI_PROD_CODE, ISSUE_DATE_START, ISSUE_DATE_END, EXPIRY_DATE_START, 
				EXPIRY_DATE_END, ALLOCATE_FLAG, STATUS, NOTICE_STATUS,FAIL_COUNT, INSERT_BY, INSERT_TIME, 
				INSERT_TIMESTAMP, UPDATE_BY, UPDATE_TIME, UPDATE_TIMESTAMP) 
			VALUES (#{list_id, jdbcType=NUMERIC} , 
			    #{serial_no,jdbcType=VARCHAR},
				#{manage_com, jdbcType=VARCHAR}, #{agent_group, jdbcType=VARCHAR} , #{channel_type, jdbcType=VARCHAR} , #{agent_code, jdbcType=VARCHAR} , #{validate_date_start, jdbcType=DATE} 
				, #{validate_date_end, jdbcType=DATE} , #{distribute_flag, jdbcType=VARCHAR} , #{holder_customer_name, jdbcType=VARCHAR} 
				, #{holder_customer_id, jdbcType=VARCHAR} , #{prem_max, jdbcType=VARCHAR} , #{prem_min, jdbcType=VARCHAR} 
				, #{prem_status, jdbcType=VARCHAR} , #{policy_period, jdbcType=VARCHAR} , #{service_bank_branch, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} ,
				 #{issue_date_start, jdbcType=DATE} , #{issue_date_end, jdbcType=DATE} , #{expiry_date_start, jdbcType=DATE} , #{expiry_date_end, jdbcType=DATE} , #{allocate_flag, jdbcType=VARCHAR} , #{status, jdbcType=VARCHAR} , 
				 #{notice_status, jdbcType=VARCHAR} ,#{fail_count, jdbcType=NUMERIC} ,#{insert_by, jdbcType=NUMERIC} ,SYSDATE,CURRENT_TIMESTAMP,#{update_by, jdbcType=NUMERIC} ,SYSDATE,CURRENT_TIMESTAMP)
		 ]]>
	</insert>
	<!-- 修改操作 -->
	<update id="PA_updateOrphanParameter" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_ORPHAN_PARAMETER ]]>
		<set>
			<trim suffixOverrides=",">
			STATUS = #{status, jdbcType=VARCHAR} ,
		    FAIL_COUNT = #{fail_count, jdbcType=NUMERIC} ,
			NOTICE_STATUS = #{notice_status, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  SERIAL_NO = #{serial_no} ]]>
	</update>
	
	 	<!-- 查询操作 -->
	<select id="PA_queryOrphanParameter" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID, A.SERIAL_NO, A.MANAGE_COM, A.AGENT_GROUP, A.CHANNEL_TYPE, A.AGENT_CODE, A.VALIDATE_DATE_START, 
			A.VALIDATE_DATE_END, A.DISTRIBUTE_FLAG, A.HOLDER_CUSTOMER_NAME, A.HOLDER_CUSTOMER_ID, 
			A.PREM_MAX, A.PREM_MIN, A.PREM_STATUS, A.POLICY_PERIOD, A.SERVICE_BANK_BRANCH, A.POLICY_CODE, A.BUSI_PROD_CODE, 
			A.ISSUE_DATE_START, A.ISSUE_DATE_END, A.EXPIRY_DATE_START, A.EXPIRY_DATE_END, A.ALLOCATE_FLAG, A.STATUS, 
			A.FAIL_COUNT,A.NOTICE_STATUS
			FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER A WHERE 1=1]]>
		    <include refid="PA_orphanParameterWhereCondition" />
		<![CDATA[ORDER BY A.INSERT_TIME DESC]]>
	</select>
	 	<!-- 查询操作 -->
	<select id="PA_queryAllOrphanParameter" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.LIST_ID, A.SERIAL_NO, A.MANAGE_COM, A.AGENT_GROUP, A.CHANNEL_TYPE, A.AGENT_CODE, A.VALIDATE_DATE_START, 
			A.VALIDATE_DATE_END, A.DISTRIBUTE_FLAG, A.HOLDER_CUSTOMER_NAME, A.HOLDER_CUSTOMER_ID, 
			A.PREM_MAX, A.PREM_MIN, A.PREM_STATUS, A.POLICY_PERIOD, A.SERVICE_BANK_BRANCH, A.POLICY_CODE, A.BUSI_PROD_CODE, 
			A.ISSUE_DATE_START, A.ISSUE_DATE_END, A.EXPIRY_DATE_START, A.EXPIRY_DATE_END, A.ALLOCATE_FLAG, A.STATUS, 
			A.FAIL_COUNT,A.NOTICE_STATUS
			FROM DEV_PAS.T_ORPHAN_PARAMETER A WHERE 1=1 AND A.FAIL_COUNT >0 
			 AND MOD(A.LIST_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
			]]>
			<include refid="PA_orphanParameterWhereCondition" />
		<![CDATA[ORDER BY A.INSERT_TIME DESC]]>
	</select>

	<!-- 查询操作 -->
	<select id="PA_findPolicyCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PARA_ID,
       A.PARA_NAME,
       A.PARA_DESC,
       A.PARA_VALUE,
       A.PARA_VALUE_NAME,
       A.DATA_TYPE,
       A.SYSTEM_ADMIN,
       A.ORGAN_RELA,
       A.DEPT_RELA,
       A.TIME_RELA,
       A.SINGLE_PARA_VALUE,
       A.MODULE_ID,
       A.ORGAN_ID,
       A.DEPT_ID,
       A.START_DATE,
       A.END_DATE,
       A.SYSTEM_ID,
       A.SCOPE_CODE FROM APP___PAS__DBUSER.T_UDMP_PARA_DEF A WHERE 1 = 1 AND ROWNUM = 1]]>
		<if test=" para_name != null and para_name != ''  "><![CDATA[ AND A.PARA_NAME = #{para_name} ]]></if>		    
	</select>

	<!-- 查询操作 -->
	<select id="PA_findUdmpParaDefByParaName" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PARA_ID,
       A.PARA_NAME,
       A.PARA_DESC,
       A.PARA_VALUE,
       A.PARA_VALUE_NAME,
       A.DATA_TYPE,
       A.SYSTEM_ADMIN,
       A.ORGAN_RELA,
       A.DEPT_RELA,
       A.TIME_RELA,
       A.SINGLE_PARA_VALUE,
       A.MODULE_ID,
       A.ORGAN_ID,
       A.DEPT_ID,
       A.START_DATE,
       A.END_DATE,
       A.SYSTEM_ID,
       A.SCOPE_CODE FROM APP___PAS__DBUSER.T_UDMP_PARA_DEF A WHERE 1 = 1 ]]>
		<if test="list != null and list.size()!=0">
		<![CDATA[AND A.PARA_NAME IN ]]>
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
       </foreach>
		</if>	    
	</select>


</mapper>
