<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="policyAcknowledgement">

	<sql id="PA_policyAcknowledgementWhereCondition">
		<if test=" operator_id  != null "><![CDATA[ AND A.OPERATOR_ID = #{operator_id} ]]></if>
		<if test=" branch_receive_date  != null  and  branch_receive_date  != ''  "><![CDATA[ AND A.BRANCH_RECEIVE_DATE = #{branch_receive_date} ]]></if>
		<if test=" return_reason != null and return_reason != ''  "><![CDATA[ AND A.RETURN_REASON = #{return_reason} ]]></if>
		<if test=" acknowledge_date  != null  and  acknowledge_date  != ''  "><![CDATA[ AND A.ACKNOWLEDGE_DATE = #{acknowledge_date} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" dc_collect_date  != null  and  dc_collect_date  != ''  "><![CDATA[ AND A.DC_COLLECT_DATE = #{dc_collect_date} ]]></if>
		<if test=" dispatch_date  != null  and  dispatch_date  != ''  "><![CDATA[ AND A.DISPATCH_DATE = #{dispatch_date} ]]></if>
		<if test=" return_desc != null and return_desc != ''  "><![CDATA[ AND A.RETURN_DESC = #{return_desc} ]]></if>
		<if test=" reminder_date  != null  and  reminder_date  != ''  "><![CDATA[ AND A.REMINDER_DATE = #{reminder_date} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyAcknowledgementByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyAcknowledgement"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT(
				OPERATOR_ID, INSERT_TIME, BRANCH_RECEIVE_DATE, RETURN_REASON, UPDATE_TIME, ACKNOWLEDGE_DATE, MEDIA_TYPE, 
				INSERT_TIMESTAMP, UPDATE_BY, DC_COLLECT_DATE, DISPATCH_DATE, UPDATE_TIMESTAMP, RETURN_DESC, REMINDER_DATE, 
				INSERT_BY, POLICY_ID ,E_BRANCH_RECEIVE_DATE,P_ACKNOWLEDGE_DATE,P_BRANCH_RECEIVE_DATE) 
			VALUES (
				#{operator_id, jdbcType=NUMERIC}, SYSDATE , #{branch_receive_date, jdbcType=DATE} , #{return_reason, jdbcType=VARCHAR} , SYSDATE , #{acknowledge_date, jdbcType=DATE} , #{media_type, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{dc_collect_date, jdbcType=DATE} , #{dispatch_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{return_desc, jdbcType=VARCHAR} , #{reminder_date, jdbcType=DATE} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{e_branch_receive_date, jdbcType=DATE}, #{p_acknowledge_date, jdbcType=DATE}, #{p_branch_receive_date, jdbcType=DATE}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyAcknowledgement" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT WHERE POLICY_ID=#{policy_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyAcknowledgement" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT ]]>
		<set>
		<trim suffixOverrides=",">
		    OPERATOR_ID = #{operator_id, jdbcType=NUMERIC} ,
		    BRANCH_RECEIVE_DATE = #{branch_receive_date, jdbcType=TIMESTAMP} ,
			RETURN_REASON = #{return_reason, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ACKNOWLEDGE_DATE = #{acknowledge_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    DC_COLLECT_DATE = #{dc_collect_date, jdbcType=DATE} ,
		    DISPATCH_DATE = #{dispatch_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			RETURN_DESC = #{return_desc, jdbcType=VARCHAR} ,
		    REMINDER_DATE = #{reminder_date, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SIGN_SOURCE = #{sign_source, jdbcType=VARCHAR} ,
		    E_ACKNOWLEDGE_DATE = #{e_acknowledge_date, jdbcType=DATE} ,
		    E_BRANCH_RECEIVE_DATE = #{e_branch_receive_date, jdbcType=DATE} ,
		    P_ACKNOWLEDGE_DATE = #{p_acknowledge_date, jdbcType=DATE} ,
		    P_BRANCH_RECEIVE_DATE = #{p_branch_receive_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyAcknowledgementByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE, A.E_BRANCH_RECEIVE_DATE,
			A.P_ACKNOWLEDGE_DATE,A.P_BRANCH_RECEIVE_DATE,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAcknowledgementByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyAcknowledgement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE,A.E_BRANCH_RECEIVE_DATE,A.P_ACKNOWLEDGE_DATE,
			A.P_BRANCH_RECEIVE_DATE, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyAcknowledgement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE,A.E_BRANCH_RECEIVE_DATE,A.P_ACKNOWLEDGE_DATE,
			A.P_BRANCH_RECEIVE_DATE, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <=  1000  ]]>
		 <include refid="PA_policyAcknowledgementWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyAcknowledgementTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyAcknowledgementForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATOR_ID, B.BRANCH_RECEIVE_DATE, B.RETURN_REASON, B.ACKNOWLEDGE_DATE, B.MEDIA_TYPE, 
			B.DC_COLLECT_DATE, B.DISPATCH_DATE, B.RETURN_DESC, B.REMINDER_DATE,B.E_BRANCH_RECEIVE_DATE,B.P_ACKNOWLEDGE_DATE,B.P_BRANCH_RECEIVE_DATE,  
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE,A.E_BRANCH_RECEIVE_DATE,A.P_ACKNOWLEDGE_DATE,A.P_BRANCH_RECEIVE_DATE, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询单条 -->
		<select id="PA_findPolicyAcknowledgement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.POLICY_ID,
                     A.DISPATCH_DATE,
                     A.BRANCH_RECEIVE_DATE,
                     A.DC_COLLECT_DATE,
                     A.RETURN_REASON,
                     A.RETURN_DESC,
                     A.ACKNOWLEDGE_DATE,
                     (select t.MEDIA_TYPE from APP___PAS__DBUSER.T_CONTRACT_MASTER t where t.policy_id = a.policy_id) as MEDIA_TYPE,
                     A.REMINDER_DATE,
                     A.OPERATOR_ID,
                     A.SIGN_SOURCE,
                     A.E_BRANCH_RECEIVE_DATE,
                     A.P_ACKNOWLEDGE_DATE ,
                     A.P_BRANCH_RECEIVE_DATE,
                     A.E_ACKNOWLEDGE_DATE FROM  APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <=  1000  ]]>
<!-- 		<include refid="" /> -->
		<include refid="PA_queryPolicyAcknowledgementByPolicyIdCondition" />
	</select>
	<!-- 保单密码校验  -->
	<select id="findPolicyAcknowledgementDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select reminder_date from APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT where 1=1 and policy_id=#{policy_id}
		]]>
	</select>
	
	<!-- 修改操作 -->
	<update id="PA_updatePolicyAcknowledgementByPolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT ]]>
		<set>
		<trim suffixOverrides=",">
		    OPERATOR_ID = #{operator_id, jdbcType=NUMERIC} ,
		    BRANCH_RECEIVE_DATE = #{branch_receive_date, jdbcType=DATE} ,
			RETURN_REASON = #{return_reason, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ACKNOWLEDGE_DATE = #{acknowledge_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    DC_COLLECT_DATE = #{dc_collect_date, jdbcType=DATE} ,
		    DISPATCH_DATE = #{dispatch_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			RETURN_DESC = #{return_desc, jdbcType=VARCHAR} ,
		    REMINDER_DATE = #{reminder_date, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    MEDIA_TYPE= #{media_type, jdbcType=NUMERIC},
		    E_BRANCH_RECEIVE_DATE = #{e_branch_receive_date, jdbcType=DATE} ,
		    P_ACKNOWLEDGE_DATE = #{p_acknowledge_date, jdbcType=DATE} ,
		    P_BRANCH_RECEIVE_DATE = #{p_branch_receive_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
</mapper>
