<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.udmp.impl.batch.demo.dao.IPolicyCashvalueDao">
<!--
	<sql id="policyCashvalueWhereCondition">
		<if test=" pay_liab_code != null and pay_liab_code != ''  "><![CDATA[ AND A.PAY_LIAB_CODE = #{pay_liab_code} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" cal_date  != null  and  cal_date  != ''  "><![CDATA[ AND A.CAL_DATE = #{cal_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" cal_reason != null and cal_reason != ''  "><![CDATA[ AND A.CAL_REASON = #{cal_reason} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" user_flag != null and user_flag != ''  "><![CDATA[ AND A.USER_FLAG = #{user_flag} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryPolicyCashvalueByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPolicyCashvalue"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_CASHVALUE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_CASHVALUE(
				PAY_LIAB_CODE, INSERT_TIME, PRODUCT_CODE, CAL_DATE, UPDATE_TIME, ITEM_ID, CASE_NO, 
				ACCEPT_CODE, BUSI_PROD_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, CAL_REASON, LIST_ID, 
				CASH_VALUE, UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, POLICY_ID, USER_FLAG ) 
			VALUES (
				#{pay_liab_code, jdbcType=VARCHAR}, SYSDATE , #{product_code, jdbcType=VARCHAR} , #{cal_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{case_no, jdbcType=VARCHAR} 
				, #{accept_code, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{cal_reason, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, #{cash_value, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{user_flag, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePolicyCashvalue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_CASHVALUE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePolicyCashvalue" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CASHVALUE ]]>
		<set>
		<trim suffixOverrides=",">
			PAY_LIAB_CODE = #{pay_liab_code, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    CAL_DATE = #{cal_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			CAL_REASON = #{cal_reason, jdbcType=VARCHAR} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			USER_FLAG = #{user_flag, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPolicyCashvalueByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_LIAB_CODE, A.PRODUCT_CODE, A.CAL_DATE, A.ITEM_ID, A.CASE_NO, 
			A.ACCEPT_CODE, A.BUSI_PROD_CODE, A.POLICY_CODE, A.CAL_REASON, A.LIST_ID, 
			A.CASH_VALUE, A.BUSI_ITEM_ID, A.POLICY_ID, A.USER_FLAG FROM APP___PAS__DBUSER.T_POLICY_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="queryPolicyCashvalueByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapPolicyCashvalue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_LIAB_CODE, A.PRODUCT_CODE, A.CAL_DATE, A.ITEM_ID, A.CASE_NO, 
			A.ACCEPT_CODE, A.BUSI_PROD_CODE, A.POLICY_CODE, A.CAL_REASON, A.LIST_ID, 
			A.CASH_VALUE, A.BUSI_ITEM_ID, A.POLICY_ID, A.USER_FLAG FROM APP___PAS__DBUSER.T_POLICY_CASHVALUE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPolicyCashvalue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_LIAB_CODE, A.PRODUCT_CODE, A.CAL_DATE, A.ITEM_ID, A.CASE_NO, 
			A.ACCEPT_CODE, A.BUSI_PROD_CODE, A.POLICY_CODE, A.CAL_REASON, A.LIST_ID, 
			A.CASH_VALUE, A.BUSI_ITEM_ID, A.POLICY_ID, A.USER_FLAG FROM APP___PAS__DBUSER.T_POLICY_CASHVALUE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findPolicyCashvalueTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_CASHVALUE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryPolicyCashvalueForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PAY_LIAB_CODE, B.PRODUCT_CODE, B.CAL_DATE, B.ITEM_ID, B.CASE_NO, 
			B.ACCEPT_CODE, B.BUSI_PROD_CODE, B.POLICY_CODE, B.CAL_REASON, B.LIST_ID, 
			B.CASH_VALUE, B.BUSI_ITEM_ID, B.POLICY_ID, B.USER_FLAG FROM (
					SELECT ROWNUM RN, A.PAY_LIAB_CODE, A.PRODUCT_CODE, A.CAL_DATE, A.ITEM_ID, A.CASE_NO, 
			A.ACCEPT_CODE, A.BUSI_PROD_CODE, A.POLICY_CODE, A.CAL_REASON, A.LIST_ID, 
			A.CASH_VALUE, A.BUSI_ITEM_ID, A.POLICY_ID, A.USER_FLAG FROM APP___PAS__DBUSER.T_POLICY_CASHVALUE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
