<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="productLife">

	<sql id="PA_productLifeWhereCondition">
		<if test=" option_type != null and option_type != ''  "><![CDATA[ AND A.OPTION_TYPE = #{option_type} ]]></if>
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" occupation_addfee_unit  != null "><![CDATA[ AND A.OCCUPATION_ADDFEE_UNIT = #{occupation_addfee_unit} ]]></if>
		<if test=" product_name != null and product_name != ''  "><![CDATA[ AND A.PRODUCT_NAME = #{product_name} ]]></if>
		<if test=" counter_way != null and counter_way != ''  "><![CDATA[ AND A.COUNTER_WAY = #{counter_way} ]]></if>
		<if test=" premium_unit  != null "><![CDATA[ AND A.PREMIUM_UNIT = #{premium_unit} ]]></if>
		<if test=" health_addfee_unit  != null "><![CDATA[ AND A.HEALTH_ADDFEE_UNIT = #{health_addfee_unit} ]]></if>
		<if test=" hobby_addfee_unit  != null "><![CDATA[ AND A.HOBBY_ADDFEE_UNIT = #{hobby_addfee_unit} ]]></if>
		<if test=" internal_id != null and internal_id != ''  "><![CDATA[ AND A.INTERNAL_ID = #{internal_id} ]]></if>
		<if test=" cashvalue_unit  != null "><![CDATA[ AND A.CASHVALUE_UNIT = #{cashvalue_unit} ]]></if>
		<if test=" sa_unit  != null "><![CDATA[ AND A.SA_UNIT = #{sa_unit} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryProductLifeByProductIdCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
	</sql>	
	<sql id="PA_queryProductLifeByInternalIdCondition">
		<if test=" internal_id != null and internal_id != '' "><![CDATA[ AND A.INTERNAL_ID = #{internal_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addProductLife"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PRODUCT_LIFE(
				OPTION_TYPE, BUSINESS_PRD_ID, PRODUCT_ID, OCCUPATION_ADDFEE_UNIT, PRODUCT_NAME, COUNTER_WAY, PREMIUM_UNIT, 
				HEALTH_ADDFEE_UNIT, HOBBY_ADDFEE_UNIT, INTERNAL_ID, CASHVALUE_UNIT, SA_UNIT ) 
			VALUES (
				#{option_type, jdbcType=VARCHAR}, #{business_prod_id, jdbcType=NUMERIC} , #{product_id, jdbcType=NUMERIC} , #{occupation_addfee_unit, jdbcType=NUMERIC} , #{product_name, jdbcType=VARCHAR} , #{counter_way, jdbcType=VARCHAR} , #{premium_unit, jdbcType=NUMERIC} 
				, #{health_addfee_unit, jdbcType=NUMERIC} , #{hobby_addfee_unit, jdbcType=NUMERIC} , #{internal_id, jdbcType=VARCHAR} , #{cashvalue_unit, jdbcType=NUMERIC} , #{sa_unit, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteProductLife" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PRODUCT_LIFE WHERE 1 = 1 ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateProductLife" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PRODUCT_LIFE ]]>
		<set>
		<trim suffixOverrides=",">
			OPTION_TYPE = #{option_type, jdbcType=VARCHAR} ,
		    BUSINESS_PRD_ID = #{business_prod_id, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    OCCUPATION_ADDFEE_UNIT = #{occupation_addfee_unit, jdbcType=NUMERIC} ,
			PRODUCT_NAME = #{product_name, jdbcType=VARCHAR} ,
			COUNTER_WAY = #{counter_way, jdbcType=VARCHAR} ,
		    PREMIUM_UNIT = #{premium_unit, jdbcType=NUMERIC} ,
		    HEALTH_ADDFEE_UNIT = #{health_addfee_unit, jdbcType=NUMERIC} ,
		    HOBBY_ADDFEE_UNIT = #{hobby_addfee_unit, jdbcType=NUMERIC} ,
			INTERNAL_ID = #{internal_id, jdbcType=VARCHAR} ,
		    CASHVALUE_UNIT = #{cashvalue_unit, jdbcType=NUMERIC} ,
		    SA_UNIT = #{sa_unit, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findProductLifeByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM APP___PAS__DBUSER.T_PRODUCT_LIFE A WHERE 1 = 1  ]]>
		<include refid="PA_queryProductLifeByProductIdCondition" />
	</select>
	
	<select id="PA_findProductLifeByInternalId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM APP___PAS__DBUSER.T_PRODUCT_LIFE A WHERE 1 = 1  ]]>
		<include refid="PA_queryProductLifeByInternalIdCondition" />
	</select>
	
	<select id="PA_findProductLife" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM APP___PAS__DBUSER.T_PRODUCT_LIFE A WHERE 1 = 1  ]]>
		<include refid="PA_productLifeWhereCondition" />
		<if test=" busi_prod_code  != null  and  busi_prod_code  != ''  ">
        <![CDATA[ AND EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TA
			         WHERE TA.BUSINESS_PRD_ID = A.BUSINESS_PRD_ID
			           AND TA.PRODUCT_CODE_SYS = #{busi_prod_code})
			           and ROWNUM = 1
			            ]]>
       </if>
		
	</select>

<!-- 按map查询操作 -->
	<select id="PA_findAllMapProductLife" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM APP___PAS__DBUSER.T_PRODUCT_LIFE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllProductLife" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM APP___PAS__DBUSER.T_PRODUCT_LIFE A WHERE ROWNUM <=  1000  ]]>
		 <include refid="PA_productLifeWhereCondition" /> 
		 <![CDATA[ ORDER BY A.INTERNAL_ID]]>
	</select>
<!-- 查询所有责任组操作 -->
	<select id="PA_findAllContractProductLR" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select pf.internal_id,pf.product_id,pf.product_name,us.save_table,us.save_column,pu.label_name,pu.bo_parameter,pf.business_prd_id,pu.unit_name  from APP___PAS__DBUSER.t_product_life pf,APP___PAS__DBUSER.T_PAGECFG_unit_save us,
	APP___PAS__DBUSER.t_pagecfg_unit pu 
  	where 1=1
 	 and pf.product_id = us.product_id
 	 and us.unit_code = pu.unit_code 
	and pf.internal_id = #{internal_id}
	and us.save_table = #{save_table} ]]>	
	<![CDATA[ ORDER BY pu.Bo_Parameter]]>
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findProductLifeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PRODUCT_LIFE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryProductLifeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPTION_TYPE, B.BUSINESS_PRD_ID, B.PRODUCT_ID, B.OCCUPATION_ADDFEE_UNIT, B.PRODUCT_NAME, B.COUNTER_WAY, B.PREMIUM_UNIT, 
			B.HEALTH_ADDFEE_UNIT, B.HOBBY_ADDFEE_UNIT, B.INTERNAL_ID, B.CASHVALUE_UNIT, B.SA_UNIT FROM (
					SELECT ROWNUM RN, A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM APP___PAS__DBUSER.T_PRODUCT_LIFE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_getBusiProduCodeByProductNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select product_code_sys as busi_prod_code  from APP___PAS__DBUSER.t_product_life t left join  APP___PAS__DBUSER.t_business_product tb 
				  on t.business_prd_id =tb.business_prd_id where t.internal_id = #{internal_id} ]]>
	</select>
	
</mapper>
