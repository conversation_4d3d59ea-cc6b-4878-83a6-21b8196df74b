<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ILogScopeDao">

	<sql id="PA_logScopeWhereCondition">
		<if test=" table_name != null and table_name != ''  "><![CDATA[ AND A.TABLE_NAME = #{table_name} ]]></if>
		<if test=" table_type != null and table_type != ''  "><![CDATA[ AND A.TABLE_TYPE = #{table_type} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryLogScopeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addLogScope"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_LOG_SCOPE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_LOG_SCOPE(
				TABLE_NAME, TABLE_TYPE, LIST_ID, STATUS, SERVICE_CODE ) 
			VALUES (
				#{table_name, jdbcType=VARCHAR}, #{table_type, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{status, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteLogScope" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_LOG_SCOPE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateLogScope" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LOG_SCOPE ]]>
		<set>
		<trim suffixOverrides=",">
			TABLE_NAME = #{table_name, jdbcType=VARCHAR} ,
			TABLE_TYPE = #{table_type, jdbcType=VARCHAR} ,
		    STATUS = #{status, jdbcType=NUMERIC} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findLogScopeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TABLE_NAME, A.TABLE_TYPE, A.LIST_ID, A.STATUS, A.SERVICE_CODE FROM APP___PAS__DBUSER.T_LOG_SCOPE A WHERE 1 = 1  ]]>
		<include refid="PA_queryLogScopeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
<!-- 按索引查询操作 -->	
	<select id="PA_findLogScope" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TABLE_NAME, A.TABLE_TYPE, A.LIST_ID, A.STATUS, A.SERVICE_CODE FROM APP___PAS__DBUSER.T_LOG_SCOPE A WHERE 1 = 1  ]]>
		<include refid="PA_logScopeWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
<!-- 按map查询操作 -->
	<select id="PA_findAllMapLogScope" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TABLE_NAME, A.TABLE_TYPE, A.LIST_ID, A.STATUS, A.SERVICE_CODE FROM APP___PAS__DBUSER.T_LOG_SCOPE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllLogScope" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TABLE_NAME, A.TABLE_TYPE, A.LIST_ID, A.STATUS, A.SERVICE_CODE FROM APP___PAS__DBUSER.T_LOG_SCOPE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_logScopeWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findLogScopeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_LOG_SCOPE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryLogScopeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TABLE_NAME, B.TABLE_TYPE, B.LIST_ID, B.STATUS, B.SERVICE_CODE FROM (
					SELECT ROWNUM RN, A.TABLE_NAME, A.TABLE_TYPE, A.LIST_ID, A.STATUS, A.SERVICE_CODE FROM APP___PAS__DBUSER.T_LOG_SCOPE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
