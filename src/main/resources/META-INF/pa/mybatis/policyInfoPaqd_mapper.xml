<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyInfoPaqdDao">


   <!-- 查询保单信息 -->
   <select id="PA_findPolicyByPC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT a.policy_code, 
			           a.policy_id,
				       a.end_cause,
				       a.liability_state,
				       (select min(ts.hesitate_flag)
				          FROM dev_pas.t_surrender ts
				          INNER JOIN dev_pas.t_contract_busi_prod tcbp
				            ON ts.busi_item_id = tcbp.busi_item_id
				         WHERE tcbp.liability_state = 3
				           AND tcbp.end_cause = '03'
				           AND tcbp.policy_code = a.policy_code) hesitate_flag
				  FROM dev_pas.t_contract_master a
				 WHERE 1 = 1 ]]>
				<![CDATA[  AND a.policy_code in  ]]>
				<foreach collection ="policy_list" item="policy_code" index="index" open="(" close=")" separator=",">
				<![CDATA[#{policy_code}]]>
				</foreach>	
	</select>
	
	
	<!-- 查询账户信息 -->
	<select id="PA_findAccInfo" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT m.policy_id,
				       p.busi_prod_code,
				       ai.account_code,
				       ai.interest_capital
				  FROM dev_pas.t_contract_master m,
				       dev_pas.t_contract_busi_prod p,
				       (SELECT a.policy_id,
				                       a.busi_item_id,
				                       '' as account_code,
				                       a.interest_capital
				                  FROM dev_pas.t_policy_account a
				                 WHERE a.policy_id = #{policy_id}
				                UNION ALL
				                SELECT b.policy_id,
				                       b.busi_item_id,
				                       b.account_code,
				                       b.interest_capital
				                  FROM dev_pas.t_contract_invest b
				                 WHERE b.policy_id = #{policy_id}) ai
				 WHERE m.policy_id = p.policy_id
				   AND m.policy_code = p.policy_code
				   AND m.policy_id = ai.policy_id
				   AND p.policy_id = ai.policy_id
				   AND p.busi_item_id = ai.busi_item_id
	               AND m.policy_code = #{policy_code}]]>
	</select>
	
	<!-- 查询保单信息(无保单号) -->
	<select id="PA_findPolicyInfoms" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[ SELECT CC.POLICY_ID,
		       M.POLICY_CODE,
		       M.END_CAUSE,
		       CC.FINISH_TIME,
		       CASE
		         WHEN CC.SERVICE_CODE = 'CT' THEN
		          (SELECT MIN(TS.HESITATE_FLAG)
		             FROM DEV_PAS.T_SURRENDER TS
		            INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		               ON TS.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		            WHERE TCBP.LIABILITY_STATE = 3
		              AND TCBP.END_CAUSE = '03'
		              AND TCBP.POLICY_ID = CC.POLICY_ID)
		         ELSE
		          11
		       END AS HESITATE_FLAG
		  FROM (WITH TAB AS (SELECT B.POLICY_ID, B.FINISH_TIME, B.SERVICE_CODE
		                       FROM DEV_PAS.T_POLICY_CHANGE B
		                      WHERE 1 = 1
		                        AND B.SERVICE_CODE = 'AM'
		                        AND B.FINISH_TIME >= #{get_start_date}
		                        AND B.FINISH_TIME < #{get_end_date}
		                     UNION
		                     SELECT B.POLICY_ID, B.FINISH_TIME, B.SERVICE_CODE
		                       FROM DEV_PAS.T_POLICY_CHANGE B
		                      WHERE 1 = 1
		                        AND B.SERVICE_CODE = 'TI'
		                        AND B.FINISH_TIME >= #{get_start_date}
		                        AND B.FINISH_TIME < #{get_end_date}
		                     UNION
		                     SELECT B.POLICY_ID, B.FINISH_TIME, B.SERVICE_CODE
		                       FROM DEV_PAS.T_POLICY_CHANGE B
		                      WHERE 1 = 1
		                        AND B.SERVICE_CODE = 'CT'
		                        AND B.FINISH_TIME >= #{get_start_date}
		                        AND B.FINISH_TIME < #{get_end_date}
		                     UNION
		                     SELECT B.POLICY_ID, B.FINISH_TIME, B.SERVICE_CODE
		                       FROM DEV_PAS.T_POLICY_CHANGE B
		                      WHERE 1 = 1
		                        AND B.SERVICE_CODE = 'AG'
		                        AND B.FINISH_TIME >= #{get_start_date}
		                        AND B.FINISH_TIME < #{get_end_date}
		                     UNION
		                     SELECT B.POLICY_ID, B.FINISH_TIME, B.SERVICE_CODE
		                       FROM DEV_PAS.T_POLICY_CHANGE B
		                      WHERE 1 = 1
		                        AND B.SERVICE_CODE = 'CM'
		                        AND B.FINISH_TIME >= #{get_start_date}
		                        AND B.FINISH_TIME < #{get_end_date})
		         SELECT A.POLICY_ID,
		                A.SERVICE_CODE,
		                A.FINISH_TIME,
		                ROW_NUMBER() OVER(PARTITION BY A.POLICY_ID ORDER BY A.FINISH_TIME DESC) RN
		           FROM TAB A) CC
		           LEFT JOIN DEV_PAS.T_CONTRACT_MASTER M
		             ON CC.POLICY_ID = M.POLICY_ID
		          WHERE 1 = 1
		            AND CC.RN = 1]]>
		    <if test=" service_bank  != null and service_bank != '' "><![CDATA[AND M.SERVICE_BANK = #{service_bank}]]></if>
		    <if test=" bpc_list != null and bpc_list.size()>0">
		    <![CDATA[ AND EXISTS
				          (SELECT 1
				             FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
				            WHERE P.POLICY_ID = M.POLICY_ID
					          AND P.BUSI_PROD_CODE IN ]]>
			<foreach collection ="bpc_list" item="item" index="index" open="(" close=")" separator=",">
			                 <![CDATA[#{item}]]>
			</foreach>
			      <![CDATA[)]]>
			</if> 
		    <if test=" channel_list != null and channel_list.size()>0">
			<![CDATA[AND M.SUBMIT_CHANNEL IN ]]>
			<foreach collection ="channel_list" item="item_list" index="index" open="(" close=")" separator=",">
			<![CDATA[#{item_list}]]>
			</foreach>
			</if>

	</select>

</mapper>
