<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractLiabAmoutDao">

	<sql id="PA_contractLiabAmoutWhereCondition">
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND <PERSON>.POLICY_CODE = #{policy_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractLiabAmoutByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractLiabAmout"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_LIAB_AMOUT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT(
				INSERT_TIME, PRODUCT_CODE, UPDATE_TIME, LIBA_CODE, BUSI_PROD_CODE, APPLY_CODE, INSERT_TIMESTAMP, 
				LIBA_AMOUT, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				SYSDATE, #{product_code, jdbcType=VARCHAR} , SYSDATE , #{liba_code, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{liba_amout, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractLiabAmout" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractLiabAmout" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT ]]>
		<set>
		<trim suffixOverrides=",">
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			LIBA_CODE = #{liba_code, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    LIBA_AMOUT = #{liba_amout, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractLiabAmoutByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.LIBA_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LIBA_AMOUT, A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractLiabAmoutByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractLiabAmout" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.LIBA_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LIBA_AMOUT, A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractLiabAmout" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.LIBA_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LIBA_AMOUT, A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractLiabAmoutWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractLiabAmoutTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractLiabAmoutForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRODUCT_CODE, B.LIBA_CODE, B.BUSI_PROD_CODE, B.APPLY_CODE, 
			B.LIBA_AMOUT, B.POLICY_CODE, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.PRODUCT_CODE, A.LIBA_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LIBA_AMOUT, A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_CONTRACT_LIAB_AMOUT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
