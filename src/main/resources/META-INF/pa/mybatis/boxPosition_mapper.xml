<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBoxPositionDao">

	<sql id="boxPositionWhereCondition">
		<if test=" box_unit  != null "><![CDATA[ AND A.BOX_UNIT = #{box_unit} ]]></if>
		<if test=" transfer_max  != null "><![CDATA[ AND A.TRANSFER_MAX = #{transfer_max} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" account_unit_net  != null "><![CDATA[ AND A.ACCOUNT_UNIT_NET = #{account_unit_net} ]]></if>
		<if test=" transfer_confirm_desc != null and transfer_confirm_desc != ''  "><![CDATA[ AND A.TRANSFER_CONFIRM_DESC = #{transfer_confirm_desc} ]]></if>
		<if test=" box_position_amount  != null "><![CDATA[ AND A.BOX_POSITION_AMOUNT = #{box_position_amount} ]]></if>
		<if test=" total_unit  != null "><![CDATA[ AND A.TOTAL_UNIT = #{total_unit} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" box_unit_net  != null "><![CDATA[ AND A.BOX_UNIT_NET = #{box_unit_net} ]]></if>
		<if test=" position_status  != null "><![CDATA[ AND A.POSITION_STATUS = #{position_status} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" transfer_amount  != null "><![CDATA[ AND A.TRANSFER_AMOUNT = #{transfer_amount} ]]></if>
		<if test=" asset_mag_fee  != null "><![CDATA[ AND A.ASSET_MAG_FEE = #{asset_mag_fee} ]]></if>
		<if test=" unit_off_price  != null "><![CDATA[ AND A.UNIT_OFF_PRICE = #{unit_off_price} ]]></if>
		<if test=" customer_unit_net  != null "><![CDATA[ AND A.CUSTOMER_UNIT_NET = #{customer_unit_net} ]]></if>
		<if test=" unit_bid_price  != null "><![CDATA[ AND A.UNIT_BID_PRICE = #{unit_bid_price} ]]></if>
		<if test=" account_unit  != null "><![CDATA[ AND A.ACCOUNT_UNIT = #{account_unit} ]]></if>
		<if test=" customer_unit  != null "><![CDATA[ AND A.CUSTOMER_UNIT = #{customer_unit} ]]></if>
		<if test=" asset_mag_fee_unit  != null "><![CDATA[ AND A.ASSET_MAG_FEE_UNIT = #{asset_mag_fee_unit} ]]></if>
		<if test=" transfer_confirm_status  != null "><![CDATA[ AND A.TRANSFER_CONFIRM_STATUS = #{transfer_confirm_status} ]]></if>
		<if test=" transfer_min  != null "><![CDATA[ AND A.TRANSFER_MIN = #{transfer_min} ]]></if>
		<if test=" box_position_rate  != null "><![CDATA[ AND A.BOX_POSITION_RATE = #{box_position_rate} ]]></if>
		<if test=" startDate !=null and startDate !='' "><![CDATA[ AND A.PRICING_DATE >= #{startDate}]]></if>
		<if test=" endDate != null and endDate !='' "><![CDATA[AND A.PRICING_DATE <= #{endDate}]]></if>
		<if test=" transfer_date  != null  and  transfer_date  != ''  "><![CDATA[ AND A.TRANSFER_DATE = #{transfer_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryBoxPositionByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addBoxPosition"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_BOX_POSITION__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BOX_POSITION(
				BOX_UNIT, TRANSFER_MAX, FUND_CODE, ACCOUNT_UNIT_NET, INSERT_TIMESTAMP, TRANSFER_CONFIRM_DESC, UPDATE_BY, 
				BOX_POSITION_AMOUNT, TOTAL_UNIT, LIST_ID, BOX_UNIT_NET, POSITION_STATUS, PRICING_DATE, TRANSFER_AMOUNT, 
				ASSET_MAG_FEE, INSERT_TIME, UNIT_OFF_PRICE, UPDATE_TIME, CUSTOMER_UNIT_NET, UNIT_BID_PRICE, ACCOUNT_UNIT, 
				CUSTOMER_UNIT, ASSET_MAG_FEE_UNIT, TRANSFER_CONFIRM_STATUS, TRANSFER_MIN, UPDATE_TIMESTAMP, BOX_POSITION_RATE, INSERT_BY,TRANSFER_DATE ) 
			VALUES (
				#{box_unit, jdbcType=NUMERIC}, #{transfer_max, jdbcType=NUMERIC} , #{fund_code, jdbcType=VARCHAR} , #{account_unit_net, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{transfer_confirm_desc, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{box_position_amount, jdbcType=NUMERIC} , #{total_unit, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{box_unit_net, jdbcType=NUMERIC} , #{position_status, jdbcType=NUMERIC} , #{pricing_date, jdbcType=DATE} , #{transfer_amount, jdbcType=NUMERIC} 
				, #{asset_mag_fee, jdbcType=NUMERIC} , SYSDATE , #{unit_off_price, jdbcType=NUMERIC} , SYSDATE , #{customer_unit_net, jdbcType=NUMERIC} , #{unit_bid_price, jdbcType=NUMERIC} , #{account_unit, jdbcType=NUMERIC} 
				, #{customer_unit, jdbcType=NUMERIC} , #{asset_mag_fee_unit, jdbcType=NUMERIC} , #{transfer_confirm_status, jdbcType=NUMERIC} , #{transfer_min, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{box_position_rate, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} 
				, #{transfer_date, jdbcType=DATE}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteBoxPosition" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BOX_POSITION WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateBoxPosition" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BOX_POSITION ]]>
		<set>
		<trim suffixOverrides=",">
		    BOX_UNIT = #{box_unit, jdbcType=NUMERIC} ,
		    TRANSFER_MAX = #{transfer_max, jdbcType=NUMERIC} ,
			FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
		    ACCOUNT_UNIT_NET = #{account_unit_net, jdbcType=NUMERIC} ,
			TRANSFER_CONFIRM_DESC = #{transfer_confirm_desc, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    BOX_POSITION_AMOUNT = #{box_position_amount, jdbcType=NUMERIC} ,
		    TOTAL_UNIT = #{total_unit, jdbcType=NUMERIC} ,
		    BOX_UNIT_NET = #{box_unit_net, jdbcType=NUMERIC} ,
		    POSITION_STATUS = #{position_status, jdbcType=NUMERIC} ,
		    PRICING_DATE = #{pricing_date, jdbcType=DATE} ,
		    TRANSFER_AMOUNT = #{transfer_amount, jdbcType=NUMERIC} ,
		    ASSET_MAG_FEE = #{asset_mag_fee, jdbcType=NUMERIC} ,
		    UNIT_OFF_PRICE = #{unit_off_price, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    CUSTOMER_UNIT_NET = #{customer_unit_net, jdbcType=NUMERIC} ,
		    UNIT_BID_PRICE = #{unit_bid_price, jdbcType=NUMERIC} ,
		    ACCOUNT_UNIT = #{account_unit, jdbcType=NUMERIC} ,
		    CUSTOMER_UNIT = #{customer_unit, jdbcType=NUMERIC} ,
		    ASSET_MAG_FEE_UNIT = #{asset_mag_fee_unit, jdbcType=NUMERIC} ,
		    TRANSFER_CONFIRM_STATUS = #{transfer_confirm_status, jdbcType=NUMERIC} ,
		    TRANSFER_MIN = #{transfer_min, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BOX_POSITION_RATE = #{box_position_rate, jdbcType=NUMERIC} ,
		    TRANSFER_DATE = #{transfer_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findBoxPositionByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BOX_UNIT, A.TRANSFER_MAX, A.FUND_CODE, A.ACCOUNT_UNIT_NET, A.TRANSFER_CONFIRM_DESC, 
			A.BOX_POSITION_AMOUNT, A.TOTAL_UNIT, A.LIST_ID, A.BOX_UNIT_NET, A.POSITION_STATUS, A.PRICING_DATE, A.TRANSFER_AMOUNT, 
			A.ASSET_MAG_FEE, A.UNIT_OFF_PRICE, A.CUSTOMER_UNIT_NET, A.UNIT_BID_PRICE, A.ACCOUNT_UNIT, 
			A.CUSTOMER_UNIT, A.ASSET_MAG_FEE_UNIT, A.TRANSFER_CONFIRM_STATUS, A.TRANSFER_MIN, A.BOX_POSITION_RATE,A.TRANSFER_DATE FROM APP___PAS__DBUSER.T_BOX_POSITION A WHERE 1 = 1  ]]>
		<include refid="queryBoxPositionByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapBoxPosition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BOX_UNIT, A.TRANSFER_MAX, A.FUND_CODE, A.ACCOUNT_UNIT_NET, A.TRANSFER_CONFIRM_DESC, 
			A.BOX_POSITION_AMOUNT, A.TOTAL_UNIT, A.LIST_ID, A.BOX_UNIT_NET, A.POSITION_STATUS, A.PRICING_DATE, A.TRANSFER_AMOUNT, 
			A.ASSET_MAG_FEE, A.UNIT_OFF_PRICE, A.CUSTOMER_UNIT_NET, A.UNIT_BID_PRICE, A.ACCOUNT_UNIT, 
			A.CUSTOMER_UNIT, A.ASSET_MAG_FEE_UNIT, A.TRANSFER_CONFIRM_STATUS, A.TRANSFER_MIN, A.BOX_POSITION_RATE,A.TRANSFER_DATE FROM APP___PAS__DBUSER.T_BOX_POSITION A WHERE ROWNUM <=  1000  ]]>
		 <include refid="boxPositionWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllBoxPosition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BOX_UNIT, A.TRANSFER_MAX, A.FUND_CODE, A.ACCOUNT_UNIT_NET, A.TRANSFER_CONFIRM_DESC, 
			A.BOX_POSITION_AMOUNT, A.TOTAL_UNIT, A.LIST_ID, A.BOX_UNIT_NET, A.POSITION_STATUS, A.PRICING_DATE, A.TRANSFER_AMOUNT, 
			A.ASSET_MAG_FEE, A.UNIT_OFF_PRICE, A.CUSTOMER_UNIT_NET, A.UNIT_BID_PRICE, A.ACCOUNT_UNIT, 
			A.CUSTOMER_UNIT, A.ASSET_MAG_FEE_UNIT, A.TRANSFER_CONFIRM_STATUS, A.TRANSFER_MIN, A.BOX_POSITION_RATE,A.TRANSFER_DATE FROM APP___PAS__DBUSER.T_BOX_POSITION A WHERE ROWNUM <=  1000  ]]>
		<include refid="boxPositionWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findBoxPositionTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BOX_POSITION A WHERE 1 = 1  ]]>
		<include refid="boxPositionWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryBoxPositionForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BOX_UNIT, B.TRANSFER_MAX, B.FUND_CODE, B.ACCOUNT_UNIT_NET, B.TRANSFER_CONFIRM_DESC, 
			B.BOX_POSITION_AMOUNT, B.TOTAL_UNIT, B.LIST_ID, B.BOX_UNIT_NET, B.POSITION_STATUS, B.PRICING_DATE, B.TRANSFER_AMOUNT, 
			B.ASSET_MAG_FEE, B.UNIT_OFF_PRICE, B.CUSTOMER_UNIT_NET, B.UNIT_BID_PRICE, B.ACCOUNT_UNIT, 
			B.CUSTOMER_UNIT, B.ASSET_MAG_FEE_UNIT, B.TRANSFER_CONFIRM_STATUS, B.TRANSFER_MIN, B.BOX_POSITION_RATE,B.TRANSFER_DATE FROM (
					SELECT ROWNUM RN, A.BOX_UNIT, A.TRANSFER_MAX, A.FUND_CODE, A.ACCOUNT_UNIT_NET, A.TRANSFER_CONFIRM_DESC, 
			A.BOX_POSITION_AMOUNT, A.TOTAL_UNIT, A.LIST_ID, A.BOX_UNIT_NET, A.POSITION_STATUS, A.PRICING_DATE, A.TRANSFER_AMOUNT, 
			A.ASSET_MAG_FEE, A.UNIT_OFF_PRICE, A.CUSTOMER_UNIT_NET, A.UNIT_BID_PRICE, A.ACCOUNT_UNIT, 
			A.CUSTOMER_UNIT, A.ASSET_MAG_FEE_UNIT, A.TRANSFER_CONFIRM_STATUS, A.TRANSFER_MIN, A.BOX_POSITION_RATE,A.TRANSFER_DATE 
			FROM (SELECT M.BOX_UNIT,
                       M.TRANSFER_MAX,
                       M.FUND_CODE,
                       M.ACCOUNT_UNIT_NET,
                       M.TRANSFER_CONFIRM_DESC,
                       M.BOX_POSITION_AMOUNT,
                       M.TOTAL_UNIT,
                       M.LIST_ID,
                       M.BOX_UNIT_NET,
                       M.POSITION_STATUS,
                       M.PRICING_DATE,
                       M.TRANSFER_AMOUNT,
                       M.ASSET_MAG_FEE,
                       M.UNIT_OFF_PRICE,
                       M.CUSTOMER_UNIT_NET,
                       M.UNIT_BID_PRICE,
                       M.ACCOUNT_UNIT,
                       M.CUSTOMER_UNIT,
                       M.ASSET_MAG_FEE_UNIT,
                       M.TRANSFER_CONFIRM_STATUS,
                       M.TRANSFER_MIN,
                       M.BOX_POSITION_RATE,
                       M.TRANSFER_DATE
                  FROM APP___PAS__DBUSER.T_BOX_POSITION M
                 WHERE 1 = 1 ]]>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND M.FUND_CODE = #{fund_code} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND M.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" box_position_rate  != null "><![CDATA[ AND M.BOX_POSITION_RATE = #{box_position_rate} ]]></if>
		<if test=" startDate !=null and startDate !='' "><![CDATA[ AND M.PRICING_DATE >= #{startDate}]]></if>
		<if test=" endDate != null and endDate !='' "><![CDATA[AND M.PRICING_DATE <= #{endDate}]]></if>
		<if test=" transfer_date  != null  and  transfer_date  != ''  "><![CDATA[ AND M.TRANSFER_DATE = #{transfer_date} ]]></if>
<![CDATA[
                 ORDER BY M.PRICING_DATE DESC) A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<![CDATA[ )  B WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 1导出投连日独立账户报告查询 -->
	<select id="findIndependReportExcel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TBP.PRICING_DATE AS PRICINGDATE,
				       TBP.FUND_CODE AS FUNDCODE,
				       TBA.FUND_NAME AS FUNDNAME,
				       NVL(TBP.UNIT_OFF_PRICE, 0) AS UNITOFFPRICE,
				       NVL(TBP.UNIT_BID_PRICE, 0) AS UNITBIDPRICE,
				       NVL((SELECT TBPA.CUSTOMER_UNIT
					           FROM APP___PAS__DBUSER.T_BOX_POSITION TBPA
					          WHERE TBP.FUND_CODE = TBPA.FUND_CODE
					            AND TBPA.PRICING_DATE =
					                (SELECT MAX(Z.PRICING_DATE)
					                   FROM DEV_PAS.T_BOX_POSITION Z
					                  WHERE Z.FUND_CODE = TBP.FUND_CODE
					                    AND Z.PRICING_DATE < TBP.PRICING_DATE)),
					         0) AS LASTCUSTOMERUNIT,
					     NVL((SELECT TBPB.BOX_UNIT
					           FROM APP___PAS__DBUSER.T_BOX_POSITION TBPB
					          WHERE TBP.FUND_CODE = TBPB.FUND_CODE
					            AND TBPB.PRICING_DATE =
					                (SELECT MAX(Z.PRICING_DATE)
					                   FROM DEV_PAS.T_BOX_POSITION Z
					                  WHERE Z.FUND_CODE = TBP.FUND_CODE
					                    AND Z.PRICING_DATE < TBP.PRICING_DATE)),
					         0) AS LASTBOXUNIT,
					     NVL((SELECT TBPC.TOTAL_UNIT
					           FROM APP___PAS__DBUSER.T_BOX_POSITION TBPC
					          WHERE TBP.FUND_CODE = TBPC.FUND_CODE
					            AND TBPC.PRICING_DATE =
					                (SELECT MAX(Z.PRICING_DATE)
					                   FROM DEV_PAS.T_BOX_POSITION Z
					                  WHERE Z.FUND_CODE = TBP.FUND_CODE
					                    AND Z.PRICING_DATE < TBP.PRICING_DATE)),
					         0) AS LASTTOTALUNIT,
				       NVL(TBP.CUSTOMER_UNIT, 0) AS CUSTOMERUNIT,
				       NVL(TBP.BOX_UNIT, 0) AS BOXUNIT,
				       NVL(TBP.CUSTOMER_UNIT_NET, 0) AS CUSTOMERUNITNET,
				       NVL(TBP.BOX_UNIT_NET, 0) AS BOXUNITNET,
				       NVL(TBP.TOTAL_UNIT, 0) AS TOTALUNIT,
				       NVL(TBP.ASSET_MAG_FEE_UNIT, 0) AS ASSETMAGFEEUNIT,
				       NVL(TBP.ACCOUNT_UNIT_NET, 0) AS ACCOUNTUNITNET,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE IN (11, 27, 19)
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS NBPURCHASE,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE IN (13, 31, 34, 37)
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS APPENDPREMPURCHASE,
				       0 AS HESITATEUNLOADED,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE = '24'
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS ANNUITYUNLOADED,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE IN (08, 22)
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS PGUNLOADED,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE IN (07, 21)
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS CTUNLOADED,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE = '25'
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS CLMUNLOADED,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE = '24'
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS MATURITYUNLOADED,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE IN (03, 41)
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS RISKPREMUNLOADED,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE = '04'
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS POLICYMANAFEEUNLOADED,
				       0 AS ADDRISKSUNLOADED,
				       0 AS ADJUSTMENTUNITS,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE IN (14, 16, 17, 18, 44)
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS OTHERPURCHASE,
				       NVL((SELECT SUM(TFT.TRANS_UNITS)
				             FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
				            WHERE TFT.TRANS_CODE IN (23, 26, 45)
				              AND TFT.FUND_CODE = TBP.FUND_CODE
				              AND TFT.DEAL_TIME = TBP.PRICING_DATE),
				           0) AS ZHUANCHUUNLOADED
				  FROM APP___PAS__DBUSER.T_BOX_POSITION TBP,
				       APP___PAS__DBUSER.T_BOX_ACCOUNT  TBA
				 WHERE 1 = 1
				   AND TBP.FUND_CODE = TBA.FUND_CODE ]]>
				<if test=" fundcode != null and fundcode != ''  "><![CDATA[ AND TBP.FUND_CODE = #{fundcode} ]]></if>
				<if test=" pricingdate != null "><![CDATA[ AND TBP.PRICING_DATE = #{pricingdate} ]]></if>
	</select>
	
	<!-- 3计价日报告（客户净现金流） -->
	<select id="findNetCashFlowExcel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (13, 31, 34, 37)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS ADDPREMIUM,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (17, 24)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS YEARDRAWACCVALUE,
                   
               0 AS CURRENTACCOUNTUNIT,
               
               TBP.DEAL_TIME AS PRICINGDATE,
               TBP.FUND_CODE AS FUNDCODE,               
               
               (SELECT ACC.INVEST_ACCOUNT_NAME
                     FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO ACC
                    WHERE ACC.INVEST_ACCOUNT_CODE = TBP.FUND_CODE) AS FUNDNAME,              
               
               NVL(TBP.Fund_Purc_Price, 0) AS UNITOFFPRICE,
               NVL(TBP.Fund_Sell_Price, 0) AS UNITBIDPRICE,               
               
               (SELECT DISTINCT TBPT.PRODUCT_NAME_SYS
                  FROM APP___PAS__DBUSER.T_FUND_TRANS         TFT,
                       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
                       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBPT
                 WHERE TFT.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                   AND TCBP.BUSI_PRD_ID = TBPT.BUSINESS_PRD_ID
                   AND TFT.FUND_CODE = TBP.FUND_CODE
                   AND TFT.DEAL_TIME = TBP.DEAL_TIME) AS PRONAME,
               '1' AS BRANCHNAME,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (11, 27, 29)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS NEWCONTRACTPREMIUM,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (03, 05, 12, 28, 30, 31)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS RENEWALPREMIUM,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (35, 36, 37)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS INITIALCOST,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (38, 39, 40)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS DIFFERENCEBETWEEN,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (14, 16, 17, 18, 44)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS TRANSOTHERACCOUNT,
               0 AS UNREFUNDACCVALUE,
               0 AS UNREFUNDREIMBURSE,
               0 AS UNREFUNDRETURNPREMIUM,
               0 AS UNREFUNDEXPENSE,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '22'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS PARTIALCLAIMACCVALUE,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '08'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS PARTIALCLAIMPOUNDAGE,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '21'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS SURRENDERACCVALUE,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '07'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS SURRENDERCHARGES,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '25'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS CLAIMSACCVALUE,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (03, 41)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS CLAIMSOTHERRISK,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '24'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS MATURITYACCVALUE,
               0 AS TURNSOUTREDEMPTION,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '41'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS RISKPREMIUM,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '04'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS POLICYADMINFEE,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE IN (03, 41)
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS ASSETADMINFEE,
               NVL((SELECT SUM(TFT.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                    WHERE TFT.TRANS_CODE = '46'
                      AND TFT.FUND_CODE = TBP.FUND_CODE
                      AND TFT.DEAL_TIME = TBP.DEAL_TIME),
                   0) AS DELAYADJUSTMENT,
               0 AS ERRORREBATE,
               0 AS ERRORUNITADJUSTMENT
          FROM APP___PAS__DBUSER.T_FUND_TRANS TBP
          WHERE 1 = 1  ]]>
		<if test=" fundcode != null and fundcode != ''  "><![CDATA[ AND TBP.FUND_CODE = #{fundcode} ]]></if>
		<if test=" pricingdate != null "><![CDATA[ AND TBP.DEAL_TIME = #{pricingdate} ]]></if>
	</select>
	
    <!--2 计价日客户单位数报告 下面TRANS_CODE范围取至客户批处理 -->
	<select id="exportBoxPriciingCustomerExcel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT T.PRICINGDATE,
                MAX(T.FUNDCODE) FUNDCODE,
                MAX(T.FUNDNAME) FUNDNAME,
                MAX(T.UNITOFFPRICE) UNITOFFPRICE,
                MAX(T.UNITBIDPRICE) UNITBIDPRICE,
                T.RISKCODE,
                T.ORGANCODE,
                SUM(T.QCSE) QCSE,
                SUM(T.NBPURCHASE) NBPURCHASE,
                SUM(T.XQPREMPURCHASE) XQPREMPURCHASE,
                SUM(T.OTHERPURCHASE) OTHERPURCHASE,
                SUM(T.HESITATEUNLOADED) HESITATEUNLOADED,
                SUM(T.PGUNLOADED) PGUNLOADED,
                SUM(T.CTUNLOADED) CTUNLOADED,
                SUM(T.CLMUNLOADED) CLMUNLOADED,
                SUM(T.MATURITYUNLOADED) MATURITYUNLOADED,
                SUM(T.ZHUANCHUUNLOADED) ZHUANCHUUNLOADED,
                SUM(T.NJUNLOADED) NJUNLOADED,
                SUM(T.RISKPREMUNLOADED) RISKPREMUNLOADED,
                SUM(T.POLICYMANAFEEUNLOADED) POLICYMANAFEEUNLOADED,
                SUM(T.ZCMANAFEEUNLOADED) ZCMANAFEEUNLOADED,
                SUM(T.ADDRISKSUNLOADED) ADDRISKSUNLOADED,
                '' JCHT,
                SUM(T.ADJUSTMENTUNLOADED) ADJUSTMENTUNLOADED,
                SUM(T.CUSTOMERUNITNET) CUSTOMERUNITNET,
                SUM(T.LASTTOTALUNIT) LASTTOTALUNIT 
           FROM (SELECT 
         TIUP.PRICING_DATE AS PRICINGDATE,
         TIUP.INVEST_ACCOUNT_CODE AS FUNDCODE,
         (SELECT TBA.FUND_NAME FROM 
        APP___PAS__DBUSER.T_BOX_ACCOUNT TBA 
        WHERE TBA.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE 
        AND ROWNUM = 1) AS FUNDNAME,
         TIUP.OFF_PRICE AS UNITOFFPRICE,
         TIUP.BID_PRICE AS UNITBIDPRICE,                       
         TCBP.BUSI_PROD_CODE AS RISKCODE, 
         SUBSTR(TCM.ORGAN_CODE, 1, 4) AS ORGANCODE,
         (SELECT 
           SUM(CASE WHEN   
              TFTT.TRANS_CODE IN ('11','27','29','13','31','34','37',
                        '40','14','16','17','18') 
            THEN 
              TFTT.TRANS_UNITS 
            ELSE 
              -1*TFTT.TRANS_UNITS
             END)  
          FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
         WHERE TFTT.TRANS_CODE IN ('11','27','29','13','31','34',
                     '37','40','14','16','17','18','44',
                     '07','21','08','22','25','24','23',
                     '26','45','03','41','04','02')
           AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
           AND TFTT.DEAL_TIME < TIUP.PRICING_DATE
           AND TFTT.POLICY_ID=TCM.POLICY_ID) AS QCSE,
                NVL((SELECT SUM(TFTT.TRANS_UNITS)
                    FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
                    WHERE TFTT.TRANS_CODE IN (11, 27, 29)
                    AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                    AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                    AND TFTT.POLICY_ID=TCM.POLICY_ID),
                    0) AS NBPURCHASE,
                    NVL((SELECT SUM(TFTT.TRANS_UNITS)
                    FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
                    WHERE TFTT.TRANS_CODE IN (13, 31, 34, 37, 40)
                    AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                    AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                    AND TFTT.POLICY_ID=TCM.POLICY_ID),
                    0) AS XQPREMPURCHASE,
                NVL((SELECT SUM(TFTT.TRANS_UNITS)
                   FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
                  WHERE TFTT.TRANS_CODE IN (14, 16, 17, 18, 44)
                    AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                    AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                    AND TFTT.POLICY_ID=TCM.POLICY_ID),
                 0) AS OTHERPURCHASE,
                 0 AS HESITATEUNLOADED,
               (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE IN (08, 22)
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS PGUNLOADED,
                 (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE IN (07, 21)
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS CTUNLOADED,
                 (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE = '25'
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS CLMUNLOADED,
                 (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE = '24'
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS MATURITYUNLOADED,
                 (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE IN (23, 26, 45)
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS ZHUANCHUUNLOADED,                
               (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE = '24'
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS NJUNLOADED,
             (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE IN (03, 41)
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS RISKPREMUNLOADED,
             (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE = '04'
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS POLICYMANAFEEUNLOADED,
             (SELECT SUM(TFTT.TRANS_UNITS)
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE = '02'
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS ZCMANAFEEUNLOADED,
                 0 AS ADDRISKSUNLOADED,
                 '' AS JCHT,
                 0 AS ADJUSTMENTUNLOADED,
                 (SELECT 
                 SUM(
                      CASE WHEN   
                        TFTT.TRANS_CODE IN ('11','27','29','13','31','34','37',
                                            '40','14','16','17','18') 
                      THEN 
                        TFTT.TRANS_UNITS 
                      ELSE 
                        -1*TFTT.TRANS_UNITS
                       END
                     )  
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE IN ('11','27','29','13','31','34',
                                     '37','40','14','16','17','18','44',
                                     '07','21','08','22','25','24','23',
                                     '26','45','03','41','04','02')
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME = TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS CUSTOMERUNITNET, 
                 (SELECT 
                 SUM(CASE WHEN   
                        TFTT.TRANS_CODE IN ('11','27','29','13','31','34','37',
                                            '40','14','16','17','18') 
                      THEN 
                        TFTT.TRANS_UNITS 
                      ELSE 
                        -1*TFTT.TRANS_UNITS
                       END)  
                FROM APP___PAS__DBUSER.T_FUND_TRANS TFTT
               WHERE TFTT.TRANS_CODE IN ('11','27','29','13','31','34',
                                     '37','40','14','16','17','18','44',
                                     '07','21','08','22','25','24','23',
                                     '26','45','03','41','04','02')
                 AND TFTT.FUND_CODE = TIUP.INVEST_ACCOUNT_CODE
                 AND TFTT.DEAL_TIME <= TIUP.PRICING_DATE
                 AND TFTT.POLICY_ID=TCM.POLICY_ID) AS LASTTOTALUNIT 
            FROM 
            APP___PAS__DBUSER.T_CONTRACT_MASTER  TCM
            LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
            ON TCBP.POLICY_ID = TCM.POLICY_ID
            LEFT JOIN APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP
            ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
            LEFT JOIN APP___PAS__DBUSER.t_contract_invest TCI
            ON TCI.POLICY_ID = TCM.POLICY_ID
            LEFT JOIN APP___PAS__DBUSER.T_INVEST_UNIT_PRICE TIUP  
            ON TCI.ACCOUNT_CODE=TIUP.INVEST_ACCOUNT_CODE                   
            WHERE 1=1 
              AND TBP.PRODUCT_CATEGORY1 = '20004'
              AND SUBSTR(TCM.ORGAN_CODE, 1, 4) IN
                  (SELECT Z.ORGAN_CODE
                     FROM APP___PAS__DBUSER.T_UDMP_ORG Z
                    WHERE Z.ORGAN_TYPE = 2)
              AND TCM.POLICY_ID IN (SELECT Z.POLICY_ID FROM 
                    APP___PAS__DBUSER.T_CONTRACT_INVEST Z
                  WHERE Z.INVEST_ACCOUNT_TYPE=1 
                  AND Z.ACCOUNT_CODE = TIUP.INVEST_ACCOUNT_CODE )]]>
				<if test=" fundcode != null and fundcode != ''  "><![CDATA[AND TIUP.INVEST_ACCOUNT_CODE = #{fundcode} ]]></if>
				<if test=" pricingdate != null "><![CDATA[ AND TIUP.PRICING_DATE = #{pricingdate} ]]></if>
                 <![CDATA[) T WHERE T.CUSTOMERUNITNET IS NOT NULL 
						         OR T.QCSE IS NOT NULL 
						         OR T.LASTTOTALUNIT IS NOT NULL
						         OR T.CLMUNLOADED IS NOT NULL
                    GROUP BY T.ORGANCODE, T.RISKCODE,T.PRICINGDATE ORDER BY T.ORGANCODE
				 ]]>
				
	</select>

	<!-- 4延迟日报告 -->
	<select id="findDelayedDayReportExcel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT  TFT.DEAL_TIME AS PRICINGDATE,
                (SELECT PR.PRODUCT_NAME_STD
                    FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD P,
                                     APP___PAS__DBUSER.T_BUSINESS_PRODUCT PR
                      WHERE P.BUSI_ITEM_ID = TFTA.BUSI_ITEM_ID
                      AND P.BUSI_PRD_ID = PR.BUSINESS_PRD_ID)  AS PRONAME,
               TFT.FUND_CODE AS FUNDCODE,
               (SELECT ACC.INVEST_ACCOUNT_NAME
                     FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO ACC
                    WHERE ACC.INVEST_ACCOUNT_CODE = TFT.FUND_CODE) AS FUNDNAME,
               NVL(TFT.FUND_SELL_PRICE, 0) AS UNITOFFPRICE,
               NVL(TFT.FUND_PURC_PRICE, 0) AS UNITBIDPRICE,
                (SELECT M.POLICY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER M 
                WHERE M.POLICY_ID = TFTA.POLICY_ID )  AS POLICYNO,
               TFT.DEAL_TIME AS APPLYPRODATE,
               NVL(TFTA.APPLY_UNITS, 0) AS APPLYPRONUMBER,
               NVL(TFTA.APPLY_AMOUNT, 0) AS APPLYPROVALUE,
               TFT.DEAL_TIME AS ACTPRODATE,
               TFT.FUND_PURC_PRICE AS ACTPROVALUE,
               NVL((SELECT SUM(TFTS.TRANS_UNITS)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFTS
                    WHERE TFTS.TRANS_CODE = '01'
                      AND TFTS.FUND_CODE = TFT.FUND_CODE
                      AND TFTS.DEAL_TIME = TFT.DEAL_TIME),
                   0) AS DELAYADJUSTMENT,
               NVL((SELECT SUM(TFTS.TRANS_AMOUNT)
                     FROM APP___PAS__DBUSER.T_FUND_TRANS TFTS
                    WHERE TFTS.TRANS_CODE = '01'
                      AND TFTS.FUND_CODE = TFT.FUND_CODE
                      AND TFTS.DEAL_TIME = TFT.DEAL_TIME),
                   0) AS companylossvalue
          FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY TFTA,
               APP___PAS__DBUSER.T_FUND_TRANS       TFT
         WHERE 1 = 1  AND TFT.TRANS_CODE = '01'
           AND TFTA.APPLY_ID = TFT.APPLY_ID]]>
		<if test=" fundcode != null and fundcode != ''  "><![CDATA[ AND TFT.FUND_CODE = #{fundcode} ]]></if>
		<if test=" pricingdate != null "><![CDATA[ AND TFT.DEAL_TIME = #{pricingdate} ]]></if>
	</select>
	
	
	<!-- 修改操作 -->
	<update id="updateOldSystemDataEntry" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BOX_POSITION ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_UNIT_NET = #{customer_unit_net, jdbcType=NUMERIC} ,
		    CUSTOMER_UNIT = #{customer_unit, jdbcType=NUMERIC} ,
		    PRICING_DATE = #{pricing_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE FUND_CODE = #{fund_code} ]]>
	</update>
	
	<!-- 修改操作 -->
	<update id="updateOldSystemDataReview" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BOX_POSITION ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_UNIT_NET = #{customer_unit_net, jdbcType=NUMERIC} ,
		    CUSTOMER_UNIT = #{customer_unit, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE FUND_CODE = #{fund_code} and PRICING_DATE = #{pricing_date, jdbcType=DATE}]]>
	</update>
</mapper>
