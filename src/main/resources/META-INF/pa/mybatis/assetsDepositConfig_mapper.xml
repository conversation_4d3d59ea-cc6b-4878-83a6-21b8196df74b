<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAssetsDepositConfigDao">
<!--
	<sql id="assetsDepositConfigWhereCondition">
		<if test=" file_path_name != null and file_path_name != ''  "><![CDATA[ AND A.FILE_PATH_NAME = #{file_path_name} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" invest_account_id  != null "><![CDATA[ AND A.INVEST_ACCOUNT_ID = #{invest_account_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryAssetsDepositConfigByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addAssetsDepositConfig"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_MASTER__POLICY_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG(
				FILE_PATH_NAME, INSERT_TIMESTAMP, INVEST_ACCOUNT_CODE, UPDATE_BY, INSERT_TIME, BANK_CODE, LIST_ID, 
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, INVEST_ACCOUNT_ID ) 
			VALUES (
				#{file_path_name, jdbcType=VARCHAR}, CURRENT_TIMESTAMP, #{invest_account_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{bank_code, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteAssetsDepositConfig" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateAssetsDepositConfig" parameterType="java.util.Map">
			<![CDATA[ UPDATE APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG ]]>
		<set>
		<trim suffixOverrides=",">
			FILE_PATH_NAME = #{file_path_name, jdbcType=VARCHAR} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findAssetsDepositConfigByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FILE_PATH_NAME, A.INVEST_ACCOUNT_CODE, A.BANK_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG A WHERE 1 = 1  ]]>
		<include refid="queryAssetsDepositConfigByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapAssetsDepositConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FILE_PATH_NAME, A.INVEST_ACCOUNT_CODE, A.BANK_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllAssetsDepositConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FILE_PATH_NAME, A.INVEST_ACCOUNT_CODE, A.BANK_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findAssetsDepositConfigTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryAssetsDepositConfigForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.FILE_PATH_NAME, B.INVEST_ACCOUNT_CODE, B.BANK_CODE, B.LIST_ID, 
			B.INVEST_ACCOUNT_ID FROM (
					SELECT ROWNUM RN, A.FILE_PATH_NAME, A.INVEST_ACCOUNT_CODE, A.BANK_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_ASSETS_DEPOSIT_CONFIG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
