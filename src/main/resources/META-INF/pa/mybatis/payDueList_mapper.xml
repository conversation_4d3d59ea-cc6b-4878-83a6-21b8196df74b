<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayDueListDao">

	<sql id="payDueListWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" payee_customer_id  != null "><![CDATA[ AND <PERSON>.<PERSON>YEE_CUSTOMER_ID = #{payee_customer_id} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" due_id  != null "><![CDATA[ AND A.DUE_ID = #{due_id} ]]></if>
		<if test=" telephone != null and telephone != ''  "><![CDATA[ AND A.TELEPHONE = #{telephone} ]]></if>
		<if test=" auth_draw != null and auth_draw != ''  "><![CDATA[ AND A.AUTH_DRAW = #{auth_draw} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" assignee_rate  != null "><![CDATA[ AND A.ASSIGNEE_RATE = #{assignee_rate} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>		
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryPayDueListByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryPayDueListByDueIdCondition">
		<if test=" due_id  != null "><![CDATA[ AND A.DUE_ID = #{due_id} ]]></if>
	</sql>	
	<sql id="queryPayDueListByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="queryPayDueListByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPayDueList"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PAY_DUE_LIST__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_DUE_LIST(
				ADDRESS_ID, PAYEE_NAME, ACCOUNT_ID, PLAN_ID, INSERT_TIME, PAYEE_CUSTOMER_ID, FEE_STATUS, 
				UPDATE_TIME, DUE_ID, TELEPHONE, AUTH_DRAW, INSERT_TIMESTAMP, CERTI_CODE, UPDATE_BY, 
				ASSIGNEE_RATE, MOBILE_TEL, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, CERTI_TYPE ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{payee_name, jdbcType=VARCHAR} , #{account_id, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{payee_customer_id, jdbcType=NUMERIC} , #{fee_status, jdbcType=VARCHAR} 
				, SYSDATE , #{due_id, jdbcType=NUMERIC} , #{telephone, jdbcType=VARCHAR} , #{auth_draw, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{certi_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{assignee_rate, jdbcType=NUMERIC} , #{mobile_tel, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ,  #{certi_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePayDueList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST WHERE   LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePayDueList" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE_LIST ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
			PAYEE_NAME = #{payee_name, jdbcType=VARCHAR} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    PAYEE_CUSTOMER_ID = #{payee_customer_id, jdbcType=NUMERIC} ,
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    DUE_ID = #{due_id, jdbcType=NUMERIC} ,
			TELEPHONE = #{telephone, jdbcType=VARCHAR} ,
			AUTH_DRAW = #{auth_draw, jdbcType=VARCHAR} ,
			CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    ASSIGNEE_RATE = #{assignee_rate, jdbcType=NUMERIC} ,
			MOBILE_TEL = #{mobile_tel, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CERTI_TYPE = #{certi_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPayDueListByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYEE_NAME, A.ACCOUNT_ID, A.PLAN_ID, A.PAYEE_CUSTOMER_ID, A.FEE_STATUS, 
			A.DUE_ID, A.TELEPHONE, A.AUTH_DRAW, A.CERTI_CODE, 
			A.ASSIGNEE_RATE, A.MOBILE_TEL, A.LIST_ID, A.CERTI_TYPE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE 1 = 1  ]]>
		<include refid="queryPayDueListByListIdCondition" />
	</select>
	
	<select id="findPayDueListByDueId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYEE_NAME, A.ACCOUNT_ID, A.PLAN_ID, A.PAYEE_CUSTOMER_ID, A.FEE_STATUS, 
			A.DUE_ID, A.TELEPHONE, A.AUTH_DRAW, A.CERTI_CODE, 
			A.ASSIGNEE_RATE, A.MOBILE_TEL, A.LIST_ID, A.CERTI_TYPE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE 1 = 1  ]]>
		<include refid="queryPayDueListByDueIdCondition" />
	</select>
	
	<select id="findPayDueListByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYEE_NAME, A.ACCOUNT_ID, A.PLAN_ID, A.PAYEE_CUSTOMER_ID, A.FEE_STATUS, 
			A.DUE_ID, A.TELEPHONE, A.AUTH_DRAW, A.CERTI_CODE, 
			A.ASSIGNEE_RATE, A.MOBILE_TEL, A.LIST_ID, A.CERTI_TYPE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE 1 = 1  ]]>
		<include refid="queryPayDueListByAccountIdCondition" />
	</select>
	
	<select id="findPayDueListByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYEE_NAME, A.ACCOUNT_ID, A.PLAN_ID, A.PAYEE_CUSTOMER_ID, A.FEE_STATUS, 
			A.DUE_ID, A.TELEPHONE, A.AUTH_DRAW, A.CERTI_CODE, 
			A.ASSIGNEE_RATE, A.MOBILE_TEL, A.LIST_ID, A.CERTI_TYPE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE 1 = 1  ]]>
		<include refid="queryPayDueListByPlanIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapPayDueList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYEE_NAME, A.ACCOUNT_ID, A.PLAN_ID, A.PAYEE_CUSTOMER_ID, A.FEE_STATUS, 
			A.DUE_ID, A.TELEPHONE, A.AUTH_DRAW, A.CERTI_CODE, 
			A.ASSIGNEE_RATE, A.MOBILE_TEL, A.LIST_ID, A.CERTI_TYPE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPayDueList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYEE_NAME, A.ACCOUNT_ID, A.PLAN_ID, A.PAYEE_CUSTOMER_ID, A.FEE_STATUS, 
			A.DUE_ID, A.TELEPHONE, A.AUTH_DRAW, A.CERTI_CODE, 
			A.ASSIGNEE_RATE, A.MOBILE_TEL, A.LIST_ID, A.CERTI_TYPE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE ROWNUM <=  1000  ]]>
		<include refid="payDueListWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID DESC]]>
	</select>

<!-- 查询个数操作 -->
	<select id="findPayDueListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryPayDueListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.PAYEE_NAME, B.ACCOUNT_ID, B.PLAN_ID, B.PAYEE_CUSTOMER_ID, B.FEE_STATUS, 
			B.DUE_ID, B.TELEPHONE, B.AUTH_DRAW, B.CERTI_CODE, 
			B.ASSIGNEE_RATE, B.MOBILE_TEL, B.LIST_ID, B.POLICY_CHANGE_ID, B.CERTI_TYPE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.PAYEE_NAME, A.ACCOUNT_ID, A.PLAN_ID, A.PAYEE_CUSTOMER_ID, A.FEE_STATUS, 
			A.DUE_ID, A.TELEPHONE, A.AUTH_DRAW, A.CERTI_CODE, 
			A.ASSIGNEE_RATE, A.MOBILE_TEL, A.LIST_ID, A.CERTI_TYPE FROM APP___PAS__DBUSER.T_PAY_DUE_LIST A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 修改操作 -->
	<update id="updatePayDueFeeStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE_LIST ]]>
		<set>
		<trim suffixOverrides=",">
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  due_id = #{due_id} ]]>
	</update>
	
</mapper>
