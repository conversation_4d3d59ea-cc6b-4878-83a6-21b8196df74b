<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPrecontProductDao">

	<sql id="precontProductWhereCondition">
		<if test=" prec_policy_chg_id  != null "><![CDATA[ AND A.PREC_POLICY_CHG_ID = #{prec_policy_chg_id} ]]></if>
		<if test=" precont_status != null and precont_status != ''  "><![CDATA[ AND A.PRECONT_STATUS = #{precont_status} ]]></if>
		<if test=" new_unit  != null "><![CDATA[ AND A.NEW_UNIT = #{new_unit} ]]></if>
		<if test=" new_account_id  != null "><![CDATA[ AND A.NEW_ACCOUNT_ID = #{new_account_id} ]]></if>
		<if test=" new_prem  != null "><![CDATA[ AND A.NEW_PREM = #{new_prem} ]]></if>
		<if test=" new_plan_id != null and new_plan_id != ''  "><![CDATA[ AND A.NEW_PLAN_ID = #{new_plan_id} ]]></if>
		<if test=" new_amount  != null "><![CDATA[ AND A.NEW_AMOUNT = #{new_amount} ]]></if>
		<if test=" trigger_module != null and trigger_module != ''  "><![CDATA[ AND A.TRIGGER_MODULE = #{trigger_module} ]]></if>
		<if test=" old_amount  != null "><![CDATA[ AND A.OLD_AMOUNT = #{old_amount} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" old_charge_mode  != null "><![CDATA[ AND A.OLD_CHARGE_MODE = #{old_charge_mode} ]]></if>
		<if test=" old_plan_id != null and old_plan_id != ''  "><![CDATA[ AND A.OLD_PLAN_ID = #{old_plan_id} ]]></if>
		<if test=" old_account_id  != null "><![CDATA[ AND A.OLD_ACCOUNT_ID = #{old_account_id} ]]></if>
		<if test=" precont_sa_inc  != null "><![CDATA[ AND A.PRECONT_SA_INC = #{precont_sa_inc} ]]></if>
		<if test=" process_time  != null  and  process_time  != ''  "><![CDATA[ AND A.PROCESS_TIME = #{process_time} ]]></if>
		<if test=" precont_time  != null  and  precont_time  != ''  "><![CDATA[ AND A.PRECONT_TIME = #{precont_time} ]]></if>
		<if test=" old_prem  != null "><![CDATA[ AND A.OLD_PREM = #{old_prem} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" new_pay_mode != null and new_pay_mode != ''  "><![CDATA[ AND A.NEW_PAY_MODE = #{new_pay_mode} ]]></if>
		<if test=" old_pay_mode != null and old_pay_mode != ''  "><![CDATA[ AND A.OLD_PAY_MODE = #{old_pay_mode} ]]></if>
		<if test=" old_stand_amount  != null "><![CDATA[ AND A.OLD_STAND_AMOUNT = #{old_stand_amount} ]]></if>
		<if test=" new_stand_amount  != null "><![CDATA[ AND A.NEW_STAND_AMOUNT = #{new_stand_amount} ]]></if>
		<if test=" new_count_way != null and new_count_way != ''  "><![CDATA[ AND A.NEW_COUNT_WAY = #{new_count_way} ]]></if>
		<if test=" old_unit  != null "><![CDATA[ AND A.OLD_UNIT = #{old_unit} ]]></if>
		<if test=" new_charge_mode  != null "><![CDATA[ AND A.NEW_CHARGE_MODE = #{new_charge_mode} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" precont_id  != null "><![CDATA[ AND A.PRECONT_ID = #{precont_id} ]]></if>
		<if test=" old_benefit_level != null and old_benefit_level != ''  "><![CDATA[ AND A.OLD_BENEFIT_LEVEL = #{old_benefit_level} ]]></if>
		<if test=" new_benefit_level != null and new_benefit_level != ''  "><![CDATA[ AND A.NEW_BENEFIT_LEVEL = #{new_benefit_level} ]]></if>
		<if test=" old_count_way != null and old_count_way != ''  "><![CDATA[ AND A.OLD_COUNT_WAY = #{old_count_way} ]]></if>
		<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND A.busi_item_id = #{busi_item_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryPrecontProductByPrecontIdCondition">
		<if test=" precont_id  != null "><![CDATA[ AND A.PRECONT_ID = #{precont_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPrecontProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="precont_id">
				SELECT APP___PAS__DBUSER.S_PAY_PLAN__PLAN_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PRECONT_PRODUCT(
				OLD_PRODUCT_ID,NEW_PRODUCT_ID,OLD_PRODUCT_CODE,NEW_PRODUCT_CODE,
				PREC_POLICY_CHG_ID, PRECONT_STATUS, NEW_UNIT, NEW_ACCOUNT_ID, NEW_PREM, NEW_PLAN_ID, NEW_AMOUNT, 
				TRIGGER_MODULE, OLD_AMOUNT, ITEM_ID, OLD_CHARGE_MODE, OLD_PLAN_ID, OLD_ACCOUNT_ID, INSERT_TIMESTAMP, 
				UPDATE_BY, PRECONT_SA_INC, PROCESS_TIME, PRECONT_TIME, OLD_PREM, POLICY_ID, NEW_PAY_MODE, 
				OLD_PAY_MODE, OLD_STAND_AMOUNT, NEW_STAND_AMOUNT, NEW_COUNT_WAY, OLD_UNIT, INSERT_TIME, NEW_CHARGE_MODE, 
				END_CAUSE, UPDATE_TIME, PRECONT_ID, OLD_BENEFIT_LEVEL, UPDATE_TIMESTAMP, INSERT_BY, NEW_BENEFIT_LEVEL, 
				OLD_COUNT_WAY,BUSI_ITEM_ID ) 
			VALUES (
				#{old_product_id, jdbcType=NUMERIC} ,#{new_product_id, jdbcType=NUMERIC} ,#{old_product_code, jdbcType=VARCHAR} ,#{new_product_code, jdbcType=VARCHAR}, 
				#{prec_policy_chg_id, jdbcType=NUMERIC}, #{precont_status, jdbcType=VARCHAR} , #{new_unit, jdbcType=NUMERIC} , #{new_account_id, jdbcType=NUMERIC} , #{new_prem, jdbcType=NUMERIC} , #{new_plan_id, jdbcType=VARCHAR} , #{new_amount, jdbcType=NUMERIC} 
				, #{trigger_module, jdbcType=VARCHAR} , #{old_amount, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , #{old_charge_mode, jdbcType=NUMERIC} , #{old_plan_id, jdbcType=VARCHAR} , #{old_account_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{update_by, jdbcType=NUMERIC} , #{precont_sa_inc, jdbcType=NUMERIC} , #{process_time, jdbcType=DATE} , #{precont_time, jdbcType=DATE} , #{old_prem, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{new_pay_mode, jdbcType=VARCHAR} 
				, #{old_pay_mode, jdbcType=VARCHAR} , #{old_stand_amount, jdbcType=NUMERIC} , #{new_stand_amount, jdbcType=NUMERIC} , #{new_count_way, jdbcType=VARCHAR} , #{old_unit, jdbcType=NUMERIC} , SYSDATE , #{new_charge_mode, jdbcType=NUMERIC} 
				, #{end_cause, jdbcType=VARCHAR} , SYSDATE , #{precont_id, jdbcType=NUMERIC} , #{old_benefit_level, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{new_benefit_level, jdbcType=VARCHAR} 
				, #{old_count_way, jdbcType=VARCHAR},#{busi_item_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePrecontProduct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT WHERE PRECONT_ID = #{precont_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePrecontProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PRECONT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
		    PREC_POLICY_CHG_ID = #{prec_policy_chg_id, jdbcType=NUMERIC} ,
			PRECONT_STATUS = #{precont_status, jdbcType=VARCHAR} ,
		    NEW_UNIT = #{new_unit, jdbcType=NUMERIC} ,
		    NEW_ACCOUNT_ID = #{new_account_id, jdbcType=NUMERIC} ,
		    NEW_PREM = #{new_prem, jdbcType=NUMERIC} ,
			NEW_PLAN_ID = #{new_plan_id, jdbcType=VARCHAR} ,
		    NEW_AMOUNT = #{new_amount, jdbcType=NUMERIC} ,
			TRIGGER_MODULE = #{trigger_module, jdbcType=VARCHAR} ,
		    OLD_AMOUNT = #{old_amount, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    OLD_CHARGE_MODE = #{old_charge_mode, jdbcType=NUMERIC} ,
			OLD_PLAN_ID = #{old_plan_id, jdbcType=VARCHAR} ,
		    OLD_ACCOUNT_ID = #{old_account_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PRECONT_SA_INC = #{precont_sa_inc, jdbcType=NUMERIC} ,
		    PROCESS_TIME = #{process_time, jdbcType=DATE} ,
		    PRECONT_TIME = #{precont_time, jdbcType=DATE} ,
		    OLD_PREM = #{old_prem, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			NEW_PAY_MODE = #{new_pay_mode, jdbcType=VARCHAR} ,
			OLD_PAY_MODE = #{old_pay_mode, jdbcType=VARCHAR} ,
		    OLD_STAND_AMOUNT = #{old_stand_amount, jdbcType=NUMERIC} ,
		    NEW_STAND_AMOUNT = #{new_stand_amount, jdbcType=NUMERIC} ,
			NEW_COUNT_WAY = #{new_count_way, jdbcType=VARCHAR} ,
		    OLD_UNIT = #{old_unit, jdbcType=NUMERIC} ,
		    NEW_CHARGE_MODE = #{new_charge_mode, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			OLD_BENEFIT_LEVEL = #{old_benefit_level, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			NEW_BENEFIT_LEVEL = #{new_benefit_level, jdbcType=VARCHAR} ,
			OLD_COUNT_WAY = #{old_count_way, jdbcType=VARCHAR} ,
			OLD_PRODUCT_ID = #{old_product_id, jdbcType=NUMERIC} ,
			NEW_PRODUCT_ID  =#{new_product_id, jdbcType=NUMERIC} , 
			OLD_PRODUCT_CODE =#{old_product_code, jdbcType=VARCHAR} ,
			NEW_PRODUCT_CODE = #{new_product_code, jdbcType=VARCHAR} , 
		</trim>
		</set>
		<![CDATA[ WHERE PRECONT_ID = #{precont_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPrecontProductByPrecontId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREC_POLICY_CHG_ID, A.PRECONT_STATUS, A.NEW_UNIT, A.NEW_ACCOUNT_ID, A.NEW_PREM, A.NEW_PLAN_ID, A.NEW_AMOUNT, 
			A.TRIGGER_MODULE, A.OLD_AMOUNT, A.ITEM_ID, A.OLD_CHARGE_MODE, A.OLD_PLAN_ID, A.OLD_ACCOUNT_ID, 
			A.PRECONT_SA_INC, A.PROCESS_TIME, A.PRECONT_TIME, A.OLD_PREM, A.POLICY_ID, A.NEW_PAY_MODE, 
			A.OLD_PAY_MODE, A.OLD_STAND_AMOUNT, A.NEW_STAND_AMOUNT, A.NEW_COUNT_WAY, A.OLD_UNIT, A.NEW_CHARGE_MODE, 
			A.END_CAUSE, A.PRECONT_ID, A.OLD_BENEFIT_LEVEL, A.NEW_BENEFIT_LEVEL, A.OLD_PRODUCT_ID, A.NEW_PRODUCT_ID, A.OLD_PRODUCT_CODE, A.NEW_PRODUCT_CODE,
			A.OLD_COUNT_WAY FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="queryPrecontProductByPrecontIdCondition" />
		<include refid="precontProductWhereCondition" />
		<![CDATA[ ORDER BY A.PRECONT_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapPrecontProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREC_POLICY_CHG_ID, A.PRECONT_STATUS, A.NEW_UNIT, A.NEW_ACCOUNT_ID, A.NEW_PREM, A.NEW_PLAN_ID, A.NEW_AMOUNT, 
			A.TRIGGER_MODULE, A.OLD_AMOUNT, A.ITEM_ID, A.OLD_CHARGE_MODE, A.OLD_PLAN_ID, A.OLD_ACCOUNT_ID, 
			A.PRECONT_SA_INC, A.PROCESS_TIME, A.PRECONT_TIME, A.OLD_PREM, A.POLICY_ID, A.NEW_PAY_MODE, 
			A.OLD_PAY_MODE, A.OLD_STAND_AMOUNT, A.NEW_STAND_AMOUNT, A.NEW_COUNT_WAY, A.OLD_UNIT, A.NEW_CHARGE_MODE, 
			A.END_CAUSE, A.PRECONT_ID, A.OLD_BENEFIT_LEVEL, A.NEW_BENEFIT_LEVEL, A.OLD_PRODUCT_ID, A.NEW_PRODUCT_ID, A.OLD_PRODUCT_CODE, A.NEW_PRODUCT_CODE,
			A.OLD_COUNT_WAY FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.PRECONT_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPrecontProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREC_POLICY_CHG_ID, A.PRECONT_STATUS, A.NEW_UNIT, A.NEW_ACCOUNT_ID, A.NEW_PREM, A.NEW_PLAN_ID, A.NEW_AMOUNT, 
			A.TRIGGER_MODULE, A.OLD_AMOUNT, A.ITEM_ID, A.OLD_CHARGE_MODE, A.OLD_PLAN_ID, A.OLD_ACCOUNT_ID, 
			A.PRECONT_SA_INC, A.PROCESS_TIME, A.PRECONT_TIME, A.OLD_PREM, A.POLICY_ID, A.NEW_PAY_MODE, 
			A.OLD_PAY_MODE, A.OLD_STAND_AMOUNT, A.NEW_STAND_AMOUNT, A.NEW_COUNT_WAY, A.OLD_UNIT, A.NEW_CHARGE_MODE, 
			A.END_CAUSE, A.PRECONT_ID, A.OLD_BENEFIT_LEVEL, A.NEW_BENEFIT_LEVEL,A.BUSI_ITEM_ID, A.OLD_PRODUCT_ID, A.NEW_PRODUCT_ID, A.OLD_PRODUCT_CODE, A.NEW_PRODUCT_CODE,
			A.OLD_COUNT_WAY FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT A WHERE ROWNUM <= 1000  ]]>
		 <include refid="precontProductWhereCondition" />
		<![CDATA[ ORDER BY A.PRECONT_ID  ]]> 
	</select>

	<!-- 最新一条 -->
	<select id="PA_findLastPrecontProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.PREC_POLICY_CHG_ID, A.PRECONT_STATUS, A.NEW_UNIT, A.NEW_ACCOUNT_ID, A.NEW_PREM, A.NEW_PLAN_ID, A.NEW_AMOUNT, 
			A.TRIGGER_MODULE, A.OLD_AMOUNT, A.ITEM_ID, A.OLD_CHARGE_MODE, A.OLD_PLAN_ID, A.OLD_ACCOUNT_ID, 
			A.PRECONT_SA_INC, A.PROCESS_TIME, A.PRECONT_TIME, A.OLD_PREM, A.POLICY_ID, A.NEW_PAY_MODE, 
			A.OLD_PAY_MODE, A.OLD_STAND_AMOUNT, A.NEW_STAND_AMOUNT, A.NEW_COUNT_WAY, A.OLD_UNIT, A.NEW_CHARGE_MODE, 
			A.END_CAUSE, A.PRECONT_ID, A.OLD_BENEFIT_LEVEL, A.NEW_BENEFIT_LEVEL,A.BUSI_ITEM_ID, A.OLD_PRODUCT_ID, A.NEW_PRODUCT_ID, A.OLD_PRODUCT_CODE, A.NEW_PRODUCT_CODE,
			A.OLD_COUNT_WAY FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT A WHERE 1=1]]>
		 <include refid="precontProductWhereCondition" />
		<![CDATA[ ORDER BY A.INSERT_TIME DESC) WHERE ROWNUM = 1]]> 
	</select>


<!-- 查询个数操作 -->
	<select id="findPrecontProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryPrecontProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PREC_POLICY_CHG_ID, B.PRECONT_STATUS, B.NEW_UNIT, B.NEW_ACCOUNT_ID, B.NEW_PREM, B.NEW_PLAN_ID, B.NEW_AMOUNT, 
			B.TRIGGER_MODULE, B.OLD_AMOUNT, B.ITEM_ID, B.OLD_CHARGE_MODE, B.OLD_PLAN_ID, B.OLD_ACCOUNT_ID, 
			B.PRECONT_SA_INC, B.PROCESS_TIME, B.PRECONT_TIME, B.OLD_PREM, B.POLICY_ID, B.NEW_PAY_MODE, 
			B.OLD_PAY_MODE, B.OLD_STAND_AMOUNT, B.NEW_STAND_AMOUNT, B.NEW_COUNT_WAY, B.OLD_UNIT, B.NEW_CHARGE_MODE, 
			B.END_CAUSE, B.PRECONT_ID, B.OLD_BENEFIT_LEVEL, B.NEW_BENEFIT_LEVEL, 
			B.OLD_COUNT_WAY FROM (
					SELECT ROWNUM RN, A.PREC_POLICY_CHG_ID, A.PRECONT_STATUS, A.NEW_UNIT, A.NEW_ACCOUNT_ID, A.NEW_PREM, A.NEW_PLAN_ID, A.NEW_AMOUNT, 
			A.TRIGGER_MODULE, A.OLD_AMOUNT, A.ITEM_ID, A.OLD_CHARGE_MODE, A.OLD_PLAN_ID, A.OLD_ACCOUNT_ID, 
			A.PRECONT_SA_INC, A.PROCESS_TIME, A.PRECONT_TIME, A.OLD_PREM, A.POLICY_ID, A.NEW_PAY_MODE, 
			A.OLD_PAY_MODE, A.OLD_STAND_AMOUNT, A.NEW_STAND_AMOUNT, A.NEW_COUNT_WAY, A.OLD_UNIT, A.NEW_CHARGE_MODE, 
			A.END_CAUSE, A.PRECONT_ID, A.OLD_BENEFIT_LEVEL, A.NEW_BENEFIT_LEVEL, 
			A.OLD_COUNT_WAY FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.PRECONT_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
