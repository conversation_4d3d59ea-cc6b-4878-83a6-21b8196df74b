<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IQuestionaireCustomerDao">

	<sql id="questionaireCustomerWhereCondition">
		<if test=" survey_module_result != null and survey_module_result != ''  "><![CDATA[ AND A.SURVEY_MODULE_RESULT = #{survey_module_result} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" questionaire_object != null and questionaire_object != ''  "><![CDATA[ AND A.QUESTIONAIRE_OBJECT = #{questionaire_object} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_question_id  != null "><![CDATA[ AND A.SURVEY_QUESTION_ID = #{survey_question_id} ]]></if>
		<if test=" customer_survey_id  != null "><![CDATA[ AND A.CUSTOMER_SURVEY_ID = #{customer_survey_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryQuestionaireCustomerByCustomerSurveyIdCondition">
		<if test=" customer_survey_id  != null "><![CDATA[ AND A.CUSTOMER_SURVEY_ID = #{customer_survey_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addQuestionaireCustomer"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="customer_survey_id">
			select APP___PAS__DBUSER.S_QUESTIONAIRE_CUSTOMER__CUST.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER(
				SURVEY_MODULE_RESULT, INSERT_TIME, CUSTOMER_ID, QUESTIONAIRE_OBJECT, UPDATE_TIME, APPLY_CODE, SURVEY_QUESTION_ID, 
				INSERT_TIMESTAMP, CUSTOMER_SURVEY_ID, POLICY_CODE, UPDATE_BY, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, 
				AGENT_CODE ) 
			VALUES (
				#{survey_module_result, jdbcType=VARCHAR}, SYSDATE , #{customer_id, jdbcType=NUMERIC} , #{questionaire_object, jdbcType=VARCHAR} , SYSDATE , #{apply_code, jdbcType=VARCHAR} , #{survey_question_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{customer_survey_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{agent_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteQuestionaireCustomer" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER WHERE CUSTOMER_SURVEY_ID = #{customer_survey_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateQuestionaireCustomer" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER ]]>
		<set>
		<trim suffixOverrides=",">
			SURVEY_MODULE_RESULT = #{survey_module_result, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			QUESTIONAIRE_OBJECT = #{questionaire_object, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    SURVEY_QUESTION_ID = #{survey_question_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_SURVEY_ID = #{customer_survey_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findQuestionaireCustomerByCustomerSurveyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_MODULE_RESULT, A.CUSTOMER_ID, A.QUESTIONAIRE_OBJECT, A.APPLY_CODE, A.SURVEY_QUESTION_ID, 
			A.CUSTOMER_SURVEY_ID, A.POLICY_CODE, A.POLICY_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="queryQuestionaireCustomerByCustomerSurveyIdCondition" />
		<![CDATA[ ORDER BY A.CUSTOMER_SURVEY_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapQuestionaireCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_MODULE_RESULT, A.CUSTOMER_ID, A.QUESTIONAIRE_OBJECT, A.APPLY_CODE, A.SURVEY_QUESTION_ID, 
			A.CUSTOMER_SURVEY_ID, A.POLICY_CODE, A.POLICY_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CUSTOMER_SURVEY_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllQuestionaireCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_MODULE_RESULT, A.CUSTOMER_ID, A.QUESTIONAIRE_OBJECT, A.APPLY_CODE, A.SURVEY_QUESTION_ID, 
			A.CUSTOMER_SURVEY_ID, A.POLICY_CODE, A.POLICY_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<include refid="questionaireCustomerWhereCondition" />
		<![CDATA[ ORDER BY A.CUSTOMER_SURVEY_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findQuestionaireCustomerTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) APP___PAS__DBUSER.FROM T_QUESTIONAIRE_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="questionaireCustomerWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryQuestionaireCustomerForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SURVEY_MODULE_RESULT, B.CUSTOMER_ID, B.QUESTIONAIRE_OBJECT, B.APPLY_CODE, B.SURVEY_QUESTION_ID, 
			B.CUSTOMER_SURVEY_ID, B.POLICY_CODE, B.POLICY_ID, 
			B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.SURVEY_MODULE_RESULT, A.CUSTOMER_ID, A.QUESTIONAIRE_OBJECT, A.APPLY_CODE, A.SURVEY_QUESTION_ID, 
			A.CUSTOMER_SURVEY_ID, A.POLICY_CODE, A.POLICY_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CUSTOMER_SURVEY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
			<!--客户告知接口查询 -->
	<select id="findHealthImpart" resultType="java.util.Map" parameterType="java.util.Map">
	
	 <![CDATA[ select * from ]]>
		<![CDATA[ (SELECT rownum rn, A.POLICY_CODE, A.Survey_Module_Result,B.SURVEY_VERSION, C.SURVEY_VERSION_NAME, A.CUSTOMER_ID, B.SURVEY_CODE, B.QUESTION_CONTENT, B.SURVEY_PARAM_MODULE
					FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER A
					LEFT OUTER JOIN APP___PAS__DBUSER.T_QUESTIONAIRE_INFO B ON A.SURVEY_QUESTION_ID=B.SURVEY_QUESTION_ID
					LEFT OUTER JOIN APP___PAS__DBUSER.T_QUESTIONAIRE_SURVEY_VERSION C ON B.SURVEY_VERSION=C.SURVEY_VERSION_CODE
		            WHERE A.POLICY_id=(select E.POLICY_ID from APP___PAS__DBUSER.t_contract_master E WHERE E.POLICY_CODE =
		            #{policy_code})  
		            ]]>
		    <if test="customerNo != null and customerNo != ''"> 
		           <![CDATA[  AND A.CUSTOMER_ID = #{customerNo}]]>
		    </if>
		    
		           <![CDATA[  AND trim(B.SURVEY_VERSION)in ('02','22','33','42','52','80','81')]]>
		   
		   
		    <![CDATA[  order by A.POLICY_CODE,A.CUSTOMER_ID) where 1=1 ]]>
		    
		      <if test="end !=null and end != '' and start !=null and start != ''"> 
		           <![CDATA[  AND rn<=#{end} and rn>=#{start}]]>
		    </if>
	</select>
	
	<!-- 查询条件下健康告知操作 -->
	<select id="findAllCustomerHealthImpartByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.CUSTOMER_ID,
       A.SURVEY_MODULE_RESULT,
       A.APPLY_CODE,
       A.POLICY_CODE,
       B.SURVEY_VERSION,
       B.SURVEY_CODE,
       B.QUESTION_CONTENT,
       C.APPLY_DATE,
       C.LIABILITY_STATE,
       D.CUSTOMER_NAME
  FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER A,
       APP___PAS__DBUSER.T_QUESTIONAIRE_INFO     B,
       APP___PAS__DBUSER.T_CONTRACT_MASTER       C,
       APP___PAS__DBUSER.T_CUSTOMER              D
 WHERE A.APPLY_CODE = #{apply_code}
   AND A.CUSTOMER_ID = #{customer_id}
   AND B.SURVEY_QUESTION_ID = A.SURVEY_QUESTION_ID
   AND C.POLICY_ID = A.POLICY_ID
   AND D.CUSTOMER_ID = A.CUSTOMER_ID
		]]> 
	</select>
	<!-- 保全增补告知用 -->
		<delete id="deleteQuestionaireCustomerByCs" parameterType="java.util.Map">
		<![CDATA[ 
			delete APP___PAS__DBUSER.t_questionaire_customer qc
			 where qc.customer_survey_id in
			       (select qc1.customer_survey_id
			          from APP___PAS__DBUSER.t_questionaire_customer qc1,APP___PAS__DBUSER.t_questionaire_info qi,APP___PAS__DBUSER.t_questionaire_survey_version qsv
			          where 
			           qc1.survey_question_id = qi.survey_question_id
			          and qi.survey_version =  qsv.survey_version_code
			          and qsv.survey_version_code = ${survey_version_code}
			          and qc1.policy_id = ${policy_id}
          			)
		 ]]>
	</delete>
	
	
	<!-- 根据保单查询该保单号是否存在异常告知 -->
	<select id="findAbnormalnotification" resultType="java.util.Map" parameterType="java.util.Map">
<!-- 	<![CDATA[
	   SELECT a.survey_version, a.survey_code, b.*
       FROM DEV_PAS.t_Questionaire_Customer b, DEV_PAS.T_QUESTIONAIRE_INFO a
       WHERE a.survey_question_id = b.survey_question_id
       and a.survey_version in ('19', '20', '52','65','25')
       and a.survey_code not in ('022', '023', '024', '025')
       and b.survey_module_result like '%是%'
       and policy_code = #{policy_code}
       ]]> -->
        <if test=" flag  != null and flag !='' and flag == '0'.toString() "> 
       <![CDATA[ 

   SELECT a.survey_version, a.survey_code
             FROM DEV_PAS.t_Questionaire_Customer b, DEV_PAS.T_QUESTIONAIRE_INFO a
           WHERE a.survey_question_id = b.survey_question_id
           and (a.survey_version in ('19','52','65') 
              or (a.survey_version='20' and a.survey_code not in ('022', '023', '024', '025'))
              or (a.survey_version='25' and a.survey_code in ('004', '005', '006', '007', 
                  '008', '009', '010', '011', '012', '012B', '013', '015', '016', '017', '018', '024'))
              or
          (a.survey_version = '103' and EXISTS
           (SELECT 1
            FROM DEV_PAS.T_CONTRACT_BUSI_PROD     P,
                 DEV_PAS.T_CONTRACT_PRODUCT_OTHER O
           WHERE O.POLICY_ID = P.POLICY_ID
             AND O.BUSI_ITEM_ID = P.BUSI_ITEM_ID
             AND P.BUSI_PROD_CODE = '00991000'
             AND O.FIELD1 IN ('1', '2')
             AND P.POLICY_CODE = B.POLICY_CODE))
             or (a.survey_version in('107','108','109') and a.survey_code='001'))
           and b.survey_module_result like '%是%'
           and policy_code =  #{policy_code}  ]]>
           <if test=" customer_id  != null "><![CDATA[ AND b.CUSTOMER_ID = #{customer_id} ]]></if>
       </if>
       
       <if test=" flag  != null and flag !='' and flag == '1'.toString() "> 
       <![CDATA[ 
       SELECT M.SURVEY_VERSION,M.SURVEY_CODE,M.bmi1,M.bmi2 FROM (
SELECT A.SURVEY_VERSION,
       A.SURVEY_CODE,
       B.SURVEY_MODULE_RESULT,
       SUBSTR(B.SURVEY_MODULE_RESULT,
              INSTR(B.SURVEY_MODULE_RESULT, ',') + 1) BMI1,
       POWER(SUBSTR(B.SURVEY_MODULE_RESULT,
                    1,
                    INSTR(B.SURVEY_MODULE_RESULT, ',') - 1) / 100,
             2) BMI2
  FROM DEV_PAS.T_QUESTIONAIRE_CUSTOMER B,
       DEV_PAS.T_QUESTIONAIRE_INFO     A,
       DEV_PAS.T_CUSTOMER              C
 WHERE A.SURVEY_QUESTION_ID = B.SURVEY_QUESTION_ID
   AND B.CUSTOMER_ID = C.CUSTOMER_ID
      AND (TRUNC(MONTHS_BETWEEN(SYSDATE,C.CUSTOMER_BIRTHDAY)/12)) >=18 
   AND ((A.SURVEY_VERSION IN
       ('65', '20', '19', '25', '52', '22', '32', '42') AND
       A.SURVEY_CODE = '000') OR
       (A.SURVEY_VERSION IN ('102') AND A.SURVEY_CODE = '030'))
   AND B.SURVEY_MODULE_RESULT IS NOT NULL AND B.POLICY_CODE =  #{policy_code}  ]]>
           <if test=" customer_id  != null "><![CDATA[ AND b.CUSTOMER_ID = #{customer_id} ]]></if>
         <![CDATA[   ) M 
       ]]> </if>
	</select>
	
	
	<!-- 查询投被保人年收入 -->
	<select id="findQuestionaireCustomerByTB" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			   SELECT QC.SURVEY_MODULE_RESULT
		  FROM dev_pas.T_QUESTIONAIRE_CUSTOMER QC
		 WHERE QC.SURVEY_QUESTION_ID IN (833, 564, 1093)
		   ]]>
		   <if test=" flag == 'one' "><![CDATA[ AND QC.QUESTIONAIRE_OBJECT = '1'  ]]></if> 
		   <if test=" flag == 'zero' "><![CDATA[ AND QC.QUESTIONAIRE_OBJECT = '2'  ]]></if> 
	<![CDATA[
		   AND QC.CUSTOMER_ID = #{customer_id}
       ]]>
	</select>
</mapper>
