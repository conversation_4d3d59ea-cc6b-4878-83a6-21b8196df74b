<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.TaskCashValueBDaoImpl">

	<!-- 添加操作 -->
	<insert id="PA_addTaskCashValueB"  useGeneratedKeys="false"  parameterType="java.util.Map">
				<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TASK_CASH_VALUE_B__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TASK_CASH_VALUE_B(
				LIST_<PERSON>,BATCH_NO,FILE_NAME,BUSI_TYPE,BUSI_NO,ORGAN_CODE,APPLY_CODE,POLICY_CODE,PROCESS_STATUS,PROCESS_TIMES,
				FAIL_TIMES,FAIL_REASON,
				FIELD_1,FIELD_2,FIELD_3,FIELD_4,FIELD_5,FIELD_6,FIELD_7,FIELD_8,FIELD_9,FIELD_10,INSERT_BY,UPDATE_BY,
				INSERT_TIME,INSERT_TIMESTAMP,UPDATE_TIME,UPDATE_TIMESTAMP) 
			VALUES (
			    #{list_id, jdbcType=NUMERIC},#{batch_no, jdbcType=VARCHAR},#{file_name, jdbcType=VARCHAR},#{busi_type, jdbcType=VARCHAR},
			    #{busi_no, jdbcType=VARCHAR},#{organ_code, jdbcType=VARCHAR},#{apply_code, jdbcType=VARCHAR},#{policy_code, jdbcType=VARCHAR},
			    #{process_status, jdbcType=VARCHAR},#{process_times, jdbcType=NUMERIC},#{fail_times, jdbcType=NUMERIC},#{fail_reason, jdbcType=VARCHAR},
			    #{field_1, jdbcType=VARCHAR},#{field_2, jdbcType=VARCHAR},
			    #{field_3, jdbcType=VARCHAR},#{field_4, jdbcType=VARCHAR},#{field_5, jdbcType=VARCHAR},#{field_6, jdbcType=VARCHAR},
			    #{field_7, jdbcType=VARCHAR},#{field_8, jdbcType=VARCHAR},#{field_9, jdbcType=VARCHAR},#{field_10, jdbcType=VARCHAR},
				#{insert_by, jdbcType=NUMERIC},
				#{update_by, jdbcType=NUMERIC},
				SYSDATE,
				CURRENT_TIMESTAMP,
				SYSDATE,
				CURRENT_TIMESTAMP
				)
		 ]]>
	</insert>
</mapper>
