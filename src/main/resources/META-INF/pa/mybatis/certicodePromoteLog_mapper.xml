<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICerticodePromoteLogDao">
<!--
	<sql id="certicodePromoteLogWhereCondition">
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" certi_code_bf != null and certi_code_bf != ''  "><![CDATA[ AND A.CERTI_CODE_BF = #{certi_code_bf} ]]></if>
		<if test=" certi_code_af != null and certi_code_af != ''  "><![CDATA[ AND A.CERTI_CODE_AF = #{certi_code_af} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" policy_codes != null and policy_codes != ''  "><![CDATA[ AND A.POLICY_CODES = #{policy_codes} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" promote_date  != null  and  promote_date  != ''  "><![CDATA[ AND A.PROMOTE_DATE = #{promote_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryCerticodePromoteLogByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCerticodePromoteLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
				 SELECT APP___PAS__DBUSER.s_Certicode_Promote_Log.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG (
				CUSTOMER_CERT_TYPE, CUSTOMER_NAME, CERTI_CODE_BF, CERTI_CODE_AF, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, 
				POLICY_CODES, INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, PROMOTE_DATE ) 
			VALUES (
				#{customer_cert_type, jdbcType=VARCHAR}, #{customer_name, jdbcType=VARCHAR} , #{certi_code_bf, jdbcType=VARCHAR} , #{certi_code_af, jdbcType=VARCHAR} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE 
				, #{policy_codes, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{promote_date, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCerticodePromoteLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCerticodePromoteLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			CERTI_CODE_BF = #{certi_code_bf, jdbcType=VARCHAR} ,
			CERTI_CODE_AF = #{certi_code_af, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODES = #{policy_codes, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    PROMOTE_DATE = #{promote_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCerticodePromoteLogByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CERTI_CODE_BF, A.CERTI_CODE_AF, A.CUSTOMER_ID, 
			A.POLICY_CODES, A.LIST_ID, A.PROMOTE_DATE FROM APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG A WHERE 1 = 1  ]]>
		<include refid="queryCerticodePromoteLogByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCerticodePromoteLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CERTI_CODE_BF, A.CERTI_CODE_AF, A.CUSTOMER_ID, 
			A.POLICY_CODES, A.LIST_ID, A.PROMOTE_DATE FROM APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCerticodePromoteLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CERTI_CODE_BF, A.CERTI_CODE_AF, A.CUSTOMER_ID, 
			A.POLICY_CODES, A.LIST_ID, A.PROMOTE_DATE FROM APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCerticodePromoteLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCerticodePromoteLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_CERT_TYPE, B.CUSTOMER_NAME, B.CERTI_CODE_BF, B.CERTI_CODE_AF, B.CUSTOMER_ID, 
			B.POLICY_CODES, B.LIST_ID, B.PROMOTE_DATE FROM (
					SELECT ROWNUM RN, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CERTI_CODE_BF, A.CERTI_CODE_AF, A.CUSTOMER_ID, 
			A.POLICY_CODES, A.LIST_ID, A.PROMOTE_DATE FROM APP___PAS__DBUSER.T_CERTICODE_PROMOTE_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
