<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRossInfoDao">

	<sql id="rossInfoWhereCondition">
		<if test="year != null "><![CDATA[ AND A.YEAR = #{year}]]></if>
		<if test="season != null "><![CDATA[ AND A.SEASON = #{season}]]></if>
	</sql>
	
	
	
	 
	
	<select id="findRossInfo" resultType="java.util.Map" parameterType="java.util.Map" >
	
		SELECT A.LIST_ID,A.YEAR,A.SEASON,A.SOLVENCY_RATIO,A.RISK_LEVEL,
		A.INSERT_BY,A.INSERT_TIME,A.INSERT_TIMESTAMP,
		A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP
	    FROM APP___PAS__DBUSER.T_C_ROSS_INFO A WHERE 1=1
		<include refid="rossInfoWhereCondition"/>
	
	</select>
	<select id="findRossInfoAgain" resultType="java.util.Map" parameterType="java.util.Map">
	    SELECT a.year SOLVENCY_YEAR,
          a.season SOLVENCY_QUARTER,
          a.SOLVENCY_RATIO * 100 || '%' ADEQUACY_RATIO,
          b.YEAR RISK_YEAR,
          b.SEASON RISK_QUARTER,
          b.RISK_LEVEL RISK_RATING
     FROM (SELECT M.YEAR, M.SEASON, M.SOLVENCY_RATIO
             FROM (SELECT T.YEAR, T.SEASON, T.SOLVENCY_RATIO
                     FROM APP___PAS__DBUSER.T_C_ROSS_INFO T
                    WHERE T.SOLVENCY_RATIO IS NOT NULL
                      AND T.YEAR =
                          (SELECT MAX(TC.YEAR)
                             FROM APP___PAS__DBUSER.T_C_ROSS_INFO TC
                            WHERE TC.SOLVENCY_RATIO IS NOT NULL)
                    ORDER BY T.SEASON DESC) M
            WHERE ROWNUM = 1) A,
          (SELECT M.YEAR, M.SEASON, M.RISK_LEVEL
             FROM (SELECT T.YEAR, T.SEASON, T.RISK_LEVEL
                     FROM APP___PAS__DBUSER.T_C_ROSS_INFO T
                    WHERE T.RISK_LEVEL IS NOT NULL
                      AND T.YEAR =
                          (SELECT MAX(TC.YEAR)
                             FROM APP___PAS__DBUSER.T_C_ROSS_INFO TC
                            WHERE TC.RISK_LEVEL IS NOT NULL)
                    ORDER BY T.SEASON DESC) M
            WHERE ROWNUM = 1) B
	</select>
	
</mapper>
