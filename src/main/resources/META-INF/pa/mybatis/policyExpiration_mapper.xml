<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.impl.PolicyExpirationDaoImpl">
    <sql id="PA_findPolicyExpirationByPolicyCodeCondition">
		<if test=" policy_code  != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
<select id="PA_findAllPolicyExpiration" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ select * from APP___PAS__DBUSER.t_contract_master A where A.end_cause=01
	 ]]>
		<include refid="PA_findPolicyExpirationByPolicyCodeCondition"></include>
		</select>
</mapper>