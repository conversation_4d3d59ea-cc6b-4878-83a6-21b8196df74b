<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFundAssetsLogDao">
<!--
	<sql id="PA_fundAssetsLogWhereCondition">
		<if test=" confirm_remark != null and confirm_remark != ''  "><![CDATA[ AND A.CONFIRM_REMARK = #{confirm_remark} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" account_value_net_b  != null "><![CDATA[ AND A.ACCOUNT_VALUE_NET_B = #{account_value_net_b} ]]></if>
		<if test=" confirm_time  != null  and  confirm_time  != ''  "><![CDATA[ AND A.CONFIRM_TIME = #{confirm_time} ]]></if>
		<if test=" tax_amount  != null "><![CDATA[ AND A.TAX_AMOUNT = #{tax_amount} ]]></if>
		<if test=" upload_date  != null  and  upload_date  != ''  "><![CDATA[ AND A.UPLOAD_DATE = #{upload_date} ]]></if>
		<if test=" assets_deposit_id  != null "><![CDATA[ AND A.ASSETS_DEPOSIT_ID = #{assets_deposit_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" profit_loss_amout  != null "><![CDATA[ AND A.PROFIT_LOSS_AMOUT = #{profit_loss_amout} ]]></if>
		<if test=" invest_account_id  != null "><![CDATA[ AND A.INVEST_ACCOUNT_ID = #{invest_account_id} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" asset_m_fee  != null "><![CDATA[ AND A.ASSET_M_FEE = #{asset_m_fee} ]]></if>
		<if test=" account_value  != null "><![CDATA[ AND A.ACCOUNT_VALUE = #{account_value} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" confirmer_id  != null "><![CDATA[ AND A.CONFIRMER_ID = #{confirmer_id} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" tax_rate  != null "><![CDATA[ AND A.TAX_RATE = #{tax_rate} ]]></if>
		<if test=" evaluation_date  != null  and  evaluation_date  != ''  "><![CDATA[ AND A.EVALUATION_DATE = #{evaluation_date} ]]></if>
		<if test=" confirm_result  != null "><![CDATA[ AND A.CONFIRM_RESULT = #{confirm_result} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" account_value_net  != null "><![CDATA[ AND A.ACCOUNT_VALUE_NET = #{account_value_net} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryFundAssetsLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addFundAssetsLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="policy_chg_id">
			SELECT APP___PAS__DBUSER.S_FUND_ASSETS_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_ASSETS_LOG(
				CONFIRM_REMARK, BATCH_NO, ACCOUNT_VALUE_NET_B, CONFIRM_TIME, TAX_AMOUNT, INSERT_TIMESTAMP, UPLOAD_DATE, 
				UPDATE_BY, ASSETS_DEPOSIT_ID, LIST_ID, POLICY_CHG_ID, PROFIT_LOSS_AMOUT, INVEST_ACCOUNT_ID, INVEST_ACCOUNT_CODE, 
				ASSET_M_FEE, INSERT_TIME, UPDATE_TIME, ACCOUNT_VALUE, LOG_ID, CONFIRMER_ID, BANK_CODE, 
				TAX_RATE, EVALUATION_DATE, CONFIRM_RESULT, UPDATE_TIMESTAMP, LOG_TYPE, ACCOUNT_VALUE_NET, INSERT_BY ) 
			VALUES (
				#{confirm_remark, jdbcType=VARCHAR}, #{batch_no, jdbcType=VARCHAR} , #{account_value_net_b, jdbcType=NUMERIC} , #{confirm_time, jdbcType=DATE} , #{tax_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{upload_date, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{assets_deposit_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{profit_loss_amout, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} , #{invest_account_code, jdbcType=VARCHAR} 
				, #{asset_m_fee, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{account_value, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} , #{confirmer_id, jdbcType=NUMERIC} , #{bank_code, jdbcType=VARCHAR} 
				, #{tax_rate, jdbcType=NUMERIC} , #{evaluation_date, jdbcType=DATE} , #{confirm_result, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{account_value_net, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteFundAssetsLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_ASSETS_LOG WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateFundAssetsLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_ASSETS_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			CONFIRM_REMARK = #{confirm_remark, jdbcType=VARCHAR} ,
			BATCH_NO = #{batch_no, jdbcType=VARCHAR} ,
		    ACCOUNT_VALUE_NET_B = #{account_value_net_b, jdbcType=NUMERIC} ,
		    CONFIRM_TIME = #{confirm_time, jdbcType=DATE} ,
		    TAX_AMOUNT = #{tax_amount, jdbcType=NUMERIC} ,
		    UPLOAD_DATE = #{upload_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    ASSETS_DEPOSIT_ID = #{assets_deposit_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    PROFIT_LOSS_AMOUT = #{profit_loss_amout, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
		    ASSET_M_FEE = #{asset_m_fee, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ACCOUNT_VALUE = #{account_value, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    CONFIRMER_ID = #{confirmer_id, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    TAX_RATE = #{tax_rate, jdbcType=NUMERIC} ,
		    EVALUATION_DATE = #{evaluation_date, jdbcType=DATE} ,
		    CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    ACCOUNT_VALUE_NET = #{account_value_net, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findFundAssetsLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.POLICY_CHG_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.INVEST_ACCOUNT_CODE, 
			A.ASSET_M_FEE, A.ACCOUNT_VALUE, A.LOG_ID, A.CONFIRMER_ID, A.BANK_CODE, 
			A.TAX_RATE, A.EVALUATION_DATE, A.CONFIRM_RESULT, A.LOG_TYPE, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundAssetsLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapFundAssetsLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.POLICY_CHG_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.INVEST_ACCOUNT_CODE, 
			A.ASSET_M_FEE, A.ACCOUNT_VALUE, A.LOG_ID, A.CONFIRMER_ID, A.BANK_CODE, 
			A.TAX_RATE, A.EVALUATION_DATE, A.CONFIRM_RESULT, A.LOG_TYPE, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllFundAssetsLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.POLICY_CHG_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.INVEST_ACCOUNT_CODE, 
			A.ASSET_M_FEE, A.ACCOUNT_VALUE, A.LOG_ID, A.CONFIRMER_ID, A.BANK_CODE, 
			A.TAX_RATE, A.EVALUATION_DATE, A.CONFIRM_RESULT, A.LOG_TYPE, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findFundAssetsLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_ASSETS_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryFundAssetsLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CONFIRM_REMARK, B.BATCH_NO, B.ACCOUNT_VALUE_NET_B, B.CONFIRM_TIME, B.TAX_AMOUNT, B.UPLOAD_DATE, 
			B.ASSETS_DEPOSIT_ID, B.LIST_ID, B.POLICY_CHG_ID, B.PROFIT_LOSS_AMOUT, B.INVEST_ACCOUNT_ID, B.INVEST_ACCOUNT_CODE, 
			B.ASSET_M_FEE, B.ACCOUNT_VALUE, B.LOG_ID, B.CONFIRMER_ID, B.BANK_CODE, 
			B.TAX_RATE, B.EVALUATION_DATE, B.CONFIRM_RESULT, B.LOG_TYPE, B.ACCOUNT_VALUE_NET FROM (
					SELECT ROWNUM RN, A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.POLICY_CHG_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.INVEST_ACCOUNT_CODE, 
			A.ASSET_M_FEE, A.ACCOUNT_VALUE, A.LOG_ID, A.CONFIRMER_ID, A.BANK_CODE, 
			A.TAX_RATE, A.EVALUATION_DATE, A.CONFIRM_RESULT, A.LOG_TYPE, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
