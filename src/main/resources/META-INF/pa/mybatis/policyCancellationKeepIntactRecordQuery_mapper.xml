<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyCancellationKeepIntactRecordQueryDao">
	<select id="PA_queryAppStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    	select APP_STATUS from dev_pas.T_CS_APPLICATION
	    	where CHANGE_ID = #{change_id}
    ]]>
	</select>

	<select id="PA_queryAppntName" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select tc.customer_name as name
            from DEV_PAS.T_CONTRACT_MASTER tcm,
                 DEV_PAS.T_POLICY_HOLDER ph,
                 DEV_PAS.T_CUSTOMER tc
            where tcm.policy_code = ph.policy_code
              and ph.customer_id = tc.customer_id
              and tcm.policy_code = #{policy_code}
    ]]>
	</select>
	
		<select id="PA_queryInsuredName" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			SELECT customer_name as name FROM DEV_PAS.T_CUSTOMER WHERE 
       customer_id=(select B.customer_id from dev_pas.T_BENEFIT_INSURED A,dev_pas.T_INSURED_LIST B
       where A.ORDER_ID = 1 AND  A.POLICY_CODE = B.POLICY_CODE and A.INSURED_ID = B.LIST_ID AND ROWNUM = 1
             and A.POLICY_CODE=#{policy_code})
    ]]>
	</select>
	
			<select id="PA_queryRecord" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select a.apply_time,b.accept_status,b.review_view from dev_pas.T_CS_APPLICATION a 
			left join dev_pas.T_CS_ACCEPT_CHANGE b on a.CHANGE_ID = b.CHANGE_ID 
           where a.CHANGE_ID=#{change_id}
           and b.ACCEPT_CODE=#{accept_code}
    ]]>
	</select>
	
	<select id="PA_queryChargeOrPayState" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select DISTINCT (t.POLICY_CODE),
                t.fee_status,
                t.pay_mode,
                p.name,
                t.bank_code,
                t.bank_user_name,
                t.bank_account,
                t.arap_flag
  from dev_pas.T_PREM_ARAP
  t left join dev_pas.T_PAY_MODE p on t.pay_mode = p.code
 where t.POLICY_CODE = #{policy_code}
 		and t.BUSINESS_CODE=#{accept_code}
    ]]>
	</select>
	
		<select id="PA_queryChargeOrPayStateCS" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select DISTINCT (t.POLICY_CODE),
                t.fee_status,
                t.pay_mode,
                p.name,
                t.bank_code,
                t.bank_user_name,
                t.bank_account,
                t.arap_flag
  from dev_pas.T_CS_PREM_ARAP
  t left join dev_pas.T_PAY_MODE p on t.pay_mode = p.code
 where t.POLICY_CODE = #{policy_code}
 		and t.BUSINESS_CODE=#{accept_code}
    ]]>
	</select>
	
	<select id="PA_queryPLFundsRtnCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select DISTINCT (t.POLICY_CODE),
                t.funds_rtn_code
  from dev_pas.T_PREM_ARAP
   t left join dev_pas.T_PAY_MODE p on t.pay_mode = p.code
 where t.POLICY_CODE = #{policy_code} and t.BUSINESS_CODE=#{accept_code}
    ]]>
	</select>
	
	<select id="PA_queryPolicyDatail" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[ 
	 select b.BUSI_ITEM_ID,/*险种ID*/
       b.BUSI_PRD_ID,/*产品ID*/
       b.BUSI_PROD_CODE,/*产品代码*/
        c.PRODUCT_ABBR_NAME,/*险种简称*/
       b.MASTER_BUSI_ITEM_ID,/*所属主险险种ID*/
       a.COST_FEE,/*工本费*/
       a.surrender_amount/*正常退保总金额*/
  from dev_pas.T_SURRENDER a
  left join dev_pas.t_Contract_Busi_Prod b
    on a.BUSI_ITEM_ID = b.BUSI_ITEM_ID
  left join dev_pas.t_business_product c
    on b.BUSI_PRD_ID = c.BUSINESS_PRD_ID
 where a.BUSI_ITEM_ID = #{busi_item_id} and 
  ROWNUM < 2000
    ]]>
	</select>
	
	<select id="PA_queryFail" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	 select b.bank_ret_code,B.bank_ret_name from dev_pas.T_BANK_RET_CONF B
 			where b.bank_ret_code= #{funds_rtn_code}
    ]]>
	</select>
	
	<select id="queryBankName" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			SELECT DISTINCT(t.cip_district_bank_code),bod.bank_name
        FROM dev_pas.T_CS_PREM_ARAP
         T
        LEFT JOIN dev_pas.T_CS_ACCEPT_CHANGE CAC
          ON CAC.ACCEPT_CODE = T.BUSINESS_CODE
        left join dev_pas.T_BANK bod
          on bod.bank_code = t.bank_code  
			  WHERE CAC.ACCEPT_CODE=#{accept_code}
    ]]>
	</select>
	
		<select id="PA_queryCountFor928" resultType="java.lang.Integer"  parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1)
			  FROM dev_pas.T_CS_CONTRACT_BUSI_PROD A,
			       dev_pas.T_CS_POLICY_CHANGE    B
			 WHERE A.CHANGE_ID = B.CHANGE_ID
			   AND A.POLICY_CHG_ID=B.POLICY_CHG_ID
			   AND A.BUSI_PROD_CODE IN ('********','********')
			   and A.BUSI_ITEM_ID=#{busi_item_id}
		 ]]>		
	</select>
		
	<select id="PA_queryCSFeeAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select fee_amount from dev_pas.T_cs_PREM_ARAP where BUSINESS_CODE=#{accept_code}
   ]]>
	</select>
	
	<select id="PA_queryFeeAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select fee_amount from dev_pas.T_PREM_ARAP where BUSINESS_CODE=#{accept_code}
   ]]>
	</select>
	
	<select id="PA_querySurrenderList" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select pc.change_id,pc.accept_id,ph.customer_id
            from DEV_PAS.T_CONTRACT_MASTER tcm,
                 DEV_PAS.T_POLICY_HOLDER ph,
                 DEV_PAS.T_CS_POLICY_CHANGE pc,
                 DEV_PAS.T_CS_ACCEPT_CHANGE AC
            where tcm.policy_code = ph.policy_code
            and pc.policy_code = ph.policy_code
            and ac.ACCEPT_ID=pc.ACCEPT_ID
            and ph.policy_code=#{policy_code}
            and ac.ACCEPT_CODE=#{accept_code}
    ]]>
	</select>
	
		<select id="PA_queryServiceCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select service_code
  from DEV_PAS.T_CS_ACCEPT_CHANGE
 where service_code in ('CT', 'EA', 'XT')
   and ACCEPT_CODE = #{accept_code}
   ]]>
	</select>
	
		<select id="PA_queryTBChangeId" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select change_id from DEV_PAS.T_CS_POLICY_CHANGE where POLICY_CODE = #{policy_code} and accept_id=(select accept_id from 
	     DEV_PAS.T_CS_ACCEPT_CHANGE where service_code in ('CT','EA','XT') and ACCEPT_CODE = #{accept_code})
   ]]>
	</select>

</mapper>
