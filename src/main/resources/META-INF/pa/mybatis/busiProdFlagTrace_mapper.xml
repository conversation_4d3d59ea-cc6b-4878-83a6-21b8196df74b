<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBusiProdFlagTraceDao">

	<sql id="busiProdFlagTraceWhereCondition">
		<if test=" busi_scene_code != null and busi_scene_code != ''  "><![CDATA[ AND A.BUSI_SCENE_CODE = #{busi_scene_code} ]]></if>
		<if test=" new_apply_code != null and new_apply_code != ''  "><![CDATA[ AND A.NEW_APPLY_CODE = #{new_apply_code} ]]></if>
		<if test=" switch_date  != null  and  switch_date  != ''  "><![CDATA[ AND A.SWITCH_DATE = #{switch_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" can_reinsure_flag  != null "><![CDATA[ AND A.CAN_REINSURE_FLAG = #{can_reinsure_flag} ]]></if>
		<if test=" new_policy_code != null and new_policy_code != ''  "><![CDATA[ AND A.NEW_POLICY_CODE = #{new_policy_code} ]]></if>
		<if test=" reinsured != null and reinsured != ''  "><![CDATA[ AND A.REINSURED = #{reinsured} ]]></if>
		<if test=" change_time  != null  and  change_time  != ''  "><![CDATA[ AND A.CHANGE_TIME = #{change_time} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" new_policy_id  != null "><![CDATA[ AND A.NEW_POLICY_ID = #{new_policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" accept_change != null and accept_change != ''  "><![CDATA[ AND A.ACCEPT_CHANGE = #{accept_change} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" can_change_flag  != null "><![CDATA[ AND A.CAN_CHANGE_FLAG = #{can_change_flag} ]]></if>
		<if test=" new_busi_prod_code != null and new_busi_prod_code != ''  "><![CDATA[ AND A.NEW_BUSI_PROD_CODE = #{new_busi_prod_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryBusiProdFlagTraceByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addBusiProdFlagTrace"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_BUSI_PROD_FL_TR__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE(
				BUSI_SCENE_CODE, NEW_APPLY_CODE, INSERT_TIME, SWITCH_DATE, UPDATE_TIME, BUSI_PROD_CODE, INSERT_TIMESTAMP, 
				POLICY_CODE, CAN_REINSURE_FLAG, UPDATE_BY, NEW_POLICY_CODE, REINSURED, CHANGE_TIME, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY, NEW_POLICY_ID, BUSI_ITEM_ID, ACCEPT_CHANGE, POLICY_ID, CAN_CHANGE_FLAG, 
				NEW_BUSI_PROD_CODE ) 
			VALUES (
				#{busi_scene_code, jdbcType=VARCHAR}, #{new_apply_code, jdbcType=VARCHAR} , SYSDATE , #{switch_date, jdbcType=DATE} , SYSDATE , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{policy_code, jdbcType=VARCHAR} , #{can_reinsure_flag, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{new_policy_code, jdbcType=VARCHAR} , #{reinsured, jdbcType=VARCHAR} , #{change_time, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{new_policy_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{accept_change, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{can_change_flag, jdbcType=NUMERIC} 
				, #{new_busi_prod_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteBusiProdFlagTrace" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateBusiProdFlagTrace" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE ]]>
		<set>
		<trim suffixOverrides=",">
			BUSI_SCENE_CODE = #{busi_scene_code, jdbcType=VARCHAR} ,
			NEW_APPLY_CODE = #{new_apply_code, jdbcType=VARCHAR} ,
		    SWITCH_DATE = #{switch_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    CAN_REINSURE_FLAG = #{can_reinsure_flag, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			NEW_POLICY_CODE = #{new_policy_code, jdbcType=VARCHAR} ,
			REINSURED = #{reinsured, jdbcType=VARCHAR} ,
		    CHANGE_TIME = #{change_time, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    NEW_POLICY_ID = #{new_policy_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
			ACCEPT_CHANGE = #{accept_change, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    CAN_CHANGE_FLAG = #{can_change_flag, jdbcType=NUMERIC} ,
			NEW_BUSI_PROD_CODE = #{new_busi_prod_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 查询单条操作 -->	
	<select id="findBusiProdFlagTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_SCENE_CODE, A.NEW_APPLY_CODE, A.SWITCH_DATE, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.CAN_REINSURE_FLAG, A.NEW_POLICY_CODE, A.REINSURED, A.CHANGE_TIME, A.LIST_ID, 
			A.NEW_POLICY_ID, A.BUSI_ITEM_ID, A.ACCEPT_CHANGE, A.POLICY_ID, A.CAN_CHANGE_FLAG, 
			A.NEW_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE A WHERE 1 = 1  ]]>
		<include refid="busiProdFlagTraceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
<!-- 按索引查询操作 -->	
	<select id="findBusiProdFlagTraceByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_SCENE_CODE, A.NEW_APPLY_CODE, A.SWITCH_DATE, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.CAN_REINSURE_FLAG, A.NEW_POLICY_CODE, A.REINSURED, A.CHANGE_TIME, A.LIST_ID, 
			A.NEW_POLICY_ID, A.BUSI_ITEM_ID, A.ACCEPT_CHANGE, A.POLICY_ID, A.CAN_CHANGE_FLAG, 
			A.NEW_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE A WHERE 1 = 1  ]]>
		<include refid="queryBusiProdFlagTraceByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapBusiProdFlagTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_SCENE_CODE, A.NEW_APPLY_CODE, A.SWITCH_DATE, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.CAN_REINSURE_FLAG, A.NEW_POLICY_CODE, A.REINSURED, A.CHANGE_TIME, A.LIST_ID, 
			A.NEW_POLICY_ID, A.BUSI_ITEM_ID, A.ACCEPT_CHANGE, A.POLICY_ID, A.CAN_CHANGE_FLAG, 
			A.NEW_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE A WHERE ROWNUM <=  1000  ]]>
		<include refid="busiProdFlagTraceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllBusiProdFlagTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_SCENE_CODE, A.NEW_APPLY_CODE, A.SWITCH_DATE, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.CAN_REINSURE_FLAG, A.NEW_POLICY_CODE, A.REINSURED, A.CHANGE_TIME, A.LIST_ID, 
			A.NEW_POLICY_ID, A.BUSI_ITEM_ID, A.ACCEPT_CHANGE, A.POLICY_ID, A.CAN_CHANGE_FLAG, 
			A.NEW_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE A WHERE ROWNUM <=  1000  ]]>
		<include refid="busiProdFlagTraceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findBusiProdFlagTraceTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE A WHERE 1 = 1  ]]>
		<include refid="busiProdFlagTraceWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryBusiProdFlagTraceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSI_SCENE_CODE, B.NEW_APPLY_CODE, B.SWITCH_DATE, B.BUSI_PROD_CODE, 
			B.POLICY_CODE, B.CAN_REINSURE_FLAG, B.NEW_POLICY_CODE, B.REINSURED, B.CHANGE_TIME, B.LIST_ID, 
			B.NEW_POLICY_ID, B.BUSI_ITEM_ID, B.ACCEPT_CHANGE, B.POLICY_ID, B.CAN_CHANGE_FLAG, 
			B.NEW_BUSI_PROD_CODE FROM (
					SELECT ROWNUM RN, A.BUSI_SCENE_CODE, A.NEW_APPLY_CODE, A.SWITCH_DATE, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.CAN_REINSURE_FLAG, A.NEW_POLICY_CODE, A.REINSURED, A.CHANGE_TIME, A.LIST_ID, 
			A.NEW_POLICY_ID, A.BUSI_ITEM_ID, A.ACCEPT_CHANGE, A.POLICY_ID, A.CAN_CHANGE_FLAG, 
			A.NEW_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_BUSI_PROD_FLAG_TRACE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="busiProdFlagTraceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
