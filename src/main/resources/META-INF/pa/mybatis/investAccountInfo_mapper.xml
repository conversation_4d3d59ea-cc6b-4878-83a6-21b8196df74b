<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IInvestAccountInfoDao">
	<sql id="investAccountInfoWhereCondition">
		<if test=" price_eva_day  != null "><![CDATA[ AND A.PRICE_EVA_DAY = #{price_eva_day} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" invest_account_name != null and invest_account_name != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_NAME = #{invest_account_name} ]]></if>
	</sql>

	<!-- 按索引生成的查询条件 -->
	<sql id="queryInvestAccountInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="addInvestAccountInfo" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO(
				INSERT_TIMESTAMP, PRICE_EVA_DAY, INVEST_ACCOUNT_CODE, UPDATE_BY, INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, INVEST_ACCOUNT_NAME ) 
			VALUES (
				CURRENT_TIMESTAMP, #{price_eva_day, jdbcType=NUMERIC} , #{invest_account_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{invest_account_name, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="deleteInvestAccountInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="updateInvestAccountInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO ]]>
		<set>
			<trim suffixOverrides=",">
				PRICE_EVA_DAY = #{price_eva_day, jdbcType=NUMERIC} ,
				INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				UPDATE_TIME = SYSDATE ,
				INVEST_ACCOUNT_NAME = #{invest_account_name, jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 账户资产录入-查询所有投资账户代码 -->
	<select id="editFindInvestAccountInfoByInvestAccountCodee"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRICE_EVA_DAY, A.INVEST_ACCOUNT_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_NAME FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="investAccountInfoWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>


	<!-- 按map查询操作 -->
	<select id="findAllMapInvestAccountInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRICE_EVA_DAY, A.INVEST_ACCOUNT_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_NAME FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllInvestAccountInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRICE_EVA_DAY, A.INVEST_ACCOUNT_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_NAME FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="investAccountInfoWhereCondition" />
		<![CDATA[ ORDER BY A.INVEST_ACCOUNT_CODE ]]>
	</select>
	<!-- 账户资产录入-根据投资账户代码查询投资账户名称 -->
	<select id="editFindAllInvestAccountInfolistt" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRICE_EVA_DAY, A.INVEST_ACCOUNT_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_NAME FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.INVEST_ACCOUNT_CODE ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="findInvestAccountInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="queryInvestAccountInfoForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRICE_EVA_DAY, B.INVEST_ACCOUNT_CODE, B.LIST_ID, 
			B.INVEST_ACCOUNT_NAME FROM (
					SELECT ROWNUM RN, A.PRICE_EVA_DAY, A.INVEST_ACCOUNT_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_NAME FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 条件查询单条数据 查询投资账户代码 xuyp -->
	<select id="checkFundfindInvestAccountInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT  A.LIST_ID,A.INVEST_ACCOUNT_CODE,A.INVEST_ACCOUNT_NAME,A.PRICE_EVA_DAY,A.INSERT_BY,
		A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE 1 = 1 AND invest_account_code =  #{invest_account_code}]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="getInvestAccountCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.INVEST_ACCOUNT_NAME, A.INVEST_ACCOUNT_CODE
			FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="investAccountInfoWhereCondition" />
	</select>
	
	<!-- 查询单条数据 -->
	<select id="findInvestAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRICE_EVA_DAY, A.INVEST_ACCOUNT_CODE, A.LIST_ID, 
			A.INVEST_ACCOUNT_NAME FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="investAccountInfoWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
</mapper>
