<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRenewCollectionUndoDao">

<sql id="PA_findPremArapNoRollBackCondition">
		<if test=" task_mark  != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
		<if test=" policy_organ_code != null and policy_organ_code != ''  "><![CDATA[ AND A.POLICY_ORGAN_CODE = #{policy_organ_code} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" is_item_main  != null "><![CDATA[ AND A.IS_ITEM_MAIN = #{is_item_main} ]]></if>
		<if test=" bookkeeping_id  != null "><![CDATA[ AND A.BOOKKEEPING_ID = #{bookkeeping_id} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" payee_phone != null and payee_phone != ''  "><![CDATA[ AND A.PAYEE_PHONE = #{payee_phone} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" funds_rtn_code != null and funds_rtn_code != ''  "><![CDATA[ AND A.FUNDS_RTN_CODE = #{funds_rtn_code} ]]></if>
		<if test=" customer_account_flag  != null "><![CDATA[ AND A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" seq_no  != null "><![CDATA[ AND A.SEQ_NO = #{seq_no} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" fee_status_date  != null  and  fee_status_date  != ''  "><![CDATA[ AND A.FEE_STATUS_DATE = #{fee_status_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if
			test=" red_bookkeeping_time  != null  and  red_bookkeeping_time  != ''  "><![CDATA[ AND A.RED_BOOKKEEPING_TIME = #{red_bookkeeping_time} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" is_bank_text_date  != null  and  is_bank_text_date  != ''  "><![CDATA[ AND A.IS_BANK_TEXT_DATE = #{is_bank_text_date} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" is_bank_account  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT = #{is_bank_account} ]]></if>
		<if test=" frozen_status != null and frozen_status != ''  "><![CDATA[ AND A.FROZEN_STATUS = #{frozen_status} ]]></if>
		<if test=" bookkeeping_flag  != null "><![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag} ]]></if>
		<if test=" posted != null and posted != ''  "><![CDATA[ AND A.POSTED = #{posted} ]]></if>
		<if test=" group_id  != null "><![CDATA[ AND A.GROUP_ID = #{group_id} ]]></if>
		<if test=" red_bookkeeping_by  != null "><![CDATA[ AND A.RED_BOOKKEEPING_BY = #{red_bookkeeping_by} ]]></if>
		<if test=" frozen_status_date  != null  and  frozen_status_date  != ''  "><![CDATA[ AND A.FROZEN_STATUS_DATE = #{frozen_status_date} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>

		<if test=" cus_acc_details_id  != null "><![CDATA[ AND A.CUS_ACC_DETAILS_ID = #{cus_acc_details_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" product_channel != null and product_channel != ''  "><![CDATA[ AND A.PRODUCT_CHANNEL = #{product_channel} ]]></if>
		<if
			test=" is_bank_account_date  != null  and  is_bank_account_date  != ''  "><![CDATA[ AND A.IS_BANK_ACCOUNT_DATE = #{is_bank_account_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" red_belnr != null and red_belnr != ''  "><![CDATA[ AND A.RED_BELNR = #{red_belnr} ]]></if>
		<if test=" bank_text_status != null and bank_text_status != ''  "><![CDATA[ AND A.BANK_TEXT_STATUS = #{bank_text_status} ]]></if>
		<if test=" group_code != null and group_code != ''  "><![CDATA[ AND A.GROUP_CODE = #{group_code} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" cus_acc_update_by  != null "><![CDATA[ AND A.CUS_ACC_UPDATE_BY = #{cus_acc_update_by} ]]></if>
		<if test=" is_bank_text_by  != null "><![CDATA[ AND A.IS_BANK_TEXT_BY = #{is_bank_text_by} ]]></if>
		<if test=" is_bank_account_by  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT_BY = #{is_bank_account_by} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" red_bookkeeping_flag  != null "><![CDATA[ AND A.RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag} ]]></if>
		<if test=" cus_acc_fee_amount  != null "><![CDATA[ AND A.CUS_ACC_FEE_AMOUNT = #{cus_acc_fee_amount} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" is_bank_text  != null "><![CDATA[ AND A.IS_BANK_TEXT = #{is_bank_text} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
		<if test=" group_name != null and group_name != ''  "><![CDATA[ AND A.GROUP_NAME = #{group_name} ]]></if>
		<if test=" belnr != null and belnr != ''  "><![CDATA[ AND A.BELNR = #{belnr} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != ''  "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
		<if test=" fail_times  != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" frozen_status_by  != null "><![CDATA[ AND A.FROZEN_STATUS_BY = #{frozen_status_by} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" fee_status_by  != null "><![CDATA[ AND A.FEE_STATUS_BY = #{fee_status_by} ]]></if>
		<if test=" pay_end_date  != null  and  pay_end_date  != ''  "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
		<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" red_bookkeeping_id  != null "><![CDATA[ AND A.RED_BOOKKEEPING_ID = #{red_bookkeeping_id} ]]></if>
		<if test=" bookkeeping_by  != null "><![CDATA[ AND A.BOOKKEEPING_BY = #{bookkeeping_by} ]]></if>
		<if test=" cus_acc_update_time  != null  and  cus_acc_update_time  != ''  "><![CDATA[ AND A.CUS_ACC_UPDATE_TIME = #{cus_acc_update_time} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" bookkeeping_time  != null  and  bookkeeping_time  != ''  "><![CDATA[ AND A.BOOKKEEPING_TIME = #{bookkeeping_time} ]]></if>
		<if test=" refeflag != null and refeflag != ''  "><![CDATA[ AND A.REFEFLAG = #{refeflag} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" filter_business_code  != null "><![CDATA[ AND A.business_code != #{filter_business_code} ]]></if>

		<if test=" pay_mode_list != null and pay_mode_list != ''  "><![CDATA[ AND A.PAY_MODE in ]]>
			<foreach collection="pay_mode_list" item="item" index="index"
				open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test="deriv_type_list  != null and deriv_type_list.size()!=0 ">
			<![CDATA[  AND (]]>
			<foreach collection="deriv_type_list" item="deriv_type_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.DERIV_TYPE = #{deriv_type_item} ]]>
			</foreach>
			<![CDATA[)  ]]>
		</if>
	</sql>


	<!-- 分页查询操作 -->
	<select id="findPremArapNoRollBack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1  
			and a.unit_number not in
	       (select tpa.unit_number
	          from APP___PAS__DBUSER.t_prem_arap tpa
	         where tpa.rollback_unit_number is not null)
			]]>
		<include refid="PA_findPremArapNoRollBackCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	<!-- 保全续期操作查询 --><!-- 138883 add ,A.CHARGE_PERIOD,A.BUSI_ITEM_ID,A.SPECIAL_ACCOUNT_FLAG -->
	<select id="findPremArapNoRollBackForCS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, 
			 (select MIN(C.FINISH_TIME)  from APP___PAS__DBUSER.V_PREM_ALL C  WHERE  A.UNIT_NUMBER =C.UNIT_NUMBER  
            AND A.BUSI_PROD_CODE = C.BUSI_PROD_CODE) FINISH_TIME,
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ,A.CHARGE_PERIOD,A.BUSI_ITEM_ID,A.SPECIAL_ACCOUNT_FLAG FROM APP___PAS__DBUSER.V_PREM_ARAP_ALL A
     		WHERE 1 = 1
			AND NOT EXISTS ( /* 去除已进行过续期回退的数据 */
				SELECT 'X'
				FROM APP___PAS__DBUSER.V_PREM_ARAP_ALL B
				WHERE 1 = 1
				AND B.POLICY_CODE = A.POLICY_CODE
				AND B.DERIV_TYPE = '004'
				AND (B.FEE_STATUS = '01' OR B.FEE_STATUS = '19' or B.FEE_STATUS = '00' OR B.FEE_STATUS = '03')
				AND B.ROLLBACK_UNIT_NUMBER IS NOT NULL
				AND B.ROLLBACK_UNIT_NUMBER = A.UNIT_NUMBER
				]]>
					<if test=" ShowXQBusinessCode != null and ShowXQBusinessCode != ''  ">
					<![CDATA[ AND B.BUSINESS_CODE != #{ShowXQBusinessCode} ]]></if>
				<![CDATA[
				)
				and A.DERIV_TYPE = '003' 
                and A.business_type in( '4003','1005') 
                AND A.ARAP_FLAG='1'
                AND A.FEE_STATUS NOT IN('02','16')
			
			]]>
		<include refid="PA_findPremArapNoRollBackCondition" />
		<![CDATA[ ORDER BY A.insert_time desc, A.LIST_ID DESC ]]>
	</select>

	<!--查询未办结的续期费用-->
	<select id="queryOutstandingRenewalPremiums" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
      A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,A.CHARGE_PERIOD,
      A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
      A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
      A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
      A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
      A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
      A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
      A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
      A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
      A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
      A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
      A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
      A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID,A.IS_CORPORATE,
            A.CIP_DISTRICT_BANK_CODE  ,A.SPECIAL_ACCOUNT_FLAG
          FROM APP___PAS__DBUSER.t_prem_arap A
         WHERE A.DERIV_TYPE = '003'
           and A.business_type in ('4003', '1005')
           AND A.FEE_STATUS = #{fee_status}
           AND A.POLICY_CODE = #{policy_code}
           AND A.FEE_STATUS in ('00','01','03')
			]]>
	</select>
	
	<!-- 保全续期操作查询 FOR 新增长期附加险 -->
	<select id="findPremArapNoRollBackForNS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1  
			AND NOT EXISTS ( /* 去除已进行过续期回退的数据 */
				SELECT 'X'
				FROM APP___PAS__DBUSER.T_PREM_ARAP B
				WHERE 1 = 1
				AND B.POLICY_CODE = A.POLICY_CODE
				AND B.DERIV_TYPE = '004'
				AND B.FEE_STATUS = '01' 
				AND B.ROLLBACK_UNIT_NUMBER IS NOT NULL
				AND B.ROLLBACK_UNIT_NUMBER = A.UNIT_NUMBER
				)
			/* and (a.DERIV_TYPE = '003' or a.DERIV_TYPE = '001' ) 
			 and business_type = 4003 */
			AND A.WITHDRAW_TYPE = '**********' 
			
			]]>
		<include refid="PA_findPremArapNoRollBackCondition" />
		<![CDATA[ ORDER BY A.insert_time desc ]]>
	</select>
	<!-- 判断是否有未核销续期 -->
	<select id="findPremArapNoFinisForCS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
						A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
						A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
						A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
						A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
						A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
						A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
						A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
						A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
						A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
						A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
						A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
						A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
						A.AGENT_CODE, A.PREM_FREQ 
						FROM APP___PAS__DBUSER.T_PREM_ARAP A 
					WHERE 1 = 1 /* a.DERIV_TYPE = '003' 
					 and business_type = 4003 */
					 and A.Arap_Flag = '1'
					AND WITHDRAW_TYPE IN ('**********', '**********', '**********', '**********') 
					and fee_status not in ('19','16','02','01')
			]]>
		<include refid="PA_findPremArapNoRollBackCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 保全续期操作查询 -->
	<select id="findPremArapNoRollBackForPT" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ , A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1  
			/* and (a.DERIV_TYPE = '003' or a.DERIV_TYPE = '001' ) 
			and business_type = 4003 */
			AND WITHDRAW_TYPE IN ('**********', '**********', '**********', '**********') 
			and fee_status  in ('19','16')
			and a.due_time >= #{due_time}
			and a.ROLLBACK_UNIT_NUMBER is null
			and product_code = #{product_code}
			and a.policy_code=#{policy_code}
			and not exists(
				select 'X' from APP___PAS__DBUSER.T_PREM_ARAP B where b.ROLLBACK_UNIT_NUMBER = a.UNIT_NUMBER
			)
			
			]]>
		
		<![CDATA[ ORDER BY A.insert_time desc ]]>
	</select>
	<!-- 保全续期操作查询 -->
	<select id="findPremArapNoRollBackForByRollBackNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1  
			AND NOT EXISTS ( /* 去除已进行过续期回退的数据 */
				SELECT 'X'
				FROM APP___PAS__DBUSER.T_PREM_ARAP B
				WHERE 1 = 1
				AND B.POLICY_CODE = A.POLICY_CODE
				AND B.DERIV_TYPE = '004'
				AND B.FEE_STATUS = '01'
				AND B.ROLLBACK_UNIT_NUMBER IS NOT NULL
				AND B.ROLLBACK_UNIT_NUMBER = A.UNIT_NUMBER
				)
			and (a.DERIV_TYPE = '003' or a.DERIV_TYPE = '001' ) 
			
			]]>
		<include refid="PA_findPremArapNoRollBackCondition" />
		<![CDATA[ ORDER BY A.insert_time desc ]]>
	</select>
	
<!-- 保全续期生效后方法专用  查询本次回退的续期操作 -->
	<select id="findPremArapByAcceptCodeForXQEffectAfter" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
      A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
      A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
      A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
      A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
      A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
      A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
      A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
      A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
      A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
      A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
      A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
      A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
      A.AGENT_CODE, A.PREM_FREQ FROM APP___PAS__DBUSER.T_PREM_ARAP A , APP___PAS__DBUSER.T_PREM_ARAP B WHERE 1 = 1  
      AND A.UNIT_NUMBER = B.ROLLBACK_UNIT_NUMBER
      AND A.POLICY_CODE = B.POLICY_CODE
      AND B.BUSINESS_CODE = #{business_code}
      AND B.policy_code = #{policy_code}
      and (a.DERIV_TYPE = '003' or a.DERIV_TYPE = '001' ) 
			
			 ORDER BY A.insert_time desc ]]>
	</select>
</mapper>