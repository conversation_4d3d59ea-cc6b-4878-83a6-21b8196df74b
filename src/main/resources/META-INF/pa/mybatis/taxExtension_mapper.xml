<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.TaxExtensionDaoImpl">
<!--
	<sql id="taxExtensionWhereCondition">
		<if test=" operator_mobile != null and operator_mobile != ''  "><![CDATA[ AND A.OPERATOR_MOBILE = #{operator_mobile} ]]></if>
		<if test=" receive_com_bank_code != null and receive_com_bank_code != ''  "><![CDATA[ AND A.RECEIVE_COM_BANK_CODE = #{receive_com_bank_code} ]]></if>
		<if test=" receive_com_acco_name != null and receive_com_acco_name != ''  "><![CDATA[ AND A.RECEIVE_COM_ACCO_NAME = #{receive_com_acco_name} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" from_policy_code != null and from_policy_code != ''  "><![CDATA[ AND A.FROM_POLICY_CODE = #{from_policy_code} ]]></if>
		<if test=" operator != null and operator != ''  "><![CDATA[ AND A.OPERATOR = #{operator} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" from_com_code != null and from_com_code != ''  "><![CDATA[ AND A.FROM_COM_CODE = #{from_com_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" operator_email != null and operator_email != ''  "><![CDATA[ AND A.OPERATOR_EMAIL = #{operator_email} ]]></if>
		<if test=" receive_com_bank_account != null and receive_com_bank_account != ''  "><![CDATA[ AND A.RECEIVE_COM_BANK_ACCOUNT = #{receive_com_bank_account} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" tax_extension_ic != null and tax_extension_ic != ''  "><![CDATA[ AND A.TAX_EXTENSION_IC = #{tax_extension_ic} ]]></if>
		<if test=" trans_fee_amount  != null "><![CDATA[ AND A.TRANS_FEE_AMOUNT = #{trans_fee_amount} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" from_com_name != null and from_com_name != ''  "><![CDATA[ AND A.FROM_COM_NAME = #{from_com_name} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryTaxExtensionByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryTaxExtensionByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="queryTaxExtensionByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryTaxExtensionByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addTaxExtension"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TAX_EXTENSION.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_EXTENSION(
				OPERATOR_MOBILE, RECEIVE_COM_BANK_CODE, RECEIVE_COM_ACCO_NAME, INSERT_TIME, REMARK, UPDATE_TIME, FROM_POLICY_CODE, 
				OPERATOR, APPLY_CODE, INSERT_TIMESTAMP, FROM_COM_CODE, POLICY_CODE, UPDATE_BY, OPERATOR_EMAIL, 
				RECEIVE_COM_BANK_ACCOUNT, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, TAX_EXTENSION_IC, TRANS_FEE_AMOUNT, POLICY_ID, 
				FROM_COM_NAME ) 
			VALUES (
				#{operator_mobile, jdbcType=VARCHAR}, #{receive_com_bank_code, jdbcType=VARCHAR} , #{receive_com_acco_name, jdbcType=VARCHAR} , SYSDATE , #{remark, jdbcType=VARCHAR} , SYSDATE , #{from_policy_code, jdbcType=VARCHAR} 
				, #{operator, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{from_com_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{operator_email, jdbcType=VARCHAR} 
				, #{receive_com_bank_account, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{tax_extension_ic, jdbcType=VARCHAR} , #{trans_fee_amount, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{from_com_name, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteTaxExtension" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TAX_EXTENSION WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateTaxExtension" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TAX_EXTENSION ]]>
		<set>
		<trim suffixOverrides=",">
			OPERATOR_MOBILE = #{operator_mobile, jdbcType=VARCHAR} ,
			RECEIVE_COM_BANK_CODE = #{receive_com_bank_code, jdbcType=VARCHAR} ,
			RECEIVE_COM_ACCO_NAME = #{receive_com_acco_name, jdbcType=VARCHAR} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			FROM_POLICY_CODE = #{from_policy_code, jdbcType=VARCHAR} ,
			OPERATOR = #{operator, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			FROM_COM_CODE = #{from_com_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			OPERATOR_EMAIL = #{operator_email, jdbcType=VARCHAR} ,
			RECEIVE_COM_BANK_ACCOUNT = #{receive_com_bank_account, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			TAX_EXTENSION_IC = #{tax_extension_ic, jdbcType=VARCHAR} ,
		    TRANS_FEE_AMOUNT = #{trans_fee_amount, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			FROM_COM_NAME = #{from_com_name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findTaxExtensionByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_MOBILE, A.RECEIVE_COM_BANK_CODE, A.RECEIVE_COM_ACCO_NAME, A.REMARK, A.FROM_POLICY_CODE, 
			A.OPERATOR, A.APPLY_CODE, A.FROM_COM_CODE, A.POLICY_CODE, A.OPERATOR_EMAIL, 
			A.RECEIVE_COM_BANK_ACCOUNT, A.LIST_ID, A.TAX_EXTENSION_IC, A.TRANS_FEE_AMOUNT, A.POLICY_ID, 
			A.FROM_COM_NAME FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE 1 = 1  ]]>
		<include refid="queryTaxExtensionByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findTaxExtensionByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_MOBILE, A.RECEIVE_COM_BANK_CODE, A.RECEIVE_COM_ACCO_NAME, A.REMARK, A.FROM_POLICY_CODE, 
			A.OPERATOR, A.APPLY_CODE, A.FROM_COM_CODE, A.POLICY_CODE, A.OPERATOR_EMAIL, 
			A.RECEIVE_COM_BANK_ACCOUNT, A.LIST_ID, A.TAX_EXTENSION_IC, A.TRANS_FEE_AMOUNT, A.POLICY_ID, 
			A.FROM_COM_NAME FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE 1 = 1  ]]>
		<include refid="queryTaxExtensionByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findTaxExtensionByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_MOBILE, A.RECEIVE_COM_BANK_CODE, A.RECEIVE_COM_ACCO_NAME, A.REMARK, A.FROM_POLICY_CODE, 
			A.OPERATOR, A.APPLY_CODE, A.FROM_COM_CODE, A.POLICY_CODE, A.OPERATOR_EMAIL, 
			A.RECEIVE_COM_BANK_ACCOUNT, A.LIST_ID, A.TAX_EXTENSION_IC, A.TRANS_FEE_AMOUNT, A.POLICY_ID, 
			A.FROM_COM_NAME FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE 1 = 1  ]]>
		<include refid="queryTaxExtensionByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findTaxExtensionByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_MOBILE, A.RECEIVE_COM_BANK_CODE, A.RECEIVE_COM_ACCO_NAME, A.REMARK, A.FROM_POLICY_CODE, 
			A.OPERATOR, A.APPLY_CODE, A.FROM_COM_CODE, A.POLICY_CODE, A.OPERATOR_EMAIL, 
			A.RECEIVE_COM_BANK_ACCOUNT, A.LIST_ID, A.TAX_EXTENSION_IC, A.TRANS_FEE_AMOUNT, A.POLICY_ID, 
			A.FROM_COM_NAME FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE 1 = 1  ]]>
		<include refid="queryTaxExtensionByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapTaxExtension" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_MOBILE, A.RECEIVE_COM_BANK_CODE, A.RECEIVE_COM_ACCO_NAME, A.REMARK, A.FROM_POLICY_CODE, 
			A.OPERATOR, A.APPLY_CODE, A.FROM_COM_CODE, A.POLICY_CODE, A.OPERATOR_EMAIL, 
			A.RECEIVE_COM_BANK_ACCOUNT, A.LIST_ID, A.TAX_EXTENSION_IC, A.TRANS_FEE_AMOUNT, A.POLICY_ID, 
			A.FROM_COM_NAME FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllTaxExtension" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_MOBILE, A.RECEIVE_COM_BANK_CODE, A.RECEIVE_COM_ACCO_NAME, A.REMARK, A.FROM_POLICY_CODE, 
			A.OPERATOR, A.APPLY_CODE, A.FROM_COM_CODE, A.POLICY_CODE, A.OPERATOR_EMAIL, 
			A.RECEIVE_COM_BANK_ACCOUNT, A.LIST_ID, A.TAX_EXTENSION_IC, A.TRANS_FEE_AMOUNT, A.POLICY_ID, 
			A.FROM_COM_NAME FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findTaxExtensionTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryTaxExtensionForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATOR_MOBILE, B.RECEIVE_COM_BANK_CODE, B.RECEIVE_COM_ACCO_NAME, B.REMARK, B.FROM_POLICY_CODE, 
			B.OPERATOR, B.APPLY_CODE, B.FROM_COM_CODE, B.POLICY_CODE, B.OPERATOR_EMAIL, 
			B.RECEIVE_COM_BANK_ACCOUNT, B.LIST_ID, B.TAX_EXTENSION_IC, B.TRANS_FEE_AMOUNT, B.POLICY_ID, 
			B.FROM_COM_NAME FROM (
					SELECT ROWNUM RN, A.OPERATOR_MOBILE, A.RECEIVE_COM_BANK_CODE, A.RECEIVE_COM_ACCO_NAME, A.REMARK, A.FROM_POLICY_CODE, 
			A.OPERATOR, A.APPLY_CODE, A.FROM_COM_CODE, A.POLICY_CODE, A.OPERATOR_EMAIL, 
			A.RECEIVE_COM_BANK_ACCOUNT, A.LIST_ID, A.TAX_EXTENSION_IC, A.TRANS_FEE_AMOUNT, A.POLICY_ID, 
			A.FROM_COM_NAME FROM APP___PAS__DBUSER.T_TAX_EXTENSION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
