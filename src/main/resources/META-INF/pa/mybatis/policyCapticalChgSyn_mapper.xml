<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.nci.tunan.pa.dao.IPolicyCapticalChgSynDao">

   <!-- 查询北京银行保单资产变动信息同步交易保单层的数据操作 -->	
	<select id="findAllPolicyCapticalChgSyn" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   select tcm.policy_code,
       tcm.apply_code,
       tc.customer_name as appnt_name,
       tc.customer_cert_type as appnt_id_type,
       tc.customer_certi_code as appnt_id_no
       tpca.service_code,
       tpca.validate_time as busi_date,
       tcm.liability_state AS flag,
       sum(tcp.total_prem_af) prem
  from dev_pas.t_policy_change tpca
  left join dev_pas.t_surrender ts
    on ts.accept_code = tpca.business_code
  left join dev_pas.t_contract_master tcm
  on tcm.policy_id = tpca.policy_id
  join dev_pas.t_contract_product tcp
    on tcp.policy_code = tcm.policy_code
  left join dev_pas.t_policy_holder tph
    on tph.policy_id = tpca.policy_id
  left join dev_pas.t_customer tc
    on tc.customer_id = tph.customer_id
 where tpca.service_code in ('CT', 'PED', 'PA', 'PT', 'XT', 'IT', 'PREF')
 and tcm.channel_type in ('03', '06')
  ]]>
  <if test=" start_date != null  "><![CDATA[ AND TPCA.VALIDATE_TIME >= #{start_date} ]]></if>
  <if test=" end_date != null "><![CDATA[ AND TPCA.VALIDATE_TIME < #{end_date} ]]></if>
  <if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND TCM.service_bank_branch = #{service_bank_branch} ]]></if>
  <if test=" policy_code != null and policy_code !='' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
 <![CDATA[
 group by tcm.policy_code,
          tcm.apply_code,
          tc.customer_name,
          tc.customer_cert_type,
          tc.customer_certi_code,
          tpca.service_code,
          tpca.validate_time,
          tcm.liability_state
		 ]]>
	
	</select>
	
	
	<select id="findPolicyAllRiskInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select tcb.busi_prod_code as risk_code,
                tbp.product_abbr_name as risk_name,
                sum(tcp.total_prem_af) risk_prem
           from dev_pas.t_contract_busi_prod tcb
           left join dev_pas.t_business_product tbp
             on tbp.business_prd_id = tcb.busi_prd_id
           left join dev_pas.t_contract_product tcp
             on tcp.busi_item_id = tcb.busi_item_id
          where tcb.policy_code =#{policy_code}
          group by tcb.busi_prod_code,
          tbp.product_abbr_name
		
	]]>
	</select>
	
</mapper>
