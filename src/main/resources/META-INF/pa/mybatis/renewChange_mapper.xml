<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.IRenewChangeDao">

	<sql id="renewChangeWhereCondition">
		<if test=" new_unit  != null and  new_unit  != '' "><![CDATA[ AND A.NEW_UNIT = #{new_unit} ]]></if>
		<if test=" new_prem  != null and  new_prem  != '' "><![CDATA[ AND A.NEW_PREM = #{new_prem} ]]></if>
		<if test=" new_amount  != null and  new_amount  != '' "><![CDATA[ AND A.NEW_AMOUNT = #{new_amount} ]]></if>
		<if test=" old_amount  != null and  old_amount  != '' "><![CDATA[ AND A.OLD_AMOUNT = #{old_amount} ]]></if>
		<if test=" old_busi_prd_id  != null and  old_busi_prd_id  != '' "><![CDATA[ AND A.OLD_BUSI_PRD_ID = #{old_busi_prd_id} ]]></if>
		<if test=" item_id  != null and  item_id  != '' "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" renew_change_type  != null and  renew_change_type  != '' "><![CDATA[ AND A.RENEW_CHANGE_TYPE = #{renew_change_type} ]]></if>
		<if test=" change_id  != null and  change_id  != '' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" process_time  != null  and  process_time  != ''  "><![CDATA[ AND A.PROCESS_TIME = #{process_time} ]]></if>
		<if test=" policy_chg_id  != null and  policy_chg_id  != '' "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" old_prem  != null and  old_prem  != '' "><![CDATA[ AND A.OLD_PREM = #{old_prem} ]]></if>
		<if test=" policy_id  != null and  policy_id  != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" valid_status != null and  valid_status != ''  "><![CDATA[ AND A.VALID_STATUS = #{valid_status} ]]></if>
		<if test=" accept_id  != null and  accept_id  != '' "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" old_unit  != null and  old_unit  != '' "><![CDATA[ AND A.OLD_UNIT = #{old_unit} ]]></if>
		<if test=" master_busi_item_id  != null and  master_busi_item_id  != '' "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" new_busi_prd_id  != null and  new_busi_prd_id  != '' "><![CDATA[ AND A.NEW_BUSI_PRD_ID = #{new_busi_prd_id} ]]></if>
		<if test=" end_cause != null and  end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" precont_id  != null and  precont_id  != '' "><![CDATA[ AND A.PRECONT_ID = #{precont_id} ]]></if>
		<if test=" old_std_prem_af  != null  and  old_std_prem_af  != '' "><![CDATA[ AND A.OLD_STD_PREM_AF = #{old_std_prem_af} ]]></if>
		<if test=" policy_code != null and  policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" new_std_prem_af  != null and  new_std_prem_af  != '' "><![CDATA[ AND A.NEW_STD_PREM_AF = #{new_std_prem_af} ]]></if>
		<if test=" valid_time  != null  and  valid_time  != ''  "><![CDATA[ AND A.VALID_TIME = #{valid_time} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryRenewChangeByPrecontIdCondition">
		<if test=" precont_id  != null "><![CDATA[ AND A.PRECONT_ID = #{precont_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addRenewChange"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="precont_id">
			SELECT S_RENEW_CHANGE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RENEW_CHANGE(
				NEW_UNIT, NEW_PREM, NEW_AMOUNT, OLD_AMOUNT, OLD_BUSI_PRD_ID, ITEM_ID, INSERT_TIMESTAMP, 
				UPDATE_BY, RENEW_CHANGE_TYPE, CHANGE_ID, PROCESS_TIME, POLICY_CHG_ID, OLD_PREM, POLICY_ID, 
				VALID_STATUS, ACCEPT_ID, OLD_UNIT, INSERT_TIME, MASTER_BUSI_ITEM_ID, NEW_BUSI_PRD_ID, END_CAUSE, 
				UPDATE_TIME, PRECONT_ID, OLD_STD_PREM_AF, POLICY_CODE, NEW_STD_PREM_AF, UPDATE_TIMESTAMP, INSERT_BY, 
				VALID_TIME ) 
			VALUES (
				#{new_unit, jdbcType=NUMERIC}, #{new_prem, jdbcType=NUMERIC} , #{new_amount, jdbcType=NUMERIC} , #{old_amount, jdbcType=NUMERIC} , #{old_busi_prd_id, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{update_by, jdbcType=NUMERIC} , #{renew_change_type, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{process_time, jdbcType=DATE} , #{policy_chg_id, jdbcType=NUMERIC} , #{old_prem, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{valid_status, jdbcType=VARCHAR} , #{accept_id, jdbcType=NUMERIC} , #{old_unit, jdbcType=NUMERIC} , SYSDATE , #{master_busi_item_id, jdbcType=NUMERIC} , #{new_busi_prd_id, jdbcType=NUMERIC} , #{end_cause, jdbcType=VARCHAR} 
				, SYSDATE , #{precont_id, jdbcType=NUMERIC} , #{old_std_prem_af, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{new_std_prem_af, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{valid_time, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRenewChange" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RENEW_CHANGE WHERE PRECONT_ID = #{precont_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateRenewChange" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RENEW_CHANGE ]]>
		<set>
		<trim suffixOverrides=",">
		    NEW_UNIT = #{new_unit, jdbcType=NUMERIC} ,
		    NEW_PREM = #{new_prem, jdbcType=NUMERIC} ,
		    NEW_AMOUNT = #{new_amount, jdbcType=NUMERIC} ,
		    OLD_AMOUNT = #{old_amount, jdbcType=NUMERIC} ,
		    OLD_BUSI_PRD_ID = #{old_busi_prd_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    RENEW_CHANGE_TYPE = #{renew_change_type, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    PROCESS_TIME = #{process_time, jdbcType=DATE} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    OLD_PREM = #{old_prem, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			VALID_STATUS = #{valid_status, jdbcType=VARCHAR} ,
		    ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} ,
		    OLD_UNIT = #{old_unit, jdbcType=NUMERIC} ,
		    MASTER_BUSI_ITEM_ID = #{master_busi_item_id, jdbcType=NUMERIC} ,
		    NEW_BUSI_PRD_ID = #{new_busi_prd_id, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    PRECONT_ID = #{precont_id, jdbcType=NUMERIC} ,
		    OLD_STD_PREM_AF = #{old_std_prem_af, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    NEW_STD_PREM_AF = #{new_std_prem_af, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    VALID_TIME = #{valid_time, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE PRECONT_ID = #{precont_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findRenewChangeByPrecontId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, 
			A.VALID_TIME FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE 1 = 1  ]]>
		<include refid="queryRenewChangeByPrecontIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapRenewChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, 
			A.VALID_TIME FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRenewChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, A.SAFEGUARD_PLAN, 
			A.VALID_TIME FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<include refid="renewChangeWhereCondition" />
		<![CDATA[ ORDER BY A.PROCESS_TIME DESC ]]>
	</select>
	<select id="findLastRenewChangeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.* FROM (SELECT A.* FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE 1 =1 ]]>
			<if test=" policy_id  != null and  policy_id  != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
			<if test=" old_busi_prd_id  != null and  old_busi_prd_id  != '' "><![CDATA[ AND A.OLD_BUSI_PRD_ID = #{old_busi_prd_id} ]]></if>
			<if test=" policy_code != null and  policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ORDER BY A.VALID_TIME DESC  ) B
			WHERE ROWNUM = 1]]>
	</select>
	<select id="findLastRenewChangeByValDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.* FROM (SELECT A.* FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE 1 =1 ]]>
			<if test=" policy_id  != null and  policy_id  != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
			<if test=" old_busi_prd_id  != null and  old_busi_prd_id  != '' "><![CDATA[ AND A.OLD_BUSI_PRD_ID = #{old_busi_prd_id} ]]></if>
			<if test=" policy_code != null and  policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" valid_time  != null  and  valid_time  != ''  "><![CDATA[ AND A.VALID_TIME >= #{valid_time} ]]></if>
			<if test=" item_id  != null  and  item_id  != ''  "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
			<if test=" valid_status != null and  valid_status != ''  "><![CDATA[ AND A.VALID_STATUS = #{valid_status} ]]></if>
			<if test=" new_busi_prd_id  != null and  new_busi_prd_id  != '' "><![CDATA[ AND A.NEW_BUSI_PRD_ID = #{new_busi_prd_id} ]]></if>
		<![CDATA[ORDER BY A.VALID_TIME DESC,A.PROCESS_TIME DESC,A.PRECONT_ID DESC) B WHERE ROWNUM = 1]]>
	</select>
<!-- 查询个数操作 -->
	<select id="findRenewChangeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE 1 = 1  ]]>
		<include refid="renewChangeWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryRenewChangeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NEW_UNIT, B.NEW_PREM, B.NEW_AMOUNT, B.OLD_AMOUNT, B.OLD_BUSI_PRD_ID, B.ITEM_ID, 
			B.RENEW_CHANGE_TYPE, B.CHANGE_ID, B.PROCESS_TIME, B.POLICY_CHG_ID, B.OLD_PREM, B.POLICY_ID, 
			B.VALID_STATUS, B.ACCEPT_ID, B.OLD_UNIT, B.MASTER_BUSI_ITEM_ID, B.NEW_BUSI_PRD_ID, B.END_CAUSE, 
			B.PRECONT_ID, B.OLD_STD_PREM_AF, B.POLICY_CODE, B.NEW_STD_PREM_AF, 
			B.VALID_TIME FROM (
					SELECT ROWNUM RN, A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, 
			A.VALID_TIME FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 保全操作任务状态查询接口 -->
	<select id="findListByContNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CODE, B.INSERT_TIME, B.APP_STATUS, D.ACCEPT_CODE, D.ACCEPT_TIME, A.VALIDATE_DATE, E.CAPITAL_BALANCE, D.SERVICE_CODE, A.LIABILITY_STATE
       			  FROM 
				        APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A,
				        APP___PAS__DBUSER.T_CS_APPLICATION B,
				        APP___PAS__DBUSER.T_CS_BANK_ACCOUNT C,
				        APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE D,
				        APP___PAS__DBUSER.T_CS_POLICY_ACCOUNT E
			      WHERE
				       A.CHANGE_ID = B.CHANGE_ID AND
				       A.LOG_ID = C.LOG_ID AND
				       C.ACCEPT_ID = D.ACCEPT_ID AND
				       C.LOG_ID = E.LOG_ID AND
				       A.POLICY_CODE = #{policy_code} ]]>
	</select>
	
	<select id="PA_findRenewChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, 
			A.VALID_TIME,A.SAFEGUARD_PLAN FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE ROWNUM <=  1  ]]>
		<if test=" policy_id  != null and  policy_id  != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" old_busi_prd_id  != null and  old_busi_prd_id  != '' "><![CDATA[ AND A.OLD_BUSI_PRD_ID = #{old_busi_prd_id} ]]></if>
		<if test=" policy_code != null and  policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<include refid="renewChangeWhereCondition" />
		<![CDATA[ORDER BY A.PROCESS_TIME]]>
	</select>
	
	<select id="findAllRenewChangeNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, 
			A.VALID_TIME FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE ROWNUM <=  1  ]]>
		<if test=" change_id  != null and  change_id  != '' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" old_busi_prd_id  != null and  old_busi_prd_id  != '' "><![CDATA[ AND A.OLD_BUSI_PRD_ID = #{old_busi_prd_id} ]]></if>
		<if test=" policy_chg_id != null and  policy_chg_id != ''  "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</select>
	
	<select id="findAllRenewChanges" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select c.old_busi_prd_id, c.new_busi_prd_id, e.service_code,c.master_busi_item_id
			  from dev_pas.t_renew_change c,
			       (select pc.policy_chg_id,
			               ac.accept_id,
			               ac.service_code,
			               ac.accept_code
			          from app___pas__dbuser.t_cs_accept_change ac
			          left join app___pas__dbuser.t_cs_policy_change pc
			            on ac.accept_id = pc.accept_id) e
			 where c.policy_chg_id = e.policy_chg_id
			   and e.service_code = 'RR'
		]]>
		<if test=" accept_id  != null and  accept_id  != '' "><![CDATA[ AND e.accept_id = #{accept_id} ]]></if>
			</select>
	
	
	<!-- 按索引查询操作 -->	
	<select id="findRenewChanges" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.business_Prd_Id old_busi_prd_id  from APP___PAS__DBUSER.T_BUSINESS_PRODUCT a where 1=1 and  a.product_code_sys= #{old_busi_prd_code}
    ]]>
	</select>
	
	
	<select id="findRenewChangeByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, 
			A.VALID_TIME FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE ROWNUM <=  1 AND A.VALID_STATUS = '0' AND A.RENEW_CHANGE_TYPE IN ('1', '3') ]]>
		<if test=" policy_code != null and  policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" old_busi_prd_id  != null and  old_busi_prd_id  != '' "><![CDATA[ AND A.OLD_BUSI_PRD_ID = #{old_busi_prd_id} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.VALID_TIME >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.VALID_TIME <= #{end_date} ]]></if>
	</select>
	
	<select id="PA_queryServiceType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
			       TCA.SERVICE_TYPE
			  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
			       APP___PAS__DBUSER.T_RENEW_CHANGE       TRC,
			       APP___PAS__DBUSER.t_contract_product  tcp,
			       APP___PAS__DBUSER.T_CS_APPLICATION TCA
			 WHERE 1=1
			   AND TRC.VALID_STATUS = '0'
			   AND TRC.RENEW_CHANGE_TYPE = 3
			   AND A.POLICY_CODE = TRC.POLICY_CODE
			   AND TCP.ITEM_ID = TRC.ITEM_ID
			   AND TCP.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			   AND A.BUSI_PRD_ID = TRC.OLD_BUSI_PRD_ID
			   AND TRC.CHANGE_ID = TCA.CHANGE_ID
			   AND A.RENEW = 1 ]]>
		<if test=" policy_code != null and  policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	
	<select id="findRenewChangeByBusiItemIdForXQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, 
			A.VALID_TIME FROM APP___PAS__DBUSER.T_RENEW_CHANGE A ,  dev_pas.t_contract_busi_prod b 
			WHERE ROWNUM <=  1 AND a.new_busi_prd_id = b.busi_prd_id
   				and a.policy_code = b.policy_code 
   				and a.valid_time = b.validate_date
   				and a.valid_status = '1' ]]>
		<if test=" policy_code != null and  policy_code != ''  "> <![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id != null and  busi_item_id != ''  "> <![CDATA[ AND b.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
		<select id="findAllNewRenewalProcess" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.BUSI_PROD_CODE,A.START_DATE FROM DEV_PAS.T_EN_START_DATE_CFG A WHERE A.BUSI_PROD_CODE = #{busi_prod_code};
		 ]]>
	</select>
	<!-- 查询险种预约转换表相关信息 -->
	<select id="findAllRenewChangeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEW_UNIT, A.NEW_PREM, A.NEW_AMOUNT, A.OLD_AMOUNT, A.OLD_BUSI_PRD_ID, A.ITEM_ID, 
			A.RENEW_CHANGE_TYPE, A.CHANGE_ID, A.PROCESS_TIME, A.POLICY_CHG_ID, A.OLD_PREM, A.POLICY_ID, 
			A.VALID_STATUS, A.ACCEPT_ID, A.OLD_UNIT, A.MASTER_BUSI_ITEM_ID, A.NEW_BUSI_PRD_ID, A.END_CAUSE, 
			A.PRECONT_ID, A.OLD_STD_PREM_AF, A.POLICY_CODE, A.NEW_STD_PREM_AF, A.SAFEGUARD_PLAN, A.VALID_TIME,
			(SELECT A.CONSTANTS_VALUE FROM APP___PAS__DBUSER.T_CONSTANTS_INFO A WHERE A.CONSTANTS_KEY = 'RR_CHECK_DATE_CONFIG') START_DATE,
      		(SELECT TRC.ACCEPT_TIME FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TRC  WHERE A.ACCEPT_ID = TRC.ACCEPT_ID) ACCEPT_TIME 
		  FROM APP___PAS__DBUSER.T_RENEW_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null and  policy_id  != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" old_busi_prd_id  != null and  old_busi_prd_id  != '' "><![CDATA[ AND A.OLD_BUSI_PRD_ID = #{old_busi_prd_id} ]]></if>
		<if test=" policy_code != null and  policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<include refid="renewChangeWhereCondition" />
		<![CDATA[ ORDER BY A.PROCESS_TIME DESC ]]>
	</select>
	
	
	<select id="findAllRRBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE, C.POLICY_CODE, C.BUSI_ITEM_ID, C.VALIDATE_DATE,A.VALIDATE_TIME
                FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
               INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
                  ON A.ACCEPT_ID = B.ACCEPT_ID
               INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
                  ON B.POLICY_ID = C.POLICY_ID
               INNER JOIN APP___PAS__DBUSER.T_RENEW_CHANGE T
                  ON T.ACCEPT_ID = A.ACCEPT_ID
                 AND T.POLICY_CHG_ID = B.POLICY_CHG_ID
                 AND T.POLICY_ID = C.POLICY_ID
                 AND T.NEW_BUSI_PRD_ID = C.BUSI_PRD_ID
               WHERE A.ACCEPT_STATUS = '18'
                 AND T.VALID_STATUS = '1'
                 AND T.RENEW_CHANGE_TYPE = '1'
                 AND C.POLICY_CODE  = #{policy_code} ]]>
	</select>
	
</mapper>


