<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IIdentityCheckSwitchDao">
<!--
	<sql id="PA_identityCheckSwitchWhereCondition">
		<if test=" switch_id  != null "><![CDATA[ AND A.SWITCH_ID = #{switch_id} ]]></if>
		<if test=" switch_status  != null "><![CDATA[ AND A.SWITCH_STATUS = #{switch_status} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryIdentityCheckSwitchBySwitchIdCondition">
		<if test=" switch_id  != null "><![CDATA[ AND A.SWITCH_ID = #{switch_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addIdentityCheckSwitch"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO T_IDENTITY_CHECK_SWITCH(
				INSERT_TIMESTAMP, UPDATE_BY, SWITCH_ID, INSERT_TIME, UPDATE_TIME, UPDATE_TIMESTAMP, INSERT_BY, 
				SWITCH_STATUS ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{switch_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{switch_status, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteIdentityCheckSwitch" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_IDENTITY_CHECK_SWITCH WHERE SWITCH_ID = #{switch_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateIdentityCheckSwitch" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_IDENTITY_CHECK_SWITCH ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SWITCH_STATUS = #{switch_status, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE SWITCH_ID = #{switch_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findIdentityCheckSwitchBySwitchId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SWITCH_ID, 
			A.SWITCH_STATUS FROM T_IDENTITY_CHECK_SWITCH A WHERE 1 = 1  ]]>
		<include refid="PA_queryIdentityCheckSwitchBySwitchIdCondition" />
		<![CDATA[ ORDER BY A.SWITCH_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapIdentityCheckSwitch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SWITCH_ID, 
			A.SWITCH_STATUS FROM T_IDENTITY_CHECK_SWITCH A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SWITCH_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllIdentityCheckSwitch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SWITCH_ID, 
			A.SWITCH_STATUS FROM APP___PAS__DBUSER.T_IDENTITY_CHECK_SWITCH A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SWITCH_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findIdentityCheckSwitchTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_IDENTITY_CHECK_SWITCH A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryIdentityCheckSwitchForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SWITCH_ID, 
			B.SWITCH_STATUS FROM (
					SELECT ROWNUM RN, A.SWITCH_ID, 
			A.SWITCH_STATUS FROM T_IDENTITY_CHECK_SWITCH A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SWITCH_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
