<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="payerAccountLog">
<!--
	<sql id="PA_payerAccountLogWhereCondition">
		<if test=" next_account_id  != null "><![CDATA[ AND A.NEXT_ACCOUNT_ID = #{next_account_id} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" account_name != null and account_name != ''  "><![CDATA[ AND A.ACCOUNT_NAME = #{account_name} ]]></if>
		<if test=" account_bank != null and account_bank != ''  "><![CDATA[ AND A.ACCOUNT_BANK = #{account_bank} ]]></if>
		<if test=" payer_id  != null "><![CDATA[ AND A.PAYER_ID = #{payer_id} ]]></if>
		<if test=" next_account_bank != null and next_account_bank != ''  "><![CDATA[ AND A.NEXT_ACCOUNT_BANK = #{next_account_bank} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
		<if test=" pay_next != null and pay_next != ''  "><![CDATA[ AND A.PAY_NEXT = #{pay_next} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" next_account_name != null and next_account_name != ''  "><![CDATA[ AND A.NEXT_ACCOUNT_NAME = #{next_account_name} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" next_account != null and next_account != ''  "><![CDATA[ AND A.NEXT_ACCOUNT = #{next_account} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayerAccountLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPayerAccountLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			select APP___PAS__DBUSER.S_PAYER_ACCOUNT_LOG__LOG_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG(
				PAY_LOCATION,NEXT_ACCOUNT_ID, ACCOUNT_ID, ACCOUNT_NAME, INSERT_TIME, ACCOUNT_BANK, PAYER_ID, NEXT_ACCOUNT_BANK, 
				UPDATE_TIME, ACCOUNT, PAY_NEXT, INSERT_TIMESTAMP, LOG_ID, PAY_MODE, UPDATE_BY, 
				LIST_ID, NEXT_ACCOUNT_NAME, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, POLICY_ID, 
				NEXT_ACCOUNT,MEDICAL_NO,MEDICAL_PAY_ORDER,MEDICAL_NO_NEXT,MEDICAL_PAY_ORDER_NEXT,LAW_MAN) 
			VALUES (
				#{pay_location, jdbcType=VARCHAR} ,#{next_account_id, jdbcType=NUMERIC}, #{account_id, jdbcType=NUMERIC} , #{account_name, jdbcType=VARCHAR} , SYSDATE , #{account_bank, jdbcType=VARCHAR} , #{payer_id, jdbcType=NUMERIC} , #{next_account_bank, jdbcType=VARCHAR} 
				, SYSDATE , #{account, jdbcType=VARCHAR} , #{pay_next, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{pay_mode, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , #{next_account_name, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{next_account, jdbcType=VARCHAR}, #{medical_no, jdbcType=VARCHAR}, #{medical_pay_order, jdbcType=VARCHAR}
				, #{medical_no_next, jdbcType=VARCHAR}, #{medical_pay_order_next, jdbcType=VARCHAR}, #{law_man, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePayerAccountLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG WHERE LOG_ID=#{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePayerAccountLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    NEXT_ACCOUNT_ID = #{next_account_id, jdbcType=NUMERIC} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
			ACCOUNT_NAME = #{account_name, jdbcType=VARCHAR} ,
			ACCOUNT_BANK = #{account_bank, jdbcType=VARCHAR} ,
		    PAYER_ID = #{payer_id, jdbcType=NUMERIC} ,
			NEXT_ACCOUNT_BANK = #{next_account_bank, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT = #{account, jdbcType=VARCHAR} ,
			PAY_NEXT = #{pay_next, jdbcType=VARCHAR} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			NEXT_ACCOUNT_NAME = #{next_account_name, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			NEXT_ACCOUNT = #{next_account, jdbcType=VARCHAR} ,
			PAY_LOCATION = #{pay_location, jdbcType=VARCHAR} ,
			
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPayerAccountLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_LOCATION,A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.LOG_ID, A.PAY_MODE, 
			A.LIST_ID, A.NEXT_ACCOUNT_NAME, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayerAccountLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_LOCATION,A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.LOG_ID, A.PAY_MODE, 
			A.LIST_ID, A.NEXT_ACCOUNT_NAME, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayerAccountLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_LOCATION,A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.LOG_ID, A.PAY_MODE, 
			A.LIST_ID, A.NEXT_ACCOUNT_NAME, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayerAccountLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayerAccountLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NEXT_ACCOUNT_ID, B.ACCOUNT_ID, B.ACCOUNT_NAME, B.ACCOUNT_BANK, B.PAYER_ID, B.NEXT_ACCOUNT_BANK, 
			B.ACCOUNT, B.PAY_NEXT, B.LOG_ID, B.PAY_MODE, 
			B.LIST_ID, B.NEXT_ACCOUNT_NAME, B.LOG_TYPE, B.POLICY_CHG_ID, B.POLICY_ID, 
			B.NEXT_ACCOUNT FROM (
					SELECT ROWNUM RN, A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.LOG_ID, A.PAY_MODE, 
			A.LIST_ID, A.NEXT_ACCOUNT_NAME, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
