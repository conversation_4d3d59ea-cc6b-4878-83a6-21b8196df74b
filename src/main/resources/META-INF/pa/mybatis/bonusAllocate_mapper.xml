<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBonusAllocateDao">
	<sql id="bonusAllocateWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" sa_change_id  != null "><![CDATA[ AND A.SA_CHANGE_ID = #{sa_change_id} ]]></if>
		<if test=" allocate_id  != null "><![CDATA[ AND A.ALLOCATE_ID = #{allocate_id} ]]></if>
		<if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" origin_bonus_sa  != null "><![CDATA[ AND A.ORIGIN_BONUS_SA = #{origin_bonus_sa} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND A.ALLOCATE_DATE = #{allocate_date} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" allocate_type != null and allocate_type != ''  "><![CDATA[ AND A.ALLOCATE_TYPE = #{allocate_type} ]]></if>
		<if test=" bonus_rate  != null "><![CDATA[ AND A.BONUS_RATE = #{bonus_rate} ]]></if>
		<if test=" dividend_choice  != null "><![CDATA[ AND A.DIVIDEND_CHOICE = #{dividend_choice} ]]></if>
		<if test=" cash_bonus  != null "><![CDATA[ AND A.CASH_BONUS = #{cash_bonus} ]]></if>
		<if test=" interest_margin  != null "><![CDATA[ AND A.INTEREST_MARGIN = #{interest_margin} ]]></if>
		<if test=" mortality_margin  != null "><![CDATA[ AND A.MORTALITY_MARGIN = #{mortality_margin} ]]></if>
		<if test=" reissue_interest  != null "><![CDATA[ AND A.REISSUE_INTEREST = #{reissue_interest} ]]></if>
		<if test=" bonus_doc_date  != null  and  bonus_doc_date  != ''  "><![CDATA[ AND A.Bonus_Doc_Date = #{bonus_doc_date} ]]></if>
		<if test=" allocate_due_date  != null "><![CDATA[  AND A.ALLOCATE_DUE_DATE = #{allocate_due_date} ]]></if>
	</sql>



<!-- 按索引生成的查询条件 -->	
	<sql id="queryBonusAllocateByAllocateIdCondition">
		<if test=" allocate_id  != null "><![CDATA[ AND A.ALLOCATE_ID = #{allocate_id} ]]></if>
	</sql>	

	<sql id="queryBonusAllocateByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	
	<sql id="queryBonusAllocateByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

	<sql id="queryBonusAllocateCountCondition">
		<if test=" contNo != null and contNo != ''  "><![CDATA[ AND A.POLICY_CODE = #{contNo} ]]></if>
		<if test=" bonusAllot != null and bonusAllot != ''  "><![CDATA[ AND A.BONUS_ALLOT in (#{bonusAllot}) ]]></if>
		<if test=" bonusDecStart  != null and bonusDecStart != '' and bonusDecEnd != null and bonusDecEnd != '' "><![CDATA[ AND A.ALLOCATE_DATE BETWEEN to_date(#{bonusDecStart},'yyyy-MM-DD') AND to_date(#{bonusDecEnd},'yyyy-MM-DD') ]]></if>
	</sql>

<!-- 添加操作 -->
	<insert id="addBonusAllocate"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="allocate_id">
				 SELECT APP___PAS__DBUSER.S_PAY_PLAN__PLAN_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BONUS_ALLOCATE(
				MONEY_CODE, SA_CHANGE_ID, ALLOCATE_ID, BONUS_ALLOT, BONUS_SA, INSERT_TIME, UPDATE_TIME, 
				ITEM_ID, INSERT_TIMESTAMP, ORIGIN_BONUS_SA, POLICY_CODE, UPDATE_BY, ALLOCATE_DATE, 
			    UPDATE_TIMESTAMP, POLICY_CHG_ID, BUSI_ITEM_ID, INSERT_BY, POLICY_ID, ALLOCATE_TYPE, 
				BONUS_RATE, DIVIDEND_CHOICE,CASH_BONUS, INTEREST_MARGIN,MORTALITY_MARGIN,REISSUE_INTEREST,BONUS_DOC_DATE,
				RATE_RELEASE_DATE, TERMINAL_BONUS_RATE, VALID_AMOUNT, STAND_AMOUNT, BONUS_SUM, INTEREST_SUM, ALLOCATE_DUE_DATE,MARGIN_RATE,
				DEATH_MARGIN_RATE,INVEST_RATE,PRICE_DEATH_RATE,EXPERIENCE_DEATH_RATE)  
			VALUES (
				#{money_code, jdbcType=VARCHAR}, #{sa_change_id, jdbcType=NUMERIC} , #{allocate_id, jdbcType=NUMERIC} , #{bonus_allot, jdbcType=VARCHAR} , #{bonus_sa, jdbcType=NUMERIC} , SYSDATE , SYSDATE 
				, #{item_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{origin_bonus_sa, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} ,  #{allocate_date, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{allocate_type, jdbcType=VARCHAR} 
				, #{bonus_rate, jdbcType=NUMERIC} , #{dividend_choice, jdbcType=NUMERIC} , #{cash_bonus, jdbcType=NUMERIC}, #{interest_margin, jdbcType=NUMERIC}, #{mortality_margin, jdbcType=NUMERIC}, #{reissue_interest, jdbcType=NUMERIC},
				#{bonus_doc_date, jdbcType=DATE}, #{rate_release_date, jdbcType=DATE}, #{terminal_bonus_rate, jdbcType=NUMERIC}, #{valid_amount, jdbcType=NUMERIC}, #{stand_amount, jdbcType=NUMERIC}, #{bonus_sum, jdbcType=NUMERIC}, 
				#{interest_sum, jdbcType=NUMERIC}, #{allocate_due_date, jdbcType=DATE},#{margin_rate, jdbcType=NUMERIC},#{death_margin_rate, jdbcType=NUMERIC},#{invest_rate, jdbcType=NUMERIC},#{price_death_rate, jdbcType=NUMERIC},#{experience_death_rate, jdbcType=NUMERIC})   
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteBonusAllocate" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE WHERE  ALLOCATE_ID = #{allocate_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateBonusAllocate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BONUS_ALLOCATE ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    SA_CHANGE_ID = #{sa_change_id, jdbcType=NUMERIC} ,
			BONUS_ALLOT = #{bonus_allot, jdbcType=VARCHAR} ,
		    BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    ORIGIN_BONUS_SA = #{origin_bonus_sa, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    ALLOCATE_DATE = #{allocate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			ALLOCATE_TYPE = #{allocate_type, jdbcType=VARCHAR} ,
		    BONUS_RATE = #{bonus_rate, jdbcType=NUMERIC} ,
		    DIVIDEND_CHOICE = #{dividend_choice, jdbcType=NUMERIC} ,
		    CASH_BONUS = #{cash_bonus, jdbcType=NUMERIC} ,
		    INTEREST_MARGIN = #{interest_margin, jdbcType=NUMERIC} ,
		    MORTALITY_MARGIN = #{mortality_margin, jdbcType=NUMERIC} ,
		    REISSUE_INTEREST = #{reissue_interest, jdbcType=NUMERIC} ,
		    Bonus_Doc_Date = #{bonus_doc_date, jdbcType=DATE} ,
		  	VALID_FLAG = #{valid_flag, jdbcType=NUMERIC} ,		  	
		  	VALID_AMOUNT =#{valid_amount, jdbcType=NUMERIC},
		  	STAND_AMOUNT= #{stand_amount, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE  ALLOCATE_ID = #{allocate_id}  ]]>
	</update>
 
<!-- 按索引查询操作 -->	
	<select id="findBonusAllocateByAllocateId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.SA_CHANGE_ID, A.ALLOCATE_ID, A.BONUS_ALLOT, A.BONUS_SA, 
			A.ITEM_ID, A.ORIGIN_BONUS_SA, A.POLICY_CODE,  A.ALLOCATE_DATE, 
			 A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.ALLOCATE_TYPE, 
			A.BONUS_RATE, A.DIVIDEND_CHOICE ,A.CASH_BONUS, A.INTEREST_MARGIN,A.MORTALITY_MARGIN,A.REISSUE_INTEREST,A.Bonus_Doc_Date FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1  ]]>
		<include refid="bonusAllocateWhereCondition" />
	</select>
	
	
	<select id="findBonusAllocateByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.SA_CHANGE_ID, A.ALLOCATE_ID, A.BONUS_ALLOT, A.BONUS_SA, 
			A.ITEM_ID, A.ORIGIN_BONUS_SA, A.POLICY_CODE,  A.ALLOCATE_DATE, 
			 A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.ALLOCATE_TYPE, 
			A.BONUS_RATE, A.DIVIDEND_CHOICE,A.CASH_BONUS, A.INTEREST_MARGIN,A.MORTALITY_MARGIN,A.REISSUE_INTEREST,A.Bonus_Doc_Date FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1  ]]>
		<include refid="queryBonusAllocateByPolicyChgIdCondition" />
	</select>
	
	
	<select id="findBonusAllocateByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.MONEY_CODE, A.SA_CHANGE_ID, A.ALLOCATE_ID, A.BONUS_ALLOT, A.BONUS_SA, 
			A.ITEM_ID, A.ORIGIN_BONUS_SA, A.POLICY_CODE,  A.ALLOCATE_DATE, 
			 A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.ALLOCATE_TYPE, 
			A.BONUS_RATE, A.DIVIDEND_CHOICE ,A.CASH_BONUS, A.INTEREST_MARGIN,A.MORTALITY_MARGIN,A.REISSUE_INTEREST,A.Bonus_Doc_Date FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1  ]]>
		<include refid="queryBonusAllocateByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapBonusAllocate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.SA_CHANGE_ID, A.ALLOCATE_ID, A.BONUS_ALLOT, A.BONUS_SA, 
			A.ITEM_ID, A.ORIGIN_BONUS_SA, A.POLICY_CODE,  A.ALLOCATE_DATE, 
			 A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.ALLOCATE_TYPE, 
			A.BONUS_RATE, A.DIVIDEND_CHOICE ,A.CASH_BONUS, A.INTEREST_MARGIN,A.MORTALITY_MARGIN,A.REISSUE_INTEREST,A.Bonus_Doc_Date FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllBonusAllocate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE,
         A.SA_CHANGE_ID,
         A.ALLOCATE_ID,
         A.BONUS_ALLOT,
         A.BONUS_SA,
         A.ITEM_ID,
         A.ORIGIN_BONUS_SA,
         A.POLICY_CODE,
         A.ALLOCATE_DATE,
         A.POLICY_CHG_ID,
         A.BUSI_ITEM_ID,
         A.POLICY_ID,
         A.ALLOCATE_TYPE,
         A.ALLOCATE_DUE_DATE,
         A.BONUS_RATE,
         A.DIVIDEND_CHOICE,
         A.CASH_BONUS,
         A.INTEREST_MARGIN,
         A.MORTALITY_MARGIN,
         A.REISSUE_INTEREST,
         A.Bonus_Doc_Date,
         A.BONUS_SUM,
         A.INTEREST_SUM,
         A.VALID_FLAG
    FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A
   WHERE ROWNUM <= 1000 ]]>
		<include refid="bonusAllocateWhereCondition" />
		<![CDATA[ ORDER BY A.ALLOCATE_DATE  DESC]]>
	</select>
	

<!-- 查询个数操作 -->
	<select id="findBonusAllocateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1  ]]>
	    <include refid="bonusAllocateWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="queryBonusAllocateForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MONEY_CODE, B.SA_CHANGE_ID, B.ALLOCATE_ID, B.BONUS_ALLOT, B.BONUS_SA, 
			B.ITEM_ID, B.ORIGIN_BONUS_SA, B.POLICY_CODE,  B.ALLOCATE_DATE, 
			B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID, B.ALLOCATE_TYPE, 
			B.BONUS_RATE, B.DIVIDEND_CHOICE FROM (
					SELECT ROWNUM RN, A.MONEY_CODE, A.SA_CHANGE_ID, A.ALLOCATE_ID, A.BONUS_ALLOT, A.BONUS_SA, 
			A.ITEM_ID, A.ORIGIN_BONUS_SA, A.POLICY_CODE,  A.ALLOCATE_DATE, 
			 A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.ALLOCATE_TYPE, 
			A.BONUS_RATE, A.DIVIDEND_CHOICE FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询分红个数操作 -->
	<select id="findBonusAllocateCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 ]]>
		<include refid="queryBonusAllocateCountCondition" />	
		<if test="busiItemList != null and busiItemList.size()!=0"><![CDATA[ AND A.BUSI_ITEM_ID IN]]>
				<foreach collection="busiItemList" item="busiItemList"
					index="index" open="(" close=")" separator=",">
					#{busiItemList}
				</foreach>
		</if>	     
	</select>

	<!-- 查询分红金额操作 -->
	<select id="findBonusAllocateBonusTotal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(NVL(BONUS_SA,0)) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 ]]>
		<include refid="queryBonusAllocateCountCondition" />	
		<if test="busiItemList != null and busiItemList.size()!=0"><![CDATA[ AND A.BUSI_ITEM_ID IN]]>
				<foreach collection="busiItemList" item="busiItemList"
					index="index" open="(" close=")" separator=",">
					#{busiItemList}
				</foreach>
			</if>
	</select>
	
	<!--分红历史信息查询 -->
	<!-- FROM条件中LIST_ID的NVL实际是取A.RATE_RELEASE_DATE就行，可能没迁移暂取nvl -->
	<select id="queryBonusHisInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<if test=" count_page == 'page' "><![CDATA[select * from (select rownum rn,res.* from (]]></if>
		<choose>
			<when test=" count_page == 'count' ">
				<![CDATA[ select count(1) total_num ]]>
			</when>
			<otherwise>
				<![CDATA[ 
		select        
                    d.busi_item_id,d.old_pol_no,d.BUSI_PRD_ID,b.CHARGE_YEAR,b.VALIDATE_DATE,A.RATE_RELEASE_DATE,b.PRODUCT_ID,a.allocate_date,a.ALLOCATE_DUE_DATE,a.BONUS_DOC_DATE,
					d.busi_prod_code riskNo,a.REISSUE_INTEREST, 
					a.cash_bonus,
				    (select product_name_sys from APP___PAS__DBUSER.T_BUSINESS_PRODUCT where product_code_sys=d.BUSI_PROD_CODE) riskName, 
				    b.amount baseAmnt, a.bonus_sa, a.origin_bonus_sa, a.stand_amount bonusBaseAmnt,
				    (select nvl(sum(BONUS_SA),0) from APP___PAS__DBUSER.T_BONUS_ALLOCATE 
				      where item_id=a.ITEM_ID and ALLOCATE_DATE<=a.ALLOCATE_DATE) availableamnt,				    
				    TO_CHAR(nvl(a.ALLOCATE_DUE_DATE,a.ALLOCATE_DATE), 'YYYY') - 1 AS policy_year,
				     a.ALLOCATE_TYPE, a.bonus_allot,a.BONUS_RATE,a.VALID_AMOUNT,a.terminal_bonus_rate,
				    c.INTERNAL_ID dutyCode, c.PRODUCT_NAME dutyName, c.OPTION_TYPE choflag
				]]>
			</otherwise>
		</choose>
		<![CDATA[ 
		    from APP___PAS__DBUSER.T_BONUS_ALLOCATE a,APP___PAS__DBUSER.T_CONTRACT_PRODUCT b,APP___PAS__DBUSER.T_PRODUCT_LIFE c,
	APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD d 
	where 1=1
	and a.ITEM_ID=b.ITEM_ID and b.BUSI_ITEM_ID=d.BUSI_ITEM_ID and b.PRODUCT_ID=c.PRODUCT_ID and a.policy_code= #{policy_code}
		]]>
		<if test=" startDate != null and startDate != '' "> 
		           <![CDATA[ AND a.RATE_RELEASE_DATE >= #{startDate}]]>
		</if>
		<if test=" endDate != null and endDate != '' "> 
		           <![CDATA[ AND a.RATE_RELEASE_DATE <= #{endDate}]]>
		</if>
		<if test="allocate_type != null and allocate_type != ''"> 
		           <![CDATA[ AND a.ALLOCATE_TYPE= #{allocate_type}]]>
		</if>
		<if test=" order_context != null and order_context != ''  "><![CDATA[
			${order_context}
		]]></if>
		<if test=" count_page == 'page' ">
			<![CDATA[) res) where rn>=#{row_num_start} and rn<#{row_num_end}]]>
		</if>
	</select>
	
	<!-- 查询分红历史信息查保额红利因子表(T_REV_BONUS_RATE) -->
	<select id="queryRevBonusRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
        select * from APP___PDS__DBUSER.T_REV_BONUS_RATE r where 
	r.product_id=#{PRODUCT_ID,jdbcType=NUMERIC}
	and r.BUSINESS_PRD_ID =#{BUSI_PRD_ID,jdbcType=NUMERIC}
	and r.RATE_TYPE='2' 
	and r.CHARGE_YEAR=#{CHARGE_YEAR,jdbcType=NUMERIC}
	and r.POLICY_YEAR=trunc(months_between(#{ALLOCATE_DATE,jdbcType=DATE},#{VALIDATE_DATE,jdbcType=DATE})/12)
	and r.LIST_ID=(select rb.list_id from dev_pds.T_REV_BONUS_RATE rb where rb.PRODUCT_ID=r.PRODUCT_ID and rb.BUSINESS_PRD_ID=r.BUSINESS_PRD_ID
	    and rb.RATE_TYPE=r.RATE_TYPE and rb.CHARGE_YEAR=r.CHARGE_YEAR and rb.POLICY_YEAR=r.POLICY_YEAR and rb.RATE_RELEASE_DATE=
		nvl(#{RATE_RELEASE_DATE,jdbcType=DATE},(select max(RATE_RELEASE_DATE) from dev_pds.T_REV_BONUS_RATE a where a.BUSINESS_PRD_ID=rb.BUSINESS_PRD_ID and a.product_id=rb.product_id
			and a.RATE_TYPE=rb.RATE_TYPE and a.CHARGE_YEAR=rb.CHARGE_YEAR and a.POLICY_YEAR=rb.POLICY_YEAR and a.RATE_RELEASE_DATE<=#{BONUS_DOC_DATE,jdbcType=DATE})) 
		and rownum=1)
		]]>		
	</select>
	
	<!-- 查询最近分红日 -->
	<select id="findLastBonusTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select max(A.allocate_date) as allocate_date from APP___PAS__DBUSER.t_bonus_allocate A where 1 = 1
				and A.VALID_FLAG <> 0 
		]]>
		<include refid="bonusAllocateWhereCondition" />	
	</select>
	
	<!-- 年度分红业绩报告书接口  -->
	<select id="queryYearBonusReportfindBonus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STAND_AMOUNT,A.BONUS_CV,
       A.TOTAL_BONUS_CV,
       A.VALID_AMOUNT,
       A.BONUS_DOC_DATE, A.ALLOCATE_DUE_DATE, A.TERMINAL_BONUS_RATE,
       A.TERMINAL_BONUS,A.MONEY_CODE, A.SA_CHANGE_ID, A.ALLOCATE_ID, A.BONUS_ALLOT, A.BONUS_SA, 
			A.ITEM_ID, A.ORIGIN_BONUS_SA, A.POLICY_CODE,A.ALLOCATE_DATE,
			 A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.ALLOCATE_TYPE, 
			A.BONUS_RATE, A.DIVIDEND_CHOICE,A.CASH_BONUS, A.INTEREST_MARGIN,A.MORTALITY_MARGIN,A.REISSUE_INTEREST FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE ROWNUM <=  1000  ]]>
		<if test =" policy_id  != null and  policy_id !='' ">
			 <![CDATA[ and A.POLICY_ID= #{policy_id} ]]>
		</if>
		
		<if test="create_strat_time != null and create_strat_time !='' and create_end_time != null and create_end_time !=''  "  >
			<![CDATA[
				and A.ALLOCATE_DATE > #{create_strat_time} 
				and A.ALLOCATE_DATE < #{create_end_time} 
			]]>
		</if>
		
		<if test="bonus_allot != null and bonus_allot != '' ">
			<![CDATA[
				and A.BONUS_ALLOT = #{bonus_allot}
			]]>
		</if>
		order by A.ALLOCATE_DATE desc
	</select>
	<!-- 年度分红业绩报告书接口 _现金分红接口 -->
	<select id="queryYearBonusReportForCash" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		      select 
		       tba.BONUS_SUM,
		       tba.allocate_date,
		       tba.bonus_sa,
		       tba.REISSUE_INTEREST,
		       tba.interest_sum,
		       tba.CASH_BONUS,
		       tba.busi_item_id,
		       tba.item_id, 
		       tba.policy_id,
		       (select tsm.mode_name from dev_pas.t_pay_plan tp,dev_pas.t_survival_mode tsm  where tp.PAY_PLAN_TYPE = '1'
		       and tp.policy_code = #{policy_code} and tsm.mode_code=tp.survival_mode and rownum = '1') survival_mode_name
		      from dev_pas.t_bonus_allocate tba
		      where tba.policy_code = #{policy_code}
		      and tba.bonus_allot = '4'
	    ]]>
	
	<!-- -
	
			  SELECT 
		        PD.PAY_DUE_DATE ALLOCATE_DATE/*派发日*/,PD.BUSI_ITEM_ID,PD.POLICY_ID,PD.ITEM_ID,
		        PD.SURVIVAL_MODE /*领取方式*/,PD.FEE_AMOUNT/*红利*/,PD.REISSUE_INTEREST/*迟发利息*/,
		        (select tsm.mode_name from dev_pas.t_survival_mode tsm where tsm.mode_code=pp.survival_mode ) survival_mode_name
		        FROM APP___PAS__DBUSER.T_PAY_PLAN PP
		        JOIN APP___PAS__DBUSER.T_PAY_DUE PD ON PD.PLAN_ID = PP.PLAN_ID
		        WHERE PP.PAY_PLAN_TYPE = 1
		 
		<if test =" policy_id  != null and  policy_id !='' ">
			 <![CDATA[ AND PP.POLICY_ID= #{policy_id} ]]>
		</if>
		 <![CDATA[  ORDER BY PD.PAY_DUE_DATE DESC  ]]>
		 
		  -->
	</select>
	<!-- 分红类型判断接口 -->
	<!--和需求确认，修改取值逻辑  -->
	<select id="findJudgeBonusType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.policy_code,
			   a.bonus_allot,
			   a.item_id,
			   (select b.type_name
				  from APP___PAS__DBUSER.t_bonus_allot b
				 where b.bonus_allot = a.bonus_allot) bonus_allot_name
            from APP___PAS__DBUSER.t_bonus_allocate a
		    where a.policy_code = #{policy_code}
		    and a.allocate_date=(select max(c.allocate_date) from  dev_pas.t_bonus_allocate c
		         where c.policy_code=#{policy_code}) 
		    ]]>
			 
	</select>
	
	<!-- 查询出险日之后的首次现金分红日  -->
	<select id="PA_findMinBonusDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT min(ALLOCATE_DATE) ALLOCATE_DATE FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A
			      WHERE 1 = 1
			        AND A.POLICY_ID = #{policy_id} ]]>
		<if test="allocate_date != null and allocate_date != '' "> <![CDATA[ AND A.ALLOCATE_DATE > #{allocate_date}]]> </if>
		<if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
	</select>
	
	<!-- 查询出险日之后的现金红利、补发利息  -->
	<select id="PA_findHistoryBonus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.POLICY_ID,
			            sum(CASH_BONUS) CASH_BONUS,
			            sum(REISSUE_INTEREST) REISSUE_INTEREST
			       FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A
				   JOIN APP___PAS__DBUSER.t_pay_plan b
				    ON 	A.POLICY_ID = B.POLICY_ID
			      WHERE 1 = 1 ]]>
		<if test=" policy_id  != null and  policy_id !='' "> <![CDATA[ AND A.POLICY_ID= #{policy_id} ]]> </if>
		<if test=" allocate_date != null and allocate_date != '' "> <![CDATA[ AND A.ALLOCATE_DATE >= #{allocate_date}]]> </if>
		<if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
		<if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND B.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
		<if test=" survival_mode_list != null and survival_mode_list != ''  ">
			<![CDATA[ AND B.SURVIVAL_MODE IN ]]>
			<foreach collection="survival_mode_list" item="survival_mode"	open="(" separator="," close=")">
					(#{survival_mode})
			</foreach>
		</if>
		<if test=" survival_mode != null and survival_mode != ''  "><![CDATA[ AND B.SURVIVAL_MODE = (#{survival_mode}) ]]></if>
		<![CDATA[ GROUP BY A.POLICY_ID ]]>
	</select>
	<!--  -->
	<select id="findBonusAllocatebyPolicyCode" resultType="java.util.Map" parameterType="java.util.Map" >
<![CDATA[	select NVL(sum(REISSUE_INTEREST),0) as TotalBonusInterest from APP___PAS__DBUSER.t_bonus_allocate A 

  where 1=1  ]]>
	<include refid="queryBonusAllocateCountCondition" />
	 <if test="busiItemList != null and busiItemList.size()!=0"><![CDATA[ AND A.BUSI_ITEM_ID IN]]>
				<foreach collection="busiItemList" item="busiItemList"
					index="index" open="(" close=")" separator=",">
					#{busiItemList}
				</foreach>
    </if>
	</select>
	
	<select id="PA_findHistoryBonusSum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT NVL(SUM(T.CASH_BONUS), 0) CASH_BONUS, 
				   NVL(SUM(T.REISSUE_INTEREST), 0) REISSUE_INTEREST
		  FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE T
		 WHERE T.ITEM_ID = #{item_id} ]]>
	</select>
	
	<!-- 查询红利分红日 -->
	<select id="PA_findBonusALLDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT max(t.allocate_date) allocate_date ,  NVL(SUM(t.bonus_sa), 0) sum_bonus_sa
		  FROM APP___PAS__DBUSER.t_bonus_allocate t
		 WHERE t.bonus_allot in (1, 2, 6)
		   and t.allocate_type = '02' 
		   and t.item_id = #{item_id}		   
		 ]]>
		 <if test=" start_date  != null "><![CDATA[  and t.allocate_date >= #{start_date} ]]></if>
		 <if test=" allocate_date  != null "><![CDATA[  and t.allocate_date <= #{allocate_date} ]]></if>  
	</select>
	
	
	<!-- 查询前三年分红的详细信息 -->
	<select id="findFirstThreeYears" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
       SELECT 
           TBA.ALLOCATE_ID,
           TBA.BUSI_ITEM_ID,
          TBA.ITEM_ID,
           TBA.ALLOCATE_DATE,
           TBA.POLICY_ID,
           TBA.POLICY_CODE,
           TBA.ALLOCATE_TYPE,
           TBA.SA_CHANGE_ID,
           TBA.MONEY_CODE,
           TBA.BONUS_ALLOT,
           TBA.DIVIDEND_CHOICE,
           TBA.BONUS_RATE,
           TBA.BONUS_SA,
           TBA.BONUS_CV,
           TBA.ORIGIN_BONUS_SA,
           TBA.ORIGIN_BONUS_CV,
           TBA.TOTAL_BONUS_CV,
           TBA.TERMINAL_BONUS,
           TBA.CASH_BONUS,
           TBA.INTEREST_MARGIN,
           TBA.MORTALITY_MARGIN,
           TBA.REISSUE_INTEREST,
           TBA.POLICY_CHG_ID,
           TBA.BONUS_DOC_DATE,
           TBA.RATE_RELEASE_DATE,
           TBA.TERMINAL_BONUS_RATE,
           TBA.VALID_AMOUNT,
           TBA.STAND_AMOUNT,
           TBA.BONUS_SUM,
           TBA.INTEREST_SUM,
           TBA.ALLOCATE_DUE_DATE,
           tct.is_master_item
      FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE tba,
      dev_pas.t_contract_product tct
      where 1=1
      and tba.busi_item_id = tct.busi_item_id
      and tba.item_id = tct.item_id
      and tba.policy_code = #{policy_code}
      and to_char(tba.allocate_date,'yyyy')
      in(
      select years from ( select years,rownum rn from (select to_char(te.allocate_date,'yyyy') years from APP___PAS__DBUSER.t_bonus_allocate te where te.policy_code  = #{policy_code}

      group by to_char(te.allocate_date, 'yyyy') 
      order by to_char(te.allocate_date, 'yyyy') desc)) where rn<4)
       order by  to_char(tba.allocate_date,'yyyy') desc
	 ]]>     
	</select>
	
	
	<!-- 查询现金分红历史信息 -->
	
	<select id="queryHistoryInfo" resultType="java.util.Map" parameterType="java.util.Map">
		
		<choose>
		     <when test="rowNumStart!=null and totalCount!=null">
		       <![CDATA[
		       select 
		       b.allocate_date,
		       b.allocate_due_date,
		       b.busi_item_id,
		       b.cash_bonus,
		       b.reissue_interest,
		       b.product_name,
		       b.survival_mode,
		       b.bonus_sum,
		       b.item_id,
		       b.duty_name

		       from (
		       select
		       rownum rn, 
               tba.allocate_date,
               tba.allocate_due_date,
               tba.busi_item_id,
               tba.cash_bonus,
               tba.reissue_interest,
               (select tbp.product_name_sys from APP___PAS__DBUSER.t_business_product tbp,APP___PAS__DBUSER.t_contract_busi_prod tcbp where tcbp.busi_item_id = tba.busi_item_id
               and tcbp.busi_prd_id = tbp.business_prd_id ) product_name,
               (select (select e.mode_name from APP___PAS__DBUSER.T_SURVIVAL_MODE e where e.mode_code=tpl.survival_mode) from APP___PAS__DBUSER.t_pay_plan tpl 
               where tpl.policy_code = #{policy_code} and tpl.pay_plan_type = '1' and rownum='1') survival_mode,
               tba.bonus_sum,
               tba.item_id,
               (select tpf.product_name from  
               APP___PAS__DBUSER.t_contract_product tcp,APP___PAS__DBUSER.t_product_life tpf where tcp.item_id = tba.item_id and tpf.product_id = tcp.product_id) duty_name
               from APP___PAS__DBUSER.t_bonus_allocate tba where 1=1
               and tba.bonus_allot = '4'
               and tba.policy_code = #{policy_code}
               and rownum<=#{totalCount,jdbcType=NUMERIC} 
               )b where b.rn>=#{rowNumStart,jdbcType=NUMERIC}
               
               ]]>
               <if test="orderbySort!=null and orderbySort!=''">
                  order by ${orderbySort}   
               </if>
                 
		     </when>
		     <otherwise>
		        <![CDATA[ 
		       select 
               tba.allocate_date,
               tba.allocate_due_date,
               tba.busi_item_id,
               tba.cash_bonus,
               tba.reissue_interest,
               (select tbp.product_name_sys from APP___PAS__DBUSER.t_business_product tbp,APP___PAS__DBUSER.t_contract_busi_prod tcbp where tcbp.busi_item_id = tba.busi_item_id
               and tcbp.busi_prd_id = tbp.business_prd_id ) product_name,
               (select (select e.mode_name from APP___PAS__DBUSER.T_SURVIVAL_MODE e where e.mode_code=tpl.survival_mode) from APP___PAS__DBUSER.t_pay_plan tpl 
               where tpl.policy_code = #{policy_code} and tpl.pay_plan_type = '1' and rownum='1') survival_mode,
               tba.bonus_sum,
               tba.item_id,
               (select tpf.product_name from  
               APP___PAS__DBUSER.t_contract_product tcp,APP___PAS__DBUSER.t_product_life tpf where tcp.item_id = tba.item_id and tpf.product_id = tcp.product_id) duty_name
               from APP___PAS__DBUSER.t_bonus_allocate tba where 1=1
               and tba.bonus_allot = '4'
               and tba.policy_code = #{policy_code} 
               order by tba.allocate_date desc	   
		 ]]>
		     </otherwise>
		</choose>
		
	</select>
	<!-- 查现金分红金额操作 -->
	<select id="findBonusCashBonusTotal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(NVL(CASH_BONUS,0)) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 ]]>
		<include refid="queryBonusAllocateCountCondition" />	
		<if test="busiItemList != null and busiItemList.size()!=0"><![CDATA[ AND A.BUSI_ITEM_ID IN]]>
				<foreach collection="busiItemList" item="busiItemList"
					index="index" open="(" close=")" separator=",">
					#{busiItemList}
				</foreach>
		</if>	
	</select>
	
	<!-- 查询所有操作 -->
	<select id="queryBonusSa" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE,
         A.SA_CHANGE_ID,
         A.ALLOCATE_ID,
         A.BONUS_ALLOT,
         (A.Valid_Amount- A.STAND_AMOUNT) as SUM_BONUS_SA,
         A.BONUS_SA,
         A.ITEM_ID,
         A.ORIGIN_BONUS_SA,
         A.POLICY_CODE,
         A.ALLOCATE_DATE,
         A.POLICY_CHG_ID,
         A.BUSI_ITEM_ID,
         A.POLICY_ID,
         A.ALLOCATE_TYPE,
         A.ALLOCATE_DUE_DATE,
         A.BONUS_RATE,
         A.DIVIDEND_CHOICE,
         A.CASH_BONUS,
         A.INTEREST_MARGIN,
         A.MORTALITY_MARGIN,
         A.REISSUE_INTEREST,
         A.Bonus_Doc_Date,
         A.BONUS_SUM,
         A.INTEREST_SUM,
         A.Valid_Amount,
         tcp.BONUS_MODE,
         tct.product_name_sys,
         (select z.agent_code from APP___PAS__DBUSER.t_Contract_Agent z where z.policy_code=tcbp.policy_code and z.is_current_agent=1) agent_code,
         /*技术需求任务 #134610 续收微天下新增收费计划需求对接核心接口意向单-保单管理 start */
         tct.product_abbr_name,
		 /*技术需求任务 #134610 续收微天下新增收费计划需求对接核心接口意向单-保单管理 end */
		 (SELECT tcm.special_account_flag FROM dev_pas.t_contract_master tcm where tcm.policy_id = A.POLICY_ID) as special_account_flag
    FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A,APP___PAS__DBUSER.t_Contract_Busi_Prod tcbp,APP___PAS__DBUSER.t_business_product tct,APP___PAS__DBUSER.T_CONTRACT_PRODUCT tcp
   WHERE ROWNUM <= 1000 ]]>
        and A.Busi_Item_Id = tcbp.busi_item_id
        AND A.ITEM_ID = TCP.ITEM_ID
        and tcbp.busi_prd_id = tct.business_prd_id
        and tcp.busi_item_id =tcbp.busi_item_id
		<include refid="bonusAllocateWhereCondition" />
		<![CDATA[ ORDER BY A.ALLOCATE_DUE_DATE desc  , tcp.PRODUCT_ID ]]>
	</select>
	
	<select id="findRateReleaseDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select min(distinct t.rate_release_date) as rate_release_date  from dev_pas.t_bonus_allocate t where to_char(t.rate_release_date,'yyyy')=#{year} order by t.rate_release_date
		 ]]>
	</select>
	
	
	<!-- leihong 630查询上次应分红日 -->
	<select id="queryLastBonusDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT  MAX(T.ALLOCATE_DUE_DATE) AS ALLOCATE_DUE_DATE 
			  	FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE T
			 WHERE   T.ITEM_ID = #{item_id}		   
		 ]]>		
		 <if test=" allocate_date  != null "><![CDATA[  AND T.ALLOCATE_DUE_DATE <= #{allocate_due_date} ]]></if>  
	</select>
	
	
	<!-- leihong 630 查询 累计红利保额-->
	<select id="querySumBonusSA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT  NVL(SUM(T.BONUS_SA),0)  AS BONUS_SA
			  	FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE T
			 WHERE   T.ITEM_ID = #{item_id}
			 	AND  T.ALLOCATE_DUE_DATE > #{start_date} 
			 	AND  T.ALLOCATE_DUE_DATE<= #{end_date}
		 ]]>		
		 
	</select>
	<!-- 可选责任加保，取分红明细表对应变更ID的有效保额减去基本保额，得到可选责任加保对应的累积红利保额 -->
    <select id="PA_findBonusSaBySaChangeId" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
               select (nvl(a.valid_amount, 0) - nvl(a.stand_amount, 0)) as sum_bonus_sa
  				from APP___PAS__DBUSER.T_BONUS_ALLOCATE a where 1=1
	     ]]>
	     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		 <![CDATA[AND A.bonus_allot IN ]]>
		 <foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		 </if>
	     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
	     <if test=" allocate_due_date  != null  and  allocate_due_date  != ''  "><![CDATA[ AND A.ALLOCATE_DUE_DATE =  #{allocate_due_date} ]]></if>
	     <if test=" sa_change_id  != null "><![CDATA[ AND A.SA_CHANGE_ID = #{sa_change_id} ]]></if>
	     <if test=" sa_change_id  == null "><![CDATA[ AND A.SA_CHANGE_ID is null ]]></if>
    </select>
    
    
    
    <!-- 查询所有操作 -->
	<select id="findAllBonusAllocateForCus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.ALLOCATE_ID,
				       A.BUSI_ITEM_ID,
				       A.ITEM_ID,
				       A.ALLOCATE_DATE,
				       A.POLICY_ID,
				       A.POLICY_CODE,
				       A.ALLOCATE_TYPE,
				       A.SA_CHANGE_ID,
				       A.MONEY_CODE,
				       A.BONUS_ALLOT,
				       A.DIVIDEND_CHOICE,
				       A.BONUS_RATE,
				       A.BONUS_SA,
				       A.BONUS_CV,
				       A.ORIGIN_BONUS_SA,
				       A.ORIGIN_BONUS_CV,
				       A.TOTAL_BONUS_CV,
				       A.TERMINAL_BONUS,
				       A.CASH_BONUS,
				       A.INTEREST_MARGIN,
				       A.MORTALITY_MARGIN,
				       A.REISSUE_INTEREST,
				       A.POLICY_CHG_ID,
				       A.BONUS_DOC_DATE,
				       A.RATE_RELEASE_DATE,
				       A.TERMINAL_BONUS_RATE,
				       A.VALID_AMOUNT,
				       A.STAND_AMOUNT,
				       A.BONUS_SUM,
				       A.INTEREST_SUM,
				       A.ALLOCATE_DUE_DATE,
				       A.VALID_FLAG,
				       A.MARGIN_RATE,
				       A.DEATH_MARGIN_RATE,
				       A.INVEST_RATE,
				       A.PRICE_DEATH_RATE,
				       A.EXPERIENCE_DEATH_RATE
		    FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A
		   WHERE ROWNUM <= 1000 ]]>
				<include refid="bonusAllocateWhereCondition" />		
	</select>
		<select id="findAllYLWLBonusAllocate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT DISTINCT TBA.ALLOCATE_DATE, TBA.REISSUE_INTEREST, TBA.CASH_BONUS
  FROM DEV_PAS.T_PAY_PLAN       TPP,
       DEV_PAS.T_PAY_DUE        TPD,
       DEV_PAS.T_BONUS_ALLOCATE TBA
 WHERE TPP.POLICY_CODE = TPD.POLICY_CODE
   AND TPP.BUSI_ITEM_ID = TPD.BUSI_ITEM_ID
   AND TPD.PLAN_ID = TPP.PLAN_ID
   AND TPP.PAY_PLAN_TYPE = '1'
   AND TPD.FEE_STATUS = '00'
   AND TPD.POLICY_CODE = TBA.POLICY_CODE
   AND TPD.ITEM_ID = TBA.ITEM_ID
   AND TPD.PAY_DUE_DATE = TBA.ALLOCATE_DUE_DATE
   AND TBA.BONUS_ALLOT = '4']]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPD.POLICY_CODE = #{policy_code} ]]></if>   	
	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND TPD.BUSI_ITEM_ID = #{busi_item_id} ]]></if>   	
	</select>
	
	
	
	<select id="find_bonus_CollectionList_info" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select selectAll.PRODUCT_ID,/*险种ID*/
       selectAll.busi_prod_code, /*险种代码*/
       selectAll.PRODUCT_NAME_SYS, /*险种名称*/
       selectAll.survival_mode_name, /*现金红利领取形式*/
       selectAll.BANK_CODE_NAME, /*银行代码/名称*/
       selectAll.BANK_ACCO_NAME, /*账号户名*/
       selectAll.BANK_ACCOUNT, /*银行账号*/
       (case
         when selectAll.RELATION_POLICY_CODE_COUNT > 0 or
              selectAll.RELATION_POLICY_CODE_COUNT1 > 0 or
              selectAll.RELATION_POLICY_CODE_COUNT2 > 0 then
          '是'
         else
          '否'
       end) as universal_account_type, /*约定转入万能账户*/
       (case
         when selectAll.RELATION_POLICY_CODE_COUNT > 0 then
          selectAll.Relation_Policy_Code
         when selectAll.RELATION_POLICY_CODE_COUNT1 > 0 then
          selectAll.SUB_POLICY_CODE
         when selectAll.RELATION_POLICY_CODE_COUNT2 > 0 then
          selectAll.POLICY_CODE
         else
          '--'
       end) as universal_policy, /*万能险保单号*/
       (case
         when selectAll.RELATION_POLICY_CODE_COUNT > 0 then
          (select ls.status_name
             from dev_pas.T_LIABILITY_STATUS ls
            where ls.status_code = (select tcmss.liability_state
                     from dev_pas.t_contract_master tcmss
                    where tcmss.policy_code = selectAll.Relation_Policy_Code))
         when selectAll.RELATION_POLICY_CODE_COUNT1 > 0 then
           (select ls.status_name
             from dev_pas.T_LIABILITY_STATUS ls
            where ls.status_code =
                  (select tcmss.liability_state
                     from dev_pas.t_contract_master tcmss
                    where tcmss.policy_code = selectAll.SUB_POLICY_CODE))
         when selectAll.RELATION_POLICY_CODE_COUNT2 > 0 then
          (select ls.status_name
             from dev_pas.T_LIABILITY_STATUS ls
            where ls.status_code = selectAll.Liability_State
                  )
          
         else
          '--'
       end) as universal_policy_status /*万能险保单效力状态*/
  from (
        
        SELECT CM.POLICY_CODE,
                CR.SUB_POLICY_CODE,
                CR.Master_Policy_Code,
               CM.Relation_Policy_Code,
                CP.PRODUCT_ID,
                CM.Liability_State,
                BP.Busi_Prod_Code as busi_prod_code, /*险种代码*/
                TBP.PRODUCT_NAME_STD AS PRODUCT_NAME_SYS, /*险种名称(显示标准的名称，系统名称是另外一个)*/
                (case
                  when tpp.survival_mode = 1 and
                       (tpp.survival_w_mode = 1 or tpp.survival_w_mode is null) then
                   '现金领取-需办理保全项目领取'
                  when tpp.survival_mode = 1 and tpp.survival_w_mode != 1 then
                   '现金领取-约定银行转账'
                  else
                   (select sm.mode_name
                      from dev_pas.t_survival_mode sm
                     where sm.mode_code = tpp.survival_mode)
                end) as survival_mode_name, /*现金红利领取形式*/
                (case
                  when tpp.survival_mode = 1 and tpp.survival_w_mode = 2 then
                   tba.bank_code || '/' ||
                   (SELECT A.Bank_Name
                      FROM DEV_PAS.t_Bank A
                     where A.Bank_Code = tba.bank_code)
                  when tpp.survival_mode = 1 and tpp.survival_w_mode = 3 then
                   tba.correspondent_no || '/' ||
                   (SELECT A.Correspondent_Name
                      FROM DEV_PAS.T_BANK_OF_DEPOSIT A
                     where A.Correspondent_No = tba.correspondent_no)
                  else
                   ''
                end) as BANK_CODE_NAME, /*银行代码/名称*/
                (case
                  when tpp.survival_mode = 1 and tpp.survival_w_mode in (2, 3) then
                   tba.acco_name
                  ELSE
                   ''
                end) AS BANK_ACCO_NAME, /*账号户名*/
                (case
                  when tpp.survival_mode = 1 and tpp.survival_w_mode in (2, 3) then
                   tba.bank_account
                  ELSE
                   ''
                end) AS BANK_ACCOUNT, /*银行账号*/
                
                (SELECT COUNT(*)
                   FROM (SELECT CMCount.POLICY_ID,
                                CMCount.POLICY_CODE          AS RELATION_POLICY_CODE,
                                CMCount.RELATION_POLICY_CODE AS POLICY_CODE
                           FROM DEV_PAS.T_CONTRACT_MASTER CMCount
                          WHERE CMCount.RELATION_POLICY_CODE = #{contNo}
                            AND CMCount.POLICY_CODE IS NOT NULL
                         UNION
                         SELECT CMCount.POLICY_ID,
                                CMCount.RELATION_POLICY_CODE,
                                CMCount.POLICY_CODE
                           FROM DEV_PAS.T_CONTRACT_MASTER CMCount
                          WHERE CMCount.POLICY_CODE = #{contNo}
                            and CMCount.RELATION_POLICY_CODE IS NOT NULL) B) AS RELATION_POLICY_CODE_COUNT, /*关联保单数量*/
                (SELECT COUNT(1)
                   FROM DEV_PAS.T_CONTRACT_MASTER   TCMA,
                        DEV_PAS.T_CONTRACT_RELATION TCRE
                  WHERE TCMA.POLICY_CODE = TCRE.MASTER_POLICY_CODE
                    AND (TCRE.MASTER_BUSI_ITEM_ID = BP.BUSI_ITEM_ID OR
                        TCRE.SUB_BUSI_ITEM_ID = BP.BUSI_ITEM_ID)) AS RELATION_POLICY_CODE_COUNT1, /*关联保单数量*/
                (select count(1)
                   from dev_pas.T_CONTRACT_BUSI_PROD tcbp
                   left join dev_pas.T_BUSINESS_PRODUCT ttbp
                     on tcbp.busi_prd_id = ttbp.business_prd_id
                  where tcbp.policy_code = #{contNo}
                    and ttbp.product_category1 = '20003')  AS RELATION_POLICY_CODE_COUNT2 /*当前保单是否包含万能险种*/
        
          FROM DEV_PAS.T_CONTRACT_MASTER CM
         INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
            ON BP.POLICY_CODE = CM.POLICY_CODE
         INNER JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
            ON BP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
         INNER JOIN dev_pas.t_pay_plan tpp
            ON TPP.Busi_Item_Id = BP.BUSI_ITEM_ID
           AND TPP.POLICY_CODE = BP.POLICY_CODE
           AND TPP.BUSI_PROD_CODE = BP.BUSI_PROD_CODE
         INNER JOIN (SELECT TP.BUSI_ITEM_ID,
                            TP.BUSI_PROD_CODE,
                            MAX(TP.PLAN_ID) PLAN_ID
                       FROM DEV_PAS.T_PAY_PLAN TP
                      WHERE TP.POLICY_CODE = #{contNo}
                      GROUP BY TP.POLICY_CODE,
                               TP.BUSI_PROD_CODE,
                               TP.BUSI_ITEM_ID) INNTP
            ON TPP.PLAN_ID = INNTP.PLAN_ID
         INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
            ON CP.ITEM_ID = TPP.ITEM_ID
    INNER JOIN DEV_PAS.T_BONUS_ALLOCATE BA
            ON CP.ITEM_ID = BA.ITEM_ID
          LEFT JOIN dev_pas.t_pay_plan_payee PPP
            ON PPP.plan_id = TPP.plan_id
           AND ppp.item_id = TPP.Item_Id
           AND PPP.BUSI_ITEM_ID = TPP.Busi_Item_Id
          LEFT JOIN dev_pas.t_bank_account TBA
            ON TBA.account_id = PPP.payee_account_id
          LEFT JOIN DEV_PAS.T_CONTRACT_RELATION CR
            ON (CM.POLICY_CODE = CR.MASTER_POLICY_CODE OR
               CM.POLICY_CODE = CR.Sub_Policy_Code)
           AND CR.RELATION_TYPE in (2, 4)
         WHERE 1 = 1
           AND TBP.PRODUCT_CATEGORY1 = '20002' /*只显示保单下分红的险种*/
           AND CM.policy_code = #{contNo}
        
        ) selectAll 
		]]>
	</select>
	
	   <select id="find_policy_BonusList_info" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[   
                SELECT
                distinct
                 BA.ALLOCATE_ID,
 	   BP.BUSI_ITEM_ID, --险种ID		 
       BP.BUSI_PROD_CODE, --险种代码
       TBP.PRODUCT_NAME_SYS,
       CP.PRODUCT_CODE, --责任组代码
       TPL.PRODUCT_NAME,
       BA.RATE_RELEASE_DATE,--红利公布日
       CP.AMOUNT,--基本保额
       TO_CHAR(NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE),'YYYY') - 1 AS POLICY_YEAR,--会计年度
       BA.BONUS_ALLOT, --分红类别
       (CASE WHEN BA.BONUS_ALLOT=4 THEN NULL ELSE  BA.BONUS_RATE END ) AS BONUS_RATE , --分红率
       (CASE WHEN BA.BONUS_ALLOT=4 THEN NULL ELSE  BA.TERMINAL_BONUS_RATE END ) AS FINISH_BONUS_RATE, --终了分红率 bug#3017修改
       (BA.Valid_Amount-BA.STAND_AMOUNT) as SUM_BONUS_SA, --累计红利保额
       BA.BONUS_SA, --红利保额
       BA.CASH_BONUS, --现金红利
       BA.REISSUE_INTEREST AS CASH_INTEREST, --现金红利利息
       nvl(BA.ALLOCATE_DUE_DATE,BA.ALLOCATE_DATE) AS ALLOCATE_DATE,--应分配日期
       BA.BONUS_DOC_DATE AS REALLO_DATE,--实际分配日期
       --BA.ALLOCATE_DATE AS ALLOCATE_DATE --应分配日期
       (case
         when tpd.survival_mode = 4 and PAR.fee_status = '16' then
          '已转入万能账户'
         when tpd.survival_mode = 2 and PAR.fee_status = '16' then
          '已进入累积生息'
          when tpd.survival_mode = 1 and CAC.accept_code is null and PAR.pay_mode in (32, 34) then
          '已领取-约定银行转账'
         when tpd.survival_mode = 1 and
              (PAR.service_code is null or
              (cac.accept_status is not null and cac.accept_status != 18)) then
          ''
         when tpd.survival_mode = 1 and
              cac.accept_status is not null and cac.accept_status = 18 then
          '已领取-保全项目'
        
       end) AS SURVIVAL_REALITY,
	  (case
         when tpd.survival_mode = 1 and PAR.pay_mode = 32  and cac.accept_code is null then
          PAR.BANK_CODE || '/' || (SELECT A.Bank_Name
                FROM DEV_PAS.t_Bank A
               where A.Bank_Code = PAR.BANK_CODE)
              
         when tpd.survival_mode = 1 and PAR.pay_mode = 34  and cac.accept_code is null then
             PAR.CIP_DISTRICT_BANK_CODE || '/' ||
             (SELECT A.Correspondent_Name
                FROM DEV_PAS.T_BANK_OF_DEPOSIT A
               where A.Correspondent_No = PAR.CIP_DISTRICT_BANK_CODE)
         ELSE
          ''
       end) AS BANK_CODE_NAME,
       (case
         when tpd.survival_mode = 1 and PAR.pay_mode in (32, 34)  and cac.accept_code is null then
          PAR.BANK_USER_NAME
         ELSE
          ''
       end) AS BANK_ACCO_NAME,
       (case
         when tpd.survival_mode = 1 and PAR.pay_mode in (32, 34)  and cac.accept_code is null then
          PAR.bank_account
         ELSE
          ''
       end) AS BANK_ACCOUNT,
       (case
       	 when tpd.survival_mode = 4 and PAR.fee_status = '16' and CI.INTEREST_CAPITAL > 0  then CM.Policy_Code
         when tpd.survival_mode = 4 and PAR.fee_status = '16' and CR.RELATION_TYPE = 2  then CR.Sub_Policy_Code
         ELSE ''
       
       end) AS RELATION_POLICY_CODE,
       (case
         when tpd.survival_mode = 1 and
              cac.accept_status is not null and cac.accept_status = 18 then
          cac.accept_code
         ELSE
           ''
       end) AS ACCEPT_CODE,
       (case
         when tpd.survival_mode = 1 and
              cac.accept_status is not null and cac.accept_status = 18 then
          (SELECT S.SERVICE_NAME FROM DEV_PAS.T_SERVICE S WHERE S.SERVICE_CODE = cac.Service_Code)
         ELSE
           ''
       end) AS SERVICE_NAME,
       CAC.VALIDATE_TIME AS ACCEPT_VALID_DATE,
       (CASE WHEN PAR.Unit_Number is not null AND PAR.FEE_STATUS <> '01' AND PAR.FEE_STATUS <> '16'  
                        THEN  PAR.FINISH_TIME
                     ELSE PAR.STATISTICAL_DATE  
                     END ) AS CONFIRM_DATE,/*业务核销时间*/
          PAR.FINISH_TIME AS FINISH_TIME, /*收付费到账时间*/
          CD.PAYREFNO AS PAYREFNO, /*实付号码*/
          (case
                  when PAR.PAY_MODE = '32' then
                   NVL(BBD.BANK_DEAL_DATE,AFBBD.BANK_DEAL_DATE) 
                  when PAR.PAY_MODE = '34' then
                   FMS.pay_sent_date
                end) AS VALIDATE_DATE, /*划款日期*/
          (case
           	when tpd.survival_mode = 1 and PAR.pay_mode in (32, 34) 
           then 
             (case
             	 when PAR.FEE_STATUS='01' then '成功'
             	 when PAR.FEE_STATUS='03' then '失败'
             	 else
             	 	'' 
             end)
          end) AS FEE_STATUS,/*划款结果*/
          (case
                  when PAR.PAY_MODE = '32' then
                   BR.BANK_RET_NAME
                  when PAR.PAY_MODE = '34' then
                   FMS.RTN_MSG
                  ELSE
                   ''
                end) AS FUNDS_RTN_CODE /*划款不成功原因*/
  FROM DEV_PAS.T_BONUS_ALLOCATE BA
 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
    ON CP.ITEM_ID = BA.ITEM_ID
 INNER JOIN DEV_PDS.T_PRODUCT_LIFE TPL
    ON CP.PRODUCT_ID = TPL.PRODUCT_ID
 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
    ON CP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
    ON TBP.BUSINESS_PRD_ID = BP.BUSI_PRD_ID
 LEFT JOIN dev_pas.T_CONTRACT_MASTER CM
    ON CM.policy_code = BA.Policy_Code
 LEFT JOIN DEV_PAS.T_CONTRACT_RELATION CR
    ON CR.Master_Policy_Id = BA.POLICY_ID
 LEFT JOIN DEV_PAS.t_pay_due TPD
    ON TPD.busi_item_id = BA.busi_item_id
   AND TPD.item_id = BA.item_id
   AND TPD.pay_due_date = BA.allocate_due_date
   AND TPD.fee_status <> '02'
 LEFT JOIN DEV_CAP.V_PREM_ARAP PAR
    ON PAR.UNIT_NUMBER = TPD.UNIT_NUMBER
    AND PAR.BUSI_PROD_CODE = TPD.BUSI_PROD_CODE
 LEFT JOIN DEV_CAP.V_CASH_DETAIL CD
    ON PAR.UNIT_NUMBER=CD.UNIT_NUMBER
 LEFT JOIN dev_pas.t_cs_accept_change CAC
    ON CAC.accept_code = PAR.business_code
   AND CAC.service_code = PAR.service_code
 LEFT JOIN DEV_PAS.T_BANK_RET_CONF BR
    ON BR.BANK_RET_CODE = PAR.FUNDS_RTN_CODE
 LEFT JOIN (SELECT fn.unit_number,
                    fn.rtn_msg,
                    fn.pay_sent_date,
                    ROW_NUMBER() OVER(PARTITION BY FN.Unit_Number 
                    ORDER BY FN.LIST_ID DESC /*  按时间、主键降序排序*/
             	) AS rn
           FROM DEV_CAP.T_FMS_NONREALTIME FN) FMS
    ON FMS.UNIT_NUMBER = PAR.UNIT_NUMBER
    and FMS.rn = 1 /*筛选每个分组的最新记录*/
  LEFT JOIN DEV_CAP.t_Bank_Text_Detail BBD
     ON PAR.SEQ_NO = BBD.SEQ_NO
  LEFT JOIN (SELECT BTG.Unit_Number,
                    BTG.Seq_No,
                    ROW_NUMBER() OVER(PARTITION BY BTG.Unit_Number ORDER BY BTG.GROUP_ID DESC /*  按时间、主键降序排序*/) AS rn
               FROM DEV_CAP.T_BANK_TEXT_GROUP BTG
               WHERE BTG.BANK_TEXT_STATUS = 8/*状态为8表示失败*/) AFBTG
      ON AFBTG.UNIT_NUMBER = PAR.UNIT_NUMBER
      and AFBTG.rn = 1 /*筛选每个分组的最新记录*/
   LEFT JOIN DEV_CAP.t_Bank_Text_Detail AFBBD
     ON AFBBD.SEQ_NO = AFBTG.SEQ_NO
     LEFT JOIN DEV_PAS.T_CONTRACT_INVEST CI
            ON CI.POLICY_ID = CM.POLICY_ID
           AND CI.INVEST_ACCOUNT_TYPE = 2
          WHERE 1 = 1 
          
       ]]>
        <!-- 查询条件-->
        <if test=" contNo != null and contNo != ''  "><![CDATA[ AND BA.POLICY_CODE = #{contNo} ]]></if>
    </select>
	
</mapper>
