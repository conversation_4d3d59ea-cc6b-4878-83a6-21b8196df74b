<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.CustomerDetailInfoDaoImpl">
<!-- 按索引生成的查询条件 -->	
	<sql id="PA_findCustomerRoleInfoCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_findCustomerBenefitInsuredRoleInfoCondition">
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{customer_id} ]]></if>
	</sql>
	
	<select id="findCustomerPolicyHolderRoleInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.policy_id, B.policy_code,C.address_id,C.address 
		FROM APP___PAS__DBUSER.T_policy_holder A,APP___PAS__DBUSER.T_contract_master B ,APP___PAS__DBUSER.T_address C 
		WHERE A.POLICY_ID = B.policy_id AND A.customer_id = C.customer_id
  		]]>
		<include refid="PA_findCustomerRoleInfoCondition" />
	</select>
	
	<select id="findCustomerInsuredRoleInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.policy_id,  B.policy_code,C.address_id,C.address 
		FROM APP___PAS__DBUSER.T_insured_list A,APP___PAS__DBUSER.T_contract_master B ,APP___PAS__DBUSER.T_address C 
		WHERE A.POLICY_ID = B.policy_id AND A.customer_id = C.customer_id
  		]]>
		<include refid="PA_findCustomerRoleInfoCondition" />
	</select>
	
	<select id="findCustomerBenefitInsuredRoleInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.policy_id,  B.policy_code,C.address_id,C.address 
		FROM APP___PAS__DBUSER.T_benefit_insured A,APP___PAS__DBUSER.T_contract_master B ,APP___PAS__DBUSER.T_address C 
		WHERE A.POLICY_ID = B.policy_id AND A.insured_id = C.customer_id
  		]]>
		<include refid="PA_findCustomerBenefitInsuredRoleInfoCondition" />
	</select>
	
</mapper>
