<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.CreateMedicalDaoImpl">
	<insert id="PA_insertMedical" useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="medical_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_MEDICAL.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[ INSERT INTO DEV_PAS.T_CONTRACT_MEDICAL(
					MEDICAL_ID,POLICY_ID,POLICY_CODE,APPLY_CODE,POLICY_FORMER_NO,
					POLICY_SEQUENCE_NO,CUSTOMER_SEQUENCE_NO,UNDERWRIT_SEQUENCE_NO,
					MEDICAL_NO,MEDICAL_PAY_ORDER,VALIDATE_MODE,INSERT_BY,UPDATE_BY,
					INSERT_TIME,UPDATE_TIME,INSERT_TIMESTAMP,UPDATE_TIMESTAMP,SH_JOB_CODE)
				  VALUES (
				  	#{medical_id, jdbcType=NUMERIC},#{policy_id, jdbcType=NUMERIC},
				  	#{policy_code, jdbcType=VARCHAR},#{apply_code, jdbcType=VARCHAR},
				  	#{policy_former_no, jdbcType=VARCHAR},#{policy_sequence_no, jdbcType=VARCHAR},
				  	#{customer_sequence_no, jdbcType=VARCHAR},#{underwrit_sequence_no, jdbcType=VARCHAR},
				  	#{medical_no, jdbcType=VARCHAR},#{medical_pay_order, jdbcType=VARCHAR},
				  	#{validate_mode, jdbcType=VARCHAR},#{insert_by, jdbcType=NUMERIC},
				  	#{update_by, jdbcType=NUMERIC}, SYSDATE, SYSDATE, CURRENT_TIMESTAMP,
				  	CURRENT_TIMESTAMP, #{sh_job_code, jdbcType=VARCHAR})
		 ]]>
	</insert>
	
	<select id="PA_findPolicyMedicalByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT M.POLICY_ID,M.MEDICAL_ID,M.POLICY_CODE,M.APPLY_CODE,M.POLICY_FORMER_NO,
					M.POLICY_SEQUENCE_NO,M.CUSTOMER_SEQUENCE_NO,M.UNDERWRIT_SEQUENCE_NO,
					M.MEDICAL_NO,M.MEDICAL_PAY_ORDER,M.VALIDATE_MODE FROM DEV_PAS.T_CONTRACT_MEDICAL M
					WHERE M.POLICY_ID = #{policy_id, jdbcType=NUMERIC}
		]]>
	</select>
	
	<insert id="PA_addPolicyMedicalLog" useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT DEV_PAS.S_CONTRACT_MEDICAL_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[ INSERT INTO DEV_PAS.T_CONTRACT_MEDICAL_LOG(
					LOG_ID,LOG_TYPE,POLICY_CHG_ID,MEDICAL_ID,POLICY_ID,POLICY_CODE,APPLY_CODE,
					POLICY_FORMER_NO,POLICY_SEQUENCE_NO,CUSTOMER_SEQUENCE_NO,
					UNDERWRIT_SEQUENCE_NO,MEDICAL_NO,MEDICAL_PAY_ORDER,
					VALIDATE_MODE,INSERT_BY,UPDATE_BY,INSERT_TIME,UPDATE_TIME,
					INSERT_TIMESTAMP,UPDATE_TIMESTAMP)
				VALUES(
					#{log_id, jdbcType=NUMERIC},#{log_type, jdbcType=VARCHAR},
					#{policy_chg_id, jdbcType=NUMERIC},#{medical_id, jdbcType=NUMERIC},
					#{policy_id, jdbcType=NUMERIC},
					#{policy_code, jdbcType=VARCHAR},#{apply_code, jdbcType=VARCHAR},
					#{policy_former_no, jdbcType=VARCHAR},#{policy_sequence_no, jdbcType=VARCHAR},
					#{customer_sequence_no, jdbcType=VARCHAR},#{underwrit_sequence_no, jdbcType=VARCHAR},
				  	#{medical_no, jdbcType=VARCHAR},#{medical_pay_order, jdbcType=VARCHAR},
				  	#{validate_mode, jdbcType=VARCHAR},#{insert_by, jdbcType=NUMERIC},
				  	#{update_by, jdbcType=NUMERIC}, SYSDATE, SYSDATE, CURRENT_TIMESTAMP,
				  	CURRENT_TIMESTAMP )
		 ]]>
	</insert>
	
	<insert id="PA_addPolicyMedicalCx"  useGeneratedKeys="false" parameterType="java.util.Map">
		<!-- <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="policy_chg_id">
			SELECT DEV_PAS.S_CONTRACT_MEDICAL_CX.NEXTVAL FROM DUAL
		</selectKey> -->
		<![CDATA[ INSERT INTO DEV_PAS.T_CONTRACT_MEDICAL_CX(
					POLICY_CHG_ID,MEDICAL_ID,POLICY_ID,LOG_ID,
					LOG_TYPE,PRE_LOG_ID,OPER_TYPE,CHANGE_SEQ,
					INSERT_BY,UPDATE_BY,INSERT_TIME,UPDATE_TIME,
					INSERT_TIMESTAMP,UPDATE_TIMESTAMP)
				VALUES(
					#{policy_chg_id, jdbcType=NUMERIC},#{medical_id, jdbcType=NUMERIC},
					#{policy_id, jdbcType=NUMERIC},#{log_id, jdbcType=NUMERIC},
					#{log_type, jdbcType=VARCHAR},#{pre_log_id, jdbcType=NUMERIC},
					#{oper_type, jdbcType=VARCHAR},#{change_seq, jdbcType=NUMERIC},
					#{insert_by, jdbcType=NUMERIC},#{update_by, jdbcType=NUMERIC},
					SYSDATE, SYSDATE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
					)
		 ]]>
	</insert>
</mapper>