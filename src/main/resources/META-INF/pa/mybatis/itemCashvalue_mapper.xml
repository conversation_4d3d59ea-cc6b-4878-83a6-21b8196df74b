<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.udmp.impl.batch.demo.dao.IItemCashvalueDao">

	<sql id="itemCashvalueWhereCondition">
		<if test=" update_date  != null  and  update_date  != ''  "><![CDATA[ AND A.UPDATE_DATE = #{update_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" tx_date  != null  and  tx_date  != ''  "><![CDATA[ AND A.TX_DATE = #{tx_date} ]]></if>
		<if test=" policyno != null and policyno != ''  "><![CDATA[ AND A.POLICYNO = #{policyno} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" tx_seq  != null "><![CDATA[ AND A.TX_SEQ = #{tx_seq} ]]></if>
		<if test=" cash_flag  != null "><![CDATA[ AND A.CASH_FLAG = #{cash_flag} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	

<!-- 添加操作 -->
	<insert id="addItemCashvalue"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ITEM_CASHVALUE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ITEM_CASHVALUE(
				UPDATE_DATE, INSERT_TIME, UPDATE_TIME, ITEM_ID, INSERT_TIMESTAMP, TX_DATE, POLICYNO, 
				UPDATE_BY, LIST_ID, CASH_VALUE, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, POLICY_ID, 
				TX_SEQ, CASH_FLAG ) 
			VALUES (
				#{update_date, jdbcType=DATE}, SYSDATE , SYSDATE , #{item_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{tx_date, jdbcType=DATE} , #{policyno, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{cash_value, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{tx_seq, jdbcType=NUMERIC} , #{cash_flag, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteItemCashvalue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ITEM_CASHVALUE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateItemCashvalue" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ITEM_CASHVALUE ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_DATE = #{update_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    TX_DATE = #{tx_date, jdbcType=DATE} ,
			POLICYNO = #{policyno, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    TX_SEQ = #{tx_seq, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	

<!-- 按map查询操作 -->
	<select id="findAllMapItemCashvalue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UPDATE_DATE, A.ITEM_ID, A.TX_DATE, A.POLICYNO, 
			A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.TX_SEQ FROM APP___PAS__DBUSER.T_ITEM_CASHVALUE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllItemCashvalue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UPDATE_DATE, A.ITEM_ID, A.TX_DATE, A.POLICYNO, 
			A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.TX_SEQ FROM APP___PAS__DBUSER.T_ITEM_CASHVALUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="itemCashvalueWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findItemCashvalueTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ITEM_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="itemCashvalueWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryItemCashvalueForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UPDATE_DATE, B.ITEM_ID, B.TX_DATE, B.POLICYNO, 
			B.LIST_ID, B.CASH_VALUE, B.BUSI_ITEM_ID, B.POLICY_ID, 
			B.TX_SEQ FROM (
					SELECT ROWNUM RN, A.UPDATE_DATE, A.ITEM_ID, A.TX_DATE, A.POLICYNO, 
			A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.TX_SEQ FROM APP___PAS__DBUSER.T_ITEM_CASHVALUE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询待处理的数据 -->
	<select id="PA_queryCashValueList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT A.POLICY_CODE,
					       A.IS_MASTER_ITEM,
					       A.ANNU_PAY_TYPE,
					       A.IS_PAUSE,
					       A.IS_SRP,
					       A.PROD_PKG_PLAN_CODE,
					       A.APPEND_PREM_AF,
					       A.IS_WAIVED,
					       A.INTEREST_MODE,
					       A.APPLY_CODE,
					       A.NORENEW_REASON,
					       A.ORGAN_CODE,
					       A.CHARGE_YEAR,
					       A.EXTRA_PREM_AF,
					       A.POLICY_ID,
					       A.INITIAL_AMOUNT,
					       A.CC_SA,
					       A.INITIAL_EXTRA_PREM_AF,
					       A.PAYOUT_RATE,
					       A.AMOUNT,
					       A.PAIDUP_DATE,
					       A.PAY_YEAR,
					       A.EXPIRY_DATE,
					       A.PAY_PERIOD,
					       A.LIABILITY_STATE,
					       A.COUNT_WAY,
					       A.IS_GIFT,
					       A.RERINSTATE_DATE,
					       A.ADDITIONAL_PREM_AF,
					       A.RENEW_DECISION,
					       A.VALIDATE_DATE,
					       A.BONUS_MODE,
					       A.BENEFIT_LEVEL,
					       A.PRODUCT_ID,
					       A.BONUS_SA,
					       A.PRODUCT_CODE,
					       A.APPLY_DATE,
					       A.INTEREST_FLAG,
					       A.COVERAGE_PERIOD,
					       A.ITEM_ID,
					       A.RENEWAL_EXTRA_PREM_AF,
					       A.WAIVER_END,
					       A.MATURITY_DATE,
					       A.BUSI_ITEM_ID,
					       A.HEALTH_SERVICE_FLAG,
					       A.LAPSE_DATE,
					       A.PAY_FREQ,
					       A.UNIT,
					       A.COVERAGE_YEAR,
					       A.END_CAUSE,
					       A.LAPSE_CAUSE,
					       A.RENEWAL_DISCNTED_PREM_AF,
					       A.TOTAL_PREM_AF,
					       A.DECISION_CODE,
					       A.PAUSE_DATE,
					       A.STD_PREM_AF,
					       A.CHARGE_PERIOD,
					       A.SUSPEND_DATE,
					       A.SUSPEND_CAUSE,
					       A.DEDUCTIBLE_FRANCHISE,
					       A.WAIVER_START,
					       A.PREM_FREQ,
					       A.INITIAL_DISCNT_PREM_AF,
					       A.BONUS_W_MODE,
					       A.LAST_BONUS_DATE,
					       B.TX_SEQ,
					       B.CALC_DATE,
					       B.CASH_FLAG,
					       ROWNUM
					  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A,
			               APP___PAS__DBUSER.T_CV_TX_INFO_DETAIL B,
			               APP___PAS__DBUSER.T_CONTRACT_MASTER C
			               WHERE A.POLICY_ID=C.POLICY_ID
			               AND B.POLICY_CODE=C.POLICY_CODE
			               AND B.CALC_DATE=#{calc_date, jdbcType=DATE}
					       AND MOD(A.ITEM_ID, #{modnum, jdbcType = VARCHAR}) = #{start,jdbcType = VARCHAR}]]>
	</select>
	
</mapper>
