<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="extraPremLog">
<!--
	<sql id="PA_extraPremLogWhereCondition">
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" extra_div  != null "><![CDATA[ AND A.EXTRA_DIV = #{extra_div} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" extra_prem  != null "><![CDATA[ AND A.EXTRA_PREM = #{extra_prem} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" em_value  != null "><![CDATA[ AND A.EM_VALUE = #{em_value} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" extra_period  != null "><![CDATA[ AND A.EXTRA_PERIOD = #{extra_period} ]]></if>
		<if test=" extra_type != null and extra_type != ''  "><![CDATA[ AND A.EXTRA_TYPE = #{extra_type} ]]></if>
		<if test=" into_effect_type != null and into_effect_type != ''  "><![CDATA[ AND A.INTO_EFFECT_TYPE = #{into_effect_type} ]]></if>
		<if test=" extra_para  != null "><![CDATA[ AND A.EXTRA_PARA = #{extra_para} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" extra_perc  != null "><![CDATA[ AND A.EXTRA_PERC = #{extra_perc} ]]></if>
		<if test=" add_arith != null and add_arith != ''  "><![CDATA[ AND A.ADD_ARITH = #{add_arith} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryExtraPremLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addExtraPremLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			select APP___PAS__DBUSER.S_EXTRA_PREM_LOG__LOG_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_EXTRA_PREM_LOG(
				END_DATE, PRODUCT_CODE, ITEM_ID, EXTRA_DIV, BUSI_PROD_CODE, EXTRA_PREM, APPLY_CODE, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, POLICY_CHG_ID, BUSI_ITEM_ID, EM_VALUE, POLICY_ID, 
				EXTRA_PERIOD, INSERT_TIME, EXTRA_TYPE, UPDATE_TIME, INTO_EFFECT_TYPE, EXTRA_PARA, START_DATE, 
				LOG_ID, POLICY_CODE, LOG_TYPE, UPDATE_TIMESTAMP, EXTRA_PERC, INSERT_BY, ADD_ARITH ) 
			VALUES (
				#{end_date, jdbcType=DATE}, #{product_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{extra_div, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{extra_prem, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{em_value, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{extra_period, jdbcType=NUMERIC} , SYSDATE , #{extra_type, jdbcType=VARCHAR} , SYSDATE , #{into_effect_type, jdbcType=VARCHAR} , #{extra_para, jdbcType=NUMERIC} , #{start_date, jdbcType=DATE} 
				, #{log_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{log_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{extra_perc, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{add_arith, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteExtraPremLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_EXTRA_PREM_LOG WHERE LOG_ID=#{log_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateExtraPremLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_EXTRA_PREM_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    END_DATE = #{end_date, jdbcType=DATE} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    EXTRA_DIV = #{extra_div, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    EXTRA_PREM = #{extra_prem, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    EM_VALUE = #{em_value, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    EXTRA_PERIOD = #{extra_period, jdbcType=NUMERIC} ,
			EXTRA_TYPE = #{extra_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			INTO_EFFECT_TYPE = #{into_effect_type, jdbcType=VARCHAR} ,
		    EXTRA_PARA = #{extra_para, jdbcType=NUMERIC} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    EXTRA_PERC = #{extra_perc, jdbcType=NUMERIC} ,
			ADD_ARITH = #{add_arith, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findExtraPremLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.INTO_EFFECT_TYPE, A.EXTRA_PARA, A.START_DATE, 
			A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryExtraPremLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapExtraPremLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.INTO_EFFECT_TYPE, A.EXTRA_PARA, A.START_DATE, 
			A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllExtraPremLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.INTO_EFFECT_TYPE, A.EXTRA_PARA, A.START_DATE, 
			A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findExtraPremLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_EXTRA_PREM_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryExtraPremLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.END_DATE, B.PRODUCT_CODE, B.ITEM_ID, B.EXTRA_DIV, B.BUSI_PROD_CODE, B.EXTRA_PREM, B.APPLY_CODE, 
			B.LIST_ID, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.EM_VALUE, B.POLICY_ID, 
			B.EXTRA_PERIOD, B.EXTRA_TYPE, B.INTO_EFFECT_TYPE, B.EXTRA_PARA, B.START_DATE, 
			B.LOG_ID, B.POLICY_CODE, B.LOG_TYPE, B.EXTRA_PERC, B.ADD_ARITH FROM (
					SELECT ROWNUM RN, A.END_DATE, A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.INTO_EFFECT_TYPE, A.EXTRA_PARA, A.START_DATE, 
			A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
