<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAddToPremiumPreserveQueryDao">
	<select id="PA_queryAddAppStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    	select APP_STATUS from dev_pas.T_CS_APPLICATION
	    	where CHANGE_ID = #{change_id}
    ]]>
	</select>
	
	<select id="PA_queryAddToChangeId" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select change_id from DEV_PAS.T_CS_POLICY_CHANGE where POLICY_CODE=#{policy_code} and accept_id=(select accept_id from
	     DEV_PAS.T_CS_ACCEPT_CHANGE where service_code = 'AM' and ACCEPT_CODE=#{accept_code})
   ]]>
	</select>
	
	<select id="PA_queryAddToPay" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select a.apply_time,b.accept_status,b.review_view,b.validate_time,b.fee_amount from dev_pas.T_CS_APPLICATION a 
			left join dev_pas.T_CS_ACCEPT_CHANGE b on a.CHANGE_ID = b.CHANGE_ID 
           where a.CHANGE_ID=#{change_id}
           and b.ACCEPT_CODE=#{accept_code}
    ]]>
	</select>
	
		<select id="PA_queryAddAppntName" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select tc.customer_name as name
            from DEV_PAS.T_CONTRACT_MASTER tcm,
                 DEV_PAS.T_POLICY_HOLDER ph,
                 DEV_PAS.T_CUSTOMER tc
            where tcm.policy_code = ph.policy_code
              and ph.customer_id = tc.customer_id
              and tcm.policy_code = #{policy_code}
    ]]>
	</select>
 
	<select id="PA_queryAddInsuredName" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			SELECT customer_name as name FROM DEV_PAS.T_CUSTOMER WHERE 
       customer_id=(select B.customer_id from dev_pas.T_BENEFIT_INSURED A,dev_pas.T_INSURED_LIST B
       where A.ORDER_ID = 1 AND  A.POLICY_CODE = B.POLICY_CODE and A.INSURED_ID = B.LIST_ID AND ROWNUM = 1
             and A.POLICY_CODE=#{policy_code})
    ]]>
	</select>

	<select id="PA_queryAddChargeOrPayState" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
		 select DISTINCT (t.POLICY_CODE),
                 t.fee_status,
                 t.pay_mode,
                 p.name,
                 t.funds_rtn_code,
                 t.bank_code,
                 t.bank_user_name,
                 t.bank_account,
                 t.arap_flag
  	 from dev_pas.T_PREM_ARAP t left join dev_pas.T_PAY_MODE p on t.pay_mode = p.code
 		 where t.POLICY_CODE = #{policy_code} 
 		 and t.BUSINESS_CODE=#{accept_code}
 		  ]]>
	</select>
	
	<select id="PA_queryAddChargeOrPayStateCS" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select DISTINCT (t.POLICY_CODE),
                t.fee_status,
                t.pay_mode,
                p.name,
                t.bank_code,
                t.bank_user_name,
                t.bank_account,
                t.arap_flag
  from dev_pas.T_CS_PREM_ARAP
  t left join dev_pas.T_PAY_MODE p on t.pay_mode = p.code
 where t.POLICY_CODE = #{policy_code}
 		and t.BUSINESS_CODE=#{accept_code}
    ]]>
	</select>
	
	<select id="PA_queryBankName" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select DISTINCT(t.BUSINESS_CODE),bod.bank_name as name from dev_pas.T_CS_PREM_ARAP T left join dev_pas.T_BANK bod
          on bod.bank_code = t.bank_code   
        WHERE t.BUSINESS_CODE = #{accept_code}
    ]]>
	</select>
	
		<select id="PA_queryAllIdList" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select pc.change_id,pc.accept_id,ph.customer_id
            from DEV_PAS.T_CONTRACT_MASTER tcm,
                 DEV_PAS.T_POLICY_HOLDER ph,
                 DEV_PAS.T_CS_POLICY_CHANGE pc,
                 dev_pas.T_CS_ACCEPT_CHANGE ac
            where tcm.policy_id = ph.policy_id
            and pc.policy_code = ph.policy_code
            and tcm.policy_id = pc.policy_id
            and ac.accept_id = pc.accept_id
            and ph.policy_code=#{policy_code}
            and accept_code=#{accept_code}
    ]]>
	</select>
	
	<select id="PA_queryAMAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select fee_amount from dev_pas.T_CS_ACCEPT_CHANGE where ACCEPT_CODE=#{accept_code}
   ]]>
	</select>

	<select id="PA_queryPremFreq" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	    select distinct PREM_FREQ from dev_pas.T_CS_PREM_ARAP where BUSINESS_CODE=#{accept_code}
   ]]>
	</select>
	
	<select id="PA_queryAddFail" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
	 select b.bank_ret_code,B.bank_ret_name from dev_pas.T_BANK_RET_CONF B
 			where b.bank_ret_code= #{funds_rtn_code}
    ]]>
	</select>
	
	<select id="PA_queryFundsRtnCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
			select DISTINCT (t.POLICY_CODE),
                t.funds_rtn_code
  from dev_pas.T_PREM_ARAP
   t left join dev_pas.T_PAY_MODE p on t.pay_mode = p.code
 where t.POLICY_CODE = #{policy_code} and t.BUSINESS_CODE=#{accept_code}
    ]]>
	</select>
</mapper>
