<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAbsCashTaskDetailDao">

	<sql id="absCashTaskDetailWhereCondition">
		<if test=" loancas_date  != null  and  loancas_date  != ''  "><![CDATA[ AND A.LOANCAS_DATE = #{loancas_date} ]]></if>
		<if test=" plan_code != null and plan_code != ''  "><![CDATA[ AND A.PLAN_CODE = #{plan_code} ]]></if>
		<if test=" main_list_id  != null "><![CDATA[ AND A.MAIN_LIST_ID = #{main_list_id} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>

	<!-- 按索引生成的查询条件 -->	
	<sql id="queryAbsCashTaskDetailByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryAbsCashTaskDetailByPlanCodeCondition">
		<if test=" plan_code != null and plan_code != '' "><![CDATA[ AND A.PLAN_CODE = #{plan_code} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="addAbsCashTaskDetail"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ABS_CASH_DET__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL(
				LOANCAS_DATE, INSERT_TIME, PLAN_CODE, UPDATE_TIME, MAIN_LIST_ID, STATUS, ACCEPT_CODE, 
				BUSI_PROD_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, 
				INSERT_BY ) 
			VALUES (
				#{loancas_date, jdbcType=DATE}, SYSDATE , #{plan_code, jdbcType=VARCHAR} , SYSDATE , #{main_list_id, jdbcType=NUMERIC} , #{status, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} 
				, #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 批量添加操作 -->
	<insert id="batchSaveAbsCashTaskDetail" parameterType="java.util.List" useGeneratedKeys="false">
		INSERT INTO APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL(
			   LIST_ID, MAIN_LIST_ID, PLAN_CODE, LOANCAS_DATE, STATUS, ACCEPT_CODE, POLICY_CODE,
			   BUSI_ITEM_ID, BUSI_PROD_CODE, INSERT_BY, INSERT_TIME, INSERT_TIMESTAMP,
			   UPDATE_BY, UPDATE_TIME, UPDATE_TIMESTAMP ) 
			   SELECT APP___PAS__DBUSER.S_ABS_CASH_DET__LIST_ID.NEXTVAL, S.* FROM (
		<foreach collection="list" item="policy" index="index" separator="UNION ALL">
			 SELECT
			    #{policy.mainListId, jdbcType=NUMERIC} AS MAIN_LIST_ID,
			    #{policy.planCode, jdbcType=VARCHAR} AS PLAN_CODE,
			    #{policy.loancasDate, jdbcType=DATE} AS LOANCAS_DATE,
			    #{policy.status, jdbcType=VARCHAR} AS STATUS,
			    #{policy.acceptCode, jdbcType=VARCHAR} AS ACCEPT_CODE,
			    #{policy.policyCode, jdbcType=VARCHAR} AS POLICY_CODE,
			    #{policy.busiItemId, jdbcType=NUMERIC} AS BUSI_ITEM_ID,
			    #{policy.busiProdCode, jdbcType=VARCHAR} AS BUSI_PROD_CODE,
			    0 AS INSERT_BY,
			    0 AS UPDATE_BY,
			    SYSDATE AS INSERT_TIME,
			    CURRENT_TIMESTAMP AS INSERT_TIMESTAMP,
			    SYSDATE AS UPDATE_TIME,
			    CURRENT_TIMESTAMP AS UPDATE_TIMESTAMP
			 FROM DUAL
		</foreach>
		) S
	</insert>

	<!-- 删除操作 -->	
	<delete id="deleteAbsCashTaskDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="updateAbsCashTaskDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL ]]>
		<set>
		<trim suffixOverrides=",">
		    LOANCAS_DATE = #{loancas_date, jdbcType=DATE} ,
			PLAN_CODE = #{plan_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    MAIN_LIST_ID = #{main_list_id, jdbcType=NUMERIC} ,
			STATUS = #{status, jdbcType=VARCHAR} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="findAbsCashTaskDetailByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOANCAS_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, A.STATUS, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashTaskDetailByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findAbsCashTaskDetailByPlanCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOANCAS_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, A.STATUS, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashTaskDetailByPlanCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

	<!-- 按map查询操作 -->
	<select id="findAllMapAbsCashTaskDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOANCAS_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, A.STATUS, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<include refid="absCashTaskDetailWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllAbsCashTaskDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOANCAS_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, A.STATUS, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<include refid="absCashTaskDetailWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

	<!-- 查询个数操作 -->
	<select id="findAbsCashTaskDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL A WHERE 1 = 1  ]]>
		<include refid="absCashTaskDetailWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="queryAbsCashTaskDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LOANCAS_DATE, B.PLAN_CODE, B.MAIN_LIST_ID, B.STATUS, B.ACCEPT_CODE, 
			B.BUSI_PROD_CODE, B.POLICY_CODE, B.LIST_ID, B.BUSI_ITEM_ID FROM (
					SELECT ROWNUM RN, A.LOANCAS_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, A.STATUS, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="absCashTaskDetailWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询状态为“未处理”的数据列表 -->
	<select id="queryCountInAbsCashTaskDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.*, ROWNUM RN FROM (   
					        SELECT A.LIST_ID, A.LOANCAS_DATE, A.PLAN_CODE, A.MAIN_LIST_ID,  A.ACCEPT_CODE,
					               A.BUSI_PROD_CODE, A.POLICY_CODE, A.STATUS, A.BUSI_ITEM_ID
					          FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_DETAIL A
					         WHERE 1 = 1
					           AND A.STATUS = 0 
					           AND A.LOANCAS_DATE <= #{jobDate,jdbcType=DATE}  
					 ) T WHERE 1=1]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.LIST_ID , #{modnum}) = #{start} ]]></if>
	</select>
	
</mapper>
