<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyHolderInfoQueryDao">

	<select id="findPolicyList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ROWNUM,B.POLICY_CODE FROM (SELECT distinct T.POLICY_CODE
		     FROM DEV_PAS.T_CONTRACT_MASTER T, DEV_PAS.T_CONTRACT_AGENT A,DEV_PAS.T_CS_ACCEPT_CHANGE CAC,
                       DEV_PAS.T_CS_POLICY_CHANGE CPCH,DEV_PAS.T_CS_POLICY_HOLDER CPH
		    WHERE A.POLICY_CODE = T.POLICY_CODE and CAC.ACCEPT_ID = CPCH.ACCEPT_ID
                   AND CPCH.POLICY_CHG_ID = CPH.POLICY_CHG_ID
                   and CPH.POLICY_CODE=T.POLICY_CODE
                   AND CAC.ACCEPT_STATUS = '18'
                   AND CAC.SERVICE_CODE in ('AE','PC')
		      		AND A.IS_CURRENT_AGENT = 1
		       		AND A.CHANNEL_TYPE = '03'
		]]>
		<if test=" service_bank  != null and service_bank !=''">
				<![CDATA[AND T.SERVICE_BANK = #{service_bank} ]]>
		</if>
		<if test=" start_date  != null and start_date !=''">
			<![CDATA[AND trunc(CAC.VALIDATE_TIME) >= to_date(#{start_date},'yyyy-mm-dd')]]>
		</if>
		<if test=" end_date  != null and end_date !=''">
			<![CDATA[AND trunc(CAC.VALIDATE_TIME) <= to_date(#{end_date},'yyyy-mm-dd')]]>
		</if>
		<![CDATA[
			)B
		]]>
	</select>
	<select id="findHolderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT G.POLICY_CODE,
               G.SERVICE_CODE,
               G.CUSTOMER_NAME,
               G.CUSTOMER_CERT_TYPE,
               G.CUSTOMER_CERTI_CODE,
               G.BANK_ACCOUNT,
               G.NEW_BANK_ACCOUNT,
               G.NEW_BANK_CODE,
               H.DOUBLE_MAINRISK_FLAG,
               H.APPLY_CODE,
               G.VALIDATE_TIME
          FROM (SELECT 
          Z.POLICY_CODE,
               Z.SERVICE_CODE,
               Z.CUSTOMER_NAME,
               Z.CUSTOMER_CERT_TYPE,
               Z.CUSTOMER_CERTI_CODE,
               Z.BANK_ACCOUNT,
               Z.NEW_BANK_ACCOUNT,
               Z.NEW_BANK_CODE,
               Z.VALIDATE_TIME
         FROM (SELECT CPCH.POLICY_CODE AS POLICY_CODE,
                       CAC.SERVICE_CODE AS SERVICE_CODE,
                       TC.CUSTOMER_NAME AS CUSTOMER_NAME,
                       TC.CUSTOMER_CERT_TYPE AS CUSTOMER_CERT_TYPE,
                       TC.CUSTOMER_CERTI_CODE AS CUSTOMER_CERTI_CODE,
                       (SELECT T.BANK_ACCOUNT FROM  DEV_PAS.T_CS_BANK_ACCOUNT T
                                WHERE T.OLD_NEW = '0'
                                AND T.CHANGE_ID = CPCH.CHANGE_ID
                                AND ROWNUM <= 1) AS BANK_ACCOUNT,
                       '' AS NEW_BANK_ACCOUNT,
                       '' AS NEW_BANK_CODE,
                       CAC.VALIDATE_TIME AS VALIDATE_TIME
                  FROM DEV_PAS.T_CS_ACCEPT_CHANGE CAC,
                       DEV_PAS.T_CS_POLICY_CHANGE CPCH,
                       DEV_PAS.T_CS_POLICY_HOLDER CPH,
                       DEV_PAS.T_CUSTOMER         TC,
                       DEV_PAS.T_CONTRACT_AGENT A
                 WHERE CAC.ACCEPT_ID = CPCH.ACCEPT_ID
                   AND CPCH.POLICY_CHG_ID = CPH.POLICY_CHG_ID
                   AND CPH.CUSTOMER_ID = TC.CUSTOMER_ID
                   AND CPCH.POLICY_CODE = A.POLICY_CODE
                   AND CAC.ACCEPT_STATUS = '18'
                   AND CAC.SERVICE_CODE = 'AE'
                   AND A.IS_CURRENT_AGENT = 1
		       		AND A.CHANNEL_TYPE = '03']]>
                   <if test=" start_date  != null and start_date !=''">
                   <![CDATA[AND trunc(CAC.VALIDATE_TIME) >= to_date(#{start_date},'yyyy-mm-dd')]]>
                   </if>
                   <if test=" end_date  != null and end_date !=''">
                   <![CDATA[AND trunc(CAC.VALIDATE_TIME) <= to_date(#{end_date},'yyyy-mm-dd')]]>
                   </if>
                   <if test=" policy_code  != null and policy_code !=''">
                   <![CDATA[AND CPCH.POLICY_CODE = #{policy_code}]]>
                   </if>
                   <![CDATA[AND CPH.OLD_NEW = '0'
                   ) Z WHERE ROWNUM <= 1
                UNION ALL
                SELECT 
                     K.POLICY_CODE,
               K.SERVICE_CODE,
               K.CUSTOMER_NAME,
               K.CUSTOMER_CERT_TYPE,
               K.CUSTOMER_CERTI_CODE,
               K.BANK_ACCOUNT,
               K.NEW_BANK_ACCOUNT,
               K.NEW_BANK_CODE,
               K.VALIDATE_TIME
          FROM (
                SELECT CPCH.POLICY_CODE AS POLICY_CODE,
                       CAC.SERVICE_CODE AS SERVICE_CODE,
                       TC.CUSTOMER_NAME AS CUSTOMER_NAME,
                       TC.CUSTOMER_CERT_TYPE AS CUSTOMER_CERT_TYPE,
                       TC.CUSTOMER_CERTI_CODE AS CUSTOMER_CERTI_CODE,
                       case when CBA.BANK_CODE in (select t.bank_code from dev_pas.T_BANK t where t.BANK_NAME like  '%兴业%')
                       then '0'
                       else '1'
                       end AS NEW_BANK_CODE,
                       CBA.BANK_ACCOUNT AS NEW_BANK_ACCOUNT,
                       case
                         when (SELECT T.BANK_ACCOUNT
                                 FROM DEV_PAS.T_CS_BANK_ACCOUNT T
                                WHERE T.OLD_NEW = '0'
                                  AND T.CHANGE_ID = CBA.CHANGE_ID
                                  AND ROWNUM <= 1) is null then
                          (select t.ACCOUNT
                             from dev_pas.T_PAYER_ACCOUNT t
                            where t.policy_id = CPCH.policy_id)
                         else
                          (SELECT T.BANK_ACCOUNT
                             FROM DEV_PAS.T_CS_BANK_ACCOUNT T
                            WHERE T.OLD_NEW = '0'
                              AND T.CHANGE_ID = CBA.CHANGE_ID
                              AND ROWNUM <= 1)
                       end AS BANK_ACCOUNT,
                       CAC.VALIDATE_TIME AS VALIDATE_TIME
                  FROM DEV_PAS.T_CS_ACCEPT_CHANGE CAC,
                       DEV_PAS.T_CS_POLICY_CHANGE CPCH,
                       DEV_PAS.T_CS_POLICY_HOLDER CPH,
                       DEV_PAS.T_CUSTOMER         TC,
                       DEV_PAS.T_CS_BANK_ACCOUNT  CBA,
                       DEV_PAS.T_CONTRACT_AGENT A
                 WHERE CAC.ACCEPT_ID = CPCH.ACCEPT_ID
                   AND CPCH.CHANGE_ID = CBA.CHANGE_ID
                   AND CPCH.POLICY_CHG_ID = CPH.POLICY_CHG_ID
                   AND CPH.CUSTOMER_ID = TC.CUSTOMER_ID
                   AND CPCH.POLICY_CODE = A.POLICY_CODE
                   AND CAC.ACCEPT_STATUS = '18'
                   AND CAC.SERVICE_CODE = 'PC'
                   AND A.IS_CURRENT_AGENT = 1
		       		AND A.CHANNEL_TYPE = '03']]>
                   <if test=" start_date  != null and start_date !=''">
                   <![CDATA[AND trunc(CAC.VALIDATE_TIME) >= to_date(#{start_date},'yyyy-mm-dd')]]>
                   </if>
                   <if test=" end_date  != null and end_date !=''">
                   <![CDATA[AND trunc(CAC.VALIDATE_TIME) <= to_date(#{end_date},'yyyy-mm-dd')]]>
                   </if>
                   <if test=" policy_code  != null and policy_code !=''">
                   <![CDATA[AND CPCH.POLICY_CODE = #{policy_code}]]>
                   </if>
                   <![CDATA[AND CBA.OLD_NEW = '1'  AND CPH.OLD_NEW = '0'  AND CBA.OPERATION_TYPE = '1'  ORDER BY CAC.VALIDATE_TIME) K WHERE ROWNUM <= 1
                   ) G,
               DEV_PAS.T_CONTRACT_MASTER H
         WHERE G.POLICY_CODE = H.POLICY_CODE]]>
         <if test=" service_bank  != null and service_bank !=''">
          <![CDATA[AND H.SERVICE_BANK = #{service_bank} ]]>
        </if>
	</select>
</mapper>