<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="policyChange">
	<sql id="PA_policyChangeWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" content_clob_id  != null "><![CDATA[ AND A.CONTENT_CLOB_ID = #{content_clob_id} ]]></if>
		<if test=" pre_policy_chg  != null "><![CDATA[ AND A.PRE_POLICY_CHG = #{pre_policy_chg} ]]></if>
		<if test=" change_flag != null and change_flag != ''  "><![CDATA[ AND A.CHANGE_FLAG = #{change_flag} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND A.VALIDATE_TIME = #{validate_time} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" validate_time_after  != null  and  validate_time_after  != ''  "><![CDATA[ AND A.VALIDATE_TIME > #{validate_time_after} ]]></if>
		<if test=" validate_time_before != null  and  validate_time_before != ''  "><![CDATA[ AND A.VALIDATE_TIME < #{validate_time_before} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyChangeByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyChangeByServiceCodeCondition">
		<if test=" service_code != null and service_code != '' "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
	</sql>		
	<sql id="PA_queryPolicyChangeByValidateTimeCondition">
		<if test=" validate_time  != null "><![CDATA[ AND A.VALIDATE_TIME = #{validate_time} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyChangeByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="PA_findPolicyChangePrePolicyChgByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="PA_findPolicyChangePrePolicyChgByPolicyCodeCondition">
		<if test=" policy_code  != null "><![CDATA[ AND a.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyChange"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="policy_chg_id">
			SELECT APP___PAS__DBUSER.S_POLICY_CHANGE__POLICY_CHG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_CHANGE(
				RELATED_ID, CONTENT_CLOB_ID, PRE_POLICY_CHG, CHANGE_FLAG, INSERT_TIME, UPDATE_TIME, FINISH_TIME, 
				INSERT_TIMESTAMP, VALIDATE_TIME, UPDATE_BY, UPDATE_TIMESTAMP, POLICY_CHG_ID, SERVICE_CODE, INSERT_BY, 
				POLICY_ID, BUSINESS_CODE,DERIV_TYPE ) 
			VALUES (
				#{related_id, jdbcType=NUMERIC}, #{content_clob_id, jdbcType=NUMERIC} , #{pre_policy_chg, jdbcType=NUMERIC} , #{change_flag, jdbcType=VARCHAR} , SYSDATE , SYSDATE , #{finish_time, jdbcType=TIMESTAMP } 
				, CURRENT_TIMESTAMP, #{validate_time, jdbcType=TIMESTAMP } , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} , #{business_code, jdbcType=VARCHAR},#{deriv_type, jdbcType=VARCHAR}) 
		 ]]>
	</insert>
	
	<!-- 添加操作保全用 -->
	<insert id="PA_addPolicyChangeByCs"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_CHANGE(
				RELATED_ID, CONTENT_CLOB_ID, PRE_POLICY_CHG, CHANGE_FLAG, INSERT_TIME, UPDATE_TIME, FINISH_TIME, 
				INSERT_TIMESTAMP, VALIDATE_TIME, UPDATE_BY, UPDATE_TIMESTAMP, POLICY_CHG_ID, SERVICE_CODE, INSERT_BY, 
				POLICY_ID, BUSINESS_CODE,DERIV_TYPE ) 
			VALUES (
				#{related_id, jdbcType=NUMERIC}, #{content_clob_id, jdbcType=NUMERIC} , #{pre_policy_chg, jdbcType=NUMERIC} , #{change_flag, jdbcType=VARCHAR} , SYSDATE , SYSDATE , #{finish_time, jdbcType=TIMESTAMP } 
				, CURRENT_TIMESTAMP, #{validate_time, jdbcType=TIMESTAMP } , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} , #{business_code, jdbcType=VARCHAR},#{deriv_type, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyChange" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_CHANGE WHERE POLICY_CHG_ID=#{policy_chg_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyChange" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CHANGE ]]>
		<set>
		<trim suffixOverrides=",">
		    RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
		    CONTENT_CLOB_ID = #{content_clob_id, jdbcType=NUMERIC} ,
		    PRE_POLICY_CHG = #{pre_policy_chg, jdbcType=NUMERIC} ,
			CHANGE_FLAG = #{change_flag, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
		    VALIDATE_TIME = #{validate_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

	<update id="PA_updatePolicyChangeByPolicyChgId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CHANGE ]]>
			<set>
				<trim suffixOverrides=",">
					 RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
					 CHANGE_FLAG = #{change_flag, jdbcType=VARCHAR} ,
				</trim>
			</set>
		<![CDATA[ WHERE  POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyChangeByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyChangeByPolicyChgIdCondition" />
	</select>
	
	<select id="PA_findPolicyChangeByServiceCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyChangeByServiceCodeCondition" />
	</select>

	<select id="PA_findPolicyChangeByValidateTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyChangeByValidateTimeCondition" />
	</select>
	
	<select id="PA_findPolicyChangeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyChangeByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID ,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE,A.POLICY_ID ,A.BUSINESS_CODE 
			FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyChangeWhereCondition" />
		<if test=" service_code_list != null and service_code_list.size()>0 "><![CDATA[ AND A.SERVICE_CODE IN ]]>
	  	<foreach collection="service_code_list" item="service_code_list"
			index="index" open="(" close=")" separator=",">#{service_code_list}</foreach></if>
		<if test=" notServiceCodeList != null and notServiceCodeList.size()>0 "><![CDATA[ AND A.SERVICE_CODE Not IN ]]>
	  	<foreach collection="notServiceCodeList" item="notServiceCodeList"
			index="index" open="(" close=")" separator=",">#{notServiceCodeList}</foreach></if>
		<if test=" notBackPolicyIdList != null and notBackPolicyIdList.size()>0 "><![CDATA[ AND A.POLICY_ID Not IN ]]>
			<foreach collection="notBackPolicyIdList" item="notBackPolicyIdList"
			index="index" open="(" close=")" separator=",">#{notBackPolicyIdList}</foreach></if>
		<if test=" backPolicyIdList != null and backPolicyIdList.size()>0 "><![CDATA[ AND A.POLICY_ID IN ]]>
			<foreach collection="backPolicyIdList" item="backPolicyIdList"
			index="index" open="(" close=")" separator=",">#{backPolicyIdList}</foreach></if>
		<![CDATA[ ORDER BY A.VALIDATE_TIME DESC]]>
	</select>
<!-- 冲正查询是否发生过保全或理赔 -->
	<select id="PA_findPolicyChangeByIdAndDriType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE,A.POLICY_ID ,A.BUSINESS_CODE 
			FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE A.CHANGE_FLAG != '0' AND ROWNUM <=  1000  ]]>
		 <include refid="PA_findPolicyChangeByIdAndDriTypeCondition" />
	</select>
	<sql id="PA_findPolicyChangeByIdAndDriTypeCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" start_date  != null and end_date != null"><![CDATA[ AND A.VALIDATE_TIME >= #{start_date} AND A.VALIDATE_TIME <= #{end_date}]]></if>
		<if test=" drive_type_list  != null and drive_type_list.size()!=0">
		<![CDATA[ AND A.DERIV_TYPE in ]]>
			<foreach collection ="drive_type_list" item="drive_type" index="index" open="(" close=")" separator=",">#{drive_type}</foreach>
		</if>
	</sql>


<!-- 查询个数操作 -->
	<select id="PA_findPolicyChangeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyChangeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATED_ID, B.CONTENT_CLOB_ID, B.PRE_POLICY_CHG, B.CHANGE_FLAG, B.FINISH_TIME, 
			B.VALIDATE_TIME, B.POLICY_CHG_ID, B.SERVICE_CODE, 
			B.POLICY_ID ,A.BUSINESS_CODE  FROM (
					SELECT ROWNUM RN, A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID ,A.BUSINESS_CODE  FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 	根据保单ID查询上一条的policy_chg_id -->
	<select id="PA_findPolicyChangePrePolicyChgByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT MAX(policy_chg_id) AS policy_chg_id,policy_id FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL WHERE 1=1 ]]>
		<include refid="PA_findPolicyChangePrePolicyChgByPolicyIdCondition" />
		<![CDATA[ GROUP BY POLICY_ID ]]> 
	</select>
	<!-- 保全有效操作 -->
	<select id="PA_findPolicyChangeCSValidByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT *
  FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL t
 where t.service_code in (SELECT s.service_code FROM APP___PAS__DBUSER.t_service s)
   and t.policy_id = #{policy_id}]]>
	</select>
	<!-- 理赔 -->
	<select id="PA_findPolicyChangeCLValidByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT *
  FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL t
 where t.service_code = 'CLMEND'
   and t.policy_id =
       (SELECT m.policy_id
          FROM APP___PAS__DBUSER.t_contract_master m
         where m.policy_code = #{policy_code} )]]>
	</select>
	
	<select id="PA_findByPoChgIdLinkedQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT T.* FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL T WHERE T.POLICY_ID = #{policy_id} AND T.POLICY_CHG_ID > #{policy_chg_id}]]>
	</select>
	
		<!-- 按索引查询操作 -->	
	<select id="findCsPolicyChangeById" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_STATUS, A.PRINT_NUM , A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME, 
             A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
            A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
            A.NOTIFY_TYPE, 
            A.ORDER_ID, A.POLICY_CODE ,  A.PRINT_TYPE,
			A.DELAY_CAUSE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE A.POLICY_CHG_ID = #{policy_chg_id}   ]]>
	</select>
	
	<!-- 根据条件查询个数操作 -->
	<select id="PA_findPolicyChangeTotalByCon" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  ]]>
		<include refid="PA_policyChangeWhereCondition" />
	</select>
	<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyChangeHistoryForRB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select POLICY_CHG_ID, VALIDATE_TIME, BUSINESS_CODE ,SERVICE_CODE , A.POLICY_ID,
       				  INSERT_BY 
					  from APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A
					  WHERE A.SERVICE_CODE != '00'
					  	AND A.SERVICE_CODE != 'RB'
					  	AND A.SERVICE_CODE != 'XQ'
					  	AND A.SERVICE_CODE != 'CLMBACK'
					  	AND A.SERVICE_CODE != 'RZ'
					  	AND A.SERVICE_CODE != 'SP'
					  	AND A.SERVICE_CODE != 'RG'
					  	AND A.SERVICE_CODE != 'XQ'
					  	 ]]>
		 <include refid="PA_policyChangeWhereCondition" />
		 <if test=" validate_time_end  != null  and  validate_time_end  != ''  "><![CDATA[ AND trunc(A.VALIDATE_TIME) <= #{validate_time_end} ]]></if>
		 <![CDATA[ ORDER BY trunc(A.VALIDATE_TIME) DESC,A.POLICY_CHG_ID DESC ]]> 
	</select>
	<select id="PA_findPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE,c.policy_code, a.DERIV_TYPE,
			A.POLICY_ID,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A ,APP___PAS__DBUSER.t_contract_master c WHERE 1 = 1 and a.policy_id = c.policy_id  ]]>
		<include refid="PA_queryPolicyChangeByPolicyChgIdCondition" />
	</select>
	
	<!-- 查询单条数据 -->
	<select id="PA_findPolicyChangeByBcAndPid" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.RELATED_ID, A.CONTENT_CLOB_ID,A.PRE_POLICY_CHG,A.CHANGE_FLAG,A.FINISH_TIME,A.VALIDATE_TIME,A.POLICY_CHG_ID,A.SERVICE_CODE,
       A.POLICY_ID,A.BUSINESS_CODE
       FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A where 1 = 1 AND A.BUSINESS_CODE = #{business_code} AND A.POLICY_ID = #{policy_id} and rownum=1]]>
	</select>
	
	<!-- 查询回退变更之后的所有变更信息 -->
	<select id="PA_findAllPolicyChangeHistoryForRBTWO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select POLICY_CHG_ID, VALIDATE_TIME, business_code ,service_code , A.POLICY_ID,
       				  insert_by 
					  from APP___PAS__DBUSER.V_POLICY_CHANGE_ALL a
					  where a.service_code != '00'
					  	and a.service_code != 'RB'
					  	and A.service_code != 'XQ'
					  	and a.service_code != 'CLMBACK'
					  	and a.service_code != 'RZ'
					  	and A.service_code != 'SP'
					  	and a.deriv_type = '004'
					  	 and a.validate_time > (select b.validate_time from APP___PAS__DBUSER.V_POLICY_CHANGE_ALL b where b.policy_chg_id = #{policy_chg_id})]]>
		 <include refid="PA_queryPolicyChangeByPolicyIdCondition" />
	</select>
	<!-- 查询续期核销后的保全历史 -->
	<select id="PA_findCsserviceAfterFinishTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select A.*
  					from dev_pas.V_POLICY_CHANGE_ALL A
 					where A.insert_time > ( select max(finish_time)
                        from APP___PAS__DBUSER.T_PREM_ARAP B
                       WHERE 1 = 1
                         and B.DERIV_TYPE = '003'
                         and B.FEE_TYPE in ('G003020100',
                                            'G003020200',
                                            'G003030100',
                                            'G003030200',
                                            'G003040100',
                                            'G003040200',
                                            'G003010000')
                   		AND B.POLICY_CODE = #{policy_code}
                       )
			]]>
			<include refid="PA_queryPolicyChangeByPolicyIdCondition" />
	</select>
	<!-- 查询最后一次做AG的时间 -->
	<select id="PA_findPolicyChangeByOne" resultType="java.util.Map" parameterType="java.util.Map">
			SELECT * FROM (

							SELECT A.POLICY_CHG_ID,
							       A.POLICY_ID,
							       A.SERVICE_CODE,
							       A.CONTENT_CLOB_ID,
							       A.RELATED_ID,
							       A.CHANGE_FLAG,
							       A.VALIDATE_TIME,
							       A.PRE_POLICY_CHG,
							       A.FINISH_TIME,
							       A.INSERT_BY
							  FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A
							 WHERE 1 = 1
							   AND A.POLICY_ID = #{policy_id}
							   AND A.SERVICE_CODE = 'AG'
							 ORDER BY A.VALIDATE_TIME DESC
						) WHERE 1=1 AND ROWNUM=1
	</select>
	
	<!-- 查询最近的一个变更记录 -->
	<select id="PA_findChangeByValidate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.*
		  FROM (SELECT T.*
		          FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL T
		         WHERE T.POLICY_ID =  #{policy_id}
		           AND T.VALIDATE_TIME <= #{validate_time}
		         ORDER BY T.POLICY_CHG_ID DESC) A
		WHERE ROWNUM = 1	]]>
	</select>
	
	<!-- 查询保费增加的宝全项的数据 -->
	<select id="PA_findAddAmount" resultType="java.util.Map" parameterType="java.util.Map">
	       <![CDATA[
	            SELECT DISTINCT TCM.POLICY_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL PC,APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
	            
	            WHERE 1=1
	            
	            AND TCM.POLICY_ID = PC.POLICY_ID
	       ]]>
	       <if test="service_bank!=null and service_bank!=''">
	       
	           AND TCM.SERVICE_BANK = #{service_bank}
	       </if>
	       
	       <if test="policy_code_list!=null and policy_code_list.size()>0">
	            <![CDATA[ AND TCM.POLICY_CODE IN ]]>
	             <foreach collection="policy_code_list" item="codes"
							index="index" open="(" close=")" separator=",">#{codes}</foreach>
	       </if>
	       <if test="submit_channel_list!=null and submit_channel_list.size()>0">
	            <![CDATA[  AND TCM.SUBMIT_CHANNEL IN ]]>
	            <foreach collection="submit_channel_list" item="channel"
							index="channel" open="(" close=")" separator=",">#{channel}</foreach>
	       </if>
	       <if test="service_codes!=null and service_codes.size()>0">
	           <![CDATA[ AND PC.SERVICE_CODE IN ]]>
		       <foreach collection="service_codes" item="service_code"
							index="service_code" open="(" close=")" separator=",">#{service_code}</foreach>
	       </if>
	       
	       <if test="start_date!=null">
	             <![CDATA[
	               AND PC.VALIDATE_TIME >= #{start_date}
	             ]]>
	       </if>
	       <if test="end_date!=null">
	              <![CDATA[
	               AND PC.VALIDATE_TIME <= #{end_date}
	             ]]>
	       </if>
	</select>
	<!--查询贷款中止记录  -->
	<select id="PA_findPolicyChangeHistory" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[
		   select A.SERVICE_CODE,A.POLICY_CHG_ID
     from APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A
    where A.POLICY_ID =#{policy_id} AND A.SERVICE_CODE=#{service_code}
		
		]]>
	
	</select>
	<!--保单变更记录  -->
	<select id="PA_findPolicyChangeInfo" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[
	SELECT A.SERVICE_CODE,A.POLICY_CHG_ID,A.POLICY_CODE,A.VALIDATE_TIME
     FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A
    where A.POLICY_ID =#{policy_id} AND A.SERVICE_CODE= 'PU' AND A.VALIDATE_TIME IS NOT NULL
		
		]]>
	
	</select>
	
	
	<select id="queryCountForRB" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  
				SELECT COUNT(1)
					  FROM (select A.POLICY_ID,
					               A.BUSINESS_CODE,
					               A.SERVICE_CODE,
					               A.VALIDATE_TIME,
					               (select distinct ta.accept_status
							          from APP___PAS__DBUSER.t_cs_accept_change ta,
							               APP___PAS__DBUSER.t_cs_policy_change tb
							         where ta.change_id = tb.change_id
							           and ta.accept_id = tb.accept_id
							           and ta.accept_code = a.business_code
							           and tb.policy_id = a.policy_id) accept_status, /*保全受理状态*/
					               nvl((select tc.rollback_jump_flag
					                     from APP___PAS__DBUSER.t_cs_service_org_cfg tc
					                    where TC.service_code = a.service_code),
					                   0) rollback_jump_flag /*是否可跳过 0-否 1-是*/
					          from APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A
					         WHERE A.SERVICE_CODE != '00'
					           AND A.SERVICE_CODE != 'RB'
					           AND A.SERVICE_CODE != 'XQ'
					           AND A.SERVICE_CODE != 'CLMBACK'
					           AND A.SERVICE_CODE != 'RZ'
					           AND A.SERVICE_CODE != 'SP'
					           AND A.SERVICE_CODE != 'RG'
					           AND A.SERVICE_CODE != 'XQ'
					           AND A.POLICY_ID IN ${policyIds}			           
					           AND to_date(to_char(A.VALIDATE_TIME, 'yyyy-MM-dd HH24:mi:ss'),'yyyy-MM-dd HH24:mi:ss') > #{validate_time}
					         ORDER BY A.POLICY_ID, A.VALIDATE_TIME DESC, A.POLICY_CHG_ID DESC) T
					 WHERE T.rollback_jump_flag = 0
					   AND (T.accept_status is null or t.accept_status = 18)
					  
											
		]]>
	</select>	
	
	<!--查询本年度已申请领取过账户金额次数-->
	<select id="queryAccountAmountForAI" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		 	select count(1)
 			 from APP___PAS__DBUSER.T_CONTRACT_MASTER  TCM,
      			  APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC,
       			  APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC,
                  APP___PAS__DBUSER.T_CS_APPLICATION   TCA
 			where TCM.policy_id = TCPC.policy_id
   				  and TCM.policy_code = TCPC.policy_code
   				  and TCPC.accept_id = TCAC.accept_id
   				  and TCPC.change_id = TCAC.change_id
                  and TCPC.change_id = TCA.change_id
                  and TCM.policy_code = #{policy_code}
                  and TCAC.service_code = 'AI'
                  and TCAC.accept_status = '18'
                  and TCAC.validate_time >= trunc(TCA.apply_time, 'year') /*当前年度的起始日*/
                  and TCAC.validate_time <= (add_months(trunc(TCA.apply_time, 'yyyy'), 12) - 1) /*当前年度的截止日*/
		]]>
	</select>
	
	<!-- 查询之后最近一期的记录  -->
	<select id="PA_findPolicyChangeRecording" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT a.*  
			  FROM dev_pas.V_POLICY_CHANGE_ALL a 
			 WHERE 1 = 1 
			 ]]>
				<if test=" policy_chg_id  != null  and  policy_chg_id  != ''  "><![CDATA[ AND A.policy_chg_id > #{policy_chg_id} AND A.policy_chg_id < 1000000000000  ]]></if>
				<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
				<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
				<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type}]]></if>
			 <![CDATA[
			   and rownum = 1
			  order by a.policy_chg_id]]>
	</select>
	<select id="PA_findPolicyChangeByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  ]]>
		<include refid="PA_policyChangeWhereCondition" />
	</select>
	
	
	<select id="PA_queryPolicyChangeByPolicyIdAndTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT T.POLICY_CHG_ID FROM(
		SELECT A.RELATED_ID, A.CONTENT_CLOB_ID, A.PRE_POLICY_CHG, A.CHANGE_FLAG, A.FINISH_TIME, 
			A.VALIDATE_TIME, A.POLICY_CHG_ID, A.SERVICE_CODE, 
			A.POLICY_ID,A.BUSINESS_CODE FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A WHERE 1 = 1  
			AND TO_DATE(TO_CHAR(A.INSERT_TIME,'yyyy-MM-dd'),'yyyy-MM-dd') = TO_DATE(TO_CHAR(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd')
			]]>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<![CDATA[ ORDER BY A.POLICY_CHG_ID DESC  ) T WHERE ROWNUM = 1 ]]>
	</select>
	
	
	<!-- 理赔调用保全获取养老金报送保全批单序号 -->
	<select id="PA_queryObtainEndorsementNumber" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT APP___PAS__DBUSER.S_POLICY_CHANGE__POLICY_CHG_ID.NEXTVAL  AS endorsement_number FROM DUAL ]]>
	</select>

	<select id="PA_findPolicyChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select tpc.policy_chg_id
			  from dev_pas.t_cs_accept_change tcac
			 inner join dev_pas.t_cs_policy_change tcpc
				on tcpc.accept_id = tcac.accept_id
			 inner join dev_pas.v_policy_change_all tpc
				on tpc.policy_id = tcpc.policy_id
			   and tpc.business_code = tcac.accept_code
			 where 1 = 1
		]]>
		<if test=" policy_chg_id  != null "><![CDATA[ AND tcpc.policy_chg_id = #{policy_chg_id} ]]></if>
		<if test=" accept_code  != null "><![CDATA[ AND tcac.accept_code = #{accept_code} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND tcac.accept_id = #{accept_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND tcpc.policy_id = #{policy_id} ]]></if>
	</select>
	
	
	<select id="findPolicyChangeForPSD" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		 SELECT *
				  FROM (SELECT A.RELATED_ID,
				               A.CONTENT_CLOB_ID,
				               A.PRE_POLICY_CHG,
				               A.CHANGE_FLAG,
				               A.FINISH_TIME,
				               A.VALIDATE_TIME,
				               A.POLICY_CHG_ID,
				               A.SERVICE_CODE,
				               A.POLICY_ID,
				               A.BUSINESS_CODE
				          FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL A
				         WHERE A.SERVICE_CODE = 'PSD'
				           AND A.VALIDATE_TIME >= #{validate_time}
				           AND A.POLICY_ID = #{policy_id}
				         ORDER BY A.VALIDATE_TIME) T
				 WHERE ROWNUM = 1
	]]>
	</select>
	
	<select id="findOpeatedServiceFlag" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		  SELECT DISTINCT TA.ACCEPT_STATUS
                 FROM APP___PAS__DBUSER.T_POLICY_CHANGE T
                 LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TA
                      ON TA.ACCEPT_CODE = T.BUSINESS_CODE
                 WHERE T.POLICY_ID = #{policy_id}
                 AND T.SERVICE_CODE IN ('TC', 'BC')
	]]>
	</select>
	
</mapper>
