<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.sendnote.dao.impl.BonusErrorLogDaoImpl">

	<sql id="bonusErrorLogWhereCondition">
		<if test=" send_date  != null  and  send_date  != ''  "><![CDATA[ AND A.SEND_DATE = #{send_date} ]]></if>
		<if test=" holder_customer_id  != null "><![CDATA[ AND A.HOLDER_CUSTOMER_ID = #{holder_customer_id} ]]></if>
		<if test=" busi_group != null and busi_group != ''  "><![CDATA[ AND A.BUSI_GROUP = #{busi_group} ]]></if>
		<if test=" liab_effect_sa  != null "><![CDATA[ AND A.LIAB_EFFECT_SA = #{liab_effect_sa} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" effect_sa  != null "><![CDATA[ AND A.EFFECT_SA = #{effect_sa} ]]></if>
		<if test=" agent_channel != null and agent_channel != ''  "><![CDATA[ AND A.AGENT_CHANNEL = #{agent_channel} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" department != null and department != ''  "><![CDATA[ AND A.DEPARTMENT = #{department} ]]></if>
		<if test=" liab_bonus_sa  != null "><![CDATA[ AND A.LIAB_BONUS_SA = #{liab_bonus_sa} ]]></if>
		<if test=" error_type != null and error_type != ''  "><![CDATA[ AND A.ERROR_TYPE = #{error_type} ]]></if>
		<if test=" user_num != null and user_num != ''  "><![CDATA[ AND A.USER_NUM = #{user_num} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryBonusErrorLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addBonusErrorLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_BONUS_ERROR_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BONUS_ERROR_LOG
  (SEND_DATE,
   HOLDER_CUSTOMER_ID,
   BUSI_GROUP,
   LIAB_EFFECT_SA,
   BONUS_SA,
   INSERT_TIME,
   EFFECT_SA,
   UPDATE_TIME,
   AGENT_CHANNEL,
   INSERT_TIMESTAMP,
   LOG_ID,
   ORGAN_CODE,
   POLICY_CODE,
   UPDATE_BY,
   UPDATE_TIMESTAMP,
   INSERT_BY,
   DEPARTMENT,
   LIAB_BONUS_SA,
   ERROR_TYPE,
   USER_NUM,
   bonus_allot)
VALUES
  (#{send_date,
   jdbcType = DATE},
   #{holder_customer_id,
   jdbcType = NUMERIC},
   #{busi_group,
   jdbcType = VARCHAR},
   #{liab_effect_sa,
   jdbcType = NUMERIC},
   #{bonus_sa,
   jdbcType = NUMERIC},
   SYSDATE,
   #{effect_sa,
   jdbcType = NUMERIC},
   SYSDATE,
   #{agent_channel,
   jdbcType = VARCHAR},
   CURRENT_TIMESTAMP,
   #{log_id,
   jdbcType = NUMERIC},
   #{organ_code,
   jdbcType = VARCHAR},
   #{policy_code,
   jdbcType = VARCHAR},
   #{update_by,
   jdbcType = NUMERIC},
   CURRENT_TIMESTAMP,
   #{insert_by,
   jdbcType = NUMERIC},
   #{department,
   jdbcType = VARCHAR},
   #{liab_bonus_sa,
   jdbcType = NUMERIC},
   #{error_type,
   jdbcType = VARCHAR},
   #{user_num,
   jdbcType = VARCHAR},
   #{bonus_allot,
   jdbcType = VARCHAR})
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteBonusErrorLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BONUS_ERROR_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateBonusErrorLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BONUS_ERROR_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    SEND_DATE = #{send_date, jdbcType=DATE} ,
		    HOLDER_CUSTOMER_ID = #{holder_customer_id, jdbcType=NUMERIC} ,
			BUSI_GROUP = #{busi_group, jdbcType=VARCHAR} ,
		    LIAB_EFFECT_SA = #{liab_effect_sa, jdbcType=NUMERIC} ,
		    BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
		    EFFECT_SA = #{effect_sa, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			AGENT_CHANNEL = #{agent_channel, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			DEPARTMENT = #{department, jdbcType=VARCHAR} ,
		    LIAB_BONUS_SA = #{liab_bonus_sa, jdbcType=NUMERIC} ,
			ERROR_TYPE = #{error_type, jdbcType=VARCHAR} ,
			USER_NUM = #{user_num, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findBonusErrorLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SEND_DATE, A.HOLDER_CUSTOMER_ID, A.BUSI_GROUP, A.LIAB_EFFECT_SA, A.BONUS_SA, A.EFFECT_SA, 
			A.AGENT_CHANNEL, A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, 
			A.DEPARTMENT, A.LIAB_BONUS_SA, A.ERROR_TYPE, A.USER_NUM FROM APP___PAS__DBUSER.T_BONUS_ERROR_LOG A WHERE 1 = 1  ]]>
		<include refid="queryBonusErrorLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapBonusErrorLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SEND_DATE, A.HOLDER_CUSTOMER_ID, A.BUSI_GROUP, A.LIAB_EFFECT_SA, A.BONUS_SA, A.EFFECT_SA, 
			A.AGENT_CHANNEL, A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, 
			A.DEPARTMENT, A.LIAB_BONUS_SA, A.ERROR_TYPE, A.USER_NUM FROM APP___PAS__DBUSER.T_BONUS_ERROR_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllBonusErrorLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SEND_DATE, A.HOLDER_CUSTOMER_ID, A.BUSI_GROUP, A.LIAB_EFFECT_SA, A.BONUS_SA, A.EFFECT_SA, 
			A.AGENT_CHANNEL, A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, 
			A.DEPARTMENT, A.LIAB_BONUS_SA, A.ERROR_TYPE, A.USER_NUM FROM APP___PAS__DBUSER.T_BONUS_ERROR_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findBonusErrorLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BONUS_ERROR_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryBonusErrorLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SEND_DATE, B.HOLDER_CUSTOMER_ID, B.BUSI_GROUP, B.LIAB_EFFECT_SA, B.BONUS_SA, B.EFFECT_SA, 
			B.AGENT_CHANNEL, B.LOG_ID, B.ORGAN_CODE, B.POLICY_CODE, 
			B.DEPARTMENT, B.LIAB_BONUS_SA, B.ERROR_TYPE, B.USER_NUM FROM (
					SELECT ROWNUM RN, A.SEND_DATE, A.HOLDER_CUSTOMER_ID, A.BUSI_GROUP, A.LIAB_EFFECT_SA, A.BONUS_SA, A.EFFECT_SA, 
			A.AGENT_CHANNEL, A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, 
			A.DEPARTMENT, A.LIAB_BONUS_SA, A.ERROR_TYPE, A.USER_NUM FROM APP___PAS__DBUSER.T_BONUS_ERROR_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
