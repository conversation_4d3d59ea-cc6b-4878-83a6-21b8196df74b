<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayDueDao">

	<sql id="payDueWhereCondition">
		<if test=" survival_invest_result  != null "><![CDATA[ AND A.SURVIVAL_INVEST_RESULT = #{survival_invest_result} ]]></if>
		<if test=" cancel_cause_desc != null and cancel_cause_desc != ''  "><![CDATA[ AND A.CANCEL_CAUSE_DESC = #{cancel_cause_desc} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" terminal_bonus  != null "><![CDATA[ AND A.TERMINAL_BONUS = #{terminal_bonus} ]]></if>
		<if test=" sum_assured_paid  != null "><![CDATA[ AND A.SUM_ASSURED_PAID = #{sum_assured_paid} ]]></if>
		<if test=" pay_num  != null "><![CDATA[ AND A.PAY_NUM = #{pay_num} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" total_bonus  != null "><![CDATA[ AND A.TOTAL_BONUS = #{total_bonus} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" pay_id  != null "><![CDATA[ AND A.PAY_ID = #{pay_id} ]]></if>
		<if test=" compensation  != null "><![CDATA[ AND A.COMPENSATION = #{compensation} ]]></if>
		<if test=" manual_extra_date  != null  and  manual_extra_date  != ''  "><![CDATA[ AND A.MANUAL_EXTRA_DATE = #{manual_extra_date} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survival_invest_flag  != null "><![CDATA[ AND A.SURVIVAL_INVEST_FLAG = #{survival_invest_flag} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" survival_mode  != null "><![CDATA[ AND A.SURVIVAL_MODE = #{survival_mode} ]]></if>
		<if test="start_date != null and start_date != ''"><![CDATA[ AND A.PAY_DUE_DATE > #{start_date} ]]></if>
		<if test=" survival_type  != null  and  survival_type  != ''  "><![CDATA[ AND A.survival_type = #{survival_type} ]]></if>
		<if test=" pay_due_date_1  != null  and  pay_due_date_1  != ''  "><![CDATA[ AND A.PAY_DUE_DATE > #{pay_due_date_1}  AND A.PAY_NUM > 1 ]]></if>
	</sql>



<!-- 按索引生成的查询条件 -->	
	<sql id="queryPayDueByPayIdCondition">
		<if test=" pay_id  != null "><![CDATA[ AND A.PAY_ID = #{pay_id} ]]></if>
	</sql>	

	<sql id="queryPayDueByPayDueDateCondition">
		<if test=" pay_due_date  != null "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
	</sql>	
	<sql id="queryPayDueByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	
	<sql id="queryPayDueByFeeStatusCondition">
		<if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
	</sql>	
	<sql id="queryPayDueByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="queryPayDueByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPayDue"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="pay_id">
			SELECT APP___PAS__DBUSER.S_PAY_DUE__PAY_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_DUE(
				DEDUCTION_FLAG,SURVIVAL_TYPE,SURVIVAL_MODE,SURVIVAL_INVEST_RESULT, CANCEL_CAUSE_DESC, UNIT_NUMBER, PRODUCT_CODE, ITEM_ID, BUSI_PROD_CODE, TERMINAL_BONUS, 
				SUM_ASSURED_PAID, INSERT_TIMESTAMP, UPDATE_BY, PAY_NUM, FEE_AMOUNT, POLICY_CHG_ID, TOTAL_BONUS, 
				BUSI_ITEM_ID, POLICY_ID, PAY_ID, COMPENSATION, MANUAL_EXTRA_DATE, PLAN_ID, INSERT_TIME, 
				FEE_STATUS, LIAB_ID, UPDATE_TIME, POLICY_CODE, SURVIVAL_INVEST_FLAG, PAY_DUE_DATE, UPDATE_TIMESTAMP, 
				INSERT_BY,REISSUE_INTEREST,PAY_SOURCE,FINISH_TIME,ADJUST_AMOUNT,ADJUST_AMOUNT_AFTER,ADJUST_CAUSE,UNIVERSAL_POLICY_CODE,UNIVERSAL_BUSI_ITEM_ID ) 
			VALUES (
				#{deduction_flag, jdbcType=VARCHAR} , #{survival_type, jdbcType=NUMERIC} , #{survival_mode, jdbcType=NUMERIC} ,#{survival_invest_result, jdbcType=NUMERIC}, #{cancel_cause_desc, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{product_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{terminal_bonus, jdbcType=NUMERIC} 
				, #{sum_assured_paid, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{pay_num, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{total_bonus, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{pay_id, jdbcType=NUMERIC} , #{compensation, jdbcType=NUMERIC} , #{manual_extra_date, jdbcType=DATE} , #{plan_id, jdbcType=NUMERIC} , SYSDATE 
				, #{fee_status, jdbcType=VARCHAR} , #{liab_id, jdbcType=NUMERIC} , SYSDATE , #{policy_code, jdbcType=VARCHAR} , #{survival_invest_flag, jdbcType=NUMERIC} , #{pay_due_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{reissue_interest, jdbcType=NUMERIC} , #{pay_source, jdbcType=NUMERIC},#{finish_time, jdbcType=DATE},#{adjust_amount, jdbcType=NUMERIC} ,#{adjust_amount_after, jdbcType=NUMERIC} ,#{adjust_cause, jdbcType=VARCHAR},#{universal_policy_code, jdbcType=VARCHAR},#{universal_busi_item_id, jdbcType=NUMERIC} )  
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePayDue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_DUE WHERE  PAY_ID = #{pay_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePayDue" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE ]]>
		<set>
		<trim suffixOverrides=",">
			DEDUCTION_FLAG = #{deduction_flag, jdbcType=VARCHAR} ,
			SURVIVAL_TYPE = #{survival_type, jdbcType=NUMERIC} ,
		    SURVIVAL_INVEST_RESULT = #{survival_invest_result, jdbcType=NUMERIC} ,
			CANCEL_CAUSE_DESC = #{cancel_cause_desc, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    TERMINAL_BONUS = #{terminal_bonus, jdbcType=NUMERIC} ,
		    SUM_ASSURED_PAID = #{sum_assured_paid, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PAY_NUM = #{pay_num, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    TOTAL_BONUS = #{total_bonus, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    COMPENSATION = #{compensation, jdbcType=NUMERIC} ,
		    MANUAL_EXTRA_DATE = #{manual_extra_date, jdbcType=DATE} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
		    LIAB_ID = #{liab_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVIVAL_INVEST_FLAG = #{survival_invest_flag, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SURVIVAL_MODE = #{survival_mode, jdbcType=NUMERIC} ,
		    PAY_SOURCE = #{pay_source, jdbcType=NUMERIC} ,		
		    PLAN_UNIT_NUMBER= #{plan_unit_number,jdbcType=VARCHAR} , 
		    FINISH_TIME=#{finish_time,jdbcType=DATE},
		    ADJUST_AMOUNT= #{adjust_amount, jdbcType=NUMERIC} ,
		    ADJUST_AMOUNT_AFTER= #{adjust_amount_after, jdbcType=NUMERIC} ,
		    ADJUST_CAUSE= #{adjust_cause, jdbcType=VARCHAR} ,     		    
		</trim>
		</set>
		<![CDATA[ WHERE  PAY_ID = #{pay_id}  ]]>
	</update>


	<update id="updatePayDueForCs" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE ]]>
		<set>
		<trim suffixOverrides=",">
			DEDUCTION_FLAG = #{deduction_flag, jdbcType=VARCHAR} ,
			SURVIVAL_TYPE = #{survival_type, jdbcType=NUMERIC} ,
		    SURVIVAL_INVEST_RESULT = #{survival_invest_result, jdbcType=NUMERIC} ,
			CANCEL_CAUSE_DESC = #{cancel_cause_desc, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    TERMINAL_BONUS = #{terminal_bonus, jdbcType=NUMERIC} ,
		    SUM_ASSURED_PAID = #{sum_assured_paid, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PAY_NUM = #{pay_num, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    
		    TOTAL_BONUS = #{total_bonus, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    COMPENSATION = #{compensation, jdbcType=NUMERIC} ,
		    MANUAL_EXTRA_DATE = #{manual_extra_date, jdbcType=DATE} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
		    LIAB_ID = #{liab_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVIVAL_INVEST_FLAG = #{survival_invest_flag, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SURVIVAL_MODE = #{survival_mode, jdbcType=NUMERIC} ,
		    PAY_SOURCE = #{pay_source, jdbcType=NUMERIC} ,		
		    PLAN_UNIT_NUMBER= #{plan_unit_number,jdbcType=VARCHAR} , 
		    FINISH_TIME=#{finish_time,jdbcType=DATE},
		    ADJUST_AMOUNT= #{adjust_amount, jdbcType=NUMERIC} ,
		    ADJUST_AMOUNT_AFTER= #{adjust_amount_after, jdbcType=NUMERIC} ,
		    ADJUST_CAUSE= #{adjust_cause, jdbcType=VARCHAR} ,     		    
		</trim>
		</set>
		<![CDATA[ WHERE  PAY_ID = #{pay_id}  ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPayDueByPayId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="queryPayDueByPayIdCondition" />
	</select>
	
	<!-- 查询 -->
		<select id="findPayDue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="payDueWhereCondition" />
	</select>
	
	
	<select id="findPayDueByPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="queryPayDueByPayDueDateCondition" />
	</select>
	
	<select id="findPayDueByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.DEDUCTION_FLAG,A.SURVIVAL_TYPE, A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="queryPayDueByPlanIdCondition" />
	</select>
	
	<select id="findPayDueByFeeStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="queryPayDueByFeeStatusCondition" />
	</select>
	
	<select id="findPayDueByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="queryPayDueByItemIdCondition" />
	</select>
	
	<select id="findPayDueByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="queryPayDueByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapPayDue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPayDue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS,  A.PAY_SOURCE,
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE,A.REISSUE_INTEREST,A.INSERT_TIME,A.FINISH_TIME FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="payDueWhereCondition" /> 		 
		 <![CDATA[ ORDER BY A.PAY_ID DESC , A.PAY_ID DESC]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findAllPayDueForLX" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT_NUMBER, A.FEE_AMOUNT,A.POLICY_CHG_ID,A.POLICY_ID,A.PAY_ID,A.PLAN_ID,A.FEE_STATUS,A.POLICY_CODE,A.PAY_DUE_DATE
		  FROM APP___PAS__DBUSER.T_PAY_DUE A, dev_cap.t_prem_arap b
		  WHERE a.policy_code = b.policy_code AND A.PLAN_ID = #{plan_id} AND A.PAY_DUE_DATE > #{pay_due_date} 
		  AND A.FEE_STATUS = '01' and a.unit_number = b.unit_number and b.fee_status in ('01', '16')
		  ORDER BY A.PAY_ID DESC, A.Pay_Due_Date DESC ]]>
	</select>
	
	<!-- 查询申请提交日期之前所有信息 -->
	<select id="findPayDueTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  SUM(A.FEE_AMOUNT) AS FEE_AMOUNT FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE ROWNUM <=  1000 
			AND A.FEE_STATUS = #{fee_status} AND A.ITEM_ID = #{item_id} AND A.POLICY_ID = #{policy_id}  
			AND A.PAY_DUE_DATE <= #{pay_due_date}]]>
			<include refid="queryPayDueByPlanIdCondition"/>
	</select>
	
	
	<!-- 查询所有操作 -->
	<select id="findAllPayDueOrderByDueTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="payDueWhereCondition" /> 		 
		 <![CDATA[ ORDER BY A.PAY_DUE_DATE ASC ]]>
	</select>
	

<!-- 查询个数操作 -->
	<select id="findPayDueTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="payDueWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryPayDueForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.DEDUCTION_FLAG,B.SURVIVAL_TYPE, B.SURVIVAL_MODE, B.SURVIVAL_INVEST_RESULT, B.CANCEL_CAUSE_DESC, B.UNIT_NUMBER, B.PRODUCT_CODE, B.ITEM_ID, B.BUSI_PROD_CODE, B.TERMINAL_BONUS, 
			B.SUM_ASSURED_PAID, B.PAY_NUM, B.FEE_AMOUNT, B.POLICY_CHG_ID, B.TOTAL_BONUS, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.PAY_ID, B.COMPENSATION, B.MANUAL_EXTRA_DATE, B.PLAN_ID, 
			B.FEE_STATUS, B.LIAB_ID, B.POLICY_CODE, B.SURVIVAL_INVEST_FLAG, B.PAY_DUE_DATE FROM (
					SELECT ROWNUM RN,A.DEDUCTION_FLAG, A.SURVIVAL_TYPE, A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<sql id="queryPayDueByPolicyListCondition">
<!-- 		<if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if> -->
		<if test=" policyId_list  != null and policyId_list.size()!=0">
		<![CDATA[ AND A.POLICY_ID in ]]>
			<foreach collection ="policyId_list" item="policyId_list" index="index" open="(" close=")" separator=",">#{policyId_list}</foreach>
		</if>
		<if test=" feeStatus_list  != null and feeStatus_list.size()!=0">
		<![CDATA[ AND A.FEE_STATUS in ]]>
			<foreach collection ="feeStatus_list" item="feeStatus_list" index="index" open="(" close=")" separator=",">#{feeStatus_list}</foreach>
		</if>
		
	</sql>	
	
	<!-- 根据保单id列表查询数据 -->
	<select id="findPayDueByPolicyIdList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT   A.DEDUCTION_FLAG,A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  AND A.CANCEL_CAUSE_DESC IS NULL]]>
		<include refid="queryPayDueByPolicyListCondition" />
	</select>
	
	<!-- 查询生存给付应领记录 -->
	<select id="queryPayDue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT due.policy_code,
       					due.busi_prod_code,
       					due.product_code,
       					due.fee_status,
       					due.fee_amount,
       					due.pay_due_date,
       					pla.pay_plan_type,
       					pla.liab_id,
       					pla.survival_mode,
       					pla.plan_freq,
       					due.fee_amount - due.TOTAL_BONUS as amount
  					FROM APP___PAS__DBUSER.T_PAY_DUE due, APP___PAS__DBUSER.T_PAY_PLAN pla
 					WHERE  due.policy_id = (select policy_id
                                  from APP___PAS__DBUSER.t_contract_master
                                 where policy_code = #{policy_code} 
                                   and rownum = 1) 
 					AND due.plan_id = pla.plan_id]]>
 		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND due.busi_prod_code = #{busi_prod_code} ]]></if>		
 		<if test=" product_code != null and product_code != '' "><![CDATA[ AND due.product_code = #{product_code} ]]></if>		
 		<if test=" start_date != null and start_date != '' and end_date != null and end_date != ''">
			<![CDATA[  AND due.pay_due_date >= #{start_date}]]>
		    <![CDATA[  AND due.pay_due_date <= #{end_date}]]>
		</if>	
		<if test=" start_date != null and start_date != '' ">
			<![CDATA[  AND due.pay_due_date >= #{start_date}]]>
		</if>
		<if test=" end_date != null and end_date != '' ">
			 <![CDATA[  AND due.pay_due_date <= #{end_date}]]>
		</if>	
	</select>

	<!-- 根据保单号码、应领日期起期、止期查询保单生存领取信息  -->
	<!-- Modify by yangbo_wb  2016-08-22  FIX TC 缺陷ID-7272：根据设计文档，补充响应报文返回字段 -->
	<!-- Modify by zhangxj2_wb  2018-08-06 报文比对，老核心已收和未收均查，因此去掉AND DUE.FEE_STATUS = '01'  -->
	<select id="queryPayDueByPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<if test=" count_page == 'page' "><![CDATA[select * from (select rownum rn,res.* from (]]></if>
		<choose>
			<when test=" count_page == 'count' ">
				<![CDATA[ select count(1) total_num ]]>
			</when> 
			<otherwise>
				<![CDATA[ 
					SELECT DISTINCT DUE.UNIT_NUMBER,
					                DUE.BUSI_ITEM_ID,
					                CBP.BUSI_PROD_CODE,
					                DUE.FEE_AMOUNT,
					                DUE.SURVIVAL_INVEST_FLAG,
					                CBP.OLD_POL_NO,
					                (SELECT b.FUNDS_RTN_CODE
					                   FROM APP___CAP__DBUSER.V_PREM_ARAP B
					                  WHERE B.ARAP_FLAG = '2'
					                    AND B.UNIT_NUMBER = DUE.UNIT_NUMBER
					                    and ROWNUM = 1) as funds_rtn_code,
					                DUE.PAY_DUE_DATE,
					                DUE.PAY_NUM,
					                CBP.VALIDATE_DATE,
					                PP.LIAB_CODE,
					                DUE.FROM_TABLE,
					                (SELECT ACCOUNT_TYPE
					                   FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT
					                  WHERE POLICY_ID = CBP.POLICY_ID
					                    AND CBP.MASTER_BUSI_ITEM_ID IS NULL
					                    AND CBP.BUSI_ITEM_ID = BUSI_ITEM_ID
					                    AND ROWNUM = 1) ACCOUNT_TYPE,
					                (SELECT CHANNEL_TYPE
					                   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER
					                  WHERE POLICY_ID = PP.POLICY_ID) CHANNEL_TYPE,
					                (SELECT ACKNOWLEDGE_DATE
					                   FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT
					                  WHERE POLICY_ID = CBP.POLICY_ID) ACKNOWLEDGE_DATE,
					                (SELECT BEGIN_DATE
					                   FROM APP___PAS__DBUSER.T_PAY_PLAN
					                  WHERE PLAN_ID = DUE.PLAN_ID) BEGIN_DATE,
					                DUE.FEE_STATUS,
					                DUE.SURVIVAL_MODE,
					                (SELECT TSM.MODE_NAME 
					                   FROM DEV_PAS.T_SURVIVAL_MODE TSM 
					                  WHERE TSM.MODE_CODE = DUE.SURVIVAL_MODE) SURVIVAL_MODE_NAME,
					                DUE.PLAN_ID,
					                PP.SURVIVAL_INVEST_RESULT,
					                PP.LIAB_CODE,
					                PP.SURVIVAL_W_MODE,
					                (SELECT TSWM.MODE_NAME 
					                   FROM DEV_PAS.T_SURVIVAL_W_MODE TSWM 
					                  WHERE TSWM.MODE_CODE = PP.SURVIVAL_W_MODE) SURVIVAL_W_MODE_NAME,
					                (SELECT FINISH_TIME
					                   FROM DEV_CAP.V_PREM_ARAP
					                  WHERE UNIT_NUMBER = DUE.UNIT_NUMBER
					                    AND ROWNUM = 1) AS FINISH_TIME,
					                (SELECT SUM(FEE_AMOUNT)
					                   FROM DEV_PAS.V_PREM_ARAP_ALL
					                  WHERE UNIT_NUMBER = DUE.UNIT_NUMBER) AS FEE_AMOUNT2,
					                NVL((SELECT DEAL_TIME
					                      FROM APP___PAS__DBUSER.T_FUND_TRANS FT
					                     WHERE FT.POLICY_ID = PP.POLICY_ID
					                       AND FT.UNIT_NUMBER = DUE.UNIT_NUMBER
					                       AND ROWNUM = 1),
					                    (SELECT TL.TRANS_TIME
					                       FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT            PA,
					                            APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TL
					                      WHERE PA.POLICY_ID = PP.POLICY_ID
					                        AND TL.ACCOUNT_ID = PA.ACCOUNT_ID
					                        AND TL.UNIT_NUMBER = DUE.UNIT_NUMBER
					                        AND ROWNUM = 1)) DEAL_TIME
				]]>
			</otherwise>
		</choose>
		<![CDATA[ 
			FROM (SELECT 'DUE' FROM_TABLE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_FLAG,A.BUSI_ITEM_ID,A.FEE_AMOUNT,A.PAY_DUE_DATE,A.PAY_NUM,A.PLAN_ID,A.UNIT_NUMBER,A.FEE_STATUS
				FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE A.FEE_STATUS !='02' AND A.LIAB_ID IS NOT NULL AND A.POLICY_CODE=#{policy_code}
				UNION SELECT 'PLAN' FROM_TABLE,B.SURVIVAL_MODE,B.SURVIVAL_INVEST_FLAG,B.BUSI_ITEM_ID,B.INSTALMENT_AMOUNT FEE_AMOUNT,
				B.PAY_DUE_DATE,(NVL(B.PAY_NUM,0)+1) PAY_NUM,B.PLAN_ID,'' UNIT_NUMBER,'00' FEE_STATUS
				FROM APP___PAS__DBUSER.T_PAY_PLAN B WHERE B.LIAB_ID IS NOT NULL AND B.PAY_STATUS='2' AND B.POLICY_CODE=#{policy_code}
			) DUE,APP___PAS__DBUSER.T_PAY_PLAN PP, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
			WHERE DUE.PLAN_ID=PP.PLAN_ID AND DUE.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID  AND PP.PAY_PLAN_TYPE NOT IN ('12','1')
        ]]>
 		<if test=" start_date != null and start_date != '' and end_date != null and end_date != ''">
			<![CDATA[  AND DUE.PAY_DUE_DATE >= #{start_date}]]>
		    <![CDATA[  AND DUE.PAY_DUE_DATE <= #{end_date}]]>
		</if>	
 		<if test=" order_context != null and order_context != ''">
			${order_context}
		</if>	
		<if test=" count_page == 'page' ">
			<![CDATA[) res) where rn>=#{row_num_start} and rn<#{row_num_end}]]>
		</if>
	</select>
	
		<!-- 根据保单号码、应领日期起期、止期查询保单生存领取信息 -->
	<!-- Modify by yangbo_wb  2016-08-22  FIX TC 缺陷ID-7272：根据设计文档，补充响应报文返回字段 -->
	<select id="queryPayDueByPayDueDates" resultType="java.util.Map" parameterType="java.util.Map">
		<if test=" count_page == 'page' "><![CDATA[select * from (select rownum rn,res.* from (]]></if>
		<choose>
			<when test=" count_page == 'count' ">
				<![CDATA[ select count(1) total_num ]]>
			</when>
			<otherwise>
				<![CDATA[ 
			SELECT DISTINCT DUE.PAY_ID,DUE.UNIT_NUMBER,DUE.BUSI_ITEM_ID,DUE.BUSI_PROD_CODE,
			DUE.FEE_AMOUNT,DUE.PAY_DUE_DATE,DUE.FEE_STATUS,DUE.SURVIVAL_MODE,DUE.PLAN_ID,DUE.SURVIVAL_INVEST_FLAG,
			(SELECT LIAB_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE PLAN_ID=DUE.PLAN_ID) liab_code,
			(SELECT SURVIVAL_W_MODE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE PLAN_ID=DUE.PLAN_ID) AS survival_w_mode,
			(SELECT FUNDS_RTN_CODE FROM APP___PAS__DBUSER.T_PREM WHERE DUE.UNIT_NUMBER=UNIT_NUMBER 
			  AND DUE.POLICY_CODE=POLICY_CODE AND DUE.BUSI_PROD_CODE=BUSI_PROD_CODE AND ROWNUM=1) AS bank_succ_flag, 
			(SELECT PAYREFNO FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE UNIT_NUMBER=DUE.UNIT_NUMBER AND ROWNUM=1) AS payrefno,
          	(SELECT FINISH_TIME FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE UNIT_NUMBER=DUE.UNIT_NUMBER AND ROWNUM=1) AS finish_time,
          	(SELECT FEE_AMOUNT FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE UNIT_NUMBER=DUE.UNIT_NUMBER AND ROWNUM=1) AS fee_amount2
				]]>
			</otherwise>
		</choose>
		<![CDATA[ 
			FROM APP___PAS__DBUSER.T_PAY_DUE DUE 
			WHERE DUE.POLICY_CODE = #{policy_code}
			AND DUE.FEE_STATUS = '01'
        ]]>
 		<if test=" start_date != null and start_date != '' and end_date != null and end_date != ''">
			<![CDATA[  AND DUE.PAY_DUE_DATE >= #{start_date}]]>
		    <![CDATA[  AND DUE.PAY_DUE_DATE <= #{end_date}]]>
		</if>	
 		<if test=" order_context != null and order_context != ''">
			${order_context}
		</if>	
		<if test=" count_page == 'page' ">
			<![CDATA[) res) where rn>=#{row_num_start} and rn<#{row_num_end}]]>
		</if>
	</select>
	
	 <!-- R00101000142生存领取(给付)信息查询  -->
	<select id="queryPayDues" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select due.pay_due_date,
			       due.policy_code,
			       (select cu.customer_name
			          from APP___PAS__DBUSER.T_customer cu, APP___PAS__DBUSER.T_policy_holder h
			         where h.customer_id = cu.customer_id
			           and h.policy_id = #{policy_id}) as customer_name,
			       due.busi_prod_code,
			       (select t.product_abbr_name
			          from APP___PAS__DBUSER.T_business_product t
			         where t.product_code_std = due.busi_prod_code
			            or t.product_code_sys = due.busi_prod_code
			           and rownum = 1) as product_abbr_name,
			       tpp.PAY_PLAN_TYPE as pay_gettype,
			       ar.fee_status,
			       due.fee_amount
			  from APP___PAS__DBUSER.T_pay_due due, APP___PAS__DBUSER.T_prem_arap ar,APP___PAS__DBUSER.T_pay_plan tpp
			 where due.policy_id = #{policy_id}
			   and due.unit_number = ar.unit_number(+)
			   and due.PLAN_ID=tpp.PLAN_ID(+) and tpp.PAY_PLAN_TYPE='3'	
		]]>
   <if test=" start_date != null and start_date != '' and end_date != null and end_date != ''">
    <![CDATA[ and to_char(due.pay_due_date,'yyyy-mm-dd') >= #{start_date}
   		      and to_char(due.pay_due_date,'yyyy-mm-dd') <= #{end_date}
   	]]>
   	</if>	
	</select>
	
	<!--by zhaoyoan_wb 根据保单号及险种信息查询年金满期金信息 -->
	<select id="queryAnnuityPayByRiskInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT PD.POLICY_CODE,PD.BUSI_ITEM_ID,PD.BUSI_PROD_CODE,PD.PRODUCT_CODE,
			PP.PAY_PLAN_TYPE,PD.FEE_AMOUNT,PD.PAY_DUE_DATE,PP.LIAB_CODE,
			(SELECT LIAB_NAME FROM APP___PAS__DBUSER.T_LIABILITY WHERE LIAB_ID=PD.LIAB_ID) liab_name 
			FROM APP___PAS__DBUSER.T_PAY_DUE PD 
			LEFT JOIN APP___PAS__DBUSER.T_PAY_PLAN PP ON PP.PLAN_ID=PD.PLAN_ID 
			WHERE 1=1
			AND PP.POLICY_CODE=#{policy_code}
			AND PP.BUSI_ITEM_ID=#{busi_item_id}
			AND PD.PAY_DUE_DATE <= #{apply_time}
			AND PD.FEE_STATUS='00'
			AND NOT EXISTS(SELECT 1 FROM APP___CAP__DBUSER.T_CASH_DETAIL WHERE UNIT_NUMBER=PD.UNIT_NUMBER AND FEE_STATUS='01')
		]]></select>
	<!-- AND NOT EXISTS(SELECT 1 FROM DEV_PAS.T_PRODUCT_LIABILITY_CHANGE WHERE POLICY_ID=PP.POLICY_ID 
		AND BUSI_ITEM_ID=PP.BUSI_ITEM_ID AND LIABILITY_CHANGED='4' AND CHANGE_DATE<PD.PAY_DUE_DATE)
	AND NOT EXISTS(SELECT 1 FROM DEV_PAS.T_PRODUCT_LIABILITY_CHANGE WHERE POLICY_ID=PP.POLICY_ID 
		AND BUSI_ITEM_ID=PP.BUSI_ITEM_ID AND LIABILITY_CHANGED='3' AND END_CAUSE IN('08','06') AND CHANGE_DATE<PD.PAY_DUE_DATE) -->

	
	<!--by pengren_wb 领取标准查询 -->
	<select id="queryFeeAmountByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT sum(t.fee_amount) fee_amount
  				FROM APP___PAS__DBUSER.t_pay_due t
 				where t.fee_status = '00'
 				AND t.POLICY_ID = #{policy_id}
		]]>
	</select>
	<!-- 某时段内的累计领取生存金额 -->
	<select id="queryBetweenDateAmount" resultType="java.util.Map" parameterType="java.util.Map">
			SELECT nvl(sum(a.fee_amount), 0)  fee_amount
			  FROM APP___PAS__DBUSER.T_PAY_DUE A
			  LEFT JOIN APP___PAS__DBUSER.T_PAY_PLAN B
			    ON A.plan_id = B.plan_id
			 WHERE A.FEE_STATUS IN ('00', '01')
			   AND B.PAY_DUE_DATE between #{start_date} AND #{end_date}
			   and b.item_id = #{item_id}
	</select>
	
	
	<update id="updatePayDueStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE ]]>
		<set>
		<trim suffixOverrides=",">			
		<if test=" fee_status != null">	FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,	</if>		
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  PAY_ID = #{pay_id}  ]]>
	</update>
	
	<select id="queryPayDueDateByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		select due.pay_due_date 
        from APP___PAS__DBUSER.T_PAY_DUE due
        where 1=1
        <if test=" policy_code != null and policy_code != ''  ">
        	<![CDATA[
        	 AND due.POLICY_CODE = #{policy_code} 
        	]]>
        </if>
         <if test=" start_date != null and start_date != '' ">
		    <![CDATA[ 
		    	and due.pay_due_date >= #{start_date}
		   	]]>
   		</if>	
   		 <if test="  end_date != null and end_date != ''">
		    <![CDATA[ 
		   		and due.pay_due_date<= #{end_date}
		   	]]>
   		</if>	
   		<if test=" fee_status != null and fee_status != ''  ">
   			<![CDATA[
   				 AND A.FEE_STATUS = #{fee_status} 
   			]]>
   		</if>
        order by due.pay_due_date
	</select>
	
	<!-- 查询满期金 -->
	<select id="query_FeeAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT T.FEE_AMOUNT,
      	  T.PAY_ID,
          T.ITEM_ID
          FROM APP___PAS__DBUSER.T_PAY_DUE T WHERE T.PLAN_ID IN
		(SELECT M.PLAN_ID FROM APP___PAS__DBUSER.T_PAY_PLAN M WHERE M.PAY_PLAN_TYPE ='4')
		AND T.ITEM_ID=#{item_id} AND T.FEE_STATUS=00
		]]>
	</select>
	
	<!-- 查询保全申请提交日期有无前无实际领取 -->
	<select id="queryHavePayBeforeApplyTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT T.FEE_AMOUNT,
      	  T.PAY_ID
          FROM APP___PAS__DBUSER.T_PAY_DUE T WHERE  t.policy_id = #{policy_id} and item_id=#{item_id}
			and t.FEE_STATUS in ('01','19') and t.PAY_DUE_DATE  <=#{pay_due_date, jdbcType=DATE}
		]]>
	</select>
	
		<!-- 更新满期金状态 -->
	<select id="update_PayDues" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		update dev_pas.t_pay_due td set td.fee_status=01 ,TD.FEE_AMOUNT=0 WHERE TD.PLAN_ID IN
		(SELECT M.PLAN_ID FROM APP___PAS__DBUSER.T_PAY_PLAN M WHERE M.PAY_PLAN_TYPE ='4')
		AND TD.ITEM_ID=#{item_id}
		]]>
	</select>
	
	<!-- 查询保全申请提交日期有无前无实际领取 -->
	<select id="findPayDueFeeAmountBy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT SUM(T.FEE_AMOUNT) FEE_AMOUNT
          FROM APP___PAS__DBUSER.T_PAY_DUE T WHERE  t.item_id = #{item_id}
			and t.FEE_STATUS in ('01','00') and t.PAY_DUE_DATE  >= #{start_date, jdbcType=DATE}
			and t.PAY_DUE_DATE  <= #{end_date, jdbcType=DATE}
		]]>
	</select>
	<!-- 查询保全申请提交日期有无前无实际领取 -->
	<select id="findPayDueFeeAmountByCT" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT SUM(T.SUM_ASSURED_PAID) FEE_AMOUNT
          FROM APP___PAS__DBUSER.T_PAY_DUE T WHERE  t.item_id = #{item_id}
			and t.FEE_STATUS in ('01','00') and t.PAY_DUE_DATE  >= #{start_date, jdbcType=DATE}
			and t.PAY_DUE_DATE  <= #{end_date, jdbcType=DATE}
		]]>
	</select>
	
	<select id="findAllCashBonusByPayDue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT SUM(TPD.FEE_AMOUNT) FEE_AMOUNT,
		         SUM(TPD.REISSUE_INTEREST) REISSUE_INTEREST,
		         TPD.SURVIVAL_MODE,
		         TPD.PAY_SOURCE]]>
		         <if test=" multi_mainrisk_flag != null and multi_mainrisk_flag != ''  ">
		         	<![CDATA[ ,TPP.BUSI_ITEM_ID ]]>
		         </if>
		<![CDATA[
		    FROM DEV_PAS.T_PAY_DUE TPD
		    LEFT JOIN DEV_PAS.T_PAY_PLAN TPP
		      ON TPD.PLAN_ID = TPP.PLAN_ID
		   WHERE TPP.PAY_PLAN_TYPE = 1
		     AND TPD.FEE_STATUS IN ('00', '01')
		]]>   
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPD.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND TPD.PAY_DUE_DATE < #{pay_due_date} ]]></if>
		<![CDATA[
		   GROUP BY TPD.SURVIVAL_MODE, TPD.PAY_SOURCE]]>
		<if test=" multi_mainrisk_flag != null and multi_mainrisk_flag != ''  ">
		   <![CDATA[ ,TPP.BUSI_ITEM_ID ]]>
		</if>
	</select>
	
	<select id="PA_findCashBonus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT DISTINCT TPD.*
        FROM DEV_PAS.T_PAY_DUE TPD
        LEFT JOIN DEV_PAS.T_PAY_PLAN TPP
          ON TPD.PLAN_ID = TPP.PLAN_ID
          LEFT JOIN DEV_PAS.T_PREM_ARAP TPA 
          ON TPD.UNIT_NUMBER = TPA.UNIT_NUMBER
       WHERE TPP.PAY_PLAN_TYPE = 1
        AND TPD.FEE_STATUS = '01'
        AND TPA.FEE_STATUS IN ('01','19','16') 
		]]>   
		<if test=" busi_item_id  != null and busi_item_id != ''  "><![CDATA[ AND TPD.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND to_char(TPD.PAY_DUE_DATE, 'yyyy') = to_char(#{pay_due_date}, 'yyyy') ]]></if>
	</select>
	
	<select id="PA_findAllPayDueByPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.PAY_ID,
		       A.POLICY_ID,
		       A.POLICY_CODE,
		       A.BUSI_ITEM_ID,
		       A.UNIT_NUMBER,
		       A.BUSI_PROD_CODE,
		       A.ITEM_ID,
		       A.PRODUCT_CODE,
		       A.LIAB_ID,
		       A.CANCEL_CAUSE_DESC,
		       A.MANUAL_EXTRA_DATE,
		       A.SURVIVAL_INVEST_FLAG,
		       A.SURVIVAL_INVEST_RESULT,
		       A.PAY_DUE_DATE,
		       A.PAY_NUM,
		       A.FEE_AMOUNT,
		       A.REISSUE_INTEREST,
		       A.PLAN_ID,
		       A.FEE_STATUS,
		       A.SUM_ASSURED_PAID,
		       A.TOTAL_BONUS,
		       A.TERMINAL_BONUS,
		       A.COMPENSATION,
		       A.SURVIVAL_MODE,
		       A.SURVIVAL_TYPE,
		       A.POLICY_CHG_ID,
		       A.PAY_SOURCE,
		       A.INSERT_BY,
		       A.INSERT_TIME,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_BY,
		       A.UPDATE_TIME,
		       A.UPDATE_TIMESTAMP
		  FROM DEV_PAS.T_PAY_DUE A
		  JOIN DEV_PAS.T_PAY_PLAN B
		    ON A.PLAN_ID = B.PLAN_ID
		 WHERE 1 = 1
		   AND A.FEE_STATUS = '00'
		]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE >= #{pay_due_date} ]]></if>
 	    <if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND B.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
 	    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	<!-- 在撤销后是否存在待处理生存金、年金数据 -->
	<select id="findPayDueTotalbyPayDuedesc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_ID, A.POLICY_ID,A.POLICY_CODE, A.BUSI_ITEM_ID,A.UNIT_NUMBER,A.BUSI_PROD_CODE, A.ITEM_ID, A.PRODUCT_CODE,
       	A.LIAB_ID, A.CANCEL_CAUSE_DESC,A.MANUAL_EXTRA_DATE,A.SURVIVAL_INVEST_FLAG,A.SURVIVAL_INVEST_RESULT,A.PAY_DUE_DATE,
       	A.PAY_NUM,A.FEE_AMOUNT, A.REISSUE_INTEREST,A.PLAN_ID,A.FEE_STATUS,A.SUM_ASSURED_PAID,A.TOTAL_BONUS,A.TERMINAL_BONUS,
        A.COMPENSATION,A.SURVIVAL_MODE,A.SURVIVAL_TYPE,A.POLICY_CHG_ID,A.PAY_SOURCE,A.INSERT_BY,A.INSERT_TIME,
        A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP	FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  AND A.FEE_STATUS in ('00','01')
		  AND A.BUSI_ITEM_ID = #{busi_item_id} AND A.PAY_DUE_DATE > #{pay_due_date} AND A.POLICY_CODE = #{policy_code} AND A.PLAN_ID = #{plan_id} 
		 ]]>
	</select>
	
	<select id="PA_findAllPayPlanTypeTen" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TPD.PAY_ID,
		       TPD.POLICY_ID,
		       TPD.POLICY_CODE,
		       TPD.BUSI_ITEM_ID,
		       TPD.UNIT_NUMBER,
		       TPD.BUSI_PROD_CODE,
		       TPD.ITEM_ID,
		       TPD.PRODUCT_CODE,
		       TPD.LIAB_ID,
		       TPD.CANCEL_CAUSE_DESC,
		       TPD.MANUAL_EXTRA_DATE,
		       TPD.SURVIVAL_INVEST_FLAG,
		       TPD.SURVIVAL_INVEST_RESULT,
		       TPD.PAY_DUE_DATE,
		       TPD.PAY_NUM,
		       TPD.FEE_AMOUNT,
		       TPD.REISSUE_INTEREST,
		       TPD.PLAN_ID,
		       TPD.FEE_STATUS,
		       TPD.SUM_ASSURED_PAID,
		       TPD.TOTAL_BONUS,
		       TPD.TERMINAL_BONUS,
		       TPD.COMPENSATION,
		       TPD.SURVIVAL_MODE,
		       TPD.SURVIVAL_TYPE,
		       TPD.POLICY_CHG_ID,
		       TPD.PAY_SOURCE,
		       TPD.INSERT_BY,
		       TPD.INSERT_TIME,
		       TPD.INSERT_TIMESTAMP,
		       TPD.UPDATE_BY,
		       TPD.UPDATE_TIME,
		       TPD.UPDATE_TIMESTAMP FROM DEV_PAS.T_PAY_DUE TPD ,DEV_PAS.T_PAY_PLAN TPP WHERE TPP.PLAN_ID = TPD.PLAN_ID AND TPP.PAY_PLAN_TYPE = '10' 
		]]>
		<if test=" policy_id  != null "><![CDATA[ AND TPP.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	<select id="PA_findAllPayDueByDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.PAY_ID,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.BUSI_ITEM_ID,
       A.UNIT_NUMBER,
       A.BUSI_PROD_CODE,
       A.ITEM_ID,
       A.PRODUCT_CODE,
       A.LIAB_ID,
       A.CANCEL_CAUSE_DESC,
       A.MANUAL_EXTRA_DATE,
       A.SURVIVAL_INVEST_FLAG,
       A.SURVIVAL_INVEST_RESULT,
       A.PAY_DUE_DATE,
       A.PAY_NUM,
       A.FEE_AMOUNT,
       A.REISSUE_INTEREST,
       A.PLAN_ID,
       A.FEE_STATUS,
       A.SUM_ASSURED_PAID,
       A.TOTAL_BONUS,
       A.TERMINAL_BONUS,
       A.COMPENSATION,
       A.SURVIVAL_MODE,
       A.SURVIVAL_TYPE,
       A.POLICY_CHG_ID,
       A.PAY_SOURCE,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP FROM DEV_PAS.T_PAY_DUE A WHERE 1 = 1 
		]]>
		<if test=" pay_num  != null "><![CDATA[ AND A.PAY_NUM = #{pay_num} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE >= #{pay_due_date} ]]></if>
		<if test="flag != null and flag == '1'"><![CDATA[
		 AND NOT EXISTS (
        SELECT 'X'
          FROM dev_pas.t_policy_account            pa,
               dev_pas.t_policy_account_trans_list pat
         where pa.policy_id = a.policy_id
           and pat.account_id = pat.account_id
           AND PAT.TRANS_TIME <= #{pay_due_date}
           AND ROWNUM <= 1
        )
		]]></if>
		<if test="flag != null and flag == '2'"><![CDATA[
		  AND NOT EXISTS (
        SELECT 'X'
          FROM DEV_PAS.T_FUND_TRANS FT
         WHERE FT.POLICY_ID = A.POLICY_ID
           AND FT.DEAL_TIME <= #{pay_due_date}
           AND ROWNUM <= 1
        )
		]]></if>
		<![CDATA[ ORDER BY A.PAY_DUE_DATE ASC ]]>
	</select>
	
	<!-- 查询已经实付费生存金现金发放方式 -->
	<select id="PA_findAllPayDuePremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.PAY_ID,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.BUSI_ITEM_ID,
       A.UNIT_NUMBER,
       A.BUSI_PROD_CODE,
       A.ITEM_ID,
       A.PRODUCT_CODE,
       A.LIAB_ID,
       A.CANCEL_CAUSE_DESC,
       A.MANUAL_EXTRA_DATE,
       A.SURVIVAL_INVEST_FLAG,
       A.SURVIVAL_INVEST_RESULT,
       A.PAY_DUE_DATE,
       A.PAY_NUM,
       A.FEE_AMOUNT,
       A.REISSUE_INTEREST,
       A.PLAN_ID,
       A.FEE_STATUS,
       A.SUM_ASSURED_PAID,
       A.TOTAL_BONUS,
       A.TERMINAL_BONUS,
       A.COMPENSATION,
       A.SURVIVAL_MODE,
       A.SURVIVAL_TYPE,
       A.POLICY_CHG_ID,
       A.PAY_SOURCE,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.PLAN_UNIT_NUMBER FROM DEV_PAS.T_PAY_DUE A  WHERE 1=1
		]]>
	<include refid="payDueWhereCondition" />
	<if test="start_date_due != null and start_date_due != ''"><![CDATA[ AND A.PAY_DUE_DATE > #{start_date_due} ]]></if>
	<![CDATA[
	AND EXISTS (SELECT 'X' FROM DEV_PAS.T_PREM_ARAP AA WHERE AA.UNIT_NUMBER = A.UNIT_NUMBER AND AA.FEE_STATUS IN ('01','19'))
	]]>
	</select>
	
	<select id="PA_findPayDueByPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.PAY_ID,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.BUSI_ITEM_ID,
       A.UNIT_NUMBER,
       A.BUSI_PROD_CODE,
       A.ITEM_ID,
       A.PRODUCT_CODE,
       A.LIAB_ID,
       A.CANCEL_CAUSE_DESC,
       A.MANUAL_EXTRA_DATE,
       A.SURVIVAL_INVEST_FLAG,
       A.SURVIVAL_INVEST_RESULT,
       A.PAY_DUE_DATE,
       A.PAY_NUM,
       A.FEE_AMOUNT,
       A.REISSUE_INTEREST,
       A.PLAN_ID,
       A.FEE_STATUS,
       A.SUM_ASSURED_PAID,
       A.TOTAL_BONUS,
       A.TERMINAL_BONUS,
       A.COMPENSATION,
       A.SURVIVAL_MODE,
       A.SURVIVAL_TYPE,
       A.POLICY_CHG_ID,
       A.PAY_SOURCE,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.PLAN_UNIT_NUMBER FROM DEV_PAS.T_PAY_DUE A,DEV_PAS.T_PAY_PLAN L  WHERE 1=1 
       AND A.PLAN_ID = L.PLAN_ID
       AND A.PAY_DUE_DATE >= L.BEGIN_DATE
		]]>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE >= #{pay_due_date} ]]></if>
	</select>
	
	<select id="PA_findNextPricingDate"	resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT * FROM (
       		SELECT TIUP.Pricing_Date  
       		  FROM Dev_pas.t_contract_invest TCI,
              	   Dev_pas.t_invest_unit_price TIUP,
                   Dev_pas.t_Pay_Due TPD
        	 WHERE TCI.ACCOUNT_CODE = TIUP.Invest_Account_Code
          	   AND TPD.Policy_Id = TCI.Policy_Id
          ]]>
          	   <if test=" plan_id  != null "><![CDATA[ AND TPD.Plan_Id = #{plan_id} ]]></if>
          	   <if test=" pricing_date  != null "><![CDATA[ AND TIUP.Pricing_Date > #{pricing_date} ]]></if>
          <![CDATA[
          	order by TIUP.Pricing_Date) where rownum =1 
          ]]>
	</select>
	
	<select id="PA_findMinPayDueDate"	resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT MIN(T.PAY_DUE_DATE) PAY_DUE_DATE FROM DEV_PAS.T_PAY_DUE T
         ]]>
	<if test=" fee_status  != null "><![CDATA[ AND T.FEE_STATUS = #{fee_status} ]]></if>
	<if test=" policy_code  != null "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>          
	</select>
	
	
	<select id="queryPayDueByNJ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT SUBSTR(TO_CHAR(WM_CONCAT(C.PAY_PLAN_TYPE)),0,1) PAY_PLAN_TYPE,
        SUM(C.FEE_AMOUNT) FEE_AMOUNT
   FROM (SELECT A.PAY_PLAN_TYPE,
                SUM(B.FEE_AMOUNT) FEE_AMOUNT，B.PAY_DUE_DATE
           FROM APP___PAS__DBUSER.T_PAY_PLAN A,
                APP___PAS__DBUSER.T_PAY_DUE  B
          WHERE A.PLAN_ID = B.PLAN_ID
            AND B.FEE_STATUS IN ('00')
            AND B.POLICY_CODE = #{policy_code}	
            AND B.PAY_DUE_DATE = #{pay_due_date}  ]]>
    <if test=" planIdList  != null and planIdList.size()!=0">
  	<![CDATA[AND B.PLAN_ID in ]]>
			<foreach collection ="planIdList" item="planIdList" index="index" open="(" close=")" separator=",">#{planIdList}</foreach>
		</if>
   <![CDATA[ GROUP BY A.PAY_PLAN_TYPE, B.PAY_DUE_DATE) C  ]]>
	</select>
	
	<!-- 查询死亡日期之后是否有领取生存金年金/满期金/现金红利记录 进入账户 -->
	<select id="findPayDueTotalForAI" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		  SELECT COUNT(1)
			  FROM APP___PAS__DBUSER.T_PAY_DUE A, APP___PAS__DBUSER.T_PAY_PLAN B
			 WHERE A.PLAN_ID = B.PLAN_ID
			   AND A.SURVIVAL_MODE = 2 /*进入累积生息账户*/
			   AND A.FEE_STATUS = '01' /*已发放*/
			   AND B.BUSI_ITEM_ID in(${busiItemIds})
			   AND A.PAY_DUE_DATE > #{pay_due_date}
		 ]]>
	</select>
	
	<!-- 查找满期金数据 -->
	<select id="findAllPayDueByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT    A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
		          A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
		          A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
		          A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE,A.REISSUE_INTEREST,A.INSERT_TIME,A.FINISH_TIME 
		FROM APP___PAS__DBUSER.T_PAY_DUE A 
		LEFT JOIN APP___PAS__DBUSER.T_PAY_PLAN B ON A.plan_id = B.plan_id
		WHERE B.pay_plan_type = '4' 
			  AND A.fee_status = '00'
		      AND A.policy_code = #{policy_code}
	</select>
	
	<!-- 查找满期金数据 -->
	<select id="findAllShouldButNotPayDue" resultType="java.util.Map" parameterType="java.util.Map">
			 SELECT (SELECT P.PRODUCT_NAME_SYS
			           FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT P
			          WHERE P.PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) BUSI_ITEM_NAME,  
			        B.BUSI_PROD_CODE,  
			        B.POLICY_CODE,
			        (SELECT TPPT.TYPE_DESC 
			           FROM APP___PAS__DBUSER.T_PAY_PLAN_TYPE TPPT 
			          WHERE TPPT.PAY_PLAN_TYPE = A.PAY_PLAN_TYPE) PAY_PLAN_TYPE,   
			        B.PAY_DUE_DATE,  
			        B.FEE_AMOUNT  
			   FROM APP___PAS__DBUSER.T_PAY_PLAN  A, 
			        APP___PAS__DBUSER.T_PAY_DUE   B
			  WHERE A.PLAN_ID = B.PLAN_ID
			    AND A.POLICY_ID = B.POLICY_ID
			    AND B.FEE_STATUS = '00'
			    <if test="busi_item_id != null">
				   	AND A.BUSI_ITEM_ID = #{busi_item_id}
				</if>
	</select>
	
	<!-- 查询满期金 -->
		<select id="queryFeeAmountType" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
			SELECT sum(a.fee_amount) fee_amount FROM dev_pas.t_pay_due a WHERE exists (SELECT 1 FROM dev_pas.t_pay_plan b where a.plan_id = b.plan_id) 
			]]>
			<include refid="payDueWhereCondition" />
		</select>
		
	<!-- 查询最近一次生调标识为是的数据 -->	
	<select id="findNewSurvivalInvestYFlag" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
			SELECT B.* FROM (SELECT A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
					A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
					A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
					A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  
			   AND A.SURVIVAL_INVEST_FLAG = 1
			   AND A.POLICY_CODE =  #{policy_code} ORDER BY A.PAY_DUE_DATE  DESC ) B
			WHERE 1=1  AND  ROWNUM = 1 
			]]>
	</select>
	
		<!-- 查找应领未领的生存金/年金/养老金数据 -->
	<select id="findAllPayDueByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
		SELECT DISTINCT(TCP.POLICY_ID),A.FEE_AMOUNT,TCP.CHANGE_ID
          FROM DEV_PAS.T_PAY_DUE A
        LEFT JOIN DEV_PAS.T_PAY_PLAN B
          ON A.PLAN_ID = B.PLAN_ID
        LEFT JOIN  DEV_PAS.T_CS_POLICY_CHANGE TCP
          ON A.POLICY_ID = TCP.POLICY_ID
        WHERE B.PAY_PLAN_TYPE IN ('2', '3')
          AND A.FEE_STATUS = '00'
          AND A.FEE_AMOUNT IS NOT NULL
          AND TCP.CHANGE_ID = #{change_id}
          AND ROWNUM <1000
          ]]>
	</select>	
	
	<!-- 查询应领未领累积生息账户信息 -->
	<select id="findAllInterestBearingByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
		SELECT DISTINCT(TCP.POLICY_ID), TPD.FEE_AMOUNT
           FROM DEV_PAS.T_POLICY_ACCOUNT TPA,
                DEV_PAS.T_PAY_DUE        TPD,
                DEV_PAS.T_CS_POLICY_CHANGE TCP
          WHERE TPA.POLICY_ID = TPD.POLICY_ID
            AND TCP.POLICY_ID = TPD.POLICY_ID
            AND TCP.CHANGE_ID = #{change_id}
            AND TPD.FEE_AMOUNT IS NOT NULL
            AND TPD.FEE_STATUS = '00'
            AND TPA.ACCOUNT_TYPE = '11'
            AND ROWNUM <1000
            ]]>
	</select>	
		
	<!-- 查询开始日期之后的年金发放明细 -->
	<select id="findPayDueByPolicyIdAndPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT   A.SURVIVAL_TYPE,A.SURVIVAL_MODE,A.SURVIVAL_INVEST_RESULT, A.CANCEL_CAUSE_DESC, A.UNIT_NUMBER, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.TERMINAL_BONUS, 
			A.SUM_ASSURED_PAID, A.PAY_NUM, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.TOTAL_BONUS, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAY_ID, A.COMPENSATION, A.MANUAL_EXTRA_DATE, A.PLAN_ID, 
			A.FEE_STATUS, A.LIAB_ID, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  AND A.CANCEL_CAUSE_DESC IS NULL]]>
			<include refid="payDueWhereCondition" />
			<if test=" feeStatus_list  != null and feeStatus_list.size()!=0">
			<![CDATA[ AND A.FEE_STATUS in ]]>
				<foreach collection ="feeStatus_list" item="feeStatus_list" index="index" open="(" close=")" separator=",">#{feeStatus_list}</foreach>
			</if>
			<![CDATA[ ORDER BY A.PAY_DUE_DATE ]]>
			<if test=" pay_due_date_1 != null">
				<![CDATA[ DESC ]]>
			</if>
	</select>	
		<!-- 查询应领未领年金或满期金信息 -->
	<select id="findAllAnnuityOrExpirationOfGold" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT (SELECT P.PRODUCT_NAME_SYS
			           FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT P
			          WHERE P.PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) BUSI_ITEM_NAME,  
			        B.BUSI_PROD_CODE,  
			        B.POLICY_CODE,
			        (SELECT TPPT.TYPE_DESC 
			           FROM APP___PAS__DBUSER.T_PAY_PLAN_TYPE TPPT 
			          WHERE TPPT.PAY_PLAN_TYPE = A.PAY_PLAN_TYPE) PAY_PLAN_TYPE,   
			        B.PAY_DUE_DATE,  
			        B.FEE_AMOUNT 
          FROM APP___PAS__DBUSER.T_PAY_PLAN  A, 
			   APP___PAS__DBUSER.T_PAY_DUE   B
         WHERE A.PLAN_ID = B.PLAN_ID
			AND A.PAY_PLAN_TYPE IN ('2','3','5','4','8','10','11')
			AND A.POLICY_ID = B.POLICY_ID
			AND B.FEE_STATUS = '00'
			<if test="busi_item_id != null">
            AND A.BUSI_ITEM_ID = #{busi_item_id}
			</if>
		</select>	
		
		<select id="findLastTimeSurvivalInvest" resultType="java.util.Map" parameterType="java.util.Map">
		   <![CDATA[ Select *
					  From (Select a.pay_due_date,
					               a.pay_source,
					               a.survival_invest_flag,
					               a.survival_invest_result,
					               a.pay_id,
					               a.plan_id,
					               a.busi_item_id,
					               a.unit_number,
					               a.busi_prod_code,
					               a.item_id,
					               a.pay_num,
					               a.fee_amount,
					               a.fee_status,
					               row_number() over(partition by a.plan_id, a.busi_item_id order by a.pay_due_date DESC) RN
					          From APP___PAS__DBUSER.t_Pay_Due a
					         where 1 = 1
					           and ((a.pay_source = '1' and a.survival_invest_flag = '1') or
					               (a.pay_source Is Null Or a.pay_source = '2'))
					           AND A.POLICY_ID = #{policy_id} 
					           ]]>
				    <if test=" planIdList  != null and planIdList.size()!=0">
  					<![CDATA[AND A.PLAN_ID in ]]>
						<foreach collection ="planIdList" item="planIdList" index="index" open="(" close=")" separator=",">#{planIdList}</foreach>
					</if>
					<if test=" feeStatus_list  != null and feeStatus_list.size()!=0">
					<![CDATA[ AND A.FEE_STATUS in ]]>
						<foreach collection ="feeStatus_list" item="feeStatus_list" index="index" open="(" close=")" separator=",">#{feeStatus_list}</foreach>
					</if>
					<![CDATA[ ) T    
					 where T.RN = 1
					   and ROWNUM = 1
		]]>
		</select>	
		
		<select id="findAllSumFeeAmountByPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		   <![CDATA[ Select NVL(sum(a.fee_amount),0) fee_amount
						  From APP___PAS__DBUSER.t_Pay_Due a
						 where 1 = 1 
						   AND A.PAY_DUE_DATE >]]>
					  <if test=" firstPay != null ">
						 <![CDATA[= ]]>
					  </if>	   
					  <![CDATA[ #{pay_due_date} 
						   AND A.POLICY_ID = #{policy_id} 
					   ]]>
					<if test=" planIdList  != null and planIdList.size()!=0">
  					<![CDATA[AND A.PLAN_ID in ]]>
						<foreach collection ="planIdList" item="planIdList" index="index" open="(" close=")" separator=",">#{planIdList}</foreach>
					</if>
					<if test=" feeStatus_list  != null and feeStatus_list.size()!=0">
					<![CDATA[ AND A.FEE_STATUS in ]]>
						<foreach collection ="feeStatus_list" item="feeStatus_list" index="index" open="(" close=")" separator=",">#{feeStatus_list}</foreach>
					</if>
		</select>
		
		<select id="findPayDueInfoSql" resultType="java.util.Map" parameterType="java.util.Map">
			 <![CDATA[ SELECT DISTINCT (CASE
			                   WHEN PAR.Unit_Number is not null AND
			                        PAR.FEE_STATUS <> '01' AND PAR.FEE_STATUS <> '16' THEN
			                    PAR.FINISH_TIME
			                   ELSE
			                    PAR.STATISTICAL_DATE
			                 END) AS CONFIRM_DATE, /*业务核销时间*/
			                 PAR.FINISH_TIME /*收付费到账时间*/
			   FROM DEV_PAS.T_PAY_DUE PD
			   LEFT JOIN DEV_PAS.T_PAY_PLAN PP
			     ON pd.plan_id = pp.plan_id
			  INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
			     ON CP.ITEM_ID = PD.ITEM_ID
			   LEFT JOIN DEV_CAP.T_PREM_ARAP PAR
			     ON PAR.UNIT_NUMBER = PD.UNIT_NUMBER
			   LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
           		ON PP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
			  WHERE 1 = 1
			    AND PP.Liab_Id = 1106
			    AND TBP.Exclusive_Retire_Flag = '1'
			    AND (PAR.Unit_Number is not null OR PD.FEE_STATUS = '00')
			    AND PD.POLICY_CODE = #{policy_code}
			    ]]>
		</select>
		<select id="PA_findPayDueByPCAndBusiID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				  SELECT * FROM  ( SELECT A.POLICY_CODE,
				           A.POLICY_ID,
				           TBP.PRODUCT_NAME_STD AS PRODUCTNAMESTD,
				           TL.LIAB_NAME AS LIABNAME,
				           NVL(A.INSTALMENT_AMOUNT, 0) AS FEE_AMOUNT,
				           A.PAY_DUE_DATE,
				           A.SURVIVAL_INVEST_FLAG,
				           A.PLAN_ID,
				           NULL AS PAY_ID,
				           NULL AS UNIT_NUMBER,
				           '0' AS ISISSUEFLAG,
						   A.BUSI_PROD_CODE,
						   A.BUSI_ITEM_ID
				      FROM APP___PAS__DBUSER.T_PAY_PLAN A
				      JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
				        ON A.POLICY_CODE = B.POLICY_CODE
				      JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
				        ON TBP.BUSINESS_PRD_ID = B.BUSI_PRD_ID
				      JOIN APP___PAS__DBUSER.T_LIABILITY TL
				        ON TL.LIAB_ID = A.LIAB_ID
				     WHERE 1 = 1
				       AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
				       AND A.PAY_STATUS = '2'
				       AND A.PAY_PLAN_TYPE IN ('3', '8', '10', '11')
				       AND A.POLICY_CODE = #{policy_code}
				       	]]>
				     <if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[   AND A.PAY_DUE_DATE <= #{pay_due_date}  AND A.PAY_NUM > 1]]></if> 
				  <![CDATA[
				    UNION
				    
				    SELECT A.POLICY_CODE,
				           A.POLICY_ID,
				           TBP.PRODUCT_NAME_STD AS PRODUCTNAMESTD,
				           TL.LIAB_NAME AS LIABNAME,
				           NVL(P.FEE_AMOUNT, 0) AS FEE_AMOUNT,
				           P.PAY_DUE_DATE,
				           P.SURVIVAL_INVEST_FLAG,
				           P.PLAN_ID,
				           P.PAY_ID,
				           P.UNIT_NUMBER,
				           '1' AS ISISSUEFLAG,
						   A.BUSI_PROD_CODE,
						   A.BUSI_ITEM_ID
				      FROM APP___PAS__DBUSER.T_PAY_PLAN A
				      JOIN APP___PAS__DBUSER.T_PAY_DUE P
				        ON A.PLAN_ID = P.PLAN_ID
				      JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
				        ON A.POLICY_CODE = B.POLICY_CODE
				      JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
				        ON TBP.BUSINESS_PRD_ID = B.BUSI_PRD_ID
				      JOIN APP___PAS__DBUSER.T_LIABILITY TL
				        ON TL.LIAB_ID = A.LIAB_ID
				     WHERE 1 = 1
				       AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
				       AND A.PAY_STATUS != '2'
				       AND A.PAY_PLAN_TYPE IN ('3', '8', '10', '11')
				       AND P.FEE_STATUS = '00'
				       AND A.POLICY_CODE = #{policy_code}
				       ]]>
				     <if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[  AND P.PAY_DUE_DATE <= #{pay_due_date}  AND P.PAY_NUM > 1 ]]></if>
			<![CDATA[ ) G WHERE 1=1 ORDER BY G.PAY_DUE_DATE ASC ]]>
	</select>	
	<!-- 查询应领未领生存金信息 -->
	<select id="PA_findPayDueByPCAndFS" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[
		SELECT DISTINCT PD.POLICY_CODE, 
		                PD.PLAN_ID, 
		                PD.BUSI_PROD_CODE, 
		                (SELECT A.PRODUCT_NAME_SYS
		                   FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A
		                  WHERE A.PRODUCT_CODE_SYS = PD.BUSI_PROD_CODE) PRODUCT_NAME_SYS, 
		                PP.LIAB_CODE, 
		                (SELECT LIAB_NAME
		                   FROM APP___PAS__DBUSER.T_LIABILITY
		                  WHERE LIAB_ID = PD.LIAB_ID) LIAB_NAME, 
		                PP.SURVIVAL_W_MODE, 
		                PD.PAY_DUE_DATE, 
		                PD.SURVIVAL_INVEST_FLAG 
		  FROM APP___PAS__DBUSER.T_PAY_DUE PD
		  LEFT JOIN APP___PAS__DBUSER.T_PAY_PLAN PP
		    ON PP.PLAN_ID = PD.PLAN_ID
		 WHERE 1 = 1
		   AND PP.POLICY_CODE = #{policy_code}
		   AND PD.FEE_STATUS = '00'
	     ]]>
	</select>
	<!-- 查询应领未领生存金信息 -->
	<select id="PA_findPayDueByPCAndAnnuity" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[
	        SELECT SUM(PD.FEE_AMOUNT) FEE_AMOUNT
		      FROM APP___PAS__DBUSER.T_PAY_DUE PD
		     WHERE 1 = 1
		       AND PD.POLICY_CODE = #{policy_code}
		       AND PD.FEE_STATUS = '00'
		       AND PD.PLAN_ID IN
		           (SELECT PP.PLAN_ID
		              FROM APP___PAS__DBUSER.T_PAY_PLAN PP
		             WHERE PP.PAY_PLAN_TYPE IN ('2', '3', '5', '8', '10', '11')
		               AND PP.POLICY_CODE = #{policy_code})
	     ]]>
	</select>

	<select id="PA_findPayDueByBusiIdAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.PAY_ID,
		       A.POLICY_ID,
		       A.POLICY_CODE,
		       A.BUSI_ITEM_ID,
		       A.UNIT_NUMBER,
		       A.BUSI_PROD_CODE,
		       A.ITEM_ID,
		       A.PRODUCT_CODE,
		       A.LIAB_ID,
		       A.CANCEL_CAUSE_DESC,
		       A.MANUAL_EXTRA_DATE,
		       A.SURVIVAL_INVEST_FLAG,
		       A.SURVIVAL_INVEST_RESULT,
		       A.PAY_DUE_DATE,
		       A.PAY_NUM,
		       A.FEE_AMOUNT,
		       A.REISSUE_INTEREST,
		       A.PLAN_ID,
		       A.FEE_STATUS,
		       A.SUM_ASSURED_PAID,
		       A.TOTAL_BONUS,
		       A.TERMINAL_BONUS,
		       A.COMPENSATION,
		       A.SURVIVAL_MODE,
		       A.SURVIVAL_TYPE,
		       A.POLICY_CHG_ID,
		       A.PAY_SOURCE,
		       A.INSERT_BY,
		       A.INSERT_TIME,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_BY,
		       A.UPDATE_TIME,
		       A.UPDATE_TIMESTAMP
		  FROM DEV_PAS.T_PAY_DUE A
		  JOIN DEV_PAS.T_PAY_PLAN B
		    ON A.PLAN_ID = B.PLAN_ID
		 WHERE 1 = 1
		   AND FEE_STATUS = '00'
		   AND A.BUSI_ITEM_ID = #{busi_item_id}
		   AND A.POLICY_CODE = #{policy_code}
		   AND B.PAY_PLAN_TYPE in ('2','3')
		]]>
	</select>
<!-- 生存金年金给付明细信息-->
	<select id="PA_queryPayDueInfoList" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[
	        SELECT L.PAY_DUE_DATE,
       L.FEE_AMOUNT,
       L.FEE_STATUS,
       L.SURVIVAL_INVEST_FLAG,
       L.PLAN_ID,
       L.SURVIVAL_MODE
  FROM DEV_PAS.T_PAY_DUE L
 WHERE 1 = 1
   AND L.FEE_STATUS IN ('00', '01')]]>
   
   <if test=" plan_id  != null "><![CDATA[ AND L.PLAN_ID = #{plan_id} ]]></if>
  <![CDATA[ORDER BY L.PAY_DUE_DATE]]>
	     
	</select>
	
	<!-- 查询年金、生存金的领取形式变更信息 -->
	<select id="PA_findChangeOfClaimFormOfSurvivalGold" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[SELECT DISTINCT PP.SURVIVAL_MODE,
				                 PP.SURVIVAL_W_MODE,
				                 NVL((SELECT B.STD_BANK_CODE FROM DEV_PAS.T_BANK B WHERE B.BANK_CODE = BA.BANK_CODE), BA.BANK_CODE) BANK_CODE,
				                 NVL(BA.ISSUE_BANK_NAME,(SELECT B.BANK_NAME FROM DEV_PAS.T_BANK B WHERE B.BANK_CODE = BA.BANK_CODE)) BANK_NAME,
				                 BA.ACCO_NAME,
				                 BA.BANK_ACCOUNT
				  FROM APP___PAS__DBUSER.T_PAY_PLAN PP
				  LEFT JOIN APP___PAS__DBUSER.T_PAY_PLAN_PAYEE PPP
				    ON PPP.PLAN_ID = PP.PLAN_ID
				  LEFT JOIN APP___PAS__DBUSER.T_BANK_ACCOUNT BA
				    ON PPP.PAYEE_ACCOUNT_ID = BA.ACCOUNT_ID
				 WHERE PP.PAY_PLAN_TYPE IN ('2', '3', '5', '8', '10', '11')
				   AND PP.POLICY_CODE = #{policy_code}
				   AND PP.BUSI_ITEM_ID = #{busi_item_id}]]>
	</select>
	
	<!-- 查询建立关联后已发放年金、生存金险种信息 -->
	<select id="findAllPayDueForRS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT B.POLICY_CODE,
			                B.BUSI_ITEM_ID,
			                B.BUSI_PROD_CODE,
			                C.VALIDATE_DATE
			  FROM DEV_PAS.T_PAY_PLAN           A,
			       DEV_PAS.T_PAY_DUE            B,
			       DEV_PAS.T_CONTRACT_BUSI_PROD C
			 WHERE A.PAY_PLAN_TYPE IN ('2', '3', '5', '8', '10', '11') /* 年金、生存金 */
			   AND B.POLICY_CODE = A.POLICY_CODE
			   AND B.PLAN_ID = A.PLAN_ID
			   AND B.SURVIVAL_MODE = '4'
			   AND B.FEE_STATUS = '01'
			   AND C.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			   AND C.POLICY_CODE = B.POLICY_CODE
			   AND B.POLICY_CODE = #{policy_code}
			   AND B.PAY_DUE_DATE >= #{relation_time}
		]]>
		<if test=" busi_item_id  != null and busi_item_id  != ''  "><![CDATA[ AND B.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 查询给付计划类型为3、8、10，费用状态为00的应领未领生存金/年金是否存在 -->
	<select id="PA_findNoReceiveSurvivalGold" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT SUM(PD.FEE_AMOUNT) FEE_AMOUNT
		      FROM APP___PAS__DBUSER.T_PAY_DUE PD
		     WHERE 1 = 1
		       AND PD.POLICY_CODE = #{policy_code}
		       AND PD.FEE_STATUS = '00'
		       AND PD.PLAN_ID IN
		           (SELECT PP.PLAN_ID
		              FROM APP___PAS__DBUSER.T_PAY_PLAN PP
		             WHERE PP.PAY_PLAN_TYPE IN ('3','8','10')
		               AND PP.POLICY_CODE = #{policy_code})
		]]>
	</select>
	
		<!-- 查询应领未领生存类保险金或现金红利 -->
	<select id="PA_findUnclaimedSurvivalBenefitsOrCashDividends" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT (SELECT P.PRODUCT_NAME_SYS
			           FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT P
			          WHERE P.PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) BUSI_ITEM_NAME,  
			        B.BUSI_PROD_CODE,  
			        B.POLICY_CODE,
			        (SELECT TPPT.TYPE_DESC 
			           FROM APP___PAS__DBUSER.T_PAY_PLAN_TYPE TPPT 
			          WHERE TPPT.PAY_PLAN_TYPE = A.PAY_PLAN_TYPE) PAY_PLAN_TYPE,   
			        B.PAY_DUE_DATE,  
			        B.FEE_AMOUNT 
          FROM APP___PAS__DBUSER.T_PAY_PLAN  A, 
			   APP___PAS__DBUSER.T_PAY_DUE   B
         WHERE A.PLAN_ID = B.PLAN_ID
			AND A.PAY_PLAN_TYPE IN ('1','2','3','5','4','8','10','11')
			AND A.POLICY_ID = B.POLICY_ID
			AND B.FEE_STATUS = '00'
            AND A.POLICY_CODE = #{policy_code}
            AND ROWNUM = 1
	 </select>
	
		<select id="PA_findAllPayDueInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.PAY_ID,
		       A.POLICY_ID,
		       A.POLICY_CODE,
		       A.BUSI_ITEM_ID,
		       A.UNIT_NUMBER,
		       A.BUSI_PROD_CODE,
		       A.ITEM_ID,
		       A.PRODUCT_CODE,
		       A.LIAB_ID,
		       A.CANCEL_CAUSE_DESC,
		       A.MANUAL_EXTRA_DATE,
		       A.SURVIVAL_INVEST_FLAG,
		       A.SURVIVAL_INVEST_RESULT,
		       A.PAY_DUE_DATE,
		       A.PAY_NUM,
		       A.FEE_AMOUNT,
		       A.REISSUE_INTEREST,
		       A.PLAN_ID,
		       A.FEE_STATUS,
		       A.SUM_ASSURED_PAID,
		       A.TOTAL_BONUS,
		       A.TERMINAL_BONUS,
		       A.COMPENSATION,
		       A.SURVIVAL_MODE,
		       A.SURVIVAL_TYPE,
		       A.POLICY_CHG_ID,
		       A.PAY_SOURCE,
		       A.DEDUCTION_FLAG,
		       A.INSERT_BY,
		       A.INSERT_TIME,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_BY,
		       A.UPDATE_TIME,
		       A.UPDATE_TIMESTAMP
		  FROM DEV_PAS.T_PAY_DUE A
		  JOIN DEV_PAS.T_PAY_PLAN B
		    ON A.PLAN_ID = B.PLAN_ID
		 WHERE 1 = 1
		]]>
		<include refid="payDueWhereCondition" />
		<if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND B.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
 	   	<if test=" pay_plan_type_list  != null and pay_plan_type_list.size()!=0">
			<![CDATA[ AND B.PAY_PLAN_TYPE in ]]>
				<foreach collection ="pay_plan_type_list" item="pay_plan_type_list" index="index" open="(" close=")" separator=",">#{pay_plan_type_list}</foreach>
		</if>
		<if test=" feeStatusList  != null and feeStatusList.size()!=0">
		<![CDATA[ AND A.FEE_STATUS in ]]>
			<foreach collection ="feeStatusList" item="feeStatusList" index="index" open="(" close=")" separator=",">#{feeStatusList}</foreach>
		</if>
	</select>

	<!-- 查询建立关联后已发放年金、生存金险种信息 -->
	<select id="PA_queryPayDueNotDeductionInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  	SELECT A.POLICY_ID,
			       A.POLICY_CODE,
			       A.BUSI_ITEM_ID,
			       A.ITEM_ID,
			       A.PRODUCT_CODE,
			       A.LIAB_ID,
			       A.PLAN_ID,
			       A.PAY_DUE_DATE,
			       A.FEE_AMOUNT,
			       A.PAY_ID
			  FROM DEV_PAS.T_PAY_DUE A, DEV_PAS.T_PAY_PLAN B
			 WHERE 1 = 1
			   AND A.PLAN_ID = B.PLAN_ID
			   AND A.POLICY_CODE = #{policy_code}
		]]>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE >= #{pay_due_date} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" payPlanTypeList  != null and payPlanTypeList.size()!=0">
  			<![CDATA[AND B.PAY_PLAN_TYPE IN ]]>
			<foreach collection ="payPlanTypeList" item="pay_Plan_Type_List" index="index" open="(" close=")" separator=",">#{pay_Plan_Type_List}</foreach>
		</if>
		<if test=" not_deduction_flag != null and not_deduction_flag != ''  ">
			<![CDATA[ AND NOT EXISTS
						 (SELECT 'X'
						          FROM DEV_PAS.T_PAY_DUE T
						         WHERE T.PLAN_ID = A.PLAN_ID
						           AND T.PAY_DUE_DATE >= A.PAY_DUE_DATE
						           AND (T.DEDUCTION_FLAG = 1 OR T.FEE_STATUS = '01'))
			]]>
		</if>
		<![CDATA[ ORDER BY A.PAY_DUE_DATE ]]>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="PA_policyBCZYLWLXJHL" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
		  SELECT COUNT(1)
    FROM DEV_PAS.T_PAY_DUE T
   WHERE T.PLAN_ID IN (SELECT T.PLAN_ID
                         FROM DEV_PAS.T_PAY_PLAN T
                        WHERE T.PAY_PLAN_TYPE = '1' ]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
         <![CDATA[                  )
         AND T.FEE_STATUS = '00'
         AND T.SURVIVAL_MODE = '1'
		  ]]>
		
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="PA_policyHLLQFSBWZXLQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT T.PLAN_ID
                         FROM DEV_PAS.T_PAY_PLAN T
                        WHERE T.PAY_PLAN_TYPE = '1' AND ROWNUM = 1]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	<!-- 应领未领现金红利列表 -->	
	<select id="PA_getBonusInfoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT T.PAY_NUM,T.PAY_DUE_DATE,T.FEE_AMOUNT,T.REISSUE_INTEREST
    FROM DEV_PAS.T_PAY_DUE T
   WHERE T.PLAN_ID IN (SELECT T.PLAN_ID
                         FROM DEV_PAS.T_PAY_PLAN T
                        WHERE T.PAY_PLAN_TYPE = '1' ]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" busi_item_id  != null "><![CDATA[ AND T.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
         <![CDATA[                  )
         AND T.FEE_STATUS = '00'
         AND T.SURVIVAL_MODE = '1'
		]]>
	</select>
</mapper>