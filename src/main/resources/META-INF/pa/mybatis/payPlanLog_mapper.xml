<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayPlanLogDao">
<!--
	<sql id="PA_payPlanLogWhereCondition">
		<if test=" survival_invest_result  != null "><![CDATA[ AND A.SURVIVAL_INVEST_RESULT = #{survival_invest_result} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" end_period != null and end_period != ''  "><![CDATA[ AND A.END_PERIOD = #{end_period} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" instalment_amount  != null "><![CDATA[ AND A.INSTALMENT_AMOUNT = #{instalment_amount} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" bene_amount  != null "><![CDATA[ AND A.BENE_AMOUNT = #{bene_amount} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" instalment_proportion  != null "><![CDATA[ AND A.INSTALMENT_PROPORTION = #{instalment_proportion} ]]></if>
		<if test=" end_year  != null "><![CDATA[ AND A.END_YEAR = #{end_year} ]]></if>
		<if test=" pay_num  != null "><![CDATA[ AND A.PAY_NUM = #{pay_num} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" survival_mode  != null "><![CDATA[ AND A.SURVIVAL_MODE = #{survival_mode} ]]></if>
		<if test=" plan_freq != null and plan_freq != ''  "><![CDATA[ AND A.PLAN_FREQ = #{plan_freq} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" begin_date  != null  and  begin_date  != ''  "><![CDATA[ AND A.BEGIN_DATE = #{begin_date} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
		<if test=" survival_w_mode  != null "><![CDATA[ AND A.SURVIVAL_W_MODE = #{survival_w_mode} ]]></if>
		<if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survival_invest_flag  != null "><![CDATA[ AND A.SURVIVAL_INVEST_FLAG = #{survival_invest_flag} ]]></if>
		<if test=" pay_type != null and pay_type != ''  "><![CDATA[ AND A.PAY_TYPE = #{pay_type} ]]></if>
		<if test=" total_amount  != null "><![CDATA[ AND A.TOTAL_AMOUNT = #{total_amount} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" gurnt_pay_period  != null "><![CDATA[ AND A.GURNT_PAY_PERIOD = #{gurnt_pay_period} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" one_time_flag  != null "><![CDATA[ AND A.ONE_TIME_FLAG = #{one_time_flag} ]]></if>
		<if test=" gurnt_pay_liab != null and gurnt_pay_liab != ''  "><![CDATA[ AND A.GURNT_PAY_LIAB = #{gurnt_pay_liab} ]]></if>
		<if test=" survival_date  != null  and  survival_date  != ''  "><![CDATA[ AND A.SURVIVAL_DATE = #{survival_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayPlanLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPayPlanLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_PAY_PLAN_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_PLAN_LOG(
				SURVIVAL_DATE,LIAB_NAME,LIAB_CODE,SURVIVAL_INVEST_RESULT, MONEY_CODE, END_PERIOD, END_DATE, PRODUCT_CODE, INSTALMENT_AMOUNT, ITEM_ID, 
				BENE_AMOUNT, BUSI_PROD_CODE, INSERT_TIMESTAMP, INSTALMENT_PROPORTION, END_YEAR, UPDATE_BY, PAY_NUM, 
				POLICY_CHG_ID, BUSI_ITEM_ID, POLICY_ID, SURVIVAL_MODE, PLAN_FREQ, PLAN_ID, INSERT_TIME, 
				BEGIN_DATE, LIAB_ID, PAY_STATUS, UPDATE_TIME, SURVIVAL_W_MODE, PAY_PLAN_TYPE, PAY_YEAR, 
				LOG_ID, PAY_PERIOD, POLICY_CODE, SURVIVAL_INVEST_FLAG, PAY_TYPE, TOTAL_AMOUNT, PAY_DUE_DATE, 
				GURNT_PAY_PERIOD, UPDATE_TIMESTAMP, LOG_TYPE, INSERT_BY, ONE_TIME_FLAG, GURNT_PAY_LIAB,ANNUITY_POLICY_END_FLAG ) 
			VALUES (
				 #{survival_date, jdbcType=DATE} , #{liab_name, jdbcType=VARCHAR} ,#{liab_code, jdbcType=VARCHAR} ,#{survival_invest_result, jdbcType=NUMERIC}, #{money_code, jdbcType=VARCHAR} , #{end_period, jdbcType=VARCHAR} , #{end_date, jdbcType=DATE} , #{product_code, jdbcType=VARCHAR} , #{instalment_amount, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} 
				, #{bene_amount, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{instalment_proportion, jdbcType=NUMERIC} , #{end_year, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{pay_num, jdbcType=NUMERIC} 
				, #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{survival_mode, jdbcType=NUMERIC} , #{plan_freq, jdbcType=VARCHAR} , #{plan_id, jdbcType=NUMERIC} , SYSDATE 
				, #{begin_date, jdbcType=DATE} , #{liab_id, jdbcType=NUMERIC} , #{pay_status, jdbcType=NUMERIC} , SYSDATE , #{survival_w_mode, jdbcType=NUMERIC} , #{pay_plan_type, jdbcType=VARCHAR} , #{pay_year, jdbcType=NUMERIC} 
				, #{log_id, jdbcType=NUMERIC} , #{pay_period, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{survival_invest_flag, jdbcType=NUMERIC} , #{pay_type, jdbcType=VARCHAR} , #{total_amount, jdbcType=NUMERIC} , #{pay_due_date, jdbcType=DATE} 
				, #{gurnt_pay_period, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{one_time_flag, jdbcType=NUMERIC} , #{gurnt_pay_liab, jdbcType=VARCHAR}, #{annuity_policy_end_flag, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePayPlanLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_PLAN_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePayPlanLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    SURVIVAL_INVEST_RESULT = #{survival_invest_result, jdbcType=NUMERIC} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
			END_PERIOD = #{end_period, jdbcType=VARCHAR} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    INSTALMENT_AMOUNT = #{instalment_amount, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    BENE_AMOUNT = #{bene_amount, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    INSTALMENT_PROPORTION = #{instalment_proportion, jdbcType=NUMERIC} ,
		    END_YEAR = #{end_year, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PAY_NUM = #{pay_num, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SURVIVAL_MODE = #{survival_mode, jdbcType=NUMERIC} ,
			PLAN_FREQ = #{plan_freq, jdbcType=VARCHAR} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    BEGIN_DATE = #{begin_date, jdbcType=DATE} ,
		    LIAB_ID = #{liab_id, jdbcType=NUMERIC} ,
		    PAY_STATUS = #{pay_status, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    SURVIVAL_W_MODE = #{survival_w_mode, jdbcType=NUMERIC} ,
			PAY_PLAN_TYPE = #{pay_plan_type, jdbcType=VARCHAR} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
			PAY_PERIOD = #{pay_period, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVIVAL_INVEST_FLAG = #{survival_invest_flag, jdbcType=NUMERIC} ,
			PAY_TYPE = #{pay_type, jdbcType=VARCHAR} ,
		    TOTAL_AMOUNT = #{total_amount, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    GURNT_PAY_PERIOD = #{gurnt_pay_period, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    ONE_TIME_FLAG = #{one_time_flag, jdbcType=NUMERIC} ,
			GURNT_PAY_LIAB = #{gurnt_pay_liab, jdbcType=VARCHAR} ,
			LIAB_CODE = #{liab_code, jdbcType=VARCHAR} ,
			LIAB_NAME = #{liab_name, jdbcType=VARCHAR} ,
			ANNUITY_POLICY_END_FLAG=#{annuity_policy_end_flag, jdbcType=NUMERIC} ,
			SURVIVAL_DATE = #{survival_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPayPlanLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE, A.LIAB_NAME,A.LIAB_CODE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE, A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, A.ITEM_ID, 
			A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, A.PAY_NUM, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.SURVIVAL_W_MODE, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.LOG_ID, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.PAY_DUE_DATE, 
			A.GURNT_PAY_PERIOD, A.LOG_TYPE, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB FROM,A.ANNUITY_POLICY_END_FLAG APP___PAS__DBUSER.T_PAY_PLAN_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayPlanLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE, A.LIAB_NAME,A.LIAB_CODE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE, A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, A.ITEM_ID, 
			A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, A.PAY_NUM, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.SURVIVAL_W_MODE, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.LOG_ID, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.PAY_DUE_DATE, 
			A.GURNT_PAY_PERIOD, A.LOG_TYPE, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayPlanLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE, A.LIAB_NAME,A.LIAB_CODE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE, A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, A.ITEM_ID, 
			A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, A.PAY_NUM, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.SURVIVAL_W_MODE, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.LOG_ID, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.PAY_DUE_DATE, 
			A.GURNT_PAY_PERIOD, A.LOG_TYPE, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayPlanLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_PLAN_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayPlanLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.SURVIVAL_DATE, B.LIAB_NAME,B.LIAB_CODE,B.SURVIVAL_INVEST_RESULT, B.MONEY_CODE, B.END_PERIOD, B.END_DATE, B.PRODUCT_CODE, B.INSTALMENT_AMOUNT, B.ITEM_ID, 
			B.BENE_AMOUNT, B.BUSI_PROD_CODE, B.INSTALMENT_PROPORTION, B.END_YEAR, B.PAY_NUM, 
			B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID, B.SURVIVAL_MODE, B.PLAN_FREQ, B.PLAN_ID, 
			B.BEGIN_DATE, B.LIAB_ID, B.PAY_STATUS, B.SURVIVAL_W_MODE, B.PAY_PLAN_TYPE, B.PAY_YEAR, 
			B.LOG_ID, B.PAY_PERIOD, B.POLICY_CODE, B.SURVIVAL_INVEST_FLAG, B.PAY_TYPE, B.TOTAL_AMOUNT, B.PAY_DUE_DATE, 
			B.GURNT_PAY_PERIOD, B.LOG_TYPE, B.ONE_TIME_FLAG, B.GURNT_PAY_LIAB FROM (
					SELECT ROWNUM RN, A.SURVIVAL_DATE,  A.LIAB_NAME,A.LIAB_CODE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE, A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, A.ITEM_ID, 
			A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, A.PAY_NUM, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.SURVIVAL_W_MODE, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.LOG_ID, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.PAY_DUE_DATE, 
			A.GURNT_PAY_PERIOD, A.LOG_TYPE, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
