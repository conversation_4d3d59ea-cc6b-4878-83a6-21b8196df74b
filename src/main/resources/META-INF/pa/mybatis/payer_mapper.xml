<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="payer">

	<sql id="PA_payerWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" share_rate  != null "><![CDATA[ AND A.SHARE_RATE = #{share_rate} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayerByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPayer"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PAYER__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAYER(
				ADDRESS_ID, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, RELATION_TO_PH, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, SHARE_RATE ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{relation_to_ph, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{share_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePayer" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAYER WHERE LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePayer" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SHARE_RATE = #{share_rate, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id}  ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPayerByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerByListIdCondition" />
	</select>
	
	<select id="PA_findPayerByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerByAddressIdCondition" />
	</select>
	
	<select id="PA_findPayerByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerByCustomerIdCondition" />
	</select>
	<select id="PA_findPayer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER A WHERE 1 = 1  ]]>
		<include refid="PA_payerWhereCondition" />
	</select>

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payerWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payerWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayerTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAYER A WHERE 1 = 1  ]]>
		<include refid="PA_payerWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayerForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_ID, B.RELATION_TO_PH, B.POLICY_CODE, 
			B.LIST_ID, B.POLICY_ID, B.SHARE_RATE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_payerWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
