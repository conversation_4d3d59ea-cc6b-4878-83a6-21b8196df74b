<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IWXPolicyQueryDao">

	<sql id="WX_queryYDPolicyInfoCondition">
		<choose>
			<when test="query_type == '1'.toString()">
				<![CDATA[ AND (C1.CUSTOMER_ID = #{cons_id, jdbcType=VARCHAR} OR C2.CUSTOMER_ID = #{cons_id, jdbcType=VARCHAR} ) ]]>
			</when>
			<when test="query_type == '2'.toString()">
				<![CDATA[ AND TCM.POLICY_CODE = #{policy_num, jdbcType=VARCHAR} ]]>
			</when>
			<when test="query_type == '3'.toString()">
				<![CDATA[ AND TCM.APPLY_CODE = #{insured_num, jdbcType=VARCHAR} ]]>
			</when>
			<otherwise>
				<![CDATA[ AND ((C1.CUSTOMER_NAME = #{name, jdbcType=VARCHAR} AND C1.CUSTOMER_CERT_TYPE = #{id_type, jdbcType=VARCHAR} AND C1.CUSTOMER_CERTI_CODE = #{id_no, jdbcType=VARCHAR}) 
      				OR (C2.CUSTOMER_NAME = #{name, jdbcType=VARCHAR} AND C2.CUSTOMER_CERT_TYPE = #{id_type, jdbcType=VARCHAR} AND C2.CUSTOMER_CERTI_CODE = #{id_no, jdbcType=VARCHAR}))
      			]]>
			</otherwise>
		</choose>
	</sql>

	<select id="WX_queryYDPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_CODE POLICY_NUM,TCM.ORGAN_CODE MANAGER_ORG,TO_CHAR(TCM.ISSUE_DATE,'YYYY-MM-DD') SIGN_DATE,
				(SELECT TBP.PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT TBP WHERE TBP.BUSINESS_PRD_ID=TCBP.BUSI_PRD_ID) RISK_SHORT_NAME,
				C1.CUSTOMER_NAME APP_NAME,C2.CUSTOMER_NAME INSURED_NAME,TO_CHAR(TCM.VALIDATE_DATE,'YYYY-MM-DD') CALI_TIME,
				(SELECT TCT.TYPE FROM DEV_PAS.T_CERTI_TYPE TCT WHERE TCT.CODE=C1.CUSTOMER_CERT_TYPE) APP_ID_TYPE,
				TCA.AGENT_CODE AGENT_ID,(SELECT TO_CHAR(SUM(TCP.INITIAL_DISCNT_PREM_AF)) FROM DEV_PAS.T_CONTRACT_PRODUCT TCP WHERE TCP.POLICY_CODE=TCM.POLICY_CODE) INITIAL_PREMIUM,
				(CASE WHEN TCP.CHARGE_PERIOD = '1' THEN '一次交清'
				      WHEN TCP.CHARGE_PERIOD = '2' THEN TCP.CHARGE_YEAR||'年'
				      WHEN TCP.CHARGE_PERIOD = '3' THEN '交至'||TCP.CHARGE_YEAR||'岁'
				      WHEN TCP.CHARGE_PERIOD = '4' THEN '终身交费'
				      WHEN TCP.CHARGE_PERIOD = '5' THEN '不定期交'
				      WHEN TCP.CHARGE_PERIOD = '6' THEN TCP.CHARGE_YEAR||'月'
				      WHEN TCP.CHARGE_PERIOD = '7' THEN TCP.CHARGE_YEAR||'天' ELSE '' END) PAY_YEAR,
				(SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BENE TCB WHERE TCB.POLICY_CODE=TCM.POLICY_CODE AND TCB.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID) BENE_COUNT,
				TIL.RELATION_TO_PH,
                TCBP.BUSI_PROD_CODE,
                TO_CHAR(TCP.INITIAL_DISCNT_PREM_AF) SINGLE_PREMIUM               
				FROM DEV_PAS.T_CONTRACT_MASTER TCM
				INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				ON TCM.POLICY_CODE=TCBP.POLICY_CODE AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
				INNER JOIN DEV_PAS.T_POLICY_HOLDER TPH ON TCM.POLICY_CODE=TPH.POLICY_CODE
				INNER JOIN DEV_PAS.T_CUSTOMER C1 ON TPH.CUSTOMER_ID=C1.CUSTOMER_ID
				INNER JOIN DEV_PAS.T_BENEFIT_INSURED TBI ON TBI.POLICY_CODE=TCM.POLICY_CODE AND TBI.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID AND TBI.ORDER_ID=1
				INNER JOIN DEV_PAS.T_INSURED_LIST TIL ON TCM.POLICY_CODE=TIL.POLICY_CODE AND TBI.INSURED_ID=TIL.LIST_ID
				INNER JOIN DEV_PAS.T_CUSTOMER C2 ON TIL.CUSTOMER_ID=C2.CUSTOMER_ID
				INNER JOIN DEV_PAS.T_CONTRACT_AGENT TCA ON TCA.POLICY_CODE=TCM.POLICY_CODE AND TCA.IS_CURRENT_AGENT=1
				INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP ON TCP.POLICY_CODE=TCM.POLICY_CODE AND TCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
				WHERE 1=1 AND TCM.SUBMIT_CHANNEL = 1 
				AND (TCM.LIABILITY_STATE = '1' OR (TCM.LIABILITY_STATE = '3' AND TCM.END_CAUSE IS NOT NULL AND TCM.END_CAUSE !='80')) ]]>
				<include refid="WX_queryYDPolicyInfoCondition"/>
				<![CDATA[ ORDER BY TCM.ISSUE_DATE DESC ]]>
	</select>
	
	<sql id="WX_queryYBTCustomerCondition">
		<choose>
			<when test="query_type == '1'.toString()">
				<![CDATA[ AND C.CUSTOMER_ID = #{cons_id, jdbcType=VARCHAR} ]]>
			</when>
			<when test="query_type == '2'.toString()">
				<![CDATA[ AND C.CUSTOMER_ID = (SELECT TPH.CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER TPH WHERE TPH.POLICY_CODE = #{policy_num, jdbcType=VARCHAR}) ]]>
			</when>
			<when test="query_type == '3'.toString()">
				<![CDATA[ AND C.CUSTOMER_ID = (SELECT TPH.CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER TPH WHERE TPH.APPLY_CODE = #{insured_num, jdbcType=VARCHAR} )]]>
			</when>
			<otherwise>
				<![CDATA[ AND C.CUSTOMER_NAME = #{name, jdbcType=VARCHAR} AND C.CUSTOMER_CERT_TYPE = #{id_type, jdbcType=VARCHAR} AND C.CUSTOMER_CERTI_CODE = #{id_no, jdbcType=VARCHAR} ]]>
			</otherwise>
		</choose>
	</sql>
	
	<select id="WX_queryYBTCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT C.CUSTOMER_ID,C.CUSTOMER_CERT_TYPE,C.CUSTOMER_CERTI_CODE
			FROM DEV_PAS.T_CUSTOMER C WHERE 1=1]]>
		<include refid="WX_queryYBTCustomerCondition"/>
	</select>
	
	<select id="WX_queryYBTPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_CODE POLICY_NUM,TCM.ORGAN_CODE MANAGER_ORG,TO_CHAR(TCM.ISSUE_DATE,'YYYY-MM-DD') SIGN_DATE,
			(SELECT TBP.PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT TBP WHERE TBP.BUSINESS_PRD_ID=TCBP.BUSI_PRD_ID) RISK_SHORT_NAME,
			C1.CUSTOMER_NAME APP_NAME,C2.CUSTOMER_NAME INSURED_NAME,TO_CHAR(TCM.VALIDATE_DATE,'YYYY-MM-DD') CALI_TIME,
			(SELECT TCT.TYPE FROM DEV_PAS.T_CERTI_TYPE TCT WHERE TCT.CODE=C1.CUSTOMER_CERT_TYPE) APP_ID_TYPE,
			TCA.AGENT_CODE AGENT_ID,(SELECT TO_CHAR(SUM(TCP.INITIAL_DISCNT_PREM_AF)) FROM DEV_PAS.T_CONTRACT_PRODUCT TCP WHERE TCP.POLICY_CODE=TCM.POLICY_CODE) INITIAL_PREMIUM,
			(CASE WHEN TCP.CHARGE_PERIOD = '1' THEN '一次交清'
			      WHEN TCP.CHARGE_PERIOD = '2' THEN TCP.CHARGE_YEAR||'年'
			      WHEN TCP.CHARGE_PERIOD = '3' THEN '交至'||TCP.CHARGE_YEAR||'岁'
			      WHEN TCP.CHARGE_PERIOD = '4' THEN '终身交费'
			      WHEN TCP.CHARGE_PERIOD = '5' THEN '不定期交'
			      WHEN TCP.CHARGE_PERIOD = '6' THEN TCP.CHARGE_YEAR||'月'
			      WHEN TCP.CHARGE_PERIOD = '7' THEN TCP.CHARGE_YEAR||'天' ELSE '' END) PAY_YEAR,
			TIL.RELATION_TO_PH,
			(SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BENE TCB WHERE TCB.POLICY_CODE=TCM.POLICY_CODE AND TCB.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID) BENE_COUNT,
            TCBP.BUSI_PROD_CODE,
            TO_CHAR(TCP.INITIAL_DISCNT_PREM_AF) SINGLE_PREMIUM 
			FROM DEV_PAS.T_CS_APPLICATION TCAA
			INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE TCPC ON TCAA.CHANGE_ID=TCPC.CHANGE_ID
			INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC ON TCPC.CHANGE_ID=TCAC.CHANGE_ID AND TCPC.ACCEPT_ID=TCAC.ACCEPT_ID
			INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM ON TCM.POLICY_CODE=TCPC.POLICY_CODE
			INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP ON TCM.POLICY_CODE=TCBP.POLICY_CODE AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
			INNER JOIN DEV_PAS.T_POLICY_HOLDER TPH ON TCM.POLICY_CODE=TPH.POLICY_CODE
			INNER JOIN DEV_PAS.T_CUSTOMER C1 ON TPH.CUSTOMER_ID=C1.CUSTOMER_ID
			INNER JOIN DEV_PAS.T_BENEFIT_INSURED TBI ON TBI.POLICY_CODE=TCM.POLICY_CODE AND TBI.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID AND TBI.ORDER_ID=1
			INNER JOIN DEV_PAS.T_INSURED_LIST TIL ON TCM.POLICY_CODE=TIL.POLICY_CODE AND TBI.INSURED_ID=TIL.LIST_ID
			INNER JOIN DEV_PAS.T_CUSTOMER C2 ON TIL.CUSTOMER_ID=C2.CUSTOMER_ID
			INNER JOIN DEV_PAS.T_CONTRACT_AGENT TCA ON TCA.POLICY_CODE=TCM.POLICY_CODE AND TCA.IS_CURRENT_AGENT=1
			INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP ON TCP.POLICY_CODE=TCM.POLICY_CODE AND TCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
			WHERE 1=1 AND TCPC.SERVICE_CODE IN ('CT','AG') AND TCAC.ACCEPT_STATUS='18' AND TCAA.SERVICE_TYPE = '10' AND TCM.SUBMIT_CHANNEL = 1 ]]>
			<if test=" policy_num != null and policy_num != '' "><![CDATA[ AND TCPC.POLICY_CODE = #{policy_num, jdbcType=VARCHAR} ]]></if>
	 <![CDATA[
			AND (TPH.CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} OR TIL.CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} )
			ORDER BY TCM.ISSUE_DATE DESC
		]]>
	</select>
	
	<!-- 查询银保通、银代保单下的 -->
	<select id="WX_queryDeathLiability" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[  SELECT count(1)
				 FROM DEV_PDS.T_PRODUCT_LIFE L,
				 (SELECT LA.LIAB_ID,LA.LIAB_NAME,R.LIBA_CODE,R.PRODUCT_ID,LA.LIAB_CATEGORY 
				         FROM DEV_PDS.T_LIABILITY LA,DEV_PDS.T_LIAB_PAY_RELATIVE R WHERE LA.LIAB_ID=R.LIAB_ID 
				         AND R.PRODUCT_ID IN
				               (SELECT A.PRODUCT_ID
				                  FROM DEV_PDS.T_PRODUCT_LIFE A
				                 WHERE BUSINESS_PRD_ID =
				                       (SELECT P.BUSINESS_PRD_ID
				                          FROM DEV_PDS.T_BUSINESS_PRODUCT P
				                         WHERE P.PRODUCT_CODE_SYS = #{busi_prod_code, jdbcType=VARCHAR}))) B
				WHERE L.PRODUCT_ID=B.PRODUCT_ID and B.LIAB_CATEGORY='01'   ]]>
	</select>
	<select id="WX_queryBeneTypeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
         SELECT TCB.BENE_TYPE
          FROM DEV_PAS.T_CONTRACT_BENE TCB,
               DEV_PAS.T_CONTRACT_MASTER TCM,
               DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
         WHERE TCB.POLICY_CODE = TCM.POLICY_CODE
           AND TCB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
           AND TCM.POLICY_CODE = TCBP.POLICY_CODE
           AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
		]]>
		<if test=" policy_num != null and policy_num != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_num, jdbcType=VARCHAR} ]]></if>
	</select>
	
	<select id="WX_queryServiceCodeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT TCM.POLICY_CODE, 
               TPC.SERVICE_CODE               
        FROM DEV_PAS.T_CONTRACT_MASTER TCM
        INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
        ON TCM.POLICY_CODE=TCBP.POLICY_CODE AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
        INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP 
        ON TCP.POLICY_CODE=TCM.POLICY_CODE AND TCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
        INNER JOIN DEV_PAS.T_POLICY_CHANGE TPC 
        ON TPC.POLICY_ID = TCM.POLICY_ID
        WHERE 1=1 AND TCM.SUBMIT_CHANNEL = 1 
        AND (TCM.LIABILITY_STATE = '1' OR (TCM.LIABILITY_STATE = '1' AND TCM.END_CAUSE IS NOT NULL AND TCM.END_CAUSE !='80'))    
		]]>
		<if test=" policy_num != null and policy_num != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_num, jdbcType=VARCHAR} ]]></if>
		<![CDATA[ ORDER BY TCM.ISSUE_DATE DESC ]]>
	</select>
</mapper>
