<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.automaticlifting.dao.IAutomaticliftingDao">
	<!-- 身份证升位条件 -->
	<sql id="automaticliftingCondition">
	    <!-- 客户号 -->
		<if test=" customer_id  != null"><![CDATA[ AND TC.CUSTOMER_ID = #{customer_id} ]]></if>
		
		<!-- 保单管理机构 -->
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[ AND TCM.ORGAN_CODE IN (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T 
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		
	</sql>
	
	<!-- 查询满足自动升位条件的条数-->
	<select id="countAutomaticlifting" resultType="java.lang.Integer" parameterType="java.util.Map">		
	<![CDATA[ 
SELECT 
   COUNT(1)
  FROM APP___PAS__DBUSER.T_CUSTOMER TC
 WHERE TC.CUSTOMER_CERT_TYPE = '0'
   AND LENGTH(TC.CUSTOMER_CERTI_CODE) = '15'
   AND TC.CUSTOMER_CERTI_CODE = TC.CUSTOMER_ID_CODE
   AND EXISTS (SELECT TPH.CUSTOMER_ID
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TPH.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
           AND TC.CUSTOMER_CERTI_CODE = TC.CUSTOMER_ID_CODE
           ]]>
           <include refid="automaticliftingCondition" />
        UNION
        
        <![CDATA[SELECT TIL.CUSTOMER_ID
          FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TIL.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
           ]]>
           <include refid="automaticliftingCondition" />
        UNION
        
        <![CDATA[SELECT TCB.CUSTOMER_ID
          FROM APP___PAS__DBUSER.T_CONTRACT_BENE TCB
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TCB.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TC.CUSTOMER_ID = TCB.CUSTOMER_ID]]>
           <include refid="automaticliftingCondition" />
          <![CDATA[ )]]>
           <include refid="automaticliftingCondition" />
	</select>
	
	<!--  查询满足自动升位的客户号-->
	<select id="queryAutomaticlifting" resultType="java.util.Map" parameterType="java.util.Map">		
		<![CDATA[ 
SELECT 
   ROWNUM,TC.CUSTOMER_ID,TC.CUSTOMER_CERTI_CODE,TC.CUSTOMER_CERT_TYPE,TC.CUSTOMER_BIRTHDAY,TC.CUSTOMER_ID_CODE,TC.CUSTOMER_NAME
  FROM APP___PAS__DBUSER.T_CUSTOMER TC
 WHERE TC.CUSTOMER_CERT_TYPE = '0'
   AND LENGTH(TC.CUSTOMER_CERTI_CODE) = '15'
   AND TC.CUSTOMER_CERTI_CODE = TC.CUSTOMER_ID_CODE
   AND EXISTS (SELECT TPH.CUSTOMER_ID
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TPH.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
           AND TC.CUSTOMER_CERTI_CODE = TC.CUSTOMER_ID_CODE
           ]]>
           <include refid="automaticliftingCondition" />
        UNION
        
        <![CDATA[SELECT TIL.CUSTOMER_ID
          FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TIL.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
           ]]>
           <include refid="automaticliftingCondition" />
        UNION
        
        <![CDATA[SELECT TCB.CUSTOMER_ID
          FROM APP___PAS__DBUSER.T_CONTRACT_BENE TCB
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TCB.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TC.CUSTOMER_ID = TCB.CUSTOMER_ID]]>
           <include refid="automaticliftingCondition" />
          <![CDATA[ )]]>
           <include refid="automaticliftingCondition" />	
           <if test=" modnum != null and start != null  "><![CDATA[ AND MOD(TC.CUSTOMER_ID , #{modnum}) = #{start}]]></if>					
	</select>
	<!--查询满足自动升位保单的信息  -->
	<select id="makeAutomaticlifting" resultType="java.util.Map" parameterType="java.util.Map">		
	<![CDATA[ 		      
		SELECT TCM.POLICY_CODE
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TPH.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TPH.CUSTOMER_ID = #{customer_id}
        UNION
        SELECT TCM.POLICY_CODE
          FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TIL.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TIL.CUSTOMER_ID = #{customer_id}
        UNION
        SELECT TCM.POLICY_CODE
          FROM APP___PAS__DBUSER.T_CONTRACT_BENE TCB
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
            ON TCB.POLICY_ID = TCM.POLICY_ID
         WHERE TCM.LIABILITY_STATE <> '3'
           AND TCB.CUSTOMER_ID = #{customer_id}
 		]]> 					
	</select>
<!-- 修改操作 -->
	<update id="PA_updateCustomerIDcard" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER ]]>


		<set>
				<trim suffixOverrides=",">
	customer_id_code = #{customer_id_code, jdbcType=VARCHAR},
	customer_certi_code = #{customer_certi_code, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_ID = #{customer_id} ]]>
	</update>
	
	<!--查询保单是否被保全理赔挂起  -->
	<select id="PA_queryLockPolicyByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">		
	<![CDATA[ 		      
		SELECT A.POLICY_CODE
		  FROM APP___PAS__DBUSER.T_LOCK_POLICY A
		  LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
		    ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
		 WHERE B.SUB_ID IN ('068', '067')
		   AND B.LOCK_SERVICE_TYPE = 1
		   AND A.POLICY_CODE IN
 		]]> 		
 		<foreach collection="policycode_list" item="policycode"
				index="index" open="(" close=")" separator=",">
				#{policycode}
		</foreach>	
		UNION
		<![CDATA[  SELECT TPA.POLICY_CODE
         FROM DEV_PAS.T_PREM_ARAP TPA
        WHERE 1 = 1
          AND TPA.FROZEN_STATUS = '03'
          AND TPA.POLICY_CODE IN 
          ]]>		
        <foreach collection="policycode_list" item="policycode"
				index="index" open="(" close=")" separator=",">
				#{policycode}
		</foreach>	
	</select>
	
</mapper>