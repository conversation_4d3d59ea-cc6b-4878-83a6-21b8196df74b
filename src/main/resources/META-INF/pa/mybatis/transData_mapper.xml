<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.ITransDataDao">
	
	<sql id="queryPolicyChangeByTimeAndServiceCode">
		<if test="service_code != null and service_code != '' "><![CDATA[AND t_policy_change.service_code = #{service_code}]]></if>
		<if test="validate_start != null and validate_start != '' "><![CDATA[AND t_policy_change.finish_time >= #{validate_start}]]></if>
		<if test="validate_end != null and validate_end != '' "><![CDATA[AND t_policy_change.finish_time <= #{validate_end}]]></if>
	</sql>
	<select id="findAllTransData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select APP___PAS__DBUSER.t_contract_master.policy_code,APP___PAS__DBUSER.t_policy_change.service_code,
						APP___PAS__DBUSER.t_policy_change.validate_time,APP___PAS__DBUSER.t_policy_change.related_id,t_policy_change.policy_chg_id,
						APP___PAS__DBUSER.t_policy_change.business_code,APP___PAS__DBUSER.t_policy_change.deriv_type
				 from  APP___PAS__DBUSER.t_policy_change,APP___PAS__DBUSER.t_contract_master
				 where APP___PAS__DBUSER.t_policy_change.policy_id = APP___PAS__DBUSER.t_contract_master.policy_id
				     and APP___PAS__DBUSER.t_contract_master.policy_code = #{policy_code}
				     and APP___PAS__DBUSER.t_policy_change.CHANGE_FLAG != 0 ]]>
		<include refid="queryPolicyChangeByTimeAndServiceCode" />
	</select>
</mapper>


























