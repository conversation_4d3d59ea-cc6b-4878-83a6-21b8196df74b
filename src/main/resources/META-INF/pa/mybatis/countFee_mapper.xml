<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="countFee">
	<sql id="PA_queryCustomerId">
		<if test="customer_id != null"><![CDATA[ and til.customer_id=#{customer_id}]]></if>
	</sql>
	
	<sql  id="PA_queryRiskType">
		<if test="risk_type != null"><![CDATA[ AND TR.RISK_TYPE != #{risk_type}]]></if>
	</sql>
	
	<sql id="PA_queryRiskPolicy">
		 <if test="policyCode_List != null and policyCode_List.size()!=0"><![CDATA[ AND TR.POLICY_CODE NOT IN ]]>
			<foreach collection="policyCode_List" item="policyCode_List"
				index="index" open="(" close=")" separator=",">
				#{policyCode_List}
			</foreach>
		</if>
		<!-- <if test="application_date != null"><![CDATA[ AND TC.VALIDATE_DATE > ADD_MONTHS(#{application_date},-24)]]> -->
		<!-- <![CDATA[
		   AND TR.POLICY_CODE IS NOT NULL
		]]> -->
		<!-- </if> -->
		<!--  -->
	</sql>
	
	<sql id="PA_queryCsAllPrem">
		 <if test="policyCode_List != null and policyCode_List.size()!=0"><![CDATA[ AND TCP.POLICY_CODE NOT IN ]]>
			<foreach collection="policyCode_List" item="policyCode_List"
				index="index" open="(" close=")" separator=",">
				#{policyCode_List}
			</foreach>
		</if>
		<if test="application_date != null"><![CDATA[ AND TC.VALIDATE_DATE > ADD_MONTHS(#{application_date},-24)]]>
		<![CDATA[
		   AND TR.POLICY_CODE IS NOT NULL
		]]></if>
	</sql>
	<sql id="PA_queryTphCustomerId">
	<if test="customer_id != null"><![CDATA[ and tph.customer_id=#{customer_id}]]></if>
	</sql>
	
	<sql id="PA_queryTrmByCalType">
	<if test="i_logrule_code != null"><![CDATA[ and TR.RISK_TYPE=#{i_logrule_code}]]></if>
	</sql>
	
	<sql id="PA_queryBusiItemId">
		<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ where tcbp.busi_prod_code in]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>
	</sql>
	
	<sql id="PA_queryBusiPrdCode">
		<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ AND TCBP.BUSI_PROD_CODE IN]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>
	</sql>
	<sql id="PA_queryTrmCustomerId">
	  <if test="customer_id != null"><![CDATA[ and tr.customer_id=#{customer_id}]]></if>
	</sql>
	
	<sql id="PA_queryCalProductCode">
	<if test="cal_product_code_list != null and cal_product_code_list.size()!=0"><![CDATA[ AND TR.INTERNAL_CODE IN]]>
			<foreach collection="cal_product_code_list" item="cal_product_code_list"
				index="index" open="(" close=")" separator=",">
				#{cal_product_code_list}
			</foreach>
		</if>
	</sql>
	
	<sql id="PA_queryTrmRiskType">
	<if test="trm_risk_type_list != null and trm_risk_type_list.size()!=0"><![CDATA[ and tr.risk_type in]]>
			<foreach collection="trm_risk_type_list" item="trm_risk_type_list"
				index="index" open="(" close=")" separator=",">
				#{trm_risk_type_list}
			</foreach>
		</if>
	</sql>
	<sql id="PA_queryTrmProduct">
	<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ and tr.BUSI_PROD_CODE in]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>
	</sql>
	<!-- 查询终止状态以外的风险累计 -->
	<select id="PA_queryTrmRiskAmount" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
      SELECT SUM(TR.RISK_AMOUNT) AS AMOUNT
        FROM APP___PAS__DBUSER.T_RISK_AMOUNT      TR
       WHERE 1 = 1 AND TR.AMOUNT_STATUS != 3
         AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code}  AND TRC.BIZ_SOURCE='001' )
	]]>
	<include refid="PA_queryTrmCustomerId" />
	<include refid="PA_queryTrmRiskType"/>
	<include refid="PA_queryCalProductCode"/>
	</select>
	<!-- 查询既往保单的固定年限内的风险保额 -->
	<select id="PA_queryTrmApplyCodeIsNotNull" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
	SELECT SUM(TR.RISK_AMOUNT) AS AMOUNT
	  FROM APP___PAS__DBUSER.T_RISK_AMOUNT      TR,
	       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD  TCM
	 WHERE 1 = 1
       AND TCM.BUSI_ITEM_ID = TR.BUSI_ITEM_ID
       AND TCM.POLICY_CODE = TR.POLICY_CODE
       AND TO_CHAR(ADD_MONTHS(TCM.Initial_Validate_Date, #{num_Year})) > #{apply_date}
       AND TR.AMOUNT_STATUS != 3
       AND TCM.POLICY_CODE IS NOT NULL
       AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})
	]]>
	<include refid="PA_queryTrmCustomerId" />
	<include refid="PA_queryTrmRiskType"/>
	</select>
	<!-- 查询既往保单的一至两年的风险保额 -->
	<select id="PA_queryTrmApplyCodeIsNotNullOneToTwo" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
	SELECT SUM(TR.RISK_AMOUNT) AS AMOUNT
	  FROM APP___PAS__DBUSER.T_RISK_AMOUNT      TR,
	       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD  TCBP
	 WHERE 1 = 1
       AND TCBP.BUSI_ITEM_ID = TR.BUSI_ITEM_ID
       AND TCBP.POLICY_CODE = TR.POLICY_CODE
       AND TO_CHAR(ADD_MONTHS(TCBP.VALIDATE_DATE, 12)) <= #{apply_date}
       AND TO_CHAR(ADD_MONTHS(TCBP.VALIDATE_DATE, 24)) > #{apply_date}
       AND TR.AMOUNT_STATUS != 3
       AND TCBP.POLICY_CODE IS NOT NULL
       AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})
	]]>
	<include refid="PA_queryTrmCustomerId" />
	<include refid="PA_queryTrmRiskType"/>
	</select>
	
	<!-- 查询新投保固定年限内的风险保额 -->
	<select id="PA_queryTrmApplyCodeIsNull" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
	SELECT SUM(TR.RISK_AMOUNT) AS AMOUNT
	  FROM APP___PAS__DBUSER.T_RISK_AMOUNT      TR
	 WHERE 1 = 1
       AND TO_CHAR(ADD_MONTHS(TR.START_TIME, #{num_Year})) > #{apply_date}
       AND TR.AMOUNT_STATUS != 3
       AND TR.POLICY_CODE IS NULL
       AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})
	]]>
	<include refid="PA_queryTrmCustomerId" />
	<include refid="PA_queryTrmRiskType"/>
	</select>
	<!-- 查询新投保一至两年内的风险保额 -->
	<select id="PA_queryTrmApplyCodeIsNullOneToTwo" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
     SELECT SUM(TR.RISK_AMOUNT) AS AMOUNT
       FROM APP___PAS__DBUSER.T_RISK_AMOUNT TR
      WHERE 1 = 1
       AND TO_CHAR(ADD_MONTHS(TR.START_TIME, 12)) <= #{apply_date}
       AND TO_CHAR(ADD_MONTHS(TR.START_TIME, 24)) > #{apply_date}
       AND TR.AMOUNT_STATUS != 3
       AND TR.POLICY_CODE IS NULL
       AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})
	]]>
	<include refid="PA_queryTrmCustomerId" />
	<include refid="PA_queryTrmRiskType"/>
	</select>
 	<!-- 根据产品代码查询豁免险风险保额 -->
	<select id="PA_queryTRiskAmountByProduct" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
			SELECT SUM(TR.RISK_AMOUNT) AS AMOUNT
			  FROM APP___PAS__DBUSER.T_RISK_AMOUNT TR
			 WHERE 1=1
			   AND TR.AMOUNT_STATUS != 3
			 AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code}) 
	]]>
	<include refid="PA_queryTrmCustomerId" />
	<include refid="PA_queryTrmRiskType"/>
	</select>
	<!-- 累计两年内保额 -->
	<select id="PA_queryAmountByApplyDate" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[  SELECT SUM(TCPD.AMOUNT) AS calc_amount
			  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCPD,
			       APP___PAS__DBUSER.T_contract_busi_prod tcbp
			 WHERE TCPD.busi_item_id = tcbp.busi_item_id
			   AND TCbp.LIABILITY_STATE = 1 ]]>
			<if test="apply_date != null"><![CDATA[ 
					and tcbp.APPLY_DATE >= add_months(#{apply_date}, -24)
			     	and tcbp.APPLY_DATE <= #{apply_date}]]></if>
			<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ and tcbp.busi_prod_code in]]>
				<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
					index="index" open="(" close=")" separator=",">
					#{busi_prod_code_list}
				</foreach>
			</if>
			<![CDATA[
			   AND EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
			         WHERE TIL.POLICY_CODE = TCPD.POLICY_CODE ]]>
 			<if test="customer_id != null"><![CDATA[ AND TIL.CUSTOMER_ID = #{customer_id}]]></if>
	<![CDATA[  ) ]]>
	</select>
	
	<!-- 累计保额 -->
	<select id="PA_queryByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[SELECT sum(tcpd.amount) as amount
             FROM APP___PAS__DBUSER.T_contract_product tcpd
             where exists
             (SELECT 1
             FROM APP___PAS__DBUSER.T_insured_list til
		     where til.policy_code=tcpd.policy_code ]]>
		<include refid="PA_queryCustomerId" />  
		   <![CDATA[ )]]> 
		   <![CDATA[and  exists
             (SELECT tcbp.busi_item_id
             FROM APP___PAS__DBUSER.T_contract_busi_prod tcbp]]>
		<include refid="PA_queryBusiItemId" />
            <![CDATA[and tcbp.liability_state = 1]]>
            <![CDATA[and  exists
               (SELECT 1
                  FROM APP___PAS__DBUSER.T_insured_list til
                   where tcbp.policy_code = til.policy_code
            ]]>
		<include refid="PA_queryCustomerId" />
		   <![CDATA[) and tcpd.busi_item_id = tcbp.busi_item_id ]]>
           <![CDATA[)]]>
	</select>
	<!-- 累计份数 -->
	<select id="PA_queryUnitByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[SELECT sum(tcpd.unit) as amount
             FROM APP___PAS__DBUSER.T_contract_product tcpd
             where exists
             (SELECT til.policy_code
             FROM APP___PAS__DBUSER.T_insured_list til
		     where tcpd.policy_code = til.policy_code ]]>
		<include refid="PA_queryCustomerId" />  
		   <![CDATA[ )]]> 
		   <![CDATA[and exists
             (SELECT tcbp.busi_item_id
             FROM APP___PAS__DBUSER.T_contract_busi_prod tcbp]]>
		<include refid="PA_queryBusiItemId" />
            <![CDATA[and tcbp.liability_state = 1]]>
            <![CDATA[and exists
               (SELECT til.policy_code
                  FROM APP___PAS__DBUSER.T_insured_list til
                   where tcbp.policy_code = til.policy_code
            ]]>
		<include refid="PA_queryCustomerId" />
		   <![CDATA[) and tcpd.busi_item_id = tcbp.busi_item_id ]]>
           <![CDATA[)]]>
	</select>
	
	<!-- 累计趸交保费-客户为被保人 -->
	<select id="PA_queryStdPremAfByBB" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[SELECT sum(tcpd.std_prem_af) as amount
            FROM APP___PAS__DBUSER.T_contract_product tcpd
             where exists
             (SELECT til.policy_code
             FROM APP___PAS__DBUSER.T_insured_list til
		     where tcpd.policy_code = til.policy_code ]]>
		<include refid="PA_queryCustomerId" />  
		   <![CDATA[ )and tcpd.charge_period = 1]]> 
		   <![CDATA[  and exists
          (SELECT tcbp.busi_item_id
          FROM APP___PAS__DBUSER.T_contract_busi_prod tcbp
             ]]>
		<include refid="PA_queryBusiItemId" />
		<![CDATA[and exists
               (SELECT til.policy_code
                  FROM APP___PAS__DBUSER.T_insured_list til
                  where tcbp.policy_code = til.policy_code ]]>
         <include refid="PA_queryCustomerId"/>  
           <![CDATA[) and tcpd.busi_item_id = tcbp.busi_item_id ]]> 
           <![CDATA[)]]>
	</select>
	<!--累计趸交保费- 客户为投保人 -->
	<select id="PA_queryStdPremAfByTB" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[SELECT sum(tcpd.std_prem_af) as amount
             FROM APP___PAS__DBUSER.T_contract_product tcpd
              where tcpd.policy_code in
              (SELECT tph.policy_code
               FROM APP___PAS__DBUSER.T_policy_holder tph
		       where 1=1]]>
		<include refid="PA_queryTphCustomerId" />  
		   <![CDATA[ )and tcpd.charge_period = 1]]> 
		   <![CDATA[  and tcpd.busi_item_id in
          (SELECT tcbp.busi_item_id
          FROM APP___PAS__DBUSER.T_contract_busi_prod tcbp
             ]]>
		<include refid="PA_queryBusiItemId" />
		<![CDATA[and tcbp.policy_code in
               (SELECT tph.policy_code
                  FROM APP___PAS__DBUSER.T_policy_holder tph
                  where 1=1 ]]>
         <include refid="PA_queryTphCustomerId"/>  
           <![CDATA[)]]> 
           <![CDATA[)]]>
	</select>
	<!-- 累计年交保费 -->
	<select id="PA_queryStdPremAfForYear" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[SELECT sum(tcpd.std_prem_af) as amount
            FROM APP___PAS__DBUSER.T_contract_product tcpd
             where tcpd.policy_code in
             (SELECT til.policy_code
             FROM APP___PAS__DBUSER.T_insured_list til
		     where 1=1]]>
		<include refid="PA_queryCustomerId" />  
		   <![CDATA[ )and tcpd.charge_period = 2]]> 
		   <![CDATA[  and tcpd.busi_item_id in
          (SELECT tcbp.busi_item_id
          FROM APP___PAS__DBUSER.T_contract_busi_prod tcbp
             ]]>
		<include refid="PA_queryBusiItemId" />
		<![CDATA[and tcbp.policy_code in
               (SELECT til.policy_code
                  FROM APP___PAS__DBUSER.T_insured_list til
                  where 1=1 ]]>
         <include refid="PA_queryCustomerId"/>  
           <![CDATA[)]]> 
           <![CDATA[)]]>
	</select>
	<!--  累计保费 -客户为被保人-->
	<select id="PA_queryStdPremAfForLJBYBB" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[SELECT SUM(TWW.STD_PREM_AF * TWW.CHARGE_YEAR) AS AMOUNT
              FROM (SELECT TQQ.*
               FROM (SELECT TCPD1.CHARGE_PERIOD AS QQ, TCPD1.STD_PREM_AF,TCPD1.CHARGE_YEAR
                  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCPD1
		           WHERE 1=1
		           AND TCPD1.POLICY_CODE IN
                       (SELECT TIL.POLICY_CODE
                          FROM APP___PAS__DBUSER.T_INSURED_LIST TIL 
                          WHERE 1=1
		           ]]>
		 <include refid="PA_queryCustomerId" />  
		 <![CDATA[)]]>  
		 <![CDATA[  AND TCPD1.BUSI_ITEM_ID IN
          (SELECT TCBP.BUSI_ITEM_ID
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
             ]]>
		<include refid="PA_queryBusiItemId" />
		<![CDATA[AND TCBP.POLICY_CODE IN
               (SELECT TIL.POLICY_CODE
                  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
                  WHERE 1=1 ]]>
         <include refid="PA_queryCustomerId"/>  
           <![CDATA[)]]> 
           <![CDATA[)) TQQ]]>
           <![CDATA[WHERE TQQ.QQ = #{qq}) TWW]]>
	</select>
	<!--  累计保费 -客户为投保人-->
	<select id="PA_queryStdPremAfForLJBYTB" resultType="java.util.Map"
		parameterType="java.util.Map">
 	<![CDATA[SELECT SUM(TWW.STD_PREM_AF) AS AMOUNT
              FROM (SELECT TQQ.*
               FROM (SELECT TCPD1.CHARGE_PERIOD AS QQ, TCPD1.STD_PREM_AF
                  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCPD1
		           WHERE 1=1
		           AND TCPD1.POLICY_CODE IN
                       (SELECT TPH.POLICY_CODE
                          FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                          WHERE 1=1
		           ]]>
		<include refid="PA_queryTphCustomerId" />
		<![CDATA[)]]>   
		   <![CDATA[  AND TCPD1.BUSI_ITEM_ID IN
          (SELECT TCBP.BUSI_ITEM_ID
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
             ]]>
		<include refid="PA_queryBusiItemId" />
		<![CDATA[AND TCBP.POLICY_CODE IN
               (SELECT TIL.POLICY_CODE
                  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
                  WHERE 1=1 ]]>
         <include refid="PA_queryCustomerId"/>  
           <![CDATA[)]]> 
           <![CDATA[)) TQQ]]>
           <![CDATA[WHERE TQQ.QQ = #{qq}) TWW]]>
	</select>
	
	<select id="PA_queryWaiver" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(T.RISK_AMOUNT) as amount
  FROM APP___PAS__DBUSER.T_RISK_AMOUNT T
 WHERE T.POLICY_CODE IN (SELECT TIL.POLICY_CODE
                           FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
                          WHERE TIL.CUSTOMER_ID = #{customer_id} )
   AND T.RISK_TYPE IN (1, 2)
   AND T.AMOUNT_STATUS = 1
   AND T.CUSTOMER_ID = #{customer_id} ]]>
	</select>
	
	<select id="PA_queryStdPremAf" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select sum(tcp.std_prem_af*tcp.charge_year) as amount
			  from APP___PAS__DBUSER.t_contract_busi_prod tcbp,
			       APP___PAS__DBUSER.t_contract_product   tcp
			 where tcbp.busi_item_id = tcp.busi_item_id
			   and tcp.policy_code in
			       (SELECT til.policy_code
			          FROM APP___PAS__DBUSER.T_insured_list til
			         where til.customer_id = #{insured_customer_id})
			   and tcp.policy_code in
			       (SELECT tph.policy_code
			          FROM APP___PAS__DBUSER.T_policy_holder tph
			         where tph.customer_id = #{holder_customer_id})
			   and tcp.PREM_FREQ = #{prem_freq}
			   and tcbp.busi_prod_code = #{busi_prod_code} 
			   and tcbp.liability_state <> 3 ]]>
	</select>
	<select id="PA_queryNewRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(T.RISK_AMOUNT) RISK_AMOUNT,
       T.RISK_TYPE,
       T.AMOUNT_STATUS,
       T.PRODUCT_ID,
       T.Busi_Prod_Code,
       T.POLICY_CODE,
       T.start_time,
       T.TOTAL_AMOUNT
  FROM (SELECT TC.PRODUCT_ID,
               TR.Busi_Prod_Code,
               TR.RISK_TYPE,
               TR.POLICY_CODE,
               TR.AMOUNT_STATUS,
               SUM(TR.RISK_AMOUNT) AS RISK_AMOUNT,
               TC.VALIDATE_DATE start_time,
               TC.AMOUNT TOTAL_AMOUNT
          FROM APP___PAS__DBUSER.T_RISK_AMOUNT TR
          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
            ON TR.ITEM_ID = TC.ITEM_ID
         WHERE EXISTS (SELECT R.CUSTOMER_ID
                  FROM APP___PAS__DBUSER.T_CUSTOMER R
                 WHERE R.CUSTOMER_ID = #{customer_id}
                   AND R.CUSTOMER_ID = TR.CUSTOMER_ID)
           AND TR.AMOUNT_STATUS != 3]]> 
            
          <include refid="PA_queryRiskType"/>
           <include refid="PA_queryRiskPolicy"/>
           
           <![CDATA[
          GROUP BY RISK_TYPE,
                  TC.LIABILITY_STATE,
                  TR.AMOUNT_STATUS,
                  TC.PRODUCT_ID,
                  TR.Busi_Prod_Code,
                  TR.POLICY_CODE,
                  TR.RISK_TYPE,
                  TC.VALIDATE_DATE,
                  TC.AMOUNT
                  ) T
            
 GROUP BY T.RISK_TYPE,
          T.AMOUNT_STATUS,
          T.PRODUCT_ID,
          T.Busi_Prod_Code,
          T.POLICY_CODE,
          T.start_time,
       T.TOTAL_AMOUNT ]]>
	</select>
	
	<select id="PA_queryAllPremByBB" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[     SELECT TCP.POLICY_CODE,
				       TCBP.BUSI_PROD_CODE,
				       TCP.CHARGE_PERIOD,
				       TCP.CHARGE_YEAR,
				       TCP.PREM_FREQ,
				       TCP.STD_PREM_AF,
				       TCP.BUSI_ITEM_ID,
				       TCP.VALIDATE_DATE,
				       TCP.PRODUCT_ID,
				       TCP.IRREGULAR_PREM,
				       NVL(TCP.APPEND_PREM_AF,0)APPEND_PREM_AF,
				       TCP.TOTAL_PREM_AF
				  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
				       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
				       APP___PAS__DBUSER.T_INSURED_LIST       TIL
				 WHERE TCP.POLICY_CODE = TCBP.POLICY_CODE
				   AND TCBP.POLICY_CODE = TIL.POLICY_CODE
				   AND TIL.POLICY_CODE = TCP.POLICY_CODE
				   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
				   AND TCBP.LIABILITY_STATE <> 3
            AND  TIL.CUSTOMER_ID=#{customer_id}
            ]]>
              
            <include refid="PA_queryBusiPrdCode"/>
             <include refid="PA_queryCsAllPrem"/>
	</select>
	
	<select id="PA_queryAllPremByTB" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[  SELECT DISTINCT TCP.POLICY_CODE,
                         TCP.CHARGE_PERIOD,
                         TCP.CHARGE_YEAR,
                         TCP.PREM_FREQ,
                         TCP.STD_PREM_AF,
                         TCP.BUSI_ITEM_ID,
                         TCP.IRREGULAR_PREM,
                         TCP.VALIDATE_DATE
           FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
                APP___PAS__DBUSER.T_POLICY_HOLDER       TIL
          WHERE TCP.POLICY_CODE = TCBP.POLICY_CODE
            AND TCBP.POLICY_CODE = TIL.POLICY_CODE
            AND TIL.POLICY_CODE = TCP.POLICY_CODE
            AND TCBP.LIABILITY_STATE <> 3
            AND  TIL.CUSTOMER_ID=#{customer_id}
            ]]>
            <include refid="PA_queryBusiPrdCode"/>
            <include refid="PA_queryCsAllPrem"/>
	</select>
	
	<select id="PA_queryAllAmountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[  SELECT DISTINCT SUM(TCP.AMOUNT) AMOUNT
       FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,
            APP___PAS__DBUSER.T_INSURED_LIST TIL
      WHERE TIL.POLICY_CODE = TCP.POLICY_CODE
        AND TIL.CUSTOMER_ID = #{customer_id}
            ]]>
            <include refid="PA_queryCsAllPrem"/>
	</select>
	
	<!-- 查询保全在途的风险保额 -->
    <select id="PA_queryCountFeeByNb" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select nvl(sum((case tr.old_new
             when '1' then
              tr.risk_amount
             when '0' then
              -tr.risk_amount
           end)),0) as risk_amount
      from APP___PAS__DBUSER.t_cs_accept_change cac,
           APP___PAS__DBUSER.t_cs_policy_change cpc,
           APP___PAS__DBUSER.t_cs_risk_amount   tr
     where cac.accept_id = cpc.accept_id
       and cpc.policy_chg_id = tr.policy_chg_id
       and tr.customer_id = #{customer_id}
       and cac.accept_status in ('07', '08', '09', '10', '11', '13')
       AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})
		 ]]>
    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and cpc.policy_code = #{policy_code}  ]]></if>
	<include refid="PA_queryTrmRiskType"/>
	</select>
<!-- /** fanzc_wb 38509 start **/ --> 
	<!-- 一年内风险保额数据(包含保全于新契约在途数据) -->
	<select id="PA_CsRiskAmountOneYear" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
		select sum((case cra.old_new
                     when '1' then
                      cra.risk_amount
                     when '0' then
                      -cra.risk_amount
                   end)) as risk_amount,
               cra.risk_type,
               cra.busi_prod_code,
               cra.policy_code,
               cra.start_time
          from APP___PAS__DBUSER.t_cs_accept_change cac,
               APP___PAS__DBUSER.t_cs_policy_change cpc,
               APP___PAS__DBUSER.t_cs_risk_amount   cra
         where cac.accept_id = cpc.accept_id
           and cpc.policy_chg_id = cra.policy_chg_id
           and cra.customer_id = #{customer_id} 
           and cac.accept_status in ('07', '08', '09', '10', '11', '13')
           and cra.amount_status <> 3
         group by cra.risk_type,
                  cra.busi_prod_code,
                  cra.policy_code,
                  cra.start_time
        union all
         select case when tcp.amount = 0 or TSC.SA = 0 then TRA.RISK_AMOUNT
            else (TRA.RISK_AMOUNT / tcp.amount * TSC.SA) end risk_amount,
                        tra.risk_type,
                        tra.busi_prod_code,
                        tra.policy_code,
                        TSC.START_DATE
          from dev_pas.t_policy_change    tpc,
               dev_pas.t_risk_amount      tra,
               dev_pas.t_sa_change        tsc,dev_pas.t_contract_product tcp
         where tsc.start_date >= add_months(#{apply_time}, -12)
           and tpc.policy_chg_id = tsc.policy_chg_id
           and tsc.policy_id = tra.policy_id
           and tra.item_id = tsc.item_id
           and tsc.item_id = tcp.item_id
           and tra.customer_id =  #{customer_id}  
           and tra.amount_status <> 3
        union all
        select ra.risk_amount,
               ra.risk_type,
               ra.busi_prod_code,
               ra.policy_code,
               ra.start_time
          from dev_pas.t_customer cus, dev_pas.t_risk_amount ra
         where cus.customer_id = ra.customer_id
           and ra.start_time >= add_months(#{apply_time}, -12)
           and cus.customer_id = #{customer_id} 
           and ra.policy_code is null
           and ra.amount_status <> 3
          ]]>
	</select>

	<!-- 保全两年内风险保额数据 -->
	<select id="PA_CsRiskAmountTwoYear" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
          select case when tcp.amount = 0 or TSC.SA = 0 then TRA.RISK_AMOUNT
            else (TRA.RISK_AMOUNT / tcp.amount * TSC.SA) end risk_amount,
                        tra.risk_type,
                        tra.busi_prod_code,
                        tra.policy_code,
                        TSC.START_DATE
          from dev_pas.t_policy_change    tpc,
               dev_pas.t_risk_amount      tra,
               dev_pas.t_sa_change        tsc,dev_pas.t_contract_product tcp
         where tsc.start_date >= add_months(#{apply_time}, -24)
           and tsc.start_date < add_months(#{apply_time}, -12)
           and tpc.policy_chg_id = tsc.policy_chg_id
           and tsc.policy_id = tra.policy_id
           and tra.item_id = tsc.item_id
           and tsc.item_id = tcp.item_id
           and tra.customer_id = #{customer_id} 
           and tra.amount_status <> 3
            ]]>
	</select>
	<!-- /** fanzc_wb 38509 end **/ -->
	
	<!-- 查询一年期以内加保，并且生效的风险累计 -->
	<select id="PA_riskAmountOneYearPA" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
	  select sum((TRA.RISK_AMOUNT / TCP.AMOUNT * TSC.SA)) risk_amount
	    from APP___PAS__DBUSER.t_policy_change      tpc,
	         APP___PAS__DBUSER.t_risk_amount        tra,
	         APP___PAS__DBUSER.t_sa_change          tsc,
	         APP___PAS__DBUSER.t_contract_product   tcp,
	         APP___PAS__DBUSER.t_contract_busi_prod tcbp
	   where tpc.validate_time >= add_months(#{apply_time}, -12)
	     and tpc.policy_chg_id = tsc.policy_chg_id
	     and tsc.policy_id = tcp.policy_id
	     and tsc.item_id = tcp.item_id
	     and tcp.item_id = tra.item_id
	     and tcp.busi_item_id = tcbp.busi_item_id
	     and tra.customer_id = #{customer_id}
	     and tcp.liability_state in ('1', '4')
	     and tpc.service_code = 'PA'
	     and tra.amount_status <> 3
	     and exists
	   (select 1
	            from APP___PAS__DBUSER.t_ilog_rule_config tirc
	           where tirc.cal_busi_prod_code = tcbp.busi_prod_code and tirc.ilog_rule_code=#{ilog_rule_code})
            ]]>
		<if test="trm_risk_type_list != null and trm_risk_type_list.size()!=0"><![CDATA[ and tra.risk_type in]]>
			<foreach collection="trm_risk_type_list" item="trm_risk_type_list"
				index="index" open="(" close=")" separator=",">
				#{trm_risk_type_list}
			</foreach>
		</if>
	</select>
	
	<!-- 查询一年期以外，两年期以内加保，并且生效的风险累计 -->
	<select id="PA_riskAmountTWOYearPA" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
	  select sum((TRA.RISK_AMOUNT / TCP.AMOUNT * TSC.SA)) risk_amount
	    from APP___PAS__DBUSER.t_policy_change      tpc,
	         APP___PAS__DBUSER.t_risk_amount        tra,
	         APP___PAS__DBUSER.t_sa_change          tsc,
	         APP___PAS__DBUSER.t_contract_product   tcp,
	         APP___PAS__DBUSER.t_contract_busi_prod tcbp
	   where tpc.validate_time >= add_months(#{apply_time}, -24)
	     and tpc.validate_time < add_months(#{apply_time}, -12)
	     and tpc.policy_chg_id = tsc.policy_chg_id
	     and tsc.policy_id = tcp.policy_id
	     and tsc.item_id = tcp.item_id
	     and tcp.item_id = tra.item_id
	     and tcp.busi_item_id = tcbp.busi_item_id
	     and tra.customer_id = #{customer_id}
	     and tcp.liability_state in ('1', '4')
	     and tpc.service_code = 'PA'
	     and tra.amount_status <> 3
	     and exists
	   (select 1
	            from APP___PAS__DBUSER.t_ilog_rule_config tirc
	           where tirc.cal_busi_prod_code = tcbp.busi_prod_code and tirc.ilog_rule_code=#{ilog_rule_code})
            ]]>
		<if test="trm_risk_type_list != null and trm_risk_type_list.size()!=0"><![CDATA[ and tra.risk_type in]]>
			<foreach collection="trm_risk_type_list" item="trm_risk_type_list"
				index="index" open="(" close=")" separator=",">
				#{trm_risk_type_list}
			</foreach>
		</if>
	</select>
	
	<!-- 判断是否属于两年期外做的加保 -->
	<select id="PA_isTwoYearForPA" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
			select tra.policy_code, tra.apply_code, tra.policy_id, tra.busi_prod_code
			  from APP___PAS__DBUSER.t_policy_change      tpc,
			       APP___PAS__DBUSER.t_risk_amount        tra,
			       APP___PAS__DBUSER.t_sa_change          tsc,
			       APP___PAS__DBUSER.t_contract_product   tcp,
			       APP___PAS__DBUSER.t_contract_busi_prod tcbp
			 where 1 = 1
			   and tra.start_time > add_months(trunc(tpc.validate_time), -24)
			   and tpc.policy_chg_id = tsc.policy_chg_id
			   and tsc.policy_id = tcp.policy_id
			   and tsc.item_id = tcp.item_id
			   and tcp.item_id = tra.item_id
			   and tcp.busi_item_id = tcbp.busi_item_id
			   and tra.customer_id = #{customer_id}
			   and tcp.liability_state in ('1', '4')
			   and tpc.service_code = 'PA'
			   and tra.amount_status <> 3
			   and rownum = 1
            ]]>
	</select>
	<!-- 需求分析任务 #49723 要求只累计投保人的保全在途的追加保费 -->
	<select id="PA_queryAppendPremAfListTB" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT NVL(SUM(CCP.APPEND_PREM_AF),0) APPEND_PREM_AF
  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE    CAC,
       APP___PAS__DBUSER.T_CS_POLICY_CHANGE    CPC,
       APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CCP,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD  TCBP,
       APP___PAS__DBUSER.T_CONTRACT_MASTER      TCM,      
       APP___PAS__DBUSER.T_POLICY_HOLDER       TPH
 WHERE CAC.ACCEPT_ID = CPC.ACCEPT_ID
   AND CPC.POLICY_CHG_ID = CCP.POLICY_CHG_ID   
   AND CCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
   AND TCBP.POLICY_ID = TCM.POLICY_ID
   AND TCM.POLICY_ID = TPH.POLICY_ID
   AND CAC.ACCEPT_STATUS IN ('07', '08', '09', '10', '11', '13')
   AND TPH.CUSTOMER_ID = #{customer_id}
            ]]>
		<include refid="PA_queryBusiPrdCode" />
	</select>
	<select id="PA_queryProductStdPremAfTB" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT NVL(SUM(TCP.STD_PREM_AF),0) AMOUNT
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
           APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
           APP___PAS__DBUSER.T_CONTRACT_EXTEND    TCE,
           APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP,
           APP___PAS__DBUSER.T_POLICY_HOLDER      TPH
     WHERE TCM.POLICY_ID = TPH.POLICY_ID
       AND TCM.POLICY_ID = TCBP.POLICY_ID
       AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
       AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
       AND TCP.ITEM_ID = TCE.ITEM_ID
       AND TBP.PRODUCT_CATEGORY = '10001'
       AND TBP.PRODUCT_CATEGORY2 = '30004'
       AND TCP.PAIDUP_DATE>TCE.PAY_DUE_DATE
       AND TPH.CUSTOMER_ID = #{holder_customer_id}
       AND EXISTS (SELECT 'X'
              FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TIRC
	           WHERE TIRC.CAL_BUSI_PROD_CODE = TCBP.BUSI_PROD_CODE AND TIRC.ILOG_RULE_CODE=#{ilog_rule_code})
      ]]>
		</select>
		<select id="PA_queryProductStdPremAfBB" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT NVL(SUM(TCP.STD_PREM_AF),0) AMOUNT
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
           APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
           APP___PAS__DBUSER.T_CONTRACT_EXTEND    TCE,
           APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP,
           APP___PAS__DBUSER.T_INSURED_LIST       TIL
     WHERE TCM.POLICY_ID = TIL.POLICY_ID
       AND TCM.POLICY_ID = TCBP.POLICY_ID
       AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
       AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
       AND TCP.ITEM_ID = TCE.ITEM_ID
       AND TBP.PRODUCT_CATEGORY = '10001'
       AND TBP.PRODUCT_CATEGORY2 = '30004'
       AND TCP.PAIDUP_DATE>TCE.PAY_DUE_DATE
       AND TIL.CUSTOMER_ID = #{insured_customer_id}
       AND EXISTS (SELECT 'X'
              FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TIRC
	           WHERE TIRC.CAL_BUSI_PROD_CODE = TCBP.BUSI_PROD_CODE AND TIRC.ILOG_RULE_CODE=#{ilog_rule_code})
      ]]>
		</select>	
		
		<!-- #87427查询需要累计保额的责任组信息-->
		<select id="PA_queryProductAmountInfo" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[SELECT distinct tcpd.*
             FROM APP___PAS__DBUSER.T_contract_product tcpd
             where exists
             (SELECT 1
             FROM APP___PAS__DBUSER.T_insured_list til
		     where til.policy_code=tcpd.policy_code ]]>
		<include refid="PA_queryCustomerId" />  
		   <![CDATA[ )]]> 
		   <![CDATA[and  exists
             (SELECT tcbp.busi_item_id
             FROM APP___PAS__DBUSER.T_contract_busi_prod tcbp where 1=1]]>
		<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ and tcbp.busi_prod_code in]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>
            <![CDATA[and tcbp.liability_state = 1]]>
            <![CDATA[and  exists
               (SELECT 1
                  FROM APP___PAS__DBUSER.T_insured_list til
                   where tcbp.policy_code = til.policy_code
            ]]>
		<include refid="PA_queryCustomerId" />
		   <![CDATA[) and tcpd.busi_item_id = tcbp.busi_item_id 
					  and exists (SELECT 1
			          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
			          WHERE tcbp.BUSI_PROD_CODE = trc.cal_busi_prod_code
			          AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})]]>
           <![CDATA[)]]>
		</select>	
		
		<!-- #87427根据责任组ID查询保额变化信息-->
		<select id="PA_querySaChangeByItemID" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT DISTINCT P.SERVICE_CODE,A.LIST_ID,A.ITEM_ID,A.POLICY_CHG_ID,A.POLICY_ID,A.START_DATE,A.SA 
					FROM APP___PAS__DBUSER.T_SA_CHANGE A
					INNER JOIN APP___PAS__DBUSER.T_POLICY_CHANGE P ON A.POLICY_CHG_ID = P.POLICY_CHG_ID
					WHERE A.ITEM_ID = #{item_id}  ]]>
			<if test=" service_code != null and service_code != ''  "><![CDATA[ AND P.SERVICE_CODE = #{service_code}]]></if>	
			<![CDATA[ ORDER BY A.START_DATE ASC	]]>
		</select>
		
        <!-- #87427查询契约在途风险保额详细信息 -->
		<select id="PA_queryNBApplyCodeRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT RA.*
	           FROM APP___PAS__DBUSER.T_CUSTOMER CUS, APP___PAS__DBUSER.T_RISK_AMOUNT RA
	           WHERE CUS.CUSTOMER_ID = RA.CUSTOMER_ID
	           AND CUS.CUSTOMER_ID = #{customer_id} 
	           AND RA.POLICY_CODE IS NULL
	           AND RA.AMOUNT_STATUS <> 3 ]]>
          <if test="trm_risk_type_list != null and trm_risk_type_list.size()!=0"><![CDATA[ AND RA.RISK_TYPE IN]]>
			<foreach collection="trm_risk_type_list" item="trm_risk_type_list"
				index="index" open="(" close=")" separator=",">
				#{trm_risk_type_list}
			</foreach>
		</if>
          <![CDATA[ AND EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
			          WHERE RA.BUSI_PROD_CODE = TRC.CAL_BUSI_PROD_CODE
			          AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})	]]>
		</select>
		
		<!-- #87427查询已承保的风险保额详细信息-->
		<select id="PA_queryPAPolicyCodeRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
			SELECT TR.*
			  FROM APP___PAS__DBUSER.T_RISK_AMOUNT  TR,
			       APP___PAS__DBUSER.T_CONTRACT_PRODUCT  TCM
			 WHERE 1 = 1
		       AND TCM.BUSI_ITEM_ID = TR.BUSI_ITEM_ID 
		       AND TCM.ITEM_ID = TR.ITEM_ID
		       AND TR.AMOUNT_STATUS != 3
		       AND TR.POLICY_CODE IS NOT NULL
		       AND EXISTS (SELECT 1
		          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
		         WHERE TR.BUSI_PROD_CODE = trc.cal_busi_prod_code
		           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code})
			]]>
			<include refid="PA_queryTrmCustomerId" />
			<include refid="PA_queryTrmRiskType"/>
	</select>

	<!--查询客户下相应保单相应险种相应风险保额类型的风险保额累计数据。-->
	<select id="findAllSumOneYearToTwoYearRiskAmountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				select t.risk_amount,t.risk_type,t.busi_prod_code,t.policy_code,t.start_time from (
				select sum((case cra.old_new
                     when '1' then
                      cra.risk_amount
                     when '0' then
                      -cra.risk_amount
                   end)) as risk_amount,
               cra.risk_type,
               cra.busi_prod_code,
               cra.policy_code,
               cra.start_time
          from APP___PAS__DBUSER.t_cs_accept_change cac,
               APP___PAS__DBUSER.t_cs_policy_change cpc,
               APP___PAS__DBUSER.t_cs_risk_amount   cra
         where cac.accept_id = cpc.accept_id
           and cpc.policy_chg_id = cra.policy_chg_id
           and cra.customer_id = #{customer_id}
           and cpc.policy_code = #{policy_code}
           and cra.busi_prod_code = #{busi_prod_code}
           and cra.risk_type = #{risk_type}
           and cac.accept_status in ('07', '08', '09', '10', '11', '13')
           and cra.amount_status <> 3
         group by cra.risk_type,
                  cra.busi_prod_code,
                  cra.policy_code,
                  cra.start_time
        union all
         select case when tcp.amount = 0 or TSC.SA = 0 then TRA.RISK_AMOUNT
            else (TRA.RISK_AMOUNT / tcp.amount * TSC.SA) end risk_amount,
                        tra.risk_type,
                        tra.busi_prod_code,
                        tra.policy_code,
                        TSC.START_DATE
          from dev_pas.t_policy_change    tpc,
               dev_pas.t_risk_amount      tra,
               dev_pas.t_sa_change        tsc,dev_pas.t_contract_product tcp
         where tpc.policy_chg_id = tsc.policy_chg_id
           and tsc.policy_id = tra.policy_id
           and tra.item_id = tsc.item_id
           and tsc.item_id = tcp.item_id
           and tra.busi_prod_code = #{busi_prod_code}
           and tcp.policy_code = #{policy_code}
           and tra.customer_id =  #{customer_id} 
           and tra.risk_type = #{risk_type}
           and tra.amount_status <> 3
        union all
        select ra.risk_amount,
               ra.risk_type,
               ra.busi_prod_code,
               ra.policy_code,
               ra.start_time
          from dev_pas.t_customer cus, dev_pas.t_risk_amount ra
         where cus.customer_id = ra.customer_id
          
           and cus.customer_id = #{customer_id}
           and ra.busi_prod_code = #{busi_prod_code}
           and ra.risk_type = #{risk_type}
           and ra.policy_code is null
           and ra.amount_status <> 3) t order by t.start_time
		]]>

	</select>
	
	
	<!-- #87427查询保全在途（根据受理状态筛选未生效保全数据）的变更后的（T_CS_RISK_AMOUNT.OLD_NEW=1）风险保额详细信息 -->
    <select id="PA_queryCSPolicyCodeRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT DISTINCT TR.*
		      FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
		           APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
		           APP___PAS__DBUSER.T_CS_RISK_AMOUNT   TR,
		           APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CP
		     WHERE CAC.ACCEPT_ID = CPC.ACCEPT_ID
		       AND CPC.POLICY_CHG_ID = TR.POLICY_CHG_ID
		       AND CPC.POLICY_CHG_ID = CP.POLICY_CHG_ID
		       AND TR.ITEM_ID = CP.ITEM_ID
		       AND TR.OLD_NEW=1 AND TR.OPERATION_TYPE IN ('1','2')
		       AND TR.CUSTOMER_ID = #{customer_id}
		       AND CAC.ACCEPT_STATUS IN ('07', '08', '09', '10', '11', '13') ]]>
		       <if test=" ilog_rule_code != null and ilog_rule_code != ''  ">
			       <![CDATA[ AND EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_ILOG_RULE_CONFIG TRC
			         WHERE TR.BUSI_PROD_CODE = TRC.CAL_BUSI_PROD_CODE
			           AND TRC.ILOG_RULE_CODE = #{ilog_rule_code}) ]]>
		      </if>
			 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CPC.POLICY_CODE = #{policy_code}  ]]></if>
			<include refid="PA_queryTrmRiskType"/>
	</select>
	<!--查询客户下保全在途，保单和契约的责任组风险保额 非本次-->
	<select id="findAllRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select tra.policy_code,
			       tra.item_id,
			       tra.busi_prod_code,
			       tra.risk_amount,
			       (select tccp.amount
			          from dev_pas.t_contract_product tccp
			         where tccp.item_id = tra.item_id) as amount,
			       (select tcpc.policy_chg_id
			          from dev_pas.t_cs_accept_change tcac
			         inner join dev_pas.t_cs_policy_change tcpc
			            on tcac.accept_id = tcpc.accept_id
			         where tra.policy_code = tcpc.policy_code
			           and tcac.accept_status in
			               ('06', '07', '08', '09', '10', '11', '13', '20')) as policy_chg_id
			  from dev_pas.t_risk_amount tra
			 where 1 = 1
			   and exists (select 1
			          from dev_pas.t_ilog_rule_config tirc
			         where tirc.cal_busi_prod_code = tra.busi_prod_code
			           and tirc.ilog_rule_code = #{ilog_rule_code}
			           and tirc.aggregation_risk_type = #{risk_type}
			           and tirc.biz_source = '004')
			   and tra.customer_id = #{customer_id}
			   and tra.risk_type = #{risk_type}
			   and tra.apply_code = #{apply_code}
		]]>

	</select>
	<!--查询客户下保全在途，保单和契约的责任组风险保额 本次-->
	<select id="findAllRiskAmountCurrent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				select tra.item_id,
				       tra.busi_prod_code,
				       tra.risk_amount,
				       (select tccp.amount
				          from dev_pas.t_cs_contract_product tccp
				         where tccp.item_id = tra.item_id
				           and tccp.policy_chg_id = tcpc.policy_chg_id
				           and tccp.old_new = '1') as amount
				  from dev_pas.t_cs_risk_amount tra
				 inner join dev_pas.t_cs_policy_change tcpc
				    on tra.policy_chg_id = tcpc.policy_chg_id
				 inner join dev_pas.t_cs_contract_busi_prod tccb
				    on tra.policy_chg_id = tccb.policy_chg_id
				   and tra.busi_item_id = tccb.busi_item_id
				 where tra.old_new = '1'
				   and tccb.old_new = '1'
				   and exists((select 1
				                from dev_pas.t_cs_accept_change tcac
				               where tcac.accept_id = tcpc.accept_id
				                 and tcac.accept_status in
				                     ('06', '07', '08', '09', '10', '11', '13','20'))) --保全在途
				   and exists (select 1
				          from dev_pas.t_ilog_rule_config tirc
				         where tirc.cal_busi_prod_code = tccb.busi_prod_code
				           and tirc.ilog_rule_code = #{ilog_rule_code}
				           and tirc.aggregation_risk_type = #{risk_type}
				           and tirc.biz_source = '004')
				   and tra.customer_id = #{customer_id}
				   and tra.risk_type = #{risk_type}
				   and tra.change_id = #{change_id}
				   and tra.apply_code = #{apply_code}
		]]>

	</select>
	<!--查询客户下保全在途，保单和契约风险保额轨迹。-->
	<select id="findAllRiskAmountSa" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select tra.item_id,
			       (tccp.amount - nvl((select a.amount
			                            from dev_pas.t_contract_product a
			                           where a.item_id = tccp.item_id),
			                          0)) sa,
			       (select tcpc.apply_time
			          from dev_pas.t_cs_policy_change tcpc
			         where tcpc.policy_chg_id = tccp.policy_chg_id) as start_time,
			         1 as cs
			  from dev_pas.t_cs_risk_amount tra
			 inner join dev_pas.t_cs_contract_busi_prod tcbp
			    on tra.busi_item_id = tcbp.busi_item_id
			 inner join dev_pas.t_cs_contract_product tccp
			    on tra.item_id = tccp.item_id
			 where 1 = 1
			   and tra.old_new = '1'
			   and tcbp.old_new = '1'
			   and tccp.old_new = '1'
			   and exists((select 1
			                from dev_pas.t_cs_accept_change tcac
			               inner join dev_pas.t_cs_policy_change tcpc
			                  on tcac.accept_id = tcpc.accept_id
			               where tccp.policy_chg_id = tcpc.policy_chg_id
			                 and tcac.accept_status in
			                     ('06', '07', '08', '09', '10', '11', '13','20'))) --保全在途
			   and exists (select 1
			          from dev_pas.t_ilog_rule_config tirc
			         where tirc.cal_busi_prod_code = tra.busi_prod_code
			           and tirc.ilog_rule_code = #{ilog_rule_code}
			           and tirc.aggregation_risk_type = #{risk_type}
			           and tirc.biz_source = '004')
			   and tra.customer_id = #{customer_id}
			   and tra.risk_type = #{risk_type}
			   and tra.change_id <> #{change_id}
			   and tra.apply_code = #{apply_code}
			   and tra.item_id = #{item_id}
			union all
			select tsc.item_id, tsc.sa, tsc.start_date as start_time,0 as cs
			  from dev_pas.t_sa_change tsc
			 inner join dev_pas.t_risk_amount tra
			    on tsc.item_id = tra.item_id
			 where 1 = 1
			   and not exists((select 1
			                    from dev_pas.t_cs_accept_change tcac
			                   inner join dev_pas.t_cs_policy_change tcpc
			                      on tcac.accept_id = tcpc.accept_id
			                   where tsc.policy_id = tcpc.policy_id
			                     and tcac.accept_status in
			                         ('06', '07', '08', '09', '10', '11', '13','20'))) --保单 契约
			   and exists (select 1
			          from dev_pas.t_ilog_rule_config tirc
			         where tirc.cal_busi_prod_code = tra.busi_prod_code
			           and tirc.ilog_rule_code = #{ilog_rule_code}
			           and tirc.aggregation_risk_type = #{risk_type}
			           and tirc.biz_source = '004')
			   and tra.customer_id = #{customer_id}
			   and tra.risk_type = #{risk_type}
			   and tra.apply_code = #{apply_code}
			   and tsc.item_id = #{item_id}
			 order by 3
		]]>

	</select>
	<!--查询客户下保全在途，保单和契约风险保额保单。-->
	<select id="findAllPolicyCodeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select tra.apply_code
			  from app___pas__dbuser.t_risk_amount tra
			 where tra.customer_id = #{customer_id}
			   and tra.amount_status != '3' --契约只用 0 和 3 两个状态 0 表示正常状态，3 表示撤保
			 group by tra.apply_code
			union
			select tra.apply_code
			  from app___pas__dbuser.t_cs_risk_amount tra
			 inner join app___pas__dbuser.t_cs_policy_change tcpc
			    on tra.policy_chg_id = tcpc.policy_chg_id
			 where tra.old_new = '1'
			   and exists((select 1
			                from dev_pas.t_cs_accept_change tcac
			               where tcac.accept_id = tcpc.accept_id
			                 and tcac.accept_status in
			                     ('06', '07', '08', '09', '10', '11', '13','20'))) --保全在途
			   and tra.amount_status != '3'
			   and tra.customer_id = #{customer_id}
			 group by tra.apply_code
		]]>
	</select>
	
</mapper>
