<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFundAssetsDetailDao">

	<sql id="fundAssetsDetailWhereCondition">
		<if test=" units  != null "><![CDATA[ AND A.UNITS = #{units} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" account_value  != null "><![CDATA[ AND A.ACCOUNT_VALUE = #{account_value} ]]></if>
		<if test=" upload_date  != null  and  upload_date  != ''  "><![CDATA[ AND A.UPLOAD_DATE = #{upload_date} ]]></if>
		<if test=" assets_deposit_id  != null "><![CDATA[ AND A.ASSETS_DEPOSIT_ID = #{assets_deposit_id} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" evaluation_date  != null  and  evaluation_date  != ''  "><![CDATA[ AND A.EVALUATION_DATE = #{evaluation_date} ]]></if>
		<if test=" profit_loss_amout  != null "><![CDATA[ AND A.PROFIT_LOSS_AMOUT = #{profit_loss_amout} ]]></if>
		<if test=" invest_account_id  != null "><![CDATA[ AND A.INVEST_ACCOUNT_ID = #{invest_account_id} ]]></if>
		<if test=" fund_assets_id  != null "><![CDATA[ AND A.FUND_ASSETS_ID = #{fund_assets_id} ]]></if>
		<if test="invest_account_id_list != null and invest_account_id_list.size() > 0">
			<![CDATA[ AND A.INVEST_ACCOUNT_ID IN ]]>
			<foreach collection="invest_account_id_list" item="invest_account_id_list"
			index="index" open="(" close=")" separator=",">#{invest_account_id_list}</foreach>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryFundAssetsDetailByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addFundAssetsDetail"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.s_contract_call.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL(
				UNITS, INVEST_ACCOUNT_CODE, INSERT_TIME, BATCH_NO, UPDATE_TIME, INSERT_TIMESTAMP, ACCOUNT_VALUE, 
				UPLOAD_DATE, UPDATE_BY, ASSETS_DEPOSIT_ID, BANK_CODE, LIST_ID, EVALUATION_DATE, UPDATE_TIMESTAMP, 
				PROFIT_LOSS_AMOUT, INSERT_BY, INVEST_ACCOUNT_ID, FUND_ASSETS_ID ) 
			VALUES (
				#{units, jdbcType=NUMERIC}, #{invest_account_code, jdbcType=VARCHAR} , SYSDATE , #{batch_no, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP, #{account_value, jdbcType=NUMERIC} 
				, #{upload_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{assets_deposit_id, jdbcType=NUMERIC} , #{bank_code, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{evaluation_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{profit_loss_amout, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} , #{fund_assets_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteFundAssetsDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateFundAssetsDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL ]]>
		<set>
		<trim suffixOverrides=",">
		    UNITS = #{units, jdbcType=NUMERIC} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
			BATCH_NO = #{batch_no, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ACCOUNT_VALUE = #{account_value, jdbcType=NUMERIC} ,
		    UPLOAD_DATE = #{upload_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    ASSETS_DEPOSIT_ID = #{assets_deposit_id, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    EVALUATION_DATE = #{evaluation_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    PROFIT_LOSS_AMOUT = #{profit_loss_amout, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
		    FUND_ASSETS_ID = #{fund_assets_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findFundAssetsDetailByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNITS, A.INVEST_ACCOUNT_CODE, A.BATCH_NO, A.ACCOUNT_VALUE, 
			A.UPLOAD_DATE, A.ASSETS_DEPOSIT_ID, A.BANK_CODE, A.LIST_ID, A.EVALUATION_DATE, 
			A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.FUND_ASSETS_ID FROM APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL A WHERE 1 = 1  ]]>
		<include refid="queryFundAssetsDetailByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapFundAssetsDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNITS, A.INVEST_ACCOUNT_CODE, A.BATCH_NO, A.ACCOUNT_VALUE, 
			A.UPLOAD_DATE, A.ASSETS_DEPOSIT_ID, A.BANK_CODE, A.LIST_ID, A.EVALUATION_DATE, 
			A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.FUND_ASSETS_ID FROM APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllFundAssetsDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNITS, A.INVEST_ACCOUNT_CODE, A.BATCH_NO, A.ACCOUNT_VALUE, 
			A.UPLOAD_DATE, A.ASSETS_DEPOSIT_ID, A.BANK_CODE, A.LIST_ID, A.EVALUATION_DATE, 
			A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.FUND_ASSETS_ID FROM APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findFundAssetsDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryFundAssetsDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UNITS, B.INVEST_ACCOUNT_CODE, B.BATCH_NO, B.ACCOUNT_VALUE, 
			B.UPLOAD_DATE, B.ASSETS_DEPOSIT_ID, B.BANK_CODE, B.LIST_ID, B.EVALUATION_DATE, 
			B.PROFIT_LOSS_AMOUT, B.INVEST_ACCOUNT_ID, B.FUND_ASSETS_ID FROM (
					SELECT ROWNUM RN, A.UNITS, A.INVEST_ACCOUNT_CODE, A.BATCH_NO, A.ACCOUNT_VALUE, 
			A.UPLOAD_DATE, A.ASSETS_DEPOSIT_ID, A.BANK_CODE, A.LIST_ID, A.EVALUATION_DATE, 
			A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.FUND_ASSETS_ID FROM APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 	按照投资账户汇总的投资账户资产价值明细 -->
<select id="queryFundAssetsDetailTotal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECTsum( A.ACCOUNT_VALUE)as ACCOUNT_VALUE , 
			sum(A.PROFIT_LOSS_AMOUT)as PROFIT_LOSS_AMOUT,max(a.BATCH_NO) as BATCH_NO,max(a.EVALUATION_DATE) as EVALUATION_DATE
			, A.INVEST_ACCOUNT_ID,a.invest_account_code,a.UPLOAD_DATE
			, A.ASSETS_DEPOSIT_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_FUND_ASSETS_DETAIL A WHERE 1 = 1 
      ]]>
		<include refid="fundAssetsDetailWhereCondition" />
		<![CDATA[  GROUP BY a.INVEST_ACCOUNT_ID,a.invest_account_code,a.UPLOAD_DATE, A.ASSETS_DEPOSIT_ID, A.BANK_CODE]]>
	</select>
</mapper>
