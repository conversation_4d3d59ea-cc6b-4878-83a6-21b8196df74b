<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ILiabilityDao">

	<sql id="PA_liabilityWhereCondition">
		<if test=" liab_category  != null "><![CDATA[ AND A.LIAB_CATEGORY = #{liab_category} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" liab_name != null and liab_name != ''  "><![CDATA[ AND A.LIAB_NAME = #{liab_name} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryLiabilityByLiabIdCondition">
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id,jdbcType=NUMERIC} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addLiability"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_LIABILITY(
				LIAB_CATEGORY, LIAB_ID, LIAB_NAME ) 
			VALUES (
				#{liab_category, jdbcType=NUMERIC}, #{liab_id, jdbcType=NUMERIC} , #{liab_name, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteLiability" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_LIABILITY WHERE LIAB_ID = #{liab_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateLiability" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LIABILITY ]]>
		<set>
		<trim suffixOverrides=",">
		    LIAB_CATEGORY = #{liab_category, jdbcType=NUMERIC} ,
			LIAB_NAME = #{liab_name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIAB_ID = #{liab_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findLiabilityByLiabId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CATEGORY, A.LIAB_ID, A.LIAB_NAME FROM APP___PAS__DBUSER.T_LIABILITY A WHERE 1 = 1  ]]>
		<include refid="PA_queryLiabilityByLiabIdCondition" />
		<![CDATA[ ORDER BY A.LIAB_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapLiability" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CATEGORY, A.LIAB_ID, A.LIAB_NAME FROM APP___PAS__DBUSER.T_LIABILITY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIAB_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllLiability" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CATEGORY, A.LIAB_ID, A.LIAB_NAME FROM APP___PAS__DBUSER.T_LIABILITY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIAB_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findLiabilityTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_LIABILITY A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryLiabilityForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LIAB_CATEGORY, B.LIAB_ID, B.LIAB_NAME FROM (
					SELECT ROWNUM RN, A.LIAB_CATEGORY, A.LIAB_ID, A.LIAB_NAME FROM APP___PAS__DBUSER.T_LIABILITY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIAB_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
