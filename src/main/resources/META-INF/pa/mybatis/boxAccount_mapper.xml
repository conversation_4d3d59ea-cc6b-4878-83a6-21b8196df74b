<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IBoxAccountDao">

<sql id="boxAccountWhereCondition">
		<if test=" box_unit  != null "><![CDATA[ AND A.BOX_UNIT = #{box_unit} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" fund_name != null and fund_name != ''  "><![CDATA[ AND A.FUND_NAME = #{fund_name} ]]></if>
		<if test=" off_price  != null "><![CDATA[ AND A.OFF_PRICE = #{off_price} ]]></if>
		<if test=" make_date  != null  and  make_date  != ''  "><![CDATA[ AND A.MAKE_DATE = #{make_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" bid_price  != null "><![CDATA[ AND A.BID_PRICE = #{bid_price} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryBoxAccountByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryBoxAccountByFundCodeCondition">
		<if test=" fund_code != null and fund_code != '' "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addBoxAccount"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_BOX_ACCOUNT__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BOX_ACCOUNT(
				BOX_UNIT, INSERT_TIME, FUND_CODE, UPDATE_TIME, STATUS, INSERT_TIMESTAMP, FUND_NAME, 
				UPDATE_BY, OFF_PRICE, MAKE_DATE, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, BID_PRICE ) 
			VALUES (
				#{box_unit, jdbcType=NUMERIC}, SYSDATE , #{fund_code, jdbcType=VARCHAR} , SYSDATE , #{status, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{fund_name, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{off_price, jdbcType=NUMERIC} , #{make_date, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{bid_price, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteBoxAccount" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BOX_ACCOUNT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateBoxAccount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BOX_ACCOUNT ]]>
		<set>
		<trim suffixOverrides=",">
		    BOX_UNIT = #{box_unit, jdbcType=NUMERIC} ,
			FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    STATUS = #{status, jdbcType=NUMERIC} ,
			FUND_NAME = #{fund_name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    OFF_PRICE = #{off_price, jdbcType=NUMERIC} ,
		    MAKE_DATE = #{make_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BID_PRICE = #{bid_price, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findBoxAccountByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BOX_UNIT, A.FUND_CODE, A.STATUS, A.FUND_NAME, 
			A.OFF_PRICE, A.MAKE_DATE, A.LIST_ID, A.BID_PRICE FROM APP___PAS__DBUSER.T_BOX_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="queryBoxAccountByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findBoxAccountByFundCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BOX_UNIT, A.FUND_CODE, A.STATUS, A.FUND_NAME, 
			A.OFF_PRICE, A.MAKE_DATE, A.LIST_ID, A.BID_PRICE FROM APP___PAS__DBUSER.T_BOX_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="queryBoxAccountByFundCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapBoxAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BOX_UNIT, A.FUND_CODE, A.STATUS, A.FUND_NAME, 
			A.OFF_PRICE, A.MAKE_DATE, A.LIST_ID, A.BID_PRICE FROM APP___PAS__DBUSER.T_BOX_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="boxAccountWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllBoxAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BOX_UNIT, A.FUND_CODE, A.STATUS, A.FUND_NAME, 
			A.OFF_PRICE, A.MAKE_DATE, A.LIST_ID, A.BID_PRICE FROM APP___PAS__DBUSER.T_BOX_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="boxAccountWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findBoxAccountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BOX_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="boxAccountWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryBoxAccountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BOX_UNIT, B.FUND_CODE, B.STATUS, B.FUND_NAME, 
			B.OFF_PRICE, B.MAKE_DATE, B.LIST_ID, B.BID_PRICE FROM (
					SELECT ROWNUM RN, A.BOX_UNIT, A.FUND_CODE, A.STATUS, A.FUND_NAME, 
			A.OFF_PRICE, A.MAKE_DATE, A.LIST_ID, A.BID_PRICE FROM APP___PAS__DBUSER.T_BOX_ACCOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="boxAccountWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

     <!-- 根据基金代码查询名称 -->
     <select id="findCodeName" resultType="java.util.Map" parameterType="java.util.Map">
         SELECT TF.INVEST_ACCOUNT_NAME as FUND_NAME FROM APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO TF WHERE 1=1
         <if test="fund_code!=null">
             AND TF.INVEST_ACCOUNT_CODE = #{fund_code, jdbcType=VARCHAR}
         </if>
     </select>
     
      <!-- 内部限额确认页面查询投资账户的监管限额信息-->
     <select id="findBOXSuperviseLimitByFundCode" resultType="java.util.Map" parameterType="java.util.Map">
     	<![CDATA[	SELECT T.MAX_RATE, T.END_DATE, T.FUND_CODE, T.START_TIME, T.MIN_AMOUNT, 
			        T.STATUS, T.MIN_RATE, T.START_DATE, T.MAX_AMOUNT, T.LIST_ID, 
			        T.END_TIME FROM (
             SELECT B.MAX_RATE, B.END_DATE, B.FUND_CODE, B.START_TIME, B.MIN_AMOUNT, 
			        B.STATUS, B.MIN_RATE, B.START_DATE, B.MAX_AMOUNT, B.LIST_ID, 
			        B.END_TIME FROM  APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT B 
             WHERE B.FUND_CODE = #{fund_code, jdbcType=VARCHAR} ORDER BY B.UPDATE_TIME DESC) T WHERE ROWNUM = 1]]>
     </select>
     
     <!-- 根据fundCode查询box账户是否存在 -->
     <select id="queryBoxAccountByFundCode" resultType="java.lang.Integer" parameterType="java.util.Map">
         SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BOX_ACCOUNT T WHERE T.FUND_CODE = #{fund_code, jdbcType=VARCHAR}
     </select>
     
     <!-- 查询是否可以关闭账户 -->
     <select id="queryIsCloseBoxAcc" resultType="java.lang.Integer" parameterType="java.util.Map">
          <![CDATA[  SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST C,APP___PAS__DBUSER.T_CONTRACT_MASTER M 
			WHERE C.POLICY_ID = M.POLICY_ID AND 
			EXISTS (SELECT * FROM APP___PAS__DBUSER.T_CONTRACT_MASTER CM WHERE CM.POLICY_ID = M.POLICY_ID AND CM.LIABILITY_STATE ='1') 
			AND C.ACCOUNT_CODE = #{fund_code, jdbcType=VARCHAR}]]>
     </select>
     
     <!--查询有相应菜单权限的用户 -->
     <select id="queryUserByPermission" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[    SELECT US.USER_ID,US.USER_NAME,US.EMAIL,US.REAL_NAME
             FROM APP___PAS__DBUSER.T_UDMP_USER US,
 			      APP___PAS__DBUSER.T_UDMP_GROUP_USER GU,
 			      APP___PAS__DBUSER.T_UDMP_GROUP_ROLE GR,
 			      APP___PAS__DBUSER.T_UDMP_ROLE_MODULE RM,
 			      APP___PAS__DBUSER.T_UDMP_MODULE M
 			WHERE M.MODULE_NAME = #{module_name, jdbcType=VARCHAR}
 				AND US.USER_ID = GU.USER_ID
 				AND GU.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
 				AND GR.ROLE_ID = RM.ROLE_ID
 				AND RM.MODULE_ID = M.MODULE_ID
 				AND US.USER_ID = #{user_id, jdbcType=NUMERIC}
 				AND ROWNUM <= 1000 ]]>
     </select>
     
     <!--renxd -->
	
</mapper>