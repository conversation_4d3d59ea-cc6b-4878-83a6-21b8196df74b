<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractMarketActDao">
<!--
	<sql id="PA_contractMarketActWhereCondition">
		<if test=" public_activity  != null "><![CDATA[ AND A.PUBLIC_ACTIVITY = #{public_activity} ]]></if>
		<if test=" suurender_handle_code != null and suurender_handle_code != ''  "><![CDATA[ AND A.SUURENDER_HANDLE_CODE = #{suurender_handle_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" main_list_id  != null "><![CDATA[ AND A.MAIN_LIST_ID = #{main_list_id} ]]></if>
		<if test=" hesitate_handle_code != null and hesitate_handle_code != ''  "><![CDATA[ AND A.HESITATE_HANDLE_CODE = #{hesitate_handle_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" market_line_no != null and market_line_no != ''  "><![CDATA[ AND A.MARKET_LINE_NO = #{market_line_no} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" cost_source_code != null and cost_source_code != ''  "><![CDATA[ AND A.COST_SOURCE_CODE = #{cost_source_code} ]]></if>
		<if test=" market_main_id  != null "><![CDATA[ AND A.MARKET_MAIN_ID = #{market_main_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" market_act_id  != null "><![CDATA[ AND A.MARKET_ACT_ID = #{market_act_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" market_is_gift != null and market_is_gift != ''  "><![CDATA[ AND A.MARKET_IS_GIFT = #{market_is_gift} ]]></if>
		<if test=" market_subactivity_name != null and market_subactivity_name != ''  "><![CDATA[ AND A.MARKET_SUBACTIVITY_NAME = #{market_subactivity_name} ]]></if>
		<if test=" market_subactivity_id  != null "><![CDATA[ AND A.MARKET_SUBACTIVITY_ID = #{market_subactivity_id} ]]></if>
		<if test=" market_discount_type != null and market_discount_type != ''  "><![CDATA[ AND A.MARKET_DISCOUNT_TYPE = #{market_discount_type} ]]></if>
		<if test=" market_cost_type != null and market_cost_type != ''  "><![CDATA[ AND A.MARKET_COST_TYPE = #{market_cost_type} ]]></if>
		<if test=" market_bonus  != null "><![CDATA[ AND A.MARKET_BONUS = #{market_bonus} ]]></if>
		<if test=" increases_code != null and increases_code != ''  "><![CDATA[ AND A.INCREASES_CODE = #{increases_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" market_cost  != null "><![CDATA[ AND A.MARKET_COST = #{market_cost} ]]></if>
		<if test=" market_discount  != null "><![CDATA[ AND A.MARKET_DISCOUNT = #{market_discount} ]]></if>
		<if test=" commission_code != null and commission_code != ''  "><![CDATA[ AND A.COMMISSION_CODE = #{commission_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	

<!-- 添加操作 -->
	<insert id="addContractMarketAct"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO T_CONTRACT_MARKET_ACT(
				PUBLIC_ACTIVITY, SUURENDER_HANDLE_CODE, ITEM_ID, MAIN_LIST_ID, HESITATE_HANDLE_CODE, APPLY_CODE, MARKET_LINE_NO, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, COST_SOURCE_CODE, MARKET_MAIN_ID, BUSI_ITEM_ID, MARKET_ACT_ID, 
				POLICY_ID, MARKET_IS_GIFT, MARKET_SUBACTIVITY_NAME, MARKET_SUBACTIVITY_ID, INSERT_TIME, MARKET_DISCOUNT_TYPE, UPDATE_TIME, 
				MARKET_COST_TYPE, MARKET_BONUS, INCREASES_CODE, POLICY_CODE, UPDATE_TIMESTAMP, INSERT_BY, MARKET_COST, 
				MARKET_DISCOUNT, COMMISSION_CODE ) 
			VALUES (
				#{public_activity, jdbcType=NUMERIC}, #{suurender_handle_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{main_list_id, jdbcType=NUMERIC} , #{hesitate_handle_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{market_line_no, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{cost_source_code, jdbcType=VARCHAR} , #{market_main_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{market_act_id, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} , #{market_is_gift, jdbcType=VARCHAR} , #{market_subactivity_name, jdbcType=VARCHAR} , #{market_subactivity_id, jdbcType=NUMERIC} , SYSDATE , #{market_discount_type, jdbcType=VARCHAR} , SYSDATE 
				, #{market_cost_type, jdbcType=VARCHAR} , #{market_bonus, jdbcType=NUMERIC} , #{increases_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{market_cost, jdbcType=NUMERIC} 
				, #{market_discount, jdbcType=NUMERIC} , #{commission_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteContractMarketAct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CONTRACT_MARKET_ACT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateContractMarketAct" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_CONTRACT_MARKET_ACT ]]>
		<set>
		<trim suffixOverrides=",">
		    PUBLIC_ACTIVITY = #{public_activity, jdbcType=NUMERIC} ,
			SUURENDER_HANDLE_CODE = #{suurender_handle_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    MAIN_LIST_ID = #{main_list_id, jdbcType=NUMERIC} ,
			HESITATE_HANDLE_CODE = #{hesitate_handle_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			MARKET_LINE_NO = #{market_line_no, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			COST_SOURCE_CODE = #{cost_source_code, jdbcType=VARCHAR} ,
		    MARKET_MAIN_ID = #{market_main_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    MARKET_ACT_ID = #{market_act_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			MARKET_IS_GIFT = #{market_is_gift, jdbcType=VARCHAR} ,
			MARKET_SUBACTIVITY_NAME = #{market_subactivity_name, jdbcType=VARCHAR} ,
		    MARKET_SUBACTIVITY_ID = #{market_subactivity_id, jdbcType=NUMERIC} ,
			MARKET_DISCOUNT_TYPE = #{market_discount_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			MARKET_COST_TYPE = #{market_cost_type, jdbcType=VARCHAR} ,
		    MARKET_BONUS = #{market_bonus, jdbcType=NUMERIC} ,
			INCREASES_CODE = #{increases_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    MARKET_COST = #{market_cost, jdbcType=NUMERIC} ,
		    MARKET_DISCOUNT = #{market_discount, jdbcType=NUMERIC} ,
			COMMISSION_CODE = #{commission_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	

<!-- 按map查询操作 -->
	<select id="findAllMapContractMarketAct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PUBLIC_ACTIVITY, A.SUURENDER_HANDLE_CODE, A.ITEM_ID, A.MAIN_LIST_ID, A.HESITATE_HANDLE_CODE, A.APPLY_CODE, A.MARKET_LINE_NO, 
			A.LIST_ID, A.COST_SOURCE_CODE, A.MARKET_MAIN_ID, A.BUSI_ITEM_ID, A.MARKET_ACT_ID, 
			A.POLICY_ID, A.MARKET_IS_GIFT, A.MARKET_SUBACTIVITY_NAME, A.MARKET_SUBACTIVITY_ID, A.MARKET_DISCOUNT_TYPE, 
			A.MARKET_COST_TYPE, A.MARKET_BONUS, A.INCREASES_CODE, A.POLICY_CODE, A.MARKET_COST, 
			A.MARKET_DISCOUNT, A.COMMISSION_CODE FROM T_CONTRACT_MARKET_ACT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllContractMarketAct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PUBLIC_ACTIVITY, A.SUURENDER_HANDLE_CODE, A.ITEM_ID, A.MAIN_LIST_ID, A.HESITATE_HANDLE_CODE, A.APPLY_CODE, A.MARKET_LINE_NO, 
			A.LIST_ID, A.COST_SOURCE_CODE, A.MARKET_MAIN_ID, A.BUSI_ITEM_ID, A.MARKET_ACT_ID, 
			A.POLICY_ID, A.MARKET_IS_GIFT, A.MARKET_SUBACTIVITY_NAME, A.MARKET_SUBACTIVITY_ID, A.MARKET_DISCOUNT_TYPE, 
			A.MARKET_COST_TYPE, A.MARKET_BONUS, A.INCREASES_CODE, A.POLICY_CODE, A.MARKET_COST, 
			A.MARKET_DISCOUNT, A.COMMISSION_CODE FROM T_CONTRACT_MARKET_ACT A WHERE  ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<!--查询单个操作  -->
	<select id="findContractMarketAct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PUBLIC_ACTIVITY, A.SUURENDER_HANDLE_CODE, A.ITEM_ID, A.MAIN_LIST_ID, A.HESITATE_HANDLE_CODE, A.APPLY_CODE, A.MARKET_LINE_NO, 
			A.LIST_ID, A.COST_SOURCE_CODE, A.MARKET_MAIN_ID, A.BUSI_ITEM_ID, A.MARKET_ACT_ID, 
			A.POLICY_ID, A.MARKET_IS_GIFT, A.MARKET_SUBACTIVITY_NAME, A.MARKET_SUBACTIVITY_ID, A.MARKET_DISCOUNT_TYPE, 
			A.MARKET_COST_TYPE, A.MARKET_BONUS, A.INCREASES_CODE, A.POLICY_CODE, A.MARKET_COST, 
			A.MARKET_DISCOUNT, A.COMMISSION_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MARKET_ACT A WHERE A.POLICY_ID = #{policy_id} AND A.BUSI_ITEM_ID = #{busi_item_id} AND ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findContractMarketActTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CONTRACT_MARKET_ACT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryContractMarketActForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PUBLIC_ACTIVITY, B.SUURENDER_HANDLE_CODE, B.ITEM_ID, B.MAIN_LIST_ID, B.HESITATE_HANDLE_CODE, B.APPLY_CODE, B.MARKET_LINE_NO, 
			B.LIST_ID, B.COST_SOURCE_CODE, B.MARKET_MAIN_ID, B.BUSI_ITEM_ID, B.MARKET_ACT_ID, 
			B.POLICY_ID, B.MARKET_IS_GIFT, B.MARKET_SUBACTIVITY_NAME, B.MARKET_SUBACTIVITY_ID, B.MARKET_DISCOUNT_TYPE, 
			B.MARKET_COST_TYPE, B.MARKET_BONUS, B.INCREASES_CODE, B.POLICY_CODE, B.MARKET_COST, 
			B.MARKET_DISCOUNT, B.COMMISSION_CODE FROM (
					SELECT ROWNUM RN, A.PUBLIC_ACTIVITY, A.SUURENDER_HANDLE_CODE, A.ITEM_ID, A.MAIN_LIST_ID, A.HESITATE_HANDLE_CODE, A.APPLY_CODE, A.MARKET_LINE_NO, 
			A.LIST_ID, A.COST_SOURCE_CODE, A.MARKET_MAIN_ID, A.BUSI_ITEM_ID, A.MARKET_ACT_ID, 
			A.POLICY_ID, A.MARKET_IS_GIFT, A.MARKET_SUBACTIVITY_NAME, A.MARKET_SUBACTIVITY_ID, A.MARKET_DISCOUNT_TYPE, 
			A.MARKET_COST_TYPE, A.MARKET_BONUS, A.INCREASES_CODE, A.POLICY_CODE, A.MARKET_COST, 
			A.MARKET_DISCOUNT, A.COMMISSION_CODE FROM T_CONTRACT_MARKET_ACT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
