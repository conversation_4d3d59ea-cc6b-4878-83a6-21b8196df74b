<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IShortPremArapDao">

	<sql id="shortPremArapWhereCondition">
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" frozen_status != null and frozen_status != ''  "><![CDATA[ AND A.FROZEN_STATUS = #{frozen_status} ]]></if>
		<if test=" refeflag != null and refeflag != ''  "><![CDATA[ AND A.REFEFLAG = #{refeflag} ]]></if>
		<if test=" area != null and area != ''  "><![CDATA[ AND A.AREA = #{area} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" payee_email != null and payee_email != ''  "><![CDATA[ AND A.PAYEE_EMAIL = #{payee_email} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" is_bank_account_by  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT_BY = #{is_bank_account_by} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
		<if test=" cip_branch_bank_code != null and cip_branch_bank_code != ''  "><![CDATA[ AND A.CIP_BRANCH_BANK_CODE = #{cip_branch_bank_code} ]]></if>
		<if test=" product_abbr_name != null and product_abbr_name != ''  "><![CDATA[ AND A.PRODUCT_ABBR_NAME = #{product_abbr_name} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != ''  "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		<if test=" pay_end_date  != null  and  pay_end_date  != ''  "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" audit_date  != null  and  audit_date  != ''  "><![CDATA[ AND A.AUDIT_DATE = #{audit_date} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" cip_district_bank_code != null and cip_district_bank_code != ''  "><![CDATA[ AND A.CIP_DISTRICT_BANK_CODE = #{cip_district_bank_code} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" payee_phone != null and payee_phone != ''  "><![CDATA[ AND A.PAYEE_PHONE = #{payee_phone} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" frozen_status_date  != null  and  frozen_status_date  != ''  "><![CDATA[ AND A.FROZEN_STATUS_DATE = #{frozen_status_date} ]]></if>
		<if test=" pre_unit_number != null and pre_unit_number != ''  "><![CDATA[ AND A.PRE_UNIT_NUMBER = #{pre_unit_number} ]]></if>
		<if test=" cip_bank_code != null and cip_bank_code != ''  "><![CDATA[ AND A.CIP_BANK_CODE = #{cip_bank_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" product_channel != null and product_channel != ''  "><![CDATA[ AND A.PRODUCT_CHANNEL = #{product_channel} ]]></if>
		<if test=" group != null and group != ''  "><![CDATA[ AND A.GROUP = #{group} ]]></if>
		<if test=" fee_amount_befor_tax  != null "><![CDATA[ AND A.FEE_AMOUNT_BEFOR_TAX = #{fee_amount_befor_tax} ]]></if>
		<if test=" frozen_status_by  != null "><![CDATA[ AND A.FROZEN_STATUS_BY = #{frozen_status_by} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" part != null and part != ''  "><![CDATA[ AND A.PART = #{part} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" is_item_main  != null "><![CDATA[ AND A.IS_ITEM_MAIN = #{is_item_main} ]]></if>
		<if test=" fee_amount_tax  != null "><![CDATA[ AND A.FEE_AMOUNT_TAX = #{fee_amount_tax} ]]></if>
		<if test=" is_bank_account_date  != null  and  is_bank_account_date  != ''  "><![CDATA[ AND A.IS_BANK_ACCOUNT_DATE = #{is_bank_account_date} ]]></if>
		<if test=" group_code != null and group_code != ''  "><![CDATA[ AND A.GROUP_CODE = #{group_code} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" statistical_date  != null  and  statistical_date  != ''  "><![CDATA[ AND A.STATISTICAL_DATE = #{statistical_date} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" fee_status_by  != null "><![CDATA[ AND A.FEE_STATUS_BY = #{fee_status_by} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" is_bank_account  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT = #{is_bank_account} ]]></if>
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
		<if test=" policy_organ_code != null and policy_organ_code != ''  "><![CDATA[ AND A.POLICY_ORGAN_CODE = #{policy_organ_code} ]]></if>
		<if test=" group_name != null and group_name != ''  "><![CDATA[ AND A.GROUP_NAME = #{group_name} ]]></if>
		<if test=" pay_liab_code != null and pay_liab_code != ''  "><![CDATA[ AND A.PAY_LIAB_CODE = #{pay_liab_code} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" fee_status_date  != null  and  fee_status_date  != ''  "><![CDATA[ AND A.FEE_STATUS_DATE = #{fee_status_date} ]]></if>
		<if test=" policy_balance  != null "><![CDATA[ AND A.POLICY_BALANCE = #{policy_balance} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" customer_account_flag  != null "><![CDATA[ AND A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryShortPremArapByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByBankAccountCondition">
		<if test=" bank_account != null and bank_account != '' "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByFeeTypeCondition">
		<if test=" fee_type != null and fee_type != '' "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByBusinessCodeCondition">
		<if test=" business_code != null and business_code != '' "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByBusiApplyDateCondition">
		<if test=" busi_apply_date  != null "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByDerivTypeCondition">
		<if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByDueTimeCondition">
		<if test=" due_time  != null "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByFeeStatusCondition">
		<if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByFinishTimeCondition">
		<if test=" finish_time  != null "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByHolderIdCondition">
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByInsuredIdCondition">
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByPayEndDateCondition">
		<if test=" pay_end_date  != null "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryShortPremArapByUnitNumberCondition">
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addShortPremArap"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SHORT_PR_ARAP__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SHORT_PREM_ARAP(
				PAYEE_NAME, CERTI_CODE, POLICY_YEAR, SERVICE_CODE, AGENT_NAME, ORGAN_CODE, BUSINESS_CODE, 
				POLICY_TYPE, POLICY_CHG_ID, PRODUCT_CODE, FROZEN_STATUS, REFEFLAG, AREA, FINISH_TIME, 
				INSURED_NAME, PAYEE_EMAIL, BRANCH_CODE, CHANNEL_TYPE, IS_BANK_ACCOUNT_BY, HOLDER_ID, PAID_COUNT, 
				FEE_AMOUNT, DERIV_TYPE, BUSI_APPLY_DATE, CIP_BRANCH_BANK_CODE, PRODUCT_ABBR_NAME, INSURED_ID, CUSTOMER_ID, 
				BUSI_PROD_NAME, AGENT_CODE, FEE_TYPE, WITHDRAW_TYPE, INSERT_BY, PAY_END_DATE, MONEY_CODE, 
				AUDIT_DATE, OPERATOR_BY, CIP_DISTRICT_BANK_CODE, DUE_TIME, PAYEE_PHONE, PREM_FREQ, UPDATE_TIMESTAMP, 
				FROZEN_STATUS_DATE, PRE_UNIT_NUMBER, CIP_BANK_CODE, VALIDATE_DATE, COVERAGE_PERIOD, APPLY_CODE, HOLDER_NAME, 
				BUSI_PROD_CODE, PRODUCT_CHANNEL, INSERT_TIMESTAMP, "GROUP", FEE_AMOUNT_BEFOR_TAX, FROZEN_STATUS_BY, BANK_USER_NAME, 
				PART, BUSINESS_TYPE, IS_ITEM_MAIN, FEE_AMOUNT_TAX, IS_BANK_ACCOUNT_DATE, GROUP_CODE, BANK_CODE, 
				CERTI_TYPE, STATISTICAL_DATE, FEE_STATUS, INSERT_TIME, FEE_STATUS_BY, COVERAGE_YEAR, UPDATE_BY, 
				CHARGE_YEAR, PAY_MODE, UPDATE_TIME, IS_BANK_ACCOUNT, ROLLBACK_UNIT_NUMBER, POLICY_ORGAN_CODE, GROUP_NAME, 
				PAY_LIAB_CODE, BANK_ACCOUNT, UNIT_NUMBER, FEE_STATUS_DATE, POLICY_BALANCE, LIST_ID, POLICY_CODE, 
				IS_RISK_MAIN, CUSTOMER_ACCOUNT_FLAG, ARAP_FLAG,BUSI_ITEM_ID ) 
			VALUES (
				#{payee_name, jdbcType=VARCHAR}, #{certi_code, jdbcType=VARCHAR} , #{policy_year, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{agent_name, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{business_code, jdbcType=VARCHAR} 
				, #{policy_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{frozen_status, jdbcType=VARCHAR} , #{refeflag, jdbcType=VARCHAR} , #{area, jdbcType=VARCHAR} , #{finish_time, jdbcType=DATE} 
				, #{insured_name, jdbcType=VARCHAR} , #{payee_email, jdbcType=VARCHAR} , #{branch_code, jdbcType=VARCHAR} , #{channel_type, jdbcType=VARCHAR} , #{is_bank_account_by, jdbcType=NUMERIC} , #{holder_id, jdbcType=NUMERIC} , #{paid_count, jdbcType=NUMERIC} 
				, #{fee_amount, jdbcType=NUMERIC} , #{deriv_type, jdbcType=VARCHAR} , #{busi_apply_date, jdbcType=DATE} , #{cip_branch_bank_code, jdbcType=VARCHAR} , #{product_abbr_name, jdbcType=VARCHAR} , #{insured_id, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} 
				, #{busi_prod_name, jdbcType=VARCHAR} , #{agent_code, jdbcType=VARCHAR} , #{fee_type, jdbcType=VARCHAR} , #{withdraw_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{pay_end_date, jdbcType=DATE} , #{money_code, jdbcType=VARCHAR} 
				, #{audit_date, jdbcType=DATE} , #{operator_by, jdbcType=NUMERIC} , #{cip_district_bank_code, jdbcType=VARCHAR} , #{due_time, jdbcType=DATE} , #{payee_phone, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{frozen_status_date, jdbcType=DATE} , #{pre_unit_number, jdbcType=VARCHAR} , #{cip_bank_code, jdbcType=VARCHAR} , #{validate_date, jdbcType=DATE} , #{coverage_period, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{holder_name, jdbcType=VARCHAR} 
				, #{busi_prod_code, jdbcType=VARCHAR} , #{product_channel, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{group, jdbcType=VARCHAR} , #{fee_amount_befor_tax, jdbcType=NUMERIC} , #{frozen_status_by, jdbcType=NUMERIC} , #{bank_user_name, jdbcType=VARCHAR} 
				, #{part, jdbcType=VARCHAR} , #{business_type, jdbcType=VARCHAR} , #{is_item_main, jdbcType=NUMERIC} , #{fee_amount_tax, jdbcType=NUMERIC} , #{is_bank_account_date, jdbcType=DATE} , #{group_code, jdbcType=VARCHAR} , #{bank_code, jdbcType=VARCHAR} 
				, #{certi_type, jdbcType=VARCHAR} , #{statistical_date, jdbcType=DATE} , #{fee_status, jdbcType=VARCHAR} , SYSDATE , #{fee_status_by, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} 
				, #{charge_year, jdbcType=NUMERIC} , #{pay_mode, jdbcType=VARCHAR} , SYSDATE , #{is_bank_account, jdbcType=NUMERIC} , #{rollback_unit_number, jdbcType=VARCHAR} , #{policy_organ_code, jdbcType=VARCHAR} , #{group_name, jdbcType=VARCHAR} 
				, #{pay_liab_code, jdbcType=VARCHAR} , #{bank_account, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{fee_status_date, jdbcType=DATE} , #{policy_balance, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} 
				, #{is_risk_main, jdbcType=NUMERIC} , #{customer_account_flag, jdbcType=NUMERIC} , #{arap_flag, jdbcType=VARCHAR}, #{busi_item_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteShortPremArap" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateShortPremArap" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SHORT_PREM_ARAP ]]>
		<set>
		<trim suffixOverrides=",">
			PAYEE_NAME = #{payee_name, jdbcType=VARCHAR} ,
			CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} ,
			REFEFLAG = #{refeflag, jdbcType=VARCHAR} ,
			AREA = #{area, jdbcType=VARCHAR} ,
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
			INSURED_NAME = #{insured_name, jdbcType=VARCHAR} ,
			PAYEE_EMAIL = #{payee_email, jdbcType=VARCHAR} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    IS_BANK_ACCOUNT_BY = #{is_bank_account_by, jdbcType=NUMERIC} ,
		    HOLDER_ID = #{holder_id, jdbcType=NUMERIC} ,
		    PAID_COUNT = #{paid_count, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
			DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} ,
		    BUSI_APPLY_DATE = #{busi_apply_date, jdbcType=DATE} ,
			CIP_BRANCH_BANK_CODE = #{cip_branch_bank_code, jdbcType=VARCHAR} ,
			PRODUCT_ABBR_NAME = #{product_abbr_name, jdbcType=VARCHAR} ,
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			BUSI_PROD_NAME = #{busi_prod_name, jdbcType=VARCHAR} ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
			FEE_TYPE = #{fee_type, jdbcType=VARCHAR} ,
			WITHDRAW_TYPE = #{withdraw_type, jdbcType=VARCHAR} ,
		    PAY_END_DATE = #{pay_end_date, jdbcType=DATE} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    AUDIT_DATE = #{audit_date, jdbcType=DATE} ,
		    OPERATOR_BY = #{operator_by, jdbcType=NUMERIC} ,
			CIP_DISTRICT_BANK_CODE = #{cip_district_bank_code, jdbcType=VARCHAR} ,
		    DUE_TIME = #{due_time, jdbcType=DATE} ,
			PAYEE_PHONE = #{payee_phone, jdbcType=VARCHAR} ,
		    PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    FROZEN_STATUS_DATE = #{frozen_status_date, jdbcType=DATE} ,
			PRE_UNIT_NUMBER = #{pre_unit_number, jdbcType=VARCHAR} ,
			CIP_BANK_CODE = #{cip_bank_code, jdbcType=VARCHAR} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
			COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			HOLDER_NAME = #{holder_name, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			PRODUCT_CHANNEL = #{product_channel, jdbcType=VARCHAR} ,
			"GROUP" = #{group, jdbcType=VARCHAR} ,
		    FEE_AMOUNT_BEFOR_TAX = #{fee_amount_befor_tax, jdbcType=NUMERIC} ,
		    FROZEN_STATUS_BY = #{frozen_status_by, jdbcType=NUMERIC} ,
			BANK_USER_NAME = #{bank_user_name, jdbcType=VARCHAR} ,
			PART = #{part, jdbcType=VARCHAR} ,
			BUSINESS_TYPE = #{business_type, jdbcType=VARCHAR} ,
		    IS_ITEM_MAIN = #{is_item_main, jdbcType=NUMERIC} ,
		    FEE_AMOUNT_TAX = #{fee_amount_tax, jdbcType=NUMERIC} ,
		    IS_BANK_ACCOUNT_DATE = #{is_bank_account_date, jdbcType=DATE} ,
			GROUP_CODE = #{group_code, jdbcType=VARCHAR} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
			CERTI_TYPE = #{certi_type, jdbcType=VARCHAR} ,
		    STATISTICAL_DATE = #{statistical_date, jdbcType=DATE} ,
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
		    FEE_STATUS_BY = #{fee_status_by, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    IS_BANK_ACCOUNT = #{is_bank_account, jdbcType=NUMERIC} ,
			ROLLBACK_UNIT_NUMBER = #{rollback_unit_number, jdbcType=VARCHAR} ,
			POLICY_ORGAN_CODE = #{policy_organ_code, jdbcType=VARCHAR} ,
			GROUP_NAME = #{group_name, jdbcType=VARCHAR} ,
			PAY_LIAB_CODE = #{pay_liab_code, jdbcType=VARCHAR} ,
			BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
		    FEE_STATUS_DATE = #{fee_status_date, jdbcType=DATE} ,
		    POLICY_BALANCE = #{policy_balance, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    IS_RISK_MAIN = #{is_risk_main, jdbcType=NUMERIC} ,
		    CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag, jdbcType=NUMERIC} ,
			ARAP_FLAG = #{arap_flag, jdbcType=VARCHAR} ,
			BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findShortPremArapByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByBankAccountCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByFeeType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByFeeTypeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByBusinessCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByBusiApplyDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByBusiApplyDateCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByDerivType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByDerivTypeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByDueTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByDueTimeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByFeeStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByFeeStatusCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByFinishTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByFinishTimeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByHolderId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByHolderIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByInsuredIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByPayEndDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByPayEndDateCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findShortPremArapByUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryShortPremArapByUnitNumberCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapShortPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllShortPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<include refid="shortPremArapWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findShortPremArapTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_SHORT_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="shortPremArapWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryShortPremArapForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PAYEE_NAME, B.CERTI_CODE, B.POLICY_YEAR, B.SERVICE_CODE, B.AGENT_NAME, B.ORGAN_CODE, B.BUSINESS_CODE, 
			B.POLICY_TYPE, B.POLICY_CHG_ID, B.PRODUCT_CODE, B.FROZEN_STATUS, B.REFEFLAG, B.AREA, B.FINISH_TIME, B.BUSI_ITEM_ID,
			B.INSURED_NAME, B.PAYEE_EMAIL, B.BRANCH_CODE, B.CHANNEL_TYPE, B.IS_BANK_ACCOUNT_BY, B.HOLDER_ID, B.PAID_COUNT, 
			B.FEE_AMOUNT, B.DERIV_TYPE, B.BUSI_APPLY_DATE, B.CIP_BRANCH_BANK_CODE, B.PRODUCT_ABBR_NAME, B.INSURED_ID, B.CUSTOMER_ID, 
			B.BUSI_PROD_NAME, B.AGENT_CODE, B.FEE_TYPE, B.WITHDRAW_TYPE, B.PAY_END_DATE, B.MONEY_CODE, 
			B.AUDIT_DATE, B.OPERATOR_BY, B.CIP_DISTRICT_BANK_CODE, B.DUE_TIME, B.PAYEE_PHONE, B.PREM_FREQ, 
			B.FROZEN_STATUS_DATE, B.PRE_UNIT_NUMBER, B.CIP_BANK_CODE, B.VALIDATE_DATE, B.COVERAGE_PERIOD, B.APPLY_CODE, B.HOLDER_NAME, 
			B.BUSI_PROD_CODE, B.PRODUCT_CHANNEL, B.GROUP, B.FEE_AMOUNT_BEFOR_TAX, B.FROZEN_STATUS_BY, B.BANK_USER_NAME, 
			B.PART, B.BUSINESS_TYPE, B.IS_ITEM_MAIN, B.FEE_AMOUNT_TAX, B.IS_BANK_ACCOUNT_DATE, B.GROUP_CODE, B.BANK_CODE, 
			B.CERTI_TYPE, B.STATISTICAL_DATE, B.FEE_STATUS, B.FEE_STATUS_BY, B.COVERAGE_YEAR, 
			B.CHARGE_YEAR, B.PAY_MODE, B.IS_BANK_ACCOUNT, B.ROLLBACK_UNIT_NUMBER, B.POLICY_ORGAN_CODE, B.GROUP_NAME, 
			B.PAY_LIAB_CODE, B.BANK_ACCOUNT, B.UNIT_NUMBER, B.FEE_STATUS_DATE, B.POLICY_BALANCE, B.LIST_ID, B.POLICY_CODE, 
			B.IS_RISK_MAIN, B.CUSTOMER_ACCOUNT_FLAG, B.ARAP_FLAG FROM (
					SELECT ROWNUM RN, A.PAYEE_NAME, A.CERTI_CODE, A.POLICY_YEAR, A.SERVICE_CODE, A.AGENT_NAME, A.ORGAN_CODE, A.BUSINESS_CODE, 
			A.POLICY_TYPE, A.POLICY_CHG_ID, A.PRODUCT_CODE, A.FROZEN_STATUS, A.REFEFLAG, A.AREA, A.FINISH_TIME, A.BUSI_ITEM_ID,
			A.INSURED_NAME, A.PAYEE_EMAIL, A.BRANCH_CODE, A.CHANNEL_TYPE, A.IS_BANK_ACCOUNT_BY, A.HOLDER_ID, A.PAID_COUNT, 
			A.FEE_AMOUNT, A.DERIV_TYPE, A.BUSI_APPLY_DATE, A.CIP_BRANCH_BANK_CODE, A.PRODUCT_ABBR_NAME, A.INSURED_ID, A.CUSTOMER_ID, 
			A.BUSI_PROD_NAME, A.AGENT_CODE, A.FEE_TYPE, A.WITHDRAW_TYPE, A.PAY_END_DATE, A.MONEY_CODE, 
			A.AUDIT_DATE, A.OPERATOR_BY, A.CIP_DISTRICT_BANK_CODE, A.DUE_TIME, A.PAYEE_PHONE, A.PREM_FREQ, 
			A.FROZEN_STATUS_DATE, A.PRE_UNIT_NUMBER, A.CIP_BANK_CODE, A.VALIDATE_DATE, A.COVERAGE_PERIOD, A.APPLY_CODE, A.HOLDER_NAME, 
			A.BUSI_PROD_CODE, A.PRODUCT_CHANNEL, A."GROUP", A.FEE_AMOUNT_BEFOR_TAX, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, 
			A.PART, A.BUSINESS_TYPE, A.IS_ITEM_MAIN, A.FEE_AMOUNT_TAX, A.IS_BANK_ACCOUNT_DATE, A.GROUP_CODE, A.BANK_CODE, 
			A.CERTI_TYPE, A.STATISTICAL_DATE, A.FEE_STATUS, A.FEE_STATUS_BY, A.COVERAGE_YEAR, 
			A.CHARGE_YEAR, A.PAY_MODE, A.IS_BANK_ACCOUNT, A.ROLLBACK_UNIT_NUMBER, A.POLICY_ORGAN_CODE, A.GROUP_NAME, 
			A.PAY_LIAB_CODE, A.BANK_ACCOUNT, A.UNIT_NUMBER, A.FEE_STATUS_DATE, A.POLICY_BALANCE, A.LIST_ID, A.POLICY_CODE, 
			A.IS_RISK_MAIN, A.CUSTOMER_ACCOUNT_FLAG, A.ARAP_FLAG FROM APP___PAS__DBUSER.T_SHORT_PREM_ARAP A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

   <update id="updateShortPremArapFeeStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SHORT_PREM_ARAP ]]>
		<set>
		<trim suffixOverrides=",">			
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,		    
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	
</mapper>
