<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractProductLogDao">

	<sql id="PA_contractProductLogWhereCondition">
		<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_pause} ]]></if>
		<if test=" prod_pkg_plan_code != null and prod_pkg_plan_code != ''  "><![CDATA[ AND A.PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code} ]]></if>
		<if test=" append_prem_af  != null "><![CDATA[ AND A.APPEND_PREM_AF = #{append_prem_af} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" norenew_reason != null and norenew_reason != ''  "><![CDATA[ AND A.NORENEW_REASON = #{norenew_reason} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" initial_amount  != null "><![CDATA[ AND A.INITIAL_AMOUNT = #{initial_amount} ]]></if>
		<if test=" cc_sa  != null "><![CDATA[ AND A.CC_SA = #{cc_sa} ]]></if>
		<if test=" initial_extra_prem_af  != null "><![CDATA[ AND A.INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af} ]]></if>
		<if test=" payout_rate  != null "><![CDATA[ AND A.PAYOUT_RATE = #{payout_rate} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" count_way != null and count_way != ''  "><![CDATA[ AND A.COUNT_WAY = #{count_way} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" is_gift  != null "><![CDATA[ AND A.IS_GIFT = #{is_gift} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" additional_prem_af  != null "><![CDATA[ AND A.ADDITIONAL_PREM_AF = #{additional_prem_af} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" bonus_mode  != null "><![CDATA[ AND A.BONUS_MODE = #{bonus_mode} ]]></if>
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" interest_flag  != null "><![CDATA[ AND A.INTEREST_FLAG = #{interest_flag} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" renewal_extra_prem_af  != null "><![CDATA[ AND A.RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" health_service_flag  != null "><![CDATA[ AND A.HEALTH_SERVICE_FLAG = #{health_service_flag} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" pay_freq != null and pay_freq != ''  "><![CDATA[ AND A.PAY_FREQ = #{pay_freq} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" renewal_discnted_prem_af  != null "><![CDATA[ AND A.RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" pause_date  != null  and  pause_date  != ''  "><![CDATA[ AND A.PAUSE_DATE = #{pause_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" is_master_item  != null "><![CDATA[ AND A.IS_MASTER_ITEM = #{is_master_item} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" deductible_franchise  != null "><![CDATA[ AND A.DEDUCTIBLE_FRANCHISE = #{deductible_franchise} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" initial_discnt_prem_af  != null "><![CDATA[ AND A.INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af} ]]></if>
		<if test=" bonus_w_mode  != null "><![CDATA[ AND A.BONUS_W_MODE = #{bonus_w_mode} ]]></if>
		<if test=" last_bonus_date  != null "><![CDATA[ AND A.LAST_BONUS_DATE = #{last_bonus_date} ]]></if>
	    <if test=" tax_extension_prem_rate != null and tax_extension_prem_rate != ''  "><![CDATA[ AND A.TAX_EXTENSION_PREM_RATE = #{tax_extension_prem_rate} ]]></if>
	    <if test=" discount_type != null and discount_type != ''  "><![CDATA[ AND A.DISCOUNT_TYPE = #{discount_type} ]]></if>
	    <if test=" discount_rate != null and discount_rate != ''  "><![CDATA[ AND A.DISCOUNT_RATE = #{discount_rate} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractProductLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractProductLogByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractProductLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractProductLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractProductLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_PRODUCT_LOG__LOG_ID.NEXTVAL from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG(
				IS_PAUSE, PROD_PKG_PLAN_CODE, APPEND_PREM_AF, IS_WAIVED, APPLY_CODE, NORENEW_REASON, INTEREST_MODE, 
				ORGAN_CODE, UPDATE_BY, CHARGE_YEAR, EXTRA_PREM_AF, POLICY_ID, INITIAL_AMOUNT, CC_SA, 
				INITIAL_EXTRA_PREM_AF, UPDATE_TIME, PAYOUT_RATE, AMOUNT, PAIDUP_DATE, PAY_YEAR, EXPIRY_DATE, 
				PAY_PERIOD, LIABILITY_STATE, COUNT_WAY, POLICY_CODE, IS_GIFT, RERINSTATE_DATE, ADDITIONAL_PREM_AF, 
				RENEW_DECISION, VALIDATE_DATE, BONUS_MODE, UPDATE_TIMESTAMP, INSERT_BY, BENEFIT_LEVEL, PRODUCT_ID, 
				BONUS_SA, PRODUCT_CODE, APPLY_DATE, INTEREST_FLAG, COVERAGE_PERIOD, ITEM_ID, RENEWAL_EXTRA_PREM_AF, 
				INSERT_TIMESTAMP, WAIVER_END, MATURITY_DATE, POLICY_CHG_ID, BUSI_ITEM_ID, HEALTH_SERVICE_FLAG, LAPSE_DATE, 
				PAY_FREQ, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, LAPSE_CAUSE, RENEWAL_DISCNTED_PREM_AF, 
				TOTAL_PREM_AF, DECISION_CODE, PAUSE_DATE, LOG_ID, CHARGE_PERIOD, STD_PREM_AF, IS_MASTER_ITEM, 
				SUSPEND_DATE, SUSPEND_CAUSE, DEDUCTIBLE_FRANCHISE, LOG_TYPE, WAIVER_START, PREM_FREQ, INITIAL_DISCNT_PREM_AF,BONUS_W_MODE,LAST_BONUS_DATE,ANNU_PAY_TYPE,START_PAY_AGE,START_PAY_DATE,IRREGULAR_PREM,TAX_EXTENSION_PREM_RATE,DISCOUNT_TYPE,DISCOUNT_RATE) 
			VALUES (
				#{is_pause, jdbcType=NUMERIC}, #{prod_pkg_plan_code, jdbcType=VARCHAR} , #{append_prem_af, jdbcType=NUMERIC} , #{is_waived, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{norenew_reason, jdbcType=VARCHAR} , #{interest_mode, jdbcType=NUMERIC} 
				, #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{initial_amount, jdbcType=NUMERIC} , #{cc_sa, jdbcType=NUMERIC} 
				, #{initial_extra_prem_af, jdbcType=NUMERIC} , SYSDATE , #{payout_rate, jdbcType=NUMERIC} , #{amount, jdbcType=NUMERIC} , #{paidup_date, jdbcType=DATE} , #{pay_year, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} 
				, #{pay_period, jdbcType=VARCHAR} , #{liability_state, jdbcType=NUMERIC} , #{count_way, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{is_gift, jdbcType=NUMERIC} , #{rerinstate_date, jdbcType=DATE} , #{additional_prem_af, jdbcType=NUMERIC} 
				, #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} 
				, #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{interest_flag, jdbcType=NUMERIC} , #{coverage_period, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{renewal_extra_prem_af, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{health_service_flag, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} 
				, #{pay_freq, jdbcType=VARCHAR} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{lapse_cause, jdbcType=VARCHAR} , #{renewal_discnted_prem_af, jdbcType=NUMERIC} 
				, #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , #{log_id, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{is_master_item, jdbcType=NUMERIC} 
				, #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{deductible_franchise, jdbcType=NUMERIC} , #{log_type, jdbcType=VARCHAR} , #{waiver_start, jdbcType=DATE} , #{prem_freq, jdbcType=NUMERIC} , #{initial_discnt_prem_af, jdbcType=NUMERIC} 
				, #{bonus_w_mode, jdbcType=NUMERIC} , #{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR},  #{start_pay_age, jdbcType=NUMERIC}, #{start_pay_date, jdbcType=DATE},#{irregular_prem, jdbcType=NUMERIC}, #{tax_extension_prem_rate, jdbcType=NUMERIC}, #{discount_type, jdbcType=VARCHAR}, #{discount_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractProductLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractProductLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    IS_PAUSE = #{is_pause, jdbcType=NUMERIC} ,
			PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code, jdbcType=VARCHAR} ,
		    APPEND_PREM_AF = #{append_prem_af, jdbcType=NUMERIC} ,
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			NORENEW_REASON = #{norenew_reason, jdbcType=VARCHAR} ,
		    INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    EXTRA_PREM_AF = #{extra_prem_af, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    INITIAL_AMOUNT = #{initial_amount, jdbcType=NUMERIC} ,
		    CC_SA = #{cc_sa, jdbcType=NUMERIC} ,
		    INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAYOUT_RATE = #{payout_rate, jdbcType=NUMERIC} ,
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
			PAY_PERIOD = #{pay_period, jdbcType=VARCHAR} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			COUNT_WAY = #{count_way, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    IS_GIFT = #{is_gift, jdbcType=NUMERIC} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
		    ADDITIONAL_PREM_AF = #{additional_prem_af, jdbcType=NUMERIC} ,
		    RENEW_DECISION = #{renew_decision, jdbcType=NUMERIC} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    BONUS_MODE = #{bonus_mode, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			BENEFIT_LEVEL = #{benefit_level, jdbcType=VARCHAR} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INTEREST_FLAG = #{interest_flag, jdbcType=NUMERIC} ,
			COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af, jdbcType=NUMERIC} ,
		    WAIVER_END = #{waiver_end, jdbcType=DATE} ,
		    MATURITY_DATE = #{maturity_date, jdbcType=DATE} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    HEALTH_SERVICE_FLAG = #{health_service_flag, jdbcType=NUMERIC} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
			PAY_FREQ = #{pay_freq, jdbcType=VARCHAR} ,
		    UNIT = #{unit, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
		    RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af, jdbcType=NUMERIC} ,
		    TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		    PAUSE_DATE = #{pause_date, jdbcType=DATE} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
		    STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
		    IS_MASTER_ITEM = #{is_master_item, jdbcType=NUMERIC} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    DEDUCTIBLE_FRANCHISE = #{deductible_franchise, jdbcType=NUMERIC} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    WAIVER_START = #{waiver_start, jdbcType=DATE} ,
		    PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
		    INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af, jdbcType=NUMERIC} ,
		    BONUS_W_MODE = #{bonus_w_mode, jdbcType=NUMERIC} ,
		    LAST_BONUS_DATE = #{last_bonus_date, jdbcType=DATE} ,
		    TAX_EXTENSION_PREM_RATE = #{tax_extension_prem_rate, jdbcType=NUMERIC},
		    DISCOUNT_TYPE = #{discount_type, jdbcType=VARCHAR}, 
		    DISCOUNT_RATE = #{discount_rate, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	 
	<select id="PA_findContractProductLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE 1 = 1]]>
		<include refid="PA_queryContractProductLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
<!-- 按索引查询操作 -->	
	<select id="PA_findContractProductLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_contractProductLogWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>	
	<select id="PA_findContractProductLogByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE 1 = 1   ]]>
		<include refid="PA_queryContractProductLogByItemIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="PA_findContractProductLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductLogByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="PA_findContractProductLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductLogByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractProductLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractProductLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE ROWNUM <=  1000 ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractProductLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CONTRACT_PRODUCT_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractProductLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.IS_PAUSE, B.PROD_PKG_PLAN_CODE, B.APPEND_PREM_AF, B.IS_WAIVED, B.APPLY_CODE, B.NORENEW_REASON, B.INTEREST_MODE, 
			B.ORGAN_CODE, B.CHARGE_YEAR, B.EXTRA_PREM_AF, B.POLICY_ID, B.INITIAL_AMOUNT, B.CC_SA, 
			B.INITIAL_EXTRA_PREM_AF, B.PAYOUT_RATE, B.AMOUNT, B.PAIDUP_DATE, B.PAY_YEAR, B.EXPIRY_DATE, 
			B.PAY_PERIOD, B.LIABILITY_STATE, B.COUNT_WAY, B.POLICY_CODE, B.IS_GIFT, B.RERINSTATE_DATE, B.ADDITIONAL_PREM_AF, 
			B.RENEW_DECISION, B.VALIDATE_DATE, B.BONUS_MODE, B.BENEFIT_LEVEL, B.PRODUCT_ID, 
			B.BONUS_SA, B.PRODUCT_CODE, B.APPLY_DATE, B.INTEREST_FLAG, B.COVERAGE_PERIOD, B.ITEM_ID, B.RENEWAL_EXTRA_PREM_AF, 
			B.WAIVER_END, B.MATURITY_DATE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.HEALTH_SERVICE_FLAG, B.LAPSE_DATE, 
			B.PAY_FREQ, B.UNIT, B.COVERAGE_YEAR, B.END_CAUSE, B.LAPSE_CAUSE, B.RENEWAL_DISCNTED_PREM_AF, 
			B.TOTAL_PREM_AF, B.DECISION_CODE, B.PAUSE_DATE, B.LOG_ID, B.CHARGE_PERIOD, B.STD_PREM_AF, B.IS_MASTER_ITEM, 
			B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.DEDUCTIBLE_FRANCHISE, B.LOG_TYPE, B.WAIVER_START, B.PREM_FREQ, B.INITIAL_DISCNT_PREM_AF , B.BONUS_W_MODE,B.LAST_BONUS_DATE,B.TAX_EXTENSION_PREM_RATE,B.DISCOUNT_TYPE,B.DISCOUNT_RATE FROM (
					SELECT ROWNUM RN, A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.APPLY_CODE, A.NORENEW_REASON, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, 
			A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.WAIVER_END, A.MATURITY_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.IS_MASTER_ITEM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.LOG_TYPE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF , A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG A WHERE ROWNUM <= #{LESS_NUM}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
		<select id="findNBIrregularPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
			  select t.irregular_prem
    from dev_pas.t_contract_product_log t
   where t.policy_code = #{policy_code}
     and rownum = 1
   order by t.insert_timestamp
			]]>
	</select>
	
</mapper>
