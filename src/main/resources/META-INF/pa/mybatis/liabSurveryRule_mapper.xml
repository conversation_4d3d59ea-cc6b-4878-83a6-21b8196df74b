<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ILiabSurveryRuleDao">


	<select id="findRuleListByLiab" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[SELECT A.LIST_ID,A.LIAB_ID,A.RULE_CODE,A.STATUS FROM APP___PAS__DBUSER.T_LIAB_SURVERY_RULE A 
			WHERE 1=1
			AND A.LIAB_ID=#{liab_id} ]]>
		<if test=" rule_code != null and rule_code != ''  "><![CDATA[ AND A.RULE_CODE = #{rule_code} ]]></if>	
	</select>
	
	

</mapper>
