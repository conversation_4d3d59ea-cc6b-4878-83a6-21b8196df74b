<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.CsLoanReasonDaoImpl">

<sql id="csLoanReasonWhereCondition">
		<if test=" code != null and code != ''  "><![CDATA[ AND A.CODE = #{code} ]]></if>
		<if test=" name != null and name != ''  "><![CDATA[ AND A.NAME = #{name} ]]></if>
		<if test=" flag != null and flag != ''  "><![CDATA[ AND A.CODE != '99' ]]></if>				
	</sql>
	
	<!-- 添加操作 -->
	<insert id="addLoanReason" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<!-- <selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="policy_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_MASTER__POLICY_ID.nextval from dual
		</selectKey> -->
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_LOAN_REASON(CODE,NAME)
			VALUES (
			    #{code,jdbcType=VARCHAR},
			    #{name, jdbcType=VARCHAR} 
				) 
		 ]]>
	</insert>
	
	<!-- 删除操作 -->
	<delete id="deleteLoanReason" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_LOAN_REASON WHERE CODE=#{code,jdbcType=VARCHAR} ]]>
	</delete>
	
	<!-- 修改操作 -->
	<update id="updateLoanReason" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LOAN_REASON ]]>
		<set>
			<trim suffixOverrides=",">
			CODE = #{code, jdbcType=VARCHAR} ,
			NAME = #{name, jdbcType=VARCHAR} 
		</trim>
		</set>
		<![CDATA[ WHERE  CODE = #{code, jdbcType=VARCHAR} ]]>
	</update>
	
	<!-- 按查询操作 ALM24876-->
	<select id="findLoanReasonByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CODE, A.NAME
		  FROM APP___PAS__DBUSER.T_LOAN_REASON A WHERE 1=1 ]]>
		<include refid="csLoanReasonWhereCondition" />
		<![CDATA[AND ROWNUM <1000 ]]>
	</select>
</mapper>