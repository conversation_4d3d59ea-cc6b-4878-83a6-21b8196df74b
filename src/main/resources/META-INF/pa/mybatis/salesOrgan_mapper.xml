<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.SalesOrganPO">

	<sql id="salesOrganWhereCondition">
		<if test=" state  != null "><![CDATA[ AND A.STATE = #{state} ]]></if>
		<if test=" parent_code != null and parent_code != ''  "><![CDATA[ AND A.PARENT_CODE = #{parent_code} ]]></if>
		<if test=" branch_attr != null and branch_attr != ''  "><![CDATA[ AND A.BRANCH_ATTR = #{branch_attr} ]]></if>
		<if test=" organ_level_code  != null "><![CDATA[ AND A.ORGAN_LEVEL_CODE = #{organ_level_code} ]]></if>
		<if test=" organ_type != null and organ_type != ''  "><![CDATA[ AND A.ORGAN_TYPE = #{organ_type} ]]></if>
		<if test=" sales_organ_name != null and sales_organ_name != ''  "><![CDATA[ AND A.SALES_ORGAN_NAME = #{sales_organ_name} ]]></if>
		<if test=" sales_organ_code != null and sales_organ_code != ''  "><![CDATA[ AND A.SALES_ORGAN_CODE = #{sales_organ_code} ]]></if>
		<if test=" admin_organ_code != null and admin_organ_code != ''  "><![CDATA[ AND A.ADMIN_ORGAN_CODE = #{admin_organ_code} ]]></if>
 	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="querySalesOrganBySalesOrganCodeCondition">
		<if test=" sales_organ_code != null and sales_organ_code != '' "><![CDATA[ AND A.SALES_ORGAN_CODE = #{sales_organ_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSalesOrgan"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SALES_ORGAN(
				STATE, PARENT_CODE, INSERT_TIME, BRANCH_ATTR, UPDATE_TIME, INSERT_TIMESTAMP, UPDATE_BY, 
				ORGAN_LEVEL_CODE, ORGAN_TYPE, UPDATE_TIMESTAMP, SALES_ORGAN_NAME, SALES_ORGAN_CODE, INSERT_BY, ADMIN_ORGAN_CODE ) 
			VALUES (
				#{state, jdbcType=NUMERIC}, #{parent_code, jdbcType=VARCHAR} , SYSDATE , #{branch_attr, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} 
				, #{organ_level_code, jdbcType=NUMERIC} , #{organ_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{sales_organ_name, jdbcType=VARCHAR} , #{sales_organ_code, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{admin_organ_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSalesOrgan" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SALES_ORGAN WHERE SALES_ORGAN_CODE = #{sales_organ_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSalesOrgan" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SALES_ORGAN ]]>
		<set>
		<trim suffixOverrides=",">
		    STATE = #{state, jdbcType=NUMERIC} ,
			PARENT_CODE = #{parent_code, jdbcType=VARCHAR} ,
			BRANCH_ATTR = #{branch_attr, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    ORGAN_LEVEL_CODE = #{organ_level_code, jdbcType=NUMERIC} ,
			ORGAN_TYPE = #{organ_type, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			SALES_ORGAN_NAME = #{sales_organ_name, jdbcType=VARCHAR} ,
			ADMIN_ORGAN_CODE = #{admin_organ_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE SALES_ORGAN_CODE = #{sales_organ_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSalesOrganBySalesOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATE, A.PARENT_CODE, A.BRANCH_ATTR, 
			A.ORGAN_LEVEL_CODE, A.ORGAN_TYPE, A.SALES_ORGAN_NAME, A.SALES_ORGAN_CODE, A.ADMIN_ORGAN_CODE FROM APP___PAS__DBUSER.T_SALES_ORGAN A WHERE 1 = 1  ]]>
		<include refid="querySalesOrganBySalesOrganCodeCondition" />
		<![CDATA[ ORDER BY A.SALES_ORGAN_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSalesOrgan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATE, A.PARENT_CODE, A.BRANCH_ATTR, 
			A.ORGAN_LEVEL_CODE, A.ORGAN_TYPE, A.SALES_ORGAN_NAME, A.SALES_ORGAN_CODE, A.ADMIN_ORGAN_CODE FROM APP___PAS__DBUSER.T_SALES_ORGAN A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALES_ORGAN_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSalesOrgan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATE, A.PARENT_CODE, A.BRANCH_ATTR, 
			A.ORGAN_LEVEL_CODE, A.ORGAN_TYPE, A.SALES_ORGAN_NAME, A.SALES_ORGAN_CODE, A.ADMIN_ORGAN_CODE FROM APP___PAS__DBUSER.T_SALES_ORGAN A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALES_ORGAN_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSalesOrganTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SALES_ORGAN A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="querySalesOrganForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.STATE, B.PARENT_CODE, B.BRANCH_ATTR, 
			B.ORGAN_LEVEL_CODE, B.ORGAN_TYPE, B.SALES_ORGAN_NAME, B.SALES_ORGAN_CODE, B.ADMIN_ORGAN_CODE FROM (
					SELECT ROWNUM RN, A.STATE, A.PARENT_CODE, A.BRANCH_ATTR, 
			A.ORGAN_LEVEL_CODE, A.ORGAN_TYPE, A.SALES_ORGAN_NAME, A.SALES_ORGAN_CODE, A.ADMIN_ORGAN_CODE FROM APP___PAS__DBUSER.T_SALES_ORGAN A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALES_ORGAN_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
