<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IDayEndReconciliationDao">

<!-- <sql id="PA_dayEndReconciliation">
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" fee_amount  != null and fee_amount !='' "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code !='' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	 -->
	<sql id="PA_dayEndReconciliation">
		<if test=" apply_date  != null and apply_date !='' "><![CDATA[ AND A.apply_date = #{apply_date} ]]></if>
	    <if test=" service_bank  != null and service_bank !='' "><![CDATA[ AND A.service_bank = #{service_bank} ]]></if>
	    <if test=" service_bank_branch  != null and service_bank_branch !='' "><![CDATA[ AND A.service_bank_branch = #{service_bank_branch} ]]></if>
	</sql>
		<!-- 查询操作 -->
	<select id="PA_querydayEndReconciliationByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CODE,B.FEE_AMOUNT,C.BUSI_PROD_CODE
		 	FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,APP___PAS__DBUSER.T_PREM_ARAP B,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
         	WHERE A.POLICY_CODE = B.POLICY_CODE AND A.POLICY_CODE = C.POLICY_CODE  ]]>
		<include refid="PA_dayEndReconciliation" />
	</select>
</mapper>