<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRenewalFeeInfoQueryDao">
	<select id="RenewalFeeInfoQueryDaoquery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
            select distinct t.validate_date,
                   t.policy_code policy_code,
			       to_char(t.validate_date,'yyyy-mm-dd') valid_date,
			       t.organ_code organ_code,
			       '有效' policy_state,
			       c.customer_name app_name,
			       c.old_customer_id app_cust_no,
			       to_char(c.customer_gender) app_gender,
			       to_char(c.customer_birthday,'yyyy-mm-dd') app_birthday,
			       c.customer_cert_type app_certi_type_code,
			       (select cty.type
			          from dev_pas.t_certi_type cty
			         where cty.code = c.customer_cert_type) app_certi_type_name,
			       c.customer_certi_code app_certi_code,
			       to_char(c.cust_cert_star_date,'yyyy-mm-dd') app_certi_start_date,
			       to_char(c.cust_cert_end_date,'yyyy-mm-dd') app_certi_end_date,
			       (select tc.country_name from dev_pas.t_country tc where tc.country_code = c.country_code) app_country,
			       a.mobile_tel app_mobile,
			       a.fixed_tel app_phone,
			       ph.job_code app_job_cat_code,
			       (select j.job_name from dev_pas.t_job_code j where j.job_code = ph.job_code) app_job_cat_name,
			       c.company_name app_company_name,
			       a.town_street_code app_town_street_code,
                   a.town_street_name app_town_street_name,
			       a.state app_state,
			       a.city app_city,
			       a.district app_district_code,
			       (select td.name from dev_pas.T_DISTRICT td where td.code = a.state) app_state,
			       (select td.name from dev_pas.T_DISTRICT td where td.code = a.city) app_city,
			       (select td.name from dev_pas.T_DISTRICT td where td.code = a.district) app_district,
			       a.address app_address,
			       (select i.relation_to_ph from dev_pas.t_insured_list i where i.policy_id = t.policy_id and rownum = 1) rela_to_ins_code,
			       (select (select lpr.relation_name from dev_pas.t_la_ph_rela lpr 
			                 where lpr.relation_code = i.relation_to_ph) 
			          from dev_pas.t_insured_list i where i.policy_id = t.policy_id and rownum = 1) rela_to_ins_name,
			       (select pm.name from dev_pas.t_pay_mode pm where pm.code = p.pay_next) pay_mode,
			       p.next_account_name acc_name,
			       p.next_account_bank acc_bank_code,
			       (select bank_name from dev_pas.T_BANK where bank_code=p.next_account_bank) acc_bank_name,
			       p.next_account account,
			       t.liability_state liabilityState1,
			       (select z.cause_name from app___pas__dbuser.t_end_cause z where z.cause_code = t.end_cause) as end_cause_name,
			       (select z.status_name from app___pas__dbuser.t_liability_status z where z.status_code = t.liability_state) as liability_name,
			       (select z.cause_desc from app___pas__dbuser.t_lapse_cause z where z.cause_code = t.lapse_cause) as lapse_cause_name,
			       t.special_account_flag,
			       t.multi_mainrisk_flag,
			       c.LIVE_STATUS
			  from dev_pas.t_contract_master t
			  join dev_pas.t_policy_holder ph
			    on ph.policy_id = t.policy_id
			  join dev_pas.t_customer c
			    on c.customer_id = ph.customer_id
			  join dev_pas.t_address a
			    on a.address_id = ph.address_id
			  join dev_pas.t_payer_account p
			    on p.policy_id = t.policy_id
			  join dev_pas.t_insured_list isl
			    on isl.policy_id = t.policy_id
			  join dev_pas.t_customer isc
			    on isc.customer_id = isl.customer_id
			 where 
			   t.liability_state = '1'
			   
			   and ((c.customer_name = #{app_name}
			   and c.customer_gender = #{app_gender}
			   and c.customer_birthday = to_date(#{app_birthday},'yyyy-mm-dd')
			   and c.customer_cert_type = #{app_certi_type_code}
			   and c.customer_certi_code = #{app_certi_code})
			   or (isc.customer_name = #{app_name}
			   and isc.customer_gender = #{app_gender}
			   and isc.customer_birthday = to_date(#{app_birthday},'yyyy-mm-dd')
			   and isc.customer_cert_type = #{app_certi_type_code}
			   and isc.customer_certi_code = #{app_certi_code}))
		 ]]>	
	    <![CDATA[AND NOT EXISTS
				 (SELECT 1
					FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
				   WHERE M.POLICY_CODE = CBP.POLICY_CODE
					 AND M.POLICY_CODE = t.POLICY_CODE
					 AND CBP.BUSI_PROD_CODE IN 
						 (SELECT CI.CONSTANTS_VALUE
							FROM DEV_PAS.T_CONSTANTS_INFO CI
						   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		<![CDATA[ order by t.validate_date desc]]>
	</select>
	<select id="RenewalFeeInfoQueryDaoqueryInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select isc.old_customer_id ins_cust_no,
			       isc.customer_name ins_name,
			       to_char(isc.customer_gender) ins_gender,
			       to_char(isc.customer_birthday,'yyyy-mm-dd') ins_birthday,
			       isc.customer_cert_type ins_certi_type_code,
			       (select cty.type
			          from dev_pas.t_certi_type cty
			         where cty.code = isc.customer_cert_type) ins_certi_type_name,
			       isc.customer_certi_code ins_certi_code,
			       isl.relation_to_ph rela_to_ph_code,
			       (select lpr.relation_name from dev_pas.t_la_ph_rela lpr 
			         where lpr.relation_code = isl.relation_to_ph) rela_to_ph_name,
			       (SELECT TBD.ORDER_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED TBD WHERE  TBD.POLICY_CODE=isl.POLICY_CODE 
      AND TBD.INSURED_ID = isl.LIST_ID  AND ROWNUM = 1 ) AS ORDER_ID,
      				isc.LIVE_STATUS,
      				isc.CUSTOMER_ID
			  from dev_pas.t_contract_master t
			  join dev_pas.t_insured_list isl
			    on isl.policy_id = t.policy_id
			  join dev_pas.t_customer isc
			    on isc.customer_id = isl.customer_id
			 where 1=1
		]]>
		<if test="policy_code != null and policy_code != ''"><![CDATA[ and t.policy_code = #{policy_code} ]]></if>
	</select>
	
	<!--114390  通过保单号查询所有主险险种 -->
	<select id="RenewalFeeInfoQueryMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TCBP.POLICY_CODE,
			 TCBP.MASTER_BUSI_ITEM_ID,
             TCBP.LIABILITY_STATE,
             (SELECT TLS.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS TLS WHERE TLS.STATUS_CODE = TCBP.LIABILITY_STATE) LIABLIITY_NAME,
             TCBP.END_CAUSE,
             TBP.PRODUCT_ABBR_NAME,
             TCBP.BUSI_ITEM_ID,
             TCBP.BUSI_PROD_CODE,
             TBP.TAX_EXTENSION_FLAG,
             (SELECT 1
			  FROM DEV_PDS.T_BUSINESS_PRODUCT_SERVICE BPS
			 WHERE BPS.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
			   AND BPS.SERVICE_CODE = 'PC') AS SERVICE_CODE /*交费信息变更*/
        FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
             APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
      WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
        AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
			AND TCBP.POLICY_CODE=#{policy_code} 
		ORDER BY TCBP.ORDER_ID,TCBP.BUSI_ITEM_ID DESC
		]]>
	</select>

	<!--114390  通过保单号查询所有附加险险种 -->
	<select id="RenewalFeeInfofindAllContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT  TCBP.MASTER_BUSI_ITEM_ID,
             TCBP.LIABILITY_STATE,
             (SELECT TLS.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS TLS WHERE TLS.STATUS_CODE = TCBP.LIABILITY_STATE) LIABLIITY_NAME,
             TCBP.END_CAUSE,
             TBP.PRODUCT_ABBR_NAME,
             TCBP.BUSI_ITEM_ID,
             TCBP.BUSI_PROD_CODE,
             (SELECT 1
			  FROM DEV_PDS.T_BUSINESS_PRODUCT_SERVICE BPS
			 WHERE BPS.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
			   AND BPS.SERVICE_CODE = 'PC') AS SERVICE_CODE /*交费信息变更*/
        FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
             APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
      WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
        AND TCBP.MASTER_BUSI_ITEM_ID =#{master_busi_item_id} 
			AND TCBP.POLICY_CODE=#{policy_code} 
		ORDER BY TCBP.ORDER_ID,TCBP.BUSI_ITEM_ID DESC
		]]>
	</select>
</mapper>
