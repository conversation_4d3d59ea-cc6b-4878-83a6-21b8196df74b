<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cash">
<!--
	<sql id="PA_cashWhereCondition">
		<if test=" reserved_field3 != null and reserved_field3 != ''  "><![CDATA[ AND A.RESERVED_FIELD3 = #{reserved_field3} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" reserved_field4 != null and reserved_field4 != ''  "><![CDATA[ AND A.RESERVED_FIELD4 = #{reserved_field4} ]]></if>
		<if test=" cash_fee_id  != null "><![CDATA[ AND A.CASH_FEE_ID = #{cash_fee_id} ]]></if>
		<if test=" reserved_field5 != null and reserved_field5 != ''  "><![CDATA[ AND A.RESERVED_FIELD5 = #{reserved_field5} ]]></if>
		<if test=" reserved_field6 != null and reserved_field6 != ''  "><![CDATA[ AND A.RESERVED_FIELD6 = #{reserved_field6} ]]></if>
		<if test=" reserved_field7 != null and reserved_field7 != ''  "><![CDATA[ AND A.RESERVED_FIELD7 = #{reserved_field7} ]]></if>
		<if test=" reserved_field8 != null and reserved_field8 != ''  "><![CDATA[ AND A.RESERVED_FIELD8 = #{reserved_field8} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" reserved_field9 != null and reserved_field9 != ''  "><![CDATA[ AND A.RESERVED_FIELD9 = #{reserved_field9} ]]></if>
		<if test=" check_enter_time  != null  and  check_enter_time  != ''  "><![CDATA[ AND A.CHECK_ENTER_TIME = #{check_enter_time} ]]></if>
		<if test=" unit_number  != null "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" reserved_field10 != null and reserved_field10 != ''  "><![CDATA[ AND A.RESERVED_FIELD10 = #{reserved_field10} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" prem_purpose != null and prem_purpose != ''  "><![CDATA[ AND A.PREM_PURPOSE = #{prem_purpose} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" busi_prod_pack != null and busi_prod_pack != ''  "><![CDATA[ AND A.BUSI_PROD_PACK = #{busi_prod_pack} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" reserved_field2 != null and reserved_field2 != ''  "><![CDATA[ AND A.RESERVED_FIELD2 = #{reserved_field2} ]]></if>
		<if test=" reserved_field1 != null and reserved_field1 != ''  "><![CDATA[ AND A.RESERVED_FIELD1 = #{reserved_field1} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != ''  "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		<if test=" pay_collection_indi != null and pay_collection_indi != ''  "><![CDATA[ AND A.PAY_COLLECTION_INDI = #{pay_collection_indi} ]]></if>
		<if test=" payee_id  != null "><![CDATA[ AND A.PAYEE_ID = #{payee_id} ]]></if>
		<if test=" fee_status  != null "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" check_no != null and check_no != ''  "><![CDATA[ AND A.CHECK_NO = #{check_no} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" cs_accept_code != null and cs_accept_code != ''  "><![CDATA[ AND A.CS_ACCEPT_CODE = #{cs_accept_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryCashByCashFeeIdCondition">
		<if test=" cash_fee_id  != null "><![CDATA[ AND A.CASH_FEE_ID = #{cash_fee_id} ]]></if>
	</sql>	
	<sql id="PA_queryCashByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>	
	<sql id="PA_queryCashByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="PA_queryCashByFinishTimeCondition">
		<if test=" finish_time  != null "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addCash"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CASH(
				RESERVED_FIELD3, MONEY_CODE, RELATED_ID, RESERVED_FIELD4, CASH_FEE_ID, RESERVED_FIELD5, RESERVED_FIELD6, 
				RESERVED_FIELD7, RESERVED_FIELD8, ACCOUNT_ID, RESERVED_FIELD9, CHECK_ENTER_TIME, UNIT_NUMBER, FINISH_TIME, 
				CASE_NO, FEE_TYPE, RESERVED_FIELD10, APPLY_CODE, PREM_PURPOSE, INSERT_TIMESTAMP, ORGAN_CODE, 
				BUSI_PROD_PACK, UPDATE_BY, FEE_AMOUNT, POLICY_CHG_ID, POLICY_ID, RESERVED_FIELD2, RESERVED_FIELD1, 
				WITHDRAW_TYPE, PAY_COLLECTION_INDI, PAYEE_ID, INSERT_TIME, FEE_STATUS, UPDATE_TIME, CHECK_NO, 
				CASE_ID, CS_ACCEPT_CODE, POLICY_CODE, PAY_MODE, UPDATE_TIMESTAMP, INSERT_BY, AGENT_CODE ) 
			VALUES (
				#{reserved_field3, jdbcType=VARCHAR}, #{money_code, jdbcType=VARCHAR} , #{related_id, jdbcType=NUMERIC} , #{reserved_field4, jdbcType=VARCHAR} , #{cash_fee_id, jdbcType=NUMERIC} , #{reserved_field5, jdbcType=VARCHAR} , #{reserved_field6, jdbcType=VARCHAR} 
				, #{reserved_field7, jdbcType=VARCHAR} , #{reserved_field8, jdbcType=VARCHAR} , #{account_id, jdbcType=NUMERIC} , #{reserved_field9, jdbcType=VARCHAR} , #{check_enter_time, jdbcType=DATE} , #{unit_number, jdbcType=NUMERIC} , #{finish_time, jdbcType=DATE} 
				, #{case_no, jdbcType=VARCHAR} , #{fee_type, jdbcType=VARCHAR} , #{reserved_field10, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{prem_purpose, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} 
				, #{busi_prod_pack, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{reserved_field2, jdbcType=VARCHAR} , #{reserved_field1, jdbcType=VARCHAR} 
				, #{withdraw_type, jdbcType=VARCHAR} , #{pay_collection_indi, jdbcType=VARCHAR} , #{payee_id, jdbcType=NUMERIC} , SYSDATE , #{fee_status, jdbcType=NUMERIC} , SYSDATE , #{check_no, jdbcType=VARCHAR} 
				, #{case_id, jdbcType=NUMERIC} , #{cs_accept_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{agent_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteCash" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CASH WHERE 1 = 1 ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateCash" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CASH ]]>
		<set>
		<trim suffixOverrides=",">
			RESERVED_FIELD3 = #{reserved_field3, jdbcType=VARCHAR} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
			RESERVED_FIELD4 = #{reserved_field4, jdbcType=VARCHAR} ,
		    CASH_FEE_ID = #{cash_fee_id, jdbcType=NUMERIC} ,
			RESERVED_FIELD5 = #{reserved_field5, jdbcType=VARCHAR} ,
			RESERVED_FIELD6 = #{reserved_field6, jdbcType=VARCHAR} ,
			RESERVED_FIELD7 = #{reserved_field7, jdbcType=VARCHAR} ,
			RESERVED_FIELD8 = #{reserved_field8, jdbcType=VARCHAR} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
			RESERVED_FIELD9 = #{reserved_field9, jdbcType=VARCHAR} ,
		    CHECK_ENTER_TIME = #{check_enter_time, jdbcType=DATE} ,
		    UNIT_NUMBER = #{unit_number, jdbcType=NUMERIC} ,
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
			FEE_TYPE = #{fee_type, jdbcType=VARCHAR} ,
			RESERVED_FIELD10 = #{reserved_field10, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			PREM_PURPOSE = #{prem_purpose, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			BUSI_PROD_PACK = #{busi_prod_pack, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			RESERVED_FIELD2 = #{reserved_field2, jdbcType=VARCHAR} ,
			RESERVED_FIELD1 = #{reserved_field1, jdbcType=VARCHAR} ,
			WITHDRAW_TYPE = #{withdraw_type, jdbcType=VARCHAR} ,
			PAY_COLLECTION_INDI = #{pay_collection_indi, jdbcType=VARCHAR} ,
		    PAYEE_ID = #{payee_id, jdbcType=NUMERIC} ,
		    FEE_STATUS = #{fee_status, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			CHECK_NO = #{check_no, jdbcType=VARCHAR} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			CS_ACCEPT_CODE = #{cs_accept_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findCashByCashFeeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.MONEY_CODE, A.RELATED_ID, A.RESERVED_FIELD4, A.CASH_FEE_ID, A.RESERVED_FIELD5, A.RESERVED_FIELD6, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.ACCOUNT_ID, A.RESERVED_FIELD9, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, A.FINISH_TIME, 
			A.CASE_NO, A.FEE_TYPE, A.RESERVED_FIELD10, A.APPLY_CODE, A.PREM_PURPOSE, A.ORGAN_CODE, 
			A.BUSI_PROD_PACK, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, 
			A.WITHDRAW_TYPE, A.PAY_COLLECTION_INDI, A.PAYEE_ID, A.FEE_STATUS, A.CHECK_NO, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.PAY_MODE, A.AGENT_CODE FROM APP___PAS__DBUSER.T_CASH A WHERE 1 = 1  ]]>
		<include refid="PA_queryCashByCashFeeIdCondition" />
	</select>
	
	<select id="PA_findCashByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.MONEY_CODE, A.RELATED_ID, A.RESERVED_FIELD4, A.CASH_FEE_ID, A.RESERVED_FIELD5, A.RESERVED_FIELD6, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.ACCOUNT_ID, A.RESERVED_FIELD9, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, A.FINISH_TIME, 
			A.CASE_NO, A.FEE_TYPE, A.RESERVED_FIELD10, A.APPLY_CODE, A.PREM_PURPOSE, A.ORGAN_CODE, 
			A.BUSI_PROD_PACK, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, 
			A.WITHDRAW_TYPE, A.PAY_COLLECTION_INDI, A.PAYEE_ID, A.FEE_STATUS, A.CHECK_NO, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.PAY_MODE, A.AGENT_CODE FROM APP___PAS__DBUSER.T_CASH A WHERE 1 = 1  ]]>
		<include refid="PA_queryCashByOrganCodeCondition" />
	</select>
	
	<select id="PA_findCashByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.MONEY_CODE, A.RELATED_ID, A.RESERVED_FIELD4, A.CASH_FEE_ID, A.RESERVED_FIELD5, A.RESERVED_FIELD6, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.ACCOUNT_ID, A.RESERVED_FIELD9, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, A.FINISH_TIME, 
			A.CASE_NO, A.FEE_TYPE, A.RESERVED_FIELD10, A.APPLY_CODE, A.PREM_PURPOSE, A.ORGAN_CODE, 
			A.BUSI_PROD_PACK, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, 
			A.WITHDRAW_TYPE, A.PAY_COLLECTION_INDI, A.PAYEE_ID, A.FEE_STATUS, A.CHECK_NO, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.PAY_MODE, A.AGENT_CODE FROM APP___PAS__DBUSER.T_CASH A WHERE 1 = 1  ]]>
		<include refid="PA_queryCashByApplyCodeCondition" />
	</select>
	
	<select id="PA_findCashByFinishTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.MONEY_CODE, A.RELATED_ID, A.RESERVED_FIELD4, A.CASH_FEE_ID, A.RESERVED_FIELD5, A.RESERVED_FIELD6, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.ACCOUNT_ID, A.RESERVED_FIELD9, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, A.FINISH_TIME, 
			A.CASE_NO, A.FEE_TYPE, A.RESERVED_FIELD10, A.APPLY_CODE, A.PREM_PURPOSE, A.ORGAN_CODE, 
			A.BUSI_PROD_PACK, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, 
			A.WITHDRAW_TYPE, A.PAY_COLLECTION_INDI, A.PAYEE_ID, A.FEE_STATUS, A.CHECK_NO, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.PAY_MODE, A.AGENT_CODE FROM APP___PAS__DBUSER.T_CASH A WHERE 1 = 1  ]]>
		<include refid="PA_queryCashByFinishTimeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapCash" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.MONEY_CODE, A.RELATED_ID, A.RESERVED_FIELD4, A.CASH_FEE_ID, A.RESERVED_FIELD5, A.RESERVED_FIELD6, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.ACCOUNT_ID, A.RESERVED_FIELD9, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, A.FINISH_TIME, 
			A.CASE_NO, A.FEE_TYPE, A.RESERVED_FIELD10, A.APPLY_CODE, A.PREM_PURPOSE, A.ORGAN_CODE, 
			A.BUSI_PROD_PACK, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, 
			A.WITHDRAW_TYPE, A.PAY_COLLECTION_INDI, A.PAYEE_ID, A.FEE_STATUS, A.CHECK_NO, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.PAY_MODE, A.AGENT_CODE FROM APP___PAS__DBUSER.T_CASH A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllCash" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.MONEY_CODE, A.RELATED_ID, A.RESERVED_FIELD4, A.CASH_FEE_ID, A.RESERVED_FIELD5, A.RESERVED_FIELD6, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.ACCOUNT_ID, A.RESERVED_FIELD9, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, A.FINISH_TIME, 
			A.CASE_NO, A.FEE_TYPE, A.RESERVED_FIELD10, A.APPLY_CODE, A.PREM_PURPOSE, A.ORGAN_CODE, 
			A.BUSI_PROD_PACK, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, 
			A.WITHDRAW_TYPE, A.PAY_COLLECTION_INDI, A.PAYEE_ID, A.FEE_STATUS, A.CHECK_NO, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.PAY_MODE, A.AGENT_CODE FROM APP___PAS__DBUSER.T_CASH A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->	
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findCashTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CASH A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryCashForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RESERVED_FIELD3, B.MONEY_CODE, B.RELATED_ID, B.RESERVED_FIELD4, B.CASH_FEE_ID, B.RESERVED_FIELD5, B.RESERVED_FIELD6, 
			B.RESERVED_FIELD7, B.RESERVED_FIELD8, B.ACCOUNT_ID, B.RESERVED_FIELD9, B.CHECK_ENTER_TIME, B.UNIT_NUMBER, B.FINISH_TIME, 
			B.CASE_NO, B.FEE_TYPE, B.RESERVED_FIELD10, B.APPLY_CODE, B.PREM_PURPOSE, B.ORGAN_CODE, 
			B.BUSI_PROD_PACK, B.FEE_AMOUNT, B.POLICY_CHG_ID, B.POLICY_ID, B.RESERVED_FIELD2, B.RESERVED_FIELD1, 
			B.WITHDRAW_TYPE, B.PAY_COLLECTION_INDI, B.PAYEE_ID, B.FEE_STATUS, B.CHECK_NO, 
			B.CASE_ID, B.CS_ACCEPT_CODE, B.POLICY_CODE, B.PAY_MODE, B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.RESERVED_FIELD3, A.MONEY_CODE, A.RELATED_ID, A.RESERVED_FIELD4, A.CASH_FEE_ID, A.RESERVED_FIELD5, A.RESERVED_FIELD6, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.ACCOUNT_ID, A.RESERVED_FIELD9, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, A.FINISH_TIME, 
			A.CASE_NO, A.FEE_TYPE, A.RESERVED_FIELD10, A.APPLY_CODE, A.PREM_PURPOSE, A.ORGAN_CODE, 
			A.BUSI_PROD_PACK, A.FEE_AMOUNT, A.POLICY_CHG_ID, A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, 
			A.WITHDRAW_TYPE, A.PAY_COLLECTION_INDI, A.PAYEE_ID, A.FEE_STATUS, A.CHECK_NO, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.PAY_MODE, A.AGENT_CODE FROM APP___PAS__DBUSER.T_CASH A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
