<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="IsHesitationPeriod">
	<select id="queryisexitpolicy" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT ROWNUM,TPA.ACKNOWLEDGE_DATE,TCBP.VALIDATE_DATE,TCBP.HESITATION_PERIOD_DAY,TCBP.POLICY_CODE FROM 
   DEV_PAS.T_CONTRACT_MASTER TCM ,DEV_PAS.T_CONTRACT_BUSI_PROD TCBP ,DEV_PAS.T_INSURED_LIST TIL,DEV_PAS.T_POLICY_ACKNOWLEDGEMENT TPA
   WHERE TCM.POLICY_CODE = TCBP.POLICY_CODE
   AND TIL.POLICY_CODE = TCBP.POLICY_CODE
   AND TIL.POLICY_CODE = TCM.POLICY_CODE
   AND TPA.POLICY_ID = TCM.POLICY_ID
   AND TPA.POLICY_ID = TCBP.POLICY_ID
   AND TPA.POLICY_ID = TIL.POLICY_ID
   AND TCM.LIABILITY_STATE=1
   <if test="customer_id !=null and customer_id !='' "><![CDATA[ AND TIL.CUSTOMER_ID = #{customer_id}]]></if>
   <if test="batch_date !=null and batch_date !='' "> <![CDATA[ AND TPA.ACKNOWLEDGE_DATE+TCBP.HESITATION_PERIOD_DAY < #{batch_date,jdbcType=DATE}]]></if>
	</select>

<select id="queryAgentIsExit" resultType="java.util.Map" parameterType="java.util.Map">
			   SELECT ROWNUM,TCM.POLICY_CODE, TCM.VALIDATE_DATE, TCA.AGENT_CODE
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM, DEV_PAS.T_CONTRACT_AGENT TCA
			 WHERE TCM.POLICY_ID = TCA.POLICY_ID
			   AND (TCM.LIABILITY_STATE = 1 OR TCM.LIABILITY_STATE = 3 AND TCM.END_CAUSE = '02')
			   AND TCA.IS_NB_AGENT = '1'
   <if test="agent_code !=null and agent_code !='' "><![CDATA[  AND TCA.AGENT_CODE = #{agent_code}]]></if>
   <if test="max_date !=null and max_date !='' "> <![CDATA[ AND TCM.VALIDATE_DATE <=  #{max_date,jdbcType=DATE}]]></if>
   <if test="min_date !=null and min_date !='' "> <![CDATA[ AND TCM.VALIDATE_DATE >=  #{min_date,jdbcType=DATE}]]></if>
	</select>
	
</mapper>