<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ContractAgentDaoImpl">

	<sql id="contractAgentWhereCondition">
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" agent_start_date  != null  and  agent_start_date  != ''  "><![CDATA[ AND A.AGENT_START_DATE = #{agent_start_date} ]]></if>
		<if test=" agent_mobile != null and agent_mobile != ''  "><![CDATA[ AND A.AGENT_MOBILE = #{agent_mobile} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" agent_type  != null "><![CDATA[ AND A.AGENT_TYPE = #{agent_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" agent_end_date  != null  and  agent_end_date  != ''  "><![CDATA[ AND A.AGENT_END_DATE = #{agent_end_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" agent_organ_code  != null "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" is_current_agent != null and is_current_agent != ''  "><![CDATA[ AND A.IS_CURRENT_AGENT = #{is_current_agent} ]]></if>
		<if test=" is_nb_agent != null and is_nb_agent != ''  "><![CDATA[ AND A.is_nb_agent = #{is_nb_agent} ]]></if>
		<if test=" cooperation_protocol_id != null and cooperation_protocol_id != ''  "><![CDATA[ AND A.COOPERATION_PROTOCOL_ID = #{cooperation_protocol_id} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_ID in (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryContractAgentByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
		
	<sql id="queryContractAgentByPolicyCode">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="queryContractAgentByPolicyId">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>

<!-- 查询当前代理人 -->
    <sql id="queryNowContractAgentByPolicyIdOrCode">
		<![CDATA[ AND A.IS_CURRENT_AGENT='1' ]]>
	</sql>

<!-- 添加操作 -->
	<insert id="addContractAgent"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_AGENT__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_AGENT(
				INSERT_TIME, AGENT_NAME, AGENT_START_DATE, UPDATE_TIME, AGENT_MOBILE, RELATION_TO_PH, AGENT_TYPE, 
				APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, AGENT_END_DATE, LIST_ID, AGENT_ORGAN_CODE, 
				UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, AGENT_CODE ,IS_CURRENT_AGENT, IS_NB_AGENT,LAST_AGENT_CODE,LAST_AGENT_NAME,ORGAN_CODE,CHANNEL_TYPE,COOPERATION_CODE,COOPERATION_NAME,COOPERATION_PROTOCOL_ID) 
			VALUES (
				SYSDATE, #{agent_name, jdbcType=VARCHAR} , #{agent_start_date, jdbcType=DATE} , SYSDATE , #{agent_mobile, jdbcType=VARCHAR} , #{relation_to_ph, jdbcType=VARCHAR} , #{agent_type, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{agent_end_date, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} , #{agent_organ_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{agent_code, jdbcType=VARCHAR} ,#{is_current_agent, jdbcType=NUMERIC} ,#{is_nb_agent, jdbcType=NUMERIC},#{last_agent_code, jdbcType=VARCHAR},#{last_agent_name, jdbcType=VARCHAR}
				,#{organ_code, jdbcType=VARCHAR},#{channel_type, jdbcType=VARCHAR},
				#{cooperation_code,jdbcType=VARCHAR},#{cooperation_name,jdbcType=VARCHAR},#{cooperation_protocol_id,jdbcType=VARCHAR} 
				) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteContractAgent" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateContractAgent" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_AGENT ]]>
		<set>
		<trim suffixOverrides=",">
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
		    AGENT_START_DATE = #{agent_start_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			AGENT_MOBILE = #{agent_mobile, jdbcType=VARCHAR} ,
			RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} ,
		    AGENT_TYPE = #{agent_type, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    AGENT_END_DATE = #{agent_end_date, jdbcType=DATE} ,
		    AGENT_ORGAN_CODE = #{agent_organ_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
			IS_CURRENT_AGENT = #{is_current_agent, jdbcType=NUMERIC} ,
			LAST_AGENT_CODE =  #{last_agent_code, jdbcType=VARCHAR} ,
			LAST_AGENT_NAME = #{last_agent_name, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
			SEND_FLAG = #{send_flag, jdbcType=VARCHAR} ,
			COOPERATION_PROTOCOL_ID = #{cooperation_protocol_id,jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	
	
	<update id="updateContractAgentgx" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_AGENT ]]>
		<set>
		<trim suffixOverrides=",">
		    AGENT_END_DATE = #{agent_end_date, jdbcType=DATE} ,
			IS_CURRENT_AGENT = #{is_current_agent, jdbcType=NUMERIC} ,
			
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findContractAgentByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
			A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT, A.COOPERATION_PROTOCOL_ID FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE 1 = 1  ]]>
		<include refid="queryContractAgentByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,
       A.POLICY_CODE,
       A.AGENT_TYPE,
       A.POLICY_ID,
       A.APPLY_CODE,
       A.AGENT_CODE,
       A.AGENT_NAME,
       A.LAST_AGENT_CODE,
       A.LAST_AGENT_NAME,
       A.RELATION_TO_PH,
       A.AGENT_ORGAN_CODE,
       A.AGENT_MOBILE,
       A.AGENT_START_DATE,
       A.AGENT_END_DATE,
       A.IS_CURRENT_AGENT,
       A.IS_NB_AGENT,     
       A.ORGAN_CODE,
       A.COOPERATION_NAME, 
       A.CHANNEL_TYPE, A.COOPERATION_PROTOCOL_ID  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE ROWNUM <=  1000  ]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
						A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, A.COOPERATION_NAME,
						A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT  ,A.LAST_AGENT_CODE,
       A.LAST_AGENT_NAME,A.ORGAN_CODE,A.CHANNEL_TYPE, A.IS_NB_AGENT,  B.SALES_ORGAN_CODE, A.COOPERATION_PROTOCOL_ID
				FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A,APP___PAS__DBUSER.T_AGENT B 
				WHERE A.AGENT_CODE=B.AGENT_CODE AND  ROWNUM <=  1000  ]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID,A.INSERT_TIME ]]> 
	</select>
	
<!-- 查询自有保单操作 -->
	<select id="findOwnContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
						A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
						A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT  , A.IS_NB_AGENT , b.sales_organ_code,rownum rn
				FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A,APP___PAS__DBUSER.T_AGENT B,APP___PAS__DBUSER.T_CONTRACT_MASTER C
				WHERE A.AGENT_CODE=B.AGENT_CODE AND A.POLICY_ID=C.POLICY_ID AND C.POLICY_FLAG='1' AND ROWNUM < 100000   AND A.AGENT_CODE = #{agent_code} ]]>
		<if test=" is_current_agent != null and is_current_agent != ''  "><![CDATA[ AND A.IS_CURRENT_AGENT = #{is_current_agent} ]]></if> 
		<![CDATA[ ORDER BY A.LIST_ID,A.INSERT_TIME ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findContractAgentTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE 1 = 1  ]]>
		<include refid="contractAgentWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryContractAgentForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.AGENT_NAME, B.AGENT_START_DATE, B.AGENT_MOBILE, B.RELATION_TO_PH, B.AGENT_TYPE, 
			B.APPLY_CODE, B.POLICY_CODE, B.AGENT_END_DATE, B.LIST_ID, B.AGENT_ORGAN_CODE, 
			B.POLICY_ID, B.AGENT_CODE , B.IS_NB_AGENT, B.COOPERATION_PROTOCOL_ID  FROM (
					SELECT ROWNUM RN, A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
			A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT , A.COOPERATION_PROTOCOL_ID FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<select id="findContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM (
			<![CDATA[ SELECT T.AGENT_NAME, A.AGENT_START_DATE, T.AGENT_MOBILE, A.RELATION_TO_PH, T.AGENT_NORMAL_TYPE AS AGENT_TYPE,T.AGENT_LEVEL,
			A.APPLY_CODE, A.POLICY_CODE,A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE, A.IS_CURRENT_AGENT ,A.LAST_AGENT_CODE,
       A.LAST_AGENT_NAME, A.IS_NB_AGENT,A.ORGAN_CODE,A.CHANNEL_TYPE, A.COOPERATION_PROTOCOL_ID  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A ,APP___PAS__DBUSER.T_AGENT T 
	       WHERE A.AGENT_CODE=T.AGENT_CODE  AND A.IS_CURRENT_AGENT=1 ]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY  t.employment_date DESC ]]>
		)WHERE rownum =1
	</select>
	
	<select id="findContractAgentOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM (
			<![CDATA[ SELECT t.AGENT_NAME, t.employment_date as AGENT_START_DATE, t.AGENT_MOBILE, A.RELATION_TO_PH, t.agent_normal_type as AGENT_TYPE,
			A.APPLY_CODE, A.POLICY_CODE,t.dismissal_date as AGENT_END_DATE, A.LIST_ID, a.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE, A.IS_CURRENT_AGENT , A.IS_NB_AGENT, A.COOPERATION_PROTOCOL_ID   FROM APP___PAS__DBUSER.T_CONTRACT_AGENT a ,APP___PAS__DBUSER.t_agent t 
	       where a.agent_code=t.agent_code  and a.IS_CURRENT_AGENT=1 ]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY  t.employment_date DESC ]]>
		)WHERE rownum =1
	</select>
	
	
	<!-- 保单历史服务人员详细信息查询 -->
	<select id="findAllContractHisServicePersonInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select a.agent_code,
          a.agent_name,
          a.agent_type as agenttype,
          b.AGENT_STATUS,
          b.CERT_TYPE,
          b.CERTI_CODE,
          b.BIRTHDAY,
          b.AGENT_LEVEL,
          b.sales_organ_code,
          a.agent_mobile,
          b.agent_mobile as agentPhone1,
          b.AGENT_PHONE as agentPhone2,
          b.HOME_ADDRESS,
          (select license_no from APP___PAS__DBUSER.T_AGENT_LICENSE where agent_code = b.agent_code and license_type = '01') as agent_quaf_no,
          (select license_no from APP___PAS__DBUSER.T_AGENT_LICENSE where agent_code = b.agent_code and license_type = '02') as agent_dev_no,
          b.AGENT_ORGAN_CODE as agnet_organ_code,
          (select organ_name from APP___PAS__DBUSER.T_udmp_org where organ_code = b.AGENT_ORGAN_CODE) as agent_managecom_name,
          (select tn.BRANCH_ATTR from APP___PAS__DBUSER.t_sales_organ tn
      where a.agent_organ_code = tn.sales_organ_code) agnet_sales_organ_code,
          (select organ_name from APP___PAS__DBUSER.t_udmp_org where organ_code = b.agent_organ_code) as agent_group,
          a.AGENT_START_DATE,
          a.agent_end_date ,
          A.IS_CURRENT_AGENT,
          A.IS_NB_AGENT,
          B.AGENT_CHANNEL
      from APP___PAS__DBUSER.T_CONTRACT_AGENT a left join APP___PAS__DBUSER.T_AGENT b on a.agent_code = b.agent_code 
      where 1=1  
			and a.policy_code = #{policy_code}
		 ]]>
	</select>
		
	<select id="findNowMapContractAgentByPolicyIdOrCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
			A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT, A.COOPERATION_PROTOCOL_ID FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE 1 = 1  ]]>
		<include refid="queryContractAgentByPolicyCode" />
		<include refid="queryContractAgentByPolicyId" />
		<include refid="queryNowContractAgentByPolicyIdOrCode" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	<!-- R00101000142生存领取(给付)信息查询 -->
	<select id="findContractAgentInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select t.* from APP___PAS__DBUSER.T_CONTRACT_AGENT  t ,APP___PAS__DBUSER.T_POLICY_HOLDER h where t.policy_id = h.policy_id and t.agent_code = #{agent_code}]]>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[
		     and h.customer_id in (select m.customer_id from APP___PAS__DBUSER.T_customer m where m.customer_name = #{customer_name})
		 ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[
		     and t.policy_id  in (select ma.policy_id from APP___PAS__DBUSER.T_CONTRACT_MASTER ma where ma.policy_code = #{policy_code})
		 ]]></if>
	</select><!-- <![CDATA[  and exists (select 1 from APP___PAS__DBUSER.T_agent m  where m.agent_code = #{agent_code})    ]]> -->
	
	<!-- 续收查询 -->
	<select id="queryContractAgentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select t.* from APP___PAS__DBUSER.T_CONTRACT_AGENT  t where t.agent_code = #{agent_code} ]]>
				<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND t.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ and t.policy_code is not null order by t.policy_code]]>		
	</select>
	
	<!-- 保单服务人员轨迹查询 -->
	<select id="findPolicyAgentTrack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				         	         
			
					select   policy_code, agent_code, agent_name ,agent_start_date, agent_end_date,
		                      t2.area_branch_attr , t2.area_name ,
		                      t2.dept_branch_attr , t2.dept_name ,
		                      t2.team_branch_attr , t2.team_name 
		         from (select t.sales_organ_code,
		                      b.policy_code,
		                      t.agent_code,
		                      t.agent_name,
		                      b.agent_start_date,
		                      b.agent_end_date
		                 from APP___PAS__DBUSER.t_agent          t,
		             APP___PAS__DBUSER.t_contract_agent b
		                where t.agent_code = b.agent_code
		                  and b.policy_code = #{policy_code}  ) t1
		         left join (SELECT a.sales_organ_code,
		                           c.sales_organ_code area_branch_attr,
		                        C.Sales_Organ_Name area_name,
		                           b.sales_organ_code dept_branch_attr,
		                           B.Sales_Organ_Name dept_name,
		                           a.sales_organ_code team_branch_attr,
		                           A.SALES_ORGAN_NAME team_name
		                      FROM dev_pas.T_SALES_ORGAN A
		                      left join dev_pas.T_SALES_ORGAN B
		                        on A.Parent_Code = B.Sales_Organ_Code
		                      left join dev_pas.T_SALES_ORGAN C
		                        on B.Parent_Code = C.Sales_Organ_Code) t2
		           on t1.SALES_ORGAN_CODE = t2.sales_organ_code
		        order by t1.agent_start_date desc  	         
						   
		 ]]>
	</select>
	
	<!-- 上级机构查询 -->
	<select id="findOrgRelParent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select sales_organ_name, 
			parent_code, 
			sales_organ_code  
			from  APP___PAS__DBUSER.t_sales_organ 
			where 
			sales_organ_code = #{parent_code}
		 ]]>
	</select>
	
	<!-- 根据代理人编码，(保单号，投保人证件号)查询个人保单信息（MMS） -->
	<select id="queryPersonalPolicyInfoForMMS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
			CM.POLICY_ID,CBP.BUSI_PRD_ID,CM.BANK_AGENCY_FLAG,CM.POLICY_CODE AS ContNo,CM.APPLY_CODE AS PrtNo,CM.ORGAN_CODE AS Managecom,
			(SELECT ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG WHERE CM.ORGAN_CODE=ORGAN_CODE) AS ManagecomName,
			(SELECT ORGAN_ADDRESS FROM APP___PAS__DBUSER.T_UDMP_ORG WHERE CM.ORGAN_CODE=ORGAN_CODE) AS Address,
			CA.AGENT_ORGAN_CODE AS AgentGroup,
			CBP.VALIDATE_DATE AS CvaliDate,CBP.ISSUE_DATE AS SignDate,IL.INSURED_AGE AS InsAppAge,
			(SELECT ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT WHERE CM.POLICY_ID=POLICY_ID) AS CustomGetPolDate,
			(SELECT DISPATCH_DATE FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT WHERE CM.POLICY_ID=POLICY_ID) AS GetPolDate,
			CM.LIABILITY_STATE AS ContState,PA.NEXT_ACCOUNT_BANK AS BankCode,PA.NEXT_ACCOUNT AS BankAcc,PA.NEXT_ACCOUNT_NAME AS BankAccName,PA.PAY_LOCATION AS PayLocation,
			C2.CUSTOMER_NAME AS AppName,C2.CUSTOMER_ID AS AppNo,C2.CUSTOMER_CERTI_CODE AS AppIdNo,
			C1.CUSTOMER_NAME AS InsName,C1.CUSTOMER_ID AS InsNo,C1.CUSTOMER_CERTI_CODE AS InsIdNo,
			A.AGENT_CODE AS AgentCode,A.AGENT_NAME AS AgentName,A.AGENT_GENDER AS AgentSex,A.AGENT_STATUS AS AgentState,
			(SELECT AGENT_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT WHERE POLICY_ID=CM.POLICY_ID AND IS_NB_AGENT=1) AS NewAgentCode,
			(SELECT AGENT_NAME FROM APP___PAS__DBUSER.T_CONTRACT_AGENT WHERE POLICY_ID=CM.POLICY_ID AND IS_NB_AGENT=1) AS NewAgentName,
			(SELECT AGENT_GENDER FROM APP___PAS__DBUSER.T_AGENT WHERE AGENT_CODE=(SELECT AGENT_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT WHERE POLICY_ID=CM.POLICY_ID AND IS_NB_AGENT=1)) AS NewAgentSex,
			(SELECT AGENT_STATUS FROM APP___PAS__DBUSER.T_AGENT WHERE AGENT_CODE=(SELECT AGENT_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT WHERE POLICY_ID=CM.POLICY_ID AND IS_NB_AGENT=1)) AS NewAgentState,
			(CASE WHEN CBP.DECISION_CODE='33' THEN (SELECT TO_CHAR(LISTAGG(CONDITION_DESC,'') WITHIN GROUP(ORDER BY CONDITION_ID)) 
				FROM APP___PAS__DBUSER.T_POLICY_CONDITION WHERE BUSI_ITEM_ID=CBP.BUSI_ITEM_ID and cbp.policy_id = policy_id) ELSE '无' END) AS SpecContent,
			CM.BASIC_REMARK AS ContRemark,
			(SELECT SUM(NVL(STD_PREM_AF,0)) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID) AS Prem,
			(SELECT SUM(NVL(AMOUNT,0)) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID) AS Amnt,
			(SELECT NVL(SUM(EXTRA_PREM),0) FROM APP___PAS__DBUSER.T_EXTRA_PREM WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND EXTRA_TYPE=1 AND EXTRA_PERIOD=1) AS HealthPrem,
			(SELECT NVL(SUM(EXTRA_PREM),0) FROM APP___PAS__DBUSER.T_EXTRA_PREM WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND EXTRA_TYPE=2 AND EXTRA_PERIOD=1) AS OccupationPrem,
			(SELECT NVL(SUM(EXTRA_PREM),0) FROM APP___PAS__DBUSER.T_EXTRA_PREM WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND EXTRA_TYPE=1 AND EXTRA_PERIOD=2) AS REHealthPrem,
			(SELECT NVL(SUM(EXTRA_PREM),0) FROM APP___PAS__DBUSER.T_EXTRA_PREM WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND EXTRA_TYPE=2 AND EXTRA_PERIOD=2) AS REOccupationPrem,
			NVL(CBP.OLD_POL_NO,TO_CHAR(CBP.BUSI_ITEM_ID)) AS PolNo,
			(SELECT NVL(OLD_POL_NO,TO_CHAR(BUSI_ITEM_ID)) FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=(CASE WHEN CBP.MASTER_BUSI_ITEM_ID IS NULL THEN CBP.BUSI_ITEM_ID ELSE CBP.MASTER_BUSI_ITEM_ID END)) AS MainPolNo,
			(SELECT BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=(CASE WHEN CBP.MASTER_BUSI_ITEM_ID IS NULL THEN CBP.BUSI_ITEM_ID ELSE CBP.MASTER_BUSI_ITEM_ID END)) AS MAIN_RISK_CODE,
			CBP.BUSI_PROD_CODE AS Riskcode,BP.PRODUCT_NAME_SYS AS RiskName,CBP.LIABILITY_STATE AS AvailableStateCode,        
			(SELECT NVL(LOST_CAUSE,'-1') FROM APP___PAS__DBUSER.T_POLICY_LOSE WHERE POLICY_CODE=CM.POLICY_CODE AND LOST_DATE <=SYSDATE AND (UNLOSE_DATE>=SYSDATE OR UNLOSE_DATE IS NULL) AND ROWNUM=1) AS LostStateCode,
			(SELECT 1 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND ACCOUNT_TYPE='4' and cp.policy_id = policy_id AND CAPITAL_BALANCE>0 AND ROWNUM=1) AS LoanStateCode, 
			(SELECT TO_CHAR(LISTAGG(CPC.SERVICE_CODE,',') WITHIN GROUP(ORDER BY CPC.VALIDATE_TIME DESC)) FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC 
				WHERE CPC.POLICY_ID=CM.POLICY_ID AND CAC.ACCEPT_ID=CPC.ACCEPT_ID AND (CPC.SERVICE_CODE='CP' OR CPC.SERVICE_CODE='CS') AND CAC.ACCEPT_STATUS='18') AS BankLoanStateCode,
			(SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND PREM_STATUS='3' AND ROWNUM=1) AS RPUStateCode, 
			(SELECT APL_PERMIT FROM  APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND LAPSE_DATE <=SYSDATE AND (MATURITY_DATE>=SYSDATE OR MATURITY_DATE IS NULL) AND ROWNUM=1) AS PayPremStateCode,
			CP.PREM_FREQ AS PayIntv,CP.CHARGE_YEAR AS PayYears,
			(SELECT PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND ITEM_ID=CP.ITEM_ID AND ROWNUM=1) AS PayToDate,
			CP.COVERAGE_YEAR AS Years,C2.CUSTOMER_GENDER AS AppSex,C1.CUSTOMER_GENDER AS InsSex,CM.POLICY_PWD AS Password,BP.PRODUCT_ABBR_NAME AS RiskShortName,
			(SELECT BANK_NAME FROM APP___PAS__DBUSER.T_BANK WHERE BANK_CODE=PA.NEXT_ACCOUNT_BANK) AS BankName,
			(SELECT PAY_MODE FROM APP___CAP__DBUSER.T_PREM_ARAP TPA WHERE TPA.LIST_ID=
				(SELECT MIN(LIST_ID) FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE POLICY_CODE=CM.POLICY_CODE AND DERIV_TYPE='001')) AS YBT,
			BP.PRODUCT_CATEGORY2 AS RiskType1,BP.PRODUCT_CATEGORY3 AS RiskType2,CBP.PRD_PKG_CODE AS ContPlanCode,
			(SELECT GROUP_NAME FROM APP___PAS__DBUSER.T_CONTRACT_PRDGROUP_MAIN WHERE POLICY_ID=CBP.POLICY_ID AND GROUP_CODE=CBP.PRD_PKG_CODE) AS ContPlanName,
			CBP.MATURITY_DATE AS EndDate,C2.CUSTOMER_CERT_TYPE AS AppIdTypeCode,C1.CUSTOMER_CERT_TYPE AS InsIdTypeCode,
			(SELECT PRODUCT_NAME_STD FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT WHERE BUSINESS_PRD_ID=
				(SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE POLICY_ID=CM.POLICY_ID AND MASTER_BUSI_ITEM_ID IS NULL)) AS ContName,
			(SELECT ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG WHERE ORGAN_CODE=CM.ORGAN_CODE) AS SaleCom,
			CBP.APPLY_DATE AS PolApplyDate,CM.POLICY_TYPE AS ContServiceState,C1.CUSTOMER_BIRTHDAY INSURED_BIRTHDAY,C2.CUSTOMER_BIRTHDAY HOLDER_BIRTHDAY,
			(SELECT BEGIN_DATE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND POLICY_ID=CM.POLICY_ID AND ITEM_ID=CP.ITEM_ID AND ROWNUM=1) AS BEGIN_DATE,
			(SELECT MAX(PAY_YEAR) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE BUSI_ITEM_ID=CBP.BUSI_ITEM_ID) AS PaymentAge, 
			(SELECT SURVIVAL_W_MODE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND POLICY_ID=CM.POLICY_ID AND ITEM_ID=CP.ITEM_ID AND SURVIVAL_MODE='1' AND ROWNUM=1) AS PaymentForm, 
			(SELECT SUM(NVL(APPEND_PREM,0)) FROM APP___PAS__DBUSER.T_APPEND_PREM_LIST WHERE BUSI_ITEM_ID=CP.BUSI_ITEM_ID AND ITEM_ID=CP.ITEM_ID) AS TotalAdditionPremium,
			(SELECT FIXED_TEL FROM APP___PAS__DBUSER.T_ADDRESS WHERE ADDRESS_ID=PH.ADDRESS_ID) AS AppntPhone,
			(SELECT ADDRESS FROM APP___PAS__DBUSER.T_ADDRESS WHERE ADDRESS_ID=PH.ADDRESS_ID) AS AppntAddress,
			(CASE WHEN C2.CUSTOMER_CERT_TYPE='1' THEN C2.CUSTOMER_CERTI_CODE ELSE '' END) AS PassPort,
			(SELECT FIELD16 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER WHERE POLICY_ID=CM.POLICY_ID AND FIELD16 IS NOT NULL AND ROWNUM=1) AS Reason,
			(SELECT FIELD15 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER WHERE POLICY_ID=CM.POLICY_ID AND FIELD15 IS NOT NULL AND ROWNUM=1) AS Destinations,
			TO_CHAR(CBP.VALIDATE_DATE,'HH24:MI:SS') AS CvaliTime,TO_CHAR(CBP.MATURITY_DATE,'HH24:MI:SS') AS EndTime,
			(SELECT SALES_CHANNEL_NAME FROM APP___PAS__DBUSER.T_SALES_CHANNEL WHERE SALES_CHANNEL_CODE=CM.CHANNEL_TYPE) AS SaleChnlName,
			(SELECT MAX(PAID_COUNT) FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE POLICY_CODE=CM.POLICY_CODE AND (FEE_STATUS IN('01','16','19') AND ARAP_FLAG='1')) AS Paycount,
			SUBSTR(CM.AGENCY_CODE,0,2) AS NameOfBankCode,
			(SELECT BANK_BRANCH_NAME FROM APP___PAS__DBUSER.T_BANK_BRANCH WHERE BANK_BRANCH_CODE=SUBSTR(CM.AGENCY_CODE,0,2)) AS NameOfBank,
			CM.SERVICE_BANK_BRANCH AS BankOutLetsCode,
			(SELECT BANK_BRANCH_NAME FROM APP___PAS__DBUSER.T_BANK_BRANCH WHERE BANK_BRANCH_CODE=CM.SERVICE_BANK_BRANCH) AS BankOutLets,
			'' AS StandbyFlag1,
			(SELECT AGENT_ORGAN_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT WHERE POLICY_ID=CM.POLICY_ID AND IS_NB_AGENT=1) AS AgentGroupTD,
			CP.UNIT AS Mult,
			(SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD BB WHERE POLICY_ID=CM.POLICY_ID AND MASTER_BUSI_ITEM_ID IS NOT NULL 
				AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT WHERE PRODUCT_CATEGORY1='20003' AND BB.BUSI_PRD_ID=BUSINESS_PRD_ID) AND ROWNUM=1) AS IsContainUniversal,
			BP.COVER_PERIOD_TYPE AS RiskPeriod,
			(SELECT 1 FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT WHERE PRODUCT_CATEGORY1='20003' AND BUSINESS_PRD_ID=CBP.BUSI_PRD_ID) AS IsUniversal,
			CBP.FLIGHT_NO AS FlightNo,CBP.END_CAUSE AS EndReason,BP.PRODUCT_CATEGORY1 AS RiskType,CM.INPUT_TYPE AS InputType,CBP.HESITATION_PERIOD_DAY AS YouYuQi,
			(SELECT TO_CHAR(WM_CONCAT(PLAN_FREQ)) FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE BUSI_ITEM_ID=CBP.BUSI_ITEM_ID) AS GetIntv, 
			(SELECT REVERSIONARY_BONUS_FLAG FROM APP___PDS__DBUSER.T_PRODUCT_LIFE_BONUS WHERE PRODUCT_ID=CP.PRODUCT_ID) AS REVERSIONARY_BONUS_FLAG,
			(SELECT 1 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CAC WHERE POLICY_ID=CM.POLICY_ID and EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE WHERE ACCEPT_STATUS='18' AND SERVICE_CODE='LR' AND CHANGE_ID=CAC.CHANGE_ID) AND ROWNUM=1) AS IsLR,
			(CASE WHEN CBP.LIABILITY_STATE='3' THEN CBP.END_CAUSE WHEN CBP.LIABILITY_STATE='4' THEN CBP.LAPSE_CAUSE ELSE '-1' END) AS StateReason,
			A.AGENT_MOBILE AS Mobile,
			(SELECT NVL(SUM(ACTUAL_PAY),0) FROM APP___CLM__DBUSER.T_CLAIM_LIAB WHERE CBP.BUSI_ITEM_ID=BUSI_ITEM_ID and cbp.policy_code = policy_code) AS SumRealPay,
			(SELECT QC.SURVEY_MODULE_RESULT FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER QC,APP___PAS__DBUSER.T_QUESTIONAIRE_INFO QI 
				WHERE QC.SURVEY_QUESTION_ID=QI.SURVEY_QUESTION_ID AND QC.POLICY_ID=CM.POLICY_ID AND QC.CUSTOMER_ID=C2.CUSTOMER_ID AND QC.QUESTIONAIRE_OBJECT='1'
				AND INSTR(QI.QUESTION_CONTENT,'身高')>0 AND INSTR(QI.QUESTION_CONTENT,'体重')>0 AND QI.SURVEY_PARAM_MODULE='a,b' AND ROWNUM=1) AS AppntStature,
			(SELECT QC.SURVEY_MODULE_RESULT FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER QC,APP___PAS__DBUSER.T_QUESTIONAIRE_INFO QI 
				WHERE QC.SURVEY_QUESTION_ID=QI.SURVEY_QUESTION_ID AND QC.POLICY_ID=CM.POLICY_ID AND QC.CUSTOMER_ID=C1.CUSTOMER_ID AND QC.QUESTIONAIRE_OBJECT='1'
				AND INSTR(QI.QUESTION_CONTENT,'身高')>0 AND INSTR(QI.QUESTION_CONTENT,'体重')>0 AND QI.SURVEY_PARAM_MODULE='a,b' AND ROWNUM=1) AS InsuredStatureByQuery,	
			C1.CUSTOMER_HEIGHT AS InsuredStature,C1.CUSTOMER_WEIGHT AS InsuredAvoirdupois,
			(SELECT CUSTOMER_HEIGHT FROM APP___PAS__DBUSER.T_INSURED_LIST WHERE LIST_ID=
				(SELECT INSURED_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED WHERE CP.BUSI_ITEM_ID=BUSI_ITEM_ID AND ORDER_ID=2)) AS InsuredStature2,
			(SELECT CUSTOMER_WEIGHT FROM APP___PAS__DBUSER.T_INSURED_LIST WHERE LIST_ID=
				(SELECT INSURED_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED WHERE CP.BUSI_ITEM_ID=BUSI_ITEM_ID AND ORDER_ID=2)) AS InsuredAvoirdupois2,
			NVL(BP.SINGLE_JOINT_LIFE,'0') AS GetDutyName,
			C1.CUST_CERT_STAR_DATE AS InsuredIDEffStartDate,C1.CUST_CERT_END_DATE AS InsuredIDEffEndDate,C2.CUST_CERT_STAR_DATE AS AppntIDEffStartDate,C2.CUST_CERT_END_DATE AS AppntIDEffEndDate,
			DECODE(PH.SOCI_SECU,'1','Y','N') AS MedicareFlag,
			(CASE WHEN LENGTH(CM.ORGAN_CODE)>=8 THEN (SELECT ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG WHERE ORGAN_CODE=SUBSTR(CM.ORGAN_CODE,0,8)) ELSE '' END) AS SaleOfAgent3,
			(CASE WHEN LENGTH(CM.ORGAN_CODE)>=6 THEN (SELECT ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG WHERE ORGAN_CODE=SUBSTR(CM.ORGAN_CODE,0,6)) ELSE '' END) AS SaleOfAgent2,
			(CASE WHEN LENGTH(CM.ORGAN_CODE)>=4 THEN (SELECT ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG WHERE ORGAN_CODE=SUBSTR(CM.ORGAN_CODE,0,4)) ELSE '' END) AS SaleOfAgent,
			CP.COVERAGE_PERIOD AS YearsFlag,
			DECODE(IL.SOCI_SECU,'1','Y','0','N','') AS SocialState 
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER CM 
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT CA ON CM.POLICY_ID=CA.POLICY_ID AND CA.IS_CURRENT_AGENT=1 
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP ON CM.POLICY_ID=CBP.POLICY_ID 
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT CP ON CBP.BUSI_ITEM_ID=CP.BUSI_ITEM_ID and cbp.policy_id = cp.policy_id
			LEFT JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED BI ON BI.POLICY_ID=CM.POLICY_ID AND CP.BUSI_ITEM_ID=BI.BUSI_ITEM_ID AND BI.ORDER_ID=1
			LEFT JOIN APP___PAS__DBUSER.T_INSURED_LIST IL ON BI.INSURED_ID=IL.LIST_ID
			LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER C1 ON IL.CUSTOMER_ID=C1.CUSTOMER_ID
			LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP ON BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID
			LEFT JOIN APP___PAS__DBUSER.T_AGENT A ON CA.AGENT_CODE=A.AGENT_CODE
			LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER PH ON PH.POLICY_ID=CM.POLICY_ID 
			LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER C2 ON PH.CUSTOMER_ID=C2.CUSTOMER_ID
			LEFT JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT PA ON PA.POLICY_ID=CM.POLICY_ID
			WHERE 1=1 
			
			AND CA.AGENT_CODE=#{agent_code}
		]]>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[
			AND C2.CUSTOMER_CERTI_CODE=#{customer_certi_code}
		]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[
			AND CA.POLICY_CODE=#{policy_code} 
		]]></if>
		<if test=" order_context != null and order_context != ''  "><![CDATA[
			${order_context}
		]]></if>
	</select>
	<select id="updateContractAgentState" parameterType="java.util.Map">
		<![CDATA[ 
		    UPDATE APP___PAS__DBUSER.T_CONTRACT_AGENT A SET A.IS_CURRENT_AGENT = 0 WHERE 1=1 
		           ]]>
		<include refid="contractAgentWhereCondition" />
    </select>
    
    <select id="findAgentByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
			A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT,A.ORGAN_CODE, A.COOPERATION_PROTOCOL_ID FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE A.IS_CURRENT_AGENT = 1 AND A.POLICY_CODE = #{policy_code} ]]>
    </select>
    
    
    <!-- P00001000307 个人保单信息查询  :查询首期业务员-为投保时的业务员-第一笔收收付费的业务员 -->	
	<select id="findNewAgentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		 select a.agent_code,a.agent_name,a.agent_mobile,a.agent_gender,a.agent_status
          from APP___PAS__DBUSER.T_AGENT a ,
               APP___PAS__DBUSER.T_CONTRACT_AGENT ca 
         where ca.agent_code=a.agent_code
           and ca.is_nb_agent = 1
           and ca.policy_code = #{policy_code}
		 
		]]>
	</select>	
	
    
        <!-- 根据保单list查询保单代理人-->
	<select id="findAllMapContractAgentList" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
			A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT, A.COOPERATION_PROTOCOL_ID  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE A.IS_CURRENT_AGENT = 1
		<if test="policyCodeList  != null and policyCodeList.size()!=0 ">
			AND A.POLICY_CODE in (
			<foreach collection="policyCodeList" item="policy_code_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code_item} ]]>
			</foreach>
			)
		</if>
		ORDER BY A.LIST_ID
	</select> 
	<!-- 查询业务员的销售机构 -->
	    <select id="findAgentCodeByPolicyId" parameterType="java.util.Map" resultType="java.util.Map" >
		<![CDATA[ SELECT C.AGENT_ORGAN_CODE,A.AGENT_CODE,A.POLICY_CODE,A.POLICY_ID,A.AGENT_NAME FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A , APP___PAS__DBUSER.T_AGENT C   WHERE C.AGENT_CODE = A.AGENT_CODE
            ]]>
            <if test=" is_nb_agent != null and is_nb_agent != ''  "><![CDATA[
			AND A.is_nb_agent=#{is_nb_agent} 
		]]></if>
          	<if test=" policy_code != null and policy_code != ''  "><![CDATA[
			AND A.POLICY_CODE=#{policy_code} 
		]]></if>
          	<if test=" policy_id != null and policy_id != ''  "><![CDATA[
			AND A.POLICY_ID=#{policy_id} 
		]]></if>
			
    </select>
    <!-- 根据保单号查询首期业务人员 -->
    <select id="querysContractAgentByPolicyCode" parameterType="java.util.Map" resultType="java.util.Map">
              select 
          tt.agent_code,
          tt.agent_name,
          tt.agent_gender,
          (select a.agent_status_name  from dev_pas.t_agent_status a where a.agent_status = tt.AGENT_STATUS) as AGENT_STATUS,
          (select ag.grade_name  from dev_pas.t_agent_grade ag where ag.grade_code= tt.agent_grade)   as AGENT_GRADE ,
          tt.CERTI_CODE,
          tt.BIRTHDAY,
          tt.AGENT_LEVEL,
          tt.AGENT_MOBILE,
          tt.AGENT_PHONE,
          tt.HOME_ADDRESS,
          tt.QUAF_NO,
          tt.AGENT_ORGAN_CODE,
          tt.EMPLOYMENT_DATE,
          tt.AGENT_GRADE,
          (select tn.branch_attr  from dev_pas.t_sales_organ tn where tn.sales_organ_code = tt.sales_organ_code and rownum=1) BRANCH_ATTR,
          (select tn.sales_organ_code  from dev_pas.t_sales_organ tn where tn.sales_organ_code = tt.sales_organ_code and rownum=1) SALES_ORGAN_CODE,
          (select tn.sales_organ_name  from dev_pas.t_sales_organ tn where tn.sales_organ_code = tt.sales_organ_code and rownum=1) SALES_ORGAN_NAME,
          (select ug.organ_name  from dev_pas.t_udmp_org ug where ug.organ_code = tt.AGENT_ORGAN_CODE and rownum =1) as MANAGECO_MNAME ,
          (select t.license_no
                  from dev_pas.t_agent_license t
                 where t.license_type = '2'
                   and t.state = '1'
                   and t.agent_code = tt.agent_code
                   and t.list_id =
                       (select max(t.list_id)
                          from dev_pas.t_agent_license t
                         where t.license_type = '2'
                           and t.state = '1'
                           and t.agent_code = tt.agent_code
                           and sysdate between t.license_start_date and t.license_end_date
                           )
          ) as license_no
            from 
          dev_pas.t_agent tt,
          dev_pas.t_contract_agent ta
          
          where tt.agent_code = ta.agent_code
          and ta.is_nb_agent='1'
          and ta.policy_code = #{policy_code}
          
          
    </select>
    
    <!--查询首期业务员去掉原来的条件 tt.agent_code = (select agent_code from (  select agent_code ,rownum as rn from (
   select  a.agent_code,a.due_time from dev_cap.t_prem_arap a   where a.policy_code= #{policy_code}  and  A.FEE_STATUS='16'
     order by a.due_time asc )) where rn=1) -->
    
    <!-- 根据保单号查询续期业务人员 -->
    <select id="queryXContractAgentByPolicyCode" parameterType="java.util.Map" resultType="java.util.Map">
             select 
          tt.agent_code,
          tt.agent_name,
          tt.agent_gender,
          (select a.agent_status_name  from dev_pas.t_agent_status a where a.agent_status = tt.AGENT_STATUS) as AGENT_STATUS,
          (select ag.grade_name  from dev_pas.t_agent_grade ag where ag.grade_code= tt.agent_grade)   as AGENT_GRADE ,
          tt.CERTI_CODE,
          tt.BIRTHDAY,
          tt.AGENT_LEVEL,
          tt.AGENT_MOBILE,
          tt.AGENT_PHONE,
          tt.HOME_ADDRESS,
          tt.QUAF_NO,
          tt.AGENT_ORGAN_CODE,
          tt.EMPLOYMENT_DATE,
          tt.AGENT_GRADE,
          (select tn.branch_attr  from dev_pas.t_sales_organ tn where tn.sales_organ_code = tt.sales_organ_code and rownum='1') BRANCH_ATTR,
          (select tn.sales_organ_code  from dev_pas.t_sales_organ tn where tn.sales_organ_code = tt.sales_organ_code and rownum='1') SALES_ORGAN_CODE,
          (select tn.sales_organ_name  from dev_pas.t_sales_organ tn where tn.sales_organ_code = tt.sales_organ_code and rownum='1') SALES_ORGAN_NAME,
          (select ug.organ_name  from dev_pas.t_udmp_org ug where ug.organ_code = tt.AGENT_ORGAN_CODE and rownum = '1') as MANAGECO_MNAME,
          (select t.license_no
                  from dev_pas.t_agent_license t
                 where t.license_type = '2'
                   and t.state = '1'
                   and t.agent_code = tt.agent_code
                   and t.list_id =
                       (select max(t.list_id)
                          from dev_pas.t_agent_license t
                         where t.license_type = '2'
                           and t.state = '1'
                           and t.agent_code = tt.agent_code
                           and sysdate between t.license_start_date and t.license_end_date
                     
                           )) as license_no
            from 
          dev_pas.t_agent tt,
          dev_pas.t_contract_agent tca 
          where tt.agent_code = tca.agent_code
          and tca.policy_code = #{policy_code}
          and tca.is_current_agent = '1'   
    </select>
    <!-- 根据保单号查询展业机构外部编码-->
    <select id="queryBranchAttrByPolicyCode" parameterType="java.util.Map" resultType="java.util.Map">
      select tt.agent_code,
       tt.agent_name,
       tt.agent_gender,
       tt.AGENT_STATUS,
       tt.CERTI_CODE,
       tt.BIRTHDAY,
       tt.AGENT_LEVEL,
       tt.AGENT_MOBILE,
       tt.AGENT_PHONE,
       tt.HOME_ADDRESS,
       tt.QUAF_NO,
       tt.AGENT_ORGAN_CODE,
       tt.EMPLOYMENT_DATE,
       tn.BRANCH_ATTR,
       tn.SALES_ORGAN_CODE,
       tn.SALES_ORGAN_NAME
  from dev_pas.t_agent tt
  left join dev_pas.t_contract_agent tc
    on tt.agent_code = tc.agent_code
  left join dev_pas.t_sales_organ tn
    on tn.sales_organ_code = tt.sales_organ_code
 where tc.policy_code = #{policy_code}

    <if test=" is_current_agent != null and is_current_agent != ''  "><![CDATA[
			AND TC.IS_CURRENT_AGENT = #{is_current_agent} 
		]]></if>
    </select>
    <!-- 根据保单号查询银代网点名称-->
    <select id="queryYDByPolicyCode" parameterType="java.util.Map" resultType="java.util.Map">
   select tt.agent_code,
       tt.agent_name,
       tt.agent_gender,
       tt.AGENT_STATUS,
       tt.CERTI_CODE,
       tt.BIRTHDAY,
       tt.AGENT_LEVEL,
       tt.AGENT_MOBILE,
       tt.AGENT_PHONE,
       tt.HOME_ADDRESS,
       tt.QUAF_NO,
       tt.AGENT_ORGAN_CODE,
       tt.EMPLOYMENT_DATE,
       tn.BRANCH_ATTR,
       tn.SALES_ORGAN_CODE,
       tn.SALES_ORGAN_NAME
  from dev_pas.t_agent tt
  left join dev_pas.t_contract_agent tc
    on tt.agent_code = tc.agent_code
  left join dev_pas.t_sales_organ tn
    on tn.sales_organ_code = tt.sales_organ_code
 where tc.policy_code = #{policy_code}
 and tc.is_nb_agent='1'
 
    </select>
   <!-- 查询作为投保人保单生效30日内做CC是否为客户上门办理 -->
    <select id="queryHolderUpdatePhone" parameterType="java.util.Map" resultType="java.util.Map">
    	<![CDATA[ select distinct ph.policy_id,
                ph.policy_code,
                ph.policy_chg_id,
                ph.change_id
  from dev_pas.t_cs_policy_holder ph
  left join dev_pas.t_cs_contract_master m
    on ph.policy_id = m.policy_id
   and ph.policy_chg_id = m.policy_chg_id
  left join dev_pas.T_CS_APPLICATION ca
    on ph.change_id = ca.change_id
   and ca.service_type != 1
   and ca.change_id = #{change_id}
 where ph.customer_id = #{customer_id}
   and ph.old_new = 0
   and to_date(ca.apply_time) - to_date(m.validate_date) <= 30
union 
select distinct ph.policy_id,
                ph.policy_code,
                pc.policy_chg_id,
                pc.change_id
  from dev_pas.t_policy_holder ph
  left join dev_pas.t_contract_master m
    on ph.policy_id = m.policy_id
  left join dev_pas.t_cs_policy_change pc
    on pc.policy_id = m.policy_id
  left join dev_pas.T_CS_APPLICATION ca
    on pc.change_id = ca.change_id
   and ca.service_type != 1
   and ca.change_id = #{change_id}
 where ph.customer_id = #{customer_id}
   and to_date(ca.apply_time) - to_date(m.validate_date) <= 30
    	
				
				
				]]>
    </select>
    
    <!-- 查询作为投保人保单生效30日内做CC是否为客户上门办理  CC快捷办理 -->
     <select id="queryHKHolderUpdatePhone" parameterType="java.util.Map" resultType="java.util.Map">
     	<![CDATA[ select distinct ph.policy_id,ph.policy_code
		         from dev_pas.t_policy_holder ph 
		        left join dev_pas.t_contract_master m on ph.policy_id = m.policy_id 
		        where ph.customer_id = #{customer_id}
		        and to_date(#{apply_time},'yyyy-MM-dd')-to_date(m.validate_date) <=30]]>
     </select>  
    <select id="PA_findContractAgentIsCurrent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,
		       A.POLICY_CODE,
		       A.AGENT_TYPE,
		       A.POLICY_ID,
		       A.APPLY_CODE,
		       A.AGENT_CODE,
		       A.AGENT_NAME,
		       A.LAST_AGENT_CODE,
		       A.LAST_AGENT_NAME,
		       A.RELATION_TO_PH,
		       A.AGENT_ORGAN_CODE,
		       A.AGENT_MOBILE,
		       A.AGENT_START_DATE,
		       A.AGENT_END_DATE,
		       A.IS_CURRENT_AGENT,
		       A.IS_NB_AGENT,
		       A.INSERT_BY,
		       A.INSERT_TIME,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_BY,
		       A.UPDATE_TIME,
		       A.UPDATE_TIMESTAMP,
		       A.COOPERATION_PROTOCOL_ID
		       FROM DEV_PAS.T_CONTRACT_AGENT A WHERE A.IS_CURRENT_AGENT = '1' ]]>
		<include refid="queryContractAgentByPolicyId" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 查询在职业务员的绩优等级 -->
	<select id="PA_findAgentCodeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,
		       A.POLICY_CODE,
		       A.AGENT_TYPE,
		       A.POLICY_ID,
		       A.APPLY_CODE,
		       A.AGENT_CODE,
		       A.AGENT_NAME,
		       A.LAST_AGENT_CODE,
		       A.LAST_AGENT_NAME,
		       A.RELATION_TO_PH,
		       A.AGENT_ORGAN_CODE,
		       A.AGENT_MOBILE,
		       A.AGENT_START_DATE,
		       A.AGENT_END_DATE,
		       A.IS_CURRENT_AGENT,
		       A.IS_NB_AGENT,
		       A.INSERT_BY,
		       A.INSERT_TIME,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_BY,
		       A.UPDATE_TIME,
		       A.UPDATE_TIMESTAMP,
		       (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = A.AGENT_CODE) as agent_channel,
		       (SELECT CA.AGENT_LEVEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = A.AGENT_CODE) as ca_agent_level,
		       (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = A.AGENT_CODE) as tab_agent_level
		       FROM DEV_PAS.T_CONTRACT_AGENT A WHERE A.IS_CURRENT_AGENT = '1' ]]>
		<include refid="queryContractAgentByPolicyCode" />
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="queryContractAgentBycomCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT (SELECT B.AGENT_NAME FROM APP___PAS__DBUSER.T_AGENT B WHERE A.AGENT_CODE = B.AGENT_CODE) AGENT_NAME,
				A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
				A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
				A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT, A.COOPERATION_PROTOCOL_ID
				FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE 1=1 ]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID,A.INSERT_TIME ]]> 
	</select>
	<!-- 查询保单业务员姓名、 区代码、 部代码 、组代码 -->
	 <select id="PA_findPolicySaleMsg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			 SELECT C.AGENT_NAME || '-' ||
			        (SELECT TO_CHAR(LISTAGG(T.Sales_Organ_Code, '-') WITHIN
			                        GROUP(ORDER BY T.ORGAN_LEVEL_CODE ASC))
			           FROM APP___PAS__DBUSER.T_SALES_ORGAN T
			          START WITH T.SALES_ORGAN_CODE = C.SALES_ORGAN_CODE
			         CONNECT BY PRIOR T.PARENT_CODE = T.SALES_ORGAN_CODE) AS AGENT_NAME
			   FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A,
			        APP___PAS__DBUSER.T_AGENT          C,
			        DEV_PAS.T_CONTRACT_MASTER          CCM
			  WHERE C.AGENT_CODE = A.AGENT_CODE
			    AND A.IS_NB_AGENT = 1
			    AND A.POLICY_ID = CCM.POLICY_ID
			    AND CCM.POLICY_ID = #{policy_id}
		 ]]>
	</select>
		<!-- 查询保单业务员姓名、 区代码、 部代码 、组代码 (逻辑变更 rm49563 )-->
	 <select id="PA_findPolicyAgentGroup" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
               C.AGENT_NAME as AGENT_NAME,
               T.Branch_Attr as Branch_Attr
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A,
               APP___PAS__DBUSER.T_AGENT          C,
               DEV_PAS.T_CONTRACT_MASTER          CCM,
               APP___PAS__DBUSER.T_SALES_ORGAN    T
            WHERE C.AGENT_CODE = A.AGENT_CODE
                AND A.IS_CURRENT_AGENT = 1
			  	AND C.AGENT_STATUS in ('1','2')
                AND T.SALES_ORGAN_CODE = C.SALES_ORGAN_CODE
                AND A.POLICY_ID = CCM.POLICY_ID
			    AND CCM.POLICY_ID = #{policy_id}
		 ]]>
	</select>
	
	<!-- 根据机构代码查询机构级别 -->
	 <select id="PA_findOrganGradeByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.ORGAN_ID,
                 A.ORGAN_CODE,
                 A.ORGAN_NAME,
                 A.ORGAN_GRADE,
                 A.UPORGAN_LVL_CODE,
                 A.UPORGAN_ID,
                 A.UPORGAN_CODE
            FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A,
                 APP___PAS__DBUSER.T_UDMP_ORG     B
           WHERE 1 = 1
             AND A.ORGAN_CODE = B.ORGAN_CODE
             AND B.ORGAN_CODE = #{organ_code}
		 ]]>
	</select>
<select id="findContractAgentDescAgentEndDate" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM (
			<![CDATA[ SELECT T.AGENT_NAME, A.AGENT_START_DATE, T.AGENT_MOBILE, A.RELATION_TO_PH, T.AGENT_NORMAL_TYPE AS AGENT_TYPE,
			A.APPLY_CODE, A.POLICY_CODE,A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE, A.IS_CURRENT_AGENT ,A.LAST_AGENT_CODE,
       A.LAST_AGENT_NAME, A.IS_NB_AGENT  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A ,APP___PAS__DBUSER.T_AGENT T 
	       WHERE A.AGENT_CODE=T.AGENT_CODE ]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY   A.AGENT_END_DATE DESC ]]>
		)WHERE ROWNUM =1
	</select>
	
<!-- 添加操作 -->
	<insert id="addSynPolicyCode"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SYNPOLICYCODE_LOG_LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SYNPOLICYCODE_LOG(LIST_ID,
				INSERT_TIME, POLICY_CODE, CORE_AGENT_CODE, FTP_AGENT_CODE,UPDATE_TIME, 
				INSERT_TIMESTAMP,  UPDATE_BY, 
				UPDATE_TIMESTAMP, INSERT_BY) 
			VALUES (#{list_id, jdbcType=NUMERIC} ,
				SYSDATE, #{policy_code, jdbcType=VARCHAR},#{core_agent_code, jdbcType=VARCHAR},
				 #{ftp_agent_code, jdbcType=VARCHAR} ,SYSDATE,
				CURRENT_TIMESTAMP,  #{update_by, jdbcType=NUMERIC} ,
				CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC}
				) 
		 ]]>
	</insert>
	 <select id="findContResult" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.RELATION_TO_PH as appntRelationToInsured,
			DECODE(DD.TAX_EXTENSION_FLAG,'1','Y','0','N') as isTax,
			DECODE(DD.TAX_REVENUE_FLAG,'1','Y','0','N') as iscrs, 
			DECODE(DD.TAX_RATE_FLAG,'1','Y') as IsTaxRevenue 
 				FROM DEV_PAS.T_INSURED_LIST A,
 					 DEV_PAS.T_contract_busi_prod CC,
 					 DEV_PAS.T_BUSINESS_PRODUCT DD
 		WHERE 1 = 1 AND A.POLICY_CODE=CC.POLICY_CODE AND CC.BUSI_PRD_ID=DD.BUSINESS_PRD_ID
 				AND CC.master_busi_item_id is null
 				AND A.POLICY_CODE=#{policyCode}
 		]]>

  </select>
  
  <!-- 个人渠道绩优业务员信息查询 -->
  <select id="PA_findAgentByAgentCodes" resultType="java.util.Map" parameterType="java.util.Map">
  
 <![CDATA[ SELECT DISTINCT B.AGENT_CODE,
                B.AGENT_NAME,
                B.AGENT_MOBILE,
                B.AGENT_ORGAN_CODE,
                B.AGENT_CHANNEL                   
            FROM APP___PAS__DBUSER.T_AGENT B
           WHERE 1 = 1 ]]>
       <if test=" agent_code != null and agent_code != '' "><![CDATA[ and B.agent_code = #{agent_code} ]]></if>
        <if test=" agent_mobile != null and agent_mobile != '' "><![CDATA[ and B.agent_mobile = #{agent_mobile} ]]></if>
        
  </select>
  <!--个人保单信息查询P00001000307  -->
  <select id="PA_findContractAgentMobileBypolicyId" resultType="java.util.Map" parameterType="java.util.Map" >
  	<![CDATA[
   SELECT A.POLICY_CODE, B.AGENT_MOBILE
   FROM DEV_PAS.T_CONTRACT_AGENT A, DEV_PAS.T_AGENT B
 
  WHERE A.POLICY_CODE =#{policy_code}
    AND A.AGENT_CODE = B.AGENT_CODE
    AND A.IS_CURRENT_AGENT = 1
   ]]>
  </select>
  <!--需求分析任务 #70152  -->
  <select id="PA_findContractAgentByAgentCodeAndpolicyCode" resultType="java.util.Map" parameterType="java.util.Map" >
  <![CDATA[
  
 select CA.POLICY_CODE, CA.AGENT_CODE, CA.AGENT_NAME,TA.AGENT_LEVEL
  from APP___PAS__DBUSER.T_CONTRACT_AGENT CA,APP___PAS__DBUSER.T_AGENT TA
 where CA.POLICY_CODE =#{policy_code}
   AND CA.AGENT_CODE=TA.AGENT_CODE
   AND CA.IS_CURRENT_AGENT = 1
   AND TA.AGENT_LEVEL IS NOT NULL
   AND CA.Agent_Code = #{agent_code} 
    
  ]]>
  </select>
 <!-- 添加操作 -->
	<insert id="addIntermedOrphPolDeal"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_INTER_INFO__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_INTERMEDORPHPOLDEAL_INFO
			  (LIST_ID,
			   OLD_AGENT_CODE,
			   QUIT_DATE,
			   NEW_AGENT_CODE,
			   FTPFILE_NAME,
			   INSERT_BY,
			   INSERT_TIME,
			   INSERT_TIMESTAMP,
			   UPDATE_BY,
			   UPDATE_TIME,
			   UPDATE_TIMESTAMP)
			VALUES
			  (#{list_id, jdbcType = NUMERIC},
			   #{old_agent_code, jdbcType = VARCHAR},
			   #{agent_start_date, jdbcType = VARCHAR},
			   #{new_agent_code, jdbcType = VARCHAR},
			   #{file_name, jdbcType = VARCHAR},
			   #{insert_by, jdbcType = NUMERIC},
			   SYSDATE,
			   CURRENT_TIMESTAMP,
			   #{update_by,jdbcType = NUMERIC},
			   SYSDATE,
			   CURRENT_TIMESTAMP)
		 ]]>
	</insert>
	
	<select id="PA_findIsCurrentContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.AGENT_NAME, 
       A.AGENT_START_DATE,
       A.AGENT_MOBILE,
       A.RELATION_TO_PH,
       A.AGENT_TYPE,
       A.APPLY_CODE,
       A.POLICY_CODE,
       A.AGENT_END_DATE,
       A.LIST_ID,
       A.AGENT_ORGAN_CODE,
       A.POLICY_ID,
       A.AGENT_CODE,
       A.IS_CURRENT_AGENT,
       A.LAST_AGENT_CODE,
       A.LAST_AGENT_NAME,
       A.ORGAN_CODE,
       A.CHANNEL_TYPE,
       A.IS_NB_AGENT,
       B.SALES_ORGAN_CODE,
       rownum rn
  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A, APP___PAS__DBUSER.T_AGENT B
 WHERE A.AGENT_CODE = B.AGENT_CODE AND ROWNUM < 100000  AND A.AGENT_CODE = #{agent_code}  ]]>
		<if test=" is_current_agent != null and is_current_agent != ''  "><![CDATA[ AND A.IS_CURRENT_AGENT = #{is_current_agent} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select> 
	
	<select id="findCsDrAgentCfg"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select cdac.organ_code,cdac.agent_code
		  from dev_pas.t_contract_agent t, dev_pas.T_CS_DR_AGENT_CFG cdac
		 where t.policy_code = #{policy_code}
		   and t.IS_CURRENT_AGENT = '1'
		   and cdac.AGENT_CODE = t.AGENT_CODE
		]]>
	</select>
	
	<select id="findCurrentContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM (
			<![CDATA[ SELECT T.AGENT_NAME, A.AGENT_START_DATE, T.AGENT_MOBILE, A.RELATION_TO_PH, T.AGENT_NORMAL_TYPE AS AGENT_TYPE,
			A.APPLY_CODE, A.POLICY_CODE,A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE, A.IS_CURRENT_AGENT ,A.LAST_AGENT_CODE,
       A.LAST_AGENT_NAME, A.IS_NB_AGENT,A.ORGAN_CODE,A.CHANNEL_TYPE  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A ,APP___PAS__DBUSER.T_AGENT T 
	       WHERE A.AGENT_CODE=T.AGENT_CODE  AND A.IS_CURRENT_AGENT=1 AND A.POLICY_CODE =#{policy_code}]]>
		<include refid="contractAgentWhereCondition" />
		<![CDATA[ ORDER BY  t.employment_date DESC ]]>
		)WHERE rownum =1
	</select>
	
	<select id="findAgentPOEmail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT * FROM (
			 SELECT * FROM dev_pas.T_AGENT A WHERE A.AGENT_EMAIL=#{agent_email} AND A.AGENT_STATUS=#{agent_status}  ORDER BY  A.EMPLOYMENT_DATE DESC 
		)WHERE rownum =1]]>
	</select>
	
	<select id="findrepeatcustomerpoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT T.CUSTOMER_ID FROM DEV_PAS.T_CUSTOMER T WHERE T.EMAIL=#{email}]]>
	</select> 
	<!-- 根据保单号查询保单销售机构 -->
	    <select id="findOrganCodeByPolicyCode" parameterType="java.util.Map" resultType="java.util.Map" >
		<![CDATA[
		select t.organ_code
		  from dev_pas.t_contract_agent t
		 where t.policy_code = #{policy_code}
		   and t.is_nb_agent = '1'
		]]>
			
    </select>
    <!-- 根据该保单号和投保人三要素查询与该保单对应的业务员三要素是否一致 -->
    <select id="findSelfInsFlag" resultType="int" parameterType="java.util.Map">
		<![CDATA[select count(1)
 				 from APP___PAS__DBUSER.T_POLICY_HOLDER  a,
     				  APP___PAS__DBUSER.T_CONTRACT_AGENT b,
      				  APP___PAS__DBUSER.T_AGENT          c
 				 where a.POLICY_CODE = b.POLICY_CODE
   					   and b.AGENT_CODE = c.AGENT_CODE
   					   and b.IS_CURRENT_AGENT = '1'
   					   and c.AGENT_NAME = #{agent_name,jdbcType = VARCHAR}
   					   and c.CERT_TYPE = #{cert_type,jdbcType = VARCHAR}
   					   and c.CERTI_CODE = #{certi_code,jdbcType = VARCHAR}
   					   and a.POLICY_CODE = #{policy_code,jdbcType = VARCHAR}]]>
	</select>
	<!-- 判断保单的当前服务人员是否签署“新版承诺书2022版自互保件承诺书” -->
    <select id="queryCommitmentLetter" resultType="int" parameterType="java.util.Map">
		<![CDATA[
		select count(1)
		  from dev_pas.t_contract_agent a,dev_pas.t_agent_commitment b
		 where 1 = 1
		   and a.agent_code = b.agent_code
		   and a.is_current_agent = '1'
		   and b.commitment_is_upload = '1'
		   and a.policy_code = #{policy_code}
		]]>
	</select>
	
	<!-- 判断保单的当前服务人员是否签署“新版承诺书2022版自互保件承诺书” -->
    <select id="findCommitmentLetter" resultType="int" parameterType="java.util.Map">
		<![CDATA[
		select count(1)
		  from dev_pas.t_contract_agent a,dev_pas.t_agent_commitment b
		 where 1 = 1
		   and a.agent_code = b.agent_code
		   and a.is_current_agent = '1'
		   and b.commitment_is_upload = '1'
		   and a.policy_code = #{policy_code}
		]]>
	</select>

	<!-- 根据保单号查询保单销售机构 -->
	<select id="findConAgentForQueryContractAgentInfo" parameterType="java.util.Map" resultType="java.util.Map" >
		<![CDATA[ 	SELECT A.POLICY_CODE,
					       B.AGENT_CODE,
					       B.AGENT_NAME,
					       B.AGENT_ORGAN_CODE,
					       C.ORGAN_NAME,
					       B.AGENT_MOBILE,
                 		   B.AGENT_STATUS,
                 		   M.ORGAN_CODE
					  FROM DEV_PAS.T_CONTRACT_AGENT A, DEV_PAS.T_AGENT B, DEV_PAS.T_UDMP_ORG C,DEV_PAS.T_CONTRACT_MASTER M
					 WHERE 1 = 1
					   AND A.AGENT_CODE = B.AGENT_CODE
					   AND B.AGENT_ORGAN_CODE = C.ORGAN_CODE
					   AND A.POLICY_CODE = M.POLICY_CODE
					   AND A.IS_CURRENT_AGENT = 1
					   AND A.POLICY_CODE =  #{policy_code} ]]>
	</select>
	<!-- 查询保单服务信息 -->
    <select id="findPolicyServiceInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT (SELECT T.SALES_CHANNEL_NAME
           FROM DEV_PAS.T_SALES_CHANNEL T
          WHERE T.SALES_CHANNEL_CODE = B.CHANNEL_TYPE) AS CHANNEL_TYPE,
        (SELECT T.AGENT_NAME
           FROM DEV_PAS.T_AGENT T
          WHERE T.AGENT_CODE = B.AGENT_CODE) AS NB_AGENT_NAME,
        A.ORGAN_CODE,
        (SELECT T.AGENT_NAME
           FROM DEV_PAS.T_AGENT T
          WHERE T.AGENT_CODE = A.AGENT_CODE) AS AGENT_NAME
   FROM DEV_PAS.T_CONTRACT_AGENT A, DEV_PAS.T_CONTRACT_AGENT B
  WHERE A.POLICY_CODE = #{policy_code}
    AND A.IS_CURRENT_AGENT = '1'
    AND B.IS_NB_AGENT = '1'
    AND A.POLICY_CODE = B.POLICY_CODE
		]]>
	</select>
	<select id="findContractAgentPO" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[ SELECT  A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
			A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT,A.ORGAN_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE ROWNUM = 1]]>
		<include refid="contractAgentWhereCondition" />
	</select>
    <select id="findContractAgentPOByPolicyCodeAndIsNB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
			A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT,A.ORGAN_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE A.POLICY_CODE = #{policy_code} AND A.IS_NB_AGENT = #{is_nb_agent}]]>
    </select>

	<!-- 查询银代销售渠道的代理人信息 -->
	<select id="findContractAgentForPrintCVQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.AGENT_NAME,
			       A.AGENT_START_DATE,
			       A.AGENT_MOBILE,
			       A.RELATION_TO_PH,
			       A.AGENT_TYPE,
			       A.APPLY_CODE,
			       A.POLICY_CODE,
			       A.AGENT_END_DATE,
			       A.LIST_ID,
			       A.AGENT_ORGAN_CODE,
			       A.POLICY_ID,
			       A.AGENT_CODE,
			       A.IS_CURRENT_AGENT,
			       A.IS_NB_AGENT,
			       A.ORGAN_CODE
			  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A
			 WHERE A.POLICY_CODE = #{policy_code}
			   AND A.IS_NB_AGENT = '1'
			   AND A.CHANNEL_TYPE = '03'
		]]>
	</select>
	
	<!-- 关联保单与代理人关系表，返回承保代理人的销售渠道与入参【承保销售渠道】相等的保单信息 -->
	<select id="queryContractAgentChannelTypeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.AGENT_NAME,
			       A.AGENT_START_DATE,
			       A.AGENT_MOBILE,
			       A.RELATION_TO_PH,
			       A.AGENT_TYPE,
			       A.APPLY_CODE,
			       A.POLICY_CODE,
			       A.AGENT_END_DATE,
			       A.LIST_ID,
			       A.AGENT_ORGAN_CODE,
			       A.POLICY_ID,
			       A.AGENT_CODE,
			       A.IS_CURRENT_AGENT,
			       A.IS_NB_AGENT,
			       A.ORGAN_CODE
			  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A
			 WHERE A.POLICY_CODE = #{policy_code}
			   AND A.IS_NB_AGENT = '1'
			   AND A.CHANNEL_TYPE = #{channel_type}
		]]>
	</select>
</mapper>
