<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISpecialAccountRelationDao">

	<sql id="PA_specialAccountRelationWhereCondition">
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" bank_policy_id  != null "><![CDATA[ AND A.BANK_POLICY_ID = #{bank_policy_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" validiy_end_date  != null "><![CDATA[ AND A.VALIDIY_END_DATE = #{validiy_end_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySpecialAccountRelationByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationByBankPolicyIdCondition">
		<if test=" bank_policy_id  != null "><![CDATA[ AND A.BANK_POLICY_ID = #{bank_policy_id} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSpecialAccountRelation"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SPECIAL_ACCOUNT_RELATION.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION(
				APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, BANK_POLICY_ID, INSERT_BY, POLICY_ID, VALIDIY_END_DATE  ) 
			VALUES (
				#{apply_code, jdbcType=VARCHAR}, CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{bank_policy_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}, #{validiy_end_date, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSpecialAccountRelation" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSpecialAccountRelation" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    BANK_POLICY_ID = #{bank_policy_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    VALIDIY_END_DATE = #{validiy_end_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSpecialAccountRelationByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationByListIdCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationByBankPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationByBankPolicyIdCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSpecialAccountRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_specialAccountRelationWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSpecialAccountRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_specialAccountRelationWhereCondition" />
	</select>

<!-- 查询单条操作 -->
	<select id="PA_findSpecialAccountRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
		<include refid="PA_specialAccountRelationWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSpecialAccountRelationTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
		<include refid="PA_specialAccountRelationWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySpecialAccountRelationForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.APPLY_CODE, B.POLICY_CODE, B.LIST_ID, 
			B.BANK_POLICY_ID, B.POLICY_ID,B.VALIDIY_END_DATE FROM (
					SELECT ROWNUM RN, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_specialAccountRelationWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
