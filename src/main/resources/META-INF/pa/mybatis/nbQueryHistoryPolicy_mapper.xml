<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.impl.nbqueryhistorypolicy.dao.INBQueryHistoryPolicyDao">
	<select id="PA_queryHolderHistoryPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ANNUAL_INCOME_CEIL AS ANNUAL_INCOME, A.JOB_CODE
					  FROM DEV_PAS.T_POLICY_HOLDER   A,
					       DEV_PAS.T_CONTRACT_MASTER B
					 WHERE A.POLICY_CODE = B.POLICY_CODE
					   AND B.LIABILITY_STATE != 3
					   AND A.CUSTOMER_ID = #{customer_id}
					]]>
	</select>
	
	<select id="PA_queryInsuredHistoryPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.JOB_CODE, A.ANNUAL_INCOME_CEIL
					  FROM DEV_PAS.T_INSURED_LIST    A,
					       DEV_PAS.T_CONTRACT_MASTER B
					 WHERE A.POLICY_CODE = B.POLICY_CODE
					   AND B.LIABILITY_STATE != 3
					   AND A.CUSTOMER_ID = #{customer_id} ]]>
	</select>
	
	<select id="PA_queryBeneHistoryPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.POLICY_CODE
					  FROM DEV_PAS.T_CONTRACT_BENE   A,
					       DEV_PAS.T_CONTRACT_MASTER B
					 WHERE A.POLICY_CODE = B.POLICY_CODE
					   AND B.LIABILITY_STATE != 3
					   AND A.BENE_TYPE=1
					   AND A.CUSTOMER_ID = #{customer_id}]]>
	</select>
</mapper>