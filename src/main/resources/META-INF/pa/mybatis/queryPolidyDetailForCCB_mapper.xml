<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IPolicyDetailForCCBDao">

<sql id="policyCodeListCondition">
  <if test=" policy_code_list  != null and policy_code_list != '' ">
	<![CDATA[ AND tcm.policy_code in ]]>
		<foreach collection="policy_code_list" item="item" index="index"
				open="(" close=")" separator=",">#{item}</foreach>
  </if>
</sql>

<sql id="policyChangeFiniTimeCondition">
  <if test=" query_start_date != null and query_start_date != '' "><![CDATA[ AND tpc.finish_time >= #{query_start_date} ]]></if>
  <if test=" query_end_date != null and query_end_date != '' "><![CDATA[ AND tpc.finish_time < #{query_end_date} ]]></if> 
</sql>

<sql id="renewalDataTimeCondition">
  <if test=" query_start_date != null and query_start_date != '' "><![CDATA[ AND tce.insert_time >= #{query_start_date} ]]></if>
  <if test=" query_end_date != null and query_end_date != '' "><![CDATA[ AND tce.insert_time < #{query_end_date} ]]></if> 
</sql>
<select id="queryPolicyDetailinfoForCCB" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
       select tcm.policy_id as policy_id,
       tcm.policy_code as policy_code,
       tcm.apply_code as apply_code,
       tcm.validate_date as validate_date,
       tcm.lapse_date as lapse_date
  from APP___PAS__DBUSER.t_contract_master tcm
  left join ]]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
</if>
<if test="policy_code_list==null or policy_code_list.size()==0">
<![CDATA[dev_pas.t_policy_change tpc]]>
</if>
<![CDATA[
    on tpc.policy_id = tcm.policy_id
 where tpc.service_code in ('CT', 'AG')
    and tcm.submit_channel = #{channel_type}
    and tcm.service_bank = #{service_bank_branch}
   ]]>
  <include refid="policyCodeListCondition"></include>
  <include refid="policyChangeFiniTimeCondition"></include>
<![CDATA[union]]>
<![CDATA[
       select tcm.policy_id as policy_id,
       tcm.policy_code as policy_code,
       tcm.apply_code as apply_code,
       tcm.validate_date as validate_date,
       tcm.lapse_date as lapse_date
  from APP___PAS__DBUSER.t_contract_master tcm
  left join APP___PAS__DBUSER.t_contract_extend tce
    on tcm.policy_id = tce.policy_id
  where tcm.submit_channel = #{channel_type}
    and tcm.service_bank = #{service_bank_branch}
    and tce.policy_period > 1
 ]]>        
 <include refid="policyCodeListCondition"></include>
 <include refid="renewalDataTimeCondition"></include>
 </select>
</mapper>
