<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAbsCashValueDao">

	<sql id="absCashValueWhereCondition">
		<if test=" pay_due  != null "><![CDATA[ AND A.PAY_DUE = #{pay_due} ]]></if>
		<if test=" loancas_date  != null  and  loancas_date  != ''  "><![CDATA[ AND A.LOANCAS_DATE = #{loancas_date} ]]></if>
		<if test=" actual_date  != null  and  actual_date  != ''  "><![CDATA[ AND A.ACTUAL_DATE = #{actual_date} ]]></if>
		<if test=" plan_code != null and plan_code != ''  "><![CDATA[ AND <PERSON>.PLAN_CODE = #{plan_code} ]]></if>
		<if test=" main_list_id  != null "><![CDATA[ AND A.MAIN_LIST_ID = #{main_list_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->	
	<sql id="queryAbsCashValueByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryAbsCashValueByLoancasDateCondition">
		<if test=" loancas_date  != null "><![CDATA[ AND A.LOANCAS_DATE = #{loancas_date} ]]></if>
	</sql>	
	<sql id="queryAbsCashValueByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryAbsCashValueByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="queryAbsCashValueByPlanCodeCondition">
		<if test=" plan_code != null and plan_code != '' "><![CDATA[ AND A.PLAN_CODE = #{plan_code} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="addAbsCashValue"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ABS_CASH_VALUE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ABS_CASH_VALUE(
				PAY_DUE, LOANCAS_DATE, INSERT_TIME, ACTUAL_DATE, PLAN_CODE, UPDATE_TIME, MAIN_LIST_ID, 
				INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, CASH_VALUE, UPDATE_TIMESTAMP, BUSI_ITEM_ID, 
				INSERT_BY ) 
			VALUES (
				#{pay_due, jdbcType=NUMERIC}, #{loancas_date, jdbcType=DATE} , SYSDATE , #{actual_date, jdbcType=DATE} , #{plan_code, jdbcType=VARCHAR} , SYSDATE , #{main_list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{cash_value, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="deleteAbsCashValue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="updateAbsCashValue" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ABS_CASH_VALUE ]]>
		<set>
		<trim suffixOverrides=",">
		    PAY_DUE = #{pay_due, jdbcType=NUMERIC} ,
		    LOANCAS_DATE = #{loancas_date, jdbcType=DATE} ,
		    ACTUAL_DATE = #{actual_date, jdbcType=DATE} ,
			PLAN_CODE = #{plan_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    MAIN_LIST_ID = #{main_list_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="findAbsCashValueByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashValueByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findAbsCashValueByLoancasDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashValueByLoancasDateCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findAbsCashValueByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashValueByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findAbsCashValueByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashValueByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findAbsCashValueByPlanCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashValueByPlanCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

	<!-- 按map查询操作 -->
	<select id="findAllMapAbsCashValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="absCashValueWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllAbsCashValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="absCashValueWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

	<!-- 查询个数操作 -->
	<select id="findAbsCashValueTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE 1 = 1  ]]>
		<include refid="absCashValueWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="queryAbsCashValueForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PAY_DUE, B.LOANCAS_DATE, B.ACTUAL_DATE, B.PLAN_CODE, B.MAIN_LIST_ID, 
			B.POLICY_CODE, B.LIST_ID, B.CASH_VALUE, B.BUSI_ITEM_ID FROM (
					SELECT ROWNUM RN, A.PAY_DUE, A.LOANCAS_DATE, A.ACTUAL_DATE, A.PLAN_CODE, A.MAIN_LIST_ID, 
			A.POLICY_CODE, A.LIST_ID, A.CASH_VALUE, A.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="absCashValueWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 批处理查询 总记录数 -->
	<select id="PA_queryCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ABS_CASH_VALUE
		]]>
	</select>
	
	<!-- 查询收件人邮箱 -->
	<select id="PA_absCashValuefindEmail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.CONSTANTS_ID, T.CONSTANTS_KEY, T.CONSTANTS_VALUE, T.CONSTANTS_DESC, T.SUB_ID 
				    FROM DEV_PAS.T_CONSTANTS_INFO T 
				   WHERE T.CONSTANTS_KEY = 'ABSCASHVAL_NOTICEMAIL' ]]> 
	</select>
	
</mapper>
