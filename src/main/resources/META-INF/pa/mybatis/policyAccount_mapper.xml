<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyAccountDao">

	<sql id="PA_policyAccountWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" next_balance_date  != null  and  next_balance_date  != ''  "><![CDATA[ AND A.NEXT_BALANCE_DATE = #{next_balance_date} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" capitalized_date  != null  and  capitalized_date  != ''  "><![CDATA[ AND A.CAPITALIZED_DATE = #{capitalized_date} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" capital_balance  != null "><![CDATA[ AND A.CAPITAL_BALANCE = #{capital_balance} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_type  != null "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
		<if test=" balance_date  != null  and  balance_date  != ''  "><![CDATA[ AND A.BALANCE_DATE = #{balance_date} ]]></if>
		<if test=" regular_repay  != null "><![CDATA[ AND A.REGULAR_REPAY = #{regular_repay} ]]></if>
		<if test=" frozen_amount  != null "><![CDATA[ AND A.FROZEN_AMOUNT = #{frozen_amount} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" create_date  != null  and  create_date  != ''  "><![CDATA[ AND A.CREATE_DATE = #{create_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_account_status  != null "><![CDATA[ AND A.POLICY_ACCOUNT_STATUS = #{policy_account_status} ]]></if>
		<if test=" account_type_list != null and account_type_list.size()!=0">
			<![CDATA[ AND A.ACCOUNT_TYPE IN ]]>
			<foreach collection ="account_type_list" item="account_type_list" index="index" open="(" close=")" separator=",">#{account_type_list}</foreach>
		</if>
		
		<if test=" close_reason  != null "><![CDATA[ AND A.CLOSE_REASON = #{close_reason} ]]></if>
		<if test=" death_date  != null "><![CDATA[ AND A.DEATH_DATE = #{death_date} ]]></if>
	</sql>

    	
<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyAccountByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountByBalanceDateCondition">
		<if test=" balance_date  != null "><![CDATA[ AND A.BALANCE_DATE = #{balance_date} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountByCapitalizedDateCondition">
		<if test=" capitalized_date  != null "><![CDATA[ AND A.CAPITALIZED_DATE = #{capitalized_date} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyAccountByBusiItemIdCondition">
		<if test=" busi_item_id != null"><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id}]]></if>
		<if test=" account_type != null"><![CDATA[ AND A.account_type = #{account_type}]]></if>
	</sql>
		
	<sql id="queryPolicyAccountReceiveByPolicyCode">
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyAccount"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="account_id">
			SELECT APP___PAS__DBUSER.S_POLICY_ACCOUNT__ACCOUNT_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_ACCOUNT(
				 CLOSE_REASON,DEATH_DATE,MONEY_CODE, NEXT_BALANCE_DATE, INTEREST_CAPITAL, CAPITALIZED_DATE, ACCOUNT_ID, INSERT_TIME, 
				CAPITAL_BALANCE, INTEREST_SUM, UPDATE_TIME, ITEM_ID, ACCOUNT_TYPE, BALANCE_DATE, REGULAR_REPAY, 
				INSERT_TIMESTAMP, FROZEN_AMOUNT, UPDATE_BY, INTEREST_BALANCE, UPDATE_TIMESTAMP, CREATE_DATE, INSERT_BY, 
				BUSI_ITEM_ID, POLICY_ID, POLICY_ACCOUNT_STATUS ) 
			VALUES (
				 #{close_reason, jdbcType=VARCHAR} , #{death_date, jdbcType=DATE} ,#{money_code, jdbcType=VARCHAR} , #{next_balance_date, jdbcType=DATE} , #{interest_capital, jdbcType=NUMERIC} , #{capitalized_date, jdbcType=DATE} , #{account_id, jdbcType=NUMERIC} , SYSDATE 
				, #{capital_balance, jdbcType=NUMERIC} , #{interest_sum, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{account_type, jdbcType=NUMERIC} , #{balance_date, jdbcType=DATE} , #{regular_repay, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{frozen_amount, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{interest_balance, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{create_date, jdbcType=DATE} , #{insert_by, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{policy_account_status, jdbcType=NUMERIC} ) 
		 ]]> 
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyAccount" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE ACCOUNT_ID = #{account_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyAccount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    NEXT_BALANCE_DATE = #{next_balance_date, jdbcType=DATE} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    CAPITALIZED_DATE = #{capitalized_date, jdbcType=DATE} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
		    CAPITAL_BALANCE = #{capital_balance, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    ACCOUNT_TYPE = #{account_type, jdbcType=NUMERIC} ,
		    BALANCE_DATE = #{balance_date, jdbcType=DATE} ,
		    REGULAR_REPAY = #{regular_repay, jdbcType=NUMERIC} ,
		    FROZEN_AMOUNT = #{frozen_amount, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CREATE_DATE = #{create_date, jdbcType=DATE} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    POLICY_ACCOUNT_STATUS = #{policy_account_status, jdbcType=NUMERIC} ,
		    CLOSE_REASON = #{close_reason, jdbcType=VARCHAR} ,
		    DEATH_DATE = #{death_date, jdbcType=DATE} ,
		    FROZEN_DATE = #{frozen_date, jdbcType=DATE} ,
		    CLOSE_DATE = #{close_date, jdbcType=DATE}
		</trim>
		</set>
		<![CDATA[ WHERE ACCOUNT_ID = #{account_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyAccountByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, 
			NVL( (SELECT MAX(T.TRANS_TIME)
		             FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST T
		            WHERE T.ACCOUNT_ID = A.ACCOUNT_ID
		              AND T.TRANS_CODE = 6),
		           A.BALANCE_DATE) AS BALANCE_DATE,
			A.REGULAR_REPAY,A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE rownum=1  ]]>
		<include refid="PA_queryPolicyAccountByAccountIdCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>
	
	<select id="PA_findPolicyAccountByBalanceDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountByBalanceDateCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>
	
	<select id="PA_findPolicyAccountByCapitalizedDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountByCapitalizedDateCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>
	
	<select id="PA_findPolicyAccountByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountByItemIdCondition" />
		<include refid="PA_policyAccountWhereCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>
	
	<select id="PA_findPolicyAccountByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>
<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyAccountWhereCondition"/>
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS,A.FROZEN_DATE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyAccountWhereCondition"/>
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]> 
	</select>
	<!-- 保单质押贷款清偿查询 -->
	<select id="PA_findAllPolicyAccounts" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, B.repay_due_date,
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A left join APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM B on A.ACCOUNT_ID=B.ACCOUNT_ID WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyAccountWhereCondition"/>
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyAccountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_policyAccountWhereCondition"/>
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyAccountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CLOSE_REASON,B.DEATH_DATE,B.MONEY_CODE, B.NEXT_BALANCE_DATE, B.INTEREST_CAPITAL, B.CAPITALIZED_DATE, B.ACCOUNT_ID, 
			B.CAPITAL_BALANCE, B.INTEREST_SUM, B.ITEM_ID, B.ACCOUNT_TYPE, B.BALANCE_DATE, B.REGULAR_REPAY, 
			B.FROZEN_AMOUNT, B.INTEREST_BALANCE, B.CREATE_DATE, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.POLICY_ACCOUNT_STATUS FROM (
					SELECT ROWNUM RN, A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_policyAccountWhereCondition"/>
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 批处理账户结息条件 -->
	
	<!-- 可选查询条件 -->
	<sql id="PA_queryPolicyAccountByKexuan">	
		<if test="organ_code != null and organ_code != '' and organ_code !='null'">
			<![CDATA[ AND B.organ_code in (
			        select t.organ_code
                    from APP___PAS__DBUSER.T_udmp_org_rel t
                    start with t.organ_code = #{organ_code}
                    connect by prior t.organ_code = t.uporgan_code
			    	GROUP BY organ_code
		    	) 
			]]>
		</if>		
		<if test="policy_code != null and policy_code != '' and policy_code !='null'">
			<![CDATA[ AND B.policy_code = #{policy_code} ]]> </if>	
		<if test=" policy_account_state  != null and policy_account_state.size()!=0">
		<![CDATA[ AND A.POLICY_ACCOUNT_STATUS NOT IN ]]>
			<foreach collection ="policy_account_state" item="policy_account_state" index="index" open="(" close=")" separator=",">#{policy_account_state}</foreach>
		</if>
	</sql>
	
	
	<sql id="accountInterestCondition">		
		<if test=" policy_account_type_list  != null and policy_account_type_list.size()!=0">
		<![CDATA[ AND A.ACCOUNT_TYPE in ]]>
			<foreach collection ="policy_account_type_list" item="policy_account_type_list" index="index" open="(" close=")" separator=",">#{policy_account_type_list}</foreach>
		</if>
		
		<if test=" batch_time  != null  and  batch_time  != ''  "><![CDATA[ AND A.BALANCE_DATE < #{batch_time} ]]></if>
		
		<if test=" liability_state_list  != null and liability_state_list.size()!=0">
		<![CDATA[ AND B.LIABILITY_STATE in ]]>
			<foreach collection ="liability_state_list" item="liability_state_list" index="index" open="(" close=")" separator=",">#{liability_state_list}</foreach>
		</if>
	</sql> 
	
	
	
	<!-- 批处理账户结息查询所有操作 -->
	<select id="findAccountInterests" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN,
			       A.MONEY_CODE,
			       A.NEXT_BALANCE_DATE,
			       A.INTEREST_CAPITAL,
			       A.CAPITALIZED_DATE,
			       A.POLICY_ACCOUNT_STATUS,
			       A.ACCOUNT_ID,
			       A.CAPITAL_BALANCE,
			       A.INTEREST_SUM,
			       A.ITEM_ID,
			       A.ACCOUNT_TYPE,
			       A.BALANCE_DATE,
			       A.REGULAR_REPAY,
			       A.INTEREST_BALANCE,
			       A.CREATE_DATE,
			       A.BUSI_ITEM_ID,
			       A.POLICY_ID
			  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A
			  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
			    ON A.POLICY_ID = B.POLICY_ID
			 WHERE 1 = 1
			   AND ( ( (A.ACCOUNT_TYPE = 11 or A.ACCOUNT_TYPE = 12) and (B.LIABILITY_STATE = 1 or B.LIABILITY_STATE = 4) ) or 
				     (A.ACCOUNT_TYPE = 2 and B.LIABILITY_STATE = 1) )
			   AND A.POLICY_ACCOUNT_STATUS != 2
			   AND A.POLICY_ACCOUNT_STATUS != 3
			   AND NOT EXISTS (SELECT 'X'
                FROM APP___PAS__DBUSER.T_LOCK_POLICY TLP
                LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF TLSD
                  ON TLP.LOCK_SERVICE_ID = TLSD.LOCK_SERVICE_ID
               WHERE  TLSD.SUB_ID IN ('068', '067')
               	AND TLSD.LOCK_SERVICE_TYPE = 1
                 AND TLP.POLICY_CODE = B.POLICY_CODE)
			   AND MOD(A.POLICY_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
			]]>
		<if test=" batch_time  != null  and  batch_time  != ''  "><![CDATA[ AND A.BALANCE_DATE <= #{batch_time} ]]></if>
		<if test="organ_code != null and organ_code != '' and organ_code !='null'">
			<![CDATA[ AND B.organ_code in (
			        select t.organ_code
                    from APP___PAS__DBUSER.T_udmp_org_rel t
                    start with t.organ_code = #{organ_code}
                    connect by prior t.organ_code = t.uporgan_code
			    	GROUP BY organ_code
		    	) 
			]]>
		</if>		
		<if test="policy_code != null and policy_code != '' and policy_code !='null'">
			<![CDATA[ AND B.policy_code = #{policy_code} ]]> </if>
	</select>
	
	
	<!-- 批处理账户结息查询个数操作 -->
	<select id="findAccountInterestTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT COUNT(1)
			  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A
			  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
			    ON A.POLICY_ID = B.POLICY_ID
			 WHERE 1 = 1
			   AND ( ( (A.ACCOUNT_TYPE = 11 or A.ACCOUNT_TYPE = 12) and (B.LIABILITY_STATE = 1 or B.LIABILITY_STATE = 4) ) or 
				     (A.ACCOUNT_TYPE = 2 and B.LIABILITY_STATE = 1) )
			   AND A.POLICY_ACCOUNT_STATUS != 2
			   AND A.POLICY_ACCOUNT_STATUS != 3
			   AND NOT EXISTS (SELECT 'X'
                FROM APP___PAS__DBUSER.T_LOCK_POLICY TLP
                LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF TLSD
                  ON TLP.LOCK_SERVICE_ID = TLSD.LOCK_SERVICE_ID
               WHERE  TLSD.SUB_ID IN ('068', '067')
               	AND TLSD.LOCK_SERVICE_TYPE = 1
                 AND TLP.POLICY_CODE = B.POLICY_CODE)
			]]>
		<if test=" batch_time  != null  and  batch_time  != ''  "><![CDATA[ AND A.BALANCE_DATE <= #{batch_time} ]]></if>
		<if test="organ_code != null and organ_code != '' and organ_code !='null'">
			<![CDATA[ AND B.organ_code in (
			        select t.organ_code
                    from APP___PAS__DBUSER.T_udmp_org_rel t
                    start with t.organ_code = #{organ_code}
                    connect by prior t.organ_code = t.uporgan_code
			    	GROUP BY organ_code
		    	) 
			]]>
		</if>		
		<if test="policy_code != null and policy_code != '' and policy_code !='null'">
			<![CDATA[ AND B.policy_code = #{policy_code} ]]> </if>
	</select>
	
	<select id="PA_findPolicyAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOSE_REASON,A.DEATH_DATE,A.MONEY_CODE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.NEXT_BALANCE_DATE,
			A.INTEREST_BALANCE, A.CREATE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.POLICY_ACCOUNT_STATUS,A.FROZEN_DATE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_policyAccountWhereCondition" />		
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>
	
		<select id="PA_findPolicyAccountByCapFro" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.* FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE 1 = 1  AND A.INTEREST_CAPITAL IS NOT NULL AND A.FROZEN_AMOUNT IS NOT NULL]]>
		<include refid="PA_policyAccountWhereCondition" />		
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]>
	</select>
	
	<!-- 累计升息基本信息查询 -->
	<select id="queryPolicyAccountMessage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CM.POLICY_CODE,CM.POLICY_ID,CBP.BUSI_PROD_CODE,CBP.OLD_POL_NO,(CASE WHEN CBP.MASTER_BUSI_ITEM_ID IS NULL
                        THEN DECODE(CBP.OLD_POL_NO ,NULL , CBP.BUSI_ITEM_ID,CBP.OLD_POL_NO )
                   ELSE  (SELECT DECODE(P.OLD_POL_NO,NULL,P.BUSI_ITEM_ID,P.OLD_POL_NO) FROM DEV_PAS.T_CONTRACT_BUSI_PROD P  WHERE P.BUSI_ITEM_ID = CBP.MASTER_BUSI_ITEM_ID)
				   END ) MASTER_BUSI_ITEM_ID,ACC.*,BP.PRODUCT_NAME_SYS,
			(SELECT CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER WHERE CUSTOMER_ID=
				(SELECT CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER WHERE POLICY_ID=CM.POLICY_ID)) APPNTNAME,
			(SELECT CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER WHERE CUSTOMER_ID=
				(SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE POLICY_ID=CM.POLICY_ID AND ROWNUM=1)) INSUREDNAME,
			(SELECT STATUS_DESC FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STATUS WHERE STATUS_CODE=ACC.POLICY_ACCOUNT_STATUS) ACCSTATE,
			(SELECT NVL(SUM(INTEREST_CAPITAL),0) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE POLICY_ID=CM.POLICY_ID AND ACCOUNT_TYPE in ('11','2')) + 
			(SELECT NVL(SUM(INTEREST_CAPITAL),0) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE POLICY_ID=CM.POLICY_ID) ACCBALA
			FROM (	
				SELECT 'ACCOUNT' AS FROM_TABLE,ACCOUNT_ID,POLICY_ID,BUSI_ITEM_ID,NVL(CREATE_DATE,INSERT_TIME) AS ACCFOUNDDATE,
				'' AS INSUACCNO,UPDATE_TIME,BALANCE_DATE AS BALADATE,INTEREST_CAPITAL AS INSUACCBALA,POLICY_ACCOUNT_STATUS,
				(SELECT MAX(TRANS_TIME) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST WHERE ACCOUNT_ID = A.ACCOUNT_ID) AS MODIFYDATE
				FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE ACCOUNT_TYPE in ('11','2')
				UNION
				SELECT 'INVEST' AS FROM_TABLE,LIST_ID AS ACCOUNT_ID,POLICY_ID,BUSI_ITEM_ID,NVL(CREATE_DATE,INSERT_TIME) AS ACCFOUNDDATE,
				ACCOUNT_CODE AS INSUACCNO,UPDATE_TIME,SETTLE_DUE_DATE AS BALADATE,INTEREST_CAPITAL AS INSUACCBALA,null AS POLICY_ACCOUNT_STATUS,
				(SELECT MAX(DEAL_TIME) FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE LIST_ID = I.LIST_ID) AS MODIFYDATE
				FROM APP___PAS__DBUSER.T_CONTRACT_INVEST I
			) ACC 
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM ON ACC.POLICY_ID=CM.POLICY_ID
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP ON CBP.BUSI_ITEM_ID=ACC.BUSI_ITEM_ID
			LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PRD_ID=BP.BUSINESS_PRD_ID
			WHERE CM.POLICY_CODE=#{policy_code}
		]]>
	</select>
	<!-- 累计升息基本信息查询 当前账户金额(查的是结算日期之后的金额总和) -->
	<select id="queryPolicyAccountAccBala" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(B.MONEY) AS ACCBALA FROM 
			(SELECT #{baladate} BALADATE,POLICY_ID,POLICY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER WHERE POLICY_CODE=#{policy_code}) A,
			(SELECT C.POLICY_ID,C.LIST_ID AS ACCOUNT_ID,FT.TRANS_AMOUNT AS MONEY,FT.DEAL_TIME AS PAYDATE
			  FROM APP___PAS__DBUSER.T_FUND_TRANS FT,APP___PAS__DBUSER.T_CONTRACT_INVEST C WHERE C.LIST_ID=FT.LIST_ID
			 UNION ALL SELECT C.POLICY_ID,C.LIST_ID AS ACCOUNT_ID,FS.INTEREST AS MONEY,FS.SETTLE_DATE AS PAYDATE 
			  FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT FS,APP___PAS__DBUSER.T_CONTRACT_INVEST C WHERE FS.INVEST_ID=C.LIST_ID
			 UNION ALL SELECT P.POLICY_ID,P.ACCOUNT_ID,TL.TRANS_AMOUNT AS MONEY,TL.TRANS_TIME AS PAYDATE 
			  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TL,APP___PAS__DBUSER.T_POLICY_ACCOUNT P WHERE TL.ACCOUNT_ID=P.ACCOUNT_ID
			) B
			WHERE A.POLICY_ID=B.POLICY_ID
			AND B.PAYDATE>(CASE WHEN EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT FS,APP___PAS__DBUSER.T_CONTRACT_INVEST CI 
				WHERE FS.INVEST_ID=CI.LIST_ID AND CI.POLICY_ID=A.POLICY_ID) 
				OR EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TL,APP___PAS__DBUSER.T_POLICY_ACCOUNT P 
				WHERE TL.ACCOUNT_ID=P.ACCOUNT_ID AND P.POLICY_ID=A.POLICY_ID AND TL.TRANS_CODE='6') 
				THEN BALADATE ELSE BALADATE-1 END)
		]]>
	</select>
	
		<!-- 客户账户领取查询 -->
       <select id="queryPolicyAccountReceive" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT   A.POLICY_CODE,C.ACCOUNT_ID,B.BUSI_PROD_CODE,D.PRODUCT_NAME_SYS,C.INTEREST_CAPITAL,E.CUSTOMER_ID,C.INTEREST_CAPITAL FROM  APP___PAS__DBUSER.T_CONTRACT_MASTER A
			LEFT JOIN   APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD  B  ON A.POLICY_ID=B.POLICY_ID 
			LEFT JOIN  APP___PAS__DBUSER.T_BUSINESS_PRODUCT  D  ON  B.BUSI_PROD_CODE = D.PRODUCT_CODE_SYS
			LEFT  JOIN APP___PAS__DBUSER.T_POLICY_ACCOUNT C    ON   A.POLICY_ID=C.POLICY_ID AND  B.BUSI_ITEM_ID=C.BUSI_ITEM_ID
			LEFT  JOIN  APP___PAS__DBUSER.T_INSURED_LIST  E  ON A.POLICY_ID = E.POLICY_ID 
			WHERE  C.ACCOUNT_ID IS NOT NULL  		
		]]>
		<include refid="queryPolicyAccountReceiveByPolicyCode" />
	</select>	
	<!--by zhaoyoan_wb 通过保单号查询“金钱柜累积生息”账户信息 -->
    <select id="PA_findAccrueAccountIdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.ACCOUNT_ID,A.POLICY_ID,A.BUSI_ITEM_ID,A.CREATE_DATE,A.INSERT_TIME,
			(SELECT STATUS_DESC FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STATUS WHERE STATUS_CODE=A.POLICY_ACCOUNT_STATUS) AccState,
      		(SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD 
      			WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS busi_prd_id
		    FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A
		    WHERE A.ACCOUNT_TYPE = '11' 
	    	AND A.POLICY_ID=(SELECT POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER 
	    		WHERE POLICY_CODE=#{policy_code}) 
		]]>
<!-- 老核心没有判断是否金钱柜险种，之前加上是因为端到端测试时测试人员说不应该查出来非金钱柜险种的记录，现先删掉，以后有问题需要发邮件确认
	AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID AND BUSI_PRD_ID IN(629,630,655)) 
	-->
	</select>	
	<!--by zhaoyoan_wb 通过账户id查询账户信息（“累积生息”账户类型） -->
    <select id="PA_queryAccrueInterestAccountInfoByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.TRANS_AMOUNT,A.TRANS_TIME,A.UNIT_NUMBER SerialNo,A.STREAM_ID,A.POLICY_CHG_ID,A.TRANS_CODE,A.ACCOUNT_ID,
			(SELECT STATUS_DESC FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STATUS WHERE STATUS_CODE=
				(SELECT POLICY_ACCOUNT_STATUS FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE ACCOUNT_ID=A.ACCOUNT_ID)) AccState,
			((SELECT NVL(SUM(TRANS_AMOUNT),0) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST 
				WHERE A.ACCOUNT_ID=ACCOUNT_ID AND TRANS_TIME<=A.TRANS_TIME AND TRANS_TYPE='1')-
			 (SELECT NVL(SUM(TRANS_AMOUNT),0) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST 
				WHERE A.ACCOUNT_ID=ACCOUNT_ID AND TRANS_TIME<=A.TRANS_TIME AND TRANS_TYPE='2')) AccBala
			FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A 
			WHERE A.ACCOUNT_ID=#{account_id} 
			AND A.TRANS_AMOUNT <> 0
		]]>
		<if test=" order_context != null and order_context != ''  "><![CDATA[
			${order_context}
		]]></if>
	</select>	
	
	<select id="PA_queryTransactionCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from APP___PAS__DBUSER.T_TRANSACTION_CODE where trans_code=#{trans_code}		
		]]>
	</select>
	<!--by zhaoyoan_wb 根据 保单id 查询保单信息（万能险账户信息） -->
	<select id="PA_queryUniversalAccountInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_ID,
			(SELECT BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD 
			  WHERE A.POLICY_ID=POLICY_ID AND A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS busi_prod_code,
			(SELECT PRODUCT_NAME_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT WHERE BUSINESS_PRD_ID=
			  (SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD 
			    WHERE A.POLICY_ID=POLICY_ID AND A.BUSI_ITEM_ID=BUSI_ITEM_ID)) AS product_name_sys,
			(SELECT TOTAL_PREM_AF FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT 
			  WHERE A.POLICY_ID=POLICY_ID AND A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS total_prem_af,
			A.ACCOUNT_CODE,
			(SELECT FUND_NAME FROM APP___PAS__DBUSER.T_FUND 
			  WHERE FUND_CODE=A.ACCOUNT_CODE) AS invest_account_name,
			nvl(A.CREATE_DATE,A.INSERT_TIME) AS CREATE_DATE,
			(SELECT F.DEAL_TIME FROM APP___PAS__DBUSER.T_FUND_TRANS F 
			  WHERE F.FUND_CODE=A.ACCOUNT_CODE AND F.POLICY_ID=C.POLICY_ID AND F.DEAL_TIME IS NOT NULL AND ROWNUM=1 
			  AND NOT EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT WHERE F.FUND_CODE=A.ACCOUNT_CODE AND F.POLICY_ID=C.POLICY_ID
			  AND F.DEAL_TIME<DEAL_TIME)) AS settle_date, 
			A.INTEREST_CAPITAL,
			(SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER WHERE A.POLICY_ID=POLICY_ID) AS customer_id,
			(SELECT BANK_NAME FROM APP___PAS__DBUSER.T_BANK WHERE BANK_CODE=B.ACCOUNT_BANK) AS bank_name,
			B.ACCOUNT,
			B.ACCOUNT_NAME 
	        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,APP___PAS__DBUSER.T_CONTRACT_INVEST A,APP___PAS__DBUSER.T_PAYER_ACCOUNT B
			WHERE C.POLICY_ID=#{policy_id}
	        and A.POLICY_ID=C.POLICY_ID 
	        and A.POLICY_ID=B.POLICY_ID
		]]>
	</select>
	<!-- 根据 保单号 查询贷款记录 -->
	<select id="PA_queryLoanListByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.* FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A WHERE POLICY_ID=
				(SELECT POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER WHERE POLICY_CODE=#{policy_code} AND ROWNUM=1)
		 	AND ROWNUM <=  1000  ]]>
		<![CDATA[ ORDER BY A.ACCOUNT_ID ]]> 
	</select>
	<!--by zhaoyoan_wb 根据 保单号和险种号 查询账户信息 -->
	<select id="PA_queryAccountByPolicyCodeAndBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.* 
			FROM DEV_PAS.T_POLICY_ACCOUNT A WHERE 1=1 
			AND A.BUSI_ITEM_ID=(SELECT BUSI_ITEM_ID FROM DEV_PAS.T_CONTRACT_BUSI_PROD 
				WHERE BUSI_PROD_CODE=#{busi_prod_code} AND POLICY_CODE=#{policy_code})
		]]>
	</select>
	<!-- 客户账户领取查询(累计生息账户) -->
	<select id="findAllPolicyAccountAndBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.MONEY_CODE, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.ACCOUNT_ID, 
			A.CAPITAL_BALANCE, A.INTEREST_SUM, A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.CREATE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_ACCOUNT_STATUS,B.BUSI_PROD_CODE FROM dev_pas.t_policy_account A,dev_pas.t_contract_busi_prod B WHERE A.account_type in ('11','2') AND B.policy_id=#{policy_id} 
     and A.policy_id=B.policy_id and B.MASTER_BUSI_ITEM_ID IS NULL ]]>
	</select>
	<!-- 保险账户其他信息轨迹信息接口 -->
	<select id="queryAccountByPolicyCodeAndBusiitemid" resultType="java.util.Map" parameterType="java.util.Map" >
  <![CDATA[ select A.POLICY_CODE,A.BUSI_ITEM_ID,A.BUSI_PROD_CODE,B.CAPITAL_BALANCE AS SumPay,B.INTEREST_CAPITAL AS InsuAccBala,
      ADD_MONTHS(B.BALANCE_DATE,1)AS BalaDate,B.INSERT_TIME,B.UPDATE_TIME,B.ACCOUNT_ID
    from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,APP___PAS__DBUSER.T_POLICY_ACCOUNT B 
       WHERE A.BUSI_ITEM_ID=B.BUSI_ITEM_ID AND A.POLICY_CODE=#{POLICY_CODE} AND NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{BUSI_ITEM_ID} 
UNION SELECT A.POLICY_CODE,A.BUSI_ITEM_ID,A.BUSI_PROD_CODE,B.TOTAL_PREM AS SumPay,B.INTEREST_CAPITAL AS InsuAccBala,
    B.SETTLE_DUE_DATE AS BalaDate,B.INSERT_TIME,B.UPDATE_TIME,B.LIST_ID AS ACCOUNT_ID
 FROM  APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,APP___PAS__DBUSER.T_CONTRACT_INVEST B 
   WHERE A.BUSI_ITEM_ID=B.BUSI_ITEM_ID AND A.POLICY_CODE=#{POLICY_CODE} AND NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{BUSI_ITEM_ID}  ]]>
	

	</select>

	<select id="queryBalaDateByPolicyCodeAndContractMaster" resultType="java.util.Map" parameterType="java.util.Map" >
  <![CDATA[ 
  SELECT * FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.POLICY_CODE=#{POLICY_CODE}
   ]]>	
	</select>
	<!-- 通过保单id和险种id查询保险帐户信息 -->
	<select id="queryInsuAccountInfoForRI" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 'policy' AS FROMTYPE,A.ACCOUNT_ID,A.INTEREST_CAPITAL,A.INSERT_TIME,A.UPDATE_TIME,
			(SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST IL WHERE IL.LIST_ID=
				(SELECT INSURED_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED 
					WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID AND ORDER_ID='1')) AS insured_no,
			(SELECT SUM(TL.TRANS_AMOUNT) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TL 
				WHERE TL.ACCOUNT_ID=A.ACCOUNT_ID AND TL.TRANS_TYPE='1' AND TL.TRANS_TIME=
					(SELECT MIN(TL2.TRANS_TIME) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TL2 
						WHERE TL2.ACCOUNT_ID=TL.ACCOUNT_ID AND TL2.TRANS_TYPE='1')) AS last_acc_bala
			FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A
			WHERE A.POLICY_ID=#{policy_id} AND A.BUSI_ITEM_ID=#{busi_item_id} 
			UNION
			SELECT 'invest' AS FROMTYPE,B.LIST_ID AS ACCOUNT_ID,B.INTEREST_CAPITAL,B.INSERT_TIME,B.UPDATE_TIME,
			(SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST IL WHERE IL.LIST_ID=
				(SELECT INSURED_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED 
					WHERE BUSI_ITEM_ID=B.BUSI_ITEM_ID AND ORDER_ID='1')) AS insured_no,
			(SELECT SUM(FT.TRANS_AMOUNT) FROM APP___PAS__DBUSER.T_FUND_TRANS FT 
				WHERE FT.LIST_ID=B.LIST_ID AND FT.TRANS_TYPE='2' AND FT.DEAL_TIME=
					(SELECT MIN(FT2.DEAL_TIME) FROM APP___PAS__DBUSER.T_FUND_TRANS FT2 
						WHERE FT2.LIST_ID=FT.LIST_ID AND FT2.TRANS_TYPE='2')) AS last_acc_bala
			FROM APP___PAS__DBUSER.T_CONTRACT_INVEST B
			WHERE B.POLICY_ID=#{policy_id} AND B.BUSI_ITEM_ID=#{busi_item_id} 
		]]>
	</select>
	<!-- 查询是否存在贷款自垫是否未清偿 -->
	<select id="queryAllPolicyAccounts" resultType="java.util.Map" parameterType="java.util.Map">
	
	    SELECT * FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT TPA WHERE TPA.REGULAR_REPAY = '0'
	    AND TPA.ACCOUNT_TYPE = '4'
	    AND TPA.POLICY_ID = #{policy_id}
	    UNION
	    SELECT * FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT TPA WHERE TPA.REGULAR_REPAY = '0'
	    AND  ACCOUNT_TYPE = '5'
	    AND TPA.POLICY_ID = #{policy_id}
		
	</select>
	
	<!-- 查询是否存在贷款自垫是否未清偿 -->
	<select id="queryUnliquidatedByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	   SELECT A.INTEREST_CAPITAL, A.POLICY_ID, A.BUSI_ITEM_ID, A.ITEM_ID
       FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A
       WHERE A.ACCOUNT_TYPE = '5'
       AND A.INTEREST_CAPITAL <> 0
       AND A.POLICY_ID = #{policy_id}]]>
	</select>

    <select id="PA_findAllPolicyAccountMedicalInsurance" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[SELECT A.NEXT_ACCOUNT AS NEXT_ACCOUNT,
                        A.NEXT_ACCOUNT_NAME AS NEXT_ACCOUNT_NAME,
                        A.NEXT_ACCOUNT_BANK AS NEXT_ACCOUNT_BANK,
                        (SELECT TMT.PAY_ORDER_NAME
                           FROM DEV_PAS.T_MEDICAL_PAY_ORDER_TYPE TMT
                           WHERE A.MEDICAL_PAY_ORDER_NEXT = TMT.PAY_ORDER_CODE) AS pay_next,
                        A.MEDICAL_NO_NEXT AS MEDICAL_NO_NEXT
                   FROM DEV_PAS.T_CS_PAYER_ACCOUNT A,DEV_PAS.T_CS_POLICY_CHANGE CHA,DEV_PAS.T_CS_ACCEPT_CHANGE ACC
                  WHERE A.CHANGE_ID = CHA.CHANGE_ID
                    AND ACC.CHANGE_ID = CHA.CHANGE_ID
                    AND CHA.ORGAN_CODE LIKE '8622%'
                    AND A.PAY_MODE = '18'
                    AND A.MEDICAL_PAY_ORDER_NEXT = '1'
                    AND A.OLD_NEW = #{old_new}
                    AND CHA.CHANGE_ID = #{change_id}]]>
    </select>
    
    
    <select id="PA_queryNoPayOffLoanInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
       select  pa.capital_balance,pa.regular_repay,pa.account_type
       from APP___PAS__DBUSER.t_policy_account pa,
      	    APP___PAS__DBUSER.t_policy_account_stream ast
         where pa.account_id=ast.account_id
         and pa.account_type = '4'
         and ast.regular_repay='0'
         and pa.policy_id=#{policy_id}
       
       ]]>
	</select>
    
        <select id="PA_queryLJSXAccount" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
       SELECT B.TRANS_TIME, A.POLICY_ID
			  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A, APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST B
			  WHERE A.ACCOUNT_ID = B.ACCOUNT_ID
			   AND A.ACCOUNT_TYPE = 11
			   AND B.TRANS_CODE = '7'
			   AND A.POLICY_ID =#{policy_id}
               AND TO_CHAR(B.TRANS_TIME,'yyyy')= #{edorTypeAppDate, jdbcType=VARCHAR}
       ]]>
	</select>
	<!--柜面自助:查询保单原账号信息接口  -->
	<select id="queryAccountNoInfosByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[
	select pc.account, pc.next_account, cm.policy_code
  from APP___PAS__DBUSER.t_payer_account pc,
       APP___PAS__DBUSER.t_contract_master cm

 where pc.policy_id = cm.policy_id
   and cm.policy_code = #{policy_code}
	 ]]>
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if>
	 </select>
	 
	<!-- 按map查询操作(查询保单贷款账户最新还款日期) -->
	<select id="PA_queryMaxActRepayDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT MAX(TPAS.ACT_REPAY_DATE) ACT_REPAY_DATE
			  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        TPA,
			       APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TPAS
			 WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
			   AND TPA.ACCOUNT_TYPE = '4'
			   AND TPAS.REGULAR_REPAY = 1
			   AND TPAS.BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	<!--保单贷款续贷列表查询:P00001900382查询保单下险种贷款信息  -->
	<select id="PA_queryLoanContractBusiProdInfos" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	select A.CAPITAL_BALANCE,D.BUSI_ITEM_ID,
      to_char(B.LOAN_START_DATE,'yyyy-MM-dd') as START_DATE,
      to_char(B.REPAY_DUE_DATE,'yyyy-MM-dd') as END_DATE,
       (select C.LOAN_RATE
          from APP___PAS__DBUSER.t_Policy_Account_Stream_Rate C
         where C.STREAM_ID = B.STREAM_ID
           AND C.TIME_PERIDO_CODE = 1) AS LOANRATE,
       (select C.LOAN_RATE
          from APP___PAS__DBUSER.t_Policy_Account_Stream_Rate C
         where C.STREAM_ID = B.STREAM_ID
           AND C.TIME_PERIDO_CODE = 2 ) AS OVERLOANRATE,
       (SELECT E.PRODUCT_NAME_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT E WHERE E.BUSINESS_PRD_ID=D.BUSI_PRD_ID ) AS PRODUCT_NAME_SYS,
       (SELECT E.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT E WHERE E.BUSINESS_PRD_ID=D.BUSI_PRD_ID ) AS PRODUCT_CODE_SYS,
       (CASE 
         WHEN D.MASTER_BUSI_ITEM_ID IS NOT NULL
           THEN
             (SELECT E.PRODUCT_NAME_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT E, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD F
              WHERE E.BUSINESS_PRD_ID=F.BUSI_PRD_ID
                AND F.BUSI_ITEM_ID = D.MASTER_BUSI_ITEM_ID)
       END) AS MAIN_RISK_PRODUCT_NAME_SYS,
       (CASE 
         WHEN D.MASTER_BUSI_ITEM_ID IS NOT NULL
           THEN
             (SELECT E.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT E, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD F
              WHERE E.BUSINESS_PRD_ID=F.BUSI_PRD_ID
                AND F.BUSI_ITEM_ID = D.MASTER_BUSI_ITEM_ID)
       END) AS MAIN_RISK_PRODUCT_CODE_SYS,
       (SELECT TRANS_AMOUNT FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST WHERE ACCOUNT_ID = A.ACCOUNT_ID AND TRANS_CODE = '35' AND ROWNUM = 1) 
       AS AGENT_DEDUCT_TAX
  from APP___PAS__DBUSER.t_policy_account A,
       APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM B,
       APP___PAS__DBUSER.t_Contract_Busi_Prod D
 where D.POLICY_CODE=#{policy_code}
   AND A.ACCOUNT_ID = B.ACCOUNT_ID
   AND A.ACCOUNT_TYPE=4
   AND B.REGULAR_REPAY = 0
   AND B.BUSI_ITEM_ID=D.BUSI_ITEM_ID
   AND B.POLICY_ID=D.POLICY_ID
	
	]]>
	</select>
	<!--根据保单ID查询是否在犹豫期 -->
	<select id="PA_querySelfPrepaymentInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	         SELECT T.REGULAR_REPAY,
	                T.ACCOUNT_ID,
	                T.POLICY_ID,
	                T.BUSI_ITEM_ID
             FROM DEV_PAS.T_POLICY_ACCOUNT T
             WHERE T.ACCOUNT_TYPE = '5'
             AND T.REGULAR_REPAY = '0'
             AND T.POLICY_ID = #{policy_id}
	
	]]>
	</select>
	
	<!-- 根据保单ID查询保单贷款未清偿信息 -->
	<select id="PA_queryAccountByPolicyID" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
       select pa.capital_balance, pa.regular_repay, pa.account_type,ast.LOAN_START_DATE,ast.interest_start_date
		  from APP___PAS__DBUSER.t_policy_account        pa,
		       APP___PAS__DBUSER.t_policy_account_stream ast
		 where pa.account_id = ast.account_id
		   and pa.account_type = '4'
			 and pa.interest_capital > '0'
			 and ast.regular_repay = '0'
		   and pa.policy_id =#{policy_id}
       ]]>
	</select>

	<!-- 根据保单ID查询保单是否存在贷款账户 -->
	<select id="PA_queryAccountDkByPolicyID" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
       select pa.capital_balance, pa.regular_repay, pa.account_type
		  from APP___PAS__DBUSER.t_policy_account        pa
		 where pa.account_type = '4' 
		   and pa.policy_id =#{policy_id}
       ]]>
	</select>

	<!-- 累积生息账户领取详情接口-查询累积生息账户信息 -->
	<select id="PA_findAllPolicyAccountForNsPolicy" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			    SELECT ROWNUM RN,
				       TPA.POLICY_ID, /*保单ID*/
				       TPA.ACCOUNT_TYPE, /*账户类型*/
				       TCBP.BUSI_PROD_CODE, /*险种代码*/
				       TBP.PRODUCT_NAME_SYS, /*险种名称*/
				       TPA.ACCOUNT_ID, /*账户代码*/
				       (SELECT A.ACCOUNT_NAME
				          FROM DEV_PAS.T_POLICY_ACCOUNT_TYPE A
				         WHERE A.ACCOUNT_TYPE = TPA.ACCOUNT_TYPE) AS ACCOUNT_NAME, /*账户名称*/
				       TPA.POLICY_ACCOUNT_STATUS, /*账户状态*/
				       (SELECT B.STATUS_DESC FROM DEV_PAS.T_POLICY_ACCOUNT_STATUS B WHERE B.STATUS_CODE = TPA.POLICY_ACCOUNT_STATUS ) AS STATUS_DESC,
				       TPA.INTEREST_CAPITAL /*账户余额*/
				  FROM DEV_PAS.T_POLICY_ACCOUNT TPA
				 INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
				    ON TPA.POLICY_ID = TCM.POLICY_ID
				 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				    ON TCM.POLICY_ID = TCBP.POLICY_ID
				   AND TPA.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
				    ON TBP.PRODUCT_CODE_SYS = TCBP.BUSI_PROD_CODE
				 WHERE (TPA.ACCOUNT_TYPE = '11' OR TPA.ACCOUNT_TYPE = '2')
 				   AND TCM.POLICY_CODE = #{policy_code} 
		 ]]>
	</select>

	<!-- 累积生息账户领取详情接口-查询是否发生累积生息账户领取 -->
	<select id="PA_findAllReceivePolicyAccount" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[   SELECT TPA.POLICY_ID, TPA.ACCOUNT_ID, TPATL.TRANS_CODE
		          FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT            TPA,
		               APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TPATL,
		               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
		         WHERE 1 = 1
		           AND TCBP.POLICY_ID = TPA.POLICY_ID
		           AND TCBP.BUSI_ITEM_ID = TPA.BUSI_ITEM_ID
		           AND TPA.ACCOUNT_ID = TPATL.ACCOUNT_ID
		           AND TPATL.ACCOUNT_ID = #{account_id}
		           AND TPATL.TRANS_CODE = '7' 
		           AND TCBP.BUSI_PROD_CODE IN ('********','********','********','********','********','********','********')
		           AND to_char(TPATL.TRANS_TIME,'yyyy') = to_char(sysdate,'yyyy')
		           AND TPA.POLICY_ID = #{policy_id} 
		 ]]>
	</select>

	<!-- 根据保单id查询累积生息账户余额 -->
	<select id="PA_findPolicyAccountByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select tcpa.*
		  from APP___PAS__DBUSER.t_cs_policy_change cpc,
		       APP___PAS__DBUSER.t_cs_accept_change cac,
		       APP___PAS__DBUSER.t_cs_application   tcp,
		       APP___PAS__DBUSER.T_cs_POLICY_ACCOUNT tcpa
		 where 1=1 
		 	   and cpc.policy_id = #{policy_id}
		       and cpc.accept_id = cac.accept_id
		       and cac.change_id = tcp.change_id
		       and tcpa.change_id = tcp.change_id
		       and cac.service_code = 'PU'
		       and cac.accept_status = '18'
		       and tcpa.old_new= '1'
		 ]]>
	</select>




	<!-- 查询累积生息账户领取详情信息 -->
	<select id="PA_queryAIDeitail" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		    SELECT TCBP.BUSI_ITEM_ID,
		           TCBP.OLD_POL_NO,
		           (SELECT TPL.product_name 
	                  FROM APP___PDS__DBUSER.T_PRODUCT_LIFE TPL ,
	                       APP___PAS__DBUSER.T_CONTRACT_PRODUCT  TCP
	                 WHERE TPL.PRODUCT_ID = TCP.PRODUCT_ID 
	                   AND TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID 
	                   AND TCP.POLICY_CODE = TCBP.POLICY_CODE 
	                   AND ROWNUM = 1) AS PRODUCT_NAME,
		           TPA.CREATE_DATE,
		           TPA.BALANCE_DATE,
		           TPA.ACCOUNT_TYPE,
		    	   (SELECT TBP.PRODUCT_NAME_SYS 
			          FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP 
			         WHERE TBP.PRODUCT_CODE_SYS = TCBP.BUSI_PROD_CODE) BUSI_ITEM_NAME, 
			       TCBP.BUSI_PROD_CODE, 
			       (SELECT TPAT.ACCOUNT_NAME 
			          FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TYPE TPAT 
			         WHERE TPAT.ACCOUNT_TYPE = TPA.ACCOUNT_TYPE ) ACCOUNT_NAME,     
			       TPA.INTEREST_CAPITAL, 
			       (SELECT TPAS.STATUS_DESC 
			          FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STATUS TPAS 
			         WHERE TPAS.STATUS_CODE = TPA.POLICY_ACCOUNT_STATUS ) ACCOUNT_STATE 
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER                 TCM, 
			       APP___PAS__DBUSER.T_POLICY_ACCOUNT                  TPA, 
			       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD              TCBP 
			 WHERE TCM.POLICY_ID = TPA.POLICY_ID 
			   AND TPA.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID 
			   AND TPA.BUSI_ITEM_ID = #{busi_item_id}
		 ]]>
	</select>
	
	<!-- 根据保单ID查询保单贷款未清偿信息 -->
	<select id="PA_queryLoanAccountByPolicyID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT PA.CAPITAL_BALANCE,
			   PA.ACCOUNT_TYPE,
			   PA.INTEREST_CAPITAL,
			   AST.LOAN_START_DATE,
			   AST.INTEREST_START_DATE
			FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT PA,
			   APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM AST
			WHERE PA.ACCOUNT_ID = AST.ACCOUNT_ID
			AND PA.ACCOUNT_TYPE = '4'
			AND PA.INTEREST_CAPITAL > '0'
			AND AST.REGULAR_REPAY = '0'
			AND PA.POLICY_ID = #{policy_id}
			AND PA.POLICY_ACCOUNT_STATUS IN ('1', '3')
       ]]>
       <if test=" busi_item_id  != null "><![CDATA[ AND PA.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 累计生息账户总数 -->
    <select id="findLiveAccountTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[  
        SELECT COUNT(1)
      		FROM DEV_PAS.T_POLICY_ACCOUNT PT
 				INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
    			ON PT.POLICY_ID = CM.POLICY_ID
 				WHERE (PT.ACCOUNT_TYPE = '11' OR PT.ACCOUNT_TYPE = '2')
        ]]>
        <!-- 查询条件-->
        <if test=" policy_code != null and policy_code  !='' ">
        	<![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]>
        </if>
    </select>
    
    <!-- 累计生息账户分页查询 -->
    <select id="findLiveAccountPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[  
        SELECT 
     		 B.ACCOUNT_ID,
      		 B.POLICY_ID, 
       		 (select account_name from DEV_PAS.T_POLICY_ACCOUNT_TYPE t 
       		 		where t.account_type=B.ACCOUNT_TYPE) as accountTypeName, 
       		 B.POLICY_ACCOUNT_STATUS, 
             B.ACCOUNT_DATE,
             B.INTEREST_CAPITAL, 
       		 B.BUSI_ITEM_ID,
       		 B.ITEM_ID,
       		 B.BALANCE_DATE,
       		 B.INTEREST_BALANCE,
       		 B.POLICY_CODE,
       		 B.BUSI_PROD_CODE,
       		 B.PRODUCT_CODE,
       		 B.CLOSE_REASON,
       		 B.DEATH_DATE,
       		 ( select b.PRODUCT_NAME_STD from DEV_PAS.T_CONTRACT_BUSI_PROD a,DEV_PDS.T_BUSINESS_PRODUCT b 
       		 		  where a.BUSI_PRD_ID=b.BUSINESS_PRD_ID and a.busi_item_id=B.BUSI_ITEM_ID) as productName
		FROM (SELECT rownum RN,
     		        PT.ACCOUNT_ID, --账户ID
       				PT.POLICY_ID, --保单ID
       				PT.ACCOUNT_TYPE, --账户类型
       				PT.POLICY_ACCOUNT_STATUS, --账户状态
       				PT.CREATE_DATE  ACCOUNT_DATE, --账户创建日期
       				PT.INTEREST_CAPITAL, --账户本息和
       				PT.BUSI_ITEM_ID,
       				PT.ITEM_ID,
       				CBP.BUSI_PROD_CODE,
       				CP.PRODUCT_ID AS PRODUCT_CODE,
       				PT.BALANCE_DATE,
       				PT.INTEREST_BALANCE,
       				CM.POLICY_CODE, --保单号
       		(SELECT CR.CLOSE_DESC FROM DEV_PAS.T_CLOSE_REASON CR WHERE CR.CLOSE_CODE = PT.CLOSE_REASON) AS CLOSE_REASON,/*注销原因 */
       				TO_CHAR(PT.DEATH_DATE,'yyyy-MM-dd') AS DEATH_DATE /*死亡日期 */
  			 FROM DEV_PAS.T_POLICY_ACCOUNT PT
 			 		INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
    			  		 ON PT.POLICY_ID = CM.POLICY_ID
					INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP 
						 ON CM.POLICY_ID=CBP.POLICY_ID AND PT.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
  					INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP 
  						 ON CP.Item_Id=PT.ITEM_ID
 			WHERE (PT.ACCOUNT_TYPE = '11' OR PT.ACCOUNT_TYPE = '2')]]> 
         <if test=" policy_code  != null and policy_code  !='' "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if> 
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
    </select>
    
    <select id="PA_queryAccountByCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			 SELECT TCM.POLICY_CODE,
                 TCM.POLICY_ID,
                 TCM.LIABILITY_STATE as liability_state,
                TPA.CREATE_DATE,
                ROUND(TPA.INTEREST_CAPITAL, 2) INTEREST_CAPITAL,
                TPA.BALANCE_DATE,
                TPA.ACCOUNT_TYPE,
                 (SELECT Z.PRODUCT_NAME_SYS
                    FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT Z
                   WHERE Z.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID) AS product_name_sys,
                TCBP.BUSI_PROD_CODE as busi_prod_code,
				TCM.SPECIAL_ACCOUNT_FLAG as special_account_flag
            FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
                 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
                 APP___PAS__DBUSER.t_policy_account   TPA,
                 APP___PAS__DBUSER.T_POLICY_HOLDER      TPH,
                 APP___PAS__DBUSER.T_CUSTOMER           TC
           WHERE 1 = 1
             AND TCM.POLICY_CODE = TCBP.POLICY_CODE
             AND TCM.POLICY_CODE = TPH.POLICY_CODE
             AND TCM.POLICY_ID = TPA.POLICY_ID
             AND TPA.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
             AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
             AND TPA.ACCOUNT_TYPE IN ('2','11')
              ]]>
        <if test=" name != null and name != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{name}]]></if>
	    <if test=" idno != null and idno != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{idno}]]></if>
	    <if test=" appntidtype != null and appntidtype != ''  "><![CDATA[AND TC.CUSTOMER_CERT_TYPE = #{appntidtype}]]></if>        
    	
    	<![CDATA[   
    	UNION  
			 SELECT TCM1.POLICY_CODE,
                 TCM1.POLICY_ID,
                 TCM1.LIABILITY_STATE as liability_state,
                TPA1.CREATE_DATE,
                TPA1.INTEREST_CAPITAL,
                TPA1.BALANCE_DATE,
                TPA1.ACCOUNT_TYPE,
                 (SELECT Z.PRODUCT_NAME_SYS
                    FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT Z
                   WHERE Z.BUSINESS_PRD_ID = TCBP1.BUSI_PRD_ID) AS product_name_sys,
                TCBP1.BUSI_PROD_CODE as busi_prod_code,
				TCM1.SPECIAL_ACCOUNT_FLAG as special_account_flag
            FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM1,
                 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP1,
                 APP___PAS__DBUSER.t_policy_account   TPA1,
                 APP___PAS__DBUSER.T_INSURED_LIST      TPH1,
                 APP___PAS__DBUSER.T_CUSTOMER           TC1
           WHERE 1 = 1
             AND TCM1.POLICY_CODE = TCBP1.POLICY_CODE
             AND TCM1.POLICY_CODE = TPH1.POLICY_CODE
             AND TCM1.POLICY_ID = TPA1.POLICY_ID
             AND TPA1.BUSI_ITEM_ID = TCBP1.BUSI_ITEM_ID
             AND TPH1.CUSTOMER_ID = TC1.CUSTOMER_ID
             AND TPA1.ACCOUNT_TYPE IN ('2','11')
              ]]>
        <if test=" name != null and name != ''  "><![CDATA[AND TC1.CUSTOMER_NAME = #{name}]]></if>
	    <if test=" idno != null and idno != ''  "><![CDATA[AND TC1.CUSTOMER_CERTI_CODE = #{idno}]]></if>
	    <if test=" appntidtype != null and appntidtype != ''  "><![CDATA[AND TC1.CUSTOMER_CERT_TYPE = #{appntidtype}]]></if>        
    	
	</select>

    <select id="PA_queryAccountInfoByCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		 SELECT 
             B.ACCOUNT_TYPE,
             B.BUSI_PROD_CODE,
			 B.BUSI_PROD_NAME,
			 B.CREATE_DATE,
			 B.BALANCE_DATE,
			 B.INTEREST_CAPITAL,
			 B.UPDATE_TIME
          FROM(SELECT rownum RN,
			 PT.ACCOUNT_TYPE,              /*账户类型*/
			 CBP.BUSI_PROD_CODE,           /*险种编码*/
             (SELECT p.product_name_sys FROM DEV_PAS.t_Business_Product p where p.product_code_sys = CBP.BUSI_PROD_CODE ) BUSI_PROD_NAME, /*险种名称*/                             --险种名称
			 PT.CREATE_DATE,                /*账户创建日期*/
             PT.BALANCE_DATE,              /*上一结算日期*/
			 PT.INTEREST_CAPITAL,           /*账户价值*/
			 PT.UPDATE_TIME
  			FROM DEV_PAS.T_POLICY_ACCOUNT PT
 				INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM  ON PT.POLICY_ID = CM.POLICY_ID
    			INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP ON CM.POLICY_ID=CBP.POLICY_ID AND PT.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
    			INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP ON CP.Item_Id=PT.ITEM_ID
   		WHERE (PT.ACCOUNT_TYPE = '11' OR PT.ACCOUNT_TYPE = '2')
       		 AND CM.POLICY_CODE = #{policy_code}
       		)  B
		]]>
	</select>

	<!-- 根据保单号查询保单账户基本信息 -->
	<select id="PA_queryPolicyAccountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select a.close_reason,
			       a.death_date,
			       a.money_code,
			       a.next_balance_date,
			       a.interest_capital,
			       a.capitalized_date,
			       a.account_id,
			       a.capital_balance,
			       a.interest_sum,
			       a.item_id,
			       a.account_type,
			       a.balance_date,
			       a.regular_repay,
			       a.frozen_amount,
			       a.interest_balance,
			       a.create_date,
			       a.busi_item_id,
			       a.policy_id,
			       a.policy_account_status,
			       a.frozen_date,
			       (select tcbp.busi_prod_code
			          from app___pas__dbuser.t_contract_busi_prod tcbp
			         where tcbp.busi_item_id = a.busi_item_id) as busi_prod_code
			  from app___pas__dbuser.t_policy_account a
			  left join app___pas__dbuser.t_contract_master b
			    on a.policy_id = b.policy_id
			 where rownum <= 1000
			   and b.policy_code = #{policy_code}
		]]>
	</select>
	

	<!-- 根据保单号和险种代码查询保单账户基本信息 -->
	<select id="PA_queryPolicyAccountByPolicyCodeAndBusiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select a.close_reason,
			       a.death_date,
			       a.money_code,
			       a.next_balance_date,
			       a.interest_capital,
			       a.capitalized_date,
			       a.account_id,
			       a.capital_balance,
			       a.interest_sum,
			       a.item_id,
			       a.account_type,
			       a.balance_date,
			       a.regular_repay,
			       a.frozen_amount,
			       a.interest_balance,
			       a.create_date,
			       a.busi_item_id,
			       a.policy_id,
			       a.policy_account_status,
			       a.frozen_date,
			       (select tcbp.busi_prod_code
			          from app___pas__dbuser.t_contract_busi_prod tcbp
			         where tcbp.busi_item_id = a.busi_item_id) as busi_prod_code
			  from app___pas__dbuser.t_policy_account a
			  left join app___pas__dbuser.t_contract_master b
			    on a.policy_id = b.policy_id
				left join app___pas__dbuser.t_contract_busi_prod c
				  on a.busi_item_id = c.busi_item_id
			 where rownum <= 1000
			   and b.policy_code = #{policy_code}
			   and c.busi_prod_code = #{busi_prod_code}
		]]>
	</select>
	
	<!-- 查询年金累积生息账户信息 -->
	<select id="PA_queryAnnuityAccumulationInterestBearingAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			    SELECT SUM(A.INTEREST_CAPITAL) INTEREST_CAPITAL
				  FROM DEV_PAS.T_POLICY_ACCOUNT A,
				       DEV_PAS.T_PAY_DUE        B,
				       DEV_PAS.T_PAY_PLAN       C
				 WHERE A.POLICY_ID = B.POLICY_ID
				   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
				   AND B.PLAN_ID = C.PLAN_ID
				   AND B.FEE_STATUS = '01'
				   AND B.SURVIVAL_MODE = 2
				   AND A.ACCOUNT_TYPE = 11
				   AND A.POLICY_ACCOUNT_STATUS IN (1, 3)
				   AND C.PAY_PLAN_TYPE IN ('3', '8', '10')
				   AND A.POLICY_ID = #{policy_id}
				   AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	
	<!-- 根据保单号和险种代码查询保单账户基本信息 -->
	<select id="PA_queryAccumulateInterestAccountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			  SELECT SUM(A.INTEREST_CAPITAL) INTEREST_CAPITAL
			    FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT A
			   WHERE A.ACCOUNT_TYPE = '11'
			     AND A.POLICY_ID = #{policy_id}
		]]>
	</select>
	
		<!-- 查询保单账户名称 -->
	<select id="PA_queryPolicyAccountTypeName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
            SELECT ACCOUNT_NAME FROM   APP___PAS__DBUSER.T_POLICY_ACCOUNT_TYPE WHERE ACCOUNT_TYPE = #{account_type}
		]]>
	</select>
	
</mapper>
