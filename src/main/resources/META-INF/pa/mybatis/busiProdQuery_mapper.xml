<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IBusiProdQueryDao">

	<sql id="busiProdQueryDao">
		<if test=" policy_code  != null "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<!-- 查询所有操作 -->
	<select id="findAllBusinessProductquery" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[    
                 SELECT TCM.POLICY_CODE,
                        TCPM.GROUP_CODE,
                        TCPM.GROUP_NAME,
                        TBP.PRODUCT_CATEGORY1  OPERATION_TYPE,
                        TCBP.BUSI_PROD_CODE,
                        TBP.PRODUCT_DESC,
                        TC.CUSTOMER_CERTI_CODE AS HOLDER_CERTI_CODE,
                        TCC.CUSTOMER_CERTI_CODE AS INSURED_CERTI_CODE,
                        (SELECT POLICY_CODE  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T WHERE T.MASTER_BUSI_ITEM_ID IS NULL AND T.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID)BUSI_CODE
                   FROM APP___PAS__DBUSER.T_CONTRACT_PRDGROUP_MAIN TCPM,
                        APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD     TCBP,
                        APP___PAS__DBUSER.T_BUSINESS_PRODUCT       TBP,
                        APP___PAS__DBUSER.T_CONTRACT_MASTER        TCM,
                        APP___PAS__DBUSER.T_POLICY_HOLDER          TPH,
                        APP___PAS__DBUSER.T_CUSTOMER               TC,
                        APP___PAS__DBUSER.T_INSURED_LIST           TIL,
                        APP___PAS__DBUSER.T_BENEFIT_INSURED        TBI,
                        APP___PAS__DBUSER.T_CUSTOMER               TCC
                  WHERE TCM.POLICY_CODE = TCBP.POLICY_CODE
                    AND TCPM.POLICY_CODE = TCBP.POLICY_CODE
                    AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
                    AND TPH.POLICY_CODE = TCM.POLICY_CODE
                    AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
                    AND TBI.INSURED_ID = TIL.LIST_ID
                    AND TIL.POLICY_CODE = TCM.POLICY_CODE
                    AND TIL.CUSTOMER_ID = TCC.CUSTOMER_ID]]>
              <include refid="busiProdQueryDao" />
              <!-- select D.*,E.Group_Code,E.Group_Name
                  from APP___PAS__DBUSER.T_CONTRACT_PRDGROUP_MAIN E,
                  (select B.*,C.PRODUCT_DESC
                  from APP___PAS__DBUSER.T_BUSINESS_PRODUCT C,
                  (select A.Busi_Prod_Code,A.Operation_Type,A.MASTER_BUSI_ITEM_ID,A.Policy_Code,A.busi_prd_id  from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A where OLD_NEW='1') B
                  where B.BUSI_PRD_ID=C.Business_Prd_Id) D
                  where E.Policy_Code=D.policy_code  -->
	</select>
</mapper>