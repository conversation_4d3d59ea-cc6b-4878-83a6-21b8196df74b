<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="notice">
	<sql id="noticeCondition">
		<if test="ACCOUNT_TYPE != NULL"><![CDATA[ AND T_POLICY_ACCOUNT.ACCOUNT_TYPE = #{ACCOUNT_TYPE} ]]></if>
		<if test="POLICY_ID != NULL"><![CDATA[ AND T_POLICY_ACCOUNT.POLICY_ID = #{POLICY_ID} ]]></if>
		<if test="BUSI_ITEM_ID != NULL"><![CDATA[ AND T_POLICY_ACCOUNT.BUSI_ITEM_ID = #{BUSI_ITEM_ID} ]]></if>
	</sql>
	<select id="findNoticeInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT DISTINCT TCM.POLICY_CODE,
				   TCM.BRANCH_CODE,
			       TC.CUSTOMER_NAME,
			       TC.CUSTOMER_GENDER,
			       TD.ADDRESS,
			       TCM.EXPIRY_DATE,
			       TCM.CHANNEL_TYPE,
			       TCM.ORGAN_CODE,
			       TD.POST_CODE,
			       TCA.AGENT_NAME,
			       TCA.AGENT_CODE,
			       A.*
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
			       APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,
			       APP___PAS__DBUSER.T_UDMP_ORG TUO,
			       APP___PAS__DBUSER.T_CUSTOMER TC,
       			   APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
			       (SELECT DISTINCT TADD.POST_CODE,
			               TDIS1.NAME || TDIS2.NAME || TDIS3.NAME || TADD.ADDRESS ADDRESS
			          FROM APP___PAS__DBUSER.T_ADDRESS TAD, APP___PAS__DBUSER.T_POLICY_HOLDER TPH
			          LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TADD
			            ON TPH.ADDRESS_ID = TADD.ADDRESS_ID
			          LEFT JOIN APP___PAS__DBUSER.T_DISTRICT TDIS1
			            ON TADD.STATE = TDIS1.CODE
			          LEFT JOIN APP___PAS__DBUSER.T_DISTRICT TDIS2
			            ON TADD.CITY = TDIS2.CODE
			          LEFT JOIN APP___PAS__DBUSER.T_DISTRICT TDIS3
			            ON TADD.DISTRICT = TDIS3.CODE
			         WHERE TPH.POLICY_CODE = #{policy_code}
			           AND TPH.CUSTOMER_ID = TAD.CUSTOMER_ID) TD,
			       (SELECT D.ORGAN_NAME || '|' || C.ORGAN_NAME || '|' || B.ORGAN_NAME || '|' ||
			               A.ORGAN_NAME AS ORGAN_NAME
			          FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A,
			               APP___PAS__DBUSER.T_UDMP_ORG_REL B,
			               APP___PAS__DBUSER.T_UDMP_ORG_REL C,
			               APP___PAS__DBUSER.T_UDMP_ORG_REL D
			         WHERE A.UPORGAN_CODE = B.ORGAN_CODE
			           AND B.UPORGAN_CODE = C.ORGAN_CODE
			           AND C.UPORGAN_CODE = D.ORGAN_CODE
			           AND A.ORGAN_CODE =
			               (SELECT T.ORGAN_CODE
			                  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T
			                 WHERE T.POLICY_CODE = #{policy_code})) A
			 WHERE TCM.POLICY_CODE = #{policy_code}
			   AND TCM.POLICY_CODE = TPH.POLICY_CODE
        	   AND TCA.POLICY_CODE = TCM.POLICY_CODE
         	   AND TCM.BRANCH_CODE = TUO.ORGAN_CODE
         	   AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID]]>
	</select>
	
	<select id="findPaymentByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ROUND(INTEREST_CAPITAL,2) INTEREST_CAPITAL FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE 1=1 AND T_POLICY_ACCOUNT.ACCOUNT_TYPE = #{account_type}
			AND T_POLICY_ACCOUNT.POLICY_ID = #{policy_id} 
			AND T_POLICY_ACCOUNT.BUSI_ITEM_ID = #{busi_item_id} 
			]]>
		<!-- <include refid="noticeCondition" /> -->
	</select>
	
	<select id="findFullAddressNameByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT td1.name||td2.name||td3.name||tadd.address address,
				tadd.post_code
					FROM APP___PAS__DBUSER.T_policy_holder tph,
					APP___PAS__DBUSER.T_address tadd,
					APP___PAS__DBUSER.T_district td1,
					APP___PAS__DBUSER.T_district td2,
					APP___PAS__DBUSER.T_district td3
					WHERE tph.address_id=tadd.address_id
          			AND tadd.state=td1.code
					AND tadd.city=td2.code
					AND tadd.district=td3.code
          			AND tph.policy_id=#{policy_id}]]>
	</select>
	
	<select id="findAgentOrgenCodePrior" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select t.organ_code,t.organ_name,t.organ_grade
      from APP___PAS__DBUSER.T_udmp_org_rel t
     start with t.organ_code in
                (select ta.agent_organ_code
                   from APP___PAS__DBUSER.T_agent ta
                  where ta.agent_code in
                        (select tca.agent_code
                           from APP___PAS__DBUSER.T_contract_agent tca
                          where tca.policy_code = #{policy_code} and tca.is_current_agent='1'))
    connect by prior t.uporgan_code = t.organ_code
    ]]>
	</select>
	
	<select id="findOrganInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select a.organ_name,a.organ_address,a.organ_zipcode,a.organ_phone
	from APP___PAS__DBUSER.T_udmp_org  a 
	where a.organ_code = #{organ_code}
    ]]>
	</select>
	
	<select id="findInsuredCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select *
	  from APP___PAS__DBUSER.T_customer tc
	 where tc.customer_id =
	       (select til.customer_id
	          from APP___PAS__DBUSER.T_INSURED_LIST til
	         where til.list_id = (select tbi.insured_id
	                                from APP___PAS__DBUSER.T_BENEFIT_INSURED tbi
	                               where 1=1 ]]>
	       <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND tbi.policy_code = #{policy_code} ]]></if>
	       <if test=" busi_item_id  != null "><![CDATA[ AND tbi.busi_item_id = #{busi_item_id} ]]></if>    
	       <if test=" order_id  != null "><![CDATA[ AND tbi.order_id = #{order_id} ]]></if>  
	       <if test=" policy_id  != null "><![CDATA[ AND tbi.policy_id = #{policy_id} ]]></if>                      
	       <![CDATA[ ))]]>
	</select>
	
		
	<select id="findorgRelInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.organ_code, t.organ_grade, t.organ_name
  			FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
 			START WITH T.ORGAN_CODE = (SELECT T.ORGAN_CODE
                              FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T
                             WHERE T.POLICY_CODE = #{policy_code})
			CONNECT BY NOCYCLE PRIOR T.UPORGAN_CODE = T.ORGAN_CODE
		]]>
	</select>
	<select id="findAddressInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			     SELECT QE.ADDRESS, QE.POST_CODE
        FROM DEV_PAS.T_INSURED_LIST QW
       INNER JOIN DEV_PAS.T_ADDRESS QE
          ON QW.CUSTOMER_ID = QE.CUSTOMER_ID
       WHERE POLICY_CODE = #{policy_code}
       ORDER BY QW.UPDATE_TIME DESC 
		]]>
	</select>
	
	<select id="noticeFindFundSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		select *
        from APP___PAS__DBUSER.T_Fund_Settlement TFS
			 WHERE TFS.INVEST_ID IN (select list_id
			                           from APP___PAS__DBUSER.T_contract_invest tci
			                          where 1 = 1
			                          and tci.policy_id = #{policy_id}
			                           )and 1=1 
		<if test=" now_date  != null "><![CDATA[AND tfs.settle_date = #{now_date} ]]></if>	
		<if test=" end_date  != null "><![CDATA[AND tfs.settle_date <= #{end_date} ]]></if>
		<if test=" start_date != null"><![CDATA[AND tfs.settle_date >= #{start_date} ]]></if>
			     order by tfs.settle_date   
		
	</select>
	
<!-- 	获取投连万能账户累计的交易金额 -->
	<select id="noticeFindTransFundAmountByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select sum(tft.trans_amount) as trans_amount
		  from APP___PAS__DBUSER.T_fund_trans tft 
		 WHERE tft.list_id IN (select list_id
		                           from APP___PAS__DBUSER.T_contract_invest tci
		                          where 1 = 1
		]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND tci.busi_item_id = #{busi_item_id} ]]></if>    
		<![CDATA[
			                            )
		]]>
		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND tft.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		</if>
		<if test="start_date != null ">
			<![CDATA[ AND tft.deal_time >#{start_date} ]]>
		</if>
		<if test="end_date != null">
			<![CDATA[ AND tft.deal_time <=#{end_date} ]]>
		</if>
		<if test="now_date != null ">
			<![CDATA[ AND tft.deal_time =#{now_date} ]]>
		</if>
		<if test="fund_code != null and fund_code != ''">
			<![CDATA[ AND tft.fund_code =#{fund_code} ]]>
		</if>
	</select>
	
    <!-- 多账户累计保险费、个人期初、期末账户价值查询 -->
	<select id="findMultiUSTotalPayPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT NVL(sum(tfgt.trans_amount),0) as trans_amount,NVL(max(tfgt.balance_af),0) as balance_af 
		   FROM APP___PAS__DBUSER.t_fund_group_trans tfgt where 1 = 1
		]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND tfgt.busi_item_id = #{busi_item_id} ]]></if>    

		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND tfgt.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		</if>
		<if test="start_date != null ">
			<![CDATA[ AND tfgt.deal_time >#{start_date} ]]>
		</if>
		<if test="end_date != null">
			<![CDATA[ AND tfgt.deal_time <=#{end_date} ]]>
		</if>
		<if test="now_date != null ">
			<![CDATA[ AND tfgt.deal_time =#{now_date} ]]>
		</if>
	</select>	
	
<!-- 	获取流水号 -->
	<select id="getSerialNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select APP___PAS__DBUSER.s_document.NEXTVAL FROM DUAL
		]]>
	</select>
	<!-- 根据银行 代码获取银行网点名称-->
	<select id="getNamebyCode" resultType="java.util.Map" parameterType="java.util.Map">
				
		SELECT A.BANK_BRANCH_CODE,A.BANK_BRANCH_NAME FROM APP___PAS__DBUSER.T_BANK_BRANCH A WHERE 1=1 AND A.BANK_BRANCH_CODE=#{bank_branch_code}
				
	</select>
	<!-- 137001 add -->
	<select id="findServiceCodeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[		
		select * from (
		  select ac.service_code 
		    from dev_pas.T_CS_POLICY_ACCOUNT_STREAM st
		    join dev_pas.t_cs_policy_change pc
		      on pc.policy_chg_id = st.policy_chg_id
		     and pc.change_id = st.change_id
		    join dev_pas.t_cs_accept_change ac
		      on ac.accept_id = pc.accept_id
		     and ac.service_code in ('RL','LN')
		     and ac.accept_status = '18'
		   where st.old_new = 1
		     and st.policy_id = #{policy_id} 
		     and st.account_type = 4
		     and st.regular_repay = 0
		   order by ac.accept_time desc
		) t where rownum = 1
		]]>
	</select>
	<!-- 查询营业区营业组 -->
	<select id="findSalesOrganName" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
				select (case n.organ_level_code
				         when 1 then
				          n.sales_organ_name
				         when 2 then
				          m.sales_organ_name
				         when 3 then
				          q.sales_organ_name
				       end) as sales_name_one,
				       (case n.organ_level_code
				         when 2 then
				          n.sales_organ_name
				         when 3 then
				          m.sales_organ_name
				       end) as sales_name_two,
				       (case n.organ_level_code
				         when 3 then
				          n.sales_organ_name
				         else
				          ''
				       end) as sales_name_three
				  from APP___PAS__DBUSER.T_agent g
				  left join APP___PAS__DBUSER.T_sales_organ n
				    on g.sales_organ_code = n.sales_organ_code
				  left join APP___PAS__DBUSER.T_sales_organ m
				    on n.parent_code = m.sales_organ_code
				  left join APP___PAS__DBUSER.T_sales_organ q
				    on m.parent_code = q.sales_organ_code
				 where g.agent_code in
				       (select tca.agent_code
				          from APP___PAS__DBUSER.T_contract_agent tca
				         where tca.policy_code = #{policy_code} and tca.is_current_agent='1')
		]]>
</select>

   <!-- 纰漏信息显示 -->
	<select id="PA_queryMessageInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT '公司' || A.YEAR || '年' ||
		       DECODE(A.SEASON, 1, '一', 2, '二', 3, '三', '四') || '季度的综合偿付能力充足率为' ||
		       A.SOLVENCY_RATIO * 100 || '%' || CASE
		         WHEN A.YEAR = B.YEAR AND A.SEASON = B.SEASON THEN
		          '、风险综合评级为' || B.RISK_LEVEL || '类，偿付能力充足率达到监管要求。'
		         ELSE
		          ',' || B.YEAR || '年' ||
		          DECODE(B.SEASON, 1, '一', 2, '二', 3, '三', '四') || '季度风险综合评级为' ||
		          B.RISK_LEVEL || '类，偿付能力充足率达到监管要求。'
		       END AS message_info
		  FROM (SELECT M.YEAR, M.SEASON, M.SOLVENCY_RATIO
		          FROM (SELECT T.YEAR, T.SEASON, T.SOLVENCY_RATIO
		                  FROM DEV_PAS.T_C_ROSS_INFO T
		                 WHERE T.SOLVENCY_RATIO IS NOT NULL
		                   AND T.YEAR =
		                       (SELECT MAX(TC.YEAR)
		                          FROM APP___PAS__DBUSER.T_C_ROSS_INFO TC
		                         WHERE TC.SOLVENCY_RATIO IS NOT NULL)
		                 ORDER BY T.SEASON DESC) M
		         WHERE ROWNUM = 1) A,
		       (SELECT M.YEAR, M.SEASON, M.RISK_LEVEL
		          FROM (SELECT T.YEAR, T.SEASON, T.RISK_LEVEL
		                  FROM DEV_PAS.T_C_ROSS_INFO T
		                 WHERE T.RISK_LEVEL IS NOT NULL
		                   AND T.YEAR = (SELECT MAX(TC.YEAR)
		                                   FROM APP___PAS__DBUSER.T_C_ROSS_INFO TC
		                                  WHERE TC.RISK_LEVEL IS NOT NULL)
		                 ORDER BY T.SEASON DESC) M
		         WHERE ROWNUM = 1) B	]]>
	</select>
	<select id="findBankAccountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT distinct 
	          TPA.BANK_ACCOUNT
	     FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM    TPAS,
	          APP___PAS__DBUSER.T_CS_POLICY_ACCOUNT_STREAM TCPAS,
	          APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE         TCAP,
	          APP___PAS__DBUSER.T_CS_POLICY_CHANGE         TCPC,
	          APP___PAS__DBUSER.V_PREM_ARAP_ALL            TPA
	    WHERE 1 = 1
	      AND TPAS.STREAM_ID = TCPAS.STREAM_ID
	      AND TPAS.BUSI_ITEM_ID = TCPAS.BUSI_ITEM_ID
	      AND TCAP.CHANGE_ID = TCPAS.CHANGE_ID
	      AND TPA.BUSINESS_CODE = TCAP.ACCEPT_CODE
	      AND TCPAS.CHANGE_ID=TCPC.CHANGE_ID
	      AND TCPC.CHANGE_ID=TCAP.CHANGE_ID
	      AND TCPC.POLICY_CODE=TPA.POLICY_CODE
	      AND TPAS.POLICY_ID=TCPC.POLICY_ID
	      AND TPAS.ACCOUNT_TYPE = '4'
	      AND TCPAS.OLD_NEW = '1'
	      AND TCPAS.OPERATION_TYPE = '1'
	      AND TCAP.SERVICE_CODE IN ('LN', 'RL','RF')
	      AND TCAP.ACCEPT_STATUS = '18'
	      AND TPA.FEE_TYPE IN ('P004410000', 'G004790000')
	      AND TPA.FINISH_TIME IS NOT NULL
	      AND TPA.FEE_STATUS IN ('01', '19')
	      and TPAS.REGULAR_REPAY = 0  /*未清偿*/
		<if test="policy_code != null"><![CDATA[AND TPA.POLICY_code =  #{policy_code} ]]></if>		
	</select>
</mapper>
