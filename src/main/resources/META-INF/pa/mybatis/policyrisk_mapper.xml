<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.batch.policyrisk.dao.IPolicyRiskDao">

	<select id="findAllRiskParm" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT T.ITEM_ID,
	   T.POLICY_ID,
	   T.BUSI_ITEM_ID,
	   T.APPLY_CODE,
	   T.VALIDATE_DATE,
       T.PRODUCT_CODE,
       T.PREM_FREQ,
       T.CHARGE_PERIOD,
       T.COVERAGE_PERIOD,
       T.UNIT,
       T.CHARGE_YEAR,
       T.AMOUNT,
       T.STD_PREM_AF,
       T.TOTAL_PREM_AF,
       T.COVERAGE_YEAR,
       T.LIABILITY_STATE,
       TCI.INTEREST_CAPITAL,
       TBI.ORDER_ID,
       TCE.POLICY_YEAR,
       TCE.POLICY_PERIOD,
       TCR.CUSTOMER_GENDER,
       TCR.CUSTOMER_ID,
       TIL.INSURED_AGE
  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT T
  LEFT JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL
    ON T.POLICY_CODE = TIL.POLICY_CODE
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST TCI
    ON T.ITEM_ID = TCI.ITEM_ID
  LEFT JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED TBI
    ON TBI.BUSI_ITEM_ID = T.BUSI_ITEM_ID
   AND TIL.LIST_ID = TBI.INSURED_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
    ON T.ITEM_ID = TCE.ITEM_ID
  LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TCR
    ON TCR.CUSTOMER_ID = TIL.CUSTOMER_ID
 WHERE T.ITEM_ID = #{item_id}]]>
	</select>
	
	<select id="findAllRiskParms" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT T.ITEM_ID,
	   T.POLICY_ID,
	   T.BUSI_ITEM_ID,
	   T.APPLY_CODE,
	   T.VALIDATE_DATE,
       T.PRODUCT_CODE,
       T.PREM_FREQ,
       T.CHARGE_PERIOD,
       T.COVERAGE_PERIOD,
       T.UNIT,
       T.CHARGE_YEAR,
       T.AMOUNT,
       T.STD_PREM_AF,
       T.COVERAGE_YEAR,
       TCI.INTEREST_CAPITAL,
       TBI.ORDER_ID,
       TCE.POLICY_YEAR,
       TCE.POLICY_PERIOD,
       TCR.CUSTOMER_GENDER,
       TCR.CUSTOMER_ID,
       TIL.INSURED_AGE
  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT T
  LEFT JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL
    ON T.POLICY_CODE = TIL.POLICY_CODE
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST TCI
    ON T.ITEM_ID = TCI.ITEM_ID
  LEFT JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED TBI
    ON TBI.BUSI_ITEM_ID = T.BUSI_ITEM_ID
   AND TIL.LIST_ID = TBI.INSURED_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
    ON T.ITEM_ID = TCE.ITEM_ID
  LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TCR
    ON TCR.CUSTOMER_ID = TIL.CUSTOMER_ID
 WHERE T.ITEM_ID IN (SELECT A.ITEM_ID
                       FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE A
                      WHERE  A.STATUS = 0 AND A.ITEM_ID LIKE '%${item_id}') ]]>
	</select>
	
	<!-- 151299 add T_CUSTOMER -->
	<select id="findAllCsRiskParm" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT T.ITEM_ID,
			   nvl(TCR.customer_birthday,TR.Customer_Birthday) customer_birthday,
		       T.POLICY_ID,
		       T.POLICY_CHG_ID,
		       T.BUSI_ITEM_ID,
		       T.APPLY_CODE,
		       T.VALIDATE_DATE,
		       T.PRODUCT_CODE,
		       T.PREM_FREQ,
		       T.CHARGE_PERIOD,
		       T.COVERAGE_PERIOD,
		       T.UNIT,
		       T.CHARGE_YEAR,
		       T.AMOUNT,
		       T.STD_PREM_AF,
		       T.COVERAGE_YEAR,
		       TCI.INTEREST_CAPITAL,
		       TBI.ORDER_ID,
		       TCE.POLICY_YEAR,
		       TCE.POLICY_PERIOD,
		       nvl(TCR.CUSTOMER_GENDER,TR.CUSTOMER_GENDER) CUSTOMER_GENDER,
           	   nvl(TCR.CUSTOMER_ID,TR.CUSTOMER_ID) CUSTOMER_ID,
		       TIL.INSURED_AGE,
		       T.IS_WAIVED,
		       T.TOTAL_PREM_AF
		  FROM APP___PAS__DBUSER.T_cs_CONTRACT_PRODUCT T
		
		  LEFT JOIN APP___PAS__DBUSER.T_cs_CONTRACT_INVEST TCI
		    ON T.ITEM_ID = TCI.ITEM_ID  and t.old_new = tci.old_new and tci.change_id=t.change_id  and tci.policy_id = t.policy_id
		  LEFT JOIN APP___PAS__DBUSER.T_cs_BENEFIT_INSURED TBI
		    ON TBI.BUSI_ITEM_ID = T.BUSI_ITEM_ID and tbi.change_id=t.change_id
		     and t.old_new = tbi.old_new
		   LEFT JOIN APP___PAS__DBUSER.T_cs_INSURED_LIST TIL 
		    ON T.POLICY_CODE = TIL.POLICY_CODE and t.old_new = til.old_new and til.change_id=t.change_id
		    and til.list_id=tbi.insured_id
		  LEFT JOIN APP___PAS__DBUSER.T_cs_CONTRACT_EXTEND TCE
		    ON T.ITEM_ID = TCE.ITEM_ID and tce.change_id=t.change_id and tce.old_new=t.old_new
		  LEFT JOIN APP___PAS__DBUSER.T_cs_CUSTOMER TCR
		    ON TCR.CUSTOMER_ID = TIL.CUSTOMER_ID and tcr.change_id=t.change_id AND TCR.OLD_NEW=T.OLD_NEW
		  LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TR
        	on TR.CUSTOMER_ID = TIL.CUSTOMER_ID
		 WHERE  t.old_new = '1'
		   and t.change_id = ${change_id}
		   and (t.operation_type = '1' or t.operation_type = '2')]]>
		   
		   <if test=" policy_chg_id  != null "><![CDATA[ AND t.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</select>
</mapper>