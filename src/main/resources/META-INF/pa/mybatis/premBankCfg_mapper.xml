<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.IPremBankCfgDao">

	<sql id="premBankCfgWhereCondition">
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" next_bank_code != null and next_bank_code != ''  "><![CDATA[ AND A.NEXT_BANK_CODE = #{next_bank_code} ]]></if>
		<if test=" bank_name != null and bank_name != ''  "><![CDATA[ AND A.BANK_NAME = #{bank_name} ]]></if>
		<if test=" init_bank_code != null and init_bank_code != ''  "><![CDATA[ AND A.INIT_BANK_CODE = #{init_bank_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryPremBankCfgByCfgIdCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPremBankCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT S_POLICY_CASHVALUE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PREM_BANK_CFG(
				INSERT_TIMESTAMP, ORGAN_CODE, UPDATE_BY, CFG_ID, INSERT_TIME, UPDATE_TIMESTAMP, UPDATE_TIME, 
				NEXT_BANK_CODE, INSERT_BY, BANK_NAME, INIT_BANK_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{cfg_id, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP, SYSDATE 
				, #{next_bank_code, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{bank_name, jdbcType=VARCHAR} , #{init_bank_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePremBankCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PREM_BANK_CFG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePremBankCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_BANK_CFG ]]>
		<set>
		<trim suffixOverrides=",">
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			NEXT_BANK_CODE = #{next_bank_code, jdbcType=VARCHAR} ,
			BANK_NAME = #{bank_name, jdbcType=VARCHAR} ,
			INIT_BANK_CODE = #{init_bank_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPremBankCfgByCfgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.CFG_ID, 
			A.NEXT_BANK_CODE, A.BANK_NAME, A.INIT_BANK_CODE FROM APP___PAS__DBUSER.T_PREM_BANK_CFG A WHERE 1 = 1  ]]>
		<include refid="queryPremBankCfgByCfgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapPremBankCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.CFG_ID, 
			A.NEXT_BANK_CODE, A.BANK_NAME, A.INIT_BANK_CODE FROM APP___PAS__DBUSER.T_PREM_BANK_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="premBankCfgWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPremBankCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.CFG_ID, 
			A.NEXT_BANK_CODE, A.BANK_NAME, A.INIT_BANK_CODE FROM APP___PAS__DBUSER.T_PREM_BANK_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="premBankCfgWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findPremBankCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM_BANK_CFG A WHERE 1 = 1  ]]>
		<include refid="premBankCfgWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryPremBankCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ORGAN_CODE, B.CFG_ID, 
			B.NEXT_BANK_CODE, B.BANK_NAME, B.INIT_BANK_CODE FROM (
					SELECT ROWNUM RN, A.ORGAN_CODE, A.CFG_ID, 
			A.NEXT_BANK_CODE, A.BANK_NAME, A.INIT_BANK_CODE FROM APP___PAS__DBUSER.T_PREM_BANK_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="premBankCfgWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询单条 -->
	<select id="findPremBankCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.CFG_ID, 
			A.NEXT_BANK_CODE, A.BANK_NAME, A.INIT_BANK_CODE FROM APP___PAS__DBUSER.T_PREM_BANK_CFG A WHERE 1=1 AND ROWNUM = 1
		]]>
		<include refid="premBankCfgWhereCondition"></include>
	</select>
</mapper>
