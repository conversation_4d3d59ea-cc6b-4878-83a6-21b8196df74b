<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPhoneInfoQueryFromPADao">
	<select id="PA_phoneInfoQueryFromPA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT M.POLICY_SEQUENCE_NO policy_no, C.CUSTOMER_NAME accident_name,
	              CASE WHEN C.CUSTOMER_GENDER=1 THEN '0'
	                   WHEN C.CUSTOMER_GENDER=2 THEN '1' ELSE '2' END accident_sex,
	              CASE WHEN C.CUSTOMER_CERT_TYPE NOT IN ('0','1','2','3','4','5','8','9')
                 	THEN '8' ELSE C.CUSTOMER_CERT_TYPE END accident_id_type,
              	  C.CUSTOMER_CERTI_CODE accident_id_number,
              	  CASE WHEN (SELECT P.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD P WHERE P.APPLY_CODE=M.APPLY_CODE)
              	  	in('00557000','00868000') THEN '2' ELSE '1' END aaccident_reason,
              	  M.POLICY_CODE, M.CUSTOMER_SEQUENCE_NO, M.MEDICAL_NO
              	  FROM DEV_PAS.T_CONTRACT_MEDICAL M,DEV_PAS.T_INSURED_LIST L,DEV_PAS.T_CUSTOMER C
              	  WHERE M.APPLY_CODE=L.APPLY_CODE AND M.POLICY_CODE=L.POLICY_CODE 
                  AND L.CUSTOMER_ID=C.CUSTOMER_ID 
                  AND M.MEDICAL_ID = (SELECT MAX(B.MEDICAL_ID) FROM APP___PAS__DBUSER.T_CONTRACT_MEDICAL B WHERE B.POLICY_CODE=M.POLICY_CODE )
                  ]]>
         <if test="policy_no != null ">
         		<![CDATA[ AND M.POLICY_SEQUENCE_NO = #{policy_no } ]]>
         </if>
         <if test="policy_code != null ">
         		<![CDATA[ AND M.POLICY_CODE = #{policy_code } ]]>
         </if>
	</select>
</mapper>