<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IErrorFieldCfgDao">

	<sql id="errorFieldCfgWhereCondition">
		<if test=" tableName != null and tableName != '' and tableName != 'null' "><![CDATA[ AND A.TABLE_NAME = LTRIM(RTRIM(#{tableName}, ' ') , ' ') ]]></if>
		<if test=" fieldName != null and fieldName != '' and fieldName != 'null' "><![CDATA[ AND A.FIELD_NAME = LTRIM(RTRIM(#{fieldName}, ' ') , ' ') ]]></if>
		<if test=" columnName != null and columnName != '' and columnName != 'null' "><![CDATA[ AND A.COLUMN_NAME = LTRIM(RTRIM(#{columnName}, ' ') , ' ') ]]></if>
		<if test=" listId  != null "><![CDATA[ AND A.LIST_ID = ${listId} ]]></if>
		<if test=" subId != null and subId != '' and subId != 'null' "><![CDATA[ AND A.SUB_ID = LTRIM(RTRIM(#{subId}, ' ') , ' ') ]]></if>
	</sql>
	
	<!-- 根据传值查询含有传的字段的表名 -->
	<select id="findAllErrorFieldCfg" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT A.TABLE_NAME , A.FIELD_NAME , A.COLUMN_NAME , A.SUB_ID FROM DEV_PAS.T_ERROR_FIELD_CFG A WHERE 1 = 1 AND A.SUB_ID='pas' ]]>
		<if test=" field_name != null and field_name != '' and field_name != 'null' "><![CDATA[ AND A.FIELD_NAME = LTRIM(RTRIM(#{field_name}, ' ') , ' ') ]]></if>
		<!-- <include refid="errorFieldCfgWhereCondition" /> -->
	</select>
	
	<!-- 根据查询出来要改的数据动态更改 -->
	<update id="updatePasErrorFieldCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE ${Table_Name} A ]]>
		<set>
		<trim suffixOverrides=",">
			<if test=" Column_Name  != null and  Column_Name  != ''  and  Column_Value  != '' and  Column_Value  != null"> A.${Column_Name} = #{Column_Value}, </if>
			<if test=" Column_Name  != null and  Column_Name  != ''  and  Column_Value1  != '' and  Column_Value1  != null"> A.${Column_Name} = #{Column_Value1}, </if>
			<if test=" Column_Name  != null and  Column_Name  != ''  and  Column_Value2  != '' and  Column_Value2  != null"> A.${Column_Name} = #{Column_Value2}, </if>
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_ID=#{Customer_Id} ]]>
		
	</update>
	
</mapper>
