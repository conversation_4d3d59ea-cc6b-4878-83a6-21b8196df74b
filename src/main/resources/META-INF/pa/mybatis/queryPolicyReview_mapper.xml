<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.cs.dao.IQueryPolicyReviewDao">
					

	

	<!-- 查询记录数 -->
	<select id="queryReviewerForCode" resultType="java.lang.Integer" parameterType="java.util.Map">
	<![CDATA[    SELECT COUNT(1)
   FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC
  INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
     ON AC.ACCEPT_ID = PC.ACCEPT_ID
  WHERE AC.REVIEW_ID = #{review_id}
    AND PC.POLICY_CODE =  #{policy_code}
	]]>
	</select>
</mapper>