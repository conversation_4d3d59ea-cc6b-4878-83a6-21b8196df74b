<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISubmitMessageBocicDao">

	<sql id="PAS_submitMessageBocicWhereCondition">
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" submit_clob != null and submit_clob != ''  "><![CDATA[ AND A.SUBMIT_CLOB = #{submit_clob} ]]></if>
		<if test=" err_code != null and err_code != ''  "><![CDATA[ AND A.ERR_CODE = #{err_code} ]]></if>
		<if test=" deal_status != null and deal_status != ''  "><![CDATA[ AND A.DEAL_STATUS = #{deal_status} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" submit_type != null and submit_type != ''  "><![CDATA[ AND A.SUBMIT_TYPE = #{submit_type} ]]></if>
		<if test=" submit_num  != null "><![CDATA[ AND A.SUBMIT_NUM = #{submit_num} ]]></if>
		<if test=" err_detail != null and err_detail != ''  "><![CDATA[ AND A.ERR_DETAIL = #{err_detail} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" endor_sement_type != null and endor_sement_type != ''  "><![CDATA[ AND A.ENDOR_SEMENT_TYPE = #{endor_sement_type} ]]></if>
		<if test=" cal_date  != null  and  cal_date  != ''  "><![CDATA[ AND A.CAL_DATE = #{cal_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_querySubmitMessageBocicByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PAS_querySubmitMessageBocicByBusinessCodeCondition">
		<if test=" business_code != null and business_code != '' "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
	</sql>	
	<sql id="PAS_querySubmitMessageBocicByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addSubmitMessageBocic"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SUBMIT_MESSAGE_BOCIC.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC(
				ENDOR_SEMENT_TYPE,BUSINESS_CODE, SUBMIT_CLOB, ERR_CODE, INSERT_TIME, DEAL_STATUS, UPDATE_TIME, STATUS, 
				INSERT_TIMESTAMP, POLICY_CODE, SUBMIT_TYPE, UPDATE_BY, SUBMIT_NUM, ERR_DETAIL, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY ,CAL_DATE ) 
			VALUES (
				#{endor_sement_type,jdbcType=VARCHAR},#{business_code, jdbcType=VARCHAR}, #{submit_clob, jdbcType=VARCHAR} , #{err_code, jdbcType=VARCHAR} , SYSDATE , #{deal_status, jdbcType=VARCHAR} , SYSDATE , #{status, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{submit_type, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{submit_num, jdbcType=NUMERIC} , #{err_detail, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{cal_date, jdbcType=DATE}  ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deleteSubmitMessageBocic" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updateSubmitMessageBocic" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC ]]>
		<set>
		<trim suffixOverrides=",">
			ENDOR_SEMENT_TYPE=#{endor_sement_type,jdbcType=VARCHAR},
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
			SUBMIT_CLOB = #{submit_clob, jdbcType=VARCHAR} ,
			ERR_CODE = #{err_code, jdbcType=VARCHAR} ,
			DEAL_STATUS = #{deal_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			STATUS = #{status, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			SUBMIT_TYPE = #{submit_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SUBMIT_NUM = #{submit_num, jdbcType=NUMERIC} ,
			ERR_DETAIL = #{err_detail, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CAL_DATE = #{cal_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="PAS_findSubmitMessageBocicByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEMENT_TYPE,A.BUSINESS_CODE, A.SUBMIT_CLOB, A.ERR_CODE, A.DEAL_STATUS, A.STATUS, 
			A.POLICY_CODE, A.SUBMIT_TYPE, A.SUBMIT_NUM, A.ERR_DETAIL, A.LIST_ID, A.CAL_DATE   FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE 1 = 1  ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
	</select>
	
	<select id="PAS_findSubmitMessageBocicByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEMENT_TYPE,A.BUSINESS_CODE, A.SUBMIT_CLOB, A.ERR_CODE, A.DEAL_STATUS, A.STATUS, 
			A.POLICY_CODE, A.SUBMIT_TYPE, A.SUBMIT_NUM, A.ERR_DETAIL, A.LIST_ID, A.CAL_DATE  FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE 1 = 1  ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
	</select>
	
	<select id="PAS_findSubmitMessageBocicByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEMENT_TYPE,A.BUSINESS_CODE, A.SUBMIT_CLOB, A.ERR_CODE, A.DEAL_STATUS, A.STATUS, 
			A.POLICY_CODE, A.SUBMIT_TYPE, A.SUBMIT_NUM, A.ERR_DETAIL, A.LIST_ID, A.CAL_DATE  FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE 1 = 1  ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
	</select>
	

	<!-- 按map查询操作 -->
	<select id="PAS_findAllMapSubmitMessageBocic" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEMENT_TYPE,A.BUSINESS_CODE, A.SUBMIT_CLOB, A.ERR_CODE, A.DEAL_STATUS, A.STATUS, 
			A.POLICY_CODE, A.SUBMIT_TYPE, A.SUBMIT_NUM, A.ERR_DETAIL, A.LIST_ID, A.CAL_DATE  FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
	</select>

	<!-- 查询单条操作 -->
	<select id="PAS_findSubmitMessageBocic" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEMENT_TYPE,A.BUSINESS_CODE, A.SUBMIT_CLOB, A.ERR_CODE, A.DEAL_STATUS, A.STATUS, 
			A.POLICY_CODE, A.SUBMIT_TYPE, A.SUBMIT_NUM, A.ERR_DETAIL, A.LIST_ID, A.CAL_DATE  FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="PAS_findAllSubmitMessageBocic" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEMENT_TYPE,A.BUSINESS_CODE, A.ERR_CODE, A.DEAL_STATUS, A.STATUS, 
			A.POLICY_CODE, A.SUBMIT_TYPE, A.SUBMIT_NUM, A.ERR_DETAIL, A.LIST_ID, A.CAL_DATE  FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
		<![CDATA[  ORDER BY A.LIST_ID DESC ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="PAS_findSubmitMessageBocicTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE 1 = 1  ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="PAS_querySubmitMessageBocicForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSINESS_CODE, B.SUBMIT_CLOB, B.ERR_CODE, B.DEAL_STATUS, B.STATUS, 
			B.POLICY_CODE, B.SUBMIT_TYPE, B.SUBMIT_NUM, B.ERR_DETAIL, B.LIST_ID , B.CAL_DATE FROM (
					SELECT ROWNUM RN, A.BUSINESS_CODE, A.SUBMIT_CLOB, A.ERR_CODE, A.DEAL_STATUS, A.STATUS, 
			A.POLICY_CODE, A.SUBMIT_TYPE, A.SUBMIT_NUM, A.ERR_DETAIL, A.LIST_ID, A.CAL_DATE  FROM APP___PAS__DBUSER.T_SUBMIT_MESSAGE_BOCIC A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PAS_submitMessageBocicWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
