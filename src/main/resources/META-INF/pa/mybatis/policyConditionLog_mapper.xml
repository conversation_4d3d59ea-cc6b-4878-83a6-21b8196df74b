<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyConditionLogDao">

	<sql id="PA_policyConditionLogWhereCondition">
		<if test=" condition_desc != null and condition_desc != ''  "><![CDATA[ AND A.CONDITION_DESC = #{condition_desc} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" insured_list_id  != null "><![CDATA[ AND A.INSURED_LIST_ID = #{insured_list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" uw_condition_type  != null "><![CDATA[ AND A.UW_CONDITION_TYPE = #{uw_condition_type} ]]></if>
		<if test=" condition_id  != null "><![CDATA[ AND A.CONDITION_ID = #{condition_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" insured_customer_id  != null "><![CDATA[ AND A.INSURED_CUSTOMER_ID = #{insured_customer_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyConditionLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyConditionLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_POLICY_CONDITION_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_CONDITION_LOG(
				CONDITION_DESC, INSERT_TIME, PRODUCT_CODE, UPDATE_TIME, ITEM_ID, BUSI_PROD_CODE, APPLY_CODE, 
				INSERT_TIMESTAMP, LOG_ID, POLICY_CODE, INSURED_LIST_ID, UPDATE_BY, UPDATE_TIMESTAMP, LOG_TYPE, 
				POLICY_CHG_ID, INSERT_BY, UW_CONDITION_TYPE, CONDITION_ID, BUSI_ITEM_ID, POLICY_ID, INSURED_CUSTOMER_ID ) 
			VALUES (
				#{condition_desc, jdbcType=VARCHAR}, SYSDATE , #{product_code, jdbcType=VARCHAR} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{insured_list_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} 
				, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{uw_condition_type, jdbcType=NUMERIC} , #{condition_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{insured_customer_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyConditionLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_CONDITION_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyConditionLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CONDITION_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			CONDITION_DESC = #{condition_desc, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    INSURED_LIST_ID = #{insured_list_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    UW_CONDITION_TYPE = #{uw_condition_type, jdbcType=NUMERIC} ,
		    CONDITION_ID = #{condition_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    INSURED_CUSTOMER_ID = #{insured_customer_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyConditionLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONDITION_DESC, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LOG_ID, A.POLICY_CODE, A.INSURED_LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.INSURED_CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyConditionLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyConditionLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONDITION_DESC, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LOG_ID, A.POLICY_CODE, A.INSURED_LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.INSURED_CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyConditionLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONDITION_DESC, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LOG_ID, A.POLICY_CODE, A.INSURED_LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.INSURED_CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyConditionLogWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyConditionLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_CONDITION_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyConditionLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CONDITION_DESC, B.PRODUCT_CODE, B.ITEM_ID, B.BUSI_PROD_CODE, B.APPLY_CODE, 
			B.LOG_ID, B.POLICY_CODE, B.INSURED_LIST_ID, B.LOG_TYPE, 
			B.POLICY_CHG_ID, B.UW_CONDITION_TYPE, B.CONDITION_ID, B.BUSI_ITEM_ID, B.POLICY_ID, B.INSURED_CUSTOMER_ID FROM (
					SELECT ROWNUM RN, A.CONDITION_DESC, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.LOG_ID, A.POLICY_CODE, A.INSURED_LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.INSURED_CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
