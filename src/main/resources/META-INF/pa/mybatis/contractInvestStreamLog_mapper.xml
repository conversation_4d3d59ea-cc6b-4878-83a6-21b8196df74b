<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractInvestStreamLog">
<!--
	<sql id="PA_contractInvestStreamLogWhereCondition">
		<if test=" accum_units  != null "><![CDATA[ AND A.ACCUM_UNITS = #{accum_units} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		<if test=" stream_invest_id  != null "><![CDATA[ AND A.STREAM_INVEST_ID = #{stream_invest_id} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" prem_type != null and prem_type != ''  "><![CDATA[ AND A.PREM_TYPE = #{prem_type} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractInvestStreamLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractInvestStreamLogByStreamInvestIdCondition">
		<if test=" stream_invest_id  != null "><![CDATA[ AND A.STREAM_INVEST_ID = #{stream_invest_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractInvestStreamLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG(
				ACCUM_UNITS, INTEREST_CAPITAL, INVEST_ID, STREAM_INVEST_ID, INSERT_TIME, INTEREST_SUM, UPDATE_TIME, 
				ITEM_ID, ACCOUNT_CODE, INSERT_TIMESTAMP, LOG_ID, START_DATE, UPDATE_BY, PREM_TYPE, 
				INTEREST_BALANCE, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, POLICY_ID, STREAM_ID ) 
			VALUES (
				#{accum_units, jdbcType=NUMERIC}, #{interest_capital, jdbcType=NUMERIC} , #{invest_id, jdbcType=NUMERIC} , #{stream_invest_id, jdbcType=NUMERIC} , SYSDATE , #{interest_sum, jdbcType=NUMERIC} , SYSDATE 
				, #{item_id, jdbcType=NUMERIC} , #{account_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{start_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{prem_type, jdbcType=VARCHAR} 
				, #{interest_balance, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{stream_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractInvestStreamLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG WHERE 1 = 1 ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractInvestStreamLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ACCUM_UNITS = #{accum_units, jdbcType=NUMERIC} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    INVEST_ID = #{invest_id, jdbcType=NUMERIC} ,
		    STREAM_INVEST_ID = #{stream_invest_id, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			PREM_TYPE = #{prem_type, jdbcType=VARCHAR} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    STREAM_ID = #{stream_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractInvestStreamLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.START_DATE, A.PREM_TYPE, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestStreamLogByLogIdCondition" />
	</select>
	
	<select id="PA_findContractInvestStreamLogByStreamInvestId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.START_DATE, A.PREM_TYPE, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestStreamLogByStreamInvestIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractInvestStreamLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.START_DATE, A.PREM_TYPE, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractInvestStreamLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.START_DATE, A.PREM_TYPE, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractInvestStreamLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractInvestStreamLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCUM_UNITS, B.INTEREST_CAPITAL, B.INVEST_ID, B.STREAM_INVEST_ID, B.INTEREST_SUM, 
			B.ITEM_ID, B.ACCOUNT_CODE, B.LOG_ID, B.START_DATE, B.PREM_TYPE, 
			B.INTEREST_BALANCE, B.LOG_TYPE, B.POLICY_CHG_ID, B.POLICY_ID, B.STREAM_ID FROM (
					SELECT ROWNUM RN, A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.START_DATE, A.PREM_TYPE, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
