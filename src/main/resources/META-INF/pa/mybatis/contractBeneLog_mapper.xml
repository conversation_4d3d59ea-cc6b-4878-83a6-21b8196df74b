<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractBeneLogDao">
<!--
	<sql id="PA_contractBeneLogWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" bene_type  != null "><![CDATA[ AND A.BENE_TYPE = #{bene_type} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" designation != null and designation != ''  "><![CDATA[ AND A.DESIGNATION = #{designation} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" legal_bene  != null "><![CDATA[ AND A.LEGAL_BENE = #{legal_bene} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" share_rate  != null "><![CDATA[ AND A.SHARE_RATE = #{share_rate} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" share_order  != null "><![CDATA[ AND A.SHARE_ORDER = #{share_order} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractBeneLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractBeneLogByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractBeneLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_BENE_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_BENE_LOG(
				ADDRESS_ID, CUSTOMER_NAME, BENE_TYPE, PRODUCT_CODE, CUSTOMER_ID, CUSTOMER_CERTI_CODE, APPLY_CODE, 
				INSERT_TIMESTAMP, UPDATE_BY, DESIGNATION, LIST_ID, CUSTOMER_BIRTHDAY, POLICY_CHG_ID, LEGAL_BENE, 
				BUSI_ITEM_ID, POLICY_ID, SHARE_RATE, CUSTOMER_CERT_TYPE, SHARE_ORDER, INSERT_TIME, UPDATE_TIME, 
				INSURED_ID, LOG_ID, POLICY_CODE, LOG_TYPE, UPDATE_TIMESTAMP, INSERT_BY, CUSTOMER_GENDER , BENE_KIND, COMPANY_ID, AGENT_RELATION ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{customer_name, jdbcType=VARCHAR} , #{bene_type, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{customer_certi_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{designation, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{customer_birthday, jdbcType=DATE} , #{policy_chg_id, jdbcType=NUMERIC} , #{legal_bene, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{share_rate, jdbcType=NUMERIC} , #{customer_cert_type, jdbcType=VARCHAR} , #{share_order, jdbcType=NUMERIC} , SYSDATE , SYSDATE 
				, #{insured_id, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{log_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC} 
				, #{bene_kind, jdbcType=VARCHAR}, #{company_id, jdbcType=NUMERIC} , #{agent_relation, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractBeneLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractBeneLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BENE_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    BENE_TYPE = #{bene_type, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			DESIGNATION = #{designation, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    LEGAL_BENE = #{legal_bene, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SHARE_RATE = #{share_rate, jdbcType=NUMERIC} ,
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
		    SHARE_ORDER = #{share_order, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
		    BENE_KIND = #{bene_kind, jdbcType=VARCHAR} ,
			COMPANY_ID = #{company_id, jdbcType=NUMERIC} ,
			AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR} , 
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractBeneLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_NAME, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, A.CUSTOMER_CERTI_CODE, A.APPLY_CODE, 
			A.DESIGNATION, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, A.LEGAL_BENE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE, A.CUSTOMER_CERT_TYPE, A.SHARE_ORDER, 
			A.INSURED_ID, A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.CUSTOMER_GENDER , A.BENE_KIND , 
			A.COMPANY_ID, AGENT_RELATION FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBeneLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="PA_findContractBeneLogByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_NAME, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, A.CUSTOMER_CERTI_CODE, A.APPLY_CODE, 
			A.DESIGNATION, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, A.LEGAL_BENE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE, A.CUSTOMER_CERT_TYPE, A.SHARE_ORDER, 
			A.INSURED_ID, A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.CUSTOMER_GENDER , A.BENE_KIND , 
			A.COMPANY_ID, AGENT_RELATION FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBeneLogByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractBeneLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_NAME, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, A.CUSTOMER_CERTI_CODE, A.APPLY_CODE, 
			A.DESIGNATION, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, A.LEGAL_BENE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE, A.CUSTOMER_CERT_TYPE, A.SHARE_ORDER, 
			A.INSURED_ID, A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.CUSTOMER_GENDER , A.BENE_KIND , 
			A.COMPANY_ID, AGENT_RELATION FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractBeneLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_NAME, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, A.CUSTOMER_CERTI_CODE, A.APPLY_CODE, 
			A.DESIGNATION, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, A.LEGAL_BENE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE, A.CUSTOMER_CERT_TYPE, A.SHARE_ORDER, 
			A.INSURED_ID, A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.CUSTOMER_GENDER , A.BENE_KIND , 
			A.COMPANY_ID, AGENT_RELATION FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractBeneLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractBeneLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_NAME, B.BENE_TYPE, B.PRODUCT_CODE, B.CUSTOMER_ID, B.CUSTOMER_CERTI_CODE, B.APPLY_CODE, 
			B.DESIGNATION, B.LIST_ID, B.CUSTOMER_BIRTHDAY, B.POLICY_CHG_ID, B.LEGAL_BENE, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.SHARE_RATE, B.CUSTOMER_CERT_TYPE, B.SHARE_ORDER, 
			B.INSURED_ID, B.LOG_ID, B.POLICY_CODE, B.LOG_TYPE, B.CUSTOMER_GENDER , A.BENE_KIND , A.COMPANY_ID, AGENT_RELATION  FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_NAME, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, A.CUSTOMER_CERTI_CODE, A.APPLY_CODE, 
			A.DESIGNATION, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, A.LEGAL_BENE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE, A.CUSTOMER_CERT_TYPE, A.SHARE_ORDER, 
			A.INSURED_ID, A.LOG_ID, A.POLICY_CODE, A.LOG_TYPE, A.CUSTOMER_GENDER , A.BENE_KIND , A.COMPANY_ID, AGENT_RELATION FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
