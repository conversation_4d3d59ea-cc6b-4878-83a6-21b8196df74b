<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.test.mapper">
	<sql id="PA_taxValueWhereCondition">
		<if test=" live_city_english != null and live_city_english != ''  "><![CDATA[ AND A.LIVE_CITY_ENGLISH = #{live_city_english} ]]></if>
		<if test=" customer_tax_ename != null and customer_tax_ename != ''  "><![CDATA[ AND A.CUSTOMER_TAX_ENAME = #{customer_tax_ename} ]]></if>
		<if test=" cust_cert_end_date  != null  and  cust_cert_end_date  != ''  "><![CDATA[ AND A.CUST_CERT_END_DATE = #{cust_cert_end_date} ]]></if>
		<if test=" live_district_english != null and live_district_english != ''  "><![CDATA[ AND A.LIVE_DISTRICT_ENGLISH = #{live_district_english} ]]></if>
		<if test=" customertax_id  != null "><![CDATA[ AND A.CUSTOMERTAX_ID = #{customertax_id} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" standbyflag3 != null and standbyflag3 != ''  "><![CDATA[ AND A.STANDBYFLAG3 = #{standbyflag3} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
		<if test=" standbyflag1 != null and standbyflag1 != ''  "><![CDATA[ AND A.STANDBYFLAG1 = #{standbyflag1} ]]></if>
		<if test=" standbyflag2 != null and standbyflag2 != ''  "><![CDATA[ AND A.STANDBYFLAG2 = #{standbyflag2} ]]></if>
		<if test=" live_city_chn != null and live_city_chn != ''  "><![CDATA[ AND A.LIVE_CITY_CHN = #{live_city_chn} ]]></if>
		<if test=" live_state_chn != null and live_state_chn != ''  "><![CDATA[ AND A.LIVE_STATE_CHN = #{live_state_chn} ]]></if>
		<if test=" live_country_code_english != null and live_country_code_english != ''  "><![CDATA[ AND A.LIVE_COUNTRY_CODE_ENGLISH = #{live_country_code_english} ]]></if>
		<if test=" country_code != null and country_code != ''  "><![CDATA[ AND A.COUNTRY_CODE = #{country_code} ]]></if>
		<if test=" is_delete  != null "><![CDATA[ AND A.IS_DELETE = #{is_delete} ]]></if>
		<if test=" tax_country_str != null and tax_country_str != ''  "><![CDATA[ AND A.TAX_COUNTRY_STR = #{tax_country_str} ]]></if>
		<if test=" customer_tax_certi_code != null and customer_tax_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_TAX_CERTI_CODE = #{customer_tax_certi_code} ]]></if>
		<if test=" tax_resident_type != null and tax_resident_type != ''  "><![CDATA[ AND A.TAX_RESIDENT_TYPE = #{tax_resident_type} ]]></if>
		<if test=" no_even_value  != null "><![CDATA[ AND A.NO_EVEN_VALUE = #{no_even_value} ]]></if>
		<if test=" customer_tax_cert_type != null and customer_tax_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_TAX_CERT_TYPE = #{customer_tax_cert_type} ]]></if>
		<if test=" due_diligence_ind != null and due_diligence_ind != ''  "><![CDATA[ AND A.DUE_DILIGENCE_IND = #{due_diligence_ind} ]]></if>
		<if test=" surrender_value  != null "><![CDATA[ AND A.SURRENDER_VALUE = #{surrender_value} ]]></if>
		<if test=" live_state_english != null and live_state_english != ''  "><![CDATA[ AND A.LIVE_STATE_ENGLISH = #{live_state_english} ]]></if>
		<if test=" live_address_chn != null and live_address_chn != ''  "><![CDATA[ AND A.LIVE_ADDRESS_CHN = #{live_address_chn} ]]></if>
		<if test=" no_universal_value  != null "><![CDATA[ AND A.NO_UNIVERSAL_VALUE = #{no_universal_value} ]]></if>
		<if test=" live_district_chn != null and live_district_chn != ''  "><![CDATA[ AND A.LIVE_DISTRICT_CHN = #{live_district_chn} ]]></if>
		<if test=" no_participating_value  != null "><![CDATA[ AND A.NO_PARTICIPATING_VALUE = #{no_participating_value} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" participating_value  != null "><![CDATA[ AND A.PARTICIPATING_VALUE = #{participating_value} ]]></if>
		<if test=" submitted_account_flag  != null "><![CDATA[ AND A.SUBMITTED_ACCOUNT_FLAG = #{submitted_account_flag} ]]></if>
		<if test=" cause_no_str != null and cause_no_str != ''  "><![CDATA[ AND A.CAUSE_NO_STR = #{cause_no_str} ]]></if>
		<if test=" cvs_id  != null "><![CDATA[ AND A.CVS_ID = #{cvs_id} ]]></if>
		<if test=" live_address_english != null and live_address_english != ''  "><![CDATA[ AND A.LIVE_ADDRESS_ENGLISH = #{live_address_english} ]]></if>
		<if test=" tax_number_str != null and tax_number_str != ''  "><![CDATA[ AND A.TAX_NUMBER_STR = #{tax_number_str} ]]></if>
		<if test=" live_country_code_chn != null and live_country_code_chn != ''  "><![CDATA[ AND A.LIVE_COUNTRY_CODE_CHN = #{live_country_code_chn} ]]></if>
		<if test=" survival_value  != null "><![CDATA[ AND A.SURVIVAL_VALUE = #{survival_value} ]]></if>
		<if test=" closed_account_flag  != null "><![CDATA[ AND A.CLOSED_ACCOUNT_FLAG = #{closed_account_flag} ]]></if>
		<if test=" universal_value  != null "><![CDATA[ AND A.UNIVERSAL_VALUE = #{universal_value} ]]></if>
		<if test=" no_survival_value  != null "><![CDATA[ AND A.NO_SURVIVAL_VALUE = #{no_survival_value} ]]></if>
		<if test=" address_tax_id  != null "><![CDATA[ AND A.ADDRESS_TAX_ID = #{address_tax_id} ]]></if>
		<if test=" birth_country_code_english != null and birth_country_code_english != ''  "><![CDATA[ AND A.BIRTH_COUNTRY_CODE_ENGLISH = #{birth_country_code_english} ]]></if>
		<if test=" submitted_year  != null "><![CDATA[ AND A.SUBMITTED_YEAR = #{submitted_year} ]]></if>
		<if test=" account_balance  != null "><![CDATA[ AND A.ACCOUNT_BALANCE = #{account_balance} ]]></if>
		<if test=" customer_tax_birthday  != null  and  customer_tax_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_TAX_BIRTHDAY = #{customer_tax_birthday} ]]></if>
		<if test=" cust_cert_star_date  != null  and  cust_cert_star_date  != ''  "><![CDATA[ AND A.CUST_CERT_STAR_DATE = #{cust_cert_star_date} ]]></if>
		<if test=" even_value  != null "><![CDATA[ AND A.EVEN_VALUE = #{even_value} ]]></if>
		<if test=" self_prove_flag  != null "><![CDATA[ AND A.SELF_PROVE_FLAG = #{self_prove_flag} ]]></if>
		<if test=" customer_tax_gender  != null "><![CDATA[ AND A.CUSTOMER_TAX_GENDER = #{customer_tax_gender} ]]></if>
		<if test=" payment  != null "><![CDATA[ AND A.PAYMENT = #{payment} ]]></if>
		<if test=" customer_tax_name != null and customer_tax_name != ''  "><![CDATA[ AND A.CUSTOMER_TAX_NAME = #{customer_tax_name} ]]></if>
		<if test=" customer_tax_surname != null and customer_tax_surname != ''  "><![CDATA[ AND A.CUSTOMER_TAX_SURNAME = #{customer_tax_surname} ]]></if>
	</sql>

	<!-- 按索引生成的查询条件 -->
	<sql id="PA_queryTaxValueByCvsIdCondition">
		<if test=" cvs_id  != null "><![CDATA[ AND A.CVS_ID = #{cvs_id} ]]></if>
	</sql>	
	<sql id="PA_queryTaxValueByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_queryTaxValueByDueDiligenceIndCondition">
		<if test=" due_diligence_ind != null and due_diligence_ind != '' "><![CDATA[ AND A.DUE_DILIGENCE_IND = #{due_diligence_ind} ]]></if>
	</sql>	
	<sql id="PA_queryTaxValueBySubmittedYearCondition">
		<if test=" submitted_year  != null "><![CDATA[ AND A.SUBMITTED_YEAR = #{submitted_year} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="PA_addTaxValue" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="cvs_id">
			SELECT APP___PAS__DBUSER.S_T_TAX_VALUE_CVS_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_VALUE(
				LIVE_CITY_ENGLISH, CUSTOMER_TAX_ENAME, CUST_CERT_END_DATE, LIVE_DISTRICT_ENGLISH, UPDATE_BY, CUSTOMERTAX_ID, MOBILE_TEL, 
				STANDBYFLAG3, CASH_VALUE, STANDBYFLAG1, STANDBYFLAG2, LIVE_CITY_CHN, LIVE_STATE_CHN, LIVE_COUNTRY_CODE_ENGLISH, 
				COUNTRY_CODE, IS_DELETE, UPDATE_TIME, TAX_COUNTRY_STR, CUSTOMER_TAX_CERTI_CODE, TAX_RESIDENT_TYPE, NO_EVEN_VALUE, 
				CUSTOMER_TAX_CERT_TYPE, DUE_DILIGENCE_IND, UPDATE_TIMESTAMP, INSERT_BY, SURRENDER_VALUE, LIVE_STATE_ENGLISH, LIVE_ADDRESS_CHN, 
				NO_UNIVERSAL_VALUE, LIVE_DISTRICT_CHN, NO_PARTICIPATING_VALUE, CUSTOMER_ID, PARTICIPATING_VALUE, INSERT_TIMESTAMP, SUBMITTED_ACCOUNT_FLAG, 
				CAUSE_NO_STR, CVS_ID, LIVE_ADDRESS_ENGLISH, TAX_NUMBER_STR, LIVE_COUNTRY_CODE_CHN, SURVIVAL_VALUE, CLOSED_ACCOUNT_FLAG, 
				UNIVERSAL_VALUE, INSERT_TIME, NO_SURVIVAL_VALUE, ADDRESS_TAX_ID, BIRTH_COUNTRY_CODE_ENGLISH, SUBMITTED_YEAR, ACCOUNT_BALANCE, 
				CUSTOMER_TAX_BIRTHDAY, CUST_CERT_STAR_DATE, EVEN_VALUE, SELF_PROVE_FLAG, CUSTOMER_TAX_GENDER, PAYMENT, CUSTOMER_TAX_NAME, 
				CUSTOMER_TAX_SURNAME ) 
			VALUES (
				#{live_city_english, jdbcType=VARCHAR}, #{customer_tax_ename, jdbcType=VARCHAR} , #{cust_cert_end_date, jdbcType=DATE} , #{live_district_english, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{customertax_id, jdbcType=NUMERIC} , #{mobile_tel, jdbcType=VARCHAR} 
				, #{standbyflag3, jdbcType=VARCHAR} , #{cash_value, jdbcType=NUMERIC} , #{standbyflag1, jdbcType=VARCHAR} , #{standbyflag2, jdbcType=VARCHAR} , #{live_city_chn, jdbcType=VARCHAR} , #{live_state_chn, jdbcType=VARCHAR} , #{live_country_code_english, jdbcType=VARCHAR} 
				, #{country_code, jdbcType=VARCHAR} , 0, SYSDATE , #{tax_country_str, jdbcType=VARCHAR} , #{customer_tax_certi_code, jdbcType=VARCHAR} , #{tax_resident_type, jdbcType=VARCHAR} , #{no_even_value, jdbcType=NUMERIC} 
				, #{customer_tax_cert_type, jdbcType=VARCHAR} , #{due_diligence_ind, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{surrender_value, jdbcType=NUMERIC} , #{live_state_english, jdbcType=VARCHAR} , #{live_address_chn, jdbcType=VARCHAR} 
				, #{no_universal_value, jdbcType=NUMERIC} , #{live_district_chn, jdbcType=VARCHAR} , #{no_participating_value, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} , #{participating_value, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{submitted_account_flag, jdbcType=NUMERIC} 
				, #{cause_no_str, jdbcType=VARCHAR} , #{cvs_id, jdbcType=NUMERIC} , #{live_address_english, jdbcType=VARCHAR} , #{tax_number_str, jdbcType=VARCHAR} , #{live_country_code_chn, jdbcType=VARCHAR} , #{survival_value, jdbcType=NUMERIC} , #{closed_account_flag, jdbcType=NUMERIC} 
				, #{universal_value, jdbcType=NUMERIC} , SYSDATE , #{no_survival_value, jdbcType=NUMERIC} , #{address_tax_id, jdbcType=NUMERIC} , #{birth_country_code_english, jdbcType=VARCHAR} , #{submitted_year, jdbcType=NUMERIC} , #{account_balance, jdbcType=NUMERIC} 
				, #{customer_tax_birthday, jdbcType=DATE} , #{cust_cert_star_date, jdbcType=DATE} , #{even_value, jdbcType=NUMERIC} , #{self_prove_flag, jdbcType=NUMERIC} , #{customer_tax_gender, jdbcType=NUMERIC} , #{payment, jdbcType=NUMERIC} , #{customer_tax_name, jdbcType=VARCHAR} 
				, #{customer_tax_surname, jdbcType=VARCHAR} )  
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="PA_deleteTaxValue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TAX_VALUE WHERE CVS_ID = #{cvs_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="PA_updateTaxValue" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TAX_VALUE ]]>
		<set>
			<trim suffixOverrides=",">
			LIVE_CITY_ENGLISH = #{live_city_english, jdbcType=VARCHAR} ,
			CUSTOMER_TAX_ENAME = #{customer_tax_ename, jdbcType=VARCHAR} ,
		    CUST_CERT_END_DATE = #{cust_cert_end_date, jdbcType=DATE} ,
			LIVE_DISTRICT_ENGLISH = #{live_district_english, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CUSTOMERTAX_ID = #{customertax_id, jdbcType=NUMERIC} ,
			MOBILE_TEL = #{mobile_tel, jdbcType=VARCHAR} ,
			STANDBYFLAG3 = #{standbyflag3, jdbcType=VARCHAR} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
			STANDBYFLAG1 = #{standbyflag1, jdbcType=VARCHAR} ,
			STANDBYFLAG2 = #{standbyflag2, jdbcType=VARCHAR} ,
			LIVE_CITY_CHN = #{live_city_chn, jdbcType=VARCHAR} ,
			LIVE_STATE_CHN = #{live_state_chn, jdbcType=VARCHAR} ,
			LIVE_COUNTRY_CODE_ENGLISH = #{live_country_code_english, jdbcType=VARCHAR} ,
			COUNTRY_CODE = #{country_code, jdbcType=VARCHAR} ,
		    IS_DELETE = #{is_delete, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			TAX_COUNTRY_STR = #{tax_country_str, jdbcType=VARCHAR} ,
			CUSTOMER_TAX_CERTI_CODE = #{customer_tax_certi_code, jdbcType=VARCHAR} ,
			TAX_RESIDENT_TYPE = #{tax_resident_type, jdbcType=VARCHAR} ,
		    NO_EVEN_VALUE = #{no_even_value, jdbcType=NUMERIC} ,
			CUSTOMER_TAX_CERT_TYPE = #{customer_tax_cert_type, jdbcType=VARCHAR} ,
			DUE_DILIGENCE_IND = #{due_diligence_ind, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SURRENDER_VALUE = #{surrender_value, jdbcType=NUMERIC} ,
			LIVE_STATE_ENGLISH = #{live_state_english, jdbcType=VARCHAR} ,
			LIVE_ADDRESS_CHN = #{live_address_chn, jdbcType=VARCHAR} ,
		    NO_UNIVERSAL_VALUE = #{no_universal_value, jdbcType=NUMERIC} ,
			LIVE_DISTRICT_CHN = #{live_district_chn, jdbcType=VARCHAR} ,
		    NO_PARTICIPATING_VALUE = #{no_participating_value, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    PARTICIPATING_VALUE = #{participating_value, jdbcType=NUMERIC} ,
		    SUBMITTED_ACCOUNT_FLAG = #{submitted_account_flag, jdbcType=NUMERIC} ,
			CAUSE_NO_STR = #{cause_no_str, jdbcType=VARCHAR} ,
		    CVS_ID = #{cvs_id, jdbcType=NUMERIC} ,
			LIVE_ADDRESS_ENGLISH = #{live_address_english, jdbcType=VARCHAR} ,
			TAX_NUMBER_STR = #{tax_number_str, jdbcType=VARCHAR} ,
			LIVE_COUNTRY_CODE_CHN = #{live_country_code_chn, jdbcType=VARCHAR} ,
		    SURVIVAL_VALUE = #{survival_value, jdbcType=NUMERIC} ,
		    CLOSED_ACCOUNT_FLAG = #{closed_account_flag, jdbcType=NUMERIC} ,
		    UNIVERSAL_VALUE = #{universal_value, jdbcType=NUMERIC} ,
		    NO_SURVIVAL_VALUE = #{no_survival_value, jdbcType=NUMERIC} ,
		    ADDRESS_TAX_ID = #{address_tax_id, jdbcType=NUMERIC} ,
			BIRTH_COUNTRY_CODE_ENGLISH = #{birth_country_code_english, jdbcType=VARCHAR} ,
		    SUBMITTED_YEAR = #{submitted_year, jdbcType=NUMERIC} ,
		    ACCOUNT_BALANCE = #{account_balance, jdbcType=NUMERIC} ,
		    CUSTOMER_TAX_BIRTHDAY = #{customer_tax_birthday, jdbcType=DATE} ,
		    CUST_CERT_STAR_DATE = #{cust_cert_star_date, jdbcType=DATE} ,
		    EVEN_VALUE = #{even_value, jdbcType=NUMERIC} ,
		    SELF_PROVE_FLAG = #{self_prove_flag, jdbcType=NUMERIC} ,
		    CUSTOMER_TAX_GENDER = #{customer_tax_gender, jdbcType=NUMERIC} ,
		    PAYMENT = #{payment, jdbcType=NUMERIC} ,
			CUSTOMER_TAX_NAME = #{customer_tax_name, jdbcType=VARCHAR} ,
			CUSTOMER_TAX_SURNAME = #{customer_tax_surname, jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE CVS_ID = #{cvs_id} ]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="PA_findTaxValueByCvsId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIVE_CITY_ENGLISH, A.CUSTOMER_TAX_ENAME, A.CUST_CERT_END_DATE, A.LIVE_DISTRICT_ENGLISH, A.CUSTOMERTAX_ID, A.MOBILE_TEL, 
			A.STANDBYFLAG3, A.CASH_VALUE, A.STANDBYFLAG1, A.STANDBYFLAG2, A.LIVE_CITY_CHN, A.LIVE_STATE_CHN, A.LIVE_COUNTRY_CODE_ENGLISH, 
			A.COUNTRY_CODE, A.IS_DELETE, A.CUSTOMER_TAX_CERTI_CODE, A.TAX_RESIDENT_TYPE, A.NO_EVEN_VALUE, A.CUSTOMER_TAX_CERT_TYPE, 
			A.DUE_DILIGENCE_IND, A.SURRENDER_VALUE, A.LIVE_STATE_ENGLISH, A.LIVE_ADDRESS_CHN, A.NO_UNIVERSAL_VALUE, 
			A.LIVE_DISTRICT_CHN, A.NO_PARTICIPATING_VALUE, A.CUSTOMER_ID, A.PARTICIPATING_VALUE, A.SUBMITTED_ACCOUNT_FLAG, A.CVS_ID, 
			A.LIVE_ADDRESS_ENGLISH, A.LIVE_COUNTRY_CODE_CHN, A.SURVIVAL_VALUE, A.CLOSED_ACCOUNT_FLAG, A.UNIVERSAL_VALUE, A.NO_SURVIVAL_VALUE, 
			A.BIRTH_COUNTRY_CODE_ENGLISH, A.SUBMITTED_YEAR, A.ACCOUNT_BALANCE, A.CUSTOMER_TAX_BIRTHDAY, A.CUST_CERT_STAR_DATE, A.EVEN_VALUE, A.SELF_PROVE_FLAG, 
			A.CUSTOMER_TAX_GENDER, A.PAYMENT, A.CUSTOMER_TAX_NAME, A.CUSTOMER_TAX_SURNAME FROM APP___PAS__DBUSER.T_TAX_VALUE A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxValueByCvsIdCondition" />
		<![CDATA[ ORDER BY A.CVS_ID ]]>
	</select>


	<!-- 按map查询操作 -->
	<select id="PA_findAllMapTaxValue" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_VALUE, A.NO_UNIVERSAL_VALUE, A.CLOSED_ACCOUNT_FLAG, A.UNIVERSAL_VALUE, A.NO_PARTICIPATING_VALUE, A.NO_SURVIVAL_VALUE, 
			A.CUSTOMER_ID, A.SUBMITTED_YEAR, A.ACCOUNT_BALANCE, A.PARTICIPATING_VALUE, A.SUBMITTED_ACCOUNT_FLAG, 
			A.CVS_ID, A.NO_EVEN_VALUE, A.EVEN_VALUE, A.PAYMENT, A.CASH_VALUE, A.DUE_DILIGENCE_IND, 
			A.SURRENDER_VALUE FROM APP___PAS__DBUSER.T_TAX_VALUE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="PA_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CVS_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAllTaxValue" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIVE_CITY_ENGLISH, A.CUSTOMER_TAX_ENAME, A.CUST_CERT_END_DATE, A.LIVE_DISTRICT_ENGLISH, A.CUSTOMERTAX_ID, A.MOBILE_TEL, 
			A.STANDBYFLAG3, A.CASH_VALUE, A.STANDBYFLAG1, A.STANDBYFLAG2, A.LIVE_CITY_CHN, A.LIVE_STATE_CHN, A.LIVE_COUNTRY_CODE_ENGLISH, 
			A.COUNTRY_CODE, A.IS_DELETE, A.CUSTOMER_TAX_CERTI_CODE, A.TAX_RESIDENT_TYPE, A.NO_EVEN_VALUE, A.CUSTOMER_TAX_CERT_TYPE, 
			A.DUE_DILIGENCE_IND, A.SURRENDER_VALUE, A.LIVE_STATE_ENGLISH, A.LIVE_ADDRESS_CHN, A.NO_UNIVERSAL_VALUE, 
			A.LIVE_DISTRICT_CHN, A.NO_PARTICIPATING_VALUE, A.CUSTOMER_ID, A.PARTICIPATING_VALUE, A.SUBMITTED_ACCOUNT_FLAG, A.CVS_ID, 
			A.LIVE_ADDRESS_ENGLISH, A.LIVE_COUNTRY_CODE_CHN, A.SURVIVAL_VALUE, A.CLOSED_ACCOUNT_FLAG, A.UNIVERSAL_VALUE, A.NO_SURVIVAL_VALUE, 
			A.BIRTH_COUNTRY_CODE_ENGLISH, A.SUBMITTED_YEAR, A.ACCOUNT_BALANCE, A.CUSTOMER_TAX_BIRTHDAY, A.CUST_CERT_STAR_DATE, A.EVEN_VALUE, A.SELF_PROVE_FLAG, 
			A.CUSTOMER_TAX_GENDER, A.PAYMENT, A.CUSTOMER_TAX_NAME, A.CUSTOMER_TAX_SURNAME FROM APP___PAS__DBUSER.T_TAX_VALUE A WHERE 1=1  ]]>
			<include refid="PA_taxValueWhereCondition" />
		<![CDATA[ ORDER BY A.CVS_ID ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="PA_findTaxValueTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TAX_VALUE A WHERE 1 = 1  ]]>
		<!-- <include refid="PA_请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="PA_queryTaxValueForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LIVE_CITY_ENGLISH, B.CUSTOMER_TAX_ENAME, B.CUST_CERT_END_DATE, B.LIVE_DISTRICT_ENGLISH, B.CUSTOMERTAX_ID, B.MOBILE_TEL, 
			B.STANDBYFLAG3, B.CASH_VALUE, B.STANDBYFLAG1, B.STANDBYFLAG2, B.LIVE_CITY_CHN, B.LIVE_STATE_CHN, B.LIVE_COUNTRY_CODE_ENGLISH, 
			B.COUNTRY_CODE, B.IS_DELETE, B.CUSTOMER_TAX_CERTI_CODE, B.TAX_RESIDENT_TYPE, B.NO_EVEN_VALUE, B.CUSTOMER_TAX_CERT_TYPE, 
			B.DUE_DILIGENCE_IND, B.SURRENDER_VALUE, B.LIVE_STATE_ENGLISH, B.LIVE_ADDRESS_CHN, B.NO_UNIVERSAL_VALUE, 
			B.LIVE_DISTRICT_CHN, B.NO_PARTICIPATING_VALUE, B.CUSTOMER_ID, B.PARTICIPATING_VALUE, B.SUBMITTED_ACCOUNT_FLAG, B.CVS_ID, 
			B.LIVE_ADDRESS_ENGLISH, B.LIVE_COUNTRY_CODE_CHN, B.SURVIVAL_VALUE, B.CLOSED_ACCOUNT_FLAG, B.UNIVERSAL_VALUE, B.NO_SURVIVAL_VALUE, 
			B.BIRTH_COUNTRY_CODE_ENGLISH, B.SUBMITTED_YEAR, B.ACCOUNT_BALANCE, B.CUSTOMER_TAX_BIRTHDAY, B.CUST_CERT_STAR_DATE, B.EVEN_VALUE, B.SELF_PROVE_FLAG, 
			B.CUSTOMER_TAX_GENDER, B.PAYMENT, B.CUSTOMER_TAX_NAME, B.CUSTOMER_TAX_SURNAME FROM (
					SELECT ROWNUM RN, A.LIVE_CITY_ENGLISH, A.CUSTOMER_TAX_ENAME, A.CUST_CERT_END_DATE, A.LIVE_DISTRICT_ENGLISH, A.CUSTOMERTAX_ID, A.MOBILE_TEL, 
			A.STANDBYFLAG3, A.CASH_VALUE, A.STANDBYFLAG1, A.STANDBYFLAG2, A.LIVE_CITY_CHN, A.LIVE_STATE_CHN, A.LIVE_COUNTRY_CODE_ENGLISH, 
			A.COUNTRY_CODE, A.IS_DELETE, A.CUSTOMER_TAX_CERTI_CODE, A.TAX_RESIDENT_TYPE, A.NO_EVEN_VALUE, A.CUSTOMER_TAX_CERT_TYPE, 
			A.DUE_DILIGENCE_IND, A.SURRENDER_VALUE, A.LIVE_STATE_ENGLISH, A.LIVE_ADDRESS_CHN, A.NO_UNIVERSAL_VALUE, 
			A.LIVE_DISTRICT_CHN, A.NO_PARTICIPATING_VALUE, A.CUSTOMER_ID, A.PARTICIPATING_VALUE, A.SUBMITTED_ACCOUNT_FLAG, A.CVS_ID, 
			A.LIVE_ADDRESS_ENGLISH, A.LIVE_COUNTRY_CODE_CHN, A.SURVIVAL_VALUE, A.CLOSED_ACCOUNT_FLAG, A.UNIVERSAL_VALUE, A.NO_SURVIVAL_VALUE, 
			A.BIRTH_COUNTRY_CODE_ENGLISH, A.SUBMITTED_YEAR, A.ACCOUNT_BALANCE, A.CUSTOMER_TAX_BIRTHDAY, A.CUST_CERT_STAR_DATE, A.EVEN_VALUE, A.SELF_PROVE_FLAG, 
			A.CUSTOMER_TAX_GENDER, A.PAYMENT, A.CUSTOMER_TAX_NAME, A.CUSTOMER_TAX_SURNAME FROM APP___PAS__DBUSER.T_TAX_VALUE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="PA_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CVS_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

</mapper>
