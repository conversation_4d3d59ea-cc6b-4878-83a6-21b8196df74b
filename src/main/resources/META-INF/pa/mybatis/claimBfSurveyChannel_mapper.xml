<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.dao.IClaimBfSurveyChannelDao">

	<sql id="claimBfSurveyChannelWhereCondition">
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimBfSurveyChannelByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimBfSurveyChannel"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CLAIM_BF_SURVEY_CHANNEL__LIS.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL(
				INSERT_TIMESTAMP, CHANNEL_TYPE, UPDATE_BY, PLAN_ID, INSERT_TIME, LIST_ID, UPDATE_TIME, 
				UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{channel_type, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , SYSDATE 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimBfSurveyChannel" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL WHERE PLAN_ID = #{plan_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimBfSurveyChannel" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL ]]>
		<set>
		<trim suffixOverrides=",">
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimBfSurveyChannelByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANNEL_TYPE, A.PLAN_ID, A.LIST_ID FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL A WHERE 1 = 1  ]]>
		<include refid="queryClaimBfSurveyChannelByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimBfSurveyChannel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANNEL_TYPE, A.PLAN_ID, A.LIST_ID FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimBfSurveyChannel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANNEL_TYPE,(SELECT B.SALES_CHANNEL_NAME FROM APP___PAS__DBUSER.T_SALES_CHANNEL B WHERE A.CHANNEL_TYPE = B.SALES_CHANNEL_CODE ) AS CHANNEL_NAME,  
                         A.PLAN_ID, A.LIST_ID FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimBfSurveyChannelWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimBfSurveyChannelTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimBfSurveyChannelForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CHANNEL_TYPE, B.PLAN_ID, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.CHANNEL_TYPE, A.PLAN_ID, A.LIST_ID FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_CHANNEL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="findClaimBfSurveyChannelByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.* FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A WHERE 1 = 1]]>
		<include refid="findClaimBfSurveyChannelByPlanIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<sql id="findClaimBfSurveyChannelByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>
	
</mapper>
