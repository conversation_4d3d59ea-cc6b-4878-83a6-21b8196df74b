<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="bank">

	<sql id="PA_bankWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" dept_id  != null "><![CDATA[ AND A.DEPT_ID = #{dept_id} ]]></if>
		<if test=" parent_bank != null and parent_bank != ''  "><![CDATA[ AND A.PARENT_BANK = #{parent_bank} ]]></if>
		<if test=" branch_id  != null "><![CDATA[ AND A.BRANCH_ID = #{branch_id} ]]></if>
		<if test=" id_type != null and id_type != ''  "><![CDATA[ AND A.ID_TYPE = #{id_type} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" id_number != null and id_number != ''  "><![CDATA[ AND A.ID_NUMBER = #{id_number} ]]></if>
		<if test=" transfer_type != null and transfer_type != ''  "><![CDATA[ AND A.TRANSFER_TYPE = #{transfer_type} ]]></if>
		<if test=" agency_type != null and agency_type != ''  "><![CDATA[ AND A.AGENCY_TYPE = #{agency_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" recall_date  != null  and  recall_date  != ''  "><![CDATA[ AND A.RECALL_DATE = #{recall_date} ]]></if>
		<if test=" updater_id  != null "><![CDATA[ AND A.UPDATER_ID = #{updater_id} ]]></if>
		<if test=" recorder_id  != null "><![CDATA[ AND A.RECORDER_ID = #{recorder_id} ]]></if>
		<if test=" bank_org_id  != null "><![CDATA[ AND A.BANK_ORG_ID = #{bank_org_id} ]]></if>
		<if test=" organ_id  != null "><![CDATA[ AND A.ORGAN_ID = #{organ_id} ]]></if>		
		<if test=" leader_id  != null "><![CDATA[ AND A.LEADER_ID = #{leader_id} ]]></if>
		<if test=" head_id  != null "><![CDATA[ AND A.HEAD_ID = #{head_id} ]]></if>
		<if test=" bank_class != null and bank_class != ''  "><![CDATA[ AND A.BANK_CLASS = #{bank_class} ]]></if>
		<if test=" is_basic != null and is_basic != ''  "><![CDATA[ AND A.IS_BASIC = #{is_basic} ]]></if>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
		<if test=" telephone != null and telephone != ''  "><![CDATA[ AND A.TELEPHONE = #{telephone} ]]></if>
		<if test=" bank_name != null and bank_name != ''  "><![CDATA[ AND A.BANK_NAME = #{bank_name} ]]></if>
		<if test=" comeback_date  != null  and  comeback_date  != ''  "><![CDATA[ AND A.COMEBACK_DATE = #{comeback_date} ]]></if>
		<if test=" manager_id  != null "><![CDATA[ AND A.MANAGER_ID = #{manager_id} ]]></if>
		<if test=" abbr_name != null and abbr_name != ''  "><![CDATA[ AND A.ABBR_NAME = #{abbr_name} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" create_date  != null  and  create_date  != ''  "><![CDATA[ AND A.CREATE_DATE = #{create_date} ]]></if>
		<if test=" bank_type != null and bank_type != ''  "><![CDATA[ AND A.BANK_TYPE = #{bank_type} ]]></if>
		<if test=" branch_bank != null and branch_bank != ''  "><![CDATA[ AND A.BRANCH_BANK = #{branch_bank} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" business_cate != null and business_cate != ''  "><![CDATA[ AND A.BUSINESS_CATE = #{business_cate} ]]></if>
		<if test=" is_bank_disk != null and is_bank_disk != ''  "><![CDATA[ AND A.IS_BANK_DISK = #{is_bank_disk} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryBankByBankCodeCondition">
		<if test=" bank_code != null and bank_code != '' "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addBank"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BANK(
				ADDRESS_ID, DEPT_ID, PARENT_BANK, BRANCH_ID, ID_TYPE, STATUS, ID_NUMBER, 
				TRANSFER_TYPE, AGENCY_TYPE, CHANNEL_TYPE, RECALL_DATE, UPDATER_ID, RECORDER_ID, BANK_ORG_ID, 
				ORGAN_ID, LEADER_ID, HEAD_ID, BANK_CLASS, IS_BASIC, COMPANY_ID, INSERT_TIME, 
				UPDATE_TIME, TELEPHONE, BANK_NAME, COMEBACK_DATE, MANAGER_ID, ABBR_NAME, BANK_CODE, 
				CREATE_DATE, BANK_TYPE, BRANCH_BANK, INTERNAL_CODE, BUSINESS_CATE ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{dept_id, jdbcType=NUMERIC} , #{parent_bank, jdbcType=VARCHAR} , #{branch_id, jdbcType=NUMERIC} , #{id_type, jdbcType=VARCHAR} , #{status, jdbcType=VARCHAR} , #{id_number, jdbcType=VARCHAR} 
				, #{transfer_type, jdbcType=VARCHAR} , #{agency_type, jdbcType=VARCHAR} , #{channel_type, jdbcType=VARCHAR} , #{recall_date, jdbcType=DATE} , #{updater_id, jdbcType=NUMERIC} , #{recorder_id, jdbcType=NUMERIC} , #{bank_org_id, jdbcType=NUMERIC} 
				, #{organ_id, jdbcType=NUMERIC} , #{leader_id, jdbcType=NUMERIC} , #{head_id, jdbcType=NUMERIC} , #{bank_class, jdbcType=VARCHAR} , #{is_basic, jdbcType=VARCHAR} , #{company_id, jdbcType=NUMERIC} , SYSDATE 
				, SYSDATE , #{telephone, jdbcType=VARCHAR} , #{bank_name, jdbcType=VARCHAR} , #{comeback_date, jdbcType=DATE} , #{manager_id, jdbcType=NUMERIC} , #{abbr_name, jdbcType=VARCHAR} , #{bank_code, jdbcType=VARCHAR} 
				, #{create_date, jdbcType=DATE} , #{bank_type, jdbcType=VARCHAR} , #{branch_bank, jdbcType=VARCHAR} , #{internal_code, jdbcType=VARCHAR} , #{business_cate, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作  -->	
	<delete id="PA_deleteBank" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BANK WHERE BANK_CODE= #{bank_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateBank" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BANK ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    DEPT_ID = #{dept_id, jdbcType=NUMERIC} ,
			PARENT_BANK = #{parent_bank, jdbcType=VARCHAR} ,
		    BRANCH_ID = #{branch_id, jdbcType=NUMERIC} ,
			ID_TYPE = #{id_type, jdbcType=VARCHAR} ,
			STATUS = #{status, jdbcType=VARCHAR} ,
			ID_NUMBER = #{id_number, jdbcType=VARCHAR} ,
			TRANSFER_TYPE = #{transfer_type, jdbcType=VARCHAR} ,
			AGENCY_TYPE = #{agency_type, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    RECALL_DATE = #{recall_date, jdbcType=DATE} ,
		    UPDATER_ID = #{updater_id, jdbcType=NUMERIC} ,
		    RECORDER_ID = #{recorder_id, jdbcType=NUMERIC} ,
		    BANK_ORG_ID = #{bank_org_id, jdbcType=NUMERIC} ,
		    ORGAN_ID = #{organ_id, jdbcType=NUMERIC} ,
		    LEADER_ID = #{leader_id, jdbcType=NUMERIC} ,
		    HEAD_ID = #{head_id, jdbcType=NUMERIC} ,
			BANK_CLASS = #{bank_class, jdbcType=VARCHAR} ,
			IS_BASIC = #{is_basic, jdbcType=VARCHAR} ,
		    COMPANY_ID = #{company_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			TELEPHONE = #{telephone, jdbcType=VARCHAR} ,
			BANK_NAME = #{bank_name, jdbcType=VARCHAR} ,
		    COMEBACK_DATE = #{comeback_date, jdbcType=DATE} ,
		    MANAGER_ID = #{manager_id, jdbcType=NUMERIC} ,
			ABBR_NAME = #{abbr_name, jdbcType=VARCHAR} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    CREATE_DATE = #{create_date, jdbcType=DATE} ,
			BANK_TYPE = #{bank_type, jdbcType=VARCHAR} ,
			BRANCH_BANK = #{branch_bank, jdbcType=VARCHAR} ,
			INTERNAL_CODE = #{internal_code, jdbcType=VARCHAR} ,
			BUSINESS_CATE = #{business_cate, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findBankByBankCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.DEPT_ID, A.PARENT_BANK, A.BRANCH_ID, A.ID_TYPE, A.STATUS, A.ID_NUMBER, 
			A.TRANSFER_TYPE, A.AGENCY_TYPE, A.CHANNEL_TYPE, A.RECALL_DATE, A.UPDATER_ID, A.RECORDER_ID, A.BANK_ORG_ID, 
			A.ORGAN_ID, A.LEADER_ID, A.HEAD_ID, A.BANK_CLASS, A.IS_BASIC, A.COMPANY_ID, 
			A.TELEPHONE, A.BANK_NAME, A.COMEBACK_DATE, A.MANAGER_ID, A.ABBR_NAME, A.BANK_CODE, 
			A.CREATE_DATE, A.BANK_TYPE, A.BRANCH_BANK, A.INTERNAL_CODE, A.BUSINESS_CATE FROM APP___PAS__DBUSER.T_BANK A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankByBankCodeCondition" />
	</select>

	<!-- 按索引查询操作 -->	
	<select id="PA_findBank" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.DEPT_ID, A.PARENT_BANK, A.BRANCH_ID, A.ID_TYPE, A.STATUS, A.ID_NUMBER, 
			A.TRANSFER_TYPE, A.AGENCY_TYPE, A.CHANNEL_TYPE, A.RECALL_DATE, A.UPDATER_ID, A.RECORDER_ID, A.BANK_ORG_ID, 
			A.ORGAN_ID, A.LEADER_ID, A.HEAD_ID, A.BANK_CLASS, A.IS_BASIC, A.COMPANY_ID, 
			A.TELEPHONE, A.BANK_NAME, A.COMEBACK_DATE, A.MANAGER_ID, A.ABBR_NAME, A.BANK_CODE, 
			A.CREATE_DATE, A.BANK_TYPE, A.BRANCH_BANK, A.INTERNAL_CODE, A.BUSINESS_CATE,A.IS_CREDIT_CARD FROM APP___PAS__DBUSER.T_BANK A WHERE 1 = 1  ]]>
		<include refid="PA_bankWhereCondition" />
	</select>
	<!-- 按机构查询银行 -->
	<select id="PA_findBankByOrg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select B.ADDRESS_ID,
	       B.DEPT_ID,
	       B.PARENT_BANK,
	       B.BRANCH_ID,
	       B.ID_TYPE,
	       B.STATUS,
	       B.ID_NUMBER,
	       B.TRANSFER_TYPE,
	       B.AGENCY_TYPE,
	       B.CHANNEL_TYPE,
	       B.RECALL_DATE,
	       B.UPDATER_ID,
	       B.RECORDER_ID,
	       B.BANK_ORG_ID,
	       B.ORGAN_ID,
	       B.LEADER_ID,
	       B.HEAD_ID,
	       B.BANK_CLASS,
	       B.IS_BASIC,
	       B.COMPANY_ID,
	       B.TELEPHONE,
	       B.BANK_NAME,
	       B.COMEBACK_DATE,
	       B.MANAGER_ID,
	       B.ABBR_NAME,
	       B.BANK_CODE,
	       B.CREATE_DATE,
	       B.BANK_TYPE,
	       B.BRANCH_BANK,
	       B.INTERNAL_CODE,
	       B.BUSINESS_CATE
	  from APP___PAS__DBUSER.T_BANK_ORGEN_REL R, APP___PAS__DBUSER.T_BANK B
	 where r.organ_code = #{organ_code}
	   and r.bank_code = b.bank_code
	 order by b.bank_name
		
		]]>
	</select>
	
<!-- 按map查询操作 -->
	<select id="PA_findAllMapBank" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.DEPT_ID, A.PARENT_BANK, A.BRANCH_ID, A.ID_TYPE, A.STATUS, A.ID_NUMBER, 
			A.TRANSFER_TYPE, A.AGENCY_TYPE, A.CHANNEL_TYPE, A.RECALL_DATE, A.UPDATER_ID, A.RECORDER_ID, A.BANK_ORG_ID, 
			A.ORGAN_ID, A.LEADER_ID, A.HEAD_ID, A.BANK_CLASS, A.IS_BASIC, A.COMPANY_ID, 
			A.TELEPHONE, A.BANK_NAME, A.COMEBACK_DATE, A.MANAGER_ID, A.ABBR_NAME, A.BANK_CODE, 
			A.CREATE_DATE, A.BANK_TYPE, A.BRANCH_BANK, A.INTERNAL_CODE, A.BUSINESS_CATE FROM APP___PAS__DBUSER.T_BANK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllBank" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.DEPT_ID, A.PARENT_BANK, A.BRANCH_ID, A.ID_TYPE, A.STATUS, A.ID_NUMBER, 
			A.TRANSFER_TYPE, A.AGENCY_TYPE, A.CHANNEL_TYPE, A.RECALL_DATE, A.UPDATER_ID, A.RECORDER_ID, A.BANK_ORG_ID, 
			A.ORGAN_ID, A.LEADER_ID, A.HEAD_ID, A.BANK_CLASS, A.IS_BASIC, A.COMPANY_ID, 
			A.TELEPHONE, A.BANK_NAME, A.COMEBACK_DATE, A.MANAGER_ID, A.ABBR_NAME, A.BANK_CODE, 
			A.CREATE_DATE, A.BANK_TYPE, A.BRANCH_BANK, A.INTERNAL_CODE, A.BUSINESS_CATE,A.IS_BANK_DISK FROM APP___PAS__DBUSER.T_BANK A WHERE A.BANK_NAME like 'NTS%' AND A.IS_CREDIT_CARD='0' AND ROWNUM <=  1000  ]]>
		<include refid="PA_bankWhereCondition" /> 
		<![CDATA[ORDER BY A.BANK_CODE]]>
	</select>
	
<!-- 查询HC银行信息所有操作-->
	<select id="PA_findAllHCBank" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.DEPT_ID, A.PARENT_BANK, A.BRANCH_ID, A.ID_TYPE, A.STATUS, A.ID_NUMBER, 
			A.TRANSFER_TYPE, A.AGENCY_TYPE, A.CHANNEL_TYPE, A.RECALL_DATE, A.UPDATER_ID, A.RECORDER_ID, A.BANK_ORG_ID, 
			A.ORGAN_ID, A.LEADER_ID, A.HEAD_ID, A.BANK_CLASS, A.IS_BASIC, A.COMPANY_ID, 
			A.TELEPHONE, A.BANK_NAME, A.COMEBACK_DATE, A.MANAGER_ID, A.ABBR_NAME, A.BANK_CODE, 
			A.CREATE_DATE, A.BANK_TYPE, A.BRANCH_BANK, A.INTERNAL_CODE, A.BUSINESS_CATE,A.IS_BANK_DISK FROM APP___PAS__DBUSER.T_BANK A WHERE IS_CREDIT_CARD = '0'  AND IS_BANK_DISK = '1'
			AND EXISTS
 			(SELECT 1
         	 FROM DEV_PAS.t_Bank_Orgen_Rel B
         	WHERE A.BANK_CODE = B.BANK_CODE
          	 AND EXISTS (SELECT ORGAN_CODE
                  FROM (SELECT C.ORGAN_CODE
                          FROM DEV_PAS.T_UDMP_ORG_REL C where  C.ORGAN_CODE = #{organ_code}                         
                           ) E
                 WHERE 1 = 1
                   AND B.ORGAN_CODE = E.ORGAN_CODE)) ]]>		
	</select>
	
	
	

<!-- 查询个数操作 -->
	<select id="PA_findBankTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BANK A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<!-- 根据银行代码查询是否是建行网点 -->
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} AND 
		(A.BANK_NAME LIKE '%建设%' OR A.BANK_NAME LIKE '%建行%') ]]></if>
		
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryBankForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.DEPT_ID, B.PARENT_BANK, B.BRANCH_ID, B.ID_TYPE, B.STATUS, B.ID_NUMBER, 
			B.TRANSFER_TYPE, B.AGENCY_TYPE, B.CHANNEL_TYPE, B.RECALL_DATE, B.UPDATER_ID, B.RECORDER_ID, B.BANK_ORG_ID, 
			B.ORGAN_ID, B.LEADER_ID, B.HEAD_ID, B.BANK_CLASS, B.IS_BASIC, B.COMPANY_ID, 
			B.TELEPHONE, B.BANK_NAME, B.COMEBACK_DATE, B.MANAGER_ID, B.ABBR_NAME, B.BANK_CODE, 
			B.CREATE_DATE, B.BANK_TYPE, B.BRANCH_BANK, B.INTERNAL_CODE, B.BUSINESS_CATE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.DEPT_ID, A.PARENT_BANK, A.BRANCH_ID, A.ID_TYPE, A.STATUS, A.ID_NUMBER, 
			A.TRANSFER_TYPE, A.AGENCY_TYPE, A.CHANNEL_TYPE, A.RECALL_DATE, A.UPDATER_ID, A.RECORDER_ID, A.BANK_ORG_ID, 
			A.ORGAN_ID, A.LEADER_ID, A.HEAD_ID, A.BANK_CLASS, A.IS_BASIC, A.COMPANY_ID, 
			A.TELEPHONE, A.BANK_NAME, A.COMEBACK_DATE, A.MANAGER_ID, A.ABBR_NAME, A.BANK_CODE, 
			A.CREATE_DATE, A.BANK_TYPE, A.BRANCH_BANK, A.INTERNAL_CODE, A.BUSINESS_CATE FROM APP___PAS__DBUSER.T_BANK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
		
		<!-- 查询代理人区部组代码 -->
	<select id="PA_getAgentSale" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.AGENT_CODE,
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.SALES_ORGAN_CODE,
	              '2',
	              BU.Parent_Code,
	              '3',
	              QU.PARENT_CODE,
	              '4',
	              QU.PARENT_CODE,
	              '') AGENT_AREA,
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.SALES_ORGAN_NAME,
	              '2',
	              QU.SALES_ORGAN_NAME,
	              '3',
	              RU.SALES_ORGAN_NAME,
	              '4',
	              RU.SALES_ORGAN_NAME,
	              '') AGENT_AREA_NAME,
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '2',
	              BU.SALES_ORGAN_CODE,
	              '3',
	              BU.Parent_Code,
	              '4',
	              BU.Parent_Code,
	              '') AGENT_PART,
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '2',
	              BU.SALES_ORGAN_NAME,
	              '3',
	              QU.SALES_ORGAN_NAME,
	              '4',
	              QU.SALES_ORGAN_NAME,
	              '') AGENT_PART_NAME,
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.SALES_ORGAN_CODE,
	              '2',
	              BU.SALES_ORGAN_CODE,
	              '3',
	              BU.SALES_ORGAN_CODE,
	              '4',
	              BU.SALES_ORGAN_CODE,
	              '') AGENT_GROUP,
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.SALES_ORGAN_NAME,
	              '2',
	              BU.SALES_ORGAN_NAME,
	              '3',
	              BU.SALES_ORGAN_NAME,
	              '4',
	              BU.SALES_ORGAN_NAME,
	              '') AGENT_GROUP_NAME,
	       
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.BRANCH_ATTR,
	              '2',
	              BU.BRANCH_ATTR,
	              '3',
	              QU.BRANCH_ATTR,
	              '4',
	              QU.BRANCH_ATTR,
	              '') OUT_AGENT_PART, /*外部营业部*/
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.SALES_ORGAN_NAME,
	              '2',
	              BU.SALES_ORGAN_NAME,
	              '3',
	              QU.SALES_ORGAN_NAME,
	              '4',
	              QU.SALES_ORGAN_NAME,
	              '') OUT_AGENT_PART_NAME, /*外部营业部名称*/
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.BRANCH_ATTR,
	              '2',
	              BU.BRANCH_ATTR,
	              '3',
	              BU.BRANCH_ATTR,
	              '4',
	              BU.BRANCH_ATTR,
	              '') OUT_AGENT_GROUP, /*外部营业组*/
	       decode(to_char(BU.ORGAN_LEVEL_CODE),
	              '1',
	              BU.SALES_ORGAN_NAME,
	              '2',
	              BU.SALES_ORGAN_NAME,
	              '3',
	              BU.SALES_ORGAN_NAME,
	              '4',
	              BU.SALES_ORGAN_NAME,
	              '') OUT_AGENT_GROUP_NAME, /*外部营业组名称*/
	              BU.BRANCH_ATTR /*展业机构外部编码*/
	  FROM APP___PAS__DBUSER.T_AGENT T
	  LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN BU
	    ON T.GROUP_CODE = BU.SALES_ORGAN_CODE
	  LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN QU
	    ON BU.PARENT_CODE = QU.SALES_ORGAN_CODE
	  LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN RU
	    ON QU.PARENT_CODE = RU.SALES_ORGAN_CODE
		WHERE T.AGENT_CODE =  #{agent_code}
		]]>
	</select>
	
	<select id="PA_findStdBankCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.STD_BANK_CODE
			  FROM APP___PAS__DBUSER.T_BANK A
			 WHERE A.BANK_CODE = #{bank_code}
			   AND ROWNUM = 1
		]]>
	</select>
	
</mapper>
