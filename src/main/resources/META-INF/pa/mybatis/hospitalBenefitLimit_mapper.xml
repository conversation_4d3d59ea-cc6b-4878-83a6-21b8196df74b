<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="hospitalBenefitLimit">

	<sql id="PA_hospitalBenefitLimit">
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]></if>
		<if test=" hospital_limit_amount  != null "><![CDATA[ AND A.HOSPITAL_LIMIT_AMOUNT = #{hospital_limit_amount} ]]></if>
		<if test=" config_id  != null "><![CDATA[ AND A.CONFIG_ID IN ${config_id} ]]></if>
		<if test=" configids  != null and configids != ''  "><![CDATA[ AND trim(A.CONFIG_ID) IN (${configids}) ]]></if>		
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE LIKE CONCAT('%',CONCAT(#{product_code},'%'))  ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryHospitalBenefitLimitByConfigIdCondition">
		<if test=" config_id  != null "><![CDATA[ AND A.CONFIG_ID = #{config_id} ]]></if>
	</sql>	
	<sql id="PA_queryHospitalBenefitLimitByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>	
	<sql id="PA_queryHospitalBenefitLimitByProductCodeCondition">
		<if test=" product_code != null and product_code != '' "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
	</sql>	
	
	<sql id="PA_queryHospitalBenefitLimitLikeProductCodeCondition">
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE LIKE CONCAT('%',CONCAT(#{product_code},'%'))  ]]></if>
	</sql>

<!-- 添加操作 -->
	<insert id="PA_addHospitalBenefitLimit"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="config_id">
			SELECT APP___PAS__DBUSER.S_T_HOSPITAL_BENEFIT_LIMIT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT(
				INSERT_TIMESTAMP, ORGAN_CODE, UPDATE_BY, HOSPITAL_LIMIT_AMOUNT, INSERT_TIME, CONFIG_ID, PRODUCT_CODE, 
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{hospital_limit_amount, jdbcType=NUMERIC} , SYSDATE , #{config_id, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteHospitalBenefitLimit" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT WHERE CONFIG_ID = #{config_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateHospitalBenefitLimit" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    HOSPITAL_LIMIT_AMOUNT = #{hospital_limit_amount, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE CONFIG_ID = #{config_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findHospitalBenefitLimitByConfigId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.HOSPITAL_LIMIT_AMOUNT, A.CONFIG_ID, A.PRODUCT_CODE FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryHospitalBenefitLimitByConfigIdCondition" />
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]>
	</select>
	
	<select id="PA_findHospitalBenefitLimitByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.HOSPITAL_LIMIT_AMOUNT, A.CONFIG_ID, A.PRODUCT_CODE FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryHospitalBenefitLimitByOrganCodeCondition" />
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]>
	</select>
	
	<select id="PA_findHospitalBenefitLimitByProductCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.HOSPITAL_LIMIT_AMOUNT, A.CONFIG_ID, A.PRODUCT_CODE FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryHospitalBenefitLimitByProductCodeCondition" />
		<include refid="PA_queryHospitalBenefitLimitLikeProductCodeCondition" />
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]>
	</select>
	
	<select id="PA_findHospitalBenefitLimit"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.HOSPITAL_LIMIT_AMOUNT, A.CONFIG_ID, A.PRODUCT_CODE FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryHospitalBenefitLimitByOrganCodeCondition" />
		<include refid="PA_queryHospitalBenefitLimitLikeProductCodeCondition" />
	</select>

<!-- 按map查询操作 -->
	<select id="PA_findAllMapHospitalBenefitLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.HOSPITAL_LIMIT_AMOUNT, A.CONFIG_ID, A.PRODUCT_CODE FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllHospitalBenefitLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN,A.ORGAN_CODE, A.HOSPITAL_LIMIT_AMOUNT, A.CONFIG_ID, A.PRODUCT_CODE, 
		(SELECT UOR.ORGAN_NAME
                                  FROM DEV_PAS.T_udmp_org_rel UOR
                                 WHERE UOR.ORGAN_CODE = A.ORGAN_CODE) AS ORGAN_NAME
		FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE 1=1  ]]>
		<include refid="PA_hospitalBenefitLimit" />
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
	</select>
<!-- 查询所有操作 -->
	<select id="PA_findAllHospitalOrg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE
                                  FROM DEV_PAS.T_udmp_org_rel  A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_hospitalBenefitLimit" />
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
	</select>
	
<!-- 查询个数操作 -->
	<select id="PA_findHospitalBenefitLimitTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PA_hospitalBenefitLimit" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryHospitalBenefitLimitForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT ROWNUM RN, EE.* FROM (
	SELECT ROWNUM RN, B.ORGAN_CODE, B.HOSPITAL_LIMIT_AMOUNT, B.CONFIG_ID, B.PRODUCT_CODE,B.ORGAN_NAME FROM (
					SELECT ROWNUM RN, A.ORGAN_CODE, A.HOSPITAL_LIMIT_AMOUNT, A.CONFIG_ID, A.PRODUCT_CODE,
					(SELECT UOR.ORGAN_NAME
                                  FROM DEV_PAS.T_udmp_org_rel UOR
                                 WHERE UOR.ORGAN_CODE = A.ORGAN_CODE) AS ORGAN_NAME FROM 
					APP___PAS__DBUSER.T_HOSPITAL_BENEFIT_LIMIT A WHERE 1 =1  ]]>
		<include refid="PA_hospitalBenefitLimit" />
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
		<![CDATA[ )  B)EE
		WHERE 1=1 AND  EE.RN > #{GREATER_NUM} AND EE.RN <= #{LESS_NUM} ]]>
	</select>
	
</mapper>
