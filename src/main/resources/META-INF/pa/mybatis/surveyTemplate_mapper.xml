<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="surveyTemplate">
<!--
	<sql id="PA_surveyTemplateWhereCondition">
		<if test=" defined_by  != null "><![CDATA[ AND A.DEFINED_BY = #{defined_by} ]]></if>
		<if test=" defined_for_organ != null and defined_for_organ != ''  "><![CDATA[ AND A.DEFINED_FOR_ORGAN = #{defined_for_organ} ]]></if>
		<if test=" define_time  != null  and  define_time  != ''  "><![CDATA[ AND A.DEFINE_TIME = #{define_time} ]]></if>
		<if test=" survey_template_id  != null "><![CDATA[ AND <PERSON>.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
		<if test=" template_name != null and template_name != ''  "><![CDATA[ AND A.TEMPLATE_NAME = #{template_name} ]]></if>
		<if test=" survey_templ_code != null and survey_templ_code != ''  "><![CDATA[ AND A.SURVEY_TEMPL_CODE = #{survey_templ_code} ]]></if>
		<if test=" survey_object  != null "><![CDATA[ AND A.SURVEY_OBJECT = #{survey_object} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySurveyTemplateBySurveyTemplateIdCondition">
		<if test=" survey_template_id  != null "><![CDATA[ AND A.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSurveyTemplate"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVEY_TEMPLATE(
				DEFINED_BY, DEFINED_FOR_ORGAN, INSERT_TIME, UPDATE_TIME, INSERT_TIMESTAMP, DEFINE_TIME, UPDATE_BY, 
				SURVEY_TEMPLATE_ID, TEMPLATE_NAME, SURVEY_TEMPL_CODE, UPDATE_TIMESTAMP, INSERT_BY, SURVEY_OBJECT ) 
			VALUES (
				#{defined_by, jdbcType=NUMERIC}, #{defined_for_organ, jdbcType=VARCHAR} , SYSDATE , SYSDATE , CURRENT_TIMESTAMP, #{define_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} 
				, #{survey_template_id, jdbcType=NUMERIC} , #{template_name, jdbcType=VARCHAR} , #{survey_templ_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{survey_object, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSurveyTemplate" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SURVEY_TEMPLATE WHERE SURVEY_TEMPLATE_ID=#{survey_template_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSurveyTemplate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURVEY_TEMPLATE ]]>
		<set>
		<trim suffixOverrides=",">
		    DEFINED_BY = #{defined_by, jdbcType=NUMERIC} ,
			DEFINED_FOR_ORGAN = #{defined_for_organ, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    DEFINE_TIME = #{define_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SURVEY_TEMPLATE_ID = #{survey_template_id, jdbcType=NUMERIC} ,
			TEMPLATE_NAME = #{template_name, jdbcType=VARCHAR} ,
			SURVEY_TEMPL_CODE = #{survey_templ_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SURVEY_OBJECT = #{survey_object, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1  ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSurveyTemplateBySurveyTemplateId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFINED_BY, A.DEFINED_FOR_ORGAN, A.DEFINE_TIME, 
			A.SURVEY_TEMPLATE_ID, A.TEMPLATE_NAME, A.SURVEY_TEMPL_CODE, A.SURVEY_OBJECT FROM APP___PAS__DBUSER.T_SURVEY_TEMPLATE A WHERE 1 = 1  ]]>
		<include refid="PA_querySurveyTemplateBySurveyTemplateIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSurveyTemplate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFINED_BY, A.DEFINED_FOR_ORGAN, A.DEFINE_TIME, 
			A.SURVEY_TEMPLATE_ID, A.TEMPLATE_NAME, A.SURVEY_TEMPL_CODE, A.SURVEY_OBJECT FROM APP___PAS__DBUSER.T_SURVEY_TEMPLATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSurveyTemplate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFINED_BY, A.DEFINED_FOR_ORGAN, A.DEFINE_TIME, 
			A.SURVEY_TEMPLATE_ID, A.TEMPLATE_NAME, A.SURVEY_TEMPL_CODE, A.SURVEY_OBJECT FROM APP___PAS__DBUSER.T_SURVEY_TEMPLATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSurveyTemplateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SURVEY_TEMPLATE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySurveyTemplateForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.DEFINED_BY, B.DEFINED_FOR_ORGAN, B.DEFINE_TIME, 
			B.SURVEY_TEMPLATE_ID, B.TEMPLATE_NAME, B.SURVEY_TEMPL_CODE, B.SURVEY_OBJECT FROM (
					SELECT ROWNUM RN, A.DEFINED_BY, A.DEFINED_FOR_ORGAN, A.DEFINE_TIME, 
			A.SURVEY_TEMPLATE_ID, A.TEMPLATE_NAME, A.SURVEY_TEMPL_CODE, A.SURVEY_OBJECT FROM APP___PAS__DBUSER.T_SURVEY_TEMPLATE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
