<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.CvTxInfoDetailDaoImpl">

	<sql id="PA_cvTxInfoDetailWhereCondition">
		<if test=" list_id != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" main_id != null "><![CDATA[ AND A.MAIN_ID = #{main_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" calc_date != null  "><![CDATA[ AND A.CALC_DATE = #{calc_date} ]]></if>
		<if test=" tx_seq != null  "><![CDATA[ AND A.TX_SEQ = #{tx_seq} ]]></if>
		<if test=" cash_flag != null and cash_flag != ''  "><![CDATA[ AND A.CASH_FLAG = #{cash_flag} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="PA_addCvTxInfoDetail" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CV_TX_INFO_DETAIL.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CV_TX_INFO_DETAIL(LIST_ID,MAIN_ID,POLICY_CODE,TX_SEQ,
				CALC_DATE,CASH_FLAG,INSERT_BY,UPDATE_BY,INSERT_TIME,INSERT_TIMESTAMP,UPDATE_TIME,UPDATE_TIMESTAMP) 
			VALUES (
			    #{list_id, jdbcType=NUMERIC},
			    #{main_id, jdbcType=NUMERIC},
			    #{policy_code,jdbcType=VARCHAR},
				#{tx_seq, jdbcType=NUMERIC},
			    #{calc_date, jdbcType=DATE},
				#{cash_flag, jdbcType=VARCHAR},
				#{insert_by, jdbcType=NUMERIC},
				#{update_by, jdbcType=NUMERIC},
				SYSDATE,
				CURRENT_TIMESTAMP,
				SYSDATE,
				CURRENT_TIMESTAMP
				)
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="PA_deleteCvTxInfoDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CV_TX_INFO_DETAIL A WHERE 1=1]]>
		<include refid="PA_cvTxInfoDetailWhereCondition" />
	</delete>

    <!-- 单条查询操作 -->
	<select id="PA_findObjectCvTxInfoDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CODE,A.CALC_DATE,A.TX_SEQ,A.CASH_FLAG
		FROM APP___PAS__DBUSER.T_CV_TX_INFO_DETAIL A WHERE 1 = 1  ]]>
		<include refid="PA_cvTxInfoDetailWhereCondition" />
	</select>

    <!-- 多条查询操作 -->
	<select id="PA_findAllCvTxInfoDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CODE,A.CALC_DATE,A.TX_SEQ,A.CASH_FLAG
		FROM APP___PAS__DBUSER.T_CV_TX_INFO_DETAIL A WHERE 1 = 1  ]]>
		<include refid="PA_cvTxInfoDetailWhereCondition" />
	</select>

	<!-- 修改操作 -->
	<update id="PA_updateCvTxInfoDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CV_TX_INFO_DETAIL ]]>
		<set>
			<trim suffixOverrides=",">
		    main_id = #{main_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE CALC_DATE = #{calc_date, jdbcType=DATE} ]]>
	</update>
	
</mapper>
