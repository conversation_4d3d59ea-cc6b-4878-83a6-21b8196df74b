<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.policyDueDateDaoImpl">
	<sql id="PA_findPolicyDueDateByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<select id="PA_findPolicyDueDateByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT  * 
		FROM (SELECT TCE.policy_id ,TCE.item_id,TCE.due_date 
			  FROM APP___PAS__DBUSER.t_contract_extend TCE 
			  ) A
		WHERE 1=1 ]]>
		<include refid="PA_findPolicyDueDateByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID ]]>
	</select>
</mapper>