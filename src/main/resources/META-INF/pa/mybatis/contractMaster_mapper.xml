<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ContractMasterDaoImpl"> 

	<sql id="PA_contractMasterWhereCondition">
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" subinput_type != null and subinput_type != ''  "><![CDATA[ AND A.SUBINPUT_TYPE = #{subinput_type} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" sale_com_code != null and sale_com_code != ''  "><![CDATA[ AND A.SALE_COM_CODE = #{sale_com_code} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" double_mainrisk_flag  != null "><![CDATA[ AND A.DOUBLE_MAINRISK_FLAG = #{double_mainrisk_flag} ]]></if>
		<if test=" submit_channel  != null "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" sale_type != null and sale_type != ''  "><![CDATA[ AND A.SALE_TYPE = #{sale_type} ]]></if>
		<if test=" input_type != null and input_type != ''  "><![CDATA[ AND A.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" statistic_channel != null and statistic_channel != ''  "><![CDATA[ AND A.STATISTIC_CHANNEL = #{statistic_channel} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
		<if test=" relation_policy_code != null and relation_policy_code != ''  "><![CDATA[ AND A.RELATION_POLICY_CODE = #{relation_policy_code} ]]></if>
		<if test=" policy_flag != null and policy_flag != '' "><![CDATA[ AND A.POLICY_FLAG = #{policy_flag} ]]></if>
		<if test=" tax_extension_source != null and tax_extension_source != '' "><![CDATA[ AND A.TAX_EXTENSION_SOURCE = #{tax_extension_source} ]]></if>
		<if test=" policy_prd_flag != null and policy_prd_flag != '' "><![CDATA[ AND A.POLICY_PRD_FLAG = #{policy_prd_flag} ]]></if>
		<if test=" group_sale_type != null and group_sale_type != '' "><![CDATA[ AND A.group_sale_type = #{group_sale_type} ]]></if>
		<if test=" lapse_loan_suspend_date != null and lapse_loan_suspend_date != '' "><![CDATA[ AND A.lapse_loan_suspend_date = #{lapse_loan_suspend_date} ]]></if>
	    <if test=" banknrt_falg != null and banknrt_falg != '' "><![CDATA[ AND A.banknrt_falg = #{banknrt_falg} ]]></if>
	    <if test=" special_account_flag != null and special_account_flag != '' "><![CDATA[ AND A.special_account_flag = #{special_account_flag} ]]></if>
	    <if test=" is_channel_self_insured != null and is_channel_self_insured != '' "><![CDATA[ AND A.is_channel_self_insured = #{is_channel_self_insured} ]]></if>
	    <if test=" is_channel_mutual_insured != null and is_channel_mutual_insured != '' "><![CDATA[ AND A.is_channel_mutual_insured = #{is_channel_mutual_insured} ]]></if>
	    <if test=" trust_busi_flag  != null "><![CDATA[ AND A.TRUST_BUSI_FLAG = #{trust_busi_flag} ]]></if>
	    <if test=" notification_receive_method  != null "><![CDATA[ AND A.notification_receive_method = #{notification_receive_method} ]]></if>
		<if test=" tax_extension_sum_prem  != null "><![CDATA[ AND A.TAX_EXTENSION_SUM_PREM = #{tax_extension_sum_prem} ]]></if>
		<if test=" total_policy_sequence_no  != null "><![CDATA[ AND A.TOTAL_POLICY_SEQUENCE_NO = #{total_policy_sequence_no} ]]></if>
		<if test=" policy_sequence_no  != null "><![CDATA[ AND A.POLICY_SEQUENCE_NO = #{policy_sequence_no} ]]></if>
		<if test=" jointly_insured_type  != null "><![CDATA[ AND A.JOINTLY_INSURED_TYPE = #{jointly_insured_type} ]]></if>
		<if test=" self_apply_flag  != null "><![CDATA[ AND A.SELF_APPLY_FLAG = #{self_apply_flag} ]]></if>		
	    <if test=" per_fin_pvt_bank_code  != null "><![CDATA[ AND A.PER_FIN_PVT_BANK_CODE = #{per_fin_pvt_bank_code} ]]></if>	
	    <if test=" policy_lock_flag  != null "><![CDATA[ AND A.POLICY_LOCK_FLAG = #{policy_lock_flag} ]]></if>	
	    <if test=" is_Dflt_Acknowledge_Date  != null "><![CDATA[ AND A.IS_DFLT_ACKNOWLEDGE_DATE = #{is_Dflt_Acknowledge_Date} ]]></if>	
	    <if test=" merge_Signature_Flag != null "><![CDATA[ AND A.MERGE_SIGNATURE_FLAG = #{merge_signature_flag} ]]></if>			
		<if test="policy_relation_type_list != null and policy_relation_type_list.size() != 0 ">
			<![CDATA[ AND A.policy_relation_type in (]]>
				<foreach collection="policy_relation_type_list" item="policy_relation_type_item" index="index" open="" close="" separator=",">
					<![CDATA[ #{policy_relation_type_item} ]]>
				</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="PA_queryContractMasterByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<!-- 按索引生成的查询条件 -->
	<sql id="PA_queryContractMasterByPolicyIdCondition1">
		<if test=" policy_id  != null "><![CDATA[ A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="PA_queryContractMasterByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="PA_queryContractMasterByBranchCodeCondition">
		<if test=" branch_code != null and branch_code != '' "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
	</sql>
	<sql id="PA_queryContractMasterByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>
	<sql id="PA_queryContractMasterByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = trim(#{policy_code}) ]]></if>
		<if test=" relation_policy_code != null and relation_policy_code != ''  "><![CDATA[ AND A.RELATION_POLICY_CODE = #{relation_policy_code} ]]></if>
		<if test=" double_mainrisk_flag  != null "><![CDATA[ AND A.DOUBLE_MAINRISK_FLAG = #{double_mainrisk_flag} ]]></if>
	</sql>
	<!-- 添加操作 -->
	<insert id="PA_addContractMaster" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="policy_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_MASTER__POLICY_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_MASTER(MERGE_SIGNATURE_FLAG, RELATION_POLICY_CODE,
				POLICY_PWD, MEDIA_TYPE, APPLY_CODE, INTEREST_MODE, ORGAN_CODE, CHANNEL_TYPE, SALE_AGENT_NAME, 
				INSURED_FAMILY, UPDATE_BY, POLICY_ID, DERIVATION, UPDATE_TIME, SUBINPUT_TYPE, 
				BASIC_REMARK, SALE_COM_CODE, INPUT_DATE, POLICY_TYPE, EXPIRY_DATE, PWD_INVALID_FLAG, SUBMIT_CHANNEL, 
				LIABILITY_STATE, POLICY_CODE, SALE_AGENT_CODE, RERINSTATE_DATE, BRANCH_CODE, VALIDATE_DATE, UPDATE_TIMESTAMP, 
				INSERT_BY, AGENCY_CODE, MONEY_CODE, SERVICE_HANDLER_CODE, APPLY_DATE, INITIAL_VALIDATE_DATE, INSERT_TIMESTAMP, 
				SUBMISSION_DATE, SERVICE_HANDLER, E_SERVICE_FLAG, SERVICE_BANK_BRANCH, LAPSE_DATE, DC_INDI, SALE_TYPE, 
				INPUT_TYPE, INSERT_TIME, END_CAUSE, ISSUE_DATE, STATISTIC_CHANNEL, LAPSE_CAUSE, DECISION_CODE, 
				AGENT_ORG_ID, SERVICE_BANK, SUSPEND_DATE, INITIAL_PREM_DATE, SUSPEND_CAUSE, LANG_CODE, FORMER_ID, 
				BANK_AGENCY_FLAG, IS_SELF_INSURED, WINNING_START_FLAG,POLICY_FLAG,DOUBLE_MAINRISK_FLAG,TAX_EXTENSION_SOURCE,
				POLICY_PRD_FLAG,GROUP_SALE_TYPE,APPLY_TIME,IS_ALONE_INSURE,LAPSE_LOAN_SUSPEND_DATE,MEDICAL_INSURANCE_CARD,CALL_TIME_LIST,
				POLICY_REINSURE_FLAG,REINSURED_TIMES,BANK_MANAGER_NAME,BANK_MANAGER_LICENSENO,POLICY_RELATION_TYPE,TRUST_BUSI_FLAG,
				MULTI_MAINRISK_FLAG,MEET_POV_STANDARD_FLAG,IS_MUTUAL_INSURED,BANKNRT_FALG,SPECIAL_ACCOUNT_FLAG,IS_CHANNEL_SELF_INSURED,
				IS_CHANNEL_MUTUAL_INSURED ,NOTIFICATION_RECEIVE_METHOD,TAX_EXTENSION_SUM_PREM,TOTAL_POLICY_SEQUENCE_NO,POLICY_SEQUENCE_NO ,JOINTLY_INSURED_TYPE,SELF_APPLY_FLAG,PER_FIN_PVT_BANK_CODE,POLICY_LOCK_FLAG,IS_DFLT_ACKNOWLEDGE_DATE )
			VALUES (
			    #{merge_signature_flag,jdbcType=NUMERIC},#{relation_policy_code,jdbcType=VARCHAR},
				#{policy_pwd, jdbcType=VARCHAR}, #{media_type, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{interest_mode, jdbcType=NUMERIC} , #{organ_code, jdbcType=VARCHAR} , #{channel_type, jdbcType=VARCHAR} , #{sale_agent_name, jdbcType=VARCHAR} 
				, #{insured_family, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{derivation, jdbcType=VARCHAR} , SYSDATE , #{subinput_type, jdbcType=VARCHAR} 
				, #{basic_remark, jdbcType=VARCHAR} , #{sale_com_code, jdbcType=VARCHAR} , #{input_date, jdbcType=DATE} , #{policy_type, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} , #{pwd_invalid_flag, jdbcType=NUMERIC} , #{submit_channel, jdbcType=NUMERIC} 
				, #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{sale_agent_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{branch_code, jdbcType=VARCHAR} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{agency_code, jdbcType=VARCHAR} , #{money_code, jdbcType=VARCHAR} , #{service_handler_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{initial_validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{submission_date, jdbcType=DATE} , #{service_handler, jdbcType=VARCHAR} , #{e_service_flag, jdbcType=NUMERIC} , #{service_bank_branch, jdbcType=VARCHAR} , #{lapse_date, jdbcType=DATE} , #{dc_indi, jdbcType=NUMERIC} , #{sale_type, jdbcType=VARCHAR} 
				, #{input_type, jdbcType=VARCHAR} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{statistic_channel, jdbcType=VARCHAR} , #{lapse_cause, jdbcType=VARCHAR} , #{decision_code, jdbcType=VARCHAR} 
				, #{agent_org_id, jdbcType=VARCHAR} , #{service_bank, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{initial_prem_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{lang_code, jdbcType=VARCHAR} , #{former_id, jdbcType=NUMERIC} , #{bank_agency_flag, jdbcType=NUMERIC} 
				, #{is_self_insured, jdbcType=NUMERIC}, #{winning_start_flag, jdbcType=NUMERIC},#{policy_flag,jdbcType=VARCHAR}, #{double_mainrisk_flag, jdbcType=NUMERIC}, #{tax_extension_source, jdbcType=NUMERIC}, #{policy_prd_flag, jdbcType=NUMERIC},#{group_sale_type, jdbcType=VARCHAR},#{apply_time, jdbcType=VARCHAR}
				, #{is_alone_insure, jdbcType=NUMERIC}, #{lapse_loan_suspend_date, jdbcType=DATE}, #{medical_insurance_card, jdbcType=NUMERIC}, #{call_time_list, jdbcType=VARCHAR},#{policy_reinsure_flag, jdbcType=VARCHAR},#{reinsured_times, jdbcType=NUMERIC}
				, #{bank_manager_name, jdbcType=VARCHAR},#{bank_manager_licenseno, jdbcType=VARCHAR}, #{policy_relation_type, jdbcType=NUMERIC},#{trust_busi_flag,jdbcType=NUMERIC},#{multi_mainrisk_flag,jdbcType=NUMERIC},#{meet_pov_standard_flag,jdbcType=NUMERIC},#{is_mutual_insured,jdbcType=NUMERIC}
				, #{banknrt_falg,jdbcType=NUMERIC}, #{special_account_flag, jdbcType=VARCHAR},#{is_channel_self_insured,jdbcType=NUMERIC}, #{is_channel_mutual_insured,jdbcType=NUMERIC}, #{notification_receive_method,jdbcType=NUMERIC}, #{tax_extension_sum_prem,jdbcType=NUMERIC},#{total_policy_sequence_no, jdbcType=VARCHAR},#{policy_sequence_no, jdbcType=VARCHAR}
				,#{jointly_insured_type, jdbcType=VARCHAR},#{self_apply_flag,jdbcType=NUMERIC},#{per_fin_pvt_bank_code, jdbcType=VARCHAR},#{policy_lock_flag,jdbcType=NUMERIC},#{is_Dflt_Acknowledge_Date,jdbcType=NUMERIC}) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="PA_deleteContractMaster" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER WHERE POLICY_ID=#{policy_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="PA_updateContractMaster" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
			MERGE_SIGNATURE_FLAG = #{merge_signature_flag,jdbcType=NUMERIC},
			POLICY_RELATION_TYPE=#{policy_relation_type,jdbcType=NUMERIC},
			POLICY_PWD = #{policy_pwd, jdbcType=VARCHAR} ,
		    MEDIA_TYPE = #{media_type, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=CHAR} ,
			SALE_AGENT_NAME = #{sale_agent_name, jdbcType=VARCHAR} ,
		    INSURED_FAMILY = #{insured_family, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			DERIVATION = #{derivation, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			SUBINPUT_TYPE = #{subinput_type, jdbcType=VARCHAR} ,
			BASIC_REMARK = #{basic_remark, jdbcType=VARCHAR} ,
			SALE_COM_CODE = #{sale_com_code, jdbcType=VARCHAR} ,
		    INPUT_DATE = #{input_date, jdbcType=DATE} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    PWD_INVALID_FLAG = #{pwd_invalid_flag, jdbcType=NUMERIC} ,
		    SUBMIT_CHANNEL = #{submit_channel, jdbcType=NUMERIC} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			SALE_AGENT_CODE = #{sale_agent_code, jdbcType=VARCHAR} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENCY_CODE = #{agency_code, jdbcType=VARCHAR} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
			SERVICE_HANDLER_CODE = #{service_handler_code, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INITIAL_VALIDATE_DATE = #{initial_validate_date, jdbcType=DATE} ,
		    SUBMISSION_DATE = #{submission_date, jdbcType=DATE} ,
			SERVICE_HANDLER = #{service_handler, jdbcType=VARCHAR} ,
		    E_SERVICE_FLAG = #{e_service_flag, jdbcType=NUMERIC} ,
			SERVICE_BANK_BRANCH = #{service_bank_branch, jdbcType=VARCHAR} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    DC_INDI = #{dc_indi, jdbcType=NUMERIC} ,
			SALE_TYPE = #{sale_type, jdbcType=VARCHAR} ,
			INPUT_TYPE = #{input_type, jdbcType=VARCHAR} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			STATISTIC_CHANNEL = #{statistic_channel, jdbcType=VARCHAR} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
			AGENT_ORG_ID = #{agent_org_id, jdbcType=VARCHAR} ,
			SERVICE_BANK = #{service_bank, jdbcType=VARCHAR} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
			LANG_CODE = #{lang_code, jdbcType=VARCHAR} ,
		    FORMER_ID = #{former_id, jdbcType=NUMERIC} ,
		    RELATION_POLICY_CODE = #{relation_policy_code, jdbcType=VARCHAR} ,
		    POLICY_MANAGE_ORGAN = #{policy_manage_organ, jdbcType=VARCHAR},
		    group_sale_type = #{group_sale_type, jdbcType=VARCHAR},
		    LAPSE_LOAN_SUSPEND_DATE = #{lapse_loan_suspend_date, jdbcType=DATE} ,
		    <if test=" policy_flag != null and policy_flag != '' ">
		    <![CDATA[  POLICY_FLAG = #{policy_flag, jdbcType=VARCHAR}, ]]></if>
		    IS_CHANNEL_SELF_INSURED = #{is_channel_self_insured,jdbcType=NUMERIC}, 
		    IS_CHANNEL_MUTUAL_INSURED = #{is_channel_mutual_insured,jdbcType=NUMERIC},
		    POLICY_REINSURE_FLAG = #{policy_reinsure_flag, jdbcType=VARCHAR} ,
		    REINSURED_TIMES = #{reinsured_times, jdbcType=NUMERIC} ,
		    TRUST_BUSI_FLAG = #{trust_busi_flag,jdbcType=NUMERIC}, 
		    MULTI_MAINRISK_FLAG = #{multi_mainrisk_flag,jdbcType=NUMERIC},
		    MEET_POV_STANDARD_FLAG = #{meet_pov_standard_flag,jdbcType=NUMERIC},
		    IS_SELF_INSURED = #{is_self_insured, jdbcType=NUMERIC},
		    IS_MUTUAL_INSURED = #{is_mutual_insured,jdbcType=NUMERIC}, 
		    <if test=" banknrt_falg != null and banknrt_falg != '' ">
		    <![CDATA[ BANKNRT_FALG = #{banknrt_falg, jdbcType=NUMERIC}, ]]></if>
		    <if test=" special_account_flag != null and special_account_flag != '' ">
		    <![CDATA[ SPECIAL_ACCOUNT_FLAG = #{special_account_flag, jdbcType=NUMERIC}, ]]></if>
		 	NOTIFICATION_RECEIVE_METHOD = #{notification_receive_method,jdbcType=NUMERIC},
		 	TAX_EXTENSION_SUM_PREM = #{tax_extension_sum_prem,jdbcType=NUMERIC},
		 	TOTAL_POLICY_SEQUENCE_NO = #{total_policy_sequence_no, jdbcType=VARCHAR} ,
		 	POLICY_SEQUENCE_NO = #{policy_sequence_no, jdbcType=VARCHAR},
			JOINTLY_INSURED_TYPE = #{jointly_insured_type, jdbcType=VARCHAR}, 	
			<if test=" special_account_flag != null and special_account_flag != '' ">
		    <![CDATA[ SELF_APPLY_FLAG = #{self_apply_flag, jdbcType=NUMERIC}, ]]></if>
		    PER_FIN_PVT_BANK_CODE = #{per_fin_pvt_bank_code, jdbcType=VARCHAR},
		    POLICY_LOCK_FLAG = #{policy_lock_flag,jdbcType=NUMERIC},
		    IS_DFLT_ACKNOWLEDGE_DATE = #{is_Dflt_Acknowledge_Date,jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id, jdbcType=NUMERIC} ]]>
	</update>
 
	<update id="PA_updateContractMasterAllDate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
				VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
				EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
			</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>

	<update id="PA_updateContractMasterEndCause" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
				END_CAUSE = #{end_cause, jdbcType=VARCHAR}
				,
			</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	<update id="PA_updateContractMasterMark" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
				SPECIAL_CUS_FLAG =2
			</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>

   <update id="PA_updateContractMasterMediaType" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
				MEDIA_TYPE =2
			</trim>
		</set>
		<![CDATA[ WHERE  POLICY_CODE = #{policy_code} ]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="PA_findContractMasterByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, A.SALE_TYPE, 
			A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, A.LAPSE_CAUSE, A.DECISION_CODE, A.WINNING_START_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK,A.BANK_AGENCY_FLAG, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.RELATION_POLICY_CODE,A.IS_SELF_INSURED, 
			A.POLICY_FLAG,A.DOUBLE_MAINRISK_FLAG,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG,A.group_sale_type, 
			A.APPLY_TIME,A.IS_ALONE_INSURE,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST,A.POLICY_REINSURE_FLAG, A.TRUST_BUSI_FLAG, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.REINSURED_TIMES ,A.BANK_MANAGER_NAME, A.BANK_MANAGER_LICENSENO,A.POLICY_RELATION_TYPE,A.BANKNRT_FALG ,A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.LAPSE_LOAN_SUSPEND_DATE,
			A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE ]]>
		    A.POLICY_ID = #{policy_id}
		<![CDATA[ORDER BY A.VALIDATE_DATE DESC]]>
	</select>

	<select id="PA_findContractMasterByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, A.SALE_TYPE, 
			A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, A.LAPSE_CAUSE, A.DECISION_CODE, A.TRUST_BUSI_FLAG,A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.GROUP_SALE_TYPE,A.POLICY_REINSURE_FLAG  ,A.BANKNRT_FALG,
			A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM ,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterByApplyCodeCondition" />
	</select>

	<select id="PA_findContractMasterByBranchCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, A.SALE_TYPE, 
			A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, A.LAPSE_CAUSE, A.DECISION_CODE, A.TRUST_BUSI_FLAG,A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.GROUP_SALE_TYPE,A.POLICY_REINSURE_FLAG  ,A.BANKNRT_FALG,
			A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterByBranchCodeCondition" />
	</select>

	<select id="PA_findContractMasterByOrganCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.TRUST_BUSI_FLAG, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, A.SALE_TYPE, 
			A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, A.LAPSE_CAUSE, A.DECISION_CODE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.GROUP_SALE_TYPE,A.POLICY_REINSURE_FLAG,
			A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterByOrganCodeCondition" />
	</select>

	<select id="PA_findContractMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,
			(SELECT TPRT.POLICY_RELATION_TYPE FROM DEV_PAS.T_POLICY_RELATION_TYPE TPRT WHERE TPRT.POLICY_RELATION_TYPE = A.POLICY_RELATION_TYPE)POLICY_RELATION_NAME,
			A.POLICY_FLAG, A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.RELATION_POLICY_CODE,
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.TRUST_BUSI_FLAG, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE,A.is_self_insured, A.insert_time,]]>
			<if test=" agent_code != null and agent_code != '' "><![CDATA[
				(select ag.agent_name from APP___PAS__DBUSER.T_agent ag where ag.agent_code = #{agent_code} and rownum = 1) as agent_name,]]></if>		
			<![CDATA[
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.DOUBLE_MAINRISK_FLAG,A.BANKNRT_FALG  ,
			A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.POLICY_MANAGE_ORGAN,
			A.GROUP_SALE_TYPE,A.LAPSE_LOAN_SUSPEND_DATE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,A.TAX_EXTENSION_SUM_PREM ,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1    ]]>
		<include refid="PA_queryContractMasterByPolicyCodeCondition" />
		<include refid="PA_queryContractMasterByApplyCodeCondition" />
		<include refid="PA_queryContractMasterByPolicyIdCondition" />
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ 
			AND exists (select 1 from APP___PAS__DBUSER.T_CONTRACT_AGENT m where m.POLICY_CODE = #{policy_code} and m.AGENT_CODE = #{agent_code})
		 ]]></if>
		 <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		 <if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND trim(A.SERVICE_BANK) = #{service_bank} ]]></if>
		 <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		    <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = A.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		 </if>
	</select>
	<!-- 根据保单号查询关联保单信息 -->
	<select id="PA_findRelevancyContractMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_FLAG, A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
		      A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
		      A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.RELATION_POLICY_CODE,
		      A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, A.IS_MUTUAL_INSURED,
		      A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.TRUST_BUSI_FLAG, 
		      A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
		      A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE,A.is_self_insured, A.insert_time,
		      A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.DOUBLE_MAINRISK_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED,
		      A.TAX_EXTENSION_SUM_PREM,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A , APP___PAS__DBUSER.T_CONTRACT_MASTER B WHERE 1 = 1   
		      AND B.POLICY_CODE = trim(#{policy_code})
		      and A.POLICY_CODE = B.RELATION_POLICY_CODE]]>
	</select>
	<!--关联保单查询接口：查询保单是否被其他保单关联  -->
	<select id="findContactMasterByRelationPolicyCode" resultType="java.util.Map" parameterType = "java.util.Map">
		<![CDATA[
			SELECT A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
				A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
				A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
				A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
				A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
				A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
				A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
				A.AGENT_ORG_ID, A.SERVICE_BANK, A.BANK_AGENCY_FLAG,A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID, A.RELATION_POLICY_CODE, A.IS_SELF_INSURED,
				A.GROUP_SALE_TYPE,A.POLICY_REINSURE_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG
			FROM DEV_PAS.T_CONTRACT_MASTER A 
			WHERE 1 = 1  
				AND A.RELATION_POLICY_CODE= #{relation_policy_code} 
				AND A.POLICY_CODE IS NOT NULL
            UNION
            SELECT CM.STATISTIC_CHANNEL,CM.POLICY_PWD, CM.MEDIA_TYPE, CM.APPLY_CODE, CM.INTEREST_MODE, CM.ORGAN_CODE, CM.CHANNEL_TYPE, CM.SALE_AGENT_NAME, 
				CM.INSURED_FAMILY, CM.POLICY_ID, CM.DERIVATION, CM.SUBINPUT_TYPE, 
				CM.BASIC_REMARK, CM.SALE_COM_CODE, CM.INPUT_DATE, CM.POLICY_TYPE, CM.EXPIRY_DATE, CM.PWD_INVALID_FLAG, CM.SUBMIT_CHANNEL, 
				CM.LIABILITY_STATE, CM.POLICY_CODE, CM.SALE_AGENT_CODE, CM.RERINSTATE_DATE, CM.BRANCH_CODE, CM.VALIDATE_DATE, 
				CM.AGENCY_CODE, CM.MONEY_CODE, CM.SERVICE_HANDLER_CODE, CM.APPLY_DATE, CM.INITIAL_VALIDATE_DATE, CM.IS_MUTUAL_INSURED,
				CM.SUBMISSION_DATE, CM.SERVICE_HANDLER, CM.E_SERVICE_FLAG, CM.SERVICE_BANK_BRANCH, CM.LAPSE_DATE, CM.DC_INDI, 
				CM.SALE_TYPE, CM.INPUT_TYPE, CM.END_CAUSE, CM.ISSUE_DATE, CM.LAPSE_CAUSE, CM.DECISION_CODE, CM.MULTI_MAINRISK_FLAG,CM.MEET_POV_STANDARD_FLAG,
				CM.AGENT_ORG_ID, CM.SERVICE_BANK, CM.BANK_AGENCY_FLAG,CM.SUSPEND_DATE, CM.INITIAL_PREM_DATE, CM.SUSPEND_CAUSE, CM.LANG_CODE, CM.FORMER_ID, CM.RELATION_POLICY_CODE, CM.IS_SELF_INSURED,
				CM.GROUP_SALE_TYPE,CM.POLICY_REINSURE_FLAG,CM.IS_CHANNEL_SELF_INSURED,CM.IS_CHANNEL_MUTUAL_INSURED,CM.TAX_EXTENSION_SUM_PREM,CM.TOTAL_POLICY_SEQUENCE_NO,CM.POLICY_SEQUENCE_NO,CM.JOINTLY_INSURED_TYPE,CM.SELF_APPLY_FLAG
				
            FROM DEV_PAS.T_CONTRACT_MASTER CM
            WHERE CM.POLICY_CODE = #{relation_policy_code}
            AND CM.RELATION_POLICY_CODE IS NOT NULL
        ]]>
	</select>
	
	
	
	
	<!--关联保单查询接口：查询保单是否被其他保单关联  -->
	<select id="findContactMasterByRelationPolicyCodeWB" resultType="java.util.Map" parameterType = "java.util.Map">
		<![CDATA[
			SELECT A.MERGE_SIGNATURE_FLAG,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
				A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
				A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
				A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
				A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
				A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
				A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
				A.AGENT_ORG_ID, A.SERVICE_BANK, A.BANK_AGENCY_FLAG,A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID, A.RELATION_POLICY_CODE, A.IS_SELF_INSURED,
				A.GROUP_SALE_TYPE,A.POLICY_REINSURE_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.POLICY_RELATION_TYPE
			FROM DEV_PAS.T_CONTRACT_MASTER A 
			WHERE 1 = 1  
				AND A.RELATION_POLICY_CODE= #{relation_policy_code} 
				AND A.POLICY_CODE IS NOT NULL
         
        ]]>
	</select>
	
	
	
	<!--2740接口查询校验范围内的保单：保单保险期间超[不能直接作差。应按照产品定义判断是否为一年期以上。]过一年的个人人身保险业务（含团体个人单的业务）。
		仅对参与本次变更的保单进行校验，同时失效保单、终止保单均不出现阻断流程。
		（即当保全项目支持失效保单、终止保单变更时，该类保单参与校验，且提示不阻断）  -->
    <!--
     modify by cuiqi_wb 20201106
     RM82376 查询有效和终止的一年期以上的符合的保单，有效的直接参加校验，终止的看附加险是否是849若是参加校验 
     #114788 去掉即将校验保单的限制范围            
    -->
	<select id="PA_findEffectiveContractMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<!-- <![CDATA[
			 select pro.cover_period_type, a.liability_state, cbp.policy_code
			   from APP___PAS__DBUSER.t_contract_master    a,
			        APP___PAS__DBUSER.t_contract_busi_prod cbp, 
			        APP___PAS__DBUSER.t_business_product   pro        
			  where 1 = 1
			    and cbp.policy_code = a.policy_code
			    and pro.business_prd_id = cbp.busi_prd_id
			    and cbp.master_busi_item_id is null
			    and cbp.policy_code = #{policy_code}
			    and pro.cover_period_type = '0'			    
		 ]]>-->
		 <![CDATA[ 
				SELECT CBP.LIABILITY_STATE cpb_state,    
			       CBP.MASTER_BUSI_ITEM_ID,  
			       CBP.BUSI_ITEM_ID,         
			       PRO.PRODUCT_CODE_STD,     
			       PRO.COVER_PERIOD_TYPE,    
			       A.LIABILITY_STATE A_STATE,        
			       CBP.POLICY_CODE,          
			       PRO.PRODUCT_CATEGORY,     
			       PRO.PRODUCT_ABBR_NAME    
			    FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
			         APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
			         APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO
			    WHERE 1 = 1
			        AND CBP.POLICY_CODE = A.POLICY_CODE
			        AND PRO.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
			        AND CBP.MASTER_BUSI_ITEM_ID IS NULL
			        AND CBP.POLICY_CODE = #{policy_code} 
			        AND NOT EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
                    WHERE Z.POLICY_CODE=A.POLICY_CODE
                    AND Z.IS_PAUSE = 1 AND Z.PAUSE_DATE IS NOT NULL)   
		 ]]>
	</select>
	<select id="PA_findContractMasterByPolicyCode1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<!-- <![CDATA[SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, 
			A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.POLICY_ID, 
			A.DERIVATION, A.SUBINPUT_TYPE, A.BASIC_REMARK, A.SALE_COM_CODE, 
			A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, 
			A.VALIDATE_DATE, A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, 
			A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, A.SALE_TYPE, 
			A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, 
			A.LANG_CODE, A.FORMER_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1 AND A.POLICY_CODE 
			= #{policy_code} ]]> -->
		<!-- <include refid="PA_queryContractMasterByPolicyCodeCondition" /> -->
	<![CDATA[ SELECT *  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A   where  A.POLICY_CODE = #{policy_code}  ]]>
	</select>

	<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.GROUP_SALE_TYPE,
			A.POLICY_REINSURE_FLAG ,A.BANKNRT_FALG,A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,
			A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE ROWNUM <=  1000 ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAllContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.WINNING_START_FLAG,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.TRUST_BUSI_FLAG,A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.BANK_AGENCY_FLAG,A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID, A.IS_SELF_INSURED,A.RELATION_POLICY_CODE,
			A.POLICY_FLAG,A.DOUBLE_MAINRISK_FLAG,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG,A.GROUP_SALE_TYPE,A.POLICY_REINSURE_FLAG,A.MEDICAL_INSURANCE_CARD,A.BANKNRT_FALG  ,
			A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE ROWNUM <=  1000   ]]>
		<include refid="PA_contractMasterWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="PA_findContractMasterTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_contractMasterWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="PA_queryContractMasterForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT B.RN AS rowNumber, B.MERGE_SIGNATURE_FLAG,B.POLICY_RELATION_TYPE,B.STATISTIC_CHANNEL,B.POLICY_PWD, B.MEDIA_TYPE, B.APPLY_CODE, B.INTEREST_MODE, B.ORGAN_CODE, B.CHANNEL_TYPE, B.SALE_AGENT_NAME, 
			B.INSURED_FAMILY, B.POLICY_ID, B.DERIVATION, B.SUBINPUT_TYPE, 
			B.BASIC_REMARK, B.SALE_COM_CODE, B.INPUT_DATE, B.POLICY_TYPE, B.EXPIRY_DATE, B.PWD_INVALID_FLAG, B.SUBMIT_CHANNEL, 
			B.LIABILITY_STATE, B.POLICY_CODE, B.SALE_AGENT_CODE, B.RERINSTATE_DATE, B.BRANCH_CODE, B.VALIDATE_DATE, 
			B.AGENCY_CODE, B.MONEY_CODE, B.SERVICE_HANDLER_CODE, B.APPLY_DATE, B.INITIAL_VALIDATE_DATE, B.IS_MUTUAL_INSURED,
			B.SUBMISSION_DATE, B.SERVICE_HANDLER, B.E_SERVICE_FLAG, B.SERVICE_BANK_BRANCH, B.LAPSE_DATE, B.DC_INDI, 
			B.SALE_TYPE, B.INPUT_TYPE, B.END_CAUSE, B.ISSUE_DATE, B.LAPSE_CAUSE, B.DECISION_CODE, B.MULTI_MAINRISK_FLAG,
			B.AGENT_ORG_ID, B.SERVICE_BANK, B.SUSPEND_DATE, B.INITIAL_PREM_DATE, B.SUSPEND_CAUSE, B.LANG_CODE, B.FORMER_ID  ,B.BANKNRT_FALG,B.SPECIAL_ACCOUNT_FLAG,B.IS_CHANNEL_SELF_INSURED,
			B.IS_CHANNEL_MUTUAL_INSURED ,B.NOTIFICATION_RECEIVE_METHOD,B.TAX_EXTENSION_SUM_PREM ,B.TOTAL_POLICY_SEQUENCE_NO,B.POLICY_SEQUENCE_NO,B.JOINTLY_INSURED_TYPE,B.SELF_APPLY_FLAG,B.PER_FIN_PVT_BANK_CODE,B.POLICY_LOCK_FLAG,B.IS_DFLT_ACKNOWLEDGE_DATE FROM (
					SELECT ROWNUM RN,A.MERGE_SIGNATURE_FLAG, A.POLICY_RELATION_TYPE,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID ,A.GROUP_SALE_TYPE,A.POLICY_REINSURE_FLAG  ,A.BANKNRT_FALG,A.SPECIAL_ACCOUNT_FLAG,
			A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED  ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE ROWNUM <= #{LESS_NUM}  ]]>
		<include refid="PA_contractMasterWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询单条 -->
	<select id="PA_findContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.MERGE_SIGNATURE_FLAG,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.IS_MUTUAL_INSURED,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.DOUBLE_MAINRISK_FLAG,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG,
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.BANK_AGENCY_FLAG,A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID, A.RELATION_POLICY_CODE, A.IS_SELF_INSURED, 
			A.POLICY_FLAG,A.GROUP_SALE_TYPE,A.APPLY_TIME,A.IS_ALONE_INSURE,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,a.TRUST_BUSI_FLAG,
			A.BANK_MANAGER_NAME,A.BANK_MANAGER_LICENSENO,A.POLICY_RELATION_TYPE,A.BANKNRT_FALG ,A.SPECIAL_ACCOUNT_FLAG ,A.LAPSE_LOAN_SUSPEND_DATE,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,
			A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE,
			(SELECT TPF.FLAG_NAME FROM APP___PAS__DBUSER.T_POLICY_FLAG TPF WHERE TPF.FLAG_CODE = A.POLICY_FLAG) FLAG_NAME
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_contractMasterWhereCondition" />
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">
          <![CDATA[ AND NOT EXISTS
						 (SELECT 1
						    FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
						   WHERE CBP.POLICY_CODE = A.POLICY_CODE
						     AND CBP.BUSI_PROD_CODE IN 
						         (SELECT CI.CONSTANTS_VALUE
						            FROM DEV_PAS.T_CONSTANTS_INFO CI
						           WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT')) ]]>
       </if>
	</select>

	<!-- 查询单条 -->
	<select id="PA_findContractMasters" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select a.change_id,t.policy_code,t.organ_code from dev_pas.t_cs_policy_change t ,(
select t.accept_code,t.accept_id,t.change_id from dev_pas.t_cs_accept_change t) a
where t.accept_id = a.accept_id   ]]>
		<if test=" policy_id  != null "><![CDATA[ AND  t.change_id = #{policy_id} ]]></if>
	</select>

	<select id="PA_findContractMasterComp" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.MONEY_CODE, A.POLICY_PWD, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.MEDIA_TYPE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.SUBMISSION_DATE, 
			A.INSURED_FAMILY, A.E_SERVICE_FLAG, A.SERVICE_HANDLER, A.POLICY_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, 
			A.DERIVATION, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.BASIC_REMARK, A.AGENT_ORG_ID, A.DECISION_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.VALIDATE_DATE, 
			A.LIABILITY_STATE, A.SUBMIT_CHANNEL, A.POLICY_CODE, A.SERVICE_BANK, A.SALE_AGENT_CODE, A.BRANCH_CODE, 
			A.LANG_CODE, A.FORMER_ID, A.AGENCY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A 
			           LEFT JOIN APP___PAS__DBUSER.T_UDMP_ORG TCO1 ON A.ORGAN_CODE = TCO1.ORGAN_CODE
			           LEFT JOIN (SELECT TA3.AGENT_CODE,TA3.AGENT_NAME SERVICE_AGENT_NAME,TCO3.CHANNEL_NAME SERVICE_ORGAN FROM APP___PAS__DBUSER.T_AGENT TA3,APP___PAS__DBUSER.T_CHANNEL_ORG TCO3
			                    WHERE TA3.SALES_ORGAN_CODE = TCO3.CHANNLE_CODE) D
			           ON A.AGENT_ORG_ID = D.AGENT_CODE
			           LEFT JOIN (SELECT TCA4.AGENT_CODE, TCO4.CHANNEL_NAME AGENT_ORGAN_NAME FROM APP___PAS__DBUSER.T_CONTRACT_AGENT TCA4,APP___PAS__DBUSER.T_CHANNEL_ORG TCO4
			                   WHERE TCA4.AGENT_ORGAN_CODE = TCO4.CHANNLE_CODE) E 
			           ON  A.SERVICE_HANDLER_CODE = E.AGENT_CODE
			      WHERE 1 = 1]]>
		<include refid="PA_queryContractMasterByPolicyIdCondition" />
		<include refid="PA_queryContractMasterByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID ]]>
	</select>

	<select id="findAgentCodeByCustomerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select distinct AGENT_CODE AS SALE_AGENT_CODE 
			  from APP___PAS__DBUSER.T_contract_agent a
			 where a.policy_code in
			       (select policy_code
			          from APP___PAS__DBUSER.T_policy_holder
			         where customer_id = #{customer_Id}
			        union
			        select policy_code
			          from APP___PAS__DBUSER.T_insured_list
			         where customer_id = #{customer_Id})
   ]]>
	</select>
	<!-- E事历-保单提醒功能查询 -->
	<select id="queryContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select  distinct
		       a.policy_code,
		       a.PAY_DUE_DATE,
		       (select max(b.pay_due_date)
		                   from APP___PAS__DBUSER.T_PAY_DUE b
		                  where b.policy_code = a.policy_code) as PAY_DUE_DATE1,
		        (select MAX(APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD.MATURITY_DATE)
                   from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD
                  where APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD.Policy_Code = a.policy_code) as MQDate
  		from 
       		APP___PAS__DBUSER.T_CONTRACT_EXTEND  a
 		where 
 			a.policy_code = #{policy_code}
		]]>
		<if test=" start_date!= null and start_date!='' "><![CDATA[and a.pay_due_date >=#{start_date}]]></if>
		<if test=" end_date !=null and end_date !='' "><![CDATA[ and a.pay_due_date <= #{end_date}]]></if>
	</select>

	<select id="findCustomerPolicyByCustomerId" resultType="java.util.Map"
		parameterType="java.util.Map">
   <![CDATA[
   			SELECT 
			A.POLICY_ID,A.POLICY_CODE,A.ORGAN_CODE,A.VALIDATE_DATE,A.SPECIAL_ACCOUNT_FLAG,
			(SELECT TUO.ORGAN_NAME
               FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
              WHERE TUO.ORGAN_CODE = A.ORGAN_CODE
                AND ROWNUM = 1) ORGAN_NAME,
			(SELECT PRODUCT_CODE_SYS
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD M,
			APP___PAS__DBUSER.T_BUSINESS_PRODUCT N
			WHERE M.POLICY_ID = A.POLICY_ID
			AND M.MASTER_BUSI_ITEM_ID IS NULL
			AND M.BUSI_PRD_ID = N.BUSINESS_PRD_ID
			AND ROWNUM = 1) as riskcode,
			(SELECT N.PRODUCT_NAME_STD
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD M,
			APP___PAS__DBUSER.T_BUSINESS_PRODUCT N
			WHERE M.POLICY_ID = A.POLICY_ID
			AND M.MASTER_BUSI_ITEM_ID IS NULL
			AND M.BUSI_PRD_ID = N.BUSINESS_PRD_ID
			AND ROWNUM = 1) as riskname,
			(SELECT N.PRODUCT_ABBR_NAME
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD M,
            APP___PAS__DBUSER.T_BUSINESS_PRODUCT N
            WHERE M.POLICY_ID = A.POLICY_ID
            AND M.MASTER_BUSI_ITEM_ID IS NULL
            AND M.BUSI_PRD_ID = N.BUSINESS_PRD_ID
            AND ROWNUM = 1) as product_abbr_name,
            (SELECT M.BUSI_ITEM_ID
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD M
            WHERE M.POLICY_ID = A.POLICY_ID
            AND M.MASTER_BUSI_ITEM_ID IS NULL
            AND ROWNUM = 1) as busi_item_id,
			A.LIABILITY_STATE
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
			WHERE A.POLICY_ID IN 
   ]]>
   <foreach item="item" index="index" collection="policy_id"  open="(" separator="," close=")">
         <![CDATA[#{item}]]>
   </foreach>
   </select>

	<select id="findFundTransHisByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select 
				    T.contNo,
				    T.POLICY_ID AS policyId,
				    T.BUSI_ITEM_ID,
				    T.LIST_ID,
				    T.cValiDate,
				    LS.status_name AS contState,
				    T.TRANS_UNITS,
				    T.TRANS_AMOUNT,
				    T.TRANS_PRICE,
				    T.APPLY_TIME,
				    G.PRODUCT_ABBR_NAME AS riskShortName, 
				    F.Post_Code AS zipCode,
				    F.address AS postalAddress, 
				    E.Customer_Name AS appntName,
				    E.customer_gender AS appntSex,
            		H.ORGAN_ADDRESS AS organAddress,
            		H.ORGAN_ZIPCODE 
				FROM
				APP___PAS__DBUSER.T_POLICY_HOLDER D, APP___PAS__DBUSER.T_CUSTOMER E, APP___PAS__DBUSER.T_ADDRESS F, APP___PAS__DBUSER.T_BUSINESS_PRODUCT G, APP___PAS__DBUSER.T_UDMP_ORG H,
				(select 
				    A.policy_code AS contNo,
				    A.POLICY_ID,
				    C.LIST_ID, 
				    K.BUSI_PRD_ID,
				    K.BUSI_ITEM_ID,
				    A.VALIDATE_DATE AS cValiDate,
				    A.LIABILITY_STATE ,
            		A.ORGAN_CODE,
				    NVL(C.TRANS_UNITS,0) AS TRANS_UNITS,
				    NVL(C.TRANS_AMOUNT,0) AS TRANS_AMOUNT,
				    NVL(C.TRANS_PRICE,0) AS  TRANS_PRICE,
            		M.APPLY_TIME 
				from 
				    APP___PAS__DBUSER.T_CONTRACT_MASTER A, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD K
				LEFT JOIN APP___PAS__DBUSER.T_FUND_TRANS C ON C.TRANS_CODE = '22' 
				                       AND C.POLICY_ID = K.POLICY_ID 
				LEFT JOIN APP___PAS__DBUSER.T_FUND_TRANS_APPLY M ON M.APPLY_ID = C.APPLY_ID
				where 
				    	A.POLICY_CODE = #{contNo}
				    AND K.POLICY_CODE = A.POLICY_CODE) T
				    LEFT JOIN APP___PAS__DBUSER.T_LIABILITY_STATUS LS ON LS.STATUS_CODE=T.LIABILITY_STATE
				    
				where
				    	D.POLICY_CODE = T.ContNo
				    AND E.CUSTOMER_ID = D.CUSTOMER_ID 
				    AND F.ADDRESS_ID = D.ADDRESS_ID 
				    AND G.BUSINESS_PRD_ID = T.BUSI_PRD_ID
	                AND H.ORGAN_CODE = T.ORGAN_CODE
 		]]>
	</select>
	<!-- 查询有效的保单 -->
	<select id="queryContractMasterByState" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select ma.policy_code, ho.customer_id
				  from APP___PAS__DBUSER.T_CONTRACT_MASTER ma, APP___PAS__DBUSER.T_POLICY_HOLDER ho
				 where ma.policy_id = ho.policy_id
				   and ma.liability_state != '4']]>
		<if test=" customer_id != null and customer_id!='' "><![CDATA[  and ho.customer_id = #{customer_id}]]></if>
		<if test=" policy_code != null and policy_code!='' "><![CDATA[  and ma.policy_code =#{policy_code}]]></if>
		<![CDATA[  and exists (select 1 from APP___PAS__DBUSER.T_customer cu where cu.customer_id = ho.customer_id )]]>
	</select>
	
	<!--by zhaoyoan_wb 根据 保单号 查询保单信息和投保人信息 -->
	<select id="queryContUniversalAndHolderByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.POLICY_CODE,A.POLICY_ID,A.ORGAN_CODE,A.ISSUE_DATE,B.CUSTOMER_NAME,B.CUSTOMER_GENDER,B.MOBILE_TEL,
			NVL(B.CUSTOMER_ID_CODE,(SELECT CUSTOMER_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER 
				WHERE CUSTOMER_ID=B.CUSTOMER_ID AND CUSTOMER_CERT_TYPE='0')) customer_id_code
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A 
      			LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER C ON A.POLICY_ID=C.POLICY_ID 
				LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER B ON B.CUSTOMER_ID=C.CUSTOMER_ID
			WHERE A.POLICY_CODE=#{policy_code} AND ROWNUM=1
		]]>
	</select>

	<select id="queryLianBaoContUniversal" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT (SELECT FUND_NAME  FROM APP___PAS__DBUSER.T_FUND WHERE FUND_CODE=D.INVEST_ACCOUNT_CODE)INVEST_ACCOUNT_NAME,
                A.INSERT_TIME,
                NVL(A.TOTAL_PREM,0)TOTAL_PREM,
                D.BID_PRICE,
                D.PRICING_DATE,
                DECODE(A.ACCUM_UNITS, 0, 1, A.ACCUM_UNITS)ACCUM_UNITS, 
                F.ACCOUNT_BANK,
                F.ACCOUNT_NAME,
                F.ACCOUNT,
                G.CHANNEL_TYPE
			  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST     A,
			       APP___PAS__DBUSER.T_INVEST_UNIT_PRICE   D,
			       APP___PAS__DBUSER.T_PAYER_ACCOUNT       F,
			       APP___PAS__DBUSER.T_CONTRACT_MASTER     G
			 WHERE A.POLICY_ID = G.POLICY_ID  
			   AND G.LIABILITY_STATE != '4'   
			   AND A.ACCOUNT_CODE = D.INVEST_ACCOUNT_CODE
			   AND F.POLICY_ID = A.POLICY_ID
        AND D.PRICING_DATE=(SELECT MAX(PRICING_DATE)FROM 
        APP___PAS__DBUSER.T_INVEST_UNIT_PRICE WHERE INVEST_ACCOUNT_CODE = D.INVEST_ACCOUNT_CODE)
		AND G.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!--by lixiao_wb 根据 保单号 查询保单信息（投连险账户部分领取信息） -->
	<!-- 续期缴费账号查询 -->
	<select id="PA_findRenewalPaymentByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
	 		SELECT 
	            B.PAY_MODE,
	            B.PAY_NEXT,
	            B.NEXT_ACCOUNT,
	            B.NEXT_ACCOUNT_NAME,
	            B.NEXT_ACCOUNT_BANK,
	            C.PREM_FREQ,
	            B.PAY_LOCATION,
	            B.ACCOUNT,
                B.ACCOUNT_BANK,
                B.ACCOUNT_NAME,
                (SELECT G.BANK_NAME
                   FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT F,
                        APP___PAS__DBUSER.t_bank          G
                  WHERE F.POLICY_ID = B.POLICY_ID
                    AND F.NEXT_ACCOUNT_BANK = G.BANK_CODE
                    AND ROWNUM = 1) AS NEXT_ACCOUNT_BANK_NAME,
                (SELECT G.BANK_NAME
                   FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT F,
                        APP___PAS__DBUSER.t_bank          G
                  WHERE F.POLICY_ID = B.POLICY_ID
                    AND F.ACCOUNT_BANK = G.BANK_CODE
                    AND ROWNUM = 1) AS ACCOUNT_BANK_NAME
	          FROM APP___PAS__DBUSER.t_Contract_Busi_Prod A,APP___PAS__DBUSER.T_PAYER_ACCOUNT B,APP___PAS__DBUSER.T_CONTRACT_PRODUCT C  
	          WHERE 1 = 1 
	                AND A.POLICY_CODE = #{contNo}
	                AND A.POLICY_ID = B.POLICY_ID
	                AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
	                AND A.MASTER_BUSI_ITEM_ID IS NULL
	                and C.is_master_item ='1' 
   		]]>
	</select>

	<!-- 保单基础信息查询 -->
	<!--报文比对修改sql,首期银行名称，续期银行名称，交费对应日  -->
	<select id="PA_findBusinessCentreByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT distinct A.POLICY_CODE,
                (SELECT DISTINCT CE.PAY_DUE_DATE
                   FROM APP___PAS__DBUSER.T_contract_extend CE ,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD BP 
                  WHERE CE.BUSI_ITEM_ID=BP.BUSI_ITEM_ID AND BP.POLICY_CODE=A.POLICY_CODE
                  ) AS PAY_DUE_DATE,
                B.PAY_MODE,
                B.PAY_NEXT,
                A.VALIDATE_DATE,
                (select AC.EXPIRY_DATE from dev_pas.t_contract_busi_prod AC where AC.Policy_Code=a.policy_code and AC.Master_Busi_Item_Id is null) as EXPIRY_DATE,
                (SELECT CBP.RENEW FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP WHERE CBP.POLICY_CODE = A.POLICY_CODE AND CBP.MASTER_BUSI_ITEM_ID IS NULL) RENEW,
                B.ACCOUNT,
                B.ACCOUNT_NAME,
                B.NEXT_ACCOUNT,
                B.NEXT_ACCOUNT_NAME,
                B.ACCOUNT_BANK,
                B.NEXT_ACCOUNT_BANK,
                (SELECT G.BANK_NAME
                   FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT F,
                        APP___PAS__DBUSER.t_bank          G
                  WHERE F.POLICY_ID = A.POLICY_ID
                    AND F.NEXT_ACCOUNT_BANK = G.BANK_CODE
                    AND ROWNUM = 1) AS NEXT_ACCOUNT_BANK_NAME,
                (SELECT G.BANK_NAME
                   FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT F,
                        APP___PAS__DBUSER.t_bank          G
                  WHERE F.POLICY_ID = A.POLICY_ID
                    AND F.ACCOUNT_BANK = G.BANK_CODE
                    AND ROWNUM = 1) AS ACCOUNT_BANK_NAME,
                A.LIABILITY_STATE,
                (select sum(cp.std_prem_af)
                   from dev_pas.t_contract_product cp
                  where cp.policy_code = a.policy_code) std_prem_af
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
       APP___PAS__DBUSER.T_PAYER_ACCOUNT   B
 WHERE 1 = 1
   AND A.POLICY_CODE = #{contNo}
   AND A.POLICY_ID = B.POLICY_ID 
   		]]>
	</select>
<select id="PA_findContractBusiProdByPolicyToRnewTbflag" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
 			select a.busi_prod_code from dev_pas.t_contract_busi_prod a where a.policy_id=#{policy_id}
   		]]>
	</select>
	<!-- 续收查询 -->
	<select id="queryContractMasterInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
 			select a.* from APP___PAS__DBUSER.T_contract_master a where a.validate_date =  #{validate_date, jdbcType=DATE} and a.expiry_date = #{expiry_date, jdbcType=DATE} 
 				and a.policy_type = #{policy_type} and  a.policy_code= #{policy_code}
   		]]>
	</select>
			<!-- 短期意外险被保人信息接口 -->
	<select id="queryInsuredInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	 			SELECT    DISTINCT C.CUSTOMER_ID,
	 			          C.CUSTOMER_NAME,
						  C.CUSTOMER_GENDER,
						  C.CUSTOMER_CERT_TYPE,
						  C.CUSTOMER_CERTI_CODE,
						  CT.TYPE,
						  TBI.RELATION_TO_INSURED_1,
						  (SELECT T.RELATION_NAME  FROM APP___PAS__DBUSER.T_LA_PH_RELA  T WHERE T.RELATION_CODE=TBI.RELATION_TO_INSURED_1)RELATION_NAME
				FROM   
					      APP___PAS__DBUSER.T_INSURED_LIST IL,
						  APP___PAS__DBUSER.T_CUSTOMER C,
						  APP___PAS__DBUSER.T_CERTI_TYPE CT,
						  APP___PAS__DBUSER.T_POLICY_HOLDER PH,
						  APP___PAS__DBUSER.T_CUSTOMER C1,
						  APP___PAS__DBUSER.T_CONTRACT_MASTER CM,
						  APP___PAS__DBUSER.T_CONTRACT_AGENT CA,
						  APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
						  APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP,
						  APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,
						  APP___PAS__DBUSER.T_BENEFIT_INSURED TBI
				WHERE 	  IL.CUSTOMER_ID=C.CUSTOMER_ID
					AND   C.CUSTOMER_CERT_TYPE=CT.CODE
					AND   CM.POLICY_ID=IL.POLICY_ID
					AND   PH.POLICY_ID=CM.POLICY_ID
					AND   PH.CUSTOMER_ID=C1.CUSTOMER_ID
					AND   CM.POLICY_CODE= #{policy_code}
					AND   CA.POLICY_ID=CM.POLICY_ID
					AND   CM.POLICY_ID=CBP.POLICY_ID
      				AND   CBP.BUSI_PRD_ID=BP.BUSINESS_PRD_ID
      				AND TCP.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
      				and TBI.INSURED_ID=IL.LIST_ID
			]]>
	<if test=" holder_id_code != null and holder_id_code !='' "><![CDATA[  AND C1.CUSTOMER_ID_CODE = #{holder_id_code}]]></if>
	<if test=" customer_name != null and customer_name !='' "><![CDATA[  AND C1.CUSTOMER_NAME = #{customer_name}]]></if>
	<if test=" insured_id_code != null and insured_id_code !='' "><![CDATA[  AND C.CUSTOMER_ID_CODE = #{insured_id_code}]]></if>
	<if test=" customer_id != null and customer_id !='' "><![CDATA[
	    AND (C.CUSTOMER_ID =#{customer_id} or  C1.CUSTOMER_ID=#{customer_id})
    ]]></if>
	<if test=" start_date != null and start_date !='' "><![CDATA[  AND CM.ISSUE_DATE >= #{start_date}]]></if>
	<if test="  end_date !=null and end_date !='' "><![CDATA[  AND CM.ISSUE_DATE <= #{end_date}]]></if>
	<if test="  liability_state !=null and liability_state !='' "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state}]]></if>
	<if test="  product_static_code !=null and product_static_code !='' "><![CDATA[  AND BP.PRODUCT_STATIC_CODE = #{product_static_code}]]></if>
	<if test="  agent_code !=null and agent_code !='' "><![CDATA[  AND CA.AGENT_CODE = #{agent_code}]]></if>
	<if test="  pay_mode !=null and pay_mode !='' "><![CDATA[ AND TCP.PREM_FREQ = #{pay_mode}]]></if>
	</select>

	<select id="PA_findContractMasterByPolicyCodeUnionState" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, A.POLICY_REINSURE_FLAG,
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK,
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID, B.STATUS_NAME,A.SELF_APPLY_FLAG,A.LAPSE_LOAN_SUSPEND_DATE,
	      A.MULTI_MAINRISK_FLAG,A.IS_MUTUAL_INSURED,A.MEET_POV_STANDARD_FLAG,A.BANKNRT_FALG,A.SPECIAL_ACCOUNT_FLAG,
	      A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED,A.NOTIFICATION_RECEIVE_METHOD,A.IDCARD_OCR_FLAG,
	      A.TAX_EXTENSION_SUM_PREM,A.TRUST_BUSI_FLAG,A.POLICY_RELATION_TYPE,A.BANK_MANAGER_LICENSENO,A.BANK_MANAGER_NAME,
	      A.REINSURED_TIMES,A.CALL_TIME_LIST,A.MEDICAL_INSURANCE_CARD,A.IS_ALONE_INSURE,A.GROUP_SALE_TYPE,
	      A.DOUBLE_MAINRISK_FLAG,A.POLICY_FLAG,A.BANKCARD_OCR_FLAG,A.EMER_CON_RELATION_TO_PH,A.EMERGENCY_CONTACTS_MOBILE,
	      A.EMERGENCY_CONTACTS_NAME,A.POLICY_MANAGE_ORGAN,A.IS_SELF_INSURED,A.BANK_AGENCY_FLAG,A.SPECIAL_CUS_FLAG
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A LEFT JOIN APP___PAS__DBUSER.T_LIABILITY_STATUS B ON A.LIABILITY_STATE = B.STATUS_CODE WHERE 1 = 1 ]]>
		<include refid="PA_queryContractMasterByPolicyCodeCondition" />
	</select>

	<!--by zhaoyoan_wb 根据 保单号 查询 满期给付通知书 保单信息 -->
	<select id="queryWebLPEdorGetNoticePolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,
			A.DERIVATION,
			C.AGENT_NAME,
			C.AGENT_CODE,
			C.AGENT_ORGAN_CODE,
			C.SALES_ORGAN_CODE 
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT E ON A.POLICY_ID = E.POLICY_ID AND E.IS_CURRENT_AGENT=1
			LEFT JOIN APP___PAS__DBUSER.T_AGENT C ON C.AGENT_CODE = E.AGENT_CODE
			WHERE A.POLICY_CODE = #{policy_code}
			AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_DOCUMENT D WHERE A.POLICY_ID=D.POLICY_ID 
				AND (D.TEMPLATE_CODE='PAS_00005' OR D.TEMPLATE_CODE='PAS_00004'))
   		]]>
		<!--  AND D.STATUS='2'	老核心条件的是状态为0的，对应新核心是2 -->
	</select>
	<!--团体个人单和个人保单特约内容查询老核心查询的主险信息-->
	<select id="PA_QueryPaContent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select       
			 tcbp.POLICY_ID,
             tcm.POLICY_CODE,
             tcpm.GROUP_CODE,
             tcpm.GROUP_NAME,
             tpc.CONDITION_DESC,
             tcm.BUSI_PROD_CODE,
             tbp.PRODUCT_NAME_SYS
        from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD tcm
        left join APP___PAS__DBUSER.T_CONTRACT_PRDGROUP_MAIN tcpm
          on tcm.policy_id = tcpm.policy_id
        left join APP___PAS__DBUSER.T_POLICY_CONDITION  tpc
          on tcm.policy_id =tpc.policy_id
        left join APP___PAS__DBUSER.T_CONTRACT_MASTER tcbp
          on tcm.policy_id=tcbp.policy_id
        left join APP___PAS__DBUSER.T_BUSINESS_PRODUCT tbp
           on tcm.BUSI_PRD_ID = tbp.BUSINESS_PRD_ID
        where tcm.policy_code=#{policy_code} and tcm.MASTER_BUSI_ITEM_ID is null
   		]]>
	</select>
	
	 <!-- 查询客户相关保单生效日期 add by panmd -->
	<select id="findPolicyValidateTime" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
			select *
	  		from (select cm.*, row_number() over(order by validate_date) rn
	          from APP___PAS__DBUSER.T_contract_master cm
	         where cm.policy_id in
	               (select tl.policy_id
	                  from APP___PAS__DBUSER.T_INSURED_LIST tl
	                 where 1 = 1
	                    
                       ]]>
                <if test="customer_id != null and customer_id != ''">
                 <![CDATA[ and  tl.customer_id = #{customer_id} ]]>
                </if>   
          <![CDATA[
	                union
	                select ph.policy_id
	                  from APP___PAS__DBUSER.T_POLICY_HOLDER ph
	                 where 1 = 1
                      ]]>
            <if test="customer_id != null and customer_id != ''">
                 <![CDATA[ and ph.customer_id = #{customer_id} ]]>
            </if>   
                       <![CDATA[
                       )order by cm.validate_date desc) r
			 where r.rn <= 1
			 ]]>
	</select>
	
	 <!-- 续收查询 -->
	<select id="queryRenewalMessage" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				select tce.Pay_Due_Date,
			       tpa.FEE_AMOUNT,
			       tcm.policy_code,
			       tc.customer_name,
			       tcp.std_prem_af,
			       tbp.Product_Name_Sys,
			       tpa.PAY_MODE,
			       tcm.liability_state,
			       tcm.VALIDATE_DATE,
			       tcm.LAPSE_DATE,
			       tcm.SUSPEND_DATE,
			       tcm.EXPIRY_DATE
			  from APP___PAS__DBUSER.T_CONTRACT_AGENT tca left join APP___PAS__DBUSER.T_CONTRACT_MASTER tcm on tca.policy_id=tcm.policy_id
			  left join APP___PAS__DBUSER.T_CONTRACT_EXTEND tce
			    on tcm.policy_id = tce.policy_id
			  left join APP___PAS__DBUSER.T_POLICY_HOLDER tph
			    on tcm.policy_id = tph.policy_id
			  left join APP___PAS__DBUSER.T_CUSTOMER tc
			    on tph.customer_id = tc.customer_id
			  left join APP___PAS__DBUSER.T_CONTRACT_PRODUCT tcp
			    on tcm.policy_id = tcp.policy_id
			  left join APP___PAS__DBUSER.T_BUSINESS_PRODUCT tbp
			    on tcp.PRODUCT_ID = tbp.Business_Prd_Id
			  left join APP___PAS__DBUSER.T_PREM_ARAP tpa
			    on tcm.apply_code = tpa.apply_code
			 where tca.agent_code=#{agent_code} ]]>
			 <if test=" policy_code != null and policy_code !='' "><![CDATA[and tcm.policy_code = #{policy_code}]]></if>
			   <![CDATA[and tcm.validate_date>=#{startPayDate}
			   and tcm.validate_date<=#{endPayDate}
			   and tpa.FEE_STATUS=#{fee_status} and tpa.FEE_TYPE='G003010000']]>
			 <if test=" name != null and name !='' "><![CDATA[and tc.customer_name = #{name}]]></if>
	</select>
	
	
	
	<!--by zhaoyoan_wb 根据 保单号 查询保单信息（万能险账户信息） -->
	<select id="queryContUniversal" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			select a.LIABILITY_STATE,a.policy_id,a.policy_code,d.customer_id,b.BUSI_PRD_ID,b.BUSI_PROD_CODE,
			(select e.account_code from APP___PAS__DBUSER.T_CONTRACT_INVEST e left join APP___PAS__DBUSER.T_POLICY_ACCOUNT f 
				on e.policy_id=f.policy_id and e.BUSI_ITEM_ID=f.busi_item_id and e.ITEM_ID=f.ITEM_ID
				where e.policy_id=a.policy_id and rownum=1) as account_code,
			(select PRODUCT_NAME_SYS from APP___PAS__DBUSER.T_BUSINESS_PRODUCT 
				where b.BUSI_PRD_ID=BUSINESS_PRD_ID and rownum=1) as product_name_sys,
			a.Validate_Date,
			(select sum(nvl(TOTAL_PREM_AF,0)) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT 
				where PRODUCT_ID=b.BUSI_PRD_ID) as total_prem_af,
			a.BRANCH_CODE,
			a.ORGAN_CODE,
			(select INTEREST_CAPITAL from APP___PAS__DBUSER.T_POLICY_ACCOUNT where policy_id=a.policy_id 
				and busi_item_id=b.busi_item_id and rownum=1) as interest_capital,
			(select CUSTOMER_NAME from APP___PAS__DBUSER.T_CUSTOMER where d.customer_id=customer_id) as customer_name,
			(select customer_gender from APP___PAS__DBUSER.T_CUSTOMER where d.customer_id=customer_id) as customer_gender,
			(select MOBILE_TEL from APP___PAS__DBUSER.T_CUSTOMER where d.customer_id=customer_id) as mobile_tel, 
			c.ACCOUNT_BANK,
			c.ACCOUNT_ID,
			c.ACCOUNT_NAME,
			(select CUSTOMER_ID_CODE from APP___PAS__DBUSER.T_CUSTOMER where d.customer_id=customer_id) as customer_id_code
			from APP___PAS__DBUSER.T_CONTRACT_MASTER a 
				left join APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD b on a.policy_id=b.policy_id 
				left join APP___PAS__DBUSER.T_PAYER_ACCOUNT c on a.policy_id=c.policy_id 
				left join APP___PAS__DBUSER.T_POLICY_HOLDER d on a.policy_id=d.policy_id 
			where 1 = 1 
			and b.master_Busi_item_id is null 
			and a.policy_code=#{policy_code}
		]]>
	</select>

    <!-- 通过机构号查询机构名称 -->
	<select id="findOragnNameByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
			     SELECT * FROM APP___PAS__DBUSER.T_UDMP_ORG WHERE 1=1  AND ORGAN_CODE=#{organ_code} AND ROWNUM=1
			 ]]>
	</select>

	
	<!-- 通过保单号查询保单列表     Add by yangbo  2016-07-26  Fix TC 缺陷ID-6791 -->
	<select id="queryContractMasterList" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
	     SELECT a.policy_code,b.product_code, 
	     		A.AGENT_ORG_ID,
        		(SELECT TUO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = A.AGENT_ORG_ID )ORGAN_NAME,
				c.PRODUCT_ABBR_NAME , 
				g.customer_name as appnt_name, 
				h.customer_name as insured_name, 
				e.customer_id, 
				b.coverage_year, 
				sum(b.std_prem_af) as prem, 
				a.liability_state, 
				b.coverage_period
				FROM APP___PAS__DBUSER.T_Contract_Master a ,APP___PAS__DBUSER.T_Contract_Product b,APP___PAS__DBUSER.T_POLICY_HOLDER d,APP___PAS__DBUSER.T_INSURED_LIST e,APP___PAS__DBUSER.T_BUSINESS_PRODUCT c,APP___PAS__DBUSER.T_product_life f,APP___PAS__DBUSER.T_Customer g,APP___PAS__DBUSER.T_Customer h 
				WHERE 1=1 
				AND a.policy_code = b.policy_code 
				AND a.policy_id = b.policy_id 
				AND a.apply_code = b.apply_code 
				AND a.policy_code = #{policy_code} 
				AND d.policy_id = a.policy_id AND a.policy_code = d.policy_code 
				AND e.policy_id = a.policy_id AND e.policy_code = a.policy_code 
				AND f.product_id = b.product_id AND f.business_prd_id = c.business_prd_id 
				AND d.customer_id = g.customer_id AND e.customer_id = h.customer_id
				AND exists (select 1 from APP___PAS__DBUSER.t_Contract_Busi_Prod cbp  where cbp.busi_item_id = b.busi_item_id and cbp.master_busi_item_id is null) 
				GROUP BY a.policy_code,b.product_code,c.PRODUCT_ABBR_NAME,g.customer_name,h.customer_name,d.customer_id,e.customer_id,b.coverage_year,a.liability_state,b.coverage_period
				,A.AGENT_ORG_ID 
			 ]]>
	</select>

	
	<select id="PA_FindContractMastersByMsg" resultType="java.util.Map" parameterType="java.util.Map">
	 		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
				      A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
				      A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
				      A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
				      A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
				      A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
				      A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
				      A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID ,A.GROUP_SALE_TYPE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A where 1=1 ]]> 
			 <if test="organ_code != null and organ_code != ''">
			 	<![CDATA[  and A.organ_code = #{organ_code}  ]]>
			 </if>
			 <![CDATA[   and A.Channel_Type in (${survey_channel_str})
				    and A.Liability_State = '1'
				    and A.Validate_Date >= #{validate_date_start}
				    and A.Validate_Date <= #{validate_date_end}  ]]>
	</select>
	
	
	<select id="PA_findCustomerIdListByMsg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select D.customer_id customer_id, count(1) insert_times from APP___PAS__DBUSER.T_insured_list D where D.Customer_Id in (
					SELECT distinct C.Customer_Id
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A, APP___PAS__DBUSER.T_insured_list B,APP___PAS__DBUSER.t_Customer C
					 where 1 = 1 ]]>
				<if test="organ_code != null and organ_code != ''">
			 		<![CDATA[  and A.organ_code = #{organ_code}  ]]>
			 	</if>
			 	<if test="survey_channel_str != null and survey_channel_str != ''">
			 		<![CDATA[   and A.Channel_Type in (${survey_channel_str})]]>
			 	</if>
				<if test="validate_date_start != null and validate_date_start != ''">
					 <![CDATA[   and A.Validate_Date >= #{validate_date_start} ]]>
				</if>	   
				<if test="validate_date_end != null and validate_date_end != ''">
					 <![CDATA[   and A.Validate_Date <= #{validate_date_end}  ]]>
				</if>	
					 <![CDATA[ 
					  		and A.Liability_State = '1'
					  		and b.customer_id = c.customer_id
					 	  and A.Policy_Code = B.Policy_Code  ) 
					   group by D.Customer_Id having count(1) > #{insure_times} ]]>
	</select>
	
	<!-- 根据前置调查计划抽取保单数据 -->
	<select id="PA_queryBFSurveyMsg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.POLICY_CODE,
				       A.ORGAN_CODE,
				       A.CUSTOMER_NAME,
				       A.CUSTOMER_ID,
				       A.CUSTOMER_CERTI_CODE,
				       COUNT(DISTINCT IL1.POLICY_CODE) INSURE_TIMES
				  FROM (SELECT CM.POLICY_CODE,
				               CM.ORGAN_CODE,
				               IL.CUSTOMER_ID,
				               TC.CUSTOMER_NAME,
				               TC.CUSTOMER_CERTI_CODE
				          FROM DEV_PAS.T_CONTRACT_MASTER CM,
				               DEV_PAS.T_INSURED_LIST    IL,]]>
				        <if test=" risk_score != null and risk_score != ''  "><![CDATA[APP___PAS__DBUSER.T_CONTRACT_AGENT E,]]></if>
				        <if test=" risk_score != null and risk_score != ''  "><![CDATA[APP___PAS__DBUSER.t_Contract_Busi_Prod D, ]]></if>
				          <![CDATA[DEV_PAS.T_CUSTOMER        TC
				          WHERE CM.LIABILITY_STATE = '1' ]]>
				        <if test=" risk_score != null and risk_score != ''  "><![CDATA[and D.Policy_Code = CM.Policy_Code ]]></if>
          				<if test=" start_date != null and start_date != ''  "><![CDATA[ and  CM.VALIDATE_DATE >= #{start_date} ]]></if>
          				<if test=" end_date != null and end_date != ''  "><![CDATA[ and  CM.VALIDATE_DATE <= #{end_date} ]]></if>
  		  				<if test=" is_claim != null and is_claim != ''  "><![CDATA[ and C.CASE_STATUS != '10'and C.CASE_STATUS != '20'and C.CASE_STATUS != '21' ]]></if>
  		  				<if test=" certi_code != null and certi_code != ''  "><![CDATA[ and TC.CUSTOMER_ID_CODE = #{certi_code} ]]></if>
  		  				<if test=" risk_score != null and risk_score != ''  "><![CDATA[ and D.RISK_SCORE > #{risk_score} ]]></if>
  		  				<if test=" agent_code != null and agent_code != ''  "><![CDATA[ and E.AGENT_CODE = #{agent_code} ]]></if>
  		  				<if test=" organ_code != null and organ_code != ''  "><![CDATA[ and CM.ORGAN_CODE like '${organ_code}%' ]]></if>
				         <if test="organ_code != null and organ_code != ''">
				         	<![CDATA[ and CM.organ_code = #{organ_code} ]]>
				         </if>
				         <if test="survey_channel_str != null and survey_channel_str != ''">
			 				<![CDATA[   and CM.Channel_Type in (${survey_channel_str})]]>
			 			</if>
						<if test="validate_date_start != null and validate_date_start != ''">
							 <![CDATA[   and CM.Validate_Date >= #{validate_date_start} ]]>
						</if>	   
						<if test="validate_date_end != null and validate_date_end != ''">
							 <![CDATA[   and CM.Validate_Date <= #{validate_date_end}  ]]>
						</if>
				   <![CDATA[ AND CM.POLICY_CODE = IL.POLICY_CODE
				             AND IL.CUSTOMER_ID = TC.CUSTOMER_ID) A,DEV_PAS.T_INSURED_LIST IL1
					 WHERE A.CUSTOMER_ID = IL1.CUSTOMER_ID
					 GROUP BY A.POLICY_CODE,
					          A.ORGAN_CODE,
					          A.CUSTOMER_ID,
					          A.CUSTOMER_NAME,
					          A.CUSTOMER_CERTI_CODE
					HAVING COUNT(DISTINCT IL1.POLICY_CODE) > #{insure_times}) WHERE ROWNUM < 1999
		 ]]>
	</select>
	
	<select id="PA_findContractMasterGtDate"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select *
					  from APP___PAS__DBUSER.T_insured_list ins, APP___PAS__DBUSER.T_CONTRACT_MASTER mas
					 where rownum < 1000
					   and ins.policy_id = mas.policy_id ]]>
			<if test="customer_id != null and customer_id != ''">
				 <![CDATA[   and ins.customer_id = #{customer_id} ]]>
			</if>		   
			<if test="liability_state != null and liability_state != ''">
				<![CDATA[ and mas.liability_state = #{liability_state} ]]>
			</if>		   
			<if test="apply_date != null and apply_date != ''">
				 <![CDATA[  and mas.apply_date > #{apply_date} ]]>
			</if>
	</select>
	
	<select id="PA_findCountContractMasterOfCustomerId" resultType="java.util.Map" parameterType="java.util.Map" >
		 <![CDATA[  SELECT *
						FROM APP___PAS__DBUSER.T_insured_list ins ,APP___PAS__DBUSER.T_CONTRACT_MASTER mas
				where ins.policy_code = mas.policy_code
				and ins.customer_id = #{customer_id} ]]>
	</select>
	
	<select id="PA_queryContractMasterBOList" resultType="java.util.Map" parameterType="java.util.Map" >
		 <![CDATA[ SELECT  T.POLICY_ID,
           T.POLICY_CODE,
           T.VALIDATE_DATE,
           T.LIABILITY_STATE,
           T.CHANNEL_TYPE,
           T.ORGAN_CODE
       FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T
       WHERE T.LIABILITY_STATE <> #{liability_state}
         AND T.VALIDATE_DATE >= #{input_date}
         AND T.VALIDATE_DATE <= #{apply_date}
         AND TO_NUMBER(TO_CHAR(T.VALIDATE_DATE, 'yyyy')) <
             TO_NUMBER(TO_CHAR(SYSDATE, 'yyyy'))           
             ]]>
  <if test="organ_code != null and organ_code != ''"><![CDATA[  AND T.ORGAN_CODE > #{organ_code} ]]></if>
	</select>
	
	<!--电话中心保单信息查询 -->
	<select id="PA_telephoneCenterQueryContractMasterInfo" resultType="java.util.Map" parameterType="java.util.Map" >
		 <![CDATA[  
				select TCM.Policy_Id as Policy_Id,
				       TCM.POLICY_CODE as Contno,
				       case
				         when (select capital_balance
				                 from APP___PAS__DBUSER.t_policy_account
				                where policy_id = TCM.POLICY_ID
				                  and account_type = '4'
				                  and rownum = '1') > 0 then
				          1
				         else
				          0
				       end as LoanStateCode,
				       TCM.ORGAN_CODE AS Managecom,
				       (SELECT t2.organ_name
				          FROM APP___PAS__DBUSER.T_UDMP_ORG t2
				         where t2.organ_code = TCM.ORGAN_CODE) as ManagecomName,
				       (select t3.Group_Name
				          from APP___PAS__DBUSER.T_CONTRACT_PRDGROUP_MAIN t3
				         where t3.apply_code = TCM.Apply_Code) as ContPlanName,
						(select t3.group_code
				          from APP___PAS__DBUSER.T_CONTRACT_PRDGROUP_MAIN t3
				         where t3.apply_code = TCM.Apply_Code) as ContPlanCode,
				       TCM.LIABILITY_STATE as ContState,
				       TCM.EXPIRY_DATE as EndDate,
				       (select agent_code
                   from (select a.agent_code
                           from APP___PAS__DBUSER.T_contract_agent a
                          where  a.policy_code = (select t1.policy_code
                          from APP___PAS__DBUSER.T_CONTRACT_MASTER t1  where  
                t1.policy_code =#{policy_code}
 ) order by a.AGENT_START_DATE )where  rownum=1) as Sale_Agent_Code,
				       (select agent_code
                   from (select a.agent_code
                           from APP___PAS__DBUSER.T_contract_agent a
                          where a.AGENT_END_DATE is null AND  a.policy_code = (select t1.policy_code
                          from APP___PAS__DBUSER.T_CONTRACT_MASTER t1  where rownum=1  
          ]]>          
        <if test="policy_code != null and policy_code != ''">
				 <![CDATA[   AND t1.policy_code = #{policy_code} ]]>
		</if>
		<if test="apply_code != null and apply_code != ''">
				 <![CDATA[   AND t1.apply_code = #{apply_code} ]]>
		</if>
         <![CDATA[                    	
                         ) order by a.list_id desc)
                  where rownum = 1)agent_code
				  from APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
				 WHERE rownum <1000
		 ]]>
		 <if test="policy_code != null and policy_code != ''">
				 <![CDATA[   AND TCM.POLICY_CODE = #{policy_code} ]]>
		</if>
		
		 <if test="apply_code != null and apply_code != ''">
				 <![CDATA[   AND TCM.APPLY_CODE = #{apply_code} ]]>
		</if>
	</select>
	
	<!-- 保单号快查 -->
	<select id="PA_findContractMasterPolicyCodeExist" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.POLICY_CODE=#{policy_code} ]]>
	</select>
	
		
	<!-- 查询id -->
	<select id="PA_findContractMasterByPolicyCodeInOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT tcm.POLICY_ID,
           				tcm.POLICY_CODE,
           				tcm.VALIDATE_DATE,
           				tcm.LIABILITY_STATE,
           				tcm.CHANNEL_TYPE,
           				tcm.ORGAN_CODE
  					from APP___PAS__DBUSER.t_contract_master tcm
 					where tcm.policy_code = #{policy_code}
   						and tcm.organ_code in (select t.organ_code
                            from APP___PAS__DBUSER.t_udmp_org_rel t
                           start with t.organ_code = #{organ_code}
                          	connect by prior t.organ_code = t.uporgan_code
                           	GROUP BY organ_code)   ]]>
	</select>
	<!-- 整单退保查询 -->
	<select id="queryContractMasterCT" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select CM.Policy_Code,
       
       (select AC.Accept_Status
          from dev_pas.T_POLICY_REISSUE PR, dev_pas.t_cs_accept_change AC
         where PR.Accept_Code = AC.Accept_Code
           AND PR.policy_id = CM.Policy_Id and AC.ACCEPT_STATUS='18'and rownum=1 ) as ACCEPT_STATUS,
       (select t.interest_capital from dev_pas.t_policy_account t
                  where t.policy_account_status = '1' and t.account_type = '4' 
                  and t.policy_id = CM.policy_id) INTEREST_CAPITAL,
    (select count(1)
   from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_accept_change ac
  where pc.policy_code =CM.Policy_Code
    and pc.accept_id = ac.accept_id
    and ac.service_code = 'PL') as LOSECOUNTS ,
     (select count(1)
   from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_accept_change ac
  where pc.policy_code =CM.Policy_Code
    and pc.accept_id = ac.accept_id
    and ac.service_code = 'PF')AS UNLOSECOUNTS

           
  from dev_pas.t_contract_master CM

 where CM.policy_code =#{policy_code}
		]]>
	</select>
	
	<!-- 根据addressid查询crs采集地址信息 -->
	<select id="queryAddressByAddressID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select * from dev_pas.t_address a where a.address_id = #{address_id}]]>
	</select>
	
	<select id="findCsContractInvestId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT * FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1=1
		]]>
		<include refid="PA_contractMasterWhereCondition" />
		
	</select>
	<!--by zhaoyoan_wb 根据 保单号 查询保单信息（投连退保查询接口）  -->
	<select id="queryContInfoForInvestTB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_ID,A.POLICY_CODE,A.VALIDATE_DATE,A.ISSUE_DATE,A.LIABILITY_STATE,
			A.BANK_AGENCY_FLAG,A.ORGAN_CODE,A.CHANNEL_TYPE,A.MEDIA_TYPE,
			(SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER WHERE POLICY_ID=A.POLICY_ID) AS holder_id,
			(SELECT SUM(NVL(FEE_AMOUNT,0)) FROM APP___PAS__DBUSER.T_PREM_ARAP WHERE POLICY_CODE=A.POLICY_CODE) AS totalfee
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
			WHERE A.POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	<!-- 查询终止保单 -->
	<select id="findEndContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.MERGE_SIGNATURE_FLAG,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			      A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			      A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			      A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			      A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			      A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			      A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			      A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID, A.POLICY_FLAG,A.GROUP_SALE_TYPE
			      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A where 1=1 ]]> 
		<if test="policy_code != null and policy_code != ''"><![CDATA[  AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" end_policy_list != null and end_policy_list.size()!=0">
			<![CDATA[ AND A.END_CAUSE IN ]]>
			<foreach collection ="end_policy_list" item="end_policy_list" index="index" open="(" close=")" separator=",">#{end_policy_list}</foreach>
		</if>
	</select>
	<!-- 查询保单类型 -->
	<select id="findContractMasterTypeByPolicyType" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[
	select A.POLICY_TYPE, B.TYPE_NAME
  from dev_pas.t_contract_master A, dev_pas.t_channel_type B
 WHERE A.POLICY_TYPE = B.INDIVIDUAL_GROUP
 and A.POLICY_CODE=#{policy_code} ]]>
	</select>
	<!-- 修改险种状态 -->
	<update id="updateContractMasterLiabilityState" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
			 EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
		    END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	
	<!-- 按map查询操作 -->
	<select id="findMapBankBranch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.bank_branch_code, a.bank_branch_name
   					from dev_pas.T_BANK_BRANCH a
  				where a.bank_branch_code = #{service_bank_branch}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	<!-- 按map查询操作 -->
	<select id="findMapBankBranch1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.bank_branch_code, a.bank_branch_name
   					from dev_pas.T_BANK_AGENCY_BRANCH a
  				where a.bank_branch_code = #{service_bank_branch}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	
	<select id="policyDetailCountQuery" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  SELECT COUNT(C.POLICY_ID)
			  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A 
			  INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE B ON A.ACCEPT_ID = B.ACCEPT_ID
			  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER C ON B.POLICY_ID = C.POLICY_ID
			  INNER JOIN APP___PAS__DBUSER.T_SALES_CHANNEL D ON C.CHANNEL_TYPE = D.SALES_CHANNEL_CODE
			 WHERE A.SERVICE_CODE = 'CT'
			   AND A.ACCEPT_STATUS = '18'
			   AND C.CHANNEL_TYPE IN ('03', '08')
			   AND C.SUBMIT_CHANNEL = '1'
			   AND C.SERVICE_BANK = '02'
			   AND A.VALIDATE_TIME  = #{end_date} ]]>
	</select>
	<!-- 判断该保单是否是建行代理 -->
	<select id="findCCBAgent" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			 SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A 
			 WHERE A.SUBMIT_CHANNEL = '1' AND A.SERVICE_BANK = '02'
		]]>	
		<if test=" policy_code != null and policy_code != ''">AND A.POLICY_CODE = #{policy_code}</if>
	</select>
	
	<!-- 判断该保单是否某代理 -->
	<select id="findAgent" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			 SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A 
			 where	1=1	 
		]]>	
			<include refid="PA_contractMasterWhereCondition" />
	</select>
	
	<!--判断该保单是不是上海的保单 -->
	<select id="findPolicyIsSh" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT M.POLICY_ID,M.AGENT_ORG_ID FROM DEV_PAS.T_UDMP_ORG ORG,
			DEV_PAS.T_CONTRACT_MASTER M
			WHERE ORG.ORGAN_CODE LIKE '8622%' AND M.AGENT_ORG_ID=ORG.ORGAN_CODE ]]>
		<if test=" policy_code != null and policy_code != ''">AND M.POLICY_CODE = #{policy_code}</if>
	</select>
	<!--根据policyId判断该保单是不是上海的保单  -->
	<select id="findPolicyIsShBypolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT M.POLICY_ID,M.AGENT_ORG_ID FROM DEV_PAS.T_UDMP_ORG ORG,
			DEV_PAS.T_CONTRACT_MASTER M
			WHERE ORG.ORGAN_CODE LIKE '8622%' AND M.AGENT_ORG_ID=ORG.ORGAN_CODE ]]>
		<if test=" policy_code != null and policy_code != ''">AND M.POLICY_ID = #{policy_id}</if>
	</select>
	<!--判断该保单是不是无锡的保单 -->
		<select id="findPolicyIsWx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID,A.AGENT_ORG_ID FROM DEV_PAS.T_UDMP_ORG ORG,
			DEV_PAS.T_CONTRACT_MASTER A
			WHERE ORG.ORGAN_CODE LIKE '8651%' AND A.AGENT_ORG_ID=ORG.ORGAN_CODE]]>
		<if test=" policy_code != null and policy_code != ''">AND A.POLICY_CODE = #{policy_code}</if>
	</select>
	
	<!-- 银保通出单情况查询交易 -->
	<select id="PA_queryBankAllContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   SELECT A.MERGE_SIGNATURE_FLAG,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.BANK_AGENCY_FLAG,A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,  
			A.POLICY_FLAG,A.GROUP_SALE_TYPE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE ROWNUM <=  1000   ]]>
		<include refid="PA_contractMasterWhereCondition" />
		<if test=" start_date != null and start_date != ''  "><![CDATA[ AND A.submission_date >= #{start_date} ]]></if>
		<if test=" end_date != null and end_date != ''  "><![CDATA[ AND A.submission_date <= #{end_date} ]]></if>
	</select>
	
	<select id="PA_queryWaiverPolicyForHolder" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,
						TCM.ORGAN_CODE,
			            TCM.POLICY_CODE,
			            TCM.VALIDATE_DATE,
			            TCM.LIABILITY_STATE,
			            TCM.APPLY_CODE,
			            TCM.DECISION_CODE,
			            TCM.CHANNEL_TYPE,
			            TCM.MULTI_MAINRISK_FLAG,
			            A.CUSTOMER_ID,
			            A.ANNUAL_INCOME_CEIL,
			            TCM.SUBMIT_CHANNEL,
			            B.BUSI_PROD_CODE,
			            B.BUSI_ITEM_ID,
			            A.EXCEPTION_HEALTH_FLAG
			       FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			            APP___PAS__DBUSER.T_POLICY_HOLDER      A,
			            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
			      WHERE TCM.POLICY_ID = A.POLICY_ID
			        AND TCM.POLICY_ID = B.POLICY_ID
			        AND B.WAIVER = 1 
			         ]]>
		<if test=" customer_Id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_Id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND B.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code  != null "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_status!=null and apply_status!='' and apply_status =='1'.toString()"><![CDATA[ AND TCM.LIABILITY_STATE = '3' ]]></if>
		<if test=" apply_status!=null and apply_status!='' and apply_status =='0'.toString()"><![CDATA[ AND TCM.LIABILITY_STATE != '3' ]]></if>
		<if test="Product_List != null and Product_List.size()!=0"><![CDATA[ AND B.BUSI_PROD_CODE  in]]>
			<foreach collection="Product_List" item="Product_List"
				index="index" open="(" close=")" separator=",">
				#{Product_List}
			</foreach>
		</if>
		<![CDATA[ ORDER BY TCM.VALIDATE_DATE DESC  ]]>
	</select>
	
	
<!-- 查询保单销售渠道 add 2018-2-6 --> <!-- 低级sql错误，去掉查询条件中的小于号  修改被 谭于航  2018-12-12 193缺陷 -->
	<select id="PA_findSalesChannelName" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
			SELECT SCT.SALES_CHANNEL_NAME as channel_type 
			FROM
	        APP___PAS__DBUSER.T_CONTRACT_MASTER CM
	        JOIN APP___PAS__DBUSER.T_SALES_CHANNEL SCT
	        ON CM.CHANNEL_TYPE = SCT.SALES_CHANNEL_CODE
	        WHERE 1 = 1
  		]]>
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND CM.policy_id = #{policy_id} ]]></if>
		<![CDATA[ AND  ROWNUM = 1 ]]>
	</select>
	<!-- 查询是否补发保单 add 2018-2-6 -->
	<select id="PA_findIsLRPolicy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT to_char(COUNT(1)) as channel_type FROM APP___PAS__DBUSER.T_POLICY_REISSUE PR 
	        JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC
	        ON CAC.ACCEPT_STATUS = '18'  AND  CAC.CHANGE_ID = PR.CHANGE_ID
	        WHERE PR.POLICY_ID = #{policy_id}
	        AND ROWNUM = 1 
  		]]>
	</select>
	
	<select id="PA_queryReviewTimeAndPWByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT D.POLICY_CODE,D.POLICY_PWD, D.REVIEW_TIME
    FROM (SELECT A.POLICY_CODE,A.POLICY_PWD, C.REVIEW_TIME
            FROM APP___PAS__DBUSER.T_CONTRACT_MASTER  A,
                 APP___PAS__DBUSER.T_CS_POLICY_CHANGE B,
                 APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C
           WHERE A.POLICY_CODE = B.POLICY_CODE
             AND B.ACCEPT_ID = C.ACCEPT_ID
             AND A.POLICY_CODE = #{policy_code}
           ORDER BY A.INSERT_TIME DESC) D
   WHERE ROWNUM = 1
  		]]>
	</select>
	
	<select id="PA_quereyPolicyLoseByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			  SELECT A.UNLOSE_DATE,A.LOST_DATE
    FROM APP___PAS__DBUSER.T_POLICY_LOSE A
   WHERE A.POLICY_CODE = #{policy_code}
   ORDER BY A.UPDATE_TIMESTAMP DESC
  		]]>
	</select>
	
	<select id="PA_quereyContrctProductbyPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			    SELECT B.IS_PAUSE
    FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
         APP___PAS__DBUSER.T_CONTRACT_PRODUCT   B
   WHERE A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
     AND A.MASTER_BUSI_ITEM_ID IS NULL
     AND A.POLICY_CODE = #{policy_code}
  		]]>
	</select>
	<!-- 修改操作 -->
	<update id="PA_updateCsContractMaster" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
			MERGE_SIGNATURE_FLAG = #{merge_signature_flag,jdbcType=NUMERIC},
			POLICY_PWD = #{policy_pwd, jdbcType=VARCHAR} ,
		    MEDIA_TYPE = #{media_type, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
			SALE_AGENT_NAME = #{sale_agent_name, jdbcType=VARCHAR} ,
		    INSURED_FAMILY = #{insured_family, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			DERIVATION = #{derivation, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			SUBINPUT_TYPE = #{subinput_type, jdbcType=VARCHAR} ,
			BASIC_REMARK = #{basic_remark, jdbcType=VARCHAR} ,
			SALE_COM_CODE = #{sale_com_code, jdbcType=VARCHAR} ,
		    INPUT_DATE = #{input_date, jdbcType=DATE} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    PWD_INVALID_FLAG = #{pwd_invalid_flag, jdbcType=NUMERIC} ,
		    SUBMIT_CHANNEL = #{submit_channel, jdbcType=NUMERIC} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			SALE_AGENT_CODE = #{sale_agent_code, jdbcType=VARCHAR} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENCY_CODE = #{agency_code, jdbcType=VARCHAR} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
			SERVICE_HANDLER_CODE = #{service_handler_code, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INITIAL_VALIDATE_DATE = #{initial_validate_date, jdbcType=DATE} ,
		    SUBMISSION_DATE = #{submission_date, jdbcType=DATE} ,
			SERVICE_HANDLER = #{service_handler, jdbcType=VARCHAR} ,
		    E_SERVICE_FLAG = #{e_service_flag, jdbcType=NUMERIC} ,
			SERVICE_BANK_BRANCH = #{service_bank_branch, jdbcType=VARCHAR} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    DC_INDI = #{dc_indi, jdbcType=NUMERIC} ,
			SALE_TYPE = #{sale_type, jdbcType=VARCHAR} ,
			INPUT_TYPE = #{input_type, jdbcType=VARCHAR} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			STATISTIC_CHANNEL = #{statistic_channel, jdbcType=VARCHAR} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
			AGENT_ORG_ID = #{agent_org_id, jdbcType=VARCHAR} ,
			SERVICE_BANK = #{service_bank, jdbcType=VARCHAR} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
			LANG_CODE = #{lang_code, jdbcType=VARCHAR} ,
		    FORMER_ID = #{former_id, jdbcType=NUMERIC} ,
		    RELATION_POLICY_CODE = #{relation_policy_code, jdbcType=VARCHAR} ,
		    POLICY_MANAGE_ORGAN = #{policy_manage_organ, jdbcType=VARCHAR},
		    POLICY_FLAG = #{policy_flag, jdbcType=VARCHAR},
		    <if test=" is_self_insured != null and is_self_insured != '' "><![CDATA[ IS_SELF_INSURED = #{is_self_insured, jdbcType=NUMERIC}, ]]></if>		    
		    <if test=" is_mutual_insured != null and is_mutual_insured != '' "><![CDATA[ IS_MUTUAL_INSURED = #{is_mutual_insured, jdbcType=NUMERIC}, ]]></if>
		    IS_CHANNEL_SELF_INSURED = #{is_channel_self_insured,jdbcType=NUMERIC},
		    IS_CHANNEL_MUTUAL_INSURED = #{is_channel_mutual_insured,jdbcType=NUMERIC},
		    ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>

		<select id="PA_queryContrctMasterbyPolicyFlag" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			     SELECT TCM.POLICY_ID,
        TCM.POLICY_CODE,
        TCM.RELATION_POLICY_CODE,
        TCM.INSURED_FAMILY,
        TCM.APPLY_CODE,
        TCM.DECISION_CODE,
        TCM.APPLY_DATE,
        TCM.POLICY_TYPE,
        TCM.BRANCH_CODE,
        TCM.ORGAN_CODE,
        TCM.VALIDATE_DATE,
        TCM.INITIAL_PREM_DATE,
        TCM.LIABILITY_STATE,
        TCM.LAPSE_CAUSE,
        TCM.LAPSE_DATE,
        TCM.RERINSTATE_DATE,
        TCM.SUSPEND_CAUSE,
        TCM.SUSPEND_DATE,
        TCM.END_CAUSE,
        TCM.EXPIRY_DATE,
        TCM.MONEY_CODE,
        TCM.SUBMISSION_DATE,
        TCM.SUBMIT_CHANNEL,
        TCM.CHANNEL_TYPE,
        TCM.SALE_COM_CODE,
        TCM.LANG_CODE,
        TCM.SERVICE_BANK,
        TCM.SERVICE_BANK_BRANCH,
        TCM.SERVICE_HANDLER,
        TCM.SERVICE_HANDLER_CODE,
        TCM.SALE_AGENT_CODE,
        TCM.SALE_AGENT_NAME,
        TCM.WINNING_START_FLAG,
        TCM.AGENT_ORG_ID,
        TCM.MEDIA_TYPE,
        TCM.E_SERVICE_FLAG,
        TCM.ISSUE_DATE,
        TCM.AGENCY_CODE,
        TCM.POLICY_PWD,
        TCM.PWD_INVALID_FLAG,
        TCM.INITIAL_VALIDATE_DATE,
        TCM.DERIVATION,
        TCM.FORMER_ID,
        TCM.BASIC_REMARK,
        TCM.DC_INDI,
        TCM.INTEREST_MODE,
        TCM.INPUT_DATE,
        TCM.INPUT_TYPE,
        TCM.SUBINPUT_TYPE,
        TCM.STATISTIC_CHANNEL,
        TCM.SALE_TYPE,
        TCM.SPECIAL_CUS_FLAG,
        TCM.BANK_AGENCY_FLAG,
        TCM.IS_SELF_INSURED,        
        TCM.POLICY_MANAGE_ORGAN,
        TCM.EMERGENCY_CONTACTS_NAME,
        TCM.EMERGENCY_CONTACTS_MOBILE,
        TCM.EMER_CON_RELATION_TO_PH,
        TCM.IDCARD_OCR_FLAG,
        TCM.BANKCARD_OCR_FLAG,
        TCM.POLICY_FLAG,
        TCM.DOUBLE_MAINRISK_FLAG,
        rownum rn FROM APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,APP___PAS__DBUSER.T_CONTRACT_MASTER TCM WHERE TCM.POLICY_CODE = TCA.POLICY_CODE
 AND TCA.IS_CURRENT_AGENT = '1' AND TCM.POLICY_FLAG = '2' AND TCA.AGENT_CODE= #{agent_code} AND ROWNUM < 100000
  		]]>
	</select>	
	
	<!-- 按索引查询操作 -->
	<select id="PA_findContractMasterRelation" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID,
       A.POLICY_CODE,
       A.RELATION_POLICY_CODE,
       A.INSURED_FAMILY,
       A.APPLY_CODE,
       A.DECISION_CODE,
       A.APPLY_DATE,
       A.POLICY_TYPE,
       A.BRANCH_CODE,
       A.ORGAN_CODE,
       A.VALIDATE_DATE,
       A.INITIAL_PREM_DATE,
       A.LIABILITY_STATE,
       A.LAPSE_CAUSE,
       A.LAPSE_DATE,
       A.RERINSTATE_DATE,
       A.SUSPEND_CAUSE,
       A.SUSPEND_DATE,
       A.END_CAUSE,
       A.EXPIRY_DATE,
       A.MONEY_CODE,
       A.SUBMISSION_DATE,
       A.SUBMIT_CHANNEL,
       A.CHANNEL_TYPE,
       A.SALE_COM_CODE,
       A.LANG_CODE,
       A.SERVICE_BANK,
       A.SERVICE_BANK_BRANCH,
       A.SERVICE_HANDLER,
       A.SERVICE_HANDLER_CODE,
       A.SALE_AGENT_CODE,
       A.SALE_AGENT_NAME,
       A.WINNING_START_FLAG,
       A.AGENT_ORG_ID,
       A.MEDIA_TYPE,
       A.E_SERVICE_FLAG,
       A.ISSUE_DATE,
       A.AGENCY_CODE,
       A.POLICY_PWD,
       A.PWD_INVALID_FLAG,
       A.INITIAL_VALIDATE_DATE,
       A.DERIVATION,
       A.FORMER_ID,
       A.BASIC_REMARK,
       A.DC_INDI,
       A.INTEREST_MODE,
       A.INPUT_DATE,
       A.INPUT_TYPE,
       A.SUBINPUT_TYPE,
       A.STATISTIC_CHANNEL,
       A.SALE_TYPE,
       A.SPECIAL_CUS_FLAG,
       A.BANK_AGENCY_FLAG,
       A.IS_SELF_INSURED,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.GROUP_SALE_TYPE
        FROM dev_pas.t_contract_master a where a.policy_code in (SELECT Aa.Relation_Policy_Code FROM dev_pas.t_contract_master aa where aa.policy_id = #{policy_id}]]>
		<![CDATA[)ORDER BY A.VALIDATE_DATE DESC]]>
	</select>
	
	<!-- 保单信息列表查询接口 -->
	<select id="PA_queryPolicyListInformation" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT A.POLICY_CODE,A.ORGAN_CODE,A.POLICY_REINSURE_FLAG,
           A.VALIDATE_DATE,
           A.EXPIRY_DATE,
           A.LIABILITY_STATE,
           (SELECT Z.STATUS_NAME
              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
             WHERE Z.STATUS_CODE = A.LIABILITY_STATE) AS LIABILITY_NAME,
           A.END_CAUSE,
           (SELECT Z.CAUSE_NAME
           FROM APP___PAS__DBUSER.T_END_CAUSE Z
           WHERE Z.CAUSE_CODE = A.END_CAUSE) AS END_CAUSE_NAME,
           A.LAPSE_CAUSE,
           (SELECT Z.CAUSE_DESC
           FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
           WHERE Z.CAUSE_CODE = A.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
           C.CUSTOMER_NAME,
           C.CUSTOMER_BIRTHDAY,
           C.CUSTOMER_CERT_TYPE,
           C.CUSTOMER_CERTI_CODE,
           C.CUSTOMER_GENDER,
           C.OLD_CUSTOMER_ID,
           C.CUSTOMER_ID,
           A.POLICY_ID,
           D.AGENT_CODE,
           D.AGENT_NAME,
           D.AGENT_MOBILE,
           (SELECT Z.AGENT_ORGAN_CODE
              FROM APP___PAS__DBUSER.T_AGENT Z
             WHERE Z.AGENT_CODE = D.AGENT_CODE
               AND ROWNUM = 1) AS AGENT_ORGAN_CODE,
           (SELECT Y.ORGAN_NAME
              FROM APP___PAS__DBUSER.T_UDMP_ORG Y,
                   APP___PAS__DBUSER.T_AGENT    Z
             WHERE Y.ORGAN_CODE = Z.AGENT_ORGAN_CODE
               AND Z.AGENT_CODE = D.AGENT_CODE) AS ORGAN_NAME,
               (SELECT (SELECT Y.PAY_TYPE_NAME
              FROM APP___PAS__DBUSER.T_PAY_LOCATION Y
             WHERE Y.PAY_TYPE_CODE = Z.PAY_LOCATION)
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID =A.POLICY_ID AND ROWNUM = 1) AS PAY_LOCATION_NAME,
             (SELECT (SELECT Y.BANK_NAME
              FROM APP___PAS__DBUSER.T_BANK Y
             WHERE Y.BANK_CODE = Z.NEXT_ACCOUNT_BANK)
             FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
             WHERE Z.POLICY_ID =A.POLICY_ID AND ROWNUM = 1) AS BANK_NAME,
     (SELECT Z.NEXT_ACCOUNT
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT,
     (SELECT T.NAME FROM DEV_PAS.T_PAY_MODE T WHERE T.CODE IN(SELECT E.PAY_NEXT FROM DEV_PAS.T_PAYER_ACCOUNT E WHERE E.POLICY_ID = A.POLICY_ID))NEXT_PAY_NEXT,		 
	 (SELECT N.NEXT_ACCOUNT_NAME FROM DEV_PAS.T_PAYER_ACCOUNT N WHERE N.POLICY_ID = A.POLICY_ID)NEXT_ACCOUNT_NAME,	
     (SELECT X.CUSTOMER_ID
        FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,APP___PAS__DBUSER.T_BENEFIT_INSURED    Y, APP___PAS__DBUSER.T_INSURED_LIST       X
       WHERE Z.BUSI_ITEM_ID = Y.BUSI_ITEM_ID AND Z.POLICY_CODE=Y.POLICY_CODE AND Z.MASTER_BUSI_ITEM_ID IS NULL AND Y.INSURED_ID = X.LIST_ID
         AND Y.ORDER_ID = 1 AND Z.POLICY_CODE =A.POLICY_CODE AND ROWNUM = 1) AS INSURNO,
         A.Special_Account_Flag,
         A.Multi_Mainrisk_Flag
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
           APP___PAS__DBUSER.T_POLICY_HOLDER   B,
           APP___PAS__DBUSER.T_CUSTOMER        C,
           APP___PAS__DBUSER.T_CONTRACT_AGENT  D
     WHERE A.POLICY_CODE = B.POLICY_CODE
       AND A.POLICY_CODE = D.POLICY_CODE
       AND D.IS_CURRENT_AGENT = 1
       AND B.CUSTOMER_ID = C.CUSTOMER_ID
       AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>  
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND A.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND A.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND A.LIABILITY_STATE = #{liability_state} ]]></if>
        
	</select>
	
		<!-- 保单信息列表查询接口  不查未生效的-->
	<select id="PA_queryPolicyListInformationUnenforced" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT A.POLICY_CODE,A.ORGAN_CODE,
        A.SPECIAL_ACCOUNT_FLAG,
           A.ORGAN_CODE,
           A.VALIDATE_DATE,
           A.LIABILITY_STATE,
           (SELECT Z.STATUS_NAME
              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
             WHERE Z.STATUS_CODE = A.LIABILITY_STATE) AS LIABILITY_NAME,
           A.END_CAUSE,
           (SELECT Z.CAUSE_NAME
           FROM APP___PAS__DBUSER.T_END_CAUSE Z
           WHERE Z.CAUSE_CODE = A.END_CAUSE) AS END_CAUSE_NAME,
           A.LAPSE_CAUSE,
           (SELECT Z.CAUSE_DESC
           FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
           WHERE Z.CAUSE_CODE = A.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
           C.CUSTOMER_NAME,
           C.CUSTOMER_BIRTHDAY,
           C.CUSTOMER_CERT_TYPE,
           C.CUSTOMER_CERTI_CODE,
           C.CUSTOMER_GENDER,
           C.OLD_CUSTOMER_ID,
           C.CUSTOMER_ID,
           C.CUSTOMER_RISK_LEVEL,
           A.POLICY_ID,
           (SELECT Z.AGENT_ORGAN_CODE
              FROM APP___PAS__DBUSER.T_AGENT Z
             WHERE Z.AGENT_CODE = D.AGENT_CODE
               AND ROWNUM = 1) AS AGENT_ORGAN_CODE,
           (SELECT Y.ORGAN_NAME
              FROM APP___PAS__DBUSER.T_UDMP_ORG Y,
                   APP___PAS__DBUSER.T_AGENT    Z
             WHERE Y.ORGAN_CODE = Z.AGENT_ORGAN_CODE
               AND Z.AGENT_CODE = D.AGENT_CODE) AS ORGAN_NAME,
               (SELECT (SELECT Y.NAME
                  FROM APP___PAS__DBUSER.T_PAY_MODE Y
                 WHERE Y.CODE = Z.PAY_NEXT)
          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND ROWNUM = 1) AS PAY_LOCATION_NAME,
             (SELECT (SELECT Y.BANK_NAME
              FROM APP___PAS__DBUSER.T_BANK Y
             WHERE Y.BANK_CODE = Z.NEXT_ACCOUNT_BANK)
             FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
             WHERE Z.POLICY_ID =A.POLICY_ID AND ROWNUM = 1) AS BANK_NAME,
     (SELECT Z.NEXT_ACCOUNT
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT,
     (SELECT Z.NEXT_ACCOUNT_BANK
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT_BANK,
     (SELECT Z.NEXT_ACCOUNT_NAME
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT_NAME,
	       (SELECT TCBP.BUSI_PROD_CODE
	          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
	         WHERE TCBP.POLICY_CODE = A.POLICY_CODE
	           AND TCBP.BUSI_PROD_CODE = '********'
	           AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
	           AND ROWNUM = 1) BUSI_PROD_CODE,
	       (SELECT TCR.MASTER_POLICY_CODE
	          FROM APP___PAS__DBUSER.T_CONTRACT_RELATION TCR
	         WHERE TCR.SUB_POLICY_CODE = A.POLICY_CODE
	           AND TCR.RELATION_TYPE = '1'
	           AND ROWNUM = 1) MASTER_POLICY_CODE,
	       (SELECT TCR.SUB_POLICY_CODE
	          FROM APP___PAS__DBUSER.T_CONTRACT_RELATION TCR
	         WHERE TCR.MASTER_POLICY_CODE = A.POLICY_CODE
	           AND TCR.RELATION_TYPE = '1'
	           AND ROWNUM = 1) SUB_POLICY_CODE,
             A.SUBMIT_CHANNEL
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
           APP___PAS__DBUSER.T_POLICY_HOLDER   B,
           APP___PAS__DBUSER.T_CUSTOMER        C,
           APP___PAS__DBUSER.T_CONTRACT_AGENT  D
     WHERE A.POLICY_CODE = B.POLICY_CODE
       AND A.POLICY_CODE = D.POLICY_CODE
       AND D.IS_CURRENT_AGENT = 1
       AND B.CUSTOMER_ID = C.CUSTOMER_ID
       AND A.LIABILITY_STATE <> '0'
       AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
	</select>
	
	<!-- 查询保单号是否是在该业务员名下，并且按照生效日倒序 -->
	<select id="PA_queryPolicyByGgentcodeAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
          	          SELECT A.POLICY_CODE,
	       A.ORGAN_CODE,
	       A.VALIDATE_DATE,
	       A.EXPIRY_DATE,
	       A.LIABILITY_STATE,
	       A.SPECIAL_ACCOUNT_FLAG,
	       (SELECT Z.STATUS_NAME
	          FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
	         WHERE Z.STATUS_CODE = A.LIABILITY_STATE) AS LIABILITY_NAME,
	       A.END_CAUSE,
	       (SELECT Z.CAUSE_NAME
	          FROM APP___PAS__DBUSER.T_END_CAUSE Z
	         WHERE Z.CAUSE_CODE = A.END_CAUSE) AS END_CAUSE_NAME,
	       A.LAPSE_CAUSE,
	       (SELECT Z.CAUSE_DESC
	          FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
	         WHERE Z.CAUSE_CODE = A.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
	       C.CUSTOMER_NAME,
	       C.CUSTOMER_BIRTHDAY,
	       C.CUSTOMER_CERT_TYPE,
	       C.CUSTOMER_CERTI_CODE,
	       C.CUSTOMER_GENDER,
	       C.OLD_CUSTOMER_ID,
	       C.CUSTOMER_ID,
	       A.POLICY_ID,
	       (SELECT Z.AGENT_ORGAN_CODE
	          FROM APP___PAS__DBUSER.T_AGENT Z
	         WHERE Z.AGENT_CODE = D.AGENT_CODE
	           AND ROWNUM = 1) AS AGENT_ORGAN_CODE,
	       (SELECT Y.ORGAN_NAME
	          FROM APP___PAS__DBUSER.T_UDMP_ORG Y, APP___PAS__DBUSER.T_AGENT Z
	         WHERE Y.ORGAN_CODE = Z.AGENT_ORGAN_CODE
	           AND Z.AGENT_CODE = D.AGENT_CODE) AS ORGAN_NAME,
	       (SELECT (SELECT Y.PAY_TYPE_NAME
	                  FROM APP___PAS__DBUSER.T_PAY_LOCATION Y
	                 WHERE Y.PAY_TYPE_CODE = Z.PAY_LOCATION)
	          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
	         WHERE Z.POLICY_ID = A.POLICY_ID
	           AND ROWNUM = 1) AS PAY_LOCATION_NAME,
	       (SELECT (SELECT Y.BANK_NAME
	                  FROM APP___PAS__DBUSER.T_BANK Y
	                 WHERE Y.BANK_CODE = Z.NEXT_ACCOUNT_BANK)
	          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
	         WHERE Z.POLICY_ID = A.POLICY_ID
	           AND ROWNUM = 1) AS BANK_NAME,
	       (SELECT Z.NEXT_ACCOUNT
	          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
	         WHERE Z.POLICY_ID = A.POLICY_ID
	           AND ROWNUM = 1) AS NEXT_ACCOUNT,
	       (SELECT X.CUSTOMER_ID
	          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,
	               APP___PAS__DBUSER.T_BENEFIT_INSURED    Y,
	               APP___PAS__DBUSER.T_INSURED_LIST       X
	         WHERE Z.BUSI_ITEM_ID = Y.BUSI_ITEM_ID
	           AND Z.POLICY_CODE = Y.POLICY_CODE
	           AND Z.MASTER_BUSI_ITEM_ID IS NULL
	           AND Y.INSURED_ID = X.LIST_ID
	           AND Y.ORDER_ID = 1
	           AND Z.POLICY_CODE = A.POLICY_CODE
	           AND ROWNUM = 1) AS INSURNO,
	       (SELECT TCBP.BUSI_PROD_CODE
	          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
	         WHERE TCBP.POLICY_CODE = A.POLICY_CODE
	           AND TCBP.BUSI_PROD_CODE in (select a.PRODUCT_CODE_SYS  from APP___PAS__DBUSER.T_BUSINESS_PRODUCT a
         where a.ACC_RISK_SINGLE_INSURE_FLAG='1')
	           
	           AND ROWNUM = 1) BUSI_PROD_CODE,
	       (SELECT TCR.MASTER_POLICY_CODE
	          FROM APP___PAS__DBUSER.T_CONTRACT_RELATION TCR
	         WHERE TCR.SUB_POLICY_CODE = A.POLICY_CODE
	           AND TCR.RELATION_TYPE = '1'
	           AND ROWNUM = 1) MASTER_POLICY_CODE,
	       (SELECT TCR.SUB_POLICY_CODE
	          FROM APP___PAS__DBUSER.T_CONTRACT_RELATION TCR
	         WHERE TCR.MASTER_POLICY_CODE = A.POLICY_CODE
	           AND TCR.RELATION_TYPE = '1'
	           AND ROWNUM = 1) SUB_POLICY_CODE,
	           A.ISSUE_DATE,
	           (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = D.AGENT_CODE) as agent_channel,
               (SELECT CA.AGENT_LEVEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = D.AGENT_CODE) as ca_agent_level,
               (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = D.AGENT_CODE) as tab_agent_level
	  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
	       APP___PAS__DBUSER.T_POLICY_HOLDER   B,
	       APP___PAS__DBUSER.T_CUSTOMER        C,
	       APP___PAS__DBUSER.T_CONTRACT_AGENT  D
	 WHERE A.POLICY_CODE = B.POLICY_CODE
	   AND A.POLICY_CODE = D.POLICY_CODE
	   AND D.IS_CURRENT_AGENT = 1
	   AND B.CUSTOMER_ID = C.CUSTOMER_ID       
       AND D.AGENT_CODE=#{agent_code,jdbcType=VARCHAR} AND A.POLICY_CODE IN ]]>
      <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
       </foreach>
       <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
          <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = A.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
       </if>
   <![CDATA[ ORDER BY A.VALIDATE_DATE DESC  ]]>   
	</select>
	
	<!-- 查询保单是否冻结-->
	<select id="PA_quereyPolicyFreezeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
     SELECT A.UNFREEZE_DATE,A.FREEZE_DATE FROM APP___PAS__DBUSER.T_POLICY_FREEZE A 
     WHERE A.POLICY_CODE=#{policy_code,jdbcType=VARCHAR} 
     ORDER BY A.UPDATE_TIMESTAMP DESC
        ]]>
	</select>
	
	<!-- 查询保单  生存给付银行划款期间-->
	<select id="PA_queryExistenceByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
          SELECT A.POLICY_CODE, A.UNIT_NUMBER, A.FEE_STATUS
          FROM APP___PAS__DBUSER.T_PAY_DUE A
         WHERE A.POLICY_CODE = #{policy_code}
        ]]>
	</select>
	
	<!-- 续期划款-->
	<select id="PA_queryPCBanckTransferFunds" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
              SELECT DISTINCT C.UNIT_NUMBER
      FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,
           APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,
           APP___PAS__DBUSER.T_PREM_ARAP        C
     WHERE A.CHANGE_ID = B.CHANGE_ID
       AND B.ACCEPT_CODE = C.BUSINESS_CODE
       AND B.SERVICE_CODE='PC'
       AND A.POLICY_CODE = #{POLICY_CODE,jdbcType=VARCHAR}
        ]]>
	</select>
	
	<!-- 查询保单是否客户未签收     保单没有回执签收日期-->
	<select id="PA_queryPolicyAcknowledgement" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
            SELECT A.ACKNOWLEDGE_DATE,A.MEDIA_TYPE 
		   FROM APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT A 
		   WHERE A.ACKNOWLEDGE_DATE IS NULL AND A.POLICY_ID=#{policy_id, jdbcType=NUMERIC}
        ]]>
	</select>
	
	<!-- 附加特约终止-->
	<select id="PA_queryPolicyAttachedEnd" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
          SELECT A.POLICY_CODE,A.BUSI_ITEM_ID FROM DEV_PAS.T_CONTRACT_BUSI_PROD A 
          WHERE A.BUSI_PROD_CODE='00504000' AND A.LIABILITY_STATE='3' AND A.POLICY_CODE=#{POLICY_CODE,jdbcType=VARCHAR}
        ]]>
	</select>
	
	<select id="PA_queryPolicyContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
     SELECT A.POLICY_CODE,
                A.POLICY_ID,
                A.ORGAN_CODE,
                A.VALIDATE_DATE,
                A.LIABILITY_STATE,
                A.END_CAUSE,
                A.LAPSE_CAUSE,
                B.ACKNOWLEDGE_DATE,
                A.LAPSE_DATE,
                (CASE
                  WHEN A.LAPSE_CAUSE = '6' THEN
                   'Y'
                  ELSE
                   'N'
                END) AS ENDCAUSE,
                (SELECT LS.STATUS_NAME
                   FROM APP___PAS__DBUSER.T_LIABILITY_STATUS LS
                  WHERE LS.STATUS_CODE = A.LIABILITY_STATE) AS POLICYSTATUS,
                (CASE
                  WHEN A.LIABILITY_STATE = '4' THEN
                   (SELECT LC.CAUSE_DESC
                      FROM APP___PAS__DBUSER.T_LAPSE_CAUSE LC
                     WHERE LC.CAUSE_CODE = A.LAPSE_CAUSE)
                  WHEN A.LIABILITY_STATE = '3' THEN
                   (SELECT EC.CAUSE_NAME
                      FROM APP___PAS__DBUSER.T_END_CAUSE EC
                     WHERE EC.CAUSE_CODE = A.END_CAUSE)
                END) AS STATUSMESSAGE,
                A.AGENT_ORG_ID,
                C.CUSTOMER_ID AS INSURED_FAMILY,
                (SELECT MAX(Y.IS_PAUSE) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Y 
                WHERE D.BUSI_ITEM_ID=Y.BUSI_ITEM_ID 
                AND Y.IS_PAUSE=1 AND Y.PAUSE_DATE IS NOT NULL) IS_PAUSE,
                A.ISSUE_DATE,         
                A.EXPIRY_DATE
           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER        A,
                APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT B,
                APP___PAS__DBUSER.T_POLICY_HOLDER          C,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD D
          WHERE A.POLICY_ID = B.POLICY_ID
            AND A.POLICY_ID = C.POLICY_ID
            AND A.POLICY_ID = D.POLICY_ID
            AND D.MASTER_BUSI_ITEM_ID IS NULL
            AND A.POLICY_CODE=#{policy_code}
            AND ROWNUM = 1
  ]]>
		
	</select>
	 <!-- 置灰码值 -->
   <sql id="QueryGreyCode">
<if test="sale_agent_name!=null and sale_agent_name!='' and agency_code !=null and agency_code!=''">
<if test="policy_flag!=null and policy_flag!='' and policy_flag =='0'.toString()">
<![CDATA[
(SELECT distinct DECODE(E.POLICY_CODE, AA.POLICY_CODE, '4001;')|| 

       DECODE(B.POLICY_ID, AA.POLICY_ID, '4002;')||  
       
       DECODE(AA.END_CAUSE, '06', '4003;')|| 
       
       DECODE(B.LOCK_SERVICE_ID, '93', '4004;')|| 
       
       DECODE(AA.END_CAUSE, '03', '4005;')||  
       
       DECODE(AA.END_CAUSE, '01', '4006;')|| 
       
       DECODE(AA.END_CAUSE, '02', '4007;')||
       
       DECODE(AA.END_CAUSE, '06', '4008;')||
       
       DECODE(AA.END_CAUSE, '08', '4009;')|| 
       
	   (case when cp.IS_PAUSE = '1' and cp.PAUSE_DATE is not null then '4010;' else '' end)|| 
       
       DECODE(AA.liability_state, '1' , '4011;' ,'2','4011;' ,'3','4011;') || 
         (case when  pa.acknowledge_date is null  then '4012;' else '' end) 
               as greycode 
             

  FROM DEV_PAS.T_CONTRACT_MASTER AA
  LEFT JOIN DEV_PAS.T_LOCK_POLICY B
    ON AA.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT CC
    ON AA.POLICY_ID = CC.POLICY_ID
   AND CC.ACCOUNT_TYPE = '5'
   AND CC.REGULAR_REPAY = '0'
  LEFT JOIN DEV_PAS.T_POLICY_LOSE E
    ON E.POLICY_CODE = AA.POLICY_CODE
   AND E.UNLOSE_DATE IS NULL
  LEFT JOIN dev_pas.t_contract_product cp  on 
  cp.POLICY_CODE = AA.POLICY_CODE 
   LEFT JOIN dev_pas.T_POLICY_ACKNOWLEDGEMENT pa on 
  	pa.POLICY_ID  = AA.POLICY_ID  
  
 WHERE 1 = 1 and AA.Policy_Code=a.policy_code and AA.POLICY_CODE=c.policy_code 
 and c.agent_code=#{agentcode} and exists(
select policy_code from APP___PAS__DBUSER.T_policy_holder tt  where tt.policy_code=a.policy_code and  customer_id=(
select customer_id from APP___PAS__DBUSER.T_customer where customer_name=#{sale_agent_name} and customer_certi_code=#{agency_code}))
 ) as PutAshCase,
	]]>
	</if>
	<if test="policy_flag!=null and policy_flag!='' and policy_flag =='1'.toString()">
<![CDATA[
(SELECT distinct DECODE(E.POLICY_CODE, AA.POLICY_CODE, '4001;')|| 

       DECODE(B.POLICY_ID, AA.POLICY_ID, '4002;')||  
       
       DECODE(AA.END_CAUSE, '06', '4003;')|| 
       
       DECODE(B.LOCK_SERVICE_ID, '93', '4004;')|| 
       
       DECODE(AA.END_CAUSE, '03', '4005;')||  
       
       DECODE(AA.END_CAUSE, '01', '4006;')|| 
       
       DECODE(AA.END_CAUSE, '02', '4007;')||
       
       DECODE(AA.END_CAUSE, '06', '4008;')||
       
       DECODE(AA.END_CAUSE, '08', '4009;')|| 
       
	   (case when cp.IS_PAUSE = '1' and cp.PAUSE_DATE is not null then '4010;' else '' end)|| 
       
       DECODE(AA.liability_state, '1' , '4011;' ,'2','4011;' ,'3','4011;') || 
       
       (case when  pa.acknowledge_date is null  then '4012;' else '' end) 
               as greycode

  FROM DEV_PAS.T_CONTRACT_MASTER AA
  LEFT JOIN DEV_PAS.T_LOCK_POLICY B
    ON AA.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT CC
    ON AA.POLICY_ID = CC.POLICY_ID
   AND CC.ACCOUNT_TYPE = '5'
   AND CC.REGULAR_REPAY = '0'
  LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT D
    ON D.POLICY_ID = AA.POLICY_ID
  LEFT JOIN DEV_PAS.T_POLICY_LOSE E
    ON E.POLICY_CODE = AA.POLICY_CODE
   AND E.UNLOSE_DATE IS NULL 
    LEFT JOIN dev_pas.t_contract_product cp on 
  cp.POLICY_CODE = AA.POLICY_CODE 
   LEFT JOIN dev_pas.T_POLICY_ACKNOWLEDGEMENT pa on 
  	pa.POLICY_ID  = AA.POLICY_ID  
  
 WHERE 1 = 1 and AA.Policy_Code=a.policy_code and AA.POLICY_CODE=d.policy_code 
 and d.agent_code=#{agentcode} and exists (
select policy_code from APP___PAS__DBUSER.T_insured_list mm where c.policy_code=mm.policy_code and customer_id=(
select customer_id from APP___PAS__DBUSER.T_customer t where t.customer_name=#{sale_agent_name} and t.customer_certi_code=#{agency_code}))
 ) as PutAshCase,
	]]>
	</if>
	<if test="policy_flag!=null and policy_flag!='' and policy_flag =='2'.toString()">
<![CDATA[
(SELECT distinct DECODE(E.POLICY_CODE, AA.POLICY_CODE, '4001;')|| 

       DECODE(B.POLICY_ID, AA.POLICY_ID, '4002;')||  
       
       DECODE(AA.END_CAUSE, '06', '4003;')|| 
       
       DECODE(B.LOCK_SERVICE_ID, '93', '4004;')|| 
       
       DECODE(AA.END_CAUSE, '03', '4005;')||  
       
       DECODE(AA.END_CAUSE, '01', '4006;')|| 
       
       DECODE(AA.END_CAUSE, '02', '4007;')||
       
       DECODE(AA.END_CAUSE, '06', '4008;')||
       
       DECODE(AA.END_CAUSE, '08', '4009;')|| 
       
	   (case when cp.IS_PAUSE = '1' and cp.PAUSE_DATE is not null then '4010;' else '' end)|| 
       
       DECODE(AA.liability_state, '1' , '4011;' ,'2','4011;' ,'3','4011;') || 
       
       (case when  pa.acknowledge_date is null  then '4012;' else '' end) 
               as greycode

  FROM DEV_PAS.T_CONTRACT_MASTER AA
  LEFT JOIN DEV_PAS.T_LOCK_POLICY B
    ON AA.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT CC
    ON AA.POLICY_ID = CC.POLICY_ID
   AND CC.ACCOUNT_TYPE = '5'
   AND CC.REGULAR_REPAY = '0'
  LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT D
    ON D.POLICY_ID = AA.POLICY_ID
  LEFT JOIN DEV_PAS.T_POLICY_LOSE E
    ON E.POLICY_CODE = AA.POLICY_CODE
   AND E.UNLOSE_DATE IS NULL 
    LEFT JOIN dev_pas.t_contract_product cp on 
  cp.POLICY_CODE = AA.POLICY_CODE 
  	LEFT JOIN dev_pas.T_POLICY_ACKNOWLEDGEMENT pa on 
  	pa.POLICY_ID  = AA.POLICY_ID 
   
 WHERE 1 = 1 and AA.Policy_Code=a.policy_code and AA.POLICY_CODE=c.policy_code 
 and c.agent_code=#{agentcode} and exists (select tt.policy_code
          from APP___PAS__DBUSER.t_policy_holder tt,APP___PAS__DBUSER.T_INSURED_LIST ss
         where tt.policy_code = a.policy_code
         and tt.policy_code=ss.policy_code and tt.customer_id=ss.customer_id
           and tt.customer_id =
               (select customer_id
                  from APP___PAS__DBUSER.t_customer
                 where customer_name =#{sale_agent_name} and customer_certi_code=#{agency_code}))

 ) as PutAshCase,
	]]>
	</if>
</if>
<if test="policy_code !=null and policy_code !=''">
<![CDATA[ 
(SELECT distinct  DECODE(E.POLICY_CODE, AA.POLICY_CODE, '4001;')|| 

       DECODE(B.POLICY_ID, AA.POLICY_ID, '4002;')||  
       
       DECODE(AA.END_CAUSE, '06', '4003;')|| 
       
       DECODE(B.LOCK_SERVICE_ID, '93', '4004;')|| 
       
       DECODE(AA.END_CAUSE, '03', '4005;')||  
       
       DECODE(AA.END_CAUSE, '01', '4006;')|| 
       
       DECODE(AA.END_CAUSE, '02', '4007;')||
       
       DECODE(AA.END_CAUSE, '06', '4008;')||
       
       DECODE(AA.END_CAUSE, '08', '4009;')|| 
       
	   (case when cp.IS_PAUSE = '1' and cp.PAUSE_DATE is not null then '4010;' else '' end)|| 
       
       DECODE(AA.liability_state, '1' , '4011;' ,'2','4011;' ,'3','4011;') || 
       (case when  pa.acknowledge_date is null  then '4012;' else '' end) 
               as greycode

  FROM DEV_PAS.T_CONTRACT_MASTER AA
  LEFT JOIN DEV_PAS.T_LOCK_POLICY B
    ON AA.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT CC
    ON AA.POLICY_ID = CC.POLICY_ID
   AND CC.ACCOUNT_TYPE = '5'
   AND CC.REGULAR_REPAY = '0'
  LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT D
    ON D.POLICY_ID = AA.POLICY_ID
  LEFT JOIN DEV_PAS.T_POLICY_LOSE E
    ON E.POLICY_CODE = AA.POLICY_CODE
   AND E.UNLOSE_DATE IS NULL 
   LEFT JOIN dev_pas.t_contract_product cp on 
  cp.POLICY_CODE = AA.POLICY_CODE 
  LEFT JOIN dev_pas.T_POLICY_ACKNOWLEDGEMENT pa on 
  	pa.POLICY_ID  = AA.POLICY_ID  
  
 WHERE 1 = 1
and AA.policy_code=#{policy_code} ) as PutAshCase,

]]>
</if>
   </sql>
   
     <!-- 68313 受益人变更-保单列表查询-->
    <sql id="PA_findByAgentCodeWhereCondition">
    	<if test=" policy_code  != null and policy_code  != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
    	<if test=" customer_name  != null and customer_name  != '' "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
    	<if test=" customer_certi_code  != null and customer_certi_code  != '' "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
    	<if test=" customer_cert_type  != null and customer_cert_type  != '' "><![CDATA[ AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
    	<if test=" customer_id  != null and customer_id  != '' "><![CDATA[ AND TC.CUSTOMER_ID = #{customer_id} ]]></if>
    	<if test=" agent_code  != null and agent_code  != '' "><![CDATA[ AND TT.AGENT_CODE = #{agent_code} ]]></if>
    	<if test=" customer_gender  != null and customer_gender  != '' "><![CDATA[ AND TC.CUSTOMER_GENDER = #{customer_gender} ]]></if>
    	<if test=" customer_birthday  != null and customer_birthday  != '' "><![CDATA[ AND TC.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
    </sql>
   <select id="findContractMasterByAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT TCM.POLICY_ID,
	                   TCM.POLICY_CODE, /*保单号*/
				       TCM.VALIDATE_DATE, /*保单生效日*/
				       TCM.EXPIRY_DATE, /*保单终止日*/
				       TCM.LIABILITY_STATE, /*保单状态*/
				       TCM.SPECIAL_ACCOUNT_FLAG,
				       TCM.APPLY_DATE,
				       TCM.MULTI_MAINRISK_FLAG,
				       (SELECT EC.CAUSE_NAME FROM APP___PAS__DBUSER.T_END_CAUSE EC WHERE EC.CAUSE_CODE=TCM.END_CAUSE ) AS end_cause_name,
				       (SELECT Z.CAUSE_DESC  FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
				       (SELECT TA.AGENT_ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_AGENT TA
				         WHERE TT.AGENT_CODE = TA.AGENT_CODE) AS AGENT_ORGAN_CODE, /*业务员所属管理机构*/
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T, APP___PAS__DBUSER.T_AGENT TA
				         WHERE TT.AGENT_CODE = TA.AGENT_CODE
				           AND TA.AGENT_ORGAN_CODE = T.ORGAN_CODE) AS AGENT_ORGAN_NAME, /*业务员所属管理机构名称*/
				       TCM.ORGAN_CODE, /*保单管理机构代码*/
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T
				         WHERE TCM.ORGAN_CODE = T.ORGAN_CODE) ORGAN_NAME /*保单管理机构名称*/
				         ,TCM.ISSUE_DATE,
				       (SELECT 1
				          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
				               APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
				         WHERE A.POLICY_ID = TCM.POLICY_ID
				           AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
				           AND B.TAX_REVENUE_FLAG = '1'
				           AND ROWNUM = 1) TAX_REVENUE_FLAG,
				       (SELECT SPH.CUSTOMER_ID
				          FROM DEV_PAS.T_SECOND_POLICY_HOLDER SPH
				         WHERE SPH.POLICY_ID = TCM.POLICY_ID
				           AND ROWNUM = 1) SECOND_CUSTOMER_ID,
				       TT.AGENT_CODE
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
			       APP___PAS__DBUSER.T_CONTRACT_AGENT  TT,
			       APP___PAS__DBUSER.T_POLICY_HOLDER   TB,
			       APP___PAS__DBUSER.T_CUSTOMER        TC
			 WHERE TCM.POLICY_ID = TT.POLICY_ID
			   AND TCM.POLICY_ID = TB.POLICY_ID
			   AND TB.CUSTOMER_ID = TC.CUSTOMER_ID
			   AND TT.IS_CURRENT_AGENT = 1
			   /*AND TCM.LIABILITY_STATE = 1*/
			   /*AND TCM.VALIDATE_DATE < SYSDATE*/
		]]>
		<include refid="PA_findByAgentCodeWhereCondition" />
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = TCM.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		</if>
		<![CDATA[			   
			UNION 
			SELECT TCM.POLICY_ID,
			           TCM.POLICY_CODE, /*保单号*/
				       TCM.VALIDATE_DATE, /*保单生效日*/
				       TCM.EXPIRY_DATE, /*保单终止日*/
				       TCM.LIABILITY_STATE, /*保单状态*/
				       TCM.SPECIAL_ACCOUNT_FLAG,
				       TCM.APPLY_DATE,
				       TCM.MULTI_MAINRISK_FLAG,
				       (SELECT EC.CAUSE_NAME FROM APP___PAS__DBUSER.T_END_CAUSE EC WHERE EC.CAUSE_CODE=TCM.END_CAUSE ) AS end_cause_name,
				       (SELECT Z.CAUSE_DESC  FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
				       (SELECT TA.AGENT_ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_AGENT TA
				         WHERE TT.AGENT_CODE = TA.AGENT_CODE) AS AGENT_ORGAN_CODE, /*业务员所属管理机构*/
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T, APP___PAS__DBUSER.T_AGENT TA
				         WHERE TT.AGENT_CODE = TA.AGENT_CODE
				           AND TA.AGENT_ORGAN_CODE = T.ORGAN_CODE) AS AGENT_ORGAN_NAME, /*业务员所属管理机构名称*/
				       TCM.ORGAN_CODE, /*保单管理机构代码*/
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T
				         WHERE TCM.ORGAN_CODE = T.ORGAN_CODE) ORGAN_NAME /*保单管理机构名称*/
				         ,TCM.ISSUE_DATE,
				       (SELECT 1
				          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
				               APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
				         WHERE A.POLICY_ID = TCM.POLICY_ID
				           AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
				           AND B.TAX_REVENUE_FLAG = '1'
				           AND ROWNUM = 1) TAX_REVENUE_FLAG,
				       (SELECT SPH.CUSTOMER_ID
				          FROM DEV_PAS.T_SECOND_POLICY_HOLDER SPH
				         WHERE SPH.POLICY_ID = TCM.POLICY_ID
				           AND ROWNUM = 1) SECOND_CUSTOMER_ID,
				       TT.AGENT_CODE
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
			       APP___PAS__DBUSER.T_CONTRACT_AGENT  TT,
			       APP___PAS__DBUSER.T_INSURED_LIST    TB,
			       APP___PAS__DBUSER.T_CUSTOMER        TC
			 WHERE TCM.POLICY_ID = TT.POLICY_ID
			   AND TCM.POLICY_ID = TB.POLICY_ID
			   AND TB.CUSTOMER_ID = TC.CUSTOMER_ID
			   AND TT.IS_CURRENT_AGENT = 1
			   /*AND TCM.LIABILITY_STATE = 1*/
			   AND TCM.VALIDATE_DATE < SYSDATE
		]]>
		<include refid="PA_findByAgentCodeWhereCondition" />
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = TCM.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		</if>
   </select>
   
   <select id="findCustomerRoleHolderByFiveCon" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
               SELECT TPH.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
               APP___PAS__DBUSER.T_CUSTOMER        TC,
               APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
               WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                     AND TPH.POLICY_CODE = TCM.POLICY_CODE
        ]]>
             <include refid="PA_findByAgentCodeWhereCondition" />     
   </select>
   
      <select id="findCustomerRoleInsuredByFiveCon" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
               SELECT TIL.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_INSURED_LIST TIL,
               APP___PAS__DBUSER.T_CUSTOMER        TC,
               APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
               WHERE TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                     AND TIL.POLICY_CODE = TCM.POLICY_CODE
        ]]>
             <include refid="PA_findByAgentCodeWhereCondition" />     
   </select>
   
   <!--查询业务锁信息  -->
<select id="PA_queryLockPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[ SELECT LP.OPERATION_ID,
       LP.POLICY_ID,
       LP.POLICY_CODE,
       LP.LOCK_SERVICE_ID,
       LP.INSERT_TIME,
       SD.LOCK_SERVICE_NAME,
       SD.LOCK_SERVICE_TYPE,
       SD.SUB_ID,
       LP.EXCEPT_GROUP_1,
       LP.EXCEPT_GROUP_2
  FROM APP___PAS__DBUSER.T_LOCK_POLICY LP
 INNER JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF SD
    ON SD.LOCK_SERVICE_ID = LP.LOCK_SERVICE_ID
 WHERE 1 = 1]]>
   <if test=" policy_code  != null "><![CDATA[ AND LP.POLICY_CODE = #{policy_code} ]]></if>
   <![CDATA[UNION
    SELECT 
    LP.OPERATION_ID,
    LP.POLICY_ID,
       LP.POLICY_CODE,
       LP.LOCK_SERVICE_ID,
       LP.INSERT_TIME,
       SD.LOCK_SERVICE_NAME,
       SD.LOCK_SERVICE_TYPE,
       SD.SUB_ID,
       LP.EXCEPT_GROUP_1,
       LP.EXCEPT_GROUP_2
  FROM APP___PAS__DBUSER.T_LOCK_POLICY LP
 INNER JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF SD
    ON SD.LOCK_SERVICE_ID = LP.LOCK_SERVICE_ID
 WHERE 1 = 1 AND LP.POLICY_ID = (SELECT CM.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER CM WHERE 1=1 ]]>
 <if test=" policy_code  != null "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
  <![CDATA[)]]>
</select>

<!--查询业务锁信息  -->
<select id="PA_queryBQLockPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[ SELECT  LP.POLICY_CODE
  FROM APP___PAS__DBUSER.T_LOCK_POLICY LP
 INNER JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF SD
    ON SD.LOCK_SERVICE_ID = LP.LOCK_SERVICE_ID
 WHERE 1 = 1 and  SD.SUB_ID = '068']]>
   <if test=" policy_code  != null "><![CDATA[ AND LP.POLICY_CODE = #{policy_code} ]]></if>
   <![CDATA[UNION
    SELECT LP.POLICY_CODE
  FROM APP___PAS__DBUSER.T_LOCK_POLICY LP
 INNER JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF SD
    ON SD.LOCK_SERVICE_ID = LP.LOCK_SERVICE_ID
 WHERE 1 = 1 and  SD.SUB_ID = '068' AND  LP.POLICY_ID = (SELECT CM.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER CM WHERE 1=1 ]]>
 <if test=" policy_code  != null "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
  <![CDATA[)]]>
</select>

  <select id="PA_queryLiabilityStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select l.policy_code as Contno,l.liability_state as appflag  from APP___PAS__DBUSER.T_CONTRACT_MASTER l WHERE 1=1
		  and l.policy_code = #{policy_code}
  ]]>	
	</select>  
	
	<!-- 查询保单是否挂失或者理赔未决状态 -->
	<select id="PA_QueryPolicyStatu" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TO_CHAR(COUNT(1)) AS STATU FROM DEV_PAS.T_LOCK_POLICY T 
			WHERE T.LOCK_SERVICE_ID IN('93','85') AND T.POLICY_CODE = #{policy_code}
  		]]>
	</select>
	
	<!-- 任务量试算 -->
	<select id="PA_extractionTaskQuantity" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(A.Policy_Code) from APP___PAs__DBUSER.T_CONTRACT_MASTER A,]]>
		<if test=" risk_score != null and risk_score != ''  "><![CDATA[APP___PAS__DBUSER.t_Contract_Busi_Prod D, ]]></if>
          APP___PAS__DBUSER.T_CONTRACT_AGENT E,
          APP___PAS__DBUSER.T_INSURED_LIST F,
          APP___PAS__DBUSER.T_CUSTOMER G
          where A.LIABILITY_STATE = '1'
          <if test=" risk_score != null and risk_score != ''  "><![CDATA[and D.Policy_Code = A.Policy_Code ]]></if>
          and E.Policy_Code = A.Policy_Code
          and F.Policy_Code = A.Policy_Code
          and F.Customer_Id = G.CUSTOMER_ID
          <if test=" start_date != null and start_date != ''  "><![CDATA[ and  A.VALIDATE_DATE >= #{start_date} ]]></if>
          <if test=" end_date != null and end_date != ''  "><![CDATA[ and  A.VALIDATE_DATE <= #{end_date} ]]></if>
  		  <if test=" is_claim != null and is_claim != ''  "><![CDATA[ and C.CASE_STATUS != '10'and C.CASE_STATUS != '20'and C.CASE_STATUS != '21' ]]></if>
  		  <if test=" certi_code != null and certi_code != ''  "><![CDATA[ and G.CUSTOMER_ID_CODE = #{certi_code} ]]></if>
  		  <if test=" risk_score != null and risk_score != ''  "><![CDATA[ and D.RISK_SCORE > #{risk_score} ]]></if>
  		  <if test=" agent_code != null and agent_code != ''  "><![CDATA[ and E.AGENT_CODE = #{agent_code} ]]></if>
  		  <if test=" organ_code != null and organ_code != ''  "><![CDATA[ and A.ORGAN_CODE like '${organ_code}%' ]]></if>
	</select>
	
	
	<!-- 抽取 -->
	<select id="PA_extractionTaskQuantitys" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select A.POLICY_CODE,A.ORGAN_CODE,F.CUSTOMER_ID,G.CUSTOMER_NAME,G.CUSTOMER_CERTI_CODE
			 from APP___PAs__DBUSER.T_CONTRACT_MASTER A,]]>
		<if test=" risk_score != null and risk_score != ''  "><![CDATA[APP___PAS__DBUSER.t_Contract_Busi_Prod D, ]]></if>
		<if test=" is_claim != null and is_claim != '' and is_claim !='1'  "><![CDATA[ APP___CLM__DBUSER.t_Contract_Master B,
          APP___CLM__DBUSER.t_claim_case C,]]></if>
          APP___PAS__DBUSER.T_CONTRACT_AGENT E,
          APP___PAS__DBUSER.T_INSURED_LIST F,
          <if test=" organ_code_area != null and organ_code_area != ''  "><![CDATA[APP___PAS__DBUSER.T_AGENT          TA,
       	  APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,]]></if>
          APP___PAS__DBUSER.T_CUSTOMER G
          where A.LIABILITY_STATE = '1'
          <if test=" risk_score != null and risk_score != ''  "><![CDATA[and D.Policy_Code = A.Policy_Code ]]></if>
          and E.Policy_Code = A.Policy_Code
          and F.Policy_Code = A.Policy_Code
          and F.Customer_Id = G.CUSTOMER_ID
          <if test=" start_date != null and start_date != ''  "><![CDATA[ and  A.VALIDATE_DATE >= #{start_date} ]]></if>
          <if test=" end_date != null and end_date != ''  "><![CDATA[ and  A.VALIDATE_DATE <= #{end_date} ]]></if>
  		  <if test=" is_claim != null and is_claim != '' and is_claim !='1'  "><![CDATA[ and  A.Policy_Code = B.Policy_Code 
          and c.case_id = b.case_id 
          and C.CASE_STATUS != '10'
          and C.CASE_STATUS != '20'
          and C.CASE_STATUS != '21' ]]></if>
  		  <if test=" certi_code != null and certi_code != ''  "><![CDATA[ and G.CUSTOMER_ID_CODE = #{certi_code} ]]></if>
  		  <if test=" risk_score != null and risk_score != ''  "><![CDATA[ and D.RISK_SCORE > #{risk_score} ]]></if>
  		  <if test=" agent_code != null and agent_code != ''  "><![CDATA[ and E.AGENT_CODE = #{agent_code} ]]></if>
  		  <if test=" organ_code != null and organ_code != ''  "><![CDATA[ and A.ORGAN_CODE like '${organ_code}%' ]]></if>
  		  <if test=" organ_code_area != null and organ_code_area != ''  "><![CDATA[ and TA.GROUP_CODE = #{organ_code_area}
  		  and  TA.AGENT_CODE = TCA.AGENT_CODE and  TCA.Policy_Code = A.Policy_Code ]]></if>
	</select>
	
	
	
	<!-- 抽取 -->
	<select id="PA_findQueryBFSurveysNo" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			select count(AA.POLICY_CODE) from (
			 select distinct A.POLICY_CODE,A.ORGAN_CODE,F.CUSTOMER_ID,G.CUSTOMER_NAME,G.CUSTOMER_CERTI_CODE from APP___PAS__DBUSER.T_CONTRACT_AGENT E,]]>
          APP___PAS__DBUSER.T_INSURED_LIST F,
          <if test=" organ_code_area != null and organ_code_area != ''  "><![CDATA[APP___PAS__DBUSER.T_AGENT          TA,
       	  APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,]]></if>
          APP___PAS__DBUSER.T_CUSTOMER G,
          APP___PAs__DBUSER.T_CONTRACT_MASTER A
          right join APP___PAS__DBUSER.t_Contract_Busi_Prod D ON  D.POLICY_CODE = A.Policy_Code
          <if test=" risk_lievel!=null ">
		 <![CDATA[right join  DEV_PAS.T_RISK_LEVEL_CONFIG T on T.IS_VALID = '1'   ]]></if>
          <if test=" is_claim!= null "><![CDATA[ left join APP___CLM__DBUSER.t_Contract_Master B on A.Policy_Code = B.Policy_Code
          left join APP___CLM__DBUSER.t_claim_case C on c.case_id = b.case_id and B.Cur_Flag = '1']]></if>
          where A.LIABILITY_STATE = '1'
          <if test=" risk_score != null and risk_score != ''  "><![CDATA[and D.Policy_Code = A.Policy_Code ]]></if>
          and E.Policy_Code = A.Policy_Code
          and F.Policy_Code = A.Policy_Code
          and F.Customer_Id = G.CUSTOMER_ID
          <if test=" start_date != null and start_date != ''  "><![CDATA[ and  A.VALIDATE_DATE >= #{start_date} ]]></if>
          <if test=" end_date != null and end_date != ''  "><![CDATA[ and  A.VALIDATE_DATE <= #{end_date} ]]></if>
  		  <if test=" is_claim == 0 "><![CDATA[ 
          and C.CASE_STATUS not in( '10','20','21') ]]></if>
          <if test=" is_claim == 1 "><![CDATA[ 
  		  	and (B.Log_Id is null
        	or C.CASE_STATUS in( '10','20','21')) ]]></if>
  		  <if test=" certi_code != null and certi_code != ''  "><![CDATA[ and G.CUSTOMER_ID_CODE = #{certi_code} ]]></if>
  		  <if test=" risk_score != null and risk_score != '' ">
  		  <![CDATA[ and D.RISK_SCORE > #{risk_score} ]]></if>
  		  <if test=" agent_code != null and agent_code != ''  "><![CDATA[ and E.AGENT_CODE = #{agent_code} ]]></if>
  		  <if test=" organ_code != null and organ_code != ''  "><![CDATA[ and A.ORGAN_CODE like '${organ_code}%' ]]></if>
  		  <if test=" organ_code_area != null and organ_code_area != ''  "><![CDATA[ and TA.GROUP_CODE = #{organ_code_area}
  		  and  TA.AGENT_CODE = TCA.AGENT_CODE and  TCA.Policy_Code = A.Policy_Code ]]></if>
  		  <if test=" risk_lievel!=null and risk_lievel == '0'.toString()   "><![CDATA[ AND T.RISK_SCORE_MAX > D.RISK_SCORE 
        and T.RISK_SCORE_MIN <= D.RISK_SCORE and T.IS_VALID = '1' AND D.POLICY_CODE = A.Policy_Code ]]></if>
  		  <if test=" risk_lievel!=null and risk_lievel == '1'.toString()  "><![CDATA[ AND T.RISK_SCORE_MAX_SECONDARY > D.RISK_SCORE 
        and T.RISK_SCORE_MIN_SECONDARY <= D.RISK_SCORE and T.IS_VALID = '1' AND D.POLICY_CODE = A.Policy_Code]]></if>
  		  <if test=" risk_lievel!=null and risk_lievel == '2'.toString() "><![CDATA[ AND T.RISK_SCORE_MAX_HIGH >= D.RISK_SCORE 
        and T.RISK_SCORE_MIN_HIGH <= D.RISK_SCORE and T.IS_VALID = '1' AND D.POLICY_CODE = A.Policy_Code ]]></if>
        <![CDATA[ )  AA]]> 
	</select>
	
	<!-- 微信续投保表单信息查询 -->
	<select id="PA_queryWechatRenewal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			   SELECT A.VALIDATE_DATE,
          (SELECT Z.MATURITY_DATE
             FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z
            WHERE Z.POLICY_ID = A.POLICY_ID
              AND Z.MASTER_BUSI_ITEM_ID IS NULL) EXPIRY_DATE,
          B.AGENT_CODE
     FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
          APP___PAS__DBUSER.T_CONTRACT_AGENT  B
    WHERE A.POLICY_CODE = B.POLICY_CODE
      AND B.IS_CURRENT_AGENT = '1' 
      AND A.POLICY_CODE = #{policy_code}
  		]]>
	</select>
	<select id="PA_queryAGPolicyContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select a.policy_code as ContNo, -- 保单号,
		a.policy_id as POLICY_ID,
		a.SUBMIT_CHANNEL as PlatForm ,  --投保平台             
       'Y' as IsNew, -- 是否为新核心保单
       to_char(a.validate_date, 'yyyy-mm-dd') as CvaliDate, -- 保单生效日期 
       to_char(a.EXPIRY_DATE, 'yyyy-mm-dd') as EXPIRY_DATE, --保单终止日期
       a.organ_code ContManageCom,
       (select z.organ_name
          from APP___PAS__DBUSER.t_Udmp_Org z
         where z.organ_code = a.organ_code) ContManageComName,
       (select ls.status_name
          from APP___PAS__DBUSER.t_liability_status ls
         where ls.status_code = a.liability_state) as ContState, -- 保单状态名称
       (case
         when a.liability_state = '4' then
          (select lc.cause_desc
             from APP___PAS__DBUSER.t_lapse_cause lc
            where lc.cause_code = a.lapse_cause)
         when a.liability_state = '3' then
          (select ec.cause_name
             from APP___PAS__DBUSER.t_end_cause ec
            where ec.cause_code = a.end_cause)
       end) as ContStateReason, -- 状态原因
       a.agent_org_id as ManageCom, -- 业务员所属管理机构
       (select q.organ_name
          from dev_pas.t_udmp_org q
         where q.organ_code = a.agent_org_id) as ManageComName,
       --   (select l.bank_name from dev_pas.t_bank l where l.bank_code = c.next_account_bank) as GetBankCode,  --  开户银行
       c.next_account_bank as GetBankCode, --  开户银行
       c.next_account as GetBankAccNo, --  交费账号    
       c.next_account_name,  --  户名
       (select g.bank_name
                   from app___pas__dbuser.t_payer_account f,
                        app___pas__dbuser.t_bank          g
                  where f.policy_id = a.policy_id
                    and f.next_account_bank = g.bank_code
                    and rownum = 1) as next_account_bank_name, --  开户行中文名称  
       (SELECT distinct DECODE(E.POLICY_CODE, AA.POLICY_CODE, '4001;') ||
                         DECODE(B.LOCK_SERVICE_ID, '93', '4002;') ||
                         (case
                            when 1 = (select 1
                                        from dev_cap.t_prem_arap x
                                       where x.policy_code = aa.policy_code
                                         and x.fee_status = '04'
                                         and rownum = 1) then
                             '4003;'
                            else
                             ''
                          end) || DECODE(B.LOCK_SERVICE_ID, '86', '4004;') ||
                         (case
                            when cp.IS_PAUSE = '1' and
                                 cp.PAUSE_DATE is not null then
                             '4005;'
                            else
                             ''
                          end) ||
                         DECODE((SELECT distinct 1
                                  FROM DEV_PAS.T_CUSTOMER
                                 WHERE CUSTOMER_ID IN
                                       (SELECT CUSTOMER_ID
                                          FROM DEV_PAS.T_INSURED_LIST
                                         WHERE POLICY_CODE = AA.POLICY_CODE)
                                   AND LIVE_STATUS = '2'),
                                '1',
                                '4005;') || (case
                                               when 0 =
                                                    (SELECT count(1)
                                                       FROM dev_pas.t_pay_plan b
                                                      WHERE b.policy_code = aa.policy_code) then
                                                '4006;'
                                               when (select count(*)
                                                              from (select pd.policy_code,
                                                                           k.begin_date
                                                                      from dev_pas.t_pay_plan k
                                                                      left join dev_pas.t_pay_due pd
                                                                        on k.plan_id = pd.plan_id
                                                                       and pd.fee_status = '00'
                                                                    where pd.policy_code = #{policy_code}
                                                                     group by pd.policy_code,
                                                                              k.begin_date
                                                                     ) cp
                                                             where  cp.policy_code = aa.policy_code) = 0 then
                                                '4006;'
                                               else
                                                ''
                                             end) ||
                        -- DECODE(CC.ACCOUNT_TYPE,'5','4007;')||  
                         (case
                            when 1 = (select 1
                                        from dev_pas.T_CONTRACT_BUSI_PROD p
                                       where p.policy_code = aa.policy_code
                                         and p.busi_item_id = cc.busi_item_id
                                         and p.MATURITY_DATE < sysdate
                                         and p.master_busi_item_id is null) and 
                                 CC.ACCOUNT_TYPE = '5' then
                             '4007;'
                            else
                             ''
                          end) || DECODE(AA.END_CAUSE, '06', '4008;') ||
                         DECODE(AA.END_CAUSE, '08', '4008;') ||
                         (case
                                   when cc.regular_repay = '0' and CC.ACCOUNT_TYPE = '4' then
                                    '4009;'
                                   else
                                    ''
                                 end)||
                         (case
                            when aa.end_cause = '03' then
                             '4010;'
                            else
                             ''
                          end) || (case
                                     when to_char(TRUNC(SYSDATE), 'yyyy-mm-dd') <
                                          to_char((select cp.begin_date
                                                    from (select count(1),
                                                                 k.policy_code,
                                                                 k.begin_date
                                                            from dev_pas.t_pay_plan k
                                                          
                                                           group by k.policy_code,
                                                                    k.begin_date
                                                          having(count(1)) > 0
                                                           order by k.begin_date asc) cp
                                                   where rownum = 1
                                                        
                                                     and cp.policy_code = aa.policy_code),
                                                  'yyyy-mm-dd') then
                                      '4011;'
                                     else
                                      ''
                                   end) || (case
                                              when cp.product_code in
                                                   ('650100',
                                                    '650200',
                                                    '650300',
                                                    '652000',
                                                    '655000',
                                                    '658000',
                                                    '666000',
                                                    '673000',
                                                    '674000',
                                                    '678000',
                                                    '680000') and
                                                   1 = (select 1
                                                          from dev_pas.t_policy_account g
                                                         where g.policy_id = aa.policy_id
                                                           and g.account_type in ('2', '11')
                                                           and g.policy_account_status = '1'
                                                           and rownum = 1) then
                                               '4012;'
                                              else
                                               ''
                                            end) ||
                         (case
                            when sysdate <
                                 (select tiup.PRICING_DATE
                                    from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD tcbp
                                   inner join APP___PAS__DBUSER.T_CONTRACT_INVEST tci
                                      on tcbp.BUSI_ITEM_ID = tci.BUSI_ITEM_ID
                                   inner join APP___PAS__DBUSER.T_INVEST_UNIT_PRICE tiup
                                      on tci.ACCOUNT_CODE =
                                         tiup.INVEST_ACCOUNT_CODE
                                   where tcbp.POLICY_CODE = aa.policy_code
                                     and tcbp.MATURITY_DATE < tiup.PRICING_DATE
                                     and rownum = 1) and
                                 cp.product_code = '892000' then
                             '4013;'
                            else
                             ''
                          end) || (case
                                     when 1 = (select 1
                                                 from dev_pas.T_POLICY_FREEZE f
                                                where f.policy_id = aa.policy_id
                                                  and f.unfreeze_date is null
                                                  and f.unfreeze_cause is null) then
                                      '4014;'
                                     else
                                      ''
                                   end) ||
                         DECODE(pa.ACKNOWLEDGE_DATE, '', '4015;')|| (
                           case 
                             when 1 = (
                                    select 1 
                                    from dev_pas.t_pay_plan tpl
                                    where 
                                    tpl.policy_code = #{policy_code}
                                    and tpl.liab_id = 1106 
                                    and tpl.busi_prod_code in ('********', '********')
                                    and EXISTS (
                                        select 1 from dev_pas.t_pay_due tpd where tpl.plan_id = tpd.plan_id and tpd.fee_status = '01'
                                    )
                                    and NOT EXISTS (
                                        select 1 from dev_pas.t_pay_due tpd where tpl.plan_id = tpd.plan_id and tpd.fee_status = '00'
                                    )
                             )
                             then
                                      '4016;'
                                     else
                                      ''
                             end)  || (
                    SELECT CASE
                           WHEN MAX(P.PAY_DUE_DATE) = MAX(ST.PAY_DUE_DATE) THEN
                            '4017;'
                           ELSE
                            ''
                         END
                    FROM APP___PAS__DBUSER.T_PAY_DUE P
                    LEFT JOIN APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK ST
                      ON P.POLICY_ID = ST.POLICY_ID
                     AND ST.DEDU_RESULT = 1
                   WHERE 1 = 1
                     AND P.PLAN_ID = ST.PLAN_ID
                     AND P.FEE_STATUS = '01'
                     AND P.POLICY_CODE = #{policy_code}
                     AND NOT EXISTS (SELECT 'X'
                            FROM DEV_PAS.T_PAY_DUE D
                           WHERE P.POLICY_CODE = D.POLICY_CODE
                             AND D.FEE_STATUS = '00')) as greycode
          FROM DEV_PAS.T_CONTRACT_MASTER AA
          LEFT JOIN DEV_PAS.T_LOCK_POLICY B
            ON AA.POLICY_ID = B.POLICY_ID
          LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT CC
            ON AA.POLICY_ID = CC.POLICY_ID
           AND CC.ACCOUNT_TYPE = '5'
           AND CC.REGULAR_REPAY = '0'
          LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT D
            ON D.POLICY_ID = AA.POLICY_ID
          LEFT JOIN DEV_PAS.T_POLICY_LOSE E
            ON E.POLICY_CODE = AA.POLICY_CODE
           AND E.UNLOSE_DATE IS NULL
          LEFT JOIN dev_pas.t_contract_product cp
            on cp.POLICY_CODE = AA.POLICY_CODE
          LEFT JOIN dev_pas.T_POLICY_ACKNOWLEDGEMENT pa
            on pa.POLICY_ID = AA.POLICY_ID
        
          WHERE 1 = 1
           and AA.policy_code = a.policy_code
           AND ROWNUM = 1
           ) as UnavailableCode,
           a.LIABILITY_STATE as LIABILITY_STATE,
           A.SPECIAL_ACCOUNT_FLAG as SPECIAL_ACCOUNT_FLAG,
           A.Multi_Mainrisk_Flag as MULTI_MAINRISK_FLAG,
           A.SUBMIT_CHANNEL as SUBMIT_CHANNEL  --递交渠道

  from APP___PAS__DBUSER.t_contract_master a,
       APP___PAS__DBUSER.t_payer_account   c
 where 1 = 1
   and a.policy_id = c.policy_id
   and sysdate >= a.validate_date
  and a.policy_code = #{policy_code}
  ]]> 
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND a.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND a.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND a.LIABILITY_STATE = #{liability_state} ]]></if>
   <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
      <![CDATA[AND NOT EXISTS
				 (SELECT 1
					FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
				   WHERE M.POLICY_CODE = CBP.POLICY_CODE
					 AND M.POLICY_CODE = a.POLICY_CODE
					 AND CBP.BUSI_PROD_CODE IN 
						 (SELECT CI.CONSTANTS_VALUE
							FROM DEV_PAS.T_CONSTANTS_INFO CI
						   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
   </if> 
	</select>
	
	<!-- 查询保单状态是否有效 -->
	<select id="PA_queryValidByContNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select a.liability_state from dev_pas.t_contract_master a 
			where a.liability_state != '1' and a.policy_code = #{policy_code}
  		]]>
	</select>
	
	<!-- 客户保单基本信息保单层信息查询sql -->
	<select id="PA_queryCusPlyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT 
	         TO_CHAR(TPA.ACKNOWLEDGE_DATE,'YYYY-MM-DD') SIGNDATE,
	         CASE WHEN (SELECT COUNT(*) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TPAS WHERE TPAS.POLICY_ID=TCM.POLICY_ID AND TPAS.REGULAR_REPAY=0)>0
	         	THEN 'Y'
	         	ELSE 'N' END LOANSTATE,
	         (SELECT DECODE(TA.AGENT_LEVEL,'A1','A1;三星级','A2','A2;四星级','A3','A3;五星级','') FROM APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,APP___PAS__DBUSER.T_AGENT TA 
	                 WHERE TCA.AGENT_CODE=TA.AGENT_CODE AND TCA.POLICY_CODE=TCM.POLICY_CODE AND TCA.IS_CURRENT_AGENT=1 AND ROWNUM=1) AGENTSTAR,
	         PA.NEXT_ACCOUNT ACCOUNT
	      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT TPA,APP___PAS__DBUSER.T_PAYER_ACCOUNT PA 
	      WHERE TCM.POLICY_ID = TPA.POLICY_ID AND TCM.POLICY_ID = PA.POLICY_ID AND TCM.POLICY_CODE  = #{policy_code}  
  		]]>
	</select>
	
	<!-- 心圆福职域保单列表查询-->
	<select id="PA_queryxyfzPolicyList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			         SELECT (SELECT Y.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,
               APP___PDS__DBUSER.T_BUSINESS_PRODUCT   Y
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND Z.BUSI_PRD_ID = Y.BUSINESS_PRD_ID
           AND Z.MASTER_BUSI_ITEM_ID IS NULL) CONTNAME,
       A.POLICY_CODE,
       (SELECT C.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,
               APP___PAS__DBUSER.T_BENEFIT_INSURED    D,
               APP___PAS__DBUSER.T_INSURED_LIST       B,
               APP___PAS__DBUSER.T_CUSTOMER           C
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND Z.BUSI_ITEM_ID = D.BUSI_ITEM_ID
           AND D.INSURED_ID = B.LIST_ID
           AND B.CUSTOMER_ID = C.CUSTOMER_ID
           AND B.POLICY_ID = A.POLICY_ID
           AND D.POLICY_ID = A.POLICY_ID
           AND D.ORDER_ID = 1
           AND Z.MASTER_BUSI_ITEM_ID IS NULL
           AND ROWNUM = 1) INSNAME,
       A.LIABILITY_STATE,
       A.VALIDATE_DATE,
       (SELECT Y.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER Z,
               APP___PAS__DBUSER.T_CUSTOMER      Y
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND Y.CUSTOMER_ID = Z.CUSTOMER_ID) APPNAME,
       (SELECT Z.MATURITY_DATE
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND Z.MASTER_BUSI_ITEM_ID IS NULL) MATURITY_DATE,
       (SELECT MAX(Y.COVERAGE_PERIOD)
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,
               APP___PAS__DBUSER.T_CONTRACT_PRODUCT   Y
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND Z.BUSI_ITEM_ID = Y.BUSI_ITEM_ID
           AND Z.MASTER_BUSI_ITEM_ID IS NULL) COVERAGE_PERIOD,
       (SELECT MAX(Y.COVERAGE_YEAR)
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,
               APP___PAS__DBUSER.T_CONTRACT_PRODUCT   Y
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND Z.BUSI_ITEM_ID = Y.BUSI_ITEM_ID
           AND Z.MASTER_BUSI_ITEM_ID IS NULL) COVERAGE_YEAR,
       A.ORGAN_CODE,
       A.SUBMIT_CHANNEL
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
 WHERE  a.submit_channel = 15 and 
 A.POLICY_CODE IN
  		]]>
  		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
       </foreach>
       <if test="  sale_agent_name != null and sale_agent_name != ''  ">
       <![CDATA[    AND EXISTS (SELECT 1
          FROM APP___PAS__DBUSER.T_INSURED_LIST T,
               APP___PAS__DBUSER.T_CUSTOMER     G
         WHERE T.POLICY_ID = A.POLICY_ID
           AND T.CUSTOMER_ID = G.CUSTOMER_ID
           AND G.CUSTOMER_NAME = #{sale_agent_name})       
       ]]></if>
	</select>
	
	<!-- 查询投被保人客户是否死亡 -->
	<select id="PA_queryCustmerDead" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			         SELECT  LIVE_STATUS FROM APP___PAS__DBUSER.T_CUSTOMER
                                 WHERE CUSTOMER_ID IN
                                       (SELECT CUSTOMER_ID
                                          FROM APP___PAS__DBUSER.T_POLICY_HOLDER
                                         WHERE POLICY_CODE = #{policy_code}
                                        UNION
                                        SELECT CUSTOMER_ID
                                          FROM APP___PAS__DBUSER.T_INSURED_LIST
                                         WHERE POLICY_CODE = #{policy_code}) AND LIVE_STATUS=2
  		]]>
	</select>
	
	<!-- 个人保单信息查询接口查询保单信息 -->
	<select id="PA_queryContractMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
  SELECT A.POLICY_ID,
         A.POLICY_CODE,
         A.APPLY_CODE,
         A.ORGAN_CODE,
         A.LIABILITY_STATE,
         A.BASIC_REMARK,
         A.POLICY_PWD,
         A.SUBMIT_CHANNEL,
         (SELECT TSC.CHANNEL_NAME FROM DEV_PAS.T_SUBMIT_CHANNEL TSC WHERE TSC.SUBMIT_CHANNEL = A.SUBMIT_CHANNEL)SUBMIT_CHANNEL_NAME,
         A.SUBINPUT_TYPE,
		 (SELECT TST.TYPE_DESC FROM DEV_PAS.T_SUBINPUT_TYPE TST WHERE TST.TYPE_CODE = A.SUBINPUT_TYPE)SUBINPUT_TYPE_NAME,	
         A.POLICY_TYPE,
         A.AGENCY_CODE,
         A.END_CAUSE,
         A.INPUT_TYPE,
         A.LAPSE_CAUSE,
         A.SUSPEND_CAUSE,
         A.SPECIAL_ACCOUNT_FLAG,
         E.FIXED_TEL,
         E.ADDRESS,
         D.CUSTOMER_ID,
         D.CUSTOMER_NAME,
         D.CUSTOMER_CERTI_CODE,
         D.CUSTOMER_GENDER,
         D.CUSTOMER_CERT_TYPE,
         D.CUST_CERT_STAR_DATE,
         D.CUST_CERT_END_DATE,
         D.CUSTOMER_HEIGHT,
         D.CUSTOMER_WEIGHT,
         D.COUNTRY_CODE,
         D.JOB_CODE,
         D.TAX_CODE,
         B.ACKNOWLEDGE_DATE,
         B.BRANCH_RECEIVE_DATE,
         (SELECT Z.AGENT_CODE
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.IS_CURRENT_AGENT = 1) AGENT_CODE,
         (SELECT Y.AGENT_NAME
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_AGENT          Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_CODE = Y.AGENT_CODE
             AND Z.IS_CURRENT_AGENT = 1) AGENT_NAME,
         (SELECT Y.AGENT_GENDER
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_AGENT          Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_CODE = Y.AGENT_CODE
             AND Z.IS_CURRENT_AGENT = 1) AGENT_GENDER,
         (SELECT Z.AGENT_ORGAN_CODE
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.IS_CURRENT_AGENT = 1) AGENT_ORGAN_CODE,
         (SELECT Y.AGENT_STATUS
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_AGENT          Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_CODE = Y.AGENT_CODE
             AND Z.IS_CURRENT_AGENT = 1) AGENT_STATUS,
         (SELECT Y.AGENT_MOBILE
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_AGENT          Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_CODE = Y.AGENT_CODE
             AND Z.IS_CURRENT_AGENT = 1) AGENT_MOBILE,
         (SELECT Z.AGENT_CODE
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.IS_NB_AGENT = 1) NB_AGENT_CODE,
         (SELECT Y.AGENT_NAME
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_AGENT          Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_CODE = Y.AGENT_CODE
             AND Z.IS_NB_AGENT = 1) NB_AGENT_NAME,
         (SELECT Y.AGENT_GENDER
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_AGENT          Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_CODE = Y.AGENT_CODE
             AND Z.IS_NB_AGENT = 1) NB_AGENT_GENDER,
         (SELECT Y.AGENT_STATUS
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_AGENT          Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_CODE = Y.AGENT_CODE
             AND Z.IS_NB_AGENT = 1) NB_AGENT_STATUS,
         (SELECT Y.SALES_CHANNEL_NAME
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_SALES_CHANNEL  Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.CHANNEL_TYPE = Y.SALES_CHANNEL_CODE
             AND Z.IS_NB_AGENT = 1) NB_AGENT_CHANNEL,
          (SELECT Z.CHANNEL_TYPE
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.IS_NB_AGENT = 1) CHANNEL_TYPE,
         (SELECT Y.SALES_ORGAN_NAME
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_SALES_ORGAN    Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_ORGAN_CODE = Y.SALES_ORGAN_CODE
             AND Z.IS_NB_AGENT = 1) NB_SALES_ORGAN_NAME,
         (SELECT Y.BRANCH_ATTR
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_SALES_ORGAN    Y
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.AGENT_ORGAN_CODE = Y.SALES_ORGAN_CODE
             AND Z.IS_CURRENT_AGENT = 1) BRANCH_ATTR,
         (SELECT Q.SALES_ORGAN_NAME || Y.SALES_ORGAN_NAME ||
                 X.SALES_ORGAN_NAME AS SALES_ORGAN_NAME
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z,
                 APP___PAS__DBUSER.T_SALES_ORGAN    X
            LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN Y
              ON X.PARENT_CODE = Y.SALES_ORGAN_CODE
            LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN Q
              ON Y.PARENT_CODE = Q.SALES_ORGAN_CODE
           WHERE 1 = 1
             AND Z.POLICY_ID = A.POLICY_ID
             AND Z.IS_CURRENT_AGENT = 1
             AND X.SALES_ORGAN_CODE = Z.AGENT_ORGAN_CODE) SALES_ORGAN_NAME,
         (SELECT Z.AGENT_ORGAN_CODE
            FROM APP___PAS__DBUSER.T_CONTRACT_AGENT Z
           WHERE Z.POLICY_ID = A.POLICY_ID
             AND Z.IS_NB_AGENT = 1) NB_AGENT_ORGAN_CODE,
             F.BUSI_ITEM_ID,
             F.OLD_POL_NO,
             (SELECT Z.PRODUCT_CODE_SYS FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z WHERE Z.BUSINESS_PRD_ID=F.BUSI_PRD_ID) PRODUCT_CODE_SYS,
             (SELECT Z.PRODUCT_NAME_STD FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z WHERE Z.BUSINESS_PRD_ID=F.BUSI_PRD_ID) PRODUCT_NAME_STD,
             (SELECT Z.ACC_RISK_SINGLE_INSURE_FLAG FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z WHERE Z.BUSINESS_PRD_ID=F.BUSI_PRD_ID) ACC_RISK_SINGLE_INSURE_FLAG,
             (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT Y WHERE Z.POLICY_CODE=A.POLICY_CODE 
             AND Z.MASTER_BUSI_ITEM_ID IS NOT NULL AND Z.BUSI_ITEM_ID =  #{busi_item_id} AND Y.BUSINESS_PRD_ID=Z.BUSI_PRD_ID AND Y.PRODUCT_CATEGORY1='20003') PRODUCT_CATEGORY1,
             F.HESITATION_PERIOD_DAY  HESITATION_PERIOD_DAY,
             (SELECT SUM(X.TRANS_AMOUNT)
                FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS X
               WHERE X.POLICY_ID = A.POLICY_ID
                 AND X.TRANS_CODE = '53') TRANS_AMOUNT,
             A.CHANNEL_TYPE,
             A.TRUST_BUSI_FLAG,
             (SELECT TC.COMPANY_NAME  FROM DEV_PAS.T_TRUST_COMPANY TC WHERE TC.CUSTOMER_ID = D.CUSTOMER_ID ) COMPANY_NAME
    FROM APP___PAS__DBUSER.T_CONTRACT_MASTER        A,
         APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT B,
         APP___PAS__DBUSER.T_POLICY_HOLDER          C,
         APP___PAS__DBUSER.T_CUSTOMER               D,
         APP___PAS__DBUSER.T_ADDRESS                E,
         APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD     F
   WHERE 1 = 1
     AND A.POLICY_ID = B.POLICY_ID
     AND A.POLICY_ID = C.POLICY_ID
     AND C.CUSTOMER_ID = D.CUSTOMER_ID
     AND E.ADDRESS_ID(+) = C.ADDRESS_ID
     AND F.POLICY_ID=A.POLICY_ID
     AND F.MASTER_BUSI_ITEM_ID IS NULL 
     AND A.POLICY_ID = #{policy_id} 
     and rownum = 1 ]]>
     <choose>
            <when test=" master_busi_item_id != null and master_busi_item_id != '' ">
                <![CDATA[ AND (F.BUSI_ITEM_ID = #{master_busi_item_id} OR F.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} )  ]]>
            </when>
            <otherwise>
            	<![CDATA[ AND (F.BUSI_ITEM_ID = #{busi_item_id}) ]]>
            </otherwise>
     </choose>

	</select>

	<!-- 承保保单统计查询 -->
	<select id="PA_underwritingPolicyStatistics" resultType="java.util.Map" parameterType="java.util.Map">
	     SELECT TCM.POLICY_CODE,
        TCM.CHANNEL_TYPE,
        TCM.Liability_State,
		TCM.INPUT_TYPE,
        TCM.DOUBLE_MAINRISK_FLAG,
        TCM.RELATION_POLICY_CODE,
        TCM.APPLY_DATE 
   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
        APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
        APP___PAS__DBUSER.T_AGENT           TA
  WHERE TCM.POLICY_ID = TCA.POLICY_ID
    AND TCA.IS_NB_AGENT = 1
    AND TCA.AGENT_CODE = TA.AGENT_CODE
    AND TA.AGENT_CHANNEL = '03'
    <if test="  service_bank != null and service_bank != ''  and  issue_date != null and issue_date != ''  ">
    <![CDATA[AND TCM.SERVICE_BANK = #{service_bank}
    		 AND TCM.ISSUE_DATE = #{issue_date}]]>
    </if>
    <if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ AND TCM.POLICY_CODE in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ AND TCM.SUBMIT_CHANNEL in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" input_type_list  != null and input_type_list">
			<![CDATA[ AND TCM.INPUT_TYPE in (]]>
			<foreach collection="input_type_list" item="input_type"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{input_type} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
    AND NOT EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER Z 
    WHERE Z.RELATION_POLICY_CODE=TCM.POLICY_CODE AND Z.DOUBLE_MAINRISK_FLAG=1)
	</select>
	<!-- 查询是否是年金险保单 -->
	<select id="PA_quereyProductCategory" resultType="java.util.Map" parameterType="java.util.Map">
	     SELECT TCM.POLICY_CODE, TBP.PRODUCT_CATEGORY2
   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
        APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
        APP___PDS__DBUSER.T_BUSINESS_PRODUCT   TBP
  WHERE TCM.POLICY_CODE = TCBP.POLICY_CODE
    AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
    AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
    AND TBP.PRODUCT_CATEGORY2 = '30004'
    AND TCM.POLICY_CODE = #{policy_code}
	</select>
	
	<!-- 根据保单号和业务员好查询保单基本信息   -->
	<select id="PA_findContractByPolicyCodeAndAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
		   <![CDATA[  select
		           tcm.policy_code,
		           tcm.validate_date,
		           tcm.liability_state,
		           tcm.policy_id,
		           tcm.organ_code,
		           (select tl.cause_desc from APP___PAS__DBUSER.t_lapse_cause tl where tl.cause_code = tcm.lapse_cause) lapse,
                   (select tc.cause_name from APP___PAS__DBUSER.t_end_cause tc where tc.cause_code = tcm.end_cause) end_cause,
		           (select ump.organ_name from APP___PAS__DBUSER.t_udmp_org ump where ump.organ_code = tcm.organ_code) organ_name,
		           (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = tca.AGENT_CODE) as agent_channel,
                   (SELECT CA.AGENT_LEVEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = tca.AGENT_CODE) as ca_agent_level,
                   (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = tca.AGENT_CODE) as tab_agent_level,
                   tcm.multi_mainrisk_flag,
                   tcm.special_account_flag,
                   tcm.SUBMIT_CHANNEL,
                   (select 1 from dev_pas.t_policy_account tpa where tpa.policy_id = tcm.policy_id 
                   			and tpa.account_type = 4 and tpa.regular_repay = 0 and rownum = 1) as regular_repay
		           from APP___PAS__DBUSER.t_contract_master tcm,
		      APP___PAS__DBUSER.t_contract_agent tca,
		      APP___PAS__DBUSER.t_customer tcu,
		      APP___PAS__DBUSER.t_policy_holder tph
		           where  tcm.policy_code = tph.policy_code
	               and tcm.policy_code = tca.policy_code
	               and tph.customer_id = tcu.customer_id
	               and tca.is_current_agent = '1' 
	            ]]>
	           <if test="policy_code!=null and policy_code!=''">
	             <![CDATA[  and tcm.policy_code = #{policy_code} ]]>
	           </if> 
	           <if test="name!=null and name!=''">
	              <![CDATA[ and tcu.customer_name = #{name}]]>
	           </if>
	           <if test="idNo!=null and idNo!=''">
	              <![CDATA[  and tcu.customer_certi_code = #{idNo}]]>
	           </if>
	           <if test="agent_code!=null and agent_code!=''">
	               <![CDATA[  and tca.agent_code = #{agent_code}]]>
	           </if>
	           <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">
	               <![CDATA[AND NOT EXISTS
							 (SELECT 1
								FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
							   WHERE M.POLICY_CODE = CBP.POLICY_CODE
								 AND M.POLICY_CODE = tcm.POLICY_CODE
								 AND CBP.BUSI_PROD_CODE IN 
									 (SELECT CI.CONSTANTS_VALUE
										FROM DEV_PAS.T_CONSTANTS_INFO CI
									   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
	           </if>      
   
	           
	</select>
	<!-- 根据保单号查询保单基本信息
	 //#118918随信通_新增贷款清偿保全项接口需求-保单管理
     //modify by cuiqi_wb
     //2022-07-8
	 -->
	<select id="PA_findLoanContractMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	              
	             select
	               tcm.policy_code,
		           tcm.validate_date,
		           tcm.liability_state,
		           tcm.organ_code,
		           tcm.policy_id,
		           tcm.expiry_date,
		           udmp.organ_name
		         from APP___PAS__DBUSER.t_contract_master tcm,APP___PAS__DBUSER.t_udmp_org udmp
		           where tcm.organ_code = udmp.organ_code
		           and tcm.policy_code = #{policy_code}
	</select>
	
	
		<!-- 根据保单号查询保单信息 -->
	<select id="PA_findMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">   
	   SELECT TCM.POLICY_CODE,
           TCM.VALIDATE_DATE,
           TCM.LIABILITY_STATE,
           (SELECT TCR.INSERT_TIME
              FROM APP___PAS__DBUSER.T_CONTRACT_RELATION TCR
             WHERE TCR.SUB_POLICY_CODE = TCM.POLICY_CODE) INSERT_TIME
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM   
     WHERE TCM.POLICY_CODE =#{policy_code}
	</select>
	<!-- 76630  追加保费-保单列表查询-->
    <sql id="PA_findByAgentCodeAddition">
    	<if test=" policy_code  != null "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
    	<if test=" customer_name  != null "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
    	<if test=" customer_id_code  != null "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_id_code} ]]></if>
    	<if test=" agent_code  != null "><![CDATA[ AND TT.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND TC.CUSTOMER_ID = #{customer_id} ]]></if>
    </sql>
   <select id="findContractMasterByAgentCodeAddition" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[   
			  SELECT TCM.POLICY_CODE, /*保单号*/
					 TCM.VALIDATE_DATE, /*保单生效日*/
					 TCM.LIABILITY_STATE, /*保单状态*/
					 TCM.APPLY_DATE,
					 TCM.ISSUE_DATE,/*保单承保日*/
					 TCM.IS_SELF_INSURED,/*是否自保件*/
					 TCM.IS_MUTUAL_INSURED,/*是否互保件*/
					 TCM.SPECIAL_ACCOUNT_FLAG,
					 TT.AGENT_CODE,
					 TT.AGENT_NAME,
					 TT.AGENT_MOBILE,
					 TCM.END_CAUSE,
					 (SELECT EC.CAUSE_NAME
							FROM APP___PAS__DBUSER.T_END_CAUSE EC
						 WHERE EC.CAUSE_CODE = TCM.END_CAUSE) AS end_cause_name,
					 (SELECT Z.CAUSE_DESC
							FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
						 WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
					 (SELECT TA.AGENT_ORGAN_CODE
							FROM APP___PAS__DBUSER.T_AGENT TA
						 WHERE TT.AGENT_CODE = TA.AGENT_CODE) AS AGENT_ORGAN_CODE, /*业务员所属管理机构*/
					 (SELECT T.ORGAN_NAME
							FROM APP___PAS__DBUSER.T_UDMP_ORG T, APP___PAS__DBUSER.T_AGENT TA
						 WHERE TT.AGENT_CODE = TA.AGENT_CODE
							 AND TA.AGENT_ORGAN_CODE = T.ORGAN_CODE) AS AGENT_ORGAN_NAME, /*业务员所属管理机构名称*/
					 TCM.ORGAN_CODE, /*保单管理机构代码*/
					 TPAT.NEXT_ACCOUNT,
					 TPAT.NEXT_ACCOUNT_NAME,
					 (select T.BANK_NAME from DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK )NEXT_ACCOUNT_BANK,
					 TPAT.ACCOUNT_BANK ACCOUNT_ID,
					 TPAT.NEXT_ACCOUNT_BANK NEXT_ACCOUNT_ID,
					 TPAT.ACCOUNT,
					 TPAT.ACCOUNT_NAME,
					 (select T.BANK_NAME from DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPAT.ACCOUNT_BANK )ACCOUNT_BANK,
					 (SELECT T.ORGAN_NAME
							FROM APP___PAS__DBUSER.T_UDMP_ORG T
						 WHERE TCM.ORGAN_CODE = T.ORGAN_CODE) ORGAN_NAME, /*保单管理机构名称*/
					 TCM.Multi_Mainrisk_Flag,
					 TCM.POLICY_ID,
					 TCM.SUBMIT_CHANNEL
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
			     DEV_PAS.T_PAYER_ACCOUNT TPAT,
					 APP___PAS__DBUSER.T_CONTRACT_AGENT  TT,
					 APP___PAS__DBUSER.T_POLICY_HOLDER   TB,
					 APP___PAS__DBUSER.T_CUSTOMER        TC
		 WHERE TCM.POLICY_ID = TT.POLICY_ID
		   	 AND TCM.POLICY_ID = TPAT.POLICY_ID
			 AND TCM.POLICY_ID = TB.POLICY_ID
			 AND TB.CUSTOMER_ID = TC.CUSTOMER_ID
			 AND TT.IS_CURRENT_AGENT = 1
			 AND TCM.VALIDATE_DATE < SYSDATE 
		]]>
		<include refid="PA_findByAgentCodeAddition" />
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = TCM.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		</if>
   </select>
   <!--技术需求任务#79549保单贷款续贷列表查询:P00001900382使用  -->
   <select id="queryPolicyCodeLoanInfos" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[ 
 select distinct C.POLICY_CODE, C.POLICY_ID
  from APP___PAS__DBUSER.t_policy_account  A,
       APP___PAS__DBUSER.t_policy_holder   B,
       APP___PAS__DBUSER.t_contract_master C
      
 WHERE A.POLICY_ID = B.POLICY_ID
   AND B.POLICY_ID = C.POLICY_ID
   AND A.ACCOUNT_TYPE=4
   AND A.CAPITAL_BALANCE <> 0
   AND A.REGULAR_REPAY=0
   AND B.CUSTOMER_ID =#{customer_id}]]>
   
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND C.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND C.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND C.LIABILITY_STATE = #{liability_state} ]]></if>
  <![CDATA[ 
   AND NOT EXISTS
 (select 1
          from APP___PAS__DBUSER.t_lock_policy E, APP___PAS__DBUSER.T_LOCK_SET_CONFIG F
         where E.LOCK_SERVICE_ID = F.LOCK_SERVICE_ID
           AND A.POLICY_ID = E.POLICY_ID
           AND F.SERVICE_CODE =#{service_code})
     ]]>
   </select>
   
   <!-- 查询投保人客户是否死亡 -->
	<select id="PA_queryTBRCustmerDead" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			         SELECT  LIVE_STATUS FROM APP___PAS__DBUSER.T_CUSTOMER
                                 WHERE CUSTOMER_ID IN
                                       (SELECT CUSTOMER_ID
                                          FROM APP___PAS__DBUSER.T_POLICY_HOLDER
                                         WHERE POLICY_CODE = #{policy_code}) AND LIVE_STATUS=2
  		]]>
	</select>
	
	
	<!-- 查询被保人客户是否死亡 -->
	<select id="PA_queryBBRCustmerDead" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			         SELECT  LIVE_STATUS FROM APP___PAS__DBUSER.T_CUSTOMER
                                 WHERE CUSTOMER_ID IN
                                       (
                                        SELECT CUSTOMER_ID
                                          FROM APP___PAS__DBUSER.T_INSURED_LIST
                                         WHERE POLICY_CODE = #{policy_code}) AND LIVE_STATUS=2
  		]]>
	</select>
	
	<!-- 查询被保人客户是否存在未死亡 -->
	<select id="PA_queryBBRCustmerNoDead" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			         SELECT  LIVE_STATUS,CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER
                                 WHERE CUSTOMER_ID IN
                                       (
                                        SELECT CUSTOMER_ID
                                          FROM APP___PAS__DBUSER.T_INSURED_LIST
                                         WHERE POLICY_CODE = #{policy_code}) AND (LIVE_STATUS!=2 OR LIVE_STATUS IS NULL)
  		]]>
	</select>
	<!-- 根据保单号和险种查询保单信息 -->
	<select id="queryContractMasterByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		SELECT TCM.POLICY_CODE,TCBP.BUSI_PROD_CODE,TCBP.BUSI_ITEM_ID
          FROM DEV_PAS.T_CONTRACT_BUSI_PROD                 TCBP,
			   DEV_PAS.T_CONTRACT_MASTER                    TCM
		  where TCM.POLICY_CODE = TCBP.POLICY_CODE
		  	and	TCM.POLICY_CODE = #{policy_code} and TCBP.BUSI_PROD_CODE = #{busi_prod_code}
	]]>
	</select>
	
	<!-- 根据保单号和险种查询保单信息 -->
	<select id="queryBBRCustmerDeadBy462" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT  LIVE_STATUS FROM APP___PAS__DBUSER.T_CUSTOMER
                                 WHERE CUSTOMER_ID IN
                                       (
                                        SELECT CUSTOMER_ID
                 FROM APP___PAS__DBUSER.T_INSURED_LIST 
                 WHERE list_id = (select insured_id
                                 from dev_pas.t_benefit_insured tb
                                where tb.policy_code = #{policy_code}
                                and tb.order_id = '1'
                                and tb.busi_item_id = #{busi_item_id}
             )) AND LIVE_STATUS=2
		]]>
	</select>
	<!-- 根据保单号查询保单下462险种第一被保人是否未死亡 -->
	<select id="queryBBRCustmerNoDeadBy462" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT  LIVE_STATUS FROM APP___PAS__DBUSER.T_CUSTOMER
                                 WHERE CUSTOMER_ID IN
                                       (
                                        SELECT CUSTOMER_ID
                 FROM APP___PAS__DBUSER.T_INSURED_LIST 
                 WHERE list_id = (select insured_id
                                 from dev_pas.t_benefit_insured tb
                                where tb.policy_code = #{policy_code}
                                and tb.order_id = '1'
                                and tb.busi_item_id = #{busi_item_id}
             )) AND (LIVE_STATUS!=2 OR LIVE_STATUS IS NULL)
		]]>
	</select>
	<!-- 根据保单号和险种查询保单信息 -->
	<select id="selectOgran" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select SALES_ORGAN_CODE,SALES_ORGAN_NAME 
		from APP___pas__DBUSER.T_SALES_ORGAN  
		where 1=1 ]]>
		<if test=" ORGAN_LEVEL_CODE != null and ORGAN_LEVEL_CODE != ''  "><![CDATA[ AND ORGAN_LEVEL_CODE = #{ORGAN_LEVEL_CODE} ]]></if>
		<if test=" PARENT_CODE != null and PARENT_CODE != ''  "><![CDATA[ AND A.PARENT_CODE = #{PARENT_CODE} ]]></if>
		
	</select>
	
	<!-- 查询保单处于“保单质押第三方止付”状态 -->
	<select id="PA_queryPolicyZJZF" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			        SELECT 
	(select z.lock_service_id from app___pas__dbuser.t_lock_policy z where z.policy_id=TCM.policy_id and z.lock_service_id = 86 and rownum = 1) as lock_service_id
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM WHERE TCM.POLICY_CODE = #{policy_code}
  		]]>
	</select>
	
	<select id="PA_queryAdditionalInsurance" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
				SELECT CBP.LIABILITY_STATE CBP_STATE,    
			       CBP.MASTER_BUSI_ITEM_ID,  
			       CBP.BUSI_ITEM_ID,         
			       PRO.PRODUCT_CODE_STD,     
			       PRO.COVER_PERIOD_TYPE,    
			       A.LIABILITY_STATE A_STATE,        
			       CBP.POLICY_CODE,          
			       PRO.PRODUCT_CATEGORY,     
			       PRO.PRODUCT_ABBR_NAME    
			    FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
			         APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
			         APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO
			    WHERE 1 = 1
			        AND CBP.POLICY_CODE = A.POLICY_CODE
			        AND PRO.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
			        AND PRO.PRODUCT_CATEGORY = '10002'
			        AND CBP.POLICY_CODE = #{policy_code}
			        AND CBP.MASTER_BUSI_ITEM_ID = #{busi_item_id}    
		 ]]>
	</select>
	
	<!-- 查询免填单标识 -->
	<select id="PA_findContactMasterByNobillFlag" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			select A.CUSTOMER_ID,c.* from dev_pas.t_cs_accept_change c
				left join dev_pas.T_CS_APPLICATION a
				on a.change_id = c.change_id
				where c.service_code = 'RR'
				AND A.NOFILL_FLAG = '1'
				and a.customer_id in (SELECT A.CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER A WHERE A.POLICY_CODE = #{policy_code}) 
			UNION
				select A.CUSTOMER_ID,c.* from dev_pas.t_cs_accept_change c
				left join dev_pas.T_CS_APPLICATION a
				on a.change_id = c.change_id
				where c.service_code = 'RR'
				AND A.NOFILL_FLAG = '1'
				and a.customer_id in (SELECT B.CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST B WHERE B.POLICY_CODE = #{policy_code})  
		 ]]>
	</select>
	<!-- 复效查询接口 保单状态随附加险849状态 -->
	<select id="PA_queryPolicyContractMaster849F" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
    SELECT A.POLICY_CODE,
                A.POLICY_ID,
                A.ORGAN_CODE,
                A.VALIDATE_DATE,
                D.LIABILITY_STATE,
                D.END_CAUSE,
                D.LAPSE_CAUSE,
                B.ACKNOWLEDGE_DATE,
                D.LAPSE_DATE,
                (CASE
                  WHEN D.LAPSE_CAUSE = '6' THEN
                   'Y'
                  ELSE
                   'N'
                END) AS ENDCAUSE,
                (SELECT LS.STATUS_NAME
                   FROM APP___PAS__DBUSER.T_LIABILITY_STATUS LS
                  WHERE LS.STATUS_CODE = D.LIABILITY_STATE) AS POLICYSTATUS,
                (CASE
                  WHEN D.LIABILITY_STATE = '4' THEN
                   (SELECT LC.CAUSE_DESC
                      FROM APP___PAS__DBUSER.T_LAPSE_CAUSE LC
                     WHERE LC.CAUSE_CODE = D.LAPSE_CAUSE)
                  WHEN A.LIABILITY_STATE = '3' THEN
                   (SELECT EC.CAUSE_NAME
                      FROM APP___PAS__DBUSER.T_END_CAUSE EC
                     WHERE EC.CAUSE_CODE = D.END_CAUSE)
                END) AS STATUSMESSAGE,
                A.AGENT_ORG_ID,
                C.CUSTOMER_ID AS INSURED_FAMILY,
                (SELECT MAX(Y.IS_PAUSE) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Y 
                WHERE D.BUSI_ITEM_ID=Y.BUSI_ITEM_ID 
                AND Y.IS_PAUSE=1 AND Y.PAUSE_DATE IS NOT NULL) IS_PAUSE
           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER        A,
                APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT B,
                APP___PAS__DBUSER.T_POLICY_HOLDER          C,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD D
          WHERE A.POLICY_ID = B.POLICY_ID
            AND A.POLICY_ID = C.POLICY_ID
            AND A.POLICY_ID = D.POLICY_ID
            AND D.MASTER_BUSI_ITEM_ID IS NOT NULL
            AND D.BUSI_PROD_CODE in (SELECT T.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT T WHERE T.ACC_RISK_SINGLE_INSURE_FLAG='1')
            AND A.POLICY_CODE=#{policy_code}
            AND ROWNUM=1
  ]]>
		
	</select>
	
	<!-- 复效查询接口 保单状态随主险状态 -->
	<select id="PA_queryPolicyContractMaster849Z" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
    SELECT A.POLICY_CODE,
                A.POLICY_ID,
                A.ORGAN_CODE,
                A.VALIDATE_DATE,
                D.LIABILITY_STATE,
                D.END_CAUSE,
                D.LAPSE_CAUSE,
                B.ACKNOWLEDGE_DATE,
                D.LAPSE_DATE,
                (CASE
                  WHEN D.LAPSE_CAUSE = '6' THEN
                   'Y'
                  ELSE
                   'N'
                END) AS ENDCAUSE,
                (SELECT LS.STATUS_NAME
                   FROM APP___PAS__DBUSER.T_LIABILITY_STATUS LS
                  WHERE LS.STATUS_CODE = D.LIABILITY_STATE) AS POLICYSTATUS,
                (CASE
                  WHEN D.LIABILITY_STATE = '4' THEN
                   (SELECT LC.CAUSE_DESC
                      FROM APP___PAS__DBUSER.T_LAPSE_CAUSE LC
                     WHERE LC.CAUSE_CODE = D.LAPSE_CAUSE)
                  WHEN A.LIABILITY_STATE = '3' THEN
                   (SELECT EC.CAUSE_NAME
                      FROM APP___PAS__DBUSER.T_END_CAUSE EC
                     WHERE EC.CAUSE_CODE = D.END_CAUSE)
                END) AS STATUSMESSAGE,
                A.AGENT_ORG_ID,
                C.CUSTOMER_ID AS INSURED_FAMILY,
                (SELECT MAX(Y.IS_PAUSE) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Y 
                WHERE D.BUSI_ITEM_ID=Y.BUSI_ITEM_ID 
                AND Y.IS_PAUSE=1 AND Y.PAUSE_DATE IS NOT NULL) IS_PAUSE,
                A.SUBMIT_CHANNEL
           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER        A,
                APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT B,
                APP___PAS__DBUSER.T_POLICY_HOLDER          C,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD D
          WHERE A.POLICY_ID = B.POLICY_ID
            AND A.POLICY_ID = C.POLICY_ID
            AND A.POLICY_ID = D.POLICY_ID
            AND D.MASTER_BUSI_ITEM_ID IS NULL
            AND A.POLICY_CODE=#{policy_code}
            AND ROWNUM=1
  ]]>
	
	</select>
	
		<!-- 保单信息列表查询接口  附加849 保单状态根据主险状态-->
	<select id="PA_queryPolicyListWithMain849Z" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
    SELECT A.POLICY_CODE,
           A.ORGAN_CODE,
           A.VALIDATE_DATE,
           E.LIABILITY_STATE,
           A.SPECIAL_ACCOUNT_FLAG,
           (SELECT Z.STATUS_NAME
              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
             WHERE Z.STATUS_CODE = E.LIABILITY_STATE) AS LIABILITY_NAME,
           E.END_CAUSE,
           (SELECT Z.CAUSE_NAME
           FROM APP___PAS__DBUSER.T_END_CAUSE Z
           WHERE Z.CAUSE_CODE = E.END_CAUSE) AS END_CAUSE_NAME,
           E.LAPSE_CAUSE,
           (SELECT Z.CAUSE_DESC
           FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
           WHERE Z.CAUSE_CODE = E.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
           C.CUSTOMER_NAME,
           C.CUSTOMER_BIRTHDAY,
           C.CUSTOMER_CERT_TYPE,
           C.CUSTOMER_CERTI_CODE,
           C.CUSTOMER_GENDER,
           C.OLD_CUSTOMER_ID,
           C.CUSTOMER_ID,
           C.CUSTOMER_RISK_LEVEL,
           A.POLICY_ID,
           (SELECT Z.AGENT_ORGAN_CODE
              FROM APP___PAS__DBUSER.T_AGENT Z
             WHERE Z.AGENT_CODE = D.AGENT_CODE
               AND ROWNUM = 1) AS AGENT_ORGAN_CODE,
           (SELECT Y.ORGAN_NAME
              FROM APP___PAS__DBUSER.T_UDMP_ORG Y,
                   APP___PAS__DBUSER.T_AGENT    Z
             WHERE Y.ORGAN_CODE = Z.AGENT_ORGAN_CODE
               AND Z.AGENT_CODE = D.AGENT_CODE) AS ORGAN_NAME,
               (SELECT (SELECT Y.NAME
                  FROM APP___PAS__DBUSER.T_PAY_MODE Y
                 WHERE Y.CODE = Z.PAY_NEXT)
          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND ROWNUM = 1) AS PAY_LOCATION_NAME,
             (SELECT (SELECT Y.BANK_NAME
              FROM APP___PAS__DBUSER.T_BANK Y
             WHERE Y.BANK_CODE = Z.NEXT_ACCOUNT_BANK)
             FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
             WHERE Z.POLICY_ID =A.POLICY_ID AND ROWNUM = 1) AS BANK_NAME,
     (SELECT Z.NEXT_ACCOUNT
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT,
     (SELECT Z.NEXT_ACCOUNT_BANK
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT_BANK,
     (SELECT Z.NEXT_ACCOUNT_NAME
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT_NAME,
     A.SUBMIT_CHANNEL
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
           APP___PAS__DBUSER.T_POLICY_HOLDER   B,
           APP___PAS__DBUSER.T_CUSTOMER        C,
           APP___PAS__DBUSER.T_CONTRACT_AGENT  D,
           APP___PAS__DBUSER.t_Contract_Busi_Prod E
     WHERE A.POLICY_CODE = B.POLICY_CODE
       AND A.POLICY_CODE = D.POLICY_CODE
       AND A.POLICY_CODE = E.POLICY_CODE
       AND E.MASTER_BUSI_ITEM_ID IS NULL
       AND D.IS_CURRENT_AGENT = 1
       AND B.CUSTOMER_ID = C.CUSTOMER_ID
       AND A.LIABILITY_STATE <> '0'
       AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
	</select>
	
		<!-- 保单信息列表查询接口  附加849 保单状态根据主险状态-->
	<select id="PA_queryPolicyListWithMain849F" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
    SELECT A.POLICY_CODE,
           A.ORGAN_CODE,
           A.VALIDATE_DATE,
           E.LIABILITY_STATE,
           A.SPECIAL_ACCOUNT_FLAG,
           (SELECT Z.STATUS_NAME
              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
             WHERE Z.STATUS_CODE = E.LIABILITY_STATE) AS LIABILITY_NAME,
           E.END_CAUSE,
           (SELECT Z.CAUSE_NAME
           FROM APP___PAS__DBUSER.T_END_CAUSE Z
           WHERE Z.CAUSE_CODE = E.END_CAUSE) AS END_CAUSE_NAME,
           E.LAPSE_CAUSE,
           (SELECT Z.CAUSE_DESC
           FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
           WHERE Z.CAUSE_CODE = E.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
           C.CUSTOMER_NAME,
           C.CUSTOMER_BIRTHDAY,
           C.CUSTOMER_CERT_TYPE,
           C.CUSTOMER_CERTI_CODE,
           C.CUSTOMER_GENDER,
           C.OLD_CUSTOMER_ID,
           C.CUSTOMER_ID,
           C.CUSTOMER_RISK_LEVEL,
           A.POLICY_ID,
           (SELECT Z.AGENT_ORGAN_CODE
              FROM APP___PAS__DBUSER.T_AGENT Z
             WHERE Z.AGENT_CODE = D.AGENT_CODE
               AND ROWNUM = 1) AS AGENT_ORGAN_CODE,
           (SELECT Y.ORGAN_NAME
              FROM APP___PAS__DBUSER.T_UDMP_ORG Y,
                   APP___PAS__DBUSER.T_AGENT    Z
             WHERE Y.ORGAN_CODE = Z.AGENT_ORGAN_CODE
               AND Z.AGENT_CODE = D.AGENT_CODE) AS ORGAN_NAME,
               (SELECT (SELECT Y.NAME
                  FROM APP___PAS__DBUSER.T_PAY_MODE Y
                 WHERE Y.CODE = Z.PAY_NEXT)
          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND ROWNUM = 1) AS PAY_LOCATION_NAME,
             (SELECT (SELECT Y.BANK_NAME
              FROM APP___PAS__DBUSER.T_BANK Y
             WHERE Y.BANK_CODE = Z.NEXT_ACCOUNT_BANK)
             FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
             WHERE Z.POLICY_ID =A.POLICY_ID AND ROWNUM = 1) AS BANK_NAME,
     (SELECT Z.NEXT_ACCOUNT
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT,
      (SELECT Z.NEXT_ACCOUNT_BANK
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT_BANK,
     (SELECT Z.NEXT_ACCOUNT_NAME
      FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT Z
     WHERE Z.POLICY_ID = A.POLICY_ID AND ROWNUM = 1) AS NEXT_ACCOUNT_NAME,
                A.SUBMIT_CHANNEL 
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
           APP___PAS__DBUSER.T_POLICY_HOLDER   B,
           APP___PAS__DBUSER.T_CUSTOMER        C,
           APP___PAS__DBUSER.T_CONTRACT_AGENT  D,
           APP___PAS__DBUSER.t_Contract_Busi_Prod E
     WHERE A.POLICY_CODE = B.POLICY_CODE
       AND A.POLICY_CODE = D.POLICY_CODE
       AND A.POLICY_CODE = E.POLICY_CODE
       AND E.MASTER_BUSI_ITEM_ID IS NOT NULL
       AND E.BUSI_PROD_CODE in (SELECT T.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT T WHERE T.ACC_RISK_SINGLE_INSURE_FLAG='1')
       AND D.IS_CURRENT_AGENT = 1
       AND B.CUSTOMER_ID = C.CUSTOMER_ID
       AND A.LIABILITY_STATE <> '0'
       AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
	</select>
	<!-- 查询置灰规则 -->
	<select id="PA_findAshInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
                 SELECT 
                       TCM.POLICY_CODE,
                       D.BRANCH_RECEIVE_DATE,
                       TCBP.HESITATION_PERIOD_DAY,
                       TCBP.VALIDATE_DATE,
                       (CASE
                            WHEN (TCBP.VALIDATE_DATE + TCBP.HESITATION_PERIOD_DAY) < SYSDATE THEN
                            '1'
                            ELSE
                            '0'
                  END) HHH_FLG
                  FROM 
                      DEV_PAS.T_CONTRACT_MASTER TCM
                  INNER JOIN 
                      DEV_PAS.T_POLICY_ACKNOWLEDGEMENT D
                  ON 
                      TCM.POLICY_ID = D.POLICY_ID
                  INNER JOIN 
                      DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                  ON 
                      TCM.POLICY_CODE = TCBP.POLICY_CODE
                  AND 
                      TCBP.MASTER_BUSI_ITEM_ID IS NULL
                  WHERE 
                      TCM.POLICY_ID  = #{policy_id}  
		 ]]>
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND TCM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND  TCM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND TCM.LIABILITY_STATE = #{liability_state} ]]></if>
	</select>
	<!-- 查询保单豁免状态 -->
	<select id="PA_findAbortStatusInfoByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			select A.ITEM_ID,
			       A.PRODUCT_ID,
			       A.IS_MASTER_ITEM,
			       A.POLICY_CODE,
			       A.POLICY_ID,
			       A.WAIVER_START
			  from DEV_PAS.T_CONTRACT_PRODUCT A
			 WHERE A.BUSI_ITEM_ID = #{busi_item_id}
               AND T.POLICY_ID = #{policy_id}
               AND A.WAIVER_START IS NOT NULL;  
		 ]]>
	</select>
	<!-- 查询保单解约终止状态 -->
	<select id="PA_findTerminationStatusInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT T.POLICY_ID,
			       T.POLICY_CODE,
			       T.APPLY_CODE,
			       T.LIABILITY_STATE,
			       T.END_CAUSE
			  FROM DEV_PAS.T_CONTRACT_MASTER T
			 WHERE 1=1
			   AND T.LIABILITY_STATE = '3'
			   AND T.END_CAUSE = '05'
               AND T.POLICY_ID = #{policy_id}  
		 ]]>
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND T.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND  T.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND T.LIABILITY_STATE = #{liability_state} ]]></if>
	</select>
	<!-- 查询该保单附加险信息 -->
	<select id="PA_findAdditionalInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT CBP.LIABILITY_STATE cpb_state,    
             CBP.MASTER_BUSI_ITEM_ID,  
             CBP.BUSI_ITEM_ID,         
             PRO.PRODUCT_CODE_STD,     
             PRO.COVER_PERIOD_TYPE,    
             A.LIABILITY_STATE A_STATE,        
             CBP.POLICY_CODE,
             CBP.POLICY_ID,         
             PRO.PRODUCT_CATEGORY,     
             PRO.PRODUCT_ABBR_NAME,
             CBP.Hesitation2acc,
             CBP.HESITATION_PERIOD_DAY,
             CBP.IS_RPU,
             CBP.VALIDATE_DATE,
             CBP.BUSI_PRD_ID     
          FROM DEV_PAS.T_CONTRACT_MASTER A,
               DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
               DEV_PAS.T_BUSINESS_PRODUCT PRO
          WHERE 1 = 1
              AND CBP.POLICY_CODE = A.POLICY_CODE
              AND PRO.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID            
              AND PRO.PRODUCT_CATEGORY = '10002'
              AND A.POLICY_ID = #{policy_id}  
		 ]]>
	<if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND A.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND A.VALIDATE_DATE <= #{suspend_date}  ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND A.LIABILITY_STATE = #{liability_state} ]]></if>
	</select>
	
	<!-- 查询该保单效力状态信息-->
	<select id="PA_findLiabilityStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
					SELECT T.POLICY_ID,
					       T.POLICY_CODE,
					       T.APPLY_CODE,
					       T.LIABILITY_STATE,
					       T.END_CAUSE,
					       T.LAPSE_CAUSE
					FROM DEV_PAS.T_CONTRACT_MASTER T
					WHERE 1 = 1
                    AND T.POLICY_ID = #{policy_id}  
		 ]]>
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND T.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND  T.VALIDATE_DATE <= #{suspend_date}  ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND T.LIABILITY_STATE = #{liability_state} ]]></if>
	</select>
	
	<!-- 查询该保单主险信息 -->
	<select id="PA_findBusiByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT CBP.LIABILITY_STATE cpb_state,    
             CBP.MASTER_BUSI_ITEM_ID,  
             CBP.BUSI_ITEM_ID,         
             PRO.PRODUCT_CODE_STD,     
             PRO.COVER_PERIOD_TYPE,    
             A.LIABILITY_STATE A_STATE,        
             CBP.POLICY_CODE,
             CBP.POLICY_ID,         
             PRO.PRODUCT_CATEGORY,     
             PRO.PRODUCT_ABBR_NAME,
             CBP.Hesitation2acc,
             CBP.HESITATION_PERIOD_DAY,
             CBP.IS_RPU,
             CBP.VALIDATE_DATE,
             CBP.BUSI_PRD_ID     
          FROM DEV_PAS.T_CONTRACT_MASTER A,
               DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
               DEV_PAS.T_BUSINESS_PRODUCT PRO
          WHERE 1 = 1
              AND CBP.POLICY_CODE = A.POLICY_CODE
              AND PRO.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID            
              AND PRO.PRODUCT_CATEGORY = '10001'
              AND A.POLICY_ID = #{policy_id}  
		 ]]>
	</select>
		<!-- 查询银代保单信息 -->
	<select id="PA_findYinDaiContractMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT 
			  TCM.ORGAN_CODE,TCM.POLICY_ID,TCM.POLICY_CODE
            FROM 
              DEV_PAS.T_CONTRACT_MASTER TCM
            WHERE 1=1 
              AND TCM.SUBMIT_CHANNEL = '1'
              AND (TCM.END_CAUSE != '80' or  TCM.END_CAUSE is null)
              AND TCM.POLICY_CODE  = #{policy_code}  
		 ]]>
	</select>
		<!-- 查询银代受益人保单信息 -->
	<select id="PA_queryYindaiBenefitContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT 
			  (SELECT COUNT(1)
               FROM DEV_PAS.T_CONTRACT_BENE TCB
               WHERE TCB.POLICY_CODE = TCM.POLICY_CODE
               AND TCB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
               AND TCB.BENE_TYPE = 1) BENE_COUNT
            FROM 
               DEV_PAS.T_CONTRACT_MASTER TCM, DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
            WHERE 1 = 1
               AND TCM.POLICY_CODE = TCBP.POLICY_CODE
               AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
               AND TCM.POLICY_CODE  = #{policy_code}  
		 ]]>
	</select>
	
	<!-- 查询保单贷款保单信息 -->
	<select id="PA_queryLNPolicyListYDBQ" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
		 	SELECT T.POLICY_ID, T.POLICY_CODE
        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T,
             APP___PAS__DBUSER.T_POLICY_HOLDER   A,
             APP___PAS__DBUSER.T_CUSTOMER        B,
             APP___PAS__DBUSER.T_CONTRACT_AGENT  AG ]]>
            <if test=" insuredFlag != null and insuredFlag != ''  "><![CDATA[  ,APP___PAS__DBUSER.T_INSURED_LIST    TIL,
             APP___PAS__DBUSER.T_CUSTOMER        B1 ]]></if> 
        <![CDATA[     
       WHERE 1 = 1
         AND T.POLICY_CODE = A.POLICY_CODE
         AND T.POLICY_CODE = AG.POLICY_CODE
         AND A.CUSTOMER_ID = B.CUSTOMER_ID
         AND T.VALIDATE_DATE < SYSDATE
         AND T.LIABILITY_STATE NOT IN ('3', '4')
         AND AG.IS_CURRENT_AGENT = '1'  ]]>
         <if test=" insuredFlag != null and insuredFlag != ''  "><![CDATA[  AND B1.CUSTOMER_ID = TIL.CUSTOMER_ID   AND A.POLICY_CODE = TIL.POLICY_CODE ]]></if> 
		<if test=" insuredBirthday != null and insuredBirthday != ''  "><![CDATA[ AND B1.CUSTOMER_BIRTHDAY = #{insuredBirthday} ]]></if>
    	<if test=" insuredIDNo != null and insuredIDNo != ''  "><![CDATA[AND B1.CUSTOMER_CERTI_CODE = #{insuredIDNo} ]]></if>
    	<if test=" insuredIDType != null and insuredIDType != ''  "><![CDATA[AND B1.CUSTOMER_CERT_TYPE = #{insuredIDType} ]]></if>
    	<if test=" insuredName != null and insuredName != ''  "><![CDATA[AND B1.CUSTOMER_NAME = #{insuredName} ]]></if>
    	<if test=" insuredSex != null and insuredSex != ''  "><![CDATA[AND B1.CUSTOMER_GENDER = #{insuredSex} ]]></if>   
		<if test=" contNo != null and contNo != '' "><![CDATA[  AND T.POLICY_CODE = #{contNo} ]]></if>
		<if test=" agentCode != null and agentCode != '' "><![CDATA[  AND AG.AGENT_CODE = #{agentCode} ]]></if>
		<if test=" appntBirthday != null and appntBirthday != ''  "><![CDATA[ AND B.CUSTOMER_BIRTHDAY = #{appntBirthday} ]]></if>
    	<if test=" appntIDNo != null and appntIDNo != ''  "><![CDATA[AND B.CUSTOMER_CERTI_CODE = #{appntIDNo} ]]></if>
    	<if test=" appntIDType != null and appntIDType != ''  "><![CDATA[AND B.CUSTOMER_CERT_TYPE = #{appntIDType} ]]></if>
    	<if test=" appntName != null and appntName != ''  "><![CDATA[AND B.CUSTOMER_NAME = #{appntName} ]]></if>
    	<if test=" appntSex != null and appntSex != ''  "><![CDATA[AND B.CUSTOMER_GENDER = #{appntSex} ]]></if>
    	<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
    	   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = T.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
    	</if>	   
	</select>
	
    <!-- 查询保单贷款续贷保单信息 
       #119143随信通_新增贷款续贷保全项接口需求-保单管理
    -->
	<select id="PA_queryRLPolicyListYDBQ" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
		 	SELECT T.POLICY_ID, T.POLICY_CODE
        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T,
             APP___PAS__DBUSER.T_POLICY_HOLDER   A,
             APP___PAS__DBUSER.T_CUSTOMER        B,
             APP___PAS__DBUSER.T_CONTRACT_AGENT  AG ]]>
            <if test=" insuredFlag != null and insuredFlag != ''  "><![CDATA[  ,APP___PAS__DBUSER.T_INSURED_LIST    TIL,
             APP___PAS__DBUSER.T_CUSTOMER        B1 ]]></if> 
        <![CDATA[     
       WHERE 1 = 1
         AND T.POLICY_CODE = A.POLICY_CODE
         AND T.POLICY_CODE = AG.POLICY_CODE
         AND A.CUSTOMER_ID = B.CUSTOMER_ID
         AND T.VALIDATE_DATE < SYSDATE
         AND AG.IS_CURRENT_AGENT = '1'  ]]>
         <if test=" insuredFlag != null and insuredFlag != ''  "><![CDATA[  AND B1.CUSTOMER_ID = TIL.CUSTOMER_ID   AND A.POLICY_CODE = TIL.POLICY_CODE ]]></if> 
		<if test=" insuredBirthday != null and insuredBirthday != ''  "><![CDATA[ AND B1.CUSTOMER_BIRTHDAY = #{insuredBirthday} ]]></if>
    	<if test=" insuredIDNo != null and insuredIDNo != ''  "><![CDATA[AND B1.CUSTOMER_CERTI_CODE = #{insuredIDNo} ]]></if>
    	<if test=" operator_user_code == '23' "> 
    	    <![CDATA[AND B1.CUSTOMER_CERT_TYPE in ('0','5') AND B.CUSTOMER_CERT_TYPE in ('0','5')]]>
    	    </if>
    	<if test=" insuredIDType != null and insuredIDType != '' and operator_user_code != '23' ">
    	    
    	    <![CDATA[AND B1.CUSTOMER_CERT_TYPE = #{insuredIDType} ]]>
    	    
    	</if>
    	<if test=" appntIDType != null and appntIDType != '' and operator_user_code != '23' ">   	   
    	    <![CDATA[AND B.CUSTOMER_CERT_TYPE = #{appntIDType} ]]>    	    
    	</if>
    	<if test=" insuredName != null and insuredName != ''  "><![CDATA[AND B1.CUSTOMER_NAME = #{insuredName} ]]></if>
    	<if test=" insuredSex != null and insuredSex != ''  "><![CDATA[AND B1.CUSTOMER_GENDER = #{insuredSex} ]]></if>   
		<if test=" contNo != null and contNo != '' "><![CDATA[  AND T.POLICY_CODE = #{contNo} ]]></if>
		<if test=" agentCode != null and agentCode != '' "><![CDATA[  AND AG.AGENT_CODE = #{agentCode} ]]></if>
		<if test=" appntBirthday != null and appntBirthday != ''  "><![CDATA[ AND B.CUSTOMER_BIRTHDAY = #{appntBirthday} ]]></if>
    	<if test=" appntIDNo != null and appntIDNo != ''  "><![CDATA[AND B.CUSTOMER_CERTI_CODE = #{appntIDNo} ]]></if>
    	
    	<if test=" appntName != null and appntName != ''  "><![CDATA[AND B.CUSTOMER_NAME = #{appntName} ]]></if>
    	<if test=" appntSex != null and appntSex != ''  "><![CDATA[AND B.CUSTOMER_GENDER = #{appntSex} ]]></if>	
    	<if test=" kd_product != null and kd_product != '' and  kd_product == '0'.toString() ">
	       <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = T.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
    	</if>
	</select>
    
    <!-- 查询保单信息-->
	<select id="findContractMasterBYDK" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[  SELECT TCM.POLICY_CODE,
					       TCM.POLICY_ID,
					       TCM.VALIDATE_DATE,
					       TCM.LIABILITY_STATE,
					       TCM.EXPIRY_DATE,
					       TCM.APPLY_DATE,
					       TCM.ISSUE_DATE,
					       case when 0 in( select b.COVER_PERIOD_TYPE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,APP___PDS__DBUSER.T_BUSINESS_PRODUCT B 
            				where a.policy_code = TCM.policy_code and a.MASTER_BUSI_ITEM_ID is null and A.BUSI_PRD_ID = B.BUSINESS_PRD_ID) then 'Y' else 'N' end as COVER_PERIOD_TYPE,
					       (select t.AGENT_CODE from dev_pas.T_CONTRACT_AGENT t where t.policy_code = TCM.POLICY_CODE and t.IS_CURRENT_AGENT = 1) AGENT_CODE,
					       (SELECT EC.CAUSE_NAME FROM APP___PAS__DBUSER.T_END_CAUSE EC  WHERE EC.CAUSE_CODE = TCM.END_CAUSE) AS end_cause_name,
					       (SELECT Z.CAUSE_DESC  FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
					       TCM.ORGAN_CODE,
					       (SELECT T.ORGAN_NAME  FROM APP___PAS__DBUSER.T_UDMP_ORG T    WHERE TCM.ORGAN_CODE = T.ORGAN_CODE) ORGAN_NAME,
					       TCA.ACCOUNT,
					       TCA.ACCOUNT_NAME,
					       TCA.ACCOUNT_BANK,
					       (SELECT T.BANK_NAME FROM DEV_PAS.T_BANK T WHERE T.BANK_CODE = TCA.ACCOUNT_BANK) BANKNAME,
					       TCA.NEXT_ACCOUNT,
					       TCA.NEXT_ACCOUNT_NAME,
					       TCA.NEXT_ACCOUNT_BANK,
					       (SELECT T.BANK_NAME FROM DEV_PAS.T_BANK T WHERE T.BANK_CODE = TCA.NEXT_ACCOUNT_BANK) NEXTBANKNAME,
					       (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_CONTRACT_AGENT TC,DEV_PAS.T_AGENT CA WHERE  TC.AGENT_CODE = CA.AGENT_CODE AND TC.IS_CURRENT_AGENT = '1' AND TC.POLICY_CODE = TCM.POLICY_CODE) as  agent_channel,
					       (SELECT CA.AGENT_LEVEL FROM DEV_PAS.T_CONTRACT_AGENT TC,DEV_PAS.T_AGENT CA WHERE TC.AGENT_CODE = CA.AGENT_CODE AND TC.IS_CURRENT_AGENT = '1' AND TC.POLICY_CODE = TCM.POLICY_CODE) as ca_agent_level,
					       (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_CONTRACT_AGENT TC,DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = TC.AGENT_CODE AND TC.IS_CURRENT_AGENT = '1' AND TC.POLICY_CODE = TCM.POLICY_CODE) as tab_agent_level,
					       TCM.special_account_flag,
					       TCM.multi_mainrisk_flag
					       FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					       APP___PAS__DBUSER.T_PAYER_ACCOUNT   TCA,
					       APP___PAS__DBUSER.t_Policy_Holder TPH,
                           APP___PAS__DBUSER.t_Customer TC
					 WHERE TCM.POLICY_ID = TCA.POLICY_ID
					 AND TCM.Policy_Id = tph.policy_id
                     AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID 
					 AND TCM.POLICY_CODE = #{policy_code} 
		]]>
		<if test=" appntIDType != null and appntIDType != '' ">
	    <![CDATA[AND TC.CUSTOMER_CERT_TYPE = #{appntIDType} ]]>
	    </if>
	    <if test=" operator_user_code == '23'  ">
	    <![CDATA[AND TC.CUSTOMER_CERT_TYPE in ('0','5') ]]>
	    </if>
	    <![CDATA[AND NOT EXISTS
				 (SELECT 1
				          FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
				         WHERE M.POLICY_CODE = CBP.POLICY_CODE
				           AND M.POLICY_CODE = TCM.POLICY_CODE
				           AND CBP.BUSI_PROD_CODE IN
				               (SELECT CI.CONSTANTS_VALUE
				                  FROM DEV_PAS.T_CONSTANTS_INFO CI
				                 WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT')) ]]>
	</select>
	
	<!-- 查询保单信息-->
	<select id="findCompanyCodeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.ORGAN_CODE,A.SUBMIT_CHANNEL
			FROM DEV_PAS.T_CONTRACT_MASTER A 
			WHERE A.POLICY_CODE = #{policy_code}
			AND SUBSTR(A.ORGAN_CODE,0,4) = #{organ_code}
		]]>
	</select>


	<!-- 查询保单下的首期银行、渠道等信息-->
	<select id="PA_queryPolicyBankInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TCA.POLICY_CODE
		  FROM APP___PAS__DBUSER.T_CONTRACT_AGENT TCA
		  JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA
		    ON TCA.POLICY_ID = TPA.POLICY_ID
		 WHERE TCA.IS_NB_AGENT = '1' 
		 AND TCA.CHANNEL_TYPE ='03'
		 AND TPA.ACCOUNT_BANK=#{service_bank}
		 AND TCA.POLICY_CODE=#{policy_code}
		]]>
	</select>	
	
	<!-- 查询该保单是否被挂起 -->
	<select id="PA_findLockPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.POLICY_CODE,
		       A.POLICY_ID,
		       B.SUB_ID 
		       FROM APP___PAS__DBUSER.T_LOCK_POLICY A
		       LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
		         ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
		      WHERE  B.SUB_ID IN ('068', '067')
		        AND B.LOCK_SERVICE_TYPE = 1
		        AND A.POLICY_CODE = #{policy_code}
		]]>
	</select>	
	
		<!-- 核心险种信息查询接口查询 -->
	<select id="PA_findWXPolicyHXXZByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT DISTINCT TC.CUSTOMER_NAME,   
					       TC.CUSTOMER_GENDER,    
					       TC.CUSTOMER_BIRTHDAY,  
					       TAS.MOBILE_TEL,       
					       TA.AGENT_NAME,        
					       TA.AGENT_CODE,         
					       TC.COUNTRY_CODE,       
					       TCM.APPLY_CODE         
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					       APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
					       APP___PAS__DBUSER.T_CUSTOMER        TC,
					       APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
					       APP___PAS__DBUSER.T_AGENT           TA,
					       APP___PAS__DBUSER.T_ADDRESS         TAS
					 WHERE TCM.POLICY_CODE = TPH.POLICY_CODE
					   AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
					   AND TCM.POLICY_CODE = TCA.POLICY_CODE
					   AND TCA.AGENT_CODE = TA.AGENT_CODE
					   AND TPH.ADDRESS_ID = TAS.ADDRESS_ID
					   AND TCM.LIABILITY_STATE = 1
					   and tca.is_current_agent = 1
					   AND TCM.POLICY_CODE =  #{policy_code}
		]]>
	</select>	
	
	<!-- 受理机构是否为保单管理机构的下属机构 -->
	<select id="checkAcceptOrganAndPolicyCodeOrgan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select cm.POLICY_PWD, cm.MEDIA_TYPE, cm.APPLY_CODE, cm.INTEREST_MODE, cm.ORGAN_CODE, cm.CHANNEL_TYPE, cm.SALE_AGENT_NAME, 
      cm.INSURED_FAMILY, cm.POLICY_ID, cm.DERIVATION, cm.SUBINPUT_TYPE, 
      cm.BASIC_REMARK, cm.SALE_COM_CODE, cm.INPUT_DATE, cm.POLICY_TYPE, cm.EXPIRY_DATE, cm.PWD_INVALID_FLAG, cm.SUBMIT_CHANNEL, 
      cm.LIABILITY_STATE, cm.POLICY_CODE, cm.SALE_AGENT_CODE, cm.RERINSTATE_DATE, cm.BRANCH_CODE, cm.VALIDATE_DATE, 
      cm.AGENCY_CODE, cm.MONEY_CODE, cm.SERVICE_HANDLER_CODE, cm.APPLY_DATE, cm.INITIAL_VALIDATE_DATE, 
      cm.SUBMISSION_DATE, cm.SERVICE_HANDLER, cm.E_SERVICE_FLAG, cm.SERVICE_BANK_BRANCH, cm.LAPSE_DATE, cm.DC_INDI, cm.SALE_TYPE, 
      cm.INPUT_TYPE, cm.END_CAUSE, cm.ISSUE_DATE, cm.STATISTIC_CHANNEL, cm.LAPSE_CAUSE, cm.DECISION_CODE, cm.WINNING_START_FLAG,
      cm.AGENT_ORG_ID, cm.SERVICE_BANK,cm.BANK_AGENCY_FLAG, cm.SUSPEND_DATE, cm.INITIAL_PREM_DATE, cm.SUSPEND_CAUSE, cm.LANG_CODE, cm.FORMER_ID,cm.RELATION_POLICY_CODE,cm.IS_SELF_INSURED, 
      cm.POLICY_FLAG,cm.DOUBLE_MAINRISK_FLAG,cm.TAX_EXTENSION_SOURCE,cm.POLICY_PRD_FLAG,cm.group_sale_type,cm.APPLY_TIME,cm.IS_ALONE_INSURE,cm.MEDICAL_INSURANCE_CARD,cm.CALL_TIME_LIST,cm.POLICY_REINSURE_FLAG,cm.REINSURED_TIMES
  from dev_pas.t_contract_master cm
 where cm.policy_code = #{policy_code}
   and cm.organ_code in
       (SELECT t.Organ_Code
          FROM Dev_Pas.t_Udmp_Org_Rel t
         WHERE t.Organ_Code = #{acceptOrganCode}
        UNION
        
        SELECT t.Organ_Code
          FROM Dev_Pas.t_Udmp_Org_Rel t
         WHERE t.Organ_Code IN
               (SELECT t.Uporgan_Code
                  FROM Dev_Pas.t_Udmp_Org_Rel t
                 WHERE t.Organ_Code = #{acceptOrganCode})
        
        UNION
        SELECT t.Organ_Code
          FROM Dev_Pas.t_Udmp_Org_Rel t
         WHERE t.Organ_Code IN
               (SELECT t.Uporgan_Code
                  FROM Dev_Pas.t_Udmp_Org_Rel t
                 WHERE t.Organ_Code IN
                       (SELECT t.Uporgan_Code
                          FROM Dev_Pas.t_Udmp_Org_Rel t
                         WHERE t.Organ_Code = #{acceptOrganCode}))
        UNION
        
        SELECT t.Organ_Code
          FROM Dev_Pas.t_Udmp_Org_Rel t
         WHERE t.Organ_Code IN
               (SELECT t.Uporgan_Code
                  FROM Dev_Pas.t_Udmp_Org_Rel t
                 WHERE t.Organ_Code IN
                       (SELECT t.Uporgan_Code
                          FROM Dev_Pas.t_Udmp_Org_Rel t
                         WHERE t.Organ_Code IN
                               (SELECT t.Uporgan_Code
                                  FROM Dev_Pas.t_Udmp_Org_Rel t
                                 WHERE t.Organ_Code = #{acceptOrganCode}))))
		]]>
	</select>
	
	<!-- 查询该保单退保终止类型 -->
	<select id="findSurrenderTypeAndFlag" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT ROWNUM RN, T.SURRENDER_TYPE, T.HESITATE_FLAG
          FROM (SELECT TS.SURRENDER_TYPE, TS.HESITATE_FLAG
                  FROM APP___PAS__DBUSER.T_SURRENDER          TS,
                       APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
                       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
                 WHERE 1 = 1
                   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
                   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
                   AND TS.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                   AND TCM.POLICY_CODE = #{policy_code} 
                   ORDER BY TS.LIST_ID DESC) T
           WHERE ROWNUM = 1	          
				          
				               ]]>
	</select>

	<!-- 查询是否银代新政保单，自动核保使用 #100258 -->
	<select id="findBankNewDealFlag" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			  select case when count(1) > 0 then '1' else '0' end as bank_new_deal_flag
				  from dev_pas.t_contract_master     cm
				  		inner join dev_pas.t_contract_agent	ca on ca.policy_id = cm.policy_id
				 where ca.channel_type = '03'
				   and cm.service_bank not in ('25', '37', '60', '90')
				   and cm.policy_code = #{policy_code}       
		]]>
	</select>

	<!-- 查询是否银代新政保单，自动核保使用 #100258 -->
	<select id="findBankNewDealFlagByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			  select case when count(1) > 0 then '1' else '0' end as bank_new_deal_flag
				  from dev_pas.t_contract_master     cm
				  		inner join dev_pas.t_contract_agent	ca on ca.policy_id = cm.policy_id
				 where ca.channel_type = '03'
				   and cm.service_bank not in ('25', '37', '60', '90')
				   and cm.apply_code = #{apply_code}       
		]]>
	</select>

	<!-- 查询是否利益确认产品，保全录入使用 -->
	<select id="findInterestFlag" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			 select case when count(1) > 0 then '1' else '0' end as interest_flag
			  from dev_pas.t_contract_master     t,
				   dev_pas.t_contract_busi_prod  cbp,
				   dev_pas.t_business_product bp
			 where  t.policy_id = cbp.policy_id
			   and bp.product_code_sys = cbp.busi_prod_code
			   and bp.product_category1 = '20001'
			   and t.policy_code =  #{policy_code} 
		]]>
	</select>
	<select id="findSHPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
			t.POLICY_PWD, 
			t.MEDIA_TYPE, 
			t.APPLY_CODE, 
			t.INTEREST_MODE, 
			t.ORGAN_CODE, 
			t.CHANNEL_TYPE, 
			t.SALE_AGENT_NAME, 
			
			t.INSURED_FAMILY, 
			t.POLICY_ID, 
			t.DERIVATION, 
			t.SUBINPUT_TYPE, 
			
			t.BASIC_REMARK, 
			t.SALE_COM_CODE, 
			t.INPUT_DATE, 
			t.POLICY_TYPE, 
			t.EXPIRY_DATE, 
			t.PWD_INVALID_FLAG, 
			t.SUBMIT_CHANNEL, 
			
			t.LIABILITY_STATE, 
			t.POLICY_CODE, 
			t.SALE_AGENT_CODE, 
			t.RERINSTATE_DATE, 
			t.BRANCH_CODE, 
			t.VALIDATE_DATE, 
			
			t.AGENCY_CODE, 
			t.MONEY_CODE, 
			t.SERVICE_HANDLER_CODE, 
			t.APPLY_DATE, 
			t.INITIAL_VALIDATE_DATE, 
			
			t.SUBMISSION_DATE, 
			t.SERVICE_HANDLER, 
			t.E_SERVICE_FLAG, 
			t.SERVICE_BANK_BRANCH, 
			t.LAPSE_DATE, 
			t.DC_INDI, 
			t.SALE_TYPE, 
			
			t.INPUT_TYPE, 
			t.END_CAUSE, 
			t.ISSUE_DATE, 
			t.STATISTIC_CHANNEL, 
			t.LAPSE_CAUSE, 
			t.DECISION_CODE, 
			t.WINNING_START_FLAG,
			
			t.AGENT_ORG_ID, 
			t.SERVICE_BANK,
			t.BANK_AGENCY_FLAG, 
			t.SUSPEND_DATE, 
			t.INITIAL_PREM_DATE, 
			t.SUSPEND_CAUSE, 
			t.LANG_CODE, 
			t.FORMER_ID,
			t.RELATION_POLICY_CODE,
			t.IS_SELF_INSURED, 
			
			t.POLICY_FLAG,
			t.DOUBLE_MAINRISK_FLAG,
			t.TAX_EXTENSION_SOURCE,
			t.POLICY_PRD_FLAG,
			t.group_sale_type,
			t.APPLY_TIME,
			t.IS_ALONE_INSURE,
			t.MEDICAL_INSURANCE_CARD,
			t.CALL_TIME_LIST,
			t.POLICY_REINSURE_FLAG,
			t.REINSURED_TIMES from dev_pas.t_contract_master t where  EXISTS (SELECT 1
          FROM (SELECT O.ORGAN_CODE
                  FROM DEV_PAS.T_UDMP_ORG_REL O
                 START WITH O.ORGAN_CODE = '8622'
                CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE) DD
         WHERE DD.ORGAN_CODE = t.ORGAN_CODE) and t.policy_code = #{policy_code}
		]]>
	</select>
	
	<!-- 查询所有满期不续保的保单 -->
	<select id="PA_queryNoRenewalExpiration" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				SELECT TCM.POLICY_CODE,
					   TCM.POLICY_ID,
				       TCM.LIABILITY_STATE,
				       TCM.BRANCH_CODE,
				        (SELECT Z.STATUS_NAME
				              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
				             WHERE Z.STATUS_CODE = TCM.LIABILITY_STATE) AS LIABILITY_NAME,
				           TCM.END_CAUSE,
				           (SELECT Z.CAUSE_NAME
				           FROM APP___PAS__DBUSER.T_END_CAUSE Z
				           WHERE Z.CAUSE_CODE = TCM.END_CAUSE) AS END_CAUSE_NAME,
				           TCM.LAPSE_CAUSE,
				           (SELECT Z.CAUSE_DESC
				           FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
				           WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
				       TCM.VALIDATE_DATE,
				       TCM.EXPIRY_DATE,
				       (SELECT TU.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG TU
				         WHERE TU.ORGAN_CODE = TCAT.ORGAN_CODE) AGENT_ORGAN_NAME,
				       TCAT.ORGAN_CODE AGENT_ORGAN_CODE,
				       (SELECT T.ORGAN_NAME
				          FROM DEV_PAS.T_UDMP_ORG_REL T
				         WHERE T.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME,
				       TCM.ORGAN_CODE,
				       TCM.SPECIAL_ACCOUNT_FLAG,
				       TCM.MULTI_MAINRISK_FLAG,
				       TCM.SUBMIT_CHANNEL
				  FROM DEV_PAS.T_CONTRACT_MASTER TCM,
				       DEV_PAS.T_CONTRACT_AGENT  TCAT,
				       DEV_PAS.T_POLICY_HOLDER   TPHR,
				       DEV_PAS.T_CUSTOMER        TC
				 WHERE 1 = 1
				   AND TCM.POLICY_CODE = TCAT.POLICY_CODE
				   AND TCM.POLICY_CODE = TPHR.POLICY_CODE
				   AND TPHR.CUSTOMER_ID = TC.CUSTOMER_ID
				   AND TCAT.IS_CURRENT_AGENT =1
				  and not exists (Select 'X'
			          From dev_pas.t_Contract_Relation  CR,
			               dev_pas.t_Contract_Busi_Prod cbp,
			               dev_pds.t_Business_Product   bp
			         where CR.Sub_Policy_id = TCM.Policy_Id
			           and cbp.busi_item_id = CR.Sub_Busi_Item_Id
			           and cbp.busi_prd_id = bp.business_prd_id
			           and CR.RELATION_TYPE = 1
			           and bp.acc_risk_single_insure_flag = 1)]]>
		<if test=" ContNo != null and ContNo != '' "><![CDATA[  AND TCM.POLICY_CODE = #{ContNo} ]]></if>		
		<if test=" AgentCode != null and AgentCode != '' "><![CDATA[  AND TCAT.AGENT_CODE = #{AgentCode} ]]></if>           
		<if test=" AppntName != null and AppntName != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{AppntName} ]]></if>
		<if test=" IdNo != null and IdNo != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{IdNo} ]]></if>
		<if test=" AppntSex != null and AppntSex != ''  "><![CDATA[AND TC.CUSTOMER_GENDER = #{AppntSex} ]]></if>
		<if test=" AppBirthday != null and AppBirthday != ''  "><![CDATA[AND TC.CUSTOMER_BIRTHDAY = #{AppBirthday} ]]></if>
		<if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND TCM.VALIDATE_DATE >= #{validate_date} ]]></if>	
        <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND TCM.VALIDATE_DATE <= #{suspend_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND TCM.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" id_type_list  != null and id_type_list.size()!=0">
			<![CDATA[ AND TC.CUSTOMER_CERT_TYPE IN (]]>
			<foreach collection="id_type_list" item="id_type"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{id_type} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">
		   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = TCM.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>		
		</if>
	</select>
	
	<!-- 失效预终止 --> 
	<select id="PA_queryPreTermination" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
		SELECT ROWNUM RN,A.POLICY_CODE 
		FROM DEV_PAS.T_CONTRACT_MASTER A WHERE 1=1 
			AND A.LIABILITY_STATE=4
			AND A.LAPSE_CAUSE='1'
			AND Add_Months(A.LAPSE_DATE,22) = #{batch_date}
		]]>
		<include refid="PA_contractMasterWhereCondition" />
		<![CDATA[ AND MOD(A.POLICY_ID,#{modNum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR} ]]>
	</select>	
	
	<!-- 查询所有退保的保单 -->
	<select id="PA_queryPolicySurrender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				SELECT TCM.POLICY_CODE,
								     TCM.POLICY_ID,
								     TCM.APPLY_DATE,
								     TCAT.AGENT_NAME,
									 TCAT.AGENT_MOBILE,
									 TCM.LIABILITY_STATE,
									 TCM.DOUBLE_MAINRISK_FLAG,
									 TCM.RELATION_POLICY_CODE,
									 TCM.EXPIRY_DATE,
									 TCM.MEDICAL_INSURANCE_CARD,
									 (SELECT Z.STATUS_NAME
											FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
										 WHERE Z.STATUS_CODE = TCM.LIABILITY_STATE) AS LIABILITY_NAME,
									 TCM.END_CAUSE,
									 (SELECT Z.CAUSE_NAME
											FROM APP___PAS__DBUSER.T_END_CAUSE Z
										 WHERE Z.CAUSE_CODE = TCM.END_CAUSE) AS END_CAUSE_NAME,
									 TCM.LAPSE_CAUSE,
									 (SELECT Z.CAUSE_DESC
											FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
										 WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
									 TCM.VALIDATE_DATE,
									 TCAT.AGENT_CODE,
									 (SELECT TU.ORGAN_NAME
											FROM APP___PAS__DBUSER.T_UDMP_ORG TU
										 WHERE TU.ORGAN_CODE = TCAT.ORGAN_CODE) AGENT_ORGAN_NAME,
									 TCAT.ORGAN_CODE AGENT_ORGAN_CODE,
									 (SELECT T.ORGAN_NAME
				          FROM DEV_PAS.T_UDMP_ORG_REL T
				         WHERE T.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME,
									 TCM.ORGAN_CODE,
									 (SELECT TSC.SALES_CHANNEL_CODE FROM DEV_PAS.T_SALES_CHANNEL TSC WHERE TSC.SALES_CHANNEL_CODE IN (SELECT TCA.CHANNEL_TYPE FROM DEV_PAS.T_CONTRACT_AGENT TCA WHERE TCA.IS_CURRENT_AGENT = '1' AND TCA.POLICY_CODE = TCM.POLICY_CODE))CHANNEL_TYPE,
									 TPA.ACKNOWLEDGE_DATE,
									 TCM.MEDIA_TYPE,
									 TPAT.NEXT_ACCOUNT,
									 TPAT.NEXT_ACCOUNT_NAME,
									 (select T.BANK_NAME from DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK )NEXT_ACCOUNT_BANK,
									 TPAT.ACCOUNT_BANK ACCOUNT_ID,
									 TPAT.NEXT_ACCOUNT_BANK NEXT_ACCOUNT_ID,
									 TPAT.ACCOUNT,
									 TPAT.ACCOUNT_NAME,
									 (select T.BANK_NAME from DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPAT.ACCOUNT_BANK )ACCOUNT_BANK,
									 TCM.SUBMIT_CHANNEL,
									 (select a.CHANNEL_NAME 
					                from dev_pas.t_SUBMIT_CHANNEL a 
					                where a.SUBMIT_CHANNEL = TCM.SUBMIT_CHANNEL) SUBMIT_CHANNEL_NAME,
					                TCM.SPECIAL_ACCOUNT_FLAG as special_account_flag,
					                (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = TCAT.AGENT_CODE) as agent_channel,
					                (SELECT CA.AGENT_LEVEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = TCAT.AGENT_CODE) as ca_agent_level,
					                (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = TCAT.AGENT_CODE) as tab_agent_level,
                          			TCM.Multi_Mainrisk_Flag as Multi_Mainrisk_Flag,
                          			TCM.IS_SELF_INSURED,
                          			TCM.IS_MUTUAL_INSURED
							FROM DEV_PAS.T_CONTRACT_MASTER TCM,
									 DEV_PAS.T_CONTRACT_AGENT  TCAT,
									 DEV_PAS.T_PAYER_ACCOUNT TPAT,
									 DEV_PAS.T_POLICY_ACKNOWLEDGEMENT TPA,
									 DEV_PAS.T_POLICY_HOLDER   TPHR,
									 DEV_PAS.T_CUSTOMER        TC
						 WHERE 1 = 1
						     AND TCM.POLICY_ID = TPAT.POLICY_ID
						     AND TCM.POLICY_ID = TPA.POLICY_ID
							 AND TCM.POLICY_CODE = TCAT.POLICY_CODE
							 AND TCM.POLICY_CODE = TPHR.POLICY_CODE
							 AND TPHR.CUSTOMER_ID = TC.CUSTOMER_ID
							 AND TCAT.IS_CURRENT_AGENT = '1'
							  ]]>
							  
		<if test=" contno != null and contno != '' "><![CDATA[  AND TCM.POLICY_CODE = #{contno}]]></if>		
		<if test=" appntname != null and appntname != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{appntname}]]></if>
		<if test=" idno != null and idno != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{idno}]]></if>
		<if test=" agentcode != null and agentcode != ''  "><![CDATA[AND TCAT.AGENT_CODE = #{agentcode}]]></if>
		<if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND TCM.VALIDATE_DATE >= #{validate_date} ]]></if>	
        <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND TCM.VALIDATE_DATE <= #{suspend_date} ]]></if>
        <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND TCM.LIABILITY_STATE = #{liability_state} ]]></if>
        <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
           <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = TCM.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
        </if>
	</select>
	
	<!-- 查询保单下有可转换险种，但其中存在投保时选择不自动续保、且也不存在其他的险种可以转换 -->
	<select id="PA_queryALLPolicyByEn" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			SELECT TCM.POLICY_CODE,TCBP.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_MASTER TCM,
              DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
							WHERE 1=1
							AND TCM.POLICY_CODE = TCBP.POLICY_CODE
							AND TCBP.RENEW = '0'
							AND TCM.POLICY_CODE = #{policy_code}
							AND EXISTS (select 1 from APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE C,
													  APP___PDS__DBUSER.t_service                  s,
													  APP___PDS__DBUSER.t_business_product         p
												 where c.business_prd_id = p.business_prd_id
													 and c.service_code = s.service_code
													 and P.Product_Code_Sys = TCBP.Busi_Prod_Code
													 and c.service_code = 'RR')
							AND NOT EXISTS (SELECT 1 FROM DEV_PAS.T_CS_ACCEPT_CHANGE TCAC,
							                              DEV_PAS.T_CS_POLICY_CHANGE TCPC
														  WHERE 1=1 
														  AND TCPC.POLICY_CODE = TCM.POLICY_CODE
														  AND TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
														  AND TCAC.SERVICE_CODE = 'EN'
														  AND TCAC.ACCEPT_STATUS = '18')
		]]>		
	</select>
	
	<!-- 保单下有可转换险种，但其中有险种操作过满期不续保、且也不存在其他的险种可以转换-->
	<select id="PA_queryALLPolicyCodeByEn" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			SELECT TCM.POLICY_CODE,TCBP.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_MASTER TCM,
              DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
							WHERE 1=1
							AND TCM.POLICY_CODE = TCBP.POLICY_CODE
							AND TCM.POLICY_CODE = #{policy_code}
							AND EXISTS (select 1 from APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE C,
													  APP___PDS__DBUSER.t_service                  s,
													  APP___PDS__DBUSER.t_business_product         p
												 where c.business_prd_id = p.business_prd_id
													 and c.service_code = s.service_code
													 and P.Product_Code_Sys = TCBP.Busi_Prod_Code
													 and c.service_code = 'RR')
							AND EXISTS (SELECT 1 FROM DEV_PAS.T_CS_ACCEPT_CHANGE TCAC,
							                              DEV_PAS.T_CS_POLICY_CHANGE TCPC
														  WHERE 1=1 
														  AND TCPC.POLICY_CODE = TCM.POLICY_CODE
														  AND TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
														  AND TCAC.SERVICE_CODE = 'EN'
														  AND TCAC.ACCEPT_STATUS = '18')
		]]>		
	</select>
	
	<!-- 累积生息账户领取详情查询接口查询保单信息 -->
	<select id="PA_findConMasterForQueryAIPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    		
					    SELECT DISTINCT TCM.POLICY_ID,
					           TCM.POLICY_CODE, /*保单号*/
					           TCM.VALIDATE_DATE, /*保单生效日期*/
					           TCM.EXPIRY_DATE, /*保单终止日期*/ 
					           TCM.END_CAUSE,
                     		   TCM.LAPSE_CAUSE,
                     		   TCM.MULTI_MAINRISK_FLAG,
                     		   TCM.SPECIAL_ACCOUNT_FLAG,
                     		   TCM.SUBMIT_CHANNEL, 
					           (SELECT TLS.STATUS_NAME
					              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS TLS
					             WHERE TLS.STATUS_CODE = TCM.LIABILITY_STATE) AS CONTSTATESMSG, /*保单状态描述*/
					           (CASE
					             WHEN TCM.LIABILITY_STATE = '3' THEN
					              (SELECT TEC.CAUSE_NAME
					                 FROM APP___PAS__DBUSER.T_END_CAUSE TEC
					                WHERE TEC.CAUSE_CODE = TCM.END_CAUSE)
					             WHEN TCM.LIABILITY_STATE = '4' THEN
					              (SELECT TLC.CAUSE_DESC
					                 FROM APP___PAS__DBUSER.T_LAPSE_CAUSE TLC
					                WHERE TLC.CAUSE_CODE = TCM.LAPSE_CAUSE)
					             ELSE
					              ''
					           END) AS STATESMSG, /*状态原因*/
					           TCM.ORGAN_CODE, /*保单管理机构代码*/
					           (SELECT TUO.ORGAN_NAME
					              FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
					             WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) AS POLICYORGANNAME, /*保单管理机构名称*/
					  (Select b.acc_risk_single_insure_flag From APP___PAS__DBUSER.t_Contract_Busi_Prod A,APP___PDS__DBUSER.t_Business_Product b where A.Policy_Code = TCM.Policy_Code and a.busi_prd_id = b.business_prd_id and b.acc_risk_single_insure_flag = 1 and a.liability_state = 1) acc_risk_single_insure_flag,
                      (Select (case when a.liability_state = 3 and a.end_cause = '02' then a.busi_item_id else null end) From APP___PAS__DBUSER.t_Contract_Busi_Prod a where a.policy_code = tcm.policy_code and a.busi_item_id = tpa.busi_item_id and a.master_busi_item_id is null) master_busi_item_id
					      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					           APP___PAS__DBUSER.T_POLICY_ACCOUNT  TPA,
					           APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
					           APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
					           APP___PAS__DBUSER.T_CUSTOMER        TC
					     WHERE 1 = 1
					       AND TPA.POLICY_ID = TCM.POLICY_ID
					       AND TCA.IS_CURRENT_AGENT = 1
					       AND TCM.LIABILITY_STATE != 0
					       AND TPA.ACCOUNT_TYPE IN ('11', '2')
					       AND TPA.POLICY_ACCOUNT_STATUS != 2 
					       AND TCM.VALIDATE_DATE <= SYSDATE 
					       AND TCM.POLICY_CODE = TCA.POLICY_CODE
					       AND TCM.POLICY_CODE = TPH.POLICY_CODE
					       AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
					       and not exists (Select 'X'
				             From dev_pas.t_Contract_Relation  CR,
				                  dev_pas.t_Contract_Busi_Prod cbp,
				                  dev_pds.t_Business_Product   bp
				            where CR.Sub_Policy_id = TCM.Policy_Id
				              and cbp.busi_item_id = CR.Sub_Busi_Item_Id
				              and cbp.busi_prd_id = bp.business_prd_id
				              and CR.RELATION_TYPE = 1
				              and bp.acc_risk_single_insure_flag = 1)
				      	   
					        ]]>	
			 <include refid="PA_WhereFindConMasterForQueryAIPolicy" />
			 <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">
			    <![CDATA[AND NOT EXISTS
						 (SELECT 1
							FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
						   WHERE M.POLICY_CODE = CBP.POLICY_CODE
							 AND M.POLICY_CODE = TCM.POLICY_CODE
							 AND CBP.BUSI_PROD_CODE IN 
								 (SELECT CI.CONSTANTS_VALUE
									FROM DEV_PAS.T_CONSTANTS_INFO CI
								   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>		
			 </if> 
				<![CDATA[ 	UNION
					SELECT TCM.POLICY_ID,
				           TCM.POLICY_CODE, /*保单号*/
				           TCM.VALIDATE_DATE, /*保单生效日期*/ 
				           TCM.EXPIRY_DATE, /*保单终止日期*/ 
				           TCM.END_CAUSE,
                     	   TCM.LAPSE_CAUSE,
                     	   TCM.MULTI_MAINRISK_FLAG,
                     	   TCM.SPECIAL_ACCOUNT_FLAG,
                     	   TCM.SUBMIT_CHANNEL, 
				           (SELECT TLS.STATUS_NAME
				              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS TLS
				             WHERE TLS.STATUS_CODE = TCM.LIABILITY_STATE) AS CONTSTATESMSG, /*保单状态描述*/
				           (CASE
				             WHEN TCM.LIABILITY_STATE = '3' THEN
				              (SELECT TEC.CAUSE_NAME
				                 FROM APP___PAS__DBUSER.T_END_CAUSE TEC
				                WHERE TEC.CAUSE_CODE = TCM.END_CAUSE)
				             WHEN TCM.LIABILITY_STATE = '4' THEN
				              (SELECT TLC.CAUSE_DESC
				                 FROM APP___PAS__DBUSER.T_LAPSE_CAUSE TLC
				                WHERE TLC.CAUSE_CODE = TCM.LAPSE_CAUSE)
				             ELSE
				              ''
				           END) AS STATESMSG, /*状态原因*/
				           TCM.ORGAN_CODE, /*保单管理机构代码*/
				           (SELECT TUO.ORGAN_NAME
				              FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
				             WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) AS POLICYORGANNAME, /*保单管理机构名称*/
					  (Select b.acc_risk_single_insure_flag From APP___PAS__DBUSER.t_Contract_Busi_Prod A,APP___PDS__DBUSER.t_Business_Product b where A.Policy_Code = TCM.Policy_Code and a.busi_prd_id = b.business_prd_id and b.acc_risk_single_insure_flag = 1 and a.liability_state = 1) acc_risk_single_insure_flag,
                      (Select (case when a.liability_state = 3 and a.end_cause = '02' then a.busi_item_id else null end) From APP___PAS__DBUSER.t_Contract_Busi_Prod a where a.policy_code = tcm.policy_code and a.busi_item_id = tpa.busi_item_id  and a.master_busi_item_id is null) master_busi_item_id
				      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
				           APP___PAS__DBUSER.T_POLICY_ACCOUNT  TPA,
				           APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
				           APP___PAS__DBUSER.T_INSURED_LIST    TIL,
				           APP___PAS__DBUSER.T_CUSTOMER        TC
				     WHERE 1 = 1
				       AND TPA.POLICY_ID = TCM.POLICY_ID
				       AND TCA.IS_CURRENT_AGENT = 1
				       AND TCM.LIABILITY_STATE != 0
				       AND TPA.ACCOUNT_TYPE IN ('11', '2')
				       AND TPA.POLICY_ACCOUNT_STATUS != 2 
				       AND TCM.VALIDATE_DATE <= SYSDATE 
				       AND TCM.POLICY_CODE = TCA.POLICY_CODE
				       AND TCM.POLICY_CODE = TIL.POLICY_CODE
				       AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID 
				       and not exists (Select 'X'
			             From dev_pas.t_Contract_Relation  CR,
			                  dev_pas.t_Contract_Busi_Prod cbp,
			                  dev_pds.t_Business_Product   bp
			            where CR.Sub_Policy_id = TCM.Policy_Id
			              and cbp.busi_item_id = CR.Sub_Busi_Item_Id
			              and cbp.busi_prd_id = bp.business_prd_id
			              and CR.RELATION_TYPE = 1
			              and bp.acc_risk_single_insure_flag = 1)
			      ]]>	
			 <include refid="PA_WhereFindConMasterForQueryAIPolicy" />
			 <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">
			    <![CDATA[AND NOT EXISTS
						 (SELECT 1
							FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
						   WHERE M.POLICY_CODE = CBP.POLICY_CODE
							 AND M.POLICY_CODE = TCM.POLICY_CODE
							 AND CBP.BUSI_PROD_CODE IN 
								 (SELECT CI.CONSTANTS_VALUE
									FROM DEV_PAS.T_CONSTANTS_INFO CI
								   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>		
			 </if>
	</select>
	
	<!-- 累积生息账户领取详情查询接口查询保单信息查询条件 -->
	<sql id="PA_WhereFindConMasterForQueryAIPolicy">
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</sql>
	
	<!-- 万能险账户领取保单查询 -->
	<select id="PA_queryQueryPGPolicyForYdbq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				    SELECT TCM.POLICY_CODE,
                           TCM.POLICY_ID,
                           TCM.VALIDATE_DATE,
                           TCM.LIABILITY_STATE ,
                           (SELECT Z.STATUS_NAME
                              FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
                             WHERE Z.STATUS_CODE = TCM.LIABILITY_STATE) AS LIABILITY_NAME,
                           TCM.END_CAUSE,
                           (SELECT Z.CAUSE_NAME
                              FROM APP___PAS__DBUSER.T_END_CAUSE Z
                             WHERE Z.CAUSE_CODE = TCM.END_CAUSE) AS END_CAUSE_NAME,
                           TCM.LAPSE_CAUSE,
                           (SELECT Z.CAUSE_DESC
                              FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
                             WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
                            TCM.ORGAN_CODE,
                            (SELECT T.ORGAN_NAME
                              FROM DEV_PAS.T_UDMP_ORG_REL T
                             WHERE T.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME,
                           TC.CUSTOMER_NAME,
                           TC.CUSTOMER_CERTI_CODE ,
                           TCA.AGENT_CODE,
                           TCM.EXPIRY_DATE,
                           (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = TCA.AGENT_CODE) as agent_channel,
                           (SELECT CA.AGENT_LEVEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = TCA.AGENT_CODE) as ca_agent_level,
                           (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = TCA.AGENT_CODE) as tab_agent_level,
                           TCM.MULTI_MAINRISK_FLAG,
                           TCM.SPECIAL_ACCOUNT_FLAG,
                           TCM.SUBMIT_CHANNEL
                      FROM DEV_PAS.T_CONTRACT_MASTER        TCM,
                           DEV_PAS.T_CONTRACT_BUSI_PROD     TCBP,
                           DEV_PAS.T_BUSINESS_PRODUCT       TBP,
                           DEV_PAS.T_CONTRACT_AGENT         TCA,
                           DEV_PAS.T_POLICY_HOLDER          TPH,
                           DEV_PAS.T_CUSTOMER               TC
                     WHERE 1 = 1
                       AND TCM.POLICY_CODE = TCBP.POLICY_CODE 
                       AND TCM.POLICY_CODE = TCA.POLICY_CODE
                       AND TCM.POLICY_CODE = TPH.POLICY_CODE
                       AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
                       AND TCA.IS_CURRENT_AGENT = 1 
                       AND TCM.LIABILITY_STATE != 3
                       AND TCBP.LIABILITY_STATE != 3
                       AND TBP.acc_risk_single_insure_flag = 0
                       AND TCBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS 
                       AND TBP.PRODUCT_CATEGORY1 IN ('20003','20004') 
                       and not exists (Select 'X' From dev_pas.t_Contract_Relation CR,dev_pas.t_Contract_Busi_Prod cbp,dev_pds.t_Business_Product bp where CR.Sub_Policy_id = TCM.Policy_Id and cbp.busi_item_id =CR.Sub_Busi_Item_Id and cbp.busi_prd_id = bp.business_prd_id and CR.RELATION_TYPE = 1 and bp.acc_risk_single_insure_flag = 1)
                        ]]> 
		<if test=" contno != null and contno != '' "><![CDATA[  AND TCM.POLICY_CODE = #{contno}]]></if>		
		<if test=" appntname != null and appntname != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{appntname}]]></if>
		<if test=" idno != null and idno != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{idno}]]></if>
		<if test=" agentcode != null and agentcode != ''  "><![CDATA[AND TCA.AGENT_CODE = #{agentcode}]]></if>
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = TCM.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		</if>
	</select>

	<!-- 查询客户名下所有928保单 -->
	<select id="PA_findAllTLWNPolicyForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			        SELECT TCM.POLICY_CODE,
					       TCM.POLICY_ID,
					       TCM.VALIDATE_DATE,
					       TCM.APPLY_DATE,
					       TCM.LIABILITY_STATE,
					       (SELECT Z.STATUS_NAME
					          FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
					         WHERE Z.STATUS_CODE = TCM.LIABILITY_STATE) AS LIABILITY_NAME,
					       TCM.END_CAUSE,
					       (SELECT Z.CAUSE_NAME
					          FROM APP___PAS__DBUSER.T_END_CAUSE Z
					         WHERE Z.CAUSE_CODE = TCM.END_CAUSE) AS END_CAUSE_NAME,
					       TCM.LAPSE_CAUSE,
					       (SELECT Z.CAUSE_DESC
					          FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
					         WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME,
					       TCM.ORGAN_CODE,
					       (SELECT T.ORGAN_NAME
					          FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
					         WHERE T.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME,
					       TC.CUSTOMER_NAME,
					       TC.CUSTOMER_CERTI_CODE,
					       TCA.AGENT_NAME,
					       TCA.AGENT_MOBILE,
					       TCA.AGENT_CODE,
					       TCM.SPECIAL_ACCOUNT_FLAG,
					       TCBP.ISSUE_DATE,
					       TCBP.BUSI_ITEM_ID,
					       TCBP.MASTER_BUSI_ITEM_ID,
					       TBP.PRODUCT_CATEGORY,
					       TBP.PRODUCT_CATEGORY1
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
					       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
					       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP,
					       APP___PAS__DBUSER.T_CONTRACT_AGENT     TCA,
					       APP___PAS__DBUSER.T_POLICY_HOLDER      TPH,
					       APP___PAS__DBUSER.T_CUSTOMER           TC
					 WHERE 1 = 1
					   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
					   AND TCM.POLICY_CODE = TCA.POLICY_CODE
					   AND TCM.POLICY_CODE = TPH.POLICY_CODE
					   AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
					   AND TCA.IS_CURRENT_AGENT = 1
					   AND TCBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS 
					    ]]>
	    <if test=" liability_state != null and liability_state != ''  "><![CDATA[AND TCM.LIABILITY_STATE = #{liability_state}]]></if>
		<if test=" appntname != null and appntname != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{appntname}]]></if>
		<if test=" appntidno != null and appntidno != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{appntidno}]]></if>
    	<if test=" appntidtype != null and appntidtype != ''  "><![CDATA[AND TC.CUSTOMER_CERT_TYPE = #{appntidtype}]]></if>
    	<if test=" contno != null and contno != ''  "><![CDATA[AND TCM.POLICY_CODE = #{contno}]]></if>
    	<if test=" querytype != null and querytype != '' and querytype == '1'.toString()  "><![CDATA[AND TCM.SPECIAL_ACCOUNT_FLAG = '1' AND TBP.PRODUCT_CATEGORY1 IN ('20003','20004') ]]></if>
    	<if test=" querytype != null and querytype != '' and querytype == '2'.toString()  "><![CDATA[  AND ( TCM.SPECIAL_ACCOUNT_FLAG = '1' or exists (SELECT  'X' from
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD P,APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP WHERE P.BUSI_PROD_CODE=BP.PRODUCT_CODE_SYS AND P.POLICY_CODE = TCBP.POLICY_CODE AND (P.BUSI_PROD_CODE in ('********','********','00Z01000','00Z01100') OR BP.TAX_EXTENSION_FLAG = '1'))) AND TBP.PRODUCT_CATEGORY1 IN ('20003','20004') ]]></if>
    	 <if test=" querytype != null and querytype != '' and querytype == '3'.toString()  "><![CDATA[AND TBP.PRODUCT_CATEGORY1 IN ('20003','20004') ]]></if>
    	 <if test=" querytype != null and querytype != '' and querytype == '4'.toString()  "><![CDATA[AND (TBP.product_code_sys NOT IN ('********','********','00Z01000','00Z01100') AND TBP.TAX_EXTENSION_FLAG != '1')  AND TBP.PRODUCT_CATEGORY1 IN ('20003','20004') ]]></if>
    	 <if test=" querytype == null or querytype == '' "><![CDATA[ AND TBP.PRODUCT_CATEGORY1 = '20003'  ]]></if>
    	<![CDATA[   ORDER BY TCM.APPLY_DATE DESC   ]]>
    	
	</select>
	<!-- TC30425 add POLICY_CHG_ID -->
	<select id="findInsuredPolicys" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			select cm.POLICY_RELATION_TYPE,cm.STATISTIC_CHANNEL,cm.POLICY_PWD, cm.MEDIA_TYPE, cm.APPLY_CODE, cm.INTEREST_MODE, cm.ORGAN_CODE, cm.CHANNEL_TYPE, cm.SALE_AGENT_NAME, 
			cm.INSURED_FAMILY, cm.POLICY_ID, cm.DERIVATION, cm.SUBINPUT_TYPE, 
			cm.BASIC_REMARK, cm.SALE_COM_CODE, cm.INPUT_DATE, cm.POLICY_TYPE, cm.EXPIRY_DATE, cm.PWD_INVALID_FLAG, cm.SUBMIT_CHANNEL, 
			cm.LIABILITY_STATE, cm.POLICY_CODE, cm.SALE_AGENT_CODE, cm.RERINSTATE_DATE, cm.BRANCH_CODE, cm.VALIDATE_DATE, 
			cm.AGENCY_CODE, cm.MONEY_CODE, cm.SERVICE_HANDLER_CODE, cm.APPLY_DATE, cm.INITIAL_VALIDATE_DATE, cm.WINNING_START_FLAG,
			cm.SUBMISSION_DATE, cm.SERVICE_HANDLER, cm.E_SERVICE_FLAG, cm.SERVICE_BANK_BRANCH, cm.LAPSE_DATE, cm.DC_INDI, 
			cm.SALE_TYPE, cm.INPUT_TYPE, cm.END_CAUSE, cm.ISSUE_DATE, cm.LAPSE_CAUSE, cm.DECISION_CODE, cm.TRUST_BUSI_FLAG,
			cm.AGENT_ORG_ID, cm.SERVICE_BANK, cm.BANK_AGENCY_FLAG,cm.SUSPEND_DATE, cm.INITIAL_PREM_DATE, cm.SUSPEND_CAUSE, cm.LANG_CODE, cm.FORMER_ID, cm.IS_SELF_INSURED,cm.RELATION_POLICY_CODE,
			cm.POLICY_FLAG,cm.DOUBLE_MAINRISK_FLAG,cm.TAX_EXTENSION_SOURCE,cm.POLICY_PRD_FLAG,cm.GROUP_SALE_TYPE,cm.POLICY_REINSURE_FLAG,cm.MEDICAL_INSURANCE_CARD
			,(select cpc.POLICY_CHG_ID
	            from dev_pas.t_cs_policy_change cpc,
	                 dev_pas.t_cs_accept_change cac
	           where cac.accept_id = cpc.accept_id
	             and cpc.policy_code = cm.policy_code
                 and cpc.policy_id = cm.policy_id
	             and cac.accept_status in
	               ('06', '07', '08', '09', '10', '11', '13','20')
                 and rownum = 1) POLICY_CHG_ID
  from dev_pas.t_insured_list il, dev_pas.t_contract_master cm
 where il.policy_id = cm.policy_id
   and il.customer_id = #{customer_id}
		]]>
	</select>
	
	<!-- 查询保单状态与描述 -->
	<select id="PA_findContStateAndStateReason" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			  SELECT TCM.POLICY_CODE,
	                 TCM.POLICY_ID,
	                 TCM.LIABILITY_STATE,
	                 (SELECT Z.STATUS_NAME
	                    FROM APP___PAS__DBUSER.T_LIABILITY_STATUS Z
	                   WHERE Z.STATUS_CODE = TCM.LIABILITY_STATE) AS LIABILITY_NAME,
	                 TCM.END_CAUSE,
	                 (SELECT Z.CAUSE_NAME
	                    FROM APP___PAS__DBUSER.T_END_CAUSE Z
	                   WHERE Z.CAUSE_CODE = TCM.END_CAUSE) AS END_CAUSE_NAME,
	                 TCM.LAPSE_CAUSE,
	                 (SELECT Z.CAUSE_DESC
	                    FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Z
	                   WHERE Z.CAUSE_CODE = TCM.LAPSE_CAUSE) AS LAPSE_CAUSE_NAME 
	            FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM
	           WHERE 1 = 1
                 AND TCM.POLICY_CODE = #{policy_code} ]]>
	</select>
	
	<!-- 查询保单下是否存在可操作满期不续保的保单 -->
	<select id="PA_queryENByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				 SELECT TCM.POLICY_CODE,TCBP.BUSI_PROD_CODE
				          FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE C,
				               DEV_PAS.T_CONTRACT_BUSI_PROD                 TCBP,
							   DEV_PAS.T_CONTRACT_MASTER                    TCM,
				               APP___PDS__DBUSER.T_BUSINESS_PRODUCT         P
				         where C.BUSINESS_PRD_ID = P.BUSINESS_PRD_ID
				           AND P.PRODUCT_CODE_SYS = TCBP.BUSI_PROD_CODE
						   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
				           AND C.SERVICE_CODE = 'EN'
						   AND TCM.LIABILITY_STATE = '1'
				           AND SYSDATE > TCM.VALIDATE_DATE
				           AND TCM.POLICY_CODE = #{policy_code}
		]]>
	</select>

	<!-- 查询双主险各自保单状态 -->
	<select id="PA_findDoubleMasterRisksStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		  select a.LIABILITY_STATE as state1, b.LIABILITY_STATE as state2 
     		from APP___PAS__DBUSER.T_CONTRACT_MASTER a left join APP___PAS__DBUSER.T_CONTRACT_MASTER b 
     		on a.relation_policy_code = b.policy_code
     		where  a.policy_code=#{policy_code}
		]]>
	</select>
	
	<!-- 查询单条 -->
	<select id="PA_findContractMasterChannel" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.STATISTIC_CHANNEL,
       A.POLICY_PWD,
       A.MEDIA_TYPE,
       A.APPLY_CODE,
       A.INTEREST_MODE,
       A.ORGAN_CODE,
       A.CHANNEL_TYPE,
       A.SALE_AGENT_NAME,
       A.INSURED_FAMILY,
       A.POLICY_ID,
       A.DERIVATION,
       A.SUBINPUT_TYPE,
       A.BASIC_REMARK,
       A.SALE_COM_CODE,
       A.INPUT_DATE,
       A.POLICY_TYPE,
       A.EXPIRY_DATE,
       A.PWD_INVALID_FLAG,
       A.SUBMIT_CHANNEL,
       A.LIABILITY_STATE,
       A.POLICY_CODE,
       A.SALE_AGENT_CODE,
       A.RERINSTATE_DATE,
       A.BRANCH_CODE,
       A.VALIDATE_DATE,
       A.AGENCY_CODE,
       A.MONEY_CODE,
       A.SERVICE_HANDLER_CODE,
       A.APPLY_DATE,
       A.INITIAL_VALIDATE_DATE,
       A.SUBMISSION_DATE,
       A.SERVICE_HANDLER,
       A.E_SERVICE_FLAG,
       A.SERVICE_BANK_BRANCH,
       A.LAPSE_DATE,
       A.DC_INDI,
       A.SALE_TYPE,
       A.INPUT_TYPE,
       A.END_CAUSE,
       A.ISSUE_DATE,
       A.LAPSE_CAUSE,
       A.DECISION_CODE,
       A.DOUBLE_MAINRISK_FLAG,
       A.AGENT_ORG_ID,
       A.SERVICE_BANK,
       A.BANK_AGENCY_FLAG,
       A.SUSPEND_DATE,
       A.INITIAL_PREM_DATE,
       A.SUSPEND_CAUSE,
       A.LANG_CODE,
       A.FORMER_ID,
       A.RELATION_POLICY_CODE,
       A.IS_SELF_INSURED,
       A.GROUP_SALE_TYPE,
       A.APPLY_TIME,
       B.SALES_CHANNEL_NAME
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
  DEV_PAS.T_SALES_CHANNEL B
 WHERE 1 = 1
 AND B.SALES_CHANNEL_CODE = A.CHANNEL_TYPE ]]>
		<include refid="PA_contractMasterWhereCondition" />
	</select>
	
	<update id="updateCsContractMasterLapseLoanSuspendDate">
		<![CDATA[ 
		update dev_pas.t_cs_contract_master ccm
		   set ccm.lapse_loan_suspend_date = #{lapse_loan_suspend_date},
			   ccm.update_time             = SYSDATE,
			   ccm.update_timestamp        = CURRENT_TIMESTAMP
		 where ccm.old_new = '1'
		   and ccm.policy_chg_id =
			   (select b.policy_chg_id
				  from (
						select cpc.policy_chg_id, cac.accept_time
						  from dev_pas.t_cs_policy_change cpc,
							   dev_pas.t_cs_accept_change cac
						 where cpc.policy_id = #{policy_id}
						   and cpc.service_code = 'LN'
						   and cac.accept_id = cpc.accept_id
						   and cac.accept_status = '18'
						 order by cac.accept_time desc
				) b
		 where rownum = 1)
		 ]]>
		
	</update>
	  <!-- 转保单接口返回字段 -->
  <select id="PA_findTurnPolicy" resultType="java.util.Map"
    parameterType="java.util.Map">
    <![CDATA[
    SELECT  tcbp.busi_prod_code, /*险种代码*/
      (SELECT  a.BUSI_PROD_CODE
        FROM  APP___PAS__DBUSER.t_contract_busi_prod a 
         WHERE a.BUSI_ITEM_ID  =  TCBP.master_busi_item_id
         ) main_risk_code, /*所属主险的险种代码 */
       tcbp.maturity_date, /*满期日*/
       tce.pay_due_date,/*下次缴费对应日*/
       PLD.INTERNAL_ID /*精算产品代码*/
  FROM APP___PAS__DBUSER.t_contract_busi_prod tcbp
  LEFT JOIN APP___PAS__DBUSER.t_contract_extend tce
    ON tcbp.busi_item_id = tce.busi_item_id
   AND tcbp.policy_id = tce.policy_id
  LEFT JOIN APP___PAS__DBUSER.T_PRODUCT_LIFE PLD /*精算产品/责任组*/
    ON PLD.BUSINESS_PRD_ID = tcbp.busi_prd_id
 WHERE tcbp.apply_code =  #{apply_code}
    ]]>
  </select>
  
  	<!-- 查询重投之前的保单信息 -->
  	<select id="PA_queryBeforReApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  	SELECT A.POLICY_CODE
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
			 WHERE A.POLICY_ID = (SELECT A.FORMER_ID
			                        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
			                       WHERE A.POLICY_CODE = #{policy_code})
		]]>
	</select> 
  <!--by zhangrx01_wb 根据 保单号 查询保单信息和投保人信息 -->
	<select id="queryContAndHolderByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.POLICY_CODE,B.CUSTOMER_NAME,B.CUSTOMER_GENDER,B.CUSTOMER_CERT_TYPE,B.CUSTOMER_CERTI_CODE,B.CUSTOMER_BIRTHDAY
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A 
      			LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER C ON A.POLICY_ID=C.POLICY_ID 
				LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER B ON B.CUSTOMER_ID=C.CUSTOMER_ID
			WHERE A.POLICY_CODE=#{policy_code} AND ROWNUM=1
		]]>
	</select>
	
	<!--by zhangrx01_wb 根据查询条件查询有效保单 -->
	<select id="queryValidOrPeriodOfGracePolicyList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select distinct tcm.policy_code 
		from dev_pas.T_CONTRACT_MASTER tcm ,dev_pas.T_POLICY_HOLDER tph , dev_pas.T_CUSTOMER tc ,dev_pas.T_CONTRACT_AGENT tca 
		where tph.APPLY_CODE = tcm.APPLY_CODE and tph.CUSTOMER_ID = tc.CUSTOMER_ID and tcm.policy_code = tca.policy_code
	    AND not exists (Select 'X' From dev_pas.t_Contract_Relation CR,dev_pas.t_Contract_Busi_Prod cbp,dev_pds.t_Business_Product bp where CR.Sub_Policy_id = TCM.Policy_Id and cbp.busi_item_id =CR.Sub_Busi_Item_Id and cbp.busi_prd_id = bp.business_prd_id and CR.RELATION_TYPE = 1 and bp.acc_risk_single_insure_flag = 1)
		and tcm.LIABILITY_STATE = 1 /* 保单状态为“有效”状态 */ and tca.IS_CURRENT_AGENT = 1 /* 当前代理人 */ and rownum<2000]]>
		<if test=" agent_code != null and agent_code != ''  ">
			<![CDATA[and tca.AGENT_CODE = #{agent_code} ]]>
		</if>
		<if test=" customer_name != null and customer_name != ''  ">
			<![CDATA[and tc.CUSTOMER_NAME = #{customer_name}]]>
		</if>
		<if test=" sex != null and sex != ''  ">
			<![CDATA[and tc.CUSTOMER_GENDER = #{sex}]]>
		</if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  ">
			<![CDATA[and tc.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]>
		</if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  ">
			<![CDATA[and tc.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]>
		</if>
		<if test=" customer_birthday != null and customer_birthday != ''  ">
			<![CDATA[and tc.CUSTOMER_BIRTHDAY = #{customer_birthday}]]>
		</if>
		<if test=" policy_code != null and policy_code != ''  ">
			<![CDATA[and tcm.policy_code = #{policy_code}]]>
		</if>
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = tcm.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		</if>
		<![CDATA[union
		select distinct tcm.policy_code 
		from dev_pas.T_CONTRACT_MASTER tcm ,dev_pas.T_BENEFIT_INSURED  tbi , dev_pas.T_CUSTOMER tc ,dev_pas.T_CONTRACT_AGENT tca,dev_pas.T_INSURED_LIST til
		where tbi.policy_code = tcm.policy_code and tbi.INSURED_ID = til.list_id and til.CUSTOMER_ID =  tc.CUSTOMER_ID and tcm.policy_code = tca.policy_code
		and not exists (Select 'X' From dev_pas.t_Contract_Relation CR,dev_pas.t_Contract_Busi_Prod cbp,dev_pds.t_Business_Product bp where CR.Sub_Policy_id = TCM.Policy_Id and cbp.busi_item_id =CR.Sub_Busi_Item_Id and cbp.busi_prd_id = bp.business_prd_id and CR.RELATION_TYPE = 1 and bp.acc_risk_single_insure_flag = 1)
		and tcm.LIABILITY_STATE = 1 /* 保单状态为“有效”状态 */ and tca.IS_CURRENT_AGENT = 1 /* 当前代理人 */ and rownum<2000]]>
		<if test=" order_id != null and order_id != ''  ">
			<![CDATA[and tbi.ORDER_ID = #{order_id} ]]>
		</if>
		<if test=" agent_code != null and agent_code != ''  ">
			<![CDATA[and tca.AGENT_CODE = #{agent_code} ]]>
		</if>
		<if test=" customer_name != null and customer_name != ''  ">
			<![CDATA[and tc.CUSTOMER_NAME = #{customer_name}]]>
		</if>
		<if test=" sex != null and sex != ''  ">
			<![CDATA[and tc.CUSTOMER_GENDER = #{sex}]]>
		</if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  ">
			<![CDATA[and tc.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]>
		</if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  ">
			<![CDATA[and tc.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]>
		</if>
		<if test=" customer_birthday != null and customer_birthday != ''  ">
			<![CDATA[and tc.CUSTOMER_BIRTHDAY = #{customer_birthday}]]>
		</if>
		<if test=" policy_code != null and policy_code != ''  ">
			<![CDATA[and tcm.policy_code = #{policy_code}]]>
		</if>
		<if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		   <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE M.POLICY_CODE = CBP.POLICY_CODE
						 AND M.POLICY_CODE = tcm.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		</if>
	</select>
	<select id="findPolicyInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			select  tcm.ORGAN_CODE,
		    (select t.ORGAN_NAME from dev_pas.T_UDMP_ORG t where t.organ_code = tcm.ORGAN_CODE)ORGAN_NAME,
		    tcm.policy_code,
		    (select t.AGENT_CODE from dev_pas.T_CONTRACT_AGENT t where t.policy_code = tcm.POLICY_CODE and t.IS_CURRENT_AGENT = 1) AGENT_CODE,
		    tcm.EXPIRY_DATE,/*保障终止日期*/
		    case when 0 in( select b.COVER_PERIOD_TYPE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,APP___PDS__DBUSER.T_BUSINESS_PRODUCT B 
            where a.policy_code = tcm.policy_code and a.MASTER_BUSI_ITEM_ID is null and A.BUSI_PRD_ID = B.BUSINESS_PRD_ID) then 'Y' else 'N' end as COVER_PERIOD_TYPE,/*保单主险是否为长期险*/
		    TCM.VALIDATE_DATE,
		    (select A.STATUS_NAME from dev_pas.T_LIABILITY_STATUS A where A.STATUS_CODE = TCM.LIABILITY_STATE) LIABILITY_STATE_NAME,
		    (select case count(0) when 0 then 'N' else 'Y' end
	        from dev_pas.T_PREM t 
	        where t.FEE_SCENE_CODE = 'RN' 
	        and t.ARAP_FLAG = 1 
	        and t.FINISH_TIME is not null 
	        and t.policy_code = tcm.policy_code
	        and t.BANK_ACCOUNT = TPA.NEXT_ACCOUNT) RN,/*是否做过续费*/
		    TPA.NEXT_ACCOUNT_NAME,
		    tpa.NEXT_ACCOUNT_BANK,
		    ( select t.BANK_NAME from dev_pas.t_bank t where t.BANK_CODE = tpa.NEXT_ACCOUNT_BANK) NEXT_ACCOUNT_BANK_NAME,
		    TPA.NEXT_ACCOUNT,
		    tpa.ACCOUNT_NAME,
		    tpa.ACCOUNT_BANK,
		    ( select t.BANK_NAME from dev_pas.t_bank t where t.BANK_CODE = tpa.ACCOUNT_BANK) ACCOUNT_BANK_NAME,
		    tpa.ACCOUNT,
		    tcm.special_account_flag,
		    tcm.multi_mainrisk_flag,
		    (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_CONTRACT_AGENT TC,DEV_PAS.T_AGENT CA WHERE  TC.AGENT_CODE = CA.AGENT_CODE AND TC.IS_CURRENT_AGENT = '1' AND TC.POLICY_CODE = tcm.POLICY_CODE) as agent_channel,
		    (SELECT TA.AGENT_LEVEL FROM DEV_PAS.T_CONTRACT_AGENT TC,DEV_PAS.T_AGENT TA WHERE TC.AGENT_CODE = TA.AGENT_CODE AND TC.IS_CURRENT_AGENT = '1' AND TC.POLICY_CODE = tcm.POLICY_CODE) as ca_agent_level,
		    (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_CONTRACT_AGENT TC,DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = TC.AGENT_CODE AND TC.IS_CURRENT_AGENT = '1' AND TC.POLICY_CODE = tcm.POLICY_CODE) as tab_agent_level
			from dev_pas.T_CONTRACT_MASTER tcm,dev_pas.T_PAYER_ACCOUNT tpa
			where tcm.policy_id = tpa.policy_id
			and tcm.policy_code = #{policy_code} AND ROWNUM=1
		]]>
	</select>
	
	<!-- 保单下有可转换险种，但其中有险种操作过满期不续保、且也不存在其他的险种可以转换-->
	<select id="queryNaturalYearIncome" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			select nyl.income
  				from dev_pas.t_Natural_Year_Income nyl, dev_pas.t_cs_accept_change ac
 				where nyl.customer_id in
       				(select ph.customer_id
         			 	from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_policy_holder ph
        				where pc.policy_chg_id = ph.policy_chg_id
           				and ph.old_new = '1'
           				and pc.change_id = ac.change_id
        			   union
        				select il.customer_id
          				from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_insured_list il
         				where pc.policy_chg_id = il.policy_chg_id
           				and il.old_new = '1'
           				and pc.change_id = ac.cancel_id
        			   union
        				select cb.customer_id
          				from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_contract_bene cb
         				where pc.policy_chg_id = cb.policy_chg_id
           				and cb.old_new = '1'
           				and pc.change_id = ac.change_id)
   				and ac.accept_code = #{accept_code}
		]]>		
	</select>
	<!--根据客户五要素查询保单信息-->
	<select id="queryPolicayInfoByCustmoerInfo" parameterType="java.util.Map" resultType="java.util.Map">
	
	<if test="customerType != null and customerType != ''  and customerType=='2'.toString()">
		<![CDATA[
	  	 select a.POLICY_CODE,a.POLICY_ID,a.VALIDATE_DATE,a.ORGAN_CODE,a.INSURED_FAMILY,
       			a.APPLY_CODE,a.DECISION_CODE,a.APPLY_DATE,a.POLICY_TYPE,a.INITIAL_PREM_DATE,a.LIABILITY_STATE,
     		    a.LAPSE_CAUSE,a.LAPSE_DATE,a.RERINSTATE_DATE,a.SUSPEND_CAUSE,a.SUSPEND_DATE,a.END_CAUSE,
       			a.EXPIRY_DATE,a.CHANNEL_TYPE,a.ISSUE_DATE,a.AGENCY_CODE, a.DERIVATION,a.SUBINPUT_TYPE,
       			a.POLICY_FLAG,a.DOUBLE_MAINRISK_FLAG, a.POLICY_REINSURE_FLAG, a.REINSURED_TIMES,c.customer_name,
       			(SELECT SUM(PA.FEE_AMOUNT) FROM APP___PAS__DBUSER.V_PREM_ALL PA WHERE PA.POLICY_CODE = a.policy_code AND PA.FEE_SCENE_CODE = 'NB' AND PA.FEE_STATUS = '16' AND PA.DUE_FEE_TYPE = '1') AS INITIAL_TOTAL_PREM_AF
 		 from APP___PAS__DBUSER.T_CONTRACT_MASTER a,
       		  APP___PAS__DBUSER.T_POLICY_HOLDER   b,
       		  APP___PAS__DBUSER.T_CUSTOMER c,
       		  APP___PAS__DBUSER.T_INSURED_LIST    d
 		 where a.policy_code = b.policy_code  
 		   and a.policy_id=d.policy_id
 		   and (b.customer_id = c.customer_id or d.customer_id=c.customer_id)]]>
 		</if>
 		<if test="customerType != null and customerType != ''  and customerType=='1'.toString()">
		<![CDATA[
	  	 select a.POLICY_CODE,a.POLICY_ID,a.VALIDATE_DATE,a.ORGAN_CODE,a.INSURED_FAMILY,
       			a.APPLY_CODE,a.DECISION_CODE,a.APPLY_DATE,a.POLICY_TYPE,a.INITIAL_PREM_DATE,a.LIABILITY_STATE,
     		    a.LAPSE_CAUSE,a.LAPSE_DATE,a.RERINSTATE_DATE,a.SUSPEND_CAUSE,a.SUSPEND_DATE,a.END_CAUSE,
       			a.EXPIRY_DATE,a.CHANNEL_TYPE,a.ISSUE_DATE,a.AGENCY_CODE, a.DERIVATION,a.SUBINPUT_TYPE,
       			a.POLICY_FLAG,a.DOUBLE_MAINRISK_FLAG, a.POLICY_REINSURE_FLAG, a.REINSURED_TIMES,c.customer_name,
       			(SELECT SUM(PA.FEE_AMOUNT) FROM APP___PAS__DBUSER.V_PREM_ALL PA WHERE PA.POLICY_CODE = a.policy_code AND PA.FEE_SCENE_CODE = 'NB' AND PA.FEE_STATUS = '16' AND PA.DUE_FEE_TYPE = '1') AS INITIAL_TOTAL_PREM_AF
 		 from APP___PAS__DBUSER.T_CONTRACT_MASTER a,
       		  APP___PAS__DBUSER.T_POLICY_HOLDER   b,
       		  APP___PAS__DBUSER.T_CUSTOMER c
 		 where a.policy_code = b.policy_code  
 		   and b.customer_id = c.customer_id]]>
 		</if>
 		 <if test="name != null and name != ''  ">
			<![CDATA[and c.CUSTOMER_NAME = #{name}]]>
		</if>
		<if test="sex != null and sex != ''  ">
			<![CDATA[and c.CUSTOMER_GENDER = #{sex}]]>
		</if>
		<if test="birthday != null and birthday != ''  ">
			<![CDATA[and c.CUSTOMER_BIRTHDAY = to_date(#{birthday}, 'YYYY-MM-DD')]]>
		</if>
		<if test="idType != null and idType != ''  ">
			<![CDATA[and c.CUSTOMER_CERT_TYPE = #{idType}]]>
		</if>
		<if test="idNo != null and idNo != ''  ">
			<![CDATA[and c.CUSTOMER_CERTI_CODE = #{idNo}]]>
		</if>				
	</select>
	<!-- 查询保全录入完成录入的年收入（既往保全用） -->
	<select id="queryNaturalYearIncomeJwaqy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
		select a.income
  		  from (select t.income
          		  from dev_pas.t_Natural_Year_Income t
         		 where t.customer_id = #{customer_id}
         		   and t.accept_id = #{accept_id}        
         		 order by t.update_time desc) a
         where rownum = 1
		]]>		
	</select>
	<!-- 根据保单号查询保全信息 -->
		<select id="findCsAcceptInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			select tcac.change_id,
                   tcac.accept_id,
                   tph.customer_id,
                   tcpc.policy_id,
                   tcpc.policy_chg_id,
                   tcm.validate_date
              from dev_pas.t_contract_master  tcm,
                   dev_pas.t_cs_policy_change tcpc,
                   dev_pas.t_cs_accept_change tcac,
                   dev_pas.t_policy_holder    tph
             where tcm.policy_code = tcpc.policy_code
               and tcpc.change_id = tcac.change_id
               and tph.policy_code = tcm.policy_code
               and tcac.service_code = 'RL'
               and rownum = 1
               and tcm.policy_code = #{policy_code}
		]]>		
	</select>
	<!-- 查询保单信息签单日期及保单当前代理人所属渠道 -->
		<select id="findPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			SELECT TCM.ISSUE_DATE, TA.AGENT_CHANNEL
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM,
			       DEV_PAS.T_CONTRACT_AGENT  TCA,
			       DEV_PAS.T_AGENT           TA
			 WHERE TCM.POLICY_CODE = TCA.POLICY_CODE
			   AND TCA.AGENT_CODE = TA.AGENT_CODE
			   AND TCA.IS_CURRENT_AGENT = 1
			   AND TCM.POLICY_CODE = #{policy_code}
		]]>		
	</select>
	
		<!-- 查询保单特殊账户标识-->
		<select id="querySpecialaccountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			SELECT TCM.SPECIAL_ACCOUNT_FLAG
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM
			 WHERE 1=1
			   AND TCM.POLICY_CODE = #{policy_code}
		]]>		
	</select>
	
	<!-- 代理人信息查询接口查询保单号数量配置 -->
	<select id="findPolicyCountForContractAgentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[     SELECT T.CONSTANTS_ID, T.CONSTANTS_KEY, T.CONSTANTS_VALUE, T.CONSTANTS_DESC, T.SUB_ID 
			            FROM DEV_PAS.T_CONSTANTS_INFO T 
			           WHERE T.CONSTANTS_KEY = 'R06401900832_POLICYCOUNT'   ]]>		
	</select>

	<!-- 根据客户三要素查询客户作为投保人或被保人名下保单-->
	<select id="queryContractMasterByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[    
		SELECT distinct TCM.POLICY_CODE,TCM.POLICY_ID,TCM.VALIDATE_DATE,(select ls.status_name from dev_pas.t_liability_status ls where ls.status_code = TCM.LIABILITY_STATE) status_name,TCM.ISSUE_DATE
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
       APP___PAS__DBUSER.T_INSURED_LIST    TIL,
       APP___PAS__DBUSER.T_CUSTOMER        TC,
	   APP___PAS__DBUSER.T_POLICY_ACCOUNT T
 WHERE TCM.POLICY_CODE = TIL.POLICY_CODE
   AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
   AND T.POLICY_ID = TCM.POLICY_ID 
   AND (T.ACCOUNT_TYPE = '11' OR T.ACCOUNT_TYPE = '2')
   AND TC.CUSTOMER_NAME = #{customer_name}
   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
   AND TC.CUSTOMER_CERTI_CODE = #{customer_cert_code} ]]>
		<if test="liability_state != null and liability_state != ''  "><![CDATA[AND TCM.LIABILITY_STATE = #{liability_state}]]></if>
		<if test=" policy_code  != null  and policy_code  != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>  
<![CDATA[	 
UNION
SELECT TCM.POLICY_CODE,TCM.POLICY_ID,TCM.VALIDATE_DATE,(select ls.status_name from dev_pas.t_liability_status ls where ls.status_code = TCM.LIABILITY_STATE) status_name,TCM.ISSUE_DATE 
       FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
            APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
            APP___PAS__DBUSER.T_CUSTOMER TC,
						DEV_PAS.T_POLICY_ACCOUNT T
    WHERE TCM.POLICY_CODE = TPH.POLICY_CODE
   AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
    and T.POLICY_ID = TCM.POLICY_ID 
		AND (T.ACCOUNT_TYPE = '11' OR T.ACCOUNT_TYPE = '2')
   AND TC.CUSTOMER_NAME = #{customer_name}
   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
   AND TC.CUSTOMER_CERTI_CODE = #{customer_cert_code}]]>
		<if test="liability_state != null and liability_state != ''  "><![CDATA[AND TCM.LIABILITY_STATE = #{liability_state}]]></if>
		<if test=" policy_code  != null  and policy_code  != '' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>  
	</select>
	
	<!-- 更新保单主表新型产品通知接收方式 -->
	<update id="PA_updateContractMasterNotificationReceiveMethod" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				NOTIFICATION_RECEIVE_METHOD = #{notification_receive_method,jdbcType=NUMERIC},
			</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<!-- 更新保单主表轨迹表新型产品通知接收方式 -->
	<update id="PA_updateContractMasterLogNotificationReceiveMethod" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG ]]>
		<set>
			<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				NOTIFICATION_RECEIVE_METHOD = #{notification_receive_method,jdbcType=NUMERIC},
			</trim>
		</set>
		<![CDATA[ WHERE  LOG_ID = #{log_id} ]]>
	</update>
	
	<!-- 更新保全主表新型产品通知接收方式 -->
	<update id="PA_updateCsContractMasterNotificationReceiveMethod" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				NOTIFICATION_RECEIVE_METHOD = #{notification_receive_method,jdbcType=NUMERIC},
			</trim>
		</set>
		<![CDATA[ WHERE  LOG_ID = #{log_id} ]]>
	</update>
	
	<select id="PA_queryPolicyInfoByCustomInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT cm.POLICY_CODE, cm.APPLY_DATE
        FROM APP___PAS__DBUSER.T_CUSTOMER c
        INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER ph ON c.CUSTOMER_ID = ph.CUSTOMER_ID
        INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER cm ON ph.POLICY_CODE = cm.POLICY_CODE
        WHERE  c.MOBILE_TEL = #{mobile_tel} ]]>
        <if test=" customer_name  != null and customer_name != ''"><![CDATA[AND c.CUSTOMER_NAME = #{customer_name} ]]></if>
        <![CDATA[ 
        UNION
        SELECT cm.POLICY_CODE, cm.APPLY_DATE
        FROM APP___PAS__DBUSER.T_CUSTOMER c
        INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST il ON c.CUSTOMER_ID = il.CUSTOMER_ID
        INNER JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED bi ON il.LIST_ID = bi.INSURED_ID
        INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER cm ON bi.POLICY_CODE = cm.POLICY_CODE
        WHERE  c.MOBILE_TEL = #{mobile_tel}  ]]>
       <if test=" customer_name  != null and customer_name != ''"><![CDATA[AND c.CUSTOMER_NAME = #{customer_name} ]]></if>	
	</select>
	<!-- 查询客户名下存在作为投保人的“失效”状态的税优保单的数量 -->
	<select id="findPolicyForInvalidityTax" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_contract_master a
			 inner join dev_pas.t_policy_holder b
			    on a.policy_id = b.policy_id
			 where 1 = 1
			   and a.liability_state = '4'
			   and a.special_account_flag = '2'
			   and b.customer_id = #{customer_Id}
		]]>
	</select>
	<!-- 查询客户名下存在作为投保人非“终止”状态的税优保单，且证件类型不为“居民身份证、港澳居民来往内地通行证、台湾居民来往内地通行证、外国人永久居留证、护照、军官证”保单的数量 -->
	<select id="findPolicyForNonTerminationTax" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_contract_master a
			 left join dev_pas.t_cs_policy_holder b
			    on a.policy_id = b.policy_id
			 left join dev_pas.t_cs_customer c
			    on b.customer_id = c.customer_id
			   and b.change_id = c.change_id
			 where 1 = 1
			   and a.liability_state != '3'
			   and a.special_account_flag = '2'
			   and b.customer_id = #{customer_id}
			   and c.accept_id = #{accept_id}
			   and b.old_new = '1'
			   and c.old_new = '1'
			   and c.customer_cert_type not in ('0','i','d','e','1','2')
		]]>
	</select>
	<!-- 查询客户名下存在作为被保人非“终止”状态的税优保单，且证件类型为“港澳台居民居住证”保单的数量 -->
	<select id="findPolicyForNonTerminationTaxAsInsured" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_contract_master a
			  left join dev_pas.t_cs_insured_list b
			    on a.policy_id = b.policy_id
			  left join dev_pas.t_cs_customer c
			    on b.customer_id = c.customer_id
			 where 1 = 1
			   and a.liability_state != '3'
			   and a.special_account_flag = '2'
			   and b.customer_id = #{customer_id}
			   and c.accept_id = #{accept_id}
			   and b.old_new = '1'
			   and c.old_new = '1'
			   and c.customer_cert_type = 'h'
		]]>
	</select>
	
		<!-- 查询客户实际领取数量 -->
	<select id="PA_findCountAGService" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1)
		       FROM DEV_PAS.T_CS_ACCEPT_CHANGE T, DEV_PAS.T_CS_POLICY_CHANGE P
		       WHERE T.ACCEPT_ID = P.ACCEPT_ID
              ]]>
		<if test=" policy_id  != null "><![CDATA[AND P.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND P.POLICY_CODE = trim(#{policy_code}) ]]></if>
		    <![CDATA[   AND T.SERVICE_CODE = 'AG'
		       AND T.ACCEPT_STATUS = '18'
		       	]]>
	</select>
	
	  <!--根据投保人客户ID查询保单信息 -->
	<select id="PA_queryPolicyInfoByCustomerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT DISTINCT A.POLICY_CODE,A.ORGAN_CODE
            FROM DEV_PAS.T_CONTRACT_MASTER A, DEV_PAS.T_POLICY_HOLDER H
            WHERE 1 = 1
		   AND A.POLICY_ID = H.POLICY_ID]]>
		   <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND H.CUSTOMER_ID = #{customer_id} ]]></if>
	</select>
	
	<select id="findPolicyCodes" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			select ROWNUM RN, T.* from (select distinct A.POLICY_CODE,A.ORGAN_CODE,F.CUSTOMER_ID,G.CUSTOMER_NAME,G.CUSTOMER_CERTI_CODE,
			(SELECT max(nvl(b.risk_score, 0))
		          FROM dev_pas.t_contract_busi_prod b
		         where b.master_busi_item_id is null
		           and b.policy_code = a.policy_code) master_risk_score
			 from APP___PAS__DBUSER.T_CONTRACT_AGENT E,]]>
          APP___PAS__DBUSER.T_INSURED_LIST F,
          <if test=" organ_code_area != null and organ_code_area != ''  "><![CDATA[APP___PAS__DBUSER.T_AGENT          TA,
       	  APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,]]></if>
          APP___PAS__DBUSER.T_CUSTOMER G,
          APP___PAs__DBUSER.T_CONTRACT_MASTER A
          right join APP___PAS__DBUSER.t_Contract_Busi_Prod D ON  D.POLICY_CODE = A.Policy_Code
          <if test=" risk_lievel!=null">
		 <![CDATA[right join DEV_PAS.T_RISK_LEVEL_CONFIG T on T.IS_VALID = '1'  ]]></if>
          <if test=" is_claim != null "><![CDATA[ left join APP___CLM__DBUSER.t_Contract_Master B on A.Policy_Code = B.Policy_Code
          left join APP___CLM__DBUSER.t_claim_case C on c.case_id = b.case_id and B.Cur_Flag = '1']]></if>
          where A.LIABILITY_STATE = '1'
          <if test=" risk_score != null and risk_score != ''  "><![CDATA[and D.Policy_Code = A.Policy_Code ]]></if>
          and E.Policy_Code = A.Policy_Code
          and F.Policy_Code = A.Policy_Code
          and F.Customer_Id = G.CUSTOMER_ID
          <if test=" start_date != null and start_date != ''  "><![CDATA[ and  A.VALIDATE_DATE >= #{start_date} ]]></if>
          <if test=" end_date != null and end_date != ''  "><![CDATA[ and  A.VALIDATE_DATE <= #{end_date} ]]></if>
  		  <if test=" is_claim == 0 "><![CDATA[ 
          and C.CASE_STATUS not in( '10','20','21') ]]></if>
          <if test=" is_claim == 1 "><![CDATA[ 
  		  	and (B.Log_Id is null
        	or C.CASE_STATUS in( '10','20','21')) ]]></if>
  		  <if test=" certi_code != null and certi_code != ''  "><![CDATA[ and G.CUSTOMER_ID_CODE = #{certi_code} ]]></if>
  		  <if test=" risk_score != null and risk_score != ''  ">
  		  <![CDATA[ and D.RISK_SCORE > #{risk_score} ]]></if>
  		  <if test=" agent_code != null and agent_code != ''  "><![CDATA[ and E.AGENT_CODE = #{agent_code} ]]></if>
  		  <if test=" organ_code != null and organ_code != ''  "><![CDATA[ and A.ORGAN_CODE like '${organ_code}%' ]]></if>
  		  <if test=" organ_code_area != null and organ_code_area != ''  "><![CDATA[ and TA.GROUP_CODE = #{organ_code_area}
  		  and  TA.AGENT_CODE = TCA.AGENT_CODE and  TCA.Policy_Code = A.Policy_Code ]]></if>
  		  <if test="risk_lievel!=null and risk_lievel == '0'.toString()   "><![CDATA[ AND T.RISK_SCORE_MAX > D.RISK_SCORE 
        and T.RISK_SCORE_MIN <= D.RISK_SCORE and T.IS_VALID = '1' AND D.POLICY_CODE = A.Policy_Code ]]></if>
  		  <if test="risk_lievel!=null and risk_lievel == '1'.toString()  "><![CDATA[ AND T.RISK_SCORE_MAX_SECONDARY > D.RISK_SCORE 
        and T.RISK_SCORE_MIN_SECONDARY <= D.RISK_SCORE and T.IS_VALID = '1' AND D.POLICY_CODE = A.Policy_Code]]></if>
  		  <if test="risk_lievel!=null and risk_lievel == '2'.toString() "><![CDATA[ AND T.RISK_SCORE_MAX_HIGH >= D.RISK_SCORE 
        and T.RISK_SCORE_MIN_HIGH <= D.RISK_SCORE and T.IS_VALID = '1' AND D.POLICY_CODE = A.Policy_Code ]]></if>
        <![CDATA[ )  T ORDER BY t.master_risk_score desc]]>
	</select>
	
	<!--根据保单号查询续保/转投前的保单信息 -->
	<select id="PA_findFirstPolicyInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select t.policy_code from APP___PAS__DBUSER.t_contract_master t where POLICY_ID =
			(select FORMER_ID from APP___PAS__DBUSER.t_contract_master tcm where tcm.POLICY_CODE =#{policy_code})
			]]>
	</select>
	
	<!-- 按索引查询操作 -->
	<select id="findRiskScoreByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select D.Policy_Code,D.BUSI_PROD_CODE,D.risk_score,T.RISK_SCORE_MAX,
		T.RISK_SCORE_MIN,T.RISK_SCORE_MAX_SECONDARY,T.RISK_SCORE_MIN_SECONDARY,T.RISK_SCORE_MAX_HIGH,T.RISK_SCORE_MIN_HIGH 
		from  dev_pas.t_Contract_Busi_Prod D, DEV_PAS.T_RISK_LEVEL_CONFIG T 
		where T.IS_VALID = '1'
    	and D.Master_Busi_Item_Id is null ]]>
		 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and D.Policy_Code = #{policy_code} ]]></if>
		 <if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ and D.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		 <![CDATA[order by D.risk_score desc]]>
	</select>
	<!-- 查询保单状态及关联类型 -->
	<select id="findPolicyCodeRelationType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT CM.RELATION_POLICY_CODE,CM.POLICY_RELATION_TYPE,(select t.LIABILITY_STATE from DEV_PAS.T_CONTRACT_MASTER t where t.policy_code = CM.RELATION_POLICY_CODE) as LIABILITY_STATE
			FROM DEV_PAS.T_CONTRACT_MASTER CM
            WHERE CM.POLICY_CODE = #{policy_code}
            AND CM.RELATION_POLICY_CODE IS NOT NULL]]>
	</select>
	<!-- 查询关联万能险保单 -->
	<select id="findPolicyCodeRelationInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT CM.LIABILITY_STATE,CM.RELATION_POLICY_CODE, CM.POLICY_RELATION_TYPE,CM.POLICY_CODE
			FROM DEV_PAS.T_CONTRACT_MASTER CM
            WHERE CM.POLICY_CODE = #{policy_code}
            AND CM.RELATION_POLICY_CODE IS NOT NULL]]>
	</select>
	
	<!-- 客户信息维护-查询客户名下所有保单接口查询保单号数量配置 -->
	<select id="findPolicyCountForCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[     SELECT T.CONSTANTS_ID, T.CONSTANTS_KEY, T.CONSTANTS_VALUE, T.CONSTANTS_DESC, T.SUB_ID 
			            FROM DEV_PAS.T_CONSTANTS_INFO T 
			           WHERE T.CONSTANTS_KEY = 'R06401003111_POLICYCOUNT'   ]]>		
	</select>
	
	<!-- 查询保单是否发生过理赔 -->
	<select id="findCaseStatusByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
             select A.Policy_Code, B.Case_Status
           from APP___CLM__DBUSER.t_Contract_Master A
           left join APP___CLM__DBUSER.t_claim_case B
             on A.case_id = b.case_id
            and A.Cur_Flag = '1'
          where B.CASE_STATUS not in ('10', '20', '21')
            and A.Policy_Code = #{policy_code}]]>
	</select>
	<!-- 修改保单基本信息表保单的保单标识为'分配单' -->
	<update id="PA_updateContractMasterPolicyFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CONTRACT_MASTER ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_FLAG = '6'
			</trim>
		</set>
		<![CDATA[ WHERE  POLICY_CODE = #{policy_code} ]]>
	</update>
	
	<!-- 查询保单基本信息表所有数据 -->
	<select id="PA_findAllContractMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
             SELECT A.POLICY_ID, A.POLICY_CODE
               FROM DEV_PAS.T_CONTRACT_MASTER A
              WHERE A.POLICY_CODE IN ]]>
        <foreach item="item" index="index" collection="policy_list"  open="(" separator="," close=")">
           <![CDATA[#{item}]]>
        </foreach>
	</select>
	
		<!-- 查询保单基本信息表所有数据 -->
	<select id="PA_findContractMasterByPCAndCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
           SELECT TCM.POLICY_CODE,TCM.ORGAN_CODE
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM,
			       DEV_PAS.T_INSURED_LIST    TIL,
			       DEV_PAS.T_CUSTOMER        TC
			 WHERE TCM.POLICY_CODE = TIL.POLICY_CODE
			   AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			   AND TIL.LIST_ID IN (SELECT DISTINCT TBI.INSURED_ID
			                       FROM DEV_PAS.T_BENEFIT_INSURED TBI
			                      WHERE TBI.POLICY_CODE = TCM.POLICY_CODE
			                        AND TBI.ORDER_ID = 1)
			   AND TCM.POLICY_CODE = #{policy_code}
			   AND TC.CUSTOMER_NAME = #{customer_name}
			   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
			   AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]>
	</select>
	
	<!-- 根据保单号查询保单信息 -->
	<select id="PAS_querySleepContractMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">   
	<![CDATA[
             SELECT TCM.POLICY_CODE,
       TCM.LIABILITY_STATE,
       TCM.EXPIRY_DATE,
       TCM.LAPSE_DATE,
       TC.CUSTOMER_NAME,
       TC.CUSTOMER_ID,
       TC.MOBILE_TEL,
       TT.AGENT_NAME,
       TT.AGENT_CODE,
       TT.AGENT_MOBILE,
       (SELECT TA.AGENT_ORGAN_CODE
              FROM APP___PAS__DBUSER.T_AGENT TA
             WHERE TT.AGENT_CODE = TA.AGENT_CODE) AS AGENT_ORGAN_CODE, /*业务员所属管理机构*/
					 (SELECT T.ORGAN_NAME
							FROM APP___PAS__DBUSER.T_UDMP_ORG T, APP___PAS__DBUSER.T_AGENT TA
						 WHERE TT.AGENT_CODE = TA.AGENT_CODE
							 AND TA.AGENT_ORGAN_CODE = T.ORGAN_CODE) AS AGENT_ORGAN_NAME /*业务员所属管理机构名称*/
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
       APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
       APP___PAS__DBUSER.T_CUSTOMER TC,
       APP___PAS__DBUSER.T_CONTRACT_AGENT  TT
 WHERE 1=1
 AND TCM.POLICY_ID = TPH.POLICY_ID
 AND TCM.POLICY_ID = TT.POLICY_ID
 AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
 AND TT.IS_CURRENT_AGENT = 1 ]]>
 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	
	<!-- 根据保单号查询保单信息 -->
	<select id="PAS_querySleepInfoByAgentCodeANDAppntName" resultType="java.util.Map" parameterType="java.util.Map">   
	<![CDATA[
              SELECT TCM.POLICY_CODE
   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
        APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
        APP___PAS__DBUSER.T_CUSTOMER        TC,
        APP___PAS__DBUSER.T_CONTRACT_AGENT  TT
  WHERE 1 = 1
    AND TCM.POLICY_ID = TPH.POLICY_ID
    AND TCM.POLICY_ID = TT.POLICY_ID
    AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
    AND TT.IS_CURRENT_AGENT = 1
     ]]>
  <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TT.AGENT_CODE = #{agent_code} ]]></if>
  <if test=" appnt_name != null and appnt_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME = #{appnt_name} ]]></if>
	</select>
	
	<select id="PA_queryConstantsInfoByKey" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select t.constants_id,
		       t.constants_key,
		       t.constants_value,
		       t.constants_desc,
		       t.sub_id
		  from dev_pas.T_CONSTANTS_INFO t
		 where t.constants_key = #{constants_key} 
	  ]]>
	</select>
	<select id="PA_queryPolicyAreaByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select (SELECT TUO.ORGAN_NAME
                          FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
                         WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) BRANCH_CODE_NAME,
                       (SELECT TUO.ORGAN_PROVINCE
                          FROM APP___PAS__DBUSER.T_UDMP_ORG TUO
                         WHERE TUO.ORGAN_CODE = SUBSTR(TCM.ORGAN_CODE, 0, 4)) ORGAN_PROVINCE  
                         from dev_pas.t_contract_master tcm where tcm.policy_code= #{policy_code}
	  ]]>
	</select>
		<select id="PAS_queryContractMasterInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TCM.POLICY_CODE, TCM.POLICY_ID, TCM.SPECIAL_ACCOUNT_FLAG
  FROM DEV_PAS.T_CONTRACT_MASTER TCM
 WHERE 1 = 1
   AND EXISTS (SELECT 'X'
          FROM APP___PAS__DBUSER.T_PAY_PLAN TPP
         WHERE TPP.POLICY_ID = TCM.POLICY_ID
           AND TPP.PAY_PLAN_TYPE IN ('3', '4', '8', '10','11'))
	  ]]>
	  <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	<!-- 根据客户三要素和业务员号查询保单信息 -->
	<select id="PA_findPolicyInfosByCustomerAndAgent" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ SELECT TCM.POLICY_CODE,
				       TCM.POLICY_ID,
				       TCM.ORGAN_CODE,
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T
				         WHERE T.ORGAN_CODE = TCM.ORGAN_CODE
				           AND ROWNUM = 1) ORGAN_NAME,
				       TT.AGENT_CODE,
				       (SELECT TA.AGENT_ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_AGENT TA
				         WHERE TT.AGENT_CODE = TA.AGENT_CODE) AS AGENT_ORGAN_CODE,
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T, APP___PAS__DBUSER.T_AGENT TA
				         WHERE TT.AGENT_CODE = TA.AGENT_CODE
				           AND TA.AGENT_ORGAN_CODE = T.ORGAN_CODE
				           AND ROWNUM = 1) AS AGENT_ORGAN_NAME,
				       (SELECT TA.SALES_ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_AGENT TA
				         WHERE TA.AGENT_CODE = TT.AGENT_CODE) SALES_ORGAN_CODE,
				       (SELECT TSO.SALES_ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO,
				               APP___PAS__DBUSER.T_AGENT       TA
				         WHERE TSO.SALES_ORGAN_CODE = TA.SALES_ORGAN_CODE
				           AND TA.AGENT_CODE = TT.AGENT_CODE
				           AND ROWNUM = 1) SALES_ORGAN_NAME,
				       TCM.EXPIRY_DATE,
				       TCM.VALIDATE_DATE,
				       TCM.LIABILITY_STATE,
				       TCM.LAPSE_CAUSE,
				       TPAT.NEXT_ACCOUNT_NAME,
				       NVL((SELECT BT.STD_BANK_CODE
				             FROM APP___PAS__DBUSER.T_BANK BT
				            WHERE BT.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK),TPAT.NEXT_ACCOUNT_BANK) NEXT_ACCOUNT_BANK,
				       (SELECT BT.BANK_NAME
				          FROM APP___PAS__DBUSER.T_BANK BT
				         WHERE BT.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK) NEXT_BANK_NAME,    
				       TPAT.NEXT_ACCOUNT,
				       TPAT.ACCOUNT_NAME,
				       NVL((SELECT BT.STD_BANK_CODE
				             FROM APP___PAS__DBUSER.T_BANK BT
				            WHERE BT.BANK_CODE = TPAT.ACCOUNT_BANK),TPAT.ACCOUNT_BANK) ACCOUNT_BANK,
				       (SELECT BT.BANK_NAME
				          FROM APP___PAS__DBUSER.T_BANK BT
				         WHERE BT.BANK_CODE = TPAT.ACCOUNT_BANK) BANK_NAME,
				       TPAT.ACCOUNT,
				       TCM.SPECIAL_ACCOUNT_FLAG,
				       TCM.TRUST_BUSI_FLAG,
				       TCM.MULTI_MAINRISK_FLAG,
				       TCM.MEDIA_TYPE,
				       (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = TT.AGENT_CODE) AS AGENT_CHANNEL,
					   (SELECT CA.AGENT_LEVEL FROM DEV_PAS.T_AGENT CA WHERE CA.AGENT_CODE = TT.AGENT_CODE) AS CA_AGENT_LEVEL,
					   (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = TT.AGENT_CODE) AS TAB_AGENT_LEVEL,
					   (SELECT 1
						  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
						       APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
						 WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
						   AND B.TAX_EXTENSION_FLAG = '1'
						   AND A.POLICY_CODE = TCM.POLICY_CODE
						   AND ROWNUM = 1) TAX_EXTENSION_FLAG,
					   (SELECT 1
				          FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
				         WHERE CBP.POLICY_CODE = TCM.POLICY_CODE
				           AND CBP.BUSI_PROD_CODE IN
				               (SELECT CI.CONSTANTS_VALUE
				                  FROM DEV_PAS.T_CONSTANTS_INFO CI
				                 WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT')
				           AND ROWNUM = 1) KD_FLAG,
					   TCM.APPLY_DATE,
					   (SELECT TPA.ACKNOWLEDGE_DATE 
							FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT TPA 
							WHERE TPA.POLICY_ID = TCM.POLICY_ID) ACKNOWLEDGE_DATE
				  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
				       APP___PAS__DBUSER.T_PAYER_ACCOUNT   TPAT,
				       APP___PAS__DBUSER.T_CONTRACT_AGENT  TT,
				       APP___PAS__DBUSER.T_POLICY_HOLDER   TB,
				       APP___PAS__DBUSER.T_CUSTOMER        TC
				 WHERE TCM.POLICY_ID = TT.POLICY_ID
				   AND TCM.POLICY_ID = TPAT.POLICY_ID
				   AND TCM.POLICY_ID = TB.POLICY_ID
				   AND TB.CUSTOMER_ID = TC.CUSTOMER_ID
				   AND TT.IS_CURRENT_AGENT = 1
				   AND TCM.LIABILITY_STATE IN (1, 4) ]]>
		   <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TT.AGENT_CODE = #{agent_code} ]]></if>
		   <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		   <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
		   <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		   <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND TC.customer_cert_type = #{customer_cert_type} ]]></if>
		  <![CDATA[AND ROWNUM < 2000 ]]>
	</select>
	
	<!-- 根据保单号,业务员和客户三要素查询保单补发信息 -->
	<select id="PA_findPolicyReplacementInforsByFourElements" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ SELECT TCM.POLICY_CODE,
				       TCM.ORGAN_CODE,
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T
				         WHERE T.ORGAN_CODE = TCM.ORGAN_CODE
				           AND ROWNUM = 1) ORGAN_CODE_NAME,
				       TCA.AGENT_CODE,
				       (SELECT TA.AGENT_ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_AGENT TA
				         WHERE TCA.AGENT_CODE = TA.AGENT_CODE) AS AGENT_ORGAN_CODE,
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T, APP___PAS__DBUSER.T_AGENT TA
				         WHERE TCA.AGENT_CODE = TA.AGENT_CODE
				           AND TA.AGENT_ORGAN_CODE = T.ORGAN_CODE
				           AND ROWNUM = 1) AS AGENT_ORGAN_NAME,
				       (SELECT TA.SALES_ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_AGENT TA
				         WHERE TA.AGENT_CODE = TCA.AGENT_CODE) SALES_ORGAN_CODE,
				       (SELECT TSO.SALES_ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_SALES_ORGAN TSO, APP___PAS__DBUSER.T_AGENT TA
				         WHERE TSO.SALES_ORGAN_CODE = TA.SALES_ORGAN_CODE
				           AND TA.AGENT_CODE = TCA.AGENT_CODE
				           AND ROWNUM = 1) SALES_ORGAN_NAME,
				       TCM.EXPIRY_DATE,
				       TCM.END_CAUSE,
				       TCM.VALIDATE_DATE,
				       TCM.LIABILITY_STATE,
				       TCM.LAPSE_CAUSE,
				       TCM.LAPSE_DATE,
				       (SELECT MAX(TCBP.MATURITY_DATE)
				          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
				         WHERE TCBP.POLICY_CODE = TCM.POLICY_CODE
				           AND TCBP.MASTER_BUSI_ITEM_ID IS NULL) MATURITY_DATE,
				       TPAT.ACCOUNT,
				       TPAT.ACCOUNT_NAME,
				       NVL((SELECT BT.STD_BANK_CODE
				             FROM APP___PAS__DBUSER.T_BANK BT
				            WHERE BT.BANK_CODE = TPAT.ACCOUNT_BANK),
				           TPAT.ACCOUNT_BANK) ACCOUNT_BANK,
				       (SELECT BT.BANK_NAME
				          FROM APP___PAS__DBUSER.T_BANK BT
				         WHERE BT.BANK_CODE = TPAT.ACCOUNT_BANK) BANK_NAME,
				       CASE
				         WHEN TCM.SERVICE_BANK = 'B8' AND TCM.SUBMIT_CHANNEL = 1 AND
				              TCM.CHANNEL_TYPE = '03' THEN
				          (SELECT L.LAW_MAN_NAME
				             FROM APP___PAS__DBUSER.T_LAW_MAN L
				            WHERE L.LAW_MAN = TPAT.LAW_MAN
				              AND ROWNUM = 1)
				         ELSE
				          (SELECT BT.BANK_NAME
				             FROM APP___PAS__DBUSER.T_BANK BT
				            WHERE BT.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK)
				       END AS NEXT_BANK_NAME,
				       CASE
				         WHEN TCM.SERVICE_BANK = 'B8' AND TCM.SUBMIT_CHANNEL = 1 AND
				              TCM.CHANNEL_TYPE = '03' THEN
				          ''
				         ELSE
				          NVL((SELECT BT.STD_BANK_CODE
				                FROM APP___PAS__DBUSER.T_BANK BT
				               WHERE BT.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK),
				              TPAT.NEXT_ACCOUNT_BANK)
				       END AS NEXT_ACCOUNT_BANK,
				       TPAT.NEXT_ACCOUNT,
				       TPAT.NEXT_ACCOUNT_NAME,
				       TCM.MULTI_MAINRISK_FLAG
				  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
				  JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT TPAT
				    ON TCM.POLICY_ID = TPAT.POLICY_ID
				  JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT TCA
				    ON TCM.POLICY_ID = TCA.POLICY_ID
				  JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
				    ON TCM.POLICY_ID = TPH.POLICY_ID
				  JOIN APP___PAS__DBUSER.T_CUSTOMER TC
				    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
				 WHERE TCA.IS_CURRENT_AGENT = 1
				   AND TCA.AGENT_CODE = #{agent_code} ]]>
		   <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		   <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
		   <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		 <![CDATA[ AND (TCM.LIABILITY_STATE <> 3 OR
				       (TCM.LIABILITY_STATE = 3 AND
				       TCM.END_CAUSE IN ('01', '06', '08', '11')))
				   AND NOT EXISTS
				 (SELECT 1
				          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
				         WHERE CBP.POLICY_CODE = TCM.POLICY_CODE
				           AND CBP.BUSI_PROD_CODE IN
				               (SELECT CI.CONSTANTS_VALUE
				                  FROM APP___PAS__DBUSER.T_CONSTANTS_INFO CI
				                 WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))
		           AND ROWNUM < 2000 ]]>
	</select>
	
	<!-- 根据保单号,业务员和客户三要素查询保单关联保单信息 -->
	<select id="PA_findPolicyRelatedPolicyByThreeElements" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ SELECT TCM.POLICY_CODE,
				       TCM.ORGAN_CODE,
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T
				         WHERE T.ORGAN_CODE = TCM.ORGAN_CODE
				           AND ROWNUM = 1) ORGAN_CODE_NAME,
				       (SELECT TA.AGENT_ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_AGENT TA
				         WHERE TCA.AGENT_CODE = TA.AGENT_CODE) AS AGENT_ORGAN_CODE,
				       (SELECT T.ORGAN_NAME
				          FROM APP___PAS__DBUSER.T_UDMP_ORG T, APP___PAS__DBUSER.T_AGENT TA
				         WHERE TCA.AGENT_CODE = TA.AGENT_CODE
				           AND TA.AGENT_ORGAN_CODE = T.ORGAN_CODE
				           AND ROWNUM = 1) AS AGENT_ORGAN_NAME,
				       TCM.END_CAUSE,
				       TCM.VALIDATE_DATE,
				       TCM.LIABILITY_STATE,
				       TCM.LAPSE_CAUSE,
				       CASE
				         WHEN TCM.SERVICE_BANK = 'B8' AND TCM.SUBMIT_CHANNEL = 1 AND
				              TCM.CHANNEL_TYPE = '03' THEN
				          (SELECT L.LAW_MAN_NAME
				             FROM APP___PAS__DBUSER.T_LAW_MAN L
				            WHERE L.LAW_MAN = TPAT.LAW_MAN
				              AND ROWNUM = 1)
				         ELSE
				          (SELECT BT.BANK_NAME
				             FROM APP___PAS__DBUSER.T_BANK BT
				            WHERE BT.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK)
				       END AS NEXT_BANK_NAME,
				       CASE
				         WHEN TCM.SERVICE_BANK = 'B8' AND TCM.SUBMIT_CHANNEL = 1 AND
				              TCM.CHANNEL_TYPE = '03' THEN
				          ''
				         ELSE
				          NVL((SELECT BT.STD_BANK_CODE
				                FROM APP___PAS__DBUSER.T_BANK BT
				               WHERE BT.BANK_CODE = TPAT.NEXT_ACCOUNT_BANK),
				              TPAT.NEXT_ACCOUNT_BANK)
				       END AS NEXT_ACCOUNT_BANK,
				       TPAT.NEXT_ACCOUNT,
				       TPAT.NEXT_ACCOUNT_NAME,
				       TCM.MULTI_MAINRISK_FLAG
				  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
				  JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT TPAT
				    ON TCM.POLICY_ID = TPAT.POLICY_ID
				  JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT TCA
				    ON TCM.POLICY_ID = TCA.POLICY_ID
				  JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
				    ON TCM.POLICY_ID = TPH.POLICY_ID
				  JOIN APP___PAS__DBUSER.T_CUSTOMER TC
				    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
				 WHERE TCA.IS_CURRENT_AGENT = 1
				   AND TCA.AGENT_CODE = #{agent_code} ]]>
		   <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		   <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
		   <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		 <![CDATA[ AND ROWNUM < 2000 ]]>
	</select>
	
	<!-- 根据客户三要素和业务员号查询保单信息 -->
	<select id="PA_findChangeOfClaimFormByCustomerAndAgent" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ SELECT CM.POLICY_CODE,
				       CM.POLICY_ID,
				       CM.VALIDATE_DATE,
				       CM.LIABILITY_STATE,
				       CM.LAPSE_CAUSE,
				       CM.ORGAN_CODE,
				       (SELECT TA.AGENT_ORGAN_CODE FROM DEV_PAS.T_AGENT TA WHERE TA.AGENT_CODE = CA.AGENT_CODE) AGENT_ORGAN_CODE,
				       CM.MULTI_MAINRISK_FLAG,
				       CM.TRUST_BUSI_FLAG,
				       CM.SPECIAL_ACCOUNT_FLAG,
       				   CM.SUBMIT_CHANNEL
				  FROM DEV_PAS.T_CONTRACT_MASTER CM,
				       DEV_PAS.T_POLICY_HOLDER   PH,
				       DEV_PAS.T_CUSTOMER        C,
				       DEV_PAS.T_CONTRACT_AGENT  CA
				 WHERE CM.POLICY_CODE = PH.POLICY_CODE
				   AND PH.CUSTOMER_ID = C.CUSTOMER_ID
				   AND CA.POLICY_CODE = CM.POLICY_CODE
				   AND CM.LIABILITY_STATE IN (1, 4)
				   AND CA.IS_CURRENT_AGENT = 1
				   AND CA.AGENT_CODE = #{agent_code} ]]>
				   <if test=" policy_code != null and policy_code != ''  "><![CDATA[AND CM.POLICY_CODE = #{policy_code}]]></if>
				   <if test=" customer_name != null and customer_name != ''  "><![CDATA[AND C.CUSTOMER_NAME = #{customer_name}]]></if>
				   <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[AND C.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
	      <![CDATA[AND NOT EXISTS (SELECT 1
							         FROM DEV_PAS.T_CONTRACT_BENE CB
							        WHERE CB.POLICY_CODE = CM.POLICY_CODE
							          AND CB.COMPANY_ID IS NOT NULL)
				   AND NOT EXISTS (SELECT 1
						             FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
						            WHERE M.POLICY_CODE = CBP.POLICY_CODE
						              AND M.POLICY_CODE = CM.POLICY_CODE
						              AND CBP.BUSI_PROD_CODE IN
						               (SELECT CI.CONSTANTS_VALUE
						                  FROM DEV_PAS.T_CONSTANTS_INFO CI
						                 WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT')) 
				                           
				UNION
				
				SELECT CM.POLICY_CODE,
				       CM.POLICY_ID,
				       CM.VALIDATE_DATE,
				       CM.LIABILITY_STATE,
				       CM.LAPSE_CAUSE,
				       CM.ORGAN_CODE,
				       (SELECT TA.AGENT_ORGAN_CODE FROM DEV_PAS.T_AGENT TA WHERE TA.AGENT_CODE = CA.AGENT_CODE) AGENT_ORGAN_CODE,
				       CM.MULTI_MAINRISK_FLAG,
				       CM.TRUST_BUSI_FLAG,
				       CM.SPECIAL_ACCOUNT_FLAG,
       				   CM.SUBMIT_CHANNEL
				  FROM DEV_PAS.T_CONTRACT_MASTER CM,
				       DEV_PAS.T_INSURED_LIST    IL,
				       DEV_PAS.T_CUSTOMER        C,
				       DEV_PAS.T_CONTRACT_AGENT  CA
				 WHERE CM.POLICY_CODE = IL.POLICY_CODE
				   AND IL.CUSTOMER_ID = C.CUSTOMER_ID
				   AND CA.POLICY_CODE = CM.POLICY_CODE
				   AND CM.LIABILITY_STATE IN (1, 4)
				   AND CA.IS_CURRENT_AGENT = 1
				   AND CA.AGENT_CODE = #{agent_code}  ]]>
				   <if test=" policy_code != null and policy_code != ''  "><![CDATA[AND CM.POLICY_CODE = #{policy_code}]]></if>
				   <if test=" customer_name != null and customer_name != ''  "><![CDATA[AND C.CUSTOMER_NAME = #{customer_name}]]></if>
				   <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[AND C.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
		  <![CDATA[AND NOT EXISTS (SELECT 1
				                     FROM DEV_PAS.T_CONTRACT_BENE CB
				                    WHERE CB.POLICY_CODE = CM.POLICY_CODE
				                      AND CB.COMPANY_ID IS NOT NULL)
				   AND NOT EXISTS (SELECT 1
				                     FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
				                    WHERE M.POLICY_CODE = CBP.POLICY_CODE
				                      AND M.POLICY_CODE = CM.POLICY_CODE
				                      AND CBP.BUSI_PROD_CODE IN
					                  (SELECT CI.CONSTANTS_VALUE
					                     FROM DEV_PAS.T_CONSTANTS_INFO CI
					                    WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT')) ]]>
	</select>
	
	<!-- 根据客户三要素查询保单信息 -->
	<select id="PA_findPolicyInfosByThreeElementsOfCustomer" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ SELECT TCM.POLICY_ID,
				       TCM.POLICY_CODE,
				       TCM.SPECIAL_ACCOUNT_FLAG,
				       TCM.VALIDATE_DATE,
				       ACK.ACKNOWLEDGE_DATE,
				       TCM.APPLY_DATE,
				       TCA.AGENT_CODE,
				       TCM.LIABILITY_STATE,
				       TCM.LAPSE_CAUSE,
				       TCM.LAPSE_DATE,
				       TCM.END_CAUSE,
				       TCM.EXPIRY_DATE,
				       TCM.ORGAN_CODE,
				       (SELECT TUO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME
				  FROM DEV_PAS.T_CONTRACT_MASTER TCM
				  LEFT JOIN DEV_PAS.T_POLICY_HOLDER TPH
				    ON TCM.POLICY_CODE = TPH.POLICY_CODE
				   AND TCM.POLICY_ID = TPH.POLICY_ID
				  LEFT JOIN DEV_PAS.T_CUSTOMER TC
				    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
				  LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT ACK
				    ON TCM.POLICY_ID = ACK.POLICY_ID
				  LEFT JOIN DEV_PAS.T_CONTRACT_AGENT TCA
				    ON TCA.POLICY_CODE = TCM.POLICY_CODE
				 WHERE 1 = 1
				   AND TCA.IS_CURRENT_AGENT = 1
				   AND TC.CUSTOMER_NAME = #{customer_name}
				   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
				   AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}
				    ]]>
        <if test=" customer_gender != null and customer_gender != ''  "><![CDATA[ AND TC.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND TC.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
        <![CDATA[ AND ROWNUM < 2000 ]]>
	</select>
	
	<!-- 根据客户三要素查询投被保人的保单信息 -->
	<select id="PA_findHolderAndInsurdPolicyInfosByThreeElementsOfCustomer" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ SELECT TCM.POLICY_ID,
				       TCM.POLICY_CODE,
				       TCM.SPECIAL_ACCOUNT_FLAG,
				       TCM.VALIDATE_DATE,
				       ACK.ACKNOWLEDGE_DATE,
				       TCM.APPLY_DATE,
				       TCA.AGENT_CODE,
				       TCM.LIABILITY_STATE,
				       TCM.LAPSE_CAUSE,
				       TCM.LAPSE_DATE,
				       TCM.END_CAUSE,
				       TCM.EXPIRY_DATE,
				       TCM.ORGAN_CODE,
				       (SELECT TUO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME
				  FROM DEV_PAS.T_CONTRACT_MASTER TCM
				  LEFT JOIN DEV_PAS.T_POLICY_HOLDER TPH
				    ON TCM.POLICY_CODE = TPH.POLICY_CODE
				   AND TCM.POLICY_ID = TPH.POLICY_ID
				  LEFT JOIN DEV_PAS.T_CUSTOMER TC
				    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
				  LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT ACK
				    ON TCM.POLICY_ID = ACK.POLICY_ID
				  LEFT JOIN DEV_PAS.T_CONTRACT_AGENT TCA
				    ON TCA.POLICY_CODE = TCM.POLICY_CODE 
				 WHERE 1 = 1
				   AND TCA.IS_CURRENT_AGENT = 1
				   AND TC.CUSTOMER_NAME = #{customer_name}
				   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
				   AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}
				   AND ROWNUM < 2000 
				     ]]>
        <if test=" customer_gender != null and customer_gender != ''  "><![CDATA[ AND TC.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND TC.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
        <![CDATA[
				   union 

				SELECT TCM.POLICY_ID,
		               TCM.POLICY_CODE,
		               TCM.SPECIAL_ACCOUNT_FLAG,
		               TCM.VALIDATE_DATE,
		               ACK.ACKNOWLEDGE_DATE,
		               TCM.APPLY_DATE,
		               TCA.AGENT_CODE,
		               TCM.LIABILITY_STATE,
		               TCM.LAPSE_CAUSE,
		               TCM.LAPSE_DATE,
		               TCM.END_CAUSE,
		               TCM.EXPIRY_DATE,
		               TCM.ORGAN_CODE,
		               (SELECT TUO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME
		          FROM DEV_PAS.T_CONTRACT_MASTER TCM
		          LEFT JOIN DEV_PAS.T_INSURED_LIST TIL
		            ON TCM.POLICY_CODE = TIL.POLICY_CODE
		           AND TCM.POLICY_ID = TIL.POLICY_ID
		          LEFT JOIN DEV_PAS.T_CUSTOMER TC
		            ON TIL.CUSTOMER_ID = TC.CUSTOMER_ID
		          LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT ACK
		            ON TCM.POLICY_ID = ACK.POLICY_ID
		          LEFT JOIN DEV_PAS.T_CONTRACT_AGENT TCA
		            ON TCA.POLICY_CODE = TCM.POLICY_CODE 
		         WHERE 1 = 1
				   AND TCA.IS_CURRENT_AGENT = 1
				   AND TC.CUSTOMER_NAME = #{customer_name}
				   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
				   AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}
				    ]]>
        <if test=" customer_gender != null and customer_gender != ''  "><![CDATA[ AND TC.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND TC.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
        <![CDATA[ AND ROWNUM < 2000]]>
	</select>
	
	<!-- 根据客户信息查询保单号 -->
	<select id="PA_queryPolicyCodeByCustomInfo" resultType="java.util.Map" parameterType="java.util.Map">  
	
	<if test="cust_role != null and cust_role != '' and cust_role == '1'.toString()">
		<![CDATA[
	        SELECT DISTINCT 
	        TCM.POLICY_CODE,
	        TCM.LIABILITY_STATE,
	        TCM.ORGAN_CODE
	        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
	        INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH ON TCM.POLICY_CODE = TPH.POLICY_CODE
	        AND TCM.POLICY_ID = TPH.POLICY_ID
	        INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TC ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
	        INNER JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT TCA ON TCA.POLICY_CODE = TCM.POLICY_CODE
	        WHERE  1 =1 
	        AND TCA.IS_CURRENT_AGENT = '1'
	         ]]>
	        <if test=" customer_no != null and customer_no != ''  "><![CDATA[ AND TC.OLD_CUSTOMER_ID = #{customer_no} ]]></if>
	        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
	        <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	        <if test=" customer_gender != null and customer_gender != ''  "><![CDATA[ AND TC.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	        <if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND TC.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
	        <if test=" customer_certi_type != null and customer_certi_type != ''  "><![CDATA[ AND TC.CUSTOMER_CERT_TYPE = #{customer_certi_type} ]]></if>
	        <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if>   
	        UNION
	</if> 
        <![CDATA[ 
        SELECT DISTINCT 
        TCM.POLICY_CODE,
        TCM.LIABILITY_STATE,
        TCM.ORGAN_CODE
        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
        INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL ON TCM.POLICY_CODE = TIL.POLICY_CODE
        AND TCM.POLICY_ID = TIL.POLICY_ID
        INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TC ON TIL.CUSTOMER_ID = TC.CUSTOMER_ID
        INNER JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT TCA ON TCA.POLICY_CODE = TCM.POLICY_CODE
        WHERE  1 = 1 
       	AND TCA.IS_CURRENT_AGENT = '1'
         ]]>
        <if test=" customer_no != null and customer_no != ''  "><![CDATA[ AND TC.OLD_CUSTOMER_ID = #{customer_no} ]]></if>
        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME = #{customer_name} ]]></if>
        <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
        <if test=" customer_gender != null and customer_gender != ''  "><![CDATA[ AND TC.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND TC.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
        <if test=" customer_certi_type != null and customer_certi_type != ''  "><![CDATA[ AND TC.CUSTOMER_CERT_TYPE = #{customer_certi_type} ]]></if>
        <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if>
	</select>
	
	
	<select id="PA_queryHolderChangePolicyCodeInfo" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ 
	  SELECT TCM.POLICY_CODE,TCM.POLICY_ID,
       TCM.ORGAN_CODE,
       (SELECT R.ORGAN_NAME
          FROM DEV_PAS.T_UDMP_ORG_REL R
         WHERE R.ORGAN_CODE = TCM.ORGAN_CODE
           AND ROWNUM = 1) ORGAN_CODE_NAME,
       TCM.CHANNEL_TYPE,
       TCM.VALIDATE_DATE,
       TCM.LIABILITY_STATE,
       TCM.END_CAUSE,
       DECODE(TCM.LIABILITY_STATE,
              '4',
              (SELECT T.CAUSE_DESC
                 FROM DEV_PAS.T_LAPSE_CAUSE T
                WHERE T.CAUSE_CODE = TCM.LAPSE_CAUSE),
              '3',
              (SELECT A.CAUSE_NAME
                 FROM DEV_PAS.T_END_CAUSE A
                WHERE A.CAUSE_CODE = TCM.END_CAUSE)) AS CONT_STATE_REASON,
       NVL((SELECT CARD_DESC
             FROM DEV_PAS.T_MEDICAL_INSURANCE_CARD TIC
            WHERE CARD_CODE = TCM.MEDICAL_INSURANCE_CARD),
           '') AS MEDICAL_INSURANCE_CARD1,
       TCM.TRUST_BUSI_FLAG,
       TCM.APPLY_DATE
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
       APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA,
       APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
       APP___PAS__DBUSER.T_CUSTOMER        TC
 WHERE TCM.POLICY_ID = TCA.POLICY_ID
   AND TCM.POLICY_ID = TPH.POLICY_ID
   AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
      
   AND TCA.IS_CURRENT_AGENT = '1'
   AND (TCM.LIABILITY_STATE = '1' OR TCM.LIABILITY_STATE = '4' OR
       TCM.LIABILITY_STATE = '3' AND TCM.END_CAUSE = '01')
   AND NOT EXISTS (SELECT 'X'
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
               APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
         WHERE 1 = 1
           AND TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
           AND TCBP.POLICY_ID = TCM.POLICY_ID
           AND (TBP.PRODUCT_CATEGORY1 = '20003' AND
               TBP.TAX_EXTENSION_FLAG = '1')) ]]>
   <if test="customer_name != null and customer_name != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{customer_name}]]></if>
   <if test="customer_certi_code != null and customer_certi_code != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
   <if test="agent_code != null and agent_code != ''  "><![CDATA[AND TCA.AGENT_CODE = #{agent_code}]]></if>
   <if test="policy_code != null and policy_code != ''  "><![CDATA[AND TCM.POLICY_CODE = #{policy_code}]]></if>
 <![CDATA[  ORDER BY TCM.VALIDATE_DATE DESC
	  
]]>
	</select>
	
	
	
	<!-- 保单信息列表查询接口 -->
	<select id="PA_queryPolicyListInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT A.POLICY_CODE,
           A.VALIDATE_DATE,
           A.LIABILITY_STATE,
		   A.LAPSE_CAUSE,
           A.END_CAUSE,
           A.ORGAN_CODE,
           A.POLICY_ID,
           (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE  D.AGENT_CODE = CA.AGENT_CODE) AS AGENT_CHANNEL,
       	   (SELECT TA.AGENT_LEVEL FROM DEV_PAS.T_AGENT TA WHERE D.AGENT_CODE = TA.AGENT_CODE) AS CA_AGENT_LEVEL,
		   (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = D.AGENT_CODE) AS TAB_AGENT_LEVEL,
           (SELECT Z.AGENT_ORGAN_CODE
              FROM APP___PAS__DBUSER.T_AGENT Z
             WHERE Z.AGENT_CODE = D.AGENT_CODE
               AND ROWNUM = 1) AS AGENT_ORGAN_CODE,
           (SELECT Y.ORGAN_NAME
              FROM APP___PAS__DBUSER.T_UDMP_ORG Y,
                   APP___PAS__DBUSER.T_AGENT    Z
             WHERE Y.ORGAN_CODE = Z.AGENT_ORGAN_CODE
               AND Z.AGENT_CODE = D.AGENT_CODE) AS ORGAN_NAME
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
           APP___PAS__DBUSER.T_POLICY_HOLDER   B,
           APP___PAS__DBUSER.T_CUSTOMER        C,
           APP___PAS__DBUSER.T_CONTRACT_AGENT  D
     WHERE A.POLICY_CODE = B.POLICY_CODE
       AND A.POLICY_ID = B.POLICY_ID
       AND A.POLICY_CODE = D.POLICY_CODE
       AND B.CUSTOMER_ID = C.CUSTOMER_ID
       AND D.IS_CURRENT_AGENT = 1
   ]]>
   <if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and A.POLICY_CODE in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
   <if test=" cont_no != null and cont_no != ''  "><![CDATA[  AND A.POLICY_CODE = #{cont_no} ]]></if>
   <if test=" cust_name != null and cust_name != ''  "><![CDATA[  AND C.CUSTOMER_NAME = #{cust_name} ]]></if>
   <if test=" agent_code != null and agent_code != ''  "><![CDATA[  AND D.AGENT_CODE = #{agent_code} ]]></if>
   <if test=" cust_certi_code != null and cust_certi_code != ''  "><![CDATA[  AND C.CUSTOMER_CERTI_CODE = #{cust_certi_code} ]]></if>
   <![CDATA[ 
    UNION
    SELECT A.POLICY_CODE,
           A.VALIDATE_DATE,
           A.LIABILITY_STATE,
		   A.LAPSE_CAUSE,
           A.END_CAUSE,
           A.ORGAN_CODE,
           A.POLICY_ID,
           (SELECT CA.AGENT_CHANNEL FROM DEV_PAS.T_AGENT CA WHERE  D.AGENT_CODE = CA.AGENT_CODE) AS AGENT_CHANNEL,
       	   (SELECT TA.AGENT_LEVEL FROM DEV_PAS.T_AGENT TA WHERE D.AGENT_CODE = TA.AGENT_CODE) AS CA_AGENT_LEVEL,
		   (SELECT TAB.AGENT_LEVEL_CS FROM DEV_PAS.T_AGENT_BANCASEXC TAB WHERE TAB.AGENT_CODE = D.AGENT_CODE) AS TAB_AGENT_LEVEL,
           (SELECT Z.AGENT_ORGAN_CODE
              FROM APP___PAS__DBUSER.T_AGENT Z
             WHERE Z.AGENT_CODE = D.AGENT_CODE
               AND ROWNUM = 1) AS AGENT_ORGAN_CODE,
           (SELECT Y.ORGAN_NAME
              FROM APP___PAS__DBUSER.T_UDMP_ORG Y,
                   APP___PAS__DBUSER.T_AGENT    Z
             WHERE Y.ORGAN_CODE = Z.AGENT_ORGAN_CODE
               AND Z.AGENT_CODE = D.AGENT_CODE) AS ORGAN_NAME
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
           APP___PAS__DBUSER.T_INSURED_LIST   B,
           APP___PAS__DBUSER.T_CUSTOMER        C,
           APP___PAS__DBUSER.T_CONTRACT_AGENT  D
     WHERE A.POLICY_CODE = B.POLICY_CODE
       AND A.POLICY_ID = B.POLICY_ID
       AND A.POLICY_CODE = D.POLICY_CODE
       AND B.CUSTOMER_ID = C.CUSTOMER_ID
       AND D.IS_CURRENT_AGENT = 1
   ]]>
   <if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and A.POLICY_CODE in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
   <if test=" cont_no != null and cont_no != ''  "><![CDATA[  AND A.POLICY_CODE = #{cont_no} ]]></if>
   <if test=" cust_name != null and cust_name != ''  "><![CDATA[  AND C.CUSTOMER_NAME = #{cust_name} ]]></if>
   <if test=" agent_code != null and agent_code != ''  "><![CDATA[  AND D.AGENT_CODE = #{agent_code} ]]></if>
   <if test=" cust_certi_code != null and cust_certi_code != ''  "><![CDATA[  AND C.CUSTOMER_CERTI_CODE = #{cust_certi_code} ]]></if>
   </select>
   
   
   
   	<select id="PA_queryCardContByPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT 1 as KD_PRODUCT
            FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
             WHERE M.POLICY_CODE = CBP.POLICY_CODE
             AND M.POLICY_CODE =  #{policy_code}
             AND CBP.BUSI_PROD_CODE IN 
               (SELECT CI.CONSTANTS_VALUE
                FROM DEV_PAS.T_CONSTANTS_INFO CI
                 WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT')                
	  ]]>
	</select>
	
	
	<select id="PA_queryPayDueFlagByPolicycode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT 1 as PayDue_Flag
            FROM DEV_PAS.T_PAY_PLAN TPP
            join DEV_PAS.T_CONTRACT_MASTER TCM  
            ON TCM.POLICY_CODE = TPP.POLICY_CODE
             AND TCM.POLICY_ID = TPP.POLICY_ID
            join APP___PAS__DBUSER.T_PAY_DUE TPD
            on TPP.PLAN_ID = TPD.PLAN_ID
            where TCM.POLICY_CODE =  #{policy_code}
             AND TPP.PAY_PLAN_TYPE IN ('1','3')
             AND TPD.FEE_STATUS = '00'               
	  ]]>
	</select>
	<select id="PA_queryPolicyListInfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select b.policy_code,b.policy_id
		   from dev_pas.t_policy_holder a, dev_pas.t_contract_master b
		  where a.policy_code = b.policy_code
		    and b.liability_state in (1, 4)
		    and a.customer_id = #{customer_id}
		    and exists (select 1
				          from dev_pas.t_prem t
				         where t.policy_code = a.policy_code
				           and t.FEE_SCENE_CODE in ('NB','RN')
				           and t.due_time <#{end_date}+1
				           and t.due_time >#{start_date}-1)            
	  ]]>
	</select>
	
	<!--获取保单首次投保的保单号-->
	<select id="PA_queryOriginalPolicyByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT A.POLICY_ID ,A.POLICY_CODE
		  FROM DEV_PAS.T_CONTRACT_MASTER A
		 WHERE A.FORMER_ID IS NULL
		 START WITH A.POLICY_ID = #{policy_id}
		CONNECT BY PRIOR A.FORMER_ID = A.POLICY_ID 
		 ORDER BY A.POLICY_ID  ]]>
	</select>
	
	<select id="PA_findAllCustomerPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				     SELECT *
		       FROM (SELECT I.POLICY_CODE, M.SPECIAL_ACCOUNT_FLAG
		               FROM DEV_PAS.T_INSURED_LIST I, DEV_PAS.T_CONTRACT_MASTER M
		              WHERE 1 = 1
		                AND I.POLICY_CODE = M.POLICY_CODE
		                AND I.CUSTOMER_ID = #{customer_id}
		             UNION
		             SELECT H.POLICY_CODE, M.SPECIAL_ACCOUNT_FLAG
		               FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CONTRACT_MASTER M
		              WHERE 1 = 1
		                AND H.POLICY_CODE = M.POLICY_CODE
		                AND H.CUSTOMER_ID = #{customer_id}
		          ) TCM  WHERE 1 = 1  ]]>
		      
		    <if test=" kd_policy_flag != null "><![CDATA[
			  AND NOT EXISTS
			      (SELECT 1
			               FROM DEV_PAS.T_CONTRACT_MASTER    M,
			                    DEV_PAS.T_CONTRACT_BUSI_PROD CBP
			              WHERE M.POLICY_CODE = CBP.POLICY_CODE
			                AND M.POLICY_CODE = TCM.POLICY_CODE
			                AND CBP.BUSI_PROD_CODE IN
			                    (SELECT CI.CONSTANTS_VALUE
			                       FROM DEV_PAS.T_CONSTANTS_INFO CI
			                      WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))
	   		]]></if>
	</select>
	
	<!-- 根据客户ID查询客户作为投被保人的保单 根据投保日期倒序 -->
	<select id="PA_findMaxApplyDatePolicyByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			  SELECT POLICY_CODE, APPLY_DATE, ANNUAL_INCOME_CEIL
   					FROM ( SELECT T.POLICY_CODE, T.APPLY_DATE, ANNUAL_INCOME_CEIL
            			FROM ( SELECT TPH.POLICY_CODE,
                         			 TCM.APPLY_DATE,
                         			 TPH.ANNUAL_INCOME_CEIL as ANNUAL_INCOME_CEIL
                    FROM DEV_PAS.T_POLICY_HOLDER TPH
                   INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                      ON TPH.POLICY_CODE = TCM.POLICY_CODE
                   WHERE TPH.CUSTOMER_ID = #{customer_id}
                     AND TCM.LIABILITY_STATE = #{liability_state}
                     AND TPH.ANNUAL_INCOME_CEIL IS NOT NULL
                  UNION
                  SELECT TIL.POLICY_CODE,
                         TCM.APPLY_DATE,
                         TIL.ANNUAL_INCOME_CEIL as ANNUAL_INCOME_CEIL
                    FROM DEV_PAS.T_INSURED_LIST TIL
                   INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                      ON TIL.POLICY_CODE = TCM.POLICY_CODE
                   WHERE TIL.CUSTOMER_ID = #{customer_id}
                     AND TCM.LIABILITY_STATE = #{liability_state}
                     AND TIL.ANNUAL_INCOME_CEIL IS NOT NULL ) T
           ORDER BY T.APPLY_DATE DESC )
   WHERE ROWNUM = 1

		]]>
	
	</select>
	
	<!--根据保单号查询保费缴纳总次数-->
	<select id="PA_queryTotalPremCountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)  as TotalPremCount
                      FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                     WHERE TP1.FEE_SCENE_CODE IN ('NB', 'RN')
                       AND TP1.FEE_STATUS IN ('01','16','19')
                      AND TP1.POLICY_CODE = #{policy_code}
	     ]]>
	</select>
	
	    <!--根据保单号查询操作过的保全项-->
		<select id="PA_findNewPolicyStateByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				 SELECT TCM.POLICY_CODE, TCM.APPLY_CODE, TCM.POLICY_ID, TPC.SERVICE_CODE
                   FROM DEV_PAS.V_POLICY_CHANGE_ALL TPC
                   JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                   ON TCM.POLICY_ID = TPC.POLICY_ID
                    WHERE TCM.POLICY_CODE = #{policy_code} 
	   		]]>
	   </select>
	   
	   <!--根据保单号查询操作过的保全项-->
		<select id="PA_findTypeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT A.policy_code,A.apply_code,
                       A.policy_id,
                       A.SERVICE_CODE,
                       A.liability_state,
                       A.end_cause,
                       A.finish_time,A.lock_service_id FROM (select tcm.policy_code,
                       tcm.apply_code,
                       tcm.policy_id,
                       tpc.SERVICE_CODE,
                       tcm.liability_state,
                       tcm.end_cause,
                       tpc.finish_time,
                       (select z.lock_service_id
                  from app___pas__dbuser.t_lock_policy z
                 where z.policy_id = TCM.policy_id
                   and rownum = 1) as lock_service_id
                 from dev_pas.V_POLICY_CHANGE_ALL tpc
                 join dev_pas.t_contract_master tcm
                 on tcm.policy_id = tpc.policy_id
                 where 1 = 1
                 and tcm.policy_code = #{policy_code}
                 and tpc.SERVICE_CODE in ('RE','SR','CS','CF','XT','CT','EA','RB') order by tpc.finish_time  DESC ) A WHERE 1=1 and ROWNUM = 1]]>      
	   </select>
	
		   
	   <select id="PA_queryBonuspayPolicyListInfo" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ 
	  SELECT TCM.POLICY_CODE,
       TCM.POLICY_ID,
       TCM.VALIDATE_DATE,
       TCM.APPLY_DATE,
       TCM.LIABILITY_STATE,
       TCM.END_CAUSE,
       TCM.LAPSE_CAUSE,
       TCM.SUBMIT_CHANNEL,
       (SELECT MAX(T.POLICY_PERIOD) FROM DEV_PAS.T_CONTRACT_EXTEND T WHERE T.POLICY_CODE = TCM.POLICY_CODE) PAID_COUNT,
       TPA.NEXT_ACCOUNT,
	 TPA.NEXT_ACCOUNT_NAME,
	 (select T.BANK_NAME from DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPA.NEXT_ACCOUNT_BANK ) NEXT_ACCOUNT_BANK,
	 TPA.ACCOUNT_BANK ACCOUNT_ID,
	 TPA.NEXT_ACCOUNT_BANK NEXT_ACCOUNT_ID,
	 TPA.ACCOUNT,
	 TPA.ACCOUNT_NAME,
	 (select T.BANK_NAME from DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPA.ACCOUNT_BANK ) ACCOUNT_BANK,
        TCM.ORGAN_CODE,
        TCM.SPECIAL_ACCOUNT_FLAG,
        TCM.MULTI_MAINRISK_FLAG,
        TCM.SUBINPUT_TYPE,
        (SELECT CA.AGENT_ORGAN_CODE FROM DEV_PAS.T_AGENT CA WHERE TCA.AGENT_CODE = CA.AGENT_CODE AND TCA.IS_CURRENT_AGENT = '1') AS MANAGE_COM,
        (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = ((SELECT CA.AGENT_ORGAN_CODE FROM DEV_PAS.T_AGENT CA WHERE TCA.AGENT_CODE = CA.AGENT_CODE AND TCA.IS_CURRENT_AGENT = '1' AND ROWNUM =1))) MANAGE_COM_NAME
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
  LEFT JOIN  APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA ON TCM.POLICY_ID = TCA.POLICY_ID
  LEFT JOIN  APP___PAS__DBUSER.T_POLICY_HOLDER   TPH ON TCM.POLICY_ID = TPH.POLICY_ID
  LEFT JOIN  APP___PAS__DBUSER.T_CUSTOMER        TC ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
  LEFT JOIN  APP___PAS__DBUSER.T_PAYER_ACCOUNT   TPA ON TCM.POLICY_ID = TPA.POLICY_ID
 WHERE 1=1
   AND TCA.IS_CURRENT_AGENT = '1'
   AND (TCM.LIABILITY_STATE = '1' OR TCM.LIABILITY_STATE = '4' OR
      ( TCM.LIABILITY_STATE = '3' AND TCM.END_CAUSE <> '03'))
	   ]]>
   <if test="customer_name != null and customer_name != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{customer_name}]]></if>
   <if test="customer_cert_type != null and customer_cert_type != ''  "><![CDATA[AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]></if>
   <if test="customer_certi_code != null and customer_certi_code != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
   <if test="agent_code != null and agent_code != ''  "><![CDATA[AND TCA.AGENT_CODE = #{agent_code}]]></if>
   <if test="policy_code != null and policy_code != ''  "><![CDATA[AND TCM.POLICY_CODE = #{policy_code}]]></if>
 <![CDATA[  ORDER BY TCM.VALIDATE_DATE DESC ]]>
	</select>
	
	    <!--根据保单号查询操作过的保全项-->
		<select id="PA_findCIXEBACKByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				   SELECT A.policy_code,A.apply_code,
                       A.policy_id,
                       A.SERVICE_CODE,
                       A.validate_date,
                       A.finish_time,
                       A.liability_state,
                       A.end_cause FROM (select tcm.policy_code,
               tcm.apply_code,
               tcm.policy_id, 
               'CIXEBACK' service_code, 
               tcm.validate_date,
               tpc.finish_time,
               tcm.liability_state,
               tcm.end_cause
          from dev_pas.V_POLICY_CHANGE_ALL tpc
          join dev_pas.V_POLICY_CHANGE_ALL tpc1
           on  tpc.PRE_POLICY_CHG  = tpc1.POLICY_CHG_ID 
          join dev_pas.t_contract_master tcm
           on tcm.policy_id = tpc.policy_id
          where 1 = 1 
          and tpc.SERVICE_CODE ='RB'
                 and tpc1.SERVICE_CODE in ('CT','XT','EA')
                 and tcm.policy_code = #{policy_code}  order by tpc.finish_time  DESC )A WHERE 1=1 and ROWNUM = 1 ]]>
	   </select>
	   
	   <select id="PA_queryBonusClaimFromChangeInfo" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ 
	  SELECT TCM.POLICY_CODE,
       TCM.POLICY_ID,
       TCM.VALIDATE_DATE,
       TCM.LIABILITY_STATE,
       TCM.END_CAUSE,
       TCM.LAPSE_CAUSE,
       TCM.SUBMIT_CHANNEL,
       TCM.APPLY_DATE,
       TPA.NEXT_ACCOUNT,
	 TPA.NEXT_ACCOUNT_NAME,
	 (SELECT T.BANK_NAME FROM DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPA.NEXT_ACCOUNT_BANK ) NEXT_ACCOUNT_BANK,
	 TPA.ACCOUNT_BANK ACCOUNT_ID,
	 TPA.NEXT_ACCOUNT_BANK NEXT_ACCOUNT_ID,
 		    (SELECT CASE COUNT(0) WHEN 0 THEN '0' ELSE '1' END
     FROM DEV_PAS.T_PREM T 
     WHERE T.FEE_SCENE_CODE = 'RN' 
     AND T.ARAP_FLAG = 1 
     AND T.FINISH_TIME IS NOT NULL 
     AND T.POLICY_CODE = TCM.POLICY_CODE
     AND T.BANK_ACCOUNT = TPA.NEXT_ACCOUNT) RN,/*是否做过续费*/
	  TPA.PAY_MODE,
	 TPA.ACCOUNT,
	 TPA.ACCOUNT_NAME,
	 (select T.BANK_NAME from DEV_PAS.T_BANK T WHERE T.BANK_CODE = TPA.ACCOUNT_BANK ) ACCOUNT_BANK,
        TCM.ORGAN_CODE,
        TCM.SPECIAL_ACCOUNT_FLAG,
        TCM.MULTI_MAINRISK_FLAG,
        TCM.SUBINPUT_TYPE,
        (SELECT CA.AGENT_ORGAN_CODE FROM DEV_PAS.T_AGENT CA WHERE TCA.AGENT_CODE = CA.AGENT_CODE AND TCA.IS_CURRENT_AGENT = '1') AS MANAGE_COM,
        (SELECT TUO.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG TUO WHERE TUO.ORGAN_CODE = ((SELECT CA.AGENT_ORGAN_CODE FROM DEV_PAS.T_AGENT CA WHERE TCA.AGENT_CODE = CA.AGENT_CODE AND TCA.IS_CURRENT_AGENT = '1' AND ROWNUM =1))) MANAGE_COM_NAME
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
  LEFT JOIN  APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA ON TCM.POLICY_ID = TCA.POLICY_ID
  LEFT JOIN  APP___PAS__DBUSER.T_POLICY_HOLDER   TPH ON TCM.POLICY_ID = TPH.POLICY_ID
  LEFT JOIN  APP___PAS__DBUSER.T_CUSTOMER        TC ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
  LEFT JOIN  APP___PAS__DBUSER.T_PAYER_ACCOUNT   TPA ON TCM.POLICY_ID = TPA.POLICY_ID
 WHERE 1=1
   AND TCA.IS_CURRENT_AGENT = '1'
   AND (TCM.LIABILITY_STATE = '1' OR TCM.LIABILITY_STATE = '4')
	   ]]>
   <if test="customer_name != null and customer_name != ''  "><![CDATA[AND TC.CUSTOMER_NAME = #{customer_name}]]></if>
   <if test="customer_cert_type != null and customer_cert_type != ''  "><![CDATA[AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]></if>
   <if test="customer_certi_code != null and customer_certi_code != ''  "><![CDATA[AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
   <if test="agent_code != null and agent_code != ''  "><![CDATA[AND TCA.AGENT_CODE = #{agent_code}]]></if>
   <if test="policy_code != null and policy_code != ''  "><![CDATA[AND TCM.POLICY_CODE = #{policy_code}]]></if>
   <if test="customer_id != null and customer_id != ''  "><![CDATA[AND TPH.CUSTOMER_ID = #{customer_id}]]></if>
   <if test=" policy_code_list  != null and policy_code_list.size()>0">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
   </if>
 <![CDATA[  ORDER BY TCM.VALIDATE_DATE DESC ]]>
	</select>
	
	<!-- TR-20250704-015214掌上新华攻坚组问题单-PRM-20250703-0006_自助理赔申请时，出险人信息查询缓慢-个险新核心 -->
	<select id="PA_queryPolicyInfoByCmFiveFacters" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ 
	  SELECT TCM.POLICY_CODE,
       TCM.POLICY_ID,
       TPH.CUSTOMER_ID
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
  LEFT JOIN  APP___PAS__DBUSER.T_POLICY_HOLDER TPH ON TCM.POLICY_ID = TPH.POLICY_ID
 WHERE 1=1]]>
    <if test=" customer_Id  != null "><![CDATA[ AND TPH.CUSTOMER_ID = #{customer_Id} ]]></if>
   	   <![CDATA[AND NOT EXISTS
 (SELECT 1
	FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
   WHERE M.POLICY_CODE = CBP.POLICY_CODE
	 AND M.POLICY_CODE = TCM.POLICY_CODE
	 AND CBP.BUSI_PROD_CODE IN 
		 (SELECT CI.CONSTANTS_VALUE
			FROM DEV_PAS.T_CONSTANTS_INFO CI
		   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
<![CDATA[ UNION     
SELECT TCM.POLICY_CODE,
       TCM.POLICY_ID,
       TIL.CUSTOMER_ID
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
  LEFT JOIN  APP___PAS__DBUSER.T_INSURED_LIST TIL ON TCM.POLICY_ID = TIL.POLICY_ID
 WHERE 1=1]]>
   <if test=" customer_Id  != null "><![CDATA[ AND TIL.CUSTOMER_ID = #{customer_Id} ]]></if>
   	   <![CDATA[AND NOT EXISTS
 (SELECT 1
	FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
   WHERE M.POLICY_CODE = CBP.POLICY_CODE
	 AND M.POLICY_CODE = TCM.POLICY_CODE
	 AND CBP.BUSI_PROD_CODE IN 
		 (SELECT CI.CONSTANTS_VALUE
			FROM DEV_PAS.T_CONSTANTS_INFO CI
		   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
	</select>
	
	<!-- TR-20250704-015214掌上新华攻坚组问题单-PRM-20250703-0006_自助理赔申请时，出险人信息查询缓慢-个险新核心 -->
	<select id="PA_queryPolicyInfoByInsureCustomerIds" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ 
	  SELECT TCM.POLICY_CODE,
       TCM.APPLY_DATE,
       TCM.VALIDATE_DATE,
       TCM.INITIAL_VALIDATE_DATE,
       TCM.ISSUE_DATE,
       TCM.LIABILITY_STATE,
       TCM.LAPSE_CAUSE,
       TCM.END_CAUSE,
       TCM.EXPIRY_DATE,
       TCM.ORGAN_CODE
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
  LEFT JOIN  APP___PAS__DBUSER.T_INSURED_LIST TIL ON TCM.POLICY_ID = TIL.POLICY_ID
 WHERE 1=1 AND (TCM.LIABILITY_STATE = '1' OR TCM.LIABILITY_STATE = '4' OR (TCM.LIABILITY_STATE ='3' AND TCM.END_CAUSE ='01'))]]>
   <if test=" customer_Id  != null "><![CDATA[ AND TIL.CUSTOMER_ID = #{customer_Id} ]]></if>
    <![CDATA[AND NOT EXISTS
 (SELECT 1
	FROM DEV_PAS.T_CONTRACT_MASTER M, DEV_PAS.T_CONTRACT_BUSI_PROD CBP
   WHERE M.POLICY_CODE = CBP.POLICY_CODE
	 AND M.POLICY_CODE = TCM.POLICY_CODE
	 AND CBP.BUSI_PROD_CODE IN 
		 (SELECT CI.CONSTANTS_VALUE
			FROM DEV_PAS.T_CONSTANTS_INFO CI
		   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
	</select>

	   
	   <!--根据保单号查询销售渠道等信息-->
		<select id="PA_findChannelTypeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				   SELECT TCA.CHANNEL_TYPE,
		               (SELECT TSC.SALES_CHANNEL_NAME
		                  FROM DEV_PAS.T_SALES_CHANNEL TSC
		                 WHERE TCA.CHANNEL_TYPE = TSC.SALES_CHANNEL_CODE) AS CHANNEL_NAME,
		               (SELECT TUO.ORGAN_NAME
		                  FROM DEV_PAS.T_UDMP_ORG TUO
		                 WHERE TUO.ORGAN_CODE = TCM.AGENT_ORG_ID) AS AGENT_ORG_NAME,
		               TCA.COOPERATION_NAME
		          FROM DEV_PAS.T_CONTRACT_MASTER TCM, DEV_PAS.T_CONTRACT_AGENT TCA
		         WHERE 1 = 1
		           AND TCM.POLICY_ID = TCA.POLICY_ID
		           AND TCA.IS_NB_AGENT = 1
		           AND TCM.POLICY_CODE = #{policy_code}
           ]]>
	   </select>
  
</mapper>
