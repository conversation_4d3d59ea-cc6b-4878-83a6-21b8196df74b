<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="doubleMainRiskLR">
	<!-- zhaojp 查询双主险补发轨迹需操作 总记录数 -->
	<select id="getDoubleMainLRCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		 SELECT COUNT(1) FROM  (SELECT a.change_id
  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A,
       DEV_PAS.T_CS_POLICY_CHANGE B,
       DEV_PAS.T_CONTRACT_MASTER  M
 WHERE A.SERVICE_CODE in ('LR', 'RN')
   AND A.ACCEPT_STATUS = '18'
   AND A.CHANGE_ID = B.CHANGE_ID
   AND M.DOUBLE_MAINRISK_FLAG = '1'
   AND A.SERVICE_CODE = B.SERVICE_CODE
   AND B.POLICY_CODE = M.POLICY_CODE
   AND A.ACCEPT_ID = B.ACCEPT_ID
   and a.change_id not in(
   SELECT a.change_id
  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A,
       DEV_PAS.T_CS_POLICY_CHANGE B,
       DEV_PAS.T_CONTRACT_MASTER  M
 WHERE A.SERVICE_CODE in ('LR', 'RN')
   AND A.ACCEPT_STATUS = '16'
   AND A.CHANGE_ID = B.CHANGE_ID
   AND M.DOUBLE_MAINRISK_FLAG = '1'
   AND A.SERVICE_CODE = B.SERVICE_CODE
   AND B.POLICY_CODE = M.POLICY_CODE
   AND A.ACCEPT_ID = B.ACCEPT_ID  
GROUP BY A.CHANGE_ID
HAVING COUNT(A.CHANGE_ID) = 1
   )
GROUP BY A.CHANGE_ID
HAVING COUNT(A.CHANGE_ID) = 1)
        ]]>
	</select>
	
	<!-- zhaojp 查询双主险补发轨迹需操作 -->
	<select id="getDoubleMainLRList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		     SELECT CAC.ACCEPT_ID,
		     CAC.SERVICE_CODE,
       CAC.CHANGE_ID,
       CPC.POLICY_CODE,
       cac.validate_time,
       cac.organ_code,
       mm.apply_code
  FROM DEV_PAS.T_CS_ACCEPT_CHANGE CAC,
       DEV_PAS.T_CS_POLICY_CHANGE CPC,
       dev_pas.t_contract_master  mm
 WHERE CPC.ACCEPT_ID = CAC.ACCEPT_ID
   and mm.policy_code = cpc.policy_code
   AND CAC.CHANGE_ID IN
       (SELECT a.change_id
  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A,
       DEV_PAS.T_CS_POLICY_CHANGE B,
       DEV_PAS.T_CONTRACT_MASTER  M
 WHERE A.SERVICE_CODE in ('LR', 'RN')
   AND A.ACCEPT_STATUS = '18'
   AND A.CHANGE_ID = B.CHANGE_ID
   AND M.DOUBLE_MAINRISK_FLAG = '1'
   AND A.SERVICE_CODE = B.SERVICE_CODE
   AND B.POLICY_CODE = M.POLICY_CODE
   AND A.ACCEPT_ID = B.ACCEPT_ID
   and a.change_id not in(
   SELECT a.change_id
  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A,
       DEV_PAS.T_CS_POLICY_CHANGE B,
       DEV_PAS.T_CONTRACT_MASTER  M
 WHERE A.SERVICE_CODE in ('LR', 'RN')
   AND A.ACCEPT_STATUS = '16'
   AND A.CHANGE_ID = B.CHANGE_ID
   AND M.DOUBLE_MAINRISK_FLAG = '1'
   AND A.SERVICE_CODE = B.SERVICE_CODE
   AND B.POLICY_CODE = M.POLICY_CODE
   AND A.ACCEPT_ID = B.ACCEPT_ID  
GROUP BY A.CHANGE_ID
HAVING COUNT(A.CHANGE_ID) = 1
   )
GROUP BY A.CHANGE_ID
HAVING COUNT(A.CHANGE_ID) = 1)
	and cac.service_code in('LR','RN')
	AND CAC.ACCEPT_STATUS = '18'
        ]]>
	</select>
	
	
</mapper>