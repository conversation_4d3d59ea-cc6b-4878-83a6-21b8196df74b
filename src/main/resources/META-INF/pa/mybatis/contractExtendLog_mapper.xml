<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractExtendLog">
<!--
	<sql id="PA_contractExtendLogWhereCondition">
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" extraction_due_date  != null  and  extraction_due_date  != ''  "><![CDATA[ AND A.EXTRACTION_DUE_DATE = #{extraction_due_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" billing_date  != null  and  billing_date  != ''  "><![CDATA[ AND A.BILLING_DATE = #{billing_date} ]]></if>
		<if test=" renew_decision_status  != null "><![CDATA[ AND A.RENEW_DECISION_STATUS = #{renew_decision_status} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_period  != null "><![CDATA[ AND A.POLICY_PERIOD = #{policy_period} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" next_prem  != null "><![CDATA[ AND A.NEXT_PREM = #{next_prem} ]]></if>
		<if test=" prem_status  != null "><![CDATA[ AND A.PREM_STATUS = #{prem_status} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractExtendLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractExtendLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_EXTEND_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG(
				INSERT_TIME, POLICY_YEAR, EXTRACTION_DUE_DATE, UPDATE_TIME, ITEM_ID, BILLING_DATE, RENEW_DECISION_STATUS, 
				INSERT_TIMESTAMP, LOG_ID, ORGAN_CODE, POLICY_CODE, UPDATE_BY, POLICY_PERIOD, LIST_ID, 
				PAY_DUE_DATE, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, BUSI_ITEM_ID, POLICY_ID, 
				NEXT_PREM, PREM_STATUS ) 
			VALUES (
				SYSDATE, #{policy_year, jdbcType=NUMERIC} , #{extraction_due_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{billing_date, jdbcType=DATE} , #{renew_decision_status, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{organ_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{policy_period, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{pay_due_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{next_prem, jdbcType=NUMERIC} , #{prem_status, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractExtendLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG WHERE LOG_ID=#{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractExtendLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
		    EXTRACTION_DUE_DATE = #{extraction_due_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    BILLING_DATE = #{billing_date, jdbcType=DATE} ,
		    RENEW_DECISION_STATUS = #{renew_decision_status, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_PERIOD = #{policy_period, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    NEXT_PREM = #{next_prem, jdbcType=NUMERIC} ,
		    PREM_STATUS = #{prem_status, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractExtendLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, A.RENEW_DECISION_STATUS, 
			A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, 
			A.PAY_DUE_DATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEXT_PREM, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractExtendLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, A.RENEW_DECISION_STATUS, 
			A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, 
			A.PAY_DUE_DATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEXT_PREM, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractExtendLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, A.RENEW_DECISION_STATUS, 
			A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, 
			A.PAY_DUE_DATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEXT_PREM, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractExtendLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractExtendLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_YEAR, B.EXTRACTION_DUE_DATE, B.ITEM_ID, B.BILLING_DATE, B.RENEW_DECISION_STATUS, 
			B.LOG_ID, B.ORGAN_CODE, B.POLICY_CODE, B.POLICY_PERIOD, B.LIST_ID, 
			B.PAY_DUE_DATE, B.LOG_TYPE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID, 
			B.NEXT_PREM, B.PREM_STATUS FROM (
					SELECT ROWNUM RN, A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, A.RENEW_DECISION_STATUS, 
			A.LOG_ID, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, 
			A.PAY_DUE_DATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEXT_PREM, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
