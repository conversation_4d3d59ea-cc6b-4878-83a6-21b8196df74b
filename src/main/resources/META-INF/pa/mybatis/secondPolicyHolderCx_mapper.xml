<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISecondPolicyHolderCxDao">

	<sql id="secondPolicyHolderCxWhereCondition">
		<if test=" change_seq  != null "><![CDATA[ AND A.CHANGE_SEQ = #{change_seq} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" oper_type != null and oper_type != ''  "><![CDATA[ AND A.OPER_TYPE = #{oper_type} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" pre_log_id  != null "><![CDATA[ AND A.PRE_LOG_ID = #{pre_log_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="querySecondPolicyHolderCxByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderCxByChangeSeqCondition">
		<if test=" change_seq  != null "><![CDATA[ AND A.CHANGE_SEQ = #{change_seq} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderCxByOperTypeCondition">
		<if test=" oper_type != null and oper_type != '' "><![CDATA[ AND A.OPER_TYPE = #{oper_type} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderCxByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderCxByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSecondPolicyHolderCx"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT S_SE_PO_HOLDER_CX__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_SECOND_POLICY_HOLDER_CX(
				INSERT_TIME, CHANGE_SEQ, UPDATE_TIME, LOG_ID, INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, 
				UPDATE_TIMESTAMP, POLICY_CHG_ID, LOG_TYPE, OPER_TYPE, INSERT_BY, POLICY_ID, PRE_LOG_ID ) 
			VALUES (
				SYSDATE, #{change_seq, jdbcType=NUMERIC} , SYSDATE , #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{log_type, jdbcType=VARCHAR} , #{oper_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{pre_log_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSecondPolicyHolderCx" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_SECOND_POLICY_HOLDER_CX WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSecondPolicyHolderCx" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_SECOND_POLICY_HOLDER_CX ]]>
		<set>
		<trim suffixOverrides=",">
		    CHANGE_SEQ = #{change_seq, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
			OPER_TYPE = #{oper_type, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PRE_LOG_ID = #{pre_log_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSecondPolicyHolderCxByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderCxByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderCxByChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderCxByChangeSeqCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderCxByOperType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderCxByOperTypeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderCxByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderCxByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderCxByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderCxByLogIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSecondPolicyHolderCx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE ROWNUM <=  1000  ]]>
		<include refid="secondPolicyHolderCxWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSecondPolicyHolderCx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE ROWNUM <=  1000  ]]>
		<include refid="secondPolicyHolderCxWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSecondPolicyHolderCxTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_SECOND_POLICY_HOLDER_CX A WHERE 1 = 1  ]]>
		<include refid="secondPolicyHolderCxWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="querySecondPolicyHolderCxForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CHANGE_SEQ, B.LOG_ID, B.LIST_ID, 
			B.POLICY_CHG_ID, B.LOG_TYPE, B.OPER_TYPE, B.POLICY_ID, B.PRE_LOG_ID FROM (
					SELECT ROWNUM RN, A.CHANGE_SEQ, A.LOG_ID, A.LIST_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM T_SECOND_POLICY_HOLDER_CX A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="secondPolicyHolderCxWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
