<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISpecialAccountInfoDao">

	<sql id="PA_specialAccountInfoWhereCondition">
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" account_name != null and account_name != ''  "><![CDATA[ AND A.ACCOUNT_NAME = #{account_name} ]]></if>
		<if test=" account_bank != null and account_bank != ''  "><![CDATA[ AND A.ACCOUNT_BANK = #{account_bank} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" bank_phone != null and bank_phone != ''  "><![CDATA[ AND A.BANK_PHONE = #{bank_phone} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" validiy_end_date  != null  and  validiy_end_date  != ''  "><![CDATA[ AND A.VALIDIY_END_DATE = #{validiy_end_date} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySpecialAccountInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountInfoByAccountCondition">
		<if test=" account != null and account != '' "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountInfoByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountInfoByCustomerCertiCodeCondition">
		<if test=" customer_certi_code != null and customer_certi_code != '' "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountInfoByCustomerNameCondition">
		<if test=" customer_name != null and customer_name != '' "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountInfoByBankPhoneCondition">
		<if test=" bank_phone != null and bank_phone != '' "><![CDATA[ AND A.BANK_PHONE = #{bank_phone} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSpecialAccountInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SPECIAL_ACCOUNT_INFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO(
				CUSTOMER_CERT_TYPE, CUSTOMER_NAME, INSERT_TIME, ACCOUNT_NAME, ACCOUNT_BANK, CUSTOMER_ID, UPDATE_TIME, 
				ACCOUNT, CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, BANK_PHONE, UPDATE_BY, LIST_ID, CUSTOMER_BIRTHDAY, 
				UPDATE_TIMESTAMP, INSERT_BY, CUSTOMER_GENDER, PLAT_INVEST_ACC, VALIDIY_END_DATE ) 
			VALUES (
				#{customer_cert_type, jdbcType=VARCHAR}, #{customer_name, jdbcType=VARCHAR} , SYSDATE , #{account_name, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , SYSDATE 
				, #{account, jdbcType=VARCHAR} , #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{bank_phone, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{customer_birthday, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC}, #{plat_invest_acc, jdbcType=VARCHAR}, #{validiy_end_date, jdbcType=DATE} ) 
		 ]]>
	</insert>
	
	<!-- 添加操作 -->
	<insert id="PA_addSpecialAccountInfoForCS"  useGeneratedKeys="false" parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO(
				CUSTOMER_CERT_TYPE, CUSTOMER_NAME, INSERT_TIME, ACCOUNT_NAME, ACCOUNT_BANK, CUSTOMER_ID, UPDATE_TIME, 
				ACCOUNT, CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, BANK_PHONE, UPDATE_BY, LIST_ID, CUSTOMER_BIRTHDAY, 
				UPDATE_TIMESTAMP, INSERT_BY, CUSTOMER_GENDER,PLAT_INVEST_ACC, VALIDIY_END_DATE ) 
			VALUES (
				#{customer_cert_type, jdbcType=VARCHAR}, #{customer_name, jdbcType=VARCHAR} , SYSDATE , #{account_name, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , SYSDATE 
				, #{account, jdbcType=VARCHAR} , #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{bank_phone, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{customer_birthday, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC}, #{plat_invest_acc, jdbcType=VARCHAR}, #{validiy_end_date, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSpecialAccountInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSpecialAccountInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			ACCOUNT_NAME = #{account_name, jdbcType=VARCHAR} ,
			ACCOUNT_BANK = #{account_bank, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT = #{account, jdbcType=VARCHAR} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
			BANK_PHONE = #{bank_phone, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
		    PLAT_INVEST_ACC = #{plat_invest_acc, jdbcType=VARCHAR} ,
		    VALIDIY_END_DATE = #{validiy_end_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSpecialAccountInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountInfoByListIdCondition" />
	</select>
	<select id="PA_findSpecialAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_specialAccountInfoWhereCondition" />
	</select>
	
	<select id="PA_findSpecialAccountInfoByAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountInfoByAccountCondition" />
	</select>
	
	<select id="PA_findSpecialAccountInfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountInfoByCustomerIdCondition" />
	</select>
	
	<select id="PA_findSpecialAccountInfoByCustomerCertiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountInfoByCustomerCertiCodeCondition" />
	</select>
	
	<select id="PA_findSpecialAccountInfoByCustomerName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountInfoByCustomerNameCondition" />
	</select>
	
	<select id="PA_findSpecialAccountInfoByBankPhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountInfoByBankPhoneCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSpecialAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_specialAccountInfoWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSpecialAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_specialAccountInfoWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSpecialAccountInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_specialAccountInfoWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySpecialAccountInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_CERT_TYPE, B.CUSTOMER_NAME, B.ACCOUNT_NAME, B.ACCOUNT_BANK, B.CUSTOMER_ID, 
			B.ACCOUNT, B.CUSTOMER_CERTI_CODE, B.BANK_PHONE, B.LIST_ID, B.CUSTOMER_BIRTHDAY, 
			B.CUSTOMER_GENDER, B.PLAT_INVEST_ACC, B.VALIDIY_END_DATE FROM (
					SELECT ROWNUM RN, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_specialAccountInfoWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findSpecialAccountInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.CUSTOMER_CERTI_CODE, A.BANK_PHONE, A.LIST_ID, A.CUSTOMER_BIRTHDAY, 
			A.CUSTOMER_GENDER, A.PLAT_INVEST_ACC, A.VALIDIY_END_DATE, (SELECT T.BANK_NAME FROM APP___PAS__DBUSER.T_BANK T WHERE T.BANK_CODE=A.ACCOUNT_BANK) as account_bank_name FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_INFO A 
			WHERE 1=1 AND A.LIST_ID IN (SELECT R.BANK_POLICY_ID FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION R  WHERE 1=1 
			 ]]>
			<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND R.POLICY_CODE  = #{policy_code} ]]></if>
			<![CDATA[ ) ]]>
			
			 
	</select>
	
	<select id="PA_querySpecialAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT AI.ACCOUNT_BANK AS ACCOUNTBANKCODE, /*银行代码*/
				       (SELECT B.BANK_NAME
				          FROM DEV_PAS.T_BANK B
				         WHERE AI.ACCOUNT_BANK = B.BANK_CODE) AS ACCOUNT_BANK, /* 银行名称*/
				       AI.ACCOUNT_NAME, /*账号名称*/
				       AI.ACCOUNT /*账户号码*/
				  FROM DEV_PAS.T_SPECIAL_ACCOUNT_RELATION AR
				  LEFT JOIN DEV_PAS.T_SPECIAL_ACCOUNT_INFO AI
				    ON AR.BANK_POLICY_ID = AI.LIST_ID
				 WHERE AR.POLICY_CODE =  #{policy_code}
				   AND ROWNUM = 1
			 ]]>
	</select>
	
</mapper>
