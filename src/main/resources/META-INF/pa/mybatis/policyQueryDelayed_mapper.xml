<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyQueryDelayedDao">

	<!-- 若类型为空，则根据查询日期查询当天生效的保单 --> 
	<select id="PA_findDelayedPolicyByDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, ]]>
		<if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
		<![CDATA[	    tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        (select t.bank_code from dev_pas.T_PREM t 
				            where t.policy_code = tcm.policy_code 
				            and t.busi_prod_code = tcbp.busi_prod_code 
				            and t.arap_flag = '1' and rownum=1) as bank_code,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa,
		                (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.T_PREM TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.T_PREM TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' ]]>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.validate_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date == null and date_flag == 1 "><![CDATA[ and tcm.validate_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date == null and date_flag == 1 "><![CDATA[ and tcm.validate_date <= #{batch_end_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date != null and date_flag == 1 "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date != null and date_flag == 1 "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date == null and date_flag == 2 "><![CDATA[ and tcm.apply_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date == null and date_flag == 2 "><![CDATA[ and tcm.apply_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list">
			<![CDATA[ and tcbp.busi_prod_code in (]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcbp.busi_prod_code,
					       tcm.service_bank_branch]]>
	</select>

	<select id="PA_findDelayedPolicyByHesitationDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
				        '17' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
				   join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id  
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  
				    and tca.is_nb_agent = '1' 
				    and tcm.LIABILITY_STATE = 1 
				    and sysdate - tpa.ACKNOWLEDGE_DATE >= 15    ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.validate_date = #{batch_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tcm.validate_date >= #{batch_start_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tcm.validate_date <= #{batch_end_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
						   tcm.SUBINPUT_TYPE,
						   tcm.SUBMIT_CHANNEL,
						   tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 -->
	<select id="PA_findDelayedPolicyByType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.lapse_cause,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       (SELECT DISTINCT tp1.UNIT_NUMBER
				          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
				         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
				           AND tp1.BUSINESS_CODE = tpc.business_code
				           and rownum = 1) UNIT_NUMBER,
				       case when sum(ts.fee_amount) is null 
				       then (select sum(a.fee_amount) from dev_pas.v_prem_arap_all a 
				       where tcm.policy_code = a.policy_code and a.business_code=ts.accept_code) 
				       else sum(ts.fee_amount) end surrender_amount, 
				       sum(ts.fee_amount) fee_amount,
				       (select tpr.PREMIUM from dev_pas.T_POLICY_REINSTATE tpr
						    where  tpr.accept_code = tpc.business_code
						    AND ROWNUM = 1) PREMIUM,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE, 
				        (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1 ,DEV_PAS.T_CONTRACT_BUSI_PROD TCBP1
		               WHERE TCBP1.POLICY_CODE = TCM.POLICY_CODE]]>
		               <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[AND TCBP1.APPLY_DATE = TCM.APPLY_DATE]]></if>
		               <![CDATA[
		               and TCBP1.POLICY_CODE = TCP1.POLICY_CODE
		               and TCBP1.BUSI_ITEM_ID = TCP1.BUSI_ITEM_ID
		               AND TCP1.PRODUCT_CODE IN
		                     (SELECT DISTINCT TPL.INTERNAL_ID
		                        FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                        JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
                          ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID)) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
			                   tpc.CHANGE_FLAG,
			                   tpc.POLICY_CHG_ID,
			                   (SELECT SUM(TP1.FEE_AMOUNT)
		                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                         AND tp1.BUSINESS_CODE = tpc.business_code) as transaction_amount
		                       
				  from ]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[dev_pas.t_policy_change tpc]]> 
				</if>
				<![CDATA[
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
		            AND TS.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			where 1 = 1  and tca.is_nb_agent = '1']]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>

		<if test=" batch_date != null and batch_date != '' "><![CDATA[and  tpc.finish_time >= #{batch_date} and tpc.finish_time < #{batch_date} +1 ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[  and tpc.finish_time >= #{batch_start_date}  ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[and tpc.finish_time < #{batch_end_date} +1 ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<if test=" company_id != null and company_id != ''">
	 	 <![CDATA[ and exists (
		     ((select 1 from dev_pas.t_policy_holder tph, dev_pas.t_trust_company ttc 
				         where tph.policy_id = tpc.policy_id and tph.customer_id = ttc.customer_id and ttc.company_id = #{company_id})
				 union
				 (select 2 from dev_pas.t_contract_bene tcb, dev_pas.t_trust_company ttc 
				         where tcb.policy_id = tpc.policy_id and tcb.company_id = ttc.company_id and tcb.bene_kind = '02' and ttc.company_id = #{company_id}))
		 )]]>
		</if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcm.lapse_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
                tpac.pay_next,
                tpc.CHANGE_FLAG,
                tpc.POLICY_CHG_ID,
                ts.accept_code   ]]>
	</select>

	<select id="PA_findDelayedPolicyByServiceCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select distinct tcm.policy_code,
				                tcm.apply_code,
				                tp.UNIT_NUMBER,
				                tcm.policy_id,
				                #{service_code} service_code,
				                tcm.validate_date,
				                tcm.liability_state,
				                tcm.lapse_date,
				                tcm.expiry_date,
				                tcm.apply_date,
				                tcm.issue_date,
				                tcm.end_cause,
				                tcm.SUBINPUT_TYPE,
				                tcm.SUBMIT_CHANNEL,
				                tca.CHANNEL_TYPE,
				                tpa.acknowledge_date,
				                tcm.service_bank_branch,
				                tpac.next_account,
				                tpac.pay_next,
				                tpar.finish_time validate_time,
				                sum(tcp.total_prem_af) total_prem_af,
				                sum(tcp.initial_amount) initial_amount,
				                sum(tcp.bonus_sa) bonus_sa,
				                tp.pay_mode,
			                    CASE WHEN TP.FEE_SCENE_CODE='NB' THEN
			                      0
			                     WHEN TP.FEE_SCENE_CODE='RN' THEN
			                      1
			                      WHEN TP.FEE_SCENE_CODE='CS' AND TP.SERVICE_CODE = 'RE' THEN
			                      1
			                      WHEN TP.FEE_SCENE_CODE='CS' AND TP.SERVICE_CODE = 'SR' THEN
			                      1
			                   END PAY_TYPE,
			                   tpar.BANK_CODE,
			                   sum(tcp.STD_PREM_AF) FEE_AMOUNT,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                    (SELECT SUM(CASE
               WHEN A.ARAP_FLAG = '1' THEN
                A.FEE_AMOUNT
               ELSE
                A.FEE_AMOUNT * -1
               END)
          FROM DEV_PAS.V_PREM_ALL         A,
             DEV_PAS.T_CONTRACT_MASTER    B,
             DEV_PAS.T_CONTRACT_BUSI_PROD C
         WHERE B.POLICY_CODE = C.POLICY_CODE
           AND B.APPLY_DATE = C.APPLY_DATE
           AND C.POLICY_CODE = A.POLICY_CODE
           AND C.BUSI_PROD_CODE = A.BUSI_PROD_CODE
           AND A.FEE_STATUS IN ('01', '16', '19')
           AND (A.FEE_SCENE_CODE IN ('RN', 'NB') OR
             (A.FEE_SCENE_CODE = 'CS' AND
             A.SERVICE_CODE IN ('RE', 'SR')))
           AND A.POLICY_CODE = TCM.POLICY_CODE)  RENEWAL_PAY_AMOUNT
				  from dev_pas.t_contract_master tcm
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id
				  left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				   ]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[left join dev_pas.v_prem_arap_all tpar
					    on tpar.policy_code = tcbp.policy_code
					    and tpar.busi_prod_code=tcbp.busi_prod_code
					    and tpar.policy_code = tcm.policy_code
					    left join dev_pas.v_prem_all tp
	           			 on tpar.UNIT_NUMBER = tp.UNIT_NUMBER
                  		and tp.policy_code = tcbp.policy_code
			            and tp.busi_prod_code=tcbp.busi_prod_code
			            and tp.policy_code = tcm.policy_code]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[left join dev_pas.t_prem_arap tpar
					    on tpar.policy_code = tcbp.policy_code
					    and tpar.busi_prod_code=tcbp.busi_prod_code
					    and tpar.policy_code = tcm.policy_code
					    left join dev_pas.t_prem tp
	           			 on tpar.UNIT_NUMBER = tp.UNIT_NUMBER
                  		and tp.policy_code = tcbp.policy_code
              			and tp.busi_prod_code=tcbp.busi_prod_code
              			and tp.policy_code = tcm.policy_code]]>
				</if>
				<![CDATA[
				 where 1 = 1 and tpar.fee_type in ('G003010000', 'G003100000') and tca.is_nb_agent = '1'  AND tpar.ARAP_FLAG = '1' AND tpar.FEE_STATUS IN ('01','19') ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpar.finish_time = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tpar.finish_time >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tpar.finish_time <= #{batch_end_date} ]]></if>
		<if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[
				 group by tcm.policy_code,
		                  tcm.apply_code,
		                  tcm.policy_id,
		                  tcm.validate_date,
		                  tcm.liability_state,
		                  tcm.apply_date,
		                  tcm.issue_date,
		                  tcm.SUBINPUT_TYPE,
		                  tcm.SUBMIT_CHANNEL,
		                  tca.CHANNEL_TYPE,
		                  tpa.acknowledge_date,
		                  tcm.lapse_date,
		                  tcm.expiry_date,
		                  tcm.service_bank_branch,
		                  tcm.end_cause,
		                  tpar.BANK_CODE,
		                  tpar.finish_time,
		                  tpar.UNIT_NUMBER,
		                  tpac.next_account,
		                  TP.FEE_SCENE_CODE,
		                  TP.SERVICE_CODE,
		                  tp.pay_mode,
		                  tpac.pay_next,
		                  tp.UNIT_NUMBER
				 order by validate_time]]>
	</select>

	<!-- 查询险种、责任组信息 -->
	<select id="PA_findContractBusiProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		         select tbp.product_code_sys,
		                tbp.product_name_sys,
		                tbp.product_category,
		                tbp.PRODUCT_CATEGORY1,
		                tbp.PRODUCT_CATEGORY2,
		                tcp.coverage_period,
		                tcp.coverage_year,
		                tcp.charge_period,
		                tcp.charge_year,
		                tcp.bonus_mode,
		                tcp.prem_freq,
		                tcp.count_way,
		                tcbp.maturity_date,
		                tcbp.renew,
		                tcbp.initial_validate_date,
		                tcpo.field1,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.unit) unit,
		                sum(tcp.std_prem_af) std_prem_af,
		                sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.AMOUNT) as amount,
		                (SELECT 1
				           FROM APP___PAS__DBUSER.T_CONTRACT_BENE TCB
				          WHERE TCB.POLICY_ID = tcbp.POLICY_ID
				            AND TCB.BUSI_ITEM_ID = tcbp.BUSI_ITEM_ID
				            AND TCB.BENE_TYPE = 1
				            AND ROWNUM = 1) BENE_TYPE,
		                (SELECT T.TRANS_AMOUNT
						  FROM DEV_PAS.T_FUND_GROUP_TRANS T
						 WHERE T.TRANS_CODE = '53'
						   AND T.POLICY_ID = tcbp.policy_id
						   AND T.BUSI_ITEM_ID = tcbp.busi_item_id) as TRANS_AMOUNT
		           from APP___PAS__DBUSER.t_contract_busi_prod tcbp
		           join APP___PAS__DBUSER.t_contract_product tcp
		             on tcbp.busi_item_id = tcp.busi_item_id
		           join APP___PAS__DBUSER.t_business_product tbp
		             on tcbp.busi_prd_id = tbp.business_prd_id
		           left join  APP___PAS__DBUSER.t_Contract_Product_Other  tcpo
                     on tcpo.busi_item_id = tcbp.busi_item_id ]]>
                  <if test=" is_new_add_risk != null and is_new_add_risk != ''">
				  <![CDATA[  left join dev_pas.T_CONTRACT_MASTER TCM on tcbp.policy_id = tcm.policy_id ]]></if>
		           <![CDATA[where 1 = 1
		            and tcbp.policy_code = #{policy_code}]]>
		            <if test=" is_not_coverage != null and is_not_coverage != ''"><![CDATA[ and tcbp.LIABILITY_STATE <> 3 ]]></if>
		            <if test=" is_new_add_risk != null and is_new_add_risk != ''"> <![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE  ]]></if>
		          <![CDATA[group by tbp.product_code_sys,
		                   tbp.product_name_sys,
		                   tbp.product_category,
						   tbp.PRODUCT_CATEGORY1,
		                   tbp.PRODUCT_CATEGORY2,
		                   tcp.coverage_period,
		                   tcp.coverage_year,
		                   tcp.charge_period,
		                   tcp.charge_year,
		                   tcp.bonus_mode,
		                   tcp.prem_freq,
		                   tcbp.hesitation_period_day,
		                   tcbp.maturity_date,
		                   tcbp.renew,
		                   tcbp.initial_validate_date,
		                   tcbp.policy_id,
               			   tcbp.busi_item_id,
		                   tcpo.field1,
		                   tcp.count_way
 				 order by tbp.product_category]]>
	</select>

	<!-- 查询上次缴费日 -->
	<select id="PA_findContractDueTime" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			  SELECT tpa.policy_code,
			         SUM(TPA.FEE_AMOUNT) fee_amount,
			         max(TPA.DUE_TIME) DUE_TIME
			    FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
			   WHERE TPA.FEE_TYPE IN ('G001010000','G001070100',
			   						  'G003100000',
			                          'G003010000',
			                          'G003020100',
			                          'G003020200',
			                          'G003030100',
			                          'G003030200',
			                          'G003040100',
			                          'G003040200')
			     AND TPA.FEE_STATUS = '01'
			     AND TPA.POLICY_CODE = #{policy_code} ]]>
		<if test=" due_time != null and due_time != ''"><![CDATA[ and TPA.DUE_TIME <= #{due_time} ]]></if>	     
		<![CDATA[   GROUP BY tpa.policy_code]]>
	</select>
	
	<!-- 查询首期缴费金额 -->
	<select id="PA_findPolicyInitialPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			  SELECT tpa.policy_code,
			         SUM(TPA.FEE_AMOUNT) fee_amount
			    FROM APP___PAS__DBUSER.T_PREM TPA
			   WHERE TPA.FEE_SCENE_CODE='NB'
			     AND TPA.POLICY_CODE = #{policy_code} ]]>
		<if test=" due_time != null and due_time != ''"><![CDATA[ and TPA.DUE_TIME <= #{due_time} ]]></if>	     
		<![CDATA[   GROUP BY tpa.policy_code]]>
	</select>
	
	<!-- 查询终了红利金额 -->
	<select id="PA_findTerminalBonus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
           select sum(tba.terminal_bonus) terminal_bonus
             from dev_pas.t_bonus_allocate tba
            where tba.policy_code = #{policy_code} ]]>
	</select>

	<!-- 交通银行做特殊处理(手工单承保、退保、续期缴费、保全等业务场景) -->
	<select id="PA_findBCMDelayedPolicyByDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
           	SELECT TCM.POLICY_CODE,
			       TCM.SERVICE_BANK,
			       TCM.SERVICE_BANK_BRANCH,
			       TCM.SERVICE_HANDLER_CODE,
			       (SELECT MAX(TPA1.DUE_TIME)
			          FROM DEV_CAP.T_PREM_ARAP TPA1
			         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) DUE_TIME,
			       (SELECT MAX(TPA1.UNIT_NUMBER)
			          FROM DEV_CAP.T_PREM_ARAP TPA1
			         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER,
			       (SELECT MAX(TPA1.UPDATE_TIME)
			          FROM DEV_CAP.T_PREM_ARAP TPA1
			         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME,
			       TA.STATE,
			       TA.CITY,
			       TA.DISTRICT,
			       TCBP.BUSI_PROD_CODE,
			       TBP.PRODUCT_NAME_STD,
			       TCE.PAY_DUE_DATE,
			       TCBP.HESITATION_PERIOD_DAY,
			       (SELECT SUM(TCP1.STD_PREM_AF)
			           FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1 ,DEV_PAS.T_CONTRACT_BUSI_PROD TCBP1
		               WHERE TCBP1.POLICY_CODE = TCM.POLICY_CODE]]>
		               <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[AND TCBP1.APPLY_DATE = TCM.APPLY_DATE]]></if>
		               <![CDATA[
		               and TCBP1.POLICY_CODE = TCP1.POLICY_CODE
		               and TCBP1.BUSI_ITEM_ID = TCP1.BUSI_ITEM_ID
		               AND TCP1.PRODUCT_CODE IN
		                     (SELECT DISTINCT TPL.INTERNAL_ID
		                        FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                        JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
                          ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID)) STD_PREM_AF,
			       CASE
			         WHEN TCP.PREM_FREQ = '5' THEN
			          TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
			          TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
			         WHEN TCP.PREM_FREQ = '1' THEN
			          TCP.CHARGE_YEAR - 1
			       END TOTAL_PERIOD,
			       (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1 ,DEV_PAS.T_CONTRACT_BUSI_PROD TCBP1
		               WHERE TCBP1.POLICY_CODE = TCM.POLICY_CODE]]>
		               <if test=" is_newadd_risk != null and is_newadd_risk != ''"><![CDATA[AND TCBP1.APPLY_DATE = TCM.APPLY_DATE]]></if>
		               <![CDATA[
		               and TCBP1.POLICY_CODE = TCP1.POLICY_CODE
		               and TCBP1.BUSI_ITEM_ID = TCP1.BUSI_ITEM_ID
		               AND TCP1.PRODUCT_CODE IN
		                     (SELECT DISTINCT TPL.INTERNAL_ID
		                        FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                        JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
                          ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID)) TOTAL_PREM_AF,
			       TCE.POLICY_PERIOD, 
			       (SELECT CASE WHEN TPA.ACCOUNT_BANK = '12' THEN  TPA.ACCOUNT
                           ELSE TPA.NEXT_ACCOUNT  END         
                      FROM DEV_PAS.T_PAYER_ACCOUNT TPA
                     WHERE TPA.POLICY_ID = TCM.POLICY_ID
                       AND ROWNUM = 1) NEXT_ACCOUNT,  /*交通银行取首期帐号*/
                   (SELECT TPA.NEXT_ACCOUNT_NAME       
                      FROM DEV_PAS.T_PAYER_ACCOUNT TPA
                     WHERE TPA.POLICY_ID = TCM.POLICY_ID
                       AND ROWNUM = 1) NEXT_ACCOUNT_NAME,
                   TPA.ACCOUNT,
                   TCM.MEDIA_TYPE,
                   (SELECT MAX(TP.DUE_TIME)
                FROM DEV_PAS.T_PREM TP
               WHERE TP.FEE_SCENE_CODE='NB' AND TP.POLICY_CODE = TCM.POLICY_CODE) as nb_due_time
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM
			  JOIN DEV_PAS.T_POLICY_HOLDER TPH
			    ON TCM.POLICY_CODE = TPH.POLICY_CODE
			  JOIN DEV_PAS.T_CUSTOMER TC
			    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
			  JOIN DEV_PAS.T_ADDRESS TA
			    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
			  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
			    ON TCM.POLICY_ID = TCBP.POLICY_ID
			  JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
			    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
			  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
			    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
			  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
			    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			  JOIN DEV_PAS.T_PAYER_ACCOUNT TPA
                ON TPA.POLICY_ID = TCM.POLICY_ID    
			 WHERE 1 = 1
			   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
               AND TCM.POLICY_CODE = #{policy_code} ]]>
	</select>

	<!-- 查询理赔金额 -->
	<select id="PA_findPolicyClaimPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 SELECT tpa.policy_code, SUM(TPA.FEE_AMOUNT) fee_amount
			   FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
			  WHERE 1 = 1
			    AND TPA.DERIV_TYPE = '005'
			    AND TPA.FEE_STATUS = '01'
			    AND TPA.POLICY_CODE = #{policy_code} ]]>
		<![CDATA[  GROUP BY tpa.policy_code]]>
	</select>

	<!-- 补退费金额 -->
	<select id="PA_findPolicyPayPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 SELECT tpa.policy_code, SUM(TPA.FEE_AMOUNT) fee_amount
			   FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
			  WHERE 1 = 1
			    AND TPA.BUSINESS_CODE = #{business_code} ]]>
		<![CDATA[  GROUP BY tpa.policy_code]]>
	</select>
	
	<!-- 查询缴费信息 -->
	<select id="PA_findDelayedPayInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.POLICY_ID,
			   T.LIABILITY_STATE,
			   T.POLICY_CODE,
			   T.APPLY_CODE,
		       T.PAY_TYPE,
		       SUM(T.FEE_AMOUNT) FEE_AMOUNT,
		       '22' SERVICE_CODE,
		       T.PAY_MODE,
		       T.BANK_CODE,
		       T.BANK_ACCOUNT,
		       T.FINISH_TIME,
		       T.RENEWAL_COUNT,
		       T.RENEWAL_PAY_AMOUNT,
		       T.TOTAL_PAY_COUNT,
		       T.TOTAL_PAY_AMOUNT,
		       T.TOTAL_PAY_AMOUNT1
		  FROM (SELECT DISTINCT TPA.POLICY_CODE,
					   TCM.POLICY_ID,
					   TCM.APPLY_CODE,
					   TCM.LIABILITY_STATE,
		               CASE
		                 WHEN TPA.FEE_SCENE_CODE = 'NB' THEN
		                  0
		                 WHEN TPA.FEE_SCENE_CODE = 'RN' THEN
		                  1
		                 WHEN TPA.FEE_SCENE_CODE = 'CS' AND TPA.SERVICE_CODE = 'AM' THEN
		                  2
		               END PAY_TYPE,
		               TPA.FEE_AMOUNT,
		               TPA.PAY_MODE,
		               TPA.BANK_CODE,
		               TPA.BANK_ACCOUNT,
		               TPA.FINISH_TIME,
		               (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
		                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
		               (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
		                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   OR (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE = 'AM'))
		                   AND TP1.FEE_STATUS IN ('01','16','19')) TOTAL_PAY_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   OR (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE = 'AM'))
		                   AND TP1.FEE_STATUS IN ('01','16','19')) TOTAL_PAY_AMOUNT,
		                   (SELECT SUM(TP1.FEE_AMOUNT)
                      FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                     WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                       AND (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE in ('RE','SR'))
                       AND TP1.FEE_STATUS IN ('01','16','19')) TOTAL_PAY_AMOUNT1
		          FROM APP___PAS__DBUSER.T_PREM TPA
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		            ON TPA.POLICY_CODE = TCM.POLICY_CODE]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''">
				   <![CDATA[  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    				 ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TPA.ARAP_FLAG = '1' AND TCA.IS_NB_AGENT = '1' AND TPA.FEE_STATUS IN ('01','16','19') ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and tpa.finish_time >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[  and tpa.finish_time < #{batch_end_date}+1 ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
								  T.APPLY_CODE,
						          T.PAY_TYPE,
						          T.PAY_MODE,
						          T.BANK_CODE,
						          T.BANK_ACCOUNT,
						          T.FINISH_TIME,
						          T.RENEWAL_COUNT,
						          T.RENEWAL_PAY_AMOUNT,
						          T.TOTAL_PAY_COUNT,
						          T.TOTAL_PAY_AMOUNT,
						          T.TOTAL_PAY_AMOUNT1]]>
	</select>
	<!-- 查询保单状态信息 -->
	<select id="PA_findDelayedPolicyState" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.POLICY_ID,
			   T.LIABILITY_STATE,
			   T.POLICY_CODE,
		       T.PAY_TYPE,
		       SUM(T.FEE_AMOUNT) FEE_AMOUNT,
		       '22' SERVICE_CODE,
		       T.PAY_MODE,
		       T.BANK_CODE,
		       T.BANK_ACCOUNT,
		       T.FINISH_TIME,
		       T.RENEWAL_COUNT,
		       T.RENEWAL_PAY_AMOUNT,
		       T.TOTAL_PAY_COUNT,
		       T.TOTAL_PAY_AMOUNT
		  FROM (SELECT TPA.POLICY_CODE,
					   TCM.POLICY_ID,
					   TCM.LIABILITY_STATE,
		               CASE
		                 WHEN TPA.FEE_SCENE_CODE = 'NB' THEN
		                  0
		                 WHEN TPA.FEE_SCENE_CODE = 'RN' THEN
		                  1
		                 WHEN TPA.FEE_SCENE_CODE = 'CS' THEN
		                  2
		               END PAY_TYPE,
		               TPA.FEE_AMOUNT,
		               TPA.PAY_MODE,
		               TPA.BANK_CODE,
		               TPA.BANK_ACCOUNT,
		               TPA.FINISH_TIME,
		               (SELECT COUNT(DISTINCT TP1.FINISH_TIME)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')) RENEWAL_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')) RENEWAL_PAY_AMOUNT,
		               (SELECT COUNT(DISTINCT TP1.FINISH_TIME)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN', 'CS')) TOTAL_PAY_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN', 'CS')) TOTAL_PAY_AMOUNT
		          FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE PLC
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM--
		            ON PLC.POLICY_ID=TCM.POLICY_ID
		          JOIN APP___PAS__DBUSER.T_PREM TPA
		            ON TPA.POLICY_CODE = TCM.POLICY_CODE
				  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    				 ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TPA.ARAP_FLAG = '1' AND TCA.IS_NB_AGENT = '1' ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[and PLC.CHANGE_DATE >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and PLC.CHANGE_DATE < #{batch_end_date} +1 ]]></if>
		<!--  
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		-->
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 
		<if test=" submit_channel_list  != null and submit_channel_list.size()>0">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		 -->
		<if test="policy_code_list!=null and policy_code_list.size()>0">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="(" close=")" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
						          T.PAY_TYPE,
						          T.PAY_MODE,
						          T.BANK_CODE,
						          T.BANK_ACCOUNT,
						          T.FINISH_TIME,
						          T.RENEWAL_COUNT,
						          T.RENEWAL_PAY_AMOUNT,
						          T.TOTAL_PAY_COUNT,
						          T.TOTAL_PAY_AMOUNT]]>
	</select>
	
	
	<!-- 查询万能险账户变动信息-->
	<select id="PA_findDelayedFundTransChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT T.POLICY_ID,
		       T.LIABILITY_STATE,
		       T.POLICY_CODE
		  FROM (SELECT TCM.POLICY_CODE,
		               TCM.POLICY_ID,
		               TCM.LIABILITY_STATE
		          FROM APP___PAS__DBUSER.T_CONTRACT_INVEST TCI
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		            ON TCI.POLICY_ID= TCM.POLICY_ID
		          JOIN DEV_PAS.T_CONTRACT_AGENT            TCA
		            ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TCA.IS_NB_AGENT = '1' 
		           AND EXISTS (SELECT 'x' FROM APP___PAS__DBUSER.T_FUND_TRANS  TFT 
		                                 WHERE TFT.POLICY_ID = TCI.POLICY_ID
		           ]]>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and TFT.DEAL_TIME >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and TFT.DEAL_TIME <= #{batch_end_date} ]]></if>
		           <![CDATA[ )]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		 
		<if test=" submit_channel_list  != null and submit_channel_list.size>0">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="(" close=")" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	
		<if test=" policy_code_list  != null and policy_code_list.size()>0">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="(" close=")" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE
						         ]]>
	</select>
	
	
	
	
	<!-- 查询退保信息 -->
	<select id="PA_findSurrenderInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	select tcbp.policy_code,
	       sum(ts.surrender_amount) surrender_amount,
	       min(ts.hesitate_flag) hesitate_flag
	  from dev_pas.t_surrender ts
	  join dev_pas.t_contract_busi_prod tcbp
	    on ts.busi_item_id = tcbp.busi_item_id
	 where tcbp.liability_state = '3'
	   and tcbp.end_cause = '03'
	   and tcbp.policy_code = #{policy_code}
	 group by tcbp.policy_code
	</select>	
	
	<!-- 查询开门红撤单 -->
	<select id="PA_findDelayedCancellation" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, ]]>
		<if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
		<![CDATA[	    tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='3' and tcm.end_cause='15']]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.expiry_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>
		
	<!-- 退保回退 -->
	<select id="PA_findCTRollbackInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, 
					   'CIXEBACK' service_code, 
					   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
						(select sum(a.fee_amount)
			             from dev_pas.t_prem_arap a,dev_pas.t_contract_busi_prod b
			            where a.business_code = ts.accept_code
			            and a.BUSI_PROD_CODE = b.busi_prod_code
			            and b.apply_date = tcm.apply_date
			            and a.policy_code = b.policy_code)as surrender_amount,
			           sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT DISTINCT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE,
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.T_PREM TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.T_PREM TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
			                   tpc1.service_code as new_service_code,
			                   tpc1.validate_time as new_validate_time
				  from ]]>
					<if test="policy_code_list!=null and policy_code_list.size()>0">
					<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc
					join dev_pas.V_POLICY_CHANGE_ALL tpc1]]>
					</if>
					<if test="policy_code_list==null or policy_code_list.size()==0">
					<![CDATA[dev_pas.t_policy_change tpc
					join dev_pas.t_policy_change tpc1]]>
					</if>
					<![CDATA[
				   on  tpc.PRE_POLICY_CHG  = tpc1.POLICY_CHG_ID 
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_cs_accept_change tac
                on tac.accept_code = tpc.business_code
                and tac.accept_status = '18'
              left join dev_pas.t_cs_application tcsa
                on tcsa.change_id = tac.change_id
              left join dev_pas.t_surrender ts
                on ts.accept_code = tpc.business_code
                and ts.change_id = tcsa.change_id
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1'
				  and tpc.SERVICE_CODE ='RB'
					and tpc1.SERVICE_CODE in ('CT','IT','XT','EA')
					and to_char(tpc.VALIDATE_TIME,'YYYY-MM-DD') <> to_char(tpc1.VALIDATE_TIME,'YYYY-MM-DD')
					and not exists (select 0 from dev_pas.T_POLICY_CHANGE t where t.PRE_POLICY_CHG = tpc.POLICY_CHG_ID and t.SERVICE_CODE <> 'CLMBACK')
				  ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpc.finish_time >= #{batch_date} and tpc.finish_time < #{batch_date} +1]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tpc.finish_time >= #{batch_start_date}]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tpc.finish_time < #{batch_end_date} +1]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,
						  tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
				tpc.PRE_POLICY_CHG,
				tpc.SERVICE_CODE,
				tpc.POLICY_CHG_ID,
				tpc1.POLICY_CHG_ID,
                tpac.pay_next,
                tpc.insert_time,
                ts.accept_code,
                tpc1.service_code,
                tpc1.VALIDATE_TIME    ]]>
	</select>
	<!-- 理赔回退 -->
	<select id="PA_findCLMRollbackInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id,
				       'CLMBACK' service_code, 
					   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       case when sum(ts.fee_amount) is null then (select sum(a.fee_amount) from dev_pas.t_prem_arap a where a.business_code=ts.accept_code) else sum(ts.fee_amount) end surrender_amount,
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT DISTINCT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE,
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.T_PREM TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.T_PREM TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT
				  from ]]>
					<if test="policy_code_list!=null and policy_code_list.size()>0">
					<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc
					join dev_pas.V_POLICY_CHANGE_ALL tpc1]]>
					</if>
					<if test="policy_code_list==null or policy_code_list.size()==0">
					<![CDATA[dev_pas.t_policy_change tpc
					join dev_pas.t_policy_change tpc1]]>
					</if>
					<![CDATA[
				   on  tpc.PRE_POLICY_CHG  = tpc1.POLICY_CHG_ID 
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_cs_accept_change tac
                on tac.accept_code = tpc.business_code
                and tac.accept_status = '18'
              left join dev_pas.t_cs_application tcsa
                on tcsa.change_id = tac.change_id
              left join dev_pas.t_surrender ts
                on ts.accept_code = tpc.business_code
                and ts.change_id = tcsa.change_id
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1'
				  and tpc.SERVICE_CODE ='CLMBACK'
				  and to_char(tpc.VALIDATE_TIME,'YYYY-MM-DD') <> to_char(tpc1.VALIDATE_TIME,'YYYY-MM-DD')
				   ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpc.finish_time >= #{batch_date} and tpc.finish_time < #{batch_date}+1]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tpc.finish_time >= #{batch_start_date}]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tpc.finish_time < #{batch_end_date}+1]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,
						  tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,                                                                                                            
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
				tpc.SERVICE_CODE,
                tpac.pay_next,
                tpc.insert_time,
                ts.accept_code   ]]>
	</select>
	
	<!-- 查询客户实际累计缴纳金额 -->
	<select id="PA_findAPDelayedPolicyByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
  SELECT SUM(Z.FEE_AMOUNT) AS FEE_AMOUNT,
       (SELECT T.FEE_AMOUNT
          FROM DEV_PAS.V_PREM_ALL T
         WHERE T.POLICY_CODE = Z.POLICY_CODE
           AND T.FEE_STATUS IN ('01', '16', '19')
           AND T.FEE_SCENE_CODE IN ('NB', 'RN')
           AND ROWNUM = 1
           AND T.FINISH_TIME =
               (SELECT MAX(T.FINISH_TIME)
                  FROM DEV_PAS.T_PREM T
                 WHERE T.POLICY_CODE = Z.POLICY_CODE
                   AND T.FEE_STATUS IN ('01', '16', '19')
                   AND T.FEE_SCENE_CODE IN ('NB', 'RN'))) AMOUNT
  FROM (SELECT (CASE
                 WHEN A.ARAP_FLAG = '1' THEN
                  A.FEE_AMOUNT
                 ELSE
                  A.FEE_AMOUNT * -1
               END) AS FEE_AMOUNT, /*保费金额*/
               B.POLICY_CODE
          FROM DEV_PAS.V_PREM_ALL               A,
               DEV_PAS.T_CONTRACT_MASTER    B,
               DEV_PAS.T_CONTRACT_BUSI_PROD C
         WHERE B.POLICY_CODE = C.POLICY_CODE
           AND B.APPLY_DATE = C.APPLY_DATE
           AND C.POLICY_CODE = A.POLICY_CODE
           AND C.BUSI_PROD_CODE = A.BUSI_PROD_CODE
           AND A.FEE_STATUS IN ('01', '16', '19')
           AND (A.FEE_SCENE_CODE IN ('RN', 'NB') OR
               (A.FEE_SCENE_CODE = 'CS' AND A.SERVICE_CODE IN ('PA', 'PT')))
           AND A.POLICY_CODE = #{policy_code}) Z
           GROUP BY Z.POLICY_CODE]]>
	</select>
	
	<!-- 查询客户除新增附加险外实际累计缴纳金额 -->
	<select id="PA_findNotNewAddRiskAmountByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
  		SELECT ]]>
  		<if test=" business_code  != null and business_code !=''">
			<![CDATA[  
			sum((select ts.fee_amount from dev_pas.t_surrender ts
				    where ts.accept_code = #{business_code}
				    and C.BUSI_ITEM_ID = ts.BUSI_ITEM_ID)) AS fee_amount,
			]]>
		</if>
  		<![CDATA[
  		D.STD_PREM_AF AS FEE_AMOUNT1,
  		SUM(CASE
                 WHEN A.ARAP_FLAG = '1' THEN
                  A.FEE_AMOUNT
                 ELSE
                  A.FEE_AMOUNT * -1
               END) AS renewal_pay_amount, /*累计缴费金额*/
				(SELECT TCE.NEXT_PREM
                      FROM DEV_PAS.T_CONTRACT_EXTEND TCE
                     WHERE C.POLICY_CODE = TCE.POLICY_CODE
                       AND C.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
                       AND TCE.PAY_DUE_DATE =
                           (SELECT MAX(T.PAY_DUE_DATE)
                              FROM DEV_PAS.T_CONTRACT_EXTEND T
                             WHERE C.POLICY_CODE = T.POLICY_CODE
                               AND C.BUSI_ITEM_ID = T.BUSI_ITEM_ID)
                       AND ROWNUM = 1) as NEXT_PAY_ACCOUNT,
                       (SELECT MAX(T.PAY_DUE_DATE)
                              FROM DEV_PAS.T_CONTRACT_EXTEND T
                             WHERE C.POLICY_CODE = T.POLICY_CODE
                               AND C.BUSI_ITEM_ID = T.BUSI_ITEM_ID) as NEXT_PAY_DATE
          FROM DEV_PAS.V_PREM_ALL               A,
               DEV_PAS.T_CONTRACT_MASTER    B,
               DEV_PAS.T_CONTRACT_BUSI_PROD C,
               DEV_PAS.T_CONTRACT_PRODUCT D
         WHERE B.POLICY_CODE = C.POLICY_CODE
           AND B.APPLY_DATE = C.APPLY_DATE
           AND C.POLICY_CODE = A.POLICY_CODE
           AND C.BUSI_PROD_CODE = A.BUSI_PROD_CODE
           AND D.POLICY_CODE = C.POLICY_CODE
           AND C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
           AND A.FEE_STATUS IN ('01', '16', '19')
           AND (A.FEE_SCENE_CODE IN ('RN', 'NB') OR
               (A.FEE_SCENE_CODE = 'CS' AND A.SERVICE_CODE IN ('PA', 'PT','AM','RE','SR')))
           AND A.POLICY_CODE = #{policy_code}
           group by D.STD_PREM_AF,C.POLICY_CODE,C.BUSI_ITEM_ID]]>
	</select>
	
	<!-- 查询保单最后一次承保或续期缴费方式 -->
	<select id="PA_findLastRNPayMode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
  			SELECT A.PAY_MODE
	        FROM DEV_PAS.T_PREM_ARAP A,DEV_PAS.T_PREM B
	        WHERE B.POLICY_CODE = #{policy_code}
	        AND B.FEE_SCENE_CODE IN ('RN', 'NB')
	        AND B.FINISH_TIME IS NOT NULL
	        AND A.UNIT_NUMBER = B.UNIT_NUMBER
	        AND ROWNUM = 1
	        ORDER BY B.FINISH_TIME DESC
  		]]>
	</select>
	
	
	<!-- 查询复效特殊情况（指特殊处理需要返回多条复效保费信息场景） -->
	<select id="PA_findSpecialRESRInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select tcm.policy_code,
               tcm.apply_code,
               tcm.policy_id, 
               '29' SERVICE_CODE, 
               tcm.validate_date,
               tcm.liability_state,
               tcm.lapse_date,
               tcm.expiry_date,
               tcm.apply_date,
               tcm.issue_date,
               tcm.end_cause,
               tcm.SUBINPUT_TYPE,
               tcm.SUBMIT_CHANNEL,
               tcm.lapse_cause,
               tca.CHANNEL_TYPE,
               tpa.acknowledge_date,
               min(ts.hesitate_flag) hesitate_flag,
               (SELECT DISTINCT tp1.UNIT_NUMBER
                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                   AND tp1.BUSINESS_CODE = tpc.business_code
                   and rownum = 1) UNIT_NUMBER,
               case when sum(ts.fee_amount) is null 
               then (select sum(a.fee_amount) from dev_pas.v_prem_arap_all a 
               where tcm.policy_code = a.policy_code and a.business_code=ts.accept_code) 
               else sum(ts.fee_amount) end surrender_amount, 
               sum(ts.fee_amount) fee_amount,
               tcsa.apply_name,
               tcm.service_bank_branch,
               (select max(tcbp.hesitation_period_day)
                  from dev_pas.t_contract_busi_prod tcbp
                 where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
               tcsa.customer_id,
               tcsa.service_type,
               tcsa.bank_channel,
               (SELECT A.ORGAN_CODE 
                  FROM DEV_PAS.T_CONTRACT_AGENT A 
                 WHERE A.POLICY_CODE = TCM.POLICY_CODE 
                   AND A.IS_NB_AGENT = '1') AS organ_code ,
               tpac.next_account,
               tpac.pay_next,
               tpc.business_code,
               max(tpc.validate_time) validate_time,
               (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE, 
                (SELECT SUM(TCP1.TOTAL_PREM_AF)
                    FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1 ,DEV_PAS.T_CONTRACT_BUSI_PROD TCBP1
                   WHERE TCBP1.POLICY_CODE = TCM.POLICY_CODE]]>
                   <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[AND TCBP1.APPLY_DATE = TCM.APPLY_DATE]]></if>
                   <![CDATA[
                   and TCBP1.POLICY_CODE = TCP1.POLICY_CODE
                   and TCBP1.BUSI_ITEM_ID = TCP1.BUSI_ITEM_ID
                   AND TCP1.PRODUCT_CODE IN
                         (SELECT DISTINCT TPL.INTERNAL_ID
                            FROM DEV_PDS.T_PRODUCT_LIFE TPL
                            JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
                          ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID)) total_prem_af,
               (select sum(tcp.initial_amount)
                  from dev_pas.t_contract_product tcp
                 where tcm.policy_code = tcp.policy_code) initial_amount,
               (select sum(tcp.bonus_sa)
                  from dev_pas.t_contract_product tcp
                 where tcm.policy_code = tcp.policy_code) bonus_sa,
                         (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
                              (SELECT COUNT( TP1.UNIT_NUMBER)
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
                              (SELECT SUM(TP1.FEE_AMOUNT)
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
                         tpc.CHANGE_FLAG,
                         tpc.POLICY_CHG_ID,
                         (SELECT SUM(TP1.FEE_AMOUNT)
                            FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                           WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                             AND tp1.BUSINESS_CODE = tpc.business_code) as transaction_amount
                           
          from ]]>
        <if test="policy_code_list!=null and policy_code_list.size()>0">
        <![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
        </if>
        <if test="policy_code_list==null or policy_code_list.size()==0">
        <![CDATA[dev_pas.t_policy_change tpc]]> 
        </if>
        <![CDATA[
          join dev_pas.t_contract_master tcm
            on tcm.policy_id = tpc.policy_id
          left join dev_pas.t_contract_busi_prod tcbp
            on tcbp.policy_id = tcm.policy_id]]> 
          <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
          <![CDATA[left join dev_pas.t_contract_product tcp
            on tcp.busi_item_id = tcbp.busi_item_id
          left join dev_pas.t_cs_accept_change tac
                on tac.accept_code = tpc.business_code
                and tac.accept_status = '18'
              left join dev_pas.t_cs_application tcsa
                on tcsa.change_id = tac.change_id
              left join dev_pas.t_surrender ts
                on ts.accept_code = tpc.business_code
                and ts.change_id = tcsa.change_id
                AND TS.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
          left join dev_pas.t_policy_acknowledgement tpa
            on tpa.policy_id = tcm.policy_id
          left join dev_pas.t_payer_account tpac
            on tpac.policy_id = tcm.policy_id
          join dev_pas.t_contract_agent tca
             on tcm.policy_code = tca.policy_code
          where 1 = 1  and tca.is_nb_agent = '1']]>
    <if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
    <if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>      
    <if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>

    <if test=" batch_date != null and batch_date != '' "><![CDATA[and  tpc.finish_time >= #{batch_date} and tpc.finish_time < #{batch_date} +1 ]]></if>
    <if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[  and tpc.finish_time >= #{batch_start_date}  ]]></if>
    <if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[and tpc.finish_time < #{batch_end_date} +1 ]]></if>
    <if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
    <!-- 快捷保全只有CC -->
    <![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
    <if test=" service_codes  != null and service_codes.size()!=0">
      <![CDATA[ and tpc.service_code in ( ]]>
      <foreach collection="service_codes" item="service_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{service_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
      <![CDATA[ and tcm.policy_code not in ( ]]>
      <foreach collection="no_policyCodes" item="no_policyCode"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{no_policyCode} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>

    <if test=" liability_state_end != null and liability_state_end != ''">
      <![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
    </if>
    <if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
      <![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
      <![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
      <foreach collection="lapse_cause_list" item="lapse_cause"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{lapse_cause} ]]>
      </foreach>
      <![CDATA[ )) ]]>
    </if>
    <![CDATA[ ) ]]>
    <if test=" policy_code_list  != null and policy_code_list">
      <![CDATA[ and tcm.policy_code in (]]>
      <foreach collection="policy_code_list" item="policy_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{policy_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" submit_channel_list  != null and submit_channel_list">
      <![CDATA[ and tcm.submit_channel in (]]>
      <foreach collection="submit_channel_list" item="submit_channel"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{submit_channel} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
    <if test=" company_id != null and company_id != ''">
     <![CDATA[ and exists (
         ((select 1 from dev_pas.t_policy_holder tph, dev_pas.t_trust_company ttc 
                 where tph.policy_id = tpc.policy_id and tph.customer_id = ttc.customer_id and ttc.company_id = #{company_id})
         union
         (select 2 from dev_pas.t_contract_bene tcb, dev_pas.t_trust_company ttc 
                 where tcb.policy_id = tpc.policy_id and tcb.company_id = ttc.company_id and tcb.bene_kind = '02' and ttc.company_id = #{company_id}))
     )]]>
    </if>
    <![CDATA[group by tcm.policy_code,
                  tcm.apply_code,
                  tcm.policy_id,]]>
    <if test=" liability_state_end == null or liability_state_end == ''">
      <![CDATA[ tpc.service_code, ]]>
    </if>
<![CDATA[             tcm.validate_date,
              tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcm.lapse_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
                tpac.pay_next,
                tpc.CHANGE_FLAG,
                tpc.POLICY_CHG_ID,
                ts.accept_code]]>
	</select>
	
		<!-- 查询复效特殊复效信息 -->
	<select id="PA_findRESRInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				SELECT T.POLICY_ID,
				T.APPLY_CODE,
               T.LIABILITY_STATE,
               T.POLICY_CODE,
               T.PAY_TYPE,
               T.VALIDATE_DATE,
               SUM(T.FEE_AMOUNT) FEE_AMOUNT,
               '29' SERVICE_CODE,
               T.PAY_MODE,
               T.BANK_CODE,
               T.BANK_ACCOUNT,
               T.FINISH_TIME,
               T.DUE_TIME,
               T.RENEWAL_COUNT,
               T.RENEWAL_PAY_AMOUNT,
               T.TOTAL_PAY_COUNT,
               T.TOTAL_PAY_AMOUNT
          FROM (SELECT TPA.POLICY_CODE,
                       TCM.POLICY_ID,
                       TCM.APPLY_CODE,
                       TCM.LIABILITY_STATE,
                       TCM.VALIDATE_DATE,
                       1 PAY_TYPE,
                       TPA.FEE_AMOUNT,
                       TPA.PAY_MODE,
                       TPA.BANK_CODE,
                       TPA.BANK_ACCOUNT,
                       TPA.FINISH_TIME,
                       TPA.DUE_TIME,
                       (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                        WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                        AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR
                       (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE IN ('RE', 'SR')))
                       AND TP1.FEE_STATUS IN ('01', '16', '19')) RENEWAL_COUNT,
                       (SELECT SUM(TP1.FEE_AMOUNT)
                          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                           AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')
                           AND TP1.FEE_STATUS IN ('01', '16', '19')) RENEWAL_PAY_AMOUNT,
                       (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER) 
		                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                           AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR
                               (TP1.FEE_SCENE_CODE = 'CS' AND
                               TP1.SERVICE_CODE IN ('AM', 'RE','SR')))
                           AND TP1.FEE_STATUS IN ('01', '16', '19')) TOTAL_PAY_COUNT,
                       (SELECT SUM(TP1.FEE_AMOUNT)
                          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                           AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR
                               (TP1.FEE_SCENE_CODE = 'CS' AND
                               TP1.SERVICE_CODE IN ('AM', 'RE','SR')))
                           AND TP1.FEE_STATUS IN ('01', '16', '19')) TOTAL_PAY_AMOUNT
		          FROM APP___PAS__DBUSER.V_PREM_ALL TPA
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		            ON TPA.POLICY_CODE = TCM.POLICY_CODE
				  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    				 ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TPA.ARAP_FLAG = '1' AND TCA.IS_NB_AGENT = '1' AND TPA.FEE_STATUS IN ('01','19') AND TPA.SERVICE_CODE IN ('RE','SR') 
                 and tcm.policy_code =  #{policy_code} ) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
						          T.PAY_TYPE,
						          T.PAY_MODE,
						          T.BANK_CODE,
						          T.BANK_ACCOUNT,
						          T.FINISH_TIME,
						          T.DUE_TIME,
						          T.RENEWAL_COUNT,
						          T.RENEWAL_PAY_AMOUNT,
						          T.TOTAL_PAY_COUNT,
						          T.VALIDATE_DATE,
                                  T.APPLY_CODE,
						          T.TOTAL_PAY_AMOUNT
		]]>
	</select>
	
	
		<!-- 查询续期缴费金额（不计算新增附加险） -->
	<select id="PA_findrenewalPayAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select sum(a.FEE_AMOUNT) as renewal_pay_amount
		  from dev_pas.v_prem_all           a,
		       dev_pas.t_contract_master    b,
		       dev_pas.t_contract_busi_prod c
		 where a.policy_Code = #{policy_code}
		   and a.BUSI_PROD_CODE = c.busi_prod_code
		   AND (A.FEE_SCENE_CODE IN ('NB', 'RN') OR (A.FEE_SCENE_CODE='CS' AND A.SERVICE_CODE IN ('RE','SR')))
       		AND A.FEE_STATUS IN ('01','16','19')
		   and a.POLICY_CODE = c.policy_code
		   and b.policy_code = a.POLICY_CODE
		   and b.policy_code = c.policy_code
		   and c.apply_date = b.apply_date
		]]>
	</select>
	
	<!-- 查询操作的保全项（不含新增附加险） --> 
	<select id="PA_findIsNewAddRiskByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select t.policy_code
              from dev_pas.v_Prem_all t
              where t.UNIT_NUMBER =  #{unit_number}
                 and exists (SELECT 1
                   FROM App___Pas__Dbuser.t_Contract_Master tcm,
                   		App___Pas__Dbuser.t_contract_product tcp,
              	 		App___Pas__Dbuser.t_Contract_Busi_Prod tcbp
                   where t.POLICY_CODE = tcm.POLICY_CODE
                     and t.ITEM_ID = tcp.item_id
                     and tcp.busi_item_id = tcbp.busi_item_id
                     AND tcm.policy_code = tcbp.policy_code
                     and tcbp.Apply_Date = tcm.Apply_Date
                 )
		]]>
	</select>
	
		<!-- 查询产品即将到期 -->
	<select id="PA_findBusiExpireInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, 
				        '32' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='1' ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.expiry_date = #{batch_date} + 31 ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} + 31]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} + 31 ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>
	
	
			<!-- 查询即将续期缴费 -->
	<select id="PA_findNextPayInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, 
		                '33'  service_code, 	
		                tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                (SELECT TCE.NEXT_PREM
                      FROM DEV_PAS.T_CONTRACT_EXTEND TCE
                     WHERE tcbp.POLICY_CODE = TCE.POLICY_CODE
                       AND tcbp.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
                       AND TCE.PAY_DUE_DATE =
                           (SELECT MAX(T.PAY_DUE_DATE)
                              FROM DEV_PAS.T_CONTRACT_EXTEND T
                             WHERE tcbp.POLICY_CODE = T.POLICY_CODE
                               AND tcbp.BUSI_ITEM_ID = T.BUSI_ITEM_ID)
                       AND ROWNUM = 1) as NEXT_PAY_ACCOUNT,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			   join  dev_pas.T_CONTRACT_EXTEND tce
                    on  tcm.policy_id = tce.policy_id
                    ]]>	 
    				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[left join dev_pas.v_prem_arap_all tpar
					    on tpar.policy_code = tcbp.policy_code
					    and tpar.busi_prod_code=tcbp.busi_prod_code
					    and tpar.busi_item_id=tcbp.busi_item_id
					    and tpar.policy_code = tcm.policy_code]]> 
					 </if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[left join dev_pas.t_prem_arap tpar
					    on tpar.policy_code = tcbp.policy_code
					    and tpar.busi_prod_code=tcbp.busi_prod_code
					    and tpar.busi_item_id=tcbp.busi_item_id
					    and tpar.policy_code = tcm.policy_code]]> 
					 </if>
				<![CDATA[  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='1' and tpar.fee_status = '00' ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tce.pay_due_date = #{batch_date} + 11 ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tce.pay_due_date >= #{batch_start_date} + 11]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tce.pay_due_date <= #{batch_end_date} + 11 ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcbp.POLICY_CODE,
                           tcbp.BUSI_ITEM_ID,
					       tcm.service_bank_branch]]>
	</select>
	
	
			<!-- 查询到期未领取-->
	<select id="PA_findUnclaimedInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
		                '34' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                (select sum(tpd.fee_amount)
				        from dev_pas.t_pay_due tpd
				        where tcm.policy_code = tpd.policy_code 
				        and tpd.fee_status = '00') trade_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			  join dev_pas.t_pay_due tpd
    				 on tcm.policy_id = tpd.policy_id
    				 and tcm.policy_code = tpd.policy_code  	 
				  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='3' and  tcm.end_cause= '01' and tpd.fee_status = '00' ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcbp.maturity_date = #{batch_date} - 14 ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcbp.maturity_date >= #{batch_start_date} - 14 ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcbp.maturity_date <= #{batch_end_date} - 14  ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>
	
	

	<!-- 查询续期缴费失败-->
	<select id="PA_findPayFailInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, 
				        '35' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			   join  dev_pas.T_CONTRACT_EXTEND tce
                    on  tcm.policy_id = tce.policy_id
                    ]]>	 
    				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[left join dev_pas.v_prem_arap_all tpar
					    on tpar.policy_code = tcbp.policy_code
					    and tpar.busi_prod_code=tcbp.busi_prod_code
					    and tpar.busi_item_id=tcbp.busi_item_id
					    and tpar.policy_code = tcm.policy_code]]> 
					 </if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[left join dev_pas.t_prem_arap tpar
					    on tpar.policy_code = tcbp.policy_code
					    and tpar.busi_prod_code=tcbp.busi_prod_code
					    and tpar.busi_item_id=tcbp.busi_item_id
					    and tpar.policy_code = tcm.policy_code]]> 
					 </if>
				<![CDATA[  where 1 = 1  and tca.is_nb_agent = '1'  and tpar.fee_status = '03' ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tce.pay_due_date = #{batch_date} - 44 ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tce.pay_due_date >= #{batch_start_date} - 44]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tce.pay_due_date <= #{batch_end_date} - 44 ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>
	
	<!-- 查询承保日提数 -->
	<select id="PA_findIssueDateInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, 
				        '36' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1 and tca.is_nb_agent = '1' and tcm.liability_state='1' and tcm.validate_date > tcm.issue_date ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.issue_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.issue_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.issue_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>
	
	<!-- 查询受益人（含信托）变更 -->
	<select id="PA_findBCAndTCByType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.lapse_cause,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       (SELECT DISTINCT tp1.UNIT_NUMBER
				          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
				         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
				           AND tp1.BUSINESS_CODE = tpc.business_code
				           and rownum = 1) UNIT_NUMBER,
				       case when sum(ts.fee_amount) is null 
				       then (select sum(a.fee_amount) from dev_pas.v_prem_arap_all a 
				       where tcm.policy_code = a.policy_code and a.business_code=ts.accept_code) 
				       else sum(ts.fee_amount) end surrender_amount, 
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE, 
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
			                   tpc.CHANGE_FLAG,
			                   tpc.POLICY_CHG_ID,
			                   (SELECT SUM(TP1.FEE_AMOUNT)
		                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                         AND tp1.BUSINESS_CODE = tpc.business_code) as transaction_amount,
					 tpc.finish_time
		                       
				  from ]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[dev_pas.t_policy_change tpc]]> 
				</if>
				<![CDATA[
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
		            AND TS.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			where 1 = 1  and tca.is_nb_agent = '1']]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and tpc.finish_time >= #{batch_start_date} - 1 ]]></if> 
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and tpc.finish_time < #{batch_end_date}]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" company_id != null and company_id != ''">
	 	 <![CDATA[ and exists (
		     ((select 1 from dev_pas.t_policy_holder tph, dev_pas.t_trust_company ttc 
				         where tph.policy_id = tpc.policy_id and tph.customer_id = ttc.customer_id and ttc.company_id = #{company_id})
				 union
				 (select 2 from dev_pas.t_contract_bene tcb, dev_pas.t_trust_company ttc 
				         where tcb.policy_id = tpc.policy_id and tcb.company_id = ttc.company_id and tcb.bene_kind = '02' and ttc.company_id = #{company_id}))
		 )]]>
		</if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcm.lapse_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
                tpac.pay_next,
                tpc.CHANGE_FLAG,
                tpc.POLICY_CHG_ID,
                ts.accept_code,
				 tpc.finish_time   ]]>
	</select>
		
	<!-- 查询红利领取形式变更 -->
	<select id="PA_findHCByType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.lapse_cause,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       (SELECT DISTINCT tp1.UNIT_NUMBER
				          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
				         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
				           AND tp1.BUSINESS_CODE = tpc.business_code
				           and rownum = 1) UNIT_NUMBER,
				       case when sum(ts.fee_amount) is null 
				       then (select sum(a.fee_amount) from dev_pas.v_prem_arap_all a 
				       where tcm.policy_code = a.policy_code and a.business_code=ts.accept_code) 
				       else sum(ts.fee_amount) end surrender_amount, 
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE, 
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
			                   tpc.CHANGE_FLAG,
			                   tpc.POLICY_CHG_ID,
			                   (SELECT SUM(TP1.FEE_AMOUNT)
		                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                         AND tp1.BUSINESS_CODE = tpc.business_code) as transaction_amount,
					 tpc.finish_time
		                       
				  from ]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[dev_pas.t_policy_change tpc]]> 
				</if>
				<![CDATA[
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
		            AND TS.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			where 1 = 1  and tca.is_nb_agent = '1']]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and tpc.finish_time >= #{batch_start_date} - 1 ]]></if> 
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and tpc.finish_time < #{batch_end_date}]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<if test=" company_id != null and company_id != ''">
	 	 <![CDATA[ and exists (
		     ((select 1 from dev_pas.t_policy_holder tph, dev_pas.t_trust_company ttc 
				         where tph.policy_id = tpc.policy_id and tph.customer_id = ttc.customer_id and ttc.company_id = #{company_id})
				 union
				 (select 2 from dev_pas.t_contract_bene tcb, dev_pas.t_trust_company ttc 
				         where tcb.policy_id = tpc.policy_id and tcb.company_id = ttc.company_id and tcb.bene_kind = '02' and ttc.company_id = #{company_id}))
		 )]]>
		</if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcm.lapse_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
                tpac.pay_next,
                tpc.CHANGE_FLAG,
                tpc.POLICY_CHG_ID,
                ts.accept_code,
					 tpc.finish_time   ]]>
	</select>
	
	<!-- 查询减额交清 -->
	<select id="PA_findPUByType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.lapse_cause,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       (SELECT DISTINCT tp1.UNIT_NUMBER
				          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
				         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
				           AND tp1.BUSINESS_CODE = tpc.business_code
				           and rownum = 1) UNIT_NUMBER,
				       case when sum(ts.fee_amount) is null 
				       then (select sum(a.fee_amount) from dev_pas.v_prem_arap_all a 
				       where tcm.policy_code = a.policy_code and a.business_code=ts.accept_code) 
				       else sum(ts.fee_amount) end surrender_amount, 
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE, 
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
			                   tpc.CHANGE_FLAG,
			                   tpc.POLICY_CHG_ID,
			                   (SELECT SUM(TP1.FEE_AMOUNT)
		                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                         AND tp1.BUSINESS_CODE = tpc.business_code) as transaction_amount,
					 tpc.finish_time
		                       
				  from ]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[dev_pas.t_policy_change tpc]]> 
				</if>
				<![CDATA[
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
		            AND TS.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			where 1 = 1  and tca.is_nb_agent = '1']]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and tpc.finish_time >= #{batch_start_date} - 1 ]]></if> 
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and tpc.finish_time < #{batch_end_date}]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<if test=" company_id != null and company_id != ''">
	 	 <![CDATA[ and exists (
		     ((select 1 from dev_pas.t_policy_holder tph, dev_pas.t_trust_company ttc 
				         where tph.policy_id = tpc.policy_id and tph.customer_id = ttc.customer_id and ttc.company_id = #{company_id})
				 union
				 (select 2 from dev_pas.t_contract_bene tcb, dev_pas.t_trust_company ttc 
				         where tcb.policy_id = tpc.policy_id and tcb.company_id = ttc.company_id and tcb.bene_kind = '02' and ttc.company_id = #{company_id}))
		 )]]>
		</if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcm.lapse_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
                tpac.pay_next,
                tpc.CHANGE_FLAG,
                tpc.POLICY_CHG_ID,
                ts.accept_code,
					 tpc.finish_time   ]]>
	</select>
	
	<!-- 查询投保人变更（信托） -->
	<select id="PA_findALByType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.lapse_cause,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       (SELECT DISTINCT tp1.UNIT_NUMBER
				          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
				         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
				           AND tp1.BUSINESS_CODE = tpc.business_code
				           and rownum = 1) UNIT_NUMBER,
				       case when sum(ts.fee_amount) is null 
				       then (select sum(a.fee_amount) from dev_pas.v_prem_arap_all a 
				       where tcm.policy_code = a.policy_code and a.business_code=ts.accept_code) 
				       else sum(ts.fee_amount) end surrender_amount, 
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE, 
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
			                   tpc.CHANGE_FLAG,
			                   tpc.POLICY_CHG_ID,
			                   (SELECT SUM(TP1.FEE_AMOUNT)
		                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                         AND tp1.BUSINESS_CODE = tpc.business_code) as transaction_amount,
					 tpc.finish_time
		                       
				  from ]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[dev_pas.t_policy_change tpc]]> 
				</if>
				<![CDATA[
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
		            AND TS.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			where 1 = 1  and tca.is_nb_agent = '1']]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and tpc.finish_time >= #{batch_start_date} - 1 ]]></if> 
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and tpc.finish_time < #{batch_end_date}]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" company_id != null and company_id != ''">
	 	 <![CDATA[ and exists (
		     ((select 1 from dev_pas.t_policy_holder tph, dev_pas.t_trust_company ttc 
				         where tph.policy_id = tpc.policy_id and tph.customer_id = ttc.customer_id and ttc.company_id = #{company_id})
				 union
				 (select 2 from dev_pas.t_contract_bene tcb, dev_pas.t_trust_company ttc 
				         where tcb.policy_id = tpc.policy_id and tcb.company_id = ttc.company_id and tcb.bene_kind = '02' and ttc.company_id = #{company_id}))
		 )]]>
		</if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcm.lapse_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
                tpac.pay_next,
                tpc.CHANGE_FLAG,
                tpc.POLICY_CHG_ID,
                ts.accept_code,
					 tpc.finish_time   ]]>
	</select>
	
	<select id="PA_findAllServiceItem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select a.service_code from dev_pas.t_service a 
			 ]]>
	</select>
	
	<select id="PA_findDelayedTrustCompany" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT a.company_id FROM dev_pas.T_TRUST_COMPANY A WHERE 1 = 1  ]]>
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</select>
	
	<select id="PA_findDelayedTotalPayCount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(DISTINCT TP1.UNIT_NUMBER) as total_pay_count
		                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                         WHERE TP1.POLICY_CODE = #{policy_code}
                           AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR
                               (TP1.FEE_SCENE_CODE = 'CS' AND
                               TP1.SERVICE_CODE IN ('AM', 'RE','SR')))
                           AND TP1.FEE_STATUS IN ('01', '16', '19')  ]]>
	</select>
	
	<!-- 查询保全（当天做过非涉及保费变化的保全项目） -->
	<select id="PA_findCSExceptPremChg" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.lapse_cause,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       (SELECT DISTINCT tp1.UNIT_NUMBER
				          FROM APP___PAS__DBUSER.V_PREM_ALL TP1
				         WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
				           AND tp1.BUSINESS_CODE = tpc.business_code
				           and rownum = 1) UNIT_NUMBER,
				       case when sum(ts.fee_amount) is null 
				       then (select sum(a.fee_amount) from dev_pas.v_prem_arap_all a 
				       where tcm.policy_code = a.policy_code and a.business_code=ts.accept_code) 
				       else sum(ts.fee_amount) end surrender_amount, 
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (SELECT DISTINCT TP1.pay_mode
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) pay_mode,
                         (SELECT CASE 
                           WHEN TP1.FEE_SCENE_CODE='NB' THEN
                            0
                           WHEN TP1.FEE_SCENE_CODE='RN' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'RE' THEN
                            1
                            WHEN TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE = 'SR' THEN
                            1
                         END
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) PAY_TYPE,
                         (SELECT DISTINCT tp1.BANK_CODE
                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND tp1.BUSINESS_CODE=tpc.business_code and rownum=1) BANK_CODE, 
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
			                   (select sum(tcp.STD_PREM_AF) from dev_pas.t_contract_product tcp
                         where tcp.policy_code = tcm.policy_code) FEE_AMOUNT1,
			                        (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
			                        (SELECT SUM(TP1.FEE_AMOUNT)
			                  FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
			                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
			                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
			                   tpc.CHANGE_FLAG,
			                   tpc.POLICY_CHG_ID,
			                   (SELECT SUM(TP1.FEE_AMOUNT)
		                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
		                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                         AND tp1.BUSINESS_CODE = tpc.business_code) as transaction_amount
		                       
				  from ]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[dev_pas.t_policy_change tpc]]> 
				</if>
				<![CDATA[
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id]]> 
				  <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
				  <![CDATA[left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
		            AND TS.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    			where 1 = 1  and tca.is_nb_agent = '1']]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
        <if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[  and tpc.finish_time >= #{batch_start_date}  ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[and tpc.finish_time < #{batch_end_date} +1 ]]></if>
		<if test=" batch_date != null and batch_date != '' "><![CDATA[and  tpc.finish_time >= #{batch_date} - 1 and tpc.finish_time < #{batch_date} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<![CDATA[  and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
						  tcm.liability_state,
                tcm.apply_date,
                tcm.issue_date,
                tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
                  tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                  tcsa.apply_name,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.service_bank_branch,
                tcm.end_cause,
                tcm.lapse_cause,
                tcsa.customer_id,
                tcsa.service_type,
                tcsa.bank_channel,
                tpac.next_account,
                tpc.business_code,
                tpac.pay_next,
                tpc.CHANGE_FLAG,
                tpc.POLICY_CHG_ID,
                ts.accept_code   ]]>
	</select>
	
	
	<!-- 保单在前一日已承保但无回执日期--> 
	<select id="PA_findAcknowledgeDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
                tcm.apply_code,
                tcm.policy_id, ]]>
    <if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
    <![CDATA[     tcm.validate_date,
                tcm.liability_state,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.apply_date,
                tcm.issue_date,
                tcm.end_cause,
            tcm.SUBINPUT_TYPE,
            tcm.SUBMIT_CHANNEL,
            tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                tcm.service_bank_branch,
                tpac.next_account,
                tpac.pay_next,
                (select t.bank_code from dev_pas.T_PREM t 
                    where t.policy_code = tcm.policy_code 
                    and t.busi_prod_code = tcbp.busi_prod_code 
                    and t.arap_flag = '1' and rownum=1) as bank_code,
                sum(tcp.total_prem_af) total_prem_af,
                    sum(tcp.initial_amount) initial_amount,
                    sum(tcp.bonus_sa) bonus_sa,
                    (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
                        FROM APP___PAS__DBUSER.T_PREM TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
                              (SELECT SUM(TP1.FEE_AMOUNT)
                        FROM APP___PAS__DBUSER.T_PREM TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT
           from dev_pas.t_contract_master tcm
           join dev_pas.t_contract_product tcp
             on tcp.policy_code = tcm.policy_code
           join dev_pas.t_contract_busi_prod tcbp
             on tcbp.busi_item_id = tcp.busi_item_id]]> 
          <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
          <![CDATA[
           join dev_pas.t_policy_acknowledgement tpa
             on tpa.policy_id = tcm.policy_id
             join dev_pas.t_payer_account tpac
                 on tpac.policy_id = tcm.policy_id
          join dev_pas.t_contract_agent tca
             on tcm.policy_code = tca.policy_code
          where 1 = 1  and tca.is_nb_agent = '1' and tpa.acknowledge_date is null ]]>
    <if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
    <if test=" service_bank != null and service_bank != '' and service_bank == '02' "><![CDATA[ and trim(tcm.service_bank) = #{service_bank} and tcm.submit_channel <> '1' ]]></if>
     <if test=" service_bank != null and service_bank != '' and service_bank != '02'"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
    <if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.ISSUE_DATE = #{batch_date} ]]></if>
    <if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.ISSUE_DATE >= #{batch_start_date}  ]]></if>
    <if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.ISSUE_DATE < #{batch_end_date} + 1]></if>
    <if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
    <if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
    <if test=" policy_code_list  != null and policy_code_list.size()>0">
      <![CDATA[ and tcm.policy_code in (]]>
      <foreach collection="policy_code_list" item="policy_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{policy_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" submit_channel_list  != null and service_bank != '02' and submit_channel_list.size()>0 " >
      <![CDATA[ and tcm.submit_channel in (]]>
      <foreach collection="submit_channel_list" item="submit_channel"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{submit_channel} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" busi_prod_code_list  != null and busi_prod_code_list.size()>0 ">
      <![CDATA[ and tcbp.busi_prod_code in (]]>
      <foreach collection="busi_prod_code_list" item="busi_prod_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{busi_prod_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <![CDATA[ group by tcm.policy_code,
                   tcm.apply_code,
                   tcm.policy_id,
                   tcm.validate_date,
                   tcm.liability_state,
                   tcm.lapse_date,
                   tcm.expiry_date,
                   tcm.apply_date,
                 tcm.issue_date,
                 tcm.end_cause,
              tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
              tca.CHANNEL_TYPE,
                 tpa.acknowledge_date,
                 tpac.next_account,
                 tpac.pay_next,
                 tcbp.busi_prod_code,
                 tcm.service_bank_branch]]>
	</select>
	
	<!-- 保单在前一日已更新回执日期 --> 
	<select id="PA_findChangeAcknowledgeDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
                tcm.apply_code,
                tcm.policy_id, ]]>
    <if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
    <![CDATA[     tcm.validate_date,
                tcm.liability_state,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.apply_date,
                tcm.issue_date,
                tcm.end_cause,
            tcm.SUBINPUT_TYPE,
            tcm.SUBMIT_CHANNEL,
            tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                tcm.service_bank_branch,
                tpac.next_account,
                tpac.pay_next,
                (select t.bank_code from dev_pas.T_PREM t 
                    where t.policy_code = tcm.policy_code 
                    and t.busi_prod_code = tcbp.busi_prod_code 
                    and t.arap_flag = '1' and rownum=1) as bank_code,
                sum(tcp.total_prem_af) total_prem_af,
                    sum(tcp.initial_amount) initial_amount,
                    sum(tcp.bonus_sa) bonus_sa,
                    (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
                        FROM APP___PAS__DBUSER.T_PREM TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
                              (SELECT SUM(TP1.FEE_AMOUNT)
                        FROM APP___PAS__DBUSER.T_PREM TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT
           from dev_pas.t_contract_master tcm
           join dev_pas.t_contract_product tcp
             on tcp.policy_code = tcm.policy_code
           join dev_pas.t_contract_busi_prod tcbp
             on tcbp.busi_item_id = tcp.busi_item_id]]> 
          <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
          <![CDATA[
           join dev_pas.t_policy_acknowledgement tpa
             on tpa.policy_id = tcm.policy_id
             join dev_pas.t_payer_account tpac
                 on tpac.policy_id = tcm.policy_id
          join dev_pas.t_contract_agent tca
             on tcm.policy_code = tca.policy_code
          where 1 = 1  and tca.is_nb_agent = '1' and tpa.acknowledge_date is not null ]]>
    <if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
    <if test=" service_bank != null and service_bank != '' and service_bank == '02' "><![CDATA[ and trim(tcm.service_bank) = #{service_bank} and tcm.submit_channel <>'1' ]]></if>
    <if test=" service_bank != null and service_bank != '' and service_bank != '02' "><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
    <if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpa.acknowledge_date = #{batch_date} - 1]]></if>
    <if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tpa.acknowledge_date >= #{batch_start_date} ]]></if>
    <if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tpa.acknowledge_date < #{batch_end_date} + 1]]></if>
    <if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
    <if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
    <if test=" policy_code_list  != null and policy_code_list.size()>0">
      <![CDATA[ and tcm.policy_code in (]]>
      <foreach collection="policy_code_list" item="policy_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{policy_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" submit_channel_list  != null and service_bank != '02' and submit_channel_list.size()>0 ">
      <![CDATA[ and tcm.submit_channel in (]]>
      <foreach collection="submit_channel_list" item="submit_channel"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{submit_channel} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" busi_prod_code_list  != null and busi_prod_code_list.size()>0">
      <![CDATA[ and tcbp.busi_prod_code in (]]>
      <foreach collection="busi_prod_code_list" item="busi_prod_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{busi_prod_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <![CDATA[ group by tcm.policy_code,
                   tcm.apply_code,
                   tcm.policy_id,
                   tcm.validate_date,
                   tcm.liability_state,
                   tcm.lapse_date,
                   tcm.expiry_date,
                   tcm.apply_date,
                 tcm.issue_date,
                 tcm.end_cause,
              tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
              tca.CHANNEL_TYPE,
                 tpa.acknowledge_date,
                 tpac.next_account,
                 tpac.pay_next,
                 tcbp.busi_prod_code,
                 tcm.service_bank_branch]]>
	</select>
	
	<!-- 理赔 --> 
	<select id="PA_findClaimPoliycInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
                tcm.apply_code,
                tcm.policy_id, ]]>
    <if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
    <![CDATA[     tcm.validate_date,
                tcm.liability_state,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.apply_date,
                tcm.issue_date,
                tcm.end_cause,
            tcm.SUBINPUT_TYPE,
            tcm.SUBMIT_CHANNEL,
            tca.CHANNEL_TYPE,
                tpa.acknowledge_date,
                tcm.service_bank_branch,
                tpac.next_account,
                tpac.pay_next,
                tpc.validate_time,
                tpc.BUSINESS_CODE,
                (select t.bank_code from dev_pas.T_PREM t 
                    where t.policy_code = tcm.policy_code 
                    and t.busi_prod_code = tcbp.busi_prod_code 
                    and t.arap_flag = '1' and rownum=1) as bank_code,
                sum(tcp.total_prem_af) total_prem_af,
                    sum(tcp.initial_amount) initial_amount,
                    sum(tcp.bonus_sa) bonus_sa,
                    (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
                        FROM APP___PAS__DBUSER.T_PREM TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
                              (SELECT SUM(TP1.FEE_AMOUNT)
                        FROM APP___PAS__DBUSER.T_PREM TP1
                       WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                         AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                         AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT
           from dev_pas.t_contract_master tcm
           join dev_pas.t_contract_product tcp
             on tcp.policy_code = tcm.policy_code
           join dev_pas.t_contract_busi_prod tcbp
             on tcbp.busi_item_id = tcp.busi_item_id]]> 
          <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
          
          <if test="policy_code_list!=null and policy_code_list.size()>0">
	       	<![CDATA[
	       		join dev_pas.V_POLICY_CHANGE_ALL tpc
	       			on tcm.policy_id = tpc.policy_id]]>
	       	</if>
       	 <if test="policy_code_list==null or policy_code_list.size()==0">
	       	<![CDATA[
	       		join dev_pas.t_policy_change tpc
					on tcm.policy_id = tpc.policy_id]]> 
	       	</if>
          <![CDATA[
           join dev_pas.t_policy_acknowledgement tpa
             on tpa.policy_id = tcm.policy_id
             join dev_pas.t_payer_account tpac
                 on tpac.policy_id = tcm.policy_id
          join dev_pas.t_contract_agent tca
             on tcm.policy_code = tca.policy_code         
          where 1 = 1  and tca.is_nb_agent = '1' and tpc.SERVICE_CODE = 'CLMEND']]>
    <if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
    <if test=" service_bank != null and service_bank != '' and service_bank == '02' "><![CDATA[ and trim(tcm.service_bank) = #{service_bank} and tcm.submit_channel <>'1' ]]></if>
    <if test=" service_bank != null and service_bank != '' and service_bank != '02' "><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
    <if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpc.validate_time = #{batch_date} - 1]]></if>
    <if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tpc.validate_time >= #{batch_start_date} ]]></if>
    <if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tpc.validate_time < #{batch_end_date} + 1]]></if>
    <if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
    <if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
    <if test=" policy_code_list  != null and policy_code_list.size()>0">
      <![CDATA[ and tcm.policy_code in (]]>
      <foreach collection="policy_code_list" item="policy_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{policy_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" submit_channel_list  != null and service_bank != '02' and submit_channel_list.size()>0 ">
      <![CDATA[ and tcm.submit_channel in (]]>
      <foreach collection="submit_channel_list" item="submit_channel"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{submit_channel} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" busi_prod_code_list  != null and busi_prod_code_list.size()>0">
      <![CDATA[ and tcbp.busi_prod_code in (]]>
      <foreach collection="busi_prod_code_list" item="busi_prod_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{busi_prod_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <![CDATA[ group by tcm.policy_code,
                   tcm.apply_code,
                   tcm.policy_id,
                   tcm.validate_date,
                   tcm.liability_state,
                   tcm.lapse_date,
                   tcm.expiry_date,
                   tcm.apply_date,
                 tcm.issue_date,
                 tcm.end_cause,
              tcm.SUBINPUT_TYPE,
              tcm.SUBMIT_CHANNEL,
              tca.CHANNEL_TYPE,
                 tpa.acknowledge_date,
                 tpac.next_account,
                 tpac.pay_next,
                 tcbp.busi_prod_code,
                 tcm.service_bank_branch,
                 tpc.validate_time,
                 tpc.BUSINESS_CODE]]>
	</select>
	
	<!-- 保单分红 --> 
	<select id="PA_findPolicyCashBonus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		 select tcm.policy_code,
                tcm.apply_code,
                tcm.policy_id, ]]>
    <if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
    <![CDATA[   tcm.validate_date,
                tcm.liability_state,
                tcm.lapse_date,
                tcm.expiry_date,
                tcm.apply_date,
                tcm.issue_date,
                tcm.end_cause,
	            tcm.SUBINPUT_TYPE,
	            tcm.SUBMIT_CHANNEL,
	            tca.CHANNEL_TYPE,
	            tba.reissue_interest,
	            tba.cash_bonus,
	            tba.allocate_date,
                tpa.acknowledge_date,
                tcm.service_bank_branch,
                tpac.next_account,
                tpac.pay_next,
                (select t.bank_code from dev_pas.T_PREM t 
                    where t.policy_code = tcm.policy_code 
                    and t.busi_prod_code = tcbp.busi_prod_code 
                    and t.arap_flag = '1' and rownum=1) as bank_code,
                sum(tcp.total_prem_af) total_prem_af,
                sum(tcp.initial_amount) initial_amount,
                sum(tcp.bonus_sa) bonus_sa,
                (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
                    FROM APP___PAS__DBUSER.T_PREM TP1
                    WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                    AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                    AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
                (SELECT SUM(TP1.FEE_AMOUNT)
                    FROM APP___PAS__DBUSER.T_PREM TP1
                    WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
                    AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN') OR (TP1.FEE_SCENE_CODE='CS' AND TP1.SERVICE_CODE IN ('RE','SR')))
                    AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT
           from dev_pas.t_contract_master tcm
           join dev_pas.t_contract_product tcp
             on tcp.policy_code = tcm.policy_code
           join dev_pas.t_contract_busi_prod tcbp
             on tcbp.busi_item_id = tcp.busi_item_id]]> 
          <if test=" is_new_add_risk != null and is_new_add_risk != ''"><![CDATA[ and tcbp.APPLY_DATE = tcm.APPLY_DATE ]]></if>  
          <![CDATA[
           join dev_pas.t_policy_acknowledgement tpa
             on tpa.policy_id = tcm.policy_id
           join dev_pas.t_payer_account tpac
             on tpac.policy_id = tcm.policy_id
           join dev_pas.t_contract_agent tca
             on tcm.policy_code = tca.policy_code
           join dev_pas.t_bonus_allocate tba
          	 on tcm.policy_code = tba.policy_code 
          	and tcbp.BUSI_ITEM_ID = tba.busi_item_id
          where 1 = 1  and tca.is_nb_agent = '1' and tba.allocate_type = '02' and tba.bonus_allot = '4']]>
    <if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
    <if test=" service_bank != null and service_bank != '' and service_bank == '02' "><![CDATA[ and trim(tcm.service_bank) = #{service_bank} and tcm.submit_channel <>'1' ]]></if>
    <if test=" service_bank != null and service_bank != '' and service_bank != '02' "><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
    <if test=" batch_date != null and batch_date != ''"><![CDATA[ and tba.allocate_date = #{batch_date} - 1]]></if>
    <if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tba.allocate_date >= #{batch_start_date} ]]></if>
    <if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tba.allocate_date < #{batch_end_date} + 1]]></if>
    <if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
    <if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
    <if test=" policy_code_list  != null and policy_code_list.size()>0">
      <![CDATA[ and tcm.policy_code in (]]>
      <foreach collection="policy_code_list" item="policy_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{policy_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" submit_channel_list  != null and service_bank != '02' and submit_channel_list.size()>0 ">
      <![CDATA[ and tcm.submit_channel in (]]>
      <foreach collection="submit_channel_list" item="submit_channel"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{submit_channel} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <if test=" busi_prod_code_list  != null and busi_prod_code_list.size()>0">
      <![CDATA[ and tcbp.busi_prod_code in (]]>
      <foreach collection="busi_prod_code_list" item="busi_prod_code"
        index="index" open="" close="" separator=",">
        <![CDATA[ #{busi_prod_code} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
    <![CDATA[ group by tcm.policy_code,
                 tcm.apply_code,
                 tcm.policy_id,
                 tcm.validate_date,
                 tcm.liability_state,
                 tcm.lapse_date,
                 tcm.expiry_date,
                 tcm.apply_date,
                 tcm.issue_date,
                 tcm.end_cause,
	             tcm.SUBINPUT_TYPE,
	             tcm.SUBMIT_CHANNEL,
	             tca.CHANNEL_TYPE,
                 tpa.acknowledge_date,
                 tpac.next_account,
                 tpac.pay_next,
                 tcbp.busi_prod_code,
                 tcm.service_bank_branch,
                 tba.reissue_interest,
		         tba.cash_bonus,
		         tba.allocate_date]]>
	</select>
</mapper>
