<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IMeetPovStandardFlagTrackDao">

<!-- 添加操作 -->
	<insert id="addMeetPovStandardFlagTrack"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ME_PO_ST_FLAG_TR__LIST_ID .NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_MEET_POV_STANDARD_FLAG_TRACK(
			       LIST_ID , BUSI_ITEM_ID , MEET_POV_STANDARD_FLAG ,
			       START_DATE , END_DATE, 
			       INSERT_BY , INSERT_TIME , INSERT_TIMESTAMP,
			       UPDATE_BY , UPDATE_TIME , UPDATE_TIMESTAMP) 
			VALUES (
				  #{list_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{meet_pov_standard_flag, jdbcType=NUMERIC},
				  #{start_date, jdbcType=DATE} , #{end_date, jdbcType=DATE},
				  #{insert_by, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP,
				  #{update_by, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP
				 ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteMeetPovStandardFlagTrack" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_MEET_POV_STANDARD_FLAG_TRACK  WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateMeetPovStandardFlagTrack" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_MEET_POV_STANDARD_FLAG_TRACK ]]>
		<set>
		<trim suffixOverrides=",">
	        LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    MEET_POV_STANDARD_FLAG = #{meet_pov_standard_flag, jdbcType=NUMERIC} ,
		    START_DATE =  #{start_date, jdbcType=DATE},
		    END_DATE =  #{end_date, jdbcType=DATE},
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>



	
	


	
	
	
</mapper>
