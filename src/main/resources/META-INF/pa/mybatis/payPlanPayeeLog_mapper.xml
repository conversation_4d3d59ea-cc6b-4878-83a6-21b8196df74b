<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayPlanPayeeLogDao">
<!--
	<sql id="PA_payPlanPayeeLogWhereCondition">
		<if test=" payee_address_id  != null "><![CDATA[ AND A.PAYEE_ADDRESS_ID = #{payee_address_id} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" payee_account_id  != null "><![CDATA[ AND A.PAYEE_ACCOUNT_ID = #{payee_account_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" payee_rate  != null "><![CDATA[ AND A.PAYEE_RATE = #{payee_rate} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayPlanPayeeLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPayPlanPayeeLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_PAY_PLAN_PAYEE_LOG__LOG_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG(
				PAYEE_ADDRESS_ID, CUSTOMER_NAME, PLAN_ID, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, ITEM_ID, 
				INSERT_TIMESTAMP, LOG_ID, PAY_MODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, LOG_TYPE, 
				POLICY_CHG_ID, PAYEE_ACCOUNT_ID, BUSI_ITEM_ID, INSERT_BY, POLICY_ID, PAYEE_RATE ) 
			VALUES (
				#{payee_address_id, jdbcType=NUMERIC}, #{customer_name, jdbcType=VARCHAR} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{pay_mode, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} 
				, #{policy_chg_id, jdbcType=NUMERIC} , #{payee_account_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{payee_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePayPlanPayeeLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePayPlanPayeeLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    PAYEE_ADDRESS_ID = #{payee_address_id, jdbcType=NUMERIC} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    PAYEE_ACCOUNT_ID = #{payee_account_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PAYEE_RATE = #{payee_rate, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPayPlanPayeeLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.LOG_ID, A.PAY_MODE, A.LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanPayeeLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayPlanPayeeLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.LOG_ID, A.PAY_MODE, A.LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayPlanPayeeLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.LOG_ID, A.PAY_MODE, A.LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayPlanPayeeLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayPlanPayeeLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PAYEE_ADDRESS_ID, B.CUSTOMER_NAME, B.PLAN_ID, B.CUSTOMER_ID, B.ITEM_ID, 
			B.LOG_ID, B.PAY_MODE, B.LIST_ID, B.LOG_TYPE, 
			B.POLICY_CHG_ID, B.PAYEE_ACCOUNT_ID, B.BUSI_ITEM_ID, B.POLICY_ID, B.PAYEE_RATE FROM (
					SELECT ROWNUM RN, A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.LOG_ID, A.PAY_MODE, A.LIST_ID, A.LOG_TYPE, 
			A.POLICY_CHG_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
