<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="customerSurveyDetail">

	<sql id="PA_customerSurveyDetailWhereCondition">
		<if test=" question_option_code != null and question_option_code != ''  "><![CDATA[ AND A.QUESTION_OPTION_CODE = #{question_option_code} ]]></if>
		<if test=" detail_id  != null "><![CDATA[ AND A.DETAIL_ID = #{detail_id} ]]></if>
		<if test=" question_answer_txt4 != null and question_answer_txt4 != ''  "><![CDATA[ AND A.QUESTION_ANSWER_TXT4 = #{question_answer_txt4} ]]></if>
		<if test=" question_id  != null "><![CDATA[ AND A.QUESTION_ID = #{question_id} ]]></if>
		<if test=" question_answer_txt3 != null and question_answer_txt3 != ''  "><![CDATA[ AND A.QUESTION_ANSWER_TXT3 = #{question_answer_txt3} ]]></if>
		<if test=" question_code != null and question_code != ''  "><![CDATA[ AND A.QUESTION_CODE = #{question_code} ]]></if>
		<if test=" question_answer_txt2 != null and question_answer_txt2 != ''  "><![CDATA[ AND A.QUESTION_ANSWER_TXT2 = #{question_answer_txt2} ]]></if>
		<if test=" question_answer_txt1 != null and question_answer_txt1 != ''  "><![CDATA[ AND A.QUESTION_ANSWER_TXT1 = #{question_answer_txt1} ]]></if>
		<if test=" question_option_id  != null "><![CDATA[ AND A.QUESTION_OPTION_ID = #{question_option_id} ]]></if>
		<if test=" survey_id  != null "><![CDATA[ AND A.SURVEY_ID = #{survey_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryCustomerSurveyDetailByDetailIdCondition">
		<if test=" detail_id  != null "><![CDATA[ AND A.DETAIL_ID = #{detail_id} ]]></if>
	</sql>	
	<sql id="PA_queryCustomerSurveyDetailBySurveyIdCondition">
		<if test=" survey_id  != null "><![CDATA[ AND A.SURVEY_ID = #{survey_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addCustomerSurveyDetail"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL(
				QUESTION_OPTION_CODE, INSERT_TIME, DETAIL_ID, UPDATE_TIME, QUESTION_ANSWER_TXT4, QUESTION_ID, QUESTION_ANSWER_TXT3, 
				QUESTION_CODE, QUESTION_ANSWER_TXT2, QUESTION_ANSWER_TXT1, INSERT_TIMESTAMP, UPDATE_BY, QUESTION_OPTION_ID, UPDATE_TIMESTAMP, 
				INSERT_BY, SURVEY_ID ) 
			VALUES (
				#{question_option_code, jdbcType=VARCHAR}, SYSDATE , #{detail_id, jdbcType=NUMERIC} , SYSDATE , #{question_answer_txt4, jdbcType=VARCHAR} , #{question_id, jdbcType=NUMERIC} , #{question_answer_txt3, jdbcType=VARCHAR} 
				, #{question_code, jdbcType=VARCHAR} , #{question_answer_txt2, jdbcType=VARCHAR} , #{question_answer_txt1, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{question_option_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{survey_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteCustomerSurveyDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL WHERE DETAIL_ID = #{detail_id}  ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateCustomerSurveyDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL ]]>
		<set>
		<trim suffixOverrides=",">
			QUESTION_OPTION_CODE = #{question_option_code, jdbcType=VARCHAR} ,
		    DETAIL_ID = #{detail_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			QUESTION_ANSWER_TXT4 = #{question_answer_txt4, jdbcType=VARCHAR} ,
		    QUESTION_ID = #{question_id, jdbcType=NUMERIC} ,
			QUESTION_ANSWER_TXT3 = #{question_answer_txt3, jdbcType=VARCHAR} ,
			QUESTION_CODE = #{question_code, jdbcType=VARCHAR} ,
			QUESTION_ANSWER_TXT2 = #{question_answer_txt2, jdbcType=VARCHAR} ,
			QUESTION_ANSWER_TXT1 = #{question_answer_txt1, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    QUESTION_OPTION_ID = #{question_option_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SURVEY_ID = #{survey_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE DETAIL_ID = #{detail_id}  ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findCustomerSurveyDetailByDetailId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_OPTION_CODE, A.DETAIL_ID, A.QUESTION_ANSWER_TXT4, A.QUESTION_ID, A.QUESTION_ANSWER_TXT3, 
			A.QUESTION_CODE, A.QUESTION_ANSWER_TXT2, A.QUESTION_ANSWER_TXT1, A.QUESTION_OPTION_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL A WHERE 1 = 1  ]]>
		<include refid="PA_queryCustomerSurveyDetailByDetailIdCondition" />
	</select>
	
	<select id="PA_findCustomerSurveyDetailBySurveyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_OPTION_CODE, A.DETAIL_ID, A.QUESTION_ANSWER_TXT4, A.QUESTION_ID, A.QUESTION_ANSWER_TXT3, 
			A.QUESTION_CODE, A.QUESTION_ANSWER_TXT2, A.QUESTION_ANSWER_TXT1, A.QUESTION_OPTION_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL A WHERE 1 = 1  ]]>
		<include refid="PA_queryCustomerSurveyDetailBySurveyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapCustomerSurveyDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_OPTION_CODE, A.DETAIL_ID, A.QUESTION_ANSWER_TXT4, A.QUESTION_ID, A.QUESTION_ANSWER_TXT3, 
			A.QUESTION_CODE, A.QUESTION_ANSWER_TXT2, A.QUESTION_ANSWER_TXT1, A.QUESTION_OPTION_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllCustomerSurveyDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_OPTION_CODE, A.DETAIL_ID, A.QUESTION_ANSWER_TXT4, A.QUESTION_ID, A.QUESTION_ANSWER_TXT3, 
			A.QUESTION_CODE, A.QUESTION_ANSWER_TXT2, A.QUESTION_ANSWER_TXT1, A.QUESTION_OPTION_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_customerSurveyDetailWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findCustomerSurveyDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryCustomerSurveyDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.QUESTION_OPTION_CODE, B.DETAIL_ID, B.QUESTION_ANSWER_TXT4, B.QUESTION_ID, B.QUESTION_ANSWER_TXT3, 
			B.QUESTION_CODE, B.QUESTION_ANSWER_TXT2, B.QUESTION_ANSWER_TXT1, B.QUESTION_OPTION_ID, 
			B.SURVEY_ID FROM (
					SELECT ROWNUM RN, A.QUESTION_OPTION_CODE, A.DETAIL_ID, A.QUESTION_ANSWER_TXT4, A.QUESTION_ID, A.QUESTION_ANSWER_TXT3, 
			A.QUESTION_CODE, A.QUESTION_ANSWER_TXT2, A.QUESTION_ANSWER_TXT1, A.QUESTION_OPTION_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY_DETAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
