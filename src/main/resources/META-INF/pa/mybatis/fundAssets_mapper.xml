<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFundAssetsDao">

	<sql id="fundAssetsWhereCondition">
		<if test=" confirm_remark != null and confirm_remark != ''  "><![CDATA[ AND A.CONFIRM_REMARK = #{confirm_remark} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" account_value_net_b  != null "><![CDATA[ AND A.ACCOUNT_VALUE_NET_B = #{account_value_net_b} ]]></if>
		<if test=" confirm_time  != null  and  confirm_time  != ''  "><![CDATA[ AND A.CONFIRM_TIME = #{confirm_time} ]]></if>
		<if test=" tax_amount  != null "><![CDATA[ AND A.TAX_AMOUNT = #{tax_amount} ]]></if>
		<if test=" upload_date  != null  and  upload_date  != ''  "><![CDATA[ AND A.UPLOAD_DATE = #{upload_date} ]]></if>
		<if test=" assets_deposit_id  != null "><![CDATA[ AND A.ASSETS_DEPOSIT_ID = #{assets_deposit_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" profit_loss_amout  != null "><![CDATA[ AND A.PROFIT_LOSS_AMOUT = #{profit_loss_amout} ]]></if>
		<if test=" invest_account_id  != null "><![CDATA[ AND A.INVEST_ACCOUNT_ID = #{invest_account_id} ]]></if>
		<if test=" asset_m_fee  != null "><![CDATA[ AND A.ASSET_M_FEE = #{asset_m_fee} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" account_value  != null "><![CDATA[ AND A.ACCOUNT_VALUE = #{account_value} ]]></if>
		<if test=" confirmer_id  != null "><![CDATA[ AND A.CONFIRMER_ID = #{confirmer_id} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" tax_rate  != null "><![CDATA[ AND A.TAX_RATE = #{tax_rate} ]]></if>
		<if test=" evaluation_date  != null  and  evaluation_date  != ''  "><![CDATA[ AND A.EVALUATION_DATE = #{evaluation_date} ]]></if>
		<if test=" confirm_result  != null "><![CDATA[ AND A.CONFIRM_RESULT = #{confirm_result} ]]></if>
		<if test=" account_value_net != null and account_value_net != ''  "><![CDATA[ AND A.ACCOUNT_VALUE_NET = #{account_value_net} ]]></if>
	</sql>
	<!--start by liucmit -->
	<update id="PA_updateFundAssets" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_ASSETS A ]]>
		<set>
			<trim suffixOverrides=",">
				CONFIRM_REMARK = #{confirm_remark,
				jdbcType=VARCHAR} ,
				CONFIRM_RESULT = #{confirm_result,
				jdbcType=NUMERIC}
			</trim>
		</set>
		<![CDATA[ WHERE INVEST_ACCOUNT_CODE = #{invest_account_code} ]]>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</update>
	<select id="getFundAssetsPO" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT  A.ACCOUNT_VALUE_NET, A.ASSET_M_FEE 
			 FROM APP___PAS__DBUSER.T_FUND_ASSETS A WHERE A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	<!--end by liucmit -->

	<!-- 按索引生成的查询条件 -->
	<sql id="queryFundAssetsByListIdCondition">
		<!-- <if test=" list_id != null "><![CDATA[ AND A.LIST_ID = #{list_id} 
			]]></if> -->
		<if test=" invest_account_code  != null "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="addFundAssets" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
				SELECT APP___PAS__DBUSER.S_FUND_ASSETS__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_ASSETS(
				CONFIRM_REMARK, BATCH_NO, ACCOUNT_VALUE_NET_B, CONFIRM_TIME, TAX_AMOUNT, INSERT_TIMESTAMP, UPLOAD_DATE, 
				UPDATE_BY, ASSETS_DEPOSIT_ID, LIST_ID, PROFIT_LOSS_AMOUT, INVEST_ACCOUNT_ID, ASSET_M_FEE, INVEST_ACCOUNT_CODE, 
				INSERT_TIME, UPDATE_TIME, ACCOUNT_VALUE, CONFIRMER_ID, BANK_CODE, TAX_RATE, EVALUATION_DATE, 
				CONFIRM_RESULT, UPDATE_TIMESTAMP, ACCOUNT_VALUE_NET, INSERT_BY ) 
			VALUES (
				#{confirm_remark, jdbcType=VARCHAR}, #{batch_no, jdbcType=VARCHAR} , #{account_value_net_b, jdbcType=NUMERIC} , #{confirm_time, jdbcType=DATE} , #{tax_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{upload_date, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{assets_deposit_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{profit_loss_amout, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} , #{asset_m_fee, jdbcType=NUMERIC} , #{invest_account_code, jdbcType=VARCHAR} 
				, SYSDATE , SYSDATE , #{account_value, jdbcType=NUMERIC} , #{confirmer_id, jdbcType=NUMERIC} , #{bank_code, jdbcType=VARCHAR} , #{tax_rate, jdbcType=NUMERIC} , #{evaluation_date, jdbcType=DATE} 
				, #{confirm_result, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{account_value_net, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="deleteFundAssets" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_ASSETS WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 账户资产录入-修改资产净值 -->
	<update id="editUpdateFundAssetsByConfirmResultt" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_ASSETS A ]]>
		<set>
			<trim suffixOverrides=",">
				<!-- CONFIRM_REMARK = #{confirm_remark, jdbcType=VARCHAR} , BATCH_NO 
					= #{batch_no, jdbcType=VARCHAR} , ACCOUNT_VALUE_NET_B = #{account_value_net_b, 
					jdbcType=NUMERIC} , CONFIRM_TIME = #{confirm_time, jdbcType=DATE} , TAX_AMOUNT 
					= #{tax_amount, jdbcType=NUMERIC} , UPLOAD_DATE = #{upload_date, jdbcType=DATE} 
					, UPDATE_BY = #{update_by, jdbcType=NUMERIC} , ASSETS_DEPOSIT_ID = #{assets_deposit_id, 
					jdbcType=NUMERIC} , PROFIT_LOSS_AMOUT = #{profit_loss_amout, jdbcType=NUMERIC} 
					, INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} , ASSET_M_FEE 
					= #{asset_m_fee, jdbcType=NUMERIC} , INVEST_ACCOUNT_CODE = #{invest_account_code, 
					jdbcType=VARCHAR} , UPDATE_TIME = SYSDATE , ACCOUNT_VALUE = #{account_value, 
					jdbcType=NUMERIC} , CONFIRMER_ID = #{confirmer_id, jdbcType=NUMERIC} , BANK_CODE 
					= #{bank_code, jdbcType=VARCHAR} , TAX_RATE = #{tax_rate, jdbcType=NUMERIC} 
					, EVALUATION_DATE = #{evaluation_date, jdbcType=DATE} , CONFIRM_RESULT = 
					#{confirm_result, jdbcType=NUMERIC} , UPDATE_TIMESTAMP = CURRENT_TIMESTAMP 
					, -->
				ACCOUNT_VALUE_NET = #{account_value_net, jdbcType=NUMERIC} ,
				ASSET_M_FEE = #{asset_m_fee, jdbcType=NUMERIC} ,
				CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				UPDATE_TIME = SYSDATE ,  
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<!-- <![CDATA[ WHERE LIST_ID = #{list_id} ]]> -->
 		<![CDATA[ WHERE 1=1 ]]>
 		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
 		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
	</update>

	<!-- 账户资产录入-根据投资账户代码查询计费信息 -->
	<select id="editFindFundAssetsByInvestAccountCodee" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   SELECT ROWNUM RN ,B.* FROM (
					SELECT A.CONFIRM_REMARK,
					       A.BATCH_NO,
					       A.ACCOUNT_VALUE_NET_B,
					       A.CONFIRM_TIME,
					       A.TAX_AMOUNT,
					       A.UPLOAD_DATE,
					       A.ASSETS_DEPOSIT_ID,
					       A.LIST_ID,
					       A.PROFIT_LOSS_AMOUT,
					       A.INVEST_ACCOUNT_ID,
					       A.ASSET_M_FEE,
					       A.INVEST_ACCOUNT_CODE,
					       A.ACCOUNT_VALUE,
					       A.CONFIRMER_ID,
					       A.BANK_CODE,
					       A.TAX_RATE,
					       A.EVALUATION_DATE,
					       A.CONFIRM_RESULT,
					       A.ACCOUNT_VALUE_NET
					  FROM APP___PAS__DBUSER.T_FUND_ASSETS A
					 WHERE 1 = 1
					]]>
		<include refid="fundAssetsWhereCondition" />
		<![CDATA[  ORDER BY A.insert_time desc)b WHERE rownum = 1]]>
	</select>


	<!-- 按map查询操作 -->
	<select id="findAllMapFundAssets" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, 
			A.ACCOUNT_VALUE, A.CONFIRMER_ID, A.BANK_CODE, A.TAX_RATE, A.EVALUATION_DATE, 
			A.CONFIRM_RESULT, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllFundAssets" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, 
			A.ACCOUNT_VALUE, A.CONFIRMER_ID, A.BANK_CODE, A.TAX_RATE, A.EVALUATION_DATE, 
			A.CONFIRM_RESULT, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS A WHERE ROWNUM <=  1000  ]]>
		<include refid="fundAssetsWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="findFundAssetsTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_ASSETS A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="queryFundAssetsForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CONFIRM_REMARK, B.BATCH_NO, B.ACCOUNT_VALUE_NET_B, B.CONFIRM_TIME, B.TAX_AMOUNT, B.UPLOAD_DATE, 
			B.ASSETS_DEPOSIT_ID, B.LIST_ID, B.PROFIT_LOSS_AMOUT, B.INVEST_ACCOUNT_ID, B.ASSET_M_FEE, B.INVEST_ACCOUNT_CODE, 
			B.ACCOUNT_VALUE, B.CONFIRMER_ID, B.BANK_CODE, B.TAX_RATE, B.EVALUATION_DATE, 
			B.CONFIRM_RESULT, B.ACCOUNT_VALUE_NET FROM (
					SELECT ROWNUM RN, A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, 
			A.ACCOUNT_VALUE, A.CONFIRMER_ID, A.BANK_CODE, A.TAX_RATE, A.EVALUATION_DATE, 
			A.CONFIRM_RESULT, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 单条数据查询 -->
	<select id="findFundAssets" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, 
			A.ACCOUNT_VALUE, A.CONFIRMER_ID, A.BANK_CODE, A.TAX_RATE, A.EVALUATION_DATE, 
			A.CONFIRM_RESULT, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS A WHERE 1 = 1  ]]>
		<include refid="fundAssetsWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	<!-- 修改操作 -->
	<update id="updateFundAssets" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_ASSETS ]]>
		<set>
		<trim suffixOverrides=",">
			CONFIRM_REMARK = #{confirm_remark, jdbcType=VARCHAR} ,
			BATCH_NO = #{batch_no, jdbcType=VARCHAR} ,
		    ACCOUNT_VALUE_NET_B = #{account_value_net_b, jdbcType=NUMERIC} ,
		    CONFIRM_TIME = #{confirm_time, jdbcType=DATE} ,
		    TAX_AMOUNT = #{tax_amount, jdbcType=NUMERIC} ,
		    UPLOAD_DATE = #{upload_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    ASSETS_DEPOSIT_ID = #{assets_deposit_id, jdbcType=NUMERIC} ,
		    PROFIT_LOSS_AMOUT = #{profit_loss_amout, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
		    ASSET_M_FEE = #{asset_m_fee, jdbcType=NUMERIC} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ACCOUNT_VALUE = #{account_value, jdbcType=NUMERIC} ,
		    CONFIRMER_ID = #{confirmer_id, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    TAX_RATE = #{tax_rate, jdbcType=NUMERIC} ,
		    EVALUATION_DATE = #{evaluation_date, jdbcType=DATE} ,
		    CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			ACCOUNT_VALUE_NET = #{account_value_net, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findFundAssetsByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONFIRM_REMARK, A.BATCH_NO, A.ACCOUNT_VALUE_NET_B, A.CONFIRM_TIME, A.TAX_AMOUNT, A.UPLOAD_DATE, 
			A.ASSETS_DEPOSIT_ID, A.LIST_ID, A.PROFIT_LOSS_AMOUT, A.INVEST_ACCOUNT_ID, A.ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, 
			A.ACCOUNT_VALUE, A.CONFIRMER_ID, A.BANK_CODE, A.TAX_RATE, A.EVALUATION_DATE, 
			A.CONFIRM_RESULT, A.ACCOUNT_VALUE_NET FROM APP___PAS__DBUSER.T_FUND_ASSETS A WHERE 1 = 1  ]]>
		<include refid="queryFundAssetsByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
<!-- 	查询最后一条投资账户资产价值信息 -->
		<select id="queryLastFundAssets" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select rownum rn ,b.* from (
				SELECT A.CONFIRM_REMARK,
				       A.BATCH_NO,
				       A.ACCOUNT_VALUE_NET_B,
				       A.CONFIRM_TIME,
				       A.TAX_AMOUNT,
				       A.UPLOAD_DATE,
				       A.ASSETS_DEPOSIT_ID,
				       A.LIST_ID,
				       A.PROFIT_LOSS_AMOUT,
				       A.INVEST_ACCOUNT_ID,
				       A.ASSET_M_FEE,
				       A.INVEST_ACCOUNT_CODE,
				       A.ACCOUNT_VALUE,
				       A.CONFIRMER_ID,
				       A.BANK_CODE,
				       A.TAX_RATE,
				       A.EVALUATION_DATE,
				       A.CONFIRM_RESULT,
				       A.ACCOUNT_VALUE_NET
				  FROM APP___PAS__DBUSER.T_FUND_ASSETS A
				 WHERE 1 = 1  ]]>
		<include refid="fundAssetsWhereCondition" />
		<![CDATA[ order by a.insert_time desc)b where rownum = 1 ]]>
	</select>
</mapper>
