<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayPlanDao">
	
	<sql id="payPlanWhereCondition">
		<if test=" survival_invest_result  != null "><![CDATA[ AND A.SURVIVAL_INVEST_RESULT = #{survival_invest_result} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>		
		<if test=" end_period != null and end_period != ''  "><![CDATA[ AND A.END_PERIOD = #{end_period} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND <PERSON>.<PERSON><PERSON>_DATE = #{end_date} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" instalment_amount  != null "><![CDATA[ AND A.INSTALMENT_AMOUNT = #{instalment_amount} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" bene_amount  != null "><![CDATA[ AND A.BENE_AMOUNT = #{bene_amount} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" instalment_proportion  != null "><![CDATA[ AND A.INSTALMENT_PROPORTION = #{instalment_proportion} ]]></if>
		<if test=" end_year  != null "><![CDATA[ AND A.END_YEAR = #{end_year} ]]></if>
		<if test=" pay_num  != null "><![CDATA[ AND A.PAY_NUM = #{pay_num} ]]></if>
		<if test=" liab_code != null and liab_code != ''  "><![CDATA[ AND A.LIAB_CODE = #{liab_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" survival_mode  != null "><![CDATA[ AND A.SURVIVAL_MODE = #{survival_mode} ]]></if>
		<if test=" plan_freq != null and plan_freq != ''  "><![CDATA[ AND A.PLAN_FREQ = #{plan_freq} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" begin_date  != null  and  begin_date  != ''  "><![CDATA[ AND A.BEGIN_DATE = #{begin_date} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
		<if test=" liab_name != null and liab_name != ''  "><![CDATA[ AND A.LIAB_NAME = #{liab_name} ]]></if>
		<if test=" survival_w_mode  != null "><![CDATA[ AND A.SURVIVAL_W_MODE = #{survival_w_mode} ]]></if>
		<if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survival_invest_flag  != null "><![CDATA[ AND A.SURVIVAL_INVEST_FLAG = #{survival_invest_flag} ]]></if>
		<if test=" pay_type != null and pay_type != ''  "><![CDATA[ AND A.PAY_TYPE = #{pay_type} ]]></if>
		<if test=" total_amount  != null "><![CDATA[ AND A.TOTAL_AMOUNT = #{total_amount} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" gurnt_pay_period  != null "><![CDATA[ AND A.GURNT_PAY_PERIOD = #{gurnt_pay_period} ]]></if>
		<if test=" one_time_flag  != null "><![CDATA[ AND A.ONE_TIME_FLAG = #{one_time_flag} ]]></if>
		<if test=" gurnt_pay_liab != null and gurnt_pay_liab != ''  "><![CDATA[ AND A.GURNT_PAY_LIAB = #{gurnt_pay_liab} ]]></if>
		<if test=" annuity_amount != null and annuity_amount != ''  "><![CDATA[ AND A.annuity_amount = #{annuity_amount} ]]></if>
		<if test=" last_extra_date != null and last_extra_date != ''  "><![CDATA[ AND A.last_extra_date = #{last_extra_date} ]]></if>
		<if test=" annuity_policy_end_flag != null and annuity_policy_end_flag != ''  "><![CDATA[ AND A.ANNUITY_POLICY_END_FLAG = #{annuity_policy_end_flag} ]]></if>
		<if test=" total_prem_af != null and total_prem_af != ''  "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" amount != null and amount != ''  "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" bonus_sa != null and bonus_sa != ''  "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" terminal_bonus != null and terminal_bonus != ''  "><![CDATA[ AND A.TERMINAL_BONUS = #{terminal_bonus} ]]></if>
		<if test=" survival_date  != null  and  survival_date  != ''  "><![CDATA[ AND A.SURVIVAL_DATE = #{survival_date} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryPayPlanByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	
	<sql id="queryPayPlanByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="queryPayPlanByPayStatusCondition">
		<if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
	</sql>	
	<sql id="queryPayPlanByPayPlanTypeCondition">
		<if test=" pay_plan_type != null and pay_plan_type != '' "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
	</sql>	
	<sql id="queryPayPlanByPayDueDateCondition">
		<if test=" pay_due_date  != null "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
	</sql>	
	<sql id="queryPayPlanByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="queryPayPlanBynotGurntPayLiabCondition">
		<if test="notGurntPayLiab  != null and notGurntPayLiab != '' "><![CDATA[ AND (A.GURNT_PAY_LIAB is null or A.GURNT_PAY_LIAB NOT IN (#{notGurntPayLiab,jdbcType=VARCHAR})) ]]></if>
	</sql>	
<!-- 添加操作 -->
	<insert id="addPayPlan"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="plan_id">
				SELECT APP___PAS__DBUSER.S_PAY_PLAN__PLAN_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_PLAN(
				SURVIVAL_DATE, SURVIVAL_INVEST_RESULT, MONEY_CODE,END_PERIOD, END_DATE, PRODUCT_CODE, INSTALMENT_AMOUNT, 
				ITEM_ID, BENE_AMOUNT, BUSI_PROD_CODE, INSERT_TIMESTAMP, INSTALMENT_PROPORTION, END_YEAR, UPDATE_BY, 
				PAY_NUM, LIAB_CODE, BUSI_ITEM_ID, POLICY_ID, SURVIVAL_MODE, PLAN_FREQ, PLAN_ID, 
				INSERT_TIME, BEGIN_DATE, LIAB_ID, PAY_STATUS, UPDATE_TIME, LIAB_NAME, SURVIVAL_W_MODE, 
				PAY_PLAN_TYPE, PAY_YEAR, PAY_PERIOD, POLICY_CODE, SURVIVAL_INVEST_FLAG, PAY_TYPE, TOTAL_AMOUNT, 
				PAY_DUE_DATE, GURNT_PAY_PERIOD, UPDATE_TIMESTAMP, INSERT_BY, ONE_TIME_FLAG, GURNT_PAY_LIAB, LAST_EXTRA_DATE,GUARANTEE_PERIOD_TYPE,ANNUITY_POLICY_END_FLAG,TOTAL_PREM_AF,AMOUNT,BONUS_SA,TERMINAL_BONUS ) 
			VALUES (
				#{survival_date, jdbcType=DATE} ,#{survival_invest_result, jdbcType=NUMERIC}, #{money_code, jdbcType=VARCHAR} , #{end_period, jdbcType=VARCHAR} , #{end_date, jdbcType=DATE} , #{product_code, jdbcType=VARCHAR} , #{instalment_amount, jdbcType=NUMERIC} 
				, #{item_id, jdbcType=NUMERIC} , #{bene_amount, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{instalment_proportion, jdbcType=NUMERIC} , #{end_year, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} 
				, #{pay_num, jdbcType=NUMERIC} , #{liab_code, jdbcType=VARCHAR} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{survival_mode, jdbcType=NUMERIC} , #{plan_freq, jdbcType=VARCHAR} , #{plan_id, jdbcType=NUMERIC} 
				, SYSDATE , #{begin_date, jdbcType=DATE} , #{liab_id, jdbcType=NUMERIC} , #{pay_status, jdbcType=NUMERIC} , SYSDATE , #{liab_name, jdbcType=VARCHAR} , #{survival_w_mode, jdbcType=NUMERIC} 
				, #{pay_plan_type, jdbcType=VARCHAR} , #{pay_year, jdbcType=NUMERIC} , #{pay_period, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{survival_invest_flag, jdbcType=NUMERIC} , #{pay_type, jdbcType=VARCHAR} , #{total_amount, jdbcType=NUMERIC} 
				, #{pay_due_date, jdbcType=DATE} , #{gurnt_pay_period, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{one_time_flag, jdbcType=NUMERIC} , #{gurnt_pay_liab, jdbcType=VARCHAR} , #{last_extra_date, jdbcType=DATE} ,#{guarantee_period_type, jdbcType=VARCHAR}
				, #{annuity_policy_end_flag, jdbcType=NUMERIC},#{total_prem_af, jdbcType=NUMERIC},#{amount, jdbcType=NUMERIC},#{bonus_sa, jdbcType=NUMERIC},#{terminal_bonus, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePayPlan" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE   PLAN_ID = #{plan_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePayPlan" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN ]]>
		<set>
		<trim suffixOverrides=",">
			SURVIVAL_DATE = #{survival_date, jdbcType=DATE} ,
		    SURVIVAL_INVEST_RESULT = #{survival_invest_result, jdbcType=NUMERIC} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
			END_PERIOD = #{end_period, jdbcType=VARCHAR} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    INSTALMENT_AMOUNT = #{instalment_amount, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    BENE_AMOUNT = #{bene_amount, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    INSTALMENT_PROPORTION = #{instalment_proportion, jdbcType=NUMERIC} ,
		    END_YEAR = #{end_year, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PAY_NUM = #{pay_num, jdbcType=NUMERIC} ,
			LIAB_CODE = #{liab_code, jdbcType=VARCHAR} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SURVIVAL_MODE = #{survival_mode, jdbcType=NUMERIC} ,
			PLAN_FREQ = #{plan_freq, jdbcType=VARCHAR} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    BEGIN_DATE = #{begin_date, jdbcType=DATE} ,
		    LIAB_ID = #{liab_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			LIAB_NAME = #{liab_name, jdbcType=VARCHAR} ,
		    SURVIVAL_W_MODE = #{survival_w_mode, jdbcType=NUMERIC} ,
			PAY_PLAN_TYPE = #{pay_plan_type, jdbcType=VARCHAR} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
			PAY_PERIOD = #{pay_period, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVIVAL_INVEST_FLAG = #{survival_invest_flag, jdbcType=NUMERIC} ,
			PAY_TYPE = #{pay_type, jdbcType=VARCHAR} ,
		    TOTAL_AMOUNT = #{total_amount, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    GURNT_PAY_PERIOD = #{gurnt_pay_period, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    ONE_TIME_FLAG = #{one_time_flag, jdbcType=NUMERIC} ,
			GURNT_PAY_LIAB = #{gurnt_pay_liab, jdbcType=VARCHAR} ,
			 ANNUITY_AMOUNT = #{annuity_amount, jdbcType=NUMERIC} , 		
			 PAY_STATUS = #{pay_status, jdbcType=NUMERIC} ,	
			 LAST_EXTRA_DATE = #{last_extra_date, jdbcType=DATE} ,
			 ANNUITY_POLICY_END_FLAG = #{annuity_policy_end_flag, jdbcType=NUMERIC} ,	
			 TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
			 AMOUNT = #{amount, jdbcType=NUMERIC} ,
			 BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
			 TERMINAL_BONUS = #{terminal_bonus, jdbcType=NUMERIC} ,
			GUARANTEE_PERIOD_TYPE = #{guarantee_period_type, jdbcType=VARCHAR} ,
			 
		</trim>
		</set>
		<![CDATA[ WHERE  PLAN_ID = #{plan_id} ]]>
	</update>

	
	<!-- 修改操作 -->
	<update id="updatePayPlanPlanID" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN ]]>
		<set>
		<trim suffixOverrides=",">
			 TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
			 AMOUNT = #{amount, jdbcType=NUMERIC} ,
			 BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
			 TERMINAL_BONUS = #{terminal_bonus, jdbcType=NUMERIC} ,
			 
		</trim>
		</set>
		<![CDATA[ WHERE  PLAN_ID = #{plan_id} ]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="findPayPlanByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE, A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanByPlanIdCondition" />
	</select>
	
	<select id="findPayPlanByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanByItemIdCondition" />
	</select>
	
	<select id="findPayPlanByPayStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanByPayStatusCondition" />
	</select>
	
	<select id="findPayPlanByPayPlanType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanByPayPlanTypeCondition" />
	</select>
	
	<select id="findPayPlanByPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanByPayDueDateCondition" />
	</select>
	
	<select id="findPayPlanByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPayPlanByQueryResults" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,A.Guarantee_Period_Type,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,a.annuity_amount,A.LIAB_CODE,A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<include refid="payPlanWhereCondition" />
		<include refid="queryPayPlanBynotGurntPayLiabCondition" />
		<if test=" pay_plan_type_list  != null and pay_plan_type_list.size()!=0">
		<![CDATA[ AND A.PAY_PLAN_TYPE in ]]>
			<foreach collection ="pay_plan_type_list" item="pay_plan_type_list" index="index" open="(" close=")" separator=",">#{pay_plan_type_list}</foreach>
		</if>
		<if test=" survival_mode_list != null and survival_mode_list != ''  ">
			<![CDATA[ AND A.SURVIVAL_MODE IN ]]>
			<foreach collection="survival_mode_list" item="survival_mode"	open="(" separator="," close=")">
					(#{survival_mode})
			</foreach>
		</if>
		<![CDATA[ ORDER BY A.PLAN_ID DESC ]]>
	</select>
	
	<!-- 查询所有操作 现金分红，并且领取形式不为累计生息，不为现金 -->
	<select id="findAllPayPlanByQueryResultsOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,a.annuity_amount,A.LIAB_CODE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<include refid="payPlanWhereCondition" />
		<include refid="queryPayPlanBynotGurntPayLiabCondition" />
		<if test=" survival_mode_list != null and survival_mode_list != ''  ">
			<![CDATA[ AND A.SURVIVAL_MODE not IN ]]>
			<foreach collection="survival_mode_list" item="survival_mode"	open="(" separator="," close=")">
					(#{survival_mode})
			</foreach>
		</if>
		<![CDATA[ ORDER BY A.PLAN_ID DESC ]]>
	</select>

<!-- 查询个数操作 -->
	<select id="findPayPlanTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="payPlanWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryPayPlanForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SELECT B.RN AS rowNumber,B.SURVIVAL_DATE,B.SURVIVAL_INVEST_RESULT, B.MONEY_CODE, B.END_PERIOD, B.END_DATE, B.PRODUCT_CODE, B.INSTALMENT_AMOUNT, 
			B.ITEM_ID, B.BENE_AMOUNT, B.BUSI_PROD_CODE, B.INSTALMENT_PROPORTION, B.END_YEAR, 
			B.PAY_NUM, B.LIAB_CODE, B.BUSI_ITEM_ID, B.POLICY_ID, B.SURVIVAL_MODE, B.PLAN_FREQ, B.PLAN_ID, 
			B.BEGIN_DATE, B.LIAB_ID, B.PAY_STATUS, B.LIAB_NAME, B.SURVIVAL_W_MODE, B.LAST_EXTRA_DATE,
			B.PAY_PLAN_TYPE, B.PAY_YEAR, B.PAY_PERIOD, B.POLICY_CODE, B.SURVIVAL_INVEST_FLAG, B.PAY_TYPE, B.TOTAL_AMOUNT, B.TOTAL_PREM_AF,B.AMOUNT,B.BONUS_SA,B.TERMINAL_BONUS,
			B.PAY_DUE_DATE, B.GURNT_PAY_PERIOD, B.ONE_TIME_FLAG, B.GURNT_PAY_LIAB FROM (
					SELECT ROWNUM RN,A.SURVIVAL_DATE, A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!--leihong 批处理抽档条件 -->
	<sql id="extractPayPlanCondition">
		<!-- 处理日期 -->
		<if test=" batch_date  != null and batch_date !='' "><![CDATA[ AND A.PAY_DUE_DATE <= #{batch_date} ]]></if>		
		<if test=" pay_status != null and pay_status != ''  "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
		<if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
		<if test=" pay_plan_type_list  != null and pay_plan_type_list.size()!=0">
		<![CDATA[ AND A.PAY_PLAN_TYPE in ]]>
			<foreach collection ="pay_plan_type_list" item="pay_plan_type_list" index="index" open="(" close=")" separator=",">#{pay_plan_type_list}</foreach>
		</if>
		<if test="policyId_list  != null and policyId_list.size()!=0">
		<![CDATA[ AND A.POLICY_ID in ]]>
			<foreach collection ="policyId_list" item="policyId_list" index="index" open="(" close=")" separator=",">#{policyId_list}</foreach>
		</if>
		<!-- 保单管理机构 -->
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[ AND  B.ORGAN_CODE in (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		<!-- 保单号 -->
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	
	<!-- 查询满足条件的单条数据 -->
	<select id="PA_findPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN  A  WHERE 1 = 1  ]]>
		<include refid="payPlanWhereCondition" />
	</select>
	
	<select id="findPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB, A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A  WHERE 1 = 1  ]]>
		<include refid="payPlanWhereCondition" />
	</select>
	
	<!-- 根据保单id列表查询数据 -->
	<select id="PA_queryPayPlanByPolicyIds" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B  WHERE 1 = 1 AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID  ]]>
		<include refid="extractPayPlanCondition"/>
	</select>
	
	<!-- 根据保单id查询险种下的养老金责任-->
	<select id="find_isPensionLiability" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT COUNT(1) 
				FROM APP___PAS__DBUSER.T_PAY_PLAN A  
				WHERE A.POLICY_ID = #{policy_id} and A.LIAB_ID = '1106'  
		]]>
	</select>
	
	
	<!-- 查询在某个时间段的处理中以及待处理的满期金记录操作 -->
	<select id="findPayPlanByPolicyIdDurationTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
          D.POLICY_CODE,
          A.AGENT_CODE,
          E.PAY_DUE_DATE,
          D.VALIDATE_DATE,
          F.PRODUCT_ABBR_NAME,
          C.CUSTOMER_NAME,
          C.CUSTOMER_BIRTHDAY,
          C.OFFEN_USE_TEL,
          G.ADDRESS
     FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A,APP___PAS__DBUSER.T_PAY_PLAN E,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD D
     LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT F
     ON F.BUSINESS_PRD_ID=D.BUSI_PRD_ID
     LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER B
     ON B.POLICY_ID=D.POLICY_ID 
     LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER C
     ON C.CUSTOMER_ID=B.CUSTOMER_ID
     LEFT JOIN APP___PAS__DBUSER.T_ADDRESS G
     ON G.ADDRESS_ID=B.ADDRESS_ID
     WHERE A.AGENT_CODE=#{agent_code} 
     AND E.POLICY_ID=A.POLICY_ID
     AND E.PAY_STATUS IN ('1','2')
     AND E.PAY_PLAN_TYPE = '4'
     AND D.BUSI_PROD_CODE=E.BUSI_PROD_CODE
     AND D.POLICY_ID=A.POLICY_ID
     AND D.MASTER_BUSI_ITEM_ID is null
     AND E.PAY_DUE_DATE BETWEEN #{get_To_Date_Start} AND #{get_To_Date_End} ]]>
	</select>
	
	<!-- 通过保单id查询领取项目名-->
	<select id="findWebLPEdorPayPlanByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT A.*,
				(SELECT TYPE_DESC FROM APP___PAS__DBUSER.T_PAY_PLAN_TYPE WHERE PAY_PLAN_TYPE=A.PAY_PLAN_TYPE AND ROWNUM=1) 
				FROM APP___PAS__DBUSER.T_PAY_PLAN A 
				WHERE A.POLICY_ID = #{policy_id} 
		]]>
	</select>
	<!-- 查询t_pay_plan -->
	
	<select id="findPayPlanByLiadIdAndLianName" resultType="java.util.Map" parameterType="java.util.Map" >
	
<![CDATA[	select A.*
  from  APP___PAS__DBUSER.t_pay_plan A where 1=1 ]]> 
  <include refid="payPlanWhereCondition" />
	</select>
	
	<!--by zhaoyoan_wb 通过保单号(及其他条件,老核心polno)查询记录 -->
	<select id="findPayPlanByPolicyAndDutyAndLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.* FROM APP___PAS__DBUSER.T_PAY_PLAN A
			WHERE 1=1
      		AND (SELECT POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER 
      			WHERE POLICY_CODE=#{policy_code})=A.POLICY_ID 
      	]]>
		<if test=" pol_no != null and pol_no != ''  ">
			<![CDATA[ AND (SELECT NVL(OLD_POL_NO,TO_CHAR(BUSI_ITEM_ID)) FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD 
      			WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID)=#{pol_no} ]]>
		</if>
		<include refid="payPlanWhereCondition" />

	</select>
	
	<select id="PA_queryReceiveDateByContNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 1 FLAG,
			       T.BUSI_ITEM_ID,
			       T.LIAB_ID,
			       T.LIAB_CODE,
			       T.LIAB_NAME,
			       T.PRODUCT_CODE,
			       TPL.PRODUCT_NAME,
			       T.BEGIN_DATE,
			       TCBP.BUSI_PROD_CODE,
			       TCBP.OLD_POL_NO,
			       TCP.POLICY_CODE
			  FROM APP___PAS__DBUSER.T_PAY_PLAN           T,
			       APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
			       APP___PAS__DBUSER.T_PRODUCT_LIFE       TPL,
			       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
			 WHERE T.ITEM_ID = TCP.ITEM_ID
			   AND TCP.PRODUCT_ID = TPL.PRODUCT_ID
			   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
			   and T.LIAB_ID='1103'
			   AND TCP.POLICY_CODE = #{policy_code}
			   ORDER BY T.LIAB_CODE,T.PRODUCT_CODE,TPL.PRODUCT_NAME
		]]>
	</select>
	
	<!-- 查询出险日是否在首次年金领取日之前 -->
	<select id="findPayBeginDate" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(*) FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id != null and busi_item_id != null and item_id  != null">
		<![CDATA[ 
		AND A.BEGIN_DATE=(
			 SELECT min(b.begin_date) FROM APP___PAS__DBUSER.T_PAY_PLAN b WHERE 1 = 1  AND b.POLICY_ID =#{policy_id} AND b.BUSI_ITEM_ID =#{busi_item_id}
			 AND b.ITEM_ID = #{item_id}
 			AND b.PAY_PLAN_TYPE =3
 			and b.pay_status <> 4
 			) ]]>
		</if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" begin_date  != null  and  begin_date  != ''  "><![CDATA[ AND A.BEGIN_DATE >= #{begin_date} ]]></if>
		<if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
	</select>
			<!-- 查询满期金 -->
	<select id="query_FeeAmounts" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
					SELECT sum(INSTALMENT_AMOUNT)
		  FROM dev_pas.t_pay_plan
		 WHERE pay_plan_type = 4
		   and SURVIVAL_MODE = 3
		   and PAY_STATUS = 4
		   and BUSI_ITEM_ID = #{busi_item_id}	
		   and POLICY_ID=#{policy_id}
		]]>
	</select>
<!-- 查询结果按照PAY_DUE_DATE排序 -->
	<select id="findAllPayPlanOrderDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE, 
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1
		]]>
		 <include refid="payPlanWhereCondition" />
		 <![CDATA[ORDER BY A.PAY_DUE_DATE DESC]]>
	</select>
	
	
	
	
	<select id="findPayPlanByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT distinct b.pay_due_date FROM APP___PAS__DBUSER.T_PAY_PLAN  A  ,dev_pas.t_pay_due B 
			where A.POLICY_ID = #{policy_id} and A.PAY_PLAN_TYPE='3' and a.plan_id = b.plan_id and a.policy_id = b.policy_id
		]]>
		<if test=" start_date  != null "><![CDATA[ AND A.PAY_DUE_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.PAY_DUE_DATE <= #{end_date} ]]></if>
		<![CDATA[ order by b.pay_due_date ]]>
	</select>
	
	<select id="findallClaimDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select NVL(SUM(B.Fee_Amount),0) AS TOTAL_AMOUNT from dev_pas.t_pay_plan A,Dev_pas.t_Pay_Due B 
			 WHERE A.Plan_Id = B.Pay_Id AND A.POLICY_ID=#{policy_id}
		]]>
		<if test="PAY_DUE_DATE !=null"><![CDATA[ AND B.PAY_DUE_DATE <= #{PAY_DUE_DATE} ]]></if>
		<include refid="findallPlan_TypeList" />
	</select>
	
	<!-- 增加集合查询条件 -->
	<sql id="findallPlan_TypeList">
		<if test=" pay_plan_type_list  != null  and pay_plan_type_list != ''  ">
			<![CDATA[ AND A.Pay_Plan_Type IN ]]>
			<foreach collection ="pay_plan_type_list" item="pay_plan_type" open="(" close=")" separator=",">
				<![CDATA[ #{pay_plan_type} ]]>
			</foreach>
		</if>
	</sql>
	
	<!-- 保单未领生存金查询 -->
	<select id="PA_queryUnclaimLiGold" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		     SELECT T2.BUSI_ITEM_ID,T3.POLICY_CODE,(SELECT T.PRODUCT_NAME_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT T WHERE T.BUSINESS_PRD_ID=
        (SELECT A.BUSI_PRD_ID FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE A.BUSI_ITEM_ID=T1.BUSI_ITEM_ID)) PRODUCT_NAME_SYS,
        T2.PAY_DUE_DATE,T1.BEGIN_DATE,T1.END_DATE,T1.PLAN_FREQ
        FROM APP___PAS__DBUSER.T_PAY_PLAN T1,APP___PAS__DBUSER.T_PAY_DUE T2,APP___PAS__DBUSER.T_CONTRACT_MASTER T3
        WHERE T1.PLAN_ID=T2.PLAN_ID AND T2.FEE_STATUS ='00'
        AND T1.POLICY_ID=T3.POLICY_ID AND T1.PAY_PLAN_TYPE IN('3','4')
        AND T3.POLICY_CODE=#{policy_code} and  T2.BUSI_PROD_CODE='00184000'  
        union 
             SELECT T2.BUSI_ITEM_ID,T3.POLICY_CODE,(SELECT T.PRODUCT_NAME_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT T WHERE T.BUSINESS_PRD_ID=
        (SELECT A.BUSI_PRD_ID FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE A.BUSI_ITEM_ID=T1.BUSI_ITEM_ID)) PRODUCT_NAME_SYS,
        T2.PAY_DUE_DATE,T1.BEGIN_DATE,T1.END_DATE,T1.PLAN_FREQ
        FROM APP___PAS__DBUSER.T_PAY_PLAN T1,APP___PAS__DBUSER.T_PAY_DUE T2,APP___PAS__DBUSER.T_CONTRACT_MASTER T3
        WHERE T1.PLAN_ID=T2.PLAN_ID AND T2.FEE_STATUS ='00'
        AND T1.POLICY_ID=T3.POLICY_ID AND T1.PAY_PLAN_TYPE IN('3','4')AND T2.SURVIVAL_INVEST_FLAG='1'
        AND T3.POLICY_CODE=#{policy_code} ORDER BY PAY_DUE_DATE DESC 
		]]>
	</select>

	<!--   保全试算_年金、满期金给付 接口使用   最早可以领取的时间-->
	<select id="PA_qureyEarliestBeginDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		
				select min(a.begin_date) begin_date
		         from dev_pas.t_pay_plan a
		        where a.policy_code = #{policy_code}
		          and a.pay_plan_type in (3,4)
				    
		]]>
	</select>
   <select id="PA_queryMaxEndDate" resultType="java.util.Map" parameterType="java.util.Map">
	    SELECT MAX(TPP.end_date)  end_date FROM DEV_PAS.T_PAY_PLAN TPP WHERE TPP.POLICY_CODE=#{policy_code} AND TPP.PAY_PLAN_TYPE=#{pay_plan_type}
	</select>   
          
	
	 <!-- 查找年金险种 -->
	 <select id="PA_findPensionInsurance" resultType="java.util.Map" parameterType="java.util.Map">
	    
	    SELECT A.PAY_PLAN_TYPE,A.POLICY_CODE,A.POLICY_ID,A.BUSI_ITEM_ID,A.BUSI_PROD_CODE 
	    FROM APP___PAS__DBUSER.t_PAY_PLAN A WHERE A.PAY_PLAN_TYPE in('2','3','8','10','11')
			AND A.POLICY_CODE = #{policy_code}
			AND A.BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} 
			AND ROWNUM = 1
	</select>
	
	<!-- 查询领取形式 -->
	 <select id="PA_findSurvivalModeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	        SELECT A.SURVIVAL_MODE, A.SURVIVAL_W_MODE
         FROM APP___PAS__DBUSER.T_PAY_PLAN A
		WHERE A.POLICY_CODE = #{policy_code} 
		and A.SURVIVAL_MODE is not null and 
		 A.SURVIVAL_W_MODE is not null
		AND ROWNUM = 1 
	</select>
	
	<!-- 查询符合条件的年金满期金 -->
	 <select id="PA_findallNJMJ" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
	          SELECT T.PLAN_ID, T.POLICY_ID, T.BUSI_PROD_CODE
		        FROM APP___PAS__DBUSER.T_PAY_PLAN T
		       WHERE 1 = 1
		         AND (T.PAY_PLAN_TYPE = '4' OR T.Liab_Id = '1106')
		         AND T.POLICY_ID = #{policy_id}
		         and t.pay_status in ('1', '2')
         		 AND ((T.BEGIN_DATE < #{begin_date}) or
		             (exists (SELECT 1
		                         FROM dev_pas.t_pay_due aa
		                        where aa.plan_id = t.plan_id
		                          and aa.pay_due_date < #{begin_date}
		                          and aa.fee_status = '00')))
		         AND NOT EXISTS (SELECT B.PRODUCT_CODE
		                FROM DEV_PAS.T_LOAN_PRODUCT_CONFIG B
		               WHERE B.PRODUCT_CODE = T.BUSI_PROD_CODE)
		]]>		         
	</select>
	
	<!-- 查询符合条件的养老金 -->
	 <select id="PA_findallYJ" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
	         SELECT T.PLAN_ID,T.POLICY_ID,T.BUSI_PROD_CODE
			  FROM APP___PAS__DBUSER.T_PAY_PLAN T
			 WHERE 1 = 1
			   AND T.LIAB_ID = '1106'
			   AND T.POLICY_ID = #{policy_id}
			   AND T.BEGIN_DATE <= #{begin_date}
			   AND NOT EXISTS (SELECT B.PRODUCT_CODE
			          FROM DEV_PAS.T_LOAN_PRODUCT_CONFIG B
			         WHERE B.PRODUCT_CODE = T.BUSI_PROD_CODE)
		]]>			         
	</select>
	
	
	<update id="PA_updateInstalmentAmount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN ]]>
			<set>
				<trim suffixOverrides=",">
					 INSTALMENT_AMOUNT = #{instalment_amount},
				</trim>
			</set>
		<![CDATA[ WHERE  PLAN_ID = #{plan_id} ]]>
	</update>
	
	<!-- 根据pay_plan_type和险种 查询责任组 -->      
	<select id="PA_queryproductByBusiId" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
			  SELECT TCP.ITEM_ID,TCP.CHARGE_PERIOD,TCP.AMOUNT
		  FROM DEV_PAS.T_PAY_PLAN TPP, DEV_PAS.T_CONTRACT_PRODUCT TCP
		 WHERE TCP.ITEM_ID = TPP.ITEM_ID
		  	AND TPP.PAY_PLAN_TYPE = #{pay_plan_type}
	   		AND TPP.POLICY_CODE = #{policy_code}
	  ]]>
	</select>
	<!-- 查询已实付生存金\满期金\年金 总额 -->
	<select id="PA_totalAnnuity" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
			 SELECT SUM(TPD.FEE_AMOUNT) AS TOTAL_AMOUNT
				FROM DEV_PAS.T_PAY_PLAN TPP 
				LEFT JOIN DEV_PAS.T_PAY_DUE TPD ON TPP.PLAN_ID = TPD.PLAN_ID 
				WHERE TPD.FEE_STATUS IN ('00','01') 
				AND TPP.LIAB_ID != '1131' 
				AND TPP.PAY_PLAN_TYPE IN ('3','4','10','11')
				AND TPP.POLICY_ID = #{policy_id}
				AND TPP.BUSI_ITEM_ID = #{busi_item_id}
				AND TPD.PAY_DUE_DATE <= #{pay_due_date}
				GROUP BY  TPD.POLICY_CODE
	  ]]>
	</select>
	
	<!-- 查询保单下最早的发放日期 -->
	<select id="PA_findFirstTime" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		select min(a.begin_date) begin_date
		         from APP___PAS__DBUSER.t_pay_plan a
		        where 1 = 1
	  ]]>
	  <include refid="payPlanWhereCondition" />
	</select>
	
	<!-- 查询险种生存给付计划表多条数据-->
	<select id="findAllPayPlansForXQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVIVAL_DATE,A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
			A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
			A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
			A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
			A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN  A  WHERE 1 = 1  ]]>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code}]]></if>
		<if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE >= #{pay_due_date} ]]></if>
		<![CDATA[ORDER BY A.PAY_DUE_DATE DESC]]>
	</select>
	
	<select id="queryPayPlanByNJ" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	 SELECT SUBSTR(TO_CHAR(WM_CONCAT(C.PAY_PLAN_TYPE)),0,1) PAY_PLAN_TYPE,
        SUM(C.Instalment_Amount) Instalment_Amount
       FROM (SELECT A.PAY_PLAN_TYPE,
                SUM(NVL(A.Instalment_Amount,0)) Instalment_Amount,A.PAY_DUE_DATE
           	FROM APP___PAS__DBUSER.T_PAY_PLAN A
          WHERE 1 = 1  ]]>
          <if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
          <if test=" planIdList  != null and planIdList.size()!=0">
		<![CDATA[ AND A.PLAN_ID in ]]>
			<foreach collection ="planIdList" item="planIdList" index="index" open="(" close=")" separator=",">#{planIdList}</foreach>
		</if>
         <![CDATA[
            AND A.POLICY_CODE = #{policy_code}	
            AND A.PAY_DUE_DATE = #{pay_due_date} 
          GROUP BY A.PAY_PLAN_TYPE, A.PAY_DUE_DATE) C 
		 ]]>
	 </select>
	 
	 <select id="PA_queryPayPlanByNJOrSCJ" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	 SELECT A.SURVIVAL_INVEST_RESULT, A.MONEY_CODE,  A.END_PERIOD, A.END_DATE, A.PRODUCT_CODE, A.INSTALMENT_AMOUNT, 
      A.ITEM_ID, A.BENE_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
      A.PAY_NUM, A.LIAB_CODE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.PLAN_ID, 
      A.BEGIN_DATE, A.LIAB_ID, A.PAY_STATUS, A.LIAB_NAME, A.SURVIVAL_W_MODE, A.LAST_EXTRA_DATE,
      A.PAY_PLAN_TYPE, A.PAY_YEAR, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.PAY_TYPE, A.TOTAL_AMOUNT, A.TOTAL_PREM_AF,A.AMOUNT,A.BONUS_SA,A.TERMINAL_BONUS,
      A.PAY_DUE_DATE, A.GURNT_PAY_PERIOD, A.ONE_TIME_FLAG, A.GURNT_PAY_LIAB, A.GUARANTEE_PERIOD_TYPE,A.ANNUITY_POLICY_END_FLAG FROM APP___PAS__DBUSER.T_PAY_PLAN A
          WHERE A.POLICY_ID = #{policy_id}
          AND A.BUSI_ITEM_ID = #{busi_item_id}	
          AND A.PAY_PLAN_TYPE IN ('2','3')
          AND ROWNUM = 1
          ORDER BY A.UPDATE_TIME DESC ]]>
	 </select>
	 
	<select id="PA_findAllPayPlanForIsFirst" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		Select a.plan_id,
		       a.policy_code,
		       a.busi_item_id,
		       a.busi_prod_code,
		       a.pay_num
		  From dev_pas.t_Pay_Plan a
		 where 1 = 1
		   and a.policy_code = #{policy_code}
		   and a.pay_num > #{pay_num}
		   and a.busi_item_id = #{busi_item_id}	
		   AND A.PAY_PLAN_TYPE = #{pay_plan_type}
	]]>
	 </select>
	 
	 	<select id="PA_queryPayPlanInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		SELECT TPP.PLAN_ID,
       TPP.LIAB_CODE,
       TPP.LIAB_NAME,
       TPP.SURVIVAL_MODE,
       TPP.SURVIVAL_W_MODE,
       TPP.BEGIN_DATE,
       TPP.END_DATE,
       TPP.PLAN_FREQ,
       TPP.SURVIVAL_INVEST_FLAG,
       TBA.ACCO_NAME,
       TB.BANK_NAME ISSUE_BANK_NAME,
       TBA.BANK_ACCOUNT
  FROM DEV_PAS.T_PAY_PLAN TPP
  LEFT JOIN DEV_PAS.T_PAY_PLAN_PAYEE TPPP
    ON TPP.PLAN_ID = TPPP.PLAN_ID
  LEFT JOIN DEV_PAS.T_BANK_ACCOUNT TBA
    ON TPPP.PAYEE_ACCOUNT_ID = TBA.ACCOUNT_ID
  LEFT JOIN DEV_PAS.T_BANK TB
    ON TB.BANK_CODE = TBA.BANK_CODE
 WHERE 1 = 1
 AND TPP.PAY_PLAN_TYPE IN ('3','4','8','10','11')
	]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND TPP.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPP.POLICY_CODE = #{policy_code} ]]></if>
	 </select>
	 
	 	<select id="findUniversalPayPlanforPA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			with policyChange as
 (select a.*
            from dev_pas.t_cs_policy_change a
            join dev_pas.t_cs_accept_change b
              on a.accept_id = b.accept_id
           where a.policy_code = #{policy_code}
             and b.accept_status = '18'
             and b.finish_time > #{pay_due_date}
             and b.service_code in ('RS', 'AE')
           order by b.finish_time)
select a.sub_policy_code  as policy_code,
       a.sub_busi_item_id as sub_busi_item_id
  from dev_pas.t_cs_contract_relation a
  join policyChange b
    on a.policy_chg_id = b.policy_chg_id
   and a.old_new = '0'
union
select c.policy_code, c.busi_item_id
  from dev_pas.t_cs_contract_master a
  join policyChange b
    on a.policy_chg_id = b.policy_chg_id and a.old_new = '0'
  join dev_pas.t_contract_busi_prod c
    on c.policy_code = a.relation_policy_code
 where exists (select 1
    from dev_pas.t_business_product d
   where d.product_code_sys = c.busi_prod_code
     and d.product_category1 = '20003')
     and ROWNUM = 1
		]]>
	</select>
</mapper>
