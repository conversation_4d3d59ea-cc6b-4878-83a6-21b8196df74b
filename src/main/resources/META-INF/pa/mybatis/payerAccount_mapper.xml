<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="payerAccount">

	<sql id="PA_payerAccountWhereCondition">
		<if test=" next_account_id  != null "><![CDATA[ AND A.NEXT_ACCOUNT_ID = #{next_account_id} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" account_name != null and account_name != ''  "><![CDATA[ AND A.ACCOUNT_NAME = #{account_name} ]]></if>
		<if test=" account_bank != null and account_bank != ''  "><![CDATA[ AND A.ACCOUNT_BANK = #{account_bank} ]]></if>
		<if test=" payer_id  != null "><![CDATA[ AND A.PAYER_ID = #{payer_id} ]]></if>
		<if test=" next_account_bank != null and next_account_bank != ''  "><![CDATA[ AND A.NEXT_ACCOUNT_BANK = #{next_account_bank} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
		<if test=" pay_next != null and pay_next != ''  "><![CDATA[ AND A.PAY_NEXT = #{pay_next} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" next_account_name != null and next_account_name != ''  "><![CDATA[ AND A.NEXT_ACCOUNT_NAME = #{next_account_name} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" next_account != null and next_account != ''  "><![CDATA[ AND A.NEXT_ACCOUNT = #{next_account} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayerAccountByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerAccountByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerAccountByPayerIdCondition">
		<if test=" payer_id  != null "><![CDATA[ AND A.PAYER_ID = #{payer_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerAccountByNextAccountIdCondition">
		<if test=" next_account_id  != null "><![CDATA[ AND A.NEXT_ACCOUNT_ID = #{next_account_id} ]]></if>
	</sql>	
    <sql id="PA_queryPayerAccountByByPolicyIdCondition">
         <if test="policy_id!=null"><![CDATA[ AND A.policy_id = #{policy_id} ]]></if>
    </sql>
<!-- 添加操作 -->
	<insert id="PA_addPayerAccount"  useGeneratedKeys="false"  parameterType="java.util.Map">	
	 	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PAYER_ACCOUNT__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAYER_ACCOUNT(
				NEXT_ACCOUNT_ID, ACCOUNT_ID, MEDICAL_PAY_ORDER_NEXT, INSERT_TIME, ACCOUNT_NAME, ACCOUNT_BANK, MEDICAL_NO, 
				NEXT_ACCOUNT_BANK, PAYER_ID, UPDATE_TIME, ACCOUNT, MEDICAL_NO_NEXT, PAY_NEXT, INSERT_TIMESTAMP, 
				MEDICAL_PAY_ORDER, PAY_MODE, UPDATE_BY, LIST_ID, NEXT_ACCOUNT_NAME, UPDATE_TIMESTAMP, INSERT_BY, 
				PAY_LOCATION, POLICY_ID, NEXT_ACCOUNT,LAW_MAN ) 
			VALUES (
				#{next_account_id, jdbcType=NUMERIC}, #{account_id, jdbcType=NUMERIC} , #{medical_pay_order_next, jdbcType=VARCHAR} , SYSDATE , #{account_name, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , #{medical_no, jdbcType=VARCHAR} 
				, #{next_account_bank, jdbcType=VARCHAR} , #{payer_id, jdbcType=NUMERIC} , SYSDATE , #{account, jdbcType=VARCHAR} , #{medical_no_next, jdbcType=VARCHAR} , #{pay_next, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{medical_pay_order, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{next_account_name, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{pay_location, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{next_account, jdbcType=VARCHAR}, #{law_man, jdbcType=VARCHAR}) 
		 ]]>
	</insert>
	<!-- 添加操作 含主键生成策略 -->
	<insert id="PA_addPayerAccount_cs"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT S_CONTRACT_BENE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAYER_ACCOUNT(
				NEXT_ACCOUNT_ID, ACCOUNT_ID, INSERT_TIME, ACCOUNT_NAME, ACCOUNT_BANK, PAYER_ID, NEXT_ACCOUNT_BANK, 
				UPDATE_TIME, ACCOUNT, PAY_NEXT, INSERT_TIMESTAMP, PAY_MODE, UPDATE_BY, LIST_ID, 
				NEXT_ACCOUNT_NAME, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, NEXT_ACCOUNT ) 
			VALUES (
				#{next_account_id, jdbcType=NUMERIC}, #{account_id, jdbcType=NUMERIC} , SYSDATE , #{account_name, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , #{payer_id, jdbcType=NUMERIC} , #{next_account_bank, jdbcType=VARCHAR} 
				, SYSDATE , #{account, jdbcType=VARCHAR} , #{pay_next, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{pay_mode, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{next_account_name, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{next_account, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePayerAccount" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT WHERE LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePayerAccount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER_ACCOUNT ]]>
		<set>
		<trim suffixOverrides=",">
		    NEXT_ACCOUNT_ID = #{next_account_id, jdbcType=NUMERIC} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
			MEDICAL_PAY_ORDER_NEXT = #{medical_pay_order_next, jdbcType=VARCHAR} ,
			ACCOUNT_NAME = #{account_name, jdbcType=VARCHAR} ,
			ACCOUNT_BANK = #{account_bank, jdbcType=VARCHAR} ,
			MEDICAL_NO = #{medical_no, jdbcType=VARCHAR} ,
		    PAYER_ID = #{payer_id, jdbcType=NUMERIC} ,
			NEXT_ACCOUNT_BANK = #{next_account_bank, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT = #{account, jdbcType=VARCHAR} ,
			MEDICAL_NO_NEXT = #{medical_no_next, jdbcType=VARCHAR} ,
			PAY_NEXT = #{pay_next, jdbcType=VARCHAR} ,
			MEDICAL_PAY_ORDER = #{medical_pay_order, jdbcType=VARCHAR} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			NEXT_ACCOUNT_NAME = #{next_account_name, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			NEXT_ACCOUNT = #{next_account, jdbcType=VARCHAR} ,
			PAY_LOCATION = #{pay_location, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPayerAccountByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByListIdCondition" />
	</select>
	
	<select id="PA_findPayerAccountByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByAccountIdCondition" />
	</select>
	
	<select id="PA_findPayerAccountByPayerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByPayerIdCondition" />
	</select>
	
	<select id="PA_findPayerAccountByNextAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByNextAccountIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT,A.MEDICAL_NO,A.MEDICAL_NO_NEXT,A.MEDICAL_PAY_ORDER,A.MEDICAL_PAY_ORDER_NEXT,A.LAW_MAN FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>
	
	<!-- 查询保单状态续期划款中 -->
	<select id="PA_findAllPayerAccount_policy_state" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT t.unit_number,t.fee_status,t.policy_code,max(t.due_time)
	        from APP___PAS__DBUSER.T_PREM_ARAP t 
	       	where t.fee_type in ('G003030100','G003010000')
	       	and exists (select 'X' from APP___PAS__DBUSER.t_contract_master tc 
	       				where t.policy_code = tc.policy_code and tc.policy_id = #{policy_id} )
	       	group by t.unit_number,t.fee_status,t.policy_code
		]]>
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayerAccountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayerAccountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NEXT_ACCOUNT_ID, B.ACCOUNT_ID, B.ACCOUNT_NAME, B.ACCOUNT_BANK, B.PAYER_ID, B.NEXT_ACCOUNT_BANK, 
			B.ACCOUNT, B.PAY_NEXT, B.PAY_MODE, B.LIST_ID, 
			B.NEXT_ACCOUNT_NAME, B.POLICY_ID, B.NEXT_ACCOUNT FROM (
					SELECT ROWNUM RN, A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_payerAccountWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findPayerAccountByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME,  A.NEXT_ACCOUNT,A.INSERT_TIME, A.MEDICAL_NO,A.PAY_LOCATION,
        A.MEDICAL_PAY_ORDER, A.MEDICAL_NO_NEXT, A.MEDICAL_PAY_ORDER_NEXT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1=1]]>		
			<include refid="PA_queryPayerAccountByByPolicyIdCondition" />
	</select>
	
	<!-- R00101000087续期缴费账号信息变更(停用)（初始化） -->
	<select id="WS_findPayerAccountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select (select p.prem_freq
          from APP___PAS__DBUSER.t_contract_product p
         where p.policy_id = t.policy_id
           and exists (select 1
                  from APP___PAS__DBUSER.t_contract_busi_prod bp
                 where bp.policy_id = p.policy_id
                   and bp.busi_item_id = p.busi_item_id
                   and bp.master_busi_item_id is null)
           and rownum = 1) as prem_freq,
       APP___PAS__DBUSER.t.pay_location,
       APP___PAS__DBUSER.t.next_account_bank,
       APP___PAS__DBUSER.t.next_account,
       APP___PAS__DBUSER.t.next_account_name
  from APP___PAS__DBUSER.T_PAYER_ACCOUNT t
 where t.policy_id =
       (select m.policy_id from APP___PAS__DBUSER.t_contract_master m where m.policy_code = #{policyCode})
		]]>
	</select>
	
	<select id="PA_findPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT,A.INSERT_TIME,A.LAW_MAN FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>
	
	
	<select id="PA_findPayLocationPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, A.PAY_LOCATION,
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>

	<select id="PA_findPayerAccountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.NEXT_ACCOUNT_ID,
		       A.ACCOUNT_ID,
		       A.ACCOUNT_NAME,
		       A.ACCOUNT_BANK,
		       A.PAYER_ID,
		       A.NEXT_ACCOUNT_BANK,
		       A.ACCOUNT,
		       A.PAY_NEXT,
		       A.PAY_MODE,
		       A.LIST_ID,
		       A.NEXT_ACCOUNT_NAME,
		       A.POLICY_ID,
		       A.NEXT_ACCOUNT,
		       TB.IS_CREDIT_CARD
		  FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT A
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER M
		    ON M.POLICY_ID = A.POLICY_ID
		 LEFT JOIN APP___PAS__DBUSER.T_BANK TB
		 ON A.ACCOUNT_BANK = TB.BANK_CODE
		 WHERE 1 = 1
		   AND M.POLICY_CODE = #{policy_code} 
   ]]>
	</select>

	<!-- 根据保单id查询续期交费信息 -->
	<select id="PA_findPayerAccountDescByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			    SELECT TPA.PAY_NEXT,
				       TPM.NAME,
				       TPA.NEXT_ACCOUNT_NAME,
				       TPA.NEXT_ACCOUNT_BANK,
				       TB.BANK_NAME,
				       TPA.NEXT_ACCOUNT,
				       TPA.PAY_LOCATION
				  FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA 
				  LEFT JOIN APP___PAS__DBUSER.T_PAY_MODE TPM ON TPA.PAY_NEXT = TPM.CODE
				  LEFT JOIN APP___PAS__DBUSER.T_BANK     TB  ON TPA.NEXT_ACCOUNT_BANK = TB.BANK_CODE
				 WHERE 1 = 1 
				   AND ROWNUM = 1
			       AND TPA.POLICY_ID = #{policy_id}  ]]>
	</select>
	
	<!-- 根据保单id查询首期+续期交费信息 -->
	<select id="PA_findAccountDesForQueryAIPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			    SELECT A.POLICY_ID,
				       A.ACCOUNT_NAME,
				       A.ACCOUNT_BANK,
				       (SELECT TB.BANK_NAME
				          FROM DEV_PAS.T_BANK TB
				         WHERE TB.BANK_CODE = A.ACCOUNT_BANK) AS BANK_NAME,
				       A.ACCOUNT,
				       A.NEXT_ACCOUNT_NAME,
				       A.NEXT_ACCOUNT_BANK,
				       (SELECT TB.BANK_NAME
				          FROM DEV_PAS.T_BANK TB
				         WHERE TB.BANK_CODE = A.NEXT_ACCOUNT_BANK) AS NEXT_BANK_NAME,
				       A.NEXT_ACCOUNT
				  FROM DEV_PAS.T_PAYER_ACCOUNT A 
				 WHERE 1 = 1
			       AND A.POLICY_ID = #{policy_id}
			       AND ROWNUM = 1  ]]>
	</select>
	
	<!-- 投保人保单缴费信息查询接口 -->
	<select id="queryPolicyHolderPolicyAccounts" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[	
 SELECT CASE   
          WHEN cm.liability_state=1  THEN '0'
          WHEN cm.LIABILITY_STATE=3  AND cm.end_cause='01' 
               THEN '1'
          WHEN cm.liability_state=3  AND cm.end_cause='99'  
               THEN '2'  
          WHEN cm.liability_state=3  AND cm.end_cause='05'  
               THEN '3'  
          WHEN cm.liability_state=3 AND cm.end_cause='08'             
               THEN '4'
          WHEN cm.liability_state=3 AND cm.end_cause='06'             
               THEN '5'   
          WHEN cm.liability_state=3 AND cm.end_cause='02'             
               THEN '6'  
          WHEN cm.liability_state=3 AND cm.end_cause='07'             
               THEN '7'              
          WHEN cm.liability_state=3 AND cm.end_cause='03'             
               THEN '8' 
           WHEN cm.liability_state=4        
               THEN '10'        
                    
     ELSE '2' END  AS LIABILITY_STATE,
  
   cm.policy_code,cm.policy_id,bp.product_abbr_name,pa.next_account,pa.next_account_name,cm.validate_date,
   pa.next_account_bank,pa.pay_location,pa.pay_next
 
 FROM APP___PAS__DBUSER.t_contract_master cm,
 
      APP___PAS__DBUSER.t_Payer_Account pa,
 
      APP___PAS__DBUSER.t_Contract_Busi_Prod cbp,
      APP___PAS__DBUSER.t_Business_Product bp
 
 where cm.policy_id=pa.policy_id and pa.policy_id=cbp.policy_id 
 
 and cbp.busi_prd_id=bp.business_prd_id and cbp.master_busi_item_id is null and cm.policy_id in    ]]>
  <foreach collection ="list" item="policy_id" index="index" open="(" close=")" separator=",">
		<![CDATA[ #{policy_id}]]>
	</foreach>
	</select>
	
	<!-- 获取账户保单续期银行信息 -->
	<select id="findAccountBankInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select c.NEXT_ACCOUNT_BANK,
       		   c.NEXT_ACCOUNT_NAME,
       		   c.NEXT_ACCOUNT,
       		  (SELECT T.BANK_NAME
          			  FROM DEV_PAS.T_BANK T
         			  where T.BANK_CODE = C.NEXT_ACCOUNT_BANK
                      and ROWNUM<=1)
                bankName
 		 from dev_pas.T_CONTRACT_MASTER a,
      		  DEV_PAS.T_POLICY_HOLDER   b,
       		  DEV_PAS.t_payer_account   c,
       		  DEV_PAS.T_CUSTOMER        d
 		where a.policy_id = b.policy_id
  			 and a.policy_id = c.policy_id
   			 and b.CUSTOMER_ID = d.Customer_Id
   			 and a.policy_code = #{policy_code}
	 ]]>
	</select>
	
	
	<!-- 获取账户保单续期银行信息 -->
	<select id="PA_findAccountBankInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT 
       		   C.NEXT_ACCOUNT
 		 FROM DEV_PAS.T_CONTRACT_MASTER A LEFT JOIN 
       		  DEV_PAS.T_PAYER_ACCOUNT   C  ON A.POLICY_ID = C.POLICY_ID
 		WHERE A.POLICY_CODE = #{policy_code}
	 ]]>
	</select>
</mapper>
