<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyBusiDutyDao">


<!-- 保单险种责任信息查询接口 -->	
	<select id="queryPolicyBusiDutyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
				A.PRODUCT_ID,
				A.AMOUNT,
				A.VALIDATE_DATE,
				A.EXPIRY_DATE,
				A.STD_PREM_AF AS TOTAL_PREM_AF,
				B.PRODUCT_NAME,
				(SELECT C.FIELD1 FROM DEV_PAS.T_CONTRACT_PRODUCT_OTHER C WHERE C.ITEM_ID = A.ITEM_ID) as FIELD1,
				COALESCE((select tcbp.old_pol_no from dev_pas.t_contract_busi_prod tcbp where tcbp.busi_item_id = a.busi_item_id), a.busi_item_id||'', '') as POL_NO,
				A.PRODUCT_CODE,
				(SELECT D.PRODUCT_CODE_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT D WHERE D.BUSINESS_PRD_ID = B.BUSINESS_PRD_ID) as RISK_CODE,
				A.BUSI_ITEM_ID as RISK_ID
			FROM 
				APP___PAS__DBUSER.T_CONTRACT_PRODUCT A, 
				APP___PAS__DBUSER.T_PRODUCT_LIFE B
			WHERE POLICY_CODE = #{policy_code} AND A.PRODUCT_ID = B.PRODUCT_ID 
		 ]]>
	</select>
	


</mapper>
