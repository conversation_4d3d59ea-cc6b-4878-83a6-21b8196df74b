<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICashvalueDao">

	<sql id="cashvalueWhereCondition">
		<if test=" context_of_use != null and context_of_use != ''  "><![CDATA[ AND A.CONTEXT_OF_USE = #{context_of_use} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" source_system != null and source_system != ''  "><![CDATA[ AND A.SOURCE_SYSTEM = #{source_system} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" field_9 != null and field_9 != ''  "><![CDATA[ AND A.FIELD_9 = #{field_9} ]]></if>
		<if test=" field_7 != null and field_7 != ''  "><![CDATA[ AND A.FIELD_7 = #{field_7} ]]></if>
		<if test=" field_8 != null and field_8 != ''  "><![CDATA[ AND A.FIELD_8 = #{field_8} ]]></if>
		<if test=" field_5 != null and field_5 != ''  "><![CDATA[ AND A.FIELD_5 = #{field_5} ]]></if>
		<if test=" field_6 != null and field_6 != ''  "><![CDATA[ AND A.FIELD_6 = #{field_6} ]]></if>
		<if test=" calc_status != null and calc_status != ''  "><![CDATA[ AND A.CALC_STATUS = #{calc_status} ]]></if>
		<if test=" cal_date  != null  and  cal_date  != ''  "><![CDATA[ AND A.CAL_DATE = #{cal_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" field_10 != null and field_10 != ''  "><![CDATA[ AND A.FIELD_10 = #{field_10} ]]></if>
		<if test=" field_4 != null and field_4 != ''  "><![CDATA[ AND A.FIELD_4 = #{field_4} ]]></if>
		<if test=" field_3 != null and field_3 != ''  "><![CDATA[ AND A.FIELD_3 = #{field_3} ]]></if>
		<if test=" field_2 != null and field_2 != ''  "><![CDATA[ AND A.FIELD_2 = #{field_2} ]]></if>
		<if test=" field_1 != null and field_1 != ''  "><![CDATA[ AND A.FIELD_1 = #{field_1} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCashvalueByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryCashvalueByBusiItemIdCondition">
		<if test=" busi_item_id != null and busi_item_id != '' "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="queryCashvalueByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="queryCashvalueByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryCashvalueByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCashvalue"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CASH_VALUE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CASHVALUE(
				CONTEXT_OF_USE, BUSI_PROD_CODE, INSERT_TIMESTAMP, SOURCE_SYSTEM, UPDATE_BY, CASH_VALUE, LIST_ID, 
				BUSI_ITEM_ID, POLICY_ID, FIELD_9, FIELD_7, FIELD_8, FIELD_5, FIELD_6, 
				INSERT_TIME, CALC_STATUS, CAL_DATE, UPDATE_TIME, POLICY_CODE, FIELD_10, FIELD_4, 
				FIELD_3, FIELD_2, UPDATE_TIMESTAMP, FIELD_1, INSERT_BY ) 
			VALUES (
				#{context_of_use, jdbcType=VARCHAR}, #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{source_system, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{cash_value, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{field_9, jdbcType=VARCHAR} , #{field_7, jdbcType=VARCHAR} , #{field_8, jdbcType=VARCHAR} , #{field_5, jdbcType=VARCHAR} , #{field_6, jdbcType=VARCHAR} 
				, SYSDATE , #{calc_status, jdbcType=VARCHAR} , #{cal_date, jdbcType=DATE} , SYSDATE , #{policy_code, jdbcType=VARCHAR} , #{field_10, jdbcType=VARCHAR} , #{field_4, jdbcType=VARCHAR} 
				, #{field_3, jdbcType=VARCHAR} , #{field_2, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{field_1, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCashvalue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CASHVALUE WHERE  LIST_ID = #{list_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCashvalue" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CASHVALUE ]]>
		<set>
		<trim suffixOverrides=",">
			CONTEXT_OF_USE = #{context_of_use, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			SOURCE_SYSTEM = #{source_system, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			BUSI_ITEM_ID = #{busi_item_id, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			FIELD_9 = #{field_9, jdbcType=VARCHAR} ,
			FIELD_7 = #{field_7, jdbcType=VARCHAR} ,
			FIELD_8 = #{field_8, jdbcType=VARCHAR} ,
			FIELD_5 = #{field_5, jdbcType=VARCHAR} ,
			FIELD_6 = #{field_6, jdbcType=VARCHAR} ,
			CALC_STATUS = #{calc_status, jdbcType=VARCHAR} ,
		    CAL_DATE = #{cal_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			FIELD_10 = #{field_10, jdbcType=VARCHAR} ,
			FIELD_4 = #{field_4, jdbcType=VARCHAR} ,
			FIELD_3 = #{field_3, jdbcType=VARCHAR} ,
			FIELD_2 = #{field_2, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			FIELD_1 = #{field_1, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCashvalueByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>
	
	<select id="findCashvalueByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>
	
	<select id="findCashvalueByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>
	
	<select id="findCashvalueByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>
	
	<select id="findCashvalueByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCashvalue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllCashvalue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>
	
	<!-- 现价计算查询所有操作 -->
	<select id="PA_ALLfindAllCashvalue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ARE.* FROM (SELECT A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="cashvalueWhereCondition" />
		<![CDATA[	
		) ARE 
		WHERE 1=1 AND MOD(ARE.LIST_ID, #{modnum})= #{start} AND ROWNUM < 5000
		]]>
	</select>

<!-- 查询个数操作 -->
	<select id="findCashvalueTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE 1 = 1  ]]>
		<include refid="cashvalueWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCashvalueForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CONTEXT_OF_USE, B.BUSI_PROD_CODE, B.SOURCE_SYSTEM, B.CASH_VALUE, B.LIST_ID, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.FIELD_9, B.FIELD_7, B.FIELD_8, B.FIELD_5, B.FIELD_6, 
			B.CALC_STATUS, B.CAL_DATE, B.POLICY_CODE, B.FIELD_10, B.FIELD_4, 
			B.FIELD_3, B.FIELD_2, B.FIELD_1 FROM (
					SELECT ROWNUM RN, A.CONTEXT_OF_USE, A.BUSI_PROD_CODE, A.SOURCE_SYSTEM, A.CASH_VALUE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD_9, A.FIELD_7, A.FIELD_8, A.FIELD_5, A.FIELD_6, 
			A.CALC_STATUS, A.CAL_DATE, A.POLICY_CODE, A.FIELD_10, A.FIELD_4, 
			A.FIELD_3, A.FIELD_2, A.FIELD_1 FROM APP___PAS__DBUSER.T_CASHVALUE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="cashvalueWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
