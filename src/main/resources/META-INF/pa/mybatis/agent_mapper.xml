<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAgentDao">

	<sql id="PA_agentWhereCondition">
		<if test=" employee_flag  != null "><![CDATA[ AND A.EMPLOYEE_FLAG = #{employee_flag} ]]></if>
		<if test=" agent_mobile != null and agent_mobile != ''  "><![CDATA[ AND A.AGENT_MOBILE = #{agent_mobile} ]]></if>
		<if test=" staff_code != null and staff_code != ''  "><![CDATA[ AND A.STAFF_CODE = #{staff_code} ]]></if>
		<if test=" agent_normal_type  != null "><![CDATA[ AND A.AGENT_NORMAL_TYPE = #{agent_normal_type} ]]></if>
		<if test=" agent_sales_organ_code != null and agent_sales_organ_code != ''  "><![CDATA[ AND A.AGENT_SALES_ORGAN_CODE = #{agent_sales_organ_code} ]]></if>
		<if test=" postal_address != null and postal_address != ''  "><![CDATA[ AND A.POSTAL_ADDRESS = #{postal_address} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" agent_gender  != null "><![CDATA[ AND A.AGENT_GENDER = #{agent_gender} ]]></if>
		<if test=" agent_level  != null "><![CDATA[ AND A.AGENT_LEVEL = #{agent_level} ]]></if>
		<if test=" agent_organ_code != null and agent_organ_code != ''  "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
		<if test=" agent_status  != null "><![CDATA[ AND A.AGENT_STATUS = #{agent_status} ]]></if>
		<if test=" sales_organ_code != null and sales_organ_code != ''  "><![CDATA[ AND A.SALES_ORGAN_CODE = #{sales_organ_code} ]]></if>
		<if test=" dismissal_date  != null  and  dismissal_date  != ''  "><![CDATA[ AND A.DISMISSAL_DATE = #{dismissal_date} ]]></if>
		<if test=" birthday  != null  and  birthday  != ''  "><![CDATA[ AND A.BIRTHDAY = #{birthday} ]]></if>
		<if test=" sgent_horner_level  != null "><![CDATA[ AND A.SGENT_HORNER_LEVEL = #{sgent_horner_level} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" agent_phone != null and agent_phone != ''  "><![CDATA[ AND A.AGENT_PHONE = #{agent_phone} ]]></if>
		<if test=" agent_email != null and agent_email != ''  "><![CDATA[ AND A.AGENT_EMAIL = #{agent_email} ]]></if>
<!-- 		<if test=" agent_channel != null and agent_channel != ''  "><![CDATA[ AND A.AGENT_CHANNEL = #{agent_channel} ]]></if> -->
		<if test=" cert_type != null and cert_type != ''  "><![CDATA[ AND A.CERT_TYPE = #{cert_type} ]]></if>
		<if test=" home_address != null and home_address != ''  "><![CDATA[ AND A.HOME_ADDRESS = #{home_address} ]]></if>
		<if test=" agent_star_flag  != null "><![CDATA[ AND A.AGENT_STAR_FLAG = #{agent_star_flag} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" employment_date  != null  and  employment_date  != ''  "><![CDATA[ AND A.EMPLOYMENT_DATE = #{employment_date} ]]></if>
		<if test=" user_code != null and user_code != ''  "><![CDATA[ AND A.USER_CODE = #{user_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryAgentByAgentCodeCondition">
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addAgent"  useGeneratedKeys="false" parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_AGENT(
				EMPLOYEE_FLAG, AGENT_MOBILE,  AGENT_NORMAL_TYPE, INSERT_TIMESTAMP,  POSTAL_ADDRESS, 
				CERTI_CODE, UPDATE_BY, AGENT_GENDER, AGENT_LEVEL, AGENT_ORGAN_CODE, AGENT_STATUS, SALES_ORGAN_CODE, 
				DISMISSAL_DATE, BIRTHDAY, SGENT_HORNER_LEVEL, AGENT_NAME, INSERT_TIME, UPDATE_TIME, AGENT_PHONE, 
				AGENT_EMAIL,
				 CERT_TYPE, HOME_ADDRESS,  UPDATE_TIMESTAMP, INSERT_BY, 
				AGENT_CODE, EMPLOYMENT_DATE,AGENT_CHANNEL ) 
			VALUES (
				#{employee_flag, jdbcType=NUMERIC}, #{agent_mobile, jdbcType=VARCHAR} ,  #{agent_normal_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP,  #{postal_address, jdbcType=VARCHAR} 
				, #{certi_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{agent_gender, jdbcType=NUMERIC} , #{agent_level, jdbcType=VARCHAR} , #{agent_organ_code, jdbcType=VARCHAR} , #{agent_status, jdbcType=NUMERIC} , #{sales_organ_code, jdbcType=VARCHAR} 
				, #{dismissal_date, jdbcType=DATE} , #{birthday, jdbcType=DATE} , #{sgent_horner_level, jdbcType=NUMERIC} , #{agent_name, jdbcType=VARCHAR} , SYSDATE , SYSDATE , #{agent_phone, jdbcType=VARCHAR} 
				, #{agent_email, jdbcType=VARCHAR} , #{cert_type, jdbcType=VARCHAR} , #{home_address, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{agent_code, jdbcType=VARCHAR} , #{employment_date, jdbcType=DATE},#{agent_channel, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteAgent" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_AGENT WHERE AGENT_CODE = #{agent_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateAgent" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_AGENT ]]>
		<set>
		<trim suffixOverrides=",">
		    EMPLOYEE_FLAG = #{employee_flag, jdbcType=NUMERIC} ,
			AGENT_MOBILE = #{agent_mobile, jdbcType=VARCHAR} ,
		    AGENT_NORMAL_TYPE = #{agent_normal_type, jdbcType=NUMERIC} ,
			POSTAL_ADDRESS = #{postal_address, jdbcType=VARCHAR} ,
			CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    AGENT_GENDER = #{agent_gender, jdbcType=NUMERIC} ,
		    AGENT_LEVEL = #{agent_level, jdbcType=VARCHAR} ,
			AGENT_ORGAN_CODE = #{agent_organ_code, jdbcType=VARCHAR} ,
		    AGENT_STATUS = #{agent_status, jdbcType=NUMERIC} ,
			SALES_ORGAN_CODE = #{sales_organ_code, jdbcType=VARCHAR} ,
		    DISMISSAL_DATE = #{dismissal_date, jdbcType=DATE} ,
		    BIRTHDAY = #{birthday, jdbcType=DATE} ,
		    SGENT_HORNER_LEVEL = #{sgent_horner_level, jdbcType=NUMERIC} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			AGENT_PHONE = #{agent_phone, jdbcType=VARCHAR} ,
			AGENT_EMAIL = #{agent_email, jdbcType=VARCHAR} ,
			AGENT_CHANNEL=#{agent_channel, jdbcType=VARCHAR},
			CERT_TYPE = #{cert_type, jdbcType=VARCHAR} ,
			HOME_ADDRESS = #{home_address, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    EMPLOYMENT_DATE = #{employment_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE AGENT_CODE = #{agent_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findAgentByAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE,  A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL, A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryAgentByAgentCodeCondition" />
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>
<!-- 按索引查询操作 -->
	<select id="PA_findAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL, A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1  ]]>
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<include refid="PA_agentWhereCondition" />
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>	
	<!-- 根据五要素查询北分业务员 -->
	<select id="findNaturalAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL, A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1  ]]>
		<![CDATA[ AND A.AGENT_ORGAN_CODE like '8621%' and A.agent_status in ('1', '2', '3')]]>
		<include refid="PA_agentWhereCondition" />
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="PA_findAllMapAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE,  A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL, A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE, 
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.AGENT_NORMAL_TYPE,  A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="PA_agentWhereCondition" />
		<![CDATA[ ORDER BY A.AGENT_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findAgentTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryAgentForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.EMPLOYEE_FLAG, B.AGENT_MOBILE, B.STAFF_CODE, B.AGENT_NORMAL_TYPE, B.AGENT_SALES_ORGAN_CODE, B.POSTAL_ADDRESS, 
			B.CERTI_CODE, B.AGENT_GENDER, B.AGENT_LEVEL, B.AGENT_ORGAN_CODE, B.AGENT_STATUS, B.SALES_ORGAN_CODE, 
			B.DISMISSAL_DATE, B.BIRTHDAY, B.SGENT_HORNER_LEVEL, B.AGENT_NAME, B.AGENT_PHONE, 
			B.AGENT_EMAIL,  B.CERT_TYPE, B.HOME_ADDRESS, B.AGENT_STAR_FLAG, 
			B.AGENT_CODE, B.EMPLOYMENT_DATE,B.AGENT_CHANNEL FROM (
					SELECT ROWNUM RN, A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.STAFF_CODE, A.AGENT_NORMAL_TYPE, A.AGENT_SALES_ORGAN_CODE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS, A.AGENT_STAR_FLAG, 
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.AGENT_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findBankBranchAgent" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,
			   A.BANK_BRANCH_CODE,
		       A.AGENT_CODE,
		       A.Start_Date,
		       A.END_DATE,
		       A.RATE,
		       A.UPDATE_BY
       			FROM APP___PAS__DBUSER.T_BANK_BRANCH_AGENT     A WHERE 1 = 1  ]]>
		<if test=" bank_branch_code != null and bank_branch_code != '' "><![CDATA[ AND A.BANK_BRANCH_CODE = #{bank_branch_code} ]]></if>
		<if test=" agent_code != null and service_handler_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="PA_findAllAgentByMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 ]]>
			<if test=" agent_status != null and agent_status != '' "><![CDATA[ AND A.AGENT_STATUS = #{agent_status} ]]></if>
			<if test=" agent_organ_code != null and agent_organ_code != '' "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
			<![CDATA[AND A.AGENT_MOBILE = #{agent_mobile}]]>
	</select>
	
	<select id="PA_findAllAgentByMobilePhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		     SELECT TA.AGENT_CODE , TA.AGENT_NAME, TA.AGENT_GENDER, TA.BIRTHDAY, TA.CERT_TYPE, TA.CERTI_CODE
		       FROM APP___PAS__DBUSER.T_AGENT TA 
		      WHERE TA.AGENT_STATUS IN ('1','2') 
		        AND TA.AGENT_CHANNEL IN ('01','02','03') 
		        ]]>
			<if test=" agent_mobile != null and agent_mobile != '' "><![CDATA[ AND TA.AGENT_MOBILE = #{agent_mobile} ]]></if>
			<if test=" agent_phone != null and agent_phone != '' "><![CDATA[ AND TA.AGENT_PHONE = #{agent_phone} ]]></if>
	</select>
	
	<select id="findCheckAgentByMobile" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
		A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
		A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
		A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
		A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 AND A.AGENT_STATUS IN (1,2)]]>
		<if test=" agent_organ_code != null and agent_organ_code != '' "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
		<![CDATA[AND A.AGENT_MOBILE = #{agent_mobile}]]>
	</select>
	
	<select id="findAllAgentByPhone" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
		A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
		A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
		A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
		A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 AND A.AGENT_STATUS IN (1,2)]]>
		<if test=" agent_organ_code != null and agent_organ_code != '' "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
		<![CDATA[AND A.AGENT_PHONE = #{agent_mobile}]]>
	</select>
	<!-- 查询本保单四级机构在职业务员/收费员 -->
	<!-- 总条件 原来有这个A.AGENT_LEVEL = 2 AND，调bug暂时  去掉，并且agent_level取值没有2的，只有A1:三星级,A2:四星级,A3:五星级 -->
	<select id="findLevelFourOnJobAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  SELECT T.AGENT_NAME,
         T.AGENT_CODE,
         T.AGENT_MOBILE,
         T.AGENT_PHONE,
         T.CERTI_CODE,
         T.AGENT_ORGAN_CODE AS ORGAN_CODE,
         (SELECT D.ORGAN_NAME
            FROM APP___PAS__DBUSER.T_UDMP_ORG D
           WHERE D.ORGAN_CODE = T.AGENT_ORGAN_CODE) AS ORGAN_NAME
    FROM APP___PAS__DBUSER.T_AGENT T
   WHERE 1 = 1
     AND T.AGENT_STATUS = 1
     AND (T.AGENT_MOBILE = #{agent_phone} OR T.AGENT_PHONE = #{agent_phone}) 
     AND T.CERTI_CODE <>
         (SELECT C.CUSTOMER_CERTI_CODE
            FROM APP___PAS__DBUSER.T_POLICY_HOLDER A, APP___PAS__DBUSER.T_CUSTOMER C
           WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
             AND A.POLICY_CODE = #{policy_code}
             AND ROWNUM = 1)
     AND EXISTS (SELECT *
            FROM APP___PAS__DBUSER.T_CONTRACT_MASTER M
           WHERE T.AGENT_ORGAN_CODE LIKE M.ORGAN_CODE || '%'
             AND M.POLICY_CODE = #{policy_code})
			]]>
	</select>
	<!-- 查询本保单投保人姓名，证件号-->
	<select id="findNameAndCertiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT A.CUSTOMER_NAME,A.CUSTOMER_CERTI_CODE,B.POLICY_CODE
				  FROM APP___PAS__DBUSER.T_CUSTOMER A
				  JOIN APP___PAS__DBUSER.T_POLICY_HOLDER B
				    ON A.CUSTOMER_ID = B.CUSTOMER_ID
				 WHERE B.POLICY_CODE = #{policy_code}
			]]>
	</select>
	
	<!-- 查询业务员电话与否与投保人电话一致 -->
	<select id="findAgentMobileCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1) from dev_pas.t_agent ta where ta.agent_code = #{agent_code} and ta.agent_mobile = #{agent_mobile}
			 ]]>
	</select>
	
	<!-- 查询受理页面的代办人信息 rexd -->
	<select id="queryAcceptAgentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE,  A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL, A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE, 
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL,B.BANK_BRANCH_CODE BANK_BRANCH_CODE,
			B.BANK_BRANCH_NAME BANK_BRANCH_NAME 
			FROM DEV_PAS.T_AGENT A LEFT JOIN DEV_PAS.T_BANK_BRANCH B 
            ON A.AGENT_CODE = B.SALE_PERSON_CODE
            WHERE A.AGENT_CODE = #{agent_code}
			 ]]>
	</select>
	
	<!--查询当前代理人所属组-->
	<select id="PA_findSalesOrgan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT TSO.ORGAN_LEVEL_CODE, TSO.SALES_ORGAN_CODE, TSO.PARENT_CODE
       FROM APP___PAS__DBUSER.t_Agent       TA,
            APP___PAS__DBUSER.t_Sales_Organ TSO
      WHERE 1 = 1
        AND TA.SALES_ORGAN_CODE = TSO.SALES_ORGAN_CODE
			 ]]>
		<if test=" sales_organ_code != null and sales_organ_code != '' "><![CDATA[ AND TA.SALES_ORGAN_CODE = #{sales_organ_code} ]]></if>
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND TA.AGENT_CODE = #{agent_code} ]]></if>	 
	</select>
	<!--查询当前代理人所属部和区-->
	<select id="PA_findSalesOrganByParentCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT TSO.ORGAN_LEVEL_CODE, TSO.SALES_ORGAN_CODE, TSO.PARENT_CODE
          FROM DEV_PAS.t_Sales_Organ TSO
         WHERE 1 = 1
			 ]]>
		<if test=" sales_organ_code != null and sales_organ_code != '' "><![CDATA[ AND TSO.SALES_ORGAN_CODE = #{sales_organ_code} ]]></if>
	</select>
	
    <!--查询业务员所在的营业部  -->
	<select id="findAgentPO" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT T.AGENT_CODE,
		       decode(to_char(BU.ORGAN_LEVEL_CODE),
		              '1',
		              BU.SALES_ORGAN_CODE,
		              '2',
		              BU.Parent_Code,
		              '3',
		              QU.PARENT_CODE,
		              '') AGENT_AREA,
		       decode(to_char(BU.ORGAN_LEVEL_CODE),
		              '1',
		              BU.SALES_ORGAN_NAME,
		              '2',
		              QU.SALES_ORGAN_NAME,
		              '3',
		              RU.SALES_ORGAN_NAME,
		              '') AGENT_AREA_NAME,
		       decode(to_char(BU.ORGAN_LEVEL_CODE),
		              '2',
		              BU.SALES_ORGAN_CODE,
		              '3',
		              BU.Parent_Code,
		              '') AGENT_PART,
		       decode(to_char(BU.ORGAN_LEVEL_CODE),
		              '2',
		              BU.SALES_ORGAN_NAME,
		              '3',
		              QU.SALES_ORGAN_NAME,
		              '') AGENT_PART_NAME,
		       decode(to_char(BU.ORGAN_LEVEL_CODE), '3', BU.SALES_ORGAN_CODE, '') AGENT_GROUP,
		       decode(to_char(BU.ORGAN_LEVEL_CODE), '3', BU.SALES_ORGAN_NAME, '') AGENT_GROUP_NAME,
		       UM.ORGAN_NAME
		  FROM APP___PAS__DBUSER.T_AGENT T
		  LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN BU
		    ON T.SALES_ORGAN_CODE = BU.SALES_ORGAN_CODE
		  LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN QU
		    ON BU.PARENT_CODE = QU.SALES_ORGAN_CODE
		  LEFT JOIN APP___PAS__DBUSER.T_SALES_ORGAN RU
		    ON QU.PARENT_CODE = RU.SALES_ORGAN_CODE
		  LEFT JOIN APP___PAS__DBUSER.T_UDMP_ORG UM
            ON T.AGENT_ORGAN_CODE = UM.ORGAN_CODE
		WHERE T.AGENT_CODE =  #{agent_code}  
	</select>
	
	<select id="findAgentOfSameMobileAndPhone" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.AGENT_NORMAL_TYPE,  A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 AND A.AGENT_STATUS IN (1,2)]]>
		<if test=" agent_organ_code != null and agent_organ_code != '' "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
		<if test=" agent_phone != null and agent_phone != ''  "><![CDATA[ AND A.AGENT_PHONE = #{agent_phone} ]]></if>
		<if test=" agent_mobile != null and agent_mobile != ''  "><![CDATA[ AND A.AGENT_MOBILE = #{agent_mobile} ]]></if>
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE <> #{agent_code} ]]></if>	
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>
	
	<select id="judgePolicyHolderOrInsuredIsAgent" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
		 select ag.agent_code,ag.agent_name,ag.agent_gender,ag.birthday,ag.cert_type,ag.certi_code from dev_pas.t_agent ag where  exists  (
		     select * from (
			      select ph.customer_id,cus.customer_name,cus.customer_gender,cus.customer_birthday,cus.customer_cert_type,cus.customer_certi_code
			      from dev_pas.t_policy_holder ph ,dev_pas.t_customer cus 
			      where ph.customer_id = cus.customer_id
			       and  ph.policy_code = #{policy_code}
			      union 
			      select il.customer_id,cus.customer_name,cus.customer_gender,cus.customer_birthday,cus.customer_cert_type,cus.customer_certi_code
			       from dev_pas.t_insured_list il ,dev_pas.t_customer cus 
			      where il.customer_id = cus.customer_id
			       and  il.policy_code = #{policy_code} 
		      ) pil where pil.customer_name = ag.agent_name  
	              and pil.customer_gender = ag.agent_gender
	              and pil.customer_birthday = ag.birthday 
	              and pil.customer_certi_code = ag.certi_code 
	              and (   (ag.cert_type <> null and  pil.customer_cert_type = ag.cert_type )
	              		   or 1=1
	              	   )
	    
	       )
	]]>
	</select>
	
	<select id="PA_findAllAgentByMobile2740" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A 
			WHERE 1=1 and A.agent_status in (1, 2) ]]> 
			<if test=" agent_organ_code != null and agent_organ_code != '' "><![CDATA[ AND A.agent_organ_code = #{agent_organ_code} ]]></if>
			<if test=" agent_phone != null and agent_phone != '' "><![CDATA[ AND A.AGENT_PHONE = #{agent_phone} ]]></if>
			<if test=" agent_mobile != null and agent_mobile != '' "><![CDATA[ AND A.AGENT_MOBILE = #{agent_mobile} ]]></if>
			
	</select>
	
	<!-- 双录互保件查询 -->
	<select id="PA_findSLHBAllAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.AGENT_NORMAL_TYPE,  A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE ROWNUM <=  1000  AND A.AGENT_STATUS <> '4']]>
		<if test='cert_type == "b" ' ><![CDATA[ AND A.CERT_TYPE IN ('b','c','d','i') ]]></if>
		<if test='cert_type == "0" ' ><![CDATA[ AND A.CERT_TYPE IN ('0','5','8') ]]></if>
		<if test='cert_type != "b" and cert_type != "0" '><![CDATA[ AND A.CERT_TYPE = #{cert_type} ]]></if>
		<if test=" agent_gender != null and agent_gender != '' "><![CDATA[ AND A.AGENT_GENDER = #{agent_gender} ]]></if>
		<if test=" birthday != null and birthday != '' "><![CDATA[ AND A.BIRTHDAY = #{birthday} ]]></if>
		<if test=" agent_organ_code != null and agent_organ_code != '' "><![CDATA[ AND A.AGENT_ORGAN_CODE LIKE '${agent_organ_code}%' ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[  AND A.CERTI_CODE = #{certi_code} ]]></if>
		<![CDATA[ ORDER BY A.AGENT_CODE ]]> 
	</select>
	
	
     <!-- 根据保单号查询当前代理人信息 -->
	 <select id="PA_findAgentByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL, A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1  ]]>
		<if test=" policy_code != null and policy_code != '' ">
			<![CDATA[ AND EXISTS (SELECT 1
			          FROM DEV_PAS.T_CONTRACT_AGENT TA
			         WHERE TA.AGENT_CODE = A.AGENT_CODE
			           AND TA.IS_CURRENT_AGENT = 1
			           AND TA.POLICY_CODE = #{policy_code})]]>
		</if>
		
	</select>
	<!-- 根据五要素查询北京分公司的在职业务员数量通用 -->
	<select id="queryAgentCountByElement" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				select count(1)
  				  from dev_pas.t_agent age, dev_pas.t_cs_customer cc
				 where cc.customer_id in
       				   (select cph.customer_id
  						  from dev_pas.t_cs_policy_holder cph, dev_pas.t_cs_policy_change cpc
 						 where cph.policy_chg_id in
       						   (select cpcz.policy_chg_id
          					      from dev_pas.t_cs_accept_change cac,
               						   dev_pas.t_cs_policy_change cpcz
         					     where cac.accept_id = cpcz.accept_id
           						   and cac.accept_code = #{accept_code})
   								   and cph.old_new = '1'
   								   and cpc.policy_chg_id = cph.policy_chg_id
   								   and cpc.service_code <> 'AE'
				union
				select il.customer_id
  				  from dev_pas.t_cs_insured_list il
 				 where il.policy_chg_id in
       				   (select cpcz.policy_chg_id
          				  from dev_pas.t_cs_accept_change cac,
               				   dev_pas.t_cs_policy_change cpcz
         				 where cac.accept_id = cpcz.accept_id
           				   and cac.accept_code = #{accept_code})
   						   and il.old_new = '1'
				union
				select ccb.customer_id
  				  from dev_pas.t_cs_contract_bene ccb, dev_pas.t_cs_policy_change cpc
 				 where ccb.policy_chg_id in
       				   (select cpcz.policy_chg_id
          				  from dev_pas.t_cs_accept_change cac,
               				   dev_pas.t_cs_policy_change cpcz
         				 where cac.accept_id = cpcz.accept_id
           				   and cac.accept_code = #{accept_code})
                   and cpc.policy_chg_id = ccb.policy_chg_id
   				   and cpc.service_code <> 'BC')
   				   
  				   and cc.customer_name = age.agent_name
   				   and cc.customer_gender = age.agent_gender
   				   and cc.customer_birthday = age.birthday
  				   and cc.customer_cert_type = age.cert_type
   				   and cc.customer_certi_code = age.certi_code
   				   and age.agent_organ_code like '8621%'
   				   and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	
	<!-- 根据五要素查询北京分公司的在职业务员数量(投保人/受益人变更) -->
	<select id="queryAgentCountByElementAEorBC" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1)
					from dev_pas.t_agent age, dev_pas.t_cs_customer cc
 				   where cc.customer_id in
       			 		 (select cph.customer_id
             				from dev_pas.t_cs_policy_holder cph,
               					 dev_pas.t_cs_policy_change cpc
         				   where cph.policy_chg_id in
               			 		 (select cpcz.policy_chg_id
                  					from dev_pas.t_cs_accept_change cac,
                       					 dev_pas.t_cs_policy_change cpcz
                 				   where cac.accept_id = cpcz.accept_id
                   					 and cac.accept_code = #{accept_code})
           							 and cph.old_new = '1'
           							 and cpc.policy_chg_id = cph.policy_chg_id
           							 and cpc.service_code = 'AE'
        		union
        		select ccb.customer_id
          		  from dev_pas.t_cs_contract_bene ccb,
               		   dev_pas.t_cs_policy_change cpc
         		 where ccb.policy_chg_id in
               		   (select cpcz.policy_chg_id
                  		  from dev_pas.t_cs_accept_change cac,
                       		   dev_pas.t_cs_policy_change cpcz
                 		 where cac.accept_id = cpcz.accept_id
                   		   and cac.accept_code = #{accept_code})
           				   and cpc.policy_chg_id = ccb.policy_chg_id
           				   and cpc.service_code = 'BC')
   				  and cc.customer_name = age.agent_name
   				  and cc.customer_gender = age.agent_gender
   				  and cc.customer_birthday = age.birthday
  			      and cc.customer_cert_type = age.cert_type
   				  and cc.customer_certi_code = age.certi_code
   				  and age.agent_organ_code like '8621%'
   				  and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	<!-- 根据五要素查询在职或二次增员业务员 -->
	<select id = "PA_queryAgentCountsByFiveElements" resultType="java.lang.Integer" parameterType="java.util.Map">
	<![CDATA[ SELECT COUNT(1) FROM dev_pas.t_agent t WHERE t.agent_status IN ('1','2') 
  AND t.agent_name = #{agent_name}
  AND t.agent_gender = #{agent_gender}
  AND t.birthday = #{birthday}
  AND t.certi_code = #{certi_code}
  AND t.cert_type = #{cert_type} 
  and t.agent_code <> #{ agent_code}]]>
	</select>
	<select id="PA_queryAgentListsByFiveElements" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		 select tc.customer_name as agent_name,tc.customer_certi_code as certi_code,tc.customer_cert_type as cert_type,tc.customer_gender as agent_gender,tc.customer_birthday as birthday 
	        from dev_pas.t_cs_policy_holder tph  left join dev_pas.t_customer tc on tph.customer_id=tc.customer_id 
	    
	       where 1=1 and tph.old_new='1' and tph.operation_type <> '3']]>
	       
	       <if test="policy_chg_ids !=null and policy_chg_ids != '' ">	       
	       		<![CDATA[ and tph.policy_chg_id in ]]>
	       		<foreach collection="policy_chg_ids" item="policy_chg_id"
					index="index" open="(" close=")" separator=",">#{policy_chg_id}
				</foreach>
	       </if>
	       <if test="operation_type !=null and operation_type != '' ">
	       		<![CDATA[ and tph.operation_type  = #{operation_type} ]]>
	       </if>
	       <![CDATA[
	       and exists (
	       
	       select 1 from dev_pas.t_agent ta where ta.agent_status=1 and ta.agent_organ_code like '8621%' 
	       and ta.agent_name=tc.customer_name 
	       and ta.agent_gender=tc.customer_gender
	       and ta.birthday=tc.customer_birthday
	       and ta.cert_type =tc.customer_cert_type
	       and ta.certi_code=tc.customer_certi_code
	       and ta.agent_status in ('1', '2', '3')
	       
	       )
	       union 
	        select tc.customer_name as agent_name,tc.customer_certi_code as certi_code,tc.customer_cert_type as cert_type,tc.customer_gender as agent_gender,tc.customer_birthday as birthday  from dev_pas.t_cs_insured_list til  left join dev_pas.t_customer tc on til.customer_id=tc.customer_id 
	       where 1=1 and til.old_new='1' and til.operation_type <> '3']]>
	      	
	       <if test="policy_chg_ids !=null and policy_chg_ids != '' ">
	       		<![CDATA[ and til.policy_chg_id in ]]>
	       		<foreach collection="policy_chg_ids" item="policy_chg_id"
					index="index" open="(" close=")" separator=",">#{policy_chg_id}
				</foreach>
	       </if>
	       <if test="operation_type !=null and operation_type != '' ">
	       		<![CDATA[ and til.operation_type  = #{operation_type} ]]>
	       </if>
	       <![CDATA[
	       and exists (
	       
	       select 1 from dev_pas.t_agent ta where ta.agent_status=1 and ta.agent_organ_code like '8621%' 
	       and ta.agent_name=tc.customer_name 
	       and ta.agent_gender=tc.customer_gender
	       and ta.birthday=tc.customer_birthday
	       and ta.cert_type =tc.customer_cert_type
	       and ta.certi_code=tc.customer_certi_code
	       and ta.agent_status in ('1', '2', '3')
	       )
	       union 
	        select tc.customer_name as agent_name,tc.customer_certi_code as certi_code,tc.customer_cert_type as cert_type,tc.customer_gender as agent_gender,tc.customer_birthday as birthday  from dev_pas.t_cs_contract_bene tcb  left join dev_pas.t_customer tc on tcb.customer_id=tc.customer_id 
	       where 1=1 and tcb.old_new='1' and tcb.operation_type <> '3']]>
	       
	       <if test="policy_chg_ids !=null and policy_chg_ids != '' ">
	       		<![CDATA[ and tcb.policy_chg_id in ]]>
	       		<foreach collection="policy_chg_ids" item="policy_chg_id"
					index="index" open="(" close=")" separator=",">#{policy_chg_id}
				</foreach>
	       </if>
	       <if test="operation_type !=null and operation_type != '' ">
	       		<![CDATA[ and tcb.operation_type  = #{operation_type} ]]>
	       </if>
	       <![CDATA[
	       and exists (
	       
	       select 1 from dev_pas.t_agent ta where ta.agent_status=1 and ta.agent_organ_code like '8621%' 
	       and ta.agent_name=tc.customer_name 
	       and ta.agent_gender=tc.customer_gender
	       and ta.birthday=tc.customer_birthday
	       and ta.cert_type =tc.customer_cert_type
	       and ta.certi_code=tc.customer_certi_code
	       and ta.agent_status in ('1', '2', '3')
	       )
	       union
			select tc.name       as agent_name,
       			tc.certi_code as certi_code,
       			tc.cert_type  as cert_type,
       			tc.gender     as agent_gender,
       			tc.birthday   as birthday
  			from dev_pas.T_NATURAL_YEAR_INCOME tc
 			where 1 = 1]]>
	       <if test="accept_id !=null and accept_id != '' ">
	       		<![CDATA[ and tc.accept_id = #{accept_id}]]>
	       </if>
		<![CDATA[
	       and exists (
	       select 1 from dev_pas.t_agent ta where ta.agent_status=1 and ta.agent_organ_code like '8621%' 
	       and ta.agent_name=tc.name 
	       and ta.agent_gender=tc.gender
	       and ta.birthday=tc.birthday
	       and ta.cert_type =tc.cert_type
	       and ta.certi_code=tc.certi_code
	       and ta.agent_status in ('1', '2', '3')
	       )
			         ]]>
	</select>
	<select id="queryAgentMsg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.AGENT_NAME, T.AGENT_GENDER, T.BIRTHDAY, T.CERTI_CODE, T.CERT_TYPE
  				FROM DEV_PAS.T_AGENT T
 				WHERE T.AGENT_CODE = #{agent_code}
		]]>
	</select>
	<select id="queryAgentCountByPolicyChgId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
  				from dev_pas.t_agent age, dev_pas.t_cs_customer cc,dev_pas.t_cs_policy_change tpc
 					where cc.customer_id in
       					(select ph.customer_id
          					from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_policy_holder ph
         					where pc.policy_chg_id = ph.policy_chg_id
           					and ph.old_new = '1'
           					and pc.policy_chg_id = #{policy_chg_id}
        				union
        				select il.customer_id
          					from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_insured_list il
        					where pc.policy_chg_id = il.policy_chg_id
           					and il.old_new = '1'
           					and pc.policy_chg_id = #{policy_chg_id}
        				union
        				select cb.customer_id
          					from dev_pas.t_cs_policy_change pc, dev_pas.t_cs_contract_bene cb
         					where pc.policy_chg_id = cb.policy_chg_id
           					and cb.old_new = '1'
           					and pc.policy_chg_id = #{policy_chg_id})
           		and tpc.change_id = cc.change_id
                and tpc.accept_id = cc.accept_id
                and tpc.policy_chg_id = #{policy_chg_id}
                and cc.old_new = '1'
   				and cc.customer_name = age.agent_name
   				and cc.customer_gender = age.agent_gender
   				and cc.customer_birthday = age.birthday
   				and cc.customer_cert_type = age.cert_type
   				and cc.customer_certi_code = age.certi_code
   				and age.agent_organ_code like '8621%'
   				and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	<!--  -->
	<select id="queryNaturalYearIncomeBC" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
  				from dev_pas.t_agent age, dev_pas.T_NATURAL_YEAR_INCOME tc
 				where 1=1
   				and tc.accept_id = #{accept_id}
   				and tc.name = age.agent_name
   				and tc.gender = age.agent_gender
   				and tc.birthday = age.birthday
   				and tc.cert_type = age.cert_type
   				and tc.certi_code = age.certi_code
   				and age.agent_status in ('1','2','3')
   				and age.agent_organ_code like '8621%'
		]]>
	</select>
	
	<!-- 根据综合查询保全明细服务业务员逻辑查询移动保全业务员-->
	<select id="findYDBQAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT A.AGENT_CODE, A.AGENT_NAME,A.AGENT_MOBILE
                 FROM DEV_PAS.T_AGENT          A,
                      DEV_PAS.T_SALES_ORGAN    TSO1,
                      DEV_PAS.T_SALES_ORGAN    TSO2,
                      DEV_pas.t_contract_agent tca
                WHERE A.AGENT_CODE = TCA.AGENT_CODE
                  AND TSO1.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE
                  AND TSO1.PARENT_CODE = TSO2.SALES_ORGAN_CODE
                  AND A.AGENT_CODE = #{agent_code}
                  AND ROWNUM =1 ]]>
	</select>
	
	<!-- 根据五要素校验是否为在职销售人员 -->
	<select id="queryIsCurrentAgentInfo" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_agent a 
			 where 1 = 1
			   and a.agent_name = #{agent_name}
			   and a.agent_gender = #{agent_gender}
			   and a.birthday = #{birthday}
			   and a.cert_type = #{cert_type}
			   and a.certi_code = #{certi_code}
			   and a.agent_status in ('1','2')
			   and a.agent_organ_code like '8621%'
		]]>
	</select>
	
	
		<!-- 根据五要素校验是否为在职销售人员 -->
	<select id="PA_queryPolicyAgentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select T.AGENT_NAME,
       T.AGENT_CHANNEL,
       T.AGENT_CODE,
       T.AGENT_MOBILE,
       T.AGENT_STATUS,
       T.AGENT_ORGAN_CODE
  from dev_pas.t_agent t
 where t.agent_code in
       (select distinct a.agent_code
          from dev_pas.t_contract_agent a
         where a.policy_code in
               (select c.policy_code
                  from dev_pas.t_contract_master c
                 where c.policy_code in
                       (select l.policy_code
                          from dev_pas.t_insured_list l
                         where l.customer_id =
                               (select a.customer_id
                                  from DEV_PAS.T_CUSTOMER a
                                 where a.customer_name = #{customer_name, jdbcType=VARCHAR}
                                   and a.customer_cert_type =
                                       #{customer_cert_type, jdbcType=VARCHAR}
                                   and a.customer_certi_code =
                                       #{customer_certi_code, jdbcType=VARCHAR}
                                   and a.customer_gender = #{customer_gender, jdbcType=NUMERIC}
                                   and a.customer_birthday =
                                       #{customer_birthday, jdbcType=DATE}))
                   and (c.liability_state = '1' or c.liability_state = '4')
                union
                select c.policy_code
                  from dev_pas.t_contract_master c
                 where c.policy_code in
                       (select l.policy_code
                          from dev_pas.t_policy_holder l
                         where l.customer_id =
                               (select a.customer_id
                                  from DEV_PAS.T_CUSTOMER a
                                 where a.customer_name = #{customer_name, jdbcType=VARCHAR}
                                   and a.customer_cert_type =
                                       #{customer_cert_type, jdbcType=VARCHAR}
                                   and a.customer_certi_code =
                                       #{customer_certi_code, jdbcType=VARCHAR}
                                   and a.customer_gender = #{customer_gender, jdbcType=NUMERIC}
                                   and a.customer_birthday =
                                       #{customer_birthday, jdbcType=DATE}))
                   and (c.liability_state = '1' or c.liability_state = '4'))
           and a.is_current_agent = '1')
		]]>
	</select>
	<!-- 根据五要素和机构查询是否为该机构及其下属机构的业务员 -->
	<select id="PA_findIsAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, 
			A.AGENT_EMAIL, A.CERT_TYPE, A.HOME_ADDRESS, A.USER_CODE,
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1  ]]>
		<![CDATA[ AND A.AGENT_STATUS IN ('1', '2', '3')]]>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" agent_gender  != null "><![CDATA[ AND A.AGENT_GENDER = #{agent_gender} ]]></if>
		<if test=" agent_organ_code != null and agent_organ_code != ''  "><![CDATA[ AND A.AGENT_ORGAN_CODE LIKE '${agent_organ_code}%' ]]></if>
		<if test=" birthday  != null  and  birthday  != ''  "><![CDATA[ AND A.BIRTHDAY = #{birthday} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" cert_type != null and cert_type != ''  "><![CDATA[ AND A.CERT_TYPE = #{cert_type} ]]></if>
	</select>
	
	<!-- 根据五要素查询北京分公司的在职业务员数量 投保人 -->
	<select id="queryInsuranceSelfUWByHolder" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1)
					from dev_pas.t_agent age, dev_pas.t_cs_customer cc
 				   where cc.customer_id in
       			 		 (select cph.customer_id
             				from dev_pas.t_cs_policy_holder cph,
               					 dev_pas.t_cs_policy_change cpc
         				   where cph.policy_chg_id in
               			 		 (select cpcz.policy_chg_id
                  					from dev_pas.t_cs_accept_change cac,
                       					 dev_pas.t_cs_policy_change cpcz
                 				   where cac.accept_id = cpcz.accept_id
                   					 and cac.accept_code = #{accept_code})
           							 and cph.old_new = '1'
           							 and cpc.policy_chg_id = cph.policy_chg_id
        				)
   				  and cc.customer_name = age.agent_name
   				  and cc.customer_gender = age.agent_gender
   				  and cc.customer_birthday = age.birthday
  			      and cc.customer_cert_type = age.cert_type
   				  and cc.customer_certi_code = age.certi_code
   				  and age.agent_organ_code like '8621%'
   				  and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	
	<!-- 根据五要素查询北京分公司的在职业务员数量 受益人 -->
	<select id="queryInsuranceSelfUWByBene" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1)
					from dev_pas.t_agent age, dev_pas.t_cs_customer cc
 				   where cc.customer_id in
		        		(select ccb.customer_id
		          		  from dev_pas.t_cs_contract_bene ccb,
		               		   dev_pas.t_cs_policy_change cpc
		         		 where ccb.policy_chg_id in
		               		   (select cpcz.policy_chg_id
		                  		  from dev_pas.t_cs_accept_change cac,
		                       		   dev_pas.t_cs_policy_change cpcz
		                 		 where cac.accept_id = cpcz.accept_id
		                   		   and cac.accept_code = #{accept_code})
		                   		   and ccb.old_new = '1'
		           				   and cpc.policy_chg_id = ccb.policy_chg_id)
   				  and cc.customer_name = age.agent_name
   				  and cc.customer_gender = age.agent_gender
   				  and cc.customer_birthday = age.birthday
  			      and cc.customer_cert_type = age.cert_type
   				  and cc.customer_certi_code = age.certi_code
   				  and age.agent_organ_code like '8621%'
   				  and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	
	<!-- 根据五要素查询北京分公司的在职业务员数量 被保人 -->
	<select id="queryInsuranceSelfUWByInsured" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1)
					from dev_pas.t_agent age, dev_pas.t_cs_customer cc
 				   where cc.customer_id in
		        		(select cil.customer_id
		          		  from dev_pas.t_cs_insured_list cil,
		               		   dev_pas.t_cs_policy_change cpc
		         		 where cil.policy_chg_id in
		               		   (select cpcz.policy_chg_id
		                  		  from dev_pas.t_cs_accept_change cac,
		                       		   dev_pas.t_cs_policy_change cpcz
		                 		 where cac.accept_id = cpcz.accept_id
		                   		   and cac.accept_code = #{accept_code})
		                   		   and cil.old_new = '1'
		           				   and cpc.policy_chg_id = cil.policy_chg_id)
   				  and cc.customer_name = age.agent_name
   				  and cc.customer_gender = age.agent_gender
   				  and cc.customer_birthday = age.birthday
  			      and cc.customer_cert_type = age.cert_type
   				  and cc.customer_certi_code = age.certi_code
   				  and age.agent_organ_code like '8621%'
   				  and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	
	<select id="queryLevelByAgentBANCASEXC"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select T.AGENT_LEVEL_CS from dev_pas.T_AGENT_BANCASEXC T where T.AGENT_CODE = #{agent_code}
	]]>
	</select>
	<!-- 根据五要素查询北京分公司的在职业务员数量 投保人 用t_customer -->
	<select id="queryInsuranceSelfUWByHolderNew" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1)
					from dev_pas.t_agent age, dev_pas.t_customer cc
 				   where cc.customer_id in
       			 		 (select cph.customer_id
             				from dev_pas.t_cs_policy_holder cph,
               					 dev_pas.t_cs_policy_change cpc
         				   where cph.policy_chg_id in
               			 		 (select cpcz.policy_chg_id
                  					from dev_pas.t_cs_accept_change cac,
                       					 dev_pas.t_cs_policy_change cpcz
                 				   where cac.accept_id = cpcz.accept_id
                   					 and cac.accept_code = #{accept_code})
           							 and cph.old_new = '1'
           							 and cpc.policy_chg_id = cph.policy_chg_id
        				)
   				  and cc.customer_name = age.agent_name
   				  and cc.customer_gender = age.agent_gender
   				  and cc.customer_birthday = age.birthday
  			      and cc.customer_cert_type = age.cert_type
   				  and cc.customer_certi_code = age.certi_code
   				  and age.agent_organ_code like '8621%'
   				  and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	<!-- 根据五要素查询北京分公司的在职业务员数量 受益人 t_customer-->
	<select id="queryInsuranceSelfUWByBeneNew" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1)
					from dev_pas.t_agent age, dev_pas.t_customer cc
 				   where cc.customer_id in
		        		(select ccb.customer_id
		          		  from dev_pas.t_cs_contract_bene ccb,
		               		   dev_pas.t_cs_policy_change cpc
		         		 where ccb.policy_chg_id in
		               		   (select cpcz.policy_chg_id
		                  		  from dev_pas.t_cs_accept_change cac,
		                       		   dev_pas.t_cs_policy_change cpcz
		                 		 where cac.accept_id = cpcz.accept_id
		                   		   and cac.accept_code = #{accept_code})
		                   		   and ccb.old_new = '1'
		           				   and cpc.policy_chg_id = ccb.policy_chg_id)
   				  and cc.customer_name = age.agent_name
   				  and cc.customer_gender = age.agent_gender
   				  and cc.customer_birthday = age.birthday
  			      and cc.customer_cert_type = age.cert_type
   				  and cc.customer_certi_code = age.certi_code
   				  and age.agent_organ_code like '8621%'
   				  and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	
	<!-- 根据五要素查询北京分公司的在职业务员数量 被保人 t_customer -->
	<select id="queryInsuranceSelfUWByInsuredNew" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1) 
					from dev_pas.t_agent age, dev_pas.t_customer cc
 				   where cc.customer_id in
		        		(select cil.customer_id
		          		  from dev_pas.t_cs_insured_list cil,
		               		   dev_pas.t_cs_policy_change cpc
		         		 where cil.policy_chg_id in
		               		   (select cpcz.policy_chg_id
		                  		  from dev_pas.t_cs_accept_change cac,
		                       		   dev_pas.t_cs_policy_change cpcz
		                 		 where cac.accept_id = cpcz.accept_id
		                   		   and cac.accept_code = #{accept_code})
		                   		   and cil.old_new = '1'
		           				   and cpc.policy_chg_id = cil.policy_chg_id)
   				  and cc.customer_name = age.agent_name
   				  and cc.customer_gender = age.agent_gender
   				  and cc.customer_birthday = age.birthday
  			      and cc.customer_cert_type = age.cert_type
   				  and cc.customer_certi_code = age.certi_code
   				  and age.agent_organ_code like '8621%'
   				  and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	
	<select id="queryInsuranceSelfUWByInsuredFor984New" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
				  select count(1) 
          from dev_pas.t_agent age, dev_pas.t_customer cc
           where cc.customer_id in
                (select cil.customer_id
                    from dev_pas.t_cs_insured_list cil,
                         dev_pas.t_cs_contract_busi_prod cbp,
                         dev_pas.t_cs_policy_change cpc
                 where cil.policy_chg_id in
                         (select cpcz.policy_chg_id
                            from dev_pas.t_cs_accept_change cac,
                                 dev_pas.t_cs_policy_change cpcz
                         where cac.accept_id = cpcz.accept_id
                             and cac.accept_code = #{accept_code})
                             and cil.old_new = '1'
                         and cpc.policy_chg_id = cil.policy_chg_id
                         and cbp.policy_chg_id = cil.policy_chg_id
                         and cbp.busi_prod_code = '00984000'
                         and cil.customer_id = #{customer_id})
            and cc.customer_name = age.agent_name
            and cc.customer_gender = age.agent_gender
            and cc.customer_birthday = age.birthday
              and cc.customer_cert_type = age.cert_type
            and cc.customer_certi_code = age.certi_code
            and age.agent_organ_code like '8621%'
            and age.agent_status in ('1', '2', '3')
		]]>
	</select>
	
	<select id="PA_queryAgentListsByThreeElements" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select tc.customer_name       as agent_name,
		       tc.customer_cert_type  as cert_type,
		       tc.customer_certi_code as certi_code
		  from dev_pas.t_cs_policy_holder tph
		  left join dev_pas.t_customer tc
		    on tph.customer_id = tc.customer_id
		 where 1 = 1
		   and tph.old_new = '1'
		]]>
		<if test="policy_chg_ids !=null and policy_chg_ids != '' ">
            <![CDATA[ and tph.policy_chg_id in ]]>
            <foreach collection="policy_chg_ids" item="policy_chg_id" index="index" open="(" close=")" separator=",">
            	#{policy_chg_id}
        	</foreach>
        </if>
        <![CDATA[
		   and exists (
		        select 1
		          from dev_pas.t_agent ta
		         where 1 = 1
		           and ta.agent_name = tc.customer_name
		           and ta.cert_type = tc.customer_cert_type
		           and ta.certi_code = tc.customer_certi_code
		           and ta.agent_channel = '01'
		           and ta.agent_status in ('1', '2', '3'))
		union
		select tc.customer_name       as agent_name,
		       tc.customer_cert_type  as cert_type,
		       tc.customer_certi_code as certi_code
		  from dev_pas.t_cs_insured_list til
		  left join dev_pas.t_customer tc
		    on til.customer_id = tc.customer_id
		 where 1 = 1
		   and til.old_new = '1'
		]]>
		<if test="policy_chg_ids !=null and policy_chg_ids != '' ">
            <![CDATA[ and til.policy_chg_id in ]]>
            <foreach collection="policy_chg_ids" item="policy_chg_id" index="index" open="(" close=")" separator=",">
            	#{policy_chg_id}
        	</foreach>
        </if>
        <![CDATA[
		   and exists (
		        select 1
		          from dev_pas.t_agent ta
		         where 1 = 1
		           and ta.agent_name = tc.customer_name
		           and ta.cert_type = tc.customer_cert_type
		           and ta.certi_code = tc.customer_certi_code
		           and ta.agent_channel = '01'
		           and ta.agent_status in ('1', '2', '3'))
		union
		select tc.customer_name       as agent_name,
		       tc.customer_cert_type  as cert_type,
		       tc.customer_certi_code as certi_code
		  from dev_pas.t_cs_contract_bene tcb
		  left join dev_pas.t_customer tc
		    on tcb.customer_id = tc.customer_id
		 where 1 = 1
		   and tcb.old_new = '1'
		]]>
		<if test="policy_chg_ids !=null and policy_chg_ids != '' ">
            <![CDATA[ and tcb.policy_chg_id in ]]>
            <foreach collection="policy_chg_ids" item="policy_chg_id" index="index" open="(" close=")" separator=",">
            	#{policy_chg_id}
        	</foreach>
        </if>
        <![CDATA[
		   and exists (        
		        select 1
		          from dev_pas.t_agent ta
		         where 1 = 1
		           and ta.agent_name = tc.customer_name
		           and ta.cert_type = tc.customer_cert_type
		           and ta.certi_code = tc.customer_certi_code
		           and ta.agent_channel = '01'
		           and ta.agent_status in ('1', '2', '3'))
		]]>
	</select>
	<!-- 判断渠道自互保件标识
	如果投保人、被保险人、受益人的三要素（姓名、证件类型、证件号码）与本单的销售人员（T_CONTRACT_AGENT.IS_NB_AGENT=1为本单销售人员）完全一致，
	如果投保人、被保险人、受益人的三要素（姓名、证件类型、证件号码）与 “01-个人营销”（T_AGENT. AGENT_CHANNEL）渠道其他销售人员完全一致,
	 -->
	<select id="PA_queryIsChannelSelfOrMutual" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select tc.customer_name       as agent_name,
		       tc.customer_cert_type  as cert_type,
		       tc.customer_certi_code as certi_code
		  from dev_pas.t_cs_policy_holder tph
		  left join dev_pas.t_customer tc
		    on tph.customer_id = tc.customer_id]]>
		  <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  		<![CDATA[ left join dev_pas.t_contract_agent tca
		  					on tph.policy_id = tca.policy_id ]]>
		  </if>
		  <![CDATA[
		 where 1 = 1
		   and tph.old_new = '1'
		]]>
		<if test="policy_chg_ids !=null and policy_chg_ids != '' ">
            <![CDATA[ and tph.policy_chg_id in ]]>
            <foreach collection="policy_chg_ids" item="policy_chg_id" index="index" open="(" close=")" separator=",">
            	#{policy_chg_id}
        	</foreach>
        </if>
        <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  		<![CDATA[ and tca.is_nb_agent = '1']]>
		</if>
        <![CDATA[
		   and exists (
		        select 1
		          from dev_pas.t_agent ta
		         where 1 = 1
		           and ta.agent_name = tc.customer_name
		           and ta.cert_type = tc.customer_cert_type
		           and ta.certi_code = tc.customer_certi_code]]>
		           <if test="agent_channel !=null and agent_channel != '' ">
			            <![CDATA[ and ta.agent_channel = '01' ]]>
			       </if>
			       <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  				<![CDATA[ and tca.agent_code = ta.agent_code]]>
				   </if>
		           <![CDATA[)
		union
		select tc.customer_name       as agent_name,
		       tc.customer_cert_type  as cert_type,
		       tc.customer_certi_code as certi_code
		  from dev_pas.t_cs_insured_list til
		  left join dev_pas.t_customer tc
		    on til.customer_id = tc.customer_id]]>
		  <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  		<![CDATA[ left join dev_pas.t_contract_agent tca
		  					on til.policy_id = tca.policy_id ]]>
		  </if>
		  <![CDATA[
		 where 1 = 1
		   and til.old_new = '1'
		]]>
		<if test="policy_chg_ids !=null and policy_chg_ids != '' ">
            <![CDATA[ and til.policy_chg_id in ]]>
            <foreach collection="policy_chg_ids" item="policy_chg_id" index="index" open="(" close=")" separator=",">
            	#{policy_chg_id}
        	</foreach>
        </if>
        <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  		<![CDATA[ and tca.is_nb_agent = '1']]>
		</if>
        <![CDATA[
		   and exists (
		        select 1
		          from dev_pas.t_agent ta
		         where 1 = 1
		           and ta.agent_name = tc.customer_name
		           and ta.cert_type = tc.customer_cert_type
		           and ta.certi_code = tc.customer_certi_code]]>
		           <if test="agent_channel !=null and agent_channel != '' ">
			            <![CDATA[ and ta.agent_channel = '01' ]]>
			       </if>
			       <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  				<![CDATA[ and tca.agent_code = ta.agent_code]]>
				   </if>
		           <![CDATA[)
		union
		select tc.customer_name       as agent_name,
		       tc.customer_cert_type  as cert_type,
		       tc.customer_certi_code as certi_code
		  from dev_pas.t_cs_contract_bene tcb
		  left join dev_pas.t_customer tc
		    on tcb.customer_id = tc.customer_id]]>
		  <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  		<![CDATA[ left join dev_pas.t_contract_agent tca
		  					on tcb.policy_id = tca.policy_id ]]>
		  </if>
		  <![CDATA[
		 where 1 = 1
		   and tcb.old_new = '1'
		]]>
		<if test="policy_chg_ids !=null and policy_chg_ids != '' ">
            <![CDATA[ and tcb.policy_chg_id in ]]>
            <foreach collection="policy_chg_ids" item="policy_chg_id" index="index" open="(" close=")" separator=",">
            	#{policy_chg_id}
        	</foreach>
        </if>
        <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  		<![CDATA[ and tca.is_nb_agent = '1']]>
		</if>
        <![CDATA[
		   and exists (        
		        select 1
		          from dev_pas.t_agent ta
		         where 1 = 1
		           and ta.agent_name = tc.customer_name
		           and ta.cert_type = tc.customer_cert_type
		           and ta.certi_code = tc.customer_certi_code]]>
		           <if test="agent_channel !=null and agent_channel != '' ">
			            <![CDATA[ and ta.agent_channel = '01' ]]>
			       </if>
			       <if test="is_nb_agent !=null and is_nb_agent != '' ">
		  				<![CDATA[ and tca.agent_code = ta.agent_code]]>
				   </if>
			   <![CDATA[ ) ]]>
	</select>
	
	<!-- 根据保单号查询投/被/受是否有北分在职销售人员 -->
	<select id="queryAgentCountByPolicyID" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
	
with customer as
 (select tc.customer_name,
         tc.customer_gender,
         tc.customer_birthday,
         tc.customer_cert_type,
         tc.customer_certi_code
    from dev_pas.t_policy_holder tph, dev_pas.t_customer tc
   where tph.customer_id = tc.customer_id
     and tph.policy_id = #{policy_id}
  union
  select tc.customer_name,
         tc.customer_gender,
         tc.customer_birthday,
         tc.customer_cert_type,
         tc.customer_certi_code
    from dev_pas.t_insured_list til, dev_pas.t_customer tc
   where til.customer_id = tc.customer_id
     and til.policy_id = #{policy_id}
  union
  select tc.customer_name,
         tc.customer_gender,
         tc.customer_birthday,
         tc.customer_cert_type,
         tc.customer_certi_code
    from dev_pas.t_contract_bene tcb, dev_pas.t_customer tc
   where tcb.customer_id = tc.customer_id
     and tcb.policy_id = #{policy_id})
select count(*)
  from dev_pas.t_agent ta, customer c
 where ta.certi_code = c.customer_certi_code
   and ta.agent_name = c.customer_name
   and ta.agent_gender = c.customer_gender
   and c.customer_birthday = ta.birthday
   and c.customer_cert_type = ta.cert_type
   and ta.agent_organ_code like '8621%'
   and ta.agent_status in ('1', '2', '3')
		]]>
	</select>
	<select id = "PA_queryAgentByFiveElementsForzhbj" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
		SELECT T.EMPLOYEE_FLAG,
		       T.AGENT_MOBILE,
		       T.AGENT_NORMAL_TYPE,
		       T.POSTAL_ADDRESS,
		       T.CERTI_CODE,
		       T.AGENT_GENDER,
		       T.AGENT_LEVEL,
		       T.AGENT_ORGAN_CODE,
		       T.AGENT_STATUS,
		       T.SALES_ORGAN_CODE,
		       T.DISMISSAL_DATE,
		       T.BIRTHDAY,
		       T.SGENT_HORNER_LEVEL,
		       T.AGENT_NAME,
		       T.AGENT_PHONE,
		       T.AGENT_EMAIL,
		       T.CERT_TYPE,
		       T.HOME_ADDRESS,
		       T.AGENT_CODE,
		       T.EMPLOYMENT_DATE,
		       T.AGENT_CHANNEL
		  FROM DEV_PAS.T_AGENT T
		 WHERE 1 = 1
		   AND T.AGENT_ORGAN_CODE LIKE '8621%'
		   AND T.AGENT_STATUS IN ('1', '2', '3')
		   AND T.AGENT_NAME = #{agent_name}
		   AND T.AGENT_GENDER = #{agent_gender}
		   AND T.BIRTHDAY = #{birthday}
		   AND T.CERTI_CODE = #{certi_code}
		   AND T.CERT_TYPE = #{cert_type}
	 ]]>
	</select>
</mapper>
