<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICustomerUnitOtherDao">

	<sql id="customerUnitOtherWhereCondition">
		<if test=" customer_unit  != null "><![CDATA[ AND A.CUSTOMER_UNIT = #{customer_unit} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" customer_unit_net  != null "><![CDATA[ AND <PERSON><PERSON>USTOMER_UNIT_NET = #{customer_unit_net} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" is_valid  != null  and  is_valid  != ''  "><![CDATA[ AND A.IS_VALID = #{is_valid} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCustomerUnitOtherByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCustomerUnitOther"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CUST_UNIT_OTHER__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER(
				INSERT_TIMESTAMP, CUSTOMER_UNIT, UPDATE_BY, INSERT_TIME, FUND_CODE, LIST_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, CUSTOMER_UNIT_NET, PRICING_DATE,IS_VALID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{customer_unit, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{fund_code, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{customer_unit_net, jdbcType=NUMERIC} , #{pricing_date, jdbcType=DATE}, #{is_valid, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCustomerUnitOther" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER WHERE LIST_ID = #{list_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCustomerUnitOther" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    IS_VALID = #{is_valid, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE 1=1 AND IS_VALID IS NULL ]]>
		<if test=" customer_unit  != null "><![CDATA[ AND CUSTOMER_UNIT = #{customer_unit} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND FUND_CODE = #{fund_code} ]]></if>
		<if test=" customer_unit_net  != null "><![CDATA[ AND CUSTOMER_UNIT_NET = #{customer_unit_net} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND PRICING_DATE = #{pricing_date} ]]></if>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCustomerUnitOtherByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_UNIT, A.FUND_CODE, A.LIST_ID, A.IS_VALID,
			A.CUSTOMER_UNIT_NET, A.PRICING_DATE FROM APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER A WHERE 1 = 1  ]]>
		<include refid="queryCustomerUnitOtherByListIdCondition" />
	</select>
	
	<select id="findCustomerUnitOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				select pricing_date,customer_unit_net,customer_unit
				  from (select a.pricing_date,a.customer_unit_net,a.customer_unit
				          from dev_pas.T_CUSTOMER_UNIT_OTHER a
									where a.pricing_date <= #{pricing_date}
									and a.fund_code = '890000'
									and a.is_valid is null
				         order by a.pricing_date desc)
				 where pricing_date <= #{pricing_date}
				   and rownum = 1
		  ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCustomerUnitOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_UNIT, A.FUND_CODE, A.LIST_ID, A.IS_VALID,
			A.CUSTOMER_UNIT_NET, A.PRICING_DATE FROM APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<include refid="customerUnitOtherWhereCondition" /> 
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCustomerUnitOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_UNIT, A.FUND_CODE, A.LIST_ID, A.IS_VALID,
			A.CUSTOMER_UNIT_NET, A.PRICING_DATE FROM APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<include refid="customerUnitOtherWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCustomerUnitOtherTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER A WHERE 1 = 1  ]]>
		<include refid="customerUnitOtherWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="queryCustomerUnitOtherForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_UNIT, B.FUND_CODE, B.LIST_ID, B.IS_VALID,
			B.CUSTOMER_UNIT_NET, B.PRICING_DATE FROM (
					SELECT ROWNUM RN, A.CUSTOMER_UNIT, A.FUND_CODE, A.LIST_ID, A.IS_VALID,
			A.CUSTOMER_UNIT_NET, A.PRICING_DATE FROM APP___PAS__DBUSER.T_CUSTOMER_UNIT_OTHER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="customerUnitOtherWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
