<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="dailyTradingQuery">
	<sql id="PA_dailyTradingQueryCondition">
		<if test=" apply_code != null and apply_code != ''"><![CDATA[ AND tcm.apply_code=#{apply_code} ]]></if>
	</sql>
	<sql id="PA_dailyTradingQueryByChannelType">
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ AND tcm.channel_type = #{channel_type} ]]></if>
	</sql>
	<sql id="PA_dailyTradingQueryByServiceBank">
		<if test="SERVICE_BANK != null and SERVICE_BANK != ''"><![CDATA[ AND tcm.service_bank= #{service_bank} ]]></if>
	</sql>
	<sql id="PA_dailyTradingQueryByTransTime">
		<if test="trans_time != null and trans_time != ''"><![CDATA[ AND tpat.trans_time = #{trans_time}]]></if>
	</sql>
	<!-- <select id="PA_dailyTradingQuery" resultType="java.util.Map" parameterType="java.util.Map"> 
		<![CDATA[ select ROWNUM RN, tcm.policy_code,tcm.apply_code,tcm.agent_org_id,tcm.BRANCH_CODE,tcm.service_bank_branch,tcm.SERVICE_BANK, 
		(select tb.bank_code from APP___PAS__DBUSER.T_bank tb where tb.bank_code=tcm.service_bank_branch 
		and rownum=1]]> <include refid="PA_dailyTradingQueryByServiceBank" /> <![CDATA[)bank_code, 
		(select tuo.REGION_ID from APP___PAS__DBUSER.T_UDMP_ORG tuo where tcm.organ_code=tuo.organ_id 
		and rownum=1)region_id, (select tpa.fee_amount from APP___PAS__DBUSER.T_prem_arap tpa where 
		tpa.policy_code=tcm.policy_code and rownum=1)fee_amount, (select tpat.trans_time 
		from APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST tpat,APP___PAS__DBUSER.T_policy_account tpa where tpa.policy_id=tcm.policy_id 
		and tpa.account_id=tpat.account_id and rownum=1]]> <include refid="PA_dailyTradingQueryByTransTime" 
		/> <![CDATA[)trans_time, (select tft.trans_type from APP___PAS__DBUSER.T_FUND_TRANS tft where 
		tft.policy_id=tcm.policy_id and rownum=1)trans_type from APP___PAS__DBUSER.T_contract_master 
		tcm,APP___PAS__DBUSER.T_FUND_TRANS tft,APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST tpat,APP___PAS__DBUSER.T_policy_account tpa, 
		APP___PAS__DBUSER.T_bank tb where tft.policy_id=tcm.policy_id and tpa.policy_id=tcm.policy_id 
		and tpa.account_id=tpat.account_id and tb.bank_code=tcm.service_bank_branch 
		]]> <include refid="PA_dailyTradingQueryCondition" /> <include refid="PA_dailyTradingQueryByChannelType"/> 
		<include refid="PA_dailyTradingQueryByServiceBank"/> <include refid="PA_dailyTradingQueryByTransTime"/> 
		<![CDATA[and tcm.liability_state='1']]> </select> -->
	<select id="PA_dailyTradingQuery" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select ROWNUM RN,tcm.policy_code,
                  tcm.apply_code,tcm.agent_org_id,tcm.organ_code,
                  tcm.service_bank_branch,tcm.service_bank,tcm.subinput_type,
(select tpa.fee_amount from APP___PAS__DBUSER.T_prem_arap tpa where tpa.policy_code=tcm.policy_code and rownum=1)fee_amount,
(select tpat.trans_time from APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST tpat,APP___PAS__DBUSER.T_policy_account tpa where tpa.policy_id=tcm.policy_id and tpa.account_id=tpat.account_id and rownum=1]]>
		<include refid="PA_dailyTradingQueryByTransTime" />
<![CDATA[)trans_time,
(select tft.trans_type from APP___PAS__DBUSER.T_FUND_TRANS tft where  tft.policy_id=tcm.policy_id and rownum=1)trans_type
from APP___PAS__DBUSER.T_contract_master tcm,APP___PAS__DBUSER.T_FUND_TRANS tft,APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST tpat,APP___PAS__DBUSER.T_policy_account tpa where 
tft.policy_id=tcm.policy_id and tpa.policy_id=tcm.policy_id and tpa.account_id=tpat.account_id
]]>
		<include refid="PA_dailyTradingQueryCondition" />
		<include refid="PA_dailyTradingQueryByChannelType" />
		<include refid="PA_dailyTradingQueryByServiceBank" />
		<include refid="PA_dailyTradingQueryByTransTime" />
		<![CDATA[and tcm.liability_state='1']]>
	</select>



</mapper>