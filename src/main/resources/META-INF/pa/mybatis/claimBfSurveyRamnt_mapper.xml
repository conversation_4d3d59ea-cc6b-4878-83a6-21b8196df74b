<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.dao.IClaimBfSurveyRamntDao">
<!--
	<sql id="claimBfSurveyRamntWhereCondition">
		<if test=" risk_amnt  != null "><![CDATA[ AND A.RISK_AMNT = #{risk_amnt} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimBfSurveyRamntByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimBfSurveyRamnt"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CLAIM_BF_SURVEY_RAMNT__LIST.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT(
				INSERT_TIMESTAMP, UPDATE_BY, RISK_AMNT, PLAN_ID, INSERT_TIME, LIST_ID, UPDATE_TIME, 
				UPDATE_TIMESTAMP, RISK_TYPE, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{risk_amnt, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , SYSDATE 
				, CURRENT_TIMESTAMP, #{risk_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimBfSurveyRamnt" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT WHERE PLAN_ID = #{plan_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimBfSurveyRamnt" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    RISK_AMNT = #{risk_amnt, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			RISK_TYPE = #{risk_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimBfSurveyRamntByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_AMNT, A.PLAN_ID, A.LIST_ID, 
			A.RISK_TYPE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT A WHERE 1 = 1  ]]>
		<include refid="queryClaimBfSurveyRamntByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimBfSurveyRamnt" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_AMNT, A.PLAN_ID, A.LIST_ID, 
			A.RISK_TYPE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimBfSurveyRamnt" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_AMNT, A.PLAN_ID, A.LIST_ID, 
			A.RISK_TYPE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimBfSurveyRamntTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimBfSurveyRamntForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RISK_AMNT, B.PLAN_ID, B.LIST_ID, 
			B.RISK_TYPE FROM (
					SELECT ROWNUM RN, A.RISK_AMNT, A.PLAN_ID, A.LIST_ID, 
			A.RISK_TYPE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="findClaimBfSurveyRamntByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.RISK_TYPE, A.RISK_AMNT 
			 FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_RAMNT A WHERE 1 = 1 ]]>
		<include refid="findClaimBfSurveyRamntByPlanIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<sql id="findClaimBfSurveyRamntByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>
	
</mapper>
