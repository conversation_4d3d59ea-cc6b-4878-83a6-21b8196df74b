<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IInvestUnitPriceLogDao">
<!--
	<sql id="PA_investUnitPriceLogWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" eva_times  != null "><![CDATA[ AND A.EVA_TIMES = #{eva_times} ]]></if>
		<if test=" confirm_remark != null and confirm_remark != ''  "><![CDATA[ AND A.CONFIRM_REMARK = #{confirm_remark} ]]></if>
		<if test=" confirm_time  != null  and  confirm_time  != ''  "><![CDATA[ AND A.CONFIRM_TIME = #{confirm_time} ]]></if>
		<if test=" expadn_shrink_flag  != null "><![CDATA[ AND A.EXPADN_SHRINK_FLAG = #{expadn_shrink_flag} ]]></if>
		<if test=" dif_price  != null "><![CDATA[ AND A.DIF_PRICE = #{dif_price} ]]></if>
		<if test=" reviser_result  != null "><![CDATA[ AND A.REVISER_RESULT = #{reviser_result} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" reviser_id  != null "><![CDATA[ AND A.REVISER_ID = #{reviser_id} ]]></if>
		<if test=" invest_account_id  != null "><![CDATA[ AND A.INVEST_ACCOUNT_ID = #{invest_account_id} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" fund_assets_id  != null "><![CDATA[ AND A.FUND_ASSETS_ID = #{fund_assets_id} ]]></if>
		<if test=" bid_price  != null "><![CDATA[ AND A.BID_PRICE = #{bid_price} ]]></if>
		<if test=" unit_asset_m_fee  != null "><![CDATA[ AND A.UNIT_ASSET_M_FEE = #{unit_asset_m_fee} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" last_price_id  != null "><![CDATA[ AND A.LAST_PRICE_ID = #{last_price_id} ]]></if>
		<if test=" reviser_time  != null  and  reviser_time  != ''  "><![CDATA[ AND A.REVISER_TIME = #{reviser_time} ]]></if>
		<if test=" reviser_remark != null and reviser_remark != ''  "><![CDATA[ AND A.REVISER_REMARK = #{reviser_remark} ]]></if>
		<if test=" cal_off_price  != null "><![CDATA[ AND A.CAL_OFF_PRICE = #{cal_off_price} ]]></if>
		<if test=" invest_units_daysum_id  != null "><![CDATA[ AND A.INVEST_UNITS_DAYSUM_ID = #{invest_units_daysum_id} ]]></if>
		<if test=" cal_bid_price  != null "><![CDATA[ AND A.CAL_BID_PRICE = #{cal_bid_price} ]]></if>
		<if test=" re_calc_indi  != null "><![CDATA[ AND A.RE_CALC_INDI = #{re_calc_indi} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" off_price  != null "><![CDATA[ AND A.OFF_PRICE = #{off_price} ]]></if>
		<if test=" confirmer_id  != null "><![CDATA[ AND A.CONFIRMER_ID = #{confirmer_id} ]]></if>
		<if test=" confirm_result  != null "><![CDATA[ AND A.CONFIRM_RESULT = #{confirm_result} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryInvestUnitPriceLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addInvestUnitPriceLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="policy_chg_id">
			SELECT APP___PAS__DBUSER.S_INVEST_UNIT_PRICE_LOG__LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG(
				ANNUALIZED_RETURN,MONEY_CODE, EVA_TIMES, CONFIRM_REMARK, CONFIRM_TIME, EXPADN_SHRINK_FLAG, DIF_PRICE, REVISER_RESULT, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, POLICY_CHG_ID, REVISER_ID, INVEST_ACCOUNT_ID, PRICING_DATE, 
				FUND_ASSETS_ID, BID_PRICE, UNIT_ASSET_M_FEE, INVEST_ACCOUNT_CODE, LAST_PRICE_ID, REVISER_TIME, REVISER_REMARK, 
				INSERT_TIME, CAL_OFF_PRICE, INVEST_UNITS_DAYSUM_ID, CAL_BID_PRICE, UPDATE_TIME, RE_CALC_INDI, LOG_ID, 
				OFF_PRICE, CONFIRMER_ID, CONFIRM_RESULT, UPDATE_TIMESTAMP, LOG_TYPE, INSERT_BY ) 
			VALUES (
				#{annualized_return, jdbcType=NUMERIC} ,#{money_code, jdbcType=VARCHAR}, #{eva_times, jdbcType=NUMERIC} , #{confirm_remark, jdbcType=VARCHAR} , #{confirm_time, jdbcType=DATE} , #{expadn_shrink_flag, jdbcType=NUMERIC} , #{dif_price, jdbcType=NUMERIC} , #{reviser_result, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{reviser_id, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} , #{pricing_date, jdbcType=DATE} 
				, #{fund_assets_id, jdbcType=NUMERIC} , #{bid_price, jdbcType=NUMERIC} , #{unit_asset_m_fee, jdbcType=NUMERIC} , #{invest_account_code, jdbcType=VARCHAR} , #{last_price_id, jdbcType=NUMERIC} , #{reviser_time, jdbcType=DATE} , #{reviser_remark, jdbcType=VARCHAR} 
				, SYSDATE , #{cal_off_price, jdbcType=NUMERIC} , #{invest_units_daysum_id, jdbcType=NUMERIC} , #{cal_bid_price, jdbcType=NUMERIC} , SYSDATE , #{re_calc_indi, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} 
				, #{off_price, jdbcType=NUMERIC} , #{confirmer_id, jdbcType=NUMERIC} , #{confirm_result, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteInvestUnitPriceLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateInvestUnitPriceLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    EVA_TIMES = #{eva_times, jdbcType=NUMERIC} ,
			CONFIRM_REMARK = #{confirm_remark, jdbcType=VARCHAR} ,
		    CONFIRM_TIME = #{confirm_time, jdbcType=DATE} ,
		    EXPADN_SHRINK_FLAG = #{expadn_shrink_flag, jdbcType=NUMERIC} ,
		    DIF_PRICE = #{dif_price, jdbcType=NUMERIC} ,
		    REVISER_RESULT = #{reviser_result, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    REVISER_ID = #{reviser_id, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
		    PRICING_DATE = #{pricing_date, jdbcType=DATE} ,
		    FUND_ASSETS_ID = #{fund_assets_id, jdbcType=NUMERIC} ,
		    BID_PRICE = #{bid_price, jdbcType=NUMERIC} ,
		    UNIT_ASSET_M_FEE = #{unit_asset_m_fee, jdbcType=NUMERIC} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
		    LAST_PRICE_ID = #{last_price_id, jdbcType=NUMERIC} ,
		    REVISER_TIME = #{reviser_time, jdbcType=DATE} ,
			REVISER_REMARK = #{reviser_remark, jdbcType=VARCHAR} ,
		    CAL_OFF_PRICE = #{cal_off_price, jdbcType=NUMERIC} ,
		    INVEST_UNITS_DAYSUM_ID = #{invest_units_daysum_id, jdbcType=NUMERIC} ,
		    CAL_BID_PRICE = #{cal_bid_price, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    RE_CALC_INDI = #{re_calc_indi, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    OFF_PRICE = #{off_price, jdbcType=NUMERIC} ,
		    CONFIRMER_ID = #{confirmer_id, jdbcType=NUMERIC} ,
		    CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
			ANNUALIZED_RETURN = #{annualized_return, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findInvestUnitPriceLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ANNUALIZED_RETURN,A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.POLICY_CHG_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.BID_PRICE, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.CAL_BID_PRICE, A.RE_CALC_INDI, A.LOG_ID, 
			A.OFF_PRICE, A.CONFIRMER_ID, A.CONFIRM_RESULT, A.LOG_TYPE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryInvestUnitPriceLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapInvestUnitPriceLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ANNUALIZED_RETURN,A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.POLICY_CHG_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.BID_PRICE, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.CAL_BID_PRICE, A.RE_CALC_INDI, A.LOG_ID, 
			A.OFF_PRICE, A.CONFIRMER_ID, A.CONFIRM_RESULT, A.LOG_TYPE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllInvestUnitPriceLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.ANNUALIZED_RETURN,A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.POLICY_CHG_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.BID_PRICE, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.CAL_BID_PRICE, A.RE_CALC_INDI, A.LOG_ID, 
			A.OFF_PRICE, A.CONFIRMER_ID, A.CONFIRM_RESULT, A.LOG_TYPE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findInvestUnitPriceLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryInvestUnitPriceLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MONEY_CODE, B.EVA_TIMES, B.CONFIRM_REMARK, B.CONFIRM_TIME, B.EXPADN_SHRINK_FLAG, B.DIF_PRICE, B.REVISER_RESULT, 
			B.LIST_ID, B.POLICY_CHG_ID, B.REVISER_ID, B.INVEST_ACCOUNT_ID, B.PRICING_DATE, 
			B.FUND_ASSETS_ID, B.BID_PRICE, B.UNIT_ASSET_M_FEE, B.INVEST_ACCOUNT_CODE, B.LAST_PRICE_ID, B.REVISER_TIME, B.REVISER_REMARK, 
			B.CAL_OFF_PRICE, B.INVEST_UNITS_DAYSUM_ID, B.CAL_BID_PRICE, B.RE_CALC_INDI, B.LOG_ID, 
			B.OFF_PRICE, B.CONFIRMER_ID, B.CONFIRM_RESULT, B.LOG_TYPE FROM (
					SELECT ROWNUM RN, A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.POLICY_CHG_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.BID_PRICE, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.CAL_BID_PRICE, A.RE_CALC_INDI, A.LOG_ID, 
			A.OFF_PRICE, A.CONFIRMER_ID, A.CONFIRM_RESULT, A.LOG_TYPE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
