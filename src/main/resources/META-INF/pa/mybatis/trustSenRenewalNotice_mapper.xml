<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.trustSenRenewalNotice.dao.impl.TrustSenRenewalNoticeDaoImpl">
	
	<!-- 查询信托公司数据 -->
	<select id = "PA_queryTrustCompany"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COMPANY_ID, A.COMPANY_NAME, A.EMAIL, A.CUSTOMER_ID FROM dev_pas.T_TRUST_COMPANY A 
			WHERE ROWNUM <=  1000 AND A.Email is not null  ]]>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.COMPANY_ID , #{modnum}) = #{start}]]></if> 
	</select>

	<!-- 查询清单数据 -->
	<select id = "PA_queryTrustSenRenewal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT G.*,ROWNUM RN FROM (
		select 
		       distinct 
		       (select tuo.organ_name from dev_pas.t_udmp_org tuo where tuo.organ_code = substr(tcm.organ_code, 0, 4)) organ_name,
		       substr(tcm.organ_code, 0, 6) organ_code,
		       tc.customer_name holder_name,
		       tcbp.policy_code,
		       (select tbp.product_name_sys from dev_pds.t_business_product tbp where tbp.business_prd_id = tcbp.busi_prd_id) busi_prod_name,
		       tcp.std_prem_af prem,
		       tce.pay_due_date,
		       (select tls.status_name from dev_pas.t_liability_status tls where tls.status_code = tcbp.liability_state) liability_state,
		       tcb.company_id,
		       tcbp.lapse_date,
		       tce.pay_due_date + 60 as pay_end_date,
		       '01' list_type
		from 
		       dev_pas.t_contract_bene tcb, 
		       dev_pas.t_contract_busi_prod tcbp, 
		       dev_pas.t_contract_extend tce, 
		       dev_pas.t_contract_product tcp,
		       dev_pas.t_policy_holder tph,
		       dev_pas.t_customer tc,
		       dev_pas.t_contract_master tcm
		where 
		tcb.busi_item_id = tcbp.busi_item_id and 
		tcb.busi_item_id = tce.busi_item_id and 
		tcb.busi_item_id = tcp.busi_item_id and
		tph.policy_id = tcbp.policy_id and
		tcm.policy_id = tcbp.policy_id and
		tph.customer_id = tc.customer_id and
		tcbp.liability_state = 1 and 
		tcb.BENE_KIND = '02' 
		]]>
		<if test="company_id_list1 != null and company_id_list1.size()!=0 ">
			<![CDATA[ and  tcb.company_id in (]]>
			<foreach collection="company_id_list1" item="company_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{company_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test="customer_id_list1 != null and customer_id_list1.size()!=0 ">
			<![CDATA[ and  tc.customer_id in (]]>
			<foreach collection="customer_id_list1" item="customer_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{customer_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" next_time != null"><![CDATA[ AND tce.pay_due_date >= #{next_time, jdbcType=DATE} ]]></if>
		<if test=" after_next_time != null"><![CDATA[ AND tce.pay_due_date < #{after_next_time, jdbcType=DATE} ]]></if>
		
		<![CDATA[ 
		union all
		select 
		       distinct 
		       (select tuo.organ_name from dev_pas.t_udmp_org tuo where tuo.organ_code = substr(tcm.organ_code, 0, 4)) organ_name,
		       substr(tcm.organ_code, 0, 6) organ_code,
		       tc.customer_name holder_name,
		       tcbp.policy_code,
		       (select tbp.product_name_sys from dev_pds.t_business_product tbp where tbp.business_prd_id = tcbp.busi_prd_id) busi_prod_name,
		       tcp.std_prem_af prem,
		       tce.pay_due_date,
		       (select tls.status_name from dev_pas.t_liability_status tls where tls.status_code = tcbp.liability_state) liability_state,
		       tcb.company_id,
		       tcbp.lapse_date,
		       tce.pay_due_date + 60 as pay_end_date,
		       '02' list_type
		from 
		       dev_pas.t_contract_bene tcb, 
		       dev_pas.t_contract_busi_prod tcbp, 
		       dev_pas.t_contract_extend tce, 
		       dev_pas.t_contract_product tcp,
		       dev_pas.t_policy_holder tph,
		       dev_pas.t_customer tc,
		       dev_pas.t_contract_master tcm
		where 
		tcb.busi_item_id = tcbp.busi_item_id and 
		tcb.busi_item_id = tce.busi_item_id and 
		tcb.busi_item_id = tcp.busi_item_id and
		tph.policy_id = tcbp.policy_id and
		tcm.policy_id = tcbp.policy_id and
		tph.customer_id = tc.customer_id and
		tcbp.liability_state = 1 and 
		tcb.BENE_KIND = '02'  
		]]>
		<if test="company_id_list2 != null and company_id_list2.size()!=0 ">
			<![CDATA[ and  tcb.company_id in (]]>
			<foreach collection="company_id_list2" item="company_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{company_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test="customer_id_list2 != null and customer_id_list2.size()!=0 ">
			<![CDATA[ and  tc.customer_id in (]]>
			<foreach collection="customer_id_list2" item="customer_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{customer_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" batch_time != null"><![CDATA[ AND tce.pay_due_date < #{batch_time, jdbcType=DATE} ]]></if>

		<![CDATA[ 
		union all
		select 
		       distinct 
		       (select tuo.organ_name from dev_pas.t_udmp_org tuo where tuo.organ_code = substr(tcm.organ_code, 0, 4)) organ_name,
		       substr(tcm.organ_code, 0, 6) organ_code,
		       tc.customer_name holder_name,
		       tcbp.policy_code,
		       (select tbp.product_name_sys from dev_pds.t_business_product tbp where tbp.business_prd_id = tcbp.busi_prd_id) busi_prod_name,
		       tcp.std_prem_af prem,
		       tce.pay_due_date,
		       (select tls.status_name from dev_pas.t_liability_status tls where tls.status_code = tcbp.liability_state) liability_state,
		       tcb.company_id,
		       tcbp.lapse_date,
		       tce.pay_due_date + 60 as pay_end_date,
		       '03' list_type
		from 
		       dev_pas.t_contract_bene tcb, 
		       dev_pas.t_contract_busi_prod tcbp, 
		       dev_pas.t_contract_extend tce, 
		       dev_pas.t_contract_product tcp,
		       dev_pas.t_policy_holder tph,
		       dev_pas.t_customer tc,
		       dev_pas.t_contract_master tcm
		where 
		tcb.busi_item_id = tcbp.busi_item_id and 
		tcb.busi_item_id = tce.busi_item_id and 
		tcb.busi_item_id = tcp.busi_item_id and
		tph.policy_id = tcbp.policy_id and
		tcm.policy_id = tcbp.policy_id and
		tph.customer_id = tc.customer_id and
		tcbp.liability_state = 4 and 
		tcb.BENE_KIND = '02' 
		]]>
		<if test="company_id_list3 != null and company_id_list3.size()!=0 ">
			<![CDATA[ and  tcb.company_id in (]]>
			<foreach collection="company_id_list3" item="company_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{company_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test="customer_id_list3 != null and customer_id_list3.size()!=0 ">
			<![CDATA[ and  tc.customer_id in (]]>
			<foreach collection="customer_id_list3" item="customer_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{customer_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" last_time  != null "><![CDATA[ AND tcbp.lapse_date >= #{last_time, jdbcType=DATE} ]]></if>
		<if test=" batch_time  != null "><![CDATA[ AND tcbp.lapse_date < #{batch_time, jdbcType=DATE} ]]></if>
		<![CDATA[ ) G ]]>
	</select>

	<!-- 查询邮件发件人 -->
	<select id="PA_findTrustEmailSender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.CONSTANTS_ID, T.CONSTANTS_KEY, T.CONSTANTS_VALUE, T.CONSTANTS_DESC, T.SUB_ID 
				    FROM DEV_PAS.T_CONSTANTS_INFO T 
				   WHERE T.CONSTANTS_KEY = 'TRUST_SEND_RENEWAL_MAIL' ]]> 
	</select>
	
	<!-- 查询邮件抄送人 -->
	<select id="PA_findTrustEmailCCRecipient" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.CONSTANTS_ID, T.CONSTANTS_KEY, T.CONSTANTS_VALUE, T.CONSTANTS_DESC, T.SUB_ID 
				    FROM DEV_PAS.T_CONSTANTS_INFO T 
				   WHERE T.CONSTANTS_KEY = 'TRUST_SEND_RENEWAL_MAIL_CC' ]]> 
	</select>
	
</mapper>