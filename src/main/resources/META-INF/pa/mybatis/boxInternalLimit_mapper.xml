<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBoxInternalLimitDao">

	<sql id="boxInternalLimitWhereCondition">
		<if test=" max_rate  != null "><![CDATA[ AND A.MAX_RATE = #{max_rate} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" min_rate  != null "><![CDATA[ AND A.MIN_RATE = #{min_rate} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" confirm_desc != null and confirm_desc != ''  "><![CDATA[ AND A.CONFIRM_DESC = #{confirm_desc} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" confirm_date  != null  and  confirm_date  != ''  "><![CDATA[ AND A.CONFIRM_DATE = #{confirm_date} ]]></if>
		<if test=" startDate !=null and startDate !='' "><![CDATA[ AND A.START_DATE >= #{startDate}]]></if>
		<if test=" endDate != null and endDate !='' "><![CDATA[AND A.START_DATE <= #{endDate}]]></if>
		<if test=" confirm_result  != null "><![CDATA[ AND A.CONFIRM_RESULT = #{confirm_result} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryBoxInternalLimitByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addBoxInternalLimit"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_BOX_INTERNAL_LIMIT__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT(
				MAX_RATE, INSERT_TIME, END_DATE, FUND_CODE, UPDATE_TIME, START_TIME, STATUS, 
				MIN_RATE, INSERT_TIMESTAMP, START_DATE, CONFIRM_DESC, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, 
				END_TIME, INSERT_BY, CONFIRM_DATE,CONFIRM_RESULT ) 
			VALUES (
				#{max_rate, jdbcType=NUMERIC}, SYSDATE , #{end_date, jdbcType=DATE} , #{fund_code, jdbcType=VARCHAR} , SYSDATE , #{start_time, jdbcType=TIME} , #{status, jdbcType=NUMERIC} 
				, #{min_rate, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{start_date, jdbcType=DATE} , #{confirm_desc, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{end_time, jdbcType=TIME} , #{insert_by, jdbcType=NUMERIC} , #{confirm_date, jdbcType=DATE} , #{confirm_result, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteBoxInternalLimit" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateBoxInternalLimit" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT ]]>
		<set>
		<trim suffixOverrides=",">
		    MAX_RATE = #{max_rate, jdbcType=NUMERIC} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    START_TIME = #{start_time, jdbcType=TIME} ,
		    STATUS = #{status, jdbcType=NUMERIC} ,
		    MIN_RATE = #{min_rate, jdbcType=NUMERIC} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
			CONFIRM_DESC = #{confirm_desc, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=TIME} ,
		    CONFIRM_DATE = #{confirm_date, jdbcType=DATE} ,
		    CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findBoxInternalLimitByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.STATUS, 
			A.MIN_RATE, A.START_DATE, A.CONFIRM_DESC, A.LIST_ID, 
			A.END_TIME, A.CONFIRM_DATE,A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE 1 = 1  ]]>
		<include refid="queryBoxInternalLimitByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapBoxInternalLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.STATUS, 
			A.MIN_RATE, A.START_DATE, A.CONFIRM_DESC, A.LIST_ID, 
			A.END_TIME, A.CONFIRM_DATE,A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE ROWNUM <=  1000  ]]>
		<include refid="boxInternalLimitWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 查询单条 -->
	<select id="findBoxInternalLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.STATUS, 
			A.MIN_RATE, A.START_DATE, A.CONFIRM_DESC, A.LIST_ID, 
			A.END_TIME, A.CONFIRM_DATE,A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE 1 = 1  ]]>
		<include refid="boxInternalLimitWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllBoxInternalLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.STATUS, 
			A.MIN_RATE, A.START_DATE, A.CONFIRM_DESC, A.LIST_ID, 
			A.END_TIME, A.CONFIRM_DATE,A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE ROWNUM <=  1000  ]]>
		<include refid="boxInternalLimitWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findBoxInternalLimitTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE 1 = 1  ]]>
		 <include refid="boxInternalLimitWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryBoxInternalLimitForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MAX_RATE, B.END_DATE, B.FUND_CODE, B.START_TIME, B.STATUS, 
			B.MIN_RATE, B.START_DATE, B.CONFIRM_DESC, B.LIST_ID, 
			B.END_TIME, B.CONFIRM_DATE,B.CONFIRM_RESULT FROM (
					SELECT ROWNUM RN, A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.STATUS, 
			A.MIN_RATE, A.START_DATE, A.CONFIRM_DESC, A.LIST_ID, 
			A.END_TIME, A.CONFIRM_DATE,A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="boxInternalLimitWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
