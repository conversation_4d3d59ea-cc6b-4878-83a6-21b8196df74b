<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.CvTxInfoMainDaoImpl">

	<sql id="PA_cvTxInfoMainWhereCondition">
		<if test=" batch_id != null "><![CDATA[ AND A.BATCH_ID = #{batch_id} ]]></if>
		<if test=" file_name != null and file_name != ''  "><![CDATA[ AND A.FILE_NAME = #{file_name} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="PA_addCvTxInfoMain" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="main_id">
			SELECT APP___PAS__DBUSER.S_CV_TX_INFO_MAIN.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CV_TX_INFO_MAIN(
				MAIN_ID,FILE_NAME,POLICY_COUNT,INSERT_BY,UPDATE_BY,INSERT_TIME,INSERT_TIMESTAMP,UPDATE_TIME,UPDATE_TIMESTAMP) 
			VALUES (
			    #{main_id, jdbcType=NUMERIC},
				#{file_name, jdbcType=NUMERIC},
			    #{policy_count, jdbcType=NUMERIC},
				#{insert_by, jdbcType=NUMERIC},
				#{update_by, jdbcType=NUMERIC},
				SYSDATE,
				CURRENT_TIMESTAMP,
				SYSDATE,
				CURRENT_TIMESTAMP
				)
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="PA_deleteCvTxInfoMain" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CV_TX_INFO_MAIN A WHERE 1=1]]>
		<include refid="PA_cvTxInfoMainWhereCondition" />
	</delete>

    <!-- 单条查询操作 -->
	<select id="PA_findObjectCvTxInfoMain" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_COUNT,A.FILE_NAME
		FROM APP___PAS__DBUSER.T_CV_TX_INFO_MAIN A WHERE 1 = 1  ]]>
		<include refid="PA_cvTxInfoMainWhereCondition" />
	</select>

    <!-- 多条查询操作 -->
	<select id="PA_findAllCvTxInfoMain" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_COUNT,A.FILE_NAME
		FROM APP___PAS__DBUSER.T_CV_TX_INFO_MAIN A WHERE 1 = 1  ]]>
		<include refid="PA_cvTxInfoMainWhereCondition" />
	</select>

	<!-- 修改操作 -->
	<update id="PA_updateCvTxInfoMain" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CV_TX_INFO_MAIN ]]>
		<set>
			<trim suffixOverrides=",">
		    POLICY_COUNT = #{policy_count, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE MAIN_ID = #{main_id, jdbcType=NUMERIC} ]]>
	</update>
	
</mapper>
