<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.css.dao.IDistrictDao">
	<!-- 根据地址信息查询邮编 -->
	<select id="findValidatePost" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALIDATE_POST,A.VALIDATE_ADS_NAME FROM APP___PAS__DBUSER.T_VALIDATE_POST A WHERE 1=1 AND A.VALIDATE_ADS_NAME = #{validate_ads_name}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	<!-- 查询所有操作 -->
	<select id="findAllDistrict" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.PARENT_CODE, A.DISTRICT_LEVEL, A.CODE FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1=1 AND A.DISTRICT_LEVEL = '1'  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	<!-- 查询所有操作 -->
	
	
	<!-- 查询专户所有操作 -->
	<select id="findAllDistrictSpecial" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.PARENT_CODE, A.DISTRICT_LEVEL, A.CODE,A.NVALID_ADDRESS_FLAG  FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1=1 AND A.DISTRICT_LEVEL = '1' AND  A.NVALID_ADDRESS_FLAG = '1']]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	<!-- 查询专户所有操作 -->
	
	
	<select id="findDistrict" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.PARENT_CODE, A.DISTRICT_LEVEL, A.CODE,A.NVALID_ADDRESS_FLAG FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1=1 AND A.CODE = #{code}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	<!-- 根据parentCode查询市、区县信息 -->
	<select id="findAllDistrictByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.PARENT_CODE, A.DISTRICT_LEVEL, A.CODE FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1=1 AND A.PARENT_CODE = #{parent_code}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>


	<!-- 根据parentCode查询市、区县信息 -->
	<select id="findAllDistrictByCodeSpecial" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.PARENT_CODE, A.DISTRICT_LEVEL, A.CODE FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1=1 AND A.PARENT_CODE = #{parent_code}  AND  A.NVALID_ADDRESS_FLAG = '1']]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>

	<!-- 查询所有省操作 -->
	<select id="NB_findAllProvince" resultType="java.util.Map"
		parameterType="java.util.Map">
<![CDATA[ SELECT A.CODE, A.NAME FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1 = 1 AND A.CODE LIKE '__0000' ]]>
<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	<!-- 查询指定省对应所有市的操作 -->
	<select id="NB_findCityByProvince" resultType="java.util.Map"
		parameterType="java.util.Map">
<![CDATA[ SELECT A.CODE, A.NAME FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1 = 1 
			AND A.CODE LIKE SUBSTR(#{code }, 0, 2)||'%00' AND A.CODE NOT LIKE '%0000' ]]>
<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	<!-- 查询指定市对应的所有区县的操作 -->
	<select id="NB_findCountyByCity" resultType="java.util.Map"
		parameterType="java.util.Map">
<![CDATA[ SELECT A.CODE, A.NAME FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1 = 1 
			AND A.CODE LIKE SUBSTR(#{code }, 0, 4)||'%' AND A.CODE != #{code } 
		]]>
<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	<!-- 根据Code查询省、市、区县信息 -->
	<select id="findDistrictByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.PARENT_CODE, A.DISTRICT_LEVEL, A.CODE FROM APP___PAS__DBUSER.T_DISTRICT A 
		WHERE 1=1 AND A.CODE = #{code}  ]]>
	</select>
	
	<!-- 查询指定省对应所有市的操作 -->
	<select id="NB_findCityByProvinces" resultType="java.util.Map"
		parameterType="java.util.Map">
<![CDATA[ SELECT A.CODE, A.NAME FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1 = 1 
			AND A.CODE LIKE SUBSTR(#{code }, 0, 2)||'%00' AND A.CODE NOT LIKE '%0000' ]]>
<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	<!-- 查询指定市对应的所有区县的操作 -->
	<select id="NB_findCountyByCitys" resultType="java.util.Map"
		parameterType="java.util.Map">
<![CDATA[ SELECT A.CODE, A.NAME FROM APP___PAS__DBUSER.T_DISTRICT A WHERE 1 = 1 
			AND A.CODE LIKE SUBSTR(#{code }, 0, 4)||'%' AND A.CODE != #{code } 
		]]>
<![CDATA[ ORDER BY A.CODE ]]>
	</select>	
	
	
	
	
<!-- 查询指定区县对应的所有街道的操作 -->
	<select id="findAllDistrictFourByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
			SELECT A.TOWN_STREET_CODE AS CODE,
			  A.TOWN_STREET_NAME AS NAME, 
			  A.CODE AS PARENT_CODE
			  FROM APP___PAS__DBUSER.T_DISTRICT_FOUR A
			 WHERE 1 = 1
			   AND A.CODE = #{parent_code}
		]]>
		<![CDATA[ ORDER BY A.TOWN_STREET_CODE ]]>
	</select>
	
	<!-- 查询指定街道编码查询对应的中文的操作 -->
	<select id="findDistrictFourByTownStreetCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
			SELECT A.TOWN_STREET_CODE AS CODE,
			  A.TOWN_STREET_NAME AS NAME, 
			  A.CODE AS PARENT_CODE
			  FROM APP___PAS__DBUSER.T_DISTRICT_FOUR A
			 WHERE 1 = 1
			   AND A.TOWN_STREET_CODE = #{code}
		]]>
		<![CDATA[ ORDER BY A.TOWN_STREET_CODE ]]>
	</select>		
</mapper>
