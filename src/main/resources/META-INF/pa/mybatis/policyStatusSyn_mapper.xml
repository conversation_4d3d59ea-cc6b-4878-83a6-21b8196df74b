<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.nci.tunan.pa.dao.IPolicyStatusSynForAbcDao">

   <!-- 查询农行前一日的保全申请文件受理后的数据操作 -->	
	<select id="findAllPolicyInfoForAbc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   select TA.APPLY_TIME AS BUSI_DATE,
       TCP.POLICY_CODE,
       TA.APPLY_NAME,
       TC.CUSTOMER_NAME AS APPNT_NAME,
       TC.CUSTOMER_CERT_TYPE AS APPNT_ID_TYPE,
       TC.CUSTOMER_CERTI_CODE AS APPNT_TD_CODE,
       TCA.ACCEPT_STATUS ,
       TCP.FEE_AMOUNT,
       TCP.SERVICE_CODE,
       TCP.HESITATE_FLAG
  from dev_pas.T_CS_POLICY_CHANGE TCP
  LEFT JOIN dev_pas.T_CS_ACCEPT_CHANGE TCA
    ON TCA.ACCEPT_ID = TCP.ACCEPT_ID
  LEFT JOIN dev_pas.T_CS_APPLICATION TA
    ON TA.CHANGE_ID = TCA.CHANGE_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TM
    ON TM.POLICY_CODE = TCP.POLICY_CODE
  LEFT JOIN DEV_PAS.T_POLICY_HOLDER TPH
    ON TPH.POLICY_CODE = TCP.POLICY_CODE
  LEFT JOIN DEV_PAS.T_CUSTOMER TC
    ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
 WHERE  
   TCP.SERVICE_CODE IN ('CT', 'AG')
		 ]]>
	<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND TM.service_bank_branch = #{service_bank_branch} ]]></if>
	<if test=" start_date != null  "><![CDATA[ AND TA.APPLY_TIME >= #{start_date} ]]></if>
	<if test=" end_date != null "><![CDATA[ AND TA.APPLY_TIME < #{end_date} ]]></if>
	<if test=" policy_code != null and policy_code !='' "><![CDATA[ AND TCP.POLICY_CODE = #{policy_code} ]]></if>
	<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND TM.channel_type = #{channel_type} ]]></if>
	</select>
	
	
</mapper>
