<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IQueryCusFiveBasicByTelDao">

	<sql id="queryCusInTel">
		<if test="house_tel != null and house_tel != '' ">
			<![CDATA[AND T.HOUSE_TEL = #{house_tel}]]></if>
		<if test="mobile_tel != null and mobile_tel != '' ">
			<![CDATA[AND T.MOBILE_TEL = #{mobile_tel}]]></if>
		<if test="policy_code != null and policy_code != '' ">
			<![CDATA[AND T.POLICY_CODE = #{policy_code}]]></if>	
	    <if test="customer_id != null and customer_id != '' ">
			<![CDATA[AND T.CUSTOMER_ID = #{customer_id}]]></if>	
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[AND  T.ORGAN_CODE in (
        SELECT B.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  B 
          START WITH B.ORGAN_CODE =#{organ_code}
          CONNECT BY PRIOR B.ORGAN_CODE=B.UPORGAN_CODE
               )
			]]>
		</if>
		<if test="liability_state_list != null and liability_state_list.size()!=0"><![CDATA[ AND T.LIABILITY_STATE  IN ]]>
			<foreach collection="liability_state_list" item="liability_state_list"
				index="index" open="(" close=")" separator=",">
				#{liability_state_list}
			</foreach>
		</if>
	</sql>
	<sql id="queryCusInTelBJ">
		<if test="house_tel != null and house_tel != '' ">
			<![CDATA[AND A.HOUSE_TEL = #{house_tel}]]></if>
		<if test="mobile_tel != null and mobile_tel != '' ">
			<![CDATA[AND A.MOBILE_TEL = #{mobile_tel}]]></if>
		<if test="policy_code != null and policy_code != '' ">
			<![CDATA[AND A.POLICY_CODE = #{policy_code}]]></if>	
	    <if test="customer_id != null and customer_id != '' ">
			<![CDATA[AND A.CUSTOMER_ID = #{customer_id}]]></if>	
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[AND  A.ORGAN_CODE in (
        SELECT B.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  B 
          START WITH B.ORGAN_CODE =#{organ_code}
          CONNECT BY PRIOR B.ORGAN_CODE=B.UPORGAN_CODE
               )
			]]>
		</if>	
	</sql>
	<select id="queryAllCusFiveBasicByTel" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[   SELECT
             T.CUSTOMER_NAME,
             T.CUSTOMER_GENDER,
             T.CUSTOMER_CERTI_CODE,
             T.CUSTOMER_BIRTHDAY,
             T.CUSTOMER_ID,
             T.TYPE
              FROM (SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TPH.CUSTOMER_ID,
                           TCT.TYPE,
                           TCM.LIABILITY_STATE,
                           TC.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TC.HOUSE_TEL
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                        ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TPH.POLICY_ID = TCM.POLICY_ID
                    UNION ALL
                    SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TIL.CUSTOMER_ID,
                           TCT.TYPE,
                           TCM.LIABILITY_STATE,
                           TC.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TC.HOUSE_TEL
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL
                        ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TIL.POLICY_ID = TCM.POLICY_ID
                    UNION ALL
                    
                    SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TCB.CUSTOMER_ID,
                           TCT.TYPE,
                           TCM.LIABILITY_STATE,
                           TC.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TC.HOUSE_TEL
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_CONTRACT_BENE TCB
                        ON TC.CUSTOMER_ID = TCB.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TCB.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TCB.POLICY_ID = TCM.POLICY_ID
                      JOIN DEV_PAS.T_CONTRACT_BENE TCB
                        ON TCM.POLICY_ID = TCB.POLICY_ID) T
             WHERE T.LIABILITY_STATE = 1

				 ]]>
		<include refid="queryCusInTel" />
		 <![CDATA[GROUP BY T.CUSTOMER_NAME,
              T.CUSTOMER_GENDER,
              T.CUSTOMER_CERTI_CODE,
              T.CUSTOMER_BIRTHDAY,
              T.CUSTOMER_ID,
              T.TYPE]]>
	</select>
<select id="queryholderByTel" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[   SELECT
             T.CUSTOMER_NAME,
             T.CUSTOMER_GENDER,
             T.CUSTOMER_CERTI_CODE,
             T.CUSTOMER_BIRTHDAY,
             T.CUSTOMER_ID,
             T.CODE,
             T.CUSTOMER_CERT_TYPE
              FROM (SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TC.CUSTOMER_CERT_TYPE,
                           TPH.CUSTOMER_ID,
                           TCT.TYPE,
                           TCM.LIABILITY_STATE,
                           TA.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TCT.CODE,
                           TC.HOUSE_TEL
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                        ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TPH.POLICY_ID = TCM.POLICY_ID
                      JOIN DEV_PAS.T_ADDRESS TA 
                  		ON TA.ADDRESS_ID = TPH.ADDRESS_ID
                   ) T
             WHERE 1=1
				 ]]>
		<include refid="queryCusInTel" />
		 <![CDATA[GROUP BY T.CUSTOMER_NAME,
              T.CUSTOMER_GENDER,
              T.CUSTOMER_CERTI_CODE,
              T.CUSTOMER_BIRTHDAY,
              T.CUSTOMER_ID,
              T.CODE,
              T.CUSTOMER_CERT_TYPE]]>
</select>

  <select id="queryholderandinsuredByTel" resultType="java.util.Map" parameterType="java.util.Map">
     <![CDATA[   SELECT
             T.CUSTOMER_NAME,
             T.CUSTOMER_GENDER,
             T.CUSTOMER_CERTI_CODE,
             T.CUSTOMER_BIRTHDAY,
             T.CUSTOMER_ID,
             T.CODE,
             T.CUSTOMER_CERT_TYPE
              FROM (SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TC.CUSTOMER_CERT_TYPE,
                           TPH.CUSTOMER_ID,
                           TCT.CODE,
                           TCM.LIABILITY_STATE,
                           TC.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TC.HOUSE_TEL
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                        ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TPH.POLICY_ID = TCM.POLICY_ID
                        UNION ALL
                    SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TC.CUSTOMER_CERT_TYPE,
                           TIL.CUSTOMER_ID,
                           TCT.CODE,
                           TCM.LIABILITY_STATE,
                           TC.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TC.HOUSE_TEL
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL
                        ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TIL.POLICY_ID = TCM.POLICY_ID
                   ) T
             WHERE 1=1
				 ]]>
		<include refid="queryCusInTel" />
		 <![CDATA[ GROUP BY T.CUSTOMER_NAME,
              T.CUSTOMER_GENDER,
              T.CUSTOMER_CERTI_CODE,
              T.CUSTOMER_BIRTHDAY,
              T.CUSTOMER_ID,
              T.CODE,
              T.CUSTOMER_CERT_TYPE]]>
  </select>
  <select id="queryinsuredByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
  <![CDATA[   SELECT
             T.CUSTOMER_NAME,
             T.CUSTOMER_GENDER,
             T.CUSTOMER_CERTI_CODE,
             T.CUSTOMER_BIRTHDAY,
             T.CUSTOMER_ID,
             T.TYPE,
             T.POLICY_CODE,
             T.MOBILE_TEL,
             T.RELATION_TO_PH,
             T.CUSTOMER_CERT_TYPE
              FROM (
                    SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TC.CUSTOMER_CERT_TYPE,
                           TIL.CUSTOMER_ID,
                           TCT.TYPE,
                           TCM.LIABILITY_STATE,
                           TC.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TC.HOUSE_TEL,
                           TCM.POLICY_CODE,
                           TIL.RELATION_TO_PH
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL
                        ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TIL.POLICY_ID = TCM.POLICY_ID  
                   ) T
             WHERE 1=1
				 ]]>
			<include refid="queryCusInTel" />
		 <![CDATA[ GROUP BY T.CUSTOMER_NAME,
              T.CUSTOMER_GENDER,
              T.CUSTOMER_CERTI_CODE,
              T.CUSTOMER_BIRTHDAY,
              T.CUSTOMER_ID,
              T.TYPE,
              T.MOBILE_TEL,
              T.POLICY_CODE,
              T.RELATION_TO_PH,
              T.CUSTOMER_CERT_TYPE]]>
  </select>
  <select id="queryholderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[   SELECT
             T.CUSTOMER_NAME,
             T.CUSTOMER_GENDER,
             T.CUSTOMER_CERTI_CODE,
             T.CUSTOMER_BIRTHDAY,
             T.CUSTOMER_ID,
             T.TYPE,
             T.POLICY_CODE,
             T.MOBILE_TEL,
             T.CUSTOMER_CERT_TYPE
              FROM (SELECT TC.CUSTOMER_NAME,
                           TC.CUSTOMER_GENDER,
                           TC.CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_BIRTHDAY,
                           TC.CUSTOMER_CERT_TYPE,
                           TPH.CUSTOMER_ID,
                           TCT.TYPE,
                           TCM.LIABILITY_STATE,
                           TC.MOBILE_TEL,
                           TCM.ORGAN_CODE,
                           TC.HOUSE_TEL,
                           TCM.POLICY_CODE
                      FROM APP___PAS__DBUSER.T_CUSTOMER TC
                      JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                        ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                      JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                        ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                      JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                        ON TPH.POLICY_ID = TCM.POLICY_ID
                   ) T
             WHERE 1=1
				 ]]>
			<include refid="queryCusInTel" />
		 <![CDATA[ GROUP BY T.CUSTOMER_NAME,
              T.CUSTOMER_GENDER,
              T.CUSTOMER_CERTI_CODE,
              T.CUSTOMER_BIRTHDAY,
              T.CUSTOMER_ID,
              T.TYPE,
              T.MOBILE_TEL,
              T.POLICY_CODE,
              T.CUSTOMER_CERT_TYPE]]>
  </select>
  
  
  
  <select id="queryholderByTelBJ" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[   SELECT C.CUSTOMER_NAME,
       C.CUSTOMER_GENDER,
       C.CUSTOMER_CERTI_CODE,
       C.CUSTOMER_BIRTHDAY,
       C.CUSTOMER_ID,
       C.CODE,
       C.CUSTOMER_CERT_TYPE
  FROM (SELECT T.CUSTOMER_NAME,
               T.CUSTOMER_GENDER,
               T.CUSTOMER_CERTI_CODE,
               T.CUSTOMER_BIRTHDAY,
               T.CUSTOMER_ID,
               T.CODE,
               T.CUSTOMER_CERT_TYPE
          FROM (SELECT TC.CUSTOMER_NAME,
                       TC.CUSTOMER_GENDER,
                       TC.CUSTOMER_CERTI_CODE,
                       TC.CUSTOMER_BIRTHDAY,
                       TC.CUSTOMER_CERT_TYPE,
                       TPH.CUSTOMER_ID,
                       TCT.CODE,
                       TCM.LIABILITY_STATE,
                       TA.MOBILE_TEL,
                       TCM.ORGAN_CODE,
                       TC.HOUSE_TEL
                  FROM APP___PAS__DBUSER.T_CUSTOMER TC
                  JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                    ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                  JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                    ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                  JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                    ON TPH.POLICY_ID = TCM.POLICY_ID
                  JOIN DEV_PAS.T_ADDRESS TA 
                  	ON TA.ADDRESS_ID = TPH.ADDRESS_ID
                ) T
         WHERE T.LIABILITY_STATE = 1
				 ]]>
		<include refid="queryCusInTel" />
		 UNION ALL
        SELECT A.CUSTOMER_NAME,
               A.CUSTOMER_GENDER,
               A.CUSTOMER_CERTI_CODE,
               A.CUSTOMER_BIRTHDAY,
               A.CUSTOMER_ID,
               A.CODE,
               A.CUSTOMER_CERT_TYPE
          FROM (SELECT TC.CUSTOMER_NAME,
                       TC.CUSTOMER_GENDER,
                       TC.CUSTOMER_CERTI_CODE,
                       TC.CUSTOMER_BIRTHDAY,
                       TC.CUSTOMER_CERT_TYPE,
                       TPH.CUSTOMER_ID,
                       TCT.CODE,
                       TCM.LIABILITY_STATE,
                       TC.MOBILE_TEL,
                       TCM.ORGAN_CODE,
                       TC.HOUSE_TEL
                  FROM APP___PAS__DBUSER.T_CUSTOMER TC
                  JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                    ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                  JOIN APP___PAS__DBUSER.T_CERTI_TYPE TCT
                    ON TC.CUSTOMER_CERT_TYPE = TCT.CODE
                  JOIN DEV_PAS.T_CONTRACT_MASTER TCM
                    ON TPH.POLICY_ID = TCM.POLICY_ID
                  JOIN DEV_PAS.T_ADDRESS TA 
                  	ON TA.ADDRESS_ID = TPH.ADDRESS_ID
                ) A
         WHERE A.LIABILITY_STATE = 4
         <include refid="queryCusInTelBJ" />
         
		 <![CDATA[ ) C
			 GROUP BY C.CUSTOMER_NAME,
			          C.CUSTOMER_GENDER,
			          C.CUSTOMER_CERTI_CODE,
			          C.CUSTOMER_BIRTHDAY,
			          C.CUSTOMER_ID,
			          C.CODE,
			          C.CUSTOMER_CERT_TYPE]]>
	</select>
</mapper>