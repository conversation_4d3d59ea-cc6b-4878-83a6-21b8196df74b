<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAllocatBnusDao">

<!-- 按条件查询 -->

 <sql id="AllocatBnus_startDate">
		<if test="startdate  != null  and startdate != '' "><![CDATA[  AND A.ALLOCATE_DATE > to_date(#{startdate}, 'yyyy-MM-dd') ]]></if>
</sql>	
	
<sql id="AllocatBnus_endDate">
		<if test="enddate  != null and enddate != ''"><![CDATA[ AND A.ALLOCATE_DATE < to_date(#{enddate}, 'yyyy-MM-dd') ]]></if>
</sql>		
	
<select id="queryAllocatBnus_cjk" resultType="java.util.Map" parameterType="java.util.Map">

 select B.SumBonusAmnt,D.SumBaseSBonusAmnt,E.SumSBonusAmnt,F.YBonusNo,G.SBonusNo,H.BonusCount,G.TotalBonusInterest
from 
 (select sum(BONUS_SA)as sumBonusAmnt 
  from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
 where A.POLICY_CODE = #{policy_code}
   and A.BONUS_ALLOT = '1'
   <include refid="AllocatBnus_startDate" />
   <include refid="AllocatBnus_endDate" />
   )B,(select sum(BONUS_SA) sumBaseSBonusAmnt
  from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
 where A.POLICY_CODE = #{policy_code}
   and A.BONUS_ALLOT = '2'
   <include refid="AllocatBnus_startDate" />
   <include refid="AllocatBnus_startDate" />
   )D, (select sum(BONUS_SA) sumSBonusAmnt
  from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
 where A.POLICY_CODE = #{policy_code}
   and A.BONUS_ALLOT = '6'
    <include refid="AllocatBnus_startDate" />
   <include refid="AllocatBnus_startDate" />
  )E,(select count(1) yBonusNo from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
 
 where A.POLICY_CODE = #{policy_code}
   and A.BONUS_ALLOT in ('1')
    <include refid="AllocatBnus_startDate" />
   <include refid="AllocatBnus_startDate" />
  )F,(select count(1) sBonusNo from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
 
 where A.POLICY_CODE = #{policy_code}
   and A.BONUS_ALLOT in ('2')
    <include refid="AllocatBnus_startDate" />
   <include refid="AllocatBnus_startDate" />
   )G,(select count(1) bonusCount from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
 
 where A.POLICY_CODE = #{policy_code}
   and A.BONUS_ALLOT in ('1','2')
    <include refid="AllocatBnus_startDate" />
   <include refid="AllocatBnus_startDate" />
   
   )H,(select sum(REISSUE_INTEREST) TotalBonusInterest 
  from APP___PAS__DBUSER.T_BONUS_ALLOCATE A where A.POLICY_CODE ='880161865625' )G 

</select>

<!-- 投保初始金额 -->
	<select id="queryAppBaseAmntbyFiscalyear" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
select SUM(STAND_AMOUNT)  INITIAL_AMOUNT  
    from APP___PAS__DBUSER.T_BONUS_ALLOCATE a 
    where POLICY_CODE=#{policy_code} and  extract(year from a.ALLOCATE_DUE_DATE) = (  
    select min(extract(year from ALLOCATE_DUE_DATE)) from APP___PAS__DBUSER.T_BONUS_ALLOCATE where POLICY_CODE=#{policy_code})
	and a.bonus_allot in('1','2','6')]]>
<if test="busiItemList  != null and busiItemList != ''">
  <![CDATA[ and a.busi_item_id in ]]>
            <foreach collection="busiItemList" item="busi_item_id" index="index" open="(" close=")" separator=",">
            	#{busi_item_id}
        	</foreach>
  </if>
	
	</select>
	
</mapper>
