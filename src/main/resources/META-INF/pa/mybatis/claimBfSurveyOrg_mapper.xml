<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.dao.IClaimBfSurveyOrgDao">

	<sql id="claimBfSurveyOrgWhereCondition">
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" rate  != null "><![CDATA[ AND A.RATE = #{rate} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" limit  != null "><![CDATA[ AND A.LIMIT = #{limit} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimBfSurveyOrgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	
	<sql id="queryClaimBfSurveyOrgByListIdPlanId">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimBfSurveyOrg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CLAIM_BF_SURVEY_ORG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG(
				INSERT_TIMESTAMP, ORGAN_CODE, RATE, UPDATE_BY, PLAN_ID, INSERT_TIME, LIST_ID, 
				LIMIT, UPDATE_TIME, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{rate, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} 
				, #{limit, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimBfSurveyOrg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG WHERE PLAN_ID = #{plan_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimBfSurveyOrg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG 
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    RATE = #{rate, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    LIMIT = #{limit, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,where 1 = 1 ]]>
		<include refid="queryClaimBfSurveyOrgByListIdPlanId" />
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimBfSurveyOrgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.RATE, A.PLAN_ID, A.LIST_ID, 
			A.LIMIT FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG A WHERE 1 = 1  ]]>
		<include refid="queryClaimBfSurveyOrgByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimBfSurveyOrg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.RATE, A.PLAN_ID, A.LIST_ID, 
			A.LIMIT FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimBfSurveyOrg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE,(SELECT O.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG O WHERE A.ORGAN_CODE = O.ORGAN_CODE ) AS ORGAN_NAME, A.RATE, A.PLAN_ID, A.LIST_ID, 
			A.LIMIT FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimBfSurveyOrgWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimBfSurveyOrgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimBfSurveyOrgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ORGAN_CODE, B.RATE, B.PLAN_ID, B.LIST_ID, 
			B.LIMIT FROM (
					SELECT ROWNUM RN, A.ORGAN_CODE, A.RATE, A.PLAN_ID, A.LIST_ID, 
			A.LIMIT FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 为新增查询所有 -->
	<select id="findClaimBfSurveyOrg4Add" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A 
			START WITH A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} 
			CONNECT BY PRIOR A.ORGAN_ID = A.UPORGAN_ID]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
	</select>
	
	<!-- 查询操作全部机构 -->
	<select id="findClaimBfSurveyOrg4AddTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM APP___PAS__DBUSER.T_UDMP_ORG A 
		 		where  A.ORGAN_CODE like '${organ_code}%' 
		 		ORDER BY A.ORGAN_CODE ]]>
	</select>
	<select id="findClaimBfSurveyOrg4AddForPage" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ select B.RN,
					       B.ORGAN_CODE,
					       B.ORGAN_NAME from (select rownum RN, A.ORGAN_CODE, A.ORGAN_NAME
	                       from APP___PAS__DBUSER.T_UDMP_ORG A
	                      where ROWNUM <= #{LESS_NUM} 
	                      and A.ORGAN_CODE like '${organ_code}%' 
	                      ORDER BY A.ORGAN_CODE) B where B.RN > #{GREATER_NUM}  ]]>
	</select>
	
	<sql id="findClaimBfSurveyOrgSelectedCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>
	
	<!-- 根据PLAN_ID查询所有已选择的机构 -->
	<select id="findClaimBfSurveyOrgSelected" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, B.ORGAN_NAME, A.RATE, A.LIMIT
			 FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG A
			 INNER JOIN APP___PAS__DBUSER.T_UDMP_ORG B ON A.ORGAN_CODE = B.ORGAN_CODE]]>
		<include refid="findClaimBfSurveyOrgSelectedCondition" />
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
	</select>
	
	<!-- 根据PLAN_ID查询所有未选择的机构 -->
	<select id="findClaimBfSurveyOrgUnselect" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A
			 WHERE A.ORGAN_CODE NOT IN 
			 (SELECT B.ORGAN_CODE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG B WHERE B.PLAN_ID = #{plan_id})
			 START WITH A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR}
			 CONNECT BY PRIOR A.ORGAN_ID = A.UPORGAN_ID]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
	</select>
	
	<!-- 查询未选中机构 -->
	<select id="findClaimBfSurveyOrgUnselectTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1)
				  FROM APP___PAS__DBUSER.T_UDMP_ORG A
				 WHERE A.ORGAN_CODE NOT IN
				       (SELECT B.ORGAN_CODE
				          FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG B
				         WHERE B.PLAN_ID = #{plan_id})
				   and A.ORGAN_CODE like '${organ_code}%' ]]>
	</select>
	<select id="findClaimBfSurveyOrgUnselectForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select RN,B.ORGAN_CODE, B.ORGAN_NAME
					from
					(SELECT rownum RN,A.ORGAN_CODE, A.ORGAN_NAME
					  FROM APP___PAS__DBUSER.T_UDMP_ORG A
					 WHERE rownum < #{LESS_NUM}
					       and A.ORGAN_CODE NOT IN
					       (SELECT B.ORGAN_CODE
					          FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG B
					         WHERE B.PLAN_ID = #{plan_id})
					   and A.ORGAN_CODE like '${organ_code}%'
					 ORDER BY A.ORGAN_CODE) B
					where B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- add zhangjy_wb 根据PLAN_ID查询所有未选择的机构 -->
	<select id="findUnSelectClaimBfSurveyOrg" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.ORGAN_CODE, A.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG A
			 WHERE A.ORGAN_CODE NOT IN 
			 (SELECT B.ORGAN_CODE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG B WHERE B.PLAN_ID = #{plan_id})]]>
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
	</select>
</mapper>
