<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFundTransApplyDao">
	
	<sql id="PA_fundTransApplyWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" target_id  != null "><![CDATA[ AND A.TARGET_ID = #{target_id} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ AND A.APPLY_TIME = #{apply_time} ]]></if>
		<if test=" capital_chg_id  != null "><![CDATA[ AND A.CAPITAL_CHG_ID = #{capital_chg_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" trans_type  != null "><![CDATA[ AND A.TRANS_TYPE = #{trans_type} ]]></if>
		<if test=" apply_units  != null "><![CDATA[ AND A.APPLY_UNITS = #{apply_units} ]]></if>
		<if test=" apply_amount  != null "><![CDATA[ AND A.APPLY_AMOUNT = #{apply_amount} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" application_date  != null  and  application_date  != ''  "><![CDATA[ AND A.APPLICATION_DATE = #{application_date} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" target_code != null and target_code != ''  "><![CDATA[ AND A.TARGET_CODE = #{target_code} ]]></if>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" fund_process_status != null and fund_process_status != ''  "><![CDATA[ AND A.FUND_PROCESS_STATUS = #{fund_process_status} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryFundTransApplyByApplyIdCondition">
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
	</sql>	
	<sql id="PA_queryFundTransApplyByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryFundTransApplyByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PA_queryFundTransApplyByFundCodeCondition">
		<if test=" fund_code != null and fund_code != '' "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
	</sql>	
	<sql id="PA_queryFundTransApplyByFundProcessStatusCondition">
		<if test=" fund_process_status != null and fund_process_status != '' "><![CDATA[ AND A.FUND_PROCESS_STATUS = #{fund_process_status} ]]></if>
	</sql>	
	<sql id="PA_queryFundTransApplyByProcessStatusCondition">
		<if test=" process_status != null and process_status != '' "><![CDATA[ AND A.PROCESS_STATUS = #{process_status} ]]></if>
	</sql>	
	<sql id="PA_queryFundTransApplyByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	

	<sql id="PA_queryFundTransApplyByApplyTimeCondition">
		<if test=" apply_time  != null "><![CDATA[ AND A.APPLY_TIME = #{apply_time} ]]></if>
	</sql>	
	<sql id="PA_queryFundTransApplyByCapitalChgIdCondition">
		<if test=" capital_chg_id  != null "><![CDATA[ AND A.CAPITAL_CHG_ID = #{capital_chg_id} ]]></if>
	</sql>	
	
	<sql id="PA_queryFundTransApplyByStreamInvestIdCondition">
		<if test=" stream_invest_id  != null "><![CDATA[ AND A.STREAM_INVEST_ID = #{stream_invest_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addFundTransApply"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="apply_id">
			SELECT APP___PAS__DBUSER.S_FUND_TRANS_APPLY__APPLY_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_TRANS_APPLY(
				MONEY_CODE, TARGET_ID, PRODUCT_ID, FUND_CODE, APPLY_TIME, CAPITAL_CHG_ID, 
				ITEM_ID, TRANS_TYPE, APPLY_UNITS, INSERT_TIMESTAMP, UPDATE_BY, APPLY_AMOUNT, LIST_ID, 
				BUSI_ITEM_ID, POLICY_ID, APPLY_ID, APPLICATION_DATE, INSERT_TIME, UPDATE_TIME, ACCEPT_CODE, 
				TARGET_CODE, TRANS_CODE, UPDATE_TIMESTAMP, FUND_PROCESS_STATUS, INSERT_BY ) 
			VALUES (
				#{money_code, jdbcType=VARCHAR}, #{target_id, jdbcType=NUMERIC} , #{product_id, jdbcType=NUMERIC} , #{fund_code, jdbcType=VARCHAR} , #{apply_time, jdbcType=DATE} , #{capital_chg_id, jdbcType=NUMERIC} 
				, #{item_id, jdbcType=NUMERIC} , #{trans_type, jdbcType=NUMERIC} , #{apply_units, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{apply_amount, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{apply_id, jdbcType=NUMERIC} , #{application_date, jdbcType=DATE} , SYSDATE , SYSDATE , #{accept_code, jdbcType=VARCHAR} 
				, #{target_code, jdbcType=VARCHAR} , #{trans_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{fund_process_status, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
			
   <!-- 查询部分领取所在保单年度的领取次数 -->
	<select id="PA_findTotalstTranApply" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1 ]]>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" start_date  != null  and  start_date  != '' and end_date != null and end_date != '' ">
		<![CDATA[ AND A.APPLY_TIME >= #{start_date} AND A.APPLY_TIME <= #{end_date} ]]></if>
	</select>

<!-- 删除操作 -->	
	<delete id="PA_deleteFundTransApply" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY WHERE APPLY_ID = #{apply_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateFundTransApply" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_TRANS_APPLY ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    TARGET_ID = #{target_id, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
			FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
		    APPLY_TIME = #{apply_time, jdbcType=DATE} ,
		    CAPITAL_CHG_ID = #{capital_chg_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    TRANS_TYPE = #{trans_type, jdbcType=NUMERIC} ,
		    APPLY_UNITS = #{apply_units, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    APPLY_AMOUNT = #{apply_amount, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    APPLICATION_DATE = #{application_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			TARGET_CODE = #{target_code, jdbcType=VARCHAR} ,
			TRANS_CODE = #{trans_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			FUND_PROCESS_STATUS = #{fund_process_status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_ID = #{apply_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findFundTransApplyByApplyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByApplyIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<select id="PA_findFundTransApplyByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByLogIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<select id="PA_findFundTransApplyByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByItemIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<select id="PA_findFundTransApplyByFundCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByFundCodeCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<select id="PA_findFundTransApplyByFundProcessStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByFundProcessStatusCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<select id="PA_findFundTransApplyByProcessStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByProcessStatusCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<select id="PA_findFundTransApplyByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	
	<select id="PA_findFundTransApplyByApplyTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByApplyTimeCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<select id="PA_findFundTransApplyByCapitalChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByCapitalChgIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	
	<select id="PA_findFundTransApplyByStreamInvestId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_queryFundTransApplyByStreamInvestIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapFundTransApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllFundTransApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE ROWNUM <=  1000  ]]>
		 <include refid="PA_fundTransApplyWhereCondition" />
		<![CDATA[ ORDER BY A.ACCEPT_CODE,A.APPLY_TIME DESC ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findFundTransApplyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE 1 = 1  ]]>
		<include refid="PA_fundTransApplyWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryFundTransApplyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MONEY_CODE, B.TARGET_ID, B.PRODUCT_ID, B.FUND_CODE, B.APPLY_TIME, B.CAPITAL_CHG_ID, 
			B.ITEM_ID, B.TRANS_TYPE, B.APPLY_UNITS, B.APPLY_AMOUNT, B.LIST_ID, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.APPLY_ID, B.APPLICATION_DATE, B.ACCEPT_CODE, 
			B.TARGET_CODE, B.TRANS_CODE, B.FUND_PROCESS_STATUS FROM (
					SELECT ROWNUM RN, A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_fundTransApplyWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="PA_findAllFundTransApplyAndTransByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.TARGET_ID, A.PRODUCT_ID, A.FUND_CODE, A.APPLY_TIME, A.CAPITAL_CHG_ID, 
			A.ITEM_ID, A.TRANS_TYPE, A.APPLY_UNITS, A.APPLY_AMOUNT, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, A.APPLICATION_DATE, A.ACCEPT_CODE, 
			A.TARGET_CODE, A.TRANS_CODE, A.FUND_PROCESS_STATUS,
	       B.TRANS_PRICE,
	       B.TRANS_AMOUNT,
	       B.TRANS_UNITS as UNITS,
	       B.DEAL_TIME
	  FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A, APP___PAS__DBUSER.T_FUND_TRANS B
	 WHERE A.POLICY_ID = #{policy_id}
	   AND A.BUSI_ITEM_ID = #{busi_item_id}
	   AND A.TRANS_CODE = #{trans_code}
	   AND A.LIST_ID=#{list_id}
	   AND A.FUND_PROCESS_STATUS = #{fund_process_status}
	   AND A.APPLY_ID = B.APPLY_ID
	   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
	   AND A.TRANS_CODE = B.TRANS_CODE
  ]]>
		<![CDATA[ ORDER BY A.APPLY_TIME DESC ]]> 
	</select>
	
	<!-- leihong  查询是否发生了引起单位数变化的   保全操作 -->
	<select id="PA_findApplyCountForCus" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A 
				WHERE 1 = 1	
				 AND A.FUND_PROCESS_STATUS = 1
				 AND A.TRANS_CODE in ('06','07','08','09','13','14','21','22','23')
				 AND A.POLICY_ID = #{policy_id} 
				 AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>				
	</select>	
	
</mapper>
