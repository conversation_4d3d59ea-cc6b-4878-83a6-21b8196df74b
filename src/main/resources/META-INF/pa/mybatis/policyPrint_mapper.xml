<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyPrintDao">
<!--
	<sql id="PA_policyPrintWhereCondition">
		<if test=" error_content != null and error_content != ''  "><![CDATA[ AND A.ERROR_CONTENT = #{error_content} ]]></if>
		<if test=" bpo_print_date  != null  and  bpo_print_date  != ''  "><![CDATA[ AND A.BPO_PRINT_DATE = #{bpo_print_date} ]]></if>
		<if test=" print_time  != null  and  print_time  != ''  "><![CDATA[ AND A.PRINT_TIME = #{print_time} ]]></if>
		<if test=" content_clob_id  != null "><![CDATA[ AND A.CONTENT_CLOB_ID = #{content_clob_id} ]]></if>
		<if test=" bpo_print_com  != null "><![CDATA[ AND A.BPO_PRINT_COM = #{bpo_print_com} ]]></if>
		<if test=" reissue_flag  != null "><![CDATA[ AND A.REISSUE_FLAG = #{reissue_flag} ]]></if>
		<if test=" print_status != null and print_status != ''  "><![CDATA[ AND A.PRINT_STATUS = #{print_status} ]]></if>
		<if test=" print_id  != null "><![CDATA[ AND A.PRINT_ID = #{print_id} ]]></if>
		<if test=" print_org != null and print_org != ''  "><![CDATA[ AND A.PRINT_ORG = #{print_org} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" error_code != null and error_code != ''  "><![CDATA[ AND A.ERROR_CODE = #{error_code} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" print_type != null and print_type != ''  "><![CDATA[ AND A.PRINT_TYPE = #{print_type} ]]></if>
		<if test=" print_times  != null "><![CDATA[ AND A.PRINT_TIMES = #{print_times} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyPrintByPrintIdCondition">
		<if test=" print_id  != null "><![CDATA[ AND A.PRINT_ID = #{print_id} ]]></if>
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyPrint"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_PRINT(
				ERROR_CONTENT, BPO_PRINT_DATE, PRINT_TIME, CONTENT_CLOB_ID, BPO_PRINT_COM, REISSUE_FLAG, PRINT_STATUS, 
				INSERT_TIME, UPDATE_TIME, PRINT_ID, PRINT_ORG, APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, ERROR_CODE, UPDATE_TIMESTAMP, POLICY_CHG_ID, PRINT_TYPE, INSERT_BY, PRINT_TIMES ) 
			VALUES (
				#{error_content, jdbcType=VARCHAR}, #{bpo_print_date, jdbcType=DATE} , #{print_time, jdbcType=DATE} , #{content_clob_id, jdbcType=NUMERIC} , #{bpo_print_com, jdbcType=NUMERIC} , #{reissue_flag, jdbcType=NUMERIC} , #{print_status, jdbcType=VARCHAR} 
				, SYSDATE , SYSDATE , #{print_id, jdbcType=NUMERIC} , #{print_org, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{error_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{print_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{print_times, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyPrint" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_PRINT WHERE PRINT_ID = #{print_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyPrint" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_PRINT ]]>
		<set>
		<trim suffixOverrides=",">
			ERROR_CONTENT = #{error_content, jdbcType=VARCHAR} ,
		    BPO_PRINT_DATE = #{bpo_print_date, jdbcType=DATE} ,
		    PRINT_TIME = #{print_time, jdbcType=DATE} ,
		    CONTENT_CLOB_ID = #{content_clob_id, jdbcType=NUMERIC} ,
		    BPO_PRINT_COM = #{bpo_print_com, jdbcType=NUMERIC} ,
		    REISSUE_FLAG = #{reissue_flag, jdbcType=NUMERIC} ,
			PRINT_STATUS = #{print_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			PRINT_ORG = #{print_org, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			ERROR_CODE = #{error_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			PRINT_TYPE = #{print_type, jdbcType=VARCHAR} ,
		    PRINT_TIMES = #{print_times, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE PRINT_ID = #{print_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyPrintByPrintId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ERROR_CONTENT, A.BPO_PRINT_DATE, A.PRINT_TIME, A.CONTENT_CLOB_ID, A.BPO_PRINT_COM, A.REISSUE_FLAG, A.PRINT_STATUS, 
			A.PRINT_ID, A.PRINT_ORG, A.APPLY_CODE, A.POLICY_CODE, 
			A.ERROR_CODE, A.POLICY_CHG_ID, A.PRINT_TYPE, A.PRINT_TIMES FROM APP___PAS__DBUSER.T_POLICY_PRINT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyPrintByPrintIdCondition" />
		<![CDATA[ ORDER BY A.PRINT_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyPrint" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ERROR_CONTENT, A.BPO_PRINT_DATE, A.PRINT_TIME, A.CONTENT_CLOB_ID, A.BPO_PRINT_COM, A.REISSUE_FLAG, A.PRINT_STATUS, 
			A.PRINT_ID, A.PRINT_ORG, A.APPLY_CODE, A.POLICY_CODE, 
			A.ERROR_CODE, A.POLICY_CHG_ID, A.PRINT_TYPE, A.PRINT_TIMES FROM APP___PAS__DBUSER.T_POLICY_PRINT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.PRINT_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyPrint" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ERROR_CONTENT, A.BPO_PRINT_DATE, A.PRINT_TIME, A.CONTENT_CLOB_ID, A.BPO_PRINT_COM, A.REISSUE_FLAG, A.PRINT_STATUS, 
			A.PRINT_ID, A.PRINT_ORG, A.APPLY_CODE, A.POLICY_CODE, 
			A.ERROR_CODE, A.POLICY_CHG_ID, A.PRINT_TYPE, A.PRINT_TIMES FROM APP___PAS__DBUSER.T_POLICY_PRINT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="PA_queryPolicyPrintByPrintIdCondition" />
		<![CDATA[ ORDER BY A.PRINT_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyPrintTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_PRINT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyPrintForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ERROR_CONTENT, B.BPO_PRINT_DATE, B.PRINT_TIME, B.CONTENT_CLOB_ID, B.BPO_PRINT_COM, B.REISSUE_FLAG, B.PRINT_STATUS, 
			B.PRINT_ID, B.PRINT_ORG, B.APPLY_CODE, B.POLICY_CODE, 
			B.ERROR_CODE, B.POLICY_CHG_ID, B.PRINT_TYPE, B.PRINT_TIMES FROM (
					SELECT ROWNUM RN, A.ERROR_CONTENT, A.BPO_PRINT_DATE, A.PRINT_TIME, A.CONTENT_CLOB_ID, A.BPO_PRINT_COM, A.REISSUE_FLAG, A.PRINT_STATUS, 
			A.PRINT_ID, A.PRINT_ORG, A.APPLY_CODE, A.POLICY_CODE, 
			A.ERROR_CODE, A.POLICY_CHG_ID, A.PRINT_TYPE, A.PRINT_TIMES FROM APP___PAS__DBUSER.T_POLICY_PRINT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.PRINT_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询打印结果 -->
	<select id="PA_queryPaperSupplement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select ppa.APPLY_TIME,
				  ppa.APPLY_STATUS,
				  ppa.ERROR_CONTENT
				  from APP___PAS__DBUSER.T_POLICY_PRINT pp
				  left join APP___PAS__DBUSER.T_POLICY_PRINT_APPLY ppa
				    on pp.PRINT_ID = ppa.PRINT_ID
				 where pp.policy_code = #{policy_code} ]]>
	</select>
	<!-- 查询业务员信息明细 -->
	<select id="PA_findAgentCodeAll" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.AGENT_CODE,
       A.SALES_ORGAN_CODE,
		(SELECT Y.SALES_CHANNEL_NAME FROM APP___PAS__DBUSER.T_SALES_CHANNEL Y WHERE A.AGENT_CHANNEL = Y.SALES_CHANNEL_CODE) AS AGENT_CHANNEL_NAME,
       A.POSTAL_ADDRESS,
       A.HOME_ADDRESS,
       A.AGENT_EMAIL,
       A.AGENT_MOBILE,
       A.AGENT_PHONE,
       A.BIRTHDAY,
       A.AGENT_GENDER,
       A.CERTI_CODE,
       A.CERT_TYPE,
       A.EMPLOYEE_FLAG,
       A.AGENT_CHANNEL,
       A.AGENT_NORMAL_TYPE,
       A.AGENT_ORGAN_CODE,
       A.PERSISTENCE_MONTH,
       A.AGENT_LEVEL,
       A.AGENT_LEVEL_ITEM,
       A.SGENT_HORNER_LEVEL,
       A.AGENT_STATUS,
       A.EMPLOYMENT_DATE,
       A.DISMISSAL_DATE,
       A.REMARK,
       A.USER_CODE,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.QUAF_NO,
       A.AGENT_GRADE,
       A.PFM_MANAGE_COM,
       A.SPARE,
       A.POL_SERVICE_COM,
       A.AGENT_COM,
       A.AGENT_NO,
       A.MANAGE_COM_OUTER,
       A.GROUP_CODE,
       A.ENTRY_NO FROM  APP___PAS__DBUSER.T_AGENT A  WHERE 1=1]]>
	 <if test=" agent_code != null and agent_code != ''  ">AND A.AGENT_CODE= #{agent_code}</if>
	</select>
</mapper>
