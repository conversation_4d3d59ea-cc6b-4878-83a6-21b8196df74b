<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.sendmail.dao.impl.SendMailDaoImpl">

	<!-- 新华保险续期交费提醒函 -->
	<select id="renewPayRemindMail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT distinct tcm.policy_id,
                tcm.policy_code,
                tpa.holder_name,
                tpa.due_time as pay_due_date,
                tpa.bank_account as bank_code,
                tpa.holder_id as holderCode,
                tpa.pay_end_date,
                tpa.organ_code,
                TCA.AGENT_CODE,
                TCA.AGENT_NAME,
                TCA.AGENT_MOBILE,
                (select tc.email
                   from APP___PAS__DBUSER.t_policy_holder tph,
                        APP___PAS__DBUSER.t_customer      tc
                  where tcm.policy_id = tph.policy_id
                    AND tph.customer_id = tc.customer_id) holder_email,
                (select tc.customer_gender
                   from APP___PAS__DBUSER.t_policy_holder tph,
                        APP___PAS__DBUSER.t_customer      tc
                  where tcm.policy_id = tph.policy_id
                    AND tph.customer_id = tc.customer_id) gender,
                (select ta.agent_name
                   from APP___PAS__DBUSER.t_agent ta
                  where tpa.agent_code = ta.agent_code) service_name,
                (select ta.agent_mobile
                   from APP___PAS__DBUSER.t_agent ta
                  where tpa.agent_code = ta.agent_code) service_tel,
                (select nvl(sum(tpas.fee_amount), 0)
                   from dev_pas.t_prem_arap tpas
                  where tpas.policy_code = tpa.policy_code
                    and ((tpas.FEE_TYPE = 'G003010000') or
                        (tpas.fee_type = 'G003020100') or
                        (tpas.fee_type = 'G003030100') or
                        (tpas.fee_type = 'G003040100') or
                        (tpas.fee_type = 'G003020200') or
                        (tpas.fee_type = 'G003030200') or
                        (tpas.fee_type = 'G003030300') or
                        (tpas.fee_type = 'G003040200') or
                        (tpas.fee_type = 'P003050000') or
                        (tpas.fee_type = 'G003060000') or
                        (tpas.fee_type = 'G003070000') or
                        (tpas.fee_type = 'G003080000'))) prem
  FROM APP___PAS__DBUSER.t_contract_master tcm,
       APP___PAS__DBUSER.t_prem_arap       tpa,
       DEV_PAS.T_CONTRACT_AGENT TCA
 WHERE tcm.policy_code = tpa.policy_code
   AND tpa.due_time = #{batchTime,jdbcType=DATE}
   AND tpa.fee_status = '00'
   AND TCA.POLICY_ID = TCM.POLICY_ID
   AND ROWNUM <= 1000
		]]>
	</select>

	<!-- 查询自垫相关信息 -->
	<select id="findSelfCushion" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
		select tpa.busi_prod_code,tpa.busi_prod_name,tpa.policy_code,tpa.fee_amount,
          tpas.CAPITAL_BALANCE,tpas.INTEREST_BALANCE,
          tpas.BALANCE_DATE,tpa.due_time
          from  APP___PAS__DBUSER.t_contract_master tcm
          left join APP___PAS__DBUSER.t_prem_arap tpa on  tpa.policy_code = tcm.policy_code 
          left join APP___PAS__DBUSER.t_policy_account_stream tpas on tcm.policy_id = tpas.policy_id
          where 
           tpa.fee_status='00'
           AND tcm.policy_id = #{policy_id}
	]]>
	</select>

	<!-- 查询保单险的险种及险种的标准保费 -->
	<select id="findBusiProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT TCE.NEXT_PREM,TBP.PRODUCT_ABBR_NAME,TCE.EXTRACTION_DUE_DATE
			  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP, DEV_PAS.T_CONTRACT_EXTEND TCE,
			  DEV_PAS.T_BUSINESS_PRODUCT TBP 
			 WHERE TCBP.POLICY_CODE = #{policy_code}
			 AND TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			 AND TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
		]]>
	</select>

	<!-- 生日祝福邮件查询内容 -->
	<select id="birthdayWishMail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT TC.CUSTOMER_BIRTHDAY, TC.CUSTOMER_GENDER, TC.CUSTOMER_NAME,TC.EMAIL,TC.CUSTOMER_ID
        FROM DEV_PAS.T_CONTRACT_MASTER TCM,
             DEV_PAS.T_POLICY_HOLDER   TPH,
             DEV_PAS.T_CUSTOMER        TC
       WHERE TCM.LIABILITY_STATE = '1'
         AND TPH.POLICY_ID = TCM.POLICY_ID
         AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
         AND TO_CHAR(TC.CUSTOMER_BIRTHDAY, 'MM-dd') =
             TO_CHAR(#{batchTime, jdbcType = DATE}, 'MM-dd')
             AND ROWNUM <= 1000
		]]>
	</select>

	<!-- 查询失效邮件相关数据 -->
	<select id="lapseMail" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				SELECT TD.INSERT_TIME, TC.EMAIL,TC.CUSTOMER_NAME,TC.CUSTOMER_GENDER,TCBP.POLICY_CODE,TCBP.BUSI_PROD_CODE,TBP.PRODUCT_CATEGORY1,TCA.AGENT_CODE,TCA.AGENT_NAME
  FROM DEV_PAS.T_DOCUMENT TD,
  DEV_PAS.T_POLICY_HOLDER TPH,
  DEV_PAS.T_CUSTOMER TC,
  DEV_PAS.T_BUSINESS_PRODUCT TBP,
  DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
  DEV_PAS.T_CONTRACT_AGENT TCA
 WHERE TD.INSERT_TIME = #{batchTime, jdbcType = DATE}
   AND TD.TEMPLATE_CODE = 'PAS_00001'
   AND TPH.POLICY_ID = TD.POLICY_ID
   AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
   AND TCBP.POLICY_ID = TD.POLICY_ID
   AND TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
   and tca.policy_id = tcbp.policy_id
			]]>
	</select>

	<!-- 贷款预终止邮件提醒 -->
	<select id="loanPreStopMail" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[
				             SELECT distinct TCBP.POLICY_CODE,
                             TPA.INTEREST_CAPITAL,
                             TCBP.EXPIRY_DATE,
                             TC.CUSTOMER_NAME,
                             TC.EMAIL,
                             TC.CUSTOMER_GENDER,
                             TPA.CAPITAL_BALANCE
               FROM DEV_PAS.T_POLICY_ACCOUNT     TPA,
                    DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                    DEV_PAS.T_POLICY_HOLDER      TPH,
                    DEV_PAS.T_CUSTOMER           TC
              WHERE TCBP.EXPIRY_DATE + 1 = #{batchTime,
              jdbcType = DATE}
                AND TCBP.END_CAUSE = '06'
                AND TCBP.POLICY_ID = TPA.POLICY_ID
                AND TPH.POLICY_ID = TCBP.POLICY_ID
                AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                AND TCBP.INSERT_BY = TPA.INSERT_BY
			]]>
	</select>

	<!-- 贷款终止邮件提醒 -->
	<select id="loanStopMail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT distinct TCBP.POLICY_CODE,
                             TPA.INTEREST_CAPITAL,
                             TCBP.EXPIRY_DATE,
                             TC.CUSTOMER_NAME,
                             TC.EMAIL,
                             TC.CUSTOMER_GENDER,
                             TPA.CAPITAL_BALANCE
               FROM DEV_PAS.T_POLICY_ACCOUNT     TPA,
                    DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                    DEV_PAS.T_POLICY_HOLDER      TPH,
                    DEV_PAS.T_CUSTOMER           TC
              WHERE TCBP.EXPIRY_DATE + 1 = #{batchTime,
              jdbcType = DATE}
                AND TCBP.END_CAUSE = '06'
                AND TCBP.POLICY_ID = TPA.POLICY_ID
                AND TPH.POLICY_ID = TCBP.POLICY_ID
                AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                AND TCBP.INSERT_BY = TPA.INSERT_BY
		]]>
	</select>

	<!-- 满期终止邮件提醒 -->
	<select id="maturityMail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT TC.CUSTOMER_NAME,TC.EMAIL,TC.CUSTOMER_GENDER,TCBP.MATURITY_DATE,TPH.POLICY_CODE,TCA.AGENT_NAME,TCA.AGENT_CODE
		  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
		       DEV_PAS.T_POLICY_HOLDER      TPH,
		       DEV_PAS.T_CUSTOMER           TC,
		       DEV_PAS.T_CONTRACT_AGENT TCA
		 WHERE TCBP.MATURITY_DATE = #{batchTime,jdbcType = DATE}
		 AND TPH.POLICY_ID = TCBP.POLICY_ID 
		 AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
		 AND TCA.POLICY_ID = TCBP.POLICY_ID
		 and rownum <= 1000
		]]>
	</select>

	<!-- 查询保单账户价值 -->
	<select id="PA_findAllInvestUnitPrice" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT TF.SETTLE_DATE,TF.BALANCE FROM( SELECT TFS.SETTLE_DATE,TFS.BALANCE FROM DEV_PAS.T_CONTRACT_INVEST TCI ,
         DEV_PAS.T_FUND_SETTLEMENT TFS
         WHERE TCI.POLICY_ID = #{policyId}
         AND TFS.INVEST_ID = TCI.LIST_ID
         AND TFS.SETTLE_DATE <= #{batchTime,jdbcType = DATE}
         ORDER BY TFS.SETTLE_DATE DESC) TF WHERE ROWNUM = 1
		]]>
	</select>
	
	<!-- 红利分配邮件提醒 -->
	<select id="bonusAllotMail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT (TD.POLICY_CODE) ,TC.EMAIL, TC.CUSTOMER_NAME,TC.CUSTOMER_GENDER,TRRA.INSURED_NAME,TCBP.VALIDATE_DATE,TCE.NEXT_PREM,TCA.AGENT_CODE,TCA.AGENT_NAME,TCE.POLICY_PERIOD,TCBP.BUSI_ITEM_ID
  FROM DEV_PAS.T_DOCUMENT      TD,
       DEV_PAS.T_POLICY_HOLDER TPH,
       DEV_PAS.T_CUSTOMER      TC,
       DEV_PAS.T_RENEW_REVERSAL_APPLY TRRA,
       DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
       DEV_PAS.T_CONTRACT_PRODUCT TCP,
       DEV_PAS.T_CONTRACT_AGENT TCA,
       DEV_PAS.T_CONTRACT_EXTEND TCE 
 WHERE TD.TEMPLATE_CODE = 'PAS_00010'
   AND TD.CREATE_TIME =  #{batchTime,jdbcType = DATE}
   AND TPH.POLICY_ID = TD.POLICY_ID
   AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
   AND TRRA.POLICY_ID = TPH.POLICY_ID
   AND TCBP.POLICY_ID = TRRA.POLICY_ID
   AND TCP.POLICY_ID = TCBP.POLICY_ID
   AND TCA.POLICY_ID = TCBP.POLICY_ID
   AND TCE.POLICY_CODE = TCBP.POLICY_CODE
   AND TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		]]>
	</select>
	
	<!-- 身份证过期邮件提醒 -->
	<select id="certiExpiredMail"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT    A.CUSTOMER_NAME CERTEDNAME,
					  A.CUSTOMER_CERT_TYPE CERTEDTYPE,
					  A.CUSTOMER_CERTI_CODE CERTEDCODE,
					  TO_CHAR(A.CUST_CERT_END_DATE,'YYYY-MM-DD') CERTEDTIME,
					  (SELECT BB.EMAIL FROM APP___PAS__DBUSER.T_POLICY_HOLDER AA,APP___PAS__DBUSER.T_CUSTOMER BB WHERE AA.CUSTOMER_ID=BB.CUSTOMER_ID
	                  AND AA.POLICY_CODE= A.POLICY_CODE ) EMAIL,
	                  A.CUSTOMER_ID,
					  SUBSTR(TCMM.ORGAN_CODE,0,4) FILIALE_ID,
					  SUBSTR(TCMM.ORGAN_CODE,0,6) CORE_BRANCH_COM_ID,
					  TCMM.ORGAN_CODE ORGAN_CODE,
					  SUBSTR(A.POLICY_CODE,-4) POLICY_FOUR,
					  A.POLICY_CODE
					  
             FROM (
						 
                SELECT B.CUSTOMER_ID,B.CUST_CERT_END_DATE,B.CUSTOMER_NAME,B.CUSTOMER_BIRTHDAY,B.CUSTOMER_GENDER,B.CUSTOMER_CERT_TYPE,B.CUSTOMER_CERTI_CODE,MAX(B.POLICY_CODE) POLICY_CODE
                    FROM (
					 
					SELECT INFO.CUSTOMER_ID,INFO.CUST_CERT_END_DATE,INFO.CUSTOMER_NAME,INFO.CUSTOMER_BIRTHDAY,INFO.CUSTOMER_GENDER,INFO.CUSTOMER_CERT_TYPE,INFO.CUSTOMER_CERTI_CODE,INFO.ENDATE,TT.POLICY_CODE 
					FROM DEV_PAS.T_CONTRACT_MASTER TT,DEV_PAS.T_POLICY_HOLDER TPHH, ( 				
	            		SELECT TC.CUSTOMER_ID,TC.CUST_CERT_END_DATE,TC.CUSTOMER_NAME,TC.CUSTOMER_BIRTHDAY,TC.CUSTOMER_GENDER,TC.CUSTOMER_CERT_TYPE,TC.CUSTOMER_CERTI_CODE,MAX(TCM.VALIDATE_DATE) ENDATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM , 
	                    APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP, 
	                    APP___PAS__DBUSER.T_POLICY_HOLDER TPH,  
	                    APP___PAS__DBUSER.T_CUSTOMER TC,       
	                    APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBPP
	                    WHERE TCM.POLICY_ID = TCBP.POLICY_ID
	                          AND TCM.POLICY_ID = TPH.POLICY_ID
	                          AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
	                          AND TCBP.BUSI_PRD_ID = TBPP.BUSINESS_PRD_ID
	                          AND TBPP.COVER_PERIOD_TYPE=0    
	                          AND TC.CUST_CERT_END_DATE = #{batchTime}
	                          AND TCM.LIABILITY_STATE='1'    
	                          GROUP BY TC.CUSTOMER_ID,TC.CUST_CERT_END_DATE,TC.CUSTOMER_NAME,TC.CUSTOMER_BIRTHDAY,TC.CUSTOMER_GENDER,TC.CUSTOMER_CERT_TYPE,TC.CUSTOMER_CERTI_CODE   
		            ) INFO WHERE TT.POLICY_ID=TPHH.POLICY_ID
		            AND TPHH.CUSTOMER_ID=INFO.CUSTOMER_ID AND TT.VALIDATE_DATE=INFO.ENDATE  
						
							UNION ALL
							
					SELECT INFO.CUSTOMER_ID,INFO.CUST_CERT_END_DATE,INFO.CUSTOMER_NAME,INFO.CUSTOMER_BIRTHDAY,INFO.CUSTOMER_GENDER,INFO.CUSTOMER_CERT_TYPE,INFO.CUSTOMER_CERTI_CODE,INFO.ENDATE,TT.POLICY_CODE 
					FROM DEV_PAS.T_CONTRACT_MASTER TT,DEV_PAS.T_INSURED_LIST TPHH, ( 		
	            		SELECT TC.CUSTOMER_ID,TC.CUST_CERT_END_DATE,TC.CUSTOMER_NAME,TC.CUSTOMER_BIRTHDAY,TC.CUSTOMER_GENDER,TC.CUSTOMER_CERT_TYPE,TC.CUSTOMER_CERTI_CODE,MAX(TCM.VALIDATE_DATE) ENDATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM , 
	                    APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,  
	                    APP___PAS__DBUSER.T_INSURED_LIST TIL,   
	                    APP___PAS__DBUSER.T_CUSTOMER TC,       
	                    APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBPP
	                    WHERE TCM.POLICY_ID = TCBP.POLICY_ID
	                          AND TCM.POLICY_ID = TIL.POLICY_ID
	                          AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
	                          AND TCBP.BUSI_PRD_ID = TBPP.BUSINESS_PRD_ID
	                          AND TBPP.COVER_PERIOD_TYPE=0   
	                          AND TC.CUST_CERT_END_DATE = #{batchTime}
	                          AND TCM.LIABILITY_STATE='1'    
	                          GROUP BY TC.CUSTOMER_ID,TC.CUST_CERT_END_DATE,TC.CUSTOMER_NAME,TC.CUSTOMER_BIRTHDAY,TC.CUSTOMER_GENDER,TC.CUSTOMER_CERT_TYPE,TC.CUSTOMER_CERTI_CODE            
		           ) INFO WHERE TT.POLICY_ID=TPHH.POLICY_ID
		            AND TPHH.CUSTOMER_ID=INFO.CUSTOMER_ID AND TT.VALIDATE_DATE=INFO.ENDATE  
					 
					  UNION ALL
           
					SELECT INFO.CUSTOMER_ID,INFO.CUST_CERT_END_DATE,INFO.CUSTOMER_NAME,INFO.CUSTOMER_BIRTHDAY,INFO.CUSTOMER_GENDER,INFO.CUSTOMER_CERT_TYPE,INFO.CUSTOMER_CERTI_CODE,INFO.ENDATE,TT.POLICY_CODE 
					FROM DEV_PAS.T_CONTRACT_MASTER TT,DEV_PAS.T_CONTRACT_BENE TPHH, ( 
					    SELECT TC.CUSTOMER_ID,TC.CUST_CERT_END_DATE,TC.CUSTOMER_NAME,TC.CUSTOMER_BIRTHDAY,TC.CUSTOMER_GENDER,TC.CUSTOMER_CERT_TYPE,TC.CUSTOMER_CERTI_CODE,MAX(TCM.VALIDATE_DATE) ENDATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM ,
	                    APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,  
	                    APP___PAS__DBUSER.T_CONTRACT_BENE TCB,   
	                    APP___PAS__DBUSER.T_CUSTOMER TC,        
	                    APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBPP
	                    WHERE TCM.POLICY_ID = TCBP.POLICY_ID
	                          AND TCM.POLICY_ID = TCB.POLICY_ID
	                          AND TCB.CUSTOMER_ID = TC.CUSTOMER_ID
	                          AND TCBP.BUSI_PRD_ID = TBPP.BUSINESS_PRD_ID
	                          AND TBPP.COVER_PERIOD_TYPE=0    
	                          AND TC.CUST_CERT_END_DATE = #{batchTime}
	                          AND TCM.LIABILITY_STATE='1'    
	                          GROUP BY TC.CUSTOMER_ID,TC.CUST_CERT_END_DATE,TC.CUSTOMER_NAME,TC.CUSTOMER_BIRTHDAY,TC.CUSTOMER_GENDER,TC.CUSTOMER_CERT_TYPE,TC.CUSTOMER_CERTI_CODE
		             ) INFO WHERE TT.POLICY_ID=TPHH.POLICY_ID
		            AND TPHH.CUSTOMER_ID=INFO.CUSTOMER_ID AND TT.VALIDATE_DATE=INFO.ENDATE               
													
					) B GROUP BY B.CUSTOMER_ID,B.CUST_CERT_END_DATE,B.CUSTOMER_NAME,B.CUSTOMER_BIRTHDAY,B.CUSTOMER_GENDER,B.CUSTOMER_CERT_TYPE,B.CUSTOMER_CERTI_CODE ) A
				LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCMM 
                ON A.POLICY_CODE=TCMM.POLICY_CODE 
		]]>
	</select>
	
</mapper>