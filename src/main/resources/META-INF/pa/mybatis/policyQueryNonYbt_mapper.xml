<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyQueryNonYbtDao">

	<!-- 非银保通出单查询主信息 -->
	<select id="PA_findNonYbtByDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select tcm.policy_code,
				       tcm.apply_code,
				       tcm.ISSUE_DATE,
				       tcm.policy_id,
				       tcm.validate_date,
				       tcm.liability_state,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.SUBINPUT_TYPE,
				       tcm.service_bank_branch,
				       tcm.SUBMIT_CHANNEL,
				       tcm.CHANNEL_TYPE,
				       tcm.policy_pwd,
				       tpac.pay_next,
				       tpac.next_account_bank,
				       tpac.next_account,
				       sum(tcp.total_prem_af) total_prem_af,
				       sum(tcp.initial_amount) initial_amount
				  from dev_pas.t_contract_master tcm
				  join dev_pas.t_contract_product tcp
				    on tcp.policy_code = tcm.policy_code
				  join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.busi_item_id = tcp.busi_item_id
				  join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  where 1 = 1 
				   and (tcm.submit_channel in (3,4) or (tcm.submit_channel = 5 and tcm.subinput_type in (18,19))) ]]> 
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and tcm.issue_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and tcm.issue_date < #{batch_end_date} + 1 ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tcm.channel_type = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list != ''">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[    group by tcm.policy_code,
					          tcm.apply_code,
					          tcm.policy_id,
					          tcm.ISSUE_DATE,
					          tcm.validate_date,
					          tcm.liability_state,
					          tcm.expiry_date,
					          tcm.apply_date,
					          tcm.service_bank_branch,
					          tcm.SUBINPUT_TYPE,
					          tcm.SUBMIT_CHANNEL,
					          tcm.CHANNEL_TYPE,
					          tcm.policy_pwd,
					          tpac.pay_next,
					          tpac.next_account_bank,
					          tpac.next_account]]>
	</select>

	<!-- 查询保单的生存金、分红、满期金领取方式以及主险、投保人等信息 -->
	<select id="PA_findNonYbtByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		         select distinct tcm.policy_code,
		                tcp.total_prem_af,
		                tcp.prem_freq,
		                tcp.charge_year,
		                tcp.charge_period,
		                tcp.coverage_year,
		                tcp.coverage_period,
		                tcp.unit,
		                tbp.hesitation2acc,
		                tph.customer_id,
		                tph.address_id,
		                (select distinct tpp.survival_mode
		                   from dev_pas.t_pay_plan tpp
		                  where tpp.policy_code = tcm.policy_code
		                    and tpp.pay_plan_type = 3 and rownum = 1) survival_mode,
		                (select distinct tpp.survival_mode
		                   from dev_pas.t_pay_plan tpp
		                  where tpp.policy_code = tcm.policy_code
		                    and tpp.pay_plan_type = 1 and rownum = 1) bonus_mode,
		                (select distinct tpp.survival_mode
		                   from dev_pas.t_pay_plan tpp
		                  where tpp.policy_code = tcm.policy_code
		                    and tpp.pay_plan_type = 4 and rownum = 1) maturity_mode
		  from dev_pas.t_contract_master tcm
		  join dev_pas.t_contract_busi_prod tbp
		    on tcm.policy_code = tbp.policy_code
		  join dev_pas.t_contract_product tcp
		    on tbp.busi_item_id = tcp.busi_item_id
		  left join dev_pas.t_pay_plan tpp
		    on tcm.policy_code = tpp.policy_code
		  join dev_pas.t_policy_holder tph
		    on tph.policy_code = tcm.policy_code
		 where 1 = 1
		   and tbp.master_busi_item_id is null
		   and tcm.policy_id = #{policy_id}]]>
	</select>


	<!-- 查询保单下的附加险信息 -->
	<select id="PA_findNonYbtRiderInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		        select distinct tcm.policy_code,
				                tbp.busi_prod_code,
				                tcp.amount,
				                tcp.unit,
				                tcp.std_prem_af,
				                tcp.additional_prem_af,
				                tcp.charge_period,
				                tcp.charge_year,
				                tcp.coverage_period,
				                tcp.coverage_year,
				                tbp.renew,
				                tc.customer_certi_code
				  from dev_pas.t_contract_master tcm
				  join dev_pas.t_contract_busi_prod tbp
				    on tcm.policy_code = tbp.policy_code
				  join dev_pas.t_contract_product tcp
				    on tbp.busi_item_id = tcp.busi_item_id
				  left join dev_pas.t_benefit_insured tbi
				    on tbi.busi_item_id = tbp.busi_item_id
				  left join dev_pas.t_insured_list til
				    on til.list_id = tbi.insured_id
				  left join dev_pas.t_customer tc
				    on til.customer_id = tc.customer_id
				 where 1 = 1
				   and tbp.master_busi_item_id is not null
		   		   and tcm.policy_id = #{policy_id}]]>
	</select>

	<!-- 查询被保人的客户id、顺序、与投保人关系 -->
	<select id="PA_findNonYbtInsuredList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		        select distinct til.customer_id, til.relation_to_ph, tbi.order_id,til.address_id
				  from dev_pas.t_contract_master tcm
				  join dev_pas.t_insured_list til
				    on til.policy_id = tcm.policy_id
				  join dev_pas.t_benefit_insured tbi
				    on til.list_id = tbi.insured_id
				 where 1 = 1
		   		  and tcm.policy_id = #{policy_id}]]>
    </select>
	
	<!-- 查询交费信息 -->
	<select id="PA_findNonYbtPremArapInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				SELECT tpa.policy_code,
				       tpa.busi_prod_code,
				       SUM(TPA.FEE_AMOUNT) fee_amount,
				       TPA.DUE_TIME,
				       tpa.bank_code,
				       tpa.bank_account,
				       tpa.money_code
				  FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
				 WHERE 1 = 1
				   AND TPA.FEE_STATUS = '01'
				   AND TPA.DERIV_TYPE = '001'
			       AND TPA.POLICY_CODE = #{policy_code} ]]>
		<![CDATA[    GROUP BY tpa.policy_code,
					          tpa.busi_prod_code,
					          TPA.DUE_TIME,
					          tpa.bank_code,
					          tpa.bank_account,
					          tpa.money_code]]>
	</select>
	
</mapper>
