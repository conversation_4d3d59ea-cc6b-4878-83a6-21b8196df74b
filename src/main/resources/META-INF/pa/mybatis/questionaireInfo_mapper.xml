<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IQuestionaireInfoDao">
<!--
	<sql id="PA_questionaireInfoWhereCondition">
		<if test=" survey_version != null and survey_version != ''  "><![CDATA[ AND A.SURVEY_VERSION = #{survey_version} ]]></if>
		<if test=" survey_question_id  != null "><![CDATA[ AND A.SURVEY_QUESTION_ID = #{survey_question_id} ]]></if>
		<if test=" question_content != null and question_content != ''  "><![CDATA[ AND A.QUESTION_CONTENT = #{question_content} ]]></if>
		<if test=" survey_param_module != null and survey_param_module != ''  "><![CDATA[ AND A.SURVEY_PARAM_MODULE = #{survey_param_module} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" survey_code != null and survey_code != ''  "><![CDATA[ AND A.SURVEY_CODE = #{survey_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryQuestionaireInfoBySurveyQuestionIdCondition">
		<if test=" survey_question_id  != null "><![CDATA[ AND A.SURVEY_QUESTION_ID = #{survey_question_id} ]]></if>
		<if test=" survey_code != null and survey_code != ''  "><![CDATA[ AND A.SURVEY_CODE = #{survey_code} ]]></if>
		<if test=" survey_version != null and survey_version != ''  "><![CDATA[ AND A.SURVEY_VERSION = #{survey_version} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addQuestionaireInfo"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_QUESTIONAIRE_INFO(
				SURVEY_VERSION, INSERT_TIMESTAMP, SURVEY_QUESTION_ID, QUESTION_CONTENT, UPDATE_BY, INSERT_TIME, SURVEY_PARAM_MODULE, 
				REMARK, SURVEY_CODE, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				#{survey_version, jdbcType=VARCHAR}, CURRENT_TIMESTAMP, #{survey_question_id, jdbcType=NUMERIC} , #{question_content, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{survey_param_module, jdbcType=VARCHAR} 
				, #{remark, jdbcType=VARCHAR} , #{survey_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteQuestionaireInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_INFO WHERE SURVEY_QUESTION_ID = #{survey_question_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateQuestionaireInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_QUESTIONAIRE_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			SURVEY_VERSION = #{survey_version, jdbcType=VARCHAR} ,
			QUESTION_CONTENT = #{question_content, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			SURVEY_PARAM_MODULE = #{survey_param_module, jdbcType=VARCHAR} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
			SURVEY_CODE = #{survey_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE SURVEY_QUESTION_ID = #{survey_question_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findQuestionaireInfoBySurveyQuestionId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_VERSION, A.SURVEY_QUESTION_ID, A.QUESTION_CONTENT, A.SURVEY_PARAM_MODULE, 
			A.REMARK, A.SURVEY_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryQuestionaireInfoBySurveyQuestionIdCondition" />
		<![CDATA[ ORDER BY A.SURVEY_QUESTION_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapQuestionaireInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_VERSION, A.SURVEY_QUESTION_ID, A.QUESTION_CONTENT, A.SURVEY_PARAM_MODULE, 
			A.REMARK, A.SURVEY_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_QUESTION_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllQuestionaireInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_VERSION, A.SURVEY_QUESTION_ID, A.QUESTION_CONTENT, A.SURVEY_PARAM_MODULE, 
			A.REMARK, A.SURVEY_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_QUESTION_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findQuestionaireInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_QUESTIONAIRE_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryQuestionaireInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SURVEY_VERSION, B.SURVEY_QUESTION_ID, B.QUESTION_CONTENT, B.SURVEY_PARAM_MODULE, 
			B.REMARK, B.SURVEY_CODE FROM (
					SELECT ROWNUM RN, A.SURVEY_VERSION, A.SURVEY_QUESTION_ID, A.QUESTION_CONTENT, A.SURVEY_PARAM_MODULE, 
			A.REMARK, A.SURVEY_CODE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_QUESTION_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
