<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="riskScoreConfig">
	
	<sql id="findOrphanPolicyWhereCondition">
		
		<if test="date != null and date != '' and date == 'yyyy-mm-dd' ">
		<if test=" validate_date_start != null and validate_date_start != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-mm-dd') >= #{validate_date_start}]]></if>
			<if test=" validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND to_char(T.VALIDATE_DATE, 'yyyy-mm-dd') <= #{validate_date_end} ]]></if>
			<if test=" issue_date_start != null and issue_date_start != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-mm-dd') >= #{issue_date_start} ]]></if>
			<if test=" issue_date_end != null and issue_date_end != ''  "><![CDATA[ AND to_char(T.ISSUE_DATE, 'yyyy-mm-dd') <= #{issue_date_end} ]]></if>
			<if test=" expiry_date_start != null and expiry_date_start != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-mm-dd') >= #{expiry_date_start} ]]></if>
			<if test=" expiry_date_end != null and expiry_date_end != ''  "><![CDATA[ AND to_char(T.EXPIRY_DATE, 'yyyy-mm-dd') <= #{expiry_date_end} ]]></if>
		</if>
	</sql>
	
<sql id="PA_orphanParameterWhereCondition"> 
		<if test=" serial_no != null and serial_no != ''  "><![CDATA[ AND A.SERIAL_NO = #{serial_no} ]]></if>
		<if test=" list_id != null and list_id != ''  "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" notice_status != null and notice_status != ''  "><![CDATA[ AND A.NOTICE_STATUS = #{notice_status} ]]></if>
	</sql>
	<select id="PA_findAllRiskScoreConfigTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
            SELECT COUNT(1)
              FROM (
              SELECT DISTINCT TCM.POLICY_CODE
         FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
              DEV_PAS.T_BUSINESS_PRODUCT TBP,
              DEV_PAS.T_CONTRACT_MASTER TCM,
              DEV_PAS.T_POLICY_ACKNOWLEDGEMENT TPA,
              DEV_PAS.T_CONTRACT_PRODUCT TCP,
              DEV_PAS.T_POLICY_HOLDER TPH,
              DEV_PAS.T_INSURED_LIST TIL,
              DEV_PAS.T_CUSTOMER TC,
              DEV_PAS.T_CUSTOMER TCR
              WHERE 1=1
              AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
              AND TCM.POLICY_CODE = TCBP.POLICY_CODE
              AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
              AND TCM.POLICY_CODE = TPH.POLICY_CODE
              AND TCM.POLICY_CODE = TIL.POLICY_CODE
              AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
              AND TIL.CUSTOMER_ID = TCR.CUSTOMER_ID
              AND TCM.POLICY_ID = TPA.POLICY_ID
              AND TBP.PRODUCT_CATEGORY4 = '50001'
              AND TCM.DECISION_CODE <> '20'
              AND NOT EXISTS (SELECT 1 FROM DEV_PAS.T_CS_ACCEPT_CHANGE TCAC,
                                       DEV_PAS.T_CS_POLICY_CHANGE TCPC
                                       WHERE TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
                                       AND TCAC.CHANGE_ID = TCPC.CHANGE_ID
                                       AND TCPC.POLICY_ID = TCM.POLICY_ID
                                       AND TCAC.ACCEPT_STATUS IN ('14','18')
                                       AND TCAC.SERVICE_CODE = 'HI'
                                       AND TCAC.VALIDATE_TIME < TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY) ]]>
             <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
             <if test=" batchTime != null and batchTime != ''  "><![CDATA[ AND TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY = #{batchTime} +1]]></if>                
             <if test=" validate_date_start != null and validate_date_start != '' "><![CDATA[ AND TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY +1 >= #{validate_date_start} ]]></if>    
             <if test="  validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY +1 <= #{validate_date_end}  ]]></if>    
             
             <![CDATA[                          
                   )]]>
	</select>
	
	<select id="queryRiskScoreConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM RN,
	         TCM.POLICY_ID,
	         TCM.POLICY_CODE,
	         TCBP.BUSI_ITEM_ID,
			 TPH.CUSTOMER_ID TCUSTOMER_ID,
			 TIL.CUSTOMER_ID BCUSTOMER_ID,
			 TCBP.VALIDATE_DATE,
			 TCP.STD_PREM_AF,
			 TCP.AMOUNT,
			 TCBP.BUSI_PROD_CODE,
			 (SELECT T.AGENT_CODE FROM DEV_PAS.T_CONTRACT_AGENT T WHERE T.POLICY_CODE = TCM.POLICY_CODE AND T.IS_CURRENT_AGENT = '1')AGENT_CODE,
			 (SELECT T.AGENT_CODE FROM DEV_PAS.T_CONTRACT_AGENT T WHERE T.POLICY_CODE = TCM.POLICY_CODE AND T.IS_NB_AGENT = '1')NBAGENT_CODE,
			 (SELECT T.AGENT_ORGAN_CODE FROM DEV_PAS.T_CONTRACT_AGENT T WHERE T.POLICY_CODE = TCM.POLICY_CODE AND T.IS_NB_AGENT = '1')AGENT_ORGAN_CODE,
			 TCM.ORGAN_CODE,
			 substr(TCM.ORGAN_CODE,0,4)ORGAN_CODE4,
			 substr(TCM.ORGAN_CODE,0,6)ORGAN_CODE6,
			 substr(TCM.ORGAN_CODE,0,8)ORGAN_CODE8,
			 substr(TC.CUSTOMER_CERTI_CODE,0.6)TCUSTOMER_CERTI_CODE,
			 substr(TCR.CUSTOMER_CERTI_CODE,0.6)BCUSTOMER_CERTI_CODE
         FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
              DEV_PAS.T_BUSINESS_PRODUCT TBP,
							DEV_PAS.T_CONTRACT_MASTER TCM,
							DEV_PAS.T_POLICY_ACKNOWLEDGEMENT TPA,
							DEV_PAS.T_CONTRACT_PRODUCT TCP,
							DEV_PAS.T_POLICY_HOLDER TPH,
							DEV_PAS.T_INSURED_LIST TIL,
							DEV_PAS.T_CUSTOMER TC,
							DEV_PAS.T_CUSTOMER TCR
							WHERE 1=1
							AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
							AND TCM.POLICY_CODE = TCBP.POLICY_CODE
							AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
							AND TCM.POLICY_CODE = TPH.POLICY_CODE
							AND TCM.POLICY_CODE = TIL.POLICY_CODE
							AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
							AND TIL.CUSTOMER_ID = TCR.CUSTOMER_ID
							AND TCM.POLICY_ID = TPA.POLICY_ID
							AND TBP.PRODUCT_CATEGORY4 = '50001'
							AND TCM.DECISION_CODE <> '20'
							AND NOT EXISTS (SELECT 1 FROM DEV_PAS.T_CS_ACCEPT_CHANGE TCAC,
							                         DEV_PAS.T_CS_POLICY_CHANGE TCPC
																			 WHERE TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
																			 AND TCAC.CHANGE_ID = TCPC.CHANGE_ID
																			 AND TCPC.POLICY_ID = TCM.POLICY_ID
																			 AND TCAC.ACCEPT_STATUS IN ('14','18')
																			 AND TCAC.SERVICE_CODE = 'HI'
																			 AND TCAC.VALIDATE_TIME < TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY) ]]>
			 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
             <if test=" batchTime != null and batchTime != ''  "><![CDATA[ AND TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY = #{batchTime} +1]]></if>                
             <if test=" validate_date_start != null and validate_date_start != '' "><![CDATA[ AND TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY +1 >= #{validate_date_start} ]]></if>    
             <if test="  validate_date_end != null and validate_date_end != ''  "><![CDATA[ AND TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY +1 <= #{validate_date_end}  ]]></if>    																       
			  <![CDATA[
			   AND MOD(TCM.POLICY_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
			   ]]>
		</select>
		
	<!-- 修改险种风险评分 -->
	<update id="PA_updateContractBusiProdRiskScore" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ]]>
		<set>
		<trim suffixOverrides=",">
		    RISK_SCORE = #{risk_score, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} AND  POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ]]>
	</update>

</mapper>
