<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.QueryPolicyHisForCCBDaoImpl">

	 <sql id="queryCsInformationQueryCondition">
		<if test=" policy_code != null and policy_code != '' ">
			<![CDATA[ and t.policy_code = #{policy_code} ]]>
		</if>
	</sql>
	<!-- **********************************************************  查询条件保单号 start **********************************************************-->

	
	<!-- 银保通-查询保单历史缴费信息 -->
	<select id="ybtQueryPolicyHistoryFeePa" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
             SELECT P.DUE_TIME, sum(P.FEE_AMOUNT)  FEE_AMOUNT
        FROM DEV_PAS.T_PREM_ARAP P
		 WHERE 
		   P.FEE_TYPE in ('G003100000','G003010000','G003020100','G003030100','G003040100','G003020200','G003030200','G003040200')
		 ]]>	
		 <if test=" end_date != null and end_date != '' ">
			<![CDATA[   AND P.DUE_TIME <=#{end_date}	]]>
		</if>
		<if test=" start_date != null and start_date != '' "> 
			<![CDATA[    AND P.DUE_TIME >=#{start_date} ]]>
		</if>      	
		<if test=" policy_code != null and policy_code != '' ">
			<![CDATA[ and p.policy_code = #{policy_code} ]]>
		</if>
		<![CDATA[ GROUP BY P.DUE_TIME]]>
	</select>
	
	<!-- 银保通-查询保单历史红利发放 -->
	<select id="ybtQueryPolicyHistoryBonusPa" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
      SELECT B.ALLOCATE_DATE,
             B.ALLOCATE_TYPE,
             B.BONUS_SA,
             B.TERMINAL_BONUS,
             A.BONUS_SA AS BONUS_SAM                             
        FROM DEV_PAS.T_BONUS_ALLOCATE B
        LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT A
          ON B.POLICY_ID = A.POLICY_ID
		 WHERE 1=1
		 ]]>
		 <if test=" end_date != null and end_date != '' ">
			<![CDATA[   AND B.ALLOCATE_DATE <=#{end_date}	]]>
		</if>
		<if test=" start_date != null and start_date != '' "> 
			<![CDATA[    AND B.ALLOCATE_DATE >=#{start_date} ]]>
		</if>
		<if test=" policy_code != null and policy_code != '' ">
			<![CDATA[ and a.policy_code = #{policy_code} ]]>
		</if>
	</select>
	<!--查询保单失效复效状态  -->
		<select id="ybtQueryPolicyHistorylibPa" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
			SELECT PLC.CHANGE_DATE LAPSE_DATE, TCP.RERINSTATE_DATE RERINSTATE_DATE, TCP.LIABILITY_STATE LIABILITY_STATE
			  FROM DEV_PAS.T_CONTRACT_PRODUCT TCP
			  LEFT JOIN DEV_PAS.T_PRODUCT_LIABILITY_CHANGE PLC
			    ON TCP.POLICY_ID = PLC.POLICY_ID
			 WHERE PLC.LIABILITY_CHANGED = '4'
		 ]]>
				<if test=" start_date != null and start_date != '' "> 
			<![CDATA[    AND tcp.validate_date >=#{start_date} ]]>
		</if> 
	
				<if test=" policy_code != null and policy_code != '' ">
			<![CDATA[ and tcp.policy_code = #{policy_code} ]]>
		</if>
	</select>
	
		<!-- 银保通-查询保单历史变动信息 -->
	<select id="ybtQueryPolicyHistoryPA" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
    SELECT A.APPLY_CODE,
           AC.ACCEPT_CODE,
           A.APPLY_TIME,
           (SELECT S.SERVICE_NAME
              FROM DEV_PAS.T_SERVICE S
             WHERE S.SERVICE_CODE = AC.SERVICE_CODE) || ','||ac.SERVICE_CODE as SERVICE_CODE,
           A.APPLY_NAME,
           AC.VALIDATE_TIME
      FROM DEV_PAS.T_CS_APPLICATION A
     INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
        ON AC.CHANGE_ID = A.CHANGE_ID
		 WHERE EXISTS (SELECT 'X'
		          FROM DEV_PAS.T_CS_POLICY_CHANGE PC
		         WHERE 1=1]]>
		     <if test=" policy_code != null and policy_code != '' ">
			<![CDATA[ and PC.POLICY_CODE = #{policy_code} ]]>
		</if>
		          <![CDATA[ and PC.CHANGE_ID = A.CHANGE_ID)
		]]>
		<if test=" service_codes != null and service_codes != '' "><![CDATA[AND AC.SERVICE_CODE IN (${service_codes})]]></if>
		<if test=" customer_id != null and customer_id != '' ">
		<![CDATA[
		   AND A.CUSTOMER_ID= #{customer_id} ]]>
		   </if>
		 <if test=" end_date != null and end_date != '' "> 
		<![CDATA[   AND A.APPLY_TIME <=#{end_date} ]]>
		</if>
		<if test=" start_date != null and start_date != '' "> 
		  <![CDATA[ AND A.APPLY_TIME >=#{start_date}]]>
		</if>

	</select>
</mapper>