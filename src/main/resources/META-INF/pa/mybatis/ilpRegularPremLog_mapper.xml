<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ilpRegularPremLog">
<!--
	<sql id="PA_ilpRegularPremLogWhereCondition">
		<if test=" active_indi  != null "><![CDATA[ AND A.ACTIVE_INDI = #{active_indi} ]]></if>
		<if test=" discnted_prem_af  != null "><![CDATA[ AND A.DISCNTED_PREM_AF = #{discnted_prem_af} ]]></if>
		<if test=" next_discnted_prem_an  != null "><![CDATA[ AND A.NEXT_DISCNTED_PREM_AN = #{next_discnted_prem_an} ]]></if>
		<if test=" next_insur_prem_an  != null "><![CDATA[ AND A.NEXT_INSUR_PREM_AN = #{next_insur_prem_an} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" discnted_prem_an  != null "><![CDATA[ AND A.DISCNTED_PREM_AN = #{discnted_prem_an} ]]></if>
		<if test=" next_total_prem_af  != null "><![CDATA[ AND A.NEXT_TOTAL_PREM_AF = #{next_total_prem_af} ]]></if>
		<if test=" next_discnted_prem_af  != null "><![CDATA[ AND A.NEXT_DISCNTED_PREM_AF = #{next_discnted_prem_af} ]]></if>
		<if test=" prem_months  != null "><![CDATA[ AND A.PREM_MONTHS = #{prem_months} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" insur_prem_an  != null "><![CDATA[ AND A.INSUR_PREM_AN = #{insur_prem_an} ]]></if>
		<if test=" discnted_prem_bf  != null "><![CDATA[ AND A.DISCNTED_PREM_BF = #{discnted_prem_bf} ]]></if>
		<if test=" insur_prem_af  != null "><![CDATA[ AND A.INSUR_PREM_AF = #{insur_prem_af} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" next_discnted_prem_bf  != null "><![CDATA[ AND A.NEXT_DISCNTED_PREM_BF = #{next_discnted_prem_bf} ]]></if>
		<if test=" next_insur_prem_af  != null "><![CDATA[ AND A.NEXT_INSUR_PREM_AF = #{next_insur_prem_af} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" pay_to_date  != null  and  pay_to_date  != ''  "><![CDATA[ AND A.PAY_TO_DATE = #{pay_to_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryIlpRegularPremLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addIlpRegularPremLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG(
				ACTIVE_INDI, DISCNTED_PREM_AF, NEXT_DISCNTED_PREM_AN, NEXT_INSUR_PREM_AN, ITEM_ID, DISCNTED_PREM_AN, NEXT_TOTAL_PREM_AF, 
				NEXT_DISCNTED_PREM_AF, PREM_MONTHS, INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, POLICY_CHG_ID, POLICY_ID, 
				INSUR_PREM_AN, DISCNTED_PREM_BF, INSERT_TIME, UPDATE_TIME, INSUR_PREM_AF, TOTAL_PREM_AF, START_DATE, 
				LOG_ID, NEXT_DISCNTED_PREM_BF, NEXT_INSUR_PREM_AF, UPDATE_TIMESTAMP, LOG_TYPE, PAY_TO_DATE, INSERT_BY ) 
			VALUES (
				#{active_indi, jdbcType=NUMERIC}, #{discnted_prem_af, jdbcType=NUMERIC} , #{next_discnted_prem_an, jdbcType=NUMERIC} , #{next_insur_prem_an, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , #{discnted_prem_an, jdbcType=NUMERIC} , #{next_total_prem_af, jdbcType=NUMERIC} 
				, #{next_discnted_prem_af, jdbcType=NUMERIC} , #{prem_months, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{insur_prem_an, jdbcType=NUMERIC} , #{discnted_prem_bf, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{insur_prem_af, jdbcType=NUMERIC} , #{total_prem_af, jdbcType=NUMERIC} , #{start_date, jdbcType=DATE} 
				, #{log_id, jdbcType=NUMERIC} , #{next_discnted_prem_bf, jdbcType=NUMERIC} , #{next_insur_prem_af, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{pay_to_date, jdbcType=DATE} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteIlpRegularPremLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG WHERE 1 = 1 ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateIlpRegularPremLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ACTIVE_INDI = #{active_indi, jdbcType=NUMERIC} ,
		    DISCNTED_PREM_AF = #{discnted_prem_af, jdbcType=NUMERIC} ,
		    NEXT_DISCNTED_PREM_AN = #{next_discnted_prem_an, jdbcType=NUMERIC} ,
		    NEXT_INSUR_PREM_AN = #{next_insur_prem_an, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    DISCNTED_PREM_AN = #{discnted_prem_an, jdbcType=NUMERIC} ,
		    NEXT_TOTAL_PREM_AF = #{next_total_prem_af, jdbcType=NUMERIC} ,
		    NEXT_DISCNTED_PREM_AF = #{next_discnted_prem_af, jdbcType=NUMERIC} ,
		    PREM_MONTHS = #{prem_months, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    INSUR_PREM_AN = #{insur_prem_an, jdbcType=NUMERIC} ,
		    DISCNTED_PREM_BF = #{discnted_prem_bf, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    INSUR_PREM_AF = #{insur_prem_af, jdbcType=NUMERIC} ,
		    TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    NEXT_DISCNTED_PREM_BF = #{next_discnted_prem_bf, jdbcType=NUMERIC} ,
		    NEXT_INSUR_PREM_AF = #{next_insur_prem_af, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    PAY_TO_DATE = #{pay_to_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findIlpRegularPremLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.DISCNTED_PREM_AF, A.NEXT_DISCNTED_PREM_AN, A.NEXT_INSUR_PREM_AN, A.ITEM_ID, A.DISCNTED_PREM_AN, A.NEXT_TOTAL_PREM_AF, 
			A.NEXT_DISCNTED_PREM_AF, A.PREM_MONTHS, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.INSUR_PREM_AN, A.DISCNTED_PREM_BF, A.INSUR_PREM_AF, A.TOTAL_PREM_AF, A.START_DATE, 
			A.LOG_ID, A.NEXT_DISCNTED_PREM_BF, A.NEXT_INSUR_PREM_AF, A.LOG_TYPE, A.PAY_TO_DATE FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRegularPremLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapIlpRegularPremLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.DISCNTED_PREM_AF, A.NEXT_DISCNTED_PREM_AN, A.NEXT_INSUR_PREM_AN, A.ITEM_ID, A.DISCNTED_PREM_AN, A.NEXT_TOTAL_PREM_AF, 
			A.NEXT_DISCNTED_PREM_AF, A.PREM_MONTHS, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.INSUR_PREM_AN, A.DISCNTED_PREM_BF, A.INSUR_PREM_AF, A.TOTAL_PREM_AF, A.START_DATE, 
			A.LOG_ID, A.NEXT_DISCNTED_PREM_BF, A.NEXT_INSUR_PREM_AF, A.LOG_TYPE, A.PAY_TO_DATE FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllIlpRegularPremLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTIVE_INDI, A.DISCNTED_PREM_AF, A.NEXT_DISCNTED_PREM_AN, A.NEXT_INSUR_PREM_AN, A.ITEM_ID, A.DISCNTED_PREM_AN, A.NEXT_TOTAL_PREM_AF, 
			A.NEXT_DISCNTED_PREM_AF, A.PREM_MONTHS, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.INSUR_PREM_AN, A.DISCNTED_PREM_BF, A.INSUR_PREM_AF, A.TOTAL_PREM_AF, A.START_DATE, 
			A.LOG_ID, A.NEXT_DISCNTED_PREM_BF, A.NEXT_INSUR_PREM_AF, A.LOG_TYPE, A.PAY_TO_DATE FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findIlpRegularPremLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryIlpRegularPremLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACTIVE_INDI, B.DISCNTED_PREM_AF, B.NEXT_DISCNTED_PREM_AN, B.NEXT_INSUR_PREM_AN, B.ITEM_ID, B.DISCNTED_PREM_AN, B.NEXT_TOTAL_PREM_AF, 
			B.NEXT_DISCNTED_PREM_AF, B.PREM_MONTHS, B.LIST_ID, B.POLICY_CHG_ID, B.POLICY_ID, 
			B.INSUR_PREM_AN, B.DISCNTED_PREM_BF, B.INSUR_PREM_AF, B.TOTAL_PREM_AF, B.START_DATE, 
			B.LOG_ID, B.NEXT_DISCNTED_PREM_BF, B.NEXT_INSUR_PREM_AF, B.LOG_TYPE, B.PAY_TO_DATE FROM (
					SELECT ROWNUM RN, A.ACTIVE_INDI, A.DISCNTED_PREM_AF, A.NEXT_DISCNTED_PREM_AN, A.NEXT_INSUR_PREM_AN, A.ITEM_ID, A.DISCNTED_PREM_AN, A.NEXT_TOTAL_PREM_AF, 
			A.NEXT_DISCNTED_PREM_AF, A.PREM_MONTHS, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.INSUR_PREM_AN, A.DISCNTED_PREM_BF, A.INSUR_PREM_AF, A.TOTAL_PREM_AF, A.START_DATE, 
			A.LOG_ID, A.NEXT_DISCNTED_PREM_BF, A.NEXT_INSUR_PREM_AF, A.LOG_TYPE, A.PAY_TO_DATE FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
