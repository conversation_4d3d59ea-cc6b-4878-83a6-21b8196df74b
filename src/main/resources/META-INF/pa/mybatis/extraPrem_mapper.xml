<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="extraPrem">

	<sql id="PA_extraPremWhereCondition">
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" extra_div  != null "><![CDATA[ AND A.EXTRA_DIV = #{extra_div} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" extra_prem  != null "><![CDATA[ AND A.EXTRA_PREM = #{extra_prem} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" em_value  != null "><![CDATA[ AND A.EM_VALUE = #{em_value} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" extra_period  != null "><![CDATA[ AND A.EXTRA_PERIOD = #{extra_period} ]]></if>
		<if test=" extra_type != null and extra_type != ''  "><![CDATA[ AND A.EXTRA_TYPE = #{extra_type} ]]></if>
		<if test=" extra_para  != null "><![CDATA[ AND A.EXTRA_PARA = #{extra_para} ]]></if>
		<if test=" into_effect_type != null and into_effect_type != ''  "><![CDATA[ AND A.INTO_EFFECT_TYPE = #{into_effect_type} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" extra_perc  != null "><![CDATA[ AND A.EXTRA_PERC = #{extra_perc} ]]></if>
		<if test=" add_arith != null and add_arith != ''  "><![CDATA[ AND A.ADD_ARITH = #{add_arith} ]]></if>
		<if test=" ui_em_value  != null "><![CDATA[ AND A.UI_EM_VALUE = #{ui_em_value} ]]></if>
		<if test="extra_type_list  != null and extra_type_list.size()!=0 ">
			<![CDATA[ AND A.EXTRA_TYPE in (]]>
			<foreach collection="extra_type_list" item="extra_type"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{extra_type} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryExtraPremByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryExtraPremByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addExtraPrem"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_EXTRA_PREM__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_EXTRA_PREM(
				UI_EM_VALUE,END_DATE, PRODUCT_CODE, ITEM_ID, EXTRA_DIV, BUSI_PROD_CODE, EXTRA_PREM, APPLY_CODE, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, BUSI_ITEM_ID, EM_VALUE, POLICY_ID, INSERT_TIME, 
				EXTRA_PERIOD, EXTRA_TYPE, UPDATE_TIME, EXTRA_PARA, INTO_EFFECT_TYPE, START_DATE, POLICY_CODE, 
				UPDATE_TIMESTAMP, INSERT_BY, EXTRA_PERC, ADD_ARITH ) 
			VALUES (
				#{ui_em_value, jdbcType=NUMERIC} ,#{end_date, jdbcType=DATE}, #{product_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{extra_div, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{extra_prem, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{em_value, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , SYSDATE 
				, #{extra_period, jdbcType=NUMERIC} , #{extra_type, jdbcType=VARCHAR} , SYSDATE , #{extra_para, jdbcType=NUMERIC} , #{into_effect_type, jdbcType=VARCHAR} , #{start_date, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{extra_perc, jdbcType=NUMERIC} , #{add_arith, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteExtraPrem" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_EXTRA_PREM WHERE 1 = 1 AND LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateExtraPrem" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_EXTRA_PREM ]]>
		<set>
		<trim suffixOverrides=",">
		    END_DATE = #{end_date, jdbcType=DATE} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    EXTRA_DIV = #{extra_div, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    EXTRA_PREM = #{extra_prem, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    EM_VALUE = #{em_value, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    EXTRA_PERIOD = #{extra_period, jdbcType=NUMERIC} ,
			EXTRA_TYPE = #{extra_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    EXTRA_PARA = #{extra_para, jdbcType=NUMERIC} ,
			INTO_EFFECT_TYPE = #{into_effect_type, jdbcType=VARCHAR} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    EXTRA_PERC = #{extra_perc, jdbcType=NUMERIC} ,
			ADD_ARITH = #{add_arith, jdbcType=VARCHAR} ,
			UI_EM_VALUE = #{ui_em_value, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findExtraPremByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UI_EM_VALUE,A.END_DATE, A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.EXTRA_PARA, A.INTO_EFFECT_TYPE, A.START_DATE, A.POLICY_CODE, 
			A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM A WHERE 1 = 1  ]]>
		<include refid="PA_queryExtraPremByListIdCondition" />
	</select>
	
	<select id="PA_findExtraPremByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UI_EM_VALUE,A.END_DATE, A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.EXTRA_PARA, A.INTO_EFFECT_TYPE, A.START_DATE, A.POLICY_CODE, 
			A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM A WHERE 1 = 1  ]]>
		<include refid="PA_queryExtraPremByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapExtraPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.UI_EM_VALUE,A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.EXTRA_PARA, A.INTO_EFFECT_TYPE, A.START_DATE, A.POLICY_CODE, 
			A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_extraPremWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllExtraPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.PRODUCT_CODE, A.UI_EM_VALUE,A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.EXTRA_PARA, A.INTO_EFFECT_TYPE, A.START_DATE, A.POLICY_CODE, 
			A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_extraPremWhereCondition" />
	</select>

	<!-- 查询首期加费 -->
	<select id="findFirstPhaseExtraPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.PRODUCT_CODE, A.UI_EM_VALUE,A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.EXTRA_PARA, A.INTO_EFFECT_TYPE, A.START_DATE, A.POLICY_CODE, 
			A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM A WHERE ROWNUM =1  ]]>
		<include refid="PA_extraPremWhereCondition" />
		order by a.START_DATE asc    
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findExtraPremTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_EXTRA_PREM A WHERE 1 = 1  ]]>
		<include refid="PA_extraPremWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryExtraPremForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.END_DATE, B.PRODUCT_CODE, B.ITEM_ID, B.EXTRA_DIV, B.BUSI_PROD_CODE, B.EXTRA_PREM, B.APPLY_CODE, 
			B.LIST_ID, B.BUSI_ITEM_ID, B.EM_VALUE, B.POLICY_ID, 
			B.EXTRA_PERIOD, B.EXTRA_TYPE, B.EXTRA_PARA, B.INTO_EFFECT_TYPE, B.START_DATE, B.POLICY_CODE, 
			B.EXTRA_PERC, B.ADD_ARITH FROM (
					SELECT ROWNUM RN, A.END_DATE, A.PRODUCT_CODE, A.ITEM_ID, A.EXTRA_DIV, A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.EM_VALUE, A.POLICY_ID, 
			A.EXTRA_PERIOD, A.EXTRA_TYPE, A.EXTRA_PARA, A.INTO_EFFECT_TYPE, A.START_DATE, A.POLICY_CODE, 
			A.EXTRA_PERC, A.ADD_ARITH FROM APP___PAS__DBUSER.T_EXTRA_PREM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_extraPremWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findExtraPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.LIST_ID,
       A.APPLY_CODE,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.BUSI_ITEM_ID,
       A.ITEM_ID,
       A.BUSI_PROD_CODE,
       A.PRODUCT_CODE,
       A.ADD_ARITH,
       A.START_DATE,
       A.END_DATE,
       A.EXTRA_TYPE,
       A.EXTRA_PREM,
       A.EXTRA_PERIOD,
       A.EXTRA_PERC,
       A.EXTRA_PARA,
       A.EXTRA_DIV,
       A.EM_VALUE,
       A.INTO_EFFECT_TYPE,
       A.UI_EM_VALUE,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP FROM DEV_PAS.T_EXTRA_PREM A WHERE 1 = 1 AND ROWNUM = 1
		]]>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		
	</select>
</mapper>
