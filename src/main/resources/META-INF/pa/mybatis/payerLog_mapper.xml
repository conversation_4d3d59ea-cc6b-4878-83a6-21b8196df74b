<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="payerLog">
<!--
	<sql id="PA_payerLogWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A<PERSON>POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" share_rate  != null "><![CDATA[ AND A.SHARE_RATE = #{share_rate} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayerLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPayerLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_PAYER_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAYER_LOG(
				ADDRESS_ID, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, RELATION_TO_PH, LOG_ID, INSERT_TIMESTAMP, 
				POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, 
				POLICY_ID, SHARE_RATE ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{relation_to_ph, jdbcType=VARCHAR} , #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} , #{share_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePayerLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAYER_LOG WHERE  LOG_ID=#{log_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePayerLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SHARE_RATE = #{share_rate, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPayerLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.LOG_ID, 
			A.POLICY_CODE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayerLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.LOG_ID, 
			A.POLICY_CODE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayerLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.LOG_ID, 
			A.POLICY_CODE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayerLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAYER_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayerLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_ID, B.RELATION_TO_PH, B.LOG_ID, 
			B.POLICY_CODE, B.LIST_ID, B.LOG_TYPE, B.POLICY_CHG_ID, 
			B.POLICY_ID, B.SHARE_RATE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.LOG_ID, 
			A.POLICY_CODE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID, A.SHARE_RATE FROM APP___PAS__DBUSER.T_PAYER_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
