<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.dao.ISalesChannelDao">
<!--
	<sql id="salesChannelWhereCondition">
		<if test=" sales_channel_name != null and sales_channel_name != ''  "><![CDATA[ AND A.SALES_CHANNEL_NAME = #{sales_channel_name} ]]></if>
		<if test=" is_valid != null and is_valid != ''  "><![CDATA[ AND A.IS_VALID = #{is_valid} ]]></if>
		<if test=" sales_channel_code != null and sales_channel_code != ''  "><![CDATA[ AND A.SALES_CHANNEL_CODE = #{sales_channel_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="querySalesChannelBySalesChannelCodeCondition">
		<if test=" sales_channel_code != null and sales_channel_code != '' "><![CDATA[ AND A.SALES_CHANNEL_CODE = #{sales_channel_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSalesChannel"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SALES_CHANNEL(
				SALES_CHANNEL_NAME, IS_VALID, SALES_CHANNEL_CODE ) 
			VALUES (
				#{sales_channel_name, jdbcType=VARCHAR}, #{is_valid, jdbcType=VARCHAR} , #{sales_channel_code,jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSalesChannel" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SALES_CHANNEL WHERE SALES_CHANNEL_CODE = #{sales_channel_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSalesChannel" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SALES_CHANNEL ]]>
		<set>
		<trim suffixOverrides=",">
			SALES_CHANNEL_NAME = #{sales_channel_name, jdbcType=VARCHAR} ,
			IS_VALID = #{is_valid, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE SALES_CHANNEL_CODE = #{sales_channel_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSalesChannelBySalesChannelCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM  APP___PAS__DBUSER.T_SALE_COM A WHERE 1 = 1  ]]>
		<if test=" sales_channel_code != null and sales_channel_code != '' "><![CDATA[ AND A.SALE_COM_CODE=#{sales_channel_code}]]></if>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSalesChannel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SALES_CHANNEL_NAME, A.IS_VALID, A.SALES_CHANNEL_CODE FROM APP___PAS__DBUSER.T_SALES_CHANNEL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALES_CHANNEL_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSalesChannelELPSYCONGROO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SALES_CHANNEL_NAME, A.IS_VALID, A.SALES_CHANNEL_CODE FROM APP___PAS__DBUSER.T_SALES_CHANNEL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALES_CHANNEL_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSalesChannelTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SALES_CHANNEL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="querySalesChannelForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SALES_CHANNEL_NAME, B.IS_VALID, B.SALES_CHANNEL_CODE FROM (
					SELECT ROWNUM RN, A.SALES_CHANNEL_NAME, A.IS_VALID, A.SALES_CHANNEL_CODE FROM APP___PAS__DBUSER.T_SALES_CHANNEL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALES_CHANNEL_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findSalesChannel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SALES_CHANNEL_NAME, A.IS_VALID, A.SALES_CHANNEL_CODE FROM APP___PAS__DBUSER.T_SALES_CHANNEL A WHERE 1 = 1  ]]>
		<if test=" sales_channel_code != null and sales_channel_code != ''  "><![CDATA[ AND A.SALES_CHANNEL_CODE = #{sales_channel_code} ]]></if>
	</select>
	
</mapper>
