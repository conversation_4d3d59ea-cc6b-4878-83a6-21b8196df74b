<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IQueryInjuredDao">
	<sql id="queryinjuredWhereCondition">
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TCO.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND TCO.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TCO.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" birthday != null and birthday != ''  "><![CDATA[ AND to_char(TCO.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') like '%${birthday}%' ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND TCO.CUSTOMER_GENDER = #{customer_gender} ]]></if>

	</sql>

	<sql id="queryinjuredByPolicyCode">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TIL.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<sql id="queryHolderByPolicyCode">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPH.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<sql id="queryBusiProdCodeByPolicyCode">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCBP.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<sql id="queryByPolicyCodeList">
		<if test="list != null and list.size()>0"><![CDATA[ and tqqq.busi_prod_code in ]]>
			<foreach collection="list" item="list" index="index" open="("
				close=")" separator=",">#{list}</foreach>
		</if>
	</sql>
	<!-- 查询出险人 -->
	<select id="PA_findAllInsured" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[select E.* from ( select D.* from (select C.*,rownum rn from(
	SELECT B.* from  (
	select		t.customer_name,
				t.customer_id ,
				t.customer_gender,
				t.customer_cert_type,
				t.customer_certi_code,
				t.customer_birthday,
				t.customer_vip
	from APP___PAS__DBUSER.t_customer t ,(
	SELECT distinct tcmo.customer_id
  	from APP___PAS__DBUSER.t_customer tcmo
 	where tcmo.customer_id in
      (SELECT til.customer_id
          FROM APP___PAS__DBUSER.t_insured_list til
         where til.customer_id in
               (SELECT tco.customer_id
                  from APP___PAS__DBUSER.t_customer tco
                 where 1=1 ]]>
		<include refid="queryinjuredWhereCondition"></include>
             <![CDATA[    )
             ]]>
		<include refid="queryinjuredByPolicyCode"></include>
             <![CDATA[
             )
    or tcmo.customer_id in
 (select tph.customer_id
          from APP___PAS__DBUSER.t_policy_holder tph
         where tph.customer_id in
               (SELECT tco.customer_id
                  from APP___PAS__DBUSER.t_customer tco
                 where 1=1	]]>
		<include refid="queryinjuredWhereCondition"></include>
		<include refid="queryHolderByPolicyCode"></include>
               <![CDATA[  )
           and tph.policy_code in
               (select tqqq.policy_code
                  from APP___PAS__DBUSER.t_contract_busi_prod tqqq
                 where 1=1 and tqqq.master_busi_item_id is null ]]>
		<include refid="queryByPolicyCodeList"></include>
                 <![CDATA[))
                 )A 
                 where t.customer_id =A.customer_id)B  
                 order by B.customer_birthday desc)C)D WHERE D.rn <= #{end_number})E WHERE E.RN >#{begain_number} and e.rn<1999]]>
	</select>


	<!-- 查询出险人总数 -->
	<select id="PA_countQueryInjuredInsured" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[select count(*) from(
		select	t.customer_name,
				t.customer_id ,
				t.customer_birthday from APP___PAS__DBUSER.t_customer t ,
	(SELECT distinct tcmo.customer_id from APP___PAS__DBUSER.t_customer tcmo
 	where tcmo.customer_id in (SELECT til.customer_id
          FROM APP___PAS__DBUSER.t_insured_list til where til.customer_id in
               (SELECT tco.customer_id from APP___PAS__DBUSER.t_customer tco
                 where  1=1]]>
		<include refid="queryinjuredWhereCondition"></include>
                 <![CDATA[))
    or tcmo.customer_id in
 (select tph.customer_id
          from APP___PAS__DBUSER.t_policy_holder tph
         where tph.customer_id in
               (SELECT tco.customer_id
                  from APP___PAS__DBUSER.t_customer tco
                 where 1=1 ]]>
		<include refid="queryinjuredWhereCondition"></include>
                 <![CDATA[)
           and tph.policy_code in (select tqqq.policy_code
                  from APP___PAS__DBUSER.t_contract_busi_prod tqqq
                 where 1=1	and tqqq.master_busi_item_id is null ]]>
		<include refid="queryByPolicyCodeList"></include>
                 <![CDATA[)) )A
                  where t.customer_id =A.customer_id order by t.customer_birthday)
		]]>
	</select>

	<!-- 根据保单号查询出险人 -->
	<select id="PA_queryInsuredByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
 	   <![CDATA[SELECT C.RN,C.* FROM (SELECT B.*,ROWNUM RN FROM (SELECT 
 	   				   DISTINCT t.Customer_Id,
 	   				   t.Customer_Name,
		               t.Customer_Cert_Type,
		               t.Customer_Certi_Code,
		               t.Customer_Gender,
		               t.Customer_Birthday,
		               t.Customer_Vip 
		          FROM App___Pas__Dbuser.t_Customer t
		          WHERE 1 = 1 ]]>
		          <if test=" policy_code != null and policy_code != ''  ">
                       <![CDATA[ AND T.CUSTOMER_ID IN 
                       (SELECT DISTINCT CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                       WHERE TPH.POLICY_CODE = #{policy_code} UNION ALL 
                        SELECT DISTINCT CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
                        WHERE TIL.POLICY_CODE = #{policy_code})]]>
                  </if>
		          <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND t.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
		 		  <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND t.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		 		  <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND t.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		 		  <if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND t.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		 		  <if test=" customer_gender  != null "><![CDATA[ AND t.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		          <![CDATA[
		      	  	  ORDER BY t.Customer_Birthday DESC) B where ROWNUM <= #{LESS_NUM} ) C WHERE C.RN > #{GREATER_NUM}    
		          ]]>
	</select>

	<!-- 根据保单号查询出险人总数 -->
	<select id="PA_countQueryPolicyInsuredByPolicyCode" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(DISTINCT(T.CUSTOMER_ID))
              FROM App___Pas__Dbuser.t_Customer t
              WHERE 1 = 1 ]]>
             <if test=" policy_code != null and policy_code != ''  ">
                <![CDATA[ AND T.CUSTOMER_ID IN 
               (SELECT DISTINCT CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
               WHERE TPH.POLICY_CODE = #{policy_code} UNION ALL 
                SELECT DISTINCT CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
                WHERE TIL.POLICY_CODE = #{policy_code})]]>
             </if>
             <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND t.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
          	 <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND t.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
             <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND t.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
             <if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND t.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
             <if test=" customer_gender  != null "><![CDATA[ AND t.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</select>
	
	<!-- 针对完整出生日期特殊处理sql -->
	<sql id="queryinjuredWhereConditionBirthday">
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND INSTR(t.CUSTOMER_NAME , #{customer_name}) > 0 ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND TCO.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TCO.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" birthday != null and birthday != ''  "><![CDATA[ AND TCO.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND TCO.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</sql>
	<!-- 查询出险人总数 -->
	<select id="PA_countQueryInjuredInsuredBirthday" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[select count(*) from(
		select	t.customer_name,
				t.customer_id ,
				t.customer_birthday from APP___PAS__DBUSER.t_customer t ,
	(SELECT distinct tcmo.customer_id from APP___PAS__DBUSER.t_customer tcmo
 	where tcmo.customer_id in (SELECT til.customer_id
          FROM APP___PAS__DBUSER.t_insured_list til where til.customer_id in
               (SELECT tco.customer_id from APP___PAS__DBUSER.t_customer tco
                 where  1=1]]>
		<include refid="queryinjuredWhereConditionBirthday"></include>
                 <![CDATA[))
    or tcmo.customer_id in
 (select tph.customer_id
          from APP___PAS__DBUSER.t_policy_holder tph
         where tph.customer_id in
               (SELECT tco.customer_id
                  from APP___PAS__DBUSER.t_customer tco
                 where 1=1 ]]>
		<include refid="queryinjuredWhereConditionBirthday"></include>
                 <![CDATA[)
           and tph.policy_code in (select tqqq.policy_code
                  from APP___PAS__DBUSER.t_contract_busi_prod tqqq
                 where 1=1	and tqqq.master_busi_item_id is null ]]>
		<include refid="queryByPolicyCodeList"></include>
                 <![CDATA[)) )A
                  where t.customer_id =A.customer_id order by t.customer_birthday)
		]]>
	</select>
	
	<!-- 查询出险人 -->
	<select id="PA_findAllInsuredBirthday" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[select E.* from ( select D.* from (select C.*,rownum rn from(
	SELECT B.* from  (
	select		t.customer_name,
				t.customer_id ,
				t.customer_gender,
				t.customer_cert_type,
				t.customer_certi_code,
				t.customer_birthday,
				t.customer_vip
	from APP___PAS__DBUSER.t_customer t ,(
	SELECT distinct tcmo.customer_id
  	from APP___PAS__DBUSER.t_customer tcmo
 	where tcmo.customer_id in
      (SELECT til.customer_id
          FROM APP___PAS__DBUSER.t_insured_list til
         where til.customer_id in
               (SELECT tco.customer_id
                  from APP___PAS__DBUSER.t_customer tco
                 where 1=1 ]]>
		<include refid="queryinjuredWhereConditionBirthday"></include>
             <![CDATA[    )
             ]]>
		<include refid="queryinjuredByPolicyCode"></include>
             <![CDATA[
             )
    or tcmo.customer_id in
 (select tph.customer_id
          from APP___PAS__DBUSER.t_policy_holder tph
         where tph.customer_id in
               (SELECT tco.customer_id
                  from APP___PAS__DBUSER.t_customer tco
                 where 1=1	]]>
		<include refid="queryinjuredWhereConditionBirthday"></include>
		<include refid="queryHolderByPolicyCode"></include>
               <![CDATA[  )
           and tph.policy_code in
               (select tqqq.policy_code
                  from APP___PAS__DBUSER.t_contract_busi_prod tqqq
                 where 1=1 and tqqq.master_busi_item_id is null ]]>
		<include refid="queryByPolicyCodeList"></include>
                 <![CDATA[))
                 )A 
                 where t.customer_id =A.customer_id)B  
                 order by B.customer_birthday desc)C)D WHERE D.rn <= #{end_number})E WHERE E.RN >#{begain_number}]]>
	</select>
	
	
</mapper>