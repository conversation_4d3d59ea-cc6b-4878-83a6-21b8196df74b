<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICrsCustPolDao">

	<sql id="crsCustPolWhereCondition">
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" customer_id  != null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" submitted_year  != null and submitted_year != '' "><![CDATA[ AND <PERSON><PERSON>SUBMITTED_YEAR = #{submitted_year} ]]></if>
		<if test=" acknowledge_date  != null  and  acknowledge_date  != ''  "><![CDATA[ AND A.ACKNOWLEDGE_DATE = #{acknowledge_date} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" organ_name != null and organ_name != ''  "><![CDATA[ AND A.ORGAN_NAME = #{organ_name} ]]></if>
		<if test=" submit_channel  != null "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" list_id  != null and list_id != ''"><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" customer_gender  != null and customer_gender != ''"><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCrsCustPolByCustomerIdCondition">
		<if test=" customer_id  != null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="queryCrsCustPolByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryCrsCustPolBySubmittedYearCondition">
		<if test=" submitted_year  != null and submitted_year != '' "><![CDATA[ AND A.SUBMITTED_YEAR = #{submitted_year} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCrsCustPol"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CRS_CUST_POL__LIST_ID.nextval FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CRS_CUST_POL(
				CUSTOMER_NAME, INSERT_TIME, AGENT_NAME, CUSTOMER_ID, UPDATE_TIME, SUBMITTED_YEAR, ACKNOWLEDGE_DATE, 
				CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, ORGAN_CODE, ORGAN_NAME, SUBMIT_CHANNEL, POLICY_CODE, UPDATE_BY, 
				MOBILE_TEL, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, AGENT_CODE, CUSTOMER_GENDER, CUSTOMER_TAX_CERT_TYPE, CUSTOMER_TAX_BIRTHDAY, COUNTRY_CODE, TAX_RESIDENT_TYPE, ACCOUNT_BALANCE ) 
			VALUES (
				#{customer_name, jdbcType=VARCHAR}, SYSDATE , #{agent_name, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{submitted_year, jdbcType=NUMERIC} , #{acknowledge_date, jdbcType=DATE} 
				, #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{organ_name, jdbcType=VARCHAR} , #{submit_channel, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{mobile_tel, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{agent_code, jdbcType=VARCHAR} , #{customer_gender, jdbcType=NUMERIC}, #{customer_tax_cert_type, jdbcType=VARCHAR}, #{customer_tax_birthday, jdbcType=DATE}
				 , #{country_code, jdbcType=VARCHAR}, #{tax_resident_type, jdbcType=VARCHAR} , #{account_balance, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCrsCustPol" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CRS_CUST_POL WHERE LIST_ID = #{list_id, jdbcType=NUMERIC} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCrsCustPol" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CRS_CUST_POL ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    SUBMITTED_YEAR = #{submitted_year, jdbcType=NUMERIC} ,
		    ACKNOWLEDGE_DATE = #{acknowledge_date, jdbcType=DATE} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			ORGAN_NAME = #{organ_name, jdbcType=VARCHAR} ,
		    SUBMIT_CHANNEL = #{submit_channel, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			MOBILE_TEL = #{mobile_tel, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		    CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id, jdbcType=NUMERIC} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCrsCustPolByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.AGENT_NAME, A.CUSTOMER_ID, A.SUBMITTED_YEAR, A.ACKNOWLEDGE_DATE, 
			A.CUSTOMER_CERTI_CODE, A.ORGAN_CODE, A.ORGAN_NAME, A.SUBMIT_CHANNEL, A.POLICY_CODE, 
			A.MOBILE_TEL, A.LIST_ID, A.AGENT_CODE, A.CUSTOMER_GENDER FROM APP___PAS__DBUSER.T_CRS_CUST_POL A WHERE 1 = 1  ]]>
		<include refid="crsCustPolWhereCondition" />
	</select>
	
	<select id="findCrsCustPolByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.AGENT_NAME, A.CUSTOMER_ID, A.SUBMITTED_YEAR, A.ACKNOWLEDGE_DATE, 
			A.CUSTOMER_CERTI_CODE, A.ORGAN_CODE, A.ORGAN_NAME, A.SUBMIT_CHANNEL, A.POLICY_CODE, 
			A.MOBILE_TEL, A.LIST_ID, A.AGENT_CODE, A.CUSTOMER_GENDER FROM APP___PAS__DBUSER.T_CRS_CUST_POL A WHERE 1 = 1  ]]>
		<include refid="crsCustPolWhereCondition" />
	</select>
	
	<select id="findCrsCustPolBySubmittedYear" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.AGENT_NAME, A.CUSTOMER_ID, A.SUBMITTED_YEAR, A.ACKNOWLEDGE_DATE, 
			A.CUSTOMER_CERTI_CODE, A.ORGAN_CODE, A.ORGAN_NAME, A.SUBMIT_CHANNEL, A.POLICY_CODE, 
			A.MOBILE_TEL, A.LIST_ID, A.AGENT_CODE, A.CUSTOMER_GENDER FROM APP___PAS__DBUSER.T_CRS_CUST_POL A WHERE 1 = 1  ]]>
		<include refid="crsCustPolWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCrsCustPol" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.AGENT_NAME, A.CUSTOMER_ID, A.SUBMITTED_YEAR, A.ACKNOWLEDGE_DATE, 
			A.CUSTOMER_CERTI_CODE, A.ORGAN_CODE, A.ORGAN_NAME, A.SUBMIT_CHANNEL, A.POLICY_CODE, 
			A.MOBILE_TEL, A.LIST_ID, A.AGENT_CODE, A.CUSTOMER_GENDER FROM APP___PAS__DBUSER.T_CRS_CUST_POL A WHERE ROWNUM <=  1000  ]]>
		<include refid="crsCustPolWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCrsCustPol" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.AGENT_NAME, A.CUSTOMER_ID, A.SUBMITTED_YEAR, A.ACKNOWLEDGE_DATE, 
			A.CUSTOMER_CERTI_CODE, A.ORGAN_CODE, A.ORGAN_NAME, A.SUBMIT_CHANNEL, A.POLICY_CODE, 
			A.MOBILE_TEL, A.LIST_ID, A.AGENT_CODE, A.CUSTOMER_GENDER FROM APP___PAS__DBUSER.T_CRS_CUST_POL A WHERE ROWNUM <=  1000  ]]>
		<include refid="crsCustPolWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCrsCustPolTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CRS_CUST_POL A WHERE 1 = 1  ]]>
		<include refid="crsCustPolWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCrsCustPolForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_NAME, B.AGENT_NAME, B.CUSTOMER_ID, B.SUBMITTED_YEAR, B.ACKNOWLEDGE_DATE, 
			B.CUSTOMER_CERTI_CODE, B.ORGAN_CODE, B.ORGAN_NAME, B.SUBMIT_CHANNEL, B.POLICY_CODE, 
			B.MOBILE_TEL, B.LIST_ID, B.AGENT_CODE, B.CUSTOMER_GENDER FROM (
					SELECT ROWNUM RN, A.CUSTOMER_NAME, A.AGENT_NAME, A.CUSTOMER_ID, A.SUBMITTED_YEAR, A.ACKNOWLEDGE_DATE, 
			A.CUSTOMER_CERTI_CODE, A.ORGAN_CODE, A.ORGAN_NAME, A.SUBMIT_CHANNEL, A.POLICY_CODE, 
			A.MOBILE_TEL, A.LIST_ID, A.AGENT_CODE, A.CUSTOMER_GENDER FROM APP___PAS__DBUSER.T_CRS_CUST_POL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="crsCustPolWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findAllCrsCustPolByCRS" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	   SELECT ROWNUM,T.* FROM (
		SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TC.CUSTOMER_ID,TCT.TAX_RESIDENT_TYPE
				  FROM DEV_PAS.T_CUSTOMER TC,
               DEV_PAS.T_CUSTOMER_TAX TCT,
               DEV_PAS.T_CONTRACT_MASTER TCM,
               DEV_PAS.T_POLICY_HOLDER TPH
         WHERE 1 = 1
           AND TCM.POLICY_CODE = TPH.POLICY_CODE
           AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
           AND TCM.LIABILITY_STATE = '1'
           AND TCT.CUSTOMER_TAX_NAME = TC.CUSTOMER_NAME
         AND TCT.CUSTOMER_TAX_GENDER = TC.CUSTOMER_GENDER
         AND TCT.CUSTOMER_TAX_BIRTHDAY = TC.CUSTOMER_BIRTHDAY
         AND TCT.CUSTOMER_TAX_CERT_TYPE = TC.CUSTOMER_CERT_TYPE
         AND TCT.CUSTOMER_TAX_CERTI_CODE = TC.CUSTOMER_CERTI_CODE
          AND (TCT.TAX_RESIDENT_TYPE = '5' OR TCT.TAX_RESIDENT_TYPE = '4' OR TCT.TAX_RESIDENT_TYPE IS NULL)
					 AND TCM.VALIDATE_DATE >= #{currYearFirst}
					 AND TCM.VALIDATE_DATE <= #{currYearLast}	
			AND EXISTS(SELECT 1 FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
               APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP WHERE TCM.POLICY_CODE = TCBP.POLICY_CODE
           AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
           AND TBP.TAX_REVENUE_FLAG = '1')		 
					 
		UNION
		SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TC.CUSTOMER_ID,TCT.TAX_RESIDENT_TYPE
				   FROM DEV_PAS.T_CUSTOMER TC,
          DEV_PAS.T_CUSTOMER_TAX TCT,
               DEV_PAS.T_CONTRACT_MASTER TCM,
               DEV_PAS.T_INSURED_LIST TL
         WHERE 1 = 1
           AND TCM.POLICY_CODE = TL.POLICY_CODE
           AND TL.CUSTOMER_ID = TC.CUSTOMER_ID
           AND TCM.LIABILITY_STATE = '1'
           AND TCT.CUSTOMER_TAX_NAME = TC.CUSTOMER_NAME
         AND TCT.CUSTOMER_TAX_GENDER = TC.CUSTOMER_GENDER
         AND TCT.CUSTOMER_TAX_BIRTHDAY = TC.CUSTOMER_BIRTHDAY
         AND TCT.CUSTOMER_TAX_CERT_TYPE = TC.CUSTOMER_CERT_TYPE
         AND TCT.CUSTOMER_TAX_CERTI_CODE = TC.CUSTOMER_CERTI_CODE
         AND (TCT.TAX_RESIDENT_TYPE = '5' OR TCT.TAX_RESIDENT_TYPE = '4' OR TCT.TAX_RESIDENT_TYPE IS NULL)
		AND TCM.VALIDATE_DATE >= #{currYearFirst}
		AND TCM.VALIDATE_DATE <= #{currYearLast}
		AND EXISTS(SELECT 1 FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
               APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP WHERE TCM.POLICY_CODE = TCBP.POLICY_CODE
           AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
           AND TBP.TAX_REVENUE_FLAG = '1')
					 ) T WHERE 1=1
					 AND MOD(T.POLICY_ID,#{modNum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
	 ]]>
	</select>
	
	
	<select id="PA_findAllCRSContractBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	   SELECT TCBP.BUSI_ITEM_ID
  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP, APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP
 WHERE 1 = 1
   AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
   AND TBP.TAX_REVENUE_FLAG = '1'
   AND (TBP.PRODUCT_CATEGORY1 <> '20003' OR TBP.PRODUCT_CATEGORY1 <> '20004')
	 ]]>
	 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCBP.POLICY_CODE = #{policy_code} ]]></if>
	</select>
</mapper>
