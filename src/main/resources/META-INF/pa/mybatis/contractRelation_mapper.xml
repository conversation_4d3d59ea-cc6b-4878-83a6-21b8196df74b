<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractRelationDao">

	<sql id="pa_contractrelationwherecondition">
		<if test=" list_id != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" relation_type != null and relation_type != ''  "><![CDATA[ AND A.RELATION_TYPE = #{relation_type} ]]></if>
		<if test=" master_policy_id  != null "><![CDATA[ AND A.MASTER_POLICY_ID = #{master_policy_id} ]]></if>
		<if test=" master_policy_code != null and master_policy_code != ''  "><![CDATA[ AND <PERSON>.MASTER_POLICY_CODE = #{master_policy_code} ]]></if>
		<if test=" master_busi_item_id != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>		
		<if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND A.MASTER_BUSI_PROD_CODE = #{master_busi_prod_code} ]]></if>
		<if test=" sub_policy_id  != null "><![CDATA[ AND A.SUB_POLICY_ID = #{sub_policy_id} ]]></if>
		<if test=" sub_policy_code != null and sub_policy_code != ''  "><![CDATA[ AND A.SUB_POLICY_CODE = #{sub_policy_code} ]]></if>
		<if test=" sub_busi_item_id  != null "><![CDATA[ AND A.SUB_BUSI_ITEM_ID = #{sub_busi_item_id} ]]></if>
		<if test=" sub_busi_prod_code != null and sub_busi_prod_code != ''  "><![CDATA[ AND A.SUB_BUSI_PROD_CODE = #{sub_busi_prod_code} ]]></if>
		<if test="relation_Type_list  != null and relation_Type_list.size()!=0 ">
			<![CDATA[ AND A.relation_type in (]]>
			<foreach collection="relation_Type_list" item="relation_Type_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{relation_Type_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>
	
	<sql id="pa_queryContractRelationByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="PA_addContractRelation" useGeneratedKeys="false"
		parameterType="java.util.Map">
			<selectKey resultType="java.math.BigDecimal" order="BEFORE"
				keyProperty="list_id">
				SELECT APP___PAS__DBUSER.S_CONTRACT_RELATION.nextval from dual
			</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_RELATION(
				LIST_ID, RELATION_TYPE, MASTER_POLICY_ID, MASTER_POLICY_CODE, MASTER_BUSI_ITEM_ID, MASTER_BUSI_PROD_CODE, 
				SUB_POLICY_ID, SUB_POLICY_CODE, SUB_BUSI_ITEM_ID, SUB_BUSI_PROD_CODE, INSERT_BY, INSERT_TIME,
				INSERT_TIMESTAMP,UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP) 
			VALUES (
			#{list_id, jdbcType=NUMERIC} , #{relation_type,jdbcType=VARCHAR}, #{master_policy_id, jdbcType=NUMERIC} ,
			#{master_policy_code,jdbcType=VARCHAR},#{master_busi_item_id,jdbcType=NUMERIC}, #{master_busi_prod_code,jdbcType=VARCHAR}, 
			#{sub_policy_id,jdbcType=NUMERIC}, #{sub_policy_code,jdbcType=VARCHAR}, #{sub_busi_item_id,jdbcType=NUMERIC}, 
			#{sub_busi_prod_code,jdbcType=VARCHAR}, #{insert_by, jdbcType=NUMERIC} ,SYSDATE,CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC},
			SYSDATE,CURRENT_TIMESTAMP
			)
		 ]]>
	</insert>
	
	
	<insert id="PA_addContractRelationWhenHavePrimaryKeyValue" useGeneratedKeys="false"
		parameterType="java.util.Map">
			
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_RELATION(
				LIST_ID, RELATION_TYPE, MASTER_POLICY_ID, MASTER_POLICY_CODE, MASTER_BUSI_ITEM_ID, MASTER_BUSI_PROD_CODE, 
				SUB_POLICY_ID, SUB_POLICY_CODE, SUB_BUSI_ITEM_ID, SUB_BUSI_PROD_CODE, INSERT_BY, INSERT_TIME,
				INSERT_TIMESTAMP,UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP) 
			VALUES (
			#{list_id, jdbcType=NUMERIC} , #{relation_type,jdbcType=VARCHAR}, #{master_policy_id, jdbcType=NUMERIC} ,
			#{master_policy_code,jdbcType=VARCHAR},#{master_busi_item_id,jdbcType=NUMERIC}, #{master_busi_prod_code,jdbcType=VARCHAR}, 
			#{sub_policy_id,jdbcType=NUMERIC}, #{sub_policy_code,jdbcType=VARCHAR}, #{sub_busi_item_id,jdbcType=NUMERIC}, 
			#{sub_busi_prod_code,jdbcType=VARCHAR}, 0 ,SYSDATE,CURRENT_TIMESTAMP, 0,
			SYSDATE,CURRENT_TIMESTAMP
			)
		 ]]>
	</insert>
    <!-- 根据保单号查询主保单信息 -->
	<select id="PA_findAllContractRelationByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	
	    select 
	         a.LIST_ID,
	         a.RELATION_TYPE,
	         a.MASTER_POLICY_ID,
	         a.MASTER_POLICY_CODE,
	         a.MASTER_BUSI_ITEM_ID,
	         a.MASTER_BUSI_PROD_CODE,
	         a.SUB_POLICY_ID,
	         a.SUB_POLICY_CODE,
	         a.SUB_BUSI_ITEM_ID,
	         a.SUB_BUSI_PROD_CODE,
	         (Select BP.acc_risk_single_insure_flag
                 From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP,
                      APP___PDS__DBUSER.t_Business_Product   BP
                where CBP.policy_code = A.SUB_POLICY_CODE
                  and CBP.liability_state = 1
                  and CBP.busi_prd_id = BP.business_prd_id
                  and BP.acc_risk_single_insure_flag = 1) acc_risk_single_insure_flag,
               (Select BP.acc_risk_single_insure_flag
                 From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP,
                      APP___PDS__DBUSER.t_Business_Product   BP
                where CBP.policy_code = A.SUB_POLICY_CODE
                  and CBP.busi_prd_id = BP.business_prd_id
                  and BP.acc_risk_single_insure_flag = 1) acc_insure_flag_new,
              (Select CBP.Liability_State
                 From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP
                where CBP.policy_code = A.sub_policy_code
                  and A.sub_busi_item_id = CBP.busi_item_id) liability_state,
              (select cm.liability_state 
              	 from dev_pas.t_contract_master cm
              	 where cm.policy_code = a.sub_policy_code) sub_policy_liability_state
	     from APP___PAS__DBUSER.t_contract_relation a
	     where 1=1
		     and a.MASTER_POLICY_CODE = #{master_policy_code}
		     and a.RELATION_TYPE = '1'
	</select>
	<!-- 根据保单号查询主保单信息 -->
	<select id="PA_findAllSubPolicyByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	    select 
	         a.LIST_ID,
	         a.RELATION_TYPE,
	         a.MASTER_POLICY_ID,
	         a.MASTER_POLICY_CODE,
	         a.MASTER_BUSI_ITEM_ID,
	         a.MASTER_BUSI_PROD_CODE,
	         a.SUB_POLICY_ID,
	         a.SUB_POLICY_CODE,
	         a.SUB_BUSI_ITEM_ID,
	         a.SUB_BUSI_PROD_CODE
	     from APP___PAS__DBUSER.t_contract_relation a,APP___PAS__DBUSER.t_contract_master b
	     where 1=1
		     and a.sub_policy_code = #{sub_policy_code}
		     and a.RELATION_TYPE = '1'
		     and a.sub_policy_code = b.policy_code
		     and b.liability_state <> '3'
	     ]]>
	</select>
	<!-- 根据保单号查询主保单信息，无非终止判断 -->
	<select id="PA_findSubPolicyByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	    select 
	         a.LIST_ID,
	         a.RELATION_TYPE,
	         a.MASTER_POLICY_ID,
	         a.MASTER_POLICY_CODE,
	         a.MASTER_BUSI_ITEM_ID,
	         a.MASTER_BUSI_PROD_CODE,
	         a.SUB_POLICY_ID,
	         a.SUB_POLICY_CODE,
	         a.SUB_BUSI_ITEM_ID,
	         a.SUB_BUSI_PROD_CODE
	     from APP___PAS__DBUSER.t_contract_relation a,APP___PAS__DBUSER.t_contract_master b
	     where 1=1
		     and a.sub_policy_code = #{sub_policy_code}
		     and a.RELATION_TYPE = '1'
		     and a.sub_policy_code = b.policy_code
	     ]]>
	</select>
	<!-- 根据保单号查询关联主险保单号 -->
	<select id="PA_findMasterContract" resultType="java.util.Map" parameterType="java.util.Map">
	    select 
	         a.sub_policy_code,
	         a.relation_type
	     from APP___PAS__DBUSER.t_contract_relation a
	         where a.MASTER_POLICY_CODE = #{master_policy_code}
	     <if test="relation_type != null and relation_type != ''  ">
	        <![CDATA[ AND A.relation_type = #{relation_type} ]]>
	     </if>	
	</select>
	<!-- 根据子保单查询主保单信息 -->
	<select id="PA_findSubMasterRelation" resultType="java.util.Map" parameterType="java.util.Map">
	
	    select 
	         a.master_policy_code,
	         a.relation_type
	     from APP___PAS__DBUSER.t_contract_relation a
	         where a.sub_policy_code = #{sub_policy_code}
	         and a.relation_type = '1'
	</select>
	
	<!-- 根据保单号查询主保单信息 -->
	<select id="PA_findAllSubPolicyForRB" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	    SELECT B.BUSI_PROD_CODE,B.LIABILITY_STATE,B.END_CAUSE,A.* FROM DEV_PAS.t_contract_relation A,DEV_PAS.T_CONTRACT_BUSI_PROD B 
				WHERE A.MASTER_BUSI_ITEM_ID=B.BUSI_ITEM_ID
				AND B.END_CAUSE IN ('03','05')
				AND B.LIABILITY_STATE='3'
				AND A.SUB_POLICY_CODE=#{sub_policy_code}

	     ]]>
	</select>
	
	<select id="PA_queryDataForAE" resultType="java.util.Map" parameterType="java.util.Map">
	    SELECT 
	         A.LIST_ID,
	         A.RELATION_TYPE,
	         A.MASTER_POLICY_ID,
	         A.MASTER_POLICY_CODE,
	         A.MASTER_BUSI_ITEM_ID,
	         A.MASTER_BUSI_PROD_CODE,
	         A.SUB_POLICY_ID,
	         A.SUB_POLICY_CODE,
	         A.SUB_BUSI_ITEM_ID,
	         A.SUB_BUSI_PROD_CODE
	     FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A	     
	     WHERE A.RELATION_TYPE = '1'
	     	   AND (A.SUB_POLICY_CODE = #{master_policy_code} OR A.MASTER_POLICY_CODE=#{master_policy_code})
	</select>
	<!-- 根据主保单查询自保单信息 -->
	<select id="findSubBusiProdCodeInfo" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	   
	    select a.SUB_POLICY_CODE,a.MASTER_POLICY_CODE
          from APP___PAS__DBUSER.t_contract_relation a
          where 1 = 1
          and a.MASTER_POLICY_CODE =#{master_policy_code}
          and a.RELATION_TYPE =#{relation_type}
         
	     ]]>
	</select>
	
	<select id="PA_queryMasterPolicyBySubPolicy" resultType="java.util.Map" parameterType="java.util.Map">
         SELECT 
	         A.LIST_ID,
	         A.RELATION_TYPE,
	         A.MASTER_POLICY_ID,
	         A.MASTER_POLICY_CODE,
	         A.MASTER_BUSI_ITEM_ID,
	         A.MASTER_BUSI_PROD_CODE,
	         A.SUB_POLICY_ID,
	         A.SUB_POLICY_CODE,
	         A.SUB_BUSI_ITEM_ID,
	         A.SUB_BUSI_PROD_CODE,
	         (Select bp.acc_risk_single_insure_flag
                 From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP,
                      APP___PDS__DBUSER.t_Business_Product   BP
                where CBP.policy_code = A.SUB_POLICY_CODE
                  and A.sub_busi_item_id = CBP.busi_item_id
                  and CBP.busi_prd_id = BP.business_prd_id
                  and BP.acc_risk_single_insure_flag = 1) acc_risk_single_insure_flag,
             (Select CBP.Liability_State
                 From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP
                where CBP.policy_code = A.sub_policy_code
                  and A.sub_busi_item_id = CBP.busi_item_id) liability_state,
              (Select CBP.Liability_State
                 From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP
                where CBP.policy_code = A.MASTER_POLICY_CODE
                  and A.MASTER_BUSI_ITEM_ID = CBP.busi_item_id) master_liability_state,
              (select cm.liability_state 
              	 from dev_pas.t_contract_master cm
              	 where cm.policy_code = a.master_policy_code) master_policy_liability_state
	     FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A
	     WHERE 1=1
		     AND A.SUB_POLICY_CODE = #{sub_policy_code}
		     AND A.RELATION_TYPE = '1'
	</select>
	
	<update id="updateContractRelation" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_RELATION ]]>
		<set>
		<trim suffixOverrides=",">
			RELATION_TYPE = #{relation_type, jdbcType=VARCHAR} ,
		    MASTER_POLICY_ID = #{master_policy_id, jdbcType=NUMERIC} ,
			MASTER_POLICY_CODE = #{master_policy_code, jdbcType=VARCHAR} ,
		    MASTER_BUSI_ITEM_ID = #{master_busi_item_id, jdbcType=NUMERIC} ,
			MASTER_BUSI_PROD_CODE = #{master_busi_prod_code, jdbcType=VARCHAR} ,
		    SUB_POLICY_ID = #{sub_policy_id, jdbcType=NUMERIC} ,
			SUB_POLICY_CODE = #{sub_policy_code, jdbcType=VARCHAR} ,
		    SUB_BUSI_ITEM_ID = #{sub_busi_item_id, jdbcType=NUMERIC} ,
			SUB_BUSI_PROD_CODE = #{sub_busi_prod_code, jdbcType=VARCHAR} ,
			INSERT_BY =  #{insert_by, jdbcType=NUMERIC} ,
			INSERT_TIME =  SYSDATE ,
			INSERT_TIMESTAMP = SYSDATE,
			UPDATE_BY =  #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME =  SYSDATE ,
			UPDATE_TIMESTAMP = SYSDATE,
		</trim>
		</set>
		<![CDATA[ WHERE 1=1  ]]>
		<include refid="pa_queryContractRelationByListIdCondition" />
	</update>
	
	<delete id="deleteContractRelation" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A WHERE  1=1 ]]>
		<include refid="pa_contractrelationwherecondition" />
	</delete>
	
	<delete id="deleteContractRelationByListId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A WHERE  1=1 ]]>
		<include refid="pa_queryContractRelationByListIdCondition" />
	</delete>
	<select id="PA_findAllContractRelation" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	    select 
	         a.LIST_ID,
	         a.RELATION_TYPE,
	         a.MASTER_POLICY_ID,
	         a.MASTER_POLICY_CODE,
	         a.MASTER_BUSI_ITEM_ID,
	         a.MASTER_BUSI_PROD_CODE,
	         a.SUB_POLICY_ID,
	         a.SUB_POLICY_CODE,
	         a.SUB_BUSI_ITEM_ID,
	         a.SUB_BUSI_PROD_CODE
	     from APP___PAS__DBUSER.t_contract_relation a
	     where 1=1
	     ]]>
	     <include refid="pa_contractrelationwherecondition" />
	</select>
	<!-- 129800 add 查询险种关联保单信息 -->
	<select id="PA_findAllContractRelationByPolicyCodeAndBusiItemID" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	    SELECT CM.POLICY_CODE,
		       CM.LIABILITY_STATE,
		       CBP.BUSI_ITEM_ID,
		       CBP.BUSI_PROD_CODE,
		       TEMP.THIS_BUSI_ITEM_ID,
		       TEMP.RELATION_TYPE,
		       TEMP.POLICY_RELATION_TYPE
		  FROM (
		        /*非多主险*/
		        SELECT CM.RELATION_POLICY_CODE POLICY_CODE, /*关联的保单号*/
		                TCBP.BUSI_ITEM_ID, /*关联的险种id*/
		                A.Busi_Item_Id THIS_BUSI_ITEM_ID, /*本保单险种id*/
		                case TO_CHAR(CM.POLICY_RELATION_TYPE)
		                  when '0' then
		                   '01'
		                  when '1' then
		                   '02'
		                  when '2' then
		                   '03'
		                end RELATION_TYPE,
		                (SELECT policy_RELATION_NAME
		                   FROM DEV_PAS.T_policy_RELATION_type
		                  WHERE policy_RELATION_TYPE = TO_CHAR(CM.POLICY_RELATION_TYPE)) AS POLICY_RELATION_TYPE
		          FROM DEV_PAS.T_CONTRACT_MASTER    CM,
		                DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
		                DEV_PAS.T_CONTRACT_BUSI_PROD A
		         WHERE CM.RELATION_POLICY_CODE = TCBP.POLICY_CODE
		           AND A.POLICY_CODE = CM.POLICY_CODE
		           and (cm.multi_mainrisk_flag is null or
		               cm.multi_mainrisk_flag <> '1') ]]>
		           <if test=" master_busi_item_id != null "><![CDATA[ and a.busi_item_id = #{master_busi_item_id} ]]></if>
		           <if test=" master_policy_code != null and master_policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{master_policy_code} ]]></if>
		 <![CDATA[ and CM.RELATION_POLICY_CODE IS NOT NULL
		        union
		        /*多主险*/
		        SELECT TCR.SUB_POLICY_CODE POLICY_CODE, /*关联的保单号*/
		                TCR.SUB_BUSI_ITEM_ID BUSI_ITEM_ID, /*关联的险种id*/
		                TCR.MASTER_BUSI_ITEM_ID THIS_BUSI_ITEM_ID, /*本保单险种id*/
		                case TCR.RELATION_TYPE
		                  when '2' then
		                   '01'
		                  when '3' then
		                   '02'
		                  when '4' then
		                   '03'
		                end RELATION_TYPE,
		                (SELECT contract_RELATION_NAME
		                   FROM DEV_PAS.T_CONTRACT_RELATION_type
		                  WHERE contract_RELATION_TYPE = TCR.RELATION_TYPE) AS POLICY_RELATION_TYPE
		          FROM DEV_PAS.T_CONTRACT_MASTER    CM,
		                DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
		                DEV_PAS.T_CONTRACT_RELATION  TCR
		         WHERE CM.POLICY_CODE = TCR.MASTER_POLICY_CODE
		           AND CM.POLICY_CODE = TCBP.POLICY_CODE
		           AND TCBP.BUSI_ITEM_ID = TCR.MASTER_BUSI_ITEM_ID
		           and cm.multi_mainrisk_flag = '1'
		           and TCR.RELATION_TYPE <> '1' ]]>
		           <if test=" master_busi_item_id != null "><![CDATA[ and TCR.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		           <if test=" master_policy_code != null and master_policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{master_policy_code} ]]></if>
		 <![CDATA[ ) TEMP
		 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		    ON TEMP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
		  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
		    ON CM.POLICY_CODE = CBP.POLICY_CODE
	     ]]>
	</select>
	<select id="PA_findAllContractRelationAndLiabilityState" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	    SELECT tcbp.liability_state,A.*
		  FROM APP___PAS__DBUSER.t_Contract_Relation A,
		       APP___PAS__DBUSER.t_Contract_Busi_Prod tcbp
		 WHERE 1 = 1
		   and A.master_policy_code = tcbp.policy_code
		   and A.master_busi_item_id = tcbp.busi_item_id
	     ]]>
	     <include refid="pa_contractrelationwherecondition" />
	</select>
	
	<select id="PA_findAllContractRelationByPolicy" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	    select 
	         a.LIST_ID,
	         a.RELATION_TYPE,
	         a.MASTER_POLICY_ID,
	         a.MASTER_POLICY_CODE,
	         a.MASTER_BUSI_ITEM_ID,
	         a.MASTER_BUSI_PROD_CODE,
	         a.SUB_POLICY_ID,
	         a.SUB_POLICY_CODE,
	         a.SUB_BUSI_ITEM_ID,
	         a.SUB_BUSI_PROD_CODE
	     from APP___PAS__DBUSER.t_contract_relation a
	     where 1=1
	     and (a.MASTER_POLICY_CODE = #{master_policy_code} OR a.SUB_POLICY_CODE = #{sub_policy_code})
	     ]]>
	</select>
	
	<!-- 查询所有关联保单信息+险种名称 -->
	<select id="PA_findAllContractRelationDesc" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
            SELECT A.LIST_ID,
                 A.RELATION_TYPE,
                 A.MASTER_POLICY_ID,
                 A.MASTER_POLICY_CODE,
                 A.MASTER_BUSI_ITEM_ID,
                 (SELECT B.PRODUCT_NAME_STD
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.MASTER_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS MASTER_PRODUCT_NAME,
                 A.MASTER_BUSI_PROD_CODE,
                  (SELECT B.PRODUCT_CATEGORY1
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.MASTER_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS MASTER_PRODUCT_CATEGORY1,
                 (SELECT B.LIABILITY_STATE
                    FROM APP___PAS__DBUSER.t_Contract_Busi_Prod B
                   WHERE A.MASTER_BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS MASTER_LIABILITY_STATE,
                 A.SUB_POLICY_ID,
                 A.SUB_POLICY_CODE,
                 A.SUB_BUSI_ITEM_ID,
                 (SELECT B.PRODUCT_NAME_STD
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.SUB_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS SUB_PRODUCT_NAME,
                 A.SUB_BUSI_PROD_CODE,
                 (SELECT B.PRODUCT_CATEGORY1
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.SUB_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS SUB_PRODUCT_CATEGORY1,
                                  (SELECT B.LIABILITY_STATE
                    FROM APP___PAS__DBUSER.t_Contract_Busi_Prod B
                   WHERE A.SUB_BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS SUB_LIABILITY_STATE
            FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A
           WHERE 1 = 1
	     ]]>
	     <include refid="pa_contractrelationwherecondition" />
	</select>
	
	<!-- 132611  查询险种关联保单信息 -->
	<select id="PA_findContractRelationByBusiItemID" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	    SELECT     TEMP.POLICY_CODE,
           CM.LIABILITY_STATE,
           TEMP.BUSI_ITEM_ID,
           CBP.BUSI_PROD_CODE,
           TEMP.THIS_BUSI_ITEM_ID,
           TEMP.RELATION_TYPE
      FROM (
            /*非多主险*/
            SELECT CM.RELATION_POLICY_CODE POLICY_CODE, /*关联的保单号*/
                    TCBP.BUSI_ITEM_ID, /*关联的险种id*/
                    A.Busi_Item_Id THIS_BUSI_ITEM_ID, /*本保单险种id*/
                    case TO_CHAR(CM.POLICY_RELATION_TYPE)
                      when '0' then
                       '0'
                      when '1' then
                       '1'
                      when '2' then
                       '2'
                    end RELATION_TYPE
              FROM DEV_PAS.T_CONTRACT_MASTER    CM,
                    DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                    DEV_PAS.T_CONTRACT_BUSI_PROD A
             WHERE CM.RELATION_POLICY_CODE = TCBP.POLICY_CODE
               AND A.POLICY_CODE = CM.POLICY_CODE
               and (cm.multi_mainrisk_flag is null or
                   cm.multi_mainrisk_flag <> '1') ]]>
               <if test=" master_busi_item_id != null "><![CDATA[ and a.busi_item_id = #{master_busi_item_id} ]]></if>
               <if test=" master_policy_code != null and master_policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{master_policy_code} ]]></if>
     <![CDATA[ and CM.RELATION_POLICY_CODE IS NOT NULL
            union
            /*多主险*/
            SELECT TCR.SUB_POLICY_CODE POLICY_CODE, /*关联的保单号*/
                    TCR.SUB_BUSI_ITEM_ID BUSI_ITEM_ID, /*关联的险种id*/
                    TCR.MASTER_BUSI_ITEM_ID THIS_BUSI_ITEM_ID, /*本保单险种id*/
                    case TCR.RELATION_TYPE
                      when '2' then
                       '0'
                      when '3' then
                       '1'
                      when '4' then
                       '2'
                    end RELATION_TYPE
              FROM DEV_PAS.T_CONTRACT_MASTER    CM,
                    DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                    DEV_PAS.T_CONTRACT_RELATION  TCR
             WHERE CM.POLICY_CODE = TCR.MASTER_POLICY_CODE
               AND CM.POLICY_CODE = TCBP.POLICY_CODE
               AND TCBP.BUSI_ITEM_ID = TCR.MASTER_BUSI_ITEM_ID
               and cm.multi_mainrisk_flag = '1'
               and TCR.RELATION_TYPE <> '1' ]]>
               <if test=" master_busi_item_id != null "><![CDATA[ and TCR.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
               <if test=" master_policy_code != null and master_policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{master_policy_code} ]]></if>
     <![CDATA[ ) TEMP
     INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
        ON TEMP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
      LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
        ON CM.POLICY_CODE = CBP.POLICY_CODE
	     ]]>
	</select>
	
		<select id="PA_queryDataForRelationAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	    SELECT 
	         A.LIST_ID,
	         A.RELATION_TYPE,
	         A.MASTER_POLICY_ID,
	         A.MASTER_POLICY_CODE,
	         A.MASTER_BUSI_ITEM_ID,
	         A.MASTER_BUSI_PROD_CODE,
	         A.SUB_POLICY_ID,
	         A.SUB_POLICY_CODE,
	         A.SUB_BUSI_ITEM_ID,
	         A.SUB_BUSI_PROD_CODE
	     FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A	     
	     WHERE A.RELATION_TYPE = '5'
	     	   AND (A.SUB_POLICY_CODE = #{master_policy_code} OR A.MASTER_POLICY_CODE=#{master_policy_code})
	</select>
	
	
	<select id="PA_queryPolicyReleaseFlag" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	  SELECT A.SUB_POLICY_CODE,
       A.SUB_BUSI_PROD_CODE,
       B.LIABILITY_STATE,
       TBP.PRODUCT_NAME_SYS,
       A.RELATION_TYPE
  FROM APP___PAS__DBUSER.T_CONTRACT_RELATION  A,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
 WHERE 1 = 1
   AND A.SUB_BUSI_ITEM_ID = B.BUSI_ITEM_ID
   AND B.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
   AND A.RELATION_TYPE in ('2', '3', '4')
   AND A.MASTER_POLICY_CODE = #{master_policy_code}
  UNION 
   SELECT TCM1.POLICY_CODE SUB_POLICY_CODE,
       TCBP.BUSI_PROD_CODE SUB_BUSI_PROD_CODE,
       TCM1.LIABILITY_STATE,
       TBP.PRODUCT_NAME_SYS,
       CASE
         WHEN TCM.POLICY_RELATION_TYPE = '0' THEN '2'
         WHEN TCM.POLICY_RELATION_TYPE = '1' THEN '3'
         WHEN TCM.POLICY_RELATION_TYPE = '2' THEN '4'
         ELSE ''
       END AS RELATION_TYPE
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
       APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM1,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
 WHERE 1 = 1
   AND TCM.RELATION_POLICY_CODE IS NOT NULL
   AND TCM.POLICY_CODE = #{master_policy_code}
   AND TCM.RELATION_POLICY_CODE = TCM1.POLICY_CODE
   AND TCM1.POLICY_CODE = TCBP.POLICY_CODE
   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
   AND (TCM.POLICY_RELATION_TYPE IS NULL OR TCM.POLICY_RELATION_TYPE in ('0', '1', '2'))
   AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
	     ]]>
	</select>
	
		<!-- 查询所有多主多副关联保单信息+险种名称 -->
	<select id="PA_findAllContractRelationDzdf" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
            SELECT A.LIST_ID,
                 A.RELATION_TYPE,
                 A.MASTER_POLICY_ID,
                 A.MASTER_POLICY_CODE,
                 A.MASTER_BUSI_ITEM_ID,
                 (SELECT B.PRODUCT_NAME_STD
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.MASTER_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS MASTER_PRODUCT_NAME,
                 A.MASTER_BUSI_PROD_CODE,
                  (SELECT B.PRODUCT_CATEGORY1
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.MASTER_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS MASTER_PRODUCT_CATEGORY1,
                 (SELECT B.LIABILITY_STATE
                    FROM APP___PAS__DBUSER.t_Contract_Busi_Prod B
                   WHERE A.MASTER_BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS MASTER_LIABILITY_STATE,
                    (SELECT B.LIABILITY_STATE
                    FROM APP___PAS__DBUSER.t_contract_master B
                   WHERE A.MASTER_POLICY_ID = B.POLICY_ID) AS MASTER_LIABILITY_STATE1,
                   
                   (SELECT B.PRODUCT_CATEGORY1
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.MASTER_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS MASTER_PRODUCT_CATEGORY2,
                 A.SUB_POLICY_ID,
                 A.SUB_POLICY_CODE,
                 A.SUB_BUSI_ITEM_ID,
                 (SELECT B.PRODUCT_NAME_STD
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.SUB_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS SUB_PRODUCT_NAME,
                 A.SUB_BUSI_PROD_CODE,
                 (SELECT B.PRODUCT_CATEGORY1
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.SUB_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS SUB_PRODUCT_CATEGORY1,
                                  (SELECT B.LIABILITY_STATE
                    FROM APP___PAS__DBUSER.t_Contract_Busi_Prod B
                   WHERE A.SUB_BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS SUB_LIABILITY_STATE,
                    (SELECT B.LIABILITY_STATE
                    FROM APP___PAS__DBUSER.t_contract_master B
                   WHERE A.SUB_POLICY_ID = B.POLICY_ID) AS SUB_LIABILITY_STATE1,
                   
                                           (SELECT B.PRODUCT_CATEGORY1
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                   WHERE A.SUB_BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS SUB_PRODUCT_CATEGORY2
            FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A
           WHERE 1 = 1
             AND (A.MASTER_POLICY_CODE = #{policy_code} OR A.SUB_POLICY_CODE = #{policy_code} )]]>
	     <include refid="pa_contractrelationwherecondition" />
	</select>
	
	<select id="PA_getRelaRiskListInfo" resultType="java.util.Map" parameterType="java.util.Map">
	    SELECT A.LIST_ID,
       A.RELATION_TYPE,
       A.MASTER_POLICY_ID,
       A.MASTER_POLICY_CODE,
       A.MASTER_BUSI_ITEM_ID,
       A.MASTER_BUSI_PROD_CODE,
       TCM1.LIABILITY_STATE liability_state1,
       A.SUB_POLICY_ID,
       A.SUB_POLICY_CODE,
       A.SUB_BUSI_ITEM_ID,
       A.SUB_BUSI_PROD_CODE,
       TCM2.LIABILITY_STATE liability_state2
  FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A,
       DEV_PAS.T_CONTRACT_MASTER             TCM1,
       DEV_PAS.T_CONTRACT_MASTER             TCM2
 WHERE 1 = 1
   AND A.MASTER_POLICY_ID = TCM1.POLICY_ID
   AND A.SUB_POLICY_ID = TCM2.POLICY_ID
   AND A.RELATION_TYPE = '1'
   AND (A.SUB_POLICY_CODE = #{master_policy_code} OR A.MASTER_POLICY_CODE=#{master_policy_code})
	</select>
	
	
	
	<!-- 根据保单号查询主保单关联附加险的信息 -->
	<select id="PA_queryAllContractRelationByPolicyCodeAndBusi" resultType="java.util.Map" parameterType="java.util.Map">
		select a.LIST_ID,
		       a.RELATION_TYPE,
		       a.MASTER_POLICY_ID,
		       a.MASTER_POLICY_CODE,
		       a.MASTER_BUSI_ITEM_ID,
		       a.MASTER_BUSI_PROD_CODE,
		       a.SUB_POLICY_ID,
		       a.SUB_POLICY_CODE,
		       a.SUB_BUSI_ITEM_ID,
		       a.SUB_BUSI_PROD_CODE,
		       (select se.customer_id
		          from dev_pas.T_SECOND_POLICY_HOLDER se
		         where se.policy_code = a.sub_policy_code) second_customer_id,
		       (Select CBP.Liability_State
		          From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP
		         where CBP.policy_code = A.sub_policy_code
		           and A.sub_busi_item_id = CBP.busi_item_id) liability_state
		  from APP___PAS__DBUSER.t_contract_relation a
		 where 1 = 1
		   and a.MASTER_POLICY_CODE = #{master_policy_code}
		   and a.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}
		   and a.RELATION_TYPE = '1'
	</select>
	
	
	<select id="PA_queryAllContractRelationBySubPolicy" resultType="java.util.Map" parameterType="java.util.Map">
         SELECT 
	         A.LIST_ID,
	         A.RELATION_TYPE,
	         A.MASTER_POLICY_ID,
	         A.MASTER_POLICY_CODE,
	         A.MASTER_BUSI_ITEM_ID,
	         A.MASTER_BUSI_PROD_CODE,
	         A.SUB_POLICY_ID,
	         A.SUB_POLICY_CODE,
	         A.SUB_BUSI_ITEM_ID,
	         A.SUB_BUSI_PROD_CODE,
	         (select se.customer_id
		          from dev_pas.T_SECOND_POLICY_HOLDER se
		         where se.policy_code = a.MASTER_POLICY_CODE) second_customer_id,
             (Select CBP.Liability_State
                 From APP___PAS__DBUSER.t_Contract_Busi_Prod CBP
                where CBP.policy_code = A.MASTER_POLICY_CODE
                  and A.MASTER_BUSI_ITEM_ID = CBP.busi_item_id) master_liability_state
	     FROM APP___PAS__DBUSER.T_CONTRACT_RELATION A
	     WHERE 1=1
		     AND A.SUB_POLICY_CODE = #{sub_policy_code}
		     and A.SUB_BUSI_ITEM_ID = #{sub_busi_item_id}
		     AND A.RELATION_TYPE = '1'
	</select>
	
	
</mapper>
