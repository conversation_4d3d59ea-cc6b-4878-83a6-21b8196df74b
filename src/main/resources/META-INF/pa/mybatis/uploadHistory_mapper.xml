<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ClaimCompMapper">
    <!-- sunjl_wb 查询保单历史上载记录 -->
    <select id="queryPolicyUploadHistory" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT * FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A where 1=1]]>
        <if test=" plan_name != null and plan_name != '' "><![CDATA[and A.PLAN_NAME like '%${plan_name}%' ]]></if>
        <if test=" organ_code != null and organ_code != '' "><![CDATA[ and A.organ_code = #{organ_code} ]]></if>      
        <if test=" start_date != null and end_date != null "><![CDATA[ and <PERSON>.UPLOAD_DATE between #{start_date} and #{end_date} ]]></if>       
    </select>
    
    <sql id="queryPolicyUploadHistoryCondition"> 
		<if test=" plan_name != null and plan_name != '' "><![CDATA[and A.PLAN_NAME like '%${plan_name}%' ]]></if>
        <if test=" organ_code != null and organ_code != '' "><![CDATA[ and A.organ_code = #{organ_code} ]]></if>    
        <if test="start_date != null and end_date != null "><![CDATA[ and A.UPLOAD_DATE between #{start_date} and #{end_date} ]]></if>
	</sql>	
    
    <select id="queryPolicyUploadHistoryTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A left join APP___PAS__DBUSER.t_udmp_user C on A.Insert_By = C.User_Id
				 where 1=1 
				 and A.SURVEY_MODE = #{survey_mode}
				 and A.Biz_Type = #{biz_type}   ]]>
		<include refid="queryPolicyUploadHistoryCondition" />
	</select>
	
	<select id="queryPolicyUploadHistoryForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select B.RN,
				       B.BIZ_TYPE,
				       B.SURVEY_REASON,
				       B.PLAN_ID,
				       B.SURVEY_MODE,
				       B.ORGAN_CODE,
				       B.UPLOAD_DATE,
				       B.PLAN_NAME,
				       B.BATCH_ID,
				       B.EXTRACT_RULE,
				       B.INSERT_BY,
				       B.User_Name
				from (SELECT ROWNUM rn,
				       A.BIZ_TYPE,
				       A.SURVEY_REASON,
				       A.PLAN_ID,
				       A.SURVEY_MODE,
				       A.ORGAN_CODE,
				       A.UPLOAD_DATE,
				       A.PLAN_NAME,
				       A.BATCH_ID,
				       A.EXTRACT_RULE,
				       A.INSERT_BY,
				       C.User_Name
				  FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A left join APP___PAS__DBUSER.t_udmp_user C on A.Insert_By = C.User_Id
				 where 1 = 1  
				 	and ROWNUM <= #{LESS_NUM} ]]>
		 <include refid="queryPolicyUploadHistoryCondition" />
		<![CDATA[ and A.SURVEY_MODE = #{survey_mode} 
				  and A.Biz_Type = #{biz_type}
				 order by  A.Batch_Id ) B        
				 where B.RN > #{GREATER_NUM}  ]]>
	</select>
    
</mapper>
















