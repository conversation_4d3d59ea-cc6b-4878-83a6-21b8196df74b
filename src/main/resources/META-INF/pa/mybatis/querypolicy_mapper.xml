<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.impl.querypolicylist.dao.IQueryPolicyListDao">

	<sql id="customerInfoCondition">
	<!-- 	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if> -->
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND <PERSON><PERSON>CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</sql>
	
	<sql id="customerPolicyCondition">
		<!-- 保单id-->
		<if test=" policy_id_list != null and policy_id_list.size() !=0 ">
			<![CDATA[ AND t.POLICY_ID IN ]]>
			<foreach collection ="policy_id_list" item="policy_id_list" index="index" open="(" close=")" separator=",">#{policy_id_list}</foreach>
		</if>
		<if test=" policy_code_list != null and policy_code_list.size() !=0 ">
			<![CDATA[ AND t.POLICY_CODE IN ]]>
			<foreach collection ="policy_code_list" item="policy_code_list" index="index" open="(" close=")" separator=",">#{policy_code_list}</foreach>
		</if>
	</sql>

	<!-- 查询投保人保单 -->
	<select id="queryHolderPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT t.policy_code,t.customer_id, t.policy_id
  FROM APP___PAS__DBUSER.t_policy_holder t
  left join APP___PAS__DBUSER.t_customer A
    on t.customer_id = A.customer_id
 where 1=1 ]]>
 <include refid="customerInfoCondition" />
	</select>
	
	<!-- 查询被保人保单 -->
	<select id="queryInsuredPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT t.policy_code, t.customer_id, t.policy_id
  					FROM APP___PAS__DBUSER.t_insured_list t
  					left join APP___PAS__DBUSER.t_customer A
    				on t.customer_id = A.customer_id
 					where 1=1 ]]>
 <include refid="customerInfoCondition" />
	</select>
	
	<!-- 查询受益人保单 -->
	<select id="queryBenefitPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT t.policy_code,t.customer_id, t.policy_id
  FROM APP___PAS__DBUSER.T_CONTRACT_BENE t
  left join APP___PAS__DBUSER.t_customer A
    on t.customer_id = A.customer_id
 where 1=1 ]]>
 <include refid="customerInfoCondition" />
	</select>
	
	<!-- 查询保单主表信息 -->
	<select id="queryPolicyList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT t.policy_id,
		       t.policy_code,
		       t.liability_state,
		       t.end_cause,
		       ph.customer_id as holder_id ,
		       ph.customer_name as holder_name ,
		       t.validate_date,
		       t.sale_agent_code,
		       t.sale_agent_name,
		       t.organ_code
		  FROM APP___PAS__DBUSER.t_contract_master t
		  left join (SELECT tcu.customer_name,
		                    tph.customer_id,
		                    tph.policy_code,
		                    tph.policy_id
		               FROM APP___PAS__DBUSER.t_policy_holder tph
		               left join APP___PAS__DBUSER.t_customer tcu
		                 on tcu.customer_id = tph.customer_id) ph
		    on t.policy_id = ph.policy_id where 1=1
	]]>
 <include refid="customerPolicyCondition" />
	</select>
	
	<!-- 查询险种信息 -->
	<select id="queryContractBusiList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT t.policy_id, 
			t.policy_code, 
			t.busi_item_id,
       		t.liability_state,
       		t.validate_date,
       		t.busi_prod_code,
       t.expiry_date,
       temp.amount,
       t.master_busi_item_id
  FROM APP___PAS__DBUSER.t_contract_busi_prod t
  left join (SELECT tcd.busi_item_id, sum(tcd.amount) as amount
               FROM APP___PAS__DBUSER.t_contract_product tcd
              group by tcd.busi_item_id) temp
    on temp.busi_item_id = t.busi_item_id
 where 1=1
	]]>
 <include refid="customerPolicyCondition" />
	</select>
	
	<!-- 查询被保人信息 -->
	<select id="queryInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT t.policy_id, t.policy_code,  t.customer_id , tcu.customer_name, tbi.order_id
  FROM APP___PAS__DBUSER.t_insured_list t
  left join APP___PAS__DBUSER.t_customer tcu
    on t.customer_id = tcu.customer_id
  left join APP___PAS__DBUSER.t_benefit_insured tbi
    on t.list_id = tbi.insured_id where 1=1
	]]>
 <include refid="customerPolicyCondition" />
	</select>

</mapper>