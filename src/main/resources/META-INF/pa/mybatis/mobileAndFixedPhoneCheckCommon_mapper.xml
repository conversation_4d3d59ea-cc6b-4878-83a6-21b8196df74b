<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IMobileAndFixedPhoneCheckCommonDao">
	
	
	
	<select id="queryMobileForCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ select distinct taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from (]]>
                
               <!-- 投保人名下有效保单  begin -->
             	 <![CDATA[ 
                
                select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                  from APP___PAS__DBUSER.T_CONTRACT_MASTER m
                  left join APP___PAS__DBUSER.t_contract_product pro on m.policy_id = pro.policy_id
                  left join APP___PAS__DBUSER.T_POLICY_HOLDER ph 
                  on m.policy_id = ph.policy_id
                  left join APP___PAS__DBUSER.T_ADDRESS ad
                  on ad.address_id = ph.address_id
                  left join APP___PAS__DBUSER.T_CUSTOMER c 
                  on c.customer_id = ad.customer_id
                  where  1 = 1  ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[ and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                      from dual)) > 1 and m.liability_state not in('3','4'))
                  and  ad.mobile_tel = #{mobile_tel}
                 ]]>
                 <if test=" organ_code != null and organ_code != ''  and organ_code == '8649'  "><![CDATA[ 
                	 and (abs((select ((months_between(c.customer_birthday, SYSDATE)) / 12)
                      from dual)) > 17)
                  ]]></if>
                 <if test=" organ_code != null and organ_code != ''  and organ_code == '8621'  "><![CDATA[ 
                	and pro.pause_date is null  
                  ]]></if>
                 
                 
                  <![CDATA[ 
               
                union 
                select distinct c.customer_id,
                                 c.customer_name,
                                 c.customer_gender,
                                 c.customer_birthday,
                                 c.customer_cert_type,
                                 c.customer_certi_code
                  from APP___PAS__DBUSER.t_contract_master m
                  left join APP___PAS__DBUSER.T_INSURED_LIST l 
                  on m.policy_id = l.policy_id
                  left join APP___PAS__DBUSER.t_address ad
                  on ad.address_id = l.address_id
                  left join APP___PAS__DBUSER.t_Customer c 
                  on c.customer_id = ad.customer_id
                  where  1 = 1  ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[ and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                      from dual)) > 1 and m.liability_state not in('3','4'))
                       and ad.mobile_tel = #{mobile_tel}
                 ]]>
                 <if test=" organ_code != null and organ_code != ''  and organ_code == '8649'  "><![CDATA[ 
                	 and (abs((select ((months_between(c.customer_birthday, SYSDATE)) / 12)
                      from dual)) > 17)
                  ]]></if>
                 <!-- 29501山西分公司:客户移动电话不得与8621-北京分公司、8653-甘肃分公司、8638-山西分公司及其下辖机构的其他有效投保人（存在有效或失效保单的投保人，即有效投保人）的手机号码一致 ,添加条件将‘被保人’去掉-->
                  <if test=" organ_code != null and organ_code != ''  and (organ_code == '8621' or organ_code == '8653' or organ_code == '8638')  "><![CDATA[ 
                	 and m.policy_code=null
                  ]]></if>
                  <![CDATA[ 
                
                union 
                select distinct c.customer_id,
                                 c.customer_name,
                                 c.customer_gender,
                                 c.customer_birthday,
                                 c.customer_cert_type,
                                 c.customer_certi_code
                  from APP___PAS__DBUSER.t_contract_master m
                  left join APP___PAS__DBUSER.T_INSURED_LIST l 
                  on m.policy_id = l.policy_id
                  left join APP___PAS__DBUSER.t_address ad
                  on ad.address_id = l.address_id
                  left join APP___PAS__DBUSER.t_Customer c 
                  on c.customer_id = ad.customer_id
                  where  1 = 1 ]]>
                  <!-- and  m.lapse_cause not in ('1', '6'), 36661-755-操作CC-客户基本资料变更修改重复手机号码无检验，故将此条件去掉 -->
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[ and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                      from dual)) > 1 and m.liability_state = '4' ) 
                      and ad.mobile_tel = #{mobile_tel}
                 ]]>
                 <if test=" organ_code != null and organ_code != ''  and organ_code == '8649'  "><![CDATA[ 
                	 and (abs((select ((months_between(c.customer_birthday, SYSDATE)) / 12)
                      from dual)) > 17)
                 ]]></if>
                 <!-- 29501山西分公司:客户移动电话不得与8621-北京分公司、8653-甘肃分公司、8638-山西分公司及其下辖机构的其他有效投保人（存在有效或失效保单的投保人，即有效投保人）的手机号码一致 ,添加条件将‘被保人’去掉-->
                  <if test=" organ_code != null and organ_code != ''  and (organ_code == '8621' or organ_code == '8653' or organ_code == '8638')  "><![CDATA[ 
                	 and m.policy_code=null
                  ]]></if>
                 <!-- 被保人名下有效保单  end -->
                 <!-- 受益人名下有效保单  begin -->
                 <if test=" organ_code != null and organ_code != ''  and organ_code == '8649'  ">
                 <![CDATA[ 
                union 
                select distinct c.customer_id,
                                 c.customer_name,
                                 c.customer_gender,
                                 c.customer_birthday,
                                 c.customer_cert_type,
                                 c.customer_certi_code
                  from APP___PAS__DBUSER.T_CONTRACT_MASTER m
                  left join APP___PAS__DBUSER.T_CONTRACT_BENE b
                  on b.policy_id = m.policy_id
                  left join APP___PAS__DBUSER.T_ADDRESS ad 
                  on ad.address_id = b.address_id
                  left join APP___PAS__DBUSER.T_CUSTOMER c
                  on c.customer_id = ad.customer_id 
                  where 1 = 1 
                    ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[ and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                      from dual)) > 1 and m.liability_state not in('3','4'))
                      and  ad.mobile_tel = #{mobile_tel}
                 ]]>
                 <if test=" organ_code != null and organ_code != ''  and organ_code == '8649'  "><![CDATA[ 
                	 and (abs((select ((months_between(c.customer_birthday, SYSDATE)) / 12)
                      from dual)) > 17)
                  ]]></if>
                  
                  </if>
                  <!-- 受益人名下有效保单  end -->
          
                  <![CDATA[
                ) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from APP___PAS__DBUSER.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{customer_certi_code}
                  
                   ]]>
                  <if test=" certi_code != null and certi_code != '' ">
                   union all
                   		select d.customer_id
			                 from APP___PAS__DBUSER.t_customer d
			                 where d.customer_name = #{customer_name}
			                  and d.customer_gender = #{customer_gender}
			                  and d.customer_birthday = #{customer_birthday}
			                  and d.customer_cert_type = #{customer_cert_type}
			                  and d.customer_certi_code =  #{certi_code}
                   </if> 
                      <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 
                  	 select d.customer_id
                  from APP___PAS__DBUSER.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                  <if test=" old_certi_code != null and old_certi_code != '' ">
                  	union all
                  	 
                  	 select d.customer_id
		                  from APP___PAS__DBUSER.t_customer d
		                  where d.customer_name = #{old_customer_name}
		                  and d.customer_gender = #{old_customer_gender}
		                  and d.customer_birthday = #{old_customer_birthday}
		                  and d.customer_cert_type = #{old_customer_cert_type}
		                  and d.customer_certi_code = #{old_certi_code}
                   </if> 
                  </if>
                    <![CDATA[  )]]>
     
      
	</select>
	
	
	<select id="queryFixedTelForCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ select distinct taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from ( select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                  from APP___PAS__DBUSER.T_CONTRACT_MASTER m
                  left join APP___PAS__DBUSER.T_POLICY_HOLDER ph 
                  on m.policy_id = ph.policy_id
                  left join APP___PAS__DBUSER.T_ADDRESS ad
                  on ad.address_id = ph.address_id
                  left join APP___PAS__DBUSER.T_CUSTOMER c 
                  on c.customer_id = ad.customer_id
                  where  1 = 1
                   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                      from dual)) > 1 and m.liability_state not in('3','4'))
           
                  and  ad.FIXED_TEL = #{offen_use_tel}
                
                    --被保人
                  union  
                    select distinct c.customer_id,
                                 c.customer_name,
                                 c.customer_gender,
                                 c.customer_birthday,
                                 c.customer_cert_type,
                                 c.customer_certi_code
                  from APP___PAS__DBUSER.t_contract_master m
                  left join APP___PAS__DBUSER.T_INSURED_LIST l 
                  on m.policy_id = l.policy_id
                  left join APP___PAS__DBUSER.t_address ad
                  on ad.address_id = l.address_id
                  left join APP___PAS__DBUSER.t_Customer c 
                  on c.customer_id = ad.customer_id
                  where  1 = 1 
                   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                      from dual)) > 1 and m.liability_state not in('3','4'))
                   and ad.FIXED_TEL = #{offen_use_tel}
                 
                ) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from APP___PAS__DBUSER.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{customer_certi_code} ]]>
                   
                  <if test=" certi_code != null and certi_code != '' ">
                  union all
                   select d.customer_id
	                 from APP___PAS__DBUSER.t_customer d
	                where d.customer_name = #{customer_name}
		                  and d.customer_gender = #{customer_gender}
		                  and d.customer_birthday = #{customer_birthday}
		                  and d.customer_cert_type = #{customer_cert_type}
		                  and d.customer_certi_code = #{certi_code}
                   </if> 
                      <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 select d.customer_id
                  from APP___PAS__DBUSER.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                  <if test=" old_certi_code != null and old_certi_code != '' ">
                  	union all
                    select d.customer_id
		                  from APP___PAS__DBUSER.t_customer d
		                  where d.customer_name = #{old_customer_name}
		                  and d.customer_gender = #{old_customer_gender}
		                  and d.customer_birthday = #{old_customer_birthday}
		                  and d.customer_cert_type = #{old_customer_cert_type}
		                  and d.customer_certi_code = #{old_certi_code} 
                   </if> 
                  </if>
                    <![CDATA[  )]]>
     
      
	</select>


    <select id="queryMobileOrFixedTelSH" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
                    select distinct taCus.customer_id,
                                    taCus.customer_name,
                                    taCus.customer_gender,
                                    taCus.customer_birthday,
                                    taCus.customer_cert_type,
                                    taCus.customer_certi_code
                      from (select distinct c.customer_id,
                                            c.customer_name,
                                            c.customer_gender,
                                            c.customer_birthday,
                                            c.customer_cert_type,
                                            c.customer_certi_code
                              from APP___PAS__DBUSER.T_CONTRACT_MASTER m
                              left join APP___PAS__DBUSER.T_POLICY_HOLDER ph
                                on m.policy_id = ph.policy_id
                              left join APP___PAS__DBUSER.T_ADDRESS ad
                                on ad.address_id = ph.address_id
                              left join APP___PAS__DBUSER.T_CUSTOMER c
                                on c.customer_id = ad.customer_id
                             where 1 = 1
                               and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                                          from dual)) > 1 
                               and  m.liability_state not in ('3', '4'))]]>
                       <if test="offen_use_tel != null and offen_use_tel != ''">
                               and ad.FIXED_TEL = #{offen_use_tel}
                       </if>
                       <if test="mobile_tel != null and mobile_tel != ''">
                               and ad.MOBILE_TEL = #{mobile_tel}
                       </if>
                       <![CDATA[
                            --被保人
                            union
                            select distinct c.customer_id,
                                            c.customer_name,
                                            c.customer_gender,
                                            c.customer_birthday,
                                            c.customer_cert_type,
                                            c.customer_certi_code
                              from APP___PAS__DBUSER.t_contract_master m
                              left join APP___PAS__DBUSER.T_INSURED_LIST l
                                on m.policy_id = l.policy_id
                              left join APP___PAS__DBUSER.t_address ad
                                on ad.address_id = l.address_id
                              left join APP___PAS__DBUSER.t_Customer c
                                on c.customer_id = ad.customer_id
                             where 1 = 1
                               and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                                          from dual)) > 1 
                               and   m.liability_state not in ('3', '4'))]]>
                       <if test="offen_use_tel != null and offen_use_tel != ''">
                               and ad.FIXED_TEL = #{offen_use_tel}
                       </if>
                       <if test="mobile_tel != null and mobile_tel != ''">
                               and ad.MOBILE_TEL = #{mobile_tel}
                       </if>
                       <![CDATA[
                            --第二投保人
                            union
                            select distinct c.customer_id,
                                            c.customer_name,
                                            c.customer_gender,
                                            c.customer_birthday,
                                            c.customer_cert_type,
                                            c.customer_certi_code
                              from APP___PAS__DBUSER.t_contract_master m
                              left join APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER s
                                on m.policy_id = s.policy_id
                              left join APP___PAS__DBUSER.t_address ad
                                on ad.address_id = s.address_id
                              left join APP___PAS__DBUSER.t_Customer c
                                on c.customer_id = ad.customer_id
                             where 1 = 1
                               and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
                                          from dual)) > 1 
                               and   m.liability_state not in ('3', '4'))]]>
                       <if test="offen_use_tel != null and offen_use_tel != ''">
                               and ad.FIXED_TEL = #{offen_use_tel}
                       </if>
                       <if test="mobile_tel != null and mobile_tel != ''">
                               and ad.MOBILE_TEL = #{mobile_tel}
                       </if>
            <![CDATA[
                       ) taCus
                     where taCus.Customer_Id not in
                           (select d.customer_id
                              from APP___PAS__DBUSER.t_customer d
                             where d.customer_name = #{customer_name}
                               and d.customer_gender = #{customer_gender}
                               and d.customer_birthday = #{customer_birthday}
                               and d.customer_cert_type = #{customer_cert_type}
                               and d.customer_certi_code = #{customer_certi_code}]]>
                      <if test=" certi_code != null and certi_code != '' ">
                            union all
                            select d.customer_id
                              from APP___PAS__DBUSER.t_customer d
                             where d.customer_name = #{customer_name}
                               and d.customer_gender = #{customer_gender}
                               and d.customer_birthday = #{customer_birthday}
                               and d.customer_cert_type = #{customer_cert_type}
                               and d.customer_certi_code = #{certi_code}
                      </if> 
                      <if test=" old_customer_name != null and old_customer_name != '' ">
                            union all
                            select d.customer_id
                              from APP___PAS__DBUSER.t_customer d
                             where d.customer_name = #{old_customer_name}
                               and d.customer_gender = #{old_customer_gender}
                               and d.customer_birthday = #{old_customer_birthday}
                               and d.customer_cert_type = #{old_customer_cert_type}
                               and d.customer_certi_code = #{old_customer_certi_code}
                          <if test=" old_certi_code != null and old_certi_code != '' ">
                                union all
                                select d.customer_id
                                  from APP___PAS__DBUSER.t_customer d
                                 where d.customer_name = #{old_customer_name}
                                   and d.customer_gender = #{old_customer_gender}
                                   and d.customer_birthday = #{old_customer_birthday}
                                   and d.customer_cert_type = #{old_customer_cert_type}
                                   and d.customer_certi_code = #{old_certi_code}
                         </if>
                      </if>
                    <![CDATA[  )]]>
    </select>

	<select id="findAllAgentByMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 ]]>
			<if test=" agent_status_list != null and agent_status_list != ''  ">
		        <![CDATA[ AND A.AGENT_STATUS in ]]>
			<foreach collection="agent_status_list" item="agent_status"
				index="index" open="(" close=")" separator=",">#{agent_status}</foreach>
			</if>
		
		     
			<![CDATA[AND  A.AGENT_MOBILE = #{agent_mobile}
					 AND A.AGENT_CHANNEL IN ('01','02','03')
					 AND A.AGENT_CODE NOT IN (
					 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERTI_CODE = #{certi_code}
						     AND AG.CERT_TYPE = #{cert_type}
			]]>
			 <!-- 15位身份证号码升位后 -->
			  <if test=" new_certi_code != NULL and new_certi_code != '' ">
			   union all
			          SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERTI_CODE = #{new_certi_code}
						     AND AG.CERT_TYPE = #{cert_type}
              </if> 
              	<!-- 操作CM -->
              <if test=" old_agent_name != NULL and  old_agent_name != '' ">
                  	 UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_agent_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
						<!-- 操作CM，身份证15位升位 -->
                  <if test=" old_certi_code != NULL AND old_certi_code != '' ">
                  UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
                   </if> 
               </if>
                 <![CDATA[  )]]>
	</select>
	
	
	<select id="findAllAgentByOffenUseTel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 ]]>
			<if test=" agent_status_list != null and agent_status_list != ''  ">
		        <![CDATA[ AND A.AGENT_STATUS in ]]>
			<foreach collection="agent_status_list" item="agent_status"
				index="index" open="(" close=")" separator=",">#{agent_status}</foreach>
			</if>
		
		     
			<![CDATA[AND  A.AGENT_PHONE = #{agent_phone}
					 AND A.AGENT_CHANNEL IN ('01','02','03')
					 AND A.AGENT_CODE NOT IN (
					 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERTI_CODE = #{certi_code}
						     AND AG.CERT_TYPE = #{cert_type}
			]]>
			 <!-- 15位身份证号码升位后 -->
			  <if test=" new_certi_code != NULL and new_certi_code != '' ">
			   union all
			          SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERTI_CODE = #{new_certi_code}
						     AND AG.CERT_TYPE = #{cert_type}
              </if> 
              	<!-- 操作CM -->
              <if test=" old_agent_name != NULL and  old_agent_name != '' ">
                  	 UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_agent_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
						<!-- 操作CM，身份证15位升位 -->
                  <if test=" old_certi_code != NULL AND old_certi_code != '' ">
                  UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
                   </if> 
               </if>
                 <![CDATA[  )]]>
	</select>
	
		
	
	 <!-- 回访电话1客户移动电话查询 -->
	<select id="queryMobileForCallPhone" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ select distinct taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from (SELECT c.customer_name,
               c.customer_birthday,
               c.customer_gender,
               c.customer_cert_type,
               c.customer_certi_code,
               c.customer_id
          FROM APP___PAS__DBUSER.T_CS_APPLICATION A
          INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
          ON A.CHANGE_ID = AC.CHANGE_ID
          AND AC.ACCEPT_STATUS = '18'
          AND ac.call_phone1 is not null
          AND ac.call_phone1 = #{mobile_tel}
          INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
          ON C.CUSTOMER_ID = A.CUSTOMER_ID 
         WHERE A.APPLY_TIME >= #{begin_date}
           and A.APPLY_TIME <= #{end_date}
           
           
                ) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from APP___PAS__DBUSER.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{customer_certi_code}
                  
                   ]]>
                     <!-- 身份证15位升18位后的证件号码 -->
                  <if test=" certi_code != null and certi_code != '' ">
                   union all
                  select d.customer_id
                 from APP___PAS__DBUSER.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code =  #{certi_code}
                   </if>
                     <!-- 操作CM -->
                   <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 
                  	 select d.customer_id
                  from APP___PAS__DBUSER.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                    <!-- 操作CM之前为15位身份证 -->
                  <if test=" old_certi_code != null and old_certi_code != '' ">
                   union all
                   select d.customer_id
                  from APP___PAS__DBUSER.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code =  #{old_certi_code}
                   </if> 
                  </if>
                    <![CDATA[  )]]>
     
      
	</select>
	
	
	
	
	 <!-- 回访电话1业务员重复电话冲讯重复查询 -->
	<select id="findAllAgentByCallMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 ]]>
			<if test=" agent_status_list != null and agent_status_list != ''  ">
		        <![CDATA[ AND A.AGENT_STATUS in ]]>
			<foreach collection="agent_status_list" item="agent_status"
				index="index" open="(" close=")" separator=",">#{agent_status}</foreach>
			</if>
		
		     
			<![CDATA[AND A.AGENT_MOBILE = #{agent_mobile}
			         AND A.AGENT_CHANNEL IN ('01','02','03')
					 AND A.AGENT_CODE NOT IN (
					 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERTI_CODE = #{certi_code}
						     AND AG.CERT_TYPE = #{cert_type}
			]]>
				  <!-- 身份证15位升18位后的证件号码 -->
			  <if test=" new_certi_code != NULL and new_certi_code != '' ">
			      union all
			      SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERTI_CODE = #{new_certi_code}
						     AND AG.CERT_TYPE = #{cert_type}
              </if> 
               <!-- 操作CM重要资料变更-->
              <if test=" old_agent_name != NULL and  old_agent_name != '' ">
                  	 UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_agent_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
				 <!-- 操作CM重要资料变更且变更前为15位身份证号-->
                  <if test=" old_certi_code != NULL AND old_certi_code != '' ">
                    UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
                   </if> 
               </if>
                 <![CDATA[  )]]>
	</select>
	
	
	
	
	 <!-- 回访电话2客户固定电话重复查询 -->
	<select id="queryFixedTelForCallPhone" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ select distinct taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from (SELECT c.customer_name,
                       c.customer_birthday,
                       c.customer_gender,
                       c.customer_cert_type,
                       c.customer_certi_code,
                       c.customer_id
                  FROM APP___PAS__DBUSER.T_CS_APPLICATION A
                  INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
                    ON A.CHANGE_ID = AC.CHANGE_ID
                   AND AC.ACCEPT_STATUS = '18'
                   AND ac.call_phone2 is not null
                   and ac.call_phone2 = #{offen_use_tel}
                  INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
                    ON C.CUSTOMER_ID = A.CUSTOMER_ID  
                 WHERE A.APPLY_TIME >= #{begin_date}
                   and A.APPLY_TIME <= #{end_date}
                   
                ) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from APP___PAS__DBUSER.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{customer_certi_code}
                  
                   ]]>
                    <!-- 身份证15位升18位后的证件号码 -->
                  <if test=" certi_code != null and certi_code != '' ">
                    union all
                  	select d.customer_id
		                   from APP___PAS__DBUSER.t_customer d
		                   where d.customer_name = #{customer_name}
		                         and d.customer_gender = #{customer_gender}
				                 and d.customer_birthday = #{customer_birthday}
				                 and d.customer_certi_code = #{certi_code}
				                 and d.customer_certi_code = #{customer_certi_code}
                   </if> 
                    <!-- 操作CM重要资料变更-->
                   <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 
                  	 select d.customer_id
                  from APP___PAS__DBUSER.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                   <!-- 操作CM重要资料变更且变更前为15位身份证号-->
                  <if test=" old_certi_code != null and old_certi_code != '' ">
                  union all
                  	 
                  	 select d.customer_id
                  from APP___PAS__DBUSER.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_certi_code}
                   </if> 
                  </if>
                    <![CDATA[  )]]>
     
      
	</select>
	
	
	
	 <!-- 回访电话2业务员重复电话冲讯重复查询 -->
	<select id="findAllAgentByCallPhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE,  A.AGENT_NORMAL_TYPE, A.POSTAL_ADDRESS, 
			A.CERTI_CODE, A.AGENT_GENDER, A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, 
			A.DISMISSAL_DATE, A.BIRTHDAY, A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE,
			A.AGENT_EMAIL,  A.CERT_TYPE, A.HOME_ADDRESS,  
			A.AGENT_CODE, A.EMPLOYMENT_DATE,A.AGENT_CHANNEL FROM APP___PAS__DBUSER.T_AGENT A WHERE 1 = 1 ]]>
			<if test=" agent_status_list != null and agent_status_list != ''  ">
		        <![CDATA[ AND A.AGENT_STATUS in ]]>
			<foreach collection="agent_status_list" item="agent_status"
				index="index" open="(" close=")" separator=",">#{agent_status}</foreach>
			</if>
		
		     
			<![CDATA[AND A.AGENT_PHONE = #{agent_phone}
					 AND A.AGENT_CHANNEL IN ('01','02','03')
					 AND A.AGENT_CODE NOT IN (
					 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERTI_CODE = #{certi_code}
						     AND AG.CERT_TYPE = #{cert_type}
			]]>
			
			  <!-- 身份证15位升18位后的证件号码 -->
			  <if test=" new_certi_code != NULL and new_certi_code != '' ">
			  		  UNION ALL
			  		  SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{agent_name}
                             AND AG.AGENT_GENDER = #{agent_gender}
                             AND AG.BIRTHDAY = #{birthday}
						     AND AG.CERT_TYPE = #{cert_type}
                             AND AG.CERTI_CODE = #{new_certi_code}
              </if> 
              <!--客户操作CM重要资料变更  -->
              <if test=" old_agent_name != NULL and  old_agent_name != '' ">
                  	 UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_agent_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
					<!--客户操作CM重要资料变更之前客户为15位身份证号  -->
                  <if test=" old_certi_code != NULL AND old_certi_code != '' ">
                   UNION ALL
                  	 SELECT AG.AGENT_CODE FROM APP___PAS__DBUSER.T_AGENT AG WHERE AG.AGENT_NAME = #{old_agent_name}
                              AND AG.AGENT_GENDER = #{old_agent_gender}
                             AND AG.BIRTHDAY = #{old_agent_birthday}
						     AND AG.CERTI_CODE = #{old_certi_code}
						     AND AG.CERT_TYPE = #{old_agent_cert_type}
                     
                   </if> 
               </if>
                 <![CDATA[  )]]>
	</select>
	
	
	
</mapper>
