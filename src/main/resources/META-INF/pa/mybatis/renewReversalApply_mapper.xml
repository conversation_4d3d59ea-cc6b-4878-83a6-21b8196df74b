<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.util.test">

	<sql id="renewReversalApplyWhereCondition">
		<if test=" reversal_mode != null and reversal_mode != ''  "><![CDATA[ AND A.REVERSAL_MODE = #{reversal_mode} ]]></if>
		<if test=" review_state != null and review_state != ''  "><![CDATA[ AND A.REVIEW_STATE = #{review_state} ]]></if>
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND <PERSON>.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" check_enter_time  != null  and  check_enter_time  != ''  "><![CDATA[ AND A.CHECK_ENTER_TIME = #{check_enter_time} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" apply_by  != null "><![CDATA[ AND A.APPLY_BY = #{apply_by} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" payee_id  != null "><![CDATA[ AND A.PAYEE_ID = #{payee_id} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" fee_item != null and fee_item != ''  "><![CDATA[ AND A.FEE_ITEM = #{fee_item} ]]></if>
		<if test=" review_by  != null "><![CDATA[ AND A.REVIEW_BY = #{review_by} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" apply_organ != null and apply_organ != ''  "><![CDATA[ AND A.APPLY_ORGAN = #{apply_organ} ]]></if>
		<if test=" review_result_desc != null and review_result_desc != ''  "><![CDATA[ AND A.REVIEW_RESULT_DESC = #{review_result_desc} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" arap_time  != null  and  arap_time  != ''  "><![CDATA[ AND A.ARAP_TIME = #{arap_time} ]]></if>
		<if test=" review_organ != null and review_organ != ''  "><![CDATA[ AND A.REVIEW_ORGAN = #{review_organ} ]]></if>
		<if test=" reversal_bank_code != null and reversal_bank_code != ''  "><![CDATA[ AND A.REVERSAL_BANK_CODE = #{reversal_bank_code} ]]></if>
		<if test=" reversal_bank_account != null and reversal_bank_account != ''  "><![CDATA[ AND A.REVERSAL_BANK_ACCOUNT = #{reversal_bank_account} ]]></if>
		<if test=" arap_fee_id  != null "><![CDATA[ AND A.ARAP_FEE_ID = #{arap_fee_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryRenewReversalApplyByApplyIdCondition">
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
	</sql>
	
	<sql id="queryRenewReversalApplyByArapFeeIdCondition">
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" arap_fee_id  != null "><![CDATA[ AND A.ARAP_FEE_ID = #{arap_fee_id} ]]></if>
	</sql>	
	

<!-- 添加操作 -->
	<insert id="addRenewReversalApply"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="apply_id">
			SELECT APP___PAS__DBUSER.S_RENEW_REVERSAL_APPLY__APPLY.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY(
				REVERSAL_MODE, REVIEW_STATE, PAYEE_NAME, HOLDER_NAME, BANK_ACCOUNT, CHECK_ENTER_TIME, UNIT_NUMBER, 
				APPLY_BY, INSERT_TIMESTAMP, DUE_TIME, UPDATE_BY, FEE_AMOUNT, POLICY_ID, APPLY_ID, 
				PAYEE_ID, INSERT_TIME, INSURED_NAME, UPDATE_TIME, FEE_ITEM, REVIEW_BY, PAY_MODE, 
				APPLY_ORGAN, REVIEW_RESULT_DESC, BANK_CODE, ARAP_TIME, UPDATE_TIMESTAMP, REVIEW_ORGAN, REVERSAL_BANK_CODE, 
				REVERSAL_BANK_ACCOUNT, INSERT_BY, ARAP_FEE_ID ) 
			VALUES (
				#{reversal_mode, jdbcType=VARCHAR}, #{review_state, jdbcType=VARCHAR} , #{payee_name, jdbcType=VARCHAR} , #{holder_name, jdbcType=VARCHAR} , #{bank_account, jdbcType=VARCHAR} , #{check_enter_time, jdbcType=DATE} , #{unit_number, jdbcType=VARCHAR} 
				, #{apply_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{due_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{apply_id, jdbcType=NUMERIC} 
				, #{payee_id, jdbcType=NUMERIC} , SYSDATE , #{insured_name, jdbcType=VARCHAR} , SYSDATE , #{fee_item, jdbcType=VARCHAR} , #{review_by, jdbcType=NUMERIC} , #{pay_mode, jdbcType=VARCHAR} 
				, #{apply_organ, jdbcType=VARCHAR} , #{review_result_desc, jdbcType=VARCHAR} , #{bank_code, jdbcType=VARCHAR} , #{arap_time, jdbcType=DATE} , CURRENT_TIMESTAMP, #{review_organ, jdbcType=VARCHAR} , #{reversal_bank_code, jdbcType=VARCHAR} 
				, #{reversal_bank_account, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{arap_fee_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRenewReversalApply" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE A.APPLY_ID=#{apply_id} ]]>
		<include refid="renewReversalApplyWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="updateRenewReversalApply" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY ]]>
		<set>
		<trim suffixOverrides=",">
			REVERSAL_MODE = #{reversal_mode, jdbcType=VARCHAR} ,
			REVIEW_STATE = #{review_state, jdbcType=VARCHAR} ,
			PAYEE_NAME = #{payee_name, jdbcType=VARCHAR} ,
			HOLDER_NAME = #{holder_name, jdbcType=VARCHAR} ,
			BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
		    CHECK_ENTER_TIME = #{check_enter_time, jdbcType=DATE} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
		    APPLY_BY = #{apply_by, jdbcType=NUMERIC} ,
		    DUE_TIME = #{due_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PAYEE_ID = #{payee_id, jdbcType=NUMERIC} ,
			INSURED_NAME = #{insured_name, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			FEE_ITEM = #{fee_item, jdbcType=VARCHAR} ,
		    REVIEW_BY = #{review_by, jdbcType=NUMERIC} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
			APPLY_ORGAN = #{apply_organ, jdbcType=VARCHAR} ,
			REVIEW_RESULT_DESC = #{review_result_desc, jdbcType=VARCHAR} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    ARAP_TIME = #{arap_time, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			REVIEW_ORGAN = #{review_organ, jdbcType=VARCHAR} ,
			REVERSAL_BANK_CODE = #{reversal_bank_code, jdbcType=VARCHAR} ,
			REVERSAL_BANK_ACCOUNT = #{reversal_bank_account, jdbcType=VARCHAR} ,
		    ARAP_FEE_ID = #{arap_fee_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_ID = #{apply_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findRenewReversalApplyByApplyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REVERSAL_MODE, A.REVIEW_STATE, A.PAYEE_NAME, A.HOLDER_NAME, A.BANK_ACCOUNT, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, 
			A.APPLY_BY, A.DUE_TIME, A.FEE_AMOUNT, A.POLICY_ID, A.APPLY_ID, 
			A.PAYEE_ID, A.INSURED_NAME, A.FEE_ITEM, A.REVIEW_BY, A.PAY_MODE, 
			A.APPLY_ORGAN, A.REVIEW_RESULT_DESC, A.BANK_CODE, A.ARAP_TIME, A.REVIEW_ORGAN, A.REVERSAL_BANK_CODE, 
			A.REVERSAL_BANK_ACCOUNT, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE 1 = 1  ]]>
		<include refid="queryRenewReversalApplyByApplyIdCondition" />
	</select>
	
	<select id="findRenewReversalApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REVERSAL_MODE, A.REVIEW_STATE, A.PAYEE_NAME, A.HOLDER_NAME, A.BANK_ACCOUNT, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, 
			A.APPLY_BY, A.DUE_TIME, A.FEE_AMOUNT, A.POLICY_ID, A.APPLY_ID, 
			A.PAYEE_ID, A.INSURED_NAME, A.FEE_ITEM, A.REVIEW_BY, A.PAY_MODE, 
			A.APPLY_ORGAN, A.REVIEW_RESULT_DESC, A.BANK_CODE, A.ARAP_TIME, A.REVIEW_ORGAN, A.REVERSAL_BANK_CODE, 
			A.REVERSAL_BANK_ACCOUNT, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE 1 = 1  ]]>
		<include refid="queryRenewReversalApplyByArapFeeIdCondition" />
		<![CDATA[ORDER BY A.DUE_TIME DESC]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapRenewReversalApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REVERSAL_MODE, A.REVIEW_STATE, A.PAYEE_NAME, A.HOLDER_NAME, A.BANK_ACCOUNT, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, 
			A.APPLY_BY, A.DUE_TIME, A.FEE_AMOUNT, A.POLICY_ID, A.APPLY_ID, 
			A.PAYEE_ID, A.INSURED_NAME, A.FEE_ITEM, A.REVIEW_BY, A.PAY_MODE, 
			A.APPLY_ORGAN, A.REVIEW_RESULT_DESC, A.BANK_CODE, A.ARAP_TIME, A.REVIEW_ORGAN, A.REVERSAL_BANK_CODE, 
			A.REVERSAL_BANK_ACCOUNT, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRenewReversalApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REVERSAL_MODE, A.REVIEW_STATE, A.PAYEE_NAME, A.HOLDER_NAME, A.BANK_ACCOUNT, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, 
			A.APPLY_BY, A.DUE_TIME, A.FEE_AMOUNT, A.POLICY_ID, A.APPLY_ID, 
			A.PAYEE_ID, A.INSURED_NAME, A.FEE_ITEM, A.REVIEW_BY, A.PAY_MODE, 
			A.APPLY_ORGAN, A.REVIEW_RESULT_DESC, A.BANK_CODE, A.ARAP_TIME, A.REVIEW_ORGAN, A.REVERSAL_BANK_CODE, 
			A.REVERSAL_BANK_ACCOUNT, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE ROWNUM <=  1000 AND A.REVIEW_STATE IS NULL  ]]>
		 <include refid="renewReversalApplyWhereCondition" /> 
	</select>
	
	<select id="findAllRenewReversalApplyByReviewState" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REVERSAL_MODE, A.REVIEW_STATE, A.PAYEE_NAME, A.HOLDER_NAME, A.BANK_ACCOUNT, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, 
			A.APPLY_BY, A.DUE_TIME, A.FEE_AMOUNT, A.POLICY_ID, A.APPLY_ID, 
			A.PAYEE_ID, A.INSURED_NAME, A.FEE_ITEM, A.REVIEW_BY, A.PAY_MODE, 
			A.APPLY_ORGAN, A.REVIEW_RESULT_DESC, A.BANK_CODE, A.ARAP_TIME, A.REVIEW_ORGAN, A.REVERSAL_BANK_CODE, 
			A.REVERSAL_BANK_ACCOUNT, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE ROWNUM <=  1000 AND A.REVIEW_STATE IS NULL  ]]>
<!-- 		 <include refid="renewReversalApplyWhereCondition" />  -->
			<![CDATA[ORDER BY A.DUE_TIME DESC]]>
	</select>

<!-- 查询个数操作 -->
	<select id="findRenewReversalApplyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryRenewReversalApplyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.REVERSAL_MODE, B.REVIEW_STATE, B.PAYEE_NAME, B.HOLDER_NAME, B.BANK_ACCOUNT, B.CHECK_ENTER_TIME, B.UNIT_NUMBER, 
			B.APPLY_BY, B.DUE_TIME, B.FEE_AMOUNT, B.POLICY_ID, B.APPLY_ID, 
			B.PAYEE_ID, B.INSURED_NAME, B.FEE_ITEM, B.REVIEW_BY, B.PAY_MODE, 
			B.APPLY_ORGAN, B.REVIEW_RESULT_DESC, B.BANK_CODE, B.ARAP_TIME, B.REVIEW_ORGAN, B.REVERSAL_BANK_CODE, 
			B.REVERSAL_BANK_ACCOUNT, B.ARAP_FEE_ID FROM (
					SELECT ROWNUM RN, A.REVERSAL_MODE, A.REVIEW_STATE, A.PAYEE_NAME, A.HOLDER_NAME, A.BANK_ACCOUNT, A.CHECK_ENTER_TIME, A.UNIT_NUMBER, 
			A.APPLY_BY, A.DUE_TIME, A.FEE_AMOUNT, A.POLICY_ID, A.APPLY_ID, 
			A.PAYEE_ID, A.INSURED_NAME, A.FEE_ITEM, A.REVIEW_BY, A.PAY_MODE, 
			A.APPLY_ORGAN, A.REVIEW_RESULT_DESC, A.BANK_CODE, A.ARAP_TIME, A.REVIEW_ORGAN, A.REVERSAL_BANK_CODE, 
			A.REVERSAL_BANK_ACCOUNT, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询没有进行回退的费用数据个数 -->
	<select id="findNotReversalPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select * from (select *
  from (select *
          from APP___PAS__DBUSER.T_prem_arap tpa
         where policy_code = #{policy_code}
           and tpa.due_time > #{due_time}  ]]>
     <if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND tpa.UNIT_NUMBER = #{unit_number} ]]></if>
     <if test=" list_id  != null "><![CDATA[ AND tpa.LIST_ID = #{list_id} ]]></if>
           <![CDATA[) a
  left join APP___PAS__DBUSER.T_RENEW_REVERSAL_APPLY trra
    on a.list_id = trra.arap_fee_id )a where a.REVIEW_STATE is null or a.REVIEW_STATE <> #{REVIEW_STATE}  ]]>
	</select>
</mapper>
