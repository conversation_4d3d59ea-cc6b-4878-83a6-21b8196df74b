<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRiskAmountQueueDao">

	<sql id="PA_riskAmountQueueWhereCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[AND B.ORGAN_CODE IN (
		SELECT T.ORGAN_CODE
			  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
			 START WITH T.ORGAN_CODE = #{organ_code}
			CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE)  ]]></if>	
	</sql>
	
	<sql id="PA_riskAmountQueueCondition">
		<if test=" customer_id  != null "><![CDATA[ AND CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND ITEM_ID = #{item_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND POLICY_ID = #{policy_id} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND INTERNAL_CODE = #{internal_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryRiskAmountQueueByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addRiskAmountQueue"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE(
				INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, ITEM_ID, START_TIME, STATUS, BUSI_PROD_CODE, 
				APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, END_TIME, 
				BUSI_ITEM_ID, INSERT_BY, POLICY_ID, INTERNAL_CODE ) 
			VALUES (
				SYSDATE, #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{start_time, jdbcType=DATE} , #{status, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{end_time, jdbcType=DATE} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{internal_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteRiskAmountQueue" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateRiskAmountQueue" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    START_TIME = #{start_time, jdbcType=DATE} ,
		    STATUS = #{status, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			INTERNAL_CODE = #{internal_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	<!-- 修改操作 -->
	<update id="updateRiskAmountQueueByPolicyCodeItemId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE ]]>
		<set>
		<trim suffixOverrides=",">
		    STATUS = #{status, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="PA_riskAmountQueueCondition" />
	</update>
	
	<update id="updateRiskAmountQueueByPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE ]]>
		<set>
		<trim suffixOverrides=",">
		    STATUS = #{status, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CODE = #{policy_code}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findRiskAmountQueueByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.START_TIME, A.STATUS, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.INTERNAL_CODE FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE A WHERE 1 = 1  ]]>
		<include refid="PA_queryRiskAmountQueueByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findRiskAmountQueueByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.START_TIME, A.STATUS, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.INTERNAL_CODE FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE A WHERE 1 = 1  ]]>
		<include refid="PA_riskAmountQueueWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapRiskAmountQueue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.START_TIME, A.STATUS, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.INTERNAL_CODE FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_riskAmountQueueCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllRiskAmountQueue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN,
			   A.CUSTOMER_ID,
               A.ITEM_ID,
               A.START_TIME,
               A.STATUS,
               A.BUSI_PROD_CODE,
               A.APPLY_CODE,
               A.POLICY_CODE,
               A.LIST_ID,
               A.END_TIME,
               A.BUSI_ITEM_ID,
               A.POLICY_ID,
               A.INTERNAL_CODE
          FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE A 
          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
		    ON A.POLICY_CODE = B.POLICY_CODE where 1=1  ]]>
          <include refid="PA_riskAmountQueueWhereCondition" />
          <![CDATA[ AND MOD(A.ITEM_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}]]>
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findRiskAmountQueueTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) as END_NUM FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE A WHERE 1 = 1  ]]>
		<include refid="PA_riskAmountQueueWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryRiskAmountQueueForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_ID, B.ITEM_ID, B.START_TIME, B.STATUS, B.BUSI_PROD_CODE, 
			B.APPLY_CODE, B.POLICY_CODE, B.LIST_ID, B.END_TIME, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.INTERNAL_CODE FROM (
					SELECT ROWNUM RN, A.CUSTOMER_ID, A.ITEM_ID, A.START_TIME, A.STATUS, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.INTERNAL_CODE FROM APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_riskAmountQueueWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
