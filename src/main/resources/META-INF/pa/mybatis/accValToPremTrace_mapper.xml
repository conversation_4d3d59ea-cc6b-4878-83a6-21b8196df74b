<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.AccValToPremTraceDaoImpl">
	<sql id="PA_accValToPremTraceWhereCondition">
		<if test=" list_id != null and list_id != ''  "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" prem_policy_code  != null and prem_policy_code != '' "><![CDATA[ AND A.PREM_POLICY_CODE = #{prem_policy_code} ]]></if>
		<if test=" prem_policy_id != null "><![CDATA[ AND A.PREM_POLICY_ID = #{prem_policy_id} ]]></if>
		<if test=" prem_busi_prod_code  != null and prem_busi_prod_code != '' "><![CDATA[ AND A.PREM_BUSI_PROD_CODE = #{prem_busi_prod_code} ]]></if>
		<if test=" prem_busi_item_id != null "><![CDATA[ AND A.PREM_BUSI_ITEM_ID = #{prem_busi_item_id} ]]></if>
		<if test=" prem_unit_number  != null and prem_unit_number != '' "><![CDATA[ AND A.PREM_UNIT_NUMBER = #{prem_unit_number} ]]></if>
		<if test=" universal_policy_code  != null and universal_policy_code != '' "><![CDATA[ AND A.UNIVERSAL_POLICY_CODE = #{universal_policy_code} ]]></if>
		<if test=" universal_policy_id != null "><![CDATA[ AND A.UNIVERSAL_POLICY_ID = #{universal_policy_id} ]]></if>
		<if test=" universal_busi_prod_code  != null and universal_busi_prod_code != '' "><![CDATA[ AND A.UNIVERSAL_BUSI_PROD_CODE = #{universal_busi_prod_code} ]]></if>
		<if test=" universal_busi_item_id != null and universal_busi_item_id != ''  "><![CDATA[ AND A.UNIVERSAL_BUSI_ITEM_ID = #{universal_busi_item_id} ]]></if>
		<if test=" fund_trans_id != null "><![CDATA[ AND A.FUND_TRANS_ID = #{fund_trans_id} ]]></if>
		<if test=" batch_date != null "><![CDATA[ AND A.BATCH_DATE = #{batch_date} ]]></if>
		<if test=" batch_status  != null and batch_status != '' "><![CDATA[ AND A.BATCH_STATUS = #{batch_status} ]]></if>
		<if test=" faild_reason  != null and faild_reason != '' "><![CDATA[ AND A.FAILD_REASON = #{faild_reason} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="PA_addAccValToPremTrace" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ACC_VAL_PREM_TR__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE(
	   LIST_ID,PREM_POLICY_CODE,PREM_POLICY_ID,PREM_BUSI_PROD_CODE,
	   PREM_BUSI_ITEM_ID,PREM_UNIT_NUMBER,UNIVERSAL_POLICY_CODE,
       UNIVERSAL_POLICY_ID,UNIVERSAL_BUSI_PROD_CODE,UNIVERSAL_BUSI_ITEM_ID,
       FUND_TRANS_ID,BATCH_DATE,BATCH_STATUS,FAILD_REASON
       ,INSERT_BY,INSERT_TIME,INSERT_TIMESTAMP,UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP)
			VALUES (
		#{list_id, jdbcType=NUMERIC} ,#{prem_policy_code, jdbcType=VARCHAR} ,#{prem_policy_id, jdbcType=NUMERIC} ,#{prem_busi_prod_code, jdbcType=VARCHAR}
		,#{prem_busi_item_id, jdbcType=NUMERIC} ,#{prem_unit_number, jdbcType=VARCHAR} ,#{universal_policy_code, jdbcType=VARCHAR}
		,#{universal_policy_id, jdbcType=NUMERIC} ,#{universal_busi_prod_code, jdbcType=VARCHAR} ,#{universal_busi_item_id, jdbcType=NUMERIC}
		,#{fund_trans_id, jdbcType=NUMERIC} ,#{batch_date, jdbcType=DATE} ,#{batch_status, jdbcType=VARCHAR} ,#{faild_reason, jdbcType=VARCHAR}
		,#{insert_by, jdbcType=NUMERIC} ,SYSDATE ,CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} ,SYSDATE ,CURRENT_TIMESTAMP)  
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="PA_deleteAccValToPremTrace" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE WHERE LIST_ID=#{list_id} ]]>
	</delete>

	<!-- 按索引查询操作 -->
	<select id="PA_findAccValToPremTraceByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,A.PREM_POLICY_CODE,A.PREM_POLICY_ID,A.PREM_BUSI_PROD_CODE,A.PREM_BUSI_ITEM_ID,
       A.PREM_UNIT_NUMBER,A.UNIVERSAL_POLICY_CODE,A.UNIVERSAL_POLICY_ID, A.UNIVERSAL_BUSI_PROD_CODE,A.UNIVERSAL_BUSI_ITEM_ID,
       A.FUND_TRANS_ID,A.BATCH_DATE,A.BATCH_STATUS,A.FAILD_REASON FROM APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE A WHERE 1=1]]>
	   <include refid="PA_accValToPremTraceWhereCondition" /> 
	   <![CDATA[ORDER BY A.LIST_ID DESC]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAccValToPremTrace" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   SELECT A.LIST_ID,A.PREM_POLICY_CODE,A.PREM_POLICY_ID,A.PREM_BUSI_PROD_CODE,A.PREM_BUSI_ITEM_ID,
       A.PREM_UNIT_NUMBER,A.UNIVERSAL_POLICY_CODE,A.UNIVERSAL_POLICY_ID, A.UNIVERSAL_BUSI_PROD_CODE,A.UNIVERSAL_BUSI_ITEM_ID,
       A.FUND_TRANS_ID,A.BATCH_DATE,A.BATCH_STATUS,A.FAILD_REASON FROM APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE A WHERE ROWNUM <=  1000   ]]>
		<include refid="PA_accValToPremTraceWhereCondition" />
		<![CDATA[ORDER BY A.LIST_ID DESC]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="PA_findAccValToPremTraceTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE A WHERE 1 = 1  ]]>
		<include refid="PA_accValToPremTraceWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="PA_queryAccValToPremTraceForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT B.RN AS rowNumber, B.LIST_ID,B.PREM_POLICY_CODE,B.PREM_POLICY_ID,B.PREM_BUSI_PROD_CODE,B.PREM_BUSI_ITEM_ID,
       B.PREM_UNIT_NUMBER,B.UNIVERSAL_POLICY_CODE,B.UNIVERSAL_POLICY_ID, B.UNIVERSAL_BUSI_PROD_CODE,B.UNIVERSAL_BUSI_ITEM_ID,
       B.FUND_TRANS_ID,B.BATCH_DATE,B.BATCH_STATUS,B.FAILD_REASON FROM FROM (
					SELECT ROWNUM RN, A.LIST_ID,A.PREM_POLICY_CODE,A.PREM_POLICY_ID,A.PREM_BUSI_PROD_CODE,A.PREM_BUSI_ITEM_ID,
       A.PREM_UNIT_NUMBER,A.UNIVERSAL_POLICY_CODE,A.UNIVERSAL_POLICY_ID, A.UNIVERSAL_BUSI_PROD_CODE,A.UNIVERSAL_BUSI_ITEM_ID,
       A.FUND_TRANS_ID,A.BATCH_DATE,A.BATCH_STATUS,A.FAILD_REASON FROM APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE A WHERE ROWNUM <= #{LESS_NUM}  ]]>
		<include refid="PA_accValToPremTraceWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
		<!-- 查询所有操作 -->
	<select id="PA_getUseUniverInsurPrems" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   SELECT A.LIST_ID,
	   A.FUND_TRANS_ID,
       A.PREM_POLICY_CODE,
       A.PREM_POLICY_ID,
       A.PREM_BUSI_PROD_CODE,
       A.PREM_BUSI_ITEM_ID,
       A.PREM_UNIT_NUMBER,
       A.UNIVERSAL_POLICY_CODE,
       A.UNIVERSAL_POLICY_ID,
       A.UNIVERSAL_BUSI_PROD_CODE,
       A.UNIVERSAL_BUSI_ITEM_ID,
       A.BATCH_DATE
  FROM APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE A  WHERE 1=1 ]]>
  <if test=" back_flag !=null and back_flag !='' and back_flag =='1'.toString() "><![CDATA[ AND A.BATCH_STATUS = '1' ]]></if>
  <if test=" back_flag !=null and back_flag !='' and back_flag =='0'.toString() "><![CDATA[ AND A.BATCH_STATUS = '3' ]]></if>
		<include refid="PA_accValToPremTraceWhereCondition" /> 
		<![CDATA[ORDER BY A.LIST_ID DESC]]>
	</select>
	
	
		<!-- 查询个数操作 -->
	<select id="PA_queryUniversalAccountChange" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ 
		SELECT COUNT(1)
  FROM (SELECT T.LIST_ID
          FROM DEV_PAS.T_FUND_TRANS T WHERE 1=1 ]]>
		<if test=" fund_trans_id != null "><![CDATA[ AND T.LIST_ID = #{fund_trans_id} ]]></if>
          	<![CDATA[  AND T.TRANS_ID >
               (SELECT T.TRANS_ID
                  FROM DEV_PAS.T_FUND_TRANS T
                 WHERE 1=1  ]]>
                 <if test=" fund_trans_id != null "><![CDATA[ AND T.LIST_ID = #{fund_trans_id} ]]></if>
                 <if test=" prem_unit_number  != null and prem_unit_number != '' "><![CDATA[ AND T.UNIT_NUMBER = #{prem_unit_number} ]]></if>
            <![CDATA[        )
        UNION
        SELECT T.INVEST_ID INVEST_ID
          FROM DEV_PAS.T_FUND_SETTLEMENT T WHERE 1=1  ]]>
          <if test=" fund_trans_id != null "><![CDATA[ AND T.INVEST_ID = #{fund_trans_id} ]]></if>
          <![CDATA[    AND T.SETTLE_DATE >=
               (SELECT T.DEAL_TIME
                  FROM DEV_PAS.T_FUND_TRANS T
                 WHERE 1=1  ]]>
                 <if test=" fund_trans_id != null "><![CDATA[ AND T.LIST_ID = #{fund_trans_id} ]]></if>
                 <if test=" prem_unit_number  != null and prem_unit_number != '' "><![CDATA[ AND T.UNIT_NUMBER = #{prem_unit_number} ]]></if>
                <![CDATA[    ))
		  ]]>
	</select>
	
	
	<!-- 修改操作 -->
	<update id="PA_updateAccValToPremTrace" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ACC_VAL_TO_PREM_TRACE ]]>
    <set>
    <trim suffixOverrides=",">
      UPDATE_TIME = SYSDATE , 
      UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
      UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
      BATCH_STATUS = '3',
      BACK_ACCEPT_CODE = #{back_accept_code, jdbcType=VARCHAR} ,
    </trim>
    </set>
    <![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	
		<!-- 保全回退删除操作 -->	
	<delete id="PA_deleteFundTransForBack" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_TRANS T WHERE T.LIST_ID = #{list_id, jdbcType=NUMERIC} AND T.UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} AND T.BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} AND T.TRANS_CODE = '57'  ]]>
	</delete>
	
	<!-- 按索引查询操作 -->	
	<select id="PA_findFundTransForBack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A  
			WHERE ROWNUM = 1 AND A.LIST_ID = #{list_id, jdbcType=NUMERIC} AND A.UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} AND A.BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} AND A.TRANS_CODE = '57'   ]]>
	</select>
	
	
<!-- 修改操作 -->
	<update id="PA_updateContractInvestForBack" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST ]]>
		<set>
		<trim suffixOverrides=",">
		    INTEREST_CAPITAL = INTEREST_CAPITAL+#{interest_capital, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

</mapper>
