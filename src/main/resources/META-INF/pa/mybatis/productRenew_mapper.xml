<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IProductRenewDao">

<sql id="PA_queryCustomerByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
<!--保单号是否存在  -->
<select id="PA_contractmaster_cjk" resultType="java.util.Map" parameterType="java.util.Map">

select * from APP___PAS__DBUSER.t_contract_master A where A.POLICY_CODE=#{policy_code}
</select>
	
	
<!-- 保单号与险种是否匹配 -->	

<select id="PA_contractbusiprod_cjk" resultType="java.util.Map" parameterType="java.util.Map">

select * from T_CONTRACT_BUSI_PROD A WHERE 
A.POLICY_CODE =#{policy_code}
   and A.BUSI_PROD_CODE =#{busi_prod_code}

 and  A.BUSI_PRD_ID IN

  (select BUSINESS_PRD_ID  from t_business_product A WHERE A.PRODUCT_NAME_SYS LIKE '%${i}%')
</select>

<!-- 产品是否可续保 -->

<select id="queryPolicyRenew1_cjk" resultType="java.util.Map" parameterType="java.util.Map">

select distinct tcm.policy_id from
        APP___PAS__DBUSER.t_contract_master tcm left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on 
        tcm.policy_id = tcbp.policy_id left join APP___PAS__DBUSER.t_contract_product tcp on tcbp.busi_item_id = tcp.busi_item_id 
        left join APP___PAS__DBUSER.t_business_product tbp on tcbp.busi_prd_id = tbp.business_prd_id 
        where tcm.liability_state=1 and tcbp.renewal_state !=1
        and tcbp.busi_prod_code not in(********) 
        and tcbp.liability_state=1 
        and tbp.renew_option!=0 and 
                tcbp.renew!=0      
          and (tcbp.renew_decision !=1 or tcbp.renew_decision is null)
       
         and tcbp.POLICY_CODE =#{policy_code}
   </select>
   <!-- 查询被保人 -->
   <select id="queryBusiInsuredInfo_cjk" resultType="java.util.Map" parameterType="java.util.Map">
   
   select til.customer_id,til.list_id from APP___PAS__DBUSER.t_insured_list til left join APP___PAS__DBUSER.t_benefit_insured tbi 
			on til.list_id = tbi.insured_id where tbi.busi_item_id=#{busi_item_id} 
   </select>
   <!-- 查询客户 -->
   <select id="PA_findCustomerByCustomerId_cjk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.IS_SUBSCRIPTION_EMAIL FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="PA_queryCustomerByCustomerIdCondition" />
	</select>
	<!-- 效力状态终止 -->
	<select id="queryPolicyRenew2_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	select distinct tcm.policy_id from 
        APP___PAS__DBUSER.t_contract_master tcm left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on 
        tcm.policy_id = tcbp.policy_id 
        left join APP___PAS__DBUSER.t_contract_product tcp on tcbp.busi_item_id = tcp.busi_item_id 
        left join APP___PAS__DBUSER.t_business_product tbp on tcbp.busi_prd_id = tbp.business_prd_id 
        left join APP___PAS__DBUSER.t_pay_due tpdu on tcp.item_id = tpdu.item_id 
      where tcm.liability_state=3 and tcm.end_cause=01  
        and tbp.renew_option!=0 
        and tcbp.renew_times=0
        and (tcm.policy_id in (select A.policy_id from (select tpa.policy_id,
                sum(tpa.CAPITAL_BALANCE) over(partition by tpa.policy_id) CAPITAL_BALANCE
                from APP___PAS__DBUSER.t_policy_account tpa where tpa.account_type in ('4', '5')) A
                where A.CAPITAL_BALANCE = '0' ) or tcm.policy_id not in (select tpa.policy_id from t_policy_account tpa where tpa.account_type in ('4', '5')))
        and  tpdu.fee_status=00 
        and tcbp.busi_prod_code in (********)
        
        and tcm.policy_code=#{policy_code}
	
	</select>
</mapper>