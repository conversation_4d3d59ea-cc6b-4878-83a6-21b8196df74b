<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFundPriceNoticeMailDao">
<!--
	<sql id="fundPriceNoticeMailWhereCondition">
		<if test=" name != null and name != ''  "><![CDATA[ AND A.NAME = #{name} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" email != null and email != ''  "><![CDATA[ AND A.EMAIL = #{email} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryFundPriceNoticeMailByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addFundPriceNoticeMail"  useGeneratedKeys="true"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_ACCOUNT__ACCOUNT_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL(
				INSERT_TIMESTAMP, NAME, UPDATE_BY, INSERT_TIME, LIST_ID, EMAIL, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{name, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , #{email, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteFundPriceNoticeMail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateFundPriceNoticeMail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL ]]>
		<set>
		<trim suffixOverrides=",">
			NAME = #{name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			EMAIL = #{email, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findFundPriceNoticeMailByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL A WHERE 1 = 1  ]]>
		<include refid="queryFundPriceNoticeMailByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapFundPriceNoticeMail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllFundPriceNoticeMail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findFundPriceNoticeMailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryFundPriceNoticeMailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NAME, B.LIST_ID, B.EMAIL FROM (
					SELECT ROWNUM RN, A.NAME, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_FUND_PRICE_NOTICE_MAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
