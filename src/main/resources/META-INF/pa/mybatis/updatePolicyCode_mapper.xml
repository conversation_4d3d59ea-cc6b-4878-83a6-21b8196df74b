<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="updatePolicyCode">

	<update id="updateContractMasterPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractBusiProdPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractProductPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePolicyHolderPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_HOLDER ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[  WHERE  POLICY_ID = #{policy_id}]]>
	</update>
	
	<update id="updateInsuredListPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INSURED_LIST ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id}]]>
	</update>
	
	<update id="updateBenefitInsuredPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BENEFIT_INSURED ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractBenePolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BENE ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractAgentPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_AGENT ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePayerPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePolicyConditionPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CONDITION ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id}  ]]>
	</update>
	
	<update id="updatePayPlanPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePayDuePolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id}  ]]>
	</update>
	
	
	<select id="findContractMasterPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT  A.POLICY_ID,  A.POLICY_CODE  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1=1  ]]>
		<include refid="queryContractMasterByPolicyCodeCondition" />
	</select>
	<sql id="queryContractMasterByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	

</mapper>