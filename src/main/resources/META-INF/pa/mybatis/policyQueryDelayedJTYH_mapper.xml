<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyQueryDelayedJTYHDao">

	<!-- 查询承保有效的保单  -->
	<select id="PA_findDelayedPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT '记录序号' cc,
       '校验结果（体记录）' dd,
       '校验说明（体记录）' ee,
       (select tbbm.zone_no
          from dev_pas.t_bank_branch_mapping tbbm
         where tcm.service_bank_branch = tbbm.bank_branch_code
           and rownum = 1) SERVICE_BANK, 
       (select tbbm.bank_node
          from dev_pas.t_bank_branch_mapping tbbm
         where tcm.service_bank_branch = tbbm.bank_branch_code
           and rownum = 1) SERVICE_BANK_BRANCH, 
       TCM.SERVICE_HANDLER_CODE, 
       TCM.ISSUE_DATE DUE_TIME,
       (SELECT MAX(TPA1.UNIT_NUMBER)
          FROM DEV_PAS.T_PREM_ARAP TPA1
         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER, 
       (SELECT MAX(TPA1.UPDATE_TIME)
          FROM DEV_PAS.T_PREM_ARAP TPA1
         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
       (SELECT MAX(TPA1.UPDATE_TIME)
          FROM DEV_PAS.T_PREM_ARAP TPA1
         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
       TC.CUSTOMER_CERT_TYPE, 
       TC.CUSTOMER_CERTI_CODE, 
       TC.COUNTRY_CODE, 
       TA.STATE,
       TA.CITY, 
       TA.DISTRICT, 
       TA.ADDRESS, 
       TC.HOUSE_TEL, 
       TC.HOUSE_TEL, 
       TC.MOBILE_TEL, 
       '' fenjh, 
       '1' IS_RIDER, 
       TCBP.BUSI_PROD_CODE, 
       TBP.PRODUCT_NAME_STD, 
       CASE
         WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
          '001' 
         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
              TBP.PRODUCT_CATEGORY2 = '30003' THEN
          '002' 
         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
              TBP.PRODUCT_CATEGORY2 = '30002' THEN
          '003' 
         WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
          '004' 
         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
              TBP.PRODUCT_CATEGORY2 = '30004' THEN
          '005' 
         WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
          '006' 
         ELSE
          '010' 
       END CATEGORY, 
       TCP.PREM_FREQ, 
       TCP.CHARGE_PERIOD, 
       TCP.CHARGE_YEAR, 
       TCP.COVERAGE_PERIOD, 
       TCP.COVERAGE_YEAR, 
       '0' MASTER_FEE, 
       TCM.APPLY_CODE, 
       TCM.POLICY_CODE, 
       'CNY' BZ, 
       (SELECT SUM(A.FEE_AMOUNT)
              FROM DEV_PAS.V_PREM_ALL A
             WHERE TCM.POLICY_CODE = A.POLICY_CODE
               AND A.FEE_STATUS IN ('01', '16', '19')
               AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF, 
       (SELECT SUM(TCP1.STD_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
           AND TCP1.PREM_FREQ <> '1'
           AND TCP1.PRODUCT_CODE IN
               (SELECT DISTINCT TPL.INTERNAL_ID
                  FROM DEV_PDS.T_PRODUCT_LIFE TPL
                  JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
                 WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF, 
       CASE
         WHEN TCP.PREM_FREQ = '5' THEN
          TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
          TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
         WHEN TCP.PREM_FREQ = '1' THEN
          TCP.CHARGE_YEAR - 1
       END CHARGE_YEAR, 
       (SELECT SUM(TCP1.TOTAL_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
           AND TCP1.PRODUCT_CODE IN
               (SELECT DISTINCT TPL.INTERNAL_ID
                  FROM DEV_PAS.T_PRODUCT_LIFE TPL
                  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
                 WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF, 
       TCE.POLICY_PERIOD, 
       (SELECT SUM(TCP1.TOTAL_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) TOTAL_PREM, 
       (SELECT SUM(TCP1.AMOUNT)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) AMOUNT, 
       (SELECT TPA.NEXT_ACCOUNT
          FROM DEV_PAS.T_PAYER_ACCOUNT TPA
         WHERE TPA.POLICY_ID = TCM.POLICY_ID
           AND ROWNUM = 1) NEXT_ACCOUNT, 
       TCM.APPLY_DATE,   
       TCM.ISSUE_DATE, 
       '1' tbrq, 
       TCM.VALIDATE_DATE,
       TCM.VALIDATE_DATE validate_time,
       TCM.EXPIRY_DATE, 
       TCE.PAY_DUE_DATE,
       '01' f111, 
       TCM.SUBINPUT_TYPE, 
       TCM.CHANNEL_TYPE,
       TCM.LIABILITY_STATE 
  FROM DEV_PAS.T_CONTRACT_MASTER TCM
  JOIN DEV_PAS.T_POLICY_HOLDER TPH
    ON TCM.POLICY_CODE = TPH.POLICY_CODE
  JOIN DEV_PAS.T_CUSTOMER TC
    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
  JOIN DEV_PAS.T_ADDRESS TA
    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
    ON TCM.POLICY_ID = TCBP.POLICY_ID
  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    ON TCA.POLICY_ID = TCM.POLICY_ID
 WHERE 1 = 1
   AND TCA.IS_NB_AGENT = '1'
   AND TCA.CHANNEL_TYPE = '03'
   AND TCM.LIABILITY_STATE = '1'
   AND TRIM(TCM.SERVICE_BANK) = '12' 
   AND TCBP.BUSI_PROD_CODE NOT IN ('********','********')
   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL    ]]>
   <if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND TCM.VALIDATE_DATE >= #{batch_start_date}]]></if>
   <if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND TCM.VALIDATE_DATE <= #{batch_end_date} ]]></if>		
   
   		<if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
	<![CDATA[
		 UNION
		 SELECT DISTINCT '记录序号' cc,
							'校验结果（体记录）' dd,
							'校验说明（体记录）' ee,
							(select tbbm.zone_no
							   from dev_pas.t_bank_branch_mapping tbbm
							  where tcm.service_bank_branch = tbbm.bank_branch_code
								and rownum = 1) SERVICE_BANK, 
							(select tbbm.bank_node
							   from dev_pas.t_bank_branch_mapping tbbm
							  where tcm.service_bank_branch = tbbm.bank_branch_code
								and rownum = 1) SERVICE_BANK_BRANCH, 
							TCM.SERVICE_HANDLER_CODE, 
							TCM.ISSUE_DATE DUE_TIME, 
							(SELECT MAX(TPA1.UNIT_NUMBER)
							   FROM DEV_PAS.T_PREM_ARAP TPA1
							  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER, 
							(SELECT MAX(TPA1.UPDATE_TIME)
							   FROM DEV_PAS.T_PREM_ARAP TPA1
							  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
							(SELECT MAX(TPA1.UPDATE_TIME)
							   FROM DEV_PAS.T_PREM_ARAP TPA1
							  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
							TC.CUSTOMER_CERT_TYPE, 
							TC.CUSTOMER_CERTI_CODE, 
							TC.COUNTRY_CODE, 
							TA.STATE, 
							TA.CITY, 
							TA.DISTRICT, 
							TA.ADDRESS, 
							TC.HOUSE_TEL, 
							TC.HOUSE_TEL, 
							TC.MOBILE_TEL, 
							'' fenjh, 
							'1' IS_RIDER, 
							TCBP.BUSI_PROD_CODE, 
							TBP.PRODUCT_NAME_STD,
							CASE
							  WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
							   '001' 
							  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
								   TBP.PRODUCT_CATEGORY2 = '30003' THEN
							   '002' 
							  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
								   TBP.PRODUCT_CATEGORY2 = '30002' THEN
							   '003' 
							  WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
							   '004' 
							  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
								   TBP.PRODUCT_CATEGORY2 = '30004' THEN
							   '005' 
							  WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
							   '006' 
							  ELSE
							   '010' 
							END CATEGORY, 
							TCP.PREM_FREQ, 
							TCP.CHARGE_PERIOD, 
							TCP.CHARGE_YEAR, 
							TCP.COVERAGE_PERIOD, 
							TCP.COVERAGE_YEAR,
							'0' MASTER_FEE,
							TCM.APPLY_CODE, 
							TCM.POLICY_CODE, 
							'CNY' BZ, 
							(SELECT SUM(A.FEE_AMOUNT)
				              FROM DEV_PAS.V_PREM_ALL A
				             WHERE TCM.POLICY_CODE = A.POLICY_CODE
				               AND A.FEE_STATUS IN ('01', '16', '19')
				               AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF, 
							(SELECT SUM(TCP1.STD_PREM_AF)
							   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
							  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
								AND TCP1.PREM_FREQ <> '1'
								AND TCP1.PRODUCT_CODE IN
									(SELECT DISTINCT TPL.INTERNAL_ID
									   FROM DEV_PDS.T_PRODUCT_LIFE TPL
									   JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
										 ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
									  WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF, 
							CASE
							  WHEN TCP.PREM_FREQ = '5' THEN
							   TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
							   TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
							  WHEN TCP.PREM_FREQ = '1' THEN
							   TCP.CHARGE_YEAR - 1
							END CHARGE_YEAR, 
							(SELECT SUM(TCP1.TOTAL_PREM_AF)
							   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
							  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
								AND TCP1.LIABILITY_STATE <> '3'
								AND TCP1.PRODUCT_CODE IN
									(SELECT DISTINCT TPL.INTERNAL_ID
									   FROM DEV_PAS.T_PRODUCT_LIFE TPL
									   JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
										 ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
									  WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF, 
							TCE.POLICY_PERIOD, 
							(SELECT SUM(TCP1.TOTAL_PREM_AF)
							   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
							  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
								AND TCP1.LIABILITY_STATE <> '3') TOTAL_PREM, 
							(SELECT SUM(TCP1.AMOUNT)
							   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
							  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
								AND TCP1.LIABILITY_STATE <> '3') AMOUNT, 
							(SELECT TPA.NEXT_ACCOUNT
							   FROM DEV_PAS.T_PAYER_ACCOUNT TPA
							  WHERE TPA.POLICY_ID = TCM.POLICY_ID
								AND ROWNUM = 1) NEXT_ACCOUNT, 
							TCM.APPLY_DATE,   
							TCM.ISSUE_DATE, 
							'1' tbrq, 
							TCAC.VALIDATE_TIME VALIDATE_DATE,
							TCAC.VALIDATE_TIME,
							TCBP.EXPIRY_DATE, 
							TCE.PAY_DUE_DATE, 
							'12' f111, 
							TCM.SUBINPUT_TYPE, 
							TCM.CHANNEL_TYPE,
						    TCM.LIABILITY_STATE 
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM
			  JOIN DEV_PAS.T_POLICY_HOLDER TPH
				ON TCM.POLICY_CODE = TPH.POLICY_CODE
			  JOIN DEV_PAS.T_CUSTOMER TC
				ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
			  JOIN DEV_PAS.T_ADDRESS TA
				ON TPH.ADDRESS_ID = TA.ADDRESS_ID
			  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				ON TCM.POLICY_ID = TCBP.POLICY_ID
			  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
				ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
			  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
				ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
			  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
				ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			  JOIN DEV_PAS.T_CS_POLICY_CHANGE TCPC
				ON TCPC.POLICY_ID = TCM.POLICY_ID
			  JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
				ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
						AND TCPC.CHANGE_ID = TCAC.CHANGE_ID
			  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
				ON TCA.POLICY_ID = TCM.POLICY_ID
					JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD TCCBP
					  ON TCCBP.CHANGE_ID = TCAC.CHANGE_ID
						AND TCCBP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			 WHERE 1 = 1
			   AND TCCBP.OPERATION_TYPE = '1'
			   AND TCA.IS_NB_AGENT = '1'
			   AND TCA.CHANNEL_TYPE = '03'
			   AND TCAC.SERVICE_CODE IN ('FM', 'NS', 'RA', 'RE','RG','HI','RB','RL','SR')
			   AND TCM.LIABILITY_STATE = '1' 
			   AND TRIM(TCM.SERVICE_BANK) = '12'
			   AND TCBP.BUSI_PROD_CODE NOT IN ('********','********')
			   AND TCAC.ACCEPT_STATUS = '18' ]]>
		
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND TCM.VALIDATE_DATE >= #{batch_start_date}]]></if>
   		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND TCM.VALIDATE_DATE <= #{batch_end_date} ]]></if>		
   		<if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
	</select>
		
	
	<!-- 查询满期的保单 -->
	<select id="PA_findContractMasterPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT '记录序号' cc,
		       '校验结果（体记录）' dd,
		       '校验说明（体记录）' ee,
		       (select tbbm.zone_no
		          from dev_pas.t_bank_branch_mapping tbbm
		         where tcm.service_bank_branch = tbbm.bank_branch_code
		           and rownum = 1) SERVICE_BANK, 
		       (select tbbm.bank_node
		          from dev_pas.t_bank_branch_mapping tbbm
		         where tcm.service_bank_branch = tbbm.bank_branch_code
		           and rownum = 1) SERVICE_BANK_BRANCH, 
		       TCM.SERVICE_HANDLER_CODE, 
				(SELECT MAX(TPA1.DUE_TIME)
			          FROM DEV_CAP.T_PREM_ARAP TPA1
			         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) DUE_TIME,
		       (SELECT MAX(TPA1.UNIT_NUMBER)
		          FROM DEV_PAS.T_PREM_ARAP TPA1
		         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER, 
		       (SELECT MAX(TPA1.UPDATE_TIME)
		          FROM DEV_PAS.T_PREM_ARAP TPA1
		         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
		       (SELECT MAX(TPA1.UPDATE_TIME)
		          FROM DEV_PAS.T_PREM_ARAP TPA1
		         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
		       TC.CUSTOMER_CERT_TYPE, 
		       TC.CUSTOMER_CERTI_CODE, 
		       TC.COUNTRY_CODE, 
		       TA.STATE, 
		       TA.CITY, 
		       TA.DISTRICT, 
		       TA.ADDRESS, 
		       TC.HOUSE_TEL, 
		       TC.HOUSE_TEL, 
		       TC.MOBILE_TEL, 
		       '' fenjh,
		       '1' IS_RIDER, 
		       TCBP.BUSI_PROD_CODE, 
		       TBP.PRODUCT_NAME_STD, 
		       CASE
		         WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
		          '001' 
		         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		              TBP.PRODUCT_CATEGORY2 = '30003' THEN
		          '002' 
		         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		              TBP.PRODUCT_CATEGORY2 = '30002' THEN
		          '003' 
		         WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
		          '004'
		         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		              TBP.PRODUCT_CATEGORY2 = '30004' THEN
		          '005' 
		         WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
		          '006' 
		         ELSE
		          '010' 
		       END CATEGORY, 
		       TCP.PREM_FREQ, 
		       TCP.CHARGE_PERIOD, 
		       TCP.CHARGE_YEAR, 
		       TCP.COVERAGE_PERIOD, 
		       TCP.COVERAGE_YEAR,
		       '0' MASTER_FEE, 
		       TCM.APPLY_CODE, 
		       TCM.POLICY_CODE, 
		       'CNY' BZ, 
		       (SELECT SUM(A.FEE_AMOUNT)
              FROM DEV_PAS.V_PREM_ALL A
             WHERE TCM.POLICY_CODE = A.POLICY_CODE
               AND A.FEE_STATUS IN ('01', '16', '19')
               AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF, 
		       (SELECT SUM(TCP1.STD_PREM_AF)
		          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		           AND TCP1.PREM_FREQ <> '1'
		           AND TCP1.PRODUCT_CODE IN
		               (SELECT DISTINCT TPL.INTERNAL_ID
		                  FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                  JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
		                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                 WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF, 
		       CASE
		         WHEN TCP.PREM_FREQ = '5' THEN
		          TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
		          TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
		         WHEN TCP.PREM_FREQ = '1' THEN
		          TCP.CHARGE_YEAR - 1
		       END CHARGE_YEAR, 
		       (SELECT SUM(TCP1.TOTAL_PREM_AF)
		          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		           AND TCP1.PRODUCT_CODE IN
		               (SELECT DISTINCT TPL.INTERNAL_ID
		                  FROM DEV_PAS.T_PRODUCT_LIFE TPL
		                  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
		                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                 WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF, 
		       TCE.POLICY_PERIOD, 
		       (SELECT SUM(TCP1.TOTAL_PREM_AF)
		          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) TOTAL_PREM, 
		       (SELECT SUM(TCP1.AMOUNT)
		          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) AMOUNT, 
		       (SELECT TPA.NEXT_ACCOUNT
		          FROM DEV_PAS.T_PAYER_ACCOUNT TPA
		         WHERE TPA.POLICY_ID = TCM.POLICY_ID
		           AND ROWNUM = 1) NEXT_ACCOUNT, 
		       TCM.APPLY_DATE,    
		       TCM.ISSUE_DATE, 
		       '1' tbrq, 
		       TCM.VALIDATE_DATE, 
		       TCM.EXPIRY_DATE validate_time,
		       TCM.EXPIRY_DATE, 
		       TCE.PAY_DUE_DATE, 
		       '08' f111, 
		       TCM.SUBINPUT_TYPE, 
		       TCM.CHANNEL_TYPE,
		       TCM.LIABILITY_STATE 
		  FROM DEV_PAS.T_CONTRACT_MASTER TCM
		  JOIN DEV_PAS.T_POLICY_HOLDER TPH
		    ON TCM.POLICY_CODE = TPH.POLICY_CODE
		  JOIN DEV_PAS.T_CUSTOMER TC
		    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		  JOIN DEV_PAS.T_ADDRESS TA
		    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
		  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		    ON TCM.POLICY_ID = TCBP.POLICY_ID
		  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
		    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
		    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
		    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
		    ON TCA.POLICY_ID = TCM.POLICY_ID
		 WHERE 1 = 1
		   AND TCA.IS_NB_AGENT = '1'
		   AND TCA.CHANNEL_TYPE = '03'
		   AND TCM.LIABILITY_STATE = '3' 
		   AND TCM.END_CAUSE = '01'
		   AND TCBP.BUSI_PROD_CODE NOT IN ('********','********')
		   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
		   AND TRIM(TCM.SERVICE_BANK) = '12' ]]>
		   <if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND TCM.EXPIRY_DATE >= #{batch_start_date} ]]></if>
  		   <if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND TCM.EXPIRY_DATE <= #{batch_end_date} ]]></if>	
  		   
		<if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
			
	</select>
	
	<!-- 查询续期的保单 -->
	<select id="PA_findDelayedPolicyJTYHByType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT distinct '记录序号' cc,
		                '校验结果（体记录）' dd,
		                '校验说明（体记录）' ee,
		                (select tbbm.zone_no
		                   from dev_pas.t_bank_branch_mapping tbbm
		                  where tcm.service_bank_branch = tbbm.bank_branch_code
		                    and rownum = 1) SERVICE_BANK, 
		                (select tbbm.bank_node
		                   from dev_pas.t_bank_branch_mapping tbbm
		                  where tcm.service_bank_branch = tbbm.bank_branch_code
		                    and rownum = 1) SERVICE_BANK_BRANCH, 
		                TCM.SERVICE_HANDLER_CODE, 
		                (SELECT MAX(TPA1.DUE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TPA1.DERIV_TYPE = '003'
		                    AND TPA1.BUSINESS_TYPE IN ('1005', '4003')
		                    and tpa1.fee_status in ('01', '19')) DUE_TIME, 
		                (SELECT MAX(TPA1.UNIT_NUMBER)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TPA1.DERIV_TYPE = '003'
		                    AND TPA1.BUSINESS_TYPE IN ('1005', '4003')
		                    and tpa1.fee_status in ('01', '19')) UNIT_NUMBER, 
		                (SELECT MAX(TPA1.UPDATE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TPA1.DERIV_TYPE = '003'
		                    AND TPA1.BUSINESS_TYPE IN ('1005', '4003')
		                    and tpa1.fee_status in ('01', '19')) UPDATE_TIME, 
		                (SELECT MAX(TPA1.UPDATE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TPA1.DERIV_TYPE = '003'
		                    AND TPA1.BUSINESS_TYPE IN ('1005', '4003')
		                    and tpa1.fee_status in ('01', '19')) UPDATE_TIME, 
		                TC.CUSTOMER_CERT_TYPE, 
		                TC.CUSTOMER_CERTI_CODE, 
		                TC.COUNTRY_CODE, 
		                TA.STATE, 
		                TA.CITY, 
		                TA.DISTRICT, 
		                TA.ADDRESS, 
		                TC.HOUSE_TEL, 
		                TC.MOBILE_TEL, 
		                '' fenjh, 
		                '1' IS_RIDER, 
		                TCBP.BUSI_PROD_CODE, 
		                TBP.PRODUCT_NAME_STD, 
		                CASE
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
		                   '001' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30003' THEN
		                   '002' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30002' THEN
		                   '003' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
		                   '004' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30004' THEN
		                   '005' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
		                   '006' 
		                  ELSE
		                   '010' 
		                END CATEGORY, 
		                TCP.PREM_FREQ, 
		                TCP.CHARGE_PERIOD,
		                TCP.CHARGE_YEAR, 
		                TCP.COVERAGE_PERIOD, 
		                TCP.COVERAGE_YEAR,
		                '0' MASTER_FEE, 
		                TCM.APPLY_CODE, 
		                TCM.POLICY_CODE, 
		                'CNY' BZ, 
		                (SELECT SUM(A.FEE_AMOUNT)
			              FROM DEV_PAS.V_PREM_ALL A
			             WHERE TCM.POLICY_CODE = A.POLICY_CODE
			               AND A.FEE_STATUS IN ('01', '16', '19')
			               AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF, 
		                (SELECT SUM(TCP1.STD_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.PREM_FREQ <> '1'
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF, 
		                CASE
		                  WHEN TCP.PREM_FREQ = '5' THEN
		                   TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
		                   TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
		                  WHEN TCP.PREM_FREQ = '1' THEN
		                   TCP.CHARGE_YEAR - 1
		                END CHARGE_YEAR, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PAS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF, 
		                TCE.POLICY_PERIOD, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) TOTAL_PREM, 
		                (SELECT SUM(TCP1.AMOUNT)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) AMOUNT, 
		                (SELECT TPA.NEXT_ACCOUNT
		                   FROM DEV_PAS.T_PAYER_ACCOUNT TPA
		                  WHERE TPA.POLICY_ID = TCM.POLICY_ID
		                    AND ROWNUM = 1) NEXT_ACCOUNT, 
		                TCM.APPLY_DATE, 
		                TCM.ISSUE_DATE, 
		                '1' tbrq, 
		                TCM.VALIDATE_DATE, 
		                PRAR.FINISH_TIME validate_time, 
		                TCM.EXPIRY_DATE, 
		                TCE.PAY_DUE_DATE, 
		                '07' f111, 
		                TCM.SUBINPUT_TYPE, 
		                TCM.CHANNEL_TYPE,
		       			TCM.LIABILITY_STATE 
		  FROM DEV_PAS.T_CONTRACT_MASTER TCM
		  JOIN DEV_PAS.T_POLICY_HOLDER TPH
		    ON TCM.POLICY_CODE = TPH.POLICY_CODE
		  JOIN DEV_PAS.T_CUSTOMER TC
		    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		  JOIN DEV_PAS.T_ADDRESS TA
		    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
		  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		    ON TCM.POLICY_ID = TCBP.POLICY_ID
		  ]]>
		<if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[JOIN DEV_PAS.V_PREM_ARAP_ALL PRAR]]>
		</if>
		<if test=" policycodelist  == null or policycodelist.size()==0">
			<![CDATA[JOIN DEV_PAS.T_PREM_ARAP PRAR]]>
		</if>
   <![CDATA[
		    ON PRAR.POLICY_CODE = TCBP.POLICY_CODE
		   AND PRAR.BUSI_PROD_CODE = TCBP.BUSI_PROD_CODE
		   AND PRAR.BUSINESS_TYPE IN ('1005','4003')
		   AND PRAR.FEE_STATUS IN ('01','19')
		  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
		    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
		    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
		    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		  JOIN ]]>
			<if test="policy_code_list!=null and policy_code_list.size()>0">
			<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
			</if>
			<if test="policy_code_list==null or policy_code_list.size()==0">
			<![CDATA[dev_pas.t_policy_change tpc]]>
			</if>
			<![CDATA[
		    ON TPC.POLICY_ID = TCM.POLICY_ID
		  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
		    ON TCA.POLICY_ID = TCM.POLICY_ID
		 WHERE 1 = 1
		   AND TCA.IS_NB_AGENT = '1'
		   AND TCA.CHANNEL_TYPE = '03'
		   AND TCM.LIABILITY_STATE = '1' 
		   AND TRIM(TCM.SERVICE_BANK) = '12' 
		   AND TCBP.BUSI_PROD_CODE NOT IN ('********','********')
		   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
		   ]]>
		   <if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND tpc.Finish_Time >= #{batch_start_date} ]]></if>
  		   <if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND tpc.Finish_Time < #{batch_end_date} + 1 ]]></if>	
		   
		<if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
			
	</select>
	
	<!-- 查询犹豫期内外部分退保 -->
	<select id="PA_findDelayedPolicyByBF" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT DISTINCT '记录序号' cc,
		                '校验结果（体记录）' dd,
		                '校验说明（体记录）' ee,
		                (select tbbm.zone_no
		                   from dev_pas.t_bank_branch_mapping tbbm
		                  where tcm.service_bank_branch = tbbm.bank_branch_code
		                    and rownum = 1) SERVICE_BANK, 
		                (select tbbm.bank_node
		                   from dev_pas.t_bank_branch_mapping tbbm
		                  where tcm.service_bank_branch = tbbm.bank_branch_code
		                    and rownum = 1) SERVICE_BANK_BRANCH, 
		                TCM.SERVICE_HANDLER_CODE, 
		                (SELECT MAX(TPA1.DUE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) DUE_TIME, 
		                (SELECT MAX(TPA1.UNIT_NUMBER)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER, 
		                (SELECT MAX(TPA1.UPDATE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
		                (SELECT MAX(TPA1.UPDATE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
		                TC.CUSTOMER_CERT_TYPE, 
		                TC.CUSTOMER_CERTI_CODE, 
		                TC.COUNTRY_CODE, 
		                TA.STATE, 
		                TA.CITY, 
		                TA.DISTRICT, 
		                TA.ADDRESS, 
		                TC.HOUSE_TEL, 
		                TC.HOUSE_TEL, 
		                TC.MOBILE_TEL, 
		                '' fenjh, 
		                '1' IS_RIDER, 
		                TCBP.BUSI_PROD_CODE, 
		                TBP.PRODUCT_NAME_STD,
		                CASE
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
		                   '001' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30003' THEN
		                   '002' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30002' THEN
		                   '003' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
		                   '004' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30004' THEN
		                   '005' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
		                   '006' 
		                  ELSE
		                   '010' 
		                END CATEGORY, 
		                TCP.PREM_FREQ, 
		                TCP.CHARGE_PERIOD, 
		                TCP.CHARGE_YEAR, 
		                TCP.COVERAGE_PERIOD, 
		                TCP.COVERAGE_YEAR,
		                '0' MASTER_FEE,
		                TCM.APPLY_CODE, 
		                TCM.POLICY_CODE, 
		                'CNY' BZ, 
		                (SELECT SUM(A.FEE_AMOUNT)
			              FROM DEV_PAS.V_PREM_ALL A
			             WHERE TCM.POLICY_CODE = A.POLICY_CODE
			               AND A.FEE_STATUS IN ('01', '16', '19')
			               AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF, 
		                (SELECT SUM(TCP1.STD_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.PREM_FREQ <> '1'
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF, 
		                CASE
		                  WHEN TCP.PREM_FREQ = '5' THEN
		                   TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
		                   TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
		                  WHEN TCP.PREM_FREQ = '1' THEN
		                   TCP.CHARGE_YEAR - 1
		                END CHARGE_YEAR, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.LIABILITY_STATE <> '3'
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PAS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF, 
		                TCE.POLICY_PERIOD, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.LIABILITY_STATE <> '3') TOTAL_PREM, 
		                (SELECT SUM(TCP1.AMOUNT)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.LIABILITY_STATE <> '3') AMOUNT, 
		                (SELECT TPA.NEXT_ACCOUNT
		                   FROM DEV_PAS.T_PAYER_ACCOUNT TPA
		                  WHERE TPA.POLICY_ID = TCM.POLICY_ID
		                    AND ROWNUM = 1) NEXT_ACCOUNT, 
		                TCM.APPLY_DATE,   
		                TCM.ISSUE_DATE, 
		                TCM.EXPIRY_DATE tbrq,
		                TCM.VALIDATE_DATE, 
		                TCAC.VALIDATE_TIME validate_time,
		                TCBP.EXPIRY_DATE, 
		                TCE.PAY_DUE_DATE, 
		                '05' f111, 
		                TCM.SUBINPUT_TYPE, 
		                TCM.CHANNEL_TYPE,
		       			TCM.LIABILITY_STATE 
		  FROM DEV_PAS.T_CONTRACT_MASTER TCM
		    JOIN ]]>
			<if test="policy_code_list!=null and policy_code_list.size()>0">
			<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
			</if>
			<if test="policy_code_list==null or policy_code_list.size()==0">
			<![CDATA[dev_pas.t_policy_change tpc]]>
			</if>
			<![CDATA[
		    ON TPC.POLICY_ID = TCM.POLICY_ID
		  JOIN DEV_PAS.T_POLICY_HOLDER TPH
		    ON TCM.POLICY_CODE = TPH.POLICY_CODE
		  JOIN DEV_PAS.T_CUSTOMER TC
		    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		  JOIN DEV_PAS.T_ADDRESS TA
		    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
		  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		    ON TCM.POLICY_ID = TCBP.POLICY_ID
		  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
		    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
		    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
		    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CS_POLICY_CHANGE TCPC
		    ON TCPC.POLICY_ID = TCM.POLICY_ID
		  JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
		    ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
		  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
		    ON TCA.POLICY_ID = TCM.POLICY_ID
		 WHERE 1 = 1
		   AND TCA.IS_NB_AGENT = '1'
		   AND TCA.CHANNEL_TYPE = '03'
		   AND TCAC.SERVICE_CODE IN ('CT', 'XT', 'IT', 'EA')
		   AND TCM.LIABILITY_STATE = '1' 
		   AND TRIM(TCM.SERVICE_BANK) = '12'
		   AND TCBP.BUSI_PROD_CODE NOT IN ('********','********')
		   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
		   AND TCAC.ACCEPT_STATUS = '18' 
		   ]]>
		   <if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND TPC.finish_time >= #{batch_start_date} ]]></if>
  		   <if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND TPC.finish_time < #{batch_end_date} +1 ]]></if>
		   
		 <if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
			
	</select>
	
	<!-- 查询犹豫期内外全部退保 -->
	<select id="PA_findDelayedPolicyByQB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT DISTINCT '记录序号' cc,
		                '校验结果（体记录）' dd,
		                '校验说明（体记录）' ee,
						       (select tbbm.zone_no
						          from dev_pas.t_bank_branch_mapping tbbm
						         where tcm.service_bank_branch = tbbm.bank_branch_code
						           and rownum = 1) SERVICE_BANK, 
						       (select tbbm.bank_node
						          from dev_pas.t_bank_branch_mapping tbbm
						         where tcm.service_bank_branch = tbbm.bank_branch_code
						           and rownum = 1) SERVICE_BANK_BRANCH,
		                TCM.SERVICE_HANDLER_CODE, 
		                (SELECT MAX(TPA1.DUE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) DUE_TIME, 
		                (SELECT MAX(TPA1.UNIT_NUMBER)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER, 
		                (SELECT MAX(TPA1.UPDATE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
		                (SELECT MAX(TPA1.UPDATE_TIME)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME, 
		                TC.CUSTOMER_CERT_TYPE, 
		                TC.CUSTOMER_CERTI_CODE, 
		                TC.COUNTRY_CODE, 
		                TA.STATE, 
		                TA.CITY, 
		                TA.DISTRICT, 
		                TA.ADDRESS, 
		                TC.HOUSE_TEL, 
		                TC.HOUSE_TEL, 
		                TC.MOBILE_TEL, 
		                '' fenjh,
		                '1' IS_RIDER, 
		                TCBP.BUSI_PROD_CODE,
		                TBP.PRODUCT_NAME_STD, 
		                CASE
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
		                   '001' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30003' THEN
		                   '002' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30002' THEN
		                   '003' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
		                   '004' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30004' THEN
		                   '005' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
		                   '006'
		                  ELSE
		                   '010' 
		                END CATEGORY, 
		                TCP.PREM_FREQ, 
		                TCP.CHARGE_PERIOD, 
		                TCP.CHARGE_YEAR, 
		                TCP.COVERAGE_PERIOD, 
		                TCP.COVERAGE_YEAR,
		                '0' MASTER_FEE, 
		                TCM.APPLY_CODE, 
		                TCM.POLICY_CODE, 
		                'CNY' BZ, 
		                (SELECT SUM(A.FEE_AMOUNT)
			              FROM DEV_PAS.V_PREM_ALL A
			             WHERE TCM.POLICY_CODE = A.POLICY_CODE
			               AND A.FEE_STATUS IN ('01', '16', '19')
			               AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF, 
		                (SELECT SUM(TCP1.STD_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.PREM_FREQ <> '1'
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF, 
		                CASE
		                  WHEN TCP.PREM_FREQ = '5' THEN
		                   TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
		                   TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
		                  WHEN TCP.PREM_FREQ = '1' THEN
		                   TCP.CHARGE_YEAR - 1
		                END CHARGE_YEAR, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PAS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF, 
		                TCE.POLICY_PERIOD, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) TOTAL_PREM, 
		                (SELECT SUM(TCP1.AMOUNT)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) AMOUNT, 
		                (SELECT TPA.NEXT_ACCOUNT
		                   FROM DEV_PAS.T_PAYER_ACCOUNT TPA
		                  WHERE TPA.POLICY_ID = TCM.POLICY_ID
		                    AND ROWNUM = 1) NEXT_ACCOUNT, 
		                TCM.APPLY_DATE, 
		                TCM.ISSUE_DATE, 
		                TCM.EXPIRY_DATE tbrq,
		                TCM.VALIDATE_DATE, 
		                TCAC.VALIDATE_TIME validate_time,
		                TCBP.EXPIRY_DATE, 
		                TCE.PAY_DUE_DATE, 
		                case
		                  when tcac.hesitate_flag = '1' then
		                   '04'
		                  else
		                   '06'
		                end f111, 
		                TCM.SUBINPUT_TYPE, 
		                TCM.CHANNEL_TYPE,
		       			TCM.LIABILITY_STATE 
		  FROM DEV_PAS.T_CONTRACT_MASTER TCM
		    JOIN ]]>
			<if test="policy_code_list!=null and policy_code_list.size()>0">
			<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
			</if>
			<if test="policy_code_list==null or policy_code_list.size()==0">
			<![CDATA[dev_pas.t_policy_change tpc]]>
			</if>
			<![CDATA[
		    ON TPC.POLICY_ID = TCM.POLICY_ID
		  JOIN DEV_PAS.T_POLICY_HOLDER TPH
		    ON TCM.POLICY_CODE = TPH.POLICY_CODE
		  JOIN DEV_PAS.T_CUSTOMER TC
		    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		  JOIN DEV_PAS.T_ADDRESS TA
		    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
		  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		    ON TCM.POLICY_ID = TCBP.POLICY_ID
		  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
		    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
		    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
		    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CS_POLICY_CHANGE TCPC
		    ON TCPC.POLICY_ID = TCM.POLICY_ID
		  JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
		    ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
		  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
		    ON TCA.POLICY_ID = TCM.POLICY_ID
		 WHERE 1 = 1
		   AND TCA.IS_NB_AGENT = '1'
		   AND TCA.CHANNEL_TYPE = '03'
		   AND TCAC.SERVICE_CODE IN ('CT', 'XT', 'IT', 'EA')
		   AND TCM.LIABILITY_STATE = '3' 
		   AND TCM.END_CAUSE = '03'
		   AND TRIM(TCM.SERVICE_BANK) = '12' 
		   AND TCBP.BUSI_PROD_CODE NOT IN ('********','********')
		   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
		   AND TCAC.ACCEPT_STATUS = '18' 
		    ]]>
		    <if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND TPC.finish_time >= #{batch_start_date} ]]></if>
  		    <if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND TPC.finish_time < #{batch_end_date} +1 ]]></if>
		    
		  <if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
			
	</select>
	
	<!-- 查询发生保全的保单 -->
	<select id="PA_findDelayedPolicyByBQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT DISTINCT '记录序号' cc,
		                '校验结果（体记录）' dd,
		                '校验说明（体记录）' ee,
		                (select tbbm.zone_no
		                   from dev_pas.t_bank_branch_mapping tbbm
		                  where tcm.service_bank_branch = tbbm.bank_branch_code
		                    and rownum = 1) SERVICE_BANK, 
		                (select tbbm.bank_node
		                   from dev_pas.t_bank_branch_mapping tbbm
		                  where tcm.service_bank_branch = tbbm.bank_branch_code
		                    and rownum = 1) SERVICE_BANK_BRANCH, 
		                TCM.SERVICE_HANDLER_CODE, 
		                (SELECT MAX(TCPC1.Validate_Time)
		                   FROM DEV_PAS.T_CS_POLICY_CHANGE TCPC1
		                  WHERE TCPC1.POLICY_ID = TCM.POLICY_ID
		                    AND TCPC1.SERVICE_CODE IN ('CM', 'PA', 'XD', 'PT', 'AM', 'RE', 'SR')) DUE_TIME, 
		                (SELECT MAX(TPA1.UNIT_NUMBER)
		                   FROM DEV_PAS.T_PREM_ARAP TPA1
		                  WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER, 
		                (SELECT MAX(TCPC1.Validate_Time)
		                   FROM DEV_PAS.T_CS_POLICY_CHANGE TCPC1
		                  WHERE TCPC1.POLICY_ID = TCM.POLICY_ID
		                    AND TCPC1.SERVICE_CODE IN ('CM', 'PA', 'XD', 'PT', 'AM', 'RE', 'SR')) UPDATE_TIME, 
		                (SELECT MAX(TCPC1.Validate_Time)
		                   FROM DEV_PAS.T_CS_POLICY_CHANGE TCPC1
		                  WHERE TCPC1.POLICY_ID = TCM.POLICY_ID
		                    AND TCPC1.SERVICE_CODE IN ('CM', 'PA', 'XD', 'PT', 'AM', 'RE', 'SR')) UPDATE_TIME, 
		                TC.CUSTOMER_CERT_TYPE, 
		                TC.CUSTOMER_CERTI_CODE, 
		                TC.COUNTRY_CODE, 
		                TA.STATE, 
		                TA.CITY, 
		                TA.DISTRICT, 
		                TA.ADDRESS, 
		                TC.HOUSE_TEL, 
		                TC.HOUSE_TEL, 
		                TC.MOBILE_TEL, 
		                '' fenjh, 
		                '1' IS_RIDER, 
		                TCBP.BUSI_PROD_CODE, 
		                TBP.PRODUCT_NAME_STD, 
		                CASE
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
		                   '001' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30003' THEN
		                   '002' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30002' THEN
		                   '003' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
		                   '004' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
		                       TBP.PRODUCT_CATEGORY2 = '30004' THEN
		                   '005' 
		                  WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
		                   '006' 
		                  ELSE
		                   '010' 
		                END CATEGORY, 
		                TCP.PREM_FREQ, 
		                TCP.CHARGE_PERIOD, 
		                TCP.CHARGE_YEAR, 
		                TCP.COVERAGE_PERIOD, 
		                TCP.COVERAGE_YEAR,
		                '0' MASTER_FEE, 
		                TCM.APPLY_CODE, 
		                TCM.POLICY_CODE, 
		                'CNY' BZ, 
		                (SELECT SUM(A.FEE_AMOUNT)
			              FROM DEV_PAS.V_PREM_ALL A
			             WHERE TCM.POLICY_CODE = A.POLICY_CODE
			               AND A.FEE_STATUS IN ('01', '16', '19')
			               AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF, 
		                (SELECT SUM(TCP1.STD_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.PREM_FREQ <> '1'
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PDS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF, 
		                CASE
		                  WHEN TCP.PREM_FREQ = '5' THEN
		                   TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
		                   TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
		                  WHEN TCP.PREM_FREQ = '1' THEN
		                   TCP.CHARGE_YEAR - 1
		                END CHARGE_YEAR, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
		                    AND TCP1.PRODUCT_CODE IN
		                        (SELECT DISTINCT TPL.INTERNAL_ID
		                           FROM DEV_PAS.T_PRODUCT_LIFE TPL
		                           JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
		                             ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
		                          WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF, 
		                TCE.POLICY_PERIOD, 
		                (SELECT SUM(TCP1.TOTAL_PREM_AF)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) TOTAL_PREM, 
		                (SELECT SUM(TCP1.AMOUNT)
		                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
		                  WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) AMOUNT, 
		                (SELECT TPA.NEXT_ACCOUNT
		                   FROM DEV_PAS.T_PAYER_ACCOUNT TPA
		                  WHERE TPA.POLICY_ID = TCM.POLICY_ID
		                    AND ROWNUM = 1) NEXT_ACCOUNT, 
		                TCM.APPLY_DATE,      
		                TCM.ISSUE_DATE, 
		                TCM.EXPIRY_DATE tbrq, 
		                TCM.VALIDATE_DATE, 
		                TCAC.VALIDATE_TIME validate_time,
		                TCBP.EXPIRY_DATE,
		                TCE.PAY_DUE_DATE, 
		                case
		                  when TCAC.SERVICE_CODE = 'CM' then
		                   '08'
		                  when TCAC.SERVICE_CODE = 'AM' then
		                   '13'
		                  when TCAC.SERVICE_CODE = 'PT' then
		                   '10'
		                  else
		                   '09'
		                end f111, 
		                TCM.SUBINPUT_TYPE, 
		                TCM.CHANNEL_TYPE,
		       			TCM.LIABILITY_STATE 
		  FROM DEV_PAS.T_CONTRACT_MASTER TCM
		    JOIN ]]>
			<if test="policy_code_list!=null and policy_code_list.size()>0">
			<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
			</if>
			<if test="policy_code_list==null or policy_code_list.size()==0">
			<![CDATA[dev_pas.t_policy_change tpc]]>
			</if>
			<![CDATA[
		    ON TPC.POLICY_ID = TCM.POLICY_ID
		  JOIN DEV_PAS.T_POLICY_HOLDER TPH
		    ON TCM.POLICY_CODE = TPH.POLICY_CODE
		  JOIN DEV_PAS.T_CUSTOMER TC
		    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		  JOIN DEV_PAS.T_ADDRESS TA
		    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
		  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		    ON TCM.POLICY_ID = TCBP.POLICY_ID
		  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
		    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
		    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
		    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		  JOIN DEV_PAS.T_CS_POLICY_CHANGE TCPC
		    ON TCPC.POLICY_ID = TCM.POLICY_ID
		  JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
		    ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
		  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
		    ON TCA.POLICY_ID = TCM.POLICY_ID
		 WHERE 1 = 1
		   AND TCA.IS_NB_AGENT = '1'
		   AND TCA.CHANNEL_TYPE = '03'
		   AND TCAC.SERVICE_CODE IN ('CM', 'PA', 'XD', 'PT', 'AM', 'RE', 'SR')
		   AND TCPC.SERVICE_CODE IN ('CM', 'PA', 'XD', 'PT', 'AM', 'RE', 'SR')
		   AND TCM.LIABILITY_STATE = '1' 
		   AND TRIM(TCM.SERVICE_BANK) = '12' 
		   AND TCBP.BUSI_PROD_CODE NOT IN ('********','********')
		   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
		   AND TCAC.ACCEPT_STATUS = '18'
		   ]]>
		   <if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND TPC.FINISH_TIME >= #{batch_start_date}  ]]></if>
  		   <if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND TPC.FINISH_TIME < #{batch_end_date} +1 ]]></if>
  		    
		 <if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
			
	</select>
	
<!-- 查询合同成立前撤保的保单 -->
	<select id="PA_findDelayedPolicyPreEff" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT '记录序号' cc,
       '校验结果（体记录）' dd,
       '校验说明（体记录）' ee,
       (select tbbm.zone_no
          from dev_pas.t_bank_branch_mapping tbbm
         where tcm.service_bank_branch = tbbm.bank_branch_code
           and rownum = 1) SERVICE_BANK,
       (select tbbm.bank_node
          from dev_pas.t_bank_branch_mapping tbbm
         where tcm.service_bank_branch = tbbm.bank_branch_code
           and rownum = 1) SERVICE_BANK_BRANCH,
       TCM.SERVICE_HANDLER_CODE,
       (SELECT MAX(TPA1.DUE_TIME)
          FROM DEV_CAP.T_PREM_ARAP TPA1
         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) DUE_TIME,
       (SELECT MAX(TPA1.UNIT_NUMBER)
          FROM DEV_PAS.T_PREM_ARAP TPA1
         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER,
       (SELECT MAX(TPA1.UPDATE_TIME)
          FROM DEV_PAS.T_PREM_ARAP TPA1
         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME,
       (SELECT MAX(TPA1.UPDATE_TIME)
          FROM DEV_PAS.T_PREM_ARAP TPA1
         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME,
       TC.CUSTOMER_CERT_TYPE,
       TC.CUSTOMER_CERTI_CODE,
       TC.COUNTRY_CODE,
       TA.STATE,
       TA.CITY,
       TA.DISTRICT,
       TA.ADDRESS,
       TC.HOUSE_TEL,
       TC.HOUSE_TEL,
       TC.MOBILE_TEL,
       '' fenjh,
       '1' IS_RIDER,
       TCBP.BUSI_PROD_CODE,
       TCBP.RENEW,
       TBP.PRODUCT_NAME_STD,
       CASE
         WHEN TBP.PRODUCT_CATEGORY1 = '20002' THEN
          '001'
         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
              TBP.PRODUCT_CATEGORY2 = '30003' THEN
          '002'
         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
              TBP.PRODUCT_CATEGORY2 = '30002' THEN
          '003'
         WHEN TBP.PRODUCT_CATEGORY1 = '20003' THEN
          '004'
         WHEN TBP.PRODUCT_CATEGORY1 = '20001' AND
              TBP.PRODUCT_CATEGORY2 = '30004' THEN
          '005'
         WHEN TBP.PRODUCT_CATEGORY1 = '20004' THEN
          '006'
         ELSE
          '010'
       END CATEGORY,
       TCP.PREM_FREQ,
       TCP.CHARGE_PERIOD,
       TCP.CHARGE_YEAR,
       TCP.COVERAGE_PERIOD,
       TCP.COVERAGE_YEAR,
       TCP.ITEM_ID,
       '0' MASTER_FEE,
       TCM.APPLY_CODE,
       TCM.POLICY_CODE,
       'CNY' BZ,
       (SELECT SUM(A.FEE_AMOUNT)
          FROM DEV_PAS.V_PREM_ALL A
         WHERE TCM.POLICY_CODE = A.POLICY_CODE
           AND A.FEE_STATUS IN ('01', '16', '19')
           AND A.FEE_SCENE_CODE = 'NB') INITIAL_DISCNT_PREM_AF,
       (SELECT SUM(TCP1.STD_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
           AND TCP1.PREM_FREQ <> '1'
           AND TCP1.PRODUCT_CODE IN
               (SELECT DISTINCT TPL.INTERNAL_ID
                  FROM DEV_PDS.T_PRODUCT_LIFE TPL
                  JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
                 WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) STD_PREM_AF,
       CASE
         WHEN TCP.PREM_FREQ = '5' THEN
          TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
          TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
         WHEN TCP.PREM_FREQ = '1' THEN
          TCP.CHARGE_YEAR - 1
       END CHARGE_YEAR,
       (SELECT SUM(TCP1.TOTAL_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
           AND TCP1.PRODUCT_CODE IN
               (SELECT DISTINCT TPL.INTERNAL_ID
                  FROM DEV_PAS.T_PRODUCT_LIFE TPL
                  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP1
                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID
                 WHERE TBP1.PRODUCT_CODE_SYS = TBP.PRODUCT_CODE_SYS)) TOTAL_PREM_AF,
       TCE.POLICY_PERIOD,
       (SELECT SUM(TCP1.TOTAL_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) TOTAL_PREM,
       (SELECT SUM(TCP1.AMOUNT)
          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE) AMOUNT,
       (SELECT TPA.NEXT_ACCOUNT
          FROM DEV_PAS.T_PAYER_ACCOUNT TPA
         WHERE TPA.POLICY_ID = TCM.POLICY_ID
           AND ROWNUM = 1) NEXT_ACCOUNT,
       TCM.APPLY_DATE,
       TCM.ISSUE_DATE,
       TCM.EXPIRY_DATE tbrq,
       TCM.VALIDATE_DATE,
       TCM.VALIDATE_DATE validate_time,
       TCM.EXPIRY_DATE,
       TCE.PAY_DUE_DATE,
       '14' f111,
       TCM.SUBINPUT_TYPE,
       TCM.CHANNEL_TYPE,
       TCM.LIABILITY_STATE
  FROM DEV_PAS.T_CONTRACT_MASTER TCM
  JOIN DEV_PAS.T_POLICY_HOLDER TPH
    ON TCM.POLICY_CODE = TPH.POLICY_CODE
  JOIN DEV_PAS.T_CUSTOMER TC
    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
  JOIN DEV_PAS.T_ADDRESS TA
    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
    ON TCM.POLICY_ID = TCBP.POLICY_ID
  JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    ON TCA.POLICY_ID = TCM.POLICY_ID
 WHERE 1 = 1
   AND TCA.IS_NB_AGENT = '1'
   AND TCA.CHANNEL_TYPE = '03'
   AND TCM.LIABILITY_STATE = '3'
   AND TCM.END_CAUSE = '15'
   AND TCBP.BUSI_PROD_CODE NOT IN ('********', '********')
   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
   AND TRIM(TCM.SERVICE_BANK) = '12'
		]]>
		   <if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ AND TCM.EXPIRY_DATE >= #{batch_start_date} ]]></if>
  		   <if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ AND TCM.EXPIRY_DATE <= #{batch_end_date} ]]></if>	
  		   
		<if test=" policycodelist  != null and policycodelist.size()!=0">
			<![CDATA[ and tcm.policy_code in ]]>
			<foreach collection="policycodelist" item="policycode"
				index="index" open="(" close=")" separator=",">#{policycode}</foreach>
		</if>
		
		<if test="submit_channel_list != null and submit_channel_list.size()!=0">
		<![CDATA[ and tcm.submit_channel in ]]>
			<foreach collection="submit_channel_list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		
		<if test=" busiprodcodelist  != null and busiprodcodelist.size()!=0">
			<![CDATA[ and tcbp.busi_prod_code in ]]>
			<foreach collection="busiprodcodelist" item="busiprodcode"
				index="index" open="(" close=")" separator=",">#{busiprodcode}</foreach>
		</if>
			
	</select>
	
</mapper>
