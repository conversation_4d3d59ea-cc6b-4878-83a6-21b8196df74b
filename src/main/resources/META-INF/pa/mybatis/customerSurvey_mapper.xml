<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICustomerSurveyDao">

	<sql id="customerSurveyWhereCondition">
		<if test=" reserved_field3 != null and reserved_field3 != ''  "><![CDATA[ AND A.RESERVED_FIELD3 = #{reserved_field3} ]]></if>
		<if test=" ph_certi_type != null and ph_certi_type != ''  "><![CDATA[ AND A.PH_CERTI_TYPE = #{ph_certi_type} ]]></if>
		<if test=" reserved_field4 != null and reserved_field4 != ''  "><![CDATA[ AND A.RESERVED_FIELD4 = #{reserved_field4} ]]></if>
		<if test=" reserved_field5 != null and reserved_field5 != ''  "><![CDATA[ AND A.RESERVED_FIELD5 = #{reserved_field5} ]]></if>
		<if test=" reserved_field6 != null and reserved_field6 != ''  "><![CDATA[ AND A.RESERVED_FIELD6 = #{reserved_field6} ]]></if>
		<if test=" main_busi_prd_code != null and main_busi_prd_code != ''  "><![CDATA[ AND A.MAIN_BUSI_PRD_CODE = #{main_busi_prd_code} ]]></if>
		<if test=" audit_organ_code != null and audit_organ_code != ''  "><![CDATA[ AND A.AUDIT_ORGAN_CODE = #{audit_organ_code} ]]></if>
		<if test=" reserved_field7 != null and reserved_field7 != ''  "><![CDATA[ AND A.RESERVED_FIELD7 = #{reserved_field7} ]]></if>
		<if test=" reserved_field8 != null and reserved_field8 != ''  "><![CDATA[ AND A.RESERVED_FIELD8 = #{reserved_field8} ]]></if>
		<if test=" reserved_field9 != null and reserved_field9 != ''  "><![CDATA[ AND A.RESERVED_FIELD9 = #{reserved_field9} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" reserved_field10 != null and reserved_field10 != ''  "><![CDATA[ AND A.RESERVED_FIELD10 = #{reserved_field10} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_template_id  != null "><![CDATA[ AND A.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" insurant_order  != null "><![CDATA[ AND A.INSURANT_ORDER = #{insurant_order} ]]></if>
		<if test=" main_busi_prd_prem_years  != null "><![CDATA[ AND A.MAIN_BUSI_PRD_PREM_YEARS = #{main_busi_prd_prem_years} ]]></if>
		<if test=" ph_birthday  != null  and  ph_birthday  != ''  "><![CDATA[ AND A.PH_BIRTHDAY = #{ph_birthday} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" reserved_field2 != null and reserved_field2 != ''  "><![CDATA[ AND A.RESERVED_FIELD2 = #{reserved_field2} ]]></if>
		<if test=" reserved_field1 != null and reserved_field1 != ''  "><![CDATA[ AND A.RESERVED_FIELD1 = #{reserved_field1} ]]></if>
		<if test=" ph_cert_type != null and ph_cert_type != ''  "><![CDATA[ AND A.PH_CERT_TYPE = #{ph_cert_type} ]]></if>
		<if test=" ph_certi_code != null and ph_certi_code != ''  "><![CDATA[ AND A.PH_CERTI_CODE = #{ph_certi_code} ]]></if>
		<if test=" total_premium  != null "><![CDATA[ AND A.TOTAL_PREMIUM = #{total_premium} ]]></if>
		<if test=" main_busi_prod_code != null and main_busi_prod_code != ''  "><![CDATA[ AND A.MAIN_BUSI_PROD_CODE = #{main_busi_prod_code} ]]></if>
		<if test=" main_busi_prd_coverage  != null "><![CDATA[ AND A.MAIN_BUSI_PRD_COVERAGE = #{main_busi_prd_coverage} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" total_sa  != null "><![CDATA[ AND A.TOTAL_SA = #{total_sa} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" survey_object  != null "><![CDATA[ AND A.SURVEY_OBJECT = #{survey_object} ]]></if>
		<if test=" survey_id  != null "><![CDATA[ AND A.SURVEY_ID = #{survey_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCustomerSurveyBySurveyIdCondition">
		<if test=" survey_id  != null "><![CDATA[ AND A.SURVEY_ID = #{survey_id} ]]></if>
	</sql>	
	<sql id="queryCustomerSurveyByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCustomerSurvey"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CUSTOMER_SURVEY(
				RESERVED_FIELD3, PH_CERTI_TYPE, RESERVED_FIELD4, RESERVED_FIELD5, RESERVED_FIELD6, MAIN_BUSI_PRD_CODE, AUDIT_ORGAN_CODE, 
				RESERVED_FIELD7, RESERVED_FIELD8, RESERVED_FIELD9, CUSTOMER_ID, RESERVED_FIELD10, APPLY_CODE, INSERT_TIMESTAMP, 
				UPDATE_BY, SURVEY_TEMPLATE_ID, CHANGE_ID, INSURANT_ORDER, MAIN_BUSI_PRD_PREM_YEARS, PH_BIRTHDAY, 
				POLICY_ID, RESERVED_FIELD2, RESERVED_FIELD1, PH_CERT_TYPE, INSERT_TIME, PH_CERTI_CODE, TOTAL_PREMIUM, 
				UPDATE_TIME, MAIN_BUSI_PROD_CODE, MAIN_BUSI_PRD_COVERAGE, POLICY_CODE, PAY_MODE, TOTAL_SA, UPDATE_TIMESTAMP, 
				INSERT_BY, AGENT_CODE, SURVEY_OBJECT, SURVEY_ID ) 
			VALUES (
				#{reserved_field3, jdbcType=VARCHAR}, #{ph_certi_type, jdbcType=VARCHAR} , #{reserved_field4, jdbcType=VARCHAR} , #{reserved_field5, jdbcType=VARCHAR} , #{reserved_field6, jdbcType=VARCHAR} , #{main_busi_prd_code, jdbcType=VARCHAR} , #{audit_organ_code, jdbcType=VARCHAR} 
				, #{reserved_field7, jdbcType=VARCHAR} , #{reserved_field8, jdbcType=VARCHAR} , #{reserved_field9, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{reserved_field10, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{update_by, jdbcType=NUMERIC} , #{survey_template_id, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{insurant_order, jdbcType=NUMERIC} , #{main_busi_prd_prem_years, jdbcType=NUMERIC} , #{ph_birthday, jdbcType=DATE} ,
				, #{policy_id, jdbcType=NUMERIC} , #{reserved_field2, jdbcType=VARCHAR} , #{reserved_field1, jdbcType=VARCHAR} , #{ph_cert_type, jdbcType=VARCHAR} , SYSDATE , #{ph_certi_code, jdbcType=VARCHAR} , #{total_premium, jdbcType=NUMERIC} 
				, SYSDATE , #{main_busi_prod_code, jdbcType=VARCHAR} , #{main_busi_prd_coverage, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR} , #{total_sa, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{agent_code, jdbcType=VARCHAR} , #{survey_object, jdbcType=NUMERIC} , #{survey_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCustomerSurvey" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY WHERE SURVEY_ID = #{survey_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCustomerSurvey" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER_SURVEY ]]>
		<set>
		<trim suffixOverrides=",">
			RESERVED_FIELD3 = #{reserved_field3, jdbcType=VARCHAR} ,
			PH_CERTI_TYPE = #{ph_certi_type, jdbcType=VARCHAR} ,
			RESERVED_FIELD4 = #{reserved_field4, jdbcType=VARCHAR} ,
			RESERVED_FIELD5 = #{reserved_field5, jdbcType=VARCHAR} ,
			RESERVED_FIELD6 = #{reserved_field6, jdbcType=VARCHAR} ,
			MAIN_BUSI_PRD_CODE = #{main_busi_prd_code, jdbcType=VARCHAR} ,
			AUDIT_ORGAN_CODE = #{audit_organ_code, jdbcType=VARCHAR} ,
			RESERVED_FIELD7 = #{reserved_field7, jdbcType=VARCHAR} ,
			RESERVED_FIELD8 = #{reserved_field8, jdbcType=VARCHAR} ,
			RESERVED_FIELD9 = #{reserved_field9, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			RESERVED_FIELD10 = #{reserved_field10, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SURVEY_TEMPLATE_ID = #{survey_template_id, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    INSURANT_ORDER = #{insurant_order, jdbcType=NUMERIC} ,
		    MAIN_BUSI_PRD_PREM_YEARS = #{main_busi_prd_prem_years, jdbcType=NUMERIC} ,
		    PH_BIRTHDAY = #{ph_birthday, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			RESERVED_FIELD2 = #{reserved_field2, jdbcType=VARCHAR} ,
			RESERVED_FIELD1 = #{reserved_field1, jdbcType=VARCHAR} ,
			PH_CERT_TYPE = #{ph_cert_type, jdbcType=VARCHAR} ,
			PH_CERTI_CODE = #{ph_certi_code, jdbcType=VARCHAR} ,
		    TOTAL_PREMIUM = #{total_premium, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			MAIN_BUSI_PROD_CODE = #{main_busi_prod_code, jdbcType=VARCHAR} ,
		    MAIN_BUSI_PRD_COVERAGE = #{main_busi_prd_coverage, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    TOTAL_SA = #{total_sa, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		    SURVEY_OBJECT = #{survey_object, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE SURVEY_ID = #{survey_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCustomerSurveyBySurveyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.PH_CERTI_TYPE, A.RESERVED_FIELD4, A.RESERVED_FIELD5, A.RESERVED_FIELD6, A.MAIN_BUSI_PRD_CODE, A.AUDIT_ORGAN_CODE, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.RESERVED_FIELD9, A.CUSTOMER_ID, A.RESERVED_FIELD10, A.APPLY_CODE, 
			A.SURVEY_TEMPLATE_ID, A.CHANGE_ID, A.INSURANT_ORDER, A.MAIN_BUSI_PRD_PREM_YEARS, A.PH_BIRTHDAY,
			A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, A.PH_CERT_TYPE, A.PH_CERTI_CODE, A.TOTAL_PREMIUM, 
			A.MAIN_BUSI_PROD_CODE, A.MAIN_BUSI_PRD_COVERAGE, A.POLICY_CODE, A.PAY_MODE, A.TOTAL_SA, 
			A.AGENT_CODE, A.SURVEY_OBJECT, A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY A WHERE 1 = 1  ]]>
		<include refid="queryCustomerSurveyBySurveyIdCondition" />
		<![CDATA[ ORDER BY A.SURVEY_ID ]]>
	</select>
	
	<select id="findCustomerSurveyByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.PH_CERTI_TYPE, A.RESERVED_FIELD4, A.RESERVED_FIELD5, A.RESERVED_FIELD6, A.MAIN_BUSI_PRD_CODE, A.AUDIT_ORGAN_CODE, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.RESERVED_FIELD9, A.CUSTOMER_ID, A.RESERVED_FIELD10, A.APPLY_CODE, 
			A.SURVEY_TEMPLATE_ID, A.CHANGE_ID, A.INSURANT_ORDER, A.MAIN_BUSI_PRD_PREM_YEARS, A.PH_BIRTHDAY,
			A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, A.PH_CERT_TYPE, A.PH_CERTI_CODE, A.TOTAL_PREMIUM, 
			A.MAIN_BUSI_PROD_CODE, A.MAIN_BUSI_PRD_COVERAGE, A.POLICY_CODE, A.PAY_MODE, A.TOTAL_SA, 
			A.AGENT_CODE, A.SURVEY_OBJECT, A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY A WHERE 1 = 1  ]]>
		<include refid="queryCustomerSurveyByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.SURVEY_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCustomerSurvey" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.PH_CERTI_TYPE, A.RESERVED_FIELD4, A.RESERVED_FIELD5, A.RESERVED_FIELD6, A.MAIN_BUSI_PRD_CODE, A.AUDIT_ORGAN_CODE, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.RESERVED_FIELD9, A.CUSTOMER_ID, A.RESERVED_FIELD10, A.APPLY_CODE, 
			A.SURVEY_TEMPLATE_ID, A.CHANGE_ID, A.INSURANT_ORDER, A.MAIN_BUSI_PRD_PREM_YEARS, A.PH_BIRTHDAY,
			A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, A.PH_CERT_TYPE, A.PH_CERTI_CODE, A.TOTAL_PREMIUM, 
			A.MAIN_BUSI_PROD_CODE, A.MAIN_BUSI_PRD_COVERAGE, A.POLICY_CODE, A.PAY_MODE, A.TOTAL_SA, 
			A.AGENT_CODE, A.SURVEY_OBJECT, A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCustomerSurvey" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.PH_CERTI_TYPE, A.RESERVED_FIELD4, A.RESERVED_FIELD5, A.RESERVED_FIELD6, A.MAIN_BUSI_PRD_CODE, A.AUDIT_ORGAN_CODE, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.RESERVED_FIELD9, A.CUSTOMER_ID, A.RESERVED_FIELD10, A.APPLY_CODE, 
			A.SURVEY_TEMPLATE_ID, A.CHANGE_ID, A.INSURANT_ORDER, A.MAIN_BUSI_PRD_PREM_YEARS, A.PH_BIRTHDAY,
			A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, A.PH_CERT_TYPE, A.PH_CERTI_CODE, A.TOTAL_PREMIUM, 
			A.MAIN_BUSI_PROD_CODE, A.MAIN_BUSI_PRD_COVERAGE, A.POLICY_CODE, A.PAY_MODE, A.TOTAL_SA, 
			A.AGENT_CODE, A.SURVEY_OBJECT, A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY A WHERE ROWNUM <=  1000  ]]>
		<include refid="customerSurveyWhereCondition" />
		<![CDATA[ ORDER BY A.SURVEY_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCustomerSurveyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCustomerSurveyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RESERVED_FIELD3, B.PH_CERTI_TYPE, B.RESERVED_FIELD4, B.RESERVED_FIELD5, B.RESERVED_FIELD6, B.MAIN_BUSI_PRD_CODE, B.AUDIT_ORGAN_CODE, 
			B.RESERVED_FIELD7, B.RESERVED_FIELD8, B.RESERVED_FIELD9, B.CUSTOMER_ID, B.RESERVED_FIELD10, B.APPLY_CODE, 
			B.SURVEY_TEMPLATE_ID, B.CHANGE_ID, B.INSURANT_ORDER, B.MAIN_BUSI_PRD_PREM_YEARS, B.PH_BIRTHDAY,
			B.POLICY_ID, B.RESERVED_FIELD2, B.RESERVED_FIELD1, B.PH_CERT_TYPE, B.PH_CERTI_CODE, B.TOTAL_PREMIUM, 
			B.MAIN_BUSI_PROD_CODE, B.MAIN_BUSI_PRD_COVERAGE, B.POLICY_CODE, B.PAY_MODE, B.TOTAL_SA, 
			B.AGENT_CODE, B.SURVEY_OBJECT, B.SURVEY_ID FROM (
					SELECT ROWNUM RN, A.RESERVED_FIELD3, A.PH_CERTI_TYPE, A.RESERVED_FIELD4, A.RESERVED_FIELD5, A.RESERVED_FIELD6, A.MAIN_BUSI_PRD_CODE, A.AUDIT_ORGAN_CODE, 
			A.RESERVED_FIELD7, A.RESERVED_FIELD8, A.RESERVED_FIELD9, A.CUSTOMER_ID, A.RESERVED_FIELD10, A.APPLY_CODE, 
			A.SURVEY_TEMPLATE_ID, A.CHANGE_ID, A.INSURANT_ORDER, A.MAIN_BUSI_PRD_PREM_YEARS, A.PH_BIRTHDAY,
			A.POLICY_ID, A.RESERVED_FIELD2, A.RESERVED_FIELD1, A.PH_CERT_TYPE, A.PH_CERTI_CODE, A.TOTAL_PREMIUM, 
			A.MAIN_BUSI_PROD_CODE, A.MAIN_BUSI_PRD_COVERAGE, A.POLICY_CODE, A.PAY_MODE, A.TOTAL_SA, 
			A.AGENT_CODE, A.SURVEY_OBJECT, A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findAllCustomerSurvey" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.POLICY_CODE, A.PAY_MODE, A.SURVEY_ID FROM APP___PAS__DBUSER.T_CUSTOMER_SURVEY A WHERE ROWNUM <=  1000  ]]>
		<include refid="customerSurveyWhereCondition" />
		<![CDATA[ ORDER BY A.SURVEY_ID ]]> 
	</select>
	
	
</mapper>
