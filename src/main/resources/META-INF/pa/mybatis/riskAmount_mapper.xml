<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="riskAmount">

	<sql id="PA_riskAmountWhereCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" amount_status  != null "><![CDATA[ AND A.AMOUNT_STATUS = #{amount_status} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT = #{risk_amount} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryRiskAmountByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_queryRiskAmountByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_findAllRiskTypeAndAmountCondition">
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND T.CUSTOMER_ID = #{ customer_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND T.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND T.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND T.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="PA_findAllRiskTypeAndAmountByCustomerCondition">
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND r.CUSTOMER_ID = #{ customer_id} ]]></if>
	</sql>
	<sql id="PA_findRiskAmountCondition">
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" amount_status  != null "><![CDATA[ AND A.AMOUNT_STATUS = #{amount_status} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT = #{risk_amount} ]]></if>
		
		<if test=" liability_state_list  != null and liability_state_list.size()!=0">
		<![CDATA[ AND (A.liability_state in ]]>
		<foreach collection ="liability_state_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		<if test=" liability_is_null  != null "><![CDATA[ OR A.liability_state is null ]]></if>
		<![CDATA[ )]]>
		</if>
	</sql>
	<sql id="PA_queryRiskAmountByBusiCode">
		<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ AND CRA.BUSI_PROD_CODE IN]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>
	</sql>
<!-- 添加操作 -->
	<insert id="PA_addRiskAmount"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_RISK_AMOUNT__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RISK_AMOUNT(
				INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, ITEM_ID, RISK_TYPE, START_TIME, BUSI_PROD_CODE, 
				APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, END_TIME, 
				BUSI_ITEM_ID, INSERT_BY, POLICY_ID, AMOUNT_STATUS, INTERNAL_CODE, RISK_AMOUNT ) 
			VALUES (
				SYSDATE, #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{risk_type, jdbcType=VARCHAR} , #{start_time, jdbcType=DATE} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{end_time, jdbcType=DATE} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{amount_status, jdbcType=NUMERIC} , #{internal_code, jdbcType=VARCHAR} , #{risk_amount, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteRiskAmount" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE A.LIST_ID=#{list_id} ]]>
		<include refid="PA_riskAmountWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateRiskAmount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT ]]>
		<set>
		<trim suffixOverrides=",">
		   CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			RISK_TYPE = #{risk_type, jdbcType=VARCHAR} ,
		    START_TIME = #{start_time, jdbcType=DATE} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    AMOUNT_STATUS = #{amount_status, jdbcType=NUMERIC} ,
			INTERNAL_CODE = #{internal_code, jdbcType=VARCHAR} ,
		    RISK_AMOUNT = #{risk_amount, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[WHERE 1 = 1 AND LIST_ID = #{list_id}]]>
	</update>
	
<!-- 修改操作 -->
	<update id="updateRiskAmountInbatch" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT ]]>
		<set>
		<trim suffixOverrides=",">
		   CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			RISK_TYPE = #{risk_type, jdbcType=VARCHAR} ,
		    START_TIME = #{start_time, jdbcType=DATE} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    AMOUNT_STATUS = #{amount_status, jdbcType=NUMERIC} ,
			INTERNAL_CODE = #{internal_code, jdbcType=VARCHAR} ,
		    RISK_AMOUNT = #{risk_amount, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[WHERE ITEM_ID = #{item_id} AND POLICY_CODE = #{policy_code} AND RISK_TYPE = #{risk_type}]]>
		<if test=" customer_id  != null "><![CDATA[ AND CUSTOMER_ID = #{customer_id} ]]></if>
	</update>
	
	
	<!-- 修改操作 -->
	<update id="updateRiskAmountStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT ]]>
		<set>
		<trim suffixOverrides=",">
		    AMOUNT_STATUS = #{amount_status, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[WHERE ITEM_ID = #{item_id} ]]>
	</update>	
		

<!-- 按索引查询操作 -->	
	<select id="PA_findRiskAmountByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.START_TIME, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.AMOUNT_STATUS, A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryRiskAmountByCustomerIdCondition" />
	</select>
	
	<select id="PA_findRiskAmountByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.START_TIME, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.AMOUNT_STATUS, A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryRiskAmountByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.START_TIME, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.AMOUNT_STATUS, A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.START_TIME, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.AMOUNT_STATUS, A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE ROWNUM <=  1000 ]]>
		<include refid="PA_riskAmountWhereCondition" />
		<if test=" min_risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT > #{min_risk_amount} ]]></if>
	</select>
	
	<!-- 查询所有操作  按RISKTYPE排序 -->
	<select id="PA_findAllRiskAmountByRiskType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TO_CHAR(LISTAGG(A.AGGR_RISK_TYPE, '、') WITHIN
               GROUP(ORDER BY A.AGGR_RISK_TYPE ASC)) RISK_TYPE
  FROM (SELECT DISTINCT TO_NUMBER(T.AGGR_RISK_TYPE) AGGR_RISK_TYPE
          FROM APP___PDS__DBUSER.T_PRODUCT_AGGR_RISK T
         WHERE T.PRODUCT_ID IN
               (SELECT D.PRODUCT_ID
                  FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT C
                 INNER JOIN APP___PDS__DBUSER.T_PRODUCT_LIFE D
                    ON C.BUSINESS_PRD_ID = D.BUSINESS_PRD_ID
                 WHERE C.BUSINESS_PRD_ID = #{business_prd_id,jdbcType = NUMERIC})
         GROUP BY T.AGGR_RISK_TYPE) A ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findRiskAmountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryRiskAmountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_ID, B.ITEM_ID, B.RISK_TYPE, B.START_TIME, B.BUSI_PROD_CODE, 
			B.APPLY_CODE, B.POLICY_CODE, B.LIST_ID, B.END_TIME, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.AMOUNT_STATUS, B.INTERNAL_CODE, B.RISK_AMOUNT FROM (
					SELECT ROWNUM RN, A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.START_TIME, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.AMOUNT_STATUS, A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 风险保额查询条件查询	 -->
	<select id="PA_findAllRiskTypeAndAmount" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[select tary.risk_type,
				tary.risk_desc,
       nvl(riskamount.active_risk, 0) as active_risk,
       nvl(riskamount.terminated_risk, 0) as terminated_risk 
  from APP___PAS__DBUSER.T_aggregation_risk_type tary
  left join (select r.risk_type,
                    (SELECT nvl(sum(a.risk_amount), 0)
                       FROM APP___PAS__DBUSER.T_risk_amount a
                      where sysdate > a.start_time
                        and sysdate < a.end_time
                        and a.risk_type = r.risk_type
                        and a.customer_id = r.customer_id
                        and a.busi_prod_code = r.busi_prod_code) active_risk,
                    (SELECT nvl(sum(b.risk_amount), 0)
                       FROM APP___PAS__DBUSER.T_risk_amount b
                      where sysdate < b.start_time
                         or sysdate > b.end_time
                        and b.risk_type = r.risk_type
                        and b.customer_id = r.customer_id
                        and b.busi_prod_code = r.busi_prod_code) terminated_risk
               from APP___PAS__DBUSER.T_risk_amount r where 1=1 ]]>      
                <include refid="PA_findAllRiskTypeAndAmountByCustomerCondition" />
             <![CDATA[  group by r.risk_type, r.customer_id, r.busi_prod_code) riskamount
    on tary.risk_type = riskamount.risk_type
]]> 
	</select>
<!-- 	查询风险保额单条记录 -->
	<select id="PA_findRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.START_TIME, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.AMOUNT_STATUS, A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_findRiskAmountCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<select id="PA_findRiskAmountAddByLiangpl" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT  A.LIST_ID FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE A.ITEM_ID = #{item_id} AND A.RISK_TYPE = #{risk_type}  ]]>
	</select>
	
	<!-- 条件查询所有的风险类型 -->
	<select id="PA_findAllRiskType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT DISTINCT A.risk_type FROM APP___PAS__DBUSER.T_risk_amount A WHERE 1=1 ]]>
		<include refid="PA_findRiskAmountCondition" />
	</select>
	<!-- 	条件累加风险保额 -->
	<select id="PA_sumRiskAmountByAmountStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      SELECT SUM(A.RISK_AMOUNT) as risk_amount FROM (
  		  select t1.*,
               t2.apply_code,
               t2.item_id,
               t2.liability_state
          from APP___PAS__DBUSER.T_risk_amount t1
          left join APP___PAS__DBUSER.T_contract_product t2
            on (t1.item_id = t2.item_id and t1.apply_code = t2.apply_code)
            where t1.amount_status =1 )  A WHERE 1=1]]>
		<include refid="PA_findRiskAmountCondition" />			    
	</select>
	
	<!-- 查询被保险人风险保额 -->
	<select id="findSumAmountOfInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      select a.risk_type,sum(a.risk_amount) as risk_amount from APP___PAS__DBUSER.T_risk_amount a where 1=1 ]]>
		<include refid="PA_findRiskAmountCondition" />			    
		<![CDATA[    group by a.risk_type ]]>
	</select>
	
	<!-- 查询每个保单对应客户的累积风险保额 -->
	<select id="findSumAuountOfPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select C.RISK_TYPE, sum(C.RISK_AMOUNT) sum_amount
          from APP___PAS__DBUSER.T_risk_amount C
         where exists
               (SELECT 1
                  FROM APP___PAS__DBUSER.T_insured_list    B,
                       APP___PAS__DBUSER.T_CONTRACT_MASTER mas
                 where B.policy_code = mas.policy_code
                   and exists
                       (SELECT 1
                          FROM APP___PAS__DBUSER.T_insured_list A
                         where B.customer_id=A.Customer_Id
                           and A.policy_code = #{policy_code})
                   and mas.Liability_State = '1' and c.policy_code=mas.policy_code )
         group by C.RISK_TYPE ]]>
	</select>
	
	<!-- 根据客户号查询该客户的累积风险保额 -->
	<select id="findSumAuountOfCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select sum(mout.risk_amount) sum_risk_amount from APP___PAS__DBUSER.T_risk_amount mout where mout.policy_code in (
					SELECT mas.policy_code
									  FROM APP___PAS__DBUSER.T_insured_list ins ,APP___PAS__DBUSER.T_CONTRACT_MASTER mas
									 where 1=1 
									 and ins.policy_code = mas.policy_code
									 and ins.customer_id = #{customer_id}  ]]>
								<if test="liability_state != null and liability_state != ''">
									<![CDATA[ and mas.liability_state = #{liability_state}  ]]>
								</if>	 
					<![CDATA[  )  ]]>
	</select>
	
	<!-- 通过客户号查询不同类型的累积风险保额  -->
	<select id="findRiskAuountByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select sum(am.risk_amount) sum_amount,am.risk_type 
					  from APP___PAS__DBUSER.T_risk_amount am, APP___PAS__DBUSER.t_insured_list ins
					 where am.policy_code = ins.policy_code 
					 and ins.customer_id = #{customer_id}
					 group by am.risk_type   ]]>
	</select>
	
	<!-- 根据客户号查询该客户不在某些责任组内时的所有累积风险保额 -->
	<select id="findRiskAmountByCustomerIdAndOutOfItemIds" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      select a.risk_type,sum(a.risk_amount) as risk_amount from APP___PAS__DBUSER.T_risk_amount a where 1=1 ]]>
		 <include refid="PA_findRiskAmountCondition" />		    
		 <if test="policy_chg_id_list  != null and policy_chg_id_list.size()!=0 ">
		 <![CDATA[  AND a.item_id NOT IN ( SELECT B.item_id from APP___PAS__DBUSER.t_cs_contract_product B WHERE 1=1 AND (]]>
			<foreach collection="policy_chg_id_list" item="policy_chg_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ B.POLICY_CHG_ID=#{policy_chg_id_item}]]>
			</foreach>
			<![CDATA[)]]>
			<if test=" old_new != null and old_new != ''  "><![CDATA[ AND B.OLD_NEW = #{old_new} ]]></if>
			<![CDATA[and ( B.operation_type = '1'  or B.operation_type = '2')]]>
			<![CDATA[)]]>
			
			</if>
		<![CDATA[    group by a.risk_type ]]> 
	</select>
	
	<select id="findRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.START_TIME, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, A.END_TIME, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.AMOUNT_STATUS, A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE ROWNUM <=  1000 ]]>
		<include refid="PA_riskAmountWhereCondition" />
	</select>
	
	<!-- 核保查询风险累计计算用 -->
	<select id="PA_queryRiskAmountValueForCalc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
	SELECT RISK_TYPE, NVL(SUM(TERMINATEDRISK),0) AS TERMINATEDRISK,  
       NVL(SUM(LAPSERISK),0) AS LAPSERISK, 
       NVL(SUM(ACTIVERISK),0) AS ACTIVERISK
  FROM (SELECT RISK_TYPE,
               CASE
                 WHEN AMOUNT_STATUS = 3 THEN
                  SUM(RISK_AMOUNT)
               END TERMINATEDRISK,
               CASE
                 WHEN AMOUNT_STATUS = 4 THEN
                  SUM(RISK_AMOUNT)
               END LAPSERISK,
               CASE
                 WHEN AMOUNT_STATUS = 1 THEN
                  SUM(RISK_AMOUNT)
               END ACTIVERISK
          FROM (SELECT TR.RISK_TYPE AS RISK_TYPE,
                       SUM(TR.RISK_AMOUNT) AS RISK_AMOUNT,
                       CASE
                         WHEN TR.AMOUNT_STATUS = 3 OR TC.LIABILITY_STATE = 3 THEN
                          3
                         WHEN TC.LIABILITY_STATE = 4 THEN
                          4
                         ELSE
                          1
                       END AS AMOUNT_STATUS
                  FROM APP___PAS__DBUSER.T_RISK_AMOUNT TR
                  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
                    ON TR.ITEM_ID = TC.ITEM_ID
                    	AND TR.APPLY_CODE = TC.APPLY_CODE
                 WHERE  1 = 1   
                  AND EXISTS (SELECT R.CUSTOMER_ID
                          FROM APP___PAS__DBUSER.T_CUSTOMER R
                         WHERE R.CUSTOMER_ID = #{customer_id}
                           AND R.CUSTOMER_ID = TR.CUSTOMER_ID)]]>
                  <if test=" no_calc_risk_amount_list  != null and no_calc_risk_amount_list">
					<![CDATA[ AND (TR.BUSI_PROD_CODE NOT IN ]]>
						<foreach collection ="no_calc_risk_amount_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
					<![CDATA[ )]]>
                  </if>
                 <![CDATA[ GROUP BY TR.RISK_TYPE, TC.LIABILITY_STATE, TR.AMOUNT_STATUS)
         GROUP BY RISK_TYPE, AMOUNT_STATUS)
 GROUP BY RISK_TYPE ]]>
	</select>
	
	<!-- 核保查询风险累计显示用 -->
	<select id="PA_queryRiskAmountValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
 SELECT RISK_TYPE,
       PRODUCT_ID,
       INTERNAL_CODE,
       AMOUNT_STATUS,
       NVL(SUM(TERMINATEDRISK),0) AS TERMINATEDRISK, 
       NVL(SUM(LAPSERISK),0) AS LAPSERISK, 
       NVL(SUM(ACTIVERISK),0) AS ACTIVERISK
  FROM (SELECT RISK_TYPE,
               PRODUCT_ID,
               INTERNAL_CODE,
               AMOUNT_STATUS,
               CASE
                 WHEN AMOUNT_STATUS = 3 THEN
                  SUM(RISK_AMOUNT)
               END TERMINATEDRISK,
               CASE
                 WHEN AMOUNT_STATUS = 4 THEN
                  SUM(RISK_AMOUNT)
               END LAPSERISK,
               CASE
                 WHEN AMOUNT_STATUS = 1 THEN
                  SUM(RISK_AMOUNT)
               END ACTIVERISK
          FROM (SELECT SUM(T.RISK_AMOUNT) RISK_AMOUNT,
                       T.RISK_TYPE,
                       T.AMOUNT_STATUS,
                       T.PRODUCT_ID,
                       T.INTERNAL_CODE
                  FROM (SELECT TC.PRODUCT_ID,
                               TR.INTERNAL_CODE,
                               TR.RISK_TYPE,
                               SUM(TR.RISK_AMOUNT) AS RISK_AMOUNT,
                               CASE
                                 WHEN TR.AMOUNT_STATUS = 3 OR
                                      TC.LIABILITY_STATE = 3 THEN
                                  3
                                 WHEN TC.LIABILITY_STATE = 4 THEN
                                  4
                                 ELSE
                                  1
                               END AS AMOUNT_STATUS
                          FROM APP___PAS__DBUSER.T_RISK_AMOUNT TR
                          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
                            ON TR.ITEM_ID = TC.ITEM_ID
                            AND TR.APPLY_CODE = TC.APPLY_CODE
                         WHERE EXISTS
                         (SELECT R.CUSTOMER_ID
                                  FROM APP___PAS__DBUSER.T_CUSTOMER R
                                 WHERE R.CUSTOMER_ID = #{customer_id}
                                   AND R.CUSTOMER_ID = TR.CUSTOMER_ID) ]]>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list.size()!=0">
			<![CDATA[ AND (TR.BUSI_PROD_CODE IN ]]>
				<foreach collection ="busi_prod_code_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
			<![CDATA[ )]]>
		</if>
		<![CDATA[        GROUP BY RISK_TYPE,
                                  TC.LIABILITY_STATE,
                                  TR.AMOUNT_STATUS,
                                  TC.PRODUCT_ID,
                                  TR.INTERNAL_CODE,
                                  TR.RISK_TYPE) T
                 GROUP BY T.RISK_TYPE,
                          T.AMOUNT_STATUS,
                          T.PRODUCT_ID,
                          T.INTERNAL_CODE)
         GROUP BY RISK_TYPE, AMOUNT_STATUS, PRODUCT_ID, INTERNAL_CODE)
 GROUP BY RISK_TYPE, PRODUCT_ID, INTERNAL_CODE,AMOUNT_STATUS
		 ]]>
	</select>
<!-- 	查询客户下以承包保单详细信息 -->
	<select id="PA_findDetilRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[  SELECT 
               TRA.RISK_TYPE,
               TRA.START_TIME,
               TRA.BUSI_PROD_CODE,
               TRA.APPLY_CODE,
               TRA.AMOUNT_STATUS,
               TRA.INTERNAL_CODE,
               TRA.RISK_AMOUNT,
               TCP.PRODUCT_ID,
               TCP.ITEM_ID 
          FROM  APP___PAS__DBUSER.T_RISK_AMOUNT TRA,
          APP___PAS__DBUSER.t_Contract_Product TCP
         WHERE 1=1
           AND TRA.POLICY_CODE IS NOT NULL
           AND TCP.ITEM_ID = TRA.ITEM_ID
           AND TRA.CUSTOMER_ID = #{customer_id}
           AND tra.RISK_AMOUNT > #{min_risk_amount} 
           AND TRA.AMOUNT_STATUS <>3 ]]>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list.size()!=0">
			<![CDATA[ AND (TRA.BUSI_PROD_CODE IN ]]>
				<foreach collection ="busi_prod_code_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
			<![CDATA[ )]]>
		</if>
		<![CDATA[ 
		           
		  
   		]]>
<!-- 	  SELECT A.CUSTOMER_ID, -->
<!--        A.ITEM_ID, -->
<!--        A.RISK_TYPE, -->
<!--        A.START_TIME, -->
<!--        A.BUSI_PROD_CODE, -->
<!--        A.APPLY_CODE, -->
<!--        A.POLICY_CODE, -->
<!--        A.LIST_ID, -->
<!--        A.END_TIME, -->
<!--        A.BUSI_ITEM_ID, -->
<!--        A.POLICY_ID, -->
<!--        A.AMOUNT_STATUS, -->
<!--        A.INTERNAL_CODE, -->
<!--        A.RISK_AMOUNT -->
<!--   FROM APP___PAS__DBUSER.T_RISK_AMOUNT A -->
<!--  where exists (SELECT R.Customer_Id, R.* -->
<!--           FROM APP___PAS__DBUSER.T_CUSTOMER R -->
<!--          WHERE exists (SELECT T.* -->
<!--                   FROM APP___PAS__DBUSER.T_CUSTOMER T -->
<!--                  WHERE T.CUSTOMER_ID = #{customer_id} -->
<!--                    and t.un_customer_code = R.Un_Customer_Code) -->
<!--            and R.Customer_Id = A.Customer_Id) -->
<!--    AND A.RISK_AMOUNT > #{min_risk_amount} -->
   
    
	</select>
    <!--     查询客户下未承保保单的风险保额信息 -->
	<select id="PA_findRiskAmountNoSign" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select tra.CUSTOMER_ID,
                 tra.ITEM_ID,
                 tra.RISK_TYPE,
                 tra.START_TIME,
                 tra.BUSI_PROD_CODE,
                 tra.APPLY_CODE,
                 tra.POLICY_CODE,
                 tra.LIST_ID,
                 tra.END_TIME,
                 tra.BUSI_ITEM_ID,
                 tra.POLICY_ID,
                 tra.AMOUNT_STATUS,
                 tra.INTERNAL_CODE,
                 tra.RISK_AMOUNT
            from (select customer_id
                    from APP___PAS__DBUSER.t_customer tci
                   where tci.customer_id = #{customer_id}) tc
            left join APP___PAS__DBUSER.T_RISK_AMOUNT tra
              on tc.customer_id = tra.Customer_Id
           where  tra.policy_code is null
             and tra.CUSTOMER_ID is not null 
             AND TRA.AMOUNT_STATUS <> 3 
		]]>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list.size()!=0">
			<![CDATA[ AND (TRA.BUSI_PROD_CODE IN ]]>
				<foreach collection ="busi_prod_code_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
			<![CDATA[ )]]>
		</if>
	</select>
	
	
	<!-- 修改操作 -->
	<update id="PA_updateRiskAmountSetStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT A ]]>
		<set>
		<trim suffixOverrides=",">
		    AMOUNT_STATUS = #{amount_status, jdbcType=NUMERIC} ,
		    RISK_AMOUNT = #{risk_amount, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[WHERE 1=1]]>
		<include refid="PA_riskAmountWhereConditionNoStatus" />
	</update>
	
	<sql id="PA_riskAmountWhereConditionNoStatus">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT = #{risk_amount} ]]></if>
	</sql>
	
	<!-- 查询特殊产品风险保额 -->
	<select id="PA_findAmountOfBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT RISK_TYPE, SUM(TERMINATEDRISK) AS TERMINATEDRISK, SUM(LAPSERISK) AS LAPSERISK, SUM(ACTIVERISK) AS ACTIVERISK
  FROM (SELECT RISK_TYPE,
               CASE
                 WHEN AMOUNT_STATUS = 3 THEN
                  SUM(RISK_AMOUNT)
               END TERMINATEDRISK,
               CASE
                 WHEN AMOUNT_STATUS = 4 THEN
                  SUM(RISK_AMOUNT)
               END LAPSERISK,
               CASE
                 WHEN AMOUNT_STATUS = 1 THEN
                  SUM(RISK_AMOUNT)
               END ACTIVERISK
          FROM (SELECT TR.RISK_TYPE AS RISK_TYPE,
                       SUM(TR.RISK_AMOUNT) AS RISK_AMOUNT,
                       CASE
                         WHEN TR.AMOUNT_STATUS = 3 OR TC.LIABILITY_STATE = 3 THEN
                          3
                         WHEN TC.LIABILITY_STATE = 4 THEN
                          4
                         ELSE
                          1
                       END AS AMOUNT_STATUS
                  FROM APP___PAS__DBUSER.T_RISK_AMOUNT TR
                  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
                    ON TR.ITEM_ID = TC.ITEM_ID
                 WHERE EXISTS (SELECT R.CUSTOMER_ID
                          FROM APP___PAS__DBUSER.T_CUSTOMER R
                         WHERE R.CUSTOMER_ID = #{customer_id}
                           AND R.CUSTOMER_ID = TR.CUSTOMER_ID)
                           ]]>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list.size()!=0">
			<![CDATA[ AND (TR.BUSI_PROD_CODE IN ]]>
			<foreach collection ="busi_prod_code_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
			<![CDATA[ )]]>
		</if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND TR.RISK_TYPE = #{risk_type} ]]></if>
        <![CDATA[          GROUP BY TR.RISK_TYPE, TC.LIABILITY_STATE, TR.AMOUNT_STATUS)
         GROUP BY RISK_TYPE, AMOUNT_STATUS)
 GROUP BY RISK_TYPE 
		]]>
	</select>
<!-- 查询新投保固定年限内的风险保额 -->
	<select id="PA_querySpecialRiskAmountIsNull" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
			   SELECT 
       CRA.CUSTOMER_ID,
       CRA.POLICY_ID,
       CRA.POLICY_CODE,
       CRA.APPLY_CODE,
       CRA.BUSI_ITEM_ID,
       CRA.BUSI_PROD_CODE,
       CRA.ITEM_ID,
       CRA.INTERNAL_CODE,
       CRA.RISK_TYPE,
       CRA.AMOUNT_STATUS,
       CRA.START_TIME,
       CRA.END_TIME,
       CRA.RISK_AMOUNT
      FROM APP___PAS__DBUSER.T_RISK_AMOUNT CRA
     WHERE 1 = 1
       AND CRA.START_TIME < #{apply_date}
       AND CRA.POLICY_CODE IS NULL
       AND TO_CHAR(ADD_MONTHS(CRA.START_TIME, #{num_Year})) >= #{apply_date}
       AND CRA.AMOUNT_STATUS != 3
        AND CRA.CUSTOMER_ID=#{customer_id}
	]]>
	<include refid="PA_queryRiskAmountByBusiCode" />
	</select>
	<!-- 查询新投保一至两年内的风险保额 -->
	<select id="PA_querySpecialRiskAmountIsNullOneToTwo" resultType="java.util.Map" 
	parameterType="java.util.Map">
	<![CDATA[
			   SELECT 
       CRA.CUSTOMER_ID,
       CRA.POLICY_ID,
       CRA.POLICY_CODE,
       CRA.APPLY_CODE,
       CRA.BUSI_ITEM_ID,
       CRA.BUSI_PROD_CODE,
       CRA.ITEM_ID,
       CRA.INTERNAL_CODE,
       CRA.RISK_TYPE,
       CRA.AMOUNT_STATUS,
       CRA.START_TIME,
       CRA.END_TIME,
       CRA.RISK_AMOUNT
      FROM APP___PAS__DBUSER.T_RISK_AMOUNT CRA
     WHERE 1 = 1
       AND TO_CHAR(ADD_MONTHS(CRA.START_TIME, 12)) < #{apply_date}
       AND TO_CHAR(ADD_MONTHS(CRA.START_TIME, 24)) >= #{apply_date}
       AND CRA.AMOUNT_STATUS != 3
       AND CRA.POLICY_CODE IS NULL
       AND CRA.CUSTOMER_ID=#{customer_id}
	]]>
	<include refid="PA_queryRiskAmountByBusiCode" />
	</select>
	<!-- 查询保单是否做过保单复效 -->
	<select id="PA_queryIsExitsnormalLapse" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT NVL(TCBP.RENEW_TIMES, 0) RENEW_TIMES,
        TCBP.INITIAL_VALIDATE_DATE,
        TCBP.LIABILITY_STATE
   FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
  WHERE 1 = 1
    AND TCBP.POLICY_ID = #{policy_id}
    AND TCBP.BUSI_ITEM_ID = #{busi_item_id} 
    
		 ]]>
	</select>
	<!-- 查询是否存在保全在途 -->
	<select id="PA_queryIsExitsLockPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TCPC.POLICY_ID,TCPC.APPLY_TIME APPLY_DATE,TCPC.POLICY_CODE
  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC,APP___PAS__DBUSER.T_LOCK_POLICY LP,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC
 WHERE 1 = 1 AND
         CAC.ACCEPT_ID = TCPC.ACCEPT_ID
       AND  LP.BUSINESS_CODE = CAC.ACCEPT_CODE
       AND  TCPC.SERVICE_CODE IN ('NS','PA')
       AND TCPC.POLICY_CODE = #{policy_code} 
		 ]]>
	</select>
	<!-- 查询保全在途的风险保额 -->
	
	<select id="PA_queryCsRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
	select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         max(CASE
             WHEN 
                  tcm.LIABILITY_STATE = 3 THEN
              3
             WHEN tcm.LIABILITY_STATE = 4 THEN
              4
             ELSE
              1
           END) AS AMOUNT_STATUS,
         cra.busi_prod_code,cra.policy_code
        from APP___PAS__DBUSER.t_cs_accept_change cac,
             APP___PAS__DBUSER.t_cs_policy_change cpc,
             APP___PAS__DBUSER.t_cs_risk_amount   cra,
             APP___PAS__DBUSER.t_Contract_Master tcm
       where cac.accept_id = cpc.accept_id and cra.amount_status<>3
         and cpc.policy_chg_id = cra.policy_chg_id 
         and tcm.policy_code=cpc.policy_code
         and cra.customer_id = #{customer_id}
          and cac.accept_status in ('07', '08', '09', '10', '11', '13')
		 ]]>
    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and cpc.policy_code = #{policy_code}  ]]></if>
		<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ and CRA.BUSI_PROD_CODE IN]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>	
	<![CDATA[  group by cra.risk_type, cra.busi_prod_code,cra.policy_code]]>
	</select>
	
	<!-- 查询保全在途的风险保额 -->
	<select id="PA_findDetilRiskByCs" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[   
select distinct a.CUSTOMER_ID,
                a.ITEM_ID,
                a.RISK_TYPE,
                a.START_TIME,
                a.BUSI_PROD_CODE,
                a.APPLY_CODE,
                a.POLICY_CODE,
                a.LIST_ID,
                a.END_TIME,
                a.BUSI_ITEM_ID,
                a.POLICY_ID,
                a.AMOUNT_STATUS,
                a.INTERNAL_CODE,
                a.RISK_AMOUNT,
                tcp.liability_state,
                tcp.charge_period,
                tcp.charge_year,
                tcp.coverage_period,
                tcp.coverage_year,
                tcp.amount,
                tcp.total_prem_af,
                tcp.extra_prem_af,
                tcp.benefit_level,
                tcp.std_prem_af,
                tcp.validate_date,
                tcp.prem_freq,
                tcp.product_id
  from (select tra.CUSTOMER_ID,
               tra.ITEM_ID,
               tra.RISK_TYPE,
               tra.START_TIME,
               tra.BUSI_PROD_CODE,
               tra.APPLY_CODE,
               tra.POLICY_CODE,
               tra.LIST_ID,
               tra.END_TIME,
               tra.BUSI_ITEM_ID,
               tra.POLICY_ID,
               tra.AMOUNT_STATUS,
               tra.INTERNAL_CODE,
               sum((case tra.old_new
	             when '1' then
	              tra.risk_amount
	             when '0' then
	              -tra.risk_amount
	           end)) as RISK_AMOUNT
          from (select customer_id
                  from APP___PAS__DBUSER.t_customer tci
                 where exists
                 (select tun.customer_id
                          from APP___PAS__DBUSER.t_customer tun
                         where tun.customer_id = #{customer_id}
                           and tci.customer_id = tun.customer_id)) tc
          join APP___PAS__DBUSER.t_cs_risk_amount tra
            on tc.customer_id = tra.Customer_Id
          join APP___PAS__DBUSER.t_cs_policy_change cpc
            on cpc.policy_chg_id = tra.policy_chg_id
          join APP___PAS__DBUSER.t_cs_accept_change cac
            on cac.accept_id = cpc.accept_id
         where tra.policy_code is not null and tra.amount_status<>3
           AND tra.RISK_AMOUNT > #{min_risk_amount} 
             and cac.accept_status in ('07', '08', '09', '10', '11', '13')
                    group by tra.CUSTOMER_ID,
               tra.ITEM_ID,
               tra.RISK_TYPE,
               tra.START_TIME,
               tra.BUSI_PROD_CODE,
               tra.APPLY_CODE,
               tra.POLICY_CODE,
               tra.LIST_ID,
               tra.END_TIME,
               tra.BUSI_ITEM_ID,
               tra.POLICY_ID,
               tra.AMOUNT_STATUS,
               tra.INTERNAL_CODE
           ]]>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list.size()!=0">
			<![CDATA[ AND (TRA.BUSI_PROD_CODE IN ]]>
				<foreach collection ="busi_prod_code_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
			<![CDATA[ )]]>
		</if>
		<![CDATA[ 
		           ) a
		  left join APP___PAS__DBUSER.t_contract_product tcp
		    on a.item_id = tcp.item_id
   		]]>    
	</select>
	
	<!-- 查询一年期以内加保，并且生效的风险累计 -->
	<select id="PA_riskAmountOneYearPADetail" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
		    select sum((TRA.RISK_AMOUNT / TCP.AMOUNT * TSC.SA)) risk_amount,
		           tra.risk_type
		      from APP___PAS__DBUSER.t_policy_change      tpc,
		           APP___PAS__DBUSER.t_risk_amount        tra,
		           APP___PAS__DBUSER.t_sa_change          tsc,
		           APP___PAS__DBUSER.t_contract_product   tcp,
		           APP___PAS__DBUSER.t_contract_busi_prod tcbp
		     where tpc.validate_time >= add_months(#{apply_time}, -12)
		       and tpc.validate_time <= #{apply_time}
		       and tpc.policy_chg_id = tsc.policy_chg_id
		       and tsc.policy_id = tcp.policy_id
		       and tsc.item_id = tcp.item_id
		       and tcp.item_id = tra.item_id
		       and tcp.busi_item_id = tcbp.busi_item_id
		       and tra.customer_id = #{customer_id}
		       and tcp.liability_state in ('1', '4')
		       and tpc.service_code = 'PA'
			   and tpc.change_flag ='1'
		       and tra.amount_status <> 3
		     ]]>
		<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ and tcbp.BUSI_PROD_CODE IN]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>
        <![CDATA[ group by risk_type ]]>
	</select>
	
	<!-- 查询一年期以外，两年期以内加保，并且生效的风险累计 -->
	<select id="PA_riskAmountTWOYearPADetail" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
       select sum((TRA.RISK_AMOUNT / TCP.AMOUNT * TSC.SA)) risk_amount,
              tra.risk_type
         from APP___PAS__DBUSER.t_policy_change      tpc,
              APP___PAS__DBUSER.t_risk_amount        tra,
              APP___PAS__DBUSER.t_sa_change          tsc,
              APP___PAS__DBUSER.t_contract_product   tcp,
              APP___PAS__DBUSER.t_contract_busi_prod tcbp
        where tpc.validate_time >= add_months(#{apply_time}, -24)
          and tpc.validate_time < add_months(#{apply_time}, -12)
          and tpc.policy_chg_id = tsc.policy_chg_id
          and tsc.policy_id = tcp.policy_id
          and tsc.item_id = tcp.item_id
          and tcp.item_id = tra.item_id
          and tcp.busi_item_id = tcbp.busi_item_id
          and tra.customer_id = #{customer_id}
          and tcp.liability_state in ('1', '4')
          and tpc.service_code = 'PA'
		  and tpc.change_flag ='1'
          and tra.amount_status <> 3 ]]>
		<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ and tcbp.BUSI_PROD_CODE IN]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
				index="index" open="(" close=")" separator=",">
				#{busi_prod_code_list}
			</foreach>
		</if>
        <![CDATA[ group by risk_type ]]>
	</select>
	
	<!-- 根据投保单号查询风险保额 -->
	<select id="findRiskAmountByApplyCode"  resultType="java.util.Map" parameterType="java.util.Map">
	      select rat.risk_type,rat.risk_amount from APP___PAS__DBUSER.t_risk_amount rat
	      where 1=1
	      and rat.apply_code = #{apply_code}
	      <![CDATA[ and rat.risk_type IN]]> 
	      <foreach collection="list" item="item"
				index="index" open="(" close=")" separator=",">
				#{item}
		  </foreach>
	</select>
	<!-- 删除操作 -->	
	<delete id="PA_deleteRiskAmountByItemId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RISK_AMOUNT A WHERE A.ITEM_ID=#{item_id} ]]>
	</delete>
	
	<select id="selectRiskAmountByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
	       SELECT 
	       A.LIST_ID,
	       A.CUSTOMER_ID,
	       A.POLICY_ID,
	       A.POLICY_CODE,
	       A.APPLY_CODE,
	       A.BUSI_ITEM_ID,
	       A.BUSI_PROD_CODE,
	       A.ITEM_ID,
	       A.INTERNAL_CODE,
	       A.RISK_TYPE,
	       A.AMOUNT_STATUS,
	       A.START_TIME,
	       A.END_TIME,
	       A.RISK_AMOUNT
	       FROM
	       APP___PAS__DBUSER.T_RISK_AMOUNT A  
	       WHERE A.APPLY_CODE=#{apply_code}
    </select>
    
    <!-- 根据客户号查询风险保额信息 -->
	<select id="PA_selectRiskAmountByCustomerID" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[  SELECT 
	       A.LIST_ID,
	       A.CUSTOMER_ID,
	       A.POLICY_ID,
	       A.POLICY_CODE,
	       A.APPLY_CODE,
	       A.BUSI_ITEM_ID,
	       A.BUSI_PROD_CODE,
	       A.ITEM_ID,
	       A.INTERNAL_CODE,
	       A.RISK_TYPE,
	       A.AMOUNT_STATUS,
	       A.START_TIME,
	       A.END_TIME,
	       A.RISK_AMOUNT
	       FROM
	       APP___PAS__DBUSER.T_RISK_AMOUNT A  
	       WHERE 1 = 1 
	       AND A.AMOUNT_STATUS != 3 
	       AND A.CUSTOMER_ID = #{customer_id}  ]]> 
		<if test="internalCodeList != null and internalCodeList.size()!=0"><![CDATA[ AND A.INTERNAL_CODE IN]]>
			<foreach collection="internalCodeList" item="internalCodeList"
				index="index" open="(" close=")" separator=",">
				#{internalCodeList}
			</foreach>
		</if>
    </select>
	
	<!-- 查询客户有效和失效保额 -->
	<select id="PA_riskAmountByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[	
		 	SELECT tr.risk_type, tr.amount_status
       FROM APP___PAS__DBUSER.T_RISK_AMOUNT TR
       LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
         ON TR.ITEM_ID = TC.ITEM_ID
	 where amount_status in ('1', '4')]]>
  	  <if test=" customer_id != null and customer_id != ''  "><![CDATA[ and tr.customer_id = #{customer_id}  ]]></if>
  	 group by tr.risk_type, tr.amount_status
 	</select>
 	
 	<!-- 查询风险保额数据表(T_RISK_AMOUNT)和险种责任组表获取保单下的责任组和对应的风险保额 -->
	<select id="findAllRiskAmountFor588BQ" resultType="java.util.Map" parameterType="java.util.Map">
		             SELECT CP.PRODUCT_CODE AS INTERNAL_CODE, RA.RISK_AMOUNT,RA.RISK_TYPE,RA.ITEM_ID
           FROM DEV_PAS.T_RISK_AMOUNT RA, DEV_PAS.T_CONTRACT_PRODUCT CP
          WHERE CP.ITEM_ID = RA.ITEM_ID
            AND RA.POLICY_CODE = #{policy_code}
 	</select>
	
</mapper>
