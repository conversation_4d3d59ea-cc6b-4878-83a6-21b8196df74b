<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IPolicyBonusDao">

<!-- 保单红利查询 -->
	<select id="findPolicyBoundsByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	select 
    A.POLICY_CODE,A.PRODUCT_CODE,A.AMOUNT,A.BONUS_SA,A.VALIDATE_DATE,A.ITEM_ID,A.CHARGE_YEAR,
    B.BUSI_PRD_ID,B.BUSI_PROD_CODE,
    C.PRODUCT_NAME,C.OPTION_TYPE,
    
   (select to_char(ALLOCATE_DATE,'yyyy') from  APP___PAS__DBUSER.T_CONTRACT_PRODUCT T1,APP___PAS__DBUSER.T_BONUS_ALLOCATE T2 WHERE T1.ITEM_ID=T2.ITEM_ID AND rownum=1) as ACOUNTINGYEAR,
    
   (select T2.BONUS_ALLOT from  APP___PAS__DBUSER.T_CONTRACT_PRODUCT T1,APP___PAS__DBUSER.T_BONUS_ALLOCATE T2 WHERE T1.ITEM_ID=T2.ITEM_ID AND rownum=1) as BONUS_ALLOT,
    
   (select T2.ALLOCATE_DATE from  APP___PAS__DBUSER.T_CONTRACT_PRODUCT T1,APP___PAS__DBUSER.T_BONUS_ALLOCATE T2 WHERE T1.ITEM_ID=T2.ITEM_ID AND rownum=1) as ALLOCATE_DATE,

   (select sum(AMOUNT) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT t group by t.policy_code having t.policy_code=#{POLICY_CODE}) as AMOUNTS,  
   (select to_char(count(*))
  from APP___PAS__DBUSER.T_BONUS_ALLOCATE T
 where T.ALLOCATE_DATE between
        (select to_date((select a.sss
                   from (select to_char(sysdate, 'YYYY') || '-01-01' as sss
                           from dual) a),
                 'yyyy-MM-dd')
          from dual )
          and  (select  sysdate from dual ) ) as BonusNo
 
  from APP___PAS__DBUSER.T_CONTRACT_PRODUCT  A
 
 LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B ON A.BUSI_ITEM_ID=B.BUSI_ITEM_ID
 
 LEFT JOIN APP___PAS__DBUSER.T_product_life C ON A.PRODUCT_ID=C.PRODUCT_ID
 
 WHERE A.POLICY_CODE=#{POLICY_CODE}
 </select>
</mapper>