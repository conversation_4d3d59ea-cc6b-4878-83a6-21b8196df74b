<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.dao.IClaimBfSurveyPlanDao">

	<sql id="claimBfSurveyPlanWhereCondition">
		<if test=" valid_flag  != null "><![CDATA[ AND A.VALID_FLAG = #{valid_flag} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND A.PLAN_NAME = #{plan_name} ]]></if>
		<if test=" insure_times  != null "><![CDATA[ AND A.INSURE_TIMES = #{insure_times} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimBfSurveyPlanByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimBfSurveyPlan"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="plan_id">
			SELECT APP___PAS__DBUSER.S_CLAIM_BF_SURVEY_PLAN__PLAN_I.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN(
				VALID_FLAG, PLAN_ID, END_DATE, INSERT_TIME, REMARK, UPDATE_TIME, START_DATE,IS_CLAIM,
				RISK_LEVEL,RISK_SCORE,CERTI_CODE,AGENT_CODE,ORGAN_CODE_AREA,ORGAN_CODE_STRY,ORGAN_CODE_GROUP,
				INSERT_TIMESTAMP, PLAN_NAME, UPDATE_BY, INSURE_TIMES, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{valid_flag, jdbcType=NUMERIC}, #{plan_id, jdbcType=NUMERIC} , 
				#{end_date, jdbcType=DATE} , SYSDATE , #{remark, jdbcType=VARCHAR} , SYSDATE , 
				#{start_date, jdbcType=DATE} ,#{is_claim, jdbcType=NUMERIC},#{risk_level, jdbcType=NUMERIC},#{risk_score, jdbcType=NUMERIC},
				  #{certi_code, jdbcType=VARCHAR},#{agent_code, jdbcType=VARCHAR},#{organ_code_area, jdbcType=VARCHAR},#{organ_code_stry, jdbcType=VARCHAR},
				  #{organ_code_group, jdbcType=VARCHAR},CURRENT_TIMESTAMP, #{plan_name, jdbcType=VARCHAR} , 
				#{update_by, jdbcType=NUMERIC} , #{insure_times, jdbcType=NUMERIC} ,
				 CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimBfSurveyPlan" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN WHERE PLAN_ID = #{plan_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimBfSurveyPlan" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN ]]>
		<set>
		<trim suffixOverrides=",">
		    VALID_FLAG = #{valid_flag, jdbcType=NUMERIC} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    START_DATE = #{start_date, jdbcType=DATE} ,
			PLAN_NAME = #{plan_name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    INSURE_TIMES = #{insure_times, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    IS_CLAIM = #{is_claim, jdbcType=NUMERIC} ,
		    RISK_LEVEL = #{risk_level, jdbcType=NUMERIC} ,
		    RISK_SCORE = #{risk_score, jdbcType=NUMERIC} ,
		    CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		    ORGAN_CODE_AREA = #{organ_code_area, jdbcType=VARCHAR} ,
		    ORGAN_CODE_STRY = #{organ_code_stry, jdbcType=VARCHAR} ,
		    ORGAN_CODE_GROUP = #{organ_code_group, jdbcType=VARCHAR} , 
		</trim>
		</set>
		<![CDATA[ WHERE PLAN_ID = #{plan_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimBfSurveyPlanByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.* FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryClaimBfSurveyPlanByPlanIdCondition" />
		<![CDATA[ ORDER BY A.PLAN_ID ]]>
	</select>
	
	
<!-- 按索引查询操作 -->	
	<select id="findSalesOrganByOrranCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.* FROM APP___CLM__DBUSER.t_Sales_Organ A WHERE 1 = 1  ]]>
		<if test=" sales_organ_code  != null "><![CDATA[ AND A.sales_organ_code = #{sales_organ_code} ]]></if>
		<if test=" organ_level_code  != null "><![CDATA[ AND A.organ_level_code = #{organ_level_code} ]]></if>
		<![CDATA[ ORDER BY A.sales_organ_code ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="findAllMapClaimBfSurveyPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.PLAN_ID, A.END_DATE, A.REMARK, A.START_DATE, 
			A.PLAN_NAME,A.IS_CLAIM,
				A.RISK_LEVEL,A.RISK_SCORE,A.CERTI_CODE,A.AGENT_CODE,A.ORGAN_CODE_AREA,
				A.ORGAN_CODE_STRY,A.ORGAN_CODE_GROUP,A.INSURE_TIMES FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.PLAN_ID ]]>
	</select>
<!-- 查询所有操作 -->
	<select id="findAllClaimBfSurveyPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.PLAN_ID, A.END_DATE, A.REMARK, A.START_DATE, 
			A.PLAN_NAME,A.IS_CLAIM,
				A.RISK_LEVEL,A.RISK_SCORE,A.CERTI_CODE,A.AGENT_CODE,A.ORGAN_CODE_AREA,
				A.ORGAN_CODE_STRY,A.ORGAN_CODE_GROUP, A.INSURE_TIMES FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimBfSurveyPlanWhereCondition" /> 
		<![CDATA[ ORDER BY A.PLAN_ID ]]> 
	</select>
	
	<select id="validateSurveyPlan" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[  SELECT A.VALID_FLAG, A.PLAN_ID, A.END_DATE, A.REMARK, A.START_DATE, 
			A.PLAN_NAME,A.IS_CLAIM,
				A.RISK_LEVEL,A.RISK_SCORE,A.CERTI_CODE,A.AGENT_CODE,A.ORGAN_CODE_AREA,
				A.ORGAN_CODE_STRY,A.ORGAN_CODE_GROUP, A.INSURE_TIMES FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<if test="plan_name != null and plan_name != '' ">
			<![CDATA[ AND A.plan_name = #{plan_name} ]]>
		</if>
		<if test="plan_id != null and plan_id != '' ">
			<![CDATA[ AND A.plan_id <> #{plan_id} ]]>
		</if>
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimBfSurveyPlanTotals" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(*)FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A,
       APP___PAS__DBUSER.T_YES_NO D
       where D.YES_NO = A.Valid_Flag]]>
		<include refid="queryClaimBfSurveyPlanForPageCondition3" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimBfSurveyPlanForPages" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.VALID_FLAG, B.PLAN_ID, B.END_DATE, B.REMARK, B.START_DATE, 
			B.PLAN_NAME, B.INSURE_TIMES, B.TYPE_NAME, B.UPDATE_TIME, B.UPDATE_BY,B.ORGAN_NAME,
			B.IS_CLAIM,B.RISK_LEVEL,B.RISK_SCORE,B.CERTI_CODE,B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.VALID_FLAG, A.PLAN_ID, A.END_DATE, A.REMARK, A.START_DATE, 
			A.PLAN_NAME, A.INSURE_TIMES, D.TYPE_NAME, trunc(A.UPDATE_TIME) UPDATE_TIME,
      (select C.REAL_NAME from APP___CLM__DBUSER.t_Udmp_User C where C.USER_ID = A.UPDATE_BY) UPDATE_BY,
      (select B.ORGAN_NAME from APP___CLM__DBUSER.T_udmp_ORG B,APP___CLM__DBUSER.t_Udmp_User C
       WHERE  C.USER_ID = A.UPDATE_BY and  B.ORGAN_ID = C.Organ_Id) ORGAN_NAME,A.IS_CLAIM,A.RISK_LEVEL,A.RISK_SCORE,A.CERTI_CODE,A.AGENT_CODE
       FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A,
			APP___PAS__DBUSER.T_YES_NO D
       where D.YES_NO = A.Valid_Flag
       and ROWNUM <= #{LESS_NUM}  ]]>
		<include refid="queryClaimBfSurveyPlanForPageCondition3" />
		<![CDATA[ ORDER BY A.PLAN_ID )  B 
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 查询个数操作org -->
	<select id="findClaimBfSurveyPlanOrgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(*)FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A,
       APP___PAS__DBUSER.T_YES_NO D,
        APP___clm__DBUSER.t_Udmp_Org C,
       APP___clm__DBUSER.t_Udmp_User E
       where D.YES_NO = A.Valid_Flag
       and A.Insert_By = E.User_Id 
       and E.ORGAN_CODE = C.ORGAN_CODE  ]]>
		<include refid="queryClaimBfSurveyPlanForPageCondition3" />
	</select>
	
	<!-- 分页查询操作org -->
	<select id="queryClaimBfSurveyPlanOrgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.VALID_FLAG, B.PLAN_ID, B.END_DATE, B.REMARK, B.START_DATE, 
			B.PLAN_NAME, B.INSURE_TIMES, B.TYPE_NAME, B.UPDATE_TIME, B.UPDATE_BY,B.ORGAN_NAME,
			B.IS_CLAIM,B.RISK_LEVEL,B.RISK_SCORE,B.CERTI_CODE,B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.VALID_FLAG, A.PLAN_ID, A.END_DATE, A.REMARK, A.START_DATE, 
			A.PLAN_NAME, A.INSURE_TIMES, D.TYPE_NAME, trunc(A.UPDATE_TIME) UPDATE_TIME,
      (select C.REAL_NAME from APP___CLM__DBUSER.t_Udmp_User C where C.USER_ID = A.UPDATE_BY) UPDATE_BY,
      (select B.ORGAN_NAME from APP___CLM__DBUSER.T_udmp_ORG B,APP___CLM__DBUSER.t_Udmp_User C
       WHERE  C.USER_ID = A.UPDATE_BY and  B.ORGAN_code = C.Organ_code) ORGAN_NAME,A.IS_CLAIM,A.RISK_LEVEL,A.RISK_SCORE,A.CERTI_CODE,A.AGENT_CODE
       FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A,
			APP___PAS__DBUSER.T_YES_NO D,
       		APP___clm__DBUSER.t_Udmp_Org C,
       		APP___clm__DBUSER.t_Udmp_User E
       where D.YES_NO = A.Valid_Flag
       and A.Insert_By = E.User_Id
       and E.ORGAN_CODE = C.ORGAN_CODE 
       and ROWNUM <= #{LESS_NUM}  ]]>
		<include refid="queryClaimBfSurveyPlanForPageCondition3" />
		<![CDATA[ ORDER BY A.PLAN_ID )  B 
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 查询个数操作 -->
	<select id="findSalesOrganTotals" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select count(1)from (select ROWNUM RN, A.SALES_ORGAN_CODE as SALES_ORGAN_CODE_A,
       A.SALES_ORGAN_NAME as SALES_ORGAN_NAME_A,
       B.SALES_ORGAN_CODE as SALES_ORGAN_CODE_B,
       B.SALES_ORGAN_NAME as SALES_ORGAN_NAME_B,
       C.SALES_ORGAN_CODE as SALES_ORGAN_CODE_C,
       C.SALES_ORGAN_NAME AS SALES_ORGAN_NAME_C
  from APP___CLM__DBUSER.t_Sales_Organ A
INNER JOIN APP___CLM__DBUSER.t_Sales_Organ B
    ON A.PARENT_CODE=B.SALES_ORGAN_CODE
   AND B.ORGAN_LEVEL_CODE='2'
   AND B.STATE=0  
INNER JOIN APP___CLM__DBUSER.t_Sales_Organ C
    ON B.PARENT_CODE=C.SALES_ORGAN_CODE
   AND C.ORGAN_LEVEL_CODE='1'
   AND C.STATE=0 
 where A.STATE = 0 AND A.ORGAN_LEVEL_CODE=3]]>
 		<if test=" sales_organ_namea != null and sales_organ_namea != ''"><![CDATA[ AND A.Sales_Organ_Name like '%${sales_organ_namea}%' ]]></if>
 		<if test=" sales_organ_nameb != null and sales_organ_nameb != ''"><![CDATA[ AND B.Sales_Organ_Name like '%${sales_organ_nameb}%' ]]></if>
 		<if test=" sales_organ_namec != null and sales_organ_namec != ''"><![CDATA[ AND C.Sales_Organ_Name like '%${sales_organ_namec}%' ]]></if>
 		<![CDATA[) B ]]></select>
	<!-- 分页查询操作 -->
	<select id="findSalesOrganForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.RN AS rowNumber,B.SALES_ORGAN_CODE_A,
		B.SALES_ORGAN_NAME_A,
		B.SALES_ORGAN_CODE_B,
		B.SALES_ORGAN_NAME_B,
		B.SALES_ORGAN_CODE_C,
		B.SALES_ORGAN_NAME_C from( select ROWNUM RN, A.SALES_ORGAN_CODE as SALES_ORGAN_CODE_A,
       A.SALES_ORGAN_NAME as SALES_ORGAN_NAME_A,
       B.SALES_ORGAN_CODE as SALES_ORGAN_CODE_B,
       B.SALES_ORGAN_NAME as SALES_ORGAN_NAME_B,
       C.SALES_ORGAN_CODE as SALES_ORGAN_CODE_C,
       C.SALES_ORGAN_NAME AS SALES_ORGAN_NAME_C
  from APP___CLM__DBUSER.t_Sales_Organ A
INNER JOIN APP___CLM__DBUSER.t_Sales_Organ B
    ON A.PARENT_CODE=B.SALES_ORGAN_CODE
   AND B.ORGAN_LEVEL_CODE='2'
   AND B.STATE=0  
INNER JOIN APP___CLM__DBUSER.t_Sales_Organ C
    ON B.PARENT_CODE=C.SALES_ORGAN_CODE
   AND C.ORGAN_LEVEL_CODE='1'
   AND C.STATE=0 
 where A.STATE = 0 AND A.ORGAN_LEVEL_CODE=3]]>
 		<if test=" sales_organ_namea != null and sales_organ_namea != ''"><![CDATA[ AND A.Sales_Organ_Name like '%${sales_organ_namea}%' ]]></if>
 		<if test=" sales_organ_nameb != null and sales_organ_nameb != ''"><![CDATA[ AND B.Sales_Organ_Name like '%${sales_organ_nameb}%' ]]></if>
 		<if test=" sales_organ_namec != null and sales_organ_namec != ''"><![CDATA[ AND C.Sales_Organ_Name like '%${sales_organ_namec}%' ]]></if>
 		<![CDATA[AND ROWNUM <= #{LESS_NUM}) B WHERE B.RN > #{GREATER_NUM}]]>
	</select>
	
	<sql id="queryClaimBfSurveyPlanForPageCondition1">
		<if test=" organ_code != null ">
			<![CDATA[ INNER JOIN 
      			 (SELECT DISTINCT t.plan_id FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_ORG t WHERE t.organ_code IN 
      			 (SELECT A.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A 
				 START WITH A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} 
				 CONNECT BY PRIOR A.ORGAN_ID = A.UPORGAN_ID)) D ON D.PLAN_ID = A.PLAN_ID ]]>
		</if>
	</sql>
	
	<sql id="queryClaimBfSurveyPlanForPageCondition3">
		<if test=" valid_flag != null "><![CDATA[ AND A.VALID_FLAG = #{valid_flag} ]]></if>
		<if test=" plan_name != null and plan_name != ''"><![CDATA[ AND A.PLAN_NAME like '%${plan_name}%'  ]]></if>
		<if test=" plan_id != null"><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" organ_code != null and organ_code != ''"><![CDATA[ AND C.ORGAN_CODE like '%${organ_code}%']]></if>
	</sql>
	
	
	<!-- 查询个数操作 -->
	<select id="findClaimBfSurveyPlanTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A ]]>
		<include refid="queryClaimBfSurveyPlanForPageCondition1" />
		<![CDATA[ WHERE 1 = 1 ]]> 
		<include refid="queryClaimBfSurveyPlanForPageCondition2" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimBfSurveyPlanForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.VALID_FLAG, B.PLAN_ID, B.END_DATE, B.REMARK, B.START_DATE, 
			B.PLAN_NAME, B.INSURE_TIMES, B.TYPE_NAME, B.UPDATE_TIME, B.UPDATE_BY,ORGAN_NAME,
			B.IS_CLAIM,B.RISK_LEVEL,B.RISK_SCORE,B.CERTI_CODE,B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.VALID_FLAG, A.PLAN_ID, A.END_DATE, A.REMARK, A.START_DATE, 
			A.PLAN_NAME, A.INSURE_TIMES, D.TYPE_NAME, trunc(A.UPDATE_TIME) UPDATE_TIME,
      (select C.REAL_NAME from APP___CLM__DBUSER.t_Udmp_User C where C.USER_ID = A.UPDATE_BY) UPDATE_BY,
      (select B.ORGAN_NAME from APP___CLM__DBUSER.T_udmp_ORG B,APP___CLM__DBUSER.t_Udmp_User C
       WHERE  C.USER_ID = A.UPDATE_BY and  B.ORGAN_code = C.Organ_code) ORGAN_NAME,A.IS_CLAIM,A.RISK_LEVEL,A.RISK_SCORE,A.CERTI_CODE,A.AGENT_CODE
       FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A
			INNER JOIN APP___PAS__DBUSER.T_YES_NO D ON D.YES_NO = A.VALID_FLAG  ]]>
		<include refid="queryClaimBfSurveyPlanForPageCondition1" />
		<![CDATA[ WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="queryClaimBfSurveyPlanForPageCondition2" />
		<![CDATA[ ORDER BY A.PLAN_ID )  B 
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findClaimBfSurveyByPlanName" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[ SELECT A.VALID_FLAG, A.PLAN_ID, A.END_DATE, A.REMARK, A.START_DATE, 
			A.PLAN_NAME, A.INSURE_TIMES FROM APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND A.PLAN_NAME = #{plan_name} ]]></if>
	</select>
	
	<sql id="queryClaimBfSurveyPlanForPageCondition2">
		<if test=" valid_flag != null "><![CDATA[ AND A.VALID_FLAG = #{valid_flag} ]]></if>
		<if test=" plan_name != null"><![CDATA[ AND A.PLAN_NAME like '%'||#{plan_name}||'%' ]]></if>
		<if test=" plan_id != null"><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>
	
</mapper>
