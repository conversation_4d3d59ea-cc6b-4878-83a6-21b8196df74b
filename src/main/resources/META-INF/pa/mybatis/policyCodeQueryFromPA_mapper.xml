<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyCodeQueryFromPADao">
	<select id="PA_policyCodeQueryFromPA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT L.POLICY_CODE cont_no, C.CUSTOMER_NAME ins_name,
      				CASE WHEN C.CUSTOMER_GENDER=1 THEN '0'
          				 WHEN C.CUSTOMER_GENDER=2 THEN '1' ELSE '2' END ins_sex_code,
      				CASE WHEN C.CUSTOMER_CERT_TYPE NOT IN ('0','1','2','3','4','5','8','9')
        				 THEN '8' ELSE C.CUSTOMER_CERT_TYPE END ins_id_type_code,
      				C.CUSTOMER_CERTI_CODE ins_id_no 
      				FROM DEV_PAS.T_CONTRACT_MEDICAL M,DEV_PAS.T_INSURED_LIST L,DEV_PAS.T_CUSTOMER C
      				WHERE M.APPLY_CODE=L.APPLY_CODE AND M.POLICY_CODE=L.POLICY_CODE 
      					AND L.CUSTOMER_ID=C.CUSTOMER_ID 
      					AND M.MEDICAL_ID = (SELECT MAX(B.MEDICAL_ID) FROM APP___PAS__DBUSER.T_CONTRACT_MEDICAL B WHERE B.POLICY_CODE=M.POLICY_CODE )
      					AND M.POLICY_SEQUENCE_NO = #{cont_no} ]]>
	</select>
</mapper>
