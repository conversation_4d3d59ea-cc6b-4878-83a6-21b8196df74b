<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IQueryInsuAccInfoDao">

    <sql id="queryInsuAccInfoByPolicyId">
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND E.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	
	<select id="PA_findqueryInsuAccInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT E.*,F.INTEREST_SUM,F.INVEST_ACCOUNT_TYPE,F.INTEREST_CAPITAL
FROM APP___PAS__DBUSER.T_CONTRACT_INVEST F,
(SELECT C.*,D.TRANS_CODE,D.TRANS_AMOUNT,D.DEAL_TIME
FROM APP___PAS__DBUSER.T_FUND_TRANS D,
(SELECT A.*,B.SETTLE_DATE, B.LAST_SETTLE_DATE,B.INTEREST,B.BALANCE,B.GURNT_INTEREST,B.INTEREST_RATE
FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT B,
(SELECT INVEST_ID,POLICY_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS GROUP BY INVEST_ID,POLICY_ID) A
 WHERE B.INVEST_ID=A.INVEST_ID )  C 
 WHERE C.POLICY_ID=D.POLICY_ID) E
 WHERE F.POLICY_ID=E.POLICY_ID
  AND E.SETTLE_DATE BETWEEN #{startDate} AND #{endDate} and  E.DEAL_TIME BETWEEN E.LAST_SETTLE_DATE AND E.SETTLE_DATE
 ]]>
 <include refid="queryInsuAccInfoByPolicyId"></include>
<![CDATA[ORDER BY E.INVEST_ID,E.POLICY_ID,E.SETTLE_DATE,E.INTEREST,E.BALANCE,E.GURNT_INTEREST,E.INTEREST_RATE,E.TRANS_CODE,E.TRANS_AMOUNT,F.INTEREST_SUM,F.INVEST_ACCOUNT_TYPE,F.INTEREST_CAPITAL ]]>		
	</select>
	
</mapper>
