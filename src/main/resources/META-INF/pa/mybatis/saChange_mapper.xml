<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ISaChangeDao">

	<sql id="saChangeWhereCondition">
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND <PERSON>.POLICY_ID = #{policy_id} ]]></if>
		<if test=" sa  != null "><![CDATA[ AND A.SA = #{sa} ]]></if>
		<if test=" start_date_between"><![CDATA[ AND A.START_DATE <= #{start_date_between} ]]></if>
		<if test=" start_date_after"><![CDATA[ AND A.START_DATE >= #{start_date_after} ]]></if>
		<if test=" after_date"><![CDATA[ AND A.START_DATE > #{after_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="querySaChangeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="querySaChangeByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="querySaChangeByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="querySaChangeByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSaChange"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_RENEW_REVERSAL_APPLY__APPLY.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SA_CHANGE(
				INSERT_TIMESTAMP, START_DATE, UPDATE_BY, INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, 
				POLICY_CHG_ID, ITEM_ID, INSERT_BY, POLICY_ID, SA ) 
			VALUES (
				CURRENT_TIMESTAMP, #{start_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE 
				, #{policy_chg_id, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{sa, jdbcType=NUMERIC} ) 
		  ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSaChange" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SA_CHANGE WHERE LIST_ID = #{list_id} ]]>
	</delete>
	<delete id="deletePaSaChange" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SA_CHANGE WHERE POLICY_CHG_ID IN (select pc.policy_chg_id
          from APP___PAS__DBUSER.t_policy_change pc
         where pc.policy_chg_id =#{policy_chg_id}
           and pc.service_code = 'PA' ) ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSaChange" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SA_CHANGE ]]>
		<set>
		<trim suffixOverrides=",">
			START_DATE = #{start_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SA = #{sa, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSaChangeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE 1 = 1  ]]>
		<include refid="querySaChangeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSaChangeByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE 1 = 1  ]]>
		<include refid="querySaChangeByItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSaChangeByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE 1 = 1  ]]>
		<include refid="querySaChangeByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSaChangeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE 1 = 1  ]]>
		<include refid="querySaChangeByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSaChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSaChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<include refid="saChangeWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSaChangeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="querySaChangeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.START_DATE, B.LIST_ID, 
			B.POLICY_CHG_ID, B.ITEM_ID, B.POLICY_ID, B.SA FROM (
					SELECT ROWNUM RN, A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findNewSaChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.START_DATE, B.LIST_ID, 
			B.POLICY_CHG_ID, B.ITEM_ID, B.POLICY_ID, B.SA FROM (
					SELECT A.START_DATE, A.LIST_ID, 
			A.POLICY_CHG_ID, A.ITEM_ID, A.POLICY_ID, A.SA FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE 1 =  1  ]]>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" start_date_after"><![CDATA[ AND A.START_DATE > #{start_date_after} ]]></if>
		<if test=" start_date_before"><![CDATA[ AND A.START_DATE < #{start_date_before} ]]></if>
		<![CDATA[ ORDER BY A.START_DATE DESC ) B WHERE ROWNUM=1]]> 
	</select>
	<!--计算现价时点所处的保单年度内累计新增加的保额 -->
	  <select id="sumSa" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(SUM(T.sa), 0) SUM_AMOUNT
            FROM APP___PAS__DBUSER.T_SA_CHANGE T
          WHERE t.item_id= #{item_id}  and t.policy_id=#{policy_id} ]]>
	</select>
	<!--计算现价时点所处的保单年度内累计新增加的保额 -->
	<select id="findTopUpSa" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(SUM(T.sa), 0) SUM_AMOUNT
            FROM APP___PAS__DBUSER.T_SA_CHANGE T
          WHERE t.item_id= #{item_id}
          and t.START_DATE >= #{start_date} 
          and t.START_DATE <= #{end_date} ]]>
	</select>
	
	<select id="findAllSaChangeSa" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT t.start_date 
            FROM APP___PAS__DBUSER.T_SA_CHANGE T
          WHERE t.item_id= #{item_id}
          and t.START_DATE >= #{start_date} 
          and t.START_DATE <= #{end_date} ]]>
	</select>
	
	<!-- 查询做过加保的 -->
		<select id="findAllSaChangeSaPA" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT CP.BUSI_ITEM_ID,PC.POLICY_CHG_ID,PC.VALIDATE_TIME START_DATE,SUM(NVL(CP1.STD_PREM_AF,0)-NVL(CP.STD_PREM_AF,0)) SA 
		     FROM  APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC               
        LEFT  JOIN APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CP ON CP.POLICY_CHG_ID=PC.POLICY_CHG_ID AND CP.OPERATION_TYPE='0'
        LEFT  JOIN APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CP1 ON CP1.POLICY_CHG_ID=PC.POLICY_CHG_ID AND CP1.OPERATION_TYPE IN('1','2')
        WHERE PC.SERVICE_CODE='PA' AND PC.VALIDATE_TIME IS NOT NULL ]]>
	   <if test=" start_date  != null "><![CDATA[
	    AND PC.VALIDATE_TIME >= #{start_date}
	    AND EXISTS(SELECT 1 FROM DEV_PAS.T_SA_CHANGE A WHERE A.ITEM_ID=CP.ITEM_ID AND A.START_DATE>= #{start_date})
	   ]]></if>
       <if test=" policy_id  != null "><![CDATA[ AND PC.POLICY_ID = #{policy_id}]]></if>
       <if test=" busi_item_id  != null "><![CDATA[ AND CP.busi_item_id = #{busi_item_id} AND CP1.busi_item_id = #{busi_item_id} ]]></if>
		       GROUP BY CP.BUSI_ITEM_ID,PC.POLICY_CHG_ID,PC.VALIDATE_TIME
		</select>
 	<!-- 查询保单某时间之后做的Sa之和 -->
 	<select id="findSumSaChangeByStartDate" resultType="java.util.Map" parameterType="java.util.Map" >
 			<![CDATA[ 
 			   SELECT sum(SA) as sumsa
 			     FROM APP___PAS__DBUSER.t_Sa_Change a where 1 = 1
 			 ]]>
 			 <if test=" policy_id  != null "><![CDATA[AND A.POLICY_ID = #{policy_id}]]></if>
 			 <if test=" start_date  != null "><![CDATA[AND A.START_DATE <= #{start_date}]]></if>
 			 <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
 	</select>
 	
 	<select id="PA_findSaChangeByitemId" resultType="java.util.Map" parameterType="java.util.Map" >
 			<![CDATA[ 
 			   SELECT A.LIST_ID,
				       A.ITEM_ID,
				       A.POLICY_ID,
				       A.POLICY_CHG_ID,
				       A.START_DATE,
				       A.SA,
				       A.INSERT_BY,
				       A.INSERT_TIME,
				       A.INSERT_TIMESTAMP,
				       A.UPDATE_BY,
				       A.UPDATE_TIME,
				       A.UPDATE_TIMESTAMP
            FROM APP___PAS__DBUSER.T_SA_CHANGE A WHERE 1 = 1  and ROWNUM = 1
 			 ]]>
 			 <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
 	</select>
 	
</mapper>
