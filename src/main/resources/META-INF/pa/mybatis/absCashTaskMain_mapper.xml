<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAbsCashTaskMainDao">

	<sql id="absCashTaskMainWhereCondition">
		<if test=" busi_item_num  != null "><![CDATA[ AND A.BUSI_ITEM_NUM = #{busi_item_num} ]]></if>
		<if test=" loancas_date  != null  and  loancas_date  != ''  "><![CDATA[ AND A.LOANCAS_DATE = #{loancas_date} ]]></if>
		<if test=" plan_code != null and plan_code != ''  "><![CDATA[ AND A.PLAN_CODE = #{plan_code} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" file_name != null and file_name != ''  "><![CDATA[ AND A.FILE_NAME = #{file_name} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" url != null and url != ''  "><![CDATA[ AND A.URL = #{url} ]]></if>
	</sql>

	<!-- 按索引生成的查询条件 -->	
	<sql id="queryAbsCashTaskMainByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="addAbsCashTaskMain"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ABS_CASH_MAIN__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN(
				BUSI_ITEM_NUM, LOANCAS_DATE, INSERT_TIME, PLAN_CODE, UPDATE_TIME, STATUS, FILE_NAME, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, URL, INSERT_BY ) 
			VALUES (
				#{busi_item_num, jdbcType=NUMERIC}, #{loancas_date, jdbcType=DATE} , SYSDATE , #{plan_code, jdbcType=VARCHAR} , SYSDATE , #{status, jdbcType=VARCHAR} , #{file_name, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{url, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="deleteAbsCashTaskMain" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="updateAbsCashTaskMain" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN ]]>
		<set>
		<trim suffixOverrides=",">
		    BUSI_ITEM_NUM = #{busi_item_num, jdbcType=NUMERIC} ,
		    LOANCAS_DATE = #{loancas_date, jdbcType=DATE} ,
			PLAN_CODE = #{plan_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			STATUS = #{status, jdbcType=VARCHAR} ,
			FILE_NAME = #{file_name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			URL = #{url, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="findAbsCashTaskMainByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_ITEM_NUM, A.LOANCAS_DATE, A.PLAN_CODE, A.STATUS, A.FILE_NAME, 
			A.LIST_ID, A.URL FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN A WHERE 1 = 1  ]]>
		<include refid="queryAbsCashTaskMainByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

	<!-- 按map查询操作 -->
	<select id="findAllMapAbsCashTaskMain" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_ITEM_NUM, A.LOANCAS_DATE, A.PLAN_CODE, A.STATUS, A.FILE_NAME, 
			A.LIST_ID, A.URL FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN A WHERE ROWNUM <=  1000  ]]>
		<include refid="absCashTaskMainWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllAbsCashTaskMain" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_ITEM_NUM, A.LOANCAS_DATE, A.PLAN_CODE, A.STATUS, A.FILE_NAME, 
			A.LIST_ID, A.URL FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN A WHERE ROWNUM <=  1000  ]]>
		<include refid="absCashTaskMainWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

	<!-- 查询个数操作 -->
	<select id="findAbsCashTaskMainTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN A WHERE 1 = 1  ]]>
		<include refid="absCashTaskMainWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="queryAbsCashTaskMainForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSI_ITEM_NUM, B.LOANCAS_DATE, B.PLAN_CODE, B.STATUS, B.FILE_NAME, 
			B.LIST_ID, B.URL FROM (
					SELECT ROWNUM RN, A.BUSI_ITEM_NUM, A.LOANCAS_DATE, A.PLAN_CODE, A.STATUS, A.FILE_NAME, 
			A.LIST_ID, A.URL FROM APP___PAS__DBUSER.APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="absCashTaskMainWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findAllUntreatedAbsCashTaskMain" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.*, ROWNUM RN FROM (
					 SELECT A.BUSI_ITEM_NUM, A.LOANCAS_DATE, A.PLAN_CODE, A.STATUS, A.FILE_NAME, 
							 A.LIST_ID, A.URL FROM APP___PAS__DBUSER.T_ABS_CASH_TASK_MAIN A 
							 WHERE 1 = 1 AND A.STATUS = 0 ) T WHERE 1=1]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.LIST_ID , #{modnum}) = #{start} ]]></if>
	</select>

</mapper>
