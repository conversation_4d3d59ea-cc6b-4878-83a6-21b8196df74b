<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyAccountLogDao">

<sql id="PA_policyAccountLogWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" capital_balance  != null "><![CDATA[ AND A.CAPITAL_BALANCE = #{capital_balance} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_type  != null "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
		<if test=" regular_repay  != null "><![CDATA[ AND A.REGULAR_REPAY = #{regular_repay} ]]></if>
		<if test=" frozen_amount  != null "><![CDATA[ AND A.FROZEN_AMOUNT = #{frozen_amount} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_account_status  != null "><![CDATA[ AND A.POLICY_ACCOUNT_STATUS = #{policy_account_status} ]]></if>
		<if test=" next_balance_date  != null  and  next_balance_date  != ''  "><![CDATA[ AND A.NEXT_BALANCE_DATE = #{next_balance_date} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" capitalized_date  != null  and  capitalized_date  != ''  "><![CDATA[ AND A.CAPITALIZED_DATE = #{capitalized_date} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" balance_date  != null  and  balance_date  != ''  "><![CDATA[ AND A.BALANCE_DATE = #{balance_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" create_date  != null  and  create_date  != ''  "><![CDATA[ AND A.CREATE_DATE = #{create_date} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
	</sql>
<!-- -->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyAccountLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyAccountLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="log_id">
            SELECT APP___PAS__DBUSER.S_POLICY_ACCOUNT_LOG__Log_ID.nextval from dual
        </selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG(
				MONEY_CODE, ACCOUNT_ID, CAPITAL_BALANCE, ITEM_ID, ACCOUNT_TYPE, REGULAR_REPAY, INSERT_TIMESTAMP, 
				FROZEN_AMOUNT, UPDATE_BY, INTEREST_BALANCE, POLICY_CHG_ID, BUSI_ITEM_ID, POLICY_ID, 
				POLICY_ACCOUNT_STATUS, NEXT_BALANCE_DATE, INTEREST_CAPITAL, CAPITALIZED_DATE, INSERT_TIME, INTEREST_SUM, UPDATE_TIME, 
				BALANCE_DATE, LOG_ID, CREATE_DATE, LOG_TYPE, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{money_code, jdbcType=VARCHAR}, #{account_id, jdbcType=NUMERIC} , #{capital_balance, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , #{account_type, jdbcType=NUMERIC} , #{regular_repay, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{frozen_amount, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{interest_balance, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{policy_account_status, jdbcType=NUMERIC} , #{next_balance_date, jdbcType=DATE} , #{interest_capital, jdbcType=NUMERIC} , #{capitalized_date, jdbcType=DATE} , SYSDATE , #{interest_sum, jdbcType=NUMERIC} , SYSDATE 
				, #{balance_date, jdbcType=DATE} , #{log_id, jdbcType=NUMERIC} , #{create_date, jdbcType=DATE} , #{log_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyAccountLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyAccountLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
		    CAPITAL_BALANCE = #{capital_balance, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    ACCOUNT_TYPE = #{account_type, jdbcType=NUMERIC} ,
		    REGULAR_REPAY = #{regular_repay, jdbcType=NUMERIC} ,
		    FROZEN_AMOUNT = #{frozen_amount, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    POLICY_ACCOUNT_STATUS = #{policy_account_status, jdbcType=NUMERIC} ,
		    NEXT_BALANCE_DATE = #{next_balance_date, jdbcType=DATE} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    CAPITALIZED_DATE = #{capitalized_date, jdbcType=DATE} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    BALANCE_DATE = #{balance_date, jdbcType=DATE} ,
		    CREATE_DATE = #{create_date, jdbcType=DATE} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyAccountLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.ITEM_ID, A.ACCOUNT_TYPE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.POLICY_ACCOUNT_STATUS, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.INTEREST_SUM, 
			A.BALANCE_DATE, A.LOG_ID, A.CREATE_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyAccountLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.ITEM_ID, A.ACCOUNT_TYPE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.POLICY_ACCOUNT_STATUS, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.INTEREST_SUM, 
			A.BALANCE_DATE, A.LOG_ID, A.CREATE_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG A WHERE ROWNUM <=  1000   ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyAccountLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.ITEM_ID, A.ACCOUNT_TYPE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.POLICY_ACCOUNT_STATUS, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.INTEREST_SUM, 
			A.BALANCE_DATE, A.LOG_ID, A.CREATE_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG A WHERE ROWNUM <=  1000   ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyAccountLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyAccountLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MONEY_CODE, B.ACCOUNT_ID, B.CAPITAL_BALANCE, B.ITEM_ID, B.ACCOUNT_TYPE, B.REGULAR_REPAY, 
			B.FROZEN_AMOUNT, B.INTEREST_BALANCE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID, 
			B.POLICY_ACCOUNT_STATUS, B.NEXT_BALANCE_DATE, B.INTEREST_CAPITAL, B.CAPITALIZED_DATE, B.INTEREST_SUM, 
			B.BALANCE_DATE, B.LOG_ID, B.CREATE_DATE, B.LOG_TYPE FROM (
					SELECT ROWNUM RN, A.MONEY_CODE, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.ITEM_ID, A.ACCOUNT_TYPE, A.REGULAR_REPAY, 
			A.FROZEN_AMOUNT, A.INTEREST_BALANCE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.POLICY_ACCOUNT_STATUS, A.NEXT_BALANCE_DATE, A.INTEREST_CAPITAL, A.CAPITALIZED_DATE, A.INTEREST_SUM, 
			A.BALANCE_DATE, A.LOG_ID, A.CREATE_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG A WHERE ROWNUM <= #{LESS_NUM}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 根据保单id和时间区间查询账户信息 -->
	<select id="PA_findAllByPolicyIdBetweenDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select * from APP___PAS__DBUSER.T_POLICY_ACCOUNT_LOG 
			where POLICY_ID=#{policy_id} AND BALANCE_DATE between 
			#{start_date} and #{end_date}
		]]>
	</select>
</mapper>
