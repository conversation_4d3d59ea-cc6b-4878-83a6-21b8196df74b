<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISecondPolicyHolderLogDao">

	<sql id="secondPolicyHolderLogWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" resident_type != null and resident_type != ''  "><![CDATA[ AND A.RESIDENT_TYPE = #{resident_type} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" annual_income_ceil  != null "><![CDATA[ AND A.ANNUAL_INCOME_CEIL = #{annual_income_ceil} ]]></if>
		<if test=" income_source != null and income_source != ''  "><![CDATA[ AND A.INCOME_SOURCE = #{income_source} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND A.RELATION_TO_INSURED_1 = #{relation_to_insured_1} ]]></if>
		<if test=" comm_method != null and comm_method != ''  "><![CDATA[ AND A.COMM_METHOD = #{comm_method} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND A.SOCI_SECU = #{soci_secu} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="querySecondPolicyHolderLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderLogByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderLogByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderLogByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="querySecondPolicyHolderLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSecondPolicyHolderLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT S_SE_POL_HOLDER_LOG__LISTID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_SECOND_POLICY_HOLDER_LOG(
				ADDRESS_ID, RESIDENT_TYPE, CUSTOMER_HEIGHT, CUSTOMER_ID, APPLY_CODE, ANNUAL_INCOME_CEIL, INCOME_SOURCE, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, POLICY_CHG_ID, POLICY_ID, SMOKING, RELATION_TO_INSURED_1, 
				INSERT_TIME, COMM_METHOD, JOB_CODE, UPDATE_TIME, CUSTOMER_WEIGHT, SOCI_SECU, LOG_ID, 
				POLICY_CODE, UPDATE_TIMESTAMP, LOG_TYPE, INSERT_BY ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{resident_type, jdbcType=VARCHAR} , #{customer_height, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{annual_income_ceil, jdbcType=NUMERIC} , #{income_source, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{smoking, jdbcType=NUMERIC} , #{relation_to_insured_1, jdbcType=VARCHAR} 
				, SYSDATE , #{comm_method, jdbcType=VARCHAR} , #{job_code, jdbcType=VARCHAR} , SYSDATE , #{customer_weight, jdbcType=NUMERIC} , #{soci_secu, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} 
				, #{policy_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSecondPolicyHolderLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_SECOND_POLICY_HOLDER_LOG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSecondPolicyHolderLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_SECOND_POLICY_HOLDER_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
			RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC} ,
			INCOME_SOURCE = #{income_source, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SMOKING = #{smoking, jdbcType=NUMERIC} ,
			RELATION_TO_INSURED_1 = #{relation_to_insured_1, jdbcType=VARCHAR} ,
			COMM_METHOD = #{comm_method, jdbcType=VARCHAR} ,
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSecondPolicyHolderLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderLogByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderLogByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderLogByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderLogByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderLogByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderLogByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderLogByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSecondPolicyHolderLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="querySecondPolicyHolderLogByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSecondPolicyHolderLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="secondPolicyHolderLogWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSecondPolicyHolderLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="secondPolicyHolderLogWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSecondPolicyHolderLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_SECOND_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="secondPolicyHolderLogWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="querySecondPolicyHolderLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.RESIDENT_TYPE, B.CUSTOMER_HEIGHT, B.CUSTOMER_ID, B.APPLY_CODE, B.ANNUAL_INCOME_CEIL, B.INCOME_SOURCE, 
			B.LIST_ID, B.POLICY_CHG_ID, B.POLICY_ID, B.SMOKING, B.RELATION_TO_INSURED_1, 
			B.COMM_METHOD, B.JOB_CODE, B.CUSTOMER_WEIGHT, B.SOCI_SECU, B.LOG_ID, 
			B.POLICY_CODE, B.LOG_TYPE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.RESIDENT_TYPE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.APPLY_CODE, A.ANNUAL_INCOME_CEIL, A.INCOME_SOURCE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.SMOKING, A.RELATION_TO_INSURED_1, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.LOG_ID, 
			A.POLICY_CODE, A.LOG_TYPE FROM T_SECOND_POLICY_HOLDER_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="secondPolicyHolderLogWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
