<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayPlanPayeeDao">

	<sql id="payPlanPayeeWhereCondition">
		<if test=" payee_address_id  != null "><![CDATA[ AND A.PAYEE_ADDRESS_ID = #{payee_address_id} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" payee_account_id  != null "><![CDATA[ AND A.PAYEE_ACCOUNT_ID = #{payee_account_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" payee_rate  != null "><![CDATA[ AND A.PAYEE_RATE = #{payee_rate} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryPayPlanPayeeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryPayPlanPayeeByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="queryPayPlanPayeeByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	
	<sql id="queryPayPlanPayeeByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="queryPayPlanPayeeByPayeeAccountIdCondition">
		<if test=" payee_account_id  != null "><![CDATA[ AND A.PAYEE_ACCOUNT_ID = #{payee_account_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPayPlanPayee"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PAY_PLAN_PAYEE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_PLAN_PAYEE(
				PAYEE_ADDRESS_ID, CUSTOMER_NAME, PLAN_ID, INSERT_TIME, CUSTOMER_ID, ITEM_ID, UPDATE_TIME, 
				INSERT_TIMESTAMP, PAY_MODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, PAYEE_ACCOUNT_ID, BUSI_ITEM_ID, 
				INSERT_BY, POLICY_ID, PAYEE_RATE ) 
			VALUES (
				#{payee_address_id, jdbcType=NUMERIC}, #{customer_name, jdbcType=VARCHAR} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , SYSDATE 
				, CURRENT_TIMESTAMP, #{pay_mode, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{payee_account_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{payee_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePayPlanPayee" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 =1  ]]>
		<include refid="payPlanPayeeWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="updatePayPlanPayee" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN_PAYEE ]]>
		<set>
		<trim suffixOverrides=",">
		    PAYEE_ADDRESS_ID = #{payee_address_id, jdbcType=NUMERIC} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    PAYEE_ACCOUNT_ID = #{payee_account_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PAYEE_RATE = #{payee_rate, jdbcType=NUMERIC} ,
		    <if test=" busi_item_id  != null and busi_item_id !='' "><![CDATA[  BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} , ]]></if>	
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPayPlanPayeeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanPayeeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findPayPlanPayeeByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanPayeeByItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findPayPlanPayeeByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanPayeeByPlanIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findPayPlanPayeeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanPayeeByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findPayPlanPayeeByPayeeAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 = 1  ]]>
		<include refid="queryPayPlanPayeeByPayeeAccountIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findPayPlanPayee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 = 1  ]]>
		<include refid="payPlanPayeeWhereCondition" />
   </select>
	
<!-- 按map查询操作 -->
	<select id="findAllMapPayPlanPayee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPayPlanPayee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE ROWNUM <=  1000  ]]>
		<include refid="payPlanPayeeWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findPayPlanPayeeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryPayPlanPayeeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PAYEE_ADDRESS_ID, B.CUSTOMER_NAME, B.PLAN_ID, B.CUSTOMER_ID, B.ITEM_ID, 
			B.PAY_MODE, B.LIST_ID, B.PAYEE_ACCOUNT_ID, B.BUSI_ITEM_ID, 
			B.POLICY_ID, B.PAYEE_RATE FROM (
					SELECT ROWNUM RN, A.PAYEE_ADDRESS_ID, A.CUSTOMER_NAME, A.PLAN_ID, A.CUSTOMER_ID, A.ITEM_ID, 
			A.PAY_MODE, A.LIST_ID, A.PAYEE_ACCOUNT_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PAYEE_RATE FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
