<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICommonTaskDao">

	<sql id="commonTaskWhereCondition">
		<if test=" ext_val7 != null and ext_val7 != ''  "><![CDATA[ AND A.EXT_VAL7 = #{ext_val7} ]]></if>
		<if test=" ext_val10 != null and ext_val10 != ''  "><![CDATA[ AND A.EXT_VAL10 = #{ext_val10} ]]></if>
		<if test=" ext_val8 != null and ext_val8 != ''  "><![CDATA[ AND A.EXT_VAL8 = #{ext_val8} ]]></if>
		<if test=" ext_val11 != null and ext_val11 != ''  "><![CDATA[ AND A.EXT_VAL11 = #{ext_val11} ]]></if>
		<if test=" ext_val9 != null and ext_val9 != ''  "><![CDATA[ AND A.EXT_VAL9 = #{ext_val9} ]]></if>
		<if test=" ext_val3 != null and ext_val3 != ''  "><![CDATA[ AND A.EXT_VAL3 = #{ext_val3} ]]></if>
		<if test=" ext_val14 != null and ext_val14 != ''  "><![CDATA[ AND A.EXT_VAL14 = #{ext_val14} ]]></if>
		<if test=" ext_val4 != null and ext_val4 != ''  "><![CDATA[ AND A.EXT_VAL4 = #{ext_val4} ]]></if>
		<if test=" ext_val15 != null and ext_val15 != ''  "><![CDATA[ AND A.EXT_VAL15 = #{ext_val15} ]]></if>
		<if test=" ext_val5 != null and ext_val5 != ''  "><![CDATA[ AND A.EXT_VAL5 = #{ext_val5} ]]></if>
		<if test=" ext_val12 != null and ext_val12 != ''  "><![CDATA[ AND A.EXT_VAL12 = #{ext_val12} ]]></if>
		<if test=" ext_val6 != null and ext_val6 != ''  "><![CDATA[ AND A.EXT_VAL6 = #{ext_val6} ]]></if>
		<if test=" ext_val13 != null and ext_val13 != ''  "><![CDATA[ AND A.EXT_VAL13 = #{ext_val13} ]]></if>
		<if test=" common_task_status != null and common_task_status != ''  "><![CDATA[ AND A.COMMON_TASK_STATUS = #{common_task_status} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" task_type != null and task_type != ''  "><![CDATA[ AND A.TASK_TYPE = #{task_type} ]]></if>
		<if test=" ext_val0 != null and ext_val0 != ''  "><![CDATA[ AND A.EXT_VAL0 = #{ext_val0} ]]></if>
		<if test=" ext_val2 != null and ext_val2 != ''  "><![CDATA[ AND A.EXT_VAL2 = #{ext_val2} ]]></if>
		<if test=" ext_val1 != null and ext_val1 != ''  "><![CDATA[ AND A.EXT_VAL1 = #{ext_val1} ]]></if>
		<if test=" common_task_delete_flag  != null "><![CDATA[ AND A.COMMON_TASK_DELETE_FLAG = #{common_task_delete_flag} ]]></if>
		<if test=" task_create_time  != null  and  task_create_time  != ''  "><![CDATA[ AND A.TASK_CREATE_TIME = #{task_create_time} ]]></if>
		<if test=" bussiness_code != null and bussiness_code != ''  "><![CDATA[ AND A.BUSSINESS_CODE = #{bussiness_code} ]]></if>
		<if test=" pln_resp_time  != null  and  pln_resp_time  != ''  "><![CDATA[ AND A.PLN_RESP_TIME = #{pln_resp_time} ]]></if>
		<if test=" resp_time  != null  and  resp_time  != ''  "><![CDATA[ AND A.RESP_TIME = #{resp_time} ]]></if>
		<if test=" common_task_id  != null "><![CDATA[ AND A.COMMON_TASK_ID = #{common_task_id} ]]></if>
		<if test=" sub_id != null and sub_id != ''  "><![CDATA[ AND A.SUB_ID = #{sub_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCommonTaskByCommonTaskIdCondition">
		<if test=" common_task_id  != null "><![CDATA[ AND A.COMMON_TASK_ID = #{common_task_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCommonTask"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_COMMON_TASK(
				EXT_VAL7, EXT_VAL10, EXT_VAL8, EXT_VAL11, EXT_VAL9, EXT_VAL3, EXT_VAL14, 
				EXT_VAL4, EXT_VAL15, EXT_VAL5, EXT_VAL12, EXT_VAL6, EXT_VAL13, COMMON_TASK_STATUS, 
				INSERT_TIMESTAMP, ORGAN_CODE, UPDATE_BY, TASK_TYPE, EXT_VAL0, EXT_VAL2, EXT_VAL1, 
				COMMON_TASK_DELETE_FLAG, TASK_CREATE_TIME, BUSSINESS_CODE, INSERT_TIME, UPDATE_TIME, PLN_RESP_TIME, RESP_TIME, 
				UPDATE_TIMESTAMP, INSERT_BY, COMMON_TASK_ID, SUB_ID ) 
			VALUES (
				#{ext_val7, jdbcType=VARCHAR}, #{ext_val10, jdbcType=VARCHAR} , #{ext_val8, jdbcType=VARCHAR} , #{ext_val11, jdbcType=VARCHAR} , #{ext_val9, jdbcType=VARCHAR} , #{ext_val3, jdbcType=VARCHAR} , #{ext_val14, jdbcType=VARCHAR} 
				, #{ext_val4, jdbcType=VARCHAR} , #{ext_val15, jdbcType=VARCHAR} , #{ext_val5, jdbcType=VARCHAR} , #{ext_val12, jdbcType=VARCHAR} , #{ext_val6, jdbcType=VARCHAR} , #{ext_val13, jdbcType=VARCHAR} , #{common_task_status, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{task_type, jdbcType=VARCHAR} , #{ext_val0, jdbcType=VARCHAR} , #{ext_val2, jdbcType=VARCHAR} , #{ext_val1, jdbcType=VARCHAR} 
				, #{common_task_delete_flag, jdbcType=NUMERIC} , #{task_create_time, jdbcType=DATE} , #{bussiness_code, jdbcType=VARCHAR} , SYSDATE , SYSDATE , #{pln_resp_time, jdbcType=DATE} , #{resp_time, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{common_task_id, jdbcType=NUMERIC} , #{sub_id, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCommonTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_COMMON_TASK WHERE COMMON_TASK_ID = #{common_task_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCommonTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_COMMON_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			EXT_VAL7 = #{ext_val7, jdbcType=VARCHAR} ,
			EXT_VAL10 = #{ext_val10, jdbcType=VARCHAR} ,
			EXT_VAL8 = #{ext_val8, jdbcType=VARCHAR} ,
			EXT_VAL11 = #{ext_val11, jdbcType=VARCHAR} ,
			EXT_VAL9 = #{ext_val9, jdbcType=VARCHAR} ,
			EXT_VAL3 = #{ext_val3, jdbcType=VARCHAR} ,
			EXT_VAL14 = #{ext_val14, jdbcType=VARCHAR} ,
			EXT_VAL4 = #{ext_val4, jdbcType=VARCHAR} ,
			EXT_VAL15 = #{ext_val15, jdbcType=VARCHAR} ,
			EXT_VAL5 = #{ext_val5, jdbcType=VARCHAR} ,
			EXT_VAL12 = #{ext_val12, jdbcType=VARCHAR} ,
			EXT_VAL6 = #{ext_val6, jdbcType=VARCHAR} ,
			EXT_VAL13 = #{ext_val13, jdbcType=VARCHAR} ,
			COMMON_TASK_STATUS = #{common_task_status, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			TASK_TYPE = #{task_type, jdbcType=VARCHAR} ,
			EXT_VAL0 = #{ext_val0, jdbcType=VARCHAR} ,
			EXT_VAL2 = #{ext_val2, jdbcType=VARCHAR} ,
			EXT_VAL1 = #{ext_val1, jdbcType=VARCHAR} ,
		    COMMON_TASK_DELETE_FLAG = #{common_task_delete_flag, jdbcType=NUMERIC} ,
		    TASK_CREATE_TIME = #{task_create_time, jdbcType=DATE} ,
			BUSSINESS_CODE = #{bussiness_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    PLN_RESP_TIME = #{pln_resp_time, jdbcType=DATE} ,
		    RESP_TIME = #{resp_time, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			SUB_ID = #{sub_id, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE COMMON_TASK_ID = #{common_task_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCommonTaskByCommonTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXT_VAL7, A.EXT_VAL10, A.EXT_VAL8, A.EXT_VAL11, A.EXT_VAL9, A.EXT_VAL3, A.EXT_VAL14, 
			A.EXT_VAL4, A.EXT_VAL15, A.EXT_VAL5, A.EXT_VAL12, A.EXT_VAL6, A.EXT_VAL13, A.COMMON_TASK_STATUS, 
			A.ORGAN_CODE, A.TASK_TYPE, A.EXT_VAL0, A.EXT_VAL2, A.EXT_VAL1, 
			A.COMMON_TASK_DELETE_FLAG, A.TASK_CREATE_TIME, A.BUSSINESS_CODE, A.PLN_RESP_TIME, A.RESP_TIME, 
			A.COMMON_TASK_ID, A.SUB_ID FROM APP___PAS__DBUSER.T_COMMON_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCommonTaskByCommonTaskIdCondition" />
		<![CDATA[ ORDER BY A.COMMON_TASK_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCommonTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXT_VAL7, A.EXT_VAL10, A.EXT_VAL8, A.EXT_VAL11, A.EXT_VAL9, A.EXT_VAL3, A.EXT_VAL14, 
			A.EXT_VAL4, A.EXT_VAL15, A.EXT_VAL5, A.EXT_VAL12, A.EXT_VAL6, A.EXT_VAL13, A.COMMON_TASK_STATUS, 
			A.ORGAN_CODE, A.TASK_TYPE, A.EXT_VAL0, A.EXT_VAL2, A.EXT_VAL1, 
			A.COMMON_TASK_DELETE_FLAG, A.TASK_CREATE_TIME, A.BUSSINESS_CODE, A.PLN_RESP_TIME, A.RESP_TIME, 
			A.COMMON_TASK_ID, A.SUB_ID FROM APP___PAS__DBUSER.T_COMMON_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.COMMON_TASK_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllCommonTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXT_VAL7, A.EXT_VAL10, A.EXT_VAL8, A.EXT_VAL11, A.EXT_VAL9, A.EXT_VAL3, A.EXT_VAL14, 
			A.EXT_VAL4, A.EXT_VAL15, A.EXT_VAL5, A.EXT_VAL12, A.EXT_VAL6, A.EXT_VAL13, A.COMMON_TASK_STATUS, 
			A.ORGAN_CODE, A.TASK_TYPE, A.EXT_VAL0, A.EXT_VAL2, A.EXT_VAL1, 
			A.COMMON_TASK_DELETE_FLAG, A.TASK_CREATE_TIME, A.BUSSINESS_CODE, A.PLN_RESP_TIME, A.RESP_TIME, 
			A.COMMON_TASK_ID, A.SUB_ID FROM APP___PAS__DBUSER.T_COMMON_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="commonTaskWhereCondition" />
		<![CDATA[ ORDER BY A.COMMON_TASK_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCommonTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_COMMON_TASK A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCommonTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.EXT_VAL7, B.EXT_VAL10, B.EXT_VAL8, B.EXT_VAL11, B.EXT_VAL9, B.EXT_VAL3, B.EXT_VAL14, 
			B.EXT_VAL4, B.EXT_VAL15, B.EXT_VAL5, B.EXT_VAL12, B.EXT_VAL6, B.EXT_VAL13, B.COMMON_TASK_STATUS, 
			B.ORGAN_CODE, B.TASK_TYPE, B.EXT_VAL0, B.EXT_VAL2, B.EXT_VAL1, 
			B.COMMON_TASK_DELETE_FLAG, B.TASK_CREATE_TIME, B.BUSSINESS_CODE, B.PLN_RESP_TIME, B.RESP_TIME, 
			B.COMMON_TASK_ID, B.SUB_ID FROM (
					SELECT ROWNUM RN, A.EXT_VAL7, A.EXT_VAL10, A.EXT_VAL8, A.EXT_VAL11, A.EXT_VAL9, A.EXT_VAL3, A.EXT_VAL14, 
			A.EXT_VAL4, A.EXT_VAL15, A.EXT_VAL5, A.EXT_VAL12, A.EXT_VAL6, A.EXT_VAL13, A.COMMON_TASK_STATUS, 
			A.ORGAN_CODE, A.TASK_TYPE, A.EXT_VAL0, A.EXT_VAL2, A.EXT_VAL1, 
			A.COMMON_TASK_DELETE_FLAG, A.TASK_CREATE_TIME, A.BUSSINESS_CODE, A.PLN_RESP_TIME, A.RESP_TIME, 
			A.COMMON_TASK_ID, A.SUB_ID FROM APP___PAS__DBUSER.T_COMMON_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.COMMON_TASK_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
