<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.CustomerTaxInfoDaoImpl">

	<sql id="customerTaxInfoWhereCondition">
		<if test=" tax_district != null and tax_district != ''  "><![CDATA[ AND A.TAX_DISTRICT = #{tax_district} ]]></if>
		<if test=" cause_ins != null and cause_ins != ''  "><![CDATA[ AND A.CAUSE_INS = #{cause_ins} ]]></if>
		<if test=" tax_base_amount  != null "><![CDATA[ AND A.TAX_BASE_AMOUNT = #{tax_base_amount} ]]></if>
		<if test=" tax_city != null and tax_city != ''  "><![CDATA[ AND A.TAX_CITY = #{tax_city} ]]></if>
		<if test=" tax_station_code != null and tax_station_code != ''  "><![CDATA[ AND A.TAX_STATION_CODE = #{tax_station_code} ]]></if>
		<if test=" tax_station_name != null and tax_station_name != ''  "><![CDATA[ AND A.TAX_STATION_NAME = #{tax_station_name} ]]></if>
		<if test=" tax_end_date  != null  and  tax_end_date  != ''  "><![CDATA[ AND A.TAX_END_DATE = #{tax_end_date} ]]></if>
		<if test=" tax_start_date  != null  and  tax_start_date  != ''  "><![CDATA[ AND A.TAX_START_DATE = #{tax_start_date} ]]></if>
		<if test=" retirement_age  != null "><![CDATA[ AND A.RETIREMENT_AGE = #{retirement_age} ]]></if>
		<if test=" tax_state != null and tax_state != ''  "><![CDATA[ AND A.TAX_STATE = #{tax_state} ]]></if>
		<if test=" gross_pay  != null "><![CDATA[ AND A.GROSS_PAY = #{gross_pay} ]]></if>
		<if test=" organization_code != null and organization_code != ''  "><![CDATA[ AND A.ORGANIZATION_CODE = #{organization_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" tax_base_interest  != null "><![CDATA[ AND A.TAX_BASE_INTEREST = #{tax_base_interest} ]]></if>
		<if test=" organization_end_date  != null  and  organization_end_date  != ''  "><![CDATA[ AND A.ORGANIZATION_END_DATE = #{organization_end_date} ]]></if>
		<if test=" cause_no != null and cause_no != ''  "><![CDATA[ AND A.CAUSE_NO = #{cause_no} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" organization_start_date  != null  and  organization_start_date  != ''  "><![CDATA[ AND A.ORGANIZATION_START_DATE = #{organization_start_date} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" tax_number != null and tax_number != ''  "><![CDATA[ AND A.TAX_NUMBER = #{tax_number} ]]></if>
		<if test=" tax_id  != null "><![CDATA[ AND A.TAX_ID = #{tax_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCustomerTaxInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryCustomerTaxInfoByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="queryCustomerTaxInfoByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryCustomerTaxInfoByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCustomerTaxInfo"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CUSTOMER_TAX_INFO.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO(
				TAX_DISTRICT, CAUSE_INS, TAX_BASE_AMOUNT, TAX_CITY, TAX_STATION_CODE, TAX_STATION_NAME, TAX_END_DATE, 
				TAX_START_DATE, INSERT_TIME, RETIREMENT_AGE, TAX_STATE, GROSS_PAY, ORGANIZATION_CODE, CUSTOMER_ID, 
				UPDATE_TIME, APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, TAX_BASE_INTEREST, ORGANIZATION_END_DATE, UPDATE_BY, 
				CAUSE_NO, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, ORGANIZATION_START_DATE, POLICY_ID, TAX_NUMBER, 
				TAX_ID ) 
			VALUES (
				#{tax_district, jdbcType=VARCHAR}, #{cause_ins, jdbcType=VARCHAR} , #{tax_base_amount, jdbcType=NUMERIC} , #{tax_city, jdbcType=VARCHAR} , #{tax_station_code, jdbcType=VARCHAR} , #{tax_station_name, jdbcType=VARCHAR} , #{tax_end_date, jdbcType=DATE} 
				, #{tax_start_date, jdbcType=DATE} , SYSDATE , #{retirement_age, jdbcType=NUMERIC} , #{tax_state, jdbcType=VARCHAR} , #{gross_pay, jdbcType=NUMERIC} , #{organization_code, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} 
				, SYSDATE , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{tax_base_interest, jdbcType=NUMERIC} , #{organization_end_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} 
				, #{cause_no, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{organization_start_date, jdbcType=DATE} , #{policy_id, jdbcType=NUMERIC} , #{tax_number, jdbcType=VARCHAR} 
				, #{tax_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCustomerTaxInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCustomerTaxInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			TAX_DISTRICT = #{tax_district, jdbcType=VARCHAR} ,
			CAUSE_INS = #{cause_ins, jdbcType=VARCHAR} ,
		    TAX_BASE_AMOUNT = #{tax_base_amount, jdbcType=NUMERIC} ,
			TAX_CITY = #{tax_city, jdbcType=VARCHAR} ,
			TAX_STATION_CODE = #{tax_station_code, jdbcType=VARCHAR} ,
			TAX_STATION_NAME = #{tax_station_name, jdbcType=VARCHAR} ,
		    TAX_END_DATE = #{tax_end_date, jdbcType=DATE} ,
		    TAX_START_DATE = #{tax_start_date, jdbcType=DATE} ,
		    RETIREMENT_AGE = #{retirement_age, jdbcType=NUMERIC} ,
			TAX_STATE = #{tax_state, jdbcType=VARCHAR} ,
		    GROSS_PAY = #{gross_pay, jdbcType=NUMERIC} ,
			ORGANIZATION_CODE = #{organization_code, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    TAX_BASE_INTEREST = #{tax_base_interest, jdbcType=NUMERIC} ,
		    ORGANIZATION_END_DATE = #{organization_end_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			CAUSE_NO = #{cause_no, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    ORGANIZATION_START_DATE = #{organization_start_date, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			TAX_NUMBER = #{tax_number, jdbcType=VARCHAR} ,
		    TAX_ID = #{tax_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCustomerTaxInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.CAUSE_INS, A.TAX_BASE_AMOUNT, A.TAX_CITY, A.TAX_STATION_CODE, A.TAX_STATION_NAME, A.TAX_END_DATE, 
			A.TAX_START_DATE, A.RETIREMENT_AGE, A.TAX_STATE, A.GROSS_PAY, A.ORGANIZATION_CODE, A.CUSTOMER_ID, 
			A.APPLY_CODE, A.POLICY_CODE, A.TAX_BASE_INTEREST, A.ORGANIZATION_END_DATE, 
			A.CAUSE_NO, A.LIST_ID, A.ORGANIZATION_START_DATE, A.POLICY_ID, A.TAX_NUMBER, 
			A.TAX_ID FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCustomerTaxInfoByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findCustomerTaxInfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.CAUSE_INS, A.TAX_BASE_AMOUNT, A.TAX_CITY, A.TAX_STATION_CODE, A.TAX_STATION_NAME, A.TAX_END_DATE, 
			A.TAX_START_DATE, A.RETIREMENT_AGE, A.TAX_STATE, A.GROSS_PAY, A.ORGANIZATION_CODE, A.CUSTOMER_ID, 
			A.APPLY_CODE, A.POLICY_CODE, A.TAX_BASE_INTEREST, A.ORGANIZATION_END_DATE, 
			A.CAUSE_NO, A.LIST_ID, A.ORGANIZATION_START_DATE, A.POLICY_ID, A.TAX_NUMBER, 
			A.TAX_ID FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCustomerTaxInfoByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findCustomerTaxInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.CAUSE_INS, A.TAX_BASE_AMOUNT, A.TAX_CITY, A.TAX_STATION_CODE, A.TAX_STATION_NAME, A.TAX_END_DATE, 
			A.TAX_START_DATE, A.RETIREMENT_AGE, A.TAX_STATE, A.GROSS_PAY, A.ORGANIZATION_CODE, A.CUSTOMER_ID, 
			A.APPLY_CODE, A.POLICY_CODE, A.TAX_BASE_INTEREST, A.ORGANIZATION_END_DATE, 
			A.CAUSE_NO, A.LIST_ID, A.ORGANIZATION_START_DATE, A.POLICY_ID, A.TAX_NUMBER, 
			A.TAX_ID FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCustomerTaxInfoByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findCustomerTaxInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.CAUSE_INS, A.TAX_BASE_AMOUNT, A.TAX_CITY, A.TAX_STATION_CODE, A.TAX_STATION_NAME, A.TAX_END_DATE, 
			A.TAX_START_DATE, A.RETIREMENT_AGE, A.TAX_STATE, A.GROSS_PAY, A.ORGANIZATION_CODE, A.CUSTOMER_ID, 
			A.APPLY_CODE, A.POLICY_CODE, A.TAX_BASE_INTEREST, A.ORGANIZATION_END_DATE, 
			A.CAUSE_NO, A.LIST_ID, A.ORGANIZATION_START_DATE, A.POLICY_ID, A.TAX_NUMBER, 
			A.TAX_ID FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCustomerTaxInfoByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCustomerTaxInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.CAUSE_INS, A.TAX_BASE_AMOUNT, A.TAX_CITY, A.TAX_STATION_CODE, A.TAX_STATION_NAME, A.TAX_END_DATE, 
			A.TAX_START_DATE, A.RETIREMENT_AGE, A.TAX_STATE, A.GROSS_PAY, A.ORGANIZATION_CODE, A.CUSTOMER_ID, 
			A.APPLY_CODE, A.POLICY_CODE, A.TAX_BASE_INTEREST, A.ORGANIZATION_END_DATE, 
			A.CAUSE_NO, A.LIST_ID, A.ORGANIZATION_START_DATE, A.POLICY_ID, A.TAX_NUMBER, 
			A.TAX_ID FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="customerTaxInfoWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCustomerTaxInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.CAUSE_INS, A.TAX_BASE_AMOUNT, A.TAX_CITY, A.TAX_STATION_CODE, A.TAX_STATION_NAME, A.TAX_END_DATE, 
			A.TAX_START_DATE, A.RETIREMENT_AGE, A.TAX_STATE, A.GROSS_PAY, A.ORGANIZATION_CODE, A.CUSTOMER_ID, 
			A.APPLY_CODE, A.POLICY_CODE, A.TAX_BASE_INTEREST, A.ORGANIZATION_END_DATE, 
			A.CAUSE_NO, A.LIST_ID, A.ORGANIZATION_START_DATE, A.POLICY_ID, A.TAX_NUMBER, 
			A.TAX_ID FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="customerTaxInfoWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCustomerTaxInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE 1 = 1  ]]>
		<include refid="customerTaxInfoWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCustomerTaxInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TAX_DISTRICT, B.CAUSE_INS, B.TAX_BASE_AMOUNT, B.TAX_CITY, B.TAX_STATION_CODE, B.TAX_STATION_NAME, B.TAX_END_DATE, 
			B.TAX_START_DATE, B.RETIREMENT_AGE, B.TAX_STATE, B.GROSS_PAY, B.ORGANIZATION_CODE, B.CUSTOMER_ID, 
			B.APPLY_CODE, B.POLICY_CODE, B.TAX_BASE_INTEREST, B.ORGANIZATION_END_DATE, 
			B.CAUSE_NO, B.LIST_ID, B.ORGANIZATION_START_DATE, B.POLICY_ID, B.TAX_NUMBER, 
			B.TAX_ID FROM (
					SELECT ROWNUM RN, A.TAX_DISTRICT, A.CAUSE_INS, A.TAX_BASE_AMOUNT, A.TAX_CITY, A.TAX_STATION_CODE, A.TAX_STATION_NAME, A.TAX_END_DATE, 
			A.TAX_START_DATE, A.RETIREMENT_AGE, A.TAX_STATE, A.GROSS_PAY, A.ORGANIZATION_CODE, A.CUSTOMER_ID, 
			A.APPLY_CODE, A.POLICY_CODE, A.TAX_BASE_INTEREST, A.ORGANIZATION_END_DATE, 
			A.CAUSE_NO, A.LIST_ID, A.ORGANIZATION_START_DATE, A.POLICY_ID, A.TAX_NUMBER, 
			A.TAX_ID FROM APP___PAS__DBUSER.T_CUSTOMER_TAX_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="customerTaxInfoWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
