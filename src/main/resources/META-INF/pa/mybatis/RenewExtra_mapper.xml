<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.RenewExtraDaoImpl">
	<sql id="queryRenewExtraPolicyInfo">
		<if test=" policy_code  != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" organ_code  != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type  != null and channel_type != '' and channel_type!='null'"><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" service_agent  != null and service_agent != '' "><![CDATA[ AND A.SERVICE_AGENT = #{service_agent} ]]></if>
		<if test=" renewal_type  != null and renewal_type != '' "><![CDATA[ AND A.RENEWAL_TYPE = #{renewal_type} ]]></if>
		<if test=" liability_state  != null"><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
	</sql>	
	<!-- 查询个数操作 -->
	<select id="findRenewExtraPolicyInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM (
				SELECT DISTINCT  B.policy_id, B.policy_code ,B.Customer_Id,B.service_agent,B.organ_code 
				FROM (  	 SELECT A.policy_id, A.policy_code,A.channel_type ,A.Customer_Id,A.service_agent,A.organ_code,A.liability_state,A.RENEWAL_TYPE
							 FROM    			(SELECT  	TCM.policy_id, TCM.policy_code ,TCM.channel_type,TCM.service_agent,TCM.organ_code,TCM.liability_state,
		                  									TPH.Customer_Id,TCP.RENEWAL_TYPE
					         		 			 FROM 		APP___PAS__DBUSER.T_contract_master TCM
					                   			 LEFT JOIN  APP___PAS__DBUSER.T_policy_holder TPH 
					        					 ON			TCM.policy_id = TPH.policy_id
					                   			 LEFT JOIN 	APP___PAS__DBUSER.T_contract_product TCP 
					                   			 ON 		TCM.policy_id = TCP.policy_id 
					          					 ) A  
					         WHERE 1=1 ]]>
							<include refid="queryRenewExtraPolicyInfo" />
		<![CDATA[ )  B]]>
		<![CDATA[)]]>
	</select>

	<!-- 分页查询操作 -->
	<select id="queryRenewExtraPolicyInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT DISTINCT  B.policy_id, B.policy_code ,B.Customer_Id,B.service_agent,B.organ_code 
		FROM (  	 SELECT ROWNUM RN,A.policy_id, A.policy_code,A.channel_type ,A.Customer_Id,A.service_agent,A.organ_code,A.liability_state,A.RENEWAL_TYPE
					 FROM    			(SELECT  	TCM.policy_id, TCM.policy_code ,TCM.channel_type,TCM.service_agent,TCM.organ_code,TCM.liability_state,
                  									TPH.Customer_Id,TCP.RENEWAL_TYPE
			         		 			 FROM 		APP___PAS__DBUSER.T_contract_master TCM
			                   			 LEFT JOIN  APP___PAS__DBUSER.T_policy_holder TPH 
			        					 ON			TCM.policy_id = TPH.policy_id
			                   			 LEFT JOIN 	APP___PAS__DBUSER.T_contract_product TCP 
			                   			 ON 		TCM.policy_id = TCP.policy_id 
			          					 ) A  
			         WHERE ROWNUM <= #{LESS_NUM} ]]>
					<include refid="queryRenewExtraPolicyInfo" />
<!-- 		<![CDATA[ ORDER BY A.policy_id ]]>  -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
</mapper>