<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ILppremDao">

	
	<select id="findAllLpprem" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT *FROM (
			SELECT A.PAYENDDATE     ,
              A.FREEENDDATE     ,
              A.RATE            ,
              A.SUMPREM         ,
              A.MANAGECOM       ,
              A.<PERSON>YIN<PERSON>         ,
              A.STATE           ,
              A.MAKEDATE        ,
              A.<PERSON>         ,
              A.<PERSON>Y<PERSON>      ,
              A.FREERATE        ,
              A.PREM            ,
              A.APPNTTYPE       ,
              A.PAYPLANTYPE     ,
              A.GRPCONTNO       ,
              A.PAYTODATE       ,
              A.NEEDACC         ,
              A.<PERSON>   ,
              A<PERSON>        ,
              <PERSON><PERSON>     ,
              <PERSON><PERSON>          ,
              <PERSON><PERSON>          ,
              <PERSON><PERSON>           ,
              <PERSON><PERSON>        ,
              <PERSON><PERSON>       ,
              A.<PERSON>        ,
              A.<PERSON>CT    ,
              A.PAYTIMES        ,
              A.PAYPLANCODE     ,
              A.MODIFYTIME      ,
              A.EDORTYPE        ,
              A.SUPPRISKSCORE   ,
              A.OPERATOR        ,
              A.SECINSUADDPOINT ,
              A.PAYSTARTDATE    ,
              A.TRANSFERDATE    ,
              ROW_NUMBER() OVER(PARTITION BY A.PAYPLANTYPE ORDER BY A.MODIFYDATE DESC) RN
         FROM APP___PAS__DBUSER.LPPREM A WHERE 1=1
	]]>
		<if test=" contno  != null and contno  !='' "><![CDATA[ AND A.CONTNO =  rpad(#{contno},(select length(l.CONTNO) from APP___PAS__DBUSER.LPPREM l where rownum = 1)) ]]></if>
		<if test=" polno  != null and polno  !=''"><![CDATA[ AND A.POLNO =  rpad(#{polno},(select length(l.POLNO) from APP___PAS__DBUSER.LPPREM l where rownum = 1)) ]]></if>
		<if test=" payplantype   != null and payplantype  !=''"><![CDATA[ AND A.PAYPLANTYPE  IN ('01','02','03','04') ]]></if>
		<if test=" modifydate   != null and modifydate  !=''"><![CDATA[ AND A.modifydate > #{modifydate} ]]></if>
		<![CDATA[ ) WHERE RN = 1
		]]>
	</select>
	
</mapper>
