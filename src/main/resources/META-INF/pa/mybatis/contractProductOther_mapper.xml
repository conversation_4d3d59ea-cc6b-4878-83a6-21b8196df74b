<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractProductOtherDao">

	<sql id="contractProductOtherWhereCondition">
		<if test=" is_extend_rate  != null "><![CDATA[ AND A.IS_EXTEND_RATE = #{is_extend_rate} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" field20 != null and field20 != ''  "><![CDATA[ AND A.FIELD20 = #{field20} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" field19 != null and field19 != ''  "><![CDATA[ AND A.FIELD19 = #{field19} ]]></if>
		<if test=" field17 != null and field17 != ''  "><![CDATA[ AND A.FIELD17 = #{field17} ]]></if>
		<if test=" field18 != null and field18 != ''  "><![CDATA[ AND A.FIELD18 = #{field18} ]]></if>
		<if test=" busi_item_id != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" field13 != null and field13 != ''  "><![CDATA[ AND A.FIELD13 = #{field13} ]]></if>
		<if test=" field14 != null and field14 != ''  "><![CDATA[ AND A.FIELD14 = #{field14} ]]></if>
		<if test=" field15 != null and field15 != ''  "><![CDATA[ AND A.FIELD15 = #{field15} ]]></if>
		<if test=" field16 != null and field16 != ''  "><![CDATA[ AND A.FIELD16 = #{field16} ]]></if>
		<if test=" field10 != null and field10 != ''  "><![CDATA[ AND A.FIELD10 = #{field10} ]]></if>
		<if test=" field11 != null and field11 != ''  "><![CDATA[ AND A.FIELD11 = #{field11} ]]></if>
		<if test=" field12 != null and field12 != ''  "><![CDATA[ AND A.FIELD12 = #{field12} ]]></if>
		<if test=" field7 != null and field7 != ''  "><![CDATA[ AND A.FIELD7 = #{field7} ]]></if>
		<if test=" field6 != null and field6 != ''  "><![CDATA[ AND A.FIELD6 = #{field6} ]]></if>
		<if test=" field9 != null and field9 != ''  "><![CDATA[ AND A.FIELD9 = #{field9} ]]></if>
		<if test=" field8 != null and field8 != ''  "><![CDATA[ AND A.FIELD8 = #{field8} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" field3 != null and field3 != ''  "><![CDATA[ AND A.FIELD3 = #{field3} ]]></if>
		<if test=" field2 != null and field2 != ''  "><![CDATA[ AND A.FIELD2 = #{field2} ]]></if>
		<if test=" field5 != null and field5 != ''  "><![CDATA[ AND A.FIELD5 = #{field5} ]]></if>
		<if test=" field4 != null and field4 != ''  "><![CDATA[ AND A.FIELD4 = #{field4} ]]></if>
		<if test=" field1 != null and field1 != ''  "><![CDATA[ AND A.FIELD1 = #{field1} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator="OR">
				<![CDATA[ A.POLICY_ID = #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" max_benefit_period != null and max_benefit_period != ''  "><![CDATA[ AND A.MAX_BENEFIT_PERIOD = #{max_benefit_period} ]]></if>
		<if test=" hospital_scope != null and hospital_scope != ''  "><![CDATA[ AND A.HOSPITAL_SCOPE = #{hospital_scope} ]]></if>
		<if test=" insured_address_is_offen_live != null and insured_address_is_offen_live != ''  "><![CDATA[ AND A.INSURED_ADDRESS_IS_OFFEN_LIVE = #{insured_address_is_offen_live} ]]></if>
		<if test=" annuity_start_pay_day != null and annuity_start_pay_day != ''  "><![CDATA[ AND A.ANNUITY_START_PAY_DAY = #{annuity_start_pay_day} ]]></if>	
		<if test=" death_benefit_plan != null and death_benefit_plan != ''  "><![CDATA[ AND A.DEATH_BENEFIT_PLAN = #{death_benefit_plan} ]]></if>	
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryContractProductOtherByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addContractProductOther"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER(
				INSURED_AGE_FLAG,IS_EXTEND_RATE, PRODUCT_ID, BUSI_PRD_ID, ITEM_ID, FIELD20, APPLY_CODE, FIELD19, 
				INSERT_TIMESTAMP, FIELD17, FIELD18, UPDATE_BY, BUSI_ITEM_ID, POLICY_ID, FIELD13, 
				FIELD14, FIELD15, FIELD16, FIELD10, FIELD11, INSERT_TIME, FIELD12, 
				UPDATE_TIME, FIELD7, FIELD6, FIELD9, FIELD8, POLICY_CODE, FIELD3, 
				FIELD2, FIELD5, FIELD4, FIELD1, UPDATE_TIMESTAMP, INSERT_BY, MAX_BENEFIT_PERIOD,HOSPITAL_SCOPE,INSURED_ADDRESS_IS_OFFEN_LIVE,ANNUITY_START_PAY_DAY,DEATH_BENEFIT_PLAN ) 
			VALUES (
				#{insured_age_flag,jdbcType=NUMERIC},#{is_extend_rate, jdbcType=NUMERIC}, #{product_id, jdbcType=NUMERIC} , #{busi_prd_id, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , #{field20, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{field19, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{field17, jdbcType=VARCHAR} , #{field18, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{field13, jdbcType=VARCHAR} 
				, #{field14, jdbcType=VARCHAR} , #{field15, jdbcType=VARCHAR} , #{field16, jdbcType=VARCHAR} , #{field10, jdbcType=VARCHAR} , #{field11, jdbcType=VARCHAR} , SYSDATE , #{field12, jdbcType=VARCHAR} 
				, SYSDATE , #{field7, jdbcType=VARCHAR} , #{field6, jdbcType=VARCHAR} , #{field9, jdbcType=VARCHAR} , #{field8, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{field3, jdbcType=VARCHAR} 
				, #{field2, jdbcType=VARCHAR} , #{field5, jdbcType=VARCHAR} , #{field4, jdbcType=VARCHAR} , #{field1, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC}, #{max_benefit_period, jdbcType=NUMERIC} , #{hospital_scope, jdbcType=NUMERIC} , #{insured_address_is_offen_live, jdbcType=NUMERIC}
				,#{annuity_start_pay_day, jdbcType=VARCHAR},#{death_benefit_plan, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteContractProductOther" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER WHERE ITEM_ID = #{item_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateContractProductOther" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER ]]>
		<set>
		<trim suffixOverrides=",">
			INSURED_AGE_FLAG = #{insured_age_flag,jdbcType=NUMERIC},
		    IS_EXTEND_RATE = #{is_extend_rate, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			FIELD20 = #{field20, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			FIELD19 = #{field19, jdbcType=VARCHAR} ,
			FIELD17 = #{field17, jdbcType=VARCHAR} ,
			FIELD18 = #{field18, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			FIELD13 = #{field13, jdbcType=VARCHAR} ,
			FIELD14 = #{field14, jdbcType=VARCHAR} ,
			FIELD15 = #{field15, jdbcType=VARCHAR} ,
			FIELD16 = #{field16, jdbcType=VARCHAR} ,
			FIELD10 = #{field10, jdbcType=VARCHAR} ,
			FIELD11 = #{field11, jdbcType=VARCHAR} ,
			FIELD12 = #{field12, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			FIELD7 = #{field7, jdbcType=VARCHAR} ,
			FIELD6 = #{field6, jdbcType=VARCHAR} ,
			FIELD9 = #{field9, jdbcType=VARCHAR} ,
			FIELD8 = #{field8, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			FIELD3 = #{field3, jdbcType=VARCHAR} ,
			FIELD2 = #{field2, jdbcType=VARCHAR} ,
			FIELD5 = #{field5, jdbcType=VARCHAR} ,
			FIELD4 = #{field4, jdbcType=VARCHAR} ,
			FIELD1 = #{field1, jdbcType=VARCHAR} ,
			MAX_BENEFIT_PERIOD = #{max_benefit_period, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    HOSPITAL_SCOPE = #{hospital_scope, jdbcType=NUMERIC} , 
		    INSURED_ADDRESS_IS_OFFEN_LIVE = #{insured_address_is_offen_live, jdbcType=NUMERIC},
		    ANNUITY_START_PAY_DAY = #{annuity_start_pay_day, jdbcType=VARCHAR},
		    DEATH_BENEFIT_PLAN = #{death_benefit_plan, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>

		<update id="updateContractProductOtherField1" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			FIELD1 = #{field1, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findContractProductOtherByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
			A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
			A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
			A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, A.INSURED_AGE_FLAG,
			A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.MAX_BENEFIT_PERIOD ,A.HOSPITAL_SCOPE,A.INSURED_ADDRESS_IS_OFFEN_LIVE,A.ANNUITY_START_PAY_DAY,A.DEATH_BENEFIT_PLAN FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER A WHERE 1 = 1 
			AND A.ITEM_ID = #{item_id}  ]]>
		<include refid="contractProductOtherWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
<!-- 按索引查询操作 -->	
	<select id="findContractProductOtherByItemIdOne" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
			A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
			A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
			A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, A.INSURED_AGE_FLAG,
			A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.MAX_BENEFIT_PERIOD,A.HOSPITAL_SCOPE,A.INSURED_ADDRESS_IS_OFFEN_LIVE,A.ANNUITY_START_PAY_DAY,A.DEATH_BENEFIT_PLAN FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER A WHERE 1 = 1 ]]>
		<include refid="contractProductOtherWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapContractProductOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
			A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
			A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
			A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, A.INSURED_AGE_FLAG,
			A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.MAX_BENEFIT_PERIOD,A.HOSPITAL_SCOPE,A.INSURED_ADDRESS_IS_OFFEN_LIVE,A.ANNUITY_START_PAY_DAY,A.DEATH_BENEFIT_PLAN FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllContractProductOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
			A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
			A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
			A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, A.INSURED_AGE_FLAG,
			A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.MAX_BENEFIT_PERIOD,A.HOSPITAL_SCOPE,A.INSURED_ADDRESS_IS_OFFEN_LIVE,A.ANNUITY_START_PAY_DAY,A.DEATH_BENEFIT_PLAN FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<include refid="contractProductOtherWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findContractProductOtherTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryContractProductOtherForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT B.RN AS rowNumber, B.IS_EXTEND_RATE, B.PRODUCT_ID, B.BUSI_PRD_ID, B.ITEM_ID, B.FIELD20, B.APPLY_CODE, B.FIELD19, 
			B.FIELD17, B.FIELD18, B.BUSI_ITEM_ID, B.POLICY_ID, B.FIELD13, 
			B.FIELD14, B.FIELD15, B.FIELD16, B.FIELD10, B.FIELD11, B.FIELD12, 
			B.FIELD7, B.FIELD6, B.FIELD9, B.FIELD8, B.POLICY_CODE, B.FIELD3, B.INSURED_AGE_FLAG,
			B.FIELD2, B.FIELD5, B.FIELD4, B.FIELD1, B.MAX_BENEFIT_PERIOD,B.HOSPITAL_SCOPE,B.INSURED_ADDRESS_IS_OFFEN_LIVE,B.ANNUITY_START_PAY_DAY,B.DEATH_BENEFIT_PLAN FROM (
					SELECT ROWNUM RN, A.IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
			A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
			A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
			A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, A.INSURED_AGE_FLAG,
			A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.MAX_BENEFIT_PERIOD,A.HOSPITAL_SCOPE,A.INSURED_ADDRESS_IS_OFFEN_LIVE,A.ANNUITY_START_PAY_DAY,A.DEATH_BENEFIT_PLAN FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ITEM_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询保单责任组备用表FIELD1(保障计划) -->
	<select id="findProtectionPlanByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT CASE TCPO.FIELD1
					       WHEN '1' THEN
					        '计划一'
					       WHEN '2' THEN
					        '计划二'
					       WHEN '3' THEN
					        '计划三'
					       WHEN '4' THEN
					        '计划四'
					       WHEN '5' THEN
					        '计划五'
					       WHEN '6' THEN
					        '计划六'
					       ELSE
					        ''
					     END AS FIELD1
                    FROM DEV_PAS.T_CONTRACT_PRODUCT_OTHER TCPO
                   WHERE TCPO.ITEM_ID = #{item_id} ]]> 
	</select>
</mapper>
