<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="operateQuery">
    
    <!-- 查询操作 -->
	<select id="PA_operateQuery" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select to_char(wm_concat(to_char(a.deal_time,'yyyy-mm-dd'))) as partpaydate from APP___PAS__DBUSER.T_fund_trans a,APP___PAS__DBUSER.T_contract_master b where a.policy_id=b.policy_id]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ and b.policy_code=#{policy_code}]]>
		<![CDATA[ and a.deal_time > #{deal_time}]]>
		<![CDATA[ and a.trans_code in (13,22)]]>
		<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND a.busi_item_id = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 根据保单号出险时间查询出险时间以后有没有领取回退的记录 -->
	<select id="PA_operateCancel" resultType="java.util.Map" parameterType="java.util.Map">
		select count(*) as amount 
	     from dev_pas.t_Policy_Reversal tpr
	     left join dev_pas.t_cs_accept_change t
	       on tpr.rb_accept_code = t.accept_code
	     left join dev_pas.t_cs_policy_change tpc
	       on tpc.accept_id = t.accept_id
	    where tpc.policy_code = #{policy_code}
	      and t.service_code = 'PG'
	      and tpr.rb_service_code ='PG'
	      and tpc.validate_time > #{deal_time}
	</select>
</mapper>