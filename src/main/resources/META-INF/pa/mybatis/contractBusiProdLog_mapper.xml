<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ContractBusiProdLogDaoImpl">
 
	<sql id="PA_contractBusiProdLogWhereCondition">
		<if test=" gurnt_start_date  != null  and  gurnt_start_date  != ''  "><![CDATA[ AND A.GURNT_START_DATE = #{gurnt_start_date} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" gurnt_period  != null "><![CDATA[ AND A.GURNT_PERIOD = #{gurnt_period} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" hesitation2acc  != null "><![CDATA[ AND A.HESITATION2ACC = #{hesitation2acc} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" waiver  != null "><![CDATA[ AND A.WAIVER = #{waiver} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" assurerenew_flag  != null "><![CDATA[ AND A.ASSURERENEW_FLAG = #{assurerenew_flag} ]]></if>
		<if test=" renewal_state  != null "><![CDATA[ AND A.RENEWAL_STATE = #{renewal_state} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
		<if test=" renew_times  != null "><![CDATA[ AND A.RENEW_TIMES = #{renew_times} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" gurnt_perd_type != null and gurnt_perd_type != ''  "><![CDATA[ AND A.GURNT_PERD_TYPE = #{gurnt_perd_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" joint_life_flag  != null "><![CDATA[ AND A.JOINT_LIFE_FLAG = #{joint_life_flag} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" due_lapse_date  != null  and  due_lapse_date  != ''  "><![CDATA[ AND A.DUE_LAPSE_DATE = #{due_lapse_date} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" prd_pkg_code != null and prd_pkg_code != ''  "><![CDATA[ AND A.PRD_PKG_CODE = #{prd_pkg_code} ]]></if>
		<if test=" gurnt_renew_end  != null  and  gurnt_renew_end  != ''  "><![CDATA[ AND A.GURNT_RENEW_END = #{gurnt_renew_end} ]]></if>
		<if test=" gurnt_renew_start  != null  and  gurnt_renew_start  != ''  "><![CDATA[ AND A.GURNT_RENEW_START = #{gurnt_renew_start} ]]></if>
		<if test=" first_validate_date != null and first_validate_date != '' "><![CDATA[ AND A.FIRST_VALIDATE_DATE = #{first_validate_date} ]]></if>
		<if test=" before_renew_busi_code != null and before_renew_busi_code != '' "><![CDATA[ AND A.BEFORE_RENEW_BUSI_CODE = #{before_renew_busi_code} ]]></if>
		<if test=" lapse_loan_suspend_date != null and lapse_loan_suspend_date != '' "><![CDATA[ AND A.LAPSE_LOAN_SUSPEND_DATE = #{lapse_loan_suspend_date} ]]></if>
		<if test=" waived_busi_prods != null and waived_busi_prods != '' "><![CDATA[ AND A.WAIVED_BUSI_PRODS = #{waived_busi_prods} ]]></if>
	</sql>
 

<!-- 按索引生成的查询条件 -->	
	<sql id="queryContractBusiProdLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="queryContractBusiProdLogByLogTypeCondition">
		<if test=" log_type != null and log_type != '' "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
	</sql>	
	<sql id="queryContractBusiProdLogByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="queryContractBusiProdLogByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="queryContractBusiProdLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="queryContractBusiProdLogByBusiPrdIdCondition">
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addContractBusiProdLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_BUSI_PROD_LOG__LOG.NEXTVAL from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG(
				INITIAL_VALIDATE_DATE,GURNT_START_DATE, BUSI_PRD_ID, GURNT_PERIOD, BUSI_PROD_CODE, HESITATION2ACC, APPLY_CODE, 
				IS_WAIVED, UPDATE_BY, POLICY_ID, WAIVER, MASTER_BUSI_ITEM_ID, UPDATE_TIME, PAIDUP_DATE, 
				EXPIRY_DATE, LIABILITY_STATE, POLICY_CODE, RERINSTATE_DATE, RENEW_DECISION, VALIDATE_DATE, ASSURERENEW_FLAG, 
				RENEWAL_STATE, UPDATE_TIMESTAMP, INSERT_BY, APL_PERMIT, APPLY_DATE, RENEW, INSERT_TIMESTAMP, 
				RENEW_TIMES, WAIVER_END, MATURITY_DATE, GURNT_PERD_TYPE, POLICY_CHG_ID, BUSI_ITEM_ID, LAPSE_DATE, 
				JOINT_LIFE_FLAG, INSERT_TIME, END_CAUSE, ISSUE_DATE, LAPSE_CAUSE, DECISION_CODE, LOG_ID, 
				SUSPEND_DATE, INITIAL_PREM_DATE, SUSPEND_CAUSE, DUE_LAPSE_DATE, LOG_TYPE, WAIVER_START, PRD_PKG_CODE, 
				GURNT_RENEW_END, GURNT_RENEW_START, GURNT_RATE,SETTLE_METHOD,OLD_POL_NO,FLIGHT_NO,HESITATION_PERIOD_DAY,
				CAN_CHANGE_FLAG,CAN_REINSURE_FLAG,CHARGE_FLAG,REINSURED,ORDER_ID,MEET_POV_STANDARD_FLAG, 
				FIRST_VALIDATE_DATE, BEFORE_RENEW_BUSI_CODE, LAPSE_LOAN_SUSPEND_DATE,UW_RENEW_FLAG,WAIVED_BUSI_PRODS )
			VALUES (
				#{initial_validate_date, jdbcType=DATE},#{gurnt_start_date, jdbcType=DATE}, #{busi_prd_id, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} ,  #{busi_prod_code, jdbcType=VARCHAR} , #{hesitation2acc, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} 
				, #{is_waived, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{waiver, jdbcType=NUMERIC} , #{master_busi_item_id, jdbcType=NUMERIC} , SYSDATE , #{paidup_date, jdbcType=DATE} 
				, #{expiry_date, jdbcType=DATE} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{assurerenew_flag, jdbcType=NUMERIC} 
				, #{renewal_state, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{apl_permit, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} , #{renew, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{gurnt_perd_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} 
				, #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{lapse_cause, jdbcType=VARCHAR} , #{decision_code, jdbcType=VARCHAR} , #{log_id, jdbcType=NUMERIC} 
				, #{suspend_date, jdbcType=DATE} , #{initial_prem_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{due_lapse_date, jdbcType=DATE} , #{log_type, jdbcType=VARCHAR} , #{waiver_start, jdbcType=DATE} , #{prd_pkg_code, jdbcType=VARCHAR} 
				, #{gurnt_renew_end, jdbcType=DATE} , #{gurnt_renew_start, jdbcType=DATE} , #{gurnt_rate, jdbcType=NUMERIC} ,#{settle_method, jdbcType=NUMERIC}, #{old_pol_no, jdbcType=VARCHAR}, #{flight_no, jdbcType=VARCHAR}, #{hesitation_period_day, jdbcType=NUMERIC}
				, #{can_change_flag, jdbcType=NUMERIC}, #{can_reinsure_flag, jdbcType=NUMERIC}
		        , #{charge_flag, jdbcType=NUMERIC}, #{REINSURED, jdbcType=VARCHAR},#{order_id, jdbcType=VARCHAR},#{meet_pov_standard_flag,jdbcType=NUMERIC} 
		        , #{first_validate_date, jdbcType=DATE}, #{before_renew_busi_code, jdbcType=VARCHAR}, #{lapse_loan_suspend_date, jdbcType=DATE}, #{uw_renew_flag, jdbcType=VARCHAR}, #{waived_busi_prods, jdbcType=VARCHAR} )
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteContractBusiProdLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG WHERE LOG_ID = #{log_id}  ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateContractBusiProdLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			INITIAL_VALIDATE_DATE = #{initial_validate_date, jdbcType=DATE},
		    GURNT_START_DATE = #{gurnt_start_date, jdbcType=DATE} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
		    GURNT_PERIOD = #{gurnt_period, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    HESITATION2ACC = #{hesitation2acc, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    WAIVER = #{waiver, jdbcType=NUMERIC} ,
		    MASTER_BUSI_ITEM_ID = #{master_busi_item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
		    RENEW_DECISION = #{renew_decision, jdbcType=NUMERIC} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    ASSURERENEW_FLAG = #{assurerenew_flag, jdbcType=NUMERIC} ,
		    RENEWAL_STATE = #{renewal_state, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    APL_PERMIT = #{apl_permit, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    RENEW = #{renew, jdbcType=NUMERIC} ,
		    RENEW_TIMES = #{renew_times, jdbcType=NUMERIC} ,
		    WAIVER_END = #{waiver_end, jdbcType=DATE} ,
		    MATURITY_DATE = #{maturity_date, jdbcType=DATE} ,
			GURNT_PERD_TYPE = #{gurnt_perd_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    JOINT_LIFE_FLAG = #{joint_life_flag, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    DUE_LAPSE_DATE = #{due_lapse_date, jdbcType=DATE} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    WAIVER_START = #{waiver_start, jdbcType=DATE} ,
			PRD_PKG_CODE = #{prd_pkg_code, jdbcType=VARCHAR} ,
		    GURNT_RENEW_END = #{gurnt_renew_end, jdbcType=DATE} ,
		    GURNT_RENEW_START = #{gurnt_renew_start, jdbcType=DATE} ,
		    GURNT_RATE = #{gurnt_rate, jdbcType=NUMERIC},
		    SETTLE_METHOD = #{settle_method, jdbcType=NUMERIC},
		    OLD_POL_NO = #{old_pol_no, jdbcType=VARCHAR} ,
		    FLIGHT_NO = #{flight_no, jdbcType=VARCHAR} ,
		    HESITATION_PERIOD_DAY = #{hesitation_period_day, jdbcType=NUMERIC} ,
		    ORDER_ID = #{order_id, jdbcType=VARCHAR} ,
		    MEET_POV_STANDARD_FLAG = #{meet_pov_standard_flag,jdbcType=NUMERIC},
		    FIRST_VALIDATE_DATE = #{first_validate_date, jdbcType=DATE} ,
		    BEFORE_RENEW_BUSI_CODE = #{before_renew_busi_code, jdbcType=VARCHAR} ,
		    LAPSE_LOAN_SUSPEND_DATE = #{lapse_loan_suspend_date, jdbcType=VARCHAR} ,
		    UW_RENEW_FLAG = #{uw_renew_flag, jdbcType=VARCHAR} ,
		    WAIVED_BUSI_PRODS  = #{waived_busi_prods, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findContractBusiProdLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW, A.INITIAL_VALIDATE_DATE,
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE,A.SETTLE_METHOD ,A.OLD_POL_NO , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE 1 = 1  ]]>
		<include refid="queryContractBusiProdLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="findContractBusiProdLogByLogType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE,A.INITIAL_VALIDATE_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE,A.SETTLE_METHOD ,A.OLD_POL_NO , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE 1 = 1  ]]>
		<include refid="queryContractBusiProdLogByLogTypeCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="findContractBusiProdLogByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE,A.INITIAL_VALIDATE_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO  , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE 1 = 1  ]]>
		<include refid="queryContractBusiProdLogByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="findContractBusiProdLogByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE,A.INITIAL_VALIDATE_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW,A.INITIAL_VALIDATE_DATE, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE 1 = 1  ]]>
		<include refid="queryContractBusiProdLogByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="findContractBusiProdLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW,A.INITIAL_VALIDATE_DATE, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO  , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE 1 = 1  ]]>
		<include refid="queryContractBusiProdLogByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="findContractBusiProdLogByBusiPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW,A.INITIAL_VALIDATE_DATE, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO  , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE 1 = 1  ]]>
		<include refid="queryContractBusiProdLogByBusiPrdIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapContractBusiProdLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE,  A.HESITATION_PERIOD_DAY,A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW,A.INITIAL_VALIDATE_DATE, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO  , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllContractBusiProdLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW,A.INITIAL_VALIDATE_DATE, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractBusiProdLogWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findContractBusiProdLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryContractBusiProdLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_RENEW_FLAG, B.HESITATION_PERIOD_DAY, B.FLIGHT_NO, B.GURNT_START_DATE, B.BUSI_PRD_ID, B.GURNT_PERIOD,  B.BUSI_PROD_CODE, B.HESITATION2ACC, B.APPLY_CODE, 
			B.IS_WAIVED, B.POLICY_ID, B.WAIVER, B.MASTER_BUSI_ITEM_ID, B.PAIDUP_DATE, 
			B.EXPIRY_DATE, B.LIABILITY_STATE, B.POLICY_CODE, B.RERINSTATE_DATE, B.RENEW_DECISION, B.VALIDATE_DATE, B.ASSURERENEW_FLAG, 
			B.RENEWAL_STATE, B.APL_PERMIT, B.APPLY_DATE, B.RENEW,B.INITIAL_VALIDATE_DATE, 
			B.RENEW_TIMES, B.WAIVER_END, B.MATURITY_DATE, B.GURNT_PERD_TYPE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.LAPSE_DATE, 
			B.JOINT_LIFE_FLAG, B.END_CAUSE, B.ISSUE_DATE, B.LAPSE_CAUSE, B.DECISION_CODE, B.LOG_ID, 
			B.SUSPEND_DATE, B.INITIAL_PREM_DATE, B.SUSPEND_CAUSE, B.DUE_LAPSE_DATE, B.LOG_TYPE, B.WAIVER_START, B.PRD_PKG_CODE, B.ORDER_ID,
			B.GURNT_RENEW_END, B.GURNT_RENEW_START ,B.GURNT_RATE, B.SETTLE_METHOD,B.WAIVED_BUSI_PRODS  FROM (
					SELECT ROWNUM RN, A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW,A.INITIAL_VALIDATE_DATE, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO,A.WAIVED_BUSI_PRODS FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
		<select id="PAS_findChangeStateByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD,  A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
			A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW,A.INITIAL_VALIDATE_DATE, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
			A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, 
			A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, A.ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE, A.SETTLE_METHOD ,A.OLD_POL_NO  , 
			A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE ,A.LAPSE_LOAN_SUSPEND_DATE,A.WAIVED_BUSI_PRODS  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B WHERE 1 = 1  ]]>
		<![CDATA[ AND A.POLICY_ID = B.POLICY_ID
		   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID]]>
		   <if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID =  #{policy_chg_id}]]></if>
		   <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID =  #{policy_id}]]></if>
		  <![CDATA[ AND A.LIABILITY_STATE = '1'
		   AND B.LIABILITY_STATE <> '1' ]]>
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<!-- 查询log表保证续保周期数据，生产缺陷：34535 -->
	<!-- 按索引查询操作 -->	
	<select id="findContractBusiProdLogRenewDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.UW_RENEW_FLAG, A.GURNT_START_DATE, A.HESITATION_PERIOD_DAY, A.FLIGHT_NO, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
      A.IS_WAIVED, A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, 
      A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.ASSURERENEW_FLAG, 
      A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, A.RENEW, A.INITIAL_VALIDATE_DATE,
      A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.LAPSE_DATE, 
      A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, A.ORDER_ID,
      A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.LOG_TYPE, A.WAIVER_START, A.PRD_PKG_CODE, 
      A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.GURNT_RATE,A.SETTLE_METHOD ,A.OLD_POL_NO, A.MEET_POV_STANDARD_FLAG,
      A.NEXT_FLAG ,A.FIRST_VALIDATE_DATE, A.BEFORE_RENEW_BUSI_CODE,A.WAIVED_BUSI_PRODS  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG A  WHERE ROWNUM=1
	      AND A.POLICY_ID=#{policy_id}
	      AND A.BUSI_ITEM_ID=#{busi_item_id}
	      AND A.POLICY_CHG_ID=
	      (SELECT *
	  FROM (SELECT B.POLICY_CHG_ID
	          FROM DEV_PAS.T_POLICY_CHANGE B	         
			 WHERE  TO_DATE(TO_CHAR(B.VALIDATE_TIME,'yyyy-MM-dd'), 'yyyy-MM-dd') <=TO_DATE(TO_CHAR(#{validate_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	           AND B.SERVICE_CODE IN ('PREF')
	           AND B.POLICY_ID = #{policy_id}
	         ORDER BY B.FINISH_TIME DESC)
	 WHERE ROWNUM = 1 )
			 ]]>
	</select>
	
</mapper>
