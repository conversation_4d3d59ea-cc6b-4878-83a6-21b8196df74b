<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.impl.TempPremReturnDaoImpl">
	
	<sql id="PA_querytempPremReturn">
		<if test=" policy_code  != null  and  policy_code  != ''  "><![CDATA[AND TCM.POLICY_CODE = #{policy_code}  ]]></if>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(TCM.POLICY_ID,#{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}]]></if>
		<if test=" universal_policy_code  != null  and  universal_policy_code  != ''  "><![CDATA[AND TCMM.POLICY_CODE = #{universal_policy_code}  ]]></if>
		<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[AND TCM.ORGAN_CODE IN (
		SELECT T.ORGAN_CODE
		  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
		 START WITH T.ORGAN_CODE = #{organ_code}
		CONNECT BY PRIOR T.ORGAN_CODE = t.UPORGAN_CODE)  ]]></if>	
    </sql>
	
	<!-- 查询个数操作 -->
	<select id="PA_findProvisionalCollectionRefundTrackTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
				<![CDATA[ SELECT COUNT(1) FROM (
		SELECT DISTINCT 
					TPA.HOLDER_NAME appnt_Name,
					TCM.POLICY_CODE policy_Code,
					TCM.ORGAN_CODE organ_Code,
					TPA.FINISH_TIME finish_Time,
					TPA.PAY_MODE pay_Mode,
					TPA.UNIT_NUMBER unit_Number,
					TPA.FEE_AMOUNT fee_Amount,
					(select A.DOCUMENT_NO from dev_pas.T_DOCUMENT A where A.POLICY_CODE = TCM.POLICY_CODE)DOCUMENT_NO,
					TCA.AGENT_CODE agent_Code,
					TCA.AGENT_NAME agent_Name
				FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
				     APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,
					 APP___PAS__DBUSER.T_PREM_ARAP          TPA
				WHERE 1 = 1
				 AND TCM.POLICY_CODE = TPA.POLICY_CODE
				 AND TCM.POLICY_CODE = TCA.POLICY_CODE
				 AND TCA.IS_CURRENT_AGENT = '1'
				 AND TPA.BUSINESS_TYPE IN ('4003', '1005')
		]]>
		<include refid="PA_querytempPremReturn" />
		<![CDATA[ )  B ]]>
	</select>
	
	<!-- 分页查询操作 -->
	<select id="PA_findProvisionalCollectionRefundTrackForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
      <![CDATA[ SELECT    B.appnt_Name,
			              B.policy_Code,
			              B.organ_Code,
			              B.finish_Time,
			              B.pay_Mode,
			              B.unit_Number,
			              B.fee_Amount,
			              B.DOCUMENT_NO,
			              B.agent_Code,
			              B.agent_Name,
        FROM (
             select ROWNUM RN,
                    C.appntName,
                    C.policyCode,
                    C.organCode,
                    C.finishTime,
                    C.payMode,
                    C.unitNumber,
                    C.feeAmount,
                    C.DOCUMENT_NO,
                    C.agentCode,
                    C.agentName,
                    FROM (
		SELECT DISTINCT 
					TPA.HOLDER_NAME appntName,
					TCM.POLICY_CODE policyCode,
					TCM.ORGAN_CODE organCode,
					TPA.FINISH_TIME finishTime,
					TPA.PAY_MODE payMode,
					TPA.UNIT_NUMBER unitNumber,
					TPA.FEE_AMOUNT feeAmount,
					(select A.DOCUMENT_NO from dev_pas.T_DOCUMENT A where A.POLICY_CODE = TCM.POLICY_CODE)DOCUMENTNO,
					TCA.AGENT_CODE agentCode,
					TCA.AGENT_NAME agentName
				FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
				     APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,
					 APP___PAS__DBUSER.T_PREM_ARAP          TPA
				WHERE 1 = 1
				 AND TCM.POLICY_CODE = TPA.POLICY_CODE
				 AND TCM.POLICY_CODE = TCA.POLICY_CODE
				 AND TCA.IS_CURRENT_AGENT = '1'
				 AND TPA.BUSINESS_TYPE IN ('4003', '1005') ]]>
		<include refid="PA_querytempPremReturn" />
		 <![CDATA[   ) C WHERE ROWNUM <= #{LESS_NUM} ]]>		
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 点击查询按钮获取查询结果方法 -->
	<select id="PA_queryTempPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		      SELECT  MAX(B.POLICY_ID) POLICY_ID,
			          MAX(B.HOLDER_NAME) HOLDER_NAME,
				      MAX(B.POLICY_CODE) POLICY_CODE,
				      MAX(B.ORGAN_CODE) ORGAN_CODE,
				      MAX(B.FINISH_TIME) FINISH_TIME,
				      MAX(B.PAY_MODE) PAY_MODE,
				      MAX(B.PAY_MODE_NAME) PAY_MODE_NAME,
				      MAX(B.UNIT_NUMBER) UNIT_NUMBER,
				      SUM(B.FEE_AMOUNT) FEE_AMOUNT,
				      MAX(B.DUE_TIME) DUE_TIME
		   FROM (
			   SELECT TCM.POLICY_ID POLICY_ID,
			          A.HOLDER_NAME HOLDER_NAME,
				      A.POLICY_CODE POLICY_CODE,
				      A.ORGAN_CODE ORGAN_CODE,
				      A.FINISH_TIME FINISH_TIME,
				      A.PAY_MODE PAY_MODE,
				      (SELECT Z.NAME FROM DEV_PAS.T_PAY_MODE Z WHERE Z.CODE=A.PAY_MODE) PAY_MODE_NAME,
				      A.UNIT_NUMBER UNIT_NUMBER,
				      A.FEE_AMOUNT FEE_AMOUNT,
				      A.DUE_TIME DUE_TIME
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
			       APP___PAS__DBUSER.T_PREM_ARAP A,
			       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
			       APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
			 WHERE TCM.POLICY_CODE = A.POLICY_CODE
			   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
			   AND TCBP.POLICY_CODE=A.POLICY_CODE
			   AND TCBP.BUSI_PROD_CODE=A.BUSI_PROD_CODE
			   AND TCBP.BUSI_ITEM_ID=TCE.BUSI_ITEM_ID
			   AND TCE.PAY_DUE_DATE = A.DUE_TIME
			   AND TCE.PAY_DUE_DATE>A.FINISH_TIME
			   AND A.FEE_STATUS = '01'
			   AND A.ARAP_FLAG = '1'
			   AND A.BUSINESS_TYPE IN ('4003', '1005') 
			   AND A.ORGAN_CODE IN (
					SELECT T.ORGAN_CODE
					  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
					 START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE = t.UPORGAN_CODE) 
			]]>	 
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
			<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
			<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND TRUNC(A.FINISH_TIME) = #{finish_time}]]></if>
			<if test=" current_time != null  and  current_time  != ''  "><![CDATA[ AND A.FINISH_TIME < #{current_time} + 1]]></if>
			<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT  = #{fee_amount} ]]></if>
			<![CDATA[	 
			) B GROUP BY B.POLICY_CODE
			]]>
	</select>
	
	<!-- 暂收费通知书打印查询 -->
	<select id="PA_findProvisionalCollectionRefundPrint" resultType="java.util.Map"
		parameterType="java.util.Map">
      <![CDATA[SELECT MAX(B.DOCUMENT_NO) DOCUMENT_NO,
              MAX(B.APPNTNAME) APPNT_NAME,
              MAX(B.POLICY_CODE) POLICY_CODE,
              MAX(B.ORGAN_CODE) ORGAN_CODE,
              MAX(B.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
              MAX(B.UNIT_NUMBER) UNIT_NUMBER,
              SUM(B.FEE_AMOUNT) FEE_AMOUNT,
              MAX(B.DUE_TIME) DUE_TIME,
              MAX(B.FINISH_TIME) FINISH_TIME,
              MAX(B.AGENT_CODE) AGENT_CODE,
              MAX(B.AGENT_NAME) AGENT_NAME,
              MAX(B.USER_NAME) USER_NAME
       FROM (
         SELECT (SELECT MAX(Z.DOCUMENT_NO)
                  FROM APP___PAS__DBUSER.T_DOCUMENT Z,
                       APP___PAS__DBUSER.T_CLOB     Y
                 WHERE Z.CLOB_ID = Y.CLOB_ID
                   AND Z.POLICY_CODE = TCM.POLICY_CODE
                   AND Z.TEMPLATE_CODE = 'PAS_00022'
                   AND Y.CONTENT LIKE '%' || A.UNIT_NUMBER || '%') DOCUMENT_NO,
              A.HOLDER_NAME APPNTNAME,
              A.POLICY_CODE POLICY_CODE,
              A.ORGAN_CODE ORGAN_CODE,
              A.ROLLBACK_UNIT_NUMBER ROLLBACK_UNIT_NUMBER,
              A.UNIT_NUMBER UNIT_NUMBER,
              A.FEE_AMOUNT FEE_AMOUNT,
			  A.DUE_TIME DUE_TIME,
			  TRUNC(A.FINISH_TIME) FINISH_TIME,
              TCA.AGENT_CODE,
              TCA.AGENT_NAME,
              (SELECT A.USER_NAME 
              FROM APP___PAS__DBUSER.T_UDMP_USER A 
              WHERE A.USER_ID=A.OPERATOR_BY) USER_NAME
        FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
             DEV_PAS.T_CONTRACT_AGENT TCA,
             DEV_PAS.T_PREM_ARAP A
       WHERE TCM.POLICY_CODE = A.POLICY_CODE
         AND TCA.POLICY_CODE=TCM.POLICY_CODE
         AND TCA.IS_CURRENT_AGENT=1
         AND A.FEE_STATUS IN ('00','01')
         AND A.BUSINESS_TYPE IN ('4001') ]]>
         <if test=" fee_amount  != null  and  fee_amount  != ''  "><![CDATA[AND A.FEE_AMOUNT = #{fee_amount}  ]]></if>
         <if test=" policy_code  != null  and  policy_code  != ''  "><![CDATA[AND A.POLICY_CODE = #{policy_code}  ]]></if>
         <if test=" unit_number  != null  and  unit_number  != ''  "><![CDATA[AND A.UNIT_NUMBER = #{unit_number}  ]]></if>
         <if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[AND TRUNC(A.FINISH_TIME) = #{finish_time}  ]]></if>
         <if test=" rollback_unit_number  != null  and  rollback_unit_number  != ''  "><![CDATA[AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number}  ]]></if>
      <![CDATA[   AND A.ORGAN_CODE IN (
         SELECT T.ORGAN_CODE
         FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
         START WITH T.ORGAN_CODE = #{organ_code}
         CONNECT BY PRIOR T.ORGAN_CODE = t.UPORGAN_CODE) 
         ) B  GROUP BY UNIT_NUMBER]]>
	</select>
</mapper>