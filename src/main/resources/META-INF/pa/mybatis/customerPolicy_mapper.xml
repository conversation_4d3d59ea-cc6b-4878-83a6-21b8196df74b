<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.ICustomerPolicyDao">

	<!-- 查询客户保单(建行) -->
	<select id="PA_findCustomerPolicy" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT TCM.POLICY_CODE,TCM.SERVICE_BANK_BRANCH
		  FROM DEV_PAS.T_CONTRACT_MASTER TCM
		  JOIN DEV_PAS.T_POLICY_HOLDER TPH
		    ON TCM.POLICY_CODE = TPH.POLICY_CODE
		  JOIN DEV_PAS.T_CUSTOMER TC
		    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		 WHERE 1 = 1
 		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND TC.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" query_start_date != null and query_start_date != '' "><![CDATA[ AND TCM.APPLY_DATE >= #{query_start_date} ]]></if>
		<if test=" query_end_date != null and query_end_date != '' "><![CDATA[ AND TCM.APPLY_DATE <= #{query_end_date} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TCM.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
	</select>
</mapper>
