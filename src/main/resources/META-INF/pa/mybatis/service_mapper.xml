<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="service">
<!--
	<sql id="PA_serviceWhereCondition">
		<if test=" is_calc != null and is_calc != ''  "><![CDATA[ AND A.IS_CALC = #{is_calc} ]]></if>
		<if test=" is_financial != null and is_financial != ''  "><![CDATA[ AND A.IS_FINANCIAL = #{is_financial} ]]></if>
		<if test=" service_camel_name != null and service_camel_name != ''  "><![CDATA[ AND A.SERVICE_CAMEL_NAME = #{service_camel_name} ]]></if>
		<if test=" back_effect_flag != null and back_effect_flag != ''  "><![CDATA[ AND <PERSON>.BACK_EFFECT_FLAG = #{back_effect_flag} ]]></if>
		<if test=" auto_sign_flag != null and auto_sign_flag != ''  "><![CDATA[ AND A.AUTO_SIGN_FLAG = #{auto_sign_flag} ]]></if>
		<if test=" multi_policy_flag != null and multi_policy_flag != ''  "><![CDATA[ AND A.MULTI_POLICY_FLAG = #{multi_policy_flag} ]]></if>
		<if test=" gl_source != null and gl_source != ''  "><![CDATA[ AND A.GL_SOURCE = #{gl_source} ]]></if>
		<if test=" service_pinyin != null and service_pinyin != ''  "><![CDATA[ AND A.SERVICE_PINYIN = #{service_pinyin} ]]></if>
		<if test=" rollback_jump_flag != null and rollback_jump_flag != ''  "><![CDATA[ AND A.ROLLBACK_JUMP_FLAG = #{rollback_jump_flag} ]]></if>
		<if test=" back_charge != null and back_charge != ''  "><![CDATA[ AND A.BACK_CHARGE = #{back_charge} ]]></if>
		<if test=" page_order  != null "><![CDATA[ AND A.PAGE_ORDER = #{page_order} ]]></if>
		<if test=" check_policy_flag != null and check_policy_flag != ''  "><![CDATA[ AND A.CHECK_POLICY_FLAG = #{check_policy_flag} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" service_name != null and service_name != ''  "><![CDATA[ AND A.SERVICE_NAME = #{service_name} ]]></if>
		<if test=" password_flag != null and password_flag != ''  "><![CDATA[ AND A.PASSWORD_FLAG = #{password_flag} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryServiceByServiceCodeCondition">
		<if test=" service_code != null and service_code != '' "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addService"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SERVICE(
				IS_CALC, IS_FINANCIAL, SERVICE_CAMEL_NAME, BACK_EFFECT_FLAG, AUTO_SIGN_FLAG, MULTI_POLICY_FLAG, GL_SOURCE, 
				SERVICE_PINYIN, ROLLBACK_JUMP_FLAG, BACK_CHARGE, PAGE_ORDER, CHECK_POLICY_FLAG, SERVICE_CODE, SERVICE_NAME, 
				PASSWORD_FLAG ) 
			VALUES (
				#{is_calc, jdbcType=VARCHAR}, #{is_financial, jdbcType=VARCHAR} , #{service_camel_name, jdbcType=VARCHAR} , #{back_effect_flag, jdbcType=VARCHAR} , #{auto_sign_flag, jdbcType=VARCHAR} , #{multi_policy_flag, jdbcType=VARCHAR} , #{gl_source, jdbcType=VARCHAR} 
				, #{service_pinyin, jdbcType=VARCHAR} , #{rollback_jump_flag, jdbcType=VARCHAR} , #{back_charge, jdbcType=VARCHAR} , #{page_order, jdbcType=NUMERIC} , #{check_policy_flag, jdbcType=VARCHAR} , #{service_code, jdbcType=VARCHAR} , #{service_name, jdbcType=VARCHAR} 
				, #{password_flag, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteService" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SERVICE WHERE SERVICE_CODE=#{service_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateService" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SERVICE ]]>
		<set>
		<trim suffixOverrides=",">
			IS_CALC = #{is_calc, jdbcType=VARCHAR} ,
			IS_FINANCIAL = #{is_financial, jdbcType=VARCHAR} ,
			SERVICE_CAMEL_NAME = #{service_camel_name, jdbcType=VARCHAR} ,
			BACK_EFFECT_FLAG = #{back_effect_flag, jdbcType=VARCHAR} ,
			AUTO_SIGN_FLAG = #{auto_sign_flag, jdbcType=VARCHAR} ,
			MULTI_POLICY_FLAG = #{multi_policy_flag, jdbcType=VARCHAR} ,
			GL_SOURCE = #{gl_source, jdbcType=VARCHAR} ,
			SERVICE_PINYIN = #{service_pinyin, jdbcType=VARCHAR} ,
			ROLLBACK_JUMP_FLAG = #{rollback_jump_flag, jdbcType=VARCHAR} ,
			BACK_CHARGE = #{back_charge, jdbcType=VARCHAR} ,
		    PAGE_ORDER = #{page_order, jdbcType=NUMERIC} ,
			CHECK_POLICY_FLAG = #{check_policy_flag, jdbcType=VARCHAR} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
			SERVICE_NAME = #{service_name, jdbcType=VARCHAR} ,
			PASSWORD_FLAG = #{password_flag, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findServiceByServiceCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_CALC, A.IS_FINANCIAL, A.SERVICE_CAMEL_NAME, A.BACK_EFFECT_FLAG, A.AUTO_SIGN_FLAG, A.MULTI_POLICY_FLAG, A.GL_SOURCE, 
			A.SERVICE_PINYIN, A.ROLLBACK_JUMP_FLAG, A.BACK_CHARGE, A.PAGE_ORDER, A.CHECK_POLICY_FLAG, A.SERVICE_CODE, A.SERVICE_NAME, 
			A.PASSWORD_FLAG FROM APP___PAS__DBUSER.T_SERVICE A WHERE 1 = 1  ]]>
		<include refid="PA_queryServiceByServiceCodeCondition" />
	</select>
<!-- 按索引查询操作 -->	
	<select id="PA_findService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.SERVICE_CODE, A.SERVICE_NAME FROM APP___PAS__DBUSER.T_SERVICE A WHERE 1 = 1 and A.SERVICE_CODE = #{service_code} ]]>
		
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_CALC, A.IS_FINANCIAL, A.SERVICE_CAMEL_NAME, A.BACK_EFFECT_FLAG, A.AUTO_SIGN_FLAG, A.MULTI_POLICY_FLAG, A.GL_SOURCE, 
			A.SERVICE_PINYIN, A.ROLLBACK_JUMP_FLAG, A.BACK_CHARGE, A.PAGE_ORDER, A.CHECK_POLICY_FLAG, A.SERVICE_CODE, A.SERVICE_NAME, 
			A.PASSWORD_FLAG FROM APP___PAS__DBUSER.T_SERVICE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_CALC, A.IS_FINANCIAL, A.SERVICE_CAMEL_NAME, A.BACK_EFFECT_FLAG, A.AUTO_SIGN_FLAG, A.MULTI_POLICY_FLAG, A.GL_SOURCE, 
			A.SERVICE_PINYIN, A.ROLLBACK_JUMP_FLAG, A.BACK_CHARGE, A.PAGE_ORDER, A.CHECK_POLICY_FLAG, A.SERVICE_CODE, A.SERVICE_NAME, 
			A.PASSWORD_FLAG FROM APP___PAS__DBUSER.T_SERVICE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findServiceTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SERVICE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryServiceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.IS_CALC, B.IS_FINANCIAL, B.SERVICE_CAMEL_NAME, B.BACK_EFFECT_FLAG, B.AUTO_SIGN_FLAG, B.MULTI_POLICY_FLAG, B.GL_SOURCE, 
			B.SERVICE_PINYIN, B.ROLLBACK_JUMP_FLAG, B.BACK_CHARGE, B.PAGE_ORDER, B.CHECK_POLICY_FLAG, B.SERVICE_CODE, B.SERVICE_NAME, 
			B.PASSWORD_FLAG FROM (
					SELECT ROWNUM RN, A.IS_CALC, A.IS_FINANCIAL, A.SERVICE_CAMEL_NAME, A.BACK_EFFECT_FLAG, A.AUTO_SIGN_FLAG, A.MULTI_POLICY_FLAG, A.GL_SOURCE, 
			A.SERVICE_PINYIN, A.ROLLBACK_JUMP_FLAG, A.BACK_CHARGE, A.PAGE_ORDER, A.CHECK_POLICY_FLAG, A.SERVICE_CODE, A.SERVICE_NAME, 
			A.PASSWORD_FLAG FROM APP___PAS__DBUSER.T_SERVICE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
