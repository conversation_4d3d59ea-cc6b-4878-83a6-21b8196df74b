<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractInvestRateLogDao">
<!--
	<sql id="PA_contractInvestRateLogWhereCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" prem_type != null and prem_type != ''  "><![CDATA[ AND A.PREM_TYPE = #{prem_type} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" assign_rate  != null "><![CDATA[ AND A.ASSIGN_RATE = #{assign_rate} ]]></if>
		<if test=" low_ply_year  != null "><![CDATA[ AND A.LOW_PLY_YEAR = #{low_ply_year} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" high_ply_year  != null "><![CDATA[ AND A.HIGH_PLY_YEAR = #{high_ply_year} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractInvestRateLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractInvestRateLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_INVEST_RATE_LOG__LO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG(
				INSERT_TIME, UPDATE_TIME, ITEM_ID, ACCOUNT_CODE, INSERT_TIMESTAMP, LOG_ID, UPDATE_BY, 
				PREM_TYPE, LIST_ID, ASSIGN_RATE, LOW_PLY_YEAR, UPDATE_TIMESTAMP, POLICY_CHG_ID, LOG_TYPE, 
				HIGH_PLY_YEAR, BUSI_ITEM_ID, INSERT_BY, POLICY_ID,GURNT_START_DATE,GURNT_PERIOD,SETTLE_METHOD,GURNT_RATE,GURNT_PERD_TYPE ) 
			VALUES (
				SYSDATE, SYSDATE , #{item_id, jdbcType=NUMERIC} , #{account_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} 
				, #{prem_type, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{assign_rate, jdbcType=NUMERIC} , #{low_ply_year, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{log_type, jdbcType=VARCHAR} 
				, #{high_ply_year, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC},#{gurnt_start_date, jdbcType=DATE},#{gurnt_period, jdbcType=NUMERIC},#{settle_method, jdbcType=NUMERIC},#{gurnt_rate, jdbcType=NUMERIC},#{gurnt_perd_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractInvestRateLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractInvestRateLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			PREM_TYPE = #{prem_type, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    ASSIGN_RATE = #{assign_rate, jdbcType=NUMERIC} ,
		    LOW_PLY_YEAR = #{low_ply_year, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    HIGH_PLY_YEAR = #{high_ply_year, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractInvestRateLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, 
			A.PREM_TYPE, A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.POLICY_CHG_ID, A.LOG_TYPE, 
			A.HIGH_PLY_YEAR, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestRateLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractInvestRateLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, 
			A.PREM_TYPE, A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.POLICY_CHG_ID, A.LOG_TYPE, 
			A.HIGH_PLY_YEAR, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractInvestRateLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, 
			A.PREM_TYPE, A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.POLICY_CHG_ID, A.LOG_TYPE, 
			A.HIGH_PLY_YEAR, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractInvestRateLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractInvestRateLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ITEM_ID, B.ACCOUNT_CODE, B.LOG_ID, 
			B.PREM_TYPE, B.LIST_ID, B.ASSIGN_RATE, B.LOW_PLY_YEAR, B.POLICY_CHG_ID, B.LOG_TYPE, 
			B.HIGH_PLY_YEAR, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, 
			A.PREM_TYPE, A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.POLICY_CHG_ID, A.LOG_TYPE, 
			A.HIGH_PLY_YEAR, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
