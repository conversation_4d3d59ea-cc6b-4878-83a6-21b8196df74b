<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ilpRegularPremCx">
<!--
	<sql id="PA_ilpRegularPremCxWhereCondition">
		<if test=" change_seq  != null "><![CDATA[ AND A.CHANGE_SEQ = #{change_seq} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND <PERSON><PERSON>POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" oper_type != null and oper_type != ''  "><![CDATA[ AND A.OPER_TYPE = #{oper_type} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" pre_log_id  != null "><![CDATA[ AND A.PRE_LOG_ID = #{pre_log_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryIlpRegularPremCxByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="PA_queryIlpRegularPremCxByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryIlpRegularPremCxByOperTypeCondition">
		<if test=" oper_type != null and oper_type != '' "><![CDATA[ AND A.OPER_TYPE = #{oper_type} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addIlpRegularPremCx"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX(
				INSERT_TIME, CHANGE_SEQ, UPDATE_TIME, ITEM_ID, LOG_ID, INSERT_TIMESTAMP, UPDATE_BY, 
				LIST_ID, UPDATE_TIMESTAMP, POLICY_CHG_ID, LOG_TYPE, OPER_TYPE, INSERT_BY, POLICY_ID, 
				PRE_LOG_ID ) 
			VALUES (
				SYSDATE, #{change_seq, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{log_type, jdbcType=VARCHAR} , #{oper_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{pre_log_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteIlpRegularPremCx" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX WHERE 1 = 1 ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateIlpRegularPremCx" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX ]]>
		<set>
		<trim suffixOverrides=",">
		    CHANGE_SEQ = #{change_seq, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
			OPER_TYPE = #{oper_type, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PRE_LOG_ID = #{pre_log_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findIlpRegularPremCxByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.ITEM_ID, A.LOG_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, 
			A.PRE_LOG_ID FROM APP___PAS__DBUSER.APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRegularPremCxByPolicyChgIdCondition" />
	</select>
	
	<select id="PA_findIlpRegularPremCxByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.ITEM_ID, A.LOG_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, 
			A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRegularPremCxByListIdCondition" />
	</select>
	
	<select id="PA_findIlpRegularPremCxByOperType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.ITEM_ID, A.LOG_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, 
			A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRegularPremCxByOperTypeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapIlpRegularPremCx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.ITEM_ID, A.LOG_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, 
			A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllIlpRegularPremCx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_SEQ, A.ITEM_ID, A.LOG_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, 
			A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findIlpRegularPremCxTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryIlpRegularPremCxForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CHANGE_SEQ, B.ITEM_ID, B.LOG_ID, 
			B.LIST_ID, B.POLICY_CHG_ID, B.LOG_TYPE, B.OPER_TYPE, B.POLICY_ID, 
			B.PRE_LOG_ID FROM (
					SELECT ROWNUM RN, A.CHANGE_SEQ, A.ITEM_ID, A.LOG_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, 
			A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_ILP_REGULAR_PREM_CX A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
