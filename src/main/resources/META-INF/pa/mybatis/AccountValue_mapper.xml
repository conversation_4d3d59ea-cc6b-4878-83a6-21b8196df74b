<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.batchforenci.dao.impl.AccountValueDaoImpl">
	
	<!-- 查询投连险账户价值-->
	<select id="findUniversalAccount" resultType="java.util.Map" parameterType="java.util.Map">		
	<![CDATA[ 
     SELECT TCBP.POLICY_ID,
            TCBP.POLICY_CODE,
            TCBP.BUSI_PROD_CODE  RICKCODE,
            TCBP.VALIDATE_DATE   VALIDDATE,
            TCBP.BUSI_ITEM_ID,
            TCBP.LIABILITY_STATE POLICYSTATE,
            ROWNUM
       FROM DEV_PDS.T_BUSINESS_PRODUCT   TBP,
            DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
            DEV_PAS.T_CONTRACT_MASTER    TCM
      WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
        AND TCBP.LIABILITY_STATE = '1'
        AND TCM.POLICY_CODE = TCBP.POLICY_CODE
        AND TCM.CHANNEL_TYPE = '10'
 		]]> 					
			
        <if test=" batch_Date != null and batch_Date !='' ">
        	<![CDATA[ AND  tcbp.validate_date <= #{batch_Date,jdbcType=VARCHAR} ]]>
        </if>
        <if test=" product_category1 != null and product_category1 != ''  ">
 			<![CDATA[ and tbp.product_category1 = #{product_category1} ]]>
 		</if>
 		<if test=" rickCode != null and rickCode != ''  "><![CDATA[ and tcbp.busi_prod_code = #{rickCode,jdbcType=VARCHAR} ]]></if>
 				
   		<![CDATA[
			AND MOD(tcbp.POLICY_ID, #{counts,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
		]]>
	</select>
	<!-- 查询投连险账户价值数量 -->
		<select id="findUniversalAccountCount" resultType="java.lang.Integer" parameterType="java.util.Map">		
	<![CDATA[ 
		SELECT COUNT(TCBP.POLICY_CODE)
  	FROM DEV_PDS.T_BUSINESS_PRODUCT TBP, DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
    dev_pas.t_contract_master tcm
 	WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
   AND TCBP.LIABILITY_STATE = '1'
   and tcm.policy_code = tcbp.policy_code
   and tcm.channel_type = '10']]>
		<if test=" batch_Date != null and batch_Date !='' "><![CDATA[ AND  tcbp.validate_date <= #{batch_Date,jdbcType=VARCHAR} ]]></if>
 		<if test=" product_category1 != null and product_category1 != ''  "><![CDATA[ and  tbp.product_category1 = #{product_category1,jdbcType=VARCHAR} ]]></if>
 		<if test=" rickCode != null and rickCode != ''  "><![CDATA[ and tcbp.busi_prod_code = #{rickCode,jdbcType=VARCHAR} ]]></if>

	</select>
	<!-- 投连险前天交易金额 -->
		<select id="findUniversalAccountYesterday" resultType="java.util.Map" parameterType="java.util.Map">		
	<![CDATA[ 
		SELECT TFT.TRANS_TYPE,TFT.TRANS_AMOUNT,TFT.DEAL_TIME FROM DEV_PAS.T_FUND_TRANS TFT 
		WHERE  TFT.TRANS_TYPE IN ('1','2','3') AND ROWNUM < 1000
	]]>
	 <if test=" policy_id != null and policy_id != ''  "><![CDATA[ and tft.policy_id = #{policy_id,jdbcType=VARCHAR} ]]></if>
	 <if test=" deal_time != null and deal_time != ''  "><![CDATA[ and  tft.deal_time = #{deal_time,jdbcType=VARCHAR} ]]></if>
	<![CDATA[ order by tft.deal_time desc ]]>
	</select>
	
</mapper>