<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.impl.hessianinterface.dao.impl.ProvideCustomerInfosForBancasBusinessForNBDaoImpl">

 <!-- 查询保单信息 -->
   <select id="PA_findCustomerInfoByIssueDateAndOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT B.CUSTOMER_ID, ROWNUM
				  FROM (WITH CM AS (SELECT POLICY_ID
				                      FROM DEV_PAS.T_CONTRACT_MASTER
				                     WHERE SUBMIT_CHANNEL = #{submit_channel}
				                       AND ISSUE_DATE >= #{coverage_start_date}
				                       AND ISSUE_DATE < #{coverage_end_date}
				                       AND ORGAN_CODE IN (SELECT A.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL A START WITH A.ORGAN_CODE = #{organ_code} CONNECT BY PRIOR A.ORGAN_CODE = A.UPORGAN_CODE))
				         SELECT IL.CUSTOMER_ID
				           FROM DEV_PAS.T_INSURED_LIST IL
				          INNER JOIN CM
				             ON IL.POLICY_ID = CM.POLICY_ID
				          INNER JOIN DEV_PAS.T_CUSTOMER C
				             ON C.CUSTOMER_ID = IL.CUSTOMER_ID
				          WHERE C.CUST_CERT_STAR_DATE IS NOT NULL
				            AND C.CUST_CERT_END_DATE IS NOT NULL
				         UNION ALL
				         SELECT PH.CUSTOMER_ID
				           FROM DEV_PAS.T_POLICY_HOLDER PH
				          INNER JOIN CM
				             ON PH.POLICY_ID = CM.POLICY_ID
				          INNER JOIN DEV_PAS.T_CUSTOMER C
				             ON C.CUSTOMER_ID = PH.CUSTOMER_ID
				          WHERE C.CUST_CERT_STAR_DATE IS NOT NULL
				            AND C.CUST_CERT_END_DATE IS NOT NULL
				         UNION ALL
				         SELECT CB.CUSTOMER_ID
				           FROM DEV_PAS.T_CONTRACT_BENE CB
				          INNER JOIN CM
				             ON CB.POLICY_ID = CM.POLICY_ID
				          INNER JOIN DEV_PAS.T_CUSTOMER C
				             ON C.CUSTOMER_ID = CB.CUSTOMER_ID
				          WHERE CB.BENE_TYPE = 1
				            AND C.CUST_CERT_STAR_DATE IS NOT NULL
				            AND C.CUST_CERT_END_DATE IS NOT NULL) B ]]>
	</select>

</mapper>
