<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRiskAmountArchiveDao">

	<sql id="riskAmountArchiveWhereCondition">
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" change_cause != null and change_cause != ''  "><![CDATA[ AND A.CHANGE_CAUSE = #{change_cause} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" archive_id  != null "><![CDATA[ AND A.ARCHIVE_ID = #{archive_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" change_time  != null  and  change_time  != ''  "><![CDATA[ AND A.CHANGE_TIME = #{change_time} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT = #{risk_amount} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	

<!-- 添加操作 -->
	<insert id="addRiskAmountArchive"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="archive_id">
			SELECT APP___PAS__DBUSER.S_RISK_AMOUNT_ARCHIVE__ARCHIVE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE(
				BUSINESS_CODE, INSERT_TIME, CHANGE_CAUSE, CUSTOMER_ID, UPDATE_TIME, START_TIME, ITEM_ID, 
				RISK_TYPE, BUSI_PROD_CODE, APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, ARCHIVE_ID, 
				LIST_ID, CHANGE_TIME, UPDATE_TIMESTAMP, END_TIME, INSERT_BY, BUSI_ITEM_ID, POLICY_ID, 
				INTERNAL_CODE, RISK_AMOUNT ) 
			VALUES (
				#{business_code, jdbcType=VARCHAR}, SYSDATE , #{change_cause, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{start_time, jdbcType=DATE} , #{item_id, jdbcType=NUMERIC} 
				, #{risk_type, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{archive_id, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , #{change_time, jdbcType=DATE} , CURRENT_TIMESTAMP, #{end_time, jdbcType=DATE} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{internal_code, jdbcType=VARCHAR} , #{risk_amount, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRiskAmountArchive" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE WHERE ARCHIVE_ID = #{archive_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateRiskAmountArchive" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE ]]>
		<set>
		<trim suffixOverrides=",">
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
			CHANGE_CAUSE = #{change_cause, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    START_TIME = #{start_time, jdbcType=DATE} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			RISK_TYPE = #{risk_type, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    CHANGE_TIME = #{change_time, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			INTERNAL_CODE = #{internal_code, jdbcType=VARCHAR} ,
		    RISK_AMOUNT = #{risk_amount, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE ARCHIVE_ID = #{archive_id} ]]>
	</update>

<!-- 按索引查询操作 -->	

<!-- 按map查询操作 -->
	<select id="findAllMapRiskAmountArchive" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.CHANGE_CAUSE, A.CUSTOMER_ID, A.START_TIME, A.ITEM_ID, 
			A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.POLICY_CODE, A.ARCHIVE_ID, 
			A.LIST_ID, A.CHANGE_TIME, A.END_TIME, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ARCHIVE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRiskAmountArchive" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.CHANGE_CAUSE, A.CUSTOMER_ID, A.START_TIME, A.ITEM_ID, 
			A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.POLICY_CODE, A.ARCHIVE_ID, 
			A.LIST_ID, A.CHANGE_TIME, A.END_TIME, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ARCHIVE_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findRiskAmountArchiveTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryRiskAmountArchiveForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSINESS_CODE, B.CHANGE_CAUSE, B.CUSTOMER_ID, B.START_TIME, B.ITEM_ID, 
			B.RISK_TYPE, B.BUSI_PROD_CODE, B.APPLY_CODE, B.POLICY_CODE, B.ARCHIVE_ID, 
			B.LIST_ID, B.CHANGE_TIME, B.END_TIME, B.BUSI_ITEM_ID, B.POLICY_ID, 
			B.INTERNAL_CODE, B.RISK_AMOUNT FROM (
					SELECT ROWNUM RN, A.BUSINESS_CODE, A.CHANGE_CAUSE, A.CUSTOMER_ID, A.START_TIME, A.ITEM_ID, 
			A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.POLICY_CODE, A.ARCHIVE_ID, 
			A.LIST_ID, A.CHANGE_TIME, A.END_TIME, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ARCHIVE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
