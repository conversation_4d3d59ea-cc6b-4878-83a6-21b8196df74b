<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IAccountCheckDao">

	<sql id="accountCheckDao">
		<if test=" policy_code  != null "><![CDATA[ AND APP___PAS__DBUSER.t_contract_master.policy_code = #{policy_code} ]]></if>
	</sql>

	<!-- 查询所有操作 -->
	<select id="findAllAccountCheck" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT TCM.POLICY_CODE,
      			 	TPA.ACCOUNT_TYPE,
     				TPA.INTEREST_CAPITAL,
       				TPA.UPDATE_TIME
  			FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT TPA, APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
 			WHERE TPA.POLICY_ID = TCM.POLICY_ID
   				AND TCM.POLICY_CODE =  #{policy_code}
   				AND TPA.INTEREST_CAPITAL <> 0
   				AND TPA.ACCOUNT_TYPE IN ('2', '4', '5', '11', '13','12')
   				ORDER BY TPA.ACCOUNT_TYPE DESC,TPA.UPDATE_TIME DESC]]>
	</select>
</mapper>