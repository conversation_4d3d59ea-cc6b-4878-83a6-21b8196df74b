<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyIncomeQueryDao">

	<select id="findBusiProdInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT 
		A.LIABILITY_STATE AS LIABILITY_STATE_MASTER,
		C.PRODUCT_CATEGORY1,
		B.POLICY_CODE,
		B.BUSI_ITEM_ID,
		B.MASTER_BUSI_ITEM_ID,
		A.APPLY_DATE AS MASTER_APPLY_DATE,
		B.APPLY_DATE,B.BUSI_PROD_CODE,B.POLICY_ID
		FROM DEV_PAS.T_CONTRACT_MASTER A, DEV_PAS.T_CONTRACT_BUSI_PROD B , DEV_PDS.T_BUSINESS_PRODUCT C
		WHERE A.POLICY_CODE = B.POLICY_CODE AND B.BUSI_PRD_ID = C.BUSINESS_PRD_ID
		]]>
		<if test=" policy_code  != null and policy_code!= ''">
				<![CDATA[AND A.POLICY_CODE =#{policy_code} ]]>
		</if>
	</select>


	<select id="findBouns" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT G.BONUS_ALLOT,
       G.ALLOCATE_DUE_DATE,
       G.BONUS_SA,
       G.BONUS_SA_SUM,
       G.CASH_BONUS + G.REISSUE_INTEREST AS CASH_BONUS,
       G.CASH_BONUS_SUM + G.REISSUE_INTEREST_SUM AS CASH_BONUS_SUM
  FROM (SELECT ROWNUM AS RN,
               T.BONUS_ALLOT, --分红类型
               T.ALLOCATE_DUE_DATE, --分红年度
               SUM(T.BONUS_SA) AS BONUS_SA, --分红保额
               (SELECT SUM(A.BONUS_SA)
                  FROM DEV_PAS.T_CONTRACT_PRODUCT A
                 WHERE A.POLICY_CODE = T.POLICY_CODE
                   AND A.BUSI_ITEM_ID = T.BUSI_ITEM_ID) AS BONUS_SA_SUM, -- 累计保额
               SUM(T.CASH_BONUS) AS CASH_BONUS,
               SUM(T.REISSUE_INTEREST) AS REISSUE_INTEREST,
               (SELECT SUM(A.CASH_BONUS)
                  FROM DEV_PAS.T_BONUS_ALLOCATE A
                 WHERE A.POLICY_CODE = T.POLICY_CODE
                   AND A.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                   AND T.BONUS_ALLOT = '4') AS CASH_BONUS_SUM,
               (SELECT SUM(A.REISSUE_INTEREST)
                  FROM DEV_PAS.T_BONUS_ALLOCATE A
                 WHERE A.POLICY_CODE = T.POLICY_CODE
                   AND A.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                   AND T.BONUS_ALLOT = '4') AS REISSUE_INTEREST_SUM
          FROM DEV_PAS.T_BONUS_ALLOCATE T
         WHERE  T.ALLOCATE_DUE_DATE IS NOT NULL]]>
		<if test=" policy_code  != null and policy_code != ''">
				<![CDATA[AND T.POLICY_CODE =#{policy_code} ]]>
		</if>
		<if test=" busi_item_id  != null and busi_item_id != ''">
				<![CDATA[ AND T.BUSI_ITEM_ID = #{busi_item_id} ]]>
		</if>
           <![CDATA[AND T.BONUS_ALLOT IN ('1', '4')
         GROUP BY ROWNUM,
                  T.BONUS_ALLOT,
                  T.ALLOCATE_DUE_DATE,
                  T.POLICY_CODE,
                  T.BUSI_ITEM_ID,
                  T.BONUS_ALLOT
         ORDER BY T.ALLOCATE_DUE_DATE DESC) G
 			WHERE G.RN = 1]]>
	</select>
</mapper>