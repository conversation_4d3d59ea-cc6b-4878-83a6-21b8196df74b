<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.dao.IYesNoDao">
<!--
	<sql id="yesNoWhereCondition">
		<if test=" type_name != null and type_name != ''  "><![CDATA[ AND A.TYPE_NAME = #{type_name} ]]></if>
		<if test=" yes_no  != null "><![CDATA[ AND A.YES_NO = #{yes_no} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryYesNoByYesNoCondition">
		<if test=" yes_no  != null "><![CDATA[ AND A.YES_NO = #{yes_no} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addYesNo"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_YES_NO(
				TYPE_NAME, YES_NO ) 
			VALUES (
				#{type_name, jdbcType=VARCHAR}, #{yes_no, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteYesNo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_YES_NO WHERE YES_NO = #{yes_no} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateYesNo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_YES_NO ]]>
		<set>
		<trim suffixOverrides=",">
			TYPE_NAME = #{type_name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE YES_NO = #{yes_no} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findYesNoByYesNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.YES_NO FROM APP___PAS__DBUSER.T_YES_NO A WHERE 1 = 1  ]]>
		<include refid="queryYesNoByYesNoCondition" />
		<![CDATA[ ORDER BY A.YES_NO ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapYesNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.YES_NO FROM APP___PAS__DBUSER.T_YES_NO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.YES_NO ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllYesNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.YES_NO FROM APP___PAS__DBUSER.T_YES_NO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.YES_NO DESC ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findYesNoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_YES_NO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryYesNoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TYPE_NAME, B.YES_NO FROM (
					SELECT ROWNUM RN, A.TYPE_NAME, A.YES_NO FROM APP___PAS__DBUSER.T_YES_NO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.YES_NO ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
