<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBenefitInsuredLogDao">
<!--
	<sql id="PA_benefitInsuredLogWhereCondition">
		<if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND A.RELATION_TO_INSURED_1 = #{relation_to_insured_1} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" job_underwrite != null and job_underwrite != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{job_underwrite} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryBenefitInsuredLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addBenefitInsuredLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_BENEFIT_INSURED_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG(
				RELATION_TO_INSURED_1, INSERT_TIME, PRODUCT_CODE, UPDATE_TIME, INSURED_ID, INSERT_TIMESTAMP, 
				LOG_ID, ORDER_ID, POLICY_CODE, UPDATE_BY, JOB_UNDERWRITE, LIST_ID, UPDATE_TIMESTAMP, 
				LOG_TYPE, POLICY_CHG_ID, BUSI_ITEM_ID, INSERT_BY, POLICY_ID ) 
			VALUES (
				#{relation_to_insured_1, jdbcType=VARCHAR}, SYSDATE , #{product_code, jdbcType=VARCHAR} , SYSDATE , #{insured_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{log_id, jdbcType=NUMERIC} , #{order_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{job_underwrite, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteBenefitInsuredLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateBenefitInsuredLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			RELATION_TO_INSURED_1 = #{relation_to_insured_1, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
		    ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findBenefitInsuredLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, 
			A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryBenefitInsuredLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapBenefitInsuredLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, 
			A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllBenefitInsuredLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,
			A.LOG_ID, A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, 
			A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findBenefitInsuredLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryBenefitInsuredLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATION_TO_INSURED_1, B.PRODUCT_CODE, B.INSURED_ID,
			B.LOG_ID, B.ORDER_ID, B.POLICY_CODE, B.JOB_UNDERWRITE, B.LIST_ID, 
			B.LOG_TYPE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,
			A.LOG_ID, A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, 
			A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
