<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.ServiceChargeReconciliationDaoImpl">

   <!-- 查询保单信息 -->
   <select id="PA_findPolicyInfosByRequestInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT TA.POLICY_CODE,
				       TA.APPLY_CODE,
				       (SELECT CBP.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP WHERE CBP.POLICY_CODE = TA.POLICY_CODE AND CBP.MASTER_BUSI_ITEM_ID IS NULL AND ROWNUM = 1) BUSI_PROD_CODE 
				  FROM (SELECT DISTINCT CM.POLICY_CODE, 
				                        CM.APPLY_CODE
						  FROM DEV_PAS.T_CONTRACT_MASTER        CM,
						       DEV_PAS.T_POLICY_ACKNOWLEDGEMENT PA,
						       DEV_PAS.T_CONTRACT_AGENT         CA
						 WHERE CM.POLICY_ID = PA.POLICY_ID
						   AND CM.POLICY_ID = CA.POLICY_ID
						   AND PA.ACKNOWLEDGE_DATE = #{acknowledge_date}
						   AND CM.LIABILITY_STATE = 1
						   AND CA.IS_NB_AGENT = 1
						   AND CA.CHANNEL_TYPE = #{channel_type}
						   AND CM.SERVICE_BANK = #{service_bank} ]]>
						<if test=" policy_list != null and policy_list.size()>0">
				        <![CDATA[ AND CM.POLICY_CODE IN ( ]]>
						<foreach collection ="policy_list" item="policy_code" index="index" open="" close="" separator=",">
						<![CDATA[#{policy_code}]]>
						</foreach>	
						<![CDATA[ ) ]]>
						</if>
			  <![CDATA[ ) TA ]]>
	</select>
	
	
   <!-- 查询t_lacommision“轧帐表”中的直接手续费 -->
   <select id="PA_findLacommisionTableByCondition" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		SELECT SUM(LAC.DIRECT_FEE) DIRECT_FEE
		  FROM DEV_PAS.T_LACOMMISION LAC
		 WHERE LAC.CONTNO = #{policy_code}
		   AND LAC.RISKCODE = #{busi_prod_code}
		   AND LAC.PREM_ARAP_LIST_ID IN ( ]]>
		   <foreach collection ="list_id" item="list_id" index="index" open="" close="" separator=",">
			   <![CDATA[#{list_id}]]>
		   </foreach>
		   <![CDATA[ ) ]]>
   </select>

</mapper>