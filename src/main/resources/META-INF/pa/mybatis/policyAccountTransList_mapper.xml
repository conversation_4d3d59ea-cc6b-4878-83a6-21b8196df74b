<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyAccountTransListDao">

	<sql id="policyAccountTransListWhereCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" trans_time  != null  and  trans_time  != ''  "><![CDATA[ AND A.TRANS_TIME = #{trans_time} ]]></if>
		<if test=" trans_amount  != null "><![CDATA[ AND A.TRANS_AMOUNT = #{trans_amount} ]]></if>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
		<if test=" trans_type  != null "><![CDATA[ AND A.TRANS_TYPE = #{trans_type} ]]></if>
		<if test=" interest_rate  != null "><![CDATA[ AND A.INTEREST_RATE = #{interest_rate} ]]></if>
		<if test=" account_balance  != null "><![CDATA[ AND A.account_balance = #{account_balance} ]]></if>
		<if test=" trans_policy_code != null and trans_policy_code != '' "><![CDATA[ AND A.TRANS_POLICY_CODE = #{trans_policy_code} ]]></if>
		<if test=" trans_amount_type != null and trans_amount_type != '' "><![CDATA[ AND A.TRANS_AMOUNT_TYPE = #{trans_amount_type} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryPolicyAccountTransListByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryPolicyAccountTransListByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="queryPolicyAccountTransListByTransCodeCondition">
		<if test=" trans_code != null and trans_code != '' "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
	</sql>	
	<sql id="queryPolicyAccountTransListByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="queryPolicyAccountTransListByTransTimeCondition">
		<if test=" trans_time  != null "><![CDATA[ AND A.TRANS_TIME = #{trans_time} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPolicyAccountTransList"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PAYER_ACCOUNT__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST(TRANS_POLICY_CODE,TRANS_AMOUNT_TYPE,TRANS_TYPE,
				ACCOUNT_ID, INSERT_TIME, UNIT_NUMBER, TRANS_TIME, UPDATE_TIME, INSERT_TIMESTAMP, TRANS_AMOUNT, 
				INTEREST_RATE,TRANS_CODE, UPDATE_BY, LIST_ID, POLICY_CHG_ID, UPDATE_TIMESTAMP, INSERT_BY, STREAM_ID, ACCOUNT_BALANCE ) 
			VALUES (
				#{trans_policy_code, jdbcType=VARCHAR} ,#{trans_amount_type, jdbcType=VARCHAR} ,#{trans_type, jdbcType=VARCHAR},#{account_id, jdbcType=NUMERIC}, SYSDATE , #{unit_number, jdbcType=VARCHAR} , #{trans_time, jdbcType=DATE} , SYSDATE , CURRENT_TIMESTAMP, #{trans_amount, jdbcType=NUMERIC}, 
				#{interest_rate, jdbcType=NUMERIC}, #{trans_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{stream_id, jdbcType=NUMERIC}
				 , #{account_balance, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePolicyAccountTransList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePolicyAccountTransList" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST ]]>
		<set>
		<trim suffixOverrides=",">
			TRANS_POLICY_CODE = #{trans_policy_code, jdbcType=VARCHAR},
		    TRANS_AMOUNT_TYPE = #{trans_amount_type, jdbcType=VARCHAR},
			TRANS_TYPE = #{trans_type, jdbcType=VARCHAR},
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
		    TRANS_TIME = #{trans_time, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    TRANS_AMOUNT = #{trans_amount, jdbcType=NUMERIC} ,
			TRANS_CODE = #{trans_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    STREAM_ID = #{stream_id, jdbcType=NUMERIC} ,
		    ACCOUNT_BALANCE = #{account_balance, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPolicyAccountTransListByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE 1 = 1  ]]>
		<include refid="queryPolicyAccountTransListByListIdCondition" />
	</select>
	
	<select id="findPolicyAccountTransListByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE 1 = 1  ]]>
		<include refid="queryPolicyAccountTransListByAccountIdCondition" />
	</select>
	
	<select id="findPolicyAccountTransListByTransCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE 1 = 1  ]]>
		<include refid="queryPolicyAccountTransListByTransCodeCondition" />
	</select>
	
	<select id="findPolicyAccountTransListByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE rownum = 1   ]]>
		<include refid="queryPolicyAccountTransListByPolicyChgIdCondition" />
	</select>
	
	<select id="findPolicyAccountTransListByTransTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE 1 = 1   ]]>
		<include refid="queryPolicyAccountTransListByTransTimeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapPolicyAccountTransList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPolicyAccountTransList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE ROWNUM <=  1000 ]]>
		<if test=" ct_apply_time != null and ct_apply_time != ''  "><![CDATA[ AND A.trans_time > #{ct_apply_time} ]]></if>
		<include refid="policyAccountTransListWhereCondition" />
	</select>
	
	<select id="findAllPolicyAccountByTransCode" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[   SELECT *
	    	FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST T
	   		WHERE T.UNIT_NUMBER = #{unit_number}
	    		 AND T.TRANS_CODE = #{trans_code}
	     		AND T.TRANS_TYPE = #{trans_type}]]>
<!-- 		<include refid="policyAccountTransListWhereCondition" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="findPolicyAccountTransListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryPolicyAccountTransListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.TRANS_POLICY_CODE,B.TRANS_AMOUNT_TYPE,B.TRANS_TYPE,B.ACCOUNT_BALANCE, B.ACCOUNT_ID, B.UNIT_NUMBER, B.TRANS_TIME, B.TRANS_AMOUNT, 
			B.TRANS_CODE, B.LIST_ID, B.POLICY_CHG_ID, B.STREAM_ID, B.INTEREST_RATE
			 FROM (
					SELECT ROWNUM RN, A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE ROWNUM <= #{LESS_NUM}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 批处理账户结息条件 -->
	<sql id="accountTransListCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" t_policy_account_code  != null ">
		<![CDATA[ AND A.TRANS_CODE in ]]>
			<foreach collection ="t_policy_account_code" item="account_code" index="index" open="(" close=")" separator=",">#{account_code}</foreach>
		</if>
		<if test=" start_date  != null"><![CDATA[ AND A.TRANS_TIME >= #{start_date}]]></if>
		<if test=" end_date != null"><![CDATA[ AND A.TRANS_TIME <= #{end_date}]]></if>
	</sql> 
	<!-- 账户结息批处理sql -->
	
<select id="findAllPolicyAccountTransListByaccountTransListCondition" resultType="java.util.Map" parameterType="java.util.Map">
	     
		<![CDATA[SELECT B.TRANS_TYPE,
                        B.ACCOUNT_ID,
                        B.ACCOUNT_BALANCE,
                        B.TRANS_TIME,
                        B.TRANS_AMOUNT,
                        B.TRANS_CODE,
                        B.LIST_ID,
                        B.POLICY_CHG_ID,
                        B.STREAM_ID,
                        B.INTEREST_RATE
                        FROM
            ( SELECT A.TRANS_TYPE,A.ACCOUNT_ID, A.ACCOUNT_BALANCE,A.TRANS_TIME, A.TRANS_AMOUNT, A.TRANS_CODE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE ROWNUM <=  1000 
		 ]]>
		<include refid="accountTransListCondition" />		
			<![CDATA[ 
		union	SELECT A.TRANS_TYPE,A.ACCOUNT_ID, A.ACCOUNT_BALANCE,A.TRANS_TIME, A.TRANS_AMOUNT, A.TRANS_CODE, 
			A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE  FROM APP___PAS__DBUSER.V_POL_ACC_TRANS_LIST_ALL A WHERE ROWNUM <=  1000 
		 ]]>
		<include refid="accountTransListCondition" />
		<![CDATA[ ) B
  ORDER BY B.TRANS_TIME DESC]]>
	</select>
	
	<!--by zhaoyoan 根据账户id和交易时间区间查询所有数据 
	  	<select id="findAllByAccountIdBetweenTransTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.TRANS_AMOUNT,A.TRANS_TIME,A.TRANS_TYPE,A.STREAM_ID,A.POLICY_CHG_ID,A.TRANS_CODE,A.INTEREST_RATE,
			(SELECT LIAB_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE PLAN_ID=(SELECT PLAN_ID FROM  APP___PAS__DBUSER.T_PAY_DUE
			        WHERE ITEM_ID=B.ITEM_ID AND FEE_AMOUNT=A.TRANS_AMOUNT AND ROWNUM=1)) liab_code,
			(NVL((SELECT SUM(TRANS_AMOUNT) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST 
			         WHERE A.ACCOUNT_ID=ACCOUNT_ID AND TRANS_TIME<=A.TRANS_TIME AND TRANS_TYPE='1'),0)+
			 NVL((SELECT SUM(TRANS_AMOUNT) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST 
			         WHERE A.ACCOUNT_ID=ACCOUNT_ID AND TRANS_TIME<=A.TRANS_TIME AND TRANS_TYPE='2'),0)) acc_bala,
			(SELECT PRODUCT_NAME FROM APP___PDS__DBUSER.T_PRODUCT_LIFE WHERE PRODUCT_ID=
			     (SELECT PRODUCT_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE ITEM_ID=
			             (SELECT ITEM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE ACCOUNT_ID=A.ACCOUNT_ID))) product_name
			FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A 
			LEFT JOIN APP___PAS__DBUSER.T_POLICY_ACCOUNT B ON A.ACCOUNT_ID=B.ACCOUNT_ID
			WHERE A.ACCOUNT_ID=#{account_id} 
			AND A.TRANS_AMOUNT <> 0
			AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE A.ACCOUNT_ID=ACCOUNT_ID AND ACCOUNT_TYPE='11')
		]]>
			<if test=" start_date  != null "><![CDATA[ 
				AND a.TRANS_TIME >= #{start_date} ]]></if>
			<if test=" end_date  != null "><![CDATA[ 
				AND a.TRANS_TIME <= #{end_date} ]]></if>
		<![CDATA[
			ORDER BY A.TRANS_TIME DESC,A.LIST_ID DESC
		]]>
	</select>
	
	-->
	<select id="findAllByAccountIdBetweenTransTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.TRANS_AMOUNT,A.LIST_ID,A.TRANS_TIME,A.TRANS_TYPE,A.STREAM_ID,A.POLICY_CHG_ID,A.TRANS_CODE,A.INTEREST_RATE,
			 
			   (SELECT  DECODE(A.master_busi_item_id ,NULL,
				 DECODE(A.OLD_POL_NO,NULL,A.BUSI_ITEM_ID,A.OLD_POL_NO),
				 (SELECT DECODE(A.OLD_POL_NO,NULL,A.BUSI_ITEM_ID,A.OLD_POL_NO) FROM DEV_PAS.T_CONTRACT_BUSI_PROD C 
				 WHERE C.BUSI_ITEM_ID = A.master_busi_item_id ))  
				 FROM dev_pas.t_contract_busi_prod A
				 WHERE A.busi_item_id = B.busi_item_id ) master_busi_item_id,			
		   (SELECT LIAB_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE PLAN_ID=(SELECT PLAN_ID FROM  APP___PAS__DBUSER.T_PAY_DUE
			        WHERE ITEM_ID=B.ITEM_ID AND FEE_AMOUNT=A.TRANS_AMOUNT AND ROWNUM=1)) liab_code,
			 A.ACCOUNT_BALANCE acc_bala,
			(SELECT PRODUCT_NAME FROM APP___PDS__DBUSER.T_PRODUCT_LIFE WHERE PRODUCT_ID=
			     (SELECT PRODUCT_ID FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE ITEM_ID=
			             (SELECT ITEM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE ACCOUNT_ID=A.ACCOUNT_ID))) product_name
			FROM APP___PAS__DBUSER.V_POL_ACC_TRANS_LIST_ALL A 
			LEFT JOIN APP___PAS__DBUSER.T_POLICY_ACCOUNT B ON A.ACCOUNT_ID=B.ACCOUNT_ID
			WHERE 1=1 
			AND A.TRANS_AMOUNT <> 0
			AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE A.ACCOUNT_ID=ACCOUNT_ID AND ACCOUNT_TYPE in('11','2'))
		]]>
			<if test=" start_date  != null "><![CDATA[ 
				AND a.TRANS_TIME >= #{start_date} ]]></if>
			<if test=" end_date  != null "><![CDATA[ 
				AND a.TRANS_TIME <= #{end_date} ]]></if>
			<if test=" account_id  != null "><![CDATA[ 
				AND A.ACCOUNT_ID = #{account_id}  ]]></if>
			<if test=" account_id_list  != null and account_id_list.size()>0 ">
		       <![CDATA[ AND A.ACCOUNT_ID in ]]>
			   <foreach collection ="account_id_list" item="account_id" index="index" open="(" close=")" separator=",">#{account_id}</foreach>
			</if>
		<![CDATA[
			ORDER BY A.TRANS_TIME DESC,A.INSERT_TIME DESC
		]]>
	</select>
	<!--根据账户policy_code修改保单账户交易详细记录状态 -->
	<update id='updatePolicyCodeTransListState' parameterType="java.util.Map" >
	    update APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST t set t.trans_code='91'
        where t.unit_number in
        (select a.Unit_Number from APP___PAS__DBUSER.T_PREM_ARAP a where a.policy_code=#{policy_code, jdbcType=VARCHAR})
        and t.trans_code='90' and t.unit_number is not null
	</update>
	<!--根据账户policy_code查询保单账户交易详细记录 -->
	<select id='selectPolicyCodeTransListState' resultType="java.util.Map" parameterType="java.util.Map">
	    select * from  APP___PAS__DBUSER.t_Policy_Account_Trans_List t
        where t.unit_number in
        (select a.Unit_Number from APP___PAS__DBUSER.T_PREM_ARAP a where a.policy_code=#{policy_code, jdbcType=VARCHAR})
        and t.trans_code='90' and t.unit_number is not null
	</select>
	
	<select id='PA_findPolAccTraListRollBackData' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
	   	SELECT T.POLICY_CHG_ID,T.ACCOUNT_ID,T.TRANS_TYPE,T.LIST_ID,T.TRANS_CODE,SUM(T.TRANS_AMOUNT) AS TRANS_AMOUNT FROM DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST T 
		WHERE T.ACCOUNT_ID IN (
		      SELECT A.ACCOUNT_ID
                          FROM DEV_PAS.T_POLICY_ACCOUNT A
                         WHERE A.POLICY_ID = #{policy_id}
		) AND T.POLICY_CHG_ID >= #{policy_chg_id} AND T.POLICY_CHG_ID < *************
		GROUP BY T.POLICY_CHG_ID,T.ACCOUNT_ID,T.TRANS_TYPE,T.LIST_ID,T.TRANS_CODE
		ORDER BY T.POLICY_CHG_ID DESC]]>
	</select>
	
	<select id='PA_findNearestTransList' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT T.TRANS_TYPE,T.ACCOUNT_ID, T.UNIT_NUMBER,T.TRANS_TIME, T.TRANS_AMOUNT, 
			T.TRANS_CODE, T.LIST_ID, T.POLICY_CHG_ID,T.STREAM_ID,T.INTEREST_RATE FROM DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST T 
		WHERE T.POLICY_CHG_ID < #{policy_chg_id} AND T.ACCOUNT_ID = #{account_id} AND ROWNUM = 1 ORDER BY T.POLICY_CHG_ID DESC]]>
	</select>
	<!--保险帐户表记价履历接口  --> 
	<select id="findTransListHistoryRecord" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[	
SELECT ROWNUM,Z.* 
  FROM (
		SELECT TCBP.BUSI_ITEM_ID,
		       TFT.UNIT_NUMBER,
		       TFT.LIST_ID ACCOUNT_ID,
		       TCBP.BUSI_PROD_CODE,
		       TFT.TRANS_AMOUNT,
			   TFT.DEAL_TIME FINISH_TIME,
		       TFT.INSERT_TIME,
		       TFT.UPDATE_TIME,
		       (SELECT TCPC.ENDORSE_CODE
		          FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL    TPC,
		               APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC,
		               APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
		         WHERE TPC.POLICY_CHG_ID = TFT.POLICY_CHG_ID
		           AND TPC.BUSINESS_CODE = TCAC.ACCEPT_CODE
		           AND TCAC.CHANGE_ID = TCPC.CHANGE_ID
		           AND ROWNUM = 1) ENDORSE_CODE,
		       (SELECT TCAC.SERVICE_CODE
		          FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL    TPC,
		               APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC,
		               APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
		         WHERE TPC.POLICY_CHG_ID = TFT.POLICY_CHG_ID
		           AND TPC.BUSINESS_CODE = TCAC.ACCEPT_CODE
		           AND TCAC.CHANGE_ID = TCPC.CHANGE_ID
		           AND ROWNUM = 1) SERVICE_CODE,
		       TFT.INSERT_TIMESTAMP
		  FROM APP___PAS__DBUSER.T_FUND_TRANS TFT, 
		       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
		 WHERE TFT.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		   AND TFT.POLICY_ID = TCBP.POLICY_ID
		   AND TCBP.POLICY_CODE = #{policy_code}
		   AND NVL(TCBP.OLD_POL_NO, TCBP.BUSI_ITEM_ID) = #{busi_item_id}
		
		UNION
		
		SELECT TPA.BUSI_ITEM_ID,
		       TPATL.UNIT_NUMBER,
		       TPATL.ACCOUNT_ID,
		       TCBP.BUSI_PROD_CODE,
		       TPATL.TRANS_AMOUNT,
			   TPATL.TRANS_TIME FINISH_TIME,
		       TPATL.INSERT_TIME,
		       TPATL.UPDATE_TIME,
		       (SELECT TCPC.ENDORSE_CODE
		          FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL    TPC,
		               APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC,
		               APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
		         WHERE TPC.POLICY_CHG_ID = TPATL.POLICY_CHG_ID
		           AND TPC.BUSINESS_CODE = TCAC.ACCEPT_CODE
		           AND TCAC.CHANGE_ID = TCPC.CHANGE_ID
		           AND ROWNUM = 1) ENDORSE_CODE,
		       (SELECT TCAC.SERVICE_CODE
		          FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL    TPC,
		               APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC,
		               APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
		         WHERE TPC.POLICY_CHG_ID = TPATL.POLICY_CHG_ID
		           AND TPC.BUSINESS_CODE = TCAC.ACCEPT_CODE
		           AND TCAC.CHANGE_ID = TCPC.CHANGE_ID
		           AND ROWNUM = 1) SERVICE_CODE,
		       TPATL.INSERT_TIMESTAMP
		  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT            TPA,
		       APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TPATL,
		       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD        TCBP
		 WHERE TPA.ACCOUNT_ID = TPATL.ACCOUNT_ID
		   AND TPA.POLICY_ID = TCBP.POLICY_ID
		   AND TPA.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		   AND TPA.ACCOUNT_TYPE IN (2, 11)
		   AND TCBP.POLICY_CODE = #{policy_code}
		   AND NVL(TCBP.OLD_POL_NO, TCBP.BUSI_ITEM_ID) = #{busi_item_id}
   ) Z
  ]]>  
</select>
	
	<!-- 查询理赔后账户明细 -->
	<select id="PA_findAllByAccountByClaimDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_POLICY_CODE,A.TRANS_AMOUNT_TYPE,A.TRANS_TYPE,A.ACCOUNT_BALANCE,A.ACCOUNT_ID, A.UNIT_NUMBER, A.TRANS_TIME, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.LIST_ID, A.POLICY_CHG_ID, A.STREAM_ID,A.INTEREST_RATE
			 FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST A WHERE ROWNUM <=  1000 ]]>
	<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	<if test=" trans_time  != null  and  trans_time  != ''  "><![CDATA[ AND A.TRANS_TIME >= #{trans_time} ]]></if>
	</select>
	
	<!-- 查询指定日期之后的结息数据 -->
	<select id="PA_queryAccountByPointDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT T.TRANS_POLICY_CODE,T.TRANS_AMOUNT_TYPE,T.LIST_ID,T.ACCOUNT_ID,T.POLICY_CHG_ID,T.UNIT_NUMBER,T.TRANS_CODE,
				T.TRANS_AMOUNT,T.TRANS_TIME,T.TRANS_TYPE,T.ACCOUNT_BALANCE  FROM DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST T 
			WHERE T.TRANS_CODE='6'
		]]>
		<if test=" account_id  != null "><![CDATA[ AND T.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" trans_time  != null  and  trans_time  != ''  "><![CDATA[ AND T.TRANS_TIME >= #{trans_time} ]]></if>
		<![CDATA[ ORDER BY T.TRANS_TIME DESC ]]>
	</select>
	<select id='findNearestTransListForRB' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT T.TRANS_TYPE,T.ACCOUNT_ID, T.UNIT_NUMBER,T.TRANS_TIME, T.TRANS_AMOUNT, 
			T.TRANS_CODE, T.LIST_ID, T.POLICY_CHG_ID,T.STREAM_ID,T.INTEREST_RATE FROM DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST T 
		WHERE T.POLICY_CHG_ID < #{policy_chg_id} AND T.ACCOUNT_ID = #{account_id} ORDER BY T.POLICY_CHG_ID DESC]]>
	</select>
	
	<!-- 累积生息账户状态报告书查询所有交易记录 -->
	<select id='PA_findAllByAccountIdForStaRep' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[    SELECT ROWNUM RN, T.*
					  FROM (SELECT PT.ACCOUNT_ID, --账户ID
					               PT.TRANS_CODE, --变更类型
					               PT.TRANS_TYPE, --交易类型，1入账，2出账
					               (SELECT A.CODE_NAME
					                  FROM DEV_PAS.T_POLICY_ACCOUNT_CODE A
					                 WHERE A.TRANS_CODE = PT.TRANS_CODE) AS TRANS_CODE_NAME, --变更类型名称
					               PT.TRANS_TIME, --变更日期
					               PT.TRANS_AMOUNT, --变更金额
					               PT.INTEREST_RATE, --结算利率,
					               PT.TRANS_AMOUNT AS INTEREST_BALANCE, --利息
					               PT.ACCOUNT_BALANCE AS INTEREST_CAPITAL --账户余额
					          FROM DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST PT
					         WHERE 1 = 1
					           AND PT.ACCOUNT_ID = #{account_id} ]]>
		<if test=" start_date  != null"><![CDATA[ AND PT.TRANS_TIME >= #{start_date,jdbcType=DATE} ]]></if>
		<if test=" end_date != null"><![CDATA[ AND PT.TRANS_TIME <= #{end_date,jdbcType=DATE} ]]></if>
		<![CDATA[  			 ORDER BY  PT.TRANS_TIME DESC , PT.INSERT_TIME DESC  ) T
							 WHERE 1 = 1
							 ORDER BY RN DESC   ]]>
	</select>

	<!-- 累积生息账户状态报告书查询对应日后的第一个结算日 -->
	<select id='PA_findTransListForAccountInterestStaRep' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ 
			      SELECT B.*
				    FROM (SELECT PT.ACCOUNT_ID, /*账户ID*/
				                 PT.TRANS_CODE, /*变更类型*/
				                 (SELECT A.CODE_NAME
				                    FROM DEV_PAS.T_POLICY_ACCOUNT_CODE A
				                   WHERE A.TRANS_CODE = PT.TRANS_CODE) AS TRANS_CODE_NAME, /*变更类型名称*/
				                 PT.TRANS_TIME /*变更日期*/
				            FROM DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST PT
				           WHERE 1 = 1
				             AND PT.TRANS_CODE = 6
				             AND PT.ACCOUNT_ID =  #{account_id}  
				             AND PT.TRANS_TIME > #{trans_time}
				           ORDER BY PT.TRANS_TIME) B
				   WHERE 1 = 1  AND ROWNUM = 1
	   ]]>
	</select>

	<!-- 查询上次保单账户交易详细记录信息 -->
	<select id='PA_findPrexTransTimeTransList' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[  SELECT B.*
		            FROM (SELECT PT.ACCOUNT_ID, /*账户ID*/
		                         PT.TRANS_CODE, /*变更类型*/
		                         PT.ACCOUNT_BALANCE,
		                         PT.TRANS_TIME /*变更日期*/
		                    FROM DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST PT
		                   WHERE 1 = 1
		                     AND PT.ACCOUNT_ID = #{account_id}  
				             AND PT.TRANS_TIME < #{trans_time,jdbcType=DATE} 
		                   ORDER BY PT.TRANS_TIME DESC, PT.INSERT_TIME DESC  ) B
		           WHERE 1 = 1  AND ROWNUM = 1
	   ]]>
	</select>
	
	<select id='queryPolicyAccountTransListByPlanId' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ select distinct c.plan_id,a.ACCOUNT_ID
				  from APP___PAS__DBUSER.v_pol_acc_trans_list_all a,
				       APP___PAS__DBUSER.t_Policy_Account         b,
				       APP___PAS__DBUSER.t_Pay_Due                c
				 where 1 = 1
				   and a.account_id = b.account_id
				   and b.policy_id = c.policy_id
				   and b.item_id = c.item_id
				   and a.unit_number = c.unit_number]]>
		 <if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		 <if test=" trans_type  != null "><![CDATA[ AND A.TRANS_TYPE = #{trans_type} ]]></if>
		 <if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		 <if test=" trans_time  != null  and  trans_time  != ''  "><![CDATA[ AND A.TRANS_TIME > #{trans_time} ]]></if>
	</select>
	
	<select id='findPolicyAccountTransListForRG' resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ 
	  		 select A.TRANS_TIME, A.TRANS_CODE, A.TRANS_TYPE ,A.INTEREST_RATE  
				  from DEV_PAS.v_Pol_Acc_Trans_List_All A
				 where 1 = 1 
				   AND A.ACCOUNT_ID = #{account_id}
				   AND A.TRANS_TIME > #{start_date}
				   AND A.TRANS_CODE = #{trans_code}
				   AND A.TRANS_TYPE = #{trans_type}
				 group by A.TRANS_TIME, A.TRANS_CODE, A.TRANS_TYPE, A.ACCOUNT_ID, A.INTEREST_RATE 
	  ]]>
	</select>
	<!-- 查询保单代扣印花税数据 -->
	<select id="PA_selectWithhodIngstampTax" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	     SELECT SUM(TPATL.TRANS_AMOUNT) AS WITHHODINGSTAMPTAX
  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TPATL,
       (SELECT TPAS.ACCOUNT_ID
          FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        TPA,
               APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TPAS
         WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
           AND TPA.POLICY_ID = TPAS.POLICY_ID 
           AND TPA.ACCOUNT_TYPE = 4
           AND TPA.INTEREST_CAPITAL > 0
           AND TPA.REGULAR_REPAY = 0
           AND TPA.POLICY_ID = #{POLICY_ID}) T
 WHERE TPATL.ACCOUNT_ID = T.ACCOUNT_ID
   AND TPATL.TRANS_CODE = '35' 	 ]]> 
	 </select>
	 
	 <!-- 查询险种代扣印花税数据 -->
	<select id="PA_selectWithhodIngstampTaxBusi" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	     SELECT TPATL.TRANS_AMOUNT AS WITHHODINGSTAMPTAX
  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TPATL,
       (SELECT TPAS.ACCOUNT_ID
          FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT        TPA,
               APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TPAS
         WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
           AND TPA.POLICY_ID = TPAS.POLICY_ID 
           AND TPA.ACCOUNT_TYPE = 4
           AND TPA.INTEREST_CAPITAL > 0
           AND TPA.REGULAR_REPAY = 0
           AND TPA.BUSI_ITEM_ID = #{BUSI_ITEM_ID}) T
 WHERE TPATL.ACCOUNT_ID = T.ACCOUNT_ID
   AND TPATL.TRANS_CODE = '35' 	 ]]> 
	 </select>
	 
	 <!-- 养老金报送根据保单id查询累积生息账户信息 -->
	<select id="PA_findAllPolicyAccountTransListForGryl" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[			SELECT TCBP.BUSI_PROD_CODE,
	  						   A.ITEM_ID,
						       B.LIST_ID,
						       B.TRANS_TIME,
						       B.TRANS_AMOUNT,
						       B.TRANS_TYPE,
                   			   B.INTEREST_RATE
						  FROM DEV_PAS.T_POLICY_ACCOUNT            A,
						       DEV_PAS.T_POLICY_ACCOUNT_TRANS_LIST B,
						       DEV_PAS.T_POLICY_ACCOUNT_CODE       C,
						       DEV_PAS.T_CONTRACT_BUSI_PROD        TCBP
						 WHERE A.ACCOUNT_ID = B.ACCOUNT_ID
						   AND B.TRANS_CODE = C.TRANS_CODE
						   AND A.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID ]]> 
			<if test=" trans_time  != null  and  trans_time  != ''  "><![CDATA[ AND B.TRANS_TIME = #{trans_time} ]]></if>
			<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND B.TRANS_CODE = #{trans_code} ]]></if>
			<![CDATA[	   AND A.POLICY_ID =  #{policy_id}   ORDER BY B.TRANS_TIME DESC 	 ]]> 
	 </select>
	 
</mapper>