<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="queryPaperSupplement">

	<!-- 查询申请方式 -->
	<select id="getServiceTypeByOldPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT CA.SERVICE_TYPE FROM DEV_PAS.T_CS_POLICY_CHANGE  CPC 
 						INNER JOIN DEV_PAS.T_CS_APPLICATION CA ON CPC.CHANGE_ID=CA.CHANGE_ID
 						 INNER JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL BP
              ON CPC.CHANGE_ID = BP.CHANGE_ID
             AND CPC.POLICY_CHG_ID = BP.POLICY_CHG_ID
 				  WHERE CPC.POLICY_CODE=#{policy_code} AND CPC.SERVICE_CODE='RR' 
        ]]>
	</select>
	
	<!-- 查询是否操作过‘重新出单’或‘保单补发’保全项 -->
	<select id="findCsPolicyChangeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT * FROM
      (SELECT A.INSERT_TIMESTAMP, A.UPDATE_TIMESTAMP
      FROM DEV_PAS.T_POLICY_CHANGE A
      WHERE A.POLICY_ID =
      (SELECT B.POLICY_ID
      FROM DEV_PAS.T_CONTRACT_MASTER B
      WHERE #{policy_code} IN(B.POLICY_CODE,B.APPLY_CODE))
      and A.SERVICE_CODE IN ('RN', 'LR')
      ORDER BY A.INSERT_TIMESTAMP DESC)
      WHERE ROWNUM = 1 
	 	]]>
	</select>
	
	<!-- 查询保单打印时间 -->
	<select id="findQryPolicyPrintTimeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT MIN(PRINT_TIME) AS REPLY_TIME
                   FROM (SELECT DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS PRINT_TIME,
                                PPA.POLICY_CODE,
                                PPA.PRINT_TYPE
                           FROM DEV_PAS.T_POLICY_PRINT PPA
                         ) A
                  WHERE POLICY_CODE = #{policy_code}
                    AND PRINT_TIME IS NOT NULL
                    AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1')
		]]>
	</select>
	
	<!-- 工行纸质保单补充接口 使用 IsApply字段。  -->
	<select id="findPolicyPrintIsApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	  select (
         (select count(*)
             from dev_pas.t_cs_accept_change cac,
                  dev_pas.t_cs_policy_change cpc,
                  DEV_PAS.T_CONTRACT_MASTER  T,
                  dev_pas.t_policy_print     a
            where cpc.accept_id = cac.accept_id
              AND CPC.POLICY_ID = T.POLICY_ID
              and a.policy_code = t.policy_code
              AND a.PRINT_STATUS = '3'
              and a.print_type in ('1', '0')
              and cac.accept_status = '18'
              and cac.service_code in ('LR', 'RN')
              and #{policy_code} IN (T.POLICY_CODE, T.APPLY_CODE))) as print_times
    from dual
	 	]]>
	</select>
	
	<!-- 保单效力状态 -->
	<select id="findState" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT T.LIABILITY_STATE
	   FROM DEV_PAS.T_CONTRACT_MASTER T
	  WHERE #{policy_code} IN (T.POLICY_CODE,T.APPLY_CODE)
	 	]]>
	</select>
	
	<!-- 申请状态和错误内容 -->
	<select id="findApplyStatusAndErrorContent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT *
		 FROM (SELECT C.PRINT_ID,
		              A.APPLY_ID,
		              C.PRINT_TYPE,
		              A.APPLY_STATUS,
		              C.ERROR_CONTENT,
		              C.PRINT_TIME,
		              TO_CHAR(PD.SEND_DATE, 'yyyy:mm:dd') || PD.SEND_TIEM AS SEND_DATE
		         FROM DEV_PAS.T_POLICY_PRINT C
		         LEFT JOIN DEV_PAS.T_POLICY_PRINT_APPLY A
		           ON C.PRINT_ID = A.PRINT_ID
		         LEFT JOIN DEV_PAS.T_PRINT_DETAIL PD
		           ON PD.APPLY_ID = A.APPLY_ID
		          and C.policy_code = PD.policy_code
		        WHERE #{policy_code} IN (C.POLICY_CODE, C.APPLY_CODE)
		          AND C.PRINT_TYPE <> 2
		        ORDER BY A.INSERT_TIMESTAMP DESC)
		WHERE ROWNUM = 1
	 	]]>
	</select>
	
		<!-- 工行纸质保单补充接口nspolicyHolder 字段。  -->
	<select id="findNsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT CU.OLD_CUSTOMER_ID,
		       CU.CUSTOMER_NAME,
		       CU.CUSTOMER_GENDER,
		       CU.CUSTOMER_BIRTHDAY,
		       CU.CUSTOMER_CERT_TYPE,
		       CU.CUSTOMER_CERTI_CODE
	 	  FROM DEV_PAS.T_POLICY_HOLDER PH
	    LEFT JOIN DEV_PAS.T_CUSTOMER CU
	    	ON PH.CUSTOMER_ID = CU.CUSTOMER_ID
	    WHERE 1=1 AND #{policy_code} IN (PH.POLICY_CODE,PH.APPLY_CODE)
	 	]]>
	</select>
</mapper>
