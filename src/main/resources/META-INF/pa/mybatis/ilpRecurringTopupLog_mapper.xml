<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ilpRecurringTopupLog">
<!--
	<sql id="PA_ilpRecurringTopupLogWhereCondition">
		<if test=" add_prem  != null "><![CDATA[ AND A.ADD_PREM = #{add_prem} ]]></if>
		<if test=" active_indi  != null "><![CDATA[ AND A.ACTIVE_INDI = #{active_indi} ]]></if>
		<if test=" add_start_date  != null  and  add_start_date  != ''  "><![CDATA[ AND A.ADD_START_DATE = #{add_start_date} ]]></if>
		<if test=" add_period  != null "><![CDATA[ AND A.ADD_PERIOD = #{add_period} ]]></if>
		<if test=" operator_id  != null "><![CDATA[ AND A.OPERATOR_ID = #{operator_id} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" initial_start_date  != null  and  initial_start_date  != ''  "><![CDATA[ AND A.INITIAL_START_DATE = #{initial_start_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" charge_type != null and charge_type != ''  "><![CDATA[ AND A.CHARGE_TYPE = #{charge_type} ]]></if>
		<if test=" add_prem_an  != null "><![CDATA[ AND A.ADD_PREM_AN = #{add_prem_an} ]]></if>
		<if test=" sa_factor  != null "><![CDATA[ AND A.SA_FACTOR = #{sa_factor} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" add_year  != null "><![CDATA[ AND A.ADD_YEAR = #{add_year} ]]></if>
		<if test=" topup_chg_id  != null "><![CDATA[ AND A.TOPUP_CHG_ID = #{topup_chg_id} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" pay_to_date  != null  and  pay_to_date  != ''  "><![CDATA[ AND A.PAY_TO_DATE = #{pay_to_date} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryIlpRecurringTopupLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addIlpRecurringTopupLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG(
				ADD_PREM, ACTIVE_INDI, ADD_START_DATE, ADD_PERIOD, OPERATOR_ID, ACCOUNT_ID, INITIAL_START_DATE, 
				ITEM_ID, CHARGE_TYPE, ADD_PREM_AN, INSERT_TIMESTAMP, UPDATE_BY, SA_FACTOR, LIST_ID, 
				POLICY_CHG_ID, POLICY_ID, INSERT_TIME, UPDATE_TIME, PAIDUP_DATE, LOG_ID, ADD_YEAR, 
				TOPUP_CHG_ID, PAY_MODE, UPDATE_TIMESTAMP, PAY_TO_DATE, LOG_TYPE, INSERT_BY ) 
			VALUES (
				#{add_prem, jdbcType=NUMERIC}, #{active_indi, jdbcType=NUMERIC} , #{add_start_date, jdbcType=DATE} , #{add_period, jdbcType=NUMERIC} , #{operator_id, jdbcType=NUMERIC} , #{account_id, jdbcType=NUMERIC} , #{initial_start_date, jdbcType=DATE} 
				, #{item_id, jdbcType=NUMERIC} , #{charge_type, jdbcType=VARCHAR} , #{add_prem_an, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{sa_factor, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{policy_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{paidup_date, jdbcType=DATE} , #{log_id, jdbcType=NUMERIC} , #{add_year, jdbcType=NUMERIC} 
				, #{topup_chg_id, jdbcType=NUMERIC} , #{pay_mode, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{pay_to_date, jdbcType=DATE} , #{log_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteIlpRecurringTopupLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG WHERE LOG_ID=#{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateIlpRecurringTopupLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ADD_PREM = #{add_prem, jdbcType=NUMERIC} ,
		    ACTIVE_INDI = #{active_indi, jdbcType=NUMERIC} ,
		    ADD_START_DATE = #{add_start_date, jdbcType=DATE} ,
		    ADD_PERIOD = #{add_period, jdbcType=NUMERIC} ,
		    OPERATOR_ID = #{operator_id, jdbcType=NUMERIC} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
		    INITIAL_START_DATE = #{initial_start_date, jdbcType=DATE} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			CHARGE_TYPE = #{charge_type, jdbcType=VARCHAR} ,
		    ADD_PREM_AN = #{add_prem_an, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SA_FACTOR = #{sa_factor, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    ADD_YEAR = #{add_year, jdbcType=NUMERIC} ,
		    TOPUP_CHG_ID = #{topup_chg_id, jdbcType=NUMERIC} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    PAY_TO_DATE = #{pay_to_date, jdbcType=DATE} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findIlpRecurringTopupLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADD_PREM, A.ACTIVE_INDI, A.ADD_START_DATE, A.ADD_PERIOD, A.OPERATOR_ID, A.ACCOUNT_ID, A.INITIAL_START_DATE, 
			A.ITEM_ID, A.CHARGE_TYPE, A.ADD_PREM_AN, A.SA_FACTOR, A.LIST_ID, 
			A.POLICY_CHG_ID, A.POLICY_ID, A.PAIDUP_DATE, A.LOG_ID, A.ADD_YEAR, 
			A.TOPUP_CHG_ID, A.PAY_MODE, A.PAY_TO_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryIlpRecurringTopupLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapIlpRecurringTopupLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADD_PREM, A.ACTIVE_INDI, A.ADD_START_DATE, A.ADD_PERIOD, A.OPERATOR_ID, A.ACCOUNT_ID, A.INITIAL_START_DATE, 
			A.ITEM_ID, A.CHARGE_TYPE, A.ADD_PREM_AN, A.SA_FACTOR, A.LIST_ID, 
			A.POLICY_CHG_ID, A.POLICY_ID, A.PAIDUP_DATE, A.LOG_ID, A.ADD_YEAR, 
			A.TOPUP_CHG_ID, A.PAY_MODE, A.PAY_TO_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllIlpRecurringTopupLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADD_PREM, A.ACTIVE_INDI, A.ADD_START_DATE, A.ADD_PERIOD, A.OPERATOR_ID, A.ACCOUNT_ID, A.INITIAL_START_DATE, 
			A.ITEM_ID, A.CHARGE_TYPE, A.ADD_PREM_AN, A.SA_FACTOR, A.LIST_ID, 
			A.POLICY_CHG_ID, A.POLICY_ID, A.PAIDUP_DATE, A.LOG_ID, A.ADD_YEAR, 
			A.TOPUP_CHG_ID, A.PAY_MODE, A.PAY_TO_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findIlpRecurringTopupLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryIlpRecurringTopupLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADD_PREM, B.ACTIVE_INDI, B.ADD_START_DATE, B.ADD_PERIOD, B.OPERATOR_ID, B.ACCOUNT_ID, B.INITIAL_START_DATE, 
			B.ITEM_ID, B.CHARGE_TYPE, B.ADD_PREM_AN, B.SA_FACTOR, B.LIST_ID, 
			B.POLICY_CHG_ID, B.POLICY_ID, B.PAIDUP_DATE, B.LOG_ID, B.ADD_YEAR, 
			B.TOPUP_CHG_ID, B.PAY_MODE, B.PAY_TO_DATE, B.LOG_TYPE FROM (
					SELECT ROWNUM RN, A.ADD_PREM, A.ACTIVE_INDI, A.ADD_START_DATE, A.ADD_PERIOD, A.OPERATOR_ID, A.ACCOUNT_ID, A.INITIAL_START_DATE, 
			A.ITEM_ID, A.CHARGE_TYPE, A.ADD_PREM_AN, A.SA_FACTOR, A.LIST_ID, 
			A.POLICY_CHG_ID, A.POLICY_ID, A.PAIDUP_DATE, A.LOG_ID, A.ADD_YEAR, 
			A.TOPUP_CHG_ID, A.PAY_MODE, A.PAY_TO_DATE, A.LOG_TYPE FROM APP___PAS__DBUSER.T_ILP_RECURRING_TOPUP_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
