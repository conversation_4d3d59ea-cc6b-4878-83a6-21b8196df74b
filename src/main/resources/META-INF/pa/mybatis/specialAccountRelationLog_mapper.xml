<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISpecialAccountRelationLogDao">

	<sql id="PA_specialAccountRelationLogWhereCondition">
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" bank_policy_id  != null "><![CDATA[ AND A.BANK_POLICY_ID = #{bank_policy_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" validiy_end_date  != null "><![CDATA[ AND A.VALIDIY_END_DATE = #{validiy_end_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySpecialAccountRelationLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationLogByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationLogByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationLogByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_querySpecialAccountRelationLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSpecialAccountRelationLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_SPECIAL_ACCOUNT_RELATION_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG(
				INSERT_TIME, UPDATE_TIME, APPLY_CODE, LOG_ID, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, 
				LIST_ID, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, BANK_POLICY_ID, INSERT_BY, POLICY_ID, VALIDIY_END_DATE ) 
			VALUES (
				SYSDATE, SYSDATE , #{apply_code, jdbcType=VARCHAR} , #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{bank_policy_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}, #{validiy_end_date, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSpecialAccountRelationLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSpecialAccountRelationLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BANK_POLICY_ID = #{bank_policy_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    VALIDIY_END_DATE = #{validiy_end_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSpecialAccountRelationLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationLogByLogIdCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationLogByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationLogByApplyCodeCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationLogByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationLogByListIdCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationLogByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationLogByPolicyChgIdCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationLogByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findSpecialAccountRelationLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySpecialAccountRelationLogByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSpecialAccountRelationLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_specialAccountRelationLogWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSpecialAccountRelationLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_specialAccountRelationLogWhereCondition" />
	</select>
	
	<!-- 查询单条操作 -->
	<select id="PA_findSpecialAccountRelationLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_specialAccountRelationLogWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSpecialAccountRelationLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_specialAccountRelationLogWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySpecialAccountRelationLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.APPLY_CODE, B.LOG_ID, B.POLICY_CODE, 
			B.LIST_ID, B.LOG_TYPE, B.POLICY_CHG_ID, B.BANK_POLICY_ID, B.POLICY_ID,B.VALIDIY_END_DATE FROM (
					SELECT ROWNUM RN, A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, 
			A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_specialAccountRelationLogWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
