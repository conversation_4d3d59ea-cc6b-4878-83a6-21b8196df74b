<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.impl.nbdatadeal.dao.impl.PostLostDealDaoImpl">
	<!-- <sql id="policyPrintListWhereCondition"> <if test=" is_reissue != null 
		"><![CDATA[ AND A.IS_REISSUE = #{is_reissue} ]]></if> <if test=" print_cause 
		!= null and print_cause != '' "><![CDATA[ AND A.PRINT_CAUSE = #{print_cause} 
		]]></if> <if test=" print_time != null and print_time != '' "><![CDATA[ AND 
		A.PRINT_TIME = #{print_time} ]]></if> <if test=" policy_code != null and 
		policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if> 
		<if test=" print_date != null and print_date != '' "><![CDATA[ AND A.PRINT_DATE 
		= #{print_date} ]]></if> <if test=" list_id != null "><![CDATA[ AND A.LIST_ID 
		= #{list_id} ]]></if> </sql> -->

	<!-- 按索引生成的查询条件 -->

	<!-- 添加操作 -->
	<insert id="insertPostLost" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_PRINT_LIST__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_PRINT_LIST(
				INSERT_TIMESTAMP, IS_REISSUE, PRINT_CAUSE, PRINT_TIME, POLICY_CODE, PRINT_DATE, UPDATE_BY, 
				INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{is_reissue, jdbcType=NUMERIC} , #{print_cause, jdbcType=VARCHAR} , #{print_time, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{print_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} 
				, SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="deletePolicyPrintList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_PRINT_LIST WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="updatePolicyPrintList" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_PRINT_LIST ]]>
		<set>
			<trim suffixOverrides=",">
				IS_REISSUE = #{is_reissue, jdbcType=NUMERIC} ,
				PRINT_CAUSE = #{print_cause, jdbcType=VARCHAR} ,
				PRINT_TIME = #{print_time, jdbcType=DATE} ,
				POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				PRINT_DATE = #{print_date, jdbcType=DATE} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				UPDATE_TIME = SYSDATE ,
			</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->

	<!-- 按map查询操作 -->
	<select id="findAllMapPolicyPrintList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_REISSUE, A.PRINT_CAUSE, A.PRINT_TIME, A.POLICY_CODE, A.PRINT_DATE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_POLICY_PRINT_LIST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllPolicyPrintList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_REISSUE, A.PRINT_CAUSE, A.PRINT_TIME, A.POLICY_CODE, A.PRINT_DATE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_POLICY_PRINT_LIST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="findPolicyPrintListTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_PRINT_LIST A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="queryPolicyPrintListForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.IS_REISSUE, B.PRINT_CAUSE, B.PRINT_TIME, B.POLICY_CODE, B.PRINT_DATE, 
			B.LIST_ID FROM (
					SELECT ROWNUM RN, A.IS_REISSUE, A.PRINT_CAUSE, A.PRINT_TIME, A.POLICY_CODE, A.PRINT_DATE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_POLICY_PRINT_LIST A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

</mapper>
