<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.batch.terminate.dao.impl.OverrunTerminateDaoImpl">
	
	<!-- 添加保单号 -->
	<sql id="queryTerminatePolicyByPolicyCode">
		<if
			test="policy_code != null and policy_code != '' and policy_code !='null'">
         <![CDATA[ AND tcbp.policy_code = #{policy_code} ]]></if>
         
         <!-- 添加机构 -->    
     <if test="organ_code != null and organ_code != '' and organ_code !='null'">
			<![CDATA[ AND tcm.organ_code in (
			    	select t.organ_code
                    from APP___PAS__DBUSER.t_udmp_org_rel t
                    start with t.organ_code = #{organ_code}
                    connect by prior t.organ_code = t.uporgan_code
			    	GROUP BY organ_code) ]]>
		</if>
	</sql>

	<select id="terminateQueryCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(1) FROM (select tci.constants_key,tci.constants_value,tcp.STD_PREM_AF,tcp.TOTAL_PREM_AF,tcbp.policy_ID,tcbp.busi_item_id,tcbp.policy_code,tc.customer_birthday,tcbp.busi_prod_code,tc.customer_id,tcp.amount,tcbp.validate_date
        from APP___PAS__DBUSER.t_Contract_Master tcm,
        APP___PAS__DBUSER.t_contract_busi_prod tcbp,
        APP___PAS__DBUSER.t_customer tc,
        APP___PAS__DBUSER.t_insured_list til,
        APP___PAS__DBUSER.t_contract_product tcp,
        APP___PAS__DBUSER.T_CONSTANTS_INFO tci
       where tcm.liability_state != '3'
         AND NOT EXISTS (SELECT 'X'
          FROM APP___PAS__DBUSER.T_LOCK_POLICY A
          LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
            ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
         WHERE B.LOCK_SERVICE_ID IN (87, 86)
           AND B.SUB_ID IN ('068', '067')
           AND A.POLICY_CODE = tcm.POLICY_CODE)
           and tcbp.policy_code = tcm.policy_code
           and til.policy_id = tcbp.policy_id
           and tc.customer_id = til.customer_id
           and tcp.policy_id = tcbp.policy_id
           and tcp.busi_item_id = tcbp.busi_item_id
           and tci.constants_key like '%OVER_RUN_TERMIN_PROD%'
           and tci.constants_value=tcbp.busi_prod_code
		]]>
		<include refid="queryTerminatePolicyByPolicyCode"/>
		<![CDATA[)T]]>
	</select>

	<select id="terminateQueryList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT t.constants_key,t.constants_value,t.STD_PREM_AF,t.TOTAL_PREM_AF,T.policy_ID,T.busi_item_id,T.policy_code,T.customer_birthday,T.customer_id,T.busi_prod_code,T.amount,t.validate_date 
			FROM (select tci.constants_key,tci.constants_value,tcp.STD_PREM_AF,tcp.TOTAL_PREM_AF,tcbp.policy_ID,tcbp.busi_item_id,tcbp.policy_code,tc.customer_birthday,tcbp.busi_prod_code,tc.customer_id,tcp.amount,tcbp.validate_date
        from APP___PAS__DBUSER.t_Contract_Master tcm,
        APP___PAS__DBUSER.t_contract_busi_prod tcbp,
        APP___PAS__DBUSER.t_customer tc,
        APP___PAS__DBUSER.t_insured_list til,
        APP___PAS__DBUSER.t_contract_product tcp,
        APP___PAS__DBUSER.T_CONSTANTS_INFO tci
       where tcm.liability_state != '3'
         AND NOT EXISTS (SELECT 'X'
          FROM APP___PAS__DBUSER.T_LOCK_POLICY A
          LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
            ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
         WHERE B.LOCK_SERVICE_ID IN (87, 86)
           AND B.SUB_ID IN ('068', '067')
           AND A.POLICY_CODE = tcm.POLICY_CODE)
           and tcbp.policy_code = tcm.policy_code
           and til.policy_id = tcbp.policy_id
           and tc.customer_id = til.customer_id
           and tcp.policy_id = tcbp.policy_id
           and tcp.busi_item_id = tcbp.busi_item_id
           and tci.constants_key like '%OVER_RUN_TERMIN_PROD%'
           and tci.constants_value=tcbp.busi_prod_code
		]]>
		<include refid="queryTerminatePolicyByPolicyCode"/>
		<![CDATA[
           )T WHERE 1 = 1 AND MOD(T.POLICY_ID , #{modnum}) = #{start} AND ROWNUM <= 5000
		]]>
	</select>

	<!-- 查询是否为冻结状态 -->
	<select id="findPolicyFreeze" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT TPF.FREEZE_ID,
       TPF.CHANGE_ID,
       TPF.POLICY_CHG_ID,
       TPF.POLICY_ID,
       TPF.POLICY_CODE,
       TPF.FREEZE_DATE,
       TPF.FREEZE_CAUSE,
       TPF.EXEC_DOC_NO,
       TPF.EXEC_ORG,
       TPF.ORGAN_CODE,
       TPF.ANNT_NAME,
       TPF.ANNT_CERTI_TYPE,
       TPF.ANNT_CERTI_CODE,
       TPF.ANNT_PHONE,
       TPF.UNFREEZE_DATE,
       TPF.UNFREEZE_CAUSE,
       TPF.INSERT_TIME,
       TPF.INSERT_BY,
       TPF.INSERT_TIMESTAMP,
       TPF.UPDATE_BY,
       TPF.UPDATE_TIME,
       TPF.UPDATE_TIMESTAMP,
       TPF.UNFREEZE_CHANGE_ID,
       TPF.UNFREEZE_POLICY_CHG_ID,
       TPF.ACCEPT_CODE FROM DEV_PAS.t_Policy_Freeze TPF WHERE TPF.POLICY_ID = #{policy_id}
		]]>
	</select>

</mapper>