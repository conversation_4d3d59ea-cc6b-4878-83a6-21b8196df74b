<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.INBQueryCrsCustomerDao">

    	<sql id="PA_QueryCrsCustomerWhereCondition">
		<if test=" customer_tax_name != null and customer_tax_name != ''  "><![CDATA[ AND A.CUSTOMER_TAX_NAME = #{customer_tax_name} ]]></if>
		<if test=" customer_tax_birthday  != null  and  customer_tax_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_TAX_BIRTHDAY = #{customer_tax_birthday} ]]></if>
		<if test=" customer_tax_gender  != null "><![CDATA[ AND A.CUSTOMER_TAX_GENDER = #{customer_tax_gender} ]]></if>
		<if test=" customer_tax_cert_type != null and customer_tax_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_TAX_CERT_TYPE = #{customer_tax_cert_type} ]]></if>
		<if test=" customer_tax_certi_code != null and customer_tax_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_TAX_CERTI_CODE = #{customer_tax_certi_code} ]]></if>
		<if test=" customertax_id != null and customertax_id != ''  "><![CDATA[ AND A.CUSTOMERTAX_ID = #{customertax_id} ]]></if>
		
	</sql>

	<select id="queryCRSCustomer" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				  SELECT A.CUSTOMER_TAX_SURNAME,
			      A.CUSTOMER_TAX_ENAME,
			      A.CUSTOMER_TAX_NAME,
			      A.TAX_RESIDENT_TYPE,
			      B.LIVE_ADDRESS_CHN,
			      ((SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.BIRTH_STATE_CHN = O.CODE ) || 
                   (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.BIRTH_CITY_CHN = O.CODE ) || 
                   (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.BIRTH_DISTRICT_CHN  = O.CODE )
                  || B.BIRTH_ADDRESS_CHN) AS BIRTHPLACECHS,
			      (B.LIVE_STATE_ENGLISH || B.LIVE_CITY_ENGLISH || B.LIVE_DISTRICT_ENGLISH || B.LIVE_ADDRESS_ENGLISH) AS CURRENTADDRESS,
			      (B.BIRTH_STATE_ENGLISH || B.BIRTH_CITY_ENGLISH || B.BIRTH_DISTRICT_ENGLISH || B.BIRTH_ADDRESS_ENGLISH) AS BIRTHPLACEEN,
			      (SELECT wm_concat(C.TAX_NUMBER) FROM APP___PAS__DBUSER.T_TAX_INFO C WHERE C.CUSTOMERTAX_ID=A.CUSTOMERTAX_ID) AS TEXPAYERIDNUM,
                  (SELECT wm_concat(C.TAX_COUNTRY) FROM APP___PAS__DBUSER.T_TAX_INFO C WHERE C.CUSTOMERTAX_ID=A.CUSTOMERTAX_ID) AS TAXREGION,
                  (SELECT wm_concat(C.CAUSE_INS) FROM APP___PAS__DBUSER.T_TAX_INFO C WHERE C.CUSTOMERTAX_ID=A.CUSTOMERTAX_ID) AS CANTREASON  
			 FROM APP___PAS__DBUSER.T_CUSTOMER_TAX A
			 LEFT JOIN APP___PAS__DBUSER.T_ADDRESS_TAX B
			   ON A.CUSTOMERTAX_ID = B.CUSTOMERTAX_ID
               WHERE 1=1
			]]>
          <include refid="PA_QueryCrsCustomerWhereCondition" />
	</select>
	
</mapper>
