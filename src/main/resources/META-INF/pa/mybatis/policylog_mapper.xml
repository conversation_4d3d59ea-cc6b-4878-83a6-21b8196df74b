<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyLogDao">
<!--
	<sql id="PA_policyChangeWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" content_clob_id  != null "><![CDATA[ AND A.CONTENT_CLOB_ID = #{content_clob_id} ]]></if>
		<if test=" pre_policy_chg  != null "><![CDATA[ AND A.PRE_POLICY_CHG = #{pre_policy_chg} ]]></if>
		<if test=" change_flag != null and change_flag != ''  "><![CDATA[ AND <PERSON>.<PERSON>_FLAG = #{change_flag} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND A.VALIDATE_TIME = #{validate_time} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引查询操作 -->	
	<select id="findLastPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ ${sql} ]]>
	</select>
<!-- 查找记录某时点保单状态的保单变更ID -->
	<select id="findPolicyChgIdAfterTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT POLICY_CHG_ID
				   FROM (SELECT POLICY_CHG_ID, ROWNUM
				           FROM (SELECT POLICY_CHG_ID, T.VALIDATE_TIME
				                   FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL T
				                  WHERE 1=1 
				                  AND T.CHANGE_FLAG <> '0'
				                  AND T.SERVICE_CODE IN ('AE','CM','IO','CM','CT','EA','FK','IT',
'MC','MR','PR','PU','RE','RS','SR','XT','AE','IO','NS','YS','AP','CM','CT','DA','DT',
'EA','EN','FM','GB','IT','LC','MR','NS','PR','PU','RE','SR','XT','XX','YS','MR','PA','PRE','CLMEND',
'BC','CM','PU','MD','NS','PT','PU','CM','FM','RE','AM','CA','CT','DA','DT','EA','EN','FM','IO','IT','LC','MD','MR','NS','PA','PG','PR','PT','PU','RA','RE','SR','XD','XT','XX','YS','NS','PU','BC','PRL','PAPL','PILPI','PLE','PLPE','PME','PNL','PPD','PPL')
				                  ]]>
<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND to_char(T.VALIDATE_TIME,'yyyy-mm-dd') > to_char(#{validate_time,jdbcType=DATE},'yyyy-mm-dd') ]]></if>
<if test=" policy_id  != null "><![CDATA[ AND T.POLICY_ID = #{policy_id} ]]></if>
<if test=" related_id_is_null  != null "><![CDATA[ AND T.RELATED_ID is null ]]></if>
				                  <![CDATA[
				                  order by T.VALIDATE_TIME ASC,T.POLICY_CHG_ID ASC))
				  where rownum = 1 
		]]>
	</select>
	
	<!-- 根据条件查找保单变更前保单的状态 -->
	<select id="findLogByCxPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			select *
			  from APP___PAS__DBUSER.t_contract_master_log tlog
			 where tlog.log_id = (select log_id
			                        from APP___PAS__DBUSER.t_contract_master_cx tcx
			                       where 1 = 1
			                         and tcx.policy_chg_id = 334585
			                         and tcx.policy_id = 329146
			                         )
		]]>
	</select>
	
	<!-- 查询某一时点的某个表的信息 -->	
	<select id="findPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
<!-- 		${sql}  -->
		<![CDATA[ 
			select * from ${table_name} where 1=1 ${condition}
		]]>
	</select>
	
	<!-- 查询某一时点的某个表的信息 -->	
	<select id="findPolicyInfo1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select * from (select * from ${table_name} where 1=1 ${condition} ) t where  rownum = 1 
		]]>
	</select>
	
	<!-- 查询某一时点的某个表的信息 -->	
	<select id="findPremPolicyChg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select *
				  from (select ROW_NUMBER() OVER(ORDER BY LEVEL DESC) rn, ta.*
				          from (select *
				                  from APP___PAS__DBUSER.V_POLICY_CHANGE_ALL t1
				                 where 1 = 1
				                   and policy_id =
				                       (select policy_id
				                          from APP___PAS__DBUSER.t_contract_master t
				                         where 1=1]]>
<if test=" policy_id  != null "><![CDATA[ AND T.POLICY_ID = #{policy_id} ]]></if>				                         
				                 <![CDATA[)) ta
				         start with pre_policy_chg is null
				        CONNECT BY PRIOR policy_chg_id = pre_policy_chg)
				 where rn = 1 ]]>
	</select>
	
	<!-- 查询某一时点的某个表的信息 -->	
	<select id="findLogAndType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ ${sql} and tcx.policy_chg_id > ${policy_chg_id}  and tcx.oper_type in (2,3) ]]>
	</select>
	
	<update id="updateAllCx" parameterType="java.util.Map" >
		<![CDATA[ update ${table_name} set ${sql} where 1=1 ${condition}]]>
	</update>
	
	
	<!-- 动态查询表的历史时点信息 :多条数据-->	
	<select id="findLogsByChgIdAndPrmId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select ${select_property} 
		  from dev_pas.${table_name}_log tcl 
		 where tcl.log_id in (select log_id
		                        from dev_pas.${table_name}_cx t 
		                       where (t.log_id != t.pre_log_id or t.pre_log_id is null)
		                         and t.${prim_id_code} = ${prim_id_value}
		                         and t.policy_chg_id = ${policy_chg_id}
		                      union
		                      select bb.log_id
		                        from (select *
		                                from dev_pas.${table_name}_cx t
		                               where t.change_seq = 1
		                                 and t.log_id = t.pre_log_id
		                                 and t.${prim_id_code} = ${prim_id_value}
		                                 and t.policy_chg_id = ${policy_chg_id}) aa,
		                             dev_pas.${table_name}_cx bb 
		                       where bb.log_id != bb.pre_log_id
		                           and bb.pre_log_id = aa.pre_log_id
		                           and bb.${prim_id_code} = ${prim_id_value}
		                           and aa.${prim_id_code} = bb.${prim_id_code})
		union
		select ${select_property}
		  from dev_pas.${table_name} tcl
		 where tcl.${prim_id_code} = ${prim_id_value}
		   and not exists
		 (select 1
		          from (select tcl.*
		                  from dev_pas.${table_name}_log tcl
		                 where tcl.log_id in
		                       (select log_id
		                          from dev_pas.${table_name}_cx t
		                         where (t.log_id != t.pre_log_id or t.pre_log_id is null)
		                         and t.${prim_id_code} = ${prim_id_value}
		                         and t.policy_chg_id = ${policy_chg_id}
		                        union
		                        select bb.log_id
		                          from (select *
		                                  from dev_pas.${table_name}_cx t
		                                 where t.change_seq = 1
		                                 and t.log_id = t.pre_log_id
		                                 and t.${prim_id_code} = ${prim_id_value}
		                                 and t.policy_chg_id = ${policy_chg_id}) aa,
		                               dev_pas.${table_name}_cx bb 
		                         where bb.log_id != bb.pre_log_id
		                           and bb.pre_log_id = aa.pre_log_id
		                           and bb.${prim_id_code} = ${prim_id_value}
		                           and aa.${prim_id_code} = bb.${prim_id_code})) abc
		         where tcl.${prim_id_code} = abc.${prim_id_code})
		]]>
	</select>
	
	<!-- 动态查询表的历史时点信息 ：单条数据-->	
	<select id="findLogByChgIdAndPrmId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select ${select_property} 
		  from dev_pas.${table_name}_log tcl 
		 where tcl.log_id in (select log_id
		                        from dev_pas.${table_name}_cx t 
		                       where t.log_id != t.pre_log_id
		                         and t.policy_chg_id = ${policy_chg_id}
		                         and t.${prim_id_code} = ${prim_id_value}
		                      union
		                      select bb.log_id
		                        from (select *
		                                from dev_pas.${table_name}_cx t
		                               where t.log_id = t.pre_log_id
		                                 and t.policy_chg_id = ${policy_chg_id}
		                                 and t.${prim_id_code} = ${prim_id_value}) aa,
		                             dev_pas.${table_name}_cx bb 
		                       where bb.log_id != bb.pre_log_id
		                         and bb.pre_log_id = aa.pre_log_id
		                         and bb.${prim_id_code} = ${prim_id_value}
		                         and aa.${prim_id_code} = bb.${prim_id_code})
		union
		select ${select_property}
		  from dev_pas.${table_name} tcl
		 where tcl.${prim_id_code} = ${prim_id_value}
		   and not exists
		 (select 1
		          from (select tcl.*
		                  from dev_pas.${table_name}_log tcl
		                 where tcl.log_id in
		                       (select log_id
		                          from dev_pas.${table_name}_cx t
		                         where t.log_id != t.pre_log_id
		                           and t.${prim_id_code} = ${prim_id_value}
		                           and t.policy_chg_id = ${policy_chg_id}
		                        union
		                        select bb.log_id
		                          from (select *
		                                  from dev_pas.${table_name}_cx t
		                                 where t.log_id = t.pre_log_id
		                                   and t.${prim_id_code} = ${prim_id_value}
		                                   and t.policy_chg_id = ${policy_chg_id}) aa,
		                               dev_pas.${table_name}_cx bb 
		                         where bb.pre_log_id = aa.pre_log_id
		                           and aa.${prim_id_code} = bb.${prim_id_code}
		                           and bb.${prim_id_code} = ${prim_id_value}
		                           and bb.log_id != bb.pre_log_id)) abc
		         where tcl.${prim_id_code} = abc.${prim_id_code})
		]]>
	</select>
	
	
	<select id="PA_findPolicyChangeByTimeMin" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select policy_chg_id
				   from (select policy_chg_id, rownum
				           from (select policy_chg_id, t.validate_time
				                   from APP___PAS__DBUSER.V_POLICY_CHANGE_ALL t
				                  where 1=1
				                  AND T.CHANGE_FLAG <> '0'
				                   ]]>
<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND to_char(T.VALIDATE_TIME,'yyyy-mm-dd') < to_char(#{validate_time,jdbcType=DATE},'yyyy-mm-dd') ]]></if>
<if test=" policy_id  != null "><![CDATA[ AND T.POLICY_ID = #{policy_id} ]]></if>
<if test=" related_id_is_null  != null "><![CDATA[ AND T.RELATED_ID is null ]]></if>
				                  <![CDATA[
				                  order by T.VALIDATE_TIME desc,T.POLICY_CHG_ID desc))
				  where rownum = 1 
		]]>
	</select>
	
	<!-- 	查存实际需要执行的log信息 -->
	<select id="PA_findPolicyChangeByLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT POLICY_CHG_ID
				   FROM (SELECT POLICY_CHG_ID, ROWNUM
				           FROM (SELECT POLICY_CHG_ID, T.VALIDATE_TIME
				                   FROM APP___PAS__DBUSER.V_POLICY_CHANGE_ALL T
				                  WHERE 1=1 
				                  AND T.CHANGE_FLAG <> '0'
				                  AND T.SERVICE_CODE IN ('PREF',
                                          'PRE',
                                          'NS',
                                          'CLMEND',
                                          'PRL',
                                          'PAPL',
                                          'PILPI',
                                          'PLE',
                                          'PLPE',
                                          'PME',
                                          'PNL',
                                          'PPD',
                                          'PPL')
				                  ]]>
		<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND to_char(T.VALIDATE_TIME,'yyyy-mm-dd') > to_char(#{validate_time,jdbcType=DATE},'yyyy-mm-dd') ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND T.POLICY_ID = #{policy_id} ]]></if>
		<if test=" related_id_is_null  != null "><![CDATA[ AND T.RELATED_ID is null ]]></if>
				                  <![CDATA[
				                  order by T.VALIDATE_TIME ASC,T.POLICY_CHG_ID ASC))
				  where rownum = 1 
		]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="findContractMasterLogLastPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
					  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
					          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG T
					       WHERE 1 = 1
					         AND T.POLICY_ID = #{policy_id}
					         ORDER BY T.INSERT_TIMESTAMP DESC)
					 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findContractBusiProdLogLastPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
				  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
				          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_LOG T
				       WHERE 1 = 1
				         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" busi_item_id != null and busi_item_id != '' "><![CDATA[ AND T.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
				 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findContractProductLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" item_id != null and item_id != '' "><![CDATA[ AND T.ITEM_ID = #{item_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>

	<select id="findContractExtendLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findPolicyHolderLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findInsuredListLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_INSURED_LIST_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>

	<select id="findBenefitInsuredLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>

	<select id="findContractBeneLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_CONTRACT_BENE_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findPayerLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_PAYER_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findPayerAccountLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findExtraPremLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_EXTRA_PREM_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findContractInvestLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findContractInvestRateLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="finPayPlanLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_PAY_PLAN_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" plan_id != null and plan_id != '' "><![CDATA[ AND T.PLAN_ID = #{plan_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findPayPlanPayeeLogLastPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="findSpecialAccountRelationLogLastChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, LOG_ID, POLICY_CHG_ID
			  FROM (SELECT T.LOG_ID, T.POLICY_CHG_ID
			          FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_LOG T
			       WHERE 1 = 1
			         AND T.POLICY_ID = #{policy_id}]]>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
		<![CDATA[  ORDER BY T.INSERT_TIMESTAMP DESC)
			 WHERE ROWNUM = 1 ]]>
	</select>

	<!-- 按索引查询操作 -->	
	<select id="findContractMasterCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
						  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_CX T
						 WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
	</select>
	
	<select id="findContractBusiPordCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" busi_item_id != null and busi_item_id != '' "><![CDATA[ AND T.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
	<select id="findContractProdctCxPolicyChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" item_id != null and item_id != '' "><![CDATA[ AND T.ITEM_ID = #{item_id} ]]></if>
	</select>
	
	<select id="findContractExtendCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findPolicyHolderCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_POLICY_HOLDER_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findInsuredListCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_INSURED_LIST_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findBenefitInsuredCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_BENEFIT_INSURED_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findPayerCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_PAYER_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findPayerAccountCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findExtraPremCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_EXTRA_PREM_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findContractInvestCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findContractInvestRateCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>

	<select id="findPayPlanCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_PAY_PLAN_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			<if test=" plan_id != null and plan_id != '' "><![CDATA[ AND T.PLAN_ID = #{plan_id} ]]></if>
	</select>

	<select id="findPayPlanPayeeCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<select id="findSpecialAccountRelationCxMaxChangeSeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(MAX(CHANGE_SEQ), 0) AS MAX_CHANGE_SEQ
					FROM APP___PAS__DBUSER.T_SPECIAL_ACCOUNT_RELATION_CX T
					   WHERE 1 = 1
						 AND T.POLICY_ID = #{policy_id} ]]>
			 <if test=" list_id != null and list_id != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
	</select>
</mapper>
