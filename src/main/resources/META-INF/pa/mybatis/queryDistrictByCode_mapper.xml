<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IQueryYearBonusReportDao">

	
	<!-- 查询所有操作 -->
	<select id="queryDistrictByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	    <![CDATA[
	        SELECT T.CODE,T.NAME FROM APP___PAS__DBUSER.T_DISTRICT T WHERE T.CODE=#{code}	
	    ]]>
	</select>
	
	<select id="findPremCountByItemId" resultType="java.lang.Integer"
		parameterType="java.util.Map">
	    <![CDATA[
	         SELECT COUNT(*)
	            FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,
	                 APP___PAS__DBUSER.T_PREM_ARAP          TPA,
	                 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
	            WHERE TCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
	               AND TPA.BUSI_PROD_CODE = TCBP.BUSI_PROD_CODE
	               AND TPA.POLICY_CODE=TCP.POLICY_CODE
	               AND TCP.POLICY_CODE=TCBP.POLICY_CODE
	               AND TPA.FEE_STATUS IN ('01', '19')
	               AND TCP.ITEM_ID = #{item_id}
	    ]]>
	</select>
	
	<select id="findDistictAndDepartment" resultType="java.util.Map"
		parameterType="java.util.Map">
	    <![CDATA[
	        	SELECT T.SALES_ORGAN_NAME,T.PARENT_CODE,T.ORGAN_LEVEL_CODE FROM  APP___PAS__DBUSER.T_SALES_ORGAN T WHERE 1=1 
	    ]]>
	    <if test=" sales_organ_code != null and sales_organ_code != ''  "><![CDATA[ AND T.SALES_ORGAN_CODE = #{sales_organ_code} ]]></if>
	    <if test=" parent_code != null and parent_code != ''  "><![CDATA[ AND T.PARENT_CODE = #{parent_code} ]]></if>
	</select>
	
</mapper>