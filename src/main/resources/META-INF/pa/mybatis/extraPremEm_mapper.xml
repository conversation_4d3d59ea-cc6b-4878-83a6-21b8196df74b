<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.impl.ExtraPremEmDaoImpl">
	
	<sql id="queryExtraPremEmByPolicyCode">
		<if test="policy_id != null and policy_id != '' "><![CDATA[AND t.policy_id = #{policy_id}]]></if>
		<if test="item_id != null and item_id != '' "><![CDATA[AND t.item_id = #{item_id}]]></if>
	</sql>
	<!-- <select id="findExtraPremEmList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select t.policy_code,t.busi_prod_code,
				t.product_code,t.em_value
				 from APP___PAS__DBUSER.t_extra_prem t
				 where APP___PAS__DBUSER.t_extra_prem.policy_code=#{policy_code}]]>
		<include refid="queryExtraPremEmByPolicyCode" />
	</select> -->
	
	<select id="findExtraPremEmList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.insert_time,t.policy_code,t.busi_prod_code,t.product_code,t.em_value
			FROM APP___PAS__DBUSER.t_extra_prem t
			WHERE 1 = 1
		]]>
         <include refid="queryExtraPremEmByPolicyCode" />	
         <![CDATA[
         	GROUP BY t.insert_time,
         			t.policy_code,
         			t.busi_prod_code,
         			t.product_code,
         			t.em_value 
		]]>
	</select>
</mapper>


























