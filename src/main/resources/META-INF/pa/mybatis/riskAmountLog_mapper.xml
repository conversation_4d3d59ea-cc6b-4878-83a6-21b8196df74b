<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRiskAmountLogDao">
<!--
	<sql id="PA_riskAmountLogWhereCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" amount_status  != null "><![CDATA[ AND A.AMOUNT_STATUS = #{amount_status} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT = #{risk_amount} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryRiskAmountLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addRiskAmountLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_RISK_AMOUNT_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RISK_AMOUNT_LOG(
				INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, START_TIME, ITEM_ID, RISK_TYPE, BUSI_PROD_CODE, 
				APPLY_CODE, INSERT_TIMESTAMP, LOG_ID, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, 
				END_TIME, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, BUSI_ITEM_ID, AMOUNT_STATUS, POLICY_ID, 
				INTERNAL_CODE, RISK_AMOUNT ) 
			VALUES (
				SYSDATE, #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{start_time, jdbcType=DATE} , #{item_id, jdbcType=NUMERIC} , #{risk_type, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{end_time, jdbcType=DATE} , #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{amount_status, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{internal_code, jdbcType=VARCHAR} , #{risk_amount, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteRiskAmountLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RISK_AMOUNT_LOG WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateRiskAmountLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    START_TIME = #{start_time, jdbcType=DATE} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			RISK_TYPE = #{risk_type, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    AMOUNT_STATUS = #{amount_status, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			INTERNAL_CODE = #{internal_code, jdbcType=VARCHAR} ,
		    RISK_AMOUNT = #{risk_amount, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findRiskAmountLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.START_TIME, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.LIST_ID, 
			A.END_TIME, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.AMOUNT_STATUS, A.POLICY_ID, 
			A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryRiskAmountLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapRiskAmountLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.START_TIME, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.LIST_ID, 
			A.END_TIME, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.AMOUNT_STATUS, A.POLICY_ID, 
			A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllRiskAmountLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.START_TIME, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.LIST_ID, 
			A.END_TIME, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.AMOUNT_STATUS, A.POLICY_ID, 
			A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findRiskAmountLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RISK_AMOUNT_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryRiskAmountLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_ID, B.START_TIME, B.ITEM_ID, B.RISK_TYPE, B.BUSI_PROD_CODE, 
			B.APPLY_CODE, B.LOG_ID, B.POLICY_CODE, B.LIST_ID, 
			B.END_TIME, B.LOG_TYPE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.AMOUNT_STATUS, B.POLICY_ID, 
			B.INTERNAL_CODE, B.RISK_AMOUNT FROM (
					SELECT ROWNUM RN, A.CUSTOMER_ID, A.START_TIME, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.LIST_ID, 
			A.END_TIME, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.AMOUNT_STATUS, A.POLICY_ID, 
			A.INTERNAL_CODE, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_RISK_AMOUNT_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
