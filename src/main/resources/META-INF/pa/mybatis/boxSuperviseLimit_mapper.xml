<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBoxSuperviseLimitDao">

	<sql id="boxSuperviseLimitWhereCondition">
		<if test=" max_rate  != null "><![CDATA[ AND A.MAX_RATE = #{max_rate} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" min_amount  != null "><![CDATA[ AND A.MIN_AMOUNT = #{min_amount} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" min_rate  != null "><![CDATA[ AND A.MIN_RATE = #{min_rate} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" max_amount  != null "><![CDATA[ AND A.MAX_AMOUNT = #{max_amount} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" startDate !=null and startDate !='' "><![CDATA[ AND A.START_DATE >= #{startDate}]]></if>
		<if test=" endDate != null and endDate !='' "><![CDATA[AND A.START_DATE <= #{endDate}]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryBoxSuperviseLimitByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addBoxSuperviseLimit"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_BOX_SUPERVISE_LIMIT__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT(
				MAX_RATE, INSERT_TIME, END_DATE, FUND_CODE, UPDATE_TIME, START_TIME, MIN_AMOUNT, 
				STATUS, MIN_RATE, INSERT_TIMESTAMP, START_DATE, MAX_AMOUNT, UPDATE_BY, LIST_ID, 
				UPDATE_TIMESTAMP, END_TIME, INSERT_BY ) 
			VALUES (
				#{max_rate, jdbcType=NUMERIC}, SYSDATE , #{end_date, jdbcType=DATE} , #{fund_code, jdbcType=VARCHAR} , SYSDATE , #{start_time, jdbcType=TIME} , #{min_amount, jdbcType=NUMERIC} 
				, #{status, jdbcType=NUMERIC} , #{min_rate, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{start_date, jdbcType=DATE} , #{max_amount, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{end_time, jdbcType=TIME} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteBoxSuperviseLimit" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateBoxSuperviseLimit" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT ]]>
		<set>
		<trim suffixOverrides=",">
		    MAX_RATE = #{max_rate, jdbcType=NUMERIC} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    START_TIME = #{start_time, jdbcType=TIME} ,
		    MIN_AMOUNT = #{min_amount, jdbcType=NUMERIC} ,
		    STATUS = #{status, jdbcType=NUMERIC} ,
		    MIN_RATE = #{min_rate, jdbcType=NUMERIC} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
		    MAX_AMOUNT = #{max_amount, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=TIME} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findBoxSuperviseLimitByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.MIN_AMOUNT, 
			A.STATUS, A.MIN_RATE, A.START_DATE, A.MAX_AMOUNT, A.LIST_ID, 
			A.END_TIME FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT A WHERE 1 = 1  ]]>
		<include refid="queryBoxSuperviseLimitByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapBoxSuperviseLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.MIN_AMOUNT, 
			A.STATUS, A.MIN_RATE, A.START_DATE, A.MAX_AMOUNT, A.LIST_ID, 
			A.END_TIME FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT A WHERE ROWNUM <=  1000  ]]>
		<include refid="boxSuperviseLimitWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllBoxSuperviseLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.MIN_AMOUNT, 
			A.STATUS, A.MIN_RATE, A.START_DATE, A.MAX_AMOUNT, A.LIST_ID, 
			A.END_TIME FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT A WHERE ROWNUM <=  1000  ]]>
		 <include refid="boxSuperviseLimitWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findBoxSuperviseLimitTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT A WHERE 1 = 1  ]]>
		<include refid="boxSuperviseLimitWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryBoxSuperviseLimitForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MAX_RATE, B.END_DATE, B.FUND_CODE, B.START_TIME, B.MIN_AMOUNT, 
			B.STATUS, B.MIN_RATE, B.START_DATE, B.MAX_AMOUNT, B.LIST_ID, 
			B.END_TIME FROM (
					SELECT ROWNUM RN, A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, A.MIN_AMOUNT, 
			A.STATUS, A.MIN_RATE, A.START_DATE, A.MAX_AMOUNT, A.LIST_ID, 
			A.END_TIME FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="boxSuperviseLimitWhereCondition" />
		<![CDATA[ ORDER BY A.INSERT_TIME ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 	任晓迪 -->
    <sql id="boxInternalLimitWhereCondition">
		<if test=" max_rate  != null "><![CDATA[ AND A.MAX_RATE = #{max_rate} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" min_rate  != null "><![CDATA[ AND A.MIN_RATE = #{min_rate} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" confirm_desc != null and confirm_desc != ''  "><![CDATA[ AND A.CONFIRM_DESC = #{confirm_desc} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" confirm_date  != null  and  confirm_date  != ''  "><![CDATA[ AND A.CONFIRM_DATE = #{confirm_date} ]]></if>
		<if test=" startDate !=null and startDate !='' "><![CDATA[ AND A.START_DATE >= #{startDate}]]></if>
		<if test=" endDate != null and endDate !='' "><![CDATA[AND A.START_DATE <= #{endDate}]]></if>
		<if test=" confirm_result  != null "><![CDATA[ AND A.CONFIRM_RESULT = #{confirm_result} ]]></if>
	</sql>
<!-- 查询BOX限额个数操作 -->
	<select id="findBoxLimitTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		
		<![CDATA[ SELECT SUM(CON)  from (]]>
	
		<![CDATA[ SELECT COUNT(1) AS CON FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE 1 = 1  ]]>
		 <include refid="boxInternalLimitWhereCondition" />
		 <![CDATA[ UNION ALL]]>
		 <![CDATA[ SELECT COUNT(1) AS CON FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT A WHERE 1 = 1  ]]>
		 <include refid="boxSuperviseLimitWhereCondition" />
		  <![CDATA[ ) T]]>
	</select>

<!-- 分页查询操作 -->
	<select id="queryBoxLimitForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MAX_RATE, B.END_DATE, B.FUND_CODE, B.START_TIME, 
			B.STATUS, B.MIN_RATE, B.START_DATE,  B.LIST_ID, B.END_TIME FROM (
			
			SELECT ROWNUM AS RN, D.MAX_RATE, D.END_DATE, D.FUND_CODE, D.START_TIME, 
			D.STATUS, D.MIN_RATE, D.START_DATE,  D.LIST_ID, D.END_TIME  FROM
			( SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, 
			1 AS STATUS, A.MIN_RATE, A.START_DATE, A.LIST_ID, A.END_TIME FROM APP___PAS__DBUSER.T_BOX_SUPERVISE_LIMIT A WHERE 1=1 ]]>
		<include refid="boxSuperviseLimitWhereCondition" />
		
		<![CDATA[ UNION ALL ]]> 
		
		<![CDATA[ SELECT A.MAX_RATE, A.END_DATE, A.FUND_CODE, A.START_TIME, 2 AS STATUS, 
			A.MIN_RATE, A.START_DATE,  A.LIST_ID, A.END_TIME FROM APP___PAS__DBUSER.T_BOX_INTERNAL_LIMIT A WHERE 1=1 ]]>
		<include refid="boxInternalLimitWhereCondition" />
		<![CDATA[ ) D WHERE ROWNUM <= #{LESS_NUM} ORDER BY D.FUND_CODE]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
