<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="policyAccountStreamLog">
<!--
	<sql id="PA_policyAccountStreamLogWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" capitalized_date  != null  and  capitalized_date  != ''  "><![CDATA[ AND A.CAPITALIZED_DATE = #{capitalized_date} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" capital_balance  != null "><![CDATA[ AND A.CAPITAL_BALANCE = #{capital_balance} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_type  != null "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
		<if test=" balance_date  != null  and  balance_date  != ''  "><![CDATA[ AND A.BALANCE_DATE = #{balance_date} ]]></if>
		<if test=" regular_repay  != null "><![CDATA[ AND A.REGULAR_REPAY = #{regular_repay} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" stream_pol_chg_id  != null "><![CDATA[ AND A.STREAM_POL_CHG_ID = #{stream_pol_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyAccountStreamLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyAccountStreamLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_POLICY_ACCOUNT_STREAM_LOG__L.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG (
				MONEY_CODE, CAPITALIZED_DATE, INTEREST_CAPITAL, ACCOUNT_ID, INSERT_TIME, CAPITAL_BALANCE, INTEREST_SUM, 
				UPDATE_TIME, ITEM_ID, ACCOUNT_TYPE, BALANCE_DATE, REGULAR_REPAY, INSERT_TIMESTAMP, LOG_ID, 
				UPDATE_BY, INTEREST_BALANCE, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, BUSI_ITEM_ID, 
				STREAM_POL_CHG_ID, POLICY_ID, STREAM_ID,LOAN_START_DATE ,REPAY_DUE_DATE,INTEREST_EXPECT) 
			VALUES (
				#{money_code, jdbcType=VARCHAR}, #{capitalized_date, jdbcType=DATE} , #{interest_capital, jdbcType=NUMERIC} , #{account_id, jdbcType=NUMERIC} , SYSDATE , #{capital_balance, jdbcType=NUMERIC} , #{interest_sum, jdbcType=NUMERIC} 
				, SYSDATE , #{item_id, jdbcType=NUMERIC} , #{account_type, jdbcType=NUMERIC} , #{balance_date, jdbcType=DATE} , #{regular_repay, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} 
				, #{update_by, jdbcType=NUMERIC} , #{interest_balance, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{stream_pol_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{stream_id, jdbcType=NUMERIC} , #{loan_start_date, jdbcType=DATE},#{repay_due_date, jdbcType=DATE}
				, #{interest_expect, jdbcType=NUMERIC}
				) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyAccountStreamLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG  WHERE LOG_ID=#{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyAccountStreamLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG  ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    CAPITALIZED_DATE = #{capitalized_date, jdbcType=DATE} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    ACCOUNT_ID = #{account_id, jdbcType=NUMERIC} ,
		    CAPITAL_BALANCE = #{capital_balance, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    ACCOUNT_TYPE = #{account_type, jdbcType=NUMERIC} ,
		    BALANCE_DATE = #{balance_date, jdbcType=DATE} ,
		    LOAN_START_DATE = #{loan_start_date, jdbcType=DATE} ,
		    REPAY_DUE_DATE = #{repay_due_date, jdbcType=DATE} ,
		    REGULAR_REPAY = #{regular_repay, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    STREAM_POL_CHG_ID = #{stream_pol_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    STREAM_ID = #{stream_id, jdbcType=NUMERIC} ,
		    INTEREST_EXPECT = #{interest_expect, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyAccountStreamLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE,A.INTEREST_EXPECT, A.REPAY_DUE_DATE,A.LOAN_START_DATE,A.CAPITALIZED_DATE, A.INTEREST_CAPITAL, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.LOG_ID, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.STREAM_POL_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG  A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyAccountStreamLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE,A.INTEREST_EXPECT,A.REPAY_DUE_DATE, A.LOAN_START_DATE,A.CAPITALIZED_DATE, A.INTEREST_CAPITAL, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.LOG_ID, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.STREAM_POL_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyAccountStreamLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE,A.INTEREST_EXPECT,A.REPAY_DUE_DATE, A.LOAN_START_DATE,A.CAPITALIZED_DATE, A.INTEREST_CAPITAL, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.LOG_ID, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.STREAM_POL_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyAccountStreamLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG  A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyAccountStreamLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.INTEREST_EXPECT,B.REPAY_DUE_DATE,B.LOAN_START_DATE,B.MONEY_CODE, B.CAPITALIZED_DATE, B.INTEREST_CAPITAL, B.ACCOUNT_ID, B.CAPITAL_BALANCE, B.INTEREST_SUM, 
			B.ITEM_ID, B.ACCOUNT_TYPE, B.BALANCE_DATE, B.REGULAR_REPAY, B.LOG_ID, 
			B.INTEREST_BALANCE, B.LOG_TYPE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, 
			B.STREAM_POL_CHG_ID, B.POLICY_ID, B.STREAM_ID FROM (
					SELECT ROWNUM RN, A.MONEY_CODE,A.INTEREST_EXPECT,A.REPAY_DUE_DATE,A.LOAN_START_DATE, A.CAPITALIZED_DATE, A.INTEREST_CAPITAL, A.ACCOUNT_ID, A.CAPITAL_BALANCE, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_TYPE, A.BALANCE_DATE, A.REGULAR_REPAY, A.LOG_ID, 
			A.INTEREST_BALANCE, A.LOG_TYPE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.STREAM_POL_CHG_ID, A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_LOG  A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
