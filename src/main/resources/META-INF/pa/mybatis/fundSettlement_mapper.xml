<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFundSettlementDao">

	<sql id="fundSettlementWhereCondition">
		<if test=" batch_date  != null  and  batch_date  != ''  "><![CDATA[ AND A.BATCH_DATE = #{batch_date} ]]></if>
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		<if test=" last_gurnt_balance  != null "><![CDATA[ AND A.LAST_GURNT_BALANCE = #{last_gurnt_balance} ]]></if>
		<if test=" adjust_flag  != null "><![CDATA[ AND A.ADJUST_FLAG = #{adjust_flag} ]]></if>
		<if test=" last_balance  != null "><![CDATA[ AND A.LAST_BALANCE = #{last_balance} ]]></if>
		<if test=" settle_date  != null  and  settle_date  != ''  "><![CDATA[ AND A.SETTLE_DATE = #{settle_date} ]]></if>
		<if test=" doc_status  != null "><![CDATA[ AND A.DOC_STATUS = #{doc_status} ]]></if>
		<if test=" gurnt_interest  != null "><![CDATA[ AND A.GURNT_INTEREST = #{gurnt_interest} ]]></if>
		<if test=" adjust_type != null and adjust_type != ''  "><![CDATA[ AND A.ADJUST_TYPE = #{adjust_type} ]]></if>
		<if test=" interest  != null "><![CDATA[ AND A.INTEREST = #{interest} ]]></if>
		<if test=" settlement_id  != null "><![CDATA[ AND A.SETTLEMENT_ID = #{settlement_id} ]]></if>
		<if test=" adjust_amount  != null "><![CDATA[ AND A.ADJUST_AMOUNT = #{adjust_amount} ]]></if>
		<if test=" balance  != null "><![CDATA[ AND A.BALANCE = #{balance} ]]></if>
		<if test=" balance_ba  != null "><![CDATA[ AND A.BALANCE_BA = #{balance_ba} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" last_settle_date  != null  and  last_settle_date  != ''  "><![CDATA[ AND A.LAST_SETTLE_DATE = #{last_settle_date} ]]></if>
		<if test=" interest_rate  != null "><![CDATA[ AND A.INTEREST_RATE = #{interest_rate} ]]></if>
		<if test=" adjust_date  != null  and  adjust_date  != ''  "><![CDATA[ AND A.ADJUST_DATE = #{adjust_date} ]]></if>
		<if test=" gurnt_balance  != null "><![CDATA[ AND A.GURNT_BALANCE = #{gurnt_balance} ]]></if>
		<if test=" end_interest_rate  != null "><![CDATA[ AND A.END_INTEREST_RATE = #{end_interest_rate} ]]></if>
		<if test=" gurnt_interest_rate  != null "><![CDATA[ AND A.GURNT_INTEREST_RATE = #{gurnt_interest_rate} ]]></if>
		<if test=" settle_date_other != null and settle_date_other != '' "><![CDATA[AND A.SETTLE_DATE <= #{settle_date_other}]]></if>
		<!-- 计算部分领取时的查询参数 -->
		<if test=" apply_date != null and apply_date != '' "><![CDATA[AND A.SETTLE_DATE < #{apply_date}]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryFundSettlementBySettlementIdCondition">
		<if test=" settlement_id  != null "><![CDATA[ AND A.SETTLEMENT_ID = #{settlement_id} ]]></if>
	</sql>	
	<sql id="queryFundSettlementByInvestIdCondition">
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		<if test=" settle_date_other != null and settle_date_other != '' "><![CDATA[AND A.SETTLE_DATE <= #{settle_date_other}]]></if>
		<!-- 持续奖金参数，针对持续奖金是否更新结算信息表 -->
		<if test=" settle_date_prize != null and settle_date_prize != '' "><![CDATA[AND A.SETTLE_DATE > #{settle_date_prize}]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addFundSettlement"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="settlement_id">
			SELECT APP___PAS__DBUSER.S_FUND_TRANS__TRANS_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_SETTLEMENT(
				BATCH_DATE, INVEST_ID, LAST_GURNT_BALANCE, ADJUST_FLAG, LAST_BALANCE, SETTLE_DATE, DOC_STATUS, 
				GURNT_INTEREST, ADJUST_TYPE, INTEREST, INSERT_TIMESTAMP, UPDATE_BY, SETTLEMENT_ID, ADJUST_AMOUNT, 
				BALANCE, BALANCE_BA, INSERT_TIME, UPDATE_TIME, ACCOUNT_CODE, LAST_SETTLE_DATE, INTEREST_RATE, 
				ADJUST_DATE, GURNT_BALANCE, UPDATE_TIMESTAMP, INSERT_BY, END_INTEREST_RATE, GURNT_INTEREST_RATE ) 
			VALUES (
				#{batch_date, jdbcType=DATE}, #{invest_id, jdbcType=NUMERIC} , #{last_gurnt_balance, jdbcType=NUMERIC} , #{adjust_flag, jdbcType=NUMERIC} , #{last_balance, jdbcType=NUMERIC} , #{settle_date, jdbcType=DATE} , #{doc_status, jdbcType=NUMERIC} 
				, #{gurnt_interest, jdbcType=NUMERIC} , #{adjust_type, jdbcType=VARCHAR} , #{interest, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{settlement_id, jdbcType=NUMERIC} , #{adjust_amount, jdbcType=NUMERIC} 
				, #{balance, jdbcType=NUMERIC} , #{balance_ba, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{account_code, jdbcType=VARCHAR} , #{last_settle_date, jdbcType=DATE} , #{interest_rate, jdbcType=NUMERIC} 
				, #{adjust_date, jdbcType=DATE} , #{gurnt_balance, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{end_interest_rate, jdbcType=NUMERIC} , #{gurnt_interest_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteFundSettlement" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT WHERE  SETTLEMENT_ID=#{settlement_id}]]>
	</delete>
	<!-- 保全回退时删除操作-->	
	<delete id="deleteFundSettlementForRB" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT WHERE  INVEST_ID=#{invest_id} and settle_date>#{settle_date}]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateFundSettlement" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_SETTLEMENT ]]>
		<set>
		<trim suffixOverrides=",">
		     BATCH_DATE = #{batch_date, jdbcType=DATE} ,
		    INVEST_ID = #{invest_id, jdbcType=NUMERIC} ,
		    LAST_GURNT_BALANCE = #{last_gurnt_balance, jdbcType=NUMERIC} ,
		    ADJUST_FLAG = #{adjust_flag, jdbcType=NUMERIC} ,
		    LAST_BALANCE = #{last_balance, jdbcType=NUMERIC} ,
		    SETTLE_DATE = #{settle_date, jdbcType=DATE} ,
		    DOC_STATUS = #{doc_status, jdbcType=NUMERIC} ,
		    GURNT_INTEREST = #{gurnt_interest, jdbcType=NUMERIC} ,
			ADJUST_TYPE = #{adjust_type, jdbcType=VARCHAR} ,
		    INTEREST = #{interest, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SETTLEMENT_ID = #{settlement_id, jdbcType=NUMERIC} ,
		    ADJUST_AMOUNT = #{adjust_amount, jdbcType=NUMERIC} ,
		    BALANCE = #{balance, jdbcType=NUMERIC} ,
		    BALANCE_BA = #{balance_ba, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    LAST_SETTLE_DATE = #{last_settle_date, jdbcType=DATE} ,
		    INTEREST_RATE = #{interest_rate, jdbcType=NUMERIC} ,
		    ADJUST_DATE = #{adjust_date, jdbcType=DATE} ,
		    GURNT_BALANCE = #{gurnt_balance, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_INTEREST_RATE = #{end_interest_rate, jdbcType=NUMERIC} ,
		    GURNT_INTEREST_RATE = #{gurnt_interest_rate, jdbcType=NUMERIC} ,
		    MONEY_DEVOTE = #{money_devote, jdbcType=NUMERIC} ,
		    END_INTEREST = #{end_interest, jdbcType=NUMERIC} ,
		    POLICY_CASH_VALUE = #{policy_cash_value, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  1=1 AND SETTLEMENT_ID=#{settlement_id}]]>
	</update>
	<update id="updateFundSettlementDocStatus" parameterType="java.util.Map">
	
			UPDATE APP___PAS__DBUSER.T_FUND_SETTLEMENT
			<set>
				DOC_STATUS = #{doc_status,jdbcType=NUMERIC}
			</set>
			WHERE 1=1 AND SETTLEMENT_ID=#{settlement_id}
	</update>
<!-- 按索引查询操作 -->	
	<select id="findFundSettlementBySettlementId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.DOC_STATUS, 
			A.GURNT_INTEREST, A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, 
			A.BALANCE, A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE, 
			A.ADJUST_DATE, A.GURNT_BALANCE, A.END_INTEREST_RATE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE 1 = 1  ]]>
		<include refid="queryFundSettlementBySettlementIdCondition" />
	</select>
	
	<select id="findFundSettlementByInvestId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.DOC_STATUS, 
			A.GURNT_INTEREST, A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, 
			A.BALANCE, A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE, 
			A.ADJUST_DATE, A.GURNT_BALANCE, A.END_INTEREST_RATE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE 1 = 1  ]]>
		<include refid="queryFundSettlementByInvestIdCondition" />
	</select>
	
	<!-- 根据结算时间排列查询单条结算数据 -->
	<select id="findFundSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.DOC_STATUS, 
			A.GURNT_INTEREST, A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, 
			A.BALANCE, A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE, 
			A.ADJUST_DATE, A.GURNT_BALANCE, A.END_INTEREST_RATE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE 1 = 1  ]]>
		<include refid="fundSettlementWhereCondition" />
		<![CDATA[ ORDER BY A.SETTLE_DATE DESC) WHERE ROWNUM = 1]]>
	</select>
<!-- 按map查询操作 -->
	<select id="findAllMapFundSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.DOC_STATUS, 
			A.GURNT_INTEREST, A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, 
			A.BALANCE, A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE, 
			A.ADJUST_DATE, A.GURNT_BALANCE, A.END_INTEREST_RATE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllFundSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.DOC_STATUS, 
			A.GURNT_INTEREST, A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, 
			A.BALANCE, A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE, 
			A.ADJUST_DATE, A.GURNT_BALANCE, A.END_INTEREST_RATE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE ROWNUM <=  1000  ]]>
		<if test=" ct_apply_time != null and ct_apply_time != ''  "><![CDATA[ AND A.settle_date > #{ct_apply_time} ]]></if>
		<include refid="fundSettlementWhereCondition" />  
		<![CDATA[ORDER BY A.SETTLE_DATE DESC]]>
	</select>

<!-- 查询所有操作:结算时间升序 -->
	<select id="PA_findAllFundSettlementOrderBySettleDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.DOC_STATUS, 
			A.GURNT_INTEREST, A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, 
			A.BALANCE, A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE, 
			A.ADJUST_DATE, A.GURNT_BALANCE, A.END_INTEREST_RATE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE ROWNUM <=  1000  ]]>
		<include refid="fundSettlementWhereCondition" />  
		<![CDATA[ORDER BY A.SETTLE_DATE]]>
	</select>
<!-- 查询个数操作 -->
	<select id="findFundSettlementTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE 1 = 1  ]]>
		 <include refid="fundSettlementWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="queryFundSettlementForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BATCH_DATE, B.INVEST_ID, B.LAST_GURNT_BALANCE, B.ADJUST_FLAG, B.LAST_BALANCE, B.SETTLE_DATE, B.DOC_STATUS, 
			B.GURNT_INTEREST, B.ADJUST_TYPE, B.INTEREST, B.SETTLEMENT_ID, B.ADJUST_AMOUNT, 
			B.BALANCE, B.BALANCE_BA, B.ACCOUNT_CODE, B.LAST_SETTLE_DATE, B.INTEREST_RATE, B.MONEY_DEVOTE, B.END_INTEREST, B.POLICY_CASH_VALUE, 
			B.ADJUST_DATE, B.GURNT_BALANCE, B.END_INTEREST_RATE, B.GURNT_INTEREST_RATE FROM (
					SELECT ROWNUM RN, A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.DOC_STATUS, 
			A.GURNT_INTEREST, A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, 
			A.BALANCE, A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE, 
			A.ADJUST_DATE, A.GURNT_BALANCE, A.END_INTEREST_RATE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="fundSettlementWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 万能险账户价值结算查询 -->
<select id="queryAccountCostSql" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		 select '' as g_Type, 
           '' as gt_Type, 
           a.interest as GetMoney, 
           a.Settle_Date as BalanceDate,
           a.Interest_Rate as BalanceRate, 
           a.Last_Settle_Date as lastDate, 
           a.Last_Balance as InceptContValue,
           a.BALANCE as CurrentContValue ,
           a.Settle_Date as ChangeDate 
     	 from APP___PAS__DBUSER.T_FUND_SETTLEMENT a, APP___PAS__DBUSER.T_CONTRACT_INVEST b
			where a.account_code=b.account_code  and a.invest_id = b.list_id  and b.policy_id = #{policy_id}
	]]>
	<if test=" balance_start_date!=null "><![CDATA[and a.SETTLE_DATE>=  #{balance_start_date}]]></if>
	<if test=" balance_end_date!=null "><![CDATA[and a.SETTLE_DATE<=  #{balance_end_date}]]></if>
	order by a.SETTLE_DATE 
</select>
	<!-- 根据账户代码最早的结算数据 -->
	<select id="PA_findEarliestSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select * from
			(select * from APP___PAS__DBUSER.T_FUND_SETTLEMENT 
				where ACCOUNT_CODE=#{account_code} and INVEST_ID=#{invest_id} 
				order by SETTLE_DATE) 
			where rownum=1
		 ]]>
	</select>
	<!-- 根据账户代码最晚的结算数据 BALANCE,SETTLE_DATE,GURNT_INTEREST_RATE-->
	<select id="PA_findLatestSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select * from
			(select * from APP___PAS__DBUSER.T_FUND_SETTLEMENT  A
				where ACCOUNT_CODE=#{account_code} and INVEST_ID=#{invest_id} 
			]]>	
			<include refid="fundSettlementWhereCondition" />
			<![CDATA[ 
				 order by SETTLE_DATE desc) 
			where rownum=1
		 ]]>
	</select>
	<!-- 万能险账户价值结算查询-查询最新的SETTLE_DATE日期 -->
	<select id="findDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select g.SETTLE_DATE  from APP___PAS__DBUSER.T_FUND_SETTLEMENT g where #{account_code}=g.account_code and rownum=1 order by g.settle_date
		]]>
	</select>
	
	<!-- 根据账户代码查找结算数据并按照结算日期逆序SETTLE_DATE排序  by liubinit-->
	<select id="PA_findSettlementOrderBySettleDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select A.SETTLEMENT_ID, A.INVEST_ID, A.ACCOUNT_CODE, A.SETTLE_DATE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.INTEREST, A.GURNT_INTEREST_RATE, A.GURNT_INTEREST,
			A.LAST_BALANCE, A.BALANCE_BA, A.BALANCE, A.LAST_GURNT_BALANCE, A.GURNT_BALANCE, A.BATCH_DATE, A.ADJUST_FLAG, A.ADJUST_TYPE, A.ADJUST_DATE, A.ADJUST_AMOUNT 
			from APP___PAS__DBUSER.T_FUND_SETTLEMENT A where A.ACCOUNT_CODE=#{account_code} and ROWNUM <=  1000 
			order by A.SETTLE_DATE desc
			
		 ]]>
	</select>
	
	<!-- 按照险种号，结算年度，结算月度，万能险利率查询 -->
	<select id="PA_findSettlementByRiskCodeYearMonth" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				select 
			       t.settle_date,
			       t.interest_rate,
			       t.gurnt_interest_rate,
			       to_number('0.3') as end_interest_rate
			  from APP___PAS__DBUSER.T_fund_settlement t
 		where exists (select 1
          from APP___PAS__DBUSER.T_contract_invest ci, APP___PAS__DBUSER.T_contract_busi_prod cbp
         where ci.busi_item_id = cbp.busi_item_id
           and ci.list_id = t.invest_id and cbp.busi_prod_code= #{riskCode} )
           and to_char(t.settle_date,'yyyy') = #{year}
           and to_char(t.settle_date,'mm') = #{month}			
		 ]]>
	</select>
	<select id="findInvest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				select *
				  from (select case
				                 when t.balance > t.gurnt_balance then
				                  t.balance
				                 else
				                  t.gurnt_balance
				               end balance
				          from APP___PAS__DBUSER.T_FUND_SETTLEMENT t
				         where t.INVEST_ID = #{invest_id}
				         order by t.settle_date desc)
				 where rownum = 1
		 ]]>
	</select>

	<select id="PA_findLastFundSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 	SELECT B.BATCH_DATE,
					       B.INVEST_ID,
					       B.LAST_GURNT_BALANCE,
					       B.ADJUST_FLAG,
					       B.LAST_BALANCE,
					       B.SETTLE_DATE,
					       B.DOC_STATUS,
					       B.GURNT_INTEREST,
					       B.ADJUST_TYPE,
					       B.INTEREST,
					       B.SETTLEMENT_ID,
					       B.ADJUST_AMOUNT,
					       B.BALANCE,
					       B.BALANCE_BA,
					       B.ACCOUNT_CODE,
					       B.LAST_SETTLE_DATE,
					       B.INTEREST_RATE,
					       B.ADJUST_DATE,
					       B.GURNT_BALANCE,
					       B.END_INTEREST_RATE,
					       B.GURNT_INTEREST_RATE
					  FROM (SELECT A.BATCH_DATE,
					               A.INVEST_ID,
					               A.LAST_GURNT_BALANCE,
					               A.ADJUST_FLAG,
					               A.LAST_BALANCE,
					               A.SETTLE_DATE,
					               A.DOC_STATUS,
					               A.GURNT_INTEREST,
					               A.ADJUST_TYPE,
					               A.INTEREST,
					               A.SETTLEMENT_ID,
					               A.ADJUST_AMOUNT,
					               A.BALANCE,
					               A.BALANCE_BA,
					               A.ACCOUNT_CODE,
					               A.LAST_SETTLE_DATE,
					               A.INTEREST_RATE,
					               A.ADJUST_DATE,
					               A.GURNT_BALANCE,
					               A.END_INTEREST_RATE,
					               A.GURNT_INTEREST_RATE
					          FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A
					         WHERE 1 = 1  ]]>
		<include refid="queryFundSettlementByInvestIdCondition" />
		<![CDATA[  ORDER BY A.SETTLE_DATE DESC) B WHERE ROWNUM = 1 ]]>
	</select>	
	<select id="PA_findFundSettlementDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 	select * from dev_pas.t_fund_settlement t where t.invest_id=#{invest_id}
				 	and t.settle_date>=#{settle_date}
				 	order by t.settle_date ]]>
	</select>	
	
	<select id="PA_findAllFundSettlementOfthreeYear" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 	
		    SELECT A.SETTLEMENT_ID, A.INVEST_ID, A.ACCOUNT_CODE, A.SETTLE_DATE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.INTEREST, A.GURNT_INTEREST_RATE, A.GURNT_INTEREST,
			A.LAST_BALANCE, A.BALANCE_BA, A.BALANCE, A.LAST_GURNT_BALANCE, A.GURNT_BALANCE, A.BATCH_DATE, A.ADJUST_FLAG, A.ADJUST_TYPE, A.ADJUST_DATE, A.ADJUST_AMOUNT
            FROM DEV_PAS.T_FUND_SETTLEMENT A WHERE A.INVEST_ID=#{invest_id} AND A.SETTLE_DATE BETWEEN DATE'${settle_date}' AND SYSDATE
		 ]]>
	</select>
	<!-- 查询最新一条万能结算信息 -->
	<select id="PA_findSettlementOrderBySettleDateNew" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[ 
			
			select A.SETTLEMENT_ID, A.INVEST_ID, A.ACCOUNT_CODE, A.SETTLE_DATE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, 
A.INTEREST, A.GURNT_INTEREST_RATE, A.GURNT_INTEREST, A.LAST_BALANCE, A.BALANCE_BA, A.BALANCE, A.LAST_GURNT_BALANCE,
 A.GURNT_BALANCE, A.BATCH_DATE, A.ADJUST_FLAG, A.ADJUST_TYPE, A.ADJUST_DATE, A.ADJUST_AMOUNT, A.MONEY_DEVOTE, A.END_INTEREST, A.POLICY_CASH_VALUE 
from APP___PAS__DBUSER.T_FUND_SETTLEMENT A where A.invest_id=#{invest_id} ORDER BY a.SETTLE_DATE desc 
		 ]]>
	
	</select>
	
		<!-- 根据账户代码最早的结算数据 -->
	<select id="PA_findFirstIntoAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			
		     select sum(a.trans_amount) as trans_amount
		        from dev_pas.t_fund_trans a
		       where a.trans_type = 2
		         and a.list_id = #{invest_id}
		         and a.deal_time <= (select settle_date
		                               from (select b.settle_date
		                                       from dev_pas.t_fund_settlement b
		                                      where b.account_code = #{account_code}
		                                        and b.invest_id =#{invest_id}
		                                      order by SETTLE_DATE)
		                              where rownum = 1)
		                              
		 ]]>
	</select>
	<!-- 查询多账户价值 -->
	<select id="PA_findInvAccVal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.* from APP___PAS__DBUSER.t_contract_invest tci,
				APP___PAS__DBUSER.t_fund_settlement a 
				where tci.list_id=a.invest_id
				and tci.policy_id = #{policy_id}
		 ]]>
		 <include refid="fundSettlementWhereCondition" />
	</select>	
</mapper>
