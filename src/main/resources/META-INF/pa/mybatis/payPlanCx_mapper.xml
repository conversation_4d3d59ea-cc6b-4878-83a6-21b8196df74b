<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayPlanCxDao">
<!--
	<sql id="PA_payPlanCxWhereCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" change_seq  != null "><![CDATA[ AND A.CHANGE_SEQ = #{change_seq} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" oper_type != null and oper_type != ''  "><![CDATA[ AND A.OPER_TYPE = #{oper_type} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" pre_log_id  != null "><![CDATA[ AND A.PRE_LOG_ID = #{pre_log_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayPlanCxByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayPlanCxByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayPlanCxByOperTypeCondition">
		<if test=" oper_type != null and oper_type != '' "><![CDATA[ AND A.OPER_TYPE = #{oper_type} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPayPlanCx"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_PLAN_CX(
				PLAN_ID, INSERT_TIME, CHANGE_SEQ, UPDATE_TIME, LOG_ID, INSERT_TIMESTAMP, UPDATE_BY, 
				UPDATE_TIMESTAMP, POLICY_CHG_ID, LOG_TYPE, OPER_TYPE, INSERT_BY, POLICY_ID, PRE_LOG_ID ) 
			VALUES (
				#{plan_id, jdbcType=NUMERIC}, SYSDATE , #{change_seq, jdbcType=NUMERIC} , SYSDATE , #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{log_type, jdbcType=VARCHAR} , #{oper_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{pre_log_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePayPlanCx" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_PLAN_CX WHERE  POLICY_CHG_ID=#{policy_chg_id} AND PLAN_ID=#{plan_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePayPlanCx" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN_CX ]]>
		<set>
		<trim suffixOverrides=",">
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    CHANGE_SEQ = #{change_seq, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
			OPER_TYPE = #{oper_type, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PRE_LOG_ID = #{pre_log_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  1=2 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPayPlanCxByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.CHANGE_SEQ, A.LOG_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_PAY_PLAN_CX A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCxByPolicyChgIdCondition" />
	</select>
	
	<select id="PA_findPayPlanCxByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.CHANGE_SEQ, A.LOG_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_PAY_PLAN_CX A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCxByPlanIdCondition" />
	</select>
	
	<select id="PA_findPayPlanCxByOperType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.CHANGE_SEQ, A.LOG_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_PAY_PLAN_CX A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCxByOperTypeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayPlanCx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.CHANGE_SEQ, A.LOG_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_PAY_PLAN_CX A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayPlanCx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.CHANGE_SEQ, A.LOG_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_PAY_PLAN_CX A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_queryPayPlanCxByPolicyChgIdCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayPlanCxTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_PLAN_CX A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayPlanCxForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PLAN_ID, B.CHANGE_SEQ, B.LOG_ID, 
			B.POLICY_CHG_ID, B.LOG_TYPE, B.OPER_TYPE, B.POLICY_ID, B.PRE_LOG_ID FROM (
					SELECT ROWNUM RN, A.PLAN_ID, A.CHANGE_SEQ, A.LOG_ID, 
			A.POLICY_CHG_ID, A.LOG_TYPE, A.OPER_TYPE, A.POLICY_ID, A.PRE_LOG_ID FROM APP___PAS__DBUSER.T_PAY_PLAN_CX A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
