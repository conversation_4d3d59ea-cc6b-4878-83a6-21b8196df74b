<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IExtendGracePeriodRuleDao">

	<sql id="extendGracePeriodRuleWhereCondition">
		<if test=" valid_flag != null and valid_flag != ''  "><![CDATA[ AND A.VALID_FLAG = #{valid_flag} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" prem  != null "><![CDATA[ AND A.PREM = #{prem} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryExtendGracePeriodRuleByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addExtendGracePeriodRule"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT S_CS_EXTEND_GRACE_PERIOD .NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE(
				VALID_FLAG, INSERT_TIMESTAMP, CHANNEL_TYPE, UPDATE_BY, PREM, INSERT_TIME, BRANCH_CODE, 
				LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				#{valid_flag, jdbcType=VARCHAR}, CURRENT_TIMESTAMP, #{channel_type, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{prem, jdbcType=NUMERIC} , SYSDATE , #{branch_code, jdbcType=VARCHAR} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteExtendGracePeriodRule" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateExtendGracePeriodRule" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE ]]>
		<set>
		<trim suffixOverrides=",">
			VALID_FLAG = #{valid_flag, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PREM = #{prem, jdbcType=NUMERIC} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findExtendGracePeriodRuleByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.CHANNEL_TYPE, A.PREM, A.BRANCH_CODE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE A WHERE 1 = 1  ]]>
		<include refid="queryExtendGracePeriodRuleByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapExtendGracePeriodRule" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.CHANNEL_TYPE, A.PREM, A.BRANCH_CODE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE A WHERE ROWNUM <=  1000  ]]>
		<include refid="extendGracePeriodRuleWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	
	<!-- 按map查询操作 -->
	<select id="findExtendGracePeriodRule" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.CHANNEL_TYPE, A.PREM, A.BRANCH_CODE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE A WHERE ROWNUM <=  1000  ]]>
		<include refid="extendGracePeriodRuleWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllExtendGracePeriodRule" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.CHANNEL_TYPE, A.PREM, A.BRANCH_CODE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE A WHERE ROWNUM <=  1000  ]]>
		<include refid="extendGracePeriodRuleWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findExtendGracePeriodRuleTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE A WHERE 1 = 1  ]]>
		<include refid="extendGracePeriodRuleWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryExtendGracePeriodRuleForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.VALID_FLAG, B.CHANNEL_TYPE, B.PREM, B.BRANCH_CODE, 
			B.LIST_ID FROM (
					SELECT ROWNUM RN, A.VALID_FLAG, A.CHANNEL_TYPE, A.PREM, A.BRANCH_CODE, 
			A.LIST_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD_RULE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="extendGracePeriodRuleWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
