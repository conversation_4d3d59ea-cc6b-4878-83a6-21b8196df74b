<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAggregationRiskTypeDao">
<!--
	<sql id="aggregationRiskTypeWhereCondition">
		<if test=" risk_desc != null and risk_desc != ''  "><![CDATA[ AND A.RISK_DESC = #{risk_desc} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryAggregationRiskTypeByRiskTypeCondition">
		<if test=" risk_type != null and risk_type != '' "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addAggregationRiskType"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE(
				RISK_DESC, RISK_TYPE ) 
			VALUES (
				#{risk_desc, jdbcType=VARCHAR}, #{risk_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteAggregationRiskType" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE WHERE RISK_TYPE = #{risk_type} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateAggregationRiskType" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE ]]>
		<set>
		<trim suffixOverrides=",">
			RISK_DESC = #{risk_desc, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE RISK_TYPE = #{risk_type} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findAggregationRiskTypeByRiskType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_DESC, A.RISK_TYPE FROM APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE A WHERE 1 = 1  ]]>
		<include refid="queryAggregationRiskTypeByRiskTypeCondition" />
		<![CDATA[ ORDER BY A.RISK_TYPE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapAggregationRiskType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_DESC, A.RISK_TYPE FROM APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RISK_TYPE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllAggregationRiskTypeELPSYCONGROO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_DESC, A.RISK_TYPE FROM APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RISK_TYPE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findAggregationRiskTypeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryAggregationRiskTypeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RISK_DESC, B.RISK_TYPE FROM (
					SELECT ROWNUM RN, A.RISK_DESC, A.RISK_TYPE FROM APP___PAS__DBUSER.T_AGGREGATION_RISK_TYPE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RISK_TYPE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
