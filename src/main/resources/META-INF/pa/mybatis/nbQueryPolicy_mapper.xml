<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.impl.nbquerypolicy.dao.INbQueryPolicyDao">

	<sql id="PA_queryBycustomerId">
		<if test=" customer_id  != null "><![CDATA[ AND tcu.customer_id = #{customer_id} ]]></if>
	</sql>	
	
	<sql id="PA_queryBypolicyId">
		<if test=" policy_id  != null "><![CDATA[ AND t.policy_id = #{policy_id} ]]></if>
	</sql>
	
	<sql id="PA_queryContractProduct">
		<if test=" policy_id  != null "><![CDATA[ AND t.policy_id = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND t.busi_item_id = #{busi_item_id} ]]></if>
	</sql>
	
	
	<sql id="PA_queryBenefitInsured">
		<if test=" policy_id  != null "><![CDATA[ AND tbi.policy_id = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND tbi.busi_item_id = #{busi_item_id} ]]></if>
	</sql>
	
	<sql id="PA_queryContractBene">
		<if test=" policy_id  != null "><![CDATA[ AND t.policy_id = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND t.busi_item_id = #{busi_item_id} ]]></if>
	</sql>
	<!-- 查询客户作为投保人 -->
	<select id="queryHolderPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT t.policy_id,
       t.apply_code,
       t.policy_type,
       t.organ_code,
       t.apply_date,
	   t.issue_date,
	   t.end_cause,
       t.submit_channel,
       t.validate_date,
       t.liability_state,
       t.special_account_flag,
       tph.customer_id,
       tcu.customer_name,
       case
         when (SELECT sum(tcpd.additional_prem_af)
                 FROM dev_pas.t_contract_product tcpd
                where tcpd.policy_id = t.policy_id) > 0 then
          1
         else
          0
       end as noStandardHistory
     FROM dev_pas.t_contract_master t
     left join dev_pas.t_policy_holder tph
     on tph.policy_id = t.policy_id
     left join dev_pas.t_customer tcu
     on tcu.customer_id = tph.customer_id
     where exists (SELECT 1
          FROM dev_pas.t_policy_holder tph
         where t.policy_id = tph.policy_id
          )
		]]>
		<include refid="PA_queryBycustomerId" />
	</select>
	
	<!-- 查询客户作为被保人 -->
	<select id="queryInsuredPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT t.policy_id,
       t.apply_code,
       t.policy_type,
       t.organ_code,
       t.apply_date,
	   t.issue_date,
	   t.end_cause,
       t.validate_date,
       t.submit_channel,
       t.liability_state,
       t.special_account_flag,
       tph.customer_id,
       tcu.customer_name,
       case
         when (SELECT sum(tcpd.additional_prem_af)
                 FROM dev_pas.t_contract_product tcpd
                where tcpd.policy_id = t.policy_id) > 0 then
          1
         else
          0
       end as noStandardHistory
       FROM dev_pas.t_contract_master t
       left join dev_pas.t_insured_list tph
       on tph.policy_id = t.policy_id
       left join dev_pas.t_customer tcu
       on tcu.customer_id = tph.customer_id
       where exists (SELECT 1
          FROM dev_pas.t_insured_list til
         where t.policy_id = til.policy_id
           )
		]]>
		<include refid="PA_queryBycustomerId" />
	</select>
	
	<!-- 查询客户作为收益人 -->
	<select id="queryBenefitPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT T.POLICY_ID,
			       T.APPLY_CODE,
			       T.POLICY_TYPE,
			       T.ORGAN_CODE,
			       T.APPLY_DATE,
			       T.ISSUE_DATE,
			       T.END_CAUSE,
			       T.SUBMIT_CHANNEL,
			       T.VALIDATE_DATE,
			       T.LIABILITY_STATE,
			       T.SPECIAL_ACCOUNT_FLAG,
			       TCB.CUSTOMER_ID,
			       TCU.CUSTOMER_NAME,
			       CASE  WHEN (SELECT SUM(TCPD.ADDITIONAL_PREM_AF)
			                 FROM DEV_PAS.T_CONTRACT_PRODUCT TCPD
			                WHERE TCPD.POLICY_ID = T.POLICY_ID) > 0 THEN 1
			         ELSE 0 END AS NOSTANDARDHISTORY
			  FROM DEV_PAS.T_CONTRACT_MASTER T
			  LEFT JOIN DEV_PAS.T_CONTRACT_BENE TCB
			    ON TCB.POLICY_ID = T.POLICY_ID
			  LEFT JOIN DEV_PAS.T_CUSTOMER TCU
			    ON TCU.CUSTOMER_ID = TCB.CUSTOMER_ID
			 WHERE EXISTS (SELECT 1
			          FROM DEV_PAS.T_CONTRACT_BENE TCB
			         WHERE T.POLICY_ID = TCB.POLICY_ID) 
		]]>
		<include refid="PA_queryBycustomerId" />
	</select>
	
	
	<!-- 查询险种层信息 -->
<select id="queryBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[ 
		SELECT t.busi_item_id,
       t.busi_prd_id,
       t.busi_prod_code,
       t.apply_code,
       t.policy_id,
       t.policy_code,
       t.master_busi_item_id,
       t.validate_date,
       t.expiry_date,
       t.decision_code,
       t.liability_state,
       t.waiver,
       t.issue_date,
       t.joint_life_flag,
       t.renew,
       t.hesitation2acc,
       t.apply_date,
       t.waiver_start,
       t.waiver_end,
       t.renew_decision,
       t.renew_times,
       t.apl_permit,
       t.prd_pkg_code,
       t.lapse_cause,
       t.paidup_date,
       t.end_cause,
       t.gurnt_start_date,
       t.gurnt_perd_type,
       t.gurnt_period,
       t.maturity_date,
       t.lapse_date,
       t.rerinstate_date,
       t.suspend_cause,
       t.suspend_date,
       t.gurnt_renew_start,
       t.gurnt_renew_end,
       t.renewal_state,
       t.due_lapse_date,
       t.initial_prem_date,
       t.is_waived,
       t.assurerenew_flag,
       t.gurnt_rate,
       t.settle_method,
       t.initial_validate_date,
       t.flight_no,
       t.old_pol_no,
       t.surrender_flag,
       t.hesitation_period_day,
       t.is_renewal_switch,
       t.is_rpu,
       t.REINSURED,
       t.CAN_CHANGE_FLAG,
       t.CAN_REINSURE_FLAG,
       tbp.cover_period_type,
       tcpd.prem,
       tcpd.amount,
       (case
         when t.master_busi_item_id = null then
          null
         else
          (SELECT tp.busi_prod_code
             FROM dev_pas.t_contract_busi_prod tp
            where tp.busi_item_id = t.master_busi_item_id)
       end) as parentBusiProdCode,
       tbp.PRODUCT_CATEGORY3,
       t.FIRST_VALIDATE_DATE
  FROM dev_pas.t_contract_busi_prod t
  left join APP___PAS__DBUSER.T_BUSINESS_PRODUCT tbp
    on t.busi_prod_code = tbp.PRODUCT_CODE_ORIGINAL
  left join (SELECT t.busi_item_id,
                    sum(t.std_prem_af) as prem,
                    sum(t.amount) as amount
               FROM dev_pas.t_contract_product t
              group by t.busi_item_id) tcpd
    on t.busi_item_id = tcpd.busi_item_id
 where 1 = 1
		]]>
<include refid="PA_queryBypolicyId" />
</select>
	
	
	<!-- 查询责任组信息 -->
	<select id="queryContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT t.product_id,
       t.prem_freq,
       t.amount,
       t.std_prem_af,
       t.unit,
       t.validate_date,
       t.expiry_date,
       t.product_code,po.field1,
       t.busi_item_id,
       t.liability_state,
       t.TOTAL_PREM_AF,
       tbp.product_name,
       tbp.option_type
  FROM dev_pas.t_contract_product t
  left join APP___PAS__DBUSER.T_PRODUCT_LIFE tbp
    on t.product_id = tbp.product_id
   left join dev_pas.T_CONTRACT_PRODUCT_OTHER po 
   on t.policy_id = po.policy_id 
   and t.busi_item_id = po.busi_item_id 
   and t.item_id = po.item_id
 where 1=1
]]>
<include refid="PA_queryContractProduct" />

	</select>
	
	
	<!-- 查询险种被保人信息 -->
	<select id="queryBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT tcu.customer_name, tcu.customer_id, tcu.customer_certi_code
        FROM dev_pas.t_customer tcu
        where exists (SELECT 1
          FROM dev_pas.t_insured_list t
         where exists (SELECT tbi.insured_id
                  FROM dev_pas.t_benefit_insured tbi
                 where  t.list_id = tbi.insured_id
           and tcu.customer_id = t.customer_id
           and tbi.policy_id=#{policy_id}
           and tbi.busi_item_id=#{busi_item_id}))
]]>
<!-- <include refid="PA_queryBenefitInsured" /> -->
	</select>
	
	
	<!-- 查询受益人信息 -->
	<select id="queryContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT tcu.customer_name, tcu.customer_gender
        FROM dev_pas.t_customer tcu
        where exists (SELECT *
          FROM dev_pas.t_contract_bene t
         where 
         tcu.customer_id = t.customer_id
         and t.policy_id=#{policy_id}
           and t.busi_item_id=#{busi_item_id})
]]>

<!-- <include refid="PA_queryContractBene" /> -->
	</select>
		<!-- 查询被保人五要素和投被保人关系 -->
	<select id="queryInsuredListByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
                SELECT TIL.RELATION_TO_PH,
                 	   TC.CUSTOMER_ID,  
                       TC.CUSTOMER_NAME,
                       TC.CUSTOMER_BIRTHDAY,
                       TC.CUSTOMER_GENDER,
                       TC.CUSTOMER_CERT_TYPE,
                       TC.CUSTOMER_CERTI_CODE
                  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL,
                       APP___PAS__DBUSER.T_CUSTOMER     TC
                 WHERE TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                   AND TIL.POLICY_ID = #{policy_id}
]]>
	</select>
	
	<select id="PA_queryPolicyHolderByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select tc.customer_name,
                          th.customer_id
    from APP___PAS__DBUSER.T_Customer tc, APP___PAS__DBUSER.T_POLICY_HOLDER th
   where tc.customer_id = th.customer_id
     and th.policy_id = #{policy_id}  ]]>
	</select>
</mapper>