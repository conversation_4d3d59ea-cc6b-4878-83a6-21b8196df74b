<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractMasterLog">

	<sql id="PA_contractMasterLogWhereCondition">
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" subinput_type != null and subinput_type != ''  "><![CDATA[ AND A.SUBINPUT_TYPE = #{subinput_type} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" sale_com_code != null and sale_com_code != ''  "><![CDATA[ AND A.SALE_COM_CODE = #{sale_com_code} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" submit_channel  != null "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" sale_type != null and sale_type != ''  "><![CDATA[ AND A.SALE_TYPE = #{sale_type} ]]></if>
		<if test=" input_type != null and input_type != ''  "><![CDATA[ AND A.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" statistic_channel != null and statistic_channel != ''  "><![CDATA[ AND A.STATISTIC_CHANNEL = #{statistic_channel} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
		<if test=" relation_policy_code != null and relation_policy_code != ''  "><![CDATA[ AND A.RELATION_POLICY_CODE = #{relation_policy_code} ]]></if>
		<if test=" special_cus_flag  != null "><![CDATA[ AND A.special_cus_flag = #{special_cus_flag} ]]></if>
		<if test=" tax_extension_source  != null and tax_extension_source != '' "><![CDATA[ AND A.TAX_EXTENSION_SOURCE = #{tax_extension_source} ]]></if>
		<if test=" policy_prd_flag  != null and policy_prd_flag != '' "><![CDATA[ AND A.POLICY_PRD_FLAG = #{policy_prd_flag} ]]></if>
		<if test=" special_account_flag != null and special_account_flag != '' "><![CDATA[ AND A.special_account_flag = #{special_account_flag} ]]></if>
		<if test=" is_channel_self_insured != null and is_channel_self_insured != '' "><![CDATA[ AND A.is_channel_self_insured = #{is_channel_self_insured} ]]></if>
	    <if test=" is_channel_mutual_insured != null and is_channel_mutual_insured != '' "><![CDATA[ AND A.is_channel_mutual_insured = #{is_channel_mutual_insured} ]]></if>
		<if test=" notification_receive_method  != null "><![CDATA[ AND A.notification_receive_method = #{notification_receive_method} ]]></if>
		<if test=" tax_extension_sum_prem  != null "><![CDATA[ AND A.TAX_EXTENSION_SUM_PREM = #{tax_extension_sum_prem} ]]></if>
		<if test=" total_policy_sequence_no  != null "><![CDATA[ AND A.TOTAL_POLICY_SEQUENCE_NO = #{total_policy_sequence_no} ]]></if>
		<if test=" policy_sequence_no  != null "><![CDATA[ AND A.POLICY_SEQUENCE_NO = #{policy_sequence_no} ]]></if>
		<if test=" jointly_insured_type  != null "><![CDATA[ AND A.JOINTLY_INSURED_TYPE = #{jointly_insured_type} ]]></if>	
		<if test=" self_apply_flag  != null "><![CDATA[ AND A.SELF_APPLY_FLAG = #{self_apply_flag} ]]></if>	
		<if test=" per_fin_pvt_bank_code  != null "><![CDATA[ AND A.PER_FIN_PVT_BANK_CODE = #{per_fin_pvt_bank_code} ]]></if>
        <if test=" policy_lock_flag  != null "><![CDATA[ AND A.POLICY_LOCK_FLAG = #{policy_lock_flag} ]]></if>	
		<if test=" is_Dflt_Acknowledge_Date  != null "><![CDATA[ AND A.IS_DFLT_ACKNOWLEDGE_DATE = #{is_Dflt_Acknowledge_Date} ]]></if>	
		<if test=" merge_Signature_Flag != null "><![CDATA[ AND A.MERGE_SIGNATURE_FLAG = #{merge_signature_flag} ]]></if>			
					
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractMasterLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractMasterLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			select APP___PAS__DBUSER.S_CONTRACT_MASTER_LOG__LOG_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG(
				MERGE_SIGNATURE_FLAG,POLICY_RELATION_TYPE,POLICY_PWD, MEDIA_TYPE, INTEREST_MODE, APPLY_CODE, ORGAN_CODE, CHANNEL_TYPE, SALE_AGENT_NAME, 
				INSURED_FAMILY, UPDATE_BY, POLICY_ID, DERIVATION, UPDATE_TIME, SUBINPUT_TYPE, 
				BASIC_REMARK, INPUT_DATE, SALE_COM_CODE, POLICY_TYPE, EXPIRY_DATE, PWD_INVALID_FLAG, SUBMIT_CHANNEL, 
				LIABILITY_STATE, POLICY_CODE, SALE_AGENT_CODE, RERINSTATE_DATE, BRANCH_CODE, VALIDATE_DATE, UPDATE_TIMESTAMP, 
				INSERT_BY, AGENCY_CODE, MONEY_CODE, SERVICE_HANDLER_CODE, APPLY_DATE, INITIAL_VALIDATE_DATE, 
				INSERT_TIMESTAMP, SUBMISSION_DATE, SERVICE_HANDLER, E_SERVICE_FLAG, POLICY_CHG_ID, SERVICE_BANK_BRANCH, LAPSE_DATE, 
				DC_INDI, SALE_TYPE, INPUT_TYPE, INSERT_TIME, END_CAUSE, ISSUE_DATE, STATISTIC_CHANNEL, 
				LAPSE_CAUSE, DECISION_CODE, AGENT_ORG_ID, LOG_ID, SERVICE_BANK, SUSPEND_DATE, SUSPEND_CAUSE, 
				INITIAL_PREM_DATE, LANG_CODE, LOG_TYPE, FORMER_ID ,SPECIAL_CUS_FLAG, RELATION_POLICY_CODE ,
				TAX_EXTENSION_SOURCE ,POLICY_PRD_FLAG ,GROUP_SALE_TYPE,APPLY_TIME,IS_ALONE_INSURE,MEDICAL_INSURANCE_CARD,CALL_TIME_LIST,
				POLICY_REINSURE_FLAG,REINSURED_TIMES,BANK_MANAGER_NAME,BANK_MANAGER_LICENSENO,TRUST_BUSI_FLAG,MULTI_MAINRISK_FLAG,
				MEET_POV_STANDARD_FLAG,SPECIAL_ACCOUNT_FLAG,IS_CHANNEL_SELF_INSURED,IS_CHANNEL_MUTUAL_INSURED,NOTIFICATION_RECEIVE_METHOD,TAX_EXTENSION_SUM_PREM,TOTAL_POLICY_SEQUENCE_NO,POLICY_SEQUENCE_NO,JOINTLY_INSURED_TYPE,SELF_APPLY_FLAG,PER_FIN_PVT_BANK_CODE,POLICY_LOCK_FLAG,IS_DFLT_ACKNOWLEDGE_DATE)
			VALUES (
				 #{merge_signature_flag,jdbcType=NUMERIC},#{policy_relation_type, jdbcType=NUMERIC},#{policy_pwd, jdbcType=VARCHAR}, #{media_type, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{channel_type, jdbcType=VARCHAR} , #{sale_agent_name, jdbcType=VARCHAR} 
				, #{insured_family, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{derivation, jdbcType=VARCHAR} , SYSDATE , #{subinput_type, jdbcType=VARCHAR} 
				, #{basic_remark, jdbcType=VARCHAR} , #{input_date, jdbcType=DATE} , #{sale_com_code, jdbcType=VARCHAR} , #{policy_type, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} , #{pwd_invalid_flag, jdbcType=NUMERIC} , #{submit_channel, jdbcType=NUMERIC} 
				, #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{sale_agent_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{branch_code, jdbcType=VARCHAR} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{agency_code, jdbcType=VARCHAR} , #{money_code, jdbcType=VARCHAR} , #{service_handler_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{initial_validate_date, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{submission_date, jdbcType=DATE} , #{service_handler, jdbcType=VARCHAR} , #{e_service_flag, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{service_bank_branch, jdbcType=VARCHAR} , #{lapse_date, jdbcType=DATE} 
				, #{dc_indi, jdbcType=NUMERIC} , #{sale_type, jdbcType=VARCHAR} , #{input_type, jdbcType=VARCHAR} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{statistic_channel, jdbcType=VARCHAR} 
				, #{lapse_cause, jdbcType=VARCHAR} , #{decision_code, jdbcType=VARCHAR} , #{agent_org_id, jdbcType=VARCHAR} , #{log_id, jdbcType=NUMERIC} , #{service_bank, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} 
				, #{initial_prem_date, jdbcType=DATE} , #{lang_code, jdbcType=VARCHAR} , #{log_type, jdbcType=VARCHAR} , #{former_id, jdbcType=NUMERIC} , #{special_cus_flag, jdbcType=NUMERIC} , #{relation_policy_code, jdbcType=VARCHAR} 
				, #{tax_extension_source, jdbcType=NUMERIC} , #{policy_prd_flag, jdbcType=NUMERIC}, #{group_sale_type, jdbcType=VARCHAR},#{apply_time, jdbcType=VARCHAR}, #{is_alone_insure, jdbcType=NUMERIC}, #{medical_insurance_card, jdbcType=NUMERIC}, #{call_time_list, jdbcType=VARCHAR}
				,#{policy_reinsure_flag, jdbcType=VARCHAR},#{reinsured_times, jdbcType=NUMERIC},#{bank_manager_name, jdbcType=VARCHAR},#{bank_manager_licenseno, jdbcType=VARCHAR}, #{trust_busi_flag,jdbcType=NUMERIC},#{multi_mainrisk_flag,jdbcType=NUMERIC}
				,#{meet_pov_standard_flag,jdbcType=NUMERIC}, #{special_account_flag, jdbcType=VARCHAR},#{is_channel_self_insured,jdbcType=NUMERIC}, #{is_channel_mutual_insured,jdbcType=NUMERIC}, #{notification_receive_method,jdbcType=NUMERIC}, #{tax_extension_sum_prem,jdbcType=NUMERIC},#{total_policy_sequence_no, jdbcType=VARCHAR},#{policy_sequence_no, jdbcType=VARCHAR}
				,#{jointly_insured_type, jdbcType=VARCHAR}, #{self_apply_flag,jdbcType=NUMERIC},#{per_fin_pvt_bank_code, jdbcType=VARCHAR},#{policy_lock_flag,jdbcType=NUMERIC},#{is_Dflt_Acknowledge_Date,jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractMasterLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG WHERE LOG_ID = #{log_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractMasterLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			MERGE_SIGNATURE_FLAG = #{merge_signature_flag,jdbcType=NUMERIC},
			POLICY_RELATION_TYPE = #{policy_relation_type, jdbcType=NUMERIC},
			POLICY_PWD = #{policy_pwd, jdbcType=VARCHAR} ,
		    MEDIA_TYPE = #{media_type, jdbcType=NUMERIC} ,
		    INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
			SALE_AGENT_NAME = #{sale_agent_name, jdbcType=VARCHAR} ,
		    INSURED_FAMILY = #{insured_family, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			DERIVATION = #{derivation, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			SUBINPUT_TYPE = #{subinput_type, jdbcType=VARCHAR} ,
			BASIC_REMARK = #{basic_remark, jdbcType=VARCHAR} ,
		    INPUT_DATE = #{input_date, jdbcType=DATE} ,
			SALE_COM_CODE = #{sale_com_code, jdbcType=VARCHAR} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    PWD_INVALID_FLAG = #{pwd_invalid_flag, jdbcType=NUMERIC} ,
		    SUBMIT_CHANNEL = #{submit_channel, jdbcType=NUMERIC} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			SALE_AGENT_CODE = #{sale_agent_code, jdbcType=VARCHAR} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENCY_CODE = #{agency_code, jdbcType=VARCHAR} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
			SERVICE_HANDLER_CODE = #{service_handler_code, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INITIAL_VALIDATE_DATE = #{initial_validate_date, jdbcType=DATE} ,
		    SUBMISSION_DATE = #{submission_date, jdbcType=DATE} ,
			SERVICE_HANDLER = #{service_handler, jdbcType=VARCHAR} ,
		    E_SERVICE_FLAG = #{e_service_flag, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			SERVICE_BANK_BRANCH = #{service_bank_branch, jdbcType=VARCHAR} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    DC_INDI = #{dc_indi, jdbcType=NUMERIC} ,
			SALE_TYPE = #{sale_type, jdbcType=VARCHAR} ,
			INPUT_TYPE = #{input_type, jdbcType=VARCHAR} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			STATISTIC_CHANNEL = #{statistic_channel, jdbcType=VARCHAR} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
			AGENT_ORG_ID = #{agent_org_id, jdbcType=VARCHAR} ,
			SERVICE_BANK = #{service_bank, jdbcType=VARCHAR} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE} ,
			LANG_CODE = #{lang_code, jdbcType=VARCHAR} ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    FORMER_ID = #{former_id, jdbcType=NUMERIC} ,
		    SPECIAL_CUS_FLAG = #{special_cus_flag, jdbcType=NUMERIC} ,
			RELATION_POLICY_CODE = #{relation_policy_code, jdbcType=VARCHAR} ,
			TAX_EXTENSION_SOURCE = #{tax_extension_source, jdbcType=NUMERIC} ,
			POLICY_PRD_FLAG = #{policy_prd_flag, jdbcType=NUMERIC} ,
			
			POLICY_REINSURE_FLAG = #{policy_reinsure_flag, jdbcType=VARCHAR} ,
		    REINSURED_TIMES = #{reinsured_times, jdbcType=NUMERIC} ,
		    TRUST_BUSI_FLAG = #{trust_busi_flag,jdbcType=NUMERIC},  
		    MULTI_MAINRISK_FLAG = #{multi_mainrisk_flag,jdbcType=NUMERIC},
		    MEET_POV_STANDARD_FLAG = #{meet_pov_standard_flag,jdbcType=NUMERIC},
		    <if test=" special_account_flag != null and special_account_flag != '' ">
		    <![CDATA[ SPECIAL_ACCOUNT_FLAG = #{special_account_flag, jdbcType=NUMERIC}, ]]></if>
		     <if test=" is_channel_self_insured != null and is_channel_self_insured != '' ">
		    <![CDATA[  IS_CHANNEL_SELF_INSURED = #{is_channel_self_insured,jdbcType=NUMERIC}, ]]>
		    </if>
		    <if test=" is_channel_mutual_insured != null and is_channel_mutual_insured != '' ">
		    <![CDATA[  IS_CHANNEL_MUTUAL_INSURED = #{is_channel_mutual_insured,jdbcType=NUMERIC}, ]]>
		    </if>
		    NOTIFICATION_RECEIVE_METHOD = #{notification_receive_method,jdbcType=NUMERIC},
		    TAX_EXTENSION_SUM_PREM = #{tax_extension_sum_prem,jdbcType=NUMERIC},
		    TOTAL_POLICY_SEQUENCE_NO = #{total_policy_sequence_no, jdbcType=VARCHAR} ,
		    POLICY_SEQUENCE_NO = #{policy_sequence_no, jdbcType=VARCHAR} ,
		    JOINTLY_INSURED_TYPE = #{jointly_insured_type, jdbcType=VARCHAR}, 
		    PER_FIN_PVT_BANK_CODE = #{per_fin_pvt_bank_code, jdbcType=VARCHAR},
		    <if test=" self_apply_flag != null and self_apply_flag != '' ">
		    <![CDATA[  SELF_APPLY_FLAG = #{self_apply_flag,jdbcType=NUMERIC}, ]]>
		    </if>
		    POLICY_LOCK_FLAG = #{policy_lock_flag,jdbcType=NUMERIC},
		    IS_DFLT_ACKNOWLEDGE_DATE = #{is_Dflt_Acknowledge_Date,jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id}  ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractMasterLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD, A.SPECIAL_CUS_FLAG, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.INPUT_DATE, A.SALE_COM_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, 
			A.DC_INDI, A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.LOG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.INITIAL_PREM_DATE, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID, A.RELATION_POLICY_CODE,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG ,A.SPECIAL_ACCOUNT_FLAG,
			A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterLogByLogIdCondition" />
	</select>
	
	<select id="PA_findContractMasterLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD, A.SPECIAL_CUS_FLAG,  A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.INPUT_DATE, A.SALE_COM_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, 
			A.DC_INDI, A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.LOG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.INITIAL_PREM_DATE, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID, A.RELATION_POLICY_CODE,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG  , A.TRUST_BUSI_FLAG ,A.SPECIAL_ACCOUNT_FLAG,
			A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterLogByPolicyIdCondition" />
	</select>
	
	<select id="PA_findContractMasterLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD, A.SPECIAL_CUS_FLAG,  A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.INPUT_DATE, A.SALE_COM_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, 
			A.DC_INDI, A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.LOG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.INITIAL_PREM_DATE, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID, A.RELATION_POLICY_CODE,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG , A.TRUST_BUSI_FLAG ,
			A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterLogByPolicyCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractMasterLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD,  A.SPECIAL_CUS_FLAG, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.INPUT_DATE, A.SALE_COM_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, 
			A.DC_INDI, A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.LOG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.INITIAL_PREM_DATE, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID, A.RELATION_POLICY_CODE,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG , A.TRUST_BUSI_FLAG  ,A.SPECIAL_ACCOUNT_FLAG,
			A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractMasterLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD, A.SPECIAL_CUS_FLAG,  A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.INPUT_DATE, A.SALE_COM_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, 
			A.DC_INDI, A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.LOG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.INITIAL_PREM_DATE, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID, A.RELATION_POLICY_CODE,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG , A.TRUST_BUSI_FLAG  ,A.SPECIAL_ACCOUNT_FLAG,
			A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_contractMasterLogWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractMasterLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractMasterLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.MERGE_SIGNATURE_FLAG,B.POLICY_RELATION_TYPE, B.SPECIAL_CUS_FLAG,  B.POLICY_PWD, B.MEDIA_TYPE, B.INTEREST_MODE, B.APPLY_CODE, B.ORGAN_CODE, B.CHANNEL_TYPE, B.SALE_AGENT_NAME, 
			B.INSURED_FAMILY, B.POLICY_ID, B.DERIVATION, B.SUBINPUT_TYPE, 
			B.BASIC_REMARK, B.INPUT_DATE, B.SALE_COM_CODE, B.POLICY_TYPE, B.EXPIRY_DATE, B.PWD_INVALID_FLAG, B.SUBMIT_CHANNEL, 
			B.LIABILITY_STATE, B.POLICY_CODE, B.SALE_AGENT_CODE, B.RERINSTATE_DATE, B.BRANCH_CODE, B.VALIDATE_DATE, 
			B.AGENCY_CODE, B.MONEY_CODE, B.SERVICE_HANDLER_CODE, B.APPLY_DATE, B.INITIAL_VALIDATE_DATE, 
			B.SUBMISSION_DATE, B.SERVICE_HANDLER, B.E_SERVICE_FLAG, B.POLICY_CHG_ID, B.SERVICE_BANK_BRANCH, B.LAPSE_DATE, 
			B.DC_INDI, B.SALE_TYPE, B.INPUT_TYPE, B.END_CAUSE, B.ISSUE_DATE, B.STATISTIC_CHANNEL, 
			B.LAPSE_CAUSE, B.DECISION_CODE, B.AGENT_ORG_ID, B.LOG_ID, B.SERVICE_BANK, B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.MULTI_MAINRISK_FLAG,
			B.INITIAL_PREM_DATE, B.LANG_CODE, B.LOG_TYPE, B.FORMER_ID, B.RELATION_POLICY_CODE,B.IS_CHANNEL_SELF_INSURED,B.IS_CHANNEL_MUTUAL_INSURED ,
			B.NOTIFICATION_RECEIVE_METHOD,B.TAX_EXTENSION_SUM_PREM ,B.TOTAL_POLICY_SEQUENCE_NO,B.POLICY_SEQUENCE_NO ,B.JOINTLY_INSURED_TYPE,B.SELF_APPLY_FLAG,B.PER_FIN_PVT_BANK_CODE,B.POLICY_LOCK_FLAG,B.IS_DFLT_ACKNOWLEDGE_DATE FROM (
					SELECT ROWNUM RN, A.MERGE_SIGNATURE_FLAG,A.POLICY_RELATION_TYPE,A.POLICY_PWD,  A.SPECIAL_CUS_FLAG, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.INPUT_DATE, A.SALE_COM_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, 
			A.DC_INDI, A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.STATISTIC_CHANNEL, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.LOG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.MULTI_MAINRISK_FLAG, A.MEET_POV_STANDARD_FLAG,
			A.INITIAL_PREM_DATE, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID, A.RELATION_POLICY_CODE,A.TAX_EXTENSION_SOURCE,A.POLICY_PRD_FLAG , A.TRUST_BUSI_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,
			A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.TOTAL_POLICY_SEQUENCE_NO,A.POLICY_SEQUENCE_NO ,A.JOINTLY_INSURED_TYPE,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG,A.IS_DFLT_ACKNOWLEDGE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<sql id="PA_findLiabilityStateChangeCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
	</sql>	
	<select id="PA_findLiabilityStateChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT MAX(A.INSERT_TIME) INSERT_TIME
			         FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG A
			              WHERE 1 = 1]]>
		<include refid="PA_findLiabilityStateChangeCondition" />
	</select>
	
	<!-- 查询log表对应万能险数据 -->
	<select id="PA_findMinContractMasterLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT T.POLICY_CODE, T.RELATION_POLICY_CODE, T.DOUBLE_MAINRISK_FLAG 
		             FROM APP___PAS__DBUSER.T_CONTRACT_MASTER_LOG T, APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
		            WHERE 1 = 1 
		              AND T.DOUBLE_MAINRISK_FLAG = 1
		              AND T.POLICY_CODE = TCM.POLICY_CODE 
		              AND T.POLICY_CODE = #{policy_code} 
		            ORDER BY T.LOG_ID   ]]>
	</select>
</mapper>
