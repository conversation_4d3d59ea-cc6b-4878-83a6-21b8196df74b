<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="policyCondition">

	<sql id="PA_policyConditionWhereCondition">
		<if test=" condition_desc != null and condition_desc != ''  "><![CDATA[ AND A.CONDITION_DESC = #{condition_desc} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" insured_list_id  != null "><![CDATA[ AND A.INSURED_LIST_ID = #{insured_list_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" uw_condition_type  != null "><![CDATA[ AND A.UW_CONDITION_TYPE = #{uw_condition_type} ]]></if>
		<if test=" condition_id  != null "><![CDATA[ AND A.CONDITION_ID = #{condition_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" insured_customer_id  != null "><![CDATA[ AND A.INSURED_CUSTOMER_ID = #{insured_customer_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyConditionByConditionIdCondition">
		<if test=" condition_id  != null "><![CDATA[ AND A.CONDITION_ID = #{condition_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyConditionByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyConditionByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyCondition"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="condition_id">
			SELECT APP___PAS__DBUSER.S_POLICY_CONDITION__CONDITION.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_CONDITION(
				 CONDITION_DESC, INSERT_TIME,  PRODUCT_CODE,  UPDATE_TIME, 
				ITEM_ID, BUSI_PROD_CODE, APPLY_CODE, INSERT_TIMESTAMP, INSURED_LIST_ID, POLICY_CODE, UPDATE_BY, 
				UPDATE_TIMESTAMP, UW_CONDITION_TYPE, CONDITION_ID, INSERT_BY, BUSI_ITEM_ID, INSURED_CUSTOMER_ID, POLICY_ID ) 
			VALUES (
				 #{condition_desc, jdbcType=VARCHAR} , SYSDATE ,  #{product_code, jdbcType=VARCHAR}  , SYSDATE 
				, #{item_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insured_list_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{uw_condition_type, jdbcType=NUMERIC} , #{condition_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{insured_customer_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyCondition" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_CONDITION WHERE CONDITION_ID = #{condition_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyCondition" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CONDITION ]]>
		<set>
		<trim suffixOverrides=",">
			CONDITION_DESC = #{condition_desc, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    INSURED_LIST_ID = #{insured_list_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    UW_CONDITION_TYPE = #{uw_condition_type, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    INSURED_CUSTOMER_ID = #{insured_customer_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyConditionByConditionId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CONDITION_DESC,  A.PRODUCT_CODE,
			A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.INSURED_LIST_ID, A.POLICY_CODE, 
			A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.INSURED_CUSTOMER_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyConditionByConditionIdCondition" />
	</select>
	
	<select id="PA_findPolicyConditionByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CONDITION_DESC, A.PRODUCT_CODE, 
			A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.INSURED_LIST_ID, A.POLICY_CODE, 
			A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.INSURED_CUSTOMER_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyConditionByPolicyIdCondition" />
	</select>
	
	<select id="PA_findPolicyConditionByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CONDITION_DESC,  A.PRODUCT_CODE,  
			A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.INSURED_LIST_ID, A.POLICY_CODE, 
			A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.INSURED_CUSTOMER_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyConditionByPolicyCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CONDITION_DESC,  A.PRODUCT_CODE, 
			A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.INSURED_LIST_ID, A.POLICY_CODE, 
			A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.INSURED_CUSTOMER_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION A WHERE 1 = 1   ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CONDITION_DESC,  A.PRODUCT_CODE, 
			A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.INSURED_LIST_ID, A.POLICY_CODE, 
			A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.INSURED_CUSTOMER_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION A WHERE 1 = 1  ]]>
		<include refid="PA_policyConditionWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyConditionTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_CONDITION A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyConditionForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,  B.CONDITION_DESC,  B.PRODUCT_CODE, 
			B.ITEM_ID, B.BUSI_PROD_CODE, B.APPLY_CODE, B.INSURED_LIST_ID, B.POLICY_CODE, 
			B.UW_CONDITION_TYPE, B.CONDITION_ID, B.BUSI_ITEM_ID, B.INSURED_CUSTOMER_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.CONDITION_DESC,  A.PRODUCT_CODE,
			A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.INSURED_LIST_ID, A.POLICY_CODE, 
			A.UW_CONDITION_TYPE, A.CONDITION_ID, A.BUSI_ITEM_ID, A.INSURED_CUSTOMER_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_CONDITION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CONDITION_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
