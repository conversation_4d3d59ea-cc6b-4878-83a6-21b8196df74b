<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBenefitInsuredDao">

	<sql id="PA_benefitInsuredWhereCondition">
		<if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND A.RELATION_TO_INSURED_1 = #{relation_to_insured_1} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" job_underwrite != null and job_underwrite != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{job_underwrite} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_ID in (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryBenefitInsuredByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryBenefitInsuredByOrderIdCondition">
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
	</sql>	
	<sql id="PA_queryBenefitInsuredByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

	<sql id="PA_queryBenefitInsuredByBusiItemIdCondition"><!-- zhouyao 2014/12/08 -->
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PA_queryBenefitInsuredByInsuredIdCondition">
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
	</sql>
<!-- 添加操作 -->
	<insert id="PA_addBenefitInsured"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_BENEFIT_INSURED__LIST_ID.NEXTVAL from dual
		</selectKey>
		<![CDATA[INSERT INTO APP___PAS__DBUSER.T_BENEFIT_INSURED(
				RELATION_TO_INSURED_1, INSERT_TIME, PRODUCT_CODE, UPDATE_TIME, INSURED_ID, INSERT_TIMESTAMP, 
				ORDER_ID, POLICY_CODE, UPDATE_BY, JOB_UNDERWRITE, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, 
				INSERT_BY, POLICY_ID ) 
			VALUES (
				#{relation_to_insured_1, jdbcType=VARCHAR}, SYSDATE , #{product_code, jdbcType=VARCHAR} , SYSDATE , #{insured_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{order_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{job_underwrite, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteBenefitInsured" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BENEFIT_INSURED WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateBenefitInsured" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BENEFIT_INSURED ]]>
		<set>
		<trim suffixOverrides=",">
			RELATION_TO_INSURED_1 = #{relation_to_insured_1, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
		    ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findBenefitInsuredByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE 1 = 1]]>
		<include refid="PA_queryBenefitInsuredByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findBenefitInsuredByOrderId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,
			A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE 1 = 1   ]]>
		<include refid="PA_queryBenefitInsuredByOrderIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findBenefitInsuredByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,
			A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE 1 = 1 ]]>
		<include refid="PA_queryBenefitInsuredByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,
			A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE  ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,
			A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE  ROWNUM <=  1000  ]]>
		<include refid="PA_benefitInsuredWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findBenefitInsuredTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryBenefitInsuredForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATION_TO_INSURED_1, B.PRODUCT_CODE, B.INSURED_ID,
			B.ORDER_ID, B.POLICY_CODE, B.JOB_UNDERWRITE, B.LIST_ID, B.BUSI_ITEM_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,
			A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>	
	<!--  -->
	<select id="PA_findBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID,
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_benefitInsuredWhereCondition" />
<!-- 		<include refid="PA_queryBenefitInsuredByBusiItemIdCondition" /> -->
<!-- 		<include refid="PA_queryBenefitInsuredByOrderIdCondition" /> -->
	</select>
	<select id="PA_findAllOrderId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT(A.ORDER_ID) FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE 1 = 1 ]]>
		<include refid="PA_queryBenefitInsuredByPolicyIdCondition" />
		<include refid="PA_queryBenefitInsuredByInsuredIdCondition" />
		<![CDATA[ ORDER BY A.ORDER_ID ]]> 
	</select>
	<select id="PT_findIsCaladleLR" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select  ac.service_code
					 from APP___PAS__DBUSER.T_CS_APPLICATION   a,
					      APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ac
					where a.change_id = ac.change_id
					  and exists (select 1
					         from APP___PAS__DBUSER.T_CS_POLICY_CHANGE t
					        where t.change_id = a.change_id
					          and t.policy_code = #{policy_code})
					  and ac.accept_status = '18'
					order by ac.change_id asc
		 ]]>
	</select>
		<select id="PA_find609BenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select tb.insured_id
          from dev_pas.t_benefit_insured tb
         where tb.policy_code = #{policy_code} and
         tb.order_id = '1'
           and tb.busi_item_id =
               (SELECT p.busi_item_id
                  FROM dev_pas.t_contract_busi_prod p
                 WHERE p.busi_prod_code = '00609000'
                   and p.policy_code = #{policy_code}) 
		]]>
	</select>
	
	<select id="PA_findInsuredAgeByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select til.insured_age,tbi.list_id,tbi.insured_id
				  from APP___PAS__DBUSER.t_benefit_insured tbi
				  left join APP___PAS__DBUSER.t_insured_list til
				    on tbi.insured_id = til.list_id
				 where tbi.busi_item_id = #{busi_item_id}
		]]>
	</select>
	<!-- 个单被保人信息接口 -->
	<select id="PA_queryContInsuredInfoForRI" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.POLICY_CODE,A.CUSTOMER_ID,B.RELATION_TO_INSURED_1,A.RELATION_TO_PH,B.ORDER_ID,
			(SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER WHERE POLICY_ID=A.POLICY_ID) AS appnt_no,
			(SELECT ORGAN_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER WHERE POLICY_ID=A.POLICY_ID) AS manage_com,
			C.CUSTOMER_NAME,C.CUSTOMER_GENDER,C.CUSTOMER_BIRTHDAY,A.UPDATE_BY,
			(SELECT USER_NAME FROM APP___PAS__DBUSER.T_UDMP_USER WHERE USER_ID=A.UPDATE_BY) AS operator,
			A.INSERT_TIME,A.UPDATE_TIME,C.CUSTOMER_CERT_TYPE,C.CUSTOMER_CERTI_CODE
			FROM APP___PAS__DBUSER.T_INSURED_LIST A,APP___PAS__DBUSER.T_BENEFIT_INSURED B,APP___PAS__DBUSER.T_CUSTOMER C
			WHERE A.LIST_ID=B.INSURED_ID AND A.CUSTOMER_ID=C.CUSTOMER_ID
			AND B.POLICY_ID=#{policy_id} AND B.BUSI_ITEM_ID=#{busi_item_id}
		]]>
	</select>
	<!-- 社保状态变更查询接口 -->
	<select id="PA_queryYDBQContInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select bp.product_code_sys,bp.product_abbr_name, cp.policy_code,cbp.old_pol_no,cbp.busi_item_id,bp.PRODUCT_NAME_SYS,case when cp.count_way = 5 then cp.unit else cp.amount end amountunit,cp.INITIAL_DISCNT_PREM_AF,
            (select SUM(EXTRA_PREM) from APP___PAS__DBUSER.T_EXTRA_PREM WHERE EXTRA_TYPE=2 and policy_id=cp.policy_id) as addprem
            from APP___PAS__DBUSER.t_contract_busi_prod cbp,APP___PAS__DBUSER.t_business_product bp,APP___PAS__DBUSER.t_contract_product cp
			where cp.policy_code=cbp.policy_code and cbp.busi_prod_code=bp.product_code_sys and cbp.busi_item_id = cp.busi_item_id
			and cp.policy_id=#{policy_id}
			and cbp.liability_state = '1'
		]]>
	</select>
	<!-- 查询第一被保险人 -->
	<select id="queryFirstBenefitInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[
  select c.customer_id,c.customer_certi_code, c.customer_id_code, c.customer_name
  from APP___PAS__DBUSER.t_benefit_insured tb
  left join APP___PAS__DBUSER.t_insured_list ti
    on tb.insured_id = ti.list_id
  left join APP___PAS__DBUSER.t_Customer c
    on ti.customer_id = c.customer_id
 where 1 = 1
]]>
   <if test=" policy_id  != null "><![CDATA[ AND tb.policy_id = #{policy_id} ]]></if>
   <if test=" order_id  != null "><![CDATA[ AND tb.order_id =#{order_id} ]]></if>
   <if test=" busi_item_id  != null "><![CDATA[ AND tb.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
   <if test=" live_status  != null "><![CDATA[ and c.LIVE_STATUS = #{live_status}]]></if>
  <![CDATA[ and rownum = 1]]>
	</select>
	
	<!--150568 查询参与退保的被保人 -->
	<select id="findCTBusiInsureds" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select distinct b.insured_id
				  from dev_pas.t_cs_Contract_Busi_Prod p
				  join dev_pas.T_BENEFIT_INSURED b
				    on b.policy_id = p.policy_id
				   and b.busi_item_id = p.busi_item_id
				 where ROWNUM <=  1000
				   and p.old_new = 1
				   and p.operation_type = 2  
				   AND p.policy_chg_id = #{policy_chg_id} ]]>
		<if test=" change_id  != null "><![CDATA[ AND p.change_id = #{change_id} ]]></if>
	</select>
	
	<select id="findInsuredByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
     		SELECT DISTINCT TI.CUSTOMER_ID,B.CUSTOMER_NAME
      FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A, 
           APP___PAS__DBUSER.T_INSURED_LIST TI,
           APP___PAS__DBUSER.T_CUSTOMER B 
      WHERE
       	 	TI.POLICY_CODE = #{policy_code}
        	AND A.INSURED_ID = TI.LIST_ID 
        	AND A.POLICY_ID=TI.POLICY_ID
        	AND TI.CUSTOMER_ID=B.CUSTOMER_ID 
		]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findBenefitInsuredInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select 
		       c.old_customer_id, a.order_id, a.busi_item_id, c.customer_id , c.customer_name, a.product_code
		from 
		       dev_pas.t_benefit_insured a, 
		       dev_pas.t_insured_list b, 
		       dev_pas.t_customer c
		where 
		       a.insured_id = b.list_id and
		       b.customer_id = c.customer_id ]]>
		<include refid="PA_benefitInsuredWhereCondition" />
	</select>
	
	<select id="findInsuredByPolicyBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
     		SELECT DISTINCT A.ORDER_ID,B.CUSTOMER_NAME,TI.INPUT_SEQUENCE
      FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A, 
           APP___PAS__DBUSER.T_INSURED_LIST TI,
           APP___PAS__DBUSER.T_CUSTOMER B 
      WHERE
        	A.INSURED_ID = TI.LIST_ID 
        	AND A.POLICY_ID=TI.POLICY_ID
        	AND TI.CUSTOMER_ID=B.CUSTOMER_ID 
        	AND A.POLICY_CODE = #{policy_code}
        	AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	
	<!-- 查询险种下是否存在生存被保人 -->
	<select id="findAllLiveBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
						SELECT A.BUSI_ITEM_ID , A.POLICY_CODE , A.LIST_ID
						  FROM DEV_PAS.T_BENEFIT_INSURED A,
						       DEV_PAS.T_INSURED_LIST    B,
						       DEV_PAS.T_CUSTOMER        C
						 WHERE A.INSURED_ID = B.LIST_ID
						   AND B.CUSTOMER_ID = C.CUSTOMER_ID
						   AND (C.LIVE_STATUS <> '2' OR C.LIVE_STATUS IS NULL)
						   AND A.BUSI_ITEM_ID =  #{busi_item_id}
		]]>
	</select>
	
		<!-- 查询险种被保人列表 -->
	<select id="findBusiInsuredInfoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
                         SELECT TBI.BUSI_ITEM_ID, TIL.CUSTOMER_ID, TBI.ORDER_ID
                           FROM APP___PAS__DBUSER.T_BENEFIT_INSURED TBI
                                LEFT JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL
                                ON TBI.INSURED_ID = TIL.LIST_ID
                           WHERE TBI.POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="PA_findBenefitInsuredTotalFor984" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A WHERE A.POLICY_CODE = #{policy_code} AND A.POLICY_ID = #{policy_id } AND A.PRODUCT_CODE like '00984%']]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	
</mapper>
