<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.QueryPolicyDetailsInfoDaoImpl">

	<sql id="PA_queryContractMasterByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<!-- 查询保单层基本信息 -->
	<select id="PA_queryContractMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		
		SELECT A.POLICY_CODE,
       A.POLICY_ID,
       A.LIABILITY_STATE,
       A.ORGAN_CODE,
       A.SPECIAL_ACCOUNT_FLAG,
       (SELECT T1.ORGAN_NAME
          FROM APP___PAS__DBUSER.T_UDMP_ORG T1
         WHERE T1.ORGAN_CODE = A.ORGAN_CODE) AS manageorgname,
       A.VALIDATE_DATE,
       (SELECT T2.SALES_CHANNEL_NAME
          FROM APP___PAS__DBUSER.T_SALES_CHANNEL T2
         WHERE T2.SALES_CHANNEL_CODE = TCA2.CHANNEL_TYPE) AS channeltype,
       TCA2.AGENT_CODE agent_code1,
       TCA2.AGENT_NAME agent_name1,
       (select substr(t.admin_organ_code,0,4)
          from dev_pas.t_sales_organ t
         where t.sales_organ_code =
               (select t.sales_organ_code
                  from dev_pas.t_agent t
                 where t.agent_code = TCA2.Agent_Code)) organ_code1,
       (SELECT T2.ORGAN_NAME
          FROM APP___PAS__DBUSER.T_UDMP_ORG T2
         WHERE T2.ORGAN_CODE =
               (select substr(t.admin_organ_code,0,4)
                  from dev_pas.t_sales_organ t
                 where t.sales_organ_code =
                       (select t.sales_organ_code
                          from dev_pas.t_agent t
                         where t.agent_code = TCA2.Agent_Code))) AS agentorganname1,
       TCA1.AGENT_CODE agent_code2,
       TCA1.AGENT_NAME agent_name2,
       (select substr(t.admin_organ_code,0,4)
          from dev_pas.t_sales_organ t
         where t.sales_organ_code =
               (select t.sales_organ_code
                  from dev_pas.t_agent t
                 where t.agent_code = TCA1.Agent_Code)) organ_code2,
       (SELECT T2.ORGAN_NAME
          FROM APP___PAS__DBUSER.T_UDMP_ORG T2
         WHERE T2.ORGAN_CODE =
               (select substr(t.admin_organ_code,0,4)
                  from dev_pas.t_sales_organ t
                 where t.sales_organ_code =
                       (select t.sales_organ_code
                          from dev_pas.t_agent t
                         where t.agent_code = TCA1.Agent_Code))) AS agentorganname2,
       (select t.agent_mobile
          from dev_pas.t_agent t
         where t.agent_code = TCA1.Agent_Code) AGENT_MOBILE,
       (select tpa.next_account_bank
          from dev_pas.t_payer_account tpa
         where tpa.policy_id = a.policy_id) as next_account_bank,
       (select tb.bank_name
          from app___pas__dbuser.t_payer_account tpa,
               app___pas__dbuser.t_bank          tb
         where tpa.policy_id = a.policy_id
           and tpa.next_account_bank = tb.bank_code
           and rownum = 1) as next_account_bank_name,
       (select tpa.next_account
          from dev_pas.t_payer_account tpa
         where tpa.policy_id = a.policy_id) as next_account
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A,
       APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA1,
       APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA2  WHERE 1 = 1 ]]>
   <![CDATA[ 
   AND A.POLICY_CODE = #{policy_code}
   AND A.POLICY_CODE = TCA1.POLICY_CODE
   AND A.POLICY_CODE = TCA2.POLICY_CODE
   AND TCA1.IS_CURRENT_AGENT = '1'
   AND TCA2.IS_NB_AGENT = '1' ]]>

		
	</select>
	
	
	<select id="PA_queryPolicyAppentHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			 SELECT distinct (TCU.CUSTOMER_ID),
			    TCU.OLD_CUSTOMER_ID,
                TCU.CUSTOMER_NAME as appntname,
                TCU.CUSTOMER_GENDER appntsex,
                TCU.CUSTOMER_BIRTHDAY appntbirthday,
                TCU.CUSTOMER_CERT_TYPE appntidtype,
                TCU.CUSTOMER_CERTI_CODE appntidno,
                TCU.CUST_CERT_STAR_DATE appntideffstartdate,
                TCU.CUST_CERT_END_DATE appntideffenddate,
                (SELECT TY.COUNTRY_NAME
                   FROM APP___PAS__DBUSER.T_COUNTRY TY
                  WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) appntnative,
                TS.MOBILE_TEL appntmobile,
                TS.FIXED_TEL fixed_tel,
                TPH.JOB_CODE appntoccupationcode,
                (select tjc.job_name
                   from APP___PAS__DBUSER.T_JOB_CODE tjc
                  where tjc.job_code = TPH.JOB_CODE) appntoccupation,
                Ts.State provincecode,
                (SELECT TT.NAME
                   FROM APP___PAS__DBUSER.T_DISTRICT TT
                  WHERE TT.CODE = TS.STATE) province,
                ts.city citycode,
                (SELECT TT.NAME
                   FROM APP___PAS__DBUSER.T_DISTRICT TT
                  WHERE TT.CODE = TS.CITY) city,
                TS.DISTRICT countycode,
                (SELECT TT.NAME
                   FROM APP___PAS__DBUSER.T_DISTRICT TT
                  WHERE TT.CODE = TS.DISTRICT) county,
                TS.ADDRESS
  FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
       APP___PAS__DBUSER.T_CUSTOMER      TCU,
       APP___PAS__DBUSER.T_ADDRESS       TS
 WHERE TPH.POLICY_CODE = #{policy_code}
   AND TCU.CUSTOMER_ID = TPH.CUSTOMER_ID
   AND TS.ADDRESS_ID = TPH.ADDRESS_ID
	     ]]>
	</select>
	
	
	<select id="PA_queryRiskListInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT T.BUSI_ITEM_ID,T.MATURITY_DATE,T.BUSI_PROD_CODE,DECODE(T.MASTER_BUSI_ITEM_ID, '', 'Y', 'N') mainriskflag FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T WHERE T.POLICY_CODE=#{policy_code} ORDER BY T.MASTER_BUSI_ITEM_ID DESC
	     ]]>
	</select>
	
	<select id="PA_queryRiskInfoByusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	select tbp.product_name_sys  riskname,
       tbp.product_abbr_name riskabsname,
       tbp.product_name_std risknamestd,
       tcbp.busi_prod_code   riskcode,
       decode(sum(tcp.amount),0,'0.00',ltrim(to_char(sum(tcp.amount),'999999999999.99'))) amount,
       decode(sum(tcp.std_prem_af),0,'0.00',ltrim(to_char(sum(tcp.std_prem_af),'999999999999.99'))) premium,
       tcbp.validate_date validatedate,
       tcbp.maturity_date maturitydate,
       tcbp.liability_state riskliabilitystate,
       tcc.prem_freq chargemode,
       tcc.pay_due_date paytodate,
       decode(tcbp.master_busi_item_id, '', 'Y', 'N') mainriskflag,
       tcbp.busi_item_id riskid,
       tcbp.master_busi_item_id mainriskid,
         (select tcp.coverage_period
                     from APP___PAS__DBUSER.t_contract_product tcp
                    where tcp.IS_MASTER_ITEM = '1'
                      and tcp.busi_item_id = #{busi_item_id}) covperiod,
                  (select tcp.coverage_year
                     from APP___PAS__DBUSER.t_contract_product tcp
                    where tcp.IS_MASTER_ITEM = '1'
                      and tcp.busi_item_id = #{busi_item_id}) covyear
  from APP___PAS__DBUSER.t_contract_busi_prod tcbp,
       APP___PAS__DBUSER.t_contract_product tcp,
       (select t.prem_freq,
               t.pay_due_date,
               t.policy_code,
               t.policy_id,
               t.busi_item_id
          from (select tcp.prem_freq,
                       tce.pay_due_date,
                       tce.policy_code,
                       tcp.policy_id,
                       tcp.busi_item_id
                  from APP___PAS__DBUSER.t_contract_product tcp,
                       APP___PAS__DBUSER.T_CONTRACT_EXTEND  tce
                 where tcp.busi_item_id = #{busi_item_id}
                   and tce.policy_id = tcp.policy_id
                   and tce.item_id = tcp.item_id
                 order by tcp.is_master_item desc) T
         where rownum = 1) tcc,
       dev_pds.t_business_product tbp
 where tcbp.busi_item_id = #{busi_item_id}
   and tcbp.policy_code = tcc.policy_code
   and tcbp.policy_code = tcp.policy_code
   and tcbp.busi_item_id = tcp.busi_item_id
   and tcbp.busi_item_id = tcc.busi_item_id
   and tcbp.busi_prd_id = tbp.business_prd_id
 group by 
       tbp.product_name_sys ,
       tbp.product_abbr_name,
       tcbp.busi_prod_code  ,
       tcbp.validate_date ,
       tcbp.maturity_date ,
       tcbp.liability_state ,
       tcc.prem_freq,
       tcc.pay_due_date,
       decode(tcbp.master_busi_item_id, '', 'Y', 'N') ,
       tcbp.busi_item_id ,
       tcbp.master_busi_item_id ,
       tbp.product_name_std
	     ]]>
	</select>
	
	<select id="PA_queryBenefitInsuredInfoByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			 SELECT DISTINCT (TCU.CUSTOMER_ID) custid,
			 TCU.OLD_CUSTOMER_ID,
			 tbi.insured_id insured_id,
                TBI.ORDER_ID orderid,
                TCU.CUSTOMER_NAME AS insuredname,
                TCU.CUSTOMER_GENDER insuredsex,
                 (SELECT TY.COUNTRY_NAME
                   FROM APP___PAS__DBUSER.T_COUNTRY TY
                  WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) insurednative,
                TCU.CUSTOMER_BIRTHDAY insuredbirthday,
                TCU.CUSTOMER_CERT_TYPE insuredidtype,
                TCU.CUSTOMER_CERTI_CODE insuredidno,
                TCU.CUST_CERT_STAR_DATE insuredideffstartdate,
                TCU.CUST_CERT_END_DATE insuredideffenddate,
                TS.MOBILE_TEL insuredmobile,
                TS.FIXED_TEL fixed_tel,
                TIL.JOB_CODE insuredoccupationcode,
                (SELECT TJC.JOB_NAME
                   FROM APP___PAS__DBUSER.T_JOB_CODE TJC
                  WHERE TJC.JOB_CODE = TIL.JOB_CODE) insuredoccupation,
                TS.STATE provincecode,
                (SELECT TT.NAME
                   FROM APP___PAS__DBUSER.T_DISTRICT TT
                  WHERE TT.CODE = TS.STATE) province,
                TS.CITY citycode,
                (SELECT TT.NAME
                   FROM APP___PAS__DBUSER.T_DISTRICT TT
                  WHERE TT.CODE = TS.CITY) city,
                TS.DISTRICT countycode,
                (SELECT TT.NAME
                   FROM APP___PAS__DBUSER.T_DISTRICT TT
                  WHERE TT.CODE = TS.DISTRICT) county,
                TS.ADDRESS insuredaddress,
                TIL.RELATION_TO_PH relationtoappntcode,
                TIL.INSURED_AGE insuredage,
                (SELECT TT.RELATION_NAME
                   FROM APP___PAS__DBUSER.T_LA_PH_RELA TT
                  WHERE TT.RELATION_CODE = TIL.RELATION_TO_PH) relationtoappnt              
  FROM APP___PAS__DBUSER.T_BENEFIT_INSURED TBI,
       APP___PAS__DBUSER.T_INSURED_LIST  TIL,
       APP___PAS__DBUSER.T_CUSTOMER      TCU,
       APP___PAS__DBUSER.T_ADDRESS       TS
 WHERE TBI.BUSI_ITEM_ID =  #{busi_item_id}
 AND TBI.INSURED_ID = TIL.LIST_ID
   AND TCU.CUSTOMER_ID = TIL.CUSTOMER_ID
   AND TS.ADDRESS_ID = TIL.ADDRESS_ID
	     ]]>
	</select>
	
	
	<select id="PA_queryContractBeneInfoByInsuredid" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			 SELECT TCB.BENE_TYPE,
			 TC.CUSTOMER_NAME beneficiaryname,
       TC.CUSTOMER_CERT_TYPE beneficiaryidtype,
       TC.CUSTOMER_CERTI_CODE beneficiaryidno,
       TCB.SHARE_ORDER beneficiaryorder,
       decode(TCB.SHARE_RATE,1,'1.000000',ltrim(to_char(TCB.SHARE_RATE,'0.999999'))) beneficiaryshare,
       TCB.DESIGNATION beneficiaryrelationcode,
       (SELECT TT.RELATION_NAME
                   FROM APP___PAS__DBUSER.T_LA_PH_RELA TT
                  WHERE TT.RELATION_CODE = TCB.DESIGNATION) beneficiaryrelation
  FROM APP___PAS__DBUSER.T_CONTRACT_BENE TCB,
       APP___PAS__DBUSER.t_customer      tc
 WHERE 1 = 1 AND TCB.CUSTOMER_ID = TC.CUSTOMER_ID AND TCB.BENE_TYPE = '1' AND TCB.INSURED_ID = #{insured_id}
 ORDER BY TCB.SHARE_ORDER ASC,TCB.SHARE_RATE DESC
	     ]]>
	</select>

</mapper>
