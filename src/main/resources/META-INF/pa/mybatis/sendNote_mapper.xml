<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.sendnote.dao.impl.SendNoteDaoImpl">

	<!-- 失效短信提醒 保单失效前10天发送 -->
	<select id="selectlapseRemindNote" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select maste.policy_code POLICYCODE,
    min(arap.fee_amount) MONEY,
    min(prdlife.product_name) CONTRACTNAME,
    min(arap.pay_end_date) ERRORDATE,
    min(agen.agent_name) AGENTNAME,
    min(agen.agent_mobile)
    AGENTPHONE,
    min(cus.email) EMAIL,
    min(cus.mobile_tel) TEL,
    cus.customer_name CUSTOMERNAME,
    cus.customer_gender GENDER
    from
    APP___PAS__DBUSER.T_contract_master
    maste,
    APP___PAS__DBUSER.T_policy_holder holder,
    APP___PAS__DBUSER.T_contract_busi_prod busi,
    APP___PAS__DBUSER.T_Contract_Product
    PRODUCT,
    APP___PAS__DBUSER.T_PREM_ARAP ARAP,
    APP___PAS__DBUSER.T_PRODUCT_LIFE
    PRDLIFE,
    APP___PAS__DBUSER.T_CUSTOMER CUS,
    APP___PAS__DBUSER.T_CONTRACT_AGENT AGEN,
    APP___PAS__DBUSER.T_POLICY_HOLDER INSURE
    WHERE
    MASTE.POLICY_ID =
    BUSI.POLICY_ID
    AND
    AGEN.POLICY_ID=MASTE.POLICY_ID
    AND
    BUSI.BUSI_ITEM_ID=PRODUCT.BUSI_ITEM_ID
    AND
    MASTE.POLICY_ID=HOLDER.POLICY_ID
    AND HOLDER.CUSTOMER_ID=CUS.CUSTOMER_ID
    AND ARAP.FEE_STATUS = '00'
    AND ARAP.ARAP_FLAG='1'
    AND
    ARAP.BUSINESS_CODE= MASTE.POLICY_CODE
    AND
    PRDLIFE.BUSINESS_PRD_ID=BUSI.BUSI_PRD_ID
    AND ARAP.FEE_TYPE IN ('G003010000','G003100000','G003020100','G003030100','G003040100','G003020200','G003030200','G003040200')
    AND INSURE.POLICY_CODE = MASTE.POLICY_CODE
    and to_char(arap.Pay_End_Date - 10,'yyyy-MM-dd') = to_char(#{batchTime,jdbcType=DATE},'yyyy-MM-dd')
    group by maste.policy_code, cus.customer_name,
    cus.customer_gender 
		]]>
	</select>

	<!-- 永久失效提醒短信 保单永久失效前20天发送 -->
	<select id="selectForeverLapseRemindNote" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT DISTINCT TCM.POLICY_CODE POLICYCODE,
       ADD_MONTHS(TCM.LAPSE_DATE, 24) ERRORDATE,
       TPA.FEE_AMOUNT MONEY,
       PLIFE.PRODUCT_NAME CONTRACTNAME,
       TCA.AGENT_NAME AGENTNAME,
       TCA.AGENT_MOBILE AGENTPHONE,
       cus.customer_name CUSTOMERNAME,
       cus.customer_gender GENDER,
       CUS.EMAIL EMAIL,
       CUS.MOBILE_TEL TEL
  FROM DEV_PAS.T_CONTRACT_MASTER TCM,
       DEV_PAS.T_PREM_ARAP TPA,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
       APP___PAS__DBUSER.T_PRODUCT_LIFE PLIFE,
       APP___PAS__DBUSER.T_CONTRACT_AGENT TCA,
       APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
       APP___PAS__DBUSER.T_CUSTOMER CUS
 WHERE TCM.LIABILITY_STATE = '4'
   AND TPA.FEE_STATUS = '02'
   AND TPA.FEE_TYPE = 'G003010000'
   and tpa.is_risk_main = '1'
   AND TPA.POLICY_CODE = TCM.POLICY_CODE
   AND CBP.POLICY_ID = TCM.POLICY_ID
   AND PLIFE.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
   AND TCA.POLICY_ID = TCM.POLICY_ID
   AND TPH.POLICY_ID = TCM.POLICY_ID
   AND CUS.CUSTOMER_ID = TPH.CUSTOMER_ID
   AND ADD_MONTHS(TCM.LAPSE_DATE, 24) - 20 = #{batchTime}
   AND (TCM.LAPSE_CAUSE = '1'
    OR TCM.LAPSE_CAUSE = '2')
		]]>
	</select>

	<!-- 失效短信 保单失效后第5天发送 -->
	<select id="selectLapseNote" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select maste.policy_code POLICYCODE,
    min(arap.fee_amount) MONEY,
    min(prdlife.product_name) CONTRACTNAME,
    min(maste.lapse_date)
    ERRORDATE,
    min(agen.agent_name) AGENTNAME,
    min(agen.agent_mobile)
    AGENTPHONE,
    min(cus.email) EMAIL,
    min(cus.mobile_tel) TEL,
    cus.customer_name CUSTOMERNAME,
    cus.customer_gender GENDER
    from
    APP___PAS__DBUSER.T_contract_master maste,
    APP___PAS__DBUSER.T_policy_holder holder,
    APP___PAS__DBUSER.T_contract_busi_prod busi,
    APP___PAS__DBUSER.T_Contract_Product
    product,
    APP___PAS__DBUSER.T_prem_arap arap,
    APP___PAS__DBUSER.T_product_life
    prdlife,
    APP___PAS__DBUSER.T_customer cus,
    APP___PAS__DBUSER.T_CONTRACT_AGENT agen,
    APP___PAS__DBUSER.T_POLICY_HOLDER insure
    where maste.policy_id = busi.policy_id
    and agen.policy_id=maste.policy_id
    and busi.busi_item_id=product.busi_item_id
    and maste.policy_id=holder.policy_id
    and holder.customer_id=cus.customer_id
    and arap.business_code= maste.policy_code
    and prdlife.business_prd_id=busi.busi_prd_id
    and insure.policy_code = maste.policy_code
    and to_char(maste.Lapse_Date + 5,'yyyy-MM-dd') = to_char(#{batchTime},'yyyy-MM-dd')
    group by maste.policy_code, cus.customer_name,
    cus.customer_gender
		]]>
	</select>

	<!-- 红利分配短信提醒（山西专用） 红利实际分配日次日 -->
	<select id="selectBonusAllotNoteSX" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select bbb.policy_code POLICYCODESORT,
		bbb.bonus_sa AMOUNT,
		bbb.item_id,
		product.amount MONEY,
		bbb.allocate_date ACCOUNTYEAR,
		bbb.mobile_tel TEL,
		bbb.email EMAIL
		from (select abc.policy_code,
		abc.bonus_sa,
		abc.ALLOCATE_DATE,
		aaa.item_id,
		address.mobile_tel,
		address.email
		from (select bonus.policy_code,
		max(bonus.allocate_date)
		as allocate_date,
		sum(bonus.bonus_sa) as bonus_sa
		from
		APP___PAS__DBUSER.T_BONUS_ALLOCATE
		bonus
		where
		to_char(bonus.allocate_date, 'yyyy') =
		to_char(#{batchTime},
		'yyyy')]]>
                  <![CDATA[ and bonus.allocate_date <= #{batchTime,jdbcType=DATE} ]]>
		 <![CDATA[ group by bonus.policy_code) abc,
		APP___PAS__DBUSER.T_BONUS_ALLOCATE
		aaa,
		APP___PAS__DBUSER.T_POLICY_HOLDER
		insure,
		APP___PAS__DBUSER.T_address address
		where to_char(abc.allocate_date, 'yyyy-mm-dd') =
		to_char(#{batchTime}, 'yyyy-mm-dd')
		and
		aaa.allocate_date = abc.allocate_date
		and abc.policy_code =
		aaa.policy_code
		and insure.policy_code = aaa.policy_code
		and
		address.address_id = insure.address_id) bbb,
		APP___PAS__DBUSER.T_Contract_Product product
		where bbb.item_id =
		product.item_id and product.ORGAN_CODE in (
        SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T 
          START WITH T.ORGAN_CODE = '8638'
          CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
      )
		]]>
	</select>

	<!-- 万能险月度结算短信提醒（山西专用） 月度结算日次日 -->
	<select id="selectSettlementNoteSX" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[		SELECT T.INVEREST,
       T.SETTLE_DATE,
       T.POLICY_NUM,
       T.INTEREST_CAPITAL,
       T.PHONENUM,
       T.ORGANCODE,
       T.POL_MANAGE_FEE,
       (T.RISK_FEE + T.RISK_FEE_OTHER) as RISK_FEE,
       
       (CASE
         WHEN RISK_FEE > 0 THEN
          1
         ELSE
          0
       END) AS IS_DED_RISK_FEE,
       (CASE
         WHEN POL_MANAGE_FEE > 0 THEN
          1
         ELSE
          0
       END) AS IS_DED_POL_FEE,
       T.CUSTOMERNAME,
       T.CUSTOMERID,
       T.GENDER
  FROM (SELECT SUM(TFS.INTEREST) INVEREST,
               MAX(TFS.SETTLE_DATE) SETTLE_DATE,
               TCM.POLICY_CODE POLICY_NUM,
               TC.CUSTOMER_NAME CUSTOMERNAME,
               TC.CUSTOMER_ID CUSTOMERID,
               TC.CUSTOMER_GENDER GENDER,
               INVEST.INTEREST_CAPITAL,
               (SELECT NVL(SUM(TFT.TRANS_AMOUNT), 0)
                  FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                 WHERE TFT.LIST_ID = INVEST.LIST_ID
                   AND TFT.TRANS_CODE = '04'
                   AND TO_CHAR(TFT.DEAL_TIME + 4, 'yyyy-MM-dd') <=to_char(#{batchTime}, 'yyyy-MM-dd')
                   AND ADD_MONTHS(TFT.DEAL_TIME, 12) + 4 >= #{batchTime}) AS POL_MANAGE_FEE,
               (SELECT NVL(SUM(TFT.TRANS_AMOUNT), 0)
                  FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                 WHERE TFT.LIST_ID = INVEST.LIST_ID
                   AND TFT.TRANS_CODE = '03'
                   AND TO_CHAR(TFT.DEAL_TIME + 4, 'yyyy-MM-dd') <=to_char(#{batchTime}, 'yyyy-MM-dd')
                   AND ADD_MONTHS(TFT.DEAL_TIME, 12) + 4 >= #{batchTime}) AS RISK_FEE,
               (SELECT NVL(SUM(TFT.TRANS_AMOUNT), 0)
                  FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
                 WHERE TFT.LIST_ID = INVEST.LIST_ID
                   AND TFT.TRANS_CODE = '41'
                   AND TO_CHAR(TFT.DEAL_TIME + 4, 'yyyy-MM-dd') <=to_char(#{batchTime}, 'yyyy-MM-dd')
                   AND ADD_MONTHS(TFT.DEAL_TIME, 12) + 4 >= #{batchTime}) AS RISK_FEE_OTHER,
               ADDRESS.MOBILE_TEL PHONENUM,
               TCM.ORGAN_CODE ORGANCODE
          FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT TFS,
               APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
               APP___PAS__DBUSER.T_CONTRACT_INVEST INVEST,
               APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
               APP___PAS__DBUSER.T_ADDRESS         ADDRESS,
               APP___PAS__DBUSER.T_CUSTOMER        TC
         WHERE TFS.INVEST_ID = INVEST.LIST_ID
           AND INVEST.POLICY_ID = TCM.POLICY_ID
           AND TPH.POLICY_CODE = TCM.POLICY_CODE
           AND ADDRESS.ADDRESS_ID = TPH.ADDRESS_ID
           AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
           AND INVEST.INVEST_ACCOUNT_TYPE = '2'
           AND TO_CHAR(TCM.VALIDATE_DATE, 'MM-DD') = TO_CHAR(#{batchTime} - 4, 'MM-DD')
           AND ADD_MONTHS(TCM.VALIDATE_DATE, 12) <= #{batchTime}
           AND TO_CHAR(TFS.SETTLE_DATE + 4, 'yyyy-MM-dd') <=to_char(#{batchTime}, 'yyyy-MM-dd')
           AND ADD_MONTHS(TFS.SETTLE_DATE, 12) + 4 >= #{batchTime}
           AND TCM.ORGAN_CODE LIKE '8638%'
         GROUP BY TCM.POLICY_CODE,
                  INVEST.LIST_ID,
                  INVEST.INTEREST_CAPITAL,
                  ADDRESS.MOBILE_TEL,
                  TCM.ORGAN_CODE, 
                  TC.CUSTOMER_NAME ,
                   TC.CUSTOMER_ID ,
                   TC.CUSTOMER_GENDER ) T
		]]>
	</select>

	<!-- 累计生息账户月度结息短信提醒（山西专用） 年度结算-->
	<select id="selectAccountInterestNoteSX" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT MASTE.POLICY_CODE POLICYCODESORT,
       ACCOUN.INTEREST_CAPITAL INTEREST_CAPITAL,
       ADDRESS.EMAIL EMAIL,
       ADDRESS.MOBILE_TEL PHONENUM,
       TC.CUSTOMER_NAME CUSTOMERNAME,
       TC.CUSTOMER_ID CUSTOMERID,
       TC.CUSTOMER_GENDER GENDER,
       SUM(TPATL.TRANS_AMOUNT) INTEREST,
       MAX(TPATL.TRANS_TIME) SETTLE_DATE,
       MASTE.ORGAN_CODE,
       ACCOUN.ACCOUNT_ID
  FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT            ACCOUN,
       APP___PAS__DBUSER.T_CONTRACT_MASTER           MASTE,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD        PROD,
       APP___PAS__DBUSER.T_POLICY_HOLDER             INSURE,
       APP___PAS__DBUSER.T_ADDRESS                   ADDRESS,
       APP___PAS__DBUSER.T_POLICY_ACCOUNT_TRANS_LIST TPATL,
       APP___PAS__DBUSER.T_CUSTOMER                  TC
 WHERE ACCOUN.POLICY_ID = MASTE.POLICY_ID
   AND ACCOUN.ACCOUNT_TYPE = '11'
   AND MASTE.POLICY_ID = PROD.POLICY_ID
   AND ACCOUN.BUSI_ITEM_ID = PROD.BUSI_ITEM_ID
   AND INSURE.POLICY_CODE = MASTE.POLICY_CODE
   AND ADDRESS.ADDRESS_ID = INSURE.ADDRESS_ID
   AND TC.CUSTOMER_ID = INSURE.CUSTOMER_ID
   AND ACCOUN.ACCOUNT_ID = TPATL.ACCOUNT_ID
   AND TPATL.TRANS_CODE = '6'
   AND MASTE.ORGAN_CODE LIKE '8638%'
   AND TO_CHAR(MASTE.VALIDATE_DATE, 'MM-DD') = TO_CHAR(#{batchTime}- 4, 'MM-DD')
   AND TO_CHAR(TPATL.TRANS_TIME + 4,'yyyy-MM-dd') <= to_char(#{batchTime},'yyyy-MM-dd')
   AND ADD_MONTHS(TPATL.TRANS_TIME, 12) + 4 >= #{batchTime}
 GROUP BY ACCOUN.INTEREST_SUM,
          MASTE.POLICY_CODE,
          ACCOUN.CREATE_DATE,
          ACCOUN.INTEREST_CAPITAL,
          ADDRESS.EMAIL,
          ADDRESS.MOBILE_TEL,
          TC.CUSTOMER_NAME,
          TC.CUSTOMER_ID,
          TC.CUSTOMER_GENDER,
          ACCOUN.ACCOUNT_ID,
          MASTE.ORGAN_CODE
		]]>
	</select>

	<!-- 投连险计价短信提醒（山西专用） 每月首日 -->
	<select id="selectInvestPriceNoteSX" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
select tcbp.policy_code POLICYCODESORT,
    tci.ACCUM_UNITS POLICYACCOUNTSUM,
    tci.INTEREST_CAPITAL MONEY,
    customer.customer_name POLICYHOLDERSEX,
    customer.customer_gender GENDER,
    address.email EMAIL,
    address.mobile_tel TEL,
    tci.account_code ,
    tci.insert_time
    from APP___PAS__DBUSER.T_CONTRACT_INVEST tci,
    APP___PAS__DBUSER.t_Contract_Busi_Prod tcbp,
    APP___PAS__DBUSER.T_POLICY_HOLDER insure,
    APP___PAS__DBUSER.T_address address,
    dev_pas.t_contract_master tcm,
    APP___PAS__DBUSER.T_customer customer
    where tci.policy_id = tcbp.policy_id
    and insure.policy_code = tcbp.policy_code
    and address.address_id = insure.address_id
    and customer.customer_id = insure.customer_id
    and tcm.policy_id = tcbp.policy_id
    and tcbp.liability_state = '1'
    and tcbp.busi_prd_id in (select tbp.business_prd_id from dev_pas.t_business_product tbp where tbp.product_category1 = '20004')
    and tcm.ORGAN_CODE in (
        SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T 
          START WITH T.ORGAN_CODE = '8638'
          CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
      )
		]]>
	</select>

	<!-- 内蒙古万能险账户结算短信提醒 -->
	<select id="settlementNM" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
		  SELECT ROWNUM,T.POLICY_ID,
         T.POLICY_CODE   POLICYCODESORT,
         T.ORGAN_CODE,
         T.MOBILE_TEL,
         T.Create_Time   ENDDATE,
         T.SEND_TIME,
         T.BUSI_ITEM_ID,
         T.customer_name CUSTOMERNAME,
         T.customer_id   CUSTOMERID
    FROM (SELECT TCBP.POLICY_ID,
                 TCBP.POLICY_CODE,
                 TCM.ORGAN_CODE,
                 TC.MOBILE_TEL,
                 TD.Create_Time,
                 TD.SEND_TIME,
                 TCBP.BUSI_ITEM_ID,
                 tc.customer_id,
                 tc.customer_name
            FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
                 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD      TCBP,
                 APP___PAS__DBUSER.T_DOCUMENT                TD,
                 APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
                 APP___PAS__DBUSER.T_CUSTOMER      TC
           WHERE TCBP.BUSI_PRD_ID IN
                 (SELECT TBP.BUSINESS_PRD_ID
                    FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP
                   WHERE TBP.PRODUCT_CATEGORY1 = ('20003'))
             AND TCM.POLICY_ID = TCBP.POLICY_ID
             AND TD.POLICY_ID = TCBP.POLICY_ID
             AND TCBP.LIABILITY_STATE = '1'
             AND TD.TEMPLATE_CODE = 'PAS_00014'
             AND #{batchTime, jdbcType = DATE} = TD.Create_Time + 1
             AND TD.ORGAN_CODE like '8649%'
             AND TPH.POLICY_ID = TCBP.POLICY_ID
             AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID) T
	]]>
	</select>

	<!-- 查询本年度风险保额 -->
	<select id="queryBpnusSa" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select t.bonus_sa,t.insert_time
  from (select tba.insert_time, tba.bonus_sa
          from dev_pas.t_bonus_allocate tba
         where tba.policy_id = #{policy_id}
          and 1 = 1
           and to_char(tba.insert_time,'yyyy') <= to_char(#{batchTime,jdbcType=DATE} ,'yyyy')
         order by tba.insert_time desc) t
 where rownum = 1 order by t.insert_time desc]]>
	</select>

	<!-- 查询保险金额 -->
	<select id="queryAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select t.amount,t.option_type,t.bonus_sa from (select tcp.amount, tpl.option_type,tcp.bonus_sa
      from dev_pas.t_contract_product tcp, dev_pds.t_product_life tpl
       where tpl.product_id = tcp.product_id
       AND tcp.policy_code = #{POLICY_CODE}
       and tcp.product_id = #{PRODUCT_ID}
       ]]>
			<![CDATA[)t]]>
	</select>

	<!-- 北京分公司续期交费短信提醒 -->
	<select id="renewPayNoteBJ" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[
		SELECT t.policy_code POLICYCODESORT, t.fee_amount NEXTPREM, t.bank_account BANKACCOUNT,t.due_time PAYDUEDATE,t.mobile_tel MOBILETEL,t.customer_id CUSTOMERID,t.customer_name CUSTOMERNAME, t.BANK_CODE BANKCODE
  FROM (SELECT tpa.fee_amount,
               tpa.bank_account,
               tcbp.policy_code,
               tpa.due_time,
               tc.mobile_tel,
               tc.customer_id,
               tpa.bank_code,
               tc.customer_name
          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
               DEV_PAS.T_PREM_ARAP          TPA,
               dev_pas.t_Policy_Holder tph,
               dev_pas.t_customer tc,
               dev_pas.t_contract_master tcm
         WHERE TCBP.LIABILITY_STATE = '1'
           AND tpa.due_time = #{batchTime,jdbcType=DATE}
            AND TPA.FEE_STATUS = '00'
           AND TPA.POLICY_CODE = TCBP.POLICY_CODE
           AND TPA.BUSI_PROD_CODE = TCBP.BUSI_PROD_CODE
           and tph.policy_id = tcbp.policy_id
           and tc.customer_id = tph.customer_id
           and tcm.policy_id = tcbp.policy_id
           and tcm.organ_code like '8621%'
           and rownum <= 1999)T
           ]]>
	</select>
	
	<!-- 查询投连险的账户价值 -->
	<select id="queryAllInvestUnitPriceBOs" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT C.BID_PRICE,C.PRICING_DATE FROM(SELECT ROWNUM RN, A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1=1 ]]>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.PRICING_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.PRICING_DATE <= #{end_date} ]]></if>
		<![CDATA[ ORDER BY A.PRICING_DATE DESC) C WHERE ROWNUM = 1	]]>
		
	</select>	
	<!-- 提前存款短信提醒（针对于自动清偿）贷款止期前七天发送 -->
	<select id="newDepositNote" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TPAS.LOAN_START_DATE, TPAS.REPAY_DUE_DATE, TPAS.INTEREST_CAPITAL,TBA.BANK_ACCOUNT,TBA.ISSUE_BANK_NAME,TCM.POLICY_CODE,TC.CUSTOMER_NAME,TC.MOBILE_TEL,TC.CUSTOMER_ID
  FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS,DEV_PAS.T_CONTRACT_MASTER TCM,DEV_PAS.T_POLICY_HOLDER TPH,DEV_PAS.T_CUSTOMER TC,DEV_PAS.T_BANK_ACCOUNT TBA
 WHERE TPAS.IS_AUTO_RENEW = '0'
   AND TPAS.REPAY_DUE_DATE = #{batchTime,jdbcType=DATE}
   AND TPAS.ACCOUNT_TYPE = '4'
   AND TPAS.POLICY_ID = TCM.POLICY_ID
   AND TCM.LIABILITY_STATE = '1'
   AND TPH.POLICY_ID = TPAS.POLICY_ID
   AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
   AND TBA.CUSTOMER_ID = TC.CUSTOMER_ID
		]]>
	</select>
	
	<!-- 查询分红信息 -->
	<select id="findBonusAll" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select tca.policy_code, tcm.channel_type, tph.customer_id,tcm.organ_code,ta.sales_organ_code,ta.agent_code
  from dev_pas.t_contract_agent tca,
       dev_pas.t_agent          ta,
       dev_pas.t_policy_holder  tph,
       dev_pas.t_contract_master tcm
 where tca.policy_code = #{POLICY_CODE}
   and tca.is_current_agent = '1'
   and ta.agent_code = tca.agent_code
   and tph.policy_id = tca.policy_id
   and tcm.policy_code = tph.policy_code
		]]>
	</select>
	
	<!-- 查询银行名称 -->
	<select id="findBankName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select tbb.bank_branch_name from dev_pas.t_bank_branch tbb where tbb.bank_branch_code = #{BANKCODE}
		]]>
	</select>
	
	<!-- 续保转投短信提醒 -->
	<select id="renewalSwitchNote" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		  SELECT TCBP.POLICY_CODE,
         TBP.PRODUCT_NAME_STD,
         TCBP.MATURITY_DATE,
         TCE.PAY_DUE_DATE,
         TCBP.BUSI_ITEM_ID,
         TC.CUSTOMER_ID,
         TC.CUSTOMER_NAME,
         TC.MOBILE_TEL,
         TCBP.BUSI_PRD_ID
    FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
         DEV_PDS.T_BUSINESS_PRODUCT   TBP,
         DEV_PAS.T_CONTRACT_EXTEND    TCE,
         DEV_PAS.T_POLICY_HOLDER TPH,
         DEV_PAS.T_CUSTOMER TC
   WHERE TCBP.BUSI_PROD_CODE in ('********', '********')
     AND TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
     AND TCBP.POLICY_CODE = TCE.POLICY_CODE
     AND TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
     AND TPH.POLICY_ID = TCBP.POLICY_ID
     AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
     AND TCBP.MATURITY_DATE = #{batchTime,jdbcType=DATE}
     and tcbp.renew != '0'
     and ((tcbp.renew_decision != '1') or (tcbp.renew_decision is null))
	]]>
	</select>
	
	<!-- 查询投保人年龄 -->
	<select id="findPolicyAge" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TC.CUSTOMER_BIRTHDAY,TIL.POLICY_ID
  FROM DEV_PAS.t_Insured_List til, dev_pas.t_customer tc
 where til.policy_code = #{policyCode , jdbcType=VARCHAR}
   AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
		]]>
	</select>
	<select id="additionalRiskPayNote" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT DISTINCT TC.CUSTOMER_NAME CUSTOMERNAME,
               TC.CUSTOMER_ID CUSTOMERID,
                         TC.CUSTOMER_GENDER SEX,
                         TC.MOBILE_TEL TEL,
                         TPH.POLICY_CODE POLICYCODESORT,
                         SUM(TPA.FEE_AMOUNT) MONEY,
                         TPA.DUE_TIME PAYTIME,
                        SUBSTR(TCM.ORGAN_CODE, 0, 4) FILIALE_ID,
				       SUBSTR(TCM.ORGAN_CODE, 0, 6) CORE_BRANCH_COM_ID
           FROM DEV_PAS.T_POLICY_HOLDER TPH
           LEFT JOIN DEV_PAS.T_CUSTOMER TC
             ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
           LEFT JOIN DEV_PAS.T_PREM_ARAP TPA
             ON TPH.POLICY_CODE = TPA.POLICY_CODE
           LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM
             ON TPA.POLICY_CODE = TCM.POLICY_CODE
          WHERE TPH.POLICY_CODE IN
                (SELECT DISTINCT TCP.POLICY_CODE
                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP
                   LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                     ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                  WHERE TCP.PAIDUP_DATE <= #{batchTime,jdbcType=DATE}
                    AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
                 INTERSECT
                 SELECT DISTINCT TCP.POLICY_CODE
                   FROM DEV_PAS.T_CONTRACT_PRODUCT TCP
                   LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                     ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                     JOIN DEV_PAS.T_CONTRACT_EXTEND TCE 
                     ON TCBP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
                  WHERE TCE.PAY_DUE_DATE > #{batchTime,jdbcType=DATE}
                    AND TCBP.MASTER_BUSI_ITEM_ID IS NOT NULL)
            AND TPA.FEE_STATUS = 00
            AND TPA.IS_RISK_MAIN = 0
           AND TPA.DUE_TIME=#{batchTime,jdbcType=DATE}
          GROUP BY TC.CUSTOMER_NAME,
                   TC.CUSTOMER_GENDER,
                   TC.MOBILE_TEL,
                   TPH.POLICY_CODE,
                   TPA.DUE_TIME,
                   TCM.ORGAN_CODE,
                   TC.CUSTOMER_ID
		]]>
	</select>
	<!--大额保单首次划款失败短信  -->
	<select id="bigAmountPayFailNote" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
              SELECT DISTINCT TC.CUSTOMER_NAME ,
                              TG.GENDER_DESC CUSTOMER_GENDER,
                              TC.MOBILE_TEL,
                              TPH.POLICY_CODE POLICY_NUM,
                              SUBSTR(tph.POLICY_CODE,9,12) POLICY_FOUR,
                              				       SUBSTR(TCM.ORGAN_CODE, 0, 4) FILIALE_ID,
				       SUBSTR(TCM.ORGAN_CODE, 0, 6) CORE_BRANCH_COM_ID,
                            (select sum(TPA.FEE_AMOUNT) from DEV_PAS.T_PREM_ARAP TPA where TPA.FEE_STATUS = '03' and tpa.policy_code=tph.policy_code) FEE_AMOUNT,
                              TPA.DUE_TIME RETURN_FAILURE_DATE
                FROM DEV_PAS.T_POLICY_HOLDER TPH
                LEFT JOIN DEV_PAS.T_CUSTOMER TC
                  ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
                LEFT JOIN DEV_PAS.T_GENDER TG
                  ON TC.CUSTOMER_GENDER = TG.GENDER_CODE
                LEFT JOIN DEV_PAS.T_PREM_ARAP TPA
                  ON TPH.POLICY_CODE = TPA.POLICY_CODE
                LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                  ON TPH.POLICY_CODE = TCBP.POLICY_CODE
                LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
                  ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
                  left join dev_Pas.t_Contract_Master tcm
                  on tcbp.policy_id=tcm.policy_id
               WHERE TPH.POLICY_CODE in
                     (SELECT POLICY_CODE
                        FROM (SELECT SUM(TPA.FEE_AMOUNT) MONEY, TPA.POLICY_CODE
                                FROM DEV_PAS.T_PREM_ARAP TPA
                               WHERE TPA.FEE_STATUS = '03'
                                 AND TPA.FAIL_TIMES = 1
                                 AND TPA.DUE_TIME = #{batchTime,jdbcType=DATE}
                               GROUP BY TPA.POLICY_CODE) T
                       WHERE T.MONEY >= 100000)
                       AND TPA.FEE_STATUS = '03'

		]]>
	</select>
	<!--大额保单预失效提醒短信  -->
	<select id="bigAmountlapseNote" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
          SELECT DISTINCT TC.CUSTOMER_NAME,
                          TG.GENDER_DESC CUSTOMER_GENDER,
                          TC.MOBILE_TEL,
                          TPH.POLICY_CODE POLICY_NUM,
                          SUBSTR(TPH.POLICY_CODE, 9, 12) POLICY_FOUR,
                          SUBSTR(TCM.ORGAN_CODE, 0, 4) FILIALE_ID,
                          SUBSTR(TCM.ORGAN_CODE, 0, 6) CORE_BRANCH_COM_ID,
                          (SELECT SUM(TP.FEE_AMOUNT)
                             FROM DEV_PAS.T_PREM_ARAP TP
                            WHERE TPA.FEE_STATUS IN ('00', '03')
                              AND TP.POLICY_CODE = TPH.POLICY_CODE
                              AND TP.DUE_TIME = TPA.DUE_TIME
                              ) FEE_AMOUNT,
                          TPA.PAY_END_DATE + 1 RETURN_FAILURE_DATE
            FROM DEV_PAS.T_POLICY_HOLDER TPH
            LEFT JOIN DEV_PAS.T_CUSTOMER TC
              ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
            LEFT JOIN DEV_PAS.T_GENDER TG
              ON TC.CUSTOMER_GENDER = TG.GENDER_CODE
            LEFT JOIN DEV_PAS.T_PREM_ARAP TPA
              ON TPH.POLICY_CODE = TPA.POLICY_CODE
            LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
              ON TPH.POLICY_CODE = TCBP.POLICY_CODE
            LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
              ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
            LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM
              ON TCBP.POLICY_ID = TCM.POLICY_ID
           WHERE TPH.POLICY_CODE IN
                 (SELECT POLICY_CODE
                    FROM (SELECT SUM(TPA.FEE_AMOUNT) MONEY, TPA.POLICY_CODE
                            FROM DEV_PAS.T_PREM_ARAP TPA
                           WHERE TPA.FEE_STATUS IN ('00', '03')
                             AND TPA.PAY_END_DATE + 1 = #{batchTime,jdbcType=DATE}
                           GROUP BY TPA.POLICY_CODE) T
                   WHERE T.MONEY >= 100000)
             AND TPA.PAY_END_DATE + 1 = #{batchTime,jdbcType=DATE}      
             AND TPA.FEE_STATUS IN ('00', '03')

		]]>

	</select>	
		<select id="queryRiskNameAndMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT TBP.PRODUCT_ABBR_NAME RISKNAME, TCBP.MASTER_BUSI_ITEM_ID MASTERS
				  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
				    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
				 WHERE TCBP.MASTER_BUSI_ITEM_ID IS NULL
				   AND TCBP.POLICY_CODE = #{POLICYCODESORT,jdbcType=VARCHAR}
				UNION ALL
				SELECT TBP.PRODUCT_ABBR_NAME RISKNAME, TCBP.MASTER_BUSI_ITEM_ID MASTERS
				  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
				    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
				 WHERE TCBP.MASTER_BUSI_ITEM_ID IS NOT NULL
				   AND TCBP.POLICY_CODE = #{POLICYCODESORT,jdbcType=VARCHAR}
		]]>
	</select>
	<select id="queryContractProdIsMaster" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[   SELECT  SUM(TCP.AMOUNT)  AMOUNT
       FROM DEV_PAS.T_CONTRACT_PRODUCT TCP
      WHERE TCP.IS_MASTER_ITEM = '0'
        AND TCP.POLICY_CODE = #{policy_Code}
        ]]>
	
	</select>
	<!--根据保单查询险种名称  -->
	<select id="queryContractProdName" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[   SELECT TBP.PRODUCT_ABBR_NAME,CBP.MASTER_BUSI_ITEM_ID FROM DEV_PAS.T_BUSINESS_PRODUCT TBP  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP ON TBP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID WHERE CBP.POLICY_CODE= #{policy_Code}
        ]]>
	
	</select>
	
	<!-- 续保通知短信 -->
	<!--<select id="renewalAdvanceNote" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	SELECT DISTINCT CM.POLICY_CODE POLICY_CODE,SUBSTR(CM.POLICY_CODE,-4,4) POLICY_FOUR,
	            (SELECT SC.SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL SC WHERE SC.SALES_CHANNEL_CODE=CM.CHANNEL_TYPE) ORIGINAL,
	            CBP.BUSI_PROD_CODE INSURANCE_CODE,BP.PRODUCT_ABBR_NAME INSURANCE_NAME,
	            C.OLD_CUSTOMER_ID OLD_CUSTOMER_ID,C.CUSTOMER_NAME CUSTOMER_NAME,TA.MOBILE_TEL MOBILE_TEL,
	            C.CUSTOMER_GENDER CUSTOMER_GENDER,C.WECHAT_NO WECHAT,'' AS APP,C.EMAIL EMAIL,
	            A.AGENT_CODE AGENT_SAPID,A.AGENT_NAME AGENT_NAME,SO.SALES_ORGAN_NAME BUSINESS_TEAM,
	            (SELECT SC.SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL SC WHERE SC.SALES_CHANNEL_CODE=A.AGENT_CHANNEL) SERVICE_CONTENT,
	            (SELECT SO1.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN SO1 WHERE SO1.SALES_ORGAN_CODE=SO.PARENT_CODE) BUSINESS_DEPT,
	            A.AGENT_MOBILE AGENT_PHONE,(SELECT S.AGENT_STATUS_NAME FROM DEV_PAS.T_AGENT_STATUS S WHERE S.AGENT_STATUS=A.AGENT_STATUS) AGENT_ATATUS,
	            (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) FILIALE_ID,
	            (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) FILIALE_NAME,
	            (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) CORE_BRANCH_COM_ID,
	            (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) CORE_BRANCH_COM_NAME,
	            (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,8)) BRANCH_COM_ID,
	            (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,8)) BRANCH_COM_NAME,
	            (SELECT LISTAGG((SELECT BP.PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT BP WHERE BP.PRODUCT_CODE_SYS = PROD.BUSI_PROD_CODE),'、') 
	                    WITHIN GROUP (ORDER BY PROD.MASTER_BUSI_ITEM_ID) FROM DEV_PAS.T_CONTRACT_BUSI_PROD PROD 
	                    WHERE PROD.MASTER_BUSI_ITEM_ID=CBP.BUSI_ITEM_ID AND PROD.LIABILITY_STATE=1 AND PROD.RENEWAL_STATE='1' AND PROD.RENEW_DECISION='2') ADDITION_NAME,
	            TO_CHAR(CBP.EXPIRY_DATE-1,'yyyy"年"MM"月"dd"日"') EXPIRY_DATE,'是' RENEW,
	            TO_CHAR(PA.DUE_TIME,'yyyy"年"MM"月"dd"日"') PAY_DUE_DATE,
	            (SELECT TO_CHAR(PREM.PAY_END_DATE,'yyyy"年"MM"月"dd"日"') FROM DEV_PAS.T_PREM_ARAP PREM WHERE PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE AND PREM.DERIV_TYPE='003' AND PREM.ARAP_FLAG='1' AND PREM.FEE_STATUS='00') PAY_END_DATE,
	            (SELECT SUM(PREM.FEE_AMOUNT) FROM DEV_PAS.T_PREM_ARAP PREM WHERE PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.DERIV_TYPE='003' AND PREM.ARAP_FLAG='1' AND PREM.FEE_STATUS='00') TOTAL_PREM,
	            (SELECT SUM(PREM.FEE_AMOUNT) FROM DEV_PAS.T_PREM_ARAP PREM WHERE PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.DERIV_TYPE='003' AND PREM.BUSI_PROD_CODE!=CBP.BUSI_PROD_CODE AND PREM.ARAP_FLAG='1' AND PREM.FEE_STATUS='00') ADDITION_PREM,
	            (SELECT SUM(PREM.FEE_AMOUNT) FROM DEV_PAS.T_PREM_ARAP PREM WHERE PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.DERIV_TYPE='003' AND PREM.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE AND PREM.ARAP_FLAG='1' AND PREM.FEE_STATUS='00') MAIN_PREM,
	            SUBSTR(ACC.NEXT_ACCOUNT,-4,4) ACCOUNT_FOUR,
	            (SELECT B.BANK_NAME FROM DEV_PAS.T_BANK B WHERE B.BANK_CODE = ACC.NEXT_ACCOUNT_BANK) BANK_NAME
	
	            FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
	            INNER JOIN DEV_PAS.T_POLICY_HOLDER PH ON CBP.POLICY_CODE = PH.POLICY_CODE
	            INNER JOIN DEV_PAS.T_ADDRESS TA ON PH.ADDRESS_ID = TA.ADDRESS_ID
	            INNER JOIN DEV_PAS.T_CUSTOMER C ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	            INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CBP.POLICY_CODE = CM.POLICY_CODE
	            INNER JOIN DEV_PAS.T_CONTRACT_AGENT CA ON CBP.POLICY_CODE = CA.POLICY_CODE
	            INNER JOIN DEV_PAS.T_AGENT A ON CA.AGENT_CODE = A.AGENT_CODE
	            INNER JOIN DEV_PAS.T_SALES_ORGAN SO ON A.SALES_ORGAN_CODE = SO.SALES_ORGAN_CODE
	            INNER JOIN DEV_PAS.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID AND BP.PRODUCT_CATEGORY='10001'
	            INNER JOIN DEV_PAS.T_PAYER_ACCOUNT ACC ON CM.POLICY_ID = ACC.POLICY_ID
	            INNER JOIN DEV_PAS.T_PREM_ARAP PA ON PA.POLICY_CODE = CBP.POLICY_CODE AND PA.DERIV_TYPE='003' AND PA.FEE_STATUS='00'
	            WHERE 1=1
	            AND CBP.MASTER_BUSI_ITEM_ID IS NULL
	            AND CBP.RENEW = '1' /*自动续保标志*/
	            AND CBP.LIABILITY_STATE = 1
	            AND CBP.RENEWAL_STATE = '1' /*已抽档*/
	            AND CBP.RENEW_DECISION = '2' /*可续保*/
	            AND CBP.BUSI_PROD_CODE NOT IN ('********','********','********','********','********','********','********','********','********','********','********','********','********','********')
	            ]]>
	            <if test=" batchTime  != null  and  batchTime  != '' "><![CDATA[ AND PA.DUE_TIME = #{batchTime,jdbcType=DATE} ]]></if>
	</select>-->
	<!-- 划款期结束提醒 -->
	<!-- <select id="transferDateNote" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	SELECT DISTINCT CM.POLICY_CODE POLICY_CODE,SUBSTR(CM.POLICY_CODE,-4,4) POLICY_FOUR,
		        (SELECT SC.SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL SC WHERE SC.SALES_CHANNEL_CODE=CM.CHANNEL_TYPE) ORIGINAL,
		        CBP.BUSI_PROD_CODE INSURANCE_CODE,BP.PRODUCT_ABBR_NAME INSURANCE_NAME,
		        C.OLD_CUSTOMER_ID OLD_CUSTOMER_ID,C.CUSTOMER_NAME CUSTOMER_NAME,TA.MOBILE_TEL MOBILE_TEL,
		        C.CUSTOMER_GENDER CUSTOMER_GENDER,
		        C.WECHAT_NO WECHAT,'' AS APP,C.EMAIL EMAIL,A.AGENT_CODE,A.AGENT_NAME,SO.SALES_ORGAN_NAME BUSINESS_TEAM,
		        (SELECT SC.SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL SC WHERE SC.SALES_CHANNEL_CODE=A.AGENT_CHANNEL) SERVICE_CONTENT,
		        (SELECT SO1.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN SO1 WHERE SO1.SALES_ORGAN_CODE=SO.PARENT_CODE) BUSINESS_DEPT,
		        A.AGENT_MOBILE AGENT_PHONE,(SELECT S.AGENT_STATUS_NAME FROM DEV_PAS.T_AGENT_STATUS S WHERE S.AGENT_STATUS=A.AGENT_STATUS) AGENT_ATATUS,
		        (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) FILIALE_ID,
		        (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) FILIALE_NAME,
		        (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) CORE_BRANCH_COM_ID,
		        (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) CORE_BRANCH_COM_NAME,
		        (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,8)) BRANCH_COM_ID,
		        (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,8)) BRANCH_COM_NAME,
		        (SELECT LISTAGG((SELECT BP.PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT BP WHERE BP.PRODUCT_CODE_SYS = PROD.BUSI_PROD_CODE),'、') 
		                WITHIN GROUP (ORDER BY PROD.MASTER_BUSI_ITEM_ID) FROM DEV_PAS.T_CONTRACT_BUSI_PROD PROD 
		                WHERE PROD.MASTER_BUSI_ITEM_ID=CBP.BUSI_ITEM_ID AND PROD.LIABILITY_STATE=1 AND PROD.RENEWAL_STATE='1' AND PROD.RENEW_DECISION='2') ADDITION_NAME,
		        TO_CHAR(CBP.EXPIRY_DATE-1,'yyyy"年"MM"月"dd"日"') EXPIRY_DATE,'是' RENEW,
		        TO_CHAR(PREM.DUE_TIME,'yyyy"年"MM"月"dd"日"') PAY_DUE_DATE,
		        TO_CHAR(PREM.PAY_END_DATE,'yyyy"年"MM"月"dd"日"') PAY_END_DATE,
		        PREM.SEQ_NO SEQ_NO
		        
		        FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		        INNER JOIN DEV_PAS.T_POLICY_HOLDER PH ON CBP.POLICY_CODE=PH.POLICY_CODE
		        INNER JOIN DEV_PAS.T_ADDRESS TA ON PH.ADDRESS_ID = TA.ADDRESS_ID
		        INNER JOIN DEV_PAS.T_CUSTOMER C ON PH.CUSTOMER_ID = C.CUSTOMER_ID
		        INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CBP.POLICY_CODE=CM.POLICY_CODE
		        INNER JOIN DEV_PAS.T_CONTRACT_AGENT CA ON CBP.POLICY_CODE=CA.POLICY_CODE
		        INNER JOIN DEV_PAS.T_AGENT A ON CA.AGENT_CODE=A.AGENT_CODE
		        INNER JOIN DEV_PAS.T_SALES_ORGAN SO ON A.SALES_ORGAN_CODE=SO.SALES_ORGAN_CODE
		        INNER JOIN DEV_PAS.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PRD_ID=BP.BUSINESS_PRD_ID AND BP.PRODUCT_CATEGORY='10001'
		        INNER JOIN DEV_PAS.T_PREM_ARAP PREM ON PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.DERIV_TYPE='003' AND PREM.FEE_STATUS='02' AND PREM.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE AND PREM.ARAP_FLAG='2'
		        WHERE 1=1
		        AND CBP.MASTER_BUSI_ITEM_ID IS NULL
		        AND CBP.RENEW = '1' /*自动续保标志*/
		        AND CBP.LIABILITY_STATE = 3 AND CM.LIABILITY_STATE=3
		        AND CBP.END_CAUSE='01' AND CM.END_CAUSE='01'
		        AND CBP.RENEWAL_STATE = '1' /*已抽档*/
		        AND CBP.RENEW_DECISION = '2' /*可续保*/
		        AND CBP.BUSI_PROD_CODE NOT IN ('********','********','********','********','********','********','********','********','********','********','********','********','********','********')
		        ]]>
		        <if test=" batchTime  != null  and  batchTime  != '' "><![CDATA[ AND PREM.PAY_END_DATE + 1 = #{batchTime,jdbcType=DATE} ]]></if>
	</select> -->
	<!-- 续保成功短信 -->
	<!--<select id="renewalSuccessNote" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	SELECT DISTINCT CM.POLICY_CODE POLICY_CODE,SUBSTR(CM.POLICY_CODE,-4,4) POLICY_FOUR,
	            (SELECT SC.SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL SC WHERE SC.SALES_CHANNEL_CODE=CM.CHANNEL_TYPE) ORIGINAL,
	            CBP.BUSI_PROD_CODE INSURANCE_CODE,BP.PRODUCT_ABBR_NAME INSURANCE_NAME,
	            C.OLD_CUSTOMER_ID OLD_CUSTOMER_ID,C.CUSTOMER_NAME CUSTOMER_NAME,TA.MOBILE_TEL MOBILE_TEL,
	            C.CUSTOMER_GENDER CUSTOMER_GENDER,C.WECHAT_NO WECHAT,'' AS APP,C.EMAIL EMAIL,
	            A.AGENT_CODE AGENT_SAPID,A.AGENT_NAME AGENT_NAME,SO.SALES_ORGAN_NAME BUSINESS_TEAM,
	            (SELECT SC.SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL SC WHERE SC.SALES_CHANNEL_CODE=A.AGENT_CHANNEL) SERVICE_CONTENT,
	            (SELECT SO1.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN SO1 WHERE SO1.SALES_ORGAN_CODE=SO.PARENT_CODE) BUSINESS_DEPT,
	            A.AGENT_MOBILE AGENT_PHONE,(SELECT S.AGENT_STATUS_NAME FROM DEV_PAS.T_AGENT_STATUS S WHERE S.AGENT_STATUS=A.AGENT_STATUS) AGENT_ATATUS,
	            (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) FILIALE_ID,
	            (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) FILIALE_NAME,
	            (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) CORE_BRANCH_COM_ID,
	            (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) CORE_BRANCH_COM_NAME,
	            (SELECT UO.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,8)) BRANCH_COM_ID,
	            (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,8)) BRANCH_COM_NAME,
	            (SELECT LISTAGG((SELECT BP.PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT BP WHERE BP.PRODUCT_CODE_SYS = PROD.BUSI_PROD_CODE),'、') 
	                    WITHIN GROUP (ORDER BY PROD.MASTER_BUSI_ITEM_ID) FROM DEV_PAS.T_CONTRACT_BUSI_PROD PROD 
	                    WHERE PROD.MASTER_BUSI_ITEM_ID=CBP.BUSI_ITEM_ID AND PROD.LIABILITY_STATE=1 AND PROD.RENEWAL_STATE='1' AND PROD.RENEW_DECISION='2') ADDITION_NAME,
	            TO_CHAR(PA.DUE_TIME-1,'yyyy"年"MM"月"dd"日"') EXPIRY_DATE,'是' RENEW,
	            TO_CHAR(PA.DUE_TIME,'yyyy"年"MM"月"dd"日"') PAY_DUE_DATE,
	            TO_CHAR(PA.FINISH_TIME,'yyyy"年"MM"月"dd"日"') PAY_FINISH_DATE,
	            (SELECT SUM(PREM.FEE_AMOUNT) FROM DEV_PAS.T_PREM_ARAP PREM WHERE PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.DERIV_TYPE='003' 
	                    AND PREM.ARAP_FLAG='1' AND PREM.FEE_STATUS='19' ]]>
	            <if test=" batchTime  != null  and  batchTime  != '' "><![CDATA[ AND PREM.FINISH_TIME = #{batchTime,jdbcType=DATE} ]]></if>
	            <![CDATA[ ) TOTAL_PREM,
	            (SELECT SUM(PREM.FEE_AMOUNT) FROM DEV_PAS.T_PREM_ARAP PREM WHERE PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.DERIV_TYPE='003' 
	                    AND PREM.BUSI_PROD_CODE!=CBP.BUSI_PROD_CODE AND PREM.ARAP_FLAG='1' AND PREM.FEE_STATUS='19' ]]> 
	            <if test=" batchTime  != null  and  batchTime  != '' "><![CDATA[ AND PREM.FINISH_TIME = #{batchTime,jdbcType=DATE} ]]></if>
	            <![CDATA[ ) ADDITION_PREM,
	            (SELECT SUM(PREM.FEE_AMOUNT) FROM DEV_PAS.T_PREM_ARAP PREM WHERE PREM.POLICY_CODE=CBP.POLICY_CODE AND PREM.DERIV_TYPE='003' 
	                    AND PREM.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE AND PREM.ARAP_FLAG='1' AND PREM.FEE_STATUS='19'  ]]>
	            <if test=" batchTime  != null  and  batchTime  != '' "><![CDATA[ AND PREM.FINISH_TIME = #{batchTime,jdbcType=DATE} ]]></if>
	            <![CDATA[ ) MAIN_PREM,
	            SUBSTR(ACC.NEXT_ACCOUNT,-4,4) ACCOUNT_FOUR,
	            (SELECT B.BANK_NAME FROM DEV_PAS.T_BANK B WHERE B.BANK_CODE = ACC.NEXT_ACCOUNT_BANK) BANK_NAME,
	            TO_CHAR(CBP.VALIDATE_DATE,'yyyy"年"MM"月"dd"日"') NEXT_VALIDATE_DATE,
	            TO_CHAR(CBP.EXPIRY_DATE,'yyyy"年"MM"月"dd"日"') NEXT_EXPIRY_DATE
	            
	            FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
	            INNER JOIN DEV_PAS.T_POLICY_HOLDER PH ON CBP.POLICY_CODE=PH.POLICY_CODE
	            INNER JOIN DEV_PAS.T_ADDRESS TA ON PH.ADDRESS_ID = TA.ADDRESS_ID
	            INNER JOIN DEV_PAS.T_CUSTOMER C ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	            INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CBP.POLICY_CODE=CM.POLICY_CODE
	            INNER JOIN DEV_PAS.T_CONTRACT_AGENT CA ON CBP.POLICY_CODE=CA.POLICY_CODE
	            INNER JOIN DEV_PAS.T_AGENT A ON CA.AGENT_CODE=A.AGENT_CODE
	            INNER JOIN DEV_PAS.T_SALES_ORGAN SO ON A.SALES_ORGAN_CODE=SO.SALES_ORGAN_CODE
	            INNER JOIN DEV_PAS.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PRD_ID=BP.BUSINESS_PRD_ID AND BP.PRODUCT_CATEGORY='10001'
	            INNER JOIN DEV_PAS.T_PAYER_ACCOUNT ACC ON CM.POLICY_ID=ACC.POLICY_ID
	            INNER JOIN DEV_PAS.T_PREM_ARAP PA ON PA.POLICY_CODE=CBP.POLICY_CODE AND PA.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE AND PA.DERIV_TYPE='003' AND PA.FEE_STATUS='19'
	            WHERE 1=1
	            AND CBP.MASTER_BUSI_ITEM_ID IS NULL
	            AND CBP.RENEW = '1' /*自动续保标志*/
	            AND CBP.LIABILITY_STATE = 1
	            AND CBP.BUSI_PROD_CODE NOT IN ('********','********','********','********','********','********','********','********','********','********','********','********','********','********')
	            ]]>
	            <if test=" batchTime  != null  and  batchTime  != '' "><![CDATA[ AND PA.FINISH_TIME = #{batchTime,jdbcType=DATE} ]]></if>
	</select> -->
</mapper>