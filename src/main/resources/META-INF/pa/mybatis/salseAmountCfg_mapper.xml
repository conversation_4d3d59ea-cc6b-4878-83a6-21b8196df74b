<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISalseAmountCfgDao">

	<sql id="salseAmountCfgWhereCondition">
		<if test=" start_calc_time  != null  and  start_calc_time  != ''  "><![CDATA[ AND A.START_CALC_TIME = #{start_calc_time} ]]></if>
		<if test=" open_flag  != null "><![CDATA[ AND A.OPEN_FLAG = #{open_flag} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" modify_date  != null  and  modify_date  != ''  "><![CDATA[ AND A.MODIFY_DATE = #{modify_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" validate_flag  != null "><![CDATA[ AND A.VALIDATE_FLAG = #{validate_flag} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" cur_amount  != null "><![CDATA[ AND A.CUR_AMOUNT = #{cur_amount} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" sale_limit_amount  != null "><![CDATA[ AND A.SALE_LIMIT_AMOUNT = #{sale_limit_amount} ]]></if>
		<if test=" start_calc_date  != null  and  start_calc_date  != ''  "><![CDATA[ AND A.START_CALC_DATE = #{start_calc_date} ]]></if>
		<if test=" balance_amount  != null "><![CDATA[ AND A.BALANCE_AMOUNT = #{balance_amount} ]]></if>
		<if test=" quota_warn_ratio  != null "><![CDATA[ AND A.QUOTA_WARN_RATIO = #{quota_warn_ratio} ]]></if>
		<if test=" operate_type  != null "><![CDATA[ AND A.OPERATE_TYPE = #{operate_type} ]]></if>
		<if test=" warn_mail_flag  != null "><![CDATA[ AND A.WARN_MAIL_FLAG = #{warn_mail_flag} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="querySalseAmountCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="querySalseAmountCfgByBankCodeCondition">
		<if test=" bank_code != null and bank_code != '' "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
	</sql>	
	<sql id="querySalseAmountCfgByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="querySalseAmountCfgByChannelTypeCondition">
		<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
	</sql>	
	<sql id="querySalseAmountCfgByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSalseAmountCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SALSE_AM_CFG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG(
				START_CALC_TIME, OPEN_FLAG, INSERT_TIME, UPDATE_TIME, INPUT_DATE, 
				]]><if test="modify_date != null and modify_date != ''">MODIFY_DATE, </if><![CDATA[ BUSI_PROD_CODE, 
				VALIDATE_FLAG, INSERT_TIMESTAMP, ORGAN_CODE, CUR_AMOUNT, 
				]]><if test="channel_type != null and channel_type != ''">CHANNEL_TYPE, </if><![CDATA[ UPDATE_BY, OPERATOR_BY, 
				]]><if test="bank_code != null and bank_code != ''">BANK_CODE, </if><![CDATA[
				LIST_ID, UPDATE_TIMESTAMP, SALE_LIMIT_AMOUNT, INSERT_BY, START_CALC_DATE, BALANCE_AMOUNT,
				]]><if test="quota_warn_ratio != null"> QUOTA_WARN_RATIO,</if><![CDATA[
				]]><if test="warn_mail_flag != null and warn_mail_flag != ''"> WARN_MAIL_FLAG,</if><![CDATA[OPERATE_TYPE) 
			VALUES (
				#{start_calc_time, jdbcType=TIMESTAMP}, #{open_flag, jdbcType=NUMERIC} , SYSDATE , SYSDATE , CURRENT_TIMESTAMP , 
				]]><if test="modify_date != null and modify_date != ''">#{modify_date, jdbcType=DATE} , </if><![CDATA[ #{busi_prod_code, jdbcType=VARCHAR} 
				, #{validate_flag, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{cur_amount, jdbcType=NUMERIC} , 
				]]><if test="channel_type != null and channel_type != ''">#{channel_type, jdbcType=VARCHAR} , </if><![CDATA[ #{update_by, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC}, 
				]]><if test="bank_code != null and bank_code != ''"> #{bank_code, jdbcType=VARCHAR} ,</if>
				<![CDATA[ #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{sale_limit_amount, jdbcType=NUMERIC} , 
				#{insert_by, jdbcType=NUMERIC} , #{start_calc_date, jdbcType=DATE} , #{balance_amount, jdbcType=NUMERIC},
				 ]]><if test="quota_warn_ratio != null"> #{quota_warn_ratio, jdbcType=NUMERIC} ,</if><![CDATA[
				]]><if test="warn_mail_flag != null and warn_mail_flag != ''">  #{warn_mail_flag, jdbcType=VARCHAR} ,</if><![CDATA[#{operate_type, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSalseAmountCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSalseAmountCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    START_CALC_TIME = #{start_calc_time, jdbcType=TIMESTAMP} ,
		    OPEN_FLAG = #{open_flag, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    INPUT_DATE = #{input_date, jdbcType=DATE} ,
		    MODIFY_DATE = CURRENT_TIMESTAMP ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    VALIDATE_FLAG = #{validate_flag, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    CUR_AMOUNT = #{cur_amount, jdbcType=NUMERIC} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    OPERATOR_BY = #{update_by, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SALE_LIMIT_AMOUNT = #{sale_limit_amount, jdbcType=NUMERIC} ,
		    START_CALC_DATE = #{start_calc_date, jdbcType=DATE} ,
		    BALANCE_AMOUNT = #{balance_amount, jdbcType=NUMERIC} ,
		    QUOTA_WARN_RATIO = #{quota_warn_ratio, jdbcType=NUMERIC} ,
			OPERATE_TYPE = #{operate_type, jdbcType=VARCHAR} ,
			WARN_MAIL_FLAG = #{warn_mail_flag, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSalseAmountCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="querySalseAmountCfgByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalseAmountCfgByBankCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="querySalseAmountCfgByBankCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalseAmountCfgByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="querySalseAmountCfgByBusiProdCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalseAmountCfgByChannelType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="querySalseAmountCfgByChannelTypeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findSalseAmountCfgByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="querySalseAmountCfgByOrganCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSalseAmountCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="salseAmountCfgWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="findSalseAmountCfgLikeOrganCodeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM (]]>
		<include refid="findAllSalseAmountCfgLikeOrganCode" />
		<![CDATA[)]]>
	</select>

	<!-- 分页查询操作 -->
	<select id="querySalseAmountCfgLikeOrganCodeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select T.rowNumber,T.START_CALC_TIME, T.OPEN_FLAG, T.INPUT_DATE, T.MODIFY_DATE, T.BUSI_PROD_CODE, 
			T.VALIDATE_FLAG, T.ORGAN_CODE, T.CUR_AMOUNT, T.CHANNEL_TYPE, T.OPERATOR_BY, 
			T.BANK_CODE, T.LIST_ID, T.SALE_LIMIT_AMOUNT, T.START_CALC_DATE, T.BALANCE_AMOUNT,T.QUOTA_WARN_RATIO,T.OPERATE_TYPE,T.WARN_MAIL_FLAG
		from(]]>
		     <include refid="findAllSalseAmountCfgLikeOrganCode" /> 
        <![CDATA[) T where T.rowNumber > #{GREATER_NUM} and T.rowNumber <=#{LESS_NUM}]]>
	</select>
	<!-- 查询所有操作(OrganCode模糊查) -->
	<sql id="findAllSalseAmountCfgLikeOrganCode">
		<![CDATA[ SELECT ROWNUM rowNumber,A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE ROWNUM <=  1000 AND A.VALIDATE_FLAG = 0 ]]>
		<if test=" start_calc_time  != null  and  start_calc_time  != ''  "><![CDATA[ AND A.START_CALC_TIME = #{start_calc_time} ]]></if>
		<if test=" open_flag  != null "><![CDATA[ AND A.OPEN_FLAG = #{open_flag} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" modify_date  != null  and  modify_date  != ''  "><![CDATA[ AND A.MODIFY_DATE = #{modify_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%') ]]></if>
		<if test=" cur_amount  != null "><![CDATA[ AND A.CUR_AMOUNT = #{cur_amount} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" sale_limit_amount  != null "><![CDATA[ AND A.SALE_LIMIT_AMOUNT = #{sale_limit_amount} ]]></if>
		<if test=" start_calc_date  != null  and  start_calc_date  != ''  "><![CDATA[ AND A.START_CALC_DATE = #{start_calc_date} ]]></if>
		<if test=" balance_amount  != null "><![CDATA[ AND A.BALANCE_AMOUNT = #{balance_amount} ]]></if>
		<if test=" quota_warn_ratio  != null "><![CDATA[ AND A.QUOTA_WARN_RATIO = #{quota_warn_ratio} ]]></if>
		<if test=" operate_type  != null "><![CDATA[ AND A.OPERATE_TYPE = #{operate_type} ]]></if>
		<if test=" warn_mail_flag  != null "><![CDATA[ AND A.WARN_MAIL_FLAG = #{warn_mail_flag} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</sql>
	
	<!-- 分页查询操作 -->
	<select id="PA_findAllSalseAmountCfgLikeOrganCodeList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM rowNumber,A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, 
			(SELECT T.SALES_CHANNEL_NAME FROM APP___PAS__DBUSER.T_SALES_CHANNEL T WHERE T.SALES_CHANNEL_CODE = A.CHANNEL_TYPE AND ROWNUM = 1) as channel_name,
			A.OPERATOR_BY,A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,
			(SELECT T.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T WHERE T.ORGAN_CODE = A.ORGAN_CODE AND ROWNUM = 1) AS organ_name,
			 A.BANK_CODE,A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE,
			(SELECT T.bank_name FROM APP___PAS__DBUSER.T_BANK T WHERE T.bank_code = A.BANK_CODE and rownum = 1) as bank_name, 
			 A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE ROWNUM <=  1000 AND A.VALIDATE_FLAG = 0 ]]>
		<if test=" start_calc_time  != null  and  start_calc_time  != ''  "><![CDATA[ AND A.START_CALC_TIME = #{start_calc_time} ]]></if>
		<if test=" open_flag  != null "><![CDATA[ AND A.OPEN_FLAG = #{open_flag} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" modify_date  != null  and  modify_date  != ''  "><![CDATA[ AND A.MODIFY_DATE = #{modify_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%') ]]></if>
		<if test=" cur_amount  != null "><![CDATA[ AND A.CUR_AMOUNT = #{cur_amount} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" sale_limit_amount  != null "><![CDATA[ AND A.SALE_LIMIT_AMOUNT = #{sale_limit_amount} ]]></if>
		<if test=" start_calc_date  != null  and  start_calc_date  != ''  "><![CDATA[ AND A.START_CALC_DATE = #{start_calc_date} ]]></if>
		<if test=" balance_amount  != null "><![CDATA[ AND A.BALANCE_AMOUNT = #{balance_amount} ]]></if>
		<if test=" quota_warn_ratio  != null "><![CDATA[ AND A.QUOTA_WARN_RATIO = #{quota_warn_ratio} ]]></if>
		<if test=" operate_type  != null "><![CDATA[ AND A.OPERATE_TYPE = #{operate_type} ]]></if>
		<if test=" warn_mail_flag  != null "><![CDATA[ AND A.WARN_MAIL_FLAG = #{warn_mail_flag} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findAllSalseAmountCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="salseAmountCfgWhereCondition" />
		<if test=" is_list_id  != null "><![CDATA[ AND A.LIST_ID != #{is_list_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<!-- 查询所有操作(允许销售渠道\银行为空) -->
	<select id="findAllSalseAmountCfgIsCBNull" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE ROWNUM <=  1000  ]]>
		<if test=" start_calc_time  != null  and  start_calc_time  != ''  "><![CDATA[ AND A.START_CALC_TIME = #{start_calc_time} ]]></if>
		<if test=" open_flag  != null "><![CDATA[ AND A.OPEN_FLAG = #{open_flag} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" modify_date  != null  and  modify_date  != ''  "><![CDATA[ AND A.MODIFY_DATE = #{modify_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" validate_flag  != null "><![CDATA[ AND A.VALIDATE_FLAG = #{validate_flag} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" cur_amount  != null "><![CDATA[ AND A.CUR_AMOUNT = #{cur_amount} ]]></if>
		<choose>
			<when test=" channel_type != null and channel_type != ''  ">
				<![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]>
			</when>
			<otherwise>
				<![CDATA[ AND A.CHANNEL_TYPE is null ]]>
			</otherwise>
		</choose>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<choose>
			<when test=" bank_code != null and bank_code != ''  ">
				<![CDATA[ AND A.BANK_CODE = #{bank_code}]]>
			</when>
			<otherwise>
				<![CDATA[ AND A.BANK_CODE is null ]]>
			</otherwise>
		</choose>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" is_list_id  != null "><![CDATA[ AND A.LIST_ID != #{is_list_id} ]]></if>
		<if test=" sale_limit_amount  != null "><![CDATA[ AND A.SALE_LIMIT_AMOUNT = #{sale_limit_amount} ]]></if>
		<if test=" start_calc_date  != null  and  start_calc_date  != ''  "><![CDATA[ AND A.START_CALC_DATE = #{start_calc_date} ]]></if>
		<if test=" balance_amount  != null "><![CDATA[ AND A.BALANCE_AMOUNT = #{balance_amount} ]]></if>
		<if test=" quota_warn_ratio  != null "><![CDATA[ AND A.QUOTA_WARN_RATIO = #{quota_warn_ratio} ]]></if>
		<if test=" operate_type  != null "><![CDATA[ AND A.OPERATE_TYPE = #{operate_type} ]]></if>
		<if test=" warn_mail_flag  != null "><![CDATA[ AND A.WARN_MAIL_FLAG = #{warn_mail_flag} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	

<!-- 查询个数操作 -->
	<select id="findSalseAmountCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="salseAmountCfgWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="querySalseAmountCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.START_CALC_TIME, B.OPEN_FLAG, B.INPUT_DATE, B.MODIFY_DATE, B.BUSI_PROD_CODE, 
			B.VALIDATE_FLAG, B.ORGAN_CODE, B.CUR_AMOUNT, B.CHANNEL_TYPE, B.OPERATOR_BY, 
			B.BANK_CODE, B.LIST_ID, B.SALE_LIMIT_AMOUNT, B.START_CALC_DATE, B.BALANCE_AMOUNT,B.QUOTA_WARN_RATIO,B.OPERATE_TYPE,B.WARN_MAIL_FLAG
			 FROM (
					SELECT ROWNUM RN, A.START_CALC_TIME, A.OPEN_FLAG, A.INPUT_DATE, A.MODIFY_DATE, A.BUSI_PROD_CODE, 
			A.VALIDATE_FLAG, A.ORGAN_CODE, A.CUR_AMOUNT, A.CHANNEL_TYPE, A.OPERATOR_BY, 
			A.BANK_CODE, A.LIST_ID, A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT,A.QUOTA_WARN_RATIO,A.OPERATE_TYPE,A.WARN_MAIL_FLAG
			 FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="salseAmountCfgWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 根据机构代码校验是否存在 -->
	<select id="checkByOrganCode" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  SELECT COUNT(1) FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A WHERE A.ORGAN_CODE = #{organ_code} ]]> 
	</select>
	
	<!-- 查询险种表基础信息 -->
	<select id="findSalseBusinessProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		 <![CDATA[select Count(1) from (SELECT DISTINCT P.PRODUCT_CODE_SYS,P.PRODUCT_NAME_STD AS PRODUCT_NAME_SYS
		                         FROM APP___PDS__DBUSER.T_MMS_AUTH_MAIN_INFO A join
		                              APP___PDS__DBUSER.T_MMS_AUTH_LIST_INFO M on A.AUTH_MAIN_ID = M.AUTH_MINFO_ID join
		                              APP___PDS__DBUSER.T_BUSINESS_PRODUCT P on A.AUTHO_CODE = P.PRODUCT_CODE_SYS
				WHERE M.CHANNEL_CODE IN ('03')
				AND P.PRODUCT_NAME_STD LIKE CONCAT(CONCAT('%',#{query_name} ), '%') 
				AND M.SALE_START <= #{current_date} AND M.SALE_END >= #{current_date})]]> 
	</select>
	<!-- 查询险种表基础信息 -->
	<select id="findSalseBusinessProductPage" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[SELECT DISTINCT P.PRODUCT_CODE_SYS,P.PRODUCT_NAME_STD AS PRODUCT_NAME_SYS
		                         FROM APP___PDS__DBUSER.T_MMS_AUTH_MAIN_INFO A join
		                              APP___PDS__DBUSER.T_MMS_AUTH_LIST_INFO M on A.AUTH_MAIN_ID = M.AUTH_MINFO_ID join
		                              APP___PDS__DBUSER.T_BUSINESS_PRODUCT P on A.AUTHO_CODE = P.PRODUCT_CODE_SYS
				WHERE M.CHANNEL_CODE IN ('03')
				AND P.PRODUCT_NAME_STD LIKE CONCAT(CONCAT('%',#{query_name} ), '%') 
				AND M.SALE_START <= #{current_date} AND M.SALE_END >= #{current_date}]]> 
	</select>
	<!-- 查询机构表基础信息 -->
	<select id="findSalseOrgNameTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM  APP___PAS__DBUSER.T_UDMP_ORG_REL B WHERE 1=1
		 and length(ORGAN_CODE)!=8 and B.ORGAN_NAME LIKE CONCAT(CONCAT('%',#{query_name} ), '%') ]]>
	</select>
	<!-- 查询机构表基础信息 -->
	<select id="findSalseOrgNamePage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM  RN,B.organ_code,B.organ_name FROM  APP___PAS__DBUSER.T_UDMP_ORG_REL B WHERE 1=1
		 and length(ORGAN_CODE)!=8 and B.ORGAN_NAME LIKE CONCAT(CONCAT('%',#{query_name} ), '%') ]]>
		 <![CDATA[ ORDER BY B.organ_code ]]> 
	</select>
	<!-- 查询银行基础信息 -->
	<select id="findSalseBankNameTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM  APP___PAS__DBUSER.T_BANK B WHERE length(b.bank_code)=2
				  and B.BANK_NAME LIKE CONCAT(CONCAT('%',#{query_name} ), '%') ]]>
	</select>
	<!-- 查询银行基础信息 -->
	<select id="findSalseBankNamePage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM  RN,B.bank_code,B.bank_name FROM  APP___PAS__DBUSER.T_BANK B WHERE length(b.bank_code)=2
				  and B.BANK_NAME LIKE CONCAT(CONCAT('%',#{query_name} ), '%') ORDER BY  b.bank_code asc ]]> 
	</select>
	
</mapper>
