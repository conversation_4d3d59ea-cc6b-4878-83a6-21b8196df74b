<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPremDao">

	<sql id="PA_premWhereCondition">
		<if test=" special_account_flag != null and special_account_flag != '' "><![CDATA[ AND A.special_account_flag = #{special_account_flag} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" funds_rtn_code != null and funds_rtn_code != ''  "><![CDATA[ AND A.FUNDS_RTN_CODE = #{funds_rtn_code} ]]></if>
		<if test=" due_fee_type != null and due_fee_type != ''  "><![CDATA[ AND A.DUE_FEE_TYPE = #{due_fee_type} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" holder_tel != null and holder_tel != ''  "><![CDATA[ AND A.HOLDER_TEL = #{holder_tel} ]]></if>
		<if test=" handler_name != null and handler_name != ''  "><![CDATA[ AND A.HANDLER_NAME = #{handler_name} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" service_bank_branch_name != null and service_bank_branch_name != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH_NAME = #{service_bank_branch_name} ]]></if>
		<if test=" insured_relation_to_ph != null and insured_relation_to_ph != ''  "><![CDATA[ AND A.INSURED_RELATION_TO_PH = #{insured_relation_to_ph} ]]></if>
		<if test=" insured_birthday  != null  and  insured_birthday  != ''  "><![CDATA[ AND A.INSURED_BIRTHDAY = #{insured_birthday} ]]></if>
		<if test=" holder_certi_code != null and holder_certi_code != ''  "><![CDATA[ AND A.HOLDER_CERTI_CODE = #{holder_certi_code} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" channel_org_code != null and channel_org_code != ''  "><![CDATA[ AND A.CHANNEL_ORG_CODE = #{channel_org_code} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" handler_pay_mode != null and handler_pay_mode != ''  "><![CDATA[ AND A.HANDLER_PAY_MODE = #{handler_pay_mode} ]]></if>
		<if test=" service_bank_branch_code != null and service_bank_branch_code != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH_CODE = #{service_bank_branch_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" is_agent_lock  != null "><![CDATA[ AND A.IS_AGENT_LOCK = #{is_agent_lock} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" pay_location != null and pay_location != ''  "><![CDATA[ AND A.PAY_LOCATION = #{pay_location} ]]></if>
		<if test=" insured_tel != null and insured_tel != ''  "><![CDATA[ AND A.INSURED_TEL = #{insured_tel} ]]></if>
		<if test=" policy_status  != null "><![CDATA[ AND A.POLICY_STATUS = #{policy_status} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" agent_start_date  != null  and  agent_start_date  != ''  "><![CDATA[ AND A.AGENT_START_DATE = #{agent_start_date} ]]></if>
		<if test=" last_agent_name != null and last_agent_name != ''  "><![CDATA[ AND A.LAST_AGENT_NAME = #{last_agent_name} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" last_agent_code != null and last_agent_code != ''  "><![CDATA[ AND A.LAST_AGENT_CODE = #{last_agent_code} ]]></if>
		<if test=" holder_gender  != null "><![CDATA[ AND A.HOLDER_GENDER = #{holder_gender} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" agent_relation_to_ph != null and agent_relation_to_ph != ''  "><![CDATA[ AND A.AGENT_RELATION_TO_PH = #{agent_relation_to_ph} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" agent_organ_code  != null "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" waiver_flag  != null "><![CDATA[ AND A.WAIVER_FLAG = #{waiver_flag} ]]></if>
		<if test=" holder_id_code != null and holder_id_code != ''  "><![CDATA[ AND A.HOLDER_ID_CODE = #{holder_id_code} ]]></if>
		<if test=" holder_company_name != null and holder_company_name != ''  "><![CDATA[ AND A.HOLDER_COMPANY_NAME = #{holder_company_name} ]]></if>
		<if test=" fail_times  != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" holder_cert_type != null and holder_cert_type != ''  "><![CDATA[ AND A.HOLDER_CERT_TYPE = #{holder_cert_type} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" handler_organ_code  != null "><![CDATA[ AND A.HANDLER_ORGAN_CODE = #{handler_organ_code} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" fee_scene_code != null and fee_scene_code != ''  "><![CDATA[ AND A.FEE_SCENE_CODE = #{fee_scene_code} ]]></if>
		<if test=" discount_rate != null and discount_rate != ''  "><![CDATA[ AND A.DISCOUNT_RATE = #{discount_rate} ]]></if>
		<if test=" family_policy_rate_flag != null and family_policy_rate_flag != ''  "><![CDATA[ AND A.FAMILY_POLICY_RATE_FLAG = #{family_policy_rate_flag} ]]></if>
	<!-- 		guyy_wb start -->
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND to_date(to_char(A.DUE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd') > to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND to_date(to_char(A.DUE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd') <= to_date(to_char(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
<!-- 		guyy_wb end -->
		<if test=" feeStatusList  != null and feeStatusList.size()!=0">
			<![CDATA[ AND A.FEE_STATUS IN ]]>
			<foreach collection ="feeStatusList"  item="feestatus" index="index" open="(" close=")" separator=",">#{feestatus}</foreach>
		</if>
		<!-- 责任层ID -->
		<if test=" item_id_list  != null and item_id_list.size()!=0">
			<![CDATA[ AND A.ITEM_ID IN ]]>
			<foreach collection ="item_id_list"  item="item_id" index="index" open="(" close=")" separator=",">#{item_id}</foreach>
		</if>
		<if test=" busi_prod_code_list  != null  and busi_prod_code_list.size() !=0 ">
			<![CDATA[ AND A.busi_prod_code IN ]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code" index="index"
				open="(" close=")" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
		</if>
		<!-- 责任层ID -->
		<if test=" item_id_list  != null and item_id_list.size()!=0">
			<![CDATA[ AND A.ITEM_ID IN ]]>  
			<foreach collection ="item_id_list"  item="item_id" index="index" open="(" close=")" separator=",">#{item_id}</foreach>
		</if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPremByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPrem"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PREM__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PREM(
				HOLDER_NAME, BUSI_PROD_NAME, UNIT_NUMBER, PAID_COUNT, FUNDS_RTN_CODE, DUE_FEE_TYPE, BUSI_PROD_CODE, 
				ORGAN_CODE, HOLDER_TEL, UPDATE_BY, HANDLER_NAME, CHARGE_YEAR, IS_RISK_MAIN, SERVICE_BANK_BRANCH_NAME, 
				INSURED_RELATION_TO_PH, INSURED_BIRTHDAY, HOLDER_CERTI_CODE, AGENT_NAME, UPDATE_TIME, CHANNEL_ORG_CODE, INSURED_NAME, 
				AMOUNT, INSURED_ID, HANDLER_PAY_MODE, SERVICE_BANK_BRANCH_CODE, POLICY_CODE, IS_AGENT_LOCK, PAY_MODE, 
				UPDATE_TIMESTAMP, INSERT_BY, PAY_LOCATION, INSURED_TEL, POLICY_STATUS, BANK_ACCOUNT, AGENT_START_DATE, 
				LAST_AGENT_NAME, FINISH_TIME, ITEM_ID, LAST_AGENT_CODE, HOLDER_GENDER, INSERT_TIMESTAMP, DUE_TIME, 
				AGENT_RELATION_TO_PH, LIST_ID, FEE_AMOUNT, AGENT_ORGAN_CODE, HOLDER_ID, WAIVER_FLAG, HOLDER_ID_CODE, 
				HOLDER_COMPANY_NAME, FAIL_TIMES, INSERT_TIME, POLICY_YEAR, BANK_USER_NAME, FEE_STATUS, HOLDER_CERT_TYPE, 
				CHARGE_PERIOD, HANDLER_ORGAN_CODE, BANK_CODE, AGENT_CODE, PREM_FREQ, FEE_SCENE_CODE, ARAP_FLAG , 
				BUSINESS_CODE, SERVICE_CODE,PRODUCT_CODE,SPECIAL_ACCOUNT_FLAG,DISCOUNT_RATE,FAMILY_POLICY_RATE_FLAG,POVRE_DISCOUNT_RATE ) 
			VALUES (
				#{holder_name, jdbcType=VARCHAR}, #{busi_prod_name, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{paid_count, jdbcType=NUMERIC} , #{funds_rtn_code, jdbcType=VARCHAR} , #{due_fee_type, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{organ_code, jdbcType=VARCHAR} , #{holder_tel, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{handler_name, jdbcType=VARCHAR} , #{charge_year, jdbcType=NUMERIC} , #{is_risk_main, jdbcType=NUMERIC} , #{service_bank_branch_name, jdbcType=VARCHAR} 
				, #{insured_relation_to_ph, jdbcType=VARCHAR} , #{insured_birthday, jdbcType=DATE} , #{holder_certi_code, jdbcType=VARCHAR} , #{agent_name, jdbcType=VARCHAR} , SYSDATE , #{channel_org_code, jdbcType=VARCHAR} , #{insured_name, jdbcType=VARCHAR} 
				, #{amount, jdbcType=NUMERIC} , #{insured_id, jdbcType=NUMERIC} , #{handler_pay_mode, jdbcType=VARCHAR} , #{service_bank_branch_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{is_agent_lock, jdbcType=NUMERIC} , #{pay_mode, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{pay_location, jdbcType=VARCHAR} , #{insured_tel, jdbcType=VARCHAR} , #{policy_status, jdbcType=NUMERIC} , #{bank_account, jdbcType=VARCHAR} , #{agent_start_date, jdbcType=DATE} 
				, #{last_agent_name, jdbcType=VARCHAR} , #{finish_time, jdbcType=DATE} , #{item_id, jdbcType=NUMERIC} , #{last_agent_code, jdbcType=VARCHAR} , #{holder_gender, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{due_time, jdbcType=DATE} 
				, #{agent_relation_to_ph, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{agent_organ_code, jdbcType=NUMERIC} , #{holder_id, jdbcType=NUMERIC} , #{waiver_flag, jdbcType=NUMERIC} , #{holder_id_code, jdbcType=VARCHAR} 
				, #{holder_company_name, jdbcType=VARCHAR} , #{fail_times, jdbcType=NUMERIC} , SYSDATE , #{policy_year, jdbcType=NUMERIC} , #{bank_user_name, jdbcType=VARCHAR} , #{fee_status, jdbcType=VARCHAR} , #{holder_cert_type, jdbcType=VARCHAR} 
				, #{charge_period, jdbcType=VARCHAR} , #{handler_organ_code, jdbcType=NUMERIC} , #{bank_code, jdbcType=VARCHAR} , #{agent_code, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC} , #{fee_scene_code, jdbcType=VARCHAR} , #{arap_flag, jdbcType=VARCHAR}
				, #{business_code, jdbcType=VARCHAR}, #{service_code, jdbcType=VARCHAR}, #{product_code, jdbcType=VARCHAR}, #{special_account_flag, jdbcType=VARCHAR}, #{discount_rate, jdbcType=NUMERIC}, #{family_policy_rate_flag, jdbcType=NUMERIC},#{povre_discount_rate, jdbcType=NUMERIC} ) 
		 
		]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePrem" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PREM WHERE LIST_ID = #{list_id} ]]>
	</delete>
	
	<!-- 修改操作 续保时使用 -->
	<update id="PA_updatePremByUnitNumber" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM ]]>
		<set>
		<trim suffixOverrides=",">
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
			HANDLER_NAME = #{handler_name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE UNIT_NUMBER = #{unit_number} and BUSI_PROD_CODE = #{busi_prod_code}]]>
		<if test=" item_id  != null "><![CDATA[ AND ITEM_ID = #{item_id} ]]></if>
	</update>

<!-- 修改操作 -->
	<update id="PA_updatePrem" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM ]]>
		<set>
		<trim suffixOverrides=",">
			HOLDER_NAME = #{holder_name, jdbcType=VARCHAR} ,
			BUSI_PROD_NAME = #{busi_prod_name, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
		    PAID_COUNT = #{paid_count, jdbcType=NUMERIC} ,
			FUNDS_RTN_CODE = #{funds_rtn_code, jdbcType=VARCHAR} ,
			DUE_FEE_TYPE = #{due_fee_type, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			HOLDER_TEL = #{holder_tel, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			HANDLER_NAME = #{handler_name, jdbcType=VARCHAR} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    IS_RISK_MAIN = #{is_risk_main, jdbcType=NUMERIC} ,
			SERVICE_BANK_BRANCH_NAME = #{service_bank_branch_name, jdbcType=VARCHAR} ,
			INSURED_RELATION_TO_PH = #{insured_relation_to_ph, jdbcType=VARCHAR} ,
		    INSURED_BIRTHDAY = #{insured_birthday, jdbcType=DATE} ,
			HOLDER_CERTI_CODE = #{holder_certi_code, jdbcType=VARCHAR} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			CHANNEL_ORG_CODE = #{channel_org_code, jdbcType=VARCHAR} ,
			INSURED_NAME = #{insured_name, jdbcType=VARCHAR} ,
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
			HANDLER_PAY_MODE = #{handler_pay_mode, jdbcType=VARCHAR} ,
			SERVICE_BANK_BRANCH_CODE = #{service_bank_branch_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    IS_AGENT_LOCK = #{is_agent_lock, jdbcType=NUMERIC} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			PAY_LOCATION = #{pay_location, jdbcType=VARCHAR} ,
			INSURED_TEL = #{insured_tel, jdbcType=VARCHAR} ,
		    POLICY_STATUS = #{policy_status, jdbcType=NUMERIC} ,
			BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
		    AGENT_START_DATE = #{agent_start_date, jdbcType=DATE} ,
			LAST_AGENT_NAME = #{last_agent_name, jdbcType=VARCHAR} ,
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			LAST_AGENT_CODE = #{last_agent_code, jdbcType=VARCHAR} ,
		    HOLDER_GENDER = #{holder_gender, jdbcType=NUMERIC} ,
		    DUE_TIME = #{due_time, jdbcType=DATE} ,
			AGENT_RELATION_TO_PH = #{agent_relation_to_ph, jdbcType=VARCHAR} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    AGENT_ORGAN_CODE = #{agent_organ_code, jdbcType=NUMERIC} ,
		    HOLDER_ID = #{holder_id, jdbcType=NUMERIC} ,
		    WAIVER_FLAG = #{waiver_flag, jdbcType=NUMERIC} ,
			HOLDER_ID_CODE = #{holder_id_code, jdbcType=VARCHAR} ,
			HOLDER_COMPANY_NAME = #{holder_company_name, jdbcType=VARCHAR} ,
		    FAIL_TIMES = #{fail_times, jdbcType=NUMERIC} ,
		    POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
			BANK_USER_NAME = #{bank_user_name, jdbcType=VARCHAR} ,
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
			HOLDER_CERT_TYPE = #{holder_cert_type, jdbcType=VARCHAR} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
		    HANDLER_ORGAN_CODE = #{handler_organ_code, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR},
		    PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
		    FEE_SCENE_CODE = #{fee_scene_code, jdbcType=VARCHAR} ,
		    ARAP_FLAG = #{arap_flag, jdbcType=VARCHAR} ,
		    <if test=" special_account_flag != null and special_account_flag != '' ">
		    <![CDATA[ SPECIAL_ACCOUNT_FLAG = #{special_account_flag, jdbcType=NUMERIC}, ]]></if>
		    DISCOUNT_RATE = #{discount_rate, jdbcType=NUMERIC} ,
		    FAMILY_POLICY_RATE_FLAG = #{family_policy_rate_flag, jdbcType=NUMERIC} ,
		    POVRE_DISCOUNT_RATE = #{povre_discount_rate, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPremByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.DUE_FEE_TYPE, A.BUSI_PROD_CODE, 
			A.ORGAN_CODE, A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, 
			A.INSURED_RELATION_TO_PH, A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
			A.AMOUNT, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, A.PAY_MODE, 
			A.PAY_LOCATION, A.INSURED_TEL, A.POLICY_STATUS, A.BANK_ACCOUNT, A.AGENT_START_DATE, 
			A.LAST_AGENT_NAME, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, A.HOLDER_GENDER, A.DUE_TIME, 
			A.AGENT_RELATION_TO_PH, A.LIST_ID, A.FEE_AMOUNT, A.AGENT_ORGAN_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, 
			A.HOLDER_COMPANY_NAME, A.FAIL_TIMES, A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE, 
			A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ, A.BUSINESS_CODE,A.FEE_SCENE_CODE, A.ARAP_FLAG  ,
			A.SPECIAL_ACCOUNT_FLAG,A.DISCOUNT_RATE,A.FAMILY_POLICY_RATE_FLAG,A.POVRE_DISCOUNT_RATE
			FROM APP___PAS__DBUSER.T_PREM A WHERE 1 = 1  ]]>
		<include refid="PA_queryPremByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	
	<select id="PA_findPremByBusiUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.DUE_FEE_TYPE, A.BUSI_PROD_CODE, 
			A.ORGAN_CODE, A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, 
			A.INSURED_RELATION_TO_PH, A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
			A.AMOUNT, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, A.PAY_MODE, 
			A.PAY_LOCATION, A.INSURED_TEL, A.POLICY_STATUS, A.BANK_ACCOUNT, A.AGENT_START_DATE, 
			A.LAST_AGENT_NAME, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, A.HOLDER_GENDER, A.DUE_TIME, 
			A.AGENT_RELATION_TO_PH, A.LIST_ID, A.FEE_AMOUNT, A.AGENT_ORGAN_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, 
			A.HOLDER_COMPANY_NAME, A.FAIL_TIMES, A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE, 
			A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ, A.BUSINESS_CODE,A.FEE_SCENE_CODE,A.ARAP_FLAG  ,
			A.SPECIAL_ACCOUNT_FLAG,A.DISCOUNT_RATE,A.FAMILY_POLICY_RATE_FLAG,A.POVRE_DISCOUNT_RATE  
			FROM APP___PAS__DBUSER.T_PREM A WHERE 1 = 1  ]]>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.DUE_FEE_TYPE, A.BUSI_PROD_CODE, 
			A.ORGAN_CODE, A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, 
			A.INSURED_RELATION_TO_PH, A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
			A.AMOUNT, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, A.PAY_MODE, 
			A.PAY_LOCATION, A.INSURED_TEL, A.POLICY_STATUS, A.BANK_ACCOUNT, A.AGENT_START_DATE, 
			A.LAST_AGENT_NAME, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, A.HOLDER_GENDER, A.DUE_TIME, 
			A.AGENT_RELATION_TO_PH, A.LIST_ID, A.FEE_AMOUNT, A.AGENT_ORGAN_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, 
			A.HOLDER_COMPANY_NAME, A.FAIL_TIMES, A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE, 
			A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ, A.BUSINESS_CODE,A.FEE_SCENE_CODE,A.ARAP_FLAG  ,
			A.SPECIAL_ACCOUNT_FLAG,A.DISCOUNT_RATE,A.FAMILY_POLICY_RATE_FLAG,A.POVRE_DISCOUNT_RATE  FROM APP___PAS__DBUSER.T_PREM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作   已交保费期数-->

	<select id="PA_findAllPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.DUE_FEE_TYPE, A.BUSI_PROD_CODE, 
      A.ORGAN_CODE, A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, 
      A.INSURED_RELATION_TO_PH, A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
      A.AMOUNT, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, A.PAY_MODE, 
      A.PAY_LOCATION, A.INSURED_TEL, A.POLICY_STATUS, A.BANK_ACCOUNT, A.AGENT_START_DATE, 
      A.LAST_AGENT_NAME, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, A.HOLDER_GENDER, A.DUE_TIME, A.ARAP_FLAG,
      A.AGENT_RELATION_TO_PH, A.LIST_ID, A.FEE_AMOUNT, A.AGENT_ORGAN_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, 
      A.HOLDER_COMPANY_NAME, A.FAIL_TIMES, A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE,
      A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ, A.FEE_SCENE_CODE,A.BUSINESS_CODE,A.DISCOUNT_RATE,A.FAMILY_POLICY_RATE_FLAG  FROM APP___PAS__DBUSER.V_PREM_ALL A WHERE ROWNUM <=  1000]]>
		<include refid="PA_premWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<select id="PA_findAllPremByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT T.UNIT_NUMBER,
                           T.fee_status,
                           T.INSERT_TIME,
                           T.ARAP_FLAG,
                           T.busi_prod_code,
                           NVL(SUM(CASE
                                     WHEN T.ARAP_FLAG = 1 THEN
                                      T.FEE_AMOUNT
                                     ELSE
                                      -T.FEE_AMOUNT
                                   END),
                               0) FEE_AMOUNT
                      FROM APP___PAS__DBUSER.T_PREM T
                     where T.FEE_STATUS != '02'
                       and T.policy_code = #{policy_code}
                       and T.FINISH_TIME <= #{finish_time}
                       and T.busi_prod_code in ('00890000','00893000','00892000')
                     group by T.UNIT_NUMBER, T.fee_status, T.INSERT_TIME, T.ARAP_FLAG,T.busi_prod_code]]> 
	</select>
	
<!-- leihong -->
	<sql id="queryPremByDateCondition">
<!-- 		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if> -->
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" start_date  != null "><![CDATA[ AND A.DUE_TIME >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.DUE_TIME <= #{end_date}  ]]></if>
	</sql>	

<!--leihong  查询指定期间的个数操作 -->
	<select id="findPremTotalByDate" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM A WHERE 1 = 1  ]]>
		<include refid="queryPremByDateCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPremTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM A WHERE 1 = 1  ]]>
		<include refid="PA_premWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPremForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.HOLDER_NAME, B.BUSI_PROD_NAME, B.UNIT_NUMBER, B.PAID_COUNT, B.FUNDS_RTN_CODE, B.DUE_FEE_TYPE, B.BUSI_PROD_CODE, 
			B.ORGAN_CODE, B.HOLDER_TEL, B.HANDLER_NAME, B.CHARGE_YEAR, B.IS_RISK_MAIN, B.SERVICE_BANK_BRANCH_NAME, 
			B.INSURED_RELATION_TO_PH, B.INSURED_BIRTHDAY, B.HOLDER_CERTI_CODE, B.AGENT_NAME, B.CHANNEL_ORG_CODE, B.INSURED_NAME, 
			B.AMOUNT, B.INSURED_ID, B.HANDLER_PAY_MODE, B.SERVICE_BANK_BRANCH_CODE, B.POLICY_CODE, B.IS_AGENT_LOCK, B.PAY_MODE, 
			B.PAY_LOCATION, B.INSURED_TEL, B.POLICY_STATUS, B.BANK_ACCOUNT, B.AGENT_START_DATE, 
			B.LAST_AGENT_NAME, B.FINISH_TIME, B.ITEM_ID, B.LAST_AGENT_CODE, B.HOLDER_GENDER, B.DUE_TIME, 
			B.AGENT_RELATION_TO_PH, B.LIST_ID, B.FEE_AMOUNT, B.AGENT_ORGAN_CODE, B.HOLDER_ID, B.WAIVER_FLAG, B.HOLDER_ID_CODE, 
			B.HOLDER_COMPANY_NAME, B.FAIL_TIMES, B.POLICY_YEAR, B.BANK_USER_NAME, B.FEE_STATUS, B.HOLDER_CERT_TYPE, 
			B.CHARGE_PERIOD, B.HANDLER_ORGAN_CODE, B.BANK_CODE, B.AGENT_CODE, B.PREM_FREQ, B.BUSINESS_CODE,B.DISCOUNT_RATE,B.FAMILY_POLICY_RATE_FLAG  FROM (
					SELECT ROWNUM RN, A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.DUE_FEE_TYPE, A.BUSI_PROD_CODE, 
			A.ORGAN_CODE, A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, 
			A.INSURED_RELATION_TO_PH, A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
			A.AMOUNT, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, A.PAY_MODE, 
			A.PAY_LOCATION, A.INSURED_TEL, A.POLICY_STATUS, A.BANK_ACCOUNT, A.AGENT_START_DATE, 
			A.LAST_AGENT_NAME, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, A.HOLDER_GENDER, A.DUE_TIME, 
			A.AGENT_RELATION_TO_PH, A.LIST_ID, A.FEE_AMOUNT, A.AGENT_ORGAN_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, 
			A.HOLDER_COMPANY_NAME, A.FAIL_TIMES, A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE, 
			A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ, A.BUSINESS_CODE,A.FEE_SCENE_CODE,A.ARAP_FLAG,A.DISCOUNT_RATE,A.FAMILY_POLICY_RATE_FLAG  FROM APP___PAS__DBUSER.T_PREM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 年度业绩分红报告书查询接口  -->
	<select id="findPremPOYearBonusReport" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[ 
			select A.Amount,A.FEE_AMOUNT
			  FROM APP___PAS__DBUSER.T_PREM A
			 where rownum = 1
			   and A.Fee_Status in ('01', '19')
			   and A.POLICY_CODE = #{policy_code}
			 ORDER BY A.INSERT_TIME DESC
		
		]]> 
	</select>
	
	<!-- 年度业绩分红报告书查询 -->
	<select id="findPremYearBonusReport" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[  
				SELECT  sum(fee_amount) fee_amount ,max(PAID_COUNT) PAID_COUNT
		       FROM APP___PAS__DBUSER.t_prem a
		      where  item_id = #{item_id}
		      and a.due_time <= #{due_time}
		      order by Insert_Time desc 
		]]> 
	</select>
	
	
	<!--查询prep中所以数据  -->
	<select id="PA_queryAllPrem" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	select a.list_id,
       a.unit_number,
       a.policy_code,
       a.policy_year,
       a.busi_prod_code,
       a.busi_prod_name,
       a.is_risk_main,
       a.amount,
       a.due_fee_type,
       a.fee_amount,
       a.paid_count,
       a.prem_freq,
       a.pay_mode,
       a.pay_location,
       a.charge_period,
       a.charge_year,
       a.finish_time,
       a.due_time,
       a.organ_code,
       a.channel_org_code,
       a.holder_id,
       a.holder_name,
       a.holder_gender,
       a.holder_company_name,
       a.holder_cert_type,
       a.holder_certi_code,
       a.holder_id_code,
       a.holder_tel,
       a.agent_organ_code,
       a.agent_code,
       a.agent_name,
       a.last_agent_code,
       a.last_agent_name,
       a.agent_start_date,
       a.agent_relation_to_ph,
       a.insured_tel,
       a.insured_relation_to_ph,
       a.insured_birthday,
       a.insured_id,
       a.insured_name,
       a.service_bank_branch_code,
       a.service_bank_branch_name,
       a.bank_code,
       a.bank_account,
       a.bank_user_name,
       a.fail_times,
       a.funds_rtn_code,
       a.fee_status,
       a.policy_status,
       a.is_agent_lock,
       a.handler_name,
       a.handler_organ_code,
       a.handler_pay_mode,
       a.waiver_flag,
       a.insert_by,
       a.insert_time,
       a.insert_timestamp,
       a.update_by,
       a.update_time,
       a.update_timestamp,
       a.business_code,
       a.item_id,
       A.FEE_SCENE_CODE,
       a.arap_flag,
       a.business_code,  
       a.product_code,A.DISCOUNT_RATE,A.FAMILY_POLICY_RATE_FLAG,a.POVRE_DISCOUNT_RATE  
       from dev_pas.t_prem a WHERE ROWNUM <=  1000]]>
		<include refid="PA_premWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<!-- 查询某一时点抄单信息 -->
	<select id="PA_findAllExtraPremAF" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(TP.FEE_AMOUNT) AS FEE_AMOUNT
			  FROM DEV_PAS.T_PREM TP, DEV_PAS.T_CONTRACT_PRODUCT TCP
			 WHERE TCP.ITEM_ID = TP.ITEM_ID
			   AND (TP.DUE_FEE_TYPE = '2' OR TP.DUE_FEE_TYPE = '3')
			  AND TP.FEE_STATUS = '19'
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCP.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND TP.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND TP.DUE_TIME <= #{due_time} ]]></if>
	</select>
	
	<!-- 查询某时点的总保费 -->
	<select id="PA_findAllPremByDueTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(TP.FEE_AMOUNT) AS FEE_AMOUNT
			  FROM DEV_PAS.T_PREM TP, DEV_PAS.T_CONTRACT_PRODUCT TCP
			 WHERE TCP.ITEM_ID = TP.ITEM_ID
			   AND (TP.DUE_FEE_TYPE = '2' OR TP.DUE_FEE_TYPE = '3' OR TP.DUE_FEE_TYPE = '1')
			  AND TP.FEE_STATUS = '19'
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCP.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND TP.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND TP.DUE_TIME <= #{due_time} ]]></if>
	</select>
	<!-- 查询续期应收未收数据 -->
	<select id="PA_findOrphanPolicyNoticePrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT TP.LIST_ID,
         TP.UNIT_NUMBER,
         TP.POLICY_CODE,
         TP.POLICY_YEAR,
         TP.BUSI_PROD_CODE,
         TP.BUSI_PROD_NAME,
         TP.IS_RISK_MAIN,
         TP.ITEM_ID,
         TP.AMOUNT,
         TP.DUE_FEE_TYPE,
         TP.FEE_AMOUNT,
         TP.PAID_COUNT,
         TP.PREM_FREQ,
         TP.PAY_MODE,
         TP.PAY_LOCATION,
         TP.CHARGE_PERIOD,
         TP.CHARGE_YEAR,
         TP.FINISH_TIME,
         TP.DUE_TIME,
         TP.ORGAN_CODE,
         TP.CHANNEL_ORG_CODE,
         TP.HOLDER_ID,
         TP.HOLDER_NAME,
         TP.HOLDER_GENDER,
         TP.HOLDER_COMPANY_NAME,
         TP.HOLDER_CERT_TYPE,
         TP.HOLDER_CERTI_CODE,
         TP.HOLDER_ID_CODE,
         TP.HOLDER_TEL,
         TP.AGENT_ORGAN_CODE,
         TP.AGENT_CODE,
         TP.AGENT_NAME,
         TP.LAST_AGENT_CODE,
         TP.LAST_AGENT_NAME,
         TP.AGENT_START_DATE,
         TP.AGENT_RELATION_TO_PH,
         TP.INSURED_TEL,
         TP.INSURED_RELATION_TO_PH,
         TP.INSURED_BIRTHDAY,
         TP.INSURED_ID,
         TP.INSURED_NAME,
         TP.SERVICE_BANK_BRANCH_CODE,
         TP.SERVICE_BANK_BRANCH_NAME,
         TP.BANK_CODE,
         TP.BANK_ACCOUNT,
         TP.BANK_USER_NAME,
         TP.FAIL_TIMES,
         TP.FUNDS_RTN_CODE,
         TP.FEE_STATUS,
         TP.POLICY_STATUS,
         TP.IS_AGENT_LOCK,
         TP.HANDLER_NAME,
         TP.HANDLER_ORGAN_CODE,
         TP.HANDLER_PAY_MODE,
         TP.WAIVER_FLAG,
         TP.BUSINESS_CODE,
         TP.FEE_SCENE_CODE,
         TP.SERVICE_CODE,
         TP.ARAP_FLAG,
         TP.PRODUCT_CODE,
         TP.POVRE_DISCOUNT_RATE,
		 TP.DISCOUNT_RATE,
		 TP.FAMILY_POLICY_RATE_FLAG
    FROM APP___PAS__DBUSER.T_PREM TP
   WHERE 1 = 1
     AND TP.FEE_SCENE_CODE = 'RN'
     AND TP.FEE_STATUS IN ( '00','03')
   
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[   AND TP.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	<!-- 查询续期应收未收数据 -->
	<select id="PA_findAllPremByBusiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT TP.LIST_ID,
         TP.UNIT_NUMBER,
         TP.POLICY_CODE,
         TP.POLICY_YEAR,
         TP.BUSI_PROD_CODE,
         TP.BUSI_PROD_NAME,
         TP.IS_RISK_MAIN,
         TP.ITEM_ID,
         TP.AMOUNT,
         TP.DUE_FEE_TYPE,
         TP.FEE_AMOUNT,
         TP.PAID_COUNT,
         TP.PREM_FREQ,
         TP.PAY_MODE,
         TP.PAY_LOCATION,
         TP.CHARGE_PERIOD,
         TP.CHARGE_YEAR,
         TP.FINISH_TIME,
         TP.DUE_TIME,
         TP.ORGAN_CODE,
         TP.CHANNEL_ORG_CODE,
         TP.HOLDER_ID,
         TP.HOLDER_NAME,
         TP.HOLDER_GENDER,
         TP.HOLDER_COMPANY_NAME,
         TP.HOLDER_CERT_TYPE,
         TP.HOLDER_CERTI_CODE,
         TP.HOLDER_ID_CODE,
         TP.HOLDER_TEL,
         TP.AGENT_ORGAN_CODE,
         TP.AGENT_CODE,
         TP.AGENT_NAME,
         TP.LAST_AGENT_CODE,
         TP.LAST_AGENT_NAME,
         TP.AGENT_START_DATE,
         TP.AGENT_RELATION_TO_PH,
         TP.INSURED_TEL,
         TP.INSURED_RELATION_TO_PH,
         TP.INSURED_BIRTHDAY,
         TP.INSURED_ID,
         TP.INSURED_NAME,
         TP.SERVICE_BANK_BRANCH_CODE,
         TP.SERVICE_BANK_BRANCH_NAME,
         TP.BANK_CODE,
         TP.BANK_ACCOUNT,
         TP.BANK_USER_NAME,
         TP.FAIL_TIMES,
         TP.FUNDS_RTN_CODE,
         TP.FEE_STATUS,
         TP.POLICY_STATUS,
         TP.IS_AGENT_LOCK,
         TP.HANDLER_NAME,
         TP.HANDLER_ORGAN_CODE,
         TP.HANDLER_PAY_MODE,
         TP.WAIVER_FLAG,
         TP.BUSINESS_CODE,
         TP.FEE_SCENE_CODE,
         TP.SERVICE_CODE,
         TP.ARAP_FLAG,
         TP.PRODUCT_CODE,
		 TP.POVRE_DISCOUNT_RATE,
		 TP.DISCOUNT_RATE,
		 TP.FAMILY_POLICY_RATE_FLAG
    FROM APP___PAS__DBUSER.T_PREM TP
   WHERE 1 = 1
     AND TP.FEE_SCENE_CODE = 'RN'
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[   AND TP.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_prod_code_list  != null  and busi_prod_code_list.size() !=0 ">
			<![CDATA[ AND TP.busi_prod_code IN ]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code" index="index"
				open="(" close=")" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
		</if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND TP.UNIT_NUMBER = #{unit_number} ]]></if>
	</select>
	
	<select id="PA_findPremInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	  SELECT * FROM (
		SELECT * FROM Dev_pas.T_prem TP  
		 WHERE 1=1
		  AND TP.fee_status in ('01','19','16')
	]]>
	  <if test=" item_id  != null "><![CDATA[ AND TP.ITEM_ID = #{item_id} ]]></if>
	  <if test=" busi_prod_code  != null "><![CDATA[ AND TP.busi_prod_code = #{busi_prod_code} ]]></if>
	  <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TP.POLICY_CODE = #{policy_code} ]]></if>
	  <if test=" fee_scene_code != null and fee_scene_code != ''  "><![CDATA[ AND TP.FEE_SCENE_CODE = #{fee_scene_code} ]]></if>
	    <![CDATA[
	    		order by TP.due_time desc) where rownum = 1
	    ]]>
	</select>
	<!-- 根据保单号查询续期数据 -->
	<select id="findRNPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
			 SELECT DISTINCT A.POLICY_CODE, A.FEE_AMOUNT, A.FINISH_TIME, A.UNIT_NUMBER, A.FEE_SCENE_CODE,
			                 TP.PAY_MODE, TP.BANK_ACCOUNT, TP.ARAP_FLAG, TCM.SERVICE_BANK, TK.BANK_NAME,
			                 TCU.CUSTOMER_NAME AS HOLDER_NAME
			   FROM (SELECT TM.POLICY_CODE, SUM(TM.FEE_AMOUNT) FEE_AMOUNT, 
			                TM.FINISH_TIME, TM.UNIT_NUMBER, TM.FEE_SCENE_CODE
			           FROM APP___PAS__DBUSER.T_PREM TM
			          WHERE 1 = 1
			            AND TM.POLICY_CODE = #{policy_code}
			            AND TM.FINISH_TIME = #{finish_time} ]]>
			            
			<choose>
			<when test='service_codes!=null and service_codes.size()>0 ' >
					   AND TM.FEE_SCENE_CODE = 'CS'
					   AND TM.SERVICE_CODE IN 
				<foreach collection="service_codes" item="service_code"
					index="service_code" open="(" close=")" separator=",">#{service_code}</foreach>
			</when>
			<otherwise>
					   AND TM.FEE_SCENE_CODE = 'RN'
			</otherwise>
			</choose> 
		<![CDATA[	  GROUP BY TM.POLICY_CODE, TM.FINISH_TIME, TM.UNIT_NUMBER, TM.FEE_SCENE_CODE) A
					  INNER JOIN APP___PAS__DBUSER.T_PREM_ARAP TP         ON A.UNIT_NUMBER = TP.UNIT_NUMBER
					  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM  ON A.POLICY_CODE = TCM.POLICY_CODE
					  INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH    ON A.POLICY_CODE = TPH.POLICY_CODE
					  INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU         ON TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
					   LEFT JOIN APP___PAS__DBUSER.T_BANK TK              ON TK.BANK_CODE = TP.BANK_CODE
					  WHERE 1 = 1;
		 ]]>

	</select>
	
	<!-- 查询符合条件的的保单号 -->
		<select id="findAllCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCPC.POLICY_CODE,
       #{flag} as flag,
       SUM(TP.FEE_AMOUNT) FEE_AMOUNT,
       TCAC.VALIDATE_TIME AS FINISH_TIME ,
       TP.UNIT_NUMBER,
       TP.POLICY_YEAR,
       TP.PAY_MODE,
       TP.BANK_ACCOUNT,
       TP.ARAP_FLAG,
       TCPC.HESITATE_FLAG,
       (SELECT TK.BANK_NAME
          FROM APP___PAS__DBUSER.T_BANK TK
         WHERE TK.BANK_CODE = TP.BANK_CODE) AS BANK_NAME,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCPC.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME,
       TCBP.BUSI_ITEM_ID,
       TCBP.MASTER_BUSI_ITEM_ID
  FROM DEV_PAS.T_CS_POLICY_CHANGE TCPC
  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
    ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM
    ON TCM.POLICY_ID = TCPC.POLICY_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
    ON TCBP.POLICY_ID = TCM.POLICY_ID
   AND TCM.APPLY_DATE = TCBP.APPLY_DATE]]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
<![CDATA[JOIN DEV_PAS.V_PREM_ALL TP]]>
</if>
<if test="policy_code_list==null or policy_code_list.size()==0">
<![CDATA[JOIN DEV_PAS.T_PREM TP]]>
</if>
<![CDATA[ON TCAC.ACCEPT_CODE = TP.BUSINESS_CODE
   AND TCBP.POLICY_CODE = TP.POLICY_CODE
 WHERE TCAC.ACCEPT_STATUS = '18']]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="liability_state != null and liability_state!= ''"> <![CDATA[ AND TCM.LIABILITY_STATE = #{liability_state} ]]> </if>
    <if test="is_risk_main != null"> <![CDATA[ AND Tp.IS_RISK_MAIN = #{is_risk_main} AND TCM.LIABILITY_STATE <> 3 ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TCPC.finish_time >= #{start_date} ]]> </if>
    <if test="end_date != null"> <![CDATA[ AND TCPC.finish_time < #{end_date} ]]> </if>
    <if test='service_codes!=null and service_codes.size()>0 ' >
        <![CDATA[AND TCAC.SERVICE_CODE IN ]]>
        <foreach collection="service_codes" item="service_code"
          index="service_code" open="(" close=")" separator=",">#{service_code}</foreach>
    </if>
<![CDATA[ GROUP BY TCPC.POLICY_CODE,
          TCAC.VALIDATE_TIME,
          TP.UNIT_NUMBER,
          TP.POLICY_YEAR,
          TP.DUE_TIME,
          TP.BUSINESS_CODE,
          TP.PAY_MODE,
          TP.BANK_ACCOUNT,
          TP.ARAP_FLAG,
          TP.BANK_CODE,
          TCBP.BUSI_ITEM_ID,
          TCBP.MASTER_BUSI_ITEM_ID,
          TCPC.HESITATE_FLAG]]>
	</select>
	<!-- 查询符合条件的的保单号 -->
	<select id="findRNCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TBP.POLICY_CODE,
               #{flag} as flag,
               SUM(TM.FEE_AMOUNT) FEE_AMOUNT,
               TM.FINISH_TIME,
               TM.UNIT_NUMBER,
               TM.POLICY_YEAR,
               TM.PAY_MODE,
               TM.BANK_ACCOUNT,
               TM.ARAP_FLAG,
               (SELECT TK.BANK_NAME
                   FROM APP___PAS__DBUSER.T_BANK TK
                  WHERE TK.BANK_CODE = TM.BANK_CODE) AS BANK_NAME,
                (SELECT TCU.CUSTOMER_NAME
                  FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
                       APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                 WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
                   and TBP.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME,
               TBP.BUSI_ITEM_ID,
               TBP.MASTER_BUSI_ITEM_ID
          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
			   APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP,
			   APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,]]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
          <![CDATA[APP___PAS__DBUSER.V_PREM_ARAP_ALL TM]]>
          </if>
          <if test="policy_code_list==null or policy_code_list.size()==0">
          <![CDATA[APP___PAS__DBUSER.T_PREM_ARAP TM]]>
          </if>
          <![CDATA[
         WHERE TCM.POLICY_ID = TBP.POLICY_ID
           AND TCM.APPLY_DATE = TBP.APPLY_DATE
           AND TBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
           AND TBP.BUSI_PRD_ID = TCP.PRODUCT_ID
           AND TCP.POLICY_CODE = TM.POLICY_CODE
           AND TCP.PRODUCT_CODE = TM.PRODUCT_CODE
           AND TM.FEE_STATUS IN ('01', '16', '19')
           AND TM.BUSINESS_CODE = '4003'
           ]]>
    <if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TM.FINISH_TIME >= #{start_date} ]]> </if>
    <if test="end_date != null"> <![CDATA[ AND TM.FINISH_TIME < #{end_date} ]]> </if>
    <![CDATA[GROUP BY 
               TM.FINISH_TIME,
               TM.UNIT_NUMBER,
               TM.POLICY_YEAR,
               TM.PAY_MODE,
               TM.BANK_ACCOUNT,
               TM.ARAP_FLAG,
               TM.BANK_CODE,
               TBP.POLICY_CODE,
               TBP.BUSI_ITEM_ID,
               TBP.MASTER_BUSI_ITEM_ID]]>
	</select>
		<!-- 根据保单号查询续期数据 -->
	<select id="findAllPremForWX" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ SELECT TP.INSURED_NAME,
				       (SELECT PRODUCT_ABBR_NAME
				          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT
				         WHERE PRODUCT_CODE_SYS = TP.BUSI_PROD_CODE) AS BUSI_PROD_NAME,
				       TP.AMOUNT,
				       TP.DUE_TIME,
				       TP.FEE_AMOUNT,
				       TP.UNIT_NUMBER,
				       TP.BUSI_PROD_CODE
				  FROM APP___PAS__DBUSER.T_PREM TP
				 WHERE TP.FEE_SCENE_CODE = 'RN'
				   AND TP.FEE_STATUS IN ('00', '03')
				   AND TP.ARAP_FLAG = '1'
                   AND TO_DATE(TO_CHAR(SYSDATE,'yyyy-mm-dd'),'yyyy-mm-dd') >=  TO_DATE(TO_CHAR(TP.DUE_TIME,'yyyy-mm-dd'),'yyyy-mm-dd')
                   AND TO_DATE(TO_CHAR(SYSDATE,'yyyy-mm-dd'),'yyyy-mm-dd') <=  TO_DATE(TO_CHAR(TP.DUE_TIME + 60,'yyyy-mm-dd'),'yyyy-mm-dd')
				   AND TP.POLICY_CODE = #{policy_code}
				   order by TP.BUSI_PROD_CODE
	   ]]>
	</select>
	
	<select id="queryLastPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ SELECT MAX(TP.DUE_TIME) as DUE_TIME
				  FROM APP___PAS__DBUSER.T_PREM TP
				 WHERE TP.FEE_SCENE_CODE = 'RN'
				   AND TP.FEE_STATUS IN ('01', '19')
				   AND TP.POLICY_CODE = #{policy_code}
				   AND TP.BUSI_PROD_CODE = #{busi_prod_code}
	   ]]>
	</select>
	
	<!-- 查询最近缴费账号 -->	
	<select id="PA_findLastAccountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ 
		SELECT  B.BANK_ACCOUNT , B.POLICY_CODE ,B.FINISH_TIME ,B.BANK_CODE FROM ( SELECT A.BANK_ACCOUNT , A.POLICY_CODE ,A.FINISH_TIME ,A.BANK_CODE
          FROM DEV_PAS.V_PREM_ALL A 
         WHERE 
				   A.POLICY_CODE = #{policy_code}
           AND A.FINISH_TIME IS NOT NULL
           AND A.PAY_MODE NOT IN ('10','11','20')
           AND A.ARAP_FLAG = '1'
         ORDER BY A.FINISH_TIME DESC) B
         WHERE ROWNUM = 1 
	   ]]>
	</select>
	<!-- 查询满期终止的保单 -->
	<select id="PA_findExpirePolicyList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCM.POLICY_CODE,
       TPC.VALIDATE_TIME AS FINISH_TIME,
       #{flag} as flag,
       SUM(TM.FEE_AMOUNT) FEE_AMOUNT,
       TM.UNIT_NUMBER,
       TM.PAY_MODE,
       TM.ARAP_FLAG,
       TBP.MASTER_BUSI_ITEM_ID,
       TBP.BUSI_ITEM_ID,
       (SELECT TK.BANK_NAME
          FROM APP___PAS__DBUSER.T_BANK TK
         WHERE TK.BANK_CODE = TM.BANK_CODE) AS BANK_NAME,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME
  FROM ]]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
</if>
<if test="policy_code_list==null or policy_code_list.size()==0">
<![CDATA[dev_pas.t_policy_change tpc]]>
</if>
<![CDATA[
  LEFT JOIN APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC
  ON TPC.POLICY_CHG_ID = TPLC.POLICY_CHG_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
    ON TPC.POLICY_ID = TCM.POLICY_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP
    ON TPLC.POLICY_ID = TBP.POLICY_ID
   AND TPLC.BUSI_ITEM_ID = TBP.BUSI_ITEM_ID
   AND TCM.APPLY_DATE = TBP.APPLY_DATE
  LEFT JOIN APP___PAS__DBUSER.T_PAY_DUE TPD
    on TBP.POLICY_CODE = TPD.POLICY_CODE
   AND TBP.BUSI_ITEM_ID = TPD.BUSI_ITEM_ID]]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
          <![CDATA[left join APP___PAS__DBUSER.V_PREM_ARAP_ALL TM]]>
          </if>
          <if test="policy_code_list==null or policy_code_list.size()==0">
          <![CDATA[left join APP___PAS__DBUSER.T_PREM_ARAP TM]]>
          </if>
          <![CDATA[
    on TPD.UNIT_NUMBER = TM.UNIT_NUMBER
   AND TPD.POLICY_CODE = TM.POLICY_CODE
   AND TM.FEE_STATUS in ('01', '16', '19')
 WHERE TPC.SERVICE_CODE = 'PME'
   AND TBP.LIABILITY_STATE = 3
   AND TBP.END_CAUSE = '01'
   AND TPLC.LIABILITY_CHANGED = 3
   AND TPLC.END_CAUSE = '01']]>
    <if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TPC.FINISH_TIME >= #{start_date} ]]> </if>
		<if test="end_date != null"> <![CDATA[ AND TPC.FINISH_TIME < #{end_date} ]]> </if>
 <![CDATA[GROUP BY TPC.VALIDATE_TIME,
          TBP.POLICY_CODE,
          TBP.MASTER_BUSI_ITEM_ID,
          TBP.BUSI_ITEM_ID,
          TCM.POLICY_CODE,
          TM.UNIT_NUMBER,
          TM.PAY_MODE,
          TM.ARAP_FLAG,
          TM.BANK_CODE]]>
	</select>
	
	<!-- 查询合同成立前撤保的保单 -->
	<select id="PA_findCancellationPolicyList" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT TCM.POLICY_CODE,
                 #{flag} as flag,
               SUM(TM.FEE_AMOUNT) FEE_AMOUNT,
               TM.FINISH_TIME,
               TM.UNIT_NUMBER,
               TM.POLICY_YEAR,
               TM.PAY_MODE,
               TM.BANK_ACCOUNT,
               TM.ARAP_FLAG,
               '0' hesitate_flag,
               (SELECT TK.BANK_NAME
                   FROM APP___PAS__DBUSER.T_BANK TK
                  WHERE TK.BANK_CODE = TM.BANK_CODE) AS BANK_NAME,
               (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME,
               TBP.BUSI_ITEM_ID,
               TBP.MASTER_BUSI_ITEM_ID
          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
          APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP,
          APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,]]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
          <![CDATA[DEV_PAS.V_PREM_ARAP_ALL TM]]>
          </if>
          <if test="policy_code_list==null or policy_code_list.size()==0">
          <![CDATA[DEV_PAS.T_PREM_ARAP TM]]>
          </if>
          <![CDATA[
         WHERE TCM.POLICY_ID = TBP.POLICY_ID
           AND TCM.APPLY_DATE = TBP.APPLY_DATE
           AND TBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
           AND TBP.BUSI_PRD_ID = TCP.PRODUCT_ID
           AND TCP.POLICY_CODE = TM.POLICY_CODE
           AND TCP.PRODUCT_CODE = TM.PRODUCT_CODE
           AND TCM.LIABILITY_STATE = '3'
           AND TCM.END_CAUSE IN ('80', '15', '82')
           AND TM.FEE_STATUS IN ('01', '16', '19')
		   AND TM.BUSINESS_CODE = '1005']]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
            <![CDATA[ AND TCM.POLICY_CODE IN ]]>
              <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
          </if>
          <if test="submit_channel_list!=null and submit_channel_list.size()>0">
          <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
            <foreach collection="submit_channel_list" item="channel"
              index="channel" open="(" close=")" separator=",">#{channel}</foreach>
          </if>
          <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
          <if test="start_date != null"> <![CDATA[ AND TM.FINISH_TIME >= #{start_date} ]]> </if>
		      <if test="end_date != null"> <![CDATA[ AND TM.FINISH_TIME < #{end_date} ]]> </if>
         <![CDATA[GROUP BY TCM.POLICY_CODE,
                  TM.FEE_AMOUNT,
                  TM.FINISH_TIME,
                  TM.UNIT_NUMBER,
                  TM.POLICY_YEAR,
                  TM.DUE_TIME,
                  TM.PAY_MODE,
                  TM.BANK_ACCOUNT,
                  TM.ARAP_FLAG,
                  TM.BANK_CODE,
                  TBP.BUSI_ITEM_ID,
                  TBP.MASTER_BUSI_ITEM_ID]]>
	</select>
	
	<!-- 根据保单号查询此保单是否缴纳过续期保费  -->
	<select id="PA_findVPremByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				select  vpa.list_id
		        from APP___PAS__DBUSER.v_prem_all vpa 
		        where vpa.fee_scene_code in ('RN')
		        and vpa.arap_flag = 1
		        and vpa.fee_status in ('01','19')
			]]>	 
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND VPA.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND VPA.BANK_ACCOUNT = #{bank_account} ]]></if>
			<![CDATA[	 
			  and rownum < 2000
			]]>
	</select>
	
	<!-- 根据保单号查询此保单最近一期缴费 -->
	<select id="PA_findPremiumInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	  Select *
  From (SELECT A.Unit_Number,
               A.Busi_Prod_Code,
               A.Prem_Freq,
               A.Due_Time,
               A.Finish_Time,
               sum(A.Fee_Amount) Fee_Amount,
               A.Business_Code,
               A.ITEM_ID,
               ROW_NUMBER() OVER(PARTITION BY A.BUSI_PROD_CODE ORDER BY A.DUE_TIME DESC) RN
          FROM Dev_pas.T_prem A
           WHERE 1 = 1]]>
         
         <include refid="PA_premWhereCondition" />
         
         <![CDATA[
         Group BY A.Unit_Number,
                  A.Busi_Prod_Code,
                  A.Prem_Freq,
                  A.Due_Time,
                  A.Finish_Time,
                  A.Business_Code,
                  A.ITEM_ID
         ORDER BY A.DUE_TIME DESC) T
 where t.rn = 1
	]]>
	</select>
	
	<!-- 根据保单号及出险日期查询欠缴的续期保费  -->
	<select id="PA_findAllPremByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			Select A.POLICY_CODE,
			       a.Busi_Prod_Code,
			       a.Arap_Flag,
			       a.Item_Id,
			       SUM(A.Fee_Amount) Fee_Amount
			  From APP___PAS__DBUSER.T_Prem A
			 where 1 = 1 
		]]>	 
		<if test=" feeStatusList  != null and feeStatusList.size()!=0">
			<![CDATA[ AND A.FEE_STATUS IN ]]>
			<foreach collection ="feeStatusList"  item="feestatus" index="index" open="(" close=")" separator=",">#{feestatus}</foreach>
		</if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" fee_scene_code != null and fee_scene_code != ''  "><![CDATA[ AND A.FEE_SCENE_CODE = #{fee_scene_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME <= #{due_time} ]]></if>
		<![CDATA[
		 GROUP BY A.POLICY_CODE,
			      A.Busi_Prod_Code ,
			      A.Arap_Flag,
			      A.Item_Id
		]]>	 
	</select>
	
	<!-- 个人养老税延报送查询查询交费信息  -->
	<select id="PA_findAllPremForGRYLPolicyInfoSend" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[		SELECT A.POLICY_CODE,
						       A.ITEM_ID,
						       A.FINISH_TIME,
						       A.UNIT_NUMBER,
						       A.BUSI_PROD_CODE,
						       A.FEE_SCENE_CODE,
						       A.PREM_FREQ,
						       A.DUE_TIME,
						       MAX(A.LIST_ID) LIST_ID,
						       SUM(A.FEE_AMOUNT) FEE_AMOUNT
						  FROM DEV_PAS.T_PREM A
						 WHERE 1 = 1
						   AND A.FEE_SCENE_CODE IN ('NB', 'RN', 'CS')
						   AND A.ARAP_FLAG = '1'
						   AND A.FINISH_TIME IS NOT NULL
						   AND A.FEE_STATUS != '02'
						   AND A.ITEM_ID = #{item_id}
						   AND A.POLICY_CODE = #{policy_code} 
						 GROUP BY A.POLICY_CODE,
						          A.ITEM_ID,
						          A.FINISH_TIME,
						          A.UNIT_NUMBER,
						          A.BUSI_PROD_CODE,
						          A.FEE_SCENE_CODE,
						          A.PREM_FREQ,
						          A.DUE_TIME ]]>
	</select>
	<select id="queryLastPayDueDateForSY" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ SELECT MAX(TP.DUE_TIME) as DUE_TIME
				  FROM APP___PAS__DBUSER.T_PREM TP
				 WHERE TP.FEE_SCENE_CODE = 'RN'
				   AND TP.FEE_STATUS IN ('01', '02','19')
				   AND TP.POLICY_CODE = #{policy_code}
				   AND TP.BUSI_PROD_CODE = #{busi_prod_code}
	   ]]>
	</select>
	<select id="PA_queryPremInfosByPolicy" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
         SELECT A.BUSI_PROD_CODE,
                A.BUSI_PROD_NAME,
                A.PRODUCT_CODE,
                A.PRODUCT_NAME,
                A.DUE_TIME,
                SUM(A.FEE_AMOUNT) AS FEE_AMOUNT,
                A.FEE_STATUS,
                A.PAY_MODE,
                A.FEE_SCENE_CODE,
                A.SERVICE_CODE,
                A.UNIT_NUMBER
            FROM (SELECT PR.BUSI_PROD_CODE, /*险种代码*/
                         PR.BUSI_PROD_NAME, /*险种名称*/
                         NVL(PR.PRODUCT_CODE, CP.PRODUCT_CODE) AS PRODUCT_CODE,/*责任组代码*/
                         (SELECT PRL.PRODUCT_NAME
                            FROM DEV_PDS.T_PRODUCT_LIFE PRL
                           WHERE PRL.INTERNAL_ID = NVL(PR.PRODUCT_CODE, CP.PRODUCT_CODE)) AS PRODUCT_NAME, /*责任组名称*/
                         PR.DUE_TIME, /*保费应交日期*/
			             (CASE
			               WHEN PR.ARAP_FLAG = '1' THEN
			                PR.FEE_AMOUNT
			               ELSE
			                PR.FEE_AMOUNT * -1
			              END) AS FEE_AMOUNT, /*保费金额*/
			              (CASE
			                WHEN PR.FEE_STATUS = '16' THEN
			                 '01'
			              ELSE
			                PR.FEE_STATUS
			              END) AS FEE_STATUS, /*续期交费状态*/
			              PR.PAY_MODE, /*交费形式*/
			              PR.FEE_SCENE_CODE, /*交费类型*/
			              PR.SERVICE_CODE, /*保全项代码*/
			              PR.UNIT_NUMBER /*应收应付ID*/
			          FROM DEV_PAS.V_PREM_ALL PR
			          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
			            ON CP.ITEM_ID = PR.ITEM_ID
			         WHERE 1 = 1]]>
			         <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND PR.POLICY_CODE = #{policy_code} ]]></if>
			         <![CDATA[   AND PR.FEE_STATUS != '02' 
			           AND NOT EXISTS
			         (SELECT 1
			                  FROM DEV_PAS.V_PREM_ARAP_ALL Z
			                 WHERE Z.UNIT_NUMBER = PR.UNIT_NUMBER
			                   AND ((Z.FEE_TYPE = 'P004990000' AND
			                       Z.WITHDRAW_TYPE = '0049900000') or
			                       ((Z.FEE_TYPE = 'P004300000' OR
			                       Z.FEE_TYPE = 'G004270000') AND
			                       Z.WITHDRAW_TYPE = '0040900000')))) A
			 GROUP BY A.BUSI_PROD_CODE,
			          A.BUSI_PROD_NAME,
			          A.PRODUCT_CODE,
			          A.PRODUCT_NAME,
			          A.DUE_TIME,
			          A.FEE_STATUS,
			          A.PAY_MODE,
			          A.FEE_SCENE_CODE,
			          A.SERVICE_CODE,
			          A.UNIT_NUMBER
			 ORDER BY A.DUE_TIME, A.UNIT_NUMBER ]]>
	</select>
	<!-- 查询保单是否存在扶贫费率  -->
	<select id="PA_findAllMeetPovStandardFlagInfo" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
			SELECT C.MEET_POV_STANDARD_FLAG, B.PRODUCT_ID,A.*
			  FROM APP___PAS__DBUSER.T_PREM               A,
			       APP___PAS__DBUSER.T_CONTRACT_PRODUCT   B,
			       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
			 WHERE A.ITEM_ID = B.ITEM_ID
			   AND A.POLICY_CODE = B.POLICY_CODE
			   AND C.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			   AND C.MEET_POV_STANDARD_FLAG = '1'
			   AND A.POLICY_CODE = #{policy_code}
			   ]]>
			   <if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
			   <if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
			   <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
			   <if test=" feeStatusList  != null and feeStatusList.size()!=0">
					<![CDATA[ AND A.FEE_STATUS IN ]]>
			   <foreach collection ="feeStatusList"  item="feestatus" index="index" open="(" close=")" separator=",">#{feestatus}</foreach>
		</if>
	</select>
	<!-- 查询保单家庭单保费信息  -->
	<select id="PA_findAllFamilyInfo" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
			SELECT DECODE(T.NEWPRODUCTID, NULL, T.PRODUCT_ID, T.NEWPRODUCTID) NEW_PRODUCT_ID,
			       T.*
			  FROM (SELECT B.DISCOUNT_TYPE,
			               (SELECT D.NEW_PRODUCT_ID
			                  FROM APP___PAS__DBUSER.T_PRECONT_PRODUCT D
			                 WHERE D.ITEM_ID = A.ITEM_ID
			                   AND A.DUE_TIME = D.PRECONT_TIME
			                   AND D.PRECONT_STATUS = '0') NEWPRODUCTID,
			               B.PRODUCT_ID,
			               C.MEET_POV_STANDARD_FLAG,
			               B.BUSI_ITEM_ID,
			               A.*
			          FROM APP___PAS__DBUSER.T_PREM               A,
			               APP___PAS__DBUSER.T_CONTRACT_PRODUCT   B,
			               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
			         WHERE A.ITEM_ID = B.ITEM_ID
			           AND A.POLICY_CODE = B.POLICY_CODE
			           AND C.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			           AND A.BUSI_PROD_CODE = '00984000'
					   AND A.POLICY_CODE = #{policy_code}
			   ]]>
			   <if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
			   <if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
			   <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
			   <if test=" feeStatusList  != null and feeStatusList.size()!=0">
					<![CDATA[ AND A.FEE_STATUS IN ]]>
			   <foreach collection ="feeStatusList"  item="feestatus" index="index" open="(" close=")" separator=",">#{feestatus}</foreach>
			   </if>
			<![CDATA[ ) T ]]>
		
	</select>
	
	<!-- 查询保单家庭单费率  -->
	<select id="PA_findFamilyPolicyRateFlag" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT 1
				  FROM DEV_PAS.V_PREM_ALL           VPA,
				       DEV_PAS.T_CONTRACT_PRODUCT   CP
				 WHERE VPA.POLICY_CODE = CP.POLICY_CODE 
				   AND VPA.ITEM_ID = CP.ITEM_ID
				   AND VPA.FAMILY_POLICY_RATE_FLAG  = 1
				   AND VPA.UNIT_NUMBER = #{unit_number}
				   AND VPA.POLICY_CODE = #{policy_code}
				   AND VPA.BUSI_PROD_CODE = #{busi_prod_code}
				   AND CP.BUSI_ITEM_ID = #{busi_item_id} ]]>
	</select>
	
		<!-- 查询保单终止且终止原因为“02-理赔终止”、“09-身故终止”且终止日期为前一日的保单列表 -->
	<select id="PA_findExpirePolicyClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCM.POLICY_CODE,
       TPC.VALIDATE_TIME AS FINISH_TIME,
       #{flag} as flag,
       SUM(TM.FEE_AMOUNT) FEE_AMOUNT,
       TM.UNIT_NUMBER,
       TM.PAY_MODE,
       TBP.MASTER_BUSI_ITEM_ID,
       TBP.BUSI_ITEM_ID,
       (SELECT TK.BANK_NAME
          FROM APP___PAS__DBUSER.T_BANK TK
         WHERE TK.BANK_CODE = TM.BANK_CODE) AS BANK_NAME,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME
  FROM ]]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
</if>
<if test="policy_code_list==null or policy_code_list.size()==0">
<![CDATA[dev_pas.t_policy_change tpc]]>
</if>
<![CDATA[
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
    ON TPC.POLICY_ID = TCM.POLICY_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP
    ON TCM.POLICY_ID = TBP.POLICY_ID
   AND TCM.APPLY_DATE = TBP.APPLY_DATE
  LEFT JOIN APP___PAS__DBUSER.T_PAY_DUE TPD
    on TBP.POLICY_CODE = TPD.POLICY_CODE
   AND TBP.BUSI_ITEM_ID = TPD.BUSI_ITEM_ID]]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
          <![CDATA[left join APP___PAS__DBUSER.V_PREM_ARAP_ALL TM]]>
          </if>
          <if test="policy_code_list==null or policy_code_list.size()==0">
          <![CDATA[left join APP___PAS__DBUSER.T_PREM_ARAP TM]]>
          </if>
          <![CDATA[
    on TPD.UNIT_NUMBER = TM.UNIT_NUMBER
   AND TPD.POLICY_CODE = TM.POLICY_CODE
   AND TM.FEE_STATUS in ('01', '16', '19')
 WHERE tpc.service_code = 'CLMEND'
   and TBP.LIABILITY_STATE = 3
   AND TBP.END_CAUSE in ( '02','09')]]>
    <if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TPC.FINISH_TIME >= #{start_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TPC.FINISH_TIME < #{end_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TCM.EXPIRY_DATE = #{end_date} -1]]> </if>
 <![CDATA[GROUP BY TPC.VALIDATE_TIME,
          TBP.POLICY_CODE,
          TBP.MASTER_BUSI_ITEM_ID,
          TBP.BUSI_ITEM_ID,
          TCM.POLICY_CODE,
          TM.UNIT_NUMBER,
          TM.PAY_MODE,
          TM.BANK_CODE]]>
	</select>
	
	
	<!-- 查询保单状态为终止且终止原因为“06-贷款终止” -->
	<select id="PA_findPolicyEndLoan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCM.POLICY_CODE,
       TPC.VALIDATE_TIME AS FINISH_TIME,
       #{flag} as flag,
       SUM(TM.FEE_AMOUNT) FEE_AMOUNT,
       TM.UNIT_NUMBER,
       TM.PAY_MODE,
       TBP.MASTER_BUSI_ITEM_ID,
       TBP.BUSI_ITEM_ID,
       (SELECT TK.BANK_NAME
          FROM APP___PAS__DBUSER.T_BANK TK
         WHERE TK.BANK_CODE = TM.BANK_CODE) AS BANK_NAME,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME
  FROM ]]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
</if>
<if test="policy_code_list==null or policy_code_list.size()==0">
<![CDATA[dev_pas.t_policy_change tpc]]>
</if>
<![CDATA[
  LEFT JOIN APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC
  ON TPC.POLICY_CHG_ID = TPLC.POLICY_CHG_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
    ON TPC.POLICY_ID = TCM.POLICY_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP
    ON TPLC.POLICY_ID = TBP.POLICY_ID
   AND TPLC.BUSI_ITEM_ID = TBP.BUSI_ITEM_ID
   AND TCM.APPLY_DATE = TBP.APPLY_DATE
  LEFT JOIN APP___PAS__DBUSER.T_PAY_DUE TPD
    on TBP.POLICY_CODE = TPD.POLICY_CODE
   AND TBP.BUSI_ITEM_ID = TPD.BUSI_ITEM_ID]]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
          <![CDATA[left join APP___PAS__DBUSER.V_PREM_ARAP_ALL TM]]>
          </if>
          <if test="policy_code_list==null or policy_code_list.size()==0">
          <![CDATA[left join APP___PAS__DBUSER.T_PREM_ARAP TM]]>
          </if>
          <![CDATA[
    on TPD.UNIT_NUMBER = TM.UNIT_NUMBER
   AND TPD.POLICY_CODE = TM.POLICY_CODE
   AND TM.FEE_STATUS in ('01', '16', '19')
 WHERE TBP.LIABILITY_STATE = 3
   AND TBP.END_CAUSE = '06'
   AND TPLC.LIABILITY_CHANGED = 3
   AND TPLC.END_CAUSE = '06']]>
    <if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TPC.FINISH_TIME >= #{start_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TPC.FINISH_TIME < #{end_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TPC.FINISH_TIME = #{end_date} -1]]> </if>
 <![CDATA[GROUP BY TPC.VALIDATE_TIME,
          TBP.POLICY_CODE,
          TBP.MASTER_BUSI_ITEM_ID,
          TBP.BUSI_ITEM_ID,
          TCM.POLICY_CODE,
          TM.UNIT_NUMBER,
          TM.PAY_MODE,
          TM.BANK_CODE]]>
	</select>
	
	
	<!-- 查询保单状态为终止且终止原因为“14-超限终止”数据-->
	<select id="PA_findPolicyEndOverrun" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCM.POLICY_CODE,
       TPC.VALIDATE_TIME AS FINISH_TIME,
       #{flag} as flag,
       SUM(TM.FEE_AMOUNT) FEE_AMOUNT,
       TM.UNIT_NUMBER,
       TM.PAY_MODE,
       TBP.MASTER_BUSI_ITEM_ID,
       TBP.BUSI_ITEM_ID,
       (SELECT TK.BANK_NAME
          FROM APP___PAS__DBUSER.T_BANK TK
         WHERE TK.BANK_CODE = TM.BANK_CODE) AS BANK_NAME,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME
  FROM ]]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
</if>
<if test="policy_code_list==null or policy_code_list.size()==0">
<![CDATA[dev_pas.t_policy_change tpc]]>
</if>
<![CDATA[
  LEFT JOIN APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC
  ON TPC.POLICY_CHG_ID = TPLC.POLICY_CHG_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
    ON TPC.POLICY_ID = TCM.POLICY_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP
    ON TPLC.POLICY_ID = TBP.POLICY_ID
   AND TPLC.BUSI_ITEM_ID = TBP.BUSI_ITEM_ID
   AND TCM.APPLY_DATE = TBP.APPLY_DATE
  LEFT JOIN APP___PAS__DBUSER.T_PAY_DUE TPD
    on TBP.POLICY_CODE = TPD.POLICY_CODE
   AND TBP.BUSI_ITEM_ID = TPD.BUSI_ITEM_ID]]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
          <![CDATA[left join APP___PAS__DBUSER.V_PREM_ARAP_ALL TM]]>
          </if>
          <if test="policy_code_list==null or policy_code_list.size()==0">
          <![CDATA[left join APP___PAS__DBUSER.T_PREM_ARAP TM]]>
          </if>
          <![CDATA[
    on TPD.UNIT_NUMBER = TM.UNIT_NUMBER
   AND TPD.POLICY_CODE = TM.POLICY_CODE
   AND TM.FEE_STATUS in ('01', '16', '19')
 WHERE TBP.LIABILITY_STATE = 3
   AND TBP.END_CAUSE = '14'
   AND TPLC.LIABILITY_CHANGED = 3
   AND TPLC.END_CAUSE = '14']]>
    <if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TPC.FINISH_TIME >= #{start_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TPC.FINISH_TIME < #{end_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TPC.FINISH_TIME = #{end_date} -1]]> </if>
 <![CDATA[GROUP BY TPC.VALIDATE_TIME,
          TBP.POLICY_CODE,
          TBP.MASTER_BUSI_ITEM_ID,
          TBP.BUSI_ITEM_ID,
          TCM.POLICY_CODE,
          TM.UNIT_NUMBER,
          TM.PAY_MODE,
          TM.BANK_CODE]]>
	</select>
	
	
	<!-- 查询终止原因为“04-转换终止、07-失效终止、10-贷款预终止、12-贷款中止、13-自垫预终止、08-自垫终止、11-永久失效、99-其他终止、81-银保通冲正撤单终止的保单-->
	<select id="PA_findPolicyEndOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCM.POLICY_CODE,
       TPC.VALIDATE_TIME AS FINISH_TIME,
       #{flag} as flag,
       SUM(TM.FEE_AMOUNT) FEE_AMOUNT,
       TM.UNIT_NUMBER,
       TM.PAY_MODE,
       TBP.MASTER_BUSI_ITEM_ID,
       TBP.BUSI_ITEM_ID,
       (SELECT TK.BANK_NAME
          FROM APP___PAS__DBUSER.T_BANK TK
         WHERE TK.BANK_CODE = TM.BANK_CODE) AS BANK_NAME,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.POLICY_CODE = TPH.POLICY_CODE) AS HOLDER_NAME
  FROM ]]>
<if test="policy_code_list!=null and policy_code_list.size()>0">
<![CDATA[dev_pas.V_POLICY_CHANGE_ALL tpc]]>
</if>
<if test="policy_code_list==null or policy_code_list.size()==0">
<![CDATA[dev_pas.t_policy_change tpc]]>
</if>
<![CDATA[
  LEFT JOIN APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC
  ON TPC.POLICY_CHG_ID = TPLC.POLICY_CHG_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
    ON TPC.POLICY_ID = TCM.POLICY_ID
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP
    ON TPLC.POLICY_ID = TBP.POLICY_ID
   AND TPLC.BUSI_ITEM_ID = TBP.BUSI_ITEM_ID
   AND TCM.APPLY_DATE = TBP.APPLY_DATE
  LEFT JOIN APP___PAS__DBUSER.T_PAY_DUE TPD
    on TBP.POLICY_CODE = TPD.POLICY_CODE
   AND TBP.BUSI_ITEM_ID = TPD.BUSI_ITEM_ID]]>
          <if test="policy_code_list!=null and policy_code_list.size()>0">
          <![CDATA[left join APP___PAS__DBUSER.V_PREM_ARAP_ALL TM]]>
          </if>
          <if test="policy_code_list==null or policy_code_list.size()==0">
          <![CDATA[left join APP___PAS__DBUSER.T_PREM_ARAP TM]]>
          </if>
          <![CDATA[
    on TPD.UNIT_NUMBER = TM.UNIT_NUMBER
   AND TPD.POLICY_CODE = TM.POLICY_CODE
   AND TM.FEE_STATUS in ('01', '16', '19')
 WHERE TBP.LIABILITY_STATE = 3
   AND TBP.END_CAUSE in ('04', '07', '10',  '12', '13', '08', '11', '99', '81')]]>
    <if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TPC.FINISH_TIME >= #{start_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TPC.FINISH_TIME < #{end_date} ]]> </if>
	<if test="end_date != null"> <![CDATA[ AND TCM.EXPIRY_DATE = #{end_date} -1]]> </if>
 <![CDATA[GROUP BY TPC.VALIDATE_TIME,
          TBP.POLICY_CODE,
          TBP.MASTER_BUSI_ITEM_ID,
          TBP.BUSI_ITEM_ID,
          TCM.POLICY_CODE,
          TM.UNIT_NUMBER,
          TM.PAY_MODE,
          TM.BANK_CODE]]>
	</select>
	<!-- 查询短期险在途应收保费（应缴日在投保一年 -->
	<select id="PA_findShortRiskDueAmount" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT A.FEE_AMOUNT FROM DEV_PAS.T_PREM A 
				WHERE A.POLICY_CODE= #{policy_code}
				AND A.BUSI_PROD_CODE=#{busi_prod_code}
				AND A.DUE_TIME>#{start_date}-1
				AND A.DUE_TIME<#{end_date}+1 
				AND A.FEE_STATUS = '04']]>
	</select>
	<!-- 查询保单实际缴纳保费信息  -->
	<select id="PA_findAllPremAfInfo" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
			   	SELECT A.PRODUCT_NAME_SYS,
		           A.PRODUCT_NAME,
		           A.DUE_TIME,
		           SUM(A.FEE_AMOUNT) AS FEE_AMOUNT,
		           A.FEE_STATUS,
		           A.PAY_MODE,
		           A.FEE_SCENE_CODE,
		           A.SERVICE_CODE,
		           A.UNIT_NUMBER,
		           A.ORDER_ID
		      FROM (SELECT PR.BUSI_PROD_CODE AS PRODUCT_NAME_SYS /*险种代码*/,
		                   (SELECT PRL.PRODUCT_NAME
		                      FROM DEV_PDS.T_PRODUCT_LIFE PRL
		                     WHERE PRL.INTERNAL_ID =
		                           NVL(PR.PRODUCT_CODE, CP.PRODUCT_CODE)) AS PRODUCT_NAME,/*责任组名称*/
		                   PR.DUE_TIME, /*保费应交日期*/
		                   (CASE
		                     WHEN PR.ARAP_FLAG = '1' THEN
		                      PR.FEE_AMOUNT
		                     ELSE
		                      PR.FEE_AMOUNT * -1
		                   END) AS FEE_AMOUNT, /*保费金额*/
		                   
		                   (CASE 
		                      WHEN PR.FEE_STATUS = '16' THEN
		                        '01'
		                      ELSE
		                        PR.FEE_STATUS
		                      END)AS FEE_STATUS, /*续期交费状态*/
		                   PR.PAY_MODE, /*交费形式*/
		                   PR.FEE_SCENE_CODE, /*交费类型*/
		                   PR.SERVICE_CODE, /*保全项代码*/
		                   PR.UNIT_NUMBER, /*应收应付ID*/
		                   (CASE
		                     WHEN PR.FEE_SCENE_CODE = 'CS' AND PR.SERVICE_CODE = 'XQ'
		                       THEN '2'
		                     ELSE
		                       '1'
		                    END) AS ORDER_ID
		              FROM DEV_PAS.V_PREM_ALL PR
		              LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
		                ON CP.ITEM_ID = PR.ITEM_ID                                      
		             WHERE 1 = 1
		               AND PR.FEE_STATUS != '02'
		               AND PR.POLICY_CODE = #{policy_code}
		               AND NOT EXISTS (
		                       SELECT 1 FROM DEV_PAS.V_PREM_ARAP_ALL Z WHERE Z.UNIT_NUMBER=PR.UNIT_NUMBER 
		                       AND ((Z.FEE_TYPE='P004990000' AND Z.WITHDRAW_TYPE='0049900000') OR (( Z.FEE_TYPE='P004300000' OR Z.FEE_TYPE='G004270000') AND Z.WITHDRAW_TYPE='0040900000')))                       ) A
		     GROUP BY A.PRODUCT_NAME_SYS,
		              A.PRODUCT_NAME,
		              A.DUE_TIME,
		              A.FEE_STATUS,
		              A.PAY_MODE,
		              A.FEE_SCENE_CODE,
		              A.SERVICE_CODE,
		              A.UNIT_NUMBER,
		              A.ORDER_ID
		     ORDER BY A.DUE_TIME, A.ORDER_ID, A.UNIT_NUMBER
	   ]]>
	</select>
	
	<!-- 查询累计缴费金额（剔除新增附加险）与最近缴费日期 -->
	<select id="PA_findRenewalPayAmountAndFinishTime" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT A.POLICY_CODE,
       MAX(A.FINISH_TIME) AS FINISH_TIME,
       SUM(CASE
             WHEN A.ARAP_FLAG = '1' THEN
              A.FEE_AMOUNT
             ELSE
              A.FEE_AMOUNT * -1
           END) AS RENEWAL_PAY_AMOUNT /*累计缴费金额*/
  FROM DEV_PAS.V_PREM_ALL           A,
       DEV_PAS.T_CONTRACT_MASTER    B,
       DEV_PAS.T_CONTRACT_BUSI_PROD C,
       DEV_PAS.T_CONTRACT_PRODUCT   D
 WHERE B.POLICY_CODE = C.POLICY_CODE
   AND B.APPLY_DATE = C.APPLY_DATE
   AND C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
   AND D.ITEM_ID = A.ITEM_ID
   AND A.FINISH_TIME IS NOT NULL
   AND (A.FEE_SCENE_CODE IN ('RN', 'NB') OR
       (A.FEE_SCENE_CODE = 'CS' AND
       A.SERVICE_CODE IN ('PA', 'PT', 'AM', 'RE', 'SR')))
   AND A.POLICY_CODE = #{policy_code}
		 GROUP BY A.POLICY_CODE]]>
	</select>
	
	
	<select id="queryLastPayDueDateForPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[ select max(A.DUE_TIME) as DUE_TIME
				  from DEV_PAS.V_PREM_ALL A
				 WHERE A.POLICY_CODE = #{policy_code}
				   AND A.FEE_SCENE_CODE = 'RN'
				   AND A.Finish_Time is not null
				   AND NOT EXISTS (select 1
				          from DEV_PAS.V_PREM_ARAP_ALL T
				         WHERE T.POLICY_CODE = A.POLICY_CODE
				           AND T.ROLLBACK_UNIT_NUMBER = A.UNIT_NUMBER
				           AND T.SERVICE_CODE = 'XQ')
	   	]]>
	</select>
	
	
</mapper>
