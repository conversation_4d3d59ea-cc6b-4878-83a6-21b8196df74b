<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="policyStrategyLog">
<!--
	<sql id="PA_policyStrategyLogWhereCondition">
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" begin_date  != null  and  begin_date  != ''  "><![CDATA[ AND A.BEGIN_DATE = #{begin_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" strgy_freq_type  != null "><![CDATA[ AND A.STRGY_FREQ_TYPE = #{strgy_freq_type} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" invested_duration  != null "><![CDATA[ AND A.INVESTED_DURATION = #{invested_duration} ]]></if>
		<if test=" ref_investment_amt  != null "><![CDATA[ AND A.REF_INVESTMENT_AMT = #{ref_investment_amt} ]]></if>
		<if test=" strategy_code != null and strategy_code != ''  "><![CDATA[ AND A.STRATEGY_CODE = #{strategy_code} ]]></if>
		<if test=" ref_fund_price  != null "><![CDATA[ AND A.REF_FUND_PRICE = #{ref_fund_price} ]]></if>
		<if test=" assign_rate  != null "><![CDATA[ AND A.ASSIGN_RATE = #{assign_rate} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" strgy_dca_type != null and strgy_dca_type != ''  "><![CDATA[ AND A.STRGY_DCA_TYPE = #{strgy_dca_type} ]]></if>
		<if test=" target_fund_code != null and target_fund_code != ''  "><![CDATA[ AND A.TARGET_FUND_CODE = #{target_fund_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyStrategyLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyStrategyLogByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyStrategyLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG(
				INSERT_TIME, END_DATE, BEGIN_DATE, UPDATE_TIME, ITEM_ID, STATUS, STRGY_FREQ_TYPE, 
				INSERT_TIMESTAMP, LOG_ID, INVESTED_DURATION, REF_INVESTMENT_AMT, UPDATE_BY, STRATEGY_CODE, REF_FUND_PRICE, 
				ASSIGN_RATE, LIST_ID, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, POLICY_ID, 
				STRGY_DCA_TYPE, TARGET_FUND_CODE ) 
			VALUES (
				SYSDATE, #{end_date, jdbcType=DATE} , #{begin_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{status, jdbcType=VARCHAR} , #{strgy_freq_type, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{invested_duration, jdbcType=NUMERIC} , #{ref_investment_amt, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{strategy_code, jdbcType=VARCHAR} , #{ref_fund_price, jdbcType=NUMERIC} 
				, #{assign_rate, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{strgy_dca_type, jdbcType=VARCHAR} , #{target_fund_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyStrategyLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG WHERE 1 = 1 ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyStrategyLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    END_DATE = #{end_date, jdbcType=DATE} ,
		    BEGIN_DATE = #{begin_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			STATUS = #{status, jdbcType=VARCHAR} ,
		    STRGY_FREQ_TYPE = #{strgy_freq_type, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    INVESTED_DURATION = #{invested_duration, jdbcType=NUMERIC} ,
		    REF_INVESTMENT_AMT = #{ref_investment_amt, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			STRATEGY_CODE = #{strategy_code, jdbcType=VARCHAR} ,
		    REF_FUND_PRICE = #{ref_fund_price, jdbcType=NUMERIC} ,
		    ASSIGN_RATE = #{assign_rate, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			STRGY_DCA_TYPE = #{strgy_dca_type, jdbcType=VARCHAR} ,
			TARGET_FUND_CODE = #{target_fund_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyStrategyLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BEGIN_DATE, A.ITEM_ID, A.STATUS, A.STRGY_FREQ_TYPE, 
			A.LOG_ID, A.INVESTED_DURATION, A.REF_INVESTMENT_AMT, A.STRATEGY_CODE, A.REF_FUND_PRICE, 
			A.ASSIGN_RATE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.STRGY_DCA_TYPE, A.TARGET_FUND_CODE FROM APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyStrategyLogByLogIdCondition" />
	</select>
	
	<select id="PA_findPolicyStrategyLogByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BEGIN_DATE, A.ITEM_ID, A.STATUS, A.STRGY_FREQ_TYPE, 
			A.LOG_ID, A.INVESTED_DURATION, A.REF_INVESTMENT_AMT, A.STRATEGY_CODE, A.REF_FUND_PRICE, 
			A.ASSIGN_RATE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.STRGY_DCA_TYPE, A.TARGET_FUND_CODE FROM APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyStrategyLogByItemIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyStrategyLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BEGIN_DATE, A.ITEM_ID, A.STATUS, A.STRGY_FREQ_TYPE, 
			A.LOG_ID, A.INVESTED_DURATION, A.REF_INVESTMENT_AMT, A.STRATEGY_CODE, A.REF_FUND_PRICE, 
			A.ASSIGN_RATE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.STRGY_DCA_TYPE, A.TARGET_FUND_CODE FROM APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyStrategyLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BEGIN_DATE, A.ITEM_ID, A.STATUS, A.STRGY_FREQ_TYPE, 
			A.LOG_ID, A.INVESTED_DURATION, A.REF_INVESTMENT_AMT, A.STRATEGY_CODE, A.REF_FUND_PRICE, 
			A.ASSIGN_RATE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.STRGY_DCA_TYPE, A.TARGET_FUND_CODE FROM APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyStrategyLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyStrategyLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.END_DATE, B.BEGIN_DATE, B.ITEM_ID, B.STATUS, B.STRGY_FREQ_TYPE, 
			B.LOG_ID, B.INVESTED_DURATION, B.REF_INVESTMENT_AMT, B.STRATEGY_CODE, B.REF_FUND_PRICE, 
			B.ASSIGN_RATE, B.LIST_ID, B.LOG_TYPE, B.POLICY_CHG_ID, B.POLICY_ID, 
			B.STRGY_DCA_TYPE, B.TARGET_FUND_CODE FROM (
					SELECT ROWNUM RN, A.END_DATE, A.BEGIN_DATE, A.ITEM_ID, A.STATUS, A.STRGY_FREQ_TYPE, 
			A.LOG_ID, A.INVESTED_DURATION, A.REF_INVESTMENT_AMT, A.STRATEGY_CODE, A.REF_FUND_PRICE, 
			A.ASSIGN_RATE, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.STRGY_DCA_TYPE, A.TARGET_FUND_CODE FROM APP___PAS__DBUSER.T_POLICY_STRATEGY_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
