<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISaleComDao">
<!--
	<sql id="PA_saleComWhereCondition">
		<if test=" sale_com_state != null and sale_com_state != ''  "><![CDATA[ AND A.SALE_COM_STATE = #{sale_com_state} ]]></if>
		<if test=" sale_com_name != null and sale_com_name != ''  "><![CDATA[ AND A.SALE_COM_NAME = #{sale_com_name} ]]></if>
		<if test=" sale_com_sort != null and sale_com_sort != ''  "><![CDATA[ AND A.SALE_COM_SORT = #{sale_com_sort} ]]></if>
		<if test=" sale_com_code != null and sale_com_code != ''  "><![CDATA[ AND A.SALE_COM_CODE = #{sale_com_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySaleComBySaleComCodeCondition">
		<if test=" sale_com_code != null and sale_com_code != '' "><![CDATA[ AND A.SALE_COM_CODE = #{sale_com_code} ]]></if>
		<if test=" sale_com_state != null and sale_com_state != '' "><![CDATA[ AND A.SALE_COM_STATE = #{sale_com_state} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSaleCom"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SALE_COM(
				INSERT_TIMESTAMP, SALE_COM_STATE, UPDATE_BY, SALE_COM_NAME, INSERT_TIME, SALE_COM_SORT, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, SALE_COM_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{sale_com_state, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{sale_com_name, jdbcType=VARCHAR} , SYSDATE , #{sale_com_sort, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{sale_com_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSaleCom" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_SALE_COM WHERE SALE_COM_CODE = #{sale_com_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSaleCom" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SALE_COM ]]>
		<set>
		<trim suffixOverrides=",">
			SALE_COM_STATE = #{sale_com_state, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			SALE_COM_NAME = #{sale_com_name, jdbcType=VARCHAR} ,
			SALE_COM_SORT = #{sale_com_sort, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE SALE_COM_CODE = #{sale_com_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSaleComBySaleComCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SALE_COM_STATE, A.SALE_COM_NAME, A.SALE_COM_SORT, 
			A.SALE_COM_CODE,A.INSERT_BY,A.INSERT_TIME,A.UPDATE_BY,A.UPDATE_TIME FROM APP___PAS__DBUSER.T_SALE_COM A WHERE 1 = 1  ]]>
		<include refid="PA_querySaleComBySaleComCodeCondition" />
		<![CDATA[ ORDER BY A.SALE_COM_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSaleCom" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SALE_COM_STATE, A.SALE_COM_NAME, A.SALE_COM_SORT, 
			A.SALE_COM_CODE FROM T_SALE_COM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALE_COM_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSaleCom" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SALE_COM_STATE, A.SALE_COM_NAME, A.SALE_COM_SORT, 
			A.SALE_COM_CODE FROM T_SALE_COM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALE_COM_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSaleComTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_SALE_COM A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySaleComForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SALE_COM_STATE, B.SALE_COM_NAME, B.SALE_COM_SORT, 
			B.SALE_COM_CODE FROM (
					SELECT ROWNUM RN, A.SALE_COM_STATE, A.SALE_COM_NAME, A.SALE_COM_SORT, 
			A.SALE_COM_CODE FROM T_SALE_COM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SALE_COM_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
