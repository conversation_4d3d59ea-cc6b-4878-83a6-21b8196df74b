<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.RenewCollectionOffsetCompDaoImpl">

	<!-- 查询条件 -->

	<!-- 保单续期实收记录的查询 niuyu_wb 2014-12-12 -->
	<select id="PA_queryPaidUpRenewRecord" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT TPA.POLICY_ID, --保单号
				       TC.POLICY_CODE,
				       TC.ORGAN_CODE, --管理机构
				       TPA.FEE_AMOUNT, --应收金额
				       TC.PREM_PURPOSE, --收费项目
				       TPA.DUE_TIME, --应收日期
				       TC.FINISH_TIME, --实收日期
				       TC.CHECK_ENTER_TIME, --财务到账日期
				       TC.PAY_MODE, --交款方式
				       TC.BANK_CODE, --银行代码
				       TC.BANK_ACCOUNT, --银行帐号
				       HOLDER.CUSTOMER_NAME  HOLDER_NAME, --投保人姓名
				       INSURED.CUSTOMER_NAME INSURED_NAME --被保人姓名
				  FROM APP___PAS__DBUSER.T_CASH TC
				  LEFT JOIN APP___PAS__DBUSER.T_PREM_ARAP TPA
				    ON TPA.UNIT_NUMBER = TC.UNIT_NUMBER
				  LEFT JOIN (SELECT TCUSTM.CUSTOMER_NAME, TPH.POLICY_ID
				               FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
				               LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TCUSTM
				                 ON TPH.CUSTOMER_ID = TCUSTM.CUSTOMER_ID) HOLDER
				    ON TC.POLICY_ID = HOLDER.POLICY_ID
				  LEFT JOIN (SELECT TCUSTM.CUSTOMER_NAME, TIL.POLICY_ID
				               FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
				               LEFT APP___PAS__DBUSER.JOIN T_CUSTOMER TCUSTM
				                 ON TIL.CUSTOMER_ID = TCUSTM.CUSTOMER_ID) INSURED
				    ON TC.POLICY_ID = INSURED.POLICY_ID
				 WHERE 1 = 1
				    AND TPA.POLICY_CODE = #{policy_code} ]]>
	</select>

	<!-- 查询个数操作 niuyu_wb 2014-12-12 -->
	<select id="PA_findPaidUpRenewRecordTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) 
					FROM(SELECT TPA.POLICY_ID, --保单号
				       TC.POLICY_CODE,
				       TC.ORGAN_CODE, --管理机构
				       TPA.FEE_AMOUNT, --应收金额
				       TC.PREM_PURPOSE, --收费项目
				       TPA.DUE_TIME, --应收日期
				       TC.FINISH_TIME, --实收日期
				       TC.CHECK_ENTER_TIME, --财务到账日期
				       TC.PAY_MODE, --交款方式
				       TC.BANK_CODE, --银行代码
				       TC.BANK_ACCOUNT, --银行帐号
				       HOLDER.CUSTOMER_NAME  HOLDER_NAME, --投保人姓名
				       INSURED.CUSTOMER_NAME INSURED_NAME --被保人姓名
				  FROM APP___PAS__DBUSER.T_CASH TC
				  LEFT APP___PAS__DBUSER.JOIN T_PREM_ARAP TPA
				    ON TPA.UNIT_NUMBER = TC.UNIT_NUMBER
				  LEFT JOIN (SELECT TCUSTM.CUSTOMER_NAME, TPH.POLICY_ID
				               FROM T_POLICY_HOLDER TPH
				               LEFT JOIN T_CUSTOMER TCUSTM
				                 ON TPH.CUSTOMER_ID = TCUSTM.CUSTOMER_ID) HOLDER
				    ON TC.POLICY_ID = HOLDER.POLICY_ID
				  LEFT JOIN (SELECT TCUSTM.CUSTOMER_NAME, TIL.POLICY_ID
				               FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
				               LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TCUSTM
				                 ON TIL.CUSTOMER_ID = TCUSTM.CUSTOMER_ID) INSURED
				    ON TC.POLICY_ID = INSURED.POLICY_ID
				 WHERE 1 = 1
				    AND TPA.POLICY_CODE = #{policy_code})B]]>
	</select>

	<!-- 分页查询操作 niuyu_wb 2014-12-12 -->
	<select id="PA_queryPaidUpRenewRecordForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT B.RN AS ROWNUMBER,B.POLICY_ID,B.POLICY_CODE,B.ORGAN_CODE,B.FEE_AMOUNT,
				    B.PREM_PURPOSE,B.DUE_TIME,B.FINISH_TIME,B.CHECK_ENTER_TIME,B.PAY_MODE,
				    B.BANK_CODE,B.BANK_ACCOUNT,B.HOLDER_NAME,B.INSURED_NAME
				  FROM (SELECT ROWNUM RN,TPA.POLICY_ID, --保单号
						       TC.POLICY_CODE,
						       TC.ORGAN_CODE, --管理机构
						       TPA.FEE_AMOUNT, --应收金额
						       TC.PREM_PURPOSE, --收费项目
						       TPA.DUE_TIME, --应收日期
						       TC.FINISH_TIME, --实收日期
						       TC.CHECK_ENTER_TIME, --财务到账日期
						       TC.PAY_MODE, --交款方式
						       TC.BANK_CODE, --银行代码
						       TC.BANK_ACCOUNT, --银行帐号
						       HOLDER.CUSTOMER_NAME  HOLDER_NAME, --投保人姓名
						       INSURED.CUSTOMER_NAME INSURED_NAME --被保人姓名
						  FROM APP___PAS__DBUSER.T_CASH TC
						  LEFT JOIN APP___PAS__DBUSER.T_PREM_ARAP TPA
						    ON TPA.UNIT_NUMBER = TC.UNIT_NUMBER
						  LEFT JOIN (SELECT TCUSTM.CUSTOMER_NAME, TPH.POLICY_ID
						               FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
						               LEFT APP___PAS__DBUSER.JOIN T_CUSTOMER TCUSTM
						                 ON TPH.CUSTOMER_ID = TCUSTM.CUSTOMER_ID) HOLDER
						    ON TC.POLICY_ID = HOLDER.POLICY_ID
						  LEFT JOIN (SELECT TCUSTM.CUSTOMER_NAME, TIL.POLICY_ID
						               FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
						               LEFT APP___PAS__DBUSER.JOIN T_CUSTOMER TCUSTM
						                 ON TIL.CUSTOMER_ID = TCUSTM.CUSTOMER_ID) INSURED
						    ON TC.POLICY_ID = INSURED.POLICY_ID
						 WHERE 1 = 1
				           AND TPA.POLICY_CODE = #{policy_code}
				           AND ROWNUM <= #{LESS_NUM}) B
				 WHERE B.RN > #{GREATER_NUM}]]>
	</select>

	<!-- 查询待冲正记录的实收日期后发生过其他实收付行为 -->
	<select id="PA_queryAfterPaidUpRecord" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT count(1)
       				FROM APP___PAS__DBUSER.T_PREM_ARAP TPA
            			WHERE 1 = 1
                  			AND TPA.POLICY_id = #{policy_id}
                  			AND TPA.LIST_ID = #{list_id}
                  			--AND receivedTime > #{receivedTime}]]>
	</select>


</mapper>
