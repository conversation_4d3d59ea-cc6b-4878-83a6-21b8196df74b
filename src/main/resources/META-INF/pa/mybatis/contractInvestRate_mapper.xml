<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractInvestRate">

	<sql id="PA_contractInvestRateWhereCondition">
		<if test=" item_id  != null and item_id != '' "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" busi_item_id  != null and Busi_item_id != '' "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" prem_type != null and prem_type != ''  "><![CDATA[ AND A.PREM_TYPE = #{prem_type} ]]></if>
		<if test=" list_id  != null and list_id != ''"><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" assign_rate  != null "><![CDATA[ AND A.ASSIGN_RATE = #{assign_rate} ]]></if>
		<if test=" low_ply_year  != null "><![CDATA[ AND A.LOW_PLY_YEAR = #{low_ply_year} ]]></if>
		<if test=" high_ply_year  != null "><![CDATA[ AND A.HIGH_PLY_YEAR = #{high_ply_year} ]]></if>
		<if test=" policy_id  != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractInvestRateByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractInvestRateByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractInvestRate"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_EXTEND__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE(
				INSERT_TIME, ITEM_ID, UPDATE_TIME, ACCOUNT_CODE, INSERT_TIMESTAMP, UPDATE_BY, PREM_TYPE, 
				LIST_ID, ASSIGN_RATE, LOW_PLY_YEAR, UPDATE_TIMESTAMP, HIGH_PLY_YEAR, INSERT_BY, POLICY_ID,BUSI_ITEM_ID ) 
			VALUES (
				SYSDATE, #{item_id, jdbcType=NUMERIC} , SYSDATE , #{account_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{prem_type, jdbcType=VARCHAR} 
				, #{list_id, jdbcType=NUMERIC} , #{assign_rate, jdbcType=NUMERIC} , #{low_ply_year, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{high_ply_year, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractInvestRate" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE WHERE LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractInvestRate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE ]]>
		<set>
		<trim suffixOverrides=",">
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			PREM_TYPE = #{prem_type, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    ASSIGN_RATE = #{assign_rate, jdbcType=NUMERIC} ,
		    LOW_PLY_YEAR = #{low_ply_year, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    HIGH_PLY_YEAR = #{high_ply_year, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id}]]>
	</update>


	<update id="PA_updatecontractInvestRateTwo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE ]]>
		<set>
		<trim suffixOverrides=",">
			GURNT_START_DATE = #{gurnt_start_date, jdbcType=DATE} ,
			GURNT_PERIOD = #{gurnt_period, jdbcType=NUMERIC} ,
			SETTLE_METHOD = #{settle_method, jdbcType=NUMERIC} ,
			GURNT_RATE = #{gurnt_rate, jdbcType=NUMERIC} ,
			GURNT_PERD_TYPE = #{gurnt_perd_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} AND POLICY_ID = #{policy_id, jdbcType=NUMERIC} ]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="PA_findContractInvestRateByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.PREM_TYPE,A.BUSI_ITEM_ID ,A.GURNT_START_DATE,A.GURNT_PERIOD,A.SETTLE_METHOD,A.GURNT_RATE,A.GURNT_PERD_TYPE,
			A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.HIGH_PLY_YEAR, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A WHERE 1 = 1  ]]>
		<include refid="PA_contractInvestRateWhereCondition" />
	</select>
	
	<!-- 查询 zhulh -->
		<select id="PA_findContractInvestRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.PREM_TYPE, A.BUSI_ITEM_ID,A.GURNT_START_DATE,A.GURNT_PERIOD,A.SETTLE_METHOD,A.GURNT_RATE,A.GURNT_PERD_TYPE,
			A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.HIGH_PLY_YEAR, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A WHERE 1 = 1  ]]>
		<include refid="PA_contractInvestRateWhereCondition" />
	</select>
	
	<select id="PA_findContractInvestRateByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.PREM_TYPE, A.BUSI_ITEM_ID,A.GURNT_START_DATE,A.GURNT_PERIOD,A.SETTLE_METHOD,A.GURNT_RATE,A.GURNT_PERD_TYPE,
			A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.HIGH_PLY_YEAR, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestRateByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractInvestRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.PREM_TYPE, A.BUSI_ITEM_ID,A.GURNT_START_DATE,A.GURNT_PERIOD,A.SETTLE_METHOD,A.GURNT_RATE,A.GURNT_PERD_TYPE,
			A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.HIGH_PLY_YEAR, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractInvestRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.PREM_TYPE, A.BUSI_ITEM_ID,A.GURNT_START_DATE,A.GURNT_PERIOD,A.SETTLE_METHOD,A.GURNT_RATE,A.GURNT_PERD_TYPE,
			A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.HIGH_PLY_YEAR, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractInvestRateWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractInvestRateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractInvestRateForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ITEM_ID, B.ACCOUNT_CODE, B.PREM_TYPE, B.GURNT_START_DATE,B.GURNT_PERIOD,B.SETTLE_METHOD,B.GURNT_RATE,B.GURNT_PERD_TYPE,
			B.LIST_ID, B.ASSIGN_RATE, B.LOW_PLY_YEAR, B.HIGH_PLY_YEAR, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ITEM_ID, A.ACCOUNT_CODE, A.PREM_TYPE, A.GURNT_START_DATE,A.GURNT_PERIOD,A.SETTLE_METHOD,A.GURNT_RATE,A.GURNT_PERD_TYPE,
			A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.HIGH_PLY_YEAR, A.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 查询分配比例和基金名称 -->	
	<select id="PA_findAssignRateAndFundName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 	SELECT A.ITEM_ID,
					       A.ACCOUNT_CODE,
					       A.PREM_TYPE,
					       A.BUSI_ITEM_ID,
					       A.LIST_ID,
					       A.ASSIGN_RATE,
					       A.LOW_PLY_YEAR,
					       A.HIGH_PLY_YEAR,
					       A.POLICY_ID,
					       B.FUND_NAME
					  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE A
					  JOIN APP___PAS__DBUSER.T_FUND B
					    ON A.ACCOUNT_CODE = B.FUND_CODE
					 WHERE 1 = 1  ]]>
		<include refid="PA_contractInvestRateWhereCondition" />
	</select>	
</mapper>
