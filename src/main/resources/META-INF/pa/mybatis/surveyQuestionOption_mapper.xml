<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="surveyQuestionOption">
<!--
	<sql id="PA_surveyQuestionOptionWhereCondition">
		<if test=" sub_question_flag  != null "><![CDATA[ AND A.SUB_QUESTION_FLAG = #{sub_question_flag} ]]></if>
		<if test=" question_option_code != null and question_option_code != ''  "><![CDATA[ AND A.QUESTION_OPTION_CODE = #{question_option_code} ]]></if>
		<if test=" question_option_id  != null "><![CDATA[ AND A.QUESTION_OPTION_ID = #{question_option_id} ]]></if>
		<if test=" ref_question_id  != null "><![CDATA[ AND A.REF_QUESTION_ID = #{ref_question_id} ]]></if>
		<if test=" question_option_desc != null and question_option_desc != ''  "><![CDATA[ AND A.QUESTION_OPTION_DESC = #{question_option_desc} ]]></if>
		<if test=" question_id  != null "><![CDATA[ AND A.QUESTION_ID = #{question_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySurveyQuestionOptionByQuestionOptionIdCondition">
		<if test=" question_option_id  != null "><![CDATA[ AND A.QUESTION_OPTION_ID = #{question_option_id} ]]></if>
	</sql>	
	<sql id="PA_querySurveyQuestionOptionByQuestionOptionCodeCondition">
		<if test=" question_option_code != null and question_option_code != '' "><![CDATA[ AND A.QUESTION_OPTION_CODE = #{question_option_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSurveyQuestionOption"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION(
				INSERT_TIMESTAMP, SUB_QUESTION_FLAG, QUESTION_OPTION_CODE, UPDATE_BY, INSERT_TIME, QUESTION_OPTION_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, REF_QUESTION_ID, QUESTION_OPTION_DESC, INSERT_BY, QUESTION_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{sub_question_flag, jdbcType=NUMERIC} , #{question_option_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{question_option_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{ref_question_id, jdbcType=NUMERIC} , #{question_option_desc, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{question_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSurveyQuestionOption" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION WHERE QUESTION_OPTION_ID=#{question_option_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSurveyQuestionOption" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION ]]>
		<set>
		<trim suffixOverrides=",">
		    SUB_QUESTION_FLAG = #{sub_question_flag, jdbcType=NUMERIC} ,
			QUESTION_OPTION_CODE = #{question_option_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    QUESTION_OPTION_ID = #{question_option_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    REF_QUESTION_ID = #{ref_question_id, jdbcType=NUMERIC} ,
			QUESTION_OPTION_DESC = #{question_option_desc, jdbcType=VARCHAR} ,
		    QUESTION_ID = #{question_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSurveyQuestionOptionByQuestionOptionId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_QUESTION_FLAG, A.QUESTION_OPTION_CODE, A.QUESTION_OPTION_ID, 
			A.REF_QUESTION_ID, A.QUESTION_OPTION_DESC, A.QUESTION_ID FROM APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION A WHERE 1 = 1  ]]>
		<include refid="PA_querySurveyQuestionOptionByQuestionOptionIdCondition" />
	</select>
	
	<select id="PA_findSurveyQuestionOptionByQuestionOptionCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_QUESTION_FLAG, A.QUESTION_OPTION_CODE, A.QUESTION_OPTION_ID, 
			A.REF_QUESTION_ID, A.QUESTION_OPTION_DESC, A.QUESTION_ID FROM APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION A WHERE 1 = 1  ]]>
		<include refid="PA_querySurveyQuestionOptionByQuestionOptionCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSurveyQuestionOption" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_QUESTION_FLAG, A.QUESTION_OPTION_CODE, A.QUESTION_OPTION_ID, 
			A.REF_QUESTION_ID, A.QUESTION_OPTION_DESC, A.QUESTION_ID FROM APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSurveyQuestionOption" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_QUESTION_FLAG, A.QUESTION_OPTION_CODE, A.QUESTION_OPTION_ID, 
			A.REF_QUESTION_ID, A.QUESTION_OPTION_DESC, A.QUESTION_ID FROM APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSurveyQuestionOptionTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySurveyQuestionOptionForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SUB_QUESTION_FLAG, B.QUESTION_OPTION_CODE, B.QUESTION_OPTION_ID, 
			B.REF_QUESTION_ID, B.QUESTION_OPTION_DESC, B.QUESTION_ID FROM (
					SELECT ROWNUM RN, A.SUB_QUESTION_FLAG, A.QUESTION_OPTION_CODE, A.QUESTION_OPTION_ID, 
			A.REF_QUESTION_ID, A.QUESTION_OPTION_DESC, A.QUESTION_ID FROM APP___PAS__DBUSER.T_SURVEY_QUESTION_OPTION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
