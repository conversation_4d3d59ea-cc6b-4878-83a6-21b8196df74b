<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="updatePolicyInfo">

	<update id="updateContractMasterInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractBusiProdInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractProductInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePolicyHolderInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_HOLDER ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[  WHERE  POLICY_ID = #{policy_id}]]>
	</update>
	
	<update id="updateInsuredListInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INSURED_LIST ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id}]]>
	</update>
	
	<update id="updateBenefitInsuredInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BENEFIT_INSURED ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractBeneInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BENE ]]>
		<set>
<!-- 		<trim suffixOverrides=","> -->
<!-- 			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} , -->
<!-- 		</trim> -->
		<trim suffixOverrides=",">
			SHARE_RATE = #{share_rate, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updateContractAgentInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_AGENT ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePayerInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePolicyConditionInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CONDITION ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id}  ]]>
	</update>
	
	<update id="updatePayPlanInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id} ]]>
	</update>
	
	<update id="updatePayDueInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE  POLICY_ID = #{policy_id}  ]]>
	</update>
	
	
	<select id="findContractMasterInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT  A.POLICY_ID,  A.POLICY_CODE , A.APPLY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1=1  ]]>
		<include refid="queryContractMasterByPolicyCodeCondition" />
	</select>
	<sql id="queryContractMasterByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	

</mapper>