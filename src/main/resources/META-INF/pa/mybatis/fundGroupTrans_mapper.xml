<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFundGroupTransDao">

	<sql id="fundGroupTransWhereCondition">
		<if test=" balance_af  != null "><![CDATA[ AND A.BALANCE_AF = #{balance_af} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" trans_type  != null "><![CDATA[ AND A.TRANS_TYPE = #{trans_type} ]]></if>
		<if test=" trans_amount  != null "><![CDATA[ AND A.TRANS_AMOUNT = #{trans_amount} ]]></if>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME = #{deal_time} ]]></if>		
		<if test=" deal_time_start  != null  and  deal_time_start  != ''  "><![CDATA[ AND A.DEAL_TIME >= #{deal_time_start} ]]></if>		
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.TRANS_CODE IN ]]>
			<foreach collection ="trans_code_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" trans_type_list  != null and trans_type_list.size()!=0">
			<![CDATA[ AND A.TRANS_TYPE IN ]]>
			<foreach collection ="trans_type_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>	
		<if test=" list_type != null  and  list_type  != ''  ">
			<![CDATA[ AND A.Deal_Time in (Select max(b.deal_time)
      					From APP___PAS__DBUSER.T_FUND_GROUP_TRANS B
    						    where A.Policy_Id = b.policy_id
       						  and a.trans_code = b.trans_code) ]]>
   		</if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.DEAL_TIME <= #{end_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryFundGroupTransByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryFundGroupTransByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="queryFundGroupTransByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="queryFundGroupTransByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addFundGroupTrans"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_FUND_GR_TRANS__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_GROUP_TRANS(
				FUND_CODE,INSERT_TIME, BALANCE_AF, UPDATE_TIME, BUSI_PROD_CODE, TRANS_TYPE, INSERT_TIMESTAMP, TRANS_AMOUNT, 
				TRANS_CODE, DEAL_TIME, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, 
				POLICY_ID ) 
			VALUES (
				#{fund_code, jdbcType=VARCHAR} ,SYSDATE, #{balance_af, jdbcType=NUMERIC} , SYSDATE , #{busi_prod_code, jdbcType=VARCHAR} , #{trans_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{trans_amount, jdbcType=NUMERIC} 
				, #{trans_code, jdbcType=VARCHAR} , #{deal_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteFundGroupTrans" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateFundGroupTrans" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_GROUP_TRANS ]]>
		<set>
		<trim suffixOverrides=",">
		    FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
		    BALANCE_AF = #{balance_af, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    TRANS_TYPE = #{trans_type, jdbcType=NUMERIC} ,
		    TRANS_AMOUNT = #{trans_amount, jdbcType=NUMERIC} ,
			TRANS_CODE = #{trans_code, jdbcType=VARCHAR} ,
		    DEAL_TIME = #{deal_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findFundGroupTransByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FUND_CODE,A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE 1 = 1  ]]>
		<include refid="queryFundGroupTransByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findFundGroupTransByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FUND_CODE,A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE 1 = 1  ]]>
		<include refid="queryFundGroupTransByBusiProdCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findFundGroupTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FUND_CODE,A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE 1 = 1  ]]>
		<include refid="fundGroupTransWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	
	
	<select id="findFundGroupTransByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FUND_CODE,A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE 1 = 1  ]]>
		<include refid="queryFundGroupTransByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapFundGroupTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FUND_CODE,A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllFundGroupTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FUND_CODE,A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE ROWNUM <=  1000  ]]>
		<include refid="fundGroupTransWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findFundGroupTransTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryFundGroupTransForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BALANCE_AF, B.BUSI_PROD_CODE, B.TRANS_TYPE, B.TRANS_AMOUNT, 
			B.TRANS_CODE, B.DEAL_TIME, B.LIST_ID, B.BUSI_ITEM_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findLastFundGroupTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.FUND_CODE,A.BALANCE_AF, A.BUSI_PROD_CODE, A.TRANS_TYPE, A.TRANS_AMOUNT, 
			A.TRANS_CODE, A.DEAL_TIME, A.LIST_ID, A.BUSI_ITEM_ID,  A.POLICY_ID 
			FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A 
			WHERE A.LIST_ID = (SELECT MAX(A.LIST_ID)
                      FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A
                     WHERE A.POLICY_ID = #{policy_id}
                       AND A.BUSI_ITEM_ID = #{busi_item_id})
		 ]]>		 
	</select>
	
	
	<!-- 保全回退删除操作 -->	
	<delete id="deleteFundGroupTransForRB" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS WHERE POLICY_ID = #{policy_id} AND DEAL_TIME >=#{deal_time}  ]]>
	</delete>
	
	<!-- 官微查询账户基础信息 -->
	<select id="PA_finBasicFundGroupTransForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		
			SELECT TF.POLICY_ID,
			       (CASE
			         WHEN TF.TRANS_CODE = '05' THEN 3
			         ELSE TF.TRANS_TYPE END) AS TRANS_TYPE, /*交易类型*/
			       (CASE
			         WHEN (tf.trans_code = '21' or tf.trans_code = '24')  AND TF.FUND_CODE = '928000' THEN
			          '账户注销-稳健回报型投资组合'
			         WHEN (tf.trans_code = '21' or tf.trans_code = '24')  AND TF.FUND_CODE = '928001' THEN
			          '账户注销-积极进取型投资组合'
			         WHEN TF.TRANS_CODE = '04' AND TF.FUND_CODE = '928000' THEN
			          '保单管理费-稳健回报型投资组合'
			         WHEN TF.TRANS_CODE = '04' AND TF.FUND_CODE = '928001' THEN
			          '保单管理费-积极进取型投资组合'
			         WHEN TF.TRANS_CODE = '42' AND TF.FUND_CODE = '928000' THEN
			          '投资组合收益-稳健回报型投资组合'
			         WHEN TF.TRANS_CODE = '42' AND TF.FUND_CODE = '928001' THEN
			          '投资组合收益-积极进取型投资组合'
			         WHEN (tf.trans_code = '21' or tf.trans_code = '24')  AND TF.FUND_CODE = '928100' THEN
			          '账户注销-稳健回报型投资组合'
			         WHEN (tf.trans_code = '21' or tf.trans_code = '24')  AND TF.FUND_CODE = '928101' THEN
			          '账户注销-积极进取型投资组合'
			         WHEN TF.TRANS_CODE = '04' AND TF.FUND_CODE = '928100' THEN
			          '保单管理费-稳健回报型投资组合'
			         WHEN TF.TRANS_CODE = '04' AND TF.FUND_CODE = '928101' THEN
			          '保单管理费-积极进取型投资组合'
			         WHEN TF.TRANS_CODE = '42' AND TF.FUND_CODE = '928100' THEN
			          '投资组合收益-稳健回报型投资组合'
			         WHEN TF.TRANS_CODE = '42' AND TF.FUND_CODE = '928101' THEN
			          '投资组合收益-积极进取型投资组合'
			         WHEN TF.TRANS_CODE = '14' THEN
			          '投资组合转换-转入'
			         WHEN TF.TRANS_CODE = '23' THEN
			          '投资组合转换-转出'
			         WHEN TF.TRANS_CODE IN ('05', '35', '46', '48', '50') THEN
			          '初始扣费'
			         WHEN TF.TRANS_CODE = '12' THEN
			          '期交保费-续期'
			         WHEN TF.TRANS_CODE = '52' THEN
			          '期交保费-首期'
			         WHEN TF.TRANS_CODE = '53' THEN
			          '不定期交保费-首期'
			         WHEN TF.TRANS_CODE = '51' THEN
			          '期交保费-补交'
			         WHEN TF.TRANS_CODE = '54' THEN
			          '不定期交保费'
			         WHEN TF.TRANS_CODE = '55' THEN
			          '期交保费-补交'
			         WHEN TF.TRANS_CODE = '42' THEN
			          '投资组合收益'
			         WHEN TF.TRANS_CODE = '22' THEN
			         '部分领取保单账户价值'
                     WHEN TF.TRANS_CODE = '03' and TF.TRANS_TYPE='2' THEN
			         '退还风险保险费'
			         ELSE
			          (SELECT T.DESCRIPTION
			             FROM APP___PAS__DBUSER.T_TRANSACTION_CODE T
			            WHERE TF.TRANS_CODE = T.TRANS_CODE)
			       END) AS TRANS_CODE,
			       ROUND(TF.TRANS_AMOUNT, 2) AS TRANS_AMOUNT, /*变更金额*/
			       TF.DEAL_TIME, /*交易日期*/
			       ROUND(TF.BALANCE_AF, 2) AS TRANS_INTEREST, /*变更后账户价值*/
			       TF.BUSI_PROD_CODE,/*险种代码*/
			       (SELECT B.PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = TF.BUSI_PROD_CODE) AS PRODUCT_ABBR_NAME,/*险种名称*/
			        (SELECT MAX(A.ANNUAL_INTEREST_RATE)
                          FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                         WHERE A.FUND_CODE = TF.FUND_CODE
                           AND A.INTEREST_RATE_TYPE = '1'
                           AND A.INTEREST_START_DATE <= TF.DEAL_TIME
                           AND A.INTEREST_END_DATE >= TF.DEAL_TIME) * 100 AS GURNT_INTEREST_RATE/*保证利率*/
			  FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS TF, APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
			 WHERE TF.POLICY_ID = TCM.POLICY_ID
			   AND TCM.POLICY_CODE = #{policy_code}
			   AND TF.BUSI_PROD_CODE IN ('00928000','00928100')  ]]>	
			   <if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND TF.DEAL_TIME >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
			   <if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND TF.DEAL_TIME <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>
			<![CDATA[  ORDER BY TF.DEAL_TIME DESC, TF.LIST_ID DESC  ]]>		 
	</select>
	
	<!-- 官微查询账户详细信息 -->
	<select id="PA_finFundGroupTransForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT C.*  FROM (SELECT 2 AS TRANS_TYPE, /*交易类型 1-出账 2-入账 码表T_INVEST_TRANS_TYPE*/
					               '投资组合收益' AS TRANS_CODE, /*变更类型*/
					               B.SETTLE_DATE AS DEAL_TIME, /*变更日期*/
					               ROUND(B.INTEREST, 2) AS TRANS_AMOUNT, /*变更金额*/
					               B.INTEREST_RATE * 100 AS TRANS_PROPORTION, /*结算利率*/
					               ROUND(B.BALANCE, 2) AS TRANS_INTEREST, /*变更后账户价值*/
					               B.SETTLEMENT_ID AS TRANS_ID, /*交易ID*/
					               (SELECT TCB.BUSI_PROD_CODE
                                       FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                        WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS BUSI_PROD_CODE, /*险种代码*/
                                   (SELECT B.PRODUCT_ABBR_NAME
                                       FROM DEV_PAS.T_BUSINESS_PRODUCT B
                                       WHERE B.PRODUCT_CODE_SYS =
                                    (SELECT TCB.BUSI_PROD_CODE
                                      FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                      WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID)) AS PRODUCT_ABBR_NAME,/*险种名称*/
                                   B.GURNT_INTEREST_RATE * 100 AS GURNT_INTEREST_RATE/*保证利率*/
					          FROM (SELECT CI.*, FS.*, ROWNUM AS RN
					                  FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT FS
					                 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST CI
					                    ON CI.LIST_ID = FS.INVEST_ID
					                 WHERE 1 = 1 ]]>          
			<if test=" listid != null and listid.size()!=0">
					<![CDATA[  AND CI.LIST_ID IN  ]]>
					<foreach collection ="listid" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
			</if>
			<if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND FS.SETTLE_DATE >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
			   <if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND FS.SETTLE_DATE <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>	 
			<![CDATA[  		) B    
					        UNION ALL
					        SELECT B.TRANS_TYPE AS TRANS_TYPE, /*交易类型*/
					               (case
					                 when B.TRANS_CODE = '11' then
					                  '首期期交保费'
					                 when B.TRANS_CODE = '47' then
					                  '首期不定期交保费'
					                 when B.TRANS_CODE = '12' then
					                  '续期期交保费'
					                 when B.TRANS_CODE = '51' then
					                  '补交保费'
					                 when B.TRANS_CODE = '49' then
					                  '不定期交保费'
					                 when B.TRANS_CODE = '42' then
					                  '投资组合收益'
					                 when B.TRANS_CODE = '21' OR B.TRANS_CODE = '24'  then
					                  '账户注销'
					                 when B.TRANS_CODE = '14' then
					                  '投资组合转换-转入'
					                 when B.TRANS_CODE = '23' then
					                  '投资组合转换-转出'
					                 WHEN B.TRANS_CODE = '22' THEN
			                          '部分领取保单账户价值'
                                     WHEN B.TRANS_CODE = '03' AND B.TRANS_TYPE='2' THEN
			                          '退还风险保险费'
					                 else
					                  (select T.DESCRIPTION
					                     from APP___PAS__DBUSER.T_TRANSACTION_CODE T
					                    WHERE B.TRANS_CODE = T.TRANS_CODE)
					               end) AS trans_code, /*变更类型*/
					               B.DEAL_TIME AS DEAL_TIME, /*变更日期*/
					               ROUND(B.TRANS_AMOUNT, 2) AS TRANS_AMOUNT, /*变更金额*/
					               B.TRANS_INTEREST AS TRANS_PROPORTION, /*结算利率*/
					               ROUND(B.BALANCE, 2) AS TRANS_INTEREST, /*变更后账户价值*/
					               B.TRANS_ID AS TRANS_ID /*交易ID*/,
					               (SELECT TCB.BUSI_PROD_CODE
                                       FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                      WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS BUSI_PROD_CODE, /*险种代码*/
                                   (SELECT B.PRODUCT_ABBR_NAME
                                     FROM DEV_PAS.T_BUSINESS_PRODUCT B
                                         WHERE B.PRODUCT_CODE_SYS =
                                     (SELECT TCB.BUSI_PROD_CODE
                                       FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                       WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID)) AS PRODUCT_ABBR_NAME,/*险种名称*/
                                     B.GURNT_INTEREST_RATE /*保证利率*/
					          FROM (SELECT CI.*,
					                       TC.TRANS_CODE,
					                       TC.DESCRIPTION,
					                       FT.TRANS_TYPE,
					                       FT.DEAL_TIME,
					                       FT.TRANS_AMOUNT,
					                       (CASE
					                         WHEN FT.TRANS_CODE = '42' THEN
					                          (SELECT A.ANNUAL_INTEREST_RATE
					                             FROM APP___PDS__DBUSER.T_UNIVERSAL_SETTLEMENT_RATE A
					                            WHERE A.INTEREST_RATE_TYPE = '1'
					                              AND A.INTEREST_START_DATE <= FT.DEAL_TIME
					                              AND A.FUND_CODE = FT.FUND_CODE
					                              AND A.interest_start_date =
					                                  (SELECT MAX(A.interest_start_date)
					                                     FROM APP___PDS__DBUSER.T_UNIVERSAL_SETTLEMENT_RATE A
					                                    WHERE A.INTEREST_RATE_TYPE = '1'
					                                      AND A.INTEREST_START_DATE <= FT.DEAL_TIME
					                                      AND A.FUND_CODE = FT.FUND_CODE))
					                         ELSE
					                          NULL
					                       END) * 100 AS TRANS_INTEREST,
					                       (SELECT MAX(A.ANNUAL_INTEREST_RATE)
                                              FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                                              WHERE A.FUND_CODE = FT.FUND_CODE
                                               AND A.INTEREST_RATE_TYPE = '1'
                                               AND A.INTEREST_START_DATE <= FT.DEAL_TIME
                                               AND A.INTEREST_END_DATE >= FT.DEAL_TIME) * 100 AS GURNT_INTEREST_RATE, /*保证利率*/
					                       FT.TRANS_UNITS,
					                       FT.TRANS_PRICE,
					                       FT.BALANCE_UNITS_BF,
					                       (CASE FT.TRANS_TYPE
					                         WHEN 0 THEN
					                          FT.BALANCE_UNITS_BF
					                         WHEN 1 THEN
					                          FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
					                         WHEN 2 THEN
					                          FT.BALANCE_UNITS_BF + FT.TRANS_AMOUNT
					                         WHEN 3 THEN
					                          FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
					                       END) AS BALANCE,
					                       ROWNUM AS RN,
					                       FT.TRANS_ID AS TRANS_ID					                       
					                  FROM APP___PAS__DBUSER.T_FUND_TRANS FT
					                 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST CI
					                    ON CI.LIST_ID = FT.LIST_ID
					                 INNER JOIN APP___PAS__DBUSER.T_TRANSACTION_CODE TC
					                    ON FT.TRANS_CODE = TC.TRANS_CODE
					                 WHERE TC.TRANS_CODE NOT IN ('05', '30', '31', '35', '36', '37') ]]>
			<if test=" listid != null and listid.size()!=0">
					<![CDATA[  AND CI.LIST_ID IN  ]]>
					<foreach collection ="listid" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
			</if>	
			<if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND FT.DEAL_TIME >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
			   <if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND FT.DEAL_TIME <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>
			<![CDATA[ ) B) C ORDER BY C.DEAL_TIME DESC, C.TRANS_ID DESC ]]> 
	</select>
	
	<!-- 查询账户变动金额 -->	
	<select id="PA_findLastFundGroupTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(A.TRANS_AMOUNT) TRANS_AMOUNT FROM APP___PAS__DBUSER.T_FUND_GROUP_TRANS A WHERE 1 = 1  ]]>
		<include refid="fundGroupTransWhereCondition" />
	</select>
	

	<!-- 查询一年期内的不定期缴保费及追加保费 -->	
	<select id="PA_findAllFundGroupTransByPolicyID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT TFGT.TRANS_AMOUNT FROM DEV_PAS.T_FUND_GROUP_TRANS TFGT
              WHERE 1=1
              AND TFGT.POLICY_ID = #{policy_id}
			  AND TFGT.TRANS_CODE IN ('53','54','55','56')	
			  AND TFGT.DEAL_TIME >= TRUNC(sysdate, 'yyyy') 				 
	    	  AND TFGT.DEAL_TIME <= add_months(trunc(sysdate, 'yyyy'), 12) - 1
		 ]]>
	</select>
	
	<!-- 查询保单账户结息数据-->
	<select id="PA_findAllFundSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.FUND_CODE,
                A.BALANCE_AF,
                A.BUSI_PROD_CODE,
                A.TRANS_TYPE,
                FS.Interest As trans_amount,
                A.TRANS_CODE,
                A.DEAL_TIME,
                FS.SETTLEMENT_ID AS LIST_ID,
                A.BUSI_ITEM_ID,
                A.POLICY_ID,
                FS.Interest_Rate interestrate
  From dev_pas.t_Contract_Invest  TCI,
       dev_pas.t_Fund_Settlement  FS,
       dev_pas.t_Fund_Group_Trans A
 where TCI.list_id = FS.invest_id
   and TCI.settle_due_date = FS.settle_date
   and FS.settle_date = A.deal_time
   AND FS.INTEREST = A.Trans_Amount
   AND TCI.policy_id = A.policy_id  
   AND ROWNUM <=  1000  ]]>
		<include refid="fundGroupTransWhereCondition" />
		<![CDATA[ ORDER BY FS.SETTLEMENT_ID ]]> 
	</select>
	
	<!-- 查询保单最后一次交易日期 -->	
	<select id="PA_findPolicyFundGroupTransDealTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT MAX(DEAL_TIME) AS DEAL_TIME
      	FROM DEV_PAS.T_FUND_GROUP_TRANS
     	WHERE POLICY_ID = #{policy_id}
		 ]]>
	</select>
	<!-- 查询账户基础信息（税延产品） -->
	<select id="PA_finBasicFundGroupTransForSYByPC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 		
  SELECT C.*
  FROM (SELECT 2 AS TRANS_TYPE, /*交易类型 1-出账 2-入账 码表t_invest_trans_type*/
               '投资组合收益' AS TRANS_CODE, /*变更类型*/
               B.SETTLE_DATE AS DEAL_TIME, /*变更日期*/
               B.INTEREST AS TRANS_AMOUNT, /*变更金额*/
               B.INTEREST_RATE * 100 AS TRANS_PROPORTION, /*结算利率*/
               B.GURNT_INTEREST_RATE * 100 AS GURNT_INTEREST_RATE, /*保证利率*/
               B.BALANCE AS TRANS_INTEREST, /*变更后账户价值*/
               B.SETTLEMENT_ID AS TRANS_ID, /*交易ID*/
               (SELECT TCB.BUSI_PROD_CODE
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                 WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS BUSI_PROD_CODE, /*险种代码*/
               (SELECT TBP.PRODUCT_ABBR_NAME
                  FROM DEV_PAS.T_BUSINESS_PRODUCT TBP
                 WHERE TBP.PRODUCT_CODE_SYS =
                       (SELECT TCB.BUSI_PROD_CODE
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                         WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID)) AS PRODUCT_ABBR_NAME /*险种名称*/
          FROM (SELECT CI.*, FS.*, ROWNUM AS RN
                  FROM DEV_PAS.T_FUND_SETTLEMENT FS
                 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                    ON CI.LIST_ID = FS.INVEST_ID
                 WHERE 1 = 1 ]]> 
            <if test=" listid != null and listid.size()!=0">
				<![CDATA[  AND CI.LIST_ID IN  ]]>
				<foreach collection ="listid" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
			</if>
			<if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND FS.SETTLE_DATE >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
			<if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND FS.SETTLE_DATE <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>	
       <![CDATA[  ) B
        UNION ALL
        SELECT B.TRANS_TYPE AS TRANS_TYPE, /*交易类型*/
               (case
                 when B.TRANS_CODE = '11' then
                  '首期期交保费'
                 when B.TRANS_CODE = '47' then
                  '首期不定期交保费'
                 when B.TRANS_CODE = '12' then
                  '续期期交保费'
                 when B.TRANS_CODE = '51' then
                  '补交保费'
                 when B.TRANS_CODE = '49' then
                  '不定期交保费'
                 when B.TRANS_CODE = '42' then
                  '投资组合收益'
                 when B.TRANS_CODE = '21' OR B.TRANS_CODE = '24' then
                  '账户注销'
                 when B.TRANS_CODE = '14' then
                  '投资组合转换-转入'
                 when B.TRANS_CODE = '23' then
                  '投资组合转换-转出'
                 else
                  (select T.DESCRIPTION
                     from DEV_PAS.T_TRANSACTION_CODE T
                    WHERE B.TRANS_CODE = T.TRANS_CODE)
               end) AS trans_code, /*变更类型*/
               B.DEAL_TIME AS DEAL_TIME, /*变更日期*/
               B.TRANS_AMOUNT AS TRANS_AMOUNT, /*变更金额*/
               B.TRANS_INTEREST AS TRANS_PROPORTION, /*结算利率*/
               B.GURNT_INTEREST_RATE AS GURNT_INTEREST_RATE, /*保证利率*/
               B.BALANCE AS TRANS_INTEREST, /*变更后账户价值*/
               B.TRANS_ID AS TRANS_ID, /*交易ID*/
               (SELECT TCB.BUSI_PROD_CODE
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                 WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS BUSI_PROD_CODE, /*险种代码*/
               (SELECT TBP.PRODUCT_ABBR_NAME
                  FROM DEV_PAS.T_BUSINESS_PRODUCT TBP
                 WHERE TBP.PRODUCT_CODE_SYS =
                       (SELECT TCB.BUSI_PROD_CODE
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                         WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID)) AS PRODUCT_ABBR_NAME /*险种名称*/
          FROM (SELECT CI.*,
                       FT.TRANS_CODE,
                       FT.TRANS_TYPE,
                       FT.DEAL_TIME,
                       FT.TRANS_AMOUNT,
                       FT.FUND_CODE,
                       (CASE
                         WHEN FT.TRANS_CODE = '42' THEN
                          (SELECT A.ANNUAL_INTEREST_RATE
                             FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                            WHERE A.INTEREST_RATE_TYPE = '1'
                              AND A.INTEREST_START_DATE <= FT.DEAL_TIME
                              AND A.FUND_CODE = FT.FUND_CODE
                              AND A.interest_start_date =
                                  (SELECT MAX(A.interest_start_date)
                                     FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                                    WHERE A.INTEREST_RATE_TYPE = '1'
                                      AND A.INTEREST_START_DATE <= FT.DEAL_TIME
                                      AND A.FUND_CODE = FT.FUND_CODE))
                         ELSE
                          NULL
                       END) * 100 AS TRANS_INTEREST,
                       (SELECT MAX(A.ANNUAL_INTEREST_RATE)
                          FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                         WHERE A.FUND_CODE = FT.FUND_CODE
                           AND A.INTEREST_RATE_TYPE = '1'
                           AND A.INTEREST_START_DATE <= FT.DEAL_TIME
                           AND A.INTEREST_END_DATE >= FT.DEAL_TIME) * 100 AS GURNT_INTEREST_RATE,
                       FT.TRANS_UNITS,
                       FT.TRANS_PRICE,
                       FT.BALANCE_UNITS_BF,
                       (CASE FT.TRANS_TYPE
                         WHEN 0 THEN
                          FT.BALANCE_UNITS_BF
                         WHEN 1 THEN
                          FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
                         WHEN 2 THEN
                          FT.BALANCE_UNITS_BF + FT.TRANS_AMOUNT
                         WHEN 3 THEN
                          FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
                       END
                       
                       ) AS BALANCE,
                       ROWNUM AS RN,
                       FT.TRANS_ID AS TRANS_ID
                  FROM DEV_PAS.T_FUND_TRANS FT
                 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                    ON CI.LIST_ID = FT.LIST_ID
                 INNER JOIN DEV_PAS.T_TRANSACTION_CODE TC
                    ON FT.TRANS_CODE = TC.TRANS_CODE
                  WHERE TC.TRANS_CODE NOT IN ('05', '30', '31', '35', '36', '37') ]]>
           <if test=" listid != null and listid.size()!=0">
		         <![CDATA[  AND CI.LIST_ID IN  ]]>
				 <foreach collection ="listid" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		   </if>
		   <if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND FT.DEAL_TIME >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
		   <if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND FT.DEAL_TIME <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>
           <![CDATA[     ) B) C
   ORDER BY C.DEAL_TIME DESC, C.TRANS_ID DESC  ]]>		 
	</select>
	
	<!-- 税优报送根据保单id查询账户信息 -->	
	<select id="PA_findAllFundGroupTransForSybs" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    SELECT A.FUND_CODE,
					        A.BALANCE_AF,
					        A.BUSI_PROD_CODE,
					        A.TRANS_TYPE,
					        A.TRANS_AMOUNT,
					        A.TRANS_CODE,
					        A.DEAL_TIME,
					        A.LIST_ID,
					        A.BUSI_ITEM_ID,
					        A.POLICY_ID,
					        A.INSERT_TIME
					   FROM DEV_PAS.T_FUND_GROUP_TRANS A
					  WHERE A.LIST_ID IN (SELECT MAX(A.LIST_ID)
					                        FROM DEV_PAS.T_FUND_GROUP_TRANS A
					                       WHERE A.POLICY_ID = #{policy_id}
					                       GROUP BY A.BUSI_PROD_CODE)   ]]>
	</select>
	
	<!-- 查询个人账户(税延产品)轨迹记录 -->
	<select id="PA_finFundGroupTransForSYAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TF.POLICY_ID,
       (case
         when tf.trans_code = '05' then
          3
         else
          TF.TRANS_TYPE
       end) as TRANS_TYPE, /*交易类型*/
       (case 
         when tf.trans_code = '14' then
          '投资组合转换-转入'
         when tf.trans_code = '23' then
          '投资组合转换-转出'
         when tf.trans_code = '42' and tf.busi_prod_code = '********' then
          '结算利息-个人税收递延型养老年金保险A款（2018）产品账户'
         when tf.trans_code = '42' and tf.busi_prod_code = '********' then
          '结算利息-个人税收递延型养老年金保险B1款（2018）产品账户'
         when tf.trans_code = '42' and tf.busi_prod_code = '********' then
          '结算利息-个人税收递延型养老年金保险B2款（2018）产品账户'
         when tf.trans_code = '04' and tf.busi_prod_code = '********' then
          '保单管理费-个人税收递延型养老年金保险A款（2018）产品账户'
         when tf.trans_code = '04' and tf.busi_prod_code = '********' then
          '保单管理费-个人税收递延型养老年金保险B1款（2018）产品账户'
         when tf.trans_code = '04' and tf.busi_prod_code = '********' then
          '保单管理费-个人税收递延型养老年金保险B2款（2018）产品账户'
         when (tf.trans_code = '21' or tf.trans_code = '24') and
              tf.busi_prod_code = '********' then
          '账户注销-个人税收递延型养老年金保险A款（2018）产品账户'
         when (tf.trans_code = '21' or tf.trans_code = '24') and
              tf.busi_prod_code = '********' then
          '账户注销-个人税收递延型养老年金保险B1款（2018）产品账户'
         when (tf.trans_code = '21' or tf.trans_code = '24') and
              tf.busi_prod_code = '********' then
          '账户注销-个人税收递延型养老年金保险B2款（2018）产品账户'
         when tf.trans_code = '14' and tf.busi_prod_code = '********' then
          '转入-个人税收递延型养老年金保险A款（2018）产品账户'
         when tf.trans_code = '14' and tf.busi_prod_code = '********' then
          '转入-个人税收递延型养老年金保险B1款（2018）产品账户'
         when tf.trans_code = '14' and tf.busi_prod_code = '********' then
          '转入-个人税收递延型养老年金保险B2款（2018）产品账户'
         when tf.trans_code = '23' and tf.busi_prod_code = '********' then
          '转出-个人税收递延型养老年金保险A款（2018）产品账户'
         when tf.trans_code = '23' and tf.busi_prod_code = '********' then
          '转出-个人税收递延型养老年金保险B1款（2018）产品账户'
         when tf.trans_code = '23' and tf.busi_prod_code = '********' then
          '转出-个人税收递延型养老年金保险B2款（2018）产品账户'
         when tf.trans_code in ('05', '35', '46', '48', '50', '56') and
              tf.busi_prod_code = '********' then
          '初始扣费-个人税收递延型养老年金保险A款（2018）产品账户'
         when tf.trans_code in ('05', '35', '46', '48', '50', '56') and
              tf.busi_prod_code = '********' then
          '初始扣费-个人税收递延型养老年金保险B1款（2018）产品账户'
         when tf.trans_code in ('05', '35', '46', '48', '50', '56') and
              tf.busi_prod_code = '********' then
          '初始扣费-个人税收递延型养老年金保险B2款（2018）产品账户'
         when tf.trans_code = '52' and tf.busi_prod_code = '********' then
          '期交保费-首期-个人税收递延型养老年金保险A款（2018）产品账户'
         when tf.trans_code = '52' and tf.busi_prod_code = '********' then
          '期交保费-首期-个人税收递延型养老年金保险B1款（2018）产品账户'
         when tf.trans_code = '52' and tf.busi_prod_code = '********' then
          '期交保费-首期-个人税收递延型养老年金保险B2款（2018）产品账户'
         when tf.trans_code = '12' and tf.busi_prod_code = '********' then
          '期交保费-续期-个人税收递延型养老年金保险A款（2018）产品账户'
         when tf.trans_code = '12' and tf.busi_prod_code = '********' then
          '期交保费-续期-个人税收递延型养老年金保险B1款（2018）产品账户'
         when tf.trans_code = '12' and tf.busi_prod_code = '********' then
          '期交保费-续期-个人税收递延型养老年金保险B2款（2018）产品账户'
         when tf.trans_code in ('05', '35', '46', '48', '50', '56') then
          '初始扣费'
         when tf.trans_code = '12' then
          '期交保费-续期'
         when tf.trans_code = '52' then
          '期交保费-首期'
         when tf.trans_code = '53' then
          '不定期交保费-首期'
         when tf.trans_code = '51' then
          '期交保费-补交'
         when tf.trans_code = '54' then
          '不定期交保费'
         when tf.trans_code = '55' then
          '期交保费-补交'
         when tf.trans_code = '42' then
          '投资组合收益'
         else
          (select T.DESCRIPTION
             from DEV_PAS.T_TRANSACTION_CODE T
            WHERE tf.TRANS_CODE = T.TRANS_CODE)
       end) as trans_code,
       TF.TRANS_AMOUNT, /*变更金额*/
       TF.DEAL_TIME, /*交易日期*/
       TF.Balance_Af TRANS_INTEREST, /*变更后账户价值*/
       TF.BUSI_PROD_CODE,/*险种代码*/
       TBP.Product_Abbr_Name,/*险种名称*/
       (SELECT MAX(A.ANNUAL_INTEREST_RATE)
                          FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                         WHERE A.FUND_CODE = TF.FUND_CODE
                           AND A.INTEREST_RATE_TYPE = '2'
                           AND A.SETTLE_PERIOD_START_DATE <= TF.DEAL_TIME
                           AND A.SETTLE_PERIOD_END_DATE >= TF.DEAL_TIME) * 100 AS trans_proportion,/*结算利率*/
       (SELECT MAX(A.ANNUAL_INTEREST_RATE)
                          FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                         WHERE A.FUND_CODE = TF.FUND_CODE
                           AND A.INTEREST_RATE_TYPE = '1'
                           AND A.INTEREST_START_DATE <= TF.DEAL_TIME
                           AND A.INTEREST_END_DATE >= TF.DEAL_TIME) * 100 AS GURNT_INTEREST_RATE/*保证利率*/
        FROM DEV_PAS.T_FUND_GROUP_TRANS TF,DEV_PAS.T_CONTRACT_MASTER TCM,DEV_PAS.T_BUSINESS_PRODUCT TBP
        WHERE TF.POLICY_ID = TCM.POLICY_ID
         AND TF.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
         AND TCM.POLICY_CODE = #{policy_code}]]>
   <if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[AND TF.BUSI_PROD_CODE = #{busi_prod_code}]]></if>
    <if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND TF.DEAL_TIME >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
	<if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND TF.DEAL_TIME <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>
         <![CDATA[ AND TBP.TAX_EXTENSION_FLAG = '1'
        ORDER BY TF.DEAL_TIME DESC, TF.BUSI_PROD_CODE, TF.LIST_ID DESC
		]]>
	</select>

	<!-- 个人养老金承保报送查询万能险单账户信息 -->
	<select id="PA_findAllFundTransForGrylWn" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT DISTINCT A.TRANS_ID AS LIST_ID,
					       A.TRANS_AMOUNT,
					       A.TRANS_CODE,
					       A.TRANS_TYPE,
					       A.BUSI_ITEM_ID,
					       A.DEAL_TIME,
					       B.BUSI_PROD_CODE 
					  FROM DEV_PAS.T_FUND_TRANS A, DEV_PAS.T_CONTRACT_BUSI_PROD B
					 WHERE A.POLICY_ID = B.POLICY_ID
					   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID  ]]>
		<include refid="fundGroupTransWhereCondition" />
	</select>
	
	<!-- 查询单账户结息信息 -->
	<select id="PA_findAllFundSettlementForSingleAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    SELECT DISTINCT 
					        FS.ACCOUNT_CODE AS FUND_CODE,
					        B.BUSI_PROD_CODE,
					        2 AS TRANS_TYPE,
					        FS.INTEREST AS TRANS_AMOUNT,
					        '42' AS TRANS_CODE,
					        FS.SETTLE_DATE AS DEAL_TIME,
					        FS.SETTLEMENT_ID AS LIST_ID,
					        TCI.BUSI_ITEM_ID,
					        TCI.POLICY_ID,
					        FS.INTEREST_RATE AS INTERESTRATE
					  FROM DEV_PAS.T_CONTRACT_INVEST    TCI,
					       DEV_PAS.T_FUND_SETTLEMENT    FS,
					       DEV_PAS.T_CONTRACT_BUSI_PROD B
					 WHERE TCI.LIST_ID = FS.INVEST_ID
					   AND TCI.SETTLE_DUE_DATE = FS.SETTLE_DATE
					   AND TCI.POLICY_ID = B.POLICY_ID
					   AND TCI.BUSI_ITEM_ID = B.BUSI_ITEM_ID ]]>
		<if test=" policy_id  != null "><![CDATA[ AND TCI.POLICY_ID = #{policy_id} ]]></if>			   
		<if test=" busi_item_id  != null "><![CDATA[ AND TCI.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
</mapper>
