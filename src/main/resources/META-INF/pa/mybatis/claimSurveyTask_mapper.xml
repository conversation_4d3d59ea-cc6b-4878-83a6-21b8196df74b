<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimSurveyTaskPO">

	<sql id="claimSurveyTaskWhereCondition">
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" biz_no  != null "><![CDATA[ AND A.BIZ_NO = #{biz_no} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" claim_pay  != null "><![CDATA[ AND A.CLAIM_PAY = #{claim_pay} ]]></if>
		<if test=" accu_amount  != null "><![CDATA[ AND A.ACCU_AMOUNT = #{accu_amount} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" apply_num  != null "><![CDATA[ AND A.APPLY_NUM = #{apply_num} ]]></if>
		<if test=" batch_id  != null "><![CDATA[ AND A.BATCH_ID = #{batch_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" payee_num  != null "><![CDATA[ AND A.PAYEE_NUM = #{payee_num} ]]></if>
		<if test=" organ_code  != null "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimSurveyTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	
	<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimSurveyTaskByPolicyBatch">
		<if test="batch_id != null and batch_id != ''">
			<![CDATA[ AND A.BATCH_ID = #{batch_id} ]]>
		</if>
		<if test="biz_no != null and biz_no != ''">
			<![CDATA[ AND A.BIZ_NO = #{biz_no} ]]>
		</if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimSurveyTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CLAIM_SURVEY_TASK__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK(
				BIZ_TYPE, BIZ_NO, CUSTOMER_NAME, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, CLAIM_PAY, 
				ACCU_AMOUNT, INSERT_TIMESTAMP, CHANNEL_TYPE, APPLY_NUM, UPDATE_BY, BATCH_ID, LIST_ID, 
				PAYEE_NUM, UPDATE_TIMESTAMP, INSERT_BY,ORGAN_CODE ) 
			VALUES (
				#{biz_type, jdbcType=NUMERIC}, #{biz_no, jdbcType=NUMERIC} , #{customer_name, jdbcType=VARCHAR} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{claim_pay, jdbcType=NUMERIC} 
				, #{accu_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{channel_type, jdbcType=VARCHAR} , #{apply_num, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{batch_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{payee_num, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{organ_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimSurveyTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK WHERE LIST_ID = #{list_id} ]]>
	</delete>
	
     <delete id="batchDeleteClaimSurveyTasks" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK WHERE 1=1 AND BATCH_ID = #{batch_id} AND BIZ_NO = #{biz_no}  ]]>
		<if test="biz_type != null and biz_type != ''">
			<![CDATA[ AND BIZ_TYPE = #{biz_type}  ]]>
		</if>
	</delete>
	
<!-- 修改操作 -->
	<update id="updateClaimSurveyTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK ]]>
		<set>
		<trim suffixOverrides=",">
		    BIZ_TYPE = #{biz_type, jdbcType=NUMERIC} ,
		    BIZ_NO = #{biz_no, jdbcType=NUMERIC} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    CLAIM_PAY = #{claim_pay, jdbcType=NUMERIC} ,
		    ACCU_AMOUNT = #{accu_amount, jdbcType=NUMERIC} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    APPLY_NUM = #{apply_num, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    BATCH_ID = #{batch_id, jdbcType=NUMERIC} ,
		    PAYEE_NUM = #{payee_num, jdbcType=NUMERIC} ,
		    ORGAN_CODE = #{organ_code, jdbcType=VARCHAR},
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimSurveyTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIZ_TYPE, A.BIZ_NO, A.CUSTOMER_NAME, A.CUSTOMER_ID, A.CLAIM_PAY, 
			A.ACCU_AMOUNT, A.CHANNEL_TYPE, A.APPLY_NUM, A.BATCH_ID, A.LIST_ID, A.ORGAN_CODE,
			A.PAYEE_NUM FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE 1 = 1  ]]>
		<include refid="queryClaimSurveyTaskByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimSurveyTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIZ_TYPE, A.BIZ_NO, A.CUSTOMER_NAME, A.CUSTOMER_ID, A.CLAIM_PAY, 
			A.ACCU_AMOUNT, A.CHANNEL_TYPE, A.APPLY_NUM, A.BATCH_ID, A.LIST_ID, A.ORGAN_CODE,
			A.PAYEE_NUM FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimSurveyTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIZ_TYPE, A.BIZ_NO, A.CUSTOMER_NAME, A.CUSTOMER_ID, A.CLAIM_PAY, 
			A.ACCU_AMOUNT, A.CHANNEL_TYPE, A.APPLY_NUM, A.BATCH_ID, A.LIST_ID, A.ORGAN_CODE,
			A.PAYEE_NUM FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimSurveyTaskWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<select id="findAllClaimSurveyTaskByConditions" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT distinct A.BIZ_NO,A.BIZ_TYPE,A.BATCH_ID FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE 1=1 AND A.BATCH_ID = #{batch_id} AND A.BIZ_TYPE = #{biz_type}]]>
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimSurveyTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE 1 = 1  ]]>
		<include refid="queryClaimSurveyTaskByPolicyBatch" /> 
	</select>
	
<!-- 分页查询操作 -->
	<select id="queryClaimSurveyTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BIZ_TYPE, B.BIZ_NO, B.CUSTOMER_NAME, B.CUSTOMER_ID, B.CLAIM_PAY, 
			B.ACCU_AMOUNT, B.CHANNEL_TYPE, B.APPLY_NUM, B.BATCH_ID, B.LIST_ID, B.ORGAN_CODE,
			B.PAYEE_NUM FROM (
					SELECT ROWNUM RN, A.BIZ_TYPE, A.BIZ_NO, A.CUSTOMER_NAME, A.CUSTOMER_ID, A.CLAIM_PAY, 
			A.ACCU_AMOUNT, A.CHANNEL_TYPE, A.APPLY_NUM, A.BATCH_ID, A.LIST_ID, A.ORGAN_CODE,
			A.PAYEE_NUM FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="queryClaimSurveyTaskByPolicyBatch" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 查询个数操作(按customerid查询，customerId不重复) -->
	<select id="findClaimSurveyTaskOfCustomerIdTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select count(1)
                         FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE 1 = 1  ]]>
				<include refid="queryClaimSurveyTaskByPolicyBatch" /> 
	</select>
	
<!-- 分页查询操作 (按customerid查询，customerId不重复)-->
	<select id="queryClaimSurveyTaskOfCustomerIdForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,
				       B.BIZ_TYPE,
				       B.BIZ_NO,
				       B.CUSTOMER_NAME,
				       B.CUSTOMER_ID,
				       B.CLAIM_PAY,
				       B.ACCU_AMOUNT,
				       B.CHANNEL_TYPE,
				       B.APPLY_NUM,
				       B.BATCH_ID,
				       B.LIST_ID,
				       B.ORGAN_CODE,
				       B.PAYEE_NUM
				  from (SELECT ROWNUM RN,
				               A.BIZ_TYPE,
				               A.BIZ_NO,
				               A.CUSTOMER_NAME,
				               A.CUSTOMER_ID,
				               A.CLAIM_PAY,
				               A.ACCU_AMOUNT,
				               A.CHANNEL_TYPE,
				               A.APPLY_NUM,
				               A.BATCH_ID,
				               A.LIST_ID,
				               A.ORGAN_CODE,
				               A.PAYEE_NUM from
				         APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE 1 = 1 and rownum < = #{LESS_NUM}]]>
				          <include refid="queryClaimSurveyTaskByPolicyBatch" />
				          <![CDATA[  ORDER BY A.LIST_ID) B WHERE B.RN >  #{GREATER_NUM} ]]>
	</select>
	
	<!--add by zhangjy_wb 查询手工抽取前置调查任务结果信息 -->
    <select id="queryExtractBfcheckTaskResultTotal" parameterType="java.util.Map"
        resultType="java.lang.Integer">
             <![CDATA[select count(1)
                           from APP___PAS__DBUSER.T_Contract_Master cm, APP___PAS__DBUSER.T_Customer ct, APP___PAS__DBUSER.T_insured_list l
                          where l.customer_id = ct.customer_id
                            and l.policy_id = cm.policy_id]]>
        <if test="channelType !=null and channelType != ''"> 
                            <![CDATA[  and cm.channel_type in (${channelType}) ]]>
                            <![CDATA[  and cm.liability_state = #{liability_state} ]]>
        </if>
        <if
            test="startDate !=null and startDate != '' and endDate !=null and endDate != ''"> 
                            <![CDATA[  and cm.validate_date >= #{startDate}]]>
                            <![CDATA[  and cm.validate_date <= #{endDate}]]>
        </if>
        <if
            test="startDate !=null and startDate != '' and endDate !=null and endDate != '' and channelType ==null and channelType == ''"> 
                            <![CDATA[  and cm.liability_state = #{liability_state} ]]>
        </if>
        <if test="organCode !=null and organCode != ''"> 
                              <![CDATA[  and cm.organ_code = #{organCode} ]]>
        </if>
    </select>
    <select id="queryExtractBfcheckTaskResultForPage" parameterType="java.util.Map"
        resultType="java.util.Map">
        <![CDATA[ 
        select B.* from(
           select A.*,rownum RN from(
                             select distinct cm.policy_code,
                                             cm.validate_date,
                                             cm.expiry_date,
                                             cm.liability_state,
                                             cm.organ_code,
                                             cm.channel_type,
                                             ct.customer_certi_code,
                                             ct.customer_name
                               from APP___PAS__DBUSER.T_Contract_Master cm, APP___PAS__DBUSER.T_Customer ct, APP___PAS__DBUSER.T_insured_list l
                              where l.customer_id = ct.customer_id
                                and l.policy_id = cm.policy_id]]>
        <if test="channelType !=null and channelType != ''"> 
                                <![CDATA[  and cm.channel_type in (${channelType}) ]]>
                                <![CDATA[  and cm.liability_state = #{liability_state} ]]>
        </if>
        <if
            test="startDate !=null and startDate != '' and endDate !=null and endDate != ''"> 
                                <![CDATA[  and cm.validate_date >= #{startDate}]]>
                                <![CDATA[  and cm.validate_date <= #{endDate}]]>
        </if>
        <if
            test="startDate !=null and startDate != '' and endDate !=null and endDate != '' and channelType ==null and channelType == ''"> 
                                <![CDATA[  and cm.liability_state = #{liability_state} ]]>
        </if>
        <if test="organCode !=null and organCode != ''"> 
                              <![CDATA[  and cm.organ_code = #{organCode} ]]>
        </if>
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
    </select>
    
    <!-- 距离上次前置调查的时间-->
    <select id="checkSpaceDate"  parameterType="java.util.Map"  resultType="java.util.Map">
    	<![CDATA[ select trunc(sysdate -
		             (select B.apply_date
		                from (select app.apply_date
		                        from APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK task, APP___PAS__DBUSER.T_SURVEY_APPLY app
		                       where task.list_id = app.survey_rule_id
		                         and task.customer_id = #{customer_id}
		                       order by app.apply_date desc) B
		               where rownum = 1)) space_date
		 		 from dual ]]>

    </select>
    
    <select id="findSurveyTaskByCustomerId"  parameterType="java.util.Map"  resultType="java.util.Map">
    	<![CDATA[ select * from APP___PAS__DBUSER.T_SURVEY_APPLY app, APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK task
					 where app.SURVEY_RULE_ID = task.list_id
					  and app.survey_status = #{survey_status}
					  and task.customer_id = #{customer_id} ]]>
    </select>
    
    <select id="findSurveyTaskByBatchIdAndBizNo" parameterType="java.util.Map"  resultType="java.util.Map">
    	<![CDATA[ SELECT A.BIZ_TYPE, A.BIZ_NO, A.CUSTOMER_NAME, A.CUSTOMER_ID, A.CLAIM_PAY, 
			A.ACCU_AMOUNT, A.CHANNEL_TYPE, A.APPLY_NUM, A.BATCH_ID, A.LIST_ID, A.ORGAN_CODE,
			A.PAYEE_NUM FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK A WHERE 1 = 1  ]]>
		<include refid="claimSurveyTaskWhereCondition" />
    </select>
    
</mapper>
