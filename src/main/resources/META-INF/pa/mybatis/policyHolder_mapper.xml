<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.PolicyHolderDaoImpl">

	<sql id="PA_policyHolderWhereCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND A.SOCI_SECU = #{soci_secu} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" job_underwrite != null and job_underwrite != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{job_underwrite} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" in_customer_id  != null"><![CDATA[ or tl.customer_id = #{in_customer_id} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.POLICY_ID = #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND A.AGENT_RELATION = #{agent_relation} ]]></if>
		<if test=" new_resident != null and new_resident != ''  "><![CDATA[ AND A.NEW_RESIDENT = #{new_resident} ]]></if>
		<if test=" exception_health_flag != null and exception_health_flag != ''  "><![CDATA[ AND A.EXCEPTION_HEALTH_FLAG = #{exception_health_flag} ]]></if>	
		<if test=" educational_background != null and educational_background != ''  "><![CDATA[ AND A.EDUCATIONAL_BACKGROUND = #{educational_background} ]]></if>
		<if test=" risk_estimate_score != null and risk_estimate_score != ''  "><![CDATA[ AND A.RISK_ESTIMATE_SCORE = #{risk_estimate_score} ]]></if>
		<if test=" non_smoker != null and non_smoker != ''  "><![CDATA[ AND A.NON_SMOKER = #{non_smoker} ]]></if>
		<if test=" exception_health_flag != null and exception_health_flag != ''  "><![CDATA[ AND A.EXCEPTION_HEALTH_FLAG = #{exception_health_flag} ]]></if>
	</sql>

<sql id="findHolderCustomerorinsuredCustomerIdByPolicyCodeAndName_cjk">
		<if test=" policy_code  != null "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" customer_name  != null "><![CDATA[ AND D.CUSTOMER_NAME = #{customer_name} ]]></if>
	</sql>	
<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyHolderByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyHolderByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyHolderByPolicyCodeCondition">
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="PA_queryPolicyHolderByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	
	
<!-- 添加操作 -->
	<insert id="PA_addPolicyHolder"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_HOLDER__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_HOLDER(
				ADDRESS_ID, INSERT_TIME, CUSTOMER_HEIGHT, CUSTOMER_ID, JOB_CODE, UPDATE_TIME, CUSTOMER_WEIGHT, 
				APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, JOB_UNDERWRITE, LIST_ID, UPDATE_TIMESTAMP, 
				INSERT_BY, POLICY_ID, SOCI_SECU, SMOKING,ANNUAL_INCOME_CEIL,INCOME_SOURCE,RESIDENT_TYPE,AGENT_RELATION,APPLICANT_SPE_PEOPLE,NEW_RESIDENT,EXCEPTION_HEALTH_FLAG,
				EDUCATIONAL_BACKGROUND,RISK_ESTIMATE_SCORE,NON_SMOKER ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{customer_height, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} , #{job_code, jdbcType=VARCHAR} , SYSDATE , #{customer_weight, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{job_underwrite, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{soci_secu, jdbcType=NUMERIC} , #{smoking, jdbcType=NUMERIC}, #{annual_income_ceil, jdbcType=NUMERIC}, #{income_source, jdbcType=VARCHAR}
				, #{resident_type, jdbcType=VARCHAR} , #{agent_relation, jdbcType=VARCHAR},#{applicant_spe_people, jdbcType=NUMERIC},#{new_resident, jdbcType=NUMERIC},#{exception_health_flag, jdbcType=NUMERIC}
				, #{educational_background, jdbcType=VARCHAR} ,#{risk_estimate_score, jdbcType=NUMERIC},#{non_smoker, jdbcType=VARCHAR})  
		 ]]>
	</insert>
<!-- 删除操作 -->	
	<delete id="PA_deletePolicyHolder" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_HOLDER WHERE  LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyHolder" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_HOLDER ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
		    SMOKING = #{smoking, jdbcType=NUMERIC} ,
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC} ,
		    INCOME_SOURCE = #{income_source, jdbcType=VARCHAR} ,
		    RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR} ,
		    AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR} ,
		    APPLICANT_SPE_PEOPLE = #{applicant_spe_people, jdbcType=NUMERIC},
		    NEW_RESIDENT = #{new_resident, jdbcType=NUMERIC},
		    EXCEPTION_HEALTH_FLAG = #{exception_health_flag, jdbcType=NUMERIC},
		    EDUCATIONAL_BACKGROUND = #{educational_background, jdbcType=VARCHAR},
		    RISK_ESTIMATE_SCORE = #{risk_estimate_score, jdbcType=NUMERIC},
		    NON_SMOKER = #{non_smoker, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyHolderByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE, AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1 ]]>
		<include refid="PA_queryPolicyHolderByListIdCondition" />
	</select>
	
	<select id="PA_findPolicyHolderByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyHolderByPolicyIdCondition" />
	</select>
	
	<select id="findPolicyHolderByPolicyCode1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE ROWNUM = 1 ]]>
		<include refid="PA_queryPolicyHolderByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findPolicyHolderByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1   ]]>
		<include refid="PA_queryPolicyHolderByAddressIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE   ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE  ROWNUM <=  1000  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyHolderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyHolderForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_HEIGHT, B.CUSTOMER_ID, B.JOB_CODE, B.CUSTOMER_WEIGHT, B.APPLICANT_SPE_PEOPLE,
			B.APPLY_CODE, B.POLICY_CODE, B.JOB_UNDERWRITE, B.LIST_ID, B.SOCI_SECU, B.SMOKING,B.ANNUAL_INCOME_CEIL,B.INCOME_SOURCE,B.RESIDENT_TYPE,AGENT_RELATION,
			B.POLICY_ID,B.NEW_RESIDENT,B.EXCEPTION_HEALTH_FLAG,B.EDUCATIONAL_BACKGROUND,B.RISK_ESTIMATE_SCORE,B.NON_SMOKER  FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 查询单条 -->
	<select id="PA_findPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.APPLICANT_SPE_PEOPLE,
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
<!--  -->
<select id="PA_findPolicyHolderCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.APPLICANT_SPE_PEOPLE,
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />
		  union
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.APPLICANT_SPE_PEOPLE,
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.POLICY_ID,A.NEW_RESIDENT,A.EXCEPTION_HEALTH_FLAG,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER  FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />  
	</select>
	<!--查询客户信息  -->	
<select id="PA_findPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UN_CUSTOMER_CODE, A.OLD_CUSTOMER_ID, A.CUSTOMER_ID, A.CUSTOMER_NAME, 
			A.CUSTOMER_WEIGHT, A.CUSTOMER_GENDER, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_CERTI_CODE, A.CUSTOMER_LEVEL
			FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />	  
</select>
<!-- 老核心改为查询投保人地址 -->
	<select id="findPolicyHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
       <![CDATA[

			 SELECT A1.CUSTOMER_NAME,
		        floor(months_between(A1.ISSUE_DATE,A1.CUSTOMER_BIRTHDAY) / 12) AS AGE,
		        A1.JOB_UNDERWRITE, 
		        A1.CUSTOMER_BIRTHDAY,
		        A1.GENDER_DESC,
		        A1.TYPE_NAME,
		        A1.CUSTOMER_VIP,
		        A1.MARRIAGE_STATUS,
		        A1.MARRIAGE,
		        A1.COUNTRY_CODE,
		        A1.COUNTRY_NAME,
		        A1.CUSTOMER_CERT_TYPE,
		        A1.TYPE,
		        A1.CUSTOMER_CERTI_CODE,
		        A1.CUSTOMER_ID,
		       A1.DRIVER_LICENSE_TYPE,
		        A1.LICENSE_DESC,
		        A1.JOB_CODE,
		       A1.JOB_UW_LEVEL,
		        A1.JOB_CATEGORY,
           		A1.JOB_CATEGORY_NAME,
		       A1.CATEGORY_JOB_DESC,
		        A1.KIND_JOB_DESC,
		        P.MOBILE_TEL,
		        A1.OFFICE_TEL,
		        A1.OFFEN_USE_TEL,
		        A1.FAX_TEL,
		        A1.HOUSE_TEL,
		        P.ADDRESS,
		        P.POST_CODE,
		        P.EMAIL,
		        P.STATE,
		        QSTATE.NAME            AS STATENAME,
		        P.CITY,
		        QCITY.NAME             AS CITYNAME,
		        P.DISTRICT,
		        QDISTRICT.NAME         AS DISTRICTNAME,
		        P.TOWN_STREET_CODE,
		        P.TOWN_STREET_NAME,
		        D.NEXT_ACCOUNT_BANK,
		        (select TB.BANK_BRANCH_NAME from APP___PAS__DBUSER.T_BANK_BRANCH TB where TB.BANK_CODE = D.NEXT_ACCOUNT_BANK and rownum=1)  AS BANK_NAME,
		        D.NEXT_ACCOUNT,
		        D.NEXT_ACCOUNT_NAME,
		        D.PAY_NEXT ,
		        D.PAY_LOCATION,
		        N.NAME,
		        A1.COMPANY_NAME,
		        A1.APPLY_DATE,
		        A1.CUSTOMER_GENDER,
		        A1.ORGAN_CODE,
		        TU.ORGAN_NAME,
		        TJU.JOB_UW_LEVEL_NAME,
		        TJU.JOB_UW_LEVEL_CODE,
		        A1.CUST_CERT_STAR_DATE,
		        A1.CUST_CERT_END_DATE,
				A1.OPERATOR_NAME,
				A1.OPERATOR_CERTI_TYPE,
				A1.OPERATOR_CERTI_CODE
		   FROM (SELECT B.POLICY_ID,
		                B.JOB_CODE,
		                B.JOB_UNDERWRITE,
		                B.ADDRESS_ID,
		                A.CUSTOMER_ID,
		                A.UN_CUSTOMER_CODE,
		                A.OLD_CUSTOMER_ID,
		                A.MARRIAGE_DATE,
		                A.EDUCATION,
		                A.CUSTOMER_NAME,
		                A.CUSTOMER_BIRTHDAY,
		                A.CUSTOMER_GENDER,
		                A.CUSTOMER_HEIGHT,
		                A.CUSTOMER_WEIGHT,
		                A.CUSTOMER_CERT_TYPE,
		                A.CUSTOMER_CERTI_CODE,
		                A.CUSTOMER_ID_CODE,
		                A.CUST_CERT_STAR_DATE,
		                A.CUST_CERT_END_DATE,
		                A.JOB_NATURE,
		                A.JOB_TITLE,
		                A.MARRIAGE_STATUS,
		                A.IS_PARENT,
		                A.ANNUAL_INCOME,
		                A.COUNTRY_CODE,
		                A.RELIGION_CODE,
		                A.NATION_CODE,
		               A.DRIVER_LICENSE_TYPE,
		                A.COMPANY_NAME,
		                A.OFFEN_USE_TEL,
		                A.HOUSE_TEL,
		                A.FAX_TEL,
		                A.OFFICE_TEL,
		                A.MOBILE_TEL,
		                A.EMAIL,
		                A.QQ,
		                A.WECHAT_NO,
		                A.OTHER,
		                A.CUSTOMER_LEVEL,
		                A.CUSTOMER_RISK_LEVEL,
		                A.CUSTOMER_VIP,
		                A.SMOKING_FLAG,
		                A.DRUNK_FLAG,
		                A.BLACKLIST_FLAG,
		                A.HOUSEKEEPER_FLAG,
		                A.SYN_MDM_FLAG,
		                A.LIVE_STATUS,
		                A.RETIRED_FLAG,
		                A.DEATH_DATE,
		                A.HEALTH_STATUS,
		                A.REMARK,
		                A.CUST_PWD,
		                E.GENDER_DESC,
		                F.TYPE_NAME,
		                G.MARRIAGE,
		                H.COUNTRY_NAME,
		                J.TYPE,
		               K.LICENSE_DESC,
		                TM.APPLY_DATE,
		                TM.ORGAN_CODE,
		                TM.ISSUE_DATE,
		                L.JOB_NAME           AS CATEGORY_JOB_DESC,
		                L.JOB_UW_LEVEL,
		                M.JOB_UW_LEVEL_NAME   AS KIND_JOB_DESC,
		                L.JOB_CATEGORY , 
                    	(SELECT AA.JOB_CATEGORY_NAME FROM DEV_PAS.T_JOB_CATEGORY AA WHERE AA.JOB_CATEGORY_CODE = L.JOB_CATEGORY) JOB_CATEGORY_NAME,
						N.OPERATOR_NAME,
						N.OPERATOR_CERTI_TYPE,
						N.OPERATOR_CERTI_CODE
		           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TM,
		                APP___PAS__DBUSER.T_POLICY_HOLDER B,
		               APP___PAS__DBUSER.T_CUSTOMER        A
		           LEFT JOIN APP___PAS__DBUSER.T_GENDER E
		             ON E.GENDER_CODE = A.CUSTOMER_GENDER
		           LEFT JOIN APP___PAS__DBUSER.T_YES_NO F
		             ON F.YES_NO = A.CUSTOMER_VIP
		           LEFT JOIN APP___PAS__DBUSER.T_MARRIAGE G
		             ON G.MARRIAGE_CODE = A.MARRIAGE_STATUS
		           LEFT JOIN APP___PAS__DBUSER.T_COUNTRY H
		             ON H.COUNTRY_CODE = A.COUNTRY_CODE
		           LEFT JOIN APP___PAS__DBUSER.T_CERTI_TYPE J
		             ON J.CODE = A.CUSTOMER_CERT_TYPE
		           LEFT JOIN APP___PAS__DBUSER.T_LICENSE_TYPE K
		             ON K.LICENSE_TYPE = A.DRIVER_LICENSE_TYPE
		           LEFT JOIN APP___PAS__DBUSER.T_JOB_CODE L
		             ON L.JOB_CODE =A.Job_Code
		           LEFT JOIN APP___PAS__DBUSER.t_Job_Underwrite M
		             ON JOB_UW_LEVEL_CODE = A.JOB_KIND
		           LEFT JOIN APP___PAS__DBUSER.T_TRUST_COMPANY N
					 ON N.CUSTOMER_ID = A.CUSTOMER_ID
		             WHERE A.CUSTOMER_ID = B.CUSTOMER_ID
		            AND TM.POLICY_ID = B.POLICY_ID
		           AND B.POLICY_ID=TM.POLICY_ID
		            AND B.POLICY_CODE =#{policy_code}) A1
		   LEFT JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT D
		     ON D.POLICY_ID = A1.POLICY_ID
		   LEFT JOIN APP___PAS__DBUSER.T_PAY_MODE N
		     ON N.CODE = D.PAY_NEXT
		   LEFT JOIN APP___PAS__DBUSER.T_ADDRESS P
		     ON P.ADDRESS_ID = A1.ADDRESS_ID
		   LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QSTATE
		     ON QSTATE.CODE = P.STATE
		   LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QCITY
		     ON QCITY.CODE = P.CITY
		   LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QDISTRICT
		     ON QDISTRICT.CODE = P.DISTRICT
		   LEFT JOIN APP___PAS__DBUSER.t_Udmp_Org TU
		     ON TU.ORGAN_CODE = a1.ORGAN_CODE
		   LEFT JOIN APP___PAS__DBUSER.T_JOB_UNDERWRITE TJU
		     ON TJU.JOB_UW_LEVEL_CODE = A1.JOB_UW_LEVEL
 
           ]]> 
	</select>
	
	<select id="PA_findPolicyHolderInformationByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select tc.customer_name as appnt_name,th.address_id,
		(case when tc.customer_gender = '1'
         then '男'
           else '女'
             end ) as customer_gender,
         tc.offen_use_tel as phone,
         tc.customer_birthday as appnt_birthday,
         th.customer_id ,
          (select td.name
            from APP___PAS__DBUSER.T_address ta,APP___PAS__DBUSER.t_District td
           where ta.address_id = th.address_id and td.code = ta.state)||
          ( select td.name
            from APP___PAS__DBUSER.T_address ta,APP___PAS__DBUSER.t_District td
           where ta.address_id = th.address_id and td.code = ta.city)||
           (select td.name
            from APP___PAS__DBUSER.T_address ta,APP___PAS__DBUSER.t_District td
           where ta.address_id = th.address_id and td.code = ta.district) ||
           (select ta.address
           from APP___PAS__DBUSER.T_address ta
           where ta.address_id = th.address_id) as postal_address,
         (select ta.post_code
            from APP___PAS__DBUSER.T_address ta
           where ta.address_id = th.address_id) as postal_code,
         (select tcm.organ_code 
            from APP___PAS__DBUSER.t_contract_master tcm
            where tcm.policy_id = th.policy_id) as organ_code ,
          (select tsc.sales_channel_name
                  from APP___PAS__DBUSER.t_Contract_Agent tca,
                       APP___PAS__DBUSER.t_Agent          taa,
                       APP___PAS__DBUSER.t_Sales_Channel tsc
                 where tca.agent_code = taa.agent_code and tsc.sales_channel_code = taa.agent_channel
                   and tca.policy_code = th.policy_code and tca.is_current_agent = '1') as SUBMIT_CHANNEL,
            (select tca.agent_name
                  from APP___PAS__DBUSER.t_Contract_Agent tca,
                       APP___PAS__DBUSER.t_Agent          ta
                 where tca.agent_code = ta.agent_code
                   and tca.policy_code = th.policy_code and tca.is_current_agent = '1' ) as AGENT_NAME,
               (select tca.agent_code
                  from APP___PAS__DBUSER.t_Contract_Agent tca,
                       APP___PAS__DBUSER.t_Agent          ta
                 where tca.agent_code = ta.agent_code
                   and tca.policy_code = th.policy_code and tca.is_current_agent = '1') as AGENT_CODE,
            (SELECT TCY.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCY WHERE tc.COUNTRY_CODE = TCY.COUNTRY_CODE ) COUNTRY_NAME, 
			tc.COUNTRY_CODE
    from APP___PAS__DBUSER.T_Customer tc, APP___PAS__DBUSER.T_POLICY_HOLDER th
   where tc.customer_id = th.customer_id
     and th.policy_id = #{policy_id}  ]]>
	</select>
	
	<!-- 再保险 受益人查询 -->
	<select id="findCustomerInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    SELECT C.CUSTOMER_ID,
					        C.CUSTOMER_NAME,
					        C.CUSTOMER_GENDER,
					        C.CUSTOMER_CERT_TYPE,
					        C.CUSTOMER_CERTI_CODE,
					        C.RURAL_POPULATION_FLAG,
					        C.DISABILITY_FLAG,
					        C.CUSTOMER_RISK_LEVEL,
					        C.DISABILITY_NO,
		                  	C.CUST_CERT_STAR_DATE,
		                  	C.CUST_CERT_END_DATE,
		                  	TA.MOBILE_TEL,
		                  	TA.EMAIL,
		                  	TA.COUNTRY_CODE,
		                  	(SELECT TCY.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCY WHERE TA.COUNTRY_CODE = TCY.COUNTRY_CODE ) COUNTRY_NAME,
		                  	C.JOB_NATURE,
		                  	(SELECT TJN.JOB_NATURE FROM APP___PAS__DBUSER.T_JOB_NATURE TJN WHERE TJN.JOB_NATURE_CODE = C.JOB_NATURE) AS JOB_NATURE_NAME,
		                  	(SELECT TJC.JOB_NAME FROM APP___PAS__DBUSER.T_JOB_CODE TJC where TJC.JOB_CODE = C.JOB_CODE) JOB_NAME,
		                  	TA.STATE,
		                  	(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.Code = TA.STATE) STATE_NAME,
		                  	TA.CITY,
		                  	(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.Code = TA.CITY) CITY_NAME,
		                  	TA.DISTRICT,
		                  	(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.Code = TA.DISTRICT) DISTRICT_NAME,
		                  	TA.ADDRESS,
		                  	C.BLACKLIST_FLAG,
		                  	C.CUSTOMER_BIRTHDAY,
		                  	TA.TOWN_STREET_CODE,
		                  	TA.TOWN_STREET_NAME
					   FROM APP___PAS__DBUSER.T_CUSTOMER      C,
					        APP___PAS__DBUSER.T_POLICY_HOLDER PH,
                  			APP___PAS__DBUSER.T_ADDRESS TA
					  WHERE 1 = 1
					    AND PH.CUSTOMER_ID = C.CUSTOMER_ID
					    AND PH.ADDRESS_ID = TA.ADDRESS_ID 
			            AND PH.POLICY_CODE = #{policy_code} ]]>
	</select>
	
	<!-- Modify by yangbo_wb  2016-08-09  FIX TC 缺陷ID-9275：响应报文字段ExitReason（出境事由），应取：T_CONTRACT_PRODUCT_OTHER.Field16。 -->
	<select id="findPolicyHolderInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
      SELECT A1.ADDRESS_ID, A1.CUSTOMER_ID, A1.UN_CUSTOMER_CODE, A1.POLICY_ID, TCRO.FIELD16,
      A1.OLD_CUSTOMER_ID, A1.MARRIAGE_DATE, A1.EDUCATION, 
      A1.CUSTOMER_NAME, A1.CUSTOMER_BIRTHDAY, A1.CUSTOMER_GENDER, 
      A1.CUSTOMER_HEIGHT, A1.CUSTOMER_WEIGHT, A1.CUSTOMER_CERT_TYPE, 
      A1.CUSTOMER_CERTI_CODE, A1.CUSTOMER_ID_CODE, 
      A1.CUST_CERT_STAR_DATE, A1.CUST_CERT_END_DATE, 
      A1.JOB_CODE, A1.JOB_NATURE, A1.JOB_KIND, A1.JOB_TITLE, 
      A1.MARRIAGE_STATUS, A1.IS_PARENT, A1.ANNUAL_INCOME, 
      A1.COUNTRY_CODE, A1.RELIGION_CODE, A1.NATION_CODE, 
      A1.DRIVER_LICENSE_TYPE, A1.COMPANY_NAME, A1.OFFEN_USE_TEL, 
      A1.HOUSE_TEL, A1.FAX_TEL, A1.OFFICE_TEL, A1.MOBILE_TEL, 
      A1.EMAIL, A1.QQ, A1.WECHAT_NO, A1.OTHER, A1.CUSTOMER_LEVEL, 
      A1.CUSTOMER_RISK_LEVEL, A1.CUSTOMER_VIP, A1.SMOKING_FLAG, 
      A1.DRUNK_FLAG, A1.BLACKLIST_FLAG, A1.HOUSEKEEPER_FLAG, 
      A1.SYN_MDM_FLAG, A1.LIVE_STATUS, A1.RETIRED_FLAG, A1.DEATH_DATE, 
      A1.HEALTH_STATUS, A1.REMARK, A1.CUST_PWD,  
      A1.GENDER_DESC, A1.TYPE_NAME, A1.MARRIAGE, A1.COUNTRY_NAME, A1.TYPE, A1.LICENSE_DESC, 
      A1.CATEGORY_JOB_DESC, A1.KIND_JOB_DESC, 
	  D.NEXT_ACCOUNT, D.NEXT_ACCOUNT_NAME, D.NEXT_ACCOUNT_BANK, D.NEXT_ACCOUNT_ID, D.PAY_NEXT, 
      N.NAME, P.ADDRESS, P.POST_CODE, P.STATE, P.CITY, P.DISTRICT,
      QSTATE.NAME AS STATENAME, QCITY.NAME AS CITYNAME, QDISTRICT.NAME AS DISTRICTNAME ,A1.JOB_UNDERWRITE,
       ( select sheng.name||shi.name||xian.name
      from dev_pas.t_district sheng
      left join dev_pas.t_district shi
        on shi.parent_code = sheng.code
      left join dev_pas.t_district xian
        on xian.parent_code = shi.code
     where xian.code =P.district ) AS SSX
      FROM
      ( SELECT B.POLICY_ID, B.ADDRESS_ID, A.CUSTOMER_ID, A.UN_CUSTOMER_CODE, 
      A.OLD_CUSTOMER_ID, A.MARRIAGE_DATE, A.EDUCATION, 
      A.CUSTOMER_NAME, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_GENDER, 
      A.CUSTOMER_HEIGHT, A.CUSTOMER_WEIGHT, A.CUSTOMER_CERT_TYPE, 
      A.CUSTOMER_CERTI_CODE, A.CUSTOMER_ID_CODE, 
      A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
      A.JOB_CODE, A.JOB_NATURE, A.JOB_KIND, A.JOB_TITLE, 
      A.MARRIAGE_STATUS, A.IS_PARENT, A.ANNUAL_INCOME, 
      A.COUNTRY_CODE, A.RELIGION_CODE, A.NATION_CODE, 
      A.DRIVER_LICENSE_TYPE, A.COMPANY_NAME, A.OFFEN_USE_TEL, 
      A.HOUSE_TEL, A.FAX_TEL, A.OFFICE_TEL, A.MOBILE_TEL, 
      A.EMAIL, A.QQ, A.WECHAT_NO, A.OTHER, A.CUSTOMER_LEVEL, 
      A.CUSTOMER_RISK_LEVEL, A.CUSTOMER_VIP, A.SMOKING_FLAG, 
      A.DRUNK_FLAG, A.BLACKLIST_FLAG, A.HOUSEKEEPER_FLAG, 
      A.SYN_MDM_FLAG, A.LIVE_STATUS, A.RETIRED_FLAG, A.DEATH_DATE, 
      A.HEALTH_STATUS, A.REMARK, A.CUST_PWD,  
      E.GENDER_DESC, F.TYPE_NAME, G.MARRIAGE, H.COUNTRY_NAME, J.TYPE, K.LICENSE_DESC, 
      L.JOB_CATEGORY_NAME AS CATEGORY_JOB_DESC, M.JOB_DESC AS KIND_JOB_DESC ,B.JOB_UNDERWRITE
      FROM APP___PAS__DBUSER.T_POLICY_HOLDER B, APP___PAS__DBUSER.T_CUSTOMER A
      LEFT JOIN APP___PAS__DBUSER.T_GENDER E ON E.GENDER_CODE = A.CUSTOMER_GENDER 
      LEFT JOIN APP___PAS__DBUSER.T_YES_NO F ON F.YES_NO = A.CUSTOMER_VIP 
      LEFT JOIN APP___PAS__DBUSER.T_MARRIAGE G ON G.MARRIAGE_CODE = A.MARRIAGE_STATUS 
      LEFT JOIN APP___PAS__DBUSER.T_COUNTRY H ON H.COUNTRY_CODE = A.COUNTRY_CODE 
      LEFT JOIN APP___PAS__DBUSER.T_CERTI_TYPE J ON J.CODE = A.CUSTOMER_CERT_TYPE 
      LEFT JOIN APP___PAS__DBUSER.T_LICENSE_TYPE K ON K.LICENSE_TYPE = A.DRIVER_LICENSE_TYPE 
      LEFT JOIN APP___PAS__DBUSER.T_JOB_CATEGORY L ON L.JOB_CATEGORY_CODE = A.JOB_CODE 
      LEFT JOIN APP___PAS__DBUSER.T_JOB_KIND M ON M.JOB_KIND = A.JOB_KIND
      WHERE 
      A.CUSTOMER_ID = B.CUSTOMER_ID 
      AND B.POLICY_CODE =  #{policy_code} ) A1
      LEFT JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT D ON D.POLICY_ID = A1.POLICY_ID
      LEFT JOIN APP___PAS__DBUSER.T_PAY_MODE N ON N.CODE = D.PAY_NEXT
      LEFT JOIN APP___PAS__DBUSER.T_ADDRESS P ON P.ADDRESS_ID = A1.ADDRESS_ID     
      LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QSTATE ON QSTATE.CODE = P.STATE
      LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QCITY ON QCITY.CODE = P.CITY
      LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QDISTRICT ON QDISTRICT.CODE = P.DISTRICT 
      LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER TCRO ON TCRO.POLICY_ID = A1.POLICY_ID]]>
	</select>
	<!--by zhaoyoan_wb 根据保单号和客户姓名查询客户id(被保人或投保人) -->
	<select id="findInsuredOrHolderCustomerIdByPolicyCodeAndName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,A.CUSTOMER_ID,B.CUSTOMER_NAME,A.POLICY_ID
			FROM APP___PAS__DBUSER.T_INSURED_LIST A LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER B ON A.CUSTOMER_ID=B.CUSTOMER_ID
			WHERE A.POLICY_CODE=#{policy_code}
			AND B.CUSTOMER_NAME=#{customer_name} 
      		union
      		SELECT C.POLICY_CODE,C.CUSTOMER_ID,D.CUSTOMER_NAME,C.POLICY_ID
			FROM APP___PAS__DBUSER.T_POLICY_HOLDER C LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER D ON C.CUSTOMER_ID=D.CUSTOMER_ID
			WHERE C.POLICY_CODE=#{policy_code} 
			AND D.CUSTOMER_NAME=#{customer_name} 
		]]>	
	</select>
	<!--保单投保人信息AC变更成功次数  -->
	<select id="findACSuccessCountBypolicyCodeAndcustomerId"  resultType="java.lang.Integer" parameterType="java.util.Map">
<![CDATA[
	 SELECT COUNT(1) AS ACSuccessCount
   FROM APP___PAS__DBUSER.T_POLICY_CHANGE D
  WHERE  D.POLICY_ID IN
        (SELECT A.POLICY_ID
           FROM 
                APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A,
                APP___PAS__DBUSER.T_POLICY_CHANGE    B,
                APP___PAS__DBUSER.T_CS_CUSTOMER      C
          WHERE A.POLICY_CHG_ID = B.POLICY_CHG_ID
            AND A.CUSTOMER_ID = C.CUSTOMER_ID
            AND A.POLICY_CODE=#{policy_code}
            AND B.SERVICE_CODE='CC' AND C.OLD_NEW=1
            
          )
]]>	
	</select>
	<!--  -->
	<select id="findHolderCustomerIdByPolicyCodeAndName_cjk" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[
	 SELECT C.POLICY_CODE,C.CUSTOMER_ID,D.CUSTOMER_NAME,C.POLICY_ID
      FROM APP___PAS__DBUSER.T_POLICY_HOLDER C LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER D ON C.CUSTOMER_ID=D.CUSTOMER_ID
      WHERE 1=1
      ]]>
      <include refid="findHolderCustomerorinsuredCustomerIdByPolicyCodeAndName_cjk" />
     	
       union 
     <![CDATA[  
       select C.POLICY_CODE,C.CUSTOMER_ID,D.CUSTOMER_NAME,C.POLICY_ID
       from APP___PAS__DBUSER.T_INSURED_LIST C LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER D ON C.CUSTOMER_ID=D.CUSTOMER_ID
       WHERE 1=1
        ]]>
       <include refid="findHolderCustomerorinsuredCustomerIdByPolicyCodeAndName_cjk" />
   
	</select>
    
   
    <!-- 根据policyId查询保单投保人与代理人信息 -->
    <select id="findPolicyHolderAndAgentInfo" resultType="java.util.Map" parameterType="java.util.Map" >
    <![CDATA[
   select c.customer_id         as customer_id,
       c.customer_name       as customer_name,
       c.customer_gender     as customer_gender,
       c.company_name        as company_name,
       c.customer_cert_type  as customer_cert_type,
       c.customer_certi_code as customer_certi_code,
       c.customer_id_code    as customer_id_code,
       c.mobile_tel          as mobile_tel,
       c.house_tel           as house_tel,
       a.agent_organ_code    as agent_organ_code,
       a.agent_code          as agent_code,
       t.agent_name          as agent_name,
       a.last_agent_code     as last_agent_code,
       a.last_agent_name     as last_agent_name,
       a.agent_start_date    as agent_start_date,
       a.relation_to_ph      as relation_to_ph

    from APP___PAS__DBUSER.t_customer c, APP___PAS__DBUSER.t_policy_holder h, APP___PAS__DBUSER.T_CONTRACT_AGENT a,
  		APP___PAS__DBUSER.t_agent t
 where h.policy_code = a.policy_code
   and h.customer_id = c.customer_id
   and a.agent_code = t.agent_code
   and h.policy_id = #{policy_id}
   and a.is_current_agent = #{is_current_agent}
      ]]>   
    </select>
    
    <!-- 根据policyId查询被保险人和续期付款方式信息 -->
    <select id="findPolicyInsuredAndPayAcountInfo" resultType="java.util.Map" parameterType="java.util.Map" >
    <![CDATA[
    select c.customer_id       as customer_id,
       c.customer_name     as customer_name,
       c.mobile_tel        as mobile_tel,
       c.house_tel         as house_tel,
       i.relation_to_ph    as relation_to_ph,
       a.pay_location      as pay_location,
       a.next_account_bank as next_account_bank,
       a.next_account      as next_account,
       a.next_account_id   as next_account_id,
       c.customer_birthday  as customer_birthday

  from APP___PAS__DBUSER.t_customer c, APP___PAS__DBUSER.t_insured_list i, APP___PAS__DBUSER.T_PAYER_ACCOUNT a
 where i.policy_id = a.policy_id
   and i.customer_id = c.customer_id
   and i.policy_id = #{policy_id}
   and rownum = '1'
      ]]>   
    </select>
    <!-- 根据busiItemId查询第一被保险人和续期付款方式信息 -->
    <select id="findBusiInsuredAndPayAcountInfo" resultType="java.util.Map" parameterType="java.util.Map" >
    <![CDATA[
    select c.customer_id       as customer_id,
       c.customer_name     as customer_name,
       c.mobile_tel        as mobile_tel,
       c.house_tel         as house_tel,
       i.relation_to_ph    as relation_to_ph,
       a.pay_location      as pay_location,
       a.next_account_bank as next_account_bank,
       a.next_account      as next_account,
       a.next_account_id   as next_account_id,
       c.customer_birthday  as customer_birthday

  from APP___PAS__DBUSER.t_customer c, APP___PAS__DBUSER.t_insured_list i, APP___PAS__DBUSER.T_PAYER_ACCOUNT a,
	   APP___PAS__DBUSER.t_benefit_insured b
 where i.policy_id = a.policy_id
    and i.customer_id = c.customer_id
    and i.policy_id = b.policy_id
    and i.list_id = b.insured_id 
	and b.order_id = '1'
    and b.busi_item_id = #{busi_item_id}
    and rownum = '1'
      ]]>   
    </select>
    
    <select id="findPolicyHolderInfoForSendMessage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
				CU.OFFEN_USE_TEL, CU.MOBILE_TEL,CU.CUSTOMER_NAME,CU.CUSTOMER_GENDER,PH.POLICY_CODE,NVL(PA.ACCOUNT,PA.NEXT_ACCOUNT) AS NEXT_ACCOUNT
				FROM  APP___PAS__DBUSER.T_CS_POLICY_HOLDER PH
				LEFT JOIN 
				APP___PAS__DBUSER.T_CUSTOMER CU
				ON PH.CUSTOMER_ID =  CU.CUSTOMER_ID
				LEFT JOIN 
				APP___PAS__DBUSER.T_PAYER_ACCOUNT PA
				ON PH.POLICY_ID = PA.POLICY_ID
				WHERE 
				PH.POLICY_CODE = #{policy_code} AND PH.OLD_NEW = '1'
		  ]]>
	</select>
	
	
	<select id="findAllPolicyHolderNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT CM.POLICY_ID,
		       CM.POLICY_CODE,
		       CM.LIABILITY_STATE,
		       CM.VALIDATE_DATE,
		       CM.END_CAUSE,
		       CM.WINNING_START_FLAG,
		       PH.CUSTOMER_ID,
		       PA.ACKNOWLEDGE_DATE
		  FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		    ON PH.POLICY_ID = CM.POLICY_ID
		 INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT PA
		    ON PA.POLICY_ID = PH.POLICY_ID
		 WHERE 1=1 
		 ]]>
		 <if test=" customer_id  != null "><![CDATA[ AND PH.CUSTOMER_ID = ${customer_id} ]]></if>
		 <if test=" policy_code  != null "><![CDATA[ AND PH.POLICY_CODE = ${policy_code} ]]></if>
		 <![CDATA[ ORDER BY PH.POLICY_CODE ]]> 
	</select>
	
	<select id="PA_findCustomerHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
	<select id="PA_findCustomerList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />  
	</select>
	
	<select id="queryHolderOtherPolicyCodeByCode" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT TCBP.BUSI_ITEM_ID,
       TCBP.BUSI_PRD_ID,
       TCBP.BUSI_PROD_CODE,
       TCBP.APPLY_CODE,
       TCBP.POLICY_ID,
       TCBP.POLICY_CODE,
       TCBP.MASTER_BUSI_ITEM_ID,
       TCBP.VALIDATE_DATE
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
       DEV_PDS.T_BUSINESS_PRODUCT   TBP
 WHERE 1 = 1
   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
   AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
  
   AND TBP.PRODUCT_CATEGORY2 = '30004'
      
   AND TCM.POLICY_CODE IN
       (SELECT T.POLICY_CODE
          FROM DEV_PAS.T_POLICY_HOLDER T
         WHERE T.CUSTOMER_ID IN
               (SELECT TPH.CUSTOMER_ID
                  FROM DEV_PAS.T_POLICY_HOLDER TPH
                 WHERE TPH.POLICY_CODE = #{policy_code}))
   AND TCM.POLICY_CODE != #{policy_code}
   AND TCM.RELATION_POLICY_CODE = #{policy_code}
	</select>
	
	<!-- 查询投保人信息 -->
	<select id="findPolicyInfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[
	select a.customer_id,a.policy_id FROM  APP___PAS__DBUSER.T_POLICY_HOLDER A  where a.customer_id=#{customer_id}
	
	]]>
	</select>
	
	<!-- 通过客户信息查询出该客户作为投保人、被保人对应的所有保单 -->
	<select id="PA_findPolicysByCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[ 
				select ph.policy_code
			     from APP___PAS__DBUSER.T_policy_holder          ph,
			          APP___PAS__DBUSER.t_Policy_Acknowledgement ack
			    where 1 = 1
			      and ack.policy_id = ph.policy_id
			      and ack.acknowledge_date is not null
		]]>
		<if test=" customer_id_list  != null and customer_id_list.size()!=0">
		    <![CDATA[ AND PH.CUSTOMER_ID  in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
		 </if>
		 <![CDATA[  GROUP BY  ph.policy_code ]]>
		 
    	 <![CDATA[ union ]]>
    	 <![CDATA[
   		 		select il.policy_code
			     from APP___PAS__DBUSER.T_insured_list il,
			          APP___PAS__DBUSER.t_Policy_Acknowledgement ack
			    where 1 = 1
			      and il.policy_id = ack.policy_id 
			      and ack.acknowledge_date is not null
   		 ]]>
		
		 <if test=" customer_id_list  != null and customer_id_list.size()!=0">
		    <![CDATA[ AND il.CUSTOMER_ID  in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
	 	 </if>
	    <![CDATA[  GROUP BY  il.policy_code ]]>
	
		
	</select>
	
	<select id="PA_findPolicyHolderByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT  A.POLICY_ID,A.POLICY_CODE,A.CUSTOMER_ID, TC.CUSTOMER_NAME,A.APPLY_CODE,A.ADDRESS_ID, 
		TL.CUSTOMER_ID AS IN_CUSTOMER_ID ,TL.ADDRESS_ID as IN_ADDRESS_ID FROM DEV_PAS.T_POLICY_HOLDER A 
 JOIN DEV_PAS.T_INSURED_LIST TL ON A.POLICY_ID=TL.POLICY_ID JOIN DEV_PAS.T_CUSTOMER TC ON A.CUSTOMER_ID=TC.CUSTOMER_ID
WHERE   ROWNUM<1000 ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
	
	<!-- 客户保单基本信息查询保单投保人信息sql -->
	<select id="queryCusPlyPolicyHolderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
		        TC.CUSTOMER_NAME APPNTNAME,
				DECODE(TC.CUSTOMER_GENDER,'1','0','2','1','2') APPNTSEX,
				TC.CUSTOMER_CERT_TYPE APPNTIDTYPE,
				TC.CUSTOMER_CERTI_CODE APPNTIDCODE,
				TO_CHAR(TC.CUSTOMER_BIRTHDAY,'YYYY-MM-DD') APPNTBIRTHDAY,
				TO_CHAR(TC.CUST_CERT_STAR_DATE,'YYYY-MM-DD') APPNTIDEFFSTARTDATE,
				TO_CHAR(TC.CUST_CERT_END_DATE,'YYYY-MM-DD') APPNTIDEFFENDDATE,
				(SELECT TCO.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCO WHERE TCO.COUNTRY_CODE=TC.COUNTRY_CODE) APPNTNATIVEPLACE,
				(SELECT TJ.JOB_NAME FROM APP___PAS__DBUSER.T_JOB_CODE TJ WHERE TJ.JOB_CODE=TC.JOB_CODE) APPNTPOSITION,
				(SELECT TJ.JOB_CODE FROM APP___PAS__DBUSER.T_JOB_CODE TJ WHERE TJ.JOB_CODE=TC.JOB_CODE) APPNTPOSITIONCODE,
				TA.MOBILE_TEL APPNTMOBILE,
				TA.FIXED_TEL APPNTPHONE,
				(
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.STATE) ||
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.CITY)  ||
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.DISTRICT)  ||	
					 TA.ADDRESS				
				) APPNTPOSTALADDRESS,
				TA.POST_CODE APPNTZIPCODE,
				NVL((SELECT DECODE(TCT.TAX_RESIDENT_TYPE,'','N','Y') FROM DEV_PAS.T_CUSTOMER_TAX TCT 
						WHERE  tct.customer_tax_name =tc.customer_name
				       and tct.customer_tax_birthday = tc.customer_birthday
				       and tct.customer_tax_cert_type = tc.customer_cert_type
				       and tct.customer_tax_certi_code = tc.customer_certi_code
				       and tct.customer_tax_gender = tc.customer_gender),'N') APPNTCRSFLAG,
				(SELECT TRT.RESIDENT_NAME FROM DEV_PAS.T_CUSTOMER_TAX TCT LEFT JOIN DEV_PAS.T_TAX_RESIDENT_TYPE TRT ON TCT.TAX_RESIDENT_TYPE = TRT.RESIDENT_CODE
						WHERE  tct.customer_tax_name =tc.customer_name
				       and tct.customer_tax_birthday = tc.customer_birthday
				       and tct.customer_tax_cert_type = tc.customer_cert_type
				       and tct.customer_tax_certi_code = tc.customer_certi_code
				       and tct.customer_tax_gender = tc.customer_gender) RESIDENTNAME
			FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,APP___PAS__DBUSER.T_CUSTOMER TC,APP___PAS__DBUSER.T_ADDRESS TA 
			WHERE TPH.CUSTOMER_ID=TC.CUSTOMER_ID AND TPH.ADDRESS_ID=TA.ADDRESS_ID
			AND TPH.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
		]]>
	</select>
	<!-- 查询所有的投保人信息
	#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求-受益人变更-保单查询接口
	#119143随信通_新增贷款续贷保全项接口需求-保单管理
    modify by cuiqi_wb
    2022-11-3 -->
	<select id="findHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	   SELECT A.POLICY_CODE,
            C.OLD_CUSTOMER_ID,
            C.CUSTOMER_ID,
            (SELECT Z.ORDER_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED Z 
            WHERE Z.ORDER_ID=1 AND Z.POLICY_CODE=A.POLICY_CODE 
            AND Z.INSURED_ID=A.LIST_ID AND ROWNUM = 1) AS INSUREDORDER,
            C.CUSTOMER_NAME,
            C.CUSTOMER_GENDER,
            C.COUNTRY_CODE,
            A.SOCI_SECU,            
            C.CUSTOMER_BIRTHDAY,
            C.CUSTOMER_CERT_TYPE,
            C.CUSTOMER_CERTI_CODE,
            C.CUST_CERT_STAR_DATE,
            C.CUST_CERT_END_DATE,
            (select tct.tax_resident_type 
               from dev_pas.T_CUSTOMER_TAX tct
              where tct.Customer_Tax_Name = c.customer_name
                and tct.customer_tax_birthday = c.customer_birthday
                and tct.customer_tax_gender = c.customer_gender
                and tct.customer_tax_cert_type = c.customer_cert_type
                and tct.customer_tax_certi_code = c.customer_certi_code) TAX_RESIDENT_TYPE,
            B.MOBILE_TEL,
            B.FIXED_TEL,
            A.JOB_CODE,
            (select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = A.JOB_CODE) JOB_NAME,
            (SELECT T1.JOB_CATEGORY FROM APP___PAS__DBUSER.T_JOB_CODE T1 WHERE T1.JOB_CODE = A.JOB_CODE) AS job_category_code,
            (SELECT T2.JOB_CATEGORY_NAME 
            	FROM APP___PAS__DBUSER.T_JOB_CODE T1, APP___PAS__DBUSER.T_JOB_CATEGORY T2 
            	WHERE T1.JOB_CATEGORY = T2.JOB_CATEGORY_CODE AND T1.JOB_CODE = A.JOB_CODE) AS job_category_name, 
            C.COMPANY_NAME,
            B.EMAIL,
            B.POST_CODE,
            B.TOWN_STREET_CODE,
            B.TOWN_STREET_NAME,
            B.STATE,
            B.CITY,
            B.DISTRICT,
            B.ADDRESS,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.STATE = O.CODE )  AS STATENAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.CITY = O.CODE )  AS CITYNAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.DISTRICT = O.CODE )  AS DISTRICTNAME,
            (SELECT O.COUNTRY_NAME FROM  APP___PAS__DBUSER.T_COUNTRY O WHERE O.COUNTRY_CODE = C.COUNTRY_CODE) COUNTRY_NAME,
            (SELECT O.TYPE FROM  APP___PAS__DBUSER.T_CERTI_TYPE O WHERE O.CODE = C.CUSTOMER_CERT_TYPE) CERT_TYPE_NAME,
            C.LIVE_STATUS
       FROM APP___PAS__DBUSER.T_POLICY_HOLDER A,
            APP___PAS__DBUSER.T_CUSTOMER      C,
            APP___PAS__DBUSER.T_ADDRESS       B
      WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
        AND A.ADDRESS_ID = B.ADDRESS_ID
        AND A.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
         ]]>
	    <if test=" customer_cert_type != null and customer_cert_type != '' ">
	    <![CDATA[AND C.CUSTOMER_CERT_TYPE = #{customer_cert_type,jdbcType=VARCHAR} ]]>
	    </if>
	    <if test=" operator_user_code == '23'  ">
	    <![CDATA[AND C.CUSTOMER_CERT_TYPE in ('0','5') ]]>
	    </if>
	</select>
	<!-- 根据保单号查询保单信息 
	//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求-贷款清偿-保单查询接口
	//#118918随信通_新增贷款清偿保全项接口需求-保单管理
    //modify by cuiqi_wb
    //2022-07-8-->
	<select id="PA_findAppCustomerByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			 SELECT distinct(TCU.CUSTOMER_ID),
			                TCU.CUSTOMER_NAME,
			                TCU.CUSTOMER_GENDER,
			                TCU.COUNTRY_CODE,
			                (SELECT TY.COUNTRY_NAME
			                   FROM APP___PAS__DBUSER.T_COUNTRY TY
			                  WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			                TCU.CUSTOMER_BIRTHDAY,
			                TCU.CUSTOMER_CERT_TYPE,
			                (SELECT TE.TYPE
			                   FROM APP___PAS__DBUSER.T_CERTI_TYPE TE
			                  WHERE TE.CODE = TCU.CUSTOMER_CERT_TYPE) TYPE_NAME,
			                TCU.CUSTOMER_CERTI_CODE,
			                TCU.CUST_CERT_STAR_DATE,
			                TCU.CUST_CERT_END_DATE,
			                TS.MOBILE_TEL,
			                TS.OFFICE_TEL OFFICETEL,
			                TS.FIXED_TEL FIXEDTEL,
			                TCU.OLD_CUSTOMER_ID,
			       	TPH.JOB_CODE,
            		(select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = TPH.JOB_CODE) JOB_NAME,
            		(select tjcg.JOB_CATEGORY_NAME 
            		 from dev_pas.T_JOB_CATEGORY tjcg,dev_pas.T_JOB_CODE tjc 
            		 where tjc.job_code = TPH.JOB_CODE and tjc.job_category =tjcg.JOB_CATEGORY_CODE ) occupation_type,
            		(select tjc.job_category from dev_pas.T_JOB_CODE tjc where tjc.job_code = TPH.JOB_CODE) occupationtype_id,
			                TCU.COMPANY_NAME,
			                (SELECT TT.NAME
			                   FROM APP___PAS__DBUSER.T_DISTRICT TT
			                  WHERE TT.CODE = TS.STATE) STATE,
			                (SELECT TT.NAME
			                   FROM APP___PAS__DBUSER.T_DISTRICT TT
			                  WHERE TT.CODE = TS.CITY) CITY,
			                (SELECT TT.NAME
			                   FROM APP___PAS__DBUSER.T_DISTRICT TT
			                  WHERE TT.CODE = TS.DISTRICT) DISTRICT,
			                TS.ADDRESS,
			                TS.STATE province_id,
			                TS.CITY city_id,
			                TS.DISTRICT county_id,
			                TS.POST_CODE,
			                TS.TOWN_STREET_CODE,
			                TS.TOWN_STREET_NAME,
			                (SELECT TCT.TAX_RESIDENT_TYPE 
			                   FROM APP___PAS__DBUSER.T_CUSTOMER_TAX  TCT 
			                  WHERE TCU.CUSTOMER_NAME = TCT.CUSTOMER_TAX_NAME 
			                    AND TCU.CUSTOMER_BIRTHDAY = TCT.CUSTOMER_TAX_BIRTHDAY 
                                AND TCU.CUSTOMER_GENDER = TCT.CUSTOMER_TAX_GENDER 
                                AND TCU.CUSTOMER_CERT_TYPE = TCT.CUSTOMER_TAX_CERT_TYPE
                                AND TCU.CUSTOMER_CERTI_CODE = TCT.CUSTOMER_TAX_CERTI_CODE 
                                AND ROWNUM =1) AS TAX_RESIDENT_TYPE,
			                TPH.SOCI_SECU 
			           FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
			                APP___PAS__DBUSER.T_CUSTOMER      TCU,
			                APP___PAS__DBUSER.T_ADDRESS       TS
			          WHERE TPH.POLICY_CODE = #{policy_code}
			            AND TCU.CUSTOMER_ID = TPH.CUSTOMER_ID
			            AND TS.ADDRESS_ID = TPH.ADDRESS_ID
	     ]]>
	</select>
	<!-- 根据客户id查询保单号 -->
	<select id="PA_findCustomerContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1
			 AND A.CUSTOMER_ID = ${customer_id} 
	    ]]>
	</select>
		<!-- 根据客户id查询保单号 -->
	<select id="PA_findCustomerInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select A.POLICY_CODE policy_code,A.Customer_Id customer_id
			 from APP___PAS__DBUSER.T_INSURED_LIST A 
			 where A.Customer_Id = ${customer_id}
	    ]]>
	</select>
	
	
	<select id="PA_findPolicyHolderAge" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select TRUNC(months_between(sysdate, cus.customer_birthday) / 12) AS holderAge 
			  from dev_pas.t_policy_holder ph, dev_pas.t_customer cus 
			 where cus.customer_id = ph.customer_id
			   and ph.policy_code =#{policy_code} 
	   	]]>
	</select>
	
	<select id="PA_findPolicyHolderByCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT distinct PH.POLICY_ID,PH.POLICY_CODE,PH.CUSTOMER_ID
      FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH,
           APP___PAS__DBUSER.T_CONTRACT_MASTER CM
     WHERE 1=1 
     	 AND CM.POLICY_CODE = PH.POLICY_CODE
      	 AND CM.LIABILITY_STATE IN ('1','4')
		 AND PH.CUSTOMER_ID = #{customer_id}
		 AND CM.VALIDATE_DATE >= TRUNC(sysdate, 'yyyy') 
		 AND CM.VALIDATE_DATE <= add_months(trunc(sysdate, 'yyyy'), 12) - 1
		 ]]>
	</select>
	
	<!-- 核保查询投保人已承保保单信息 -->
	<select id="PA_PolicyHolderQueryUnderWrite" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,
					   TCM.ORGAN_CODE,
				       TCM.POLICY_CODE,
				       TCM.VALIDATE_DATE,
				       TCM.LIABILITY_STATE,
				       TCM.APPLY_CODE,
				       TCM.DECISION_CODE,
				       TCM.MULTI_MAINRISK_FLAG,
				       TCM.CHANNEL_TYPE,
				       A.CUSTOMER_ID,
				       A.ANNUAL_INCOME_CEIL,
				       TCM.SUBMIT_CHANNEL,
				       A.EXCEPTION_HEALTH_FLAG
				  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
				       APP___PAS__DBUSER.T_POLICY_HOLDER A
				 WHERE TCM.POLICY_ID = A.POLICY_ID  ]]>
		<if test=" customer_Id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_Id} ]]></if>
		<if test=" policy_code  != null "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_status!=null and apply_status!='' and apply_status =='1'.toString()"><![CDATA[ AND TCM.LIABILITY_STATE = '3' ]]></if>
		<if test=" apply_status!=null and apply_status!='' and apply_status =='0'.toString()"><![CDATA[ AND TCM.LIABILITY_STATE != '3' ]]></if>
		<![CDATA[ ORDER BY TCM.VALIDATE_DATE DESC  ]]>
	</select>
	
		<!-- 核保查询投保人已承保保单信息 -->
	<select id="PA_queryPolicyHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TC.CUSTOMER_NAME,
                        TC.CUSTOMER_BIRTHDAY,
                        TC.CUSTOMER_GENDER,
                        TC.CUSTOMER_CERT_TYPE,
                        TC.CUSTOMER_CERTI_CODE
               FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CUSTOMER TC
               WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID ]]>
		<if test=" policy_code  != null "><![CDATA[ AND TPH.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	<!-- 查询投保人信息 -->
	<select id="PA_queryPolicyHolderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.CUSTOMER_ID,B.CUSTOMER_NAME, B.CUSTOMER_BIRTHDAY, B.CUSTOMER_GENDER,B.CUSTOMER_CERT_TYPE,B.CUSTOMER_CERTI_CODE
			  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER B
			    ON A.CUSTOMER_ID = B.CUSTOMER_ID
			 WHERE A.POLICY_CODE = #{policy_code}		   
		]]>		
	</select>
	<!-- 根据客户三要素查询客户作为投保人保单下所有分红信息保单列表信息 -->
	<select id="PA_queryPolicyCodeByHolderInfo" resultType="java.util.Map"
	parameterType="java.util.Map">
		<![CDATA[
			SELECT  DISTINCT TCM.POLICY_CODE, TCM.VALIDATE_DATE, TC.CUSTOMER_NAME,
			(select ls.status_name from dev_pas.t_liability_status ls where ls.status_code = TCM.LIABILITY_STATE) LIABILITY_STATE,
			TCM.APPLY_DATE,
            (SELECT TCY.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCY WHERE tc.COUNTRY_CODE = TCY.COUNTRY_CODE ) COUNTRY_NAME, 
			tc.COUNTRY_CODE,
			TCM.SPECIAL_ACCOUNT_FLAG,
			TCM.END_CAUSE
  FROM DEV_PAS.T_CUSTOMER        TC,
       DEV_PAS.T_POLICY_HOLDER   TPH,
       DEV_PAS.T_CONTRACT_MASTER TCM
 WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID
   AND TPH.POLICY_CODE = TCM.POLICY_CODE
   AND exists
       (SELECT TCBP.POLICY_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                    DEV_PAS.T_BUSINESS_PRODUCT   TBP
         WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID AND TBP.PRODUCT_CATEGORY1 = '20002' and tcbp.policy_code = tcm.policy_code)
   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
   AND TC.CUSTOMER_CERTI_CODE = #{customer_cert_code}
   AND TC.CUSTOMER_NAME = #{customer_name} ]]>
	<if test=" liability_state  != null  and liability_state  != '' "><![CDATA[ AND TCM.LIABILITY_STATE = #{liability_state} ]]></if>
	<if test=" policy_code  != null  and policy_code  != '' "><![CDATA[ AND TPH.POLICY_CODE = #{policy_code} ]]></if>
	<![CDATA[ ORDER BY TCM.VALIDATE_DATE DESC]]>
</select>
	<!-- 136592 add 查询投保人名下保单是否有贷款逾期且未清偿 -->
	<select id="findLnOverduePolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select distinct h.policy_code
		  from dev_pas.T_POLICY_HOLDER h
		  join dev_pas.t_policy_account a
		    on a.policy_id = h.policy_id
		   and a.account_type = 4
		   and a.interest_capital > 0 /*未清偿*/
		 where rownum < 2000 
		   /*逾期未清偿*/
		   and exists (select 1 from dev_pas.t_policy_account_stream s
		       where s.account_id = a.account_id 
		       and s.account_type = a.account_type
		       and s.policy_id = a.policy_id
		       and s.busi_item_id = a.busi_item_id
		       and s.regular_repay = 0 ]]>
		       <if test=" change_id  != null "><![CDATA[ and s.repay_due_date < (select t.apply_time from dev_pas.T_CS_APPLICATION t where t.change_id = #{change_id}) ]]></if>
		       <if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ and s.repay_due_date < #{apply_time} ]]></if>
		       <![CDATA[ )
		]]>
		<if test=" policy_id  != null "><![CDATA[ AND h.POLICY_ID = #{policy_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND h.CUSTOMER_ID = #{customer_id} ]]></if>
	</select>
	<!-- 根据客户号 查询作为投保人 被保人的保单 -->
	<select id="PA_findPolicysByCustomerIdAndStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TPH.POLICY_CODE FROM DEV_PAS.T_POLICY_HOLDER TPH 
							 INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM ON TPH.POLICY_CODE=TCM.POLICY_CODE
      				         INNER JOIN DEV_PAS.T_CUSTOMER TC ON TPH.CUSTOMER_ID=TC.CUSTOMER_ID 
  					WHERE 1=1 ]]>

	<if test="policyStatus != null and policyStatus != '' "><![CDATA[  AND TCM.LIABILITY_STATE=#{policyStatus}  ]]></if>
	<if test="policy_code != null and policy_code != ''  "><![CDATA[  AND TCM.POLICY_CODE = #{policy_code} ]]> </if>
	<if test="customer_name != null and customer_name != '' and customer_idtype !=null and customer_idtype !='' 
			and customer_idno !=null and customer_idno !='' ">
		<![CDATA[ AND TC.CUSTOMER_NAME= #{customer_name} AND TC.CUSTOMER_CERT_TYPE=#{customer_idtype} AND TC.CUSTOMER_CERTI_CODE=#{customer_idno} ]]>
	</if>

	<if test="query_type != null and query_type != '' and query_type ==3 ">
           <![CDATA[
           UNION 
         SELECT TIL.POLICY_CODE FROM DEV_PAS.T_INSURED_LIST TIL 
         				  INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM ON TIL.POLICY_CODE=TCM.POLICY_CODE
        				  INNER JOIN DEV_PAS.T_CUSTOMER TC ON TIL.CUSTOMER_ID=TC.CUSTOMER_ID 
    				WHERE  1=1 
			]]>
		<if test="policyStatus != null and policyStatus != '' "><![CDATA[  AND TCM.LIABILITY_STATE=#{policyStatus}  ]]></if>
		<if test="policy_code != null and policy_code != ''  "><![CDATA[  AND  TCM.POLICY_CODE = #{policy_code} ]]> </if>
		<if test="customer_name != null and customer_name != '' and customer_idtype !=null and customer_idtype !='' 
				and customer_idno!=null and customer_idno !=''">
			<![CDATA[ AND TC.CUSTOMER_NAME= #{customer_name} AND TC.CUSTOMER_CERT_TYPE=#{customer_idtype} AND TC.CUSTOMER_CERTI_CODE=#{customer_idno} ]]>
		</if>
	</if>   
 		
	</select>

    <!-- 查询减保保单列表查询接口的投保人信息 -->
	<select id="PA_findPolicyHolderInfosForReduction" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT TCU.CUSTOMER_ID,
			       TCU.CUSTOMER_NAME,
			       TCU.CUSTOMER_GENDER,
			       TCU.CUSTOMER_BIRTHDAY,
			       TCU.CUSTOMER_CERT_TYPE,
			       (SELECT TE.TYPE
			          FROM APP___PAS__DBUSER.T_CERTI_TYPE TE
			         WHERE TE.CODE = TCU.CUSTOMER_CERT_TYPE) CUSTOMER_CERT_TYPE_NAME,
			       TCU.CUSTOMER_CERTI_CODE,
			       TCU.CUST_CERT_STAR_DATE,
			       TCU.CUST_CERT_END_DATE,
			       TS.POST_CODE,
			       (SELECT TJC.JOB_NAME
			          FROM DEV_PAS.T_JOB_CODE TJC
			         WHERE TJC.JOB_CODE = TPH.JOB_CODE) JOB_NAME,
			       TPH.JOB_CODE,
			       (SELECT TJCG.JOB_CATEGORY_NAME
			          FROM DEV_PAS.T_JOB_CATEGORY TJCG, DEV_PAS.T_JOB_CODE TJC
			         WHERE TJC.JOB_CODE = TPH.JOB_CODE
			           AND TJC.JOB_CATEGORY = TJCG.JOB_CATEGORY_CODE) OCCUPATION_TYPE_NAME,
			       (SELECT TJC.JOB_CATEGORY
			          FROM DEV_PAS.T_JOB_CODE TJC
			         WHERE TJC.JOB_CODE = TPH.JOB_CODE) OCCUPATION_TYPE_CODE,
			       TS.MOBILE_TEL,
			       TS.FIXED_TEL,
			       TS.ADDRESS,
			       TS.COMPANY_NAME,
			       TCU.COUNTRY_CODE,
			       (SELECT TY.COUNTRY_NAME
			          FROM APP___PAS__DBUSER.T_COUNTRY TY
			         WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			       TS.STATE,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.STATE) STATE_NAME,
			       TS.CITY,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.CITY) CITY_NAME,
			       TS.DISTRICT,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.DISTRICT) DISTRICT_NAME,
			       TCU.LIVE_STATUS,
			       TCU.CUSTOMER_RISK_LEVEL,
			       (SELECT T.TAX_RESIDENT_TYPE 
		              FROM APP___PAS__DBUSER.T_CUSTOMER_TAX T 
		             WHERE T.CUSTOMER_TAX_NAME = TCU.CUSTOMER_NAME 
		               AND TCU.CUSTOMER_BIRTHDAY = T.CUSTOMER_TAX_BIRTHDAY 
		               AND T.CUSTOMER_TAX_GENDER = TCU.CUSTOMER_GENDER 
		               AND T.CUSTOMER_TAX_CERT_TYPE = TCU.CUSTOMER_CERT_TYPE 
		               AND T.CUSTOMER_TAX_CERTI_CODE = TCU.CUSTOMER_CERTI_CODE) AS TAX_RESIDENT_TYPE,
			       TS.TOWN_STREET_CODE,
			       TS.TOWN_STREET_NAME,
			       TCU.OLD_CUSTOMER_ID
			  FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU
			    ON TCU.CUSTOMER_ID = TPH.CUSTOMER_ID
			  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TS
			    ON TS.ADDRESS_ID = TPH.ADDRESS_ID
			 WHERE 1 = 1
			   AND TPH.POLICY_CODE = #{policy_code}
			   AND ROWNUM = 1
		]]>   
	</select>
	
	<!-- 查询保单补发保单列表查询接口的投保人信息 -->
	<select id="PA_findPolicyHolderInfosOnReplacement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT TCU.OLD_CUSTOMER_ID,
			       TCU.CUSTOMER_ID,
			       TCU.CUSTOMER_NAME,
			       TCU.CUSTOMER_GENDER,
			       TCU.CUSTOMER_BIRTHDAY,
			       TCU.CUSTOMER_CERT_TYPE,
			       TCU.CUSTOMER_CERTI_CODE,
			       TCU.CUST_CERT_STAR_DATE,
			       TCU.CUST_CERT_END_DATE,
			       (SELECT TJC.JOB_NAME
			          FROM APP___PAS__DBUSER.T_JOB_CODE TJC
			         WHERE TJC.JOB_CODE = TPH.JOB_CODE) JOB_NAME,
			       TPH.JOB_CODE,
			       (SELECT TJCG.JOB_CATEGORY_NAME
			          FROM APP___PAS__DBUSER.T_JOB_CATEGORY TJCG,
			               APP___PAS__DBUSER.T_JOB_CODE     TJC
			         WHERE TJC.JOB_CODE = TPH.JOB_CODE
			           AND TJC.JOB_CATEGORY = TJCG.JOB_CATEGORY_CODE) OCCUPATION_TYPE_NAME,
			       (SELECT TJC.JOB_CATEGORY
			          FROM APP___PAS__DBUSER.T_JOB_CODE TJC
			         WHERE TJC.JOB_CODE = TPH.JOB_CODE) OCCUPATION_TYPE_CODE,
			       TS.MOBILE_TEL,
			       TS.FIXED_TEL,
			       TS.POST_CODE,
			       TCU.COUNTRY_CODE,
			       (SELECT TY.COUNTRY_NAME
			          FROM APP___PAS__DBUSER.T_COUNTRY TY
			         WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			       TS.STATE,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.STATE) STATE_NAME,
			       TS.CITY,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.CITY) CITY_NAME,
			       TS.DISTRICT,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.DISTRICT) DISTRICT_NAME,
			       TS.TOWN_STREET_CODE,
			       TS.TOWN_STREET_NAME,
			       TS.ADDRESS,
			       TCU.LIVE_STATUS,
			       (SELECT T.TAX_RESIDENT_TYPE
			          FROM APP___PAS__DBUSER.T_CUSTOMER_TAX T
			         WHERE T.CUSTOMER_TAX_NAME = TCU.CUSTOMER_NAME
			           AND TCU.CUSTOMER_BIRTHDAY = T.CUSTOMER_TAX_BIRTHDAY
			           AND T.CUSTOMER_TAX_GENDER = TCU.CUSTOMER_GENDER
			           AND T.CUSTOMER_TAX_CERT_TYPE = TCU.CUSTOMER_CERT_TYPE
			           AND T.CUSTOMER_TAX_CERTI_CODE = TCU.CUSTOMER_CERTI_CODE) AS TAX_RESIDENT_TYPE
			  FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU
			    ON TCU.CUSTOMER_ID = TPH.CUSTOMER_ID
			  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TS
			    ON TS.ADDRESS_ID = TPH.ADDRESS_ID
			 WHERE 1 = 1
			   AND TPH.POLICY_CODE = #{policy_code}
			   AND ROWNUM = 1
		]]>   
	</select>
	
	
	<!-- 查询保单关联保单列表查询接口的投保人信息 -->
	<select id="PA_findPolicyHolderInfosOnPolicyRelated" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT TCU.OLD_CUSTOMER_ID,
			       TCU.CUSTOMER_ID,
			       TCU.CUSTOMER_NAME,
			       TCU.CUSTOMER_GENDER,
			       TCU.CUSTOMER_BIRTHDAY,
			       TCU.CUSTOMER_CERT_TYPE,
			       TCU.CUSTOMER_CERTI_CODE,
			       TCU.CUST_CERT_STAR_DATE,
			       TCU.CUST_CERT_END_DATE,
			       (SELECT TJC.JOB_NAME
			          FROM APP___PAS__DBUSER.T_JOB_CODE TJC
			         WHERE TJC.JOB_CODE = TPH.JOB_CODE) JOB_NAME,
			       TPH.JOB_CODE,
			       TS.MOBILE_TEL,
			       TS.FIXED_TEL,
			       TS.COMPANY_NAME,
			       TCU.COUNTRY_CODE,
			       (SELECT TY.COUNTRY_NAME
			          FROM APP___PAS__DBUSER.T_COUNTRY TY
			         WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			       TS.STATE,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.STATE) STATE_NAME,
			       TS.CITY,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.CITY) CITY_NAME,
			       TS.DISTRICT,
			       (SELECT TT.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT TT
			         WHERE TT.CODE = TS.DISTRICT) DISTRICT_NAME,
			       TS.TOWN_STREET_CODE,
			       TS.TOWN_STREET_NAME,
			       TS.ADDRESS,
			       TCU.LIVE_STATUS
			  FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU
			    ON TCU.CUSTOMER_ID = TPH.CUSTOMER_ID
			  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TS
			    ON TS.ADDRESS_ID = TPH.ADDRESS_ID
			 WHERE 1 = 1
			   AND TPH.POLICY_CODE = #{policy_code}
			   AND ROWNUM = 1
		]]>   
	</select>

	<!-- 根据保单号查询投保人国籍信息 -->
	<select id="findCountryInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT 
			(SELECT TCY.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCY WHERE C.COUNTRY_CODE = TCY.COUNTRY_CODE ) COUNTRY_NAME, 
			C.COUNTRY_CODE
		FROM 
			APP___PAS__DBUSER.T_POLICY_HOLDER A,
			APP___PAS__DBUSER.T_CUSTOMER      C
		WHERE 
			A.CUSTOMER_ID = C.CUSTOMER_ID
			AND A.POLICY_CODE= #{policy_code, jdbcType=VARCHAR}]]>   
	</select>
	
	<select id="PA_findPolicyHolderIsTrust" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_policy_holder a
			 inner join dev_pas.t_trust_company b
			    on a.customer_id = b.customer_id
			 where 1 = 1
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	
	<select id="PA_geNsPolicyHolderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TPH.CUSTOMER_ID,
	   TC.OLD_CUSTOMER_ID, 
       TC.CUSTOMER_NAME,
       TC.CUSTOMER_GENDER,
       TC.CUSTOMER_BIRTHDAY,
       TC.CUSTOMER_CERT_TYPE,
       (SELECT TE.TYPE
                FROM APP___PAS__DBUSER.T_CERTI_TYPE TE
               WHERE TE.CODE = TC.CUSTOMER_CERT_TYPE) CUSTOMER_CERT_TYPE_NAME,
       TC.CUSTOMER_CERTI_CODE,
       TC.CUST_CERT_STAR_DATE,
       TC.CUST_CERT_END_DATE,
       TPH.JOB_CODE,
       (SELECT TJC.JOB_NAME
                FROM DEV_PAS.T_JOB_CODE TJC
               WHERE TJC.JOB_CODE = TPH.JOB_CODE) OCCUPATION_NAME,
       TA.MOBILE_TEL,
       TA.FIXED_TEL,
       TC.COMPANY_NAME,
       TC.COUNTRY_CODE,
       (SELECT TY.COUNTRY_NAME
                FROM APP___PAS__DBUSER.T_COUNTRY TY
               WHERE TY.COUNTRY_CODE = TC.COUNTRY_CODE) COUNTRY_NAME,
       TA.ADDRESS,
       TA.STATE,
       TA.CITY,
       TA.DISTRICT,
       TA.TOWN_STREET_CODE,
       TA.TOWN_STREET_NAME,
       (SELECT O.NAME
          FROM APP___PAS__DBUSER.T_DISTRICT O
         WHERE TA.STATE = O.CODE) AS STATENAME,
       (SELECT O.NAME
          FROM APP___PAS__DBUSER.T_DISTRICT O
         WHERE TA.CITY = O.CODE) AS CITYNAME,
       (SELECT O.NAME
          FROM APP___PAS__DBUSER.T_DISTRICT O
         WHERE TA.DISTRICT = O.CODE) AS DISTRICTNAME,
       TC.LIVE_STATUS
  FROM DEV_PAS.T_POLICY_HOLDER TPH
  LEFT JOIN DEV_PAS.T_CUSTOMER TC
    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
  LEFT JOIN DEV_PAS.T_ADDRESS TA
    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
 WHERE TPH.POLICY_ID = #{policy_id}
		]]>   
	</select>
	
	<!-- 根据保单号查询信托业务标识 -->
	<select id="PA_findPolicyIsTrust" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select 
		  case 
		    when A.trust_busi_flag = '12' then '投保人/受益人'
		    when A.trust_busi_flag = '1'  then '受益人'
		    else ''
		  end as trust_busi_flag
		from (
		  select  
		    (select '1' from dev_pas.t_contract_bene tcb 
		    	where tcb.policy_code = #{policy_code, jdbcType=VARCHAR} and tcb.company_id is not null and rownum = 1) ||
		    (select '2' from dev_pas.t_trust_company ttc where ttc.customer_id = tph.customer_id) as trust_busi_flag
		   from dev_pas.t_policy_holder tph 
		   where tph.policy_code = #{policy_code, jdbcType=VARCHAR}
		) A]]>   
	</select>
	<!-- 查询个数操作 -->
	<select id="PA_findAllYXPolicy" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
              select count(1)
        from dev_pas.t_policy_holder a, dev_pas.t_contract_master b
       where a.policy_code = b.policy_code
         and b.liability_state != '3'
         and a.customer_id = #{customer_id}
             ]]>
	</select>
	
	
	<select id="PA_geNsPolicyHolderCustomerInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TC.OLD_CUSTOMER_ID,
        TPH.CUSTOMER_ID,
        TC.CUSTOMER_NAME,
        TC.CUSTOMER_GENDER,
        TC.CUSTOMER_BIRTHDAY,
        TC.CUSTOMER_CERT_TYPE,
        TC.CUSTOMER_CERTI_CODE,
        TC.CUST_CERT_STAR_DATE,
        TC.CUST_CERT_END_DATE,
        TPH.JOB_CODE,
        (SELECT TJC.JOB_NAME
           FROM DEV_PAS.T_JOB_CODE TJC
          WHERE TJC.JOB_CODE = TPH.JOB_CODE) OCCUPATION_NAME,
        tc.job_nature,
        (SELECT Tjn.Job_Nature
           FROM DEV_PAS.t_Job_Nature Tjn
          WHERE tjn.job_nature_code = tc.job_nature) job_nature_name,
        TA.MOBILE_TEL,
        ta.email,
        Ta.COUNTRY_CODE,
        (SELECT TY.COUNTRY_NAME
           FROM APP___PAS__DBUSER.T_COUNTRY TY
          WHERE TY.COUNTRY_CODE = Ta.COUNTRY_CODE) COUNTRY_NAME,
        TA.STATE,
        TA.CITY,
        TA.DISTRICT,
        (SELECT O.NAME
           FROM APP___PAS__DBUSER.T_DISTRICT O
          WHERE TA.STATE = O.CODE) AS STATENAME,
        (SELECT O.NAME
           FROM APP___PAS__DBUSER.T_DISTRICT O
          WHERE TA.CITY = O.CODE) AS CITYNAME,
        (SELECT O.NAME
           FROM APP___PAS__DBUSER.T_DISTRICT O
          WHERE TA.DISTRICT = O.CODE) AS DISTRICTNAME,
        TA.ADDRESS
  FROM DEV_PAS.T_POLICY_HOLDER TPH
  LEFT JOIN DEV_PAS.T_CUSTOMER TC
    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
  LEFT JOIN DEV_PAS.T_ADDRESS TA
    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
 WHERE TPH.POLICY_CODE = #{policy_code}
		]]>   
	</select>
	
	<select id="PA_geNsPolicyHolderCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TC.OLD_CUSTOMER_ID,
       TPH.CUSTOMER_ID,
       TC.CUSTOMER_NAME,
       TC.CUSTOMER_GENDER,
       TC.CUSTOMER_BIRTHDAY,
       TC.CUSTOMER_CERT_TYPE,
       TC.CUSTOMER_CERTI_CODE,
       TC.CUST_CERT_STAR_DATE,
       TC.CUST_CERT_END_DATE,
       TC.COUNTRY_CODE,
       (SELECT TY.COUNTRY_NAME
          FROM APP___PAS__DBUSER.T_COUNTRY TY
         WHERE TY.COUNTRY_CODE = TC.COUNTRY_CODE) COUNTRY_NAME,
       TPH.JOB_CODE,
       (SELECT TJC.JOB_NAME
          FROM DEV_PAS.T_JOB_CODE TJC
         WHERE TJC.JOB_CODE = TPH.JOB_CODE) OCCUPATION_NAME,
       TA.MOBILE_TEL,
       TA.STATE,
       TA.CITY,
       TA.DISTRICT,
       (SELECT O.NAME
          FROM APP___PAS__DBUSER.T_DISTRICT O
         WHERE TA.STATE = O.CODE) AS STATENAME,
       (SELECT O.NAME
          FROM APP___PAS__DBUSER.T_DISTRICT O
         WHERE TA.CITY = O.CODE) AS CITYNAME,
       (SELECT O.NAME
          FROM APP___PAS__DBUSER.T_DISTRICT O
         WHERE TA.DISTRICT = O.CODE) AS DISTRICTNAME,
       TA.ADDRESS,
       TA.FIXED_TEL,
       TC.COMPANY_NAME,
       TA.TOWN_STREET_CODE,
       TA.TOWN_STREET_NAME,
       TC.LIVE_STATUS
  FROM DEV_PAS.T_POLICY_HOLDER TPH
  LEFT JOIN DEV_PAS.T_CUSTOMER TC
    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
  LEFT JOIN DEV_PAS.T_ADDRESS TA
    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
 WHERE TPH.POLICY_ID = #{policy_id}
		]]>   
	</select>
	
</mapper> 
