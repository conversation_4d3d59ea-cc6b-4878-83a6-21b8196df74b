<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ILcpremDao">
	
	<!-- <resultMap id="testPO" type="com.test.po.TestPO">
		<result property="payenddate" column="PAYENDDATE"/>
		<result property="freeenddate" column="FREEENDDATE"/>
		<result property="rate" column="RATE"/>
		<result property="sumprem" column="SUMPREM"/>
		<result property="managecom" column="MANAGECOM"/>
		<result property="payintv" column="PAYINTV"/>
		<result property="state" column="STATE"/>
		<result property="makedate" column="MAKEDATE"/>
		<result property="appntno" column="APPNTNO"/>
		<result property="modifydate" column="MODIFYDATE"/>
		<result property="freerate" column="FREERATE"/>
		<result property="prem" column="PREM"/>
		<result property="appnttype" column="APPNTTYPE"/>
		<result property="payplantype" column="PAYPLANTYPE"/>
		<result property="grpcontno" column="GRPCONTNO"/>
		<result property="paytodate" column="PAYTODATE"/>
		<result property="spop" column="SPOP"/>
		<result property="needacc" column="NEEDACC"/>
		<result property="freestartdate" column="FREESTARTDATE"/>
		<result property="dutycode" column="DUTYCODE"/>
		<result property="urgepayflag" column="URGEPAYFLAG"/>
		<result property="contno" column="CONTNO"/>
		<result property="polno" column="POLNO"/>
		<result property="freeflag" column="FREEFLAG"/>
		<result property="standprem" column="STANDPREM"/>
		<result property="maketime" column="MAKETIME"/>
		<result property="addfeedirect" column="ADDFEEDIRECT"/>
		<result property="paytimes" column="PAYTIMES"/>
		<result property="payplancode" column="PAYPLANCODE"/>
		<result property="modifytime" column="MODIFYTIME"/>
		<result property="sptimeNew" column="SPTIME_NEW"/>
		<result property="suppriskscore" column="SUPPRISKSCORE"/>
		<result property="operator" column="OPERATOR"/>
		<result property="secinsuaddpoint" column="SECINSUADDPOINT"/>
		<result property="paystartdate" column="PAYSTARTDATE"/>
	</resultMap> -->
	
	<sql id="cpremWhereCondition">
		<if test=" payenddate  != null  and  payenddate  != ''  and  payenddate  != 'null' "><![CDATA[ AND A.PAYENDDATE = TO_DATE(#{payenddate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" freeenddate  != null  and  freeenddate  != ''  and  freeenddate  != 'null' "><![CDATA[ AND A.FREEENDDATE = TO_DATE(#{freeenddate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" rate  != null "><![CDATA[ AND A.RATE = ${rate} ]]></if>
		<if test=" sumprem  != null "><![CDATA[ AND A.SUMPREM = ${sumprem} ]]></if>
		<if test=" managecom != null and managecom != '' and managecom != 'null' "><![CDATA[ AND A.MANAGECOM = LTRIM(RTRIM(#{managecom}, ' ') , ' ') ]]></if>
		<if test=" payintv  != null "><![CDATA[ AND A.PAYINTV = ${payintv} ]]></if>
		<if test=" state != null and state != '' and state != 'null' "><![CDATA[ AND A.STATE = LTRIM(RTRIM(#{state}, ' ') , ' ') ]]></if>
		<if test=" makedate  != null  and  makedate  != ''  and  makedate  != 'null' "><![CDATA[ AND A.MAKEDATE = TO_DATE(#{makedate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" appntno != null and appntno != '' and appntno != 'null' "><![CDATA[ AND A.APPNTNO = LTRIM(RTRIM(#{appntno}, ' ') , ' ') ]]></if>
		<if test=" modifydate  != null  and  modifydate  != ''  and  modifydate  != 'null' "><![CDATA[ AND A.MODIFYDATE = TO_DATE(#{modifydate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" freerate  != null  and  freerate  != ''  and  freerate  != 'null' "><![CDATA[ AND A.FREERATE = TO_DATE(#{freerate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" prem  != null "><![CDATA[ AND A.PREM = ${prem} ]]></if>
		<if test=" appnttype != null and appnttype != '' and appnttype != 'null' "><![CDATA[ AND A.APPNTTYPE = LTRIM(RTRIM(#{appnttype}, ' ') , ' ') ]]></if>
		<if test=" payplantype != null and payplantype != '' and payplantype != 'null' "><![CDATA[ AND A.PAYPLANTYPE = LTRIM(RTRIM(#{payplantype}, ' ') , ' ') ]]></if>
		<if test=" grpcontno != null and grpcontno != '' and grpcontno != 'null' "><![CDATA[ AND A.GRPCONTNO = LTRIM(RTRIM(#{grpcontno}, ' ') , ' ') ]]></if>
		<if test=" paytodate  != null  and  paytodate  != ''  and  paytodate  != 'null' "><![CDATA[ AND A.PAYTODATE = TO_DATE(#{paytodate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" spop != null and spop != '' and spop != 'null' "><![CDATA[ AND A.SPOP = LTRIM(RTRIM(#{spop}, ' ') , ' ') ]]></if>
		<if test=" needacc != null and needacc != '' and needacc != 'null' "><![CDATA[ AND A.NEEDACC = LTRIM(RTRIM(#{needacc}, ' ') , ' ') ]]></if>
		<if test=" freestartdate  != null  and  freestartdate  != ''  and  freestartdate  != 'null' "><![CDATA[ AND A.FREESTARTDATE = TO_DATE(#{freestartdate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" dutycode != null and dutycode != '' and dutycode != 'null' "><![CDATA[ AND A.DUTYCODE = LTRIM(RTRIM(#{dutycode}, ' ') , ' ') ]]></if>
		<if test=" urgepayflag != null and urgepayflag != '' and urgepayflag != 'null' "><![CDATA[ AND A.URGEPAYFLAG = LTRIM(RTRIM(#{urgepayflag}, ' ') , ' ') ]]></if>
		<if test=" contno != null and contno != '' and contno != 'null' "><![CDATA[ AND A.CONTNO = LTRIM(RTRIM(#{contno}, ' ') , ' ') ]]></if>
		<if test=" polno != null and polno != '' and polno != 'null' "><![CDATA[ AND A.POLNO = LTRIM(RTRIM(#{polno}, ' ') , ' ') ]]></if>
		<if test=" freeflag != null and freeflag != '' and freeflag != 'null' "><![CDATA[ AND A.FREEFLAG = LTRIM(RTRIM(#{freeflag}, ' ') , ' ') ]]></if>
		<if test=" standprem  != null "><![CDATA[ AND A.STANDPREM = ${standprem} ]]></if>
		<if test=" maketime != null and maketime != '' and maketime != 'null' "><![CDATA[ AND A.MAKETIME = LTRIM(RTRIM(#{maketime}, ' ') , ' ') ]]></if>
		<if test=" addfeedirect != null and addfeedirect != '' and addfeedirect != 'null' "><![CDATA[ AND A.ADDFEEDIRECT = LTRIM(RTRIM(#{addfeedirect}, ' ') , ' ') ]]></if>
		<if test=" paytimes  != null "><![CDATA[ AND A.PAYTIMES = ${paytimes} ]]></if>
		<if test=" payplancode != null and payplancode != '' and payplancode != 'null' "><![CDATA[ AND A.PAYPLANCODE = LTRIM(RTRIM(#{payplancode}, ' ') , ' ') ]]></if>
		<if test=" modifytime != null and modifytime != '' and modifytime != 'null' "><![CDATA[ AND A.MODIFYTIME = LTRIM(RTRIM(#{modifytime}, ' ') , ' ') ]]></if>
		<if test=" sptimeNew  != null  and  sptimeNew  != ''  and  sptimeNew  != 'null' "><![CDATA[ AND A.SPTIME_NEW = TO_DATE(#{sptime_new},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" suppriskscore  != null "><![CDATA[ AND A.SUPPRISKSCORE = ${suppriskscore} ]]></if>
		<if test=" operator != null and operator != '' and operator != 'null' "><![CDATA[ AND A.OPERATOR = LTRIM(RTRIM(#{operator}, ' ') , ' ') ]]></if>
		<if test=" secinsuaddpoint  != null "><![CDATA[ AND A.SECINSUADDPOINT = ${secinsuaddpoint} ]]></if>
		<if test=" paystartdate  != null  and  paystartdate  != ''  and  paystartdate  != 'null' "><![CDATA[ AND A.PAYSTARTDATE = TO_DATE(#{paystartdate},'yyyy-MM-dd HH24:mi:ss') ]]></if>
	</sql>
	
	
	
	<select id="findAllLcprem" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT *FROM (
		SELECT A.PAYENDDATE      ,
        A.FREEENDDATE     ,
        A.RATE            ,
        A.SUMPREM         ,
        A.MANAGECOM       ,
        A.PAYINTV         ,
        A.STATE           ,
        A.MAKEDATE        ,
        A.APPNTNO         ,
        A.MODIFYDATE      ,
        A.FREERATE        ,
        A.PREM            ,
        A.APPNTTYPE       ,
        A.PAYPLANTYPE     ,
        A.GRPCONTNO       ,
        A.PAYTODATE       ,
        A.NEEDACC         ,
        A.FREESTARTDATE   ,
        A.DUTYCODE        ,
        A.URGEPAYFLAG     ,
        A.CONTNO          ,
        A.POLNO           ,
        A.FREEFLAG        ,
        A.STANDPREM       ,
        A.MAKETIME        ,
        A.ADDFEEDIRECT    ,
        A.PAYTIMES        ,
        A.PAYPLANCODE     ,
        A.MODIFYTIME      ,
        A.SUPPRISKSCORE   ,
        A.OPERATOR        ,
        A.SECINSUADDPOINT ,
        A.PAYSTARTDATE    ,
        A.TRANSFERDATE    ,
        ROW_NUMBER() OVER(PARTITION BY A.PAYPLANTYPE ORDER BY A.MODIFYDATE DESC) RN
   FROM APP___PAS__DBUSER.LCPREM A WHERE 1=1
	]]>
		<if test=" contno  != null and contno  !='' "><![CDATA[ AND A.CONTNO =  rpad(#{contno},(select length(l.CONTNO) from APP___PAS__DBUSER.LcPrem l where rownum = 1)) ]]></if>
		<if test=" polno  != null and polno  !=''"><![CDATA[ AND A.POLNO =  rpad(#{polno},(select length(l.POLNO) from APP___PAS__DBUSER.LcPrem l where rownum = 1)) ]]></if>
		<if test=" payplantype   != null and payplantype  !=''"><![CDATA[ AND A.PAYPLANTYPE  IN ('01','02','03','04') ]]></if>
		<![CDATA[ ) WHERE RN = 1
		]]>
	</select>
	
	
</mapper>
