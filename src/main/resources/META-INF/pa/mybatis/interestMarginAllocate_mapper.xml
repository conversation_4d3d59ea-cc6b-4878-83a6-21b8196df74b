<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IInterestMarginAllocateDao">
<!--
	<sql id="interestMarginAllocateWhereCondition">
		<if test=" allocate_id  != null "><![CDATA[ AND A.ALLOCATE_ID = #{allocate_id} ]]></if>
		<if test=" batch_date  != null  and  batch_date  != ''  "><![CDATA[ AND A.BATCH_DATE = #{batch_date} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" next_cash_value  != null "><![CDATA[ AND A.NEXT_CASH_VALUE = #{next_cash_value} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
		<if test=" interest_amount  != null "><![CDATA[ AND A.INTEREST_AMOUNT = #{interest_amount} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
		<if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND A.ALLOCATE_DATE = #{allocate_date} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryInterestMarginAllocateByAllocateIdCondition">
		<if test=" allocate_id  != null "><![CDATA[ AND A.ALLOCATE_ID = #{allocate_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addInterestMarginAllocate"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="allocate_id">
			SELECT APP___PAS__DBUSER.S_LOAN_BUSI_CFG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE(
				ALLOCATE_ID, BATCH_DATE, PRODUCT_ID, NEXT_CASH_VALUE, INSERT_TIME, UPDATE_TIME, ITEM_ID, 
				PAY_STATUS, INTEREST_AMOUNT, INSERT_TIMESTAMP, UPDATE_BY, CASH_VALUE, ALLOCATE_DATE, UPDATE_TIMESTAMP, 
				INSERT_BY, POLICY_ID ) 
			VALUES (
				#{allocate_id, jdbcType=NUMERIC}, #{batch_date, jdbcType=DATE} , #{product_id, jdbcType=NUMERIC} , #{next_cash_value, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{item_id, jdbcType=NUMERIC} 
				, #{pay_status, jdbcType=NUMERIC} , #{interest_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{cash_value, jdbcType=NUMERIC} , #{allocate_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteInterestMarginAllocate" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE WHERE ALLOCATE_ID = #{allocate_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateInterestMarginAllocate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE ]]>
		<set>
		<trim suffixOverrides=",">
		    BATCH_DATE = #{batch_date, jdbcType=DATE} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    NEXT_CASH_VALUE = #{next_cash_value, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    PAY_STATUS = #{pay_status, jdbcType=NUMERIC} ,
		    INTEREST_AMOUNT = #{interest_amount, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    ALLOCATE_DATE = #{allocate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE ALLOCATE_ID = #{allocate_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findInterestMarginAllocateByAllocateId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ALLOCATE_ID, A.BATCH_DATE, A.PRODUCT_ID, A.NEXT_CASH_VALUE, A.ITEM_ID, 
			A.PAY_STATUS, A.INTEREST_AMOUNT, A.CASH_VALUE, A.ALLOCATE_DATE, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE A WHERE 1 = 1  ]]>
		<include refid="queryInterestMarginAllocateByAllocateIdCondition" />
		<![CDATA[ ORDER BY A.ALLOCATE_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapInterestMarginAllocate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ALLOCATE_ID, A.BATCH_DATE, A.PRODUCT_ID, A.NEXT_CASH_VALUE, A.ITEM_ID, 
			A.PAY_STATUS, A.INTEREST_AMOUNT, A.CASH_VALUE, A.ALLOCATE_DATE, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ALLOCATE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllInterestMarginAllocate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ALLOCATE_ID, A.BATCH_DATE, A.PRODUCT_ID, A.NEXT_CASH_VALUE, A.ITEM_ID, 
			A.PAY_STATUS, A.INTEREST_AMOUNT, A.CASH_VALUE, A.ALLOCATE_DATE, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ALLOCATE_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findInterestMarginAllocateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryInterestMarginAllocateForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ALLOCATE_ID, B.BATCH_DATE, B.PRODUCT_ID, B.NEXT_CASH_VALUE, B.ITEM_ID, 
			B.PAY_STATUS, B.INTEREST_AMOUNT, B.CASH_VALUE, B.ALLOCATE_DATE, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ALLOCATE_ID, A.BATCH_DATE, A.PRODUCT_ID, A.NEXT_CASH_VALUE, A.ITEM_ID, 
			A.PAY_STATUS, A.INTEREST_AMOUNT, A.CASH_VALUE, A.ALLOCATE_DATE, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INTEREST_MARGIN_ALLOCATE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ALLOCATE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
