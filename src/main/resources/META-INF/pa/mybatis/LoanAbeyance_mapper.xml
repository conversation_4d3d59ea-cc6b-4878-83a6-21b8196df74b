<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.batch.sendnote.dao.impl.LoanAbeyanceDaoImpl">

	<sql id="queryByLiabilityState">
		<if test="liability_state != null and liability_state !=''"><![CDATA[AND TCM.LIABILITY_STATE = #{liability_state}]]></if>
	</sql>
	<sql id="loanfindOrganCodeInfo">
		<if test="organ_code != null and organ_code != ''">
        	AND TCM.ORGAN_CODE IN (
			    	SELECT T.ORGAN_CODE
				    FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
				    START WITH T.ORGAN_CODE = #{organ_code}
				    CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE
				    GROUP BY ORGAN_CODE)
		</if>
	</sql>
	<!-- 贷款中止通知 -->
	<select id="queryLoanAbeyanceCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1) from
			(
				SELECT distinct tc.mobile_tel, TCM.policy_code 
				FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
				LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER tph ON tph.POLICY_ID = TCM.policy_id
				LEFT JOIN APP___PAS__DBUSER.t_customer tc ON tc.customer_id = tph.customer_id
				WHERE 1 = 1 AND TCM.LAPSE_CAUSE ='6' AND FLOOR(#{batch_time,jdbcType=DATE} - TCM.LAPSE_DATE) = 0
		]]>
			<include refid="queryByLiabilityState"></include>
		<![CDATA[) t]]>
	</select>
	
	<select id="queryLoanAbeyance" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT distinct tc.mobile_tel, TCM.policy_code 
				FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
				LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER tph ON tph.POLICY_ID = TCM.policy_id
				LEFT JOIN APP___PAS__DBUSER.t_customer tc ON tc.customer_id = tph.customer_id
				WHERE 1 = 1 AND TCM.LAPSE_CAUSE ='6' AND FLOOR(#{batch_time,jdbcType=DATE} - TCM.LAPSE_DATE) = 0
		]]>
			<include refid="queryByLiabilityState"></include>
	</select>
	
	<!-- 贷款中止预通知 -->
	<select id="queryLoanAbeyanceWarnCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(1)
  FROM (SELECT DISTINCT TC.MOBILE_TEL, TCBP.POLICY_CODE,tc.customer_id 
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
          LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
            ON TPH.POLICY_ID = TCBP.POLICY_ID
          LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TC
            ON TC.CUSTOMER_ID = TPH.CUSTOMER_ID
           LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT_STREAM PAS
            ON PAS.POLICY_ID = TCBP.POLICY_ID
         WHERE 1 = 1
           AND TCBP.SUSPEND_DATE IS NOT NULL
           AND TCBP.SUSPEND_CAUSE = '01'
           AND PAS.REPAY_DUE_DATE+1 <= #{batch_time}
		]]>
		<![CDATA[) t ]]>
	</select>
	
	<select id="queryLoanAbeyanceWarn" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.mobile_tel,T.policy_code,T.customer_id,T.POLICY_ID,rownum FROM(SELECT distinct tc.mobile_tel,TCBP.POLICY_ID, TCBP.policy_code,tc.customer_id 
  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
  LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER tph
    ON tph.POLICY_ID = TCBP.policy_id
  LEFT JOIN APP___PAS__DBUSER.t_customer tc
    ON tc.customer_id = tph.customer_id
     LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT_STREAM PAS
     ON PAS.POLICY_ID = TCBP.POLICY_ID
 WHERE 1 = 1
   AND TCBP.SUSPEND_DATE IS NOT NULL
   AND TCBP.SUSPEND_CAUSE = '01'
   AND #{batch_time,jdbcType=DATE} >= PAS.REPAY_DUE_DATE+1)	T WHERE 1=1 AND MOD(t.POLICY_ID,#{modNum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
		]]>
	</select>
	
	<!-- 贷款逾期保单《预》中止提醒 -->
	<select id="PA_sendLoanOverTimePolicyExpectEnd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			SELECT ROWNUM RN,M.POLICY_CODE,M.SUSPEND_DATE,M.ORGANCODE,M.BANK,M.AMOUNT, M.MULTI_MAINRISK_FLAG  FROM  (
			 SELECT  N.POLICY_CODE,N.SUSPEND_DATE,N.ORGANCODE,N.BANK, sum(N.AMOUNT) as AMOUNT, N.MULTI_MAINRISK_FLAG FROM 
					 ( SELECT TCBP.POLICY_CODE POLICY_CODE,
                             TO_CHAR(TCBP.SUSPEND_DATE, 'yyyy-mm-dd') SUSPEND_DATE,
                             TCM.ORGAN_CODE ORGANCODE,
                             CASE
                               WHEN TPAS.AUTO_ACCOUNT_ID IS NOT NULL THEN
                                (SELECT (SELECT TB.BANK_NAME
                                           FROM APP___PAS__DBUSER.T_BANK TB
                                          WHERE TB.BANK_CODE = TBA.BANK_CODE)
                                   FROM APP___PAS__DBUSER.T_BANK_ACCOUNT TBA
                                  WHERE TBA.ACCOUNT_ID = TPAS.AUTO_ACCOUNT_ID)
                               ELSE
                                (SELECT A.BANK_NAME
                                   FROM APP___PAS__DBUSER.T_BANK A
                                  WHERE A.BANK_CODE = TPA.NEXT_ACCOUNT_BANK)
                             END BANK,
                             TPAS.CAPITAL_BALANCE AMOUNT,
                             TCM.MULTI_MAINRISK_FLAG
                        FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD    TCBP,
                             APP___PAS__DBUSER.T_CONTRACT_MASTER       TCM,
                             APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TPAS,
                             APP___PAS__DBUSER.T_PAYER_ACCOUNT         TPA
                       WHERE TCBP.POLICY_ID = TPAS.POLICY_ID
                         AND TCM.POLICY_CODE = TCBP.POLICY_CODE
                         AND TCBP.POLICY_ID = TPA.POLICY_ID
                         AND TCBP.BUSI_ITEM_ID = TPAS.BUSI_ITEM_ID
                         AND TCBP.LIABILITY_STATE NOT IN ('3','4')
				 		 AND TPAS.REGULAR_REPAY = 0
		]]>
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND Add_Months(TCBP.SUSPEND_DATE,-1) +1 = #{batch_time,jdbcType=DATE}]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND TCBP.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]></if>
		<include refid="loanfindOrganCodeInfo" />
		<![CDATA[
			 ) N group by N.POLICY_CODE, N.SUSPEND_DATE, N.ORGANCODE, N.BANK, N.MULTI_MAINRISK_FLAG ) M
		]]>
	</select>
	
	<!-- 贷款逾期保单中止提醒 -->
	<select id="PA_sendLoanOverTimePolicyEnd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			SELECT ROWNUM RN,M.POLICY_CODE,M.SUSPEND_DATE,M.ORGANCODE,M.LASTFOUR,M.BANK,M.BUSI_ITEM_ID,M.MULTI_MAINRISK_FLAG FROM (
		        SELECT 
		          TCBP.POLICY_CODE POLICY_CODE,   
		          TO_CHAR(TCBP.LAPSE_DATE,'yyyy-mm-dd') SUSPEND_DATE, 
		          (SELECT TCM.ORGAN_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM WHERE TCM.POLICY_CODE=TCBP.POLICY_CODE) ORGANCODE, 
		          CASE WHEN TPAS.AUTO_ACCOUNT_ID IS NOT NULL
					THEN (SELECT SUBSTR(TBA.BANK_ACCOUNT,-4) FROM APP___PAS__DBUSER.T_BANK_ACCOUNT TBA WHERE TBA.ACCOUNT_ID=TPAS.AUTO_ACCOUNT_ID)
						ELSE SUBSTR(TPA.NEXT_ACCOUNT,-4) END LASTFOUR,
		          CASE WHEN TPAS.AUTO_ACCOUNT_ID IS NOT NULL
					THEN (SELECT (SELECT TB.BANK_NAME FROM APP___PAS__DBUSER.T_BANK TB WHERE TB.BANK_CODE=TBA.BANK_CODE) 
						FROM APP___PAS__DBUSER.T_BANK_ACCOUNT TBA WHERE TBA.ACCOUNT_ID=TPAS.AUTO_ACCOUNT_ID)
						ELSE (SELECT A.BANK_NAME FROM APP___PAS__DBUSER.T_BANK A WHERE A.BANK_CODE = TPA.NEXT_ACCOUNT_BANK) END BANK,
		            TCBP.BUSI_ITEM_ID BUSI_ITEM_ID,
		            TCM.MULTI_MAINRISK_FLAG
		         FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TPAS,APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA  , 
		              APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
		         WHERE   TCBP.POLICY_ID=TPAS.POLICY_ID 
		         AND TCBP.POLICY_ID = TPA.POLICY_ID
		         AND TCBP.BUSI_ITEM_ID = TPAS.BUSI_ITEM_ID
		         AND TCBP.LIABILITY_STATE = 4
		         AND TCBP.LAPSE_CAUSE='6'
		         AND TPAS.REGULAR_REPAY='0'
		         AND TCM.POLICY_ID = TPAS.POLICY_ID
		]]>
		<if test=" batch_time != null and batch_time != ''  "><![CDATA[AND TCBP.LAPSE_DATE+1= #{batch_time,jdbcType=DATE}]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND TCBP.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]></if>
		<include refid="loanfindOrganCodeInfo" />
		<![CDATA[
			 ) M
		]]>
	</select>
	
	<!-- 获取投保人手机号（回访电话1>保单层电话>客户层电话） -->
	<select id="PA_getPhone" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT
		      CASE WHEN 
		       ( SELECT A.POLICY_ID 
                  FROM DEV_PAS.T_CS_POLICY_CHANGE      A,
                       DEV_PAS.T_CS_ACCEPT_CHANGE      B,
                       DEV_PAS.T_POLICY_ACCOUNT_STREAM C
                 WHERE A.ACCEPT_ID = B.ACCEPT_ID
                   AND A.POLICY_ID = C.POLICY_ID
                   AND B.ACCEPT_STATUS = '18'
                   AND A.SERVICE_CODE = 'AE'
                   AND C.ACCOUNT_TYPE = '4'
                   AND A.APPLY_TIME > C.LOAN_START_DATE
                   AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} 
                   AND ROWNUM = 1 ) IS NOT NULL THEN  TC.MOBILE_TEL 
		      WHEN	(SELECT P.CALL_PHONE1 FROM (
		            SELECT TCAC.CALL_PHONE1 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC 
		            LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC  ON TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
		            WHERE TCAC.ACCEPT_STATUS='18' AND TCPC.POLICY_CODE=#{policy_code,jdbcType=VARCHAR} AND TCAC.CALL_PHONE1 IS NOT NULL 
		            ORDER BY TCPC.UPDATE_TIMESTAMP DESC ) P WHERE ROWNUM=1) IS NOT NULL THEN 
					(SELECT P.CALL_PHONE1 FROM (
		            SELECT TCAC.CALL_PHONE1 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC 
		            LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC  ON TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
		            WHERE TCAC.ACCEPT_STATUS='18' AND TCPC.POLICY_CODE=#{policy_code,jdbcType=VARCHAR} AND TCAC.CALL_PHONE1 IS NOT NULL 
		            ORDER BY TCPC.UPDATE_TIMESTAMP DESC ) P WHERE ROWNUM=1) 
			   WHEN  TA.MOBILE_TEL IS NOT NULL THEN TA.MOBILE_TEL
			   WHEN  TC.MOBILE_TEL IS NOT NULL THEN TC.MOBILE_TEL		
			   END
			   PHONE
		FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,APP___PAS__DBUSER.T_CUSTOMER TC,APP___PAS__DBUSER.T_ADDRESS TA 
		WHERE TPH.CUSTOMER_ID=TC.CUSTOMER_ID AND TPH.ADDRESS_ID = TA.ADDRESS_ID 
		AND  TPH.POLICY_CODE=#{policy_code,jdbcType=VARCHAR}
	</select>
	
	<!-- 获取保单应领未领生存金 -->
	<select id="PA_PolicySurAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT   NVL(SUM(B.FEE_AMOUNT), 0) AS FEE_AMOUNT
	          FROM APP___PAS__DBUSER.T_PAY_PLAN A, APP___PAS__DBUSER.T_PAY_DUE B
	         WHERE A.PLAN_ID = B.PLAN_ID
	           AND A.POLICY_ID = B.POLICY_ID
	           AND A.PAY_PLAN_TYPE = 3
	           AND B.FEE_STATUS = '00'
			   AND B.POLICY_CODE = #{policy_code}
			   AND B.PAY_DUE_DATE <= #{batch_date}
		 ]]>
	</select>

	<select id="PA_LoanOverTimePolicyEndBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT tcbp.liability_state,
	       tcbp.lapse_cause,
	       tcbp.lapse_date,
	       tcbp.master_busi_item_id,
	       tcbp.busi_prod_code,
	       tcbp.end_cause,
	       (SELECT TBP.PRODUCT_ABBR_NAME
	          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
	         WHERE TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID) AS INSURANCE_NAME
		FROM dev_pas.t_contract_busi_prod tcbp
		WHERE tcbp.busi_item_id in (select T.busi_item_id
		                               from (select row_number() over(partition by a.busi_prod_code order by a.update_time) dup,
		                                            a.busi_item_id
		                                       from dev_pas.t_contract_busi_prod a
		                                      where a.policy_code = #{policy_code}
		                                     
		                                     ) T
		                              where T.dup = 1)
		ORDER BY tcbp.master_busi_item_id DESC
	</select>
	
	<!-- 查询保单原始销售渠道 -->
	<select id="PA_findOriginal" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[  
		 	SELECT B.AGENT_CHANNEL
			  FROM DEV_PAS.T_CONTRACT_AGENT A, DEV_PAS.T_AGENT B
			 WHERE A.AGENT_CODE = B.AGENT_CODE
			   AND A.IS_NB_AGENT = '1'
			   AND A.POLICY_CODE = #{policy_code}
			   AND ROWNUM = 1
		 ]]>
	</select>
	
</mapper>