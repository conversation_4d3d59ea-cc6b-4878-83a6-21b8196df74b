<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="persistenceBonusLog">
<!--
	<sql id="PA_persistenceBonusLogWhereCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPersistenceBonusLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPersistenceBonusLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG(
				PRODUCT_ID, INSERT_TIME, UPDATE_TIME, ITEM_ID, PAY_YEAR, LOG_ID, INSERT_TIMESTAMP, 
				UPDATE_BY, LIST_ID, PAY_DUE_DATE, FEE_AMOUNT, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, 
				INSERT_BY, POLICY_ID ) 
			VALUES (
				#{product_id, jdbcType=NUMERIC}, SYSDATE , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{pay_year, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{pay_due_date, jdbcType=DATE} , #{fee_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePersistenceBonusLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG WHERE 1 = 1 ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePersistenceBonusLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPersistenceBonusLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.ITEM_ID, A.PAY_YEAR, A.LOG_ID, 
			A.LIST_ID, A.PAY_DUE_DATE, A.FEE_AMOUNT, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPersistenceBonusLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPersistenceBonusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.ITEM_ID, A.PAY_YEAR, A.LOG_ID, 
			A.LIST_ID, A.PAY_DUE_DATE, A.FEE_AMOUNT, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPersistenceBonusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.ITEM_ID, A.PAY_YEAR, A.LOG_ID, 
			A.LIST_ID, A.PAY_DUE_DATE, A.FEE_AMOUNT, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPersistenceBonusLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPersistenceBonusLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRODUCT_ID, B.ITEM_ID, B.PAY_YEAR, B.LOG_ID, 
			B.LIST_ID, B.PAY_DUE_DATE, B.FEE_AMOUNT, B.LOG_TYPE, B.POLICY_CHG_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.PRODUCT_ID, A.ITEM_ID, A.PAY_YEAR, A.LOG_ID, 
			A.LIST_ID, A.PAY_DUE_DATE, A.FEE_AMOUNT, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
