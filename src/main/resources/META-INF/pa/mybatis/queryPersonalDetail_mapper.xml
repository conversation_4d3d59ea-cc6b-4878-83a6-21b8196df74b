<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IQueryPersonalPolicyDetailDao">


	<!-- 查询所有操作 -->
	<select id="queryPersonalPolicyDetailForInsert" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
	  SELECT DISTINCT  TCP.POLICY_ID, TCP.ITEM_ID, TCBP.ORDER_ID, TCBP.BUSI_ITEM_ID,TCBP.MASTER_BUSI_ITEM_ID,tcp.is_master_item 
          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
          APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
          APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
      WHERE 1 = 1
      AND TCM.POLICY_CODE = TCP.POLICY_CODE 
      AND TCM.POLICY_CODE = TCBP.POLICY_CODE
      AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID  ]]>
      
      
	 <if test="is_master_item!=null and is_master_item!='' "><![CDATA[
      		and tcp.is_master_item =#{is_master_item} ]]>
       </if>
       
      <if test="busi_item_id!=null and busi_item_id!='' "><![CDATA[
      		and tcp.busi_item_id =#{busi_item_id} ]]>
      </if>
		<if test="pay_next!=null and pay_next!='' "><![CDATA[
		  AND EXISTS (SELECT 1
		  FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA
		  WHERE TPA.POLICY_ID = TCP.POLICY_ID
		  AND TPA.PAY_NEXT = #{pay_next})]]> </if>   
    <![CDATA[
      ]]>
   <if test="due_start_time!=null and due_start_time!='' and due_end_time!=null and due_end_time!=''"><![CDATA[ 
         AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_PREM_ARAP TPRA
         WHERE TPRA.POLICY_CODE = TCP.POLICY_CODE
         AND TPRA.DUE_TIME >= to_date(#{due_start_time},'yyyy-MM-dd')
         AND TPRA.DUE_TIME <= to_date(#{due_end_time},'yyyy-MM-dd'))]]> </if>
   <if test="due_start_time!=null and due_start_time!='' and due_end_time==null || due_end_time==''"><![CDATA[ 
         AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_PREM_ARAP TPRA
         WHERE TPRA.POLICY_CODE = TCP.POLICY_CODE
         AND TPRA.DUE_TIME >= to_date(#{due_start_time},'yyyy-MM-dd'))]]> </if>
    <if test="due_end_time!=null and due_end_time!='' and due_start_time==null || due_start_time==''"><![CDATA[ 
         AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_PREM_ARAP TPRA
         WHERE TPRA.POLICY_CODE = TCP.POLICY_CODE
         AND TPRA.DUE_TIME <= to_date(#{due_end_time},'yyyy-MM-dd'))]]> </if>     
   <if test="pay_start_date!=null and pay_start_date!='' and pay_end_date!=null and pay_end_date!=''"><![CDATA[
         AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_PREM_ARAP TPRA
         WHERE TPRA.POLICY_CODE = TCP.POLICY_CODE
         AND TPRA.PAY_END_DATE >= to_date(#{pay_start_date},'yyyy-MM-dd')
         AND TPRA.PAY_END_DATE <= to_date(#{pay_end_date},'yyyy-MM-dd'))]]> </if> 
   <if test="pay_start_date!=null and pay_start_date!='' and pay_end_date==null || pay_end_date==''"><![CDATA[
         AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_PREM_ARAP TPRA
         WHERE TPRA.POLICY_CODE = TCP.POLICY_CODE
         AND TPRA.PAY_END_DATE >= to_date(#{pay_start_date},'yyyy-MM-dd'))]]> </if>      
    <if test="pay_end_date!=null and pay_end_date!='' and pay_start_date==null || pay_start_date==''"><![CDATA[
         AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_PREM_ARAP TPRA
         WHERE TPRA.POLICY_CODE = TCP.POLICY_CODE
         AND TPRA.PAY_END_DATE <= to_date(#{pay_end_date},'yyyy-MM-dd'))]]> </if>    
   <if test="policy_code!=null and policy_code!='' "><![CDATA[AND TCM.POLICY_CODE = #{policy_code}]]> </if>      
   <if test=" holder_certy_code != null and holder_certy_code != ''  "><![CDATA[ AND  EXISTS (SELECT 1 
                   FROM APP___PAS__DBUSER.T_POLICY_HOLDER T1, APP___PAS__DBUSER.T_CUSTOMER T2
                   WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                   AND T1.POLICY_CODE = TCM.POLICY_CODE AND T2.CUSTOMER_CERTI_CODE=#{holder_certy_code}) ]]></if>
   <if test="insured_certy_code!=null and insured_certy_code!='' "><![CDATA[AND EXISTS (SELECT 1
                   FROM APP___PAS__DBUSER.T_INSURED_LIST T1, APP___PAS__DBUSER.T_CUSTOMER T2
                   WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                   AND T1.POLICY_CODE = TCM.POLICY_CODE AND T2.CUSTOMER_CERTI_CODE=#{insured_certy_code}) ]]> </if>          
   <if test="customer_certy_code!=null and customer_certy_code!=null!='' "><![CDATA[AND (EXISTS(SELECT 1
                   FROM APP___PAS__DBUSER.T_POLICY_HOLDER T1, APP___PAS__DBUSER.T_CUSTOMER T2
                   WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                   AND T1.POLICY_CODE = TCM.POLICY_CODE  and T2.CUSTOMER_CERTI_CODE=#{customer_certy_code}) OR EXISTS
                   (SELECT 1 FROM APP___PAS__DBUSER.T_INSURED_LIST T1, APP___PAS__DBUSER.T_CUSTOMER T2
                   WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                   AND T1.POLICY_CODE = TCM.POLICY_CODE AND T2.CUSTOMER_CERTI_CODE=#{customer_certy_code}))]]> </if>
   <if test="customer_id!=null and customer_id!='' "><![CDATA[AND ( EXISTS (SELECT 1
                FROM APP___PAS__DBUSER.T_POLICY_HOLDER T1, APP___PAS__DBUSER.T_CUSTOMER T2
                WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                AND T1.POLICY_CODE = TCM.POLICY_CODE AND  T2.CUSTOMER_ID=#{customer_id}) OR EXISTS
                (SELECT 1 FROM APP___PAS__DBUSER.T_INSURED_LIST T1, APP___PAS__DBUSER.T_CUSTOMER T2
                WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                AND T1.POLICY_CODE = TCM.POLICY_CODE and T2.CUSTOMER_ID =#{customer_id}))]]> </if>             
   <if test="apply_code!=null and apply_code!='' "><![CDATA[AND TCM.APPLY_CODE=#{apply_code}]]> </if>         
   <if test="agent_code!=null and agent_code!='' "><![CDATA[AND  EXISTS (SELECT 1
                       FROM APP___PAS__DBUSER.T_CONTRACT_AGENT T
                       WHERE T.POLICY_CODE = TCM.POLICY_CODE and t.is_current_agent = 1
                       AND T.AGENT_CODE=#{agent_code}) ]]> </if>
   <if test="sign_star_date!=null and sign_star_date!='' "><![CDATA[AND TCM.ISSUE_DATE >=to_date(#{sign_star_date},'yyyy-MM-dd')]]> </if>
   <if test="sign_end_date!=null and sign_end_date!='' "><![CDATA[AND TCM.ISSUE_DATE <= to_date(#{sign_end_date},'yyyy-MM-dd')]]> </if>    
   <if test="holder_name!=null and holder_name!='' "><![CDATA[ AND EXISTS (SELECT 1
                  FROM APP___PAS__DBUSER.T_POLICY_HOLDER T1, APP___PAS__DBUSER.T_CUSTOMER T2
                 WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                   AND T1.POLICY_CODE = TCM.POLICY_CODE AND T2.CUSTOMER_NAME=#{holder_name})]]> </if>  
   <if test="busi_prod_code!=null and busi_prod_code!='' "><![CDATA[AND TCBP.BUSI_PROD_CODE = #{busi_prod_code}]]> </if>     
   <if test="apply_start_date!=null and apply_start_date!='' "><![CDATA[AND TCM.APPLY_DATE >= to_date(#{apply_start_date},'yyyy-MM-dd')]]> </if> 
   <if test="apply_end_date!=null and apply_end_date!='' "><![CDATA[AND TCM.APPLY_DATE <= to_date(#{apply_end_date},'yyyy-MM-dd') ]]> </if> 
   <if test="holder_zj_code!=null and holder_zj_code!='' "><![CDATA[AND  EXISTS(SELECT 1
                FROM APP___PAS__DBUSER.T_POLICY_HOLDER T1, APP___PAS__DBUSER.T_CUSTOMER T2
               WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                 AND T1.POLICY_CODE = TCM.POLICY_CODE
                 AND T2.CUSTOMER_CERT_TYPE = '1' AND T2.CUSTOMER_CERTI_CODE=#{holder_zj_code})]]> </if>
   <if test="insured_zj_code!=null and insured_zj_code!='' "><![CDATA[AND  EXISTS (SELECT 1
                FROM APP___PAS__DBUSER.T_INSURED_LIST T1, APP___PAS__DBUSER.T_CUSTOMER T2
               WHERE T1.CUSTOMER_ID = T2.CUSTOMER_ID
                 AND T1.POLICY_CODE = TCM.POLICY_CODE
                 AND T2.CUSTOMER_CERT_TYPE = '1' AND T2.CUSTOMER_CERTI_CODE=#{insured_zj_code}) ]]> </if>
   <if test="temp_fee_no != null and temp_fee_no != '' "><![CDATA[
		AND EXISTS(SELECT 1 FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE TCM.POLICY_CODE=POLICY_CODE AND BUSINESS_CODE=#{temp_fee_no})
		]]> </if>
	
	<![CDATA[ ORDER BY TCBP.ORDER_ID , TCBP.BUSI_ITEM_ID ]]>	
	</select>
	
	
	<select id="querySalesChannelNameByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.SALES_CHANNEL_CODE,T.SALES_CHANNEL_NAME FROM APP___PAS__DBUSER.T_SALES_CHANNEL T WHERE T.SALES_CHANNEL_CODE=#{sales_channel_code} ]]>
	</select>
	
	
	<select id="queryPayTypeNamebyPayType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT CODE,NAME FROM APP___PAS__DBUSER.T_PAY_MODE WHERE CODE=#{code} ]]>
	</select>
	
	
	<select id="queryContractPrdGroupMainByGroupId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.GROUP_CODE,T.GROUP_NAME FROM APP___PAS__DBUSER.T_CONTRACT_PRDGROUP_MAIN T WHERE T.POLICY_ID=#{policy_id} ]]>
		<if test="element_group_id!=null and pelement_group_id!=''"><![CDATA[AND T.ELEMENT_GROUP_ID=#{element_group_id}]]></if>
	    <if test="policy_code!=null and policy_code!=''"><![CDATA[AND T.POLICY_CODE=#{policy_code}]]></if>
	    <if test="apply_code!=null and apply_code!=''"><![CDATA[AND T.APPLY_CODE=#{apply_code}]]></if>	 
	</select>
	
	<select id="queryChargePeriodDescByChargePeriod" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.CHARGE_DESC FROM APP___PAS__DBUSER.T_CHARGE_PERIOD T WHERE T.CHARGE_PERIOD= #{charge_period}]]>
	</select>
	
	<select id="queryPolicyIsLR" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCAC.ACCEPT_ID
                  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC, APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC
                   WHERE TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
                     AND TCPC.POLICY_CODE = #{policy_code}
                     AND TCAC.SERVICE_CODE = #{service_code}
                     AND TCPC.SERVICE_CODE=#{service_code}
                     AND TCAC.ACCEPT_STATUS = #{accept_status}]]>
	</select>
	
	<select id="queryYearsFlagByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   	SELECT  TC.COVERAGE_DESC  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,APP___PAS__DBUSER.T_COVERAGE_PERIOD TC WHERE TCP.COVERAGE_PERIOD=TC.COVERAGE_PERIOD AND TCP.ITEM_ID=#{item_id}	
		]]>
	</select>
	
	<select id="queryPayYearsFlagByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT  TCP.CHARGE_PERIOD,TC.CHARGE_DESC  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,APP___PAS__DBUSER.T_CHARGE_PERIOD TC WHERE TCP.CHARGE_PERIOD=TC.CHARGE_PERIOD AND TCP.ITEM_ID=#{item_id}
		]]>
	</select>
	
	<select id="queryPayLocationNameByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
           SELECT T.CODE AS  PAY_TYPE_CODE,T.NAME AS PAY_TYPE_NAME FROM APP___PAS__DBUSER.T_PAY_MODE T WHERE T.CODE=
          (SELECT TRIM(PAY_NEXT) FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT WHERE POLICY_ID= #{policy_id})
		]]>
	</select>
	
	<select id="queryEndCauseNameByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT T.CAUSE_NAME FROM APP___PAS__DBUSER.T_END_CAUSE T WHERE T.CAUSE_CODE=#{code}
		]]>
	</select>
	
	<select id="queryBranchAttrByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT TS.BRANCH_ATTR FROM DEV_PAS.T_SALES_ORGAN TS,DEV_PAS.T_AGENT TA,DEV_PAS.T_CONTRACT_AGENT TCA
			WHERE TS.SALES_ORGAN_CODE=TA.SALES_ORGAN_CODE AND TA.AGENT_CODE =TCA.AGENT_CODE 
			AND TCA.INSERT_TIME = (SELECT MIN(INSERT_TIME) FROM DEV_PAS.T_CONTRACT_AGENT WHERE POLICY_ID=#{policy_id})
			AND TCA.POLICY_ID=#{policy_id} AND ROWNUM=1
		]]>
	</select>
	
	<select id="queryBonusByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    select c.product_id,c.REVERSIONARY_BONUS_FLAG AS REVERSIONARY_FLAG,
           c.CASH_BONUS_FLAG
      from dev_pds.t_product_life_bonus c
     where c.product_id = #{product_id} AND ROWNUM=1
		]]>
	</select>
	
	<select id="queryBankLoanStateByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT * FROM (
				SELECT CPC.POLICY_CODE,CPC.POLICY_ID,CPC.SERVICE_CODE,CPC.VALIDATE_TIME 
				FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC 
				WHERE CPC.POLICY_CODE=#{policy_code} AND CAC.ACCEPT_ID=CPC.ACCEPT_ID 
				AND CAC.ACCEPT_STATUS='18' 
				AND (CPC.SERVICE_CODE='CP' OR CPC.SERVICE_CODE='CS') 
				ORDER BY CPC.VALIDATE_TIME DESC 
			) WHERE ROWNUM=1 
		]]>
	</select>
	<select id="queryInputTypeNameByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT * FROM APP___PAS__DBUSER.T_INPUT_TYPE A WHERE A.TYPE_CODE=#{input_type}
		]]>
	</select>
	
	<select id="queryNSSecurity" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT BP.PRODUCT_CODE_SYS,CCP.PRODUCT_CODE,CPC.POLICY_CODE,
			CPC.SERVICE_CODE,CCP.ITEM_ID,CPC.VALIDATE_TIME,CCP.BUSI_ITEM_ID
			FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
			APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
			APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CCP,
			APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
			WHERE CAC.ACCEPT_ID=CPC.ACCEPT_ID 
			AND CPC.POLICY_CHG_ID = CCP.POLICY_CHG_ID
			AND CAC.ACCEPT_STATUS='18' 
			AND CPC.SERVICE_CODE='NS' 
			AND SUBSTR(BP.PRODUCT_CODE_SYS,3,8) = CCP.PRODUCT_CODE
			AND CCP.OPERATION_TYPE = '1'
			AND CCP.OLD_NEW = '1'
			AND BP.COVER_PERIOD_TYPE = '0'
			AND CPC.POLICY_CODE=#{policy_code}
			UNION ALL 
			SELECT BP.PRODUCT_CODE_SYS,CCP.PRODUCT_CODE,CPC.POLICY_CODE,
			CPC.SERVICE_CODE,CCP.ITEM_ID,CPC.VALIDATE_TIME,CCP.BUSI_ITEM_ID
			FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
			APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
			APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CCP,
			APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
			WHERE CAC.ACCEPT_ID=CPC.ACCEPT_ID 
			AND CPC.POLICY_CHG_ID = CCP.POLICY_CHG_ID
			AND CAC.ACCEPT_STATUS='18' 
			AND CPC.SERVICE_CODE='NS' 
			AND SUBSTR(BP.PRODUCT_CODE_SYS,3,8) = CCP.PRODUCT_CODE
			AND CCP.OPERATION_TYPE = '1'
			AND CCP.OLD_NEW = '1'
			AND BP.COVER_PERIOD_TYPE = '1'
			AND BP.RENEW_OPTION = '2'
			AND CPC.POLICY_CODE=#{policy_code}
		]]>
	</select>
	<!-- 查询新增附加险 -->
	<select id="queryOldNSSecurity" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	    SELECT TD.OLD_POL_NO, CAC.ACCEPT_CODE, BP.PRODUCT_CODE_SYS,CCP.PRODUCT_CODE,CPC.POLICY_CODE,
			CPC.SERVICE_CODE,CCP.ITEM_ID,CPC.VALIDATE_TIME,CCP.BUSI_ITEM_ID
			FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
			APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
			APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CCP,
			APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP,
			APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TD
			WHERE CAC.ACCEPT_ID=CPC.ACCEPT_ID 
			AND CPC.POLICY_CHG_ID = CCP.POLICY_CHG_ID
			AND CCP.BUSI_ITEM_ID = TD.BUSI_ITEM_ID
			AND CAC.ACCEPT_STATUS='18' 
			AND CPC.SERVICE_CODE='NS' 
			AND SUBSTR(BP.PRODUCT_CODE_SYS,3,8) = CCP.PRODUCT_CODE
			AND CCP.OLD_NEW = '1'
			AND BP.COVER_PERIOD_TYPE = '0'
			AND CPC.POLICY_CODE=#{policy_code}
			UNION ALL 
			SELECT TD.OLD_POL_NO,CAC.ACCEPT_CODE, BP.PRODUCT_CODE_SYS,CCP.PRODUCT_CODE,CPC.POLICY_CODE,
			CPC.SERVICE_CODE,CCP.ITEM_ID,CPC.VALIDATE_TIME,CCP.BUSI_ITEM_ID
			FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
			APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
			APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CCP,
			APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP,
			APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TD
			WHERE CAC.ACCEPT_ID=CPC.ACCEPT_ID 
			AND CPC.POLICY_CHG_ID = CCP.POLICY_CHG_ID
			AND CCP.BUSI_ITEM_ID = TD.BUSI_ITEM_ID
			AND CAC.ACCEPT_STATUS='18' 
			AND CPC.SERVICE_CODE='NS' 
			AND SUBSTR(BP.PRODUCT_CODE_SYS,3,8) = CCP.PRODUCT_CODE
			AND CCP.OLD_NEW = '1'
			AND BP.COVER_PERIOD_TYPE = '1'
			AND BP.RENEW_OPTION = '2'
			AND CPC.POLICY_CODE=#{policy_code}
			]]>
	</select>
	<!-- 查询新增附加险 -->
	<select id="queryNbBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	  SELECT * FROM APP___PAS__DBUSER.T_CON_BUSI_PROD_MAPPING  t  
	  WHERE t.busi_item_id_pas
        = #{busi_item_id}
        and t.policy_id = #{policy_id}
			]]>
	</select>
</mapper>