<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyHolderLogDao">

	<sql id="PA_policyHolderLogWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND <PERSON><PERSON>CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" job_underwrite != null and job_underwrite != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{job_underwrite} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND A.SOCI_SECU = #{soci_secu} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND A.AGENT_RELATION = #{agent_relation} ]]></if>
		<if test=" new_resident != null and new_resident != ''  "><![CDATA[ AND A.NEW_RESIDENT = #{new_resident} ]]></if>
		<if test=" educational_background != null and educational_background != ''  "><![CDATA[ AND A.EDUCATIONAL_BACKGROUND = #{educational_background} ]]></if>
		<if test=" risk_estimate_score != null and risk_estimate_score != ''  "><![CDATA[ AND A.RISK_ESTIMATE_SCORE = #{risk_estimate_score} ]]></if>
		<if test=" non_smoker != null and non_smoker != ''  "><![CDATA[ AND A.NON_SMOKER = #{non_smoker} ]]></if>
		<if test=" exception_health_flag != null and exception_health_flag != ''  "><![CDATA[ AND A.EXCEPTION_HEALTH_FLAG = #{exception_health_flag} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyHolderLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyHolderLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_POLICY_HOLDER_LOG__LOG_ID.NEXTVAL from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_HOLDER_LOG(
				ADDRESS_ID, INSERT_TIME, CUSTOMER_ID, CUSTOMER_HEIGHT, UPDATE_TIME, JOB_CODE, CUSTOMER_WEIGHT, 
				APPLY_CODE, INSERT_TIMESTAMP, LOG_ID, POLICY_CODE, UPDATE_BY, JOB_UNDERWRITE, LIST_ID, 
				UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, INSERT_BY, POLICY_ID , SOCI_SECU, SMOKING,ANNUAL_INCOME_CEIL,INCOME_SOURCE,RESIDENT_TYPE,AGENT_RELATION,APPLICANT_SPE_PEOPLE,NEW_RESIDENT,EDUCATIONAL_BACKGROUND,RISK_ESTIMATE_SCORE,NON_SMOKER,EXCEPTION_HEALTH_FLAG  ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{customer_id, jdbcType=NUMERIC} , #{customer_height, jdbcType=NUMERIC} , SYSDATE , #{job_code, jdbcType=VARCHAR} , #{customer_weight, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{job_underwrite, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{soci_secu, jdbcType=NUMERIC} , #{smoking, jdbcType=NUMERIC}
				, #{annual_income_ceil, jdbcType=NUMERIC}, #{income_source, jdbcType=VARCHAR}, #{resident_type, jdbcType=VARCHAR}, #{agent_relation, jdbcType=VARCHAR},#{APPLICANT_SPE_PEOPLE, jdbcType=NUMERIC},#{new_resident, jdbcType=NUMERIC} 
				, #{educational_background, jdbcType=VARCHAR} ,#{risk_estimate_score, jdbcType=NUMERIC},#{non_smoker, jdbcType=VARCHAR},#{exception_health_flag, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyHolderLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyHolderLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_HOLDER_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
		    SMOKING = #{smoking, jdbcType=NUMERIC} ,
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC} ,
		    INCOME_SOURCE = #{income_source, jdbcType=VARCHAR} ,
		    RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR} ,
		    AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR} ,
		    APPLICANT_SPE_PEOPLE = #{APPLICANT_SPE_PEOPLE, jdbcType=NUMERIC},
		    NEW_RESIDENT = #{new_resident, jdbcType=NUMERIC},
		    EDUCATIONAL_BACKGROUND = #{educational_background, jdbcType=VARCHAR},
		    RISK_ESTIMATE_SCORE = #{risk_estimate_score, jdbcType=NUMERIC},
		    NON_SMOKER = #{non_smoker, jdbcType=VARCHAR},
		    EXCEPTION_HEALTH_FLAG = #{exception_health_flag, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyHolderLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID,A.NEW_RESIDENT,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER,A.EXCEPTION_HEALTH_FLAG  FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyHolderLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyHolderLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID,A.NEW_RESIDENT,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER,A.EXCEPTION_HEALTH_FLAG  FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderLogWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyHolderLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID,A.NEW_RESIDENT,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER,A.EXCEPTION_HEALTH_FLAG  FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyHolderLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID,A.NEW_RESIDENT,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER,A.EXCEPTION_HEALTH_FLAG  FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyHolderLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyHolderLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT, B.JOB_CODE, B.CUSTOMER_WEIGHT, B.APPLICANT_SPE_PEOPLE,
			B.APPLY_CODE, B.LOG_ID, B.POLICY_CODE, B.JOB_UNDERWRITE, B.LIST_ID, B.SOCI_SECU, B.SMOKING,B.ANNUAL_INCOME_CEIL,B.INCOME_SOURCE,B.RESIDENT_TYPE,AGENT_RELATION,
			B.LOG_TYPE, B.POLICY_CHG_ID, B.POLICY_ID,B.NEW_RESIDENT,B.EDUCATIONAL_BACKGROUND,B.RISK_ESTIMATE_SCORE,B.NON_SMOKER,B.EXCEPTION_HEALTH_FLAG  FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.LOG_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.LOG_TYPE, A.POLICY_CHG_ID, A.POLICY_ID,A.NEW_RESIDENT,A.EDUCATIONAL_BACKGROUND,A.RISK_ESTIMATE_SCORE,A.NON_SMOKER,A.EXCEPTION_HEALTH_FLAG  FROM APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
