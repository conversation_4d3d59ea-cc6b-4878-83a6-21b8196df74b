<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IQuestionaireCustomerParamDao">
<!--
	<sql id="questionaireCustomerParamWhereCondition">
		<if test=" customer_survey_id  != null "><![CDATA[ AND A.CUSTOMER_SURVEY_ID = #{customer_survey_id} ]]></if>
		<if test=" survey_param != null and survey_param != ''  "><![CDATA[ AND A.SURVEY_PARAM = #{survey_param} ]]></if>
		<if test=" survey_params_id  != null "><![CDATA[ AND A.SURVEY_PARAMS_ID = #{survey_params_id} ]]></if>
		<if test=" survey_id  != null "><![CDATA[ AND A.SURVEY_ID = #{survey_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryQuestionaireCustomerParamBySurveyParamsIdCondition">
		<if test=" survey_params_id  != null "><![CDATA[ AND A.SURVEY_PARAMS_ID = #{survey_params_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addQuestionaireCustomerParam"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="survey_params_id">
			select APP___PAS__DBUSER.S_QUESTIONAIRE_CUSTOMER_PARAM.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM(
				INSERT_TIMESTAMP, CUSTOMER_SURVEY_ID, SURVEY_PARAM, UPDATE_BY, INSERT_TIME, SURVEY_PARAMS_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, SURVEY_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{customer_survey_id, jdbcType=NUMERIC} , #{survey_param, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{survey_params_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{survey_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteQuestionaireCustomerParam" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM WHERE SURVEY_PARAMS_ID = #{survey_params_id} ]]>
	</delete>
<!-- 修改操作 -->
	<update id="updateQuestionaireCustomerParam" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_SURVEY_ID = #{customer_survey_id, jdbcType=NUMERIC} ,
			SURVEY_PARAM = #{survey_param, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    SURVEY_ID = #{survey_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE SURVEY_PARAMS_ID = #{survey_params_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findQuestionaireCustomerParamBySurveyParamsId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_SURVEY_ID, A.SURVEY_PARAM, A.SURVEY_PARAMS_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM A WHERE 1 = 1  ]]>
		<include refid="queryQuestionaireCustomerParamBySurveyParamsIdCondition" />
		<![CDATA[ ORDER BY A.SURVEY_PARAMS_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapQuestionaireCustomerParam" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_SURVEY_ID, A.SURVEY_PARAM, A.SURVEY_PARAMS_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_PARAMS_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllQuestionaireCustomerParam" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_SURVEY_ID, A.SURVEY_PARAM, A.SURVEY_PARAMS_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_PARAMS_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findQuestionaireCustomerParamTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryQuestionaireCustomerParamForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_SURVEY_ID, B.SURVEY_PARAM, B.SURVEY_PARAMS_ID, 
			B.SURVEY_ID FROM (
					SELECT ROWNUM RN, A.CUSTOMER_SURVEY_ID, A.SURVEY_PARAM, A.SURVEY_PARAMS_ID, 
			A.SURVEY_ID FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER_PARAM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SURVEY_PARAMS_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
		<delete id="deleteQuestionaireCustomerParamByCs" parameterType="java.util.Map">
		<![CDATA[ delete APP___PAS__DBUSER.t_questionaire_customer_param qcp
					where qcp.customer_survey_id in  (
					select qc1.customer_survey_id
					          from APP___PAS__DBUSER.t_questionaire_customer qc1,APP___PAS__DBUSER.t_questionaire_info qi,APP___PAS__DBUSER.t_questionaire_survey_version qsv
					          where 
					           qc1.survey_question_id = qi.survey_question_id
					          and qi.survey_version =  qsv.survey_version_code
					           and qsv.survey_version_code = ${survey_version_code}
			          			and qc1.policy_id = ${policy_id}
						)
 ]]>
	</delete>
</mapper>
