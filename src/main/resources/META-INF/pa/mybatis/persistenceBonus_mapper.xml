<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPersistenceBonusDao">

	<sql id="PA_persistenceBonusWhereCondition">
		<if test=" batch_date  != null  and  batch_date  != ''  "><![CDATA[ AND A.BATCH_DATE = #{batch_date} ]]></if>
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" process_status  != null "><![CDATA[ AND A.PROCESS_STATUS = #{process_status} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" arap_fee_id  != null "><![CDATA[ AND A.ARAP_FEE_ID = #{arap_fee_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPersistenceBonusByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPersistenceBonusByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryPersistenceBonusByProductIdCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPersistenceBonus"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PERSISTENCE_BONUS__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PERSISTENCE_BONUS(
				BATCH_DATE, INVEST_ID, INSERT_TIME, ITEM_ID, UPDATE_TIME, ACCOUNT_CODE, PAY_YEAR, 
				INSERT_TIMESTAMP, PROCESS_STATUS, UPDATE_BY, FEE_AMOUNT, LIST_ID, PAY_DUE_DATE, UPDATE_TIMESTAMP, 
				INSERT_BY, BUSI_ITEM_ID, POLICY_ID, ARAP_FEE_ID,POLICY_CHG_ID ) 
			VALUES (
				#{batch_date, jdbcType=DATE}, #{invest_id, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} , SYSDATE , #{account_code, jdbcType=VARCHAR} , #{pay_year, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{process_status, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{pay_due_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{arap_fee_id, jdbcType=NUMERIC},#{policy_chg_id} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePersistenceBonus" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePersistenceBonus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PERSISTENCE_BONUS ]]>
		<set>
		<trim suffixOverrides=",">
		    BATCH_DATE = #{batch_date, jdbcType=DATE} ,
		    INVEST_ID = #{invest_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
		    PROCESS_STATUS = #{process_status, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    ARAP_FEE_ID = #{arap_fee_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id,jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPersistenceBonusByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID,A.POLICY_CHG_ID, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A WHERE 1 = 1  ]]>
		<include refid="PA_queryPersistenceBonusByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findPersistenceBonusByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID,A.POLICY_CHG_ID, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A WHERE 1 = 1  ]]>
		<include refid="PA_queryPersistenceBonusByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findPersistenceBonusByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.POLICY_CHG_ID,A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A WHERE 1 = 1  ]]>
		<include refid="PA_queryPersistenceBonusByProductIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPersistenceBonus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID,A.POLICY_CHG_ID, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPersistenceBonus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID,A.POLICY_CHG_ID, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A WHERE 1=1 ]]>
		<!-- <include refid="请添加查询条件" />-->
		<if test="policy_id != null and policy_id != '' ">
			<![CDATA[  AND A.POLICY_ID = #{policy_id} ]]>
		</if>
		<if test="busi_item_id != null and busi_item_id != '' ">
			<![CDATA[  AND A.BUSI_ITEM_ID = #{busi_item_id} ]]>
		</if>
		<![CDATA[ AND ROWNUM <= 1000 ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPersistenceBonusTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPersistenceBonusForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BATCH_DATE, B.INVEST_ID, B.ITEM_ID, B.ACCOUNT_CODE, B.PAY_YEAR, 
			B.PROCESS_STATUS, B.FEE_AMOUNT, B.LIST_ID, B.PAY_DUE_DATE, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.POLICY_CHG_ID,B.ARAP_FEE_ID FROM (
					SELECT ROWNUM RN, A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID,A.POLICY_CHG_ID, A.ARAP_FEE_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
