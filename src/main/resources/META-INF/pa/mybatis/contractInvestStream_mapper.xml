<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractInvestStream">

	<sql id="PA_contractInvestStreamWhereCondition">
		<if test=" accum_units  != null "><![CDATA[ AND A.ACCUM_UNITS = #{accum_units} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		<if test=" stream_invest_id  != null "><![CDATA[ AND A.STREAM_INVEST_ID = #{stream_invest_id} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" prem_type != null and prem_type != ''  "><![CDATA[ AND A.PREM_TYPE = #{prem_type} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractInvestStreamByStreamInvestIdCondition">
		<if test=" stream_invest_id  != null "><![CDATA[ AND A.STREAM_INVEST_ID = #{stream_invest_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractInvestStreamByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractInvestStreamByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractInvestStream"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="stream_invest_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_INVEST_STREAM__STRE.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM(
				ACCUM_UNITS, INTEREST_CAPITAL, INVEST_ID, STREAM_INVEST_ID, INSERT_TIME, INTEREST_SUM, UPDATE_TIME, 
				ITEM_ID, ACCOUNT_CODE, INSERT_TIMESTAMP, START_DATE, UPDATE_BY, PREM_TYPE, INTEREST_BALANCE, 
				UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, STREAM_ID ) 
			VALUES (
				#{accum_units, jdbcType=NUMERIC}, #{interest_capital, jdbcType=NUMERIC} , #{invest_id, jdbcType=NUMERIC} , #{stream_invest_id, jdbcType=NUMERIC} , SYSDATE , #{interest_sum, jdbcType=NUMERIC} , SYSDATE 
				, #{item_id, jdbcType=NUMERIC} , #{account_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{start_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{prem_type, jdbcType=VARCHAR} , #{interest_balance, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{stream_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractInvestStream" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM WHERE STREAM_INVEST_ID = #{stream_invest_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractInvestStream" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM ]]>
		<set>
		<trim suffixOverrides=",">
		    ACCUM_UNITS = #{accum_units, jdbcType=NUMERIC} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    INVEST_ID = #{invest_id, jdbcType=NUMERIC} ,
		    STREAM_INVEST_ID = #{stream_invest_id, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			PREM_TYPE = #{prem_type, jdbcType=VARCHAR} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    STREAM_ID = #{stream_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE STREAM_INVEST_ID = #{stream_invest_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractInvestStreamByStreamInvestId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.START_DATE, A.PREM_TYPE, A.INTEREST_BALANCE, 
			A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestStreamByStreamInvestIdCondition" />
	</select>
	
	<select id="PA_findContractInvestStreamByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.START_DATE, A.PREM_TYPE, A.INTEREST_BALANCE, 
			A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestStreamByItemIdCondition" />
	</select>
	
	<select id="PA_findContractInvestStreamByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.START_DATE, A.PREM_TYPE, A.INTEREST_BALANCE, 
			A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestStreamByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractInvestStream" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.START_DATE, A.PREM_TYPE, A.INTEREST_BALANCE, 
			A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractInvestStream" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.START_DATE, A.PREM_TYPE, A.INTEREST_BALANCE, 
			A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractInvestStreamWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractInvestStreamTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractInvestStreamForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCUM_UNITS, B.INTEREST_CAPITAL, B.INVEST_ID, B.STREAM_INVEST_ID, B.INTEREST_SUM, 
			B.ITEM_ID, B.ACCOUNT_CODE, B.START_DATE, B.PREM_TYPE, B.INTEREST_BALANCE, 
			B.POLICY_ID, B.STREAM_ID FROM (
					SELECT ROWNUM RN, A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.INVEST_ID, A.STREAM_INVEST_ID, A.INTEREST_SUM, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.START_DATE, A.PREM_TYPE, A.INTEREST_BALANCE, 
			A.POLICY_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_STREAM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
