<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRenewConfirmTraceDao">
<!--
	<sql id="renewConfirmTraceWhereCondition">
		<if test=" renew_confirm_flag  != null "><![CDATA[ AND A.RENEW_CONFIRM_FLAG = #{renew_confirm_flag} ]]></if>
		<if test=" trace_id  != null "><![CDATA[ AND A.TRACE_ID = #{trace_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_sequence_no != null and policy_sequence_no != ''  "><![CDATA[ AND <PERSON>.POLICY_SEQUENCE_NO = #{policy_sequence_no} ]]></if>
		<if test=" change_date  != null  and  change_date  != ''  "><![CDATA[ AND A.CHANGE_DATE = #{change_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryRenewConfirmTraceByTraceIdCondition">
		<if test=" trace_id  != null "><![CDATA[ AND A.TRACE_ID = #{trace_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addRenewConfirmTrace"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="trace_id">
			SELECT APP___PAS__DBUSER.S_RENEW_CON_TRACE__TRACE_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE(
				INSERT_TIMESTAMP, RENEW_CONFIRM_FLAG, POLICY_ID, IS_E04, RESPONSE_CODE, TRACE_ID, POLICY_CODE, UPDATE_BY, POLICY_SEQUENCE_NO, INSERT_TIME, 
				CHANGE_DATE, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{renew_confirm_flag, jdbcType=NUMERIC}, #{policy_id, jdbcType=NUMERIC}, #{is_e04, jdbcType=NUMERIC}, #{response_code, jdbcType=VARCHAR}
				, #{trace_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{policy_sequence_no, jdbcType=VARCHAR} , SYSDATE 
				, #{change_date, jdbcType=DATE} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRenewConfirmTrace" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE WHERE TRACE_ID = #{trace_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateRenewConfirmTrace" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE ]]>
		<set>
		<trim suffixOverrides=",">
		    RENEW_CONFIRM_FLAG = #{renew_confirm_flag, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			POLICY_SEQUENCE_NO = #{policy_sequence_no, jdbcType=VARCHAR} ,
		    CHANGE_DATE = #{change_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE TRACE_ID = #{trace_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findRenewConfirmTraceByTraceId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RENEW_CONFIRM_FLAG, A.TRACE_ID, A.POLICY_CODE, A.POLICY_SEQUENCE_NO, 
			A.CHANGE_DATE FROM APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE A WHERE 1 = 1  ]]>
		<include refid="queryRenewConfirmTraceByTraceIdCondition" />
		<![CDATA[ ORDER BY A.TRACE_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapRenewConfirmTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RENEW_CONFIRM_FLAG, A.TRACE_ID, A.POLICY_CODE, A.POLICY_SEQUENCE_NO, 
			A.CHANGE_DATE FROM APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TRACE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRenewConfirmTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RENEW_CONFIRM_FLAG, A.TRACE_ID, A.POLICY_CODE, A.POLICY_SEQUENCE_NO, 
			A.CHANGE_DATE FROM APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TRACE_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findRenewConfirmTraceTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryRenewConfirmTraceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RENEW_CONFIRM_FLAG, B.TRACE_ID, B.POLICY_CODE, B.POLICY_SEQUENCE_NO, 
			B.CHANGE_DATE FROM (
					SELECT ROWNUM RN, A.RENEW_CONFIRM_FLAG, A.TRACE_ID, A.POLICY_CODE, A.POLICY_SEQUENCE_NO, 
			A.CHANGE_DATE FROM APP___PAS__DBUSER.T_RENEW_CONFIRM_TRACE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TRACE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
