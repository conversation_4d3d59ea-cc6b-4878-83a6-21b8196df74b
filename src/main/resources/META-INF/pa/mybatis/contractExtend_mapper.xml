<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractExtend">

	<sql id="PA_contractExtendWhereCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" extraction_due_date  != null  and  extraction_due_date  != ''  "><![CDATA[ AND A.EXTRACTION_DUE_DATE = #{extraction_due_date} ]]></if>
		<if test=" billing_date  != null  and  billing_date  != ''  "><![CDATA[ AND A.BILLING_DATE = #{billing_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" renew_decision_status  != null "><![CDATA[ AND A.RENEW_DECISION_STATUS = #{renew_decision_status} ]]></if>
		<if test=" policy_period  != null "><![CDATA[ AND A.POLICY_PERIOD = #{policy_period} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" prem_status != null and prem_status != ''  "><![CDATA[ AND A.PREM_STATUS = #{prem_status} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_ID in (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractExtendByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByPremStatusCondition">
		<if test=" prem_status != null and prem_status != '' "><![CDATA[ AND A.PREM_STATUS = #{prem_status} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByExtractionDueDateCondition">
		<if test=" extraction_due_date  != null "><![CDATA[ AND A.EXTRACTION_DUE_DATE = #{extraction_due_date} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByBusiItemIdCondition">
		<if test=" busi_item_id != null and busi_item_id != '' "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>
	<sql id="PA_findContractExtendByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>
	<sql id="PA_findContractExtendByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	
<!-- 添加操作 -->
	<insert id="PA_addContractExtend"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_EXTEND__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_EXTEND(
				INSERT_TIME, POLICY_YEAR, EXTRACTION_DUE_DATE, UPDATE_TIME, ITEM_ID, BILLING_DATE, INSERT_TIMESTAMP, 
				ORGAN_CODE, RENEW_DECISION_STATUS, POLICY_CODE, UPDATE_BY, POLICY_PERIOD, LIST_ID, PAY_DUE_DATE, 
				UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, POLICY_ID, PREM_STATUS ,NEXT_PREM ) 
			VALUES (
				SYSDATE, #{policy_year, jdbcType=NUMERIC} , #{extraction_due_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{billing_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{organ_code, jdbcType=VARCHAR} , #{renew_decision_status, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{policy_period, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{pay_due_date, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{prem_status, jdbcType=NUMERIC},#{next_prem, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractExtend" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND WHERE LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractExtend" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_EXTEND A]]>
		<set>
		<trim suffixOverrides=",">
		    A.POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
		    A.EXTRACTION_DUE_DATE = #{extraction_due_date, jdbcType=DATE} ,
			A.UPDATE_TIME = SYSDATE , 
		    A.ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    A.BILLING_DATE = #{billing_date, jdbcType=DATE} ,
			A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    A.RENEW_DECISION_STATUS = #{renew_decision_status, jdbcType=NUMERIC} ,
			A.POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    A.UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    A.POLICY_PERIOD = #{policy_period, jdbcType=NUMERIC} ,
		    A.PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    A.BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    A.POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			A.PREM_STATUS = #{prem_status, jdbcType=NUMERIC} ,
			<if test=" next_prem  != null and next_prem !='' ">
			<![CDATA[ A.NEXT_PREM = #{next_prem, jdbcType=NUMERIC} , ]]>
			</if>	
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
		<include refid="PA_queryContractExtendByListIdCondition" />
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractExtendByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByListIdCondition" />
	</select>
	
	<select id="PA_findContractExtendByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByPolicyIdCondition" />
		<include refid="PA_queryContractExtendByPremStatusCondition" />
		<![CDATA[
			ORDER BY A.PAY_DUE_DATE DESC
		]]>
	</select>
	
	<select id="PA_findContractExtendByPremStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByPremStatusCondition" />
	</select>
	
	<select id="PA_findContractExtendByExtractionDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByExtractionDueDateCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractExtend" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractExtendWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractExtend" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractExtendWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractExtendTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_contractExtendWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractExtendForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_YEAR, B.EXTRACTION_DUE_DATE, B.ITEM_ID, B.BILLING_DATE, 
			B.ORGAN_CODE, B.RENEW_DECISION_STATUS, B.POLICY_CODE, B.POLICY_PERIOD, B.LIST_ID, B.PAY_DUE_DATE, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.PREM_STATUS, A.NEXT_PREM FROM (
					SELECT ROWNUM RN, A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_contractExtendWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 查询单条 -->
	<select id="PA_findContractExtend" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B WHERE A.BUSI_ITEM_ID=B.BUSI_ITEM_ID and 1 = 1  ]]>
		<!-- <include refid="PA_queryContractExtendByPolicyIdCondition" />
		<include refid="PA_findContractExtendByPolicyCodeCondition" />
		<include refid="PA_queryContractExtendByBusiItemIdCondition" />
		<include refid="PA_findContractExtendByItemIdCondition" />
		<include refid="PA_queryContractExtendByListIdCondition" /> -->
		<include refid="PA_contractExtendWhereCondition" />
	</select>
	
<!--  -->
	<select id="PA_findContractExtendByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.POLICY_YEAR, A.POLICY_PERIOD,A.BUSI_ITEM_ID, A.POLICY_ID,TC.PAY_DUE_DATE
              FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A,(SELECT MAX(TCE.PAY_DUE_DATE) PAY_DUE_DATE,TCE.BUSI_ITEM_ID
                          FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE GROUP BY TCE.BUSI_ITEM_ID) TC
                WHERE TC.BUSI_ITEM_ID = A.BUSI_ITEM_ID]]>
		<include refid="PA_queryContractExtendByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 	鏍规嵁璐ｄ换缁処D鏌ヨ鍗曟潯鏁版嵁 -->
	<select id="PA_findContractExtendByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE 1 = 1 ]]>
		<include refid="PA_findContractExtendByItemIdCondition" />
		<include refid="PA_findContractExtendByPolicyCodeCondition" />
		<include refid="PA_queryContractExtendByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
 
 
 	<select id="PA_findContractExtendByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
 	
		<![CDATA[ 
				SELECT distinct pay_due_date FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A 
			WHERE A.POLICY_ID = #{policy_id}
		]]>
		
		<if test=" start_date  != null "><![CDATA[ AND A.PAY_DUE_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.PAY_DUE_DATE <= #{end_date} ]]></if>
	</select>
	
	<update id="updateExtractionDueDate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_EXTEND A]]>
		<set>
		<trim suffixOverrides=",">
		    A.EXTRACTION_DUE_DATE = #{extraction_due_date, jdbcType=DATE},
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 AND A.BUSI_ITEM_ID IN 
		(SELECT TCBP.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP WHERE 
		TCBP.LIABILITY_STATE = 1 AND TCBP.POLICY_CODE = #{policy_code}]]>
		<if test="busiProdCodeList  != null and busiProdCodeList.size()!=0 ">
			<![CDATA[ AND TCBP.BUSI_PROD_CODE in (]]>
			<foreach collection="busiProdCodeList" item="busi_prod_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[)]]>
	</update>

	<!-- 出险日后查询缴费计划 -->
	<select id="PA_findContractExtendByBusiItemIdList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,
       A.POLICY_ID,
       A.BUSI_ITEM_ID,
       A.ITEM_ID,
       A.POLICY_CODE,
       A.ORGAN_CODE,
       A.PAY_DUE_DATE,
       A.EXTRACTION_DUE_DATE,
       A.POLICY_YEAR,
       A.POLICY_PERIOD,
       A.PREM_STATUS,
       A.BILLING_DATE,
       A.NEXT_PREM,
       A.RENEW_DECISION_STATUS,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1 ]]>
       <if test="busiItemIdWaiver  != null and busiItemIdWaiver.size()!=0 ">
			<![CDATA[ AND A.BUSI_ITEM_ID IN (]]>
			<foreach collection="busiItemIdWaiver" item="busiItemId_Waiver"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{busiItemId_Waiver} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 根据保单ID查询减额缴清 -->
	<select id="PA_findDeductedPaymentByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CASE WHEN COUNT(A.LIST_ID) > 0 THEN 1
			ELSE 0 END AS CAPITAL_BALANCE
			FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE A.POLICY_ID = #{policy_id} AND A.PREM_STATUS = '3' 
		]]>
	</select>
	
	<!-- 根据policycode查询主险月交应缴未缴信息 -->
	<select id="PA_findConExtendForQueryAIPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TCP.PREM_FREQ, TCE.PAY_DUE_DATE, TCE.PREM_STATUS,  
			       TCE.POLICY_CODE, TCE.POLICY_ID 
			  FROM DEV_PAS.T_CONTRACT_PRODUCT   TCP,
			       DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
			       DEV_PAS.T_CONTRACT_EXTEND    TCE
			 WHERE 1 = 1
			   AND TCP.POLICY_CODE = TCBP.POLICY_CODE
			   AND TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			   AND TCP.POLICY_CODE = TCE.POLICY_CODE
			   AND TCP.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
			   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
			   AND TCP.PREM_FREQ = '2'
			   AND TCE.PREM_STATUS != '2'
			   AND TCE.PAY_DUE_DATE < TRUNC(SYSDATE, 'yyyy') 
			   AND TCE.POLICY_CODE = #{policy_code} 
		]]>
	</select>
	
	<!-- 根据BUSIITEMID查询主险下期应缴费日 -->
	<!-- <select id="PA_findPAYDUEDATEByBUSIITEMID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TCP.PREM_FREQ, TCE.PAY_DUE_DATE, TCE.PREM_STATUS,  
			       TCE.POLICY_CODE, TCE.POLICY_ID 
			  FROM DEV_PAS.T_CONTRACT_PRODUCT   TCP,
			       DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
			       DEV_PAS.T_CONTRACT_EXTEND    TCE
			 WHERE 1 = 1
			   AND TCP.POLICY_CODE = TCBP.POLICY_CODE
			   AND TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			   AND TCP.POLICY_CODE = TCE.POLICY_CODE
			   AND TCP.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
			   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
			   AND TCBP.LIABILITY_STATE = '1'
			   AND TCE.BUSI_ITEM_ID = #{busi_item_id} 
		]]>
	</select> -->
	<!-- 根据险种id和保单号查询险种续期是否缴清 -->
	<select id="PA_findPAYDUEDATEByBUSIITEMID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT  TCE.PAY_DUE_DATE, TCE.PREM_STATUS,  
			       TCE.POLICY_CODE, TCE.POLICY_ID 
			  FROM DEV_PAS.T_CONTRACT_EXTEND   TCE
			 WHERE 1 = 1
			   AND TCE.POLICY_CODE = #{policy_code}
			   AND TCE.BUSI_ITEM_ID = #{busi_item_id} 
			   AND TCE.PREM_STATUS in ('2','3')
		]]>
	</select>
	<select id="PA_findNextPayDateByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT  A.PAY_DUE_DATE,B.IS_MASTER_ITEM
			FROM DEV_PAS.T_CONTRACT_EXTEND   A, DEV_PAS.T_CONTRACT_PRODUCT   B
			 WHERE A.ITEM_ID = B.ITEM_ID
			   AND A.POLICY_CODE = #{policy_code}
			   AND A.BUSI_ITEM_ID = #{busi_item_id}
			 ORDER BY B.IS_MASTER_ITEM DESC
		]]>
	</select>
</mapper>
