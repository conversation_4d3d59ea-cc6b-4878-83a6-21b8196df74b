<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IDayDetailCheckDao">
	<sql id="dayDetailCheckCondition">
		<if test="finish_time != null and finish_time !='' "><![CDATA[and t.finish_time = #{finish_time}]]></if>
		<if test="service_bank_branch != null and service_bank_branch !='' "><![CDATA[and t.service_bank_branch = #{service_bank_branch}]]></if>
		<if test="service_bank != null and service_bank !='' "><![CDATA[and t.service_bank = #{service_bank}]]></if>
	</sql>

	<!-- 执行语句 -->
	<select id="findPolicy" resultType="java.util.Map" parameterType="java.util.Map">	
		<![CDATA[
			SELECT T.POLICY_CODE,
        T.TOTAL_PREM_AF,
        T.FEE_AMOUNT,
        T.DUE_TIME,
        T.APPLY_CODE,
        TCM.LIABILITY_STATE,
        T.BASIC_REMARK,
        TCM.BRANCH_CODE,
        T.SERVICE_BANK,
        T.SERVICE_BANK_BRANCH,
        TPH.CUSTOMER_ID,
        T.HOLDER_NAME,
        T.SERVICE_HANDLER_CODE
   FROM(SELECT *
        FROM APP___PAS__DBUSER.T_PREM_ARAP TPA LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM ON TPA.POLICY_CODE = TCM.POLICY_CODE 
        LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP ON TCM.POLICY_CODE = TCP.POLICY_CODE) T ,APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,APP___PAS__DBUSER.T_POLICY_HOLDER TPH
  		WHERE   TCM.POLICY_CODE = T.POLICY_CODE
         AND TPH.POLICY_CODE = T.POLICY_CODE]]>
		<include refid="dayDetailCheckCondition" />
	</select>
</mapper>
