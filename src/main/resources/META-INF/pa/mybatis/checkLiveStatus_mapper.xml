<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="checkLiveStatusDaoImpl">

<!--leihong 批处理抽档条件 -->
	<sql id="extractPayPlanCondition">
		<!-- 保单号 -->
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND a.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]></if>
		<!-- 批处理执行日期 -->
	    <if test=" min_batch_date  != null and min_batch_date !='' "><![CDATA[ AND a.PAY_DUE_DATE >= #{min_batch_date,jdbcType=DATE} ]]></if>
	    <if test=" max_batch_date  != null and max_batch_date !='' "><![CDATA[ AND a.PAY_DUE_DATE <= #{max_batch_date,jdbcType=DATE} ]]></if>
	    <if test=" organ_code  != null and organ_code !='' "><![CDATA[ AND T.organ_code <= #{organ_code,jdbcType=DATE} ]]></if>
	</sql>
    <sql id="updPayPlanCondition">
		<!-- 保单号 -->
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]></if>
		<!-- 批处理执行日期 -->
	    <if test=" min_batch_date  != null and min_batch_date !='' "><![CDATA[ AND PAY_DUE_DATE >= #{min_batch_date,jdbcType=DATE} ]]></if>
	    <if test=" max_batch_date  != null and max_batch_date !='' "><![CDATA[ AND PAY_DUE_DATE <= #{max_batch_date,jdbcType=DATE} ]]></if>
	</sql>
	
	<!-- song 批处理查询 总记录数 -->
	<select id="getPayPlanCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		       SELECT
       COUNT(1)
   FROM APP___PAS__DBUSER.T_PAY_PLAN A
  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
     ON A.POLICY_ID = B.POLICY_ID
  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
     ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT D
     ON A.ITEM_ID = D.ITEM_ID
  WHERE A.SURVIVAL_INVEST_FLAG = 1
    AND A.PAY_PLAN_TYPE = 3
    AND A.PAY_NUM > 0
    AND A.SURVIVAL_MODE = 1
    AND (A.SURVIVAL_INVEST_RESULT != 2 OR A.SURVIVAL_INVEST_RESULT IS NULL)
   
        ]]>
		<include refid="extractPayPlanCondition" />
	</select>
	
	<!-- song 批处理查询  符合条件的信息-->
	<select id="getPayPlanList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		     SELECT * FROM(SELECT B.VALIDATE_DATE,
        B.APPLY_CODE,
        B.BRANCH_CODE,
        B.ORGAN_CODE,
        B.POLICY_TYPE,
        B.CHANNEL_TYPE,
        B.SERVICE_BANK,
        B.SERVICE_BANK_BRANCH,
        C.LIABILITY_STATE,
        C.RENEW_TIMES,
        C.BUSI_PRD_ID,
        D.MATURITY_DATE,
        D.EXPIRY_DATE,
        D.PREM_FREQ,
        D.CHARGE_YEAR,
        D.CHARGE_PERIOD,
        D.COVERAGE_PERIOD,
        D.COVERAGE_YEAR,
        D.AMOUNT,
        D.BONUS_SA,
        D.TOTAL_PREM_AF,
        D.INITIAL_DISCNT_PREM_AF,
        D.UNIT,
        D.PAY_YEAR,
        D.PAY_PERIOD,
        D.IS_MASTER_ITEM,
        D.PRODUCT_ID,
        D.PAY_FREQ,
        A.PLAN_ID,
        A.POLICY_ID,
        B.POLICY_CODE,
        A.BUSI_ITEM_ID,
        A.ITEM_ID,
        A.BUSI_PROD_CODE,
        A.PRODUCT_CODE,
        A.LIAB_ID,
        A.LIAB_NAME,
        A.LIAB_CODE,
        A.PAY_DUE_DATE,
        A.PAY_NUM,
        A.BEGIN_DATE,
        A.END_DATE,
        A.PAY_STATUS,
        A.SURVIVAL_MODE,
        A.PAY_PLAN_TYPE,
        A.BENE_AMOUNT,
        A.TOTAL_AMOUNT,
        A.INSTALMENT_AMOUNT,
        A.SURVIVAL_INVEST_FLAG,
        A.SURVIVAL_INVEST_RESULT,
        A.GURNT_PAY_LIAB,
        A.GURNT_PAY_PERIOD,
        A.ONE_TIME_FLAG,
        A.PAY_TYPE,
        B.TRUST_BUSI_FLAG
   FROM APP___PAS__DBUSER.T_PAY_PLAN A
  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
     ON A.POLICY_ID = B.POLICY_ID
  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
     ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT D
     ON A.ITEM_ID = D.ITEM_ID
  WHERE A.SURVIVAL_INVEST_FLAG = 1
    AND A.PAY_PLAN_TYPE = 3
    AND A.PAY_NUM > 0
    AND A.SURVIVAL_MODE = 1
        ]]>
		<include refid="extractPayPlanCondition" />
		<![CDATA[) T WHERE MOD(T.POLICY_ID,#{modNum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}]]>
	</select>
	
	<!-- song 查询第一别保人-->
	<select id="checkInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT C.CUSTOMER_ID,C.CUSTOMER_NAME, C.CUSTOMER_CERTI_CODE,C.CUSTOMER_BIRTHDAY, C.CUSTOMER_GENDER,C.CUSTOMER_CERT_TYPE,
        C.LIVE_STATUS,C.DEATH_DATE,A.ORDER_ID
        FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A
        INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST B
        ON A.INSURED_ID = B.LIST_ID
        INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
        ON B.CUSTOMER_ID = C.CUSTOMER_ID
        WHERE  A.ORDER_ID=1
		
        
        ]]>
        <![CDATA[ AND A.POLICY_ID = #{policy_id,jdbcType=NUMERIC} ]]>
        <![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id,jdbcType=NUMERIC} ]]>
	</select>
	
	<!-- 修改生调标识  生调结论 -->
	<update id="updateCheckLiveStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_PAY_PLAN SET
			survival_invest_flag = #{survival_invest_flag, jdbcType=NUMERIC},
			survival_invest_result = #{survival_invest_result, jdbcType=NUMERIC}
		]]>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="updPayPlanCondition" />
	</update>
	
	<select id="findSurvivalInvestTrace" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM APP___PAS__DBUSER.T_SURVIVAL_INVEST_TRACE a where 1 = 1 
		<if test=" policy_id  != null and policy_id !='' "><![CDATA[ AND a.POLICY_id = #{policy_id,jdbcType=NUMERIC} ]]></if>
		<if test=" pay_due_date  != null and pay_due_date !='' "><![CDATA[ AND a.PAY_DUE_DATE = #{pay_due_date,jdbcType=DATE} ]]></if>
	</select>
	
	<insert id="addSurvivalInvestTrace"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SUR_INVEST_TRACE__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVIVAL_INVEST_TRACE(
				LIST_ID,
				POLICY_ID,
				PLAN_ID,
				CUSTOMER_ID,
				SURVIVAL_INVEST_DATE,
				SURVIVAL_INVEST_RESULT,
				SURVIVAL_RESULT,
				PAY_DUE_DATE,
				INSERT_BY,
				INSERT_TIME,
				INSERT_TIMESTAMP,
				UPDATE_BY,
				UPDATE_TIME,
				UPDATE_TIMESTAMP)
			VALUES (
				#{list_id, jdbcType=NUMERIC} ,
				#{policy_id, jdbcType=NUMERIC} ,
				#{plan_id, jdbcType=NUMERIC} ,
				#{customer_id, jdbcType=NUMERIC} ,
				#{survival_invest_date, jdbcType=DATE} ,
				#{survival_invest_result, jdbcType=NUMERIC} ,
				#{survival_result, jdbcType=NUMERIC} ,
				#{pay_due_date, jdbcType=DATE} ,
				#{insert_by, jdbcType=NUMERIC} ,
				CURRENT_TIMESTAMP ,
				CURRENT_TIMESTAMP ,
				#{update_by, jdbcType=NUMERIC} ,
				CURRENT_TIMESTAMP ,
				CURRENT_TIMESTAMP)
			]]>	
	</insert>
	
	<select id="findMaxSurvivalDate" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
		Select a.policy_id, a.plan_id, to_date(Max(a.PAY_DUE_DATE),'yyyy-MM-dd') pay_due_date
		  From dev_pas.t_Survival_Invest_Trace a
		 where 1 = 1
		 	 and a.policy_id =  #{policy_id} 
		 	 and a.plan_id =  #{plan_id} 
		     and a.SURVIVAL_RESULT = #{survival_result} 
		 group by a.policy_id, a.plan_id
		]]>			
	</select>
	
	<select id="findMinSurvivalDate" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
		Select a.policy_id, a.plan_id, to_date(Min(a.PAY_DUE_DATE),'yyyy-MM-dd') pay_due_date
		  From dev_pas.t_Survival_Invest_Trace a
		 where 1 = 1
		 	 and a.policy_id =  #{policy_id} 
		 	 and a.plan_id =  #{plan_id} 
		     and a.SURVIVAL_RESULT = #{survival_result} 
		 group by a.policy_id, a.plan_id
		]]>			
	</select>
</mapper>