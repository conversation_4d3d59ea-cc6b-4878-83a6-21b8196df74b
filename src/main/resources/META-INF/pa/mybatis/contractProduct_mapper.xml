<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractProductDao">

	<sql id="contractProductWhereCondition">
	<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_srp} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_pause} ]]></if>
		<if test=" prod_pkg_plan_code != null and prod_pkg_plan_code != ''  "><![CDATA[ AND A.PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code} ]]></if>
		<if test=" append_prem_af  != null "><![CDATA[ AND A.APPEND_PREM_AF = #{append_prem_af} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" norenew_reason != null and norenew_reason != ''  "><![CDATA[ AND A.NORENEW_REASON = #{norenew_reason} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" initial_amount  != null "><![CDATA[ AND A.INITIAL_AMOUNT = #{initial_amount} ]]></if>
		<if test=" cc_sa  != null "><![CDATA[ AND A.CC_SA = #{cc_sa} ]]></if>
		<if test=" initial_extra_prem_af  != null "><![CDATA[ AND A.INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af} ]]></if>
		<if test=" payout_rate  != null "><![CDATA[ AND A.PAYOUT_RATE = #{payout_rate} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" on_liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE <> #{on_liability_state} ]]></if>
		<if test=" count_way != null and count_way != ''  "><![CDATA[ AND A.COUNT_WAY = #{count_way} ]]></if>
		<if test=" is_gift  != null "><![CDATA[ AND A.IS_GIFT = #{is_gift} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" additional_prem_af  != null "><![CDATA[ AND A.ADDITIONAL_PREM_AF = #{additional_prem_af} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" bonus_mode  != null "><![CDATA[ AND A.BONUS_MODE = #{bonus_mode} ]]></if>
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" interest_flag  != null "><![CDATA[ AND A.INTEREST_FLAG = #{interest_flag} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" renewal_extra_prem_af  != null "><![CDATA[ AND A.RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" health_service_flag  != null "><![CDATA[ AND A.HEALTH_SERVICE_FLAG = #{health_service_flag} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" pay_freq != null and pay_freq != ''  "><![CDATA[ AND A.PAY_FREQ = #{pay_freq} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" renewal_discnted_prem_af  != null "><![CDATA[ AND A.RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" pause_date  != null  and  pause_date  != ''  "><![CDATA[ AND A.PAUSE_DATE = #{pause_date} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" is_master_item  != null "><![CDATA[ AND A.IS_MASTER_ITEM = #{is_master_item} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" deductible_franchise  != null "><![CDATA[ AND A.DEDUCTIBLE_FRANCHISE = #{deductible_franchise} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" initial_discnt_prem_af  != null "><![CDATA[ AND A.INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af} ]]></if>
		<if test=" bonus_w_mode  != null "><![CDATA[ AND A.BONUS_W_MODE = #{bonus_w_mode} ]]></if>
		<if test=" last_bonus_date  != null "><![CDATA[ AND A.LAST_BONUS_DATE = #{last_bonus_date} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_ID in (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" annu_pay_type != null and annu_pay_type != ''  "><![CDATA[ AND A.annu_pay_type = #{annu_pay_type} ]]></if>
		<if test=" tax_extension_prem_rate != null and tax_extension_prem_rate != ''  "><![CDATA[ AND A.TAX_EXTENSION_PREM_RATE = #{tax_extension_prem_rate} ]]></if>
        <if test=" discount_type != null and discount_type != ''  "><![CDATA[ AND A.DISCOUNT_TYPE = #{discount_type} ]]></if>
	    <if test=" discount_rate != null and discount_rate != ''  "><![CDATA[ AND A.DISCOUNT_RATE = #{discount_rate} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryContractProductByItemIdCondition">
		<if test=" item_id  != null and item_id !=''"><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="queryContractProductByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id != null and busi_item_id != '' "><![CDATA[ AND A.busi_item_id = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractProductByPolicyIdCodeCondition">
		<if test=" policy_id != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="PA_queryContractProductByProductCodeCondition">
		<if test=" product_code != null and product_code != '' "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
	</sql>	
	<sql id="PA_queryContractProductByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>

<!-- 添加操作 -->
	<insert id="addContractProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="item_id">			
                SELECT APP___PAS__DBUSER.S_CONTRACT_PRODUCT__ITEM_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_PRODUCT(
				IS_MASTER_ITEM,IS_PAUSE,IS_SRP, PROD_PKG_PLAN_CODE, APPEND_PREM_AF, IS_WAIVED, INTEREST_MODE, APPLY_CODE, 
				NORENEW_REASON, ORGAN_CODE, UPDATE_BY, CHARGE_YEAR, EXTRA_PREM_AF, POLICY_ID, INITIAL_AMOUNT, 
				CC_SA, INITIAL_EXTRA_PREM_AF, UPDATE_TIME, PAYOUT_RATE, AMOUNT, PAIDUP_DATE,
				PAY_YEAR, EXPIRY_DATE, PAY_PERIOD, LIABILITY_STATE, POLICY_CODE, COUNT_WAY, 
				IS_GIFT, RERINSTATE_DATE, ADDITIONAL_PREM_AF, RENEW_DECISION, VALIDATE_DATE, BONUS_MODE, UPDATE_TIMESTAMP, 
				INSERT_BY, BENEFIT_LEVEL, PRODUCT_ID, BONUS_SA, PRODUCT_CODE, APPLY_DATE, INTEREST_FLAG, 
				COVERAGE_PERIOD, ITEM_ID, RENEWAL_EXTRA_PREM_AF, INSERT_TIMESTAMP, WAIVER_END, MATURITY_DATE, BUSI_ITEM_ID, 
				HEALTH_SERVICE_FLAG, LAPSE_DATE, PAY_FREQ, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, 
				LAPSE_CAUSE, RENEWAL_DISCNTED_PREM_AF, TOTAL_PREM_AF, DECISION_CODE, PAUSE_DATE, STD_PREM_AF, CHARGE_PERIOD, 
				SUSPEND_DATE, SUSPEND_CAUSE, DEDUCTIBLE_FRANCHISE, WAIVER_START, PREM_FREQ, INITIAL_DISCNT_PREM_AF,BONUS_W_MODE,LAST_BONUS_DATE,ANNU_PAY_TYPE,IRREGULAR_PREM,TAX_EXTENSION_PREM_RATE,DISCOUNT_TYPE,DISCOUNT_RATE) 
			VALUES (
				#{is_master_item, jdbcType=NUMERIC},#{is_pause, jdbcType=NUMERIC},#{is_srp, jdbcType=NUMERIC},#{prod_pkg_plan_code, jdbcType=VARCHAR} , #{append_prem_af, jdbcType=NUMERIC} , #{is_waived, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} 
				, #{norenew_reason, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{initial_amount, jdbcType=NUMERIC} 
				, #{cc_sa, jdbcType=NUMERIC} , #{initial_extra_prem_af, jdbcType=NUMERIC} , SYSDATE , #{payout_rate, jdbcType=NUMERIC} , #{amount, jdbcType=NUMERIC} , #{paidup_date, jdbcType=DATE} 
				,#{pay_year, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{pay_period, jdbcType=VARCHAR} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{count_way, jdbcType=VARCHAR} 
				, #{is_gift, jdbcType=NUMERIC} , #{rerinstate_date, jdbcType=DATE} , #{additional_prem_af, jdbcType=NUMERIC} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{interest_flag, jdbcType=NUMERIC} 
				, #{coverage_period, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{renewal_extra_prem_af, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{health_service_flag, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} , #{pay_freq, jdbcType=VARCHAR} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} 
				, #{lapse_cause, jdbcType=VARCHAR} , #{renewal_discnted_prem_af, jdbcType=NUMERIC} , #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , #{std_prem_af, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} 
				, #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{deductible_franchise, jdbcType=NUMERIC} , #{waiver_start, jdbcType=DATE} , #{prem_freq, jdbcType=NUMERIC} , #{initial_discnt_prem_af, jdbcType=NUMERIC} , #{bonus_w_mode, jdbcType=NUMERIC},#{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR}, #{irregular_prem, jdbcType=NUMERIC}, #{tax_extension_prem_rate, jdbcType=NUMERIC}, #{discount_type, jdbcType=VARCHAR}, #{discount_rate, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteContractProduct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE  ITEM_ID = #{item_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateContractProductByItemId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			IS_MASTER_ITEM=#{is_master_item, jdbcType=NUMERIC} ,
			IRREGULAR_PREM=#{irregular_prem, jdbcType=NUMERIC},
			IS_PAUSE = #{is_pause, jdbcType=NUMERIC} ,	
			IS_SRP = #{is_srp, jdbcType=NUMERIC} ,		
			PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code, jdbcType=VARCHAR} ,
		    APPEND_PREM_AF = #{append_prem_af, jdbcType=NUMERIC} ,
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
		    INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			NORENEW_REASON = #{norenew_reason, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    EXTRA_PREM_AF = #{extra_prem_af, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    INITIAL_AMOUNT = #{initial_amount, jdbcType=NUMERIC} ,
		    CC_SA = #{cc_sa, jdbcType=NUMERIC} ,
		    INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAYOUT_RATE = #{payout_rate, jdbcType=NUMERIC} ,
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
			PAY_PERIOD = #{pay_period, jdbcType=VARCHAR} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			COUNT_WAY = #{count_way, jdbcType=VARCHAR} ,
		    IS_GIFT = #{is_gift, jdbcType=NUMERIC} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
		    ADDITIONAL_PREM_AF = #{additional_prem_af, jdbcType=NUMERIC} ,
		    RENEW_DECISION = #{renew_decision, jdbcType=NUMERIC} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    BONUS_MODE = #{bonus_mode, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			BENEFIT_LEVEL = #{benefit_level, jdbcType=VARCHAR} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INTEREST_FLAG = #{interest_flag, jdbcType=NUMERIC} ,
			COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af, jdbcType=NUMERIC} ,
		    WAIVER_END = #{waiver_end, jdbcType=DATE} ,
		    MATURITY_DATE = #{maturity_date, jdbcType=DATE} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    HEALTH_SERVICE_FLAG = #{health_service_flag, jdbcType=NUMERIC} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
			PAY_FREQ = #{pay_freq, jdbcType=VARCHAR} ,
		    UNIT = #{unit, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
		    RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af, jdbcType=NUMERIC} ,
		    TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		    PAUSE_DATE = #{pause_date, jdbcType=DATE} ,
		    STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    DEDUCTIBLE_FRANCHISE = #{deductible_franchise, jdbcType=NUMERIC} ,
		    WAIVER_START = #{waiver_start, jdbcType=DATE} ,
		    PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
		    INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af, jdbcType=NUMERIC} ,
		    BONUS_W_MODE = #{bonus_w_mode, jdbcType=NUMERIC} ,
		    LAST_BONUS_DATE = #{last_bonus_date, jdbcType=DATE} ,
		    annu_pay_type = #{annu_pay_type, jdbcType=VARCHAR} ,
		    
		    start_pay_age= #{start_pay_age, jdbcType=NUMERIC},
		    start_pay_date = #{start_pay_date, jdbcType=DATE},
		    TAX_EXTENSION_PREM_RATE = #{tax_extension_prem_rate, jdbcType=NUMERIC},
		    DISCOUNT_TYPE = #{discount_type, jdbcType=VARCHAR}, 
		    DISCOUNT_RATE = #{discount_rate, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>
	
	<!-- 修改操作 -->
	<update id="updateContractProductByItemIdFalg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			DISCOUNT_TYPE = #{discount_type, jdbcType=VARCHAR},
		    DISCOUNT_RATE = #{discount_rate, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>
	
	<!-- 更新豁免止期 -->
	<update id="updateContractProductWaiverEnd" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
			<trim suffixOverrides=",">
			   WAIVER_END = #{waiver_end, jdbcType=DATE} ,
			   IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>
	
	<!-- 更新豁免止期 -->
	<update id="updateContractProductIrregularPrem" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
			<trim suffixOverrides=",">
			   IRREGULAR_PREM = #{irregular_prem, jdbcType=NUMERIC}
			</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
    <!-- 按照险种查询信息 -->
    <select id="findContractProductByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.IS_PAUSE, A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductByBusiItemIdCondition" />
	</select>
    <!-- 按照险种查询信息 -->
	<select id="findContractProductByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM,A.ANNU_PAY_TYPE, A.IS_PAUSE, A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.START_PAY_DATE,A.START_PAY_AGE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="queryContractProductByItemIdCondition" />
	</select>
	
	<!-- Modify By Terence 2016-7-15 17:23:47  Fix TC defect 6791 and format SQL-->
	<select id="findContractProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.IS_MASTER_ITEM, A.IS_PAUSE,					      
					       A.PROD_PKG_PLAN_CODE,
					       A.APPEND_PREM_AF,
					       A.IS_WAIVED,
					       A.INTEREST_MODE,
					       A.APPLY_CODE,
					       A.NORENEW_REASON,
					       A.ORGAN_CODE,
					       A.CHARGE_YEAR,
					       A.EXTRA_PREM_AF,
					       A.POLICY_ID,
					       A.INITIAL_AMOUNT,
					       A.CC_SA,
					       A.INITIAL_EXTRA_PREM_AF,
					       A.PAYOUT_RATE,
					       A.AMOUNT,
					       A.PAIDUP_DATE,
					       A.PAY_YEAR,
					       A.EXPIRY_DATE,
					       A.PAY_PERIOD,
					       A.LIABILITY_STATE,
					       A.POLICY_CODE,
					       A.COUNT_WAY,
					       A.IS_GIFT,
					       A.RERINSTATE_DATE,
					       A.ADDITIONAL_PREM_AF,
					       A.RENEW_DECISION,
					       A.VALIDATE_DATE,
					       A.BONUS_MODE,
					       A.BENEFIT_LEVEL,
					       A.PRODUCT_ID,
					       A.BONUS_SA,
					       A.PRODUCT_CODE,
					       A.APPLY_DATE,
					       A.INTEREST_FLAG,
					       A.COVERAGE_PERIOD,
					       A.ITEM_ID,
					       A.RENEWAL_EXTRA_PREM_AF,
					       A.WAIVER_END,
					       A.MATURITY_DATE,
					       A.BUSI_ITEM_ID,
					       A.HEALTH_SERVICE_FLAG,
					       A.LAPSE_DATE,
					       A.PAY_FREQ,
					       A.UNIT,
					       A.COVERAGE_YEAR,
					       A.END_CAUSE,
					       A.LAPSE_CAUSE,
					       A.RENEWAL_DISCNTED_PREM_AF,
					       A.TOTAL_PREM_AF,
					       A.DECISION_CODE,
					       A.PAUSE_DATE,
					       A.STD_PREM_AF,
					       A.CHARGE_PERIOD,
					       A.SUSPEND_DATE,
					       A.SUSPEND_CAUSE,
					       A.DEDUCTIBLE_FRANCHISE,
					       A.WAIVER_START,
					       A.PREM_FREQ,
					       A.INITIAL_DISCNT_PREM_AF,
					       A.BONUS_W_MODE,
					       A.LAST_BONUS_DATE,
					       A.IRREGULAR_PREM,
					       A.ANNU_PAY_TYPE,
					       A.START_PAY_AGE,
					       A.START_PAY_DATE,
					       A.TAX_EXTENSION_PREM_RATE,
					       A.DISCOUNT_TYPE,
					       A.DISCOUNT_RATE
					  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE 1 = 1]]>
		<include refid="queryContractProductByPolicyCodeCondition" />
	</select>
	
	<select id="findContractProductByProductCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductByProductCodeCondition" />
	</select>
	
	<select id="findContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP,A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,  
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			( SELECT SUM(B.TRANS_AMOUNT) FROM APP___PAS__DBUSER.t_fund_group_trans B WHERE  B.trans_code='53'   AND  B.POLICY_ID = A.POLICY_ID  AND B.BUSI_ITEM_ID = A.BUSI_ITEM_ID )    initial_irregular_prem, /*首期不定期交保费*/
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="contractProductWhereCondition" />
	</select>
	<select id="findContractProductForPrint" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_PAY_AGE, A.START_PAY_DATE,A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP,A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,  
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			( SELECT SUM(B.TRANS_AMOUNT) FROM APP___PAS__DBUSER.t_fund_group_trans B WHERE  B.trans_code='53'   AND  B.POLICY_ID = A.POLICY_ID  AND B.BUSI_ITEM_ID = A.BUSI_ITEM_ID )    initial_irregular_prem, /*首期不定期交保费*/
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD,A.IRREGULAR_PREM, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A 
			WHERE 1 = 1 and A.LIABILITY_STATE in('1','4') ]]>
		<include refid="contractProductWhereCondition" />
	</select>
	
	<select id="findContractProductSpecial" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP,A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,  
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A ,APP___PAS__DBUSER.T_BUSINESS_PRODUCT  B, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C 
			WHERE B.PRODUCT_CATEGORY2 = '30004'
   			and C.BUSI_ITEM_ID = A.BUSI_ITEM_ID
   			and C.BUSI_PRD_ID = B.BUSINESS_PRD_ID  ]]>
		<include refid="contractProductWhereCondition" />
	</select>
<!-- 按map查询操作 -->
	<select id="findAllMapContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.START_PAY_AGE,A.START_PAY_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000 ]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ORDER BY A.VALIDATE_DATE]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="findAllAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.amount, b.product_category, b.product_category4
  from dev_pas.t_contract_busi_prod c,
       dev_pas.t_business_product   b,
       dev_pas.t_Contract_Product   a
 where  c.busi_prd_id = b.business_prd_id
   and c.BUSI_ITEM_ID = a.busi_item_id
   and c.Master_Busi_Item_Id is null]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ORDER BY A.VALIDATE_DATE]]>
	</select>
	
	
	
<!-- 查询所有操作 用于主附险排序 -->
	<select id="findAllContractProduct2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT distinct A.IS_MASTER_ITEM,  A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,B.Master_Busi_Item_Id FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
			 WHERE A.Busi_Item_Id=B.Busi_Item_Id  and A.POLICY_CODE=#{policy_code} ]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ORDER BY B.Master_Busi_Item_Id desc]]>
	</select>
<!-- 查询所有操作 根据责任组排序 -->
	<select id="findAllContractProduct3" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.START_PAY_AGE,A.START_PAY_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000 ]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ORDER BY A.Product_Code]]>
	</select>
<!-- 查询个数操作 -->
	<select id="findContractProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryContractProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,  B.ANNU_PAY_TYPE,  B.IS_MASTER_ITEM, B.IS_PAUSE,  B.PROD_PKG_PLAN_CODE, B.APPEND_PREM_AF, B.IS_WAIVED, B.INTEREST_MODE, B.APPLY_CODE, 
			B.NORENEW_REASON, B.ORGAN_CODE, B.CHARGE_YEAR, B.EXTRA_PREM_AF, B.POLICY_ID, B.INITIAL_AMOUNT, 
			B.CC_SA, B.INITIAL_EXTRA_PREM_AF, B.PAYOUT_RATE, B.AMOUNT, B.PAIDUP_DATE,  
			B.PAY_YEAR, B.EXPIRY_DATE, B.PAY_PERIOD, B.LIABILITY_STATE, B.POLICY_CODE, B.COUNT_WAY, 
			B.IS_GIFT, B.RERINSTATE_DATE, B.ADDITIONAL_PREM_AF, B.RENEW_DECISION, B.VALIDATE_DATE, B.BONUS_MODE, 
			B.BENEFIT_LEVEL, B.PRODUCT_ID, B.BONUS_SA, B.PRODUCT_CODE, B.APPLY_DATE, B.INTEREST_FLAG, 
			B.COVERAGE_PERIOD, B.ITEM_ID, B.RENEWAL_EXTRA_PREM_AF, B.WAIVER_END, B.MATURITY_DATE, B.BUSI_ITEM_ID, 
			B.HEALTH_SERVICE_FLAG, B.LAPSE_DATE, B.PAY_FREQ, B.UNIT, B.COVERAGE_YEAR, B.END_CAUSE, 
			B.LAPSE_CAUSE, B.RENEWAL_DISCNTED_PREM_AF, B.TOTAL_PREM_AF, B.DECISION_CODE, B.PAUSE_DATE, B.STD_PREM_AF, B.CHARGE_PERIOD, 
			B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.DEDUCTIBLE_FRANCHISE, B.WAIVER_START, B.PREM_FREQ, B.INITIAL_DISCNT_PREM_AF,B.DISCOUNT_TYPE,B.DISCOUNT_RATE FROM (
					SELECT ROWNUM RN,A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE,  A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findContractProductByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE,  A.IS_PAUSE,A.IS_SRP,A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.IRREGULAR_PREM,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.TAX_EXTENSION_PREM_RATE,A.DISCOUNT_TYPE,A.DISCOUNT_RATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductByPolicyIdCodeCondition" />
	</select>
	
	<insert id="addContractProductCreatPolicyPast"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="item_id">
			SELECT S_CONTRACT_PRODUCT__ITEM_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_PRODUCT(
				IS_MASTER_ITEM, A.INITIAL_AMOUNT,IS_PAUSE,IS_SRP, PROD_PKG_PLAN_CODE, APPEND_PREM_AF, IS_WAIVED, INTEREST_MODE, APPLY_CODE, 
				NORENEW_REASON, ORGAN_CODE, UPDATE_BY, CHARGE_YEAR, EXTRA_PREM_AF, POLICY_ID, CC_SA, 
				INITIAL_EXTRA_PREM_AF, UPDATE_TIME, PAYOUT_RATE, AMOUNT, PAIDUP_DATE,
				PAY_YEAR, EXPIRY_DATE, PAY_PERIOD, LIABILITY_STATE, POLICY_CODE, COUNT_WAY, IS_GIFT, 
				RERINSTATE_DATE, ADDITIONAL_PREM_AF, RENEW_DECISION, VALIDATE_DATE, BONUS_MODE, UPDATE_TIMESTAMP, INSERT_BY, 
				BENEFIT_LEVEL, PRODUCT_ID, BONUS_SA, PRODUCT_CODE, APPLY_DATE, COVERAGE_PERIOD, ITEM_ID, 
				RENEWAL_EXTRA_PREM_AF, INSERT_TIMESTAMP, WAIVER_END, MATURITY_DATE, BUSI_ITEM_ID, HEALTH_SERVICE_FLAG, LAPSE_DATE, 
				PAY_FREQ, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, LAPSE_CAUSE, RENEWAL_DISCNTED_PREM_AF, 
				TOTAL_PREM_AF, DECISION_CODE, PAUSE_DATE, STD_PREM_AF, CHARGE_PERIOD, SUSPEND_DATE, SUSPEND_CAUSE, 
				DEDUCTIBLE_FRANCHISE, WAIVER_START, PREM_FREQ, INITIAL_DISCNT_PREM_AF ,BONUS_W_MODE,LAST_BONUS_DATE ANNU_PAY_TYPE,IRREGULAR_PREM,TAX_EXTENSION_PREM_RATE,DISCOUNT_TYPE,DISCOUNT_RATE  ) 
			VALUES (
				 #{is_master_item, jdbcType=NUMERIC} , #{initial_amount, jdbcType=NUMERIC},#{is_pause, jdbcType=NUMERIC},#{is_srp, jdbcType=NUMERIC},#{prod_pkg_plan_code, jdbcType=VARCHAR} , #{append_prem_af, jdbcType=NUMERIC} , #{is_waived, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} 
				, #{norenew_reason, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{cc_sa, jdbcType=NUMERIC} 
				, #{initial_extra_prem_af, jdbcType=NUMERIC} , SYSDATE , #{payout_rate, jdbcType=NUMERIC} , #{amount, jdbcType=NUMERIC} , #{paidup_date, jdbcType=DATE}
				, #{pay_year, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{pay_period, jdbcType=VARCHAR} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{count_way, jdbcType=VARCHAR} , #{is_gift, jdbcType=NUMERIC} 
				, #{rerinstate_date, jdbcType=DATE} , #{additional_prem_af, jdbcType=NUMERIC} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{coverage_period, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} 
				, #{renewal_extra_prem_af, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{busi_item_id, jdbcType=NUMERIC} , #{health_service_flag, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} 
				, #{pay_freq, jdbcType=VARCHAR} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{lapse_cause, jdbcType=VARCHAR} , #{renewal_discnted_prem_af, jdbcType=NUMERIC} 
				, #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , #{std_prem_af, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} 
				, #{deductible_franchise, jdbcType=NUMERIC} , #{waiver_start, jdbcType=DATE} , #{prem_freq, jdbcType=NUMERIC} , #{initial_discnt_prem_af, jdbcType=NUMERIC} , #{bonus_w_mode, jdbcType=NUMERIC},#{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR},#{irregular_prem, jdbcType=NUMERIC},#{tax_extension_prem_rate, jdbcType=NUMERIC}, #{discount_type, jdbcType=VARCHAR}, #{discount_rate, jdbcType=NUMERIC}) 
		 ]]>
	</insert>
	<!-- 尊享人生险种可选责任信息 -->
	<select id="querySelectMessage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select t.item_id,
       t.annu_pay_type,
       t.busi_prod_code,
       t.product_id,
       t.policy_code,
       t.charge_year,
       t.BUSI_ITEM_ID,
       t.OLD_POL_NO,
       t.product_name,
       t.duty_type,
       t.contract_get_date,
       t.amount,
       t.std_prem_af,
       (select BONUS_SA
          from APP___PAS__DBUSER.T_BONUS_ALLOCATE c1
         where c1.bonus_allot = '1'
           and t.allocate_id = c1.allocate_id
           and to_char(sysdate ,'yyyy') = to_char(c1.allocate_date, 'yyyy')) bonus_amnt,
       (select TERMINAL_BONUS
          from APP___PAS__DBUSER.T_BONUS_ALLOCATE c1
         where c1.bonus_allot = '1'
           and t.allocate_id = c1.allocate_id
           and to_char(sysdate ,'yyyy') = to_char(c1.allocate_date, 'yyyy')) terminal_bonus_amnt,
       (select sum(BONUS_SA)
          from APP___PAS__DBUSER.T_BONUS_ALLOCATE c1
         where c1.bonus_allot in ('1', '2', '6')
           and t.item_id = c1.item_id) sum_bonus_amnt,
           
           (select TERMINAL_BONUS_RATE
          from APP___PAS__DBUSER.T_BONUS_ALLOCATE c1
         where t.allocate_id = c1.allocate_id
           and to_char(sysdate ,'yyyy') = to_char(c1.allocate_date, 'yyyy')) TERMINAL_BONUS_RATE,
           (select VALID_AMOUNT
          from APP___PAS__DBUSER.T_BONUS_ALLOCATE c1
         where t.allocate_id = c1.allocate_id
           and to_char(sysdate ,'yyyy') = to_char(c1.allocate_date, 'yyyy')) VALID_AMOUNT
        from (
select c.allocate_id,
       a.item_id,
       a.annu_pay_type,
       b.busi_prod_code,
       a.product_id,
       a.policy_code,
       a.charge_year,
       A.BUSI_ITEM_ID,
       B.OLD_POL_NO,
       (select product_name_sys
          from APP___PAS__DBUSER.T_BUSINESS_PRODUCT
         where business_prd_id = b.busi_prd_id) as product_name,
       a.product_id as duty_type,
       (select BEGIN_DATE
          from APP___PAS__DBUSER.T_PAY_PLAN
         where a.item_id = item_id
           and rownum = 1) as contract_get_date,
       a.amount as amount,
       a.std_prem_af as std_prem_af,
       c.BONUS_SA,
       c.bonus_allot,
       c.TERMINAL_BONUS,
       c.TERMINAL_BONUS_RATE,
       c.VALID_AMOUNT,
       c.allocate_date
  from APP___PAS__DBUSER.T_CONTRACT_PRODUCT   a,
       APP___PAS__DBUSER.t_contract_busi_prod b,
       APP___PAS__DBUSER.T_BONUS_ALLOCATE c
 where b.busi_item_id = a.busi_item_id
   and c.item_id = a.item_id
   and a.policy_code =  #{policy_code}
   and b.busi_prod_code = #{busi_prod_code}
   and a.IS_MASTER_ITEM = 0
 order by a.product_code) t
 where to_char(sysdate ,'yyyy') = to_char(t.allocate_date, 'yyyy')
		]]>
	</select>
	<!-- 尊享人生险种可选责任信息 -->
	<select id="querySelectProductMessage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select a.item_id , a.annu_pay_type, b.busi_prod_code,a.product_id, a.policy_code, a.charge_year, A.BUSI_ITEM_ID ,B.OLD_POL_NO,
		       (select product_name_sys
		          from APP___PAS__DBUSER.T_BUSINESS_PRODUCT
		         where business_prd_id = b.busi_prd_id) as product_name,
		       a.product_id as duty_type,
		       (select BEGIN_DATE
		          from APP___PAS__DBUSER.T_PAY_PLAN
		         where a.item_id = item_id
		           and rownum = 1) as contract_get_date,
		       a.amount as amount,
		       a.std_prem_af as std_prem_af
		  from APP___PAS__DBUSER.T_CONTRACT_PRODUCT   a,
		       APP___PAS__DBUSER.t_contract_busi_prod b
		 where b.busi_item_id = a.busi_item_id
		       and a.policy_code=#{policy_code} and b.busi_prod_code=#{busi_prod_code} and a.IS_MASTER_ITEM=0
		       order by a.product_code
		]]>
	</select>
	
	<insert id="addContractProductCreatPolicy"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="item_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_PRODUCT__ITEM_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_PRODUCT(
				IS_MASTER_ITEM,INITIAL_AMOUNT,IS_PAUSE,IS_SRP, PROD_PKG_PLAN_CODE, APPEND_PREM_AF, IS_WAIVED, INTEREST_MODE, APPLY_CODE, 
				NORENEW_REASON, ORGAN_CODE, UPDATE_BY, CHARGE_YEAR, EXTRA_PREM_AF, POLICY_ID, CC_SA, 
				INITIAL_EXTRA_PREM_AF, UPDATE_TIME, PAYOUT_RATE, AMOUNT, PAIDUP_DATE,
				PAY_YEAR, EXPIRY_DATE, PAY_PERIOD, LIABILITY_STATE, POLICY_CODE, COUNT_WAY, IS_GIFT, 
				RERINSTATE_DATE, ADDITIONAL_PREM_AF, RENEW_DECISION, VALIDATE_DATE, BONUS_MODE, UPDATE_TIMESTAMP, INSERT_BY, 
				BENEFIT_LEVEL, PRODUCT_ID, BONUS_SA, PRODUCT_CODE, APPLY_DATE, COVERAGE_PERIOD, ITEM_ID, 
				RENEWAL_EXTRA_PREM_AF, INSERT_TIMESTAMP, WAIVER_END, MATURITY_DATE, BUSI_ITEM_ID, HEALTH_SERVICE_FLAG, LAPSE_DATE, 
				PAY_FREQ, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, LAPSE_CAUSE, RENEWAL_DISCNTED_PREM_AF, 
				TOTAL_PREM_AF, DECISION_CODE, PAUSE_DATE, STD_PREM_AF, CHARGE_PERIOD, SUSPEND_DATE, SUSPEND_CAUSE, 
				DEDUCTIBLE_FRANCHISE, WAIVER_START, PREM_FREQ, INITIAL_DISCNT_PREM_AF ,BONUS_W_MODE,LAST_BONUS_DATE,ANNU_PAY_TYPE,IRREGULAR_PREM,START_PAY_AGE,START_PAY_DATE ,TAX_EXTENSION_PREM_RATE,DISCOUNT_TYPE,DISCOUNT_RATE ) 
			VALUES (
				  #{is_master_item, jdbcType=NUMERIC} ,#{initial_amount, jdbcType=NUMERIC},#{is_pause, jdbcType=NUMERIC},#{is_srp, jdbcType=NUMERIC}, #{prod_pkg_plan_code, jdbcType=VARCHAR} , #{append_prem_af, jdbcType=NUMERIC} , #{is_waived, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} 
				, #{norenew_reason, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{cc_sa, jdbcType=NUMERIC} 
				, #{initial_extra_prem_af, jdbcType=NUMERIC} , SYSDATE , #{payout_rate, jdbcType=NUMERIC} , #{amount, jdbcType=NUMERIC} , #{paidup_date, jdbcType=DATE} 
				, #{pay_year, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{pay_period, jdbcType=VARCHAR} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{count_way, jdbcType=VARCHAR} , #{is_gift, jdbcType=NUMERIC} 
				, #{rerinstate_date, jdbcType=DATE} , #{additional_prem_af, jdbcType=NUMERIC} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{coverage_period, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} 
				, #{renewal_extra_prem_af, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{busi_item_id, jdbcType=NUMERIC} , #{health_service_flag, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} 
				, #{pay_freq, jdbcType=VARCHAR} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{lapse_cause, jdbcType=VARCHAR} , #{renewal_discnted_prem_af, jdbcType=NUMERIC} 
				, #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , #{std_prem_af, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} 
				, #{deductible_franchise, jdbcType=NUMERIC} , #{waiver_start, jdbcType=DATE} , #{prem_freq, jdbcType=NUMERIC} , #{initial_discnt_prem_af, jdbcType=NUMERIC} , #{bonus_w_mode, jdbcType=NUMERIC},#{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR},#{irregular_prem, jdbcType=NUMERIC}, #{start_pay_age, jdbcType=NUMERIC}, #{start_pay_date, jdbcType=DATE}, #{tax_extension_prem_rate, jdbcType=NUMERIC}, #{discount_type, jdbcType=VARCHAR}, #{discount_rate, jdbcType=NUMERIC}) 
		 ]]>
	</insert>
	
	<!-- 第k个保单年度末累计可选责任的基本保额 -->
	  <select id="calcAddItemSumAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(SUM(T.AMOUNT), 0) SUM_AMOUNT
  					FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT T
				  WHERE T.BUSI_ITEM_ID = #{busi_item_id} AND T.IS_MASTER_ITEM=0  ]]>
	</select>
	<!--   获取保单主险信息(提供给外围系统) -->
	<select id="findRisktByPolicyCodeOrPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select (select PRODUCT_CODE_STD
			          from APP___PAS__DBUSER.T_BUSINESS_PRODUCT t
			         where t.BUSINESS_PRD_ID = bu.busi_prd_id) as PRODUCT_CODE,
			       (select PRODUCT_NAME_STD
			          from APP___PAS__DBUSER.T_BUSINESS_PRODUCT t
			         where t.BUSINESS_PRD_ID = bu.busi_prd_id) as PRODUCT_NAME,
			       pr.TOTAL_PREM_AF,
			       pr.AMOUNT,
			       pr.CHARGE_YEAR,
			       pr.PREM_FREQ,
			       pr.TOTAL_PREM_AF,
			       bu.policy_id
			  from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD bu
			  left join APP___PAS__DBUSER.T_CONTRACT_PRODUCT pr
			    on bu.busi_item_id = pr.busi_item_id
			   and bu.policy_id = pr.policy_id
			 where bu.master_busi_item_id is null
			   and bu.policy_code = #{policy_code} ]]>
		<if test=" policy_id  != null and policy_id !='' "><![CDATA[ and bu.policy_id = #{policy_id} ]]></if>
	</select>
	<!-- 查询某被保人下的责任组信息 -->
	<select id="findAccumulateAmountInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.INITIAL_AMOUNT,A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, A.IS_GIFT, 
			A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, 
			A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, 
			A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.IRREGULAR_PREM,
			E.BUSI_PROD_CODE AS PRODUCT_CODE ,A.BONUS_W_MODE,A.LAST_BONUS_DATE 
			FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A , APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD E WHERE ROWNUM <=  1000  AND E.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND E.POLICY_ID=A.POLICY_ID  
			AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER D WHERE D.POLICY_ID=A.POLICY_ID AND D.LIABILITY_STATE=#{policy_liability_state})]]>
		 <include refid="contractProductWhereCondition" />
		 <![CDATA[ ]]>
		 <if test=" customer_id  != null or customer_id !='' ">
			 <![CDATA[ AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_BENEFIT_INSURED B WHERE B.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND 
				    EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_INSURED_LIST C WHERE C.LIST_ID=B.INSURED_ID AND C.CUSTOMER_ID=#{customer_id}))]]>
		</if>
	</select>
	
	
	
	<!-- 再保险受益人查询接口 -->
	<select id="findPremByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select sum(t.total_prem_af) as prem , sum(t.amount) as amount from  ( select cp.* from APP___PAS__DBUSER.T_contract_product cp where cp.policy_code =#{policy_code}) t ,APP___PAS__DBUSER.T_contract_bene cb where 
  
    cb.policy_code = t.policy_code]]>
	</select>
	
	<!-- 可选责任信息查询列表 -->
	<select id="queryOptionLiabInfoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT PP.LIAB_ID,
                    pp.Liab_Name,
                    pp.BEGIN_DATE,
					P.PRODUCT_NAME,
					CP.VALIDATE_DATE,
					CP.AMOUNT,
					CP.TOTAL_PREM_AF
					,PP.BEGIN_DATE
					,PP.PLAN_FREQ 
					,(SELECT T.PAY_NAME FROM APP___PAS__DBUSER.T_PAY_TYPE T WHERE T.PAY_TYPE = PP.PLAN_FREQ AND ROWNUM=1) PLAN_FREQ_NAME 
					,PP.PAY_PERIOD
					,PP.PAY_YEAR,
          FLOOR(MONTHS_BETWEEN(pp.BEGIN_DATE,a.CUSTOMER_BIRTHDAY)/12) age
				FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT CP,
        APP___PAS__DBUSER.T_PAY_PLAN PP,
        APP___PAS__DBUSER.T_PRODUCT_LIFE P,
        APP___PAS__DBUSER.T_PAY_PLAN_PAYEE TPPP,
        APP___PAS__DBUSER.T_CUSTOMER a
				WHERE CP.POLICY_ID = PP.POLICY_ID and p.product_id=cp.product_id and TPPP.PLAN_ID=pp.PLAN_ID and a.CUSTOMER_ID = TPPP.CUSTOMER_ID
				AND CP.POLICY_CODE = #{policy_code} AND CP.IS_MASTER_ITEM = '0'
		]]>
	</select>
	
	<!-- 查询保单主险的交费方式 -->
	<select id="PA_findMainChargePeriodByCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[   SELECT TCP.CHARGE_PERIOD , TCP.PREM_FREQ
				  FROM DEV_PAS.T_CONTRACT_MASTER TCM
				 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				    ON TCM.POLICY_CODE = TCBP.POLICY_CODE
				   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
				 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
				    ON TCP.POLICY_CODE = TCM.POLICY_CODE
				   AND TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				   AND TCM.POLICY_CODE = #{policy_code}
				   AND ROWNUM = 1
	]]>
	</select>
	
	<!-- 保单险种信息查询接口 -->
	<select id="queryPolicyRiskInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT 
				      CP.CHARGE_YEAR,
				      CE.POLICY_PERIOD,
				      CE.PAY_DUE_DATE
				FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT CP,
				APP___PAS__DBUSER.T_CONTRACT_EXTEND CE
				WHERE CP.POLICY_ID = CE.POLICY_ID
				AND CP.POLICY_ID = #{policy_id} and CP.BUSI_ITEM_ID = #{busi_item_id} AND ROWNUM=1
		]]>
	</select>
	<!-- 查询附加险的费用 -->
	<select id="findContractProductGetProCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				select tc.total_prem_af,tc.product_code from dev_pas.t_contract_product tc where tc.busi_item_id in (
select busi_item_id from dev_pas.T_CONTRACT_BUSI_PROD t where t.master_busi_item_id = #{busi_item_id})
		]]>
	</select>
	
	<select id="findContractProductGetProCode_cjk" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[ 
SELECT SUM(Stand_Amount) as amount FROM APP___PAS__DBUSER.t_Bonus_Allocate A WHERE 1=1 AND A.POLICY_CODE=#{policy_code}
	]]>
	</select>
	
    <!-- 更新追加保费到T_CONTRACT_PRODUCT表  add by zhouly-->
	<update id="updateContractProductByPolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
				APPEND_PREM_AF = #{append_prem_af, jdbcType=NUMERIC},
				TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC}
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<if test=" policy_id  != null "><![CDATA[ AND POLICY_ID = #{policy_id} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND PRODUCT_CODE = #{product_code} ]]></if>
	</update>	
	
	<!-- 查询附加险的费用 -->
	<select id="SumConProductTotalPremByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				select sum(a.std_prem_af) as std_prem_af from APP___PAS__DBUSER.t_contract_product a where a.policy_id = #{policy_id}
		]]>
	</select>
	
	<!--by zhaoyoan_wb 通过险种编码和保单号查询附加险险种信息 -->
	<select id="PA_queryRiderRiskInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.AMOUNT,A.UNIT,A.COVERAGE_YEAR,A.PREM_FREQ,A.CHARGE_YEAR,
			(SELECT FIELD1 FROM DEV_PAS.T_CONTRACT_PRODUCT_OTHER WHERE ITEM_ID=A.ITEM_ID) standbyflag1
			FROM DEV_PAS.T_CONTRACT_PRODUCT A
			WHERE A.POLICY_CODE=#{policy_code} 
			AND EXISTS(SELECT 1 FROM DEV_PAS.T_CONTRACT_BUSI_PROD WHERE A.POLICY_ID=POLICY_ID AND BUSI_PROD_CODE=#{busi_prod_code}
			AND A.BUSI_ITEM_ID=BUSI_ITEM_ID AND MASTER_BUSI_ITEM_ID IS NOT NULL)
		]]>
	</select>
	
	
	<select id="PA_queryEndDateByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			
			SELECT  distinct  A.MATURITY_DATE FROM  APP___PAS__DBUSER.t_contract_product  A
			  where A.policy_id = #{policy_id}
		]]>
		
		<if test=" start_date  != null "><![CDATA[ AND A.maturity_date >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.maturity_date <= #{end_date} ]]></if>
	</select>
	
	<update id="updateBonusSa" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
		    BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
		    LAST_BONUS_DATE = #{last_bonus_date, jdbcType=DATE},
		    STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC},
		    TOTAL_PREM_AF =  #{total_prem_af, jdbcType=NUMERIC},
		    AMOUNT =  #{amount, jdbcType=NUMERIC},
		    INITIAL_DISCNT_PREM_AF = #{initialDiscntPremAf, jdbcType=NUMERIC},
		    RENEWAL_DISCNTED_PREM_AF = #{renewalDiscntedPremAf, jdbcType=NUMERIC},
		    INITIAL_EXTRA_PREM_AF = #{initialExtraPremAf, jdbcType=NUMERIC},
		    RENEWAL_EXTRA_PREM_AF = #{renewalExtraPremAf, jdbcType=NUMERIC},
		    PAY_YEAR  = #{pay_year, jdbcType=NUMERIC},
		    COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR},
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC},
		    START_PAY_DATE = #{start_pay_date, jdbcType=DATE},
		    START_PAY_AGE= #{start_pay_age, jdbcType=NUMERIC},
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
   		    DISCOUNT_TYPE = #{discount_type, jdbcType=VARCHAR}, 
		    DISCOUNT_RATE = #{discount_rate, jdbcType=NUMERIC},
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE},
		    MATURITY_DATE = #{maturity_date, jdbcType=DATE} ,
 			EXTRA_PREM_AF =#{extra_prem_af,jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>
	
	<!-- 险种责任接口 -->
	<select id="PA_queryBusiItemDuty" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT   BUSI_ITEM_ID,PRODUCT_CODE,POLICY_CODE,UNIT,STD_PREM_AF,TOTAL_PREM_AF,
		   AMOUNT,PREM_FREQ,COVERAGE_YEAR,CHARGE_PERIOD,CHARGE_YEAR,PAY_PERIOD,
		   PAY_YEAR,EXPIRY_DATE,VALIDATE_DATE,PAY_FREQ,INSERT_TIME,UPDATE_TIME FROM 
		   APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE POLICY_CODE=#{policy_code} AND BUSI_ITEM_ID=#{busi_item_id}
		]]>
	</select>
	<!--查询首期保费  -->
	<select id="PA_queryFirstfee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT A.INITIAL_DISCNT_PREM_AF, A.INITIAL_EXTRA_PREM_AF
			   FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A
			  WHERE 1 = 1 
		]]>
		<include refid="contractProductWhereCondition" />
	</select>
	<select id="PA_findChannelType10Prem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT NVL(SUM(B.TOTAL_PREM_AF),0) AS TOTAL_PREM_AF
  			FROM DEV_PAS.T_CONTRACT_PRODUCT B
 			WHERE B.POLICY_CODE IN
       		(SELECT A.POLICY_CODE
          	FROM DEV_PAS.T_CONTRACT_MASTER A
         	START WITH FORMER_ID = #{policy_id}
        	CONNECT BY PRIOR POLICY_ID = FORMER_ID) 
		]]>
	</select>
	
	<select id="PA_findContractProductbyCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		    
     select tcm.liability_state libstate from dev_pas.t_contract_master tcm ,dev_pas.t_policy_holder tph,dev_pas.t_customer tcu
	 where tcm.policy_code = tph.policy_code
	 and tcu.customer_id = tph.customer_id
	 and tcu.customer_name = #{customer_name}
	 and tcu.customer_birthday = TO_DATE(#{customer_birthday},'yyyy-MM-dd')
	 and tcu.customer_cert_type = #{customer_cert_type}
	 and tcu.customer_certi_code = #{customer_certi_code}
	 and tcu.customer_gender = #{customer_gender}
	 union all
	 select tcm.liability_state libstate from dev_pas.t_contract_master tcm ,dev_pas.t_insured_list til,dev_pas.t_customer tcu
	 where tcm.policy_code = til.policy_code
	 and tcu.customer_id = til.customer_id
	 and tcu.customer_name = #{customer_name}
	 and tcu.customer_birthday = TO_DATE(#{customer_birthday},'yyyy-MM-dd') 
	 and tcu.customer_cert_type = #{customer_cert_type}
	 and tcu.customer_certi_code = #{customer_certi_code} 
	 and tcu.customer_gender = #{customer_gender}
		
		]]>
	</select>
	
	<!-- 查询907 909对应的责任组信息 -->
	<select id="findAllContractProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
    select A.IS_PAUSE, A.PAUSE_DATE, A.PRODUCT_CODE, A.POLICY_CODE
  from DEV_PAS.T_CONTRACT_PRODUCT A
 where A.POLICY_CODE =#{policy_code}
   and A.PRODUCT_CODE IN ('909000', '907000')
	]]>
	</select>
	
	
	
	<!-- 30660  修改保单险种状态T_CONTRACT_PRODUCT表  add by liudx-->
	<update id="updateContractProductLiabilityStateByPolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
		    END_CAUSE = #{end_cause, jdbcType=VARCHAR} 
		</trim>    
		</set>
		<![CDATA[ WHERE 1=1 
		AND POLICY_ID = #{policy_id}
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND POLICY_CODE = #{policy_code} ]]></if>
	</update>	
	
	<!-- 添加操作 -->
	<insert id="addContractProductNS"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_PRODUCT(
				IS_MASTER_ITEM,IS_PAUSE,IS_SRP, PROD_PKG_PLAN_CODE, APPEND_PREM_AF, IS_WAIVED, INTEREST_MODE, APPLY_CODE, 
				NORENEW_REASON, ORGAN_CODE, UPDATE_BY, CHARGE_YEAR, EXTRA_PREM_AF, POLICY_ID, INITIAL_AMOUNT, 
				CC_SA, INITIAL_EXTRA_PREM_AF, UPDATE_TIME, PAYOUT_RATE, AMOUNT, PAIDUP_DATE,
				PAY_YEAR, EXPIRY_DATE, PAY_PERIOD, LIABILITY_STATE, POLICY_CODE, COUNT_WAY, 
				IS_GIFT, RERINSTATE_DATE, ADDITIONAL_PREM_AF, RENEW_DECISION, VALIDATE_DATE, BONUS_MODE, UPDATE_TIMESTAMP, 
				INSERT_BY, BENEFIT_LEVEL, PRODUCT_ID, BONUS_SA, PRODUCT_CODE, APPLY_DATE, INTEREST_FLAG, 
				COVERAGE_PERIOD, ITEM_ID, RENEWAL_EXTRA_PREM_AF, INSERT_TIMESTAMP, WAIVER_END, MATURITY_DATE, BUSI_ITEM_ID, 
				HEALTH_SERVICE_FLAG, LAPSE_DATE, PAY_FREQ, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, 
				LAPSE_CAUSE, RENEWAL_DISCNTED_PREM_AF, TOTAL_PREM_AF, DECISION_CODE, PAUSE_DATE, STD_PREM_AF, CHARGE_PERIOD, 
				SUSPEND_DATE, SUSPEND_CAUSE, DEDUCTIBLE_FRANCHISE, WAIVER_START, PREM_FREQ, INITIAL_DISCNT_PREM_AF,BONUS_W_MODE,LAST_BONUS_DATE,ANNU_PAY_TYPE,IRREGULAR_PREM ,TAX_EXTENSION_PREM_RATE,DISCOUNT_TYPE,DISCOUNT_RATE ) 
			VALUES (
				#{is_master_item, jdbcType=NUMERIC},#{is_pause, jdbcType=NUMERIC},#{is_srp, jdbcType=NUMERIC},#{prod_pkg_plan_code, jdbcType=VARCHAR} , #{append_prem_af, jdbcType=NUMERIC} , #{is_waived, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} 
				, #{norenew_reason, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{initial_amount, jdbcType=NUMERIC} 
				, #{cc_sa, jdbcType=NUMERIC} , #{initial_extra_prem_af, jdbcType=NUMERIC} , SYSDATE , #{payout_rate, jdbcType=NUMERIC} , #{amount, jdbcType=NUMERIC} , #{paidup_date, jdbcType=DATE} 
				,#{pay_year, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{pay_period, jdbcType=VARCHAR} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{count_way, jdbcType=VARCHAR} 
				, #{is_gift, jdbcType=NUMERIC} , #{rerinstate_date, jdbcType=DATE} , #{additional_prem_af, jdbcType=NUMERIC} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{interest_flag, jdbcType=NUMERIC} 
				, #{coverage_period, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{renewal_extra_prem_af, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{health_service_flag, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} , #{pay_freq, jdbcType=VARCHAR} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} 
				, #{lapse_cause, jdbcType=VARCHAR} , #{renewal_discnted_prem_af, jdbcType=NUMERIC} , #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , #{std_prem_af, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} 
				, #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{deductible_franchise, jdbcType=NUMERIC} , #{waiver_start, jdbcType=DATE} , #{prem_freq, jdbcType=NUMERIC} , #{initial_discnt_prem_af, jdbcType=NUMERIC} , #{bonus_w_mode, jdbcType=NUMERIC},#{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR},#{irregular_prem, jdbcType=NUMERIC},#{tax_extension_prem_rate, jdbcType=NUMERIC}, #{discount_type, jdbcType=VARCHAR}, #{discount_rate, jdbcType=NUMERIC} ) 
		]]>
	</insert>
	<select id="PA_findAllContractProductByPolicyCodePrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT nvl(SUM(A.TOTAL_PREM_AF),0) AS TOTAL_PREM_AF FROM DEV_PAS.T_CONTRACT_PRODUCT A WHERE  1 = 1  
		]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>		
	<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>	
	<if test=" busi_item_id != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ]]></if>	
	<if test=" busi_item_id_list != null and busi_item_id_list.size()!=0 ">
			<![CDATA[ AND A.BUSI_ITEM_ID in (]]>
			<foreach collection="busi_item_id_list" item="busi_item_id"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{busi_item_id} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</select>
	
	<select id="PA_findIsPauseByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 1 FROM DEV_PAS.T_CONTRACT_PRODUCT C WHERE C.IS_PAUSE = '1' AND C.POLICY_CODE = #{policy_code} ]]>
	</select>
	
	<select id="queryNSData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select ta.organ_code, 
			   ta.validate_date,		      
		       tb.expiry_date,
		       tc.pay_freq,
		       tc.MATURITY_DATE,
		       te.customer_birthday
		  from APP___PAS__DBUSER.t_contract_master ta
		 inner join dev_pas.t_contract_busi_prod tb
		    on ta.policy_id = tb.policy_id
		 inner join APP___PAS__DBUSER.t_contract_product tc
		    on tc.busi_item_id = tb.busi_item_id
		 inner join APP___PAS__DBUSER.t_policy_holder td
		    on td.policy_id = ta.policy_id
		 inner join APP___PAS__DBUSER.t_customer te
		    on te.customer_id = td.customer_id
		 where tb.master_busi_item_id is null
		   
	]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TA.POLICY_CODE = #{policy_code} ]]></if>	
	<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND TA.policy_id = #{policy_id} ]]></if>
	<if test=" busi_item_id  != null "><![CDATA[ AND TB.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	<if test=" is_master_item  != null "><![CDATA[ AND tc.is_master_item = #{is_master_item} ]]></if>
	</select>
	
	<!-- 查询中止状态信息 -->
	<select id="PA_findAbortStatusInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			SELECT A.ITEM_ID,
			       A.PRODUCT_ID,
			       A.IS_MASTER_ITEM,
			       A.POLICY_CODE,
			       A.POLICY_ID,
			       A.WAIVER_START
			  FROM DEV_PAS.T_CONTRACT_PRODUCT A
			 WHERE A.BUSI_ITEM_ID = #{busi_item_id}
			   AND A.POLICY_ID = #{policy_id}
			   AND A.WAIVER_START IS NOT NULL
	]]>
	</select>
	
	<!-- 查询保单险种保费信息 -->
	<select id="PA_queryBaofei" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
       SELECT 
       TCP.CHARGE_YEAR*TCP.TOTAL_PREM_AF CONTRACTPREMIUM,
       TCP.CHARGE_PERIOD
       FROM DEV_PAS.T_CONTRACT_MASTER TCMM,
            DEV_PAS.T_CONTRACT_PRODUCT TCP
       WHERE 1=1
       AND TCMM.POLICY_CODE = TCP.POLICY_CODE
       AND TCMM.SUBMIT_CHANNEL = '1'
       AND (TCMM.END_CAUSE != '80' OR TCMM.END_CAUSE IS NULL)
       AND TCMM.POLICY_CODE = #{policy_code}
	]]>
	</select>
		<!-- 查询险种交费年期 -->
	<select id="PA_queryMasterChargeYear" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			SELECT B.CHARGE_YEAR
            FROM DEV_PAS.T_CONTRACT_MASTER A, DEV_PAS.T_CONTRACT_PRODUCT B
           WHERE A.POLICY_CODE = B.POLICY_CODE
             /*AND B.IS_MASTER_ITEM IS NULL*/
             AND (A.END_CAUSE != '80' OR A.END_CAUSE IS NULL)
             AND B.IS_WAIVED = '1'
             AND A.POLICY_CODE = #{policy_code}
             AND ROWNUM = 1
	]]>
	</select>
	<select id="PA_queryPreWaiver" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CASE WHEN T.IS_WAIVED = 1 THEN 1  ELSE 0  END AS IS_WAIVED 
		 	FROM  APP___PAS__DBUSER.T_CONTRACT_PRODUCT T 
		 	WHERE T.POLICY_CODE =#{policy_code}
		]]>
	</select>
	
	<!-- 查询所有操作 根据险种号 byzhangjh1_wb -->
	<select id="findAllContractProductByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,
			A.LAST_BONUS_DATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A , APP___PAS__DBUSER.T_CONTRACT_PRODUCT B WHERE ROWNUM <=  1000 
      		AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND B.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>		
		<![CDATA[ORDER BY A.VALIDATE_DATE]]>
	</select>
		
	<select id="PA_queryBusiInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.ITEM_ID,B.IS_MASTER_ITEM,B.BUSI_ITEM_ID,
			A.MASTER_BUSI_ITEM_ID,A.POLICY_CODE,A.BUSI_PROD_CODE
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
			ON A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			WHERE A.POLICY_CODE = #{policy_code}
		]]>
		<if test="busi_prod_code  != null and busi_prod_code.size()!=0 ">
			<![CDATA[ AND A.BUSI_PROD_CODE in (]]>
			<foreach collection="busi_prod_code" item="busi_prod_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</select>
	
	<!-- 根据保单号查询主险保障期间 -->
	<select id="PA_findContractProductMain" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT B.COVERAGE_YEAR 
					  FROM DEV_PAS.T_CONTRACT_BUSI_PROD A, DEV_PAS.T_CONTRACT_PRODUCT B 
					 WHERE 1 = 1 
					   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID 
					   AND A.MASTER_BUSI_ITEM_ID IS NULL 
					   AND B.IS_MASTER_ITEM = 1 
					   AND B.POLICY_CODE = #{policy_code} 
		]]>
	</select>
	
	<!-- 查询保单险种责任组表累积保费 -->
	<select id="PA_findAllContractProductByCustomerID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT distinct TCP.POLICY_ID,
					                TCP.POLICY_CODE,
					                TCP.PREM_FREQ,
					                TCBP.MASTER_BUSI_ITEM_ID,
					                TCP.STD_PREM_AF
					  FROM APP___PAS__DBUSER.T_POLICY_HOLDER      PH,
					       APP___PAS__DBUSER.T_CONTRACT_MASTER    CM,
					       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
					       DEV_PAS.T_CONTRACT_PRODUCT             TCP
					 WHERE 1 = 1
					   AND CM.POLICY_CODE = PH.POLICY_CODE
					   AND CM.POLICY_CODE = TCBP.POLICY_CODE
					   AND TCP.POLICY_CODE = TCBP.POLICY_CODE
					   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
					   AND CM.LIABILITY_STATE IN ('1', '4')
					   AND NOT EXISTS
					 (SELECT 1
                    FROM DEV_PAS.T_CS_ACCEPT_CHANGE      TCAC,
                         DEV_PAS.T_CS_CONTRACT_BUSI_PROD TCCBP
                   WHERE TCAC.CHANGE_ID = TCCBP.CHANGE_ID
                     AND TCAC.SERVICE_CODE = 'NS'
                     AND TCAC.ACCEPT_STATUS = '18'
                     AND TCCBP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                     AND TCCBP.POLICY_ID = TCBP.POLICY_ID
                     and TCCBP.OLD_NEW = 1
                     and TCCBP.OPERATION_TYPE = 1)
 
		]]>
		<if test=" customerid != null and customerid != ''  "><![CDATA[ AND PH.CUSTOMER_ID = #{customerid} ]]></if>
		<if test=" begindate != null and begindate != ''  "><![CDATA[ AND CM.APPLY_DATE <=  #{begindate, jdbcType=DATE} ]]></if>
		<if test=" enddate  != null and enddate != ''  "><![CDATA[ AND CM.APPLY_DATE >= #{enddate, jdbcType=DATE} ]]></if>		
	</select>
	
	<!-- 查询第一被保人名下保单险种责任组表累积保费 -->
	<select id="PA_findAllContractProductByInsuredCustomerID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT distinct TCP.POLICY_ID,
                          TCP.POLICY_CODE,
                          TCP.PREM_FREQ,
                          TCBP.MASTER_BUSI_ITEM_ID,
                          TCP.STD_PREM_AF
            FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    CM,
                 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
                 DEV_PAS.T_CONTRACT_PRODUCT             TCP,
				 APP___PAS__DBUSER.T_BENEFIT_INSURED      BI,
				 APP___PAS__DBUSER.T_INSURED_LIST    IL
           WHERE 1 = 1
             AND CM.POLICY_CODE = TCBP.POLICY_CODE
             AND TCP.POLICY_CODE = TCBP.POLICY_CODE
             AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
						 AND IL.POLICY_CODE = CM.POLICY_CODE
             AND CM.LIABILITY_STATE IN ('1', '4')
             AND NOT EXISTS
           (SELECT 1
                    FROM DEV_PAS.T_CS_ACCEPT_CHANGE      TCAC,
                         DEV_PAS.T_CS_CONTRACT_BUSI_PROD TCCBP
                   WHERE TCAC.CHANGE_ID = TCCBP.CHANGE_ID
                     AND TCAC.SERVICE_CODE = 'NS'
                     AND TCAC.ACCEPT_STATUS = '18'
                     AND TCCBP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                     AND TCCBP.POLICY_ID = TCBP.POLICY_ID
                     and TCCBP.OLD_NEW = 1
                     and TCCBP.OPERATION_TYPE = 1)
		]]>
		<if test=" customerid != null and customerid != ''  "><![CDATA[ AND IL.CUSTOMER_ID = #{customerid} ]]></if>
		<if test=" begindate != null and begindate != ''  "><![CDATA[ AND CM.APPLY_DATE <=  #{begindate, jdbcType=DATE} ]]></if>
		<if test=" enddate  != null and enddate != ''  "><![CDATA[ AND CM.APPLY_DATE >= #{enddate, jdbcType=DATE} ]]></if>		
		<if test=" orderId  != null and orderId != '' and orderId == 1 "><![CDATA[ AND BI.ORDER_ID = '1' ]]></if>		
		<if test=" orderId  != null and orderId != '' and orderId == 2 "><![CDATA[ AND BI.ORDER_ID = '2' ]]></if>		
		
		
	</select>
	
	<!-- 根据保单号查询主险缴费期间 -->
	<select id="PA_findContractProductPayIntvByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT B.PREM_FREQ 
					 FROM DEV_PAS.T_CONTRACT_BUSI_PROD A, DEV_PAS.T_CONTRACT_PRODUCT B 
					 WHERE 1 = 1 
					   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID 
					   AND A.MASTER_BUSI_ITEM_ID IS NULL 
					   AND B.POLICY_CODE = #{policy_code} 
		]]>
	</select>
	
	<!-- 根据保单号查询主险缴费年期类型和下期缴费日 -->
	<select id="PA_findContractProductInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT TCP.CHARGE_PERIOD,
				       TCP.CHARGE_YEAR,
				       TCP.PREM_FREQ,
				       (SELECT DISTINCT T.PAY_DUE_DATE
		                  FROM DEV_PAS.T_CONTRACT_EXTEND T
		                 WHERE T.POLICY_CODE = TCP.POLICY_CODE
		                 AND T.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		                 AND TCP.IS_MASTER_ITEM = 1
		                   AND ROWNUM = 1) AS PAY_DUE_DATE,
				       TCP.VALIDATE_DATE,
				       (SELECT DISTINCT T.POLICY_PERIOD
				          FROM DEV_PAS.T_CONTRACT_EXTEND T
				         WHERE T.POLICY_CODE = TCP.POLICY_CODE
				           AND T.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				           AND TCP.IS_MASTER_ITEM = 1
				           AND ROWNUM = 1) AS POLICY_PERIOD,
				       (CASE
				         WHEN TCP.STD_PREM_AF IS NULL THEN
				          0
				         ELSE
				          TCP.STD_PREM_AF
				       END) + (CASE
				         WHEN TEP.EXTRA_PREM IS NULL THEN
				          0
				         ELSE
				          TEP.EXTRA_PREM
				       END) AS STD_PREM_AF,
				       (SELECT SUM(T.FEE_AMOUNT)
				          FROM DEV_PAS.V_PREM_ALL T
				         WHERE T.POLICY_CODE = TCP.POLICY_CODE
				           AND T.FEE_SCENE_CODE = 'NB'
				           AND T.FEE_STATUS = '16') AS FEE_AMOUNT,
				       TCBP.MASTER_BUSI_ITEM_ID,
				       (SELECT T.INSURED_AGE
				          FROM DEV_PAS.T_INSURED_LIST T
				         WHERE T.POLICY_CODE = TCM.POLICY_CODE
				           AND ROWNUM = 1) AS INSURED_AGE
				  FROM DEV_PAS.T_CONTRACT_MASTER TCM
				  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				    ON TCM.POLICY_CODE = TCBP.POLICY_CODE
				   AND TCM.APPLY_DATE = TCBP.APPLY_DATE
				  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
				    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
				  LEFT JOIN DEV_PAS.T_EXTRA_PREM TEP
				    ON TEP.ITEM_ID = TCP.ITEM_ID
				   AND TEP.POLICY_CODE = TCP.POLICY_CODE
				 WHERE TCP.POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	<!-- 银保信账户查询接口险种信息查询 -->
	<select id ="PA_findContractProductInfoForAccount"  resultType="java.util.Map" parameterType="java.util.Map">
		select b.busi_item_id,
		       b.busi_prod_code,
		       a.prem_freq,
		       a.start_pay_age,
		       a.start_pay_date,
		       a.std_prem_af,
		       a.total_prem_af,
		       a.initial_discnt_prem_af,
		       (select c.customer_name
		          from dev_pas.t_customer c
		         where c.customer_id in
		               (select t.customer_id
		                  from dev_pas.t_insured_list t
		                 where t.list_id = (select m.insured_id
		                                      from dev_pas.T_BENEFIT_INSURED m
		                                     where m.policy_code = #{policy_code}
		                                       and m.order_id = 1
		                                       and rownum = 1))) insured_name
		  from dev_pas.t_contract_product a, dev_pas.t_contract_busi_prod b
		 where a.busi_item_id = b.busi_item_id
		   and a.policy_code = #{policy_code}
	</select>
	
	<!-- 根据保单号和险种id查询险种已实际缴纳保费次数  -->
	<select id="PA_findPaidCountByPI" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				select max(q.paid_count) as max_paid_count
                  from dev_pas.v_prem_all q, dev_pas.t_contract_product w
                 where q.item_id = w.item_id
                   and q.fee_status in ('01', '16', '19')
                   and q.arap_flag = 1
			]]>	 
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND w.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND w.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 获取险种缴费信息   -->
	<select id="findBusiProdInfoByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT a.PRODUCT_CODE, a.PAIDUP_DATE, a.PREM_FREQ, b.PAY_DUE_DATE, a.EXTRA_PREM_AF, a.STD_PREM_AF, a.WAIVER_START, a.WAIVER_END, a.AMOUNT, b.NEXT_PREM,A.CHARGE_PERIOD,A.CHARGE_YEAR,
			       (SELECT SUM(TP.FEE_AMOUNT)
			          FROM DEV_PAS.T_PREM TP
			         WHERE TP.POLICY_CODE = A.POLICY_CODE
			           AND TP.ITEM_ID = A.ITEM_ID
			           AND TP.ARAP_FLAG = '1'
			           AND TP.FINISH_TIME IS NOT NULL) AS TOTAL_PREM_AF,
				(select tgp.GRACE_PERIOD_DAY  from dev_pds.T_GRACE_PERIOD tgp where (tgp.business_prd_id = c.busi_prd_id)) GRACE_PERIOD_DAY
		FROM DEV_PAS.T_CONTRACT_PRODUCT a, DEV_PAS.T_CONTRACT_EXTEND b, dev_pas.t_contract_busi_prod c
		WHERE a.ITEM_ID = b.ITEM_ID AND a.busi_item_id = c.busi_item_id AND a.BUSI_ITEM_ID = #{busi_item_id} 
	</select>
	
	<!-- 获取险种缴费信息   -->
	<select id="findRNW011AllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT A.PRODUCT_ID,
				       A.AMOUNT,
				       A.INITIAL_AMOUNT,
				       A.LIABILITY_STATE,
				       B.FIELD1,
				       (SELECT TP.FEE_AMOUNT
				          FROM DEV_PAS.T_PREM TP
				         WHERE 1 = 1
				           AND TP.POLICY_CODE = A.POLICY_CODE
				           AND TP.ITEM_ID = A.ITEM_ID
				           AND TP.FEE_SCENE_CODE = 'NB') AS FIRST_FEE_AMOUNT,    ]]>
				<if test=" unit_number != null and unit_number != ''  "><![CDATA[ 
 						(SELECT TPP.FEE_AMOUNT
				          FROM DEV_PAS.T_PREM TPP
				         WHERE 1 = 1
				           AND TPP.POLICY_CODE = A.POLICY_CODE
				           AND TPP.ITEM_ID = A.ITEM_ID
				           AND TPP.UNIT_NUMBER = #{unit_number}) AS FEE_AMOUNT  ]]></if>
				<if test=" unit_number == null or unit_number == ''  "><![CDATA[ 
 						0 AS FEE_AMOUNT  ]]></if>
		<![CDATA[ FROM DEV_PAS.T_CONTRACT_PRODUCT A, DEV_PAS.T_CONTRACT_PRODUCT_OTHER B
				 WHERE A.POLICY_CODE = B.POLICY_CODE
				   AND A.ITEM_ID = B.ITEM_ID
				   AND A.POLICY_CODE = #{policy_code}
				   AND A.BUSI_ITEM_ID = #{busi_item_id}    ]]>	 
	</select>
	<!-- 查询责任组是否为保单新增附加险的责任组   -->
	<select id="findIsNewAddriskProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT TCP.POLICY_CODE,TCP.BUSI_ITEM_ID
				  FROM DEV_PAS.T_CONTRACT_PRODUCT   TCP,
				       DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
				       DEV_PAS.T_CONTRACT_MASTER    TCM
				  WHERE TCP.POLICY_CODE=TCBP.POLICY_CODE
				  AND   TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				  AND   TCP.POLICY_CODE = TCM.POLICY_CODE
				  AND   TCBP.APPLY_DATE != TCM.APPLY_DATE
				  AND   TCBP.MASTER_BUSI_ITEM_ID IS NOT NULL
				  AND   TCP.PRODUCT_CODE = #{product_code}
				  AND   TCBP.POLICY_CODE=#{policy_code}
		]]>	 
	</select>
	
	<!-- 查询险种下责任组的客户投保金额、标准保费、客户投保份数信息  -->
	<select id="PA_findAUSByPCAndBII" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				 SELECT SUM(A.AMOUNT) AMOUNT,
				        SUM(A.STD_PREM_AF) STD_PREM_AF, 
				        SUM(A.UNIT) UNIT 
				   FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A
				  WHERE A.POLICY_CODE = #{policy_code}
				    AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>	 
	</select>

	<!-- 查询减保保单列表查询接口的责任组信息  -->
	<select id="PA_findContractProductInfoForReduction" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
		SELECT TCP.ITEM_ID,
		       TCP.PRODUCT_ID,
		       TCP.PRODUCT_CODE,
		       (SELECT TPL.PRODUCT_NAME
		          FROM APP___PAS__DBUSER.T_PRODUCT_LIFE TPL
		         WHERE TPL.PRODUCT_ID = TCP.PRODUCT_ID) PRODUCT_NAME,
		       TCP.STD_PREM_AF,
		       TCE.PAY_DUE_DATE,
		       TCP.PREM_FREQ,
		       TCP.AMOUNT || '/' || TCP.UNIT || '/' || (SELECT CASE TCPO.FIELD1
		                 WHEN '1' THEN '计划一'
		                 WHEN '2' THEN '计划二'
		                 WHEN '3' THEN '计划三'
		                 WHEN '4' THEN '计划四'
		                 WHEN '5' THEN '计划五'
		                 ELSE ''
		               END
		          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER TCPO
		         WHERE TCPO.ITEM_ID = TCP.ITEM_ID) AMOUNT_UNIT_FIELD1,
		       (SELECT TO_CHAR(WM_CONCAT(PEV.CODE || '-' || PEV.CODE2))
		          FROM APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA PPCR,
		               APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT   PPE,
		               APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE PEV
		         WHERE PPCR.RELATION_ID = PPE.RELATION_ID
		           AND PEV.LIST_VALUE_CODE = PPE.LIST_VALUE
		           AND PPE.UNIT_CODE = '017'
		           AND PPCR.BUSI_PRD_ID = #{busi_prd_id}
		           AND PPCR.PRODUCT_ID = TCP.PRODUCT_ID) FIELD1,
		       TCP.VALIDATE_DATE,
		       TCP.IS_MASTER_ITEM
		  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
		  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
		    ON TCP.POLICY_CODE = TCE.POLICY_CODE
		   AND TCP.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
		   AND TCP.ITEM_ID = TCE.ITEM_ID
		 WHERE 1 = 1
		 AND TCP.POLICY_CODE = #{policy_code}
		 AND TCP.BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	
		
	<select id="PA_findPayDueDateByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
				<![CDATA[
		    SELECT DISTINCT TCP.PAIDUP_DATE, TCE.PAY_DUE_DATE, TCP.COVERAGE_YEAR, TCP.COVERAGE_PERIOD
      FROM DEV_PAS.T_CONTRACT_PRODUCT TCP, DEV_PAS.T_CONTRACT_EXTEND TCE
     WHERE 1 = 1
       AND TCP.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
       AND TCP.ITEM_ID = TCE.ITEM_ID	]]>
       <if test=" policy_id  != null "><![CDATA[ AND TCP.POLICY_ID = #{policy_id} ]]></if>
       <if test=" busi_item_id  != null "><![CDATA[ AND TCP.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 根据保单号、险种ID查询主险保障期间和保障期间类型 -->
	<select id="PA_findContractProductCoverAgeMain" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT B.COVERAGE_YEAR,B.COVERAGE_PERIOD,B.IS_MASTER_ITEM
					  FROM DEV_PAS.T_CONTRACT_BUSI_PROD A, DEV_PAS.T_CONTRACT_PRODUCT B 
					 WHERE 1 = 1 
					   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID 
					   AND A.MASTER_BUSI_ITEM_ID IS NULL 
					   AND B.POLICY_CODE = #{policy_code} 
					   AND B.BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	
	<!-- 养老金承保报送根据保单号查询主险信息 -->
	<select id="PA_findAllMasterContractProductForGryl" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT B.POLICY_CODE, B.PREM_FREQ, B.BUSI_ITEM_ID
					  FROM DEV_PAS.T_CONTRACT_BUSI_PROD A, DEV_PAS.T_CONTRACT_PRODUCT B 
					 WHERE 1 = 1 
					   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID 
					   AND A.MASTER_BUSI_ITEM_ID IS NULL 
					   AND B.POLICY_CODE = #{policy_code}  ]]>
	</select>
	
</mapper>
