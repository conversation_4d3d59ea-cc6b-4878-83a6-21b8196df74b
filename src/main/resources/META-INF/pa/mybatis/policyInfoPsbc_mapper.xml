<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyInfoPsbcDao">
	
	<!-- 查询保单信息 -->
	<select id="PA_findPolicyInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT TCM.POLICY_CODE,
		       TCM.POLICY_ID,
		       TCM.SUBMIT_CHANNEL,
		       (SELECT T.CHARGE_PERIOD
		          FROM DEV_PAS.T_CONTRACT_PRODUCT T
		         WHERE T.POLICY_CODE = TCM.POLICY_CODE
		           AND T.LIABILITY_STATE = 1
		           AND ROWNUM = 1) AS CHARGE_PERIOD,
		       (SELECT T.ACCOUNT
		          FROM DEV_PAS.T_PAYER_ACCOUNT T
		         WHERE T.POLICY_ID = TCM.POLICY_ID
		           AND ROWNUM = 1) AS NOW_ACCOUNT,
		       (SELECT T.NEXT_ACCOUNT
		          FROM DEV_PAS.T_PAYER_ACCOUNT T
		         WHERE T.POLICY_ID = TCM.POLICY_ID
		           AND ROWNUM = 1) AS NEXT_ACCOUNT,
		       TCM.LIABILITY_STATE,
		       TCM.APPLY_DATE,
		       TCM.VALIDATE_DATE,
		       TCM.LAPSE_DATE,
		       TCM.EXPIRY_DATE,
		       (SELECT SUM(CASE
		                     WHEN A.ARAP_FLAG = '1' THEN
		                      A.FEE_AMOUNT
		                     ELSE
		                      A.FEE_AMOUNT * -1
		                   END) AS RENEWAL_PAY_AMOUNT /*累计缴费金额*/
		          FROM DEV_PAS.T_PREM A, DEV_PAS.T_CONTRACT_BUSI_PROD B
		         WHERE B.POLICY_CODE = TCM.POLICY_CODE
		           AND B.POLICY_CODE = A.POLICY_CODE
		           AND B.BUSI_PROD_CODE = A.BUSI_PROD_CODE
		           AND B.APPLY_DATE = TCM.APPLY_DATE
		           AND A.FEE_STATUS IN ('01', '16', '19')
		           AND (A.FEE_SCENE_CODE IN ('RN', 'NB') OR
		               (A.FEE_SCENE_CODE = 'CS' AND
		               A.SERVICE_CODE IN ('PA', 'PT', 'AM')))) AS TOTAL_AMOUNT,
		       (SELECT SUM(T.AMOUNT)
		          FROM DEV_PAS.T_CONTRACT_PRODUCT T, DEV_PAS.T_CONTRACT_BUSI_PROD U
		         WHERE T.POLICY_CODE = U.POLICY_CODE
		           AND T.BUSI_ITEM_ID = U.BUSI_ITEM_ID
		           AND U.MASTER_BUSI_ITEM_ID IS NULL
		           AND U.POLICY_CODE = TCM.POLICY_CODE) AS TOTAL_PREM_AF,
		       (SELECT T.PAIDUP_DATE
				  FROM DEV_PAS.T_CONTRACT_BUSI_PROD T
				 WHERE T.POLICY_CODE = TCM.POLICY_CODE
				   AND T.MASTER_BUSI_ITEM_ID IS NULL
				   AND ROWNUM = 1) AS PAIDUP_DATE,
		       (SELECT TCE.PAY_DUE_DATE
		          FROM DEV_PAS.T_CONTRACT_EXTEND TCE
		         WHERE TCM.POLICY_CODE = TCE.POLICY_CODE
		           AND TCE.PAY_DUE_DATE =
		               (SELECT MAX(T.PAY_DUE_DATE)
		                  FROM DEV_PAS.T_CONTRACT_EXTEND T
		                 WHERE TCM.POLICY_CODE = T.POLICY_CODE)
		           AND ROWNUM = 1) AS PAY_DUE_DATE,
		       (SELECT TCE.NEXT_PREM
		          FROM DEV_PAS.T_CONTRACT_EXTEND TCE
		         WHERE TCM.POLICY_CODE = TCE.POLICY_CODE
		           AND TCE.PAY_DUE_DATE =
		               (SELECT MAX(T.PAY_DUE_DATE)
		                  FROM DEV_PAS.T_CONTRACT_EXTEND T
		                 WHERE TCM.POLICY_CODE = T.POLICY_CODE)
		           AND ROWNUM = 1) AS NEXT_PREM,
		        (SELECT count(1)
			  FROM DEV_PAS.T_CONTRACT_BUSI_PROD B
			 WHERE TCM.POLICY_CODE = B.POLICY_CODE
			   AND TCM.APPLY_DATE  = B.APPLY_DATE) AS product_sum,
			 (SELECT SUM(T.FEE_AMOUNT)
           FROM DEV_PAS.T_PREM_ARAP T
          WHERE T.POLICY_CODE = TCM.POLICY_CODE
            AND T.FEE_TYPE = 'T003030100') AS annuity_fee_amount, --年金给付总金额
            (select t.BONUS_ALLOT from dev_pas.T_BONUS_ALLOCATE t where t.policy_code = TCM.POLICY_CODE and  rownum = 1) AS bonus_type,--分红类型
            (SELECT SUM(T.FEE_AMOUNT)
           FROM DEV_PAS.T_PREM_ARAP T
          WHERE T.POLICY_CODE = TCM.POLICY_CODE
            AND T.FEE_TYPE IN ('P004620000','P004620100')) AS Bonus_FEE_AMOUNT,
       (SELECT SUM(TCI.INTEREST_CAPITAL)
	      FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
	           DEV_PDS.T_BUSINESS_PRODUCT   TBP,
	           DEV_PAS.T_CONTRACT_INVEST    TCI
	     WHERE TCBP.POLICY_CODE = TCM.POLICY_CODE
	       AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
	       AND TCBP.POLICY_ID = TCI.POLICY_ID
	       AND TCBP.BUSI_ITEM_ID = TCI.BUSI_ITEM_ID
	       AND TBP.PRODUCT_CATEGORY1 = '20003') AS interest_capital -- 万能险账户价值
		  FROM DEV_PAS.T_CONTRACT_MASTER TCM
		 WHERE TCM.POLICY_CODE = #{policy_code} 
		   AND TCM.SERVICE_BANK = #{service_bank}

		]]>
	</select>
	<!-- 查询险种信息 -->
	<select id="PA_findBusiProdInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT 
		C.PRODUCT_CATEGORY1,
		B.POLICY_CODE,
		B.BUSI_ITEM_ID,
		B.MASTER_BUSI_ITEM_ID
		FROM DEV_PAS.T_CONTRACT_MASTER A, DEV_PAS.T_CONTRACT_BUSI_PROD B , DEV_PDS.T_BUSINESS_PRODUCT C
		WHERE A.POLICY_CODE = B.POLICY_CODE AND A.APPLY_DATE = B.APPLY_DATE AND B.BUSI_PRD_ID = C.BUSINESS_PRD_ID
		]]>
		<if test=" policy_code  != null and policy_code!= ''">
				<![CDATA[AND A.POLICY_CODE =#{policy_code} ]]>
		</if>
	</select>
	
	
	<!-- 查询保单发生解除合同、协议退保、公司解约，投连险退保，满期终止且发生满期金领取，对应交易的退费账号 -->
	<select id="PA_findBankAccountPsbc" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.BANK_ACCOUNT
		FROM (SELECT A.BANK_ACCOUNT
           FROM DEV_PAS.T_PREM_ARAP A, DEV_PAS.T_CONTRACT_MASTER B
          WHERE A.POLICY_CODE = B.POLICY_CODE
            AND B.POLICY_CODE = #{policy_code} 
            AND A.PAY_MODE = '32'
            AND A.FINISH_TIME IS NOT NULL
            AND (A.SERVICE_CODE IN ('CT', 'XT', 'EA', 'IT') OR
                (A.SERVICE_CODE = 'AG' AND B.END_CAUSE = '01'))
          ORDER BY A.FINISH_TIME DESC) T
          WHERE ROWNUM = 1
		]]>
	</select>
	
	<!-- 查询保单累计现金分红金额 -->
	<select id="PA_findPolicyXJBonusAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT SUM(T.CASH_BONUS) AS CASH_BONUS
		  FROM DEV_PAS.T_BONUS_ALLOCATE T
		 WHERE T.POLICY_CODE = #{policy_code} 
		   AND T.BONUS_ALLOT = '4'
		]]>
	</select>
	
	<!-- 查询保单最近一次累计分红金额 -->
	<select id="findPolicyBEBonusAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT A.CASH_BONUS FROM
		(SELECT T.CASH_BONUS
		  FROM DEV_PAS.T_BONUS_ALLOCATE T
		 WHERE T.BONUS_ALLOT != '4'
		   AND T.POLICY_CODE = #{policy_code} 
		   ORDER BY T.ALLOCATE_DATE DESC) A
		   WHERE ROWNUM = 1
		]]>
	</select>
	
	<!-- 查询分红或年金信息 -->
	<select id="PA_findProductBonusOrAnnuityInfos" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT SUM(A.FEE_AMOUNT) as FEE_AMOUNT, A.PAY_DUE_DATE as validate_date
       FROM DEV_PAS.T_PAY_DUE A, DEV_PAS.T_PAY_PLAN B
      WHERE A.PLAN_ID = B.PLAN_ID
        AND A.POLICY_CODE = #{policy_code}]]>
        <if test=" find_type != null and find_type != '' and find_type == 1">
            	<![CDATA[AND B.PAY_PLAN_TYPE in (2,3) ]]>
            </if>
            <if test=" find_type != null and find_type != '' and find_type == 2">
            	<![CDATA[ AND B.PAY_PLAN_TYPE = 1  ]]>
            </if>
        <![CDATA[
      GROUP BY A.PAY_DUE_DATE
      ORDER BY A.PAY_DUE_DATE DESC
		]]>
	</select>
	
	<!-- 查询保单下的身故受益人信息 -->
	<select id="PA_findPolicyBeneInfos" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT B.CUSTOMER_NAME
		  FROM DEV_PAS.T_CONTRACT_BENE A, DEV_PAS.T_CUSTOMER B
		 WHERE A.CUSTOMER_ID = B.CUSTOMER_ID
		   AND A.BENE_TYPE = 1
		   AND A.POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	<!-- 查询保单下的被保人信息 -->
	<select id="PA_findPolicyInsuredInfos" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT B.CUSTOMER_NAME
		  FROM DEV_PAS.T_INSURED_LIST A, DEV_PAS.T_CUSTOMER B
		 WHERE A.CUSTOMER_ID = B.CUSTOMER_ID
		   AND A.POLICY_CODE = #{policy_code}
		]]>
	</select>
</mapper>