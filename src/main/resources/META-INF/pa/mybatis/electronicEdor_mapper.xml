<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IElectronicEdorDao">
<!-- 添加操作 -->
	<sql id="PA_dailyTradingQueryCondition">
		<if test=" apply_code != null and apply_code != ''"><![CDATA[ AND tcm.apply_code=#{apply_code} ]]></if>
	</sql>

    <select id="PA_findSocialSecurityByApplyCode" resultType="java.lang.Integer" parameterType="java.util.Map">
	   <![CDATA[ SELECT COUNT(1)
  	               FROM APP___PAS__DBUSER.T_CONTRACT_MEDICAL A
                   WHERE 1=1
                     AND A.policy_code=#{POLICY_CODE} ]]>
	</select>
	
	<select id="PA_findPreservationAlterationByApplyCode" resultType="java.lang.Integer" parameterType="java.util.Map">
	   <![CDATA[ SELECT COUNT(1)
  	               FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC
                   WHERE PC.policy_code=#{POLICY_CODE}  ]]>
	</select>

    <select id="PA_findWeChatInsuranceByApplyCode" resultType="java.lang.Integer" parameterType="java.util.Map">
	   <![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_cs_application A
	             WHERE A.CHANGE_ID IN
	             (select B.change_id from APP___PAS__DBUSER.T_CS_POLICY_CHANGE B WHERE B.policy_code=#{POLICY_CODE})
	             AND A.source_type ='03' 
                 AND A.service_type = '21'
	     ]]>
	</select>
	
	<select id="PA_findCertificateInformationByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[  select A.CUSTOMER_CERT_TYPE, A.CUSTOMER_CERTI_CODE
                    from dev_pas.T_CONTRACT_MASTER cm,
                         dev_pas.T_POLICY_HOLDER   il,
                         dev_pas.t_customer         A
                   where cm.policy_code = #{POLICY_CODE}
                     and cm.policy_id = il.policy_id
                     and il.customer_id = A.customer_id   ]]>
	</select>
	
	<select id="PA_findElectronicEdor" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[  select TC.policy_code as cont_no,tc.endorse_code as edor_no,
                           to_char(tc.validate_time,'yyyy-MM-dd') as validate_time,
                           to_char(tc.update_time,'hh24:mi:ss') modify_time,
                           tc.service_code as edor_type,
                         case tc.print_status when '3' then '1'
                         else '0' end as if_finish
                     from APP___PAS__DBUSER.T_CS_POLICY_CHANGE TC,
                          APP___PAS__DBUSER.t_Cs_Accept_Change CA
                      where TC.change_id = CA.change_id
                      and TC.accept_id = CA.accept_id
                      and CA.accept_status = '18'
                      and tc.service_code in ('CC','PC','EN','CT','ES','AC')
                      and TC.policy_code = #{POLICY_CODE}
                       ]]>
        <if test="edor_type !=null"><![CDATA[ and tc.service_code = #{edor_type}]]></if>                     
		<if test="validate_time_start !=null"><![CDATA[ and tc.validate_time >= #{validate_time_start} ]]></if>
		<if test="validate_time_end !=null"><![CDATA[ and tc.validate_time <= #{validate_time_end} ]]></if>
	    <![CDATA[order by validate_time desc,modify_time desc ]]>
	</select>
	
	<select id="PA_findElectronicEdorList" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[  select TC.policy_code as cont_no,tc.endorse_code as edor_no,
                           to_char(tc.validate_time,'yyyy-MM-dd') as validate_time,
                           to_char(tc.update_time,'hh24:mi:ss') modify_time,
                           tc.service_code as edor_type,
                         case tc.print_status when '3' then '1'
                         else '0' end as if_finish
                     from APP___PAS__DBUSER.T_CS_POLICY_CHANGE TC,
                          APP___PAS__DBUSER.t_Cs_Accept_Change CA
                      where TC.change_id = CA.change_id
                      and TC.accept_id = CA.accept_id
                      and CA.accept_status = '18'                 
                      and TC.policy_code = #{POLICY_CODE} ]]>
        <if test="edor_type !=null"><![CDATA[ and tc.service_code = #{edor_type}]]></if>              
		<if test="validate_time_start !=null"><![CDATA[ and tc.validate_time >= #{validate_time_start} ]]></if>
		<if test="validate_time_end !=null"><![CDATA[ and tc.validate_time <= #{validate_time_end} ]]></if>
		<![CDATA[order by validate_time desc,modify_time desc ]]>
	</select>

</mapper>
