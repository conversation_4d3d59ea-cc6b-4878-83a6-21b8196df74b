<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="IsMergeCustomerID">
	
	<!-- 更新保单被保人列表客户ID -->
	<update id="PA_updateInsuredList_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INSURED_LIST ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新保单被保人变更履历表客户ID -->
	<update id="PA_updateInsuredListLog_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INSURED_LIST_LOG ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新保单投保人表客户ID -->
	<update id="PA_updatePolicyHolder_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_HOLDER ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新业务锁记录表客户ID -->
	<update id="PA_updateLockPolicy_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LOCK_POLICY ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新业务锁记录轨迹备份表 客户ID -->
	<update id="PA_updateLockPolicyBackup_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LOCK_POLICY_BACKUP ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新保单付款人变更履历表 客户ID -->
	<update id="PA_updatePayerLog_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER_LOG ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新保单付款人表 客户ID -->
	<update id="PA_updatePayer_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAYER ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>  
	
	<!-- 更新保单受益人表 客户ID -->
	<update id="PA_updateContractBene_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BENE ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update> 
	
	<!-- 更新保单受益人变更履历表 客户ID -->
	<update id="PA_updateContractBeneLog_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BENE_LOG ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新特别约定表  客户ID -->
	<update id="PA_PolicyCondition_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CONDITION ]]>
		<set>
		<trim suffixOverrides=",">		    
		    insured_customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE insured_customer_id = #{customerId} ]]>
	</update>  
	
	<!-- 更新特别约定变更履历表  客户ID -->
	<update id="PA_PolicyConditionLog_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_CONDITION_LOG ]]>
		<set>
		<trim suffixOverrides=",">		    
		    insured_customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE insured_customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新生存给付应领明细表 客户ID -->
	<update id="PA_PayDueList_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_DUE_LIST ]]>
		<set>
		<trim suffixOverrides=",">		    
		    payee_customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE payee_customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新生存给付计划收款人信息变更履历表 客户ID -->
	<update id="PA_updatePayDueListLog_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN_PAYEE_LOG ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新生存给付计划收款人信息表  客户ID -->
	<update id="PA_updatePayPlanPayee_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN_PAYEE ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新风险保额归档数据归档表 客户ID -->
	<update id="PA_updateRiskAmountArchive_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_ARCHIVE ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新风险保额数据主表 客户ID -->
	<update id="PA_updateRiskAmount_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新风险保额数据变更履历表 客户ID -->
	<update id="PA_updateRiskAmountLog_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_LOG ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新风险保额队列数据表 客户ID -->
	<update id="PA_updateRiskAmountQueue_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_AMOUNT_QUEUE ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
	<!-- 更新保单费用记录表 被保人ID -->
	<update id="PA_updatePrem_holderId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_prem ]]>
		<set>
		<trim suffixOverrides=",">		    
		    holder_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE holder_id = #{customerId} ]]>
	</update>
	
	<!-- 更新保单费用记录表 投保人ID -->
	<update id="PA_updatePrem_insuredId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_prem ]]>
		<set>
		<trim suffixOverrides=",">		    
		    insured_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE insured_id = #{customerId} ]]>
	</update>
	
	
	<!-- 更新应收应付表 客户ID -->
	<update id="PA_updatePremArap_customerId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_Prem_Arap ]]>
		<set>
		<trim suffixOverrides=",">		    
		    customer_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customerId} ]]>
	</update>
	
		<!-- 更新应收应付表 被保人ID -->
	<update id="PA_updatePremArap_holderId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_Prem_Arap ]]>
		<set>
		<trim suffixOverrides=",">		    
		    holder_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE holder_id = #{customerId} ]]>
	</update>
	
	<!-- 更新应收应付表 投保人ID -->
	<update id="PA_updatePremArap_insuredId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_Prem_Arap ]]>
		<set>
		<trim suffixOverrides=",">		    
		    insured_id = #{customerIdStd, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE insured_id = #{customerId} ]]>
	</update>
</mapper>