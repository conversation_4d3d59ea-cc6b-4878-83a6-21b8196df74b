<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IInvestUnitsDaysumDao">
	
	<sql id="investUnitsDaysumWhereCondition">
		<if test=" units  != null "><![CDATA[ AND A.UNITS = #{units} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" box_units  != null "><![CDATA[ AND A.BOX_UNITS = #{box_units} ]]></if>
		<if test=" cal_date  != null  and  cal_date  != ''  "><![CDATA[ AND to_char(A.CAL_DATE,'yyyy-MM-dd') = to_char(#{cal_date},'yyyy-MM-dd') ]]></if>
		<if test=" cus_units  != null "><![CDATA[ AND A.CUS_UNITS = #{cus_units} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" invest_account_id  != null "><![CDATA[ AND A.INVEST_ACCOUNT_ID = #{invest_account_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryInvestUnitsDaysumByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addInvestUnitsDaysum"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
				SELECT APP___PAS__DBUSER.S_INVEST_UNITS_DAYSUM__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM(
				UNITS, INVEST_ACCOUNT_CODE, INSERT_TIME, BOX_UNITS, CAL_DATE, UPDATE_TIME, INSERT_TIMESTAMP, 
				CUS_UNITS, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, INVEST_ACCOUNT_ID ) 
			VALUES (
				#{units, jdbcType=NUMERIC}, #{invest_account_code, jdbcType=VARCHAR} , SYSDATE , #{box_units, jdbcType=NUMERIC} , #{cal_date, jdbcType=DATE} , SYSDATE , CURRENT_TIMESTAMP
				, #{cus_units, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteInvestUnitsDaysum" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateInvestUnitsDaysum" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM ]]>
		<set>
		<trim suffixOverrides=",">
		    UNITS = #{units, jdbcType=NUMERIC} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
		    BOX_UNITS = #{box_units, jdbcType=NUMERIC} ,
		    CAL_DATE = #{cal_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    CUS_UNITS = #{cus_units, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findInvestUnitsDaysumByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNITS, A.INVEST_ACCOUNT_CODE, A.BOX_UNITS, A.CAL_DATE, 
			A.CUS_UNITS, A.LIST_ID, A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM A WHERE 1 = 1  ]]>
		<include refid="queryInvestUnitsDaysumByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
<!-- 查询单个操作 -->	
	<select id="findInvestUnitsDaysum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNITS, A.INVEST_ACCOUNT_CODE, A.BOX_UNITS, A.CAL_DATE, 
			A.CUS_UNITS, A.LIST_ID, A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM A WHERE 1 = 1  ]]>
		<include refid="investUnitsDaysumWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>	

<!-- 按map查询操作 -->
	<select id="findAllMapInvestUnitsDaysum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNITS, A.INVEST_ACCOUNT_CODE, A.BOX_UNITS, A.CAL_DATE, 
			A.CUS_UNITS, A.LIST_ID, A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllInvestUnitsDaysum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNITS, A.INVEST_ACCOUNT_CODE, A.BOX_UNITS, A.CAL_DATE, 
			A.CUS_UNITS, A.LIST_ID, A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findInvestUnitsDaysumTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryInvestUnitsDaysumForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UNITS, B.INVEST_ACCOUNT_CODE, B.BOX_UNITS, B.CAL_DATE, 
			B.CUS_UNITS, B.LIST_ID, B.INVEST_ACCOUNT_ID FROM (
					SELECT ROWNUM RN, A.UNITS, A.INVEST_ACCOUNT_CODE, A.BOX_UNITS, A.CAL_DATE, 
			A.CUS_UNITS, A.LIST_ID, A.INVEST_ACCOUNT_ID FROM APP___PAS__DBUSER.T_INVEST_UNITS_DAYSUM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
    
    <!-- 根据交易日期查询基金代码、交易单位数总数sum(TRANS_UNITS)-->
    <select id="findAccountCodeBydate" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        select sum(TRANS_UNITS)TRANS_UNITS, f.fund_code FUND_CODE
         from dev_pas.T_FUND_TRANS f 
         where f.deal_time=to_date(#{PricingDate},'yyyy-MM-dd')
         and f.fund_code in ('890000', '892001')
          group by fund_code 
        ]]>
    </select>
    
    <!-- 根据交易日期、基金代码、查询各交易类型的 单位数总和 -->
    <select id="findAccountCodeByDCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
    select 
(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE in(35,36,37,38,39,40)) as NbPremOff,

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE in(06，13，31，34)) as AppendPremOff,

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE =14) as OtherAccountIn，
'0' as FreeSurrenderBid，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE =24) as SurvivalBid，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE in(08,22)) as PartialBid，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE in(07,21)) as SurrenderBid，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE =25) as ClaimBid，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE =24) as MaturityBid，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE =26) as OutBid，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE in(03,41)) as RiskPremCharge，

(select nvl(sum(trans_units),0)  from dev_pas.T_FUND_TRANS s 
where deal_time=to_date(#{PricingDate},'yyyy-MM-dd') and fund_code=#{AccountCode}  and TRANS_CODE =04) as PolicyManageCharge，

'0' as UnitAdjust    from dual

         ]]>
    </select>
    <!-- 根据基金代码和交易日期获取基金的上一次交易日期  -->
    <select id="findAccounDateBydate" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ 
                select * from(
        select to_char(s.deal_time,'yyyy-MM-dd') DEAL_TIME from dev_pas.T_FUND_TRANS s 
        where s.fund_code= #{AccountCode}  
        and s.deal_time<to_date(#{PricingDate},'yyyy-MM-dd') order by s.deal_time desc
        )
         where rownum<2
      ]]>
    </select>
</mapper>
