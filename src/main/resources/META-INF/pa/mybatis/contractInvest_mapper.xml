<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractInvestDao">

	<sql id="PA_contractInvestWhereCondition">
		<if test=" accum_units  != null "><![CDATA[ AND A.ACCUM_UNITS = #{accum_units} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" invest_account_type  != null "><![CDATA[ AND A.INVEST_ACCOUNT_TYPE = #{invest_account_type} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND trim(A.ACCOUNT_CODE) = trim(#{account_code}) ]]></if>		
		<if test=" settle_due_date  != null  and  settle_due_date  != ''  "><![CDATA[ AND A.SETTLE_DUE_DATE = #{settle_due_date} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" settlement_rate  != null "><![CDATA[ AND A.SETTLEMENT_RATE = #{settlement_rate} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" negative_time  != null  and  negative_time  != ''  "><![CDATA[ AND A.NEGATIVE_TIME = #{negative_time} ]]></if>
		<if test=" total_prem  != null  and  total_prem  != ''  "><![CDATA[ AND A.TOTAL_PREM = #{total_prem} ]]></if>
	</sql>


	<sql id="PA_findAllContractInvestByInterestCapitalMore">
		<if test=" accum_units  != null "><![CDATA[ AND A.ACCUM_UNITS = #{accum_units} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL >= #{interest_capital} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" invest_account_type  != null "><![CDATA[ AND A.INVEST_ACCOUNT_TYPE = #{invest_account_type} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>		
		<if test=" settle_due_date  != null  and  settle_due_date  != ''  "><![CDATA[ AND A.SETTLE_DUE_DATE = #{settle_due_date} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" settlement_rate  != null "><![CDATA[ AND A.SETTLEMENT_RATE = #{settlement_rate} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" negative_time  != null  and  negative_time  != ''  "><![CDATA[ AND A.NEGATIVE_TIME = #{negative_time} ]]></if>
		<if test=" total_prem  != null  and  total_prem  != ''  "><![CDATA[ AND A.TOTAL_PREM = #{total_prem} ]]></if>
	</sql>
<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractInvestByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractInvestByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractInvestByAccountCodeCondition">
		<if test=" account_code != null and account_code != '' "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
	</sql>	
	<sql id="PA_queryContractInvestByAccumUnitsCondition">
		<if test=" accum_units  != null "><![CDATA[ AND A.ACCUM_UNITS = #{accum_units} ]]></if>
	</sql>	
	
	<sql id="PA_queryContractInvestBySettleDueDateCondition">
		<if test=" settle_due_date  != null "><![CDATA[ AND A.SETTLE_DUE_DATE = #{settle_due_date} ]]></if>
	</sql>

<!-- 添加操作 -->
	<insert id="PA_addContractInvest"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_INVEST_RATE__LIST_I.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_INVEST(
		        TOTAL_PREM,ACCUM_UNITS, INTEREST_CAPITAL, PRODUCT_ID, INSERT_TIME, INTEREST_SUM, ITEM_ID, INVEST_ACCOUNT_TYPE, 
		        UPDATE_TIME, ACCOUNT_CODE, INSERT_TIMESTAMP,  UPDATE_BY, SETTLE_DUE_DATE, INTEREST_BALANCE, 
		        LIST_ID, SETTLEMENT_RATE, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, POLICY_ID,CREATE_DATE,
		        NEGATIVE_TIME ) 
		      VALUES (
		        #{total_prem, jdbcType=NUMERIC},#{accum_units, jdbcType=NUMERIC}, #{interest_capital, jdbcType=NUMERIC} , #{product_id, jdbcType=NUMERIC} , SYSDATE , #{interest_sum, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , #{invest_account_type, jdbcType=NUMERIC} 
		        , SYSDATE , #{account_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{settle_due_date, jdbcType=DATE} , #{interest_balance, jdbcType=NUMERIC} 
		        , #{list_id, jdbcType=NUMERIC} , #{settlement_rate, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ,#{create_date, jdbcType=DATE}
		        , #{negative_time, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractInvest" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractInvest" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST ]]>
		<set>
		<trim suffixOverrides=",">
			TOTAL_PREM = #{total_prem, jdbcType=NUMERIC},
		    ACCUM_UNITS = #{accum_units, jdbcType=NUMERIC} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_TYPE = #{invest_account_type, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,		    
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SETTLE_DUE_DATE = #{settle_due_date, jdbcType=DATE} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    SETTLEMENT_RATE = #{settlement_rate, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    NEGATIVE_TIME = #{negative_time, jdbcType=DATE} ,
		    <if test=" create_date  != null "><![CDATA[  CREATE_DATE = #{create_date, jdbcType=DATE} ,]]></if>
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractInvestByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE,A.CREATE_DATE, 
			A.ACCOUNT_CODE,  A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A where 1 = 1  ]]>
		<include refid="PA_queryContractInvestByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findContractInvestByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE,A.CREATE_DATE, 
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findContractInvestByPolicyId1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE,A.CREATE_DATE, 
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE POLICY_ID = #{policy_id}  ]]>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	<select id="PA_findContractInvestByAccountCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE,A.CREATE_DATE, 
			A.ACCOUNT_CODE,A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestByAccountCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findContractInvestByAccumUnits" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE,A.CREATE_DATE,  
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestByAccumUnitsCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
		
	
	<select id="PA_findContractInvestBySettleDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE,A.CREATE_DATE,  
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestBySettleDueDateCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractInvest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE,A.CREATE_DATE,  
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractInvest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE, 
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME,A.INSERT_TIME,A.CREATE_DATE,P.tax_extension_flag,P.product_code_sys 
			FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A,APP___PAS__DBUSER.T_BUSINESS_PRODUCT P,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B 
			WHERE ROWNUM <=  1000 AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
            AND B.BUSI_PRD_ID = P.BUSINESS_PRD_ID  ]]>
	    <include refid="PA_contractInvestWhereCondition" />
		<![CDATA[ ORDER BY  A.ACCOUNT_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractInvestTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1  ]]>
		<include refid="PA_contractInvestWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractInvestForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.TOTAL_PREM, B.ACCUM_UNITS, B.INTEREST_CAPITAL, B.PRODUCT_ID, B.INTEREST_SUM, B.ITEM_ID, B.INVEST_ACCOUNT_TYPE, 
			B.ACCOUNT_CODE, B.SETTLE_DUE_DATE, B.INTEREST_BALANCE, 
			 B.LIST_ID, B.SETTLEMENT_RATE, B.BUSI_ITEM_ID, B.POLICY_ID, B.CREATE_DATE, 
			B.NEGATIVE_TIME FROM (
					SELECT ROWNUM RN,A.TOTAL_PREM, A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE, 
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, A.CREATE_DATE, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 	单条数据查询 -->
	<select id="PA_findContractInvest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE, A.CREATE_DATE, 
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID,A.INSERT_TIME, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1   ]]>
		<include refid="PA_contractInvestWhereCondition" />
		<![CDATA[ order by a.insert_time desc ]]>
		
	</select>
	
		<select id="PA_findContractInvestList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE, A.CREATE_DATE, 
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID,A.INSERT_TIME, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1   ]]>
		<include refid="PA_contractInvestWhereCondition" />
		<![CDATA[ order by a.insert_time desc ]]>
		
	</select>
	<!-- 查找本息和大于零的投资连结数据 -->
	<select id="PA_findAllContractInvestByInterestCapitalMore" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE, A.CREATE_DATE, 
			A.ACCOUNT_CODE,A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A WHERE 1 = 1   ]]>
		<include refid="PA_findAllContractInvestByInterestCapitalMore" />
	</select>

	<!-- 万能保单最新月度结算信息
		其中#{product_name_sys}实际为#{busi_item_id}属性,此处借用,否则参数set错误导致查询记录失败
	 -->
	<select id="messageQuerySql" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select a.policy_id,a.account_code,( select product_code_sys  from APP___PAS__DBUSER.T_business_product where business_prd_id=(select b.busi_prd_id from APP___PAS__DBUSER.T_contract_busi_prod b
  where b.busi_item_id = a.busi_item_id)) as product_code_sys ,(select b.policy_code from APP___PAS__DBUSER.T_contract_busi_prod b  where b.busi_item_id = a.busi_item_id) as busi_prd_id,
(select c.product_name_sys from APP___PAS__DBUSER.T_BUSINESS_PRODUCT c where c.product_code_sys =(select busi_prod_code from APP___PAS__DBUSER.T_contract_busi_prod
 where busi_item_id = a.busi_item_id)) as product_name_sys,(select c.product_abbr_name from APP___PAS__DBUSER.T_BUSINESS_PRODUCT c where c.product_code_sys =(select busi_prod_code
  from APP___PAS__DBUSER.T_contract_busi_prod where busi_item_id = a.busi_item_id)) as PRODUCT_ABBR_NAME, (select sum(TOTAL_PREM_AF) from APP___PAS__DBUSER.T_CONTRACT_PRODUCT d
  where d.policy_id = a.policy_id and d.busi_item_id = a.busi_item_id and d.item_id = a.item_id) as TOTAL_PREM_AF_ZC,(select sum(TOTAL_PREM_AF)
 from APP___PAS__DBUSER.T_CONTRACT_PRODUCT d where d.policy_id = a.policy_id and d.busi_item_id = a.busi_item_id and d.item_id = a.item_id) as TOTAL_PREM_AF_AM,(select TRANS_AMOUNT
 from APP___PAS__DBUSER.T_FUND_TRANS e where e.trans_code = '04' and e.policy_id = a.policy_id and e.busi_item_id = a.busi_item_id and e.item_id = a.item_id and rownum = 1) as trand_amount1,
 (select FEE_AMOUNT from APP___PAS__DBUSER.T_PERSISTENCE_BONUS d where d.policy_id = a.policy_id and d.busi_item_id = a.busi_item_id and d.item_id = a.item_id and  d.PAY_DUE_DATE = (select max(d.PAY_DUE_DATE) from APP___PAS__DBUSER.T_PERSISTENCE_BONUS d
                 where d.policy_id = a.policy_id
                   and d.busi_item_id = a.busi_item_id
                   and d.item_id = a.item_id) and rownum=1) as FEE_AMOUNT,
 (select TRANS_AMOUNT from APP___PAS__DBUSER.T_FUND_TRANS e where e.trans_code = '03' and e.policy_id = a.policy_id and e.busi_item_id = a.busi_item_id and e.item_id = a.item_id
  and rownum = 1) as trand_amount2, (select TRANS_AMOUNT from APP___PAS__DBUSER.T_FUND_TRANS e where e.trans_code = '04'and e.policy_id = a.policy_id and e.busi_item_id = a.busi_item_id
  and rownum = 1) as trand_amount3, (select f.CAPITAL_BALANCE from APP___PAS__DBUSER.T_POLICY_ACCOUNT f
 where f.policy_id = a.policy_id and f.busi_item_id = a.busi_item_id and f.ACCOUNT_TYPE=4) as CAPITAL_BALANCE,
 (select t.balance_ba  from APP___PAS__DBUSER.T_FUND_SETTLEMENT t where t.invest_id = a.list_id and t.settle_date = (select max(fs.SETTLE_DATE) from APP___PAS__DBUSER.T_FUND_SETTLEMENT fs where fs.invest_id = a.list_id)) as BALANCE_BA,
 (select t.interest_rate from APP___PAS__DBUSER.T_FUND_SETTLEMENT t where t.invest_id = a.list_id and t.settle_date = (select max(fs.SETTLE_DATE) from APP___PAS__DBUSER.T_FUND_SETTLEMENT fs where fs.invest_id = a.list_id)) as INTEREST_RATE,
 (select max(fs.SETTLE_DATE) from APP___PAS__DBUSER.T_FUND_SETTLEMENT fs where fs.invest_id = a.list_id)as balaDate ,
 (select t.interest from APP___PAS__DBUSER.T_FUND_SETTLEMENT t where t.invest_id = a.list_id and t.settle_date = (select max(fs.SETTLE_DATE) from APP___PAS__DBUSER.T_FUND_SETTLEMENT fs where fs.invest_id = a.list_id)) as INTEREST
  from APP___PAS__DBUSER.T_CONTRACT_INVEST a where 1=1]]>
  <if test="policy_code !=null"><![CDATA[and exists (select 1 from APP___PAS__DBUSER.T_contract_master tcm  where tcm.policy_code = trim(#{policy_code}) and tcm.policy_id = a.policy_id)]]></if>
  <if test="product_code_sys !=null"><![CDATA[and a.busi_item_id =(select b.busi_item_id from APP___PAS__DBUSER.T_contract_busi_prod b where b.busi_prd_id =(select business_prd_id
 from APP___PAS__DBUSER.T_business_product where product_code_sys = trim(#{product_code_sys})) and a.policy_id=b.policy_id )]]></if> 
	</select>
	<!--by zhaoyoan_wb 投连投资账户信息查询 -->
	<select id="PA_findAllRiskCodeAndAccnoAndRateByPolicyid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_ID,
			       A.BUSI_ITEM_ID,
			       B.BUSI_PRD_ID,
			       (SELECT OLD_POL_NO FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS OLD_POL_NO,
			       B.BUSI_PROD_CODE,
			       (SELECT PRODUCT_NAME_SYS
			          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT
			         WHERE B.BUSI_PRD_ID = BUSINESS_PRD_ID) AS product_name_sys,
			       A.ACCOUNT_CODE,
			       (SELECT FUND_NAME FROM APP___PAS__DBUSER.T_FUND WHERE FUND_CODE = A.ACCOUNT_CODE) AS fund_name,
			       (SELECT SUM(NVL(ASSIGN_RATE, 0))
			          FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_RATE
			         WHERE POLICY_ID = A.POLICY_ID
			           AND BUSI_ITEM_ID = A.BUSI_ITEM_ID) AS assign_rate
			  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A
			  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
			    ON B.POLICY_ID = A.POLICY_ID
			   AND B.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			 WHERE B.POLICY_CODE = #{policy_code} 
			 GROUP BY A.POLICY_ID,
			          A.BUSI_ITEM_ID,
			          A.ACCOUNT_CODE,
			          B.BUSI_PRD_ID,
			          B.BUSI_PROD_CODE
		]]>
	</select>
	<!-- 根据保单id查询所有账户信息 -->
	<select id="PA_findAllByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM APP___PAS__DBUSER.T_CONTRACT_INVEST 
			WHERE POLICY_ID=#{policy_id}
		]]>
	</select>

	<!-- 根据保单号码、计算日期起止、保险账户号码查询投连保险账户信息  Modify By Terence 2016-8-23 16:55:38 Fix TC defect 9883-->
	<select id="PA_findContractInvestListBySettleDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT　* FROM 
		(SELECT  POLICY_ID,BUSI_ITEM_ID,ITEM_ID,ACCOUNT_CODE,account_value,LIST_ID,SETTLE_DATE,INSERT_TIME,ACCUM_UNITS,INSURANCE_ACCOUNT_NAME,TOTAL_PREM,count_date,pricing_date,INVEST_ACCOUNT_TYPE
		       FROM (SELECT INVEST.POLICY_ID,
					 INVEST.BUSI_ITEM_ID,
					 INVEST.ITEM_ID,
			         INVEST.ACCOUNT_CODE,
                     INVEST.INTEREST_CAPITAL as account_value,
			         (SELECT TRANS_ID FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=INVEST.POLICY_ID AND DEAL_TIME =(SELECT MAX(DEAL_TIME) FROM APP___PAS__DBUSER.T_FUND_TRANS
                     WHERE DEAL_TIME <=INVEST.SETTLE_DUE_DATE AND POLICY_ID=INVEST.POLICY_ID)AND ROWNUM=1)LIST_ID,
			         SETTLE.SETTLE_DATE,
			         SETTLE.INSERT_TIME,
			         INVEST.ACCUM_UNITS,
			         IAF.INSURANCE_ACCOUNT_NAME,
			         INVEST.TOTAL_PREM,
			         SETTLE.LAST_SETTLE_DATE as count_date,
			         null as pricing_date,
			         INVEST.INVEST_ACCOUNT_TYPE
				FROM APP___PAS__DBUSER.T_CONTRACT_INVEST        INVEST,
				       APP___PAS__DBUSER.T_FUND_SETTLEMENT        SETTLE,
				       APP___PAS__DBUSER.T_INSURANCE_ACCOUNT_FLAG IAF
				WHERE INVEST.POLICY_ID =  #{policy_id}
				   	AND INVEST.LIST_ID = SETTLE.INVEST_ID
				   	AND INVEST.INVEST_ACCOUNT_TYPE='2'
				   	AND INVEST.INVEST_ACCOUNT_TYPE = IAF.INSURANCE_ACCOUNT_CODE]]>
				<if test=" start_date != null and start_date != '' and end_date != null and end_date != ''">
			<![CDATA[  AND SETTLE.SETTLE_DATE >= #{start_date}]]>
		    <![CDATA[  AND SETTLE.SETTLE_DATE <= #{end_date}]]>
		</if>
 		<if test=" account_code != null and account_code != ''">
			<![CDATA[  AND INVEST.ACCOUNT_CODE = #{account_code}]]>
		</if>					   	
		<![CDATA[)]]>		
		<![CDATA[ 
        UNION 
        SELECT POLICY_ID,BUSI_ITEM_ID,ITEM_ID,ACCOUNT_CODE,account_value,LIST_ID,SETTLE_DATE,INSERT_TIME,ACCUM_UNITS,INSURANCE_ACCOUNT_NAME,TOTAL_PREM,count_date,pricing_date,INVEST_ACCOUNT_TYPE
		FROM  (SELECT INVEST.POLICY_ID,
               INVEST.BUSI_ITEM_ID,
               INVEST.ITEM_ID,
               INVEST.ACCOUNT_CODE,
              (SELECT round(BID_PRICE * INVEST.ACCUM_UNITS,2) FROM(
                SELECT BID_PRICE, ROWNUM AS RN
                       FROM (SELECT TUP.BID_PRICE, ROWNUM AS RN
                       FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
                    INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                       ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
                         WHERE 1 = 1 AND  CI.POLICY_ID = #{policy_id}
         ORDER BY TUP.PRICING_DATE DESC) C) D
                    WHERE D.RN = 1) account_value,
               (SELECT TRANS_ID FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=INVEST.POLICY_ID AND DEAL_TIME =(SELECT MAX(DEAL_TIME) FROM APP___PAS__DBUSER.T_FUND_TRANS
               WHERE  POLICY_ID=INVEST.POLICY_ID)AND ROWNUM=1)LIST_ID,
               (SELECT T.DEAL_TIME FROM APP___PAS__DBUSER.T_FUND_TRANS T WHERE T.POLICY_ID=INVEST.POLICY_ID AND DEAL_TIME =(SELECT MAX(DEAL_TIME) FROM APP___PAS__DBUSER.T_FUND_TRANS
               WHERE  POLICY_ID=INVEST.POLICY_ID)AND ROWNUM=1) AS SETTLE_DATE,
               (SELECT MIN(INSERT_TIME)FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE policy_id=INVEST.POLICY_ID )INSERT_TIME,
               INVEST.ACCUM_UNITS,
               (SELECT IAF.INVEST_ACCOUNT_NAME FROM  APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO IAF WHERE INVEST.ACCOUNT_CODE = IAF.INVEST_ACCOUNT_CODE)INSURANCE_ACCOUNT_NAME,
               INVEST.TOTAL_PREM,
                null as count_date,
			(SELECT pricing_date FROM  (
                  select ipd.pricing_date
             from dev_pas.T_INVEST_unit_PRICE ipd
             INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI  
            ON ipd.invest_account_code=CI.Account_Code
             INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM 
            ON  CI.POLICY_ID=CM.POLICY_ID
            WHERE CM.POLICY_ID= #{policy_id}
            order by ipd.pricing_date desc
           ) S WHERE ROWNUM = 1) as pricing_date,
               INVEST.INVEST_ACCOUNT_TYPE
         FROM APP___PAS__DBUSER.T_CONTRACT_INVEST   INVEST
         WHERE INVEST.POLICY_ID = #{policy_id} AND INVEST.INVEST_ACCOUNT_TYPE='1'
 		]]>
 		<if test=" start_date != null and start_date != '' and end_date != null and end_date != ''">
			<![CDATA[  AND INVEST.SETTLE_DUE_DATE >= #{start_date}]]>
		    <![CDATA[  AND INVEST.SETTLE_DUE_DATE <= #{end_date}]]>
		</if>
 		<if test=" account_code != null and account_code != ''">
			<![CDATA[  AND INVEST.ACCOUNT_CODE = #{account_code}]]>
		</if>			
		<![CDATA[))]]>	
		<if test=" tOrder != null and tOrder != ''">
			${tOrder}
		</if>	
 		<if test=" tOrder2 != null and tOrder2 != ''">
			${tOrder2}
		</if>	
	</select>
	<!--  投连保单信息查询 -->
	<select id="queryInvestPolicyInfoQuery" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[   
		SELECT * FROM (SELECT ROWNUM RN,RES.* FROM (
			SELECT A.POLICY_ID, A.POLICY_CODE , C.BUSI_PROD_CODE,C.OLD_POL_NO,B.TRANS_TYPE,D.PRODUCT_NAME_SYS,
			(SELECT SUM(TOTAL_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE BUSI_ITEM_ID = C.BUSI_ITEM_ID) prem,
			C.BUSI_ITEM_ID,B.TRANS_AMOUNT ,B.TRANS_CODE ,C.VALIDATE_DATE,B.DEAL_TIME ,E.INVEST_ACCOUNT_TYPE ,E.ACCOUNT_CODE       
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
			LEFT JOIN APP___PAS__DBUSER.T_FUND_TRANS B ON A.POLICY_ID = B.POLICY_ID
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C ON A.POLICY_CODE = C.POLICY_CODE AND B.BUSI_ITEM_ID = C.BUSI_ITEM_ID
			LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT D ON C.BUSI_PRD_ID = D.BUSINESS_PRD_ID
			LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST E ON A.POLICY_ID= E.POLICY_ID AND  B.BUSI_ITEM_ID=E.BUSI_ITEM_ID
			WHERE A.POLICY_CODE = #{policy_code}
		) RES) WHERE RN>=#{row_num_start} AND RN<#{row_num_end}
	]]>
	</select>
	 
	<!-- 投连保单期初账户单位价格卖出  -->
	<select id="queryByCalBidPriceByDealDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT   CAL_BID_PRICE AS LAST_UNIT_PRICE FROM (SELECT TI.PRICING_DATE,TI.CAL_BID_PRICE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE TI WHERE TI.INVEST_ACCOUNT_CODE=#{account_code} AND  TI.PRICING_DATE<#{deal_time}
         ORDER BY TI.PRICING_DATE DESC) WHERE ROWNUM=1
		]]>
	</select>
	
	<!-- 投连保单期初账户单位价格买入 -->
	<select id="queryByCalOffPriceByDealDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT   CAL_OFF_PRICE AS LAST_UNIT_PRICE FROM (SELECT TI.PRICING_DATE,TI.CAL_OFF_PRICE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE TI  WHERE TI.INVEST_ACCOUNT_CODE=#{account_code} AND TI.PRICING_DATE<#{deal_time}
         ORDER BY TI.PRICING_DATE DESC) WHERE ROWNUM=1
		]]>
	</select>
	
	
	<!-- 根据保单id查询所有账户信息 -->
	<select id="queryUniversalNBAcount" resultType="java.util.Map" parameterType="java.util.Map">
	<!--   	<![CDATA[
             SELECT  GREATEST((SELECT ACCUM_UNITS  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE ITEM_ID=TCP.ITEM_ID AND ROWNUM=1),1)ACCUM_UNITS,
             TCP.AMOUNT/GREATEST((SELECT ACCUM_UNITS  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE ITEM_ID=TCP.ITEM_ID AND ROWNUM=1),1) AS PERMULTAMOUNT,
             TCP.PREM_FREQ,
             TCP.CHARGE_YEAR,
             TCBP.BUSI_PROD_CODE,
             (SELECT SUM(TRANS_AMOUNT)  FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND  ITEM_ID=TCP.ITEM_ID AND TRANS_CODE IN ('11','27','29','35','36','37') AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE  POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID))STD_PREM_AF,
             (SELECT SUM(TRANS_AMOUNT)FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE ITEM_ID=TCP.ITEM_ID AND POLICY_ID=TCP.POLICY_ID AND  ITEM_ID=TCP.ITEM_ID AND TRANS_CODE IN ('35','36','37') AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID))/(SELECT SUM(TRANS_AMOUNT)  FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND TRANS_CODE IN ('11','27','29','35','36','37')  AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID)) * 100 AS INITRATE,
             (SELECT SUM(TRANS_AMOUNT)FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE ITEM_ID=TCP.ITEM_ID AND POLICY_ID=TCP.POLICY_ID    AND TRANS_CODE IN ('35','36','37') AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE  POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID)) AS INITDEDUCTMONEY,
             100*TCBP.GURNT_RATE||'%' AS RATE,      
             ((SELECT SUM(TRANS_AMOUNT)  FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID  AND TRANS_CODE IN ('11','27','29','35','36','37') AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID ))- (SELECT SUM(TRANS_AMOUNT)FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE ITEM_ID=TCP.ITEM_ID AND POLICY_ID=TCP.POLICY_ID AND TRANS_CODE IN ('35','36','37') AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID)))AS INITCONTVALUE,
             TCBP.VALIDATE_DATE
             FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP WHERE   TCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID  AND TCP.POLICY_CODE=#{policy_code}       
             AND TCP.BUSI_ITEM_ID IN(SELECT T1.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T1,APP___PAS__DBUSER.T_BUSINESS_PRODUCT T2 WHERE T1.BUSI_PRD_ID=T2.BUSINESS_PRD_ID AND T2.PRODUCT_CATEGORY1='20003')
		]]>  -->
		<![CDATA[  SELECT  GREATEST((SELECT ACCUM_UNITS  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE ITEM_ID=TCP.ITEM_ID AND ROWNUM=1),1)ACCUM_UNITS,
             TCP.POLICY_CODE,
             TCP.AMOUNT/GREATEST((SELECT ACCUM_UNITS  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE ITEM_ID=TCP.ITEM_ID AND ROWNUM=1),1) AS PERMULTAMOUNT,
             
            (SELECT B.PREM_FREQ FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, APP___PAS__DBUSER.T_CONTRACT_PRODUCT B WHERE
             A.POLICY_CODE=#{policy_code}  AND  A.MASTER_BUSI_ITEM_ID IS NULL AND
              B.BUSI_ITEM_ID=A.BUSI_ITEM_ID ) AS PREM_FREQ,
             TCP.CHARGE_YEAR,
             TCBP.BUSI_PROD_CODE,
            (SELECT B.TOTAL_PREM_AF FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,APP___PAS__DBUSER.T_CONTRACT_PRODUCT B WHERE  A.POLICY_CODE=#{policy_code}  AND  A.MASTER_BUSI_ITEM_ID IS NULL AND
              B.BUSI_ITEM_ID=A.BUSI_ITEM_ID ) AS STD_PREM_AF,
             (SELECT SUM(TRANS_AMOUNT)FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE ITEM_ID=TCP.ITEM_ID AND POLICY_ID=TCP.POLICY_ID AND  
             ITEM_ID=TCP.ITEM_ID AND TRANS_CODE IN ('35','36','37') AND DEAL_TIME=(SELECT MIN(DEAL_TIME) 
             FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID))/(SELECT SUM(TRANS_AMOUNT)  
             FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND TRANS_CODE IN ('11','27','29','35','36','37')  
             AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID)) * 100 AS INITRATE,
             (SELECT SUM(TRANS_AMOUNT)FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE ITEM_ID=TCP.ITEM_ID AND POLICY_ID=TCP.POLICY_ID AND TRANS_CODE IN ('35','36','37') 
             AND DEAL_TIME=(SELECT MIN(DEAL_TIME) 
             FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE  POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID)) AS INITDEDUCTMONEY,
             100*TCBP.GURNT_RATE||'%' AS RATE,      
             ((SELECT SUM(TRANS_AMOUNT)  FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID  
             AND TRANS_CODE IN ('11','27','29','35','36','37') AND DEAL_TIME=(SELECT MIN(DEAL_TIME) FROM  
             APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID ))-
             (SELECT SUM(TRANS_AMOUNT)FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE ITEM_ID=TCP.ITEM_ID 
             AND POLICY_ID=TCP.POLICY_ID AND TRANS_CODE IN ('35','36','37') AND DEAL_TIME=
             (SELECT MIN(DEAL_TIME) FROM  APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=TCP.POLICY_ID AND ITEM_ID=TCP.ITEM_ID)))AS INITCONTVALUE,
             TCBP.VALIDATE_DATE
             FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP 
             WHERE  TCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID  AND TCP.POLICY_CODE=#{policy_code}      
             AND TCP.BUSI_ITEM_ID IN(SELECT T1.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T1,
             APP___PAS__DBUSER.T_BUSINESS_PRODUCT T2 WHERE T1.BUSI_PRD_ID=T2.BUSINESS_PRD_ID AND T2.PRODUCT_CATEGORY1='20003')
		 
		 ]]>
		<if test="orderflag!=null and orderflag!=''"><![CDATA[ ${orderflag} ]]> </if>
		
	</select>
	<!-- 附加险万能险新契约账户价值查询-->
	<select id="queryAccountWorth" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
             SELECT  GREATEST((SELECT ACCUM_UNITS  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE ITEM_ID=T.ITEM_ID AND ROWNUM=1),1)ACCUM_UNITS,
             TCP.AMOUNT/GREATEST((SELECT ACCUM_UNITS  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST WHERE ITEM_ID=T.ITEM_ID AND ROWNUM=1),1) AS PERMULTAMOUNT,
             TCP.PREM_FREQ,
             TCP.INITIAL_DISCNT_PREM_AF as STD_PREM_AF ,
             (TCP.INITIAL_DISCNT_PREM_AF-T.TRANS_AMOUNT)/TCP.INITIAL_DISCNT_PREM_AF*100||'%' AS INITRATE,
             (TCP.INITIAL_DISCNT_PREM_AF-T.TRANS_AMOUNT) AS INITDEDUCTMONEY,
             100*TCBP.GURNT_RATE||'%' AS RATE,
             T.TRANS_AMOUNT AS INITCONTVALUE,
             TCBP.VALIDATE_DATE
             FROM APP___PAS__DBUSER.T_FUND_TRANS T,APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP WHERE T.ITEM_ID=TCP.ITEM_ID AND TCP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID  ]]>
        <if test="policy_code!=null and policy_code!=''"><![CDATA[ AND TCP.POLICY_CODE=#{policy_code} ]]> </if>
        <![CDATA[ 
             AND T.TRANS_CODE IN ('35','36','37') AND T.DEAL_TIME<=TCBP.VALIDATE_DATE
             AND T.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID
             AND TCP.BUSI_ITEM_ID IN(SELECT T1.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T1,APP___PAS__DBUSER.T_BUSINESS_PRODUCT T2 WHERE T1.BUSI_PRD_ID=T2.BUSINESS_PRD_ID AND T2.PRODUCT_CATEGORY1='20003')]]> 
        <if test="busi_prod_code!=null and busi_prod_code!=''"><![CDATA[  AND TCBP.BUSI_PROD_CODE=#{busi_prod_code} ]]> </if>     
	</select>
	
<!-- i添财投资连结信息查询，当日年化收益率、当日收益、累计收益-->
<select id="PA_queryRenturnRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select max(m.PRICING_DATE) as PRICING_DATE　　from APP___PAS__DBUSER.T_INVEST_UNIT_PRICE m, APP___PAS__DBUSER.T_CONTRACT_INVEST inv
　　　 where m.invest_account_code = inv.ACCOUNT_CODE　　 and inv.policy_id =　 (select ma.policy_id　  from APP___PAS__DBUSER.T_CONTRACT_MASTER ma　  where ma.policy_code = #{policy_code}) 
		]]>
	</select>
	
	<!-- 万能保单最新月度结算信息接口 根据保单号查询  -->
	<select id="queryInvestPolicyQueryPOByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	
	
		<if test="mark  != null and mark =='bigDate' ">
			<![CDATA[ 
	             select
				 (select max(a.settle_date)
				    from dev_pas.t_fund_settlement a
				   where a.invest_id = (select list_id
				                          from dev_pas.t_contract_invest
				                         where policy_id = #{policy_id})) as balaDate,
				 
				 (select list_id
				    from dev_pas.t_contract_invest
				   where policy_id = #{policy_id}) as invest_id
				
				  from dual      
			 ]]>
		</if>
		
		<if test="mark  != null and mark =='smallDate' ">
			<![CDATA[ 
				select max(a.settle_date) as samllBalaDate
 				from dev_pas.t_fund_settlement a
				where a.invest_id = (select list_id
                               from dev_pas.t_contract_invest 
                              where policy_id =  #{policy_id}
                      )
       			and a.settle_date< #{settle_date}
			 ]]>
		</if>
	
		
	</select>
	
	<!-- 根据保单id查询万能险账户信息 -->
	<select id="PA_findUniversalAccountInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.*,
			(SELECT SUM(NVL(TOTAL_PREM_AF,0)+NVL(ADDITIONAL_PREM_AF,0)) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS sum_prem,
			(SELECT SUM(NVL(AMOUNT,0)) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS sum_amnt, 
			a.total_prem AS sum_prem_new,
			(SELECT BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS busi_prod_code, 
			(select PRODUCT_NAME_SYS
           from dev_pas.t_business_product  where BUSINESS_PRD_ID =
            (SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID)) AS busi_prod_name, 
			(SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS busi_prd_id, 
			(SELECT FROZEN_AMOUNT FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS frozen_amount,
			 (SELECT sum(a1.trans_amount)
		          FROM APP___PAS__DBUSER.T_FUND_TRANS a1
		         where a1.POLICY_ID = a.policy_id
		           and a1.trans_code = '08') as pg_prem,
		       
		       (select sum(a2.trans_amount)
		          from dev_pas.t_fund_trans a2
		         where a2.policy_id = a.product_id
		           and a2.trans_type = '3'
		           and a2.trans_code not in ('03', '04', '41')
		           and a2.deal_time < #{end_deal_time}
		           and a2.deal_time > #{start_deal_time}
		           ) as deduct_prem,
		       
		       (select sum(a3.trans_amount)
		          from dev_pas.t_fund_trans a3
		         where a3.policy_id = a.policy_id
		           and a3.trans_type = '2'
		           and a3.deal_time < #{end_deal_time}
		           and a3.deal_time > #{start_deal_time}
		        
		        ) as incracc_prem,
		        
		        (
		          select sum(a4.trans_amount)
		          from dev_pas.t_fund_trans a4
		         where a4.policy_id = a.policy_id
		           and a4.trans_type = '2'
		        ) as incracc_sumprem ,
		        
		        (
		         select sum(a5.trans_amount)
		          from dev_pas.t_fund_trans a5
		         where a5.policy_id = a.policy_id
		           and a5.trans_type = '1'
		        )as sum_paym ,
		        
		         (select a6.master_busi_item_id
		          from dev_pas.t_contract_busi_prod a6
		         where a6.busi_item_id = a.busi_item_id) as master_busi , 
			 	(SELECT A7.OLD_POL_NO 
                   FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A7 
                  WHERE A7.BUSI_ITEM_ID = A.BUSI_ITEM_ID ) AS OLD_POL_NO 
			FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A
			WHERE POLICY_ID=#{policy_id}
			AND EXISTS(SELECT 1 FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT WHERE PRODUCT_CATEGORY1='20003' AND BUSINESS_PRD_ID=
			  (SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID )) 
		]]>
		<if test=" order_context != null and order_context != ''  "><![CDATA[
			${order_context}
		]]></if>
	</select>
	
	<!-- 查询账户信息和账户名称 -->
	<select id="PA_findInvestAndFundName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_PREM,A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.ITEM_ID, A.INVEST_ACCOUNT_TYPE, 
			A.ACCOUNT_CODE, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			 A.LIST_ID, A.SETTLEMENT_RATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.NEGATIVE_TIME,A.INSERT_TIME,B.FUND_NAME 
			FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A JOIN APP___PAS__DBUSER.T_FUND B ON A.ACCOUNT_CODE = B.FUND_CODE WHERE 1=1  ]]>
	    <include refid="PA_contractInvestWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>	
	
	<!-- 追回修改操作 -->
	<update id="PA_updateRGContractInvest" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST ]]>
		<set>
		<trim suffixOverrides=",">
			TOTAL_PREM = #{total_prem, jdbcType=NUMERIC},
		    ACCUM_UNITS = #{accum_units, jdbcType=NUMERIC} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_TYPE = #{invest_account_type, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,		    
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SETTLE_DUE_DATE = #{settle_due_date, jdbcType=DATE} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    SETTLEMENT_RATE = #{settlement_rate, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    NEGATIVE_TIME = #{negative_time, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	
	<!-- 投连万能账户信息详情查询万能险账户listid -->
	<select id="PA_findAllContractInvestForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			     SELECT TCI.LIST_ID,
			            TCI.POLICY_ID, /*保单ID*/
			            TCBP.BUSI_PROD_CODE, /*险种代码*/
			            TCI.ACCOUNT_CODE, /*账户编码*/
			            TCM.POLICY_CODE /*保单号*/
			       FROM APP___PAS__DBUSER.T_CONTRACT_INVEST TCI
			      INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
			         ON TCBP.BUSI_ITEM_ID = TCI.BUSI_ITEM_ID
			        AND TCBP.POLICY_ID = TCI.POLICY_ID
			      INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
			         ON TCI.POLICY_ID = TCM.POLICY_ID
			        AND TCBP.POLICY_ID = TCM.POLICY_ID
			      WHERE 1 = 1
			        AND TCBP.BUSI_PROD_CODE IN ('********','********') 
			        AND TCM.POLICY_CODE = #{policy_code}  ]]> 
		<if test=" account_code != null and account_code != ''"> <![CDATA[ AND TCI.ACCOUNT_CODE = #{account_code}]]> </if>	      
	</select>
	
	<!-- 万能投连账户信息查询接口-查询万能险账户信息 -->
	<select id="PA_findContractInvestDescForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			      SELECT AP.POLICY_ID, /*保单ID*/
                         BP.BUSI_PROD_CODE, /*险种代码*/
                         P.PRODUCT_NAME_SYS AS BUSI_PROD_NAME, /*险种代码名称*/
                         TC.POLICY_CODE, /*保单号*/
                         MIN(AP.CREATE_DATE) CREATE_DATE, /*账户成立日期*/
                         SUM(AP.INTEREST_CAPITAL) AS ACCOUNT_VALUE, /*账户价值*/
                         AP.INVEST_ACCOUNT_TYPE /*账户类型*/
                    FROM DEV_PAS.T_CONTRACT_INVEST AP
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
                      ON BP.BUSI_ITEM_ID = AP.BUSI_ITEM_ID
                     AND BP.POLICY_ID = AP.POLICY_ID
                   INNER JOIN DEV_PAS.T_CONTRACT_MASTER TC
                      ON AP.POLICY_ID = TC.POLICY_ID
                     AND BP.POLICY_ID = TC.POLICY_ID
                   INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT P
                      ON BP.BUSI_PROD_CODE = P.PRODUCT_CODE_SYS
                   WHERE 1 = 1
                     AND (BP.BUSI_PROD_CODE IN ('********','********','00Z01000','00Z01100') OR P.TAX_EXTENSION_FLAG = '1')
                     AND TC.POLICY_CODE =  #{policy_code}  /*保单号*/
                     AND BP.BUSI_ITEM_ID = #{busi_item_id}
                   GROUP BY AP.POLICY_ID,
                            BP.BUSI_PROD_CODE,
                            P.PRODUCT_NAME_SYS,
                            TC.POLICY_CODE,
                            AP.INVEST_ACCOUNT_TYPE  ]]> 
	</select>
	
	<!-- 万能投连账户信息查询接口-查询非928账户详细信息 -->
	<select id="PA_findAllContractInvestDetailForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				    SELECT ROWNUM RN,
				           AP.LIST_ID AS ACCOUNT_ID,
				           TC.POLICY_CODE,
				           AP.INVEST_ACCOUNT_TYPE,
				           AP.ACCOUNT_CODE,  /*投资组合账户编号*/
				           BP.BUSI_PROD_CODE,
				           (SELECT TT.PRODUCT_ABBR_NAME
				              FROM DEV_PDS.T_BUSINESS_PRODUCT TT
				              LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD DD
				                ON TT.BUSINESS_PRD_ID = DD.BUSI_PRD_ID
				             WHERE DD.BUSI_ITEM_ID = AP.BUSI_ITEM_ID) AS BUSI_PROD_NAME, /*投资组合名称*/
				           AP.POLICY_ID,
				           AP.ITEM_ID PRODUCT_CODE, 
				           (SELECT DEAL_TIME
				              FROM (SELECT FS.SETTLE_DATE AS DEAL_TIME, FS.INVEST_ID
				                      FROM DEV_PAS.T_FUND_SETTLEMENT FS
				                     INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
				                        ON FS.INVEST_ID = CI.LIST_ID
				                     INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
				                        ON CI.POLICY_ID = CM.POLICY_ID
				                     WHERE CM.POLICY_CODE =  #{policy_code}
				                     ORDER BY FS.SETTLE_DATE DESC) B
				             WHERE ROWNUM = 1) COUNT_DATE,  /*上一结算日期*/
				           AP.CREATE_DATE ACCOUNT_DATE, /*账户成立日期*/
				           (              
           SELECT PRICING_DATE FROM  (
                  SELECT IPD.PRICING_DATE
             FROM DEV_PAS.T_INVEST_UNIT_PRICE IPD
             INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI  
            ON IPD.INVEST_ACCOUNT_CODE=CI.ACCOUNT_CODE
             INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM 
            ON  CI.POLICY_ID=CM.POLICY_ID
            WHERE CM.POLICY_CODE=  #{policy_code}
            ORDER BY IPD.PRICING_DATE DESC
           ) S WHERE ROWNUM = 1
       ) as pricing_date,
				           (CASE AP.INVEST_ACCOUNT_TYPE
				             WHEN 1 THEN
				              (SELECT ROUND(BID_PRICE * AP.ACCUM_UNITS,2)
				                 FROM (SELECT BID_PRICE, ROWNUM AS RN
                                     FROM (SELECT TUP.BID_PRICE
                                         FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
                                        INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                                           ON TUP.INVEST_ACCOUNT_CODE =
                                              CI.ACCOUNT_CODE
                                        INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
                                           ON CM.POLICY_ID = CI.POLICY_ID
                                        WHERE CM.POLICY_CODE =  #{policy_code}
                                        ORDER BY TUP.PRICING_DATE DESC) C) D
                        WHERE D.RN = 1)
				             WHEN 2 THEN
				              AP.INTEREST_CAPITAL  
				           END) ACCOUNT_VALUE,  /*账户价值*/
				           AP.ACCUM_UNITS UNIT_NUMBER,
				           (CASE AP.INVEST_ACCOUNT_TYPE
				             WHEN 1 THEN
				              (SELECT BID_PRICE
				                 FROM (SELECT BID_PRICE, ROWNUM AS RN
				                         FROM (SELECT TUP.BID_PRICE, ROWNUM AS RN
				                                 FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
				                                INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
				                                   ON TUP.INVEST_ACCOUNT_CODE =
				                                      CI.ACCOUNT_CODE
				                                INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
				                                   ON CM.POLICY_ID = CI.POLICY_ID
				                                WHERE CM.POLICY_CODE =  #{policy_code}
				                                ORDER BY TUP.PRICING_DATE DESC) C) D
				                WHERE D.RN = 1)
				             WHEN 2 THEN
				              NULL
				           END) UNIT_PRICE,
				           (SELECT PL.PRODUCT_NAME
				              FROM DEV_PDS.T_PRODUCT_LIFE PL
				              LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
				                ON CP.PRODUCT_ID = PL.PRODUCT_ID
				             WHERE CP.ITEM_ID = AP.ITEM_ID) PRODUCT_NAME   /*险种名称*/
				      FROM DEV_PAS.T_CONTRACT_INVEST AP
				     INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
				        ON BP.BUSI_ITEM_ID = AP.BUSI_ITEM_ID
				       AND BP.POLICY_ID = AP.POLICY_ID
				      LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TC
				        ON AP.POLICY_ID = TC.POLICY_ID
				       AND BP.POLICY_ID = TC.POLICY_ID
				     WHERE 1 = 1
				       AND (AP.INVEST_ACCOUNT_TYPE = 1 OR AP.INVEST_ACCOUNT_TYPE = 2)
				       AND TC.POLICY_CODE =  #{policy_code}
				       AND BP.BUSI_ITEM_ID = #{busi_item_id}
				       AND (BP.BUSI_PROD_CODE NOT IN ('********','********','00Z01000','00Z01100') 
				       AND NOT EXISTS (SELECT  'X' from DEV_PDS.T_BUSINESS_PRODUCT P WHERE P.PRODUCT_CODE_SYS = BP.BUSI_PROD_CODE AND P.TAX_EXTENSION_FLAG = '1'))
				     ORDER BY AP.CREATE_DATE DESC  ]]> 
	</select>
	
	<!-- 万能投连账户信息查询接口-查询万能险928账户详细信息 -->
	<select id="PA_findAll928ContractInvestDetailForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			     SELECT AP.LIST_ID,
			            AP.ACCOUNT_CODE,
			            AP.POLICY_ID, /*保单ID*/
			            BP.BUSI_PROD_CODE, /*险种代码*/
			            FD.FUND_NAME, /*投资组合名称*/
			            P.PRODUCT_NAME_SYS AS BUSI_PROD_NAME, /*险种代码名称*/
			            TC.POLICY_CODE, /*保单号*/
			            AP.CREATE_DATE, /*账户成立日期*/
			            AP.INTEREST_CAPITAL, /*账户价值*/
			            (SELECT DEAL_TIME
			               FROM (SELECT FS.SETTLE_DATE AS DEAL_TIME, FS.INVEST_ID
			                       FROM DEV_PAS.T_FUND_SETTLEMENT FS
			                      INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			                         ON FS.INVEST_ID = CI.LIST_ID
			                      INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
			                         ON CI.POLICY_ID = CM.POLICY_ID
			                      WHERE CM.POLICY_CODE = #{policy_code}
			                      ORDER BY FS.SETTLE_DATE DESC) B
			              WHERE ROWNUM = 1) COUNT_DATE /*上一结算日期*/
			       FROM DEV_PAS.T_CONTRACT_INVEST AP
			      INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
			         ON BP.BUSI_ITEM_ID = AP.BUSI_ITEM_ID
			        AND BP.POLICY_ID = AP.POLICY_ID
			      INNER JOIN DEV_PAS.T_CONTRACT_MASTER TC
			         ON AP.POLICY_ID = TC.POLICY_ID
			        AND BP.POLICY_ID = TC.POLICY_ID
			      INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT P
			         ON BP.BUSI_PROD_CODE = P.PRODUCT_CODE_SYS
			      INNER JOIN DEV_PDS.T_FUND FD
			         ON AP.ACCOUNT_CODE = FD.FUND_CODE
			      WHERE 1 = 1
			        AND TC.POLICY_CODE = #{policy_code} /*保单号*/
			        AND BP.BUSI_PROD_CODE IN ('********','********','00Z01000','00Z01100') ]]>  
	</select>
	
	<!-- 投连万能账户信息详情查询接口-查询万能险账户详细信息 -->
	<select id="PA_findAllWNXContractInvestForGw" resultType="java.util.Map" parameterType="java.util.Map">
	<if test=" flag != null and flag != '' and flag == '0'.toString() "> 	
		<![CDATA[
			    SELECT ROWNUM RN,(SELECT A.DESCRIPTION
				          FROM APP___PAS__DBUSER.T_TRANSACTION_CODE A
				         WHERE A.TRANS_CODE = C.TRANS_CODE) AS TRANS_CODE_NAME,
				       C.*
				  FROM (SELECT '42' AS TRANS_CODE,
				               B.SETTLE_DATE AS DEAL_TIME,
				               ROUND(B.INTEREST, 2) AS TRANS_AMOUNT,
				               B.INTEREST_RATE AS TRANS_PROPORTION,
				               ROUND(B.BALANCE, 2) AS TRANS_INTEREST,
				               NULL AS UNIT_NUMBER,
				               NULL AS UNIT_PRICE,
				               B.LIST_ID AS LIST_ID,
				               B.SETTLEMENT_ID AS TRANS_ID,
                               2 AS TRANS_TYPE,
                               (SELECT TCB.BUSI_PROD_CODE
                                   FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                   WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS BUSI_PROD_CODE, /*险种代码*/
                               (SELECT B.PRODUCT_ABBR_NAME
                                   FROM DEV_PAS.T_BUSINESS_PRODUCT B
                                    WHERE B.PRODUCT_CODE_SYS =
                                      (SELECT TCB.BUSI_PROD_CODE
                                         FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                         WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID)) AS PRODUCT_ABBR_NAME,/*险种名称*/
                                B.GURNT_INTEREST_RATE  AS GURNT_INTEREST_RATE/*保证利率*/
				          FROM (SELECT CI.*, FS.*, ROWNUM AS RN
				                  FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT FS
				                 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST CI
				                    ON CI.LIST_ID = FS.INVEST_ID
				                 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
				                    ON TCM.POLICY_ID = CI.POLICY_ID
				                 WHERE 1 = 1
				                   AND TCM.POLICY_CODE = #{policy_code} ]]> 
		<if test=" account_code != null and account_code != ''"> <![CDATA[ AND CI.ACCOUNT_CODE = #{account_code}]]> </if>	
		<if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND FS.SETTLE_DATE >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
			   <if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND FS.SETTLE_DATE <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>	                    
		<![CDATA[              ) B
					 UNION ALL
				        SELECT B.TRANS_CODE     AS TRANS_CODE,
				               B.DEAL_TIME      AS DEAL_TIME,
				               ROUND(B.TRANS_AMOUNT, 2)   AS TRANS_AMOUNT,
				               B.TRANS_INTEREST AS TRANS_PROPORTION,
				               ROUND(B.BALANCE, 2)        AS TRANS_INTEREST,
				               B.TRANS_UNITS    AS UNIT_NUMBER,
				               B.TRANS_PRICE    AS UNIT_PRICE,
				               B.LIST_ID        AS LIST_ID,
				               B.TRANS_ID       AS TRANS_ID,
				               B.TRANS_TYPE AS TRANS_TYPE,
				               (SELECT TCB.BUSI_PROD_CODE
                                   FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                  WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID) AS BUSI_PROD_CODE, /*险种代码*/
                                (SELECT B.PRODUCT_ABBR_NAME
                                   FROM DEV_PAS.T_BUSINESS_PRODUCT B
                                     WHERE B.PRODUCT_CODE_SYS =
                                     (SELECT TCB.BUSI_PROD_CODE
                                        FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                        WHERE TCB.BUSI_ITEM_ID = B.BUSI_ITEM_ID)) AS PRODUCT_ABBR_NAME,/*险种名称*/
                                B.GURNT_INTEREST_RATE  AS GURNT_INTEREST_RATE/*保证利率*/
				          FROM (SELECT CI.*,
				                       TC.TRANS_CODE,
				                       TC.DESCRIPTION,
				                       FT.DEAL_TIME,
				                       FT.TRANS_AMOUNT,
				                       FT.TRANS_INTEREST,
				                       FT.TRANS_UNITS,
				                       FT.TRANS_PRICE,
				                       FT.BALANCE_UNITS_BF,
				                       (CASE FT.TRANS_TYPE
				                         WHEN 0 THEN
				                          FT.BALANCE_UNITS_BF
				                         WHEN 1 THEN
				                          FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
				                         WHEN 2 THEN
				                          FT.BALANCE_UNITS_BF + FT.TRANS_AMOUNT
				                         WHEN 3 THEN
				                          FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
				                       END) AS BALANCE,
				                       ROWNUM AS RN,
				                       FT.TRANS_ID AS TRANS_ID,
				                       FT.TRANS_TYPE AS TRANS_TYPE,
				                       (SELECT MAX(A.ANNUAL_INTEREST_RATE)
                                          FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
                                          WHERE A.FUND_CODE = FT.FUND_CODE
                                             AND A.INTEREST_RATE_TYPE = '1'
                                             AND A.INTEREST_START_DATE <= FT.DEAL_TIME
                                             AND A.INTEREST_END_DATE >= FT.DEAL_TIME) * 100 AS GURNT_INTEREST_RATE /*保证利率*/
				                  FROM APP___PAS__DBUSER.T_FUND_TRANS FT
				                 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST CI
				                    ON CI.LIST_ID = FT.LIST_ID
				                 INNER JOIN APP___PAS__DBUSER.T_TRANSACTION_CODE TC
				                    ON FT.TRANS_CODE = TC.TRANS_CODE
				                 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
				                    ON TCM.POLICY_ID = CI.POLICY_ID
				                 WHERE 1 = 1
				                   AND TCM.POLICY_CODE = #{policy_code} ]]> 
		<if test=" account_code != null and account_code != ''"> <![CDATA[ AND CI.ACCOUNT_CODE = #{account_code}]]> </if>
		<if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND FT.DEAL_TIME >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
			   <if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND FT.DEAL_TIME <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>		                    
		<![CDATA[		   ) B) C ]]> 
		<![CDATA[ ORDER BY C.DEAL_TIME DESC, C.TRANS_ID DESC ]]> 
		</if> 
		<if test=" flag != null and flag != '' and flag == '1'.toString() "> 	
		  <![CDATA[ SELECT ROWNUM RN,c.TRANS_CODE_NAME,
                   c.DEAL_TIME,
                   c.TRANS_AMOUNT,
                   '' as TRANS_PROPORTION,
                   c.TRANS_INTEREST,
                   c.UNIT_NUMBER,
                   c.UNIT_PRICE,
                   C.trans_id,
                   c.trans_code,
                   c.trans_type,
                   c.BUSI_PROD_CODE,
                   c.PRODUCT_ABBR_NAME,
                   '' as GURNT_INTEREST_RATE
              FROM (select A.TRANS_CODE_NAME, --变更类型
                           A.deal_time, -- 成交日期
                           CAST(ABS(A.trans_amount) as decimal(18, 2)) as trans_amount, -- 交易金额
                           CAST(A.TRANS_INTEREST as decimal(18, 2)) as TRANS_INTEREST, --账户价值
                           ROUND(A.UNIT_NUMBE, 6) as UNIT_NUMBER, -- 单位数
                           A.UNIT_PRICE, -- 单位价格
                           a.trans_id,
                           a.trans_code,
                           a.trans_type,
                           a.BUSI_PROD_CODE,
                           a.PRODUCT_ABBR_NAME
                           
                      from (select (select t.description
                                      from dev_pas.t_transaction_code t
                                     where t.trans_code = ft.trans_code) as TRANS_CODE_NAME, --变更类型
                                   ft.deal_time, -- 成交日期
                                   ft.trans_amount, -- 交易金额
                                   (case
                                     when ft.TRANS_TYPE = '1' then
                                      ft.balance_units_bf - ft.trans_amount
                                     when ft.TRANS_TYPE = '2' then
                                      ft.balance_units_bf + ft.trans_amount
                                     when ft.TRANS_TYPE = '3' then
                                      ft.balance_units_bf - ft.trans_amount
                                     else
                                      ft.balance_units_bf
                                   end) as TRANS_INTEREST, --账户价值
                                   (case
                                     when ft.trans_price = '0' then
                                      null
                                     else
                                      (case
                                        when ft.TRANS_TYPE = '1' then
                                         (ft.balance_units_bf - ft.trans_amount) /
                                         ft.trans_price
                                        when ft.TRANS_TYPE = '2' then
                                         (ft.balance_units_bf + ft.trans_amount) /
                                         ft.trans_price
                                        when ft.TRANS_TYPE = '3' then
                                         round(ft.balance_units_bf /
                                               ft.trans_price,
                                               6) - ft.trans_units
                                        else
                                         ft.balance_units_bf
                                      end)
                                   end) as UNIT_NUMBE, -- 单位数
                                   ft.trans_price as UNIT_PRICE, -- 单位价格
                                   ft.trans_id,
                                   ft.trans_code,
                                   ft.trans_type,
                                   (SELECT TCB.BUSI_PROD_CODE
                                      FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                       WHERE TCB.BUSI_ITEM_ID = ft.BUSI_ITEM_ID) AS BUSI_PROD_CODE, /*险种代码*/
                                   (SELECT B.PRODUCT_ABBR_NAME
                                      FROM DEV_PAS.T_BUSINESS_PRODUCT B
                                      WHERE B.PRODUCT_CODE_SYS =
                                        (SELECT TCB.BUSI_PROD_CODE
                                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                           WHERE TCB.BUSI_ITEM_ID = ft.BUSI_ITEM_ID)) AS PRODUCT_ABBR_NAME /*险种名称*/
                              from dev_pas.T_FUND_TRANS ft
                             WHERE 1 = 1
                               AND ft.list_id in
                                  
                                   (select a.list_id
                                      from dev_pas.t_contract_invest a
                                     where a.policy_id =
                                           (select m.policy_id
                                              from dev_pas.t_contract_master m
                                             where m.policy_code =
                                                  #{policy_code})) ]]>
        <if test=" account_code != null and account_code != ''"> <![CDATA[ AND ft.FUND_CODE = #{account_code}]]> </if>
		<if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND ft.deal_time >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
		<if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND ft.deal_time <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>		
                            
               <![CDATA[              UNION All
                            select '投连日计价' as TRANS_CODE_NAME, --变更类型
                                   ipd.price_date as deal_time, -- 成交日期
                                   (ipd.last_invest_unit -
                                   ipd.init_invest_unit) * ipd.bid_price as TRANS_AMOUNT, -- 交易金额
                                   ipd.curr_balance as TRANS_INTEREST, -- --账户价值
                                   ipd.last_invest_unit as UNIT_NUMBER, --单位数
                                   ipd.bid_price as UNIT_PRICE, --单位价格
                                   null trans_id,
                                   '00' trans_code,
                                   1 AS TRANS_TYPE,
                                    (SELECT TCB.BUSI_PROD_CODE
                                       FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                       WHERE substr(TCB.BUSI_PROD_CODE,3) = ipd.fund_code and rownum = 1) as BUSI_PROD_CODE,/*险种代码*/
                                    (SELECT B.PRODUCT_ABBR_NAME
                                       FROM DEV_PAS.T_BUSINESS_PRODUCT B
                                       WHERE B.PRODUCT_CODE_SYS =
                                           (SELECT TCB.BUSI_PROD_CODE
                                              FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCB
                                              WHERE substr(TCB.BUSI_PROD_CODE,3) = ipd.fund_code and rownum = 1)) AS PRODUCT_ABBR_NAME /*险种名称*/
                              from dev_pas.T_INVEST_PRICE_DAY ipd
                             WHERE 1 = 1
                               AND ipd.invest_id in
                                   (select a.list_id
                                      from dev_pas.t_contract_invest a
                                     where a.policy_id =
                                           (select m.policy_id
                                              from dev_pas.t_contract_master m
                                             where m.policy_code =
                                                  #{policy_code})) ]]>
    <if test=" account_code != null and account_code != ''"> <![CDATA[ AND ipd.FUND_CODE = #{account_code}]]> </if>
		<if test=" aichangestartdate  != null  and  aichangestartdate  != ''  "><![CDATA[ AND ipd.price_date >= to_date(#{aichangestartdate},'yyyy-MM-dd') ]]></if>
		<if test=" aichangeenddate  != null  and  aichangeenddate  != ''  "><![CDATA[ AND ipd.price_date <= to_date(#{aichangeenddate},'yyyy-MM-dd') ]]></if>		                                  
            <![CDATA[                                       ) A) C ]]>
          <![CDATA[    ORDER BY C.DEAL_TIME DESC, C.TRANS_ID DESC  ]]> 
		</if>
	</select>

	<!-- 移动保全2.0查询万能险账户 -->
	<select id="PA_findAllContractInvestForYdbq2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			        SELECT TCI.POLICY_ID, TCI.SETTLE_DUE_DATE,TBP.PRODUCT_CODE_SYS,TBP.PRODUCT_CATEGORY
					  FROM APP___PAS__DBUSER.T_CONTRACT_INVEST    TCI,
					       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
					       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
					 WHERE TCI.POLICY_ID = TCBP.POLICY_ID
					   AND TCI.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
					   AND TCBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
					   AND TBP.PRODUCT_CATEGORY1 IN ('20003')
					   AND TCI.POLICY_ID = #{policy_id}  ]]> 
	</select>
	
	<select id="queryNextSettleDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SETTLE_ACCOUNT_DAY AS settle_due_date
			 FROM (	SELECT A.SETTLE_PERIOD_START_DATE,
	               A.BUSINESS_PRD_ID,
	               A.DAILY_INTEREST_RATE,
	               A.SETTLE_PERIOD_END_DATE,
	               A.END_INTE_RATE_UNIT,
	               A.INTEREST_START_DATE,
	               A.END_INTE_RATE_VALUE,
	               A.INTEREST_END_DATE,
	               A.LIST_ID,
	               FUND_CODE,
	               A.DEL_FLAG,
	               A.INTEREST_RATE_TYPE,
	               A.ANNUAL_INTEREST_RATE,
	               A.SETTLE_ACCOUNT_DAY,
	               BP.PRODUCT_NAME_SYS,
	               BP.PRODUCT_CODE_SYS
	          FROM APP___PDS__DBUSER.T_UNIVERSAL_SETTLEMENT_RATE A
	          LEFT JOIN APP___PDS__DBUSER.T_BUSINESS_PRODUCT BP
	            ON A.BUSINESS_PRD_ID = BP.BUSINESS_PRD_ID
	         WHERE A.INTEREST_RATE_TYPE = 2
	           AND A.BUSINESS_PRD_ID = #{busi_prd_id}
	           AND A.FUND_CODE = #{account_code}
	          ]]> 
	          <if test=" start_date  != null "><![CDATA[ AND A.SETTLE_PERIOD_END_DATE > #{start_date} ]]></if>
	          <if test=" end_date  != null "><![CDATA[ AND A.SETTLE_PERIOD_END_DATE <= #{end_date} ]]></if>
          <![CDATA[           
        	 ORDER BY A.SETTLE_ACCOUNT_DAY  ASC) T WHERE ROWNUM=1
	     ]]>
	</select>
	
	<!-- 查询结息日期非空且等于该保单最大结息日期的账户数量 -->
	<select id="PA_findMaxSettleDueDateCountContractInvest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_ID, 
			       A.ACCOUNT_CODE, 
			       A.SETTLE_DUE_DATE
			  FROM DEV_PAS.T_CONTRACT_INVEST A
			 WHERE 1 = 1
			   AND A.POLICY_ID = #{policy_id}
			   AND A.LIST_ID != #{list_id} 
			   AND A.SETTLE_DUE_DATE IS NOT NULL
			   AND A.SETTLE_DUE_DATE =
			       (SELECT MAX(B.SETTLE_DUE_DATE)
			          FROM DEV_PAS.T_CONTRACT_INVEST B
			         WHERE B.POLICY_ID = #{policy_id})
	     ]]>
	</select>
	
	<!-- 查询保单账户价值 -->
	<select id="PA_findPolicyContractInvest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(INTEREST_CAPITAL) AS INTEREST_CAPITAL
			  FROM DEV_PAS.T_CONTRACT_INVEST A
			 WHERE A.POLICY_ID = #{policy_id}
	     ]]>
	</select>
	<!-- 根据保单号查询保单投资连结表所有数据 -->
	<select id="PA_findAllContractInvestByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select max(a.invest_account_type) as invest_account_type,
			       (select tcbp.busi_prod_code
			          from app___pas__dbuser.t_contract_busi_prod tcbp
			         where tcbp.busi_item_id = a.busi_item_id) as busi_prod_code
			  from app___pas__dbuser.t_contract_invest a
			  left join app___pas__dbuser.t_contract_master b
			    on a.policy_id = b.policy_id
			 where rownum <= 1000
			   and b.policy_code = #{policy_code}
			 group by a.busi_item_id
		]]> 
	</select>
	<!-- 根据保单号和险种代码查询保单投资连结表所有数据 -->
	<select id="PA_findAllContractInvestByPolicyCodeAndBusiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select max(a.invest_account_type) as invest_account_type,
			       (select tcbp.busi_prod_code
			          from app___pas__dbuser.t_contract_busi_prod tcbp
			         where tcbp.busi_item_id = a.busi_item_id) as busi_prod_code
			  from app___pas__dbuser.t_contract_invest a
			  left join app___pas__dbuser.t_contract_master b
			    on a.policy_id = b.policy_id
			  left join app___pas__dbuser.t_contract_busi_prod c
			    on a.busi_item_id = c.busi_item_id
			 where rownum <= 1000
			   and b.policy_code = #{policy_code}
			   and c.busi_prod_code = #{busi_prod_code}
			 group by a.busi_item_id
		   
		]]>
	</select>
	<!-- 投连万能账户信息详情查询税延产品账户listid -->
	<select id="PA_findAllContractInvestForSY" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			     SELECT AP.LIST_ID,
			            AP.POLICY_ID, /*保单ID*/
			            BP.BUSI_PROD_CODE, /*险种代码*/
			            AP.ACCOUNT_CODE, /*账户编码*/
			            TC.POLICY_CODE /*保单号*/
			       FROM APP___PAS__DBUSER.T_CONTRACT_INVEST AP
			      INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD BP
			         ON BP.BUSI_ITEM_ID = AP.BUSI_ITEM_ID
			        AND BP.POLICY_ID = AP.POLICY_ID
			      INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TC
			         ON AP.POLICY_ID = TC.POLICY_ID
			        AND BP.POLICY_ID = TC.POLICY_ID
			      INNER JOIN DEV_PDS.T_FUND FD
                     ON AP.ACCOUNT_CODE = FD.FUND_CODE
			      WHERE 1 = 1
			        AND BP.BUSI_PROD_CODE IN ('********','********','********') 
			        AND TC.POLICY_CODE = #{policy_code}  ]]> 
		<if test=" account_code != null and account_code != ''"> <![CDATA[ AND AP.ACCOUNT_CODE = #{account_code}]]> </if>	
		<![CDATA[ ORDER BY BP.BUSI_PROD_CODE  ]]> 
	</select>
	
	<!-- 万能投连账户信息查询接口-查询928及税延产品账户详细信息 -->
	<select id="PA_findAll928AndSYContractInvestDetailForGw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			     SELECT AP.LIST_ID,
			            AP.ACCOUNT_CODE,
			            AP.POLICY_ID, /*保单ID*/
			            BP.BUSI_PROD_CODE, /*险种代码*/
			            FD.FUND_NAME, /*投资组合名称*/
			            P.PRODUCT_NAME_SYS AS BUSI_PROD_NAME, /*险种代码名称*/
			            TC.POLICY_CODE, /*保单号*/
			            AP.CREATE_DATE, /*账户成立日期*/
			            AP.INTEREST_CAPITAL, /*账户价值*/
			            (SELECT DEAL_TIME
			               FROM (SELECT FS.SETTLE_DATE AS DEAL_TIME, FS.INVEST_ID
			                       FROM DEV_PAS.T_FUND_SETTLEMENT FS
			                      INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			                         ON FS.INVEST_ID = CI.LIST_ID
			                      INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
			                         ON CI.POLICY_ID = CM.POLICY_ID
			                      WHERE CM.POLICY_CODE = #{policy_code}
			                      ORDER BY FS.SETTLE_DATE DESC) B
			              WHERE ROWNUM = 1) COUNT_DATE ,/*上一结算日期*/
			              (IR.ASSIGN_RATE * 100 || '%') AS ASSIGN_RATE
			       FROM DEV_PAS.T_CONTRACT_INVEST AP
			      INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
			         ON BP.BUSI_ITEM_ID = AP.BUSI_ITEM_ID
			        AND BP.POLICY_ID = AP.POLICY_ID
			      INNER JOIN DEV_PAS.T_CONTRACT_MASTER TC
			         ON AP.POLICY_ID = TC.POLICY_ID
			        AND BP.POLICY_ID = TC.POLICY_ID
			      INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT P
			         ON BP.BUSI_PROD_CODE = P.PRODUCT_CODE_SYS
			      INNER JOIN DEV_PDS.T_FUND FD
			         ON AP.ACCOUNT_CODE = FD.FUND_CODE
			      INNER JOIN DEV_PAS.T_CONTRACT_INVEST_RATE IR
                     ON IR.ACCOUNT_CODE = AP.ACCOUNT_CODE
                    AND IR.POLICY_ID=AP.POLICY_ID
			      WHERE 1 = 1
			        AND TC.POLICY_CODE = #{policy_code} /*保单号*/
			        AND BP.BUSI_ITEM_ID = #{busi_item_id}
			        AND (BP.BUSI_PROD_CODE IN ('********','********','00Z01000','00Z01100') OR P.TAX_EXTENSION_FLAG = '1')
			         ]]>  
			         
	</select>
</mapper>
