<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IOrphanParameterDetailDao">
<!--
	<sql id="orphanParameterDetailWhereCondition">
		<if test=" detail_id  != null "><![CDATA[ AND A.DETAIL_ID = #{detail_id} ]]></if>
		<if test=" main_id  != null "><![CDATA[ AND A.MAIN_ID = #{main_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryOrphanParameterDetailByDetailIdCondition">
		<if test=" detail_id  != null "><![CDATA[ AND A.DETAIL_ID = #{detail_id} ]]></if>
	</sql>	
	<sql id="queryOrphanParameterDetailByMainIdCondition">
		<if test=" main_id  != null "><![CDATA[ AND A.MAIN_ID = #{main_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addOrphanParameterDetail"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="detail_id">
			SELECT APP___PAS__DBUSER.S_ORPHAN_PARAMETER_DETAIL.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL(
				INSERT_BY, INSERT_TIME, DETAIL_ID, MAIN_ID, UPDATE_BY, POLICY_CODE, UPDATE_TIME, 
				UPDATE_TIMESTAMP, INSERT_TIMESTAMP ) 
			VALUES (
				#{insert_by, jdbcType=NUMERIC}, SYSDATE , #{detail_id, jdbcType=NUMERIC} , #{main_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , SYSDATE 
				, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteOrphanParameterDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL WHERE DETAIL_ID = #{detail_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateOrphanParameterDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL ]]>
		<set>
		<trim suffixOverrides=",">
		    MAIN_ID = #{main_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE DETAIL_ID = #{detail_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findOrphanParameterDetailByDetailId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_ID, A.MAIN_ID, A.POLICY_CODE FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL A WHERE 1 = 1  ]]>
		<include refid="queryOrphanParameterDetailByDetailIdCondition" />
		<![CDATA[ ORDER BY A.DETAIL_ID ]]>
	</select>
	
	<select id="findOrphanParameterDetailByMainId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_ID, A.MAIN_ID, A.POLICY_CODE FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL A WHERE 1 = 1  ]]>
		<include refid="queryOrphanParameterDetailByMainIdCondition" />
		<![CDATA[ ORDER BY A.DETAIL_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapOrphanParameterDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_ID, A.MAIN_ID, A.POLICY_CODE FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllOrphanParameterDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_ID, A.MAIN_ID, A.POLICY_CODE FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findOrphanParameterDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryOrphanParameterDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.DETAIL_ID, B.MAIN_ID, B.POLICY_CODE FROM (
					SELECT ROWNUM RN, A.DETAIL_ID, A.MAIN_ID, A.POLICY_CODE FROM APP___PAS__DBUSER.T_ORPHAN_PARAMETER_DETAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<insert id="PA_batchSaveDetail" parameterType="java.util.List" useGeneratedKeys="false">
		INSERT INTO DEV_PAS.T_ORPHAN_PARAMETER_DETAIL (DETAIL_ID,MAIN_ID,
		POLICY_CODE, UPDATE_TIME, UPDATE_TIMESTAMP, INSERT_TIMESTAMP,INSERT_BY, INSERT_TIME, UPDATE_BY) SELECT
		APP___PAS__DBUSER.S_ORPHAN_PARAMETER_DETAIL.NEXTVAL,S.* FROM (
		<foreach collection="list" item="policy" index="index" separator="UNION ALL">
			SELECT
			#{policy.mainId,jdbcType=NUMERIC} as MAIN_ID,
			#{policy.policyCode,jdbcType=VARCHAR} as POLICY_CODE,
			SYSDATE as UPDATE_TIME,
			SYSDATE as UPDATE_TIMESTAMP,
			SYSDATE as INSERT_TIMESTAMP,
			#{policy.insertBy,jdbcType=NUMERIC} as INSERT_BY, 
			SYSDATE as INSERT_TIME, 
			#{policy.updateBy,jdbcType=NUMERIC} as UPDATE_BY
			FROM DUAL
		</foreach>
		) S
	</insert>
	
</mapper>
