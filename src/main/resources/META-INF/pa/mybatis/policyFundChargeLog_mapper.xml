<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyFundChargeLogDao">
<!--
	<sql id="PA_policyFundChargeLogWhereCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" charge_due_date  != null  and  charge_due_date  != ''  "><![CDATA[ AND A.CHARGE_DUE_DATE = #{charge_due_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" charge_code != null and charge_code != ''  "><![CDATA[ AND A.CHARGE_CODE = #{charge_code} ]]></if>
		<if test=" last_charge_date  != null  and  last_charge_date  != ''  "><![CDATA[ AND A.LAST_CHARGE_DATE = #{last_charge_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyFundChargeLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyFundChargeLogByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyFundChargeLogByLogTypeCondition">
		<if test=" log_type != null and log_type != '' "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyFundChargeLogByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyFundChargeLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_POLICY_FUND_CHARGE_LOG__LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG(
				PRODUCT_ID, CHARGE_DUE_DATE, INSERT_TIME, ITEM_ID, CHARGE_CODE, UPDATE_TIME, LAST_CHARGE_DATE, 
				LOG_ID, INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, 
				BUSI_ITEM_ID, INSERT_BY, POLICY_ID ) 
			VALUES (
				#{product_id, jdbcType=NUMERIC}, #{charge_due_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{charge_code, jdbcType=VARCHAR} , SYSDATE , #{last_charge_date, jdbcType=DATE} 
				, #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyFundChargeLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyFundChargeLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    CHARGE_DUE_DATE = #{charge_due_date, jdbcType=DATE} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			CHARGE_CODE = #{charge_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    LAST_CHARGE_DATE = #{last_charge_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyFundChargeLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.ITEM_ID, A.CHARGE_CODE, A.LAST_CHARGE_DATE, 
			A.LOG_ID, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	<select id="PA_findPolicyFundChargeLogByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.ITEM_ID, A.CHARGE_CODE, 
			A.LOG_ID, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeLogByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

	<select id="PA_findPolicyFundChargeLogByLogType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.ITEM_ID, A.CHARGE_CODE, 
			A.LOG_ID, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeLogByLogTypeCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="PA_findPolicyFundChargeLogByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.ITEM_ID, A.CHARGE_CODE, 
			A.LOG_ID, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyFundChargeLogByListIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyFundChargeLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.ITEM_ID, A.CHARGE_CODE, A.LAST_CHARGE_DATE, 
			A.LOG_ID, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyFundChargeLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.ITEM_ID, A.CHARGE_CODE, A.LAST_CHARGE_DATE, 
			A.LOG_ID, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyFundChargeLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyFundChargeLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRODUCT_ID, B.CHARGE_DUE_DATE, B.ITEM_ID, B.CHARGE_CODE, B.LAST_CHARGE_DATE, 
			B.LOG_ID, B.LIST_ID, B.LOG_TYPE, B.POLICY_CHG_ID, 
			B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.PRODUCT_ID, A.CHARGE_DUE_DATE, A.ITEM_ID, A.CHARGE_CODE, A.LAST_CHARGE_DATE, 
			A.LOG_ID, A.LIST_ID, A.LOG_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
