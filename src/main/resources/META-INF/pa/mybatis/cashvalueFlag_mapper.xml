<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.udmp.impl.batch.demo.dao.ICashvalueFlagDao">

	<sql id="cashvalueFlagWhereCondition">
		<if test=" tx_date  != null  and  tx_date  != ''  "><![CDATA[ AND A.TX_DATE = #{tx_date} ]]></if>
		<if test=" update_date  != null  and  update_date  != ''  "><![CDATA[ AND A.UPDATE_DATE = #{update_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" complete_flag != null and complete_flag != ''  "><![CDATA[ AND A.COMPLETE_FLAG = #{complete_flag} ]]></if>
		<if test=" tx_seq  != null "><![CDATA[ AND A.TX_SEQ = #{tx_seq} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCashvalueFlagByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCashvalueFlag"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CASHVALUE_FLAG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CASHVALUE_FLAG(
				INSERT_TIMESTAMP, TX_DATE, UPDATE_BY, INSERT_TIME, UPDATE_DATE, LIST_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, COMPLETE_FLAG, TX_SEQ ) 
			VALUES (
				CURRENT_TIMESTAMP, #{tx_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{update_date, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{complete_flag, jdbcType=VARCHAR} , #{tx_seq, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCashvalueFlag" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CASHVALUE_FLAG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCashvalueFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CASHVALUE_FLAG ]]>
		<set>
		<trim suffixOverrides=",">
		    TX_DATE = #{tx_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_DATE = #{update_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			COMPLETE_FLAG = #{complete_flag, jdbcType=VARCHAR} ,
		    TX_SEQ = #{tx_seq, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCashvalueFlagByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TX_DATE, A.UPDATE_DATE, A.LIST_ID, 
			A.COMPLETE_FLAG, A.TX_SEQ FROM APP___PAS__DBUSER.T_CASHVALUE_FLAG A WHERE 1 = 1  ]]>
		<include refid="queryCashvalueFlagByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCashvalueFlag" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TX_DATE, A.UPDATE_DATE, A.LIST_ID, 
			A.COMPLETE_FLAG, A.TX_SEQ FROM APP___PAS__DBUSER.T_CASHVALUE_FLAG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCashvalueFlag" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TX_DATE, A.UPDATE_DATE, A.LIST_ID, 
			A.COMPLETE_FLAG, A.TX_SEQ FROM APP___PAS__DBUSER.T_CASHVALUE_FLAG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCashvalueFlagTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CASHVALUE_FLAG A WHERE 1 = 1  ]]>
		<include refid="cashvalueFlagWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCashvalueFlagForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TX_DATE, B.UPDATE_DATE, B.LIST_ID, 
			B.COMPLETE_FLAG, B.TX_SEQ FROM (
					SELECT ROWNUM RN, A.TX_DATE, A.UPDATE_DATE, A.LIST_ID, 
			A.COMPLETE_FLAG, A.TX_SEQ FROM APP___PAS__DBUSER.T_CASHVALUE_FLAG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
