<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace=" com.nci.tunan.pa.impl.commonQuery.service.IExternalSystemInfoService">

	<sql id="PA_externalSystemInfoWhereCondition">
		<if test=" external_system_ip != null and external_system_ip != ''  "><![CDATA[ AND A.EXTERNAL_SYSTEM_IP = #{external_system_ip} ]]></if>
		<if test=" external_system_id != null and external_system_id != ''  "><![CDATA[ AND A.EXTERNAL_SYSTEM_ID = #{external_system_id} ]]></if>
		<if test=" external_system_context != null and external_system_context != ''  "><![CDATA[ AND A.EXTERNAL_SYSTEM_CONTEXT = #{external_system_context} ]]></if>
		<if test=" external_system_host != null and external_system_host != ''  "><![CDATA[ AND A.EXTERNAL_SYSTEM_HOST = #{external_system_host} ]]></if>
		<if test=" type != null and type != ''  "><![CDATA[ AND A.TYPE = #{type} ]]></if>
		<if test=" external_system_url != null and external_system_url != ''  "><![CDATA[ AND A.EXTERNAL_SYSTEM_URL = #{external_system_url} ]]></if>
		<if test=" external_system_name != null and external_system_name != ''  "><![CDATA[ AND A.EXTERNAL_SYSTEM_NAME = #{external_system_name} ]]></if>
		<if test=" external_system_domain != null and external_system_domain != ''  "><![CDATA[ AND A.EXTERNAL_SYSTEM_DOMAIN = #{external_system_domain} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryExternalSystemInfoByExternalSystemIdCondition">
		<if test=" external_system_id != null and external_system_id != '' "><![CDATA[ AND A.EXTERNAL_SYSTEM_ID = #{external_system_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addExternalSystemInfo"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO(
				EXTERNAL_SYSTEM_IP, EXTERNAL_SYSTEM_ID, EXTERNAL_SYSTEM_CONTEXT, EXTERNAL_SYSTEM_HOST, TYPE, EXTERNAL_SYSTEM_URL, EXTERNAL_SYSTEM_NAME, 
				EXTERNAL_SYSTEM_DOMAIN ) 
			VALUES (
				#{external_system_ip, jdbcType=VARCHAR}, #{external_system_id, jdbcType=VARCHAR} , #{external_system_context, jdbcType=VARCHAR} , #{external_system_host, jdbcType=VARCHAR} , #{type, jdbcType=VARCHAR} , #{external_system_url, jdbcType=VARCHAR} , #{external_system_name, jdbcType=VARCHAR} 
				, #{external_system_domain, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteExternalSystemInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO WHERE EXTERNAL_SYSTEM_ID = #{external_system_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateExternalSystemInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			EXTERNAL_SYSTEM_IP = #{external_system_ip, jdbcType=VARCHAR} ,
			EXTERNAL_SYSTEM_CONTEXT = #{external_system_context, jdbcType=VARCHAR} ,
			EXTERNAL_SYSTEM_HOST = #{external_system_host, jdbcType=VARCHAR} ,
			TYPE = #{type, jdbcType=VARCHAR} ,
			EXTERNAL_SYSTEM_URL = #{external_system_url, jdbcType=VARCHAR} ,
			EXTERNAL_SYSTEM_NAME = #{external_system_name, jdbcType=VARCHAR} ,
			EXTERNAL_SYSTEM_DOMAIN = #{external_system_domain, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE EXTERNAL_SYSTEM_ID = #{external_system_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findExternalSystemInfoByExternalSystemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTERNAL_SYSTEM_IP, A.EXTERNAL_SYSTEM_ID, A.EXTERNAL_SYSTEM_CONTEXT, A.EXTERNAL_SYSTEM_HOST, A.TYPE, A.EXTERNAL_SYSTEM_URL, A.EXTERNAL_SYSTEM_NAME, 
			A.EXTERNAL_SYSTEM_DOMAIN FROM APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryExternalSystemInfoByExternalSystemIdCondition" />
		<![CDATA[ ORDER BY A.EXTERNAL_SYSTEM_ID ]]>
	</select>
	
<!-- 查询操作 -->	
	<select id="PA_findExternalSystemInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTERNAL_SYSTEM_IP, A.EXTERNAL_SYSTEM_ID, A.EXTERNAL_SYSTEM_CONTEXT, A.EXTERNAL_SYSTEM_HOST, A.TYPE, A.EXTERNAL_SYSTEM_URL, A.EXTERNAL_SYSTEM_NAME, 
			A.EXTERNAL_SYSTEM_DOMAIN FROM APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_externalSystemInfoWhereCondition" />
		<![CDATA[ ORDER BY A.EXTERNAL_SYSTEM_ID ]]>
	</select>
<!-- 按map查询操作 -->
	<select id="PA_findAllMapExternalSystemInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTERNAL_SYSTEM_IP, A.EXTERNAL_SYSTEM_ID, A.EXTERNAL_SYSTEM_CONTEXT, A.EXTERNAL_SYSTEM_HOST, A.TYPE, A.EXTERNAL_SYSTEM_URL, A.EXTERNAL_SYSTEM_NAME, 
			A.EXTERNAL_SYSTEM_DOMAIN FROM APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.EXTERNAL_SYSTEM_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllExternalSystemInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTERNAL_SYSTEM_IP, A.EXTERNAL_SYSTEM_ID, A.EXTERNAL_SYSTEM_CONTEXT, A.EXTERNAL_SYSTEM_HOST, A.TYPE, A.EXTERNAL_SYSTEM_URL, A.EXTERNAL_SYSTEM_NAME, 
			A.EXTERNAL_SYSTEM_DOMAIN FROM APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.EXTERNAL_SYSTEM_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findExternalSystemInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryExternalSystemInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.EXTERNAL_SYSTEM_IP, B.EXTERNAL_SYSTEM_ID, B.EXTERNAL_SYSTEM_CONTEXT, B.EXTERNAL_SYSTEM_HOST, B.TYPE, B.EXTERNAL_SYSTEM_URL, B.EXTERNAL_SYSTEM_NAME, 
			B.EXTERNAL_SYSTEM_DOMAIN FROM (
					SELECT ROWNUM RN, A.EXTERNAL_SYSTEM_IP, A.EXTERNAL_SYSTEM_ID, A.EXTERNAL_SYSTEM_CONTEXT, A.EXTERNAL_SYSTEM_HOST, A.TYPE, A.EXTERNAL_SYSTEM_URL, A.EXTERNAL_SYSTEM_NAME, 
			A.EXTERNAL_SYSTEM_DOMAIN FROM APP___PAS__DBUSER.T_UDMP_EXTERNAL_SYSTEM_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.EXTERNAL_SYSTEM_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
