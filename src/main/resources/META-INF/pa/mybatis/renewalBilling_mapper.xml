<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.RenewalBillingDaoImpl">

	<sql id="renewalBillingWhereCondition">
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" renewal_prem  != null "><![CDATA[ AND A.RENEWAL_PREM = #{renewal_prem} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" billing_days  != null "><![CDATA[ AND A.BILLING_DAYS = #{billing_days} ]]></if>
		<if test=" billing_date  != null  and  billing_date  != ''  "><![CDATA[ AND A.BILLING_DATE = #{billing_date} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" renewal_amount  != null "><![CDATA[ AND A.RENEWAL_AMOUNT = #{renewal_amount} ]]></if>
		<if test=" extra_prem  != null "><![CDATA[ AND A.EXTRA_PREM = #{extra_prem} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" renewal_state  != null "><![CDATA[ AND A.RENEWAL_STATE = #{renewal_state} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryRenewalBillingByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryRenewalBillingByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
        <if test=" billing_date  != null "><![CDATA[ AND A.BILLING_DATE = #{billing_date} ]]></if>
	</sql>

<!-- 添加操作 -->
	<insert id="addRenewalBilling"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">			
			SELECT APP___PAS__DBUSER.S_RENEWAL_BILLING__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RENEWAL_BILLING(
				INSERT_TIME, UNIT_NUMBER, RENEWAL_PREM, UPDATE_TIME, ITEM_ID, BILLING_DAYS, BILLING_DATE, 
				EXPIRY_DATE, RENEWAL_AMOUNT, EXTRA_PREM, INSERT_TIMESTAMP, UPDATE_BY, RENEW_DECISION, LIST_ID, 
				RENEWAL_STATE, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, POLICY_ID ) 
			VALUES (
				SYSDATE, #{unit_number, jdbcType=VARCHAR} , #{renewal_prem, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{billing_days, jdbcType=NUMERIC} , #{billing_date, jdbcType=DATE} 
				, #{expiry_date, jdbcType=DATE} , #{renewal_amount, jdbcType=NUMERIC} , #{extra_prem, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{renew_decision, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{renewal_state, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRenewalBilling" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RENEWAL_BILLING WHERE POLICY_ID = #{policy_id} ]]>
		<if test=" item_id  != null "><![CDATA[ AND ITEM_ID = #{item_id} ]]></if>
	</delete>

<!-- 修改操作 -->
	<update id="updateRenewalBilling" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RENEWAL_BILLING ]]>
		<set>
		<trim suffixOverrides=",">
		   UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
		    RENEWAL_PREM = #{renewal_prem, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    BILLING_DAYS = #{billing_days, jdbcType=NUMERIC} ,
		    BILLING_DATE = #{billing_date, jdbcType=DATE} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    RENEWAL_AMOUNT = #{renewal_amount, jdbcType=NUMERIC} ,
		    EXTRA_PREM = #{extra_prem, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    RENEW_DECISION = #{renew_decision, jdbcType=NUMERIC} ,
		    RENEWAL_STATE = #{renewal_state, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE BUSI_ITEM_ID = #{busi_item_id} and BILLING_DATE = #{billing_date} and ITEM_ID = #{item_id} ]]>
	</update>
	<!-- 按照险种查询 -->
	<select id="findRenewalBillingByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT_NUMBER, A.RENEWAL_PREM, A.ITEM_ID, A.BILLING_DAYS, A.BILLING_DATE, 
			A.EXPIRY_DATE, A.RENEWAL_AMOUNT, A.EXTRA_PREM, A.RENEW_DECISION, A.LIST_ID, 
			A.RENEWAL_STATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_RENEWAL_BILLING A WHERE 1 = 1  ]]>
		<include refid="renewalBillingWhereCondition" />
		<![CDATA[ ORDER BY A.BILLING_DATE DESC ]]>
	</select>
<!-- 按索引查询操作 -->	
	<select id="findRenewalBillingByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT_NUMBER, A.RENEWAL_PREM, A.ITEM_ID, A.BILLING_DAYS, A.BILLING_DATE, 
			A.EXPIRY_DATE, A.RENEWAL_AMOUNT, A.EXTRA_PREM, A.RENEW_DECISION, A.LIST_ID, 
			A.RENEWAL_STATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_RENEWAL_BILLING A WHERE 1 = 1  ]]>
		<include refid="queryRenewalBillingByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapRenewalBilling" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT_NUMBER, A.RENEWAL_PREM, A.ITEM_ID, A.BILLING_DAYS, A.BILLING_DATE, 
			A.EXPIRY_DATE, A.RENEWAL_AMOUNT, A.EXTRA_PREM, A.RENEW_DECISION, A.LIST_ID, 
			A.RENEWAL_STATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_RENEWAL_BILLING A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRenewalBilling" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT_NUMBER, A.RENEWAL_PREM, A.ITEM_ID, A.BILLING_DAYS, A.BILLING_DATE, 
			A.EXPIRY_DATE, A.RENEWAL_AMOUNT, A.EXTRA_PREM, A.RENEW_DECISION, A.LIST_ID, 
			A.RENEWAL_STATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_RENEWAL_BILLING A WHERE ROWNUM <=  1000  ]]>
		<include refid="renewalBillingWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID DESC]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findRenewalBillingTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RENEWAL_BILLING A WHERE 1 = 1 ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryRenewalBillingForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UNIT_NUMBER, B.RENEWAL_PREM, B.ITEM_ID, B.BILLING_DAYS, B.BILLING_DATE, 
			B.EXPIRY_DATE, B.RENEWAL_AMOUNT, B.EXTRA_PREM, B.RENEW_DECISION, B.LIST_ID, 
			B.RENEWAL_STATE, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.UNIT_NUMBER, A.RENEWAL_PREM, A.ITEM_ID, A.BILLING_DAYS, A.BILLING_DATE, 
			A.EXPIRY_DATE, A.RENEWAL_AMOUNT, A.EXTRA_PREM, A.RENEW_DECISION, A.LIST_ID, 
			A.RENEWAL_STATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_RENEWAL_BILLING A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 根据保单ID查询续保数据-->
	<select id="findRenewalBillingByPolicyID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT TRB.LIST_ID, 
		           TRB.POLICY_ID, 
		           TRB.BUSI_ITEM_ID,
		           TRB.BILLING_DATE
		      FROM APP___PAS__DBUSER.T_RENEWAL_BILLING    TRB,
		           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
		     WHERE TRB.POLICY_ID = TCBP.POLICY_ID
		       AND TRB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		       AND TRB.BILLING_DATE = TCBP.MATURITY_DATE
			AND TRB.POLICY_ID = #{policy_id}	  
		]]>
	</select>
	
</mapper>
