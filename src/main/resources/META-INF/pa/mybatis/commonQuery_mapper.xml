<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.CommonQueryDaoImpl">

	<!-- 查询条件 -->	
	<sql id="PA_findHolderPolicyInfosCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TCM.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" service_agent != null and service_agent != '' and service_bank_branch != null and service_bank_branch != ''">
			<!-- <![CDATA[ AND TCM.SERVICE_AGENT = #{service_agent} OR TCM.SERVICE_BANK_BRANCH = #{service_bank_branch}]]> -->
			<![CDATA[    AND   (TCA.AGENT_NAME= #{service_agent} OR TCM.SERVICE_BANK_BRANCH = #{service_bank_branch})]]>
		</if>
		<if test=" customer_id  != null "><![CDATA[ AND CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND TPA.ACCOUNT = #{account} ]]></if>
	</sql>	
	<sql id="PA_findBusiItemInfosCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

	<!-- 获取客户作为投保人的保单信息 -->	
	<select id="PA_findHolderPolicyInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TCM.APPLY_CODE,TCM.LIABILITY_STATE,TPH.CUSTOMER_ID,
       			TCA.AGENT_NAME,TCM.SERVICE_BANK_BRANCH,TPA.ACCOUNT
       			  FROM T_CONTRACT_MASTER TCM,APP___PAS__DBUSER.T_POLICY_HOLDER TPH,APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA , APP___PAS__DBUSER.T_CONTRACT_AGENT TCA
       			  	WHERE 1 = 1  
       			  		AND TCA.POLICY_ID = TCM.POLICY_ID
						AND TPH.POLICY_ID = TCM.POLICY_ID
                  		AND TPA.POLICY_ID = TCM.POLICY_ID]]>
		<include refid="PA_findHolderPolicyInfosCondition" />
		<![CDATA[ ORDER BY TCM.POLICY_ID ]]>
	</select>
	
	<!-- 获取客户作为被保人的保单信息 -->
	<select id="PA_findInsuredPolicyInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TCM.APPLY_CODE,TCM.LIABILITY_STATE,TIL.CUSTOMER_ID,
       				TCA.AGENT_NAME,TCM.SERVICE_BANK_BRANCH,TPA.ACCOUNT
       			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,APP___PAS__DBUSER.T_INSURED_LIST TIL,APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA , APP___PAS__DBUSER.T_CONTRACT_AGENT TCA
       			  	WHERE 1 = 1  
       			  		AND TCA.POLICY_ID = TCM.POLICY_ID
						AND TIL.POLICY_ID = TCM.POLICY_ID
                  		AND TPA.POLICY_ID = TCM.POLICY_ID]]>
		<include refid="PA_findHolderPolicyInfosCondition" />
		<![CDATA[ ORDER BY TCM.POLICY_ID ]]>
	</select>
	
	<!-- 获取保单信息 -->
	<select id="PA_findAllCommonQueryComp" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TCM.APPLY_CODE,TCM.LIABILITY_STATE,
       			TCA.AGENT_NAME,TCM.SERVICE_BANK_BRANCH,TPA.ACCOUNT
       			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,APP___PAS__DBUSER.T_PAYER_ACCOUNT TPA , APP___PAS__DBUSER.T_CONTRACT_AGENT TCA
       			  	WHERE 1 = 1  
       			  		AND TCA.POLICY_ID = TCM.POLICY_ID
                  		AND TPA.POLICY_ID = TCM.POLICY_ID 
                  		]]>
		<include refid="PA_findHolderPolicyInfosCondition" />
		<![CDATA[ ORDER BY TCM.POLICY_ID ]]>
	</select>
	
	<!--查询险种责任组列表信息 -->
	<select id="PA_findBusiItemInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCBP.BUSI_PROD_CODE,A.PRODUCT_CODE,A.COVERAGE_PERIOD,A.AMOUNT,A.UNIT,
		A.BENEFIT_LEVEL,A.STD_PREM_AF,A.LIABILITY_STATE,TCE.PAY_DUE_DATE,TCE.PREM_STATUS,A.POLICY_ID,
		A.POLICY_CODE,A.BUSI_ITEM_ID,A.PRODUCT_ID,A.COVERAGE_YEAR,A.ITEM_ID,TCBP.BUSI_PRD_ID
      	 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A
            LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE ON TCE.ITEM_ID = A.ITEM_ID
            LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP ON A.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
       WHERE 1 = 1 ]]>
		<include refid="PA_findBusiItemInfosCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	
</mapper>
