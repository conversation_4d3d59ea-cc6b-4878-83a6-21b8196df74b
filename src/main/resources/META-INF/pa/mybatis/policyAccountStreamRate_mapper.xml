<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyAccountStreamRateDao">
	<sql id="PA_policyAccountStreamRateWhereCondition">
		<if test=" rate_id  != null "><![CDATA[ AND A.RATE_ID = #{rate_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" time_perido_code  != null "><![CDATA[ AND A.TIME_PERIDO_CODE = #{time_perido_code} ]]></if>
		<if test=" loan_rate  != null "><![CDATA[ AND A.LOAN_RATE = #{loan_rate} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyAccountStreamRateByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" stream_id  != null "><![CDATA[ AND A.STREAM_ID = #{stream_id} ]]></if>
	    <if test=" time_perido_code  != null "><![CDATA[ AND A.TIME_PERIDO_CODE = #{time_perido_code} ]]></if>
		
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyAccountStreamRate"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE(
				INSERT_TIMESTAMP, RATE_ID, LOG_ID, TIME_PERIDO_CODE, UPDATE_BY, INSERT_TIME, LOAN_RATE, 
				UPDATE_TIMESTAMP, UPDATE_TIME, POLICY_CHG_ID, INSERT_BY, STREAM_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{rate_id, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} , #{time_perido_code, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{loan_rate, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{stream_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyAccountStreamRate" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyAccountStreamRate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE ]]>
		<set>
		<trim suffixOverrides=",">
		    RATE_ID = #{rate_id, jdbcType=NUMERIC} ,
		    TIME_PERIDO_CODE = #{time_perido_code, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LOAN_RATE = #{loan_rate, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    STREAM_ID = #{stream_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyAccountStreamRateByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ID, A.LOG_ID, A.TIME_PERIDO_CODE, A.LOAN_RATE, 
			A.POLICY_CHG_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamRateByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<!-- 按索引查询操作 zhulh-->	
	<select id="PA_findPolicyAccountStreamRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.LOAN_RATE,
			A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAccountStreamRateByLogIdCondition" />
		<![CDATA[ ORDER BY A.STREAM_ID ]]>
	</select>
	
<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyAccountStreamRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ID, A.LOG_ID, A.TIME_PERIDO_CODE, A.LOAN_RATE, 
			A.POLICY_CHG_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyAccountStreamRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ID, A.LOG_ID, A.TIME_PERIDO_CODE, A.LOAN_RATE, 
			A.POLICY_CHG_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyAccountStreamRateWhereCondition" /> 
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyAccountStreamRateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyAccountStreamRateForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RATE_ID, B.LOG_ID, B.TIME_PERIDO_CODE, B.LOAN_RATE, 
			B.POLICY_CHG_ID, B.STREAM_ID FROM (
					SELECT ROWNUM RN, A.RATE_ID, A.LOG_ID, A.TIME_PERIDO_CODE, A.LOAN_RATE, 
			A.POLICY_CHG_ID, A.STREAM_ID FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM_RATE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
