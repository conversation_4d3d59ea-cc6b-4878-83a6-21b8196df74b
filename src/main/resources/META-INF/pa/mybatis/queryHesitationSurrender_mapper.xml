<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IQueryHesitationSurrenderDao">
	<sql id="queryHesitationSurrenderXmlListWhereCondition">
		<if test="serviceBankBranch!=null and serviceBankBranch!=''">
			<![CDATA[ AND temp.SERVICE_BANK_BRANCH like '${serviceBankBranch}%' ]]>
		</if>
		<if test="startDate!=null and endDate!='' and startDate!='' and endDate!=null">
			<![CDATA[AND temp.HESITATE_DATE >= '${startDate}' AND temp.HESITATE_DATE <= '${endDate}' ]]>
		</if>
	</sql>

	<!-- 查询银行犹豫期退保数据 -->
	<select id="findAllHesitationSurrenderXmlListForCMBC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select P.* from (
       SELECT * FROM(
       SELECT 
          TS.HESITATE_FLAG,TS.CHANGE_ID,TS.BUSI_ITEM_ID,/*险种ID-KEY*/
          TCCM.APPLY_CODE,/*投保单号*/TCCM.POLICY_CODE,/*保单号*/TCCM.SERVICE_BANK,TCCM.SERVICE_BANK_BRANCH,/*银行类别*/TCCM.POLICY_ID,
          /*变更前有效数据*/ (SELECT to_char(TPLC.CHANGE_DATE,'YYYYMMDD') FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC WHERE TPLC.POLICY_ID=TCCM.POLICY_ID AND LIABILITY_STATUS=1 AND ROWNUM=1)AS HESITATE_DATE,/*退保日期*/
          (SELECT TEM.END_CAUSE FROM (SELECT TPLC.END_CAUSE,TPLC.POLICY_ID FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC WHERE LIABILITY_STATUS=1 ORDER BY UPDATE_TIME DESC) TEM WHERE TEM.POLICY_ID=TCCM.POLICY_ID AND ROWNUM=1) AS END_CAUSE,/*退保类型*/
          to_char(TCBP.ISSUE_DATE,'YYYYMMDD') AS ISSUE_DATE/*签单日期*/,TCBP.BUSI_PROD_CODE,/*产品代码*/to_char(TCBP.VALIDATE_DATE,'YYYYMMDD') AS VALIDATE_DATE,/*生效日期*/to_char(TCBP.EXPIRY_DATE,'YYYYMMDD') AS EXPIRY_DATE,/*到期日期*/
          TPH.CUSTOMER_ID,TC.CUSTOMER_CERT_TYPE,/*投保人证件类型*/TC.CUSTOMER_CERTI_CODE,/*投保人证件号码*/TC.CUSTOMER_NAME,/*投保人姓名*/
          to_char(TCP.INITIAL_AMOUNT) AS INITIAL_AMOUNT/*首期投保金额*/
       FROM APP___PAS__DBUSER.T_SURRENDER TS/*退保记录表为主连接数据*/
          LEFT JOIN APP___PAS__DBUSER.T_CS_CONTRACT_MASTER TCCM
          ON TS.CHANGE_ID = TCCM.CHANGE_ID
          AND TS.POLICY_CHG_ID=TCCM.POLICY_CHG_ID
          AND TCCM.OLD_NEW='1'
            LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
            ON TCBP.BUSI_ITEM_ID=TS.BUSI_ITEM_ID
              LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
                   ON TCCM.POLICY_CODE=TPH.POLICY_CODE
                      LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TC
                           ON TPH.CUSTOMER_ID=TC.CUSTOMER_ID
                           LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
                           ON TCP.ITEM_ID=TS.ITEM_ID
          WHERE TS.HESITATE_FLAG=1 /*犹豫期退保*/
          AND TCCM.SUBMIT_CHANNEL='1'/*银行代理*/) temp
	        WHERE 1=1 ]]> 
		  <include refid="queryHesitationSurrenderXmlListWhereCondition" />
		  <![CDATA[ )P where 1=1 ORDER BY P.POLICY_CODE]]> 
	</select>
	
	<!-- 查询银行犹豫期退保数据 -->
	<select id="findAllHesitationSurrenderXmlListForBDC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select P.* from (
       SELECT * FROM(
       SELECT 
          TS.HESITATE_FLAG,TS.CHANGE_ID,TS.BUSI_ITEM_ID,
          TCCM.APPLY_CODE,TCCM.POLICY_CODE,TCCM.SERVICE_BANK,TCCM.SERVICE_BANK_BRANCH,TCCM.POLICY_ID,
          /*变更前有效数据*/ (SELECT to_char(TPLC.CHANGE_DATE,'YYYYMMDD') FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC WHERE TPLC.POLICY_ID=TCCM.POLICY_ID AND LIABILITY_STATUS=1 AND ROWNUM=1)AS HESITATE_DATE
       FROM APP___PAS__DBUSER.T_SURRENDER TS/*退保记录表为主连接数据*/
          LEFT JOIN APP___PAS__DBUSER.T_CS_CONTRACT_MASTER TCCM
          ON TS.CHANGE_ID = TCCM.CHANGE_ID
          AND TS.POLICY_CHG_ID=TCCM.POLICY_CHG_ID
          AND TCCM.OLD_NEW='1'
          WHERE TS.HESITATE_FLAG=1 /*犹豫期退保*/
          AND TCCM.SUBMIT_CHANNEL='1'/*银行代理*/) temp
	        WHERE 1=1 ]]> 
		  <include refid="queryHesitationSurrenderXmlListWhereCondition" />
		  <![CDATA[ )P where 1=1 ORDER BY P.POLICY_CODE]]> 
	</select>
	
	<!-- 查询银行犹豫期退保数据 -->
	<select id="findAllHesitationSurrenderXmlList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select P.* from (
       SELECT * FROM(
       SELECT 
          TS.HESITATE_FLAG,TS.CHANGE_ID,TS.BUSI_ITEM_ID,
          TCCM.APPLY_CODE,TCCM.POLICY_CODE,TCCM.SERVICE_BANK,TCCM.SERVICE_BANK_BRANCH,TCCM.POLICY_ID,
          /*变更前有效数据*/ (SELECT to_char(TPLC.CHANGE_DATE,'YYYYMMDD') FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE TPLC WHERE TPLC.POLICY_ID=TCCM.POLICY_ID AND LIABILITY_STATUS=1 AND ROWNUM=1)AS HESITATE_DATE
       FROM APP___PAS__DBUSER.T_SURRENDER TS/*退保记录表为主连接数据*/
          LEFT JOIN APP___PAS__DBUSER.T_CS_CONTRACT_MASTER TCCM
          ON TS.CHANGE_ID = TCCM.CHANGE_ID
          AND TS.POLICY_CHG_ID=TCCM.POLICY_CHG_ID
          AND TCCM.OLD_NEW='1'
          WHERE TS.HESITATE_FLAG=1 /*犹豫期退保*/
          AND TCCM.SUBMIT_CHANNEL='1'/*银行代理*/) temp
	        WHERE 1=1 ]]> 
		  <include refid="queryHesitationSurrenderXmlListWhereCondition" />
		  <![CDATA[ )P where 1=1 ORDER BY P.POLICY_CODE]]> 
	</select>
	
</mapper>
