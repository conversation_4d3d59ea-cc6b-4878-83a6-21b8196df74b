<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.impl.queryPolicyAmount.dao.impl.QueryPolicyAmountDaoImpl">

	<sql id="queryCondition">
		<if test=" policyId != null and policyId != '' ">
			AND A.POLICY_ID = #{policyId} 
		</if>
		<if test=" policyCode != null and policyCode != '' ">
			AND A.POLICY_CODE = #{policyCode}
		</if>
		<if test=" policyChgId != null and policyChgId != '' ">
			AND A.POLICY_CHG_ID = #{policyChgId}		
		</if>
	</sql>
	
	<!-- 根据policyChgId查询保单金额相关数据 -->
	<select id="queryPolicyAmountByLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT CASE WHEN A.LIABILITY_STATE = 3 AND A.END_CAUSE = '01' THEN
					          1  ELSE  0 END EXPIRATIONFLAG,
					       (SELECT SUM(B.AMOUNT) CONTAMOUNT
					          FROM DEV_PAS.T_CONTRACT_PRODUCT B
					         WHERE B.POLICY_CODE = A.POLICY_CODE ]]>
		<if test=" busi_prod_code != null and busi_prod_code != '' ">
							 AND B.BUSI_ITEM_ID in
				               (SELECT TCBP.BUSI_ITEM_ID
				                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				                 WHERE TCBP.POLICY_CODE = B.POLICY_CODE
				                   AND TCBP.BUSI_PROD_CODE = #{busi_prod_code} )
		</if>			         
		<![CDATA[		   AND B.LIABILITY_STATE = 1) CONTAMOUNT,
					       (SELECT SUM(B.TOTAL_PREM_AF) PREMMONEY
					          FROM DEV_PAS.T_CONTRACT_PRODUCT_LOG B
					         WHERE B.POLICY_CHG_ID = A.POLICY_CHG_ID ]]>
		<if test=" busi_prod_code != null and busi_prod_code != '' ">
							 AND B.BUSI_ITEM_ID in
				               (SELECT TCBP.BUSI_ITEM_ID
				                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				                 WHERE TCBP.POLICY_CODE = B.POLICY_CODE
				                   AND TCBP.BUSI_PROD_CODE = #{busi_prod_code} )
		</if>			         
		<![CDATA[		   AND B.LIABILITY_STATE = 1) PREMMONEY,
					       A.CHANNEL_TYPE SALECODE,
					       C.SALES_CHANNEL_NAME SALECODENAME
					  FROM DEV_PAS.T_CONTRACT_MASTER_LOG A, DEV_PAS.T_SALES_CHANNEL C
					 WHERE A.CHANNEL_TYPE = C.SALES_CHANNEL_CODE ]]>
		<include refid="queryCondition" />
	</select>
	
	<!-- 查询当前保单金额相关数据 -->
	<select id="queryPolicyAmountByNow" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT CASE WHEN A.LIABILITY_STATE = 3 AND A.END_CAUSE = '01' THEN
					          1  ELSE 0 END EXPIRATIONFLAG,
					       (SELECT SUM(B.AMOUNT) CONTAMOUNT
					          FROM DEV_PAS.T_CONTRACT_PRODUCT B
					         WHERE B.POLICY_CODE = A.POLICY_CODE ]]>
		<if test=" busi_prod_code != null and busi_prod_code != '' ">
							 AND B.BUSI_ITEM_ID in
				               (SELECT TCBP.BUSI_ITEM_ID
				                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				                 WHERE TCBP.POLICY_CODE = B.POLICY_CODE
				                   AND TCBP.BUSI_PROD_CODE = #{busi_prod_code} )
		</if>				         
		<![CDATA[			) CONTAMOUNT,
					       (SELECT SUM(B.TOTAL_PREM_AF) PREMMONEY
					          FROM DEV_PAS.T_CONTRACT_PRODUCT B
					         WHERE B.POLICY_CODE = A.POLICY_CODE ]]>
		<if test=" busi_prod_code != null and busi_prod_code != '' ">
							 AND B.BUSI_ITEM_ID in
				               (SELECT TCBP.BUSI_ITEM_ID
				                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
				                 WHERE TCBP.POLICY_CODE = B.POLICY_CODE
				                   AND TCBP.BUSI_PROD_CODE = #{busi_prod_code} )
		</if>				         
		<![CDATA[			) PREMMONEY,
					       A.CHANNEL_TYPE SALECODE,
					       C.SALES_CHANNEL_NAME SALECODENAME
					  FROM DEV_PAS.T_CONTRACT_MASTER A, DEV_PAS.T_SALES_CHANNEL C
					 WHERE A.CHANNEL_TYPE = C.SALES_CHANNEL_CODE ]]>
					 
					 
					 	<![CDATA[	 ]]>
		<include refid="queryCondition" />
	</select>
	
</mapper>