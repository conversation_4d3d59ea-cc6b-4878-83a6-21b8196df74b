<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IExtendGracePeriodDao">

	<sql id="extendGracePeriodWhereCondition">
		<if test=" extend_day  != null "><![CDATA[ AND A.EXTEND_DAY = #{extend_day} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="queryExtendGracePeriodByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	<sql id="queryExtendGracePeriodByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="queryExtendGracePeriodByPayDueDateCondition">
		<if test=" pay_due_date  != null "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
	</sql>
	<sql id="queryExtendGracePeriodByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="addExtendGracePeriod" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT S_EXTEND_GRACE_PERIOD.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD(
				INSERT_TIMESTAMP, EXTEND_DAY, POLICY_CODE, UPDATE_BY, INSERT_TIME, UNIT_NUMBER, PAY_DUE_DATE, 
				LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, POLICY_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{extend_day, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{unit_number, jdbcType=VARCHAR} , #{pay_due_date, jdbcType=DATE} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="deleteExtendGracePeriod" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="updateExtendGracePeriod" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD ]]>
		<set>
			<trim suffixOverrides=",">
				EXTEND_DAY = #{extend_day, jdbcType=NUMERIC} ,
				POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
				PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				UPDATE_TIME = SYSDATE ,
				POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="findExtendGracePeriodByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE 1 = 1  ]]>
		<include refid="queryExtendGracePeriodByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="findExtendGracePeriodByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE 1 = 1  ]]>
		<include refid="queryExtendGracePeriodByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="findExtendGracePeriodByPayDueDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE 1 = 1  ]]>
		<include refid="queryExtendGracePeriodByPayDueDateCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="findExtendGracePeriodByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE 1 = 1  ]]>
		<include refid="queryExtendGracePeriodByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>


	<!-- 按map查询操作 -->
	<select id="findAllMapExtendGracePeriod" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllExtendGracePeriod" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="findExtendGracePeriodTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="queryExtendGracePeriodForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.EXTEND_DAY, B.POLICY_CODE, B.UNIT_NUMBER, B.PAY_DUE_DATE, 
			B.LIST_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

	<!-- 查询单条数据 -->
	<select id="findExtendGracePeriod" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE 1 = 1  ]]>
		<include refid="extendGracePeriodWhereCondition" />
	</select>
	
	<!-- 查询某个时间前最近一次的宽限期延长天数 -->
	<select id="queryLastTimeExtendDays" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT ROWNUM RN, A.EXTEND_DAY, A.POLICY_CODE, A.UNIT_NUMBER, A.PAY_DUE_DATE, 
			A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD A WHERE 1 = 1  ]]>
			
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE < #{pay_due_date} ]]></if>
		
		<![CDATA[ ORDER BY A.PAY_DUE_DATE DESC) B WHERE B.RN = 1 ]]>
	</select>
	
	<!-- 延长宽限期  无索引添加 -->
	<insert id="addEPExtendGracePeriod"  useGeneratedKeys="true" parameterType="java.util.Map">
		
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_EXTEND_GRACE_PERIOD(
				INSERT_TIMESTAMP, EXTEND_DAY, POLICY_CODE, UPDATE_BY, INSERT_TIME, UNIT_NUMBER, PAY_DUE_DATE, 
				LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, POLICY_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{extend_day, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{unit_number, jdbcType=VARCHAR} , #{pay_due_date, jdbcType=DATE} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
</mapper>
