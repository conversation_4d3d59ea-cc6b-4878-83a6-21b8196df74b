<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimSurveyBatchPO">

	<sql id="claimSurveyBatchWhereCondition">
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_reason != null and survey_reason != ''  "><![CDATA[ AND A.SURVEY_REASON = #{survey_reason} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" survey_mode  != null "><![CDATA[ AND A.SURVEY_MODE = #{survey_mode} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND A.PLAN_NAME like '%${plan_name}%' ]]></if>
		<if test=" batch_id  != null "><![CDATA[ AND A.BATCH_ID = #{batch_id} ]]></if>
		<if test=" extract_rule != null and extract_rule != ''  "><![CDATA[ AND A.EXTRACT_RULE = #{extract_rule} ]]></if>
	    <if test=" upload_date  != null  and  upload_date  != ''  "><![CDATA[ AND A.UPLOAD_DATE = #{upload_date} ]]></if>
	    <!-- 单证生成日期start -->
		<if
			test=" uploaddate_star != null and uploaddate_star != '' and uploaddate_end == null "><![CDATA[ and A.upload_date >= #{uploaddate_star} ]]></if>
		<!-- 单证生成日期end -->
		<if
			test=" uploaddate_end != null and uploaddate_end != '' and uploaddate_star == null "><![CDATA[ and A.upload_date <= #{uploaddate_end} ]]></if>
		<if
			test=" uploaddate_star != null and uploaddate_star != '' and uploaddate_end != null and uploaddate_end != '' "><![CDATA[ and A.upload_date between #{uploaddate_star} and #{uploaddate_end}]]></if>
	</sql>
	
	<sql id="allClaimSurveyBatchWhere">
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_reason != null and survey_reason != ''  "><![CDATA[ AND A.SURVEY_REASON = #{survey_reason} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" survey_mode  != null "><![CDATA[ AND A.SURVEY_MODE = #{survey_mode} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND A.PLAN_NAME = #{plan_name} ]]></if>
		<if test=" batch_id  != null "><![CDATA[ AND A.BATCH_ID = #{batch_id} ]]></if>
		<if test=" extract_rule != null and extract_rule != ''  "><![CDATA[ AND A.EXTRACT_RULE = #{extract_rule} ]]></if>
	    <if test=" upload_date  != null  and  upload_date  != ''  "><![CDATA[ AND A.UPLOAD_DATE = #{upload_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimSurveyBatchByBatchIdCondition">
		<if test=" batch_id  != null "><![CDATA[ AND A.BATCH_ID = #{batch_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimSurveyBatch"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="batch_id">
			SELECT APP___PAS__DBUSER.S_CLAIM_SURVEY_BATCH__BATCH_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH(
				BIZ_TYPE, SURVEY_REASON, PLAN_ID, INSERT_TIME, UPDATE_TIME, SURVEY_MODE, ORGAN_CODE, 
				INSERT_TIMESTAMP, UPLOAD_DATE, PLAN_NAME, BATCH_ID, UPDATE_BY, EXTRACT_RULE, UPDATE_TIMESTAMP, 
				INSERT_BY ) 
			VALUES (
				#{biz_type, jdbcType=NUMERIC}, #{survey_reason, jdbcType=VARCHAR} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{survey_mode, jdbcType=NUMERIC} , #{organ_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{upload_date, jdbcType=DATE} , #{plan_name, jdbcType=VARCHAR} , #{batch_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{extract_rule, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimSurveyBatch" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH WHERE BATCH_ID = #{batch_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimSurveyBatch" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH ]]>
		<set>
		<trim suffixOverrides=",">
		    BIZ_TYPE = #{biz_type, jdbcType=NUMERIC} ,
			SURVEY_REASON = #{survey_reason, jdbcType=VARCHAR} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    SURVEY_MODE = #{survey_mode, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    UPLOAD_DATE = #{upload_date, jdbcType=DATE} ,
			PLAN_NAME = #{plan_name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			EXTRACT_RULE = #{extract_rule, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE BATCH_ID = #{batch_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimSurveyBatchByBatchId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIZ_TYPE, A.SURVEY_REASON, A.PLAN_ID, A.SURVEY_MODE, A.ORGAN_CODE, 
			A.UPLOAD_DATE, A.PLAN_NAME, A.BATCH_ID, A.EXTRACT_RULE FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A WHERE 1 = 1  ]]>
		<include refid="queryClaimSurveyBatchByBatchIdCondition" />
		<![CDATA[ ORDER BY A.BATCH_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimSurveyBatch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIZ_TYPE, A.SURVEY_REASON, A.PLAN_ID, A.SURVEY_MODE, A.ORGAN_CODE, 
			A.UPLOAD_DATE, A.PLAN_NAME, A.BATCH_ID, A.EXTRACT_RULE FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BATCH_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimSurveyBatch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIZ_TYPE, A.SURVEY_REASON, A.PLAN_ID, A.SURVEY_MODE, A.ORGAN_CODE, 
			A.UPLOAD_DATE, A.PLAN_NAME, A.BATCH_ID, A.EXTRACT_RULE FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A WHERE ROWNUM <=  1000  ]]>
		 <include refid="allClaimSurveyBatchWhere" /> 
		<![CDATA[ ORDER BY A.BATCH_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimSurveyBatchTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A WHERE 1 = 1  ]]>
		<include refid="claimSurveyBatchWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimSurveyBatchForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BIZ_TYPE, B.SURVEY_REASON, B.PLAN_ID, B.SURVEY_MODE, B.ORGAN_CODE, 
			B.UPLOAD_DATE, B.PLAN_NAME, B.BATCH_ID, B.EXTRACT_RULE, B.INSERT_BY FROM (
					SELECT ROWNUM RN, A.BIZ_TYPE, A.SURVEY_REASON, A.PLAN_ID, A.SURVEY_MODE, A.ORGAN_CODE, 
			A.UPLOAD_DATE, A.PLAN_NAME, A.BATCH_ID, A.EXTRACT_RULE, A.INSERT_BY FROM APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="claimSurveyBatchWhereCondition" />
		<![CDATA[ ORDER BY A.BATCH_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
