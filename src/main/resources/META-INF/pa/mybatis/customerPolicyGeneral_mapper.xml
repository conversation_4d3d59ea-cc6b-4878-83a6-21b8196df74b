<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ICustomerPolicyGeneralDao">
	<!-- 分页查询保单ID -->
	<select id="PA_findPolicyIdByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.POLICY_ID,T.RUNM FROM (
			  SELECT TAB.POLICY_ID,ROWNUM RUNM FROM (
			    SELECT TPH.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH WHERE TPH.CUSTOMER_ID=#{customerId}
			    UNION
			    SELECT TIL.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST TIL WHERE TIL.CUSTOMER_ID=#{customerId}
			    UNION
			    SELECT TCB.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_BENE TCB WHERE TCB.CUSTOMER_ID=#{customerId}) TAB) T
			  WHERE T.RUNM>=#{begainNumber} AND T.RUNM<=#{endNumber}
		]]>
		
	</select>
	<!-- 统计查询个数 -->
	<select id="PA_countPolicyIdByCustomerId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(T.POLICY_ID) AS COUNTNUM FROM (
			  SELECT TAB.POLICY_ID,ROWNUM RUNM FROM (
			    SELECT TPH.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH WHERE TPH.CUSTOMER_ID=#{customerId}
			    UNION
			    SELECT TIL.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST TIL WHERE TIL.CUSTOMER_ID=#{customerId}
			    UNION
			    SELECT TCB.POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_BENE TCB WHERE TCB.CUSTOMER_ID=#{customerId}) TAB) T
			  
		]]>
		
	</select>
	<!-- 查询保单信息 -->
	<select id="PA_queryPolicyInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TCM.ORGAN_CODE,
		       TCM.POLICY_CODE,
		       TCM.SALE_AGENT_NAME,
		       TCM.LIABILITY_STATE,
		       TCM.VALIDATE_DATE
		   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		   WHERE TCM.POLICY_ID = #{policyId}
		]]>
	</select>
	<!--查询分红信息  -->
<!-- 		<select id="PA_queryPolicyBonusInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
               select tcp.bonus_sa,
                      tcp.amount,
                      tcp.apply_code,
                      tcp.busi_item_id,
                      tcp.policy_code,
                      tba.bonus_sa,                    
                      tbp.product_category1,
                      tpl.product_name
                 from dev_pas.t_contract_product tcp
                 left join dev_pas.t_bonus_allocate tba
                   on tcp.policy_code = tba.policy_code
                 left join app___pas__dbuser.t_product_life tpl
                   on tcp.product_id = tpl.product_id
                   left join dev_pas.t_contract_busi_prod tcb 
                   on  tcp.policy_id = tcb.policy_id
                   left join APP___PAS__DBUSER.t_business_product tbp 
                   on tcb.busi_prd_id = tbp.business_prd_id
                where tbp.product_category1 = '20002' and tcp.apply_code = '00000028026297'
		]]>
	</select> -->
</mapper>
