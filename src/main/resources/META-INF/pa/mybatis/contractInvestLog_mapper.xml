<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractInvestLogDao">
<!--
	<sql id="PA_contractInvestLogWhereCondition">
		<if test=" accum_units  != null "><![CDATA[ AND A.ACCUM_UNITS = #{accum_units} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" interest_sum  != null "><![CDATA[ AND A.INTEREST_SUM = #{interest_sum} ]]></if>
		<if test=" invest_account_type  != null "><![CDATA[ AND A.INVEST_ACCOUNT_TYPE = #{invest_account_type} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" settle_due_date  != null  and  settle_due_date  != ''  "><![CDATA[ AND A.SETTLE_DUE_DATE = #{settle_due_date} ]]></if>
		<if test=" interest_balance  != null "><![CDATA[ AND A.INTEREST_BALANCE = #{interest_balance} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" settlement_rate  != null "><![CDATA[ AND A.SETTLEMENT_RATE = #{settlement_rate} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" total_prem  != null "><![CDATA[ AND A.TOTAL_PREM = #{total_prem} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" negative_time  != null  and  negative_time  != ''  "><![CDATA[ AND A.NEGATIVE_TIME = #{negative_time} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractInvestLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addContractInvestLog"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_INVEST_LOG__LOG_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG(
				ACCUM_UNITS, INTEREST_CAPITAL, PRODUCT_ID, INSERT_TIME, INTEREST_SUM, INVEST_ACCOUNT_TYPE, UPDATE_TIME, 
				ITEM_ID, ACCOUNT_CODE, INSERT_TIMESTAMP, LOG_ID, UPDATE_BY, SETTLE_DUE_DATE, INTEREST_BALANCE, 
				LIST_ID, SETTLEMENT_RATE, UPDATE_TIMESTAMP, LOG_TYPE, POLICY_CHG_ID, TOTAL_PREM, BUSI_ITEM_ID, 
				INSERT_BY, POLICY_ID, NEGATIVE_TIME ) 
			VALUES (
				#{accum_units, jdbcType=NUMERIC}, #{interest_capital, jdbcType=NUMERIC} , #{product_id, jdbcType=NUMERIC} , SYSDATE , #{interest_sum, jdbcType=NUMERIC} , #{invest_account_type, jdbcType=NUMERIC} , SYSDATE 
				, #{item_id, jdbcType=NUMERIC} , #{account_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{settle_due_date, jdbcType=DATE} , #{interest_balance, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , #{settlement_rate, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{log_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{total_prem, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{negative_time, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractInvestLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateContractInvestLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    ACCUM_UNITS = #{accum_units, jdbcType=NUMERIC} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    INTEREST_SUM = #{interest_sum, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_TYPE = #{invest_account_type, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SETTLE_DUE_DATE = #{settle_due_date, jdbcType=DATE} ,
		    INTEREST_BALANCE = #{interest_balance, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    SETTLEMENT_RATE = #{settlement_rate, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LOG_TYPE = #{log_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    TOTAL_PREM = #{total_prem, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    NEGATIVE_TIME = #{negative_time, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findContractInvestLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.INVEST_ACCOUNT_TYPE, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			A.LIST_ID, A.SETTLEMENT_RATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.TOTAL_PREM, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractInvestLogByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractInvestLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.INVEST_ACCOUNT_TYPE, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			A.LIST_ID, A.SETTLEMENT_RATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.TOTAL_PREM, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractInvestLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.INVEST_ACCOUNT_TYPE, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			A.LIST_ID, A.SETTLEMENT_RATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.TOTAL_PREM, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractInvestLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractInvestLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCUM_UNITS, B.INTEREST_CAPITAL, B.PRODUCT_ID, B.INTEREST_SUM, B.INVEST_ACCOUNT_TYPE, 
			B.ITEM_ID, B.ACCOUNT_CODE, B.LOG_ID, B.SETTLE_DUE_DATE, B.INTEREST_BALANCE, 
			B.LIST_ID, B.SETTLEMENT_RATE, B.LOG_TYPE, B.POLICY_CHG_ID, B.TOTAL_PREM, B.BUSI_ITEM_ID, 
			B.POLICY_ID, B.NEGATIVE_TIME FROM (
					SELECT ROWNUM RN, A.ACCUM_UNITS, A.INTEREST_CAPITAL, A.PRODUCT_ID, A.INTEREST_SUM, A.INVEST_ACCOUNT_TYPE, 
			A.ITEM_ID, A.ACCOUNT_CODE, A.LOG_ID, A.SETTLE_DUE_DATE, A.INTEREST_BALANCE, 
			A.LIST_ID, A.SETTLEMENT_RATE, A.LOG_TYPE, A.POLICY_CHG_ID, A.TOTAL_PREM, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.NEGATIVE_TIME FROM APP___PAS__DBUSER.T_CONTRACT_INVEST_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询单条账户信息 -->
	<select id="PA_findContractInvestLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.LOG_ID,
       A.LOG_TYPE,
       A.POLICY_CHG_ID,
       A.LIST_ID,
       A.POLICY_ID,
       A.BUSI_ITEM_ID,
       A.ITEM_ID,
       A.PRODUCT_ID,
       A.ACCOUNT_CODE,
       A.ACCUM_UNITS,
       A.NEGATIVE_TIME,
       A.INTEREST_CAPITAL,
       A.INTEREST_BALANCE,
       A.INTEREST_SUM,
       A.SETTLEMENT_RATE,
       A.SETTLE_DUE_DATE,
       A.INVEST_ACCOUNT_TYPE,
       A.TOTAL_PREM,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP FROM DEV_PAS.T_CONTRACT_INVEST_LOG A WHERE 1 = 1
		]]>
	<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</select>
</mapper>
