<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAppendPremListDao">

	<sql id="appendPremListWhereCondition">
		<if test=" account_prem  != null "><![CDATA[ AND A.ACCOUNT_PREM = #{account_prem} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" app_prem_id  != null "><![CDATA[ AND A.APP_PREM_ID = #{app_prem_id} ]]></if>
		<if test=" initial_cost  != null "><![CDATA[ AND A.INITIAL_COST = #{initial_cost} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" append_prem  != null "><![CDATA[ AND A.APPEND_PREM = #{append_prem} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	

<!-- 添加操作 -->
	<insert id="addAppendPremList"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_APPEND_PREM_LIST(
				ACCOUNT_PREM, PRODUCT_ID, INSERT_TIME, APP_PREM_ID, INITIAL_COST, PRODUCT_CODE, ITEM_ID, 
				UPDATE_TIME, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, 
				POLICY_ID, APPEND_PREM ) 
			VALUES (
				#{account_prem, jdbcType=NUMERIC}, #{product_id, jdbcType=NUMERIC} , SYSDATE , #{app_prem_id, jdbcType=NUMERIC} , #{initial_cost, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} 
				, SYSDATE , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} , #{append_prem, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteAppendPremList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_APPEND_PREM_LIST WHERE APP_PREM_ID = #{app_prem_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateAppendPremList" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_APPEND_PREM_LIST ]]>
		<set>
		<trim suffixOverrides=",">
		    ACCOUNT_PREM = #{account_prem, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    INITIAL_COST = #{initial_cost, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    APPEND_PREM = #{append_prem, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE APP_PREM_ID = #{app_prem_id} ]]>
	</update>

<!-- 按索引查询操作 -->	

<!-- 按map查询操作 -->
	<select id="findAllMapAppendPremList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_PREM, A.PRODUCT_ID, A.APP_PREM_ID, A.INITIAL_COST, A.PRODUCT_CODE, A.ITEM_ID, 
			A.POLICY_CODE, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.APPEND_PREM FROM APP___PAS__DBUSER.T_APPEND_PREM_LIST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APP_PREM_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllAppendPremList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_PREM, A.PRODUCT_ID, A.APP_PREM_ID, A.INITIAL_COST, A.PRODUCT_CODE, A.ITEM_ID, 
			A.POLICY_CODE, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.APPEND_PREM,A.INSERT_TIME FROM APP___PAS__DBUSER.T_APPEND_PREM_LIST A WHERE ROWNUM <=  1000  ]]>
		 <include refid="appendPremListWhereCondition" />
		<![CDATA[ ORDER BY A.APP_PREM_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findAppendPremListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_APPEND_PREM_LIST A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryAppendPremListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCOUNT_PREM, B.PRODUCT_ID, B.APP_PREM_ID, B.INITIAL_COST, B.PRODUCT_CODE, B.ITEM_ID, 
			B.POLICY_CODE, B.BUSI_ITEM_ID, 
			B.POLICY_ID, B.APPEND_PREM FROM (
					SELECT ROWNUM RN, A.ACCOUNT_PREM, A.PRODUCT_ID, A.APP_PREM_ID, A.INITIAL_COST, A.PRODUCT_CODE, A.ITEM_ID, 
			A.POLICY_CODE, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.APPEND_PREM FROM APP___PAS__DBUSER.T_APPEND_PREM_LIST A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APP_PREM_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 根据保单号查询所有数据 ，同一保单号追加保费合并 -->
	<select id="findAllAppendPremListByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		
	</select>
</mapper>
