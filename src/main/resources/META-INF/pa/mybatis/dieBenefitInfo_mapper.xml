<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.impl.DieBenefitInfoDaoImpl">

   <!-- 查询身故受益人信息 -->
   <select id="PA_findDieBenefitInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT TCB.BUSI_ITEM_ID,
				       TC.CUSTOMER_NAME,
				       TC.CUSTOMER_GENDER,
				       TC.CUSTOMER_BIRTHDAY,
				       TC.CUSTOMER_CERT_TYPE,
				       TC.CUSTOMER_CERTI_CODE,
				       TCB.DESIGNATION,
				       TCB.SHARE_ORDER,
				       TCB.SHARE_RATE,
				       TA.COUNTRY_CODE,
				       TC.CUST_CERT_STAR_DATE,
				       TC.CUST_CERT_END_DATE,
				       TA.MOBILE_TEL,
				       TA.POST_CODE,
				       TA.ADDRESS,
				       TC.RESIDENT_TYPE,
				       TC.MARRIAGE_STATUS,
				       TCB.BENE_TYPE,
				       TCB.COMPANY_ID,
				       TCB.BENE_KIND
				  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BENE TCB
				    ON TCM.POLICY_ID = TCB.POLICY_ID
				 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TC
				    ON TCB.CUSTOMER_ID = TC.CUSTOMER_ID
				  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TA
				    ON TCB.ADDRESS_ID = TA.ADDRESS_ID
				 WHERE 1 = 1
				   AND TCM.POLICY_CODE = #{policy_code}
         ]]>
	</select>

</mapper>