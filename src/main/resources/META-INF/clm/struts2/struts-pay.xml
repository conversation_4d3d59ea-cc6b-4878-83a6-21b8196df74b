<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.3//EN" "http://struts.apache.org/dtds/struts-2.3.dtd">
<struts>
	<package name="struts-pay"  extends="default" namespace="/clm/pay" >
		<action name="*_*" method="{1}" class="{2}">
		<!-- add by dengjyit 付费信息变更 -->
		<!-- 付费变更申请主界面 -->
		<result name="showClaimPayChange">/clm/pages/pay/claimPayChange.jsp</result>
		<!-- 审核页面 -->
		<result name="showClaimPayChangeCheck">/clm/pages/pay/claimPayChangeCheck.jsp</result>
		<!-- 付费变更申请查询 -->
		<result name="findClaimPayChange">/clm/pages/pay/claimPayChange.jsp</result>
		<!-- 审核页面 -->
		<result name="findClaimPayChangeCheck">/clm/pages/pay/claimPayChangeCheck.jsp</result>
		<!-- 付费变更修改确认 -->
		<result name="saveChangeInfo">/clm/pages/pay/claimPayChange.jsp</result>
		<!-- 转扫描 -->
		<result name="toScan">/clm/pages/pay/claimPayChange.jsp</result>
		<!-- 转验真 -->
		<result name="toConfirm">/clm/pages/pay/claimPayChange.jsp</result>
		<!-- 付费变更轨迹查询 -->
		<result name="showPayChangeTrace">/clm/pages/pay/payChangeTrace.jsp</result>
		<!-- end -->
		
		<!-- add by wangcx1_wb 付费变更进入法人信息录入页面 -->
		<result name="legalPersonInfoPagePay">/clm/pages/legalPerson/legalPersonInfoPagePay.jsp</result>
		</action>
	</package>
</struts>