<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimCasePO">
	<sql id="claimCaseWhereConditionForPage">
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no, jdbcType=VARCHAR} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ]]></if>
		<if test=" case_status != null and case_status != ''  "><![CDATA[ AND A.CASE_STATUS = #{case_status, jdbcType=VARCHAR} ]]></if>
		<if test=" related_no != null and related_no != ''  "><![CDATA[ AND A.RELATED_NO = #{related_no} ]]></if>
	</sql>
	
	<sql id="claimCaseWhereCondition">
		<if test=" trustee_certi_code != null and trustee_certi_code != ''  "><![CDATA[ AND A.TRUSTEE_CERTI_CODE = #{trustee_certi_code} ]]></if>
		<if test=" end_case_time  != null  and  end_case_time  != ''  "><![CDATA[ AND A.END_CASE_TIME = #{end_case_time} ]]></if>
		<if test=" actual_pay  != null "><![CDATA[ AND A.ACTUAL_PAY = #{actual_pay} ]]></if>
		<if test=" report_mode  != null "><![CDATA[ AND A.REPORT_MODE = #{report_mode} ]]></if>
		<if test=" approve_reject_reason != null and approve_reject_reason != ''  "><![CDATA[ AND A.APPROVE_REJECT_REASON = #{approve_reject_reason} ]]></if>
		<if test=" related_no != null and related_no != ''  "><![CDATA[ AND A.RELATED_NO = #{related_no} ]]></if>
		<if test=" rptr_relation != null and rptr_relation != ''  "><![CDATA[ AND A.RPTR_RELATION = #{rptr_relation} ]]></if>
		<if test=" over_comp_flag  != null "><![CDATA[ AND A.OVER_COMP_FLAG = #{over_comp_flag} ]]></if>
		<if test=" other_reason != null and other_reason != ''  "><![CDATA[ AND A.OTHER_REASON = #{other_reason} ]]></if>
		<if test=" repeal_reason  != null "><![CDATA[ AND A.REPEAL_REASON = #{repeal_reason} ]]></if>
		<if test=" is_common  != null "><![CDATA[ AND A.IS_COMMON = #{is_common} ]]></if>
		<if test=" cure_hospital != null and cure_hospital != ''  "><![CDATA[ AND A.CURE_HOSPITAL = #{cure_hospital} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" review_flag  != null "><![CDATA[ AND A.REVIEW_FLAG = #{review_flag} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" advance_ask_flag  != null "><![CDATA[ AND A.ADVANCE_ASK_FLAG = #{advance_ask_flag} ]]></if>
		<if test=" case_flag  != null "><![CDATA[ AND A.CASE_FLAG = #{case_flag} ]]></if>
		<if test=" green_flag  != null "><![CDATA[ AND A.GREEN_FLAG = #{green_flag} ]]></if>
		<if test=" audit_reject_reason != null and audit_reject_reason != ''  "><![CDATA[ AND A.AUDIT_REJECT_REASON = #{audit_reject_reason} ]]></if>
		<if test=" balance_pay  != null "><![CDATA[ AND A.BALANCE_PAY = #{balance_pay} ]]></if>
		<if test=" reject_reason != null and reject_reason != ''  "><![CDATA[ AND A.REJECT_REASON = #{reject_reason} ]]></if>
		<if test=" auditor_id  != null "><![CDATA[ AND A.AUDITOR_ID = #{auditor_id} ]]></if>
		<if test=" rptr_mp != null and rptr_mp != ''  "><![CDATA[ AND A.RPTR_MP = #{rptr_mp} ]]></if>
		<if test=" serious_disease != null and serious_disease != ''  "><![CDATA[ AND A.SERIOUS_DISEASE = #{serious_disease} ]]></if>
		<if test=" registe_time  != null  and  registe_time  != ''  "><![CDATA[ AND A.REGISTE_TIME = #{registe_time} ]]></if>
		<if test=" case_apply_type  != null "><![CDATA[ AND A.CASE_APPLY_TYPE = #{case_apply_type} ]]></if>
		<if test=" repeal_desc != null and repeal_desc != ''  "><![CDATA[ AND A.REPEAL_DESC = #{repeal_desc} ]]></if>
		<if test=" rptr_email != null and rptr_email != ''  "><![CDATA[ AND A.RPTR_EMAIL = #{rptr_email} ]]></if>
		<if test=" trustee_certi_type != null and trustee_certi_type != ''  "><![CDATA[ AND A.TRUSTEE_CERTI_TYPE = #{trustee_certi_type} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" accident_detail != null and accident_detail != ''  "><![CDATA[ AND A.ACCIDENT_DETAIL = #{accident_detail} ]]></if>
		<if test=" case_status != null and case_status != ''  "><![CDATA[ AND A.CASE_STATUS = #{case_status} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" rptr_time  != null  and  rptr_time  != ''  "><![CDATA[ AND A.RPTR_TIME = #{rptr_time} ]]></if>
		<if test=" audit_decision  != null "><![CDATA[ AND A.AUDIT_DECISION = #{audit_decision} ]]></if>
		<if test=" register_id  != null "><![CDATA[ AND A.REGISTER_ID = #{register_id} ]]></if>
		<if test=" claim_source  != null "><![CDATA[ AND A.CLAIM_SOURCE = #{claim_source} ]]></if>
		<if test=" trustee_type  != null "><![CDATA[ AND A.TRUSTEE_TYPE = #{trustee_type} ]]></if>
		<if test=" approver_id  != null "><![CDATA[ AND A.APPROVER_ID = #{approver_id} ]]></if>
		<if test=" accident_id  != null "><![CDATA[ AND A.ACCIDENT_ID = #{accident_id} ]]></if>
		<if test=" reject_pay  != null "><![CDATA[ AND A.REJECT_PAY = #{reject_pay} ]]></if>
		<if test=" sign_time  != null  and  sign_time  != ''  "><![CDATA[ AND A.SIGN_TIME = #{sign_time} ]]></if>
		<if test=" signer_id  != null "><![CDATA[ AND A.SIGNER_ID = #{signer_id} ]]></if>
		<if test=" advance_pay  != null "><![CDATA[ AND A.ADVANCE_PAY = #{advance_pay} ]]></if>
		<if test=" rptr_name != null and rptr_name != ''  "><![CDATA[ AND A.RPTR_NAME = #{rptr_name} ]]></if>
		<if test=" rptr_addr != null and rptr_addr != ''  "><![CDATA[ AND A.RPTR_ADDR = #{rptr_addr} ]]></if>
		<if test=" rptr_id  != null "><![CDATA[ AND A.RPTR_ID = #{rptr_id} ]]></if>
		<if test=" audit_permission_name != null and audit_permission_name != ''  "><![CDATA[ AND A.AUDIT_PERMISSION_NAME = #{audit_permission_name} ]]></if>
		<if test=" approve_remark != null and approve_remark != ''  "><![CDATA[ AND A.APPROVE_REMARK = #{approve_remark} ]]></if>
		<if test=" trustee_name != null and trustee_name != ''  "><![CDATA[ AND A.TRUSTEE_NAME = #{trustee_name} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" registe_conf_time  != null  and  registe_conf_time  != ''  "><![CDATA[ AND A.REGISTE_CONF_TIME = #{registe_conf_time} ]]></if>
		<if test=" trustee_tel != null and trustee_tel != ''  "><![CDATA[ AND A.TRUSTEE_TEL = #{trustee_tel} ]]></if>
		<if test=" rptr_zip  != null "><![CDATA[ AND A.RPTR_ZIP = #{rptr_zip} ]]></if>
		<if test=" is_bpo  != null "><![CDATA[ AND A.IS_BPO = #{is_bpo} ]]></if>
		<if test=" audit_time  != null  and  audit_time  != ''  "><![CDATA[ AND A.AUDIT_TIME = #{audit_time} ]]></if>
		<if test=" approve_time  != null  and  approve_time  != ''  "><![CDATA[ AND A.APPROVE_TIME = #{approve_time} ]]></if>
		<if test=" audit_remark != null and audit_remark != ''  "><![CDATA[ AND A.AUDIT_REMARK = #{audit_remark} ]]></if>
		<if test=" approve_permission_name != null and approve_permission_name != ''  "><![CDATA[ AND A.APPROVE_PERMISSION_NAME = #{approve_permission_name} ]]></if>
		<if test=" approve_decision  != null "><![CDATA[ AND A.APPROVE_DECISION = #{approve_decision} ]]></if>
		<if test=" trustee_code != null and trustee_code != ''  "><![CDATA[ AND A.TRUSTEE_CODE = #{trustee_code} ]]></if>
		<if test=" door_sign_time  != null  and  door_sign_time  != ''  "><![CDATA[ AND A.DOOR_SIGN_TIME = #{door_sign_time} ]]></if>
		<if test=" comfort_flag  != null "><![CDATA[ AND A.COMFORT_FLAG = #{comfort_flag} ]]></if>
		<if test=" accept_time  != null  and  accept_time  != ''  "><![CDATA[ AND A.ACCEPT_TIME = #{accept_time} ]]></if>
		<if test=" is_deduct_flag  != null "><![CDATA[ AND A.IS_DEDUCT_FLAG = #{is_deduct_flag} ]]></if>
		<if test=" med_dept != null and med_dept != ''  "><![CDATA[ AND A.MED_DEPT = #{med_dept} ]]></if>
		<if test=" doctor_name != null and doctor_name != ''  "><![CDATA[ AND A.DOCTOR_NAME = #{doctor_name} ]]></if>
		<if test=" accept_decision  != null "><![CDATA[ AND A.ACCEPT_DECISION = #{accept_decision} ]]></if>
		<if test=" trustee_mp != null and trustee_mp != ''  "><![CDATA[ AND A.TRUSTEE_MP = #{trustee_mp} ]]></if>
		<if test=" cure_status != null and cure_status != ''  "><![CDATA[ AND A.CURE_STATUS = #{cure_status} ]]></if>
		<if test=" acceptor_id  != null "><![CDATA[ AND A.ACCEPTOR_ID = #{acceptor_id} ]]></if>
		<if test=" case_sub_status != null and case_sub_status != ''  "><![CDATA[ AND A.CASE_SUB_STATUS = #{case_sub_status} ]]></if>
		<if test=" calc_pay  != null "><![CDATA[ AND A.CALC_PAY = #{calc_pay} ]]></if>
		<if test=" survey_result_info != null and survey_result_info != ''  "><![CDATA[ AND A.SURVEY_RESULT_INFO = #{survey_result_info} ]]></if>
		<if test=" medical_amount_criterion != null and medical_amount_criterion != ''  "><![CDATA[ AND A.MEDICAL_AMOUNT_CRITERION = #{medical_amount_criterion} ]]></if>
		<if test=" is_migration != null"><![CDATA[ AND A.IS_MIGRATION = #{is_migration} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" medical_total_mony != null"><![CDATA[ AND A.MEDICAL_TOTAL_MONY = #{medical_total_mony} ]]></if>
		<if test=" medical_amount_flag != null"><![CDATA[ AND A.MEDICAL_AMOUNT_FLAG = #{medical_amount_flag} ]]></if>
		<if test=" bene_count != null"><![CDATA[ AND A.BENE_COUNT = #{bene_count} ]]></if>
		<if test=" sign_user_type != null"><![CDATA[ AND A.SIGN_USER_TYPE = #{sign_user_type} ]]></if>
		<if test=" sign_user_code != null"><![CDATA[ AND A.SIGN_USER_CODE = #{sign_user_code} ]]></if>
		<if test=" sign_user_name != null"><![CDATA[ AND A.SIGN_USER_NAME = #{sign_user_name} ]]></if>		
		<if test=" pre_audit_id != null"><![CDATA[ AND A.PRE_AUDIT_ID = #{pre_audit_id} ]]></if>
		<if test=" re_audit_decision != null"><![CDATA[ AND A.RE_AUDIT_DECISION = #{re_audit_decision} ]]></if>
		<if test=" re_audit_opinion != null"><![CDATA[ AND A.RE_AUDIT_OPINION = #{re_audit_opinion} ]]></if>		
		<if test=" inverse_risk_level != null"><![CDATA[ AND A.INVERSE_RISK_LEVEL = #{inverse_risk_level} ]]></if>
		<if test=" invoice_risk_level != null"><![CDATA[ AND A.INVOICE_RISK_LEVEL = #{invoice_risk_level} ]]></if>
		<if test=" case_risk_level != null"><![CDATA[ AND A.CASE_RISK_LEVEL = #{case_risk_level} ]]></if>
		<if test=" realtime_pay != null"><![CDATA[ AND A.REALTIME_PAY = #{realtime_pay} ]]></if>
		<if test=" material_free_flag != null"><![CDATA[ AND A.MATERIAL_FREE_FLAG = #{material_free_flag} ]]></if>
		<if test=" medical_connection_flag != null"><![CDATA[ AND A.MEDICAL_CONNECTION_FLAG = #{medical_connection_flag} ]]></if>
		<if test=" salesman_self_insurance != null"><![CDATA[ AND A.SALESMAN_SELF_INSURANCE = #{salesman_self_insurance} ]]></if>
		<if test=" sms_send_flag != null"><![CDATA[ AND A.SMS_SEND_FLAG = #{sms_send_flag} ]]></if>
		<if test=" is_small_case != null"><![CDATA[ AND A.IS_SMALL_CASE = #{is_small_case} ]]></if>
		<if test=" is_problem_solve != null"><![CDATA[ AND A.IS_PROBLEM_SOLVE = #{is_problem_solve} ]]></if>
		<if test=" acquist_way != null"><![CDATA[ AND A.ACQUIST_WAY = #{acquist_way} ]]></if>
		<if test=" is_deduct_prem != null"><![CDATA[ AND A.IS_DEDUCT_PREM = #{is_deduct_prem} ]]></if>
		<if test=" claim_case_id != null"><![CDATA[ AND A.CLAIM_CASE_ID = #{claim_case_id} ]]></if>
		<if test=" claim_order != null"><![CDATA[ AND A.CLAIM_ORDER = #{claim_order} ]]></if>
		<if test=" claim_order_audit != null"><![CDATA[ AND A.CLAIM_ORDER_AUDIT = #{claim_order_audit} ]]></if>
		<if test=" elec_check_flag != null"><![CDATA[ AND A.ELEC_CHECK_FLAG = #{elec_check_flag} ]]></if>
		<if test=" auto_case_no != null and auto_case_no != '' "><![CDATA[ AND A.AUTO_CASE_NO = #{auto_case_no} ]]></if>
		<if test=" auto_case_type != null "><![CDATA[ AND A.AUTO_CASE_TYPE = #{auto_case_type} ]]></if>
		<if test=" lock_flag != null "><![CDATA[ AND A.LOCK_FLAG = #{lock_flag} ]]></if>
		<if test=" lock_sys != null and lock_sys != '' "><![CDATA[ AND A.LOCK_SYS = #{lock_sys} ]]></if>
		<if test=" servcom != null and servcom != '' "><![CDATA[ AND A.SERVCOM = #{servcom} ]]></if>
		<if test=" first_rptr_time != null and first_rptr_time != '' "><![CDATA[ AND A.FIRST_RPTR_TIME = #{first_rptr_time} ]]></if>
		<if test=" report_source != null and report_source != '' "><![CDATA[ AND A.report_source = #{report_source} ]]></if>
		<if test=" is_called_back != null "><![CDATA[ AND A.IS_CALLED_BACK = #{is_called_back} ]]></if>
		<if test=" job_prompt != null and job_prompt != '' "><![CDATA[ AND A.JOB_PROMPT = #{job_prompt} ]]></if>
		<if test=" notregist_doc_flag != null and notregist_doc_flag != '' "><![CDATA[ AND A.NOTREGIST_DOC_FLAG = #{notregist_doc_flag} ]]></if>
		<if test=" un_doc_reason != null and un_doc_reason != '' "><![CDATA[ AND A.UN_DOC_REASON = #{un_doc_reason} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="queryClaimCaseByCaseIdCondition">
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
	</sql>
	

	
	<!-- 根据赔案号变更轨迹表-->
	<select id="findClaimChangTrackByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
	      	<![CDATA[ SELECT A.CASE_ID,
		       A.CASE_NO,
		       A.TASK_NUM,
		       A.TASK_NAME,
		       A.ORIGI_OPERATOR_ID,
		       A.ORIGI_OPERATOR_ORG,
		       A.CHANGE_OPERATOR_ID,
		       A.CHANGE_OPERATOR_ORG,
		       A.CHANGE_TIME,
		       A.CHANGER_ID
		  FROM APP___CLM__DBUSER.T_CLAIM_CHANGE_TRACK A WHERE 1=1  ]]>
             <if test="case_no != null and case_no != ''">
                 AND A.CASE_NO = #{case_no}
             </if>
            <![CDATA[ ORDER BY A.INSERT_TIME ASC ]]>
	</select>
	
	
<!-- 增加任务轨迹表 -->
	<insert id="addClaimChangetrack"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="change_track_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_CHANGE_TRACK__CHANGE_T.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_CHANGE_TRACK(change_track_id,CASE_ID,CASE_NO,TASK_NUM,TASK_NAME,ORIGI_OPERATOR_ID,ORIGI_OPERATOR_ORG,
			CHANGE_OPERATOR_ID,CHANGE_OPERATOR_ORG,CHANGE_TIME,CHANGER_ID,INSERT_BY,INSERT_TIME,INSERT_TIMESTAMP,UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP)
	VALUES(
	#{change_track_id,jdbcType=NUMERIC},
	#{case_id, jdbcType=NUMERIC},
	#{case_no, jdbcType=VARCHAR},
	#{task_num, jdbcType=VARCHAR},
	#{task_name, jdbcType=VARCHAR},
	#{origi_operator_id, jdbcType=NUMERIC},
	#{origi_operator_org, jdbcType=VARCHAR},
	#{change_operator_id, jdbcType=NUMERIC},
	#{change_operator_org, jdbcType=VARCHAR},
	#{change_time, jdbcType=TIMESTAMP},
	#{changer_id, jdbcType=NUMERIC},
	#{insert_by, jdbcType=NUMERIC},
	SYSDATE,
	SYSDATE,
	#{update_by, jdbcType=NUMERIC},
	SYSDATE,
	SYSDATE
)
		 ]]>
	</insert>
	
<!-- 添加操作 -->
	<insert id="addClaimCase"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="case_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_CASE__CASE_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_CASE(
				TRUSTEE_CERTI_CODE, END_CASE_TIME, ACTUAL_PAY, REPORT_MODE, APPROVE_REJECT_REASON, RELATED_NO, RPTR_RELATION, 
				OVER_COMP_FLAG, OTHER_REASON, REPEAL_REASON, IS_COMMON, CURE_HOSPITAL, CASE_NO, REVIEW_FLAG, 
				ORGAN_CODE, ADVANCE_ASK_FLAG, CASE_FLAG, UPDATE_BY, GREEN_FLAG, AUDIT_REJECT_REASON, BALANCE_PAY, 
				REJECT_REASON, AUDITOR_ID, RPTR_MP, SERIOUS_DISEASE, REGISTE_TIME, CASE_APPLY_TYPE, UPDATE_TIME, 
				REPEAL_DESC, RPTR_EMAIL, TRUSTEE_CERTI_TYPE, INSURED_ID, ACCIDENT_DETAIL, CASE_STATUS, CASE_ID, 
				RPTR_TIME, AUDIT_DECISION, REGISTER_ID, CLAIM_SOURCE, TRUSTEE_TYPE, UPDATE_TIMESTAMP, APPROVER_ID, 
				INSERT_BY, ACCIDENT_ID, REJECT_PAY, SIGN_TIME, SIGNER_ID, ADVANCE_PAY, RPTR_NAME, 
				RPTR_ADDR, RPTR_ID, AUDIT_PERMISSION_NAME, APPROVE_REMARK, TRUSTEE_NAME, APPLY_DATE, ADVANCE_FLAG, 
				REGISTE_CONF_TIME, TRUSTEE_TEL, RPTR_ZIP, IS_BPO, INSERT_TIMESTAMP, AUDIT_TIME, APPROVE_TIME, 
				AUDIT_REMARK, APPROVE_PERMISSION_NAME, APPROVE_DECISION, TRUSTEE_CODE, INSERT_TIME, DOOR_SIGN_TIME, COMFORT_FLAG, 
				ACCEPT_TIME, IS_DEDUCT_FLAG, MED_DEPT, DOCTOR_NAME, ACCEPT_DECISION, TRUSTEE_MP, CURE_STATUS, 
				ACCEPTOR_ID, CASE_SUB_STATUS, CALC_PAY, SURVEY_RESULT_INFO, SPECIAL_REMARK_CODE, LOSS_REASON_CODE, LOSS_LEVEL_CODE, 
				IS_MIGRATION,SIGN_ORGAN,REGISTE_ORGAN,AUDIT_ORGAN,APPROVE_ORGAN,EASY_AUDIT_DECISION,EASY_AUDITOR_ID,
			    MEDICAL_AMOUNT_CRITERION,MEDICAL_TOTAL_MONY,MEDICAL_AMOUNT_FLAG,REMARK,AUDIT_INDIVIDUAL_POOL_TIME,APPROVE_INDIVIDUAL_POOL_TIME,BENE_COUNT,SIGN_USER_TYPE,SIGN_USER_CODE,SIGN_USER_NAME, REALTIME_PAY, 
			    INVERSE_RISK_LEVEL,INVOICE_RISK_LEVEL,CASE_RISK_LEVEL,MATERIAL_FREE_FLAG,MEDICAL_CONNECTION_FLAG,SALESMAN_SELF_INSURANCE,CHANNEL_CODE,IS_CHECKLIST,AGENT_RISK_LEVEL,CUSTOMER_RISK_LEVEL,EARLY_WARNING,DIAGNOSIS_TIME,SIGNATURE_TRACE_FLAG,FACE_RECOGNITION_FLAG,
			    SMS_SEND_FLAG,ASSIGNEE_START_DATE,ASSIGNEE_END_DATE
			    ,MEDICAL_NUM,TREAT_DATE,HOSPITAL_NUM,CHANGE_STATUS_TIME,INSURANCE_PAID_FLAG,IS_SMALL_CASE,IS_PROBLEM_SOLVE,ACQUIST_WAY,IS_DEDUCT_PREM,CLAIM_CASE_ID,CLAIM_ORDER,CLAIM_ORDER_AUDIT,ELEC_CHECK_FLAG,AUTO_CASE_NO,
			    AUTO_CASE_TYPE,LOCK_FLAG,LOCK_SYS,SERVCOM,FIRST_RPTR_TIME,is_called_back,report_source,job_prompt,notregist_doc_flag,un_doc_reason) 
			VALUES (
				#{trustee_certi_code, jdbcType=VARCHAR}, #{end_case_time, jdbcType=TIMESTAMP} , #{actual_pay, jdbcType=NUMERIC} , #{report_mode, jdbcType=NUMERIC} , #{approve_reject_reason, jdbcType=VARCHAR} , #{related_no, jdbcType=VARCHAR} , #{rptr_relation, jdbcType=VARCHAR} 
				, #{over_comp_flag, jdbcType=NUMERIC} , #{other_reason, jdbcType=VARCHAR} , #{repeal_reason, jdbcType=NUMERIC} , #{is_common, jdbcType=NUMERIC} , #{cure_hospital, jdbcType=VARCHAR} , #{case_no, jdbcType=VARCHAR} , #{review_flag, jdbcType=NUMERIC} 
				, #{organ_code, jdbcType=VARCHAR} , #{advance_ask_flag, jdbcType=NUMERIC} , #{case_flag, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{green_flag, jdbcType=NUMERIC} , #{audit_reject_reason, jdbcType=VARCHAR} , #{balance_pay, jdbcType=NUMERIC} 
				, #{reject_reason, jdbcType=VARCHAR} , #{auditor_id, jdbcType=NUMERIC} , #{rptr_mp, jdbcType=VARCHAR} , #{serious_disease, jdbcType=VARCHAR} , #{registe_time, jdbcType=TIMESTAMP} , #{case_apply_type, jdbcType=NUMERIC} , SYSDATE 
				, #{repeal_desc, jdbcType=VARCHAR} , #{rptr_email, jdbcType=VARCHAR} , #{trustee_certi_type, jdbcType=VARCHAR} , #{insured_id, jdbcType=NUMERIC} , #{accident_detail, jdbcType=VARCHAR} , #{case_status, jdbcType=VARCHAR} , #{case_id, jdbcType=NUMERIC} 
				, #{rptr_time, jdbcType=TIMESTAMP} , #{audit_decision, jdbcType=NUMERIC} , #{register_id, jdbcType=NUMERIC} , #{claim_source, jdbcType=NUMERIC} , #{trustee_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{approver_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{accident_id, jdbcType=NUMERIC} , #{reject_pay, jdbcType=NUMERIC} , #{sign_time, jdbcType=TIMESTAMP} , #{signer_id, jdbcType=NUMERIC} , #{advance_pay, jdbcType=NUMERIC} , #{rptr_name, jdbcType=VARCHAR} 
				, #{rptr_addr, jdbcType=VARCHAR} , #{rptr_id, jdbcType=NUMERIC} , #{audit_permission_name, jdbcType=VARCHAR} , #{approve_remark, jdbcType=VARCHAR} , #{trustee_name, jdbcType=VARCHAR} , #{apply_date, jdbcType=TIMESTAMP} , #{advance_flag, jdbcType=NUMERIC} 
				, #{registe_conf_time, jdbcType=TIMESTAMP} , #{trustee_tel, jdbcType=VARCHAR} , #{rptr_zip, jdbcType=NUMERIC} , #{is_bpo, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{audit_time, jdbcType=TIMESTAMP} , #{approve_time, jdbcType=TIMESTAMP} 
				, #{audit_remark, jdbcType=VARCHAR} , #{approve_permission_name, jdbcType=VARCHAR} , #{approve_decision, jdbcType=NUMERIC} , #{trustee_code, jdbcType=VARCHAR} , SYSDATE , #{door_sign_time, jdbcType=TIMESTAMP} , #{comfort_flag, jdbcType=NUMERIC} 
				, #{accept_time, jdbcType=TIMESTAMP} , #{is_deduct_flag, jdbcType=NUMERIC} , #{med_dept, jdbcType=VARCHAR} , #{doctor_name, jdbcType=VARCHAR} , #{accept_decision, jdbcType=NUMERIC} , #{trustee_mp, jdbcType=VARCHAR} , #{cure_status, jdbcType=VARCHAR} 
				, #{acceptor_id, jdbcType=NUMERIC} , #{case_sub_status, jdbcType=VARCHAR} , #{calc_pay, jdbcType=NUMERIC} , #{survey_result_info, jdbcType=VARCHAR}, #{special_remark_code, jdbcType=VARCHAR}, #{loss_reason_code, jdbcType=VARCHAR}, #{loss_level_code, jdbcType=VARCHAR}
				, #{is_migration, jdbcType=NUMERIC},#{sign_organ, jdbcType=VARCHAR},#{registe_organ, jdbcType=VARCHAR},#{audit_organ, jdbcType=VARCHAR},#{approve_organ, jdbcType=VARCHAR}, #{easy_audit_decision, jdbcType=NUMERIC}, #{easy_auditor_id, jdbcType=NUMERIC}
				, #{medical_amount_criterion, jdbcType=VARCHAR},#{medical_total_mony, jdbcType=NUMERIC},#{medical_amount_flag, jdbcType=NUMERIC},#{remark, jdbcType=VARCHAR}, #{audit_individual_pool_time, jdbcType=TIMESTAMP}, #{approve_individual_pool_time, jdbcType=TIMESTAMP}
				, #{bene_count, jdbcType=NUMERIC}, #{sign_user_type, jdbcType=NUMERIC}, #{sign_user_code, jdbcType=VARCHAR}, #{sign_user_name, jdbcType=VARCHAR}, #{realtime_pay, jdbcType=NUMERIC},#{inverse_risk_level, jdbcType=VARCHAR},#{invoice_risk_level, jdbcType=VARCHAR},#{case_risk_level, jdbcType=VARCHAR},#{material_free_flag, jdbcType=NUMERIC},#{medical_connection_flag, jdbcType=NUMERIC},#{salesman_self_insurance, jdbcType=NUMERIC}
				, #{channel_code, jdbcType=VARCHAR}, #{is_checklist, jdbcType=NUMERIC}
				, #{agent_risk_level, jdbcType=VARCHAR}, #{customer_risk_level, jdbcType=VARCHAR}, #{early_warning, jdbcType=NUMERIC}
				, #{diagnosis_time, jdbcType=DATE},#{signature_trace_flag, jdbcType=NUMERIC},#{face_recognition_flag, jdbcType=NUMERIC},#{sms_send_flag, jdbcType=NUMERIC},#{assignee_start_date, jdbcType=TIMESTAMP},#{assignee_end_date, jdbcType=TIMESTAMP}
				, #{medical_num, jdbcType=VARCHAR}, #{treat_date, jdbcType=DATE}, #{hospital_num, jdbcType=VARCHAR}, #{change_status_time, jdbcType=DATE}, #{insurance_paid_flag, jdbcType=VARCHAR}, #{is_small_case, jdbcType=NUMERIC}, #{is_problem_solve, jdbcType=NUMERIC}, #{acquist_way, jdbcType=NUMERIC}, #{is_deduct_prem, jdbcType=NUMERIC},#{claim_case_id, jdbcType=NUMERIC},#{claim_order, jdbcType=NUMERIC},#{claim_order_audit, jdbcType=NUMERIC},#{elec_check_flag, jdbcType=NUMERIC}
				, #{auto_case_no, jdbcType=VARCHAR}, #{auto_case_type, jdbcType=NUMERIC},#{lock_flag, jdbcType=NUMERIC},#{lock_sys, jdbcType=VARCHAR},#{servcom, jdbcType=VARCHAR},#{first_rptr_time, jdbcType=TIMESTAMP},#{is_called_back, jdbcType=NUMERIC},#{report_source ,jdbcType=VARCHAR},#{job_prompt,jdbcType=VARCHAR}
				, #{notregist_doc_flag,jdbcType=NUMERIC},#{un_doc_reason,jdbcType=VARCHAR})
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimCase" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_CASE WHERE CASE_ID = #{case_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimCase" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			TRUSTEE_CERTI_CODE = #{trustee_certi_code, jdbcType=VARCHAR} ,
		    END_CASE_TIME = #{end_case_time, jdbcType=TIMESTAMP} ,
		    ACTUAL_PAY = #{actual_pay, jdbcType=NUMERIC} ,
		    REPORT_MODE = #{report_mode, jdbcType=NUMERIC} ,
			APPROVE_REJECT_REASON = #{approve_reject_reason, jdbcType=VARCHAR} ,
			RELATED_NO = #{related_no, jdbcType=VARCHAR} ,
			RPTR_RELATION = #{rptr_relation, jdbcType=VARCHAR} ,
		    OVER_COMP_FLAG = #{over_comp_flag, jdbcType=NUMERIC} ,
			OTHER_REASON = #{other_reason, jdbcType=VARCHAR} ,
		    REPEAL_REASON = #{repeal_reason, jdbcType=NUMERIC} ,
		    IS_COMMON = #{is_common, jdbcType=NUMERIC} ,
			CURE_HOSPITAL = #{cure_hospital, jdbcType=VARCHAR} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
		    REVIEW_FLAG = #{review_flag, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    ADVANCE_ASK_FLAG = #{advance_ask_flag, jdbcType=NUMERIC} ,
		    CASE_FLAG = #{case_flag, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    GREEN_FLAG = #{green_flag, jdbcType=NUMERIC} ,
			AUDIT_REJECT_REASON = #{audit_reject_reason, jdbcType=VARCHAR} ,
		    BALANCE_PAY = #{balance_pay, jdbcType=NUMERIC} ,
			REJECT_REASON = #{reject_reason, jdbcType=VARCHAR} ,
		    AUDITOR_ID = #{auditor_id, jdbcType=NUMERIC} ,
		    EASY_AUDITOR_ID = #{easy_auditor_id, jdbcType=NUMERIC} ,
			RPTR_MP = #{rptr_mp, jdbcType=VARCHAR} ,
			SERIOUS_DISEASE = #{serious_disease, jdbcType=VARCHAR} ,
		    REGISTE_TIME = #{registe_time, jdbcType=TIMESTAMP} ,
		    CASE_APPLY_TYPE = #{case_apply_type, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			REPEAL_DESC = #{repeal_desc, jdbcType=VARCHAR} ,
			RPTR_EMAIL = #{rptr_email, jdbcType=VARCHAR} ,
			TRUSTEE_CERTI_TYPE = #{trustee_certi_type, jdbcType=VARCHAR} ,
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
			ACCIDENT_DETAIL = #{accident_detail, jdbcType=VARCHAR} ,
			CASE_STATUS = #{case_status, jdbcType=VARCHAR} ,
		    RPTR_TIME = #{rptr_time, jdbcType=TIMESTAMP} ,
		    AUDIT_DECISION = #{audit_decision, jdbcType=NUMERIC} ,
		    EASY_AUDIT_DECISION = #{easy_audit_decision, jdbcType=NUMERIC} ,
		    REGISTER_ID = #{register_id, jdbcType=NUMERIC} ,
		    CLAIM_SOURCE = #{claim_source, jdbcType=NUMERIC} ,
		    TRUSTEE_TYPE = #{trustee_type, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    APPROVER_ID = #{approver_id, jdbcType=NUMERIC} ,
		    ACCIDENT_ID = #{accident_id, jdbcType=NUMERIC} ,
		    REJECT_PAY = #{reject_pay, jdbcType=NUMERIC} ,
		    SIGN_TIME = #{sign_time, jdbcType=TIMESTAMP} ,
		    SIGNER_ID = #{signer_id, jdbcType=NUMERIC} ,
		    ADVANCE_PAY = #{advance_pay, jdbcType=NUMERIC} ,
			RPTR_NAME = #{rptr_name, jdbcType=VARCHAR} ,
			RPTR_ADDR = #{rptr_addr, jdbcType=VARCHAR} ,
		    RPTR_ID = #{rptr_id, jdbcType=NUMERIC} ,
			AUDIT_PERMISSION_NAME = #{audit_permission_name, jdbcType=VARCHAR} ,
			APPROVE_REMARK = #{approve_remark, jdbcType=VARCHAR} ,
			TRUSTEE_NAME = #{trustee_name, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=TIMESTAMP} ,
		    ADVANCE_FLAG = #{advance_flag, jdbcType=NUMERIC} ,
		    REGISTE_CONF_TIME = #{registe_conf_time, jdbcType=TIMESTAMP} ,
			TRUSTEE_TEL = #{trustee_tel, jdbcType=VARCHAR} ,
		    RPTR_ZIP = #{rptr_zip, jdbcType=NUMERIC} ,
		    IS_BPO = #{is_bpo, jdbcType=NUMERIC} ,
		    AUDIT_TIME = #{audit_time, jdbcType=TIMESTAMP} ,
		    AUDIT_START_TIME = #{audit_start_time, jdbcType=TIMESTAMP} ,
		    APPROVE_TIME = #{approve_time, jdbcType=TIMESTAMP} ,
			AUDIT_REMARK = #{audit_remark, jdbcType=VARCHAR} ,
			APPROVE_PERMISSION_NAME = #{approve_permission_name, jdbcType=VARCHAR} ,
		    APPROVE_DECISION = #{approve_decision, jdbcType=NUMERIC} ,
			TRUSTEE_CODE = #{trustee_code, jdbcType=VARCHAR} ,
		    DOOR_SIGN_TIME = #{door_sign_time, jdbcType=TIMESTAMP} ,
		    COMFORT_FLAG = #{comfort_flag, jdbcType=NUMERIC} ,
		    ACCEPT_TIME = #{accept_time, jdbcType=TIMESTAMP} ,
		    IS_DEDUCT_FLAG = #{is_deduct_flag, jdbcType=NUMERIC} ,
			MED_DEPT = #{med_dept, jdbcType=VARCHAR} ,
			DOCTOR_NAME = #{doctor_name, jdbcType=VARCHAR} ,
		    ACCEPT_DECISION = #{accept_decision, jdbcType=NUMERIC} ,
			TRUSTEE_MP = #{trustee_mp, jdbcType=VARCHAR} ,
			CURE_STATUS = #{cure_status, jdbcType=VARCHAR} ,
		    ACCEPTOR_ID = #{acceptor_id, jdbcType=NUMERIC} ,
			CASE_SUB_STATUS = #{case_sub_status, jdbcType=VARCHAR} ,
		    CALC_PAY = #{calc_pay, jdbcType=NUMERIC} ,
			SURVEY_RESULT_INFO = #{survey_result_info, jdbcType=VARCHAR} ,
			CHANNEL_CODE = #{channel_code, jdbcType=VARCHAR},
			IS_AUTO_HUNGUP = #{is_auto_hungup, jdbcType=NUMERIC},
			SPECIAL_REMARK_CODE = #{special_remark_code, jdbcType=VARCHAR}, 
			LOSS_REASON_CODE = #{loss_reason_code, jdbcType=VARCHAR}, 
			LOSS_LEVEL_CODE = #{loss_level_code, jdbcType=VARCHAR},
			COMFORT_STATUS = #{comfort_status, jdbcType=NUMERIC},
			IS_MIGRATION = #{is_migration, jdbcType=NUMERIC},
			SEND_BENE_DOC_FLAG=#{send_bene_doc_flag, jdbcType=NUMERIC},
			SIGN_ORGAN = #{sign_organ, jdbcType=VARCHAR},
			REGISTE_ORGAN = #{registe_organ, jdbcType=VARCHAR},
			AUDIT_ORGAN = #{audit_organ, jdbcType=VARCHAR},
			APPROVE_ORGAN = #{approve_organ, jdbcType=VARCHAR},
			SPECIFIC_CAUSE = #{specific_cause, jdbcType=VARCHAR},
			MEDICAL_AMOUNT_CRITERION = #{medical_amount_criterion, jdbcType=VARCHAR},
			MEDICAL_TOTAL_MONY = #{medical_total_mony, jdbcType=NUMERIC},
			MEDICAL_AMOUNT_FLAG = #{medical_amount_flag, jdbcType=NUMERIC},
			REMARK = #{remark, jdbcType=VARCHAR},
			IS_RISK = #{is_risk, jdbcType=NUMERIC}, 
			RISK_LABEL = #{risk_label, jdbcType=VARCHAR} ,
		    RISK_OTHER_REASON = #{risk_other_reason, jdbcType=VARCHAR},
		    AUDIT_INDIVIDUAL_POOL_TIME = #{audit_individual_pool_time, jdbcType = TIMESTAMP},
		    APPROVE_INDIVIDUAL_POOL_TIME = #{approve_individual_pool_time, jdbcType = TIMESTAMP},
		    REJECT_REMARKS = #{reject_remarks, jdbcType = VARCHAR},
		    BENE_COUNT = #{bene_count, jdbcType = NUMERIC},
		    SIGN_USER_TYPE = #{sign_user_type, jdbcType = NUMERIC},
		    SIGN_USER_CODE = #{sign_user_code, jdbcType = VARCHAR},
		    SIGN_USER_NAME = #{sign_user_name, jdbcType = VARCHAR},
		    INVERSE_RISK_LEVEL = #{inverse_risk_level, jdbcType = VARCHAR},
		    INVOICE_RISK_LEVEL = #{invoice_risk_level, jdbcType = VARCHAR},
		    CASE_RISK_LEVEL = #{case_risk_level,jdbcType = VARCHAR},
		    REALTIME_PAY = #{realtime_pay, jdbcType = NUMERIC},	    
		    MATERIAL_FREE_FLAG = #{material_free_flag, jdbcType = NUMERIC},
		    MEDICAL_CONNECTION_FLAG = #{medical_connection_flag, jdbcType = NUMERIC},
		    REPEAT_NUMBER_TYPE = #{repeat_number_type, jdbcType = VARCHAR},
		    REPEAT_NUMBER_REASON = #{repeat_number_reason, jdbcType = VARCHAR},
		    REPEAT_NUMBER_FLAG = #{repeat_number_flag, jdbcType = NUMERIC},
		    SALESMAN_SELF_INSURANCE = #{salesman_self_insurance, jdbcType = NUMERIC},
		    IS_CHECKLIST = #{is_checklist, jdbcType = NUMERIC},
		    AGENT_RISK_LEVEL = #{agent_risk_level, jdbcType = VARCHAR},
		    CUSTOMER_RISK_LEVEL = #{customer_risk_level, jdbcType = VARCHAR},
		    EARLY_WARNING = #{early_warning, jdbcType = NUMERIC},
		    DIAGNOSIS_TIME = #{diagnosis_time, jdbcType=DATE}, 
		    SIGNATURE_TRACE_FLAG =#{signature_trace_flag, jdbcType = NUMERIC},
		    SMS_SEND_FLAG = #{sms_send_flag, jdbcType = NUMERIC},
		    ASSIGNEE_START_DATE = #{assignee_start_date, jdbcType=TIMESTAMP},
		    ASSIGNEE_END_DATE = #{assignee_end_date, jdbcType=TIMESTAMP},
		    FACE_RECOGNITION_FLAG =#{face_recognition_flag, jdbcType = NUMERIC},
		    IS_LAWSUITS = #{is_lawsuits, jdbcType=NUMERIC},
		    IS_OVER_COMP = #{is_over_comp, jdbcType=NUMERIC},
		    OVER_REASON = #{over_reason, jdbcType=VARCHAR},
		    OVER_DAYS = #{over_days, jdbcType=NUMERIC},
		    OVER_DUE_MONEY = #{over_due_money, jdbcType=NUMERIC},
		    PRE_AUDIT_ID = #{pre_audit_id, jdbcType=NUMERIC},
		    RE_AUDIT_DECISION = #{re_audit_decision, jdbcType=NUMERIC},
		    RE_AUDIT_OPINION = #{re_audit_opinion, jdbcType=VARCHAR},
		    MEDICAL_NUM = #{medical_num, jdbcType=VARCHAR},
		    TREAT_DATE = #{treat_date, jdbcType=DATE},
		    HOSPITAL_NUM = #{hospital_num, jdbcType=VARCHAR},
		    CHANGE_STATUS_TIME = #{change_status_time, jdbcType=DATE},
		    INSURANCE_PAID_FLAG = #{insurance_paid_flag, jdbcType=VARCHAR},
		    IS_SMALL_CASE = #{is_small_case, jdbcType=NUMERIC},
		    IS_PROBLEM_SOLVE = #{is_problem_solve, jdbcType=NUMERIC},
		    ACQUIST_WAY = #{acquist_way, jdbcType=NUMERIC},
		    OFF_HOSP_FLAG = #{off_hosp_flag, jdbcType=VARCHAR},
		    REMOTE_IDENT_FLAG = #{remote_ident_flag, jdbcType=VARCHAR},
		    IS_DEDUCT_PREM = #{is_deduct_prem, jdbcType=NUMERIC},
		    CLAIM_CASE_ID = #{claim_case_id, jdbcType=NUMERIC},
		    CLAIM_ORDER = #{claim_order, jdbcType=NUMERIC},
		    CLAIM_ORDER_AUDIT = #{claim_order_audit, jdbcType=NUMERIC},
		    ELEC_CHECK_FLAG = #{elec_check_flag, jdbcType=NUMERIC},
		    AUTO_CASE_NO = #{auto_case_no, jdbcType=VARCHAR},
		    AUTO_CASE_TYPE = #{auto_case_type, jdbcType=NUMERIC},
		    LOCK_FLAG = #{lock_flag, jdbcType=NUMERIC},
		    LOCK_SYS = #{lock_sys, jdbcType=VARCHAR},
		    SERVCOM = #{servcom, jdbcType=VARCHAR},
			FIRST_RPTR_TIME = #{first_rptr_time, jdbcType=TIMESTAMP},
			IS_CALLED_BACK= #{is_called_back, jdbcType=NUMERIC},
			REPORT_SOURCE = #{report_source ,jdbcType=VARCHAR},
			JOB_PROMPT = #{job_prompt ,jdbcType=VARCHAR},
			NOTREGIST_DOC_FLAG = #{notregist_doc_flag,jdbcType=NUMERIC},
			UN_DOC_REASON = #{un_doc_reason,jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	
	
	<!--	修改风险标识  -->
	<update id="updateClaimCaseRejectRemarks" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP , 
			REJECT_REMARKS = #{reject_remarks, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	<!--	修改风险标识  -->
	<update id="updateClaimCaseRisk" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			 
		    IS_RISK = #{is_risk, jdbcType=NUMERIC} , 
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP , 
			RISK_LABEL = #{risk_label, jdbcType=VARCHAR} ,
		    RISK_OTHER_REASON = #{risk_other_reason, jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	<update id="updateCaseStatusClaimCase" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} , 
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP , 
			CASE_STATUS = #{case_status, jdbcType=VARCHAR} ,
		    AUDIT_START_TIME = #{audit_start_time, jdbcType=TIMESTAMP}
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	<!-- 根据赔案号查询陪案类型和出险原因 -->
	<select id="findClaimCasePOByCase" resultType="java.util.Map" parameterType="java.util.Map">
	      SELECT TCT.NAME typename,TCN.NAME name FROM APP___CLM__DBUSER.T_CLAIM_CASE A,APP___CLM__DBUSER.T_CLAIM_SUB_CASE TCSC,
             APP___CLM__DBUSER.T_CLAIM_TYPE TCT,APP___CLM__DBUSER.T_CLAIM_NATURE TCN
             WHERE A.CASE_ID = TCSC.CASE_ID 
             AND TCT.CODE = TCSC.CLAIM_TYPE
             AND TCSC.ACC_REASON = TCN.CODE
             <if test="case_no != null and case_no != ''">
                AND A.CASE_NO = #{case_no}
             </if>
	</select>
	
	<!-- 根据赔案号查询所有历史回退案件 -->
	<select id="findClaimCaseByCaseHTid" resultType="java.util.Map" parameterType="java.util.Map">
	     SELECT A.CASE_ID,A.ACCIDENT_ID,A.CASE_NO,A.INSURED_ID,A.RPTR_RELATION,A.RPTR_NAME,A.RPTR_MP,A.RPTR_ZIP,
             A.RPTR_ADDR,A.RPTR_EMAIL,A.REPORT_MODE,A.ORGAN_CODE,A.RPTR_TIME,A.CASE_APPLY_TYPE,A.APPLY_DATE,A.ACCEPTOR_ID,
             A.ACCEPT_TIME,A.TRUSTEE_TYPE,A.TRUSTEE_CODE,A.TRUSTEE_NAME,A.TRUSTEE_MP,A.TRUSTEE_TEL,A.TRUSTEE_CERTI_TYPE,
             A.TRUSTEE_CERTI_CODE,A.DOOR_SIGN_TIME,A.SIGN_TIME,A.RPTR_ID,A.SIGNER_ID,A.SERIOUS_DISEASE,ACCIDENT_DETAIL,
             A.CURE_HOSPITAL,A.CURE_STATUS,A.DOCTOR_NAME,A.MED_DEPT,A.CASE_SUB_STATUS,A.CASE_STATUS,A.GREEN_FLAG,A.IS_BPO,
             A.CASE_FLAG,A.REVIEW_FLAG,A.COMFORT_FLAG,A.ADVANCE_ASK_FLAG,A.ADVANCE_FLAG,A.IS_DEDUCT_FLAG,A.REPEAL_REASON,
             A.REPEAL_DESC,A.REGISTER_ID,A.REGISTE_TIME,A.REGISTE_CONF_TIME,A.ACCEPT_DECISION,A.REJECT_REASON,A.CALC_PAY,
             ADVANCE_PAY,A.BALANCE_PAY,A.ACTUAL_PAY,A.REJECT_PAY,A.AUDIT_TIME,A.AUDITOR_ID,A.AUDIT_DECISION,A.AUDIT_REMARK,
             A.AUDIT_REJECT_REASON,A.OTHER_REASON,A.APPROVER_ID,A.APPROVE_TIME,A.APPROVE_DECISION,A.APPROVE_REJECT_REASON,
             A.APPROVE_REMARK,A.END_CASE_TIME,A.OVER_COMP_FLAG,A.RELATED_NO,A.CLAIM_SOURCE,A.IS_COMMON,A.INSERT_BY,
             A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP,A.AUDIT_PERMISSION_NAME,
             A.APPROVE_PERMISSION_NAME,A.SURVEY_RESULT_INFO,A.CHANNEL_CODE,A.IS_AUTO_HUNGUP,A.SPECIAL_REMARK_CODE,
             A.LOSS_REASON_CODE,A.LOSS_LEVEL_CODE,A.COMFORT_STATUS,A.IS_MIGRATION,A.SEND_BENE_DOC_FLAG,A.SIGN_ORGAN,
             A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.REMARK,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.SPECIFIC_CAUSE,A.REALTIME_PAY,
             A.MEDICAL_AMOUNT_FLAG,A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
             A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
             A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,
             A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE
             ,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.CLAIM_CASE_ID,A.CLAIM_ORDER,A.CLAIM_ORDER_AUDIT
             ,A.ELEC_CHECK_FLAG,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON
              FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1 = 1
             <!-- AND A.CASE_ID != #{case_id} -->
			start with 
			
			<if test="case_id != null and case_id != ''">
                A.CASE_ID = #{case_id}
            </if>
  			connect by prior A.RELATED_NO = A.case_no 
  			order by A.insert_time asc
            
	</select>
<!-- 按索引查询操作 -->	
	<select id="findClaimCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,A.ACCIDENT_ID,A.CASE_NO,A.INSURED_ID,A.RPTR_RELATION,A.RPTR_NAME,A.RPTR_MP,A.RPTR_ZIP,
             A.RPTR_ADDR,A.RPTR_EMAIL,A.REPORT_MODE,A.ORGAN_CODE,A.RPTR_TIME,A.CASE_APPLY_TYPE,A.APPLY_DATE,A.ACCEPTOR_ID,
             A.ACCEPT_TIME,A.TRUSTEE_TYPE,A.TRUSTEE_CODE,A.TRUSTEE_NAME,A.TRUSTEE_MP,A.TRUSTEE_TEL,A.TRUSTEE_CERTI_TYPE,
             A.TRUSTEE_CERTI_CODE,A.DOOR_SIGN_TIME,A.SIGN_TIME,A.RPTR_ID,A.SIGNER_ID,A.SERIOUS_DISEASE,ACCIDENT_DETAIL,
             A.CURE_HOSPITAL,A.CURE_STATUS,A.DOCTOR_NAME,A.MED_DEPT,A.CASE_SUB_STATUS,A.CASE_STATUS,A.GREEN_FLAG,A.IS_BPO,
             A.CASE_FLAG,A.REVIEW_FLAG,A.COMFORT_FLAG,A.ADVANCE_ASK_FLAG,A.ADVANCE_FLAG,A.IS_DEDUCT_FLAG,A.REPEAL_REASON,
             A.REPEAL_DESC,A.REGISTER_ID,A.REGISTE_TIME,A.REGISTE_CONF_TIME,A.ACCEPT_DECISION,A.REJECT_REASON,A.CALC_PAY,
             ADVANCE_PAY,A.BALANCE_PAY,A.ACTUAL_PAY,A.REJECT_PAY,A.AUDIT_TIME,A.AUDITOR_ID,A.AUDIT_DECISION,A.AUDIT_REMARK,
             A.AUDIT_REJECT_REASON,A.OTHER_REASON,A.APPROVER_ID,A.APPROVE_TIME,A.APPROVE_DECISION,A.APPROVE_REJECT_REASON,
             A.APPROVE_REMARK,A.END_CASE_TIME,A.OVER_COMP_FLAG,A.RELATED_NO,A.CLAIM_SOURCE,A.IS_COMMON,A.INSERT_BY,
             A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP,A.AUDIT_PERMISSION_NAME,
             A.APPROVE_PERMISSION_NAME,A.SURVEY_RESULT_INFO,A.CHANNEL_CODE,A.IS_AUTO_HUNGUP,A.SPECIAL_REMARK_CODE,
             A.LOSS_REASON_CODE,A.LOSS_LEVEL_CODE,A.COMFORT_STATUS,A.IS_MIGRATION,A.SEND_BENE_DOC_FLAG,A.SIGN_ORGAN,
             A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME, A.AUDIT_START_TIME,
             A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.REMARK,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.SPECIFIC_CAUSE,A.MEDICAL_AMOUNT_FLAG,A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.REJECT_REMARKS,
             A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.REALTIME_PAY, A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.MATERIAL_FREE_FLAG, A.MEDICAL_CONNECTION_FLAG,A.REPEAT_NUMBER_TYPE,A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_FLAG,A.SALESMAN_SELF_INSURANCE,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.SIGNATURE_TRACE_FLAG,A.DIAGNOSIS_TIME,
             A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,A.IS_LAWSUITS ,A.IS_OVER_COMP,A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,A.PRE_AUDIT_ID,A.RE_AUDIT_DECISION,A.RE_AUDIT_OPINION,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.SHARE_CONDITION_VALID,A.SHARE_CONDITION_DECISION,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.OFF_HOSP_FLAG,A.REMOTE_IDENT_FLAG,
             A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.JOB_PROMPT,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1 = 1  ]]>
		<if test=" case_id != null and case_id != ''  "><![CDATA[ AND A.case_id = #{case_id} ]]></if>   
		 
		 <if test=" case_id == null or case_id == ''  ">
			<if test=" case_no == null or case_no == ''  ">
				<![CDATA[ AND A.case_id = '' ]]>
			</if>
		</if> 
		   
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>
	
	
	
<!-- 按索引查询操作 -->	
	<select id="findClaimResults" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.CHANNEL_CODE,
       c.CHANNEL_DESC,
       a.ACCEPT_TIME,
       b.CLMT_MP,
       b.clmt_name,
       NVL(a.ADVANCE_PAY,'0') + a.ACTUAL_PAY as pay,
       a.AUDIT_DECISION,
       A.ACCEPT_DECISION,
       d.ACC_PROVINCE,
       d.ACC_CITY,
       d.ACC_DISTREACT,
       a.APPLY_DATE,
       a.end_case_time,
       a.case_no,
       a.case_status,
       d.ACC_REASON,
       (select a.name from dev_clm.T_DISTRICT a where a.code = d.ACC_PROVINCE) ||
       (select a.name from dev_clm.T_DISTRICT a where a.code = d.ACC_CITY) ||
       (select a.name from dev_clm.T_DISTRICT a where a.code = d.ACC_DISTREACT) ||
        d.ACC_STREET as occurPlace,
       f.CLAIM_TYPE,
       f.CLAIM_DATE,
       g.customer_birthday,
       g.customer_certi_code,
       g.customer_cert_type,
       g.customer_name,
       g.customer_gender,
       a.calc_pay,
       a.case_id,
       a.RPTR_EMAIL,
       a.RPTR_MP,
       a.RPTR_NAME,
       A.RPTR_TIME,
       A.RELATED_NO,
       A.FIRST_RPTR_TIME
  from APP___CLM__DBUSER.t_claim_case a
  left join APP___CLM__DBUSER.T_CLAIM_APPLICANT b
    on a.case_id = b.case_id
  left join APP___CLM__DBUSER.T_ACCEPT_CHANNEL c
    on a.CHANNEL_CODE = c.CHANNEL_CODE
    left join APP___CLM__DBUSER.t_claim_accident d 
    on a.accident_id = d.ACCIDENT_ID
    left join APP___CLM__DBUSER.t_claim_sub_case f
    on a.case_id = f.case_id
    left join APP___CLM__DBUSER.t_customer g
    on a.INSURED_ID = g.customer_id WHERE 1 = 1  ]]>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="findSerlIdNum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.BILL_NO,
       a.ADMINSSION_ID,
       b.actual_pay  
  from  dev_clm.T_CLAIM_BILL a
  left join dev_clm.t_claim_liab b
    on a.case_id = b.case_id WHERE 1 = 1 ]]>
		<if test=" case_id != null and case_id != ''  "><![CDATA[ AND a.case_id = #{case_id} ]]></if>
		<![CDATA[ ORDER BY a.CASE_ID ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="findAllMapClaimCase" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, A.CHANNEL_CODE,
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, 
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, A.REALTIME_PAY,
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.IS_MIGRATION,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,
			A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,
			A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,
			A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.REPEAT_NUMBER_FLAG,
            A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,
            A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,A.IS_OVER_COMP,A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,
            A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.JOB_PROMPT
            ,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimCase" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME,A.AUDIT_START_TIME, A.APPROVE_TIME, A.CHANNEL_CODE,
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.COMFORT_STATUS,
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, A.REALTIME_PAY,
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.UPDATE_BY, A.IS_AUTO_HUNGUP, A.IS_MIGRATION, A.IS_MIGRATION ,A.SIGN_TIME,
			A.RPTR_TIME,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,
			A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
			A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.UPDATE_TIMESTAMP,A.INSERT_TIME,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
            A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,
            A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,
            A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.IS_OVER_COMP,
            A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,
            A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.JOB_PROMPT,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON  FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimCaseWhereCondition" />
		<![CDATA[ ORDER BY A.CASE_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimCaseTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1 = 1  ]]>
		<include refid="claimCaseWhereConditionForPage" />
	</select>
	
	<!-- 作业监控查询结案赔案 -->
	<select id="findEndClaimCase" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(*) FROM (
		 SELECT  CASE WHEN LENGTH(C.ORGAN_CODE) > = 4 THEN SUBSTR (C.ORGAN_CODE,0,4) ELSE C.ORGAN_CODE END ORGAN_CODE 
			 FROM APP___CLM__DBUSER.T_CLAIM_CASE C
		       WHERE C.CASE_STATUS = 80
		         AND C.END_CASE_TIME >= #{start_time}
		         AND C.END_CASE_TIME <= #{end_time} ]]>
		       <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND C.ORGAN_CODE like '${organ_code}%' ]]></if>  
		       <if test=" acceptor_id != null and acceptor_id != ''  "><![CDATA[AND (C.APPROVER_ID = #{acceptor_id}
		        OR C.AUDITOR_ID = #{acceptor_id} OR C.REGISTER_ID = #{acceptor_id})]]> </if>
		       <![CDATA[ ) B, APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN D WHERE B.ORGAN_CODE= D.ORGAN_CODE(+)]]>  
		       <if test=" area_code != null and area_code != ''  "><![CDATA[ AND D.area_code = #{area_code}]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimCaseForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TRUSTEE_CERTI_CODE, B.END_CASE_TIME, B.ACTUAL_PAY, B.REPORT_MODE, B.APPROVE_REJECT_REASON, B.RELATED_NO, B.RPTR_RELATION, 
			B.OVER_COMP_FLAG, B.OTHER_REASON, B.REPEAL_REASON, B.IS_COMMON, B.CURE_HOSPITAL, B.CASE_NO, B.REVIEW_FLAG, 
			B.ORGAN_CODE, B.ADVANCE_ASK_FLAG, B.CASE_FLAG, B.GREEN_FLAG, B.AUDIT_REJECT_REASON, B.BALANCE_PAY, 
			B.REJECT_REASON, B.AUDITOR_ID, B.RPTR_MP, B.SERIOUS_DISEASE, B.REGISTE_TIME, B.CASE_APPLY_TYPE, 
			B.REPEAL_DESC, B.RPTR_EMAIL, B.TRUSTEE_CERTI_TYPE, B.INSURED_ID, B.ACCIDENT_DETAIL, B.CASE_STATUS, B.CASE_ID, 
			B.RPTR_TIME, B.AUDIT_DECISION, B.REGISTER_ID, B.CLAIM_SOURCE, B.TRUSTEE_TYPE, B.APPROVER_ID, B.CHANNEL_CODE,
			B.ACCIDENT_ID, B.REJECT_PAY, B.SIGN_TIME, B.SIGNER_ID, B.ADVANCE_PAY, B.RPTR_NAME, 
			B.RPTR_ADDR, B.RPTR_ID, B.AUDIT_PERMISSION_NAME, B.APPROVE_REMARK, B.TRUSTEE_NAME, B.APPLY_DATE, B.ADVANCE_FLAG, 
			B.REGISTE_CONF_TIME, B.TRUSTEE_TEL, B.RPTR_ZIP, B.IS_BPO, B.AUDIT_TIME, B.APPROVE_TIME, 
			B.AUDIT_REMARK, B.APPROVE_PERMISSION_NAME, B.APPROVE_DECISION, B.TRUSTEE_CODE, B.DOOR_SIGN_TIME, B.COMFORT_FLAG, B.COMFORT_STATUS,
			B.ACCEPT_TIME, B.IS_DEDUCT_FLAG, B.MED_DEPT, B.DOCTOR_NAME, B.ACCEPT_DECISION, B.TRUSTEE_MP, B.CURE_STATUS, 
			B.ACCEPTOR_ID, B.CASE_SUB_STATUS, B.CALC_PAY, B.SURVEY_RESULT_INFO, B.IS_MIGRATION,B.EASY_AUDIT_DECISION,B.EASY_AUDITOR_ID,B.MEDICAL_AMOUNT_FLAG,
			B.MEDICAL_TOTAL_MONY,B.MEDICAL_AMOUNT_CRITERION,B.AUDIT_INDIVIDUAL_POOL_TIME,B.APPROVE_INDIVIDUAL_POOL_TIME,B.INVERSE_RISK_LEVEL,B.INVOICE_RISK_LEVEL,B.CASE_RISK_LEVEL,B.SALESMAN_SELF_INSURANCE,B.IS_RISK,B.RISK_LABEL,B.RISK_OTHER_REASON,B.REPEAT_NUMBER_FLAG,
            B.REPEAT_NUMBER_REASON,B.REPEAT_NUMBER_TYPE,B.MATERIAL_FREE_FLAG,B.MEDICAL_CONNECTION_FLAG,B.IS_CHECKLIST,B.AGENT_RISK_LEVEL,B.CUSTOMER_RISK_LEVEL,B.EARLY_WARNING,B.DIAGNOSIS_TIME,B.SIGNATURE_TRACE_FLAG,B.FACE_RECOGNITION_FLAG,B.SMS_SEND_FLAG,
            B.ASSIGNEE_START_DATE,B.ASSIGNEE_END_DATE ,B.MEDICAL_NUM,
            B.TREAT_DATE,B.HOSPITAL_NUM,B.CHANGE_STATUS_TIME,B.INSURANCE_PAID_FLAG,B.IS_SMALL_CASE,B.IS_PROBLEM_SOLVE,B.ACQUIST_WAY,B.IS_DEDUCT_PREM,B.claim_case_id,B.claim_order,B.claim_order_audit,B.ELEC_CHECK_FLAG,B.AUTO_CASE_NO,
            B.AUTO_CASE_TYPE,B.LOCK_FLAG,B.LOCK_SYS,B.SERVCOM,B.FIRST_RPTR_TIME,B.IS_CALLED_BACK,B.REPORT_SOURCE,B.JOB_PROMPT,B.NOTREGIST_DOC_FLAG,B.UN_DOC_REASON FROM (
					SELECT ROWNUM RN, A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, (select st.name from APP___CLM__DBUSER.T_CASE_STATUS st where A.CASE_STATUS=st.code ) CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME,  A.CHANNEL_CODE,
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.COMFORT_STATUS,
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, 
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.IS_MIGRATION,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,
			A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
			A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
            A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,
            A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE  ,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,
            A.CHANGE_STATUS_TIME,A.INSURANCE_PAID_FLAG,A.IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.JOB_PROMPT,
            A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="claimCaseWhereConditionForPage" />
		<![CDATA[ ORDER BY A.CASE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 修改操作 -->
	<update id="updateClaimCaseStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
			<trim suffixOverrides=",">
				REPEAL_REASON = #{repeal_reason,
				jdbcType=NUMERIC} ,
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				REPEAL_DESC = #{repeal_desc, jdbcType=VARCHAR} ,
				CASE_STATUS = #{case_status, jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE CASE_NO = #{case_no} ]]>

	</update>
	<!-- caoyy_wb修改操作 -->
	<update id="updateCaseStatusByCaseIdAndGreenFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
			<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				GREEN_FLAG = #{green_flag, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>

	</update>
	
	<update id="updateCaseFlagByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
			<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				CASE_FLAG = #{case_flag, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>

	</update>
	
    <!-- 通过保单号查询赔案状态 -->
	<select id="findCasesByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	   SELECT A.CASE_ID, A.CASE_NO,A.CASE_STATUS, B.POLICY_CODE
     FROM APP___CLM__DBUSER.T_CLAIM_CASE   A,
          APP___CLM__DBUSER.T_CLAIM_POLICY B
    WHERE A.CASE_ID = B.CASE_ID
      AND B.POLICY_CODE=#{policy_code,jdbcType=VARCHAR}
	]]>
	</select>
	
    <!-- add by zhaoyq start -->
	<select id="findCaseIdByCaseNo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.claim_case_id,A.claim_order,A.claim_order_audit,A.IS_DEDUCT_PREM,A.ACQUIST_WAY,A.INSERT_TIME,A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, A.REALTIME_PAY,
			A.RPTR_ADDR, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, 
			A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, A.AUDIT_REMARK, A.APPROVE_DECISION, A.CHANNEL_CODE,
			A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG,A.COMFORT_STATUS, A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, 
			A.DOCTOR_NAME, A.TRUSTEE_MP,A.RPTR_ID, A.CURE_STATUS, A.ACCEPT_DECISION, A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.AUDIT_PERMISSION_NAME, A.APPROVE_PERMISSION_NAME, A.IS_AUTO_HUNGUP,
			A.IS_MIGRATION,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,A.MEDICAL_AMOUNT_CRITERION,A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,
               A.MEDICAL_AMOUNT_FLAG,
               A.MEDICAL_TOTAL_MONY,
               A.REPEAT_NUMBER_FLAG,A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,
               A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,
               A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING ,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE,
               A.IS_OVER_COMP,A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,A.LOCK_FLAG,A.LOCK_SYS,
               A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.ELEC_CHECK_FLAG,A.FIRST_RPTR_TIME,A.SERVCOM,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.JOB_PROMPT,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON,
            (SELECT LISTAGG(ssa.org_opr, ',') WITHIN GROUP(ORDER BY ssa.case_no) from (
			select t.case_no,c.org_opr
			from APP___CLM__DBUSER.t_claim_case t 
			left join APP___CLM__DBUSER.t_Survey_Apply b on t.case_no = b.case_no 
			left join APP___CLM__DBUSER.t_Survey_Conclusion c on b.apply_id = c.apply_id  
			) ssa where ssa.case_no = A.Case_No) as org_opr_str
			FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_NO = #{case_no}  ORDER BY A.CASE_ID ]]>
	</select>
	<!-- 批处理-自动信息发送  办理理赔提醒 -->
	<select id="findClaimSignCaseTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_STATUS = '20'  ]]>
	</select>
	<select id="findClaimSignCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN, B.TRUSTEE_CERTI_CODE, B.END_CASE_TIME, B.ACTUAL_PAY, B.REPORT_MODE, B.APPROVE_REJECT_REASON, B.RELATED_NO, B.RPTR_RELATION, 
			                   B.OVER_COMP_FLAG, B.OTHER_REASON, B.REPEAL_REASON, B.IS_COMMON, B.CURE_HOSPITAL, B.CASE_NO, B.REVIEW_FLAG, 
			                   B.ORGAN_CODE, B.ADVANCE_ASK_FLAG, B.CASE_FLAG, B.GREEN_FLAG, B.AUDIT_REJECT_REASON, B.BALANCE_PAY, 
			                   B.REJECT_REASON, B.AUDITOR_ID, B.RPTR_MP, B.SERIOUS_DISEASE, B.REGISTE_TIME, B.CASE_APPLY_TYPE, 
			                   B.REPEAL_DESC, B.RPTR_EMAIL, B.TRUSTEE_CERTI_TYPE, B.INSURED_ID, B.ACCIDENT_DETAIL, B.CASE_STATUS, B.CASE_ID, 
			                   B.RPTR_TIME, B.AUDIT_DECISION, B.REGISTER_ID, B.CLAIM_SOURCE, B.TRUSTEE_TYPE, B.APPROVER_ID, 
			                   B.ACCIDENT_ID, B.REJECT_PAY, B.SIGN_TIME, B.SIGNER_ID, B.ADVANCE_PAY, B.RPTR_NAME, 
			                   B.RPTR_ADDR, B.APPROVE_REMARK, B.TRUSTEE_NAME, B.APPLY_DATE, B.ADVANCE_FLAG, B.REGISTE_CONF_TIME, B.TRUSTEE_TEL, 
			                   B.RPTR_ZIP, B.IS_BPO, B.AUDIT_TIME, B.APPROVE_TIME, B.AUDIT_REMARK, B.APPROVE_DECISION, B.CHANNEL_CODE,
			                   B.TRUSTEE_CODE, B.DOOR_SIGN_TIME, B.COMFORT_FLAG,B.COMFORT_STATUS, B.ACCEPT_TIME, B.IS_DEDUCT_FLAG, B.MED_DEPT, 
			                   B.DOCTOR_NAME, B.TRUSTEE_MP, B.CURE_STATUS, B.ACCEPT_DECISION, B.ACCEPTOR_ID, B.CASE_SUB_STATUS, B.CALC_PAY, B.IS_MIGRATION,B.SIGN_ORGAN,
			                   B.REGISTE_ORGAN,B.AUDIT_ORGAN,B.APPROVE_ORGAN,B.EASY_AUDIT_DECISION,B.EASY_AUDITOR_ID,B.AUDIT_INDIVIDUAL_POOL_TIME,B.APPROVE_INDIVIDUAL_POOL_TIME,
			                   B.INVERSE_RISK_LEVEL,B.INVOICE_RISK_LEVEL,B.CASE_RISK_LEVEL,B.SALESMAN_SELF_INSURANCE,B.IS_RISK,B.RISK_LABEL,B.RISK_OTHER_REASON,B.REPEAT_NUMBER_FLAG,
                               B.REPEAT_NUMBER_REASON,B.REPEAT_NUMBER_TYPE,B.MATERIAL_FREE_FLAG,B.MEDICAL_CONNECTION_FLAG  ,B.AGENT_RISK_LEVEL,B.CUSTOMER_RISK_LEVEL,B.EARLY_WARNING,B.SIGNATURE_TRACE_FLAG,B.FACE_RECOGNITION_FLAG,B.SMS_SEND_FLAG,
                               B.ASSIGNEE_START_DATE,B.ASSIGNEE_END_DATE ,B.MEDICAL_NUM,B.TREAT_DATE,B.HOSPITAL_NUM,B.CHANGE_STATUS_TIME,B.INSURANCE_PAID_FLAG,B.IS_SMALL_CASE,B.IS_PROBLEM_SOLVE,B.ACQUIST_WAY,B.IS_DEDUCT_PREM,B.claim_case_id,B.claim_order,B.claim_order_audit,B.ELEC_CHECK_FLAG,
                               B.AUTO_CASE_NO,B.AUTO_CASE_TYPE,B.LOCK_FLAG,B.LOCK_SYS,B.SERVCOM,B.FIRST_RPTR_TIME,B.IS_CALLED_BACK,B.REPORT_SOURCE,B.JOB_PROMPT,B.NOTREGIST_DOC_FLAG,B.UN_DOC_REASON
		                  FROM (select ROWNUM RN, A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO,A.RPTR_RELATION, 
			                           A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			                           A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			                           A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			                           A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			                           A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			                           A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			                           A.RPTR_ADDR, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, 
			                           A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, A.AUDIT_REMARK, A.APPROVE_DECISION, A.CHANNEL_CODE,
			                           A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG,A.COMFORT_STATUS, A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, 
			                           A.DOCTOR_NAME, A.TRUSTEE_MP, A.CURE_STATUS, A.ACCEPT_DECISION, A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.IS_MIGRATION,
			                           A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
			                           A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
			                           A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,
			                           A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.INSURANCE_PAID_FLAG,A.IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,
			                           A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.JOB_PROMPT,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON
				                  FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE  A.CASE_STATUS = '20'  ) B 
				          WHERE mod(B.RN,#{modNum})=#{start} ]]>
	</select>
	
	
	<select id="queryRemindForCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN, B.TRUSTEE_CERTI_CODE, B.END_CASE_TIME, B.ACTUAL_PAY, B.REPORT_MODE, B.APPROVE_REJECT_REASON, B.RELATED_NO, B.RPTR_RELATION, 
			                   B.OVER_COMP_FLAG, B.OTHER_REASON, B.REPEAL_REASON, B.IS_COMMON, B.CURE_HOSPITAL, B.CASE_NO, B.REVIEW_FLAG, 
			                   B.ORGAN_CODE, B.ADVANCE_ASK_FLAG, B.CASE_FLAG, B.GREEN_FLAG, B.AUDIT_REJECT_REASON, B.BALANCE_PAY, 
			                   B.REJECT_REASON, B.AUDITOR_ID, B.RPTR_MP, B.SERIOUS_DISEASE, B.REGISTE_TIME, B.CASE_APPLY_TYPE, 
			                   B.REPEAL_DESC, B.RPTR_EMAIL, B.TRUSTEE_CERTI_TYPE, B.INSURED_ID, B.ACCIDENT_DETAIL, B.CASE_STATUS, B.CASE_ID, 
			                   B.RPTR_TIME, B.AUDIT_DECISION, B.REGISTER_ID, B.CLAIM_SOURCE, B.TRUSTEE_TYPE, B.APPROVER_ID, 
			                   B.ACCIDENT_ID, B.REJECT_PAY, B.SIGN_TIME, B.SIGNER_ID, B.ADVANCE_PAY, B.RPTR_NAME, 
			                   B.RPTR_ADDR, B.APPROVE_REMARK, B.TRUSTEE_NAME, B.APPLY_DATE, B.ADVANCE_FLAG, B.REGISTE_CONF_TIME, B.TRUSTEE_TEL, 
			                   B.RPTR_ZIP, B.IS_BPO, B.AUDIT_TIME, B.APPROVE_TIME, B.AUDIT_REMARK, B.APPROVE_DECISION, B.CHANNEL_CODE,
			                   B.TRUSTEE_CODE, B.DOOR_SIGN_TIME, B.COMFORT_FLAG,B.COMFORT_STATUS, B.ACCEPT_TIME, B.IS_DEDUCT_FLAG, B.MED_DEPT, 
			                   B.DOCTOR_NAME, B.TRUSTEE_MP, B.CURE_STATUS, B.ACCEPT_DECISION, B.ACCEPTOR_ID, B.CASE_SUB_STATUS, B.CALC_PAY, B.IS_MIGRATION,B.SIGN_ORGAN,
			                   B.REGISTE_ORGAN,B.AUDIT_ORGAN,B.APPROVE_ORGAN,B.EASY_AUDIT_DECISION,B.EASY_AUDITOR_ID,B.AUDIT_INDIVIDUAL_POOL_TIME,B.APPROVE_INDIVIDUAL_POOL_TIME,B.SALESMAN_SELF_INSURANCE,B.IS_RISK,B.RISK_LABEL,B.RISK_OTHER_REASON,
			                   B.REPEAT_NUMBER_FLAG,B.REPEAT_NUMBER_REASON,B.REPEAT_NUMBER_TYPE,B.MATERIAL_FREE_FLAG,B.MEDICAL_CONNECTION_FLAG,B.IS_CHECKLIST  ,B.AGENT_RISK_LEVEL,B.CUSTOMER_RISK_LEVEL,B.EARLY_WARNING,B.DIAGNOSIS_TIME,B.SIGNATURE_TRACE_FLAG,B.FACE_RECOGNITION_FLAG,B.SMS_SEND_FLAG,
			                   B.ASSIGNEE_START_DATE,B.ASSIGNEE_END_DATE,B.MEDICAL_NUM,B.TREAT_DATE,B.HOSPITAL_NUM,B.CHANGE_STATUS_TIME,B.INSURANCE_PAID_FLAG,B.IS_SMALL_CASE,B.IS_PROBLEM_SOLVE,B.ACQUIST_WAY,B.IS_DEDUCT_PREM,B.claim_case_id,B.claim_order,B.claim_order_audit,B.ELEC_CHECK_FLAG,
			                   B.AUTO_CASE_NO,B.AUTO_CASE_TYPE,B.LOCK_FLAG,B.LOCK_SYS,B.SERVCOM,B.FIRST_RPTR_TIME,B.IS_CALLED_BACK,B.REPORT_SOURCE,B.NOTREGIST_DOC_FLAG,B.UN_DOC_REASON
		                  FROM (select ROWNUM RN, A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO,A.RPTR_RELATION, 
			                           A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			                           A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			                           A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			                           A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			                           A.INSERT_TIME RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			                           A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			                           A.RPTR_ADDR, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, 
			                           A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, A.AUDIT_REMARK, A.APPROVE_DECISION, A.CHANNEL_CODE,
			                           A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG,A.COMFORT_STATUS, A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, 
			                           A.DOCTOR_NAME, A.TRUSTEE_MP, A.CURE_STATUS, A.ACCEPT_DECISION, A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.IS_MIGRATION,A.SIGN_ORGAN,
			                           A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,
			                           A.REPEAT_NUMBER_FLAG,A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,
			                           A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.INSURANCE_PAID_FLAG,A.IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,
			                           A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON
				                  FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE  A.CASE_STATUS = '20'  ) B 
				          WHERE mod(B.RN,#{modNum})=#{start} ]]>
	</select>
	
	<!-- 根据赔案号查询相关信息(包含批次号和及时二核标志) -->
	<select id="findInformationByCaseNo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.accident_id,A.CASE_NO,(SELECT TO_CHAR(WMSYS.WM_CONCAT(CT.NAME))
          FROM DEV_CLM.T_CLAIM_SUB_CASE SC
         RIGHT JOIN DEV_CLM.T_CLAIM_TYPE CT
            ON CT.CODE = SC.CLAIM_TYPE
         WHERE SC.CASE_ID = A.Case_Id) CLAIM_TYPE,
       A.ACTUAL_PAY + NVL(A.advance_pay,0) AS ACTUAL_PAY,A.ORGAN_CODE,A.INSURED_ID,A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_NO = #{case_no} ]]>
	</select>
	<!-- 赔案号快查服务 -->
	<select id="isExistsClainByCaseNo" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_NO = #{case_no} ]]>
	</select>
	<!-- 根据保单号查询最近一次结案赔案信息 -->
	<select id="findInformationByPolicyNo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_NO,A.ORGAN_CODE,A.INSURED_ID,A.CASE_ID
		            FROM APP___CLM__DBUSER.T_CLAIM_CASE A
                    WHERE A.CASE_ID IN (SELECT DISTINCT B.CASE_ID
                                          FROM APP___CLM__DBUSER.T_CONTRACT_MASTER B
                                         WHERE B.POLICY_CODE = #{policy_code})
                         AND A.END_CASE_TIME IS NOT NULL ORDER BY END_CASE_TIME DESC
        ]]>
	</select>
	<!-- end -->
	<!-- add by gaoxyit 批处理查询案件 -->
	<select id="findAllClaimCaseBatch" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT M.INSURED_ID,M.CASE_ID,(SELECT CC.CASE_NO FROM APP___CLM__DBUSER.T_CLAIM_CASE CC WHERE CC.CASE_ID=M.CASE_ID) CASE_NO FROM (
				SELECT A.INSURED_ID,MIN(A.CASE_ID) CASE_ID
           FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1=1  ]]>
		<include refid="claimCaseWhereCondition" />
		<![CDATA[ GROUP BY A.INSURED_ID
         ORDER BY MIN(A.CASE_ID) )M WHERE mod(M.CASE_ID, #{modNum}) = #{start}]]>
	</select>
	<!-- end -->
	<!-- zhangjy_wb -->
	<update id="updateClaimCaseByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CASE_APPLY_TYPE = #{case_apply_type, jdbcType=NUMERIC} ,
			APPLY_DATE = #{apply_date, jdbcType=TIMESTAMP} ,
			TRUSTEE_TYPE = #{trustee_type, jdbcType=NUMERIC} ,
			TRUSTEE_CODE = #{trustee_code, jdbcType=VARCHAR} ,
			TRUSTEE_NAME = #{trustee_name, jdbcType=VARCHAR} ,
			TRUSTEE_CERTI_TYPE = #{trustee_certi_type, jdbcType=VARCHAR} ,
			TRUSTEE_MP = #{trustee_mp, jdbcType=VARCHAR} ,
			TRUSTEE_TEL = #{trustee_tel, jdbcType=VARCHAR} ,
			TRUSTEE_CERTI_CODE = #{trustee_certi_code, jdbcType=VARCHAR} ,
			DOOR_SIGN_TIME = #{door_sign_time, jdbcType=TIMESTAMP} ,
			SERIOUS_DISEASE = #{serious_disease, jdbcType=VARCHAR} ,
			ACCIDENT_DETAIL = #{accident_detail, jdbcType=VARCHAR} ,
			CURE_STATUS = #{cure_status, jdbcType=VARCHAR} ,
			CURE_HOSPITAL = #{cure_hospital, jdbcType=VARCHAR} ,
			DOCTOR_NAME = #{doctor_name, jdbcType=VARCHAR} ,
			MED_DEPT = #{med_dept, jdbcType=VARCHAR} ,
			<if test=" registe_time != null and registe_time != ''  ">
				REGISTE_TIME = #{registe_time,jdbcType=TIMESTAMP} ,
			</if>
			ACCIDENT_ID = #{accident_id,jdbcType=NUMERIC} ,
			AUDIT_TIME = #{audit_time,jdbcType=TIMESTAMP} ,
			IS_CHECKLIST = #{is_checklist,jdbcType=NUMERIC} ,
			DIAGNOSIS_TIME = #{diagnosis_time, jdbcType=DATE},
			ASSIGNEE_START_DATE = #{assignee_start_date, jdbcType=TIMESTAMP} ,
			ASSIGNEE_END_DATE = #{assignee_end_date, jdbcType=TIMESTAMP} ,
			INSURANCE_PAID_FLAG = #{insurance_paid_flag, jdbcType=VARCHAR},
			CLAIM_CASE_ID = #{claim_case_id, jdbcType=NUMERIC},
		    CLAIM_ORDER = #{claim_order, jdbcType=NUMERIC},
		    CLAIM_ORDER_AUDIT = #{claim_order_audit, jdbcType=NUMERIC}
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	<!-- end -->
	<!-- add by zhaoyq -->
	<!-- 修改案件状态 -->
	<update id="updateCaseStatusByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CASE_STATUS = #{case_status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	<!-- 修改审核人ID -->
	<update id="updateAuditorIdByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AUDITOR_ID = #{auditor_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	<!-- 赔案号查询明细信息 -->
	<select id="queryCasemessage" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select a.accident_no as accno,
		       c.case_no,
		       c.rptr_name,
		       c.rptr_mp,
		       c.rptr_addr,
		       c.rptr_zip,
		       c.rptr_relation,
		       (select RELATION_DESC from APP___CLM__DBUSER.T_RPTR_RELATION where RELATION_CODE = c.rptr_relation) as relationch,
		       c.report_mode,
		       (select r.name from APP___CLM__DBUSER.T_REPORT_TYPE r where r.code = c.report_mode) as rptmodech,
		       a.acc_street as accidentsite,
		       c.insert_time AS rptr_time,
		       c.organ_code,
		       (select real_name from APP___CLM__DBUSER.t_Udmp_User where user_id=c.acceptor_id) as operator1,
		       c.case_apply_type,
		       (select name from APP___CLM__DBUSER.T_APPLY_TYPE ty where ty.code = c.case_apply_type) as rgtclassch,
		       a.acc_province as provincecode,
		       a.acc_city as citycode,
		       a.acc_distreact as countycode,
		       a.acc_street as street,
		       '',
		       c.accept_decision,
		       c.case_flag,
		       c.case_status,
		       c.audit_decision,
		       c.auditor_id,
		       (select real_name from APP___CLM__DBUSER.t_udmp_user where user_id =c.auditor_id)user_name,
		       c.audit_time,
		       c.survey_result_info,
		       c.audit_remark,
		       c.approve_decision,
		       (select real_name from APP___CLM__DBUSER.t_Udmp_User where user_id=c.approver_id) as examper,
		       c.approve_time,
		       c.approve_remark,
		       c.actual_pay,
		       (select real_name from APP___CLM__DBUSER.t_Udmp_User where user_id=c.update_by) as operator2,
		       c.end_case_time,
		       (select 1 from APP___CLM__DBUSER.T_PREM_ARAP pa  where pa.business_code = c.case_no and pa.deriv_type = '005' and pa.fee_status in ('01','18') and pa.arap_flag = '1' and rownum = 1) as payflag,
		       c.insured_id,
		       (select cu.customer_name from APP___CLM__DBUSER.T_CUSTOMER cu where cu.customer_id = c.insured_id) as customername,
		       (select cu.customer_gender from APP___CLM__DBUSER.T_CUSTOMER cu where cu.customer_id = c.insured_id) as customersex,
		       (select cu.customer_birthday from APP___CLM__DBUSER.T_CUSTOMER cu where cu.customer_id = c.insured_id) as custbirthday,
		       (select cu.customer_vip from APP___CLM__DBUSER.T_CUSTOMER cu where cu.customer_id = c.insured_id) as vipflag,
		       c.audit_decision as conclusion,
		       c.audit_reject_reason,c.SIGN_ORGAN,c.REGISTE_ORGAN,c.AUDIT_ORGAN,c.APPROVE_ORGAN,C.AUDIT_INDIVIDUAL_POOL_TIME,C.FIRST_RPTR_TIME,C.APPROVE_INDIVIDUAL_POOL_TIME,C.IS_CALLED_BACK,C.REPORT_SOURCE
		  from APP___CLM__DBUSER.T_CLAIM_CASE c, APP___CLM__DBUSER.T_CLAIM_ACCIDENT a
		 where a.accident_id = c.accident_id
		       and c.case_no = #{case_no}
	]]>
	<if test="accident_no !=null and accident_no !=''"> 
         <![CDATA[  and a.ACCIDENT_NO = #{accident_no}  ]]>
	</if>
	</select>

	<!-- 理赔综合查询分页查询操作 -->
	<select id="claimCompositeQueryForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TRUSTEE_CERTI_CODE, B.END_CASE_TIME, B.ACTUAL_PAY, B.REPORT_MODE, B.APPROVE_REJECT_REASON, B.RELATED_NO, B.RPTR_RELATION, 
			B.OVER_COMP_FLAG, B.OTHER_REASON, B.REPEAL_REASON, B.IS_COMMON, B.CURE_HOSPITAL, B.CASE_NO, B.REVIEW_FLAG, 
			B.ORGAN_CODE, B.ADVANCE_ASK_FLAG, B.CASE_FLAG, B.GREEN_FLAG, B.AUDIT_REJECT_REASON, B.BALANCE_PAY, 
			B.REJECT_REASON, B.AUDITOR_ID, B.RPTR_MP, B.SERIOUS_DISEASE, B.REGISTE_TIME, B.CASE_APPLY_TYPE, 
			B.REPEAL_DESC, B.RPTR_EMAIL, B.TRUSTEE_CERTI_TYPE, B.INSURED_ID, B.ACCIDENT_DETAIL, B.CASE_STATUS, B.CASE_ID, 
			B.RPTR_TIME, B.AUDIT_DECISION, B.REGISTER_ID, B.CLAIM_SOURCE, B.TRUSTEE_TYPE, B.APPROVER_ID, 
			B.ACCIDENT_ID, B.REJECT_PAY, B.SIGN_TIME, B.SIGNER_ID, B.ADVANCE_PAY, B.RPTR_NAME, 
			B.RPTR_ADDR, B.RPTR_ID, B.AUDIT_PERMISSION_NAME, B.APPROVE_REMARK, B.TRUSTEE_NAME, B.APPLY_DATE, B.ADVANCE_FLAG, 
			B.REGISTE_CONF_TIME, B.TRUSTEE_TEL, B.RPTR_ZIP, B.IS_BPO, B.AUDIT_TIME, B.APPROVE_TIME, 
			B.AUDIT_REMARK, B.APPROVE_PERMISSION_NAME, B.APPROVE_DECISION, B.TRUSTEE_CODE, B.DOOR_SIGN_TIME, B.COMFORT_FLAG, 
			B.ACCEPT_TIME, B.IS_DEDUCT_FLAG, B.MED_DEPT, B.DOCTOR_NAME, B.TRUSTEE_MP, B.CURE_STATUS, B.ACCEPT_DECISION,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN, 
			B.ACCEPTOR_ID, B.CASE_SUB_STATUS, B.CALC_PAY, B.POLICY_CODE, B.AUDIT_INDIVIDUAL_POOL_TIME, B.APPROVE_INDIVIDUAL_POOL_TIME,B.DIAGNOSIS_TIME,B.SIGNATURE_TRACE_FLAG,B.FACE_RECOGNITION_FLAG,B.SMS_SEND_FLAG,
			B.ASSIGNEE_START_DATE,B.ASSIGNEE_END_DATE ,B.INSURANCE_PAID_FLAG,B.IS_SMALL_CASE,B.IS_PROBLEM_SOLVE,B.ACQUIST_WAY,B.IS_DEDUCT_PREM,B.claim_case_id,B.claim_order,B.claim_order_audit,B.ELEC_CHECK_FLAG,
			B.AUTO_CASE_NO,B.AUTO_CASE_TYPE,B.LOCK_FLAG,B.LOCK_SYS,B.SERVCOM,B.FIRST_RPTR_TIME,B.IS_CALLED_BACK,B.REPORT_SOURCE,B.NOTREGIST_DOC_FLAG,B.UN_DOC_REASON FROM (
					SELECT ROWNUM RN, A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, (select st.name from APP___CLM__DBUSER.T_CASE_STATUS st where A.CASE_STATUS=st.code ) CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, 
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, 
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.TRUSTEE_MP, A.CURE_STATUS, A.ACCEPT_DECISION,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN, 
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, C.POLICY_CODE, A.AUDIT_INDIVIDUAL_POOL_TIME, A.APPROVE_INDIVIDUAL_POOL_TIME,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,
			A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE,A.INSURANCE_PAID_FLAG,A.IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,
			A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON
			FROM APP___CLM__DBUSER.T_CLAIM_CASE A 
			LEFT JOIN APP___CLM__DBUSER.T_CONTRACT_MASTER C ON C.CASE_ID = A.CASE_ID
			WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="claimCompositeQueryCondition" />
		<![CDATA[ ORDER BY A.CASE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<sql id="claimCompositeQueryCondition">
		<if test=" case_no != null and case_no != ''  ">
			<![CDATA[ AND A.CASE_NO = #{case_no, jdbcType=VARCHAR} ]]>
		</if>
		<if test=" organ_code != null and organ_code != ''  ">
			<![CDATA[ AND A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ]]>
		</if>
		<if test=" case_status != null and case_status != ''  ">
			<![CDATA[ AND A.CASE_STATUS = #{case_status, jdbcType=VARCHAR} ]]>
		</if>
		<if test=" policy_code != null and policy_code != ''  ">
			<![CDATA[ AND C.POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ]]>
		</if>
		<![CDATA[ AND C.CUR_FLAG = 1 ]]>
	</sql>
	
	<!-- 理赔综合查询查询个数操作 -->
	<select id="claimCompositeQueryTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
      			FROM APP___CLM__DBUSER.T_CLAIM_CASE A 
      			LEFT JOIN APP___CLM__DBUSER.T_CONTRACT_MASTER C ON C.CASE_ID = A.CASE_ID
      			WHERE 1 = 1]]>
		<include refid="claimCompositeQueryCondition" />
	</select>
	
	
	<!-- 综合查询（重复数据去重） -->
	<select id="distinctClaimCompositeQueryForPage" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ select BO.RN ,
						       BO.CASE_NO,
						       BO.ORGAN_CODE,
						       BO.INSURED_ID,
						       BO.CASE_STATUS,
						       BO.CASE_ID,
						       BO.RPTR_TIME,
						       BO.AUDIT_TIME from
						(select ROWNUM RN,
						       B.CASE_NO,
						       B.ORGAN_CODE,
						       B.INSURED_ID,
						       B.CASE_STATUS,
						       B.CASE_ID,
						       B.RPTR_TIME,
						       B.AUDIT_TIME
						  from (SELECT distinct A.CASE_NO,
						                        A.ORGAN_CODE,
						                        A.INSURED_ID,
						                        (select st.name  from APP___CLM__DBUSER.T_CASE_STATUS st  where A.CASE_STATUS = st.code) CASE_STATUS,
						                        A.CASE_ID,
						                        A.RPTR_TIME,
						                        A.AUDIT_TIME
						          FROM APP___CLM__DBUSER.T_CLAIM_CASE A
						          LEFT JOIN APP___CLM__DBUSER.T_CONTRACT_MASTER C
						            ON C.CASE_ID = A.CASE_ID
						         where 1 = 1  ]]>
						  <include refid="claimCompositeQueryCondition" />
						     <![CDATA[    ORDER BY A.CASE_ID) B
						 WHERE ROWNUM <=  #{LESS_NUM} ) BO
						  where  BO.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 综合查询（重复数据去重） -->
	<select id="distinctClaimCompositeQueryTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >
			<![CDATA[ select count(1) from (
						 SELECT distinct A.CASE_NO,
	                        A.ORGAN_CODE,
	                        A.INSURED_ID,
	                        (select st.name
	                           from APP___CLM__DBUSER.T_CASE_STATUS st
	                          where A.CASE_STATUS = st.code) CASE_STATUS,
	                        A.CASE_ID,
	                        A.RPTR_TIME,
	                        A.AUDIT_TIME
	          FROM APP___CLM__DBUSER.T_CLAIM_CASE A
	          LEFT JOIN APP___CLM__DBUSER.T_CONTRACT_MASTER C
	            ON C.CASE_ID = A.CASE_ID
	         where 1=1 ]]>
			 <include refid="claimCompositeQueryCondition" />
	         <![CDATA[ ORDER BY A.CASE_ID ) B ]]>
	</select>
	
		<!-- 外包录入更新 -->
	<update id="updateClaimCaseByCaseNo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			<if test=" organ_code != null and organ_code != ''  ">
				ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			</if>
<!-- 			<if test=" accident_detail != null and accident_detail != ''  "> -->
				ACCIDENT_DETAIL = #{accident_detail, jdbcType=VARCHAR} ,
<!-- 			</if> -->
<!-- 			<if test=" cure_status != null and cure_status != ''  "> -->
				CURE_STATUS = #{cure_status, jdbcType=VARCHAR} ,
<!-- 			</if> -->
<!-- 			<if test=" cure_hospital != null and cure_hospital != ''  "> -->
				CURE_HOSPITAL = #{cure_hospital, jdbcType=VARCHAR} ,
<!-- 			</if> -->
			<if test=" trustee_type != null ">
				TRUSTEE_TYPE = #{trustee_type, jdbcType=NUMERIC} ,
			</if>
			<if test=" case_status != null and case_status != ''  ">
				CASE_STATUS = #{case_status, jdbcType=VARCHAR} ,
			</if>
			<if test=" trustee_code != null and trustee_code != ''  ">
				TRUSTEE_CODE = #{trustee_code, jdbcType=VARCHAR} ,
			</if>
			<if test=" trustee_name != null and trustee_name != ''  ">
				TRUSTEE_NAME = #{trustee_name, jdbcType=VARCHAR} ,
			</if>
			<if test=" trustee_mp != null and trustee_mp != ''  ">
				TRUSTEE_MP = #{trustee_mp, jdbcType=VARCHAR} ,
			</if>
			<if test=" trustee_tel != null and trustee_tel != ''  ">
				TRUSTEE_TEL = #{trustee_tel, jdbcType=VARCHAR} ,
			</if>
			<if test=" door_sign_time != null ">
				DOOR_SIGN_TIME = #{door_sign_time, jdbcType=TIMESTAMP} ,
			</if>
			<if test=" apply_date != null">
				APPLY_DATE = #{apply_date, jdbcType=TIMESTAMP} ,
			</if> 
			<if test=" diagnosis_time != null">
				DIAGNOSIS_TIME = #{diagnosis_time, jdbcType=TIMESTAMP} ,
			</if>
			<if test=" serious_disease != null and serious_disease != ''  ">
				SERIOUS_DISEASE = #{serious_disease, jdbcType=TIMESTAMP} ,
			</if>
			<if test=" trustee_certi_type != null and trustee_certi_type != ''  ">
				TRUSTEE_CERTI_TYPE = #{trustee_certi_type, jdbcType=VARCHAR} ,
			</if>
			<if test=" trustee_certi_code != null and trustee_certi_code != ''  ">
				TRUSTEE_CERTI_CODE = #{trustee_certi_code, jdbcType=TIMESTAMP} ,
			</if>
			<if test=" assignee_start_date != null">
				ASSIGNEE_START_DATE = #{assignee_start_date, jdbcType=TIMESTAMP} ,
			</if>
			<if test=" assignee_end_date != null">
				ASSIGNEE_END_DATE = #{assignee_end_date, jdbcType=TIMESTAMP} ,
			</if>
		</trim>
		</set>
		<![CDATA[ WHERE CASE_NO = #{case_no} ]]>
	</update>
	
		<!-- 按照受益人打印标识 -->
	<update id="updatePayDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			SEND_BENE_DOC_FLAG=#{send_bene_doc_flag, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	
	<!-- 理赔轨迹查询 -->
	<select id="queryClaimsTrack"  resultType="java.util.Map" parameterType="java.util.Map" >
   <![CDATA[  SELECT C.CASE_NO
                 ,(select H.CUSTOMER_ID from  APP___CLM__DBUSER.T_POLICY_HOLDER H where H.POLICY_CODE =#{POLICY_CODE} and rownum=1) AS CUSTOMER_ID
                 ,(SELECT CU.CUSTOMER_NAME FROM APP___CLM__DBUSER.T_CUSTOMER CU WHERE EXISTS 
                      (SELECT 1 FROM APP___CLM__DBUSER.T_POLICY_HOLDER H WHERE H.POLICY_CODE =#{POLICY_CODE} AND CU.CUSTOMER_ID = H.CUSTOMER_ID) AND ROWNUM=1) AS CUSTOMER_NAME
               ,(SELECT P.BUSI_PROD_CODE FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD P WHERE P.CASE_ID = C.CASE_ID AND ROWNUM=1) AS BUSI_PROD_CODE
                 ,(SELECT PRO.PRODUCT_NAME_SYS FROM APP___CLM__DBUSER.T_BUSINESS_PRODUCT PRO WHERE PRO.PRODUCT_CODE_SYS = 
                    (SELECT P.BUSI_PROD_CODE FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD P WHERE P.CASE_ID = C.CASE_ID AND ROWNUM=1)) AS PRODUCT_NAME_SYS
                 ,(SELECT E.PAY_DUE_DATE FROM APP___CLM__DBUSER.T_CONTRACT_EXTEND E WHERE E.CASE_ID = C.CASE_ID AND ROWNUM=1) PAY_DUE_DATE
                ,C.INSERT_TIME RPTR_TIME
                 ,C.REGISTE_TIME
                 ,C.END_CASE_TIME
                ,C.CASE_STATUS
                 ,C.CALC_PAY
                 ,C.INSURED_ID
                 ,(SELECT STA.NAME FROM APP___CLM__DBUSER.T_CASE_STATUS STA WHERE C.CASE_STATUS = STA.CODE) NAME
                 ,(SELECT CUS2.CUSTOMER_NAME FROM APP___CLM__DBUSER.T_CUSTOMER CUS2 WHERE C.INSURED_ID = CUS2.CUSTOMER_ID) INSURED_NAME
                 ,(SELECT STATUS_NAME FROM APP___CLM__DBUSER.T_FEE_STATUS WHERE STATUS_CODE = 
                      (SELECT ARAP.FEE_STATUS FROM APP___CLM__DBUSER.T_PREM_ARAP ARAP 
                      WHERE ARAP.DERIV_TYPE='005' 
                      AND ARAP.BUSINESS_CODE=C.CASE_NO  
                      AND ROWNUM=1))  FEE_STATUS
          FROM APP___CLM__DBUSER.T_CLAIM_CASE C
          WHERE EXISTS (SELECT 1 FROM  APP___CLM__DBUSER.T_CLAIM_POLICY P  WHERE P.CASE_ID = C.CASE_ID AND P.POLICY_CODE =#{POLICY_CODE}) ]]>
			
	</select>
	
	<!-- 保单理赔记录查询 -->
	<select id="queryPolicyClaimRecord"  resultType="java.util.Map" parameterType="java.util.Map" >
			<![CDATA[ SELECT C.CASE_NO
					       ,P.POLICY_CODE
					       ,BP.BUSI_PROD_CODE
					       ,C.INSERT_TIME RPTR_TIME
					       ,C.REGISTE_TIME
					       ,C.END_CASE_TIME
					       ,C.INSURED_ID
					       ,LIAB.LIAB_CONCLUSION
					       ,C.CASE_STATUS
					       ,SC.CLAIM_DATE
					       ,C.APPROVE_TIME
					       ,SC.CLAIM_TYPE
					       ,SC.ACC_REASON
					       ,(SELECT L.LIAB_ID FROM APP___CLM__DBUSER.T_CLAIM_LIAB L WHERE L.CASE_ID = C.CASE_ID AND ROWNUM=1) LIAB_ID
					       ,(SELECT LIA.LIAB_NAME FROM APP___CLM__DBUSER.T_LIABILITY LIA WHERE LIA.LIAB_ID = 
					                (SELECT L.LIAB_ID FROM APP___CLM__DBUSER.T_CLAIM_LIAB L WHERE L.CASE_ID = C.CASE_ID AND ROWNUM=1)) LIAB_NAME
					       ,C.AUDIT_DECISION
					       ,C.AUDIT_REJECT_REASON
                 		   ,C.OTHER_REASON
                 		   ,AC.ACC_DESC
                           ,BP.ACTUAL_PAY
                           ,PRO.AMOUNT
					FROM APP___CLM__DBUSER.T_CLAIM_CASE C
					LEFT JOIN APP___CLM__DBUSER.T_CLAIM_POLICY P ON C.CASE_ID = P.CASE_ID
					LEFT JOIN APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC ON C.CASE_ID = SC.CASE_ID
					LEFT JOIN APP___CLM__DBUSER.T_CLAIM_BUSI_PROD BP ON  C.CASE_ID = BP.CASE_ID
          			LEFT JOIN APP___CLM__DBUSER.T_CLAIM_ACCIDENT AC ON C.ACCIDENT_ID = AC.ACCIDENT_ID
          			LEFT JOIN APP___CLM__DBUSER.T_CLAIM_PRODUCT PRO ON C.CASE_ID = PRO.CASE_ID
          			LEFT JOIN APP___CLM__DBUSER.T_CLAIM_LIAB LIAB ON C.CASE_ID = LIAB.CASE_ID
					WHERE P.POLICY_CODE = #{POLICY_CODE}]]>
			
	</select>
	
	<!-- 理赔赔案号查询接口 -->
	<select id="queryClaimNo"  resultType="java.util.Map" parameterType="java.util.Map" >
			<![CDATA[select c.case_no
					       ,h.customer_id
					       ,cus.customer_name
					       ,p.policy_code
					       ,c.case_status
					       ,pay.pay_mode
					       ,c.end_case_time
					       ,c.audit_decision
					       ,a.agent_code
					from APP___CLM__DBUSER.T_CLAIM_CASE c
					,APP___CLM__DBUSER.T_CLAIM_POLICY p
					,APP___CLM__DBUSER.T_POLICY_HOLDER h
					,APP___CLM__DBUSER.T_CUSTOMER cus
					,APP___CLM__DBUSER.T_CONTRACT_AGENT a
					,APP___CLM__DBUSER.T_CLAIM_PAYEE pay
					where c.case_id = p.case_id
					and c.case_id = h.case_id
					and h.customer_id = cus.customer_id
					and c.case_id = a.case_id
					and c.case_id = pay.case_id
			]]>
		<if test=" customer_name != null and customer_name != ''  ">
			<![CDATA[ AND cus.customer_name like '%${customer_name}%' ]]>
		</if>
		<if test=" policy_code != null and policy_code != ''  ">
			<![CDATA[ AND p.policy_code = #{policy_code} ]]>
		</if>
		<if test=" agent_code != null and agent_code != ''  ">
			<![CDATA[ AND a.agent_code = #{agent_code} ]]>
		</if>
		<if test=" startDate != null and startDate != ''  ">
			<![CDATA[ AND c.rptr_time >= #{startDate} ]]>
		</if>
		<if test=" endDate != null and endDate != ''  ">
			<![CDATA[ AND c.rptr_time <= #{endDate} ]]>
		</if>
	</select>

    <!-- 外包录入更新主表状态 -->
	<update id="updateClaimCaseByOutSource" parameterType="java.util.Map">
		<![CDATA[
			UPDATE APP___CLM__DBUSER.T_CLAIM_CASE  
		]]>
		<set>
			<trim suffixOverrides=",">
				  UPDATE_TIME = SYSDATE , 
				  UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				  CASE_STATUS = #{case_status,jdbcType=VARCHAR},
			</trim>
		</set>
		<![CDATA[ WHERE CASE_NO = #{case_no,jdbcType=VARCHAR} ]]>
	</update>
    <!-- 查询签收人是否在岗（有效，能接收任务）-->
	<select id="findSignerIdByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				 SELECT A.SIGNER_ID,B.ORGAN_ID AS ACTUAL_PAY, B.USER_NAME AS RPTR_NAME,B.EMAIL AS RPTR_EMAIL 
				   FROM APP___CLM__DBUSER.T_CLAIM_CASE A
				   JOIN APP___CLM__DBUSER.T_UDMP_USER B
				     ON A.SIGNER_ID = B.USER_ID
				  WHERE 1 = 1
    				AND CASE_NO = #{case_no,jdbcType=VARCHAR} 
		]]>
	</select>	
	<!-- add by xinghj 用于批处理自动立案 为的是不重置updateby -->
	<update id="updateAuditPermissionByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
			<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE , 
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				AUDIT_PERMISSION_NAME = #{audit_permission_name, jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	
	<update id="updateCalcPayAndActualPay" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
			<trim suffixOverrides=",">
				 UPDATE_TIME = SYSDATE , 
				 UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				 CALC_PAY = #{calc_pay, jdbcType=NUMERIC} ,
				 ACTUAL_PAY = #{actual_pay, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
	<!-- end by  xinghj -->
	<!-- 申请修改报案  确认赔案待签收-->
	<select id="findIfWaitSignIn" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT CASE_ID,
				       ACCIDENT_ID,
				       CASE_NO,
				       INSURED_ID,
				       RPTR_RELATION,
				       RPTR_NAME,
				       RPTR_MP,
				       RPTR_ZIP,
				       RPTR_ADDR,
				       RPTR_EMAIL,
				       REPORT_MODE,
				       ORGAN_CODE,
				       RPTR_TIME,
				       CASE_APPLY_TYPE,
				       APPLY_DATE,
				       ACCEPTOR_ID,
				       ACCEPT_TIME,
				       TRUSTEE_TYPE,
				       TRUSTEE_CODE,
				       TRUSTEE_NAME,
				       TRUSTEE_MP,
				       TRUSTEE_TEL,
				       TRUSTEE_CERTI_TYPE,
				       TRUSTEE_CERTI_CODE,
				       DOOR_SIGN_TIME,
				       SIGN_TIME,
				       RPTR_ID,
				       SIGNER_ID,
				       SERIOUS_DISEASE,
				       ACCIDENT_DETAIL,
				       CURE_HOSPITAL,
				       CURE_STATUS,
				       DOCTOR_NAME,
				       MED_DEPT,
				       CASE_SUB_STATUS,
				       CASE_STATUS,
				       GREEN_FLAG,
				       IS_BPO,
				       CASE_FLAG,
				       REVIEW_FLAG,
				       COMFORT_FLAG,
				       ADVANCE_ASK_FLAG,
				       ADVANCE_FLAG,
				       IS_DEDUCT_FLAG,
				       REPEAL_REASON,
				       REPEAL_DESC,
				       REGISTER_ID,
				       REGISTE_TIME,
				       REGISTE_CONF_TIME,
				       ACCEPT_DECISION,
				       REJECT_REASON,
				       CALC_PAY,
				       ADVANCE_PAY,
				       BALANCE_PAY,
				       ACTUAL_PAY,
				       REJECT_PAY,
				       AUDIT_TIME,
				       AUDITOR_ID,
				       AUDIT_DECISION,
				       AUDIT_REMARK,
				       AUDIT_REJECT_REASON,
				       OTHER_REASON,
				       APPROVER_ID,
				       APPROVE_TIME,
				       APPROVE_DECISION,
				       APPROVE_REJECT_REASON,
				       APPROVE_REMARK,
				       END_CASE_TIME,
				       OVER_COMP_FLAG,
				       RELATED_NO,
				       CLAIM_SOURCE,
				       IS_COMMON,
				       INSERT_BY,
				       INSERT_TIME,
				       INSERT_TIMESTAMP,
				       UPDATE_BY,
				       UPDATE_TIME,
				       UPDATE_TIMESTAMP,
				       AUDIT_PERMISSION_NAME,
				       APPROVE_PERMISSION_NAME,
				       SURVEY_RESULT_INFO,
				       CHANNEL_CODE,
				       IS_AUTO_HUNGUP,
				       SPECIAL_REMARK_CODE,
				       LOSS_REASON_CODE,
				       LOSS_LEVEL_CODE,
				       COMFORT_STATUS,
				       IS_MIGRATION,EASY_AUDIT_DECISION,EASY_AUDITOR_ID,FIRST_RPTR_TIME,ACQUIST_WAY,IS_CALLED_BACK,REPORT_SOURCE FROM APP___CLM__DBUSER.T_CLAIM_CASE 
		WHERE CASE_STATUS = 20 AND CASE_NO =#{case_no} AND INSURED_ID =#{insured_id} ]]>
	</select>
	<!-- 网站在线自助报案客户校验接口  是否公司客户-->
	<select id="findIfCompanyCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT A.CUSTOMER_ID,
       A.UN_CUSTOMER_CODE,
       A.MARRIAGE_DATE,
       A.EDUCATION,
       A.CUSTOMER_NAME,
       A.CUSTOMER_BIRTHDAY,
       A.CUSTOMER_GENDER,
       A.CUSTOMER_HEIGHT,
       A.CUSTOMER_WEIGHT,
       A.CUSTOMER_CERT_TYPE,
       A.CUSTOMER_CERTI_CODE,
       A.CUSTOMER_ID_CODE,
       A.CUST_CERT_STAR_DATE,
       A.CUST_CERT_END_DATE,
       A.JOB_CODE,
       A.JOB_NATURE,
       A.JOB_KIND,
       A.JOB_TITLE,
       A.MARRIAGE_STATUS,
       A.IS_PARENT,
       A.ANNUAL_INCOME,
       A.COUNTRY_CODE,
       A.RELIGION_CODE,
       A.NATION_CODE,
       A.DRIVER_LICENSE_TYPE,
       A.COMPANY_NAME,
       A.OFFEN_USE_TEL,
       A.HOUSE_TEL,
       A.FAX_TEL,
       A.OFFICE_TEL,
       A.MOBILE_TEL,
       A.EMAIL,
       A.QQ,
       A.WECHAT_NO,
       A.OTHER,
       A.CUSTOMER_LEVEL,
       A.CUSTOMER_VIP,
       A.SMOKING_FLAG,
       A.DRUNK_FLAG,
       A.BLACKLIST_FLAG,
       A.HOUSEKEEPER_FLAG,
       A.SYN_MDM_FLAG,
       A.LIVE_STATUS,
       A.RETIRED_FLAG,
       A.DEATH_DATE,
       A.HEALTH_STATUS,
       A.REMARK,
       A.CUST_PWD,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.OLD_CUSTOMER_ID,
       A.CUSTOMER_RISK_LEVEL
      FROM APP___CLM__DBUSER.T_CUSTOMER A
       WHERE 
		A.CUSTOMER_NAME=#{customer_name} AND 
		A.CUSTOMER_GENDER=#{customer_gender} AND 
		A.CUSTOMER_CERT_TYPE=#{customer_cert_type} AND 
		A.CUSTOMER_CERTI_CODE=#{customer_certi_code} AND 
		EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_INSURED_LIST B WHERE A.CUSTOMER_ID=B.CUSTOMER_ID)
		]]>
	</select>
		<!-- 网站在线自助报案客户校验接口   根据出险人客户号查询有效状态险种下的赔案信息-->
	<select id="findIfMateCustomerRisk" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT CASE_ID,
       ACCIDENT_ID,
       CASE_NO,
       INSURED_ID,
       RPTR_RELATION,
       RPTR_NAME,
       RPTR_MP,
       RPTR_ZIP,
       RPTR_ADDR,
       RPTR_EMAIL,
       REPORT_MODE,
       ORGAN_CODE,
       RPTR_TIME,
       CASE_APPLY_TYPE,
       APPLY_DATE,
       ACCEPTOR_ID,
       ACCEPT_TIME,
       TRUSTEE_TYPE,
       TRUSTEE_CODE,
       TRUSTEE_NAME,
       TRUSTEE_MP,
       TRUSTEE_TEL,
       TRUSTEE_CERTI_TYPE,
       TRUSTEE_CERTI_CODE,
       DOOR_SIGN_TIME,
       SIGN_TIME,
       RPTR_ID,
       SIGNER_ID,
       SERIOUS_DISEASE,
       ACCIDENT_DETAIL,
       CURE_HOSPITAL,
       CURE_STATUS,
       DOCTOR_NAME,
       MED_DEPT,
       CASE_SUB_STATUS,
       CASE_STATUS,
       GREEN_FLAG,
       IS_BPO,
       CASE_FLAG,
       REVIEW_FLAG,
       COMFORT_FLAG,
       ADVANCE_ASK_FLAG,
       ADVANCE_FLAG,
       IS_DEDUCT_FLAG,
       REPEAL_REASON,
       REPEAL_DESC,
       REGISTER_ID,
       REGISTE_TIME,
       REGISTE_CONF_TIME,
       ACCEPT_DECISION,
       REJECT_REASON,
       CALC_PAY,
       ADVANCE_PAY,
       BALANCE_PAY,
       ACTUAL_PAY,
       REJECT_PAY,
       AUDIT_TIME,
       AUDITOR_ID,
       AUDIT_DECISION,
       AUDIT_REMARK,
       AUDIT_REJECT_REASON,
       OTHER_REASON,
       APPROVER_ID,
       APPROVE_TIME,
       APPROVE_DECISION,
       APPROVE_REJECT_REASON,
       APPROVE_REMARK,
       END_CASE_TIME,
       OVER_COMP_FLAG,
       RELATED_NO,
       CLAIM_SOURCE,
       IS_COMMON,
       INSERT_BY,
       INSERT_TIME,
       INSERT_TIMESTAMP,
       UPDATE_BY,
       UPDATE_TIME,
       UPDATE_TIMESTAMP,
       AUDIT_PERMISSION_NAME,
       APPROVE_PERMISSION_NAME,
       SURVEY_RESULT_INFO,
       CHANNEL_CODE,
       IS_AUTO_HUNGUP,
       SPECIAL_REMARK_CODE,
       LOSS_REASON_CODE,
       LOSS_LEVEL_CODE,
       COMFORT_STATUS,
       IS_MIGRATION,EASY_AUDIT_DECISION,EASY_AUDITOR_ID,FIRST_RPTR_TIME,ACQUIST_WAY FROM APP___CLM__DBUSER.T_CLAIM_CASE 
		WHERE CASE_ID IN (SELECT CASE_ID FROM APP___CLM__DBUSER.T_BENEFIT_INSURED A WHERE EXISTS(SELECT 1 FROM APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD B WHERE A.BUSI_ITEM_ID=B.BUSI_ITEM_ID AND B.LIABILITY_STATE=1)) AND INSURED_ID=#{insured_id}
		]]>
		
	</select>
	<!-- 查询个数操作 -->
	<select id="findClaimAdvancePayStateCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_CASE CC, APP___CLM__DBUSER.T_CLAIM_SUB_CASE CSC
			 WHERE CC.CASE_ID=CSC.CASE_ID AND CSC.CLAIM_TYPE IN ('03','08') AND CC.ADVANCE_FLAG=1 AND CC.CASE_STATUS NOT IN (80,90)
		  ]]>
		<if test=" insured_id != null and insured_id != ''  ">
			<![CDATA[ AND INSURED_ID =#{insured_id} ]]>
		</if>
	</select>
	<select id="findClaimAdvancePayStateInfo" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT CSC.* FROM APP___CLM__DBUSER.T_CLAIM_CASE CC, APP___CLM__DBUSER.T_CLAIM_SUB_CASE CSC
			 WHERE CC.CASE_ID=CSC.CASE_ID AND CC.CASE_STATUS NOT IN (80,90)  AND CC.CASE_STATUS IN (50, 51, 52, 53, 54)
		  ]]>
		<if test=" insured_id != null and insured_id != ''  ">
			<![CDATA[ AND INSURED_ID =#{insured_id} ]]>
		</if>
	</select>
	
	<!-- 查询审批详细信息 -->
	<select id="queryApproveDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select ntable.case_id,
       ntable.case_no,
       ntable.insured_id,
       ntable.approve_permission_name,
       ntable.audit_time,
       ntable.green_flag,
       ntable.organ_code,
       ntable.advance_flag,
       ntable.customer_name,
       ntable.customer_certi_code,
       ntable.survey_status,
       ntable.uw_status,
       ntable.treaty_status,
       ntable.discuss_status,
       ntable.qc_status,
       ntable.auditor,
       ntable.auditor_id,
       ntable.channel_code,
       ntable.pay_mode_name,
       ntable.bank_name
  from (select distinct sa.apply_id,
                        u.clm_uw_id,
                        cc.case_id,
                        cc.case_no,
                        cc.insured_id,
                        cc.approve_permission_name,
                        cc.audit_time,
                        cc.green_flag,
                        cc.organ_code,
                        cc.advance_flag,
                        cu.customer_name,
                        cu.customer_certi_code,
                        sa.survey_status,
                        u.uw_status,
                        t.treaty_status,
                        cd.discuss_status,
                        mt.qc_status,
                        cc.auditor_id,
                        cc.channel_code,
                        (SELECT to_char(replace(wm_concat(distinct c1.code),',',';'))
          FROM APP___CLM__DBUSER.t_claim_pay   a1,
               APP___CLM__DBUSER.t_claim_payee a2,
               APP___CLM__DBUSER.t_Pay_Mode    c1
         where a1.case_id = a2.case_id
           and a1.payee_id = a2.payee_id
           and c1.code = a2.pay_mode
           and a1.case_id = cc.case_id) PAY_MODE_NAME,
                        (SELECT to_char(replace(wm_concat(distinct c1.bank_name),',',';'))
          FROM APP___CLM__DBUSER.t_claim_pay   a1,
               APP___CLM__DBUSER.t_claim_payee a2,
               APP___CLM__DBUSER.t_Bank        c1
         where a1.case_id = a2.case_id
           and a1.payee_id = a2.payee_id
           and c1.bank_code = a2.bank_code
              
           and a1.case_id = cc.case_id) bank_NAME,
                        (select uu.user_name
                           from APP___CLM__DBUSER.t_udmp_user uu
                          where uu.user_id = cc.auditor_id) auditor
          from APP___CLM__DBUSER.t_claim_case cc
          left join APP___CLM__DBUSER.t_customer cu
            on cc.insured_id = cu.customer_id
          left join APP___CLM__DBUSER.T_SURVEY_APPLY sa
            on cc.case_id = sa.case_id
          left join APP___CLM__DBUSER.T_CLAIM_UW u
            on cc.case_id = u.case_id
          left join APP___CLM__DBUSER.T_CLAIM_TREATY_TALK t
            on cc.case_id = t.case_id
          left join APP___CLM__DBUSER.T_CLAIM_DISCUSS cd
            on cc.case_id = cd.case_id
          left join APP___CLM__DBUSER.T_CLAIM_MIDC_TASK mt
            on cc.case_id = mt.case_id
          left join APP___CLM__DBUSER.T_CLAIM_PAYEE a1
            on cc.case_id = a1.case_id
         where cc.case_no = #{case_no}
         group by sa.apply_id,
                  u.clm_uw_id,
                  cc.case_id,
                  cc.case_no,
                  cc.insured_id,
                  cc.approve_permission_name,
                  cc.audit_time,
                  cc.green_flag,
                  cc.organ_code,
                  cc.advance_flag,
                  cu.customer_name,
                  cu.customer_certi_code,
                  sa.survey_status,
                  u.uw_status,
                  t.treaty_status,
                  cd.discuss_status,
                  mt.qc_status,
                  cc.auditor_id,
                  cc.channel_code,
                  a1.pay_mode
         order by sa.apply_id, u.clm_uw_id desc) ntable
 where rownum = 1
  ]]>
	</select>
	<!-- 根据赔案号查询理赔类型为身故，出险人下保单是否存在受益人为法定受益人   -xinghj -->
	<select id="isLegalBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
						 A.CASE_ID,
						 C.LEGAL_BENE,
						 C.POLICY_CODE
  			FROM 
  						 APP___CLM__DBUSER.T_CLAIM_CASE     A,
       					 APP___CLM__DBUSER.T_CLAIM_SUB_CASE B,
            			 APP___CLM__DBUSER.T_CONTRACT_BENE       C
            WHERE 
            			 A.CASE_ID = B.CASE_ID
     					 AND A.CASE_ID = C.CASE_ID
	 					 AND B.CLAIM_TYPE = '01'
	 					 AND C.LEGAL_BENE = '0'
	 					 AND C.CUR_FLAG = '1'
	 					 AND A.CASE_NO = #{case_no}
		]]>
	</select>
	<!-- 二核功能 通过uw_id查询赔案ID -->
	<select id="findCaseIdByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT CC.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_CASE CC, APP___CLM__DBUSER.T_UW_MASTER UM 
		           WHERE CC.CASE_NO = UM.BIZ_CODE AND UM.UW_ID = #{uw_id}
		  ]]>
	</select>
	
	<!--查询注销人员信息 -->
	<select id="findAllCustomerAboutBatch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
SELECT  M.CUSTOMER_ID,
        M.CUSTOMER_NAME,
        M.CUSTOMER_ID_CODE,
        M.CASE_NO,
        M.SIGN_ORGAN from (
SELECT DISTINCT TC.CUSTOMER_ID,
                A.CUSTOMER_NAME,
                A.CUSTOMER_ID_CODE,
                A.CASE_NO,
                B.SIGN_ORGAN,
                rownum rn
           FROM APP___CLM__DBUSER.T_LOGOUT_PERSONNEL_DELAY A,
                APP___CLM__DBUSER.T_CLAIM_CASE             B,
                APP___CLM__DBUSER.T_CUSTOMER               TC
          WHERE A.CASE_NO = B.CASE_NO
            AND B.INSURED_ID = TC.CUSTOMER_ID
            AND A.LOGOUT_STATE != '1'
            AND B.END_CASE_TIME BETWEEN to_date(#{time_start},'yyyy-mm-dd') AND to_date(#{time_end},'yyyy-mm-dd')
UNION 
SELECT DISTINCT TC.CUSTOMER_ID,
                A.CUSTOMER_NAME,
                A.CUSTOMER_ID_CODE,
                A.CASE_NO,
                B.SIGN_ORGAN,
                rownum rn
           FROM APP___CLM__DBUSER.T_LOGOUT_PERSONNEL_DELAY A,
                APP___CLM__DBUSER.T_CLAIM_CASE             B,
                APP___CLM__DBUSER.T_CUSTOMER               TC
          WHERE A.CASE_NO = B.CASE_NO
            AND B.INSURED_ID = TC.CUSTOMER_ID
            AND A.LOGOUT_STATE IS NULL
            AND B.END_CASE_TIME BETWEEN to_date(#{time_start},'yyyy-mm-dd') AND to_date(#{time_end},'yyyy-mm-dd')
              ) M
      WHERE  mod(M.RN,#{modNum}) = #{start}
		  ]]>
	</select>
	<!--查询回退赔案的实际赔案号 -->
	<select id="queryRedoCaseNoByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT CC.CASE_NO,CC.CASE_ID,CC.CASE_STATUS FROM (
			SELECT A.RELATED_NO,A.* FROM APP___CLM__DBUSER.T_CLAIM_CASE A  
			START WITH A.CASE_NO =#{case_no}
			CONNECT BY PRIOR A.CASE_NO =  A.RELATED_NO ) CC ORDER BY CC.CASE_ID DESC
		  ]]>
	</select>
	
	<!-- 查询机构所在片区下的所有用户 -->
	<select id="findOrganAreaUser" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select uu.user_id,uu.user_name,uu.real_name from APP___CLM__DBUSER.t_udmp_user uu where uu.user_id in (
			select ap.user_id from APP___CLM__DBUSER.T_CLAIM_AREA_PERSON ap where ap.area_id in (
			select a.area_id from APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN a where a.organ_code = #{organ_code})
			union
			select u.user_id from APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN b,APP___CLM__DBUSER.t_udmp_user u 
			where b.organ_code = u.organ_code and B.ORGAN_CODE = #{organ_code} )
			 and uu.user_disable= 'N'
			and ROWNUM <= 999
		  ]]>
	</select>

			
	<!-- 通过客户ID查询该客户是否做过身故 -->
	<select id="findCaseIdByCustomerID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select c.case_id from APP___CLM__DBUSER.t_claim_case c,APP___CLM__DBUSER.t_claim_sub_case sc
                  where c.case_id = sc.case_id and sc.claim_type = '01' and c.insured_id = #{insured_id}
		  ]]>
	</select>
	
	<!-- 通过caseId查询相邻前次重疾疾病代码 -->
	<select id="findZj001ByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select c.serious_disease,cs.claim_date,c.case_id from APP___CLM__DBUSER.t_claim_case c,APP___CLM__DBUSER.t_claim_sub_case cs 
             where c.case_id = cs.case_id
             and c.related_no is null
             and c.case_status=80]]>
             <if test=" case_id_str != null and case_id_str != ''  ">
				<![CDATA[and c.case_id in(${case_id_str})]]>
			</if>
			<if test=" claim_date != null and claim_date != ''  ">
				<![CDATA[ and trunc(cs.claim_date) <= trunc(#{claim_date})]]>
			</if>
	       	<![CDATA[
             order by cs.claim_date desc
		  ]]>
	</select>
	
	<!-- 通过caseId查询既往事故 -->
	<select id="findAccidentByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT C.CASE_ID,C.CASE_STATUS,C.ACCIDENT_ID,C.INSURED_ID,C.CASE_NO FROM APP___CLM__DBUSER.T_CLAIM_CASE C 
       WHERE C.CASE_STATUS != '99']]>
        <if test=" case_id != null and case_id != ''  ">
				<![CDATA[ AND C.ACCIDENT_ID = (SELECT ACCIDENT_ID FROM APP___CLM__DBUSER.T_CLAIM_CASE where CASE_ID = #{case_id})]]>
		</if>
		<if test=" case_id != null and case_id != ''  ">
				<![CDATA[ AND C.INSURED_ID=(SELECT INSURED_ID FROM APP___CLM__DBUSER.T_CLAIM_CASE where CASE_ID=#{case_id})]]>
		</if>
		<if test=" case_id != null and case_id != ''  ">
				<![CDATA[ AND C.CASE_ID NOT IN(${case_id})]]>
		</if>
	</select>
	
	<!-- 查询出险日期 -->
	<select id="findcaseidclaimsubcaserecord" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	SELECT A.CLAIM_DATE,A.CASE_ID from APP___CLM__DBUSER.T_CLAIM_SUB_CASE_RECORD A WHERE A.CASE_ID=#{case_id}
	
	 ]]>
	</select>
	
	<!-- r00101003015客户理赔记录查询接口 -->
	<select id="findClientClaimsRecordQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
    
    SELECT    T.CASE_ID,
          T.CASE_NO  AS RGTNO,
          T.INSURED_ID,
     (SELECT MAX(CAS.CLAIM_DATE) FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE CAS WHERE CAS.CASE_ID = T.CASE_ID)   CLAIM_DATE,  
    (SELECT A.NAME FROM APP___CLM__DBUSER.T_CASE_STATUS A WHERE A.CODE=T.CASE_STATUS) AS CLMSTATE,
    (SELECT A.CLMT_NAME FROM APP___CLM__DBUSER.T_CLAIM_APPLICANT A WHERE A.CASE_ID=T.CASE_ID AND ROWNUM=1) AS RGTANTNAME,
    (SELECT (SELECT B.RELATION_NAME FROM APP___CLM__DBUSER.T_LA_PH_RELA B WHERE B.RELATION_CODE=A.CLMT_INSUR_RELATION)
    FROM APP___CLM__DBUSER.T_CLAIM_APPLICANT A WHERE A.CASE_ID=T.CASE_ID AND ROWNUM=1) AS RELATION,
    CL.ACC_DESC,
    (SELECT A.NAME FROM APP___CLM__DBUSER.T_CLAIM_ACCEPT_DECISION A WHERE A.CODE=T.ACCEPT_DECISION) AS RGTCONCLUSION,  
    (SELECT SUM(A.PAY_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_ADJUST_BUSI A WHERE A.CASE_ID=T.CASE_ID),
    T.ACTUAL_PAY AS REALPAY
    FROM APP___CLM__DBUSER.T_CLAIM_CASE T,
    APP___CLM__DBUSER.T_CLAIM_ACCIDENT CL
    WHERE T.ACCIDENT_ID = CL.ACCIDENT_ID 
    AND T.CASE_STATUS = '80'
    AND T.INSURED_ID=#{insured_id} ORDER BY T.CASE_NO  DESC
	 ]]>
	</select>
	
		<!-- r00101003015客户理赔记录查询接口 -->
	<select id="findClientClaimBusiprod" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	    SELECT A.BUSI_PROD_CODE,
           A.POLICY_CODE,
           (SELECT B.PRODUCT_ABBR_NAME
              FROM APP___CLM__DBUSER.T_BUSINESS_PRODUCT B
             WHERE B.PRODUCT_CODE_SYS = A.BUSI_PROD_CODE) AS BUSI_PROD_NAME,
           D.AGENT_CODE
      FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD A,
           APP___CLM__DBUSER.T_CONTRACT_AGENT  D
     WHERE A.CASE_ID = D.CASE_ID
       AND A.POLICY_CODE = D.POLICY_CODE
       AND D.IS_CURRENT_AGENT = '1'
       AND D.CUR_FLAG = '1'
       AND A.CASE_ID =#{case_id}	

	 ]]>
	</select>
	<!-- 查询有相同门诊账单号的赔案 -->
	<select id="findCaseNoByBillNo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT ROWNUM RN,M.* FROM ( SELECT DISTINCT A1.CASE_NO,A1.INSURED_ID
        FROM APP___CLM__DBUSER.T_CLAIM_CASE A,
             APP___CLM__DBUSER.T_CLAIM_BILL B,
             APP___CLM__DBUSER.T_CLAIM_CASE A1,
             APP___CLM__DBUSER.T_CLAIM_BILL B1
       WHERE A.CASE_ID = B.CASE_ID
         AND A1.CASE_ID = B1.CASE_ID
         AND B1.BILL_NO = B.BILL_NO   
         AND B1.HOSPITAL_CODE = B.HOSPITAL_CODE  ]]>
         <if test=" bill_type != null and bill_type != ''  ">
				<![CDATA[ AND B1.TREAT_TYPE = 0]]>
		</if>
		<![CDATA[
         AND A.CASE_ID = #{case_id}
         AND B1.BILL_NO= #{bill_no}
         AND A1.CASE_ID != A.CASE_ID
         AND A1.CASE_STATUS NOT IN(10,20,21,90) ) M]]>
	</select>
	
	<!-- 查询同一出险人的既往赔案 -->
	<select id="findCaseNoByBillNoAndInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT ROWNUM RN,M.* FROM ( SELECT DISTINCT A1.CASE_NO,A1.INSURED_ID
        FROM APP___CLM__DBUSER.T_CLAIM_CASE A,
             APP___CLM__DBUSER.T_CLAIM_BILL B,
             APP___CLM__DBUSER.T_CLAIM_CASE A1,
             APP___CLM__DBUSER.T_CLAIM_BILL B1
       WHERE A.CASE_ID = B.CASE_ID
         AND A1.CASE_ID = B1.CASE_ID
         AND B1.BILL_NO = B.BILL_NO
         AND A1.CASE_STATUS != '99'
         AND B1.HOSPITAL_CODE = B.HOSPITAL_CODE  ]]>
         <if test=" bill_type != null and bill_type != ''  ">
				<![CDATA[ AND B1.TREAT_TYPE = 0]]>
		</if>
		<if test=" insured_id != null and insured_id != ''  ">
				<![CDATA[ AND A.INSURED_ID = #{insured_id}]]>
		</if>
		<![CDATA[
         AND A.CASE_ID = #{case_id}
         AND B1.BILL_NO= #{bill_no}
         AND A1.CASE_ID != A.CASE_ID
         AND A1.CASE_STATUS NOT IN(10,20,21,90) ) M]]>
	</select>
	<!-- 查询出险人相同的门诊赔案 -->
	<select id="findCasesByBillTreat" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT ROWNUM RN,M.* FROM ( SELECT A.CASE_NO,A.INSURED_ID,
				C.CUSTOMER_NAME,
               	C.CUSTOMER_GENDER,
               	C.CUSTOMER_BIRTHDAY,
               	C.CUSTOMER_CERTI_CODE,
               	C.CUSTOMER_ID,
				COUNT(DISTINCT B.BILL_NO) BILLNO_COUNT
               FROM APP___CLM__DBUSER.T_CLAIM_CASE A, APP___CLM__DBUSER.T_CLAIM_BILL B,
               APP___CLM__DBUSER.T_CUSTOMER C
              WHERE A.CASE_ID = B.CASE_ID
              	AND A.INSURED_ID = C.CUSTOMER_ID
              	AND A.CASE_STATUS != '99' 
                AND A.CASE_STATUS NOT IN (10, 20, 21, 90)
                AND B.TREAT_START = #{treat_start}]]>
                <if test=" bill_type != null and bill_type != ''  ">
				<![CDATA[ AND B.TREAT_TYPE = 0]]>
				</if>
				<if test=" customer_gender != null and customer_gender != ''  ">
				<![CDATA[ AND C.CUSTOMER_GENDER = #{customer_gender} ]]>
				</if>
				<if test=" customer_birthday != null and customer_birthday != ''  ">
				<![CDATA[ AND C.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]>
				</if>
           		<if test=" customer_certi_code != null and customer_certi_code != ''  ">
				<![CDATA[ AND C.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]>
				</if>
           		<if test=" customer_name != null and customer_name != ''  ">
				<![CDATA[ AND C.CUSTOMER_NAME = #{customer_name} ]]>
				</if>
                <![CDATA[
           GROUP BY A.CASE_NO,A.INSURED_ID,C.CUSTOMER_NAME,
               C.CUSTOMER_GENDER,
               C.CUSTOMER_BIRTHDAY,
               C.CUSTOMER_CERTI_CODE,
               C.CUSTOMER_ID ) M ]]>
	</select>
	<!-- 账单号相同，开始日期相同、治疗医院代码相同、理赔类型相同 的赔案 -->
	<select id="findCasesByBillOther" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT ROWNUM RN,M.* FROM ( SELECT DISTINCT A.CASE_NO,A.INSURED_ID
               FROM APP___CLM__DBUSER.T_CLAIM_CASE A,
                    APP___CLM__DBUSER.T_CLAIM_BILL B,
                    APP___CLM__DBUSER.T_CLAIM_SUB_CASE C
              WHERE A.CASE_ID = B.CASE_ID
                AND A.CASE_ID = C.CASE_ID
                AND B.BILL_NO = #{bill_no}
                AND B.TREAT_START = #{treat_start}
                AND B.HOSPITAL_CODE = #{hospital_code}
                AND C.CLAIM_TYPE IN (${claim_type})
                AND A.CASE_ID != #{case_id}  ) M ]]>
	</select>
	<select id="updateRepeatAndCaseFalg" parameterType="java.util.Map">
	<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE A SET 
				A.UPDATE_TIME = SYSDATE , 
				A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				A.REPEAT_NUMBER_FLAG = #{repeat_number_flag} WHERE A.CASE_ID = #{case_id}
				]]>
	</select>
	
	<select id="updateRepeat" parameterType="java.util.Map">
	<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE A SET  
				A.UPDATE_TIME = SYSDATE , 
				A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				A.REPEAT_NUMBER_FLAG = #{repeat_number_flag} WHERE A.CASE_ID = #{case_id}]]>
	</select>
	<!-- 回退申请检验是否为上海医保产品案件 -->
	<select id="redoCheckSHByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT CC.CASE_ID,CC.CASE_NO FROM APP___CLM__DBUSER.T_CLAIM_CASE CC,APP___CLM__DBUSER.T_CLAIM_BUSI_PROD CBP
			WHERE CC.CASE_ID=CBP.CASE_ID AND CC.CASE_STATUS='80' AND CBP.BUSI_PROD_CODE IN ('00557000','00558000','00842000','00843000','00868000')
			AND CC.CASE_ID = #{case_id } ]]>
	</select>
	
	<!-- 查询关于未补录报案数据 -->
	<select id="findAllClaimReportSupplementInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			SELECT ROWNUM,M.* FROM (SELECT DISTINCT A.CASE_NO,A.CASE_ID
			  FROM DEV_CLM.T_CLAIM_CASE A, DEV_CLM.T_CONTRACT_MASTER B
			 WHERE A.CASE_NO LIKE '90%'
			   AND A.CASE_STATUS = '20'
			   AND A.CASE_ID != B.CASE_ID ORDER BY A.CASE_ID)M WHERE MOD(M.CASE_ID, #{counts}) = #{start}]]>
	</select>
	
	<!-- 获取审核、审批管理的共享池、个人任务列表中的“理赔时效”达到配置的天数的理赔数据 -->
	<select id="findAuditApprovalForOvertime" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			select *
			  from (SELECT A.Case_Id,
			               a.case_no,
			               a.sign_organ
			          FROM dev_clm.t_claim_case a
			         where ceil((sysdate-a.SIGN_TIME)* 24) > #{claimAging}
			           and a.case_status >= 60
			           and a.case_status <= 71
			           and a.is_migration is null
			        ) M
			WHERE MOD(M.CASE_ID, #{counts}) = #{start}]]>
	</select>
	
<!-- 查询个人信息(延期校验使用) -->	
    <select id="findCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ 
             SELECT A.CUSTOMER_NAME,
                    A.CUSTOMER_ID_CODE
               FROM APP___CLM__DBUSER.T_CUSTOMER   A
              WHERE A.CUSTOMER_ID = #{customer_id}
             ]]>
    <![CDATA[ ORDER BY A.CUSTOMER_ID ]]>
  </select>
	
	
	<!-- 理赔反欺诈关联图谱接口查询赔案相关信息 -->	
    <select id="findCaseFrmsInfo" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ 
             SELECT DISTINCT C.CASE_NO,
       C.CALC_PAY,
       CASE WHEN LENGTH(C.RPTR_MP)=11 THEN C.RPTR_MP ELSE NULL END RPTR_MP,
       C.INSURED_ID，
       C.REGISTE_TIME,
       C.ACTUAL_PAY,
       C.CHANNEL_CODE,
       C.ORGAN_CODE,
       C.AUDIT_DECISION,
       C.ACCIDENT_DETAIL,
       C.AUDIT_REJECT_REASON,
       (SELECT SUM(A.SUM_AMOUNT) FROM DEV_CLM.T_CLAIM_BILL A WHERE A.CASE_ID = C.CASE_ID) SUM_AMOUNT,
       (SELECT SUM(A.DAY_TOTAL) FROM DEV_CLM.T_CLAIM_BILL A WHERE A.CASE_ID = C.CASE_ID) DAY_TOTAL,
       (SELECT TO_CHAR(WM_CONCAT(DISTINCT A.CLMT_MP)) FROM DEV_CLM.T_CLAIM_APPLICANT A WHERE A.CASE_ID=C.CASE_ID) CLMT_MP，
       (SELECT TO_CHAR(SYSDATE,'YYYY')-TO_CHAR(A.CUSTOMER_BIRTHDAY,'YYYY') FROM DEV_CLM.T_CUSTOMER A WHERE A.CUSTOMER_ID=C.INSURED_ID) CUSTOMER_AGE,
       (SELECT A.ACC_DATE FROM DEV_CLM.T_CLAIM_ACCIDENT A WHERE A.ACCIDENT_ID = C.ACCIDENT_ID) ACC_DATE,
       (SELECT ACC_REASON FROM DEV_CLM.T_CLAIM_ACCIDENT A WHERE A.ACCIDENT_ID = C.ACCIDENT_ID) ACC_REASON,
       (SELECT A.ACC_RESULT1 FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT A WHERE A.CASE_ID=C.CASE_ID AND ROWNUM=1) ACC_RESULT1,
       (SELECT A.ACC_RESULT2 FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT A WHERE A.CASE_ID=C.CASE_ID AND ROWNUM=1) ACC_RESULT2,
       (SELECT SUBSTR(A.CUSTOMER_ID_CODE,1,6) FROM DEV_CLM.T_CUSTOMER A WHERE A.CUSTOMER_ID=C.INSURED_ID) CUSTOMER_ID_CODE,
       (SELECT TO_CHAR(A.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') FROM DEV_CLM.T_CUSTOMER A WHERE A.CUSTOMER_ID=C.INSURED_ID) CUSTOMER_BIRTHDAY
FROM DEV_CLM.T_CLAIM_CASE C WHERE C.CASE_ID=#{case_id}  
             ]]>
   
  </select>
  <select id="updateClaimCaseIsChecklist">
  <![CDATA[UPDATE APP___CLM__DBUSER.T_CLAIM_CASE A SET 
  			A.UPDATE_TIME = SYSDATE , 
			A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
  			A.IS_CHECKLIST = #{is_checklist} where A.CASE_ID=#{case_id} ]]>
  </select>
  
  <select id="findSpecialClaimCase" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			  SELECT A.CASE_ID,A.CASE_NO,A.ORGAN_CODE,A.AUDITOR_ID,A.AUDIT_PERMISSION_NAME,A.APPROVE_PERMISSION_NAME,A.CASE_FLAG FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1 = 1      ]]>
	    <if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
  </select>
  <!-- 详设要求，该方法来至SignSharePoolDaoImpl.findAllClaimCaseByCaseNo -->
  <select id="findAllClaimCaseByCaseNo" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			 SELECT  A.CASE_NO,
               A.ORGAN_CODE,
               A.GREEN_FLAG,
               A.SIGNER_ID,
               A.SIGN_TIME,
               A.CASE_ID,
               A.INSURED_ID,
               (SELECT C.UN_CUSTOMER_CODE FROM APP___CLM__DBUSER.T_CUSTOMER C
                 WHERE A.INSURED_ID = C.CUSTOMER_ID) UN_CUSTOMER_CODE,
               (SELECT C.CUSTOMER_NAME  FROM APP___CLM__DBUSER.T_CUSTOMER C
                 WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_NAME,
               (SELECT C.CUSTOMER_CERTI_CODE FROM APP___CLM__DBUSER.T_CUSTOMER C
                 WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_CERTI_CODE,
               A.RPTR_TIME,
               A.RPTR_ID,
               A.ACCEPTOR_ID,
               A.ACCEPT_TIME,
               A.CHANNEL_CODE,
               SC.CLAIM_TYPE,
               (SELECT B.REAL_NAME FROM APP___CLM__DBUSER.T_UDMP_USER B
                 WHERE A.RPTR_ID = B.USER_ID) RPTR_NAME,
               (select OC.IMAGE_ISSUE_STATUS_CODE from APP___CLM__DBUSER.T_OUTSOURCE_CASE OC
                 WHERE A.ACQUIST_WAY=OC.ACQUIST_WAY AND A.CASE_ID = OC.CASE_ID) IMAGE_ISSUE_STATUS_CODE,
               OSI.OUTSOURCE_NAME ,
               A.MEDICAL_CONNECTION_FLAG,
               (SELECT CA.DIRECT_APPLY_FLAG
		          FROM DEV_CLM.T_CLAIM_DIRECT_CONN_APPLY CA
		         WHERE CA.IS_NEW = '1'
		         AND CA.CASE_ID = A.CASE_ID
		           AND ROWNUM = '1') DIRECT_APPLY_FLAG
        FROM
             APP___CLM__DBUSER.T_CLAIM_CASE A left join
             APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC on
             A.CASE_ID = SC.CASE_ID left join
             APP___CLM__DBUSER.T_OUTSOURCE_CASE OC on
             A.ACQUIST_WAY=OC.ACQUIST_WAY AND A.CASE_ID = OC.CASE_ID left join
             APP___CLM__DBUSER.T_OUTSOURCE_IFNO OSI on
             OSI.OUTSOURCE_ID =OC.OUTSOURCE_ID
		]]>
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
			<![CDATA[
				WHERE A.CASE_NO = #{case_no}
			]]>
		</if>
		<if test=" pool_flag  != null  and  pool_flag  != ''  and  pool_flag  != 'null' ">
			<![CDATA[
				AND A.CASE_STATUS != '21'
			]]>
		</if>
	</select>
  	<!-- caoyy_wb 查询是否存在重复案件 详设要求，该方法来至ReportInfoInputDAOImpl.queryDoubleCase-->
	<select id="queryDoubleCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select cc.case_status from APP___CLM__DBUSER.T_CLAIM_CASE cc, 
										    APP___CLM__DBUSER.T_CLAIM_SUB_CASE sc 
							where cc.case_id = sc.case_id and cc.insured_id = ${insured_id}
											   and sc.claim_type = ${claim_type}
											   and sc.claim_date = to_date('${claim_date}','yyyy-mm-dd')
											   and sc.acc_reason = ${acc_reason}
												]]>
		<if test="case_no !=null and case_no !=''"> 
        	<![CDATA[    AND  case_no != #{case_no} ]]>
		</if>
	</select>
	<!-- 修改慰问标识 -->
	<update id="updateComfortFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    COMFORT_FLAG = 1 ,
		   
		</trim>
		</set>
		<![CDATA[ WHERE case_id = #{case_id} ]]>
	</update>
		<!-- 修改直赔案件时间 -->
	<update id="updateClaimCaseChangeStatusTimeByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CHANGE_STATUS_TIME = #{change_status_time, jdbcType=TIMESTAMP}  ,
		   <if test=" case_status != null and case_status != ''  ">
			 CASE_STATUS = #{case_status, jdbcType=VARCHAR} ,
			</if>
		</trim>
		</set>
		<![CDATA[ WHERE case_id = #{case_id} ]]>
	</update>
	<select id="queryCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.CASE_ID,a.COMFORT_FLAG from APP___CLM__DBUSER.T_CLAIM_CASE a where a.CASE_NO=#{case_no} and a.INSURED_ID=#{insured_id}]]>
	</select>
	
	<!-- 通过赔案号查询流程实时支付开关状态 -->
	<select id="queryClaimRealtimePay" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_REALTIME_PAY CRP WHERE CRP.PAYMENT_SWITCH = '1' AND CRP.IS_VALID = 1
             		AND CRP.FLOW_TYPE IN (SELECT (CASE WHEN (A.EASY_AUDITOR_ID IS NULL AND A.AUDITOR_ID =950010953) THEN '1'
                 		WHEN (A.EASY_AUDITOR_ID IS NOT NULL AND A.AUDITOR_ID != 950010953 AND A.APPROVER_ID =950010953) THEN '2'
                 		WHEN (A.EASY_AUDITOR_ID IS NULL AND A.AUDITOR_ID !=950010953 AND A.APPROVER_ID =950010953) THEN '3'
                 		WHEN (A.AUDITOR_ID !=950010953 AND A.APPROVER_ID !=950010953) THEN '4'
                 		ELSE '' END) FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_STATUS='80'
		]]>
		<include refid="queryClaimCaseByCaseIdCondition"/>
		<![CDATA[ ) ]]>
	</select>
	
	<select id="findIsHavePolicyDecision" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT count(*)
	         FROM (SELECT a.policy_code,
	                      c.policy_decision,
	                      row_number() over(partition by a.policy_code, a.case_no ORDER BY a.uw_times desc) rn
	                 FROM dev_clm.t_claim_uw a
	                inner join dev_clm.t_claim_case b
	                   on a.case_no = b.case_no
	                inner join dev_clm.t_uw_policy c
	                   on a.uw_policy_id = c.uw_policy_id
	                where b.case_status = '80'
	                  and A.UW_CONCLUSION = 1
	                  and a.claim_uw_type = 2
	                  and a.policy_code = #{policy_code}) t
	        where t.policy_decision = '60'
	          and t.rn = 1
		  ]]>
	</select>
	<select id="findIsHaveBusiProdDecision" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(*)
         			FROM (SELECT a.policy_code,
                      a.case_no,
                      c.decision_code,
                      row_number() over(partition by a.policy_code, a.case_no ORDER BY a.uw_times desc) rn
                 FROM DEV_CLM.T_CLAIM_UW A
                inner join dev_clm.t_uw_policy b
                   on a.uw_policy_id = b.uw_policy_id
                  And A.POLICY_CODE=B.POLICY_CODE
                inner join dev_clm.t_uw_busi_prod c
                   on b.policy_code = c.policy_code
                  and c.uw_id = b.uw_id
                inner join dev_clm.t_claim_case d
                   on a.case_no = d.case_no
                where A.UW_CONCLUSION = 1
                  AND A.CLAIM_UW_TYPE = 2
                  and d.case_status = '80' ]]>
				<if test=" busi_prod_code != null and busi_prod_code != ''  ">
					<![CDATA[ AND C.BUSI_PROD_CODE =#{busi_prod_code} ]]>
				</if>
				<if test=" busi_item_id != null  ">
					<![CDATA[ AND C.BUSI_ITEM_ID =#{busi_item_id} ]]>
				</if>
				<if test=" policy_code != null and policy_code != ''  ">
					<![CDATA[ AND C.POLICY_CODE =#{policy_code} ]]>
				</if>
				<![CDATA[) t
			        where t.decision_code = '60'
			          and t.rn = 1
				 ]]>
	</select>
	<select id="findIsHaveCase" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(*)
		  FROM DEV_CLM.T_CLAIM_LIAB A, DEV_CLM.T_CLAIM_CASE B
		 WHERE A.CASE_ID = B.CASE_ID
		   AND A.BUSI_PROD_CODE IN ('00563100', '00563000', '00958100', '00958000', '00846000', '00846200', '00881000', '00984000', '00822000')
		   AND B.CASE_STATUS NOT IN ('90', '99')
		   AND (A.ACTUAL_PAY>0 OR B.AUDIT_DECISION=3)
		  ]]>
		<if test=" policy_code != null and policy_code != ''  ">
			<![CDATA[ AND A.POLICY_CODE =#{policy_code} ]]>
		</if>
		
		<if test=" busi_item_id != null  ">
			<![CDATA[ AND A.BUSI_ITEM_ID =#{busi_item_id} ]]>
		</if>
	</select>
	<!-- add by zhaoyq_wb start -->
	<select id="findClaimCaseByMedicalNum" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID,A.CASE_STATUS,A.CASE_NO,A.ORGAN_CODE FROM DEV_CLM.T_CLAIM_CASE A WHERE A.MEDICAL_NUM=#{medical_num}]]>
	</select> 
	<select id="findClaimCaseByLY" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID,
	   A.CASE_NO,
       A.CASE_STATUS,
       A.CHANNEL_CODE,
       A.ACTUAL_PAY,
       A.CHANGE_STATUS_TIME
  FROM DEV_CLM.T_CLAIM_CASE A, DEV_CLM.T_CUSTOMER B
 WHERE A.INSURED_ID = B.CUSTOMER_ID
   AND A.CASE_NO = #{case_no}
   AND A.CURE_HOSPITAL = #{cure_hospital}
   AND B.CUSTOMER_NAME = #{insured_name}
   AND B.CUSTOMER_CERT_TYPE = #{insured_code_type}
   AND B.CUSTOMER_CERTI_CODE = #{insured_code_code} ]]>
	</select>
	<!-- add by zhaoyq_wb end -->
	<!-- 查询超期核定赔案信息 -->
	<select id="queryOverdueTask" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
	SELECT  F.RN AS rowNumber,
                  F.CASE_NO,
                  F.CASE_APPLY_TYPE,
                  F.CALC_PAY,
                  F.ACTUAL_PAY,
                  F.APPROVE_TIME,
                  F.FINISH_TIME
    FROM (
	SELECT  ROWNUM RN,
                  E.CASE_NO,
                  E.CASE_APPLY_TYPE,
                  E.CALC_PAY,
                  E.ACTUAL_PAY,
                  E.APPROVE_TIME,
                  E.FINISH_TIME
    FROM (SELECT DISTINCT A.CASE_NO, A.CASE_APPLY_TYPE,A.CALC_PAY, A.ACTUAL_PAY,A.OVER_DUE_MONEY, A.APPROVE_TIME,
    		(select max(B.FINISH_TIME) as FINISH_TIME from APP___CLM__DBUSER.T_PREM_ARAP B
                  where B.ARAP_FLAG = 2 AND B.FEE_TYPE != 'P005110000' and b.BUSINESS_CODE = A.CASE_NO) FINISH_TIME
    FROM APP___CLM__DBUSER.T_CLAIM_CASE  A,
         APP___CLM__DBUSER.T_CLAIM_BUSI_PROD B,
        APP___CLM__DBUSER.T_CLAIM_PAY CP,
         APP___CLM__DBUSER.T_PREM_ARAP   D
   WHERE 1 = 1
     AND A.CASE_ID = B.CASE_ID
     AND A.CASE_ID = CP.CASE_ID
     AND A.CASE_NO=D.BUSINESS_CODE
     AND B.BUSI_PROD_CODE=D.BUSI_PROD_CODE
     AND B.BUSI_PROD_CODE = CP.BUSI_PROD_CODE
     AND B.POLICY_CODE=D.POLICY_CODE
     AND B.POLICY_CODE = CP.POLICY_CODE
     AND CP.UNIT_NUMBER = D.UNIT_NUMBER
     AND D.FINISH_TIME IS NOT NULL
     AND A.CASE_APPLY_TYPE = 1 
     AND D.ARAP_FLAG = 2
     AND D.FEE_TYPE != 'P005110000'
     AND not exists (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_OVERCOMP_PAY OVERPAY  WHERE OVERPAY.OVER_PAY_FLAG = 1 and OVERPAY.ARAP_LIST_ID=D.list_id)
     AND floor(D.FINISH_TIME - A.APPROVE_TIME)>9 ]]>
             <if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.APPROVE_TIME>=#{start_date}]]></if>
			 <if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.APPROVE_TIME<=#{end_date} ]]></if>
             <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like '${organ_code}%' ]]></if>
          <![CDATA[   ) E 
          WHERE ROWNUM <= #{LESS_NUM}
          ORDER BY E.APPROVE_TIME DESC
          )F
          WHERE F.RN > #{GREATER_NUM} 
           ]]>
	</select>
	<!-- 查询超期支付赔案数量 -->
	<select id="queryOverdueTaskTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >
			<![CDATA[SELECT count(1) FROM (
    SELECT DISTINCT A.CASE_NO, A.CASE_APPLY_TYPE,A.CALC_PAY, A.ACTUAL_PAY,A.OVER_DUE_MONEY, A.APPROVE_TIME,
    	(select max(B.FINISH_TIME) as FINISH_TIME from APP___CLM__DBUSER.T_PREM_ARAP B
                  where B.ARAP_FLAG = 2 AND B.FEE_TYPE != 'P005110000' and b.BUSINESS_CODE = A.CASE_NO) FINISH_TIME
    FROM APP___CLM__DBUSER.T_CLAIM_CASE  A,
         APP___CLM__DBUSER.T_CLAIM_BUSI_PROD B,
        APP___CLM__DBUSER.T_CLAIM_PAY CP,
         APP___CLM__DBUSER.T_PREM_ARAP   D
   WHERE 1 = 1
     AND A.CASE_ID = B.CASE_ID
     AND A.CASE_ID = CP.CASE_ID
     AND A.CASE_NO=D.BUSINESS_CODE
     AND B.BUSI_PROD_CODE=D.BUSI_PROD_CODE
     AND B.BUSI_PROD_CODE = CP.BUSI_PROD_CODE
     AND B.POLICY_CODE=D.POLICY_CODE
     AND B.POLICY_CODE = CP.POLICY_CODE
     AND CP.UNIT_NUMBER = D.UNIT_NUMBER
     AND D.FINISH_TIME IS NOT NULL
     AND A.CASE_APPLY_TYPE = 1 
     AND D.ARAP_FLAG = 2
     AND D.FEE_TYPE != 'P005110000'
     AND not exists (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_OVERCOMP_PAY OVERPAY  WHERE OVERPAY.OVER_PAY_FLAG = 1 and OVERPAY.ARAP_LIST_ID=D.list_id)
     AND floor(D.FINISH_TIME - A.APPROVE_TIME)>9]]>
             <if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.APPROVE_TIME>=#{start_date}]]></if>
			 <if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.APPROVE_TIME<=#{end_date} ]]></if>
             <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like '${organ_code}%' ]]></if>
          <![CDATA[   ) E  ORDER BY E.APPROVE_TIME DESC
          ]]>
	</select>
	<!-- 查询超期支付明细 -->
	<select id="queryOverdueDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT D.LIST_ID,A.CASE_ID,
				A.CASE_NO,
                COP.OVER_PAY_REASON,
                COP.OVER_PAY_DAYS,
                COP.OVER_PAY_MONEY,
                COP.OVER_PAY_FLAG,
                COP.OVER_PAY_PERSON,
                COP.OVER_PAY_DATE,
                A.APPROVE_TIME,
                D.PAY_MODE,
                (SELECT M.NAME
                   FROM APP___CLM__DBUSER.T_PAY_MODE M
                  WHERE M.CODE = D.PAY_MODE) PAY_TYPE,
                D.PAYEE_NAME,
                D.FINISH_TIME,
                D.CUSTOMER_ID,
                D.FEE_AMOUNT AS ACTUAL_PAY
	  FROM APP___CLM__DBUSER.T_CLAIM_CASE  A,
	       APP___CLM__DBUSER.T_CLAIM_BUSI_PROD B,
	       APP___CLM__DBUSER.T_CLAIM_PAY CP,
	       APP___CLM__DBUSER.T_PREM_ARAP   D
	       left join APP___CLM__DBUSER.T_CLAIM_OVERCOMP_PAY COP on COP.OVER_PAY_FLAG = 0 AND COP.ARAP_LIST_ID = D.LIST_ID
	 WHERE 1 = 1
	   AND A.CASE_ID = B.CASE_ID
	   AND A.CASE_ID = CP.CASE_ID
	   AND A.CASE_NO=D.BUSINESS_CODE
	   AND B.BUSI_PROD_CODE=D.BUSI_PROD_CODE
	   AND CP.UNIT_NUMBER = D.UNIT_NUMBER
	   AND B.POLICY_CODE=D.POLICY_CODE
	   AND D.FINISH_TIME IS NOT NULL
	   AND A.CASE_APPLY_TYPE = 1 
	   AND D.ARAP_FLAG = 2
	   AND D.FEE_TYPE != 'P005110000'
	   AND ceil(D.FINISH_TIME - A.APPROVE_TIME)>9
       AND D.LIST_ID NOT IN (SELECT OVERPAY.ARAP_LIST_ID FROM APP___CLM__DBUSER.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE OVERPAY.OVER_PAY_FLAG = 1 ]]>
       <if test=" case_id != null and case_id != ''  "><![CDATA[ AND OVERPAY.CASE_ID = #{case_id} ]]></if>
       <![CDATA[) ]]>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no, jdbcType=VARCHAR} ]]></if>
		<if test=" case_id  != null and case_id!= ''"> AND A.CASE_ID = #{case_id} </if>
	</select>
	
	<!-- 查询核赔岗前用户权限配置清单 -->   
	<select id="queryPermissionList"  resultType="java.util.Map" parameterType="java.util.Map" >     
	        <![CDATA[ SELECT 
		       A.CASE_NO,
		       A.APPROVE_TIME,
		       A.PRE_AUDIT_ID,
		       A.AUDITOR_ID,
		       A.RE_AUDIT_DECISION,
		       A.RE_AUDIT_OPINION
		  FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1=1 and A.PRE_AUDIT_ID is not null ]]>
		  <if test="start_Time != null and  start_Time  != '' "> <![CDATA[ AND TRUNC(A.APPROVE_TIME)>=TO_DATE(#{start_Time},'yyyy-MM-dd') ]]> </if>
          <if test="end_Time != null and  end_Time  != '' "> <![CDATA[ AND  TRUNC(A.APPROVE_TIME)<= TO_DATE(#{end_Time},'yyyy-MM-dd') ]]> </if>
            <![CDATA[ ORDER BY A.INSERT_TIME ASC ]]>
          </select>  
          
          <select id="queryPermissionListTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >     
	        <![CDATA[select count(1) from ( SELECT 
		       A.CASE_NO,
		       A.APPROVE_TIME,
		       A.PRE_AUDIT_ID,
		       A.AUDITOR_ID,
		       A.RE_AUDIT_DECISION,
		       A.RE_AUDIT_OPINION
		  FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1=1 and A.PRE_AUDIT_ID is not null  ]]>
          <if test="start_Time != null and  start_Time  != '' "> <![CDATA[ AND TRUNC(A.APPROVE_TIME)>=TO_DATE(#{start_Time},'yyyy-MM-dd') ]]> </if>
          <if test="end_Time != null and  end_Time  != '' "> <![CDATA[ AND  TRUNC(A.APPROVE_TIME)<= TO_DATE(#{end_Time},'yyyy-MM-dd') ]]> </if>
            <![CDATA[ ORDER BY A.INSERT_TIME ASC ) B ]]>
          </select> 
          <!-- 修改行业有效性信息 -->
	<update id="updateTradeShareInfoByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			SHARE_CONDITION_VALID = #{share_condition_valid, jdbcType=NUMERIC} ,
			SHARE_CONDITION_DECISION = #{share_condition_decision, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>
      <!-- 查询行业共享信息清单 -->
     <select id="queryTradeShareInfoList"  resultType="java.util.Map" parameterType="java.util.Map" >     
	        <![CDATA[ select A.CASE_NO,
			(select REAL_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.INSURED_ID) INSURED_NAME,
			B.CLAIM_TYPE,A.MEDICAL_TOTAL_MONY,C.PRODUCT_TYPE,C.RISK_RESULT,
			(select Count(*)from T_SURVEY_APPLY  where case_id=A.CASE_ID) surveyMark,
			(select POSITIVE_FLAG from T_SURVEY_CONCLUSION h,T_SURVEY_APPLY k where h.apply_id=k.apply_id and k.CASE_ID=A.case_ID ) POSITIVE_FLAG,
			A.APPROVE_TIME  ,A.AUDIT_DECISION,A.ACTUAL_PAY,A.REJECT_PAY,
			(select REAL_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.AUDITOR_ID) AUDITOR_NAME,
			(select USER_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.AUDITOR_ID) AUDITOR_CODE,
			(select REAL_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.APPROVER_ID) APPROVER_NAME,
			(select USER_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.APPROVER_ID) APPROVER_CODE,
			A.AUDIT_REJECT_REASON,
			(select ORGAN_NAME from APP___CLM__DBUSER.t_udmp_org_rel where ORGAN_CODE=A.ORGAN_CODE and ORGAN_GRADE='02')ORGAN_NAME,
			(select ORGAN_NAME from APP___CLM__DBUSER.t_udmp_org_rel where ORGAN_CODE=A.ORGAN_CODE and ORGAN_GRADE='03')ORGAN_NAME,
			(select ORGAN_NAME from APP___CLM__DBUSER.t_udmp_org_rel where ORGAN_CODE=A.ORGAN_CODE and ORGAN_GRADE='04')ORGAN_NAME
			from APP___CLM__DBUSER.T_CLAIM_CASE A ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE B,APP___CLM__DBUSER.T_CIITC_BACK_RESULT C,APP___CLM__DBUSER.T_CLAIM_VIEW_RECORD D where A.CASE_ID=B.CASE_ID and C.CASE_ID=A.CASE_ID and A.CASE_ID=D.CASE_ID  
 ]]>
		  <if test=" product_Type != null and product_Type != ''  "><![CDATA[ AND C.product_Type = #{product_Type} ]]></if>
		 <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.organ_code = #{organ_code} ]]></if>
          <if test="approve_time != null and  approve_time  != '' "> <![CDATA[ AND  A.APPROVE_TIME= #{approve_time} ]]> </if>
            <![CDATA[ ORDER BY A.INSERT_TIME ASC ]]>
          </select>  
     
      <select id="queryTradeShareInfoListTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >     
	        <![CDATA[select Count(1) from 
	  (select A.CASE_NO,
      (select REAL_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.INSURED_ID) INSURED_NAME,
      B.CLAIM_TYPE,A.MEDICAL_TOTAL_MONY,C.PRODUCT_TYPE,C.RISK_RESULT,
      (select Count(*)from T_SURVEY_APPLY  where case_id=A.CASE_ID) surveyMark,
      (select POSITIVE_FLAG from T_SURVEY_CONCLUSION h,T_SURVEY_APPLY k where h.apply_id=k.apply_id and k.CASE_ID=A.case_ID ) POSITIVE_FLAG,
      A.APPROVE_TIME ,A.AUDIT_DECISION,A.ACTUAL_PAY,A.REJECT_PAY,
      (select REAL_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.AUDITOR_ID) AUDITOR_NAME,
      (select USER_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.AUDITOR_ID) AUDITOR_CODE,
      (select REAL_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.APPROVER_ID) APPROVER_NAME,
      (select USER_NAME from APP___CLM__DBUSER.T_UDMP_USER where USER_ID=A.APPROVER_ID) APPROVER_CODE,
      A.AUDIT_REJECT_REASON,
      (select ORGAN_NAME from APP___CLM__DBUSER.t_udmp_org_rel where ORGAN_CODE=A.ORGAN_CODE and ORGAN_GRADE='02')ORGAN_NAME,
      (select ORGAN_NAME from APP___CLM__DBUSER.t_udmp_org_rel where ORGAN_CODE=A.ORGAN_CODE and ORGAN_GRADE='03')ORGAN_NAME,
      (select ORGAN_NAME from APP___CLM__DBUSER.t_udmp_org_rel where ORGAN_CODE=A.ORGAN_CODE and ORGAN_GRADE='04')ORGAN_NAME
      from APP___CLM__DBUSER.T_CLAIM_CASE A ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE B,APP___CLM__DBUSER.T_CIITC_BACK_RESULT C,APP___CLM__DBUSER.T_CLAIM_VIEW_RECORD D where A.CASE_ID=B.CASE_ID and C.CASE_ID=A.CASE_ID and A.CASE_ID=D.CASE_ID    ]]>
      <if test=" product_Type != null and product_Type != ''  "><![CDATA[ AND C.product_Type = #{product_Type} ]]></if>
      <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.organ_code = #{organ_code} ]]></if>
      <if test="approve_time != null and  approve_time  != '' "> <![CDATA[ AND  A.APPROVE_TIME= #{approve_time} ]]> </if>
            <![CDATA[ ORDER BY A.INSERT_TIME ASC ) B ]]>
          </select>      
          
      <!-- 根据出险人为入参客户号查询已结案的赔案信息 -->
      <select id="findCaseByInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[
		SELECT A.CASE_ID,A.CASE_NO,A.SERIOUS_DISEASE
		FROM DEV_CLM.T_CLAIM_CASE A
		WHERE A.CASE_STATUS='80' 
		AND A.INSURED_ID = #{insured_id}
      ]]>
      </select>
          
      <!-- 根据赔案id查询已结案的赔案保项层的明细信息 -->
      <select id="findAllEndClaimCaseLiab" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[
		SELECT A.POLICY_CODE,A.CASE_ID,A.POLICY_ID,A.BUSI_PROD_CODE,A.PRODUCT_ID,A.LIAB_ID,A.CLAIM_TYPE,
	    A.ACTUAL_PAY,A.LIAB_CONCLUSION,A.REJECT_CODE,B.CLAIM_DATE
	    FROM DEV_CLM.T_CLAIM_LIAB A,DEV_CLM.T_CLAIM_SUB_CASE B
	    WHERE A.SUB_CASE_ID = B.SUB_CASE_ID 
	    AND A.CASE_ID = #{case_id}
      ]]>
      </select>
      
      <!-- 查询所有签收中的直连案件 -->
      <select id="findAllDirectConnClaimCaseId" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[
		SELECT * FROM (
			SELECT ROWNUM RN, A.CASE_ID
			FROM APP___CLM__DBUSER.T_CLAIM_CASE A
			INNER JOIN APP___CLM__DBUSER.T_CLAIM_DIRECT_CONN_AUTH B
			    ON A.CASE_ID = B.CASE_ID AND B.DIRECT_CONN_BUS != '4'
			WHERE A.CASE_STATUS = '21' AND A.MEDICAL_CONNECTION_FLAG = 1 
			AND (NOT EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_DIRECT_CONN_APPLY B WHERE B.CASE_ID = A.CASE_ID) 
				OR EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_DIRECT_CONN_APPLY B WHERE B.CASE_ID = A.CASE_ID AND B.DIRECT_APPLY_FLAG = '1' 
					AND B.APPLY_COUNT IS NOT NULL AND B.APPLY_COUNT <3 AND B.IS_NEW = '1' )
			)
		) M
		WHERE MOD(M.RN, #{modNum}) = #{start}
      ]]>
      </select>
      
      <!-- 查询所有签收中且医疗直连标识为是直连服务商为中银宝信苏州的案件 -->
      <select id="findNotDirectConnApply" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[
		SELECT * FROM (
			SELECT ROWNUM RN, A.CASE_ID
			  FROM APP___CLM__DBUSER.T_CLAIM_CASE A
			 INNER JOIN APP___CLM__DBUSER.T_CLAIM_DIRECT_CONN_AUTH B
			    ON A.CASE_ID = B.CASE_ID AND B.DIRECT_CONN_BUS = '4'
			 WHERE A.CASE_STATUS = '21'
			   AND A.MEDICAL_CONNECTION_FLAG = 1
			   AND (NOT EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_DIRECT_CONN_APPLY CD WHERE CD.CASE_ID = A.CASE_ID) 
					 OR EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_DIRECT_CONN_APPLY CA WHERE CA.CASE_ID = A.CASE_ID AND CA.DIRECT_APPLY_FLAG = '1' 
						   AND CA.APPLY_COUNT IS NOT NULL AND CA.APPLY_COUNT <3 AND CA.IS_NEW = '1' ) )
		) M
		WHERE MOD(M.RN, #{modNum}) = #{start}
      ]]>
      </select>
      
      <!-- 根据赔案号更新是否在商业保司理赔 -->
      <update id="updateClaimCaseInsurancePaidFlagByCaseId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			ACCIDENT_DETAIL = #{accident_detail, jdbcType=VARCHAR} ,
			INSURANCE_PAID_FLAG = #{insurance_paid_flag, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	  </update>      
      
      <!-- 查询赔案信息（赔案号，签收机构，理赔给付金额,理赔类型）,受托人信息 -->
      <select id="findClaimCaseForRealName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,A.ACCIDENT_ID,A.CASE_NO,A.INSURED_ID,A.RPTR_RELATION,A.RPTR_NAME,A.RPTR_MP,A.RPTR_ZIP,
             A.RPTR_ADDR,A.RPTR_EMAIL,A.REPORT_MODE,A.ORGAN_CODE,A.RPTR_TIME,A.CASE_APPLY_TYPE,A.APPLY_DATE,A.ACCEPTOR_ID,
             A.ACCEPT_TIME,A.TRUSTEE_TYPE,A.TRUSTEE_CODE,A.TRUSTEE_NAME,A.TRUSTEE_MP,A.TRUSTEE_TEL,A.TRUSTEE_CERTI_TYPE,
             A.TRUSTEE_CERTI_CODE,A.DOOR_SIGN_TIME,A.SIGN_TIME,A.RPTR_ID,A.SIGNER_ID,A.SERIOUS_DISEASE,ACCIDENT_DETAIL,
             A.CURE_HOSPITAL,A.CURE_STATUS,A.DOCTOR_NAME,A.MED_DEPT,A.CASE_SUB_STATUS,A.CASE_STATUS,A.GREEN_FLAG,A.IS_BPO,
             A.CASE_FLAG,A.REVIEW_FLAG,A.COMFORT_FLAG,A.ADVANCE_ASK_FLAG,A.ADVANCE_FLAG,A.IS_DEDUCT_FLAG,A.REPEAL_REASON,
             A.REPEAL_DESC,A.REGISTER_ID,A.REGISTE_TIME,A.REGISTE_CONF_TIME,A.ACCEPT_DECISION,A.REJECT_REASON,A.CALC_PAY,
             ADVANCE_PAY,A.BALANCE_PAY,A.ACTUAL_PAY,A.REJECT_PAY,A.AUDIT_TIME,A.AUDITOR_ID,A.AUDIT_DECISION,A.AUDIT_REMARK,
             A.AUDIT_REJECT_REASON,A.OTHER_REASON,A.APPROVER_ID,A.APPROVE_TIME,A.APPROVE_DECISION,A.APPROVE_REJECT_REASON,
             A.APPROVE_REMARK,A.END_CASE_TIME,A.OVER_COMP_FLAG,A.RELATED_NO,A.CLAIM_SOURCE,A.IS_COMMON,A.INSERT_BY,
             A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP,A.AUDIT_PERMISSION_NAME,
             A.APPROVE_PERMISSION_NAME,A.SURVEY_RESULT_INFO,A.CHANNEL_CODE,A.IS_AUTO_HUNGUP,A.SPECIAL_REMARK_CODE,
             A.LOSS_REASON_CODE,A.LOSS_LEVEL_CODE,A.COMFORT_STATUS,A.IS_MIGRATION,A.SEND_BENE_DOC_FLAG,A.SIGN_ORGAN,
             A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME, A.AUDIT_START_TIME,
             A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.REMARK,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.SPECIFIC_CAUSE,A.MEDICAL_AMOUNT_FLAG,A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.REJECT_REMARKS,
             A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.REALTIME_PAY, A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.MATERIAL_FREE_FLAG, A.MEDICAL_CONNECTION_FLAG,A.REPEAT_NUMBER_TYPE,A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_FLAG,A.SALESMAN_SELF_INSURANCE,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.SIGNATURE_TRACE_FLAG,A.DIAGNOSIS_TIME,
             A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,A.IS_LAWSUITS ,A.IS_OVER_COMP,A.OVER_REASON,A.OVER_DAYS,
             A.OVER_DUE_MONEY,A.PRE_AUDIT_ID,A.RE_AUDIT_DECISION,A.RE_AUDIT_OPINION,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,
             A.SHARE_CONDITION_VALID,A.SHARE_CONDITION_DECISION,A.INSURANCE_PAID_FLAG,A.IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.ELEC_CHECK_FLAG,
             A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE 1 = 1  ]]>
		<![CDATA[ AND A.case_id = #{case_id} ]]>
	</select>
	
	<!-- 查询881既往赔案信息  -->
	<select   id="findAllCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.CASE_ID, A.DIAGNOSIS_TIME
						  FROM APP___CLM__DBUSER.T_CLAIM_CASE A, APP___CLM__DBUSER.T_CLAIM_LIAB B
						  WHERE A.CASE_ID = B.CASE_ID
 						  AND B.BUSI_PROD_CODE = '00881000'
 						  AND A.CASE_STATUS != 90]]>
					<if test="insured_id != null and  insured_id  != '' "> <![CDATA[ AND  A.INSURED_ID= #{insured_id} ]]> </if>
					<if test="case_id != null and  case_id  != '' "> <![CDATA[ AND  A.CASE_ID != #{case_id} ]]> </if>
					<![CDATA[order by  A.DIAGNOSIS_TIME]]>
                 	
                 	
	</select>
	
	<!-- 查询同步状态信息的赔案 -->
	<select id="findCaseForStatusSynMsg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT *
              FROM APP___CLM__DBUSER.T_CLAIM_CASE A
             WHERE A.CHANNEL_CODE IN ('07', '09')
               AND EXISTS (SELECT 1
                      FROM (SELECT '30' n
                              from dual
                            union all
                            select '31' n
                              from dual
                            union all
                            select '32' n
                              from dual
                            union all
                            select '60' n
                              from dual
                            union all
                            select '61' n
                              from dual
                            union all
                            select '70' n
                              from dual
                            union all
                            select '71' n
                              from dual
                            union all
                            select '80' n
                              from dual
                            union all
                            select '90' n
                              from dual) T
						         WHERE A.CASE_STATUS = T.N)]]>
				<if test="extract_time != null and  extract_time  != '' "> <![CDATA[ AND  A.UPDATE_TIME > #{extract_time} ]]> </if>
				<if test="current_time != null and  current_time  != '' "> <![CDATA[ AND  A.UPDATE_TIME <= #{current_time} ]]> </if>
	</select>
	
	
	
  <!-- 113626-根据出险人为入参客户号查询已结案的赔案信息 -->
     <select id="findCaseByInsuredIdAndContainsTransitCase" resultType="java.util.Map" parameterType="java.util.Map">
     <![CDATA[
	SELECT A.CASE_ID,A.CASE_NO,A.SERIOUS_DISEASE
	FROM DEV_CLM.T_CLAIM_CASE A
	WHERE 1=1
	AND A.INSURED_ID = #{insured_id}
    AND  A.CASE_STATUS !='90'
  	AND  A.CASE_STATUS !='99'
     ]]>
     </select>
     <select id="findSpecialClaimCaseByCaseNOList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,
					      A.CASE_NO,
					      A.ORGAN_CODE,
					      A.AUDITOR_ID,
					      A.AUDIT_PERMISSION_NAME,
					      A.APPROVE_PERMISSION_NAME,
					      A.CASE_FLAG,
					      case when exists(SELECT 1 FROM dev_clm.t_claim_sub_case csc where csc.claim_type='08' and csc.case_id=a.case_id) then 1 else 0 end medical_case_Flag
					 FROM APP___CLM__DBUSER.T_CLAIM_CASE A
					WHERE ROWNUM <= 1000  ]]>
		<if test=" case_no_list != null"><![CDATA[ AND A.CASE_NO IN]]>
			<foreach collection="case_no_list" item="case_no" index="index"
				open="(" close=")" separator=",">#{case_no}</foreach> 
		</if>
		<![CDATA[ ORDER BY A.CASE_ID ]]> 
	</select>
     <!-- 赔案号查询 -->
     <select id="findAllClmNo" resultType="java.util.Map" parameterType="java.util.Map">
     <![CDATA[
	SELECT A.CASE_NO
  	FROM APP___CLM__DBUSER.T_CLAIM_CASE A LEFT JOIN APP___CLM__DBUSER.T_CLAIM_APPLICANT B  ON A.CASE_ID = B.CASE_ID 
	WHERE 1=1
     ]]>
     	<if test=" apply_date_start != null and apply_date_start != ''  "><![CDATA[ AND A.APPLY_DATE >= #{apply_date_start} ]]></if>
		<if test=" apply_date_end != null and apply_date_end != ''  "><![CDATA[ AND A.APPLY_DATE <= #{apply_date_end} ]]></if>
		<if test=" rgtant_name != null and rgtant_name != ''  "><![CDATA[ AND B.CLMT_NAME = #{rgtant_name} ]]></if>
		<if test=" rgtant_phone != null and rgtant_phone != ''  "><![CDATA[ AND B.CLMT_MP = #{rgtant_phone} ]]></if>
     </select>
     
<!-- 查询审核不通过或客户撤案流转至待立案的赔案 -->
	<select id="findRefuseRegister" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (
		SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME,A.AUDIT_START_TIME, A.APPROVE_TIME, A.CHANNEL_CODE,
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.COMFORT_STATUS,
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, A.REALTIME_PAY,
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.UPDATE_BY, A.IS_AUTO_HUNGUP, A.IS_MIGRATION, 
			A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,
			A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
			A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.UPDATE_TIMESTAMP,A.INSERT_TIME,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
            A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,
            A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.IS_OVER_COMP,A.OVER_REASON,
            A.OVER_DAYS,A.OVER_DUE_MONEY,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.ELEC_CHECK_FLAG,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,
            A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON
		FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_STATUS = '30' AND A.AUDIT_DECISION IN (5,6))M WHERE mod(M.CASE_ID, #{modNum}) = #{start} ]]>
	</select>
	<!-- 案件信息-报案人信息 等 -->
    <select id="queryCaseInfoRptrTime" resultType="java.util.Map" parameterType="java.util.Map">
	    SELECT A.RELATED_NO,A.INSERT_TIME
	        FROM APP___CLM__DBUSER.T_CLAIM_CASE A
	       START WITH
	       A.CASE_ID = #{case_id} 
	      CONNECT BY PRIOR A.RELATED_NO = A.CASE_NO
	       ORDER BY A.INSERT_TIME ASC
     </select>
     <!-- 查询所有操作 -->
	<select id="findAllCaseDataByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	SELECT a.case_no,
					       a.case_status,
					       case
					         when a.end_case_time is not null then a.end_case_time
					         when a.approve_time is not null then a.approve_time
					         when a.audit_time is not null then a.audit_time
					         when a.registe_conf_time is not null then a.registe_conf_time
					         when a.sign_time is not null then a.sign_time
					         when a.door_sign_time is not null then a.door_sign_time
					         when a.accept_time is not null then a.accept_time
					         when a.rptr_time is not null then a.rptr_time
					         else null end status_date,
					       a.end_case_time,
					       (SELECT cu.customer_name FROM dev_clm.t_customer cu where cu.customer_id=a.insured_id) insured_name,
					       a.audit_decision,
					       (a.actual_pay+nvl(a.advance_pay,0)) actual_pay,
					       case when (SELECT count(1) FROM dev_clm.T_SURVEY_APPLY sa where sa.case_id=a.case_id)>0 then '1' else'0' end survey_flag, 
					       case when (SELECT count(1) FROM dev_clm.t_Claim_Uw cuw where cuw.case_id=a.case_id)>0 then '1' else'0' end uw_flag, 
					       case when (SELECT count(1) FROM dev_clm.t_prem_arap parap where parap.business_code=a.case_no)=0 then null
                           when (SELECT count(1) FROM dev_clm.t_prem_arap parap where parap.fee_amount>0 and parap.fee_status in ('00','03') and parap.business_code=a.case_no)>0 or a.case_status<>'80' then '0' else'1' end fee_stauts,
					       a.realtime_pay
					  FROM dev_clm.t_claim_case a
					 where a.case_status<>'99' and exists (SELECT *
					          FROM dev_clm.t_contract_master b
					         where a.case_id = b.case_id
					           and b.policy_code = #{policy_code})
					 union
					 SELECT a.case_no,
					       a.case_status,
					       case
					         when a.end_case_time is not null then a.end_case_time
					         when a.approve_time is not null then a.approve_time
					         when a.audit_time is not null then a.audit_time
					         when a.registe_conf_time is not null then a.registe_conf_time
					         when a.sign_time is not null then a.sign_time
					         when a.door_sign_time is not null then a.door_sign_time
					         when a.accept_time is not null then a.accept_time
					         when a.rptr_time is not null then a.rptr_time
					         else null end status_date,
					       a.end_case_time,
					       (SELECT cu.customer_name FROM dev_clm.t_customer cu where cu.customer_id=a.insured_id) insured_name,
					       a.audit_decision,
					       (a.actual_pay+nvl(a.advance_pay,0)) actual_pay,
					       case when (SELECT count(1) FROM dev_clm.T_SURVEY_APPLY sa where sa.case_id=a.case_id)>0 then '1' else'0' end survey_flag, 
					       case when (SELECT count(1) FROM dev_clm.t_Claim_Uw cuw where cuw.case_id=a.case_id)>0 then '1' else'0' end uw_flag, 
					       case when (SELECT count(1) FROM dev_clm.t_prem_arap parap where parap.business_code=a.case_no)=0 then null
                           when (SELECT count(1) FROM dev_clm.t_prem_arap parap where parap.fee_amount>0 and parap.fee_status in ('00','03') and parap.business_code=a.case_no)>0 or a.case_status<>'80' then '0' else'1' end fee_stauts,
					       a.realtime_pay
					  FROM dev_clm.t_claim_case a
					 where a.case_status<>'99' and exists (SELECT *
					          FROM dev_clm.t_claim_policy b
					         where a.case_id = b.case_id
					           and b.policy_code = #{policy_code}) ]]>
	</select>
	
	
	 <!-- 查询所有操作 -->
	<select id="findIdsByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	select distinct a.policy_id, b.busi_item_id, c.insured_id
  from dev_clm.t_contract_master    a,
       dev_clm.t_contract_busi_prod b,
       dev_clm.t_claim_case         c
 where a.case_id = #{case_id}
   and b.case_id = a.case_id
   and c.case_id = a.case_id
					  ]]>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="findHisPerkClaimNo" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[    
SELECT count(1)
  FROM dev_clm.t_claim_case a
 where exists (SELECT 1
          FROM dev_clm.t_claim_liab b
         where b.liab_conclusion != '5'
           and b.IS_SUBSIDY = '1'
           and b.busi_item_id = #{busi_item_id}
           and b.policy_id = #{policy_id}
           and a.case_id=b.case_id)
   and a.case_status = '80'
   and a.insured_id = #{insured_id}  ]]>
		<include refid="claimCaseWhereConditionForPage" />
	</select>
		 		<!-- 案件信息-报案人信息 等 -->
    <select id="findallusiProdStopValuecaseid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.CASE_ID,B.liab_id,A.Insured_Id,b.policy_code
              FROM APP___CLM__DBUSER.T_CLAIM_CASE A, APP___CLM__DBUSER.T_CLAIM_LIAB B
              WHERE A.CASE_ID = B.CASE_ID
              AND B.liab_conclusion != '5'
              AND A.CASE_STATUS  not in ('90','99') 
              AND b.liab_id  in('3095','2041')]]>
              	<if test="BUSI_PROD_CODE != null  "> <![CDATA[ AND  B.BUSI_PROD_CODE= #{BUSI_PROD_CODE} ]]> </if>
				<if test="insured_id != null  "> <![CDATA[ AND  A.INSURED_ID= #{insured_id} ]]> </if>
				<if test="case_id != null "> <![CDATA[ AND  A.CASE_ID != #{case_id} ]]> </if>
					<if test="policy_code != null "> <![CDATA[  AND  b.policy_code= #{policy_code} ]]> </if>
         <![CDATA[order by  A.DIAGNOSIS_TIME]]>
     </select>
     

     <!-- 根据赔案id查询案件状态为"已回退"的案件 -->
     <select id="findBackCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
     	<![CDATA[
     			SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
				A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
				A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
				A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
				A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
				A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
				A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
				A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
				A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME,A.AUDIT_START_TIME, A.APPROVE_TIME, A.CHANNEL_CODE,
				A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.COMFORT_STATUS,
				A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, A.REALTIME_PAY,
				A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.UPDATE_BY, A.IS_AUTO_HUNGUP, A.IS_MIGRATION, A.IS_MIGRATION ,A.SIGN_TIME,
				A.RPTR_TIME,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,
				A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
				A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.UPDATE_TIMESTAMP,A.INSERT_TIME,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
	            A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,
	            A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,
	            A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.IS_OVER_COMP,
	            A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON  FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_ID = #{case_id} AND A.CASE_STATUS = '99' AND ROWNUM <=  1000
     		]]>
     		<![CDATA[ ORDER BY A.CASE_ID ]]> 
     </select>
     	


    <!-- 根据caseId查询赔案事故描述 -->
	<select id="findClaimAccidentByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_NO,A.CASE_ID,B.ACC_DESC
				    FROM APP___CLM__DBUSER.T_CLAIM_CASE A
				   INNER JOIN APP___CLM__DBUSER.T_CLAIM_ACCIDENT B
				      ON A.ACCIDENT_ID = B.ACCIDENT_ID
				   WHERE A.CASE_ID = #{case_id} ]]>
	</select>
	
	<!-- 根据insuredId查询已结案的给付责任类型为重大疾病或特种疾病的案件数 -->
	<select id="findClaimCaseCountByInsuredId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(DISTINCT A.CASE_NO)
				    FROM APP___CLM__DBUSER.T_CLAIM_CASE A
				   INNER JOIN APP___CLM__DBUSER.T_CLAIM_LIAB CL ON A.CASE_ID = CL.CASE_ID
				   INNER JOIN APP___CLM__DBUSER.T_LIABILITY LI ON CL.LIAB_ID = LI.LIAB_ID
				   WHERE A.CASE_STATUS = #{case_status}
				     AND A.INSURED_ID = #{insured_id}
				     AND LI.LIAB_CATEGORY IN ('03', '08')
				     AND CL.ADJUST_PAY > 0 ]]>
	</select>
	
	<!-- 根据insuredId查询已结案的涉及理算的责任代码，且责任理算金额大于0 -->
	<select id="findEndLiabCodeByInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	SELECT CL.LIAB_CODE, CL.BUSI_PROD_CODE, CL.CALC_PAY, A.CASE_ID
					  FROM APP___CLM__DBUSER.T_CLAIM_CASE A
					 INNER JOIN APP___CLM__DBUSER.T_CLAIM_LIAB CL ON A.CASE_ID = CL.CASE_ID
					 WHERE A.CASE_STATUS = #{case_status}
					   AND A.INSURED_ID = #{insured_id}
					   AND CL.CALC_PAY > 0  ]]>
	</select>
	
		<!-- 查询当前赔案关联赔案信息 -->
	<select id="queryClaimCaseByTogether" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
SELECT cc.case_no,
       csc.sub_case_id,
       e2.CUSTOMER_NAME   as insuredName,
       (SELECT tcy.name FROM DEV_CLM.t_Claim_Type tcy WHERE   csc.CLAIM_TYPE=tcy.code )    AS claimTypeName,
       cm2.initial_validate_date  as validate_date,
       csc.claim_date     as claim_date,
        (SELECT ad.detail_desc FROM DEV_CLM.t_Accident_Detail ad WHERE ad.detail_code=cc.accident_detail)  as accidentDetail,
        (SELECT MN.NAME FROM APP___CLM__DBUSER.T_CLAIM_NATURE MN WHERE MN.CODE=csc.acc_reason)      as accReasonName,
       cc.calc_pay        as calc_pay,
           cc.case_status as case_status,
           (SELECT csa.NAME FROM APP___CLM__DBUSER.t_Case_Status csa WHERE csa.CODE=cc.case_status)    as caseStatusName,  
          cc.end_case_time   as end_case_time
  FROM (SELECT a.case_id        as case_id,
               a.insured_id     as insured_id,
               a.case_no        as case_no,
               cm.policy_code,
               e.CUSTOMER_NAME  as CUSTOMER_NAME,
               b.CLAIM_TYPE     AS CLAIM_TYPE,
               cm.initial_validate_date as validate_date,
               b.claim_date     as claim_date
          FROM DEV_CLM.t_Claim_Case       a,
               DEV_CLM.t_claim_SUB_CASE   b,
               DEV_CLM.t_Insured_List     c,
               DEV_CLM.t_Customer         e,
               DEV_CLM.t_Contract_Master  cm,
               DEV_CLM.t_Contract_Busi_Prod cbp
         WHERE 1=1
           and a.CASE_ID = #{case_id}]]>
          <if test="BUSI_PROD_CODE != null  "> <![CDATA[   AND CBP.BUSI_PROD_CODE= #{BUSI_PROD_CODE} ]]> </if>
          <![CDATA[and cm.policy_code = c.policy_code
           and cbp.policy_code =cm.policy_code
           and cbp.case_id=cm.case_id
           and cbp.cur_flag='1' 
           and cm.policy_code = c.policy_code
           and b.claim_type in ('01','04','08')
           and a.case_id = b.case_id
           and a.insured_id = c.customer_id
           and c.case_id = a.case_id
           and c.cur_flag = '1'
           and cm.case_id = a.case_id
           and cm.cur_flag = '1'
           and e.customer_id = c.customer_id
         group by 
         a.update_time,
                   a.case_id,
                  a.insured_id,
                  case_no,
                  cm.policy_code,
                  CUSTOMER_NAME,
                  b.CLAIM_TYPE,
                  cm.initial_validate_date,
                  claim_date
                  ) tCLaim,
       DEV_CLM.t_claim_case cc,
       DEV_CLM.t_claim_sub_case csc,
       DEV_CLM.t_Contract_Master cm2,
       DEV_CLM.t_Insured_List c2,
       DEV_CLM.t_Customer e2
 where cc.case_id != tCLaim.case_id
   and cc.insured_id != tCLaim.insured_id
   and csc.case_id = cc.case_id
   and csc.claim_type in ('01','04')
   and cm2.policy_code = tCLaim.Policy_Code
   and cm2.cur_flag = '1'
   and cm2.case_id = cc.case_id
   and csc.claim_date = tCLaim.Claim_Date
   and cc.insured_id=c2.customer_id
   and c2.case_id = cc.case_id
   and c2.cur_flag = '1'
   and e2.customer_id = c2.customer_id 
      and cc.case_status  not in( '90','99')
      group by cc.case_no,
            csc.sub_case_id,
            e2.customer_name,
            csc.claim_type,
            cm2.initial_validate_date,
            csc.claim_date,
            csc.acc_reason,
            cc.accident_detail,
            cc.calc_pay,
            cc.case_status,
            cc.end_case_time
	]]> 
	</select>
	
	<!-- 根据自助理赔申请号查询赔案 -->
	<select id="findClaimCaseByAutoCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME,A.AUDIT_START_TIME, A.APPROVE_TIME, A.CHANNEL_CODE,
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.COMFORT_STATUS,
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, A.REALTIME_PAY,
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.UPDATE_BY, A.IS_AUTO_HUNGUP, A.IS_MIGRATION, A.IS_MIGRATION ,A.SIGN_TIME,
			A.RPTR_TIME,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,
			A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
			A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.UPDATE_TIMESTAMP,
			A.INSERT_TIME,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
            A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,
            A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,
            A.ASSIGNEE_END_DATE,A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.IS_OVER_COMP,
            A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,
            A.ELEC_CHECK_FLAG,A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON 
             FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE ROWNUM <= 1000 AND A.AUTO_CASE_NO = #{auto_case_no} ORDER BY A.CASE_ID ]]> 
	</select>
	

				<!-- 查询当前赔案关联赔案信息 -->
	<select id="queryClaimCaseByTogetherNotSameDate" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
SELECT cc.case_no,
               csc.sub_case_id,
               e2.CUSTOMER_NAME   as insuredName,
               (SELECT tcy.name FROM DEV_CLM.t_Claim_Type tcy WHERE   csc.CLAIM_TYPE=tcy.code )    AS claimTypeName,
               cm2.initial_validate_date  as validate_date,
               (SELECT MN.NAME FROM APP___CLM__DBUSER.T_CLAIM_NATURE MN WHERE MN.CODE=csc.acc_reason)      as accReason,
               cc.accident_detail as accidentDetail,
               cc.calc_pay        as calc_pay,
                cc.case_status as case_status,
                (SELECT csa.NAME FROM APP___CLM__DBUSER.t_Case_Status csa WHERE csa.CODE=cc.case_status)    as caseStatusName,  
               cc.end_case_time   as end_case_time,
               tCLaim.claim_date as claim_date,
               csc.claim_date as  togetherClaim_date,
                 tCLaim.rptrTime as rptrTime,
                (case when cc.rptr_time IS NOT NULL 
                         then  cc.rptr_time
                           else cc.sign_time end) as toRptrTime
          FROM (SELECT a.case_id        as case_id,
                       a.insured_id     as insured_id,
                       a.case_no        as case_no,
                       cm.policy_code,
                       e.CUSTOMER_NAME  as CUSTOMER_NAME,
                       b.CLAIM_TYPE     AS CLAIM_TYPE,
                       cm.initial_validate_date as validate_date,
                       b.claim_date     as claim_date,
                       (case when a.rptr_time IS NOT NULL 
                         then  a.rptr_time
                           else a.sign_time  end) as rptrTime
                  FROM DEV_CLM.t_Claim_Case         a,
                       DEV_CLM.t_claim_SUB_CASE     b,
                       DEV_CLM.t_Insured_List       c,
                       DEV_CLM.t_Customer           e,
                       DEV_CLM.t_Contract_master   cm,
                       DEV_CLM.t_Contract_Busi_Prod cbp
                 WHERE 1 = 1
                    and a.CASE_ID = #{case_id}]]>
                  <if test="BUSI_PROD_CODE != null  "> <![CDATA[   AND CBP.BUSI_PROD_CODE= #{BUSI_PROD_CODE} ]]> </if>
                 <![CDATA[ and cm.policy_code = c.policy_code
                   and cbp.policy_code = cm.policy_code
                   and cbp.case_id = cm.case_id
                   and cbp.cur_flag = '1'
                   and cm.policy_code = c.policy_code
                   and b.claim_type in ('01', '04', '08')
                   and a.case_id = b.case_id
                   and a.insured_id = c.customer_id
                   and c.case_id = a.case_id
                   and c.cur_flag = '1'
                   and cm.case_id = a.case_id
                   and cm.cur_flag = '1'
                   and e.customer_id = c.customer_id
                 group by a.update_time,
                          a.case_id,
                          a.insured_id,
                          case_no,
                          cm.policy_code,
                          CUSTOMER_NAME,
                          b.CLAIM_TYPE,
                          cm.initial_validate_date,
                          claim_date,
                          a.rptr_time,
                          a.sign_time) tCLaim,
               DEV_CLM.t_claim_case cc,
               DEV_CLM.t_claim_sub_case csc,
               DEV_CLM.t_Contract_Master cm2,
               DEV_CLM.t_Insured_List c2,
               DEV_CLM.t_Customer e2
         where cc.case_id != tCLaim.case_id
           and cc.insured_id != tCLaim.insured_id
           and csc.case_id = cc.case_id
           and cm2.policy_code = tCLaim.Policy_Code
           and cm2.cur_flag = '1'
           and cm2.case_id = cc.case_id
           and cc.insured_id = c2.customer_id
           and c2.case_id = cc.case_id
           and c2.cur_flag = '1'
           and e2.customer_id = c2.customer_id
           and cc.case_status  not in( '90','99')
         group by cc.case_no,
                  csc.sub_case_id,
                  e2.customer_name,
                  csc.claim_type,
                  cm2.initial_validate_date,
                  csc.acc_reason,
                  cc.accident_detail,
                  cc.calc_pay,
                  cc.case_status,
                  cc.end_case_time,
                   tCLaim.claim_date,
                   csc.claim_date,
                  tCLaim.rptrTime,
                  cc.rptr_time,
                  cc.sign_time]]>
	</select>
	<!-- 根据就诊流水号和医院ID查询赔案信息 -->
	<select id="findClaimCaseByMedicalNumAndHid" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID,A.CASE_NO FROM DEV_CLM.T_CLAIM_CASE A WHERE A.MEDICAL_NUM=#{medical_num} AND A.CURE_HOSPITAL=#{cure_hospital}]]>
	</select> 
	
	<!-- 查询案件状态 -->
	<select id="findClaimCaseStatusByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select t.case_id,t.case_no,t.insured_id,t.case_status, a.claim_type
		  from DEV_CLM.T_CLAIM_CASE t
		  left join DEV_CLM.T_CLAIM_SUB_CASE a
		    on t.case_id = a.case_id
		 where 1 = 1
		   and t.insured_id in (select tc.insured_id from DEV_CLM.T_CLAIM_CASE tc where tc.case_id = #{case_id})
		   and t.case_id <> #{case_id}
		   and a.claim_type in (select sc.claim_type from DEV_CLM.T_CLAIM_SUB_CASE sc where sc.case_id = #{case_id})
        ]]> 
	</select>
	
	<!-- 查询出险人既往的疾病代码 -->
	<select id="queryPreviousillnessByinSuredId" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT a.serious_disease,a.case_no
  		FROM DEV_CLM.t_claim_case a, DEV_CLM.t_claim_sub_case c
 			WHERE a.case_status = '80'
   				and a.insured_id = #{insured_id}
   and a.case_id=c.case_id 
   and c.claim_type in ('03','10')
	</select>
	
	<!--查询赔案中的最大的确诊时间-->
	<select id="queryPreviousillnessBycaseNoList" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT MAX(A.DIAGNOSIS_TIME) as DIAGNOSIS_TIME FROM 
	 		DEV_CLM.T_CLAIM_CASE A  WHERE  1=1
	<if test=" case_no_list != null"><![CDATA[ AND A.CASE_NO IN]]>
			<foreach collection="case_no_list" item="case_no" index="index"
				open="(" close=")" separator=",">#{case_no}</foreach>
	</if>
	</select>
	
	<!-- 根据授权标识查询案件状态 -->
	<select id="findCaseStatusByAuthorTag" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME,A.AUDIT_START_TIME, A.APPROVE_TIME, A.CHANNEL_CODE,
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.COMFORT_STATUS,
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, A.REALTIME_PAY,
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.UPDATE_BY, A.IS_AUTO_HUNGUP, A.IS_MIGRATION, A.IS_MIGRATION ,A.SIGN_TIME,
			A.RPTR_TIME,A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,
			A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
			A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL,A.UPDATE_TIMESTAMP,A.INSERT_TIME,A.SALESMAN_SELF_INSURANCE,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.REPEAT_NUMBER_FLAG,
            A.REPEAT_NUMBER_REASON,A.REPEAT_NUMBER_TYPE,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.IS_CHECKLIST,A.AGENT_RISK_LEVEL,A.CUSTOMER_RISK_LEVEL,A.EARLY_WARNING,A.DIAGNOSIS_TIME,A.SIGNATURE_TRACE_FLAG,
            A.FACE_RECOGNITION_FLAG,A.SMS_SEND_FLAG,A.ASSIGNEE_START_DATE,A.ASSIGNEE_END_DATE ,
            A.MEDICAL_NUM,A.TREAT_DATE,A.HOSPITAL_NUM,A.CHANGE_STATUS_TIME,A.IS_OVER_COMP,
            A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,A.INSURANCE_PAID_FLAG,IS_SMALL_CASE,A.IS_PROBLEM_SOLVE,A.ACQUIST_WAY,A.IS_DEDUCT_PREM,A.claim_case_id,A.claim_order,A.claim_order_audit,A.ELEC_CHECK_FLAG,
            A.AUTO_CASE_NO,A.AUTO_CASE_TYPE,A.LOCK_FLAG,A.LOCK_SYS,A.SERVCOM,A.FIRST_RPTR_TIME,A.IS_CALLED_BACK,A.REPORT_SOURCE,A.JOB_PROMPT,A.NOTREGIST_DOC_FLAG,A.UN_DOC_REASON
		    FROM APP___CLM__DBUSER.T_CLAIM_CASE A
		   INNER JOIN APP___CLM__DBUSER.T_CLAIM_DIRECT_INFO B ON A.CASE_ID = B.CASE_ID
		   INNER JOIN APP___CLM__DBUSER.T_CLAIM_AUTH_CUSTOMER_INFO C ON B.TEMP_BUSS_NO = C.TEMP_BUSS_NO
		   WHERE C.AUTHOR_TAG = #{author_tag} ]]> 
	</select>
	
	<!-- 根据对账结算日期查询调用过理算和直赔撤销接口的案件 -->
	<select id="findCaseByServcomAndBillDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.CASE_NO,
					       A.CASE_ID,
					       DI.REVOKE_FLAG,
					       DI.YMTP_REPORT_NO,
					       (SELECT DISTINCT CBID.MEDICAL_NUM
					          FROM APP___CLM__DBUSER.T_CLAIM_BILL_ITEM_DETAIL CBID
					         WHERE A.CASE_ID = CBID.CASE_ID) MEDICAL_NUM
					  FROM APP___CLM__DBUSER.T_CLAIM_CASE A
					 INNER JOIN APP___CLM__DBUSER.T_CLAIM_DIRECT_INFO DI
					    ON A.CASE_ID = DI.CASE_ID
					 WHERE A.SERVCOM = #{servcom}
					   AND DI.CALC_INTER_TIME BETWEEN
					       TO_DATE(#{bill_date} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') AND
					       TO_DATE(#{bill_date} || ' 23:59:59', 'yyyy-mm-dd hh24:mi:ss')
					
					UNION ALL
					
					SELECT A.CASE_NO,
					       A.CASE_ID,
					       DI.REVOKE_FLAG,
					       DI.YMTP_REPORT_NO,
					       (SELECT DISTINCT CBID.MEDICAL_NUM
					          FROM APP___CLM__DBUSER.T_CLAIM_BILL_ITEM_DETAIL CBID
					         WHERE A.CASE_ID = CBID.CASE_ID) MEDICAL_NUM
					  FROM APP___CLM__DBUSER.T_CLAIM_CASE A
					 INNER JOIN APP___CLM__DBUSER.T_CLAIM_DIRECT_INFO DI
					    ON A.CASE_ID = DI.CASE_ID
					 WHERE A.SERVCOM = #{servcom}
					   AND DI.CANCEL_INTER_TIME BETWEEN
					       TO_DATE(#{bill_date} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') AND
					       TO_DATE(#{bill_date} || ' 23:59:59', 'yyyy-mm-dd hh24:mi:ss')
					   AND NOT (DI.CALC_INTER_TIME BETWEEN
					        TO_DATE(#{bill_date} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') AND
					        TO_DATE(#{bill_date} || ' 23:59:59', 'yyyy-mm-dd hh24:mi:ss'))	
					 ORDER BY CASE_ID ]]> 
	</select>
	
	 <!-- 查询是否存在重复的直赔案件 -->
	<select id="queryDoubleDirectCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select cc.case_id from APP___CLM__DBUSER.T_CLAIM_CASE cc, 
										    APP___CLM__DBUSER.T_CLAIM_SUB_CASE sc 
							where cc.case_id = sc.case_id and cc.insured_id = #{insured_id}
											   and sc.claim_type = #{claim_type}
											   and sc.claim_date = #{claim_date}
											   and sc.acc_reason = #{acc_reason}
											   and cc.report_mode = '10'
												]]>
	</select>

	<select id="queryHistoricalCase2YearsPrior" resultType="java.util.Map"
			parameterType="java.util.Map">
		<![CDATA[  select distinct a.case_id,
								   a.cure_hospital,
								   a.accident_detail,
								   a.calc_pay,
								   a.advance_pay,
								   a.balance_pay,
								   a.actual_pay,
								   a.reject_pay,
								   c.accident_no,
								   d.hospital_level
				   from dev_clm.t_claim_case a
							join dev_clm.t_claim_sub_case b
								 on a.case_id = b.case_id
							join dev_clm.t_claim_accident c
								 on a.accident_id = c.accident_id
							left join dev_clm.t_claim_hospital_service  d
								 on a.cure_hospital = d.hospital_code
				   where 1 = 1
					 and a.INSURED_ID = #{insured_id}
					 and a.case_id != #{case_id} /*排除当前案件*/
					 and a.case_status = '80'
					 and b.claim = '08'
					 and b.claim_date >= #{claim_date} - INTERVAL '2' YEAR
					 and b.claim_date <= #{claim_date};
		]]>
	</select>

</mapper>
