<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ClaimCompMapper"> 
<!-- 查询理赔险种结算表中非涉案的数据 -->
	<select id="findNotClaimAdjustBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ASSIGN_FLAG, A.ADJUST_BUSI_ID, A.ADJ_AMOUNT, A.REMARKS, A.BUSI_ADJUST_REASON, A.ADVANCE_FLAG, 
			A.ADJUST_TYPE, A.INSURED_ID, A.CASE_ID, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.FEE_AMOUNT, A.BUSI_ITEM_ID, A.PAY_AMOUNT, A.POLICY_ID,A.LIAB_ID 
  				FROM APP___CLM__DBUSER.T_CLAIM_ADJUST_BUSI A WHERE A.CASE_ID = #{case_id}
           		AND A.POLICY_CODE NOT IN (
           						SELECT B.POLICY_CODE      
             					FROM APP___CLM__DBUSER.T_CLAIM_POLICY B
             					WHERE 1 = 1
              		 			AND B.CASE_ID = #{case_id}) ]]>
		<![CDATA[ ORDER BY A.ADJUST_BUSI_ID ]]> 
	</select>
	<!-- #99524 -->
	<!-- 查询个数操作 -->
	<select id="findDrugDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL A,DEV_CLM.T_DRUG_TYPE C  WHERE 1 = 1 AND A.DRUG_TYPE=C.DRUG_TYPE_CODE]]>
		<if test=" drug_type_name  != null  and  drug_type_name  != '' ">
      		<![CDATA[ AND C.DRUG_TYPE_NAME like '%${drug_type_name}%' ]]>
		</if>
		<if test=" drug_name != null and drug_name != ''  "><![CDATA[ AND A.DRUG_NAME like '%'||#{drug_name}||'%'  ]]></if>
		<if test=" chemical_name != null and chemical_name != ''  "><![CDATA[ AND A.CHEMICAL_NAME    like '%'||#{chemical_name}||'%'    ]]></if>
		<if test=" drug_type != null and drug_type != ''  "><![CDATA[ AND A.DRUG_TYPE = #{drug_type} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="queryDrugDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.VENDOR_NAME, B.CHEMICAL_NAME, B.LIST_ID, 
			B.DRUG_CODE, B.DRUG_TYPE, B.DRUG_NAME,B.DRUG_TYPE_NAME FROM (
					SELECT ROWNUM RN, A.VENDOR_NAME, A.CHEMICAL_NAME, A.LIST_ID, 
			A.DRUG_CODE, A.DRUG_TYPE, A.DRUG_NAME,C.DRUG_TYPE_NAME FROM  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL A , DEV_CLM.T_DRUG_TYPE C 
			WHERE ROWNUM <= #{LESS_NUM} AND A.DRUG_TYPE=C.DRUG_TYPE_CODE ]]>
		<if test=" drug_type_name  != null  and  drug_type_name  != '' ">
      		<![CDATA[ AND C.DRUG_TYPE_NAME like '%${drug_type_name}%' ]]>
		</if>
		<if test=" drug_name != null and drug_name != ''  "><![CDATA[ AND A.DRUG_NAME like '%'||#{drug_name}||'%'  ]]></if>
		<if test=" chemical_name != null and chemical_name != ''  "><![CDATA[ AND A.CHEMICAL_NAME    like '%'||#{chemical_name}||'%'    ]]></if>
		<if test=" drug_type != null and drug_type != ''  "><![CDATA[ AND A.DRUG_TYPE = #{drug_type} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- ${} ：将${}内的参数直接嵌入sql语句，而不适用预编译功能，这样不能在语句中使用#{}的方式传参数 -->
	<select id="findAccidentResult2ReCoce" parameterType="java.util.Map"
		resultType="java.util.Map">
    	     <![CDATA[
    	     select A2.RELA_CODE, A2.CODE, A2.NAME from APP___CLM__DBUSER.T_ACCIDENT2 a2 where a2.rela_code = #{rela_code} and a2.code = #{code}]]>
	</select>
	<select id="findCodeValue" resultType="java.util.Map"
		parameterType="java.util.Map" statementType="STATEMENT">
		<![CDATA[select ${DESC_COLUMN} as VALUE from ${TABLE_NAME} where ${ID_COLUMN} = ${CODE}]]>
	</select>
	<select id="findRelaContractProductBySubCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.IS_PAUSE,A.PROD_PKG_PLAN_CODE,A.APPEND_PREM_AF,A.APPLY_CODE,A.NORENEW_REASON,A.ORGAN_CODE,A.CHARGE_YEAR,A.EXTRA_PREM_AF,A.POLICY_ID,A.CC_SA,A.INITIAL_EXTRA_PREM_AF,
                A.AMOUNT,A.CASE_ID, A.EXPIRY_DATE, A.PAY_YEAR,A.PAY_PERIOD,A.LIABILITY_STATE,A.COPY_DATE,A.COUNT_WAY,A.POLICY_CODE,A.IS_GIFT, A.RERINSTATE_DATE,A.ADDITIONAL_PREM_AF,
                A.RENEW_DECISION,A.VALIDATE_DATE,A.BONUS_MODE,A.BENEFIT_LEVEL,A.PRODUCT_ID,A.BONUS_SA,A.PRODUCT_CODE,A.APPLY_DATE,A.COVERAGE_PERIOD,A.ITEM_ID,A.RENEWAL_EXTRA_PREM_AF,
                A.MATURITY_DATE, A.BUSI_ITEM_ID,A.HEALTH_SERVICE_FLAG,A.LAPSE_DATE,A.PAY_FREQ,A.UNIT,A.COVERAGE_YEAR,A.END_CAUSE,A.LAPSE_CAUSE,A.RENEWAL_DISCNTED_PREM_AF,A.TOTAL_PREM_AF,
                A.DECISION_CODE,A.PAUSE_DATE, A.LOG_ID,A.CHARGE_PERIOD,A.STD_PREM_AF,A.INTEREST_MODE,A.SUSPEND_DATE,A.SUSPEND_CAUSE,A.PREM_FREQ,A.INITIAL_DISCNT_PREM_AF,A.CUR_FLAG,
                A.PAIDUP_DATE,A.IS_WAIVED,A.WAIVER_START,A.WAIVER_END,A.INITIAL_AMOUNT,A.DEDUCTIBLE_FRANCHISE,A.PAYOUT_RATE,A.IS_MASTER_ITEM,A.BUSI_PROD_CODE,A.BUSI_PRD_ID,A.CUSTOMER_GENDER,
                A.MASTER_BUSI_ITEM_ID,B.BUSI_PRD_ID MASTER_BUSI_PRD_ID,A.ANNU_PAY_TYPE
  				FROM (SELECT CP.IS_PAUSE,CP.PROD_PKG_PLAN_CODE,CP.APPEND_PREM_AF,CP.APPLY_CODE,CP.NORENEW_REASON,CP.ORGAN_CODE,CP.CHARGE_YEAR,CP.EXTRA_PREM_AF,CP.POLICY_ID,CP.CC_SA,
	               CP.INITIAL_EXTRA_PREM_AF,CP.AMOUNT,CP.CASE_ID,CP.EXPIRY_DATE,CP.PAY_YEAR,CP.PAY_PERIOD,CP.LIABILITY_STATE,CP.COPY_DATE,CP.COUNT_WAY,CP.POLICY_CODE,CP.IS_GIFT,
	               CP.RERINSTATE_DATE,CP.ADDITIONAL_PREM_AF,CP.RENEW_DECISION,CP.VALIDATE_DATE,CP.BONUS_MODE,CP.BENEFIT_LEVEL,CP.PRODUCT_ID,CP.BONUS_SA,CP.PRODUCT_CODE,CP.APPLY_DATE,
	               CP.COVERAGE_PERIOD,CP.ITEM_ID,CP.RENEWAL_EXTRA_PREM_AF,CP.MATURITY_DATE,CP.BUSI_ITEM_ID,CP.HEALTH_SERVICE_FLAG,CP.LAPSE_DATE,CP.PAY_FREQ,CP.UNIT,CP.COVERAGE_YEAR,
	               CP.END_CAUSE,CP.LAPSE_CAUSE,CP.RENEWAL_DISCNTED_PREM_AF,CP.TOTAL_PREM_AF,CP.DECISION_CODE,CP.PAUSE_DATE,CP.LOG_ID,CP.CHARGE_PERIOD,CP.STD_PREM_AF,CP.INTEREST_MODE,
	               CP.SUSPEND_DATE,CP.SUSPEND_CAUSE,CP.PREM_FREQ,CP.INITIAL_DISCNT_PREM_AF,CP.CUR_FLAG,CP.PAIDUP_DATE,CP.IS_WAIVED,CP.WAIVER_START,CP.WAIVER_END,CP.INITIAL_AMOUNT,CP.DEDUCTIBLE_FRANCHISE,
	               CP.PAYOUT_RATE,CP.IS_MASTER_ITEM,BP.BUSI_PROD_CODE,BP.BUSI_PRD_ID,CU.CUSTOMER_GENDER,BP.MASTER_BUSI_ITEM_ID,CP.ANNU_PAY_TYPE
		          FROM APP___CLM__DBUSER.T_CUSTOMER           CU,
		               APP___CLM__DBUSER.T_CLAIM_CASE         C,
		               APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD BP,
		               APP___CLM__DBUSER.T_CLAIM_SUB_CASE     SC,
		               APP___CLM__DBUSER.T_CONTRACT_PRODUCT   CP
		         WHERE ((SELECT COUNT(1)
                   FROM APP___CLM__DBUSER.T_POLICY_HOLDER PH
                  WHERE PH.CUR_FLAG = 0
                    AND PH.POLICY_ID = CP.POLICY_ID
                    AND PH.COPY_DATE = CP.COPY_DATE
                    AND PH.CASE_ID = CP.CASE_ID
                    AND PH.CUSTOMER_ID = C.INSURED_ID) > 0 OR
               (SELECT COUNT(1)
                   FROM APP___CLM__DBUSER.T_INSURED_LIST    IL,
                        APP___CLM__DBUSER.T_BENEFIT_INSURED BI
                  WHERE IL.CUR_FLAG = 0
                    AND CP.COPY_DATE = IL.COPY_DATE
                    AND IL.LIST_ID = BI.INSURED_ID
                    AND CP.POLICY_ID = IL.POLICY_ID
	                  AND CP.CASE_ID = IL.CASE_ID
                    AND BI.CUR_FLAG = 0
                    AND CP.COPY_DATE = BI.COPY_DATE
	                  AND CP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
	                  AND CP.CASE_ID = BI.CASE_ID)>0)
	               AND C.INSURED_ID = CU.CUSTOMER_ID
	               AND CP.CASE_ID = C.CASE_ID
	               AND BP.CUR_FLAG = 0
	               AND CP.COPY_DATE = BP.COPY_DATE
	               AND CP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
	               AND CP.CASE_ID = BP.CASE_ID
	               AND CP.CUR_FLAG = 0
	               AND CP.VALIDATE_DATE <= SC.CLAIM_DATE
	               AND CP.COPY_DATE = SC.CLAIM_DATE
	               AND CP.CASE_ID = SC.CASE_ID
		           AND SC.SUB_CASE_ID = #{sub_case_id}) A
		  LEFT JOIN APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD B
		    ON A.CASE_ID = B.CASE_ID
		   AND B.CUR_FLAG=0
		   AND A.MASTER_BUSI_ITEM_ID = B.BUSI_ITEM_ID]]>
		<!-- LOG有了后 去掉 or cp.end_cause=01 这个条件 -->
		<!-- and cp.liability_state = 1 -->
	</select>

	<select id="findClaimProductFromLiab" resultType="java.util.Map"
		parameterType="java.util.Map"> 
		<![CDATA[SELECT distinct A.CASE_ID,
				       A.BUSI_ITEM_ID,
				       A.POLICY_ID,
				       A.POLICY_CODE,
				       A.ITEM_ID,
				       A.PRODUCT_ID,
				       A.INSURED_ID,
				       A.BUSINESS_PRD_ID,
				       A.BUSI_PROD_CODE,
				       A.VALIDATE_DATE,
				       A1.EXPIRY_DATE EXPIRY_DATE,
				       A1.AMOUNT,
				       A1.LIABILITY_STATE,
				       A1.END_CAUSE,
				       0                  AS ACTUAL_PAY,
				       0                  AS BASIC_PAY,
				       0                  AS CALC_PAY,
				       0                  AS WAIVE_AMT
				  FROM (SELECT CP.CASE_ID,
				               CP.BUSI_ITEM_ID,
				               CP.POLICY_ID,
				               CP.POLICY_CODE,
				               CP.ITEM_ID,
				               CP.PRODUCT_ID,
				               CC.INSURED_ID,
				               PL.BUSINESS_PRD_ID,
				               BP.PRODUCT_CODE_SYS BUSI_PROD_CODE,
				               min(CP.VALIDATE_DATE) VALIDATE_DATE,
				               MAX(CP.COPY_DATE) COPY_DATE,
				               MAX(CP.CUR_FLAG) CUR_FLAG
				          FROM APP___CLM__DBUSER.T_CONTRACT_PRODUCT CP
				         INNER JOIN APP___CLM__DBUSER.T_PRODUCT_LIFE PL
				            ON CP.PRODUCT_ID = PL.PRODUCT_ID
				         INNER JOIN APP___CLM__DBUSER.T_BUSINESS_PRODUCT BP
				            ON PL.BUSINESS_PRD_ID = BP.BUSINESS_PRD_ID
				         INNER JOIN APP___CLM__DBUSER.T_CLAIM_LIAB CL
				            ON CP.POLICY_ID = Cl.POLICY_ID
				           AND CP.CASE_ID = CL.CASE_ID
				         LEFT JOIN DEV_CLM.T_CLAIM_LIAB_CLOSE CLO 
				         	ON CP.POLICY_ID = CLO.POLICY_ID 
				         	AND CP.CASE_ID = CLO.CASE_ID 
				         	AND CP.BUSI_ITEM_ID = CLO.BUSI_ITEM_ID
				         INNER JOIN APP___CLM__DBUSER.T_CLAIM_CASE CC
				            ON CL.CASE_ID = CC.CASE_ID
				         WHERE (CL.ITEM_ID=CP.ITEM_ID  or (CP.CUR_FLAG='1' AND (CP.LIABILITY_STATE='1' OR CP.LIABILITY_STATE=4 or (CP.LIABILITY_STATE='3' AND CP.END_CAUSE='01'))))
				           AND CC.CASE_ID = #{case_id}
				         GROUP BY CP.CASE_ID,
				                  CP.BUSI_ITEM_ID,
				                  CP.POLICY_ID,
				                  CP.POLICY_CODE,
				                  CP.ITEM_ID,
				                  CP.PRODUCT_ID,
				                  CC.INSURED_ID,
				                  PL.BUSINESS_PRD_ID,
				                  BP.PRODUCT_CODE_SYS) A
				 INNER JOIN APP___CLM__DBUSER.T_CONTRACT_PRODUCT A1
				    ON ((a.cur_flag=1 and a.cur_flag=a1.cur_flag) 
          			or (a.cur_flag=0 and A.COPY_DATE = A1.COPY_DATE))
				   AND A.PRODUCT_ID = A1.PRODUCT_ID
				   AND A.BUSI_ITEM_ID = A1.BUSI_ITEM_ID
				   AND A.POLICY_ID = A1.POLICY_ID
				   AND A.CASE_ID = A1.CASE_ID]]>
	</select>

	<!--cur_flag: 0-出险日保单 1-当前保单 -->
	<select id="findClaimBusiProdFromLiab" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
		   SELECT distinct A.CASE_ID,
			       A.BUSI_ITEM_ID,
			       A.BUSI_PROD_CODE,
			       A.BUSI_PRD_ID,
			       A.POLICY_ID,
			       A.POLICY_CODE,
			       A.INSURED_ID,
			       A.VALID_DATE,
			       A1.EXPIRY_DATE EXPIRE_DATE,
			       A1.END_CAUSE,
			       0 AS ACTUAL_PAY,
			       0 AS CALC_PAY,
			       0 AS REJECT_PAY,
			       0 AS WAIVE_AMT,
			       A1.LIABILITY_STATE LIABILITY_STATUS
			  FROM (SELECT BP.CASE_ID,
			               BP.BUSI_ITEM_ID,
			               BP.BUSI_PROD_CODE,
			               BP.BUSI_PRD_ID,
			               BP.POLICY_ID,
			               BP.POLICY_CODE,
			               CC.INSURED_ID,
			               min(BP.VALIDATE_DATE) VALID_DATE,
			               max(BP.copy_date) copy_date,
			               max(BP.CUR_FLAG) cur_flag
			          FROM APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD BP
			         INNER JOIN APP___CLM__DBUSER.T_CLAIM_CASE CC
			            on bp.case_id = cc.case_id
			         INNER JOIN APP___CLM__DBUSER.T_CLAIM_LIAB CL
			            ON BP.POLICY_ID = CL.POLICY_ID
			           AND CC.CASE_ID=CL.CASE_ID
			        WHERE (CL.BUSI_ITEM_ID=BP.BUSI_ITEM_ID  or (BP.CUR_FLAG='1' AND (BP.LIABILITY_STATE='1' OR BP.LIABILITY_STATE=4 or (BP.LIABILITY_STATE='3' AND BP.END_CAUSE='01'))))
			           AND CC.CASE_ID = #{case_id}
			         GROUP BY BP.CASE_ID,
			                  BP.BUSI_ITEM_ID,
			                  BP.BUSI_PROD_CODE,
			                  BP.BUSI_PRD_ID,
			                  BP.POLICY_ID,
			                  BP.POLICY_CODE,
			                  CC.INSURED_ID) A
			 INNER JOIN APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD A1
			    ON ((a.cur_flag=1 and a.cur_flag=a1.cur_flag) 
          		or (a.cur_flag=0 and A.COPY_DATE = A1.COPY_DATE))
			   AND A.BUSI_PROD_CODE = A1.BUSI_PROD_CODE
			   AND A.BUSI_ITEM_ID = A1.BUSI_ITEM_ID
			   AND A.POLICY_ID = A1.POLICY_ID
			   AND A.CASE_ID = A1.CASE_ID
		]]>
	</select>

	<!--cur_flag: 0-出险日保单 1-当前保单 -->
	<select id="findClaimPolicyFromLiab" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[     
           SELECT DISTINCT CL.CASE_ID,
				           CL.POLICY_ID,
				           CL.POLICY_CODE,
                           0 AS BASIC_PAY,
		                   0 AS CALC_PAY,
                           0 AS ACTUAL_PAY,
		                   CM.ORGAN_CODE,
		                   CM.LIABILITY_STATE LIABILITY_STATUS,
		                   CM.VALIDDATE_DATE VALIDDATE_DATE,
		                   case when CM.LIABILITY_STATE=3 THEN CM.EXPIRY_DATE ELSE NULL END AS EXPIRY_DATE,
		                   case when CM.LIABILITY_STATE=3 THEN CM.END_CAUSE ELSE NULL END AS END_CAUSE
				  FROM APP___CLM__DBUSER.T_CLAIM_CASE CC, 
				       APP___CLM__DBUSER.T_CONTRACT_MASTER CM,
               		   APP___CLM__DBUSER.T_CLAIM_LIAB CL
				 WHERE CL.CASE_ID = CC.CASE_ID
				   AND CM.CUR_FLAG = 1
		           AND CL.POLICY_ID = CM.POLICY_ID
		           AND CL.CASE_ID = CM.CASE_ID
				   AND CL.CASE_ID = #{case_id} ]]>
	</select>
	
	<!--cur_flag: 0-出险日保单 1-当前保单 -->
	<select id="findClaimProductFromUw" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[     
           select DISTINCT U.CASE_ID,
                P.BUSI_ITEM_ID,
                P.POLICY_ID,
                P.POLICY_CODE,
                P.ITEM_ID,
                P.PRODUCT_ID,
                C.INSURED_ID,
                0 AS ACTUAL_PAY,
                0 AS BASIC_PAY,
                0 AS CALC_PAY,
                0  AS WAIVE_AMT,
                P.LIABILITY_STATE,
                P.AMOUNT,
                BP.PRODUCT_CODE_SYS BUSI_PROD_CODE
  from APP___CLM__DBUSER.T_CLAIM_UW         U,
       APP___CLM__DBUSER.T_CONTRACT_PRODUCT P,
       APP___CLM__DBUSER.T_CLAIM_CASE       C,
       APP___CLM__DBUSER.T_PRODUCT_LIFE L,
       APP___CLM__DBUSER.T_BUSINESS_PRODUCT BP
 where U.CASE_ID = P.CASE_ID
   AND P.CUR_FLAG = '1'
   AND U.POLICY_ID = P.POLICY_ID
   AND U.CASE_ID = C.CASE_ID
   AND P.PRODUCT_ID = L.PRODUCT_ID
   AND L.BUSINESS_PRD_ID = BP.BUSINESS_PRD_ID
   AND U.POLICY_CODE NOT IN
       (SELECT DISTINCT POLICY_CODE
          FROM APP___CLM__DBUSER.T_CLAIM_PRODUCT
         WHERE CASE_ID = #{case_id})
   AND U.CASE_ID = #{case_id} ]]>
	</select>
	
	<!--cur_flag: 0-出险日保单 1-当前保单 -->
	<select id="findClaimPolicyFromUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[     
           select distinct U.CASE_ID, M.POLICY_ID, M.POLICY_CODE, 0 AS BASIC_PAY, 0 AS CALC_PAY, 0 AS ACTUAL_PAY, M.ORGAN_CODE, 
           			M.LIABILITY_STATE LIABILITY_STATUS, case when M.LIABILITY_STATE = 3 THEN M.EXPIRY_DATE ELSE NULL END AS EXPIRY_DATE
  			from APP___CLM__DBUSER.T_CLAIM_UW U, APP___CLM__DBUSER.T_CONTRACT_MASTER M
 				where U.CASE_ID = M.CASE_ID
   				AND M.CUR_FLAG = '1'
   				AND U.POLICY_ID = M.POLICY_ID
   				AND U.POLICY_CODE NOT IN (SELECT DISTINCT POLICY_CODE FROM APP___CLM__DBUSER.T_CLAIM_POLICY WHERE CASE_ID = #{case_id})
   				AND U.CASE_ID = #{case_id} ]]>
	</select>
	
	<!--cur_flag: 0-出险日保单 1-当前保单 -->
	<select id="findClaimBusiProdFromUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
		   select DISTINCT U.CASE_ID,
       P.POLICY_ID,
       P.POLICY_CODE,
       P.BUSI_ITEM_ID,
       P.BUSI_PROD_CODE,
       C.INSURED_ID,
       P.LIABILITY_STATE LIABILITY_STATUS,
       0 AS ACTUAL_PAY,
       0 AS CALC_PAY,
       0 AS REJECT_PAY,
       0 AS WAIVE_AMT,
       P.VALIDATE_DATE VALID_DATE,
       P.EXPIRY_DATE EXPIRE_DATE
  from APP___CLM__DBUSER.T_CLAIM_UW U, APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD P, APP___CLM__DBUSER.T_CLAIM_CASE C
 where U.CASE_ID = P.CASE_ID
   AND P.CUR_FLAG = '1'
   AND U.POLICY_ID = P.POLICY_ID
   AND U.CASE_ID = C.CASE_ID
   AND U.POLICY_CODE NOT IN
       (SELECT DISTINCT POLICY_CODE FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD where CASE_ID = #{case_id})
   AND U.CASE_ID = #{case_id}]]>
	</select>
	
	<!-- case_status 90 : 关闭 -->
	<select id="findFormerCase4PerEvent" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select cc.case_id
				    from APP___CLM__DBUSER.T_CLAIM_CASE cc,
                         APP___CLM__DBUSER.T_CLAIM_POLICY cp,
                         APP___CLM__DBUSER.T_CLAIM_ACCIDENT ca
				   where cc.accident_id = ca.accident_id
				     and cc.case_id = cp.case_id]]>
		<if test="policy_id !=null and policy_id !='' ">
   			<![CDATA[and cp.policy_id = #{policy_id}]]>
		</if>
		<if test="insured_id !=null and insured_id !='' ">
	    	<![CDATA[and cc.insured_id = #{insured_id} ]]>
		</if>
				    <![CDATA[
				     and ca.acc_date = #{acc_date} 
				     and cc.case_id != #{case_id}  
				     and cc.case_status != 90  
				     order by cc.insert_timestamp desc]]>
	</select>

	<select id="findFormerCase4SameEvent" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select cc.case_id
				    from APP___CLM__DBUSER.T_CLAIM_CASE cc,
				         APP___CLM__DBUSER.T_CLAIM_ACCIDENT ca,
				         APP___CLM__DBUSER.T_CLAIM_POLICY cp,
				         APP___CLM__DBUSER.T_CLAIM_SUB_CASE sc 
				   where cc.accident_id = ca.accident_id
				     and cc.case_id = sc.case_id
				     and cc.case_id = cp.case_id  ]]>
		<if test="policy_id !=null and policy_id !='' ">
				     	<![CDATA[and cp.policy_id = #{policy_id} ]]>
		</if>
		<if test="insured_id !=null and insured_id !='' ">
				     	<![CDATA[ and cc.insured_id = #{insured_id} ]]>
		</if>
				     <![CDATA[
				     and ca.acc_date = #{acc_date} 
				     and cc.case_id != #{case_id}  
				     and sc.claim_type = #{claim_type} 
				     and cc.case_status != 90  
				     order by cc.insert_timestamp desc]]>
	</select>
	<select id="findFormerCase4pRreHsopitalization" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select c.case_id,cb.treat_end
				   from APP___CLM__DBUSER.T_CLAIM_CASE c,
				        APP___CLM__DBUSER.T_CLAIM_BILL cb,
				        APP___CLM__DBUSER.T_CLAIM_POLICY cp,
				        APP___CLM__DBUSER.T_CLAIM_SUB_CASE sc
				  where c.case_id = sc.case_id
				    and c.case_id = cb.case_id
				    and c.case_id = cp.case_id
				    and c.case_status != 90
				    and sc.claim_type in ('03', '08', '10')
				    and c.case_id != #{case_id}]]>
		<if test="insured_id !=null and insured_id !='' ">
	    	   <![CDATA[and c.insured_id = #{insured_id}]]>
		</if>
		<if test="policy_id !=null and policy_id !='' ">
		       <![CDATA[and cp.policy_id = #{policy_id}]]>
		</if>
		   <![CDATA[order by c.insert_timestamp desc]]>

	</select>
	<!--xuyz_wb 赔案计算信息 -->
	<select id="findClaimCaseForMatchResult" resultType="java.util.Map"
		parameterType="java.util.Map">
    	<![CDATA[
    		SELECT A.CALC_PAY,
    			   A.ADVANCE_PAY,
    			   A.BALANCE_PAY,
    			   A.ACTUAL_PAY,
    			   A.REJECT_PAY
    		FROM APP___CLM__DBUSER.T_CLAIM_CASE A,APP___CLM__DBUSER.T_CLAIM_ACCIDENT B 
    		WHERE 1=1 AND A.ACCIDENT_ID = B.ACCIDENT_ID AND A.CALC_PAY IS NOT NULL
    	]]>
		<if test="case_no !=null and case_no !='' ">
    		<![CDATA[ AND A.CASE_NO = ${case_no}]]>
		</if>
		<if test="case_id != null and case_id  !='' ">
			<![CDATA[ AND a.case_id = #{case_id}]]>
		</if>
		<if test="accident_id !=null and accident_id !='' ">
    		<![CDATA[ AND B.ACCIDENT_ID = ${accident_id}]]>
		</if>
	</select>
	<!--xuyz_wb 理赔类型计算信息 -->
	<!-- 此处错误 1、账单金额错误，取t_claim_bill的sum_amount， 第三方和社保other_type错误，1为社保，2为第三方支付，社保的 
		取值使用 MEDICAL_FUND_PAYMENT，别名需要重新起 -->
	<select id="findClaimLiabForMatchResultByType" resultType="java.util.Map"
		parameterType="java.util.Map">
     	<!-- <![CDATA[
     	SELECT (SELECT C.NAME FROM APP___CLM__DBUSER.T_CLAIM_TYPE C WHERE C.CODE=A.CLAIM_TYPE) CLAIM_TYPE, 
     	     SUM(A.CALC_PAY) CALC_PAY,
     	     SUM(A.ADJUST_PAY) ACTUAL_PAY,
     	     NVL((SELECT SUM(A.ACTUAL_PAY) FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.CASE_ID = #{case_id}
               AND A.IS_SUBSIDY = 1),0) JINACTUAL_PAY,
             NVL((SELECT SUM(A.ACTUAL_PAY) FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.CASE_ID = #{case_id}
               AND A.IS_SUBSIDY != 1),0) NOJINACTUAL_PAY,
     	     SUM(A.BASIC_PAY) BASIC_PAY,
     	     NVL((SELECT SUM(D.PAID_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL_PAID D WHERE D.CASE_ID = #{case_id}
          AND D.OTHER_TYPE = '1'),0) PAID_AMOUNT,
             NVL((SELECT SUM(D.PAID_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL_PAID D WHERE D.CASE_ID = #{case_id}
          AND D.OTHER_TYPE = '2'),0) SUM_AMOUNT,
              NVL((SELECT SUM(S.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_SPECIAL S WHERE S.CASE_ID = #{case_id}),0) SPECIAL_PAY
 			 FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_SUB_CASE B
 			 WHERE 1 = 1
   			 AND A.SUB_CASE_ID = B.SUB_CASE_ID
   			 AND A.CLAIM_TYPE = B.CLAIM_TYPE
   			 AND B.CASE_ID = #{case_id}
		GROUP BY A.CLAIM_TYPE
     	]]> -->
     	<!-- <![CDATA[
     	SELECT (SELECT C.NAME FROM APP___CLM__DBUSER.T_CLAIM_TYPE C WHERE C.CODE=A.CLAIM_TYPE) CLAIM_TYPE, 
           SUM(A.CALC_PAY) CALC_PAY,
           SUM(A.ADJUST_PAY) ACTUAL_PAY,
           NVL((SELECT SUM(F.ACTUAL_PAY) FROM APP___CLM__DBUSER.T_CLAIM_LIAB F WHERE F.CASE_ID = #{case_id}
               AND F.IS_SUBSIDY = 1 AND F.CLAIM_TYPE=A.CLAIM_TYPE),0) JINACTUAL_PAY,
             NVL((SELECT SUM(G.ACTUAL_PAY) FROM APP___CLM__DBUSER.T_CLAIM_LIAB G WHERE G.CASE_ID = #{case_id}
               AND G.IS_SUBSIDY != 1 AND G.CLAIM_TYPE=A.CLAIM_TYPE),0) NOJINACTUAL_PAY,
           SUM(A.BASIC_PAY) BASIC_PAY,
           NVL((SELECT SUM(D.PAID_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL_PAID D WHERE D.CASE_ID = #{case_id}
          AND D.OTHER_TYPE = '1' AND A.CLAIM_TYPE='08'),0) PAID_AMOUNT,
             NVL((SELECT SUM(E.PAID_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL_PAID E WHERE E.CASE_ID = #{case_id}
          AND E.OTHER_TYPE = '2' AND A.CLAIM_TYPE='08'),0) SUM_AMOUNT,
              NVL((SELECT SUM(S.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_SPECIAL S WHERE S.CASE_ID = #{case_id}),0) SPECIAL_PAY
       FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_SUB_CASE B
       WHERE 1 = 1
         AND A.SUB_CASE_ID = B.SUB_CASE_ID
         AND A.CLAIM_TYPE = B.CLAIM_TYPE
         AND B.CASE_ID = #{case_id}
    	GROUP BY A.CLAIM_TYPE
     	]]> -->
     	<![CDATA[
     	SELECT (SELECT C.NAME FROM APP___CLM__DBUSER.T_CLAIM_TYPE C WHERE C.CODE=A.CLAIM_TYPE) CLAIM_TYPE, 
           SUM(case when a.liab_conclusion != 5 then nvl(a.adjust_pay,0) else 0 end) ACTUAL_PAY,
           NVL((SELECT SUM(D.PAID_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL_PAID D WHERE D.CASE_ID = #{case_id}
          AND D.OTHER_TYPE = '1' AND A.CLAIM_TYPE='08'),0) PAID_AMOUNT,
             NVL((SELECT SUM(E.PAID_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL_PAID E WHERE E.CASE_ID = #{case_id}
          AND E.OTHER_TYPE = '2' AND A.CLAIM_TYPE='08'),0) SUM_AMOUNT,
          NVL((SELECT SUM(B.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL B WHERE B.CASE_ID = #{case_id}
          AND B.TREAT_TYPE = '1' AND A.CLAIM_TYPE='08'),0) HOSPITAL_FEE_AMOUNT,
          NVL((SELECT SUM(B.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL B WHERE B.CASE_ID = #{case_id}
          AND B.TREAT_TYPE = '0' AND A.CLAIM_TYPE='08'),0) OUTPATIENT_FEE_AMOUNT
       FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_SUB_CASE B
       WHERE 1 = 1
         AND A.SUB_CASE_ID = B.SUB_CASE_ID
         AND A.CLAIM_TYPE = B.CLAIM_TYPE
         AND B.CASE_ID = #{case_id}
    	GROUP BY A.CLAIM_TYPE
     	]]>
	</select>
	<!-- xuyz_wb 保项计算信息不分页 add -->
	<select id="queryClaimLiabCalculate" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  select cl.claim_liab_id,
                            cl.policy_code,
					        cl.busi_prod_code,
					        cl.busi_item_id,
					        cl.Liab_Name,
					        cl.liab_start_date,
					        cl.liab_end_date,
					        cl.calc_pay,
					        cl.adjust_pay,
					        cl.advance_pay,
					        cl.liab_conclusion,
					        (select cp.amount
					           from APP___CLM__DBUSER.T_CONTRACT_PRODUCT cp
					          where cp.item_id = cl.item_id
					          	and cp.policy_id = cl.policy_id
					          	and cp.busi_item_id= cl.busi_item_id
					            and cp.case_id = cl.case_id
					            and cp.cur_flag = 1) amount
					   from APP___CLM__DBUSER.T_CLAIM_LIAB cl
					  where cl.case_id = #{case_id}]]>
	</select>
	<!-- daizheng1 保项计算信息不分页_NEW add -->
	<select id="queryClaimLiabCalculate_new" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[   SELECT DISTINCT CL.CLAIM_LIAB_ID,
                            CL.POLICY_CODE,
                            CL.POLICY_ID,
                  CL.BUSI_PROD_CODE,
                  CL.BUSI_ITEM_ID,
                  CL.LIAB_NAME,
                  CL.LIAB_START_DATE,
                  CL.LIAB_END_DATE,
                  CL.CALC_PAY,
                  CL.ADJUST_PAY,
                  CL.ADVANCE_PAY,
                  CL.LIAB_CONCLUSION,
                  CL.SUB_CASE_ID,
                  CL.ITEM_ID,
                  CL.CLM_AFTER_STATE,
                  CL.LIAB_ID,
                  CL.SURPLUS_EFFECT_AMOUNT,
                  CL.BEFORE_SURPLUS_EFFECT_AMOUNT,
                  CL.CLAIM_TYPE,
                  CL.ACCU_BONUS_AMOUNT,
                  CP.AMOUNT,
                  CP.BONUS_SA,
                  T.MASTER_BUSI_ITEM_ID
             FROM APP___CLM__DBUSER.T_CONTRACT_PRODUCT CP,
             APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD T,
             APP___CLM__DBUSER.T_CLAIM_LIAB CL
              WHERE    T.CUR_FLAG=1
              AND CL.BUSI_ITEM_ID = T.BUSI_ITEM_ID
              AND CL.CASE_ID = T.CASE_ID
              AND CP.CUR_FLAG=1
              AND CL.ITEM_ID = CP.ITEM_ID
              AND CL.CASE_ID = CP.CASE_ID
              AND  CL.CASE_ID= #{case_id}
					  
		      ORDER BY   CL.POLICY_CODE, CL.BUSI_PROD_CODE
					  ]]>
		<!-- (select cab.fee_amount 
				from APP___CLM__DBUSER.t_claim_adjust_busi cab 
			  where cab.policy_id = cl.policy_id 
			  	and cab.busi_item_id= cl.busi_item_id 
				and cab.case_id = cl.case_id 
				and cab.adjust_type = 9) pay_amount, -->
	</select>
	
	<!-- panjj_wb 查询回退赔案前是否支付完成  add -->
	<select id="findPremArapIsPayByCaseNO" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  SELECT A.CASE_NO, A.END_CASE_TIME, T.FINISH_TIME
  FROM APP___CAP__DBUSER.T_PREM_ARAP T, APP___CLM__DBUSER.T_CLAIM_CASE A
 WHERE A.CASE_NO = T.BUSINESS_CODE(+)
   AND T.FINISH_TIME IS NOT NULL
   AND A.CASE_NO  = #{business_code}
   AND T.ROLLBACK_UNIT_NUMBER(+) IS NULL 
   AND NOT EXISTS (SELECT 1
          FROM DEV_CAP.T_PREM_ARAP T1
         WHERE T1.ROLLBACK_UNIT_NUMBER = T.UNIT_NUMBER) 
   AND T.FEE_STATUS(+) <> 16 
   AND A.ACTUAL_PAY > 0 
	 ]]>
	</select>
	
	<!-- xuyz_wb 保项计算信息分页 add -->
	<select id="queryClaimLiabCalculatePage" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  select B.* from(
                select A.*,rownum RN from(
                    select cl.claim_liab_id,
                            cl.policy_code,
					        cl.busi_prod_code,
					        cl.busi_item_id,
					        cl.Liab_Name,
					        cl.liab_start_date,
					        cl.liab_end_date,
					        cl.calc_pay,
					        cl.adjust_pay,
					        cl.advance_pay,
					        cl.liab_conclusion,
					        (select cp.amount
					           from APP___CLM__DBUSER.T_CONTRACT_PRODUCT cp
					          where cp.item_id = cl.item_id
					          	and cp.policy_id = cl.policy_id
					          	and cp.busi_item_id= cl.busi_item_id
					            and cp.case_id = cl.case_id
					            and cp.cur_flag = 1) amount
					   from APP___CLM__DBUSER.T_CLAIM_LIAB cl
					  where cl.case_id = #{case_id}]]>
        <![CDATA[    ) A where rownum <= #{LESS_NUM} ]]>
     <![CDATA[ ) B where RN > #{GREATER_NUM}]]>
	</select>
	<select id="queryClaimLiabCalculateTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  SELECT COUNT(1)  FROM APP___CLM__DBUSER.T_CLAIM_LIAB CL WHERE CL.CASE_ID=#{case_id}
                   ]]>
		<include refid="CompersatePrintWhereCondition" />
	</select>
	<!-- 保项计算信息 end -->
	<!-- gaojh_wb 查询立案共享池 赔案基本信息 -->
	<select id="selectRegisterPoolInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select a.case_no,
		a.insured_id,
		c.customer_name,
		c.customer_certi_code,
		a.case_status,
		(select t.name from APP___CLM__DBUSER.T_CASE_STATUS t where t.code=a.case_status) case_status_name,
		a.organ_code,
		a.green_flag,
		a.signer_id,
		(select tuu.real_name from APP___CLM__DBUSER.T_UDMP_USER tuu where tuu.user_id=a.signer_id) trustee_name,
		a.sign_time,
		a.case_id,
		a.channel_code,
		a.REPORT_MODE,
		a.SERVCOM
		from APP___CLM__DBUSER.T_CLAIM_CASE a left join APP___CLM__DBUSER.T_CUSTOMER c on a.insured_id=c.customer_id where 1=1 
		]]>
		<if test=" case_no  != null and  case_no  != ''  and  case_no  != 'null' ">
			<![CDATA[ and a.case_no in (${case_no}) ]]>
		</if>
          <![CDATA[ order by a.case_no desc ]]>
	</select>
	<!-- huangjh_wb 查询立案个人池 -->
	<select id="findAllClaimCaseForSelf" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ 	SELECT A.CASE_NO,
       (SELECT B.CUSTOMER_NAME
          FROM APP___CLM__DBUSER.T_CUSTOMER B
         WHERE B.CUSTOMER_ID = A.INSURED_ID) CUSTOMER_NAME,
       (SELECT B.CUSTOMER_CERTI_CODE
          FROM APP___CLM__DBUSER.T_CUSTOMER B
         WHERE B.CUSTOMER_ID = A.INSURED_ID) CUSTOMER_CERTI_CODE,
       A.GREEN_FLAG,
       A.CASE_ID,
       A.ORGAN_CODE,
       A.ACCEPTOR_ID, 
        A.SIGN_TIME,
        A.CASE_STATUS,
        A.SIGNER_ID,
        A.CHANNEL_CODE,
        A.REPORT_MODE,
        A.AUDIT_DECISION,
        A.SERVCOM
  FROM APP___CLM__DBUSER.T_CLAIM_CASE A
 WHERE A.CASE_NO = #{case_no}
	ORDER BY A.CASE_ID   DESC
	 ]]>
	</select>
	<!-- liulei_wb 查询工作流所需要数据 -->
	<select id="findClaimBpmRequest" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,A.GREEN_FLAG,A.CASE_NO,A.ACCEPT_DECISION,A.ORGAN_CODE,A.CASE_FLAG,B.CUSTOMER_NAME,B.CUSTOMER_GENDER,B.CUSTOMER_ID,C.ACC_DATE,B.CUSTOMER_CERTI_CODE FROM APP___CLM__DBUSER.T_CLAIM_CASE A
					LEFT JOIN APP___CLM__DBUSER.T_CUSTOMER B ON A.INSURED_ID=B.CUSTOMER_ID
						LEFT JOIN APP___CLM__DBUSER.T_CLAIM_ACCIDENT C ON A.ACCIDENT_ID=C.ACCIDENT_ID WHERE 1=1 and a.case_id= #{case_id}]]>
	</select>
	<!-- liulei_wb 查询报案共享池数据 -->
	<select id="selectRepoterPoolInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		select a.case_no CASE_NO,
		c.customer_id CUSTOMER_ID,
		c.customer_name
		CUSTOMER_NAME,
		c.customer_certi_code CUSTOMER_ID_CODE,
		a.organ_code ORGAN_CODE,
		a.green_flag GREEN_FLAG,
		a.case_id CASE_ID,
		a.accept_time ACCEPT_TIME,
		(select b.user_name from APP___CLM__DBUSER.T_UDMP_USER b where a.rptr_id=b.user_id)
		USER_NAME
		from APP___CLM__DBUSER.T_CLAIM_CASE a,APP___CLM__DBUSER.T_CUSTOMER c where 1=1 and a.insured_id=c.customer_id(+)
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
			<![CDATA[
				and a.case_no = #{case_no}
			]]>
		</if>
        <![CDATA[ order by a.case_no desc ]]>
	</select>
	<!-- 查询报案共享池数据 xinghj_wb -->
	<select id="findReportPoolData" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT 	A.CASE_ID CASE_ID,
				A.CASE_NO CASE_NO,
				(SELECT C.CUSTOMER_ID
					FROM APP___CLM__DBUSER.T_CUSTOMER C
				WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_ID,
				(SELECT C.CUSTOMER_NAME
					FROM APP___CLM__DBUSER.T_CUSTOMER C
				WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_NAME,
				(SELECT C.CUSTOMER_CERTI_CODE
					FROM APP___CLM__DBUSER.T_CUSTOMER C
				WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_ID_CODE,
				A.ORGAN_CODE ORGAN_CODE,
				A.GREEN_FLAG GREEN_FLAG,
				A.ACCEPT_TIME ACCEPT_TIME,
				(SELECT B.USER_NAME
					FROM APP___CLM__DBUSER.T_UDMP_USER B
				WHERE A.RPTR_ID = B.USER_ID) USER_NAME,
				SC.CLAIM_TYPE
		FROM
				APP___CLM__DBUSER.T_CLAIM_CASE A,
				APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC
		WHERE
				A.CASE_ID = SC.CASE_ID
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
			<![CDATA[
				and a.case_no = #{case_no}
			]]>
		</if>
        <![CDATA[ order by a.case_no desc ]]>
	</select>
	<!-- fanjj_wb 查询判断是否自动发起调查所需参数 -->
	<select id="findBlackNameStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select cb.BLACK_NAME_STATUS 
  			from APP___CLM__DBUSER.t_claim_black_name cb
 			where cb.black_name = (select CUSTOMER_NAME
                          from APP___CLM__DBUSER.t_customer
                         where CUSTOMER_ID = ${customer_id})
  			 and cb.BLACK_CERTI_CODE =
       		(select CUSTOMER_ID
          		from APP___CLM__DBUSER.t_customer
        		 where CUSTOMER_ID = ${customer_id})]]>
	</select>
	<!-- liulei_wb 查询判断是否自动发起调查所需参数 -->
	<select id="findSurveyParameter" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select distinct CBP.BUSI_PROD_CODE,
       CA.ACC_REASON,
       CM.ORGAN_CODE,
       (select nvl(SUM(CLP.PRE_CALC_AMNT), 0)
          from APP___CLM__DBUSER.T_CLAIM_LIAB_PRE CLP
         where CLP.CASE_ID = C.CASE_ID) PRE_CALC_AMNT,
       CM.initial_validate_date,
       CM.VALIDDATE_DATE,
       (select SUM(CP.TOTAL_PREM_AF)
          from APP___CLM__DBUSER.T_CONTRACT_PRODUCT CP
         where CP.CASE_ID = C.CASE_ID
           AND CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
           AND CP.POLICY_ID = CM.POLICY_ID) TOTAL_PREM_AF,
       nvl(CU.BLACKLIST_FLAG, 0) BLACKLIST_FLAG,
       CSC.CLAIM_DATE,
       C.INSURED_ID
  from APP___CLM__DBUSER.T_CLAIM_CASE         C,
       APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD CBP,
       APP___CLM__DBUSER.T_CLAIM_ACCIDENT     CA,
       APP___CLM__DBUSER.T_CONTRACT_MASTER    CM,
       APP___CLM__DBUSER.T_CUSTOMER           CU,
       APP___CLM__DBUSER.T_CLAIM_SUB_CASE     CSC
 where C.CASE_ID = CBP.CASE_ID
   AND C.CASE_ID = CSC.CASE_ID
   AND C.INSURED_ID = CU.CUSTOMER_ID
   AND C.CASE_ID = CM.CASE_ID
   AND CBP.POLICY_ID = CM.POLICY_ID
   AND C.ACCIDENT_ID = CA.ACCIDENT_ID
   AND CBP.CUR_FLAG = 1
   AND CM.CUR_FLAG = 1
   AND C.CASE_ID  = ${case_id}
   AND CBP.BUSI_PROD_CODE IN (${busi_prod_code_list})]]>
	</select>
	<!-- liulei_wb 查询保额与保费比 -->
	<select id="findPremRatio" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select  sum(AMOUNT*UNIT)/sum(TOTAL_PREM_AF) PREM_RATIO  from APP___CLM__DBUSER.T_CONTRACT_PRODUCT A WHERE A.CASE_ID=${case_id}]]>
	</select>
	<!-- liulei_wb 查询理赔调查规则 -->
	<select id="findClaimSurveyRuleComp" resultType="java.util.Map"
		parameterType="java.util.Map"> 
		<![CDATA[  SELECT A.POL_YEAR, A.IS_BLACK, A.RATE, A.SURVEY_RULE_CODE, A.SURVEY_RULE_ID, 
			A.BEFORE_PAY,C.ORGAN_CODE,D.ACC_REASON,E.BUSI_PROD_CODE FROM APP___CLM__DBUSER.T_CLAIM_SURVEY_RULE A 
			LEFT JOIN APP___CLM__DBUSER.T_CLAIM_SURVEY_ORGAN C ON A.SURVEY_RULE_CODE=C.SURVEY_RULE_CODE 
			LEFT JOIN APP___CLM__DBUSER.T_CLAIM_SURVEY_ACC_REASON D ON A.SURVEY_RULE_CODE=D.SURVEY_RULE_CODE 
			LEFT JOIN APP___CLM__DBUSER.T_CLAIM_SURVEY_BUSI_PROD E ON A.SURVEY_RULE_CODE=E.SURVEY_RULE_CODE 
			WHERE ROWNUM <=  1000  ]]>

		<!-- 险种名称不为空 -->
		<if test="busi_prod_code != null and busi_prod_code != '' ">
				<![CDATA[ and (E.BUSI_PROD_CODE = #{busi_prod_code} OR E.BUSI_PROD_CODE IS NULL )]]>
		</if>
		<!-- 险种名称 为空 -->
		<if test="busi_prod_code == null or busi_prod_code == '' ">
				<![CDATA[ and E.BUSI_PROD_CODE IS NULL]]>
		</if>
		<!-- 出险原因 不为空 -->
		<if test="acc_reason != null">
				<![CDATA[ and (D.ACC_REASON = #{acc_reason} OR D.ACC_REASON IS NULL)]]>
		</if>
		<!-- 出险原因为空 -->
		<if test="acc_reason == null">
				<![CDATA[ and D.ACC_REASON IS NULL]]>
		</if>
		<!-- 黑名单 不为空 -->
		<if test="blacklist_flag != null">
				<![CDATA[ and (A.IS_BLACK = #{blacklist_flag} OR A.IS_BLACK IS NULL )]]>
		</if>
		<!-- 黑名单为空 -->
		<if test="blacklist_flag == null">
				<![CDATA[ and A.IS_BLACK IS NULL ]]>
		</if>
		<!-- 保单机构 不为空 -->
		<if test="organ_code != null and organ_code != '' ">
				<![CDATA[ and  (instr(#{organ_code}, C.ORGAN_CODE) > 0  OR C.ORGAN_CODE IS NULL) ]]>
		</if>
		<!-- 保单机构为空 -->
		<if test="organ_code == null or organ_code == '' ">
				<![CDATA[ and  C.ORGAN_CODE IS NULL ]]>
		</if>
		<!-- 赔案机构 不为空 -->
		<if test="case_organ_code != null and case_organ_code != '' ">
				<![CDATA[ and  (instr(#{case_organ_code}, A.ORGAN_CODE) > 0  OR A.ORGAN_CODE IS NULL) ]]>
		</if>
		<!-- 赔案机构为空 -->
		<if test="case_organ_code == null or case_organ_code == '' ">
				<![CDATA[ and  A.ORGAN_CODE IS NULL ]]>
		</if>
		<!-- 案件预算 不为空 -->
		<if test="before_pay != null">
				<![CDATA[ and  (A.BEFORE_PAY <= #{before_pay} OR A.BEFORE_PAY IS NULL) ]]>
		</if>
		<!-- 案件预算 为空 -->
		<if test="before_pay == null">
				<![CDATA[ and  A.BEFORE_PAY IS NULL ]]>
		</if>
		<!-- 保单年限不为空 -->
		<if test="pol_year != null">
				<![CDATA[ and  (A.POL_YEAR >= #{pol_year} OR A.POL_YEAR IS NULL )]]>
		</if>
		<!-- 保单年限 为空 -->
		<if test="pol_year == null">
				<![CDATA[ and  A.POL_YEAR IS NULL]]>
		</if>
		<!-- 赔付额与保费比例不为空 -->
		<if test="rate != null">
				<![CDATA[ and  (A.RATE <= #{rate} OR A.RATE IS NULL)]]>
		</if>
		<!-- 赔付额与保费比例 为空 -->
		<if test="rate == null">
				<![CDATA[ and  A.RATE IS NULL]]>
		</if>
		<![CDATA[ ORDER BY A.SURVEY_RULE_ID ]]>
	</select>
	<!-- gaojun_wb 赔案号下的应收应付总金额 -->
	<select id="queryfeeAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			select 
				sum(p.fee_amount) fee_amount
			from
      			APP___CLM__DBUSER.T_PREM_ARAP p 
      		where  p.business_code=#{business_code}
		]]>
	</select>
	<!-- gaojun_wb 赔案号下的所有代理人agentCode -->
	<select id="queryCaseStatusByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		  select 
	          t.code,t.name,c.case_no,c.audit_decision,c.insured_id
	      from
	          APP___CLM__DBUSER.T_CONTRACT_MASTER b ,APP___CLM__DBUSER.T_CASE_STATUS t,APP___CLM__DBUSER.T_CLAIM_CASE c
	      where c.case_id=b.case_id
	      and c.case_status = t.code 
	      and b.policy_code=#{policy_code}
	      and b.cur_flag='1'
	      and c.case_status not in('80','90')
		]]>
	</select>
	<select id="queryCaseStatusByPolicyCodes" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		  select 
	          t.code,t.name,c.case_no,c.audit_decision,p.claim_pay_id
	      from
	          APP___CLM__DBUSER.T_CONTRACT_MASTER b ,APP___CLM__DBUSER.T_CASE_STATUS t,APP___CLM__DBUSER.T_CLAIM_CASE c,APP___CLM__DBUSER.T_CLAIM_PAY p
	      where c.case_id=b.case_id
	      and c.case_status = t.code 
	      and c.case_id = p.case_id 
	      and b.policy_code = p.policy_code
	      and b.policy_code=#{policy_code}
	      and b.cur_flag='1'
	      and c.case_status = '80'
		]]>
	</select>
	<!-- gaojun_wb 赔案号下的所有代理人agentCode -->
	<select id="queryAgentCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			    select 
        distinct a.agent_code ,
        (select b.agent_name  from APP___CLM__DBUSER.T_AGENT b where b.agent_code=a.agent_code )  agent_name
      from  
        APP___CLM__DBUSER.T_CONTRACT_AGENT a 
      where 
        case_id=#{case_id}
		]]>
	</select>
	<!-- gaojun_wb 给付拒付清单分页信息 -->
	<select id="queryClaimPayRejectPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			select B.* from(
           select A.*,rownum RN from(
				  select   a.organ_code,
						   a.case_no,
					       a.case_flag,
					       a.rptr_name,
					       a.rptr_mp,
					       a.rptr_time,
					       a.sign_time,
					       a.signer_id,
					       a.registe_conf_time,
					       a.register_id,
					       a.auditor_id,
					       a.audit_decision,
					       a.approver_id,
					       a.approve_time,
					       a.trustee_name,
					       a.trustee_mp,
					       a.actual_pay,
					       a.reject_pay,
					       a.audit_reject_reason,
					       a.calc_pay,
					       a.end_case_time,
					       a.cure_hospital,
					       a.accident_detail,
					       b.accident_no,
					       b.acc_date,
					       b.acc_desc,
					       c.policy_code,
					       c.busi_prod_code,
					       c.claim_type,
					       c.liab_id,
					       c.liab_conclusion,
					       c.resist_flag,
					       d.customer_birthday,
					       d.customer_name,
					       d.offen_use_tel,
					       d.customer_gender,
					       e.validdate_date,
					       f.claim_date,
					       f.acc_reason,
					       g.treat_start,
					       g.treat_type,
					       h.pay_mode,
					       h.deriv_type,
					       h.fee_amount,
					       h.finish_time,
					       k.acc_result1,
					       k.acc_result2,
					       m.clmt_name,
					       m.clmt_insur_relation,
					       m.clmt_mp
				  from APP___CLM__DBUSER.T_CLAIM_CASE            a,
				       APP___CLM__DBUSER.T_CLAIM_ACCIDENT        b,
				       APP___CLM__DBUSER.T_CLAIM_LIAB            c,
				       APP___CLM__DBUSER.T_CUSROMER              d,
				       APP___CLM__DBUSER.T_CONTRACT_MASTER       e,
				       APP___CLM__DBUSER.T_CLAIM_SUB_CASE        f,
				       APP___CLM__DBUSER.T_CLAIM_BILL            g,
				       APP___CLM__DBUSER.T_PREM_ARAP             h,
				       APP___CLM__DBUSER.T_CLAIM_ACCIDENT_RESULT k,
				       APP___CLM__DBUSER.T_CLAIM_APPLICANT       m
				 where  a.accident_id = b.accident_id
				   and c.case_id = a.case_id
				   and a.insured_id = d.customer_id
				   and e.case_id = a.case_id
				   and f.case_id = a.case_id
				   and g.case_id = a.case_id
				   and h.business_code = a.case_no
				   and k.case_id = a.case_id
				   and m.case_id = a.case_id 
				   and e.cur_flag = 1]]>
		<if test=" statisticsOrganCode != null and statisticsOrganCode != '' ">
		    		<![CDATA[AND a.organ_code = #{statisticsOrganCode }]]>
		</if>
		<if test=" statisticsStartDate != null  ">
				    <![CDATA[AND a.end_case_time >= #{statisticsStartDate }]]>
		</if>
		<if test=" statisticsEndDate != null ">
				    <![CDATA[AND a.end_case_time <= #{statisticsEndDate }]]>
		</if>
				   <![CDATA[ ) A where rownum <= #{LESS_NUM} 
     		) B where RN > #{GREATER_NUM}
          ]]>
	</select>
	<!-- gaojun_wb 给付拒付清单数量 -->
	<select id="queryClaimPayRejectTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ 
			 select count(1)
				  from APP___CLM__DBUSER.T_CLAIM_CASE            a,
				       APP___CLM__DBUSER.T_CLAIM_ACCIDENT        b,
				       APP___CLM__DBUSER.T_CLAIM_LIAB            c,
				       APP___CLM__DBUSER.T_CUSTOMER              d,
				       APP___CLM__DBUSER.T_CONTRACT_MASTER       e,
				       APP___CLM__DBUSER.T_CLAIM_SUB_CASE        f,
				       APP___CLM__DBUSER.T_CLAIM_BILL            g,
				       APP___CLM__DBUSER.T_PREM_ARAP             h,
				       APP___CLM__DBUSER.T_CLAIM_ACCIDENT_RESULT k,
				       APP___CLM__DBUSER.T_CLAIM_APPLICANT       m
				 where a.accident_id = b.accident_id
				   and c.case_id = a.case_id
				   and a.insured_id = d.customer_id
				   and e.case_id = a.case_id
				   and f.case_id = a.case_id
				   and g.case_id = a.case_id
				   and h.business_code = a.case_no
				   and k.case_id = a.case_id
				   and m.case_id = a.case_id
				   and e.cur_flag = 1
          ]]>
		<if test=" statisticsOrganCode != null and statisticsOrganCode != '' ">
		    <![CDATA[AND a.organ_code = #{statisticsOrganCode }]]>
		</if>
		<if test=" statisticsStartDate != null  ">
		    <![CDATA[AND a.end_case_time >= #{statisticsStartDate }]]>
		</if>
		<if test=" statisticsEndDate != null ">
		    <![CDATA[AND a.end_case_time <= #{statisticsEndDate }]]>
		</if>
	</select>
	<!-- gaojun_wb 质检结果查询接口（查询总件数详细信息) -->
	<select id="queryInspectResultTotalNumberPage" parameterType="java.util.Map"
		resultType="java.util.Map">
   <![CDATA[
	 select B.* from(
          select A.*,rownum RN from( 
              select c.organ_code, count(1) total_number from APP___CLM__DBUSER.T_CLAIM_AFC_TASK t
              		left join APP___CLM__DBUSER.T_CLAIM_CASE c
                   		on c.case_id = t.case_id
	                where 1 = 1 and c.organ_code is not null 
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code like '${organ_code }%']]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
		<![CDATA[ group by c.organ_code ) A where rownum <= #{LESS_NUM} 
     		) B where RN > #{GREATER_NUM}
          ]]>
	</select>
	<!-- gaojun_wb 质检结果查询接口（查询总件数的数量) -->
	<select id="queryInspectResultTotalNumberTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
   <![CDATA[
	     select count(1) from
             ( select  count(1) total_number
                from APP___CLM__DBUSER.T_CLAIM_AFC_TASK t
              		left join APP___CLM__DBUSER.T_CLAIM_CASE c
                   		on c.case_id = t.case_id
	                where 1 = 1 and c.organ_code is not null 
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code like '${organ_code }%']]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
		<![CDATA[ group by c.organ_code )]]>
	</select>
	<!-- gaojun_wb 质检结果查询接口（查询抽检件数） -->
	<select id="queryInspectResultCheckNumber" parameterType="java.util.Map"
		resultType="java.lang.Integer">
   <![CDATA[
                    select count(1) from APP___CLM__DBUSER.T_CLAIM_AFC_TASK t
                    left join APP___CLM__DBUSER.T_CLAIM_CASE c
                         on t.case_id = c.case_id
                    left join APP___CLM__DBUSER.T_CLAIM_AFC_PLAN_RELA d
                         on t.plan_id = d.plan_id
                    where 1=1 and d.role_code = '1'
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
	</select>
	<!-- gaojun_wb 质检结果查询接口（查询不符合件数） -->
	<select id="queryInspectResultUnqualifiedNumber" parameterType="java.util.Map"
		resultType="java.lang.Integer">
   <![CDATA[
             select count(1) from APP___CLM__DBUSER.T_CLAIM_AFC_TASK t
                    left join APP___CLM__DBUSER.T_CLAIM_CASE c
                         on t.case_id = c.case_id
                    left join APP___CLM__DBUSER.T_CLAIM_AFC_PLAN_RELA d
                         on t.plan_id = d.plan_id
                    where 1=1 and d.role_code = '1'
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
		<if test=" check_conclusion != null ">
		    <![CDATA[AND t.check_conclusion = #{check_conclusion }]]>
		</if>
	</select>
	<!-- gaojun_wb 质检结果查询接口（表示质检(质检岗位是签收人)） -->
	<select id="queryInspectResultPage" parameterType="java.util.Map"
		resultType="java.util.Map">
   <![CDATA[
   		select B.* from(
           select A.*,rownum RN from( 
              select 
                      t.case_id,
                      t.case_no,
                      t.check_by,
                      t.check_reason,
                      t.check_date,
                      t.remark,
                      c.organ_code,
                      c.sign_time,
                      l.gist_id
  			from  APP___CLM__DBUSER.T_CLAIM_AFC_TASK t ,
  				  APP___CLM__DBUSER.T_CLAIM_AFC_DETAIL l,
  				  APP___CLM__DBUSER.T_CLAIM_AFC_PLAN_RELA d,
  				  APP___CLM__DBUSER.T_CLAIM_CASE c 
		    where c.case_id = t.case_id
            and t.task_id = l.task_id
            and d.plan_id = t.plan_id
            and  d.role_code='1'
            and t.qc_status = '2'
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code like '${organ_code }%']]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
		<![CDATA[ ) A where rownum <= #{LESS_NUM} 
     		) B where RN > #{GREATER_NUM}
          ]]>
	</select>

	<!-- gaojun_wb 质检结果查询接口（表示质检(质检岗位是签收人)的条数） -->
	<select id="queryInspectResultTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
   <![CDATA[
            select 
                   count(*)
  			from  APP___CLM__DBUSER.T_CLAIM_AFC_TASK t ,
  				  APP___CLM__DBUSER.T_CLAIM_AFC_DETAIL l,
  				  APP___CLM__DBUSER.T_CLAIM_AFC_PLAN_RELA d,
  				  APP___CLM__DBUSER.T_CLAIM_CASE c 
  		    left join APP___CLM__DBUSER.T_CLAIM_MEMO m 
  			on c.case_id = m.case_id
		    where c.case_id = t.case_id
            and t.task_id = l.task_id
            and d.plan_id = t.plan_id
            and  d.role_code='1'
            and t.qc_status = '2'
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code like '${organ_code }%']]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
	</select>
	<!-- gaojun_wb 签收共享池查询 -->
	<select id="selectSignPoolInfo" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[
		  SELECT
		    A.CASE_NO,
		    A.ORGAN_CODE,
		    A.GREEN_FLAG,
		    A.SIGNER_ID,
		    A.SIGN_TIME,
		    A.CASE_ID,
		    A.INSURED_ID,
		    (SELECT C.UN_CUSTOMER_CODE  FROM APP___CLM__DBUSER.T_CUSTOMER   C WHERE  A.INSURED_ID = C.CUSTOMER_ID ) UN_CUSTOMER_CODE,
	        (SELECT C.CUSTOMER_NAME  FROM APP___CLM__DBUSER.T_CUSTOMER   C WHERE  A.INSURED_ID = C.CUSTOMER_ID ) CUSTOMER_NAME,
	        (SELECT C.CUSTOMER_CERTI_CODE  FROM APP___CLM__DBUSER.T_CUSTOMER   C WHERE  A.INSURED_ID = C.CUSTOMER_ID ) CUSTOMER_CERTI_CODE,
		    A.RPTR_TIME,
		    A.RPTR_ID,
		    A.ACCEPTOR_ID,
		    A.ACCEPT_TIME,
		    A.CHANNEL_CODE,
		    (SELECT B.REAL_NAME FROM APP___CLM__DBUSER.T_UDMP_USER B WHERE A.RPTR_ID=B.USER_ID) RPTR_NAME
	     FROM  APP___CLM__DBUSER.T_CLAIM_CASE A  WHERE 1 = 1
	    ]]>
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
      <![CDATA[
       	AND A.CASE_NO IN (${case_no})
      ]]>
		</if>
	</select>

	<select id="findAllClaimCaseByCaseNo" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			 SELECT  A.CASE_NO,
               A.ORGAN_CODE,
               A.GREEN_FLAG,
               A.SIGNER_ID,
               A.SIGN_TIME,
               A.CASE_ID,
               A.INSURED_ID,
               (SELECT C.UN_CUSTOMER_CODE FROM APP___CLM__DBUSER.T_CUSTOMER C
                 WHERE A.INSURED_ID = C.CUSTOMER_ID) UN_CUSTOMER_CODE,
               (SELECT C.CUSTOMER_NAME  FROM APP___CLM__DBUSER.T_CUSTOMER C
                 WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_NAME,
               (SELECT C.CUSTOMER_CERTI_CODE FROM APP___CLM__DBUSER.T_CUSTOMER C
                 WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_CERTI_CODE,
               A.RPTR_TIME,
               A.RPTR_ID,
               A.ACCEPTOR_ID,
               A.ACCEPT_TIME,
               A.CHANNEL_CODE,
               SC.CLAIM_TYPE,
               (SELECT B.REAL_NAME FROM APP___CLM__DBUSER.T_UDMP_USER B
                 WHERE A.RPTR_ID = B.USER_ID) RPTR_NAME,
               (select OC.IMAGE_ISSUE_STATUS_CODE from APP___CLM__DBUSER.T_OUTSOURCE_CASE OC
                 WHERE A.CASE_ID = OC.CASE_ID) IMAGE_ISSUE_STATUS_CODE,
               OSI.OUTSOURCE_NAME 
        FROM
             APP___CLM__DBUSER.T_CLAIM_CASE A left join
             APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC on
             A.CASE_ID = SC.CASE_ID left join
             APP___CLM__DBUSER.T_OUTSOURCE_CASE OC on
             A.CASE_ID = OC.CASE_ID left join
             APP___CLM__DBUSER.T_OUTSOURCE_IFNO OSI on
             OSI.OUTSOURCE_ID =OC.OUTSOURCE_ID
		]]>
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
			<![CDATA[
				WHERE A.CASE_NO = #{case_no}
			]]>
		</if>
		<if test=" pool_flag  != null  and  pool_flag  != ''  and  pool_flag  != 'null' ">
			<![CDATA[
				AND A.CASE_STATUS != '21'
			]]>
		</if>
	</select>

	<select id="findAllAfterCheckClaimCaseByCaseNo" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			   SELECT  A.CASE_ID, 
			           A.CASE_NO,
                       A.ORGAN_CODE,
					   A.INSURED_ID,
					   (select B.customer_name from APP___CLM__DBUSER.t_customer B where B.CUSTOMER_ID = A.INSURED_ID) AS INSURED_NAME,
					   (SELECT C.CUSTOMER_CERTI_CODE FROM APP___CLM__DBUSER.T_CUSTOMER C WHERE A.INSURED_ID = C.CUSTOMER_ID) CUSTOMER_CERTI_CODE,
					   A.CASE_FLAG,
					   (select TCL.NAME from APP___CLM__DBUSER.T_CASE_LEVEL TCL WHERE TCL.CODE = A.CASE_FLAG)AS CASE_FLAG_NAME,
					   A.GREEN_FLAG,
					   (select TYN.TYPE_NAME from  APP___CLM__DBUSER.T_YES_NO TYN WHERE A.GREEN_FLAG = TYN.YES_NO ) AS GREEN_FLAG_NAME,
					   A.END_CASE_TIME,
					   A.AUDITOR_ID,
					   (select TUU.USER_NAME from APP___CLM__DBUSER.T_UDMP_USER TUU WHERE TUU.USER_ID = A.AUDITOR_ID) AS AUDITOR_NAME,
					   A.APPROVER_ID,
					   (select TUU.USER_NAME from APP___CLM__DBUSER.T_UDMP_USER TUU WHERE TUU.USER_ID = A.APPROVER_ID) AS APPROVER_NAME
              FROM  APP___CLM__DBUSER.T_CLAIM_CASE A
   			  WHERE    1=1
		]]>
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
			<![CDATA[
				AND A.CASE_NO = #{case_no}
			]]>
		</if>
	</select>

	<!-- renxd_wb 预付共享池查询 -->
	<select id="selectAdvancePoolInfo" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[ select
		a.case_no,
		a.organ_code,
		a.green_flag,
		a.signer_id,
		a.sign_time,
		a.case_id,
		a.insured_id,
		c.un_customer_code,
		c.customer_name,
		c.customer_certi_code,
		a.rptr_time,
		a.rptr_id,
  	    u.user_name,
		a.acceptor_id,
		a.accept_time
		from APP___CLM__DBUSER.T_CLAIM_CASE a  
	    left join APP___CLM__DBUSER.T_CUSTOMER c on a.insured_id = c.customer_id
	    left join	APP___CLM__DBUSER.T_UDMP_USER u on a.rptr_id = u.user_id
		where 1 = 1 ]]>
		<if test=" case_no != null and case_no != '' ">
		    <![CDATA[AND a.case_no = #{case_no }]]>
		</if>
	</select>

	<!-- gaojun_wb 通过机构code查询所有的人员 -->
	<select id="findUserInOrganCode" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[ SELECT A.DEPT_ID, A.UNIT_NAME, A.REAL_NAME, A.ID_TYPE, A.NEED_CHANGE_PASS, A.PHONE, A.ORGAN_CODE, 
			A.SHORT_NUM, A.ID_CARD, A.EMAIL, A.PASSWORD, A.ORGAN_ID, 
			A.USER_ID, A.DISABLE_DATE, A.USER_DISABLE, A.USER_TYPE, A.UNIQUEID, 
			A.PASSWORD_CHANGE, A.CLIENT_IP, A.LATEST_LOGIN_TIME, A.PARTY_ROLE, A.USER_NAME, A.INVALID_LOGIN, 
			A.CREATE_DATE, A.CHANGE_PWD_CAUSE, A.NAVIGATION_ID, A.UNIT_CODE FROM APP___CLM__DBUSER.T_UDMP_USER A WHERE 1 = 1  ]]>
		<if test=" organ_code != null and organ_code != '' ">
		    <![CDATA[AND A.ORGAN_CODE IN (${organ_code })]]>
		</if>
	</select>
	<!-- gaojun_wb 查询二级机构findFilialeOrg -->
	<select id="findFilialeOrg" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[ SELECT A.INVOICE_ORGAN_DESC, A.ORGAN_SHORTNAME, A.ORGAN_AREA_TYPE, A.TAX_AUTHORITY_CODE, A.FOUND_DATE, A.ORGAN_ADDRESS, A.ORGAN_ZIPCODE, 
			A.ORGAN_CODE, A.ORGAN_STATE, A.ORGAN_CITY, A.BACKOUTREPLYDATE, A.WEB_ADDRESS, 
			A.REGIONALISM_CODE, A.ORGAN_TYPE, A.IS_INVOICE_ORGAN, A.EMAIL, A.ORGAN_PHONE, A.OUTORGAN_CODE, A.ORGAN_ID, 
			A.ORGAN_TAX, A.SETUPREPLYDATE, A.REGION_ID, A.SATRAP_NAME, A.ORGAN_NAME, 
			A.ORGAN_PROVINCE FROM APP___CLM__DBUSER.T_UDMP_ORG A WHERE 1 = 1  
			AND A.ORGAN_CODE like '____']]>
	</select>
	<!-- gaojun_wb 调查结论查询 -->
	<select id="findSurveyConclusionPage" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[
   		select B.* from(
           select A.*,rownum RN from( 
              select a.apply_id,
			       a.survey_code,
			       a.biz_type,
			       (select cm.policy_code from  APP___CLM__DBUSER.T_CONTRACT_MASTER cm, APP___CLM__DBUSER.t_claim_case cc where cc.case_id=cm.case_id and cm.cur_flag=1
					 AND cc.case_no =a.case_no and rownum=1) policy_code,
				   (select cm.apply_code from  APP___CLM__DBUSER.T_CONTRACT_MASTER cm, APP___CLM__DBUSER.t_claim_case cc where cc.case_id=cm.case_id and cm.cur_flag=1
					 AND cc.case_no =a.case_no  and rownum=1) apply_code,
			       b.customer_id cus_id,
			       a.cs_accept_code,
			       a.survey_doc_id,
			       a.case_no,
			       a.survey_org,
			       c.positive_flag,
			       a.apply_date,
			       u.customer_vip,
			       c.finish_date
			  from APP___CLM__DBUSER.T_SURVEY_APPLY a 
			  left join 
			       APP___CLM__DBUSER.T_SURVEY_CONCLUSION c 
			  on a.apply_id = c.apply_id
			  left join 
			       APP___CLM__DBUSER.T_SURVEY_OBJECT b 
			  on a.apply_id = b.apply_id
			  left join APP___CLM__DBUSER.T_CUSTOMER u 
			  on b.customer_id = u.customer_id
			 where 1=1]]>
		<if test=" survey_code != null and survey_code != '' ">
		    <![CDATA[AND a.survey_code = #{survey_code }]]>
		</if>
		<if test=" apply_code != null and apply_code != '' ">
		    <![CDATA[ and exists (select 1 from  APP___CLM__DBUSER.T_CONTRACT_MASTER cm, APP___CLM__DBUSER.t_claim_case cc where cc.case_id=cm.case_id and cm.cur_flag=1
 			   AND cc.case_no =a.case_no and cm.apply_code=#{apply_code })]]>
		</if>
		<if test=" policy_code != null and policy_code != '' ">
		    <![CDATA[ and exists (select 1 from  APP___CLM__DBUSER.T_CONTRACT_MASTER cm, APP___CLM__DBUSER.t_claim_case cc where cc.case_id=cm.case_id and cm.cur_flag=1
 			   AND cc.case_no =a.case_no and cm.policy_code=#{policy_code })]]>
		</if>
		<if test=" customer_id != null and customer_id != '' ">
		    <![CDATA[AND b.customer_id = #{customer_id }]]>
		</if>
		<if test=" cs_accept_code != null and cs_accept_code != '' ">
		    <![CDATA[AND a.cs_accept_code = #{cs_accept_code }]]>
		</if>
		<if test=" survey_doc_id != null ">
		    <![CDATA[AND a.survey_doc_id = #{survey_doc_id }]]>
		</if>
		<if test=" case_no != null and case_no != '' ">
		    <![CDATA[AND a.case_no = #{case_no }]]>
		</if>
		<if test=" survey_org != null and survey_org != '' ">
		    <![CDATA[AND a.survey_org = #{survey_org }]]>
		</if>
		<if test=" biz_Type_Str != null and biz_Type_Str != '' ">
		    <![CDATA[AND a.biz_type in (${biz_Type_Str })]]>
		</if>
		<if test=" apply_date != null and apply_date != '' ">
		    <![CDATA[AND a.apply_date >= #{apply_date }]]>
		</if>
		<if test=" apply_date_two != null and apply_date_two != '' ">
		    <![CDATA[AND a.apply_date <= #{apply_date_two }]]>
		</if>
		<![CDATA[ ) A where rownum <= #{LESS_NUM} 
     		) B where RN > #{GREATER_NUM}
          ]]>
	</select>
	<!-- gaojun_wb 调查结论查询（个数） -->
	<select id="findSurveyConclusionTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
   <![CDATA[
              select count(1)
			  from APP___CLM__DBUSER.T_SURVEY_APPLY a 
			  left join 
			       APP___CLM__DBUSER.T_SURVEY_CONCLUSION c 
			  on a.apply_id = c.apply_id
			  left join 
			       APP___CLM__DBUSER.T_SURVEY_OBJECT b 
			  on a.apply_id = b.apply_id
			  left join APP___CLM__DBUSER.T_CUSTOMER u 
			  on b.customer_id = u.customer_id
			 where 1=1]]>
		<if test=" apply_code != null and apply_code != '' ">
		    <![CDATA[ and exists (select 1 from  APP___CLM__DBUSER.T_CONTRACT_MASTER cm, APP___CLM__DBUSER.t_claim_case cc where cc.case_id=cm.case_id and cm.cur_flag=1
 			   AND cc.case_no =a.case_no and cm.apply_code=#{apply_code })]]>
		</if>
		<if test=" policy_code != null and policy_code != '' ">
		    <![CDATA[ and exists (select 1 from  APP___CLM__DBUSER.T_CONTRACT_MASTER cm, APP___CLM__DBUSER.t_claim_case cc where cc.case_id=cm.case_id and cm.cur_flag=1
 			   AND cc.case_no =a.case_no and cm.policy_code=#{policy_code })]]>
		</if>
		<if test=" customer_id != null and customer_id != '' ">
		    <![CDATA[AND b.customer_id = #{customer_id }]]>
		</if>
		<if test=" cs_accept_code != null and cs_accept_code != '' ">
		    <![CDATA[AND a.cs_accept_code = #{cs_accept_code }]]>
		</if>
		<if test=" survey_doc_id != null ">
		    <![CDATA[AND a.survey_doc_id = #{survey_doc_id }]]>
		</if>
		<if test=" case_no != null and case_no != '' ">
		    <![CDATA[AND a.case_no = #{case_no }]]>
		</if>
		<if test=" survey_org != null and survey_org != '' ">
		    <![CDATA[AND a.survey_org = #{survey_org }]]>
		</if>
		<if test=" biz_Type_Str != null and biz_Type_Str != '' ">
		    <![CDATA[AND a.biz_type in (${biz_Type_Str })]]>
		</if>
		<if test=" apply_date != null and apply_date != '' ">
		    <![CDATA[AND a.apply_date >= #{apply_date }]]>
		</if>
		<if test=" apply_date_two != null and apply_date_two != '' ">
		    <![CDATA[AND a.apply_date <= #{apply_date_two }]]>
		</if>
	</select>
	<!-- gaojun_wb 通过出险人查询既往赔案信息 p.product_abbr_name, 保险名有人提bug删除 to_char(substr(wm_concat( m.policy_code),1,3000)) policy_code, -->
	<select id="queryClaimHisInfo" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	select B.* from(
           select A.*,rownum RN from(   
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.insured_id,
		            c.case_status,
		            c.accept_time,
		            c.sign_time,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            c.end_case_time,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            l.actual_pay,       
		            m.policy_code policy_code,   
		            m.policy_type,
		            l.liab_id,
		            a.acc_date,
		            l.busi_prod_code,
		            (select MAX(q.audit_date) from APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT q where q.case_id = c.case_id)as back_audit_date,
		          	(l.actual_pay+nvl(l.ADVANCE_PAY,0))   as actualPayTatol,
		          	 l.LIAB_CONCLUSION
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CLAIM_LIAB l,
		            APP___CLM__DBUSER.T_CONTRACT_MASTER    m,
		            APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		      	and c.case_id = l.case_id
		        and m.policy_code = d.policy_code
		        and m.policy_code = l.policy_code
		        and m.cur_flag = d.cur_flag
		        and m.cur_flag='1'  ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status = #{case_status}]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[  AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>

		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code = #{policy_code_str} ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.CLAIM_LIAB_ID = L.CLAIM_LIAB_ID AND T.BUSI_ITEM_ID=#{busi_item_id_str}) ]]>
		</if>  
		 
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.insured_id,
                c.case_status,
                c.accept_time,
		        c.sign_time,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,
                c.approver_id,
                c.end_case_time,
                l.actual_pay,
                m.policy_code,
		        m.policy_type,
		        l.liab_id,
		        l.busi_prod_code,
		        a.acc_date,
		        l.ADVANCE_PAY,
		        l.LIAB_CONCLUSION
		        order by a.acc_date desc
		 ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM} and rn<200]]>
	</select>
	<!-- gaojun_wb 通过出险人查询既往赔案信息个数 p.product_abbr_name, to_char(substr(wm_concat( m.policy_code),1,3000)) policy_code, -->
	<select id="queryClaimHisInfoTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   select count(1) from(
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.insured_id,
		            c.case_status,
		            c.accept_time,
		            c.sign_time,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            c.end_case_time,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            l.actual_pay,       
		             m.policy_code policy_code, 
		             m.policy_type,
		             l.liab_id,
		            a.acc_date,
		            l.busi_prod_code,
		            (select MAX(q.audit_date) from APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT q where q.case_id = c.case_id)as back_audit_date,
		            l.liab_conclusion 
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CLAIM_LIAB l,
		            APP___CLM__DBUSER.T_CONTRACT_MASTER    m,
		            APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		      	and c.case_id = l.case_id
		        and m.policy_code = d.policy_code
		        and m.policy_code = l.policy_code
		        and m.cur_flag = d.cur_flag
		        and m.cur_flag='1' ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status =#{case_status}]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[ AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code =#{policy_code_str} ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.CLAIM_LIAB_ID = L.CLAIM_LIAB_ID AND T.BUSI_ITEM_ID=#{busi_item_id_str}) ]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.insured_id,
                c.case_status,
                c.accept_time,
		        c.sign_time,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,
                c.approver_id,
                c.end_case_time,
                l.actual_pay,
                m.policy_code,
		        m.policy_type,
		        l.liab_id,
		        l.busi_prod_code,
		        a.acc_date,
		        l.liab_conclusion
      ) ]]>
	</select>
	<!-- gaojun_wb 通过出险人号查询出险人历史信息(分页) -->
	<select id="findClaimByInsuredIdPage" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  select B.* from(
           select A.*,rownum RN from(
              select c.case_id,
                 c.accident_id,
                 c.case_no,
                 c.insured_id,
                 c.green_flag,
                 c.acceptor_id,
                 c.organ_code,
                 c.rptr_time,
                 c.rptr_id,
                 c.accept_time,
                 c.case_status,
                 m.customer_name,
                 m.customer_certi_code
            from APP___CLM__DBUSER.T_CLAIM_CASE c, APP___CLM__DBUSER.T_CUSTOMER m
            where c.insured_id = m.customer_id
                 and c.case_status in ('10', '20')
                 and c.insured_id = #{insured_id}
           ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM}]]>
	</select>

	<!-- gaojun_wb 通过出险人号查询出险人历史信息(分页) -->
	<select id="findClaimByInsuredIdTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  select count(1)
          from APP___CLM__DBUSER.T_CLAIM_CASE c, APP___CLM__DBUSER.T_CUSTOMER m
          where c.insured_id = m.customer_id
            and c.case_status in ('10', '20')
            and c.insured_id = #{insured_id} 
          ]]>
	</select>
	<!-- gaojun_wb 通过赔案号查询出单证信息(分页) -->
	<select id="findClaimDocuments" parameterType="java.util.Map"
		resultType="java.util.Map">
		<!-- <![CDATA[ select n.doc_list_id, n.template_code, n.reprint_times, 
			n.print_time, p.template_name from t_document t,t_document_print n,t_document_template 
			p where t.doc_list_id=n.doc_list_id and n.template_code=p.template_code]]> -->
        <![CDATA[ SELECT T.DOC_LIST_ID,
        T.DOCUMENT_NO,
        T.DOCUMENT_NAME,
        T.TEMPLATE_CODE,
        T.ORGAN_CODE,
        T.BUSS_ID,
        T.BUSS_CODE,
        T.POLICY_ID,
        T.POLICY_CODE,
        T.STATUS,
        T.OVERDUE_TIME,
        T.REPLY_DAYS,
        T.SUPPLEMENT_FLAG,
        T.REPRINT_TIMES,
        T.OVERDUE_DOCUMENT_NO,
        T.SEND_OBJ_TYPE,
        T.BUSS_SOURCE_CODE,
        T.SEND_OBJ_ID,
        T.CREATE_TIME,
        T.CREATE_BY,
        T.SEND_TIME,
        T.SEND_BY,
        T.PRINT_TIME,
        T.PRINT_BY,
        T.SCAN_TIME,
        T.SCAN_BY,
        T.REPLY_TIME,
        T.REPLY_BY,
        T.CLOSE_TIME,
        T.CLOSE_BY,
        T.IS_LINK,
        T.IS_MERGER,
        T.CLOB_ID,
        T.REPLY_CONCLUSION,
        T.REPLY_REMARK,
        T.INSERT_BY,
        T.INSERT_TIME,
        T.INSERT_TIMESTAMP,
        T.UPDATE_BY,
        T.UPDATE_TIME,
        T.UPDATE_TIMESTAMP FROM APP___CLM__DBUSER.T_DOCUMENT T, APP___CLM__DBUSER.T_DOCUMENT_TEMPLATE P where T.template_code = P.template_code ]]>
		<if
			test=" buss_code  != null  and  buss_code  != ''  and  buss_code  != 'null' ">
         		   <![CDATA[and t.buss_code=#{buss_code}]]>
		</if>
	</select>
	<!-- gaojun_wb 多条件查询历史签收任务管理信息 -->
	<select id="findClaimTaskManagePage" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  
    	select B.* from(
           select A.*,rownum RN from(
    			select 
			       a.case_id,
			       a.case_no,
			       a.green_flag,
			       a.case_status,
			       a.sign_time,
			       a.registe_time,
			       a.registe_conf_time,
  				   a.audit_time,
  				   a.approve_time,
  				   a.end_case_time,
			       r.customer_id,
			       r.customer_name
 			  from
 			  	 APP___CLM__DBUSER.T_CLAIM_CASE a ,APP___CLM__DBUSER.T_CUSTOMER r
 			  where 
      			 a.insured_id = r.customer_id ]]>
		<if test="checkproplem_str != null and checkproplem_str != ''">
      		<![CDATA[ and a.case_no in (${checkproplem_str}) ]]>
		</if>
		<if test="not_checkproplem_str != null and not_checkproplem_str !=''">
      		<![CDATA[ and a.case_no not in (${not_checkproplem_str})  ]]>
		</if>
		<if test="case_no !=null and case_no !='' and case_no !='null'">
      		<![CDATA[and a.case_no = #{case_no}]]>
		</if>
		<if test="signer_id !=null ">
      		<![CDATA[and a.signer_id = #{signer_id}]]>
		</if>
		<if
			test="customer_name !=null and customer_name !='' and customer_name !='null'">
      		<![CDATA[and r.customer_name = #{customer_name}]]>
		</if>
		<if test="customer_gender !=null">
      		<![CDATA[and r.customer_gender = #{customer_gender}]]>
		</if>
		<if
			test="customer_certi_code !=null  and customer_certi_code != ''  and customer_certi_code != 'null'">
      		<![CDATA[and r.customer_certi_code = #{customer_certi_code}]]>
		</if>
		<if
			test="case_status !=null and case_status !='' and case_status != 'null'">
      		<![CDATA[and a.case_status = #{case_status}]]>
		</if>
		<if test="case_status ==null or case_status =='' or case_status == 'null'">
      		<![CDATA[and a.case_status not in('10','20')]]>
		</if>
		<if test="green_flag !=null and case_flag!=null">
      		<![CDATA[and a.green_flag in(#{green_flag},#{case_flag})]]>
		</if>
		<if test="green_flag !=null and case_flag==null">
			<![CDATA[and a.green_flag =#{green_flag}]]>
		</if>
		<if test="case_flag !=null and green_flag==null">
			<![CDATA[and a.green_flag =#{case_flag}]]>
		</if>
		<if test="sign_time !=null and door_sign_time !=null">
      		<![CDATA[and a.sign_time between  #{sign_time} and #{door_sign_time}]]>
		</if>
		<if test="sign_time !=null and door_sign_time ==null">
      		<![CDATA[and a.sign_time >= #{sign_time}]]>
		</if>
		<if test="sign_time ==null and door_sign_time !=null">
      		<![CDATA[and a.sign_time <= #{door_sign_time} ]]>
		</if>
      		<![CDATA[ ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM}
          ]]>
	</select>
	<!-- gaojun_wb 多条件查询历史签收任务管理信息个数 -->
	<select id="findClaimTaskManageTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  select count(1)
           from
       			APP___CLM__DBUSER.T_CLAIM_CASE a ,APP___CLM__DBUSER.T_CUSTOMER r
		   where 
      			 a.insured_id = r.customer_id]]>
		<if test="checkproplem_str != null and checkproplem_str != ''">
      		<![CDATA[ and a.case_no in (${checkproplem_str}) ]]>
		</if>
		<if test="not_checkproplem_str != null and not_checkproplem_str !=''">
      		<![CDATA[ and a.case_no not in (${not_checkproplem_str})  ]]>
		</if>
		<if test="case_no !=null and case_no !='' and case_no !='null'">
      		<![CDATA[and a.case_no = #{case_no}]]>
		</if>
		<if test="case_status ==null or case_status =='' or case_status == 'null'">
      		<![CDATA[and a.case_status not in('10','20')]]>
		</if>
		<if test="signer_id !=null ">
      		<![CDATA[and a.signer_id = #{signer_id}]]>
		</if>
		<if
			test="customer_name !=null and customer_name !='' and customer_name !='null'">
      		<![CDATA[and r.customer_name = #{customer_name}]]>
		</if>
		<if test="customer_gender !=null">
      		<![CDATA[and r.customer_gender = #{customer_gender}]]>
		</if>
		<if
			test="customer_certi_code !=null  and customer_certi_code != ''  and customer_certi_code != 'null'">
      		<![CDATA[and r.customer_certi_code = #{customer_certi_code}]]>
		</if>
		<if
			test="case_status !=null and case_status !='' and case_status != 'null'">
      		<![CDATA[and a.case_status = #{case_status}]]>
		</if>
		<if test="case_status ==null or case_status =='' or case_status == 'null'">
      		<![CDATA[and a.case_status not in('10','20')]]>
		</if>
		<if test="green_flag !=null and case_flag!=null">
      		<![CDATA[and a.green_flag in(#{green_flag},#{case_flag})]]>
		</if>
		<if test="green_flag !=null and case_flag==null">
			<![CDATA[and a.green_flag =#{green_flag}]]>
		</if>
		<if test="case_flag !=null and green_flag==null">
			<![CDATA[and a.green_flag =#{case_flag}]]>
		</if>
		<if test="sign_time !=null and door_sign_time !=null">
      		<![CDATA[and a.sign_time between  #{sign_time} and #{door_sign_time}]]>
		</if>
		<if test="sign_time !=null and door_sign_time ==null">
      		<![CDATA[and a.sign_time >= #{sign_time}]]>
		</if>
		<if test="sign_time ==null and door_sign_time !=null">
      		<![CDATA[and a.sign_time <= #{door_sign_time} ]]>
		</if>
	</select>
	<!-- gaojun_wb 多条件查询质检信息个数 -->
	<select id="findClaimAfcTaskTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  SELECT COUNT(1) FROM 
    		APP___CLM__DBUSER.T_CLAIM_AFC_TASK  K, 
     		APP___CLM__DBUSER.T_CLAIM_CASE C     				
     		WHERE 
      			  K.CASE_NO = C.CASE_NO ]]>
		<if test="case_no !=null and case_no !='' and case_no !='null'">
          <![CDATA[AND C.CASE_NO = #{case_no}]]>
		</if>
		<if test="plan_id !=null">
          <![CDATA[AND K.PLAN_ID = #{plan_id}]]>
		</if>
		<if test="check_by !=null">
          <![CDATA[AND K.CHECK_BY = #{check_by}]]>
		</if>
		<if test="make_date !=null and check_date !=null">
          <![CDATA[AND MAKE_DATE BETWEEN  #{make_date} AND #{check_date}]]>
		</if>
		<if test="make_date !=null and check_date ==null">
          <![CDATA[AND MAKE_DATE >= #{make_date}]]>
		</if>
		<if test="make_date ==null and check_date !=null">
          <![CDATA[AND MAKE_DATE <= #{check_date} ]]>
		</if>
		<if test="qc_status !=null">
          <![CDATA[AND K.QC_STATUS = #{qc_status} ]]>
		</if>
		<if test="organ_code !=null and organ_code !=''">
          <![CDATA[AND C.ORGAN_CODE = #{organ_code} ]]>
		</if>
	</select>
	<!-- gaojun_wb 多条件查询质检信息个数 -->
	<select id="findClaimAfcTaskPageData" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
      select B.* from(
           select A.*,rownum RN from( 
        select
           k.case_no,
           k.qc_status,
           S.organ_code,
           k.check_by,
           S.AUDITOR_ID,
           S.APPROVER_ID
             from
             APP___CLM__DBUSER.T_CLAIM_AFC_TASK  k,APP___CLM__DBUSER.T_CLAIM_CASE S
       where S.CASE_NO=K.CASE_NO]]>
		<if test="case_no !=null and case_no !='' and case_no !='null'">
          <![CDATA[and k.case_no = #{case_no}]]>
		</if>
		<if test="plan_id !=null">
          <![CDATA[and k.plan_id = #{plan_id}]]>
		</if>
		<if test="check_by !=null">
          <![CDATA[and k.check_by = #{check_by}]]>
		</if>
		<if test="make_date !=null and check_date !=null">
          <![CDATA[and k.make_date between  #{make_date} and #{check_date}]]>
		</if>
		<if test="make_date !=null and check_date ==null">
          <![CDATA[and k.make_date >= #{make_date}]]>
		</if>
		<if test="make_date ==null and check_date !=null">
          <![CDATA[and k.make_date <= #{check_date} ]]>
		</if>
		<if test="qc_status !=null">
          <![CDATA[and k.qc_status = #{qc_status} ]]>
		</if>
		<if test="organ_code !=null and organ_code !=''">
          <![CDATA[and S.organ_code = #{organ_code} ]]>
		</if>
    <![CDATA[  ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM}]]>
	</select>
	<!-- gaojun_wb 多条件查询质检信息个数 -->
	<select id="findClaimAfcTaskPageTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  
        select
          count(1)
             from
             APP___CLM__DBUSER.T_CLAIM_AFC_TASK  k,APP___CLM__DBUSER.T_CLAIM_CASE S
       where S.CASE_NO=K.CASE_NO]]>
		<if test="case_no !=null and case_no !='' and case_no !='null'">
          <![CDATA[and k.case_no = #{case_no}]]>
		</if>
		<if test="plan_id !=null">
          <![CDATA[and k.plan_id = #{plan_id}]]>
		</if>
		<if test="check_by !=null">
          <![CDATA[and k.check_by = #{check_by}]]>
		</if>
		<if test="make_date !=null and check_date !=null">
          <![CDATA[and k.make_date between  #{make_date} and #{check_date}]]>
		</if>
		<if test="make_date !=null and check_date ==null">
          <![CDATA[and k.make_date >= #{make_date}]]>
		</if>
		<if test="make_date ==null and check_date !=null">
          <![CDATA[and k.make_date <= #{check_date} ]]>
		</if>
		<if test="qc_status !=null">
          <![CDATA[and k.qc_status = #{qc_status} ]]>
		</if>
		<if test="organ_code !=null and organ_code !=''">
      		<![CDATA[and k.organ_code = #{organ_code} ]]>
		</if>
	</select>
	<!-- gaojun_wb 通过机构code查询机构信息模糊查询 -->
	<select id="findOrganNameByCode" parameterType="java.util.Map"
		resultType="java.util.Map"> 
		<![CDATA[
		SELECT D.* FROM( SELECT C.*,ROWNUM RN FROM(
		]]>
   <![CDATA[ SELECT A.ORGAN_CODE , A.ORGAN_NAME FROM APP___CLM__DBUSER.T_UDMP_ORG A WHERE 1 = 1  ]]>
		<if test=" organ_code != null and organ_code != '' ">
		    <![CDATA[AND A.ORGAN_CODE LIKE SUBSTR(#{organ_code }, 0, #{length})||'%' AND A.ORGAN_CODE != #{organ_code } ]]>
		</if>
		<![CDATA[
		) C WHERE ROWNUM <= #{LESS_NUM}) D where D.rn >= #{GREATER_NUM}
		]]>
	</select>
	<!--xuyz_wb 出具审核结论 查询领款人受益人信息 如果预付标识为1 查预付信息 -->
	<select id="selectBeneAndPayeeMsgs" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID case_id, 
		                 A.POLICY_ID policy_id,
		                 A.BUSI_ITEM_ID busi_item_id, 
		                 A.PAY_AMOUNT,
		                 A.PAY_DENO pay_deno,
                         A.PAY_MOLE pay_mole,
		                 B.BENE_NAME bene_name,
		                 B.BENE_RELATION bene_relation,
		                 B.BENE_BIRTH bene_birth,
		                 C.PAYEE_NAME payee_name,
		                 C.ACCOUNT_NO account_no,
		                 c.account_name,
		                 B.BENE_ID bene_id,
		                 D.name pay_mode,
		                 C.PAYEE_ID payee_id,
		                 C.PAYEE_SEX payee_sex,
		                 C.PAYEE_BIRTH payee_birth,
		                 C.PAYEE_CERTI_TYPE payee_certi_type,
		                 C.PAYEE_CERTI_NO payee_certi_no,
		                 C.PAYEE_RELATION payee_relation,
		                 C.LEGAL_PERSON_ID,
               b.bene_sex,
               b.bene_certi_type,
               b.bene_certi_no
		                 FROM APP___CLM__DBUSER.T_CLAIM_PAY  A 
		                 left join APP___CLM__DBUSER.T_CLAIM_BENE B on A.BENE_ID=B.BENE_ID
		                 left join APP___CLM__DBUSER.T_CLAIM_PAYEE C on A.PAYEE_ID=C.PAYEE_ID
		                 left join APP___CLM__DBUSER.T_PAY_MODE D on C.PAY_MODE=D.CODE
		                 WHERE 1=1 AND A.CASE_ID=B.CASE_ID AND A.CASE_ID=C.CASE_ID
        ]]>
		<if test="case_id !=null and case_id !=''"> 
        <![CDATA[    AND  A.CASE_ID = #{case_id} ]]>
		</if>
		<if test="advance_flag !=null and advance_flag !=''"> 
        <![CDATA[    AND  a.advance_flag = #{advance_flag} ]]>
		</if>
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID desc]]>
	</select>

	<!--huangjh_wb 理赔关怀查询受益人信息 -->
	<select id="queryBeneForClaimCare" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT distinct A.CASE_ID case_id, 
                     B.BENE_NAME bene_name,
                     B.BENE_RELATION bene_relation,
                     B.BENE_BIRTH bene_birth,
                     B.BENE_ID bene_id,
                     b.bene_sex,
                     b.bene_certi_type,
                     b.bene_certi_no,
                     b.bene_no
                     FROM APP___CLM__DBUSER.T_CLAIM_PAY  A 
                     left join APP___CLM__DBUSER.T_CLAIM_BENE B on A.BENE_ID=B.BENE_ID
                     WHERE 1=1 AND A.CASE_ID=B.CASE_ID
        ]]>
		<if test="case_id !=null and case_id !=''"> 
        <![CDATA[    AND  A.CASE_ID = #{case_id} ]]>
		</if>
	</select>
	<!--xuyz_wb 查询 医疗 赔付明细信息 -->
	<select id="queryPayDetailMedical" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   SELECT distinct A.case_id case_id, 
                     A.policy_id policy_id,
                     A.busi_item_id busi_item_id,
                     A.policy_code policy_code,
                     A.actual_pay actual_pay,
                     A.advance_pay advance_pay,
                     A.liab_name liab_name,
                     A.clm_remark clm_remark,
                     A.product_id product_id,
                     B.product_name_sys product_name_sys,
                     C.treat_start treat_start,
                     C.treat_end treat_end,
                     C.sum_amount sum_amount,
                     D.other_type other_type,
                     D.own_expense own_expense,
                     E.fee_amount fee_amount,
                     A.liability_status liability_state    FROM APP___CLM__DBUSER.T_CLAIM_LIAB A 
                     , APP___CLM__DBUSER.T_BUSINESS_PRODUCT B
                     , APP___CLM__DBUSER.T_CLAIM_BILL C
                     , APP___CLM__DBUSER.T_CLAIM_BILL_PAID D
                     , APP___CLM__DBUSER.T_CLAIM_ADJUST E  
                     , APP___CLM__DBUSER.T_CLAIM_LIAB_BILL_RELATION R
                     where  a.busi_prod_code = B.product_code_sys (+)
                       and a.case_id = r.case_id(+)
                       and a.claim_liab_id=r.claim_liab_id(+)
                       and r.bill_id=c.bill_id (+)
                        and a.case_id = d.case_id (+)
                        and a.case_id = e.case_id (+)
                        and   A.claim_type in('08')  
        ]]>
		<if test="case_id !=null and case_id !=''"> 
        <![CDATA[    AND  A.case_id = #{case_id} ]]>
		</if>
		<![CDATA[ ORDER BY A.case_id desc]]>
	</select>

	<!--xuyz_wb 查询 非医疗 赔付明细信息 -->
	<select id="queryPayDetailUnMedical" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[    SELECT distinct A.case_id case_id, 
                     A.policy_id policy_id,
                     A.busi_item_id busi_item_id,
                     A.policy_code policy_code,
                     A.actual_pay actual_pay,
                     A.advance_pay advance_pay,
                     A.liab_name liab_name,
                     A.clm_remark clm_remark,
                     A.product_id product_id,
                     B.product_name_sys product_name_sys,
                     C.treat_start treat_start,
                     C.treat_end treat_end,
                     C.sum_amount sum_amount,
                     D.other_type other_type,
                     D.own_expense own_expense,
                     E.fee_amount fee_amount,
                     A.liability_status liability_state    FROM APP___CLM__DBUSER.T_CLAIM_LIAB A 
                     , APP___CLM__DBUSER.T_BUSINESS_PRODUCT B
                     , APP___CLM__DBUSER.T_CLAIM_BILL C
                     , APP___CLM__DBUSER.T_CLAIM_BILL_PAID D
                     , APP___CLM__DBUSER.T_CLAIM_ADJUST E  
                     , APP___CLM__DBUSER.T_CLAIM_LIAB_BILL_RELATION R
                     where  a.busi_prod_code = B.product_code_sys (+)
                       and a.case_id = r.case_id(+)
                       and a.claim_liab_id=r.claim_liab_id(+)
                       and r.bill_id=c.bill_id (+)
                        and a.case_id = d.case_id (+)
                        and a.case_id = e.case_id (+)
                        and   A.claim_type  not in('08')  
        ]]>
		<if test="case_id !=null and case_id !=''"> 
        <![CDATA[    AND  A.case_id = #{case_id} ]]>
		</if>
		<![CDATA[ ORDER BY A.case_id desc]]>
	</select>

	<!--sunjl_wb 查询事故日期 -->
	<select id="queryAccidentCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
			   select distinct ca.ACCIDENT_ID,
					ca.ACCIDENT_NO,
					ca.INSURED_ID,
					ca.ACC_DATE,
					ca.ACC_REASON,
					ca.ACC_PROVINCE,
					ca.ACC_CITY,
					ca.ACC_DISTREACT,
					ca.ACC_COUNTRY_CODE,
					ca.ACC_STREET,
					ca.ACC_DESC,
					cc.case_no,
					cc.accident_detail,
					cc.cure_hospital,
					cc.cure_status,
					cc.serious_disease
					     from APP___CLM__DBUSER.T_CLAIM_ACCIDENT ca
					     left join APP___CLM__DBUSER.T_CLAIM_CASE cc
					       on cc.accident_id = ca.accident_id
					     left join APP___CLM__DBUSER.T_DISTRICT cd
					       on ca.ACC_PROVINCE = cd.code or ca.ACC_CITY = cd.code or ca.ACC_DISTREACT = cd.CODE
					       where 1=1 AND ca.insured_id=#{insured_id}]]>
		<if test="acc_reason !=null and acc_reason !=''"> 
        	<![CDATA[  and ca.ACC_REASON = #{acc_reason} ]]>
		</if>
		<if test="acc_date !=null and acc_date!=''"> 
        	<![CDATA[  and ca.ACC_DATE = #{acc_date} ]]>
		</if>
		<if test="acc_city !=null and acc_city!=''"> 
        	<![CDATA[and concat(cd.name,ca.acc_street)  like '%${acc_city}%']]>
		</if>
		
		<![CDATA[ ORDER BY ca.ACCIDENT_ID desc]]>
		<![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
		<!-- <if test="case_id !=null and case_id !=''"> <![CDATA[ AND A.case_id 
			= #{case_id} ]]> </if> <![CDATA[ ORDER BY 1111 desc]]> acc_date accDistreact 
			acc_reason -->

	</select>

	<select id="queryAccidentCaseTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<!-- select count(1) from APP___CLM__DBUSER.T_CLAIM_CASE A,APP___CLM__DBUSER.T_CLAIM_ACCIDENT B WHERE A.ACCIDENT_ID=B.ACCIDENT_ID -->
		<![CDATA[ 
			SELECT count(1) from (select distinct ca.ACCIDENT_ID,
                       ca.ACCIDENT_NO,
                       ca.INSURED_ID,
                       ca.ACC_DATE,
                       ca.ACC_REASON,
                       ca.ACC_PROVINCE,
                       ca.ACC_COUNTRY_CODE,
                       ca.ACC_CITY,
                       ca.ACC_DISTREACT,
                       ca.ACC_STREET,
                       ca.ACC_DESC,
                       cc.case_no,
                       cc.accident_detail,
					   cc.cure_hospital,
					   cc.cure_status,
					   cc.serious_disease
                  from APP___CLM__DBUSER.T_CLAIM_ACCIDENT ca
                  left join APP___CLM__DBUSER.T_CLAIM_CASE cc
                    on cc.accident_id = ca.accident_id
                  left join APP___CLM__DBUSER.T_DISTRICT cd
                    on ca.ACC_PROVINCE = cd.code
                    or ca.ACC_CITY = cd.code
                    or ca.ACC_DISTREACT = cd.CODE
                 where 1 = 1 AND ca.insured_id=#{insured_id}
        ]]>
		<!-- <if test="case_id !=null and case_id !=''"> <![CDATA[ AND A.case_id 
			= #{case_id} ]]> </if> <![CDATA[ ORDER BY accident_id desc]]> -->
		<if test="acc_reason !=null and acc_reason !=''"> 
        	<![CDATA[    AND  CA.ACC_REASON = #{acc_reason} ]]>
		</if>
		<if test="acc_date !=null and acc_date!=''"> 
        	<![CDATA[  and CA.ACC_DATE = #{acc_date} ]]>
		</if>
		<if test="acc_city !=null and acc_city!=''"> 
        	<![CDATA[and concat(cd.name,ca.acc_street)  like '%${acc_city}%']]>
		</if>
		<![CDATA[ ORDER BY CA.ACCIDENT_ID desc)]]>
	</select>

	<!-- 查询客户历史服务记录 -->
	<select id="queryClientHistory" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		
				SELECT *
          			FROM (SELECT ROWNUM RO, CCL.*
                  	FROM (SELECT DISTINCT CC.ACCEPT_TIME,
                                CSC.CLAIM_TYPE,
                                CM.POLICY_CODE,
                                (SELECT NAME
                                   FROM APP___CLM__DBUSER.T_CASE_STATUS TCS
                                  WHERE TCS.CODE = CC.CASE_STATUS) AS CASE_STATUS,
                                CC.CASE_ID ,
                                CC.CASE_NO ,
                                case when CC.TRUSTEE_NAME is null then '' else CC.TRUSTEE_NAME end trustee,
                                case when CC.TRUSTEE_NAME is null then '个人' else '代办' end apply_type
                      FROM APP___CLM__DBUSER.T_CLAIM_CASE       CC,
                           APP___CLM__DBUSER.T_CLAIM_SUB_CASE   CSC,
                           APP___CLM__DBUSER.T_CONTRACT_MASTER CM
                     WHERE CC.CASE_ID = CSC.CASE_ID
                     AND CC.CASE_ID = CM.CASE_ID
                     AND CM.CUR_FLAG='1' 
                     AND CC.INSURED_ID = #{client_no}
                     ORDER BY CC.ACCEPT_TIME DESC
                     ) CCL                     
             WHERE ROWNUM <= #{end_count}) ROCCL
         WHERE ROCCL.RO >= #{start_count}
		]]>
	</select>

	<select id="queryClientHistoryTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT count(1)
  				FROM (SELECT DISTINCT CC.ACCEPT_TIME,
                      CSC.CLAIM_TYPE,
                      CM.POLICY_CODE,
                      CC.CASE_STATUS,
		              CC.CASE_ID
        		FROM APP___CLM__DBUSER.T_CLAIM_CASE       CC,
             		 APP___CLM__DBUSER.T_CLAIM_SUB_CASE   CSC,
             		 APP___CLM__DBUSER.T_CONTRACT_MASTER CM
           		WHERE CC.CASE_ID = CSC.CASE_ID
             		 AND CC.CASE_ID = CM.CASE_ID
             		 AND CM.CUR_FLAG='1'
 			 		 AND CC.INSURED_ID = #{client_no})
		]]>
	</select>

	<select id="queryClientHistoryIsAE" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT *
		  FROM (SELECT ROWNUM RO, CCL.*
		          FROM (SELECT DISTINCT CC.ACCEPT_TIME,
		                                CL.CLAIM_TYPE,
		                                CL.POLICY_CODE,
		                                BP.PRODUCT_NAME_STD,
		                                BP.PRODUCT_CODE_SYS,
		                                CC.CASE_STATUS,
		                                CC.CASE_ID
		                  FROM APP___CLM__DBUSER.T_CLAIM_CASE       CC,
		                       APP___CLM__DBUSER.T_CLAIM_LIAB       CL,
		                       APP___CLM__DBUSER.T_BUSINESS_PRODUCT BP
		                 WHERE CC.CASE_ID = CL.CASE_ID
		                   AND BP.PRODUCT_CODE_SYS = CL.BUSI_PROD_CODE
		                   AND CC.TRUSTEE_TYPE = '1'
		                   AND CC.INSURED_ID = #{client_no}) CCL
		         WHERE ROWNUM <= #{end_count}) ROCCL
		 WHERE ROCCL.RO >= #{start_count}
		]]>
	</select>

	<select id="queryClientHistoryIsAETotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT count(1)
  FROM (SELECT DISTINCT CC.ACCEPT_TIME,
                      CL.CLAIM_TYPE,
                      CL.POLICY_CODE,
                      BP.PRODUCT_NAME_STD,
                      BP.PRODUCT_CODE_SYS,
                      CC.CASE_STATUS,
		              CC.CASE_ID
        FROM APP___CLM__DBUSER.T_CLAIM_CASE       CC,
             APP___CLM__DBUSER.T_CLAIM_LIAB       CL,
             APP___CLM__DBUSER.T_BUSINESS_PRODUCT BP
       WHERE CC.CASE_ID = CL.CASE_ID
         AND BP.PRODUCT_CODE_SYS = CL.BUSI_PROD_CODE
         AND CC.TRUSTEE_TYPE = '1'
         AND CC.INSURED_ID = #{client_no})
		]]>
	</select>

	<!-- 查询客户历史服务记录end -->


	<!-- yanggc_wb 查询受益人与领款人信息 -->
	<select id="findAllBeneAndPaye" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT T.PAYEE_NAME PAYEE_NAME,
		B.BENE_NAME BENE_NAME,
		P.PAY_AMOUNT PAY_AMOUNT,
		T.PAY_MODE PAY_MODE,
		P.BENE_RATE BENE_RATE
		FROM
		APP___CLM__DBUSER.T_CLAIM_PAY P, APP___CLM__DBUSER.T_CLAIM_PAYEE T,APP___CLM__DBUSER.T_CLAIM_BENE B
		WHERE B.BENE_ID =
		P.BENE_ID
		AND
		T.PAYEE_ID = P.PAYEE_ID AND P.BENE_ID=B.BENE_ID
		AND
		P.CASE_ID =#{case_id}
	</select>
	<!-- caoyy_wb 查询是否存在重复案件 -->
	<select id="queryDoubleCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select cc.case_status from APP___CLM__DBUSER.T_CLAIM_CASE cc, APP___CLM__DBUSER.T_CLAIM_SUB_CASE sc where cc.case_id = sc.case_id and cc.insured_id = ${insured_id}
   																				and sc.claim_type = ${claim_type}
   																				and sc.claim_date = to_date('${claim_date}','yyyy-mm-dd')
   																				and sc.acc_reason = ${acc_reason}
   																				]]>
		<if test="case_no !=null and case_no !=''"> 
        	<![CDATA[    AND  case_no != #{case_no} ]]>
		</if>
	</select>
	<!-- 查询理赔类型为医疗时，保额是否完全赔付 -->
	<select id="claimTypeTrueOrFalse" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			select lia.liability_status from APP___CLM__DBUSER.T_CLAIM_LIAB lia,APP___CLM__DBUSER.T_CLAIM_SUB_CASE cas,APP___CLM__DBUSER.T_CLAIM_CASE clm 
			where clm.case_id=cas.case_id 
			and cas.sub_case_id=lia.sub_case_id 
			and cas.claim_type='08'
			and cas.case_id=#{case_id}
		]]>
	</select>
	<!-- zhangjy_wb 查询调查信息 -->
	<select id="querySurveyInfoByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select distinct a.survey_type,a.apply_date,a.survey_reason,a.survey_org,a.survey_desc, c.survey_conclusion, c.remark, i.survey_item
  					from APP___CLM__DBUSER.T_SURVEY_APPLY a, APP___CLM__DBUSER.T_SURVEY_CONCLUSION c, APP___CLM__DBUSER.T_SURVEY_ITEM i
 					where a.case_id = #{case_id}
   					and a.apply_id = c.apply_id
   					and a.apply_id = i.apply_id]]>
	</select>
	<!-- and by caoyy_wb 风险评估报告页面 -->
	<select id="queryClaimCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  select  distinct c.case_no,c.accident_id,c.case_id,c.actual_pay,   c.accept_decision,
                c.approve_decision, 
             c.audit_decision ,c.CASE_STATUS
             
  						from APP___CLM__DBUSER.T_CLAIM_CASE c
 						where c.insured_id =  ${insured_id}
   						and c.case_no not in  '${case_no}'
   						and c.case_status != '99'
   					 
   						]]>
	</select>
	<!-- 查询产品代码 -->
	<select id="findproductStaticCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT PRODUCT.PRODUCT_STATIC_CODE, PRODUCT.PRODUCT_NAME_SYS, PROD.BUSI_PROD_CODE, PROD.VALIDATE_DATE, PROD.BUSI_ITEM_ID, CM.FORMER_ID, PROD.MASTER_BUSI_ITEM_ID,
				(SELECT DISTINCT M.POLICY_CODE FROM APP___CLM__DBUSER.T_CONTRACT_MASTER M WHERE M.POLICY_ID=CM.FORMER_ID AND M.CUR_FLAG='1') ORIGINAL_POLICY_CODE
				FROM APP___CLM__DBUSER.T_BUSINESS_PRODUCT PRODUCT ,APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD PROD,APP___CLM__DBUSER.T_CONTRACT_MASTER CM
              	WHERE PRODUCT.PRODUCT_CODE_SYS=PROD.BUSI_PROD_CODE
              	AND PROD.POLICY_CODE = CM.POLICY_CODE
              	AND CM.POLICY_CODE = #{policy_code}
              	AND PROD.CUR_FLAG = '1'
              	AND CM.CUR_FLAG='1']]>
	</select>
	<!-- 查询调查结论 -->
	<select id="querySurveyApplyByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select C.CONCLUSION_ID,
       C.APPLY_ID,
       C.SURVEY_CONCLUSION,
       C.REMARK,
       C.SURVEY_ORG,
       C.ORG_OPR,
       C.FINISH_DATE,
       C.POSITIVE_FLAG,
       C.AFC_SURVEY_DECI,
       C.POSITIVE_REASON,
       C.AFC_REMARK,
       C.INSERT_TIME,
       C.INSERT_BY,
       C.INSERT_TIMESTAMP,
       C.UPDATE_BY,
       C.UPDATE_TIMESTAMP,
       C.UPDATE_TIME,
       C.POSITIVE_CATE_CODE,
       C.POSITIVE_STAND_CODE,
       C.COLLECT_CODE from APP___CLM__DBUSER.T_SURVEY_APPLY A, APP___CLM__DBUSER.T_SURVEY_CONCLUSION C
 					where A.Case_Id = #{case_id}
   					and A.APPLY_ID = C.Apply_Id]]>
	</select>
	<!-- 分次给付审核 -->
	<select id="queryInstalmentInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select pay.policy_code,
                 ins.principal pay_amount,
                 pay.busi_prod_code,
                 cas.case_no,
                 cas.case_id,
                 cutomer.customer_name,
                 product.product_name_sys,
                 ins.pay_due_date,
                 bene.bene_name,
                 ins.instalment_id,
                 ins.instal_type,
                 ins.survery_periods,
                 ins.survery_freq_code,
                 ins.pay_type,
                 ins.total_times,
                 ins.survey_flag,
                 ins.pay_num
            from APP___CLM__DBUSER.T_CLAIM_CASE       cas,
                 APP___CLM__DBUSER.T_CUSTOMER         cutomer,
                 APP___CLM__DBUSER.T_CLAIM_PAY       pay,
                 APP___CLM__DBUSER.T_CLAIM_INSTALMENT ins,
                 APP___CLM__DBUSER.T_CLAIM_BENE       bene,
                 APP___CLM__DBUSER.T_BUSINESS_PRODUCT product
           where cas.insured_id = cutomer.customer_id
               and cas.case_id = pay.case_id
               and pay.claim_pay_id = ins.claim_pay_id
               and bene.bene_id = pay.bene_id
               and product.product_code_sys = pay.busi_prod_code
               and ins.instalment_id=#{instalment_id}]]>
	</select>
	<select id="queryInstalmentGradationInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select pay.policy_code,
                 pay.pay_amount,
                 pay.busi_prod_code,
                 cas.case_no,
                 cas.case_id,
                 cutomer.customer_name,
                 product.product_name_sys,
                 ins.pay_due_date,
                 bene.bene_name,
                 ins.instalment_id,
                 ins.instal_type,
                 ins.survery_periods,
                 ins.survery_freq_code,
                 liab.LIAB_NAME,
                 ins.pay_num,
                 ins.total_times,
                 ins.pay_type
            from APP___CLM__DBUSER.T_CLAIM_CASE       cas,
                 APP___CLM__DBUSER.T_CUSTOMER         cutomer,
                 APP___CLM__DBUSER.T_CLAIM_PAY       pay,
                 APP___CLM__DBUSER.T_CLAIM_INSTALMENT ins,
                 APP___CLM__DBUSER.T_CLAIM_BENE       bene,
                 APP___CLM__DBUSER.T_BUSINESS_PRODUCT product,
                 APP___CLM__DBUSER.T_CLAIM_LIAB liab
           where cas.insured_id = cutomer.customer_id
               and cas.case_id = pay.case_id
               and pay.claim_pay_id = ins.claim_pay_id
               and bene.bene_id = pay.bene_id
               and product.product_code_sys = pay.busi_prod_code
               and liab.CLAIM_LIAB_ID = ins.CLAIM_LIAB_ID
               and ins.instalment_id=#{instalment_id}
               and ins.instal_type = #{instal_type}]]>
	</select>
	<!-- end -->
	<!-- dujf_wb 保单计算信息 -->  
	<select id="findClaimBusiProdByMatch" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[SELECT a.policy_code,
					        a.claim_type,
					        a.valid_date,
					        (select T_CONTRACT_EXTEND.Pay_Due_Date
					           from APP___CLM__DBUSER.T_CONTRACT_EXTEND
					          where policy_id = a.policy_id
					            and busi_item_id = a.busi_item_id
					            and cur_flag = '0'
					            and rownum = 1) due_date,
					        a.busi_prod_code,
					        a.product_name_std,
					        sum(a.actual_pay) actual_pay,
					        a.busi_item_id,
					        a.validate_date
					   FROM (select distinct cl.policy_code,
					                         sc.claim_type,
					                         cl.claim_liab_id,
					                         cp.initial_validate_date  valid_date,
					                         cp.validate_date   validate_date,
					                         cl.busi_prod_code,
					                         p.product_name_std,
					                         (case when cl.liab_conclusion != 5 then nvl(cl.adjust_pay,0) else 0 end) actual_pay,
					                         cl.busi_item_id,
					                         cl.policy_id
					           from APP___CLM__DBUSER.T_CLAIM_SUB_CASE   sc,
					                APP___CLM__DBUSER.T_BUSINESS_PRODUCT p,
					                APP___CLM__DBUSER.t_contract_busi_prod  cp,
					                APP___CLM__DBUSER.T_CLAIM_LIAB       cl
					          where sc.sub_case_id = cl.sub_case_id
					            and cp.busi_item_id = cl.busi_item_id
					            and cp.case_id = cl.case_id
					            and cp.cur_flag='1'
					            and cl.busi_prod_code = p.product_code_sys
					            and sc.case_id = #{case_id}) a
					  GROUP BY a.policy_code,
					           a.policy_id,
					           a.claim_type,
					           a.valid_date,
					           a.validate_date,
					           a.busi_prod_code,
					           a.product_name_std,
					           a.busi_item_id
					  order by a.policy_code, a.busi_prod_code

   ]]>
	</select>

	

	<!-- xuyz_wb 匹配理算日志 -->
	<select id="findClaimBusiProdByMatchLog" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[select a.*, cl.claim_type,
       cl.clm_remark,
       cl.liab_name from APP___CLM__DBUSER.T_CLAIM_LIAB cl ,(select bp.policy_code,
       bp.valid_date,
       bp.expire_date,
       bp.busi_prod_code,
       
       p.product_name_std,
       bp.calc_pay,
       bp.busi_item_id
       from APP___CLM__DBUSER.T_CLAIM_BUSI_PROD bp, APP___CLM__DBUSER.T_BUSINESS_PRODUCT p
       where bp.busi_prod_code = p.product_code_sys
       and bp.case_id = #{case_id}) a where cl.case_id=#{case_id} and cl.policy_code =a.policy_code 
   ]]>
	</select>
	<!-- zhangdong_wb1 查询医院信息(分页) -->
	<select id="findHospitalByAllPage" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
           select b.hospital_code,
			       b.hospital_name,
			       b.hospital_level,
			       a.is_designated,
			       a.is_cancer,
			       a.hospital_status,
			       b.organ_code,
			       d.organ_name,
			       a.hospital_id,
			       c.level_desc as hospital_level_name
			
			  from APP___CLM__DBUSER.T_CLAIM_HOSPITAL_SERVICE a
			  left join APP___CLM__DBUSER.T_HOSPITAL b on a.hospital_code = b.hospital_code
			  left join APP___CLM__DBUSER.T_CLM_HOSPITAL_LEVEL c on b.hospital_level = c.level_code
			  left join APP___CLM__DBUSER.T_UDMP_ORG_REL d on b.organ_code = d.organ_code
			 where 1=1 ]]>

		<if test="hospital_code !=null and hospital_code !=''"> 
	        	<![CDATA[  and b.hospital_code = #{hospital_code} ]]>
		</if>
		<if test="hospital_name !=null and hospital_name !=''"> 
	        	<![CDATA[  and b.hospital_name like '%${hospital_name}%' ]]>
		</if>
		<if test="hospital_level !=null and hospital_level !=''"> 
	        	<![CDATA[  and b.hospital_level = #{hospital_level} ]]>
		</if>
		<if test="is_designated !=9 and is_designated != null and is_designated != null"> 
	        	<![CDATA[  and a.is_designated = #{is_designated} ]]>
		</if>
		<if test="hospital_status !=null and hospital_status !=''"> 
	        	<![CDATA[  and a.hospital_status = #{hospital_status} ]]>
		</if>
		<if test="organ_code !=null and organ_code !=''"> 
	        	<![CDATA[  and b.organ_code = #{organ_code} ]]>
		</if>
			<![CDATA[ ORDER BY b.hospital_code]]>
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>

	<!-- zhangdong_wb1 查询医院信息条目(分页) -->
	<select id="findHospitalByAllTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    	<![CDATA[  select count(1)
          from APP___CLM__DBUSER.T_CLAIM_HOSPITAL_SERVICE a
			  left join APP___CLM__DBUSER.T_HOSPITAL b on a.hospital_code = b.hospital_code
			  left join APP___CLM__DBUSER.T_CLM_HOSPITAL_LEVEL c on b.hospital_level = c.level_code
			 where 1=1]]>
		<if test="hospital_code !=null and hospital_code !=''"> 
	        	<![CDATA[  and b.hospital_code = #{hospital_code} ]]>
		</if>
		<if test="hospital_name !=null and hospital_name !=''"> 
	        	<![CDATA[  and b.hospital_name like '%${hospital_name}%' ]]>
		</if>
		<if test="hospital_level !=null and hospital_level !=''"> 
	        	<![CDATA[  and b.hospital_level = #{hospital_level} ]]>
		</if>
		<if test="is_designated !=9 and is_designated != null and is_designated != null"> 
	        	<![CDATA[  and a.is_designated = #{is_designated} ]]>
		</if>
		<if test="hospital_status !=null and hospital_status !=''"> 
	        	<![CDATA[  and a.hospital_status = #{hospital_status} ]]>
		</if>
		<if test="organ_code !=null and organ_code !=''"> 
	        	<![CDATA[  and b.organ_code = #{organ_code} ]]>
		</if>
	</select>
	<!-- zhangdong_wb1 查询医院全部信息 -->
	<select id="findHospitalAllInfoByKey" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
		
           select b.hospital_code,
			       b.hospital_name,
			       b.hospital_level,
			       a.is_designated,
			       a.is_cancer,
			       a.hospital_status,
			       b.organ_code,
			       d.organ_name,
			       a.hospital_id,
			       a.is_costly,
			       a.is_deformity,
			       c.level_desc as hospital_level_name,
	               a.STATE,
	               a.CITY,
	               a.DISTRICT,
	               a.private_flag,
	               a.auxiliary_flag,
	               a.recovery_flag
			
			  from APP___CLM__DBUSER.T_CLAIM_HOSPITAL_SERVICE a
			  left join APP___CLM__DBUSER.T_HOSPITAL b on a.hospital_code = b.hospital_code
			  left join APP___CLM__DBUSER.T_CLM_HOSPITAL_LEVEL c on b.hospital_level = c.level_code
			  left join APP___CLM__DBUSER.T_UDMP_ORG_REL d on b.organ_code = d.organ_code
			 where 1=1]]>

		<if test="hospital_id !=null and hospital_id !=''"> 
	        	<![CDATA[  and a.hospital_id = #{hospital_id} ]]>
		</if>
	</select>
	<!-- 根据CaseId查询理赔类型，治疗情况，出险原因 -->
	<select id="findClaimSubCaseByCaseId" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[ SELECT A.CURE_STATUS,B.CLAIM_TYPE,ACC_REASON,B.CLAIM_DATE,A.INSURED_ID FROM APP___CLM__DBUSER.T_CLAIM_CASE A,APP___CLM__DBUSER.T_CLAIM_SUB_CASE B WHERE B.CASE_ID=A.CASE_ID AND B.CASE_ID=#{case_id}]]>
	</select>
	<!-- 根据 CUSTOMER_ID 查出生日期 -->
	<select id="finInsuredBirthday" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[select a.CUSTOMER_BIRTHDAY from APP___CLM__DBUSER.t_Customer a WHERE a.CUSTOMER_ID=#{customer_id}]]>
	</select>
	<!-- add by zhaoyq_wb 打印单证查看赔案信息 -->
	<sql id="CompersatePrintWhereCondition">
		<!-- 赔案号 -->
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND ca.case_no = #{case_no} ]]></if>
		<!-- 管理机构 -->
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ and ca.organ_code like  '${organ_code}%' ]]></if>
		<!-- 出险人姓名 -->
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ and cu.customer_name = #{customer_name} ]]></if>
		<!-- 出险人证件号码 -->
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ and cu.customer_certi_code = #{customer_certi_code} ]]></if>
		<!-- 单证生成日期start -->
		<if
			test=" insert_stime != null and insert_stime != '' and insert_etime == null "><![CDATA[ and do.insert_time >= #{insert_stime} ]]></if>
		<!-- 单证生成日期end -->
		<if
			test=" insert_etime != null and insert_etime != '' and insert_stime == null "><![CDATA[ and do.insert_time <= #{insert_etime} ]]></if>
		<if
			test=" insert_stime != null and insert_stime != '' and insert_etime != null and insert_etime != '' and insert_stime != insert_etime"><![CDATA[ and do.insert_time between #{insert_stime} and #{insert_etime}]]></if>
		<if
			test=" insert_stime != null and insert_stime != '' and insert_etime != null and insert_etime != '' and insert_stime == insert_etime "><![CDATA[ and to_date(to_char(do.insert_time,'yyyy-MM-dd'), 'yyyy-MM-dd') = #{insert_stime}]]></if>


		<!-- 保单号 -->
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ and ma.policy_code = #{policy_code} ]]></if>
		<!-- 赔案状态 -->
		<if test=" case_status != null and case_status != ''  "><![CDATA[ and ca.case_status = #{case_status} ]]></if>
		<!-- 单证类型 -->
		<if test=" template_codes != null and template_codes != ''  "><![CDATA[ and do.template_code in ${template_codes}]]></if>

	</sql>
	<select id="queryCompersatePrint" parameterType="java.util.Map"
		resultType="java.util.Map"> 
     <![CDATA[   select distinct ca.case_no,
                       ca.case_status,
                       (select cu.customer_name from  APP___CLM__DBUSER.T_CUSTOMER cu where ca.insured_id = cu.customer_id) as customer_name,
                       (select cu.customer_certi_code from  APP___CLM__DBUSER.T_CUSTOMER cu where ca.insured_id = cu.customer_id) as customer_certi_code,
                       subca.claim_date,
                       do.template_code,
                       do.reprint_times,
                       do.print_by,
                       do.print_time
                  from APP___CLM__DBUSER.T_CLAIM_CASE     ca,
                       APP___CLM__DBUSER.T_DOCUMENT       do,
                       APP___CLM__DBUSER.T_CLAIM_SUB_CASE subca
                 where ca.case_id = subca.case_id
                   and do.buss_code = ca.case_no]]>
		<include refid="CompersatePrintWhereCondition" />
	</select>

	<select id="queryCompersatePrintPage" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  SELECT B.RN,B.CASE_ID,B.CASE_NO,B.CASE_STATUS,B.ORGAN_CODE,B.CUSTOMER_NAME,B.CUSTOMER_CERTI_CODE,
    B.TEMPLATE_CODE,B.REPRINT_TIMES,B.PRINT_BY,B.PRINT_TIME,B.DOCUMENT_NO,B.DOCUMENT_NAME,B.DOC_LIST_ID,B.INSURED_ID,B.IS_SEND,B.IS_SENDS FROM(
              SELECT ROWNUM RN,C.CASE_ID,C.CASE_NO,C.CASE_STATUS,C.ORGAN_CODE,C.CUSTOMER_NAME,C.CUSTOMER_CERTI_CODE,
    C.TEMPLATE_CODE,C.REPRINT_TIMES,C.PRINT_BY,C.PRINT_TIME,C.DOCUMENT_NO,C.DOCUMENT_NAME,C.DOC_LIST_ID,C.INSURED_ID,C.IS_SEND,C.IS_SENDS FROM (
    		  	SELECT DISTINCT 
                       CA.CASE_ID,
                       CA.CASE_NO,
                       CA.CASE_STATUS,
                       CA.ORGAN_CODE,
                       CU.CUSTOMER_NAME,
                       CU.CUSTOMER_CERTI_CODE,
                       DO.TEMPLATE_CODE,
                       DO.REPRINT_TIMES,
                       DO.PRINT_BY,
                       DO.PRINT_TIME,
                       DO.DOCUMENT_NO,
                       DO.DOCUMENT_NAME,
                       DO.DOC_LIST_ID,
                       CA.INSURED_ID,
                       CD.IS_SEND,
                       CM.IS_SEND AS IS_SENDS,
                       (SELECT MIN(TCSC.CLAIM_DATE) FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE TCSC WHERE TCSC.CASE_ID = CA.CASE_ID) CLAIM_DATE
                  FROM APP___CLM__DBUSER.T_CLAIM_CASE     CA
	                  LEFT JOIN APP___CLM__DBUSER.T_DOCUMENT       DO
	                  ON DO.BUSS_CODE = CA.CASE_NO
	                  AND DO.TEMPLATE_CODE !='CLM_00008'
	                 	AND DO.TEMPLATE_CODE !='CLM_00012'
	                  LEFT JOIN APP___CLM__DBUSER.T_CUSTOMER CU
	                  ON CA.INSURED_ID = CU.CUSTOMER_ID
	                  LEFT JOIN APP___CLM__DBUSER.T_Note_Doc_Send_Batch CD
	                  ON CA.CASE_ID = CD.CASE_ID
	                  AND CD.TEMPLATE_CODE = DO.TEMPLATE_CODE
	                  LEFT JOIN APP___CLM__DBUSER.T_MAIL_DOC_SEND_BATCH CM
	                  ON CM.CASE_ID = CA.CASE_ID
	                  AND CM.TEMPLATE_CODE = DO.TEMPLATE_CODE
	                  WHERE 1 = 1  ]]>
		<include refid="CompersatePrintWhereCondition" /> 
        <![CDATA[)C WHERE 1 = 1  AND ROWNUM <= #{LESS_NUM} ORDER BY TEMPLATE_CODE]]>
     <![CDATA[ ) B WHERE RN > #{GREATER_NUM}]]>
	</select>
	<select id="queryCompersatePrintTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  select count(1)
                 from(
                    select distinct ca.case_id,
                         ca.case_no,
                         ca.case_status,
                         ca.organ_code,
                         cu.customer_name,
                         cu.customer_certi_code,
                         do.template_code,
                         do.reprint_times,
                         do.print_by,
                         do.print_time,
                         do.document_no,
                         do.document_name,
                         do.doc_list_id,
                         ca.insured_id,
                         cd.is_send,
                         cm.is_send as is_sends,
                         (select min(tcsc.claim_date) from APP___CLM__DBUSER.T_CLAIM_SUB_CASE tcsc where tcsc.case_id = ca.case_id) claim_date
                    FROM APP___CLM__DBUSER.T_CLAIM_CASE     CA
		                LEFT JOIN APP___CLM__DBUSER.T_DOCUMENT       DO
		                ON DO.BUSS_CODE = CA.CASE_NO
		                AND DO.TEMPLATE_CODE !='CLM_00008'
		               	AND DO.TEMPLATE_CODE !='CLM_00012'
		                LEFT JOIN APP___CLM__DBUSER.T_CUSTOMER CU
		                ON CA.INSURED_ID = CU.CUSTOMER_ID
		                LEFT JOIN APP___CLM__DBUSER.T_Note_Doc_Send_Batch CD
		                ON CA.CASE_ID = CD.CASE_ID
		                AND CD.TEMPLATE_CODE = DO.TEMPLATE_CODE
		                LEFT JOIN APP___CLM__DBUSER.T_MAIL_DOC_SEND_BATCH CM
	                  	ON CM.CASE_ID = CA.CASE_ID
	                  	AND CM.TEMPLATE_CODE = DO.TEMPLATE_CODE
		                WHERE 1 = 1  ]]>
		<include refid="CompersatePrintWhereCondition" /> 
        <![CDATA[     order by claim_date)  ]]>
	</select>
	<select id="queryCompersatePrintTotalCode" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  select count(1)
                 from(
                    select distinct ca.case_id,
                         ca.case_no,
                         ca.case_status,
                         ca.organ_code,
                         cu.customer_name,
                         cu.customer_certi_code,
                         do.template_code,
                         do.reprint_times,
                         do.print_by,
                         do.print_time,
                         do.document_no,
                         do.document_name,
                         do.doc_list_id,
                         ca.insured_id,
                         cd.is_send,
                         cm.is_send as is_sends,
                         (select min(tcsc.claim_date) from APP___CLM__DBUSER.T_CLAIM_SUB_CASE tcsc where tcsc.case_id = ca.case_id) claim_date
                    from APP___CLM__DBUSER.T_CLAIM_CASE ca
				 inner join APP___CLM__DBUSER.T_DOCUMENT do
				    on do.buss_code = ca.case_no
				   and do.template_code != 'CLM_00008'
				   and do.template_code != 'CLM_00012'
				 inner join APP___CLM__DBUSER.T_CONTRACT_MASTER ma
				    on ca.case_id = ma.case_id
				   and ma.cur_flag = 1
				 inner join APP___CLM__DBUSER.T_CUSTOMER cu
				    on ca.insured_id = cu.customer_id
				  left join APP___CLM__DBUSER.T_NOTE_DOC_SEND_BATCH cd
				    on ca.case_id = cd.case_id
				   and cd.template_code = do.template_code
				  left join APP___CLM__DBUSER.T_MAIL_DOC_SEND_BATCH cm
				    on ca.case_id = cm.case_id
				   and cm.template_code = do.template_code
				 where 1 = 1 ]]>
		<include refid="CompersatePrintWhereCondition" /> 
        <![CDATA[     order by claim_date)  ]]>
	</select>
	<select id="queryCompersatePrintPageCode" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  select B.case_id,B.case_no,B.case_status,B.organ_code,B.customer_name,B.customer_certi_code,
    B.template_code,B.reprint_times,B.print_by,B.print_time,B.document_no,B.document_name,B.doc_list_id,B.insured_id,B.is_send,B.is_sends,B.RN from(
           select A.case_id,A.case_no,A.case_status,A.organ_code,A.customer_name,A.customer_certi_code,
                     A.template_code,A.reprint_times,A.print_by,A.print_time,A.document_no,A.document_name,A.doc_list_id,A.insured_id,A.is_send,A.is_sends,rownum RN from(
              select distinct ca.case_id,
                       ca.case_no,
                       ca.case_status,
                       ca.organ_code,
                       cu.customer_name,
                       cu.customer_certi_code,
                       do.template_code,
                       do.reprint_times,
                       do.print_by,
                       do.print_time,
                       do.document_no,
                       do.document_name,
                       do.doc_list_id,
                       ca.insured_id,
                       cd.is_send,
                       cm.is_send as is_sends,
                       (select min(tcsc.claim_date) from APP___CLM__DBUSER.T_CLAIM_SUB_CASE tcsc where tcsc.case_id = ca.case_id) claim_date
                  from APP___CLM__DBUSER.T_CLAIM_CASE ca
				 inner join APP___CLM__DBUSER.T_DOCUMENT do
				    on do.buss_code = ca.case_no
				   and do.template_code != 'CLM_00008'
				   and do.template_code != 'CLM_00012'
				 inner join APP___CLM__DBUSER.T_CONTRACT_MASTER ma
				    on ca.case_id = ma.case_id
				   and ma.cur_flag = 1
				 inner join APP___CLM__DBUSER.T_CUSTOMER cu
				    on ca.insured_id = cu.customer_id
				  left join APP___CLM__DBUSER.T_NOTE_DOC_SEND_BATCH cd
				    on ca.case_id = cd.case_id
				   and cd.template_code = do.template_code
				  left join APP___CLM__DBUSER.T_MAIL_DOC_SEND_BATCH cm
				    on ca.case_id = cm.case_id
				   and cm.template_code = do.template_code
				 where 1 = 1 ]]>
		<include refid="CompersatePrintWhereCondition" /> 
        <![CDATA[    order by claim_date) A where rownum <= #{LESS_NUM} ]]>
     <![CDATA[ ) B where RN > #{GREATER_NUM}]]>
	</select>
	<!-- 理赔单证通知书打印查询单证列表 -->
	<select id="queryPrintChecklist" parameterType="java.util.Map"
		resultType="java.util.Map"> 
     <![CDATA[   select ls.checklist_code 
                   from APP___CLM__DBUSER.T_CHECKLIST_PARA pa,
                        APP___CLM__DBUSER.T_CLAIM_CHECKLIST ls
                  where pa.checklist_code = ls.checklist_code 
                    and case_id = #{case_id}]]>
	</select>

	<!-- 赔付通知书打印医疗信息 -->
	<select id="queryMedList" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[SELECT DISTINCT LI.CLAIM_LIAB_ID,
                LI.POLICY_CODE,
                LI.BUSI_PROD_CODE,
                LI.BUSI_ITEM_ID,
                LI.ITEM_ID,
                LI.LIAB_ID,
                LI.LIAB_NAME,
                LI.CLM_AFTER_STATE,
                LI.SURPLUS_EFFECT_AMOUNT,
                (SELECT SUM(BI.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI WHERE LI.CASE_ID=BI.CASE_ID) SUM_AMOUNT,
                (SELECT SUM(
                CASE WHEN BI.TREAT_START >= LI.LIAB_START_DATE and BI.TREAT_END <= LI.LIAB_END_DATE THEN DAY_TOTAL
                  WHEN  BI.TREAT_START <= LI.LIAB_START_DATE AND BI.TREAT_END >= LI.LIAB_START_DATE THEN BI.TREAT_END - LI.LIAB_START_DATE
                   WHEN   BI.TREAT_START< LI.LIAB_END_DATE AND BI.TREAT_END > LI.LIAB_END_DATE THEN LI.LIAB_END_DATE - BI.TREAT_START
                     ELSE 0
                     END
                ) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI WHERE LI.CASE_ID=BI.CASE_ID AND TREAT_TYPE=1  )  really_day,
                LI.ADVANCE_PAY,
                LI.ACTUAL_PAY,
                LI.CLOB_ID,
                LI.LIAB_CONCLUSION,
                LI.LIABILITY_STATUS LIABILITY_STATE
        FROM APP___CLM__DBUSER.T_CLAIM_LIAB  LI
       WHERE  LI.CLAIM_TYPE = '08'
         AND LI.LIAB_CONCLUSION!=5
         AND LI.CASE_ID = #{case_id}
			   ]]>
	</select>
	<!-- 赔付通知书打印医疗信息 -->
	<select id="queryMedListJi" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[SELECT DISTINCT LI.BUSI_PROD_CODE,
          LI.POLICY_CODE,
          LI.BUSI_ITEM_ID,
          (SELECT SUM(A.OUTPUT_AMOUNT) FROM DEV_CLM.T_ACCUTION_LIST A,DEV_CLM.T_ACCUTION B WHERE A.ACCUTION_ID=B.ACCUTION_ID AND B.ACCUMU_TYPE=15 AND LI.CASE_ID = A.CASE_ID) SUM_DUTYAMNT,
          (SELECT SUM(BI.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL_PAID BI WHERE LI.CASE_ID=BI.CASE_ID) SUM_OTHERPAY,
          (SELECT SUM(C.CALC_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI,APP___CLM__DBUSER.T_CLAIM_BILL_ITEM C WHERE LI.CASE_ID=BI.CASE_ID AND C.CASE_ID = BI.CASE_ID AND C.BILL_ID = BI.BILL_ID AND BI.TREAT_TYPE=1) SUM_HOSSELPAY,
          (SELECT SUM(C.CALC_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI,APP___CLM__DBUSER.T_CLAIM_BILL_ITEM C WHERE LI.CASE_ID=BI.CASE_ID AND C.CASE_ID = BI.CASE_ID AND C.BILL_ID = BI.BILL_ID AND BI.TREAT_TYPE=0) SUM_MEDSELPAY,
                (SELECT SUM(BI.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI WHERE LI.CASE_ID=BI.CASE_ID AND TREAT_TYPE=0) SUM_MEDPAY,
                (SELECT SUM(BI.SUM_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI WHERE LI.CASE_ID=BI.CASE_ID AND TREAT_TYPE=1) SUM_AMOUNT,
                (SELECT SUM(BI.CALC_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI WHERE LI.CASE_ID=BI.CASE_ID AND TREAT_TYPE=0) SUM_MEDADJPAY,
                (SELECT SUM(BI.CALC_AMOUNT) FROM APP___CLM__DBUSER.T_CLAIM_BILL BI WHERE LI.CASE_ID=BI.CASE_ID AND TREAT_TYPE=1) SUM_HOSADJPAY
                
        FROM APP___CLM__DBUSER.T_CLAIM_LIAB  LI
       WHERE LI.CLAIM_TYPE = '08'
       AND LI.CASE_ID = #{case_id}
			   ]]>
	</select>

	<!-- 赔付通知书打印非医疗信息 -->
	<select id="queryNotMedList" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[select distinct li.claim_liab_id,
                   li.policy_code,
                   li.busi_prod_code,
                   li.busi_item_id,
                   li.item_id,
                   li.liab_id,
                   li.liab_name,
                   li.ADVANCE_PAY,
                   li.ACTUAL_PAY,
                   li.CLM_AFTER_STATE,
                   li.SURPLUS_EFFECT_AMOUNT,
                   LI.LIAB_CONCLUSION,
                   pro.amount,
                   li.CLOB_ID,
                   p.liability_state liability_state
              from APP___CLM__DBUSER.T_claim_liab li, APP___CLM__DBUSER.T_CONTRACT_PRODUCT pro,APP___CLM__DBUSER.T_CLAIM_PRODUCT p
             where pro.item_id = li.item_id 
               and pro.case_id = li.case_id
               and pro.policy_id = li.policy_id
               and pro.product_id = li.product_id
               and pro.busi_item_id = li.busi_item_id
               and p.case_id = li.case_id
               and p.policy_id = li.policy_id
               and p.product_id = pro.product_id
               and p.item_id = li.item_id
               and p.busi_item_id = li.busi_item_id
               and pro.cur_flag = 1
               and li.claim_type != '08'
               AND LI.LIAB_CONCLUSION!=5
               and li.case_id = #{case_id}]]>
	</select>
	<!-- 赔付通知书打印非医疗信息 -->
	<select id="queryNotMedListJi" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[select distinct 
                   li.policy_code,
                   li.busi_prod_code,
                   li.busi_item_id,
                   (select p.DEAL_CONCLUSION from APP___CLM__DBUSER.T_CLAIM_BUSI_PROD p where p.case_id = li.case_id and p.busi_item_id= li.busi_item_id  AND p.BUSI_PROD_CODE = li.busi_prod_code) deal_conclusion,
                   (select sum(amount) from APP___CLM__DBUSER.T_CONTRACT_PRODUCT p where p.case_id = li.case_id and p.cur_flag = '1' and p.busi_item_id= li.busi_item_id  ) amount
                   
              from APP___CLM__DBUSER.T_claim_liab li
             where li.claim_type != '08'
               and li.case_id = #{case_id}]]>
	</select>
	<!-- 查看保单条款 根据赔案号查询相关保单号和险种代码 -->
	<select id="queryPolicyAndProdList" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[select distinct ma.case_id,
                   ma.policy_code,
                   pr.busi_prd_id,
                   prname.product_abbr_name
              from APP___CLM__DBUSER.T_CONTRACT_MASTER    ma,
                   APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD pr,
                   APP___CLM__DBUSER.T_BUSINESS_PRODUCT   prname
             where ma.policy_code = pr.policy_code
                   and pr.busi_prd_id = prname.business_prd_id
                   and ma.case_id = #{case_id}]]>
	</select>
	<!-- 维护理赔关怀-添加关怀任务 -->
	<select id="findClaimCaseByCondition" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
		select distinct ca.case_id,
                ca.case_no,
                ca.organ_code,
                ca.end_case_time,
                cu.customer_name
           from APP___CLM__DBUSER.T_CLAIM_CASE ca, APP___CLM__DBUSER.T_CUSTOMER cu, APP___CLM__DBUSER.T_CONTRACT_MASTER po
          where ca.insured_id = cu.customer_id
            and ca.case_id = po.case_id
            and ca.end_case_time is not null
		]]>
		<if test="case_no !=null and case_no !=''"> 
	        	<![CDATA[  and ca.case_no = #{case_no} ]]>
		</if>
		<if test="policy_code !=null and policy_code !=''"> 
	        	<![CDATA[  and po.policy_code = #{policy_code} ]]>
		</if>
		<if test="customer_name !=null and customer_name !=''"> 
	        	<![CDATA[  and cu.customer_name = #{customer_name} ]]>
		</if>
		<if test="customer_certi_code !=null and customer_certi_code !=''"> 
	        	<![CDATA[  and cu.customer_certi_code = #{customer_certi_code} ]]>
		</if>
		<![CDATA[ order by ca.end_case_time]]>
	</select>
	<!-- 查询出的赔案都已做过关怀任务（即回访结果中有数据） -->
	<select id="findClaimCaseByConditionIsDo" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
		select distinct ca.case_id,
                ca.case_no,
                ca.organ_code,
                ca.end_case_time,
                cu.customer_name,
                visit.next_visit_date
           from APP___CLM__DBUSER.T_CLAIM_CASE ca, APP___CLM__DBUSER.T_CUSTOMER cu, APP___CLM__DBUSER.T_CONTRACT_MASTER po,APP___CLM__DBUSER.T_CLAIM_CARE_VISIT visit
          where ca.insured_id = cu.customer_id
            and ca.case_id = po.case_id
            and visit.case_id = ca.case_id
            and visit.next_visit_date is not null
		]]>
		<if test="case_no !=null and case_no !=''"> 
	        	<![CDATA[  and ca.case_no = #{case_no} ]]>
		</if>
		<if test="policy_code !=null and policy_code !=''"> 
	        	<![CDATA[  and po.policy_code = #{policy_code} ]]>
		</if>
		<if test="customer_name !=null and customer_name !=''"> 
	        	<![CDATA[  and cu.customer_name = #{customer_name} ]]>
		</if>
		<if test="customer_certi_code !=null and customer_certi_code !=''"> 
	        	<![CDATA[  and cu.customer_certi_code = #{customer_certi_code} ]]>
		</if>
		<![CDATA[ order by visit.next_visit_date]]>
	</select>
	<!-- 维护理赔关怀信息分页查询 -->
	<sql id="queryPerserveCareServiceMsgCondition">
		<if test="case_no !=null and case_no !=''"> 
	        	<![CDATA[  and ca.case_no = #{case_no} ]]>
		</if>
		<if test="policy_code !=null and policy_code !=''"> 
	        	<![CDATA[  and po.policy_code = #{policy_code} ]]>
		</if>
		<if test="customer_name !=null and customer_name !=''"> 
	        	<![CDATA[  and cu.customer_name = #{customer_name} ]]>
		</if>
		<if test="customer_certi_code !=null and customer_certi_code !=''"> 
	        	<![CDATA[  and cu.customer_certi_code = #{customer_certi_code} ]]>
		</if>
		<if test="organ_code !=null and organ_code !=''"> 
	        	<![CDATA[  and care.organ_code = #{organ_code} ]]>
		</if>
		<if test="visite_by !=null and visite_by !=''"> 
	        	<![CDATA[  and visit.visite_by = #{visite_by} ]]>
		</if>
		<if test="return_visit_status !=null and return_visit_status !=''"> 
	        	<![CDATA[  and care.return_visit_status in ${return_visit_status} ]]>
		</if>
		<if test="service_status !=null and service_status !=''"> 
	        	<![CDATA[  and visit.service_status  = #{service_status} ]]>
		</if>
	</sql>
	<!-- 有回访人条件 关联t_claim_care_visit查询 -->
	<select id="queryPerserveCareServiceMsg" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
         select B.case_id,
        B.case_no,
        B.customer_id,
        B.customer_name,
        B.organ_code,
       B.end_case_time,
        B.server_id,
        B.care_object_id,
        B.care_object_type,
        B.care_id,
        B.service_status from (
        select A.case_id,A.case_no,A.customer_id,A.customer_name,A.organ_code,A.end_case_time,A.server_id,A.care_object_id,A.care_object_type,A.care_id,A.service_status , 
                 rownum RN   from(
		select distinct ca.case_id,
                ca.case_no,
                cu.customer_id,
                cu.customer_name,
                care.organ_code,
                ca.end_case_time,
                care.insert_time,
                care.server_id,
                care.care_object_id,
                care.care_object_type,
                care.care_id,
                visit.service_status
           from APP___CLM__DBUSER.T_CLAIM_CARE       care,
                APP___CLM__DBUSER.T_CLAIM_CASE       ca 
                	left join  APP___CLM__DBUSER.T_CLAIM_CARE_VISIT visit on ca.case_id = visit.case_id,
                APP___CLM__DBUSER.T_CUSTOMER         cu,
                APP___CLM__DBUSER.T_CONTRACT_MASTER  po
          where ca.case_id = care.case_id
            and ca.insured_id = cu.customer_id
            and po.case_id = ca.case_id
		]]>
		<include refid="queryPerserveCareServiceMsgCondition" /> 
		<![CDATA[ order by APP___CLM__DBUSER.care.insert_time) A ) B where RN <= #{LESS_NUM} and RN > #{GREATER_NUM} ]]>
        <![CDATA[ ]]>
	</select>
	<select id="queryPerserveCareServiceMsgCount" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[ 
        select count(1) from(
		select distinct ca.case_id,
                ca.case_no,
                cu.customer_id,
                cu.customer_name,
                care.organ_code,
                ca.end_case_time,
                care.insert_time,
                care.server_id,
                care.care_object_id,
                care.care_object_type,
                care.care_id,
                visit.service_status
           from APP___CLM__DBUSER.T_CLAIM_CARE       care,
                APP___CLM__DBUSER.T_CLAIM_CASE       ca 
                	left join  APP___CLM__DBUSER.T_CLAIM_CARE_VISIT visit on ca.case_id = visit.case_id,
                APP___CLM__DBUSER.T_CUSTOMER         cu,
                APP___CLM__DBUSER.T_CONTRACT_MASTER  po
          where ca.case_id = care.case_id
            and ca.insured_id = cu.customer_id
            and po.case_id = ca.case_id
		]]>
		<include refid="queryPerserveCareServiceMsgCondition" /> 
		<![CDATA[ order by care.insert_time) ]]>
	</select>
	<!-- 无回访人条件 -->
	<!-- <select id="queryPerserveCareServiceMsgNoVisit" parameterType="java.util.Map" 
		resultType="java.util.Map"> 
	<![CDATA[ 
		select A.case_id,A.case_no,A.customer_id,A.customer_name,A.organ_code,A.end_case_time,A.server_id,A.care_object_id,A.care_object_type,A.care_id from( 
		select distinct ca.case_id, 
				ca.case_no, 
				cu.customer_id, 
				cu.customer_name, 
				care.organ_code, 
				ca.end_case_time, 
				care.insert_time, 
				care.server_id, 
				care.care_object_id, 
				care.care_object_type, 
				care.care_id,rownum RN 
		from APP___CLM__DBUSER.T_CLAIM_CARE care, 
			 APP___CLM__DBUSER.T_CLAIM_CASE ca, 
			 APP___CLM__DBUSER.T_CUSTOMER cu, 
		     APP___CLM__DBUSER.T_CONTRACT_MASTER po 
		where ca.case_id = care.case_id 
		     and ca.insured_id = cu.customer_id 
		     and po.case_id = ca.case_id 
	]]> 
	<include refid="queryPerserveCareServiceMsgCondition" /> 
	<![CDATA[ order by care.insert_time) A where RN <= #{LESS_NUM} and RN > #{GREATER_NUM} ]]> 
	</select> -->
	<select id="queryPerserveCareServiceMsgNoVisit" parameterType="java.util.Map"
		resultType="java.util.Map">
	 <![CDATA[ select B.case_id,
        B.case_no,
        B.customer_id,
        B.customer_name,
        B.organ_code,
        B.end_case_time,
        B.server_id,
        B.care_object_id,
        B.care_object_type,
        B.care_id
   from (select A.case_id,
                A.case_no,
                A.customer_id,
                A.customer_name,
                A.organ_code,
                A.end_case_time,
                A.server_id,
                A.care_object_id,
                A.care_object_type,
                A.care_id,
                rownum RN
           from (select ca.case_id,
                                 ca.case_no,
                                 cu.customer_id,
                                 cu.customer_name,
                                 care.organ_code,
                                 ca.end_case_time,
                                 care.insert_time,
                                 care.server_id,
                                 care.care_object_id,
                                 care.care_object_type,
                                 max(care.care_id) care_id
                   from APP___CLM__DBUSER.T_CLAIM_CARE      care,
                        APP___CLM__DBUSER.T_CLAIM_CASE      ca,
                        APP___CLM__DBUSER.T_CUSTOMER        cu,
                        APP___CLM__DBUSER.T_CONTRACT_MASTER po
                  where ca.case_id = care.case_id
                    and ca.insured_id = cu.customer_id
                    and po.case_id = ca.case_id
                    ]]>
		<include refid="queryPerserveCareServiceMsgCondition" /> 
        <![CDATA[ group by ca.case_id,
                           ca.case_no,
                           cu.customer_id,
                           cu.customer_name,
                           care.organ_code,
                           ca.end_case_time,
                           care.insert_time,
                           care.server_id,
                           care.care_object_id,
                           care.care_object_type order by care.insert_time) A
               where rownum <= #{LESS_NUM}) B
               where RN > #{GREATER_NUM}]]>
	</select>
	<select id="queryPerserveCareServiceMsgCountNoVisit"
		parameterType="java.util.Map" resultType="java.lang.Integer">
    <![CDATA[ 
        select count(1) from(
		select distinct ca.case_id,
                ca.case_no,
                cu.customer_id,
                cu.customer_name,
                care.organ_code,
                ca.end_case_time,
                care.insert_time,
                care.server_id,
                care.care_object_id,
                care.care_object_type,
                max(care.care_id) care_id
           from APP___CLM__DBUSER.T_CLAIM_CARE       care,
                APP___CLM__DBUSER.T_CLAIM_CASE      ca,
                APP___CLM__DBUSER.T_CUSTOMER        cu,
                APP___CLM__DBUSER.T_CONTRACT_MASTER  po
          where ca.case_id = care.case_id
            and ca.insured_id = cu.customer_id
            and po.case_id = ca.case_id
		]]>
		<include refid="queryPerserveCareServiceMsgCondition" /> 
		<![CDATA[group by ca.case_id,
                          ca.case_no,
                          cu.customer_id,
                          cu.customer_name,
                          care.organ_code,
                          ca.end_case_time,
                          care.insert_time,
                          care.server_id,
                          care.care_object_id,
                          care.care_object_type order by care.insert_time) ]]>
	</select>

	<!-- 根据赔案号（回访人，回访标志）查询关怀次数和下次回访日期 -->
	<select id="findCareVisitByCondition" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
		   select visit.case_id,
                visit.case_no,
                visit.is_succeed,
                visit.live_status,
                visit.visit_date,
                visit.care_object_type,
                visit.care_object_id,
                visit.contact_address,
                visit.contact_phone,
                visit.health_status,
                visit.family_status,
                visit.visit_content,
                visit.visite_by,
                visit.visitor_phone,
                visit.service_status,
                visit.is_accept_meg,
                visit.care_crowd,
                visit.care_id,
                visit.remind_date,
                visit.next_visit_date,
                visit.care_times,
                visit.remark,
                visit.server_type,
                visit.input_date,
                visit.input_by
           from APP___CLM__DBUSER.T_CLAIM_CARE care, APP___CLM__DBUSER.T_CLAIM_CARE_VISIT visit
          where care.care_id = visit.care_id
		]]>
		<if test="visite_by !=null and visite_by !=''"> 
	        	<![CDATA[  and visit.visite_by = #{visite_by} ]]>
		</if>
		<if test="return_visit_status !=null and return_visit_status !=''"> 
	        	<![CDATA[  and care.return_visit_status in ${return_visit_status} ]]>
		</if>
		<if test="case_id !=null and case_id !=''"> 
	        	<![CDATA[  and visit.case_id = #{case_id} ]]>
		</if>
		<if test="care_id !=null and care_id !=''"> 
	        	<![CDATA[  and visit.care_id = #{care_id} ]]>
		</if>
		<![CDATA[ order by visit.next_visit_date ]]>
	</select>
	<!-- 反馈质检结果 查询审批案件权限 -->
	<select id="queryPermissionTypeList" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
		  select A.PERMISSION_TYPE_ID,A.PERMISSION_TYPE_NAME
          from APP___CLM__DBUSER.T_UDMP_PERMISSION_TYPE A
          where A.permission_type_id in
          (select PERMISSION_TYPE_ID 
             from APP___CLM__DBUSER.T_UDMP_ROLE_PERMISSION B
            where B.role_id in (select role_id
                             from APP___CLM__DBUSER.T_UDMP_GROUP_ROLE C
                            where C.ROLE_GROUP_ID in
                                  (select ROLE_GROUP_ID
                                     from APP___CLM__DBUSER.T_UDMP_GROUP_USER D
                                    where 1=1
		]]>
		<if test="user_id !=null and user_id !=''"> 
	        	<![CDATA[  and D.user_id = #{user_id} ]]>
		</if>
		 <![CDATA[ ))) ]]>
	</select>
	<!--add by zhaoyq end -->
	<!--add by zhangjy_wb 根据多条件查询待回访数据 -->
	<select id="queryCompCareVistTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
		<![CDATA[ select count(1) from(
		           			select distinct a.case_id,
							                a.case_no,
							                a.end_case_time,
							                a.organ_code,
							                 c.care_id,
							                d.customer_name,
							                d.customer_certi_code,
							                g.next_visit_date,
							                g.care_times,
							                g.is_succeed
							  from APP___CLM__DBUSER.T_CLAIM_CARE c, APP___CLM__DBUSER.T_CUSTOMER d ]]>
		<if test="policy_code !=null and policy_code != ''">
			, APP___CLM__DBUSER.T_CONTRACT_MASTER b
		</if>
							 <![CDATA[ , APP___CLM__DBUSER.T_CLAIM_CASE a
							  left join APP___CLM__DBUSER.T_CLAIM_CARE_VISIT g
							    on g.case_id = a.case_id
							 where 1 = 1
							   and a.case_status = '80' ]]>
		<if test="policy_code !=null and policy_code != ''">
			and a.case_id = b.case_id
			and b.cur_flag= '1'
		</if>
							 <![CDATA[ and a.insured_id = d.customer_id
							   and a.case_no = c.case_no]]>
		<if test="user_id!=null  and user_id!=''">AND c.organ_code like (select ORGAN_CODE
			from APP___CLM__DBUSER.T_UDMP_USER
			WHERE USER_ID = #{user_id})||'%' </if>
		<if test="case_no !=null and case_no != ''"> 
			        	      	<![CDATA[ and a.case_no = #{case_no} ]]>
		</if>
		<if test="policy_code !=null and policy_code != ''"> 
			        	      	<![CDATA[ and b.policy_code = #{policy_code} ]]>
		</if>
		<if test="organ_code !=null and organ_code != ''"> 
			        	      	<![CDATA[ and a.organ_code = #{organ_code} ]]>
		</if>
		<if test="customer_name !=null and customer_name != ''"> 
			        	      	<![CDATA[ and d.customer_name = #{customer_name} ]]>
		</if>
		<if test="customer_certi_code !=null and customer_certi_code != ''"> 
			        	      	<![CDATA[ and d.customer_certi_code = #{customer_certi_code} ]]>
		</if>
		<if test="is_succeed !=null"> 
			        	      	<![CDATA[ and c.return_visit_status  = #{is_succeed} ]]>
		</if>
         <![CDATA[ )]]>
	</select>
	<select id="queryCompCareVistPage" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
           			select distinct a.case_id,
					                a.case_no,
					                a.end_case_time,
					                c.organ_code,
					                c.care_id,
					                d.customer_name,
					                d.customer_certi_code,
					                g.next_visit_date,
					                g.care_times,
					                g.is_succeed
					  from APP___CLM__DBUSER.T_CLAIM_CARE c, APP___CLM__DBUSER.T_CUSTOMER d ]]>
		<if test="policy_code !=null and policy_code != ''">
			, APP___CLM__DBUSER.T_CONTRACT_MASTER b
		</if>
					 <![CDATA[   ,APP___CLM__DBUSER.T_CLAIM_CASE a
					  left join APP___CLM__DBUSER.T_CLAIM_CARE_VISIT g
					    on g.case_id = a.case_id
					 where 1 = 1
					   and a.case_status = '80' ]]>
		<if test="policy_code !=null and policy_code != ''">
			and a.case_id = b.case_id
			and b.cur_flag= '1'
		</if>
					  <![CDATA[ and a.insured_id = d.customer_id
					   and a.case_no = c.case_no]]>
		<if test="user_id!=null  and user_id!=''">AND c.organ_code like (select ORGAN_CODE
			from APP___CLM__DBUSER.T_UDMP_USER
			WHERE USER_ID = #{user_id})||'%' </if>
		<if test="case_no !=null and case_no != ''"> 
	        	      	<![CDATA[ and a.case_no = #{case_no} ]]>
		</if>
		<if test="policy_code !=null and policy_code != ''"> 
	        	      	<![CDATA[ and b.policy_code = #{policy_code} ]]>
		</if>
		<if test="organ_code !=null and organ_code != ''"> 
	        	      	<![CDATA[ and c.organ_code = #{organ_code} ]]>
		</if>
		<if test="customer_name !=null and customer_name != ''"> 
	        	      	<![CDATA[ and d.customer_name = #{customer_name} ]]>
		</if>
		<if test="customer_certi_code !=null and customer_certi_code != ''"> 
	        	      	<![CDATA[ and d.customer_certi_code = #{customer_certi_code} ]]>
		</if>
		<if test="is_succeed !=null"> 
	        	      	<![CDATA[ and c.return_visit_status  = #{is_succeed} ]]>
		</if>
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
           <![CDATA[ order by B.IS_SUCCEED desc,B.END_CASE_TIME]]>
	</select>

	<!-- and by caoyy_wb 自动发起分次给付 -->
	<select id="queryBathUwHistory" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[
		SELECT C.INSTALMENT_ID,CLM.CASE_NO, P.POLICY_CODE, L.CUSTOMER_ID, P.BUSI_PROD_CODE, C.SURVEY_FLAG, C.PAY_NUM, C.PAY_DUE_DATE,CLM.ORGAN_CODE, C.PRINCIPAL,C.COMMU_PAY FROM APP___CLM__DBUSER.T_CLAIM_INSTALMENT C,APP___CLM__DBUSER.T_CLAIM_PAY P ,APP___CLM__DBUSER.T_INSURED_LIST L ,APP___CLM__DBUSER.T_CLAIM_CASE CLM 
					WHERE C.CLAIM_PAY_ID=P.CLAIM_PAY_ID AND P.CASE_ID=L.CASE_ID AND P.CASE_ID=CLM.CASE_ID AND L.CUR_FLAG = '1' ]]>
		<if test="case_no !=null and case_no != ''"> 
	       		<![CDATA[ and CLM.CASE_NO = #{case_no} ]]>
		</if>
		<if test="pay_due_date !=null and pay_due_date != ''"> 
	       		<![CDATA[ and C.PAY_DUE_DATE = #{pay_due_date} ]]>
		</if>
	</select>
	<!-- 维护作业配置查询 -->
	<select id="findQueryClaimTaskStaffInfoPage" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[
		select B.* from(
           select A.*,rownum RN from(
		 select distinct us.ORGAN_CODE,us.USER_NAME,staff.IS_WORKING,staff.LIST_ID, us.USER_ID,cap.AREA_CODE,staff.audit_to,
		 staff.approve_to,staff.audit_from,staff.approve_from, staff.approve_difficult_from,staff.approve_difficult_to,staff.easy_audit_permission,
		 staff.is_working_retask，
		 (CASE WHEN staff.audit_day_limit IS NULL THEN 'A'
     		WHEN staff.audit_day_limit like '' THEN 'A'
     		ELSE staff.audit_day_limit  END)  audit_day_limit,
     	 (CASE WHEN staff.approve_day_limit IS NULL THEN 'A'
     		WHEN staff.approve_day_limit like '' THEN 'A'
     		ELSE staff.approve_day_limit  END)  approve_day_limit
   			from APP___CLM__DBUSER.T_CLAIM_TASK_STAFF     staff,
        		 APP___CLM__DBUSER.T_UDMP_USER            us,
        		 APP___CLM__DBUSER.T_CLAIM_AREA_PERSON   cap
   			where staff.USER_ID(+) = us.USER_ID and cap.USER_ID(+) = us.USER_ID]]>
		<if test="user_name !=null and user_name != ''"> 
	       		<![CDATA[   and us.USER_NAME  like '%${user_name}%' ]]>
		</if>
		<if test="organ_code !=null and organ_code != ''"> 
	       		<![CDATA[ and us.ORGAN_CODE = #{organ_code} ]]>
		</if>
		<if test="area_code !=null and area_code != ''"> 
	       		<![CDATA[ and cap.AREA_CODE = #{area_code} ]]>
		</if>
		<if test="claim_role_id !=null and claim_role_id != ''"> 
	       		<![CDATA[ and staff.claim_role_id = #{claim_role_id} ]]>
		</if>
		<if test="user_id !=null"> 
	       		<![CDATA[ and us.USER_ID = #{user_id} ]]>
		</if>
		<if test="is_working !=null and is_working == 0"> 
	       		<![CDATA[ and staff.IS_WORKING = #{is_working}]]>
		</if>
		<if test="is_working !=null and is_working == 1"> 
	       		<![CDATA[ and staff.IS_WORKING = #{is_working}]]>
		</if>
		<if test="is_working_retask !=null and is_working_retask == 0"> 
	       		<![CDATA[ and staff.IS_WORKING_RETASK = #{is_working_retask}]]>
		</if>
		<if test="is_working_retask !=null and is_working_retask == 1"> 
	       		<![CDATA[ and staff.IS_WORKING_RETASK = #{is_working_retask}]]>
		</if>
		<if test="audit_day_limit !=null and audit_day_limit != ''"> 
	       		<![CDATA[ and staff.AUDIT_DAY_LIMIT = #{audit_day_limit} ]]>
		</if>
		<if test="approve_day_limit !=null and approve_day_limit != ''"> 
	       		<![CDATA[ and staff.APPROVE_DAY_LIMIT = #{approve_day_limit} ]]>
		</if>
	     <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>
	<select id="findQueryClaimTaskStaffInfoTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
    	     <![CDATA[
    	     SELECT COUNT(1) FROM (select distinct us.ORGAN_CODE,us.USER_NAME,staff.IS_WORKING,staff.LIST_ID, us.USER_ID,cap.AREA_CODE,staff.audit_to,
		 staff.approve_to,staff.audit_from,staff.approve_from, staff.approve_difficult_from,staff.approve_difficult_to,staff.easy_audit_permission,
		 staff.is_working_retask，
		 (CASE WHEN staff.audit_day_limit IS NULL THEN 'A'
     		WHEN staff.audit_day_limit like '' THEN 'A'
     		ELSE staff.audit_day_limit  END)  audit_day_limit,
     	 (CASE WHEN staff.approve_day_limit IS NULL THEN 'A'
     		WHEN staff.approve_day_limit like '' THEN 'A'
     		ELSE staff.approve_day_limit  END)  approve_day_limit
   			from APP___CLM__DBUSER.T_CLAIM_TASK_STAFF     staff,
        		 APP___CLM__DBUSER.T_UDMP_USER            us,
        		 APP___CLM__DBUSER.T_CLAIM_AREA_PERSON   cap
   			where staff.USER_ID(+) = us.USER_ID and cap.USER_ID(+) = us.USER_ID]]>
		<if test="user_name !=null and user_name != ''"> 
	       		<![CDATA[   and us.USER_NAME  like '%${user_name}%' ]]>
		</if>
		<if test="organ_code !=null and organ_code != ''"> 
	       		<![CDATA[ and us.ORGAN_CODE = #{organ_code} ]]>
		</if>
		<if test="area_code !=null and area_code != ''"> 
	       		<![CDATA[ and cap.AREA_CODE = #{area_code} ]]>
		</if>
		<if test="claim_role_id !=null and claim_role_id != ''"> 
	       		<![CDATA[ and staff.claim_role_id = #{claim_role_id} ]]>
		</if>
		<if test="user_id !=null"> 
	       		<![CDATA[ and us.USER_ID = #{user_id} ]]>
		</if>
		<if test="is_working !=null and is_working == 0"> 
	       		<![CDATA[ and staff.IS_WORKING = #{is_working}]]>
		</if>
		<if test="is_working !=null and is_working == 1"> 
	       		<![CDATA[ and staff.IS_WORKING = #{is_working}]]>
		</if>
		<if test="is_working_retask !=null and is_working_retask == 0"> 
	       		<![CDATA[ and staff.IS_WORKING_RETASK = #{is_working_retask}]]>
		</if>
		<if test="is_working_retask !=null and is_working_retask == 1"> 
	       		<![CDATA[ and staff.IS_WORKING_RETASK = #{is_working_retask}]]>
		</if>
		<if test="audit_day_limit !=null and audit_day_limit != ''"> 
	       		<![CDATA[ and staff.AUDIT_DAY_LIMIT = #{audit_day_limit} ]]>
		</if>
		<if test="approve_day_limit !=null and approve_day_limit != ''"> 
	       		<![CDATA[ and staff.APPROVE_DAY_LIMIT = #{approve_day_limit} ]]>
		</if>
		<![CDATA[ ) A]]>
	</select>
	<!-- and by caoyy_wb 手工发起分次给付 -->
	<select id="queryUwHistory" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[
		select B.* from(
           select A.*,rownum RN from(
		SELECT C.INSTAL_TYPE, C.INSTALMENT_ID,CLM.CASE_NO, P.POLICY_CODE, L.CUSTOMER_ID, P.BUSI_PROD_CODE, C.SURVEY_FLAG, C.PAY_NUM, C.PAY_DUE_DATE,CLM.ORGAN_CODE, C.PRINCIPAL FROM APP___CLM__DBUSER.T_CLAIM_INSTALMENT C,APP___CLM__DBUSER.T_CLAIM_PAY P ,APP___CLM__DBUSER.T_INSURED_LIST L ,APP___CLM__DBUSER.T_CLAIM_CASE CLM,APP___CLM__DBUSER.T_CLAIM_LIAB LIA 
					WHERE C.CLAIM_PAY_ID=P.CLAIM_PAY_ID AND P.CASE_ID = L.CASE_ID AND LIA.CASE_ID = CLM.CASE_ID AND LIA.CLAIM_LIAB_ID = C.CLAIM_LIAB_ID AND LIA.IS_INSTALMENT = '1' AND P.CASE_ID=CLM.CASE_ID AND L.CUR_FLAG='1' AND P.POLICY_CODE = L.POLICY_CODE AND CLM.CASE_STATUS = '80' and C.INSTAL_STATUS =0 ]]>
		<if test="organ_code !=null and organ_code != ''"> 
	       		<![CDATA[ and CLM.ORGAN_CODE = #{organ_code} ]]>
		</if>
		<if test="pay_due_date !=null and pay_due_date != ''"> 
	       		<![CDATA[ and C.PAY_DUE_DATE = #{pay_due_date} ]]>
		</if>
		<if test="policy_code !=null and policy_code != ''"> 
	       		<![CDATA[ and P.POLICY_CODE = #{policy_code} ]]>
		</if>
		<if test="case_no !=null and case_no != ''"> 
	       		<![CDATA[ and CLM.CASE_NO = #{case_no} ]]>
		</if>
		<if test="busi_prod_code !=null and busi_prod_code != ''"> 
	       		<![CDATA[ and P.BUSI_PROD_CODE = #{busi_prod_code} ]]>
		</if>
		<if test="customer_id !=null and customer_id != ''"> 
	       		<![CDATA[ and L.CUSTOMER_ID = #{customer_id} ]]>
		</if>
		<if test="instal_type !=null and instal_type != ''"> 
	       		<![CDATA[ and C.INSTAL_TYPE = #{instal_type} ]]>
		</if>
	      <![CDATA[ order by c.pay_num ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>

	<select id="queryUwHistoryTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    	     <![CDATA[
    	     SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_INSTALMENT C,APP___CLM__DBUSER.T_CLAIM_PAY P ,APP___CLM__DBUSER.T_INSURED_LIST L ,APP___CLM__DBUSER.T_CLAIM_CASE CLM,APP___CLM__DBUSER.T_CLAIM_LIAB LIA 
					WHERE C.CLAIM_PAY_ID=P.CLAIM_PAY_ID AND P.CASE_ID = L.CASE_ID AND LIA.CASE_ID = CLM.CASE_ID AND LIA.CLAIM_LIAB_ID = C.CLAIM_LIAB_ID AND LIA.IS_INSTALMENT = '1' AND P.CASE_ID=CLM.CASE_ID AND L.CUR_FLAG='1' AND P.POLICY_CODE = L.POLICY_CODE AND CLM.CASE_STATUS = '80' and C.INSTAL_STATUS =0  ]]>
		<if test="organ_code !=null and organ_code != ''"> 
	       		<![CDATA[ and CLM.ORGAN_CODE = #{organ_code} ]]>
		</if>
		<if test="pay_due_date !=null and pay_due_date != ''"> 
	       		<![CDATA[ and C.PAY_DUE_DATE = #{pay_due_date} ]]>
		</if>
		<if test="policy_code !=null and policy_code != ''"> <![CDATA[ and 
			P.POLICY_CODE = #{policy_code} ]]>
		</if>
		<if test="case_no !=null and case_no != ''"> 
	       		<![CDATA[ and CLM.CASE_NO = #{case_no} ]]>
		</if>
		<if test="busi_prod_code !=null and busi_prod_code != ''"> 
	       		<![CDATA[ and P.BUSI_PROD_CODE = #{busi_prod_code} ]]>
		</if>
		<if test="customer_id !=null and customer_id != ''"> 
	       		<![CDATA[ and L.CUSTOMER_ID = #{customer_id} ]]>
		</if>
		<if test="instal_type !=null and instal_type != ''"> 
	       		<![CDATA[ and C.INSTAL_TYPE = #{instal_type} ]]>
		</if>
	</select>
	<!-- caoyy 出险结果查询 -->
	<select id="queryShowAccResultForPage" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[
		select B.* from(
           select A.*,rownum RN from(
		SELECT A1.CODE,A1.NAME,A2.CODE AS accident22Code, A2.NAME AS accident22Name FROM APP___CLM__DBUSER.T_ACCIDENT1 A1 , APP___CLM__DBUSER.T_ACCIDENT2 A2 WHERE A1.CODE = A2.RELA_CODE]]>
		<if test="code !=null and code != ''"> 
	       		<![CDATA[ and A1.code  like upper('%${code}%') ]]>
		</if>
		<if test="name !=null and name != ''"> 
	       		<![CDATA[ and A1.name like upper('%${name}%') ]]>
		</if>
		<if test="accident2Code !=null and accident2Code != ''"> 
	       		<![CDATA[ and A2.code  like upper('%${accident2Code}%')]]>
		</if>
		<if test="accident2Name !=null and accident2Name != ''"> 
	       		<![CDATA[ and A2.NAME like upper('%${accident2Name}%') ]]>
		</if>
		<if test="rela_code !=null"> 
	       		<![CDATA[ and A1.RELA_CODE = ${rela_code} ]]>
		</if>
	      <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>
	<!-- caoyy 出险结果查询条数 -->
	<select id="queryShowAccResultTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    	     <![CDATA[
    	     SELECT COUNT(1) FROM APP___CLM__DBUSER.T_ACCIDENT1 A1 , APP___CLM__DBUSER.T_ACCIDENT2 A2 WHERE A1.CODE = A2.RELA_CODE]]>
		<if test="code !=null and code != ''"> 
	       		<![CDATA[ and A1.code like upper('%${code}%') ]]>
		</if>
		<if test="name !=null and name != ''"> 
	       		<![CDATA[ and A1.name like upper('%${name}%') ]]>
		</if>
		<if test="accident2Code !=null and accident2Code != ''"> 
	       		<![CDATA[ and A2.code like upper('%${accident2Code}%') ]]>
		</if>
		<if test="accident2Name !=null and accident2Name != ''"> 
	       		<![CDATA[ and A2.name like upper('%${accident2Name}%') ]]>
		</if>
		<if test="rela_code !=null"> 
	       		<![CDATA[ and A1.RELA_CODE = ${rela_code} ]]>
		</if>
	</select>
	<select id="queryAccident2Info" parameterType="java.util.Map"
		resultType="java.util.Map">
    	     <![CDATA[
    	     select A2.RELA_CODE, A2.CODE, A2.NAME from APP___CLM__DBUSER.T_ACCIDENT2 a2 where a2.rela_code = #{rela_code}]]>
	</select>
	<select id="findAccDesc" parameterType="java.util.Map"
		resultType="java.util.Map">
    	     <![CDATA[
    	      SELECT A1.NAME FROM APP___CLM__DBUSER.T_ACCIDENT1 A1 WHERE A1.CODE = #{code}]]>
	</select>
	<select id="findAccident2" parameterType="java.util.Map"
		resultType="java.util.Map">
    	     <![CDATA[
    	      SELECT A2.NAME FROM APP___CLM__DBUSER.T_ACCIDENT2 A2 WHERE A2.CODE = #{code}]]>
	</select>
	<!-- 制定质检计划 -->
	<select id="queryAllClaimAfcPlanRela" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[
		select clmitem.item_name,
       		   clmGist.gist_desc,
       		   tclmrela.role_name,
       		   tclmrela.deduct_standard,
       		   tclmrela.list_id,
       		   tclmrela.gist_id,
       		   tclmrela.item_id
  		  from APP___CLM__DBUSER.T_CLAIM_AFC_PLAN      tclm,
               APP___CLM__DBUSER.T_CLAIM_AFC_PLAN_RELA tclmrela,
       		   APP___CLM__DBUSER.T_CLAIM_AFC_ITEM      clmitem,
       		   APP___CLM__DBUSER.T_CLAIM_AFC_GIST      clmGist
         where tclm.plan_id = tclmrela.plan_id
   		   and tclmrela.item_id = clmitem.item_id
    	   and tclmrela.gist_id = clmGist.gist_Id 
   		   and tclm.plan_id = #{plan_id}]]>
	</select>
	<!-- 根据所有条件抽取事后质检任务 -->
	<select id="queryExtractClaimCase1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT TCC.* FROM (]]>
		<!-- 自动简易案件 11、非自动简易案件 12、普通案件13、诉讼案件14、疑难案件15、调查案件 16、自动审批案件  17 -->
		<if test="caseFlagCode == '' and  DCFlag == '' and ZDJYFlag == '' and ZDSPFlag =='' and  FZDJYFlag == ''">
			<![CDATA[ SELECT distinct C1.CASE_ID,C1.CASE_NO,C1.CASE_FLAG,C1.ORGAN_CODE,C1.APPROVER_ID,C1.REGISTER_ID,C1.AUDITOR_ID,C1.INSERT_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE C1 ]]>
			<if test="claimTypes !=null and claimTypes != ''"> ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC</if>
			<if test="accReasons !=null and accReasons != ''"> ,APP___CLM__DBUSER.T_CLAIM_ACCIDENT CA</if>
			<if test="productCodes !=null and productCodes != ''"> ,APP___CLM__DBUSER.T_CLAIM_LIAB CL</if>
			<if test="certiCode !=null and certiCode != ''"> ,APP___CLM__DBUSER.T_CUSTOMER TC</if>
			<if test="areaCodes !=null and areaCodes != ''"> ,APP___CLM__DBUSER. T_CLAIM_AREA_ORGAN AO</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')"> ,APP___CLM__DBUSER. T_CONTRACT_MASTER CM</if>
	    	<![CDATA[WHERE 1=1
	    				and C1.case_status = #{case_status}
	    				and C1.end_case_time between #{approve_start_date}
	    				and #{approve_end_date}+1]]>
 			<if test="caseFlagCode != null and caseFlagCode != ''">and C1.CASE_FLAG in (${caseFlagCode})</if>
			<!-- 如果caseFlagCode为空，则使得本集合为空 -->
			<!-- <if test="caseFlagCode == null || caseFlagCode == ''">and C1.CASE_FLAG in (0)</if> -->
			<if test="claimTypes !=null and claimTypes != ''"> AND C1.CASE_ID = SC.CASE_ID AND SC.CLAIM_TYPE IN (${claimTypes})</if>
			<if test="accReasons !=null and accReasons != ''"> AND C1.ACCIDENT_ID = CA.ACCIDENT_ID AND CA.ACC_REASON IN (${accReasons})</if>
			<if test="productCodes !=null and productCodes != ''"> AND C1.CASE_ID = CL.CASE_ID AND CL.BUSI_PROD_CODE IN (${productCodes})</if>
			<if test="customerId !=null and customerId != ''"> AND C1.INSURED_ID = #{customerId}</if>
			<if test="certiCode !=null and certiCode != ''"> AND C1.INSURED_ID = TC.CUSTOMER_ID AND TC.CUSTOMER_CERTI_CODE = #{certiCode}</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')">AND C1.CASE_ID = CM.CASE_ID</if>
			<if test="organCodes !=null and organCodes != ''"> AND CM.ORGAN_CODE IN (${organCodes})</if>
			<if test="areaCodes !=null and areaCodes != ''"> AND C1.ORGAN_CODE = AO.ORGAN_CODE AND AO.AREA_CODE IN (${areaCodes})</if>
			<if test="validdate !=null and validdate != ''">  AND CM.VALIDDATE_DATE = #{validdate}</if>
			<if test="orgs !=null and orgs != ''"> AND C1.ORGAN_CODE in (${orgs})</if>
			<if test="oprs !=null and oprs != ''"> AND (C1.APPROVER_ID IN(${oprs}) OR C1.REGISTER_ID IN(${oprs}) OR C1.AUDITOR_ID IN (${oprs}))</if>
			<if test="auths !=null and auths != ''"> AND ((C1.APPROVER_ID IS NOT NULL AND C1.APPROVER_ID != 0) OR (C1.REGISTER_ID IS NOT NULL AND C1.REGISTER_ID != 0) OR (C1.AUDITOR_ID IS NOT NULL AND C1.AUDITOR_ID != 0))</if>
		</if>
		<!-- 普通案件13、诉讼案件14、疑难案件15 -->
		<if test="caseFlagCode != null and caseFlagCode != ''">
    	<![CDATA[SELECT distinct C.CASE_ID,C.CASE_NO,C.CASE_FLAG,C.ORGAN_CODE,C.APPROVER_ID,C.REGISTER_ID,C.AUDITOR_ID,C.INSERT_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE C ]]>
			<if test="claimTypes !=null and claimTypes != ''"> ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC</if>
			<if test="accReasons !=null and accReasons != ''"> ,APP___CLM__DBUSER.T_CLAIM_ACCIDENT CA</if>
			<if test="productCodes !=null and productCodes != ''"> ,APP___CLM__DBUSER.T_CLAIM_LIAB CL</if>
			<if test="certiCode !=null and certiCode != ''"> ,APP___CLM__DBUSER.T_CUSTOMER TC</if>
			<if test="areaCodes !=null and areaCodes != ''"> ,APP___CLM__DBUSER. T_CLAIM_AREA_ORGAN AO</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')"> ,APP___CLM__DBUSER. T_CONTRACT_MASTER CM</if>
    		<![CDATA[WHERE C.case_status = #{case_status} ]]>
				<if test="caseFlagCode != null and caseFlagCode != ''">and C.CASE_FLAG in (${caseFlagCode})</if>
    			<![CDATA[	and C.end_case_time between #{approve_start_date}
    				and #{approve_end_date}+1]]>
			<if test="claimTypes !=null and claimTypes != ''"> AND C.CASE_ID = SC.CASE_ID AND SC.CLAIM_TYPE IN (${claimTypes})</if>
			<if test="accReasons !=null and accReasons != ''"> AND C.ACCIDENT_ID = CA.ACCIDENT_ID AND CA.ACC_REASON IN (${accReasons})</if>
			<if test="productCodes !=null and productCodes != ''"> AND C.CASE_ID = CL.CASE_ID AND CL.BUSI_PROD_CODE IN (${productCodes})</if>
			<if test="customerId !=null and customerId != ''"> AND C.INSURED_ID = #{customerId}</if>
			<if test="certiCode !=null and certiCode != ''"> AND C.INSURED_ID = TC.CUSTOMER_ID AND TC.CUSTOMER_CERTI_CODE = #{certiCode}</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')">AND C.CASE_ID = CM.CASE_ID</if>
			<if test="organCodes !=null and organCodes != ''"> AND CM.ORGAN_CODE IN (${organCodes})</if>
			<if test="areaCodes !=null and areaCodes != ''"> AND C.ORGAN_CODE = AO.ORGAN_CODE AND AO.AREA_CODE IN (${areaCodes})</if>
			<if test="validdate !=null and validdate != ''"> AND CM.VALIDDATE_DATE = #{validdate}</if>
			<if test="orgs !=null and orgs != ''"> AND C.ORGAN_CODE in (${orgs})</if>
			<if test="oprs !=null and oprs != ''"> AND (C.APPROVER_ID IN(${oprs}) OR C.REGISTER_ID IN(${oprs}) OR C.AUDITOR_ID IN (${oprs}))</if>
		</if>
		<!-- 调查案件 16 -->
		<if test="DCFlag !=null and DCFlag != ''"> 
			<if test="caseFlagCode != null and caseFlagCode != ''">
	    		<![CDATA[	UNION	]]>
	    	</if>
    	<![CDATA[SELECT distinct C2.CASE_ID,C2.CASE_NO,C2.CASE_FLAG,C2.ORGAN_CODE,C2.APPROVER_ID,C2.REGISTER_ID,C2.AUDITOR_ID,C2.INSERT_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE C2, 
    							  APP___CLM__DBUSER.T_SURVEY_APPLY SA ]]>
			<if test="claimTypes !=null and claimTypes != ''"> ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC</if>
			<if test="accReasons !=null and accReasons != ''"> ,APP___CLM__DBUSER.T_CLAIM_ACCIDENT CA</if>
			<if test="productCodes !=null and productCodes != ''"> ,APP___CLM__DBUSER.T_CLAIM_LIAB CL</if>
			<if test="certiCode !=null and certiCode != ''"> ,APP___CLM__DBUSER.T_CUSTOMER TC</if>
			<if test="areaCodes !=null and areaCodes != ''"> ,APP___CLM__DBUSER. T_CLAIM_AREA_ORGAN AO</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')"> ,APP___CLM__DBUSER. T_CONTRACT_MASTER CM</if>
    	<![CDATA[WHERE 
    				C2.CASE_ID = SA.CASE_ID
    				and SA.SURVEY_STATUS <> '3'
    				and C2.case_status = #{case_status}
    				and C2.end_case_time between #{approve_start_date}
    				and #{approve_end_date}+1]]>
			<if test="claimTypes !=null and claimTypes != ''"> AND C2.CASE_ID = SC.CASE_ID AND SC.CLAIM_TYPE IN (${claimTypes})</if>
			<if test="accReasons !=null and accReasons != ''"> AND C2.ACCIDENT_ID = CA.ACCIDENT_ID AND CA.ACC_REASON IN (${accReasons})</if>
			<if test="productCodes !=null and productCodes != ''"> AND C2.CASE_ID = CL.CASE_ID AND CL.BUSI_PROD_CODE IN (${productCodes})</if>
			<if test="customerId !=null and customerId != ''"> AND C2.INSURED_ID = #{customerId}</if>
			<if test="certiCode !=null and certiCode != ''"> AND C2.INSURED_ID = TC.CUSTOMER_ID AND TC.CUSTOMER_CERTI_CODE = #{certiCode}</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')">AND C2.CASE_ID = CM.CASE_ID</if>
			<if test="organCodes !=null and organCodes != ''"> AND CM.ORGAN_CODE IN (${organCodes})</if>
			<if test="areaCodes !=null and areaCodes != ''"> AND C2.ORGAN_CODE = AO.ORGAN_CODE AND AO.AREA_CODE IN (${areaCodes})</if>
			<if test="validdate !=null and validdate != ''"> AND CM.VALIDDATE_DATE = #{validdate}</if>
			<if test="orgs !=null and orgs != ''"> AND C2.ORGAN_CODE in (${orgs})</if>
			<if test="oprs !=null and oprs != ''"> AND (C2.APPROVER_ID IN(${oprs}) OR C2.REGISTER_ID IN(${oprs}) OR C2.AUDITOR_ID IN (${oprs}))</if>
			<if test="auths !=null and auths != ''"> AND ((C2.APPROVER_ID IS NOT NULL AND C2.APPROVER_ID != 0) OR (C2.REGISTER_ID IS NOT NULL AND C2.REGISTER_ID != 0) OR (C2.AUDITOR_ID IS NOT NULL AND C2.AUDITOR_ID != 0))</if>
		</if>

		<!-- 自动简易案件 11 -->
		<if test="ZDJYFlag !=null and ZDJYFlag != ''">
			<choose>
				<when test="DCFlag !=null and DCFlag != ''">
					<![CDATA[	UNION	]]>
				</when>
				<when test="caseFlagCode != null and caseFlagCode != ''">
					<![CDATA[	UNION	]]>
				</when>
				<otherwise>
				</otherwise>
			</choose>
    	<![CDATA[SELECT distinct C3.CASE_ID,C3.CASE_NO,C3.CASE_FLAG,C3.ORGAN_CODE,C3.APPROVER_ID,C3.REGISTER_ID,C3.AUDITOR_ID,C3.INSERT_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE C3 ]]>
			<if test="claimTypes !=null and claimTypes != ''"> ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC</if>
			<if test="accReasons !=null and accReasons != ''"> ,APP___CLM__DBUSER.T_CLAIM_ACCIDENT CA</if>
			<if test="productCodes !=null and productCodes != ''"> ,APP___CLM__DBUSER.T_CLAIM_LIAB CL</if>
			<if test="certiCode !=null and certiCode != ''"> ,APP___CLM__DBUSER.T_CUSTOMER TC</if>
			<if test="areaCodes !=null and areaCodes != ''"> ,APP___CLM__DBUSER. T_CLAIM_AREA_ORGAN AO</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')"> ,APP___CLM__DBUSER. T_CONTRACT_MASTER CM</if>
    		<![CDATA[WHERE C3.case_status = #{case_status} ]]>
				and C3.AUDITOR_ID IS NULL
				and  C3.CASE_FLAG IS NULL
    			<![CDATA[	and C3.end_case_time between #{approve_start_date}
    				and #{approve_end_date}+1]]>
			<if test="claimTypes !=null and claimTypes != ''"> AND C3.CASE_ID = SC.CASE_ID AND SC.CLAIM_TYPE IN (${claimTypes})</if>
			<if test="accReasons !=null and accReasons != ''"> AND C3.ACCIDENT_ID = CA.ACCIDENT_ID AND CA.ACC_REASON IN (${accReasons})</if>
			<if test="productCodes !=null and productCodes != ''"> AND C3.CASE_ID = CL.CASE_ID AND CL.BUSI_PROD_CODE IN (${productCodes})</if>
			<if test="customerId !=null and customerId != ''"> AND C3.INSURED_ID = #{customerId}</if>
			<if test="certiCode !=null and certiCode != ''"> AND C3.INSURED_ID = TC.CUSTOMER_ID AND TC.CUSTOMER_CERTI_CODE = #{certiCode}</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')">AND C3.CASE_ID = CM.CASE_ID</if>
			<if test="organCodes !=null and organCodes != ''"> AND CM.ORGAN_CODE IN (${organCodes})</if>
			<if test="areaCodes !=null and areaCodes != ''"> AND C3.ORGAN_CODE = AO.ORGAN_CODE AND AO.AREA_CODE IN (${areaCodes})</if>
			<if test="validdate !=null and validdate != ''"> AND CM.VALIDDATE_DATE = #{validdate}</if>
			<if test="orgs !=null and orgs != ''"> AND C3.ORGAN_CODE in (${orgs})</if>
			<if test="oprs !=null and oprs != ''"> AND (C3.APPROVER_ID IN(${oprs}) OR C3.REGISTER_ID IN(${oprs}) OR C3.AUDITOR_ID IN (${oprs}))</if>
		</if>
		
		<!-- 自动审批案件  17 -->
		<if test="ZDSPFlag != null and ZDSPFlag !=''">
		<choose>
			<when test="ZDJYFlag !=null and ZDJYFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="DCFlag !=null and DCFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="caseFlagCode != null and caseFlagCode != ''">
				<![CDATA[	UNION	]]>
			</when>
			<otherwise>
			</otherwise>
		</choose>
    	<![CDATA[SELECT distinct C6.CASE_ID,C6.CASE_NO,C6.CASE_FLAG,C6.ORGAN_CODE,C6.APPROVER_ID,C6.REGISTER_ID,C6.AUDITOR_ID,C6.INSERT_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE C6 ]]>
			<if test="claimTypes !=null and claimTypes != ''"> ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC</if>
			<if test="accReasons !=null and accReasons != ''"> ,APP___CLM__DBUSER.T_CLAIM_ACCIDENT CA</if>
			<if test="productCodes !=null and productCodes != ''"> ,APP___CLM__DBUSER.T_CLAIM_LIAB CL</if>
			<if test="certiCode !=null and certiCode != ''"> ,APP___CLM__DBUSER.T_CUSTOMER TC</if>
			<if test="areaCodes !=null and areaCodes != ''"> ,APP___CLM__DBUSER. T_CLAIM_AREA_ORGAN AO</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')"> ,APP___CLM__DBUSER. T_CONTRACT_MASTER CM</if>
    	<![CDATA[WHERE C6.case_status = #{case_status} ]]>
				and C6.APPROVER_ID IS NULL
				and  C6.CASE_FLAG IS NOT NULL
    			<![CDATA[	and C6.end_case_time between #{approve_start_date}
    				and #{approve_end_date}+1]]>
			<if test="claimTypes !=null and claimTypes != ''"> AND C6.CASE_ID = SC.CASE_ID AND SC.CLAIM_TYPE IN (${claimTypes})</if>
			<if test="accReasons !=null and accReasons != ''"> AND C6.ACCIDENT_ID = CA.ACCIDENT_ID AND CA.ACC_REASON IN (${accReasons})</if>
			<if test="productCodes !=null and productCodes != ''"> AND C6.CASE_ID = CL.CASE_ID AND CL.BUSI_PROD_CODE IN (${productCodes})</if>
			<if test="customerId !=null and customerId != ''"> AND C6.INSURED_ID = #{customerId}</if>
			<if test="certiCode !=null and certiCode != ''"> AND C6.INSURED_ID = TC.CUSTOMER_ID AND TC.CUSTOMER_CERTI_CODE = #{certiCode}</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')">AND C6.CASE_ID = CM.CASE_ID</if>
			<if test="organCodes !=null and organCodes != ''"> AND CM.ORGAN_CODE IN (${organCodes})</if>
			<if test="areaCodes !=null and areaCodes != ''"> AND C6.ORGAN_CODE = AO.ORGAN_CODE AND AO.AREA_CODE IN (${areaCodes})</if>
			<if test="validdate !=null and validdate != ''"> AND CM.VALIDDATE_DATE = #{validdate}</if>
			<if test="orgs !=null and orgs != ''"> AND C6.ORGAN_CODE in (${orgs})</if>
			<if test="oprs !=null and oprs != ''"> AND (C6.APPROVER_ID IN(${oprs}) OR C6.REGISTER_ID IN(${oprs}) OR C6.AUDITOR_ID IN (${oprs}))</if>
		</if>
		
		<!-- 非自动简易案件 12 -->
		<if test="FZDJYFlag !=null and FZDJYFlag != ''">
    	<choose>
    		<when test="ZDSPFlag !=null and ZDSPFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="ZDJYFlag !=null and ZDJYFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="DCFlag !=null and DCFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="caseFlagCode != null and caseFlagCode != ''">
				<![CDATA[	UNION	]]>
			</when>
			<otherwise>
			</otherwise>
		</choose>
    	<![CDATA[SELECT distinct C4.CASE_ID,C4.CASE_NO,C4.CASE_FLAG,C4.ORGAN_CODE,C4.APPROVER_ID,C4.REGISTER_ID,C4.AUDITOR_ID,C4.INSERT_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE C4 ]]>
			<if test="claimTypes !=null and claimTypes != ''"> ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC</if>
			<if test="accReasons !=null and accReasons != ''"> ,APP___CLM__DBUSER.T_CLAIM_ACCIDENT CA</if>
			<if test="productCodes !=null and productCodes != ''"> ,APP___CLM__DBUSER.T_CLAIM_LIAB CL</if>
			<if test="certiCode !=null and certiCode != ''"> ,APP___CLM__DBUSER.T_CUSTOMER TC</if>
			<if test="areaCodes !=null and areaCodes != ''"> ,APP___CLM__DBUSER. T_CLAIM_AREA_ORGAN AO</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')"> ,APP___CLM__DBUSER. T_CONTRACT_MASTER CM</if>
    	<![CDATA[WHERE C4.case_status = #{case_status}
    				AND C4.CASE_FLAG IS NULL
    				AND C4.EASY_AUDITOR_ID IS NOT NULL
    				and C4.end_case_time between #{approve_start_date}
    				and #{approve_end_date}+1]]>
			<if test="claimTypes !=null and claimTypes != ''"> AND C4.CASE_ID = SC.CASE_ID AND SC.CLAIM_TYPE IN (${claimTypes})</if>
			<if test="accReasons !=null and accReasons != ''"> AND C4.ACCIDENT_ID = CA.ACCIDENT_ID AND CA.ACC_REASON IN (${accReasons})</if>
			<if test="productCodes !=null and productCodes != ''"> AND C4.CASE_ID = CL.CASE_ID AND CL.BUSI_PROD_CODE IN (${productCodes})</if>
			<if test="customerId !=null and customerId != ''"> AND C4.INSURED_ID = #{customerId}</if>
			<if test="certiCode !=null and certiCode != ''"> AND C4.INSURED_ID = TC.CUSTOMER_ID AND TC.CUSTOMER_CERTI_CODE = #{certiCode}</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')">AND C4.CASE_ID = CM.CASE_ID</if>
			<if test="organCodes !=null and organCodes != ''"> AND CM.ORGAN_CODE IN (${organCodes})</if>
			<if test="areaCodes !=null and areaCodes != ''"> AND C4.ORGAN_CODE = AO.ORGAN_CODE AND AO.AREA_CODE IN (${areaCodes})</if>
			<if test="validdate !=null and validdate != ''"> AND CM.VALIDDATE_DATE = #{validdate}</if>
			<if test="orgs !=null and orgs != ''"> AND C4.ORGAN_CODE in (${orgs})</if>
			<if test="oprs !=null and oprs != ''"> AND (C4.APPROVER_ID IN(${oprs}) OR C4.REGISTER_ID IN(${oprs}) OR C4.AUDITOR_ID IN (${oprs}))</if>
			<if test="auths !=null and auths != ''"> AND ((C4.APPROVER_ID IS NOT NULL AND C4.APPROVER_ID != 0) OR (C4.REGISTER_ID IS NOT NULL AND C4.REGISTER_ID != 0) OR (C4.AUDITOR_ID IS NOT NULL AND C4.AUDITOR_ID != 0))</if>
		</if>
		
		
		<!-- 直赔案件 18 -->
		<if test="FZDJYFlag !=null and FZDJYFlag != ''">
    	<choose>
    		<when test="ZDSPFlag !=null and ZDSPFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="ZDJYFlag !=null and ZDJYFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="DCFlag !=null and DCFlag != ''">
				<![CDATA[	UNION	]]>
			</when>
			<when test="caseFlagCode != null and caseFlagCode != ''">
				<![CDATA[	UNION	]]>
			</when>
			<otherwise>
			</otherwise>
		</choose>
    	<![CDATA[SELECT distinct C8.CASE_ID,C8.CASE_NO,C8.CASE_FLAG,C8.ORGAN_CODE,C8.APPROVER_ID,C8.REGISTER_ID,C8.AUDITOR_ID,C8.INSERT_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE C8 ]]>
			<if test="claimTypes !=null and claimTypes != ''"> ,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC</if>
			<if test="accReasons !=null and accReasons != ''"> ,APP___CLM__DBUSER.T_CLAIM_ACCIDENT CA</if>
			<if test="productCodes !=null and productCodes != ''"> ,APP___CLM__DBUSER.T_CLAIM_LIAB CL</if>
			<if test="certiCode !=null and certiCode != ''"> ,APP___CLM__DBUSER.T_CUSTOMER TC</if>
			<if test="areaCodes !=null and areaCodes != ''"> ,APP___CLM__DBUSER. T_CLAIM_AREA_ORGAN AO</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')"> ,APP___CLM__DBUSER. T_CONTRACT_MASTER CM</if>
    	<![CDATA[WHERE C8.case_status = #{case_status}
    				AND C8.CHANNEL_CODE = '11'
    				and C8.end_case_time between #{approve_start_date}
    				and #{approve_end_date}+1]]>
			<if test="claimTypes !=null and claimTypes != ''"> AND C8.CASE_ID = SC.CASE_ID AND SC.CLAIM_TYPE IN (${claimTypes})</if>
			<if test="accReasons !=null and accReasons != ''"> AND C8.ACCIDENT_ID = CA.ACCIDENT_ID AND CA.ACC_REASON IN (${accReasons})</if>
			<if test="productCodes !=null and productCodes != ''"> AND C8.CASE_ID = CL.CASE_ID AND CL.BUSI_PROD_CODE IN (${productCodes})</if>
			<if test="customerId !=null and customerId != ''"> AND C8.INSURED_ID = #{customerId}</if>
			<if test="certiCode !=null and certiCode != ''"> AND C8.INSURED_ID = TC.CUSTOMER_ID AND TC.CUSTOMER_CERTI_CODE = #{certiCode}</if>
			<if test="(validdate !=null and validdate != '') or (organCodes !=null and organCodes != '')">AND C8.CASE_ID = CM.CASE_ID</if>
			<if test="organCodes !=null and organCodes != ''"> AND CM.ORGAN_CODE IN (${organCodes})</if>
			<if test="areaCodes !=null and areaCodes != ''"> AND C8.ORGAN_CODE = AO.ORGAN_CODE AND AO.AREA_CODE IN (${areaCodes})</if>
			<if test="validdate !=null and validdate != ''"> AND CM.VALIDDATE_DATE = #{validdate}</if>
			<if test="orgs !=null and orgs != ''"> AND C8.ORGAN_CODE in (${orgs})</if>
			<if test="oprs !=null and oprs != ''"> AND (C8.APPROVER_ID IN(${oprs}) OR C8.REGISTER_ID IN(${oprs}) OR C8.AUDITOR_ID IN (${oprs}))</if>
			<if test="auths !=null and auths != ''"> AND ((C8.APPROVER_ID IS NOT NULL AND C8.APPROVER_ID != 0) OR (C8.REGISTER_ID IS NOT NULL AND C8.REGISTER_ID != 0) OR (C8.AUDITOR_ID IS NOT NULL AND C8.AUDITOR_ID != 0))</if>
		</if>
		
    	<![CDATA[) TCC WHERE ROWNUM <= 500 ORDER BY TCC.INSERT_TIME DESC]]>
	</select>

	<!-- 抽取事后质检任务 检查了下这个sql在代码里边没有用到，如果要复用请慎重考虑end_case_time这个字段的条件问题，因为这个字段是带有时分秒的，所以很大可能传进来的参数当天是出不来的 -->
	<select id="queryExtractClaimCase" resultType="java.util.Map"
		parameterType="java.util.Map">
    	<![CDATA[ SELECT A.CASE_ID,
       A.ACCIDENT_ID,
       A.CASE_NO,
       A.INSURED_ID,
       A.RPTR_RELATION,
       A.RPTR_NAME,
       A.RPTR_MP,
       A.RPTR_ZIP,
       A.RPTR_ADDR,
       A.RPTR_EMAIL,
       A.REPORT_MODE,
       A.ORGAN_CODE,
       A.RPTR_TIME,
       A.CASE_APPLY_TYPE,
       A.APPLY_DATE,
       A.ACCEPTOR_ID,
       A.ACCEPT_TIME,
       A.TRUSTEE_TYPE,
       A.TRUSTEE_CODE,
       A.TRUSTEE_NAME,
       A.TRUSTEE_MP,
       A.TRUSTEE_TEL,
       A.TRUSTEE_CERTI_TYPE,
       A.TRUSTEE_CERTI_CODE,
       A.DOOR_SIGN_TIME,
       A.SIGN_TIME,
       A.RPTR_ID,
       A.SIGNER_ID,
       A.SERIOUS_DISEASE,
       A.ACCIDENT_DETAIL,
       A.CURE_HOSPITAL,
       A.CURE_STATUS,
       A.DOCTOR_NAME,
       A.MED_DEPT,
       A.CASE_SUB_STATUS,
       A.CASE_STATUS,
       A.GREEN_FLAG,
       A.IS_BPO,
       A.CASE_FLAG,
       A.REVIEW_FLAG,
       A.COMFORT_FLAG,
       A.ADVANCE_ASK_FLAG,
       A.ADVANCE_FLAG,
       A.IS_DEDUCT_FLAG,
       A.REPEAL_REASON,
       A.REPEAL_DESC,
       A.REGISTER_ID,
       A.REGISTE_TIME,
       A.REGISTE_CONF_TIME,
       A.ACCEPT_DECISION,
       A.REJECT_REASON,
       A.CALC_PAY,
       A.ADVANCE_PAY,
       A.BALANCE_PAY,
       A.ACTUAL_PAY,
       A.REJECT_PAY,
       A.AUDIT_TIME,
       A.AUDITOR_ID,
       A.AUDIT_DECISION,
       A.AUDIT_REMARK,
       A.AUDIT_REJECT_REASON,
       A.OTHER_REASON,
       A.APPROVER_ID,
       A.APPROVE_TIME,
       A.APPROVE_DECISION,
       A.APPROVE_REJECT_REASON,
       A.APPROVE_REMARK,
       A.END_CASE_TIME,
       A.OVER_COMP_FLAG,
       A.RELATED_NO,
       A.CLAIM_SOURCE,
       A.IS_COMMON,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.AUDIT_PERMISSION_NAME,
       A.APPROVE_PERMISSION_NAME,
       A.SURVEY_RESULT_INFO,
       A.CHANNEL_CODE,
       A.IS_AUTO_HUNGUP,
       A.SPECIAL_REMARK_CODE,
       A.LOSS_REASON_CODE,
       A.LOSS_LEVEL_CODE,
       A.COMFORT_STATUS,
       A.IS_MIGRATION FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE A.CASE_FLAG in (${caseFlagCode}) ]]>
		<if test="case_status != null and case_status != ''">
    		<![CDATA[ and A.case_status = #{case_status} ]]>
		</if>
		<if test="approve_start_date != null and approve_start_date != ''">
    		<![CDATA[ and A.end_case_time >= #{approve_start_date}	]]>
		</if>
		<if test="approve_end_date != null and approve_end_date != ''">
    		<![CDATA[ and A.end_case_time <= #{approve_end_date}	]]>
		</if>
	</select>
	<!-- 生调历史查询 -->
	<select id="querySurveyConclusion" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select distinct item.survey_item,app.survey_org,app.case_id,item.apply_id
						from APP___CLM__DBUSER.T_SURVEY_OBJECT obj,APP___CLM__DBUSER.T_SURVEY_ITEM item, APP___CLM__DBUSER.T_SURVEY_APPLY app
					 	where obj.apply_id = item.apply_id
   						and obj.apply_id = app.apply_id
  					 	and obj.customer_id = #{customer_id}]]>
	</select>
	<!-- end -->

	<!--add by zhangjy_wb 根据条件查询豁免处理信息 -->
	<select id="queryWaiverManageTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    	     <![CDATA[select count(1)
					    from APP___CLM__DBUSER.T_CLAIM_CASE         a,
							 APP___CLM__DBUSER.T_CONTRACT_MASTER    b,
							 APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD c,
							 APP___CLM__DBUSER.T_CONTRACT_PRODUCT  	d,
							 APP___CLM__DBUSER.T_BUSINESS_PRODUCT   g,
							 APP___CLM__DBUSER.T_CLAIM_LIAB cl,
							 APP___CLM__DBUSER.T_LIABILITY  ly
						where a.case_id = b.case_id
			                  and a.case_id = c.case_id
			                  and b.policy_id = c.policy_id
			                  and c.busi_prod_code =g.product_code_sys 
			                  and a.case_id = d.case_id
			                  and b.policy_id = d.policy_id
			                  and c.busi_item_id = d.busi_item_id
			                  and a.case_id = cl.case_id
			                  and b.policy_id = cl.policy_id
			                  and c.busi_item_id = cl.busi_item_id
			                  and d.item_id = cl.item_id
			                  and cl.liab_id = ly.liab_id
			                  and b.cur_flag = 1
			                  and c.cur_flag = 1
			                  and d.cur_flag = 1
			                  and cl.is_waived = 1
			                  and cl.liab_id like '9%'
			                  and d.prem_freq not in (1)]]>
		<if test="case_id !=null and case_id != ''"> 
					<![CDATA[ and a.case_id = #{case_id} ]]>
		</if>
		<if test="policy_id !=null and policy_id != ''"> 
					<![CDATA[ and b.policy_id = #{policy_id} ]]>
		</if>
		<if test="busi_item_id !=null and busi_item_id != ''"> 
					<![CDATA[ and c.busi_item_id = #{busi_item_id} ]]>
		</if>
		<if test="item_id !=null and item_id != ''"> 
					<![CDATA[ and d.item_id = #{item_id} ]]>
		</if>
	</select>
	<select id="queryCompWaiverManagePage" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
           		select distinct a.case_id,
                                b.policy_id,
                                b.policy_code,
                                c.busi_item_id,
                                c.liability_state,
                                c.validate_date,
                                d.paidup_date,
                                d.item_id,
                                d.prem_freq,
                                g.product_code_sys,
                                g.product_name_sys,
                                cl.liab_id
                  from APP___CLM__DBUSER.T_CLAIM_CASE        a,
                       APP___CLM__DBUSER.T_CONTRACT_MASTER    b,
                       APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD c,
                       APP___CLM__DBUSER.T_CONTRACT_PRODUCT   d,
                       APP___CLM__DBUSER.T_BUSINESS_PRODUCT   g,
                       APP___CLM__DBUSER.T_CLAIM_LIAB        cl,
                       APP___CLM__DBUSER.T_LIABILITY         ly
                 where a.case_id = b.case_id
                   and a.case_id = c.case_id
                   and b.policy_id = c.policy_id
                   and c.busi_prod_code =g.product_code_sys 
                   and a.case_id = d.case_id
                   and b.policy_id = d.policy_id
                   and c.busi_item_id = d.busi_item_id
                   and a.case_id = cl.case_id
                   and b.policy_id = cl.policy_id
                   and c.busi_item_id = cl.busi_item_id
                   and d.item_id = cl.item_id
                   and cl.liab_id = ly.liab_id
                   and b.cur_flag = 1
                   and c.cur_flag = 1
                   and d.cur_flag = 1
                   and cl.is_waived = 1
                   and cl.liab_id like '9%'
                   and d.prem_freq not in (1)]]>
		<if test="case_id !=null and case_id != ''"> 
					<![CDATA[ and a.case_id = #{case_id} ]]>
		</if>
		<if test="policy_id !=null and policy_id != ''"> 
					<![CDATA[ and b.policy_id = #{policy_id} ]]>
		</if>
		<if test="busi_item_id !=null and busi_item_id != ''"> 
					<![CDATA[ and c.busi_item_id = #{busi_item_id} ]]>
		</if>
		<if test="item_id !=null and item_id != ''"> 
					<![CDATA[ and d.item_id = #{item_id} ]]>
		</if>
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>
	<!--豁免处理需显示没有匹配到但被豁免的险种以及被豁免的责任相关的险种 -->
	<select id="queryWaiverPage" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(]]>
		<include refid="queryWaiverLiabSQLNew" />
		<!-- <include refid="queryWaiverLiabSQL" /> 
		<![CDATA[ UNION]]> 
		<include refid="queryWaiverPrdSQL" /> -->
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>
	<!--豁免处理需显示没有匹配到但被豁免的险种以及被豁免的责任相关的险种分页条数 -->
	<select id="queryWaiverTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
    	     <![CDATA[select count(1) from (]]>
		<include refid="queryWaiverLiabSQLNew" />
		<!-- <include refid="queryWaiverLiabSQL" /> 
		<![CDATA[ UNION]]> 
		<include refid="queryWaiverPrdSQL" /> -->
        <![CDATA[) tb]]>
	</select>

	<sql id="queryWaiverLiabSQLNew">
		<![CDATA[  select distinct c.case_id,
                    cm.policy_id,
                    cm.policy_code,
                    cb.busi_item_id,
                    cb.liability_state,
                    cb.validate_date,
                    cp.paidup_date,
                    cp.item_id,
                    cp.prem_freq,
                    prd.product_code_sys,
                    prd.product_name_sys,
                    bp.waive_desc                   
                 from   APP___CLM__DBUSER.T_CLAIM_CASE  c,
                        APP___CLM__DBUSER.T_CONTRACT_MASTER    cm,
                        APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD  cb,
                        APP___CLM__DBUSER.T_CONTRACT_PRODUCT    cp,
                        APP___CLM__DBUSER.T_BUSINESS_PRODUCT   prd,
                        APP___CLM__DBUSER.T_CLAIM_BUSI_PROD    bp
                 where    c.case_id = cm.case_id
                  and c.case_id = cb.case_id
                  and c.case_id = cp.case_id
                  and c.case_id = bp.case_id
                  and cm.policy_id = cb.policy_id
                  and cm.policy_id = cp.policy_id
                  and cm.policy_id = bp.policy_id
                  and cb.busi_item_id = cp.busi_item_id
                  and cb.busi_item_id = bp.busi_item_id
                  and bp.busi_prod_code = prd.product_code_sys                
                  and  cm.cur_flag = 0
                  and cb.cur_flag = 0
                  and cp.cur_flag = 0
                  and bp.is_waived = 1             
                  and cp.prem_freq not in (1) ]]>
		<if test="case_id !=null and case_id != ''"> 
					         <![CDATA[ and c.case_id = #{case_id} ]]>
		</if>
		<if test="policy_id !=null and policy_id != ''"> 
								<![CDATA[ and cm.policy_id = #{policy_id} ]]>
		</if>
		<if test="busi_item_id !=null and busi_item_id != ''"> 
								<![CDATA[ and cb.busi_item_id = #{busi_item_id} ]]>
		</if>
		<if test="item_id !=null and item_id != ''"> 
								<![CDATA[ and cp.item_id = #{item_id} ]]>
		</if>
	</sql>
	<sql id="queryWaiverLiabSQL">
		<![CDATA[  select distinct c.case_id,
                    cm.policy_id,
                    cm.policy_code,
                    cb.busi_item_id,
                    cb.liability_state,
                    cb.validate_date,
                    cp.paidup_date,
                    cp.item_id,
                    cp.prem_freq,
                    prd.product_code_sys,
                    prd.product_name_sys,
                    cl.liab_id   ,
                    bp.waive_desc                   
                 from   APP___CLM__DBUSER.T_CLAIM_CASE  c,
                        APP___CLM__DBUSER.T_CONTRACT_MASTER    cm,
                        APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD  cb,
                        APP___CLM__DBUSER.T_CONTRACT_PRODUCT    cp,
                        APP___CLM__DBUSER.T_BUSINESS_PRODUCT   prd,
                        APP___CLM__DBUSER.T_CLAIM_BUSI_PROD    bp,
                        APP___CLM__DBUSER.t_claim_liab cl
                 where    c.case_id = cm.case_id
                  and c.case_id = cb.case_id
                  and c.case_id = cp.case_id
                  and c.case_id = bp.case_id
                  and c.case_id = cl.case_id
                  and cm.policy_id = cb.policy_id
                  and cm.policy_id = cp.policy_id
                  and cm.policy_id = bp.policy_id
                  and cm.policy_id = cl.policy_id
                  and cb.busi_item_id = cp.busi_item_id
                  and cb.busi_item_id = bp.busi_item_id
                  and cb.busi_item_id = cl.busi_item_id
                  and cp.item_id = cl.item_id 
                  and bp.busi_prod_code = prd.product_code_sys                
                  and  cm.cur_flag = 0
                  and cb.cur_flag = 0
                  and cp.cur_flag = 0
                  and cl.liab_id like '9%'   
                  and cl.is_waived = 1             
                   and cp.prem_freq not in (1)  ]]>
		<if test="case_id !=null and case_id != ''"> 
					         <![CDATA[ and c.case_id = #{case_id} ]]>
		</if>
		<if test="policy_id !=null and policy_id != ''"> 
								<![CDATA[ and cm.policy_id = #{policy_id} ]]>
		</if>
		<if test="busi_item_id !=null and busi_item_id != ''"> 
								<![CDATA[ and cb.busi_item_id = #{busi_item_id} ]]>
		</if>
		<if test="item_id !=null and item_id != ''"> 
								<![CDATA[ and cp.item_id = #{item_id} ]]>
		</if>
	</sql>

	<sql id="queryWaiverPrdSQL">
		<![CDATA[ select bb.case_id，
                    bb.policy_id,
                    bb.policy_code,
                    bb.busi_item_id,
                    bb.liability_state,
                    bb.validate_date,
                    bb.paidup_date,
                    bb.item_id,
                    bb.prem_freq,
                    bb.product_code_sys,
                    bb.product_name_sys,
                    bb.item_id liab_id,
                    bb.waive_desc from  (select l.claim_liab_id,l.case_id,l.policy_id,l.policy_code, l.RELA_MASTER_BUSI_STOP_PAY_FLAG
		 			from APP___CLM__DBUSER.t_claim_case c ,APP___CLM__DBUSER.t_claim_liab l
                   where c.case_id = l.case_id and l.case_id = #{case_id} 
                   and l.RELA_MASTER_BUSI_STOP_PAY_FLAG is not null ) aa  left join (select distinct c.case_id,
                    cm.policy_id,
                    cm.policy_code,
                    cb.busi_item_id,
                    cb.liability_state,
                    cb.validate_date,
                    cp.paidup_date,
                    cp.item_id,
                    cp.prem_freq,
                    prd.product_code_sys,
                    prd.product_name_sys,
                    cl.item_id liab_id,
                    cb.busi_prod_code,
                    bp.waive_desc
                 from   APP___CLM__DBUSER.T_CLAIM_CASE  c,
                        APP___CLM__DBUSER.T_CONTRACT_MASTER    cm,
                        APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD  cb,
                        APP___CLM__DBUSER.T_CONTRACT_PRODUCT    cp,
                        APP___CLM__DBUSER.T_BUSINESS_PRODUCT   prd,
                        APP___CLM__DBUSER.T_CLAIM_BUSI_PROD    bp,
                        APP___CLM__DBUSER.t_claim_product cl                
                 where    c.case_id = cm.case_id
                  and c.case_id = cb.case_id
                  and c.case_id = cp.case_id
                  and c.case_id = bp.case_id
                  and c.case_id = cl.case_id
                  and cm.policy_id = cb.policy_id
                  and cm.policy_id = cp.policy_id
                  and cm.policy_id = bp.policy_id
                  and cm.policy_id = cl.policy_id
                  and cb.busi_item_id = cp.busi_item_id
                  and cb.busi_item_id = bp.busi_item_id
                  and cb.busi_item_id = cl.busi_item_id
                  and cp.item_id = cl.item_id 
                  and bp.busi_prod_code = prd.product_code_sys                
                  and  cm.cur_flag = 0
                  and cb.cur_flag = 0
                  and cp.cur_flag = 0  
                  and cp.prem_freq not in (1)  ]]>
		<if test="case_id !=null and case_id != ''"> 
					         <![CDATA[ and c.case_id = #{case_id} ]]>
		</if>
		<if test="policy_id !=null and policy_id != ''"> 
								<![CDATA[ and cm.policy_id = #{policy_id} ]]>
		</if>
		<if test="busi_item_id !=null and busi_item_id != ''"> 
								<![CDATA[ and cb.busi_item_id = #{busi_item_id} ]]>
		</if>
		<if test="item_id !=null and item_id != ''"> 
								<![CDATA[ and cp.item_id = #{item_id} ]]>
		</if>
					<![CDATA[)bb on aa.case_id = bb.case_id
                    and aa.policy_id = bb.policy_id and aa.rela_master_busi_stop_pay_flag is not null 
                    and bb.BUSI_PROD_CODE in ( aa.rela_master_busi_stop_pay_flag)]]>

	</sql>

	<!-- add by xuyz_wb 上载赔案列表结果 begin -->
	<select id="queryClaimSurveyResultForPage" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  select B.* from(
           select A.*,rownum RN from(
              select view1.*,count(tcp.case_id) payee_num from  (select distinct a.case_id,
           								a.case_no,
						                a.actual_pay,
						                a.organ_code,
						                a.channel_code,
						                (select max(d.apply_date) from APP___CLM__DBUSER.t_survey_apply d where d.case_no=a.case_no and d.biz_type=1) apply_date,
						                b.acc_reason,
						                (select c.customer_name
                           from APP___CLM__DBUSER.T_CUSTOMER c
                          where a.insured_id = c.customer_id) customer_name
					    from APP___CLM__DBUSER.T_CLAIM_CASE a, APP___CLM__DBUSER.T_CLAIM_SUB_CASE b
					   where a.case_id = b.case_id
             and a.case_no in ${biz_nos}
             ) view1,APP___CLM__DBUSER.T_CLAIM_PAYEE tcp where view1.case_id = tcp.case_id(+)
               group by view1.case_id,
                        view1.case_no,
                        view1.actual_pay,
                        view1.organ_code,
                        view1.channel_code,
                        view1.apply_date,
                        view1.acc_reason,
                        view1.customer_name ]]>
        <![CDATA[    ) A where rownum <= #{LESS_NUM} ) B where RN > #{GREATER_NUM}]]>
	</select>
	<select id="findClaimSurveyResultTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  select count(1)
                 from(select view1.*,count(tcp.case_id) payee_num from  (select distinct a.case_id,
           								a.case_no,
						                a.actual_pay,
						                a.organ_code,
						                a.channel_code,
						                (select max(d.apply_date) from APP___CLM__DBUSER.t_survey_apply d where d.case_no=a.case_no and d.biz_type=1) apply_date,
						                b.acc_reason,
						                (select c.customer_name
                           from APP___CLM__DBUSER.T_CUSTOMER c
                          where a.insured_id = c.customer_id) customer_name
					    from APP___CLM__DBUSER.T_CLAIM_CASE a, APP___CLM__DBUSER.T_CLAIM_SUB_CASE b
					   where a.case_id = b.case_id
             and a.case_no in ${biz_nos}
             ) view1,APP___CLM__DBUSER.T_CLAIM_PAYEE tcp where view1.case_id = tcp.case_id(+)
               group by view1.case_id,
                        view1.case_no,
                        view1.actual_pay,
                        view1.organ_code,
                        view1.channel_code,
                        view1.apply_date,
                        view1.acc_reason,
                        view1.customer_name)]]>
	</select>
	<!-- add by xuyz_wb 上载赔案列表结果 end -->


	<select id="queryClaimCase4Redo" parameterType="java.util.Map"
		resultType="java.util.Map">
   	    select cc.case_no,
		       cc.case_id,
		       ( select c.customer_name from   
		        APP___CLM__DBUSER.T_CUSTOMER 
		         c where c.customer_id= cc.insured_id )   customer_name,
		       cc.approve_decision,
		       cc.audit_decision,
		       cc.actual_pay,
		       cc.advance_pay,
		       cc.end_case_time
		  from APP___CLM__DBUSER.T_CLAIM_CASE     cc
			 where cc.case_status = 80 
		<if test="insured_name !=null and insured_name != ''"> 
		   <![CDATA[and cc.insured_id in (select c.customer_id from APP___CLM__DBUSER.T_CUSTOMER c where c.customer_name=#{insured_name})]]>
        </if>
        <if test="certi_code !=null and certi_code != ''"> 
           <![CDATA[and cc.insured_id in (select c.customer_id from APP___CLM__DBUSER.T_CUSTOMER c where c.customer_certi_code=#{certi_code}) ]]>
        </if>
        
		<if test="case_no !=null and case_no != ''"> 
		   <![CDATA[ and cc.case_no = #{case_no} ]]>
		</if>
			   
		<if test="case_id!=null">
		   <![CDATA[ and cc.case_id=#{case_id}]]>
		</if>
	</select>
	<!-- add by liulei_wb 自动分期给付 -->
	<select id="findBatchClaimInstalmentTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
				  FROM APP___CLM__DBUSER.T_CLAIM_INSTALMENT A,
				       APP___CLM__DBUSER.T_CLAIM_PAY        B,
				       APP___CLM__DBUSER.T_CLAIM_CASE       C,
				       APP___CLM__DBUSER.T_CONTRACT_MASTER  D
				 WHERE A.CLAIM_PAY_ID = B.CLAIM_PAY_ID
				   AND B.CASE_ID = C.CASE_ID
				   AND B.POLICY_ID = D.POLICY_ID
				   AND B.CASE_ID = D.CASE_ID
				   AND C.CASE_STATUS = '80'
				   AND A.INSTAL_STATUS = '0'
				   AND A.PAY_DUE_DATE = TO_DATE(TO_CHAR(#{pay_due_date}, 'YYYY-MM-DD'), 'yyyy-MM-dd')  ]]>
	</select>
	<select id="findBatchClaimInstalment" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT E.RN, E.INSTALMENT_ID, E.CASE_ID, E.CASE_NO, E.POLICY_ID, E.APPLY_CODE FROM (select ROWNUM RN, A.INSTALMENT_ID, B.CASE_ID, C.CASE_NO, D.POLICY_ID, D.APPLY_CODE
				  FROM APP___CLM__DBUSER.T_CLAIM_INSTALMENT A,
				       APP___CLM__DBUSER.T_CLAIM_PAY        B,
				       APP___CLM__DBUSER.T_CLAIM_CASE       C,
				       APP___CLM__DBUSER.T_CONTRACT_MASTER  D
				 WHERE A.CLAIM_PAY_ID = B.CLAIM_PAY_ID
				   AND B.CASE_ID = C.CASE_ID
				   AND B.POLICY_ID = D.POLICY_ID
				   AND B.CASE_ID = D.CASE_ID
				   AND C.CASE_STATUS = '80'
				   AND A.INSTAL_STATUS = '0' 
				   AND A.PAY_DUE_DATE = TO_DATE(TO_CHAR(#{pay_due_date}, 'YYYY-MM-DD'), 'yyyy-MM-dd')
				   ) E WHERE MOD(rn ,#{modNum}) = #{start}   ]]>
	</select>
	<!-- end -->
	<!-- by caoyy_wb 自动发起分次给付 -->
	<select id="findClaimCaseTotalBath" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM (SELECT DISTINCT CAS.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_INSTALMENT INS, APP___CLM__DBUSER.T_CLAIM_PAY PAY ,APP___CLM__DBUSER.T_CLAIM_CASE CAS
       				WHERE INS.CLAIM_PAY_ID = PAY.CLAIM_PAY_ID
     			 	AND PAY.CASE_ID = CAS.CASE_ID
     			 	AND CAS.CASE_NO = #{case_no})
     		]]>
	</select>
	<select id="findBatchClaimCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT INST.SURVEY_FLAG,CAS.CASE_NO,CAS.AUDITOR_ID,CAS.CASE_ID,PAY.POLICY_CODE,INST.INSTALMENT_ID,INST.PAY_DUE_DATE
          			FROM APP___CLM__DBUSER.T_CLAIM_CASE CAS, APP___CLM__DBUSER.T_CLAIM_INSTALMENT INST, APP___CLM__DBUSER.T_CLAIM_PAY PAY
         			WHERE CAS.CASE_ID = PAY.CASE_ID
          	 		AND PAY.CLAIM_PAY_ID = INST.CLAIM_PAY_ID
          	 		
           			AND CAS.CASE_STATUS = '80'
           			AND INST.INSTAL_STATUS = '0'
           			AND INST.PAY_DUE_DATE = TO_DATE(TO_CHAR(#{pay_due_date}, 'YYYY-MM-DD'), 'yyyy-MM-dd')]]>
		<if test="case_no !=null and case_no != ''"> 
				<![CDATA[ AND CAS.CASE_NO = #{case_no} ]]>
		</if>
		<![CDATA[   AND  mod(INST.INSTALMENT_ID,#{modNum}) = #{start}]]>
	</select>
	<select id="queryBatchInstal" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT INST.SURVEY_FLAG,CAS.CASE_NO,CAS.AUDITOR_ID,CAS.CASE_ID,PAY.POLICY_CODE,INST.INSTALMENT_ID
          			FROM APP___CLM__DBUSER.T_CLAIM_CASE CAS, APP___CLM__DBUSER.T_CLAIM_INSTALMENT INST, APP___CLM__DBUSER.T_CLAIM_PAY PAY
         			WHERE CAS.CASE_ID = PAY.CASE_ID
          	 		AND PAY.CLAIM_PAY_ID = INST.CLAIM_PAY_ID
          	 		
           			AND CAS.CASE_STATUS = '80'
           			AND INST.INSTAL_STATUS = '0'
           			AND INST.PAY_DUE_DATE = TO_DATE(TO_CHAR(#{pay_due_date}, 'YYYY-MM-DD'), 'yyyy-MM-dd')
           			AND CAS.CASE_ID = #{case_id} ]]>
	</select>
	<!-- end -->

	<!--add by zhangjy_wb 根据条件查询复堪计划信息 -->
	<select id="queryRecheckPlanTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    	     <![CDATA[select count(1)
    	     		 	from APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_PLAN p
  					    left join APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_RULE r
    						 on   p.plan_id = r.plan_id
                        left join APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_ACC_TYPE t
                             on   p.plan_id = t.plan_id
                        left join APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_ORG o
                             on   p.plan_id = o.plan_id
		    	                      where 1=1]]>
		<if test="plan_name !=null and plan_name != ''"> 
					<![CDATA[ and p.plan_name = #{plan_name} ]]>
		</if>
		<if test="plan_type !=null and plan_type != ''"> 
					<![CDATA[ and p.plan_type = #{plan_type} ]]>
		</if>
	</select>
	<select id="queryRecheckPlanPage" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
           				select distinct p.plan_id,
						       p.plan_name,
						       p.plan_type,
						       p.claim_money,
						       p.payee_num,
						       p.claim_rate,
						       p.valid_flag,
						       r.para_type,
						       r.para_name,
						       r.para_value,
						       t.claim_type,
						       t.acc_days,
						       o.organ_code,
						       o.rate,
						       o.limit
    	     		 	from APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_PLAN p
  					    left join APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_RULE r
    						 on   p.plan_id = r.plan_id
                        left join APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_ACC_TYPE t
                             on p.plan_id = t.plan_id
                        left join APP___CLM__DBUSER.T_CLAIM_RE_SURVEY_ORG o
                             on p.plan_id = o.plan_id
		    	                      where 1=1]]>
		<if test="plan_name !=null and plan_name != ''"> 
					<![CDATA[ and p.plan_name = #{plan_name} ]]>
		</if>
		<if test="plan_type !=null and plan_type != ''"> 
					<![CDATA[ and p.plan_type = #{plan_type} ]]>
		</if>
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>

	<!--add by zhangjy_wb 根据条件查询抽取复堪任务结果信息 -->
	<select id="queryExtractRecheckTaskResultTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    	    <![CDATA[ select count(1) from (select view2.*,(select max(rownum) from APP___CLM__DBUSER.T_CLAIM_PAYEE d where 1 = 1 and view2.case_id =d.case_id) payee_num from 
                    (select view1.*,count(tsa.case_id) apply_num from  (select distinct a.case_id,
           								a.case_no,
						                a.actual_pay,
						                a.organ_code,
						                a.insured_id,
						                a.apply_date,
						                a.approve_time,
                            a.approve_decision,
						                b.acc_reason,
						                (select c.customer_name
                           from APP___CLM__DBUSER.T_CUSTOMER c
                          where a.insured_id = c.customer_id) customer_name
					    from APP___CLM__DBUSER.T_CLAIM_CASE a, APP___CLM__DBUSER.T_CLAIM_SUB_CASE b
					   where a.case_id = b.case_id and a.case_status = 80 ]]>
		<if test="accidentDetail !=null and accidentDetail != ''"> 
					        <![CDATA[  and b.acc_reason in (${accidentDetail}) ]]>
		</if>
		<if test="organCode !=null and organCode != ''">
							<![CDATA[  and a.organ_code = #{organCode} ]]>
		</if>
		<if test="actualPay !=null and actualPay != ''"> 
							<![CDATA[  and a.actual_pay >= #{actualPay} ]]>
		</if>
		<if
			test="startDate !=null and startDate != '' and endDate !=null and endDate != ''"> 
							<![CDATA[  and a.approve_time >= #{startDate}]]>
							<![CDATA[  and a.approve_time <= #{endDate}]]>
							<![CDATA[  and a.approve_decision = 1]]>
		</if>
             <![CDATA[ ) view1,APP___CLM__DBUSER.T_SURVEY_APPLY tsa 
               where tsa.survey_status=2 and view1.case_id = tsa.case_id(+)
               group by view1.case_id,
                        view1.case_no,
                        view1.actual_pay,
                        view1.organ_code,
                        view1.insured_id,
                        view1.apply_date,
                        view1.approve_time,
                        view1.approve_decision,
                        view1.acc_reason,
                        view1.customer_name) view2 where view2.apply_num>0) view3 ]]>
		<if test="payeeNum !=null and payeeNum != ''"> 
							<![CDATA[  where view3.payee_num >= #{payeeNum} ]]>
		</if>
	</select>
	<!-- 抽取复勘任务结果分页查询 -->
	<select id="queryExtractRecheckTaskResultForPage" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
           				select * from (select view2.*,(select max(rownum) from APP___CLM__DBUSER.T_CLAIM_PAYEE d where 1 = 1 and view2.case_id =d.case_id) payee_num from 
                    (select view1.*,count(tsa.case_id) apply_num from  (select distinct a.case_id,
           								a.case_no,
						                a.actual_pay,
						                a.organ_code,
						                a.insured_id,
						                a.apply_date,
						                a.approve_time,
                            a.approve_decision,
						                b.acc_reason,
						                (select c.customer_name
                           from APP___CLM__DBUSER.T_CUSTOMER c
                          where a.insured_id = c.customer_id) customer_name
					    from APP___CLM__DBUSER.T_CLAIM_CASE a, APP___CLM__DBUSER.T_CLAIM_SUB_CASE b
					   where a.case_id = b.case_id and a.case_status = 80 ]]>
		<if test="accidentDetail !=null and accidentDetail != ''"> 
					        <![CDATA[  and b.acc_reason in (${accidentDetail}) ]]>
		</if>
		<if test="organCode !=null and organCode != ''">
							<![CDATA[  and a.organ_code = #{organCode} ]]>
		</if>
		<if test="actualPay !=null and actualPay != ''"> 
							<![CDATA[  and a.actual_pay >= #{actualPay} ]]>
		</if>
		<if
			test="startDate !=null and startDate != '' and endDate !=null and endDate != ''"> 
							<![CDATA[  and a.approve_time >= #{startDate}]]>
							<![CDATA[  and a.approve_time <= #{endDate}]]>
							<![CDATA[  and a.approve_decision = 1]]>
		</if>
             <![CDATA[ ) view1,APP___CLM__DBUSER.T_SURVEY_APPLY tsa 
               where tsa.survey_status=2 and view1.case_id = tsa.case_id(+)
               group by view1.case_id,
                        view1.case_no,
                        view1.actual_pay,
                        view1.organ_code,
                        view1.insured_id,
                        view1.apply_date,
                        view1.approve_time,
                        view1.approve_decision,
                        view1.acc_reason,
                        view1.customer_name) view2 where view2.apply_num>0) view3 ]]>
		<if test="payeeNum !=null and payeeNum != ''"> 
							<![CDATA[  where view3.payee_num >= #{payeeNum} ]]>
		</if>
					    <![CDATA[ ORDER BY view3.CASE_NO ]]>
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>
	<!--add by xuyz_wb 抽取复勘任务结果不分页查询 -->
	<select id="queryExtractRecheckTaskResultForList" parameterType="java.util.Map" resultType="java.util.Map">
        <![CDATA[   SELECT T.CASE_ID,T.CASE_NO,T.ACTUAL_PAY,T.ORGAN_CODE,T.CUSTOMER_NAME,T.ACC_REASON,T.CLAIM_TYPE,T.CLAIM_DATE,MAX(APPLY_DATE) APPLY_DATE FROM ( 
			 SELECT DISTINCT A.CASE_ID,
			                 A.CASE_NO,
			                 A.ACTUAL_PAY,
			                 A.ORGAN_CODE,
			                  C.CUSTOMER_NAME,
			                 SC.FINISH_DATE APPLY_DATE,
			                 B.ACC_REASON，
			                 B.CLAIM_TYPE,
			                 B.CLAIM_DATE
			   FROM APP___CLM__DBUSER.T_CLAIM_CASE     A,
			        APP___CLM__DBUSER.T_CLAIM_LIAB E,
			        APP___CLM__DBUSER.T_CLAIM_SUB_CASE B,
			        APP___CLM__DBUSER.T_SURVEY_APPLY   TSA,
			        APP___CLM__DBUSER.T_SURVEY_COURSE F,
			       APP___CLM__DBUSER.T_SURVEY_CONCLUSION SC,
			        APP___CLM__DBUSER.T_CUSTOMER       C
			  WHERE A.CASE_ID = B.CASE_ID
			    AND A.CASE_ID = E.CASE_ID
			    AND A.CASE_ID = TSA.CASE_ID
			    AND TSA.APPLY_ID = F.APPLY_ID(+) 
			    AND TSA.APPLY_ID = SC.APPLY_ID(+)
			    AND A.INSURED_ID = C.CUSTOMER_ID(+)]]>
		<if test="liabConclusion !=null and liabConclusion != ''">
			    	<![CDATA[AND E.LIAB_CONCLUSION IN (${liabConclusion})]]>
		</if>
		<if test="surveyedBy !=null and surveyedBy != ''"> 
			     	<![CDATA[AND (F.SURVEY_BY_ID1 = #{surveyedBy} OR F.SURVEY_BY_ID2 = #{surveyedBy})]]>
		</if>
		<if
			test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
				  <![CDATA[  AND A.END_CASE_TIME >= #{startDate}
				    AND A.END_CASE_TIME <= #{endDate} ]]>
		</if>
		<if test="accidentDetail !=null and accidentDetail != ''">
			    	<![CDATA[  AND B.ACC_REASON IN (${accidentDetail}) ]]>
		</if>
		<if test="claimType !=null and claimType != ''">
			   		<![CDATA[  AND B.CLAIM_TYPE IN(${claimType}) ]]>
		</if>
		<if test="organCode !=null and organCode != ''">
			   	 	<![CDATA[  AND A.ORGAN_CODE = #{organCode} ]]>
		</if>
		<if test="actualPay !=null and actualPay != ''">
			   	 	<![CDATA[  AND A.ACTUAL_PAY >= #{actualPay} ]]>
		</if>
			    <![CDATA[
			    	AND TSA.SURVEY_STATUS = 2
				    AND A.CASE_STATUS = 80
				    AND ROWNUM < 1000
				    AND NOT EXISTS( SELECT 1 FROM     APP___CLM__DBUSER.T_SURVEY_APPLY  TSA2 
				       WHERE A.CASE_NO = TSA2.CASE_NO AND TSA2.SURVEY_STATUS != 2 ) 
				    AND NOT EXISTS( SELECT 1 FROM     APP___CLM__DBUSER.T_SURVEY_APPLY   TSA2
				       WHERE A.CASE_NO = TSA2.CASE_NO AND TSA2.BIZ_TYPE = 4 )
				     ) 
				    T GROUP BY T.CASE_ID,T.CASE_NO,T.ACTUAL_PAY,T.ORGAN_CODE,T.CUSTOMER_NAME,
				    T.ACC_REASON,T.CLAIM_TYPE,T.CLAIM_DATE ORDER BY T.CASE_NO 
				    ]]>
	</select>
	<!-- add by xuyz_wb 分支流程 预付信息查询 预付比例*责任给付 可计算出 此责任预付金额 -->
	<select id="findClaimLiabPayMsgByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADVANCE_PAY, 
		 A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.RESIST_FLAG, A.WAIVE_START, 
			A.ADJUST_PAY, A.LIAB_ID, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, 
			A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, A.WAIVE_END, A.CALC_PAY, 
			A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE,B.CASE_NO FROM APP___CLM__DBUSER.T_CLAIM_LIAB A,APP___CLM__DBUSER.T_CLAIM_CASE B 
			WHERE 1=1 AND A.CASE_ID = B.CASE_ID AND B.ADVANCE_FLAG='1' AND A. ADVANCE_PAY ]]> &gt; <![CDATA[ 0]]>
		<if test="case_id !=null and case_id != ''"> 
	       	<![CDATA[ AND A.CASE_ID = #{case_id} ]]>
		</if>
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]>
	</select>
	<!-- 风险检查结果-检查项目 -->
	<select id="queryInsItems" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.INSPECT_PRO_CODE,
				       A.DEPARTMENT_ID,
				       A.INSPECT_TYPE1_ID,
				       A.INSPECT_TYPE2_ID,
				       A.INSPECT_PRO_DESC,
				       A.INSPECT_MODE,
				       D.USER_NAME||'-'||D.REAL_NAME PRO_MAKE_USER,
				       A.PRO_MAKE_TIME,
				       A.PRO_MAKE_ORGAN      
				  FROM DEV_CLM.T_CLAIM_RISK_INSPECT_PRO   A,
				       DEV_CLM.T_CLAIM_INSPECT_CONDITION  B,
				       DEV_CLM.T_CLAIM_INSPECT_TASK       C,
				       APP___CLM__DBUSER.T_UDMP_USER      D
				 WHERE A.INSPECT_PRO_ID = B.INSPECT_PRO_ID
				   AND B.INSPECT_CONDITION_ID = C.INSPECT_CONDITION_ID
				   AND A.PRO_MAKE_USER = D.USER_ID
			]]>
			<if test="case_no !=null and case_no != ''"> 
	       		<![CDATA[ AND C.CASE_NO = #{case_no} ]]>
			</if>
	</select>
	
	<!-- 风险检查结果-要点列表 -->
	<select id="queryKeyPoints" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.RISK_LIB_CODE,
				       A.RISK_LIB_TYPE,
				       A.RISK_LIB_POINT,
				       A.INSPECT_DEMAND,
				       A.LIB_MAKE_LEVEL,
				       A.LIB_MAKE_ORGAN,
				       A.LIB_MAKE_TIME,
				       D.USER_NAME||'-'||D.REAL_NAME LIB_MAKE_USER,
				       B.INSPECT_RESULT,
				       B.INSPECT_DESC,
				       B.INSPECT_ORG,
				       B.INSPECT_USER,
				       B.INSPECT_TIME
				  FROM DEV_CLM.T_CLAIM_RISK_LIBRARY       A,
				       DEV_CLM.T_CLAIM_INSPECT_TASK_POINT B,
				       DEV_CLM.T_CLAIM_INSPECT_TASK       C,
				       APP___CLM__DBUSER.T_UDMP_USER      D
				 WHERE A.RISK_LIB_ID = B.RISK_LIB_ID
				   AND B.TASK_ID = C.TASK_ID
				   AND A.LIB_MAKE_USER = D.USER_ID
			]]>
			<if test="case_no !=null and case_no != ''"> 
	       		<![CDATA[ AND C.CASE_NO = #{case_no} ]]>
			</if>
	</select>
	
	<!-- 风险检查结果-检查明细 -->
	<select id="queryInsDetails" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.INSPECT_TIME,A.INSPECT_ORG,D.USER_NAME||'-'||D.REAL_NAME INSPECT_USER
		FROM DEV_CLM.T_CLAIM_INSPECT_TASK_POINT A,DEV_CLM.T_CLAIM_INSPECT_TASK B,APP___CLM__DBUSER.T_UDMP_USER D
		WHERE A.TASK_ID = B.TASK_ID AND A.INSPECT_USER = D.USER_ID
			]]>
			<if test="case_no !=null and case_no != ''"> 
	       		<![CDATA[ AND B.CASE_NO = #{case_no} ]]>
			</if>
	</select>
	<!-- add by zhangjy_wb 查询 保单保费数据 -->
	<select id="findTotalPremAfByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select distinct　m.case_id, m.policy_id, bp.busi_item_id, p.total_prem_af
					          from APP___CLM__DBUSER.T_CONTRACT_MASTER    m,
					               APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD bp,
					               APP___CLM__DBUSER.T_CONTRACT_PRODUCT   p
					         where m.case_id = bp.case_id
					           and m.policy_id = bp.policy_id
					           and m.case_id = p.case_id
					           and m.policy_id = p.policy_id
					           and bp.busi_item_id = p.busi_item_id
					           and p.cur_flag = 1
					           and bp.cur_flag = 1
					           and m.cur_flag = 1]]>
		<if test="case_id !=null and case_id != ''"> 
	       	<![CDATA[ AND m.case_id = #{case_id} ]]>
		</if>
	</select>
	<!--add by zhangjy_wb 查询手工抽取前置调查任务结果信息 -->
	<select id="queryExtractBfcheckTaskResultTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    	     <![CDATA[select count(1)
					     from APP___CLM__DBUSER.T_CLAIM_CASE      a,
					          APP___CLM__DBUSER.T_CONTRACT_MASTER cm,
					          APP___CLM__DBUSER.T_CUSTOMER        ct   
					    where a.case_id = cm.case_id
					      and a.insured_id = ct.customer_id
					      and cm.cur_flag  =1]]>
		<if test="channelType !=null and channelType != ''"> 
					        <![CDATA[  and cm.channel_type in (${channelType}) ]]>
					        <![CDATA[  and cm.liability_state = #{liability_state} ]]>
		</if>
		<if
			test="startDate !=null and startDate != '' and endDate !=null and endDate != ''"> 
					        <![CDATA[  and cm.validdate_date >= #{startDate}]]>
					        <![CDATA[  and cm.validdate_date <= #{endDate}]]>
		</if>
		<if
			test="startDate !=null and startDate != '' and endDate !=null and endDate != '' and channelType ==null and channelType == ''"> 
					        <![CDATA[  and cm.liability_state = #{liability_state} ]]>
		</if>
		<if test="organCode !=null and organCode != ''"> 
					          <![CDATA[  and cm.organ_code = #{organCode} ]]>
		</if>
	</select>
	<select id="queryExtractBfcheckTaskResultForPage" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
           					select distinct 
							                a.insured_id,
           									cm.policy_id,
                                            cm.policy_code,
							                cm.validdate_date,
							                cm.expiry_date,
							                cm.liability_state,
							                cm.organ_code,
							                cm.channel_type,
							                ct.customer_certi_code,
							                ct.customer_name
							  from APP___CLM__DBUSER.T_CLAIM_CASE      a,
							       APP___CLM__DBUSER.T_CONTRACT_MASTER cm,
							       APP___CLM__DBUSER.T_CUSTOMER        ct   
							 where a.case_id = cm.case_id
							   and a.insured_id = ct.customer_id
							   and cm.cur_flag  =1]]>
		<if test="channelType !=null and channelType != ''"> 
						        <![CDATA[  and cm.channel_type in (${channelType}) ]]>
						        <![CDATA[  and cm.liability_state = #{liability_state} ]]>
		</if>
		<if
			test="startDate !=null and startDate != '' and endDate !=null and endDate != ''"> 
						        <![CDATA[  and cm.validdate_date >= #{startDate}]]>
						        <![CDATA[  and cm.validdate_date <= #{endDate}]]>
		</if>
		<if
			test="startDate !=null and startDate != '' and endDate !=null and endDate != '' and channelType ==null and channelType == ''"> 
						        <![CDATA[  and cm.liability_state = #{liability_state} ]]>
		</if>
		<if test="organCode !=null and organCode != ''"> 
					          <![CDATA[  and cm.organ_code = #{organCode} ]]>
		</if>
           <![CDATA[ ) A where rownum <= #{LESS_NUM}) B where RN > #{GREATER_NUM}]]>
	</select>

	<!--add by zhangjy_wb 业务员历史代办分页查询 -->
	<select id="queryAgentCommissionTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
				select count(1)
			      from APP___CLM__DBUSER.T_CLAIM_CASE cc, APP___CLM__DBUSER.T_CLAIM_SUB_CASE sc, APP___CLM__DBUSER.T_CONTRACT_MASTER m
			    where cc.case_id = sc.case_id
			      and cc.case_id = m.case_id
			      and m.cur_flag = 1
			      and cc.trustee_type = 1]]>
		<if test="trustee_code !=null and trustee_code != ''"> 
				 <![CDATA[ and cc.trustee_code = #{trustee_code} ]]>
		</if>
		<if test="trustee_certi_type !=null and trustee_certi_type != ''"> 
				 <![CDATA[ and cc.trustee_certi_type = #{trustee_certi_type} ]]>
		</if>
		<if test="trustee_certi_code !=null and trustee_certi_code != ''"> 
				 <![CDATA[ and cc.trustee_certi_code = #{trustee_certi_code} ]]>
		</if>
		<if test="acceptedStartDate !=null and acceptedStartDate != ''"> 
				<![CDATA[  and cc.accept_time >= #{acceptedStartDate}]]>
		</if>
		<if test="acceptedStartDate !=null and acceptedStartDate != ''"> 
				<![CDATA[  and cc.accept_time <= #{acceptedEndDate}]]>
		</if>
	</select>
	<select id="queryAgentCommissionPage" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ 
		select B.* from(
           select A.*,rownum RN from(
           					select distinct cc.case_id,
							        cc.case_no,
							        cc.accept_time,
							        (select name from APP___CLM__DBUSER.T_CASE_STATUS cs where cs.code=cc.case_status) as case_status,
							        (select name from APP___CLM__DBUSER.T_CLAIM_TYPE ct where ct.code = sc.claim_type) as claim_type,
							        m.policy_code
							   from APP___CLM__DBUSER.T_CLAIM_CASE cc, APP___CLM__DBUSER.T_CLAIM_SUB_CASE sc, APP___CLM__DBUSER.T_CONTRACT_MASTER m
							  where cc.case_id = sc.case_id
							    and cc.case_id = m.case_id
							    and m.cur_flag = 1
							    and cc.trustee_type = 1]]>
		<if test="trustee_code !=null and trustee_code != ''"> 
					 <![CDATA[ and cc.trustee_code = #{trustee_code} ]]>
		</if>
		<if test="trustee_certi_type !=null and trustee_certi_type != ''"> 
					 <![CDATA[ and cc.trustee_certi_type = #{trustee_certi_type} ]]>
		</if>
		<if test="trustee_certi_code !=null and trustee_certi_code != ''"> 
					 <![CDATA[ and cc.trustee_certi_code = #{trustee_certi_code} ]]>
		</if>
		<if test="acceptedStartDate !=null and acceptedStartDate != ''"> 
					<![CDATA[  and cc.accept_time >= #{acceptedStartDate}]]>
		</if>
		<if test="acceptedStartDate !=null and acceptedStartDate != ''"> 
					<![CDATA[  and cc.accept_time <= #{acceptedEndDate}]]>
		</if>
           <![CDATA[ ) A where rownum <= #{endCount}) B where RN >= #{startCount}]]>
	</select>
	<!-- zhangjy_wb 查询二核信息 -->
	<select id="queryClaimUwMsgByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select distinct uw.policy_code,
						       	uw.claim_uw_type,
						       	uw.apply_date,
						      	uw.remark,
						      	uw.UW_STATUS,
						      	uw.UW_CONCLUSION,
						      	p.policy_decision,
						      	m.uw_user_id,
						       	p.uw_finish_time,
								u.user_name
						  from APP___CLM__DBUSER.T_CLAIM_UW uw, 
						  	   APP___CLM__DBUSER.T_UW_MASTER m, 
						  	   APP___CLM__DBUSER.T_UW_POLICY p,
									 APP___CLM__DBUSER.T_UDMP_USER u
						 where uw.case_no = m.biz_code
						   	and m.uw_id = p.uw_id
						   	and uw.policy_id = p.policy_id
							and m.uw_user_id=u.user_id
						   	and uw.case_id = #{case_id}]]>
	</select>

	<!-- sunjl_wb 存入Unit_Number -->
	<select id="queryArapDeleteRepetitive" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT C.BUSI_PROD_CODE,C.CUSTOMER_ID,C.BANK_ACCOUNT FROM APP___CLM__DBUSER.T_PREM_ARAP C WHERE C.BUSINESS_CODE = #{business_code}  and c.unit_number is null ]]>
	</select>

	<select id="queryArapByBusiprodCodeAndCustomerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM APP___CLM__DBUSER.T_PREM_ARAP c where  c.business_code=#{business_code}  and c.unit_number is null
		]]>
		<if test=" customer_id  != null "><![CDATA[ and c.customer_id=#{customer_id} ]]></if>
		<!-- lizheng1_wb 针对二核加费场景处理-理赔回退后重新发起二核提交 -->
		<if test=" list_id  != null "><![CDATA[ and c.fee_type = 'G005130000' ]]></if>
	</select>
	<!-- sunjl_wb 存入Unit_Number END -->
	<!-- liulei_wb 查询医疗账单的日期 -->
	<select id="claimBillTreatDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT MIN(TREAT_START) TREAT_START, MAX(TREAT_END) TREAT_END FROM APP___CLM__DBUSER.T_CLAIM_BILL WHERE CASE_ID=#{case_id} and TREAT_END>#{treat_start} and TREAT_START<#{treat_end}
		]]>
	</select>
	<!--add zhangjy_wb 按条件转办任务 -->
	<select id="findAllBizTurnByConditions" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select  BT.BIZ_TURN_ID,
       BT.BIZ_TURN_CODE,
       BT.TURN_SCENE_CODE,
       BT.BIZ_TURN_STATUS,
       BT.APPLY_POLICY_CODE,
       BT.APPLY_BIZ_CODE,
       BT.TARGET_ORGAN_CODE,
       BT.TARGET_DEPT_CODE,
       BT.TARGET_USER,
       BT.APPLY_TURN_TYPE,
       BT.APPLY_NOTICE_TYPE,
       BT.APPLY_COMMENT,
       BT.APPLY_DATE,
       BT.APPLY_DEPT,
       BT.APPLY_USER,
       BT.APPROVE_SUGGESTION,
       BT.APPROVE_COMMENT,
       BT.APPROVE_DATE,
       BT.APPROVE_DEPT,
       BT.APPROVE_USER,
       BT.REPLY_BIZ_TYPE,
       BT.REPLY_BIZ_CODE,
       BT.REPLY_COMMENT,
       BT.REPLY_DATE,
       BT.REPLY_DEPT,
       BT.REPLY_USER,
       BT.CANCEL_REASON,
       BT.CANCEL_COMMENT,
       BT.CANCEL_DATE,
       BT.CANCEL_DEPT,
       BT.CANCEL_USER,
       BT.INSERT_TIMESTAMP,
       BT.INSERT_TIME,
       BT.INSERT_BY,
       BT.UPDATE_TIMESTAMP,
       BT.UPDATE_TIME,
       BT.UPDATE_BY
					  from APP___CLM__DBUSER.T_BIZ_TURN bt, APP___CLM__DBUSER.T_CLAIM_POLICY p, APP___CLM__DBUSER.T_BIZ_TURN_SCENE sc
					 where bt.turn_scene_code = sc.turn_scene_code
					   and bt.apply_policy_code = p.policy_code
					   and bt.biz_turn_status not in ('7','8','9','10','11')
					   and sc.sub_id = '067']]>
		<if test=" case_id  != null "><![CDATA[ and p.case_id = #{case_id} ]]></if>
		<if test=" apply_biz_code != null and apply_biz_code != ''  "><![CDATA[ and bt.apply_biz_code = #{apply_biz_code} ]]></if>
	</select>
	<!-- end -->
	<!-- 通过出险人ID查既往理赔信息 -->
	<select id="queryClaimHisInfoList" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[select distinct c.case_no,
    			c.case_id,
                a.acc_date,
                l.policy_code,
                (select p.product_abbr_name
                   from APP___CLM__DBUSER.T_BUSINESS_PRODUCT p
                  where p.product_code_sys = l.busi_prod_code) product_abbr_name,
                c.case_status,
                c.audit_decision,
                c.actual_pay,
                pa.fee_status,
                pa.finish_time,
                c.auditor_id,
                c.approver_id,
                sc.claim_date,
                sc.claim_type,
                c.insured_id,
                l.liab_id,
                c.end_case_time,
                (select MAX(q.audit_date)
                   from APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT q
                  where q.case_id = c.case_id) as back_audit_date,
                l.busi_prod_code
  from APP___CLM__DBUSER.T_CLAIM_CASE        c,
       APP___CLM__DBUSER.T_CLAIM_LIAB             l,
       APP___CLM__DBUSER.T_CLAIM_SUB_CASE  sc,
       APP___CLM__DBUSER.T_CLAIM_ACCIDENT  a,
       APP___CLM__DBUSER.T_PREM_ARAP            pa
 where c.insured_id = #{insured_id}
    and c.case_id = l.case_id(+)
   and c.accident_id = a.accident_id
   and c.case_id = sc.case_id
   and c.case_no = pa.business_code(+)
   and c.case_status != '99' ]]>
		<!-- <if test=" is_policyCode == 0 "><![CDATA[ and d.busi_prod_code in 
			(SELECT T.PRODUCT_CODE_SYS -->
		<!-- FROM APP___clm__DBUSER.T_BUSINESS_PRODUCT T -->
		<!-- WHERE T.WAIVER_FLAG = '1' -->
		<!-- AND T.PRODUCT_CATEGORY = '10002') ]]></if> -->
		<if test=" is_policyCode  == 1 "><![CDATA[ and (l.busi_prod_code = '00425000' or l.busi_prod_code='00952000') ]]></if>
	</select>
	
	<!-- 通过出险人ID查既往理赔信息(查询字段去除责任id) -->
	<select id="queryClaimHisInfoLists" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[select distinct c.case_no,
    			c.case_id,
                a.acc_date,
                l.policy_code,
                (select p.product_abbr_name
                   from APP___CLM__DBUSER.T_BUSINESS_PRODUCT p
                  where p.product_code_sys = l.busi_prod_code) product_abbr_name,
                c.case_status,
                c.audit_decision,
                c.actual_pay,
                pa.fee_status,
                pa.finish_time,
                c.auditor_id,
                c.approver_id,
                sc.claim_date,
                sc.claim_type,
                c.insured_id,
                c.end_case_time,
                (select MAX(q.audit_date)
                   from APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT q
                  where q.case_id = c.case_id) as back_audit_date,
                l.busi_prod_code
  from APP___CLM__DBUSER.T_CLAIM_CASE        c,
       APP___CLM__DBUSER.T_CLAIM_LIAB             l,
       APP___CLM__DBUSER.T_CLAIM_SUB_CASE  sc,
       APP___CLM__DBUSER.T_CLAIM_ACCIDENT  a,
       APP___CLM__DBUSER.T_PREM_ARAP            pa
 where c.insured_id = #{insured_id}
    and c.case_id = l.case_id(+)
   and c.accident_id = a.accident_id
   and c.case_id = sc.case_id
   and c.case_no = pa.business_code(+)
   and c.case_status != '99' ]]>
		<!-- <if test=" is_policyCode == 0 "><![CDATA[ and d.busi_prod_code in 
			(SELECT T.PRODUCT_CODE_SYS -->
		<!-- FROM APP___clm__DBUSER.T_BUSINESS_PRODUCT T -->
		<!-- WHERE T.WAIVER_FLAG = '1' -->
		<!-- AND T.PRODUCT_CATEGORY = '10002') ]]></if> -->
		<if test=" is_policyCode  == 1 "><![CDATA[ and (l.busi_prod_code = '00425000' or l.busi_prod_code='00952000' or l.busi_prod_code='00A05000') ]]></if>
	</select>

	<!-- sunjl_wb 查询保单历史上载记录 -->
	<select id="queryPolicyUploadHistoryClm" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT CSB.BATCH_ID,
       CSB.BIZ_TYPE,
       CSB.SURVEY_MODE,
       CSB.PLAN_ID,
       CSB.PLAN_NAME,
       CSB.UPLOAD_DATE,
       CSB.ORGAN_CODE,
       CSB.SURVEY_REASON,
       CSB.EXTRACT_RULE,
       CSB.INSERT_TIME,
       CSB.INSERT_BY,
       CSB.INSERT_TIMESTAMP,
       CSB.UPDATE_BY,
       CSB.UPDATE_TIMESTAMP,
       CSB.UPDATE_TIME FROM APP___CLM__DBUSER.T_CLAIM_SURVEY_BATCH csb where 1=1]]>
		<if test=" plan_name != null and plan_name != '' "><![CDATA[and csb.PLAN_NAME like '%${plan_name}%' ]]></if>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ and csb.organ_code = #{organ_code} ]]></if>
		<if test=" startDate != null and endDate != null "><![CDATA[ and csb.UPLOAD_DATE between #{startDate} and #{endDate} ]]></if>
	</select>
	<!-- caoyy_wb 查询发起过调查的赔案,时间范围内 -->
	<select id="queryClaimCaseFindSurvey" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT CAS.CASE_ID, CAS.ACCIDENT_ID, CAS.CASE_NO, CAS.INSURED_ID, CAS.RPTR_RELATION, CAS.RPTR_NAME, CAS.RPTR_MP, CAS.RPTR_ZIP, CAS.RPTR_ADDR, CAS.RPTR_EMAIL, CAS.REPORT_MODE, CAS.ORGAN_CODE, CAS.RPTR_TIME, CAS.CASE_APPLY_TYPE, CAS.APPLY_DATE, CAS.ACCEPTOR_ID, CAS.ACCEPT_TIME, CAS.TRUSTEE_TYPE, CAS.TRUSTEE_CODE, CAS.TRUSTEE_NAME, CAS.TRUSTEE_MP, CAS.TRUSTEE_TEL, CAS.TRUSTEE_CERTI_TYPE, CAS.TRUSTEE_CERTI_CODE, CAS.DOOR_SIGN_TIME, CAS.SIGN_TIME, CAS.RPTR_ID, CAS.SIGNER_ID, CAS.SERIOUS_DISEASE, CAS.ACCIDENT_DETAIL, CAS.CURE_HOSPITAL, CAS.CURE_STATUS, CAS.DOCTOR_NAME, CAS.MED_DEPT, CAS.CASE_SUB_STATUS, CAS.CASE_STATUS, CAS.GREEN_FLAG, CAS.IS_BPO, CAS.CASE_FLAG, CAS.REVIEW_FLAG, CAS.COMFORT_FLAG, CAS.ADVANCE_ASK_FLAG, CAS.ADVANCE_FLAG, CAS.IS_DEDUCT_FLAG, CAS.REPEAL_REASON, CAS.REPEAL_DESC, CAS.REGISTER_ID, CAS.REGISTE_TIME, CAS.REGISTE_CONF_TIME, CAS.ACCEPT_DECISION, CAS.REJECT_REASON, CAS.CALC_PAY, CAS.ADVANCE_PAY, CAS.BALANCE_PAY, CAS.ACTUAL_PAY, CAS.REJECT_PAY, CAS.AUDIT_TIME, CAS.AUDITOR_ID, CAS.AUDIT_DECISION, CAS.AUDIT_REMARK, CAS.AUDIT_REJECT_REASON, CAS.OTHER_REASON, CAS.APPROVER_ID, CAS.APPROVE_TIME, CAS.APPROVE_DECISION, CAS.APPROVE_REJECT_REASON, CAS.APPROVE_REMARK, CAS.END_CASE_TIME, CAS.OVER_COMP_FLAG, CAS.RELATED_NO, CAS.CLAIM_SOURCE, CAS.IS_COMMON, CAS.INSERT_BY, CAS.INSERT_TIME, CAS.INSERT_TIMESTAMP, CAS.UPDATE_BY, CAS.UPDATE_TIME, CAS.UPDATE_TIMESTAMP, CAS.AUDIT_PERMISSION_NAME, CAS.APPROVE_PERMISSION_NAME, CAS.SURVEY_RESULT_INFO, CAS.CHANNEL_CODE, CAS.IS_AUTO_HUNGUP, CAS.SPECIAL_REMARK_CODE, CAS.LOSS_REASON_CODE, CAS.LOSS_LEVEL_CODE, CAS.COMFORT_STATUS, CAS.IS_MIGRATION
				  FROM APP___CLM__DBUSER.T_CLAIM_CASE cas, 
				       APP___CLM__DBUSER.T_SURVEY_APPLY app
				 where cas.case_id = app.case_id ]]>
		<if test="case_status != null and case_status != ''">
				 	 <![CDATA[ and cas.case_status = #{case_status} ]]>
		</if>
		<if test="approve_start_date != null and approve_start_date != ''">
				 	<![CDATA[ and cas.end_case_time >= #{approve_start_date} ]]>
		</if>
		<if test="approve_end_date != null and approve_end_date != ''">
				 	<![CDATA[ and cas.end_case_time <= #{approve_end_date} ]]>
		</if>
	</select>
	<!-- 查询部分给付的赔案 已废弃 -->
	<select id="queryClaimCaseShortGive" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM APP___CLM__DBUSER.T_CLAIM_CASE cas, APP___CLM__DBUSER.T_CLAIM_LIAB app WHERE cas.case_id = app.case_id AND app.liab_conclusion='2']]>
		<if test="case_status != null and case_status != ''">
		 	 <![CDATA[ and cas.case_status = #{case_status} ]]>
		</if>
		<if test="approve_start_date != null and approve_start_date != ''">
		 	<![CDATA[ and cas.end_case_time >= #{approve_start_date} ]]>
		</if>
		<if test="approve_end_date != null and approve_end_date != ''">
		 	<![CDATA[ and cas.end_case_time <= #{approve_end_date} ]]>
		</if>
	</select>
	<!-- end -->

	<!-- 通过条件查询时间范围内赔案数据 -->
	<select id="findAllClaimCaseBy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, 
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, 
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, 
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE ROWNUM <=  1000  ]]>
		<if test="case_flag != null and case_flag != ''">
			<![CDATA[ and A.case_flag = #{case_flag} ]]>
		</if>
		<if test="case_status != null and case_status != ''">
			<![CDATA[  and A.case_status = #{case_status} ]]>
		</if>
		<if test="approve_start_date != null and approve_start_date != ''">
			<![CDATA[  and A.end_case_time >= #{approve_start_date} ]]>
		</if>
		<if test="approve_end_date != null and approve_end_date != ''">
			<![CDATA[  and A.end_case_time <= #{approve_end_date} ]]>
		</if>
		<if test="related_no != null and related_no != ''">
			<![CDATA[  and A.related_no is not null ]]>
		</if>
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>

	<!-- liulei_wb 查询累加器赔付金额 -->
	<select id="findAccutionAccumuPay" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCUMU_OVER, A.END_DATE, A.INSURED_ID, A.START_DATE, 
			A.ACCUMU_DEDUCT, A.CONTINUED_CASE, A.POLICY_CODE, A.ACCUMU_SELFPAY, A.ACCUMU_PAY, A.ACCUMU_SERVICE, 
			A.ACCUTION_ID, A.ACCUTOR_ID, A.FAMILY_ID, A.POLICY_ID,A.ACCUMU_TYPE, A.BILL_ID FROM APP___CLM__DBUSER.T_ACCUTION A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_id != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" accutor_id  != null "><![CDATA[ AND A.ACCUTOR_ID = #{accutor_id} ]]></if>
		<if test=" bill_id  != null "><![CDATA[ AND A.BILL_ID = #{bill_id} ]]></if>
		<![CDATA[ ORDER BY A.ACCUTION_ID ]]>
	</select>
	<!-- end -->
	<!-- zhangjy_wb 根据赔案ID 查询涉及保单相关信息 -->
	<select id="findAllPolicyMsgByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			select p.policy_code,
			       p.holder_id,
			       p.holder_name,
			       p.insured_id,
			       ct.customer_name insured_name,
			       p.validdate_date,
			       p.expiry_date
			  from (select distinct m.policy_code,
			                        m.validdate_date,
			                        m.expiry_date,
			                        h.customer_id    holder_id,
			                        l.customer_id    insured_id,
			                        c.customer_name  holder_name
			          from APP___CLM__DBUSER.T_CONTRACT_MASTER m,
			               APP___CLM__DBUSER.T_POLICY_HOLDER   h,
			               APP___CLM__DBUSER.T_INSURED_LIST    l,
			               APP___CLM__DBUSER.T_CUSTOMER        c
			         where m.policy_id = h.policy_id
			           and m.policy_id = l.policy_id
			           and h.customer_id = c.customer_id
			           and m.cur_flag = 1
			           and h.cur_flag = 1
			           and l.cur_flag = 1 
			           and m.case_id = #{case_id} ) p,
			       APP___CLM__DBUSER.T_CUSTOMER ct
			 where p.insured_id = ct.customer_id
		]]>
	</select>
	<!-- 查询关怀历史信息 -->
	<select id="queryCareHistoryInfoId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT distinct vis.case_no,
                cus.customer_name,
                (select to_char(wm_concat(tcy.name))
                   from APP___CLM__DBUSER.T_CLAIM_SUB_CASE sub,
                        APP___CLM__DBUSER.t_Claim_Type tcy
                  where tcy.code = sub.claim_type
                    and sub.case_id = cas.case_id
                  group by sub.case_id) claim_type, 
                cas.end_case_time,
                vis.visit_date,
                vis.visite_by,
                vis.care_times,
                vis.service_status,
                vis.visit_id
  FROM APP___CLM__DBUSER.T_CLAIM_CASE       cas,
       APP___CLM__DBUSER.T_CLAIM_CARE_VISIT vis,
       APP___CLM__DBUSER.T_CLAIM_CARE       care,
       APP___CLM__DBUSER.T_CUSTOMER         cus
 WHERE cas.case_id = care.case_id
   AND cas.case_status = '80'
   AND vis.case_id = cas.case_id
   AND cus.customer_id = cas.insured_id
          AND vis.case_no = #{case_no}]]>
          <![CDATA[ ORDER BY vis.visit_date ]]>
	</select>
	<!-- liulei_wb 查询发起慰问规则所需参数 -->
	<select id="findComfortParameter" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT B.CUSTOMER_VIP,
			       C.ACC_REASON,
			       C.CLAIM_TYPE,
			       D.AGENT_TYPE
			  FROM APP___CLM__DBUSER.T_CLAIM_CASE     A,
			       APP___CLM__DBUSER.T_CUSTOMER       B,
			       APP___CLM__DBUSER.T_CLAIM_SUB_CASE C,
			       APP___CLM__DBUSER.T_CONTRACT_AGENT D
			 WHERE A.INSURED_ID = B.CUSTOMER_ID
			   AND A.CASE_ID = C.CASE_ID
			   AND A.CASE_ID = D.CASE_ID
			   AND D.CUR_FLAG = 1
			   AND A.CASE_ID = #{case_id}
		]]>
	</select>
	<!-- 撤销保单挂起批作业 -->
	<select id="queryRescindPolicyHangUp" parameterType="java.util.Map"
		resultType="java.util.Map"> 
        <![CDATA[ select distinct c.case_id,c.case_no, c.case_status, c.rptr_time, s.claim_type, m.policy_id, m.policy_code from APP___CLM__DBUSER.T_claim_case c, APP___CLM__DBUSER.T_claim_sub_case s, APP___CLM__DBUSER.T_contract_master m,APP___PAS__DBUSER.T_LOCK_POLICY cy where c.case_id = s.case_id and c.case_id = m.case_id and m.cur_flag = 1 and s.claim_type != '01' and cy.business_code = c.case_no and (cy.except_group_1 is null or  cy.except_group_2 is null) and  c.rptr_time < decode(#{tempTime},'',sysdate,to_date(#{tempTime},'yyyy/MM/dd'))-#{customTime} and c.case_status = '20'   ]]>
		<if test="case_no != null and case_no != ''">
		   	 <![CDATA[ and c.case_no = #{case_no} ]]>
		</if>
        <![CDATA[ AND MOD(c.CASE_ID,#{modNum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR} ]]>
	</select>
	<select id="queryAllRescindPolicy" parameterType="java.util.Map"
		resultType="java.util.Map"> 
        <![CDATA[ select B.* from( select A.*,rownum RN from( select distinct c.case_id,c.case_no, c.case_status, c.rptr_time, s.claim_type, m.policy_id, m.policy_code from APP___CLM__DBUSER.T_claim_case c, APP___CLM__DBUSER.T_claim_sub_case s, APP___CLM__DBUSER.T_contract_master m where c.case_id = s.case_id and c.case_id = m.case_id and m.cur_flag = 1 and s.claim_type != '01' and to_date(to_char(c.rptr_time,'yyyy/MM/dd'),'yyyy/MM/dd') < sysdate-#{customTime} and c.case_status = '20'  ]]>
		<if test="case_no != null and case_no != ''">
		   	 <![CDATA[ and c.case_no = #{case_no} ]]>
		</if>
        <![CDATA[ ) A where rownum <= #{endNum}) B where RN >= #{startNum}]]>
	</select>

	<!--理赔关怀服务任务清单 -->

	<select id="findClaimCareOrderTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
		 	<![CDATA[ select count(1) from ( select distinct a.case_id,
                                        a.case_no,
                                        (select cus.customer_name
                                           from APP___CLM__DBUSER.T_customer cus
                                          where cus.customer_id = a.insured_id and rownum=1) as customer_name, --出险人姓名
                                        (select tuo.organ_name
                                           from APP___CLM__DBUSER.t_udmp_org tuo
                                          where tuo.organ_code = c.organ_code and rownum=1) organ_name, --管理机构                                                            
                                        (select to_char(wm_concat(distinct csc.claim_type)) from APP___CLM__DBUSER.T_claim_sub_case csc where csc.case_id = a.case_id) claim_type,--理赔类型
                                        case
                                          when g.care_object_type = '00' then
                                           (select customer_name
                                              from APP___CLM__DBUSER.t_customer
                                             where a.insured_id =
                                                   customer_id and rownum=1)
                                          else
                                           (select customer_name
                                              from APP___CLM__DBUSER.t_customer
                                             where g.care_object_id = customer_id and rownum=1)
                                        end careObject_name,
                                        (select lpr.relation_name
                                           from APP___CLM__DBUSER.T_LA_PH_RELA lpr
                                          where lpr.relation_code =
                                                g.care_object_type and rownum=1) relation_name,
                                        (select tcc.name
                                           from APP___CLM__DBUSER.T_CARE_CROWD tcc
                                          where tcc.code = g.care_crowd and rownum=1) carecrowd_name,
                                        g.contact_address,
                                        g.contact_phone,
                                        g.visite_by,
                                        g.visitor_phone,
                                        (select tss.name
                                           from APP___CLM__DBUSER.T_SERVICE_STATE tss
                                          where tss.code = g.service_status and rownum=1) as servicestatus_name, --服务状态  
                                        g.care_times,
                                        g.next_visit_date nextvisit_date,
                                        g.update_time,
                                        c.insert_time,
                                        c.care_id
                          from APP___CLM__DBUSER.T_claim_care       c,
                           APP___CLM__DBUSER.T_claim_case       a, 
                           APP___CLM__DBUSER.T_claim_care_visit g
                         where a.case_status = '80'
                         and  c.case_id = a.case_id(+)
                         and c.care_id = g.care_id(+) ]]>

		<if test="statistic_time_start != null and statistic_time_start != ''">
		   	 <![CDATA[ and TRUNC(c.insert_time) >= #{statistic_time_start} ]]>
		</if>
		<if test="statistic_time_end != null and statistic_time_end != ''">
		   	 <![CDATA[ and TRUNC(c.insert_time) <= #{statistic_time_end} ]]>
		</if>
		<if test="return_visit_status != null and return_visit_status != ''">
		   	 <![CDATA[ and c.return_visit_status = #{return_visit_status} ]]>
		</if>
		<if test="organ_code != null and organ_code != ''">
		   	 <![CDATA[ and c.organ_code like '${organ_code}%' ]]>
		</if>
		   <![CDATA[  )  ]]>
	</select>

	<select id="findClaimCareOrderForPage" parameterType="java.util.Map"
		resultType="java.util.Map">
		 <![CDATA[select N.*
  from (select M.*, rownum RN
          from (select (select trunc((select (L.update_time -
                                            (select ccv.insert_time
                                                from APP___CLM__DBUSER.T_claim_care ccv
                                               where L.care_id = ccv.care_id
                                                 and rownum = 1)) * 24
                                       from dual),
                                     2)
                          from dual) spac_time， L.*
                  from (select distinct a.case_id,
                                        a.case_no,
                                        (select cus.customer_name
                                           from APP___CLM__DBUSER.T_customer cus
                                          where cus.customer_id = a.insured_id and rownum=1) as customer_name, --出险人姓名
                                        (select tuo.organ_name
                                           from APP___CLM__DBUSER.t_udmp_org tuo
                                          where tuo.organ_code = c.organ_code and rownum=1) organ_name, --管理机构                                                            
                                        (select to_char(wm_concat(distinct csc.claim_type)) from APP___CLM__DBUSER.T_claim_sub_case csc where csc.case_id = a.case_id) claim_type,--理赔类型
                                        case
                                          when g.care_object_type = '00' then
                                           (select customer_name
                                              from APP___CLM__DBUSER.t_customer
                                             where a.insured_id =
                                                   customer_id and rownum=1)
                                          else
                                           (select customer_name
                                              from APP___CLM__DBUSER.t_customer
                                             where g.care_object_id = customer_id and rownum=1)
                                        end careObject_name,
                                        (select lpr.relation_name
                                           from APP___CLM__DBUSER.T_LA_PH_RELA lpr
                                          where lpr.relation_code =
                                                g.care_object_type and rownum=1) relation_name,
                                        (select tcc.name
                                           from APP___CLM__DBUSER.T_CARE_CROWD tcc
                                          where tcc.code = g.care_crowd and rownum=1) carecrowd_name,
                                        g.contact_address,
                                        g.contact_phone,
                                        g.visite_by,
                                        g.visitor_phone,
                                        (select tss.name
                                           from APP___CLM__DBUSER.T_SERVICE_STATE tss
                                          where tss.code = g.service_status and rownum=1) as servicestatus_name, --服务状态  
                                        g.care_times,
                                        g.next_visit_date nextvisit_date,
                                        g.update_time,
                                        c.insert_time,
                                        c.care_id
                          from APP___CLM__DBUSER.T_claim_care       c,
                           APP___CLM__DBUSER.T_claim_case       a, 
                           APP___CLM__DBUSER.T_claim_care_visit g
                         where a.case_status = '80'
                         and  c.case_id = a.case_id(+)
                         and c.care_id = g.care_id(+)
                   ]]>

		<if test="statistic_time_start != null and statistic_time_start != ''">
		   	 <![CDATA[ and TRUNC(c.insert_time) >= #{statistic_time_start} ]]>
		</if>
		<if test="statistic_time_end != null and statistic_time_end != ''">
		   	 <![CDATA[ and TRUNC(c.insert_time) <= #{statistic_time_end} ]]>
		</if>
		<if test="return_visit_status != null and return_visit_status != ''">
		   	 <![CDATA[ and c.return_visit_status = #{return_visit_status} ]]>
		</if>
		<if test="organ_code != null and organ_code != ''">
		   	 <![CDATA[ and c.organ_code like '${organ_code}%' ]]>
		</if>
		   <![CDATA[ order by a.case_no) L )
		   M where rownum <= #{LESS_NUM} )N
		   where RN > #{GREATER_NUM} ]]>
	</select>

	<select id="UwInfoQueryByCaseNo" parameterType="java.util.Map"
		resultType="java.util.Map">
		SELECT DISTINCT CU.CASE_NO,
		CU.CLAIM_UW_TYPE,
		CU.UW_STATUS,
		CU.APPLY_DATE,
		CU.POLICY_CODE,
		CU.NOT_INFORM_SITUATION,
		UP.UW_USER_ID,
		UP.POLICY_DECISION,
		UP.UW_FINISH_TIME,
		C.CUSTOMER_ID,
		C.CUSTOMER_NAME,
		C.HEALTH_STATUS
		FROM APP___CLM__DBUSER.T_CLAIM_UW CU,
		APP___CLM__DBUSER.T_UW_POLICY UP,
		APP___CLM__DBUSER.T_UW_MASTER UM,
		APP___CLM__DBUSER.T_CUSTOMER C ,
		APP___CLM__DBUSER.T_POLICY_HOLDER PH
		WHERE CU.CASE_NO =UM.BIZ_CODE
		AND UM.UW_ID=UP.UW_ID
		AND CU.POLICY_CODE=UP.POLICY_CODE
		AND CU.UW_STATUS = '1'
		AND C.CUSTOMER_ID =PH.CUSTOMER_ID
		AND PH.CASE_ID = CU.CASE_ID
		AND PH.POLICY_ID =CU.POLICY_ID
		AND PH.CUR_FLAG='1'
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND CU.CASE_NO = #{case_no} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != '' "><![CDATA[ AND C.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" customer_name != null and customer_name != '' "><![CDATA[ AND C.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_birthday != null and customer_birthday != '' "><![CDATA[ AND C.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender != null and customer_gender != '' "><![CDATA[ AND C.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != '' "><![CDATA[ AND C.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
	</select>

	<select id="UwInfoQueryClaimRelFlag" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT CU.CLM_UW_ID FROM APP___CLM__DBUSER.T_CLAIM_POLICY CP,APP___CLM__DBUSER.T_CLAIM_UW CU
				WHERE CP.POLICY_CODE= CU.POLICY_CODE
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND CU.POLICY_CODE = #{policy_code} ]]></if>
	</select>

	<select id="UwInfoQueryToBusiPro" parameterType="java.util.Map"
		resultType="java.util.Map">
		SELECT DISTINCT CU.CASE_NO,
		UB.POLICY_CODE,
		UB.BUSI_ITEM_ID,
		UB.BUSI_PROD_CODE,
		BP.PRODUCT_NAME_SYS,
		UB.DECISION_CODE
		FROM APP___CLM__DBUSER.T_CLAIM_UW CU,
		APP___CLM__DBUSER.T_UW_POLICY UP,
		APP___CLM__DBUSER.T_UW_MASTER UM,
		APP___CLM__DBUSER.T_UW_BUSI_PROD UB,
		APP___CLM__DBUSER.T_BUSINESS_PRODUCT BP,
		APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD CB,
		APP___CLM__DBUSER.T_POLICY_HOLDER PH
		WHERE CU.CASE_NO = UM.BIZ_CODE
		AND CU.POLICY_ID =UP.POLICY_ID
		AND CU.UW_STATUS='1'
		AND UM.UW_ID = UP.UW_ID
		AND UP.UW_POLICY_ID = UB.UW_POLICY_ID
		AND UP.UW_ID = UB.UW_ID
		AND UB.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
		AND BP.BUSINESS_PRD_ID = CB.BUSI_PRD_ID
		AND CB.POLICY_ID = CU.POLICY_ID
		AND CB.CASE_ID =CU.CASE_ID
		AND PH.CASE_ID = CU.CASE_ID
		AND PH.POLICY_ID =CU.POLICY_ID
		AND PH.CUR_FLAG='1'
		<if test="case_no != null and case_no != '' and case_no !='null'"><![CDATA[ AND CU.CASE_NO = #{case_no} ]]></if>
		<if test="customer_id != null and customer_id != ''"><![CDATA[ AND PH.CUSTOMER_ID = #{customer_id} ]]></if>
	</select>

	<!-- add by zhaoyq -->
	<select id="findDetailByCaseNo" parameterType="java.util.Map"
		resultType="java.util.Map">
		select a.case_id,
		a.case_no,
		a.green_flag,
		a.case_status,
		a.sign_time,
		a.registe_time,
		a.registe_conf_time,
		a.audit_time,
		a.approve_time,
		a.end_case_time,
		r.customer_id,
		r.customer_name
		from APP___CLM__DBUSER.T_claim_case a, APP___CLM__DBUSER.T_customer r
		where a.insured_id = r.customer_id
		and a.case_no=#{case_no}
	</select>
	<!-- end -->

	<!-- add by renxiaodi -->
	<select id="findExemptReasonDesc" parameterType="java.util.Map"
		resultType="java.util.Map">
		select P.CODE, P.NAME from APP___CLM__DBUSER.T_EXEMPT_REASON p where p.name
		= #{name}
	</select>

	<!--liulei_wb 查询本赔案赔付后，涉案保单发生的其他赔案 -->
	<select id="queryClaimCaseByPolicy" parameterType="java.util.Map"
		resultType="java.util.Map">
 		<![CDATA[
 		SELECT DISTINCT a.*
	      FROM APP___CLM__DBUSER.T_CLAIM_CASE A,
	           APP___CLM__DBUSER.T_CLAIM_POLICY B,
	           (SELECT POLICY_ID FROM APP___CLM__DBUSER.T_CLAIM_POLICY WHERE CASE_ID = #{case_id}) C
	     WHERE A.CASE_ID = B.CASE_ID
		   AND B.POLICY_ID = C.POLICY_ID
		   AND (A.END_CASE_TIME > (SELECT D.END_CASE_TIME FROM APP___CLM__DBUSER.T_CLAIM_CASE D WHERE D.CASE_ID = #{case_id}) OR A.END_CASE_TIME IS NULL)
		   and a.case_status != 90 and a.case_status != 99
 		]]>
	</select>

	<!-- add by liutao -->
	<select id="findAllBeneByCaseId" parameterType="java.util.Map"
		resultType="java.util.Map"> 
        <![CDATA[
SELECT distinct TCB.BENE_NAME,
        TCB.BENE_NATION,
        TC.company_name,
        TCB.BENE_CERTI_TYPE,
        TCB.BENE_CERTI_NO,
        TCB.BENE_BIRTH,
        TCB.BENE_ID,
        TCB.BENE_NO,
        TCB.BENE_SEX,
        TCP.POLICY_CODE ,
        (select case_no from  APP___CLM__DBUSER.t_claim_case where case_id=TCP.CASE_ID) case_no,
         ( select sum(t2.pay_amount) from  APP___CLM__DBUSER.T_CLAIM_BENE t1,APP___CLM__DBUSER.T_CLAIM_PAY t2 where t1.bene_id=t2.bene_id and t2.case_id=tcp.case_id )  pay_amount
   FROM APP___CLM__DBUSER.T_CLAIM_BENE TCB, APP___CLM__DBUSER.T_CUSTOMER TC, APP___CLM__DBUSER.T_CLAIM_PAY TCP
  WHERE TCP.BENE_ID = TCB.BENE_ID
    AND TCB.Bene_No = TC.CUSTOMER_ID(+)
    AND TCP.PAY_AMOUNT !=0
    AND TCP.CASE_ID =#{case_id}

]]>

	</select>
	<!--end -->
	<!-- 分次给付支付批处理Service实现 查询单条数据 claimPay add by xuyz -->
	<select id="findClaimPayByClaimPayId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.*,(SELECT CFM.CAP_FEE_CODE FROM APP___CLM__DBUSER.T_CLAIM_FEE_MAPPING CFM WHERE CFM.CLM_FEE_CODE = TRIM(TCA.ADJUST_TYPE)) fee_type
             from APP___CLM__DBUSER.T_claim_pay A,APP___CLM__DBUSER.T_Claim_Adjust_Busi TCA WHERE 1 = 1 AND A.ADJUST_BUSI_ID = TCA.Adjust_Busi_Id(+) ]]>
		<if test=" claim_pay_id  != null "><![CDATA[ AND A.CLAIM_PAY_ID = #{claim_pay_id} ]]></if>
	</select>
	<!-- end -->
	<!-- 查询调查抽取任务表关联查询调查表 调查状态 1 已申请 -->
	<select id="findAllClaimSurveyTaskAndSurveyApplyClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,
       A.BATCH_ID,
       A.BIZ_TYPE,
       A.BIZ_NO,
       A.CUSTOMER_NAME,
       A.CUSTOMER_ID,
       A.CHANNEL_TYPE,
       A.CLAIM_PAY,
       A.PAYEE_NUM,
       A.ACCU_AMOUNT,
       A.APPLY_NUM,
       A.INSERT_TIME,
       A.INSERT_BY,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIMESTAMP,
       A.UPDATE_TIME,
       A.ORGAN_CODE,
       B.apply_id 
       FROM APP___CLM__DBUSER.T_CLAIM_SURVEY_TASK A,APP___CLM__DBUSER.T_SURVEY_APPLY B WHERE 1=1 AND A.List_Id = B.Survey_Rule_Id AND B.Survey_Status = 1 ]]>
		<if test=" biz_no  != null "><![CDATA[ AND A.BIZ_NO = #{biz_no} ]]></if>
		<if test=" batch_id  != null "><![CDATA[ AND A.BATCH_ID = #{batch_id} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
	</select>


	<!--查询理算数据 -->
	<select id="findAllClaimPayByCaseIdInCom" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		
		
SELECT  PE.CLAIM_LIAB_ID,
        PE.PREM_ARAP_ID,
        PE.ADJUST_BUSI_ID,
        PE.PAYEE_ID,
        PE.REMARK,
        PE.ADVANCE_FLAG,
        PE.PAY_MOLE,
        PE.IS_INSTALMENT,
        PE.CASE_ID,
        PE.BUSI_PROD_CODE,
        PE.BENE_ID,
        PE.POLICY_CODE,
        PE.CLAIM_PAY_ID, 
        PE.BUSI_ITEM_ID,
        PE.PAY_DENO,
        PE.POLICY_ID,
        PE.CONTRARY_PAY_FLAG,  
        PE.LIAB_NAME,
        PE.Actual_Pay,
        PE.CLAIM_TYPE,
        PE.FEE_TYPE, PE.adjust_type,
         PE.PRODUCT_CODE,
     CASE
         WHEN COUNT(PE.CLAIM_LIAB_ID) OVER(PARTITION BY PE.CLAIM_LIAB_ID) > 1 AND
              COUNT(PE.CLAIM_LIAB_ID)
          OVER(PARTITION BY PE.CLAIM_LIAB_ID) = COUNT(PE.PAY_AMOUNT)
          OVER(PARTITION BY PE.CLAIM_LIAB_ID ORDER BY ROWNUM) THEN
          ROUND(PE.ACTUAL_PAY - SUM(ROUND(PE.PAY_AMOUNT, 2))
                OVER(PARTITION BY PE.CLAIM_LIAB_ID ORDER BY ROWNUM) +
                PE.PAY_AMOUNT,
                2)
         ELSE
          ROUND(PE.PAY_AMOUNT, 2)
       END PAY_AMOUNT


    FROM (
          
SELECT A.PAY_MOLE  / A.PAY_DENO  BENE_RATE,TCL.CLAIM_LIAB_ID,
       A.PREM_ARAP_ID,
       A.ADJUST_BUSI_ID,
       A.PAYEE_ID,
       A.REMARK,
       A.ADVANCE_FLAG,
       A.PAY_MOLE,
       A.IS_INSTALMENT,
       A.CASE_ID,
       A.BUSI_PROD_CODE,
       A.BENE_ID,
       A.POLICY_CODE,
       A.CLAIM_PAY_ID, 
       A.BUSI_ITEM_ID,
       A.PAY_DENO,
       A.POLICY_ID,
       A.CONTRARY_PAY_FLAG,
       CASE WHEN A.ADVANCE_FLAG='0' THEN 
       (TCL.Actual_Pay * nvl(A.PAY_MOLE,1)) / nvl(A.PAY_DENO,1) 
       ELSE (  select nvl( ADVANCE_PAY,'0')/decode(ADVANCE_PAY,'','1','0','1',ADVANCE_PAY) 
          from   APP___CLM__DBUSER.T_CLAIM_CASE  where case_id=a.case_id)
         * (TCL.ADVANCE_PAY * A.PAY_MOLE) / A.PAY_DENO  END  PAY_AMOUNT,
       TCL.LIAB_NAME,
       TCL.Actual_Pay,
       TCL.CLAIM_TYPE,
       (SELECT TCA.ACC_REASON
          FROM APP___CLM__DBUSER.T_CLAIM_ACCIDENT TCA, APP___CLM__DBUSER.T_CLAIM_CASE TCC
         WHERE TCA.ACCIDENT_ID = TCC.ACCIDENT_ID
           AND TCC.CASE_ID = TCL.CASE_ID) ACC_REASON,
       DECODE(A.ADVANCE_FLAG,'0','P005010000','1','P005140100','P005010000')    FEE_TYPE,'' adjust_type,
       TCL.LIAB_CODE PAY_LIAB_CODE,
         (SELECT  P.INTERNAL_ID FROM APP___PDS__DBUSER.T_PRODUCT_LIFE P  WHERE P.PRODUCT_ID=TCL.PRODUCT_ID  
                                                   ) PRODUCT_CODE
  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCL, APP___CLM__DBUSER.T_CLAIM_PAY A
 WHERE TCL.CASE_ID = A.CASE_ID
   AND TCL.BUSI_ITEM_ID = A.BUSI_ITEM_ID
   AND tcl.busi_prod_code = a.busi_prod_code
   and a.adjust_busi_id is null
   and a.case_id = #{case_id} 
      ]]>
	<if test=" advance_flag  != null "><![CDATA[ and A.ADVANCE_FLAG = ${advance_flag} ]]></if>
	<![CDATA[ 
	  ) PE
	 ]]>
	
</select>
<!-- 查询追偿款数据 -->
<!--查询理算数据  -->
		<select id="findBackPayeeByCaseIdInCom" resultType="java.util.Map"
	parameterType="java.util.Map">
		<![CDATA[ 
		SELECT AA.PAYEE_NO,
       AA.CASE_ID,
       AA.OLD_PAY,
       AA.NEW_PAY,
       AA.OLD_PAY - AA.NEW_PAY BACK_PAY,
       AA.PAYEE_NAME
  FROM (SELECT A.CASE_ID,
               SUM(DECODE(B.ARAP_CODE, '01', -1, 1) * A.PAY_AMOUNT) NEW_PAY,
               C.PAYEE_NO,
               nvl((SELECT SUM(H.FEE_AMOUNT * DECODE(H.ARAP_FLAG, '1', -1, 1))
                     FROM APP___CLM__DBUSER.T_PREM_ARAP H
                    WHERE H.BUSINESS_CODE = F.CASE_NO
                     AND (H.CUSTOMER_ID = C.PAYEE_NO or
                          (c.payee_No is null and
                          c.payee_name = h.payee_name))
                      AND H.FEE_TYPE != 'G005130000'
                      AND H.UNIT_NUMBER IS NOT NULL),
                   0) OLD_PAY,
               c.payee_name
          FROM APP___CLM__DBUSER.T_CLAIM_PAY         A,
               APP___CLM__DBUSER.T_CLAIM_FEE_MAPPING B,
               APP___CLM__DBUSER.T_CLAIM_PAYEE       C,
               APP___CLM__DBUSER.T_CLAIM_ADJUST_BUSI E,
               APP___CLM__DBUSER.T_CLAIM_CASE        F
         WHERE A.ADJUST_BUSI_ID = E.ADJUST_BUSI_ID(+)
           AND E.ADJUST_TYPE = B.CLM_FEE_CODE(+)
           AND A.PAYEE_ID = C.PAYEE_ID
           AND A.CASE_ID = F.CASE_ID
           AND A.ADVANCE_FLAG != 1
           AND A.CASE_ID = #{case_id} 
         GROUP BY A.CASE_ID, C.PAYEE_NO, F.CASE_NO, c.payee_name
        UNION ALL
        SELECT A.CASE_ID,
               0 NEW_PAY,
               B.CUSTOMER_ID,
               SUM(DECODE(B.Arap_Flag, '1', -1, 1) * B.FEE_AMOUNT) OLD_PAY,
               B.PAYEE_NAME
          FROM APP___CLM__DBUSER.t_claim_case a
         inner join APP___CLM__DBUSER.t_prem_arap b
            on a.case_no = b.business_code
         where NOT EXISTS (SELECT 1
                  FROM APP___CLM__DBUSER.T_CLAIM_PAYEE CP
                 WHERE CP.PAYEE_NO = B.CUSTOMER_ID
                   AND A.CASE_ID = CP.CASE_ID)
           AND NOT EXISTS (SELECT 1
                  FROM APP___CLM__DBUSER.T_CLAIM_PAYEE CP
                 WHERE CP.PAYEE_NO IS NULL
                   AND CP.PAYEE_NAME = B.PAYEE_NAME
                   AND A.CASE_ID = CP.CASE_ID)
           AND B.FEE_TYPE != 'G005130000'
           AND B.UNIT_NUMBER IS NOT NULL
           AND a.case_id = #{case_id} 
         GROUP BY A.CASE_ID, B.CUSTOMER_ID, B.PAYEE_NAME, A.CASE_NO) AA
 WHERE NEW_PAY < OLD_PAY
   
      ]]>
	 
	
</select>

<!--查询结算数据  -->
<select id="findAllClaimPayAdjustByCaseId" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[   
	SELECT  A.PAY_MOLE  / A.PAY_DENO  BENE_RATE, 
	       A.PREM_ARAP_ID,
	       A.ADJUST_BUSI_ID,
	       A.PAYEE_ID,
	       A.REMARK,
	       A.ADVANCE_FLAG,
	       A.PAY_MOLE,
	       A.IS_INSTALMENT,
	       A.CASE_ID,
	       A.BUSI_PROD_CODE,
	       A.BENE_ID,
	       A.POLICY_CODE,
	       A.CLAIM_PAY_ID, 
	       A.BUSI_ITEM_ID,
	       A.PAY_DENO,
	       A.POLICY_ID,
	       A.CONTRARY_PAY_FLAG,
	       A.PAY_AMOUNT, 
	       (SELECT min(TCC.claim_type)  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCC WHERE TCC.CASE_ID=A.CASE_ID AND    TCC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
	        AND TCC.BUSI_PROD_CODE = A.BUSI_PROD_CODE  )  CLAIM_TYPE,
	        (SELECT TCA.ACC_REASON
	          FROM APP___CLM__DBUSER.T_CLAIM_ACCIDENT TCA, APP___CLM__DBUSER.T_CLAIM_CASE TCC
	         WHERE TCA.ACCIDENT_ID = TCC.ACCIDENT_ID
	           AND TCC.CASE_ID = A.CASE_ID) ACC_REASON,(SELECT 
	       decode(max(TTT.CAP_FEE_CODE),'P005420300','P005180100','G005060200','G005060000'
	       ,'G005060200','G005060000','G005060200','G005060000','P005170200','P005170100',
	        'P005490200','P005470300',max(TTT.CAP_FEE_CODE) ) 
	            FROM APP___CLM__DBUSER.T_CLAIM_FEE_MAPPING TTT
	               WHERE TTT.CLM_FEE_CODE =TRIM(TCA.ADJUST_TYPE)) FEE_TYPE,TCA.ADJUST_TYPE,TCA.liab_id 
	  FROM APP___CLM__DBUSER.T_CLAIM_PAY A, APP___CLM__DBUSER.T_Claim_Adjust_Busi TCA
	 WHERE A.ADJUST_BUSI_ID = TCA.Adjust_Busi_Id(+)
	   AND A.ADJUST_BUSI_ID is not null
	   and a.case_id = #{case_id} 
 ]]>
		<if test=" advance_flag  != null "><![CDATA[  and A.ADVANCE_FLAG = ${advance_flag} ]]></if>
	</select>

	<!-- add by zhaoyq 历史签收任务用 start -->
	<select id="findClaimTaskManageNoSign" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  
    			select 
			       a.case_id,
			       a.case_no,
			       a.green_flag,
			       a.case_status,
			       a.sign_time,
			       a.registe_time,
			       a.registe_conf_time,
  				   a.audit_time,
  				   a.approve_time,
  				   a.end_case_time,
			       r.customer_id,
			       r.customer_name
 			  from
 			  	 APP___CLM__DBUSER.T_claim_case a ,APP___CLM__DBUSER.T_customer r
 			  where 
      			 a.insured_id = r.customer_id(+) ]]>
		<if test="checkproplem_str != null and checkproplem_str != ''">
      		<![CDATA[ and a.case_no in (${checkproplem_str}) ]]>
		</if>
		<if test="not_checkproplem_str != null and not_checkproplem_str !=''">
      		<![CDATA[ and a.case_no not in (${not_checkproplem_str})  ]]>
		</if>
		<if test="case_no !=null and case_no !='' and case_no !='null'">
      		<![CDATA[and a.case_no = #{case_no}]]>
		</if>
		<if test="signer_id !=null ">
      		<![CDATA[and a.signer_id = #{signer_id}]]>
		</if>
		<if
			test="customer_name !=null and customer_name !='' and customer_name !='null'">
      		<![CDATA[and r.customer_name = #{customer_name}]]>
		</if>
		<if test="customer_gender !=null">
      		<![CDATA[and r.customer_gender = #{customer_gender}]]>
		</if>
		<if
			test="customer_certi_code !=null  and customer_certi_code != ''  and customer_certi_code != 'null'">
      		<![CDATA[and r.customer_certi_code = #{customer_certi_code}]]>
		</if>
		<if
			test="case_status !=null and case_status !='' and case_status != 'null'">
      		<![CDATA[and a.case_status = #{case_status}]]>
		</if>
		<if test="case_status ==null or case_status =='' or case_status == 'null'">
      		<![CDATA[and a.case_status not in('10','20')]]>
		</if>
		<if test="green_flag !=null and case_flag!=null">
      		<![CDATA[and a.green_flag in(#{green_flag},#{case_flag})]]>
		</if>
		<if test="green_flag !=null and case_flag==null">
			<![CDATA[and a.green_flag =#{green_flag}]]>
		</if>
		<if test="case_flag !=null and green_flag==null">
			<![CDATA[and a.green_flag =#{case_flag}]]>
		</if>
		<if test="sign_time !=null and door_sign_time !=null">
      		<![CDATA[and a.sign_time between  #{sign_time} and #{door_sign_time}]]>
		</if>
		<if test="sign_time !=null and door_sign_time ==null">
      		<![CDATA[and a.sign_time >= #{sign_time}]]>
		</if>
		<if test="sign_time ==null and door_sign_time !=null">
      		<![CDATA[and a.sign_time <= #{door_sign_time} ]]>
		</if>
	</select>
	<select id="findClaimTaskManageNoSignNewTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  select count(1)
           from
       			APP___CLM__DBUSER.T_CLAIM_CASE a ,APP___CLM__DBUSER.T_CUSTOMER r
		   where 
      			 a.insured_id = r.customer_id(+)]]>
		<if test="caseNo_all != null and caseNo_all != ''">
      		<![CDATA[ and a.case_no in ${caseNo_all} ]]>
		</if>
	</select>
	<select id="findClaimTaskManageNoSignNewPage" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  
    	select B.* from(
           select A.*,rownum RN from(
    			select 
			       a.case_id,
			       a.case_no,
			       a.green_flag,
			       a.case_status,
			       a.sign_time,
			       a.registe_time,
			       a.registe_conf_time,
  				   a.audit_time,
  				   a.approve_time,
  				   a.end_case_time,
			       r.customer_id,
			       r.customer_name
 			  from
 			  	 APP___CLM__DBUSER.T_claim_case a ,APP___CLM__DBUSER.T_customer r
 			  where 
      			 a.insured_id = r.customer_id(+) ]]>
		<if test="caseNo_all != null and caseNo_all != ''">
      		<![CDATA[ and a.case_no in ${caseNo_all} ]]>
		</if>		 
      		<![CDATA[ ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM}
          ]]>
	</select>
	<!-- add by zhaoyq end -->



	<select id="findBusinessTypeDef" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[  
    	 
   SELECT A.SUB_ID, A.BUSINESS_TYPE_NAME, DECODE(A.BUSINESS_TYPE,'NULL','3001',A.BUSINESS_TYPE) BUSINESS_TYPE, DECODE(A.FEE_TYPE,'NULL','P005010000',A.FEE_TYPE) FEE_TYPE
      FROM (SELECT SUB_ID,
               BUSINESS_TYPE_NAME,
               BUSINESS_TYPE,
               CASE
                 WHEN BUSINESS_TYPE = '3001' THEN
                  'P005010000'
                 WHEN BUSINESS_TYPE = '3002' THEN
                  'P005160400'
                 WHEN BUSINESS_TYPE = '3003' THEN
                  'P005110000'
                 WHEN BUSINESS_TYPE = '3002' THEN
                  'NULL'
                 WHEN BUSINESS_TYPE = '3005' THEN
                  'P005210200'
                 WHEN BUSINESS_TYPE = '3006' THEN
                  'P005150000'
                 WHEN BUSINESS_TYPE = '3007' THEN
                  'G005130000'
                 WHEN BUSINESS_TYPE = '3009' THEN
                  'P005140100'
                 WHEN BUSINESS_TYPE = '3010' THEN
                  'NULL'
                 WHEN BUSINESS_TYPE = '3011' THEN
                  'NULL'
                 ELSE
                  'NULL'
               END FEE_TYPE
          FROM APP___CLM__DBUSER.T_BUSINESS_TYPE_DEF
         WHERE SUB_ID = '067') A
         WHERE A.FEE_TYPE = decode(#{fee_type},'','NULL',#{fee_type}) AND ROWNUM=1
          ]]>
	</select>
	<!-- 通过liab_id查询分期计划 数据 add by xuyz -->
	<select id="queryClaimInstalmentByLiabId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCI.INSTALMENT_ID,
       TCI.CLAIM_PAY_ID,
       TCI.PAY_DUE_DATE,
       TCI.PAY_NUM,
       TCI.PRINCIPAL,
       TCI.COMMU_PAY,
       TCI.INTEREST,
       TCI.PAY_DUE_ID,
       TCI.VALID_FLAG,
       TCI.REVIEW_DATE,
       TCI.INSTAL_TYPE,
       TCI.INSTAL_STATUS,
       TCI.PREM_ARAP_ID,
       TCI.SURVEY_DATE,
       TCI.SURVEY_FLAG,
       TCI.INSERT_BY,
       TCI.INSERT_TIMESTAMP,
       TCI.INSERT_TIME,
       TCI.UPDATE_BY,
       TCI.UPDATE_TIMESTAMP,
       TCI.UPDATE_TIME,
       TCI.SEND_BPM_FLAG,
       TCI.SURVERY_FREQ_CODE,
       TCI.SURVERY_PERIODS,
       TCI.CLAIM_LIAB_ID,
       TCI.TOTAL_TIMES,
       TCI.PAY_TYPE
        from APP___CLM__DBUSER.T_CLAIM_INSTALMENT tci,(select tcp.claim_pay_id,tcl.claim_liab_id 
					from APP___CLM__DBUSER.T_CLAIM_LIAB tcl,APP___CLM__DBUSER.T_CLAIM_PAY tcp 
                    where tcp.case_id=tcl.case_id 
                    and tcp.policy_code=tcl.policy_code 
                    and tcp.busi_prod_code=tcl.busi_prod_code
                    and tcl.case_id=#{case_id} and tcl.liab_id=#{liab_id}) a where tci.claim_pay_id = a.claim_pay_id and a.claim_liab_id = tci.claim_liab_id order by tci.PAY_DUE_DATE asc ]]>
	</select>
	<!--add by xuyz_wb 根据条件查询豁免处理信息 -->
	<select id="queryCompWaiverManageList" parameterType="java.util.Map"
		resultType="java.util.Map">
        <![CDATA[ select distinct a.case_id,
                        b.policy_id,
                        b.policy_code,
                        c.busi_item_id,
                        c.liability_state,
                        c.validate_date,
                        d.paidup_date,
                        d.item_id,
                        d.prem_freq,
                        g.product_code_sys,
                        g.product_name_sys
          from APP___CLM__DBUSER.T_CLAIM_CASE         a,
               APP___CLM__DBUSER.T_CONTRACT_MASTER    b,
               APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD c,
               APP___CLM__DBUSER.T_CONTRACT_PRODUCT   d,
               APP___CLM__DBUSER.T_BUSINESS_PRODUCT   g,
               APP___CLM__DBUSER.T_CLAIM_PRODUCT         cp
         where a.case_id = b.case_id
           and a.case_id = c.case_id
           and b.policy_id = c.policy_id
           and c.busi_prod_code = g.product_code_sys
           and a.case_id = d.case_id
           and b.policy_id = d.policy_id
           and c.busi_item_id = d.busi_item_id
           and a.case_id = cp.case_id
           and b.policy_id = cp.policy_id
           and d.item_id = cp.item_id
           and b.cur_flag = 1
           and c.cur_flag = 1
           and d.cur_flag = 1
           and cp.is_waived = 1
           and d.prem_freq not in (1)]]>
		<if test="case_id !=null and case_id != ''"> 
					<![CDATA[ and a.case_id = #{case_id} ]]>
		</if>
		<if test="policy_id !=null and policy_id != ''"> 
					<![CDATA[ and b.policy_id = #{policy_id} ]]>
		</if>
		<if test="busi_item_id !=null and busi_item_id != ''"> 
					<![CDATA[ and c.busi_item_id = #{busi_item_id} ]]>
		</if>
		<if test="item_id !=null and item_id != ''"> 
					<![CDATA[ and d.item_id = #{item_id} ]]>
		</if>
	</select>

	<!-- 查询客户关联的任务 前置调查 -->
	<select id="surveyApplyOfCustom" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[ select APP.APPLY_ID, APP.SURVEY_RULE_ID, APP.SURVEY_CODE, APP.BIZ_TYPE, APP.CASE_ID, APP.CASE_NO, APP.POLICY_CODE, APP.APPLY_CODE, APP.CS_APPLY_CODE, APP.CS_ACCEPT_CODE, APP.CS_ITEM, APP.RELATED_ID, APP.SURVEY_DOC_ID, APP.APPLY_SECTION, APP.SURVEY_STATUS, APP.SURVEY_DESC, APP.SURVEY_TYPE, APP.SURVEY_REASON, APP.SURVEY_ORG, APP.APPLY_ORG, APP.APPLY_PER, APP.APPLY_DATE, APP.SURVEY_PER, APP.SURVEY_BY_LEVEL, APP.REPEAL_REASON, APP.REMARK, APP.INTERNAL_RESUALT, APP.SURVEY_MODE, APP.SURVEY_ADVICE, APP.CS_BACKGROUND, APP.INSERT_TIME, APP.INSERT_BY, APP.INSERT_TIMESTAMP, APP.UPDATE_BY, APP.UPDATE_TIMESTAMP, APP.UPDATE_TIME, APP.LOCIAL_FLAG_CODE,
        TASK.LIST_ID, TASK.BATCH_ID, TASK.BIZ_TYPE, TASK.BIZ_NO, TASK.CUSTOMER_NAME, TASK.CUSTOMER_ID, TASK.CHANNEL_TYPE, TASK.CLAIM_PAY, TASK.PAYEE_NUM, TASK.ACCU_AMOUNT, TASK.APPLY_NUM, TASK.INSERT_TIME, TASK.INSERT_BY, TASK.INSERT_TIMESTAMP, TASK.UPDATE_BY, TASK.UPDATE_TIMESTAMP, TASK.UPDATE_TIME, TASK.ORGAN_CODE,
        BAT.BATCH_ID, BAT.BIZ_TYPE, BAT.SURVEY_MODE, BAT.PLAN_ID, BAT.PLAN_NAME, BAT.UPLOAD_DATE, BAT.ORGAN_CODE, BAT.SURVEY_REASON, BAT.EXTRACT_RULE, BAT.INSERT_TIME, BAT.INSERT_BY, BAT.INSERT_TIMESTAMP, BAT.UPDATE_BY, BAT.UPDATE_TIMESTAMP, BAT.UPDATE_TIME
				  from APP___CLM__DBUSER.T_SURVEY_APPLY       app,
				       APP___CLM__DBUSER.T_CLAIM_SURVEY_TASK  task,
				       APP___CLM__DBUSER.t_claim_survey_batch bat
				 where rownum <= 1000
				   and app.SURVEY_RULE_ID = task.list_id
				   and bat.batch_id = task.batch_id 
				   and bat.biz_type = '5' ]]>
		<if test="survey_status != null and survey_status != ''">
				<![CDATA[  and app.survey_status = #{survey_status} ]]>
		</if>
		<if test="customer_id_str != null and customer_id_str != ''">
				<![CDATA[ and task.customer_id in (${customer_id_str})  ]]>
		</if>
		<if test="biz_no != null and biz_no != ''">
				<![CDATA[ and task.biz_no in  (${biz_no})  ]]>
		</if>		  
			<![CDATA[ order by app.apply_date desc ]]>
	</select>

	<!--add by xuyz_wb 查询赔案保项涉及险种保额已赔付金额 -->
	<select id="queryCLaimPayMoney" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ select a.* from (select CAP.CLAIM_PAY_ID,
               CAP.CASE_ID,
               CAP.POLICY_ID,
               CAP.POLICY_CODE,
               CAP.BUSI_ITEM_ID,
               CAP.BUSI_PROD_CODE,
               CAP.ADVANCE_FLAG,
               CAP.BENE_ID,
               CAP.PAYEE_ID,
               CAP.PAY_DENO,
               CAP.PAY_MOLE,
               CAP.BENE_RATE,
               CAP.PAY_AMOUNT,
               CAP.IS_INSTALMENT,
               CAP.REMARK,
               CAP.INSERT_BY,
               CAP.INSERT_TIME,
               CAP.INSERT_TIMESTAMP,
               CAP.UPDATE_BY,
               CAP.UPDATE_TIME,
               CAP.UPDATE_TIMESTAMP,
               CAP.ADJUST_BUSI_ID,
               CAP.PREM_ARAP_ID,
               CAP.UNIT_NUMBER,
               CAP.CASE_NO,
               CAP.OVER_COMP_PAY from APP___CLM__DBUSER.T_CLAIM_PAY tcp where tcp.case_id <> #{case_id}
				and tcp.policy_code= #{policy_code} 
				and tcp.busi_item_id= #{busi_item_id}
				and tcp.prem_arap_id is not null) a,APP___CLM__DBUSER.T_PREM_ARAP tpa where 1=1
				and a.prem_arap_id = tpa.list_id and tpa.fee_status='01' ]]>
	</select>
	<!--add by liulei 查询匹配理算日志所相关责任 -->
	<select id="findAllMatchJournaLiab" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ SELECT distinct A.PRODUCT_ID, A.LIAB_ID, A.ITEM_ID, A.CASE_ID, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___CLM__DBUSER.T_CLAIM_MATCH_JOURNA A WHERE 1 = 1 AND CASE_ID=#{case_id} ]]>
	</select>
	<!-- caoyy 回退历史查询 -->
	<select id="queryHistoryClaimRedoQuery" parameterType="java.util.Map" resultType="java.util.Map">
    <![CDATA[ SELECT cas.case_no,
       				 cas.case_id,
       			 	 cus.customer_name,
       				 cus.customer_certi_code,
       				 app.apply_date,
       				 app.back_status,
       				 cas.case_status,
       				 aud.audit_date,
       				 app.apply_id,
       				 app.cus_apply_date
  			   FROM APP___CLM__DBUSER.T_CLAIM_CASE       cas,
       				APP___CLM__DBUSER.T_CUSTOMER         cus,
       				APP___CLM__DBUSER.T_CLAIM_BACK_APPLY app,
       				APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT aud
 			   WHERE cas.insured_id = cus.customer_id
   				 AND cas.case_id = app.case_id
   				 AND app.apply_id = aud.apply_id(+)
   				 AND app.case_id = aud.case_id(+) ]]>
		<if test="case_no != null and case_no != ''">
				<![CDATA[  and cas.case_no in (${case_no}) ]]>
		</if>
		<if test="customer_name != null and customer_name != ''">
				<![CDATA[  and cus.customer_name = #{customer_name} ]]>
		</if>
		<if test="customer_certi_code != null and customer_certi_code != ''">
				<![CDATA[  and cus.customer_certi_code = #{customer_certi_code} ]]>
		</if>
	</select>
	<select id="queryClaimCaseRedo" parameterType="java.util.Map"
		resultType="java.util.Map">
    	<![CDATA[ SELECT case_no FROM APP___CLM__DBUSER.T_CLAIM_CASE t START WITH t.CASE_NO = #{case_no} CONNECT BY PRIOR t.RELATED_NO = t.CASE_NO ]]>
	</select>
	<select id="claimRedoHistoryDown" parameterType="java.util.Map"
		resultType="java.util.Map">
    	<![CDATA[ SELECT app.apply_id, app.case_id, app.apply_date, app.back_reason, app.back_remark, app.back_status, app.back_desc,
					     app.terminate_flag, app.conclusion_af, app.case_conclusion, app.case_pay, app.organ_code, app.apply_by, app.cus_apply_date,
					     aud.audit_id, aud.back_audit_con, aud.audit_desc, aud.audit_by, aud.audit_date
					FROM APP___CLM__DBUSER.T_CLAIM_BACK_APPLY app 
			   LEFT JOIN APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT aud 
	          		 ON  app.apply_id = aud.apply_id
	        	   WHERE app.apply_id = #{apply_id}]]>
	</select>
	<!-- end -->

	<!-- 质检结果 -->
	<!-- gaojun_wb 质检结果查询接口（查询表示为问题件（柜员问题记录/影像质检不通过）的质检） -->
	<select id="queryQuestionResultPage" parameterType="java.util.Map"
		resultType="java.util.Map">
   <![CDATA[
   		select B.* from(
           select A.*,rownum RN from( 
              select 
                      t.case_id,
                      t.case_no,
                      t.check_by,
                      t.check_reason,
                      t.check_date,
                      t.remark,
                      c.organ_code,
                      c.sign_time,
                      m.memo_type,
                      m.memo_option,
                      m.memo_content
  			from  APP___CLM__DBUSER.T_CLAIM_AFC_TASK t ,
  				  APP___CLM__DBUSER.T_CLAIM_CASE c 
  		    left join APP___CLM__DBUSER.T_CLAIM_MEMO m on c.case_id = m.case_id
		    where c.case_id = t.case_id
            and  m.memo_type in('5','6')
            and t.qc_status = '2'
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code like '${organ_code }%']]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
		<![CDATA[ ) A where rownum <= #{LESS_NUM} 
     		) B where RN > #{GREATER_NUM}
          ]]>
	</select>

	<!-- gaojun_wb 质检结果查询接口（表示质检(质检岗位是签收人)的条数） -->
	<select id="queryQuestionResultTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
   <![CDATA[
            select 
                   count(*)
  			from  APP___CLM__DBUSER.T_CLAIM_AFC_TASK t ,
  				  APP___CLM__DBUSER.T_CLAIM_CASE c 
  		    left join APP___CLM__DBUSER.T_CLAIM_MEMO m 
  			on c.case_id = m.case_id
		    where c.case_id = t.case_id
           and  m.memo_type in('5','6')
           and t.qc_status = '2'
		 ]]>
		<if test=" QCType ==1 and organ_code != null and organ_code != '' ">
		    <![CDATA[AND c.organ_code like '${organ_code }%']]>
		</if>
		<if test=" QCType ==2 and staffNo!=null">
		    <![CDATA[AND c.signer_id = #{staffNo }]]>
		</if>
		<if test=" QCType ==2 and staffNo==null">
		    <![CDATA[AND c.organ_code = #{organ_code }]]>
		</if>
		<if test=" stratTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==1">
		    <![CDATA[AND c.sign_time <= #{endTime }]]>
		</if>
		<if test=" check_by != null ">
		    <![CDATA[AND c.signer_id >= #{check_by }]]>
		</if>
		<if test=" stratTime != null and timeType ==2">
		    <![CDATA[AND t.check_date >= #{stratTime }]]>
		</if>
		<if test=" endTime != null and timeType ==2">
		    <![CDATA[AND t.check_date <= #{endTime }]]>
		</if>
	</select>

	<select id="queryClaimHisInfoDetail" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		   SELECT CC.CASE_NO
		    FROM APP___CLM__DBUSER.T_CLAIM_CASE     CC,
		         APP___CLM__DBUSER.T_CLAIM_SUB_CASE CSC
		   WHERE CC.CASE_STATUS = '80'
		     AND CC.INSURED_ID = #{insured_id}
		     AND CC.CASE_ID = CSC.CASE_ID
		     AND CSC.CLAIM_TYPE='03'	
		]]>
	</select>
	
	<!--lipeng1_wb  查询新型健康险既往理赔史 -->
	<select id="queryNewHealthClaimHisInfoDetail" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		   SELECT CC.CASE_NO
  FROM APP___CLM__DBUSER.T_CLAIM_CASE         CC,
       APP___PAS__DBUSER.T_INSURED_LIST       il,
       APP___PAS__DBUSER.t_Contract_Busi_Prod cbp
 WHERE CC.CASE_STATUS = '80'
   and il.customer_id = cc.insured_id
   and il.policy_id = cbp.policy_id
   AND CC.INSURED_ID = #{insured_id}
   AND cbp.busi_prod_code in ('00762000',
                              '00765000',
                              '00782000',
                              '00509000',
                              '00512000',
                              '00513000',
                              '00547000')  
		]]>
	</select>

	<!--lizd_wb 查询是否为迁移数据 -->
	<select id="queryClaimLog" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    		select distinct c.is_migration    
		       from APP___CLM__DBUSER.T_CLAIM_CASE c ,
		            APP___CLM__DBUSER.T_CLAIM_POLICY p,
		            APP___CLM__DBUSER.T_CLAIM_BUSI_PROD b
		      where c.case_id = p.case_id
		        and p.policy_code = b.policy_code
		        and p.policy_code = #{policy_code_str}]]>
		        <if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      				<![CDATA[   and b.busi_item_id = #{busi_item_id_str} ]]>
				</if>  
	</select>
	<!--lizd_wb 迁移数据查询既往赔案信息 -->
	<select id="queryClaimHisInfoTotalLog" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   select count(1) from(
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.insured_id,
		            c.case_status,
		            c.accept_time,
		            c.sign_time,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            c.end_case_time,
		            l.liab_id,
		            p.product_abbr_name,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            l.actual_pay,       
		            m.policy_code policy_code, 
		            a.acc_date,
		            l.busi_prod_code,
		            (select MAX(q.audit_date) from APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT q where q.case_id = c.case_id)as back_audit_date,
		            l.liab_conclusion 
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CLAIM_LIAB L,
		            APP___CLM__DBUSER.T_CLAIM_POLICY    m,
		            APP___CLM__DBUSER.T_BUSINESS_PRODUCT   p,
		            APP___CLM__DBUSER.T_CLAIM_BUSI_PROD d
		      where c.case_id = m.case_id
		        and c.case_id = l.case_id
		      	and c.case_id = d.case_id
		      	and l.BUSI_ITEM_ID = d.BUSI_ITEM_ID
		      	and l.BUSI_PROD_CODE = d.BUSI_PROD_CODE
		      	and m.policy_code = l.policy_code
		        and m.policy_code = d.policy_code 
		        and p.product_code_sys = d.busi_prod_code ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status = #{case_status}]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[ AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code = #{policy_code_str} ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND  d.busi_item_id = #{busi_item_id_str}]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.insured_id,
                c.case_status,
                c.accept_time,
		        c.sign_time,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,
                c.approver_id,
                c.end_case_time,
                l.actual_pay,
                m.policy_code,
		        a.acc_date,
		        l.liab_id,
		        l.busi_prod_code,
		        p.product_abbr_name,
		        l.liab_conclusion
      ) ]]>
	</select>
	<!--lizd_wb 迁移数据查既往赔案信息分页 -->
	<select id="queryClaimHisInfoLog" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	select B.* from(
           select A.*,rownum RN from(   
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.insured_id,
		            c.case_status,
		            c.accept_time,
		            c.sign_time,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            c.end_case_time,
		            l.liab_id,
		            p.product_abbr_name,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            l.actual_pay,       
		            m.policy_code policy_code,   
		            a.acc_date,
					l.busi_prod_code,
		            (select MAX(q.audit_date) from APP___CLM__DBUSER.T_CLAIM_BACK_AUDIT q where q.case_id = c.case_id)as back_audit_date,
		             (l.actual_pay+nvl(l.ADVANCE_PAY,0)) as actualPayTatol,
		             l.LIAB_CONCLUSION
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CLAIM_LIAB l,
		            APP___CLM__DBUSER.T_CLAIM_POLICY    m,
		            APP___CLM__DBUSER.T_BUSINESS_PRODUCT   p,
		            APP___CLM__DBUSER.T_CLAIM_BUSI_PROD d
		      where c.case_id = m.case_id
		        and c.case_id = l.case_id
		      	and c.case_id = d.case_id
		      	and l.BUSI_ITEM_ID = d.BUSI_ITEM_ID
		      	and l.BUSI_PROD_CODE = d.BUSI_PROD_CODE
		      	and m.policy_code = l.policy_code
		        and m.policy_code = d.policy_code 
		        and p.product_code_sys = d.busi_prod_code ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status = #{case_status}]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[  AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>

		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code = #{policy_code_str} ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND  d.busi_item_id = #{busi_item_id_str}]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.insured_id,
                c.case_status,
                c.accept_time,
		        c.sign_time,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,
                c.approver_id,
                c.end_case_time,
                l.actual_pay,
                m.policy_code,
		        a.acc_date,
		        l.liab_id,
		        l.busi_prod_code,
		        p.product_abbr_name,
		        l.advance_pay,
		        l.LIAB_CONCLUSION
		        order by a.acc_date desc
		 ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM} and rn<200]]>
	</select>


	<select id="problemCheckList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			    select  to_char(wm_concat(sa.survey_status)) survey_status,
                        to_char(wm_concat(u.uw_status)) uw_status,
                         to_char(wm_concat(sd.suggestion_code)) suggestion_code,
                         to_char(wm_concat(c.case_no)) case_no1,
                         to_char(wm_concat(cc.sup_falg)) sup_falg,
                         to_char(wm_concat(c.advance_flag)) advance_flag1
          from APP___CLM__DBUSER.t_claim_case c
          left join APP___CLM__DBUSER.t_survey_apply sa
            on c.case_id = sa.case_id
          left join APP___CLM__DBUSER.t_claim_uw u
            on c.case_id = u.case_id
          left join APP___CLM__DBUSER.t_ri_suggest_detail sd
            on c.case_id = sd.case_id
          left join APP___CLM__DBUSER.t_claim_checklist cc
            on c.case_id = cc.case_id and cc.is_checklist_memo is not null
          where c.case_no = #{case_no}   
         group by c.case_id
		]]>
	</select>
	
	<select id="problemCheckNewList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			    SELECT COUNT(SA.APPLY_ID) APPLY_ID,
			       COUNT(U.CLM_UW_ID) CLM_UW_ID,
			       COUNT(SD.SUGGES_ID) SUGGES_ID,
			       COUNT(CC.CHECKLIST_ID) CHECKLIST_ID,
			       COUNT(DS.DISCUSS_ID) DISCUSS_ID,
			       COUNT(CM.MEMO_ID) MEMO_ID
			  FROM APP___CLM__DBUSER.T_CLAIM_CASE C
			  LEFT JOIN APP___CLM__DBUSER.T_SURVEY_APPLY SA
			    ON C.CASE_ID = SA.CASE_ID
			   AND SA.SURVEY_STATUS IN (1, 4)
			  LEFT JOIN APP___CLM__DBUSER.T_CLAIM_UW U
			    ON C.CASE_ID = U.CASE_ID
			   AND U.UW_STATUS = '0'
			  LEFT JOIN APP___CLM__DBUSER.T_RI_SUGGEST_DETAIL SD
			    ON C.CASE_ID = SD.CASE_ID
			   AND SD.SUGGESTION_CODE IS NULL
			  LEFT JOIN APP___CLM__DBUSER.T_CLAIM_CHECKLIST CC
			    ON C.CASE_ID = CC.CASE_ID
			   AND CC.IS_CHECKLIST_MEMO IS NOT NULL
			   AND CC.SUP_FALG = 0
			  LEFT JOIN APP___CLM__DBUSER.T_CLAIM_DISCUSS DS
			  ON C.CASE_ID = DS.CASE_ID
			  AND DS.DISCUSS_STATUS IN (0,1)
			  LEFT JOIN APP___CLM__DBUSER.T_CLAIM_MEMO CM
			    ON C.CASE_ID = CM.CASE_ID
			   AND CM.MEMO_TYPE = '14'
			   AND CM.IT_PROBLEM_BACK_FLAG = 0
			 WHERE C.CASE_NO = #{case_no}   
			 GROUP BY C.CASE_ID
		]]>
	</select>


	<select id="queryRegisterTaskMessages" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			  SELECT A.CASE_NO,
       (SELECT B.CUSTOMER_NAME
          FROM APP___CLM__DBUSER.T_CUSTOMER B
         WHERE B.CUSTOMER_ID = A.INSURED_ID) CUSTOMER_NAME,
       (SELECT B.CUSTOMER_CERTI_CODE
          FROM APP___CLM__DBUSER.T_CUSTOMER B
         WHERE B.CUSTOMER_ID = A.INSURED_ID) CUSTOMER_CERTI_CODE,
       A.GREEN_FLAG,
       A.CASE_ID,
       A.ORGAN_CODE,
       A.ACCEPTOR_ID, 
       A.SIGNER_ID,
        A.SIGN_TIME,
        A.CASE_STATUS,
       (SELECT  CASE WHEN (   NVL(  SYSDATE-A.SIGN_TIME,0)-C.CAUTION_TIME)>0 THEN '1'
       ELSE '0' END
          FROM APP___CLM__DBUSER.T_COMMON_WORK_PARAM C
         WHERE C.TASK_TYPE=#{task_type}
         AND ROWNUM<2) CAUTION_FLAG
  FROM APP___CLM__DBUSER.T_CLAIM_CASE A
 WHERE A.CASE_NO IN]]>
		<foreach collection="case_no_list" item="list" index="index"
			open="(" close=")" separator=",">#{list}</foreach>

		ORDER BY A.CASE_ID DESC

	</select>





	<select id="queryStopReasonReturnMessage" parameterType="java.util.Map" resultType="java.util.Map">
			<![CDATA[
			SELECT A.CASE_ID,
	       A.INSURED_ID,
	       A.ACCIDENT_ID,
	       A.APPLY_DATE,
	       (select B.ACC_DATE
	          from APP___CLM__DBUSER.T_CLAIM_ACCIDENT B
	         WHERE B.ACCIDENT_ID = A.ACCIDENT_ID) ACC_DATE,
	       (select B.ACC_REASON
	          from APP___CLM__DBUSER.T_CLAIM_ACCIDENT B
	         WHERE B.ACCIDENT_ID = A.ACCIDENT_ID) ACC_REASON,
	       C.CLAIM_TYPE,
	       C.CLAIM_DATE,
	       D.END_CAUSE,
	       D.POLICY_CODE,
	       D.POLICY_ID,
	       D.BUSI_PROD_CODE,
	       D.EXPIRY_DATE,
	       D.BUSI_ITEM_ID,
	       E.PRODUCT_CODE_STD,
	       D.LIABILITY_STATE,
	       E.COVER_PERIOD_TYPE,
	       CASE
	         WHEN C.CLAIM_TYPE = '08' AND C.CLAIM_DATE < D.EXPIRY_DATE AND
	              D.MASTER_BUSI_ITEM_ID IS NOT NULL AND
	              E.PRODUCT_CATEGORY3='40007' THEN
	          '1'
	         ELSE
	          '0'
	       END CLAIM_TYPE_FLAG,
	       E.PRODUCT_ABBR_NAME
	
	  FROM APP___CLM__DBUSER.T_CLAIM_CASE         A,
	       APP___CLM__DBUSER.T_CLAIM_SUB_CASE     C,
	       APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD D,
	       APP___CLM__DBUSER.T_BUSINESS_PRODUCT   E
	 WHERE a.case_id = c.case_id
	   AND A.CASE_ID = D.CASE_ID
	   AND D.CUR_FLAG = 1
	   AND D.BUSI_PROD_CODE = E.PRODUCT_CODE_SYS(+)
	   AND A.CASE_ID = #{case_id}
	  ]]>
	</select>

	<!-- 根据caseid删除历史理算数数据 -->
	<delete id="deleteClaimMatchInfoByCaseId" parameterType="java.util.Map">
	 <![CDATA[
	 DECLARE
	   IN_CASE_ID NUMBER;
	    CURSOR C_CLAIM_LIAB_IDS IS
	    SELECT A.CLAIM_LIAB_ID
	      FROM APP___CLM__DBUSER.T_CLAIM_LIAB A
	     WHERE A.CASE_ID = #{case_id};
	     C_CLAIM_LIAB_ID C_CLAIM_LIAB_IDS%ROWTYPE;
	 BEGIN
	      IN_CASE_ID:=#{case_id};
	 
		  DELETE FROM APP___CLM__DBUSER.T_CLAIM_LIAB_BILL_RELATION WHERE CASE_ID = IN_CASE_ID ; 
		  
		  DELETE FROM APP___CLM__DBUSER.T_CLAIM_SUB_LIAB WHERE CASE_ID = IN_CASE_ID ;
		  
		  DELETE FROM APP___CLM__DBUSER.T_CLAIM_LIAB_FORMULA WHERE CASE_ID = IN_CASE_ID ;
		  
		 FOR C_CLAIM_LIAB_ID IN C_CLAIM_LIAB_IDS LOOP
  			  DELETE FROM APP___CLM__DBUSER.T_CLAIM_ANNUITY WHERE CLAIM_LIAB_ID = C_CLAIM_LIAB_ID.CLAIM_LIAB_ID;
 		 END LOOP;
 		 
 		 DELETE FROM APP___CLM__DBUSER.T_CLAIM_LIAB WHERE CASE_ID = IN_CASE_ID;
 		 
 		 DELETE FROM APP___CLM__DBUSER.T_CLAIM_PRODUCT WHERE CASE_ID = IN_CASE_ID;
 		 
 		 DELETE FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD WHERE CASE_ID =IN_CASE_ID;
 		 
 		 DELETE FROM APP___CLM__DBUSER.T_CLAIM_POLICY WHERE CASE_ID =IN_CASE_ID ;
 		 
 		 DELETE FROM APP___CLM__DBUSER.T_CLAIM_MATCH_JOURNA WHERE CASE_ID = IN_CASE_ID;
 		 
 		 UPDATE APP___CLM__DBUSER.T_CLAIM_CASE   SET CALC_PAY = NULL , ACTUAL_PAY = NULL
		  WHERE CASE_ID = IN_CASE_ID  ;
     END;	
     ]]>
	</delete>

	<!--xinghj 迁移数据查询既往赔案信息 用于理赔页面查询既往赔案 -->
	<select id="queryClaimHisInfoTotalLog1" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   select count(1) from(
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.case_status,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            c.actual_pay,       
		            a.acc_date,
		            c.is_common     
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CLAIM_POLICY    m,
		            APP___CLM__DBUSER.T_CLAIM_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		        and m.policy_code = d.policy_code ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[ AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND  d.busi_item_id in (${busi_item_id_str})]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
                m.policy_code,
		        a.acc_date,
		        c.is_common
      ) ]]>
	</select>
	<!--xinghj_wb 迁移数据查既往赔案信息分页 用于理赔页面查询既往赔案 -->
	<select id="queryClaimHisInfoLog1" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	select B.* from(
           select A.*,rownum RN from(   
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.case_status,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            c.actual_pay,       
		            a.acc_date ,
		            c.is_common    
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CLAIM_POLICY    m,
		            APP___CLM__DBUSER.T_CLAIM_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		      	and c.case_status != 99
		        and m.policy_code = d.policy_code  ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[  AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>

		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND  d.busi_item_id in (${busi_item_id_str})]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
		        a.acc_date,
		        c.is_common
		        order by a.acc_date desc
		 ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM} and rn<200]]>
	</select>

	<!-- xinghj_wb 通过出险人查询既往赔案信息个数 用于理赔页面查询既往赔案 -->
	<select id="queryClaimHisInfoTotal1" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   select count(1) from(
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.case_status,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            c.actual_pay,       
		             m.policy_code policy_code, 
		             m.policy_type,
		            a.acc_date,
		            c.is_common     
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CONTRACT_MASTER    m,
		            APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		        and m.policy_code = d.policy_code
		        and m.cur_flag = d.cur_flag
		        and m.cur_flag='1' ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[ AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=${busi_item_id_str} )]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
                m.policy_code,
		        m.policy_type,
		        a.acc_date,
		        c.is_common
      ) ]]>
	</select>

	<!-- xinghj_wb 通过出险人查询既往赔案信息 用于理赔页面查询既往赔案 -->
	<select id="queryClaimHisInfo1" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	select B.* from(
           select A.*,rownum RN from(   
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.case_status,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  APP___CLM__DBUSER.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            c.actual_pay,       
		             m.policy_type,
		            a.acc_date,
		            c.is_common     
		       from APP___CLM__DBUSER.T_CLAIM_CASE c left join  APP___CLM__DBUSER.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            APP___CLM__DBUSER.T_CONTRACT_MASTER    m,
		            APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		        and m.policy_code = d.policy_code
		        and m.cur_flag = d.cur_flag
		        and m.cur_flag='1' 
		        and c.case_status!='99']]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" case_id  != null ">
      		<![CDATA[and c.case_id != #{case_id}]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[  AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>

		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>

		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=${busi_item_id_str} )]]>
		</if>  
		 
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
                m.policy_code,
		        m.policy_type,
		        a.acc_date,
		        c.is_common
		        order by a.acc_date desc
		 ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM} and rn<200]]>
	</select>
	<!-- caoyy_wb 查询签收机构下的银行编码和去除信用的银行编码 -->
	<select id="CLM_queryTankCode" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[ SELECT ROWNUM RN,A.BANK_CODE,A.BANK_NAME FROM (
    	SELECT DISTINCT BANK.BANK_CODE,BANK.BANK_NAME FROM APP___CLM__DBUSER.T_BANK BANK, APP___CLM__DBUSER.T_BANK_ORGEN_REL REL
    	WHERE REL.BANK_CODE = BANK.BANK_CODE
    		  AND BANK.BANK_NAME NOT LIKE ('%信用卡%')
       		  AND BANK.IS_CREDIT_CARD = '0'
    	]]>
		 <if test=" organ_code  != null  and  organ_code  != '' ">
      		<![CDATA[ AND REL.ORGAN_CODE like '${organ_code}%' ]]>
		</if>
		<![CDATA[) A]]>
	</select>
	<select id="findAdjustDesc" parameterType="java.util.Map" resultType="java.util.Map">
	    <![CDATA[ SELECT CLM_FEE_DESC FROM APP___CLM__DBUSER.T_CLAIM_FEE_MAPPING WHERE CLM_FEE_CODE=#{clm_fee_code}]]>
	</select>

	<!-- add by lizd_wb报案查询接口使用（柜面）分页总数 -->
	<select id="queryClaimReportInfoTotal" parameterType="java.util.Map" resultType="java.lang.Integer"> 
    <![CDATA[select count(1) from (
    					select distinct c.case_no,
    											c.case_id,
                        						c.accept_time,
                        						c.rptr_name,
                        						(select ct.customer_name from APP___CLM__DBUSER.T_CUSTOMER ct where ct.customer_id = c.insured_id) insured_name
          					from APP___CLM__DBUSER.T_CLAIM_CASE c
         					where (c.case_status = '10' or c.case_status = '20')]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
          <![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
    <![CDATA[
    	order by accept_time desc)]]>
	</select>
	<!-- add by lizd_wb报案查询接口使用（柜面）分页 -->
	<select id="queryClaimReportInfo" parameterType="java.util.Map" resultType="java.util.Map"> 
    <![CDATA[select B.* from(
              select A.*,rownum RN from(
                   select distinct c.case_no,
                   					c.case_id,
                                    c.insert_time accept_time,
                                    c.rptr_name,
                                    (select ct.customer_name from APP___CLM__DBUSER.T_CUSTOMER ct where ct.customer_id = c.insured_id) insured_name     
           from APP___CLM__DBUSER.T_CLAIM_CASE c
          where (c.case_status = '10' or c.case_status = '20')]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
          <![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
    <![CDATA[
            order by c.accept_time desc) A where rownum <= #{LESS_NUM} ) B where RN > #{GREATER_NUM} and rn<200]]>
	</select>

	<!-- add by lizd_wb通过赔案查询保单号（核保接口 -->
	<select id="queryClaimPolicyByCaseNo" parameterType="java.util.Map" resultType="java.util.Map"> 
    <![CDATA[select distinct m.policy_code policy_code1,p.policy_code policy_code2
       from APP___CLM__DBUSER.t_claim_case c,
                 APP___CLM__DBUSER.t_contract_master m,
                 APP___CLM__DBUSER.t_claim_policy p
       where c.case_id=m.case_id(+)
       and c.case_id=p.case_id(+)]]>
		<if test=" case_no  != null  and  case_no  != '' ">
          <![CDATA[ and c.case_no= #{case_no} ]]>
		</if>
		<if test=" count_star_time  != null  and  count_star_time  != '' ">
          <![CDATA[ and sc.claim_date>= #{count_star_time} ]]>
		</if>
		<if test=" count_end_time  != null  and  count_end_time  != '' ">
          <![CDATA[ and sc.claim_date<= #{count_end_time} ]]>
    </if>
  </select>
  
  <!-- add by lizd_wb通过业务员查询赔案信息（核保接口  根据业务确认，用出险日期卡传过来的时间段  生效日期在报案确认时间一年内的-->  
  <select id="queryClaimByAgent" parameterType="java.util.Map" resultType="java.util.Map">
    <![CDATA[SELECT DISTINCT CC.CASE_ID,CA.ACC_REASON, CC.CASE_NO
          FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD  CBP,
               APP___CLM__DBUSER.T_CLAIM_LIAB       CL,
               APP___CLM__DBUSER.T_CLAIM_CASE       CC,
               APP___CLM__DBUSER.T_CLAIM_SUB_CASE   CSC,
               APP___CLM__DBUSER.T_CLAIM_ACCIDENT   CA,
               APP___CLM__DBUSER.T_BUSINESS_PRODUCT BP
         WHERE CC.ACCIDENT_ID = CA.ACCIDENT_ID
           AND CC.CASE_ID = CSC.CASE_ID
           AND CC.CASE_ID = CL.CASE_ID
           AND CL.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
           AND CC.case_status != '99'
           AND CL.CASE_ID = CBP.CASE_ID
           AND CL.POLICY_CODE = CBP.POLICY_CODE
           AND CL.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
       AND ((BP.COVER_PERIOD_TYPE = '0' AND BP.PRODUCT_CATEGORY4 = '50001') OR
       (BP.COVER_PERIOD_TYPE = '0' AND BP.PRODUCT_CATEGORY2 = '30001'))
       AND CSC.CLAIM_DATE BETWEEN CBP.VALID_DATE AND ADD_MONTHS(CBP.VALID_DATE,12)]]>
    <if test=" count_star_time  != null  and  count_star_time  != '' ">
          <![CDATA[ AND TRUNC(CSC.CLAIM_DATE) BETWEEN #{count_star_time}]]>
    </if>
    <if test=" count_end_time  != null  and  count_end_time  != '' ">
          <![CDATA[ AND #{count_end_time}]]>
    </if>
    <if test=" policy_code_str  != null  and  policy_code_str  != '' ">
          <![CDATA[ ${policy_code_str}]]>
    </if>
    <if test=" case_no_str  != null  and  case_no_str  != '' ">
          <![CDATA[ AND CC.CASE_NO NOT IN(${case_no_str})]]>  
    </if>
  </select>
  <select id="queryAuditTaskMessages" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
					SELECT A.CASE_ID,
		       A.CASE_NO,
		       A.GREEN_FLAG,
		       A.ORGAN_CODE, 
		       A.REGISTE_CONF_TIME,
       		   A.SIGN_TIME,
       		   A.AUDIT_PERMISSION_NAME,
       		   A.CASE_FLAG,
       		   A.PRE_AUDIT_ID,
		       B.CUSTOMER_ID,
		       B.CUSTOMER_NAME,
		       B.CUSTOMER_CERTI_CODE,
		       (SELECT max(C2.SURVEY_STATUS)
		          FROM APP___CLM__DBUSER.T_SURVEY_APPLY C2
		         WHERE C2.CASE_ID = CC.CASE_ID) SURVEY_STATUS,
		       (SELECT max(D2.UW_STATUS)
		          FROM APP___CLM__DBUSER.T_CLAIM_UW D2
		         WHERE D2.CLM_UW_ID = DD.CLM_UW_ID) uw_status,
		       FF.SUP_FALG SUP_STATUS,
		            (SELECT max(G2.TREATY_STATUS)
		          FROM APP___CLM__DBUSER.T_CLAIM_TREATY_TALK G2
		         WHERE G2.TREATY_ID=GG.TREATY_ID) treaty_talk_status ,
		         
		           (SELECT max((SELECT NAME FROM APP___CLM__DBUSER.T_DISCUSS_STATUS HH2 WHERE HH2.CODE=H2.DISCUSS_STATUS  ))
		          FROM APP___CLM__DBUSER.T_CLAIM_DISCUSS H2
		         WHERE H2.DISCUSS_ID=HH.DISCUSS_ID) discuss_status , 
		          (SELECT  I3.QC_STATUS
		          FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TASK I3
		         WHERE I3.TASK_ID=II.TASK_ID)  QC_STATUS  ,
		          A.advance_flag,
                  A.channel_code,
       (SELECT to_char(replace(wm_concat(distinct c1.bank_name),',',';'))
          FROM APP___CLM__DBUSER.t_claim_pay   a1,
               APP___CLM__DBUSER.t_claim_payee a2,
               APP___CLM__DBUSER.t_Bank        c1
         where a1.case_id = a2.case_id
           and a1.payee_id = a2.payee_id
           and c1.bank_code = a2.bank_code
              
           and a1.case_id = a.case_id) bank_NAME,
       (SELECT to_char(replace(wm_concat(distinct c1.code),',',';'))
          FROM APP___CLM__DBUSER.t_claim_pay   a1,
               APP___CLM__DBUSER.t_claim_payee a2,
               APP___CLM__DBUSER.t_Pay_Mode    c1
         where a1.case_id = a2.case_id
           and a1.payee_id = a2.payee_id
           and c1.code = a2.pay_mode
           and a1.case_id = a.case_id) PAY_MODE_NAME		       
		  FROM APP___CLM__DBUSER.T_CLAIM_CASE A,
		       APP___CLM__DBUSER.T_CUSTOMER B,
		       /*调查申请信息取最新的*/
		       (SELECT MAX(C.SURVEY_STATUS) SURVEY_STATUS, C.CASE_ID
		          FROM APP___CLM__DBUSER.T_SURVEY_APPLY C
		         WHERE C.SURVEY_STATUS != '3' GROUP BY C.CASE_ID) CC,
		          /*二核取最新的*/
		       (SELECT MAX(D.CLM_UW_ID) CLM_UW_ID, D.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_UW D
		         GROUP BY D.CASE_ID) DD,
			       /*查询问题件如果存在 SUP_FALG为0 则待补扫（0），如果都为1则已完成（1）*/
	           (SELECT CASE 
	           WHEN SUM(NVL(F.SUP_FALG, 0)) / COUNT(1) = 1   THEN 1 ELSE 0 END SUP_FALG, F.CASE_ID
	      FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST F
	      where f.is_checklist_memo is not null
	     GROUP BY F.CASE_ID) FF,
		  /*协谈取最新的*/
		   (SELECT MAX(G.TREATY_ID) TREATY_ID, G.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_TREATY_TALK G
		         GROUP BY G.CASE_ID) GG ,
		         /*合议取最新的*/
		           (SELECT MAX(H.DISCUSS_ID) DISCUSS_ID, H.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_DISCUSS H
		         GROUP BY H.CASE_ID) HH ,
		         /*事中质检取最新的*/
		          (SELECT MAX(I2.TASK_ID) TASK_ID, I2.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TASK I2
		         GROUP BY I2.CASE_ID) II 
		         
		 WHERE A.INSURED_ID = B.CUSTOMER_ID(+)
		   AND A.CASE_ID = CC.CASE_ID(+)
		   AND A.CASE_ID = DD.CASE_ID(+)
		   AND A.CASE_ID = FF.CASE_ID(+)
		   AND A.CASE_ID = GG.CASE_ID(+)
		   AND A.CASE_ID = HH.CASE_ID(+)
		    AND A.CASE_ID = II.CASE_ID(+)
		   AND A.CASE_NO IN]]> <foreach collection="case_no_list" item="list" index="index"
			open="(" close=")" separator=",">#{list}</foreach> 
		ORDER BY A.CASE_ID DESC 

	</select>
    <select id="queryAuditTaskMessageses" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
					SELECT A.CASE_ID,
		       A.CASE_NO,
		       A.GREEN_FLAG,
		       A.ORGAN_CODE, 
		       A.REGISTE_CONF_TIME,
       		   A.SIGN_TIME,
       		   A.AUDIT_PERMISSION_NAME,
       		   A.CASE_FLAG,
       		   A.PRE_AUDIT_ID,
		       B.CUSTOMER_ID,
		       B.CUSTOMER_NAME,
		       B.CUSTOMER_CERTI_CODE,
		       (SELECT max(C2.SURVEY_STATUS)
		          FROM APP___CLM__DBUSER.T_SURVEY_APPLY C2
		         WHERE C2.CASE_ID = CC.CASE_ID) SURVEY_STATUS,
		       (SELECT max(D2.UW_STATUS)
		          FROM APP___CLM__DBUSER.T_CLAIM_UW D2
		         WHERE D2.CLM_UW_ID = DD.CLM_UW_ID) uw_status,
		       FF.SUP_FALG SUP_STATUS,
		            (SELECT max(G2.TREATY_STATUS)
		          FROM APP___CLM__DBUSER.T_CLAIM_TREATY_TALK G2
		         WHERE G2.TREATY_ID=GG.TREATY_ID) treaty_talk_status ,
		         
		           (SELECT max((SELECT NAME FROM APP___CLM__DBUSER.T_DISCUSS_STATUS HH2 WHERE HH2.CODE=H2.DISCUSS_STATUS  ))
		          FROM APP___CLM__DBUSER.T_CLAIM_DISCUSS H2
		         WHERE H2.DISCUSS_ID=HH.DISCUSS_ID) discuss_status , 
		          (SELECT  I3.QC_STATUS
		          FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TASK I3
		         WHERE I3.TASK_ID=II.TASK_ID)  QC_STATUS  ,
		          A.advance_flag,
                  A.channel_code,
       (SELECT to_char(replace(wm_concat(distinct c1.bank_name),',',';'))
          FROM APP___CLM__DBUSER.t_claim_pay   a1,
               APP___CLM__DBUSER.t_claim_payee a2,
               APP___CLM__DBUSER.t_Bank        c1
         where a1.case_id = a2.case_id
           and a1.payee_id = a2.payee_id
           and c1.bank_code = a2.bank_code
              
           and a1.case_id = a.case_id) bank_NAME,
       (SELECT to_char(replace(wm_concat(distinct c1.code),',',';'))
          FROM APP___CLM__DBUSER.t_claim_pay   a1,
               APP___CLM__DBUSER.t_claim_payee a2,
               APP___CLM__DBUSER.t_Pay_Mode    c1
         where a1.case_id = a2.case_id
           and a1.payee_id = a2.payee_id
           and c1.code = a2.pay_mode
           and a1.case_id = a.case_id) PAY_MODE_NAME		       
		  FROM APP___CLM__DBUSER.T_CLAIM_CASE A,
		       APP___CLM__DBUSER.T_CUSTOMER B,
		       /*调查申请信息取最新的*/
		       (SELECT MAX(C.SURVEY_STATUS) SURVEY_STATUS, C.CASE_ID
		          FROM APP___CLM__DBUSER.T_SURVEY_APPLY C
		         WHERE C.SURVEY_STATUS != '3' GROUP BY C.CASE_ID) CC,
		          /*二核取最新的*/
		       (SELECT MAX(D.CLM_UW_ID) CLM_UW_ID, D.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_UW D
		         GROUP BY D.CASE_ID) DD,
			       /*查询问题件如果存在 SUP_FALG为0 则待补扫（0），如果都为1则已完成（1）*/
	           (SELECT CASE 
	           WHEN SUM(NVL(F.SUP_FALG, 0)) / COUNT(1) = 1   THEN 1 ELSE 0 END SUP_FALG, F.CASE_ID
	      FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST F
	      where f.is_checklist_memo is not null
	     GROUP BY F.CASE_ID) FF,
		  /*协谈取最新的*/
		   (SELECT MAX(G.TREATY_ID) TREATY_ID, G.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_TREATY_TALK G
		         GROUP BY G.CASE_ID) GG ,
		         /*合议取最新的*/
		           (SELECT MAX(H.DISCUSS_ID) DISCUSS_ID, H.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_DISCUSS H
		         GROUP BY H.CASE_ID) HH ,
		         /*事中质检取最新的*/
		          (SELECT MAX(I2.TASK_ID) TASK_ID, I2.CASE_ID
		          FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TASK I2
		         GROUP BY I2.CASE_ID) II 
		         
		 WHERE A.INSURED_ID = B.CUSTOMER_ID(+)
		   AND A.CASE_ID = CC.CASE_ID(+)
		   AND A.CASE_ID = DD.CASE_ID(+)
		   AND A.CASE_ID = FF.CASE_ID(+)
		   AND A.CASE_ID = GG.CASE_ID(+)
		   AND A.CASE_ID = HH.CASE_ID(+)
		    AND A.CASE_ID = II.CASE_ID(+)
		   AND A.CASE_NO IN]]> <foreach collection="case_no_list" item="list" index="index"
			open="(" close=")" separator=",">#{list}</foreach>
        AND A.PRE_AUDIT_ID is not null 
		ORDER BY A.PRE_AUDIT_ID, A.UPDATE_TIME

	</select>
	<!-- 核赔作业实时监测 add by zhaoyq start -->
	<!-- 屏1 start -->
	<select id="queryTimeMonitoringOneZhu" parameterType="java.util.Map" resultType="java.util.Map">
	   <![CDATA[
	   select organ_code,case_Count,end_Case_Count,middle_Case_Count from (
       select o.organ_code,
       (select count(1)
          from dev_clm.t_claim_case c
         where c.organ_code like o.organ_code || '%'
           and c.case_flag = 1
           and c.registe_conf_time> to_date(#{registe_start_time},'yyyy-MM-dd HH24:MI:SS')
           and c.registe_conf_time <= to_date(#{registe_end_time},'yyyy-MM-dd HH24:MI:SS')
           ) case_Count ,
       (select count(1)
          from dev_clm.t_claim_case c
         where c.organ_code like o.organ_code || '%'
           and c.case_flag = 1
           and c.registe_conf_time > to_date(#{registe_start_time},'yyyy-MM-dd HH24:MI:SS')
           and c.registe_conf_time <= to_date(#{registe_end_time},'yyyy-MM-dd HH24:MI:SS')
           and c.case_status = '80' 
           and c.end_case_time >= to_date(#{end_start_time},'yyyy-MM-dd HH24:MI:SS')
           and c.end_case_time <= to_date(#{end_end_time},'yyyy-MM-dd HH24:MI:SS')
           ) end_Case_Count ,
           (select count(1)
          from dev_clm.t_claim_case c
         where c.organ_code like o.organ_code || '%'
           and c.case_flag = 1
           and c.registe_conf_time > to_date(#{registe_start_time},'yyyy-MM-dd HH24:MI:SS')
           and c.registe_conf_time <= to_date(#{registe_end_time},'yyyy-MM-dd HH24:MI:SS')
           and c.end_case_time is null
           ) middle_Case_Count
        from dev_clm.t_udmp_org o
        where length(o.organ_code) = 4 and o.organ_code !=8600)  order by case_Count desc
	   ]]>
	</select>
	<select id="queryTimeMonitoringOneZhe" parameterType="java.util.Map" resultType="java.lang.Integer">
	    <![CDATA[
	    select count(1) every_Count
          from dev_clm.t_claim_case c
         where c.case_flag = 1
           and c.registe_conf_time > to_date(#{registe_start_time},'yyyy-MM-dd HH24:MI:SS')
           and c.registe_conf_time <= to_date(#{registe_end_time},'yyyy-MM-dd HH24:MI:SS')
	       and c.organ_code !=86
	    ]]>
	</select>
	<!-- 屏1 end -->
	<!-- 屏2 start 屏2显示当日的情况 CASE_FLAG=1代表普通案件 -->
	<!-- a.审核人：根据系统内作业人员（通过理赔角色判断是否为作业中心人员）有审核权限，展示顺序按审核人姓名排序。 b.审核待处理量：统计截至当前时点该审核人审核个人队列下任务类型为”正常件”且审核通过时间(AUDIT_TIME)为空的个险普通案件量(不限定立案确认时间) 
		//与需求确认，审核个人队列：进入审核个人队列，但不控制进入的时间 c. 审核案件量：统计截至当前时点该审核人个人队列中审核通过时间在当日零点后，且案件状态为结案或完成的作业量。 
		//与需求确认，审核个人队列：进入审核个人队列，但不控制进入的时间 -->

	<!-- 加字段后 AUDIT_START_TIME改为进入审核个人池的时间 -->
	<select id="queryTimeMonitoringTwoZhuAudit" parameterType="java.util.Map"
		resultType="java.util.Map">
		<!-- <![CDATA[SELECT A.USER_ID, (SELECT U.USER_NAME FROM DEV_CLM.T_UDMP_USER 
			U WHERE U.USER_ID = A.USER_ID) USER_NAME, (SELECT COUNT(1) FROM DEV_CLM.T_CLAIM_CASE 
			CA WHERE CA.CASE_FLAG = 1 AND CA.AUDIT_TIME IS NULL AND CA.AUDITOR_ID = A.USER_ID 
			AND CA.CASE_STATUS=61 ) AUDIT_M_COUNT, (SELECT COUNT(1) FROM DEV_CLM.T_CLAIM_CASE 
			CA WHERE CA.CASE_STATUS = 80 AND CA.CASE_FLAG = 1 AND TO_DATE(TO_CHAR(CA.AUDIT_TIME,'yyyy-MM-dd 
			HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') > to_date(#{audit_time},'yyyy-MM-dd 
			HH24:MI:SS') AND TO_DATE(TO_CHAR(CA.AUDIT_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd 
			HH24:MI:SS') < to_date(#{audit_time_dayend},'yyyy-MM-dd HH24:MI:SS') AND 
			CA.AUDITOR_ID = A.USER_ID) AUDIT_E_COUNT, (SELECT r.real_name FROM DEV_CLM.T_UDMP_USER 
			r WHERE r.USER_ID = A.USER_ID) real_name FROM DEV_CLM.T_CLAIM_TASK_STAFF 
			A WHERE AUDIT_FROM IS NOT NULL ORDER BY real_name]]> -->
            <![CDATA[ SELECT USER_ID,USER_NAME,AUDIT_M_COUNT,AUDIT_E_COUNT, REAL_NAME FROM(  SELECT A.USER_ID, 
                    (SELECT U.USER_NAME FROM APP___CLM__DBUSER.T_UDMP_USER U WHERE U.USER_ID = A.USER_ID) USER_NAME,
                      (SELECT COUNT(1)
                         FROM APP___CLM__DBUSER.T_CLAIM_CASE CA
                        WHERE CA.CASE_FLAG = 1
                          AND CA.AUDIT_TIME IS NULL
                          AND CA.AUDITOR_ID = A.USER_ID
                          AND CA.CASE_STATUS='61'
                         ) AUDIT_M_COUNT,
                      (SELECT COUNT(1)
                         FROM APP___CLM__DBUSER.T_CLAIM_CASE CA
                        WHERE CA.CASE_STATUS = '80'
                          AND CA.CASE_FLAG = 1
                          AND CA.END_CASE_TIME >= TO_DATE(#{end_case_time},'YYYY-MM-DD HH24:MI:SS')
                          AND CA.END_CASE_TIME <= TO_DATE(#{end_case_time_dayend},'YYYY-MM-DD HH24:MI:SS')
                          AND CA.AUDITOR_ID = A.USER_ID) AUDIT_E_COUNT,
                          (SELECT R.REAL_NAME
          FROM APP___CLM__DBUSER.T_UDMP_USER R
         WHERE R.USER_ID = A.USER_ID) REAL_NAME 
                FROM APP___CLM__DBUSER.T_CLAIM_TASK_STAFF A
               WHERE AUDIT_FROM IS NOT NULL
               AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
               WHERE B.OPERATOR_ID = C.USER_NAME AND C.USER_ID =A.USER_ID)            ORDER BY REAL_NAME) WHERE USER_NAME IS NOT NULL]]>
	</select>
	<!-- a.审批人：根据系统内作业人员（通过理赔角色判断是否为作业中心人员）有审批权限， b.审批待处理量：统计截至当前时点该审批人个人队列下审批通过时间为空的个险的案件量； 
		c. 审批案件量：统计截至当前时点运营作业中心所有审核批人个人队列中审批通过时间在当日零点后且案件状态为结案或完成的个险普通案件量。 -->
	<!-- 加字段后 APPROVE_TIME改为进入审批个人池的时间 -->
	<select id="queryTimeMonitoringTwoZhuApprove" parameterType="java.util.Map"
		resultType="java.util.Map">
		<!-- <![CDATA[SELECT A.USER_ID, (SELECT U.USER_NAME FROM DEV_CLM.T_UDMP_USER 
			U WHERE U.USER_ID = A.USER_ID) USER_NAME, (SELECT COUNT(1) FROM DEV_CLM.T_CLAIM_CASE 
			CA WHERE CA.END_CASE_TIME IS NULL AND CA.CASE_FLAG = 1 AND CA.APPROVER_ID 
			= A.USER_ID AND CA.CASE_STATUS=71 ) APPROVE_M_COUNT, (SELECT COUNT(1) FROM 
			DEV_CLM.T_CLAIM_CASE CA WHERE CA.CASE_STATUS = 80 AND CA.CASE_FLAG = 1 AND 
			TO_DATE(TO_CHAR(CA.END_CASE_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') 
			> to_date(#{end_case_time},'yyyy-MM-dd HH24:MI:SS') AND TO_DATE(TO_CHAR(CA.END_CASE_TIME,'yyyy-MM-dd 
			HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') < to_date(#{end_case_time_dayend},'yyyy-MM-dd 
			HH24:MI:SS') AND CA.APPROVER_ID = A.USER_ID) APPROVE_E_COUNT, (SELECT r.real_name 
			FROM DEV_CLM.T_UDMP_USER r WHERE r.USER_ID = A.USER_ID) real_name FROM DEV_CLM.T_CLAIM_TASK_STAFF 
			A WHERE APPROVE_FROM IS NOT NULL ORDER BY real_name]]> -->
          <![CDATA[SELECT USER_ID,USER_NAME,APPROVE_M_COUNT,APPROVE_E_COUNT, REAL_NAME FROM( SELECT A.USER_ID,
                    (SELECT U.USER_NAME FROM APP___CLM__DBUSER.T_UDMP_USER U WHERE U.USER_ID = A.USER_ID) USER_NAME,
                    (SELECT COUNT(1)
                       FROM APP___CLM__DBUSER.T_CLAIM_CASE CA
                      WHERE CA.END_CASE_TIME IS NULL
                        AND CA.APPROVER_ID = A.USER_ID
                        AND CA.CASE_STATUS='71'
                        ) APPROVE_M_COUNT,
                    (SELECT COUNT(1)
                       FROM APP___CLM__DBUSER.T_CLAIM_CASE CA
                      WHERE CA.CASE_STATUS = '80'
                        AND CA.CASE_FLAG = 1
                        AND CA.END_CASE_TIME >= TO_DATE(#{end_case_time},'YYYY-MM-DD HH24:MI:SS')
                        AND CA.END_CASE_TIME <= TO_DATE(#{end_case_time_dayend},'YYYY-MM-DD HH24:MI:SS')
                        AND CA.APPROVER_ID = A.USER_ID) APPROVE_E_COUNT,
                        (SELECT R.REAL_NAME
          FROM APP___CLM__DBUSER.T_UDMP_USER R
         WHERE R.USER_ID = A.USER_ID) REAL_NAME 
              FROM APP___CLM__DBUSER.T_CLAIM_TASK_STAFF A
             WHERE APPROVE_FROM IS NOT NULL
             AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
                           WHERE B.OPERATOR_ID = C.USER_NAME AND C.USER_ID =A.USER_ID)
          ORDER BY REAL_NAME ) WHERE USER_NAME IS NOT NULL]]>
	</select>
	<!-- 趋势图审核待处理量 审核待处理案件量：统计当日运营作业中心所有审核人审核个人队列任务类型为”正常件”且审核通过时间为空的个险普通案件量(不限定立案确认时间) -->
	<!-- 时间维度00：00-09：00 指进入个人池的时间,但此时间段内没有审核通过 -->
	<!-- 加字段后 AUDIT_START_TIME改为进入审核个人池的时间 -->
	<select id="queryTimeMonitoringTwoAuditZhe" parameterType="java.util.Map"
		resultType="java.lang.Integer">
	<![CDATA[select count(1) every_Count
          from dev_clm.t_claim_case c
         where c.CASE_STATUS=61
	       and c.audit_time is null 
	       and to_date(to_char(c.audit_individual_pool_time,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') >= to_date(#{audit_start_time},'yyyy-MM-dd HH24:MI:SS')
	       and to_date(to_char(c.audit_individual_pool_time,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') < to_date(#{audit_end_time},'yyyy-MM-dd HH24:MI:SS')
	       and c.case_flag = 1
           and c.auditor_id in(select a.user_id from dev_clm.t_claim_task_staff a where a.audit_from is not null)
           AND EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER UR
                 WHERE B.OPERATOR_ID = UR.USER_NAME AND  UR.USER_ID =C.AUDITOR_ID) 
           ]]>
	</select>
	<!-- 趋势图审批待处理量 审批待处理案件量：统计当日运营作业中心所有审批人审批个人队列下审批通过时间为空的个险的普通案件量。 -->
	<!-- 加字段后 APPROVE_TIME改为进入审批个人池的时间 -->
	<select id="queryTimeMonitoringTwoAppZhe" parameterType="java.util.Map"
		resultType="java.lang.Integer">
	<![CDATA[select count(1) every_Count
          from dev_clm.t_claim_case c
         where c.CASE_STATUS='71'
	       and c.end_case_time is null 
		   and to_date(to_char(c.APPROVE_INDIVIDUAL_POOL_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') >= to_date(#{approve_start_time},'yyyy-MM-dd HH24:MI:SS')
		   and to_date(to_char(c.APPROVE_INDIVIDUAL_POOL_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') < to_date(#{approve_end_time},'yyyy-MM-dd HH24:MI:SS')
	       and c.case_flag = 1
           and c.approver_id in(select a.user_id from dev_clm.t_claim_task_staff a where a.approve_from is not null)
           AND EXISTS( SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER UR
                 WHERE B.OPERATOR_ID = UR.USER_NAME AND UR.USER_ID = C.APPROVER_ID)
           ]]>
	</select>
	<!-- 各权限审核待处理量 -->
	<!-- 赔案各权限已立案确认尚未进入审核个人队列的个险普通案件量，（当日16:00后外包回传赔案不在统计，即立案确认时间在当日16:00后的赔案不再统计） -->
	<!-- 加字段后 AUDIT_START_TIME改为进入审核个人池的时间 -->
	<select id="queryTimeMonitoringTwoAuditPer" parameterType="java.util.Map"
		resultType="java.util.Map">
	  <![CDATA[SELECT A.PERMISSION_NAME,
                      (SELECT COUNT(1) FROM  APP___CLM__DBUSER.T_CLAIM_CASE CA 
                        WHERE CA.AUDIT_PERMISSION_NAME = A.PERMISSION_NAME
                          AND CA.CASE_FLAG = 1
                          AND CA.CASE_STATUS='60'
                          AND CA.AUDIT_INDIVIDUAL_POOL_TIME IS NULL
                          AND CA.AUDITOR_ID IS NULL
                          AND CA.REGISTE_CONF_TIME > to_date(#{regist_start_time},'yyyy-MM-dd HH24:MI:SS')
                          AND CA.REGISTE_CONF_TIME <= to_date(#{regist_end_time},'yyyy-MM-dd HH24:MI:SS')
                       ) PERMISSION_AUDIT_COUNT  
                FROM APP___CLM__DBUSER.T_UDMP_PERMISSION_INFO A 
               WHERE A.PERMISSION_NAME LIKE 'CLM-AD%' 
            ORDER BY A.PERMISSION_NAME]]>
	</select>
	<!-- 各权限审批待处理量 -->
	<!-- 显示各权限已审核确认但尚未进入审批个人队列的个险普通案件量。(审核确认时间在当日16：30之后的不再统计) -->
	<!-- 加字段后 APPROVE_TIME改为进入审批个人池的时间 -->
	<select id="queryTimeMonitoringTwoAppPer" parameterType="java.util.Map"
		resultType="java.util.Map">
	  <![CDATA[SELECT A.PERMISSION_NAME,
                      (SELECT COUNT(1) FROM  APP___CLM__DBUSER.T_CLAIM_CASE CA 
                        WHERE CA.APPROVE_PERMISSION_NAME = A.PERMISSION_NAME
                          AND CA.CASE_FLAG = 1
                          AND NOT EXISTS (SELECT 1
                            FROM APP___CLM__DBUSER.t_CASE_STATUS B
                           WHERE b.code = CA.CASE_STATUS and b.code != '80')
                          AND CA.APPROVE_INDIVIDUAL_POOL_TIME IS NULL
                          AND CA.APPROVER_ID IS NULL
                          AND CA.AUDIT_TIME > to_date(#{audit_start_time},'yyyy-MM-dd HH24:MI:SS')
                          AND CA.AUDIT_TIME <= to_date(#{audit_end_time},'yyyy-MM-dd HH24:MI:SS')
                      ) PERMISSION_APPROVE_COUNT  
                FROM APP___CLM__DBUSER.T_UDMP_PERMISSION_INFO A 
               WHERE A.PERMISSION_NAME LIKE 'CLM-AP%' 
                 AND A.PERMISSION_NAME < 'CLM-AP11' 
            ORDER BY A.PERMISSION_NAME]]>
	</select>
	<!-- 审批权限合并总数查询 -->
	<select id="queryTimeMonitoringTwoAppPerSum" parameterType="java.util.Map"
		resultType="java.lang.Integer">
	<![CDATA[SELECT sum((SELECT COUNT(1) FROM  APP___CLM__DBUSER.T_CLAIM_CASE CA 
                        WHERE CA.APPROVE_PERMISSION_NAME = A.PERMISSION_NAME
                          AND CA.CASE_FLAG = 1
                          AND NOT EXISTS (SELECT 1
                            FROM APP___CLM__DBUSER.t_CASE_STATUS B
                           WHERE b.code = CA.CASE_STATUS and b.code != '80')
                          AND CA.APPROVE_TIME IS NULL
                          AND CA.APPROVER_ID IS NULL
                          AND CA.AUDIT_TIME > to_date(#{audit_start_time},'yyyy-MM-dd HH24:MI:SS')
                          AND CA.AUDIT_TIME <= to_date(#{audit_end_time},'yyyy-MM-dd HH24:MI:SS')
                      ) )PERMISSION_APPROVE_COUNT  
                FROM APP___CLM__DBUSER.T_UDMP_PERMISSION_INFO A 
               WHERE A.PERMISSION_NAME in (${permission_name}) 
            ORDER BY A.PERMISSION_NAME]]>

	</select>
	<select id="queryTwoWorkQuantity" parameterType="java.util.Map"
		resultType="java.util.Map">
	<![CDATA[
	   SELECT A.PERSONAL_AUDIT_PROCESSED,
               A.PERSONAL_AUDIT_CASES,
               A.PERSONAL_APPROVE_PROCESSED,
               A.PERSONAL_APPROVE_CASES,
               A.TOTAL_AUDIT_CASES,
               A.TOTAL_APPROVE_CASES,
               A.AUDIT_TREATED_TOAL,
               A.APPROVE_TREATED_TOAL,
               A.OVERALL_AUDIT_CASES,
               A.OVERALL_APPROVE_CASES
          FROM DEV_CLM.T_WORK_QUANTITY A
         WHERE A.PERSONAL_AUDIT_PROCESSED IS NOT NULL
	]]>
	</select>

	<!-- 屏2 end -->
	<!-- 屏3 start -->
	<!-- 1)显示对应理赔时效天数的未结赔案件数，未结案赔案件数指的是全系统内已立案确认已经进入个人工作池但审批通过时间为空的个险普通案件量。 
		2) 天数指的是核心系统的理赔时效(审核管理界面的“理赔时效”字段)。 7天代表7天（含）以上14天（不含）；14天代表14天（含）以上21天（不含）；21天代表21天（含）以上； -->
	<select id="queryTimeMonitoringThreeDay" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[ SELECT count(1)
                FROM DEV_CLM.T_CLAIM_CASE A
               WHERE A.CASE_FLAG=1
                 AND A.CASE_STATUS != 80
                 AND A.CASE_STATUS != 90
           		 AND A.CASE_STATUS != 99
                 AND A.REGISTE_CONF_TIME IS NOT NULL
                 AND (A.APPROVER_ID IS NOT NULL OR A.AUDITOR_ID IS NOT NULL)
                 AND A.END_CASE_TIME IS NULL
                  AND ((EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
                 WHERE B.OPERATOR_ID = C.USER_NAME AND  C.USER_ID =A.AUDITOR_ID)  AND A.CASE_STATUS IN ('60', '61'))                
                 OR 
                 (EXISTS( SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
                 WHERE B.OPERATOR_ID = C.USER_NAME AND C.USER_ID = A.APPROVER_ID) AND A.CASE_STATUS IN ('70', '71') )  
                 )               
                 AND floor((TO_DATE(TO_CHAR(#{today_date},'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') -
                          TO_DATE(TO_CHAR(A.SIGN_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS')))>=#{date_start_time}]]>
		<if test="date_end_time != null">
                 <![CDATA[AND floor((TO_DATE(TO_CHAR(#{today_date},'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') -
                     TO_DATE(TO_CHAR(A.SIGN_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS')))<#{date_end_time}]]>
		</if>
	</select>
	<!-- 显示立案未结案赔案统计中大于7(含)天赔案，展示顺序为按申请时效降序排列，一页全部展示。 -->
	<select id="queryTimeMonitoringThreeDayList" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
        SELECT floor((TO_DATE(TO_CHAR(#{today_date},'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') -
               TO_DATE(TO_CHAR(A.SIGN_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS'))) SIGN_AGING,
               floor((TO_DATE(TO_CHAR(#{today_date},'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') -
               TO_DATE(TO_CHAR(A.REGISTE_CONF_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS'))) REGISTER_AGING,
               CASE WHEN A.APPROVE_PERMISSION_NAME IS NOT NULL THEN A.APPROVE_PERMISSION_NAME ELSE A.AUDIT_PERMISSION_NAME END AUDIT_PERMISSION_NAME,
               A.CASE_STATUS,
               CASE WHEN (A.APPROVER_ID IS NOT NULL AND A.CASE_STATUS IN ('70', '71')) THEN A.APPROVER_ID  ELSE (A.AUDITOR_ID) END APPROVER_ID,               
               A.CASE_NO,
               A.INSURED_ID           
          FROM DEV_CLM.T_CLAIM_CASE A 
         WHERE A.CASE_FLAG=1
           AND A.CASE_STATUS != 80
           AND A.CASE_STATUS != 90
           AND A.CASE_STATUS != 99
           AND A.REGISTE_CONF_TIME IS NOT NULL
           AND (A.APPROVER_ID IS NOT NULL OR A.AUDITOR_ID IS NOT NULL)
           AND A.END_CASE_TIME IS NULL
            AND ((EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
                 WHERE B.OPERATOR_ID = C.USER_NAME AND  C.USER_ID =A.AUDITOR_ID)  AND A.CASE_STATUS IN ('60', '61'))                
                 OR 
                 (EXISTS( SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
                 WHERE B.OPERATOR_ID = C.USER_NAME AND C.USER_ID = A.APPROVER_ID) AND A.CASE_STATUS IN ('70', '71') )  
                 )
           AND floor((TO_DATE(TO_CHAR(#{today_date},'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') -
               TO_DATE(TO_CHAR(A.SIGN_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS')))>=7
      ORDER BY SIGN_AGING DESC
    ]]>
	</select>
	<!-- 1）显示案件本身的审核或审批权限对应的未结赔案件数；未结案赔案件数指的是全系统内已立案确认但审批通过时间为空的个险普通案件。 2）显示如下权限的高额件：A11，A12，A13，A14，AP24-28，AP29-30，其中A11包含50万至100万赔案。 -->
	<select id="queryTimeMonitoringThreeHighDayAudit" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[ SELECT count(1)
                FROM DEV_CLM.T_CLAIM_CASE A
               WHERE A.CASE_FLAG=1
                 AND A.CASE_STATUS != 80
                 AND A.CASE_STATUS != 90
           		 AND A.CASE_STATUS != 99
                 AND A.REGISTE_CONF_TIME IS NOT NULL
                 AND A.END_CASE_TIME IS NULL                
                 AND A.AUDIT_PERMISSION_NAME in(${audit_permission_name})
                 AND A.CASE_STATUS in('61','60') 
                 ]]>
	</select>
	
	<!--  高额未接赔案统计  A11-->
	<select id="queryTimeMonitoringThreeHighDayAuditForADE" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[ SELECT count(1)
                FROM DEV_CLM.T_CLAIM_CASE A
               WHERE A.CASE_FLAG=1
                 AND A.CASE_STATUS != 80
                 AND A.CASE_STATUS != 90
           		 AND A.CASE_STATUS != 99
                 AND A.REGISTE_CONF_TIME IS NOT NULL
                 AND A.END_CASE_TIME IS NULL   
                 AND A.ACTUAL_PAY BETWEEN 800000 AND 1000000     
                 AND A.AUDIT_PERMISSION_NAME in(${audit_permission_name})
                 AND A.CASE_STATUS in('61','60') 
                 ]]>
	</select>
	<select id="queryTimeMonitoringThreeHighDayApprove"
		parameterType="java.util.Map" resultType="java.lang.Integer">
    <![CDATA[ SELECT count(1)
                FROM DEV_CLM.T_CLAIM_CASE A
               WHERE A.CASE_FLAG=1
                 AND A.CASE_STATUS != 80
                 AND A.CASE_STATUS != 90
           		 AND A.CASE_STATUS != 99
                 AND A.REGISTE_CONF_TIME IS NOT NULL
                 AND A.END_CASE_TIME IS NULL
                 AND A.APPROVE_PERMISSION_NAME in(${approve_permission_name})
                 AND A.CASE_STATUS in('71','70')  
                 ]]>
	</select>
	<!-- 展示高额未结案赔案统计的内容。展示顺序为按申请时效降序排列。要求一页全部展示 -->
	<select id="queryTimeMonitoringThreeHighDayList" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
        SELECT floor((TO_DATE(TO_CHAR(#{today_date},'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') -
               TO_DATE(TO_CHAR(A.SIGN_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS'))) SIGN_AGING,
               floor((TO_DATE(TO_CHAR(#{today_date},'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS') -
               TO_DATE(TO_CHAR(A.REGISTE_CONF_TIME,'yyyy-MM-dd HH24:MI:SS'),'yyyy-MM-dd HH24:MI:SS'))) REGISTER_AGING,
               CASE WHEN  A.CASE_STATUS in ('70','71') THEN A.APPROVE_PERMISSION_NAME ELSE A.AUDIT_PERMISSION_NAME END AUDIT_PERMISSION_NAME,
               A.CASE_STATUS,
               CASE WHEN (A.APPROVER_ID IS NOT NULL AND A.CASE_STATUS IN ('70', '71')) THEN A.APPROVER_ID  ELSE (A.AUDITOR_ID) END APPROVER_ID,               A.CASE_NO,
               A.INSURED_ID           
          FROM DEV_CLM.T_CLAIM_CASE A 
         WHERE A.CASE_FLAG=1
           AND A.CASE_STATUS != 80
           AND A.CASE_STATUS != 90
           AND A.CASE_STATUS != 99
               AND A.REGISTE_CONF_TIME IS NOT NULL
               AND A.END_CASE_TIME IS NULL
                AND ((EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
                 WHERE B.OPERATOR_ID = C.USER_NAME AND  C.USER_ID =A.AUDITOR_ID)  AND A.CASE_STATUS IN ('60', '61'))                
                 OR 
                 (EXISTS( SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_MONITORING_PERSON B,APP___CLM__DBUSER.T_UDMP_USER C
                 WHERE B.OPERATOR_ID = C.USER_NAME AND C.USER_ID = A.APPROVER_ID) AND A.CASE_STATUS IN ('70', '71') )  
                 )
               AND ((A.APPROVE_PERMISSION_NAME IN(${approve_permission_name}) AND  A.CASE_STATUS IN ('70', '71'))
               OR ((A.AUDIT_PERMISSION_NAME IN(${audit_permission_name}) AND A.CASE_STATUS IN ('60', '61')) 
               OR ((A.AUDIT_PERMISSION_NAME IN(${audit_eleven}) AND A.CASE_STATUS IN ('60','61') AND A.ACTUAL_PAY BETWEEN 800000 AND 1000000 ))
               ]]>
            <![CDATA[) ) 
            ORDER BY SIGN_AGING DESC]]>           
	</select>
	<!-- 屏3 end -->
	<!-- 核赔作业实时监测 add by zhaoyq end -->

	<!-- 屏4 end -->
	<!-- 查询当月审核、审批件数 -->
	<select id="findClaimMounthCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ select count(0) from APP___CLM__DBUSER.T_CLAIM_CASE R ,APP___CLM__DBUSER.T_UDMP_USER A WHERE 1=1
		        AND R.CASE_STATUS = '80' 
        ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test="audit_time!=null and audit_time !='' ">
            			<![CDATA[AND R.AUDITOR_ID=A.USER_ID AND R.audit_time >= #{audit_time}
            			and R.audit_time < #{today_end_time}]]>
		</if>
		<if test="approve_time!=null and approve_time !='' ">
            			<![CDATA[AND R.APPROVER_ID=A.USER_ID AND R.approve_time >= #{approve_time}
            			and R.approve_time < #{today_end_time}]]>
		</if>
		<![CDATA[AND EXISTS (SELECT 1 FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B WHERE B.OPERATOR_ID = A.USER_NAME )
		         ORDER BY R.INSERT_TIME ]]>
	</select>
	<!-- 查询当日审核人 与需求确认只判断审核确认时间为当日，不判断是否结案 -->
	<select id="findClaimAuditNameDay" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT L.*, ROWNUM RN
  	FROM (select distinct a.auditor_id,a.approver_id,
                        (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r where r.user_id = a.auditor_id
                        ) as auditor_name,
                          (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r   where r.user_id = a.approver_id) as approver_name
          from dev_clm.t_claim_case a
         where 1=1 ]]>
		<if test="audit_time !=null and audit_time !='' ">
            			<![CDATA[AND a.audit_time >= #{audit_time}
            			and a.audit_time < #{today_end_time}]]>
		</if>
          
       <![CDATA[ ) L where ROWNUM <= 20]]>

	</select>
	<!-- 查询当月审核人 -->
	<select id="findClaimAuditNameMounth" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT L.*, ROWNUM RN
  	FROM (select distinct a.auditor_id,a.approver_id,
                        (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r where r.user_id = a.auditor_id) as auditor_name,
                          (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r   where r.user_id = a.approver_id) as approver_name
          from dev_clm.t_claim_case a
         where 1=1 ]]>
		<if test="audit_time!=null and audit_time !='' ">
            			<![CDATA[AND a.audit_time >= #{audit_time}
            			and a.audit_time < #{today_end_time}]]>
		</if>
          
       <![CDATA[ ) L where ROWNUM <= 20]]>

	</select>
	<!-- 查询当日审批人 -->
	<select id="findClaimApproveNameDay" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT L.*, ROWNUM RN
  	FROM (select distinct a.auditor_id,a.approver_id,
                        (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r where r.user_id = a.auditor_id) as auditor_name,
                          (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r   where r.user_id = a.approver_id) as approver_name
          from dev_clm.t_claim_case a
         where 1=1 ]]>
		<if test="approve_time!=null and approve_time !='' ">
            			<![CDATA[AND a.approve_time >= #{approve_time}
            			and a.approve_time < #{today_end_time}]]>
		</if>
          
       <![CDATA[ ) L where ROWNUM <= 10]]>

	</select>
	<!-- 查询当月审批人 -->
	<select id="findClaimApproveNameMounth" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT L.*, ROWNUM RN
  	FROM (select distinct a.auditor_id,a.approver_id,
                        (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r where r.user_id = a.auditor_id) as auditor_name,
                          (select r.real_name  from APP___CLM__DBUSER.t_udmp_user r   where r.user_id = a.approver_id) as approver_name
          from dev_clm.t_claim_case a
         where 1=1 ]]>
		<if test="approve_time!=null and approve_time !='' ">
            			<![CDATA[AND a.approve_time >= #{approve_time}
            			and a.approve_time < #{today_end_time}]]>
		</if>
          
       <![CDATA[ ) L where ROWNUM <= 10]]>
	</select>
	<!-- 查询当日审核人、审批人对应的件数 -->
	<select id="findClaimAuditApproveCountDay" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ select count(0) from APP___CLM__DBUSER.T_CLAIM_CASE r where 1=1]]>
		<if test="approver_id!=null and approver_id !='' ">
            			<![CDATA[AND r.approver_id=#{approver_id}]]>
		</if>
		<if test="auditor_id!=null and auditor_id !='' ">
            			<![CDATA[AND r.auditor_id=#{auditor_id}]]>
		</if>
		<if test="audit_time!=null and audit_time !='' ">
            			<![CDATA[AND trunc(r.audit_time)=#{audit_time}]]>
		</if>
		<if test="approve_time!=null and approve_time !='' ">
            			<![CDATA[AND trunc(r.approve_time)=#{approve_time}]]>
		</if>

	</select>
	<!-- 查询当日审核人、对应的件数、倒序 -->
	<select id="findClaimAudttorCountDay" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT REAL_NAME,AUDITCOUNT FROM (SELECT DISTINCT A.REAL_NAME, COUNT(1) AS AUDITCOUNT
  				  FROM APP___CLM__DBUSER.T_CLAIM_CASE R, APP___CLM__DBUSER.T_UDMP_USER A
  				  WHERE 1 = 1
  				  AND R.AUDITOR_ID=A.USER_ID 
  				  AND R.AUDIT_TIME >= #{audit_time}
  				  and R.AUDIT_TIME < #{today_end_time}
  				   AND EXISTS (SELECT 1 FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B WHERE B.OPERATOR_ID = A.USER_NAME )
    			  GROUP BY A.REAL_NAME ORDER BY COUNT(1) DESC ) T WHERE ROWNUM<=20]]>
	</select>

	<!-- 查询当日审批人、对应的件数、倒序 -->
	<select id="findClaimApproveCountDay" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT REAL_NAME,APPROVERCOUNT FROM (SELECT DISTINCT A.REAL_NAME, COUNT(1) AS APPROVERCOUNT
  				  FROM APP___CLM__DBUSER.T_CLAIM_CASE R, APP___CLM__DBUSER.T_UDMP_USER A
  				  WHERE 1 = 1
  				  AND R.APPROVER_ID=A.USER_ID 
  				  AND R.Approve_Time >= #{approve_time}
  				  and R.Approve_Time < #{today_end_time}
  				   AND EXISTS (SELECT 1 FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B WHERE B.OPERATOR_ID = A.USER_NAME )
                  GROUP BY A.REAL_NAME ORDER BY COUNT(1) DESC ) T WHERE ROWNUM<=10]]>
	</select>

	<!-- 查询月审核人、对应的件数、倒序 -->
	<select id="findClaimAuditCountMounth" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT REAL_NAME,AUDITCOUNT FROM (SELECT DISTINCT A.REAL_NAME, COUNT(1) AS AUDITCOUNT
  				  FROM APP___CLM__DBUSER.T_CLAIM_CASE R, APP___CLM__DBUSER.T_UDMP_USER A
  				  WHERE 1 = 1
   				  AND R.AUDITOR_ID = A.USER_ID
   				  AND EXISTS (SELECT 1 FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B WHERE B.OPERATOR_ID = A.USER_NAME )]]>
		<if
			test="audit_time!=null and audit_time !='' and lastDay_Month!=null and lastDay_Month !=''">
         		<![CDATA[AND R.AUDIT_TIME>=#{audit_time}]]>  
         		<![CDATA[AND R.AUDIT_TIME<=#{lastDay_Month}]]>
		</if>
 				<![CDATA[GROUP BY A.REAL_NAME ORDER BY COUNT(1) DESC ) T WHERE ROWNUM<=20]]>
	</select>
	<!-- 查询月审批人、对应的件数、倒序 -->
	<select id="findClaimApproveCountMounth" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT REAL_NAME,APPROVERCOUNT FROM (SELECT DISTINCT A.REAL_NAME, COUNT(1) AS APPROVERCOUNT
  				  FROM APP___CLM__DBUSER.T_CLAIM_CASE R, APP___CLM__DBUSER.T_UDMP_USER A
  				  WHERE 1 = 1
         		  AND R.APPROVER_ID=A.USER_ID
         		  AND EXISTS (SELECT 1 FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B WHERE B.OPERATOR_ID = A.USER_NAME )]]>
		<if
			test="approve_time!=null and approve_time !='' and lastDay_Month!=null and lastDay_Month !='' "> 
         		 <![CDATA[AND R.APPROVE_TIME>=#{approve_time}]]>  
         		 <![CDATA[AND R.APPROVE_TIME<=#{lastDay_Month}]]>
		</if>
 				<![CDATA[GROUP BY A.REAL_NAME ORDER BY COUNT(1) DESC ) T WHERE ROWNUM<=10]]>
	</select>

	<!-- 查询当月审核人、审批人对应的件数 -->
	<select id="findClaimAuditApproveCountMounth" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ select count(0) from APP___CLM__DBUSER.T_CLAIM_CASE r where 1=1 ]]>
		<if test="approver_id!=null and approver_id !='' ">
            			<![CDATA[AND r.approver_id=#{approver_id}]]>
		</if>
		<if test="auditor_id!=null and auditor_id !='' ">
            			<![CDATA[AND r.auditor_id=#{auditor_id}]]>
		</if>
		<if
			test="audit_time!=null and audit_time !='' and lastDay_Month!=null and lastDay_Month !=''">
            			<![CDATA[AND trunc(r.audit_time)>=#{audit_time}]]>
            			<![CDATA[AND trunc(r.audit_time)<=#{lastDay_Month}]]>
		</if>
		<if
			test="approve_time!=null and approve_time !='' and lastDay_Month!=null and lastDay_Month !='' ">
            			<![CDATA[AND trunc(r.approve_time)>=#{approve_time}]]>
            			<![CDATA[AND trunc(r.approve_time)<=#{lastDay_Month}]]>
		</if>
	</select>

	<!-- 查询邮编 -->
	<select id="findValidatePost" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.VALIDATE_POST FROM DEV_CLM.T_VALIDATE_POST A WHERE A.VALIDATE_ADS_NUM=${validate_ads_num}]]>
	</select>

	<!-- 查询非医疗既往赔案 -->
	<select id="findRepCase" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[

            SELECT DISTINCT 
                A1.CASE_NO
               FROM DEV_CLM.T_CLAIM_CASE     A,
             DEV_CLM.T_CLAIM_SUB_CASE B,
             DEV_CLM.T_CLAIM_CASE     A1,
             DEV_CLM.T_CLAIM_SUB_CASE B1
              WHERE A.CASE_ID = B.CASE_ID
              AND A.INSURED_ID = A1.INSURED_ID  
              AND A1.CASE_ID = B1.CASE_ID
              AND B1.CLAIM_TYPE = B.CLAIM_TYPE 
              AND B1.ACC_REASON = B.ACC_REASON 
              AND B.CLAIM_DATE = B1.CLAIM_DATE  
              AND A1.CASE_STATUS NOT IN ('99','90')   
              AND (A1.AUDIT_DECISION <> '3'  or A1.AUDIT_DECISION is null)
           	  AND A.AUDIT_DECISION <> '3'
              AND A.CASE_ID = #{case_id} 
              AND A1.CASE_ID != A.CASE_ID  
       ]]>

	</select>

	<!-- 查询医疗门诊账单是否相同 -->
	<select id="findRepBillOutPatientCase" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
			SELECT DISTINCT A1.CASE_NO
			  FROM DEV_CLM.T_CLAIM_CASE A,
			       DEV_CLM.T_CLAIM_BILL B,
			       DEV_CLM.T_CLAIM_CASE A1,
			       DEV_CLM.T_CLAIM_BILL B1
			 WHERE A.CASE_ID = B.CASE_ID
			   AND A1.CASE_ID = B1.CASE_ID
			   AND B1.BILL_NO = B.BILL_NO   
			   AND B1.HOSPITAL_CODE = B.HOSPITAL_CODE  
			   AND B1.BILL_TYPE = 1 
			   AND A.CASE_ID = #{case_id}
			   AND A1.CASE_ID != A.CASE_ID  
       ]]>

	</select>
	<!-- 移动理赔案件信息查询接口-->
	<select id="wechatMobileQueryCaseInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
			SELECT CAS.RPTR_TIME,
			       CAS.CASE_NO,
			       CUST.CUSTOMER_NAME,
			       CAS.INSURED_ID,
			       CUST.OLD_CUSTOMER_ID,
			       CAS.CASE_STATUS,
			       (SELECT TO_CHAR(WM_CONCAT(SUB.CLAIM_TYPE))
			          FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE SUB
			         WHERE CAS.CASE_ID = SUB.CASE_ID) AS CLAIM_TYPE,
			       CAS.ACTUAL_PAY,
			       CAS.APPROVE_DECISION,
			       CAS.AUDIT_DECISION,
			       CAS.ACCEPT_DECISION,
			       CAS.End_Case_Time,
			       CAS.APPLY_DATE,
			       ACC.ACC_REASON, 
			       CAS.EASY_AUDIT_DECISION,
			       (SELECT TO_CHAR(WM_CONCAT(APP.CLMT_INSUR_RELATION))
			          FROM APP___CLM__DBUSER.T_CLAIM_APPLICANT APP
			         WHERE CAS.CASE_ID = APP.CASE_ID) AS CLMT_INSUR_RELATION
			  FROM APP___CLM__DBUSER.T_CLAIM_CASE CAS,
			  	   APP___CLM__DBUSER.T_CLAIM_ACCIDENT ACC,
			       APP___CLM__DBUSER.T_CUSTOMER   CUST
			 WHERE CUST.CUSTOMER_ID = CAS.INSURED_ID
			   AND CAS.ACCIDENT_ID = ACC.ACCIDENT_ID
			   AND CAS.CASE_STATUS != '99'
			   AND CAS.INSURED_ID = #{insured_id} ]]>
		<if test="applystardate!=null and applystardate !='' ">
            			<![CDATA[AND CAS.RPTR_TIME >= to_date(#{applystardate},'yyyy-MM-dd HH24:mi:ss')]]>
		</if>  
		<if test="applyenddate!=null and applyenddate !='' ">
            			<![CDATA[AND CAS.RPTR_TIME <= to_date(#{applyenddate},'yyyy-MM-dd HH24:mi:ss')]]>
		</if>  
		<![CDATA[ ORDER BY RPTR_TIME DESC ]]>
	</select>
	<!-- 查询被保人下的所有保单业务员信息-->
	<select id="queryPolicyAgentInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
			SELECT DISTINCT AGE.AGENT_CODE
			  FROM  APP___CLM__DBUSER.T_INSURED_LIST LIS,  APP___CLM__DBUSER.T_CONTRACT_AGENT AGE
			 WHERE AGE.POLICY_CODE = LIS.POLICY_CODE
			   AND AGE.CASE_ID=LIS.CASE_ID
			   AND LIS.CUSTOMER_ID = ${customer_id}
			   AND LIS.CUR_FLAG = '1'
       ]]>
	</select>
	<!-- 根据出险人号查询报案中的赔案信息 -->
	<select id="findAllClaimCaseByCustomerNO" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
			SELECT DISTINCT CAS.CASE_ID,
		                        CAS.CASE_NO,
		                        CAS.RPTR_NAME,
		                        CAS.RPTR_MP,
		                        CAS.RPTR_ADDR,
		                        CAS.RPTR_ZIP,
		                        CAS.RPTR_RELATION,
		                        CAS.REPORT_MODE,
		                        CAS.INSERT_TIME ACCEPT_TIME,
		                        (SELECT A.USER_NAME
		                           FROM APP___CLM__DBUSER.T_UDMP_USER A
		                          WHERE A.USER_ID = CAS.ACCEPTOR_ID) AS operator,
		                        CAS.CASE_APPLY_TYPE,
		                        ACC.ACCIDENT_NO AS ACC_NO,
		                        ACC.ACC_DATE,
		                        ACC.ACC_PROVINCE,
		                        ACC.ACC_CITY,
		                        ACC.ACC_DISTREACT,
		                        ACC.ACC_STREET,
		                        ACC.ACC_REASON,
		                        CAS.CURE_HOSPITAL,
		                        (SELECT TO_CHAR(WM_CONCAT(to_char(SUB.CLAIM_DATE,'yyyy-MM-dd')))
		                           FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE SUB
		                          WHERE SUB.CASE_ID = CAS.CASE_ID) CLAIM_DATE,
		                        CAS.ACCIDENT_DETAIL,
		                        CAS.CURE_STATUS,
		                        (SELECT TO_CHAR(WM_CONCAT(RESU.ACC_RESULT1))
		                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT RESU
		                          WHERE RESU.CASE_ID = CAS.CASE_ID) ACC_RESULT1,
		                        (SELECT TO_CHAR(WM_CONCAT(RESU.ACC_RESULT2))
		                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT RESU
		                          WHERE RESU.CASE_ID = CAS.CASE_ID) ACC_RESULT2,
		                        CAS.SERIOUS_DISEASE,
		                        (SELECT TY.NAME
		                           FROM APP___CLM__DBUSER.T_LA_TYPE TY
		                          WHERE TY.CODE = CAS.SERIOUS_DISEASE) AS SERIOUS_NAME,
		                        (SELECT TO_CHAR(WM_CONCAT(SUB.CLAIM_TYPE))
		                           FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE SUB
		                          WHERE SUB.CASE_ID = CAS.CASE_ID) CLAIM_TYPE,
		                        CAS.GREEN_FLAG,
		                        ACC.ACC_DESC,
		                        CAS.LOCK_FLAG,
		                        CAS.LOCK_SYS
		          FROM APP___CLM__DBUSER.T_CLAIM_CASE     CAS,
		               APP___CLM__DBUSER.T_CLAIM_ACCIDENT ACC
		         WHERE CAS.ACCIDENT_ID = ACC.ACCIDENT_ID
		           AND CAS.CASE_STATUS = '20'
		           AND CAS.INSURED_ID = ${insured_id}
       ]]>
	</select>
	<!-- 查询业务在途，更新客户信息 start -->
	<select id="findAllSurveyObjectByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT A.SURVEY_RESUALT, A.SURVEY_REASON, A.LIFE_FLAG, A.OBJECT_ID, A.REMARK, A.CUSTOMER_ID, 
			A.CUS_NAME, A.CERTI_NO, A.CHECK_FLAG, 
			A.SURVEY_OBJ_TYPE, A.CERTI_TYPE, A.APPLY_ID FROM APP___CLM__DBUSER.T_SURVEY_OBJECT A WHERE A.CUSTOMER_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllBodyInfoByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.INSURED_STATUS, A.CUSTOMER_ID, 
			A.CUSTOMER_BODY_ID, A.CASE_NO, A.CASE_ID,A.STATUS_DATE FROM APP___CLM__DBUSER.T_CLAIM_CUSTOMER_BODY_INFO A WHERE A.CUSTOMER_ID in(${customer_idlist}) ]]>
	</select>
	<select id="findAllInsuredListByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.INSURED_AGE, 
			A.LIST_ID, A.POLICY_ID, A.STAND_LIFE, A.SMOKING, A.SOCI_SECU,
			A.JOB_UNDERWRITE, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.CASE_ID, A.LOG_ID, A.COPY_DATE, 
			A.POLICY_CODE, A.CUR_FLAG,A.INPUT_SEQUENCE FROM APP___CLM__DBUSER.T_INSURED_LIST A WHERE A.CUSTOMER_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllPremArapByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE 
			A.CUSTOMER_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimSurveyTaskByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT A.BIZ_TYPE, A.BIZ_NO, A.CUSTOMER_NAME, A.CUSTOMER_ID, A.CLAIM_PAY, 
			A.ACCU_AMOUNT, A.CHANNEL_TYPE, A.APPLY_NUM, A.BATCH_ID, A.LIST_ID, A.ORGAN_CODE,
			A.PAYEE_NUM FROM APP___CLM__DBUSER.T_CLAIM_SURVEY_TASK A WHERE A.CUSTOMER_ID in(${customer_idlist})  ]]>
	</select>
	<select id="findAllSurveyCourseByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.COURSE_ID, A.SURVEY_DATE, A.REMARK, A.CUSTOMER_ID, A.SURVEY_ORG, 
			A.SURVEY_MODE, A.SURVEY_PLACE, A.SURVEY_BY_ID2, A.SURVEY_BY_ID1, 
			A.SURVEY_COURSE, A.APPLY_ID ,A.INSERT_BY,A.SURVEYED_PERSON,A.OPERATOR_NAME FROM APP___CLM__DBUSER.T_SURVEY_COURSE A WHERE A.CUSTOMER_ID in(${customer_idlist}) ]]>
	</select>
	<select id="findAllContractBeneByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.CASE_ID, A.APPLY_CODE, A.LOG_ID, A.COPY_DATE, A.POLICY_CODE, 
			A.DESIGNATION, A.LIST_ID, A.LEGAL_BENE, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.SHARE_RATE, A.CUR_FLAG FROM APP___CLM__DBUSER.T_CONTRACT_BENE A WHERE A.CUSTOMER_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimCaseRecordByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.REMARK, A.SPECIAL_REMARK_CODE, A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, 
			A.CASE_NO, A.REVIEW_FLAG, A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.LOSS_LEVEL_CODE, 
			A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.STAGE_CODE, A.CASE_RECORD_ID, A.LOSS_REASON_CODE, A.BALANCE_PAY, A.REJECT_REASON, 
			A.AUDITOR_ID, A.IS_MIGRATION, A.REGISTE_TIME, A.RPTR_MP, A.SERIOUS_DISEASE, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.REGISTE_ORGAN, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, 
			A.APPROVER_ID, A.ACCIDENT_ID, A.REJECT_PAY, A.AUDIT_ORGAN, A.IS_AUTO_HUNGUP, A.SIGN_TIME, 
			A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, A.RPTR_ADDR, A.SIGN_ORGAN, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, 
			A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, 
			A.IS_BPO, A.SEND_BENE_DOC_FLAG, A.AUDIT_TIME, A.APPROVE_TIME, A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, 
			A.APPROVE_DECISION, A.TRUSTEE_CODE, A.CHANNEL_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.APPROVE_ORGAN, 
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, 
			A.COMFORT_STATUS, A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO FROM DEV_CLM.T_CLAIM_CASE_RECORD A WHERE A.INSURED_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimAfcPlanByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.VALID_FLAG, A.PLAN_ID, A.REMARK, A.CUSTOMER_ID, A.PLAN_TYPE, 
			A.EXTRACT_LEVEL, A.PLAN_NAME, A.CERTI_CODE, A.MAKE_BY, A.VALID_DATE, 
			A.MAKE_DATE, A.CERTI_TYPE FROM DEV_CLM.T_CLAIM_AFC_PLAN A WHERE A.CUSTOMER_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllPolicyHolderByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.ADDRESS_ID, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.CASE_ID, A.APPLY_CODE, A.LOG_ID, A.COPY_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.CUR_FLAG  FROM DEV_CLM.T_POLICY_HOLDER A WHERE A.CUSTOMER_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimChecklistLogByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.ORGAN_CODE, A.LOG_ID, 
			A.Remark, A.INSURED_ID, A.CASE_ID FROM DEV_CLM.T_CLAIM_CHECKLIST_LOG A WHERE A.INSURED_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimAccidentByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.ACC_REASON, A.ACC_PROVINCE, A.ACC_STREET, A.ACCIDENT_NO, A.INSURED_ID, 
			A.ACC_CITY, A.ACC_DISTREACT, A.ACC_DESC, A.ACC_DATE, 
			A.ACCIDENT_ID,A.ACC_COUNTRY_CODE FROM DEV_CLM.T_CLAIM_ACCIDENT A WHERE A.INSURED_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimProductByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.ACTUAL_PAY, A.PRODUCT_ID, A.WAIVE_START, A.CLAIM_PROD_ID, A.ITEM_ID, 
			A.INSURED_ID, A.CASE_ID, A.IS_WAIVED, A.LIABILITY_STATE, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.CALC_PAY, 
			A.POLICY_ID, A.BASIC_PAY, A.AMOUNT, A.INTEREST_ACCOUNT_FEE,A.BONUS_INTEREST_FEE,A.DEDUCT_OMNIPOTENT_ACCOUNT,A.POLICY_UNIVERSAL_OUTBOUNSAMOUN,A.BUSI_PROD_CODE FROM DEV_CLM.T_CLAIM_PRODUCT A WHERE A.INSURED_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimBusiProdByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT A.ACTUAL_PAY, A.CLAIM_BUSI_PROD_ID, A.BUSI_PROD_CODE, A.IS_WAIVED, A.VALID_DATE, 
			A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.EXPIRE_DATE, A.LIABILITY_STATUS, A.DUE_DATE, A.WAIVE_START, 
			A.INSURED_ID, A.CASE_ID, A.POLICY_CODE, A.WAIVE_AMT, A.WAIVE_END, 
			A.DEAL_CONCLUSION, A.CALC_PAY, A.REJECT_PAY, A.WAIVE_DESC, A.OVER_COMP_PAY, A.COMPUTE_END_TIME,A.ASSIGN_FLAG, A.IS_RENEWABLE FROM DEV_CLM.T_CLAIM_BUSI_PROD A WHERE A.INSURED_ID in(${customer_idlist})]]>
	</select>
	<select id="findAllClaimCaseByCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
      A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
      A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
      A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
      A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
      A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
      A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
      A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
      A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME,A.AUDIT_START_TIME, A.APPROVE_TIME, A.CHANNEL_CODE,
      A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, A.COMFORT_STATUS,
      A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.ACCEPT_DECISION, A.TRUSTEE_MP, A.CURE_STATUS, A.REALTIME_PAY,
      A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY, A.SURVEY_RESULT_INFO, A.UPDATE_BY, A.IS_AUTO_HUNGUP, A.IS_MIGRATION ,
      A.SIGN_ORGAN,A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.EASY_AUDIT_DECISION,A.EASY_AUDITOR_ID,A.MEDICAL_AMOUNT_FLAG,
      A.MEDICAL_TOTAL_MONY,A.MEDICAL_AMOUNT_CRITERION,A.AUDIT_INDIVIDUAL_POOL_TIME,A.APPROVE_INDIVIDUAL_POOL_TIME,
      A.BENE_COUNT,A.SIGN_USER_TYPE,A.SIGN_USER_CODE,A.SIGN_USER_NAME,A.INVERSE_RISK_LEVEL,A.INVOICE_RISK_LEVEL,A.CASE_RISK_LEVEL  FROM DEV_CLM.T_CLAIM_CASE A WHERE A.INSURED_ID in(${customer_idlist})]]>
	</select>
	<select id="findClaimCasePassage" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_NO FROM DEV_CLM.T_CLAIM_CASE A WHERE A.INSURED_ID in(${customer_list}) AND A.CASE_STATUS NOT IN(80,90,99) ]]>
	</select>
	<select id="findClaimCasePassageEnd" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID,A.CASE_NO,A.CASE_STATUS FROM DEV_CLM.T_CLAIM_CASE A WHERE A.INSURED_ID in(${customer_list}) AND A.CASE_STATUS=80]]>
	</select>
	<select id="findClaimPayChangeNoPass" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID, A.AUDIT_STATUS FROM DEV_CLM.T_CLAIM_PAY_CHANGE A WHERE A.AUDIT_STATUS IN(1,4) AND A.CASE_ID in(${caseid_list})]]>
	</select>
	<select id="findSurveyApplyNoPass" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID,A.CASE_NO,A.BIZ_TYPE,A.SURVEY_STATUS FROM DEV_CLM.T_SURVEY_APPLY A WHERE A.SURVEY_STATUS IN(1,4) AND A.BIZ_TYPE IN(4,5) AND CASE_ID in(${caseid_list})]]>
	</select>
	<select id="findCaseBackList" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT B.CASE_ID
               FROM DEV_CLM.T_CLAIM_CASE B
              WHERE B.RELATED_NO IN (SELECT A.RELATED_NO
                                       FROM DEV_CLM.T_CLAIM_CASE A
                                      WHERE A.RELATED_NO IS NOT NULL
                                            AND A.INSURED_ID in(${customer_list}))]]>
	</select>
	<select id="findClaimBackApplyNoPass" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID,A.BACK_STATUS FROM DEV_CLM.T_CLAIM_BACK_APPLY A WHERE A.BACK_STATUS IN(0,1) AND A.CASE_ID in(${caseid_list})]]>
	</select>
	<select id="findCustomerByCustomerIdList" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT A.CUSTOMER_ID,A.COUNTRY_CODE,A.MARRIAGE_STATUS,A.UPDATE_TIME FROM DEV_CLM.T_CUSTOMER A WHERE A.CUSTOMER_ID IN(${customer_idlist}) ORDER BY A.UPDATE_TIME DESC]]>
	</select>
	<select id="updateCustomerContryAndAMarry" parameterType="java.util.Map">
	<![CDATA[UPDATE DEV_CLM.T_CUSTOMER A  SET A.COUNTRY_CODE = #{country_code} ,A.MARRIAGE_STATUS = #{marriage_status} WHERE A.CUSTOMER_ID =#{customer_id}]]>
	</select>
	<select id="deleteCustomersByIds" parameterType="java.util.Map">
	<![CDATA[DELETE FROM DEV_CLM.T_CUSTOMER A WHERE A.CUSTOMER_ID IN(${customer_idlist})]]>
	</select>
	<!-- end -->
	
	<!-- 查询投保人信息 -->
	<select id="queryPolicyHolderInfo" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT HOL.CUSTOMER_ID
			  FROM APP___CLM__DBUSER.T_POLICY_HOLDER HOL
			 WHERE HOL.POLICY_CODE = #{policy_code}
			   AND HOL.CUR_FLAG = #{cur_flag}
			   AND HOL.CASE_ID = #{case_id}]]>
	</select>
	
	<!-- 查询被保人信息 -->
	<select id="queryInsuredListInfo" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT HOL.CUSTOMER_ID
			  FROM APP___CLM__DBUSER.T_INSURED_LIST HOL
			 WHERE HOL.POLICY_CODE = #{policy_code}
			   AND HOL.CUR_FLAG = #{cur_flag}
			   AND HOL.CASE_ID = #{case_id}]]>
	</select>
	<!-- 查询指定受益人信息-->
	<select id="queryContractBeneInfo" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT HOL.CUSTOMER_ID
			  FROM APP___CLM__DBUSER.T_CONTRACT_BENE HOL
			 WHERE HOL.POLICY_CODE = #{policy_code}
			   AND HOL.CUR_FLAG = #{cur_flag}
			   AND HOL.CASE_ID = #{case_id}
			   AND HOL.BENE_TYPE = #{bene_type}]]>
	</select>
	<!-- 查询受益人信息-->
	<select id="queryBeneName" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT B.BENE_NAME ,B.BENE_CERTI_TYPE,B.BENE_CERTI_NO
				  FROM APP___CLM__DBUSER.T_CLAIM_BENE B, APP___CLM__DBUSER.T_CLAIM_PAY P
				 WHERE B.BENE_ID = P.BENE_ID
				   AND P.CASE_ID = #{case_id}
		]]>
	</select>
	<select id="queryConstantsInfoList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[select constants_value,constants_key from app___clm__dbuser.T_CONSTANTS_INFO a where a.sub_id='067']]>
	</select>
	
	<select id="findConstantsValueByKey" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT A.CONSTANTS_VALUE FROM APP___CLM__DBUSER.T_CONSTANTS_INFO A WHERE A.CONSTANTS_KEY = #{constants_key}]]>
	</select>
	
	<select id="findConstantsValueByCsKey" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT A.CONSTANTS_VALUE FROM APP___CLM__DBUSER.T_CONSTANTS_INFO A WHERE A.CONSTANTS_KEY = #{constants_key}]]>
	</select>
	
		<select id="findConstantsDesc" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT A.CONSTANTS_DESC FROM APP___CLM__DBUSER.T_CONSTANTS_INFO A WHERE A.CONSTANTS_KEY = #{constants_key} AND A.CONSTANTS_VALUE = #{constants_value}]]>
	</select>
	
	<!-- 根据赔案和抄单日期删除对应抄单数据 -->	
	<delete id="deleteMasterByCaseIdAndDate" parameterType="java.util.Map">
		<![CDATA[ 
		
		DECLARE
	   		IN_CASE_ID NUMBER;
	   		IN_COPY_DATE DATE;
	 	BEGIN
		      IN_CASE_ID := #{case_id};
		      IN_COPY_DATE :=#{copy_date};
		  DELETE FROM APP___CLM__DBUSER.T_CONTRACT_MASTER a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_INSURED_LIST a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_PRODUCT a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_EXTEND a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_PRODUCT_OTHER a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_BENE a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_BENEFIT_INSURED a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_AGENT a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_POLICY_HOLDER a WHERE a.CUR_FLAG=0 and a.COPY_DATE=IN_COPY_DATE and a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CASE_HOLDER a WHERE a.log_id in ( select b.log_id 
				FROM APP___CLM__DBUSER.T_POLICY_HOLDER b 
				WHERE b.CUR_FLAG=0 and b.COPY_DATE=IN_COPY_DATE and b.CASE_ID = IN_CASE_ID);
     	END;
		]]>
	</delete>
	<!-- 根据赔案和抄单日期删除对应抄单数据 -->	
	<delete id="deleteMasterByCaseId" parameterType="java.util.Map">
		<![CDATA[ 
		
		DECLARE
	   		IN_CASE_ID NUMBER;
	 	BEGIN
		    IN_CASE_ID := #{case_id};
		    DELETE FROM APP___CLM__DBUSER.T_CONTRACT_MASTER a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_INSURED_LIST a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_PRODUCT a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_EXTEND a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_PRODUCT_OTHER a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_BENE a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_BENEFIT_INSURED a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CONTRACT_AGENT a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_POLICY_HOLDER a WHERE a.CASE_ID = IN_CASE_ID;
			DELETE FROM APP___CLM__DBUSER.T_CASE_HOLDER a WHERE a.log_id in ( select b.log_id 
				FROM APP___CLM__DBUSER.T_POLICY_HOLDER b 
				WHERE   b.CASE_ID = IN_CASE_ID);
     	END;
		]]>
	</delete>
	<select id="queryInformSituation" parameterType="java.util.Map"
		resultType="java.lang.Integer">
        <![CDATA[  SELECT COUNT(*) FROM APP___CLM__DBUSER.T_INSURED_LIST SL,APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER QC
       					WHERE SL.POLICY_CODE=QC.POLICY_CODE  AND QC.SURVEY_MODULE_RESULT LIKE '%是%' AND SL.CUR_FLAG=1 
       					  AND QC.QUESTIONAIRE_OBJECT='2'
                   ]]>
		 <if test=" customer_id != null and customer_id != '' ">
			    <![CDATA[AND SL.customer_id = #{customer_id }]]>
		 </if>
		 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and SL.policy_code = #{policy_code} ]]></if>	
		 <if test=" case_id != null and case_id != ''  "><![CDATA[ and SL.case_id = #{case_id} ]]></if>	
		 
	</select>
	<select id="queryInformSituationAll" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  SELECT COUNT(*) FROM APP___CLM__DBUSER.T_INSURED_LIST SL,APP___PAS__DBUSER.V_QUESTIONAIRE_CUSTOMER_ALL QC
       					WHERE SL.POLICY_CODE=QC.POLICY_CODE  AND QC.SURVEY_MODULE_RESULT LIKE '%是%'  AND SL.CUR_FLAG=1
       					  AND QC.QUESTIONAIRE_OBJECT='2'
                   ]]>
		 <if test=" customer_id != null and customer_id != '' ">
			    <![CDATA[AND SL.customer_id = #{customer_id }]]>
		 </if>
		 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and SL.policy_code = #{policy_code} ]]></if>	
		 <if test=" case_id != null and case_id != ''  "><![CDATA[ and SL.case_id = #{case_id} ]]></if>	
	</select>
	<select id="queryInformSituationCs" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  SELECT COUNT(*) FROM APP___CLM__DBUSER.T_INSURED_LIST SL,APP___PAS__DBUSER.T_cs_QUESTIONAIRE_CUSTOMER SC   
       				WHERE SL.POLICY_CODE=SC.POLICY_CODE  AND SC.SURVEY_MODULE_RESULT LIKE '%是%' AND SC.QUESTIONAIRE_OBJECT='2'
                   ]]>
	 	<if test=" customer_id != null and customer_id != '' ">
		    <![CDATA[AND SL.customer_id = #{customer_id }]]>
	    </if>
	    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and SL.policy_code = #{policy_code} ]]></if>		
	</select>
	<!-- 查询本保单年度内既往赔案的赔付 -->
	<select id="querySumActualPay" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT SUM(C.ACTUAL_PAY) ACTUAL_PAY
								  FROM APP___CLM__DBUSER.T_CLAIM_CASE     A,
								       APP___CLM__DBUSER.T_CLAIM_LIAB C
								 WHERE A.CASE_ID = C.CASE_ID
								 AND C.BUSI_PROD_CODE = #{busi_prod_code}         
								 AND C.CLAIM_TYPE = '08'
								 AND C.POLICY_CODE = #{policy_code}
								 AND C.LIAB_START_DATE = #{liab_start_date}
								 AND C.LIAB_END_DATE = #{liab_end_date}
								 AND A.CASE_STATUS = '80'
								 AND A.CASE_ID != #{case_id}]]>
	</select>
	<!-- 查询本保单年度内既往赔案宽限期内出险的赔付金额 -->
	<select id="queryGraceSumActualPay" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT SUM(C.ACTUAL_PAY) ACTUAL_PAY
								  FROM APP___CLM__DBUSER.T_CLAIM_CASE     A,
								       APP___CLM__DBUSER.T_CLAIM_LIAB C,
                       				   APP___CLM__DBUSER.T_CLAIM_SUB_CASE CA
								 WHERE A.CASE_ID = C.CASE_ID
								 AND CA.SUB_CASE_ID = C.SUB_CASE_ID
								 AND C.BUSI_PROD_CODE = #{busi_prod_code}         
								 AND C.CLAIM_TYPE = '08'
								 AND C.POLICY_CODE = #{policy_code}
								 AND C.LIAB_START_DATE =  #{liab_gracestart_date}
								 AND C.LIAB_END_DATE =  #{liab_graceend_date}
								 AND A.CASE_STATUS = '80'
								 AND CA.CLAIM_DATE >  #{liab_graceend_date} 
                 				 AND CA.CLAIM_DATE <  #{liab_end_date}
								 AND A.CASE_ID != #{case_id}]]>
	</select>
	<!-- 查询重大疾病是ZJ001，理赔类型是重大疾病或者特种疾病的赔案 -->
	<select id="findClaimCaseByDisease" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT DISTINCT A.CASE_ID,A.INSURED_ID,B.CLAIM_TYPE,B.CLAIM_DATE
               FROM DEV_CLM.T_CLAIM_CASE A, DEV_CLM.T_CLAIM_SUB_CASE B
              WHERE A.CASE_ID = B.CASE_ID
                AND A.SERIOUS_DISEASE = 'zj001'
                AND (B.CLAIM_TYPE = '03' OR B.CLAIM_TYPE = '10')
                AND A.CASE_ID =#{case_id}
	]]>
	</select>
	<!-- 查询既往赔案理赔类型是重大疾病或特种疾病的给付案件 -->
	<select id="findOldClaimCaseByDisease" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID,A.ACTUAL_PAY,B.CLAIM_TYPE
               FROM DEV_CLM.T_CLAIM_CASE A, DEV_CLM.T_CLAIM_SUB_CASE B
              WHERE A.CASE_ID = B.CASE_ID
                AND A.CASE_STATUS = 80
                AND (B.CLAIM_TYPE = '03' OR B.CLAIM_TYPE = '10')
                AND (A.AUDIT_DECISION ='1' OR A.AUDIT_DECISION ='2' OR A.AUDIT_DECISION ='3' OR A.AUDIT_DECISION ='6')
                AND A.CASE_ID != #{case_id}
                AND A.INSURED_ID = #{insured_id}
	]]>
	<if test=" serious_disease != null and serious_disease != '' ">
		<![CDATA[AND A.SERIOUS_DISEASE != #{serious_disease}]]>
	</if>
	</select >
	<!-- 查询赔案的保单信息按生效日期排序 -->
	<select id="findAllContractMasterDisease" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT A.CASE_ID, A.VALIDDATE_DATE, A.RERINSTATE_DATE, A.ORGAN_CODE
               from app___clm__dbuser.t_contract_master a
			 inner join app___clm__dbuser.t_insured_list d
			    on a.case_id = d.case_id
			   and a.policy_code = d.policy_code
			   and d.cur_flag = a.cur_flag
			   and d.copy_date = a.copy_date
			   and d.customer_id = #{customer_id}
			 inner join app___clm__dbuser.t_contract_busi_prod b
			    on a.case_id = b.case_id
			   and a.policy_code = b.policy_code
			   and b.cur_flag = a.cur_flag
			   and b.copy_date = a.copy_date
			 inner join app___clm__dbuser.t_business_product e
			    on b.busi_prod_code = e.product_code_sys
			   and e.product_category3 = '40006'
              WHERE A.CASE_ID =#{case_id}
                AND A.CUR_FLAG =#{cur_flag}
                AND A.LIABILITY_STATE = 1
           ORDER BY A.VALIDDATE_DATE DESC
	]]>
	</select>
	<!-- 重疾慰问先赔标识为是的赔案 -->
	<select id="findCaseByPriorityClaim" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT DISTINCT A.CASE_ID,A.CASE_NO,B.SURVEY_TYPE,C.PRIORITY_CLAIM
               FROM DEV_CLM.T_CLAIM_CASE        A,
                    DEV_CLM.T_SURVEY_APPLY      B,
                    DEV_CLM.T_SURVEY_CONCLUSION C
              WHERE A.CASE_ID = B.CASE_ID
                AND B.APPLY_ID = C.APPLY_ID
                AND A.CASE_NO = #{case_no}
	]]>
	<if test=" priority_claim != null"><![CDATA[ AND C.PRIORITY_CLAIM = #{priority_claim} ]]></if>	
	</select>
	<!-- 查询赔案的重疾慰问先赔标识 -->
	<select id ="findPriClaimByCaseId" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[SELECT DISTINCT A.CASE_ID,B.PRIORITY_CLAIM, A.APPLY_DATE
               FROM DEV_CLM.T_SURVEY_APPLY      A,
                    DEV_CLM.T_SURVEY_CONCLUSION B
              WHERE A.APPLY_ID = B.APPLY_ID
                AND A.CASE_ID = #{case_id}
           ORDER BY A.APPLY_DATE DESC
	]]>
	</select>
	<select id="findCaseAccident" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
	SELECT A.CASE_ID, B.ACC_COUNTRY_CODE
      FROM DEV_CLM.T_CLAIM_CASE A, DEV_CLM.T_CLAIM_ACCIDENT B
     WHERE A.ACCIDENT_ID = B.ACCIDENT_ID
       AND A.CASE_ID = #{case_id}
	]]>
	</select>
	<!-- 查询虚假发票 -->
	<select id="queryRiskMessage" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
		SELECT K.RISK_NAME, MAX(K.RISK_FRACTION) RISK_FRACTION
  			FROM DEV_CLM.T_CLAIM_RISK_LEVEL_LIAB K
		 WHERE CASE_ID = #{case_id}
 		GROUP BY K.RISK_NAME
 		UNION
 		SELECT '代理人层风险聚集' RISK_NAME,
		       TO_NUMBER(MAX(A.FRMS_RULE_SCORE)) RISK_FRACTION
		  FROM APP___CLM__DBUSER.T_CLAIM_CHEAT_ATLAS A
		 WHERE A.CASE_NO = #{case_no}
		   AND A.FRMS_RULE_CODE IN (${agent_risk_name})
		 UNION
		SELECT '客户层风险聚集' RISK_NAME,
		       TO_NUMBER(MAX(A.FRMS_RULE_SCORE)) RISK_FRACTION
		  FROM APP___CLM__DBUSER.T_CLAIM_CHEAT_ATLAS A
		 WHERE A.CASE_NO = #{case_no}
		   AND A.FRMS_RULE_CODE IN (${cus_risk_name})
		   
	]]>
	</select>
	<!-- 查询已结案量 -->
	<select id="queryYetCloseCount" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) AS YET_CLOSE_CASE FROM (
					SELECT DISTINCT C.CASE_NO FROM DEV_CLM.T_CLAIM_CASE C
						WHERE 1=1 AND C.CASE_STATUS in ('80','90') AND C.RELATED_NO IS NULL
						AND C.ACCEPT_DECISION = 1
						AND C.END_CASE_TIME >= TO_DATE(#{start_time},'yyyy-mm-dd hh24:mi:ss')
						AND C.END_CASE_TIME <= TO_DATE(#{end_time},'yyyy-mm-dd hh24:mi:ss')
		]]>
			<include refid="queryYetCloseCountConditon"/>)
	</select>
	<!-- 查询回退结案量 -->
	<select id="queryYetCloseRedoCount" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) AS YET_CLOSE_REDO_CASE FROM (
					SELECT DISTINCT C.CASE_NO FROM DEV_CLM.T_CLAIM_CASE C
						WHERE 1=1 AND C.CASE_STATUS in ('80','90') AND C.RELATED_NO IS NOT NULL
						AND C.ACCEPT_DECISION = 1
						AND C.END_CASE_TIME >= TO_DATE(#{start_time},'yyyy-mm-dd hh24:mi:ss')
						AND C.END_CASE_TIME <= TO_DATE(#{end_time},'yyyy-mm-dd hh24:mi:ss')
		]]>
			<include refid="queryYetCloseCountConditon"/>)
	</select>
	
	<sql id="queryYetCloseCountConditon">
		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(C.ORGAN_CODE,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND (C.AUDITOR_ID = #{acceptor_id} or C.APPROVER_ID = #{acceptor_id}
				or C.EASY_AUDITOR_ID = #{acceptor_id})]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(C.ORGAN_CODE, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND (C.AUDITOR_ID IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
               		OR C.APPROVER_ID IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
               		OR C.EASY_AUDITOR_ID IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
               		)
				]]>
			</if>
		</if>
	</sql>
	<!-- 查询已质检量 -->
	<select id="queryYetQualityCount" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) AS YET_QUALITY FROM (
				SELECT DISTINCT CAT.CASE_NO FROM DEV_CLM.T_CLAIM_AFC_TASK CAT,DEV_CLM.T_CLAIM_CASE CC
              	WHERE 1=1 AND CC.CASE_NO=CAT.CASE_NO AND CAT.QC_STATUS = 2
            		AND CAT.CHECK_DATE >= TO_DATE(#{start_time},'yyyy-mm-dd hh24:mi:ss')
            		AND CAT.CHECK_DATE <= TO_DATE(#{end_time},'yyyy-mm-dd hh24:mi:ss')
		]]>
			<include refid="queryQualityCountConditon"/>)
	</select>
	<!-- 待质检量 -->
	<select id="queryWaitQualityCount" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) AS WAIT_QUALITY FROM (
				SELECT DISTINCT CAT.CASE_NO FROM DEV_CLM.T_CLAIM_AFC_TASK CAT,DEV_CLM.T_CLAIM_CASE CC
              	WHERE 1=1 AND CC.CASE_NO=CAT.CASE_NO AND CAT.QC_STATUS IN (0,1)
            		AND CAT.MAKE_DATE >= TO_DATE(#{start_time},'yyyy-mm-dd hh24:mi:ss')
            		AND CAT.MAKE_DATE <= TO_DATE(#{end_time},'yyyy-mm-dd hh24:mi:ss')
		]]>
			<include refid="queryQualityCountConditon"/>)
	</select>
	<sql id="queryQualityCountConditon">
		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(CC.ORGAN_CODE,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND CAT.CHECK_BY = #{acceptor_id}]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(CC.ORGAN_CODE, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND CAT.CHECK_BY IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
				]]>
			</if>
		</if>
	</sql>
	
	<!-- 已检查量 -->
	<select id="queryYetCheckCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1) AS YET_CHECK FROM (                                 	
                    SELECT DISTINCT A.CASE_NO
				         FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK      A,
				              APP___CLM__DBUSER.T_CLAIM_CASE              B,
				              APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK_POINT C
				        WHERE 1 = 1
				          AND A.CASE_NO = B.CASE_NO
				          AND A.TASK_STATUS IN ('3','4','5')
				          AND C.TASK_ID = A.TASK_ID
				          AND A.CASE_NO IS NOT NULL
				          AND C.INSPECT_TIME >=
				              TO_DATE(#{start_time}, 'yyyy-mm-dd hh24:mi:ss')
				          AND C.INSPECT_TIME <=
				              TO_DATE(#{end_time}, 'yyyy-mm-dd hh24:mi:ss') 
			
			]]>

		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(B.ORGAN_CODE,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND C.INSPECT_USER = #{acceptor_id}]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(B.ORGAN_CODE, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND C.INSPECT_USER IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
				]]>
			</if>
		</if>
		<![CDATA[	              
				        UNION
				        
				        SELECT DISTINCT A.CASE_NO
				         FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK      A,
				              APP___CLM__DBUSER.T_CLAIM_INSPECT_CONDITION B,
				              APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK_POINT C
				        WHERE 1 = 1
				          AND A.TASK_STATUS IN ('3','4','5')
				          AND A.INSPECT_CONDITION_ID = B.INSPECT_CONDITION_ID
				          AND C.TASK_ID = A.TASK_ID
				          AND A.CASE_NO IS NULL
				          AND C.INSPECT_TIME >=
				              TO_DATE(#{start_time}, 'yyyy-mm-dd hh24:mi:ss')
				          AND C.INSPECT_TIME <=
				              TO_DATE(#{end_time}, 'yyyy-mm-dd hh24:mi:ss')              	                                
		]]>
		
		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(B.ORG_CODES,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND C.INSPECT_USER = #{acceptor_id}]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(B.ORG_CODES, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND C.INSPECT_USER IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
				]]>
			</if>
		</if>			
			)
	</select>
	
	<!-- 待检查量 -->
	<select id="queryWaitCheckCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1) AS WAIT_CHECK FROM (
       				SELECT DISTINCT A.CASE_NO
				         FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK      A,
				              APP___CLM__DBUSER.T_CLAIM_CASE              B,
				              APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK_POINT C
				        WHERE 1 = 1
				          AND A.CASE_NO = B.CASE_NO
				          AND A.TASK_STATUS IN ('1','2')
				          AND C.TASK_ID = A.TASK_ID
				          AND A.CASE_NO IS NOT NULL
				          AND C.INSPECT_TIME >=
				              TO_DATE(#{start_time}, 'yyyy-mm-dd hh24:mi:ss')
				          AND C.INSPECT_TIME <=
				              TO_DATE(#{end_time}, 'yyyy-mm-dd hh24:mi:ss')
		]]>		              
		
		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(B.ORGAN_CODE,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND C.INSPECT_USER = #{acceptor_id}]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(B.ORGAN_CODE, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND C.INSPECT_USER IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
				]]>
			</if>
		</if> 
		<![CDATA[		              
				        UNION
				        
				        SELECT DISTINCT A.CASE_NO
				         FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK      A,
				              APP___CLM__DBUSER.T_CLAIM_INSPECT_CONDITION B,
				              APP___CLM__DBUSER.T_CLAIM_INSPECT_TASK_POINT C
				        WHERE 1 = 1
				          AND A.TASK_STATUS IN ('1','2')
				          AND A.INSPECT_CONDITION_ID = B.INSPECT_CONDITION_ID
				          AND C.TASK_ID = A.TASK_ID
				          AND A.CASE_NO IS NULL
				          AND C.INSPECT_TIME >=
				              TO_DATE(#{start_time}, 'yyyy-mm-dd hh24:mi:ss')
				          AND C.INSPECT_TIME <=
				              TO_DATE(#{end_time}, 'yyyy-mm-dd hh24:mi:ss')	
		]]>

		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(B.ORG_CODES,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND C.INSPECT_USER = #{acceptor_id}]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(B.ORG_CODES, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND C.INSPECT_USER IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
				]]>
			</if>
		</if>
		
			)
	</select>
	<sql id="queryCheckCountConditon">
		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(B.ORGAN_CODE,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND D.PRO_MAKE_USER = #{acceptor_id}]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(A.ORGAN_CODE, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND D.PRO_MAKE_USER IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
				]]>
			</if>
		</if>
	</sql>
	
	
	<!-- 审核中案件量 -->
	<select id="queryCourseAuditCount" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) AS COURSE_AUDIT FROM (
					SELECT DISTINCT C.CASE_NO FROM DEV_CLM.T_CLAIM_CASE C
						WHERE (CASE WHEN C.AUDIT_START_TIME IS NOT NULL THEN C.AUDIT_START_TIME 
                        			WHEN C.AUDIT_TIME IS NOT NULL THEN C.AUDIT_TIME ELSE C.AUDIT_INDIVIDUAL_POOL_TIME END)
                  			BETWEEN TO_DATE(#{start_time},'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(#{end_time},'yyyy-mm-dd hh24:mi:ss')
            				AND C.CASE_STATUS = '61'
		]]>
			<include refid="taskProblemCheckListCondition"/>)
	</select>
	
	<!-- 审批中案件量 -->	
	<select id="queryCouresApproveCount" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) AS COURES_APPROVE FROM (
					SELECT DISTINCT C.CASE_NO FROM DEV_CLM.T_CLAIM_CASE C
						WHERE (CASE WHEN C.APPROVE_INDIVIDUAL_POOL_TIME IS NOT NULL THEN C.APPROVE_INDIVIDUAL_POOL_TIME ELSE C.APPROVE_TIME END)
            			BETWEEN TO_DATE(#{start_time},'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(#{end_time},'yyyy-mm-dd hh24:mi:ss')
            			AND C.CASE_STATUS = '71'
		]]>
		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(C.ORGAN_CODE,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND C.APPROVER_ID = #{acceptor_id}]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(C.ORGAN_CODE, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND C.APPROVER_ID IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
				]]>
			</if>
		</if>
			)
	</select>
	
	<!-- 查询审核中问题件信息 -->
	<select id="taskProblemCheckList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT DISTINCT C.CASE_NO CASE_NO,
				LISTAGG(SA.SURVEY_STATUS,',') WITHIN GROUP (ORDER BY SA.SURVEY_STATUS) SURVEY_STATUS,
    			LISTAGG(U.UW_STATUS,',') WITHIN GROUP (ORDER BY U.UW_STATUS) UW_STATUS,
       			LISTAGG(SD.SUGGESTION_CODE,',') WITHIN GROUP (ORDER BY SD.SUGGESTION_CODE) SUGGESTION_CODE,
       			LISTAGG(CC.SUP_FALG,',') WITHIN GROUP (ORDER BY CC.SUP_FALG) SUP_FALG,
       			LISTAGG(C.ADVANCE_FLAG,',') WITHIN GROUP (ORDER BY C.ADVANCE_FLAG) ADVANCE_FLAG,
          		LISTAGG(SS.DISCUSS_STATUS,',') WITHIN GROUP (ORDER BY SS.DISCUSS_STATUS) DISCUSS_FLAG,
          		LISTAGG(TT.TREATY_STATUS,',') WITHIN GROUP (ORDER BY TT.TREATY_STATUS) TREATY_FLAG
          		FROM APP___CLM__DBUSER.T_CLAIM_CASE C
          		LEFT JOIN APP___CLM__DBUSER.T_SURVEY_APPLY SA ON C.CASE_ID = SA.CASE_ID
          		LEFT JOIN APP___CLM__DBUSER.T_CLAIM_UW U ON C.CASE_ID = U.CASE_ID
          		LEFT JOIN APP___CLM__DBUSER.T_RI_SUGGEST_DETAIL SD ON C.CASE_ID = SD.CASE_ID AND SD.STANDARD_CODE = 1
          		LEFT JOIN APP___CLM__DBUSER.T_CLAIM_CHECKLIST CC ON C.CASE_ID = CC.CASE_ID AND CC.IS_CHECKLIST_MEMO IS NOT NULL
          		LEFT JOIN APP___CLM__DBUSER.T_CLAIM_DISCUSS SS ON C.CASE_ID = SS.CASE_ID AND SS.DISCUSS_STATUS != 2
          		LEFT JOIN APP___CLM__DBUSER.T_CLAIM_TREATY_TALK TT ON C.CASE_ID = TT.CASE_ID AND TT.TREATY_STATUS != 2
          		WHERE (CASE WHEN C.AUDIT_START_TIME IS NOT NULL THEN C.AUDIT_START_TIME 
                        	WHEN C.AUDIT_TIME IS NOT NULL THEN C.AUDIT_TIME ELSE C.AUDIT_INDIVIDUAL_POOL_TIME END)
                  		BETWEEN TO_DATE(#{start_time},'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(#{end_time},'yyyy-mm-dd hh24:mi:ss')
            		AND C.CASE_STATUS = '61'
          		]]>
          		<include refid="taskProblemCheckListCondition"/>
          		<![CDATA[GROUP BY C.CASE_NO]]>
	</select>
	
	<select id="suggestionCheckList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT SD.SUGGESTION_CODE SUGGESTION_CODE,SD.CASE_NO CASE_NO
      			FROM APP___CLM__DBUSER.T_RI_SUGGEST_DETAIL SD WHERE SD.STANDARD_CODE = 1 
      			AND SD.CASE_NO = #{case_no} 
		]]>
	</select>
	<sql id="taskProblemCheckListCondition">
		<if test=" organ_code != null and organ_code != '' ">
			<![CDATA[AND instr(C.ORGAN_CODE,#{organ_code}) > 0]]>
		</if>
		<if test=" acceptor_id != null and acceptor_id != '' ">
			<![CDATA[AND (C.Auditor_Id = #{acceptor_id} or C.EASY_AUDITOR_ID = #{acceptor_id})]]>
		</if>
		<if test=" area != null and area != '' ">
			<if test=" monitoring_scope != null and monitoring_scope != '' and '02'.toString() == monitoring_scope ">
				<![CDATA[AND SUBSTR(C.ORGAN_CODE, 0, 4) IN (SELECT ORGAN.ORGAN_CODE
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_ORGAN ORGAN
               		WHERE CA.AREA_CODE=ORGAN.AREA_CODE AND CA.AREA_CODE = #{area})
               	]]>
			</if>
			<if test=" monitoring_scope != null and monitoring_scope != '' and '04'.toString() == monitoring_scope ">
				<![CDATA[AND (C.AUDITOR_ID IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
               		OR C.EASY_AUDITOR_ID IN (SELECT P.USER_ID
               		FROM DEV_CLM.T_CLAIM_AREA CA,DEV_CLM.T_CLAIM_AREA_PERSON P
               		WHERE CA.AREA_CODE=P.AREA_CODE AND CA.AREA_CODE = #{area})
               		)
				]]>
			</if>
		</if>
	</sql>
	<!-- add by zhaoxx_wb电子签名轨迹分页总数 -->
	<select id="findAllSignatureTraceTotal" parameterType="java.util.Map" resultType="java.lang.Integer"> 
    <![CDATA[SELECT COUNT(*) FROM DEV_CLM.T_CLAIM_SIGNATURE_TRACE A LEFT JOIN DEV_CLM.T_CLAIM_CASE B 
             ON A.CASE_NO = B.CASE_NO  WHERE   B.CASE_ID = #{case_id}]]>		
	</select>
	<!-- add by zhaoxx_wb电子签名轨迹分页 -->
	<select id="findAllSignatureTrace" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
	         SELECT A.TRACE_NUM, A.CASE_NO, 
             (SELECT C.USER_TYPE_NAME FROM DEV_CLM.T_SIGNATURE_USER_TYPE C WHERE C.USER_TYPE_CODE = A.SIGNATURE_USER_TYPE ) AS SIGNATURE_USER_TYPE, 
             A.USER_ID,A.SIGNATURE_IDENTIFICATION,A.SIGNATURE_TIME,B.CASE_ID 
             FROM DEV_CLM.T_CLAIM_SIGNATURE_TRACE A LEFT JOIN DEV_CLM.T_CLAIM_CASE B 
             ON A.CASE_NO = B.CASE_NO  
             WHERE B.CASE_ID = #{case_id}
             ORDER BY A.TRACE_NUM
	]]>
	</select>
	<!-- add by zhaoxx_wb电子签名轨迹数据查询置灰操作 -->
	<select id="querySignatureLines" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
	         SELECT A.TRACE_NUM, A.CASE_NO,A.USER_ID,A.SIGNATURE_IDENTIFICATION,A.SIGNATURE_TIME FROM DEV_CLM.T_CLAIM_SIGNATURE_TRACE A LEFT JOIN DEV_CLM.T_CLAIM_CASE B 
             ON A.CASE_NO = B.CASE_NO  WHERE   B.CASE_ID = #{case_id}
	]]>
	</select>
	<!-- 理赔黑名单信息查询 -->
	<select id="queryBlackNameInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.BLACK_NAME_TYPE,
                  TO_CHAR(A.CUSTOMER_ID) AGENT_CODE,
                  A.BLACK_NAME,
                  A.BLACK_NAME_REASON,
                  A.ESTABLISH_TIME,
                  A.ESTABLISH_PERSON
                  FROM DEV_CLM.T_CLAIM_BLACK_NAME A
                  JOIN (SELECT  DISTINCT CUS.CUSTOMER_ID,CUS.CUSTOMER_NAME,CUS.CUSTOMER_CERTI_CODE
                        FROM DEV_CLM.T_POLICY_HOLDER POL
                        JOIN DEV_CLM.T_CUSTOMER CUS
                        ON POL.CUSTOMER_ID = CUS.CUSTOMER_ID
                        WHERE POL.CASE_ID = #{case_id}
                        UNION
                        SELECT  DISTINCT CUS.CUSTOMER_ID,CUS.CUSTOMER_NAME,CUS.CUSTOMER_CERTI_CODE
                        FROM DEV_CLM.T_INSURED_LIST ins
                        JOIN DEV_CLM.T_CUSTOMER CUS
                        ON INS.CUSTOMER_ID = CUS.CUSTOMER_ID
                        WHERE INS.CASE_ID = #{case_id}) B
                  ON A.BLACK_NAME=B.CUSTOMER_NAME AND A.BLACK_CERTI_CODE=B.CUSTOMER_CERTI_CODE
                  WHERE A.BLACK_NAME_TYPE = '01'
                  UNION ALL
                  SELECT A.BLACK_NAME_TYPE,
                  A.AGENT_CODE,
                  A.BLACK_NAME,
                  A.BLACK_NAME_REASON,
                  A.ESTABLISH_TIME,
                  A.ESTABLISH_PERSON
                  FROM DEV_CLM.T_CLAIM_BLACK_NAME A
                  WHERE A.AGENT_CODE IN (                  
                      SELECT  DISTINCT  CON.AGENT_CODE FROM DEV_CLM.T_CONTRACT_AGENT CON 
                      WHERE  CON.CUR_FLAG='1'                  
                      AND CON.CASE_ID = #{case_id}                  
                  )
                  AND A.BLACK_NAME_TYPE = '02'	
		]]>
	</select>
	
	<select id="findAllClaimPayeeName" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		        SELECT A.PAY_MOLE,A.PAY_DENO,B.PAYEE_NAME
				  FROM DEV_CLM.T_CLAIM_PAY A, DEV_CLM.T_CLAIM_PAYEE B
				 WHERE A.CASE_ID = B.CASE_ID
				   AND A.PAYEE_ID = B.PAYEE_ID
				   AND A.CASE_ID = #{case_id}
				   AND A.POLICY_CODE = #{policy_code}
				   AND A.BUSI_PROD_CODE = #{busi_prod_code}
		]]>
	</select>
	
	<!-- 查询当前险种的法人受益人名称 -->
	<select id="findBeneLegalPerson" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		SELECT  B.COMPANY_NAME AS UNITNAME FROM DEV_CLM.T_CONTRACT_BENE A,DEV_PAS.T_TRUST_COMPANY B
				WHERE A.BENE_KIND='02'
				AND A.CUR_FLAG = '1'
				AND A.COMPANY_ID = B.COMPANY_ID
				   AND A.CASE_ID = #{case_id}
				   AND A.POLICY_ID = #{policy_id}
				   AND A.POLICY_CODE = #{policy_code}
				   AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	
	<!-- 查询赔案类型为结案 并且理赔现价计算表中调用场景不为结案的 案件 -->
	<select id="findAllCalculateStorageBatch" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT M.CASE_ID from (
		SELECT DISTINCT A.CASE_ID, A.CASE_NO
          FROM APP___CLM__DBUSER.T_CLAIM_CASE A
         inner join APP___CLM__DBUSER.T_CLAIM_CASHVALUE B
            on A.CASE_ID = B.CASE_ID
         WHERE A.CASE_STATUS = '80'
           AND B.CASE_ID NOT IN
               (SELECT N.CASE_ID
                  FROM APP___CLM__DBUSER.T_CLAIM_CASHVALUE N
                 WHERE N.CALL_SCENARIO = '2')]]>
               <if test="case_no != null and case_no != '' "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
               <![CDATA[ ) M
      WHERE MOD(M.CASE_ID,#{modNum}) = #{start}
			]]>
	</select>
	
	
	<!-- 查询赔案下的保单主险险种分类 -->
	<select id="findContractBusiProdForRealName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,A.POLICY_ID,A.BUSI_ITEM_ID,A.MATURITY_DATE,A.VALIDATE_DATE   
         ,B.PRODUCT_CATEGORY2
           FROM APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD A,APP___CLM__DBUSER.T_BUSINESS_PRODUCT B 
           WHERE ROWNUM <=  1000 
           AND A.MASTER_BUSI_ITEM_ID IS NULL
           AND CUR_FLAG = 1
          AND A.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS  
       		AND A.CASE_ID = #{case_id} ]]>
		<![CDATA[ ORDER BY A.MATURITY_DATE DESC]]> 
	</select>
	
	<!-- 查询直连11位流水号操作 -->
	<select id="findDirectConnRandom" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ SELECT TO_CHAR(DEV_CLM.S_DIRECT_CONN_RANDOM.NEXTVAL) AS REQUEST_ID FROM DUAL ]]>
	</select>

<!-- 查询领款人受益分配金额信息（追加付款） -->
	<select id="findRedoPayeeByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.CASE_ID,
                SUM(DECODE(B.ARAP_CODE, '01', -1, 1) * A.PAY_AMOUNT) NEW_PAY,
                C.PAYEE_NO,C.PAY_MODE,
                  c.payee_name  
          FROM  DEV_CLM.T_CLAIM_PAY         A,
                DEV_CLM.T_CLAIM_FEE_MAPPING B,
                DEV_CLM.T_CLAIM_PAYEE       C,
                DEV_CLM.T_CLAIM_ADJUST_BUSI E,
                DEV_CLM.T_CLAIM_CASE        F ]]>
                <if test="redo != null"><![CDATA[
				,(select DISTINCT G.business_code,G.UNIT_NUMBER,G.FEE_STATUS FROM DEV_CLM.t_Prem_Arap G
                WHERE 1=1  ]]>
                <if test="case_no != null and case_no != '' ">
                <![CDATA[ AND G.business_code = #{case_no} ]]>
                </if>
              <![CDATA[    AND G.FEE_STATUS='01'
                ) H
			 ]]>
			 </if>
        <![CDATA[  WHERE A.ADJUST_BUSI_ID = E.ADJUST_BUSI_ID(+)
           AND E.ADJUST_TYPE = B.CLM_FEE_CODE(+)
           AND A.PAYEE_ID = C.PAYEE_ID
           AND A.CASE_ID = F.CASE_ID
            AND A.ADVANCE_FLAG!=1
           AND A.CASE_ID =#{case_id}  ]]>
           <if test="redo != null">
           <![CDATA[ AND A.UNIT_NUMBER = H.UNIT_NUMBER
           AND H.FEE_STATUS='01'
           ]]></if>
         <![CDATA[ GROUP BY A.CASE_ID, C.PAYEE_NO,C.PAY_MODE, F.CASE_NO,c.payee_name
      ]]>
	 
	
</select>
<!-- 查询已支付赔案已支付数据 -->
	<select id="findActualPayByCaseNo" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ select B.CASE_ID, 
					       SUM(DECODE(A.ARAP_FLAG, '2', A.FEE_AMOUNT, 0-A.FEE_AMOUNT)) NEW_PAY,
					       A.CUSTOMER_ID PAYEE_NO, 
					       A.PAYEE_NAME
					  from DEV_CLM.T_PREM_ARAP A
					 inner join DEV_CLM.T_CLAIM_CASE B
					    on A.BUSINESS_CODE = B.CASE_NO
					 where a.fee_type not in ('P005110000','G005130000') and a.fee_status='01' and A.BUSINESS_CODE = #{case_no}
					 group by B.CASE_ID, A.CUSTOMER_ID, A.BUSINESS_CODE, A.PAYEE_NAME]]>
	</select>
<!-- 查询实名查验是否通过信息 -->
	<select id="findAllClaimRealnameCheckPO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_CERT_NO, A.AUTH_RESULT, A.TRANSACTION_ID, A.BUS_SRE_NAME, A.CHECK_TIMES, A.SECOND_INSU_INST_BRAN_CODE, A.BUS_SRE_ID_CARD, 
			A.RECHECK_FLAG, A.ROLE_TYPE, A.CHECK_CERT_START, A.BUS_NO, A.SUBMIT_CHANNEL, A.REALNAME_CHECK_ID, A.BUS_SRE_TYPE_CODE, 
			A.AUTH_BATCH_NO, A.CHECK_NAME, A.CHECK_TIME, A.SOURCE_CODE, A.POLICY_NO, A.BUSINESS_RESULT, A.BUSINESS_RESULT_DETAIL, 
			A.SOURCE_INFO, A.VALI_DATE, A.CHECK_VALI_DATE, A.CHECK_CERT_TYPE_CODE, A.BUS_SRE_PCNAME, A.COMPANY_CODE, 
			A.AUTH_TIME, A.RES_CODE, A.UPDATE_DATE, A.UPDATE_DAY, A.BUS_SRE_PHONE, A.CHECK_OPERATOR, A.BUS_SRE_DEPT_ADDR, 
			A.CHECK_CERT_END, A.BUSINESS_NODE, A.SERVICE_CODE, A.CHECK_OPERATOR_ORG, A.SUP_PREF_CODE, A.CLASS_CODE, 
			A.AUTHENTICATION_ID, A.BUS_SRE_IP, A.CHANNEL_CODE, A.THIRD_INSU_INST_BRAN_CODE, A.BUS_SRE_DEPT_CODE, A.POLICY_ORG, 
			A.BUS_SRE_PCIP, A.BUS_SRE_PCMAC, A.CHECK_CODE, A.BUS_LINK_CODE, A.CHECK_SOURCE FROM APP___CLM__DBUSER.T_REALNAME_CHECK A WHERE ROWNUM <=  1000  ]]>
		 <if test=" check_cert_no != null and check_cert_no != ''  "><![CDATA[ AND A.CHECK_CERT_NO = #{check_cert_no} ]]></if>
		<if test=" bus_no != null and bus_no != ''  "><![CDATA[ AND A.BUS_NO = #{bus_no} ]]></if>
		<if test=" check_name != null and check_name != ''  "><![CDATA[ AND A.CHECK_NAME = #{check_name} ]]></if>
		<if test=" policy_no != null and policy_no != ''  "><![CDATA[ AND A.POLICY_NO = #{policy_no} ]]></if>
		<if test=" check_cert_type_code != null and check_cert_type_code != ''  "><![CDATA[ AND A.CHECK_CERT_TYPE_CODE = #{check_cert_type_code} ]]></if>
		<if test=" check_cert_start  != null  and  check_cert_start  != ''  "><![CDATA[ AND A.CHECK_CERT_START = #{check_cert_start} ]]></if>
		<if test=" check_cert_end  != null  and  check_cert_end  != ''  "><![CDATA[ AND A.CHECK_CERT_END = #{check_cert_end} ]]></if>
		<if test=" check_code != null and check_code != ''  "><![CDATA[ AND A.CHECK_CODE = #{check_code} ]]></if>
		<![CDATA[ ORDER BY A.AUTH_BATCH_NO DESC ]]> 
</select>
<!-- 查询赔案实名查验信息 -->
	<select id="findRealNameChecks" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_CERT_NO, A.AUTH_RESULT, A.TRANSACTION_ID, A.BUS_SRE_NAME, A.CHECK_TIMES, A.SECOND_INSU_INST_BRAN_CODE, A.BUS_SRE_ID_CARD, 
      A.RECHECK_FLAG, A.ROLE_TYPE, A.CHECK_CERT_START, A.BUS_NO, A.SUBMIT_CHANNEL, A.REALNAME_CHECK_ID, A.BUS_SRE_TYPE_CODE, 
      A.AUTH_BATCH_NO, A.CHECK_NAME, A.CHECK_TIME, A.SOURCE_CODE, A.POLICY_NO, A.BUSINESS_RESULT, A.BUSINESS_RESULT_DETAIL, 
      A.SOURCE_INFO, A.VALI_DATE, A.CHECK_VALI_DATE, A.CHECK_CERT_TYPE_CODE, A.BUS_SRE_PCNAME, A.COMPANY_CODE, 
      A.AUTH_TIME, A.RES_CODE, A.UPDATE_DATE, A.UPDATE_DAY, A.BUS_SRE_PHONE, A.CHECK_OPERATOR, A.BUS_SRE_DEPT_ADDR, 
      A.CHECK_CERT_END, A.BUSINESS_NODE, A.SERVICE_CODE, A.CHECK_OPERATOR_ORG, A.SUP_PREF_CODE, A.CLASS_CODE, 
      A.AUTHENTICATION_ID, A.BUS_SRE_IP, A.CHANNEL_CODE, A.THIRD_INSU_INST_BRAN_CODE, A.BUS_SRE_DEPT_CODE, A.POLICY_ORG, 
      A.BUS_SRE_PCIP, A.BUS_SRE_PCMAC, A.CHECK_CODE, A.BUS_LINK_CODE, A.CHECK_SOURCE FROM APP___CLM__DBUSER.T_REALNAME_CHECK A 
      inner join (
		select CHECK_NAME,CHECK_CERT_TYPE_CODE,CHECK_CERT_NO,CHECK_CERT_START,CHECK_CERT_END,
		max(AUTH_BATCH_NO)as AUTH_BATCH_NO from dev_clm.T_REALNAME_CHECK rc where rc.BUS_NO=#{bus_no}
		group by CHECK_NAME,CHECK_CERT_TYPE_CODE,CHECK_CERT_NO,CHECK_CERT_START,CHECK_CERT_END
		)bs on bs.CHECK_NAME=a.CHECK_NAME and
		bs.CHECK_CERT_TYPE_CODE=a.CHECK_CERT_TYPE_CODE and
		bs.CHECK_CERT_NO=a.CHECK_CERT_NO and
		bs.CHECK_CERT_START=a.CHECK_CERT_START and
		bs.CHECK_CERT_END=a.CHECK_CERT_END and
		bs.AUTH_BATCH_NO = a.AUTH_BATCH_NO
      WHERE ROWNUM <=  1000 ]]>
		<![CDATA[ ORDER BY A.AUTH_BATCH_NO DESC ]]> 
</select>
<!-- 查询实名查验码表信息 -->
	<select id="findCodeNameByType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CODE_TYPE, A.TYPE_NAME, A.CODE_NAME, A.CODE_VALUE
		 FROM APP___CLM__DBUSER.T_REALNAME_CODE A WHERE ROWNUM <=  1000  ]]>
		 <![CDATA[ AND A.CODE_TYPE = #{code_type}
		 		AND A.CODE_VALUE = #{code_value} ]]>
</select>
<!-- 如果支付方式为网上银行时，则只查t_bank表 ，不关联机构-->
	<select id="CLM_queryBankNotOrg" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[ SELECT ROWNUM RN,A.BANK_CODE,A.BANK_NAME FROM (
    	SELECT DISTINCT BANK.BANK_CODE,BANK.BANK_NAME FROM APP___CLM__DBUSER.T_BANK BANK
    	WHERE BANK.BANK_NAME NOT LIKE ('%信用卡%')
       		  AND BANK.IS_CREDIT_CARD = '0'
    	]]>
		<if test="pay_mode != null and pay_mode != '' ">
			<![CDATA[ AND BANK.PAY_MODE like '%${pay_mode}%' ]]>
		</if>
		<![CDATA[) A]]>
	</select>
	
<!--  查询首次投保生效日期-->
	<select id="findFirstInsureDate" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT
      			   connect_by_root(nvl(A.INITIAL_VALIDATE_DATE,A.VALIDDATE_DATE))INITIAL_VALIDATE_DATE
      			   FROM APP___CLM__DBUSER.T_CONTRACT_MASTER A 
      			   WHERE A.CUR_FLAG = 1 
      	]]>
      	<if test="policy_code != null and policy_code != '' ">
			<![CDATA[ AND A.POLICY_CODE =#{policy_code ,jdbcType=VARCHAR} ]]>
		</if>
		<![CDATA[ START WITH A.FORMER_ID IS  NULL  CONNECT BY PRIOR A.POLICY_ID = A.FORMER_ID ]]>
	</select>
	
	<!--  查询符合实名查验预警的信息数量-->
	<select id="findClaimRealnameCheckPOList" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 	SELECT COUNT(1) FROM (
       SELECT COUNT(1) NUM FROM APP___CLM__DBUSER.T_REALNAME_CHECK A
       WHERE A.BUS_LINK_CODE='201' AND A.RES_CODE is not null AND A.RES_CODE != '200' AND trunc(A.INSERT_TIME)=trunc(SYSDATE)
       GROUP BY A.CHECK_NAME,A.CHECK_CERT_TYPE_CODE,A.CHECK_CERT_NO,A.CHECK_CERT_START,A.CHECK_CERT_END)B WHERE B.NUM>=3
         ]]>
	</select>
	<!--  查询已发送实名查验预警邮件的数量-->
	<select id="findSendContentLogList" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_SEND_CONTENT_LOG A WHERE 1=1
		AND A.NOTICE_ID='411' AND trunc(A.INSERT_TIME)=trunc(SYSDATE)
		]]>
	</select>

	<!-- 判断赔案下全部赔付金额支付成功-->
	<select id="findAllPayPremArapByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		SELECT (SELECT count(*) FROM APP___CLM__DBUSER.T_PREM_ARAP  PA WHERE PA.BUSINESS_CODE=A.CASE_NO  AND PA.ARAP_FLAG=2 
		AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS PAY_COUNT,
		(SELECT COUNT(*) FROM  APP___CLM__DBUSER.T_PREM_ARAP PA WHERE PA.BUSINESS_CODE=A.CASE_NO AND  PA.ARAP_FLAG=2 AND PA.FEE_STATUS!='01' 
		AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS UN_PAY_COUNT
 		FROM APP___CLM__DBUSER.T_CLAIM_CASE A WHERE ROWNUM <=  1000
		AND A.CASE_NO = #{business_code}  AND A.CASE_STATUS='80']]> 
	</select>
	<!-- 查询赔案下所有受益人信息 -->
	<select id="findAllClaimBeneByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_END, A.BENE_NATION, A.BENE_NAME, A.CASE_ID, 
			A.BENE_CERTI_TYPE, A.BENE_ID, A.BENE_CERTI_START, A.BENE_RELATION, A.BENE_BIRTH, 
			A.BENE_CERTI_NO, A.BENE_SEX, A.BENE_NO,
			A.BENE_JOB_CODE, A.BENE_PHONE, A.BENE_PROVINCE, A.BENE_CITY, A.BENE_DISTRICT, A.BENE_ADDRESS,A.LEGAL_PERSON_ID
			FROM APP___CLM__DBUSER.T_CLAIM_BENE A,APP___CLM__DBUSER.T_CLAIM_PAY B WHERE ROWNUM <=  1000  
		AND A.BENE_ID = B.BENE_ID
		AND B.CASE_ID = #{case_id} ]]>
		<![CDATA[ ORDER BY A.BENE_ID ]]>
	</select>
	
	<!-- 屏5机构外包案件数量 -->
	<select id="findOutContractAmount" resultType="java.util.Map" parameterType="java.util.Map"> 
		<![CDATA[ SELECT ORGAN_NAME,FBCOUNT,FHCOUNT,WTCOUNT,(FBCOUNT+FHCOUNT)WBCOUNT FROM (
						SELECT JG.AREA_CODE,JG.ORGAN_CODE,
						       (CASE WHEN FB.FBCOUNT IS NULL THEN 0 ELSE FB.FBCOUNT end)FBCOUNT ,
						       (CASE WHEN FH.FHCOUNT IS NULL THEN 0 ELSE FH.FHCOUNT END)FHCOUNT,
						       JG.ORGAN_NAME,
						       (CASE WHEN WT.WTCOUNT IS NULL THEN 0 ELSE WT.WTCOUNT END)WTCOUNT FROM (
						SELECT A.AREA_CODE ,A.ORGAN_CODE ,B.ORGAN_NAME
						       FROM  APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_UDMP_ORG_REL B 
						       WHERE A.ORGAN_CODE = B.ORGAN_CODE]]>
		<if test="area_code !=null and area_code !='' "> <![CDATA[ AND A.AREA_CODE = #{area_code} ]]> </if>
		<![CDATA[  )JG ]]>  
		<![CDATA[	LEFT JOIN      
						(SELECT A.AREA_CODE ,A.ORGAN_CODE,D.ORGAN_NAME,COUNT(B.CASE_ID)FBCOUNT FROM /*发包数量*/
						       APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_CLAIM_CASE B,
						       APP___CLM__DBUSER.T_UDMP_ORG_REL D
						       WHERE SUBSTR(B.ORGAN_CODE,0,4) = A.ORGAN_CODE
						       AND A.ORGAN_CODE = D.ORGAN_CODE
						       and exists(SELECT 1 FROM APP___CLM__DBUSER.T_OUTSOURCE_CASE  C where B.CASE_ID = C.CASE_ID]]>
		<if test="zero_time !=null and zero_time !='' "> <![CDATA[ AND C.SEND_PACKAGE_TIME >=  #{zero_time} ]]> </if>
		<if test="current_time !=null and current_time !='' "> <![CDATA[ AND C.SEND_PACKAGE_TIME <= #{current_time} ]]> </if>	
		<![CDATA[	  ) GROUP BY A.ORGAN_CODE,A.AREA_CODE,D.ORGAN_NAME)FB      
						ON JG.AREA_CODE = FB.AREA_CODE AND JG.ORGAN_CODE = FB.ORGAN_CODE
						LEFT JOIN 
						(SELECT A.ORGAN_CODE,COUNT(B.CASE_ID)FHCOUNT FROM /*收包数量*/
						       APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_CLAIM_CASE B
						       WHERE SUBSTR(B.ORGAN_CODE,0,4) = A.ORGAN_CODE
						       and exists(SELECT 1 FROM APP___CLM__DBUSER.T_OUTSOURCE_CASE  C where B.CASE_ID = C.CASE_ID ]]>
		<if test="zero_time !=null and zero_time !='' "> <![CDATA[ AND C.RECEIVED_PACKAGE_TIME >=  #{zero_time} ]]> </if>
		<if test="current_time !=null and current_time !='' "> <![CDATA[ AND C.RECEIVED_PACKAGE_TIME <= #{current_time} ]]> </if> /*收包时间*/
		 <![CDATA[     ) GROUP BY A.ORGAN_CODE)FH
						ON JG.ORGAN_CODE = FH.ORGAN_CODE
						LEFT JOIN 
						(SELECT A.AREA_CODE,A.ORGAN_CODE,COUNT(B.CASE_ID)WTCOUNT FROM  /*问题件数量*/
						       APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_CLAIM_CASE B
						       WHERE SUBSTR(B.ORGAN_CODE,0,4) = A.ORGAN_CODE
						       and exists(SELECT 1 FROM APP___CLM__DBUSER.T_OUTSOURCE_CASE  C where C.OUTSOURCE_STATUS_CODE =3 AND B.ACQUIST_WAY=C.ACQUIST_WAY AND B.CASE_ID = C.CASE_ID)
						       GROUP BY A.AREA_CODE,A.ORGAN_CODE)WT
						ON JG.AREA_CODE = WT.AREA_CODE AND JG.ORGAN_CODE = WT.ORGAN_CODE
						
						ORDER BY JG.AREA_CODE)AZ 
						  
						ORDER BY WBCOUNT DESC
		
		]]>
	</select>		
	
	<!-- 屏5片区内外包数据总量 -->
	<select id="findOutAllOrganSendCount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT SUM(FBCOUNT)ALLFBCOUT,SUM(FHCOUNT)ALLFHCOUNT,SUM(WTCOUNT)ALLWTCOUNT FROM (
				SELECT JG.AREA_CODE,JG.ORGAN_CODE,
						       (CASE WHEN FB.FBCOUNT IS NULL THEN 0 ELSE FB.FBCOUNT end)FBCOUNT ,
						       (CASE WHEN FH.FHCOUNT IS NULL THEN 0 ELSE FH.FHCOUNT END)FHCOUNT,
						       JG.ORGAN_NAME,
						       (CASE WHEN WT.WTCOUNT IS NULL THEN 0 ELSE WT.WTCOUNT END)WTCOUNT FROM (
						SELECT A.AREA_CODE ,A.ORGAN_CODE ,B.ORGAN_NAME
						       FROM  APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_UDMP_ORG_REL B 
						       WHERE A.ORGAN_CODE = B.ORGAN_CODE]]>
		<if test="area_code !=null and area_code !='' "> <![CDATA[ AND A.AREA_CODE = #{area_code} ]]> </if>
		<![CDATA[  )JG ]]>  
		<![CDATA[	LEFT JOIN      
						(SELECT A.AREA_CODE ,A.ORGAN_CODE,D.ORGAN_NAME,COUNT(B.CASE_ID)FBCOUNT FROM /*发包数量*/
						       APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_CLAIM_CASE B,
						       APP___CLM__DBUSER.T_UDMP_ORG_REL D
						       WHERE SUBSTR(B.ORGAN_CODE,0,4) = A.ORGAN_CODE
						       AND A.ORGAN_CODE = D.ORGAN_CODE
						       and exists(SELECT 1 FROM APP___CLM__DBUSER.T_OUTSOURCE_CASE  C where B.CASE_ID = C.CASE_ID ]]>
		<if test="zero_time !=null and zero_time !='' "> <![CDATA[ AND C.SEND_PACKAGE_TIME >=  #{zero_time} ]]> </if>
		<if test="current_time !=null and current_time !='' "> <![CDATA[ AND C.SEND_PACKAGE_TIME <= #{current_time} ]]> </if>	
		<![CDATA[		)GROUP BY A.ORGAN_CODE,A.AREA_CODE,D.ORGAN_NAME)FB      
						ON JG.AREA_CODE = FB.AREA_CODE AND JG.ORGAN_CODE = FB.ORGAN_CODE
						LEFT JOIN 
						(SELECT A.ORGAN_CODE,COUNT(B.CASE_ID)FHCOUNT FROM /*收包数量*/
						       APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_CLAIM_CASE B
						       WHERE SUBSTR(B.ORGAN_CODE,0,4) = A.ORGAN_CODE
						       and exists(SELECT 1 FROM APP___CLM__DBUSER.T_OUTSOURCE_CASE  C where B.CASE_ID = C.CASE_ID ]]>
		<if test="zero_time !=null and zero_time !='' "> <![CDATA[ AND C.RECEIVED_PACKAGE_TIME >=  #{zero_time} ]]> </if>
		<if test="current_time !=null and current_time !='' "> <![CDATA[ AND C.RECEIVED_PACKAGE_TIME <= #{current_time} ]]> </if> /*收包时间*/
		 <![CDATA[      )GROUP BY A.ORGAN_CODE)FH
						ON JG.ORGAN_CODE = FH.ORGAN_CODE
						LEFT JOIN 
						(SELECT A.AREA_CODE,A.ORGAN_CODE,COUNT(B.CASE_ID)WTCOUNT FROM  /*问题件数量*/
						       APP___CLM__DBUSER.T_CLAIM_AREA_ORGAN A,
						       APP___CLM__DBUSER.T_CLAIM_CASE B
						       WHERE SUBSTR(B.ORGAN_CODE,0,4) = A.ORGAN_CODE
						       and exists(SELECT 1 FROM APP___CLM__DBUSER.T_OUTSOURCE_CASE  C where C.OUTSOURCE_STATUS_CODE = 3 and B.CASE_ID = C.CASE_ID)
						       GROUP BY A.AREA_CODE,A.ORGAN_CODE)WT
						ON JG.AREA_CODE = WT.AREA_CODE AND JG.ORGAN_CODE = WT.ORGAN_CODE
						
						ORDER BY JG.AREA_CODE)AZ ]]>
	</select>	
	
	<!-- 屏4 显示个人作业量 -->
	<select id="findPersonageWork" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT AREA_CODE,REAL_NAME,AUDITCOUNT,APPROVERCOUNT,SUM(AUDITCOUNT+APPROVERCOUNT)TOTAL FROM 
				(SELECT Z.AREA_CODE,Z.REAL_NAME,
       					(CASE WHEN T.AUDITCOUNT IS NOT NULL THEN T.AUDITCOUNT ELSE 0 END)AUDITCOUNT,
      					(CASE WHEN V.APPROVERCOUNT IS NOT NULL THEN V.APPROVERCOUNT ELSE 0 END)APPROVERCOUNT FROM 
       		(SELECT A.REAL_NAME,(SELECT C.AREA_CODE
                                FROM APP___CLM__DBUSER.T_CLAIM_AREA_PERSON C
                                WHERE C.USER_ID = A.USER_ID) AREA_CODE FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B ,APP___CLM__DBUSER.T_UDMP_USER A WHERE B.OPERATOR_ID = A.USER_NAME)Z
       LEFT JOIN 
       		(SELECT DISTINCT A.REAL_NAME,A.USER_ID ,COUNT(1) AS AUDITCOUNT
           		 FROM APP___CLM__DBUSER.T_CLAIM_CASE R, 
                	  APP___CLM__DBUSER.T_UDMP_USER A
            WHERE 1 = 1
            AND R.AUDITOR_ID=A.USER_ID ]]>
         <if test="audit_time !=null and audit_time !='' "> <![CDATA[ AND TRUNC(R.AUDIT_TIME)= #{audit_time} ]]> </if>
         <![CDATA[  AND EXISTS (SELECT 1 FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B WHERE B.OPERATOR_ID = A.USER_NAME AND B.AUDIT_PERMISSION = 1 )
           		    GROUP BY A.REAL_NAME,A.USER_ID ORDER BY COUNT(1) DESC ) T
           		 	ON Z.REAL_NAME = T.REAL_NAME
        LEFT JOIN 
        		    (SELECT DISTINCT A.REAL_NAME, COUNT(1) AS APPROVERCOUNT
            	     FROM APP___CLM__DBUSER.T_CLAIM_CASE R, 
                		  APP___CLM__DBUSER.T_UDMP_USER A
           			WHERE 1 = 1
           		    AND R.Approver_Id=A.USER_ID ]]>
         <if test="approve_time !=null and approve_time !='' "> <![CDATA[ AND TRUNC(R.APPROVE_TIME)= #{approve_time} ]]> </if> 
         <![CDATA[
            AND EXISTS (SELECT 1 FROM DEV_CLM.T_CLAIM_MONITORING_PERSON B WHERE B.OPERATOR_ID = A.USER_NAME )
            GROUP BY A.REAL_NAME ORDER BY COUNT(1) DESC )V
         ON Z.REAL_NAME = V.REAL_NAME)ALLINFO
         GROUP BY ALLINFO.REAL_NAME,ALLINFO.AUDITCOUNT,ALLINFO.APPROVERCOUNT,ALLINFO.AREA_CODE
         ORDER BY TOTAL DESC ,ALLINFO.AUDITCOUNT DESC,ALLINFO.AREA_CODE DESC,ALLINFO.APPROVERCOUNT DESC
		]]>
	</select>	
	
		
	<!-- 查询出险人未结案或关闭的“垫付类”调查类型的赔案 -->
	<select id="findClaimCaseBySurveyType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,A.CASE_NO,A.CASE_STATUS,A.INSURED_ID FROM DEV_CLM.T_CLAIM_CASE A 
    INNER JOIN DEV_CLM.T_SURVEY_APPLY SA ON SA.SURVEY_TYPE='6' AND SA.CASE_ID= A.CASE_ID
     WHERE  A.CASE_STATUS NOT IN ('80','90','99') 
     AND A.INSURED_ID=#{insured_id} ]]>
     <if test=" case_id != null and case_id != ''  "><![CDATA[ AND A.CASE_ID != #{case_id} ]]></if>
	</select>
	
	<!-- 某一年度是否发生理赔接口查询 -->
	<select id="findEndClmInPolicyYear" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) 
        FROM APP___CLM__DBUSER.T_CLAIM_LIAB A,APP___CLM__DBUSER.T_CLAIM_CASE B
       WHERE 1=1 AND A.CASE_ID=B.CASE_ID 
       AND A.POLICY_CODE=#{policyCode}
       AND A.BUSI_PROD_CODE=#{busiProdCode}
       AND ( (  A.ACTUAL_PAY>0
       AND B.CASE_STATUS = '80'
       AND TO_DATE( TO_CHAR(B.END_CASE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')>=TO_DATE(TO_CHAR(#{policyYearStartDate},'yyyy-MM-dd'),'yyyy-MM-dd')
       AND TO_DATE( TO_CHAR(B.END_CASE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')<=TO_DATE(TO_CHAR(#{policyYearEndDate},'yyyy-MM-dd'),'yyyy-MM-dd')
       ) or ((select count(1) from APP___CLM__DBUSER.t_uw_busi_prod UBR where 1=1 and UBR.DECISION_CODE = '60'
        AND UBR.POLICY_CODE=#{policyCode}
        AND UBR.BUSI_PROD_CODE=#{busiProdCode}
       AND A.POLICY_ID = UBR.POLICY_ID
       AND TO_DATE( TO_CHAR(UBR.UPDATE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')>=TO_DATE(TO_CHAR(#{policyYearStartDate},'yyyy-MM-dd'),'yyyy-MM-dd')
       AND TO_DATE( TO_CHAR(UBR.UPDATE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')<=TO_DATE(TO_CHAR(#{policyYearEndDate},'yyyy-MM-dd'),'yyyy-MM-dd')
       ) >0 ))

       ]]>
	</select>
	
		
	<!-- 查询事件风险广播 （理赔）新华公司保单号所属机构代码-->
	<select id="queryRiskBroadcast" resultType="java.util.Map"	parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT B.ORGAN_CODE
        FROM APP___CLM__DBUSER.T_IHI_CLMBRO_CUSTOMER A,APP___PAS__DBUSER.T_CONTRACT_MASTER B
       WHERE 1=1
       AND A.POLICY_CODE = B.POLICY_CODE
       AND A.APPKEY='000019'
       AND TO_DATE( TO_CHAR(A.RECEIVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')=TO_DATE(TO_CHAR(#{sendTime},'yyyy-MM-dd'),'yyyy-MM-dd')
       ]]>
	</select>
	
	<!-- 查询事件风险广播 （理赔）新华公司保单号所属客户-->
	<select id="queryRiskBroadcastByCustomer" resultType="java.util.Map"	parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.DST_NAME,A.DST_CERT_TYPE,A.DST_CERTI_CODE,CUSTOMER_ID,B.ORGAN_CODE
        FROM APP___CLM__DBUSER.T_IHI_CLMBRO_CUSTOMER A,APP___PAS__DBUSER.T_CONTRACT_MASTER B
       WHERE 1=1
       AND A.POLICY_CODE = B.POLICY_CODE
       AND A.APPKEY='000019'
       AND TO_DATE( TO_CHAR(A.RECEIVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')=TO_DATE(TO_CHAR(#{sendTime},'yyyy-MM-dd'),'yyyy-MM-dd')
       ]]>
	</select>
	
	<!-- 查询事件风险广播 （理赔）每个客户的保单信息-->
	<select id="queryRiskBroadcastByDSTCustomer" resultType="java.util.Map"	parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.CLMBRO_ID,A.SCENE_NAME,A.LABEL_NAME,A.DST_NAME,A.DST_CERT_TYPE,
		A.DST_CERTI_CODE,A.APPKEY,A.POLICY_CODE,A.CUSTOMER_NAME,A.CUSTOMER_CERT_TYPE,
		A.CUSTOMER_CERTI_CODE,A.CUSTOMER_ID,A.INFO_SOURCE,A.RECEIVE_TIME
        FROM APP___CLM__DBUSER.T_IHI_CLMBRO_CUSTOMER A
       WHERE 1=1
       AND TO_DATE( TO_CHAR(A.RECEIVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')=TO_DATE(TO_CHAR(#{sendTime},'yyyy-MM-dd'),'yyyy-MM-dd')
       AND A.DST_NAME=#{dst_name}
       AND A.DST_CERT_TYPE=#{dst_cert_type}
       AND A.DST_CERTI_CODE=#{dst_certi_code}
       ]]>
	</select>
	
	<!-- 查询投保人告知信息 -->
	<select id="queryInformSituationForPolicyHolder" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  SELECT COUNT(*) FROM APP___CLM__DBUSER.T_POLICY_HOLDER SL,APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER QC
       					WHERE SL.POLICY_CODE=QC.POLICY_CODE  AND QC.SURVEY_MODULE_RESULT LIKE '%是%' AND SL.CUR_FLAG=1  
       					  AND QC.QUESTIONAIRE_OBJECT='1'
                   ]]>
		 <if test=" customer_id != null and customer_id != '' ">
			    <![CDATA[AND SL.customer_id = #{customer_id }]]>
		 </if>
		 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and SL.policy_code = #{policy_code} ]]></if>	
		 <if test=" case_id != null and case_id != ''  "><![CDATA[ and SL.case_id = #{case_id} ]]></if>	
	</select>
	<select id="queryInformSituationAllForPolicyHolder" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  SELECT COUNT(*) FROM APP___CLM__DBUSER.T_POLICY_HOLDER SL,APP___PAS__DBUSER.V_QUESTIONAIRE_CUSTOMER_ALL QC
       					WHERE SL.POLICY_CODE=QC.POLICY_CODE AND QC.CUSTOMER_ID=SL.CUSTOMER_ID AND QC.SURVEY_MODULE_RESULT LIKE '%是%' AND SL.CUR_FLAG=1 
       					  AND QC.QUESTIONAIRE_OBJECT='1'
                   ]]>
		 <if test=" customer_id != null and customer_id != '' ">
			    <![CDATA[AND SL.customer_id = #{customer_id }]]>
		 </if>
		 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and SL.policy_code = #{policy_code} ]]></if>	
		 <if test=" case_id != null and case_id != ''  "><![CDATA[ and SL.case_id = #{case_id} ]]></if>	
	</select>
	<select id="queryInformSituationCsForPolicyHolder" parameterType="java.util.Map"
		resultType="java.lang.Integer">
    <![CDATA[  SELECT COUNT(*) FROM APP___CLM__DBUSER.T_POLICY_HOLDER SL,APP___PAS__DBUSER.T_CS_QUESTIONAIRE_CUSTOMER SC   
       				WHERE SL.POLICY_CODE=SC.POLICY_CODE  AND SC.SURVEY_MODULE_RESULT LIKE '%是%' AND SC.QUESTIONAIRE_OBJECT='1'
                   ]]>
	 	<if test=" customer_id != null and customer_id != '' ">
		    <![CDATA[AND SL.customer_id = #{customer_id }]]>
	    </if>
	    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ and SL.policy_code = #{policy_code} ]]></if>		
	</select>
	<!-- 按索引查询操作 -->	
	<select id="findClaimChangConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.change_code FROM APP___CLM__DBUSER.T_CLAIM_CHANGE_CONFIG A WHERE 1 = 1  ]]>
		<if test=" change_type != null and change_type != ''  "><![CDATA[ AND A.CHANGE_TYPE = #{change_type} ]]></if>
		<if test=" claim_code != null and claim_code != ''  "><![CDATA[ AND A.CLAIM_CODE = #{claim_code} ]]></if>
		<![CDATA[ ORDER BY A.list_id ]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="findOldClaimChangConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_CODE FROM APP___CLM__DBUSER.T_CLAIM_CHANGE_CONFIG A WHERE 1 = 1  ]]>
		<if test=" change_type != null and change_type != ''  "><![CDATA[ AND A.CHANGE_TYPE = #{change_type} ]]></if>
		<if test=" change_code != null and change_code != ''  "><![CDATA[ AND A.CHANGE_CODE = #{change_code} ]]></if>
		<![CDATA[ ORDER BY A.list_id ]]>
	</select>
	<sql id="claimChangeConfig">
		<if test=" list_id != null  "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" change_type != null and change_type != ''  "><![CDATA[ AND A.CHANGE_TYPE = #{change_type, jdbcType=VARCHAR} ]]></if>
		<if test=" change_name != null and change_name != ''  "><![CDATA[ AND A.CHANGE_NAME = #{change_name, jdbcType=VARCHAR} ]]></if>
		<if test=" claim_code != null and claim_code != ''  "><![CDATA[ AND A.CLAIM_CODE = #{claim_code} ]]></if>
		<if test=" change_code != null and change_code != ''  "><![CDATA[ AND A.CHANGE_CODE = #{change_code} ]]></if>
		<if test=" change_desc != null and change_desc != ''  "><![CDATA[ AND A.CHANGE_DESC = #{change_desc} ]]></if>
	</sql>
	<!-- 查询税优报送产品信息 -->	
	<select id="findProductBocicByBusiPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TBP.business_prd_id,TPB.PRODUCT_CODE_BOCIC,
					       TPB.PRODUCT_NAME_BOCIC,
					       TCB.COVERAGE_PACK_CODE
					  FROM DEV_PDS.T_BUSINESS_PRODUCT TBP
					 inner join DEV_PDS.T_PRODUCT_BOCIC TPB
					    on TBP.Business_Prd_Id = TPB.Business_Prd_Id
					 inner join DEV_PDS.T_COVERAGE_BOCIC TCB
					    on TCB.BOCIC_COVERAGE_ID = TPB.BOCIC_COVERAGE_ID where TBP.BUSINESS_PRD_ID=#{busi_prd_id} ]]>
	</select>
	<!-- 通过赔案查询涉案保单 -->	
	<select id="queryRelatedCasePolicyByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.policy_code from APP___CLM__DBUSER.T_CONTRACT_MASTER a , APP___CLM__DBUSER.T_claim_case b
                  where 1=1 and a.case_id = b.case_id and a.cur_flag = '1' and b.case_No=#{case_no} ]]>
	</select>
	<select id="findClaimChangeConfigChangeCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_CODE
				  FROM APP___CLM__DBUSER.T_CLAIM_CHANGE_CONFIG A
				 WHERE A.CLAIM_CODE = #{claim_code} ]]>
	</select>
	
	<select id="clm_findAllBeneByCaseIdNew" parameterType="java.util.Map" resultType="java.util.Map"> 
       <![CDATA[
		SELECT DISTINCT TCB.BENE_NAME,
		        TCB.BENE_NATION,
		        TC.COMPANY_NAME,
		        TCB.BENE_CERTI_TYPE,
		        TCB.BENE_CERTI_NO,
		        TCB.BENE_BIRTH,
		        TCB.BENE_ID,
		        TCB.BENE_NO,
		        TCB.BENE_SEX,
		        TCP.POLICY_CODE ,
		        (SELECT CASE_NO FROM  APP___CLM__DBUSER.T_CLAIM_CASE WHERE CASE_ID=TCP.CASE_ID) CASE_NO,
		        (SELECT SUM(T2.PAY_AMOUNT) FROM  APP___CLM__DBUSER.T_CLAIM_BENE T1,APP___CLM__DBUSER.T_CLAIM_PAY T2 WHERE T1.BENE_ID=T2.BENE_ID AND T2.CASE_ID=TCP.CASE_ID )  PAY_AMOUNT
		   FROM APP___CLM__DBUSER.T_CLAIM_BENE TCB, APP___CLM__DBUSER.T_CUSTOMER TC, APP___CLM__DBUSER.T_CLAIM_PAY TCP
		  WHERE TCP.BENE_ID = TCB.BENE_ID
		  	AND TCP.CASE_ID = TCB.CASE_ID
		    AND TCB.BENE_NO = TC.CUSTOMER_ID(+)
		    AND TCP.PAY_AMOUNT !=0
		    AND TCP.CASE_ID =#{case_id}
		]]>
	</select>
	<!-- 通过赔案查询涉案保单及投保人信息 -->	
	<select id="clm_findPolicyListByCaseNo" parameterType="java.util.Map" resultType="java.util.Map"> 
       <![CDATA[
			SELECT B.CASE_ID,B.APPLY_CODE,A.POLICY_CODE,PH.CUSTOMER_ID,C.CUSTOMER_NAME,C.CUSTOMER_BIRTHDAY,
			  C.CUSTOMER_GENDER,C.CUSTOMER_CERT_TYPE,C.CUSTOMER_CERTI_CODE
			  FROM APP___CLM__DBUSER.T_CLAIM_POLICY A
			  INNER JOIN APP___CLM__DBUSER.T_CONTRACT_MASTER B
			    ON A.CASE_ID = B.CASE_ID
			  INNER JOIN APP___CLM__DBUSER.T_POLICY_HOLDER PH
			    ON B.POLICY_ID = PH.POLICY_ID
			  INNER JOIN APP___CLM__DBUSER.T_CUSTOMER C
    			ON PH.CUSTOMER_ID = C.CUSTOMER_ID 
			  AND A.POLICY_ID = B.POLICY_ID
			  AND B.CUR_FLAG = '1'
			  AND PH.CUR_FLAG = '1'
			  AND PH.CASE_ID = B.CASE_ID
			  WHERE A.CASE_ID = #{case_id}
		]]>
	</select>
	<!-- 查询被保人信息-->	
	<select id="clm_findInsuredCountById" parameterType="java.util.Map" resultType="java.lang.Integer"> 
       <![CDATA[
       		SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CONTRACT_MASTER CM,APP___CLM__DBUSER.T_INSURED_LIST I,APP___CLM__DBUSER.T_CUSTOMER C 
       		WHERE CM.CASE_ID=I.CASE_ID AND CM.POLICY_ID = I.POLICY_ID AND CM.CUR_FLAG = '1' AND I.CUSTOMER_ID = C.CUSTOMER_ID 
       		AND I.CUR_FLAG = '1' AND CM.CASE_ID =#{case_id}
			AND CM.POLICY_CODE= #{policy_code}
	   	]]>
			<if test="customer_id != null"> AND C.CUSTOMER_ID = #{customer_id,jdbcType=VARCHAR} </if>
			<if test="customer_name != null"> AND C.CUSTOMER_NAME = #{customer_name,jdbcType=VARCHAR} </if>
			<if test="customer_birthday != null"> AND C.CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} </if>
			<if test="customer_gender != null"> AND C.CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} </if>
			<if test="customer_certi_code != null and customer_certi_code != ''"> AND C.CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} </if>
	</select>
	<select id="clm_findInsuredCustomerDataByCaseId" parameterType="java.util.Map" resultType="java.util.Map"> 
       <![CDATA[
       		SELECT C.CUSTOMER_ID,C.CUSTOMER_NAME,C.CUSTOMER_BIRTHDAY,C.CUSTOMER_GENDER,C.CUSTOMER_CERTI_CODE,I.POLICY_CODE,I.CASE_ID
				  FROM APP___CLM__DBUSER.T_INSURED_LIST    I,
				       APP___CLM__DBUSER.T_CUSTOMER        C
				 WHERE I.CUSTOMER_ID = C.CUSTOMER_ID
				   AND I.CUR_FLAG = '1'
				   AND i.CASE_ID = #{case_id}
	   	]]>
	</select>
	<!-- 通过保单号、客户id查询受益人信息-->	
	<select id="clm_findBeneCountById" parameterType="java.util.Map" resultType="java.lang.Integer"> 
       <![CDATA[
       		SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_BENE TCB, APP___CLM__DBUSER.T_CLAIM_PAY TCP
       		WHERE TCP.BENE_ID = TCB.BENE_ID AND TCP.CASE_ID = TCB.CASE_ID 
       		AND TCP.CASE_ID =#{case_id}
			AND TCP.POLICY_CODE= #{policy_code} 
		]]>
		<if test="customer_id != null"> AND TCB.BENE_NO = #{customer_id} </if>
		<if test="customer_name != null"> AND TCB.BENE_NAME = #{customer_name,jdbcType=VARCHAR} </if>
		<if test="customer_birthday != null"> AND TCB.BENE_BIRTH = #{customer_birthday, jdbcType=DATE} </if>
		<if test="customer_gender != null"> AND TCB.BENE_SEX = #{customer_gender, jdbcType=NUMERIC} </if>
		<if test="customer_certi_code != null and customer_certi_code != ''"> AND TCB.BENE_CERTI_NO = #{customer_certi_code, jdbcType=VARCHAR} </if>
	</select>
	<select id="clm_findBeneDataByCaseId" parameterType="java.util.Map" resultType="java.util.Map"> 
       <![CDATA[
       		SELECT TCP.POLICY_CODE,TCB.BENE_NO,TCB.BENE_NAME,TCB.BENE_BIRTH,TCB.BENE_SEX,TCB.BENE_CERTI_NO,TCP.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_BENE TCB, APP___CLM__DBUSER.T_CLAIM_PAY TCP
           WHERE TCP.BENE_ID = TCB.BENE_ID AND TCP.CASE_ID = TCB.CASE_ID 
           AND TCP.CASE_ID =#{case_id}
		]]>
	</select>
		<!-- 通过保单号、客户id查询领款人信息-->	
	<select id="clm_findPayeeCountById" parameterType="java.util.Map" resultType="java.lang.Integer"> 
       <![CDATA[
			SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_PAYEE A, APP___CLM__DBUSER.T_CLAIM_PAY B
       		WHERE A.PAYEE_ID = B.PAYEE_ID AND A.CASE_ID = B.CASE_ID
       		AND B.CASE_ID =#{case_id}
			AND B.POLICY_CODE= #{policy_code}
		]]>
		<if test="customer_id != null"> AND A.PAYEE_NO = #{customer_id} </if>
		<if test="customer_name != null"> AND A.PAYEE_NAME = #{customer_name,jdbcType=VARCHAR} </if>
		<if test="customer_birthday != null"> AND A.PAYEE_BIRTH = #{customer_birthday, jdbcType=DATE} </if>
		<if test="customer_gender != null"> AND A.PAYEE_SEX = #{customer_gender, jdbcType=NUMERIC} </if>
		<if test="customer_certi_code != null and customer_certi_code != ''"> AND A.PAYEE_CERTI_NO = #{customer_certi_code, jdbcType=VARCHAR} </if>
	</select>
	<select id="clm_findPayeeDataByCaseId" parameterType="java.util.Map" resultType="java.util.Map"> 
       <![CDATA[
		SELECT B.POLICY_CODE,A.PAYEE_NO,A.PAYEE_NAME,A.PAYEE_BIRTH,A.PAYEE_SEX,A.PAYEE_CERTI_NO,A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_PAYEE A, APP___CLM__DBUSER.T_CLAIM_PAY B
           WHERE A.PAYEE_ID = B.PAYEE_ID AND A.CASE_ID = B.CASE_ID
           AND B.CASE_ID =#{case_id}
		]]>
	</select>

	<select id="findChronicDiseaseInfoCode" resultType="java.util.Map" parameterType="java.util.Map">
		select DISEASE_CODE from APP___CLM__DBUSER.T_CHRONIC_DISEASE_INFO
	</select>
</mapper>


