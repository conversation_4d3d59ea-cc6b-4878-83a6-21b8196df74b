<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.ICssPreFilledDao">

    <!-- 查询个数操作 -->
	<select id="findCssPreFilledsTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		  	SELECT COUNT(1)
			  FROM DEV_PAS.T_CS_PRE_FILLED PF
			LEFT JOIN DEV_CSS.T_CSS_PRE_FILLED_BUSINESS CB 
			     ON PF.BUSINESS_CODE = CB.BUSINESS_CODE
			WHERE 1 = 1
		]]>
		<include refid="queryPreFilledCondition" />
	</select>
	
	<!-- 查询柜面预填单的数据 -->
	<select id="findCssPreFilledsPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
			       B.*
			  FROM (
			  	SELECT ROWNUM RN,PF.PRE_FILLED_ID,
			  		   PF.CUSTOMER_ID,
				       PF.WORK_CODE ARRAY_CODE,
				       PF.BUSINESS_CODE BUSINESS_TYPE,
				       CB.BUSINESS_NAME,
				       PF.POLICY_CODE,
				       PF.BRANCH_CODE ORGAN_CODE,
				       (SELECT T.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG T WHERE T.ORGAN_CODE = PF.BRANCH_CODE) AS ORGAN_NAME,
				       PF.PRE_FILLED_TIME
				  FROM APP___PAS__DBUSER.T_CS_PRE_FILLED PF
				LEFT JOIN APP___CSS__DBUSER.T_CSS_PRE_FILLED_BUSINESS CB 
				     ON PF.BUSINESS_CODE = CB.BUSINESS_CODE
				WHERE 1 = 1
				AND ROWNUM <= #{LESS_NUM}			  	
		]]>
		<include refid="queryPreFilledCondition" />
		<![CDATA[ ORDER BY PF.PRE_FILLED_TIME DESC) B
			 WHERE  B.RN > #{GREATER_NUM}	
		]]>
	</select>
	<!-- 初始客户基本信息确认页 -->
	<select id="css_queryPrefillCCInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT C.PRE_FILLED_ID,CF.BUSINESS_CODE AS SERVICE_CDOE,
			       CF.ACCEPT_TYPE,
			       CF.CUSTOMER_ID,
			       CF.CUSTOMER_NAME,
			       C.POLICY_CODE,
			       C.ADDRESS_ID,
			       C.HOUSE_TEL,
			       C.MOBILE_TEL,
			       C.EMAIL,
			       C.COUNTRY_CODE,
			       C.STATE,
			       C.CITY,
			       C.DISTRICT,
			       C.ADDRESS,
			       C.POST_CODE
		 	FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CUSTOMER C
		 	LEFT JOIN APP___PAS__DBUSER.T_CS_PRE_FILLED CF ON C.PRE_FILLED_ID = CF.PRE_FILLED_ID
		 	WHERE 1=1
		]]>
		   <include refid="queryPreFillCondition"></include>
	</select>
	
	<!-- 初始缴费信息确认页 -->
	<select id="css_queryPrefillPCInfo" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		SELECT C.PRE_FILLED_ID,
		       C.POLICY_CODE,
		       C.PAY_MODE,
		       C.ACCOUNT,
		       C.ACCOUNT_NAME,
		       C.ACCOUNT_BANK,
		       CF.CUSTOMER_NAME,
		       CF.CUSTOMER_ID,
		       CF.ACCEPT_TYPE
		 FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_PAYER_ACCOUNT C
		 LEFT JOIN APP___PAS__DBUSER.T_CS_PRE_FILLED CF ON C.PRE_FILLED_ID = CF.PRE_FILLED_ID
		 WHERE 1=1
	  ]]>
		 <include refid="queryPreFillCondition"></include>
	</select>
	<!-- 初始受益人信息确认页 -->
	<select id="css_queryPrefillBCInfo" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		SELECT 
		   C.PRE_FILLED_BENE_ID,/*受益人信息变更主键id*/
		   C.PRE_FILLED_ID,/*预填单主键ID*/
	       C.POLICY_CODE,/*保单号*/
	       C.LIST_ID,/*受益人主键ID*/
	       C.BUSI_ITEM_ID,/*险种id*/
	       CBP.BUSI_PROD_CODE AS PRODUCT_CODE,/*险种编码*/
	       (SELECT BP.PRODUCT_NAME_SYS
	          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
	         WHERE BP.PRODUCT_CODE_SYS = CBP.BUSI_PROD_CODE) AS PRODUCT_NAME,/*险种名称*/
	       C.INSURED_ID,/*被保人id*/
	       (SELECT CU.CUSTOMER_NAME FROM APP___PAS__DBUSER.T_INSURED_LIST IL 
                 LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER CU
                      ON IL.CUSTOMER_ID = CU.CUSTOMER_ID
                 WHERE IL.LIST_ID = C.INSURED_ID) AS INSURED_NAME,/*被保人姓名*/
	       C.CUSTOMER_NAME,/*受益人姓名*/
	       C.CUSTOMER_BIRTHDAY,/*受益人出生日期*/
	       C.CUSTOMER_GENDER,/*受益人性别*/
	       C.CUSTOMER_CERT_TYPE,/*受益人证件类型*/
	       C.CUSTOMER_CERTI_CODE,/*受益人证件号码*/
	       C.SHARE_ORDER,/*受益顺位*/
	       C.CUSTOMER_ID,/*客户id*/
	       C.DESIGNATION,/*与被保人关系*/
	       C.SHARE_RATE,/*受益比例*/
	       C.BENE_TYPE,/*受益类型*/
	       C.CUST_CERT_STAR_DATE,/*客户证件有效期起始日期*/
	       C.CUST_CERT_END_DATE,/*客户证件有效期结束日期*/
	       C.COUNTRY_CODE,/*国籍代码*/
	       C.JOB_CODE,/*职业编码*/
	       C.MOBILE_TEL,/*手机号码*/
	       C.FIX_TEL as OFFEN_USE_TEL,/*固定电话号码*/
	       C.STATE,/*省*/
	       C.CITY,/*市*/
	       C.DISTRICT,/*县镇*/
	       C.ADDRESS,/*地址*/
	       C.POST_CODE,/*邮编*/
	       C.BANK_CODE,/*开户行 对应码表T_BANK*/
	       C.BANK_ACCOUNT,/*银行账户*/
	       C.ACCO_NAME,/*账户名*/
	       C.SURVIVAL_W_MODE AS SURVIVAL_MODE /*生存领取形式*/
	  FROM APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE C
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
		    ON C.POLICY_CODE = CBP.POLICY_CODE
		   AND C.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
	   WHERE 1=1
	   ]]>
		<include refid="queryPreFillCondition"></include>
	</select>  
	<select id="css_findPolicyIdByPolicyCode"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT TCM.POLICY_ID,TCM.POLICY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM 
				WHERE TCM.POLICY_CODE = #{policy_code}
		]]>
	</select> 
	<!-- 删除受益人变更表删除受益人信息 -->
	<delete id="css_deleteContractBeneInfo" parameterType="java.util.Map">
		<![CDATA[  DELETE APP___PAS__DBUSER.T_CS_PRE_FILLED_CONTRACT_BENE WHERE PRE_FILLED_BENE_ID = #{pre_filled_bene_id} ]]>
	</delete>
	<!-- 删除预填单表预填单信息 -->
	<delete id="css_deletePreFilledInfo" parameterType="java.util.Map">
		<![CDATA[  DELETE APP___PAS__DBUSER.T_CS_PRE_FILLED WHERE PRE_FILLED_ID = #{pre_filled_id} ]]>
	</delete>
	<!-- 预填单确认条件 -->
	<sql id="queryPreFillCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND C.POLICY_CODE= #{policy_code} ]]></if>
		<if test=" pre_filled_id != null and pre_filled_id != '' "><![CDATA[ AND C.PRE_FILLED_ID= #{pre_filled_id} ]]></if>
		<if test=" pre_filled_bene_id != null and pre_filled_bene_id != '' "><![CDATA[ AND C.PRE_FILLED_BENE_ID= #{pre_filled_bene_id} ]]></if>
	</sql>
	<!-- 查询柜面预填单条件 -->
	<sql id="queryPreFilledCondition"> 
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND PF.BRANCH_CODE LIKE #{organ_code} ]]></if>
		<if test=" business_type != null and business_type != '' "><![CDATA[ AND PF.BUSINESS_CODE = #{business_type} ]]></if>
		<if test=" business_name != null and business_name != '' "><![CDATA[ AND CB.BUSINESS_NAME = #{business_name} ]]></if>
		<if test=" start_time != null and start_time != '' "><![CDATA[ AND PF.PRE_FILLED_TIME >= #{start_time} ]]></if>
		<if test=" end_time != null and end_time != '' "><![CDATA[ AND PF.PRE_FILLED_TIME <= #{end_time} ]]></if>
		<if test=" customer_id != null and customer_id != '' "><![CDATA[ AND PF.CUSTOMER_ID = #{customer_id} ]]></if> 
	</sql>
    
</mapper>