<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.ICssSignInfomationDao">
    
    <sql id="queryCustomerIdCondition">
        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND PH.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" customer_id != null and customer_id != '' "><![CDATA[ AND PH.CUSTOMER_ID = #{customer_id} ]]></if>
    </sql> 
    <sql id="documentPrintWhereCondition">
        <if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
        <if test=" print_time  != null  and  print_time  != ''  "><![CDATA[ AND A.PRINT_TIME = #{print_time} ]]></if>
        <if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
        <if test=" print_id  != null "><![CDATA[ AND A.PRINT_ID = #{print_id} ]]></if>
        <if test=" print_by  != null "><![CDATA[ AND A.PRINT_BY = #{print_by} ]]></if>
        <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
        <if test=" reprint_times  != null "><![CDATA[ AND A.REPRINT_TIMES = #{reprint_times} ]]></if>
        <if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
    </sql>
    <sql id="documentWhereCondition">
        <if test=" buss_source_code != null and buss_source_code != ''  "><![CDATA[ AND A.BUSS_SOURCE_CODE = #{buss_source_code} ]]></if>
        <if test=" clob_id  != null "><![CDATA[ AND A.CLOB_ID = #{clob_id} ]]></if>
        <if test=" scan_by  != null "><![CDATA[ AND A.SCAN_BY = #{scan_by} ]]></if>
        <if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
        <if test=" print_time  != null  and  print_time  != ''  "><![CDATA[ AND A.PRINT_TIME = #{print_time} ]]></if>
        <if test=" create_time  != null  and  create_time  != ''  "><![CDATA[ AND A.CREATE_TIME = #{create_time} ]]></if>
        <if test=" send_by  != null "><![CDATA[ AND A.SEND_BY = #{send_by} ]]></if>
        <if test=" supplement_flag  != null "><![CDATA[ AND A.SUPPLEMENT_FLAG = #{supplement_flag} ]]></if>
        <if test=" send_time  != null  and  send_time  != ''  "><![CDATA[ AND A.SEND_TIME = #{send_time} ]]></if>
        <if test=" create_by  != null "><![CDATA[ AND A.CREATE_BY = #{create_by} ]]></if>
        <if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
        <if test=" is_link  != null "><![CDATA[ AND A.IS_LINK = #{is_link} ]]></if>
        <if test=" buss_id  != null "><![CDATA[ AND A.BUSS_ID = #{buss_id} ]]></if>
        <if test=" print_by  != null "><![CDATA[ AND A.PRINT_BY = #{print_by} ]]></if>
        <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
        <if test=" overdue_time  != null  and  overdue_time  != ''  "><![CDATA[ AND A.OVERDUE_TIME = #{overdue_time} ]]></if>
        <if test=" close_by  != null "><![CDATA[ AND A.CLOSE_BY = #{close_by} ]]></if>
        <if test=" reply_time  != null  and  reply_time  != ''  "><![CDATA[ AND A.REPLY_TIME = #{reply_time} ]]></if>
        <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
        <if test=" send_obj_type != null and send_obj_type != ''  "><![CDATA[ AND A.SEND_OBJ_TYPE = #{send_obj_type} ]]></if>
        <if test=" reply_remark != null and reply_remark != ''  "><![CDATA[ AND A.REPLY_REMARK = #{reply_remark} ]]></if>
        <if test=" document_name != null and document_name != ''  "><![CDATA[ AND A.DOCUMENT_NAME = #{document_name} ]]></if>
        <if test=" overdue_document_no != null and overdue_document_no != ''  "><![CDATA[ AND A.OVERDUE_DOCUMENT_NO = #{overdue_document_no} ]]></if>
        <if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
        <if test=" close_time  != null  and  close_time  != ''  "><![CDATA[ AND A.CLOSE_TIME = #{close_time} ]]></if>
        <if test=" send_obj_id != null and send_obj_id != ''  "><![CDATA[ AND A.SEND_OBJ_ID = #{send_obj_id} ]]></if>
        <if test=" reprint_times  != null "><![CDATA[ AND A.REPRINT_TIMES = #{reprint_times} ]]></if>
        <if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
        <if test=" is_merger  != null "><![CDATA[ AND A.IS_MERGER = #{is_merger} ]]></if>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" scan_time  != null  and  scan_time  != ''  "><![CDATA[ AND A.SCAN_TIME = #{scan_time} ]]></if>
        <if test=" reply_conclusion  != null "><![CDATA[ AND A.REPLY_CONCLUSION = #{reply_conclusion} ]]></if>
        <if test=" buss_code != null and buss_code != ''  "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
        <if test=" reply_by  != null "><![CDATA[ AND A.REPLY_BY = #{reply_by} ]]></if>
        <if test=" reply_days  != null "><![CDATA[ AND A.REPLY_DAYS = #{reply_days} ]]></if>
        <if test=" serviceCode != null and serviceCode !='' "><![CDATA[ AND A.DOCUMENT_NO like '${serviceCode}%' ]]></if>
    </sql>
    <!-- 查询待办保全项的应备资料  --> 
    <select id="CSS_queryCssOptionSign" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[        
            SELECT 
 DISTINCT ADC.DOC_TYPE_ID AS DOC_TYPE,
       ADC.SERVICE_CODE  ,
       ADC.ORIGINAL_FLAG  ,
       ADC.DOC_REMARK AS SIGN_REMARK,
       CD.DOC_TYPE_NAME AS DOC_NAME,
       TO_Number('1') as SIGN_COUNT
  FROM APP___PAS__DBUSER.T_CS_APP_DOC_CFG ADC
 INNER JOIN APP___PAS__DBUSER.T_CS_DOCUMENT CD
    ON ADC.DOC_TYPE_ID = CD.DOC_TYPE_ID
 WHERE 1 = 1
        ]]>
        <if test=" service_code != null and service_code != '' "><![CDATA[ AND ADC.SERVICE_CODE = #{service_code} ]]></if>
    </select>
    <!-- 【1】查询客户相关信息  --> 
    <select id="CSS_queryCustomerId" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[        
        SELECT PH.POLICY_CODE,
       PH.POLICY_ID,
       C.CUSTOMER_ID,
       C.CUSTOMER_NAME,
       C.CUSTOMER_GENDER,
       C.CUSTOMER_CERT_TYPE,
       C.CUSTOMER_CERTI_CODE
  FROM DEV_PAS.T_POLICY_HOLDER PH
 INNER JOIN DEV_PAS.T_CUSTOMER C
    ON C.CUSTOMER_ID = PH.CUSTOMER_ID
 WHERE ROWNUM < 2
        ]]>
        <include refid="queryCustomerIdCondition" />
    </select>
    <select id="CSS_findAllDocumentPrint" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.DOC_LIST_ID, A.PRINT_TIME, A.TEMPLATE_CODE, A.PRINT_ID, A.PRINT_BY, 
            A.ORGAN_CODE, A.REPRINT_TIMES, A.DOCUMENT_NO FROM APP___PAS__DBUSER.T_DOCUMENT_PRINT A WHERE ROWNUM <=  1000  ]]>
        <include refid="documentPrintWhereCondition" />
        <![CDATA[ ORDER BY A.PRINT_ID ]]> 
    </select>
    <!-- 【2】 -->
    <select id="CSS_findAllDocument" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
            A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
            A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
            A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
            A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
            A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___PAS__DBUSER.T_DOCUMENT A WHERE ROWNUM <=  1000  ]]>
        <include refid="documentWhereCondition" />
        <![CDATA[ ORDER BY A.DOC_LIST_ID DESC]]> 
    </select>
    <!-- 【3】查询保单服务人员信息  --> 
    <select id="CSS_queryAgentOrganCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[        
         SELECT CA.AGENT_ORGAN_CODE,
       CA.AGENT_CODE,
       CA.POLICY_CODE,
       TA.AGENT_CHANNEL
  FROM DEV_PAS.T_CONTRACT_AGENT CA
 INNER JOIN DEV_PAS.T_AGENT TA
    ON CA.AGENT_CODE = TA.AGENT_CODE
 WHERE TA.AGENT_STATUS = '1' AND CA.IS_CURRENT_AGENT='1'
        ]]>
        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND CA.POLICY_CODE = #{policy_code} ]]></if>
    </select>
    <!-- 【4】根据机构代码查询本级及上级机构代码名称 -->
    <select id="CSS_findOrgRelByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_NAME, A.ORGAN_CODE, A.UPORGAN_LVL_CODE, A.UPORGAN_CODE, A.UPORGAN_ID, A.ORGAN_GRADE, A.ORGAN_ID FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A WHERE 1 = 1  ]]>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<![CDATA[ ORDER BY A.ORGAN_ID ]]>
	</select>
     <insert id="addOptionAISign" useGeneratedKeys="true"
        parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="sign_main_id">
            SELECT APP___PAS__DBUSER.S_CS_METERIAL_SIGN_MAIN.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
            INSERT INTO APP___PAS__DBUSER.T_CS_METERIAL_SIGN_MAIN(
                SIGN_MAIN_ID,CUSTOMER_ID, POLICY_CODE, SIGN_DATE, SERVICE_CODE, SIGN_BY, ORGAN_CODE, 
                 INSERT_BY, INSERT_TIME, INSERT_TIMESTAMP, UPDATE_BY, UPDATE_TIME, UPDATE_TIMESTAMP) 
            VALUES (
                 #{sign_main_id, jdbcType=NUMERIC},#{customer_id, jdbcType=NUMERIC},#{policy_code, jdbcType=VARCHAR} , 
                 #{sign_date, jdbcType=DATE} , #{service_code, jdbcType=CHAR} , #{sign_by, jdbcType=NUMERIC} , 
                 #{organ_code, jdbcType=VARCHAR},#{insert_by, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP , 
                 #{update_by, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP  ) 
         ]]>
    </insert>
    
    <insert id="addSignInfomation" useGeneratedKeys="true"
        parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="sign_list_id">
            SELECT APP___PAS__DBUSER.S_CS_METERIAL_SIGN_LIST.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
            INSERT INTO APP___PAS__DBUSER.T_CS_METERIAL_SIGN_LIST(
                 SIGN_LIST_ID,SIGN_MAIN_ID, DOC_TYPE, DOC_NAME, ORIGINAL_FLAG, SIGN_COUNT,SIGN_REMARK,
                 INSERT_BY, INSERT_TIME, INSERT_TIMESTAMP, UPDATE_BY, UPDATE_TIME, UPDATE_TIMESTAMP) 
            VALUES (
                 #{sign_list_id, jdbcType=NUMERIC},#{sign_main_id, jdbcType=NUMERIC},#{doc_type, jdbcType=VARCHAR} , 
                 #{doc_name, jdbcType=DATE} , #{original_flag, jdbcType=NUMERIC} , #{sign_count, jdbcType=NUMERIC} , 
                 #{sign_remark, jdbcType=VARCHAR},#{insert_by, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP , 
                 #{update_by, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP  ) 
         ]]>
    </insert>
	
	<!-- 【5】 -->
	 <select id="CSS_findOrgNameByOrgenCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_ID,A.ORGAN_CODE,A.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG A WHERE 1 = 1  ]]>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{uporgan_code} ]]></if>
		<![CDATA[ ORDER BY A.ORGAN_ID ]]>
	</select>
	
    <update id="CSS_updateDocumentPrint" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_DOCUMENT_PRINT ]]>
        <set>
        <trim suffixOverrides=",">
            DOC_LIST_ID = #{doc_list_id, jdbcType=NUMERIC} ,
            PRINT_TIME = #{print_time, jdbcType=DATE} ,
            TEMPLATE_CODE = #{template_code, jdbcType=VARCHAR} ,
            UPDATE_TIME = SYSDATE , 
            PRINT_BY = #{print_by, jdbcType=NUMERIC} ,
            ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
            REPRINT_TIMES = #{reprint_times, jdbcType=NUMERIC} ,
            DOCUMENT_NO = #{document_no, jdbcType=VARCHAR} ,
            UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
        </trim>
        </set>
        <![CDATA[ WHERE PRINT_ID = #{print_id} ]]>
    </update>
    
    <insert id="CSS_addDocumentPrint"  useGeneratedKeys="true" parameterType="java.util.Map">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="print_id">
            SELECT APP___PAS__DBUSER.S_DOCUMENT_PRINT.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
            INSERT INTO APP___PAS__DBUSER.T_DOCUMENT_PRINT(
                DOC_LIST_ID, PRINT_TIME, TEMPLATE_CODE, INSERT_TIME, UPDATE_TIME, PRINT_ID, PRINT_BY, 
                ORGAN_CODE, REPRINT_TIMES, INSERT_TIMESTAMP, DOCUMENT_NO, UPDATE_BY, UPDATE_TIMESTAMP, INSERT_BY ) 
            VALUES (
                #{doc_list_id, jdbcType=NUMERIC}, #{print_time, jdbcType=DATE} , #{template_code, jdbcType=VARCHAR} , SYSDATE , SYSDATE , #{print_id, jdbcType=NUMERIC} , #{print_by, jdbcType=NUMERIC} 
                , #{organ_code, jdbcType=VARCHAR} , #{reprint_times, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{document_no, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
         ]]>
    </insert>
    
    <insert id="CSS_addPrintCssDocument"  useGeneratedKeys="true" parameterType="java.util.Map">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="print_css_doc_id">
            SELECT APP___PAS__DBUSER.S_PRINT_CLM_DOCUMENT__PRINT_CL.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
            INSERT INTO APP___PAS__DBUSER.T_PRINT_CLM_DOCUMENT(
                CLOB_ID, DOC_LIST_ID, ENCRYPT_BEFORE_SECURITY, ENCRYPT_AFTER_QR, INSERT_TIME, 
                ENCRYPT_BEFORE_QR, UPDATE_TIME, INSERT_TIMESTAMP, UPDATE_BY, PRINT_CLM_DOC_ID, 
                ENCRYPT_AFTER_SECURITY, UPDATE_TIMESTAMP, INSERT_BY ) 
            VALUES (
                #{clob_id, jdbcType=NUMERIC}, #{doc_list_id, jdbcType=NUMERIC} , #{encrypt_before_security, jdbcType=VARCHAR} , #{encrypt_after_qr, jdbcType=VARCHAR} , SYSDATE , 
                #{encrypt_before_qr, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{print_css_doc_id, jdbcType=NUMERIC} , 
                #{encrypt_after_security, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
         ]]>
    </insert>
    
    <select id="CSS_findPrintCssDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT PCD.CLOB_ID,PCD.PRINT_CLM_DOC_ID,PCD.DOC_LIST_ID FROM APP___PAS__DBUSER.T_PRINT_CLM_DOCUMENT PCD WHERE 1 = 1]]>
		<if test=" doc_list_id != null and doc_list_id != '' "><![CDATA[ AND PCD.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</select>
    
    <update id="CSS_updatePrintCssDocument" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_PRINT_CLM_DOCUMENT ]]>
        <set>
        <trim suffixOverrides=",">
       		CLOB_ID=#{clob_id, jdbcType=NUMERIC},
       		ENCRYPT_BEFORE_QR=#{encrypt_before_qr, jdbcType=VARCHAR},
       		ENCRYPT_BEFORE_SECURITY=#{encrypt_before_security, jdbcType=VARCHAR},
       		ENCRYPT_AFTER_QR=#{encrypt_after_qr, jdbcType=VARCHAR},
			ENCRYPT_AFTER_SECURITY=#{encrypt_after_security, jdbcType=VARCHAR},
			UPDATE_BY=#{update_by, jdbcType=NUMERIC},
       		UPDATE_TIME=SYSDATE,
			UPDATE_TIMESTAMP=CURRENT_TIMESTAMP,
        </trim>
        </set>
        <![CDATA[ WHERE DOC_LIST_ID = #{doc_list_id} ]]>
    </update>
    
    <insert id="CSS_addClob"  useGeneratedKeys="false"  parameterType="java.util.Map">
       <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="clob_id">
            SELECT APP___PAS__DBUSER.S_CLOB.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
            INSERT INTO APP___PAS__DBUSER.T_CLOB(
                CLOB_ID, TXT_CONTENT, CREATE_TIME, CONTENT ) 
            VALUES (
                #{clob_id, jdbcType=NUMERIC}, #{txt_content, jdbcType=VARCHAR} , SYSDATE, #{content, jdbcType=VARCHAR} ) 
         ]]>
    </insert>
    
    <update id="CSS_updateDocument" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_DOCUMENT ]]>
        <set>
        <trim suffixOverrides=",">
            BUSS_SOURCE_CODE = #{buss_source_code, jdbcType=VARCHAR} ,
            CLOB_ID = #{clob_id, jdbcType=NUMERIC} ,
            SCAN_BY = #{scan_by, jdbcType=NUMERIC} ,
            PRINT_TIME = #{print_time, jdbcType=DATE} ,
            CREATE_TIME = #{create_time, jdbcType=DATE} ,
            SEND_BY = #{send_by, jdbcType=NUMERIC} ,
            SUPPLEMENT_FLAG = #{supplement_flag, jdbcType=NUMERIC} ,
            SEND_TIME = #{send_time, jdbcType=DATE} ,
            CREATE_BY = #{create_by, jdbcType=NUMERIC} ,
            STATUS = #{status, jdbcType=VARCHAR} ,
            IS_LINK = #{is_link, jdbcType=NUMERIC} ,
            BUSS_ID = #{buss_id, jdbcType=NUMERIC} ,
            PRINT_BY = #{print_by, jdbcType=NUMERIC} ,
            ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
            OVERDUE_TIME = #{overdue_time, jdbcType=DATE} ,
            UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
            CLOSE_BY = #{close_by, jdbcType=NUMERIC} ,
            REPLY_TIME = #{reply_time, jdbcType=DATE} ,
            POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
            SEND_OBJ_TYPE = #{send_obj_type, jdbcType=VARCHAR} ,
            REPLY_REMARK = #{reply_remark, jdbcType=VARCHAR} ,
            DOCUMENT_NAME = #{document_name, jdbcType=VARCHAR} ,
            OVERDUE_DOCUMENT_NO = #{overdue_document_no, jdbcType=VARCHAR} ,
            TEMPLATE_CODE = #{template_code, jdbcType=VARCHAR} ,
            UPDATE_TIME = SYSDATE , 
            CLOSE_TIME = #{close_time, jdbcType=DATE} ,
            SEND_OBJ_ID = #{send_obj_id, jdbcType=VARCHAR} ,
            REPRINT_TIMES = #{reprint_times, jdbcType=NUMERIC} ,
            DOCUMENT_NO = #{document_no, jdbcType=VARCHAR} ,
            IS_MERGER = #{is_merger, jdbcType=NUMERIC} ,
            POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
            SCAN_TIME = #{scan_time, jdbcType=DATE} ,
            REPLY_CONCLUSION = #{reply_conclusion, jdbcType=NUMERIC} ,
            BUSS_CODE = #{buss_code, jdbcType=VARCHAR} ,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
            REPLY_BY = #{reply_by, jdbcType=NUMERIC} ,
            REPLY_DAYS = #{reply_days, jdbcType=NUMERIC} ,
        </trim>
        </set>
        <![CDATA[ WHERE DOC_LIST_ID = #{doc_list_id} ]]>
    </update>
    
    <insert id="CSS_addDocument"  useGeneratedKeys="true" parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="doc_list_id">
            SELECT APP___PAS__DBUSER.S_DOCUMENT.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
            INSERT INTO APP___PAS__DBUSER.T_DOCUMENT(
                BUSS_SOURCE_CODE, CLOB_ID, SCAN_BY, DOC_LIST_ID, PRINT_TIME, CREATE_TIME, SEND_BY, 
                SUPPLEMENT_FLAG, SEND_TIME, CREATE_BY, STATUS, IS_LINK, BUSS_ID, PRINT_BY, 
                INSERT_TIMESTAMP, ORGAN_CODE, OVERDUE_TIME, UPDATE_BY, CLOSE_BY, REPLY_TIME, POLICY_ID, 
                SEND_OBJ_TYPE, REPLY_REMARK, DOCUMENT_NAME, OVERDUE_DOCUMENT_NO, INSERT_TIME, TEMPLATE_CODE, UPDATE_TIME, 
                CLOSE_TIME, SEND_OBJ_ID, REPRINT_TIMES, DOCUMENT_NO, IS_MERGER, POLICY_CODE, SCAN_TIME, 
                REPLY_CONCLUSION, BUSS_CODE, UPDATE_TIMESTAMP, REPLY_BY, INSERT_BY, REPLY_DAYS ) 
            VALUES (
                #{buss_source_code, jdbcType=VARCHAR}, #{clob_id, jdbcType=NUMERIC} , #{scan_by, jdbcType=NUMERIC} , #{doc_list_id, jdbcType=NUMERIC} , #{print_time, jdbcType=DATE} , #{create_time, jdbcType=DATE} , #{send_by, jdbcType=NUMERIC} 
                , #{supplement_flag, jdbcType=NUMERIC} , #{send_time, jdbcType=DATE} , #{create_by, jdbcType=NUMERIC} , #{status, jdbcType=VARCHAR} , #{is_link, jdbcType=NUMERIC} , #{buss_id, jdbcType=NUMERIC} , #{print_by, jdbcType=NUMERIC} 
                , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{overdue_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{close_by, jdbcType=NUMERIC} , #{reply_time, jdbcType=DATE} , #{policy_id, jdbcType=NUMERIC} 
                , #{send_obj_type, jdbcType=VARCHAR} , #{reply_remark, jdbcType=VARCHAR} , #{document_name, jdbcType=VARCHAR} , #{overdue_document_no, jdbcType=VARCHAR} , SYSDATE , #{template_code, jdbcType=VARCHAR} , SYSDATE 
                , #{close_time, jdbcType=DATE} , #{send_obj_id, jdbcType=VARCHAR} , #{reprint_times, jdbcType=NUMERIC} , #{document_no, jdbcType=VARCHAR} , #{is_merger, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{scan_time, jdbcType=DATE} 
                , #{reply_conclusion, jdbcType=NUMERIC} , #{buss_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{reply_by, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{reply_days, jdbcType=NUMERIC} ) 
         ]]>
    </insert>
</mapper>