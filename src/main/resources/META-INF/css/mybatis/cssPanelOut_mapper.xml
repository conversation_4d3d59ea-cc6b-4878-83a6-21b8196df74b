<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.css.dao.ICssPanelOutDao">
	<!-- APP___PAS__DBUSER.T_COUNTER_DEVICE_CONFIG CDC -->
	<!-- 根据分页条件查询分页总条数 -->
	<select id="queryPanelOutCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(1)
			  FROM ( SELECT CDC.DEVICE_CONFIG_ID,
			                        CDC.DEVICE_ORGAN_CODE,
			                        (SELECT O.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CDC.DEVICE_ORGAN_CODE) DEVICE_ORGAN_NAME,
			                        CDC.DEVICE_USER_ID,
			                        (SELECT U.USER_NAME FROM APP___PAS__DBUSER.T_UDMP_USER U WHERE U.USER_ID=CDC.DEVICE_USER_ID) DEVICE_USER_IDS,
			                        CDC.DEVICE_FUNCTIOIN_CODE,
			                        CDC.INSERT_TIME,
			                        CDC.INSERT_BY,
			                        (SELECT U.USER_NAME FROM APP___PAS__DBUSER.T_UDMP_USER U WHERE U.USER_ID=CDC.INSERT_BY) INSERT_BYS
			                  FROM APP___PAS__DBUSER.T_COUNTER_DEVICE_CONFIG CDC WHERE 1=1
		]]>
		<if test="device_organ_code != null and device_organ_code != '' "><![CDATA[ AND CDC.DEVICE_ORGAN_CODE like #{device_organ_code}||'%' ]]></if>
		<if test="device_user_id != null and device_user_id != '' "><![CDATA[ AND CDC.DEVICE_USER_ID = #{device_user_id} ]]></if>
		<if test="device_functioin_code != null and device_functioin_code != '' "><![CDATA[ AND CDC.DEVICE_FUNCTIOIN_CODE = #{device_functioin_code} ]]></if>			                
		<![CDATA[ ORDER BY CDC.INSERT_TIME DESC) A ]]>
	</select>

	<!-- 根据分页条件查询分页结果 -->
	<select id="queryPanelOutContent" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT *
			  FROM (SELECT ROWNUM RN, A.*
			          FROM ( SELECT CDC.DEVICE_CONFIG_ID,
			                        CDC.DEVICE_ORGAN_CODE,
			                        (SELECT O.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CDC.DEVICE_ORGAN_CODE) DEVICE_ORGAN_NAME,
			                        CDC.DEVICE_USER_ID,
			                        (SELECT U.USER_NAME FROM APP___PAS__DBUSER.T_UDMP_USER U WHERE U.USER_ID=CDC.DEVICE_USER_ID) DEVICE_USER_IDS,
			                        CDC.DEVICE_FUNCTIOIN_CODE,
			                        CDC.INSERT_TIME,
			                        CDC.INSERT_BY,
			                        (SELECT U.USER_NAME FROM APP___PAS__DBUSER.T_UDMP_USER U WHERE U.USER_ID=CDC.INSERT_BY) INSERT_BYS
			                  FROM APP___PAS__DBUSER.T_COUNTER_DEVICE_CONFIG CDC WHERE 1=1
		]]>
		<if test="device_organ_code != null and device_organ_code != '' "><![CDATA[ AND CDC.DEVICE_ORGAN_CODE like #{device_organ_code}||'%' ]]></if>
		<if test="device_user_id != null and device_user_id != '' "><![CDATA[ AND CDC.DEVICE_USER_ID = #{device_user_id} ]]></if>
		<if test="device_functioin_code != null and device_functioin_code != '' "><![CDATA[ AND CDC.DEVICE_FUNCTIOIN_CODE = #{device_functioin_code} ]]></if>
		<![CDATA[ ORDER BY CDC.INSERT_TIME DESC ) A ) B WHERE B.RN > #{GREATER_NUM} AND B.RN <= #{LESS_NUM}]]>
	</select>

	<!-- 添加柜外清配置信息 -->
	<insert id="addCssPanelOut" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="device_config_id">
			SELECT APP___PAS__DBUSER.S_COUNTER_DEVICE_CONFIG__DC_ID.NEXTVAL
			FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_COUNTER_DEVICE_CONFIG(
				DEVICE_CONFIG_ID,
				DEVICE_ORGAN_CODE,
				DEVICE_USER_ID,
				DEVICE_FUNCTIOIN_CODE,
				INSERT_BY,
				INSERT_TIME,
				INSERT_TIMESTAMP,
				UPDATE_BY,
				UPDATE_TIME,
				UPDATE_TIMESTAMP) 
			VALUES (
				#{device_config_id,jdbcType=NUMERIC},
				#{device_organ_code,jdbcType=VARCHAR},
				#{device_user_id,jdbcType=NUMERIC},
				#{device_functioin_code,jdbcType=VARCHAR},
				#{insert_by,jdbcType=NUMERIC},
				CURRENT_TIMESTAMP,
				SYSDATE,
				#{update_by,jdbcType=NUMERIC},
				CURRENT_TIMESTAMP,
				SYSDATE ) 
		 ]]>
	</insert>

	<!-- 删除柜外清配置信息 -->
	<delete id="delPanelOut" parameterType="java.util.Map">
        <![CDATA[ DELETE FROM APP___PAS__DBUSER.T_COUNTER_DEVICE_CONFIG WHERE DEVICE_CONFIG_ID = #{device_config_id} ]]>
	</delete>

	<select id="queryDeviceConfig" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM APP___PAS__DBUSER.T_COUNTER_DEVICE_CONFIG WHERE DEVICE_USER_ID = #{device_user_id,jdbcType=NUMERIC} 
		AND　DEVICE_FUNCTIOIN_CODE = #{device_functioin_code,jdbcType=VARCHAR}
		]]>
	</select>

	<!-- 自定义SQL -->
	<select id="com_querySql" resultType="java.util.Map" parameterType="java.util.Map" statementType="STATEMENT">
		<![CDATA[${sql}]]>
	</select>
	
</mapper>