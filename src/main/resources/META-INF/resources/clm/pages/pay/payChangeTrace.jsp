<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript" src="clm/pages/pay/payChangeTrace.js" ></script>
<div layoutH="0" >
	<form id="pagerForm" method="post"
		action="clm/pay/findClaimPayChangeTrace_CLM_claimPayChangeAction.action?claimCaseVO.caseNo=${claimCaseVO.caseNo}&queryBranchFlow=${param.queryBranchFlow}">
		<input type="hidden" name="pageNum" value="${pageInfo.currentPage} " />
		<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
	</form>
	<!-- 查询事件访问路径 -->
	<form id="payChangeTraceJspForm"
		action="clm/pay/findClaimPayChangeTrace_CLM_claimPayChangeAction.action?claimCaseVO.caseNo=${claimCaseVO.caseNo}&queryBranchFlow=${param.queryBranchFlow}" method="post" 
		onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm">
	<!-- 查询区域 -->
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">查询条件</h1>
	</div>
		  <div class="pageFormInfoContent" > 
				<dl >	
					<dt>赔案号</dt>
					<dd>
						<input type="text"  
						id="caseNoPayChangeTrace" name="caseNo"  size="25" value="${caseNoFlag}" />
					</dd> 
				</dl>
		  <div class="pageFormdiv"><button type="submit" onclick="return change();" class="but_blue" >查询</button></div>
		  </div> 
  
  </form>
  
  <form>
	<!-- 显示数据列表区域 -->	
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">付款信息列表</h1>
	</div>
		<div class="tabdivclassbr">
			<table class="list main_dbottom" width="100%" id="payChangeTraceId">
				<thead>
					<tr>
          				<th nowrap>选择</th>
						<th nowrap>机构编码</th>
						<th nowrap>费用ID</th>
						<th nowrap>赔案号</th>
						<th nowrap>受益人</th>
						<th nowrap>付款金额</th>
						<th nowrap>不成功原因</th>
						<th nowrap>修改人</th>
						<th nowrap>复核人</th>
						<th nowrap>应付日期</th>
						<th nowrap>到账日期</th>
					</tr>
				</thead>
				<tbody align="center"  id="paychangeTbody">
						<!-- 循环显示数据 -->
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="20">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="20">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="currentPage.pageItems" status="st">
							<tr>
								<td><input type="checkbox" name="r1" style="border:0px;background:0px;width:auto; float: left;" 
											id='id<s:property value="#st.index" />'
											onclick="getSelectInfo(this)"
											value="${feeId}|${applyId}|${businessCode}|${logId}|${unitNumber}|${contraryPayFlag}|${payeeLegalPersonId}|${payeeRelation}" />
								</td>
								<td><div align="center">${organCode}</div></td>
								<td><div align="center">${feeId}</div></td>
								<td><div align="center">${businessCode}</div></td>
								<td><div align="center">${beneName}</div></td>
								<td><div align="center">${feeAmount}</div></td>
								<td><div align="center">${bankRetName}</div></td>
								<td><div align="center">${changeBy}</div></td>
								<td><div align="center">${auditBy}</div></td>
								<td><div align="center"><s:date name="dueTime" format="yyyy-MM-dd"></s:date></div></td>
								<td><div align="center"><s:date name="checkEnterTime" format="yyyy-MM-dd"></s:date></div></td>
							</tr> 
						</s:iterator>
					</tbody>
  		  </table>
  		  <!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
    </div>
  	
 
  	<div class="divfclass">
  	<input type="hidden" name="claimPayChangeVO.contraryPayFlag" value="${claimPayChangeVO.contraryPayFlag}" id="contraryPayFlag">
		<input type="hidden" name="claimPayChangeTraceVO.bfLegalPersonId" value="${claimPayChangeTraceVO.bfLegalPersonId}" id="bfLegalPersonId">
		<input type="hidden" name="claimPayChangeTraceVO.afLegalPersonId" value="${claimPayChangeTraceVO.afLegalPersonId}" id="afLegalPersonId">
		<input type="hidden" value="${claimPayChangeTraceVO.logId}" id="payChangeLogId">
		<h1><img src="clm/images/tubiao.png">变更前付款信息</h1>
	</div>
		  <div class="panelPageFormContent" > 
		  <dl style="width: 100%;">
					<dt>
						<a disabled="true" id ="bflegalPersonQueryPayeeStyle" href="javaScript:void(0)" onclick="bfqueryLegalPersonInfoPayee();" >
			<button type="button" class="but_blue " id="bflegalPersonQueryPayeeStyleButton" disabled="disabled" >法人信息录入</button></a>
			<a target="dialog" id ="bflegalPersonQueryPayee" href="javaScript:void(0)" style="display:none;"  width="1000" height="450"  > 法人信息录入</a>
					</dt>
					<dt>
					对公支付，请勾选<input type="checkbox" id="bfcontraryPayFlagCheckBoxId" onclick="chooseMethod();"/>
					</dt>
					</dl>
				<dl>	
					<dt>付款方式</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfPayMode" name="claimPayChangeTraceVO.bfPayMode" value="${claimPayChangeTraceVO.bfPayMode}" size="25" />
					</dd> 
				</dl> 
     		<dl>	
					<dt>银行代码</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfBankCode" name="claimPayChangeTraceVO.bfBankCode" value="${claimPayChangeTraceVO.bfBankCode}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>账户名称</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfAccountName" name="claimPayChangeTraceVO.bfAccountName" value="${claimPayChangeTraceVO.bfAccountName}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>银行账号</dt>
					<dd> 
						<input type="expandBankAccount" readonly="true" 
						id="bfAccountNo" name="claimPayChangeTraceVO.bfAccountNo" value="${claimPayChangeTraceVO.bfAccountNo}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>领款人与受益人关系</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfPayeeRelation" name="claimPayChangeTraceVO.bfPayeeRelation" value="${claimPayChangeTraceVO.bfPayeeRelation}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>领款人</dt>
					<dd>
						<input type="text" readonly="true" 
						id="bfPayeeName" name="claimPayChangeTraceVO.bfPayeeName" value="${claimPayChangeTraceVO.bfPayeeName}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>性别</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfPayeeGender" name="claimPayChangeTraceVO.bfPayeeGender" value="${claimPayChangeTraceVO.bfPayeeGender}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>出生日期</dt>
					<dd>
						<input type="text" readonly="true" 
						id="bfPayeeBirth" name="claimPayChangeTraceVO.bfPayeeBirth" value="${claimPayChangeTraceVO.bfPayeeBirth}" size="25" /> 
					</dd> 
				</dl>
				<dl>	
					<dt>证件类型</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfPayeeCertiType" name="claimPayChangeTraceVO.bfPayeeCertiType" value="${claimPayChangeTraceVO.bfPayeeCertiType}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>证件号码</dt>
					<dd>
						<input type="expandCertiCode" readonly="true" 
						id="bfPayeeCertiNo" name="claimPayChangeTraceVO.bfPayeeCertiNo" value="${claimPayChangeTraceVO.bfPayeeCertiNo}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>领款人国籍</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfPayeeNation" name="claimPayChangeTraceVO.bfPayeeNation" value="${claimPayChangeTraceVO.bfPayeeNation}" size="25" />
					</dd> 
				</dl>
				<dl>
				</dl>
				<dl>	
					<dt>证件有效起期</dt>
					<dd>
						<input type="text" readonly="true" 
						id="bfPayeeCertiStart" name="claimPayChangeTraceVO.bfPayeeCertiStart" value="${claimPayChangeTraceVO.bfPayeeCertiStart}" size="25" /> 
					</dd> 
				</dl>
				<dl>	
					<dt>证件有效止期</dt>
					<dd>
						<input type="text" readonly="true" 
						id="bfPayeeCertiEnd" name="claimPayChangeTraceVO.bfPayeeCertiEnd" value="${claimPayChangeTraceVO.bfPayeeCertiEnd}" size="25" /> 
					</dd> 
				</dl>
				<dl>
					<dt>领款人职业代码</dt>
					<dd>
					<dd>
						<input type="text" readonly="true"
							   id="bfPayeeJobIdFire" name="claimPayChangeTraceVO.bfPayeeJobCode" value="${claimPayChangeTraceVO.bfPayeeJobCode}" size="25" />
					</dd>
					</dd>
				</dl>
				<dl>
					<dt>领款人电话</dt>
					<dd>
						<input type="text" readonly="true"
							   id="bfPayeePhone" name="claimPayChangeTraceVO.bfPayeePhone" value="${claimPayChangeTraceVO.bfPayeePhone}" size="25" />
					</dd>
				</dl>
				<div class="mian_site">
					<dl>
						<dt>领款人地址</dt>
					</dl>
				  	<div class="main_detail">
						<dl>
							<dd>
								<input type="text" readonly="true"
									   id="bfPayeeState" name="claimPayChangeTraceVO.bfPayeeState" value="${claimPayChangeTraceVO.bfPayeeState}" size="25" />
								<span>省/直辖市</span>
							</dd>
					  	</dl>
					  	<dl>
							<dd>
							  <input type="text" readonly="true"
									 id="bfPayeeCity" name="claimPayChangeTraceVO.bfPayeeCity" value="${claimPayChangeTraceVO.bfPayeeCity}" size="25" />
							  <span>市</span>
							</dd>
					  </dl>
					  	<dl>
						  <dd>
							  <input type="text" readonly="true"
									 id="bfPayeeDistrict" name="claimPayChangeTraceVO.bfPayeeDistrict" value="${claimPayChangeTraceVO.bfPayeeDistrict}" size="25" />
							  <span>区/县</span>
						  </dd>
					  </dl>
					  	<dl>
						  <dd>
							  <input type="text" readonly="true"
									 id="bfPayeeAddress" name="claimPayChangeTraceVO.bfPayeeAddress" value="${claimPayChangeTraceVO.bfPayeeAddress}" size="25" />
							  <span>乡镇/街道</span>
						  </dd>
					  </dl>
				    </div>
				</div>
				<dl>	
					<dt>开户行信息</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="bfBankOfDeposit" name="claimPayChangeTraceVO.bfBankOfDeposit" value="${claimPayChangeTraceVO.bfBankOfDeposit}" size="25" />
					</dd> 
				</dl>
		  </div>
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">变更后付款信息</h1>
	</div>
		  <div class="panelPageFormContent" > 
		  <dl style="width: 100%;">
					<dt>
						<a disabled="true" id ="aflegalPersonQueryPayeeStyle" href="javaScript:void(0)" onclick="afqueryLegalPersonInfoPayee();" >
							<button type="button" class="but_blue " id="aflegalPersonQueryPayeeStyleButton" disabled="disabled" >法人信息录入</button>
						</a>
						<a target="dialog" id ="aflegalPersonQueryPayee" href="javaScript:void(0)" style="display:none;"  width="1000" height="450"  > 法人信息录入</a>
					</dt>
					<dt>
						对公支付，请勾选<input type="checkbox" id="afcontraryPayFlagCheckBoxId" onclick="chooseMethod();"/>
					</dt>
		  </dl>
				<dl>
					<dt>付款方式</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afPayMode" name="claimPayChangeTraceVO.afPayMode" value="${claimPayChangeTraceVO.afPayMode}" size="25" />
					</dd> 
				</dl> 
     		<dl>	
					<dt>银行代码</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afBankCode" name="claimPayChangeTraceVO.afBankCode" value="${claimPayChangeTraceVO.afBankCode}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>账户名称</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afAccountName" name="claimPayChangeTraceVO.afAccountName" value="${claimPayChangeTraceVO.afAccountName}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>银行账号</dt>
					<dd> 
						<input type="expandBankAccount" readonly="true" 
						id="afAccountNo" name="claimPayChangeTraceVO.afAccountNo" value="${claimPayChangeTraceVO.afAccountNo}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>领款人与受益人关系</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afPayeeRelation" name="claimPayChangeTraceVO.afPayeeRelation" value="${claimPayChangeTraceVO.afPayeeRelation}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>领款人</dt>
					<dd>
						<input type="text" readonly="true" 
						id="afPayeeName" name="claimPayChangeTraceVO.afPayeeName" value="${claimPayChangeTraceVO.afPayeeName}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>性别</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afPayeeGender" name="claimPayChangeTraceVO.afPayeeGender" value="${claimPayChangeTraceVO.afPayeeGender}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>出生日期</dt>
					<dd>
						<input type="text" readonly="true" 
						id="afPayeeBirth" name="claimPayChangeTraceVO.afPayeeBirth" value="${claimPayChangeTraceVO.afPayeeBirth}" size="25" /> 
					</dd> 
				</dl>
				<dl>	
					<dt>证件类型</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afPayeeCertiType" name="claimPayChangeTraceVO.afPayeeCertiType" value="${claimPayChangeTraceVO.afPayeeCertiType}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>证件号码</dt>
					<dd>
						<input type="expandCertiCode" readonly="true" 
						id="afPayeeCertiNo" name="claimPayChangeTraceVO.afPayeeCertiNo" value="${claimPayChangeTraceVO.afPayeeCertiNo}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>领款人国籍</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afPayeeNation" name="claimPayChangeTraceVO.afPayeeNation" value="${claimPayChangeTraceVO.afPayeeNation}" size="25" />
					</dd> 
				</dl>
				<dl>
				</dl>
				<dl>	
					<dt>证件有效起期</dt>
					<dd>
						<input type="text" readonly="true" 
						id="afPayeeCertiStart" name="claimPayChangeTraceVO.afPayeeCertiStart" value="${claimPayChangeTraceVO.afPayeeCertiStart}" size="25" /> 
					</dd> 
				</dl>
				<dl>	
					<dt>证件有效止期</dt>
					<dd>
						<input type="text" readonly="true" 
						id="afPayeeCertiEnd" name="claimPayChangeTraceVO.afPayeeCertiEnd" value="${claimPayChangeTraceVO.afPayeeCertiEnd}" size="25" /> 
					</dd> 
				</dl>
				<dl>
				  <dt>领款人职业代码</dt>
				  <dd>
					  <input type="text" readonly="true"
							 id="afPayeeJobIdFire" name="claimPayChangeTraceVO.afPayeeJobCode" value="${claimPayChangeTraceVO.afPayeeJobCode}" size="25" />
				  </dd>
				</dl>
				<dl>
				  <dt>领款人电话</dt>
				  <dd>
					  <input type="text" readonly="true"
							 id="afPayeePhone" name="claimPayChangeTraceVO.afPayeePhone" value="${claimPayChangeTraceVO.afPayeePhone}" size="25" />
				  </dd>
				</dl>
				<div class="mian_site">
				  <dl>
					  <dt>领款人地址</dt>
				  </dl>
				  <div class="main_detail">
					  <dl>
						  <dd>
							  <input type="text" readonly="true"
									 id="afPayeeState" name="claimPayChangeTraceVO.afPayeeState" value="${claimPayChangeTraceVO.afPayeeState}" size="25" />
							  <span>省/直辖市</span>
						  </dd>
					  </dl>
					  <dl>
						  <dd>
							  <input type="text" readonly="true"
									 id="afPayeeCity" name="claimPayChangeTraceVO.afPayeeCity" value="${claimPayChangeTraceVO.afPayeeCity}" size="25" />
							  <span>市</span>
						  </dd>
					  </dl>
					  <dl>
						  <dd>
							  <input type="text" readonly="true"
									 id="afPayeeDistrict" name="claimPayChangeTraceVO.afPayeeDistrict" value="${claimPayChangeTraceVO.afPayeeDistrict}" size="25" />
							  <span>区/县</span>
						  </dd>
					  </dl>
					  <dl>
						  <dd>
							  <input type="text" readonly="true"
									 id="afPayeeAddress" name="claimPayChangeTraceVO.afPayeeAddress" value="${claimPayChangeTraceVO.afPayeeAddress}" size="25" />
							  <span>乡镇/街道</span>
						  </dd>
					  </dl>
				  </div>
				</div>
				<dl>
				<dl>	
					<dt>修改原因</dt>
					<dd>
						<input type="text" readonly="true" 
						id="payChangeReason" name="claimPayChangeVO.payChangeReason" value="${claimPayChangeVO.payChangeReason}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>原因说明</dt>
					<dd>
						<input type="text" readonly="true" 
						id="changeDesc" name="claimPayChangeVO.changeDesc" value="${claimPayChangeVO.changeDesc}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>修改人</dt>
					<dd>
						<input type="text" readonly="true" 
						id="changeBy" name="claimPayChangeVO.changeBy" value="${claimPayChangeVO.changeBy}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>修改时间</dt>
					<dd>
						<input type="text" readonly="true" 
						id="changeTime" name="claimPayChangeVO.changeTime" value="${claimPayChangeVO.changeTime}" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>开户行信息</dt>
					<dd> 
						<input type="text" readonly="true" 
						id="afBankOfDeposit" name="claimPayChangeTraceVO.afBankOfDeposit" value="${claimPayChangeTraceVO.afBankOfDeposit}" size="25" />
					</dd> 
				</dl>
		  </div> 
  
  <div class="divfclass">
		<h1><img src="clm/images/tubiao.png">付费变更复核</h1>
	</div>
		  <div class="panelPageFormContent" > 
				<dl>	
					<dt>复核结论</dt>
					<dd>
						<input type="text" readonly="true" 
						id="payAuditCon" name="claimPayChangeVO.payAuditCon" value="" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>复核人</dt>
					<dd>
						<input type="text" readonly="true" 
						id="auditBy" name="claimPayChangeVO.auditBy" value="" size="25" />
					</dd> 
				</dl>
				<dl>	
					<dt>复核时间</dt>
					<dd>
						<input type="text" readonly="true" 
						id="auditTime" name="claimPayChangeVO.auditTime" value="" size="25" />
					</dd> 
				</dl>
				<dl></dl>
				<dl>	
					<dt>复核意见</dt>
					<dd>
						<input type="text" readonly="true" 
						id="auditDesc" name="claimPayChangeVO.auditDesc" value="${claimPayChangeVO.auditDesc}" size="93" />
					</dd> 
				</dl> 
		  </div> 
	
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">操作轨迹</h1>
	</div>
				<div class="tabdivclass">
					<table class="list" width="100%">
						<thead>
							<tr>
								<th>序号</th>
								<th>操作岗位</th>
								<th>操作员</th>
								<th>任务流传时间</th>
								<th>任务处理完成时间</th>
							</tr>
						</thead>
						<tbody id="operateTbody" align="center">
								<!-- 循环显示数据 -->
								
						</tbody>
  		  			</table>
				</div>
			
  	</form>
  	<div class="formBarButton" id="payChangeTraceClose">
		<ul> 
			<li>
			<button type="button" class="but_gray" onclick="exit();" >退出</button>
			</li>
		</ul>
	</div>
</div>


