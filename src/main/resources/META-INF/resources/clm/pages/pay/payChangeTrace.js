//格式化时间
Date.prototype.format = function(format) {
	var o = {
		"M+" : this.getMonth() + 1, // month
		"d+" : this.getDate(), // day
		"h+" : this.getHours(), // hour
		"m+" : this.getMinutes(), // minute
		"s+" : this.getSeconds(), // second
		"q+" : Math.floor((this.getMonth() + 3) / 3), // quarter
		"S" : this.getMilliseconds()
	// millisecond
	};
	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (this.getFullYear() + "")
				.substr(4 - RegExp.$1.length));
	for ( var k in o)
		if (new RegExp("(" + k + ")").test(format))
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k]
					: ("00" + o[k]).substr(("" + o[k]).length));
	return format;
};

//json日期格式转换为正常格式
function jsonDateFormat(jsonDate) {
    var date = new Date();
    date.setTime(jsonDate.time);
    date.setHours(jsonDate.hours);
    date.setMinutes(jsonDate.minutes);
    date.setSeconds(jsonDate.seconds);
    return date.format('yyyy-MM-dd');
}

// 复核页面进入 默认选中第一条
$(function(){
	$("#payChangeTraceId tbody tr td:first input", navTab.getCurrentPanel()).click();
	var selectedValue = $("#payChangeTraceId tbody tr td:first input", navTab.getCurrentPanel()).val().split("|");
	var unitNumber = selectedValue[4];
	var payeeRelation = selectedValue[7];
	if($("#payChangeTraceId tbody tr td:first input", navTab.getCurrentPanel()).attr("checked") == "checked"){
		$("#paychangeTbody", navTab.getCurrentPanel()).find("tr").each(function(){
			var selectedValue1 =  $(this).find("td:eq(0)").find("input").val().split("|");
			var unitNumber1 = selectedValue1[4];
			var payeeRelation1 = selectedValue1[7];
			// 已勾选的记录取消勾选
			if($(this).find("td:eq(0)").find("input").attr("checked") == "checked"){
				if(unitNumber != unitNumber1 || payeeRelation != payeeRelation1){
					$(this).find("td:eq(0)").find("input").removeAttr("checked");
				}
			}
			// 用户选择某一条记录时，自动勾选赔案号、领款人、领款人与受益人关系、银行账号与所选记录一致的记录。
			if(unitNumber == unitNumber1 && payeeRelation == payeeRelation1){
				$(this).find("td:eq(0)").find("input").attr("checked","checked");
			}
		});
	}else{ 
		$("#paychangeTbody", navTab.getCurrentPanel()).find("tr").each(function(){
			var selectedValue1 =  $(this).find("td:eq(0)").find("input").val().split("|");
			var unitNumber1 = selectedValue1[4];
			var payeeRelation1 = selectedValue1[7];
			if(unitNumber == unitNumber1 && payeeRelation == payeeRelation1){
				$(this).find("td:eq(0)").find("input").removeAttr("checked");
			}
		});
	}
	
});

function getTime(timeValue){
	return new Date().format('yyyy-MM-dd');
	 
}
function change(){
	var caseNo=$("#caseNoPayChangeTrace", navTab.getCurrentPanel()).val();
	if(caseNo==""){
		alertMsg.error ("请至少输入一项查询条件");
		return false;
	}
	return true;
}
//取得选中的变更信息
function getSelectInfo(delect) {
	
	var selectedValue = $(delect).val().split("|");
	//修改后的值
	var feeId =  selectedValue[0];
	var applyId = selectedValue[1];
	var caseNo = selectedValue[2];
	var logId = selectedValue[3];
	var unitNumber = selectedValue[4];
	var contraryPayFlag = selectedValue[5];
	var payeeLegalPersonId = selectedValue[6];
	var payeeRelation = selectedValue[7];
	$("#contraryPayFlag", navTab.getCurrentPanel()).val(contraryPayFlag);
	$("#payeeLegalPersonId", navTab.getCurrentPanel()).val(payeeLegalPersonId);

	if($(delect).attr("checked") == "checked"){
		$("#paychangeTbody", navTab.getCurrentPanel()).find("tr").each(function(){
			var selectedValue1 =  $(this).find("td:eq(0)").find("input").val().split("|");
			var unitNumber1 = selectedValue1[4];
			var payeeRelation1 = selectedValue1[7];
			// 已勾选的记录取消勾选
			if($(this).find("td:eq(0)").find("input").attr("checked") == "checked"){
				if(unitNumber != unitNumber1 || payeeRelation != payeeRelation1 ){
					$(this).find("td:eq(0)").find("input").removeAttr("checked");
				}
			}
			// 用户选择某一条记录时，自动勾选赔案号、领款人、领款人与受益人关系、银行账号与所选记录一致的记录。
			if(unitNumber == unitNumber1 && payeeRelation == payeeRelation1){
				$(this).find("td:eq(0)").find("input").attr("checked","checked");
			}
		});
	}else{ 
		$("#paychangeTbody", navTab.getCurrentPanel()).find("tr").each(function(){
			var selectedValue1 =  $(this).find("td:eq(0)").find("input").val().split("|");
			var unitNumber1 = selectedValue1[4];
			var payeeRelation1 = selectedValue1[7]; 
			if(unitNumber == unitNumber1 && payeeRelation == payeeRelation1){
				$(this).find("td:eq(0)").find("input").removeAttr("checked");
			}
		});
	}
	$.ajax({
		'url':'clm/pay/getTrace_CLM_claimPayChangeAction.action?feeId='+feeId+'&applyId='+applyId+'&caseNo='+caseNo+'&logId='+logId,
		'type':'POST',
		'global':false,
		'datatype':'json',
		'success':function(data){	
			var data=eval("("+data+")");
			//修改前法人id
			if(data[0].trace.bfLegalPersonId != ''){
				$("#bfLegalPersonId",navTab.getCurrentPanel()).val(data[0].trace.bfLegalPersonId);
				$("#bfcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
				$("#bfcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("readonly","readonly");
				$("#bflegalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",false);
				$("#bflegalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
			}else{
				$("#bfcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
				$("#bfcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("readonly","readonly");
				$("#bflegalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",true);
				$("#bflegalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
			}
			//修改后法人id
			if(data[0].trace.afLegalPersonId != ''){
				$("#afLegalPersonId",navTab.getCurrentPanel()).val(data[0].trace.afLegalPersonId);
				$("#afcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
				$("#afcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("readonly","readonly");
				$("#aflegalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",false);
				$("#aflegalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
			}else{
				$("#afcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
				$("#afcontraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("readonly","readonly");
				$("#aflegalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",true);
				$("#aflegalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
			}
			$("#payChangeLogId",navTab.getCurrentPanel()).val(data[0].trace.logId);
			//修改后的值
			$("#afPayMode",navTab.getCurrentPanel()).val(data[0].trace.afPayMode);
			$("#afBankCode",navTab.getCurrentPanel()).val(data[0].trace.afBankCode);
			$("#afAccountName",navTab.getCurrentPanel()).val(data[0].trace.afAccountName);
			$("#afAccountNo",navTab.getCurrentPanel()).val(data[0].trace.afAccountNo);
			$("#afBankOfDeposit",navTab.getCurrentPanel()).val(data[0].trace.afBankOfDeposit);
			$("#afPayeeRelation",navTab.getCurrentPanel()).val(data[0].trace.afPayeeRelation);
			$("#afPayeeName",navTab.getCurrentPanel()).val(data[0].trace.afPayeeName);
			if(data[0].trace.afPayeeGender == 1){
				$("#afPayeeGender",navTab.getCurrentPanel()).val("男");
			}else if(data[0].trace.afPayeeGender == 9){
				$("#afPayeeGender",navTab.getCurrentPanel()).val("未知");
			} else if(data[0].trace.afPayeeGender == 2){
				$("#afPayeeGender",navTab.getCurrentPanel()).val("女");
			}else{
				$("#afPayeeGender",navTab.getCurrentPanel()).val("");
			}
			$("#afPayeeBirth",navTab.getCurrentPanel()).val(data[0].trace.afBirth);
			
			$("#afPayeeCertiType",navTab.getCurrentPanel()).val(data[0].trace.afPayeeCertiType);
			$("#afPayeeCertiNo",navTab.getCurrentPanel()).val(data[0].trace.afPayeeCertiNo);
			$("#afPayeeNation",navTab.getCurrentPanel()).val(data[0].trace.afPayeeNation);
			$("#afPayeeCertiStart",navTab.getCurrentPanel()).val(data[0].trace.afCertiStart);
			$("#afPayeeCertiEnd",navTab.getCurrentPanel()).val(data[0].trace.afCertiEnd);
			$("#payChangeReason",navTab.getCurrentPanel()).val(data[0].apply.payChangeReason);
			
			$("#changeDesc",navTab.getCurrentPanel()).val(data[0].apply.changeDesc);
			$("#changeBy",navTab.getCurrentPanel()).val(data[0].apply.changeBy);
			$("#changeTime",navTab.getCurrentPanel()).val(jsonDateFormat(data[0].apply.changeTime));
			
			//复核结论
			$("#payAuditCon",navTab.getCurrentPanel()).val(data[0].apply.payAuditConName);
			$("#auditDesc",navTab.getCurrentPanel()).val(data[0].apply.auditDesc);
			$("#auditBy",navTab.getCurrentPanel()).val(data[0].apply.auditByName);
			if(data[0].apply.auditTime!=null&&data[0].apply.auditTime!=''){
				$("#auditTime",navTab.getCurrentPanel()).val(jsonDateFormat(data[0].apply.auditTime));
			}
			
			$("#afPayeePhone",navTab.getCurrentPanel()).val(data[0].trace.afPayeePhone);
			$("#afPayeeJobIdFire",navTab.getCurrentPanel()).val(data[0].trace.afPayeeJobCode);
			$("#afPayeeState",navTab.getCurrentPanel()).val(data[0].trace.afPayeeState);
			$("#afPayeeCity",navTab.getCurrentPanel()).val(data[0].trace.afPayeeCity);
			$("#afPayeeDistrict",navTab.getCurrentPanel()).val(data[0].trace.afPayeeDistrict);
			$("#afPayeeAddress",navTab.getCurrentPanel()).val(data[0].trace.afPayeeAddress);
			
			//alert("time:"+getTime(new Date(data[0].apply.auditTime.time)));
			//保存修改前的值
			$("#bfPayMode",navTab.getCurrentPanel()).val(data[0].trace.bfPayMode);
			$("#bfBankCode",navTab.getCurrentPanel()).val(data[0].trace.bfBankCode);
			$("#bfAccountName",navTab.getCurrentPanel()).val(data[0].trace.bfAccountName);
			$("#bfAccountNo",navTab.getCurrentPanel()).val(data[0].trace.bfAccountNo);
			$("#bfBankOfDeposit",navTab.getCurrentPanel()).val(data[0].trace.bfBankOfDeposit);
			$("#bfPayeeRelation",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeRelation);
			$("#bfPayeeName",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeName);
			if(data[0].trace.bfPayeeGender == 1){
				$("#bfPayeeGender",navTab.getCurrentPanel()).val("男");
			}else if(data[0].trace.bfPayeeGender == 9){
				$("#bfPayeeGender",navTab.getCurrentPanel()).val("未知");
			}else if(data[0].trace.bfPayeeGender == 2){
				$("#bfPayeeGender",navTab.getCurrentPanel()).val("女");
			}else{
				$("#bfPayeeGender",navTab.getCurrentPanel()).val("");
			}
			$("#bfPayeeBirth",navTab.getCurrentPanel()).val(data[0].trace.bfBirth);
			
			$("#bfPayeeCertiType",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeCertiType);
			$("#bfPayeeCertiNo",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeCertiNo);
			$("#bfPayeeNation",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeNation);
			$("#bfPayeeCertiStart",navTab.getCurrentPanel()).val(data[0].trace.bfCertiStart);
			$("#bfPayeeCertiEnd",navTab.getCurrentPanel()).val(data[0].trace.bfCertiEnd);

			$("#bfPayeePhone",navTab.getCurrentPanel()).val(data[0].trace.bfPayeePhone);
			$("#bfPayeeJobIdFire",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeJobCode);
			$("#bfPayeeState",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeState);
			$("#bfPayeeCity",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeCity);
			$("#bfPayeeDistrict",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeDistrict);
			$("#bfPayeeAddress",navTab.getCurrentPanel()).val(data[0].trace.bfPayeeAddress);
		
			//遍历操作轨迹
			var operateL = data[0].operateTrace.length;
			$("#operateTbody", navTab.getCurrentPanel()).find("tr").remove();
			for(var n=0;n<operateL;n++){
				var m = n+1;
				var insertHtml ="<tr>"
								+"<td>"+m+"</td>"
								+"<td>"+data[0].operateTrace[n].taskNode+"</td>"
								+"<td>"+data[0].operateTrace[n].acceptorName+"</td>"
								+"<td>"+data[0].operateTrace[n].starTime+"</td>"
								+"<td>"+data[0].operateTrace[n].endTime+"</td>"
								+"</tr>";
				$("#operateTbody", navTab.getCurrentPanel()).append(
										insertHtml);						
			
			}
		
		}
	});
}
function exit(){
	navTab.closeCurrentTab();
}
//退出
/*function exitPayChangeTraceE(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
}
*/


//查询变更前领款人法人录入信息
function bfqueryLegalPersonInfoPayee() {
	var listId = $("#bfLegalPersonId", navTab.getCurrentPanel()).val();
	var payChangeLogId = $("#payChangeLogId", navTab.getCurrentPanel()).val();
	var operationFlag = 'look';
	var personFlag = 'bfPayChange';
	var url = "clm/pay/payChangeLegalPersonInfoInit_CLM_legalPersonAction.action?operationFlag="+operationFlag+"&personFlag="+personFlag+"&legalPersonInfoVO.listId="+listId+"&relaListId="+payChangeLogId;
	$("#bflegalPersonQueryPayee", navTab.getCurrentPanel()).attr("href", url).click();
}
//查询变更后领款人法人录入信息
function afqueryLegalPersonInfoPayee() {
	var listId = $("#afLegalPersonId", navTab.getCurrentPanel()).val();	
	var payChangeLogId = $("#payChangeLogId", navTab.getCurrentPanel()).val();
	var operationFlag = 'look';
	var personFlag = 'afPayChange';
	var url = "clm/pay/payChangeLegalPersonInfoInit_CLM_legalPersonAction.action?operationFlag="+operationFlag+"&personFlag="+personFlag+"&legalPersonInfoVO.listId="+listId+"&relaListId="+payChangeLogId;
	$("#aflegalPersonQueryPayee", navTab.getCurrentPanel()).attr("href", url).click();
}
