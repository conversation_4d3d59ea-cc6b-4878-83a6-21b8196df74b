<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!-- 意外细节必调和责免轨迹 -->
<form id="pagerForm" method="post"
	action="clm/parameter/findYyxjDutyFeeMbSur_CLM_claimIntelPortraitConfigAction.action">
	<input type="hidden" name="pageNum" vaule="${claimYyxjLiabSurveyVOPage.pageNo} " />
	<input type="hidden" name="numPerPage" value="${claimYyxjLiabSurveyVOPage.pageSize}" />
	<input type="hidden" name="claimYyxjLiabSurveyVO.accReason" value="${claimYyxjLiabSurveyVO.accidentDetail}" />
</form>
<div>
	<table class="list" id="accidentDetailAdjustDutyFree" width="100%">
		<thead>
			<tr align="center">
				<th nowrap>选择</th>
				<th nowrap>意外细节代码</th>
				<th nowrap>意外细节名称</th>
				<th nowrap>意外等级</th>
				<th nowrap>责任免除</th>
				<th nowrap>操作</th>
			</tr>
		</thead>
		<tbody>
			<s:iterator value="claimYyxjLiabSurveyVOPage.pageItems" var="status"
				status="st">
				<tr align="center">
					<td align="center"><input type="radio" name="danXuan"
						onclick="showDetails(this);" value="${listId}|${accidentDetail }|${accidentName }|${accidentLevel }|${liabFrom }"></td>
					<td>${accidentDetail }</td>
					<td>${accidentName }</td>
					<td>${accidentLevel }</td>
					<td><Field:codeValue value="${liabFrom }" tableName="APP___CLM__DBUSER.T_YES_NO" /></td>
					<td><a title="删除" class="btnDel" id='delButton'
						href='javascript:void(0);'
						onclick="checkDelete('${listId}',this);">删除</a></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
				name="select"
				onchange="navTabPageBreak({numPerPage:this.value},'accidentDetailAdjustDutyFree')"
				value="claimYyxjLiabSurveyVOPage.pageSize">
			</s:select>
			<span>条，共${claimYyxjLiabSurveyVOPage.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			rel="accidentDetailAdjustDutyFree"
			totalCount="${claimYyxjLiabSurveyVOPage.total}"
			numPerPage="${claimYyxjLiabSurveyVOPage.pageSize}" pageNumShown="20"
			currentPage="${claimYyxjLiabSurveyVOPage.pageNo}"></div>
	</div>
</div>