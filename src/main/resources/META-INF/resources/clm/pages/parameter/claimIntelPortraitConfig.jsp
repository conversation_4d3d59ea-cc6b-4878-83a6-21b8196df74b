<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript"
	src="clm/js/commonMianBox.js">
	
</script>
<%-- <script type="text/javascript" language="javascript"
	src="clm/pages/report/claimReportConfrim.js">
</script> --%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript" src="clm/pages/sign/signadd_checkInfo.js"></script>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript">
	//点击配置轨迹查询按钮
	function selectAccidentResultLog() {
		if ($("#busiProdCodes", navTab.getCurrentPanel()).val() == ""
				|| $("#busiProdCodes", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("带*信息不能为空");
			return;
		}
		$("#selectAccidentResultLog", navTab.getCurrentPanel()).submit();

	};

	//出险结果页面查询
	function accidentVOResultPageSign(k) {
		//先给出险结果排序，防止出现重复的序号
		var trs = $("#tbodysIdSign", navTab.getCurrentPanel()).find("tr");
		for (var i = 0; i < trs.length; i++) {
			$("#tbodysIdSign", navTab.getCurrentPanel()).find(
					"tr:eq(" + i + ")").find("td:eq(0)").find("input").val(
					i + 1);
		}
		$("#accidentResultBiaoIdSign", navTab.getCurrentPanel()).val(
				$(k).parent().parent().find("td:eq(0)").find("input").val());
		$("#accidentVOResultPageIdSign", navTab.getCurrentPanel())
				.attr(
						"href",
						"clm/report/accResultQueryFollowInit_CLM_accResultQueryFollowAction.action?reportFlag=sign");
		$("#accidentVOResultPageIdSign", navTab.getCurrentPanel()).click();
	}

	//点击配置查询按钮
	function selectAccidentResult() {
		$("#selectAccidentResult", navTab.getCurrentPanel()).submit();
	};

	//新增按钮
	function insert() {
		$("#BusiMatch", navTab.getCurrentPanel()).css("display", "block");
		$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled");
		// 默认 "按险种配置"
		$("#accidentResult", navTab.getCurrentPanel()).hide();
		$("#busiProdResult", navTab.getCurrentPanel()).show();
		$("#inputMethod", navTab.getCurrentPanel()).attr("value",1);
		$("#inputMethod", navTab.getCurrentPanel()).prev().empty();
		$("#inputMethod", navTab.getCurrentPanel()).prev().append("按险种配置");
		$("#inputMethod", navTab.getCurrentPanel()).find("option").each(
				function() {
					if ($(this).val() === 1) {
						$(this).attr("selected", true);
					} else {
						$(this).attr("selected", false);
					}
				}
		);
		$("#inputMethod", navTab.getCurrentPanel()).prev().removeAttr("disabled");
		$("#inputMethodFlag", navTab.getCurrentPanel()).val(1);
		$("#saveClaimAccResultBusilistId", navTab.getCurrentPanel()).val("");
		clearForm();
	}

	//保存按钮
	function saveSet() {
		$("#ruleProductList option", navTab.getCurrentPanel()).each(function() {
			this.selected = true;
		});
		$("#saveCxjgBusiMatch", navTab.getCurrentPanel()).hide();
		$("#saveCxjgBusiMatch", navTab.getCurrentPanel()).submit();
	};

	//保存回调方法
	function saveNewsMethod(json) {
		if (json.statusCode == DWZ.statusCode.ok) {
			alertMsg.correct(json.message);
			selectAccidentResult();
		} else {
			alertMsg.error(json.message);
		}
	}

	//重置 
	function clearForm() {
		$("#businessSearchId").selectMyComBox("");
		$("#busiFrom").selectMyComBox("");
		$("#accResult2").selectMyComBox("");
		$("#accResult2Str").selectMyComBox("");
		// 移除出险结果
		$("#tbodysIdSign", navTab.getCurrentPanel()).find("tr").remove();
		$("#configBusiProdCodes", navTab.getCurrentPanel()).prev().val("")
		return AppendItem('ruleProductList', 'ruleAllProduct', true);

	}

	//删除操作  完成
	function deleteCxjgBusiMatch(value, selectVal) {

		alertMsg
				.confirm(
						"请确认是否要删除",
						{
							okCall : function() {
								$
										.ajax({
											url : "clm/parameter/delCxjgBusiMatch_CLM_claimIntelPortraitConfigAction.action",
											type : "POST",
											data : {
												'claimAccResultBusiVO.listId' : value
											},
											cache : false,
											async : false,
											success : function(data) {
												/* var json = DWZ.jsonEval(data);
												if (json.statusCode == "200") {
													alertMsg.correct("删除成功！");
													$(selectVal).parent().parent().remove();
												} else {
													alertMsg.error(json.message);
												}; */
											}
										});
							},
							cancelCall : function() {
								return;
							}
						});
	}

	//选择
	function choiceAccidentResult(arg) {
		debugger
		clearForm();
		$("#BusiMatch", navTab.getCurrentPanel()).css("display", "block");
		$("#BusiMatch", navTab.getCurrentPanel()).removeAttr("disabled");
		$("#saveCxjgBusiMatch", navTab.getCurrentPanel()).show();
		$(arg).parent().parent().find("td:eq(0)").find("input:radio").attr("checked", true);
		var selectedValue = $(arg).val().split("|");
		$("#saveClaimAccResultBusilistId", navTab.getCurrentPanel()).val(selectedValue[0]);
		$("#accResult2", navTab.getCurrentPanel()).val(selectedValue[1]);
		$("#accResult2Str", navTab.getCurrentPanel()).val(selectedValue[2]);
		
		// 默认显示为“按出险结果配置”，且不可修改。
		$("#busiProdResult", navTab.getCurrentPanel()).hide();
		$("#accidentResult", navTab.getCurrentPanel()).show();
		$("#inputMethod", navTab.getCurrentPanel()).attr("value",0);
		$("#inputMethod", navTab.getCurrentPanel()).prev().empty();
		$("#inputMethod", navTab.getCurrentPanel()).prev().append("按出险结果配置");
		$("#inputMethod", navTab.getCurrentPanel()).find("option").each(
				function() {
					if ($(this).val() === 0) {
						$(this).attr("selected", true);
					} else {
						$(this).attr("selected", false);
					}
				}
		);
		$("#inputMethod", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#inputMethodFlag", navTab.getCurrentPanel()).val(0);
		// 移除保存按钮的禁用状态
		$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled");
		debugger
		// 责免险种列表赋值
		if (selectedValue[3] != null && selectedValue[3] !== '') {
			// 删除 ruleProductList 中的所有选项
			$("#ruleProductList", navTab.getCurrentPanel()).find("option").remove();
			var ruleAllProduct = document.getElementById('ruleAllProduct');
			if (selectedValue[3].indexOf(",") === -1) {
				// 单个值的情况
				for (var j = 0; j < ruleAllProduct.options.length; j++) {
					if (ruleAllProduct.options[j].value === selectedValue[3] ) {
						addOptionToList(ruleAllProduct.options[j].value, ruleAllProduct.options[j].text);
						break;
					}
				}
			} else {
				// 多个值的情况
				var busiFromArray = selectedValue[3].split(",");
				for (var i = 0; i < busiFromArray.length; i++) {
					for (var j = 0; j < ruleAllProduct.options.length; j++) {
						if (ruleAllProduct.options[j].value === busiFromArray[i]) {
							addOptionToList(ruleAllProduct.options[j].value, ruleAllProduct.options[j].text);
							break;
						}
						
					}
				}
			}
		}
	}

	function addOptionToList(value, text) {
		$("#ruleProductList", navTab.getCurrentPanel()).append( 
				"<option class='item' value='" + LTrim(value) +"'>" + LTrim(text) +"</option>" )
	}

	//产品快速查询
	//定义产品险种集合
	var _businessArray = new Array();
	var val_flag = ""; //定义一个标识，防止多次重复验证
	$("#businessSearchId", navTab.getCurrentPanel())
			.bind(
					"input propertychange",
					function() {
						var value = $("#businessSearchId",
								navTab.getCurrentPanel()).val();
						if (value != val_flag) {
							val_flag = value;
							var optionStr = "";
							for (var i = 0; i < _businessArray.length; i++) {
								var obj = _businessArray[i];
								var text = obj.productStaticCode;
								if (text.indexOf(value) != "-1") {//${productCodeSys}_
									optionStr = optionStr
											+ "<option value='"+ obj.productStaticCode +"'>"
											+ obj.productStaticCode + "_"
											+ obj.productAbbrName + "</option>";
								}
							}
							$("#ruleAllProduct", navTab.getCurrentPanel())
									.html("");
							$("#ruleAllProduct", navTab.getCurrentPanel())
									.append(optionStr);
						}
					});

	var _businessArray = new Array();
	var val_flag = ""; //定义一个标识，防止多次重复验证
	$("#busiFrom", navTab.getCurrentPanel())
			.bind(
					"input propertychange",
					function() {
						var value = $("#busiFrom", navTab.getCurrentPanel())
								.val();
						if (value != val_flag) {
							val_flag = value;
							var optionStr = "";
							for (var i = 0; i < _businessArray.length; i++) {
								var obj = _businessArray[i];
								var text = obj.productStaticCode;
								if (text.indexOf(value) != "-1") {//${productCodeSys}_
									optionStr = optionStr
											+ "<option value='"+ obj.productStaticCode +"'>"
											+ obj.productStaticCode + "_"
											+ obj.productAbbrName + "</option>";
								}
							}
							$("#ruleProductList", navTab.getCurrentPanel())
									.html("");
							$("#ruleProductList", navTab.getCurrentPanel())
									.append(optionStr);
						}
					});

	function AppendItem(allMenu, menu, isAll) {
		debugger
		for (j = 0; j < document.getElementById(allMenu).length; j++) {
			if (isAll == true
					|| document.getElementById(allMenu).options[j].selected) {
				//GET VALUE
				document.getElementById(allMenu).options[j].selected = false;
				//GET LENGTH
				DesLen = document.getElementById(menu).length;
				// NEW OPTION

				var flag = true;
				for (var i = 0; i < document.getElementById(menu).options.length; i++) {
					//alert("document.getElementById(allMenu).options[j].value:" + document.getElementById(allMenu).options[j].value);
					//alert("document.getElementById(menu).options[i].value:" + document.getElementById(menu).options[i].value);
					//alert("i:" + i);
					if (document.getElementById(allMenu).options[j].value == document
							.getElementById(menu).options[i].value) {
						flag = false;
						//alert('jj');
						break;
					}
				}
				if (flag) {
					document.getElementById(menu).options[DesLen] = new Option(
							LTrim(document.getElementById(allMenu).options[j].text),
							document.getElementById(allMenu).options[j].value);
				}

				document.getElementById(allMenu).remove(j);
				j--;
			}
		}
	}

	function LTrim(str) {
		var whitespace = new String("　 \t\n\r");
		var s = new String(str);
		if (whitespace.indexOf(s.charAt(0)) != -1) {
			var j = 0, i = s.length;
			while (j < i && whitespace.indexOf(s.charAt(j)) != -1) {
				j++;
			}
			s = s.substring(j, i);
		}
		return s;
	}

	function selectIsClaim() {
		var value = $("#inputMethod", navTab.getCurrentPanel()).val();
		if (value == 0) {
			$("#busiProdResult", navTab.getCurrentPanel()).hide();
			$("#accidentResult", navTab.getCurrentPanel()).show();
		}
		if (value == 1) {
			$("#accidentResult", navTab.getCurrentPanel()).hide();
			$("#busiProdResult", navTab.getCurrentPanel()).show();
		}
		if (value == "") {
			$("#accidentResult", navTab.getCurrentPanel()).hide();
			$("#busiProdResult", navTab.getCurrentPanel()).hide();
		}
		$("#inputMethodFlag", navTab.getCurrentPanel()).val(value);
		clearForm();
	}

	function bringBackAccidentResultInfoSign() {
		var index = $("#accidentResultBiaoIdSign", navTab.getCurrentPanel())
				.val();//定位到选择哪个出险结果。
		var indexNew = index - 1;
		var accident2CodID = $("#accident2CodIDSign", navTab.getCurrentPanel())
				.val();
		var accident2NamID = $("#accident2NamIDSign", navTab.getCurrentPanel())
				.val();
		$("#tbodysIdSign", navTab.getCurrentPanel()).find(
				"tr:eq(" + indexNew + ")").find("td:eq(1)").find("input:eq(2)")
				.val(accident2CodID);
		$("#tbodysIdSign", navTab.getCurrentPanel()).find(
				"tr:eq(" + indexNew + ")").find("td:eq(1)").find("input:eq(3)")
				.val(accident2NamID);
	}
	
	setInterval(function(){
		$("#addsystime",navTab.getCurrentPanel()).html($("#clockStr").html());
	},1000);

	//隐藏dwz封装的添加中的input
	function hidentRowNum(){
		$("[name='dwz_rowNum']").css("display","none");
	}
	//延迟dwz
	setTimeout('hidentRowNum()',100);
</script>
<s:iterator value="businessProductVO" var="bus">
	<script>
		var obj = new Object();
		obj.productStaticCode = '<s:property value="#bus.productStaticCode"/>';
		obj.productAbbrName = '<s:property value="#bus.productAbbrName"/>';
		_businessArray.push(obj);
	</script>
</s:iterator>

<div layouth="36" id="claimIntelPortraitConfigJsp">
	<a lookupGroup="accidentVO" id="accidentVOResultPageIdSign" width="800"
		height="400" href="" class="btnLook" style="visibility: hidden;">查询出险结果</a>
	<!-- 轨迹查询-子页面 -->
	<div id="reportCommPoolSelfId">
		<%@ include file="claimIntelPortraitConfigSubclass.jsp"%>
	</div>

	<!-- 查询访问路径 -->
	<form id="pagerFormSurcey" method="post"
		action="clm/parameter/selectAccidentResult_CLM_claimIntelPortraitConfigAction.action">
		<input type="hidden" name="pageNum" vaule="${currentPage3.pageNo} " />
		<input type="hidden" name="numPerPage"
			value="${currentPage3.pageSize}" />
	</form>


	<div id="main_2" class="main_borderbg">
		<ul class="main_ul">
			<li class="clearfix">
				<h5 hasborder="true">
					<b id="two" class="main_minus"></b><span>出险结果免责与险种匹配表配置</span>
				</h5>
				<div class="main_foldContent">
					<div class="main_bqtabdivbr">
						<div class="panelPageFormContent">
							<div class="pageFormInfoContent">
								<form id="selectAccidentResult"
									action="clm/parameter/selectAccidentResult_CLM_claimIntelPortraitConfigAction.action"
									onsubmit="return navTabSearch(this,'findIntelPortraitConfigAction')"
									method="post" class="pagerForm required-validate">
									<dl>
										<dt>出险结果2</dt>
										<dd>
											<div id="1">
												<input readonly="true"
													style="width: 30px; border-right: 0px" type="text"
													name="claimAccResultBusiVO.accident2Code"
													id="accident2CodID"
													value="${claimAccResultBusiVO.accResult2}"
													postField="keyword"
													suggestFields="accident1Code,accDetail,accident1Name,icdCode,accident2Code,checkresultCode,accident2Name"
													suggestUrl="clm/pages/report/accResultQueryFollow.jsp"
													readonly lookupGroup="accidentVO" /> <input
													style="width: 100px;"
													name="claimAccResultBusiVO.accident2Name"
													id="accident2Name"
													value="${claimAccResultBusiVO.accResult2Str}"
													postField="keyword"
													suggestFields="accident1Code,accDetail,accident1Name,icdCode,accident2Code,checkresultCode,accident2Name"
													suggestUrl="clm/pages/report/accResultQueryFollow.jsp"
													readonly lookupGroup="accidentVO" /> <a class="btnLook"
													href="clm/report/showAccResult_CLM_accResultQueryFollowAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=3"
													lookupGroup="claimAccResultBusiVO"
													id="btnLookSeriousDiseaseId">查询出险结果</a>
											</div>
										</dd>
									</dl>
									<dl>
										<dt>险种</dt>
										<dd>
											<select class="selectToInput"
												name="claimAccResultBusiVO.busiProdCode" id="busiProdCodes">
												<option value=""></option>
												<s:iterator
													value="claimAccResultBusiLogVO.businessProductVOList"
													status="var">
													<option value="${productCodeStd }">${productNameStd}</option>
												</s:iterator>
											</select>
										</dd>
									</dl>
								</form>
								<div class="pageFormdiv">
									<button type="button" class="but_blue"
										onclick="selectAccidentResult();">查询</button>
								</div>
								<div id="findIntelPortraitConfigAction">
									<form id="pagerForm" method="post"
										action="clm/parameter/selectAccidentResult_CLM_claimIntelPortraitConfigAction.action">
										<input type="hidden" name="pageNum"
											vaule="${currentPage3.pageNo} " /> <input type="hidden"
											name="numPerPage" value="${currentPage3.pageSize}" />
										<!-- 查询条件回调 -->
										<input type="hidden" name="claimAccResultBusiVO.accident2Code"
											value="${claimAccResultBusiVO.accResult2}" /> <input
											type="hidden" name="claimAccResultBusiVO.accident2Name"
											value="${claimAccResultBusiVO.accResult2Str}" />
									</form>
									<div>
										<table class="list" id="surveyApplyOperTable" width="100%">
											<thead>
												<tr>
													<th nowrap>选择</th>
													<th nowrap>出险结果2代码</th>
													<th nowrap>出险结果2名称</th>
													<th nowrap>责免险种</th>
													<th nowrap>操作</th>
												</tr>
											</thead>
											<tbody>
												<s:if test="imageFlag == null">
													<tr>
														<td colspan="100">
															<div class="noRueryResult">请选择条件查询数据！</div>
														</td>
													</tr>
												</s:if>
												<s:if
													test="currentPage3.pageItems == null || currentPage3.pageItems.size()==0">
													<tr>
														<td colspan="100">
															<div class="noRueryResult">没有符合条件的查询结果！</div>
														</td>
													</tr>
												</s:if>
												<s:iterator value="currentPage3.pageItems" status="st">
													<tr align="center" target="listIdInfo">
														<td><input type="radio" class="radioIndex" name="r1"
															onclick='choiceAccidentResult(this);'
															value="${listId}|${accResult2}|${accResult2Str}|${busiFrom}" /></td>
														<td align="center" style="word-break: break-all;">${accResult2}</td>
														<td align="center" style="word-break: break-all;">${accResult2Str}</td>
														<td align="center" style="word-break: break-all;">${busiFrom}</td>
														<td><a title='删除' class='btnDel' id='delButton'
															href='javascript:void(0);'
															onclick="deleteCxjgBusiMatch('${listId}',this);">删除</a></td>
													</tr>
												</s:iterator>
											</tbody>
										</table>

										<div class="panelBar">
											<div class="pages">
												<span>显示</span>
												<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
													name="select"
													onchange="navTabPageBreak({numPerPage:this.value},'findIntelPortraitConfigAction')"
													value="currentPage3.pageSize">
												</s:select>
												<span>条，共${currentPage3.total}条</span>
											</div>
											<div class="pagination" targetType="navTab"
												totalCount="${currentPage3.total}"
												numPerPage="${currentPage3.pageSize}" pageNumShown="20"
												rel="findIntelPortraitConfigAction"></div>
										</div>
									</div>
								</div>


								<form id="saveCxjgBusiMatch"
									action="clm/parameter/saveCxjgBusiMatch_CLM_claimIntelPortraitConfigAction.action"
									onsubmit="return validateCallback(this)" rel="pagerForm"
									method="post">
									<div id="BusiMatch" style="display: none;">
										<div class="mian_site">
											<ul class="main_ul">
												<li class="clearfix">
													<h5 hasborder="false">
														<b id="two" class="main_minus"></b><span>出险结果免责与险种匹配表配置录入</span>
													</h5>
													<div>
														<table width="98%">
															<dl>
																<dt>录入方式</dt>
																<dd>
																	<select class="combox"
																		name="claimBfSurveyPlanVO.isClaim" id="inputMethod"
																		onchange="selectIsClaim()">
																		<option value="">请选择</option>
																		<option value="0">按出险结果配置</option>
																		<option value="1">按险种配置</option>
																	</select>
																</dd>
															</dl>
														</table>
														<input type="hidden" id="inputMethodFlag" name="claimAccResultBusiVO.inputMethodFlag" />
														<input type="hidden" id="saveClaimAccResultBusilistId" name="claimAccResultBusiVO.listId"/>
													</div>
													<div class="main_foldContent" id="accidentResult">
														<div class="panelPageFormContent">
															<div class="pageFormInfoContent">
																<div class="main_lptwo">
																	<div class="panelPageFormContent">
																		<div id="BusiMatch" style="display: none;">
																			<div class="mian_site">
																				<ul class="main_ul">
																					<li class="clearfix">
																						<h5 hasborder="false">
																							<b id="two" class="main_minus"></b><span>按出险结果配置</span>
																						</h5>
																						<div class="main_foldContent">

																							<div>
																								<div class="panelPageFormContent">
																									<div class="pageFormInfoContent">
																										<div class="main_lptwo">
																											<div class="panelPageFormContent"
																												id="signApplicant">
																												<table width="98%">
																													<dl>
																														<span
																															style="float: left; line-height: 1.6;">出险结果2</span>
																														<dd>
																															<div id="1">
																																<input readonly="true"
																																	style="width: 30px; border-right: 0px"
																																	type="text"
																																	name="claimAccResultBusiVO.accident2Code"
																																	id="accResult2"
																																	value="${claimAccResultBusiVO.accResult2}"
																																	postField="keyword"
																																	suggestFields="accident1Code,accDetail,accident1Name,icdCode,accident2Code,checkresultCode,accident2Name"
																																	suggestUrl="clm/pages/report/accResultQueryFollow.jsp"
																																	readonly
																																	lookupGroup="claimAccResultBusiVO" />
																																<input style="width: 100px;"
																																	name="claimAccResultBusiVO.accident2Name"
																																	id="accResult2Str"
																																	value="${accidentVO.accident2Name}"
																																	postField="keyword"
																																	suggestFields="accident1Code,accDetail,accident1Name,icdCode,accident2Code,checkresultCode,accident2Name"
																																	suggestUrl="clm/pages/report/accResultQueryFollow.jsp"
																																	readonly
																																	lookupGroup="claimAccResultBusiVO" />
																																<a class="btnLook"
																																	href="clm/report/showAccResult_CLM_accResultQueryFollowAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=3"
																																	lookupGroup="claimAccResultBusiVO"
																																	id="btnLookSeriousDiseaseId">查询出险结果</a>

																															</div>
																														</dd>
																													</dl>
																													<tr>
																														<td><span
																															style="float: left; line-height: 1.6;">险种</span></td>
																														<td></td>
																														<td><span
																															style="float: left; line-height: 1.6;">责免险种列表</span></td>
																													</tr>
																													<tr>
																														<td><span
																															style="float: left; line-height: 1.6;">险种快速查询：</span><input
																															type="text" id="businessSearchId"
																															style="float: left;" /></td>
																														<td></td>

																														<td><span
																															style="float: left; line-height: 1.6;">责免险种列表快速查询：</span><input
																															type="text" id="busiFrom"
																															style="float: left;" /></td>
																													</tr>
																													<tr>
																														<td width="40%">
																															<div>
																																<select id="ruleAllProduct"
																																	name="claimAccResultBusiVO.busiFrom"
																																	multiple="multiple"
																																	style="height: 120px; width: 100%;"
																																	size=5
																																	ondblclick="return AppendItem('ruleAllProduct', 'ruleProductList', false);">
																																	<s:iterator value="businessProductVO"
																																		status="st" id="businessProduct">
																																		<option
																																			value="${businessProduct.productStaticCode}">${businessProduct.productStaticCode}_${businessProduct.productAbbrName}</option>
																																	</s:iterator>

																																</select>
																															</div>
																														</td>
																														<td align="center" width="8%">
																															<div class="buttonContent">
																																<button class="but_gray" id="toRightP"
																																	onclick="return AppendItem('ruleAllProduct', 'ruleProductList', false);"
																																	type="button"
																																	style="padding-left: 14px; padding-right: 14px;">></button>
																															</div> <!-- 								</div> -->
																															<div
																																style="clear: left; margin: 5px 0px 0px;">
																																<div class="buttonContent">
																																	<button class="but_gray"
																																		id="allToRightP"
																																		onclick="return AppendItem('ruleAllProduct','ruleProductList', true);"
																																		type="button">>></button>
																																</div>
																															</div>
																															<div
																																style="clear: left; margin: 5px 0px 0px;">
																																<div class="buttonContent">
																																	<button class="but_gray" id="toleftP"
																																		onclick="return AppendItem('ruleProductList', 'ruleAllProduct', false);"
																																		type="button"
																																		style="padding-left: 14px; padding-right: 14px;"><</button>
																																</div>
																															</div>
																															<div
																																style="clear: left; margin: 5px 0px 0px;">
																																<div class="buttonContent">
																																	<button class="but_gray"
																																		id="allToLeftP"
																																		onclick="return AppendItem('ruleProductList','ruleAllProduct', true);"
																																		type="button"><<</button>
																																</div>
																															</div>
																														</td>
																														<td width="40%">
																															<div>
																																<select id="ruleProductList"
																																	name="claimAccResultBusiVO.busiFrom"
																																	multiple="multiple"
																																	style="height: 120px; width: 100%;"
																																	size=5
																																	ondblclick="return AppendItem('ruleProductList','ruleAllProduct',  false);">
																																	<s:iterator value="businessProductList"
																																		status="st" var="businessStr">
																																		<option value="${productStaticCode}">
																																			${productStaticCode}_${productAbbrName}</option>
																																	</s:iterator>
																																</select>
																															</div>
																														</td>
																													</tr>
																												</table>
																											</div>
																										</div>
																									</div>
																								</div>
																							</div>
																						</div>
																					</li>
																				</ul>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div> <!-- 险种配置 -->

													<div class="main_foldContent" id="busiProdResult">
														<div class="panelPageFormContent">
															<div class="pageFormInfoContent">
																<div class="main_lptwo">
																	<div class="panelPageFormContent">
																		<div id="BusiMatch" style="display: none;">
																			<div class="mian_site">
																				<ul class="main_ul">
																					<li class="clearfix">
																						<h5 hasborder="false">
																							<b id="two" class="main_minus"></b><span>按险种配置</span>
																						</h5>
																						<table width="98%">
																							<dl>
																								<dt>险种</dt>
																								<dd>
																									<select class="selectToInput"
																										name="claimAccResultBusiVO.busiProdCode"
																										id="configBusiProdCodes">
																										<option value=""></option>
																										<s:iterator value="claimAccResultBusiLogVO.businessProductVOList" status="var">
																											<option value="${productCodeStd}">${productNameStd}</option>
																										</s:iterator>
																									</select>
																								</dd>
																							</dl>
																						</table>
																						<div class="panelPageFormContent main_tabdiv">
																							<input id="accidentVOFlagId"
																								name="claimAccidentVO.flag" value=""
																								type="hidden" disabled="disabled" />
																							<div class="tabdivclassbr">
																								<table class="list nowrap itemDetail" addButton="添加出险结果"
																									   id="addTable" style="margin-bottom: 20px;padding-bottom: 10px; width: 100%;">
																									<thead>
																										<tr>
																											<th type="text" name="items[#index#].itemInt"
																												readonly defaultVal="#index#" size="12"
																												width="0%" fieldClass="digits">序号</th>
																											<th type="enum"
																												name="claimAccidentResultVO.accResult2"
																												enumUrl="clm/pages/sign/accidentResultTwo.jsp"
																												size="12">出险结果2</th>
																											<th type="del" width="60">操作</th>
																										</tr>
																									</thead>
																									<tbody id="tbodysIdSign">
																										<input type="hidden"
																											id="accidentResultBiaoIdSign">
																										<input type="hidden"
																											name="accidentVO.accident2Code"
																											id="accident2CodIDSign">
																										<input type="hidden"
																											name="accidentVO.accident2Name"
																											id="accident2NamIDSign">
																										
																										<tr>
																											<td><input name="items[0].itemInt"
																												class="digits textInput focus" type="text"
																												size="12" value="1" readonly="readonly" />
																											</td>
																											<td>
																												<!-- 添加俩个无用标签和添加的时候对应上 --> 
																												<input type="hidden" /> <input type="hidden" /> 
																												<input class="short" readonly="readonly"
																												name="claimAccResultBusiVO.accResult2"
																												id="accResult2Sign"
																												onclick="accidentVOResultPageSign(this);">
																												<input class="lang" readonly="readonly"
																												name="claimAccResultBusiVO.accResult2Str"
																												type="text" id="accResult2NameSign"
																												onclick="accidentVOResultPageSign(this);" />
																											</td>
																											<td><a class="btnDel">选择</a></td>
																										</tr>
																									</tbody>
																								</table>
																							</div>
																						</div>
																					</li>
																				</ul>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>

													<div>
														<dl>
															<dt>提交人:</dt>
															<dd>
																<span>${currentUser.userName }</span>
															</dd>
														</dl>
														<dl>
															<dt>提交日期:</dt>
															<dd>
																<span id="addsystime"></span>
															</dd>
														</dl>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</form>

								<div class="formBarButton main_bottom">
									<ul>
										<li>
											<button class="but_blue" type="button" onclick="insert()">新增</button>
										</li>
										<li>
											<button class="but_blue" type="button" onclick="saveSet()"
												id="saveRule" disabled="disabled">保存</button>
										</li>
										<li><button class="but_blue" type="button" id="clearBtn"
												onclick="clearForm()">重置</button></li>
										<li>
											<button class="but_gray" type="button" id="exitbtn"
												onclick="exit()">退出</button>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</div>
			</li>
		</ul>
	</div>
</div>




