<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript" language="javascript" src="clm/js/commonMianBox.js">
	
</script>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript">
	function query(){
		$("#findYyxjDutyFeeMbSur", navTab.getCurrentPanel()).submit();
	}
	function queryLog(){
		$("#queryYyxjDutyFeeMbSurLog", navTab.getCurrentPanel()).submit();
	}
	//新增方法
	function newInserd() {
		$("#addYyxjDutyFeeMbSur").show();
		cleanSave();
		if($("#main_3",navTab.getCurrentPanel()).find("#two",navTab.getCurrentPanel()).hasClass("main_plus")){
			$("#main_3",navTab.getCurrentPanel()).find("h5",navTab.getCurrentPanel()).click();
		}
		//保存按钮可用
		$('#save', navTab.getCurrentPanel()).removeAttr("disabled", false);
	}
	//保存方法
	function saveSet() {
		$("#saveYyxjDutyFeeMbSur", navTab.getCurrentPanel()).submit();
	}
	//重置方法
	function cleanSave() {
		$("#YyxjLiabSurveylistId", navTab.getCurrentPanel()).val("");
		$("#findaccidentDetails", navTab.getCurrentPanel()).val("");
		$("#detailDescId", navTab.getCurrentPanel()).val("");
		$("#accidentLevel", navTab.getCurrentPanel()).selectMyComBox("");
		claimFireEvent($("#accidentLevel",navTab.getCurrentPanel()));
		$("#liabFrom", navTab.getCurrentPanel()).selectMyComBox("");
		claimFireEvent($("#liabFrom",navTab.getCurrentPanel()));
		
	}
	//点击查询结果某行值，带出值至出险结果必调项配置 
	function showDetails(obj) {
		$("#addYyxjDutyFeeMbSur").show();
		$(obj).parent().parent().find("td:eq(0)").find("input:radio").attr("checked", true);
		var selectedValue = $(obj).val().split("|");
		$("#YyxjLiabSurveylistId", navTab.getCurrentPanel()).val(selectedValue[0]);
		$("#findaccidentDetails", navTab.getCurrentPanel()).val(selectedValue[1]);
		$("#detailDescId", navTab.getCurrentPanel()).val(selectedValue[2]);
		
		$("#accidentLevel", navTab.getCurrentPanel()).selectMyComBox(selectedValue[3]);
		claimFireEvent($("#accidentLevel",navTab.getCurrentPanel()));
		$("#liabFrom", navTab.getCurrentPanel()).selectMyComBox(selectedValue[4]);
		claimFireEvent($("#liabFrom",navTab.getCurrentPanel()));
		
		if($("#main_3",navTab.getCurrentPanel()).find("#two",navTab.getCurrentPanel()).hasClass("main_plus")){
			$("#main_3",navTab.getCurrentPanel()).find("h5",navTab.getCurrentPanel()).click();
		}
		//保存按钮可用
		$('#save', navTab.getCurrentPanel()).removeAttr("disabled", false);
	}
	function checkDelete(listId, selectVal) {
		alertMsg.confirm("请确认是否删除？",{
			okCall : function() {
				$
						.ajax({
							url : "clm/parameter/delYyxjDutyFeeMbSur_CLM_claimIntelPortraitConfigAction.action?listId="
									+ listId,
							global : false,
							type : "POST",
							dataType : "json",
							success : function(s) {
								if (s.statusCode == DWZ.statusCode.ok) {
									alertMsg.correct(s.message);
									$(selectVal).parent().parent().remove();
								} else {
									alertMsg.error(s.message);
								}
							}
						});
		
			}
		});
	}
	function saveYyxjAjaxDone(json) {
		if (json.statusCode == DWZ.statusCode.ok) { 
			$("#addYyxjDutyFeeMbSur",navTab.getCurrentPanel()).hide();
			cleanSave();
			alertMsg.correct(json.message);
		} else {
			alertMsg.error(json.message);
		}
	}
</script>
<body>
	<!-- 意外细节分类必调和责免配置轨迹 -->
	<div id="main_1">
		<ul class="main_ul">
			<li class="clearfix fold">
				<h5 hasborder="true">
					<b id="two" class="main_plus"></b><span>意外细节分类必调和责免配置轨迹查询</span>
				</h5>
				<div class="main_foldContent" style="display: none;">
					<div id="medicalSurveyDiv" class="panelPageFormContent">
						<div class="main_bqtabdivbr">
							<form id="queryYyxjDutyFeeMbSurLog"
								action="clm/parameter/queryYyxjDutyFeeMbSurLog_CLM_claimIntelPortraitConfigAction.action"
								method="post"
								onsubmit="return navTabSearch(this,'claimYyxjLiabSurveyLog')"
								class="pagerForm required-validate" rel="pagerForm">
								<div>
									<dl>
										<dt class="searchWhere">查询起期</dt>
										<dd>
											<input name="claimYyxjLiabSurveyLogVO.startDate"
												class="searchValue" type="expandDateYMDRO"
												value="<s:date name="claimAccResultSurveyLogVO.startDate" format="yyyy-MM-dd"/>" />
										</dd>
									</dl>
									<dl>
										<dt class="searchWhere">查询止期</dt>
										<dd>
											<input name="claimYyxjLiabSurveyLogVO.endDate"
												class="searchValue" type="expandDateYMDRO"
												value="<s:date name="claimAccResultSurveyLogVO.endDate" format="yyyy-MM-dd"/>" />
										</dd>
									</dl>
									<dl>
										<dt class="searchWhere">意外细节</dt>
										<dd>
											<input 
												name="claimYyxjLiabSurveyLogVO.accidentDetail"
												value="${claimCaseVO.accidentDetail}" type="hidden" /> <input
											    name="claimYyxjLiabSurveyLogVO.DetailDesc"
												value="${accidentDetailVO.detailDesc}" type="text"
												postField="keyword"
												suggestFields="accDetailCode,accDetailDesc" readonly
												suggestUrl="clm/pages/report/findAccidentDetails.jsp"
												lookupGroup="claimYyxjLiabSurveyLogVO" /> <a
												class="btnLook"
												href="clm/report/queryPage_CLM_accidentDetailsAction.action?leftFlag=0&menuId=${menuId}"
												lookupGroup="claimYyxjLiabSurveyLogVO"
												id="btnLookAccidnetDetalId">查询意外细节</a>
										</dd>
									</dl>
									<dl>
										<button type="button" class="but_blue" id="queryList"
											onclick="queryLog();">查询</button>
									</dl>
								</div>
							</form>
							<div id="claimYyxjLiabSurveyLog">
								<table class="list sortable main_dbottom" id="OperTable"
									width="100%">
									<span><b>意外细节必调和责免轨迹</b></span>
									<thead>
										<tr align="center">
											<th nowrap>意外细节代码</th>
											<th nowrap>意外细节名称</th>
											<th nowrap>意外等级</th>
											<th nowrap>责任免除</th>
											<th nowrap>操作</th>
											<th nowrap>提交人</th>
											<th nowrap>提交日期</th>
										</tr>
									</thead>
									<tbody>
										<s:if test="imageFlag != null">
											<tr>
												<td colspan="13">
													<div class="noRueryResult">请选择条件查询数据！</div>
												</td>
											</tr>
										</s:if>
									</tbody>
								</table>
								<div class="panelBar">
									<div class="pages">
										<span>显示</span>
										<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
											name="select"
											onchange="navTabPageBreak({numPerPage:this.value},'claimYyxjLiabSurveyLog')"
											value="claimYyxjLiabSurveyLogVOPage.pageSize">
										</s:select>
										<span>条，共${claimYyxjLiabSurveyLogVOPage.total}条</span>
									</div>
									<div class="pagination" targetType="navTab"
										rel="claimYyxjLiabSurveyLog"
										totalCount="${claimYyxjLiabSurveyLogVOPage.total}"
										numPerPage="${claimYyxjLiabSurveyLogVOPage.pageSize}" pageNumShown="20"
										currentPage="${claimYyxjLiabSurveyLogVOPage.pageNo}"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</li>
		</ul>
	</div>

	<div id="main_2">
		<ul class="main_ul">
			<li class="clearfix">
				<h5 hasborder="true">
					<b id="two" class="main_minus"></b><span>意外细节分类必调和责免配置</span>
				</h5>
				<div class="main_foldContent">
					<div id="medicalSurveyDiv" class="panelPageFormContent">
						<div class="main_bqtabdivbr">
							<form id="findYyxjDutyFeeMbSur"
								action="clm/parameter/findYyxjDutyFeeMbSur_CLM_claimIntelPortraitConfigAction.action"
								method="post"
								onsubmit="return navTabSearch(this,'accidentDetailAdjustDutyFree')"
								class="pagerForm required-validate" rel="pagerForm">
								<div>
									<dl>
										<dt class="searchWhere">意外细节</dt>
										<dd>
											<input 
												name="claimYyxjLiabSurveyVO.accidentDetail"
												value="${claimCaseVO.accidentDetail}" type="hidden" lookupGroup="claimYyxjLiabSurveyVO"/> <input
												name="claimYyxjLiabSurveyVO.DetailDesc"
												value="${accidentDetailVO.detailDesc}" type="text"
												postField="keyword"
												suggestFields="accDetailCode,accDetailDesc" readonly
												suggestUrl="clm/pages/report/findAccidentDetails.jsp"
												lookupGroup="claimYyxjLiabSurveyVO" /> <a class="btnLook"
												href="clm/report/queryPage_CLM_accidentDetailsAction.action?leftFlag=0&menuId=${menuId}"
												lookupGroup="claimYyxjLiabSurveyVO" id="btnLookAccidnetDetalId">查询意外细节</a>
										</dd>
									</dl>
									<dl>
										<button type="button" class="but_blue" id="queryList"
											onclick="query();">查询</button>
									</dl>
								</div>
							</form>
							<div id="accidentDetailAdjustDutyFree">
								<form id="pagerForm" method="post" action="clm/parameter/findYyxjDutyFeeMbSur_CLM_claimIntelPortraitConfigAction.action">
									<input type="hidden" name="pageNum" vaule="${claimYyxjLiabSurveyVOPage.pageNo} " />
									<input type="hidden" name="numPerPage" value="${claimYyxjLiabSurveyVOPage.pageSize}" />
									<input type="hidden" name="claimYyxjLiabSurveyVO.accReason" value="${claimYyxjLiabSurveyVO.accidentDetail}" />
								</form>
								<div>
									<table class="list" width="100%">
										<thead>
											<tr align="center">
												<th nowrap>选择</th>
												<th nowrap>意外细节代码</th>
												<th nowrap>意外细节名称</th>
												<th nowrap>意外等级</th>
												<th nowrap>责任免除</th>
												<th nowrap>操作</th>
											</tr>
										</thead>
										<tbody>
											<s:iterator value="claimYyxjLiabSurveyVOPage.pageItems" var="status"
												status="st">
												<tr align="center">
													<td align="center"><input type="radio" name="danXuan"
														onclick="showDetails(this);" value="${listId}|${accidentDetail }|${accidentName }|${accidentLevel }|${liabFrom }"></td>
													<td>${accidentDetail }</td>
													<td>${accidentName }</td>
													<td>${accidentLevel }</td>
													<td><Field:codeValue value="${liabFrom }" tableName="APP___CLM__DBUSER.T_YES_NO" /></td>
													<td><a title="删除" class="btnDel" id='delButton'
														href='javascript:void(0);'
														onclick="checkDelete('${listId}',this);">删除</a></td>
												</tr>
											</s:iterator>
										</tbody>
									</table>
									<div class="panelBar">
											<div class="pages">
												<span>显示</span>
												<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
													name="select"
													onchange="navTabPageBreak({numPerPage:this.value},'accidentDetailAdjustDutyFree')"
													value="claimYyxjLiabSurveyVOPage.pageSize">
												</s:select>
												<span>条，共${claimYyxjLiabSurveyVOPage.total}条</span>
											</div>
											<div class="pagination" targetType="navTab"
												rel="accidentDetailAdjustDutyFree"
												totalCount="${claimYyxjLiabSurveyVOPage.total}"
												numPerPage="${claimYyxjLiabSurveyVOPage.pageSize}" pageNumShown="20"
												currentPage="${claimYyxjLiabSurveyVOPage.pageNo}"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</li>
		</ul>
		</div>
	<div id="addYyxjDutyFeeMbSur" style="display: none;">

		<div id="main_3">
			<ul class="main_ul">
				<li class="clearfix fold">
					<h5 hasborder="true">
						<b id="two" class="main_plus"></b><span>意外细节分类必调和责免配置录入</span>
					</h5>
					<div class="main_foldContent" style="display: none;">
						<div id="medicalSurveyDiv" class="panelPageFormContent">
							<div class="main_bqtabdivbr">
								<form id="saveYyxjDutyFeeMbSur"
									action="clm/parameter/saveYyxjDutyFeeMbSur_CLM_claimIntelPortraitConfigAction.action"
									onsubmit="return validateCallback(this,saveYyxjAjaxDone)"
									method="post" class="pagerForm required-validate">
									<input type="hidden" id="YyxjLiabSurveylistId"
										name="claimYyxjLiabSurveyAddVO.listId"
										value="${claimYyxjLiabSurveyVO.listId }" />
									<div>
										<dl>
											<dt class="searchWhere">意外细节</dt>
											<dd>
													<input id="findaccidentDetails" name="claimYyxjLiabSurveyAddVO.accidentDetail"
													value="${accidentDetailVO.detailCode}" type="hidden"
													postField="keyword"
													suggestFields="accDetailCode,accDetailDesc"
													suggestUrl="clm/pages/report/findAccidentDetails.jsp"
													lookupGroup="claimYyxjLiabSurveyVO" /> 
													<input
													id="detailDescId" name="claimYyxjLiabSurveyAddVO.DetailDesc"
													value="${accidentDetailVO.detailDesc}" type="text"
													postField="keyword"
													suggestFields="accDetailCode,accDetailDesc" readonly
													suggestUrl="clm/pages/report/findAccidentDetails.jsp"
													lookupGroup="claimYyxjLiabSurveyVO" /> 
													<a class="btnLook"
													href="clm/report/queryPage_CLM_accidentDetailsAction.action?leftFlag=0&menuId=${menuId}"
													lookupGroup="claimYyxjLiabSurveyAddVO"
													id="btnLookAccidnetDetalId">查询意外细节</a>
											</dd>
										</dl>
									</div>
									<div>
										<dl>
											<dt class="searchWhere">意外等级</dt>
											<dd>
												<select id="accidentLevel" class="combox title"
													name="claimYyxjLiabSurveyAddVO.accidentLevel"
													value="${claimYyxjLiabSurveyVO.accidentLevel}">
													<option value="I"
														<s:if test="claimYyxjLiabSurveyVO.accidentLevel == I">selected</s:if>>I</option>
													<option value="II"
														<s:if test="claimYyxjLiabSurveyVO.accidentLevel == II">selected</s:if>>II</option>
													<option value="III"
														<s:if test="claimYyxjLiabSurveyVO.accidentLevel == III">selected</s:if>>III</option>
													<option value="IV"
														<s:if test="claimYyxjLiabSurveyVO.accidentLevel == IV">selected</s:if>>IV</option>
												</select>
											</dd>
										</dl>
										<dl>
											<dt class="searchWhere">责任免除</dt>
											<dd>
												<select id="liabFrom" class="combox title"
													name="claimYyxjLiabSurveyAddVO.liabFrom">
													<option value="1"
														<s:if test="claimYyxjLiabSurveyVO.liabFrom == 1">selected</s:if>>是</option>
													<option value="0"
														<s:if test="claimYyxjLiabSurveyVO.liabFrom == 0">selected</s:if>>否</option>
												</select>
											</dd>
										</dl>
									</div>
									<div>
										<dl>
											<dt>提交人</dt>
											<dd>
												<span id="submitor">${currentUser.userName }</span>
											</dd>
										</dl>
										<dl>
											<dt>提交日期</dt>
											<dd>
												<span id="submitTime"></span>
											</dd>
										</dl>
									</div>
								</form>
							</div>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button class="but_blue" type="button" id="saveRule"
					onclick="newInserd()">新增</button>
			</li>
			<li>
				<button class="but_blue" type="button" id="save" disabled="true"
					onclick="saveSet()">保存</button>
			</li>
			<li>
				<button class="but_blue" type="button" id="saveRule"
					onclick="cleanSave()">重置</button>
			</li>
			<li>
				<button class="but_gray" type="button" id="exitbtn"
					onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
</body>