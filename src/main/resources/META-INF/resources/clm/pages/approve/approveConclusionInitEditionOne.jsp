<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%> 
<% String path=request.getContextPath();  String basePath=path+"/"; %> 

 

<script type="text/javascript" src="clm/pages/approve/approveConclusionInitEditionOne.js"></script>
<style>
.spanbox1 span {width:22%}
.spanbox2 span {width:22%}
.spanbox3 span {width:25%}
</style>
<script>
var caseIdNum='${caseId}';
var isMiddleThing = '${isMiddleThing}';	//事中质检引用该页面标记
var afterFlag = '${afterFlag}';
var navTabFlag = navTabFlag;
var isRisk = '${claimCaseVO.isRisk}';
var riskLabel = '${claimCaseVO.riskLabel}';
var riskOtherReason = '${claimCaseVO.riskOtherReason}';
//页面加载数据判断
//是否风险的下拉
if(isRisk==1){
	$("#isRisk option[value='1']").attr("selected", true);
	$("#riskDiv", navTab.getCurrentPanel()).show();
}else{
	$("#isRisk option[value='0']").attr("selected", true);
	$("#riskDiv", navTab.getCurrentPanel()).hide();
}
//超期补偿标识和超期补偿核定控制，审批意见中<超期>标识为勾选状态时，显示否则不显示
var isOverComp=$("#isOverCompSP",navTab.getCurrentPanel()).val();
if(isOverComp==1){
	$("#overCompFlagTitle",navTab.getCurrentPanel()).show();
	$("#overDueTitle",navTab.getCurrentPanel()).show();
	$("#overDueDIV",navTab.getCurrentPanel()).show();
	var overReason=$("#overReason",navTab.getCurrentPanel()).val();
	if(overReason==1){
		var overduePayOld = $("#overdueMoneyOld",navTab.getCurrentPanel()).val();
		$("#overCompFlagSP",navTab.getCurrentPanel()).attr("checked","checked");
		$("#overCompFlagSP",navTab.getCurrentPanel()).val(1);
		$("#overCompFlagSPValue",navTab.getCurrentPanel()).val(1);
	}else if(overReason==2){
		$("#overCompFlagSP",navTab.getCurrentPanel()).removeAttr("checked");
		$("#overCompFlagSP",navTab.getCurrentPanel()).val(0);
		$("#overCompFlagSPValue",navTab.getCurrentPanel()).val(0);
	}else{
		$("#overCompFlagSP",navTab.getCurrentPanel()).removeAttr("checked");
		$("#overCompFlagSP",navTab.getCurrentPanel()).val(0);
		$("#overCompFlagSPValue",navTab.getCurrentPanel()).val(0);
	}
}else{
	$("#overCompFlagTitle",navTab.getCurrentPanel()).hide();
	$("#overDueTitle",navTab.getCurrentPanel()).hide();
	$("#overDueDIV",navTab.getCurrentPanel()).hide();
}
//风险选项勾选
if(riskLabel!=null){
	var dataStr2 = riskLabel.split(',');
	for (i=0;i<dataStr2.length ;i++ )
		{
			if(dataStr2[i]=='1'||dataStr2[i]==1){
				$("#riskLabel1", navTab.getCurrentPanel()).attr("checked",true);
			}
			if(dataStr2[i]=='2'||dataStr2[i]==2){
				$("#riskLabel2", navTab.getCurrentPanel()).attr("checked",true);
			}
			if(dataStr2[i]=='3'||dataStr2[i]==3){
				$("#riskLabel3", navTab.getCurrentPanel()).attr("checked",true);
			}
			if(dataStr2[i]=='4'||dataStr2[i]==4){
				$("#riskLabel4", navTab.getCurrentPanel()).attr("checked",true);
			}
			if(dataStr2[i]=='5'||dataStr2[i]==5){
				$("#riskLabel5", navTab.getCurrentPanel()).attr("checked",true);
			}
			if(dataStr2[i]=='6'||dataStr2[i]==6){
				$("#riskLabel6", navTab.getCurrentPanel()).attr("checked",true);
			}
			if(dataStr2[i]=='7'||dataStr2[i]==7){
				$("#riskLabel7", navTab.getCurrentPanel()).attr("checked",true);
			}
			if(dataStr2[i]=='8'||dataStr2[i]==8){
				$("#riskLabel8", navTab.getCurrentPanel()).attr("checked",true);
			}
		}
}
if(isMiddleThing == "middleThingFlag" || afterFlag == "1"){
	navTabFlag = $.pdialog.getCurrent();
}
$(function() {
	var auditDecision = '${claimAuditApproveVO.auditDecision}';
	var auditRejectReason = '${claimAuditApproveVO.auditRejectReason}';
	if(auditDecision==3){
		$("#refectDiv", navTab.getCurrentPanel()).show();
// 		$("#specificCauseDiv", navTab.getCurrentPanel()).show();
		var dataStr1 = auditRejectReason.split(',');
		for (i=0;i<dataStr1.length ;i++ )
		{	
			if(dataStr1[i]=='01'||dataStr1[i]==01){
//					$("#rejectCode1", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode1", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='02'||dataStr1[i]==02){
//					$("#rejectCode2", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode2", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='03'||dataStr1[i]==03){
//					$("#rejectCode3", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode3", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='04'||dataStr1[i]==04){
//					$("#rejectCode4", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode4", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='05'||dataStr1[i]==05){
//					$("#rejectCode5", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode5", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='06'||dataStr1[i]==06){
//					$("#rejectCode6", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode6", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='07'||dataStr1[i]==07){
//					$("#rejectCode7", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode7", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='08'||dataStr1[i]==08){
//					$("#rejectCode8", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode8", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
			if(dataStr1[i]=='99'||dataStr1[i]==99){
//					$("#rejectCode99", navTab.getCurrentPanel()).val(dataStr1[i]);
// 				if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
					$("#rejectCode99", navTab.getCurrentPanel()).attr("checked",true);
// 				}
			}
		}
	}else{
		$("#refectDiv", navTab.getCurrentPanel()).hide();
// 		$("#specificCauseDiv", navTab.getCurrentPanel()).hide();
	}
	//控制银行账号不可以复制粘贴剪切
	$("input#approve_accountNo", navTab.getCurrentPanel()).bind("copy cut paste", function() {
		return false;
	});
	$("input#approve_holderBankAccount", navTab.getCurrentPanel()).bind("copy cut paste", function() {
		return false;
	});
	//控制证件号码不可以复制粘贴剪切
	$("input#approve_payeeCertiNo", navTab.getCurrentPanel()).bind("copy cut paste", function() {
		return false;
	});
	$("input#approve_beneCertiNo", navTab.getCurrentPanel()).bind("copy cut paste", function() {
		return false;
	});
	//判断是否有险种层信息
	if ($("#approve_paymentPlanTbody", navTabFlag).find("tr").length > 0) {
		$("#approve_paymentPlanTbody", navTabFlag).find("tr:eq(0)").find("td:eq(0)").find("input[type='radio']", navTabFlag).attr("checked", true);
		$("input:radio:checked[name='approve_caseId']", navTabFlag).click();
		//alert(789);
		$("input:radio:checked[name='editButton']", navTabFlag).click();
	}
	//事中质检引用该页面，则删除保存、审批确认按钮,审批记录隐藏
	if(isMiddleThing == "middleThingFlag"){
		$("#saveAndAffirm", navTab.getCurrentPanel()).parent().parent().parent().remove();
		$("#saveAndAffirm2", navTab.getCurrentPanel()).parent().parent().parent().remove();
		$("#approveView", navTab.getCurrentPanel()).css("display","none");
		$("#approveView", navTab.getCurrentPanel()).prev().css("display","none");
	}
	if(afterFlag == "1"){
		$("#saveAndAffirm", navTab.getCurrentPanel()).parent().remove();
		$("#saveAndAffirm2", navTab.getCurrentPanel()).parent().remove();
		setTimeout(function(){
			$("#approveRemark", navTab.getCurrentPanel()).attr("disabled","disabled");
			$("#approveDecision", navTab.getCurrentPanel()).setMyComboxDisabled(true);
			$("#approveCaseFlag", navTab.getCurrentPanel()).setMyComboxDisabled(true);
			$("#approveRejectReason", navTab.getCurrentPanel()).setMyComboxDisabled(true);
			$("#overGive", navTab.getCurrentPanel()).attr("disabled","disabled");
		},'1000')
	}
});

//初始化加载银行编码
var caseId = $("#caseId", navTab.getCurrentPanel()).val();
$.ajax({
	'type':'post',
	'url':'clm/register/queryTankCode_CLM_toBenefitInputPageAction.action?caseId='+caseId,
	'datatype':'json',
	'success':function(data){
		var bankVOList = eval("(" + data + ")");
		var option = "<option value=''>请选择</option>";
		for(var i = 0; i < bankVOList.length; i++){
			option += "<option value='"+bankVOList[i].bankCode+"'>"+bankVOList[i].bankName+"</option>";
		}
		$("#approve_holderBankCodeId", navTab.getCurrentPanel()).append(option);
	}
});

//支付计划点击单选按钮，查询受益人与领款人信息列表
function queryBenePay(k) {
	var value = $(k).val();
	var valueSplit = value.split(":");
	var caseId = valueSplit[0];
	var policyId = valueSplit[1];
	var busiItemId = valueSplit[2];
	var busiProdCode= valueSplit[3];
	var adjustBusiId = $(k).parent().parent().find("td:eq(6)").find("#approve_adjustBusiIdHidden").val();
	//为分期支付计划中的保单ID险种ID赋值""
	$("#hiddenbusiProdCode", navTab.getCurrentPanel()).val(busiProdCode);
	$("#instalmentCaseId", navTabFlag).val(caseId);
	$("#instalmentPolicy", navTabFlag).val(policyId);
	$("#instalmentBusiPrd", navTabFlag).val(busiItemId);
	$.ajax({
		url : "clm/paymentplan/queryBenePay_CLM_paymentPlanAction.action?claimPayVO.caseId="+caseId
				+"&claimPayVO.policyId="+policyId
				+"&claimPayVO.busiItemId="+busiItemId
				+"&claimPayVO.busiProdCode="+busiProdCode
				+"&claimPayVO.advanceFlag=0&claimPayVO.adjustBusiId="+adjustBusiId,
		type : "POST",
		dataType : "html", 
		success : function (html){
			$("#approve_claimBeneTbody",navTabFlag).empty();
			$("div#approve_claimBeneDiv",navTabFlag).html(html).initUI(); 
			//如果有受益人/领款人信息 自动触发单选按钮 显示详细信息
			if ($("#claimBeneTbody", navTabFlag).find("tr").length > 0) {
				$("#claimBeneTbody", navTabFlag).find("tr:eq(0)").find("td:eq(0)").find("input[type='radio']", navTabFlag).attr("checked", true);
				$("input:radio:checked[id='editButton']", navTabFlag).click();
			}
			//判断选择的保单险种是否是退还保费  显示投保人信息   8=返还保费
			if($(k).parent().parent().find("td:eq(6)").find("input#approve_adjustTypeHidden").val() == '返还保费') {
				$("#moneyAllotMesOneThreeDIV", navTabFlag).css("display","block"); //显示投保人信息区域
				$("#approve_checkHolder", navTab.getCurrentPanel()).attr("checked", false);
				//显示投保人信息
				var policyId = $(k).parent().parent().find("td:eq(0)").find("input#approve_policyId").val();
				var policyCode = $(k).parent().parent().find("td:eq(1)").text();
				$.ajax({
					url : "clm/paymentplan/queryPolicyHolder_CLM_paymentPlanAction.action?claimPayVO.policyId="+policyId+"&claimPayVO.policyCode="+policyCode+"&claimPayVO.caseId="+caseId+"&claimPayVO.busiItemId="+busiItemId+"&claimPayVO.busiProdCode="+busiProdCode,
					 
					type : "POST",
					dataType : "json",
					 
					success : function (json){
						//清空页面的受益人领款人信息
						//$("#approve_benePayeeInfo dd", navTabFlag).empty();
						
						//为投保人赋值
						$("#approve_holderCustomerId", navTabFlag).val(json.customerId);
						$("#approve_holderName", navTabFlag).val(json.customerName);
						$("select#approve_holderCerti", navTabFlag).val(json.customerCertType); //证件类型
						$("#approve_holderCertiCode", navTabFlag).val(json.customerCertiCode);//证件号码
						
						$("#approve_holderMode", navTabFlag).selectMyComBox(json.payMode);//收费方式
						$("#approve_holderBankAccount", navTab.getCurrentPanel()).val(json.bankAccount);//银行账号
						
						$("#approve_hiddenHolderBankId", navTab.getCurrentPanel()).val(json.bankCode);
						var holderBankCode = json.bankCode;
						
						if($("#approve_hiddenHolderBankId", navTab.getCurrentPanel()).val() != "" && $("#approve_hiddenHolderBankId", navTab.getCurrentPanel()).val() != null){
							$("a[name='approve_holderBankCode']",navTab.getCurrentPanel()).val(holderBankCode);
							$("a[name='approve_holderBankCode']",navTab.getCurrentPanel()).empty();
							$("a[name='approve_holderBankCode']",navTab.getCurrentPanel()).append($("select#approve_holderBankCodeId").find("option[value="+holderBankCode+"]").attr("title"));
							$("select#approve_holderBankCodeId", navTabFlag).attr("value", holderBankCode);
							 claimFireEvent($("#approve_holderBankCodeId",navTab.getCurrentPanel()));
						}
						//退还投保人  复选框
						if (json.isBackHolder == '1') {
							//清空页面的受益人领款人信息
							$("#approve_benePayeeInfo dd", navTabFlag).empty();
							$("#approve_checkHolder", navTab.getCurrentPanel()).attr("checked", true);
						}
					}
				});
			}
		}
	});
}
//点击单选按钮查询受益人与领款人信息
function edit(k) {
	var strValue = $("input:radio:checked[name='approve_caseId']", navTabFlag).val();
	var valueSplit = strValue.split(":");
	var caseId = valueSplit[0];
	var policyId = valueSplit[1];
	var busiItemId = valueSplit[2];
	var beneId = $(k).parent().parent().find("#beneId").val();
	var payeeId = $(k).parent().parent().find("#payeeId").val();
	var payAmount = $(k).parent().parent().find("td:eq(3)").text();
	var directCaseId = $(k).parent().parent().find("#directCaseId").val();
	if(directCaseId!=''){
		caseId  = directCaseId;
	}
	var rel = $("#approve_benePayeeInfo", navTabFlag);
	rel.loadUrl("clm/approve/benePayeeInfoView_CLM_approveConclusionAction.action?beneVO.beneId="+beneId
			+"&payeeVO.payeeId="+payeeId+"&claimPayVO.caseId="+caseId
			+"&claimPayVO.policyId="+policyId+"&claimPayVO.busiItemId="+busiItemId+"&claimPayVO.payAmount="+payAmount,"html", function() {
	});
	caseId = valueSplit[0];
	//显示年金领取计划
	//得到受益人对应的受益分配 id = claimPayId 用来查询年金数据
	var claimPayId = $(k).parent().parent().find("input#claimPayId").val();
	var beneBirth = $(k).parent().parent().find("input#beneBirth").val();
	$.ajax({
		url : "clm/approve/queryClaimAnnuityList_CLM_approveConclusionAction.action?paymentPlanVO.claimPayId="+claimPayId
				+"&paymentPlanVO.beneIdBirth="+beneBirth, 
		type : "POST",
		dataType : "json",
		success : function (claimAnnuityVOList){
			if(claimAnnuityVOList.length != 0){
			$("#moneyAllotMesOneFourDIV", navTabFlag).css("display","block"); //显示年金转换区域
			var insertHtml ="";
		    var annuityPayCount = 0;
			$.each(claimAnnuityVOList,function(key,val){
				var annuityPay = claimAnnuityVOList[key].annuityPay;
				var claimPayId = claimAnnuityVOList[key].claimPayId;
				var beneAge = claimAnnuityVOList[key].beneAge;
				var claimLiabId = claimAnnuityVOList[key].claimLiabId;
				var annuityLiab = claimAnnuityVOList[key].annuityLiab;
				var annuityProd = claimAnnuityVOList[key].annuityProd;
				var annuityFreq = claimAnnuityVOList[key].annuityFreq;
				var annuityStartDate = claimAnnuityVOList[key].annuityStartDateString;
				var annuityEndDate = claimAnnuityVOList[key].annuityEndDateString;
				insertHtml += "<tr align='center'>" +
				"<td align='center'>"+(key+1)+"</td>" +
				"<td style='display:none;'>"+claimPayId+"</td>" +
				"<td>"+beneAge+"</td>" +
				"<td>"+annuityPay+"</td>" +
				"<td style='display:none;'>"+claimLiabId+"</td>" +
				"<td style='display:none;'>"+annuityLiab+"</td>" +
				"<td style='display:none;'>"+annuityProd+"</td>" +
				"<td style='display:none;'>"+annuityFreq+"</td>" +
				"<td style='display:none;'>"+annuityStartDate+"</td>" +
				"<td style='display:none;'>"+annuityEndDate+"</td>" +
				"</tr>";
				annuityPayCount = annuityPayCount+annuityPay;
			});
			$("#approve_annuDrawPlanTbody",navTabFlag).empty();
			$("#approve_annuDrawPlanTbody",navTabFlag).append(insertHtml);
			$("#approve_annuDrawPlanTbody",navTabFlag).initUI();
			//转换金额 汇总
			if ($("#approve_annuDrawPlanTbody", navTabFlag).find("tr").length > 0) {
				$("#approve_annuConvert", navTabFlag).attr("checked",true);
				$("#countAnnuityMoney",navTabFlag).text(annuityPayCount);
			}
			}
		}
	});
}
//计算分期给付按钮
function prdCalcInstallment(obj) {
	var value = $(obj).val();
    $("#instalmentProduct", navTabFlag).val(value.split("@")[0]);
    $("#instalmentClaimLiab", navTabFlag).val(value.split("@")[1]);
    $("#instalmentBusiPrdCode", navTab.getCurrentPanel()).val($(obj).parent().next().next().text());
	$("#approve_instalmentForm", navTabFlag).submit();
}
//是否常规给付 初始化控制
var specialRemarkCode='${claimCaseVO.specialRemarkCode}';
if(specialRemarkCode==""){
	$("select#specialRemarkCode", navTabFlag).val("");
}
//选择赔付明细信息
$("input[type=radio]", navTabFlag).click(function(){
	$obj = $(this).attr("id");
	if($obj == "MedBody" || $obj == "UnMedBody"){
		var liabId=$(this).parent().parent().find("input[name=liabId]").val();
    	var liabName=$(this).parent().parent().find("input[name=liabId]").attr("title");
		var remark=$(this).parent().parent().find("input[name=clmRemark]").val();
		 
		var name=remark.split("--"); 
		var  values="";
	    for(var i=0;i<name.length;i++){
	    	 if(liabId.substr(0,1)=="9"&&name[i].indexOf(liabName) != "-1"){ 
	    		  
	         }else{
	        	 
	        	 values=values+name[i]+"\r";
	        	 
	         }
	    }
	 
	    $("textarea#"+$obj, navTabFlag).text(values);
	}
});
//收缩按钮 实现
function show(show){	
		if(show.value=="-"){
			document.getElementById(show.hiddenDivId).style.display="none";
			show.value="+";
		}else if (show.value=="+"){
			document.getElementById(show.hiddenDivId).style.display="";
			show.value="-";
		}
}
var repeatNumberType=$("#repeatNumberType", navTabFlag).val();
//重复账单类型非“其他”项时，不显示其他原因字段
if(repeatNumberType=="05"){
	$("#repeatNumberReason", navTabFlag).show();
}
//如果为其他勾选时显示，否则隐藏。
if($("#riskLabelEightAudit", navTab.getCurrentPanel()).attr("checked")=="checked"){
	$("#riskOtherReasonAudit", navTab.getCurrentPanel()).show();
} else {
	$("#riskOtherReasonAudit", navTab.getCurrentPanel()).hide();
}
if($("#cheatImplementationPersonnelSevenAudit", navTab.getCurrentPanel()).attr("checked")=="checked"){
	$("#otherImplementationPersonnelAudit", navTab.getCurrentPanel()).show();
} else {
	$("#otherImplementationPersonnelAudit", navTab.getCurrentPanel()).hide();
}
	var cheatDistinguishChannel = '${claimCaseCheatVO.cheatDistinguishChannel}'
	if(cheatDistinguishChannel !="09"){
		$("#otherCheatOptionIdAudit", navTab.getCurrentPanel()).hide();
		$("#otherCheatOptionIdAudit",navTab.getCurrentPanel()).val("");
	}else{
		$("#otherCheatOptionIdAudit", navTab.getCurrentPanel()).show();
	}
	//审批意见，根据超期原因不同，超期补偿标识联动
	function overReasonChange(t){
		var overReason=$("#overReason",navTab.getCurrentPanel()).val();
		if(overReason==1){
			$("#overCompFlagSP",navTab.getCurrentPanel()).attr("checked","checked");
			$("#overCompFlagSP",navTab.getCurrentPanel()).val(1);
			$("#overCompFlagSPValue",navTab.getCurrentPanel()).val(1);
			approveCountOverMoney();
		}else if(overReason==2){
			$("#overdueMoney",navTab.getCurrentPanel()).val(overduePayOld);
			$("#overCompFlagSP",navTab.getCurrentPanel()).removeAttr("checked");
			$("#overCompFlagSP",navTab.getCurrentPanel()).val(0);
			$("#overCompFlagSPValue",navTab.getCurrentPanel()).val(0);
			approveCountOverMoney();
		}else{
			$("#overCompFlagSP",navTab.getCurrentPanel()).removeAttr("checked");
			$("#overCompFlagSP",navTab.getCurrentPanel()).val(0);
			$("#overCompFlagSPValue",navTab.getCurrentPanel()).val(0);
		}
	}
	//超期天数控制
	var day1=$("#originOverDays",navTab.getCurrentPanel()).val();
	var day2=0;
	function changeOvers(a){
	    day2=$("#overDays",navTab.getCurrentPanel()).val();
	    if ( Number(day2)>Number(day1) ){
			alertMsg.error("超期核定天数仅能向下调整，请重新调整。");
		}else{
			approveCountOverMoney();
		}
	}
//重新计算超期补偿金额
function approveCountOverMoney(){
	var overReason=$("#overReason",navTab.getCurrentPanel()).val();
	var overCompFlag=$("#overCompFlagSP",navTab.getCurrentPanel()).val();
	var isOverComp=$("#isOverCompSP",navTab.getCurrentPanel()).val();
	var overDays=$("#overDays",navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/approve/countOverDueMoneyByOverDays_CLM_approveConclusionAction.action?claimCaseVO.caseId="+caseId
				+"&claimCaseVO.overReason="+overReason+"&claimCaseVO.overCompFlag="+overCompFlag+"&claimCaseVO.isOverComp="+isOverComp
				+"&claimCaseVO.overDays="+overDays,
		type : "POST",
		dataType : "json",
		success : function (data){
			//给超期补偿总金额赋值
			$("#overdueMoney",navTab.getCurrentPanel()).val(data.overdueMoney);
			//查询最新的赔付明细表
			$.ajax({
				url : "clm/approve/findPayDetailList_CLM_approveConclusionAction.action?caseId="+caseId,
				type : "POST",
				dataType : "html", 
				success : function (html){
					$("div#approve_payDetailList",navTabFlag).html(html).initUI(); 
					}
				});
			}
		});
}
</script>
<body>
<div >
	<div id="toApproveConclusionInit" class="panelPageFormContent main_tabdiv">
			<dl>
				<dt>赔案号</dt>
				<dd>
					<input type="text" size="30" value="${claimCaseVO.caseNo}" readonly="readonly" />
					<input type="hidden"  value="${userOrgCode}"  id ="userOrgCode"} />
				</dd>
			</dl>
			<dl>
				<dt>事 件 号</dt>
				<dd>
					<input type="text" size="30" value="${claimAccidentVO.accidentNo}" readonly="readonly" />
				</dd>
			</dl>
			<s:if test="claimCaseVO.isDeductPrem!=null">

					<dl>
						<dt>
							<a>在理赔金中扣除保费</a>
						</dt>
						<dd>
							<Field:codeTable cssClass="combox title" name="" value="${claimCaseVO.isDeductPrem}" disabled="true"
								tableName="APP___CLM__DBUSER.T_YES_NO"></Field:codeTable>
						</dd>
					</dl>
				</s:if>
			<dl>
				<dt>自助个团标识</dt>
				<dd>
					<select class="combox" id="autoCaseType" disabled="disabled">
						<option value=""></option>
						<option value="0" <s:if test="claimCaseVO.autoCaseType != null && claimCaseVO.autoCaseType == 0">selected="selected"</s:if>>个险</option>
						<option value="1"<s:if test="claimCaseVO.autoCaseType != null && claimCaseVO.autoCaseType == 1">selected="selected"</s:if>>个险团险</option>
					</select>
				</dd>
			</dl>
		</div>	
		<div class="panelPageFormContent main_tabdiv">
			<dl>
				<dt>
					预付标识
					
				</dt>
				<dd><input type="checkbox" style="border:0px;background:0px;width:auto;" size="5px" value="1" <s:if test="claimCaseVO.advanceFlag==1">checked="checked"</s:if> disabled/></dd>
			</dl>
			<dl>
				<dt><font class="point" style="color:red;">* </font>案件标识</dt>
				<dd disabled>
					<Field:codeTable cssClass="combox title"  name="" tableName="APP___CLM__DBUSER.T_CASE_LEVEL"
						value="${claimCaseVO.caseFlag}" nullOption="true" orderBy="code" whereClause="code in(1,2,3)" disabled="true"/>
				</dd>
			</dl>
			<dl>
		    <dt>超期</dt>
		    <dd>
				<input
					style="border: 0px; background: 0px; width: auto; float: left;"
					type="checkbox" size="5px" id="isOverComp" disabled="true"
					<s:if test="claimCaseVO.isOverComp eq 1"> checked="checked"</s:if>
					name="claimCaseVO.isOverComp" value="${claimCaseVO.isOverComp}" />
					<input type="hidden" id="isOverCompValue" name="claimCaseVO.isOverComp" value="${claimCaseVO.isOverComp}"/>
		    </dd>
	        </dl>
			<dl>
				<dt>
					超期补偿
				</dt>
				<dd><input type="checkbox" size="5px" style="border:0px;background:0px;width:auto;"
				 name="claimCaseVO.overCompFlag" value="${claimCaseVO.overCompFlag }" 
				 id="overCompFlag"<s:if test="claimCaseVO.overCompFlag==1">checked="checked"</s:if> disabled/></dd>
				 <input type="hidden" id="overCompFlagValue" name="claimCaseVO.overCompFlag" value="${claimCaseVO.overCompFlag}"/>
				 
			</dl>
    </div>
    <!-- 金额分配信息 -->
    
    <div class="main_box">
		<div class="main_heading"><h1><img src="clm/images/tubiao.png">金额分配信息
			<b class="maim_lpask"></b></h1></div>
			<div class="main_lptwo">
			<p><div class="tabdivclassbr">
						<table class="list" style="width: 100%;" >
							<thead>
								<tr align="center">
								    <th nowrap>选择</th>
									<th nowrap>保单号</th>
									<th nowrap>险种代码</th>
									<th nowrap>险种名称</th>
									<th nowrap>赔付金额</th>
									<th nowrap>分配情况</th>
									<th nowrap>结算项目</th>
								</tr>
							</thead>
							<tbody id="approve_paymentPlanTbody">
								<s:iterator value="listPaymentPlanVO" status="st">
									<tr align="center">
										 <td>
										    <input type="hidden" name="policyId" id="approve_policyId" value="${policyId }">
										 	<input type="radio" name="approve_caseId" onclick="queryBenePay(this);" value="${caseId }:${policyId}:${busiItemId}:${busiProdCode}">
										 </td>
										 <td>${policyCode }</td>
										 <td>${busiProdCode }</td>
										 <td>${productNameSys }</td>
										 <td><span>${payAmount }</span></td>
			 							 <td>
			 							 <s:if test="assignFlag == null">未分配</s:if>
			 							 <s:if test="assignFlag == 0">未分配</s:if>
		 								 <s:if test="assignFlag == 1">已分配</s:if>
									     </td>
										 <td>
											 <input type="hidden" id="approve_adjustTypeHidden" value="${adjustType }">
											 <input type="hidden" id="approve_adjustBusiIdHidden" value="${adjustBusiId }">
											 <span>${adjustType }</span>
										 </td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div></p>				
		</div>
	</div>
    <div class="panelPageFormContent" id="moneyAllotMes">
			<div>
				<div>
<div class="main_box">
	<div class="main_heading"><h1><img src="clm/images/tubiao.png">受益人/领款人信息 
		<b class="maim_lpask"></b></h1></div>
		<div class="main_lptwo">
		<p><div id="approve_claimBeneDiv" class="tabdivclassbr">
								<table class="list" style="width: 100%;" >
									<thead>
										<tr align="center">
										    <th nowrap>受益人姓名</th>
											<th nowrap>领款人姓名</th>
											<th nowrap>受益金额</th>
											<th nowrap>受益比例</th>
											<th nowrap>支付方式</th>
											<th nowrap>操作</th>
										</tr>
									</thead>
									<tbody id="approve_claimBeneTbody">
									</tbody>
						        </table>
					        </div></p>				
	</div>
</div>
					        
					        <!-- 受益人和领款人信息 -->
					        <div id="approve_benePayeeInfo">
					        <!-- 受益人信息 -->
					        
					        <div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">受益人信息
									<b class="maim_lpask"></b>
									</h1>
									</div>
									<input type="hidden" name="beneVO.legalPersonId" value="${beneVO.legalPersonId}" id="beneLegalPersonId">
									<div class="main_lptwo">
									<p><div class="panelPageFormContent" id="moneyAllotMesOneOne">
									<dl style="width: 100%;">
					<dt style="width: 12%;">
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<a disabled="true" id ="legalPersonQueryBeneStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoBene();"  >
						<button type="button" class="but_blue " id="legalPersonQueryBeneStyleButton" disabled="disabled" >法人信息录入</button>
						</a>
			<a target="dialog" id ="legalPersonQueryBene" href="javaScript:void(0)" rel="legalPersonQueryBene" style="display:none;" width="1000" height="450"  > 法人信息录入</a>
					</dt>
					</dl>
									    <dl>
											<dt>受益人与被保人关系</dt>
											<dd>
											   <s:if test="beneVO.beneRelation!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${beneVO.beneRelation}"/>
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>投保人与受益人关系</dt>
											<dd>
												<s:if test="claimPayVO.beneHolderRelation!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${claimPayVO.beneHolderRelation }"  />
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>姓名</dt>
											<dd>
											   ${beneVO.beneName }
											</dd>
										</dl>
										<dl>
											<dt>性别</dt>
											<dd>
											   <s:if test="beneVO.beneSex!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${beneVO.beneSex}"/>
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>出生日期</dt>
											<dd>
											   <s:date name="beneVO.beneBirth" format="yyyy-MM-dd"/>
											</dd>
										</dl>
										<dl>
											<dt>证件类型</dt>
											<dd>
											   <s:if test="beneVO.beneCertiType!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${beneVO.beneCertiType}"/>
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>证件号码</dt>
											<dd>
											   ${beneVO.beneCertiNo }
											</dd>
										</dl>
										<dl>
											<dt>受益人国籍</dt>
											<dd>
											   <s:if test="beneVO.beneCertiType!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_COUNTRY" value="${beneVO.beneNation}"/>
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>证件有效起期</dt>
											<dd>
											   <s:date name="beneVO.beneCertiStart" format="yyyy-MM-dd"/>
											</dd>
										</dl>
										<dl>
											<dt>证件有效止期</dt>
											<dd>
											   <s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>
											</dd>
										</dl>
										<!-- 51699增加受益相关信息录入 -->
										<dl>
											<dt><font>* </font>受益人职业代码</dt>
											<dd>
												<s:if test="beneVO.beneJobCode!=null">
													<Field:codeValue tableName="APP___CLM__DBUSER.T_JOB_CODE"	value="${beneVO.beneJobCode}"/>
												</s:if>
											</dd>
										</dl>
										<dl>
											<dt><font>* </font>受益人电话</dt>
											<dd>
											   ${beneVO.benePhone }
											</dd>
										</dl>
										<div class="mian_site ">
											<dl>
												<dt><font>* </font>受益人地址</dt>
											</dl>
											<div class="main_detail">
												<dl style="width: 25%">
													<dd>
							                           	<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${beneVO.beneProvince }"
							                           	whereClause="DISTRICT_LEVEL = 1"/>
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp省/直辖市<font style="color:#FF0000">* </font>
													</dd>
												</dl>
												<dl style="width: 25%">
													<dd>
														<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${beneVO.beneCity }"
								                           	whereClause="DISTRICT_LEVEL = 2"/>
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp市<font style="color:#FF0000">* </font>
													</dd>
												</dl>
												<dl style="width: 25%">
													<dd>
														<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${beneVO.beneDistrict }"
								                           	whereClause="DISTRICT_LEVEL = 3"/>
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp区/县<font style="color:#FF0000">* </font>
													</dd>
												</dl>
												<dl style="width: 25%">
													<dd>	
								                           ${beneVO.beneAddress}
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp乡镇/街道<font style="color:#FF0000">* </font>
													</dd>
												</dl>
											</div>
										</div>
										
										<dl>
											<dt>电子邮箱</dt>
											<dd>
												<input type="text"  name="beneVO.beneEmail" id="beneEmail" value="${beneVO.beneEmail }">
											</dd>
										</dl>
										<dl>
											<dt>受益金额</dt>
											<dd>
												${claimPayVO.payAmount }
											</dd>
										</dl>
										<dl>
											<dt>受益分子</dt>
											<dd>
											   ${claimPayVO.payMole }
											</dd>
										</dl>
										<dl>
											<dt>受益分母</dt>
											<dd>
											   ${claimPayVO.payDeno }
											</dd>
										</dl>
							</div></p>				
								</div>
							</div>
							<!-- 领款人信息 -->
							
							<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">领款人信息1
									<b class="maim_lpask"></b></h1>
									</div>
									<input type="hidden" name="claimPayVO.contraryPayFlag" value="${claimPayVO.contraryPayFlag}" id="contraryPayFlag">
									<input type="hidden" name="payeeVO.legalPersonId" value="${payeeVO.legalPersonId}" id="payeeLegalPersonId">
									<div class="main_lptwo">
									<p><div class="panelPageFormContent" id="moneyAllotMesOneTwo">
									<dl style="width: 100%;">
								<dt style="width: 12%;">
									<a disabled="true" id ="legalPersonQueryPayeeStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoPayee();" >
									<button type="button" class="but_blue " id="legalPersonQueryPayeeStyleButton" disabled="disabled" >法人信息录入</button>
									</a>
									<a target="dialog" id ="legalPersonQueryPayee" href="javaScript:void(0)" style="display:none;"  width="1000" height="450"  > 法人信息录入</a>
								</dt>
								<dt style="width: 12%;">
								对公支付，请勾选<input type="checkbox" id="contraryPayFlagCheckBoxId" onclick="chooseMethod();"/>
								</dt>
								</dl>
									    <dl>
											<dt>领款人与受益人关系</dt>
											<dd>
											     <s:if test="payeeVO.payeeRelation!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${payeeVO.payeeRelation}"/>
											     </s:if>
											</dd>
										</dl>
										<dl>
											<dt>姓名</dt>
											<dd>
											  ${payeeVO.payeeName }
											</dd>
										</dl>
										<dl>
											<dt>性别</dt>
											<dd>
											     <s:if test="payeeVO.payeeSex!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${payeeVO.payeeSex}"/>
											     </s:if>
											</dd>
										</dl>
										<dl>
											<dt>出生日期</dt>
											<dd>
											   <s:date name="payeeVO.payeeBirth" format="yyyy-MM-dd"/>
											</dd>
										</dl>
										<dl>
											<dt>证件类型</dt>
											<dd>
											   <s:if test="payeeVO.payeeCertiType!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${payeeVO.payeeCertiType}"/>
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>证件号码</dt>
											<dd>
											   ${payeeVO.payeeCertiNo }
											</dd>
										</dl>
										<dl>
											<dt>领款人国籍</dt>
											<dd>
											   <s:if test="payeeVO.payeeNation!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_COUNTRY" value="${payeeVO.payeeNation}"/>
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>证件有效起期</dt>
											<dd>
											   <s:date name="payeeVO.payeeCertiStart" format="yyyy-MM-dd"/>
											</dd>
										</dl>
										<dl>
											<dt>证件有效止期</dt>
											<dd>
											   <s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>
											</dd>
										</dl>
										<!-- 71408 反洗钱增加 -->
										<dl>
											<dt><font>* </font>领款人职业代码</dt>
											<dd>
												<s:if test="payeeVO.beneJobCode!=null">
													<Field:codeValue tableName="APP___CLM__DBUSER.T_JOB_CODE"	value="${payeeVO.beneJobCode}"/>
												</s:if>
											</dd>
										</dl>
										<dl>
											<dt><font>* </font>领款人电话</dt>
											<dd>
											   ${payeeVO.benePhone }
											</dd>
										</dl>
										<div class="mian_site">
											<dl>
												<dt><font>* </font>领款人地址</dt>
											</dl>
											<div class="main_detail">
												<dl style="width: 25%">
													<dd>
							                           	<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${payeeVO.payeeState }"
							                           	whereClause="DISTRICT_LEVEL = 1"/>
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp省/直辖市<font style="color:#FF0000">* </font>
													</dd>
												</dl>
												<dl style="width: 25%">
													<dd>
														<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${payeeVO.payeeCity }"
								                           	whereClause="DISTRICT_LEVEL = 2"/>
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp市<font style="color:#FF0000">* </font>
													</dd>
												</dl>
												<dl style="width: 25%">
													<dd>
														<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${payeeVO.payeeDistrict }"
								                           	whereClause="DISTRICT_LEVEL = 3"/>
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp区/县<font style="color:#FF0000">* </font>
													</dd>
												</dl>
												<dl style="width: 25%">
													<dd>	
								                           ${payeeVO.payeeAddress}
														&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp乡镇/街道<font style="color:#FF0000">* </font>
													</dd>
												</dl>
											</div>
										</div>
										
										<dl>
											<dt>电子邮箱</dt>
											<dd>
												<input type="text"  name="payeeVO.payeeEmail" id="payeeEmail" value="${payeeVO.payeeEmail }">
											</dd>
										</dl>
										<dl>
											<dt>支付方式</dt>
											<dd>
											   <s:if test="payeeVO.payMode!=null">
												  <Field:codeValue tableName="APP___CLM__DBUSER.T_PAY_MODE" value="${payeeVO.payMode}"/>
											   </s:if>
											</dd>
										</dl>
										<dl>
											<dt>银行账户名</dt>
											<dd>
											   ${payeeVO.accountName }
											</dd>
										</dl>
										<dl>
											<dt>银行账号</dt>
											<dd><span type="expandBankAccount">
											   ${payeeVO.accountNo }</span>
											</dd>
							            </dl>
							</div></p>				
								</div>
							</div>
					        </div>
							
							<div id="moneyAllotMesOneThreeDIV" style="display:none;">
								<!-- 投保人信息 -->
							<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">投保人信息
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<p><div class="panelPageFormContent" id="moneyAllotMesOneThree">
										    <div id="approve_policyHolder">
								        	<dl>
												<dt></dt>
												<dd>
												   <input type="hidden" id="approve_holderCustomerId" name="holderCustomerId">
												   <input type="checkbox" id="approve_checkHolder" disabled />退还投保人
												</dd>
											</dl>
											<dl>
												<dt>投保人姓名</dt>
												<dd>
												   <input type="text" name="holderName" id="approve_holderName" value="" readonly="readonly" />
												</dd>
											</dl>
											<dl>
												<dt>证件类型</dt>
												<dd>
													<Field:codeTable cssClass="combox title"  name="holderCerti" id="approve_holderCerti" value="" 
													tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0" nullOption="true" disabled="true"
													whereClause="code in ('0','5','2','b','4','1','8')"  orderBy="decode(code,'0','001','5','002','2','003','b','004','4','005','1','006','8','007', code)" />
												</dd>
											</dl>
											<dl>
												<dt>证件号码</dt>
												<dd>
												   <input type="text" name="holderCertiCode" id="approve_holderCertiCode" value="" readonly="readonly" />
												</dd>
											</dl>
											<dl>
												<dt>付费方式</dt>
												<dd>
												   <Field:codeTable cssClass="combox title"  name="holderMode" id="approve_holderMode" tableName="APP___CLM__DBUSER.T_PAY_MODE" 
												   nullOption="true" disabled="true"/>
												</dd>
											</dl>
											<dl  >
												<dt>银行编码</dt>
												<dd>
													
													<select  name="approve_holderBankCode" id="approve_holderBankCodeId" disabled="disabled">
												   </select>
												   <input type="hidden" name="approve_hiddenHolderBankId" id="approve_hiddenHolderBankId" value="">
												</dd>
										    </dl>
											<dl>
												<dt>银行账号</dt>
												<dd>
												   <input type="expandBankAccount" name="holderBankAccount" id="approve_holderBankAccount" readonly/>
												</dd>
											</dl>
											</div>
							</div></p>				
								</div>
							</div>
							</div>
							<!-- 年金转换 -->
							<div id="moneyAllotMesOneFourDIV" style="display: none;">
							<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">年金转换
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<p><div class="panelPageFormContent" id="moneyAllotMesOneFour">
										<div class="panelPageFormContent">
											<dl>
												<dt><input style="border:0px;background:0px;width:auto; float: right;" type="checkbox" id="approve_annuConvert" disabled="disabled"></dt>
												<dd>年金转换</dd>
											</dl>
											<dl>
												<dt>转换金额</dt>
												<dd id="countAnnuityMoney">
												  
												</dd>
											</dl>
										</div>
										 <div id="" class="tabdivclassbr">
											<table id="approve_annuDrawPlanTable" class="list" style="width: 100%;" >
												<thead>
													<tr align="center">
													    <th nowrap>序号</th>
														<th nowrap>受益人年龄</th>
														<th nowrap>年领金额</th>
													</tr>
												</thead>
												<tbody id="approve_annuDrawPlanTbody">
												</tbody>
											</table>
										</div>
							</div></p>				
								</div>
							</div>
						    
							</div>
							</div>
				</div>
                <!-- 分期支付计划 -->
                <s:if test="installmentList.size() != 0">
                	<div class="main_box">
						<div class="main_heading"><h1><img src="clm/images/tubiao.png">分期支付计划
							<b class="maim_lpask"></b></h1></div>
							<div class="main_lptwo">
							<p><div class="panelPageFormContent" id="moneyAllotMesTwo">
							<div class="panelPageFormContent">
							      <div class="tabdivclassbr" >
									<table class="list" style="width: 100%;" >
										<thead>
											<tr align="center">
											    <th nowrap>选择</th>
												<th nowrap>保单号</th>
												<th nowrap>险种代码</th>
												<th nowrap>险种名称</th>
												<th nowrap>保险责任</th>
											</tr>
										</thead>
										<tbody>
											<s:iterator value="installmentList" id="list">
												<tr align='center'>
													<td><input type="radio" name="installmentLiabInfo" onclick="prdCalcInstallment(this)" value="${productId }@${liabId}"></td>
												    <td>${policyCode}</td>
												    <td>${busiProdCode}</td>
												    <td>${productNameSys}</td>
												    <td>${liabId}</td>
											    </tr>
											</s:iterator>
										</tbody>
									</table>
								</div>
								<form id="approve_instalmentForm" action="clm/approve/prdCalcInstallmentShow_CLM_approveConclusionAction.action" method="post" class="pageForm required-validate"
  			                    onsubmit="return navTabSearch(this, 'approve_instalment')">
							    <input class="paymentPlanVOValue" type="hidden"
									name="paymentPlanVO.caseId" id="instalmentCaseId"
									value="${paymentPlanVO.caseId}">
								<input class="paymentPlanVOValue" type="hidden"
									name="paymentPlanVO.policyId" id="instalmentPolicy"
									value="${paymentPlanVO.policyId}">
								<input class="paymentPlanVOValue" type="hidden"
									name="paymentPlanVO.claimBusiProdId" id="instalmentBusiPrd"
									value="${paymentPlanVO.claimBusiProdId}">
								<input class="paymentPlanVOValue" type="hidden"
									name="paymentPlanVO.productId" id="instalmentProduct"
									value="${paymentPlanVO.productId}">
								<input class="paymentPlanVOValue" type="hidden"
									name="paymentPlanVO.liabId" id="instalmentClaimLiab"
									value="${paymentPlanVO.liabId}">
								<input class="paymentPlanVOValue" type="hidden" 
								name="paymentPlanVO.busiProdCode" id="instalmentBusiPrdCode" 
								value="${paymentPlanVO.busiProdCode}">
									
							    </form>
								<div id="approve_instalment">
                                  <%@ include file="/clm/pages/approve/approvePrdCalcInstallment.jsp" %>
								</div>
							</div>
				</div></p>				
						</div>
					</div>
				</s:if>
	            </div>
    <form action="clm/audit/saveApproveConclusion_CLM_addApproveConclusionAction.action?menuId=${menuId}" method="post"  
					id="toAuditConclusionInitform" class="pageForm required-validate" 
					onsubmit="return validateCallback(this, navTabAjaxDone)"  novalidate="novalidate">
    <!-- 案件赔付结论 -->
    <div class="main_box">
		<div class="main_heading"><h1><img src="clm/images/tubiao.png">案件赔付结论
			<b class="maim_lpask"></b></h1></div>
		<div class="main_lptwo">
		<p><div class="panelPageFormContent" id="casePayConclusionDiv">
			<div >
				<div class="panelPageFormContent">
				    <dl>
						<dt>
							赔付结论
						</dt>
						<dd>
<%-- 							<Field:codeTable cssClass="combox" name="name" tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION"   whereClause="1=1"  value="${claimAuditApproveVO.auditDecision}" orderBy="decode(code,'5','3.5',code)"/>
 --%> 						    <Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION"  value="${claimAuditApproveVO.auditDecision}" />  
						</dd>
					</dl>
					<dl>
						<dt>
							是否常规给付
						</dt>
						<dd>
						    <s:if test="claimAuditApproveVO.isCommon!=null">
							   <Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_COMMON" value="${claimAuditApproveVO.isCommon}"/>
 							</s:if>
						</dd>
					</dl>
					<dl>
						<dt>拒付/审核不通过原因</dt>
						<dd>
							<!-- 由于拒付/审核不通过原因 数据库是存在一个字段里的 只满足其一 所以做以下判断 -->
							<%-- <s:if test="claimAuditApproveVO.auditDecision == 3">
								 <Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_REJECT" value="${claimAuditApproveVO.auditRejectReason}"/>
	 						</s:if> --%>
	 						<s:if test="claimAuditApproveVO.auditDecision == 6">
								 <Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_REJECT_REASON" value="${claimAuditApproveVO.auditRejectReason}"/>
	 						</s:if>
	 						<s:else>
	 							<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_REJECT_REASON" value=""/>
	 						</s:else>
						</dd>
					</dl>
					
					<div id="refectDiv" style="display: none;">
							<dl style="width: 100%;height: auto">
								<dt>拒付原因</dt> 
								 <dd  style="width: 80%;">
									<%-- <Field:codeTable id="rejectCode" name="claimLiabVO.rejectCode" tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_REJECT" cssClass="combox title notuseflagDelete"
											nullOption="true" whereClause="code in ('01','02','03','04','05','06','07','08','99')" orderBy="decode(code,'01','001','02','002','03','003','04','004','05','005','06','006','07','007','08','008','99','009', code)"/> --%>
									<span class="spanbox1" style="width: 100%;">
										<span>
											<input type="checkbox"
											id="rejectCode1" name="claimAuditApproveVO.auditRejectReason" value="01" disabled="disabled"/>故意不如实告知
										</span>
										<span>
											<input type="checkbox"
											id="rejectCode2" name="claimAuditApproveVO.auditRejectReason" value="02" disabled="disabled"/>重大过失不如实告知
										</span>
										<span>
											<input  type="checkbox"
											id="rejectCode3" name="claimAuditApproveVO.auditRejectReason" value="03" disabled="disabled"/>合同约定的责任免除项
										</span>
										<span>
											<input type="checkbox"
											id="rejectCode4" name="claimAuditApproveVO.auditRejectReason" value="04" disabled="disabled"/>未达到合同约定的保险金赔付标准
										</span>
										<%-- <span>
											<input type="checkbox"
											id="rejectCode5" name="claimAuditApproveVO.auditRejectReason" value="05" disabled="disabled"/>非保险合同保障对象
										</span>
										<span>
											<input type="checkbox"
											id="rejectCode6" name="claimAuditApproveVO.auditRejectReason" value="06" disabled="disabled"/>不在有效保障期内
										</span>
										<span>
											<input  type="checkbox"
											id="rejectCode7" name="claimAuditApproveVO.auditRejectReason" value="07" disabled="disabled"/>不在保险责任范围内
										</span>
										<span>
											<input  type="checkbox"
											id="rejectCode8" name="claimAuditApproveVO.auditRejectReason" value="08" disabled="disabled"/>索赔单证不齐备或无效
										</span> --%>
									</span>
									<span class="spanbox2" style="width: 100%;">
										<span>
											<input type="checkbox"
											id="rejectCode99" name="claimAuditApproveVO.auditRejectReason" value="99" onclick="rejectClick()"  disabled="disabled"/>其他
										</span>
									</span>
								</dd>
							</dl>
					</div>
								
					<dl>
						<dt>其他原因</dt>
						<dd>
							${claimAuditApproveVO.otherReason}
						</dd>
					</dl>
					<dl>
						<dt>
							特殊备注
						</dt>
						<dd>
						     <Field:codeValue value="${claimCaseVO.specialRemarkCode}"  
                             tableName="APP___CLM__DBUSER.T_SPECIAL_REMARK" /> 
						</dd>
					</dl>
					<dl>
						<dt>
							损失原因
						</dt>
						<dd>
						     <s:if test="claimCaseVO.lossReasonCode!=null">
							   <Field:codeValue tableName="APP___CLM__DBUSER.T_LOSS_REASON" value="${claimCaseVO.lossReasonCode}"/>
 							 </s:if>
						</dd>
					</dl>
					<dl>
						<dt>
							损失程度 
						</dt>
						<dd>
						     <s:if test="claimCaseVO.lossLevelCode!=null">
							   <Field:codeValue tableName="APP___CLM__DBUSER.T_LOSS_LEVEL" value="${claimCaseVO.lossLevelCode}"/>
 							 </s:if>
						</dd>
					</dl>
					<dl>
						<dt>
							损失程度分级
						</dt>
						<dd id="lossLevelGrade">
						    <s:if test="claimCaseVO.lossLevelCode == '00' || claimCaseVO.lossLevelCode == '01' || claimCaseVO.lossLevelCode == '02' || claimCaseVO.lossLevelCode == '03'">
						    	一级
						    </s:if>
						    <s:if test="claimCaseVO.lossLevelCode == '04' || claimCaseVO.lossLevelCode == '05' || claimCaseVO.lossLevelCode == '06' || claimCaseVO.lossLevelCode == '07'">
						    	二级
						    </s:if>
						    <s:if test="claimCaseVO.lossLevelCode == '08' || claimCaseVO.lossLevelCode == '09' || claimCaseVO.lossLevelCode == '10' || claimCaseVO.lossLevelCode == '11'">
						    	三级
						    </s:if>
						</dd>
					</dl>
					<dl style="width: 100%;height: auto">
						<dt>审核意见</dt>
						<dd >
							<textarea rows="3" cols="70" readonly>${claimAuditApproveVO.auditRemark}</textarea>
						</dd>
					</dl>
							<dl style="height: auto; width: 100%;">
			 <dt>
				<font class="point" style="color: red;">* </font>是否调查标识
			</dt>
			<dd >
				<input id="surveyMark" type="hidden" value="${claimCaseVO.surveyMark }"/>
			    <Field:codeTable cssClass="combox title" name="claimCaseVO.surveyMark" whereClause="1=1"  orderBy="yes_no*-1" value="${claimCaseVO.surveyMark }"
							tableName="APP___CLM__DBUSER.T_YES_NO" id="surveyMark" onChange="clickFlag(this.value)"  defaultValue="0"/> 
			</dd>
			<dt>
				<font class="point" style="color: red;">* </font>调查阳性标识
			</dt>
			<dd >
			 <s:if test="claimCaseVO.surveyMark != 1">
			<input id="positiveFlag"  value="" readonly="readonly"/>
			</s:if>
			<s:else> 
			<input id="positiveFlag" type="hidden" value="${claimCaseVO.positiveFlag }"/>
			    <Field:codeTable cssClass="combox title" name="claimCaseVO.positiveFlag" whereClause="1=1"  orderBy="yes_no*-1" value="${claimCaseVO.positiveFlag }"
							tableName="APP___CLM__DBUSER.T_YES_NO" id="positiveFlag" onChange="clickFlag(this.value)"  defaultValue="0"/> 
			</s:else>
			</dd>
			
		</dl>
		<dl>
			<dt>
				<a>院外医疗费</a>
			</dt>
			<dd>
				<dd>
									<s:if test="claimCaseVO.offHospFlag eq 1">
								    <input type="text" readOnly  value="是"/>
								  </s:if>
								  <s:else>    
								    <input type="text" readOnly  value="否"/>
                                  </s:else>      
         </dd>
			</dd>
		</dl>
			<dl>
			<dt>
				<a>远程鉴定</a>
			</dt>
			<dd>
									<s:if test="claimCaseVO.remoteIdentFlag eq 1">
								    <input type="text" readOnly  value="是"/>
								  </s:if>
								  <s:else>    
								    <input type="text" readOnly  value="否"/>
                                  </s:else>      
         </dd>
			</dl>
		
		<s:if test="userOrgCode==86">
			<dl style="height: auto; width: 100%;">
			<dt>
				<a>作业提示</a>
			</dt>
				<dd>
                  <textarea rows="3" cols="80" id="jobPrompt" 
					onpropertychange="if(value.length>1500) value=value.substring(0,1500)" readonly>${claimCaseVO.jobPrompt}</textarea>
                 </dd>
			</dl>
		</s:if>

					<dl style="width: 100%;height: auto">
						<dt>审核人</dt>
						<dd >
							   ${claimAuditApproveVO.auditor}</textarea>
						</dd>
					</dl>
<!-- 					<div id="specificCauseDiv" style="display: none;"> -->
<!-- 					<dl style="height: auto; width: 100%;"> -->
<!-- 								<dt> -->
<!-- 									具体理由 -->
<!-- 								</dt> -->
<!-- 								<dd > -->
<!-- 									<textarea rows="3" cols="80" id="specificCause" disabled="disabled" -->
<!-- 										onpropertychange="if(value.length>1500) value=value.substring(0,1500)" -->
<%-- 										name="claimCaseVO.specificCause">${claimCaseVO.specificCause}</textarea> --%>
<!-- 								</dd> -->
<!-- 							</dl> -->
<!-- 					</div> -->
	            </div>
			</div>
    </div></p>				
		</div>
	</div>
    
    
    <!-- 赔付通知书 -->
    <div class="main_box">
		<div class="main_heading"><h1><img src="clm/images/tubiao.png">赔付通知书
			<b class="maim_lpask"></b></h1></div>
			<div class="main_lptwo">
				<div id="main_1" class="main_borderbg">
                            <ul class="main_ul">
                                <li class="clearfix">
                                    <h5 hasborder="true"><b id="two" class="main_minus"></b><span>受益人和领款人信息</span></h5>
										 <div class="main_foldContent">
                                            <div class="main_bqtabdivbr">
												<!-- 受益人和领款人信息 -->
												    <div class="panelPageFormContent" id="payDocumentDivOne">
															<div >
																<div>
																	<div class="panelPageFormContent">
																	<input type="checkbox" <s:if test="claimCaseVO.sendBeneDocFlag==1">checked='checked'</s:if> size="5px" style="border:0px;background:0px;width:auto;padding-left: 20px;" value="0" disabled/>按受益人发送赔付通知书
																	<div class="tabdivclassbr main_tabdiv" >
																	<table class="list" style="width: 100%;">
																		<thead>
																			<tr align="center">
																				<td nowrap>序号</td>
																				<td nowrap>受益人姓名</td>
																				<td nowrap>领款人姓名</td>
																				<td nowrap>受益金额</td>
																				<td nowrap>受益比例</td>
																				<td nowrap>领款方式</td>
																				<td nowrap>银行账号</td>
																				<td nowrap>领款金额</td>
																			</tr>
																		</thead>
																		<tbody>
																		    <s:iterator value="listPaymentPlanVOBP" var="status" status="var">
																			    <tr align="center">
																					<td><s:property value="#var.index+1"></s:property></td>
																					<td><s:property value="beneName"></s:property></td>
																					<td><s:property value="payeeName"></s:property></td>
																					<td><s:property value="payAmount"></s:property></td>
																					<td><s:property value="payModeDeno"></s:property></td>
																					<td><s:property value="payMode"></s:property></td>
																					<td><s:property value="accountNo"></s:property></td>
																					<td><s:property value="payAmount"></s:property></td>
																				</tr>
																			</s:iterator>
																			<%-- <s:iterator value="beneAndPayeeVOList" var="status" status="var">
																				<tr align="center">
																					<td><s:property value="#var.index+1"></s:property></td>
																					<td><s:property value="claimbene.beneName"></s:property></td>
																					<td><s:property value="claimpayee.payeeName"></s:property></td>
																					<td><s:property value="claimpay.payAmount"></s:property></td>
																					<td><s:property value="claimpay.payMole"></s:property>/<s:property value="claimpay.payDeno"></s:property></td>
																					<td><s:property value="claimpayee.payMode"></s:property></td>
																					<td><s:property value="claimpayee.accountNo"></s:property></td>
																					<td><s:property value="claimpay.payAmount"></s:property></td>
																				</tr>
																			</s:iterator> --%>
																		</tbody>
																	</table>
																	</div>
																   </div>
													            </div>
															</div>
												    </div>			
					   						 	</div>
					    					</div>
					    				</li>
					    			</ul>
					    		</div>
					    		
					    			<div id="main_2" class="main_borderbg">
		                            	<ul class="main_ul">
		                                	<li class="clearfix">
		                                   	 <h5 hasborder="true"><b id="two" class="main_minus"></b><span>赔付明细表</span></h5>
		                                    	<div class="main_foldContent" id="approve_payDetailList" >
		                                    	<s:if test="payDetailListMedical.size!=0">
			                                       <ul class="main_ul">
                                       				<li class="clearfix">
                                          				<h5 hasborder="false"><b id="three" class="main_minuss"></b><span>医疗类</span></h5>
                                          					<div class="main_foldContent">
                                            					<div class="main_bqtabdivbr">
                                            							<!-- 医疗类 -->
																	    <div class="panelPageFormContent" id="payDocumentDivTwoOne">
										<!-- 										<div > -->
										<!-- 											<div> -->
										<!-- 											<div class="panelPageFormContent" id="medical"> -->
																					<div class="tabdivclassbr">
																					<table class="list" style="width: 100%;">
																						<thead>
																							<tr align="center">
																								<td nowrap>选择</td>
																								<td nowrap>序号</td>
																								<td nowrap>保单号</td>
																								<td nowrap>险种名称</td>
																								<td nowrap>保险责任名称</td>
																								<td nowrap>账单金额</td>
																								<td nowrap>实际住院天数</td>
																								<td nowrap>预付金额</td>
																								<td nowrap>合同结算金额</td>
																								<td nowrap>超期补偿金额</td>
																								<td nowrap>实际赔付金额</td>
																								<td nowrap>剩余有效保额</td>
																								<td nowrap>合同状态</td>
																							</tr>
																						</thead>
																						<tbody id="approve_payDetailListMedicalTbody">
																							<s:iterator value="payDetailListMedical" var="status" status="var">
																								<tr align="center">
																									<td><input type="radio" name="r1" id="MedBody"/></td>
																									<td><s:property value="#var.index+1"></s:property></td>
																									<td><s:property value="policyCode"></s:property></td>
																									<td><s:property value="productNameSys"></s:property></td>
																									<td><s:property value="liabName"></s:property></td>
																									<td><s:property value="sumAmount"></s:property><s:if test="sumAmount eq null">0</s:if></td>
																									<td><s:property value="reallyDay"></s:property><s:if test="reallyDay eq null">0</s:if></td>
																									<td><s:property value="advancePay"></s:property><s:if test="advancePay eq null">0</s:if></td>
																									<td><s:property value="freeAmount"></s:property><s:if test="freeAmount eq null">0</s:if></td>
																									<td>
 																										<s:property value="overdueMoney"></s:property><s:if test="overdueMoney eq null">0</s:if>
																									</td>
																									<td><s:property value="actualPay"></s:property><s:if test="actualPay eq null">0</s:if></td>
																									<td><s:property value="remainEffPay"></s:property><s:if test="remainEffPay eq null">0</s:if></td>
																									<td><Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/>
																									<input id="clmRemark"  type="hidden" name="clmRemark" value="${clmRemark}" />
																								    <input name="liabId"  value="${liabId}" type="hidden" title="<Field:codeValue value='${liabId}'  tableName='APP___CLM__DBUSER.t_liability' />" />
																									
																									</td>
																								</tr>
																							</s:iterator>
																						</tbody>
																					</table>
																					</div>
																					<div class="panelPageFormContent main_tabdiv">
																					<dl style="width: 100%;height: auto">
																						<dt>备注信息</dt>
																						<dd >
																							<textarea maxlength="5000" id="MedBody" name=""  rows="3" cols="70" readonly></textarea>
																						</dd>	
																					</dl>
																				</div>	
																		</div>
                                            					</div>
                                            				</div>
                                            			</li>
                                            		</ul>	 
			                                    </s:if>
			                                    
		                                      		
							    				
							    				<s:if test="payDetailListUnMedical.size!=0">
							    					<ul class="main_ul">
                                       				<li class="clearfix">
                                          				<h5 hasborder="false"><b id="three" class="main_minuss"></b><span>非医疗类</span></h5>
                                          				<div class="main_foldContent">
                                            				<div class="main_bqtabdivbr">
	                                            					  <!-- 非医疗类 -->
															    <div class="panelPageFormContent" id="payDocumentDivTwoTwo">
																		<div >
																			<div>
																			<div class="panelPageFormContent" id="nomedical">
																			<div class="tabdivclassbr" >
																			
																			<table class="list" style="width: 100%;">
																				<thead>
																					<tr align="center">
																						<td nowrap>选择</td>
																						<td nowrap>序号</td>
																						<td nowrap>保单号</td>
																						<td nowrap>险种名称</td>
																						<td nowrap>保险责任名称</td>
																						<td nowrap>保险金额</td>
																						<td nowrap>年度红利</td>
																						<td nowrap>终了红利</td>
																						<td nowrap>合同结算金额</td>
														                                <td nowrap>超期补偿金额</td>
																						<td nowrap>实际赔付金额</td>
																						<td nowrap>剩余有效保额</td>
																						<td nowrap>合同状态</td>
																					</tr>
																				</thead>
																				<tbody id="approve_payDetailListUnMedicalTbody">
																					<s:iterator value="payDetailListUnMedical" var="status" status="var" >
																						<tr>
																							<td><input type="radio" name="r2" id="UnMedBody"/></td>
																							<td><s:property value="#var.index+1"></s:property></td>
																							<td><s:property value="policyCode"></s:property></td>
																							<td><s:property value="productNameSys"></s:property></td>
																							<td><s:property value="liabName"></s:property></td>
																							<td><s:property value="amount"></s:property></td>
																							<td><s:property value="bonusSa"></s:property><s:if test="bonusSa eq null">0</s:if></td>
																							<td><s:property value="bonusSaEnd"></s:property><s:if test="bonusSaEnd eq null">0</s:if></td>
																							<td><s:property value="freeAmount"></s:property><s:if test="freeAmount eq null">0</s:if></td>
																							<td>
																								<s:property value="overdueMoney"></s:property><s:if test="overdueMoney eq null">0</s:if>
																							</td>
																							<td><s:property value="actualPay"></s:property><s:if test="actualPay eq null">0</s:if></td>
																					        <td><s:property value="remainEffPay"></s:property><s:if test="remainEffPay eq null">0</s:if></td>
																					        <td><Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/>
																							<input id="clmRemark" name="clmRemark" value="${clmRemark}" type="hidden"/>
																	                        <input name="liabId"  value="${liabId}" type="hidden" title="<Field:codeValue value='${liabId}'  tableName='APP___CLM__DBUSER.t_liability' />" />
																							</td>
																						</tr>
																					</s:iterator>
																				</tbody>
																			</table>
																			</div>
																			<div class="panelPageFormContent main_tabdiv">
																			<dl  style="width: 100%;height: auto">
																				<dt>备注信息</dt>
																				<dd>
																					<textarea maxlength="5000" id="UnMedBody" name=""  rows="3" cols="70" readonly>
																					</textarea>
																				</dd>	
																			</dl>
																			</div>	
																		    </div>
																            </div>
																		</div>
															    </div>
                                            				
                                            				</div>
                                            			</div>	
							    					</li>
							    					</ul>
							    			 </s:if>
							    					</div>
							    				</li>
							    			</ul>
						    		</div>
							</div>
						</div>
		
    <!-- 审批意见 -->
    <div class="panelPageFormContent" id="approveView">
		<div class="main_box">
			<div class="main_heading"><h1><img src="clm/images/tubiao.png">审批意见
				<b class="maim_lpask"></b></h1></div>
				<div class="main_lptwo">
				<p><div class="panelPageFormContent" id="approveConclusionOne">	
						<dl style="height: auto">
							<dt><font class="point" style="color:red;">* </font>审批意见（包括符号最多700汉字）</dt>
							<dd>
								<textarea id="approveRemark" onpropertychange="if(value.length>1000) value=value.substring(0,700)"  rows="3" cols="70" name="claimCaseVO.approveRemark">${claimCaseVO.approveRemark}</textarea>
							</dd>
						</dl>
					</div>
					<div class="panelPageFormContent main_tabdiv" id="approveConclusionTwo">	
						<dl>
							<dt>审批结论<font class="point" style="color:red;">*</font></dt>
							<dd>
								<Field:codeTable cssClass="combox title"  id="approveDecision"  onChange="changeResean(this)"
									name="claimCaseVO.approveDecision" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_DECISION"
									value="${claimCaseVO.approveDecision}" nullOption="true" />
							</dd>
						</dl>
				
						<dl>
							<dt>案件标识<font class="point" style="color:red;">*</font></dt>
							<dd>
								<Field:codeTable cssClass="combox title"  id="approveCaseFlag"
									name="claimCaseVO.caseFlag" tableName="APP___CLM__DBUSER.T_CASE_LEVEL"
									value="${claimCaseVO.caseFlag}"  orderBy="code"  disabled="true" whereClause="code in(1,3)"/>
							</dd>
						</dl>
						<dl id="ss_flag" >
							<dt >诉讼案件</dt>
							<dd >
								<select class="combox title"  name="claimCaseVO.isLawsuits" id="isLawsuits"  onchange="isLawsuitsChange()">
									<option value="0" <s:if test="claimCaseVO.isLawsuits == 0">selected</s:if>>否</option>
									<option value="1" <s:if test="claimCaseVO.isLawsuits == 1">selected</s:if>>是</option>
								</select>
							</dd>
					    </dl>
						<input type="hidden" name="claimCaseVO.caseFlag" value="${claimCaseVO.caseFlag}" id="approveCaseFlagH" />
						<dl>
					    <dt>超期</dt>
					    <dd>
							<input
								style="border: 0px; background: 0px; width: auto; float: left;"
								type="checkbox" size="5px" id="isOverCompSP" disabled="true"
								<s:if test="claimCaseVO.isOverCompSP eq 1"> checked="checked"</s:if>
								name="claimCaseVO.isOverCompSP" value="${claimCaseVO.isOverCompSP}" />
								<input type="hidden" id="isOverCompSPValue" name="claimCaseVO.isOverCompSP" value="${claimCaseVO.isOverCompSP}"/>
					    </dd>
				        </dl>
						<dl id="overCompFlagTitle">
							<dt>
								超期补偿
							</dt>
							<dd >
								<input  type="checkbox" style="border:0px;background:0px;width:auto;" float="right" size="5px" name="claimCaseVO.overCompFlagSP" value="${claimCaseVO.overCompFlagSP }" disabled="true" id="overCompFlagSP"<s:if test="claimCaseVO.overCompFlagSP==1">checked="checked"</s:if>/>
								<input type="hidden" id="overCompFlagSPValue" name="claimCaseVO.overCompFlagSP" value="${claimCaseVO.overCompFlagSP}"/>
							</dd>
						</dl>
						<dl>
							<dt>不通过原因<font class="point" style="color:red;">*</font></dt>
							<dd>
								<Field:codeTable cssClass="combox title"  id="approveRejectReason"
									name="claimCaseVO.approveRejectReason" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_REJECT"
									value="${claimCaseVO.approveRejectReason}" nullOption="true" />
							</dd>
						</dl>
						<s:if test="claimCaseVO.channelCode !='03'">
							<dl>
							    <dt>免材料标识</dt>
							    <dd>
							        <div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimCaseVO.materialFreeFlag}" /></div>
							    </dd>
							</dl>
						</s:if>
						    <dl>
							    <dt>电子签名标识</dt>
							    <dd>
							        <div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimCaseVO.signatureTraceFlag}" /></div>
							    </dd>
							</dl>							
						<dl>
			                <dt>采集方式</dt>
			                <dd>
			                    <Field:codeValue tableName="APP___CLM__DBUSER.T_ACQUIST_WAY" value="${claimCaseVO.acquistWay}"/>
			                </dd>
			             </dl>
						<dl>
							<dt>
								重复账单号标识<font class="point" style="color:red;">*</font>
							</dt>
							<dd>
							    <Field:codeTable cssClass="combox title" name="claimCaseVO.repeatNumberFlag"  value="${claimCaseVO.repeatNumberFlag }"
										nullOption="true"	tableName="APP___CLM__DBUSER.T_YES_NO" disabled="true"  defaultValue="0"/>
							</dd> 
						</dl>
						<dl>
							<dt>
								<font class="point" style="color: red;">* </font><a>重复账单类型</a>
							</dt>
							<dd>
							    <Field:codeTable cssClass="combox title" name="claimCaseVO.repeatNumberType"  value="${claimCaseVO.repeatNumberType }"
										nullOption="true"	tableName="APP___CLM__DBUSER.T_CLAIM_REPEAT" disabled="true" id="repeatNumberType"/>
							</dd>
						</dl>
						<dl style="display: none;" id="repeatNumberReason">
						    <dt>其他原因</dt>
						    <dd>
						        <input type="text"  name="claimCaseVO.repeatNumberReason" value="${claimCaseVO.repeatNumberReason}" readonly="readonly"/>
						    </dd>
						</dl>
						<dl>
						    <dt>
								<font class="point" style="color: red;">* </font><a>短信发送标识</a>
							</dt>
							<dd>
								<Field:codeTable cssClass="combox title" name="claimCaseVO.smsSendFlag" whereClause="1=1"  orderBy="yes_no*-1" value="${claimCaseVO.smsSendFlag }"
											tableName="APP___CLM__DBUSER.T_YES_NO" />
							</dd>
						</dl>
						<dl>
							<dt>
								<font class="point" style="color: red;">* </font><a>实时支付</a>
							</dt>
							<dd>
								<Field:codeTable cssClass="combox title" name="claimCaseVO.realtimePay" whereClause="1=1"  orderBy="yes_no*-1" value="${claimCaseVO.realtimePay }"
											tableName="APP___CLM__DBUSER.T_YES_NO" disabled="true" />
							</dd>
						</dl>
						<dl>
							<dt>
								<a>业务员自保件</a>
							</dt>
							<dd>
								<dd>
				                   <Field:codeTable cssClass="combox title" name="claimCaseVO.salesmanSelfInsurance" value="${claimCaseVO.salesmanSelfInsurance}" whereClause="1=1" 
				                             tableName="APP___CLM__DBUSER.T_YES_NO" orderBy="yes_no*-1" id="salesmanSelfInsuranceAudit" disabled="true" ></Field:codeTable>
				                 </dd>
							</dd>
						</dl>
						<dl>
							<dt>
								<a>赔案欺诈风险标签</a>
							</dt>
							<dd>
								<select id="isRisk" class="combox title" disabled="disabled"
											name="claimCaseVO.isRisk"	onchange="riskChange(this);">
								<option value="0">无风险</option>
								<option value="1">有风险</option>
								</select>
							</dd>
						</dl>
					</div>
					<div class="divfclass" id="overDueTitle">
					<h1>
						<img src="clm/images/tubiao.png">超期补偿核定
					</h1>
				    </div>
					<div class="panelPageFormContent" id="overDueDIV">
						<dl>
							<dt>
								<a>超期原因</a>
							</dt>
							<dd>
								<select id="overReason" class="combox title" name="claimCaseVO.overReason"	onchange="overReasonChange(this);">
									<option value=""></option>
									<option value="1" <s:if test="claimCaseVO.overReason == 1">selected</s:if>>公司原因</option>
									<option value="2" <s:if test="claimCaseVO.overReason == 2">selected</s:if>>因客户原因导致超期已告知并获认可</option>
								</select>
							</dd>
						</dl>
						<dl>
							<dt>
								<a>超期核定天数</a>
							</dt>
							<dd>
							    <input   name="claimCaseVO.overDays" id="overDays" type="text" value="${claimCaseVO.overDays}" onchange="changeOvers(this)"/>
							    <input id="originOverDays" type="hidden" value="${claimCaseVO.originOverDays}"/>
							</dd>
						</dl>
						<dl>
							<dt>
								<a>超期核定补偿总金额</a>
							</dt>
							<dd>
							    <input   name="claimCaseVO.overdueMoney" id="overdueMoney" type="text" value="${claimCaseVO.overdueMoney}"/>
							    <input id="overdueMoneyOld" type="hidden" value="${claimCaseVO.overdueMoney}"/>
							</dd>
						</dl>
					</div>
					<div style="display: none" id="riskDiv">
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">赔案欺诈风险项
		</h1>
	</div>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">一级标签
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl style="width: 100%;height: auto">
				<dt></dt> 
				 <dd  style="width: 90%; margin-left: 10%" >
					<span class="spanbox3" style="width: 100%;">
						<span>
							<input type="checkbox"
									id="riskLabelNineAudit" name="claimCaseVO.riskLabel" value="9" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("9") >= 0}'>checked="checked"</s:if>/>故意虚构保险标的
						</span>
						<span>
							<input type="checkbox"
											id="riskLabelTwoAudit" name="claimCaseVO.riskLabel" value="2" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("2") >= 0}'>checked="checked"</s:if>/>编造未曾发生的保险事故
						</span>
						<span>
							<input  type="checkbox"
											id="riskLabelFourAudit" name="claimCaseVO.riskLabel" value="4" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("4") >= 0}'>checked="checked"</s:if>/>编造虚假的事故原因
						</span>
						<span>
							<input type="checkbox"
											id="riskLabelSixAudit" name="claimCaseVO.riskLabel" value="6" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("6") >= 0}'>checked="checked"</s:if>/>夸大损失程度
						</span>
						<span>
							<input type="checkbox"
											id="riskLabelTenAudit" name="claimCaseVO.riskLabel" value="10" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("10") >= 0}'>checked="checked"</s:if>/>故意造成保险事故
						</span>
						<span>
							<input type="checkbox"
											id="riskLabelOneAudit" name="claimCaseVO.riskLabel" value="1" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("10") < 0 && claimCaseVO.riskLabel.indexOf("11") < 0 && claimCaseVO.riskLabel.indexOf("1") >= 0}'>checked="checked"</s:if>/>故意不如实告知
						</span>
					</span>
				</dd>
			</dl>
<!-- 		<div class="pageFormdiv"> -->
<!-- 			<button class="but_blue" type="button" -->
<!-- 				onclick="saveRisk()">保存</button> -->
<!-- 		</div> -->
	</div>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">二级标签
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl style="width: 100%;height: auto">
			<dt></dt> 
				<dd  style="width: 90%; margin-left: 10%" >
					<span class="spanbox3" style="width: 100%;">
						<span>
							<input type="checkbox"
											id="riskLabelThreeAudit" name="claimCaseVO.riskLabel"  value="3" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("3")  >= 0}'>checked="checked"</s:if>/>伪造、编造索赔材料
						</span>
						<span>
							<input type="checkbox"
											id="riskLabelSevenAudit" name="claimCaseVO.riskLabel" value="7" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("7")  >= 0}'>checked="checked"</s:if>/>冒名顶替
						</span>
						<span>
							<input  type="checkbox"
											id="riskLabelFiveAudit" name="claimCaseVO.riskLabel" value="5" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("5")  >= 0}'>checked="checked"</s:if>/>倒签单
						</span>
						<span>
							<input type="checkbox"
											id="riskLabelElevenAudit" name="claimCaseVO.riskLabel" value="11" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("11") >= 0}'>checked="checked"</s:if>/>同业高额投保未告知
						</span>
						<span>
							<input type="checkbox" onclick="riskLabelEightAudit(this)"
											id="riskLabelEightAudit" name="claimCaseVO.riskLabel" value="8" disabled="disabled" <s:if test='%{claimCaseVO.riskLabel.indexOf("8") >=0}'>checked="checked"</s:if>/>其他
						</span>
						<span>
							<input type="text" style="width:150%"
											id="riskOtherReasonAudit" name="claimCaseVO.riskOtherReason" maxlength="50" disabled="disabled" value="${claimCaseVO.riskOtherReason }" onblur="">
						</span>
					</span>
				</dd>
			</dl>
		</div>
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">欺诈案件信息项
						</h1>
					</div>
					
					<div class="panelPageFormContent main_tabdiv">
						<dl>
							<dt>作案性质</dt>
							<dd>
								<span><input name="claimCaseCheatVO.committingNature" id="committingNatureOneAudit" type="radio" value="01" disabled="disabled" <s:if test="claimCaseCheatVO.committingNature == 01">checked="checked"</s:if> />团伙</span>
								<span><input name="claimCaseCheatVO.committingNature" id="committingNatureTwoAudit" type="radio" value="02" disabled="disabled" <s:if test="claimCaseCheatVO.committingNature == 02">checked="checked"</s:if>  />个人</span>
							</dd>
						</dl>
						<dl>
							<dt>公检法立案</dt>
							<dd>
								<span><input name="claimCaseCheatVO.inspectionRegisterFlag" id="inspectionRegisterFlagOneAudit" type="radio" value="1" disabled="disabled" <s:if test="claimCaseCheatVO.inspectionRegisterFlag == 1">checked="checked"</s:if>/>是</span>
								<span><input name="claimCaseCheatVO.inspectionRegisterFlag" id="inspectionRegisterFlagTwoAudit" type="radio" value="0" disabled="disabled" <s:if test="claimCaseCheatVO.inspectionRegisterFlag == 0">checked="checked"</s:if>/>否</span>
							</dd>
						</dl>
						<dl>
							<dt>公检法立案日期</dt>
							<dd>
								<input type="expandDateYMD" name="claimCaseCheatVO.inspectionRegisterTime" id="inspectionRegisterTimeIdAudit" class="date" disabled="disabled" value="<s:date name="claimCaseCheatVO.inspectionRegisterTime" format='yyyy-MM-dd' />"/>
							</dd>
						</dl>
						<dl>
							<dt>欺诈识别途径</dt>
							<dd>
								 <Field:codeTable cssClass="combox title"  name="claimCaseCheatVO.cheatDistinguishChannel" value="${claimCaseCheatVO.cheatDistinguishChannel}"  disabled="true"
                             		tableName="APP___CLM__DBUSER.T_CHEAT_DISTINGUISH_CHANNEL" nullOption="true" id="cheatDistinguishChannelIdAudit"></Field:codeTable>
                             	  
							</dd>
							<dd><input style="width:150%" maxlength="20" type="text" name="claimCaseCheatVO.otherCheatOption" id="otherCheatOptionIdAudit" disabled="disabled" value="${ claimCaseCheatVO.otherCheatOption}"/></dd>
						</dl>
									<dl style="width: 100%;height: auto">
											<dt>欺诈实施人员</dt> 
											 <dd  style="width: 90%; margin-left: 10%" >
												<span class="spanbox3" style="width: 100%;">
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelOneAudit" name="claimCaseCheatVO.cheatImplementationPersonnel" value="01" disabled="disabled" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("01") >=0}'>checked="checked"</s:if>/>内勤
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelTwoAudit" name="claimCaseCheatVO.cheatImplementationPersonnel" value="02" disabled="disabled" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("02") >=0}'>checked="checked"</s:if>/>代理人
													</span>
													<span>
														<input  type="checkbox"
														id="cheatImplementationPersonnelThreeAudit" name="claimCaseCheatVO.cheatImplementationPersonnel" value="03" disabled="disabled" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("03") >=0}'>checked="checked"</s:if>/>投保人
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelFourAudit" name="claimCaseCheatVO.cheatImplementationPersonnel" value="04" disabled="disabled" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("04") >=0}'>checked="checked"</s:if>/>被保险人
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelFiveAudit" name="claimCaseCheatVO.cheatImplementationPersonnel" value="05" disabled="disabled" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("05") >=0}'>checked="checked"</s:if>/>受益人
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelSixAudit" name="claimCaseCheatVO.cheatImplementationPersonnel" value="06" disabled="disabled" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("06") >=0}'>checked="checked"</s:if>/>中介渠道
													</span>
													<span>
														<input type="checkbox" 
														id="cheatImplementationPersonnelSevenAudit" name="claimCaseCheatVO.cheatImplementationPersonnel" value="07" disabled="disabled" onclick="cheatImplementationPersonnelSeven(this);" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("07") >=0}'>checked="checked"</s:if>/>其他人员
													</span>
													<span>
														<input type="text" style="width:150%"  disabled="disabled"
														id="otherImplementationPersonnelAudit" maxlength="50" name="claimCaseCheatVO.otherImplementationPersonnel" value="${claimCaseCheatVO.otherImplementationPersonnel}">
													</span>
												</span>
											</dd>
										</dl>
							</div>
					
	</div>
					</p>				
			</div>
		</div>
					<input type="hidden" name="claimCaseVO.caseId"
									value="${claimCaseVO.caseId}" id="claimCaseVOcASEID" />
					<input type="hidden" value="${claimCaseVO.caseId}" id="caseId" />
<!-- 					<div class="panelPageFormContent" id="approveConclusionOne">	 -->
<!-- 						<dl style="height: auto"> -->
<!-- 							<dt><font class="point" style="color:red;">* </font>审批意见（包括符号最多700汉字）</dt> -->
<!-- 							<dd> -->
<%-- 								<textarea id="approveRemark" onpropertychange="if(value.length>1000) value=value.substring(0,700)"  rows="3" cols="70" name="claimCaseVO.approveRemark">${claimCaseVO.approveRemark}</textarea> --%>
<!-- 							</dd> -->
<!-- 						</dl> -->
<!-- 					</div> -->
<!-- 					<div class="panelPageFormContent main_tabdiv" id="approveConclusionTwo">	 -->
<!-- 						<dl> -->
<!-- 							<dt>审批结论<font class="point" style="color:red;">*</font></dt> -->
<!-- 							<dd> -->
<%-- 								<Field:codeTable cssClass="combox"  id="approveDecision" --%>
<%-- 									name="claimCaseVO.approveDecision" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_DECISION" --%>
<%-- 									value="${claimCaseVO.approveDecision}" nullOption="true" /> --%>
<!-- 							</dd> -->
<!-- 						</dl> -->
				
<!-- 						<dl> -->
<!-- 							<dt>案件标识<font class="point" style="color:red;">*</font></dt> -->
<!-- 							<dd> -->
<%-- 								<Field:codeTable cssClass="combox"  id="approveCaseFlag" --%>
<%-- 									name="claimCaseVO.caseFlag" tableName="APP___CLM__DBUSER.T_CASE_LEVEL" --%>
<%-- 									value="${claimCaseVO.caseFlag}" nullOption="true" orderBy="code" whereClause="code in(1,2,3)"/> --%>
<!-- 							</dd> -->
<!-- 						</dl> -->
<!-- 						<dl> -->
<!-- 							<dt> -->
<!-- 								超期补偿 -->
<!-- 							</dt> -->
<!-- 							<dd > -->
<!-- 								<input type="checkbox" style="border:0px;background:0px;width:auto;" float: right;" size="5px" name="claimCaseVO.overCompFlag" value="1" <s:if test="claimCaseVO.overCompFlag==1">checked="checked"</s:if>/> -->
<!-- 							</dd> -->
<!-- 						</dl> -->
<!-- 						<dl> -->
<!-- 							<dt>不通过原因<font class="point" style="color:red;">*</font></dt> -->
<!-- 							<dd> -->
<%-- 								<Field:codeTable cssClass="combox"  id="approveRejectReason" --%>
<%-- 									name="claimCaseVO.approveRejectReason" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_REJECT" --%>
<%-- 									value="${claimCaseVO.approveRejectReason}" nullOption="true" /> --%>
<!-- 							</dd> -->
<!-- 						</dl> -->
<!-- 					</div> -->
					<s:if test="claimCaseVO.relatedNo!=''">
<!-- 							<div class="divfclass"> -->
<!-- 								<h1> -->
<!-- 									<img src="clm/images/tubiao.png">追偿款信息 -->
<!-- 								</h1> -->
<!-- 							</div> -->
							<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">追偿款信息
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<p><div class="tabdivclassbr">
										<table class="list" style="width: 100%;">
											<thead>
												<tr>
													<th nowrap>领款人</th>
												
													<th nowrap>应收金额</th>
												
													<th nowrap>收费方式</th>
												
													<th nowrap>费用状态</th>
												
													<th nowrap>收费日期</th>
												</tr>
											</thead>
											<tbody style="margin:0 auto;">
												<s:iterator value="claimPayVOs" var="claimPayVO">
													<tr align="center">
														<td><s:property value="#claimPayVO.claimPayeeVO.payeeName"/></td>
														<td><s:property value="#claimPayVO.payAmount"/></td>
														<td>现金</td>
														<td>未收取</td>
														<td></td>
													</tr>
												</s:iterator>
											</tbody>
										</table>
									</div></p>				
								</div>
							</div>
							
<!-- 									<div class="tabdivclassbr"> -->
<!-- 										<table class="list" style="width: 100%;"> -->
<!-- 											<thead> -->
<!-- 												<tr> -->
<!-- 													<th nowrap>领款人</th> -->
												
<!-- 													<th nowrap>应收金额</th> -->
												
<!-- 													<th nowrap>收费方式</th> -->
												
<!-- 													<th nowrap>费用状态</th> -->
												
<!-- 													<th nowrap>收费日期</th> -->
<!-- 												</tr> -->
<!-- 											</thead> -->
<!-- 											<tbody style="margin:0 auto;"> -->
<%-- 												<s:iterator value="claimPayVOs" var="claimPayVO"> --%>
<!-- 													<tr align="center"> -->
<%-- 														<td><s:property value="#claimPayVO.claimPayeeVO.payeeName"/></td> --%>
<%-- 														<td><s:property value="#claimPayVO.payAmount"/></td> --%>
<!-- 														<td>现金</td> -->
<!-- 														<td>未收取</td> -->
<!-- 														<td></td> -->
<!-- 													</tr> -->
<%-- 												</s:iterator> --%>
<!-- 											</tbody> -->
<!-- 										</table> -->
<!-- 									</div> -->
<!-- 								<div class="divfclass"> -->
<!-- 									<h1> -->
<!-- 										<img src="clm/images/tubiao.png">消息提醒 -->
<!-- 									</h1> -->
<!-- 								</div> -->
								<!-- <div class="main_box">
									<div class="main_heading"><h1><img src="clm/images/tubiao.png">消息提醒
										<b class="maim_lpask"></b></h1></div>
										<div class="main_lptwo">
										<p><div class="tabdivclassbr">
									发送给业务员<br>
									<table class="list" class="table" style="width:100%;">
										<thead>
											<tr align="center">
												<th nowrap>业务员代码</th>
												<th nowrap>业务员姓名</th>
												<th nowrap>短信发送</th>
												<th nowrap>邮件发送</th>
											</tr>
										</thead>
										<tbody>
											<tr>
									     		<td>
													<input type="text" name="" value="00001"/>	
									     		</td>
									       		<td>
													<input type="text" name="" value="张艾迪"/>	
												</td>
												<td>
													<input name="" type="checkbox" checked="checked"/>
													<input name="" type="text" maxlength="11" value="13692737392"/>
												</td>
												<td>
													<input name="" type="checkbox" checked="checked"/>
													<input name="" maxlength="100" type="text" value="<EMAIL>"/> 
												</td>
										     </tr>
											<tr>
									     		<td>
													<input type="text" name="" value="00002"/>	
									     		</td>
									       		<td>
													<input type="text" name="" value="陈桥"/>	
												</td>
												<td>
													<input name="" type="checkbox" checked="checked"/>
													<input name="" type="text" maxlength="11" value="18937474838"/>
												</td>
												<td>
													<input name="" type="checkbox" checked="checked"/>
													<input name="" maxlength="100" type="text" value="<EMAIL>"/> 
												</td>
										     </tr>
										</tbody>
									</table>
									<input name=""  type="checkbox" checked="checked"/>
									<input name="signConfirmationVO.isSendCustomer"  type="hidden"  value=""/>发送给客户
									<table class="list"  style="width:100%;">
										<thead>
											<tr align="center">
												<th nowrap>姓名</th>
												<th nowrap>短信发送</th>
												<th nowrap>邮件发送</th>
											</tr>
										</thead>
										<tbody>
											<tr>
									     		<td>				     			
													<input type="text" maxlength="100" name="" value="47363839" />								
									     		</td>
									       		<td>
													<input name="" type="checkbox" checked="checked"/>
													<input name="" type="text" value="13692737392"/>
												</td>
												<td>			
													<input name="flag"  type="checkbox" checked="checked"/>
													<input name="" value="<EMAIL>"/>							
												</td>
									     	</tr>
										</tbody>
									</table>	
								</div></p>				
									</div>
								</div> -->
					</s:if>
					<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">追加付款信息
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<p><div class="tabdivclassbr">
										<table class="list" style="width: 100%;">
											<thead>
												<tr>
													<th nowrap>领款人</th>
												
													<th nowrap>应付金额</th>
												
													<th nowrap>支付方式</th>
												
													<th nowrap>费用状态</th>
												
													<th nowrap>付费日期</th>
												</tr>
											</thead>
											<tbody style="margin:0 auto;">
												<s:iterator value="claimPayList" var="claimPayVO">
													<tr align="center">
														<td><s:property value="#claimPayVO.claimPayeeVO.payeeName"/></td>
														<td><s:property value="#claimPayVO.payAmount"/></td>
														<td><Field:codeValue value="${claimPayVO.claimPayeeVO.payMode}" tableName="APP___CLM__DBUSER.T_PAY_MODE"/></td>
														<td>待支付</td>
														<td></td>
													</tr>
												</s:iterator>
											</tbody>
										</table>
									</div></p>				
								</div>
							</div>
    </div>
    </form>
    
	<div class="formBarButton main_bottom">
		<ul class="toolBar">
			<li >
				<button type="button" class="but_blue" onclick="preSaveStep(${caseId});">上一步</button>
			</li>
			<li >
				<button type="button" class="but_blue" id="saveAndAffirm" onclick="approveDoSaveFlag();">保存</button>
			</li>
			<li >
			<a id="" class="but_blue main_buta" 
					href="clm/register/findInsuredInfo_CLM_claimAntiMoneyLaunderingAction.action?claimCaseVO.caseId=${caseId}&approveReadOnly=1"
					target="dialog" rel="page2" type="button" width="1100" height="520">被保险人身份基本信息</a>
			</li>
			<li >
				<s:if test= 'claimCaseVO.contractRelieveFlag != "1"'>
				<button type="button" class="but_blue" onclick="contractRelievePrit()" disabled="disabled">合同解除通知书</button>
				</s:if>
				<s:else>
				<button type="button" class="but_blue" onclick="contractRelievePrit()" >合同解除通知书</button>
				</s:else>
			</li>
			<li >
				<s:if test= 'claimCaseVO.decisionNoticeFlag != "1"'>
				<button type="button" class="but_blue" onclick="notNote()" disabled="disabled">理赔决定通知书（拒付）</button>
				</s:if>
				<s:else>
				<button type="button" class="but_blue" onclick="notNote()">理赔决定通知书（拒付）</button>
				</s:else>
			</li>
			 <%-- <s:if test="isOCRShow == 1">
			<li> <a id="" class="but_blue main_buta" 
					href="clm/register/findOCRInfo_CLM_toBenefitInputPageAction.action?caseId=${caseId}"
					target="dialog" rel="page2" type="button" width="1000" height="520">OCR识别信息</a></li>
			</s:if>
			<s:else>
			<li> <a id="" class="but_blue main_buta"  disabled="disabled"
					href="clm/register/findOCRInfo_CLM_toBenefitInputPageAction.action?caseId=${caseId}"
					target="dialog" rel="page2" type="button" width="1000" height="520">OCR识别信息</a></li>
			</s:else> --%>
			<li >
				<a type="button" class="but_blue main_buta" id="saveAndAffirm2" onclick="checkConfirmIfInfoFlag();">审批确认</a>
			</li>
			<li>
				<s:if test="#request.qryQuitFlag == null || #request.qryQuitFlag== ''">
				<button type="button" class="but_gray" onclick="exit()">退出</button>
				</s:if>	
			</li>
		</ul>
	</div>
</div>	
<div id="printShow"></div>
</body>
