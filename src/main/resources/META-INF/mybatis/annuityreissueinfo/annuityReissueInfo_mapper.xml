<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IAnnuityReissueInfoDao">

	<sql id="PA_annuityReissueInfoWhereCondition">
		<if test=" fee_amount_before  != null "><![CDATA[ AND A.FEE_AMOUNT_BEFORE = #{fee_amount_before} ]]></if>
		<if test=" pay_id_before  != null "><![CDATA[ AND A.PAY_ID_BEFORE = #{pay_id_before} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" pay_id_after  != null "><![CDATA[ AND A.PAY_ID_AFTER = #{pay_id_after} ]]></if>
		<if test=" reissue_state != null and reissue_state != ''  "><![CDATA[ AND A.REISSUE_STATE = #{reissue_state} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" fee_amount_after  != null "><![CDATA[ AND A.FEE_AMOUNT_AFTER = #{fee_amount_after} ]]></if>
		<if test=" reissue_date  != null  and  reissue_date  != ''  "><![CDATA[ AND A.REISSUE_DATE = #{reissue_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryAnnuityReissueInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryAnnuityReissueInfoByPayDueDateCondition">
		<if test=" pay_due_date  != null "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
	</sql>	
	<sql id="PA_queryAnnuityReissueInfoByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	
	<sql id="PA_queryAnnuityReissueInfoByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryAnnuityReissueInfoByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addAnnuityReissueInfo"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ANN_REI_INFO__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO(
				FEE_AMOUNT_BEFORE, PAY_ID_BEFORE, PLAN_ID, PAY_ID_AFTER, INSERT_TIME, REISSUE_STATE, PRODUCT_CODE, 
				LIAB_ID, UPDATE_TIME, ITEM_ID, FEE_AMOUNT_AFTER, REISSUE_DATE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, LIST_ID, PAY_DUE_DATE, UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, POLICY_ID ) 
			VALUES (
				#{fee_amount_before, jdbcType=NUMERIC}, #{pay_id_before, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , #{pay_id_after, jdbcType=NUMERIC} , SYSDATE , #{reissue_state, jdbcType=VARCHAR} , #{product_code, jdbcType=VARCHAR} 
				, #{liab_id, jdbcType=NUMERIC} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{fee_amount_after, jdbcType=NUMERIC} , #{reissue_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{pay_due_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteAnnuityReissueInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateAnnuityReissueInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO ]]>
		<set>
		<trim suffixOverrides=",">
		    FEE_AMOUNT_BEFORE = #{fee_amount_before, jdbcType=NUMERIC} ,
		    PAY_ID_BEFORE = #{pay_id_before, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    PAY_ID_AFTER = #{pay_id_after, jdbcType=NUMERIC} ,
			REISSUE_STATE = #{reissue_state, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    LIAB_ID = #{liab_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    FEE_AMOUNT_AFTER = #{fee_amount_after, jdbcType=NUMERIC} ,
		    REISSUE_DATE = #{reissue_date, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findAnnuityReissueInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryAnnuityReissueInfoByListIdCondition" />
	</select>
	
	<select id="PA_findAnnuityReissueInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_annuityReissueInfoWhereCondition" />
	</select>
	
	<select id="PA_findAnnuityReissueInfoByPayDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryAnnuityReissueInfoByPayDueDateCondition" />
	</select>
	
	<select id="PA_findAnnuityReissueInfoByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryAnnuityReissueInfoByPlanIdCondition" />
	</select>
	
	<select id="PA_findAnnuityReissueInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryAnnuityReissueInfoByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findAnnuityReissueInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryAnnuityReissueInfoByPolicyIdCondition" />
	</select>
	

	<!-- 按map查询操作 -->
	<select id="PA_findAllMapAnnuityReissueInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_annuityReissueInfoWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAllAnnuityReissueInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, 
			A.LIAB_ID, A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_annuityReissueInfoWhereCondition" />
	</select>

		<!-- 查询派发任务表待重新发放的年金责任数据(批处理的query方法)  -->
	<select id="PA_findAnnuityReissueInfoPOList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT T.*, ROWNUM
				  FROM (
					SELECT A.FEE_AMOUNT_BEFORE, A.PAY_ID_BEFORE, A.PLAN_ID, A.PAY_ID_AFTER, A.REISSUE_STATE, A.PRODUCT_CODE, A.LIAB_ID, 
				            A.ITEM_ID, A.FEE_AMOUNT_AFTER, A.REISSUE_DATE, A.POLICY_CODE, A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID 
				          FROM APP___PAS__DBUSER.T_ANNUITY_REISSUE_INFO A WHERE 1 = 1 
				          AND A.REISSUE_STATE = '0'
				          AND NOT EXISTS
				                 (SELECT 'X'
				          FROM APP___PAS__DBUSER.T_LOCK_POLICY L
		                         LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
		                           ON L.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
		                        WHERE B.SUB_ID IN ('068', '067')
		                       AND (B.LOCK_SERVICE_TYPE = 1 OR (B.LOCK_SERVICE_ID IN ('87','86') AND B.LOCK_SERVICE_TYPE = 2))
		                       AND L.POLICY_CODE = A.POLICY_CODE)
			  ]]>
		<if test=" batch_date != null and batch_date !=null "><![CDATA[ AND A.PAY_DUE_DATE  <= #{batch_date,jdbcType=DATE} ]]></if>		
		<include refid="PA_annuityReissueInfoWhereCondition" />
		
		<![CDATA[ ) T WHERE 1 = 1 AND MOD(T.POLICY_ID , #{modnum}) = #{start} ]]>
		
	</select>

</mapper>
