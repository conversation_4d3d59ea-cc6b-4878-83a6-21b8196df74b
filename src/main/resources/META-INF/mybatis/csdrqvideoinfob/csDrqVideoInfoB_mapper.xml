<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsDrqVideoInfoBDao">

	<sql id="CUS_csDrqVideoInfoBWhereCondition">
		<if test=" video_name != null and video_name != ''  "><![CDATA[ AND A.VIDEO_NAME = #{video_name} ]]></if>
		<if test=" video_length  != null "><![CDATA[ AND A.VIDEO_LENGTH = #{video_length} ]]></if>
		<if test=" bantch_no != null and bantch_no != ''  "><![CDATA[ AND A.BANTCH_NO = #{bantch_no} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" input_time  != null  and  input_time  != ''  "><![CDATA[ AND A.INPUT_TIME = #{input_time} ]]></if>
		<if test=" video_size  != null "><![CDATA[ AND A.VIDEO_SIZE = #{video_size} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" up_time  != null  and  up_time  != ''  "><![CDATA[ AND A.UP_TIME = #{up_time} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsDrqVideoInfoBByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsDrqVideoInfoB"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_DRQ_VIDEO_B__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_DRQ_VIDEO_INFO_B(
				VIDEO_NAME, VIDEO_LENGTH, INSERT_TIME, BANTCH_NO, END_DATE, UPDATE_TIME, ACCEPT_CODE, 
				BUSI_PROD_CODE, INSERT_TIMESTAMP, START_DATE, POLICY_CODE, INPUT_TIME, UPDATE_BY, VIDEO_SIZE, 
				LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, UP_TIME ) 
			VALUES (
				#{video_name, jdbcType=VARCHAR}, #{video_length, jdbcType=NUMERIC} , SYSDATE , #{bantch_no, jdbcType=VARCHAR} , #{end_date, jdbcType=TIMESTAMP} , SYSDATE , #{accept_code, jdbcType=VARCHAR} 
				, #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{start_date, jdbcType=TIMESTAMP} , #{policy_code, jdbcType=VARCHAR} , #{input_time, jdbcType=TIMESTAMP} , #{update_by, jdbcType=NUMERIC} , #{video_size, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{up_time, jdbcType=TIMESTAMP} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsDrqVideoInfoB" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CS_DRQ_VIDEO_INFO_B WHERE  = #{LIST_ID} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsDrqVideoInfoB" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_DRQ_VIDEO_INFO_B ]]>
		<set>
		<trim suffixOverrides=",">
			VIDEO_NAME = #{video_name, jdbcType=VARCHAR} ,
		    VIDEO_LENGTH = #{video_length, jdbcType=NUMERIC} ,
			BANTCH_NO = #{bantch_no, jdbcType=VARCHAR} ,
		    END_DATE = #{end_date, jdbcType=TIMESTAMP} ,
			UPDATE_TIME = SYSDATE , 
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    START_DATE = #{start_date, jdbcType=TIMESTAMP} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    INPUT_TIME = #{input_time, jdbcType=TIMESTAMP} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    VIDEO_SIZE = #{video_size, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    UP_TIME = #{up_time, jdbcType=TIMESTAMP} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsDrqVideoInfoBByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VIDEO_NAME, A.VIDEO_LENGTH, A.BANTCH_NO, A.END_DATE, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.START_DATE, A.POLICY_CODE, A.INPUT_TIME, A.VIDEO_SIZE, 
			A.LIST_ID, A.UP_TIME FROM APP___PAS__DBUSER.T_CS_DRQ_VIDEO_INFO_B A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDrqVideoInfoBByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsDrqVideoInfoB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VIDEO_NAME, A.VIDEO_LENGTH, A.BANTCH_NO, A.END_DATE, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.START_DATE, A.POLICY_CODE, A.INPUT_TIME, A.VIDEO_SIZE, 
			A.LIST_ID, A.UP_TIME FROM APP___PAS__DBUSER.T_CS_DRQ_VIDEO_INFO_B A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsDrqVideoInfoB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VIDEO_NAME, A.VIDEO_LENGTH, A.BANTCH_NO, A.END_DATE, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.START_DATE, A.POLICY_CODE, A.INPUT_TIME, A.VIDEO_SIZE, 
			A.LIST_ID, A.UP_TIME FROM APP___PAS__DBUSER.T_CS_DRQ_VIDEO_INFO_B A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csDrqVideoInfoBWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsDrqVideoInfoBTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_DRQ_VIDEO_INFO_B A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsDrqVideoInfoBForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.VIDEO_NAME, B.VIDEO_LENGTH, B.BANTCH_NO, B.END_DATE, B.ACCEPT_CODE, 
			B.BUSI_PROD_CODE, B.START_DATE, B.POLICY_CODE, B.INPUT_TIME, B.VIDEO_SIZE, 
			B.LIST_ID, B.UP_TIME FROM (
					SELECT ROWNUM RN, A.VIDEO_NAME, A.VIDEO_LENGTH, A.BANTCH_NO, A.END_DATE, A.ACCEPT_CODE, 
			A.BUSI_PROD_CODE, A.START_DATE, A.POLICY_CODE, A.INPUT_TIME, A.VIDEO_SIZE, 
			A.LIST_ID, A.UP_TIME FROM APP___PAS__DBUSER.T_CS_DRQ_VIDEO_INFO_B A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
