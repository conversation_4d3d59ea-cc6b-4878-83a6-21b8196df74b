<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsDifOrgCfgDao">
<sql id="csDifOrgCfgWhereCondition">
	<if test=" is_deleted  != null "><![CDATA[ AND A.IS_DELETED = #{is_deleted} ]]></if>
	<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	<if test=" task_code != null and task_code != ''  "><![CDATA[ AND A.TASK_CODE = #{task_code} ]]></if>
	<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsDifOrgCfgByCfgIdCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsDifOrgCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="cfg_id">
			SELECT APP___PAS__DBUSER.S_CS_DIF_ORG_CFG__ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CS_DIF_ORG_CFG(
				IS_DELETED, INSERT_TIMESTAMP, ORGAN_CODE, TASK_CODE, UPDATE_BY, CFG_ID, INSERT_TIME, 
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				#{is_deleted, jdbcType=NUMERIC}, CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{task_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{cfg_id, jdbcType=NUMERIC} , SYSDATE 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsDifOrgCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CS_DIF_ORG_CFG A WHERE  A.CFG_ID = #{cfg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsDifOrgCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CS_DIF_ORG_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    IS_DELETED = #{is_deleted, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			TASK_CODE = #{task_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE  CFG_ID = #{cfg_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsDifOrgCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_DELETED, A.ORGAN_CODE, A.TASK_CODE, A.CFG_ID FROM DEV_PAS.T_CS_DIF_ORG_CFG A WHERE 1 = 1  ]]>
		<include refid="csDifOrgCfgWhereCondition" />
	</select>
	

<!-- 查询所有操作 -->
	<select id="findAllCsDifOrgCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_DELETED, A.ORGAN_CODE, A.TASK_CODE, A.CFG_ID FROM DEV_PAS.T_CS_DIF_ORG_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="csDifOrgCfgWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCsDifOrgCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_DIF_ORG_CFG A WHERE 1 = 1  ]]>
		<include refid="csDifOrgCfgWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsDifOrgCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.IS_DELETED, B.ORGAN_CODE, B.TASK_CODE, B.CFG_ID,B.TASK_NAME FROM (
					SELECT ROWNUM RN, A.IS_DELETED, A.ORGAN_CODE, A.TASK_CODE, A.CFG_ID,T.TASK_NAME FROM DEV_PAS.T_CS_DIF_ORG_CFG A 
					LEFT JOIN DEV_PAS.T_CS_VP_OPTION_LIST T ON A.TASK_CODE = T.TASK_CODE WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="csDifOrgCfgWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
