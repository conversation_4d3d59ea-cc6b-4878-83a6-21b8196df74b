<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICusSurvivalConfirmDao">
	<sql id="cusSurvivalConfirmWhereCondition">
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" confirm_channel  != null "><![CDATA[ AND A.CONFIRM_CHANNEL = #{confirm_channel} ]]></if>
		<if test=" photo_conpare_id  != null "><![CDATA[ AND A.PHOTO_CONPARE_ID = #{photo_conpare_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" survival_confirm_date  != null  and  survival_confirm_date  != ''  "><![CDATA[ AND A.SURVIVAL_CONFIRM_DATE = #{survival_confirm_date} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" survival_remark != null and survival_remark != ''  "><![CDATA[ AND A.SURVIVAL_REMARK = #{survival_remark} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryCusSurvivalConfirmByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryCusSurvivalConfirmByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="queryCusSurvivalConfirmByCustomerNameCondition">
		<if test=" customer_name != null and customer_name != '' "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
	</sql>	
	<sql id="queryCusSurvivalConfirmByCustomerCertTypeCondition">
		<if test=" customer_cert_type != null and customer_cert_type != '' "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
	</sql>	
	<sql id="queryCusSurvivalConfirmByCustomerCertiCodeCondition">
		<if test=" customer_certi_code != null and customer_certi_code != '' "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCusSurvivalConfirm"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CUS_SURVIVAL_CONFIRM.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM(
				CUSTOMER_CERT_TYPE, CUSTOMER_NAME, CONFIRM_CHANNEL, PHOTO_CONPARE_ID, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, 
				SURVIVAL_CONFIRM_DATE, CUSTOMER_CERTI_CODE, SURVIVAL_REMARK, INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, 
				INSERT_BY ) 
			VALUES (
				#{customer_cert_type, jdbcType=VARCHAR}, #{customer_name, jdbcType=VARCHAR} , #{confirm_channel, jdbcType=NUMERIC} , #{photo_conpare_id, jdbcType=NUMERIC} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE 
				, #{survival_confirm_date, jdbcType=DATE} , #{customer_certi_code, jdbcType=VARCHAR} , #{survival_remark, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCusSurvivalConfirm" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM WHERE  list_id = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCusSurvivalConfirm" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    CONFIRM_CHANNEL = #{confirm_channel, jdbcType=NUMERIC} ,
		    PHOTO_CONPARE_ID = #{photo_conpare_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    SURVIVAL_CONFIRM_DATE = #{survival_confirm_date, jdbcType=DATE} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
			SURVIVAL_REMARK = #{survival_remark, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  list_id = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCusSurvivalConfirmByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE 1 = 1  ]]>
		<include refid="queryCusSurvivalConfirmByListIdCondition" />
	</select>
	
	<select id="findCusSurvivalConfirmByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE 1 = 1  ]]>
		<include refid="queryCusSurvivalConfirmByCustomerIdCondition" />
	</select>
	
	<select id="findCusSurvivalConfirmByCustomerName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE 1 = 1  ]]>
		<include refid="queryCusSurvivalConfirmByCustomerNameCondition" />
	</select>
	
	<select id="findCusSurvivalConfirmByCustomerCertType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE 1 = 1  ]]>
		<include refid="queryCusSurvivalConfirmByCustomerCertTypeCondition" />
	</select>
	
	<select id="findCusSurvivalConfirmByCustomerCertiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE 1 = 1  ]]>
		<include refid="queryCusSurvivalConfirmByCustomerCertiCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCusSurvivalConfirm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE ROWNUM <=  1000  ]]>
		<include refid="cusSurvivalConfirmWhereCondition" /> 
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCusSurvivalConfirm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE ROWNUM <=  1000  ]]>
	    <include refid="cusSurvivalConfirmWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCusSurvivalConfirmTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CUS_SURVIVAL_CONFIRM A WHERE 1 = 1  ]]>
		<include refid="cusSurvivalConfirmWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="queryCusSurvivalConfirmForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_CERT_TYPE, B.CUSTOMER_NAME, B.CONFIRM_CHANNEL, B.PHOTO_CONPARE_ID, B.CUSTOMER_ID, 
			B.SURVIVAL_CONFIRM_DATE, B.CUSTOMER_CERTI_CODE, B.SURVIVAL_REMARK, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.CONFIRM_CHANNEL, A.PHOTO_CONPARE_ID, A.CUSTOMER_ID, 
			A.SURVIVAL_CONFIRM_DATE, A.CUSTOMER_CERTI_CODE, A.SURVIVAL_REMARK, A.LIST_ID FROM APP___PAS__DBUSER.T_CUS_SURVIVAL_CONFIRM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="cusSurvivalConfirmWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
