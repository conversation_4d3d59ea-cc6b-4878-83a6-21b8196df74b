<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsPayCompanyBeneDao">

	<sql id="CUS_csPayCompanyBeneWhereCondition">
		<if test=" bene_certi_code != null and bene_certi_code != ''  "><![CDATA[ AND A.BENE_CERTI_CODE = #{bene_certi_code} ]]></if>
		<if test=" bene_certi_start_date  != null  and  bene_certi_start_date  != ''  "><![CDATA[ AND A.BENE_CERTI_START_DATE = #{bene_certi_start_date} ]]></if>
		<if test=" pay_company_id  != null "><![CDATA[ AND <PERSON>.PAY_COMPANY_ID = #{pay_company_id} ]]></if>
		<if test=" bene_name != null and bene_name != ''  "><![CDATA[ AND A.BENE_NAME = #{bene_name} ]]></if>
		<if test=" bene_certi_end_date  != null  and  bene_certi_end_date  != ''  "><![CDATA[ AND A.BENE_CERTI_END_DATE = #{bene_certi_end_date} ]]></if>
		<if test=" bene_certi_type != null and bene_certi_type != ''  "><![CDATA[ AND A.BENE_CERTI_TYPE = #{bene_certi_type} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsPayCompanyBeneByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsPayCompanyBeneByPayCompanyIdCondition">
		<if test=" pay_company_id  != null "><![CDATA[ AND A.PAY_COMPANY_ID = #{pay_company_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsPayCompanyBene"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_PAYCOM_BE__ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE(
				BENE_CERTI_CODE, BENE_CERTI_START_DATE, PAY_COMPANY_ID, INSERT_TIME, UPDATE_TIME, BENE_NAME, BENE_CERTI_END_DATE, 
				BENE_CERTI_TYPE, INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{bene_certi_code, jdbcType=VARCHAR}, #{bene_certi_start_date, jdbcType=DATE} , #{pay_company_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{bene_name, jdbcType=VARCHAR} , #{bene_certi_end_date, jdbcType=DATE} 
				, #{bene_certi_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
<!-- 	<delete id="CUS_deleteCsPayCompanyBene" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE WHERE  = 1 = 1 ]]>
	</delete>
 -->
<!-- 修改操作 -->
	<update id="updateCsPayCompanyBene" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE ]]>
		<set>
		<trim suffixOverrides=",">
			BENE_CERTI_CODE = #{bene_certi_code, jdbcType=VARCHAR} ,
		    BENE_CERTI_START_DATE = #{bene_certi_start_date, jdbcType=DATE} ,
		    PAY_COMPANY_ID = #{pay_company_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			BENE_NAME = #{bene_name, jdbcType=VARCHAR} ,
		    BENE_CERTI_END_DATE = #{bene_certi_end_date, jdbcType=DATE} ,
			BENE_CERTI_TYPE = #{bene_certi_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  = 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsPayCompanyBeneByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.PAY_COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.LIST_ID FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsPayCompanyBeneByListIdCondition" />
	</select>
	
	<select id="findCsPayCompanyBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.PAY_COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.LIST_ID FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsPayCompanyBeneByPayCompanyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsPayCompanyBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.PAY_COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.LIST_ID FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csPayCompanyBeneWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsPayCompanyBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.PAY_COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.LIST_ID FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csPayCompanyBeneWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCsPayCompanyBeneTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE A WHERE 1 = 1  ]]>
		<include refid="CUS_csPayCompanyBeneWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsPayCompanyBeneForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BENE_CERTI_CODE, B.BENE_CERTI_START_DATE, B.PAY_COMPANY_ID, B.BENE_NAME, B.BENE_CERTI_END_DATE, 
			B.BENE_CERTI_TYPE, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.PAY_COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.LIST_ID FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY_BENE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
