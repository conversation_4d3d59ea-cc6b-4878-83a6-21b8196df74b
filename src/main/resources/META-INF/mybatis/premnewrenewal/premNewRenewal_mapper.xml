<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPremNewRenewalDao">
	<sql id="PA_premNewRenewalWhereCondition">
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" funds_rtn_code != null and funds_rtn_code != ''  "><![CDATA[ AND A.FUNDS_RTN_CODE = #{funds_rtn_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" holder_tel != null and holder_tel != ''  "><![CDATA[ AND A.HOLDER_TEL = #{holder_tel} ]]></if>
		<if test=" handler_name != null and handler_name != ''  "><![CDATA[ AND A.HANDLER_NAME = #{handler_name} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" service_bank_branch_name != null and service_bank_branch_name != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH_NAME = #{service_bank_branch_name} ]]></if>
		<if test=" insured_relation_to_ph != null and insured_relation_to_ph != ''  "><![CDATA[ AND A.INSURED_RELATION_TO_PH = #{insured_relation_to_ph} ]]></if>
		<if test=" insured_birthday  != null  and  insured_birthday  != ''  "><![CDATA[ AND A.INSURED_BIRTHDAY = #{insured_birthday} ]]></if>
		<if test=" holder_certi_code != null and holder_certi_code != ''  "><![CDATA[ AND A.HOLDER_CERTI_CODE = #{holder_certi_code} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" due_fee_type != null and due_fee_type != ''  "><![CDATA[ AND A.DUE_FEE_TYPE = #{due_fee_type} ]]></if>
		<if test=" channel_org_code != null and channel_org_code != ''  "><![CDATA[ AND A.CHANNEL_ORG_CODE = #{channel_org_code} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" special_account_flag != null and special_account_flag != ''  "><![CDATA[ AND A.SPECIAL_ACCOUNT_FLAG = #{special_account_flag} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" handler_pay_mode != null and handler_pay_mode != ''  "><![CDATA[ AND A.HANDLER_PAY_MODE = #{handler_pay_mode} ]]></if>
		<if test=" service_bank_branch_code != null and service_bank_branch_code != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH_CODE = #{service_bank_branch_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" is_agent_lock  != null "><![CDATA[ AND A.IS_AGENT_LOCK = #{is_agent_lock} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" pay_location != null and pay_location != ''  "><![CDATA[ AND A.PAY_LOCATION = #{pay_location} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" insured_tel != null and insured_tel != ''  "><![CDATA[ AND A.INSURED_TEL = #{insured_tel} ]]></if>
		<if test=" policy_status  != null "><![CDATA[ AND A.POLICY_STATUS = #{policy_status} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" agent_start_date  != null  and  agent_start_date  != ''  "><![CDATA[ AND A.AGENT_START_DATE = #{agent_start_date} ]]></if>
		<if test=" last_agent_name != null and last_agent_name != ''  "><![CDATA[ AND A.LAST_AGENT_NAME = #{last_agent_name} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" last_agent_code != null and last_agent_code != ''  "><![CDATA[ AND A.LAST_AGENT_CODE = #{last_agent_code} ]]></if>
		<if test=" holder_gender  != null "><![CDATA[ AND A.HOLDER_GENDER = #{holder_gender} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" agent_relation_to_ph != null and agent_relation_to_ph != ''  "><![CDATA[ AND A.AGENT_RELATION_TO_PH = #{agent_relation_to_ph} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" agent_organ_code  != null "><![CDATA[ AND A.AGENT_ORGAN_CODE = #{agent_organ_code} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" waiver_flag  != null "><![CDATA[ AND A.WAIVER_FLAG = #{waiver_flag} ]]></if>
		<if test=" holder_id_code != null and holder_id_code != ''  "><![CDATA[ AND A.HOLDER_ID_CODE = #{holder_id_code} ]]></if>
		<if test=" fail_times  != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
		<if test=" holder_company_name != null and holder_company_name != ''  "><![CDATA[ AND A.HOLDER_COMPANY_NAME = #{holder_company_name} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" holder_cert_type != null and holder_cert_type != ''  "><![CDATA[ AND A.HOLDER_CERT_TYPE = #{holder_cert_type} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" handler_organ_code  != null "><![CDATA[ AND A.HANDLER_ORGAN_CODE = #{handler_organ_code} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" fee_scene_code != null and fee_scene_code != ''  "><![CDATA[ AND A.FEE_SCENE_CODE = #{fee_scene_code} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
	</sql>



<!-- 添加操作 -->
	<insert id="PA_addPremNewRenewal"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
				SELECT APP___PAS__DBUSER.S_PREM_NEW_RENEWAL.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PREM_NEW_RENEWAL(
				HOLDER_NAME, BUSI_PROD_NAME, UNIT_NUMBER, PAID_COUNT, FUNDS_RTN_CODE, BUSI_PROD_CODE, ORGAN_CODE, 
				HOLDER_TEL, UPDATE_BY, HANDLER_NAME, CHARGE_YEAR, IS_RISK_MAIN, SERVICE_BANK_BRANCH_NAME, INSURED_RELATION_TO_PH, 
				INSURED_BIRTHDAY, HOLDER_CERTI_CODE, AGENT_NAME, DUE_FEE_TYPE, UPDATE_TIME, CHANNEL_ORG_CODE, INSURED_NAME, 
				AMOUNT, SPECIAL_ACCOUNT_FLAG, INSURED_ID, HANDLER_PAY_MODE, SERVICE_BANK_BRANCH_CODE, POLICY_CODE, IS_AGENT_LOCK, 
				PAY_MODE, UPDATE_TIMESTAMP, INSERT_BY, PAY_LOCATION, BUSINESS_CODE, INSURED_TEL, POLICY_STATUS, 
				BANK_ACCOUNT, AGENT_START_DATE, LAST_AGENT_NAME, PRODUCT_CODE, FINISH_TIME, ITEM_ID, LAST_AGENT_CODE, 
				HOLDER_GENDER, INSERT_TIMESTAMP, DUE_TIME, AGENT_RELATION_TO_PH, FEE_AMOUNT, LIST_ID, AGENT_ORGAN_CODE, 
				SERVICE_CODE, HOLDER_ID, WAIVER_FLAG, HOLDER_ID_CODE, FAIL_TIMES, HOLDER_COMPANY_NAME, INSERT_TIME, 
				POLICY_YEAR, BANK_USER_NAME, FEE_STATUS, HOLDER_CERT_TYPE, CHARGE_PERIOD, HANDLER_ORGAN_CODE, ARAP_FLAG, 
				FEE_SCENE_CODE, BANK_CODE, AGENT_CODE, PREM_FREQ ) 
			VALUES (
				#{holder_name, jdbcType=VARCHAR}, #{busi_prod_name, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{paid_count, jdbcType=NUMERIC} , #{funds_rtn_code, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} 
				, #{holder_tel, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{handler_name, jdbcType=VARCHAR} , #{charge_year, jdbcType=NUMERIC} , #{is_risk_main, jdbcType=NUMERIC} , #{service_bank_branch_name, jdbcType=VARCHAR} , #{insured_relation_to_ph, jdbcType=VARCHAR} 
				, #{insured_birthday, jdbcType=DATE} , #{holder_certi_code, jdbcType=VARCHAR} , #{agent_name, jdbcType=VARCHAR} , #{due_fee_type, jdbcType=VARCHAR} , SYSDATE , #{channel_org_code, jdbcType=VARCHAR} , #{insured_name, jdbcType=VARCHAR} 
				, #{amount, jdbcType=NUMERIC} , #{special_account_flag, jdbcType=VARCHAR} , #{insured_id, jdbcType=NUMERIC} , #{handler_pay_mode, jdbcType=VARCHAR} , #{service_bank_branch_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{is_agent_lock, jdbcType=NUMERIC} 
				, #{pay_mode, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{pay_location, jdbcType=VARCHAR} , #{business_code, jdbcType=VARCHAR} , #{insured_tel, jdbcType=VARCHAR} , #{policy_status, jdbcType=NUMERIC} 
				, #{bank_account, jdbcType=VARCHAR} , #{agent_start_date, jdbcType=DATE} , #{last_agent_name, jdbcType=VARCHAR} , #{product_code, jdbcType=VARCHAR} , #{finish_time, jdbcType=DATE} , #{item_id, jdbcType=NUMERIC} , #{last_agent_code, jdbcType=VARCHAR} 
				, #{holder_gender, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{due_time, jdbcType=DATE} , #{agent_relation_to_ph, jdbcType=VARCHAR} , #{fee_amount, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{agent_organ_code, jdbcType=NUMERIC} 
				, #{service_code, jdbcType=VARCHAR} , #{holder_id, jdbcType=NUMERIC} , #{waiver_flag, jdbcType=NUMERIC} , #{holder_id_code, jdbcType=VARCHAR} , #{fail_times, jdbcType=NUMERIC} , #{holder_company_name, jdbcType=VARCHAR} , SYSDATE 
				, #{policy_year, jdbcType=NUMERIC} , #{bank_user_name, jdbcType=VARCHAR} , #{fee_status, jdbcType=VARCHAR} , #{holder_cert_type, jdbcType=VARCHAR} , #{charge_period, jdbcType=VARCHAR} , #{handler_organ_code, jdbcType=NUMERIC} , #{arap_flag, jdbcType=VARCHAR} 
				, #{fee_scene_code, jdbcType=VARCHAR} , #{bank_code, jdbcType=VARCHAR} , #{agent_code, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePremNewRenewal" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PREM_NEW_RENEWAL WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePremNewRenewal" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_NEW_RENEWAL ]]>
		<set>
		<trim suffixOverrides=",">
			HOLDER_NAME = #{holder_name, jdbcType=VARCHAR} ,
			BUSI_PROD_NAME = #{busi_prod_name, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
		    PAID_COUNT = #{paid_count, jdbcType=NUMERIC} ,
			FUNDS_RTN_CODE = #{funds_rtn_code, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			HOLDER_TEL = #{holder_tel, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			HANDLER_NAME = #{handler_name, jdbcType=VARCHAR} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    IS_RISK_MAIN = #{is_risk_main, jdbcType=NUMERIC} ,
			SERVICE_BANK_BRANCH_NAME = #{service_bank_branch_name, jdbcType=VARCHAR} ,
			INSURED_RELATION_TO_PH = #{insured_relation_to_ph, jdbcType=VARCHAR} ,
		    INSURED_BIRTHDAY = #{insured_birthday, jdbcType=DATE} ,
			HOLDER_CERTI_CODE = #{holder_certi_code, jdbcType=VARCHAR} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
			DUE_FEE_TYPE = #{due_fee_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			CHANNEL_ORG_CODE = #{channel_org_code, jdbcType=VARCHAR} ,
			INSURED_NAME = #{insured_name, jdbcType=VARCHAR} ,
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
			SPECIAL_ACCOUNT_FLAG = #{special_account_flag, jdbcType=VARCHAR} ,
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
			HANDLER_PAY_MODE = #{handler_pay_mode, jdbcType=VARCHAR} ,
			SERVICE_BANK_BRANCH_CODE = #{service_bank_branch_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    IS_AGENT_LOCK = #{is_agent_lock, jdbcType=NUMERIC} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			PAY_LOCATION = #{pay_location, jdbcType=VARCHAR} ,
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
			INSURED_TEL = #{insured_tel, jdbcType=VARCHAR} ,
		    POLICY_STATUS = #{policy_status, jdbcType=NUMERIC} ,
			BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
		    AGENT_START_DATE = #{agent_start_date, jdbcType=DATE} ,
			LAST_AGENT_NAME = #{last_agent_name, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			LAST_AGENT_CODE = #{last_agent_code, jdbcType=VARCHAR} ,
		    HOLDER_GENDER = #{holder_gender, jdbcType=NUMERIC} ,
		    DUE_TIME = #{due_time, jdbcType=DATE} ,
			AGENT_RELATION_TO_PH = #{agent_relation_to_ph, jdbcType=VARCHAR} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    AGENT_ORGAN_CODE = #{agent_organ_code, jdbcType=NUMERIC} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		    HOLDER_ID = #{holder_id, jdbcType=NUMERIC} ,
		    WAIVER_FLAG = #{waiver_flag, jdbcType=NUMERIC} ,
			HOLDER_ID_CODE = #{holder_id_code, jdbcType=VARCHAR} ,
		    FAIL_TIMES = #{fail_times, jdbcType=NUMERIC} ,
			HOLDER_COMPANY_NAME = #{holder_company_name, jdbcType=VARCHAR} ,
		    POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
			BANK_USER_NAME = #{bank_user_name, jdbcType=VARCHAR} ,
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
			HOLDER_CERT_TYPE = #{holder_cert_type, jdbcType=VARCHAR} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
		    HANDLER_ORGAN_CODE = #{handler_organ_code, jdbcType=NUMERIC} ,
			ARAP_FLAG = #{arap_flag, jdbcType=VARCHAR} ,
			FEE_SCENE_CODE = #{fee_scene_code, jdbcType=VARCHAR} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		    PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	
	<update id="PA_updatePremNewRenewalUpdateFeeStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_NEW_RENEWAL ]]>
		<set>
		<trim suffixOverrides=",">
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
			UPDATE_TIMESTAMP = SYSDATE ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id}]]>
	</update>
	
	
<!-- 按map查询操作 -->
	<select id="PA_findAllMapPremNewRenewal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.BUSI_PROD_CODE, A.ORGAN_CODE, 
			A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, A.INSURED_RELATION_TO_PH, 
			A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.DUE_FEE_TYPE, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
			A.AMOUNT, A.SPECIAL_ACCOUNT_FLAG, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, 
			A.PAY_MODE, A.PAY_LOCATION, A.BUSINESS_CODE, A.INSURED_TEL, A.POLICY_STATUS, 
			A.BANK_ACCOUNT, A.AGENT_START_DATE, A.LAST_AGENT_NAME, A.PRODUCT_CODE, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, 
			A.HOLDER_GENDER, A.DUE_TIME, A.AGENT_RELATION_TO_PH, A.FEE_AMOUNT, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, A.FAIL_TIMES, A.HOLDER_COMPANY_NAME, 
			A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE, A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.ARAP_FLAG, 
			A.FEE_SCENE_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ FROM APP___PAS__DBUSER.T_PREM_NEW_RENEWAL A WHERE ROWNUM <=  1000  ]]>
		 <include refid="PA_premNewRenewalWhereCondition" /> 
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPremNewRenewal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.BUSI_PROD_CODE, A.ORGAN_CODE, 
			A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, A.INSURED_RELATION_TO_PH, 
			A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.DUE_FEE_TYPE, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
			A.AMOUNT, A.SPECIAL_ACCOUNT_FLAG, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, 
			A.PAY_MODE, A.PAY_LOCATION, A.BUSINESS_CODE, A.INSURED_TEL, A.POLICY_STATUS, 
			A.BANK_ACCOUNT, A.AGENT_START_DATE, A.LAST_AGENT_NAME, A.PRODUCT_CODE, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, 
			A.HOLDER_GENDER, A.DUE_TIME, A.AGENT_RELATION_TO_PH, A.FEE_AMOUNT, A.LIST_ID, A.AGENT_ORGAN_CODE, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, A.FAIL_TIMES, A.HOLDER_COMPANY_NAME, 
			A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE, A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.ARAP_FLAG, 
			A.FEE_SCENE_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ FROM APP___PAS__DBUSER.T_PREM_NEW_RENEWAL A WHERE ROWNUM <=  1000  ]]>
		 <include refid="PA_premNewRenewalWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPremNewRenewalInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.HOLDER_NAME, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.BUSI_PROD_CODE, A.ORGAN_CODE, 
      A.HOLDER_TEL, A.HANDLER_NAME, A.CHARGE_YEAR, A.IS_RISK_MAIN, A.SERVICE_BANK_BRANCH_NAME, A.INSURED_RELATION_TO_PH, 
      A.INSURED_BIRTHDAY, A.HOLDER_CERTI_CODE, A.AGENT_NAME, A.DUE_FEE_TYPE, A.CHANNEL_ORG_CODE, A.INSURED_NAME, 
      A.AMOUNT, A.SPECIAL_ACCOUNT_FLAG, A.INSURED_ID, A.HANDLER_PAY_MODE, A.SERVICE_BANK_BRANCH_CODE, A.POLICY_CODE, A.IS_AGENT_LOCK, 
      A.PAY_MODE, A.PAY_LOCATION, A.BUSINESS_CODE, A.INSURED_TEL, A.POLICY_STATUS, 
      A.BANK_ACCOUNT, A.AGENT_START_DATE, A.LAST_AGENT_NAME, A.PRODUCT_CODE, A.FINISH_TIME, A.ITEM_ID, A.LAST_AGENT_CODE, 
      A.HOLDER_GENDER, A.DUE_TIME, A.AGENT_RELATION_TO_PH, A.FEE_AMOUNT, A.LIST_ID, A.AGENT_ORGAN_CODE, 
      A.SERVICE_CODE, A.HOLDER_ID, A.WAIVER_FLAG, A.HOLDER_ID_CODE, A.FAIL_TIMES, A.HOLDER_COMPANY_NAME, 
      A.POLICY_YEAR, A.BANK_USER_NAME, A.FEE_STATUS, A.HOLDER_CERT_TYPE, A.CHARGE_PERIOD, A.HANDLER_ORGAN_CODE, A.ARAP_FLAG, 
      A.FEE_SCENE_CODE, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ,B.BUSI_ITEM_ID FROM DEV_PAS.T_PREM_NEW_RENEWAL A,DEV_PAS.T_CONTRACT_PRODUCT B WHERE 1 = 1
      AND A.ITEM_ID = B.ITEM_ID
      AND A.POLICY_CODE = B.POLICY_CODE
      AND ROWNUM <=  1000  ]]>
		 <include refid="PA_premNewRenewalWhereCondition" />
	</select>
	
</mapper>
