<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IAutouwRuleDao">
<!--
	<sql id="CUS_autouwRuleWhereCondition">
		<if test=" old_time  != null "><![CDATA[ AND A.OLD_TIME = #{old_time} ]]></if>
		<if test=" check_result  != null "><![CDATA[ AND A.CHECK_RESULT = #{check_result} ]]></if>
		<if test=" old_message != null and old_message != ''  "><![CDATA[ AND A.OLD_MESSAGE = #{old_message} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" new_message != null and new_message != ''  "><![CDATA[ AND A.NEW_MESSAGE = #{new_message} ]]></if>
		<if test=" new_time  != null "><![CDATA[ AND A.NEW_TIME = #{new_time} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	

<!-- 添加操作 -->
	<insert id="CUS_addAutouwRule"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO dev_pas.T_AUTOUW_RULE(
				OLD_TIME, CHECK_RESULT, OLD_MESSAGE, POLICY_CHG_ID, NEW_MESSAGE, NEW_TIME, ACCEPT_CODE ) 
			VALUES (
				#{old_time, jdbcType=NUMERIC}, #{check_result, jdbcType=NUMERIC} , #{old_message, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{new_message, jdbcType=VARCHAR} , #{new_time, jdbcType=NUMERIC} , #{accept_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteAutouwRule" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM dev_pas.T_AUTOUW_RULE WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateAutouwRule" parameterType="java.util.Map">
		<![CDATA[ UPDATE dev_pas.T_AUTOUW_RULE ]]>
		<set>
		<trim suffixOverrides=",">
		    OLD_TIME = #{old_time, jdbcType=NUMERIC} ,
		    CHECK_RESULT = #{check_result, jdbcType=NUMERIC} ,
			OLD_MESSAGE = #{old_message, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			NEW_MESSAGE = #{new_message, jdbcType=VARCHAR} ,
		    NEW_TIME = #{new_time, jdbcType=NUMERIC} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  policy_chg_id = #{policy_chg_id} ]]>
	</update>

<!-- 按索引查询操作 -->	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapAutouwRule" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OLD_TIME, A.CHECK_RESULT, A.OLD_MESSAGE, A.POLICY_CHG_ID, A.NEW_MESSAGE, A.NEW_TIME, A.ACCEPT_CODE FROM dev_pas.T_AUTOUW_RULE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllAutouwRule" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OLD_TIME, A.CHECK_RESULT, A.OLD_MESSAGE, A.POLICY_CHG_ID, A.NEW_MESSAGE, A.NEW_TIME, A.ACCEPT_CODE FROM dev_pas.T_AUTOUW_RULE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findAutouwRuleTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM dev_pas.T_AUTOUW_RULE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryAutouwRuleForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OLD_TIME, B.CHECK_RESULT, B.OLD_MESSAGE, B.POLICY_CHG_ID, B.NEW_MESSAGE, B.NEW_TIME, B.ACCEPT_CODE FROM (
					SELECT ROWNUM RN, A.OLD_TIME, A.CHECK_RESULT, A.OLD_MESSAGE, A.POLICY_CHG_ID, A.NEW_MESSAGE, A.NEW_TIME, A.ACCEPT_CODE FROM dev_pas.T_AUTOUW_RULE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
