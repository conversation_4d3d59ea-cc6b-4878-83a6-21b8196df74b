<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.impl.CsMobChkTimeCfgDaoImpl">

	<sql id="csMobChkTimeCfgWhereCondition">
		<if test=" cost_time  != null "><![CDATA[ AND A.COST_TIME = #{cost_time} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" cfg_date  != null  and  cfg_date  != ''  "><![CDATA[ AND A.CFG_DATE = #{cfg_date} ]]></if>
		<if test=" is_valid  != null "><![CDATA[ AND A.IS_VALID = #{is_valid} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	

<!-- 添加操作 -->
	<insert id="addCsMobChkTimeCfg"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG(
				INSERT_TIMESTAMP, COST_TIME, UPDATE_BY, CFG_ID, INSERT_TIME, UPDATE_TIMESTAMP, UPDATE_TIME, 
				INSERT_BY, CFG_DATE, IS_VALID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{cost_time, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{cfg_id, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP, SYSDATE 
				, #{insert_by, jdbcType=NUMERIC} , #{cfg_date, jdbcType=DATE} , #{is_valid, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<!-- <delete id="deleteCsMobChkTimeCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG WHERE  = #{} ]]>
	</delete> -->

<!-- 修改操作 -->
	<update id="updateCsMobChkTimeCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    COST_TIME = #{cost_time, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    CFG_DATE = #{cfg_date, jdbcType=DATE} ,
		    IS_VALID = #{is_valid, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  = #{} ]]>
	</update>

<!-- 按索引查询操作 -->	

<!-- 按map查询操作 -->
	<select id="findAllMapCsMobChkTimeCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_TIME, A.CFG_ID, 
			A.CFG_DATE, A.IS_VALID FROM APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="csMobChkTimeCfgWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsMobChkTimeCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_TIME, A.CFG_ID, 
			A.CFG_DATE, A.IS_VALID FROM APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="csMobChkTimeCfgWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCsMobChkTimeCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsMobChkTimeCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.COST_TIME, B.CFG_ID, 
			B.CFG_DATE, B.IS_VALID FROM (
					SELECT ROWNUM RN, A.COST_TIME, A.CFG_ID, 
			A.CFG_DATE, A.IS_VALID FROM APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询单条操作 -->
	<select id="findCsMobChkTimeCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COST_TIME, A.CFG_ID, 
			A.CFG_DATE, A.IS_VALID FROM APP___PAS__DBUSER.T_CS_MOB_CHK_TIME_CFG A WHERE ROWNUM <=  1  ]]>
		<include refid="csMobChkTimeCfgWhereCondition" />
		<![CDATA[
			ORDER BY A.CFG_ID ]]>
	</select>
	
</mapper>
