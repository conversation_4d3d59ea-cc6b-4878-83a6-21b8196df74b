<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.TaxBusitStatusLogDaoImpl">
<!--
	<sql id="PA_taxBusitStatusLogWhereCondition">
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" busi_status_log_id  != null "><![CDATA[ AND A.BUSI_STATUS_LOG_ID = #{busi_status_log_id} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID = #{submit_id} ]]></if>
		<if test=" tax_customer_no != null and tax_customer_no != ''  "><![CDATA[ AND A.TAX_CUSTOMER_NO = #{tax_customer_no} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" product_category != null and product_category != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY = #{product_category} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryTaxBusitStatusLogByBusiStatusLogIdCondition">
		<if test=" busi_status_log_id  != null "><![CDATA[ AND A.BUSI_STATUS_LOG_ID = #{busi_status_log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addTaxBusitStatusLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="busi_status_log_id">
			SELECT APP___PAS__DBUSER.S_TAX_BUSIT_STATUS_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG(
				LAPSE_DATE, INSERT_TIME, BUSI_STATUS_LOG_ID, END_CAUSE, UPDATE_TIME, LAPSE_CAUSE, BUSI_PROD_CODE, 
				EXPIRY_DATE, INSERT_TIMESTAMP, LIABILITY_STATE, SUSPEND_DATE, UPDATE_BY, SUSPEND_CAUSE, SUBMIT_ID, 
				TAX_CUSTOMER_NO, UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, POLICY_ID, PRODUCT_CATEGORY ) 
			VALUES (
				#{lapse_date, jdbcType=DATE}, SYSDATE , #{busi_status_log_id, jdbcType=NUMERIC} , #{end_cause, jdbcType=VARCHAR} , SYSDATE , #{lapse_cause, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{expiry_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{liability_state, jdbcType=NUMERIC} , #{suspend_date, jdbcType=DATE} , 0 , #{suspend_cause, jdbcType=VARCHAR} , #{submit_id, jdbcType=NUMERIC} 
				, #{tax_customer_no, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, 0 , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{product_category, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteTaxBusitStatusLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG WHERE BUSI_STATUS_LOG_ID = #{busi_status_log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateTaxBusitStatusLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    SUBMIT_ID = #{submit_id, jdbcType=NUMERIC} ,
			TAX_CUSTOMER_NO = #{tax_customer_no, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			PRODUCT_CATEGORY = #{product_category, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE BUSI_STATUS_LOG_ID = #{busi_status_log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findTaxBusitStatusLogByBusiStatusLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LAPSE_DATE, A.BUSI_STATUS_LOG_ID, A.END_CAUSE, A.LAPSE_CAUSE, A.BUSI_PROD_CODE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.SUBMIT_ID, 
			A.TAX_CUSTOMER_NO, A.BUSI_ITEM_ID, A.POLICY_ID, A.PRODUCT_CATEGORY FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxBusitStatusLogByBusiStatusLogIdCondition" />
		<![CDATA[ ORDER BY A.BUSI_STATUS_LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapTaxBusitStatusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LAPSE_DATE, A.BUSI_STATUS_LOG_ID, A.END_CAUSE, A.LAPSE_CAUSE, A.BUSI_PROD_CODE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.SUBMIT_ID, 
			A.TAX_CUSTOMER_NO, A.BUSI_ITEM_ID, A.POLICY_ID, A.PRODUCT_CATEGORY FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BUSI_STATUS_LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllTaxBusitStatusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.LAPSE_DATE, A.BUSI_STATUS_LOG_ID, A.END_CAUSE, A.LAPSE_CAUSE, A.BUSI_PROD_CODE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.SUBMIT_ID, 
			A.TAX_CUSTOMER_NO, A.BUSI_ITEM_ID, A.POLICY_ID, A.PRODUCT_CATEGORY FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A WHERE 1=1  ]]>
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID = #{submit_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" tax_customer_no != null and tax_customer_no != ''  "><![CDATA[ AND A.TAX_CUSTOMER_NO = #{tax_customer_no} ]]></if>
		<![CDATA[ ORDER BY A.BUSI_STATUS_LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findTaxBusitStatusLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryTaxBusitStatusLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LAPSE_DATE, B.BUSI_STATUS_LOG_ID, B.END_CAUSE, B.LAPSE_CAUSE, B.BUSI_PROD_CODE, 
			B.EXPIRY_DATE, B.LIABILITY_STATE, B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.SUBMIT_ID, 
			B.TAX_CUSTOMER_NO, B.BUSI_ITEM_ID, B.POLICY_ID, B.PRODUCT_CATEGORY FROM (
					SELECT ROWNUM RN, A.LAPSE_DATE, A.BUSI_STATUS_LOG_ID, A.END_CAUSE, A.LAPSE_CAUSE, A.BUSI_PROD_CODE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.SUBMIT_ID, 
			A.TAX_CUSTOMER_NO, A.BUSI_ITEM_ID, A.POLICY_ID, A.PRODUCT_CATEGORY FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BUSI_STATUS_LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 按map查询操作 -->
	<select id="PA_findTaxBusitStatusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LAPSE_DATE, A.BUSI_STATUS_LOG_ID, A.END_CAUSE, A.LAPSE_CAUSE, A.BUSI_PROD_CODE, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.SUBMIT_ID, 
			A.TAX_CUSTOMER_NO, A.BUSI_ITEM_ID, A.POLICY_ID, A.PRODUCT_CATEGORY FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BUSI_STATUS_LOG_ID ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="PA_findTaxCustomerNO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.TAX_CUSTOMER_NO, A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A WHERE ROWNUM <=  1000  ]]>
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID = #{submit_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	
	<!-- 按索引查询操作 -->	
	<select id="PA_findTaxBusitStatusLogByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.LAPSE_DATE,
       T.BUSI_STATUS_LOG_ID,
       T.END_CAUSE,
       T.LAPSE_CAUSE,
       T.BUSI_PROD_CODE,
       T.EXPIRY_DATE,
       T.LIABILITY_STATE,
       T.SUSPEND_DATE,
       T.SUSPEND_CAUSE,
       T.SUBMIT_ID,
       T.TAX_CUSTOMER_NO,
       T.BUSI_ITEM_ID,
       T.POLICY_ID,
       T.PRODUCT_CATEGORY
  FROM (SELECT 
               A.LAPSE_DATE,
               A.BUSI_STATUS_LOG_ID,
               A.END_CAUSE,
               A.LAPSE_CAUSE,
               A.BUSI_PROD_CODE,
               A.EXPIRY_DATE,
               A.LIABILITY_STATE,
               A.SUSPEND_DATE,
               A.SUSPEND_CAUSE,
               A.SUBMIT_ID,
               A.TAX_CUSTOMER_NO,
               A.BUSI_ITEM_ID,
               A.POLICY_ID,
               A.PRODUCT_CATEGORY
          FROM APP___PAS__DBUSER.T_TAX_BUSIT_STATUS_LOG A
         WHERE 1 = 1]]>
           <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
           <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
         <![CDATA[ ORDER BY A.BUSI_STATUS_LOG_ID DESC) T
 WHERE ROWNUM = 1 ]]>
	</select>
	
</mapper>
