<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.PolicyMarkingInfoDaoImpl">

	<sql id="PAS_policyMarkingInfoWhereCondition">
		<if test=" marking_time  != null  and  marking_time  != ''  "><![CDATA[ AND A.MARKING_TIME = #{marking_time} ]]></if>
		<if test=" effect_notice_info != null and effect_notice_info != ''  "><![CDATA[ AND A.EFFECT_NOTICE_INFO = #{effect_notice_info} ]]></if>
		<if test=" mark_id  != null "><![CDATA[ AND A.MARK_ID = #{mark_id} ]]></if>
		<if test=" user_id  != null "><![CDATA[ AND A.USER_ID = #{user_id} ]]></if>
		<if test=" sleep_type != null and sleep_type != ''  "><![CDATA[ AND A.SLEEP_TYPE = #{sleep_type} ]]></if>
		<if test=" deal_status != null and deal_status != ''  "><![CDATA[ AND A.DEAL_STATUS = #{deal_status} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" deal_scene != null and deal_scene != ''  "><![CDATA[ AND A.DEAL_SCENE = #{deal_scene} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME = #{deal_time} ]]></if>
		<if test=" platform != null and platform != ''  "><![CDATA[ AND A.PLATFORM = #{platform} ]]></if>
		<if test=" is_new_s_policy  != null "><![CDATA[ AND A.IS_NEW_S_POLICY = #{is_new_s_policy} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryPolicyMarkingInfoByMarkIdCondition">
		<if test=" mark_id  != null "><![CDATA[ AND A.MARK_ID = #{mark_id} ]]></if>
	</sql>	
	<sql id="PAS_queryPolicyMarkingInfoByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PAS_queryPolicyMarkingInfoByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addPolicyMarkingInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT APP___PAS__DBUSER.S_POLICY_MARKING_INFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_MARKING_INFO(
				MARKING_TIME, EFFECT_NOTICE_INFO, MARK_ID, USER_ID, SLEEP_TYPE, INSERT_TIME, DEAL_STATUS, 
				UPDATE_TIME, POLICY_TYPE, INSERT_TIMESTAMP, POLICY_CODE, DEAL_TIME, UPDATE_BY, PLATFORM, 
				UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID ) 
			VALUES (
				#{marking_time, jdbcType=TIMESTAMP}, #{effect_notice_info, jdbcType=VARCHAR} , #{mark_id, jdbcType=NUMERIC} , #{user_id, jdbcType=NUMERIC} , #{sleep_type, jdbcType=VARCHAR} , SYSDATE , #{deal_status, jdbcType=VARCHAR} 
				, SYSDATE , #{policy_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{deal_time, jdbcType=TIMESTAMP} , #{update_by, jdbcType=NUMERIC} , #{platform, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deletePolicyMarkingInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO WHERE MARK_ID = #{mark_id, jdbcType=NUMERIC} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updatePolicyMarkingInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_MARKING_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			MARKING_TIME = #{marking_time, jdbcType=TIMESTAMP} ,
			EFFECT_NOTICE_INFO = #{effect_notice_info, jdbcType=VARCHAR} ,
			MARK_ID = #{mark_id, jdbcType=NUMERIC} ,
			USER_ID = #{user_id, jdbcType=NUMERIC} ,
			SLEEP_TYPE = #{sleep_type, jdbcType=VARCHAR} ,
			DEAL_STATUS = #{deal_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
			DEAL_SCENE = #{deal_scene, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			DEAL_TIME = #{deal_time, jdbcType=TIMESTAMP} ,
			UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			PLATFORM = #{platform, jdbcType=VARCHAR} ,
			IS_NEW_S_POLICY = #{is_new_s_policy, jdbcType=NUMERIC} ,
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
			POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[where MARK_ID = #{mark_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findPolicyMarkingInfoByMarkId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingInfoByMarkIdCondition" />
	</select>
	
	<select id="PAS_findPolicyMarkingInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingInfoByPolicyCodeCondition" />
	</select>
	
	<select id="PAS_findPolicyMarkingInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingInfoByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapPolicyMarkingInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllPolicyMarkingInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_policyMarkingInfoWhereCondition" />
	</select>
<!-- 查询单条数据 -->
	<select id="PAS_findPolicyMarkingInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_policyMarkingInfoWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findPolicyMarkingInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryPolicyMarkingInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MARKING_TIME, B.EFFECT_NOTICE_INFO, B.MARK_ID, B.USER_ID, B.SLEEP_TYPE, B.DEAL_STATUS, 
			B.BUSI_PROD_CODE, B.POLICY_TYPE, B.DEAL_SCENE, B.POLICY_CODE, B.DEAL_TIME, 
			B.PLATFORM, B.IS_NEW_S_POLICY, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

	<!-- 查询此客户作为投保人或被保人的睡眠保单信息（保全专用） -->
	<select id="queryCustSleepPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			WITH CUST_HOLDER AS
			 (SELECT H.POLICY_CODE
			    FROM DEV_PAS.T_POLICY_HOLDER H
			   WHERE H.CUSTOMER_ID = #{CUSTOMER_ID}
			     AND NOT EXISTS (SELECT 1
			            FROM DEV_PAS.T_INSURED_LIST L
			           WHERE L.CUSTOMER_ID = H.CUSTOMER_ID
			             AND L.POLICY_CODE = H.POLICY_CODE)) /*投保人保单*/,
			CUST_INSURED AS
			 (SELECT L.POLICY_CODE
			    FROM DEV_PAS.T_INSURED_LIST L
			   WHERE L.CUSTOMER_ID = #{CUSTOMER_ID}
			     AND NOT EXISTS (SELECT 1
			            FROM DEV_PAS.T_POLICY_HOLDER H
			           WHERE L.CUSTOMER_ID = H.CUSTOMER_ID
			             AND L.POLICY_CODE = H.POLICY_CODE)) /*被保险人保单*/,
			CUST_HOLDER_INSURED AS
			 (SELECT H.POLICY_CODE
			    FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_POLICY_HOLDER L
			   WHERE H.CUSTOMER_ID = #{CUSTOMER_ID}
			     AND L.CUSTOMER_ID = H.CUSTOMER_ID
			     AND L.POLICY_CODE = H.POLICY_CODE) /*投、被保险人保单*/
			
			SELECT '投保人' AS CUST_TYPE, A.POLICY_CODE, A.MARK_ID
			  FROM DEV_PAS.T_POLICY_MARKING_INFO A, CUST_HOLDER Cust
			 WHERE A.POLICY_CODE = Cust.POLICY_CODE
			   AND A.SLEEP_TYPE IN ('B', 'C')
			   AND A.DEAL_STATUS = 'A'
			UNION ALL
			SELECT '被保险人' AS CUST_TYPE, A.POLICY_CODE, A.MARK_ID
			  FROM DEV_PAS.T_POLICY_MARKING_INFO A, CUST_INSURED Cust
			 WHERE A.POLICY_CODE = Cust.POLICY_CODE
			   AND A.SLEEP_TYPE IN ('B', 'C')
			   AND A.DEAL_STATUS = 'A'
			UNION ALL
			SELECT '投保人/被保险人' AS CUST_TYPE, A.POLICY_CODE, A.MARK_ID
			  FROM DEV_PAS.T_POLICY_MARKING_INFO A, CUST_HOLDER_INSURED Cust
			 WHERE A.POLICY_CODE = Cust.POLICY_CODE
			   AND A.SLEEP_TYPE IN ('B', 'C')
			   AND A.DEAL_STATUS = 'A'
		]]>
	</select>
	<!-- 按索引查询操作 -->	
	<select id="PAS_queryIsPolicySleep" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE 1 = 1 AND ROWNUM = 1 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" deal_status != null and deal_status != ''  "><![CDATA[ AND A.DEAL_STATUS = #{deal_status} ]]></if>
	</select>
	
	<!-- 修改操作 -->
	<update id="PAS_updatePolicyMarkingInfo1" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_MARKING_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			EFFECT_NOTICE_INFO = #{effect_notice_info, jdbcType=VARCHAR} ,
			PLATFORM = #{platform, jdbcType=VARCHAR} ,
			MARKING_TIME = SYSDATE,
			USER_ID = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE MARK_ID = #{mark_id, jdbcType=NUMERIC} ]]>
	</update>
	
	<!-- 添加操作 -->
	<insert id="PAS_addPolicyMarkingInfo1"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="mark_id">
			SELECT APP___PAS__DBUSER.S_POLICY_MARKING_INFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_MARKING_INFO(
				MARKING_TIME, EFFECT_NOTICE_INFO, MARK_ID, USER_ID, SLEEP_TYPE, INSERT_TIME, DEAL_STATUS, 
				UPDATE_TIME, BUSI_PROD_CODE, POLICY_TYPE, INSERT_TIMESTAMP, DEAL_SCENE, POLICY_CODE, DEAL_TIME, 
				UPDATE_BY, PLATFORM, IS_NEW_S_POLICY, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, POLICY_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{effect_notice_info, jdbcType=VARCHAR} , #{mark_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{sleep_type, jdbcType=VARCHAR} , SYSDATE , #{deal_status, jdbcType=VARCHAR} 
				, SYSDATE , #{busi_prod_code, jdbcType=VARCHAR} , #{policy_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{deal_scene, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP 
				, #{update_by, jdbcType=NUMERIC} , #{platform, jdbcType=VARCHAR} , #{is_new_s_policy, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
	<!-- 修改操作 -->
	<update id="PAS_updatePolicyMarkingInfo2" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_MARKING_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			DEAL_STATUS = #{deal_status, jdbcType=VARCHAR} ,
			DEAL_SCENE = #{deal_scene, jdbcType=VARCHAR} ,
			<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[DEAL_TIME = #{deal_time}, ]]></if>
			<if test=" deal_time  == null "><![CDATA[DEAL_TIME = SYSDATE, ]]></if>
			MARKING_TIME = SYSDATE,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE MARK_ID = #{mark_id, jdbcType=NUMERIC} ]]>
	</update>
	
	<!-- 查询所有操作 -->
	<select id="PAS_findAllPolicyMarkingInfo1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.DEAL_SCENE, A.POLICY_CODE, A.DEAL_TIME, 
			A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" deal_status != null and deal_status != ''  "><![CDATA[ AND A.DEAL_STATUS = #{deal_status} ]]></if>
	</select>
	
		<select id="PA_findConstantByKey" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select t.constants_id,
		       t.constants_key,
		       t.constants_value,
		       t.constants_desc,
		       t.sub_id
		  from dev_pas.T_CONSTANTS_INFO t
		 where t.constants_key = #{constants_key} 
	  ]]>
	</select>
	
	
		<!-- 查询所有操作 -->
	<select id="PAS_findAllPolicyMarkingInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME,
       (SELECT T.EFFECT_NOTICE_INFO_DESC
          FROM APP___PAS__DBUSER.T_EFFECT_NOTICE_INFO T
         WHERE T.EFFECT_NOTICE_INFO = A.EFFECT_NOTICE_INFO) EFFECT_NOTICE_INFO_DESC,
       A.MARK_ID,
       A.USER_ID,
       (SELECT B.USER_NAME
          FROM APP___PAS__DBUSER.T_UDMP_USER B
         WHERE B.USER_ID = A.USER_ID) USER_NAME,
       A.SLEEP_TYPE,
       A.DEAL_STATUS,
       A.BUSI_PROD_CODE,
       A.POLICY_TYPE,
       A.DEAL_SCENE,
       A.POLICY_CODE,
       A.DEAL_TIME,
       A.PLATFORM,
       A.IS_NEW_S_POLICY,
       (SELECT P.BUSI_PROD_CODE
          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
         WHERE P.BUSI_ITEM_ID = A.BUSI_ITEM_ID) PRODUCT_CODE_SYS,
       (SELECT R.PRODUCT_ABBR_NAME
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT R
         WHERE R.BUSINESS_PRD_ID =
               (SELECT P.BUSI_PRD_ID
                  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD P
                 WHERE P.BUSI_ITEM_ID = A.BUSI_ITEM_ID)) PRODUCT_ABBR_NAME,
       A.POLICY_ID
  FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ AND (A.SLEEP_TYPE = 'B' OR A.SLEEP_TYPE = 'C') ]]>
	</select>
	
	
	<!-- 查询所有操作 -->
	<select id="PAS_findPolicyMarkingInfosByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARK_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_INFO A  WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ AND A.DEAL_STATUS = 'A' AND A.EFFECT_NOTICE_INFO IS NULL]]>
	</select>
	
	
		<!-- 修改操作 -->
	<update id="PAS_updatePolicyMarkingInfo3" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_MARKING_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			EFFECT_NOTICE_INFO = #{effect_notice_info, jdbcType=VARCHAR},
			PLATFORM = #{platform, jdbcType=VARCHAR},
			USER_ID = #{user_id, jdbcType=NUMERIC},
			MARKING_TIME = #{marking_time, jdbcType=TIMESTAMP},
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{user_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE MARK_ID = #{mark_id, jdbcType=NUMERIC} ]]>
	</update>
	
</mapper>
