<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.impl.CsAgInfoDaoImpl">
	<sql id="csAgInfoWhereCondition">
		<if test=" adjust_cause != null and adjust_cause != ''  "><![CDATA[ AND A.ADJUST_CAUSE = #{adjust_cause} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsAgInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryCsAgInfoByChangeIdCondition">
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	</sql>	
	<sql id="queryCsAgInfoByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="queryCsAgInfoByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsAgInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_AG_INFO__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_AG_INFO(
				ADJUST_CAUSE, INSERT_TIME, UPDATE_TIME, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, CHANGE_ID, 
				LIST_ID, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, FEE_AMOUNT ) 
			VALUES (
				#{adjust_cause, jdbcType=VARCHAR}, SYSDATE , SYSDATE , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} ,
				#{fee_amount, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsAgInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_AG_INFO WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsAgInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_AG_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			ADJUST_CAUSE = #{adjust_cause, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按条件查询 -->
	<select id="findCsAgInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE 1 = 1  ]]>
		<include refid="csAgInfoWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
<!-- 按索引查询操作 -->	
	<select id="findCsAgInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCsAgInfoByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findCsAgInfoByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID,A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCsAgInfoByChangeIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findCsAgInfoByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCsAgInfoByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findCsAgInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID,A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE 1 = 1  ]]>
		<include refid="queryCsAgInfoByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsAgInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsAgInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCsAgInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsAgInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADJUST_CAUSE, B.POLICY_CODE, B.CHANGE_ID, 
			B.LIST_ID, B.POLICY_CHG_ID, B.FEE_AMOUNT FROM (
					SELECT ROWNUM RN, A.ADJUST_CAUSE, A.POLICY_CODE, A.CHANGE_ID, 
			A.LIST_ID, A.POLICY_CHG_ID, A.FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_AG_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
