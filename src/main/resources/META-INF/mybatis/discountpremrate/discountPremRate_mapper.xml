<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IDiscountPremRateDao">
<!--
	<sql id="PAS_discountPremRateWhereCondition">
		<if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND A.RISKCODE = #{riskcode} ]]></if>
		<if test=" year  != null "><![CDATA[ AND A.YEAR = #{year} ]]></if>
		<if test=" startdate  != null  and  startdate  != ''  "><![CDATA[ AND A.STARTDATE = #{startdate} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" payintv != null and payintv != ''  "><![CDATA[ AND A.PAYINTV = #{payintv} ]]></if>
		<if test=" discount_prem_rate  != null "><![CDATA[ AND A.DISCOUNT_PREM_RATE = #{discount_prem_rate} ]]></if>
		<if test=" enddate  != null  and  enddate  != ''  "><![CDATA[ AND A.ENDDATE = #{enddate} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryDiscountPremRateByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addDiscountPremRate"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT APP___PAS__DBUSER.S_DISCOUNT_PREM_RATE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE(
				INSERT_TIME, RISKCODE, YEAR, UPDATE_TIME, INSERT_TIMESTAMP, STARTDATE, UPDATE_BY, 
				LIST_ID, UPDATE_TIMESTAMP, PAYINTV, INSERT_BY, DISCOUNT_PREM_RATE, ENDDATE ) 
			VALUES (
				SYSDATE, #{riskcode, jdbcType=VARCHAR} , #{year, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP, #{startdate, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{payintv, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{discount_prem_rate, jdbcType=NUMERIC} , #{enddate, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deleteDiscountPremRate" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updateDiscountPremRate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE ]]>
		<set>
		<trim suffixOverrides=",">
			RISKCODE = #{riskcode, jdbcType=VARCHAR} ,
		    YEAR = #{year, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    STARTDATE = #{startdate, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			PAYINTV = #{payintv, jdbcType=VARCHAR} ,
		    DISCOUNT_PREM_RATE = #{discount_prem_rate, jdbcType=NUMERIC} ,
		    ENDDATE = #{enddate, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findDiscountPremRateByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISKCODE, A.YEAR, A.STARTDATE, 
			A.LIST_ID, A.PAYINTV, A.DISCOUNT_PREM_RATE, A.ENDDATE FROM APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE A WHERE 1 = 1  ]]>
		<include refid="PAS_queryDiscountPremRateByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapDiscountPremRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISKCODE, A.YEAR, A.STARTDATE, 
			A.LIST_ID, A.PAYINTV, A.DISCOUNT_PREM_RATE, A.ENDDATE FROM APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllDiscountPremRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISKCODE, A.YEAR, A.STARTDATE, 
			A.LIST_ID, A.PAYINTV, A.DISCOUNT_PREM_RATE, A.ENDDATE FROM APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findDiscountPremRateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE A WHERE 1 = 1  AND A.ENDDATE >= TO_DATE(TO_CHAR(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd') ]]>
		<if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND A.RISKCODE = #{riskcode} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryDiscountPremRateForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RISKCODE, B.YEAR, B.STARTDATE, 
			B.LIST_ID, B.PAYINTV, B.DISCOUNT_PREM_RATE, B.ENDDATE, B.RISKNAME FROM (
					SELECT ROWNUM RN, A.RISKCODE, A.YEAR, A.STARTDATE, 
			A.LIST_ID, TRIM(A.PAYINTV) PAYINTV, A.DISCOUNT_PREM_RATE, A.ENDDATE,
			(SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = A.RISKCODE
           AND ROWNUM = 1) RISKNAME
			 FROM APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND A.RISKCODE = #{riskcode} ]]></if>
		<![CDATA[  AND A.ENDDATE >= TO_DATE(TO_CHAR(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd'))  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 导出操作 -->
	<select id="PA_reportQueryDiscountPremRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.RISKCODE, A.YEAR, A.STARTDATE, 
			A.LIST_ID, TRIM(A.PAYINTV) PAYINTV, A.DISCOUNT_PREM_RATE, A.ENDDATE,
			(SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = A.RISKCODE
           AND ROWNUM = 1) RISKNAME
			 FROM APP___PAS__DBUSER.T_DISCOUNT_PREM_RATE A WHERE 1=1 AND A.ENDDATE >= TO_DATE(TO_CHAR(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd')]]>
		<if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND A.RISKCODE = #{riskcode} ]]></if>
		
	</select>
	
</mapper>
