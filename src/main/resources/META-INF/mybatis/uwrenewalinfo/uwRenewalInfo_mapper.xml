<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IUwRenewalInfoDao">
<!--
	<sql id="PAS_uwRenewalInfoWhereCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" uw_grade != null and uw_grade != ''  "><![CDATA[ AND A.UW_GRADE = #{uw_grade} ]]></if>
		<if test=" uw_status != null and uw_status != ''  "><![CDATA[ AND A.UW_STATUS = #{uw_status} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" uw_finish_time  != null  and  uw_finish_time  != ''  "><![CDATA[ AND A.UW_FINISH_TIME = #{uw_finish_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" uw_status_detail != null and uw_status_detail != ''  "><![CDATA[ AND A.UW_STATUS_DETAIL = #{uw_status_detail} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" uw_source_type != null and uw_source_type != ''  "><![CDATA[ AND A.UW_SOURCE_TYPE = #{uw_source_type} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" uw_renewal_id  != null "><![CDATA[ AND A.UW_RENEWAL_ID = #{uw_renewal_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryUwRenewalInfoByUwRenewalIdCondition">
		<if test=" uw_renewal_id  != null "><![CDATA[ AND A.UW_RENEWAL_ID = #{uw_renewal_id} ]]></if>
	</sql>	
	<sql id="PAS_queryUwRenewalInfoByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PAS_queryUwRenewalInfoByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PAS_queryUwRenewalInfoByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addUwRenewalInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="uw_renewal_id">
			SELECT APP___PAS__DBUSER.S_UW_RENEWAL_INFO__UW_R_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_UW_RENEWAL_INFO(
				UW_ID, UW_GRADE, INSERT_TIME, UPDATE_TIME, UW_STATUS, CASE_NO, UW_FINISH_TIME, 
				BUSI_PROD_CODE, UW_STATUS_DETAIL, POLICY_CODE, UPDATE_BY, UW_SOURCE_TYPE, UPDATE_TIMESTAMP, INSERT_BY, 
				BUSI_ITEM_ID, UW_RENEWAL_ID, POLICY_ID, DECISION_CODE ) 
			VALUES (
				#{uw_id, jdbcType=NUMERIC}, #{uw_grade, jdbcType=VARCHAR} , SYSDATE , SYSDATE , #{uw_status, jdbcType=VARCHAR} , #{case_no, jdbcType=VARCHAR} , #{uw_finish_time, jdbcType=DATE} 
				, #{busi_prod_code, jdbcType=VARCHAR} , #{uw_status_detail, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{uw_source_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{uw_renewal_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}, #{decision_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deleteUwRenewalInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO WHERE  = #{uw_renewal_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updateUwRenewalInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_UW_RENEWAL_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			UW_STATUS = #{uw_status, jdbcType=VARCHAR} ,
		    UW_FINISH_TIME = SYSDATE ,
			UW_STATUS_DETAIL = #{uw_status_detail, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    UPDATE_TIME = SYSDATE , 
		    DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE UW_ID = #{uw_id} AND BUSI_ITEM_ID = #{busi_item_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findUwRenewalInfoByUwRenewalId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.UW_GRADE, A.UW_STATUS, A.CASE_NO, A.UW_FINISH_TIME, 
			A.BUSI_PROD_CODE, A.UW_STATUS_DETAIL, A.POLICY_CODE, A.UW_SOURCE_TYPE, 
			A.BUSI_ITEM_ID, A.UW_RENEWAL_ID, A.POLICY_ID, A.DECISION_CODE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryUwRenewalInfoByUwRenewalIdCondition" />
	</select>
	
	<select id="PAS_findUwRenewalInfoByBusiItemId1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.UW_GRADE, A.UW_STATUS, A.CASE_NO, A.UW_FINISH_TIME, 
			A.BUSI_PROD_CODE, A.UW_STATUS_DETAIL, A.POLICY_CODE, A.UW_SOURCE_TYPE, 
			A.BUSI_ITEM_ID, A.UW_RENEWAL_ID, A.POLICY_ID, A.DECISION_CODE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryUwRenewalInfoByBusiItemIdCondition" />
	</select>
	
	<select id="PAS_findUwRenewalInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.UW_GRADE, A.UW_STATUS, A.CASE_NO, A.UW_FINISH_TIME, 
			A.BUSI_PROD_CODE, A.UW_STATUS_DETAIL, A.POLICY_CODE, A.UW_SOURCE_TYPE, 
			A.BUSI_ITEM_ID, A.UW_RENEWAL_ID, A.POLICY_ID, A.DECISION_CODE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryUwRenewalInfoByPolicyIdCondition" />
	</select>
	
	<select id="PAS_findUwRenewalInfoByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.UW_GRADE, A.UW_STATUS, A.CASE_NO, A.UW_FINISH_TIME, 
			A.BUSI_PROD_CODE, A.UW_STATUS_DETAIL, A.POLICY_CODE, A.UW_SOURCE_TYPE, 
			A.BUSI_ITEM_ID, A.UW_RENEWAL_ID, A.POLICY_ID, A.DECISION_CODE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryUwRenewalInfoByUwIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapUwRenewalInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.UW_GRADE, A.UW_STATUS, A.CASE_NO, A.UW_FINISH_TIME, 
			A.BUSI_PROD_CODE, A.UW_STATUS_DETAIL, A.POLICY_CODE, A.UW_SOURCE_TYPE, 
			A.BUSI_ITEM_ID, A.UW_RENEWAL_ID, A.POLICY_ID, A.DECISION_CODE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllUwRenewalInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.UW_GRADE, A.UW_STATUS, A.CASE_NO, A.UW_FINISH_TIME, 
			A.BUSI_PROD_CODE, A.UW_STATUS_DETAIL, A.POLICY_CODE, A.UW_SOURCE_TYPE, 
			A.BUSI_ITEM_ID, A.UW_RENEWAL_ID, A.POLICY_ID, A.DECISION_CODE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findUwRenewalInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryUwRenewalInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_ID, B.UW_GRADE, B.UW_STATUS, B.CASE_NO, B.UW_FINISH_TIME, 
			B.BUSI_PROD_CODE, B.UW_STATUS_DETAIL, B.POLICY_CODE, B.UW_SOURCE_TYPE, 
			B.BUSI_ITEM_ID, B.UW_RENEWAL_ID, B.POLICY_ID, B.DECISION_CODE FROM (
					SELECT ROWNUM RN, A.UW_ID, A.UW_GRADE, A.UW_STATUS, A.CASE_NO, A.UW_FINISH_TIME, 
			A.BUSI_PROD_CODE, A.UW_STATUS_DETAIL, A.POLICY_CODE, A.UW_SOURCE_TYPE, 
			A.BUSI_ITEM_ID, A.UW_RENEWAL_ID, A.POLICY_ID, A.DECISION_CODE FROM APP___PAS__DBUSER.T_UW_RENEWAL_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PAS_findUwRenewalInfoByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
        SELECT M.DECISION_CODE, M.UW_FINISH_TIME
           FROM (SELECT TURI.DECISION_CODE,
                        TURI.UW_FINISH_TIME
                   FROM DEV_PAS.T_UW_RENEWAL_INFO TURI
                  WHERE 1 = 1]]>
                    <if test=" busi_item_id  != null "><![CDATA[ AND TURI.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
                  <![CDATA[   
                    AND TURI.UW_STATUS = '04'
                    AND TURI.UW_STATUS_DETAIL = '0401'
                    AND TURI.UW_SOURCE_TYPE = '3'
                    AND TURI.UW_FINISH_TIME IS NOT NULL
                  ORDER BY TURI.UW_FINISH_TIME DESC) M
          WHERE ROWNUM = 1
		  ]]>
	</select>
	
</mapper>
