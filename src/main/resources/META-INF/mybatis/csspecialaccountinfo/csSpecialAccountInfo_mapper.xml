<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsSpecialAccountInfoDao">
   <sql id="csSpecialAccountInfoWhereCondition">
      <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
      <if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
      <if test=" plat_invest_acc != null and plat_invest_acc != ''  "><![CDATA[ AND A.PLAT_INVEST_ACC = #{plat_invest_acc} ]]></if>
      <if test=" account != null and account != ''  "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
      <if test=" plat_serialno != null and plat_serialno != ''  "><![CDATA[ AND A.PLAT_SERIALNO = #{plat_serialno} ]]></if>
      <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
      <if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
      <if test=" bank_phone != null and bank_phone != ''  "><![CDATA[ AND A.BANK_PHONE = #{bank_phone} ]]></if>
      <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
      <if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
      <if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
      <if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
      <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
      <if test=" account_name != null and account_name != ''  "><![CDATA[ AND A.ACCOUNT_NAME = #{account_name} ]]></if>
      <if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
      <if test=" account_bank != null and account_bank != ''  "><![CDATA[ AND A.ACCOUNT_BANK = #{account_bank} ]]></if>
      <if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
      <if test=" verify_flag != null and verify_flag != ''  "><![CDATA[ AND A.VERIFY_FLAG = #{verify_flag} ]]></if>
      <if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
      <if test=" validiy_end_date  != null  and  validiy_end_date  != ''  "><![CDATA[ AND A.VALIDIY_END_DATE = #{validiy_end_date} ]]></if>
   </sql>

<!-- 按索引生成的查询条件 -->  
   <sql id="queryCsSpecialAccountInfoByChangeIdCondition">
      <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
      <if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
   </sql>   

<!-- 添加操作 -->
   <insert id="addCsSpecialAccountInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
      <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="log_id">
         SELECT DEV_PAS.S_CS_SPECIAL_ACCOUNT_INFO.NEXTVAL FROM DUAL
      </selectKey>

      <![CDATA[
         INSERT INTO DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO(
            CUSTOMER_NAME, CUSTOMER_ID, PLAT_INVEST_ACC, ACCOUNT, PLAT_SERIALNO, CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, 
            OLD_NEW, BANK_PHONE, UPDATE_BY, CHANGE_ID, LIST_ID, CUSTOMER_BIRTHDAY, POLICY_CHG_ID, 
            CUSTOMER_CERT_TYPE, ACCOUNT_NAME, INSERT_TIME, OPERATION_TYPE, ACCOUNT_BANK, UPDATE_TIME, LOG_ID, 
            VERIFY_FLAG, UPDATE_TIMESTAMP, INSERT_BY, CUSTOMER_GENDER ) 
         VALUES (
            #{customer_name, jdbcType=VARCHAR}, #{customer_id, jdbcType=NUMERIC} , #{plat_invest_acc, jdbcType=VARCHAR} , #{account, jdbcType=VARCHAR} , #{plat_serialno, jdbcType=VARCHAR} , #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
            , #{old_new, jdbcType=VARCHAR} , #{bank_phone, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , APP___PAS__DBUSER.S_SPECIAL_ACCOUNT_INFO.NEXTVAL , #{customer_birthday, jdbcType=DATE} , #{policy_chg_id, jdbcType=NUMERIC} 
            , #{customer_cert_type, jdbcType=VARCHAR} , #{account_name, jdbcType=VARCHAR} , SYSDATE , #{operation_type, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , SYSDATE , #{log_id, jdbcType=NUMERIC} 
            , #{verify_flag, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC} ) 
       ]]>
   </insert>
   
<!-- 添加操作 -->
   <insert id="addCsSpecialAccountInfocs"  useGeneratedKeys="true" parameterType="java.util.Map">
      <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="log_id">
         SELECT DEV_PAS.S_CS_SPECIAL_ACCOUNT_INFO.NEXTVAL FROM DUAL
      </selectKey>

      <![CDATA[
         INSERT INTO DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO(
            CUSTOMER_NAME, CUSTOMER_ID, PLAT_INVEST_ACC, ACCOUNT, PLAT_SERIALNO, CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, 
            OLD_NEW, BANK_PHONE, UPDATE_BY, CHANGE_ID, LIST_ID, CUSTOMER_BIRTHDAY, POLICY_CHG_ID, 
            CUSTOMER_CERT_TYPE, ACCOUNT_NAME, INSERT_TIME, OPERATION_TYPE, ACCOUNT_BANK, UPDATE_TIME, LOG_ID, 
            VERIFY_FLAG, UPDATE_TIMESTAMP, INSERT_BY, CUSTOMER_GENDER ) 
         VALUES (
            #{customer_name, jdbcType=VARCHAR}, #{customer_id, jdbcType=NUMERIC} , #{plat_invest_acc, jdbcType=VARCHAR} , #{account, jdbcType=VARCHAR} , #{plat_serialno, jdbcType=VARCHAR} , #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
            , #{old_new, jdbcType=VARCHAR} , #{bank_phone, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{customer_birthday, jdbcType=DATE} , #{policy_chg_id, jdbcType=NUMERIC} 
            , #{customer_cert_type, jdbcType=VARCHAR} , #{account_name, jdbcType=VARCHAR} , SYSDATE , #{operation_type, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , SYSDATE , #{log_id, jdbcType=NUMERIC} 
            , #{verify_flag, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC} ) 
       ]]>
   </insert>

<!-- 删除操作 -->  
   <delete id="deleteCsSpecialAccountInfo" parameterType="java.util.Map">
      <![CDATA[ DELETE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO WHERE LOG_ID = #{log_id} ]]>
   </delete>

<!-- 修改操作 -->
   <update id="updateCsSpecialAccountInfo" parameterType="java.util.Map">
      <![CDATA[ UPDATE DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO ]]>
      <set>
      <trim suffixOverrides=",">
         CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
          CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
         PLAT_INVEST_ACC = #{plat_invest_acc, jdbcType=VARCHAR} ,
         ACCOUNT = #{account, jdbcType=VARCHAR} ,
         PLAT_SERIALNO = #{plat_serialno, jdbcType=VARCHAR} ,
         CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
         OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
         BANK_PHONE = #{bank_phone, jdbcType=VARCHAR} ,
          UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
          CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
          LIST_ID = #{list_id, jdbcType=NUMERIC} ,
          CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
          POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
         CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
         ACCOUNT_NAME = #{account_name, jdbcType=VARCHAR} ,
         OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
         ACCOUNT_BANK = #{account_bank, jdbcType=VARCHAR} ,
         UPDATE_TIME = SYSDATE , 
          LOG_ID = #{log_id, jdbcType=NUMERIC} ,
         VERIFY_FLAG = #{verify_flag, jdbcType=VARCHAR} ,
          UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
          CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
          VALIDIY_END_DATE = #{validiy_end_date, jdbcType=DATE} ,
      </trim>
      </set>
      <![CDATA[ WHERE LOG_ID = #{log_id} ]]>
   </update>

<!-- 按索引查询操作 -->  
   <select id="findCsSpecialAccountInfoByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_ID, A.PLAT_INVEST_ACC, A.ACCOUNT, A.PLAT_SERIALNO, A.CUSTOMER_CERTI_CODE, 
         A.OLD_NEW, A.BANK_PHONE, A.CHANGE_ID, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, 
         A.CUSTOMER_CERT_TYPE, A.ACCOUNT_NAME, A.OPERATION_TYPE, A.ACCOUNT_BANK, A.LOG_ID, 
         A.VERIFY_FLAG, A.CUSTOMER_GENDER,A.VALIDIY_END_DATE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
      <include refid="queryCsSpecialAccountInfoByChangeIdCondition" />
   </select>
<!-- 查询单条数据 -->  
   <select id="findCsSpecialAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_ID, A.PLAT_INVEST_ACC, A.ACCOUNT, A.PLAT_SERIALNO, A.CUSTOMER_CERTI_CODE, 
         A.OLD_NEW, A.BANK_PHONE, A.CHANGE_ID, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, 
         A.CUSTOMER_CERT_TYPE, A.ACCOUNT_NAME, A.OPERATION_TYPE, A.ACCOUNT_BANK, A.LOG_ID, 
         A.VERIFY_FLAG, A.CUSTOMER_GENDER,A.VALIDIY_END_DATE  FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
      <include refid="csSpecialAccountInfoWhereCondition" />
   </select>
   

<!-- 按map查询操作 -->
   <select id="findAllMapCsSpecialAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_ID, A.PLAT_INVEST_ACC, A.ACCOUNT, A.PLAT_SERIALNO, A.CUSTOMER_CERTI_CODE, 
         A.OLD_NEW, A.BANK_PHONE, A.CHANGE_ID, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, 
         A.CUSTOMER_CERT_TYPE, A.ACCOUNT_NAME, A.OPERATION_TYPE, A.ACCOUNT_BANK, A.LOG_ID, 
         A.VERIFY_FLAG, A.CUSTOMER_GENDER,A.VALIDIY_END_DATE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
       <include refid="queryCsSpecialAccountInfoByChangeIdCondition" /> 
   </select>

<!-- 查询所有操作 -->
   <select id="findAllCsSpecialAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_ID, A.PLAT_INVEST_ACC, A.ACCOUNT, A.PLAT_SERIALNO, A.CUSTOMER_CERTI_CODE, 
         A.OLD_NEW, A.BANK_PHONE, A.CHANGE_ID, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, 
         A.CUSTOMER_CERT_TYPE, A.ACCOUNT_NAME, A.OPERATION_TYPE, A.ACCOUNT_BANK, A.LOG_ID, 
         A.VERIFY_FLAG, A.CUSTOMER_GENDER,A.VALIDIY_END_DATE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO A WHERE ROWNUM <=  1000  ]]>
       <include refid="csSpecialAccountInfoWhereCondition" /> 
   </select>

<!-- 查询个数操作 -->
   <select id="findCsSpecialAccountInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
      <![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO A WHERE 1 = 1  ]]>
       <include refid="queryCsSpecialAccountInfoByChangeIdCondition" /> 
   </select>

<!-- 分页查询操作 -->
   <select id="queryCsSpecialAccountInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_NAME, B.CUSTOMER_ID, B.PLAT_INVEST_ACC, B.ACCOUNT, B.PLAT_SERIALNO, B.CUSTOMER_CERTI_CODE, 
         B.OLD_NEW, B.BANK_PHONE, B.CHANGE_ID, B.LIST_ID, B.CUSTOMER_BIRTHDAY, B.POLICY_CHG_ID, 
         B.CUSTOMER_CERT_TYPE, B.ACCOUNT_NAME, B.OPERATION_TYPE, B.ACCOUNT_BANK, B.LOG_ID, 
         B.VERIFY_FLAG, B.CUSTOMER_GENDER FROM (
               SELECT ROWNUM RN, A.CUSTOMER_NAME, A.CUSTOMER_ID, A.PLAT_INVEST_ACC, A.ACCOUNT, A.PLAT_SERIALNO, A.CUSTOMER_CERTI_CODE, 
         A.OLD_NEW, A.BANK_PHONE, A.CHANGE_ID, A.LIST_ID, A.CUSTOMER_BIRTHDAY, A.POLICY_CHG_ID, 
         A.CUSTOMER_CERT_TYPE, A.ACCOUNT_NAME, A.OPERATION_TYPE, A.ACCOUNT_BANK, A.LOG_ID, 
         A.VERIFY_FLAG, A.CUSTOMER_GENDER FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
       <include refid="queryCsSpecialAccountInfoByChangeIdCondition" /> 
      <![CDATA[ )  B
         WHERE B.RN > #{GREATER_NUM} ]]>
   </select>
   
   
   <!-- 查询所有操作 -->
   <select id="findCsSpecialAccountInfoone" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.LIST_ID,
				A.CUSTOMER_ID,
				A.CUSTOMER_NAME,
				A.CUSTOMER_BIRTHDAY,
				A.CUSTOMER_GENDER,
				A.CUSTOMER_CERT_TYPE,
				A.CUSTOMER_CERTI_CODE,
				A.ACCOUNT,
				A.ACCOUNT_NAME,
				A.ACCOUNT_BANK,
				A.BANK_PHONE
			 FROM DEV_PAS.T_SPECIAL_ACCOUNT_INFO A  
			         LEFT JOIN DEV_PAS.T_SPECIAL_ACCOUNT_RELATION B
			         ON B.BANK_POLICY_ID = A.LIST_ID 
			         WHERE  B.POLICY_CODE = #{plat_serialno}
          ]]>
   </select>
	
	<select id="findCsSpecialAccountBychangeId" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[ 
			SELECT A.CUSTOMER_ID,
			     A.ACCOUNT_NAME,
			     A.ACCOUNT,
			     A.ACCOUNT_BANK,
			     A.BANK_PHONE,
			     A.VALIDIY_END_DATE
			FROM APP___PAS__DBUSER.T_CS_SPECIAL_ACCOUNT_INFO A
			WHERE 1 = 1
		]]>
		<include refid="queryCsSpecialAccountInfoByChangeIdCondition" />
	 </select>
   
</mapper>
