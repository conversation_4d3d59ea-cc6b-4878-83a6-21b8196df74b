<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.impl.null">
<!--
	<sql id="CUS_csDocPrFileInfoWhereCondition">
		<if test=" faild_reason  != null "><![CDATA[ AND A.FAILD_REASON = #{faild_reason} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" is_faild  != null "><![CDATA[ AND A.IS_FAILD = #{is_faild} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsDocPrFileInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsDocPrFileInfoByBatchNoCondition">
		<if test=" batch_no != null and batch_no != '' "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
	</sql>	
	<sql id="CUS_queryCsDocPrFileInfoByTaskIdCondition">
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CS_addCsDocPrFileInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_DOC_PR_FILE_INFO__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_DOC_PR_FILE_INFO(
				FAILD_REASON, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, INSERT_TIME, BATCH_NO, LIST_ID, 
				UPDATE_TIMESTAMP, UPDATE_TIME, IS_FAILD, TASK_ID, INSERT_BY ) 
			VALUES (
				#{faild_reason, jdbcType=NUMERIC}, CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{batch_no, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{is_faild, jdbcType=NUMERIC} , #{task_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsDocPrFileInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CS_DOC_PR_FILE_INFO WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsDocPrFileInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_CS_DOC_PR_FILE_INFO ]]>
		<set>
		<trim suffixOverrides=",">
		    FAILD_REASON = #{faild_reason, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			BATCH_NO = #{batch_no, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    IS_FAILD = #{is_faild, jdbcType=NUMERIC} ,
		    TASK_ID = #{task_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsDocPrFileInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FAILD_REASON, A.POLICY_CODE, A.BATCH_NO, A.LIST_ID, 
			A.IS_FAILD, A.TASK_ID FROM T_CS_DOC_PR_FILE_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDocPrFileInfoByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="CUS_findCsDocPrFileInfoByBatchNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FAILD_REASON, A.POLICY_CODE, A.BATCH_NO, A.LIST_ID, 
			A.IS_FAILD, A.TASK_ID FROM T_CS_DOC_PR_FILE_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDocPrFileInfoByBatchNoCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="CUS_findCsDocPrFileInfoByTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FAILD_REASON, A.POLICY_CODE, A.BATCH_NO, A.LIST_ID, 
			A.IS_FAILD, A.TASK_ID FROM T_CS_DOC_PR_FILE_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDocPrFileInfoByTaskIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsDocPrFileInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FAILD_REASON, A.POLICY_CODE, A.BATCH_NO, A.LIST_ID, 
			A.IS_FAILD, A.TASK_ID FROM T_CS_DOC_PR_FILE_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsDocPrFileInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FAILD_REASON, A.POLICY_CODE, A.BATCH_NO, A.LIST_ID, 
			A.IS_FAILD, A.TASK_ID FROM T_CS_DOC_PR_FILE_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsDocPrFileInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CS_DOC_PR_FILE_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsDocPrFileInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.FAILD_REASON, B.POLICY_CODE, B.BATCH_NO, B.LIST_ID, 
			B.IS_FAILD, B.TASK_ID FROM (
					SELECT ROWNUM RN, A.FAILD_REASON, A.POLICY_CODE, A.BATCH_NO, A.LIST_ID, 
			A.IS_FAILD, A.TASK_ID FROM T_CS_DOC_PR_FILE_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
