<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRenewalLogDao">

	<sql id="PA_renewalLogWhereCondition">
		<if test=" gurnt_period_flag  != null "><![CDATA[ AND A.GURNT_PERIOD_FLAG = #{gurnt_period_flag} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" gurnt_renew_end  != null  and  gurnt_renew_end  != ''  "><![CDATA[ AND A.GURNT_RENEW_END = #{gurnt_renew_end} ]]></if>
		<if test=" gurnt_renew_start  != null  and  gurnt_renew_start  != ''  "><![CDATA[ AND A.GURNT_RENEW_START = #{gurnt_renew_start} ]]></if>
		<if test=" gurnt_renew_year  != null "><![CDATA[ AND A.GURNT_RENEW_YEAR = #{gurnt_renew_year} ]]></if>
		<if test=" next_date_cfg_flag  != null "><![CDATA[ AND A.NEXT_DATE_CFG_FLAG = #{next_date_cfg_flag} ]]></if>		
		<if test=" switch_busi_prod_code  != null "><![CDATA[ AND A.SWITCH_BUSI_PROD_CODE = #{switch_busi_prod_code} ]]></if>		
	
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryRenewalLogByBusiPrdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="PA_queryRenewalLogByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PA_queryRenewalLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryRenewalLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addRenewalLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_RENEWAL_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RENEWAL_LOG(
				GURNT_PERIOD_FLAG, INSERT_TIME, APPLY_DATE, UPDATE_TIME, BUSI_PROD_CODE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, POLICY_ID, GURNT_RENEW_END, 
				GURNT_RENEW_START, GURNT_RENEW_YEAR,NEXT_DATE_CFG_FLAG, SWITCH_BUSI_PROD_CODE ) 
			VALUES (
				#{gurnt_period_flag, jdbcType=NUMERIC}, SYSDATE , #{apply_date, jdbcType=DATE} , SYSDATE , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{gurnt_renew_end, jdbcType=DATE} 
				, #{gurnt_renew_start, jdbcType=DATE} , #{gurnt_renew_year, jdbcType=NUMERIC}, #{next_date_cfg_flag, jdbcType=NUMERIC}  , #{switch_busi_prod_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteRenewalLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RENEWAL_LOG WHERE  = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateRenewalLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RENEWAL_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    GURNT_PERIOD_FLAG = #{gurnt_period_flag, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    GURNT_RENEW_END = #{gurnt_renew_end, jdbcType=DATE} ,
		    GURNT_RENEW_START = #{gurnt_renew_start, jdbcType=DATE} ,
		    GURNT_RENEW_YEAR = #{gurnt_renew_year, jdbcType=NUMERIC} ,
		    NEXT_DATE_CFG_FLAG = #{next_date_cfg_flag, jdbcType=NUMERIC} ,
		    SWITCH_BUSI_PROD_CODE  = #{switch_busi_prod_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findRenewalLogByBusiPrdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryRenewalLogByBusiPrdCodeCondition" />
	</select>
	
	<select id="PA_findRenewalLogByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryRenewalLogByBusiItemIdCondition" />
	</select>
	
	<select id="PA_findRenewalLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryRenewalLogByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findRenewalLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryRenewalLogByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapRenewalLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_renewalLogWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllRenewalLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_renewalLogWhereCondition" />
	</select>
	
<!-- 查询所有操作 -->
	<select id="PA_findRenewalLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_renewalLogWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findRenewalLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_renewalLogWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryRenewalLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.GURNT_PERIOD_FLAG, B.APPLY_DATE, B.BUSI_PROD_CODE, B.POLICY_CODE, 
			B.LIST_ID, B.BUSI_ITEM_ID, B.POLICY_ID, B.GURNT_RENEW_END, 
			B.GURNT_RENEW_START, B.GURNT_RENEW_YEAR,B.NEXT_DATE_CFG_FLAG,B.SWITCH_BUSI_PROD_CODE FROM (
					SELECT ROWNUM RN, A.GURNT_PERIOD_FLAG, A.APPLY_DATE, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.GURNT_RENEW_END, 
			A.GURNT_RENEW_START, A.GURNT_RENEW_YEAR,A.NEXT_DATE_CFG_FLAG,A.SWITCH_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_RENEWAL_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_renewalLogWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 查询险种操作EN保全项的申请时间 -->
	<select id="PA_findCsEnApplyDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    Select A.Policy_Code,
			       A.Policy_Id,
			       c.busi_prod_code,
			       c.busi_item_id,
			       a.apply_time as apply_date
			  From dev_pas.T_Cs_Policy_Change      A,
			       dev_pas.T_Cs_Accept_Change      B,
			       dev_pas.t_Cs_Contract_Busi_Prod c
			 where A.ACCEPT_ID = B.Accept_Id
			   And A.Policy_Code = C.Policy_Code
			   And A.Service_Code = 'EN'
			   And B.Accept_Status = '18'
			   And C.Policy_Code = #{policy_code}
			   And C.Busi_Item_Id = #{busi_item_id}
			   And rownum = 1
			 order by a.apply_time desc
		]]>
	</select>
</mapper>
