<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsAgentAmountCfgDao">

	<sql id="csAgentAmountCfgWhereCondition">
		<if test=" account_type != null and account_type != '' "><![CDATA[ AND A.ACCOUNT_TYPE LIKE '${account_type}%' ]]></if>
		<if test=" service_codes != null and service_codes != '' "><![CDATA[ AND A.SERVICE_CODES LIKE '${service_codes}%' ]]></if>
		<if test=" sales_channels != null and sales_channels != '' "><![CDATA[ AND A.SALES_CHANNELS LIKE '${sales_channels}%' ]]></if>
		<if test=" is_deleted_new  != null "><![CDATA[ AND A.IS_DELETED = #{is_deleted_new} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" agent_amount  != null "><![CDATA[ AND A.AGENT_AMOUNT = #{agent_amount} ]]></if>
	</sql>
	
	
	<sql id="csAgentAmountCfgWhereConditionNew">
		<if test=" account_type != null and account_type != ''  "><![CDATA[ AND A.ACCOUNT_TYPE  like '%'||#{account_type}||'%']]></if>
		<if test=" service_codes != null and service_codes != ''  "><![CDATA[ AND A.SERVICE_CODES LIKE '%' ||#{service_codes}|| '%' ]]></if>
		<if test=" sales_channels != null and sales_channels != ''  "><![CDATA[ AND A.SALES_CHANNELS LIKE '%'|| #{sales_channels} ||'%' ]]></if>
	</sql>



<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsAgentAmountCfgByCfgIdCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsAgentAmountCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="cfg_id">
			SELECT DEV_PAS.S_RISK_LEVEL_CONFIG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CS_AGENT_AMOUNT_CFG(
				INSERT_TIME, UPDATE_TIME, ACCOUNT_TYPE, AGENT_AMOUNT, SERVICE_CODES, SALES_CHANNELS, IS_DELETED, 
				INSERT_TIMESTAMP, UPDATE_BY, CFG_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				SYSDATE, SYSDATE , #{account_type, jdbcType=VARCHAR} , #{agent_amount, jdbcType=NUMERIC} , #{service_codes, jdbcType=VARCHAR} , #{sales_channels, jdbcType=VARCHAR} , #{is_deleted_new, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{cfg_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->
	<delete id="deleteCsAgentAmountCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CS_AGENT_AMOUNT_CFG WHERE  CFG_ID = #{cfg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsAgentAmountCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CS_AGENT_AMOUNT_CFG ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
			ACCOUNT_TYPE = #{account_type, jdbcType=VARCHAR} ,
		    AGENT_AMOUNT = #{agent_amount, jdbcType=NUMERIC} ,
			SERVICE_CODES = #{service_codes, jdbcType=VARCHAR} ,
			SALES_CHANNELS = #{sales_channels, jdbcType=VARCHAR} ,
		    IS_DELETED = #{is_deleted_new, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
		</trim>
		</set>
		<![CDATA[ WHERE CFG_ID = #{cfg_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsAgentAmountCfgByCfgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_TYPE, A.AGENT_AMOUNT, A.SERVICE_CODES, A.SALES_CHANNELS, A.IS_DELETED as is_deleted_new, 
			A.CFG_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="queryCsAgentAmountCfgByCfgIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsAgentAmountCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_TYPE, A.AGENT_AMOUNT, A.SERVICE_CODES, A.SALES_CHANNELS, A.IS_DELETED as is_deleted_new, 
			A.CFG_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_CFG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsAgentAmountCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_TYPE, A.AGENT_AMOUNT, A.SERVICE_CODES, A.SALES_CHANNELS, A.IS_DELETED as is_deleted_new, 
			A.CFG_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_CFG A WHERE 1=1  ]]>
		<include refid="queryCsAgentAmountCfgByCfgIdCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCsAgentAmountCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_AGENT_AMOUNT_CFG A WHERE 1 = 1  ]]>
		<include refid="queryCsAgentAmountCfgByCfgIdCondition" />
	</select>



<!-- 分页查询操作 -->
	<select id="queryCsAgentAmountCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCOUNT_TYPE, B.AGENT_AMOUNT, B.SERVICE_CODES, B.SALES_CHANNELS, b.is_deleted_new, 
			 B.CFG_ID FROM (
					SELECT ROWNUM RN, A.ACCOUNT_TYPE, A.AGENT_AMOUNT, A.SERVICE_CODES, A.SALES_CHANNELS, A.IS_DELETED as is_deleted_new, 
			A.CFG_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_CFG A WHERE 1=1 ]]>	
			<include refid="queryCsAgentAmountCfgByCfgIdCondition" />
			<![CDATA[ AND ROWNUM <= #{LESS_NUM} ]]>
		<![CDATA[ )  B WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询销售渠道 -->
	<select id="findSalesChannel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select t.sales_channel_code, t.sales_channel_name from dev_pas.t_sales_channel t where 1=1]]>
			<if test=" sales_channel_code != null and sales_channel_code !='' "><![CDATA[and t.sales_channel_code in (${sales_channel_code}) ]]></if>
	</select>
	<!--查询保全项 -->
	<select id="findAllServiceCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT S.SERVICE_CODE,S.SERVICE_NAME FROM DEV_PAS.T_SERVICE S where 1=1]]>
			<if test=" service_code != null and service_code !='' "><![CDATA[ AND S.SERVICE_CODE IN (${service_code}) ]]></if>
			<![CDATA[ORDER BY S.SERVICE_CODE
	]]>
	</select>
	<!--查询保全项 -->
	<select id="findAllCsAgentAccTypeLX" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.TYPE_CODE FROM DEV_PAS.T_CS_AGENT_ACC_TYPE A WHERE ROWNUM <=  1000  ]]>
		<if test=" type_code != null and type_code != '' "><![CDATA[ AND A.TYPE_CODE in (${type_code})]]></if>
	</select>
	<!-- 没有分页查询操作 -->
	<select id="getCsAgentAmountCfgforCheck" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT A.ACCOUNT_TYPE, A.AGENT_AMOUNT, A.SERVICE_CODES, A.SALES_CHANNELS, A.IS_DELETED as is_deleted_new, 
			A.CFG_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_CFG A WHERE 1=1 ]]>	
			<include refid="csAgentAmountCfgWhereConditionNew" />
			<![CDATA[ AND ROWNUM <= 1000 ]]>

	</select>
	
	
</mapper>
