<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IAcceptSubmitTrailDao">

	<sql id="CUS_acceptSubmitTrailWhereCondition">
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" account_name != null and account_name != ''  "><![CDATA[ AND A.ACCOUNT_NAME = #{account_name} ]]></if>
		<if test=" order_no != null and order_no != ''  "><![CDATA[ AND A.ORDER_NO = #{order_no} ]]></if>
		<if test=" account_bank != null and account_bank != ''  "><![CDATA[ AND A.ACCOUNT_BANK = #{account_bank} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryAcceptSubmitTrailByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CS_addAcceptSubmitTrail"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_ACCEPT_SUBMIT_TRAIL.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ACCEPT_SUBMIT_TRAIL(
				ACCEPT_ID, ACCOUNT_ID, INSERT_TIME, ACCOUNT_NAME, ORDER_NO, ACCOUNT_BANK, CUSTOMER_ID, 
				UPDATE_TIME, ACCOUNT, ACCEPT_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY, SERVICE_CODE, POLICY_ID , PREM_RECEIVE_FORM , PAY_MODE) 
			VALUES (
				#{accept_id, jdbcType=NUMERIC}, #{account_id, jdbcType=NUMERIC} , SYSDATE , #{account_name, jdbcType=VARCHAR} , #{order_no, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} 
				, SYSDATE , #{account, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{prem_receive_form, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR}  ) 
		 ]]>
	</insert>


<!-- 按索引查询操作 -->	
	<select id="CUS_findAcceptSubmitTrailByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ORDER_NO, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.ACCEPT_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.SERVICE_CODE, A.POLICY_ID,A.prem_receive_form,A.pay_mode FROM APP___PAS__DBUSER.T_ACCEPT_SUBMIT_TRAIL A WHERE 1 = 1  ]]>
		<include refid="CUS_queryAcceptSubmitTrailByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapAcceptSubmitTrail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ORDER_NO, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.ACCEPT_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.SERVICE_CODE, A.POLICY_ID,A.prem_receive_form,A.pay_mode FROM APP___PAS__DBUSER.T_ACCEPT_SUBMIT_TRAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllAcceptSubmitTrail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ORDER_NO, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.ACCEPT_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.SERVICE_CODE, A.POLICY_ID,A.prem_receive_form,A.pay_mode FROM APP___PAS__DBUSER.T_ACCEPT_SUBMIT_TRAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findAcceptSubmitTrailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ACCEPT_SUBMIT_TRAIL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryAcceptSubmitTrailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCEPT_ID, B.ACCOUNT_ID, B.ACCOUNT_NAME, B.ORDER_NO, B.ACCOUNT_BANK, B.CUSTOMER_ID, 
			B.ACCOUNT, B.ACCEPT_CODE, B.POLICY_CODE, B.LIST_ID, 
			B.SERVICE_CODE, B.POLICY_ID,B.prem_receive_form,B.pay_mode FROM (
					SELECT ROWNUM RN, A.ACCEPT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ORDER_NO, A.ACCOUNT_BANK, A.CUSTOMER_ID, 
			A.ACCOUNT, A.ACCEPT_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.SERVICE_CODE, A.POLICY_ID,A.prem_receive_form,A.pay_mode FROM APP___PAS__DBUSER.T_ACCEPT_SUBMIT_TRAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
