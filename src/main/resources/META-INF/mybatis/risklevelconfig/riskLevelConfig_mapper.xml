<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRiskLevelConfigDao">

	<sql id="riskLevelConfigWhereCondition">
		<if test=" risk_score_max  != null "><![CDATA[ AND A.RISK_SCORE_MAX = #{risk_score_max} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" risk_score_min  != null "><![CDATA[ AND A.RISK_SCORE_MIN = #{risk_score_min} ]]></if>
		<if test=" risk_score_min_secondary  != null "><![CDATA[ AND A.RISK_SCORE_MIN_SECONDARY = #{risk_score_min_secondary} ]]></if>
		<if test=" risk_score_max_secondary  != null "><![CDATA[ AND A.RISK_SCORE_MAX_SECONDARY = #{risk_score_max_secondary} ]]></if>
		<if test=" risk_score_min_high  != null "><![CDATA[ AND A.RISK_SCORE_MIN_HIGH = #{risk_score_min_high} ]]></if>
		<if test=" risk_score_max_high  != null "><![CDATA[ AND A.RISK_SCORE_MAX_HIGH = #{risk_score_max_high} ]]></if>
		<if test=" is_valid  != null "><![CDATA[ AND A.IS_VALID = #{is_valid} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryRiskLevelConfigByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addRiskLevelConfig"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_RISK_LEVEL_CONFIG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG(
				INSERT_TIMESTAMP, RISK_SCORE_MAX, UPDATE_BY, INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, 
				INSERT_BY, RISK_SCORE_MIN, IS_VALID ,RISK_SCORE_MIN_SECONDARY,RISK_SCORE_MAX_SECONDARY,RISK_SCORE_MIN_HIGH,RISK_SCORE_MAX_HIGH) 
			VALUES (
				CURRENT_TIMESTAMP, #{risk_score_max, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE 
				, #{insert_by, jdbcType=NUMERIC} , #{risk_score_min, jdbcType=NUMERIC} , #{is_valid, jdbcType=NUMERIC} ,#{risk_score_min_secondary, jdbcType=NUMERIC},#{risk_score_max_secondary, jdbcType=NUMERIC}
				,#{risk_score_min_high, jdbcType=NUMERIC},#{risk_score_max_high, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRiskLevelConfig" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG WHERE  LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateRiskLevelConfig" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG ]]>
		<set>
		<trim suffixOverrides=",">
		    RISK_SCORE_MAX = #{risk_score_max, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    RISK_SCORE_MIN = #{risk_score_min, jdbcType=NUMERIC} ,
		    IS_VALID = #{is_valid, jdbcType=NUMERIC} ,
		    RISK_SCORE_MIN_SECONDARY = #{risk_score_min_secondary, jdbcType=NUMERIC} ,
		    RISK_SCORE_MAX_SECONDARY = #{risk_score_max_secondary, jdbcType=NUMERIC} ,
		    RISK_SCORE_MIN_HIGH = #{risk_score_min_high, jdbcType=NUMERIC} ,
		    RISK_SCORE_MAX_HIGH = #{risk_score_max_high, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findRiskLevelConfigByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_SCORE_MAX, A.LIST_ID, 
			 A.RISK_SCORE_MIN,A.RISK_SCORE_MAX_SECONDARY,A.RISK_SCORE_MIN_SECONDARY,A.RISK_SCORE_MAX_HIGH,
			 A.RISK_SCORE_MIN_HIGH, A.IS_VALID FROM APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG A WHERE 1 = 1  ]]>
		<include refid="riskLevelConfigWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapRiskLevelConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_SCORE_MAX, A.LIST_ID, 
			A.RISK_SCORE_MIN, A.RISK_SCORE_MAX_SECONDARY,A.RISK_SCORE_MIN_SECONDARY,A.RISK_SCORE_MAX_HIGH,
			 A.RISK_SCORE_MIN_HIGH, A.IS_VALID FROM APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG A WHERE ROWNUM <=  1000  ]]>
		<include refid="riskLevelConfigWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRiskLevelConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_SCORE_MAX, A.LIST_ID, 
			A.RISK_SCORE_MIN, A.RISK_SCORE_MAX_SECONDARY,A.RISK_SCORE_MIN_SECONDARY,A.RISK_SCORE_MAX_HIGH,
			 A.RISK_SCORE_MIN_HIGH, A.IS_VALID FROM APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG A WHERE ROWNUM <=  1000  ]]>
		<include refid="riskLevelConfigWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findRiskLevelConfigTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG A where 1=1]]>
		<include refid="riskLevelConfigWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryRiskLevelConfigForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select C.rowNumber,C.RISK_SCORE_MAX,C.RISK_SCORE_MIN,C.RISK_SCORE_MAX_SECONDARY,C.RISK_SCORE_MIN_SECONDARY,C.RISK_SCORE_MAX_HIGH,C.RISK_SCORE_MIN_HIGH,
				C.REAL_NAME,C.USER_NAME,C.INSERT_BY,C.INSERT_TIME,C.IS_VALID 
				from(
      				select rownum as rowNumber,B.RISK_SCORE_MAX,B.RISK_SCORE_MIN,B.RISK_SCORE_MAX_SECONDARY,B.RISK_SCORE_MIN_SECONDARY,B.RISK_SCORE_MAX_HIGH,B.RISK_SCORE_MIN_HIGH,
      						B.REAL_NAME,B.USER_NAME,B.INSERT_BY,B.INSERT_TIME,B.IS_VALID 
      						from(
              					SELECT A.RISK_SCORE_MAX,A.RISK_SCORE_MIN,A.RISK_SCORE_MAX_SECONDARY,A.RISK_SCORE_MIN_SECONDARY,A.RISK_SCORE_MAX_HIGH,A.RISK_SCORE_MIN_HIGH,
                   						(SELECT T.REAL_NAME FROM DEV_PAS.T_UDMP_USER T WHERE T.USER_ID = A.INSERT_BY)REAL_NAME,(SELECT T.USER_NAME FROM DEV_PAS.T_UDMP_USER T WHERE T.USER_ID = A.INSERT_BY)USER_NAME,
                     					A.INSERT_BY,A.INSERT_TIME ,IS_VALID
                     					FROM  APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG A  ]]>
		                 				<include refid="riskLevelConfigWhereCondition" /> 
                     					<![CDATA[order by A.IS_VALID desc,A.INSERT_TIME desc
                     ) B 
          		) C
                where C.rowNumber > #{GREATER_NUM} and C.rowNumber <=#{LESS_NUM}]]>
	</select>
	
	
	<!-- 查询风险等级 --> 
	<select id="findRiskLevelConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_SCORE_MAX, A.LIST_ID, 
			A.RISK_SCORE_MIN, A.RISK_SCORE_MAX_SECONDARY,A.RISK_SCORE_MIN_SECONDARY,A.RISK_SCORE_MAX_HIGH,
			 A.RISK_SCORE_MIN_HIGH, A.IS_VALID FROM APP___PAS__DBUSER.T_RISK_LEVEL_CONFIG A WHERE ROWNUM <=  1000  ]]>
		<include refid="riskLevelConfigWhereCondition" />
	</select>
	
</mapper>
