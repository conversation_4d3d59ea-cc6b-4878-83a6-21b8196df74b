<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.impl.null">
<!--
	<sql id="CUS_csDocPrintDetailWhereCondition">
		<if test=" liability_status  != null "><![CDATA[ AND A.LIABILITY_STATUS = #{liability_status} ]]></if>
		<if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND <PERSON>.TASK_ID = #{task_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" doc_create_date  != null  and  doc_create_date  != ''  "><![CDATA[ AND A.DOC_CREATE_DATE = #{doc_create_date} ]]></if>
		<if test=" policy_holder_name != null and policy_holder_name != ''  "><![CDATA[ AND A.POLICY_HOLDER_NAME = #{policy_holder_name} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" org_code != null and org_code != ''  "><![CDATA[ AND A.ORG_CODE = #{org_code} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsDocPrintDetailByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsDocPrintDetailByTaskIdCondition">
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CS_addCsDocPrintDetail"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
            SELECT APP___PAS__DBUSER.S_CS_DOC_PRINT_DETAIL__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_DOC_PRINT_DETAIL(
				LIABILITY_STATUS, INSERT_TIME, TEMPLATE_CODE, AGENT_NAME, UPDATE_TIME, TASK_ID, BUSI_PROD_CODE, 
				INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, DOC_CREATE_DATE, POLICY_HOLDER_NAME, LIST_ID, ORG_CODE, 
				UPDATE_TIMESTAMP, INSERT_BY, AGENT_CODE ) 
			VALUES (
				#{liability_status, jdbcType=NUMERIC}, SYSDATE , #{template_code, jdbcType=VARCHAR} , #{agent_name, jdbcType=VARCHAR} , SYSDATE , #{task_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{doc_create_date, jdbcType=DATE} , #{policy_holder_name, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{org_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{agent_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsDocPrintDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CS_DOC_PRINT_DETAIL WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsDocPrintDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_CS_DOC_PRINT_DETAIL ]]>
		<set>
		<trim suffixOverrides=",">
		    LIABILITY_STATUS = #{liability_status, jdbcType=NUMERIC} ,
			TEMPLATE_CODE = #{template_code, jdbcType=VARCHAR} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    TASK_ID = #{task_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    DOC_CREATE_DATE = #{doc_create_date, jdbcType=DATE} ,
			POLICY_HOLDER_NAME = #{policy_holder_name, jdbcType=VARCHAR} ,
			ORG_CODE = #{org_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsDocPrintDetailByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_STATUS, A.TEMPLATE_CODE, A.AGENT_NAME, A.TASK_ID, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.DOC_CREATE_DATE, A.POLICY_HOLDER_NAME, A.LIST_ID, A.ORG_CODE, 
			A.AGENT_CODE FROM T_CS_DOC_PRINT_DETAIL A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDocPrintDetailByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="CUS_findCsDocPrintDetailByTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_STATUS, A.TEMPLATE_CODE, A.AGENT_NAME, A.TASK_ID, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.DOC_CREATE_DATE, A.POLICY_HOLDER_NAME, A.LIST_ID, A.ORG_CODE, 
			A.AGENT_CODE FROM T_CS_DOC_PRINT_DETAIL A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDocPrintDetailByTaskIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsDocPrintDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_STATUS, A.TEMPLATE_CODE, A.AGENT_NAME, A.TASK_ID, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.DOC_CREATE_DATE, A.POLICY_HOLDER_NAME, A.LIST_ID, A.ORG_CODE, 
			A.AGENT_CODE FROM T_CS_DOC_PRINT_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsDocPrintDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_STATUS, A.TEMPLATE_CODE, A.AGENT_NAME, A.TASK_ID, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.DOC_CREATE_DATE, A.POLICY_HOLDER_NAME, A.LIST_ID, A.ORG_CODE, 
			A.AGENT_CODE FROM T_CS_DOC_PRINT_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsDocPrintDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CS_DOC_PRINT_DETAIL A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsDocPrintDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LIABILITY_STATUS, B.TEMPLATE_CODE, B.AGENT_NAME, B.TASK_ID, B.BUSI_PROD_CODE, 
			B.POLICY_CODE, B.DOC_CREATE_DATE, B.POLICY_HOLDER_NAME, B.LIST_ID, B.ORG_CODE, 
			B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.LIABILITY_STATUS, A.TEMPLATE_CODE, A.AGENT_NAME, A.TASK_ID, A.BUSI_PROD_CODE, 
			A.POLICY_CODE, A.DOC_CREATE_DATE, A.POLICY_HOLDER_NAME, A.LIST_ID, A.ORG_CODE, 
			A.AGENT_CODE FROM T_CS_DOC_PRINT_DETAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
