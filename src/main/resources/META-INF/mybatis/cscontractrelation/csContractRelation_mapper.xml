<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsContractRelationDao">

	<sql id="csContractRelationWhereCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" relation_type != null and relation_type != ''  "><![CDATA[ AND A.RELATION_TYPE = #{relation_type} ]]></if>
		<if test=" master_policy_id  != null "><![CDATA[ AND A.MASTER_POLICY_ID = #{master_policy_id} ]]></if>
		<if test=" master_policy_code != null and master_policy_code != ''  "><![CDATA[ AND A.MASTER_POLICY_CODE = #{master_policy_code} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND A.MASTER_BUSI_PROD_CODE = #{master_busi_prod_code} ]]></if>
		<if test=" sub_policy_id  != null "><![CDATA[ AND A.SUB_POLICY_ID = #{sub_policy_id} ]]></if>
		<if test=" sub_policy_code != null and sub_policy_code != ''  "><![CDATA[ AND A.SUB_POLICY_CODE = #{sub_policy_code} ]]></if>
		<if test=" sub_busi_item_id  != null "><![CDATA[ AND A.SUB_BUSI_ITEM_ID = #{sub_busi_item_id} ]]></if>
		<if test=" sub_busi_prod_code != null and sub_busi_prod_code != ''  "><![CDATA[ AND A.SUB_BUSI_PROD_CODE = #{sub_busi_prod_code} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsContractRelationByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryCsContractRelationByMasterPolicyCodeCondition">
		<if test=" master_policy_code != null and master_policy_code != '' "><![CDATA[ AND A.MASTER_POLICY_CODE = #{master_policy_code} ]]></if>
	</sql>	
	<sql id="queryCsContractRelationByMasterPolicyIdCondition">
		<if test=" master_policy_id  != null "><![CDATA[ AND A.MASTER_POLICY_ID = #{master_policy_id} ]]></if>
	</sql>	
	<sql id="queryCsContractRelationBySubPolicyCodeCondition">
		<if test=" sub_policy_code != null and sub_policy_code != '' "><![CDATA[ AND A.SUB_POLICY_CODE = #{sub_policy_code} ]]></if>
	</sql>	
	<sql id="queryCsContractRelationBySubPolicyIdCondition">
		<if test=" sub_policy_id  != null "><![CDATA[ AND A.SUB_POLICY_ID = #{sub_policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsContractRelation"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_REL__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_RELATION(
				SUB_POLICY_CODE, INSERT_TIME, OPERATION_TYPE, MASTER_BUSI_ITEM_ID, UPDATE_TIME, MASTER_BUSI_PROD_CODE, RELATION_TYPE, 
				MASTER_POLICY_CODE, INSERT_TIMESTAMP, LOG_ID, OLD_NEW, SUB_BUSI_ITEM_ID, UPDATE_BY, MASTER_POLICY_ID, 
				CHANGE_ID, LIST_ID, SUB_POLICY_ID, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, SUB_BUSI_PROD_CODE ) 
			VALUES (
				#{sub_policy_code, jdbcType=VARCHAR}, SYSDATE , #{operation_type, jdbcType=VARCHAR} , #{master_busi_item_id, jdbcType=NUMERIC} , SYSDATE , #{master_busi_prod_code, jdbcType=VARCHAR} , #{relation_type, jdbcType=VARCHAR} 
				, #{master_policy_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{old_new, jdbcType=VARCHAR} , #{sub_busi_item_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{master_policy_id, jdbcType=NUMERIC} 
				, #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{sub_policy_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{sub_busi_prod_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

	<insert id="batchSaveCsContractRelation" parameterType="java.util.List" useGeneratedKeys="false">
        INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_RELATION
        (
            LOG_ID,LIST_ID,
			SUB_POLICY_CODE, INSERT_TIME, OPERATION_TYPE, MASTER_BUSI_ITEM_ID, UPDATE_TIME, MASTER_BUSI_PROD_CODE, RELATION_TYPE, 
			MASTER_POLICY_CODE, INSERT_TIMESTAMP, OLD_NEW, SUB_BUSI_ITEM_ID, UPDATE_BY, MASTER_POLICY_ID, 
			CHANGE_ID, SUB_POLICY_ID, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, SUB_BUSI_PROD_CODE
        ) SELECT
		APP___PAS__DBUSER.S_CONTRACT_REL__LIST_ID.NEXTVAL as LOG_ID,
		APP___PAS__DBUSER.S_CONTRACT_RELATION.NEXTVAL as LIST_ID,
		S.* FROM (
        <foreach item="relation" index="index" collection="list" separator="union all">
               	SELECT #{relation.subPolicyCode,jdbcType=VARCHAR} as SUB_POLICY_CODE,
				SYSDATE as INSERT_TIME,
				#{relation.operationType,jdbcType=VARCHAR} as OPERATION_TYPE,
				#{relation.masterBusiItemId,jdbcType=NUMERIC} as MASTER_BUSI_ITEM_ID,
				SYSDATE as UPDATE_TIME,
				#{relation.masterBusiProdCode,jdbcType=VARCHAR} as MASTER_BUSI_PROD_CODE,
				#{relation.relationType,jdbcType=VARCHAR} as RELATION_TYPE,
				#{relation.masterPolicyCode,jdbcType=VARCHAR} as MASTER_POLICY_CODE,
				SYSDATE as INSERT_TIMESTAMP,
				#{relation.oldNew,jdbcType=VARCHAR} as OLD_NEW,
				#{relation.subBusiItemId,jdbcType=NUMERIC} as SUB_BUSI_ITEM_ID,
				0 as UPDATE_BY,
				#{relation.masterPolicyId,jdbcType=NUMERIC} as MASTER_POLICY_ID,
				#{relation.changeId,jdbcType=NUMERIC} as CHANGE_ID,
				#{relation.subPolicyId,jdbcType=NUMERIC} as SUB_POLICY_ID,
				SYSDATE as UPDATE_TIMESTAMP,
				#{relation.policyChgId,jdbcType=NUMERIC} as POLICY_CHG_ID,
				0 as INSERT_BY,
				#{relation.subBusiProdCode,jdbcType=VARCHAR} as SUB_BUSI_PROD_CODE
            FROM DUAL
        </foreach>
        ) S
    </insert>
	
	
	
	
	
<!-- 删除操作 -->	
	<delete id="deleteCsContractRelation" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE  1=1 ]]>
		<include refid="csContractRelationWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="updateCsContractRelation" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A]]>
		<set>
		<trim suffixOverrides=",">
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			RELATION_TYPE = #{relation_type, jdbcType=VARCHAR} ,
		    MASTER_POLICY_ID = #{master_policy_id, jdbcType=NUMERIC} ,
			MASTER_POLICY_CODE = #{master_policy_code, jdbcType=VARCHAR} ,
		    MASTER_BUSI_ITEM_ID = #{master_busi_item_id, jdbcType=NUMERIC} ,
			MASTER_BUSI_PROD_CODE = #{master_busi_prod_code, jdbcType=VARCHAR} ,
		    SUB_POLICY_ID = #{sub_policy_id, jdbcType=NUMERIC} ,
			SUB_POLICY_CODE = #{sub_policy_code, jdbcType=VARCHAR} ,
		    SUB_BUSI_ITEM_ID = #{sub_busi_item_id, jdbcType=NUMERIC} ,
			SUB_BUSI_PROD_CODE = #{sub_busi_prod_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="queryCsContractRelationByListIdCondition" />
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
	</update>

<!-- 修改操作 -->
	<update id="updateOperationTypeOfCsContractRelation" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A]]>
		<set>
		<trim suffixOverrides=",">
			<if test=" operation_type  != null "><![CDATA[ OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} , ]]></if>
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="queryCsContractRelationByListIdCondition" />
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
	</update>
<!-- 按索引查询操作 -->	
	<select id="findCsContractRelationByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<include refid="queryCsContractRelationByListIdCondition" />
	</select>
	
	<select id="findCsContractRelationByMasterPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<include refid="queryCsContractRelationByMasterPolicyCodeCondition" />
	</select>
	
	<select id="findCsContractRelationByMasterPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<include refid="queryCsContractRelationByMasterPolicyIdCondition" />
	</select>
	
	<select id="findCsContractRelationBySubPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<include refid="queryCsContractRelationBySubPolicyCodeCondition" />
	</select>
	
	<select id="findCsContractRelationBySubPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<include refid="queryCsContractRelationBySubPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsContractRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsContractRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="csContractRelationWhereCondition" />
		ORDER BY A.LOG_ID, A.OPERATION_TYPE ASC
	</select>
	<!-- 113326 add -->
	<select id="findAllCsContractRelationForDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select r.operation_type,r.master_busi_item_id,r.sub_busi_item_id,r.master_policy_code relPolicyNo,
				         case when mcp.master_busi_item_id is null then '1' else '0' end relMRiskFlag,
				         (select p.product_abbr_name from dev_pas.t_business_product p where p.business_prd_id = mcp.busi_prd_id)
			                 ||'，'||(SELECT TO_CHAR(LISTAGG(bp.product_abbr_name, '，') WITHIN
			                                   GROUP(ORDER BY T.master_busi_item_id)) AS product_abbr_name
			                      FROM dev_pas.t_Contract_Busi_Prod T
			                      join dev_pas.t_business_product bp
			                        on bp.business_prd_id = t.busi_prd_id
			                     WHERE t.policy_id = mcp.policy_id
			                       and t.master_busi_item_id = mcp.busi_item_id
			                       and t.liability_state <> 3) relRiskName,
				         r.sub_policy_code relPolicyNo2,
				         (select p.product_abbr_name from dev_pas.t_business_product p where p.business_prd_id = rcp.busi_prd_id) relRiskName2,
				         (select rt.policy_relation_name from dev_pas.T_POLICY_RELATION_TYPE rt 
                   		   where rt.policy_relation_type = case r.relation_type when '2' then '0' when '3' then '1' when '4' then '2' end) relType,
                 		 case r.relation_type when '2' then '0' when '3' then '1' when '4' then '2' end relTypeCode
				    from dev_pas.T_CS_CONTRACT_RELATION r
				    join dev_pas.T_CONTRACT_BUSI_PROD mcp
				      on mcp.policy_code = r.master_policy_code
				     and mcp.busi_item_id = r.master_busi_item_id
				    join dev_pas.T_CONTRACT_BUSI_PROD rcp
				      on rcp.policy_code = r.sub_policy_code
				     and rcp.busi_item_id = r.sub_busi_item_id
				   where ROWNUM <=  1000   ]]>
		<if test=" change_id  != null "><![CDATA[ AND r.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND r.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND r.OLD_NEW = #{old_new} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND r.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
	</select>
	<!-- 113326 add -->
	<select id="checkRelationChange" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select case when '0' = #{operaton_flag} then 
				         (select case when count(1) > 0 then 0 else 1 end /*如果是建立，判断新数据是否跟旧数据相同，如果相同则需要提示，0提示，>0不提示*/
				          from dev_pas.T_CS_CONTRACT_RELATION t
				         where t.old_new = 0 and rownum < 100 ]]>
				           <if test=" change_id  != null "><![CDATA[ AND t.CHANGE_ID = #{change_id} ]]></if>
				           <if test=" policy_chg_id  != null "><![CDATA[ AND t.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
				           <if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND t.MASTER_BUSI_PROD_CODE = #{master_busi_prod_code} ]]></if>
				           <if test=" sub_policy_code != null and sub_policy_code != ''  "><![CDATA[ AND t.SUB_POLICY_CODE = #{sub_policy_code} ]]></if>
				           <if test=" sub_busi_prod_code != null and sub_busi_prod_code != ''  "><![CDATA[ AND t.SUB_BUSI_PROD_CODE = #{sub_busi_prod_code} ]]></if>
				           <if test=" relation_type != null and relation_type != ''  "><![CDATA[ AND t.RELATION_TYPE = #{relation_type} ]]></if>
				 <![CDATA[ )
				           +
				           (select count(1) /*查询选中的险种以外是否有修改的数据操作类型1，2。如果存在则不同,不用提示 0提示，>0不提示*/
				            from dev_pas.T_CS_CONTRACT_RELATION t
				           where t.old_new = 1 and rownum < 100 ]]>
				           <if test=" change_id  != null "><![CDATA[ AND t.CHANGE_ID = #{change_id} ]]></if>
				           <if test=" policy_chg_id  != null "><![CDATA[ AND t.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
				           <if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND t.MASTER_BUSI_PROD_CODE <> #{master_busi_prod_code} ]]></if>
				 <![CDATA[   and t.operation_type in ('1','2')
				 			and not exists(select 1 from dev_pas.T_CS_CONTRACT_RELATION t1
				                   where t1.old_new = 0 and t1.change_id = t.change_id 
				                   and t1.policy_chg_id = t.policy_chg_id and t1.master_busi_prod_code = t.master_busi_prod_code
				                   and t1.sub_policy_code = t.sub_policy_code and t1.sub_busi_prod_code = t.sub_busi_prod_code
				                   and t1.relation_type = t1.relation_type))
				 			+
				 			(select count(1) /*查询选中的险种以外是否有修改的数据且存在旧数据的操作类型3。如果存在则不同,不用提示 0提示，>0不提示*/
				                from dev_pas.T_CS_CONTRACT_RELATION t
				               where t.old_new = 1 and rownum < 100 ]]>
				           <if test=" change_id  != null "><![CDATA[ AND t.CHANGE_ID = #{change_id} ]]></if>
				           <if test=" policy_chg_id  != null "><![CDATA[ AND t.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
				           <if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND t.MASTER_BUSI_PROD_CODE <> #{master_busi_prod_code} ]]></if>
				 <![CDATA[    and t.operation_type = '3'
				               and exists(select 1 from dev_pas.T_CS_CONTRACT_RELATION t1
				                   where t1.old_new = 0 and t1.change_id = t.change_id 
				                   and t1.policy_chg_id = t.policy_chg_id and t1.master_busi_prod_code = t.master_busi_prod_code))
				        when '1' = #{operaton_flag} then 
				          (select count(1) /*如果是取消，查询是否存在旧数据,如果存在则说明与之前不同，不用提示 0提示，>0不提示*/
				            from dev_pas.T_CS_CONTRACT_RELATION t
				           where t.old_new = 0 and rownum < 100 ]]>
				           <if test=" change_id  != null "><![CDATA[ AND t.CHANGE_ID = #{change_id} ]]></if>
				           <if test=" policy_chg_id  != null "><![CDATA[ AND t.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
				           <if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND t.MASTER_BUSI_PROD_CODE = #{master_busi_prod_code} ]]></if>
				 <![CDATA[   )
				           +
				           (select count(1) /*查询选中的险种以外是否有修改的数据操作类型1，2。如果存在则不同,不用提示 0提示，>0不提示*/
				            from dev_pas.T_CS_CONTRACT_RELATION t
				           where t.old_new = 1 and rownum < 100 ]]>
				           <if test=" change_id  != null "><![CDATA[ AND t.CHANGE_ID = #{change_id} ]]></if>
				           <if test=" policy_chg_id  != null "><![CDATA[ AND t.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
				           <if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND t.MASTER_BUSI_PROD_CODE <> #{master_busi_prod_code} ]]></if>
				  <![CDATA[  and t.operation_type in ('1','2')
				  			and not exists(select 1 from dev_pas.T_CS_CONTRACT_RELATION t1
				                   where t1.old_new = 0 and t1.change_id = t.change_id 
				                   and t1.policy_chg_id = t.policy_chg_id and t1.master_busi_prod_code = t.master_busi_prod_code
				                   and t1.sub_policy_code = t.sub_policy_code and t1.sub_busi_prod_code = t.sub_busi_prod_code
				                   and t1.relation_type = t1.relation_type))
				  			+
				 			(select count(1) /*查询选中的险种以外是否有修改的数据且存在旧数据的操作类型3。如果存在则不同,不用提示 0提示，>0不提示*/
				                from dev_pas.T_CS_CONTRACT_RELATION t
				               where t.old_new = 1 and rownum < 100 ]]>
				           <if test=" change_id  != null "><![CDATA[ AND t.CHANGE_ID = #{change_id} ]]></if>
				           <if test=" policy_chg_id  != null "><![CDATA[ AND t.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
				           <if test=" master_busi_prod_code != null and master_busi_prod_code != ''  "><![CDATA[ AND t.MASTER_BUSI_PROD_CODE <> #{master_busi_prod_code} ]]></if>
				 <![CDATA[    and t.operation_type = '3'
				               and exists(select 1 from dev_pas.T_CS_CONTRACT_RELATION t1
				                   where t1.old_new = 0 and t1.change_id = t.change_id 
				                   and t1.policy_chg_id = t.policy_chg_id and t1.master_busi_prod_code = t.master_busi_prod_code))
				        end
				from dual  ]]>
	</select>
<!-- 查询单条数据 -->
	<select id="findCsContractRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<include refid="csContractRelationWhereCondition" />
	</select>
<!-- 查询个数操作 -->
	<select id="findCsContractRelationTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsContractRelationForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SUB_POLICY_CODE, B.OPERATION_TYPE, B.MASTER_BUSI_ITEM_ID, B.MASTER_BUSI_PROD_CODE, B.RELATION_TYPE, 
			B.MASTER_POLICY_CODE, B.LOG_ID, B.OLD_NEW, B.SUB_BUSI_ITEM_ID, B.MASTER_POLICY_ID, 
			B.CHANGE_ID, B.LIST_ID, B.SUB_POLICY_ID, B.POLICY_CHG_ID, B.SUB_BUSI_PROD_CODE FROM (
					SELECT ROWNUM RN, A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findExistCsContractRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_POLICY_CODE, A.OPERATION_TYPE, A.MASTER_BUSI_ITEM_ID, A.MASTER_BUSI_PROD_CODE, A.RELATION_TYPE, 
			A.MASTER_POLICY_CODE, A.LOG_ID, A.OLD_NEW, A.SUB_BUSI_ITEM_ID, A.MASTER_POLICY_ID, 
			A.CHANGE_ID, A.LIST_ID, A.SUB_POLICY_ID, A.POLICY_CHG_ID, A.SUB_BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_RELATION A WHERE 1 = 1  ]]>
		<include refid="csContractRelationWhereCondition" />
		AND A.OPERATION_TYPE IN('0','1','2') 
	</select>
</mapper>
