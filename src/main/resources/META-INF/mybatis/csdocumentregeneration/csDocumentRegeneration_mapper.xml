<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsDocumentRegenerationDao">

	<sql id="csDocumentRegenerationWhereCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" regeneration_date  != null  and  regeneration_date  != ''  "><![CDATA[ AND A.REGENERATION_DATE = #{regeneration_date} ]]></if>
		<if test=" reg_content  != null  and  reg_content  != ''  "><![CDATA[ AND A.REG_CONTENT = #{reg_content} ]]></if>
		<if test=" reg_type != null and reg_type != ''  "><![CDATA[ AND A.REG_TYPE = #{reg_type} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsDocumentRegenerationByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>	
	<sql id="queryCsDocumentRegenerationByRegTypeCondition">
		<if test=" reg_type != null and reg_type != '' "><![CDATA[ AND A.REG_TYPE = #{reg_type} ]]></if>
	</sql>	


<!-- 修改操作 -->
	<update id="updateCsDocumentRegeneration" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_DOCUMENT_REGENERATION ]]>
		<set>
		<trim suffixOverrides=",">
			LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    REGENERATION_DATE = #{regeneration_date, jdbcType=DATE} ,
		    REG_CONTENT = #{reg_content, jdbcType=VARCHAR} ,
			REG_TYPE = #{reg_type, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE ,
			UPDATE_BY = #{update_by, jdbcType=NUMERIC},
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsDocumentRegeneration" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,A.REGENERATION_DATE, A.REG_CONTENT, A.REG_TYPE, 
			A.ACCEPT_CODE, A.UPDATE_TIME,A.UPDATE_TIMESTAMP,A.INSERT_TIME,A.INSERT_TIMESTAMP FROM APP___PAS__DBUSER.T_CS_DOCUMENT_REGENERATION A WHERE 1 = 1  ]]>
		<include refid="csDocumentRegenerationWhereCondition" />
	</select>
	
	
	<select id="findAllCsDocumentRegeneration" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,A.REGENERATION_DATE, A.REG_CONTENT, A.REG_TYPE, 
			A.ACCEPT_CODE, A.UPDATE_TIME,A.UPDATE_TIMESTAMP,A.INSERT_TIME,A.INSERT_TIMESTAMP FROM APP___PAS__DBUSER.T_CS_DOCUMENT_REGENERATION A WHERE 1 = 1  ]]>
		<include refid="csDocumentRegenerationWhereCondition" />
	</select>
	
	<select id="findAllCsDocumentRegenerationRorMod" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select cdr.list_id,cdr.accept_code,cdr.reg_type,cdr.REGENERATION_DATE,cdr.reg_content from dev_pas.t_cs_accept_change cac,dev_pas.t_cs_document_regeneration cdr
		where cdr.accept_code =  cac.accept_code and cdr.reg_type = '1' and mod(cac.accept_id,#{modNum}) = #{start}  order by cac.accept_id
		]]>
	</select>
	
</mapper>
