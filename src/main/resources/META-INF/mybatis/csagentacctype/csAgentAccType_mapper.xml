<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsAgentAccTypeDao">
<!--
	<sql id="CUS_csAgentAccTypeWhereCondition">
		<if test=" type_name != null and type_name != ''  "><![CDATA[ AND A.TYPE_NAME = #{type_name} ]]></if>
		<if test=" type_code != null and type_code != ''  "><![CDATA[ AND A.TYPE_CODE = #{type_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsAgentAccTypeByTypeCodeCondition">
		<if test=" type_code != null and type_code != '' "><![CDATA[ AND A.TYPE_CODE = #{type_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsAgentAccType"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT S_RISK_LEVEL_CONFIG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_CS_AGENT_ACC_TYPE(
				TYPE_NAME, TYPE_CODE ) 
			VALUES (
				#{type_name, jdbcType=VARCHAR}, #{type_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsAgentAccType" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CS_AGENT_ACC_TYPE WHERE TYPE_CODE = #{type_code}  ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsAgentAccType" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_CS_AGENT_ACC_TYPE ]]>
		<set>
		<trim suffixOverrides=",">
			TYPE_NAME = #{type_name, jdbcType=VARCHAR} 

		</trim>
		</set>
		<![CDATA[ WHERE  TYPE_CODE = #{type_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsAgentAccTypeByTypeCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.TYPE_CODE FROM T_CS_AGENT_ACC_TYPE A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsAgentAccTypeByTypeCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsAgentAccType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.TYPE_CODE FROM T_CS_AGENT_ACC_TYPE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsAgentAccType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.TYPE_CODE FROM T_CS_AGENT_ACC_TYPE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsAgentAccTypeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CS_AGENT_ACC_TYPE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsAgentAccTypeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TYPE_NAME, B.TYPE_CODE FROM (
					SELECT ROWNUM RN, A.TYPE_NAME, A.TYPE_CODE FROM T_CS_AGENT_ACC_TYPE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
