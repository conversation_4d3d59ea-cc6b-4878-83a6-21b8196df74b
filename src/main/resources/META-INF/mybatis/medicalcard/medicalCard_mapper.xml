<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IMedicalCardDao">

	<sql id="medicalCardWhereCondition">
		<if test=" sign_affirm_code != null and sign_affirm_code != ''  "><![CDATA[ AND A.SIGN_AFFIRM_CODE = #{sign_affirm_code} ]]></if>
		<if test=" add_serious_illness_amount  != null "><![CDATA[ AND A.ADD_SERIOUS_ILLNESS_AMOUNT = #{add_serious_illness_amount} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" bank_yl_phone != null and bank_yl_phone != ''  "><![CDATA[ AND A.BANK_YL_PHONE = #{bank_yl_phone} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" drainage_agent_code != null and drainage_agent_code != ''  "><![CDATA[ AND A.DRAINAGE_AGENT_CODE = #{drainage_agent_code} ]]></if>
		<if test=" medical_insurance_number != null and medical_insurance_number != ''  "><![CDATA[ AND A.MEDICAL_INSURANCE_NUMBER = #{medical_insurance_number} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryMedicalCardByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryMedicalCardByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="queryMedicalCardByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addMedicalCard"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_MEDICAL_CARD__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_MEDICAL_CARD(
				SIGN_AFFIRM_CODE, INSERT_TIME, UPDATE_TIME, ADD_SERIOUS_ILLNESS_AMOUNT, APPLY_CODE, BANK_YL_PHONE, INSERT_TIMESTAMP, 
				POLICY_CODE, DRAINAGE_AGENT_CODE, UPDATE_BY, MEDICAL_INSURANCE_NUMBER, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, 
				POLICY_ID ) 
			VALUES (
				#{sign_affirm_code, jdbcType=VARCHAR}, SYSDATE , SYSDATE , #{add_serious_illness_amount, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{bank_yl_phone, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{policy_code, jdbcType=VARCHAR} , #{drainage_agent_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{medical_insurance_number, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteMedicalCard" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_MEDICAL_CARD WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateMedicalCard" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_MEDICAL_CARD ]]>
		<set>
		<trim suffixOverrides=",">
			SIGN_AFFIRM_CODE = #{sign_affirm_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ADD_SERIOUS_ILLNESS_AMOUNT = #{add_serious_illness_amount, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			BANK_YL_PHONE = #{bank_yl_phone, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			DRAINAGE_AGENT_CODE = #{drainage_agent_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			MEDICAL_INSURANCE_NUMBER = #{medical_insurance_number, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findMedicalCardByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SIGN_AFFIRM_CODE, A.ADD_SERIOUS_ILLNESS_AMOUNT, A.APPLY_CODE, A.BANK_YL_PHONE, 
			A.POLICY_CODE, A.DRAINAGE_AGENT_CODE, A.MEDICAL_INSURANCE_NUMBER, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_MEDICAL_CARD A WHERE 1 = 1  ]]>
		<include refid="queryMedicalCardByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findMedicalCardByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SIGN_AFFIRM_CODE, A.ADD_SERIOUS_ILLNESS_AMOUNT, A.APPLY_CODE, A.BANK_YL_PHONE, 
			A.POLICY_CODE, A.DRAINAGE_AGENT_CODE, A.MEDICAL_INSURANCE_NUMBER, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_MEDICAL_CARD A WHERE 1 = 1  ]]>
		<include refid="queryMedicalCardByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findMedicalCardByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SIGN_AFFIRM_CODE, A.ADD_SERIOUS_ILLNESS_AMOUNT, A.APPLY_CODE, A.BANK_YL_PHONE, 
			A.POLICY_CODE, A.DRAINAGE_AGENT_CODE, A.MEDICAL_INSURANCE_NUMBER, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_MEDICAL_CARD A WHERE 1 = 1  ]]>
		<include refid="queryMedicalCardByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapMedicalCard" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SIGN_AFFIRM_CODE, A.ADD_SERIOUS_ILLNESS_AMOUNT, A.APPLY_CODE, A.BANK_YL_PHONE, 
			A.POLICY_CODE, A.DRAINAGE_AGENT_CODE, A.MEDICAL_INSURANCE_NUMBER, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_MEDICAL_CARD A WHERE ROWNUM <=  1000  ]]>
		<include refid="medicalCardWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllMedicalCard" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SIGN_AFFIRM_CODE, A.ADD_SERIOUS_ILLNESS_AMOUNT, A.APPLY_CODE, A.BANK_YL_PHONE, 
			A.POLICY_CODE, A.DRAINAGE_AGENT_CODE, A.MEDICAL_INSURANCE_NUMBER, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_MEDICAL_CARD A WHERE ROWNUM <=  1000  ]]>
		<include refid="medicalCardWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findMedicalCardTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_MEDICAL_CARD A WHERE 1 = 1  ]]>
		<include refid="medicalCardWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryMedicalCardForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SIGN_AFFIRM_CODE, B.ADD_SERIOUS_ILLNESS_AMOUNT, B.APPLY_CODE, B.BANK_YL_PHONE, 
			B.POLICY_CODE, B.DRAINAGE_AGENT_CODE, B.MEDICAL_INSURANCE_NUMBER, B.LIST_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.SIGN_AFFIRM_CODE, A.ADD_SERIOUS_ILLNESS_AMOUNT, A.APPLY_CODE, A.BANK_YL_PHONE, 
			A.POLICY_CODE, A.DRAINAGE_AGENT_CODE, A.MEDICAL_INSURANCE_NUMBER, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_MEDICAL_CARD A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="medicalCardWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
