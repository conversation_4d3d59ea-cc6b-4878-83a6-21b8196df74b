<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsEncryptionCfgDao">

	<sql id="CUS_csEncryptionCfgWhereCondition">
		<if test=" private_id != null and private_id != ''  "><![CDATA[ AND A.PRIVATE_ID = #{private_id} ]]></if>
		<if test=" public_id != null and public_id != ''  "><![CDATA[ AND A.PUBLIC_ID = #{public_id} ]]></if>
		<if test=" version != null and version != ''  "><![CDATA[ AND A.VERSION = #{version} ]]></if>
		<if test=" data_provider != null and data_provider != ''  "><![CDATA[ AND A.DATA_PROVIDER = #{data_provider} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" method != null and method != ''  "><![CDATA[ AND A.METHOD = #{method} ]]></if>
		<if test=" appId != null and appId != ''  "><![CDATA[ AND A.APPID = #{appId} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsEncryptionCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsEncryptionCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_ENCRYPTION_CFG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG(
				INSERT_TIME, PRIVATE_ID, PUBLIC_ID, UPDATE_TIME, INSERT_TIMESTAMP, VERSION, APPID, UPDATE_BY, 
				DATA_PROVIDER, LIST_ID, BUSINESS_TYPE, UPDATE_TIMESTAMP, METHOD, INSERT_BY ) 
			VALUES (
				SYSDATE, #{private_id, jdbcType=VARCHAR} , #{public_id, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP, #{version, jdbcType=VARCHAR} , #{appId, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{data_provider, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{business_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{method, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsEncryptionCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG WHERE 1 = 1 ]]>
		<include refid="CUS_csEncryptionCfgWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsEncryptionCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG ]]>
		<set>
		<trim suffixOverrides=",">
			PRIVATE_ID = #{private_id, jdbcType=VARCHAR} ,
			PUBLIC_ID = #{public_id, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			VERSION = #{version, jdbcType=VARCHAR} ,
			APPID = #{appId, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			DATA_PROVIDER = #{data_provider, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			BUSINESS_TYPE = #{business_type, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			METHOD = #{method, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  1 = 1 ]]>
		<include refid="CUS_csEncryptionCfgWhereCondition" />
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsEncryptionCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIVATE_ID, A.PUBLIC_ID, A.VERSION, A.APPID, 
			A.DATA_PROVIDER, A.LIST_ID, A.BUSINESS_TYPE, A.METHOD FROM APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsEncryptionCfgByListIdCondition" />
	</select>

	<!-- 查询单个操作 -->
	<select id="CUS_findCsEncryptionCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIVATE_ID, A.PUBLIC_ID, A.VERSION, A.APPID,
			A.DATA_PROVIDER, A.LIST_ID, A.BUSINESS_TYPE, A.METHOD FROM APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_csEncryptionCfgWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsEncryptionCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIVATE_ID, A.PUBLIC_ID, A.VERSION, A.APPID, 
			A.DATA_PROVIDER, A.LIST_ID, A.BUSINESS_TYPE, A.METHOD FROM APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csEncryptionCfgWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsEncryptionCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIVATE_ID, A.PUBLIC_ID, A.VERSION, A.APPID, 
			A.DATA_PROVIDER, A.LIST_ID, A.BUSINESS_TYPE, A.METHOD FROM APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csEncryptionCfgWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsEncryptionCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_csEncryptionCfgWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsEncryptionCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRIVATE_ID, B.PUBLIC_ID, B.VERSION, B.APPID, 
			B.DATA_PROVIDER, B.LIST_ID, B.BUSINESS_TYPE, B.METHOD FROM (
					SELECT ROWNUM RN, A.PRIVATE_ID, A.PUBLIC_ID, A.VERSION, A.APPID, 
			A.DATA_PROVIDER, A.LIST_ID, A.BUSINESS_TYPE, A.METHOD FROM APP___PAS__DBUSER.T_CS_ENCRYPTION_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_csEncryptionCfgWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
