<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ITaxCustomerInfoDao">
	<sql id="PA_taxCustomerInfoWhereCondition">
		<if test=" tax_district != null and tax_district != ''  "><![CDATA[ AND A.TAX_DISTRICT = #{tax_district} ]]></if>
		<if test=" retirement_age  != null "><![CDATA[ AND A.RETIREMENT_AGE = #{retirement_age} ]]></if>
		<if test=" bus_sre_dept_code != null and bus_sre_dept_code != ''  "><![CDATA[ AND A.BUS_SRE_DEPT_CODE = #{bus_sre_dept_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" insured_type != null and insured_type != ''  "><![CDATA[ AND A.INSURED_TYPE = #{insured_type} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" tax_number != null and tax_number != ''  "><![CDATA[ AND A.TAX_NUMBER = #{tax_number} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryTaxCustomerInfoByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="PA_queryTaxCustomerInfoByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_queryTaxCustomerInfoByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryTaxCustomerInfoByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addTaxCustomerInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TAX_CUSTOMER_INFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO(
				TAX_DISTRICT, INSERT_TIME, RETIREMENT_AGE, BUS_SRE_DEPT_CODE, CUSTOMER_ID, UPDATE_TIME, APPLY_CODE, 
				INSERT_TIMESTAMP, INSURED_TYPE, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, 
				POLICY_ID, TAX_NUMBER ) 
			VALUES (
				#{tax_district, jdbcType=VARCHAR}, SYSDATE , #{retirement_age, jdbcType=NUMERIC} , #{bus_sre_dept_code, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{apply_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{insured_type, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} , #{tax_number, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteTaxCustomerInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO WHERE LIST_ID = #{list_id, jdbcType=NUMERIC}]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateTaxCustomerInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			TAX_DISTRICT = #{tax_district, jdbcType=VARCHAR} ,
		    RETIREMENT_AGE = #{retirement_age, jdbcType=NUMERIC} ,
			BUS_SRE_DEPT_CODE = #{bus_sre_dept_code, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			INSURED_TYPE = #{insured_type, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			TAX_NUMBER = #{tax_number, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id, jdbcType=NUMERIC} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findTaxCustomerInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxCustomerInfoByApplyCodeCondition" />
	</select>
	
	<select id="PA_findTaxCustomerInfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxCustomerInfoByCustomerIdCondition" />
	</select>
	
	<select id="PA_findTaxCustomerInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxCustomerInfoByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findTaxCustomerInfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxCustomerInfoByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapTaxCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_taxCustomerInfoWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllTaxCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_taxCustomerInfoWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findTaxCustomerInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_taxCustomerInfoWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryTaxCustomerInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TAX_DISTRICT, B.RETIREMENT_AGE, B.BUS_SRE_DEPT_CODE, B.CUSTOMER_ID, B.APPLY_CODE, 
			B.INSURED_TYPE, B.POLICY_CODE, B.LIST_ID, 
			B.POLICY_ID, B.TAX_NUMBER FROM (
					SELECT ROWNUM RN, A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_taxCustomerInfoWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
		<!-- 查询单条 -->
	<select id="PA_findTaxCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TAX_DISTRICT, A.RETIREMENT_AGE, A.BUS_SRE_DEPT_CODE, A.CUSTOMER_ID, A.APPLY_CODE, 
			A.INSURED_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID, A.TAX_NUMBER FROM APP___PAS__DBUSER.T_TAX_CUSTOMER_INFO A WHERE 1=1   ]]>
		<include refid="PA_taxCustomerInfoWhereCondition" />
	</select>
</mapper>
