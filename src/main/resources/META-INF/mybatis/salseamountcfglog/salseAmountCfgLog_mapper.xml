<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISalseAmountCfgLogDao">

	<sql id="PA_salseAmountCfgLogWhereCondition">
		<if test=" operate_type != null and operate_type != ''  "><![CDATA[ AND A.OPERATE_TYPE = #{operate_type} ]]></if>
		<if test=" start_calc_time  != null  and  start_calc_time  != ''  "><![CDATA[ AND A.START_CALC_TIME = #{start_calc_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" modify_date  != null  and  modify_date  != ''  "><![CDATA[ AND A.MODIFY_DATE = #{modify_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_limit_amount  != null "><![CDATA[ AND A.SALE_LIMIT_AMOUNT = #{sale_limit_amount} ]]></if>
		<if test=" start_calc_date  != null  and  start_calc_date  != ''  "><![CDATA[ AND A.START_CALC_DATE = #{start_calc_date} ]]></if>
		<if test=" balance_amount  != null "><![CDATA[ AND A.BALANCE_AMOUNT = #{balance_amount} ]]></if>
		<if test=" warn_mail_flag != null and warn_mail_flag != ''  "><![CDATA[ AND A.WARN_MAIL_FLAG = #{warn_mail_flag} ]]></if>
		<if test=" open_flag  != null "><![CDATA[ AND A.OPEN_FLAG = #{open_flag} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" validate_flag  != null "><![CDATA[ AND A.VALIDATE_FLAG = #{validate_flag} ]]></if>
		<if test=" quota_warn_ratio  != null "><![CDATA[ AND A.QUOTA_WARN_RATIO = #{quota_warn_ratio} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" cur_amount  != null "><![CDATA[ AND A.CUR_AMOUNT = #{cur_amount} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySalseAmountCfgLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_querySalseAmountCfgLogByCfgIdCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSalseAmountCfgLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_SALSE_AMOUNT_CFG_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG(
				OPERATE_TYPE, START_CALC_TIME, BUSI_PROD_CODE,
				MODIFY_DATE,ORGAN_CODE, INSERT_TIMESTAMP, 
				]]><if test="channel_type != null and channel_type != ''">CHANNEL_TYPE, </if><![CDATA[
				UPDATE_BY, SALE_LIMIT_AMOUNT, START_CALC_DATE, BALANCE_AMOUNT, 
				]]><if test="warn_mail_flag != null and warn_mail_flag != ''"> WARN_MAIL_FLAG,</if><![CDATA[
				OPEN_FLAG, INSERT_TIME, UPDATE_TIME, INPUT_DATE, VALIDATE_FLAG, 
				]]><if test="quota_warn_ratio != null and quota_warn_ratio != ''"> QUOTA_WARN_RATIO,</if><![CDATA[
				LOG_ID, CUR_AMOUNT, OPERATOR_BY, CFG_ID, 
				]]><if test="bank_code != null and bank_code != ''">BANK_CODE, </if><![CDATA[
				UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{operate_type, jdbcType=VARCHAR}, #{start_calc_time, jdbcType=DATE} , #{busi_prod_code, jdbcType=VARCHAR} , 
				CURRENT_TIMESTAMP,#{organ_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, 
				]]><if test="channel_type != null and channel_type != ''">#{channel_type, jdbcType=VARCHAR} , </if><![CDATA[
				#{update_by, jdbcType=NUMERIC} , #{sale_limit_amount, jdbcType=NUMERIC} , #{start_calc_date, jdbcType=DATE} , #{balance_amount, jdbcType=NUMERIC} , 
				]]><if test="warn_mail_flag != null and warn_mail_flag != ''">  #{warn_mail_flag, jdbcType=VARCHAR} ,</if><![CDATA[
				#{open_flag, jdbcType=NUMERIC} , SYSDATE 
				, SYSDATE , CURRENT_TIMESTAMP , #{validate_flag, jdbcType=NUMERIC} , 
				]]><if test="quota_warn_ratio != null and quota_warn_ratio != ''"> #{quota_warn_ratio, jdbcType=NUMERIC} ,</if><![CDATA[
				#{log_id, jdbcType=NUMERIC} , #{cur_amount, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} 
				, #{cfg_id, jdbcType=NUMERIC} , 
				]]><if test="bank_code != null and bank_code != ''"> #{bank_code, jdbcType=VARCHAR} ,</if><![CDATA[ 
				CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSalseAmountCfgLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSalseAmountCfgLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			OPERATE_TYPE = #{operate_type, jdbcType=VARCHAR} ,
		    START_CALC_TIME = #{start_calc_time, jdbcType=DATE} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    MODIFY_DATE = #{modify_date, jdbcType=DATE} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SALE_LIMIT_AMOUNT = #{sale_limit_amount, jdbcType=NUMERIC} ,
		    START_CALC_DATE = #{start_calc_date, jdbcType=DATE} ,
		    BALANCE_AMOUNT = #{balance_amount, jdbcType=NUMERIC} ,
			WARN_MAIL_FLAG = #{warn_mail_flag, jdbcType=VARCHAR} ,
		    OPEN_FLAG = #{open_flag, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    INPUT_DATE = #{input_date, jdbcType=DATE} ,
		    VALIDATE_FLAG = #{validate_flag, jdbcType=NUMERIC} ,
		    QUOTA_WARN_RATIO = #{quota_warn_ratio, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    CUR_AMOUNT = #{cur_amount, jdbcType=NUMERIC} ,
		    OPERATOR_BY = #{operator_by, jdbcType=NUMERIC} ,
		    CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSalseAmountCfgLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_TYPE, A.START_CALC_TIME, A.BUSI_PROD_CODE, A.MODIFY_DATE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
			A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT, A.WARN_MAIL_FLAG, A.OPEN_FLAG, 
			A.INPUT_DATE, A.VALIDATE_FLAG, A.QUOTA_WARN_RATIO, A.LOG_ID, A.CUR_AMOUNT, A.OPERATOR_BY, 
			A.CFG_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySalseAmountCfgLogByLogIdCondition" />
	</select>
	
	<select id="PA_findSalseAmountCfgLogByCfgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_TYPE, A.START_CALC_TIME, A.BUSI_PROD_CODE, A.MODIFY_DATE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
			A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT, A.WARN_MAIL_FLAG, A.OPEN_FLAG, 
			A.INPUT_DATE, A.VALIDATE_FLAG, A.QUOTA_WARN_RATIO, A.LOG_ID, A.CUR_AMOUNT, A.OPERATOR_BY, 
			A.CFG_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_querySalseAmountCfgLogByCfgIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSalseAmountCfgLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_TYPE, A.START_CALC_TIME, A.BUSI_PROD_CODE, A.MODIFY_DATE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
			A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT, A.WARN_MAIL_FLAG, A.OPEN_FLAG, 
			A.INPUT_DATE, A.VALIDATE_FLAG, A.QUOTA_WARN_RATIO, A.LOG_ID, A.CUR_AMOUNT, A.OPERATOR_BY, 
			A.CFG_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_salseAmountCfgLogWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSalseAmountCfgLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_TYPE, A.START_CALC_TIME, A.BUSI_PROD_CODE, A.MODIFY_DATE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
			A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT, A.WARN_MAIL_FLAG, A.OPEN_FLAG, 
			A.INPUT_DATE, A.VALIDATE_FLAG, A.QUOTA_WARN_RATIO, A.LOG_ID, A.CUR_AMOUNT, A.OPERATOR_BY, 
			A.CFG_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_salseAmountCfgLogWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSalseAmountCfgLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_salseAmountCfgLogWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySalseAmountCfgLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATE_TYPE, B.START_CALC_TIME, B.BUSI_PROD_CODE, B.MODIFY_DATE, B.ORGAN_CODE, B.CHANNEL_TYPE, 
			B.SALE_LIMIT_AMOUNT, B.START_CALC_DATE, B.BALANCE_AMOUNT, B.WARN_MAIL_FLAG, B.OPEN_FLAG, 
			B.INPUT_DATE, B.VALIDATE_FLAG, B.QUOTA_WARN_RATIO, B.LOG_ID, B.CUR_AMOUNT, B.OPERATOR_BY, 
			B.CFG_ID, B.BANK_CODE FROM (
					SELECT ROWNUM RN, A.OPERATE_TYPE, A.START_CALC_TIME, A.BUSI_PROD_CODE, A.MODIFY_DATE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
			A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT, A.WARN_MAIL_FLAG, A.OPEN_FLAG, 
			A.INPUT_DATE, A.VALIDATE_FLAG, A.QUOTA_WARN_RATIO, A.LOG_ID, A.CUR_AMOUNT, A.OPERATOR_BY, 
			A.CFG_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_salseAmountCfgLogWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="findAllSalseAmountCfgLogPageTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_salseAmountCfgLogLikeOrganCode" />
	</select>

	<!-- 分页查询操作 -->
	<select id="findAllSalseAmountCfgLogPageInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATE_TYPE, B.START_CALC_TIME, B.BUSI_PROD_CODE, B.MODIFY_DATE, B.ORGAN_CODE, B.CHANNEL_TYPE, 
			B.SALE_LIMIT_AMOUNT, B.START_CALC_DATE, B.BALANCE_AMOUNT, B.WARN_MAIL_FLAG, B.OPEN_FLAG, 
			B.INPUT_DATE, B.VALIDATE_FLAG, B.QUOTA_WARN_RATIO, B.LOG_ID, B.CUR_AMOUNT, B.OPERATOR_BY, 
			B.CFG_ID, B.BANK_CODE FROM (
					SELECT ROWNUM RN, A.OPERATE_TYPE, A.START_CALC_TIME, A.BUSI_PROD_CODE, A.MODIFY_DATE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
			A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT, A.WARN_MAIL_FLAG, A.OPEN_FLAG, 
			A.INPUT_DATE, A.VALIDATE_FLAG, A.QUOTA_WARN_RATIO, A.LOG_ID, A.CUR_AMOUNT, A.OPERATOR_BY, 
			A.CFG_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_salseAmountCfgLogLikeOrganCode" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<sql id="PA_salseAmountCfgLogLikeOrganCode">
		<if test=" operate_type != null and operate_type != ''  "><![CDATA[ AND A.OPERATE_TYPE = #{operate_type} ]]></if>
		<if test=" start_calc_time  != null  and  start_calc_time  != ''  "><![CDATA[ AND A.START_CALC_TIME = #{start_calc_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" modify_date  != null  and  modify_date  != ''  "><![CDATA[ AND A.MODIFY_DATE = #{modify_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%') ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_limit_amount  != null "><![CDATA[ AND A.SALE_LIMIT_AMOUNT = #{sale_limit_amount} ]]></if>
		<if test=" start_calc_date  != null  and  start_calc_date  != ''  "><![CDATA[ AND A.START_CALC_DATE = #{start_calc_date} ]]></if>
		<if test=" balance_amount  != null "><![CDATA[ AND A.BALANCE_AMOUNT = #{balance_amount} ]]></if>
		<if test=" warn_mail_flag != null and warn_mail_flag != ''  "><![CDATA[ AND A.WARN_MAIL_FLAG = #{warn_mail_flag} ]]></if>
		<if test=" open_flag  != null "><![CDATA[ AND A.OPEN_FLAG = #{open_flag} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" validate_flag  != null "><![CDATA[ AND A.VALIDATE_FLAG = #{validate_flag} ]]></if>
		<if test=" quota_warn_ratio  != null "><![CDATA[ AND A.QUOTA_WARN_RATIO = #{quota_warn_ratio} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" cur_amount  != null "><![CDATA[ AND A.CUR_AMOUNT = #{cur_amount} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
	</sql>
	
	
	<!-- excel导出使用 -->
	<select id="findAllSalseAmountCfgLogListLikeOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT ROWNUM RN, A.OPERATE_TYPE, A.START_CALC_TIME, A.BUSI_PROD_CODE, A.MODIFY_DATE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
					(SELECT T.SALES_CHANNEL_NAME FROM APP___PAS__DBUSER.T_SALES_CHANNEL T WHERE T.SALES_CHANNEL_CODE = A.CHANNEL_TYPE AND ROWNUM = 1) as channel_name,
					(SELECT T.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T WHERE T.ORGAN_CODE = A.ORGAN_CODE AND ROWNUM = 1) AS organ_name,
					(SELECT T.bank_name FROM APP___PAS__DBUSER.T_BANK T WHERE T.bank_code = A.BANK_CODE and rownum = 1) as bank_name, 
			A.SALE_LIMIT_AMOUNT, A.START_CALC_DATE, A.BALANCE_AMOUNT, A.WARN_MAIL_FLAG, A.OPEN_FLAG, 
			A.INPUT_DATE, A.VALIDATE_FLAG, A.QUOTA_WARN_RATIO, A.LOG_ID, A.CUR_AMOUNT, A.OPERATOR_BY, 
			A.CFG_ID, A.BANK_CODE FROM APP___PAS__DBUSER.T_SALSE_AMOUNT_CFG_LOG A WHERE 1=1]]>
		<include refid="PA_salseAmountCfgLogLikeOrganCode" />
	</select>
</mapper>
