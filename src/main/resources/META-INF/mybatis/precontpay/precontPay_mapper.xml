<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPrecontPayDao">

	<sql id="PAS_precontPayWhereCondition">
		<if test=" precont_status != null and precont_status != ''  "><![CDATA[ AND A.PRECONT_STATUS = #{precont_status} ]]></if>
		<if test=" new_instalment_amount  != null "><![CDATA[ AND A.NEW_INSTALMENT_AMOUNT = #{new_instalment_amount} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" precont_id  != null "><![CDATA[ AND A.PRECONT_ID = #{precont_id} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" new_plan_freq != null and new_plan_freq != ''  "><![CDATA[ AND A.NEW_PLAN_FREQ = #{new_plan_freq} ]]></if>
		<if test=" old_plan_freq != null and old_plan_freq != ''  "><![CDATA[ AND A.OLD_PLAN_FREQ = #{old_plan_freq} ]]></if>
		<if test=" bacl_accept_code != null and bacl_accept_code != ''  "><![CDATA[ AND A.BACL_ACCEPT_CODE = #{bacl_accept_code} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" process_time  != null  and  process_time  != ''  "><![CDATA[ AND A.PROCESS_TIME = #{process_time} ]]></if>
		<if test=" precont_time  != null  and  precont_time  != ''  "><![CDATA[ AND A.PRECONT_TIME = #{precont_time} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" old_instalment_amount  != null "><![CDATA[ AND A.OLD_INSTALMENT_AMOUNT = #{old_instalment_amount} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryPrecontPayByPrecontIdCondition">
		<if test=" precont_id  != null "><![CDATA[ AND A.PRECONT_ID = #{precont_id} ]]></if>
	</sql>	
	<sql id="PAS_queryPrecontPayByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PAS_queryPrecontPayByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PAS_queryPrecontPayByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addPrecontPay"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="precont_id">
			SELECT APP___PAS__DBUSER.S_PRECONT_PAY__PRECONT_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PRECONT_PAY(
				PRECONT_STATUS, NEW_INSTALMENT_AMOUNT, PLAN_ID, INSERT_TIME, UPDATE_TIME, ITEM_ID, PRECONT_ID, 
				ACCEPT_CODE, INSERT_TIMESTAMP, NEW_PLAN_FREQ, UPDATE_BY, OLD_PLAN_FREQ, BACL_ACCEPT_CODE, CHANGE_ID, 
				PROCESS_TIME, PRECONT_TIME, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, BUSI_ITEM_ID, OLD_INSTALMENT_AMOUNT, 
				POLICY_ID ) 
			VALUES (
				#{precont_status, jdbcType=VARCHAR}, #{new_instalment_amount, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{precont_id, jdbcType=NUMERIC} 
				, #{accept_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{new_plan_freq, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{old_plan_freq, jdbcType=VARCHAR} , #{bacl_accept_code, jdbcType=VARCHAR} , #{change_id, jdbcType=NUMERIC} 
				, #{process_time, jdbcType=DATE} , #{precont_time, jdbcType=DATE} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{old_instalment_amount, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deletePrecontPay" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PRECONT_PAY WHERE PRECONT_ID = #{precont_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updatePrecontPay" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PRECONT_PAY ]]>
		<set>
		<trim suffixOverrides=",">
			PRECONT_STATUS = #{precont_status, jdbcType=VARCHAR} ,
		    NEW_INSTALMENT_AMOUNT = #{new_instalment_amount, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    PRECONT_ID = #{precont_id, jdbcType=NUMERIC} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			NEW_PLAN_FREQ = #{new_plan_freq, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			OLD_PLAN_FREQ = #{old_plan_freq, jdbcType=VARCHAR} ,
			BACL_ACCEPT_CODE = #{bacl_accept_code, jdbcType=VARCHAR} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    PROCESS_TIME = #{process_time, jdbcType=DATE} ,
		    PRECONT_TIME = #{precont_time, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    OLD_INSTALMENT_AMOUNT = #{old_instalment_amount, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE PRECONT_ID = #{precont_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findPrecontPayByPrecontId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPrecontPayByPrecontIdCondition" />
	</select>
	
	<select id="PAS_findPrecontPayByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPrecontPayByBusiItemIdCondition" />
	</select>
	
	<select id="PAS_findPrecontPayByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPrecontPayByItemIdCondition" />
	</select>
	
	<select id="PAS_findPrecontPayByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE 1 = 1 AND A.PRECONT_STATUS = '0'  ]]>
        <if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
        <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" precont_time  != null  and  precont_time  != ''  "><![CDATA[ AND A.PRECONT_TIME = #{precont_time} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<![CDATA[ AND ROWNUM = 1 ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapPrecontPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllPrecontPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_precontPayWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findPrecontPayTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE 1 = 1  ]]>
		<include refid="PAS_precontPayWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryPrecontPayForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRECONT_STATUS, B.NEW_INSTALMENT_AMOUNT, B.PLAN_ID, B.ITEM_ID, B.PRECONT_ID, 
			B.ACCEPT_CODE, B.NEW_PLAN_FREQ, B.OLD_PLAN_FREQ, B.BACL_ACCEPT_CODE, B.CHANGE_ID, 
			B.PROCESS_TIME, B.PRECONT_TIME, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.OLD_INSTALMENT_AMOUNT, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<select id="PAS_findAllOrderByPrecontTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
						A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
						A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
						A.POLICY_ID 
				FROM APP___PAS__DBUSER.T_PRECONT_PAY A 
				WHERE A.PRECONT_STATUS = 1
           			  AND A.PLAN_ID = #{plan_id}
           			  AND A.PRECONT_STATUS = #{precont_status}
			]]>			
		 <![CDATA[  ORDER BY A.PRECONT_TIME]]>
	</select>
	
	
	<insert id="addPrecontPayForCS"  useGeneratedKeys="false" parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PRECONT_PAY(
				PRECONT_STATUS, NEW_INSTALMENT_AMOUNT, PLAN_ID, INSERT_TIME, UPDATE_TIME, ITEM_ID, PRECONT_ID, 
				ACCEPT_CODE, INSERT_TIMESTAMP, NEW_PLAN_FREQ, UPDATE_BY, OLD_PLAN_FREQ, BACL_ACCEPT_CODE, CHANGE_ID, 
				PROCESS_TIME, PRECONT_TIME, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, BUSI_ITEM_ID, OLD_INSTALMENT_AMOUNT, 
				POLICY_ID ) 
			VALUES (
				#{precont_status, jdbcType=VARCHAR}, #{new_instalment_amount, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{precont_id, jdbcType=NUMERIC} 
				, #{accept_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{new_plan_freq, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{old_plan_freq, jdbcType=VARCHAR} , #{bacl_accept_code, jdbcType=VARCHAR} , #{change_id, jdbcType=NUMERIC} 
				, #{process_time, jdbcType=DATE} , #{precont_time, jdbcType=DATE} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{old_instalment_amount, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
	
	<!-- 查询单挑操作 -->
	<select id="PAS_findPrecontPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE ROWNUM = 1  ]]>
		<include refid="PAS_precontPayWhereCondition" />
	</select>
	
	
	
	<select id="PAS_findAllPrecontPayForCs" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRECONT_STATUS, A.NEW_INSTALMENT_AMOUNT, A.PLAN_ID, A.ITEM_ID, A.PRECONT_ID, 
			A.ACCEPT_CODE, A.NEW_PLAN_FREQ, A.OLD_PLAN_FREQ, A.BACL_ACCEPT_CODE, A.CHANGE_ID, 
			A.PROCESS_TIME, A.PRECONT_TIME, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.OLD_INSTALMENT_AMOUNT, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_PRECONT_PAY A WHERE  A.PRECONT_STATUS !=2  ]]>
		<include refid="PAS_precontPayWhereCondition" />
	</select>
	
</mapper>
