<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.PolicyStatusSubmitDaoImpl">
<!--
	<sql id="PA_policyStatusSubmitWhereCondition">
		<if test=" total_policy_sequence_no != null and total_policy_sequence_no != ''  "><![CDATA[ AND A.TOTAL_POLICY_SEQUENCE_NO = #{total_policy_sequence_no} ]]></if>
		<if test=" result_message != null and result_message != ''  "><![CDATA[ AND A.RESULT_MESSAGE = #{result_message} ]]></if>
		<if test=" booking_sequence_no != null and booking_sequence_no != ''  "><![CDATA[ AND A.BOOKING_SEQUENCE_NO = #{booking_sequence_no} ]]></if>
		<if test=" task_status != null and task_status != ''  "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_status_update_type != null and policy_status_update_type != ''  "><![CDATA[ AND A.POLICY_STATUS_UPDATE_TYPE = #{policy_status_update_type} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" busi_type != null and busi_type != ''  "><![CDATA[ AND A.BUSI_TYPE = #{busi_type} ]]></if>
		<if test=" result_code != null and result_code != ''  "><![CDATA[ AND A.RESULT_CODE = #{result_code} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" endorse_sequence_no != null and endorse_sequence_no != ''  "><![CDATA[ AND A.ENDORSE_SEQUENCE_NO = #{endorse_sequence_no} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID = #{submit_id} ]]></if>
		<if test=" create_date  != null  and  create_date  != ''  "><![CDATA[ AND A.CREATE_DATE = #{create_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyStatusSubmitBySubmitIdCondition">
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID = #{submit_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyStatusSubmitByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyStatusSubmitByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyStatusSubmitByTaskStatusCondition">
		<if test=" task_status != null and task_status != '' "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicyStatusSubmit"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="submit_id">
			SELECT APP___PAS__DBUSER.S_CS_TAX_TRANS_TASK__LISID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT(
				TOTAL_POLICY_SEQUENCE_NO, RESULT_MESSAGE, INSERT_TIMESTAMP, BOOKING_SEQUENCE_NO, TASK_STATUS, UPDATE_BY, POLICY_ID, 
				POLICY_STATUS_UPDATE_TYPE, LAPSE_DATE, BUSI_TYPE, RESULT_CODE, INSERT_TIME, END_CAUSE, UPDATE_TIME, 
				LAPSE_CAUSE, ENDORSE_SEQUENCE_NO, EXPIRY_DATE, LIABILITY_STATE, POLICY_CODE, SUSPEND_DATE, SUSPEND_CAUSE, 
				SUBMIT_ID, UPDATE_TIMESTAMP, CREATE_DATE, INSERT_BY ) 
			VALUES (
				#{total_policy_sequence_no, jdbcType=VARCHAR}, #{result_message, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{booking_sequence_no, jdbcType=VARCHAR} , #{task_status, jdbcType=VARCHAR} , 0 , #{policy_id, jdbcType=NUMERIC} 
				, #{policy_status_update_type, jdbcType=VARCHAR} , #{lapse_date, jdbcType=DATE} , #{busi_type, jdbcType=VARCHAR} , #{result_code, jdbcType=VARCHAR} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , SYSDATE 
				, #{lapse_cause, jdbcType=VARCHAR} , #{endorse_sequence_no, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} 
				, #{submit_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{create_date, jdbcType=DATE} , 0 ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicyStatusSubmit" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT WHERE SUBMIT_ID = #{submit_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicyStatusSubmit" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT ]]>
		<set>
		<trim suffixOverrides=",">		    
		<if test=" result_message != null and result_message != ''  "><![CDATA[ RESULT_MESSAGE = #{result_message, jdbcType=VARCHAR} , ]]></if>
		<if test=" booking_sequence_no != null and booking_sequence_no != ''  "><![CDATA[ BOOKING_SEQUENCE_NO = #{booking_sequence_no, jdbcType=VARCHAR} , ]]></if>
		<if test=" task_status != null and task_status != ''  "><![CDATA[ TASK_STATUS = #{task_status, jdbcType=VARCHAR} , ]]></if>
		UPDATE_BY = 0 ,
		<if test=" result_code != null and result_code != ''  "><![CDATA[ RESULT_CODE = #{result_code, jdbcType=VARCHAR} , ]]></if>
		UPDATE_TIME = SYSDATE , 
		<if test=" endorse_sequence_no != null and endorse_sequence_no != ''  "><![CDATA[ ENDORSE_SEQUENCE_NO = #{endorse_sequence_no, jdbcType=VARCHAR} , ]]></if>
		UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE SUBMIT_ID = #{submit_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyStatusSubmitBySubmitId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, A.BOOKING_SEQUENCE_NO, A.TASK_STATUS, A.POLICY_ID, 
			A.POLICY_STATUS_UPDATE_TYPE, A.LAPSE_DATE, A.BUSI_TYPE, A.RESULT_CODE, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.ENDORSE_SEQUENCE_NO, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.SUBMIT_ID, A.CREATE_DATE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyStatusSubmitBySubmitIdCondition" />
		<![CDATA[ ORDER BY A.SUBMIT_ID ]]>
	</select>
	
	<select id="PA_findPolicyStatusSubmitByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, A.BOOKING_SEQUENCE_NO, A.TASK_STATUS, A.POLICY_ID, 
			A.POLICY_STATUS_UPDATE_TYPE, A.LAPSE_DATE, A.BUSI_TYPE, A.RESULT_CODE, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.ENDORSE_SEQUENCE_NO, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.SUBMIT_ID, A.CREATE_DATE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyStatusSubmitByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.SUBMIT_ID ]]>
	</select>
	
	<select id="PA_findPolicyStatusSubmitByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, A.BOOKING_SEQUENCE_NO, A.TASK_STATUS, A.POLICY_ID, 
			A.POLICY_STATUS_UPDATE_TYPE, A.LAPSE_DATE, A.BUSI_TYPE, A.RESULT_CODE, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.ENDORSE_SEQUENCE_NO, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.SUBMIT_ID, A.CREATE_DATE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyStatusSubmitByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.SUBMIT_ID ]]>
	</select>
	
	<select id="PA_findPolicyStatusSubmitByTaskStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, A.BOOKING_SEQUENCE_NO, A.TASK_STATUS, A.POLICY_ID, 
			A.POLICY_STATUS_UPDATE_TYPE, A.LAPSE_DATE, A.BUSI_TYPE, A.RESULT_CODE, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.ENDORSE_SEQUENCE_NO, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.SUBMIT_ID, A.CREATE_DATE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyStatusSubmitByTaskStatusCondition" />
		<![CDATA[ ORDER BY A.SUBMIT_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyStatusSubmit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT T.*, ROWNUM RN FROM (
          SELECT A.TOTAL_POLICY_SEQUENCE_NO,
       A.RESULT_MESSAGE,
       A.BOOKING_SEQUENCE_NO,
       A.TASK_STATUS,
       A.POLICY_ID,
       A.POLICY_STATUS_UPDATE_TYPE,
       A.LAPSE_DATE,
       A.BUSI_TYPE,
       A.RESULT_CODE,
       A.END_CAUSE,
       A.LAPSE_CAUSE,
       A.ENDORSE_SEQUENCE_NO,
       A.EXPIRY_DATE,
       A.LIABILITY_STATE,
       A.POLICY_CODE,
       A.SUSPEND_DATE,
       A.SUSPEND_CAUSE,
       A.SUBMIT_ID,
       A.CREATE_DATE,
       A.INSERT_TIME AS insert_date
  FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A
 WHERE 1 = 1 AND A.TASK_STATUS IN ('1', '3')]]>
    <if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.SUBMIT_ID , #{modnum,jdbcType=VARCHAR}) = #{start,jdbcType=VARCHAR} ]]></if>		
    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
    <if test=" create_date  != null  and  create_date  != ''  "><![CDATA[ AND A.CREATE_DATE <= #{create_date} ]]></if>
    <![CDATA[) T WHERE 1=1 ORDER BY T.SUBMIT_ID ASC ]]>       
    
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyStatusSubmit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT T.*, ROWNUM RN FROM (
          SELECT A.TOTAL_POLICY_SEQUENCE_NO,
       A.RESULT_MESSAGE,
       A.BOOKING_SEQUENCE_NO,
       A.TASK_STATUS,
       A.POLICY_ID,
       A.POLICY_STATUS_UPDATE_TYPE,
       A.LAPSE_DATE,
       A.BUSI_TYPE,
       A.RESULT_CODE,
       A.END_CAUSE,
       A.LAPSE_CAUSE,
       A.ENDORSE_SEQUENCE_NO,
       A.EXPIRY_DATE,
       A.LIABILITY_STATE,
       A.POLICY_CODE,
       A.SUSPEND_DATE,
       A.SUSPEND_CAUSE,
       A.SUBMIT_ID,
       A.CREATE_DATE,
       A.INSERT_TIME AS insert_date
  FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A
 WHERE 1 = 1 AND A.TASK_STATUS IN ('1', '3')]]>
    <if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.SUBMIT_ID , #{modnum,jdbcType=VARCHAR}) = #{start,jdbcType=VARCHAR} ]]></if>		
    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
    <if test=" create_date  != null  and  create_date  != ''  "><![CDATA[ AND A.CREATE_DATE <= #{create_date} ]]></if>
    
    <![CDATA[) T WHERE 1=1 ORDER BY T.SUBMIT_ID ASC ]]>    
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyStatusSubmitTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyStatusSubmitForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TOTAL_POLICY_SEQUENCE_NO, B.RESULT_MESSAGE, B.BOOKING_SEQUENCE_NO, B.TASK_STATUS, B.POLICY_ID, 
			B.POLICY_STATUS_UPDATE_TYPE, B.LAPSE_DATE, B.BUSI_TYPE, B.RESULT_CODE, B.END_CAUSE, 
			B.LAPSE_CAUSE, B.ENDORSE_SEQUENCE_NO, B.EXPIRY_DATE, B.LIABILITY_STATE, B.POLICY_CODE, B.SUSPEND_DATE, B.SUSPEND_CAUSE, 
			B.SUBMIT_ID, B.CREATE_DATE FROM (
					SELECT ROWNUM RN, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, A.BOOKING_SEQUENCE_NO, A.TASK_STATUS, A.POLICY_ID, 
			A.POLICY_STATUS_UPDATE_TYPE, A.LAPSE_DATE, A.BUSI_TYPE, A.RESULT_CODE, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.ENDORSE_SEQUENCE_NO, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.SUBMIT_ID, A.CREATE_DATE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SUBMIT_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
		<select id="PA_findPolicyStatusSubmit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, A.BOOKING_SEQUENCE_NO, A.TASK_STATUS, A.POLICY_ID, 
			A.POLICY_STATUS_UPDATE_TYPE, A.LAPSE_DATE, A.BUSI_TYPE, A.RESULT_CODE, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.ENDORSE_SEQUENCE_NO, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.SUBMIT_ID, A.CREATE_DATE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyStatusSubmitByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.SUBMIT_ID ]]>
	</select>
	
	
	<select id="PA_checkSequencePolicyStatusSubmit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, A.BOOKING_SEQUENCE_NO, A.TASK_STATUS, A.POLICY_ID, 
			A.POLICY_STATUS_UPDATE_TYPE, A.LAPSE_DATE, A.BUSI_TYPE, A.RESULT_CODE, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.ENDORSE_SEQUENCE_NO, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, 
			A.SUBMIT_ID, A.CREATE_DATE FROM APP___PAS__DBUSER.T_POLICY_STATUS_SUBMIT A WHERE 1 = 1  AND A.TASK_STATUS <> '5']]>
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID < #{submit_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ORDER BY A.SUBMIT_ID ]]>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="PA_findTaxPremiumPolicyTaskCountInfo" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(*) FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1=1 AND  A.POLICY_CODE = #{policy_code}
		 AND A.SEND_TYPE = '5' AND A.TASK_STATUS <> '5' ]]>
	</select>
	
</mapper>
