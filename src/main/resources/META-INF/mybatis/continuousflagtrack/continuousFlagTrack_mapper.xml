<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IContinuousFlagTrackDao">

	<sql id="CUS_continuousFlagTrackWhereCondition">
		<if test=" check_type  != null "><![CDATA[ AND A.CHECK_TYPE = #{check_type} ]]></if>
		<if test=" continuous_rules  != null "><![CDATA[ AND A.CONTINUOUS_RULES = #{continuous_rules} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" check_time  != null  and  check_time  != ''  "><![CDATA[ AND A.CHECK_TIME = #{check_time} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryContinuousFlagTrackByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="addContinuousFlagTrack"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTINUOUS_FLAG_TRACK__LI.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CONTINUOUS_FLAG_TRACK(
				INSERT_TIMESTAMP, UPDATE_BY, CHECK_TYPE, CONTINUOUS_RULES, INSERT_TIME, LIST_ID, CUSTOMER_ID, 
				CHECK_TIME, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, ACCEPT_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{check_type, jdbcType=NUMERIC} , #{continuous_rules, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} 
				, #{check_time, jdbcType=DATE} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{accept_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="deleteContinuousFlagTrack" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CONTINUOUS_FLAG_TRACK WHERE LIST_ID = #{list_id, jdbcType=NUMERIC} ]]>
	</delete>

	<!-- 删除操作（根据受理号删除） -->	
	<delete id="deleteCheckContinuousLogForAcceptCode" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CONTINUOUS_FLAG_TRACK WHERE ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ]]>
	</delete>


	<!-- 修改操作 -->
	<update id="updateContinuousFlagTrack" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CONTINUOUS_FLAG_TRACK ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHECK_TYPE = #{check_type, jdbcType=NUMERIC} ,
		    CONTINUOUS_RULES = #{continuous_rules, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    CHECK_TIME = #{check_time, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id, jdbcType=NUMERIC} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="findContinuousFlagTrackByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_TYPE, A.CONTINUOUS_RULES, A.LIST_ID, A.CUSTOMER_ID, 
			A.CHECK_TIME, A.ACCEPT_CODE FROM DEV_PAS.T_CONTINUOUS_FLAG_TRACK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryContinuousFlagTrackByListIdCondition" />
	</select>
	
	<!-- 按条件查询所有 -->	
	<select id="findAllContinuousFlagTrack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			  SELECT A.LIST_ID,
			       A.ACCEPT_CODE,
			       A.CHECK_TYPE,
			       A.CONTINUOUS_RULES,
			       A.CHECK_TIME,
			       A.CUSTOMER_ID,
			       A.Insert_By,
			       A.Insert_Time,
			       A.Insert_Timestamp,
			       A.Update_By,
			       A.Update_Time,
			       A.Update_Timestamp
			  FROM DEV_PAS.T_CONTINUOUS_FLAG_TRACK A
			 WHERE 1 = 1
		]]>
		<include refid="CUS_continuousFlagTrackWhereCondition" />
	</select>
	
	
	<!-- 查询是否在六个月内存在过校验(需要针对的是已生效的数据，中途撤销的数据不计算在内) -->	
	<select id="findAllContinuousFlagTrackByCustomerIdAndCheckTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			  SELECT A.LIST_ID,
			       A.ACCEPT_CODE,
			       A.CHECK_TYPE,
			       A.CONTINUOUS_RULES,
			       A.CHECK_TIME,
			       A.CUSTOMER_ID,
			       A.Insert_By,
			       A.Insert_Time,
			       A.Insert_Timestamp,
			       A.Update_By,
			       A.Update_Time,
			       A.Update_Timestamp
			  FROM DEV_PAS.T_CONTINUOUS_FLAG_TRACK A
			  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
			  ON AC.ACCEPT_CODE = A.ACCEPT_CODE
			 WHERE 1 = 1
			 AND AC.ACCEPT_STATUS = 18
			 AND A.CHECK_TYPE = #{check_type}
			 AND A.CUSTOMER_ID = #{customer_id}
			 AND A.CHECK_TIME >= #{check_start_time}
			 AND A.CHECK_TIME <= #{check_end_time}
		]]>
	</select>
	


	
</mapper>
