<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ITrustCompanyBeneDao">

	<sql id="trustCompanyBeneWhereCondition">
		<if test=" bene_certi_code != null and bene_certi_code != ''  "><![CDATA[ AND A.BENE_CERTI_CODE = #{bene_certi_code} ]]></if>
		<if test=" bene_certi_start_date  != null  and  bene_certi_start_date  != ''  "><![CDATA[ AND A.BENE_CERTI_START_DATE = #{bene_certi_start_date} ]]></if>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
		<if test=" bene_name != null and bene_name != ''  "><![CDATA[ AND A.BENE_NAME = #{bene_name} ]]></if>
		<if test=" bene_certi_end_date  != null  and  bene_certi_end_date  != ''  "><![CDATA[ AND A.BENE_CERTI_END_DATE = #{bene_certi_end_date} ]]></if>
		<if test=" bene_certi_type != null and bene_certi_type != ''  "><![CDATA[ AND A.BENE_CERTI_TYPE = #{bene_certi_type} ]]></if>
		<if test=" bene_address != null and bene_address != ''  "><![CDATA[ AND A.BENE_ADDRESS = #{bene_address} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryTrustCompanyBeneByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryTrustCompanyBeneByCompanyIdCondition">
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addTrustCompanyBene"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT S_TRUST_COMPANY_BENE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_TRUST_COMPANY_BENE(
				BENE_CERTI_CODE, BENE_CERTI_START_DATE, COMPANY_ID, INSERT_TIME, UPDATE_TIME, BENE_NAME, BENE_CERTI_END_DATE, 
				BENE_CERTI_TYPE, INSERT_TIMESTAMP, UPDATE_BY, BENE_ADDRESS, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{bene_certi_code, jdbcType=VARCHAR}, #{bene_certi_start_date, jdbcType=DATE} , #{company_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{bene_name, jdbcType=VARCHAR} , #{bene_certi_end_date, jdbcType=DATE} 
				, #{bene_certi_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{bene_address, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteTrustCompanyBene" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_TRUST_COMPANY_BENE WHERE   LIST_ID = #{list_id}  ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateTrustCompanyBene" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_TRUST_COMPANY_BENE ]]>
		<set>
		<trim suffixOverrides=",">
			BENE_CERTI_CODE = #{bene_certi_code, jdbcType=VARCHAR} ,
		    BENE_CERTI_START_DATE = #{bene_certi_start_date, jdbcType=DATE} ,
		    COMPANY_ID = #{company_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			BENE_NAME = #{bene_name, jdbcType=VARCHAR} ,
		    BENE_CERTI_END_DATE = #{bene_certi_end_date, jdbcType=DATE} ,
			BENE_CERTI_TYPE = #{bene_certi_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			BENE_ADDRESS = #{bene_address, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id}  ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findTrustCompanyBeneByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.BENE_ADDRESS, A.LIST_ID FROM DEV_PAS.T_TRUST_COMPANY_BENE A WHERE 1 = 1  ]]>
		<include refid="trustCompanyBeneWhereCondition" />
	</select>
	
	<select id="findTrustCompanyBeneByCompanyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.BENE_ADDRESS, A.LIST_ID FROM DEV_PAS.T_TRUST_COMPANY_BENE A WHERE 1 = 1  ]]>
		<include refid="trustCompanyBeneWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapTrustCompanyBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.BENE_ADDRESS, A.LIST_ID FROM DEV_PAS.T_TRUST_COMPANY_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="trustCompanyBeneWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllTrustCompanyBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.BENE_ADDRESS, A.LIST_ID FROM DEV_PAS.T_TRUST_COMPANY_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="trustCompanyBeneWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findTrustCompanyBeneTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_TRUST_COMPANY_BENE A WHERE 1 = 1  ]]>
		<include refid="trustCompanyBeneWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryTrustCompanyBeneForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BENE_CERTI_CODE, B.BENE_CERTI_START_DATE, B.COMPANY_ID, B.BENE_NAME, B.BENE_CERTI_END_DATE, 
			B.BENE_CERTI_TYPE, B.BENE_ADDRESS, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.BENE_ADDRESS, A.LIST_ID FROM DEV_PAS.T_TRUST_COMPANY_BENE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="trustCompanyBeneWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	 
</mapper>
