<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ITrustCompanyDao">

	<sql id="trustCompanyWhereCondition">
		<if test=" manager_country != null and manager_country != ''  "><![CDATA[ AND A.MANAGER_COUNTRY = #{manager_country} ]]></if>
		<if test=" tax_code_start_date  != null  and  tax_code_start_date  != ''  "><![CDATA[ AND A.TAX_CODE_START_DATE = #{tax_code_start_date} ]]></if>
		<if test=" operator_certi_type != null and operator_certi_type != ''  "><![CDATA[ AND A.OPERATOR_CERTI_TYPE = #{operator_certi_type} ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND A.STATE = #{state} ]]></if>
		<if test=" company_en_name != null and company_en_name != ''  "><![CDATA[ AND A.COMPANY_EN_NAME = #{company_en_name} ]]></if>
		<if test=" operator_certi_start_date  != null  and  operator_certi_start_date  != ''  "><![CDATA[ AND A.OPERATOR_CERTI_START_DATE = #{operator_certi_start_date} ]]></if>
		<if test=" manager_certi_end_date  != null  and  manager_certi_end_date  != ''  "><![CDATA[ AND A.MANAGER_CERTI_END_DATE = #{manager_certi_end_date} ]]></if>
		<if test=" tax_code_end_date  != null  and  tax_code_end_date  != ''  "><![CDATA[ AND A.TAX_CODE_END_DATE = #{tax_code_end_date} ]]></if>
		<if test=" holding_person_name != null and holding_person_name != ''  "><![CDATA[ AND A.HOLDING_PERSON_NAME = #{holding_person_name} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
		<if test=" company_holder_certi_code != null and company_holder_certi_code != ''  "><![CDATA[ AND A.COMPANY_HOLDER_CERTI_CODE = #{company_holder_certi_code} ]]></if>
		<if test=" company_oran_code != null and company_oran_code != ''  "><![CDATA[ AND A.COMPANY_ORAN_CODE = #{company_oran_code} ]]></if>
		<if test=" operator_certi_code != null and operator_certi_code != ''  "><![CDATA[ AND A.OPERATOR_CERTI_CODE = #{operator_certi_code} ]]></if>
		<if test=" company_address != null and company_address != ''  "><![CDATA[ AND A.COMPANY_ADDRESS = #{company_address} ]]></if>
		<if test=" organ_code_end_date  != null  and  organ_code_end_date  != ''  "><![CDATA[ AND A.ORGAN_CODE_END_DATE = #{organ_code_end_date} ]]></if>
		<if test=" district != null and district != ''  "><![CDATA[ AND A.DISTRICT = #{district} ]]></if>
		<if test=" manager_name != null and manager_name != ''  "><![CDATA[ AND A.MANAGER_NAME = #{manager_name} ]]></if>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
		<if test=" address != null and address != ''  "><![CDATA[ AND A.ADDRESS = #{address} ]]></if>
		<if test=" legal_person_certi_type != null and legal_person_certi_type != ''  "><![CDATA[ AND A.LEGAL_PERSON_CERTI_TYPE = #{legal_person_certi_type} ]]></if>
		<if test=" operator_name  != null  and  operator_name  != ''  "><![CDATA[ AND A.OPERATOR_NAME = #{operator_name} ]]></if>
		<if test=" licence_code_end_date  != null  and  licence_code_end_date  != ''  "><![CDATA[ AND A.LICENCE_CODE_END_DATE = #{licence_code_end_date} ]]></if>
		<if test=" business_cope != null and business_cope != ''  "><![CDATA[ AND A.BUSINESS_COPE = #{business_cope} ]]></if>
		<if test=" operator_certi_end_date  != null  and  operator_certi_end_date  != ''  "><![CDATA[ AND A.OPERATOR_CERTI_END_DATE = #{operator_certi_end_date} ]]></if>
		<if test=" organ_code_start_date  != null  and  organ_code_start_date  != ''  "><![CDATA[ AND A.ORGAN_CODE_START_DATE = #{organ_code_start_date} ]]></if>
		<if test=" legal_per_certi_start_date  != null  and  legal_per_certi_start_date  != ''  "><![CDATA[ AND A.LEGAL_PER_CERTI_START_DATE = #{legal_per_certi_start_date} ]]></if>
		<if test=" company_holder_country != null and company_holder_country != ''  "><![CDATA[ AND A.COMPANY_HOLDER_COUNTRY = #{company_holder_country} ]]></if>
		<if test=" legal_per_certi_end_date  != null  and  legal_per_certi_end_date  != ''  "><![CDATA[ AND A.LEGAL_PER_CERTI_END_DATE = #{legal_per_certi_end_date} ]]></if>
		<if test=" manager_certi_type != null and manager_certi_type != ''  "><![CDATA[ AND A.MANAGER_CERTI_TYPE = #{manager_certi_type} ]]></if>
		<if test=" manager_certi_code != null and manager_certi_code != ''  "><![CDATA[ AND A.MANAGER_CERTI_CODE = #{manager_certi_code} ]]></if>
		<if test=" company_holder_certi_type != null and company_holder_certi_type != ''  "><![CDATA[ AND A.COMPANY_HOLDER_CERTI_TYPE = #{company_holder_certi_type} ]]></if>
		<if test=" legal_person_certi_code != null and legal_person_certi_code != ''  "><![CDATA[ AND A.LEGAL_PERSON_CERTI_CODE = #{legal_person_certi_code} ]]></if>
		<if test=" holder_certi_start_date  != null  and  holder_certi_start_date  != ''  "><![CDATA[ AND A.HOLDER_CERTI_START_DATE = #{holder_certi_start_date} ]]></if>
		<if test=" post_code != null and post_code != ''  "><![CDATA[ AND A.POST_CODE = #{post_code} ]]></if>
		<if test=" manager_certi_start_date  != null  and  manager_certi_start_date  != ''  "><![CDATA[ AND A.MANAGER_CERTI_START_DATE = #{manager_certi_start_date} ]]></if>
		<if test=" tax_code != null and tax_code != ''  "><![CDATA[ AND A.TAX_CODE = #{tax_code} ]]></if>
		<if test=" holder_certi_end_date  != null  and  holder_certi_end_date  != ''  "><![CDATA[ AND A.HOLDER_CERTI_END_DATE = #{holder_certi_end_date} ]]></if>
		<if test=" licence_code_start_date  != null  and  licence_code_start_date  != ''  "><![CDATA[ AND A.LICENCE_CODE_START_DATE = #{licence_code_start_date} ]]></if>
		<if test=" busi_licence_code != null and busi_licence_code != ''  "><![CDATA[ AND A.BUSI_LICENCE_CODE = #{busi_licence_code} ]]></if>
		<if test=" city != null and city != ''  "><![CDATA[ AND A.CITY = #{city} ]]></if>
		<if test=" legal_person_name != null and legal_person_name != ''  "><![CDATA[ AND A.LEGAL_PERSON_NAME = #{legal_person_name} ]]></if>
		<if test=" legal_person_country != null and legal_person_country != ''  "><![CDATA[ AND A.LEGAL_PERSON_COUNTRY = #{legal_person_country} ]]></if>
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" ilog_type != null and ilog_type != ''  "><![CDATA[ AND A.ILOG_TYPE = #{ilog_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryTrustCompanyByCompanyIdCondition">
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
	</sql>	
	<sql id="queryTrustCompanyByCompanyNameCondition">
		<if test=" company_name != null and company_name != '' "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
	</sql>	
	<sql id="queryTrustCompanyByCompanyOranCodeCondition">
		<if test=" company_oran_code != null and company_oran_code != '' "><![CDATA[ AND A.COMPANY_ORAN_CODE = #{company_oran_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addTrustCompany"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT S_TRUST_COMPANY.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO dev_pas.T_TRUST_COMPANY(
				MANAGER_COUNTRY, TAX_CODE_START_DATE, OPERATOR_CERTI_TYPE, STATE, COMPANY_EN_NAME, OPERATOR_CERTI_START_DATE, MANAGER_CERTI_END_DATE, 
				TAX_CODE_END_DATE, HOLDING_PERSON_NAME, COMPANY_NAME, COMPANY_HOLDER_CERTI_CODE, INSERT_TIMESTAMP, UPDATE_BY, COMPANY_ORAN_CODE, 
				OPERATOR_CERTI_CODE, COMPANY_ADDRESS, ORGAN_CODE_END_DATE, DISTRICT, MANAGER_NAME, COMPANY_ID, INSERT_TIME, 
				ADDRESS, LEGAL_PERSON_CERTI_TYPE, UPDATE_TIME, OPERATOR_NAME, LICENCE_CODE_END_DATE, BUSINESS_COPE, OPERATOR_CERTI_END_DATE, 
				ORGAN_CODE_START_DATE, LEGAL_PER_CERTI_START_DATE, COMPANY_HOLDER_COUNTRY, LEGAL_PER_CERTI_END_DATE, MANAGER_CERTI_TYPE, MANAGER_CERTI_CODE, COMPANY_HOLDER_CERTI_TYPE, 
				LEGAL_PERSON_CERTI_CODE, HOLDER_CERTI_START_DATE, POST_CODE, MANAGER_CERTI_START_DATE, TAX_CODE, HOLDER_CERTI_END_DATE, LICENCE_CODE_START_DATE, 
				BUSI_LICENCE_CODE, UPDATE_TIMESTAMP, CITY, LEGAL_PERSON_NAME, INSERT_BY, LEGAL_PERSON_COUNTRY,EMAIL,CUSTOMER_ID,ILOG_TYPE ) 
			VALUES (
				#{manager_country, jdbcType=VARCHAR}, #{tax_code_start_date, jdbcType=DATE} , #{operator_certi_type, jdbcType=VARCHAR} , #{state, jdbcType=VARCHAR} , #{company_en_name, jdbcType=VARCHAR} , #{operator_certi_start_date, jdbcType=DATE} , #{manager_certi_end_date, jdbcType=DATE} 
				, #{tax_code_end_date, jdbcType=DATE} , #{holding_person_name, jdbcType=VARCHAR} , #{company_name, jdbcType=VARCHAR} , #{company_holder_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{company_oran_code, jdbcType=VARCHAR} 
				, #{operator_certi_code, jdbcType=VARCHAR} , #{company_address, jdbcType=VARCHAR} , #{organ_code_end_date, jdbcType=DATE} , #{district, jdbcType=VARCHAR} , #{manager_name, jdbcType=VARCHAR} , #{company_id, jdbcType=NUMERIC} , SYSDATE 
				, #{address, jdbcType=VARCHAR} , #{legal_person_certi_type, jdbcType=VARCHAR} , SYSDATE , #{operator_name, jdbcType=VARCHAR} , #{licence_code_end_date, jdbcType=DATE} , #{business_cope, jdbcType=VARCHAR} , #{operator_certi_end_date, jdbcType=DATE} 
				, #{organ_code_start_date, jdbcType=DATE} , #{legal_per_certi_start_date, jdbcType=DATE} , #{company_holder_country, jdbcType=VARCHAR} , #{legal_per_certi_end_date, jdbcType=DATE} , #{manager_certi_type, jdbcType=VARCHAR} , #{manager_certi_code, jdbcType=VARCHAR} , #{company_holder_certi_type, jdbcType=VARCHAR} 
				, #{legal_person_certi_code, jdbcType=VARCHAR} , #{holder_certi_start_date, jdbcType=DATE} , #{post_code, jdbcType=VARCHAR} , #{manager_certi_start_date, jdbcType=DATE} , #{tax_code, jdbcType=VARCHAR} , #{holder_certi_end_date, jdbcType=DATE} , #{licence_code_start_date, jdbcType=DATE} 
				, #{busi_licence_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{city, jdbcType=VARCHAR} , #{legal_person_name, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{legal_person_country, jdbcType=VARCHAR},#{email, jdbcType=VARCHAR}, #{customer_id, jdbcType=NUMERIC}, #{ilog_type, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteTrustCompany" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM dev_pas.T_TRUST_COMPANY WHERE  company_id = #{company_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateTrustCompany" parameterType="java.util.Map">
		<![CDATA[ UPDATE dev_pas.T_TRUST_COMPANY ]]>
		<set>
		<trim suffixOverrides=",">
			MANAGER_COUNTRY = #{manager_country, jdbcType=VARCHAR} ,
		    TAX_CODE_START_DATE = #{tax_code_start_date, jdbcType=DATE} ,
			OPERATOR_CERTI_TYPE = #{operator_certi_type, jdbcType=VARCHAR} ,
			STATE = #{state, jdbcType=VARCHAR} ,
			COMPANY_EN_NAME = #{company_en_name, jdbcType=VARCHAR} ,
		    OPERATOR_CERTI_START_DATE = #{operator_certi_start_date, jdbcType=DATE} ,
		    MANAGER_CERTI_END_DATE = #{manager_certi_end_date, jdbcType=DATE} ,
		    TAX_CODE_END_DATE = #{tax_code_end_date, jdbcType=DATE} ,
			HOLDING_PERSON_NAME = #{holding_person_name, jdbcType=VARCHAR} ,
			COMPANY_NAME = #{company_name, jdbcType=VARCHAR} ,
			COMPANY_HOLDER_CERTI_CODE = #{company_holder_certi_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			COMPANY_ORAN_CODE = #{company_oran_code, jdbcType=VARCHAR} ,
			OPERATOR_CERTI_CODE = #{operator_certi_code, jdbcType=VARCHAR} ,
			COMPANY_ADDRESS = #{company_address, jdbcType=VARCHAR} ,
		    ORGAN_CODE_END_DATE = #{organ_code_end_date, jdbcType=DATE} ,
			DISTRICT = #{district, jdbcType=VARCHAR} ,
			MANAGER_NAME = #{manager_name, jdbcType=VARCHAR} ,
		    COMPANY_ID = #{company_id, jdbcType=NUMERIC} ,
			ADDRESS = #{address, jdbcType=VARCHAR} ,
			LEGAL_PERSON_CERTI_TYPE = #{legal_person_certi_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    OPERATOR_NAME = #{operator_name, jdbcType=VARCHAR} ,
		    LICENCE_CODE_END_DATE = #{licence_code_end_date, jdbcType=DATE} ,
			BUSINESS_COPE = #{business_cope, jdbcType=VARCHAR} ,
		    OPERATOR_CERTI_END_DATE = #{operator_certi_end_date, jdbcType=DATE} ,
		    ORGAN_CODE_START_DATE = #{organ_code_start_date, jdbcType=DATE} ,
		    LEGAL_PER_CERTI_START_DATE = #{legal_per_certi_start_date, jdbcType=DATE} ,
			COMPANY_HOLDER_COUNTRY = #{company_holder_country, jdbcType=VARCHAR} ,
		    LEGAL_PER_CERTI_END_DATE = #{legal_per_certi_end_date, jdbcType=DATE} ,
			MANAGER_CERTI_TYPE = #{manager_certi_type, jdbcType=VARCHAR} ,
			MANAGER_CERTI_CODE = #{manager_certi_code, jdbcType=VARCHAR} ,
			COMPANY_HOLDER_CERTI_TYPE = #{company_holder_certi_type, jdbcType=VARCHAR} ,
			LEGAL_PERSON_CERTI_CODE = #{legal_person_certi_code, jdbcType=VARCHAR} ,
		    HOLDER_CERTI_START_DATE = #{holder_certi_start_date, jdbcType=DATE} ,
			POST_CODE = #{post_code, jdbcType=VARCHAR} ,
		    MANAGER_CERTI_START_DATE = #{manager_certi_start_date, jdbcType=DATE} ,
			TAX_CODE = #{tax_code, jdbcType=VARCHAR} ,
		    HOLDER_CERTI_END_DATE = #{holder_certi_end_date, jdbcType=DATE} ,
		    LICENCE_CODE_START_DATE = #{licence_code_start_date, jdbcType=DATE} ,
			BUSI_LICENCE_CODE = #{busi_licence_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CITY = #{city, jdbcType=VARCHAR} ,
			LEGAL_PERSON_NAME = #{legal_person_name, jdbcType=VARCHAR} ,
			LEGAL_PERSON_COUNTRY = #{legal_person_country, jdbcType=VARCHAR} ,
			EMAIL = #{email, jdbcType=VARCHAR},
			CUSTOMER_ID = {customer_id, jdbcType=NUMERIC},
			ILOG_TYPE = {ilog_type, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE company_id = #{company_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findTrustCompanyByCompanyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, A.EMAIL,
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY, A.CUSTOMER_ID, A.ILOG_TYPE FROM dev_pas.T_TRUST_COMPANY A WHERE 1 = 1  ]]>
		<include refid="trustCompanyWhereCondition" />
	</select>
	
	<select id="findTrustCompanyBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, A.EMAIL,
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY,A.CUSTOMER_ID, A.ILOG_TYPE FROM dev_pas.T_TRUST_COMPANY A WHERE 1 = 1  ]]>
		<include refid="trustCompanyWhereCondition" />
	</select>
	
	<select id="findTrustCompanyByCompanyOranCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, A.EMAIL,
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY,A.CUSTOMER_ID, A.ILOG_TYPE FROM dev_pas.T_TRUST_COMPANY A WHERE 1 = 1  ]]>
		<include refid="trustCompanyWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapTrustCompany" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, A.EMAIL,
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY,A.CUSTOMER_ID, A.ILOG_TYPE FROM dev_pas.T_TRUST_COMPANY A WHERE ROWNUM <=  1000  ]]>
		<include refid="trustCompanyWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllTrustCompany" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, A.EMAIL,
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY,A.CUSTOMER_ID, A.ILOG_TYPE FROM dev_pas.T_TRUST_COMPANY A WHERE ROWNUM <=  1000  ]]>
		<include refid="trustCompanyWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findTrustCompanyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM dev_pas.T_TRUST_COMPANY A WHERE 1 = 1  ]]>
		<include refid="trustCompanyWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryTrustCompanyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MANAGER_COUNTRY, B.TAX_CODE_START_DATE, B.OPERATOR_CERTI_TYPE, B.STATE, B.COMPANY_EN_NAME, B.OPERATOR_CERTI_START_DATE, B.MANAGER_CERTI_END_DATE, 
			B.TAX_CODE_END_DATE, B.HOLDING_PERSON_NAME, B.COMPANY_NAME, B.COMPANY_HOLDER_CERTI_CODE, B.COMPANY_ORAN_CODE, 
			B.OPERATOR_CERTI_CODE, B.COMPANY_ADDRESS, B.ORGAN_CODE_END_DATE, B.DISTRICT, B.MANAGER_NAME, B.COMPANY_ID, 
			B.ADDRESS, B.LEGAL_PERSON_CERTI_TYPE, B.OPERATOR_NAME, B.LICENCE_CODE_END_DATE, B.BUSINESS_COPE, B.OPERATOR_CERTI_END_DATE, B.EMAIL,
			B.ORGAN_CODE_START_DATE, B.LEGAL_PER_CERTI_START_DATE, B.COMPANY_HOLDER_COUNTRY, B.LEGAL_PER_CERTI_END_DATE, B.MANAGER_CERTI_TYPE, B.MANAGER_CERTI_CODE, B.COMPANY_HOLDER_CERTI_TYPE, 
			B.LEGAL_PERSON_CERTI_CODE, B.HOLDER_CERTI_START_DATE, B.POST_CODE, B.MANAGER_CERTI_START_DATE, B.TAX_CODE, B.HOLDER_CERTI_END_DATE, B.LICENCE_CODE_START_DATE, 
			B.BUSI_LICENCE_CODE, B.CITY, B.LEGAL_PERSON_NAME, B.LEGAL_PERSON_COUNTRY,B.CUSTOMER_ID, B.ILOG_TYPE FROM (
					SELECT ROWNUM RN, A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, A.EMAIL,
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY,A.CUSTOMER_ID, A.ILOG_TYPE FROM dev_pas.T_TRUST_COMPANY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="trustCompanyWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
