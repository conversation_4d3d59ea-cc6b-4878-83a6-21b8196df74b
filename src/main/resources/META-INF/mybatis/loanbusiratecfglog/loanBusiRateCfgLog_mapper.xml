<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ILoanBusiRateCfgLogDao">

	<sql id="CUS_loanBusiRateCfgLogWhereCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" time_perido_code  != null "><![CDATA[ AND A.TIME_PERIDO_CODE = #{time_perido_code} ]]></if>
		<if test=" operate_flag  != null "><![CDATA[ AND A.OPERATE_FLAG = #{operate_flag} ]]></if>
		<if test=" loan_rate  != null "><![CDATA[ AND A.LOAN_RATE = #{loan_rate} ]]></if>
		<if test=" cfg_log_id  != null "><![CDATA[ AND A.CFG_LOG_ID = #{cfg_log_id} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="CUS_queryLoanBusiRateCfgLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>
	<sql id="CUS_queryLoanBusiRateCfgLogByCfgLogIdCondition">
		<if test=" cfg_log_id  != null "><![CDATA[ AND A.CFG_LOG_ID = #{cfg_log_id} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="CUS_addLoanBusiRateCfgLog" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_LOAN_BUSI_RATE_CFG_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG(
				INSERT_TIMESTAMP, LOG_ID, TIME_PERIDO_CODE, UPDATE_BY, OPERATE_FLAG, INSERT_TIME, LOAN_RATE, 
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, CFG_LOG_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{time_perido_code, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{operate_flag, jdbcType=NUMERIC} , SYSDATE , #{loan_rate, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{cfg_log_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="CUS_deleteLoanBusiRateCfgLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG WHERE LOG_ID = #{log_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="CUS_updateLoanBusiRateCfgLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG ]]>
		<set>
			<trim suffixOverrides=",">
				LOG_ID = #{log_id, jdbcType=NUMERIC} ,
				TIME_PERIDO_CODE = #{time_perido_code, jdbcType=NUMERIC} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				OPERATE_FLAG = #{operate_flag, jdbcType=NUMERIC} ,
				LOAN_RATE = #{loan_rate, jdbcType=NUMERIC} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				UPDATE_TIME = SYSDATE ,
				CFG_LOG_ID = #{cfg_log_id, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="CUS_findLoanBusiRateCfgLogByLogId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID, A.TIME_PERIDO_CODE, A.OPERATE_FLAG, A.LOAN_RATE, 
			A.CFG_LOG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryLoanBusiRateCfgLogByLogIdCondition" />
	</select>

	<select id="CUS_findLoanBusiRateCfgLogByCfgLogId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID, A.TIME_PERIDO_CODE, A.OPERATE_FLAG, A.LOAN_RATE, 
			A.CFG_LOG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryLoanBusiRateCfgLogByCfgLogIdCondition" />
	</select>

	<!-- 查询单条数据 -->
	<select id="CUS_findLoanBusiRateCfgLog" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID, A.TIME_PERIDO_CODE, A.OPERATE_FLAG, A.LOAN_RATE, 
			A.CFG_LOG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_loanBusiRateCfgLogWhereCondition" />
	</select>

	<!-- 按map查询操作 -->
	<select id="CUS_findAllMapLoanBusiRateCfgLog" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID, A.TIME_PERIDO_CODE, A.OPERATE_FLAG, A.LOAN_RATE, 
			A.CFG_LOG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_loanBusiRateCfgLogWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="CUS_findAllLoanBusiRateCfgLog" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID, A.TIME_PERIDO_CODE, A.OPERATE_FLAG, A.LOAN_RATE, 
			A.CFG_LOG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_loanBusiRateCfgLogWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="CUS_findLoanBusiRateCfgLogTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_loanBusiRateCfgLogWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="CUS_queryLoanBusiRateCfgLogForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LOG_ID, B.TIME_PERIDO_CODE, B.OPERATE_FLAG, B.LOAN_RATE, 
			B.CFG_LOG_ID FROM (
					SELECT ROWNUM RN, A.LOG_ID, A.TIME_PERIDO_CODE, A.OPERATE_FLAG, A.LOAN_RATE, 
			A.CFG_LOG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_RATE_CFG_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_loanBusiRateCfgLogWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

</mapper>
