<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.NoRenewLogDaoImpl">

	<sql id="PA_noRenewLogWhereCondition">
		<if test=" norenew_date  != null  and  norenew_date  != ''  "><![CDATA[ AND A.NORENEW_DATE = #{norenew_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" norenew_reason != null and norenew_reason != ''  "><![CDATA[ AND A.NORENEW_REASON = #{norenew_reason} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryNoRenewLogByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryNoRenewLogByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PA_queryNoRenewLogByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="PA_queryNoRenewLogByNorenewDateCondition">
		<if test=" norenew_date  != null "><![CDATA[ AND A.NORENEW_DATE = #{norenew_date} ]]></if>
	</sql>	
	<sql id="PA_queryNoRenewLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryNoRenewLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addNoRenewLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_NO_RENEW_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_NO_RENEW_LOG(
				INSERT_TIME, NORENEW_DATE, UPDATE_TIME, ITEM_ID, BUSI_PROD_CODE, NORENEW_REASON, INSERT_TIMESTAMP, 
				POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, POLICY_ID ) 
			VALUES (
				SYSDATE, #{norenew_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{norenew_reason, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteNoRenewLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_NO_RENEW_LOG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateNoRenewLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_NO_RENEW_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    NORENEW_DATE = #{norenew_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			NORENEW_REASON = #{norenew_reason, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findNoRenewLogByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_noRenewLogWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 查询单条数据 -->	
	<select id="PA_findNoRenewLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_noRenewLogWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findNoRenewLogByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryNoRenewLogByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findNoRenewLogByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryNoRenewLogByItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findNoRenewLogByNorenewDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryNoRenewLogByNorenewDateCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findNoRenewLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryNoRenewLogByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findNoRenewLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryNoRenewLogByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapNoRenewLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_noRenewLogWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllNoRenewLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_noRenewLogWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findNoRenewLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_noRenewLogWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryNoRenewLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NORENEW_DATE, B.ITEM_ID, B.BUSI_PROD_CODE, B.NORENEW_REASON, 
			B.POLICY_CODE, B.LIST_ID, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.NORENEW_DATE, A.ITEM_ID, A.BUSI_PROD_CODE, A.NORENEW_REASON, 
			A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_NO_RENEW_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_noRenewLogWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
