<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsNoticeRuleCfgDao">

	<sql id="CUS_csNoticeRuleCfgWhereCondition">
		<if test=" receive_obj_type  != null "><![CDATA[ AND A.RECEIVE_OBJ_TYPE = #{receive_obj_type} ]]></if>
		<if test=" phone_flag  != null "><![CDATA[ AND A.PHONE_FLAG = #{phone_flag} ]]></if>
		<if test=" new_phone_check  != null "><![CDATA[ AND A.NEW_PHONE_CHECK = #{new_phone_check} ]]></if>
		<if test=" endorse_content  != null "><![CDATA[ AND A.ENDORSE_CONTENT = #{endorse_content} ]]></if>
		<if test=" notice_code  != null "><![CDATA[ AND A.NOTICE_CODE = #{notice_code} ]]></if>
		<if test=" notice_desc != null and notice_desc != ''  "><![CDATA[ AND A.NOTICE_DESC = #{notice_desc} ]]></if>
		<if test=" valid_date  != null  and  valid_date  != ''  "><![CDATA[ AND A.VALID_DATE = #{valid_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" service_type != null and service_type != ''  "><![CDATA[ AND A.SERVICE_TYPE = #{service_type} ]]></if>
		
		<if test=" noticeCodeList != null and noticeCodeList != ''  ">
			<![CDATA[ AND A.NOTICE_CODE IN ]]>
			<foreach collection="noticeCodeList" item="noticeCodeList"
				index="index" open="(" close=")" separator=",">#{noticeCodeList}
			</foreach>
		</if>
		
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsNoticeRuleCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsNoticeRuleCfg"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG(
				RECEIVE_OBJ_TYPE, PHONE_FLAG, INSERT_TIME, NEW_PHONE_CHECK, UPDATE_TIME, ENDORSE_CONTENT, INSERT_TIMESTAMP, 
				NOTICE_CODE, NOTICE_DESC, UPDATE_BY, VALID_DATE, LIST_ID, UPDATE_TIMESTAMP, SERVICE_CODE, 
				SERVICE_TYPE, INSERT_BY ) 
			VALUES (
				#{receive_obj_type, jdbcType=NUMERIC}, #{phone_flag, jdbcType=NUMERIC} , SYSDATE , #{new_phone_check, jdbcType=NUMERIC} , SYSDATE , #{endorse_content, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{notice_code, jdbcType=NUMERIC} , #{notice_desc, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{valid_date, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{service_code, jdbcType=VARCHAR} 
				, #{service_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsNoticeRuleCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG WHERE  LIST_ID = #{list_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsNoticeRuleCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    RECEIVE_OBJ_TYPE = #{receive_obj_type, jdbcType=NUMERIC} ,
		    PHONE_FLAG = #{phone_flag, jdbcType=NUMERIC} ,
		    NEW_PHONE_CHECK = #{new_phone_check, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ENDORSE_CONTENT = #{endorse_content, jdbcType=NUMERIC} ,
		    NOTICE_CODE = #{notice_code, jdbcType=NUMERIC} ,
			NOTICE_DESC = #{notice_desc, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    VALID_DATE = #{valid_date, jdbcType=DATE} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
			SERVICE_TYPE = #{service_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsNoticeRuleCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.PHONE_FLAG, A.NEW_PHONE_CHECK, A.ENDORSE_CONTENT, 
			A.NOTICE_CODE, A.NOTICE_DESC, A.VALID_DATE, A.LIST_ID, A.SERVICE_CODE, 
			A.SERVICE_TYPE FROM APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsNoticeRuleCfgByListIdCondition" />
	</select>
	
	
	<select id="CUS_findCsNoticeRuleCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.PHONE_FLAG, A.NEW_PHONE_CHECK, A.ENDORSE_CONTENT, 
			A.NOTICE_CODE, A.NOTICE_DESC, A.VALID_DATE, A.LIST_ID, A.SERVICE_CODE, 
			A.SERVICE_TYPE FROM APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_csNoticeRuleCfgWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsNoticeRuleCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.PHONE_FLAG, A.NEW_PHONE_CHECK, A.ENDORSE_CONTENT, 
			A.NOTICE_CODE, A.NOTICE_DESC, A.VALID_DATE, A.LIST_ID, A.SERVICE_CODE, 
			A.SERVICE_TYPE FROM APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG A WHERE ROWNUM <=  1000  ]]>
	<include refid="CUS_csNoticeRuleCfgWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsNoticeRuleCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.PHONE_FLAG, A.NEW_PHONE_CHECK, A.ENDORSE_CONTENT, 
			A.NOTICE_CODE, A.NOTICE_DESC, A.VALID_DATE, A.LIST_ID, A.SERVICE_CODE, 
			A.SERVICE_TYPE FROM APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csNoticeRuleCfgWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsNoticeRuleCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_csNoticeRuleCfgWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsNoticeRuleCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RECEIVE_OBJ_TYPE, B.PHONE_FLAG, B.NEW_PHONE_CHECK, B.ENDORSE_CONTENT, 
			B.NOTICE_CODE, B.NOTICE_DESC, B.VALID_DATE, B.LIST_ID, B.SERVICE_CODE, 
			B.SERVICE_TYPE FROM (
					SELECT ROWNUM RN, A.RECEIVE_OBJ_TYPE, A.PHONE_FLAG, A.NEW_PHONE_CHECK, A.ENDORSE_CONTENT, 
			A.NOTICE_CODE, A.NOTICE_DESC, A.VALID_DATE, A.LIST_ID, A.SERVICE_CODE, 
			A.SERVICE_TYPE FROM APP___PAS__DBUSER.T_CS_NOTICE_RULE_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
