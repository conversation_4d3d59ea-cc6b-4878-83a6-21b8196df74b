<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.TaxProductStatusLogDaoImpl">
<!--
	<sql id="PA_taxProductStatusLogWhereCondition">
		<if test=" product_expiry_date  != null  and  product_expiry_date  != ''  "><![CDATA[ AND A.PRODUCT_EXPIRY_DATE = #{product_expiry_date} ]]></if>
		<if test=" product_liability_state  != null "><![CDATA[ AND A.PRODUCT_LIABILITY_STATE = #{product_liability_state} ]]></if>
		<if test=" busi_status_log_id  != null "><![CDATA[ AND A.BUSI_STATUS_LOG_ID = #{busi_status_log_id} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" product_end_cause != null and product_end_cause != ''  "><![CDATA[ AND A.PRODUCT_END_CAUSE = #{product_end_cause} ]]></if>
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID = #{submit_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryTaxProductStatusLogByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addTaxProductStatusLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TAX_PRODUCT_STATUS_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG(
				PRODUCT_EXPIRY_DATE, PRODUCT_LIABILITY_STATE, INSERT_TIME, BUSI_STATUS_LOG_ID, PRODUCT_CODE, UPDATE_TIME, ITEM_ID, 
				INSERT_TIMESTAMP, UPDATE_BY, PRODUCT_END_CAUSE, SUBMIT_ID, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, 
				INSERT_BY, POLICY_ID ) 
			VALUES (
				#{product_expiry_date, jdbcType=DATE}, #{product_liability_state, jdbcType=NUMERIC} , SYSDATE , #{busi_status_log_id, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , SYSDATE , #{item_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{product_end_cause, jdbcType=VARCHAR} , #{submit_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteTaxProductStatusLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateTaxProductStatusLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    PRODUCT_EXPIRY_DATE = #{product_expiry_date, jdbcType=DATE} ,
		    PRODUCT_LIABILITY_STATE = #{product_liability_state, jdbcType=NUMERIC} ,
		    BUSI_STATUS_LOG_ID = #{busi_status_log_id, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			PRODUCT_END_CAUSE = #{product_end_cause, jdbcType=VARCHAR} ,
		    SUBMIT_ID = #{submit_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findTaxProductStatusLogByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_EXPIRY_DATE, A.PRODUCT_LIABILITY_STATE, A.BUSI_STATUS_LOG_ID, A.PRODUCT_CODE, A.ITEM_ID, 
			A.PRODUCT_END_CAUSE, A.SUBMIT_ID, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxProductStatusLogByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapTaxProductStatusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_EXPIRY_DATE, A.PRODUCT_LIABILITY_STATE, A.BUSI_STATUS_LOG_ID, A.PRODUCT_CODE, A.ITEM_ID, 
			A.PRODUCT_END_CAUSE, A.SUBMIT_ID, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllTaxProductStatusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN,A.PRODUCT_EXPIRY_DATE, A.PRODUCT_LIABILITY_STATE, A.BUSI_STATUS_LOG_ID, A.PRODUCT_CODE, A.ITEM_ID, 
			A.PRODUCT_END_CAUSE, A.SUBMIT_ID, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG A WHERE 1=1  ]]>
		<if test=" busi_status_log_id  != null "><![CDATA[ AND A.BUSI_STATUS_LOG_ID = #{busi_status_log_id} ]]></if>
		<if test=" submit_id  != null "><![CDATA[ AND A.SUBMIT_ID = #{submit_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findTaxProductStatusLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryTaxProductStatusLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRODUCT_EXPIRY_DATE, B.PRODUCT_LIABILITY_STATE, B.BUSI_STATUS_LOG_ID, B.PRODUCT_CODE, B.ITEM_ID, 
			B.PRODUCT_END_CAUSE, B.SUBMIT_ID, B.LIST_ID, B.BUSI_ITEM_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.PRODUCT_EXPIRY_DATE, A.PRODUCT_LIABILITY_STATE, A.BUSI_STATUS_LOG_ID, A.PRODUCT_CODE, A.ITEM_ID, 
			A.PRODUCT_END_CAUSE, A.SUBMIT_ID, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="PA_findTaxProductStatusLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_EXPIRY_DATE, A.PRODUCT_LIABILITY_STATE, A.BUSI_STATUS_LOG_ID, A.PRODUCT_CODE, A.ITEM_ID, 
			A.PRODUCT_END_CAUSE, A.SUBMIT_ID, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_PRODUCT_STATUS_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryTaxProductStatusLogByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
</mapper>
