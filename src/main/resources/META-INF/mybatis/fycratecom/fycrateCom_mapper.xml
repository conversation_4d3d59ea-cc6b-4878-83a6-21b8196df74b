<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IFycrateComDao">
<!--
	<sql id="PAS_fycrateComWhereCondition">
		<if test=" year  != null "><![CDATA[ AND A.YEAR = #{year} ]]></if>
		<if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND A.RISKCODE = #{riskcode} ]]></if>
		<if test=" curyear  != null "><![CDATA[ AND A.CURYEAR = #{curyear} ]]></if>
		<if test=" bank_branch_code != null and bank_branch_code != ''  "><![CDATA[ AND A.BANK_BRANCH_CODE = #{bank_branch_code} ]]></if>
		<if test=" branch_rate  != null "><![CDATA[ AND A.BRANCH_RATE = #{branch_rate} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" startdate  != null  and  startdate  != ''  "><![CDATA[ AND A.STARTDATE = #{startdate} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" payintv != null and payintv != ''  "><![CDATA[ AND A.PAYINTV = #{payintv} ]]></if>
		<if test=" enddate  != null  and  enddate  != ''  "><![CDATA[ AND A.ENDDATE = #{enddate} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryFycrateComByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addFycrateCom"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_FYCRATE_COM__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FYCRATE_COM(
				INSERT_TIME, YEAR, RISKCODE, CURYEAR, BANK_BRANCH_CODE, UPDATE_TIME, BRANCH_RATE, 
				INSERT_TIMESTAMP, ORGAN_CODE, STARTDATE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, PAYINTV, 
				INSERT_BY, ENDDATE ) 
			VALUES (
				SYSDATE, #{year, jdbcType=NUMERIC} , #{riskcode, jdbcType=VARCHAR} , #{curyear, jdbcType=NUMERIC} , #{bank_branch_code, jdbcType=VARCHAR} , SYSDATE , #{branch_rate, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{startdate, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{payintv, jdbcType=VARCHAR} 
				, #{insert_by, jdbcType=NUMERIC} , #{enddate, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deleteFycrateCom" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FYCRATE_COM WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updateFycrateCom" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FYCRATE_COM ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    ENDDATE = #{enddate, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findFycrateComByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.YEAR, A.RISKCODE, A.CURYEAR, A.BANK_BRANCH_CODE, A.BRANCH_RATE, 
			A.ORGAN_CODE, A.STARTDATE, A.LIST_ID, A.PAYINTV, 
			A.ENDDATE FROM APP___PAS__DBUSER.T_FYCRATE_COM A WHERE 1 = 1  ]]>
		<include refid="PAS_queryFycrateComByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapFycrateCom" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.YEAR, A.RISKCODE, A.CURYEAR, A.BANK_BRANCH_CODE, A.BRANCH_RATE, 
			A.ORGAN_CODE, A.STARTDATE, A.LIST_ID, A.PAYINTV, 
			A.ENDDATE FROM APP___PAS__DBUSER.T_FYCRATE_COM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllFycrateCom" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.YEAR, A.RISKCODE, A.CURYEAR, A.BANK_BRANCH_CODE, A.BRANCH_RATE, 
			A.ORGAN_CODE, A.STARTDATE, A.LIST_ID, A.PAYINTV, 
			A.ENDDATE FROM APP___PAS__DBUSER.T_FYCRATE_COM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findFycrateComTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT COUNT(*)
 FROM  
(SELECT TFC.LIST_ID,TFC.ORGAN_CODE,
       (SELECT R.ORGAN_NAME
          FROM APP___PAS__DBUSER.T_UDMP_ORG_REL R
         WHERE R.ORGAN_CODE = TFC.ORGAN_CODE
           AND ROWNUM = 1) ORGAN_NAME,
       TFC.BANK_BRANCH_CODE,
       (SELECT B.BANK_BRANCH_NAME
          FROM APP___PAS__DBUSER.t_bank_branch B
         WHERE B.BANK_BRANCH_CODE = TFC.BANK_BRANCH_CODE
           AND ROWNUM = 1) BANK_BRANCH_NAME,
       TFC.RISKCODE,
       (SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = TFC.RISKCODE
           AND ROWNUM = 1) RISKNAME,
       TFC.PAYINTV,
       TFC.YEAR,
       TFC.CURYEAR,
       TFC.STARTDATE,
       TFC.ENDDATE,
       TFC.BRANCH_RATE,
       (SELECT A.RATE
          FROM APP___PAS__DBUSER.laratecommision A
         WHERE TRIM(A.BRANCHTYPE) = '3'
           AND A.RISKCODE = TFC.RISKCODE
           AND A.MANAGECOM = '86'
           AND TRIM(A.PAYINTV) = TRIM(TFC.PAYINTV)
           AND A.CURYEAR = TFC.CURYEAR
           AND A.STARTDATE <= TFC.STARTDATE
           AND A.ENDDATE >= TFC.STARTDATE
           AND DECODE(A.YEAR,0,1,NULL,999999,A.YEAR) = DECODE(TFC.YEAR,0,1,NULL,999999,TFC.YEAR)]]>
         <![CDATA[   AND ROWNUM=1)
  FROM APP___PAS__DBUSER.T_FYCRATE_COM TFC WHERE 1=1 ]]>
		 <if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND TFC.RISKCODE = #{riskcode} ]]></if>
		 <if test=" payintv != null and payintv != ''  "><![CDATA[  AND TRIM(TFC.PAYINTV) = #{payintv} ]]></if>
		 <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND INSTR(TFC.ORGAN_CODE, #{organ_code}) = 1 ]]></if>
		 <if test=" bank_branch_code != null and bank_branch_code != ''  "><![CDATA[ AND INSTR(TFC.BANK_BRANCH_CODE, #{bank_branch_code}) = 1 ]]></if>
		 <if test=" year  != null "><![CDATA[ AND TFC.YEAR = #{year} ]]></if>
		 <![CDATA[  ) T  ]]>
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryFycrateComForPage" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 	SELECT B.RN AS rowNumber, B.ORGAN_CODE,  B.BANK_BRANCH_CODE, B.RISKCODE,B.PAYINTV,B.YEAR,B.CURYEAR,B.COMPANY_RATE,B.BRANCH_RATE, B.STARTDATE, B.LIST_ID, 
      B.ENDDATE,B.RISKNAME FROM (
SELECT ROWNUM RN, t.list_id,T.ORGAN_CODE ||'-'|| T.ORGAN_NAME ORGAN_CODE,
       T.BANK_BRANCH_CODE ||'-'|| T.BANK_BRANCH_NAME BANK_BRANCH_CODE,
       T.RISKCODE,
       T.RISKNAME,
       TRIM(T.PAYINTV) PAYINTV,
       T.YEAR,
       T.CURYEAR,
       T.RATE COMPANY_RATE,
       T.BRANCH_RATE,
       T.STARTDATE,
       T.ENDDATE
 FROM  
(SELECT TFC.LIST_ID,TFC.ORGAN_CODE,
       (SELECT R.ORGAN_NAME
          FROM APP___PAS__DBUSER.T_UDMP_ORG_REL R
         WHERE R.ORGAN_CODE = TFC.ORGAN_CODE
           AND ROWNUM = 1) ORGAN_NAME,
       TFC.BANK_BRANCH_CODE,
       (SELECT B.BANK_BRANCH_NAME
          FROM APP___PAS__DBUSER.t_bank_branch B
         WHERE B.BANK_BRANCH_CODE = TFC.BANK_BRANCH_CODE
           AND ROWNUM = 1) BANK_BRANCH_NAME,
       TFC.RISKCODE,
       (SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = TFC.RISKCODE
           AND ROWNUM = 1) RISKNAME,
       TFC.PAYINTV,
       TFC.YEAR,
       TFC.CURYEAR,
       TFC.STARTDATE,
       TFC.ENDDATE,
       TFC.BRANCH_RATE,
       (SELECT A.RATE
          FROM APP___PAS__DBUSER.laratecommision A
         WHERE TRIM(A.BRANCHTYPE) = '3'
           AND A.RISKCODE = TFC.RISKCODE
           AND A.MANAGECOM = '86'
           AND TRIM(A.PAYINTV) = TRIM(TFC.PAYINTV)
           AND A.CURYEAR = TFC.CURYEAR
           AND A.STARTDATE <= TFC.STARTDATE
           AND A.ENDDATE >= TFC.STARTDATE
           AND DECODE(A.YEAR,0,1,NULL,999999,A.YEAR) = DECODE(TFC.YEAR,0,1,NULL,999999,TFC.YEAR)]]>
         <![CDATA[   AND ROWNUM=1) RATE
  FROM APP___PAS__DBUSER.T_FYCRATE_COM TFC WHERE 1=1 ]]>
     <if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND TFC.RISKCODE = #{riskcode} ]]></if>
     <if test=" payintv != null and payintv != ''  "><![CDATA[  AND TRIM(TFC.PAYINTV) = #{payintv} ]]></if>
     <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND INSTR(TFC.ORGAN_CODE, #{organ_code}) = 1 ]]></if>
     <if test=" bank_branch_code != null and bank_branch_code != ''  "><![CDATA[ AND INSTR(TFC.BANK_BRANCH_CODE, #{bank_branch_code}) = 1 ]]></if>
     <if test=" year  != null "><![CDATA[ AND TFC.YEAR = #{year} ]]></if>
     <![CDATA[  ORDER BY TFC.ENDDATE DESC ) T WHERE ROWNUM <= #{LESS_NUM} ]]>
     <![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	 <!-- 导出 -->
	<select id="PA_reportQueryFycrateCom" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT ROWNUM RN, T.LIST_ID,T.ORGAN_CODE ||'-'|| T.ORGAN_NAME ORGAN_CODE,
       T.BANK_BRANCH_CODE ||'-'|| T.BANK_BRANCH_NAME BANK_BRANCH_CODE,
       T.RISKCODE,
       T.RISKNAME,
       TRIM(T.PAYINTV) PAYINTV,
       T.YEAR,
       T.CURYEAR,
       T.RATE COMPANY_RATE,
       T.BRANCH_RATE,
       T.STARTDATE,
       T.ENDDATE
 FROM  
(SELECT TFC.LIST_ID,TFC.ORGAN_CODE,
       (SELECT R.ORGAN_NAME
          FROM APP___PAS__DBUSER.T_UDMP_ORG_REL R
         WHERE R.ORGAN_CODE = TFC.ORGAN_CODE
           AND ROWNUM = 1) ORGAN_NAME,
       TFC.BANK_BRANCH_CODE,
       (SELECT B.BANK_BRANCH_NAME
          FROM APP___PAS__DBUSER.t_bank_branch B
         WHERE B.BANK_BRANCH_CODE = TFC.BANK_BRANCH_CODE
           AND ROWNUM = 1) BANK_BRANCH_NAME,
       TFC.RISKCODE,
       (SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = TFC.RISKCODE
           AND ROWNUM = 1) RISKNAME,
       TFC.PAYINTV,
       TFC.YEAR,
       TFC.CURYEAR,
       TFC.STARTDATE,
       TFC.ENDDATE,
       TFC.BRANCH_RATE,
       (SELECT A.RATE
          FROM APP___PAS__DBUSER.laratecommision A
         WHERE TRIM(A.BRANCHTYPE) = '3'
           AND A.RISKCODE = TFC.RISKCODE
           AND A.MANAGECOM = '86'
           AND TRIM(A.PAYINTV) = TRIM(TFC.PAYINTV)
           AND A.CURYEAR = TFC.CURYEAR
           AND A.STARTDATE <= TFC.STARTDATE
           AND A.ENDDATE >= TFC.STARTDATE
           AND DECODE(A.YEAR,0,1,NULL,999999,A.YEAR) = DECODE(TFC.YEAR,0,1,NULL,999999,TFC.YEAR)]]>
         <![CDATA[   AND ROWNUM=1) RATE
  FROM APP___PAS__DBUSER.T_FYCRATE_COM TFC WHERE 1=1 ]]>
		 <if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND TFC.RISKCODE = #{riskcode} ]]></if>
		 <if test=" payintv != null and payintv != ''  "><![CDATA[  AND TRIM(TFC.PAYINTV) = #{payintv} ]]></if>
		 <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND INSTR(TFC.ORGAN_CODE, #{organ_code}) = 1 ]]></if>
		 <if test=" bank_branch_code != null and bank_branch_code != ''  "><![CDATA[ AND INSTR(TFC.BANK_BRANCH_CODE, #{bank_branch_code}) = 1 ]]></if>
		 <if test=" year  != null "><![CDATA[ AND TFC.YEAR = #{year} ]]></if>
		 <![CDATA[  ) T ORDER BY T.ENDDATE DESC ]]>
	</select>
    <!-- 总公司佣金率列表 -->
	<select id="PA_queryCommisionRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT T.RISKCODE, T.YEAR, TRIM(T.PAYINTV) PAYINTV, T.CURYEAR, T.RATE COMPANY_RATE
       FROM (SELECT A.RISKCODE, A.YEAR, A.PAYINTV, A.CURYEAR, A.RATE
               FROM APP___PAS__DBUSER.LARATECOMMISION A
              WHERE TRIM(A.BRANCHTYPE) = '3'
                AND A.RISKCODE = #{riskcode}
                AND A.MANAGECOM = '86'
                AND A.CURYEAR = '1'
                AND A.STARTDATE <= #{startdate}
                AND A.ENDDATE >= #{startdate}
              GROUP BY A.RISKCODE, A.YEAR, A.PAYINTV, A.CURYEAR, A.RATE) T
      ORDER BY T.CURYEAR ]]>
	</select>
	
	
	<!-- 查询个数操作 -->
	<select id="PA_queryOrganCommRateIsRepeat" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT COUNT(*)
           FROM APP___PAS__DBUSER.T_FYCRATE_COM TFC WHERE 1=1  ]]>
      <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND TFC.ORGAN_CODE = #{organ_code} ]]></if>
      <if test=" bank_branch_code != null and bank_branch_code != ''  "><![CDATA[ AND TFC.BANK_BRANCH_CODE = #{bank_branch_code} ]]></if>
      <if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND TFC.RISKCODE = #{riskcode} ]]></if>
      <if test=" payintv != null and payintv != ''  "><![CDATA[  AND TRIM(TFC.PAYINTV) = #{payintv} ]]></if>
      <if test=" year  != null "><![CDATA[ AND TFC.YEAR = #{year}]]></if>
	  <if test=" curyear  != null "><![CDATA[ AND TFC.CURYEAR = #{curyear} ]]></if>
      <if test=" startdate  != null  and  startdate  != '' and enddate  != null  and  enddate  != '' "><![CDATA[ 
       and ((tfc.enddate >= #{enddate} and tfc.startdate <= #{startdate}) or
       (tfc.enddate >= #{startdate} and tfc.enddate <= #{enddate}) or
       (tfc.startdate >= #{startdate} and tfc.startdate <= #{enddate}))
       ]]></if>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="PAS_findLaratecommisionRateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT COUNT(*) FROM  
     (SELECT T.RISKCODE,
       (SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = T.RISKCODE
           AND ROWNUM = 1) RISKNAME,
       T.PAYINTV,
       T.YEAR,
       T.CURYEAR,
       T.F07 LOWER_LIMITSA_RANGE,
       T.F08 UP_LIMITSA_RANGE,
       NVL(T.F09,'N') SA_INSURANCE_LEVEL,
       T.RATE,
       T.STARTDATE,
       T.ENDDATE
     FROM DEV_PAS.LARATECOMMISION T
     WHERE T.BRANCHTYPE = '3'
     AND T.MANAGECOM = '86'
     AND T.ENDDATE >= TO_DATE(TO_CHAR(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd') ]]>
     <if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND T.RISKCODE = #{riskcode} ]]></if>
	 <if test=" payintv != null and payintv != ''  "><![CDATA[  AND TRIM(T.PAYINTV) = #{payintv} ]]></if>	  
     <if test="year !=null and payintv != '99'.toString() "><![CDATA[
        AND T.YEAR = #{year}
     ]]></if>
	 <if test=" curyear  != null "><![CDATA[ AND T.CURYEAR = #{curyear} ]]></if>
		 <![CDATA[  ) T  ]]>
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryLaratecommisionRateForPage" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 	SELECT B.RN AS rowNumber, B.RISKCODE,
       B.RISKNAME,
       B.PAYINTV,
       B.YEAR,
       B.CURYEAR,
       B.LOWER_LIMITSA_RANGE,
       B.UP_LIMITSA_RANGE,
       B.SA_INSURANCE_LEVEL,
       B.RATE COMPANY_RATE,
       B.STARTDATE,
       B.ENDDATE FROM (
SELECT ROWNUM RN, A.RISKCODE,
       A.RISKNAME,
       A.PAYINTV,
       A.YEAR,
       A.CURYEAR,
       A.LOWER_LIMITSA_RANGE,
       A.UP_LIMITSA_RANGE,
       A.SA_INSURANCE_LEVEL,
       A.RATE,
       A.STARTDATE,
       A.ENDDATE
 FROM  
(  SELECT T.RISKCODE,
       (SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = T.RISKCODE
           AND ROWNUM = 1) RISKNAME,
       TRIM(T.PAYINTV) PAYINTV,
       T.YEAR,
       T.CURYEAR,
       T.F07 LOWER_LIMITSA_RANGE,
       T.F08 UP_LIMITSA_RANGE,
       NVL(T.F09,'N') SA_INSURANCE_LEVEL,
       T.RATE,
       T.STARTDATE,
       T.ENDDATE
     FROM DEV_PAS.LARATECOMMISION T
     WHERE T.BRANCHTYPE = '3'
     AND T.MANAGECOM = '86'
     AND T.ENDDATE >= TO_DATE(TO_CHAR(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd') ]]>
     <if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND T.RISKCODE = #{riskcode} ]]></if>
	 <if test=" payintv != null and payintv != ''  "><![CDATA[  AND TRIM(T.PAYINTV) = #{payintv} ]]></if>	  
     <if test="year !=null and payintv != '99'.toString() "><![CDATA[
        AND T.YEAR = #{year}
     ]]></if>
	 <if test=" curyear  != null "><![CDATA[ AND T.CURYEAR = #{curyear} ]]></if>
		<![CDATA[ ORDER BY T.STARTDATE  ) A WHERE ROWNUM <= #{LESS_NUM} ]]>
     <![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	 <!-- 导出 -->
	<select id="PA_reportLaratecommisionRateQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT ROWNUM RN ,T.RISKCODE,
       (SELECT TBP.PRODUCT_NAME_STD
          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
         WHERE TBP.PRODUCT_CODE_SYS = T.RISKCODE
           AND ROWNUM = 1) RISKNAME,
       TRIM(T.PAYINTV) PAYINTV,
       T.YEAR,
       T.CURYEAR,
       T.F07 LOWER_LIMITSA_RANGE,
       T.F08 UP_LIMITSA_RANGE,
       NVL(T.F09,'N') SA_INSURANCE_LEVEL,
       T.RATE COMPANY_RATE,
       T.STARTDATE,
       T.ENDDATE
     FROM DEV_PAS.LARATECOMMISION T
     WHERE T.BRANCHTYPE = '3'
     AND T.MANAGECOM = '86'
     AND T.ENDDATE >= TO_DATE(TO_CHAR(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd') ]]>
     <if test=" riskcode != null and riskcode != ''  "><![CDATA[ AND T.RISKCODE = #{riskcode} ]]></if>
	 <if test=" payintv != null and payintv != ''  "><![CDATA[  AND TRIM(T.PAYINTV) = #{payintv} ]]></if>	  
     <if test="year !=null and payintv != '99'.toString() "><![CDATA[
        AND T.YEAR = #{year}
     ]]></if>
	 <if test=" curyear  != null "><![CDATA[ AND T.CURYEAR = #{curyear} ]]></if>
		<![CDATA[ ORDER BY T.STARTDATE ]]>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="PA_queryBankOrganCommRateCfgIsExist" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT COUNT(*) FROM (
		SELECT M.*
  FROM DEV_PAS.T_FYCRATE_COM T, DEV_PAS.T_FYCRATE_COM M
 WHERE T.ORGAN_CODE = M.ORGAN_CODE
   AND T.BANK_BRANCH_CODE = M.BANK_BRANCH_CODE
   AND T.RISKCODE = M.RISKCODE
   AND TRIM(T.CURYEAR) = TRIM(M.CURYEAR)
   AND TRIM(T.YEAR) = TRIM(M.YEAR)
   AND TRIM(T.PAYINTV) = TRIM(M.PAYINTV)
   AND T.LIST_ID = #{list_id}
   AND (M.STARTDATE <= #{enddate, jdbcType=DATE} AND M.ENDDATE >= #{enddate, jdbcType=DATE})) A WHERE A.LIST_ID != #{list_id}
		     ]]>
	</select>
</mapper>
