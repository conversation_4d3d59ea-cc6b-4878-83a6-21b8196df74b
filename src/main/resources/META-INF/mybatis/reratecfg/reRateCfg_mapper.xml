<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IReRateCfgDao">

	<sql id="reRateCfgWhereCondition">
		<if test=" start_date  != null "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" rate  != null "><![CDATA[ AND A.RATE = #{rate} ]]></if>
		<if test=" busi_type != null and busi_type != ''  "><![CDATA[ AND A.BUSI_TYPE = #{busi_type} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryReRateCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addReRateCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT APP___PAS__DBUSER.S_RE_RATE_CFG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RE_RATE_CFG(
				INSERT_TIMESTAMP, START_DATE, RATE, BUSI_TYPE, UPDATE_BY, INSERT_TIME, END_DATE, 
				LIST_ID, UPDATE_TIME, UPDATE_TIMESTAMP, INSERT_BY, STATUS ) 
			VALUES (
				CURRENT_TIMESTAMP, #{start_date, jdbcType=NUMERIC} , #{rate, jdbcType=NUMERIC} , #{busi_type, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{end_date, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{status, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteReRateCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RE_RATE_CFG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateReRateCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RE_RATE_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    START_DATE = #{start_date, jdbcType=NUMERIC} ,
		    RATE = #{rate, jdbcType=NUMERIC} ,
			BUSI_TYPE = #{busi_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    END_DATE = #{end_date, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    STATUS = #{status, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findReRateCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.RATE, A.BUSI_TYPE, A.END_DATE, 
			A.LIST_ID, A.STATUS FROM APP___PAS__DBUSER.T_RE_RATE_CFG A WHERE 1 = 1  ]]>
		<include refid="queryReRateCfgByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapReRateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.RATE, A.BUSI_TYPE, A.END_DATE, 
			A.LIST_ID, A.STATUS FROM APP___PAS__DBUSER.T_RE_RATE_CFG A WHERE ROWNUM <=  1000  ]]>
		 <include refid="reRateCfgWhereCondition" /> 
	</select>

<!-- 查询所有操作 -->
	<select id="findAllReRateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.RATE, A.BUSI_TYPE, A.END_DATE, 
			A.LIST_ID, A.STATUS,A.RATE_TYPE  FROM APP___PAS__DBUSER.T_RE_RATE_CFG A WHERE ROWNUM <=  1000  ]]>
		 <include refid="reRateCfgWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="findReRateCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RE_RATE_CFG A WHERE 1 = 1  ]]>
		 <include refid="reRateCfgWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="queryReRateCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.START_DATE, B.RATE, B.BUSI_TYPE, B.END_DATE, 
			B.LIST_ID, B.STATUS FROM (
					SELECT ROWNUM RN, A.START_DATE, A.RATE, A.BUSI_TYPE, A.END_DATE, 
			A.LIST_ID, A.STATUS FROM APP___PAS__DBUSER.T_RE_RATE_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		 <include refid="reRateCfgWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
