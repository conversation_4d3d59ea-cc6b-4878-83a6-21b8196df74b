<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.impl.null">

	<sql id="csTrustSubmitTaskWhereCondition">
		<if test=" submit_result_code != null and submit_result_code != ''  "><![CDATA[ AND A.SUBMIT_RESULT_CODE = #{submit_result_code} ]]></if>
		<if test=" upload_result_code != null and upload_result_code != ''  "><![CDATA[ AND A.UPLOAD_RESULT_CODE = #{upload_result_code} ]]></if>
		<if test=" submit_response_desc != null and submit_response_desc != ''  "><![CDATA[ AND A.SUBMIT_RESPONSE_DESC = #{submit_response_desc} ]]></if>
		<if test=" upload_status != null and upload_status != ''  "><![CDATA[ AND A.UPLOAD_STATUS = #{upload_status} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
		<if test=" submit_batch_no != null and submit_batch_no != ''  "><![CDATA[ AND A.SUBMIT_BATCH_NO = #{submit_batch_no} ]]></if>
		<if test=" submit_status != null and submit_status != ''  "><![CDATA[ AND A.SUBMIT_STATUS = #{submit_status} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" file_name != null and file_name != ''  "><![CDATA[ AND A.FILE_NAME = #{file_name} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" submit_result_desc != null and submit_result_desc != ''  "><![CDATA[ AND A.SUBMIT_RESULT_DESC = #{submit_result_desc} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsTrustSubmitTaskByTaskIdCondition">
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>	
	<sql id="queryCsTrustSubmitTaskByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>	
	<sql id="queryCsTrustSubmitTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsTrustSubmitTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="task_id">
			SELECT DEV_PAS.S_CS_TRUST_SUB_TASK__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CS_TRUST_SUBMIT_TASK(
				SUBMIT_RESULT_CODE, UPLOAD_RESULT_CODE, SUBMIT_RESPONSE_DESC, INSERT_TIME, UPLOAD_STATUS, UPDATE_TIME, TASK_ID, 
				SUBMIT_BATCH_NO, SUBMIT_STATUS, ACCEPT_CODE, INSERT_TIMESTAMP, FILE_NAME, POLICY_CODE, UPDATE_BY, 
				SUBMIT_RESULT_DESC, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{submit_result_code, jdbcType=VARCHAR}, #{upload_result_code, jdbcType=VARCHAR} , #{submit_response_desc, jdbcType=VARCHAR} , SYSDATE , #{upload_status, jdbcType=VARCHAR} , SYSDATE , #{task_id, jdbcType=NUMERIC} 
				, #{submit_batch_no, jdbcType=VARCHAR} , #{submit_status, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{file_name, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{submit_result_desc, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsTrustSubmitTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK WHERE TASK_ID = #{task_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsTrustSubmitTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CS_TRUST_SUBMIT_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			SUBMIT_RESULT_CODE = #{submit_result_code, jdbcType=VARCHAR} ,
			UPLOAD_RESULT_CODE = #{upload_result_code, jdbcType=VARCHAR} ,
			SUBMIT_RESPONSE_DESC = #{submit_response_desc, jdbcType=VARCHAR} ,
			UPLOAD_STATUS = #{upload_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			SUBMIT_BATCH_NO = #{submit_batch_no, jdbcType=VARCHAR} ,
			SUBMIT_STATUS = #{submit_status, jdbcType=VARCHAR} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			FILE_NAME = #{file_name, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			SUBMIT_RESULT_DESC = #{submit_result_desc, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE TASK_ID = #{task_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsTrustSubmitTaskByTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCsTrustSubmitTaskByTaskIdCondition" />
		<![CDATA[ ORDER BY A.TASK_ID ]]>
	</select>
	
	<select id="findCsTrustSubmitTaskByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCsTrustSubmitTaskByAcceptCodeCondition" />
		<![CDATA[ ORDER BY A.TASK_ID ]]>
	</select>
	
	<select id="findCsTrustSubmitTaskByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCsTrustSubmitTaskByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.TASK_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsTrustSubmitTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TASK_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsTrustSubmitTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE ROWNUM <=  1000  ]]>
		 <include refid="csTrustSubmitTaskWhereCondition" /> 
		<![CDATA[ ORDER BY A.TASK_ID ]]> 
	</select>
	
<!-- 查询信托提交保单信息不成功的数据 -->
	<select id="findAllCsTrustSubmitTaskNotequalto" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE A.SUBMIT_STATUS != #{submit_status}]]>
	</select>	
	
	<!-- 查询信托未发送邮件的数据 -->
	<select id="findAllCsTrustSubmitTaskNotequaltoem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE ROWNUM <=  1000]]>
			<include refid="csTrustSubmitTaskWhereCondition" /> 
		<![CDATA[ ORDER BY A.TASK_ID ]]> 
	</select>	
<!-- 查询信托提上传状态不成功的数据 -->	
	<select id="findAllCsTrustSubmitTaskupp" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE A.SUBMIT_STATUS = #{submit_status} AND A.UPLOAD_STATUS = #{upload_status}]]>
	</select>	

<!-- 查询个数操作 -->
	<select id="findCsTrustSubmitTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsTrustSubmitTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SUBMIT_RESULT_CODE, B.UPLOAD_RESULT_CODE, B.SUBMIT_RESPONSE_DESC, B.UPLOAD_STATUS, B.TASK_ID, 
			B.SUBMIT_BATCH_NO, B.SUBMIT_STATUS, B.ACCEPT_CODE, B.FILE_NAME, B.POLICY_CODE, 
			B.SUBMIT_RESULT_DESC FROM (
					SELECT ROWNUM RN, A.SUBMIT_RESULT_CODE, A.UPLOAD_RESULT_CODE, A.SUBMIT_RESPONSE_DESC, A.UPLOAD_STATUS, A.TASK_ID, 
			A.SUBMIT_BATCH_NO, A.SUBMIT_STATUS, A.ACCEPT_CODE, A.FILE_NAME, A.POLICY_CODE, 
			A.SUBMIT_RESULT_DESC FROM DEV_PAS.T_CS_TRUST_SUBMIT_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TASK_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!--修改信托发送状态-->
	<update id="updateCsTrustSubmitTaskPOfindall" parameterType="java.util.Map" >
	<![CDATA[ UPDATE DEV_PAS.T_CS_TRUST_SUBMIT_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			SUBMIT_STATUS = #{submit_status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CODE=#{policy_code} ]]>
	</update>
	
	
	<!--修改信托发送状态-->
	<update id="updateCsTrustSubmitTaskPOfindallemails" parameterType="java.util.Map" >
	<![CDATA[ UPDATE DEV_PAS.T_CS_TRUST_SUBMIT_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			SUBMIT_BATCH_NO = #{submit_batch_no, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CODE=#{policy_code} and ACCEPT_CODE=#{accept_code}]]>
	</update>
	
	
	<!--修改信托发送状态-->
	<update id="updatexintuofileCsTrustSubmitTaskPOfindall" parameterType="java.util.Map" >
	<![CDATA[ UPDATE DEV_PAS.T_CS_TRUST_SUBMIT_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			UPLOAD_STATUS = #{upload_status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CODE=#{policy_code} ]]>
	</update>
	
</mapper>
