<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsPayCompanyDao">

	<sql id="CUS_csPayCompanyWhereCondition">
		<if test=" tax_code_start_date  != null  and  tax_code_start_date  != ''  "><![CDATA[ AND A.TAX_CODE_START_DATE = #{tax_code_start_date} ]]></if>
		<if test=" operator_certi_type != null and operator_certi_type != ''  "><![CDATA[ AND A.OPERATOR_CERTI_TYPE = #{operator_certi_type} ]]></if>
		<if test=" holding_person_name != null and holding_person_name != ''  "><![CDATA[ AND A.HOLDING_PERSON_NAME = #{holding_person_name} ]]></if>
		<if test=" manager_certi_end_date  != null  and  manager_certi_end_date  != ''  "><![CDATA[ AND A.MANAGER_CERTI_END_DATE = #{manager_certi_end_date} ]]></if>
		<if test=" operator_certi_start_date  != null  and  operator_certi_start_date  != ''  "><![CDATA[ AND A.OPERATOR_CERTI_START_DATE = #{operator_certi_start_date} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" social_security_code != null and social_security_code != ''  "><![CDATA[ AND A.SOCIAL_SECURITY_CODE = #{social_security_code} ]]></if>
		<if test=" operator_certi_code != null and operator_certi_code != ''  "><![CDATA[ AND A.OPERATOR_CERTI_CODE = #{operator_certi_code} ]]></if>
		<if test=" org_code_end_date  != null  and  org_code_end_date  != ''  "><![CDATA[ AND A.ORG_CODE_END_DATE = #{org_code_end_date} ]]></if>
		<if test=" licence_start_date  != null  and  licence_start_date  != ''  "><![CDATA[ AND A.LICENCE_START_DATE = #{licence_start_date} ]]></if>
		<if test=" company_industry != null and company_industry != ''  "><![CDATA[ AND A.COMPANY_INDUSTRY = #{company_industry} ]]></if>
		<if test=" manager_name != null and manager_name != ''  "><![CDATA[ AND A.MANAGER_NAME = #{manager_name} ]]></if>
		<if test=" legal_person_certi_type != null and legal_person_certi_type != ''  "><![CDATA[ AND A.LEGAL_PERSON_CERTI_TYPE = #{legal_person_certi_type} ]]></if>
		<if test=" business_cope != null and business_cope != ''  "><![CDATA[ AND A.BUSINESS_COPE = #{business_cope} ]]></if>
		<if test=" operator_name != null and operator_name != ''  "><![CDATA[ AND A.OPERATOR_NAME = #{operator_name} ]]></if>
		<if test=" legal_per_certi_start_date  != null  and  legal_per_certi_start_date  != ''  "><![CDATA[ AND A.LEGAL_PER_CERTI_START_DATE = #{legal_per_certi_start_date} ]]></if>
		<if test=" manager_certi_code != null and manager_certi_code != ''  "><![CDATA[ AND A.MANAGER_CERTI_CODE = #{manager_certi_code} ]]></if>
		<if test=" manager_certi_type != null and manager_certi_type != ''  "><![CDATA[ AND A.MANAGER_CERTI_TYPE = #{manager_certi_type} ]]></if>
		<if test=" org_code_start_date  != null  and  org_code_start_date  != ''  "><![CDATA[ AND A.ORG_CODE_START_DATE = #{org_code_start_date} ]]></if>
		<if test=" legal_person_certi_code != null and legal_person_certi_code != ''  "><![CDATA[ AND A.LEGAL_PERSON_CERTI_CODE = #{legal_person_certi_code} ]]></if>
		<if test=" manager_certi_start_date  != null  and  manager_certi_start_date  != ''  "><![CDATA[ AND A.MANAGER_CERTI_START_DATE = #{manager_certi_start_date} ]]></if>
		<if test=" post_code != null and post_code != ''  "><![CDATA[ AND A.POST_CODE = #{post_code} ]]></if>
		<if test=" holder_certi_end_date  != null  and  holder_certi_end_date  != ''  "><![CDATA[ AND A.HOLDER_CERTI_END_DATE = #{holder_certi_end_date} ]]></if>
		<if test=" tax_code != null and tax_code != ''  "><![CDATA[ AND A.TAX_CODE = #{tax_code} ]]></if>
		<if test=" legal_person_name != null and legal_person_name != ''  "><![CDATA[ AND A.LEGAL_PERSON_NAME = #{legal_person_name} ]]></if>
		<if test=" city != null and city != ''  "><![CDATA[ AND A.CITY = #{city} ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND A.STATE = #{state} ]]></if>
		<if test=" tax_code_end_date  != null  and  tax_code_end_date  != ''  "><![CDATA[ AND A.TAX_CODE_END_DATE = #{tax_code_end_date} ]]></if>
		<if test=" reg_capital != null and reg_capital != ''  "><![CDATA[ AND A.REG_CAPITAL = #{reg_capital} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
		<if test=" company_holder_certi_code != null and company_holder_certi_code != ''  "><![CDATA[ AND A.COMPANY_HOLDER_CERTI_CODE = #{company_holder_certi_code} ]]></if>
		<if test=" reg_capital_currency != null and reg_capital_currency != ''  "><![CDATA[ AND A.REG_CAPITAL_CURRENCY = #{reg_capital_currency} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" company_org_code != null and company_org_code != ''  "><![CDATA[ AND A.COMPANY_ORG_CODE = #{company_org_code} ]]></if>
		<if test=" company_address != null and company_address != ''  "><![CDATA[ AND A.COMPANY_ADDRESS = #{company_address} ]]></if>
		<if test=" district != null and district != ''  "><![CDATA[ AND A.DISTRICT = #{district} ]]></if>
		<if test=" holde_ratio != null and holde_ratio != ''  "><![CDATA[ AND A.HOLDE_RATIO = #{holde_ratio} ]]></if>
		<if test=" country != null and country != ''  "><![CDATA[ AND A.COUNTRY = #{country} ]]></if>
		<if test=" address != null and address != ''  "><![CDATA[ AND A.ADDRESS = #{address} ]]></if>
		<if test=" operator_certi_end_date  != null  and  operator_certi_end_date  != ''  "><![CDATA[ AND A.OPERATOR_CERTI_END_DATE = #{operator_certi_end_date} ]]></if>
		<if test=" legal_per_certi_end_date  != null  and  legal_per_certi_end_date  != ''  "><![CDATA[ AND A.LEGAL_PER_CERTI_END_DATE = #{legal_per_certi_end_date} ]]></if>
		<if test=" bene_judge_way != null and bene_judge_way != ''  "><![CDATA[ AND A.BENE_JUDGE_WAY = #{bene_judge_way} ]]></if>
		<if test=" company_holder_certi_type != null and company_holder_certi_type != ''  "><![CDATA[ AND A.COMPANY_HOLDER_CERTI_TYPE = #{company_holder_certi_type} ]]></if>
		<if test=" holder_certi_start_date  != null  and  holder_certi_start_date  != ''  "><![CDATA[ AND A.HOLDER_CERTI_START_DATE = #{holder_certi_start_date} ]]></if>
		<if test=" mobile != null and mobile != ''  "><![CDATA[ AND A.MOBILE = #{mobile} ]]></if>
		<if test=" busi_licence_code != null and busi_licence_code != ''  "><![CDATA[ AND A.BUSI_LICENCE_CODE = #{busi_licence_code} ]]></if>
		<if test=" licence_end_date  != null  and  licence_end_date  != ''  "><![CDATA[ AND A.LICENCE_END_DATE = #{licence_end_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsPayCompanyCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" company_org_code != null and company_org_code != '' "><![CDATA[ AND A.COMPANY_ORG_CODE = #{company_org_code} ]]></if>
		<if test=" company_name != null and company_name != '' "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsPayCompany"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_PAY_COM__LISID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PAY_COMPANY(
				TAX_CODE_START_DATE, OPERATOR_CERTI_TYPE, HOLDING_PERSON_NAME, MANAGER_CERTI_END_DATE, OPERATOR_CERTI_START_DATE, REMARK, SOCIAL_SECURITY_CODE, 
				UPDATE_BY, OPERATOR_CERTI_CODE, ORG_CODE_END_DATE, LICENCE_START_DATE, COMPANY_INDUSTRY, MANAGER_NAME, LEGAL_PERSON_CERTI_TYPE, 
				UPDATE_TIME, BUSINESS_COPE, OPERATOR_NAME, LEGAL_PER_CERTI_START_DATE, MANAGER_CERTI_CODE, MANAGER_CERTI_TYPE, ORG_CODE_START_DATE, 
				LEGAL_PERSON_CERTI_CODE, MANAGER_CERTI_START_DATE, POST_CODE, HOLDER_CERTI_END_DATE, TAX_CODE, UPDATE_TIMESTAMP, LEGAL_PERSON_NAME, 
				CITY, INSERT_BY, STATE, TAX_CODE_END_DATE, REG_CAPITAL, COMPANY_NAME, COMPANY_HOLDER_CERTI_CODE, 
				INSERT_TIMESTAMP, REG_CAPITAL_CURRENCY, LIST_ID, COMPANY_ORG_CODE, COMPANY_ADDRESS, DISTRICT, HOLDE_RATIO, 
				COUNTRY, INSERT_TIME, ADDRESS, OPERATOR_CERTI_END_DATE, LEGAL_PER_CERTI_END_DATE, BENE_JUDGE_WAY, COMPANY_HOLDER_CERTI_TYPE, 
				HOLDER_CERTI_START_DATE, MOBILE, BUSI_LICENCE_CODE, LICENCE_END_DATE ) 
			VALUES (
				#{tax_code_start_date, jdbcType=DATE}, #{operator_certi_type, jdbcType=VARCHAR} , #{holding_person_name, jdbcType=VARCHAR} , #{manager_certi_end_date, jdbcType=DATE} , #{operator_certi_start_date, jdbcType=DATE} , #{remark, jdbcType=VARCHAR} , #{social_security_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{operator_certi_code, jdbcType=VARCHAR} , #{org_code_end_date, jdbcType=DATE} , #{licence_start_date, jdbcType=DATE} , #{company_industry, jdbcType=VARCHAR} , #{manager_name, jdbcType=VARCHAR} , #{legal_person_certi_type, jdbcType=VARCHAR} 
				, SYSDATE , #{business_cope, jdbcType=VARCHAR} , #{operator_name, jdbcType=VARCHAR} , #{legal_per_certi_start_date, jdbcType=DATE} , #{manager_certi_code, jdbcType=VARCHAR} , #{manager_certi_type, jdbcType=VARCHAR} , #{org_code_start_date, jdbcType=DATE} 
				, #{legal_person_certi_code, jdbcType=VARCHAR} , #{manager_certi_start_date, jdbcType=DATE} , #{post_code, jdbcType=VARCHAR} , #{holder_certi_end_date, jdbcType=DATE} , #{tax_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{legal_person_name, jdbcType=VARCHAR} 
				, #{city, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{state, jdbcType=VARCHAR} , #{tax_code_end_date, jdbcType=DATE} , #{reg_capital, jdbcType=VARCHAR} , #{company_name, jdbcType=VARCHAR} , #{company_holder_certi_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{reg_capital_currency, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{company_org_code, jdbcType=VARCHAR} , #{company_address, jdbcType=VARCHAR} , #{district, jdbcType=VARCHAR} , #{holde_ratio, jdbcType=VARCHAR} 
				, #{country, jdbcType=VARCHAR} , SYSDATE , #{address, jdbcType=VARCHAR} , #{operator_certi_end_date, jdbcType=DATE} , #{legal_per_certi_end_date, jdbcType=DATE} , #{bene_judge_way, jdbcType=VARCHAR} , #{company_holder_certi_type, jdbcType=VARCHAR} 
				, #{holder_certi_start_date, jdbcType=DATE} , #{mobile, jdbcType=VARCHAR} , #{busi_licence_code, jdbcType=VARCHAR} , #{licence_end_date, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
<!-- 	<delete id="CUS_deleteCsPayCompany" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY WHERE  = 1 = 1 ]]>
	</delete> -->

<!-- 修改操作 -->
	<update id="updateCsPayCompany" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PAY_COMPANY ]]>
		<set>
		<trim suffixOverrides=",">
		    TAX_CODE_START_DATE = #{tax_code_start_date, jdbcType=DATE} ,
			OPERATOR_CERTI_TYPE = #{operator_certi_type, jdbcType=VARCHAR} ,
			HOLDING_PERSON_NAME = #{holding_person_name, jdbcType=VARCHAR} ,
		    MANAGER_CERTI_END_DATE = #{manager_certi_end_date, jdbcType=DATE} ,
		    OPERATOR_CERTI_START_DATE = #{operator_certi_start_date, jdbcType=DATE} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
			SOCIAL_SECURITY_CODE = #{social_security_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			OPERATOR_CERTI_CODE = #{operator_certi_code, jdbcType=VARCHAR} ,
		    ORG_CODE_END_DATE = #{org_code_end_date, jdbcType=DATE} ,
		    LICENCE_START_DATE = #{licence_start_date, jdbcType=DATE} ,
			COMPANY_INDUSTRY = #{company_industry, jdbcType=VARCHAR} ,
			MANAGER_NAME = #{manager_name, jdbcType=VARCHAR} ,
			LEGAL_PERSON_CERTI_TYPE = #{legal_person_certi_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			BUSINESS_COPE = #{business_cope, jdbcType=VARCHAR} ,
			OPERATOR_NAME = #{operator_name, jdbcType=VARCHAR} ,
		    LEGAL_PER_CERTI_START_DATE = #{legal_per_certi_start_date, jdbcType=DATE} ,
			MANAGER_CERTI_CODE = #{manager_certi_code, jdbcType=VARCHAR} ,
			MANAGER_CERTI_TYPE = #{manager_certi_type, jdbcType=VARCHAR} ,
		    ORG_CODE_START_DATE = #{org_code_start_date, jdbcType=DATE} ,
			LEGAL_PERSON_CERTI_CODE = #{legal_person_certi_code, jdbcType=VARCHAR} ,
		    MANAGER_CERTI_START_DATE = #{manager_certi_start_date, jdbcType=DATE} ,
			POST_CODE = #{post_code, jdbcType=VARCHAR} ,
		    HOLDER_CERTI_END_DATE = #{holder_certi_end_date, jdbcType=DATE} ,
			TAX_CODE = #{tax_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			LEGAL_PERSON_NAME = #{legal_person_name, jdbcType=VARCHAR} ,
			CITY = #{city, jdbcType=VARCHAR} ,
			STATE = #{state, jdbcType=VARCHAR} ,
		    TAX_CODE_END_DATE = #{tax_code_end_date, jdbcType=DATE} ,
			REG_CAPITAL = #{reg_capital, jdbcType=VARCHAR} ,
			COMPANY_NAME = #{company_name, jdbcType=VARCHAR} ,
			COMPANY_HOLDER_CERTI_CODE = #{company_holder_certi_code, jdbcType=VARCHAR} ,
			REG_CAPITAL_CURRENCY = #{reg_capital_currency, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			COMPANY_ORG_CODE = #{company_org_code, jdbcType=VARCHAR} ,
			COMPANY_ADDRESS = #{company_address, jdbcType=VARCHAR} ,
			DISTRICT = #{district, jdbcType=VARCHAR} ,
			HOLDE_RATIO = #{holde_ratio, jdbcType=VARCHAR} ,
			COUNTRY = #{country, jdbcType=VARCHAR} ,
			ADDRESS = #{address, jdbcType=VARCHAR} ,
		    OPERATOR_CERTI_END_DATE = #{operator_certi_end_date, jdbcType=DATE} ,
		    LEGAL_PER_CERTI_END_DATE = #{legal_per_certi_end_date, jdbcType=DATE} ,
			BENE_JUDGE_WAY = #{bene_judge_way, jdbcType=VARCHAR} ,
			COMPANY_HOLDER_CERTI_TYPE = #{company_holder_certi_type, jdbcType=VARCHAR} ,
		    HOLDER_CERTI_START_DATE = #{holder_certi_start_date, jdbcType=DATE} ,
			MOBILE = #{mobile, jdbcType=VARCHAR} ,
			BUSI_LICENCE_CODE = #{busi_licence_code, jdbcType=VARCHAR} ,
		    LICENCE_END_DATE = #{licence_end_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE  = 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsPayCompany" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.* FROM (SELECT A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.HOLDING_PERSON_NAME, A.MANAGER_CERTI_END_DATE, A.OPERATOR_CERTI_START_DATE, A.REMARK, A.SOCIAL_SECURITY_CODE, 
			A.OPERATOR_CERTI_CODE, A.ORG_CODE_END_DATE, A.LICENCE_START_DATE, A.COMPANY_INDUSTRY, A.MANAGER_NAME, A.LEGAL_PERSON_CERTI_TYPE, 
			A.BUSINESS_COPE, A.OPERATOR_NAME, A.LEGAL_PER_CERTI_START_DATE, A.MANAGER_CERTI_CODE, A.MANAGER_CERTI_TYPE, A.ORG_CODE_START_DATE, 
			A.LEGAL_PERSON_CERTI_CODE, A.MANAGER_CERTI_START_DATE, A.POST_CODE, A.HOLDER_CERTI_END_DATE, A.TAX_CODE, A.LEGAL_PERSON_NAME, 
			A.CITY, A.STATE, A.TAX_CODE_END_DATE, A.REG_CAPITAL, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, 
			A.REG_CAPITAL_CURRENCY, A.LIST_ID, A.COMPANY_ORG_CODE, A.COMPANY_ADDRESS, A.DISTRICT, A.HOLDE_RATIO, 
			A.COUNTRY, A.ADDRESS, A.OPERATOR_CERTI_END_DATE, A.LEGAL_PER_CERTI_END_DATE, A.BENE_JUDGE_WAY, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.HOLDER_CERTI_START_DATE, A.MOBILE, A.BUSI_LICENCE_CODE, A.LICENCE_END_DATE FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsPayCompanyCondition" />
		
		<![CDATA[ ORDER BY A.LIST_ID DESC) T WHERE ROWNUM = 1 ]]>
	</select>
	
<!-- 	<select id="CUS_findCsPayCompanyByCompanyName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.HOLDING_PERSON_NAME, A.MANAGER_CERTI_END_DATE, A.OPERATOR_CERTI_START_DATE, A.REMARK, A.SOCIAL_SECURITY_CODE, 
			A.OPERATOR_CERTI_CODE, A.ORG_CODE_END_DATE, A.LICENCE_START_DATE, A.COMPANY_INDUSTRY, A.MANAGER_NAME, A.LEGAL_PERSON_CERTI_TYPE, 
			A.BUSINESS_COPE, A.OPERATOR_NAME, A.LEGAL_PER_CERTI_START_DATE, A.MANAGER_CERTI_CODE, A.MANAGER_CERTI_TYPE, A.ORG_CODE_START_DATE, 
			A.LEGAL_PERSON_CERTI_CODE, A.MANAGER_CERTI_START_DATE, A.POST_CODE, A.HOLDER_CERTI_END_DATE, A.TAX_CODE, A.LEGAL_PERSON_NAME, 
			A.CITY, A.STATE, A.TAX_CODE_END_DATE, A.REG_CAPITAL, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, 
			A.REG_CAPITAL_CURRENCY, A.LIST_ID, A.COMPANY_ORG_CODE, A.COMPANY_ADDRESS, A.DISTRICT, A.HOLDE_RATIO, 
			A.COUNTRY, A.ADDRESS, A.OPERATOR_CERTI_END_DATE, A.LEGAL_PER_CERTI_END_DATE, A.BENE_JUDGE_WAY, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.HOLDER_CERTI_START_DATE, A.MOBILE, A.BUSI_LICENCE_CODE, A.LICENCE_END_DATE FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsPayCompanyByCompanyNameCondition" />
	</select> -->
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsPayCompany" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.HOLDING_PERSON_NAME, A.MANAGER_CERTI_END_DATE, A.OPERATOR_CERTI_START_DATE, A.REMARK, A.SOCIAL_SECURITY_CODE, 
			A.OPERATOR_CERTI_CODE, A.ORG_CODE_END_DATE, A.LICENCE_START_DATE, A.COMPANY_INDUSTRY, A.MANAGER_NAME, A.LEGAL_PERSON_CERTI_TYPE, 
			A.BUSINESS_COPE, A.OPERATOR_NAME, A.LEGAL_PER_CERTI_START_DATE, A.MANAGER_CERTI_CODE, A.MANAGER_CERTI_TYPE, A.ORG_CODE_START_DATE, 
			A.LEGAL_PERSON_CERTI_CODE, A.MANAGER_CERTI_START_DATE, A.POST_CODE, A.HOLDER_CERTI_END_DATE, A.TAX_CODE, A.LEGAL_PERSON_NAME, 
			A.CITY, A.STATE, A.TAX_CODE_END_DATE, A.REG_CAPITAL, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, 
			A.REG_CAPITAL_CURRENCY, A.LIST_ID, A.COMPANY_ORG_CODE, A.COMPANY_ADDRESS, A.DISTRICT, A.HOLDE_RATIO, 
			A.COUNTRY, A.ADDRESS, A.OPERATOR_CERTI_END_DATE, A.LEGAL_PER_CERTI_END_DATE, A.BENE_JUDGE_WAY, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.HOLDER_CERTI_START_DATE, A.MOBILE, A.BUSI_LICENCE_CODE, A.LICENCE_END_DATE FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_queryCsPayCompanyCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsPayCompany" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.HOLDING_PERSON_NAME, A.MANAGER_CERTI_END_DATE, A.OPERATOR_CERTI_START_DATE, A.REMARK, A.SOCIAL_SECURITY_CODE, 
			A.OPERATOR_CERTI_CODE, A.ORG_CODE_END_DATE, A.LICENCE_START_DATE, A.COMPANY_INDUSTRY, A.MANAGER_NAME, A.LEGAL_PERSON_CERTI_TYPE, 
			A.BUSINESS_COPE, A.OPERATOR_NAME, A.LEGAL_PER_CERTI_START_DATE, A.MANAGER_CERTI_CODE, A.MANAGER_CERTI_TYPE, A.ORG_CODE_START_DATE, 
			A.LEGAL_PERSON_CERTI_CODE, A.MANAGER_CERTI_START_DATE, A.POST_CODE, A.HOLDER_CERTI_END_DATE, A.TAX_CODE, A.LEGAL_PERSON_NAME, 
			A.CITY, A.STATE, A.TAX_CODE_END_DATE, A.REG_CAPITAL, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, 
			A.REG_CAPITAL_CURRENCY, A.LIST_ID, A.COMPANY_ORG_CODE, A.COMPANY_ADDRESS, A.DISTRICT, A.HOLDE_RATIO, 
			A.COUNTRY, A.ADDRESS, A.OPERATOR_CERTI_END_DATE, A.LEGAL_PER_CERTI_END_DATE, A.BENE_JUDGE_WAY, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.HOLDER_CERTI_START_DATE, A.MOBILE, A.BUSI_LICENCE_CODE, A.LICENCE_END_DATE FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_queryCsPayCompanyCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCsPayCompanyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsPayCompanyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TAX_CODE_START_DATE, B.OPERATOR_CERTI_TYPE, B.HOLDING_PERSON_NAME, B.MANAGER_CERTI_END_DATE, B.OPERATOR_CERTI_START_DATE, B.REMARK, B.SOCIAL_SECURITY_CODE, 
			B.OPERATOR_CERTI_CODE, B.ORG_CODE_END_DATE, B.LICENCE_START_DATE, B.COMPANY_INDUSTRY, B.MANAGER_NAME, B.LEGAL_PERSON_CERTI_TYPE, 
			B.BUSINESS_COPE, B.OPERATOR_NAME, B.LEGAL_PER_CERTI_START_DATE, B.MANAGER_CERTI_CODE, B.MANAGER_CERTI_TYPE, B.ORG_CODE_START_DATE, 
			B.LEGAL_PERSON_CERTI_CODE, B.MANAGER_CERTI_START_DATE, B.POST_CODE, B.HOLDER_CERTI_END_DATE, B.TAX_CODE, B.LEGAL_PERSON_NAME, 
			B.CITY, B.STATE, B.TAX_CODE_END_DATE, B.REG_CAPITAL, B.COMPANY_NAME, B.COMPANY_HOLDER_CERTI_CODE, 
			B.REG_CAPITAL_CURRENCY, B.LIST_ID, B.COMPANY_ORG_CODE, B.COMPANY_ADDRESS, B.DISTRICT, B.HOLDE_RATIO, 
			B.COUNTRY, B.ADDRESS, B.OPERATOR_CERTI_END_DATE, B.LEGAL_PER_CERTI_END_DATE, B.BENE_JUDGE_WAY, B.COMPANY_HOLDER_CERTI_TYPE, 
			B.HOLDER_CERTI_START_DATE, B.MOBILE, B.BUSI_LICENCE_CODE, B.LICENCE_END_DATE FROM (
					SELECT ROWNUM RN, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.HOLDING_PERSON_NAME, A.MANAGER_CERTI_END_DATE, A.OPERATOR_CERTI_START_DATE, A.REMARK, A.SOCIAL_SECURITY_CODE, 
			A.OPERATOR_CERTI_CODE, A.ORG_CODE_END_DATE, A.LICENCE_START_DATE, A.COMPANY_INDUSTRY, A.MANAGER_NAME, A.LEGAL_PERSON_CERTI_TYPE, 
			A.BUSINESS_COPE, A.OPERATOR_NAME, A.LEGAL_PER_CERTI_START_DATE, A.MANAGER_CERTI_CODE, A.MANAGER_CERTI_TYPE, A.ORG_CODE_START_DATE, 
			A.LEGAL_PERSON_CERTI_CODE, A.MANAGER_CERTI_START_DATE, A.POST_CODE, A.HOLDER_CERTI_END_DATE, A.TAX_CODE, A.LEGAL_PERSON_NAME, 
			A.CITY, A.STATE, A.TAX_CODE_END_DATE, A.REG_CAPITAL, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, 
			A.REG_CAPITAL_CURRENCY, A.LIST_ID, A.COMPANY_ORG_CODE, A.COMPANY_ADDRESS, A.DISTRICT, A.HOLDE_RATIO, 
			A.COUNTRY, A.ADDRESS, A.OPERATOR_CERTI_END_DATE, A.LEGAL_PER_CERTI_END_DATE, A.BENE_JUDGE_WAY, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.HOLDER_CERTI_START_DATE, A.MOBILE, A.BUSI_LICENCE_CODE, A.LICENCE_END_DATE FROM APP___PAS__DBUSER.T_CS_PAY_COMPANY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 根据手机号查询在职/二次增员代理人 -->
	<select id="findAgentCountByMobile"  resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[       
				SELECT count(1)
        		FROM APP___PAS__DBUSER.t_agent ta
       			WHERE ta.agent_status in (1, 2)          
         		AND ta.agent_mobile = #{mobile, jdbcType=VARCHAR}
          ]]>
	</select>
	
</mapper>
