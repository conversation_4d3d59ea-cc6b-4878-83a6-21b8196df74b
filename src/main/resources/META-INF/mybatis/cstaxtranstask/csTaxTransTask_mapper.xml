<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsTaxTransTaskDao">

	<sql id="CUS_csTaxTransTaskWhereCondition">
		<if test=" ph_chg_list_id  != null "><![CDATA[ AND A.PH_CHG_LIST_ID = #{ph_chg_list_id} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" cs_send_status != null and cs_send_status != ''  "><![CDATA[ AND A.CS_SEND_STATUS = #{cs_send_status} ]]></if>
		<if test=" is_prem  != null "><![CDATA[ AND A.IS_PREM = #{is_prem} ]]></if>
		<if test=" conf_no_status != null and conf_no_status != ''  "><![CDATA[ AND A.CONF_NO_STATUS = #{conf_no_status} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" ph_endor_seqno != null and ph_endor_seqno != ''  "><![CDATA[ AND A.PH_ENDOR_SEQNO = #{ph_endor_seqno} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" ph_send_status != null and ph_send_status != ''  "><![CDATA[ AND A.PH_SEND_STATUS = #{ph_send_status} ]]></if>
		<if test=" tax_code != null and tax_code != ''  "><![CDATA[ AND A.TAX_CODE = #{tax_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" endor_confirmno != null and endor_confirmno != ''  "><![CDATA[ AND A.ENDOR_CONFIRMNO = #{endor_confirmno} ]]></if>
		<if test=" cancel_send_status != null and cancel_send_status != ''  "><![CDATA[ AND A.CANCEL_SEND_STATUS = #{cancel_send_status} ]]></if>
		<if test=" ins_send_status != null and ins_send_status != ''  "><![CDATA[ AND A.INS_SEND_STATUS = #{ins_send_status} ]]></if>
		<if test=" ins_booking_sq_no != null and ins_booking_sq_no != ''  "><![CDATA[ AND A.INS_BOOKING_SQ_NO = #{ins_booking_sq_no} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="CUS_queryCsTaxTransTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	<sql id="CUS_queryCsTaxTransTaskByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>
	<sql id="CUS_queryCsTaxTransTaskByAcceptIdCondition">
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
	</sql>
	<sql id="CUS_queryCsTaxTransTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="CUS_addCsTaxTransTask" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_TAX_TRANS_TASK__LISID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK(
				PH_CHG_LIST_ID, ACCEPT_ID, CS_SEND_STATUS, INSERT_TIME, IS_PREM, UPDATE_TIME,
				CONF_NO_STATUS, ACCEPT_CODE, PH_ENDOR_SEQNO, INSERT_TIMESTAMP, POLICY_CODE, PH_SEND_STATUS,
				UPDATE_BY, TAX_CODE, LIST_ID, ENDOR_CONFIRMNO, UPDATE_TIMESTAMP, INSERT_BY, CANCEL_SEND_STATUS , INS_SEND_STATUS, INS_BOOKING_SQ_NO) 
			VALUES (
				#{ph_chg_list_id, jdbcType=NUMERIC}, #{accept_id, jdbcType=NUMERIC} , #{cs_send_status, jdbcType=VARCHAR} , SYSDATE , #{is_prem, jdbcType=NUMERIC} , SYSDATE
				, #{conf_no_status, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} , #{ph_endor_seqno, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{ph_send_status, jdbcType=VARCHAR}
				, #{update_by, jdbcType=NUMERIC} , #{tax_code, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{endor_confirmno, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{cancel_send_status, jdbcType=VARCHAR} 
				, #{ins_send_status, jdbcType=VARCHAR}, #{ins_booking_sq_no, jdbcType=VARCHAR})
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="CUS_deleteCsTaxTransTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK WHERE 1 = 1 ]]>
		<include refid="CUS_csTaxTransTaskWhereCondition" />
	</delete>

	<!-- 修改操作 -->
	<update id="CUS_updateCsTaxTransTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A]]>
		<set>
			<trim suffixOverrides=",">
				A.PH_CHG_LIST_ID = #{ph_chg_list_id, jdbcType=NUMERIC} ,
				A.ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} ,
				A.CS_SEND_STATUS = #{cs_send_status, jdbcType=VARCHAR} ,
				A.IS_PREM = #{is_prem, jdbcType=NUMERIC} ,
				A.UPDATE_TIME = SYSDATE ,
				A.CONF_NO_STATUS = #{conf_no_status, jdbcType=VARCHAR} ,
				A.ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
				A.PH_ENDOR_SEQNO = #{ph_endor_seqno, jdbcType=VARCHAR} ,
				A.POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				A.PH_SEND_STATUS = #{ph_send_status, jdbcType=VARCHAR} ,
				A.UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				A.TAX_CODE = #{tax_code, jdbcType=VARCHAR} ,
				A.LIST_ID = #{list_id, jdbcType=NUMERIC} ,
				A.ENDOR_CONFIRMNO = #{endor_confirmno, jdbcType=VARCHAR} ,
				A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				A.CANCEL_SEND_STATUS = #{cancel_send_status, jdbcType=VARCHAR} ,
				A.INS_SEND_STATUS = #{ins_send_status, jdbcType=VARCHAR} ,
				A.INS_BOOKING_SQ_NO = #{ins_booking_sq_no, jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[where A.LIST_ID = #{list_id, jdbcType=NUMERIC}]]>
	</update>
	
	<select id="CUS_findCsTaxTransTask" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_csTaxTransTaskWhereCondition" />
	</select>
	
	<select id="CUS_findCsTaxTransTaskSerierial" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT APP___PAS__DBUSER.S_CS_TAX_TRANS_TASK__LISID.NEXTVAL FROM DUAL
		 ]]>
	</select>
	
	<select id="CUS_findBeforeCsTaxTransTask" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[select count(1)
                  FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A
                 where 1 = 1
                   and ((A.CS_SEND_STATUS in ('03') or A.CS_SEND_STATUS IS NULL)
                   or (A.Ph_Send_Status in ('01','03') or A.Ph_Send_Status IS NULL)
                   or (A.Ins_Send_Status in ('01','03') or A.Ins_Send_Status IS NULL))
                   and A.list_id < #{list_id}
                   and A.policy_code = #{policy_code}
		 ]]>
	</select>

	<!-- 按索引查询操作 -->
	<select id="CUS_findCsTaxTransTaskByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsTaxTransTaskByListIdCondition" />
	</select>

	<select id="CUS_findCsTaxTransTaskByAcceptCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsTaxTransTaskByAcceptCodeCondition" />
	</select>

	<select id="CUS_findCsTaxTransTaskByAcceptId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsTaxTransTaskByAcceptIdCondition" />
	</select>

	<select id="CUS_findCsTaxTransTaskByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsTaxTransTaskByPolicyCodeCondition" />
	</select>


	<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsTaxTransTask" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csTaxTransTaskWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="CUS_findAllCsTaxTransTask" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS ,A.INSERT_TIME, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1 = 1 ]]>
			<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.LIST_ID, #{modnum}) = #{start} ]]></if>
		<include refid="CUS_csTaxTransTaskWhereCondition" />
		<![CDATA[ ORDER BY A.INSERT_TIME DESC ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="CUS_findCsTaxTransTaskTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_csTaxTransTaskWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="CUS_queryCsTaxTransTaskForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PH_CHG_LIST_ID, B.ACCEPT_ID, B.CS_SEND_STATUS, B.IS_PREM,
			B.CONF_NO_STATUS, B.ACCEPT_CODE, B.PH_ENDOR_SEQNO, B.POLICY_CODE, B.PH_SEND_STATUS,
			B.TAX_CODE, B.LIST_ID, B.ENDOR_CONFIRMNO, B.CANCEL_SEND_STATUS, B.INS_SEND_STATUS, B.INS_BOOKING_SQ_NO FROM (
					SELECT ROWNUM RN, A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_csTaxTransTaskWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="CUS_findAllCsTaxTransTaskForJob" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS, A.INS_SEND_STATUS, A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE  A.IS_PREM IS NULL 		]]>
		<include refid="CUS_csTaxTransTaskWhereCondition"/>
		<![CDATA[
			   and mod(a.list_id , #{modnum}) = #{start}
		]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="CUS_findAllCanscelTaskForJob" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			select a.ph_chg_list_id,
			       a.accept_id,
			       a.cs_send_status,
			       a.is_prem,
			       a.conf_no_status,
			       a.accept_code,
			       a.ph_endor_seqno,
			       a.policy_code,
			       a.ph_send_status,
			       a.tax_code,
			       a.list_id,
			       a.endor_confirmno,
			       a.cancel_send_status,
			       a.ins_send_status,
			       a.ins_booking_sq_no
			  from app___pas__dbuser.t_cs_tax_trans_task a
			 where a.cancel_send_status in ('03','06','09')
			   ]]>
		<include refid="CUS_csTaxTransTaskWhereCondition"/>
		<![CDATA[
			   and mod(a.list_id , #{modnum}) = #{start}
		]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="CUS_findAllCsTaxTransTask1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PH_CHG_LIST_ID, A.ACCEPT_ID, A.CS_SEND_STATUS, A.IS_PREM,
			A.CONF_NO_STATUS, A.ACCEPT_CODE, A.PH_ENDOR_SEQNO, A.POLICY_CODE, A.PH_SEND_STATUS,
			A.TAX_CODE, A.LIST_ID, A.ENDOR_CONFIRMNO, A.CANCEL_SEND_STATUS ,A.INSERT_TIME,A.INS_SEND_STATUS,A.INS_BOOKING_SQ_NO FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A WHERE 1=1]]>
			<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.LIST_ID, #{modnum}) = #{start} ]]></if>
		<include refid="CUS_csTaxTransTaskWhereCondition" />
		<![CDATA[ ORDER BY A.INSERT_TIME DESC ]]>
	</select>
	
	<!--税优 根据受理变更id查询报送数据数目 -->
	<select  id="findTAXInvalidPolicyByAcceptid" resultType="java.lang.Integer" parameterType="java.util.Map">
	 		<![CDATA[
	 			SELECT COUNT(1)
				  FROM DEV_PAS.T_CS_POLICY_CHANGE P,
				       DEV_PAS.T_CONTRACT_MASTER C
				 WHERE P.POLICY_CODE = C.POLICY_CODE
				   AND P.ACCEPT_ID = #{accept_id}
				   AND P.POLICY_CODE = #{policy_code}
				   AND C.Special_Account_Flag = '2'
	 		]]>
	</select>

	<!--税优 根据受理变更id查询报送数据数目 -->
	<select  id="PA_TakeSeririalId" resultType="java.lang.Integer" parameterType="java.util.Map">
	 		<![CDATA[
		SELECT APP___PAS__DBUSER.S_CS_TAX_TRANS_TASK__LISID.NEXTVAL FROM DUAL
		]]>
	</select>

	<!-- 税优保单获取保全确认码批处理任务查询 包含涉费确认码报送状态为报送失败的和保全项目为新增长期附加险的待报送-->
	<select id="CUS_findAllCsTaxTransTaskForEND025" resultType="java.util.Map"
			parameterType="java.util.Map">
		<![CDATA[
			SELECT T.PH_CHG_LIST_ID,
				   T.ACCEPT_ID,
				   T.CS_SEND_STATUS,
				   T.IS_PREM,
				   T.CONF_NO_STATUS,
				   T.ACCEPT_CODE,
				   T.PH_ENDOR_SEQNO,
				   T.POLICY_CODE,
				   T.PH_SEND_STATUS,
				   T.TAX_CODE,
				   T.LIST_ID,
				   T.ENDOR_CONFIRMNO,
				   T.CANCEL_SEND_STATUS,
				   T.INSERT_TIME,
				   T.INS_SEND_STATUS,
				   T.INS_BOOKING_SQ_NO
			  FROM (SELECT A.PH_CHG_LIST_ID,
						   A.ACCEPT_ID,
						   A.CS_SEND_STATUS,
						   A.IS_PREM,
						   A.CONF_NO_STATUS,
						   A.ACCEPT_CODE,
						   A.PH_ENDOR_SEQNO,
						   A.POLICY_CODE,
						   A.PH_SEND_STATUS,
						   A.TAX_CODE,
						   A.LIST_ID,
						   A.ENDOR_CONFIRMNO,
						   A.CANCEL_SEND_STATUS,
						   A.INSERT_TIME,
						   A.INS_SEND_STATUS,
						   A.INS_BOOKING_SQ_NO
					  FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A
					 WHERE A.CONF_NO_STATUS = '03'
					   AND A.IS_PREM = '1'
		]]>
					 <if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
					 <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[
					UNION
					SELECT A.PH_CHG_LIST_ID,
						   A.ACCEPT_ID,
						   A.CS_SEND_STATUS,
						   A.IS_PREM,
						   A.CONF_NO_STATUS,
						   A.ACCEPT_CODE,
						   A.PH_ENDOR_SEQNO,
						   A.POLICY_CODE,
						   A.PH_SEND_STATUS,
						   A.TAX_CODE,
						   A.LIST_ID,
						   A.ENDOR_CONFIRMNO,
						   A.CANCEL_SEND_STATUS,
						   A.INSERT_TIME,
						   A.INS_SEND_STATUS,
						   A.INS_BOOKING_SQ_NO
					  FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_TASK A
					 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
						ON TCAC.ACCEPT_ID = A.ACCEPT_ID
					 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
						ON TCAC.ACCEPT_ID = B.ACCEPT_ID
					 INNER JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL C
						ON B.POLICY_CHG_ID = C.POLICY_CHG_ID
					 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT D
						ON C.BUSI_PRD_ID = D.BUSINESS_PRD_ID
					 WHERE 1 = 1
					   AND C.OLD_NEW = '1'
					   AND C.OPERATION_TYPE = '1'
					   AND D.COVER_PERIOD_TYPE = '0'
					   AND D.PRODUCT_CATEGORY = '10002'
					   AND A.CONF_NO_STATUS = '01'
					   AND A.IS_PREM = '1'
		]]>
					   <if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
					   <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[		) T
		]]>
		<if test=" modnum != null and start != null  "><![CDATA[ WHERE MOD(T.LIST_ID, #{modnum}) = #{start} ]]></if>
	</select>

</mapper>
