<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsVpOptionListDao">
<sql id="csVpOptionListWhereCondition">
	<if test=" task_code != null and task_code != ''  "><![CDATA[ AND A.TASK_CODE = #{task_code} ]]></if>
	<if test=" task_name != null and task_name != ''  "><![CDATA[ AND A.TASK_NAME = #{task_name} ]]></if>
</sql>



<!-- 添加操作 -->
	<insert id="addCsVpOptionList"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO DEV_PAS.T_CS_VP_OPTION_LIST(TASK_CODE, TASK_NAME ) 
			VALUES (#{task_code, jdbcType=VARCHAR}, #{task_name, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsVpOptionList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CS_VP_OPTION_LIST WHERE TASK_CODE = #{task_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsVpOptionList" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CS_VP_OPTION_LIST ]]>
		<set>
		<trim suffixOverrides=",">
			TASK_CODE = #{task_code, jdbcType=VARCHAR} ,
			TASK_NAME = #{task_name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  = #{task_code, jdbcType=VARCHAR} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsVpOptionList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_CODE, A.TASK_NAME FROM DEV_PAS.T_CS_VP_OPTION_LIST A WHERE 1 = 1  ]]>
		<include refid="csVpOptionListWhereCondition" />
	</select>
	


<!-- 查询所有操作 -->
	<select id="findAllCsVpOptionList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_CODE, A.TASK_NAME FROM DEV_PAS.T_CS_VP_OPTION_LIST A WHERE ROWNUM <=  1000  ]]>
		<include refid="csVpOptionListWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCsVpOptionListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_VP_OPTION_LIST A WHERE 1 = 1  ]]>
		<include refid="csVpOptionListWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsVpOptionListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TASK_CODE, B.TASK_NAME FROM (
					SELECT ROWNUM RN, A.TASK_CODE, A.TASK_NAME FROM DEV_PAS.T_CS_VP_OPTION_LIST A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="csVpOptionListWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
