<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IAccidentBusiProdCfgDao">

	<sql id="CUS_accidentBusiProdCfgWhereCondition">
		<if test=" change_date  != null  and  change_date  != ''  "><![CDATA[ AND A.CHANGE_DATE = #{change_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryAccidentBusiProdCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addAccidentBusiProdCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT S_T_ACC_BUSI_PROD_CFG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG(
				INSERT_TIMESTAMP, UPDATE_BY, INSERT_TIME, CHANGE_DATE, LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, 
				INSERT_BY, BUSI_PROD_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , SYSDATE , #{change_date, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE 
				, #{insert_by, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteAccidentBusiProdCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG WHERE  A.LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateAccidentBusiProdCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_DATE = #{change_date, jdbcType=DATE} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  A.LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findAccidentBusiProdCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryAccidentBusiProdCfgByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapAccidentBusiProdCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_accidentBusiProdCfgWhereCondition" /> 
	</select>

<!-- 查询所有操作 -->
	<select id="findAllAccidentBusiProdCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANGE_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_accidentBusiProdCfgWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findAccidentBusiProdCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_accidentBusiProdCfgWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryAccidentBusiProdCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CHANGE_DATE, B.LIST_ID, 
			B.BUSI_PROD_CODE FROM (
					SELECT ROWNUM RN, A.CHANGE_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_accidentBusiProdCfgWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 查询单条数据 -->
	<select id="Cus_findAccidentBusiProdCfg" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ 
		   select A.LIST_ID,A.BUSI_PROD_CODE,A.CHANGE_DATE FROM APP___PAS__DBUSER.T_ACCIDENT_BUSI_PROD_CFG A 
		   WHERE 1 = 1 
		]]>
		<include refid="CUS_accidentBusiProdCfgWhereCondition" />
	</select>
	
</mapper>
