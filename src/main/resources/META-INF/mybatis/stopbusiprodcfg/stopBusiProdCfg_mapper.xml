<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IStopBusiProdCfgDao">

	<sql id="CUS_stopBusiProdCfgWhereCondition">
		<if test=" stop_date  != null  and  stop_date  != ''  "><![CDATA[ AND A.STOP_DATE = #{stop_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryStopBusiProdCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	
	<!--查询单条 -->
		<select id="findStopBusiProdCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STOP_DATE, A.LIST_ID, 
			A.SERVICE_CODE, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_stopBusiProdCfgWhereCondition" />
	</select>

<!-- 添加操作 -->
	<insert id="CUS_addStopBusiProdCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT APP___PAS__DBUSER.S_T_STOP_BUS_PROD_CFG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG(
				INSERT_TIMESTAMP, UPDATE_BY, STOP_DATE, INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, 
				INSERT_BY, SERVICE_CODE, BUSI_PROD_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{stop_date, jdbcType=DATE} , SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE 
				, #{insert_by, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteStopBusiProdCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG WHERE  LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateStopBusiProdCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    STOP_DATE = #{stop_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id, jdbcType=NUMERIC}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findStopBusiProdCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STOP_DATE, A.LIST_ID, 
			A.SERVICE_CODE, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryStopBusiProdCfgByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapStopBusiProdCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STOP_DATE, A.LIST_ID, 
			A.SERVICE_CODE, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllStopBusiProdCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STOP_DATE, A.LIST_ID, 
			A.SERVICE_CODE, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findStopBusiProdCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryStopBusiProdCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.STOP_DATE, B.LIST_ID, 
			B.SERVICE_CODE, B.BUSI_PROD_CODE FROM (
					SELECT ROWNUM RN, A.STOP_DATE, A.LIST_ID, 
			A.SERVICE_CODE, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findStopBusiProdByCodeAndDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT listagg(A.BUSI_PROD_CODE,',') WITHIN GROUP (ORDER BY A.BUSI_PROD_CODE) BUSI_PROD_CODE 
		FROM APP___PAS__DBUSER.T_STOP_BUSI_PROD_CFG A 
		WHERE A.SERVICE_CODE = 'NS' 
		AND sysdate >= STOP_DATE
		]]>
	</select>
	
</mapper>
