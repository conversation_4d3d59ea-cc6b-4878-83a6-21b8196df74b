<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsCheckConfigDao">
<!--
	<sql id="csCheckConfigWhereCondition">
		<if test=" check_code1 != null and check_code1 != ''  "><![CDATA[ AND A.CHECK_CODE1 = #{check_code1} ]]></if>
		<if test=" check_code2 != null and check_code2 != ''  "><![CDATA[ AND A.CHECK_CODE2 = #{check_code2} ]]></if>
		<if test=" check_name1 != null and check_name1 != ''  "><![CDATA[ AND A.CHECK_NAME1 = #{check_name1} ]]></if>
		<if test=" check_code3 != null and check_code3 != ''  "><![CDATA[ AND <PERSON>.<PERSON>_CODE3 = #{check_code3} ]]></if>
		<if test=" check_type != null and check_type != ''  "><![CDATA[ AND A.CHECK_TYPE = #{check_type} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" check_desc != null and check_desc != ''  "><![CDATA[ AND A.CHECK_DESC = #{check_desc} ]]></if>
		<if test=" check_name3 != null and check_name3 != ''  "><![CDATA[ AND A.CHECK_NAME3 = #{check_name3} ]]></if>
		<if test=" check_name2 != null and check_name2 != ''  "><![CDATA[ AND A.CHECK_NAME2 = #{check_name2} ]]></if>
		<if test=" check_code4 != null and check_code4 != ''  "><![CDATA[ AND A.CHECK_CODE4 = #{check_code4} ]]></if>
		<if test=" check_name4 != null and check_name4 != ''  "><![CDATA[ AND A.CHECK_NAME4 = #{check_name4} ]]></if>
	</sql>
-->
<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsCheckConfigByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsCheckConfig"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="">
			SELECT S_CS_CHECK_CONFIG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_CS_CHECK_CONFIG(
				CHECK_CODE1, CHECK_CODE2, CHECK_NAME1, CHECK_CODE3, INSERT_TIME, UPDATE_TIME, INSERT_TIMESTAMP, 
				UPDATE_BY, CHECK_TYPE, LIST_ID, CHECK_DESC, UPDATE_TIMESTAMP, CHECK_NAME3, INSERT_BY, 
				CHECK_NAME2, CHECK_CODE4, CHECK_NAME4 ) 
			VALUES (
				#{check_code1, jdbcType=VARCHAR}, #{check_code2, jdbcType=VARCHAR} , #{check_name1, jdbcType=VARCHAR} , #{check_code3, jdbcType=VARCHAR} , SYSDATE , SYSDATE , CURRENT_TIMESTAMP
				, #{update_by, jdbcType=NUMERIC} , #{check_type, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{check_desc, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{check_name3, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} 
				, #{check_name2, jdbcType=VARCHAR} , #{check_code4, jdbcType=VARCHAR} , #{check_name4, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsCheckConfig" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CS_CHECK_CONFIG WHERE LIST_ID = #{LIST_ID} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsCheckConfig" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_CS_CHECK_CONFIG ]]>
		<set>
		<trim suffixOverrides=",">
			CHECK_CODE1 = #{check_code1, jdbcType=VARCHAR} ,
			CHECK_CODE2 = #{check_code2, jdbcType=VARCHAR} ,
			CHECK_NAME1 = #{check_name1, jdbcType=VARCHAR} ,
			CHECK_CODE3 = #{check_code3, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			CHECK_TYPE = #{check_type, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			CHECK_DESC = #{check_desc, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CHECK_NAME3 = #{check_name3, jdbcType=VARCHAR} ,
			CHECK_NAME2 = #{check_name2, jdbcType=VARCHAR} ,
			CHECK_CODE4 = #{check_code4, jdbcType=VARCHAR} ,
			CHECK_NAME4 = #{check_name4, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{LIST_ID} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsCheckConfigByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_CODE1, A.CHECK_CODE2, A.CHECK_NAME1, A.CHECK_CODE3, 
			A.CHECK_TYPE, A.LIST_ID, A.CHECK_DESC, A.CHECK_NAME3, 
			A.CHECK_NAME2, A.CHECK_CODE4, A.CHECK_NAME4 FROM T_CS_CHECK_CONFIG A WHERE 1 = 1  ]]>
		<include refid="queryCsCheckConfigByListIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsCheckConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_CODE1, A.CHECK_CODE2, A.CHECK_NAME1, A.CHECK_CODE3, 
			A.CHECK_TYPE, A.LIST_ID, A.CHECK_DESC, A.CHECK_NAME3, 
			A.CHECK_NAME2, A.CHECK_CODE4, A.CHECK_NAME4 FROM T_CS_CHECK_CONFIG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsCheckConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_CODE1, A.CHECK_CODE2, A.CHECK_NAME1, A.CHECK_CODE3, 
			A.CHECK_TYPE, A.LIST_ID, A.CHECK_DESC, A.CHECK_NAME3, 
			A.CHECK_NAME2, A.CHECK_CODE4, A.CHECK_NAME4 FROM T_CS_CHECK_CONFIG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="findCsCheckConfigTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CS_CHECK_CONFIG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsCheckConfigForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CHECK_CODE1, B.CHECK_CODE2, B.CHECK_NAME1, B.CHECK_CODE3, 
			B.CHECK_TYPE, B.LIST_ID, B.CHECK_DESC, B.CHECK_NAME3, 
			B.CHECK_NAME2, B.CHECK_CODE4, B.CHECK_NAME4 FROM (
					SELECT ROWNUM RN, A.CHECK_CODE1, A.CHECK_CODE2, A.CHECK_NAME1, A.CHECK_CODE3, 
			A.CHECK_TYPE, A.LIST_ID, A.CHECK_DESC, A.CHECK_NAME3, 
			A.CHECK_NAME2, A.CHECK_CODE4, A.CHECK_NAME4 FROM T_CS_CHECK_CONFIG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 通过changeId和customerId 获取邮箱对应分公司配置 -->
	<select id="findCsCheckConfigByCustomerIdAndChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
		       min(t.CHECK_CODE2) max_num
		  from APP___PAS__DBUSER.T_CONTRACT_MASTER cm,
		       APP___PAS__DBUSER.t_cs_check_config t
		 where 1 = 1
		   and t.CHECK_TYPE = 'EMAIL_ORAGN_NUM'
		   and cm.branch_code = t.CHECK_CODE1
		   and exists (select 1
		          from APP___PAS__DBUSER.T_POLICY_HOLDER ph
		         where cm.policy_id = ph.policy_id
		           and ph.customer_id = #{customer_id})
		   and exists (select 1
		          from APP___PAS__DBUSER.t_cs_policy_change pc
		         where pc.policy_id = cm.policy_id
		           and pc.accept_id = #{accept_id})
		]]>
	</select>
	<!-- 根据机构id获取当前对应分公司配置 -->
	<select id = "findsCheckConfigByCheckCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select distinct t.LIST_ID,
		       t.CHECK_TYPE,
		       t.CHECK_DESC,
		       t.CHECK_CODE1,
		       t.CHECK_NAME1,
		       t.CHECK_CODE2,
		       t.CHECK_NAME2,
		       t.CHECK_CODE3,
		       t.CHECK_NAME3,
		       t.CHECK_CODE4,
		       t.CHECK_NAME4,
		       t.INSERT_BY,
		       t.INSERT_TIME,
		       t.INSERT_TIMESTAMP,
		       t.UPDATE_BY,
		       t.UPDATE_TIME,
		       t.UPDATE_TIMESTAMP
			from APP___PAS__DBUSER.t_cs_check_config t
			where 1 = 1
			and t.CHECK_TYPE = 'EMAIL_ORAGN_NUM'
			and t.CHECK_CODE1 = #{check_code1}
		]]>
	</select>
</mapper>
