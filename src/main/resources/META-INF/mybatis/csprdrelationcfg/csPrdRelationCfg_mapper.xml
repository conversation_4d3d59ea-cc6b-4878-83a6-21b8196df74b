<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsPrdRelationCfgDao">

	<sql id="csPrdRelationCfgWhereCondition">
		<if test=" prem_busi_prod != null and prem_busi_prod != ''  "><![CDATA[ AND A.PREM_BUSI_PROD = #{prem_busi_prod} ]]></if>
		<if test=" is_ybt  != null "><![CDATA[ AND (A.IS_YBT = #{is_ybt} OR A.IS_YBT IS NULL) ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND (A.END_DATE >= #{end_date} OR A.END_DATE IS NULL)]]></if>
		<if test=" prem_total  != null "><![CDATA[ AND A.PREM_TOTAL = #{prem_total} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" relation_type  != null "><![CDATA[ AND A.RELATION_TYPE = #{relation_type} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND (A.START_DATE <= #{start_date} OR A.START_DATE IS NULL)]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND (A.CHANNEL_TYPE = #{channel_type} OR A.CHANNEL_TYPE IS NULL)]]></if>
		<if test=" is_related  != null "><![CDATA[ AND (A.IS_RELATED = #{is_related} OR A.IS_RELATED IS NULL)]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" rel_busi_prod_code != null and rel_busi_prod_code != ''  "><![CDATA[ AND A.REL_BUSI_PROD_CODE = #{rel_busi_prod_code} ]]></if>
		<if test=" charge_year != null and charge_year != ''  "><![CDATA[ AND (INSTR(A.CHARGE_YEAR, #{charge_year}) > 0 OR A.CHARGE_YEAR IS NULL) ]]></if>
		<if test=" is_sale  != null "><![CDATA[ AND (A.IS_SALE = #{is_sale} OR A.IS_SALE IS NULL)]]></if>
	</sql>

<!-- 查询单条操作 -->
	<select id="findCsPrdRelationCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_BUSI_PROD, A.IS_YBT, A.END_DATE, A.PREM_TOTAL, A.BUSI_PROD_CODE, 
			A.RELATION_TYPE, A.START_DATE, A.CHANNEL_TYPE, A.IS_RELATED, A.CFG_ID, 
			A.REL_BUSI_PROD_CODE, A.CHARGE_YEAR, A.IS_SALE FROM DEV_PAS.T_CS_PRD_RELATION_CFG A WHERE ROWNUM <= 1  ]]>
		 <include refid="csPrdRelationCfgWhereCondition" /> 
	</select>
<!-- 查询所有操作 -->
	<select id="findAllCsPrdRelationCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_BUSI_PROD, A.IS_YBT, A.END_DATE, A.PREM_TOTAL, A.BUSI_PROD_CODE, 
			A.RELATION_TYPE, A.START_DATE, A.CHANNEL_TYPE, A.IS_RELATED, A.CFG_ID, 
			A.REL_BUSI_PROD_CODE, A.CHARGE_YEAR, A.IS_SALE FROM DEV_PAS.T_CS_PRD_RELATION_CFG A WHERE ROWNUM <=  1000  ]]>
			<include refid="csPrdRelationCfgWhereCondition" /> 
	</select>

	
</mapper>
