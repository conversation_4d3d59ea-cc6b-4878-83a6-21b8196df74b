<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IcsCustCkManualDao">
<!-- 添加操作 -->
	<insert id="cus_addCsCustCkManualResult"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_CUST_CK_MANUAL__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CUST_CK_MANUAL(
				LIST_ID, ACCEPT_CODE, NAME, CERT_TYPE, CERTI_CODE,CHECK_RESULT,CHECK_NAME,CHECK_TIME,INSERT_TIME,UPDATE_TIME,INSERT_TIMESTAMP, 
				 UPDATE_BY, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{list_id, jdbcType=NUMERIC}, #{accept_code, jdbcType=VARCHAR} , #{name, jdbcType=VARCHAR} , #{cert_type, jdbcType=VARCHAR} , 
				#{certi_code, jdbcType=VARCHAR} ,#{check_result, jdbcType=VARCHAR} ,#{check_name, jdbcType=VARCHAR}, #{check_time, jdbcType=TIMESTAMP}  ,SYSDATE  , SYSDATE , CURRENT_TIMESTAMP,#{update_by, jdbcType=NUMERIC}, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
	<!-- 查询保全验真信息 -->
  <select id="cus_findAllCustCkManualCheckResult" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
      
       SELECT A.LIST_ID,
       A.ACCEPT_CODE,
       A.NAME,
       (select t.type
          from APP___PAS__DBUSER.t_certi_type t
         where t.code = a.CERT_TYPE) as CERT_TYPE,
       A.CERTI_CODE,
       A.CHECK_NAME,
       (case
         when A.CHECK_RESULT = '1' then
          '通过'
         else
          '不通过'
       end) AS CHECK_RESULT,A.CHECK_TIME
  		FROM DEV_PAS.T_CS_CUST_CK_MANUAL A
 		WHERE A.ACCEPT_CODE = #{accept_code} ]]>
  </select>





	
	
	
	
	








	
</mapper>
