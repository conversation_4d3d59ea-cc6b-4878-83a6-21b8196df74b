<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ISurrenderSendInfoDao">
	<sql id="surrenderSendInfoWhereCondition">
		<if test=" error_message != null and error_message != ''  "><![CDATA[ AND A.ERROR_MESSAGE = #{error_message} ]]></if>
		<if test=" request_id  != null "><![CDATA[ AND A.REQUEST_ID = #{request_id} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" send_state != null and send_state != ''  "><![CDATA[ AND A.SEND_STATE = #{send_state} ]]></if>
		<if test=" response_id  != null "><![CDATA[ AND A.RESPONSE_ID = #{response_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" business_type  != null "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="querySurrenderSendInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addSurrenderSendInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SURRENDER_SEND_INFO_LISTID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURRENDER_SEND_INFO(
				INSERT_TIME, ERROR_MESSAGE, UPDATE_TIME, REQUEST_ID, ACCEPT_CODE, SEND_STATE, RESPONSE_ID, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, BUSINESS_TYPE, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				SYSDATE, #{error_message, jdbcType=VARCHAR} , SYSDATE , #{request_id, jdbcType=NUMERIC} , #{accept_code, jdbcType=VARCHAR} , #{send_state, jdbcType=VARCHAR} , #{response_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{business_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSurrenderSendInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO WHERE  = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSurrenderSendInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURRENDER_SEND_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			ERROR_MESSAGE = #{error_message, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    REQUEST_ID = #{request_id, jdbcType=NUMERIC} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			SEND_STATE = #{send_state, jdbcType=VARCHAR} ,
		    RESPONSE_ID = #{response_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    BUSINESS_TYPE = #{business_type, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSurrenderSendInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ERROR_MESSAGE, A.REQUEST_ID, A.ACCEPT_CODE, A.SEND_STATE, A.RESPONSE_ID, 
			A.LIST_ID, A.BUSINESS_TYPE FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO A WHERE 1 = 1  ]]>
		<include refid="querySurrenderSendInfoByListIdCondition" />
	</select>

	<select id="findSurrenderSendInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ERROR_MESSAGE, A.REQUEST_ID, A.ACCEPT_CODE, A.SEND_STATE, A.RESPONSE_ID, 
			A.LIST_ID, A.BUSINESS_TYPE FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO A WHERE 1 = 1  ]]>
		<include refid="surrenderSendInfoWhereCondition" />
		<if test="send_states  != null and send_states.size()!=0">
			<![CDATA[ AND A.SEND_STATE IN (]]>
			<foreach collection="send_states" item="send_state"
					 index="index" open="" close="" separator=",">#{send_state}</foreach>
			<![CDATA[)]]>
		</if>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSurrenderSendInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ERROR_MESSAGE, A.REQUEST_ID, A.ACCEPT_CODE, A.SEND_STATE, A.RESPONSE_ID, 
			A.LIST_ID, A.BUSINESS_TYPE FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="surrenderSendInfoWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSurrenderSendInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ERROR_MESSAGE, A.REQUEST_ID, A.ACCEPT_CODE, A.SEND_STATE, A.RESPONSE_ID, 
			A.LIST_ID, A.BUSINESS_TYPE FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="surrenderSendInfoWhereCondition" />
		<if test="send_states  != null and send_states.size()!=0">
			<![CDATA[ AND A.SEND_STATE IN (]]>
			<foreach collection="send_states" item="send_state"
					 index="index" open="" close="" separator=",">#{send_state}</foreach>
			<![CDATA[)]]>
		</if>
	</select>

<!-- 查询个数操作 -->
	<select id="findSurrenderSendInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO A WHERE 1 = 1  ]]>
		<include refid="surrenderSendInfoWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="querySurrenderSendInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ERROR_MESSAGE, B.REQUEST_ID, B.ACCEPT_CODE, B.SEND_STATE, B.RESPONSE_ID, 
			B.LIST_ID, B.BUSINESS_TYPE FROM (
					SELECT ROWNUM RN, A.ERROR_MESSAGE, A.REQUEST_ID, A.ACCEPT_CODE, A.SEND_STATE, A.RESPONSE_ID, 
			A.LIST_ID, A.BUSINESS_TYPE FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="surrenderSendInfoWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询蚂蚁保退保信息推送批处理后置批处理的数据 -->
	<select id="CS_findCsSurrenderPublicBatchJobTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.RN,
			       B.ERROR_MESSAGE,
			       B.REQUEST_ID,
			       B.ACCEPT_CODE,
			       B.SEND_STATE,
			       B.RESPONSE_ID,
			       B.LIST_ID,
			       B.BUSINESS_TYPE
			  FROM (SELECT ROWNUM AS RN,
			               A.ERROR_MESSAGE,
			               A.REQUEST_ID,
			               A.ACCEPT_CODE,
			               A.SEND_STATE,
			               A.RESPONSE_ID,
			               A.LIST_ID,
			               A.BUSINESS_TYPE
			          FROM APP___PAS__DBUSER.T_SURRENDER_SEND_INFO A
			         WHERE A.BUSINESS_TYPE = #{business_type}
			           AND A.SEND_STATE in ('1','3')]]>
			           <if test=" accept_code  != null and accept_code  != ''"><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
			           <if test=" insert_time  != null and insert_time  != ''"><![CDATA[ AND A.INSERT_TIME >= #{insert_time} ]]></if>
			 <![CDATA[ ORDER BY A.LIST_ID DESC) B
			 WHERE MOD(B.RN, #{modNum}) = #{start}
		]]>
	</select>
	
</mapper>
