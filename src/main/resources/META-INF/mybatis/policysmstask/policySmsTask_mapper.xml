<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicySmsTaskDao">
<!--
	<sql id="PAS_policySmsTaskWhereCondition">
		<if test=" excute_date  != null  and  excute_date  != ''  "><![CDATA[ AND A.EXCUTE_DATE = #{excute_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" serial_number != null and serial_number != ''  "><![CDATA[ AND A.SERIAL_NUMBER = #{serial_number} ]]></if>
		<if test=" business_type  != null "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" sms_info != null and sms_info != ''  "><![CDATA[ AND A.SMS_INFO = #{sms_info} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryPolicySmsTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryPolicySmsTaskByExcuteDateCondition">
		<if test=" excute_date  != null "><![CDATA[ AND A.EXCUTE_DATE = #{excute_date} ]]></if>
	</sql>	
	<sql id="queryPolicySmsTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryPolicySmsTaskBySerialNumberCondition">
		<if test=" serial_number != null and serial_number != '' "><![CDATA[ AND A.SERIAL_NUMBER = #{serial_number} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPolicySmsTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_SMS_TASK.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_SMS_TASK(
				INSERT_TIMESTAMP, EXCUTE_DATE, POLICY_CODE, UPDATE_BY, SERIAL_NUMBER, INSERT_TIME, BUSINESS_TYPE, 
				LIST_ID, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, SMS_INFO ) 
			VALUES (
				CURRENT_TIMESTAMP, #{excute_date, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{serial_number, jdbcType=VARCHAR} , SYSDATE , #{business_type, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{sms_info, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePolicySmsTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_SMS_TASK WHERE  LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePolicySmsTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_SMS_TASK ]]>
		<set>
		<trim suffixOverrides=",">
		    EXCUTE_DATE = #{excute_date, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			SERIAL_NUMBER = #{serial_number, jdbcType=VARCHAR} ,
		    BUSINESS_TYPE = #{business_type, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			SMS_INFO = #{sms_info, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findPolicySmsTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXCUTE_DATE, A.POLICY_CODE, A.SERIAL_NUMBER, A.BUSINESS_TYPE, 
			A.LIST_ID, A.SMS_INFO FROM APP___PAS__DBUSER.T_POLICY_SMS_TASK A WHERE 1 = 1  ]]>
		<include refid="queryPolicySmsTaskByListIdCondition" />
	</select>
	
	<select id="PA_findPolicySmsTaskByExcuteDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXCUTE_DATE, A.POLICY_CODE, A.SERIAL_NUMBER, A.BUSINESS_TYPE, 
			A.LIST_ID, A.SMS_INFO FROM APP___PAS__DBUSER.T_POLICY_SMS_TASK A WHERE 1 = 1  ]]>
		<include refid="queryPolicySmsTaskByExcuteDateCondition" />
	</select>
	
	<select id="PA_findPolicySmsTaskByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXCUTE_DATE, A.POLICY_CODE, A.SERIAL_NUMBER, A.BUSINESS_TYPE, 
			A.LIST_ID, A.SMS_INFO FROM APP___PAS__DBUSER.T_POLICY_SMS_TASK A WHERE 1 = 1  ]]>
		<include refid="queryPolicySmsTaskByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findPolicySmsTaskBySerialNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXCUTE_DATE, A.POLICY_CODE, A.SERIAL_NUMBER, A.BUSINESS_TYPE, 
			A.LIST_ID, A.SMS_INFO FROM APP___PAS__DBUSER.T_POLICY_SMS_TASK A WHERE 1 = 1  ]]>
		<include refid="queryPolicySmsTaskBySerialNumberCondition" />
	</select>
	
</mapper>
