<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.SignPolicyDataDaoImpl">

	<sql id="PAS_signPolicyDataWhereCondition">
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" task_status != null and task_status != ''  "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" result_code != null and result_code != ''  "><![CDATA[ AND A.RESULT_CODE = #{result_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_querySignPolicyDataByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addSignPolicyData"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT DEV_PAS.S_SIGN_POLICY_DATA.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_SIGN_POLICY_DATA(
				APPLY_CODE, INSERT_TIMESTAMP, TASK_STATUS, POLICY_CODE, RESULT_CODE, UPDATE_BY, INSERT_TIME, 
				LIST_ID, APPLY_DATE, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				#{apply_code, jdbcType=VARCHAR}, CURRENT_TIMESTAMP, #{task_status, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{result_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE 
				, #{list_id, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deleteSignPolicyData" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_SIGN_POLICY_DATA WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updateSignPolicyData" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_SIGN_POLICY_DATA ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			TASK_STATUS = #{task_status, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			RESULT_CODE = #{result_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findSignPolicyDataByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.TASK_STATUS, A.POLICY_CODE, A.RESULT_CODE, 
			A.LIST_ID, A.APPLY_DATE FROM DEV_PAS.T_SIGN_POLICY_DATA A WHERE 1 = 1  ]]>
		<include refid="PAS_querySignPolicyDataByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapSignPolicyData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.TASK_STATUS, A.POLICY_CODE, A.RESULT_CODE, 
			A.LIST_ID, A.APPLY_DATE FROM DEV_PAS.T_SIGN_POLICY_DATA A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllSignPolicyData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.TASK_STATUS, A.POLICY_CODE, A.RESULT_CODE, 
			A.LIST_ID, A.APPLY_DATE, ROWNUM RN FROM DEV_PAS.T_SIGN_POLICY_DATA A WHERE 1=1  ]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" task_status != null and task_status != ''  "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" result_code != null and result_code != ''  "><![CDATA[ AND A.RESULT_CODE = #{result_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if> 
		<if test=" modnum != null and modnum != '' and start != null and start != '' "><![CDATA[ and mod(A.LIST_ID,#{modnum}) = #{start} ]]></if> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findSignPolicyDataTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_SIGN_POLICY_DATA A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_querySignPolicyDataForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.APPLY_CODE, B.TASK_STATUS, B.POLICY_CODE, B.RESULT_CODE, 
			B.LIST_ID, B.APPLY_DATE FROM (
					SELECT ROWNUM RN, A.APPLY_CODE, A.TASK_STATUS, A.POLICY_CODE, A.RESULT_CODE, 
			A.LIST_ID, A.APPLY_DATE FROM DEV_PAS.T_SIGN_POLICY_DATA A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
