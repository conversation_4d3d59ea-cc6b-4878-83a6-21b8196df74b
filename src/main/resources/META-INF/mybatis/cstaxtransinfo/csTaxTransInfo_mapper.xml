<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsTaxTransInfoDao">

	<sql id="CUS_csTaxTransInfoWhereCondition">
		<if test=" req_clob  != null "><![CDATA[ AND A.REQ_CLOB = #{req_clob} ]]></if>
		<if test=" trans_type_name != null and trans_type_name != ''  "><![CDATA[ AND A.TRANS_TYPE_NAME = #{trans_type_name} ]]></if>
		<if test=" result_code != null and result_code != ''  "><![CDATA[ AND A.RESULT_CODE = #{result_code} ]]></if>
		<if test=" resp_clob  != null  and  resp_clob  != ''  "><![CDATA[ AND A.RESP_CLOB = #{resp_clob} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" trans_type_code != null and trans_type_code != ''  "><![CDATA[ AND A.TRANS_TYPE_CODE = #{trans_type_code} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="CUS_queryCsTaxTransInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	<sql id="CUS_queryCsTaxTransInfoByTaskIdCondition">
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="CUS_addCsTaxTransInfo" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE"
			keyProperty="list_id">
			SELECT
			APP___PAS__DBUSER.S_CS_TAX_TRANS_INF__LISID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO(
				REQ_CLOB, TRANS_TYPE_NAME, RESULT_CODE, INSERT_TIME, RESP_CLOB, TASK_ID, UPDATE_TIME, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, TRANS_TYPE_CODE, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{req_clob, jdbcType=VARCHAR}, #{trans_type_name, jdbcType=VARCHAR} , #{result_code, jdbcType=VARCHAR} , SYSDATE , #{resp_clob, jdbcType=VARCHAR} , #{task_id, jdbcType=NUMERIC} , SYSDATE 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{trans_type_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="CUS_deleteCsTaxTransInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO WHERE 1 = 1 ]]>
		<include refid="CUS_csTaxTransInfoWhereCondition" />
	</delete>

	<!-- 修改操作 -->
	<update id="CUS_updateCsTaxTransInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO A]]>
		<set>
			<trim suffixOverrides=",">
				A.REQ_CLOB = #{req_clob, jdbcType=VARCHAR} ,
				A.TRANS_TYPE_NAME = #{trans_type_name, jdbcType=VARCHAR} ,
				A.RESULT_CODE
				= #{result_code, jdbcType=VARCHAR} ,
				A.RESP_CLOB = #{resp_clob,
				jdbcType=VARCHAR} ,
				A.TASK_ID = #{task_id, jdbcType=NUMERIC} ,
				A.UPDATE_TIME
				= SYSDATE ,
				A.UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				A.LIST_ID =
				#{list_id, jdbcType=NUMERIC} ,
				A.TRANS_TYPE_CODE = #{trans_type_code,
				jdbcType=VARCHAR} ,
				A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
		<![CDATA[AND A.LIST_ID = #{list_id, jdbcType=NUMERIC}]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="CUS_findCsTaxTransInfoByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.REQ_CLOB, A.TRANS_TYPE_NAME, A.RESULT_CODE, A.RESP_CLOB, A.TASK_ID, 
			A.LIST_ID, A.TRANS_TYPE_CODE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO A WHERE 1 = 1 ]]>
		<include refid="CUS_queryCsTaxTransInfoByListIdCondition" />
	</select>

	<select id="CUS_findCsTaxTransInfoByTaskId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.REQ_CLOB, A.TRANS_TYPE_NAME, A.RESULT_CODE, A.RESP_CLOB, A.TASK_ID, 
			A.LIST_ID, A.TRANS_TYPE_CODE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsTaxTransInfoByTaskIdCondition" />
	</select>


	<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsTaxTransInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.REQ_CLOB, A.TRANS_TYPE_NAME, A.RESULT_CODE, A.RESP_CLOB, A.TASK_ID, 
			A.LIST_ID, A.TRANS_TYPE_CODE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csTaxTransInfoWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="CUS_findAllCsTaxTransInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.REQ_CLOB, A.TRANS_TYPE_NAME, A.RESULT_CODE, A.RESP_CLOB, A.TASK_ID, 
			A.LIST_ID, A.TRANS_TYPE_CODE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csTaxTransInfoWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="CUS_findCsTaxTransInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_csTaxTransInfoWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="CUS_queryCsTaxTransInfoForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.REQ_CLOB, B.TRANS_TYPE_NAME, B.RESULT_CODE, B.RESP_CLOB, B.TASK_ID, 
			B.LIST_ID, B.TRANS_TYPE_CODE FROM (
					SELECT ROWNUM RN, A.REQ_CLOB, A.TRANS_TYPE_NAME, A.RESULT_CODE, A.RESP_CLOB, A.TASK_ID, 
			A.LIST_ID, A.TRANS_TYPE_CODE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_csTaxTransInfoWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

</mapper>
