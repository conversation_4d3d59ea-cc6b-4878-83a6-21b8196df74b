<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ILoanBusiCfgLogDao">

	<sql id="CUS_loanBusiCfgLogWhereCondition">
		<if test=" operate_flag  != null "><![CDATA[ AND A.OPERATE_FLAG = #{operate_flag} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" max_std_prem_af  != null "><![CDATA[ AND A.MAX_STD_PREM_AF = #{max_std_prem_af} ]]></if>
		<if test=" validate_date_end  != null  and  validate_date_end  != ''  "><![CDATA[ AND A.VALIDATE_DATE_END = #{validate_date_end} ]]></if>
		<if test=" charge_type  != null "><![CDATA[ AND A.CHARGE_TYPE = #{charge_type} ]]></if>
		<if test=" min_std_prem_af  != null "><![CDATA[ AND A.MIN_STD_PREM_AF = #{min_std_prem_af} ]]></if>
		<if test=" max_loan_ratio  != null "><![CDATA[ AND A.MAX_LOAN_RATIO = #{max_loan_ratio} ]]></if>
		<if test=" is_carefully_chosen != null and is_carefully_chosen != ''  "><![CDATA[ AND A.IS_CAREFULLY_CHOSEN = #{is_carefully_chosen} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" valid_date  != null  and  valid_date  != ''  "><![CDATA[ AND A.VALID_DATE = #{valid_date} ]]></if>
		<if test=" validate_date_start  != null  and  validate_date_start  != ''  "><![CDATA[ AND A.VALIDATE_DATE_START = #{validate_date_start} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" period_type != null and period_type != ''  "><![CDATA[ AND A.PERIOD_TYPE = #{period_type} ]]></if>
		<if test=" business_prod_id  != null "><![CDATA[ AND A.BUSINESS_PROD_ID = #{business_prod_id} ]]></if>
		<if test=" min_paid_prem  != null "><![CDATA[ AND A.MIN_PAID_PREM = #{min_paid_prem} ]]></if>
		<if test=" operater_id  != null "><![CDATA[ AND A.OPERATER_ID = #{operater_id} ]]></if>
		<if test=" expired_date  != null  and  expired_date  != ''  "><![CDATA[ AND A.EXPIRED_DATE = #{expired_date} ]]></if>
		<if test=" operater_organ_code != null and operater_organ_code != ''  "><![CDATA[ AND A.OPERATER_ORGAN_CODE = #{operater_organ_code} ]]></if>
		<if test=" loan_flag  != null "><![CDATA[ AND A.LOAN_FLAG = #{loan_flag} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" max_paid_prem  != null "><![CDATA[ AND A.MAX_PAID_PREM = #{max_paid_prem} ]]></if>
		<if test=" is_follow_master  != null "><![CDATA[ AND A.IS_FOLLOW_MASTER = #{is_follow_master} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="CUS_queryLoanBusiCfgLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>
	<sql id="CUS_queryLoanBusiCfgLogByCfgIdCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="CUS_addLoanBusiCfgLog" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_LOAN_BUSI_CFG_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG(
				OPERATE_FLAG, REMARK, MAX_STD_PREM_AF, VALIDATE_DATE_END, CHARGE_TYPE, MIN_STD_PREM_AF, MAX_LOAN_RATIO, 
				IS_CAREFULLY_CHOSEN, ORGAN_CODE, INSERT_TIMESTAMP, CHANNEL_TYPE, UPDATE_BY, VALID_DATE, VALIDATE_DATE_START, 
				CHARGE_YEAR, PERIOD_TYPE, BUSINESS_PROD_ID, MIN_PAID_PREM, OPERATER_ID, EXPIRED_DATE, OPERATER_ORGAN_CODE, 
				INSERT_TIME, LOAN_FLAG, UPDATE_TIME, LOG_ID, CHARGE_PERIOD, MAX_PAID_PREM, IS_FOLLOW_MASTER, 
				CFG_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{operate_flag, jdbcType=VARCHAR}, #{remark, jdbcType=VARCHAR} , #{max_std_prem_af, jdbcType=NUMERIC} , #{validate_date_end, jdbcType=DATE} , #{charge_type, jdbcType=NUMERIC} , #{min_std_prem_af, jdbcType=NUMERIC} , #{max_loan_ratio, jdbcType=NUMERIC} 
				, #{is_carefully_chosen, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{channel_type, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{valid_date, jdbcType=DATE} , #{validate_date_start, jdbcType=DATE} 
				, #{charge_year, jdbcType=NUMERIC} , #{period_type, jdbcType=VARCHAR} , #{business_prod_id, jdbcType=NUMERIC} , #{min_paid_prem, jdbcType=NUMERIC} , #{operater_id, jdbcType=NUMERIC} , #{expired_date, jdbcType=DATE} , #{operater_organ_code, jdbcType=VARCHAR} 
				, SYSDATE , #{loan_flag, jdbcType=NUMERIC} , SYSDATE , #{log_id, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} , #{max_paid_prem, jdbcType=NUMERIC} , #{is_follow_master, jdbcType=NUMERIC} 
				, #{cfg_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="CUS_deleteLoanBusiCfgLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG WHERE  = #{log_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="CUS_updateLoanBusiCfgLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG ]]>
		<set>
			<trim suffixOverrides=",">
				OPERATE_FLAG = #{operate_flag, jdbcType=VARCHAR} ,
				REMARK = #{remark, jdbcType=VARCHAR} ,
				MAX_STD_PREM_AF = #{max_std_prem_af, jdbcType=NUMERIC} ,
				VALIDATE_DATE_END = #{validate_date_end, jdbcType=DATE} ,
				CHARGE_TYPE = #{charge_type, jdbcType=NUMERIC} ,
				MIN_STD_PREM_AF = #{min_std_prem_af, jdbcType=NUMERIC} ,
				MAX_LOAN_RATIO = #{max_loan_ratio, jdbcType=NUMERIC} ,
				IS_CAREFULLY_CHOSEN = #{is_carefully_chosen, jdbcType=VARCHAR} ,
				ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
				CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				VALID_DATE = #{valid_date, jdbcType=DATE} ,
				VALIDATE_DATE_START = #{validate_date_start, jdbcType=DATE} ,
				CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
				PERIOD_TYPE = #{period_type, jdbcType=VARCHAR} ,
				BUSINESS_PROD_ID = #{business_prod_id, jdbcType=NUMERIC} ,
				MIN_PAID_PREM = #{min_paid_prem, jdbcType=NUMERIC} ,
				OPERATER_ID = #{operater_id, jdbcType=NUMERIC} ,
				EXPIRED_DATE = #{expired_date, jdbcType=DATE} ,
				OPERATER_ORGAN_CODE = #{operater_organ_code, jdbcType=VARCHAR} ,
				LOAN_FLAG = #{loan_flag, jdbcType=NUMERIC} ,
				UPDATE_TIME = SYSDATE ,
				LOG_ID = #{log_id, jdbcType=NUMERIC} ,
				CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
				MAX_PAID_PREM = #{max_paid_prem, jdbcType=NUMERIC} ,
				IS_FOLLOW_MASTER = #{is_follow_master, jdbcType=NUMERIC} ,
				CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="CUS_findLoanBusiCfgLogByLogId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_FLAG, A.REMARK, A.MAX_STD_PREM_AF, A.VALIDATE_DATE_END, A.CHARGE_TYPE, A.MIN_STD_PREM_AF, A.MAX_LOAN_RATIO, 
			A.IS_CAREFULLY_CHOSEN, A.ORGAN_CODE, A.CHANNEL_TYPE, A.VALID_DATE, A.VALIDATE_DATE_START, 
			A.CHARGE_YEAR, A.PERIOD_TYPE, A.BUSINESS_PROD_ID, A.MIN_PAID_PREM, A.OPERATER_ID, A.EXPIRED_DATE, A.OPERATER_ORGAN_CODE, 
			A.LOAN_FLAG, A.LOG_ID, A.CHARGE_PERIOD, A.MAX_PAID_PREM, A.IS_FOLLOW_MASTER, 
			A.CFG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryLoanBusiCfgLogByLogIdCondition" />
	</select>

	<select id="CUS_findLoanBusiCfgLogByCfgId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_FLAG, A.REMARK, A.MAX_STD_PREM_AF, A.VALIDATE_DATE_END, A.CHARGE_TYPE, A.MIN_STD_PREM_AF, A.MAX_LOAN_RATIO, 
			A.IS_CAREFULLY_CHOSEN, A.ORGAN_CODE, A.CHANNEL_TYPE, A.VALID_DATE, A.VALIDATE_DATE_START, 
			A.CHARGE_YEAR, A.PERIOD_TYPE, A.BUSINESS_PROD_ID, A.MIN_PAID_PREM, A.OPERATER_ID, A.EXPIRED_DATE, A.OPERATER_ORGAN_CODE, 
			A.LOAN_FLAG, A.LOG_ID, A.CHARGE_PERIOD, A.MAX_PAID_PREM, A.IS_FOLLOW_MASTER, 
			A.CFG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryLoanBusiCfgLogByCfgIdCondition" />
	</select>


	<!-- 查询单条数据 -->
	<select id="CUS_findLoanBusiCfgLog" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_FLAG, A.REMARK, A.MAX_STD_PREM_AF, A.VALIDATE_DATE_END, A.CHARGE_TYPE, A.MIN_STD_PREM_AF, A.MAX_LOAN_RATIO, 
			A.IS_CAREFULLY_CHOSEN, A.ORGAN_CODE, A.CHANNEL_TYPE, A.VALID_DATE, A.VALIDATE_DATE_START, 
			A.CHARGE_YEAR, A.PERIOD_TYPE, A.BUSINESS_PROD_ID, A.MIN_PAID_PREM, A.OPERATER_ID, A.EXPIRED_DATE, A.OPERATER_ORGAN_CODE, 
			A.LOAN_FLAG, A.LOG_ID, A.CHARGE_PERIOD, A.MAX_PAID_PREM, A.IS_FOLLOW_MASTER, 
			A.CFG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_loanBusiCfgLogWhereCondition" />
	</select>
	
	<!-- 按map查询操作 -->
	<select id="CUS_findAllMapLoanBusiCfgLog" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_FLAG, A.REMARK, A.MAX_STD_PREM_AF, A.VALIDATE_DATE_END, A.CHARGE_TYPE, A.MIN_STD_PREM_AF, A.MAX_LOAN_RATIO, 
			A.IS_CAREFULLY_CHOSEN, A.ORGAN_CODE, A.CHANNEL_TYPE, A.VALID_DATE, A.VALIDATE_DATE_START, 
			A.CHARGE_YEAR, A.PERIOD_TYPE, A.BUSINESS_PROD_ID, A.MIN_PAID_PREM, A.OPERATER_ID, A.EXPIRED_DATE, A.OPERATER_ORGAN_CODE, 
			A.LOAN_FLAG, A.LOG_ID, A.CHARGE_PERIOD, A.MAX_PAID_PREM, A.IS_FOLLOW_MASTER, 
			A.CFG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_loanBusiCfgLogWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="CUS_findAllLoanBusiCfgLog" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATE_FLAG, A.REMARK, A.MAX_STD_PREM_AF, A.VALIDATE_DATE_END, A.CHARGE_TYPE, A.MIN_STD_PREM_AF, A.MAX_LOAN_RATIO, 
			A.IS_CAREFULLY_CHOSEN, A.ORGAN_CODE, A.CHANNEL_TYPE, A.VALID_DATE, A.VALIDATE_DATE_START, 
			A.CHARGE_YEAR, A.PERIOD_TYPE, A.BUSINESS_PROD_ID, A.MIN_PAID_PREM, A.OPERATER_ID, A.EXPIRED_DATE, A.OPERATER_ORGAN_CODE, 
			A.LOAN_FLAG, A.LOG_ID, A.CHARGE_PERIOD, A.MAX_PAID_PREM, A.IS_FOLLOW_MASTER, 
			A.CFG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_loanBusiCfgLogWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="CUS_findLoanBusiCfgLogTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_loanBusiCfgLogWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="CUS_queryLoanBusiCfgLogForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATE_FLAG, B.REMARK, B.MAX_STD_PREM_AF, B.VALIDATE_DATE_END, B.CHARGE_TYPE, B.MIN_STD_PREM_AF, B.MAX_LOAN_RATIO, 
			B.IS_CAREFULLY_CHOSEN, B.ORGAN_CODE, B.CHANNEL_TYPE, B.VALID_DATE, B.VALIDATE_DATE_START, 
			B.CHARGE_YEAR, B.PERIOD_TYPE, B.BUSINESS_PROD_ID, B.MIN_PAID_PREM, B.OPERATER_ID, B.EXPIRED_DATE, B.OPERATER_ORGAN_CODE, 
			B.LOAN_FLAG, B.LOG_ID, B.CHARGE_PERIOD, B.MAX_PAID_PREM, B.IS_FOLLOW_MASTER, 
			B.CFG_ID FROM (
					SELECT ROWNUM RN, A.OPERATE_FLAG, A.REMARK, A.MAX_STD_PREM_AF, A.VALIDATE_DATE_END, A.CHARGE_TYPE, A.MIN_STD_PREM_AF, A.MAX_LOAN_RATIO, 
			A.IS_CAREFULLY_CHOSEN, A.ORGAN_CODE, A.CHANNEL_TYPE, A.VALID_DATE, A.VALIDATE_DATE_START, 
			A.CHARGE_YEAR, A.PERIOD_TYPE, A.BUSINESS_PROD_ID, A.MIN_PAID_PREM, A.OPERATER_ID, A.EXPIRED_DATE, A.OPERATER_ORGAN_CODE, 
			A.LOAN_FLAG, A.LOG_ID, A.CHARGE_PERIOD, A.MAX_PAID_PREM, A.IS_FOLLOW_MASTER, 
			A.CFG_ID FROM APP___PAS__DBUSER.T_LOAN_BUSI_CFG_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_loanBusiCfgLogWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

</mapper>
