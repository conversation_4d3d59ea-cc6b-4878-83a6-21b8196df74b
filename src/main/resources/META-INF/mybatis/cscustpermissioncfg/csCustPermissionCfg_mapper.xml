<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsCustPermissionCfgDao">

	<sql id="CUS_csCustPermissionCfgWhereCondition">
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND A.STATE = #{state} ]]></if>		
		<if test=" customer_cert_code != null and customer_cert_code != ''  "><![CDATA[ AND A.CUSTOMER_CERT_CODE = #{customer_cert_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE  like '${organ_code}%' ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE <= #{end_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" service_type != null and service_type != ''  "><![CDATA[ AND A.SERVICE_TYPE = #{service_type} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" cfg_type != null and cfg_type != ''  "><![CDATA[ AND A.CFG_TYPE = #{cfg_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsCustPermissionCfgByCfgIdCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsCustPermissionCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="cfg_id">
			SELECT  DEV_PAS.S_CS_DR_RELATION.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CS_CUST_PERMISSION_CFG(
				CUSTOMER_CERT_TYPE, STATE, INSERT_TIME, END_DATE, CUSTOMER_CERT_CODE, UPDATE_TIME, INSERT_TIMESTAMP, 
				ORGAN_CODE, START_DATE, POLICY_CODE, UPDATE_BY, CFG_ID, UPDATE_TIMESTAMP, SERVICE_TYPE, 
				SERVICE_CODE, INSERT_BY, CFG_TYPE ) 
			VALUES (
				#{customer_cert_type, jdbcType=VARCHAR}, #{state, jdbcType=VARCHAR} , SYSDATE , #{end_date, jdbcType=DATE} , #{customer_cert_code, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP
				, #{organ_code, jdbcType=VARCHAR} , #{start_date, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{cfg_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{service_type, jdbcType=VARCHAR} 
				, #{service_code, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{cfg_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsCustPermissionCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CS_CUST_PERMISSION_CFG WHERE  CFG_ID = #{cfg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsCustPermissionCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CS_CUST_PERMISSION_CFG ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			STATE = #{state, jdbcType=VARCHAR} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			CUSTOMER_CERT_CODE = #{customer_cert_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			SERVICE_TYPE = #{service_type, jdbcType=VARCHAR} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
			CFG_TYPE = #{cfg_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CFG_ID = #{cfg_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsCustPermissionCfgByCfgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.STATE, A.END_DATE, A.CUSTOMER_CERT_CODE, 
			A.ORGAN_CODE, A.START_DATE, A.POLICY_CODE, A.CFG_ID, A.SERVICE_TYPE, 
			A.SERVICE_CODE, A.CFG_TYPE, A.UPDATE_BY FROM DEV_PAS.T_CS_CUST_PERMISSION_CFG A WHERE ROWNUM <=  1  ]]>
		<include refid="CUS_csCustPermissionCfgWhereCondition" />
	</select>
	
	
	<select id="CUS_findState" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.STATE, A.END_DATE, A.CUSTOMER_CERT_CODE, 
			A.ORGAN_CODE, A.START_DATE, A.POLICY_CODE, A.CFG_ID, A.SERVICE_TYPE, 
			A.SERVICE_CODE, A.CFG_TYPE, A.UPDATE_BY FROM DEV_PAS.T_CS_CUST_PERMISSION_CFG A
         WHERE A.ORGAN_CODE IN
               (SELECT ORGAN_CODE
                  FROM (SELECT T.ORGAN_CODE, T.ORGAN_GRADE
                          FROM DEV_PAS.T_UDMP_ORG_REL T
                         WHERE T.ORGAN_CODE = #{organ_code}
                        UNION
                        
                        SELECT T.ORGAN_CODE, T.ORGAN_GRADE
                          FROM DEV_PAS.T_UDMP_ORG_REL T
                         WHERE T.ORGAN_CODE IN
                               (SELECT T.UPORGAN_CODE
                                  FROM DEV_PAS.T_UDMP_ORG_REL T
                                 WHERE T.ORGAN_CODE = #{organ_code})
                        
                        UNION
                        SELECT T.ORGAN_CODE, T.ORGAN_GRADE
                          FROM DEV_PAS.T_UDMP_ORG_REL T
                         WHERE T.ORGAN_CODE IN
                               (SELECT T.UPORGAN_CODE
                                  FROM DEV_PAS.T_UDMP_ORG_REL T
                                 WHERE T.ORGAN_CODE IN
                                       (SELECT T.UPORGAN_CODE
                                          FROM DEV_PAS.T_UDMP_ORG_REL T
                                         WHERE T.ORGAN_CODE = #{organ_code}))
                        UNION
                        
                        SELECT T.ORGAN_CODE, T.ORGAN_GRADE
                          FROM DEV_PAS.T_UDMP_ORG_REL T
                         WHERE T.ORGAN_CODE IN
                               (SELECT T.UPORGAN_CODE
                                  FROM DEV_PAS.T_UDMP_ORG_REL T
                                 WHERE T.ORGAN_CODE IN
                                       (SELECT T.UPORGAN_CODE
                                          FROM DEV_PAS.T_UDMP_ORG_REL T
                                         WHERE T.ORGAN_CODE IN
                                               (SELECT T.UPORGAN_CODE
                                                  FROM DEV_PAS.T_UDMP_ORG_REL T
                                                 WHERE T.ORGAN_CODE = #{organ_code})))))  ]]>
		<include refid="CUS_csCustPermissionCfgWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsCustPermissionCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.STATE, A.END_DATE, A.CUSTOMER_CERT_CODE, 
			A.ORGAN_CODE, A.START_DATE, A.POLICY_CODE, A.CFG_ID, A.SERVICE_TYPE, 
			A.SERVICE_CODE, A.CFG_TYPE FROM DEV_PAS.T_CS_CUST_PERMISSION_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csCustPermissionCfgWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsCustPermissionCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE, A.STATE, A.END_DATE, A.CUSTOMER_CERT_CODE, 
			A.ORGAN_CODE, A.START_DATE, A.POLICY_CODE, A.CFG_ID, A.SERVICE_TYPE, 
			A.SERVICE_CODE, A.CFG_TYPE FROM DEV_PAS.T_CS_CUST_PERMISSION_CFG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsCustPermissionCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_CUST_PERMISSION_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_csCustPermissionCfgWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsCustPermissionCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_CERT_TYPE, B.STATE, B.END_DATE, B.CUSTOMER_CERT_CODE, 
			B.ORGAN_CODE, B.START_DATE, B.POLICY_CODE, B.CFG_ID, B.SERVICE_TYPE, 
			B.SERVICE_CODE, B.CFG_TYPE,B.UPDATE_BY,B.UPDATE_TIME FROM (
					SELECT ROWNUM RN, A.CUSTOMER_CERT_TYPE, A.STATE, A.END_DATE, A.CUSTOMER_CERT_CODE, 
			A.ORGAN_CODE, A.START_DATE, A.POLICY_CODE, A.CFG_ID, A.SERVICE_TYPE, 
			A.SERVICE_CODE, A.CFG_TYPE,A.UPDATE_BY,A.UPDATE_TIME FROM DEV_PAS.T_CS_CUST_PERMISSION_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		 <include refid="CUS_csCustPermissionCfgWhereCondition" /> 
		<![CDATA[ ORDER BY A.ORGAN_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 按userid查询organCode -->
	<select id="findUserOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.ORGAN_CODE
		  FROM DEV_PAS.T_UDMP_USER T
		  WHERE  T.USER_ID = #{update_by}
		  AND ROWNUM <=  1  ]]>
	</select>
	
</mapper>
