<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 143327 -->
<mapper namespace="com.nci.tunan.cs.dao.ICsPrintResultDao">
	
	<sql id="CUS_CsPrintResultWhereCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" print_trans_code  != null and print_trans_code != '' "><![CDATA[ AND A.PRINT_TRANS_CODE = #{print_trans_code} ]]></if>
		<if test=" job_name  != null and job_name != '' "><![CDATA[ AND A.JOB_NAME = #{job_name} ]]></if>
		<if test=" success_flag  != null "><![CDATA[ AND A.SUCCESS_FLAG = #{success_flag} ]]></if>
	</sql>
	
<!-- 添加操作 -->
	<insert id="CUS_addCsPrintResult"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_PRINT_RES__ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PRINT_RESULT(
				LIST_ID, PRINT_TRANS_CODE, JOB_NAME, SUCCESS_FLAG, INSERT_BY, UPDATE_BY, INSERT_TIME, UPDATE_TIME, INSERT_TIMESTAMP, UPDATE_TIMESTAMP ) 
			VALUES (
				#{list_id, jdbcType=NUMERIC}, #{print_trans_code, jdbcType=VARCHAR} , #{job_name, jdbcType=VARCHAR} , #{success_flag, jdbcType=NUMERIC}
				, #{insert_by, jdbcType=NUMERIC} ,#{update_by, jdbcType=NUMERIC},SYSDATE,SYSDATE,SYSDATE,SYSDATE
				) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsPrintResult" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_PRINT_RESULT WHERE   LIST_ID = #{list_id, jdbcType=NUMERIC}  ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsPrintResult" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PRINT_RESULT ]]>
		<set>
		<trim suffixOverrides=",">
			PRINT_TRANS_CODE = #{print_trans_code, jdbcType=VARCHAR} ,
		    JOB_NAME = #{job_name, jdbcType=VARCHAR} ,
		    SUCCESS_FLAG = #{success_flag, jdbcType=NUMERIC},
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id, jdbcType=NUMERIC}  ]]>
	</update>
	<!-- 查询单条 -->
	<select id="CUS_findCsPrintResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID, A.PRINT_TRANS_CODE, A.JOB_NAME, A.SUCCESS_FLAG FROM APP___PAS__DBUSER.T_CS_PRINT_RESULT A WHERE 1 = 1  ]]>
		<include refid="CUS_CsPrintResultWhereCondition" /> 
	</select>
	<!-- 查询所有操作 -->
	<select id="CUS_findAllCsPrintResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, 
			A.ENDORSEMENT_FLAG, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_PRINT_RESULT A WHERE 1=1 ]]>
		<include refid="CUS_CsPrintResultWhereCondition" /> 
	</select>
</mapper>