<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.batch.taxPremiumPolicyRenewalCancel.dao.ITaxPremiumPolicyRenewalCancelDao">
 
	<!-- 查询税优保单续期撤销报送保单信息 -->
	<select id="findTaxPremiumPolicyRenewalCancel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.*, ROWNUM RN FROM (
						SELECT DISTINCT TPT.LIST_ID,
              TPT.APPLY_CODE,
                 TPT.POLICY_CODE,
                 TPT.TOTAL_POLICY_SEQUENCE_NO,
                 TPT.UNIT_NUMER,
                 TPT.SEND_TYPE,
                 TPT.TASK_STATUS,
                 TPA.POLICY_YEAR,
                 TPT.DUE_TIME,
                 TPT.FINISH_TIME,
                 TPT.BATACH_DATE,
                 TPT.FEE_SEQUENCE_NO,
                 TPT.RENEWAL_SEQUENCE_NO,
                 TPT.BOOKING_SEQUENCE_NO,
                 TPT.RESULT_CODE,
                 TPT.RESULT_MESSAGE,
				 TPT.RENEWAL_ENDORSEMENT_NO,
				 TPT.TAX_CODE
            FROM DEV_PAS.T_TAX_PREMIUM_POLICY_TASK TPT,
                 DEV_PAS.T_PREM_ARAP               TPA
			WHERE (TPT.UNIT_NUMER = TPA.UNIT_NUMBER OR
			     TPT.UNIT_NUMER = TPA.ROLLBACK_UNIT_NUMBER)
			 AND TPT.FEE_SEQUENCE_NO IS NOT NULL
			 AND TPT.TASK_STATUS NOT IN ('6','2')
			 AND TPT.SEND_TYPE = '1'
			 AND TPA.BUSINESS_TYPE = '4003'
             AND TPA.FEE_STATUS = '02'
             AND EXISTS (SELECT 1
                    FROM DEV_PDS.T_BUSINESS_PRODUCT B,DEV_PAS.T_CONTRACT_BUSI_PROD      TCBP
                   WHERE TCBP.BUSI_PRD_ID = B.BUSINESS_PRD_ID
                   AND TPT.POLICY_CODE = TCBP.POLICY_CODE
                     AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
                     AND B.COVER_PERIOD_TYPE = 0)]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPT.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ) T WHERE 1=1 ]]>	
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.LIST_ID , #{modnum}) = #{start} ]]></if>		
	</select>
	
	<!-- 查询税优保单续期撤销报送保单信息 -->
	<select id="PAS_findPolicyBaseInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.POLICY_CODE,T.TOTAL_POLICY_SEQUENCE_NO,T.LIABILITY_STATE
			FROM DEV_PAS.T_CONTRACT_MASTER T
			WHERE T.POLICY_CODE =#{policy_code}
		 ]]>		
	</select>
	<!-- 添加操作 -->
	<insert id="PAS_saveMessage"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TAX_PREMIUM_POLICY_TASK.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK(
				FEE_SEQUENCE_NO, RESULT_CODE, UNIT_NUMER, INSERT_TIME, TOTAL_POLICY_SEQUENCE_NO, RESULT_MESSAGE, 
				SEND_TYPE, UPDATE_TIME, FINISH_TIME, BATACH_DATE, APPLY_CODE, INSERT_TIMESTAMP, RENEWAL_SEQUENCE_NO, 
				BOOKING_SEQUENCE_NO, DUE_TIME, POLICY_CODE, TASK_STATUS, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, 
				INSERT_BY ) 
			VALUES (
				#{fee_sequence_no, jdbcType=VARCHAR},  #{result_code, jdbcType=VARCHAR} , #{unit_numer, jdbcType=VARCHAR} , SYSDATE , #{total_policy_sequence_no, jdbcType=VARCHAR} , #{result_message, jdbcType=VARCHAR} 
				, #{send_type, jdbcType=VARCHAR} , SYSDATE , #{finish_time, jdbcType=DATE} , #{batach_date, jdbcType=DATE} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{renewal_sequence_no, jdbcType=VARCHAR} 
				, #{booking_sequence_no, jdbcType=VARCHAR} , #{due_time, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{task_status, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	<!-- 查询报送类型为“撤销”，且任务状态为“3-预约”的数据 -->
	<select id="PAS_queryTaskInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
						SELECT TPT.LIST_ID,
						       TPT.APPLY_CODE,
						       TPT.POLICY_CODE,
						       TPT.TOTAL_POLICY_SEQUENCE_NO,
						       TPT.UNIT_NUMER,
						       TPT.SEND_TYPE,
						       TPT.TASK_STATUS,
						       TPT.DUE_TIME,
						       TPT.FINISH_TIME,
						       TPT.BATACH_DATE,
						       TPT.FEE_SEQUENCE_NO,
						       TPT.RENEWAL_SEQUENCE_NO,
						       TPT.BOOKING_SEQUENCE_NO,
						       TPT.RESULT_CODE,
						       TPT.RESULT_MESSAGE,
						       TPT.RENEWAL_ENDORSEMENT_NO,
						       TPT.TAX_CODE
					  FROM DEV_PAS.T_TAX_PREMIUM_POLICY_TASK TPT
					 WHERE TPT.TASK_STATUS ='3'
					   AND TPT.SEND_TYPE = '3'
					   AND TPT.UNIT_NUMER = #{unit_numer}
					   AND TPT.POLICY_CODE = #{policy_code}]]>
	</select>
</mapper>
