<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsDocPrintTaskDao">
<!--
	<sql id="CUS_csDocPrintTaskWhereCondition">
		<if test=" apply_user_id  != null "><![CDATA[ AND A.APPLY_USER_ID = #{apply_user_id} ]]></if>
		<if test=" appy_time  != null  and  appy_time  != ''  "><![CDATA[ AND A.APPY_TIME = #{appy_time} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" status  != null "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" file_password != null and file_password != ''  "><![CDATA[ AND A.FILE_PASSWORD = #{file_password} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" file_name != null and file_name != ''  "><![CDATA[ AND A.FILE_NAME = #{file_name} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" email != null and email != ''  "><![CDATA[ AND A.EMAIL = #{email} ]]></if>
		<if test=" org_code != null and org_code != ''  "><![CDATA[ AND A.ORG_CODE = #{org_code} ]]></if>
		<if test=" file_counts  != null "><![CDATA[ AND A.FILE_COUNTS = #{file_counts} ]]></if>
		<if test=" download_finish_time  != null  and  download_finish_time  != ''  "><![CDATA[ AND A.DOWNLOAD_FINISH_TIME = #{download_finish_time} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsDocPrintTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsDocPrintTaskByBatchNoCondition">
		<if test=" batch_no != null and batch_no != '' "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CS_createDocPrintTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_DOC_PRINT_TASK__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_DOC_PRINT_TASK(
				APPLY_USER_ID, APPY_TIME, INSERT_TIME, BATCH_NO, TEMPLATE_CODE, END_DATE, UPDATE_TIME, 
				STATUS, FILE_PASSWORD, BUSI_PROD_CODE, INSERT_TIMESTAMP, START_DATE, FILE_NAME, UPDATE_BY, 
				LIST_ID, EMAIL, ORG_CODE, UPDATE_TIMESTAMP, INSERT_BY, FILE_COUNTS, DOWNLOAD_FINISH_TIME ) 
			VALUES (
				#{apply_user_id, jdbcType=NUMERIC}, #{appy_time, jdbcType=DATE} , SYSDATE , #{batch_no, jdbcType=VARCHAR} , #{template_code, jdbcType=VARCHAR} , #{end_date, jdbcType=DATE} , SYSDATE 
				, #{status, jdbcType=NUMERIC} , #{file_password, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{start_date, jdbcType=DATE} , #{file_name, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , #{email, jdbcType=VARCHAR} , #{org_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{file_counts, jdbcType=NUMERIC} , #{download_finish_time, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsDocPrintTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CS_DOC_PRINT_TASK WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsDocPrintTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_CS_DOC_PRINT_TASK ]]>
		<set>
		<trim suffixOverrides=",">
		    APPLY_USER_ID = #{apply_user_id, jdbcType=NUMERIC} ,
		    APPY_TIME = #{appy_time, jdbcType=DATE} ,
			BATCH_NO = #{batch_no, jdbcType=VARCHAR} ,
			TEMPLATE_CODE = #{template_code, jdbcType=VARCHAR} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    STATUS = #{status, jdbcType=NUMERIC} ,
			FILE_PASSWORD = #{file_password, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
			FILE_NAME = #{file_name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			EMAIL = #{email, jdbcType=VARCHAR} ,
			ORG_CODE = #{org_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    FILE_COUNTS = #{file_counts, jdbcType=NUMERIC} ,
		    DOWNLOAD_FINISH_TIME = #{download_finish_time, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsDocPrintTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_USER_ID, A.APPY_TIME, A.BATCH_NO, A.TEMPLATE_CODE, A.END_DATE, 
			A.STATUS, A.FILE_PASSWORD, A.BUSI_PROD_CODE, A.START_DATE, A.FILE_NAME, 
			A.LIST_ID, A.EMAIL, A.ORG_CODE, A.FILE_COUNTS, A.DOWNLOAD_FINISH_TIME FROM T_CS_DOC_PRINT_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDocPrintTaskByListIdCondition" />
	</select>
	
	<select id="CUS_findCsDocPrintTaskByBatchNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_USER_ID, A.APPY_TIME, A.BATCH_NO, A.TEMPLATE_CODE, A.END_DATE, 
			A.STATUS, A.FILE_PASSWORD, A.BUSI_PROD_CODE, A.START_DATE, A.FILE_NAME, 
			A.LIST_ID, A.EMAIL, A.ORG_CODE, A.FILE_COUNTS, A.DOWNLOAD_FINISH_TIME FROM T_CS_DOC_PRINT_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsDocPrintTaskByBatchNoCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsDocPrintTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_USER_ID, A.APPY_TIME, A.BATCH_NO, A.TEMPLATE_CODE, A.END_DATE, 
			A.STATUS, A.FILE_PASSWORD, A.BUSI_PROD_CODE, A.START_DATE, A.FILE_NAME, 
			A.LIST_ID, A.EMAIL, A.ORG_CODE, A.FILE_COUNTS, A.DOWNLOAD_FINISH_TIME FROM T_CS_DOC_PRINT_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsDocPrintTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_USER_ID, A.APPY_TIME, A.BATCH_NO, A.TEMPLATE_CODE, A.END_DATE, 
			A.STATUS, A.FILE_PASSWORD, A.BUSI_PROD_CODE, A.START_DATE, A.FILE_NAME, 
			A.LIST_ID, A.EMAIL, A.ORG_CODE, A.FILE_COUNTS, A.DOWNLOAD_FINISH_TIME FROM T_CS_DOC_PRINT_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsDocPrintTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CS_DOC_PRINT_TASK A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsDocPrintTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.APPLY_USER_ID, B.APPY_TIME, B.BATCH_NO, B.TEMPLATE_CODE, B.END_DATE, 
			B.STATUS, B.FILE_PASSWORD, B.BUSI_PROD_CODE, B.START_DATE, B.FILE_NAME, 
			B.LIST_ID, B.EMAIL, B.ORG_CODE, B.FILE_COUNTS, B.DOWNLOAD_FINISH_TIME FROM (
					SELECT ROWNUM RN, A.APPLY_USER_ID, A.APPY_TIME, A.BATCH_NO, A.TEMPLATE_CODE, A.END_DATE, 
			A.STATUS, A.FILE_PASSWORD, A.BUSI_PROD_CODE, A.START_DATE, A.FILE_NAME, 
			A.LIST_ID, A.EMAIL, A.ORG_CODE, A.FILE_COUNTS, A.DOWNLOAD_FINISH_TIME FROM T_CS_DOC_PRINT_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
