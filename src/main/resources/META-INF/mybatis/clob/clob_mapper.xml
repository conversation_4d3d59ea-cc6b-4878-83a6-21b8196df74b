<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.udmp.app.dao.ClobDao">
<!--
	<sql id="clobWhereCondition">
		<if test=" clob_id  != null "><![CDATA[ AND A.CLOB_ID = #{clob_id} ]]></if>
		<if test=" txt_content != null and txt_content != ''  "><![CDATA[ AND A.TXT_CONTENT = #{txt_content} ]]></if>
		<if test=" create_time  != null  and  create_time  != ''  "><![CDATA[ AND A.CREATE_TIME = #{create_time} ]]></if>
		<if test=" content  != null  and  content  != ''  "><![CDATA[ AND A.CONTENT = #{content} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryClobByClobIdCondition">
		<if test=" clob_id  != null "><![CDATA[ AND A.CLOB_ID = #{clob_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClob"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CLOB(
				CLOB_ID, TXT_CONTENT, CREATE_TIME, CONTENT ) 
			VALUES (
				#{clob_id, jdbcType=NUMERIC}, #{txt_content, jdbcType=VARCHAR} , #{create_time, jdbcType=DATE} , #{content, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClob" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CLOB WHERE CLOB_ID = #{clob_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClob" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CLOB ]]>
		<set>
		<trim suffixOverrides=",">
			TXT_CONTENT = #{txt_content, jdbcType=VARCHAR} ,
		    CREATE_TIME = #{create_time, jdbcType=DATE} ,
		    CONTENT = #{content, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE CLOB_ID = #{clob_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClobByClobId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOB_ID, A.TXT_CONTENT, A.CREATE_TIME, A.CONTENT FROM APP___PAS__DBUSER.T_CLOB A WHERE 1 = 1  ]]>
		<include refid="queryClobByClobIdCondition" />
		<![CDATA[ ORDER BY A.CLOB_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClob" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOB_ID, A.TXT_CONTENT, A.CREATE_TIME, A.CONTENT FROM APP___PAS__DBUSER.T_CLOB A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CLOB_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClob" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLOB_ID, A.TXT_CONTENT, A.CREATE_TIME, A.CONTENT FROM APP___PAS__DBUSER.T_CLOB A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CLOB_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClobTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CLOB A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClobForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CLOB_ID, B.TXT_CONTENT, B.CREATE_TIME, B.CONTENT FROM (
					SELECT ROWNUM RN, A.CLOB_ID, A.TXT_CONTENT, A.CREATE_TIME, A.CONTENT FROM APP___PAS__DBUSER.T_CLOB A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CLOB_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
