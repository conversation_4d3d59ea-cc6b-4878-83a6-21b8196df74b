<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.INaturalYearIncomeDao">

	<sql id="CUS_naturalYearIncomeWhereCondition">
		<if test=" birthday  != null  and  birthday  != ''  "><![CDATA[ AND A.BIRTHDAY = #{birthday} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" name != null and name != ''  "><![CDATA[ AND A.NAME = #{name} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" income  != null "><![CDATA[ AND A.INCOME = #{income} ]]></if>
		<if test=" cert_type != null and cert_type != ''  "><![CDATA[ AND A.CERT_TYPE = #{cert_type} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" gender  != null "><![CDATA[ AND A.GENDER = #{gender} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryNaturalYearIncomeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryNaturalYearIncomeByAcceptIdCondition">
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
	</sql>	
	<sql id="CUS_queryNaturalYearIncomeByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="CUS_queryNaturalYearIncomeByChangeIdCondition">
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addNaturalYearIncome"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_NATURAL_YEAR_IN__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME(
				BIRTHDAY, ACCEPT_ID, NAME, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, INCOME, 
				INSERT_TIMESTAMP, CERT_TYPE, CERTI_CODE, UPDATE_BY, GENDER, CHANGE_ID, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY, AGENT_CODE ) 
			VALUES (
				#{birthday, jdbcType=DATE}, #{accept_id, jdbcType=NUMERIC} , #{name, jdbcType=VARCHAR} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{income, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{cert_type, jdbcType=VARCHAR} , #{certi_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{gender, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{agent_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>
	
<!-- 批量添加操作 -->	
	<insert id="CUS_addNaturalYearIncomeList" parameterType="java.util.List" useGeneratedKeys="false">
		INSERT INTO APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME(
				LIST_ID, BIRTHDAY, ACCEPT_ID, NAME, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, INCOME, 
				INSERT_TIMESTAMP, CERT_TYPE, CERTI_CODE, UPDATE_BY, GENDER, CHANGE_ID,  
				UPDATE_TIMESTAMP, INSERT_BY, AGENT_CODE )
				SELECT APP___PAS__DBUSER.S_NATURAL_YEAR_IN__LIST_ID.NEXTVAL, A.* FROM (
					<foreach collection="list" item="var" index="index" separator="UNION ALL">
						SELECT
						    #{var.birthday, jdbcType=DATE} AS BIRTHDAY,
						    #{var.acceptId, jdbcType=NUMERIC} AS ACCEPT_ID,
						    #{var.name, jdbcType=VARCHAR} AS NAME,
						    SYSDATE as INSERT_TIME,
						    #{var.customerId, jdbcType=NUMERIC} AS CUSTOMER_ID,
						    SYSDATE as UPDATE_TIME,
						    #{var.income, jdbcType=NUMERIC} AS INCOME,
						    CURRENT_TIMESTAMP as INSERT_TIMESTAMP,
						    #{var.certType, jdbcType=VARCHAR} AS CERT_TYPE,
						    #{var.certiCode, jdbcType=VARCHAR} AS CERTI_CODE,
						    0 AS UPDATE_BY,
						    #{var.gender, jdbcType=NUMERIC} AS GENDER,
						    #{var.changeId, jdbcType=NUMERIC} AS CHANGE_ID,
						    CURRENT_TIMESTAMP AS UPDATE_TIMESTAMP,
						    0 AS INSERT_BY,
						    #{var.agentCode, jdbcType=VARCHAR} AS AGENT_CODE
			 			FROM DUAL
					</foreach>
				) A
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteNaturalYearIncome" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME WHERE  A.LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateNaturalYearIncome" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME ]]>
		<set>
		<trim suffixOverrides=",">
		    BIRTHDAY = #{birthday, jdbcType=DATE} ,
		    ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} ,
			NAME = #{name, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    INCOME = #{income, jdbcType=NUMERIC} ,
			CERT_TYPE = #{cert_type, jdbcType=VARCHAR} ,
			CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    GENDER = #{gender, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id}  ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findNaturalYearIncomeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE 1 = 1  ]]>
		<include refid="CUS_queryNaturalYearIncomeByListIdCondition" />
	</select>
	<!-- AE、CM、BC适用 -->
	<select id="CUS_findNaturalYearIncomeByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE 1 = 1  ]]>
		<include refid="CUS_queryNaturalYearIncomeByAcceptIdCondition" />
		<include refid="CUS_queryNaturalYearIncomeByCustomerIdCondition" />
		<![CDATA[  ORDER BY A.INSERT_TIME DESC) WHERE ROWNUM = 1 ]]>
	</select>
	
	<select id="CUS_findNaturalYearIncomesByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE 1 = 1  ]]>
		<include refid="CUS_queryNaturalYearIncomeByAcceptIdCondition" />
	</select>
	
	<select id="CUS_findNaturalYearIncomeByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE 1 = 1  ]]>
		<include refid="CUS_queryNaturalYearIncomeByChangeIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapNaturalYearIncome" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllNaturalYearIncome" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_queryNaturalYearIncomeByAcceptIdCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findNaturalYearIncomeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE 1 = 1  ]]>
		<include refid="CUS_naturalYearIncomeWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryNaturalYearIncomeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BIRTHDAY, B.ACCEPT_ID, B.NAME, B.CUSTOMER_ID, B.INCOME, 
			B.CERT_TYPE, B.CERTI_CODE, B.GENDER, B.CHANGE_ID, B.LIST_ID, 
			B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 根据客户ID查询最近的年收入 -->
	<select id="CUS_findNEWIncome" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select *
		  from (select y.income,y.insert_time
		          from dev_pas.t_Natural_Year_Income y
		         where y.customer_id = #{customer_id}
		         order by y.insert_time desc)
		 where rownum = 1
		 ]]>
	</select>
<!-- 根据acceptId查询投保人录入的年收入 -->
	<select id="queryPolicyHolderIncome" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select t.income,t.customer_id
  			from dev_pas.t_Natural_Year_Income t
 			where t.accept_id = #{accept_id}
   			and t.customer_id in
       			(select ph.customer_id
          			from dev_pas.t_cs_policy_holder ph, dev_pas.t_cs_policy_change pc
         		where pc.policy_chg_id = ph.policy_chg_id
           		and pc.accept_id = #{accept_id})
		 ]]>
	</select>
<!-- 根据acceptId查询被保人录入的年收入 -->
	<select id="queryInsuredListIncome" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select a.income, a.customer_id
		  from (select t.income, t.customer_id
		          from dev_pas.t_Natural_Year_Income t
		         where t.accept_id = #{accept_id}
		           and t.customer_id in
		               (select il.customer_id
		                  from dev_pas.t_cs_insured_list  il,
		                       dev_pas.t_cs_policy_change pc
		                 where pc.policy_chg_id = il.policy_chg_id
		                   and pc.accept_id = #{accept_id})
		         order by t.update_time desc) a
		 where rownum = 1
		 ]]>
	</select>
	
	<!-- 根据acceptId/customerId查询新增客户的五要素和年收入-->
	<select id="queryIncomeFive" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE
  			from dev_pas.t_Natural_Year_Income A
 			where 1 = 1
 	]]>
 			<include refid="CUS_naturalYearIncomeWhereCondition" />
	</select>
	
	<select id="CUS_findAllNaturalYearIncomeByConditions" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BIRTHDAY, A.ACCEPT_ID, A.NAME, A.CUSTOMER_ID, A.INCOME, 
			A.CERT_TYPE, A.CERTI_CODE, A.GENDER, A.CHANGE_ID, A.LIST_ID, 
			A.AGENT_CODE FROM APP___PAS__DBUSER.T_NATURAL_YEAR_INCOME A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_naturalYearIncomeWhereCondition" />
	</select>
</mapper>
