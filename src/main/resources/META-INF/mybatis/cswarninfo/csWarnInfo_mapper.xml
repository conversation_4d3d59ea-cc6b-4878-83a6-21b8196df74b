<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsWarnInfoDao">

	<sql id="csWarnInfoWhereCondition">
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" msg_des != null and msg_des != ''  "><![CDATA[ AND A.MSG_DES = #{msg_des} ]]></if>
		<if test=" warn_type  != null "><![CDATA[ AND A.WARN_TYPE = #{warn_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsWarnInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsWarnInfoByChangeIdCondition">
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsWarnInfoByAcceptIdCondition">
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsWarnInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_WARN_INFO__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_WARN_INFO(
				INSERT_TIMESTAMP, ACCEPT_ID, INSERT_TIME, CHANGE_ID, LIST_ID,  
				MSG_DES, WARN_TYPE, INSERT_BY, UPDATE_BY,
				UPDATE_TIME,UPDATE_TIMESTAMP) 
			VALUES (
				CURRENT_TIMESTAMP, #{accept_id, jdbcType=NUMERIC} ,  SYSDATE , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC}
				, #{msg_des, jdbcType=VARCHAR} , #{warn_type, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC},#{update_by, jdbcType=NUMERIC}
				, SYSDATE,CURRENT_TIMESTAMP) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsWarnInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_WARN_INFO WHERE  CHANGE_ID = #{change_id, jdbcType=NUMERIC} 
				and ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} and WARN_TYPE = #{warn_type, jdbcType=NUMERIC}]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsWarnInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_WARN_INFO ]]>
		<set>
		<trim suffixOverrides=",">
		    ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			MSG_DES = #{msg_des, jdbcType=VARCHAR} ,
		    WARN_TYPE = #{warn_type, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  = #{} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsWarnInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.CHANGE_ID, A.LIST_ID, 
			A.MSG_DES, A.WARN_TYPE FROM APP___PAS__DBUSER.T_CS_WARN_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsWarnInfoByListIdCondition" />
	</select>
	
	<select id="CUS_findCsWarnInfoByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.CHANGE_ID, A.LIST_ID, 
			A.MSG_DES, A.WARN_TYPE FROM APP___PAS__DBUSER.T_CS_WARN_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsWarnInfoByChangeIdCondition" />
	</select>
	
	<select id="CUS_findCsWarnInfoByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.CHANGE_ID, A.LIST_ID, 
			A.MSG_DES, A.WARN_TYPE FROM APP___PAS__DBUSER.T_CS_WARN_INFO A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsWarnInfoByAcceptIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findCsWarnInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.CHANGE_ID, A.LIST_ID, 
			A.MSG_DES, A.WARN_TYPE FROM APP___PAS__DBUSER.T_CS_WARN_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="csWarnInfoWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsWarnInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_ID, A.CHANGE_ID, A.LIST_ID, 
			A.MSG_DES, A.WARN_TYPE FROM APP___PAS__DBUSER.T_CS_WARN_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsWarnInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_WARN_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsWarnInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCEPT_ID, B.CHANGE_ID, B.LIST_ID, 
			B.MSG_DES, B.WARN_TYPE FROM (
					SELECT ROWNUM RN, A.ACCEPT_ID, A.CHANGE_ID, A.LIST_ID, 
			A.MSG_DES, A.WARN_TYPE FROM APP___PAS__DBUSER.T_CS_WARN_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
