<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISurvivalDeductionTaskDao">

	<sql id="PA_survivalDeductionTaskWhereCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" pay_id  != null "><![CDATA[ AND A.PAY_ID = #{pay_id} ]]></if>
		<if test=" dedu_result != null and dedu_result != ''  "><![CDATA[ AND A.DEDU_RESULT = #{dedu_result} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" dedu_ag_unit_number != null and dedu_ag_unit_number != ''  "><![CDATA[ AND A.DEDU_AG_UNIT_NUMBER = #{dedu_ag_unit_number} ]]></if>
		<if test=" dedu_task_id  != null "><![CDATA[ AND A.DEDU_TASK_ID = #{dedu_task_id} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" dedu_cs_unit_number != null and dedu_cs_unit_number != ''  "><![CDATA[ AND A.DEDU_CS_UNIT_NUMBER = #{dedu_cs_unit_number} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" accept_time  != null  and  accept_time  != ''  "><![CDATA[ AND A.ACCEPT_TIME = #{accept_time} ]]></if>
		<if test=" dedu_task_state != null and dedu_task_state != ''  "><![CDATA[ AND A.DEDU_TASK_STATE = #{dedu_task_state} ]]></if>
		<if test=" bacl_unit_number != null and bacl_unit_number != ''  "><![CDATA[ AND A.BACL_UNIT_NUMBER = #{bacl_unit_number} ]]></if>
		<if test=" accept_status != null and accept_status != ''  "><![CDATA[ AND A.ACCEPT_STATUS = #{accept_status} ]]></if>
		<if test=" bacl_accept_code != null and bacl_accept_code != ''  "><![CDATA[ AND A.BACL_ACCEPT_CODE = #{bacl_accept_code} ]]></if>
		<if test=" dedu_date  != null  and  dedu_date  != ''  "><![CDATA[ AND A.DEDU_DATE = #{dedu_date} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" accept_result_desc != null and accept_result_desc != ''  "><![CDATA[ AND A.ACCEPT_RESULT_DESC = #{accept_result_desc} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" dedu_amount  != null "><![CDATA[ AND A.DEDU_AMOUNT = #{dedu_amount} ]]></if>
		<if test=" pa_policy_chg_id  != null "><![CDATA[ AND A.PA_POLICY_CHG_ID = #{pa_policy_chg_id} ]]></if>
		<if test=" cs_policy_chg_id  != null "><![CDATA[ AND A.CS_POLICY_CHG_ID = #{cs_policy_chg_id} ]]></if>
		<if test="planIdList  != null and planIdList.size()!=0 ">
			<![CDATA[ AND A.PLAN_ID in (]]>
			<foreach collection="planIdList" item="plan_id"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{plan_id} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>


	<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySurvivalDeductionTaskByDeduTaskIdCondition">
		<if test=" dedu_task_id  != null "><![CDATA[ AND A.DEDU_TASK_ID = #{dedu_task_id} ]]></if>
	</sql>	
	<sql id="PA_querySurvivalDeductionTaskByPayIdCondition">
		<if test=" pay_id  != null "><![CDATA[ AND A.PAY_ID = #{pay_id} ]]></if>
	</sql>	
	<sql id="PA_querySurvivalDeductionTaskByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="PA_addSurvivalDeductionTask"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="dedu_task_id">
			SELECT APP___PAS__DBUSER.S_SURVIVAL_DEDUCTION_TASK.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK(
				POLICY_ID, PAY_ID, DEDU_RESULT, INTEREST_CAPITAL, DEDU_AG_UNIT_NUMBER, DEDU_TASK_ID, PLAN_ID, INSERT_TIME, 
				DEDU_CS_UNIT_NUMBER, UPDATE_TIME, ACCEPT_CODE, ACCEPT_TIME, INSERT_TIMESTAMP, DEDU_TASK_STATE, BACL_UNIT_NUMBER, 
				ACCEPT_STATUS, UPDATE_BY, BACL_ACCEPT_CODE, DEDU_DATE, PAY_DUE_DATE, FEE_AMOUNT, UPDATE_TIMESTAMP, 
				INSERT_BY, ACCEPT_RESULT_DESC, SERVICE_CODE, DEDU_AMOUNT, PA_POLICY_CHG_ID, CS_POLICY_CHG_ID ) 
			VALUES (
				#{policy_id, jdbcType=NUMERIC}, #{pay_id, jdbcType=NUMERIC}, #{dedu_result, jdbcType=VARCHAR} , #{interest_capital, jdbcType=NUMERIC} , #{dedu_ag_unit_number, jdbcType=VARCHAR} , #{dedu_task_id, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE 
				, #{dedu_cs_unit_number, jdbcType=VARCHAR} , SYSDATE , #{accept_code, jdbcType=VARCHAR} , #{accept_time, jdbcType=DATE} , CURRENT_TIMESTAMP, #{dedu_task_state, jdbcType=VARCHAR} , #{bacl_unit_number, jdbcType=VARCHAR} 
				, #{accept_status, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{bacl_accept_code, jdbcType=VARCHAR} , #{dedu_date, jdbcType=DATE} , #{pay_due_date, jdbcType=DATE} , #{fee_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{accept_result_desc, jdbcType=VARCHAR} , #{service_code, jdbcType=VARCHAR} , #{dedu_amount, jdbcType=NUMERIC} , #{pa_policy_chg_id, jdbcType=NUMERIC}, #{cs_policy_chg_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="PA_deleteSurvivalDeductionTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK WHERE  = #{dedu_task_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="PA_updateSurvivalDeductionTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PAY_ID = #{pay_id, jdbcType=NUMERIC} ,
			DEDU_RESULT = #{dedu_result, jdbcType=VARCHAR} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
			DEDU_AG_UNIT_NUMBER = #{dedu_ag_unit_number, jdbcType=VARCHAR} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			DEDU_CS_UNIT_NUMBER = #{dedu_cs_unit_number, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		    ACCEPT_TIME = #{accept_time, jdbcType=DATE} ,
			DEDU_TASK_STATE = #{dedu_task_state, jdbcType=VARCHAR} ,
			BACL_UNIT_NUMBER = #{bacl_unit_number, jdbcType=VARCHAR} ,
			ACCEPT_STATUS = #{accept_status, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			BACL_ACCEPT_CODE = #{bacl_accept_code, jdbcType=VARCHAR} ,
		    DEDU_DATE = #{dedu_date, jdbcType=DATE} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			ACCEPT_RESULT_DESC = #{accept_result_desc, jdbcType=VARCHAR} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		    DEDU_AMOUNT = #{dedu_amount, jdbcType=NUMERIC} ,
		    PA_POLICY_CHG_ID = #{pa_policy_chg_id, jdbcType=NUMERIC} ,
			CS_POLICY_CHG_ID = #{cs_policy_chg_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  DEDU_TASK_ID = #{dedu_task_id} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="PA_findSurvivalDeductionTaskByDeduTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID, A.PAY_ID, A.DEDU_RESULT, A.INTEREST_CAPITAL, A.DEDU_AG_UNIT_NUMBER, A.DEDU_TASK_ID, A.PLAN_ID, 
			A.DEDU_CS_UNIT_NUMBER, A.ACCEPT_CODE, A.ACCEPT_TIME, A.DEDU_TASK_STATE, A.BACL_UNIT_NUMBER, 
			A.ACCEPT_STATUS, A.BACL_ACCEPT_CODE, A.DEDU_DATE, A.PAY_DUE_DATE, A.FEE_AMOUNT, 
			A.ACCEPT_RESULT_DESC, A.SERVICE_CODE, A.DEDU_AMOUNT, A.PA_POLICY_CHG_ID , A.CS_POLICY_CHG_ID FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_querySurvivalDeductionTaskByDeduTaskIdCondition" />
	</select>
	
	<select id="PA_findSurvivalDeductionTaskByPayId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID, A.PAY_ID, A.DEDU_RESULT, A.INTEREST_CAPITAL, A.DEDU_AG_UNIT_NUMBER, A.DEDU_TASK_ID, A.PLAN_ID, 
			A.DEDU_CS_UNIT_NUMBER, A.ACCEPT_CODE, A.ACCEPT_TIME, A.DEDU_TASK_STATE, A.BACL_UNIT_NUMBER, 
			A.ACCEPT_STATUS, A.BACL_ACCEPT_CODE, A.DEDU_DATE, A.PAY_DUE_DATE, A.FEE_AMOUNT, 
			A.ACCEPT_RESULT_DESC, A.SERVICE_CODE, A.DEDU_AMOUNT, A.PA_POLICY_CHG_ID , A.CS_POLICY_CHG_ID FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_querySurvivalDeductionTaskByPayIdCondition" />
	</select>
	
	<select id="PA_findSurvivalDeductionTaskByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID, A.PAY_ID, A.DEDU_RESULT, A.INTEREST_CAPITAL, A.DEDU_AG_UNIT_NUMBER, A.DEDU_TASK_ID, A.PLAN_ID, 
			A.DEDU_CS_UNIT_NUMBER, A.ACCEPT_CODE, A.ACCEPT_TIME, A.DEDU_TASK_STATE, A.BACL_UNIT_NUMBER, 
			A.ACCEPT_STATUS, A.BACL_ACCEPT_CODE, A.DEDU_DATE, A.PAY_DUE_DATE, A.FEE_AMOUNT, 
			A.ACCEPT_RESULT_DESC, A.SERVICE_CODE, A.DEDU_AMOUNT, A.PA_POLICY_CHG_ID , A.CS_POLICY_CHG_ID FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_querySurvivalDeductionTaskByPlanIdCondition" />
	</select>
	

	<!-- 按map查询操作 -->
	<select id="PA_findAllMapSurvivalDeductionTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID, A.PAY_ID, A.DEDU_RESULT, A.INTEREST_CAPITAL, A.DEDU_AG_UNIT_NUMBER, A.DEDU_TASK_ID, A.PLAN_ID, 
			A.DEDU_CS_UNIT_NUMBER, A.ACCEPT_CODE, A.ACCEPT_TIME, A.DEDU_TASK_STATE, A.BACL_UNIT_NUMBER, 
			A.ACCEPT_STATUS, A.BACL_ACCEPT_CODE, A.DEDU_DATE, A.PAY_DUE_DATE, A.FEE_AMOUNT, 
			A.ACCEPT_RESULT_DESC, A.SERVICE_CODE, A.DEDU_AMOUNT, A.PA_POLICY_CHG_ID , A.CS_POLICY_CHG_ID FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_survivalDeductionTaskWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAllSurvivalDeductionTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID, A.PAY_ID, A.DEDU_RESULT, A.INTEREST_CAPITAL, A.DEDU_AG_UNIT_NUMBER, A.DEDU_TASK_ID, A.PLAN_ID, 
			A.DEDU_CS_UNIT_NUMBER, A.ACCEPT_CODE, A.ACCEPT_TIME, A.DEDU_TASK_STATE, A.BACL_UNIT_NUMBER, 
			A.ACCEPT_STATUS, A.BACL_ACCEPT_CODE, A.DEDU_DATE, A.PAY_DUE_DATE, A.FEE_AMOUNT, 
			A.ACCEPT_RESULT_DESC, A.SERVICE_CODE, A.DEDU_AMOUNT, A.PA_POLICY_CHG_ID , A.CS_POLICY_CHG_ID FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_survivalDeductionTaskWhereCondition" />
	</select>

	<!-- 查询对象信息 -->
	<select id="PA_findSurvivalDeductionTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_ID, A.PAY_ID, A.DEDU_RESULT, A.INTEREST_CAPITAL, A.DEDU_AG_UNIT_NUMBER, A.DEDU_TASK_ID, A.PLAN_ID, 
			A.DEDU_CS_UNIT_NUMBER, A.ACCEPT_CODE, A.ACCEPT_TIME, A.DEDU_TASK_STATE, A.BACL_UNIT_NUMBER, 
			A.ACCEPT_STATUS, A.BACL_ACCEPT_CODE, A.DEDU_DATE, A.PAY_DUE_DATE, A.FEE_AMOUNT, 
			A.ACCEPT_RESULT_DESC, A.SERVICE_CODE, A.DEDU_AMOUNT, A.PA_POLICY_CHG_ID , A.CS_POLICY_CHG_ID FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_survivalDeductionTaskWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="PA_findSurvivalDeductionTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A WHERE 1 = 1  ]]>
		 <include refid="PA_survivalDeductionTaskWhereCondition" />
	</select>
	
	<!-- 查询贷款抵扣总金额 -->
	<select id="PA_findSurvivalDeductionTaskDeduAmounInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[  SELECT A.POLICY_ID, A.PLAN_ID, SUM(A.DEDU_AMOUNT) DEDU_AMOUNT
   					FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A
  				WHERE 1 = 1 ]]>
	<include refid="PA_survivalDeductionTaskWhereCondition" />
		<![CDATA[ GROUP BY A.POLICY_ID, A.PLAN_ID ]]>		
	</select>
	
	<!-- 查询符合贷款抵扣任务批处理数据 -->
	<select id="PA_findAllSurvivalDeductionTaskList" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[  SELECT T.*, ROWNUM
				  FROM (
				   SELECT P.POLICY_ID,
               	   		  P.PAY_PLAN_TYPE,
                          P.POLICY_CODE,
                          P.PAY_DUE_DATE,
                          P.PLAN_ID,
                          ROW_NUMBER() OVER(PARTITION BY P.PLAN_ID,P.PAY_PLAN_TYPE ORDER BY P.PAY_DUE_DATE DESC) RN
			          FROM (
							    SELECT A.POLICY_ID,
							       (CASE WHEN M.PAY_PLAN_TYPE IN ('3','10','11') THEN '3' WHEN M.PAY_PLAN_TYPE = 4 THEN '4'END) PAY_PLAN_TYPE,M.POLICY_CODE,A.PLAN_ID,A.PAY_DUE_DATE
							          FROM DEV_PAS.T_SURVIVAL_DEDUCTION_TASK A,
							               DEV_PAS.T_PAY_PLAN         M
							         WHERE 1 = 1
							           AND A.POLICY_ID = M.POLICY_ID
							           AND A.PLAN_ID = M.PLAN_ID
							           AND NOT EXISTS (SELECT 1
							                  FROM DEV_PAS.T_SURVIVAL_DEDUCTION_TASK T
							                 WHERE A.POLICY_ID = T.POLICY_ID
							                   AND A.PAY_DUE_DATE = T.PAY_DUE_DATE
							                   AND T.DEDU_TASK_STATE IN ('1', '2'))
							           AND A.DEDU_RESULT IN ('1','2','4')
							           AND A.Dedu_Task_State IN ('3','5')
				           ]]>
		 <if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE <= #{pay_due_date} ]]></if>
		 <if test=" policy_code  != null  and  policy_code  != ''  "><![CDATA[ AND M.POLICY_CODE = #{policy_code} ]]></if>
	<![CDATA[ ) P ) T WHERE 1 = 1 AND RN = 1 AND MOD(T.POLICY_ID , #{modnum}) = #{start} ]]>

	</select>
	
	<select id="CS_findAllSurvivalDeductionTaskForCS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.* FROM ( SELECT ROWNUM RN, A.POLICY_ID, A.PAY_ID, A.DEDU_RESULT, A.INTEREST_CAPITAL, A.DEDU_AG_UNIT_NUMBER, A.DEDU_TASK_ID, A.PLAN_ID, 
					A.DEDU_CS_UNIT_NUMBER, A.ACCEPT_CODE, A.ACCEPT_TIME, A.DEDU_TASK_STATE, A.BACL_UNIT_NUMBER, 
					A.ACCEPT_STATUS, A.BACL_ACCEPT_CODE, A.DEDU_DATE, A.PAY_DUE_DATE, A.FEE_AMOUNT, 
					A.ACCEPT_RESULT_DESC, A.SERVICE_CODE, A.DEDU_AMOUNT, A.PA_POLICY_CHG_ID , A.CS_POLICY_CHG_ID 
				FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A,DEV_PAS.T_PAY_PLAN B
				WHERE A.DEDU_TASK_STATE = '1' 
				AND  A.PLAN_ID = B.PLAN_ID
		]]>
		<if test="policy_code != null and policy_code!='' ">
		<![CDATA[ 
				AND B.POLICY_CODE = #{policy_code}
		]]>
		</if>
		
		<if test="dedu_date != null and dedu_date!='' ">
		<![CDATA[ 
				AND A.PAY_DUE_DATE <= #{dedu_date}
		]]>
		</if>
		
		<![CDATA[ ORDER BY B.POLICY_CODE,A.PAY_DUE_DATE ASC) T ]]>
		<if test="start != null and counts != null ">
		<![CDATA[ 
			WHERE MOD(T.POLICY_ID, #{counts})= #{start}
		]]>
		</if>
	</select>
	<!-- 查询保单存在部分金额抵扣保单贷款且抵扣贷款剩余金额未领取的应领未领年金/生存金/满期金 -->
	<select id="CS_findSurvivalDeduNotClaimed" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_SURVIVAL_DEDUCTION_TASK A JOIN DEV_PAS.T_PAY_DUE B ON A.PAY_ID = B.PAY_ID
		WHERE A.POLICY_ID = #{policy_id} AND A.DEDU_RESULT = '1' AND B.FEE_STATUS = '00' ]]>
	</select>

	<select id="CS_querySumDeduAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT SUM(NVL(A.DEDU_AMOUNT, 0)) AS DEDU_AMOUNT
					  FROM APP___PAS__DBUSER.T_SURVIVAL_DEDUCTION_TASK A, DEV_PAS.T_PAY_DUE B
					 WHERE  A.PAY_ID = B.PAY_ID
					 	AND B.DEDUCTION_FLAG = 1	 /**已抵扣*/
					 	AND B.FEE_STATUS = '00' 
					 	AND B.POLICY_CODE = #{policy_code}	
		]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND B.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND B.ITEM_ID = #{item_id} ]]></if>
		 
	</select>
	
	<select id="CS_findUnitNumberForRB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select distinct a.unit_number as dedu_cs_unit_number,
                c.unit_number as dedu_ag_unit_number
  				from dev_pas.t_prem_arap a
  				join dev_pas.t_survival_deduction_task b
    			on a.rollback_unit_number = b.dedu_cs_unit_number
  				join dev_pas.t_prem_arap c
   				on b.dedu_ag_unit_number = c.rollback_unit_number
 				where a.business_code = #{accept_code}
		]]>
	</select>
</mapper>
