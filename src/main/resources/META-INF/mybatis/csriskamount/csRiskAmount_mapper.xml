<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="APP___PAS__DBUSER.T_CS_RISK_AMOUNT">

	<sql id="csRiskAmountWhereCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" amount_status  != null "><![CDATA[ AND A.AMOUNT_STATUS = #{amount_status} ]]></if>
		<if test=" risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT = #{risk_amount} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsRiskAmountByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsRiskAmount"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_AGENT.NEXTVAL FROM DUAL
	</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_RISK_AMOUNT(
				CUSTOMER_ID, ITEM_ID, RISK_TYPE, BUSI_PROD_CODE, APPLY_CODE, INSERT_TIMESTAMP, OLD_NEW, 
				UPDATE_BY, CHANGE_ID, LIST_ID, POLICY_CHG_ID, BUSI_ITEM_ID, POLICY_ID, INSERT_TIME, 
				OPERATION_TYPE, START_TIME, UPDATE_TIME, LOG_ID, POLICY_CODE, UPDATE_TIMESTAMP, END_TIME, 
				INSERT_BY, INTERNAL_CODE, AMOUNT_STATUS, RISK_AMOUNT ) 
			VALUES (
				#{customer_id, jdbcType=NUMERIC}, #{item_id, jdbcType=NUMERIC} , #{risk_type, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{old_new, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , SYSDATE 
				, #{operation_type, jdbcType=VARCHAR} , #{start_time, jdbcType=DATE} , SYSDATE , #{log_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{end_time, jdbcType=DATE} 
				, #{insert_by, jdbcType=NUMERIC} , #{internal_code, jdbcType=VARCHAR} , #{amount_status, jdbcType=NUMERIC} , #{risk_amount, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsRiskAmount" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT WHERE  LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsRiskAmount" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_RISK_AMOUNT ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			RISK_TYPE = #{risk_type, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
		    START_TIME = #{start_time, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
			INTERNAL_CODE = #{internal_code, jdbcType=VARCHAR} ,
		    AMOUNT_STATUS = #{amount_status, jdbcType=NUMERIC} ,
		    RISK_AMOUNT = #{risk_amount, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE log_id = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsRiskAmountByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.OLD_NEW, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.OPERATION_TYPE, A.START_TIME, A.LOG_ID, A.POLICY_CODE, A.END_TIME, 
			A.INTERNAL_CODE, A.AMOUNT_STATUS, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT A WHERE 1 = 1  ]]>
		<include refid="queryCsRiskAmountByLogIdCondition" />
		<![CDATA[ ORDER BY A.BUSI_PROD_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.OLD_NEW, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.OPERATION_TYPE, A.START_TIME, A.LOG_ID, A.POLICY_CODE, A.END_TIME, 
			A.INTERNAL_CODE, A.AMOUNT_STATUS, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BUSI_PROD_CODE ]]>
	</select>


	<select id="findCsRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.OLD_NEW, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.OPERATION_TYPE, A.START_TIME, A.LOG_ID, A.POLICY_CODE, A.END_TIME, 
			A.INTERNAL_CODE, A.AMOUNT_STATUS, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="csRiskAmountWhereCondition" />
		<![CDATA[ ORDER BY A.BUSI_PROD_CODE ]]>
	</select>
<!-- 查询所有操作 -->
	<select id="findAllCsRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.OLD_NEW, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.OPERATION_TYPE, A.START_TIME, A.LOG_ID, A.POLICY_CODE, A.END_TIME, 
			A.INTERNAL_CODE, A.AMOUNT_STATUS, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="csRiskAmountWhereCondition" />
		<![CDATA[ ORDER BY A.BUSI_PROD_CODE ]]> 
	</select>
<!-- 查询所有操作 -->
	<select id="findAllCsRiskAmountByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.OLD_NEW, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.OPERATION_TYPE, A.START_TIME, A.LOG_ID, A.POLICY_CODE, A.END_TIME, 
			A.INTERNAL_CODE, A.AMOUNT_STATUS, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT A WHERE ROWNUM <=  1000  ]]>
			<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
			<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<![CDATA[ ORDER BY A.BUSI_PROD_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCsRiskAmountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsRiskAmountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_ID, B.ITEM_ID, B.RISK_TYPE, B.BUSI_PROD_CODE, B.APPLY_CODE, B.OLD_NEW, 
			B.CHANGE_ID, B.LIST_ID, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID, 
			B.OPERATION_TYPE, B.START_TIME, B.LOG_ID, B.POLICY_CODE, B.END_TIME, 
			B.INTERNAL_CODE, B.AMOUNT_STATUS, B.RISK_AMOUNT FROM (
					SELECT ROWNUM RN, A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.OLD_NEW, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.OPERATION_TYPE, A.START_TIME, A.LOG_ID, A.POLICY_CODE, A.END_TIME, 
			A.INTERNAL_CODE, A.AMOUNT_STATUS, A.RISK_AMOUNT FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BUSI_PROD_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findSumCsRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT RISK_TYPE, nvl(SUM(TERMINATEDRISK),0) AS TERMINATEDRISK, nvl(SUM(LAPSERISK),0) AS LAPSERISK, nvl(SUM(ACTIVERISK),0) AS ACTIVERISK ,CUSTOMER_ID,old_new 
  FROM (SELECT RISK_TYPE,
               CASE
                 WHEN AMOUNT_STATUS = 3 THEN
                  SUM(RISK_AMOUNT)
               END TERMINATEDRISK,
               CASE
                 WHEN AMOUNT_STATUS = 4 THEN
                  SUM(RISK_AMOUNT)
               END LAPSERISK,
               CASE
                 WHEN AMOUNT_STATUS = 1 THEN
                  SUM(RISK_AMOUNT)
               END ACTIVERISK ,CUSTOMER_ID ,old_new
          FROM (SELECT TR.RISK_TYPE AS RISK_TYPE,
                       SUM(TR.RISK_AMOUNT) AS RISK_AMOUNT,
                       CASE
                         WHEN TR.AMOUNT_STATUS = 3 OR TC.LIABILITY_STATE = 3 THEN
                          3
                         WHEN TC.LIABILITY_STATE = 4 THEN
                          4
                         ELSE
                          1
                       END AS AMOUNT_STATUS , TR.CUSTOMER_ID,tr.old_new
                  FROM APP___PAS__DBUSER.T_CS_RISK_AMOUNT TR
                  LEFT JOIN DEV_PAS.T_cs_CONTRACT_PRODUCT TC
                    ON TR.ITEM_ID = TC.ITEM_ID and tr.change_id=tc.change_id and tr.old_new=tc.old_new
                  where  TR.Change_Id=#{change_id}
                 GROUP BY TR.RISK_TYPE, TC.LIABILITY_STATE, TR.AMOUNT_STATUS, TR.CUSTOMER_ID,tr.old_new)
         GROUP BY RISK_TYPE, AMOUNT_STATUS,CUSTOMER_ID,old_new)
 GROUP BY RISK_TYPE,CUSTOMER_ID,old_new
		 ]]>
	</select>
	
		
	<select id="findCsRiskAmountB01Sum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
	      SELECT  SUM(E.RISK_AMOUNT) AS RISK_AMOUNT 
           FROM DEV_PAS.T_CUSTOMER A
          INNER JOIN DEV_PAS.T_INSURED_LIST C
             ON A.CUSTOMER_ID = C.CUSTOMER_ID
          INNER JOIN DEV_PAS.T_RISK_AMOUNT E
             ON C.POLICY_ID = E.POLICY_ID
             AND E.CUSTOMER_ID = A.CUSTOMER_ID
          WHERE
          1 = 1
          AND E.RISK_TYPE IN ('1', '2', '3', '10')
          AND EXISTS (SELECT 1
             FROM DEV_PAS.T_CUSTOMER B
            WHERE B.CUSTOMER_ID =  #{customer_id}
              AND A.CUSTOMER_NAME = B.CUSTOMER_NAME
              AND A.CUSTOMER_BIRTHDAY = B.CUSTOMER_BIRTHDAY
              AND A.CUSTOMER_GENDER = B.CUSTOMER_GENDER
              AND A.CUSTOMER_CERT_TYPE = B.CUSTOMER_CERT_TYPE
              AND A.CUSTOMER_CERTI_CODE = B.CUSTOMER_CERTI_CODE)
          AND EXISTS (SELECT 1
             FROM DEV_PAS.T_CONTRACT_MASTER D
            WHERE C.POLICY_ID = D.POLICY_ID
              AND D.LIABILITY_STATE = '4')
		]]>
	</select>
	
	<select id="findallCsRiskAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  select a.risk_type,a.risk_amount from (
          select t.policy_chg_id,t.risk_type,
           sum(case when t.old_new = '0' then t.risk_amount * -1
              else t.risk_amount  end) as risk_amount
			    from dev_pas.t_cs_risk_amount t where t.policy_chg_id = #{policy_chg_id}  and t.customer_id = #{customer_id} 
			    group by t.policy_chg_id,t.risk_type
			) A where a.risk_amount <> 0
		]]>
	</select>
	
	<!-- /**#PL2018121746981 fanzc_wb start**/ -->
	<select id="findAllCsRiskAmountByInTwoYear" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					
select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
       cra.risk_type,
       cra.busi_prod_code
      from APP___PAS__DBUSER.t_cs_accept_change cac,
           APP___PAS__DBUSER.t_cs_policy_change cpc,
           APP___PAS__DBUSER.t_cs_risk_amount   cra
     where cac.accept_id = cpc.accept_id
       and cpc.policy_chg_id = cra.policy_chg_id
       and cra.customer_id = #{customer_id}
       and cra.busi_prod_code = #{busi_prod_code}
       and cra.risk_type =#{risk_type}
       and cac.accept_status in ('07', '08', '09', '10', '11', '13')
       group by cra.risk_type, cra.busi_prod_code
  			]]>
	</select>
	
	<select id="findAllCsRiskAmountByChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  select A.CUSTOMER_ID, A.ITEM_ID, A.RISK_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.OLD_NEW, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.OPERATION_TYPE, A.START_TIME, A.LOG_ID, A.POLICY_CODE, A.END_TIME, 
			A.INTERNAL_CODE, A.AMOUNT_STATUS, A.RISK_AMOUNT from APP___PAS__DBUSER.t_cs_risk_amount A 
			where (A.operation_type = '1'or A.operation_type = '2') and A.policy_chg_id = ${policy_chg_id}
		]]>
	</select>
	<!-- /**#PL2018121746981 fanzc_wb end**/ -->
	
	
	
	<select id="findAllCsRiskAmountIng" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
	       cra.risk_type,
	       cra.busi_prod_code
	      from APP___PAS__DBUSER.t_cs_accept_change cac,
	           APP___PAS__DBUSER.t_cs_policy_change cpc,
	           APP___PAS__DBUSER.t_cs_risk_amount   cra
	     where cac.accept_id = cpc.accept_id
	       and cpc.policy_chg_id = cra.policy_chg_id
	       and cra.customer_id = #{customer_id}
	       and cra.busi_prod_code = #{busi_prod_code}
	       and cra.risk_type = #{risk_type}
	       and cac.accept_status in ('07', '08', '09', '10', '11', '13')
	       group by cra.risk_type, cra.busi_prod_code
		]]>
	</select>
	
	<select id="findAllCsRiskAmountIngZ301" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         cra.busi_prod_code
        from APP___PAS__DBUSER.t_cs_accept_change cac,
             APP___PAS__DBUSER.t_cs_policy_change cpc,
             APP___PAS__DBUSER.t_cs_risk_amount   cra
       where cac.accept_id = cpc.accept_id
         and cpc.policy_chg_id = cra.policy_chg_id
         and cra.customer_id =#{customer_id}
         and cra.busi_prod_code in ('00107000','00108000','00108001','00108002','00108003','00109000','00109001','00109002','00118000','00118001','00132000','00133000','00134000','00135000','00136000','00137000','00141000','00143000','00144000','00146000','00147000','00150000','00155000','00159000','00167000','00170000','00170100','00171000','00172000','00193000','00197000','00221000','00411000','00412000','00415000','00416000','00601000','00607000','00610000','00611000','00616000','00623000','00629000','00634000','00635000','00636000','00637000','00638000','00641000','00645000','00646000','00647000','00659000','00660100','00660200','00660300','00660400','00663000','00664000','00665000','00668000','00675000','00679000','00681000','00688000','00696000','00110A00','00110B00','00110B01','00110B02','00435000')
         and cra.risk_type in ('1','2')
         and cac.accept_status in ('07', '08', '09', '10', '11', '13')
         group by cra.risk_type, cra.busi_prod_code
		]]>
	</select>
	
	<select id="findAllCsRiskAmountIngZ303" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         cra.busi_prod_code
        from APP___PAS__DBUSER.t_cs_accept_change cac,
             APP___PAS__DBUSER.t_cs_policy_change cpc,
             APP___PAS__DBUSER.t_cs_risk_amount   cra
       where cac.accept_id = cpc.accept_id
         and cpc.policy_chg_id = cra.policy_chg_id
         and cra.customer_id =#{customer_id}
         and cra.busi_prod_code in ('00201000','00201001','00210000','00210001','00215000','00216000','00227000','00231000','01227000','00232000','02232000','00233000','00234000','00235000','03235000','00236000','00240000','00265000','00268000','00277000','00278000','00279000','00280000','00293000','00294000','00510000','00513100','00515000','00517000','00521000','00534000','00535000','00536000','00542000','00543000','00544000','00555000','00556000','00559000','00560000','00561000','00562000','00564000','00565000','00579000','00701000','00715000','00724000','00737000','00740000','00756000','00758000','00763100','00763200','00771000','00772000','00772100','00777000','00779100','00779200','00786000','00787000','00790000','00799000','00949000','00950000','00954000','00955000','00959000','00960000','00961000','00962000','00973000','00956000','00976000','00533000','00725000')
         and cra.risk_type ='3'
         and cac.accept_status in ('07', '08', '09', '10', '11', '13')
         group by cra.risk_type, cra.busi_prod_code
		]]>
	</select>
	<select id="findAllCsRiskAmountIngZ304" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         cra.busi_prod_code
        from APP___PAS__DBUSER.t_cs_accept_change cac,
             APP___PAS__DBUSER.t_cs_policy_change cpc,
             APP___PAS__DBUSER.t_cs_risk_amount   cra
       where cac.accept_id = cpc.accept_id
         and cpc.policy_chg_id = cra.policy_chg_id
         and cra.customer_id =#{customer_id}
         and cra.busi_prod_code in ('00724000','00740000','00756000','00771000','00787000','00799000','00949000','00954000','00955000','00959000','00960000','00961000','00962000','00976000','00533000','00725000')
         and cra.risk_type ='2'
         and cac.accept_status in ('07', '08', '09', '10', '11', '13')
         group by cra.risk_type, cra.busi_prod_code
		]]>
	</select>
	
	<select id="findAllCsRiskAmountIngForUW" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         cra.busi_prod_code,cra.policy_code
        from APP___PAS__DBUSER.t_cs_accept_change cac,
             APP___PAS__DBUSER.t_cs_policy_change cpc,
             APP___PAS__DBUSER.t_cs_risk_amount   cra
       where cac.accept_id = cpc.accept_id
         and cpc.policy_chg_id = cra.policy_chg_id
         and cra.customer_id =#{customer_id}
         and cac.accept_status in ('07', '08', '09', '10', '11', '13')
         group by cra.risk_type, cra.busi_prod_code,cra.policy_code
		]]>
	</select>
	
	
		<select id="findAllCsRiskAmountIngForPA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         cra.busi_prod_code,cra.policy_code
        from APP___PAS__DBUSER.t_cs_accept_change cac,
             APP___PAS__DBUSER.t_cs_policy_change cpc,
             APP___PAS__DBUSER.t_cs_risk_amount   cra
       where cac.accept_id = cpc.accept_id
         and cpc.policy_chg_id = cra.policy_chg_id
         and cra.customer_id =#{customer_id}
         and cac.accept_status in ('07', '08', '09', '10', '11', '13')
         group by cra.risk_type, cra.busi_prod_code,cra.policy_code
		]]>
	</select>
	
	<select id="findAllCsRiskAmountFor588BQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			   SELECT CP.PRODUCT_CODE AS INTERNAL_CODE, RA.RISK_AMOUNT,RA.RISK_TYPE,RA.ITEM_ID
			     FROM DEV_PAS.T_CS_RISK_AMOUNT RA, DEV_PAS.T_CONTRACT_PRODUCT CP
			    WHERE CP.ITEM_ID = RA.ITEM_ID
			      AND RA.OLD_NEW = '1'
			      AND RA.POLICY_CHG_ID = #{policy_chg_id}
			      AND RA.POLICY_CODE = #{policy_code}
		]]>
	</select>
</mapper>
