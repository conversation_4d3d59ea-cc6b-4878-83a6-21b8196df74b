<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyMarkingBacthInfoDao">
<!--
	<sql id="PAS_policyMarkingBacthInfoWhereCondition">
		<if test=" mark_id  != null "><![CDATA[ AND A.MARK_ID = #{mark_id} ]]></if>
		<if test=" interest_capital  != null "><![CDATA[ AND A.INTEREST_CAPITAL = #{interest_capital} ]]></if>
		<if test=" batch_month  != null "><![CDATA[ AND A.BATCH_MONTH = #{batch_month} ]]></if>
		<if test=" account_balance  != null "><![CDATA[ AND A.ACCOUNT_BALANCE = #{account_balance} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" bacth_id  != null "><![CDATA[ AND A.BACTH_ID = #{bacth_id} ]]></if>
		<if test=" cash_value  != null "><![CDATA[ AND A.CASH_VALUE = #{cash_value} ]]></if>
		<if test=" calcu_date  != null  and  calcu_date  != ''  "><![CDATA[ AND A.CALCU_DATE = #{calcu_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryPolicyMarkingBacthInfoByBacthIdCondition">
		<if test=" bacth_id  != null "><![CDATA[ AND A.BACTH_ID = #{bacth_id} ]]></if>
	</sql>	
	<sql id="PAS_queryPolicyMarkingBacthInfoByMarkIdCondition">
		<if test=" mark_id  != null "><![CDATA[ AND A.MARK_ID = #{mark_id} ]]></if>
	</sql>	
	<sql id="PAS_queryPolicyMarkingBacthInfoByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addPolicyMarkingBacthInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="bacth_id">
			SELECT APP___PAS__DBUSER.S_POLICY_MARKING_BACTH_INFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_MARKING_BACTH_INFO(
				MARK_ID, INTEREST_CAPITAL, BATCH_MONTH, INSERT_TIME, UPDATE_TIME, ACCOUNT_BALANCE, BUSI_PROD_CODE, 
				POLICY_TYPE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, BACTH_ID, CASH_VALUE, UPDATE_TIMESTAMP, 
				INSERT_BY, CALCU_DATE ) 
			VALUES (
				#{mark_id, jdbcType=NUMERIC}, #{interest_capital, jdbcType=NUMERIC} , #{batch_month, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{account_balance, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{policy_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{bacth_id, jdbcType=NUMERIC} , #{cash_value, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{calcu_date, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deletePolicyMarkingBacthInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_POLICY_MARKING_BACTH_INFO WHERE BACTH_ID = #{bacth_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updatePolicyMarkingBacthInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_POLICY_MARKING_BACTH_INFO ]]>
		<set>
		<trim suffixOverrides=",">
		    MARK_ID = #{mark_id, jdbcType=NUMERIC} ,
		    INTEREST_CAPITAL = #{interest_capital, jdbcType=NUMERIC} ,
		    BATCH_MONTH = #{batch_month, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ACCOUNT_BALANCE = #{account_balance, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    BACTH_ID = #{bacth_id, jdbcType=NUMERIC} ,
		    CASH_VALUE = #{cash_value, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CALCU_DATE = #{calcu_date, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE BACTH_ID = #{bacth_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findPolicyMarkingBacthInfoByBacthId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARK_ID, A.INTEREST_CAPITAL, A.BATCH_MONTH, A.ACCOUNT_BALANCE, A.BUSI_PROD_CODE, 
			A.POLICY_TYPE, A.POLICY_CODE, A.BACTH_ID, A.CASH_VALUE, 
			A.CALCU_DATE FROM T_POLICY_MARKING_BACTH_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingBacthInfoByBacthIdCondition" />
	</select>
	
	<select id="PAS_findPolicyMarkingBacthInfoByMarkId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARK_ID, A.INTEREST_CAPITAL, A.BATCH_MONTH, A.ACCOUNT_BALANCE, A.BUSI_PROD_CODE, 
			A.POLICY_TYPE, A.POLICY_CODE, A.BACTH_ID, A.CASH_VALUE, 
			A.CALCU_DATE FROM T_POLICY_MARKING_BACTH_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingBacthInfoByMarkIdCondition" />
	</select>
	
	<select id="PAS_findPolicyMarkingBacthInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARK_ID, A.INTEREST_CAPITAL, A.BATCH_MONTH, A.ACCOUNT_BALANCE, A.BUSI_PROD_CODE, 
			A.POLICY_TYPE, A.POLICY_CODE, A.BACTH_ID, A.CASH_VALUE, 
			A.CALCU_DATE FROM T_POLICY_MARKING_BACTH_INFO A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingBacthInfoByPolicyCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapPolicyMarkingBacthInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARK_ID, A.INTEREST_CAPITAL, A.BATCH_MONTH, A.ACCOUNT_BALANCE, A.BUSI_PROD_CODE, 
			A.POLICY_TYPE, A.POLICY_CODE, A.BACTH_ID, A.CASH_VALUE, 
			A.CALCU_DATE FROM T_POLICY_MARKING_BACTH_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllPolicyMarkingBacthInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARK_ID, A.INTEREST_CAPITAL, A.BATCH_MONTH, A.ACCOUNT_BALANCE, A.BUSI_PROD_CODE, 
			A.POLICY_TYPE, A.POLICY_CODE, A.BACTH_ID, A.CASH_VALUE, 
			A.CALCU_DATE FROM T_POLICY_MARKING_BACTH_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findPolicyMarkingBacthInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_POLICY_MARKING_BACTH_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryPolicyMarkingBacthInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MARK_ID, B.INTEREST_CAPITAL, B.BATCH_MONTH, B.ACCOUNT_BALANCE, B.BUSI_PROD_CODE, 
			B.POLICY_TYPE, B.POLICY_CODE, B.BACTH_ID, B.CASH_VALUE, 
			B.CALCU_DATE FROM (
					SELECT ROWNUM RN, A.MARK_ID, A.INTEREST_CAPITAL, A.BATCH_MONTH, A.ACCOUNT_BALANCE, A.BUSI_PROD_CODE, 
			A.POLICY_TYPE, A.POLICY_CODE, A.BACTH_ID, A.CASH_VALUE, 
			A.CALCU_DATE FROM T_POLICY_MARKING_BACTH_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
