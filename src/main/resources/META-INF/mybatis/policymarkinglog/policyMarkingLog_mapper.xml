<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.PolicyMarkingLogDaoImpl">

	<sql id="PAS_policyMarkingLogWhereCondition">
		<if test=" marking_time  != null  and  marking_time  != ''  "><![CDATA[ AND A.MARKING_TIME = #{marking_time} ]]></if>
		<if test=" effect_notice_info != null and effect_notice_info != ''  "><![CDATA[ AND A.EFFECT_NOTICE_INFO = #{effect_notice_info} ]]></if>
		<if test=" mark_id  != null "><![CDATA[ AND A.MARK_ID = #{mark_id} ]]></if>
		<if test=" user_id  != null "><![CDATA[ AND A.USER_ID = #{user_id} ]]></if>
		<if test=" sleep_type != null and sleep_type != ''  "><![CDATA[ AND A.SLEEP_TYPE = #{sleep_type} ]]></if>
		<if test=" deal_status != null and deal_status != ''  "><![CDATA[ AND A.DEAL_STATUS = #{deal_status} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" deal_scene != null and deal_scene != ''  "><![CDATA[ AND A.DEAL_SCENE = #{deal_scene} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME = #{deal_time} ]]></if>
		<if test=" platform != null and platform != ''  "><![CDATA[ AND A.PLATFORM = #{platform} ]]></if>
		<if test=" is_new_s_policy  != null "><![CDATA[ AND A.IS_NEW_S_POLICY = #{is_new_s_policy} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryPolicyMarkingLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PAS_queryPolicyMarkingLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PAS_queryPolicyMarkingLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addPolicyMarkingLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_POLICY_MARKING_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_MARKING_LOG(
				MARKING_TIME, EFFECT_NOTICE_INFO, MARK_ID, USER_ID, SLEEP_TYPE, INSERT_TIME, DEAL_STATUS, 
				UPDATE_TIME, POLICY_TYPE, INSERT_TIMESTAMP, LOG_ID, POLICY_CODE, DEAL_TIME, UPDATE_BY, 
				PLATFORM, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID ) 
			VALUES (
				#{marking_time, jdbcType=TIMESTAMP}, #{effect_notice_info, jdbcType=VARCHAR} , #{mark_id, jdbcType=NUMERIC} , #{user_id, jdbcType=NUMERIC} , #{sleep_type, jdbcType=VARCHAR} , SYSDATE , #{deal_status, jdbcType=VARCHAR} 
				, SYSDATE , #{policy_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{deal_time, jdbcType=TIMESTAMP} , #{update_by, jdbcType=NUMERIC} 
				, #{platform, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deletePolicyMarkingLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG WHERE LOG_ID = #{log_id, jdbcType=NUMERIC} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updatePolicyMarkingLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_MARKING_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    MARKING_TIME = #{marking_time, jdbcType=TIMESTAMP} ,
			EFFECT_NOTICE_INFO = #{effect_notice_info, jdbcType=VARCHAR} ,
		    MARK_ID = #{mark_id, jdbcType=NUMERIC} ,
		    USER_ID = #{user_id, jdbcType=NUMERIC} ,
			SLEEP_TYPE = #{sleep_type, jdbcType=VARCHAR} ,
			DEAL_STATUS = #{deal_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			DEAL_SCENE = #{deal_scene, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    DEAL_TIME = #{deal_time, jdbcType=TIMESTAMP} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			PLATFORM = #{platform, jdbcType=VARCHAR} ,
		    IS_NEW_S_POLICY = #{is_new_s_policy, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id, jdbcType=NUMERIC} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findPolicyMarkingLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.LOG_ID, A.DEAL_SCENE, A.POLICY_CODE, 
			A.DEAL_TIME, A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingLogByLogIdCondition" />
	</select>
	
	<select id="PAS_findPolicyMarkingLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.LOG_ID, A.DEAL_SCENE, A.POLICY_CODE, 
			A.DEAL_TIME, A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingLogByPolicyCodeCondition" />
	</select>
	
	<select id="PAS_findPolicyMarkingLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.LOG_ID, A.DEAL_SCENE, A.POLICY_CODE, 
			A.DEAL_TIME, A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_queryPolicyMarkingLogByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapPolicyMarkingLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.LOG_ID, A.DEAL_SCENE, A.POLICY_CODE, 
			A.DEAL_TIME, A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllPolicyMarkingLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.LOG_ID, A.DEAL_SCENE, A.POLICY_CODE, 
			A.DEAL_TIME, A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findPolicyMarkingLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryPolicyMarkingLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MARKING_TIME, B.EFFECT_NOTICE_INFO, B.MARK_ID, B.USER_ID, B.SLEEP_TYPE, B.DEAL_STATUS, 
			B.BUSI_PROD_CODE, B.POLICY_TYPE, B.LOG_ID, B.DEAL_SCENE, B.POLICY_CODE, 
			B.DEAL_TIME, B.PLATFORM, B.IS_NEW_S_POLICY, B.BUSI_ITEM_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.MARKING_TIME, A.EFFECT_NOTICE_INFO, A.MARK_ID, A.USER_ID, A.SLEEP_TYPE, A.DEAL_STATUS, 
			A.BUSI_PROD_CODE, A.POLICY_TYPE, A.LOG_ID, A.DEAL_SCENE, A.POLICY_CODE, 
			A.DEAL_TIME, A.PLATFORM, A.IS_NEW_S_POLICY, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_MARKING_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 添加操作 -->
	<insert id="PAS_addPolicyMarkingLog1"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_POLICY_MARKING_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_MARKING_LOG(
				MARKING_TIME, EFFECT_NOTICE_INFO, MARK_ID, USER_ID, SLEEP_TYPE, INSERT_TIME, DEAL_STATUS, 
				UPDATE_TIME, BUSI_PROD_CODE, POLICY_TYPE, INSERT_TIMESTAMP, LOG_ID, DEAL_SCENE, POLICY_CODE, 
				DEAL_TIME, UPDATE_BY, PLATFORM, IS_NEW_S_POLICY, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, 
				POLICY_ID ) 
			VALUES (
				#{marking_time, jdbcType=TIMESTAMP}, #{effect_notice_info, jdbcType=VARCHAR} , #{mark_id, jdbcType=NUMERIC} , #{user_id, jdbcType=NUMERIC} , #{sleep_type, jdbcType=VARCHAR} , SYSDATE , #{deal_status, jdbcType=VARCHAR} 
				, SYSDATE , #{busi_prod_code, jdbcType=VARCHAR} , #{policy_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id, jdbcType=NUMERIC} , #{deal_scene, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} 
				, #{deal_time, jdbcType=TIMESTAMP} , #{update_by, jdbcType=NUMERIC} , #{platform, jdbcType=VARCHAR} , #{is_new_s_policy, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
</mapper>
