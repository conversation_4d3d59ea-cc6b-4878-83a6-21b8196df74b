<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ISettleReportDao">

	<sql id="settleReportWhereCondition">
		<if test=" policy_change_type != null and policy_change_type != ''  "><![CDATA[ AND A.POLICY_CHANGE_TYPE = #{policy_change_type} ]]></if>
		<if test=" document_code != null and document_code != ''  "><![CDATA[ AND A.DOCUMENT_CODE = #{document_code} ]]></if>
		<if test=" busi_code != null and busi_code != ''  "><![CDATA[ AND A.BUSI_CODE = #{busi_code} ]]></if>
		<if test=" busi_time  != null  and  busi_time  != ''  "><![CDATA[ AND A.BUSI_TIME = #{busi_time} ]]></if>
		<if test=" batch_date  != null  and  batch_date  != ''  "><![CDATA[ AND A.BUSI_TIME < #{batch_date} ]]></if>
		<if test=" is_deal  != null "><![CDATA[ AND A.IS_DEAL = #{is_deal} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="querySettleReportByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="querySettleReportByBusiCodeCondition">
		<if test=" busi_code != null and busi_code != '' "><![CDATA[ AND A.BUSI_CODE = #{busi_code} ]]></if>
	</sql>	
	<sql id="querySettleReportByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSettleReport"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SETTLE_REPORT__LIST_ID .NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SETTLE_REPORT(
				POLICY_CHANGE_TYPE, DOCUMENT_CODE, BUSI_CODE, BUSI_TIME, IS_DEAL, CLOSING_ACCOUNT_VALUE , ROBUST_CLOSING_VALUE, AGGRESSIVE_CLOSING_VALUE,INSERT_TIME, UPDATE_TIME, 
				INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{policy_change_type, jdbcType=VARCHAR}, #{document_code, jdbcType=VARCHAR} , #{busi_code, jdbcType=VARCHAR} , #{busi_time, jdbcType=DATE} , #{is_deal, jdbcType=NUMERIC},#{closing_account_value, jdbcType=NUMERIC},#{robust_closing_value, jdbcType=NUMERIC} ,#{aggressive_closing_value, jdbcType=NUMERIC} , SYSDATE , SYSDATE 
				, CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSettleReport" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SETTLE_REPORT WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSettleReport" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SETTLE_REPORT ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_CHANGE_TYPE = #{policy_change_type, jdbcType=VARCHAR} ,
			DOCUMENT_CODE = #{document_code, jdbcType=VARCHAR} ,
			BUSI_CODE = #{busi_code, jdbcType=VARCHAR} ,
		    BUSI_TIME = #{busi_time, jdbcType=DATE} ,
		    IS_DEAL = #{is_deal, jdbcType=NUMERIC} ,
		    CLOSING_ACCOUNT_VALUE = #{closing_account_value, jdbcType=NUMERIC} ,
		    ROBUST_CLOSING_VALUE = #{robust_closing_value, jdbcType=NUMERIC} ,
		    AGGRESSIVE_CLOSING_VALUE = #{aggressive_closing_value, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSettleReportByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CHANGE_TYPE, A.DOCUMENT_CODE, A.BUSI_CODE, A.BUSI_TIME, A.IS_DEAL,A.CLOSING_ACCOUNT_VALUE,A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE,
			A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE 1 = 1  ]]>
		<include refid="querySettleReportByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID DESC]]>
	</select>
	
	<select id="PA_findSettleReportByBusiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CHANGE_TYPE, A.DOCUMENT_CODE, A.BUSI_CODE, A.BUSI_TIME, A.IS_DEAL,A.CLOSING_ACCOUNT_VALUE,A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE, 
			A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE 1 = 1  ]]>
		<include refid="querySettleReportByBusiCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="PA_findSettleReportByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CHANGE_TYPE, A.DOCUMENT_CODE, A.BUSI_CODE, A.BUSI_TIME, A.IS_DEAL,A.CLOSING_ACCOUNT_VALUE,A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE, 
			A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE 1 = 1  ]]>
		<include refid="querySettleReportByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID DESC]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSettleReport" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CHANGE_TYPE, A.DOCUMENT_CODE, A.BUSI_CODE, A.BUSI_TIME, A.IS_DEAL,A.CLOSING_ACCOUNT_VALUE, A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE,
			A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE ROWNUM <=  1000  ]]>
		<include refid="settleReportWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID DESC]]>
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSettleReport" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CHANGE_TYPE, A.DOCUMENT_CODE, A.BUSI_CODE, A.BUSI_TIME, A.IS_DEAL, A.CLOSING_ACCOUNT_VALUE,A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE,
			A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE ROWNUM <=  1000  ]]>
		<include refid="settleReportWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID DESC]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSettleReportTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE 1 = 1  ]]>
		<include refid="settleReportWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySettleReportForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_CHANGE_TYPE, B.DOCUMENT_CODE, B.BUSI_CODE, B.BUSI_TIME, B.IS_DEAL, A.CLOSING_ACCOUNT_VALUE,A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE,
			B.POLICY_CODE, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.POLICY_CHANGE_TYPE, A.DOCUMENT_CODE, A.BUSI_CODE, A.BUSI_TIME, A.IS_DEAL, A.CLOSING_ACCOUNT_VALUE,A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE,
			A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="settleReportWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 查询所有操作取模 -->
	<select id="PA_findAllSettleReportByStartAndModnum" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM, A.POLICY_CHANGE_TYPE, A.DOCUMENT_CODE, A.BUSI_CODE, A.BUSI_TIME, A.IS_DEAL, A.CLOSING_ACCOUNT_VALUE,A.ROBUST_CLOSING_VALUE,A.AGGRESSIVE_CLOSING_VALUE,
			A.POLICY_CODE, A.LIST_ID FROM APP___PAS__DBUSER.T_SETTLE_REPORT A WHERE 1=1 ]]>
		<include refid="settleReportWhereCondition" /> 
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.LIST_ID,#{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>	
</mapper>
