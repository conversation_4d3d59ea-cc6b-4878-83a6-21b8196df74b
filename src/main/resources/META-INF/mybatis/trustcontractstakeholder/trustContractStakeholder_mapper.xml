<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ITrustContractStakeholderDao">
	<sql id="CUS_trustContractStakeholderWhereCondition">
		<if test=" order_id != null and order_id != ''  "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" name != null and name != ''  "><![CDATA[ AND A.NAME = #{name} ]]></if>
		<if test=" country != null and country != ''  "><![CDATA[ AND A.COUNTRY = #{country} ]]></if>
		<if test=" address != null and address != ''  "><![CDATA[ AND A.ADDRESS = #{address} ]]></if>
		<if test=" cont_id  != null "><![CDATA[ AND A.CONT_ID = #{cont_id} ]]></if>
		<if test=" stakeholder_type  != null "><![CDATA[ AND A.STAKEHOLDER_TYPE = #{stakeholder_type} ]]></if>
		<if test=" job != null and job != ''  "><![CDATA[ AND A.JOB = #{job} ]]></if>
		<if test=" phone != null and phone != ''  "><![CDATA[ AND A.PHONE = #{phone} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" gender != null and gender != ''  "><![CDATA[ AND A.GENDER = #{gender} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" certi_date != null and certi_date != ''  "><![CDATA[ AND A.CERTI_DATE = #{certi_date} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryTrustContractStakeholderByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryTrustContractStakeholderByContIdCondition">
		<if test=" cont_id  != null "><![CDATA[ AND A.CONT_ID = #{cont_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addTrustContractStakeholder"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT DEV_PAS.S_TRUST_CONTRACT_STAKEHOLDER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER(
				ORDER_ID,NAME, COUNTRY, INSERT_TIME, ADDRESS, CONT_ID, STAKEHOLDER_TYPE, UPDATE_TIME, 
				JOB, PHONE, INSERT_TIMESTAMP, CERTI_CODE, UPDATE_BY, GENDER, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY, CERTI_TYPE, CERTI_DATE ) 
			VALUES (
				#{order_id, jdbcType=VARCHAR} , #{name, jdbcType=VARCHAR}, #{country, jdbcType=VARCHAR} , SYSDATE , #{address, jdbcType=VARCHAR} , #{cont_id, jdbcType=NUMERIC} , #{stakeholder_type, jdbcType=NUMERIC} , SYSDATE 
				, #{job, jdbcType=VARCHAR} , #{phone, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{certi_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{gender, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{certi_type, jdbcType=VARCHAR} , #{certi_date, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteTrustContractStakeholder" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateTrustContractStakeholder" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER ]]>
		<set>
		<trim suffixOverrides=",">
			ORDER_ID = #{order_id, jdbcType=VARCHAR} ,
			NAME = #{name, jdbcType=VARCHAR} ,
			COUNTRY = #{country, jdbcType=VARCHAR} ,
			ADDRESS = #{address, jdbcType=VARCHAR} ,
		    CONT_ID = #{cont_id, jdbcType=NUMERIC} ,
		    STAKEHOLDER_TYPE = #{stakeholder_type, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			JOB = #{job, jdbcType=VARCHAR} ,
			PHONE = #{phone, jdbcType=VARCHAR} ,
			CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			GENDER = #{gender, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CERTI_TYPE = #{certi_type, jdbcType=VARCHAR} ,
			CERTI_DATE = #{certi_date, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findTrustContractStakeholderByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID,A.NAME, A.COUNTRY, A.ADDRESS, A.CONT_ID, A.STAKEHOLDER_TYPE, 
			A.JOB, A.PHONE, A.CERTI_CODE, A.GENDER, A.LIST_ID, 
			A.CERTI_TYPE, A.CERTI_DATE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER A WHERE 1 = 1  ]]>
		<include refid="CUS_queryTrustContractStakeholderByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="CUS_findTrustContractStakeholderByContId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID,A.NAME, A.COUNTRY, A.ADDRESS, A.CONT_ID, A.STAKEHOLDER_TYPE, 
			A.JOB, A.PHONE, A.CERTI_CODE, A.GENDER, A.LIST_ID, 
			A.CERTI_TYPE, A.CERTI_DATE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER A WHERE 1 = 1  ]]>
		<include refid="CUS_queryTrustContractStakeholderByContIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapTrustContractStakeholder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID,A.NAME, A.COUNTRY, A.ADDRESS, A.CONT_ID, A.STAKEHOLDER_TYPE, 
			A.JOB, A.PHONE, A.CERTI_CODE, A.GENDER, A.LIST_ID, 
			A.CERTI_TYPE, A.CERTI_DATE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllTrustContractStakeholder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID,A.NAME, A.COUNTRY, A.ADDRESS, A.CONT_ID, A.STAKEHOLDER_TYPE, 
			A.JOB, A.PHONE, A.CERTI_CODE, A.GENDER, A.LIST_ID, 
			A.CERTI_TYPE, A.CERTI_DATE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findTrustContractStakeholderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryTrustContractStakeholderForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ORDER_ID,B.NAME, B.COUNTRY, B.ADDRESS, B.CONT_ID, B.STAKEHOLDER_TYPE, 
			B.JOB, B.PHONE, B.CERTI_CODE, B.GENDER, B.LIST_ID, 
			B.CERTI_TYPE, B.CERTI_DATE FROM (
					SELECT ROWNUM RN, A.ORDER_ID,A.NAME, A.COUNTRY, A.ADDRESS, A.CONT_ID, A.STAKEHOLDER_TYPE, 
			A.JOB, A.PHONE, A.CERTI_CODE, A.GENDER, A.LIST_ID, 
			A.CERTI_TYPE, A.CERTI_DATE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 查询信托轨迹最新的一条数据 -->	
	<select id="findTrustContractStakeholderonezx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM (
			SELECT A.ORDER_ID,A.TRUST_PROJECT,A.CONT_NO,A.OPER_TYPE,A.LIST_ID,A.INSERT_BY,A.INSERT_TIME FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A  WHERE  1=1]]>
			<if test=" cont_no != null and cont_no != ''  "><![CDATA[ AND   A.CONT_NO = #{cont_no} ]]></if>
			 <![CDATA[ORDER BY A.LIST_ID DESC ) WHERE ROWNUM = 1]]> 
	</select>
	
	<select id="selecttrustContractStakeholderzxupdate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.ORDER_ID,A.TRUST_PROJECT,A.CONT_NO,A.OPER_TYPE,A.LIST_ID,A.INSERT_BY,A.INSERT_TIME FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A  WHERE 1=1 
		]]>
		<if test=" cont_no != null and cont_no != ''  "><![CDATA[ AND  A.CONT_NO = #{cont_no}]]></if>
		<![CDATA[ORDER BY A.LIST_ID DESC]]> 
	</select>
	
	
	<select id="selecttrustContractStakeholderjysb" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.ORDER_ID,A.CONT_NO,A.TRUST_PROJECT,B.NAME,B.CERTI_CODE,B.COUNTRY,B.CERTI_TYPE,B.GENDER,B.JOB,B.PHONE,B.CERTI_DATE,B.ADDRESS FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A LEFT JOIN APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER B 
    ON A.LIST_ID=B.CONT_ID  WHERE  B.STAKEHOLDER_TYPE='1'
		]]>
		<if test=" cont_no != null and cont_no != ''  "><![CDATA[ AND A.CONT_NO = #{cont_no} ]]></if>
		<if test=" cont_id != null and cont_id != ''  "><![CDATA[ AND B.CONT_ID = #{cont_id} ]]></if>
		<if test=" name != null and name != ''  "><![CDATA[ AND B.NAME = #{name} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND B.CERTI_CODE = #{certi_code} ]]></if>
	</select>
	
	
	<select id="selecttrustContractStakeholderjysbsyr" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.ORDER_ID,A.CONT_NO,A.TRUST_PROJECT,B.NAME,B.CERTI_CODE,B.COUNTRY,B.CERTI_TYPE,B.GENDER,B.JOB,B.PHONE,B.CERTI_DATE,B.ADDRESS FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A LEFT JOIN APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER B 
    ON A.LIST_ID=B.CONT_ID WHERE   B.STAKEHOLDER_TYPE='2'
		]]>
		<if test=" cont_no != null and cont_no != ''  "><![CDATA[ AND A.CONT_NO = #{cont_no} ]]></if>
		<if test=" cont_id != null and cont_id != ''  "><![CDATA[ AND B.CONT_ID = #{cont_id} ]]></if>
	</select>
	
	
	
	<select id="selecttrustContractStakeholderjysbbggj" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT  A.ORDER_ID,A.CONT_NO,
         A.LIST_ID,
         A.TRUST_PROJECT,
         B.NAME,
         B.CERTI_CODE,
         B.COUNTRY,
         B.CERTI_TYPE,
         B.GENDER,
         B.JOB,
         B.PHONE,
         B.CERTI_DATE,
         B.ADDRESS,
         A.INSERT_BY,
         A.INSERT_TIME
    FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A
    INNER JOIN APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER B
      ON A.LIST_ID = B.CONT_ID
      WHERE A.LIST_ID IN (
        SELECT   MAX(A.LIST_ID)   
    FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A
    INNER JOIN APP___PAS__DBUSER.T_TRUST_CONTRACT_STAKEHOLDER B
      ON A.LIST_ID = B.CONT_ID
      WHERE 1=1]]>
			<if test=" cont_no != null and cont_no != ''  "><![CDATA[ AND   A.CONT_NO = #{cont_no} ]]></if>
			<if test=" name != null and name != ''  "><![CDATA[ AND B.NAME = #{name} ]]></if>
			<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND B.CERTI_CODE = #{certi_code} ]]></if>
			 <![CDATA[GROUP BY A.CONT_NO)]]> 
			 <if test=" cont_no != null and cont_no != ''  "><![CDATA[ AND   A.CONT_NO = #{cont_no} ]]></if>
			 <if test=" name != null and name != ''  "><![CDATA[ AND B.NAME = #{name} ]]></if>
			 <if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND B.CERTI_CODE = #{certi_code} ]]></if>
			 <![CDATA[AND B.STAKEHOLDER_TYPE='1'  ORDER BY B.CONT_ID DESC]]> 
	</select>
	
	
	<!-- 添加操作 -->
	<insert id="addTrustContract"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT DEV_PAS.S_TRUST_CONTRACT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_TRUST_CONTRACT(
				A.ORDER_ID,INSERT_TIMESTAMP, TRUST_PROJECT, UPDATE_BY, CONT_NO, INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, OPER_TYPE,ORDER_ID ) 
			VALUES (
				#{order_id, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{trust_project, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{cont_no, jdbcType=VARCHAR} , SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{oper_type, jdbcType=NUMERIC},#{order_id, jdbcType=VARCHAR}  ) 
		 ]]>
	</insert>
	
	
	<!-- 添加操作 -->
	<insert id="addTrustContractStakeholders"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT DEV_PAS.S_TRUST_CONTRACT_STAKEHOLDER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_TRUST_CONTRACT_STAKEHOLDER(
				A.ORDER_ID,NAME, COUNTRY, INSERT_TIME, ADDRESS, CONT_ID, STAKEHOLDER_TYPE, UPDATE_TIME, 
				JOB, PHONE, INSERT_TIMESTAMP, CERTI_CODE, UPDATE_BY, GENDER, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY, CERTI_TYPE, CERTI_DATE ) 
			VALUES (
				#{order_id, jdbcType=VARCHAR} ,#{name, jdbcType=VARCHAR}, #{country, jdbcType=VARCHAR} , SYSDATE , #{address, jdbcType=VARCHAR} , #{cont_id, jdbcType=NUMERIC} , #{stakeholder_type, jdbcType=NUMERIC} , SYSDATE 
				, #{job, jdbcType=VARCHAR} , #{phone, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{certi_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{gender, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{certi_type, jdbcType=VARCHAR} , #{certi_date, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>
</mapper>
