<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsFeeFrozenLogDao">

	<sql id="CUS_csFeeFrozenLogWhereCondition">
		<if test=" frozen_oper_time  != null  and  frozen_oper_time  != ''  "><![CDATA[ AND A.FROZEN_OPER_TIME = #{frozen_oper_time} ]]></if>
		<if test=" frozen_situation != null and frozen_situation != ''  "><![CDATA[ AND A.FROZEN_SITUATION = #{frozen_situation} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" list_id != null and list_id != ''  "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" frozen_oper_type  != null "><![CDATA[ AND A.FROZEN_OPER_TYPE = #{frozen_oper_type} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsFeeFrozenLogByListIdCondition">
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsFeeFrozenLogByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>	
	<sql id="CUS_queryCsFeeFrozenLogByUnitNumberCondition">
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsFeeFrozenLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_FEE_FROZ_LOG__ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG(
				INSERT_TIMESTAMP, FROZEN_OPER_TIME, UPDATE_BY, FROZEN_SITUATION, INSERT_TIME, UNIT_NUMBER, LIST_ID, 
				FROZEN_OPER_TYPE, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, ACCEPT_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{frozen_oper_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{frozen_situation, jdbcType=VARCHAR} , SYSDATE , #{unit_number, jdbcType=VARCHAR} , #{list_id, jdbcType=VARCHAR} 
				, #{frozen_oper_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{accept_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsFeeFrozenLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG WHERE  = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsFeeFrozenLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG ]]>
		<set>
		<trim suffixOverrides=",">
		    FROZEN_OPER_TIME = #{frozen_oper_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			FROZEN_SITUATION = #{frozen_situation, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
			LIST_ID = #{list_id, jdbcType=VARCHAR} ,
		    FROZEN_OPER_TYPE = #{frozen_oper_type, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID= #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsFeeFrozenLogByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FROZEN_OPER_TIME, A.FROZEN_SITUATION, A.UNIT_NUMBER, A.LIST_ID, 
			A.FROZEN_OPER_TYPE, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsFeeFrozenLogByListIdCondition" />
	</select>
	
	<select id="CUS_findCsFeeFrozenLogByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FROZEN_OPER_TIME, A.FROZEN_SITUATION, A.UNIT_NUMBER, A.LIST_ID, 
			A.FROZEN_OPER_TYPE, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsFeeFrozenLogByAcceptCodeCondition" />
	</select>
	
	<select id="CUS_findCsFeeFrozenLogByUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FROZEN_OPER_TIME, A.FROZEN_SITUATION, A.UNIT_NUMBER, A.LIST_ID, 
			A.FROZEN_OPER_TYPE, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsFeeFrozenLogByUnitNumberCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsFeeFrozenLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FROZEN_OPER_TIME, A.FROZEN_SITUATION, A.UNIT_NUMBER, A.LIST_ID, 
			A.FROZEN_OPER_TYPE, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsFeeFrozenLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FROZEN_OPER_TIME, A.FROZEN_SITUATION, A.UNIT_NUMBER, A.LIST_ID, 
			A.FROZEN_OPER_TYPE, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csFeeFrozenLogWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsFeeFrozenLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_csFeeFrozenLogWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsFeeFrozenLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.FROZEN_OPER_TIME, B.FROZEN_SITUATION, B.UNIT_NUMBER, B.LIST_ID, 
			B.FROZEN_OPER_TYPE, B.ACCEPT_CODE FROM (
					SELECT ROWNUM RN, A.FROZEN_OPER_TIME, A.FROZEN_SITUATION, A.UNIT_NUMBER, A.LIST_ID, 
			A.FROZEN_OPER_TYPE, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_FEE_FROZEN_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
