<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ITaxPremiumPolicyTaskDao">
 
	<sql id="PAS_taxPremiumPolicyTaskWhereCondition">
		<if test=" fee_sequence_no != null and fee_sequence_no != ''  "><![CDATA[ AND A.FEE_SEQUENCE_NO = #{fee_sequence_no} ]]></if>
		<if test=" result_code != null and result_code != ''  "><![CDATA[ AND A.RESULT_CODE = #{result_code} ]]></if>
		<if test=" unit_numer != null and unit_numer != ''  "><![CDATA[ AND A.UNIT_NUMER = #{unit_numer} ]]></if>
		<if test=" total_policy_sequence_no != null and total_policy_sequence_no != ''  "><![CDATA[ AND A.TOTAL_POLICY_SEQUENCE_NO = #{total_policy_sequence_no} ]]></if>
		<if test=" result_message != null and result_message != ''  "><![CDATA[ AND A.RESULT_MESSAGE = #{result_message} ]]></if>
		<if test=" send_type != null and send_type != ''  "><![CDATA[ AND A.SEND_TYPE = #{send_type} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" batach_date  != null  and  batach_date  != ''  "><![CDATA[ AND A.BATACH_DATE = #{batach_date} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" renewal_sequence_no != null and renewal_sequence_no != ''  "><![CDATA[ AND A.RENEWAL_SEQUENCE_NO = #{renewal_sequence_no} ]]></if>
		<if test=" booking_sequence_no != null and booking_sequence_no != ''  "><![CDATA[ AND A.BOOKING_SEQUENCE_NO = #{booking_sequence_no} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" task_status != null and task_status != ''  "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" renewal_endorsement_no != null and renewal_endorsement_no != ''  "><![CDATA[ AND A.RENEWAL_ENDORSEMENT_NO = #{renewal_endorsement_no} ]]></if>
		<if test=" tax_code != null and tax_code != ''  "><![CDATA[ AND A.TAX_CODE = #{tax_code} ]]></if>
	</sql>
 

	<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryTaxPremiumPolicyTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPremiumPolicyTaskByBatachDateCondition">
		<if test=" batach_date  != null "><![CDATA[ AND A.BATACH_DATE = #{batach_date} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPremiumPolicyTaskByDueTimeCondition">
		<if test=" due_time  != null "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPremiumPolicyTaskByFeeSequenceNoCondition">
		<if test=" fee_sequence_no != null and fee_sequence_no != '' "><![CDATA[ AND A.FEE_SEQUENCE_NO = #{fee_sequence_no} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPremiumPolicyTaskByFinishTimeCondition">
		<if test=" finish_time  != null "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPremiumPolicyTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPremiumPolicyTaskByTaskStatusCondition">
		<if test=" task_status != null and task_status != '' "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPremiumPolicyTaskByUnitNumerCondition">
		<if test=" unit_numer != null and unit_numer != '' "><![CDATA[ AND A.UNIT_NUMER = #{unit_numer} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="PAS_addTaxPremiumPolicyTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TAX_PREMIUM_POLICY_TASK.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK(
				FEE_SEQUENCE_NO, RESULT_CODE, UNIT_NUMER, INSERT_TIME, TOTAL_POLICY_SEQUENCE_NO, RESULT_MESSAGE, 
				SEND_TYPE, UPDATE_TIME, FINISH_TIME, BATACH_DATE, APPLY_CODE, INSERT_TIMESTAMP, RENEWAL_SEQUENCE_NO, 
				BOOKING_SEQUENCE_NO, DUE_TIME, POLICY_CODE, TASK_STATUS, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, 
				INSERT_BY, RENEWAL_ENDORSEMENT_NO, TAX_CODE ) 
			VALUES (
				#{fee_sequence_no, jdbcType=VARCHAR}, #{result_code, jdbcType=VARCHAR} , #{unit_numer, jdbcType=VARCHAR} , SYSDATE , #{total_policy_sequence_no, jdbcType=VARCHAR} , #{result_message, jdbcType=VARCHAR} 
				, #{send_type, jdbcType=VARCHAR} , SYSDATE , #{finish_time, jdbcType=DATE} , #{batach_date, jdbcType=DATE} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{renewal_sequence_no, jdbcType=VARCHAR} 
				, #{booking_sequence_no, jdbcType=VARCHAR} , #{due_time, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{task_status, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{renewal_endorsement_no, jdbcType=VARCHAR} , #{tax_code, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="PAS_deleteTaxPremiumPolicyTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="PAS_updateTaxPremiumPolicyTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			FEE_SEQUENCE_NO = #{fee_sequence_no, jdbcType=VARCHAR} ,
			RESULT_CODE = #{result_code, jdbcType=VARCHAR} ,
			UNIT_NUMER = #{unit_numer, jdbcType=VARCHAR} ,
			TOTAL_POLICY_SEQUENCE_NO = #{total_policy_sequence_no, jdbcType=VARCHAR} ,
			RESULT_MESSAGE = #{result_message, jdbcType=VARCHAR} ,
			SEND_TYPE = #{send_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
		    BATACH_DATE = #{batach_date, jdbcType=DATE} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			RENEWAL_SEQUENCE_NO = #{renewal_sequence_no, jdbcType=VARCHAR} ,
			BOOKING_SEQUENCE_NO = #{booking_sequence_no, jdbcType=VARCHAR} ,
		    DUE_TIME = #{due_time, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			TASK_STATUS = #{task_status, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    RENEWAL_ENDORSEMENT_NO = #{renewal_endorsement_no, jdbcType=VARCHAR} ,
		    TAX_CODE = #{tax_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="PAS_findTaxPremiumPolicyTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByListIdCondition" />
	</select>
	
	<select id="PAS_findTaxPremiumPolicyTaskByBatachDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByBatachDateCondition" />
	</select>
	
	<select id="PAS_findTaxPremiumPolicyTaskByDueTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByDueTimeCondition" />
	</select>
	
	<select id="PAS_findTaxPremiumPolicyTaskByFeeSequenceNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByFeeSequenceNoCondition" />
	</select>
	
	<select id="PAS_findTaxPremiumPolicyTaskByFinishTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByFinishTimeCondition" />
	</select>
	
	<select id="PAS_findTaxPremiumPolicyTaskByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByPolicyCodeCondition" />
	</select>
	
	<select id="PAS_findTaxPremiumPolicyTaskByTaskStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByTaskStatusCondition" />
	</select>
	
	<select id="PAS_findTaxPremiumPolicyTaskByUnitNumer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPremiumPolicyTaskByUnitNumerCondition" />
	</select>
	

	<!-- 按map查询操作 -->
	<select id="PAS_findAllMapTaxPremiumPolicyTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_taxPremiumPolicyTaskWhereCondition" />
	</select>

	<!-- 查询单条操作 -->
	<select id="PAS_findTaxPremiumPolicyTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_taxPremiumPolicyTaskWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="PAS_findAllTaxPremiumPolicyTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_taxPremiumPolicyTaskWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="PAS_findTaxPremiumPolicyTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_taxPremiumPolicyTaskWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="PAS_queryTaxPremiumPolicyTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.FEE_SEQUENCE_NO, B.RESULT_CODE, B.UNIT_NUMER, B.TOTAL_POLICY_SEQUENCE_NO, B.RESULT_MESSAGE, 
			B.SEND_TYPE, B.FINISH_TIME, B.BATACH_DATE, B.APPLY_CODE, B.RENEWAL_SEQUENCE_NO, 
			B.BOOKING_SEQUENCE_NO, B.DUE_TIME, B.POLICY_CODE, B.TASK_STATUS, B.LIST_ID,B.RENEWAL_ENDORSEMENT_NO,B.TAX_CODE FROM (
					SELECT ROWNUM RN, A.FEE_SEQUENCE_NO, A.RESULT_CODE, A.UNIT_NUMER, A.TOTAL_POLICY_SEQUENCE_NO, A.RESULT_MESSAGE, 
			A.SEND_TYPE, A.FINISH_TIME, A.BATACH_DATE, A.APPLY_CODE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.DUE_TIME, A.POLICY_CODE, A.TASK_STATUS, A.LIST_ID,A.RENEWAL_ENDORSEMENT_NO,A.TAX_CODE FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PAS_taxPremiumPolicyTaskWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询税优保单续期报送保单信息 -->
	<select id="PAS_taxPremiumPolicyTaskQueryData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
				 SELECT T.*, ROWNUM RN FROM (
					SELECT A.FEE_SEQUENCE_NO,
					       A.RESULT_CODE,
					       A.UNIT_NUMER,
					       A.TOTAL_POLICY_SEQUENCE_NO,
					       A.RESULT_MESSAGE,
					       A.SEND_TYPE,
					       A.FINISH_TIME,
					       A.BATACH_DATE,
					       A.APPLY_CODE,
					       A.RENEWAL_SEQUENCE_NO,
					       A.BOOKING_SEQUENCE_NO,
					       A.DUE_TIME,
					       A.POLICY_CODE,
					       A.TASK_STATUS,
					       A.LIST_ID
					  FROM APP___PAS__DBUSER.T_TAX_PREMIUM_POLICY_TASK A
					 WHERE A.TASK_STATUS IN ('1', '3')
					   AND A.SEND_TYPE IN ('1', '2', '4', '5', '6')
					  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME <= #{due_time} ]]></if>
		<![CDATA[ ) T WHERE 1=1 ]]>			 
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.LIST_ID , #{modnum}) = #{start} ]]></if>			 
	</select>
	
	<!-- 根据保单号查询保单基本信息及保单续期保费信息 -->
	<select id="PAS_quryPolicyBaseInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[     SELECT DISTINCT TCM.POLICY_CODE,
			                 TCM.TOTAL_POLICY_SEQUENCE_NO,
			                 TCM.POLICY_SEQUENCE_NO,
			                 TCM.APPLY_CODE,
			                 TCM.LIABILITY_STATE,
			                 TCM.LAPSE_DATE AS SUSPEND_DATE,
			                 TP.POLICY_YEAR,
			                 TTPP.UNIT_NUMER,
			                 TTPP.DUE_TIME,
			                 TTPP.FEE_SEQUENCE_NO,
			                 TTPP.SEND_TYPE 
			            FROM DEV_PAS.T_CONTRACT_MASTER         TCM,
			                 DEV_PAS.T_PREM                    TP,
			                 DEV_PAS.T_CONTRACT_BUSI_PROD      TCBP,
			                 DEV_PAS.T_CONTRACT_PRODUCT        TCP,
			                 DEV_PAS.T_TAX_PREMIUM_POLICY_TASK TTPP
			           WHERE TCM.POLICY_CODE = TTPP.POLICY_CODE
			             AND TCM.POLICY_CODE = TCM.POLICY_CODE
			             AND TTPP.UNIT_NUMER = TP.UNIT_NUMBER
			             AND TTPP.DUE_TIME = TP.DUE_TIME 
			             AND TCM.POLICY_CODE =  TCBP.POLICY_CODE 
			             AND TCBP.POLICY_CODE = TCP.POLICY_CODE 
			             AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID 
			             AND TCP.ITEM_ID = TP.ITEM_ID 
					     AND TP.POLICY_CODE = #{policy_code} 
					     AND TP.UNIT_NUMBER = #{unit_number} 
					     AND TP.DUE_TIME = #{due_time}  
					     AND TTPP.SEND_TYPE = #{send_type} 
					      ]]>
	</select>
	
	<!-- 根据保单号查询险种产品信息 -->
	<select id="PAS_quryCoverageList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT TPA.POLICY_CODE,
					       TBI.INSURED_ID,
					       TIL.CUSTOMER_ID,
					       TPA.BUSI_ITEM_ID,
					       TPA.UNIT_NUMBER,
					       TPA.FEE_AMOUNT,
					       TPA.BUSINESS_TYPE,
					       TPA.PAY_MODE,
		                   TPA.PREM_FREQ,
		                   TPA.DUE_TIME,
		                   TPA.FINISH_TIME,
		                   TPA.BUSI_PROD_CODE,
		                   TCBP.VALIDATE_DATE,
                 		   TCBP.MATURITY_DATE,
                 		   TCP.AMOUNT,
                 		   TPA.APPLY_CODE,
                 		   TCBP.MASTER_BUSI_ITEM_ID
					  FROM DEV_PAS.T_BENEFIT_INSURED    TBI,
					       DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
					       DEV_PAS.T_CONTRACT_PRODUCT   TCP,
					       DEV_PAS.T_INSURED_LIST       TIL,
					       DEV_PAS.T_PREM_ARAP          TPA
					 WHERE TBI.POLICY_CODE = TCBP.POLICY_CODE
					   AND TBI.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
					   AND TBI.POLICY_CODE = TIL.POLICY_CODE
					   AND TBI.INSURED_ID = TIL.LIST_ID
					   AND TBI.POLICY_CODE = TPA.POLICY_CODE
					   AND TBI.BUSI_ITEM_ID = TPA.BUSI_ITEM_ID
					   AND TCBP.POLICY_CODE = TCP.POLICY_CODE
            		   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
					   AND TPA.POLICY_CODE = #{policy_code} 
					   AND TPA.UNIT_NUMBER = #{unit_number} 
					   AND TIL.CUSTOMER_ID = #{customer_id}   ]]>
	</select>

	<!-- 查询保单当期的应收应付类型为应收且费用状态不为02的应收应付数据 -->
	<select id="PAS_queryPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[       SELECT DISTINCT A.POLICY_CODE,
						                A.APPLY_CODE,
						                B.TOTAL_POLICY_SEQUENCE_NO,
						                A.UNIT_NUMBER,
						                A.DUE_TIME 
						  FROM DEV_PAS.T_PREM_ARAP A, DEV_PAS.T_CONTRACT_MASTER B
						 WHERE A.POLICY_CODE = B.POLICY_CODE
						   AND A.ARAP_FLAG = '1'
						   AND A.FEE_STATUS != '02'
						   AND B.SPECIAL_ACCOUNT_FLAG = '2'
						   AND A.POLICY_CODE = #{policy_code} 
						   AND A.DUE_TIME = #{due_time}  ]]>
	</select>

	<!-- 是否有有效的报送类型为“应收”的数据 -->
	<select id="PAS_findValidPolicyTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[       SELECT A.POLICY_CODE ,A.UNIT_NUMER
						  FROM DEV_PAS.T_TAX_PREMIUM_POLICY_TASK A
						 WHERE A.SEND_TYPE = '1'
						   AND A.TASK_STATUS IN ('1', '3', '4', '5')
						   AND A.POLICY_CODE = #{policy_code} 
						   AND A.UNIT_NUMER =  #{unit_numer}   ]]>
	</select>

	<!-- 查询保单主险是否为长期险 -->
	<select id="PAS_findLongEffectContractBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      			SELECT A.POLICY_CODE, A.POLICY_ID, A.BUSI_ITEM_ID 
								  FROM DEV_PAS.T_CONTRACT_BUSI_PROD A
								 WHERE 1 = 1
								   AND A.MASTER_BUSI_ITEM_ID IS NULL
								   AND A.POLICY_CODE = #{policy_code} 
								   AND EXISTS (SELECT 1
								          FROM DEV_PDS.T_BUSINESS_PRODUCT B
								         WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
								           AND B.COVER_PERIOD_TYPE = 0) ]]>
	</select>
	
	<!-- 查询保单主险是否为短期险 -->
	<select id="PAS_findShortEffectContractBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      			SELECT A.POLICY_CODE, A.POLICY_ID, A.BUSI_ITEM_ID 
								  FROM DEV_PAS.T_CONTRACT_BUSI_PROD A
								 WHERE 1 = 1
								   AND A.MASTER_BUSI_ITEM_ID IS NULL
								   AND A.POLICY_CODE = #{policy_code} 
								   AND EXISTS (SELECT 1
								          FROM DEV_PDS.T_BUSINESS_PRODUCT B
								         WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
								           AND B.COVER_PERIOD_TYPE != 0) ]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 税优续保查询保单信息 -->
	<select id="PAS_quryPolicyInfoForRNW011" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[     SELECT TCM.POLICY_CODE,
			                 TCM.VALIDATE_DATE,
			                 TCBP.MATURITY_DATE,
			                 TCM.TOTAL_POLICY_SEQUENCE_NO,
			                 TCM.POLICY_SEQUENCE_NO,
			                 TTPP.UNIT_NUMER,
			                 TP.POLICY_YEAR,
			                 (SELECT SUM(A.FEE_AMOUNT)
			                    FROM DEV_PAS.T_PREM A
			                   WHERE A.POLICY_CODE = TCM.POLICY_CODE
			                     AND A.FEE_SCENE_CODE = 'NB') AS FIRST_FEE_AMOUNT,
			                 SUM(TP.FEE_AMOUNT) AS FEE_AMOUNT
			            FROM DEV_PAS.T_CONTRACT_MASTER         TCM,
			                 DEV_PAS.T_CONTRACT_BUSI_PROD      TCBP,
			                 DEV_PAS.T_CONTRACT_PRODUCT        TCP,
			                 DEV_PAS.T_TAX_PREMIUM_POLICY_TASK TTPP,
			                 DEV_PAS.T_PREM                    TP
			           WHERE 1 = 1
			             AND TCM.POLICY_CODE = TCBP.POLICY_CODE
			             AND TCBP.POLICY_CODE = TCP.POLICY_CODE
			             AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
			             AND TCM.POLICY_CODE = TTPP.POLICY_CODE
			             AND TTPP.POLICY_CODE = TP.POLICY_CODE
			             AND TTPP.UNIT_NUMER = TP.UNIT_NUMBER
			             AND TCP.ITEM_ID = TP.ITEM_ID
						 AND TTPP.POLICY_CODE = #{policy_code} 
						 AND TP.UNIT_NUMBER = #{unit_number} 
						 AND TTPP.SEND_TYPE = #{send_type} 
						 GROUP BY   TCM.POLICY_CODE,
				                    TCM.VALIDATE_DATE,
				                    TCBP.MATURITY_DATE,
				                    TCM.TOTAL_POLICY_SEQUENCE_NO,
				                    TCM.POLICY_SEQUENCE_NO,
				                    TTPP.UNIT_NUMER,
				                    TP.POLICY_YEAR
 		]]>
	</select>
	
	<!-- 税优续保查询保单信息EN -->
	<select id="PAS_quryPolicyInfoForRNW011EN" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
		   	    SELECT TCM.POLICY_CODE,
                       TCM.VALIDATE_DATE,
                       TCBP.MATURITY_DATE,
                       TCBP.BUSI_PROD_CODE,
                       TCBP.BUSI_ITEM_ID,
                       TCBP.APPLY_CODE,
                       TCP.PREM_FREQ,
                       TCM.TOTAL_POLICY_SEQUENCE_NO,
                       TCM.POLICY_SEQUENCE_NO,
                       (SELECT SUM(A.FEE_AMOUNT)
                          FROM DEV_PAS.T_PREM A
                         WHERE A.POLICY_CODE = TCM.POLICY_CODE
                           AND A.FEE_SCENE_CODE = 'NB') AS FIRST_FEE_AMOUNT ,
                       TCP.AMOUNT 
                  FROM DEV_PAS.T_CONTRACT_MASTER         TCM,
                       DEV_PAS.T_CONTRACT_BUSI_PROD      TCBP,
                       DEV_PAS.T_CONTRACT_PRODUCT        TCP
                 WHERE 1 = 1
                   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
                   AND TCBP.POLICY_CODE = TCP.POLICY_CODE
                   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
                   AND TCBP.RENEW = '1' 
                   AND TCBP.RENEW_DECISION = '1'
                   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
                   AND TCM.POLICY_CODE = #{policy_code} 
                   AND ROWNUM = 1 
 		]]>
	</select>
	
</mapper>
