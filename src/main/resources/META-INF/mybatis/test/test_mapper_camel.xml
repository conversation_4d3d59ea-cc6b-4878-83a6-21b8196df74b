<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.nb.mapper.Test">
	
	<sql id="testWhereCondition">
	</sql>
	
	<insert id="addTest"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TEST(
		]]>
		<trim suffixOverrides=",">
		</trim>
		
		) VALUES (
		<trim suffixOverrides=",">
		</trim>
		<![CDATA[
		)
		]]>
	</insert>
	
	<delete id="deleteTest" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TEST A WHERE 1 = 1 ]]>
		AND A.TEST_ID = ${test_id}
	</delete>
	
	<update id="updateTest" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TEST A ]]>
		<set>
		<trim suffixOverrides=",">
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
		AND A.TEST_ID = ${test_id}
	</update>
	
	<select id="findTest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ${javabean.getPropertiesString()} FROM APP___PAS__DBUSER.T_TEST A WHERE 1 = 1  ]]>
		<include refid="testWhereCondition" />
	</select>
	
	<select id="findAllMapTest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ${javabean.getPropertiesString()} FROM APP___PAS__DBUSER.T_TEST A WHERE ROWNUM <=  1000  ]]>
		<include refid="testWhereCondition" />
	</select>
	
	<select id="findAllTest" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ${javabean.getPropertiesString()} FROM APP___PAS__DBUSER.T_TEST A WHERE ROWNUM <=  1000  ]]>
		<include refid="testWhereCondition" />
	</select>
	
	<select id="findTestTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT  COUNT(1) FROM APP___PAS__DBUSER.T_TEST A WHERE 1 = 1  ]]>
		<include refid="testWhereCondition" />
	</select>
	
	<select id="queryTestForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, ${javabean.getPropertiesStringForPage()} FROM (
					SELECT ROWNUM RN, A.* FROM APP___PAS__DBUSER.T_TEST A WHERE ROWNUM <= ${LESS_NUM} ]]>
		<include refid="testWhereCondition" />
		<![CDATA[ )  B
           WHERE B.RN > ${GREATER_NUM} ]]>
	</select>
	
</mapper>
