<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IEnNextDateCfgDao">
	<sql id="PA_enNextDateCfgWhereCondition">
		<if test=" next_prod_code != null and next_prod_code != ''  "><![CDATA[ AND A.NEXT_PROD_CODE = #{next_prod_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryEnNextDateCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 按索引查询操作 -->	
	<select id="PA_findEnNextDateCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_PROD_CODE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_NEXT_DATE_CFG A WHERE 1 = 1  ]]>
		<include refid="PA_queryEnNextDateCfgByListIdCondition" />
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="PA_findEnNextDateCfgByBusipc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_PROD_CODE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_NEXT_DATE_CFG A WHERE 1 = 1  ]]>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<include refid="PA_enNextDateCfgWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapEnNextDateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_PROD_CODE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_NEXT_DATE_CFG A WHERE 1 = 1]]>
		<include refid="PA_enNextDateCfgWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllEnNextDateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_PROD_CODE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_NEXT_DATE_CFG A WHERE 1 = 1 ]]>
		<include refid="PA_enNextDateCfgWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findEnNextDateCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_EN_NEXT_DATE_CFG A WHERE 1 = 1  ]]>
		 <include refid="PA_enNextDateCfgWhereCondition" /> 
	</select>
</mapper>
