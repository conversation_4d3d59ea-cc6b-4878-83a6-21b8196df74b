<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.INoticeMsgTaskDao">
<!--
	<sql id="PAS_noticeMsgTaskWhereCondition">
		<if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" push_status  != null "><![CDATA[ AND A.PUSH_STATUS = #{push_status} ]]></if>
		<if test=" business_number != null and business_number != ''  "><![CDATA[ AND A.BUSINESS_NUMBER = #{business_number} ]]></if>
		<if test=" push_type  != null "><![CDATA[ AND A.PUSH_TYPE = #{push_type} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" business_time  != null  and  business_time  != ''  "><![CDATA[ AND A.BUSINESS_TIME = #{business_time} ]]></if>
		<if test=" push_date  != null  and  push_date  != ''  "><![CDATA[ AND A.PUSH_DATE = #{push_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryNoticeMsgTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PAS_queryNoticeMsgTaskByPushDateCondition">
		<if test=" push_date  != null "><![CDATA[ AND A.PUSH_DATE = #{push_date} ]]></if>
	</sql>	
	<sql id="PAS_queryNoticeMsgTaskByBusinessNumberCondition">
		<if test=" business_number != null and business_number != '' "><![CDATA[ AND A.BUSINESS_NUMBER = #{business_number} ]]></if>
	</sql>	
	<sql id="PAS_queryNoticeMsgTaskByBusinessTimeCondition">
		<if test=" business_time  != null "><![CDATA[ AND A.BUSINESS_TIME = #{business_time} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addNoticeMsgTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_NOTICE_MSG_TASK.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_NOTICE_MSG_TASK(
				TEMPLATE_CODE, INSERT_TIME, UPDATE_TIME, PUSH_STATUS, BUSINESS_NUMBER, PUSH_TYPE, INSERT_TIMESTAMP, 
				POLICY_CODE, BUSINESS_TIME, UPDATE_BY, PUSH_DATE, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{template_code, jdbcType=VARCHAR}, SYSDATE , SYSDATE , #{push_status, jdbcType=NUMERIC} , #{business_number, jdbcType=VARCHAR} , #{push_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{policy_code, jdbcType=VARCHAR} , #{business_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{push_date, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deleteNoticeMsgTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updateNoticeMsgTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_NOTICE_MSG_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
		    PUSH_STATUS = 1 ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findNoticeMsgTaskByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryNoticeMsgTaskByPolicyCodeCondition" />
	</select>
	
	<select id="PAS_findNoticeMsgTaskByPushDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryNoticeMsgTaskByPushDateCondition" />
	</select>
	
	<select id="PAS_findNoticeMsgTaskByBusinessNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryNoticeMsgTaskByBusinessNumberCondition" />
	</select>
	
	<select id="PAS_findNoticeMsgTaskByBusinessTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE 1 = 1  ]]>
		<include refid="PAS_queryNoticeMsgTaskByBusinessTimeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapNoticeMsgTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllNoticeMsgTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findNoticeMsgTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryNoticeMsgTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TEMPLATE_CODE, B.PUSH_STATUS, B.BUSINESS_NUMBER, B.PUSH_TYPE, 
			B.POLICY_CODE, B.BUSINESS_TIME, B.PUSH_DATE, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 按索引查询操作 -->	
	<select id="PAS_findNoticeMsgTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TEMPLATE_CODE, A.PUSH_STATUS, A.BUSINESS_NUMBER, A.PUSH_TYPE, 
			A.POLICY_CODE, A.BUSINESS_TIME, A.PUSH_DATE, A.LIST_ID FROM APP___PAS__DBUSER.T_NOTICE_MSG_TASK A WHERE 1 = 1 AND ROWNUM = 1  ]]>
		<if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" push_status  != null "><![CDATA[ AND A.PUSH_STATUS = #{push_status} ]]></if>
		<if test=" business_number != null and business_number != ''  "><![CDATA[ AND A.BUSINESS_NUMBER = #{business_number} ]]></if>
		<if test=" push_type  != null "><![CDATA[ AND A.PUSH_TYPE = #{push_type} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" business_time  != null  and  business_time  != ''  "><![CDATA[ AND A.BUSINESS_TIME = #{business_time} ]]></if>
	</select>
	
</mapper>
