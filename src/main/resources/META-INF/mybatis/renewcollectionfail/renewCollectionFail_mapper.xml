<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRenewCollectionFailDao">

	<sql id="renewCollectionFailWhereCondition">
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" bf_discount_type != null and bf_discount_type != ''  "><![CDATA[ AND A.BF_DISCOUNT_TYPE = #{bf_discount_type} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" discount_rate  != null "><![CDATA[ AND A.DISCOUNT_RATE = #{discount_rate} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" fee_scene_code != null and fee_scene_code != ''  "><![CDATA[ AND A.FEE_SCENE_CODE = #{fee_scene_code} ]]></if>
		<if test=" renew_fail_reason != null and renew_fail_reason != ''  "><![CDATA[ AND A.RENEW_FAIL_REASON = #{renew_fail_reason} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" bf_discount_rate  != null "><![CDATA[ AND A.BF_DISCOUNT_RATE = #{bf_discount_rate} ]]></if>
		<if test=" discount_type != null and discount_type != ''  "><![CDATA[ AND A.DISCOUNT_TYPE = #{discount_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryRenewCollectionFailByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryRenewCollectionFailByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryRenewCollectionFailByUnitNumberCondition">
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addRenewCollectionFail"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_RENEW_COLLECTION_FAIL.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RENEW_COLLECTION_FAIL(
				BUSINESS_CODE, BF_DISCOUNT_TYPE, INSERT_TIME, BUSI_PROD_NAME, UNIT_NUMBER, PRODUCT_CODE, UPDATE_TIME, 
				ITEM_ID, DISCOUNT_RATE, BUSI_PROD_CODE, INSERT_TIMESTAMP, ORGAN_CODE, DUE_TIME, POLICY_CODE, 
				UPDATE_BY, FEE_SCENE_CODE, RENEW_FAIL_REASON, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, BF_DISCOUNT_RATE, 
				DISCOUNT_TYPE ) 
			VALUES (
				#{business_code, jdbcType=VARCHAR}, #{bf_discount_type, jdbcType=VARCHAR} , SYSDATE , #{busi_prod_name, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{product_code, jdbcType=VARCHAR} , SYSDATE 
				, #{item_id, jdbcType=NUMERIC} , #{discount_rate, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{due_time, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{fee_scene_code, jdbcType=VARCHAR} , #{renew_fail_reason, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{bf_discount_rate, jdbcType=NUMERIC} 
				, #{discount_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRenewCollectionFail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RENEW_COLLECTION_FAIL WHERE  = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateRenewCollectionFail" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RENEW_COLLECTION_FAIL ]]>
		<set>
		<trim suffixOverrides=",">
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
			BF_DISCOUNT_TYPE = #{bf_discount_type, jdbcType=VARCHAR} ,
			BUSI_PROD_NAME = #{busi_prod_name, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    DISCOUNT_RATE = #{discount_rate, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    DUE_TIME = #{due_time, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			FEE_SCENE_CODE = #{fee_scene_code, jdbcType=VARCHAR} ,
			RENEW_FAIL_REASON = #{renew_fail_reason, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BF_DISCOUNT_RATE = #{bf_discount_rate, jdbcType=NUMERIC} ,
			DISCOUNT_TYPE = #{discount_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findRenewCollectionFailByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.BF_DISCOUNT_TYPE, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PRODUCT_CODE, 
			A.ITEM_ID, A.DISCOUNT_RATE, A.BUSI_PROD_CODE, A.ORGAN_CODE, A.DUE_TIME, A.POLICY_CODE, 
			A.FEE_SCENE_CODE, A.RENEW_FAIL_REASON, A.LIST_ID, A.BF_DISCOUNT_RATE, 
			A.DISCOUNT_TYPE FROM APP___PAS__DBUSER.T_RENEW_COLLECTION_FAIL A WHERE 1 = 1  ]]>
		<include refid="queryRenewCollectionFailByListIdCondition" />
	</select>
	
	<select id="PA_findRenewCollectionFailByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.BF_DISCOUNT_TYPE, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PRODUCT_CODE, 
			A.ITEM_ID, A.DISCOUNT_RATE, A.BUSI_PROD_CODE, A.ORGAN_CODE, A.DUE_TIME, A.POLICY_CODE, 
			A.FEE_SCENE_CODE, A.RENEW_FAIL_REASON, A.LIST_ID, A.BF_DISCOUNT_RATE, 
			A.DISCOUNT_TYPE FROM APP___PAS__DBUSER.T_RENEW_COLLECTION_FAIL A WHERE 1 = 1  ]]>
		<include refid="queryRenewCollectionFailByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findRenewCollectionFailByUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.BF_DISCOUNT_TYPE, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PRODUCT_CODE, 
			A.ITEM_ID, A.DISCOUNT_RATE, A.BUSI_PROD_CODE, A.ORGAN_CODE, A.DUE_TIME, A.POLICY_CODE, 
			A.FEE_SCENE_CODE, A.RENEW_FAIL_REASON, A.LIST_ID, A.BF_DISCOUNT_RATE, 
			A.DISCOUNT_TYPE FROM APP___PAS__DBUSER.T_RENEW_COLLECTION_FAIL A WHERE 1 = 1  ]]>
		<include refid="queryRenewCollectionFailByUnitNumberCondition" />
	</select>
	

<!-- 查询所有操作 -->
	<select id="PA_findAllRenewCollectionFail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_CODE, A.BF_DISCOUNT_TYPE, A.BUSI_PROD_NAME, A.UNIT_NUMBER, A.PRODUCT_CODE, 
			A.ITEM_ID, A.DISCOUNT_RATE, A.BUSI_PROD_CODE, A.ORGAN_CODE, A.DUE_TIME, A.POLICY_CODE, 
			A.FEE_SCENE_CODE, A.RENEW_FAIL_REASON, A.LIST_ID, A.BF_DISCOUNT_RATE, 
			A.DISCOUNT_TYPE FROM APP___PAS__DBUSER.T_RENEW_COLLECTION_FAIL A WHERE ROWNUM <=  1000  ]]>
		<include refid="renewCollectionFailWhereCondition" />
	</select>
</mapper>
