<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IOldpeopleLoanFollowupLogDao">

	<sql id="CUS_oldpeopleLoanFollowupLogWhereCondition">
		<if test=" notice_operator != null and notice_operator != ''  "><![CDATA[ AND A.NOTICE_OPERATOR = #{notice_operator} ]]></if>
		<if test=" notice_result != null and notice_result != ''  "><![CDATA[ AND A.NOTICE_RESULT = #{notice_result} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" telephone_visit_flag  != null "><![CDATA[ AND A.TELEPHONE_VISIT_FLAG = #{telephone_visit_flag} ]]></if>
		<if test=" notice_time  != null  and  notice_time  != ''  "><![CDATA[ AND A.NOTICE_TIME = #{notice_time} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryOldpeopleLoanFollowupLogByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>	
	<sql id="CUS_queryOldpeopleLoanFollowupLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="CUS_queryOldpeopleLoanFollowupLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addOldpeopleLoanFollowupLog"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_OLDPEOPLE_LFU_LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG(
				NOTICE_OPERATOR, INSERT_TIME, UPDATE_TIME, NOTICE_RESULT, ACCEPT_CODE, LOG_ID, INSERT_TIMESTAMP, 
				POLICY_CODE, TELEPHONE_VISIT_FLAG, UPDATE_BY, UPDATE_TIMESTAMP, INSERT_BY, NOTICE_TIME ) 
			VALUES (
				#{notice_operator, jdbcType=VARCHAR}, SYSDATE , SYSDATE , #{notice_result, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} , #{log_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{policy_code, jdbcType=VARCHAR} , #{telephone_visit_flag, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{notice_time, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteOldpeopleLoanFollowupLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG WHERE ]]>
		<include refid="CUS_oldpeopleLoanFollowupLogWhereCondition" /> 
	</delete>

<!-- 修改操作 -->
	<update id="updateOldpeopleLoanFollowupLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			NOTICE_OPERATOR = #{notice_operator, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			NOTICE_RESULT = #{notice_result, jdbcType=VARCHAR} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    TELEPHONE_VISIT_FLAG = #{telephone_visit_flag, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    NOTICE_TIME = #{notice_time, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findOldpeopleLoanFollowupLogByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NOTICE_OPERATOR, A.NOTICE_RESULT, A.ACCEPT_CODE, A.LOG_ID, 
			A.POLICY_CODE, A.TELEPHONE_VISIT_FLAG, A.NOTICE_TIME FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryOldpeopleLoanFollowupLogByAcceptCodeCondition" />
	</select>
	
	<select id="findOldpeopleLoanFollowupLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NOTICE_OPERATOR, A.NOTICE_RESULT, A.ACCEPT_CODE, A.LOG_ID, 
			A.POLICY_CODE, A.TELEPHONE_VISIT_FLAG, A.NOTICE_TIME FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryOldpeopleLoanFollowupLogByPolicyCodeCondition" />
	</select>
	
	<select id="findOldpeopleLoanFollowupLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NOTICE_OPERATOR, A.NOTICE_RESULT, A.ACCEPT_CODE, A.LOG_ID, 
			A.POLICY_CODE, A.TELEPHONE_VISIT_FLAG, A.NOTICE_TIME FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryOldpeopleLoanFollowupLogByLogIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapOldpeopleLoanFollowupLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NOTICE_OPERATOR, A.NOTICE_RESULT, A.ACCEPT_CODE, A.LOG_ID, 
			A.POLICY_CODE, A.TELEPHONE_VISIT_FLAG, A.NOTICE_TIME FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_oldpeopleLoanFollowupLogWhereCondition" /> 
	</select>

<!-- 查询所有操作 -->
	<select id="findAllOldpeopleLoanFollowupLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NOTICE_OPERATOR, A.NOTICE_RESULT, A.ACCEPT_CODE, A.LOG_ID, 
			A.POLICY_CODE, A.TELEPHONE_VISIT_FLAG, A.NOTICE_TIME FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_oldpeopleLoanFollowupLogWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="findOldpeopleLoanFollowupLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG A WHERE 1 = 1  ]]>
		<include refid="CUS_oldpeopleLoanFollowupLogWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="queryOldpeopleLoanFollowupLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NOTICE_OPERATOR, B.NOTICE_RESULT, B.ACCEPT_CODE, B.LOG_ID, 
			B.POLICY_CODE, B.TELEPHONE_VISIT_FLAG, B.NOTICE_TIME FROM (
					SELECT ROWNUM RN, A.NOTICE_OPERATOR, A.NOTICE_RESULT, A.ACCEPT_CODE, A.LOG_ID, 
			A.POLICY_CODE, A.TELEPHONE_VISIT_FLAG, A.NOTICE_TIME FROM APP___PAS__DBUSER.T_OLDPEOPLE_LOAN_FOLLOWUP_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_oldpeopleLoanFollowupLogWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
