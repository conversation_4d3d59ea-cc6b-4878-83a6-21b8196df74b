<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.null">

	<sql id="CUS_csAgentAmountRateWhereCondition">
		<if test=" merit_float_rate  != null "><![CDATA[ AND A.MERIT_FLOAT_RATE = #{merit_float_rate} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" agent_level != null and agent_level != ''  "><![CDATA[ AND A.AGENT_LEVEL = #{agent_level} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsAgentAmountRateByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsAgentAmountRate"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT DEV_PAS.S_RISK_LEVEL_CONFIG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CS_AGENT_AMOUNT_RATE(
				INSERT_TIMESTAMP, MERIT_FLOAT_RATE, UPDATE_BY, CFG_ID, INSERT_TIME, AGENT_LEVEL, LIST_ID, 
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{merit_float_rate, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{cfg_id, jdbcType=NUMERIC} , SYSDATE , #{agent_level, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsAgentAmountRate" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CS_AGENT_AMOUNT_RATE WHERE CFG_ID = #{cfg_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsAgentAmountRate" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CS_AGENT_AMOUNT_RATE ]]>
		<set>
		<trim suffixOverrides=",">
		    MERIT_FLOAT_RATE = #{merit_float_rate, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
			AGENT_LEVEL = #{agent_level, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsAgentAmountRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERIT_FLOAT_RATE, A.CFG_ID, A.AGENT_LEVEL, A.LIST_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_RATE A WHERE 1 = 1  ]]>
		<include refid="CUS_csAgentAmountRateWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsAgentAmountRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERIT_FLOAT_RATE, A.CFG_ID, A.AGENT_LEVEL, A.LIST_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_RATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsAgentAmountRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MERIT_FLOAT_RATE, A.CFG_ID, A.AGENT_LEVEL, A.LIST_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_RATE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsAgentAmountRateTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_AGENT_AMOUNT_RATE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsAgentAmountRateForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MERIT_FLOAT_RATE, B.CFG_ID, B.AGENT_LEVEL, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.MERIT_FLOAT_RATE, A.CFG_ID, A.AGENT_LEVEL, A.LIST_ID FROM DEV_PAS.T_CS_AGENT_AMOUNT_RATE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 按map查询操作 -->
	<select id="findAllMapCsAgentAmountRateLX" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			   select a.agent_level,
		       b.agent_level_desc as agentlevel,
		       a.list_id,
		       a.cfg_id,
		       a.merit_float_rate
			   from DEV_PAS.T_CS_AGENT_AMOUNT_RATE a
			   left join dev_pas.t_agent_level b
			   on a.agent_level = b.agent_level_code
			   where a.cfg_id = #{cfg_id} order by a.agent_level
		]]>
	</select>
</mapper>
