<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsCustomerCheckResultDao">

	<sql id="CUS_csCustomerCheckResultWhereCondition">
		<if test=" check_id  != null "><![CDATA[ AND A.CHECK_ID = #{check_id} ]]></if>
		<if test=" gender_check_result != null and gender_check_result != ''  "><![CDATA[ AND A.GENDER_CHECK_RESULT = #{gender_check_result} ]]></if>
		<if test=" check_mode != null and check_mode != ''  "><![CDATA[ AND A.CHECK_MODE = #{check_mode} ]]></if>
		<if test=" face_check_result != null and face_check_result != ''  "><![CDATA[ AND A.FACE_CHECK_RESULT = #{face_check_result} ]]></if>
		<if test=" check_customer_id  != null "><![CDATA[ AND A.CHECK_CUSTOMER_ID = #{check_customer_id} ]]></if>
		<if test=" certi_type_check_result != null and certi_type_check_result != ''  "><![CDATA[ AND A.CERTI_TYPE_CHECK_RESULT = #{certi_type_check_result} ]]></if>
		<if test=" check_date  != null  and  check_date  != ''  "><![CDATA[ AND A.CHECK_DATE = #{check_date} ]]></if>
		<if test=" limit_date_check_result != null and limit_date_check_result != ''  "><![CDATA[ AND A.LIMIT_DATE_CHECK_RESULT = #{limit_date_check_result} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" cert_code_check_result != null and cert_code_check_result != ''  "><![CDATA[ AND A.CERT_CODE_CHECK_RESULT = #{cert_code_check_result} ]]></if>
		<if test=" country_code_check_result != null and country_code_check_result != ''  "><![CDATA[ AND A.COUNTRY_CODE_CHECK_RESULT = #{country_code_check_result} ]]></if>
		<if test=" check_name != null and check_name != ''  "><![CDATA[ AND A.CHECK_NAME = #{check_name} ]]></if>
		<if test=" check_type != null and check_type != ''  "><![CDATA[ AND A.CHECK_TYPE = #{check_type} ]]></if>
		<if test=" birthday_check_result != null and birthday_check_result != ''  "><![CDATA[ AND A.BIRTHDAY_CHECK_RESULT = #{birthday_check_result} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsCustomerCheckResultByCheckIdCondition">
		<if test=" check_id  != null "><![CDATA[ AND A.CHECK_ID = #{check_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsCustomerCheckResultByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>	
	<sql id="CUS_queryCsCustomerCheckResultByCheckCustomerIdCondition">
		<if test=" check_customer_id  != null "><![CDATA[ AND A.CHECK_CUSTOMER_ID = #{check_customer_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="cus_addCsCustomerCheckResult"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="check_id">
			SELECT APP___PAS__DBUSER.S_CS_CUSTOMER_CKRT__CHECK_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT(
				CHECK_ID, GENDER_CHECK_RESULT, CHECK_MODE, FACE_CHECK_RESULT, INSERT_TIME, CHECK_CUSTOMER_ID, CERTI_TYPE_CHECK_RESULT, 
				CHECK_DATE, LIMIT_DATE_CHECK_RESULT, UPDATE_TIME, ACCEPT_CODE, CERT_CODE_CHECK_RESULT, COUNTRY_CODE_CHECK_RESULT, INSERT_TIMESTAMP, 
				CHECK_NAME, UPDATE_BY, CHECK_TYPE, BIRTHDAY_CHECK_RESULT, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{check_id, jdbcType=NUMERIC}, #{gender_check_result, jdbcType=VARCHAR} , #{check_mode, jdbcType=VARCHAR} , #{face_check_result, jdbcType=VARCHAR} , SYSDATE , #{check_customer_id, jdbcType=NUMERIC} , #{certi_type_check_result, jdbcType=VARCHAR} 
				, #{check_date, jdbcType=TIMESTAMP} , #{limit_date_check_result, jdbcType=VARCHAR} , SYSDATE , #{accept_code, jdbcType=VARCHAR} , #{cert_code_check_result, jdbcType=VARCHAR} , #{country_code_check_result, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{check_name, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{check_type, jdbcType=VARCHAR} , #{birthday_check_result, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="cus_deleteCsCustomerCheckResult" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT WHERE  CHECK_ID = #{check_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="cus_updateCsCustomerCheckResult" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT ]]>
		<set>
		<trim suffixOverrides=",">
		    CHECK_ID = #{check_id, jdbcType=NUMERIC} ,
			GENDER_CHECK_RESULT = #{gender_check_result, jdbcType=VARCHAR} ,
			CHECK_MODE = #{check_mode, jdbcType=VARCHAR} ,
			FACE_CHECK_RESULT = #{face_check_result, jdbcType=VARCHAR} ,
		    CHECK_CUSTOMER_ID = #{check_customer_id, jdbcType=NUMERIC} ,
			CERTI_TYPE_CHECK_RESULT = #{certi_type_check_result, jdbcType=VARCHAR} ,
		    CHECK_DATE = #{check_date, jdbcType=TIMESTAMP} ,
			LIMIT_DATE_CHECK_RESULT = #{limit_date_check_result, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			CERT_CODE_CHECK_RESULT = #{cert_code_check_result, jdbcType=VARCHAR} ,
			COUNTRY_CODE_CHECK_RESULT = #{country_code_check_result, jdbcType=VARCHAR} ,
			CHECK_NAME = #{check_name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			CHECK_TYPE = #{check_type, jdbcType=VARCHAR} ,
			BIRTHDAY_CHECK_RESULT = #{birthday_check_result, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  CHECK_ID = #{check_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="cus_findCsCustomerCheckResultByCheckId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_ID, A.GENDER_CHECK_RESULT, A.CHECK_MODE, A.FACE_CHECK_RESULT, A.CHECK_CUSTOMER_ID, A.CERTI_TYPE_CHECK_RESULT, 
			A.CHECK_DATE, A.LIMIT_DATE_CHECK_RESULT, A.ACCEPT_CODE, A.CERT_CODE_CHECK_RESULT, A.COUNTRY_CODE_CHECK_RESULT, 
			A.CHECK_NAME, A.CHECK_TYPE, A.BIRTHDAY_CHECK_RESULT FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE 1 = 1  ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
	</select>
	
	<select id="cus_findCsCustomerCheckResultByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_ID, A.GENDER_CHECK_RESULT, A.CHECK_MODE, A.FACE_CHECK_RESULT, A.CHECK_CUSTOMER_ID, A.CERTI_TYPE_CHECK_RESULT, 
			A.CHECK_DATE, A.LIMIT_DATE_CHECK_RESULT, A.ACCEPT_CODE, A.CERT_CODE_CHECK_RESULT, A.COUNTRY_CODE_CHECK_RESULT, 
			A.CHECK_NAME, A.CHECK_TYPE, A.BIRTHDAY_CHECK_RESULT FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE 1 = 1  ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
	</select>
	
	
	<select id="cus_findAllCsCustomerCheckResultByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_ID, A.GENDER_CHECK_RESULT, A.CHECK_MODE, A.FACE_CHECK_RESULT, A.CHECK_CUSTOMER_ID, A.CERTI_TYPE_CHECK_RESULT, 
			A.CHECK_DATE, A.LIMIT_DATE_CHECK_RESULT, A.ACCEPT_CODE, A.CERT_CODE_CHECK_RESULT, A.COUNTRY_CODE_CHECK_RESULT, 
			A.CHECK_NAME, A.CHECK_TYPE, A.BIRTHDAY_CHECK_RESULT FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE ACCEPT_CODE = #{accept_code}  ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
	</select>
	
	
	<select id="cus_findCsCustomerCheckResultByCheckCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_ID, A.GENDER_CHECK_RESULT, A.CHECK_MODE, A.FACE_CHECK_RESULT, A.CHECK_CUSTOMER_ID, A.CERTI_TYPE_CHECK_RESULT, 
			A.CHECK_DATE, A.LIMIT_DATE_CHECK_RESULT, A.ACCEPT_CODE, A.CERT_CODE_CHECK_RESULT, A.COUNTRY_CODE_CHECK_RESULT, 
			A.CHECK_NAME, A.CHECK_TYPE, A.BIRTHDAY_CHECK_RESULT FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE 1 = 1  ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="cus_findAllMapCsCustomerCheckResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_ID, A.GENDER_CHECK_RESULT, A.CHECK_MODE, A.FACE_CHECK_RESULT, A.CHECK_CUSTOMER_ID, A.CERTI_TYPE_CHECK_RESULT, 
			A.CHECK_DATE, A.LIMIT_DATE_CHECK_RESULT, A.ACCEPT_CODE, A.CERT_CODE_CHECK_RESULT, A.COUNTRY_CODE_CHECK_RESULT, 
			A.CHECK_NAME, A.CHECK_TYPE, A.BIRTHDAY_CHECK_RESULT FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="cus_findAllCsCustomerCheckResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_ID, A.GENDER_CHECK_RESULT, A.CHECK_MODE, A.FACE_CHECK_RESULT, A.CHECK_CUSTOMER_ID, A.CERTI_TYPE_CHECK_RESULT, 
			A.CHECK_DATE, A.LIMIT_DATE_CHECK_RESULT, A.ACCEPT_CODE, A.CERT_CODE_CHECK_RESULT, A.COUNTRY_CODE_CHECK_RESULT, 
			A.CHECK_NAME, A.CHECK_TYPE, A.BIRTHDAY_CHECK_RESULT FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="cus_findCsCustomerCheckResultTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE 1 = 1  ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="cus_queryCsCustomerCheckResultForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CHECK_ID, B.GENDER_CHECK_RESULT, B.CHECK_MODE, B.FACE_CHECK_RESULT, B.CHECK_CUSTOMER_ID, B.CERTI_TYPE_CHECK_RESULT, 
			B.CHECK_DATE, B.LIMIT_DATE_CHECK_RESULT, B.ACCEPT_CODE, B.CERT_CODE_CHECK_RESULT, B.COUNTRY_CODE_CHECK_RESULT, 
			B.CHECK_NAME, B.CHECK_TYPE, B.BIRTHDAY_CHECK_RESULT FROM (
					SELECT ROWNUM RN, A.CHECK_ID, A.GENDER_CHECK_RESULT, A.CHECK_MODE, A.FACE_CHECK_RESULT, A.CHECK_CUSTOMER_ID, A.CERTI_TYPE_CHECK_RESULT, 
			A.CHECK_DATE, A.LIMIT_DATE_CHECK_RESULT, A.ACCEPT_CODE, A.CERT_CODE_CHECK_RESULT, A.COUNTRY_CODE_CHECK_RESULT, 
			A.CHECK_NAME, A.CHECK_TYPE, A.BIRTHDAY_CHECK_RESULT FROM APP___PAS__DBUSER.T_CS_CUSTOMER_CHECK_RESULT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="CUS_csCustomerCheckResultWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
