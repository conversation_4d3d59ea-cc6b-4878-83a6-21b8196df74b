<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IEnStartDateCfgDao">

	<sql id="PA_enStartDateCfgWhereCondition">
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryEnStartDateCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapEnStartDateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_START_DATE_CFG A WHERE ]]>
		<include refid="PA_enStartDateCfgWhereCondition" /> 
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllEnStartDateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_START_DATE_CFG A WHERE 1 = 1 ]]>
	    <include refid="PA_enStartDateCfgWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findEnStartDateCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_EN_START_DATE_CFG A WHERE 1 = 1  ]]>
		<include refid="PA_enStartDateCfgWhereCondition" /> 
	</select>
<!-- 查询单条操作 -->	
	<select id="PA_findEnStartDateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_START_DATE_CFG A WHERE 1 = 1]]>
		<include refid="PA_enStartDateCfgWhereCondition" /> 
	</select>
	
	
		<select id="findEnStartDateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_DATE, A.LIST_ID, 
			A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_EN_START_DATE_CFG A WHERE 1=1 ]]>
		<include refid="PA_enStartDateCfgWhereCondition" /> 
	</select>
</mapper>
