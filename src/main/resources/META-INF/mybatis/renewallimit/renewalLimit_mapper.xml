<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IRenewalLimitDao">
<!--
	<sql id="PAS_renewalLimitWhereCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" limit_amount  != null "><![CDATA[ AND A.LIMIT_AMOUNT = #{limit_amount} ]]></if>
		<if test=" charge_year_bf  != null "><![CDATA[ AND A.CHARGE_YEAR_BF = #{charge_year_bf} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" std_prem_bf  != null "><![CDATA[ AND A.STD_PREM_BF = #{std_prem_bf} ]]></if>
		<if test=" limit_benefit_level != null and limit_benefit_level != ''  "><![CDATA[ AND A.LIMIT_BENEFIT_LEVEL = #{limit_benefit_level} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" charge_period_bf != null and charge_period_bf != ''  "><![CDATA[ AND A.CHARGE_PERIOD_BF = #{charge_period_bf} ]]></if>
		<if test=" amount_af  != null "><![CDATA[ AND A.AMOUNT_AF = #{amount_af} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" coerage_period != null and coerage_period != ''  "><![CDATA[ AND A.COERAGE_PERIOD = #{coerage_period} ]]></if>
		<if test=" coverage_year_bf  != null "><![CDATA[ AND A.COVERAGE_YEAR_BF = #{coverage_year_bf} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" limit_source != null and limit_source != ''  "><![CDATA[ AND A.LIMIT_SOURCE = #{limit_source} ]]></if>
		<if test=" uw_limit_unit  != null "><![CDATA[ AND A.UW_LIMIT_UNIT = #{uw_limit_unit} ]]></if>
		<if test=" limit_id  != null "><![CDATA[ AND A.LIMIT_ID = #{limit_id} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" amount_bf  != null "><![CDATA[ AND A.AMOUNT_BF = #{amount_bf} ]]></if>
		<if test=" coerage_period_bf != null and coerage_period_bf != ''  "><![CDATA[ AND A.COERAGE_PERIOD_BF = #{coerage_period_bf} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryRenewalLimitByLimitIdCondition">
		<if test=" limit_id  != null "><![CDATA[ AND A.LIMIT_ID = #{limit_id} ]]></if>
	</sql>	
	<sql id="PAS_queryRenewalLimitByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PAS_queryRenewalLimitByInsertTimestampCondition">
		<if test=" insert_timestamp  != null "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
	</sql>	
	<sql id="PAS_queryRenewalLimitByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PAS_addRenewalLimit"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="limit_id">
			SELECT APP___PAS__DBUSER.S_RENEWAL_LIMIT__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_RENEWAL_LIMIT(
				UW_ID, LIMIT_AMOUNT, CHARGE_YEAR_BF, PRODUCT_CODE, ITEM_ID, BUSI_PROD_CODE, APPLY_CODE, 
				INSERT_TIMESTAMP, STD_PREM_BF, UPDATE_BY, LIMIT_BENEFIT_LEVEL, CHARGE_YEAR, CHARGE_PERIOD_BF, AMOUNT_AF, 
				BUSI_ITEM_ID, POLICY_ID, COERAGE_PERIOD, COVERAGE_YEAR_BF, COVERAGE_YEAR, INSERT_TIME, LIMIT_SOURCE, 
				UPDATE_TIME, UW_LIMIT_UNIT, LIMIT_ID, CHARGE_PERIOD, STD_PREM_AF, POLICY_CODE, AMOUNT_BF, 
				UPDATE_TIMESTAMP, INSERT_BY, COERAGE_PERIOD_BF ) 
			VALUES (
				#{uw_id, jdbcType=NUMERIC}, #{limit_amount, jdbcType=NUMERIC} , #{charge_year_bf, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{std_prem_bf, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{limit_benefit_level, jdbcType=VARCHAR} , #{charge_year, jdbcType=NUMERIC} , #{charge_period_bf, jdbcType=VARCHAR} , #{amount_af, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{coerage_period, jdbcType=VARCHAR} , #{coverage_year_bf, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{limit_source, jdbcType=VARCHAR} 
				, SYSDATE , #{uw_limit_unit, jdbcType=NUMERIC} , #{limit_id, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{amount_bf, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{coerage_period_bf, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PAS_deleteRenewalLimit" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT WHERE  = #{limit_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PAS_updateRenewalLimit" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_RENEWAL_LIMIT ]]>
		<set>
		<trim suffixOverrides=",">
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
		    LIMIT_AMOUNT = #{limit_amount, jdbcType=NUMERIC} ,
		    CHARGE_YEAR_BF = #{charge_year_bf, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    STD_PREM_BF = #{std_prem_bf, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			LIMIT_BENEFIT_LEVEL = #{limit_benefit_level, jdbcType=VARCHAR} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
			CHARGE_PERIOD_BF = #{charge_period_bf, jdbcType=VARCHAR} ,
		    AMOUNT_AF = #{amount_af, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			COERAGE_PERIOD = #{coerage_period, jdbcType=VARCHAR} ,
		    COVERAGE_YEAR_BF = #{coverage_year_bf, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
			LIMIT_SOURCE = #{limit_source, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UW_LIMIT_UNIT = #{uw_limit_unit, jdbcType=NUMERIC} ,
		    LIMIT_ID = #{limit_id, jdbcType=NUMERIC} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
		    STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    AMOUNT_BF = #{amount_bf, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			COERAGE_PERIOD_BF = #{coerage_period_bf, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  = #{limit_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PAS_findRenewalLimitByLimitId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.LIMIT_AMOUNT, A.CHARGE_YEAR_BF, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.STD_PREM_BF, A.LIMIT_BENEFIT_LEVEL, A.CHARGE_YEAR, A.CHARGE_PERIOD_BF, A.AMOUNT_AF, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.COERAGE_PERIOD, A.COVERAGE_YEAR_BF, A.COVERAGE_YEAR, A.LIMIT_SOURCE, 
			A.UW_LIMIT_UNIT, A.LIMIT_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, A.AMOUNT_BF, 
			A.COERAGE_PERIOD_BF FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PAS_queryRenewalLimitByLimitIdCondition" />
	</select>
	
	<select id="PAS_findRenewalLimitByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.LIMIT_AMOUNT, A.CHARGE_YEAR_BF, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.STD_PREM_BF, A.LIMIT_BENEFIT_LEVEL, A.CHARGE_YEAR, A.CHARGE_PERIOD_BF, A.AMOUNT_AF, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.COERAGE_PERIOD, A.COVERAGE_YEAR_BF, A.COVERAGE_YEAR, A.LIMIT_SOURCE, 
			A.UW_LIMIT_UNIT, A.LIMIT_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, A.AMOUNT_BF, 
			A.COERAGE_PERIOD_BF FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PAS_queryRenewalLimitByBusiItemIdCondition" />
	</select>
	
	<select id="PAS_findRenewalLimitByInsertTimestamp" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.LIMIT_AMOUNT, A.CHARGE_YEAR_BF, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.STD_PREM_BF, A.LIMIT_BENEFIT_LEVEL, A.CHARGE_YEAR, A.CHARGE_PERIOD_BF, A.AMOUNT_AF, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.COERAGE_PERIOD, A.COVERAGE_YEAR_BF, A.COVERAGE_YEAR, A.LIMIT_SOURCE, 
			A.UW_LIMIT_UNIT, A.LIMIT_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, A.AMOUNT_BF, 
			A.COERAGE_PERIOD_BF FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PAS_queryRenewalLimitByInsertTimestampCondition" />
	</select>
	
	<select id="PAS_findRenewalLimitByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.LIMIT_AMOUNT, A.CHARGE_YEAR_BF, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.STD_PREM_BF, A.LIMIT_BENEFIT_LEVEL, A.CHARGE_YEAR, A.CHARGE_PERIOD_BF, A.AMOUNT_AF, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.COERAGE_PERIOD, A.COVERAGE_YEAR_BF, A.COVERAGE_YEAR, A.LIMIT_SOURCE, 
			A.UW_LIMIT_UNIT, A.LIMIT_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, A.AMOUNT_BF, 
			A.COERAGE_PERIOD_BF FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE 1 = 1  ]]>
		<include refid="PAS_queryRenewalLimitByPolicyCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PAS_findAllMapRenewalLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.LIMIT_AMOUNT, A.CHARGE_YEAR_BF, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.STD_PREM_BF, A.LIMIT_BENEFIT_LEVEL, A.CHARGE_YEAR, A.CHARGE_PERIOD_BF, A.AMOUNT_AF, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.COERAGE_PERIOD, A.COVERAGE_YEAR_BF, A.COVERAGE_YEAR, A.LIMIT_SOURCE, 
			A.UW_LIMIT_UNIT, A.LIMIT_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, A.AMOUNT_BF, 
			A.COERAGE_PERIOD_BF FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PAS_findAllRenewalLimit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.LIMIT_AMOUNT, A.CHARGE_YEAR_BF, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.STD_PREM_BF, A.LIMIT_BENEFIT_LEVEL, A.CHARGE_YEAR, A.CHARGE_PERIOD_BF, A.AMOUNT_AF, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.COERAGE_PERIOD, A.COVERAGE_YEAR_BF, A.COVERAGE_YEAR, A.LIMIT_SOURCE, 
			A.UW_LIMIT_UNIT, A.LIMIT_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, A.AMOUNT_BF, 
			A.COERAGE_PERIOD_BF FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PAS_findRenewalLimitTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PAS_queryRenewalLimitForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_ID, B.LIMIT_AMOUNT, B.CHARGE_YEAR_BF, B.PRODUCT_CODE, B.ITEM_ID, B.BUSI_PROD_CODE, B.APPLY_CODE, 
			B.STD_PREM_BF, B.LIMIT_BENEFIT_LEVEL, B.CHARGE_YEAR, B.CHARGE_PERIOD_BF, B.AMOUNT_AF, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.COERAGE_PERIOD, B.COVERAGE_YEAR_BF, B.COVERAGE_YEAR, B.LIMIT_SOURCE, 
			B.UW_LIMIT_UNIT, B.LIMIT_ID, B.CHARGE_PERIOD, B.STD_PREM_AF, B.POLICY_CODE, B.AMOUNT_BF, 
			B.COERAGE_PERIOD_BF FROM (
					SELECT ROWNUM RN, A.UW_ID, A.LIMIT_AMOUNT, A.CHARGE_YEAR_BF, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.STD_PREM_BF, A.LIMIT_BENEFIT_LEVEL, A.CHARGE_YEAR, A.CHARGE_PERIOD_BF, A.AMOUNT_AF, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.COERAGE_PERIOD, A.COVERAGE_YEAR_BF, A.COVERAGE_YEAR, A.LIMIT_SOURCE, 
			A.UW_LIMIT_UNIT, A.LIMIT_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, A.AMOUNT_BF, 
			A.COERAGE_PERIOD_BF FROM APP___PAS__DBUSER.T_RENEWAL_LIMIT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
		<select id="PAS_findRenewalLimitByBusiItemIdAndItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT M.AMOUNT_AF, M.UW_LIMIT_UNIT, M.UW_FINISH_TIME
           FROM (SELECT TRL.AMOUNT_AF,
                        TRL.UW_LIMIT_UNIT,
                        TRL.LIMIT_AMOUNT,
                        TURI.UW_FINISH_TIME
                   FROM DEV_PAS.T_RENEWAL_LIMIT   TRL,
                        DEV_PAS.T_UW_RENEWAL_INFO TURI
                  WHERE 1 = 1
                    AND TRL.UW_ID = TURI.UW_ID
                    AND TRL.POLICY_ID = TURI.POLICY_ID
                    AND TRL.BUSI_ITEM_ID = TURI.BUSI_ITEM_ID]]>
                    <if test=" busi_item_id  != null "><![CDATA[ AND TRL.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
                    <if test=" item_id  != null "><![CDATA[ AND TRL.ITEM_ID = #{item_id} ]]></if>
                  <![CDATA[   
                    AND TURI.UW_STATUS = '04'
                    AND TURI.UW_STATUS_DETAIL = '0401'
                    AND TURI.UW_SOURCE_TYPE = '3'
                    AND TRL.LIMIT_SOURCE = '3'
                  ORDER BY TRL.INSERT_TIME DESC) M
          WHERE ROWNUM = 1
		  ]]>
	</select>
	
	
</mapper>
