<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsLoanLimtCfgDao">

	<sql id="csLoanLimtCfgWhereCondition">
		<if test=" cfg_time  != null  and  cfg_time  != ''  "><![CDATA[ AND A.CFG_TIME = #{cfg_time} ]]></if>
		<if test=" del_time  != null  and  del_time  != ''  "><![CDATA[ AND A.DEL_TIME = #{del_time} ]]></if>
		<if test=" del_operator  != null "><![CDATA[ AND A.DEL_OPERATOR = #{del_operator} ]]></if>
		<if test=" loan_limit  != null "><![CDATA[ AND <PERSON>.LOAN_LIMIT = #{loan_limit} ]]></if>
		<if test=" valid_date  != null  and  valid_date  != ''  "><![CDATA[ AND A.VALID_DATE = #{valid_date} ]]></if>
		<if test=" cfg_operator  != null "><![CDATA[ AND A.CFG_OPERATOR = #{cfg_operator} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" org_code != null and org_code != ''  "><![CDATA[ AND A.ORG_CODE = #{org_code} ]]></if>
		<if test=" del_flag  != null "><![CDATA[ AND A.DEL_FLAG = #{del_flag} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsLoanLimtCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsLoanLimtCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_RISK_LEVEL_CONFIG__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG(
				CFG_TIME, INSERT_TIME, DEL_TIME, UPDATE_TIME, INSERT_TIMESTAMP, DEL_OPERATOR, UPDATE_BY, 
				LOAN_LIMIT, VALID_DATE, CFG_OPERATOR, LIST_ID, ORG_CODE, DEL_FLAG, UPDATE_TIMESTAMP, 
				INSERT_BY ) 
			VALUES (
				#{cfg_time, jdbcType=DATE}, SYSDATE , #{del_time, jdbcType=DATE} , SYSDATE , CURRENT_TIMESTAMP, #{del_operator, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} 
				, #{loan_limit, jdbcType=NUMERIC} , #{valid_date, jdbcType=DATE} , #{cfg_operator, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{org_code, jdbcType=VARCHAR} , #{del_flag, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 
	<delete id="deleteCsLoanLimtCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG WHERE  = #{} ]]>
	</delete>-->	

<!-- 修改操作 
	<update id="updateCsLoanLimtCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    CFG_TIME = #{cfg_time, jdbcType=DATE} ,
		    DEL_TIME = #{del_time, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    DEL_OPERATOR = #{del_operator, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LOAN_LIMIT = #{loan_limit, jdbcType=NUMERIC} ,
		    VALID_DATE = #{valid_date, jdbcType=DATE} ,
		    CFG_OPERATOR = #{cfg_operator, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			ORG_CODE = #{org_code, jdbcType=VARCHAR} ,
		    DEL_FLAG = #{del_flag, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  = #{} ]]>
	</update>-->
		<update id="updateinsCsLoanLimtCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG  A  SET A.LOAN_LIMIT=#{loan_limit},A.VALID_DATE=#{valid_date},A.CFG_TIME=#{cfg_time},A.CFG_OPERATOR = #{cfg_operator}   WHERE A.LIST_ID = #{list_id}]]>
	</update>
	
	
		<update id="updatedelCsLoanLimtCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG  A  SET A.DEL_FLAG='1',A.DEL_OPERATOR=#{cfg_operator},A.DEL_TIME=#{cfg_time},A.CFG_TIME=#{cfg_time},A.CFG_OPERATOR = #{cfg_operator}   WHERE A.LIST_ID = #{list_id}]]>
	</update>
	
	
	<update id="updatedelorgcodeCsLoanLimtCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG  A  SET A.DEL_FLAG='1',A.DEL_OPERATOR=#{cfg_operator},A.DEL_TIME=#{cfg_time},A.CFG_TIME=#{cfg_time},A.CFG_OPERATOR = #{cfg_operator}   WHERE A.ORG_CODE = #{org_code}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsLoanLimtCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE 1 = 1  ]]>
		<include refid="queryCsLoanLimtCfgByListIdCondition" />
	</select>
	

<!-- 查询操作 -->
	<select id="findAllMapCsLoanLimtCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="csLoanLimtCfgWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsLoanLimtCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE ROWNUM <=  1000  ]]>
		    AND A.DEL_FLAG='0' 
		<if test="org_code != null and org_code != '' ">
			<![CDATA[ AND A.ORG_CODE LIKE '${org_code}%'  ]]>
		</if>
	</select>
	
	<!-- 机构查询所有操作 -->
	<select id="findOrgcodCsLoanLimtCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE ROWNUM <=  1000  ]]>
		    AND A.DEL_FLAG='0' 
		<if test="org_code != null and org_code != '' ">
			<![CDATA[ AND A.ORG_CODE = #{org_code}  ]]>
		</if>
	</select>
	
	

<!-- 查询个数操作 -->
	<select id="findCsLoanLimtCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsLoanLimtCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CFG_TIME, B.DEL_TIME, B.DEL_OPERATOR, 
			B.LOAN_LIMIT, B.VALID_DATE, B.CFG_OPERATOR, B.LIST_ID, B.ORG_CODE, B.DEL_FLAG FROM (
					SELECT ROWNUM RN, A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 根据Listid查询单条信息 -->
	<select id="findCsLoanLimtCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A 
			WHERE A.LIST_ID = #{list_id} AND A.DEL_FLAG='0'
		]]>
	</select>
	<!-- 查询操作 -->
	<select id="findAllMapCsLoanLimtCfgByOrg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE ROWNUM <=  1000 
			AND A.VALID_DATE < #{valid_date} AND A.DEL_FLAG='0' 
      AND A.org_code in
       (SELECT T.ORGAN_CODE
          FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
         START WITH T.ORGAN_CODE = #{org_code}
        CONNECT BY PRIOR T.UPORGAN_CODE = T.ORGAN_CODE)
 order by A.org_code,A.VALID_DATE desc
			 ]]>
	</select>
	
	<!-- 查询操作 -->
	<select id="findAllMapCsLoanLimtCfgByOrgeq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_TIME, A.DEL_TIME, A.DEL_OPERATOR, 
			A.LOAN_LIMIT, A.VALID_DATE, A.CFG_OPERATOR, A.LIST_ID, A.ORG_CODE, A.DEL_FLAG FROM APP___PAS__DBUSER.T_CS_LOAN_LIMT_CFG A WHERE ROWNUM <=  1000 
			AND A.VALID_DATE < #{valid_date} AND A.DEL_FLAG='0' 
        AND A.org_code =#{org_code}      
 order by A.VALID_DATE desc
			 ]]>
	</select>		
</mapper>
