<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsSpecialAccountRelationDao">
   <sql id="csSpecialAccountRelationWhereCondition">
      <if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
      <if test=" plat_invest_acc != null and plat_invest_acc != ''  "><![CDATA[ AND A.PLAT_INVEST_ACC = #{plat_invest_acc} ]]></if>
      <if test=" plat_serialno != null and plat_serialno != ''  "><![CDATA[ AND A.PLAT_SERIALNO = #{plat_serialno} ]]></if>
      <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
      <if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
      <if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
      <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
      <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
      <if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
      <if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
      <if test=" bank_policy_id  != null "><![CDATA[ AND A.BANK_POLICY_ID = #{bank_policy_id} ]]></if>
      <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
      <if test=" validiy_end_date  != null  and  validiy_end_date  != ''  "><![CDATA[ AND A.VALIDIY_END_DATE = #{validiy_end_date} ]]></if>
   </sql>

<!-- 按索引生成的查询条件 -->  
   <sql id="queryCsSpecialAccountRelationByChangeIdCondition">
      <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
   </sql>   

<!-- 添加操作 -->
   <insert id="addCsSpecialAccountRelation"  useGeneratedKeys="true" parameterType="java.util.Map">
      <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="log_id">
         SELECT DEV_PAS.S_CS_SPECIAL_ACCOUNT_RELATION.NEXTVAL FROM DUAL
      </selectKey>
      <![CDATA[
         INSERT INTO DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION(
            INSERT_TIME, OPERATION_TYPE, PLAT_INVEST_ACC, UPDATE_TIME, PLAT_SERIALNO, APPLY_CODE, INSERT_TIMESTAMP, 
            LOG_ID, OLD_NEW, POLICY_CODE, UPDATE_BY, CHANGE_ID, LIST_ID, UPDATE_TIMESTAMP, 
            POLICY_CHG_ID, BANK_POLICY_ID, INSERT_BY, POLICY_ID ,VALIDIY_END_DATE) 
         VALUES (
            SYSDATE, #{operation_type, jdbcType=VARCHAR} , #{plat_invest_acc, jdbcType=VARCHAR} , SYSDATE , #{plat_serialno, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
            , #{log_id, jdbcType=NUMERIC} , #{old_new, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
            , #{policy_chg_id, jdbcType=NUMERIC} , #{bank_policy_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC},
            #{validiy_end_date, jdbcType=DATE} ) 
       ]]>
   </insert>

<!-- 删除操作 -->  
   <delete id="deleteCsSpecialAccountRelation" parameterType="java.util.Map">
      <![CDATA[ DELETE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION WHERE LOG_ID = #{log_id} ]]>
   </delete>

<!-- 修改操作 -->
   <update id="updateCsSpecialAccountRelation" parameterType="java.util.Map">
      <![CDATA[ UPDATE DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION ]]>
      <set>
      <trim suffixOverrides=",">
         OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
         PLAT_INVEST_ACC = #{plat_invest_acc, jdbcType=VARCHAR} ,
         UPDATE_TIME = SYSDATE , 
         PLAT_SERIALNO = #{plat_serialno, jdbcType=VARCHAR} ,
         APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
          LOG_ID = #{log_id, jdbcType=NUMERIC} ,
         OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
         POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
          UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
          CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
          LIST_ID = #{list_id, jdbcType=NUMERIC} ,
          UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
          POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
          BANK_POLICY_ID = #{bank_policy_id, jdbcType=NUMERIC} ,
          POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
          VALIDIY_END_DATE = #{validiy_end_date, jdbcType=DATE} ,
      </trim>
      </set>
      <![CDATA[ WHERE LOG_ID = #{log_id} ]]>
   </update>

<!-- 按索引查询操作 -->  
   <select id="findCsSpecialAccountRelationByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.OPERATION_TYPE, A.PLAT_INVEST_ACC, A.PLAT_SERIALNO, A.APPLY_CODE, 
         A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID, A.LIST_ID, 
         A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
      <include refid="queryCsSpecialAccountRelationByChangeIdCondition" />
   </select>
   
<!--查询单条数据 -->  
   <select id="findCsSpecialAccountRelation" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.OPERATION_TYPE, A.PLAT_INVEST_ACC, A.PLAT_SERIALNO, A.APPLY_CODE, 
         A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID, A.LIST_ID, 
         A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID,A.VALIDIY_END_DATE  FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
      <include refid="csSpecialAccountRelationWhereCondition" />
   </select>
   

<!-- 按map查询操作 -->
   <select id="findAllMapCsSpecialAccountRelation" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.OPERATION_TYPE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_CHG_ID, A.LIST_ID,
         A.LOG_ID,  A.POLICY_CODE, A.BANK_POLICY_ID, A.POLICY_ID, A.POLICY_CODE,A.APPLY_CODE,A.PLAT_SERIALNO,
         A.PLAT_INVEST_ACC,A.VALIDIY_END_DATE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION A WHERE ROWNUM <=  1000  ]]>
      <include refid="queryCsSpecialAccountRelationByChangeIdCondition" /> 
   </select>

<!-- 查询所有操作 -->
   <select id="findAllCsSpecialAccountRelation" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT A.OPERATION_TYPE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_CHG_ID, A.LIST_ID,
         A.LOG_ID,  A.POLICY_CODE, A.BANK_POLICY_ID, A.POLICY_ID, A.POLICY_CODE, A.APPLY_CODE, A.PLAT_SERIALNO,
         A.PLAT_INVEST_ACC,A.VALIDIY_END_DATE FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION A WHERE ROWNUM <=  1000   ]]>
      <include refid="csSpecialAccountRelationWhereCondition" /> 
   </select>

<!-- 查询个数操作 -->
   <select id="findCsSpecialAccountRelationTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
      <![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION A WHERE 1 = 1  ]]>
      <include refid="queryCsSpecialAccountRelationByChangeIdCondition" /> 
   </select>

<!-- 分页查询操作 -->
   <select id="queryCsSpecialAccountRelationForPage" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ SELECT B.RN AS rowNumber, B.OPERATION_TYPE, B.PLAT_INVEST_ACC, B.PLAT_SERIALNO, B.APPLY_CODE, 
         B.LOG_ID, B.OLD_NEW, B.POLICY_CODE, B.CHANGE_ID, B.LIST_ID, 
         B.POLICY_CHG_ID, B.BANK_POLICY_ID, B.POLICY_ID FROM (
               SELECT ROWNUM RN, A.OPERATION_TYPE, A.PLAT_INVEST_ACC, A.PLAT_SERIALNO, A.APPLY_CODE, 
         A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID, A.LIST_ID, 
         A.POLICY_CHG_ID, A.BANK_POLICY_ID, A.POLICY_ID FROM DEV_PAS.T_CS_SPECIAL_ACCOUNT_RELATION A WHERE ROWNUM <= #{LESS_NUM} ]]>
      <include refid="queryCsSpecialAccountRelationByChangeIdCondition" /> 
      <![CDATA[ )  B
         WHERE B.RN > #{GREATER_NUM} ]]>
   </select>
   
</mapper>
