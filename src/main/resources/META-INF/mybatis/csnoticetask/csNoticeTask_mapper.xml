<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsNoticeTaskDao">

	<sql id="CUS_csNoticeTaskWhereCondition">
		<if test=" receive_obj_type != null and receive_obj_type != ''  "><![CDATA[ AND A.RECEIVE_OBJ_TYPE = #{receive_obj_type} ]]></if>
		<if test=" notice_code  != null "><![CDATA[ AND A.NOTICE_CODE = #{notice_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" send_flag  != null "><![CDATA[ AND <PERSON>.SEND_FLAG = #{send_flag} ]]></if>
		<if test=" endorsement_flag  != null "><![CDATA[ AND A.ENDORSEMENT_FLAG = #{endorsement_flag} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsNoticeTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryCsNoticeTaskByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsNoticeTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_NOTICE_TASK__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_NOTICE_TASK(
				INSERT_TIMESTAMP, RECEIVE_OBJ_TYPE, NOTICE_CODE, UPDATE_BY, INSERT_TIME, LIST_ID, SEND_FLAG, 
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, ENDORSEMENT_FLAG, ACCEPT_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{receive_obj_type, jdbcType=VARCHAR} , #{notice_code, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , #{send_flag, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{endorsement_flag, jdbcType=NUMERIC} , #{accept_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsNoticeTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK WHERE   LIST_ID = #{list_id, jdbcType=NUMERIC}  ]]>
	</delete>

<!-- 修改操作 --><!-- 143327 add PRINT_TRANS_CODE -->
	<update id="CUS_updateCsNoticeTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_NOTICE_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			RECEIVE_OBJ_TYPE = #{receive_obj_type, jdbcType=VARCHAR} ,
		    NOTICE_CODE = #{notice_code, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SEND_FLAG = #{send_flag, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    ENDORSEMENT_FLAG = #{endorsement_flag, jdbcType=NUMERIC} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			PRINT_TRANS_CODE = #{print_trans_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id, jdbcType=NUMERIC}  ]]>
	</update>

<!-- 按索引查询操作 -->	<!-- 143327 add A.PRINT_TRANS_CODE -->
	<select id="CUS_findCsNoticeTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE, 
			A.ENDORSEMENT_FLAG, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsNoticeTaskByListIdCondition" />
	</select>
	<!-- 143327 add A.PRINT_TRANS_CODE -->
	<select id="CUS_findCsNoticeTaskByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE, 
			A.ENDORSEMENT_FLAG, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsNoticeTaskByAcceptCodeCondition" />
		<if test=" notice_code  != null "><![CDATA[ AND A.NOTICE_CODE = #{notice_code} ]]></if>
		<if test=" send_flag  != null "><![CDATA[ AND A.SEND_FLAG = #{send_flag} ]]></if>
	</select>
	

<!-- 按map查询操作 --><!-- 143327 add A.PRINT_TRANS_CODE -->
	<select id="CUS_findAllMapCsNoticeTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE, 
			A.ENDORSEMENT_FLAG, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 --><!-- 143327 add A.PRINT_TRANS_CODE --><!-- 生效告知短信批处理优化后，弃用 -->
	<select id="CUS_findAllCsNoticeTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   select rownum RN,T.RECEIVE_OBJ_TYPE,T.NOTICE_CODE,T.LIST_ID,T.SEND_FLAG,T.PRINT_TRANS_CODE,T.ENDORSEMENT_FLAG,T.ACCEPT_CODE
					  from
					(SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE, A.ENDORSEMENT_FLAG, A.ACCEPT_CODE 
					  FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A 
					 WHERE 1=1
					   and a.endorsement_flag = 1
					   and exists(select 1 from dev_pas.t_cs_print_result r where r.print_trans_code = a.print_trans_code and r.success_flag = 1) ]]>
		<include refid="CUS_csNoticeTaskWhereCondition" /> 
		<![CDATA[    union 
					SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE,A.ENDORSEMENT_FLAG, A.ACCEPT_CODE 
					  FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A 
					 WHERE 1=1
					   and a.endorsement_flag is null ]]>
		<include refid="CUS_csNoticeTaskWhereCondition" /> 
		<![CDATA[  ) T where rownum <= 6000  ]]> 
	</select>
	<!-- 生效告知短信批处理优化,查询待处理的全部数据 -->
	<select id="CUS_findAllCsNoticeTaskToPrint" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT rownum RN,A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE,A.ENDORSEMENT_FLAG, A.ACCEPT_CODE 
					  FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A 
					 WHERE rownum <= 6000
					   and a.endorsement_flag is null ]]>
		<include refid="CUS_csNoticeTaskWhereCondition" /> 
	</select>
	<select id="CUS_findAllCsNoticeTaskNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   select rownum RN,T.RECEIVE_OBJ_TYPE,T.NOTICE_CODE,T.LIST_ID,T.SEND_FLAG,T.PRINT_TRANS_CODE,T.ENDORSEMENT_FLAG,T.ACCEPT_CODE
					  from
					(SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE, A.ENDORSEMENT_FLAG, A.ACCEPT_CODE 
					  FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A 
					 WHERE 1=1
					   and a.endorsement_flag = 1
					   and exists(select 1 from dev_pas.t_cs_print_result r where r.print_trans_code = a.print_trans_code and r.success_flag = 1) ]]>
		<include refid="CUS_csNoticeTaskWhereCondition" /> 
		<![CDATA[    union 
					SELECT A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE,A.ENDORSEMENT_FLAG, A.ACCEPT_CODE 
					  FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A 
					 WHERE 1=1
					   and a.endorsement_flag = 2 ]]>
		<include refid="CUS_csNoticeTaskWhereCondition" /> 
		<![CDATA[  ) T where rownum <= 6000  ]]> 
	</select>
<!-- 查询个数操作 -->
	<select id="CUS_findCsNoticeTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 --><!-- 143327 add A.PRINT_TRANS_CODE -->
	<select id="CUS_queryCsNoticeTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RECEIVE_OBJ_TYPE, B.NOTICE_CODE, B.LIST_ID, B.SEND_FLAG, B.PRINT_TRANS_CODE, 
			B.ENDORSEMENT_FLAG, B.ACCEPT_CODE FROM (
					SELECT ROWNUM RN, A.RECEIVE_OBJ_TYPE, A.NOTICE_CODE, A.LIST_ID, A.SEND_FLAG, A.PRINT_TRANS_CODE, 
			A.ENDORSEMENT_FLAG, A.ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_NOTICE_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 143327 add Customer_Certi_Code  CUSTOMER_BIRTHDAY-->
	<select id="findAllMapCsEffectInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TA.MOBILE_TEL,TC.CUSTOMER_NAME,TCP.POLICY_CODE,TCA.ACCEPT_CODE,'1' AS RECEIVEOBJTYPE 
			,TC.CUSTOMER_CERTI_CODE,to_char(TC.CUSTOMER_BIRTHDAY,'yyyy-mm-dd') CUSTOMER_BIRTHDAY
			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCA 
         LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCP 
         ON TCA.ACCEPT_ID=TCP.ACCEPT_ID   ]]>
         
          <if test="serviceCode!= null and serviceCode == 'AE' ">
               <![CDATA[  LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_HOLDER TCPH
   							 ON TCP.POLICY_ID = TCPH.POLICY_ID AND TCPH.POLICY_CHG_ID=TCP.POLICY_CHG_ID ]]>
          </if>
          <if test="serviceCode!= null and serviceCode == 'EN' ">
               <![CDATA[  LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_HOLDER TCPH
   							 ON TCP.POLICY_ID = TCPH.POLICY_ID AND TCPH.POLICY_CHG_ID=TCP.POLICY_CHG_ID ]]>
          </if>
           <if test="serviceCode== null">
              <![CDATA[ LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TCPH  ON TCP.POLICY_ID=TCPH.POLICY_ID ]]>
          </if>
         
         <![CDATA[
         LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TC
         ON TCPH.CUSTOMER_ID=TC.CUSTOMER_ID
         LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TA
         ON TCPH.ADDRESS_ID=TA.ADDRESS_ID
     		 WHERE TCA.ACCEPT_CODE=#{acceptCode,jdbcType=VARCHAR}
      UNION 
 		 SELECT TA.MOBILE_TEL,TC.CUSTOMER_NAME,TCP.POLICY_CODE,TCA.ACCEPT_CODE,'2' AS RECEIVEOBJTYPE  
 		 ,TC.CUSTOMER_CERTI_CODE,to_char(TC.CUSTOMER_BIRTHDAY,'yyyy-mm-dd') CUSTOMER_BIRTHDAY
 		 FROM  APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCA 
   		LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCP 
         ON TCA.ACCEPT_ID=TCP.ACCEPT_ID
   		LEFT JOIN APP___PAS__DBUSER.T_INSURED_LIST TIL 
        ON TCP.POLICY_ID=TIL.POLICY_ID
  		 LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TC
  		 ON TC.CUSTOMER_ID=TIL.CUSTOMER_ID
 		  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TA
 		  ON TA.ADDRESS_ID=TIL.ADDRESS_ID
 		  WHERE  TCA.ACCEPT_CODE=#{acceptCode,jdbcType=VARCHAR}
  	 UNION 
  		 SELECT  TA.MOBILE_TEL,(SELECT TC.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER TC WHERE TC.CUSTOMER_ID=TCAP.CUSTOMER_ID) AS CUSTOMER_NAME,
           TCP.POLICY_CODE,TCA.ACCEPT_CODE,'4' AS RECEIVEOBJTYPE 
           ,(SELECT TC.CUSTOMER_CERTI_CODE
	          FROM DEV_PAS.T_CUSTOMER TC
	         WHERE TC.CUSTOMER_ID = TCAP.CUSTOMER_ID) CUSTOMER_CERTI_CODE,
           (SELECT to_char(TC.CUSTOMER_BIRTHDAY,'yyyy-mm-dd')
	          FROM DEV_PAS.T_CUSTOMER TC
	         WHERE TC.CUSTOMER_ID = TCAP.CUSTOMER_ID) CUSTOMER_BIRTHDAY
           FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCA
        INNER  JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCP 
         ON TCA.ACCEPT_ID=TCP.ACCEPT_ID 
       LEFT JOIN APP___PAS__DBUSER.T_CS_APPLICATION TCAP 
         ON TCA.CHANGE_ID=TCAP.CHANGE_ID
           LEFT JOIN APP___PAS__DBUSER.T_ADDRESS TA 
         ON TCAP.CUSTOMER_ID=TA.CUSTOMER_ID
         AND TA.ADDRESS_ID IN (
           SELECT TPH.ADDRESS_ID FROM  APP___PAS__DBUSER.T_POLICY_HOLDER TPH WHERE TPH.POLICY_CODE=TCP.POLICY_CODE  AND  TCAP.CUSTOMER_ID=TPH.CUSTOMER_ID
           UNION 
           SELECT TIL.ADDRESS_ID FROM APP___PAS__DBUSER.T_INSURED_LIST TIL WHERE TIL.POLICY_CODE=TCP.POLICY_CODE AND TIL.CUSTOMER_ID=TCAP.CUSTOMER_ID
         )
  			 WHERE   TCA.ACCEPT_CODE=#{acceptCode,jdbcType=VARCHAR}
		 ]]>
		
	</select>
	
	
</mapper>
