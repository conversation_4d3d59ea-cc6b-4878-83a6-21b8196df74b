<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPlatInvestAccBindTraceDao">

	<sql id="PA_platInvestAccBindTraceWhereCondition">
		<if test=" account_name != null and account_name != ''  "><![CDATA[ AND A.ACCOUNT_NAME = #{account_name} ]]></if>
		<if test=" account_bank != null and account_bank != ''  "><![CDATA[ AND A.ACCOUNT_BANK = #{account_bank} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" plat_invest_acc != null and plat_invest_acc != ''  "><![CDATA[ AND A.PLAT_INVEST_ACC = #{plat_invest_acc} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" deduct_amount  != null "><![CDATA[ AND A.DEDUCT_AMOUNT = #{deduct_amount} ]]></if>
		<if test=" deduct_period != null and deduct_period != ''  "><![CDATA[ AND A.DEDUCT_PERIOD = #{deduct_period} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" is_vilidateo != null and is_vilidateo != ''  "><![CDATA[ AND A.IS_VILIDATEO = #{is_vilidateo} ]]></if>
		<if test=" limit_term != null and limit_term != ''  "><![CDATA[ AND A.LIMIT_TERM = #{limit_term} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" parti_app_sheet_no != null and parti_app_sheet_no != ''  "><![CDATA[ AND A.PARTI_APP_SHEET_NO = #{parti_app_sheet_no} ]]></if>
		<if test=" deduct_type != null and deduct_type != ''  "><![CDATA[ AND A.DEDUCT_TYPE = #{deduct_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPlatInvestAccBindTraceByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPlatInvestAccBindTraceByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="PA_queryPlatInvestAccBindTraceByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addPlatInvestAccBindTrace"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PLAT_INVEST_ACC_BIND_TRACE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE(
				INSERT_TIME, ACCOUNT_NAME, ACCOUNT_BANK, PRODUCT_CODE, PLAT_INVEST_ACC, UPDATE_TIME, STATUS, 
				ACCOUNT, APPLY_CODE, DEDUCT_AMOUNT, INSERT_TIMESTAMP, DEDUCT_PERIOD, POLICY_CODE, UPDATE_BY, 
				IS_VILIDATEO, LIMIT_TERM, LIST_ID, UPDATE_TIMESTAMP, PARTI_APP_SHEET_NO, INSERT_BY, DEDUCT_TYPE ) 
			VALUES (
				SYSDATE, #{account_name, jdbcType=VARCHAR} , #{account_bank, jdbcType=VARCHAR} , #{product_code, jdbcType=VARCHAR} , #{plat_invest_acc, jdbcType=VARCHAR} , SYSDATE , #{status, jdbcType=VARCHAR} 
				, #{account, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{deduct_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{deduct_period, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{is_vilidateo, jdbcType=VARCHAR} , #{limit_term, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{parti_app_sheet_no, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{deduct_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deletePlatInvestAccBindTrace" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE WHERE  LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updatePlatInvestAccBindTrace" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE ]]>
		<set>
		<trim suffixOverrides=",">
			ACCOUNT_NAME = #{account_name, jdbcType=VARCHAR} ,
			ACCOUNT_BANK = #{account_bank, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			PLAT_INVEST_ACC = #{plat_invest_acc, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			STATUS = #{status, jdbcType=VARCHAR} ,
			ACCOUNT = #{account, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    DEDUCT_AMOUNT = #{deduct_amount, jdbcType=NUMERIC} ,
			DEDUCT_PERIOD = #{deduct_period, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			IS_VILIDATEO = #{is_vilidateo, jdbcType=VARCHAR} ,
			LIMIT_TERM = #{limit_term, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			PARTI_APP_SHEET_NO = #{parti_app_sheet_no, jdbcType=VARCHAR} ,
			DEDUCT_TYPE = #{deduct_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
<!-- 查询单条 -->	
	<select id="PA_findPlatInvestAccBindTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PRODUCT_CODE, A.PLAT_INVEST_ACC, A.STATUS, 
			A.ACCOUNT, A.APPLY_CODE, A.DEDUCT_AMOUNT, A.DEDUCT_PERIOD, A.POLICY_CODE, 
			A.IS_VILIDATEO, A.LIMIT_TERM, A.LIST_ID, A.PARTI_APP_SHEET_NO, A.DEDUCT_TYPE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPlatInvestAccBindTraceByListIdCondition" />
	</select>
<!-- 按索引查询操作 -->	
	<select id="PA_findPlatInvestAccBindTraceByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PRODUCT_CODE, A.PLAT_INVEST_ACC, A.STATUS, 
			A.ACCOUNT, A.APPLY_CODE, A.DEDUCT_AMOUNT, A.DEDUCT_PERIOD, A.POLICY_CODE, 
			A.IS_VILIDATEO, A.LIMIT_TERM, A.LIST_ID, A.PARTI_APP_SHEET_NO, A.DEDUCT_TYPE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPlatInvestAccBindTraceByListIdCondition" />
	</select>
	
	<select id="PA_findPlatInvestAccBindTraceByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PRODUCT_CODE, A.PLAT_INVEST_ACC, A.STATUS, 
			A.ACCOUNT, A.APPLY_CODE, A.DEDUCT_AMOUNT, A.DEDUCT_PERIOD, A.POLICY_CODE, 
			A.IS_VILIDATEO, A.LIMIT_TERM, A.LIST_ID, A.PARTI_APP_SHEET_NO, A.DEDUCT_TYPE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPlatInvestAccBindTraceByApplyCodeCondition" />
	</select>
	
	<select id="PA_findPlatInvestAccBindTraceByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PRODUCT_CODE, A.PLAT_INVEST_ACC, A.STATUS, 
			A.ACCOUNT, A.APPLY_CODE, A.DEDUCT_AMOUNT, A.DEDUCT_PERIOD, A.POLICY_CODE, 
			A.IS_VILIDATEO, A.LIMIT_TERM, A.LIST_ID, A.PARTI_APP_SHEET_NO, A.DEDUCT_TYPE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE 1 = 1  ]]>
		<include refid="PA_queryPlatInvestAccBindTraceByPolicyCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPlatInvestAccBindTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PRODUCT_CODE, A.PLAT_INVEST_ACC, A.STATUS, 
			A.ACCOUNT, A.APPLY_CODE, A.DEDUCT_AMOUNT, A.DEDUCT_PERIOD, A.POLICY_CODE, 
			A.IS_VILIDATEO, A.LIMIT_TERM, A.LIST_ID, A.PARTI_APP_SHEET_NO, A.DEDUCT_TYPE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_platInvestAccBindTraceWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPlatInvestAccBindTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PRODUCT_CODE, A.PLAT_INVEST_ACC, A.STATUS, 
			A.ACCOUNT, A.APPLY_CODE, A.DEDUCT_AMOUNT, A.DEDUCT_PERIOD, A.POLICY_CODE, 
			A.IS_VILIDATEO, A.LIMIT_TERM, A.LIST_ID, A.PARTI_APP_SHEET_NO, A.DEDUCT_TYPE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_platInvestAccBindTraceWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPlatInvestAccBindTraceTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE 1 = 1  ]]>
		<include refid="PA_platInvestAccBindTraceWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPlatInvestAccBindTraceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCOUNT_NAME, B.ACCOUNT_BANK, B.PRODUCT_CODE, B.PLAT_INVEST_ACC, B.STATUS, 
			B.ACCOUNT, B.APPLY_CODE, B.DEDUCT_AMOUNT, B.DEDUCT_PERIOD, B.POLICY_CODE, 
			B.IS_VILIDATEO, B.LIMIT_TERM, B.LIST_ID, B.PARTI_APP_SHEET_NO, B.DEDUCT_TYPE FROM (
					SELECT ROWNUM RN, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PRODUCT_CODE, A.PLAT_INVEST_ACC, A.STATUS, 
			A.ACCOUNT, A.APPLY_CODE, A.DEDUCT_AMOUNT, A.DEDUCT_PERIOD, A.POLICY_CODE, 
			A.IS_VILIDATEO, A.LIMIT_TERM, A.LIST_ID, A.PARTI_APP_SHEET_NO, A.DEDUCT_TYPE FROM APP___PAS__DBUSER.T_PLAT_INVEST_ACC_BIND_TRACE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_platInvestAccBindTraceWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
