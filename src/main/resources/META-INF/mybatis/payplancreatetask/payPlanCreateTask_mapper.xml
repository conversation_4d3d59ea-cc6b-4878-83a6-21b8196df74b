<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPayPlanCreateTaskDao">

	<sql id="PA_payPlanCreateTaskWhereCondition">
		<if test=" task_status  != null "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>

	<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayPlanCreateTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayPlanCreateTaskByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="PA_queryPayPlanCreateTaskByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayPlanCreateTaskByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayPlanCreateTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="PA_addPayPlanCreateTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PAY_PLAN_CRE_TASK__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK(
				INSERT_TIMESTAMP, 
				TASK_STATUS, 
				POLICY_CODE, 
				UPDATE_BY, 
				INSERT_TIME, 
				LIST_ID, 
				UPDATE_TIMESTAMP, 
				UPDATE_TIME, 
				INSERT_BY, 
				BUSI_ITEM_ID, 
				POLICY_ID, 
				BUSI_PROD_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, 
				#{task_status, jdbcType=NUMERIC} , 
				#{policy_code, jdbcType=VARCHAR} , 
				#{update_by, jdbcType=NUMERIC} , 
				SYSDATE , 
				#{list_id, jdbcType=NUMERIC} , 
				CURRENT_TIMESTAMP, 
				SYSDATE , 
				#{insert_by, jdbcType=NUMERIC} , 
				#{busi_item_id, jdbcType=NUMERIC} , 
				#{policy_id, jdbcType=NUMERIC} , 
				#{busi_prod_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="PA_deletePayPlanCreateTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK WHERE POLICY_ID=#{policy_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="PA_updatePayPlanCreateTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK ]]>
		<set>
		<trim suffixOverrides=",">
		    TASK_STATUS = #{task_status, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id, jdbcType=NUMERIC} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="PA_findPayPlanCreateTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCreateTaskByListIdCondition" />
	</select>
	
	<select id="PA_findPayPlanCreateTaskByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCreateTaskByBusiProdCodeCondition" />
	</select>
	
	<select id="PA_findPayPlanCreateTaskByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCreateTaskByBusiItemIdCondition" />
	</select>
	
	<select id="PA_findPayPlanCreateTaskByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCreateTaskByPolicyIdCondition" />
	</select>
	
	<select id="PA_findPayPlanCreateTaskByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayPlanCreateTaskByPolicyCodeCondition" />
	</select>
	

	<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayPlanCreateTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payPlanCreateTaskWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAllPayPlanCreateTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payPlanCreateTaskWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="PA_findPayPlanCreateTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE 1 = 1  ]]>
		<include refid="PA_payPlanCreateTaskWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="PA_queryPayPlanCreateTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TASK_STATUS, B.POLICY_CODE, B.LIST_ID, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.BUSI_PROD_CODE FROM (
					SELECT ROWNUM RN, A.TASK_STATUS, A.POLICY_CODE, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_PAY_PLAN_CREATE_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_payPlanCreateTaskWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 已生成给付计划未计算领取标准的存量数据 -->
	<select id="PA_queryOldPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT T.*, ROWNUM RN FROM (   
					SELECT A.POLICY_ID, A.POLICY_CODE, A.PLAN_ID
					  FROM APP___PAS__DBUSER.T_PAY_PLAN         A,
					       APP___PDS__DBUSER.T_BUSINESS_PRODUCT B,
					       APP___PDS__DBUSER.T_BUSINESS_PROD_CS C
					 WHERE 1 = 1
					   AND A.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS
					   AND B.BUSINESS_PRD_ID = C.BUSINESS_PRD_ID
					   AND A.PAY_PLAN_TYPE IN (3, 10, 11)
					   AND C.ANNUITY_PAYMENT_PLAN_TYPE IN (1, 2)
					   AND A.INSTALMENT_AMOUNT IS NULL ]]>
		<include refid="PA_payPlanCreateTaskWhereCondition" />
		   <![CDATA[ UNION 
					SELECT A.POLICY_ID, A.POLICY_CODE, NULL AS PLAN_ID
					  FROM DEV_PAS.T_CONTRACT_BUSI_PROD A, DEV_PAS.T_POLICY_ACKNOWLEDGEMENT TPA
					 WHERE 1 = 1
					   AND A.POLICY_ID = TPA.POLICY_ID
					   AND TPA.ACKNOWLEDGE_DATE IS NULL
					   AND A.LIABILITY_STATE = '1'
					   AND A.BUSI_PRD_ID IN
					       (SELECT A.BUSINESS_PRD_ID
					          FROM DEV_PDS.T_BUSINESS_PROD_CS A
					         WHERE A.ANNUITY_PAYMENT_PLAN_TYPE = 1) ]]>
		<include refid="PA_payPlanCreateTaskWhereCondition" />
		<![CDATA[  ) T WHERE 1 = 1 	]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.POLICY_ID , #{modnum}) = #{start} ]]></if>
	</select>

	<!-- 未生成给付计划信息的保单数据 -->
	<select id="PA_queryCreatePayPlanTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT T.*, ROWNUM RN FROM (   
					SELECT A.LIST_ID, A.POLICY_ID, A.POLICY_CODE 
					  FROM DEV_PAS.T_PAY_PLAN_CREATE_TASK A 
					 WHERE 1 = 1 AND A.TASK_STATUS = 0  ]]>
		<include refid="PA_payPlanCreateTaskWhereCondition" />
		<![CDATA[  ) T WHERE 1 = 1 	]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(T.POLICY_ID , #{modnum}) = #{start} ]]></if>
	</select>

	<!-- 查询满足条件的记录集 -->
	<select id="PA_queryextractPayPlanList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
					SELECT   
					       B.VALIDATE_DATE,B.APPLY_CODE,B.BRANCH_CODE,B.ORGAN_CODE,B.POLICY_TYPE,B.CHANNEL_TYPE,
					       B.SERVICE_BANK,B.SERVICE_BANK_BRANCH,
					       C.LIABILITY_STATE,C.RENEW_TIMES,C.BUSI_PRD_ID,C.END_CAUSE,C.LAPSE_DATE,
					       D.MATURITY_DATE,D.EXPIRY_DATE,D.PREM_FREQ,D.CHARGE_YEAR,D.CHARGE_PERIOD,D.COVERAGE_PERIOD,D.COVERAGE_YEAR,       
					       D.AMOUNT,D.BONUS_SA,D.TOTAL_PREM_AF,D.INITIAL_DISCNT_PREM_AF,D.UNIT,D.PAY_YEAR ,D.PAY_PERIOD,D.IS_MASTER_ITEM,
					       D.PRODUCT_ID,
					       D.ANNU_PAY_TYPE,
					       D.PAY_FREQ, 
					       D.EXTRA_PREM_AF, 
					       D.LAST_BONUS_DATE,
					       A.PLAN_FREQ,A.ANNUITY_AMOUNT,
					       A.PLAN_ID,A.POLICY_ID,B.POLICY_CODE,A.BUSI_ITEM_ID,A.ITEM_ID,A.BUSI_PROD_CODE,A.PRODUCT_CODE,A.LIAB_ID,
					       A.LIAB_NAME,A.LIAB_CODE,A.PAY_DUE_DATE,A.PAY_NUM,A.BEGIN_DATE,A.END_DATE,A.PAY_STATUS,A.SURVIVAL_MODE,A.PAY_PLAN_TYPE,
					       A.BENE_AMOUNT,A.TOTAL_AMOUNT,A.INSTALMENT_AMOUNT,A.SURVIVAL_INVEST_FLAG,A.SURVIVAL_INVEST_RESULT,
					       A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.GUARANTEE_PERIOD_TYPE,A.ONE_TIME_FLAG,A.PAY_TYPE,A.SURVIVAL_W_MODE,
					       A.MANUAL_EXTRA_DATE,C.HESITATION_PERIOD_DAY,B.RELATION_POLICY_CODE
					  FROM APP___PAS__DBUSER.T_PAY_PLAN A
					 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
					    ON A.POLICY_ID = B.POLICY_ID
					 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
					    ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
					 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT D
					    ON A.ITEM_ID = D.ITEM_ID
					   WHERE 1 = 1	
					AND A.PLAN_ID = #{plan_id} ]]>	
	</select>
	 
</mapper>
