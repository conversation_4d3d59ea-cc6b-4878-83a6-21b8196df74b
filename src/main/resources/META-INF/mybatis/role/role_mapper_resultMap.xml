<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.nb.mapper.Test">

	<resultMap id="testPO" type="com.test.po.TestPO">
		<result property="systemId" column="SYSTEM_ID"/>
		<result property="insertTime" column="INSERT_TIME"/>
		<result property="roleName" column="ROLE_NAME"/>
		<result property="updateTime" column="UPDATE_TIME"/>
		<result property="disabled" column="DISABLED"/>
		<result property="roleId" column="ROLE_ID"/>
		<result property="roleDesc" column="ROLE_DESC"/>
		<result property="roleType" column="ROLE_TYPE"/>
		<result property="insertTimestamp" column="INSERT_TIMESTAMP"/>
		<result property="subRoleType" column="SUB_ROLE_TYPE"/>
		<result property="updateBy" column="UPDATE_BY"/>
		<result property="updateTimestamp" column="UPDATE_TIMESTAMP"/>
		<result property="insertBy" column="INSERT_BY"/>
	</resultMap>
	
	<sql id="roleWhereCondition">
		<if test=" systemId != null and systemId != '' and systemId != 'null' "><![CDATA[ AND A.SYSTEM_ID = LTRIM(RTRIM(#{systemId}, ' ') , ' ') ]]></if>
		<if test=" insertTime  != null  and  insertTime  != ''  and  insertTime  != 'null' "><![CDATA[ AND A.INSERT_TIME = TO_DATE(#{insert_time},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" roleName != null and roleName != '' and roleName != 'null' "><![CDATA[ AND A.ROLE_NAME = LTRIM(RTRIM(#{roleName}, ' ') , ' ') ]]></if>
		<if test=" updateTime  != null  and  updateTime  != ''  and  updateTime  != 'null' "><![CDATA[ AND A.UPDATE_TIME = TO_DATE(#{update_time},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" disabled != null and disabled != '' and disabled != 'null' "><![CDATA[ AND A.DISABLED = LTRIM(RTRIM(#{disabled}, ' ') , ' ') ]]></if>
		<if test=" roleId  != null "><![CDATA[ AND A.ROLE_ID = ${roleId} ]]></if>
		<if test=" roleDesc != null and roleDesc != '' and roleDesc != 'null' "><![CDATA[ AND A.ROLE_DESC = LTRIM(RTRIM(#{roleDesc}, ' ') , ' ') ]]></if>
		<if test=" roleType  != null "><![CDATA[ AND A.ROLE_TYPE = ${roleType} ]]></if>
		<if test=" insertTimestamp  != null  and  insertTimestamp  != ''  and  insertTimestamp  != 'null' "><![CDATA[ AND A.INSERT_TIMESTAMP = TO_DATE(#{insert_timestamp},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" subRoleType  != null "><![CDATA[ AND A.SUB_ROLE_TYPE = ${subRoleType} ]]></if>
		<if test=" updateBy  != null "><![CDATA[ AND A.UPDATE_BY = ${updateBy} ]]></if>
		<if test=" updateTimestamp  != null  and  updateTimestamp  != ''  and  updateTimestamp  != 'null' "><![CDATA[ AND A.UPDATE_TIMESTAMP = TO_DATE(#{update_timestamp},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" insertBy  != null "><![CDATA[ AND A.INSERT_BY = ${insertBy} ]]></if>
	</sql>
	
	<insert id="addRole"  useGeneratedKeys="false"  parameterType="testPO">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_UDMP_ROLE(
		]]>
		<trim suffixOverrides=",">
			<if test=" systemId  != null and  systemId  != ''  and  systemId  != 'null' ">
		<![CDATA[ SYSTEM_ID , ]]></if>
			<if test=" insertTime  != null and  insertTime  != ''  and  insertTime  != 'null' ">
		<![CDATA[ INSERT_TIME , ]]></if>
			<if test=" roleName  != null and  roleName  != ''  and  roleName  != 'null' ">
		<![CDATA[ ROLE_NAME , ]]></if>
			<if test=" updateTime  != null and  updateTime  != ''  and  updateTime  != 'null' ">
		<![CDATA[ UPDATE_TIME , ]]></if>
			<if test=" disabled  != null and  disabled  != ''  and  disabled  != 'null' ">
		<![CDATA[ DISABLED , ]]></if>
			<if test=" roleId  != null ">
		<![CDATA[ ROLE_ID , ]]></if>
			<if test=" roleDesc  != null and  roleDesc  != ''  and  roleDesc  != 'null' ">
		<![CDATA[ ROLE_DESC , ]]></if>
			<if test=" roleType  != null ">
		<![CDATA[ ROLE_TYPE , ]]></if>
			<if test=" insertTimestamp  != null and  insertTimestamp  != ''  and  insertTimestamp  != 'null' ">
		<![CDATA[ INSERT_TIMESTAMP , ]]></if>
			<if test=" subRoleType  != null ">
		<![CDATA[ SUB_ROLE_TYPE , ]]></if>
			<if test=" updateBy  != null ">
		<![CDATA[ UPDATE_BY , ]]></if>
			<if test=" updateTimestamp  != null and  updateTimestamp  != ''  and  updateTimestamp  != 'null' ">
		<![CDATA[ UPDATE_TIMESTAMP , ]]></if>
			<if test=" insertBy  != null ">
		<![CDATA[ INSERT_BY , ]]></if>
		</trim>
		
		) VALUES (
		<trim suffixOverrides=",">
			<if test=" systemId  != null and  systemId  != ''  and  systemId  != 'null' "><![CDATA[ LTRIM(RTRIM(#{systemId}, ' ') , ' ') , ]]></if>
			<if test=" insertTime  != null and  insertTime  != ''  and  insertTime  != 'null' "><![CDATA[ TO_DATE(#{insert_time},'yyyy-MM-dd HH24:mi:ss') , ]]></if>
			<if test=" roleName  != null and  roleName  != ''  and  roleName  != 'null' "><![CDATA[ LTRIM(RTRIM(#{roleName}, ' ') , ' ') , ]]></if>
			<if test=" updateTime  != null and  updateTime  != ''  and  updateTime  != 'null' "><![CDATA[ TO_DATE(#{update_time},'yyyy-MM-dd HH24:mi:ss') , ]]></if>
			<if test=" disabled  != null and  disabled  != ''  and  disabled  != 'null' "><![CDATA[ LTRIM(RTRIM(#{disabled}, ' ') , ' ') , ]]></if>
			<if test=" roleId  != null"> <![CDATA[ ${roleId} , ]]></if>
			<if test=" roleDesc  != null and  roleDesc  != ''  and  roleDesc  != 'null' "><![CDATA[ LTRIM(RTRIM(#{roleDesc}, ' ') , ' ') , ]]></if>
			<if test=" roleType  != null"> <![CDATA[ ${roleType} , ]]></if>
			<if test=" insertTimestamp  != null and  insertTimestamp  != ''  and  insertTimestamp  != 'null' "><![CDATA[ SYSDATE , ]]> </if>
			<if test=" subRoleType  != null"> <![CDATA[ ${subRoleType} , ]]></if>
			<if test=" updateBy  != null"> <![CDATA[ ${updateBy} , ]]></if>
			<if test=" updateTimestamp  != null and  updateTimestamp  != ''  and  updateTimestamp  != 'null' "><![CDATA[ SYSDATE , ]]> </if>
			<if test=" insertBy  != null"> <![CDATA[ ${insertBy} , ]]></if>
		</trim>
		<![CDATA[
		)
		]]>
	</insert>
	
	<delete id="deleteRole" parameterType="testPO">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_UDMP_ROLE A WHERE 1 = 1 ]]>
		AND A.ROLE_ID = ${role_id}
	</delete>
	
	<update id="updateRole" parameterType="testPO">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_UDMP_ROLE A ]]>
		<set>
		<trim suffixOverrides=",">
			<if test=" systemId  != null and  systemId  != ''  and  systemId  != 'null' "> A.SYSTEM_ID = LTRIM(RTRIM(#{systemId}, ' ') , ' ') , </if>
			<if test=" roleName  != null and  roleName  != ''  and  roleName  != 'null' "> A.ROLE_NAME = LTRIM(RTRIM(#{roleName}, ' ') , ' ') , </if>
			<if test=" updateTime  != null and  updateTime  != ''  and  updateTime  != 'null' ">A.UPDATE_TIME = TO_DATE(#{update_time},'yyyy-MM-dd HH24:mi:ss') , </if>
			<if test=" disabled  != null and  disabled  != ''  and  disabled  != 'null' "> A.DISABLED = LTRIM(RTRIM(#{disabled}, ' ') , ' ') , </if>
			<if test=" roleDesc  != null and  roleDesc  != ''  and  roleDesc  != 'null' "> A.ROLE_DESC = LTRIM(RTRIM(#{roleDesc}, ' ') , ' ') , </if>
			<if test=" roleType  != null "> A.ROLE_TYPE = ${roleType} , </if>
			<if test=" subRoleType  != null "> A.SUB_ROLE_TYPE = ${subRoleType} , </if>
			<if test=" updateBy  != null "> A.UPDATE_BY = ${updateBy} , </if>
			<if test=" updateTimestamp  != null and  updateTimestamp  != ''  and  updateTimestamp  != 'null' "> A.UPDATE_TIMESTAMP = SYSDATE , </if>
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
		AND A.ROLE_ID = ${role_id}
	</update>
	
	<select id="findRole" resultMap="testPO" parameterType="testPO">
		<![CDATA[ SELECT A.SYSTEM_ID AS systemId, A.INSERT_TIME AS insertTime, A.ROLE_NAME AS roleName, A.UPDATE_TIME AS updateTime, A.DISABLED AS disabled, A.ROLE_ID AS roleId, A.ROLE_DESC AS roleDesc, A.ROLE_TYPE AS roleType, A.INSERT_TIMESTAMP AS insertTimestamp, A.SUB_ROLE_TYPE AS subRoleType, A.UPDATE_BY AS updateBy, A.UPDATE_TIMESTAMP AS updateTimestamp, A.INSERT_BY AS insertBy FROM APP___PAS__DBUSER.T_UDMP_ROLE A WHERE 1 = 1  ]]>
		<include refid="roleWhereCondition" />
	</select>
	
	<select id="findAllMapRole" resultMap="testPO" parameterType="testPO">
		<![CDATA[ SELECT A.SYSTEM_ID AS systemId, A.INSERT_TIME AS insertTime, A.ROLE_NAME AS roleName, A.UPDATE_TIME AS updateTime, A.DISABLED AS disabled, A.ROLE_ID AS roleId, A.ROLE_DESC AS roleDesc, A.ROLE_TYPE AS roleType, A.INSERT_TIMESTAMP AS insertTimestamp, A.SUB_ROLE_TYPE AS subRoleType, A.UPDATE_BY AS updateBy, A.UPDATE_TIMESTAMP AS updateTimestamp, A.INSERT_BY AS insertBy FROM APP___PAS__DBUSER.T_UDMP_ROLE A WHERE ROWNUM <=  1000  ]]>
		<include refid="roleWhereCondition" />
	</select>
	
	<select id="findAllRole" resultMap="testPO" parameterType="testPO">
		<![CDATA[ SELECT A.SYSTEM_ID AS systemId, A.INSERT_TIME AS insertTime, A.ROLE_NAME AS roleName, A.UPDATE_TIME AS updateTime, A.DISABLED AS disabled, A.ROLE_ID AS roleId, A.ROLE_DESC AS roleDesc, A.ROLE_TYPE AS roleType, A.INSERT_TIMESTAMP AS insertTimestamp, A.SUB_ROLE_TYPE AS subRoleType, A.UPDATE_BY AS updateBy, A.UPDATE_TIMESTAMP AS updateTimestamp, A.INSERT_BY AS insertBy FROM APP___PAS__DBUSER.T_UDMP_ROLE A WHERE ROWNUM <=  1000  ]]>
		<include refid="roleWhereCondition" />
	</select>
	
	<select id="findRoleTotal" resultType="java.lang.Integer" parameterType="testPO">
		<![CDATA[ SELECT  COUNT(1) FROM APP___PAS__DBUSER.T_UDMP_ROLE A WHERE 1 = 1  ]]>
		<include refid="roleWhereCondition" />
	</select>
	
	<select id="queryRoleForPage" resultMap="testPO" parameterType="testPO">
		<![CDATA[ SELECT B.RN AS rowNumber, B.SYSTEM_ID AS systemId, B.INSERT_TIME AS insertTime, B.ROLE_NAME AS roleName, B.UPDATE_TIME AS updateTime, B.DISABLED AS disabled, B.ROLE_ID AS roleId, B.ROLE_DESC AS roleDesc, B.ROLE_TYPE AS roleType, B.INSERT_TIMESTAMP AS insertTimestamp, B.SUB_ROLE_TYPE AS subRoleType, B.UPDATE_BY AS updateBy, B.UPDATE_TIMESTAMP AS updateTimestamp, B.INSERT_BY AS insertBy FROM (
					SELECT ROWNUM RN, A.* FROM APP___PAS__DBUSER.T_UDMP_ROLE A WHERE ROWNUM <= ${LESS_NUM} ]]>
		<include refid="roleWhereCondition" />
		<![CDATA[ )  B
		 WHERE B.RN > ${GREATER_NUM} ]]>
	</select>
	
</mapper>
