<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IWarnMailInfoDao">
	<sql id="PA_warnMailInfoWhereCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" email != null and email != ''  "><![CDATA[ AND A.EMAIL = #{email} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryWarnMailInfoByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryWarnMailInfoByCfgIdCondition">
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addWarnMailInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_WARN_MAIL_INFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_WARN_MAIL_INFO(
				INSERT_TIMESTAMP, UPDATE_BY, CFG_ID, INSERT_TIME, LIST_ID, EMAIL, UPDATE_TIMESTAMP, 
				UPDATE_TIME ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{cfg_id, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} , #{email, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, SYSDATE ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteWarnMailInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_WARN_MAIL_INFO WHERE ]]>
		<if test=" cfg_id  != null "><![CDATA[ CFG_ID = #{cfg_id, jdbcType=NUMERIC} ]]></if>
		<if test=" list_id  != null "><![CDATA[ LIST_ID = #{list_id, jdbcType=NUMERIC} ]]></if>
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateWarnMailInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_WARN_MAIL_INFO ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CFG_ID = #{cfg_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			EMAIL = #{email, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findWarnMailInfoByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_ID, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_WARN_MAIL_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryWarnMailInfoByListIdCondition" />
	</select>
	
	<select id="PA_findWarnMailInfoByCfgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_ID, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_WARN_MAIL_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_queryWarnMailInfoByCfgIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapWarnMailInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_ID, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_WARN_MAIL_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_warnMailInfoWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllWarnMailInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CFG_ID, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_WARN_MAIL_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_warnMailInfoWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findWarnMailInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_WARN_MAIL_INFO A WHERE 1 = 1  ]]>
		<include refid="PA_warnMailInfoWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryWarnMailInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CFG_ID, B.LIST_ID, B.EMAIL FROM (
					SELECT ROWNUM RN, A.CFG_ID, A.LIST_ID, A.EMAIL FROM APP___PAS__DBUSER.T_WARN_MAIL_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_warnMailInfoWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
