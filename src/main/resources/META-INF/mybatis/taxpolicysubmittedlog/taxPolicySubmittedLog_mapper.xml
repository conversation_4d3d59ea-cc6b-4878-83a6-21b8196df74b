<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ITaxPolicySubmittedLogDao">

	<sql id="PAS_taxPolicySubmittedLogWhereCondition">
		<if test=" fee_sequence_no != null and fee_sequence_no != ''  "><![CDATA[ AND A.FEE_SEQUENCE_NO = #{fee_sequence_no} ]]></if>
		<if test=" req_clob != null and req_clob != ''  "><![CDATA[ AND A.REQ_CLOB = #{req_clob} ]]></if>
		<if test=" result_code != null and result_code != ''  "><![CDATA[ AND A.RESULT_CODE = #{result_code} ]]></if>
		<if test=" batach_time  != null  and  batach_time  != ''  "><![CDATA[ AND A.BATACH_TIME = #{batach_time} ]]></if>
		<if test=" result_message != null and result_message != ''  "><![CDATA[ AND A.RESULT_MESSAGE = #{result_message} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
		<if test=" renewal_endorsement_no != null and renewal_endorsement_no != ''  "><![CDATA[ AND A.RENEWAL_ENDORSEMENT_NO = #{renewal_endorsement_no} ]]></if>
		<if test=" endorse_sequence_no != null and endorse_sequence_no != ''  "><![CDATA[ AND A.ENDORSE_SEQUENCE_NO = #{endorse_sequence_no} ]]></if>
		<if test=" check_trans_type != null and check_trans_type != ''  "><![CDATA[ AND A.CHECK_TRANS_TYPE = #{check_trans_type} ]]></if>
		<if test=" batach_date  != null  and  batach_date  != ''  "><![CDATA[ AND A.BATACH_DATE = #{batach_date} ]]></if>
		<if test=" renewal_sequence_no != null and renewal_sequence_no != ''  "><![CDATA[ AND A.RENEWAL_SEQUENCE_NO = #{renewal_sequence_no} ]]></if>
		<if test=" booking_sequence_no != null and booking_sequence_no != ''  "><![CDATA[ AND A.BOOKING_SEQUENCE_NO = #{booking_sequence_no} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" task_status != null and task_status != ''  "><![CDATA[ AND A.TASK_STATUS = #{task_status} ]]></if>
		<if test=" tax_code != null and tax_code != ''  "><![CDATA[ AND A.TAX_CODE = #{tax_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>

	<!-- 按索引生成的查询条件 -->	
	<sql id="PAS_queryTaxPolicySubmittedLogByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPolicySubmittedLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPolicySubmittedLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PAS_queryTaxPolicySubmittedLogByTaskIdCondition">
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="PAS_addTaxPolicySubmittedLog"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TAX_POLICY_SUBMITTED_LOG.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG(
				FEE_SEQUENCE_NO, REQ_CLOB, RESULT_CODE, BATACH_TIME, INSERT_TIME, RESULT_MESSAGE, UPDATE_TIME, 
				TASK_ID, RENEWAL_ENDORSEMENT_NO, ENDORSE_SEQUENCE_NO, CHECK_TRANS_TYPE, BATACH_DATE, INSERT_TIMESTAMP, RENEWAL_SEQUENCE_NO, 
				BOOKING_SEQUENCE_NO, POLICY_CODE, TASK_STATUS, UPDATE_BY,  TAX_CODE, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID ) 
			VALUES (
				#{fee_sequence_no, jdbcType=VARCHAR}, #{req_clob, jdbcType=VARCHAR} , #{result_code, jdbcType=VARCHAR} , #{batach_time, jdbcType=DATE} , SYSDATE , #{result_message, jdbcType=VARCHAR} , SYSDATE 
				, #{task_id, jdbcType=NUMERIC} , #{renewal_endorsement_no, jdbcType=VARCHAR} , #{endorse_sequence_no, jdbcType=VARCHAR} , #{check_trans_type, jdbcType=VARCHAR} , #{batach_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{renewal_sequence_no, jdbcType=VARCHAR} 
				, #{booking_sequence_no, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{task_status, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} ,  #{tax_code, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="PAS_deleteTaxPolicySubmittedLog" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG WHERE  = list_id = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="PAS_updateTaxPolicySubmittedLog" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG ]]>
		<set>
		<trim suffixOverrides=",">
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		<if test=" fee_sequence_no != null and fee_sequence_no != ''  "><![CDATA[ FEE_SEQUENCE_NO = #{fee_sequence_no, jdbcType=VARCHAR} , ]]></if>
		<if test=" req_clob != null and req_clob != ''  "><![CDATA[ REQ_CLOB = #{req_clob, jdbcType=VARCHAR} , ]]></if>
		<if test=" result_code != null and result_code != ''  "><![CDATA[ RESULT_CODE = #{result_code, jdbcType=VARCHAR} , ]]></if>
		<if test=" batach_time  != null  and  batach_time  != ''  "><![CDATA[ BATACH_TIME = #{batach_time, jdbcType=DATE} , ]]></if>
		<if test=" result_message != null and result_message != ''  "><![CDATA[ RESULT_MESSAGE = #{result_message, jdbcType=VARCHAR} , ]]></if>
		<if test=" task_id  != null "><![CDATA[ TASK_ID = #{task_id, jdbcType=NUMERIC} , ]]></if>
		<if test=" renewal_endorsement_no != null and renewal_endorsement_no != ''  "><![CDATA[ RENEWAL_ENDORSEMENT_NO = #{renewal_endorsement_no, jdbcType=VARCHAR} , ]]></if>
		<if test=" endorse_sequence_no != null and endorse_sequence_no != ''  "><![CDATA[ ENDORSE_SEQUENCE_NO = #{endorse_sequence_no, jdbcType=VARCHAR} , ]]></if>
		<if test=" check_trans_type != null and check_trans_type != ''  "><![CDATA[ CHECK_TRANS_TYPE = #{check_trans_type, jdbcType=VARCHAR} , ]]></if>
		<if test=" batach_date  != null  and  batach_date  != ''  "><![CDATA[ BATACH_DATE = #{batach_date, jdbcType=DATE} , ]]></if>
		<if test=" renewal_sequence_no != null and renewal_sequence_no != ''  "><![CDATA[ RENEWAL_SEQUENCE_NO = #{renewal_sequence_no, jdbcType=VARCHAR} , ]]></if>
		<if test=" booking_sequence_no != null and booking_sequence_no != ''  "><![CDATA[ BOOKING_SEQUENCE_NO = #{booking_sequence_no, jdbcType=VARCHAR} , ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ POLICY_CODE = #{policy_code, jdbcType=VARCHAR} , ]]></if>
		<if test=" task_status != null and task_status != ''  "><![CDATA[ TASK_STATUS = #{task_status, jdbcType=VARCHAR} , ]]></if>
		<if test=" tax_code != null and tax_code != ''  "><![CDATA[ TAX_CODE = #{tax_code, jdbcType=VARCHAR} , ]]></if>
		<if test=" list_id  != null "><![CDATA[ LIST_ID = #{list_id, jdbcType=NUMERIC} , ]]></if>
		<if test=" policy_id  != null "><![CDATA[ POLICY_ID = #{policy_id, jdbcType=NUMERIC} , ]]></if>
		</trim>
		</set>
		<![CDATA[ WHERE BOOKING_SEQUENCE_NO = #{booking_sequence_no, jdbcType=VARCHAR} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="PAS_findTaxPolicySubmittedLogByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS,  A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPolicySubmittedLogByListIdCondition" />
	</select>
	
	<select id="PAS_findTaxPolicySubmittedLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS, A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPolicySubmittedLogByPolicyCodeCondition" />
	</select>
	
	<select id="PAS_findTaxPolicySubmittedLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS, A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPolicySubmittedLogByPolicyIdCondition" />
	</select>
	
	<select id="PAS_findTaxPolicySubmittedLogByTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS, A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_queryTaxPolicySubmittedLogByTaskIdCondition" />
	</select>
	
	<!-- 按map查询操作 -->
	<select id="PAS_findAllMapTaxPolicySubmittedLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS, A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_taxPolicySubmittedLogWhereCondition" />
	</select>

	<!-- 查询单条操作 -->
	<select id="PAS_findTaxPolicySubmittedLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS, A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_taxPolicySubmittedLogWhereCondition" />
	</select>

	<!-- 查询所有操作 -->
	<select id="PAS_findAllTaxPolicySubmittedLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS, A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE ROWNUM <=  1000  ]]>
		<include refid="PAS_taxPolicySubmittedLogWhereCondition" />
	</select>

	<!-- 查询个数操作 -->
	<select id="PAS_findTaxPolicySubmittedLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE 1 = 1  ]]>
		<include refid="PAS_taxPolicySubmittedLogWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="PAS_queryTaxPolicySubmittedLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.FEE_SEQUENCE_NO, B.REQ_CLOB, B.RESULT_CODE, B.BATACH_TIME, B.RESULT_MESSAGE, 
			B.TASK_ID, B.RENEWAL_ENDORSEMENT_NO, B.ENDORSE_SEQUENCE_NO, B.CHECK_TRANS_TYPE, B.BATACH_DATE, B.RENEWAL_SEQUENCE_NO, 
			B.BOOKING_SEQUENCE_NO, B.POLICY_CODE, B.TASK_STATUS, B.TAX_CODE, B.LIST_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.FEE_SEQUENCE_NO, A.REQ_CLOB, A.RESULT_CODE, A.BATACH_TIME, A.RESULT_MESSAGE, 
			A.TASK_ID, A.RENEWAL_ENDORSEMENT_NO, A.ENDORSE_SEQUENCE_NO, A.CHECK_TRANS_TYPE, A.BATACH_DATE, A.RENEWAL_SEQUENCE_NO, 
			A.BOOKING_SEQUENCE_NO, A.POLICY_CODE, A.TASK_STATUS, A.TAX_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_TAX_POLICY_SUBMITTED_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PAS_taxPolicySubmittedLogWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询统计数据 -->
	<select id="PAS_queryCheckTransData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[       SELECT A.REQUESTTOTALNUM ,  /*请求记录总数*/ 
						       A.COMSUCCESSNUM ,    /*请求成功记录数*/
						       A.COMFAILNUM,        /*请求失败记录数*/
						       A.BUSSAMOUNTSUM,     /*总成功业务汇总金额*/
						      (SELECT COUNT(DISTINCT AA.TASK_ID)
						         FROM DEV_PAS.T_TAX_TRANS_LOG AA
						        WHERE AA.DATA_TYPE = '1'
						          AND AA.INTERFACE_CODE = #{check_trans_type}
						          AND TO_CHAR(AA.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{batach_date} , 'yyyy-mm-dd')
						          ) AS comBusiTotalNum, /*请求总业务数*/
						      (SELECT COUNT(DISTINCT AA.TASK_ID)
						         FROM DEV_PAS.T_TAX_TRANS_LOG AA
						        WHERE AA.DATA_TYPE = '1' 
						          AND AA.RETURN_CODE = '1'
						          AND AA.INTERFACE_CODE = #{check_trans_type}
						          AND TO_CHAR(AA.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{batach_date} , 'yyyy-mm-dd')
						          ) AS comBusiSuccessNum, /*总成功业务数*/
						      (SELECT COUNT(DISTINCT AA.TASK_ID)
						         FROM DEV_PAS.T_TAX_TRANS_LOG AA
						        WHERE AA.DATA_TYPE = '1'  
						          AND AA.INTERFACE_CODE = #{check_trans_type}
						          AND AA.RETURN_CODE != '1'
						          AND TO_CHAR(AA.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{batach_date} , 'yyyy-mm-dd')
						         ) AS combusifailnum  /*总失败业务数*/
						 FROM ( SELECT COUNT (1)  AS requesttotalnum, /*请求记录总数 */
						               SUM(CASE WHEN TTL.RETURN_CODE = '1' THEN 1 ELSE 0 END ) AS COMSUCCESSNUM , /*请求成功记录数*/
						               SUM(CASE WHEN TTL.RETURN_CODE = '0' THEN 1 ELSE 0 END ) AS COMFAILNUM, /*请求失败记录数*/
						               SUM(CASE WHEN TTL.RETURN_CODE = '1' THEN NVL(TTL.AMOUNT,0) ELSE 0 END) AS BUSSAMOUNTSUM /*总成功业务汇总金额*/
						          FROM DEV_PAS.T_TAX_TRANS_LOG TTL 
						        WHERE 1=1 
						        AND TTL.DATA_TYPE = '1'
						        AND TTL.INTERFACE_CODE = #{check_trans_type}
						        AND TO_CHAR(TTL.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{batach_date} , 'yyyy-mm-dd') ) A ]]>
	</select>

	<!-- 核心有-银保信没有数据 -->
	<select id="PAS_findNewCodeTaxTransLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				SELECT AAA.LIST_ID,
					   AAA.INTERFACE_CODE,
					   AAA.TASK_ID,
					   AAA.DATA_TYPE,
					   AAA.TRANS_DATE,
					   AAA.BUSI_NO,
					   AAA.POLICY_CODE,
					   AAA.SEQUENCE_CODE,
					   AAA.ENDORSE_CODE,
					   AAA.CASE_NO,
					   AAA.FEE_ID,
					   AAA.AMOUNT,
					   AAA.RENEWAL_ENDORSEMENT_NO,
					   AAA.RETURN_CODE,
					   AAA.RETURN_MESSAGE
				  FROM (SELECT AA.LIST_ID,
							   AA.INTERFACE_CODE,
							   AA.TASK_ID,
							   AA.DATA_TYPE,
							   AA.TRANS_DATE,
							   AA.BUSI_NO,
							   AA.POLICY_CODE,
							   AA.SEQUENCE_CODE,
							   AA.ENDORSE_CODE,
							   AA.CASE_NO,
							   AA.FEE_ID,
							   AA.AMOUNT,
							   AA.RENEWAL_ENDORSEMENT_NO,
							   AA.RETURN_CODE,
							   AA.RETURN_MESSAGE
				          FROM (SELECT A.LIST_ID,
									   A.INTERFACE_CODE,
									   A.TASK_ID,
									   A.DATA_TYPE,
									   A.TRANS_DATE,
									   A.BUSI_NO,
									   A.POLICY_CODE,
									   A.SEQUENCE_CODE,
									   A.ENDORSE_CODE,
									   A.CASE_NO,
									   A.FEE_ID,
									   A.AMOUNT,
									   A.RENEWAL_ENDORSEMENT_NO,
									   A.RETURN_CODE,
									   A.RETURN_MESSAGE, ROW_NUMBER() OVER(PARTITION BY A.POLICY_CODE ORDER BY A.TRANS_DATE DESC) AS RN
				                  FROM DEV_PAS.T_TAX_TRANS_LOG A
				                 WHERE A.INTERFACE_CODE = #{interface_code}
				                   AND TO_CHAR(A.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{trans_date}, 'yyyy-mm-dd')
				                   AND A.DATA_TYPE = '1') AA
				         WHERE RN = 1) AAA WHERE 1=1 
				    AND NOT EXISTS (
				        SELECT 1 FROM (SELECT CC.LIST_ID,
											  CC.INTERFACE_CODE,
											  CC.TASK_ID,
											  CC.DATA_TYPE,
											  CC.TRANS_DATE,
											  CC.BUSI_NO,
											  CC.POLICY_CODE,
											  CC.SEQUENCE_CODE,
											  CC.ENDORSE_CODE,
											  CC.CASE_NO,
											  CC.FEE_ID,
											  CC.AMOUNT,
											  CC.RENEWAL_ENDORSEMENT_NO,
											  CC.RETURN_CODE,
											  CC.RETURN_MESSAGE
				                         FROM (SELECT C.LIST_ID,
													  C.INTERFACE_CODE,
													  C.TASK_ID,
													  C.DATA_TYPE,
													  C.TRANS_DATE,
													  C.BUSI_NO,
													  C.POLICY_CODE,
													  C.SEQUENCE_CODE,
													  C.ENDORSE_CODE,
													  C.CASE_NO,
													  C.FEE_ID,
													  C.AMOUNT,
													  C.RENEWAL_ENDORSEMENT_NO,
													  C.RETURN_CODE,
													  C.RETURN_MESSAGE, ROW_NUMBER() OVER(PARTITION BY C.POLICY_CODE ORDER BY C.TRANS_DATE DESC) AS RN
				                                 FROM DEV_PAS.T_TAX_TRANS_LOG C
				                                WHERE C.INTERFACE_CODE = #{interface_code}
				                                  AND TO_CHAR(C.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{trans_date}, 'yyyy-mm-dd')
				                                  AND C.DATA_TYPE = '2') CC
				                        WHERE RN = 1 ) CCC
				                WHERE AAA.INTERFACE_CODE = CCC.INTERFACE_CODE ]]>
		<if test=" interface_code != null and interface_code == 'PRM011'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.FEE_ID = CCC.FEE_ID AND AAA.SEQUENCE_CODE = CCC.SEQUENCE_CODE ]]></if>				           
		<if test=" interface_code != null and interface_code == 'RNW011'.toString() "><![CDATA[ AND AAA.RENEWAL_ENDORSEMENT_NO = CCC.RENEWAL_ENDORSEMENT_NO ]]></if>				           
		<if test=" interface_code != null and interface_code == 'PRM013'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.FEE_ID = CCC.FEE_ID ]]></if>				           
		<if test=" interface_code != null and interface_code == 'END023'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.ENDORSE_CODE = CCC.ENDORSE_CODE AND AAA.SEQUENCE_CODE = CCC.SEQUENCE_CODE ]]></if>				           
		<![CDATA[  				  )  ]]>
	</select>
	
	<!-- 核心没有-银保信有数据 -->
	<select id="PAS_findOldCodeTaxTransLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				SELECT AAA.LIST_ID,
					   AAA.INTERFACE_CODE,
					   AAA.TASK_ID,
					   AAA.DATA_TYPE,
					   AAA.TRANS_DATE,
					   AAA.BUSI_NO,
					   AAA.POLICY_CODE,
					   AAA.SEQUENCE_CODE,
					   AAA.ENDORSE_CODE,
					   AAA.CASE_NO,
					   AAA.FEE_ID,
					   AAA.AMOUNT,
					   AAA.RENEWAL_ENDORSEMENT_NO,
					   AAA.RETURN_CODE,
					   AAA.RETURN_MESSAGE
				  FROM (SELECT AA.LIST_ID,
							   AA.INTERFACE_CODE,
							   AA.TASK_ID,
							   AA.DATA_TYPE,
							   AA.TRANS_DATE,
							   AA.BUSI_NO,
							   AA.POLICY_CODE,
							   AA.SEQUENCE_CODE,
							   AA.ENDORSE_CODE,
							   AA.CASE_NO,
							   AA.FEE_ID,
							   AA.AMOUNT,
							   AA.RENEWAL_ENDORSEMENT_NO,
							   AA.RETURN_CODE,
							   AA.RETURN_MESSAGE
				          FROM (SELECT A.LIST_ID,
									   A.INTERFACE_CODE,
									   A.TASK_ID,
									   A.DATA_TYPE,
									   A.TRANS_DATE,
									   A.BUSI_NO,
									   A.POLICY_CODE,
									   A.SEQUENCE_CODE,
									   A.ENDORSE_CODE,
									   A.CASE_NO,
									   A.FEE_ID,
									   A.AMOUNT,
									   A.RENEWAL_ENDORSEMENT_NO,
									   A.RETURN_CODE,
									   A.RETURN_MESSAGE, ROW_NUMBER() OVER(PARTITION BY A.POLICY_CODE ORDER BY A.TRANS_DATE DESC) AS RN
				                  FROM DEV_PAS.T_TAX_TRANS_LOG A
				                 WHERE A.INTERFACE_CODE = #{interface_code}
				                   AND TO_CHAR(A.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{trans_date}, 'yyyy-mm-dd')
				                   AND A.DATA_TYPE = '2') AA
				         WHERE RN = 1) AAA WHERE 1=1 
				    AND NOT EXISTS (
				        SELECT 1 FROM (SELECT CC.LIST_ID,
											  CC.INTERFACE_CODE,
											  CC.TASK_ID,
											  CC.DATA_TYPE,
											  CC.TRANS_DATE,
											  CC.BUSI_NO,
											  CC.POLICY_CODE,
											  CC.SEQUENCE_CODE,
											  CC.ENDORSE_CODE,
											  CC.CASE_NO,
											  CC.FEE_ID,
											  CC.AMOUNT,
											  CC.RENEWAL_ENDORSEMENT_NO,
											  CC.RETURN_CODE,
											  CC.RETURN_MESSAGE
				                         FROM (SELECT C.LIST_ID,
													  C.INTERFACE_CODE,
													  C.TASK_ID,
													  C.DATA_TYPE,
													  C.TRANS_DATE,
													  C.BUSI_NO,
													  C.POLICY_CODE,
													  C.SEQUENCE_CODE,
													  C.ENDORSE_CODE,
													  C.CASE_NO,
													  C.FEE_ID,
													  C.AMOUNT,
													  C.RENEWAL_ENDORSEMENT_NO,
													  C.RETURN_CODE,
													  C.RETURN_MESSAGE, ROW_NUMBER() OVER(PARTITION BY C.POLICY_CODE ORDER BY C.TRANS_DATE DESC) AS RN
				                                 FROM DEV_PAS.T_TAX_TRANS_LOG C
				                                WHERE C.INTERFACE_CODE = #{interface_code}
				                                  AND TO_CHAR(C.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{trans_date}, 'yyyy-mm-dd')
				                                  AND C.DATA_TYPE = '1') CC
				                        WHERE RN = 1 ) CCC
				                WHERE AAA.INTERFACE_CODE = CCC.INTERFACE_CODE ]]>
		<if test=" interface_code != null and interface_code == 'PRM011'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.FEE_ID = CCC.FEE_ID AND AAA.SEQUENCE_CODE = CCC.SEQUENCE_CODE ]]></if>				           
		<if test=" interface_code != null and interface_code == 'RNW011'.toString() "><![CDATA[ AND AAA.RENEWAL_ENDORSEMENT_NO = CCC.RENEWAL_ENDORSEMENT_NO ]]></if>				           
		<if test=" interface_code != null and interface_code == 'PRM013'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.FEE_ID = CCC.FEE_ID ]]></if>				           
		<if test=" interface_code != null and interface_code == 'END023'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.ENDORSE_CODE = CCC.ENDORSE_CODE AND AAA.SEQUENCE_CODE = CCC.SEQUENCE_CODE ]]></if>				           
		<![CDATA[  				  )  ]]>
	</select>
	
	<!-- 核心银保信都有数据 -->
	<select id="PAS_findNewAndOldCodeTaxTransLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				SELECT AAA.LIST_ID,
					   AAA.INTERFACE_CODE,
					   AAA.TASK_ID,
					   AAA.DATA_TYPE,
					   AAA.TRANS_DATE,
					   AAA.BUSI_NO,
					   AAA.POLICY_CODE,
					   AAA.SEQUENCE_CODE,
					   AAA.ENDORSE_CODE,
					   AAA.CASE_NO,
					   AAA.FEE_ID,
					   AAA.AMOUNT,
					   AAA.RENEWAL_ENDORSEMENT_NO,
					   AAA.RETURN_CODE,
					   AAA.RETURN_MESSAGE
				  FROM (SELECT AA.LIST_ID,
							   AA.INTERFACE_CODE,
							   AA.TASK_ID,
							   AA.DATA_TYPE,
							   AA.TRANS_DATE,
							   AA.BUSI_NO,
							   AA.POLICY_CODE,
							   AA.SEQUENCE_CODE,
							   AA.ENDORSE_CODE,
							   AA.CASE_NO,
							   AA.FEE_ID,
							   AA.AMOUNT,
							   AA.RENEWAL_ENDORSEMENT_NO,
							   AA.RETURN_CODE,
							   AA.RETURN_MESSAGE
				          FROM (SELECT A.LIST_ID,
									   A.INTERFACE_CODE,
									   A.TASK_ID,
									   A.DATA_TYPE,
									   A.TRANS_DATE,
									   A.BUSI_NO,
									   A.POLICY_CODE,
									   A.SEQUENCE_CODE,
									   A.ENDORSE_CODE,
									   A.CASE_NO,
									   A.FEE_ID,
									   A.AMOUNT,
									   A.RENEWAL_ENDORSEMENT_NO,
									   A.RETURN_CODE,
									   A.RETURN_MESSAGE, ROW_NUMBER() OVER(PARTITION BY A.POLICY_CODE ORDER BY A.TRANS_DATE DESC) AS RN
				                  FROM DEV_PAS.T_TAX_TRANS_LOG A
				                 WHERE A.INTERFACE_CODE = #{interface_code}
				                   AND TO_CHAR(A.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{trans_date}, 'yyyy-mm-dd')
				                   AND A.DATA_TYPE = '1') AA
				         WHERE RN = 1) AAA WHERE 1=1 
				    AND EXISTS (
				        SELECT 1 FROM (SELECT CC.LIST_ID,
											  CC.INTERFACE_CODE,
											  CC.TASK_ID,
											  CC.DATA_TYPE,
											  CC.TRANS_DATE,
											  CC.BUSI_NO,
											  CC.POLICY_CODE,
											  CC.SEQUENCE_CODE,
											  CC.ENDORSE_CODE,
											  CC.CASE_NO,
											  CC.FEE_ID,
											  CC.AMOUNT,
											  CC.RENEWAL_ENDORSEMENT_NO,
											  CC.RETURN_CODE,
											  CC.RETURN_MESSAGE
				                         FROM (SELECT C.LIST_ID,
													  C.INTERFACE_CODE,
													  C.TASK_ID,
													  C.DATA_TYPE,
													  C.TRANS_DATE,
													  C.BUSI_NO,
													  C.POLICY_CODE,
													  C.SEQUENCE_CODE,
													  C.ENDORSE_CODE,
													  C.CASE_NO,
													  C.FEE_ID,
													  C.AMOUNT,
													  C.RENEWAL_ENDORSEMENT_NO,
													  C.RETURN_CODE,
													  C.RETURN_MESSAGE, ROW_NUMBER() OVER(PARTITION BY C.POLICY_CODE ORDER BY C.TRANS_DATE DESC) AS RN
				                                 FROM DEV_PAS.T_TAX_TRANS_LOG C
				                                WHERE C.INTERFACE_CODE = #{interface_code}
				                                  AND TO_CHAR(C.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{trans_date}, 'yyyy-mm-dd')
				                                  AND C.DATA_TYPE = '2') CC
				                        WHERE RN = 1 ) CCC
				                WHERE AAA.INTERFACE_CODE = CCC.INTERFACE_CODE ]]>
		<if test=" interface_code != null and interface_code == 'PRM011'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.FEE_ID = CCC.FEE_ID AND AAA.SEQUENCE_CODE = CCC.SEQUENCE_CODE ]]></if>				           
		<if test=" interface_code != null and interface_code == 'RNW011'.toString() "><![CDATA[ AND AAA.RENEWAL_ENDORSEMENT_NO = CCC.RENEWAL_ENDORSEMENT_NO ]]></if>				           
		<if test=" interface_code != null and interface_code == 'PRM013'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.FEE_ID = CCC.FEE_ID ]]></if>				           
		<if test=" interface_code != null and interface_code == 'END023'.toString() "><![CDATA[ AND AAA.POLICY_CODE = CCC.POLICY_CODE AND AAA.ENDORSE_CODE = CCC.ENDORSE_CODE AND AAA.SEQUENCE_CODE = CCC.SEQUENCE_CODE ]]></if>				           
		<![CDATA[  				  )  ]]>
	</select>
	
	<!-- 核心银保信都有数据 -->
	<select id="PAS_findMaxTaxTransLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    
				SELECT A.LIST_ID,
					   A.INTERFACE_CODE,
					   A.TASK_ID,
					   A.DATA_TYPE,
					   A.TRANS_DATE,
					   A.BUSI_NO,
					   A.POLICY_CODE,
					   A.SEQUENCE_CODE,
					   A.ENDORSE_CODE,
					   A.CASE_NO,
					   A.FEE_ID,
					   A.AMOUNT,
					   A.RENEWAL_ENDORSEMENT_NO,
					   A.RETURN_CODE,
					   A.RETURN_MESSAGE 
				  FROM DEV_PAS.T_TAX_TRANS_LOG A
				 WHERE A.INTERFACE_CODE = #{interface_code}
				   AND TO_CHAR(A.TRANS_DATE, 'yyyy-mm-dd') = TO_CHAR(#{trans_date}, 'yyyy-mm-dd')
				   AND A.DATA_TYPE = #{data_type}
				   AND A.POLICY_CODE = #{policy_code} 
				   AND ROWNUM = 1 ]]>
	</select>
	
</mapper>
