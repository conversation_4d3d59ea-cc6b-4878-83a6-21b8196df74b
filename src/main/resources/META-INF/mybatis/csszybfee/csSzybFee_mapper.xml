<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsSzybFeeDao">
<!--
	<sql id="csSzybFeeWhereCondition">
		<if test=" refund_money_serialnuber != null and refund_money_serialnuber != ''  "><![CDATA[ AND A.REFUND_MONEY_SERIALNUBER = #{refund_money_serialnuber} ]]></if>
		<if test=" refund_confirm_code != null and refund_confirm_code != ''  "><![CDATA[ AND A.REFUND_CONFIRM_CODE = #{refund_confirm_code} ]]></if>
		<if test=" insured_confirm_code != null and insured_confirm_code != ''  "><![CDATA[ AND A.INSURED_CONFIRM_CODE = #{insured_confirm_code} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" fail_reason != null and fail_reason != ''  "><![CDATA[ AND A.FAIL_REASON = #{fail_reason} ]]></if>
		<if test=" policy_state_reason != null and policy_state_reason != ''  "><![CDATA[ AND A.POLICY_STATE_REASON = #{policy_state_reason} ]]></if>
		<if test=" stop_confirm_code != null and stop_confirm_code != ''  "><![CDATA[ AND A.STOP_CONFIRM_CODE = #{stop_confirm_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_state_code  != null "><![CDATA[ AND A.POLICY_STATE_CODE = #{policy_state_code} ]]></if>
		<if test=" yb_fail_reason != null and yb_fail_reason != ''  "><![CDATA[ AND A.YB_FAIL_REASON = #{yb_fail_reason} ]]></if>
		<if test=" refund_date  != null  and  refund_date  != ''  "><![CDATA[ AND A.REFUND_DATE = #{refund_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" refund_money_amount  != null "><![CDATA[ AND A.REFUND_MONEY_AMOUNT = #{refund_money_amount} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" refund_money_status != null and refund_money_status != ''  "><![CDATA[ AND A.REFUND_MONEY_STATUS = #{refund_money_status} ]]></if>
		<if test=" last_flag  != null "><![CDATA[ AND A.LAST_FLAG = #{last_flag} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsSzybFeeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsSzybFee"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT dev_pas.S_CS_SZYB_FEE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_SZYB_FEE(
				INSERT_TIME, REFUND_MONEY_SERIALNUBER, UPDATE_TIME, REFUND_CONFIRM_CODE, INSURED_CONFIRM_CODE, ACCEPT_CODE, FAIL_REASON, 
				POLICY_STATE_REASON, STOP_CONFIRM_CODE, INSERT_TIMESTAMP, ORGAN_CODE, POLICY_CODE, UPDATE_BY, POLICY_STATE_CODE, 
				YB_FAIL_REASON, REFUND_DATE, LIST_ID, REFUND_MONEY_AMOUNT, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, 
				REFUND_MONEY_STATUS, LAST_FLAG ) 
			VALUES (
				SYSDATE, #{refund_money_serialnuber, jdbcType=VARCHAR} , SYSDATE , #{refund_confirm_code, jdbcType=VARCHAR} , #{insured_confirm_code, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} , #{fail_reason, jdbcType=VARCHAR} 
				, #{policy_state_reason, jdbcType=VARCHAR} , #{stop_confirm_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{policy_state_code, jdbcType=NUMERIC} 
				, #{yb_fail_reason, jdbcType=VARCHAR} , #{refund_date, jdbcType=DATE} , #{list_id, jdbcType=NUMERIC} , #{refund_money_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{refund_money_status, jdbcType=VARCHAR} , #{last_flag, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsSzybFee" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_SZYB_FEE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateCsSzybFee" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_SZYB_FEE ]]>
		<set>
		<trim suffixOverrides=",">
			REFUND_MONEY_SERIALNUBER = #{refund_money_serialnuber, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			REFUND_CONFIRM_CODE = #{refund_confirm_code, jdbcType=VARCHAR} ,
			INSURED_CONFIRM_CODE = #{insured_confirm_code, jdbcType=VARCHAR} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			FAIL_REASON = #{fail_reason, jdbcType=VARCHAR} ,
			POLICY_STATE_REASON = #{policy_state_reason, jdbcType=VARCHAR} ,
			STOP_CONFIRM_CODE = #{stop_confirm_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_STATE_CODE = #{policy_state_code, jdbcType=NUMERIC} ,
			YB_FAIL_REASON = #{yb_fail_reason, jdbcType=VARCHAR} ,
		    REFUND_DATE = #{refund_date, jdbcType=DATE} ,
		    REFUND_MONEY_AMOUNT = #{refund_money_amount, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			REFUND_MONEY_STATUS = #{refund_money_status, jdbcType=VARCHAR} ,
		    LAST_FLAG = #{last_flag, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsSzybFeeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REFUND_MONEY_SERIALNUBER, A.REFUND_CONFIRM_CODE, A.INSURED_CONFIRM_CODE, A.ACCEPT_CODE, A.FAIL_REASON, 
			A.POLICY_STATE_REASON, A.STOP_CONFIRM_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_STATE_CODE, 
			A.YB_FAIL_REASON, A.REFUND_DATE, A.LIST_ID, A.REFUND_MONEY_AMOUNT, A.POLICY_ID, 
			A.REFUND_MONEY_STATUS, A.LAST_FLAG FROM APP___PAS__DBUSER.T_CS_SZYB_FEE A WHERE 1 = 1  ]]>
		<include refid="queryCsSzybFeeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsSzybFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REFUND_MONEY_SERIALNUBER, A.REFUND_CONFIRM_CODE, A.INSURED_CONFIRM_CODE, A.ACCEPT_CODE, A.FAIL_REASON, 
			A.POLICY_STATE_REASON, A.STOP_CONFIRM_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_STATE_CODE, 
			A.YB_FAIL_REASON, A.REFUND_DATE, A.LIST_ID, A.REFUND_MONEY_AMOUNT, A.POLICY_ID, 
			A.REFUND_MONEY_STATUS, A.LAST_FLAG FROM APP___PAS__DBUSER.T_CS_SZYB_FEE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsSzybFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REFUND_MONEY_SERIALNUBER, A.REFUND_CONFIRM_CODE, A.INSURED_CONFIRM_CODE, A.ACCEPT_CODE, A.FAIL_REASON, 
			A.POLICY_STATE_REASON, A.STOP_CONFIRM_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_STATE_CODE, 
			A.YB_FAIL_REASON, A.REFUND_DATE, A.LIST_ID, A.REFUND_MONEY_AMOUNT, A.POLICY_ID, 
			A.REFUND_MONEY_STATUS, A.LAST_FLAG FROM APP___PAS__DBUSER.T_CS_SZYB_FEE A WHERE ROWNUM <=  1000 
			and A.ACCEPT_CODE = #{accept_code}
			and a.LAST_FLAG = #{last_flag}
			 ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<select id="findCsSzybFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REFUND_MONEY_SERIALNUBER, A.REFUND_CONFIRM_CODE, A.INSURED_CONFIRM_CODE, A.ACCEPT_CODE, A.FAIL_REASON, 
			A.POLICY_STATE_REASON, A.STOP_CONFIRM_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_STATE_CODE, 
			A.YB_FAIL_REASON, A.REFUND_DATE, A.LIST_ID, A.REFUND_MONEY_AMOUNT, A.POLICY_ID, 
			A.REFUND_MONEY_STATUS, A.LAST_FLAG FROM APP___PAS__DBUSER.T_CS_SZYB_FEE A WHERE ROWNUM <=  1000 
			and A.ACCEPT_CODE = #{accept_code}
			and a.LAST_FLAG = #{last_flag}
			 ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findCsSzybFeeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_SZYB_FEE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsSzybFeeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.REFUND_MONEY_SERIALNUBER, B.REFUND_CONFIRM_CODE, B.INSURED_CONFIRM_CODE, B.ACCEPT_CODE, B.FAIL_REASON, 
			B.POLICY_STATE_REASON, B.STOP_CONFIRM_CODE, B.ORGAN_CODE, B.POLICY_CODE, B.POLICY_STATE_CODE, 
			B.YB_FAIL_REASON, B.REFUND_DATE, B.LIST_ID, B.REFUND_MONEY_AMOUNT, B.POLICY_ID, 
			B.REFUND_MONEY_STATUS, B.LAST_FLAG FROM (
					SELECT ROWNUM RN, A.REFUND_MONEY_SERIALNUBER, A.REFUND_CONFIRM_CODE, A.INSURED_CONFIRM_CODE, A.ACCEPT_CODE, A.FAIL_REASON, 
			A.POLICY_STATE_REASON, A.STOP_CONFIRM_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.POLICY_STATE_CODE, 
			A.YB_FAIL_REASON, A.REFUND_DATE, A.LIST_ID, A.REFUND_MONEY_AMOUNT, A.POLICY_ID, 
			A.REFUND_MONEY_STATUS, A.LAST_FLAG FROM APP___PAS__DBUSER.T_CS_SZYB_FEE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findMedicalPolicyStopListCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT COUNT(1)
			  FROM (
select csf.REFUND_MONEY_SERIALNUBER,
       csf.REFUND_CONFIRM_CODE,
       csf.INSURED_CONFIRM_CODE,
       csf.ACCEPT_CODE,
       csf.FAIL_REASON,
       csf.POLICY_STATE_REASON,
       csf.STOP_CONFIRM_CODE,
       csf.ORGAN_CODE,
       csf.POLICY_CODE,
       csf.POLICY_STATE_CODE,
       csf.YB_FAIL_REASON,
       csf.REFUND_DATE,
       csf.LIST_ID,
       csf.REFUND_MONEY_AMOUNT,
       csf.POLICY_ID,
       csf.REFUND_MONEY_STATUS,
       csf.LAST_FLAG
  from dev_pas.T_CS_SZYB_FEE csf
 inner join dev_pas.t_medical_card mc
    on mc.policy_code = csf.policy_code
 inner join dev_pas.t_cs_accept_change ac
    on ac.accept_code = csf.accept_code
 where 1 = 1
   and ac.service_code in ('XT', 'CT', 'EA')
   and ac.accept_status = '18'
   and csf.LAST_FLAG = '1'
   and csf.REFUND_CONFIRM_CODE is not null
   and csf.REFUND_MONEY_STATUS = '02'
   and csf.POLICY_STATE_CODE is null ]]> 
   <if test="policy_code != null and policy_code != '' ">
			<![CDATA[  AND csf.accept_code = #{accept_code} ]]>
		</if>
		<![CDATA[ 
				 ORDER BY csf.ACCEPT_CODE ) 
			]]>	
	</select>
	
	
		<select id="findMedicalPolicyStopList" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
select csf.REFUND_MONEY_SERIALNUBER,
       csf.REFUND_CONFIRM_CODE,
       csf.INSURED_CONFIRM_CODE,
       csf.ACCEPT_CODE,
       csf.FAIL_REASON,
       csf.POLICY_STATE_REASON,
       csf.STOP_CONFIRM_CODE,
       csf.ORGAN_CODE,
       csf.POLICY_CODE,
       csf.POLICY_STATE_CODE,
       csf.YB_FAIL_REASON,
       csf.REFUND_DATE,
       csf.LIST_ID,
       csf.REFUND_MONEY_AMOUNT,
       csf.POLICY_ID,
       csf.REFUND_MONEY_STATUS,
       csf.LAST_FLAG
  from dev_pas.T_CS_SZYB_FEE csf
 inner join dev_pas.t_medical_card mc
    on mc.policy_code = csf.policy_code
 inner join dev_pas.t_cs_accept_change ac
    on ac.accept_code = csf.accept_code
 where 1 = 1
   and ac.service_code in ('XT', 'CT', 'EA')
   and ac.accept_status = '18'
   and csf.LAST_FLAG = '1'
   and csf.REFUND_CONFIRM_CODE is not null
   and csf.REFUND_MONEY_STATUS = '02'
   and csf.POLICY_STATE_CODE is null ]]> 
   <if test="policy_code != null and policy_code != '' ">
			<![CDATA[  AND csf.accept_code = #{accept_code} ]]>
		</if>
		<if test="start != null and counts != null ">
				<![CDATA[ 
						AND MOD(csf.ACCEPT_CODE, #{counts})= #{start}
				]]>
		</if>
		<![CDATA[ 
				 ORDER BY csf.ACCEPT_CODE )
			]]>	
	</select>
	
	
</mapper>
