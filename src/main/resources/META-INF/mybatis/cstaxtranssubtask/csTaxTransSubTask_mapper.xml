<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsTaxTransSubTaskDao">
	<sql id="csTaxTransSubTaskWhereCondition">
		<if test=" endor_seqno != null and endor_seqno != ''  "><![CDATA[ AND A.ENDOR_SEQNO = #{endor_seqno} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" cs_send_status != null and cs_send_status != ''  "><![CDATA[ AND A.CS_SEND_STATUS = #{cs_send_status} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" response_time  != null  and  response_time  != ''  "><![CDATA[ AND A.RESPONSE_TIME = #{response_time} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" endor_type != null and endor_type != ''  "><![CDATA[ AND A.ENDOR_TYPE = #{endor_type} ]]></if>
		<if test=" cancel_send_status != null and cancel_send_status != ''  "><![CDATA[ AND A.CANCEL_SEND_STATUS = #{cancel_send_status} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryCsTaxTransSubTaskByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryCsTaxTransSubTaskByAcceptCodeCondition">
		<if test=" accept_code != null and accept_code != '' "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>	
	<sql id="queryCsTaxTransSubTaskByAcceptIdCondition">
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
	</sql>	
	<sql id="queryCsTaxTransSubTaskByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addCsTaxTransSubTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CS_TAX_SUBTASK__LISTID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK(
				ENDOR_SEQNO, ACCEPT_ID, CS_SEND_STATUS, INSERT_TIME, UPDATE_TIME, ACCEPT_CODE, INSERT_TIMESTAMP, 
				POLICY_CODE, RESPONSE_TIME, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, ENDOR_TYPE, 
				CANCEL_SEND_STATUS ) 
			VALUES (
				#{endor_seqno, jdbcType=VARCHAR}, #{accept_id, jdbcType=NUMERIC} , #{cs_send_status, jdbcType=VARCHAR} , SYSDATE , SYSDATE , #{accept_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{policy_code, jdbcType=VARCHAR} , #{response_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{endor_type, jdbcType=VARCHAR} 
				, #{cancel_send_status, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteCsTaxTransSubTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE 1 = 1 ]]>
		<include refid="csTaxTransSubTaskWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="updateCsTaxTransSubTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK ]]>
		<set>
		<trim suffixOverrides=",">
			ENDOR_SEQNO = #{endor_seqno, jdbcType=VARCHAR} ,
		    ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} ,
			CS_SEND_STATUS = #{cs_send_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    RESPONSE_TIME = #{response_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			ENDOR_TYPE = #{endor_type, jdbcType=VARCHAR} ,
			CANCEL_SEND_STATUS = #{cancel_send_status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
		<![CDATA[AND LIST_ID = #{list_id, jdbcType=NUMERIC}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findCsTaxTransSubTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE, 
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE, 
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCsTaxTransSubTaskByListIdCondition" />
	</select>
	
	<select id="findCsTaxTransSubTaskByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE, 
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE, 
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCsTaxTransSubTaskByAcceptCodeCondition" />
	</select>
	
	<select id="findCsTaxTransSubTaskByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE, 
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE, 
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCsTaxTransSubTaskByAcceptIdCondition" />
	</select>
	
	<select id="findCsTaxTransSubTaskByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE, 
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE, 
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE 1 = 1  ]]>
		<include refid="queryCsTaxTransSubTaskByPolicyCodeCondition" />
	</select>

	<select id="findCsTaxTransSubTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE,
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE,
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE 1 = 1  ]]>
		<include refid="csTaxTransSubTaskWhereCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapCsTaxTransSubTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE, 
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE, 
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="csTaxTransSubTaskWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="findAllCsTaxTransSubTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE, 
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE, 
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE ROWNUM <=  1000  ]]>
		<include refid="csTaxTransSubTaskWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="findCsTaxTransSubTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE 1 = 1  ]]>
		<include refid="csTaxTransSubTaskWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsTaxTransSubTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ENDOR_SEQNO, B.ACCEPT_ID, B.CS_SEND_STATUS, B.ACCEPT_CODE, 
			B.POLICY_CODE, B.RESPONSE_TIME, B.LIST_ID, B.ENDOR_TYPE, 
			B.CANCEL_SEND_STATUS FROM (
					SELECT ROWNUM RN, A.ENDOR_SEQNO, A.ACCEPT_ID, A.CS_SEND_STATUS, A.ACCEPT_CODE, 
			A.POLICY_CODE, A.RESPONSE_TIME, A.LIST_ID, A.ENDOR_TYPE, 
			A.CANCEL_SEND_STATUS FROM APP___PAS__DBUSER.T_CS_TAX_TRANS_SUB_TASK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="csTaxTransSubTaskWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
