<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ITrustContractDao">

	<sql id="CUS_trustContractWhereCondition">
		<if test=" order_id != null and order_id != ''  "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" trust_project != null and trust_project != ''  "><![CDATA[ AND A.TRUST_PROJECT = #{trust_project} ]]></if>
		<if test=" cont_no != null and cont_no != ''  "><![CDATA[ AND A.CONT_NO = #{cont_no} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" oper_type  != null "><![CDATA[ AND A.OPER_TYPE = #{oper_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryTrustContractByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CUS_queryTrustContractByContNoCondition">
		<if test=" cont_no != null and cont_no != '' "><![CDATA[ AND A.CONT_NO = #{cont_no} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addTrustContract"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_TRUST_CONTRACT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_TRUST_CONTRACT(
				INSERT_TIMESTAMP, ORDER_ID, TRUST_PROJECT, UPDATE_BY, CONT_NO, INSERT_TIME, LIST_ID, 
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, OPER_TYPE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{order_id, jdbcType=VARCHAR} , #{trust_project, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{cont_no, jdbcType=VARCHAR} , SYSDATE , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{oper_type, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteTrustContract" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT WHERE  LIST_ID= #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateTrustContract" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_TRUST_CONTRACT ]]>
		<set>
		<trim suffixOverrides=",">
			ORDER_ID = #{order_id, jdbcType=VARCHAR} ,
			TRUST_PROJECT = #{trust_project, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			CONT_NO = #{cont_no, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    OPER_TYPE = #{oper_type, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID= #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findTrustContractByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID, A.TRUST_PROJECT, A.CONT_NO, A.LIST_ID, 
			A.OPER_TYPE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A WHERE 1 = 1  ]]>
		<include refid="CUS_queryTrustContractByListIdCondition" />
	</select>
	
	<select id="CUS_findTrustContractByContNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID, A.TRUST_PROJECT, A.CONT_NO, A.LIST_ID, 
			A.OPER_TYPE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A WHERE 1 = 1  ]]>
		<include refid="CUS_queryTrustContractByContNoCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapTrustContract" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID, A.TRUST_PROJECT, A.CONT_NO, A.LIST_ID, 
			A.OPER_TYPE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllTrustContract" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORDER_ID, A.TRUST_PROJECT, A.CONT_NO, A.LIST_ID, 
			A.OPER_TYPE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_trustContractWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findTrustContractTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A WHERE 1 = 1  ]]>
		<include refid="CUS_trustContractWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryTrustContractForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ORDER_ID, B.TRUST_PROJECT, B.CONT_NO, B.LIST_ID, 
			B.OPER_TYPE FROM (
					SELECT ROWNUM RN, A.ORDER_ID, A.TRUST_PROJECT, A.CONT_NO, A.LIST_ID, 
			A.OPER_TYPE FROM APP___PAS__DBUSER.T_TRUST_CONTRACT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<insert id="CUS_addOrderData"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="order_list_id">
			SELECT APP___PAS__DBUSER.S_ORDER_LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ORDER(
				INSERT_TIMESTAMP, ORDER_ID, ORDER_LIST_ID, UPDATE_BY, ORDER_STATUS, INSERT_TIME,  
				UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{order_id, jdbcType=VARCHAR}  , #{order_list_id, jdbcType=VARCHAR}  
				, #{update_by, jdbcType=NUMERIC} , #{order_status, jdbcType=VARCHAR} , SYSDATE 
				, CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
	<select id="CUS_findOrderData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ORDER_LIST_ID, A.ORDER_ID, A.ORDER_STATUS 
						FROM APP___PAS__DBUSER.T_ORDER A
					 WHERE A.ORDER_ID = #{order_id}
		]]>		 
	</select>
	
	
	<update id="CUS_updateOrderData" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ORDER ]]>
		<set>
		<trim suffixOverrides=",">
			ORDER_STATUS = #{order_status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE  order_id= #{order_id} and  ORDER_STATUS = 2]]>
	</update>
	
</mapper>
