<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IPolicyStatusTraceDao">

	<sql id="policyStatusTraceWhereCondition">
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" busi_code_end != null and busi_code_end != ''  "><![CDATA[ AND A.BUSI_CODE_END = #{busi_code_end} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" state_remark != null and state_remark != ''  "><![CDATA[ AND A.STATE_REMARK = #{state_remark} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" state_cause != null and state_cause != ''  "><![CDATA[ AND A.STATE_CAUSE = #{state_cause} ]]></if>
		<if test=" busi_code_start != null and busi_code_start != ''  "><![CDATA[ AND A.BUSI_CODE_START = #{busi_code_start} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" state_type != null and state_type != ''  "><![CDATA[ AND A.STATE_TYPE = #{state_type} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->	
	<sql id="queryPolicyStatusTraceByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryPolicyStatusTraceByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

	<!-- 添加操作 -->
	<insert id="addPolicyStatusTrace"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_STATUS_TR__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_STATUS_TRACE(
				INSERT_TIME, END_DATE, UPDATE_TIME, BUSI_CODE_END, BUSI_PROD_CODE, INSERT_TIMESTAMP, START_DATE, 
				STATE_REMARK, LIABILITY_STATE, POLICY_CODE, STATE_CAUSE, UPDATE_BY, BUSI_CODE_START, LIST_ID, 
				UPDATE_TIMESTAMP, INSERT_BY, BUSI_ITEM_ID, STATE_TYPE, POLICY_ID ) 
			VALUES (
				SYSDATE, #{end_date, jdbcType=DATE} , SYSDATE , #{busi_code_end, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{start_date, jdbcType=DATE} 
				, #{state_remark, jdbcType=VARCHAR} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{state_cause, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{busi_code_start, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{state_type, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->	
	<delete id="deletePolicyStatusTrace" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="updatePolicyStatusTrace" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_STATUS_TRACE ]]>
		<set>
		<trim suffixOverrides=",">
		    END_DATE = #{end_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			BUSI_CODE_END = #{busi_code_end, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
			STATE_REMARK = #{state_remark, jdbcType=VARCHAR} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			STATE_CAUSE = #{state_cause, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			BUSI_CODE_START = #{busi_code_start, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
			STATE_TYPE = #{state_type, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 按索引查询操作 -->	
	<select id="findPolicyStatusTraceByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BUSI_CODE_END, A.BUSI_PROD_CODE, A.START_DATE, 
			A.STATE_REMARK, A.LIABILITY_STATE, A.POLICY_CODE, A.STATE_CAUSE, A.BUSI_CODE_START, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.STATE_TYPE, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE A WHERE 1 = 1  ]]>
		<include refid="queryPolicyStatusTraceByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="findPolicyStatusTraceByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BUSI_CODE_END, A.BUSI_PROD_CODE, A.START_DATE, 
			A.STATE_REMARK, A.LIABILITY_STATE, A.POLICY_CODE, A.STATE_CAUSE, A.BUSI_CODE_START, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.STATE_TYPE, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE A WHERE 1 = 1  ]]>
		<include refid="queryPolicyStatusTraceByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

	<!-- 按map查询操作 -->
	<select id="findAllMapPolicyStatusTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BUSI_CODE_END, A.BUSI_PROD_CODE, A.START_DATE, 
			A.STATE_REMARK, A.LIABILITY_STATE, A.POLICY_CODE, A.STATE_CAUSE, A.BUSI_CODE_START, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.STATE_TYPE, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE A WHERE ROWNUM <=  1000  ]]>
		<include refid="policyStatusTraceWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询单条操作 -->
	<select id="findPolicyStatusTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BUSI_CODE_END, A.BUSI_PROD_CODE, A.START_DATE, 
			A.STATE_REMARK, A.LIABILITY_STATE, A.POLICY_CODE, A.STATE_CAUSE, A.BUSI_CODE_START, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.STATE_TYPE, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE A   WHERE 1 = 1]]>
		<include refid="policyStatusTraceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findAllPolicyStatusTrace" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_DATE, A.BUSI_CODE_END, A.BUSI_PROD_CODE, A.START_DATE, 
			A.STATE_REMARK, A.LIABILITY_STATE, A.POLICY_CODE, A.STATE_CAUSE, A.BUSI_CODE_START, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.STATE_TYPE, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE A WHERE ROWNUM <=  1000  ]]>
		<include refid="policyStatusTraceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID DESC]]> 
	</select>

	<!-- 查询个数操作 -->
	<select id="findPolicyStatusTraceTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_POLICY_STATUS_TRACE A WHERE 1 = 1  ]]>
		<include refid="policyStatusTraceWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="queryPolicyStatusTraceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.END_DATE, B.BUSI_CODE_END, B.BUSI_PROD_CODE, B.START_DATE, 
			B.STATE_REMARK, B.LIABILITY_STATE, B.POLICY_CODE, B.STATE_CAUSE, B.BUSI_CODE_START, B.LIST_ID, 
			B.BUSI_ITEM_ID, B.STATE_TYPE, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.END_DATE, A.BUSI_CODE_END, A.BUSI_PROD_CODE, A.START_DATE, 
			A.STATE_REMARK, A.LIABILITY_STATE, A.POLICY_CODE, A.STATE_CAUSE, A.BUSI_CODE_START, A.LIST_ID, 
			A.BUSI_ITEM_ID, A.STATE_TYPE, A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="policyStatusTraceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询一条最新的轨迹信息操作 -->
	<select id="findNewPolicyStatusTraceListid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT B.LIST_ID, B.POLICY_CODE, B.BUSI_ITEM_ID,
					       B.STATE_TYPE, B.STATE_CAUSE, B.INSERT_TIME
					  FROM (SELECT A.LIST_ID, A.POLICY_CODE,
					               A.BUSI_ITEM_ID, A.STATE_TYPE,
					               A.STATE_CAUSE, A.INSERT_TIME
					          FROM APP___PAS__DBUSER.T_POLICY_STATUS_TRACE A
					         WHERE 1 = 1 ]]>
		<include refid="policyStatusTraceWhereCondition" />			           
		<![CDATA[            ORDER BY A.INSERT_TIME DESC) B
					 WHERE 1 = 1
					   AND ROWNUM = 1
			  ]]>
	</select>
	
</mapper>
