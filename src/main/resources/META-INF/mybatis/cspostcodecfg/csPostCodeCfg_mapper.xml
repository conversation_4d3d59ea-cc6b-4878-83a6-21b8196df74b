<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.ICsPostCodeCfgDao">

	<sql id="CUS_csPostCodeCfgWhereCondition">
		<if test=" post_code != null and post_code != ''  "><![CDATA[ AND A.POST_CODE = #{post_code} ]]></if>
		<if test=" code != null and code != ''  "><![CDATA[ AND A.CODE = #{code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsPostCodeCfgByCodeCondition">
		<if test=" code != null and code != '' "><![CDATA[ AND B.CODE = #{code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsPostCodeCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_POST_CODE_CFG(
				INSERT_TIMESTAMP, UPDATE_BY, POST_CODE, INSERT_TIME, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY, 
				CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{post_code, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} 
				, #{code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsPostCodeCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_POST_CODE_CFG WHERE CODE = #{code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsPostCodeCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_POST_CODE_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			POST_CODE = #{post_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE 
			
		</trim>
		</set>
		<![CDATA[ WHERE CODE = #{code}]]>
	</update>
<!-- 批量修改操作 -->
	<update id="batchUpdateCsPostCodeCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_POST_CODE_CFG ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			POST_CODE = #{post_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE 
			
		</trim>
		</set>
		<![CDATA[ WHERE CODE = #{code}]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="CUS_findCsPostCodeCfgByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POST_CODE,A.CODE FROM APP___PAS__DBUSER.T_CS_POST_CODE_CFG A WHERE 1 = 1 AND A.CODE = #{code}]]>
	</select>

<!-- 按索引查询操作 -->	
	<select id="findCsPostCodeCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POST_CODE,A.CODE FROM APP___PAS__DBUSER.T_CS_POST_CODE_CFG A WHERE 1 = 1 ]]>
		<include refid="CUS_csPostCodeCfgWhereCondition" />
	</select>
<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsPostCodeCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POST_CODE,A.CODE FROM APP___PAS__DBUSER.T_CS_POST_CODE_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csPostCodeCfgWhereCondition" />
	</select>
	
<!-- 查询个数操作 -->
	<select id="findCsPostCodeCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_DISTRICT B 
		LEFT JOIN DEV_PAS.T_Cs_Post_Code_Cfg A 
		ON A.CODE = B.CODE
		WHERE 1 = 1 AND B.DISTRICT_LEVEL ='3' ]]>
		<include refid="CUS_queryCsPostCodeCfgByCodeCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="queryCsPostCodeCfgForPageNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT C.RN,C.POST_CODE,C.CODE,C.NAME,C.PARENT_CODE,C.DISTRICT_LEVEL,C.NVALID_ADDRESS_FLAG  FROM (
				SELECT ROWNUM RN, A.POST_CODE,B.CODE,B.NAME,B.PARENT_CODE,B.DISTRICT_LEVEL,B.NVALID_ADDRESS_FLAG 
			 FROM DEV_PAS.T_DISTRICT B 
			LEFT JOIN DEV_PAS.T_Cs_Post_Code_Cfg A ON A.CODE = B.CODE WHERE 1=1 AND B.DISTRICT_LEVEL ='3' ]]>
		<include refid="CUS_queryCsPostCodeCfgByCodeCondition" />
		<![CDATA[ order by A.CODE) C
			WHERE C.RN > #{GREATER_NUM} AND C.RN <= #{LESS_NUM} ]]>
			
	</select>
<!-- 查询所有操作 -->
<select id="CUS_findAllCsPostCodeCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POST_CODE,A.CODE FROM APP___PAS__DBUSER.T_CS_POST_CODE_CFG A WHERE ROWNUM <=  1000  ]]>
		<include refid="CUS_csPostCodeCfgWhereCondition" />
	</select>
	
</mapper>
