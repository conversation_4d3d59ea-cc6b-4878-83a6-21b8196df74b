<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsPovCfgDao">
<!--
	<sql id="CUS_csPovCfgWhereCondition">
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" district != null and district != ''  "><![CDATA[ AND A.DISTRICT = #{district} ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND A.STATE = #{state} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" city != null and city != ''  "><![CDATA[ AND A.CITY = #{city} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="CUS_queryCsPovCfgByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CUS_addCsPovCfg"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT S_TRUST_CONTRACT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_CS_POV_CFG(
				INSERT_TIMESTAMP, ORGAN_CODE, DISTRICT, STATE, UPDATE_BY, INSERT_TIME, LIST_ID, 
				UPDATE_TIMESTAMP, UPDATE_TIME, CITY, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{district, jdbcType=VARCHAR} , #{state, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, SYSDATE , #{city, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CUS_deleteCsPovCfg" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_CS_POV_CFG WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CUS_updateCsPovCfg" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_CS_POV_CFG ]]>
		<set>
		<trim suffixOverrides=",">
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			DISTRICT = #{district, jdbcType=VARCHAR} ,
			STATE = #{state, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			CITY = #{city, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CUS_findCsPovCfgByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.DISTRICT, A.STATE, A.LIST_ID, 
			A.CITY FROM T_CS_POV_CFG A WHERE 1 = 1  ]]>
		<include refid="CUS_queryCsPovCfgByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="CUS_findAllMapCsPovCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.DISTRICT, A.STATE, A.LIST_ID, 
			A.CITY FROM T_CS_POV_CFG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="CUS_findAllCsPovCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.DISTRICT, A.STATE, A.LIST_ID, 
			A.CITY FROM DEV_PAS.T_CS_POV_CFG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="CUS_findCsPovCfgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CS_POV_CFG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="CUS_queryCsPovCfgForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ORGAN_CODE, B.DISTRICT, B.STATE, B.LIST_ID, 
			B.CITY FROM (
					SELECT ROWNUM RN, A.ORGAN_CODE, A.DISTRICT, A.STATE, A.LIST_ID, 
			A.CITY FROM T_CS_POV_CFG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 查询个数操作 -->
	<select id="CUS_findCsPovCfgByAgentOrganCode" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_POV_CFG A WHERE 1 = 1  AND A.ORGAN_CODE IS NOT NULL]]>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND #{organ_code} like A.ORGAN_CODE || '%'  ]]></if>
	</select>
	
</mapper>
