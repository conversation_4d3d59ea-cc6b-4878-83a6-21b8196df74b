package GenerateUtil;

import java.util.*;

import junit.framework.TestCase;

import com.nci.udmp.tools.generate.mapper.DBConfig;
import com.nci.udmp.tools.generate.mapper.DaoImplObject;
import com.nci.udmp.tools.generate.mapper.DaoObject;
import com.nci.udmp.tools.generate.mapper.GenerateDaoBean;
import com.nci.udmp.tools.generate.mapper.GenerateDaoImplBean;
import com.nci.udmp.tools.generate.mapper.GenerateJavaObjectBean;
import com.nci.udmp.tools.generate.mapper.GenerateMapperBean;
import com.nci.udmp.tools.generate.mapper.GenerateUtil;
import com.nci.udmp.tools.generate.mapper.JavaObject;
import com.nci.udmp.tools.generate.mapper.MapperObject;

public class GenerateUtilTest extends TestCase {

    /**
     * 初始化数据库参数
     * 
     * @return
     */
    private DBConfig init() {
        String driverClassName = "oracle.jdbc.driver.OracleDriver";
        String url = "**********************************";
        String userName = "APP___PAS__DBUSER";
        String password = "PAS_NAKDFQ#new";
        DBConfig config = new DBConfig(driverClassName, url, userName, password);
        return config;
    }

    private String POpackageName ="com.nci.tunan.pa.interfaces.model.po";//po类包路径
    private String VOpackageName = "com.nci.tunan.pa.interfaces.model.vo";//vo类包路径
    private String tableName = "T_RISK_LEVEL_CONFIG";//表名
    private String POBaseName ="RiskLevelConfigPO";
    private String VOBaseName = "RiskLevelConfigVO";
    private String POClassPath = "com.nci.tunan.pa.interfaces.model.po.";//PO类包全路径
    private String IDaoClassPath = "com.nci.tunan.pa.dao.";//dao接口类包全路径
    private String IDaoPackageName = "com.nci.tunan.pa.dao";//dao接口类包路径
    private String DaoBaseBeanName;
    private String DaoPackageName = "com.nci.tunan.pa.dao.impl.";
    private String implementName = "IRiskLevelConfigDao";
    private String nameSpaceName = "com.nci.tunan.pa.dao.impl";//dao实现类包路径
    private String sequenceName ="S_RISK_LEVEL_CONFIG__LIST_ID";//SEQUENCE名称
    private String primaryKeyColumn = "LIST_ID";//使用SEQUENCE字段名称
    private String systemName;//系统名称
    private String POName;
    private String TablePOJOName;
    private String DaoImplName;
    private String bean;
    private String beanId;
    private void initBean(){
        initBeanId();
        bean= "<bean id=\""+beanId+"\" class=\""+DaoPackageName+DaoImplName+"\" parent=\"baseDao\"></bean>";
    };
    private void initBeanId(){
        beanId= "Cs_"+TablePOJOName+"Dao";
    }
    private void initPOName(){
        POName = TablePOJOName+"PO";
    }
    private void initIDaoName(){
        implementName = "I"+TablePOJOName+"Dao";
    }
    private void initDaoImplName(){
        DaoImplName = TablePOJOName+"DaoImpl";
    }
    private void tableNameToPOName(){
        String tableNameL = tableName.toLowerCase(Locale.ROOT);
        String[] poNames = tableNameL.split("_");
        TablePOJOName = "";
        for(int i = 1; i < poNames.length; i++){
            char[] cs=poNames[i].toCharArray();
            cs[0]-=32;
            TablePOJOName = TablePOJOName+ String.valueOf(cs);
        }
    }

    /**
     * 生成的Mybatis配置文件需要自行添加表前缀。
     */
    @org.junit.Test
    public void testCreateFile(){

        DBConfig config = init();//初始化连接

        List<Map<String,String>> tableList = new ArrayList<>();//需要生成的表清单

        //多表同时生成
/*        Map<String,String> T_CS_DRQ_RESULT = new HashMap<>();
        Map<String,String> T_CS_DRQ_VIDEO_INFO = new HashMap<>();
        Map<String,String> T_CS_DRQ_TASK_TRACE = new HashMap<>();
        Map<String,String> T_CS_DRQ_IMP_ERR = new HashMap<>();
        Map<String,String> T_CS_DRQ_RECHECK = new HashMap<>();


        T_CS_DRQ_RESULT.put("tableName","T_CS_DRQ_RESULT");
        T_CS_DRQ_VIDEO_INFO.put("tableName","T_CS_DRQ_VIDEO_INFO");
        T_CS_DRQ_TASK_TRACE.put("tableName","T_CS_DRQ_TASK_TRACE");
        T_CS_DRQ_IMP_ERR.put("tableName","T_CS_DRQ_IMP_ERR");
        T_CS_DRQ_RECHECK.put("tableName","T_CS_DRQ_RECHECK");


        T_CS_DRQ_RESULT.put("sequenceName","S_CS_DRQ_RESULT");
        T_CS_DRQ_VIDEO_INFO.put("sequenceName","S_CS_DRQ_VIDEO_INFO");
        T_CS_DRQ_TASK_TRACE.put("sequenceName","S_CS_DRQ_TASK_TRACE");
        T_CS_DRQ_IMP_ERR.put("sequenceName","S_CS_DRQ_IMP_ERR");
        T_CS_DRQ_RECHECK.put("sequenceName","S_CS_DRQ_RECHECK");

        T_CS_DRQ_RESULT.put("primaryKeyColumn","LIST_ID");
        T_CS_DRQ_VIDEO_INFO.put("primaryKeyColumn","LIST_ID");
        T_CS_DRQ_TASK_TRACE.put("primaryKeyColumn","LIST_ID");
        T_CS_DRQ_IMP_ERR.put("primaryKeyColumn","LIST_ID");
        T_CS_DRQ_RECHECK.put("primaryKeyColumn","LIST_ID");

        tableList.add(T_CS_DRQ_RESULT);
        tableList.add(T_CS_DRQ_VIDEO_INFO);
        tableList.add(T_CS_DRQ_TASK_TRACE);
        tableList.add(T_CS_DRQ_IMP_ERR);
        tableList.add(T_CS_DRQ_RECHECK);*/

        //单表生成
        Map<String,String> T_CS_ABNORMAL_DR_INFO = new HashMap<>();
        T_CS_ABNORMAL_DR_INFO.put("tableName",tableName);//表名
        T_CS_ABNORMAL_DR_INFO.put("sequenceName",sequenceName);//序列名
        T_CS_ABNORMAL_DR_INFO.put("primaryKeyColumn",primaryKeyColumn);//生成的序列存放字段
        tableList.add(T_CS_ABNORMAL_DR_INFO);

        for (Map<String,String> map:tableList) {
            tableName = map.get("tableName");
            sequenceName = map.get("sequenceName");
            primaryKeyColumn = map.get(primaryKeyColumn);
            tableNameToPOName();
            initPOName();
            initIDaoName();
            initDaoImplName();

            setPOData(config);
            setVOData(config);
            setDaoData(config);
            setDaoImplData(config);
            createMapperFile(config);
            initBean();
        }

    }

    /**
     * 生成javaPO文件，属性以下划线形式
     */
    private List<JavaObject> setPOData(DBConfig config) {
        List<JavaObject> javaObjectList = new ArrayList<JavaObject>();
        Set<String> impSet = new HashSet<String>();
        impSet.add("org.slf4j.Logger");
        JavaObject javaObject = new JavaObject(POpackageName, tableName, impSet, "BasePO");
        javaObjectList.add(javaObject);

            GenerateJavaObjectBean POBean = new GenerateJavaObjectBean(config, javaObjectList);
            GenerateUtil.createPOFile(POBean);

        return javaObjectList;
    }


    /**
     * 生成javaPO文件，属性以下划线形式
     */
    private List<JavaObject> setVOData(DBConfig config ) {
        List<JavaObject> javaObjectList = new ArrayList<JavaObject>();
        Set<String> impSet = new HashSet<String>();
        impSet.add("org.slf4j.Logger");
        JavaObject javaObject = new JavaObject(VOpackageName, tableName, impSet, "BaseVO");
        javaObjectList.add(javaObject);

            GenerateJavaObjectBean VOBean = new GenerateJavaObjectBean(config, javaObjectList);
            GenerateUtil.createVOFile(VOBean);

        return javaObjectList;
    }



    /**
     * 设置生成dao接口的数据
     * 
     * @return
     */
    private List<DaoObject> setDaoData(DBConfig config) {
        List<DaoObject> list = new ArrayList<DaoObject>();
        Set<String> daoset1 = new HashSet<String>();
        daoset1.add(POClassPath+POName);
        daoset1.add(IDaoClassPath+implementName);
        DaoObject dao1 = new DaoObject(IDaoPackageName, tableName, daoset1, "");
        list.add(dao1);

            GenerateDaoBean generateDaoBean = new GenerateDaoBean(config, list);
            GenerateUtil.createDaoFile(generateDaoBean);

        return list;
    }



    /**
     * 设置生成dao实现类的数据
     * 
     * @return
     */
    private List<DaoImplObject> setDaoImplData(DBConfig config ) {
        List<DaoImplObject> list = new ArrayList<DaoImplObject>();

        Set<String> set1 = new HashSet<String>();
        set1.add(POClassPath+POName);
        set1.add(IDaoClassPath+implementName);
        set1.add("com.nci.udmp.framework.dao.impl.BaseDaoImpl");
        DaoImplObject dao1 =
                new DaoImplObject(nameSpaceName,
                        tableName, set1, "BaseDaoImpl",
                        implementName, "");

        list.add(dao1);

            GenerateDaoImplBean generateDaoImplBean = new GenerateDaoImplBean(config, list);
            GenerateUtil.createDaoImplFile(generateDaoImplBean);

        return list;
    }

    public void createMapperFile(DBConfig config) {

        List<MapperObject> mapperObjectList = new ArrayList<MapperObject>();
        MapperObject mapperObject2 = new MapperObject(IDaoClassPath+implementName,
                sequenceName, primaryKeyColumn,
                tableName, "", "CUS");
        mapperObjectList.add(mapperObject2);
        GenerateMapperBean generateMapperBean = new GenerateMapperBean(config, mapperObjectList);

            GenerateUtil.createMapperFile(generateMapperBean);

    }
    /**
     * 生成mappper文件
     */
    @org.junit.Test
    public void testCreateMapperFile() {
        DBConfig config = init();
        List<MapperObject> mapperObjectList = new ArrayList<MapperObject>();
        MapperObject mapperObject2 = new MapperObject(DaoPackageName+DaoImplName,
                sequenceName, primaryKeyColumn,
                tableName, "", "CUS");
        mapperObjectList.add(mapperObject2);
        GenerateMapperBean generateMapperBean = new GenerateMapperBean(config, mapperObjectList);
        try {
            GenerateUtil.createMapperFile(generateMapperBean);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }


    @org.junit.Test
    public void test(){
        List<Map<String,String>> tableList = new ArrayList<>();
        Map<String,String> T_CS_DRQ_RESULT = new HashMap<>();
        Map<String,String> T_CS_DRQ_VIDEO_INFO = new HashMap<>();
        Map<String,String> T_CS_DRQ_TASK_TRACE = new HashMap<>();
        Map<String,String> T_CS_DRQ_IMP_ERR = new HashMap<>();
        Map<String,String> T_CS_DRQ_RECHECK = new HashMap<>();
        Map<String,String> T_CS_ABNORMAL_DR_INFO = new HashMap<>();
        Map<String,String> T_CS_DR_RELATION = new HashMap<>();
        Map<String,String> T_CS_DR_INFO = new HashMap<>();
        T_CS_DR_INFO.put("tableName","T_CS_DR_INFO");
        T_CS_DR_RELATION.put("tableName","T_CS_DR_RELATION");
        T_CS_DRQ_RESULT.put("tableName","T_CS_DRQ_RESULT");
        T_CS_DRQ_VIDEO_INFO.put("tableName","T_CS_DRQ_VIDEO_INFO");
        T_CS_DRQ_TASK_TRACE.put("tableName","T_CS_DRQ_TASK_TRACE");
        T_CS_DRQ_IMP_ERR.put("tableName","T_CS_DRQ_IMP_ERR");
        T_CS_DRQ_RECHECK.put("tableName","T_CS_DRQ_RECHECK");
        T_CS_ABNORMAL_DR_INFO.put("tableName","T_CS_ABNORMAL_DR_INFO");

        T_CS_DRQ_RESULT.put("sequenceName","S_CS_DRQ_RESULT");
        T_CS_DRQ_VIDEO_INFO.put("sequenceName","S_CS_DRQ_VIDEO_INFO");
        T_CS_DRQ_TASK_TRACE.put("sequenceName","S_CS_DRQ_TASK_TRACE");
        T_CS_DRQ_IMP_ERR.put("sequenceName","S_CS_DRQ_IMP_ERR");
        T_CS_DRQ_RECHECK.put("sequenceName","S_CS_DRQ_RECHECK");
        T_CS_ABNORMAL_DR_INFO.put("sequenceName","S_CS_ABNORMAL_DR_INFO");
        T_CS_DR_RELATION.put("sequenceName","S_CS_DR_RELATION");
        T_CS_DR_INFO.put("sequenceName","S_CS_DR_INFO");
        tableList.add(T_CS_DRQ_RESULT);
        tableList.add(T_CS_DRQ_VIDEO_INFO);
        tableList.add(T_CS_DRQ_TASK_TRACE);
        tableList.add(T_CS_DRQ_IMP_ERR);
        tableList.add(T_CS_DRQ_RECHECK);
        tableList.add(T_CS_ABNORMAL_DR_INFO);
        tableList.add(T_CS_DR_RELATION);
        tableList.add(T_CS_DR_INFO);
        for (Map<String,String> map:tableList) {
            tableName = map.get("tableName");
            sequenceName = map.get("sequenceName");
            primaryKeyColumn = "LIST_ID";
            tableNameToPOName();
            initPOName();
            initIDaoName();
            initDaoImplName();
            initBean();
            System.out.println("Bean:"+bean);
        }

    }






}
