package Thread;

import com.nci.udmp.util.threads.ThreadPoolExec;

import java.util.ArrayList;
import java.util.List;

public class CucAcceptThread {

    public static void main(String[] args) throws InterruptedException {
        CucAcceptThread cucAcceptThread = new CucAcceptThread();
        Long s1 = System.currentTimeMillis();
        cucAcceptThread.test1();
        Long s2 = System.currentTimeMillis();
        cucAcceptThread.test2();
        Long s3 = System.currentTimeMillis();

        System.out.println("test1:"+(s2-s1)+"test2:"+(s3-s2));

//        List<PolicyBO> policyBOList = new ArrayList<>();
//        for (int i = 0; i < 1000; i ++){
//            PolicyBO policyBO = new PolicyBO();
//            policyBO.setPolicyCode(i+"");
//            policyBO.setChangeId(i*2+"");
//            policyBOList.add(policyBO);
//        }
//        ThreadPoolExec<PolicyBO,PolicyVO> threadPoolExec = new ThreadPoolExec<PolicyBO,PolicyVO>() {
//            @Override
//            protected PolicyVO bizCall(Object obj) {
//                CusAcceptServiceImpl cusAcceptService = new CusAcceptServiceImpl();
//                PolicyVO policyVO= null;
//                try {
//                    policyVO = cusAcceptService.createIlogPolicy((PolicyBO) obj);
//                    System.out.println();
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                return policyVO;
//            }
//        };
//        System.out.println("policyBOList"+policyBOList.size());
//        List<PolicyVO> policyVOList = threadPoolExec.createThreadPool(policyBOList, 20, 500,"tt");
    }

    public void test1(){
        List<PolicyBO> policyBOList = new ArrayList<>();
        for (int i = 0; i < 1000; i ++){
            PolicyBO policyBO = new PolicyBO();
            policyBO.setPolicyCode(i+"");
            policyBO.setChangeId(i*2+"");
            policyBOList.add(policyBO);
        }
        ThreadPoolExec<PolicyBO,PolicyVO> threadPoolExec = new ThreadPoolExec<PolicyBO,PolicyVO>() {
            @Override
            protected PolicyVO bizCall(Object obj) {
                CusAcceptServiceImpl cusAcceptService = new CusAcceptServiceImpl();
                PolicyVO policyVO= null;
                try {
                    policyVO = cusAcceptService.createIlogPolicy((PolicyBO) obj);
                    System.out.println();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                return policyVO;
            }
        };
        System.out.println("policyBOList"+policyBOList.size());
        List<PolicyVO> policyVOList = threadPoolExec.createThreadPool(policyBOList, 30, 500,"tt");
    }
    public void test2() throws InterruptedException {
        List<PolicyBO> policyBOList = new ArrayList<>();
        CusAcceptServiceImpl cusAcceptService = new CusAcceptServiceImpl();
        for (int i = 0; i < 1000; i ++){
            PolicyBO policyBO = new PolicyBO();
            policyBO.setPolicyCode(i+"");
            policyBO.setChangeId(i*2+"");
            policyBOList.add(policyBO);
            cusAcceptService.createIlogPolicy(policyBO);
        }

    }
}
