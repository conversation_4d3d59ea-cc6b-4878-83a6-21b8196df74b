package Thread;

import com.nci.udmp.util.bean.BeanUtils;

public class CusAcceptServiceImpl {
    public PolicyVO createIlogPolicy(PolicyBO policyBO) throws InterruptedException {
//        return BeanUtils.copyProperties(PolicyVO.class,policyBO);
//        System.out.println(policyBO.getChangeId());
        Thread.sleep(2L);
        return null;
    }
}
class PolicyVO{
    private String policyCode;
    private String changeId;

    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    @Override
    public String toString() {
        return "PolicyBO{" +
                "policyCode='" + policyCode + '\'' +
                ", changeId='" + changeId + '\'' +
                '}';
    }
}
class PolicyBO{
    private String policyCode;
    private String changeId;

    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    @Override
    public String toString() {
        return "PolicyBO{" +
                "policyCode='" + policyCode + '\'' +
                ", changeId='" + changeId + '\'' +
                '}';
    }
}