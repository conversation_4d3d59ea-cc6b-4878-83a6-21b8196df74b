package service;

import java.math.BigDecimal;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import testsupport.BaseTest;

import com.nci.core.common.boservice.IAgentOperBOService;
import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.model.bo.AgentBO;
import com.nci.core.common.interfaces.model.bo.AgentCompBO;
import com.nci.core.common.interfaces.model.bo.SalesOrganBO;

/** 
 * @description 代理人操作测试
 * @<NAME_EMAIL> 
 * @date 2015年6月3日 下午5:35:53  
*/
@ContextConfiguration(locations={"classpath*:META-INF/common/spring/applicationContext.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class AgentOperBOServiceTest extends BaseTest{
    
    private IAgentOperBOService agentservice;
    @Before
    public void setUp(){
        agentservice=BOServiceFactory.getAgentOperService();
    }

    @Test
    public void testFindAllAgent() {
        AgentCompBO agentBO = new AgentCompBO();
        List<AgentCompBO> agentlist = agentservice.findAllAgent(agentBO);
        for(AgentCompBO agent:agentlist){
            logger.debug("agentName ="+agent.getAgentBO().getAgentName());
        }
    }

    /**
     * @description 根据AgentCode查代理人信息
     * @<NAME_EMAIL> 
    */
    @Test
    public void testFindAgentByAgentCode() {
        AgentCompBO agentcomBO = new AgentCompBO();
        AgentBO agentbo = new AgentBO();
        agentbo.setAgentCode("36082616");
        agentcomBO.setAgentBO(agentbo);
        AgentCompBO agentcomBack = agentservice.findAgentByAgentCode(agentcomBO);
        logger.debug(agentcomBack.getAgentBO().getAgentName());
//        List<AgentLicenseBO> licencelist=agentcomBack.getAgentLicenseBOList();
//        logger.debug(licencelist.get(0).getLicenseNo());
    }

    /**
     * @description 通过代理人类型和销售机构查询代理人信息
     * (AGENT_TYPE、AGNET_SALES_ORGAN_CODE)。 (销售类型主要包含个人、续期等)
     * @<NAME_EMAIL> 
    */
    @Test
    public void testFindAgentByTypeAndSOrgan() {
        AgentCompBO agentcomBO = new AgentCompBO();
        AgentBO agentBO = new AgentBO();
        agentBO.setAgnetSalesOrganCode("");
        agentBO.setAgentNormalType(new BigDecimal(3));
        SalesOrganBO salesOrganBO = new SalesOrganBO();
//        salesOrganBO.setSalesOrganCode("360000154119");
        agentcomBO.setSalesOrganBO(salesOrganBO);
        agentcomBO.setAgentBO(agentBO);
        List<AgentCompBO> agentCompBOlist = agentservice.findAgentByTypeAndSOrgan(agentcomBO);
        for(AgentCompBO bo:agentCompBOlist){
            logger.debug(bo.getAgentBO().getAgentName());
        }
    }

    /**
     * @description 根据机构代码、代理人登记查询代理人信息AGNET_ORGAN_CODE、AGENT_LEVEL
     * @<NAME_EMAIL> 
    */
    @Test
    public void testFindAgentByOrganLevel() {
        AgentCompBO agentcomBO = new AgentCompBO();
        AgentBO agentBO = new AgentBO();
        agentBO.setAgnetOrganCode("86210001");
//        agentBO.setAgentLevel("4");
        agentcomBO.setAgentBO(agentBO);
        List<AgentCompBO> agentCompBOlist = agentservice.findAgentByOrganLevel(agentcomBO);
        for(AgentCompBO bo:agentCompBOlist){
            logger.debug(bo.getAgentBO().getAgentName()+bo.getAgentBO().getAgnetOrganCode());
        }
    }

    /**
     * @description 通过代理人区部组查询代理人信息(ORGAN_LEVEL_CODE)
     *  District Department of group
     * @version
     * @title
     * @<NAME_EMAIL> 
    */
    @Test
    public void testFindAgentByDDOG() {
        AgentCompBO agentcomBO = new AgentCompBO();
        AgentBO agentBO = new AgentBO();
//        agentBO.setAgnetOrganCode("86210001");
//        agentBO.setAgentLevel("4");
        agentcomBO.setAgentBO(agentBO);
        List<AgentCompBO> agentCompBOlist = agentservice.findAgentByDDOG(agentcomBO);
        for(AgentCompBO bo:agentCompBOlist){
            logger.debug(bo.getAgentBO().getAgentName()+bo.getAgentBO().getAgnetOrganCode());
        }
    }

    @Test
    public void testDeleteAgentByAgentCode() {
        AgentCompBO agentcomBO = new AgentCompBO();
        AgentBO agentbo = new AgentBO();
        agentbo.setAgentCode("1234"); //"1234"不存在会报错
        agentcomBO.setAgentBO(agentbo);
        agentservice.deleteAgentByAgentCode(agentcomBO);
    }

    @Test
    public void testUpdateSalesOrganByAgentCode() {
        AgentCompBO agentcomBO = new AgentCompBO();
        AgentBO agentbo = new AgentBO();
        agentbo.setAgentCode("36007827");
        agentbo.setAgentEmail("<EMAIL>");
        agentcomBO.setAgentBO(agentbo);
        agentservice.updateSalesOrganByAgentCode(agentcomBO);
    }

    @Test
    public void testUpdateByAgentCodeLicType() {
        agentservice.updateByAgentCodeLicType(null);
    }

    @Test
    public void testUpdateByAgentCodeProdCode() {
        agentservice.updateByAgentCodeProdCode(null);
    }
    
//    private AgentCompBO getAgentCompBO() {
//        AgentCompBO agentcomBO = new AgentCompBO();
//        AgentBO agentbo = new AgentBO();
//        agentbo.setAgentCode("36007827");
//        agentcomBO.setAgentBO(agentbo);
//        return agentcomBO;
//    }

}
