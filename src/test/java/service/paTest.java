package service;

import java.math.BigDecimal;

import javax.servlet.ServletInputStream;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import testsupport.BaseTest;

import com.nci.tunan.cs.impl.csConfig.service.IReviewPointCfgService;
import com.nci.tunan.cs.model.bo.ReviewPointCfgBO;
import com.nci.tunan.pa.dao.IContractProductDao;
import com.nci.tunan.pa.interfaces.model.po.ContractProductPO;

/** 
 * @description 
 * @<NAME_EMAIL> 
 * @date 2015年8月25日 下午6:44:34  
*/
public class paTest extends BaseTest{
//    private IReviewPointCfgService reviewPointCfgService ;
    
    private IContractProductDao contractProductDao;

    @Before
    public void setUp() throws Exception {
        super.setupAbstractTest();
//        reviewPointCfgService = (IReviewPointCfgService)getBean("reviewPointCfgService");
        contractProductDao = (IContractProductDao)getBean("contractProductDao");
    }
    
//    @Test
//    public void testFindAllReviewPointCfg() {
//        System.out.println("asdfasdfasfdasdfs");
//    }
    
    @Test
    public void testProduct(){
    	ContractProductPO contractProductPO = new ContractProductPO();
    	contractProductPO.setPolicyId(new BigDecimal(2));
    	contractProductDao.findAllContractProduct(contractProductPO);
    }
    
}
