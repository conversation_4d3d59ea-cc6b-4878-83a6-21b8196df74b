package testsupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.junit.Before;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import com.nci.udmp.app.ucc.impl.LoginUCC;
import com.nci.udmp.framework.context.AppUser;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.dao.IBaseDao;
import com.nci.udmp.framework.spring.SpringContextUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/** 
 * @description 重写UDMP抽象测试类AbstractTest
 * @<NAME_EMAIL> 
 * @date 2015年6月5日 下午3:59:11  
*/
@ContextConfiguration(locations = { "classpath*:META-INF/spring/applicationContext.xml","classpath*:META-INF/pa/spring/applicationContext*.xml","classpath*:META-INF/cs/spring/applicationContext*.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public abstract class BaseTest {
    public static final  String UNIT_TEST_USER = "SYSADMIN";
    protected TransactionTemplate transactionTemplate;
    protected JdbcTemplate jdbcTemplate;
    protected DataSource dataSource;
    protected IBaseDao baseDao;
    protected Logger logger = LoggerFactory.getLogger();
    protected ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private ApplicationContext context;

    /**
     */
    /**
     * @description 获取Bean
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param name bean名称
     * @return  SpringBean
    */
    public Object getBean(String name) {
        if (context == null) {
            context = SpringContextUtils.getApplicationContext();
        }
        return context.getBean(name);
    }

    @Resource(name = "dataSource")
    public void setDataSource(DataSource dataSource) {
        this.dataSource = dataSource;
        this.jdbcTemplate = new JdbcTemplate(dataSource);
    }

    @Resource(name = "threadPoolTaskExecutor")
    public void setTaskExecutor(ThreadPoolTaskExecutor threadPoolTaskExecutor1) {
        this.threadPoolTaskExecutor = threadPoolTaskExecutor1;
    }

    @Autowired
    public void setTransactionManager(PlatformTransactionManager transactionManager) { // 娉ㄥ叆TransactionManager
        // 浠ユ敞鍏ョ殑TransactionManager浣滀负鍙傛暟锛岃幏鍙栦竴涓猅ransactionTemplate瀹炰緥锛岃瀹炰緥灏佽浜咹ibernate鐨勮涓�
        this.transactionTemplate = new TransactionTemplate(transactionManager); 

    }

    /**
     * @description 获取测试用户名
     * @<NAME_EMAIL>
     * @return 测试用户名 
    */
    protected String getTestUserName() {
        return UNIT_TEST_USER;
    }

    /**
     * @description 绑定用户模拟登陆
     * @version
     * @title
     * @<NAME_EMAIL> 
    */
    @Before
    public void setupAbstractTest() {
//        bindAppUser(); //为节省测试时间暂时注释
        baseDao = (IBaseDao) getBean("baseDao");
    }

    /**
     * @description Bind current user to current thread
     * @version
     * @title
     * @<NAME_EMAIL>
     */
    private void bindAppUser() {
        String userName = getTestUserName();
        LoginUCC ucc = (LoginUCC) getBean("loginUCC");
        // 鍒涘缓涓�釜鐧诲綍鐢ㄦ埛鐨勫璞�
        AppUser user = ucc.findAppUser(userName);

        AppUserContext.setCurrentUser(user);
    }

}
