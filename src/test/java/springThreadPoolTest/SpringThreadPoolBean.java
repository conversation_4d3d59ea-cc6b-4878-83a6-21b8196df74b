package springThreadPoolTest;

import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @description SpringThreadPoolTest
 * @.belongToModule cs-保全子系统
 * @date 2023年04月20日  15:47
 */
public class SpringThreadPoolBean {
   
    private ThreadPoolTaskExecutor threadPoolTaskExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //获取可用处理器的Java虚拟机的数量（未必能准确获取到CPU核心数量）
        int core = Runtime.getRuntime().availableProcessors();
        //核心线程数
        executor.setCorePoolSize(core);
        //最大线程数
        executor.setMaxPoolSize(core*2+1);
        //除核心线程外的线程存活时间
        executor.setKeepAliveSeconds(10);
        //等待队列数量
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("ThreadExecutor-");//设置线程前缀名称
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());//设置拒绝策略（线程不够用时由调用的线程处理该任务）
        /**
         *停机策略：该方法用来设置线程池关闭的时候等待所有任务都完成后，再继续销毁其他的Bean
         * 这样这些异步任务的销毁就会先于数据库连接池对象的销毁。 
         */
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //任务的等待时间如果 超过这个时间还没有销毁就强制销毁，以确保应用最后能够被关闭，而不是阻塞
        executor.setAwaitTerminationSeconds(60);
        
        return executor;
    }
    
}
