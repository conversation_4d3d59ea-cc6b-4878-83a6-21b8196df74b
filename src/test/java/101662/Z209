import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.dao.ICsContractProductDao;
import com.nci.tunan.cs.dao.ICsPolicyChangeDao;
import com.nci.tunan.cs.model.po.CsContractProductPO;
import com.nci.tunan.cs.model.po.CsCustomerPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.pa.dao.IContractMasterDao;
import com.nci.tunan.pa.dao.IContractProductDao;
import com.nci.tunan.pa.dao.ICustomerDao;
import com.nci.tunan.pa.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.ContractProductPO;
import com.nci.tunan.pa.interfaces.model.po.CustomerPO;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.DateUtilsEx;
import org.apache.struts2.components.Bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class Z209{
    
    private IContractMasterDao contractMasterDao;
    
    private ICsPolicyChangeDao csPolicyChangeDao;
    private IContractProductDao contractProductDao;
    private ICsContractProductDao csContractProductDao;
    private ICustomerDao customerDao;

    public static void main(String[] args) {

    }
    
    public BigDecimal calZ209(BigDecimal customerId){
        List<ContractMasterPO> contractMasterPOS = queryInsuredListPolicy(customerId);
        BigDecimal z209 = BigDecimal.ZERO;
        for (ContractMasterPO contractMasterPO:contractMasterPOS) {

            /**
             * 1、先查出被保人保单
             * 2、区分出保全在途与非保全在途
             * 3、累计非保全在途保单
             * 4、累计保全在途保单
             */
            if(isBq(contractMasterPO)){
                CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
                csPolicyChangePO.setPolicyCode(contractMasterPO.getPolicyCode());
                //查询保单是否保全在途
                csPolicyChangePO = csPolicyChangeDao.findCsPolicyChangeByIsBQ(csPolicyChangePO);
                CsContractProductPO csContractProductPO = new CsContractProductPO();
                csContractProductPO.setPolicyChgId(csPolicyChangePO.getPolicyChgId());
                csContractProductPO.setOldNew(Constants.NEW);
                List<CsContractProductPO> csContractProductPOS = csContractProductDao.findAllCsContractProduct(csContractProductPO);
                for (CsContractProductPO csContractProductPO1:csContractProductPOS) {
                    ContractProductPO contractProductPO = BeanUtils.copyProperties(ContractProductPO.class,csContractProductPO);
                    z209 = z209.add(sumZ209(contractProductPO,customerId));
                }
            }else {
                ContractProductPO contractProductPO = new ContractProductPO();
                contractProductPO.setPolicyId(contractMasterPO.getPolicyId());
                List<ContractProductPO> contractProductPOS = contractProductDao.findAllContractProduct(contractProductPO);
                for (ContractProductPO contractProductPO1:contractProductPOS) {
                    z209 = z209.add(sumZ209(contractProductPO1,customerId));
                }
            }
        }
        return z209;
    }

    /**
     * 汇总
     * @param contractProductPO
     * @return
     */
    private BigDecimal sumZ209(ContractProductPO contractProductPO,BigDecimal customerId ){
        BigDecimal result = BigDecimal.ZERO;
        switch (contractProductPO.getProductCode()){//456、438、441、448、804、925、807
            case "456000":result = calPayTotalPremAf(contractProductPO,customerId);
            case "438000":result = calPayTotalPremAf(contractProductPO,customerId);
            case "441000":result = calPayTotalPremAf(contractProductPO,customerId);
            case "448000":result = calPayTotalPremAf(contractProductPO,customerId);
            case "804000":result = calPayTotalPremAf(contractProductPO,customerId);
            case "928000":result = calPayTotalPremAf(contractProductPO,customerId);
            case "807000":result = calPayTotalPremAf(contractProductPO,customerId);
            //926、927
            case "926000":result = calPayTotalPrem(contractProductPO);
            case "927000":result = calPayTotalPrem(contractProductPO);
        }
        return result;
    }
    /**
     * 查询被保人下所有保单
     * @param customerId
     * @return
     */
    private List<ContractMasterPO> queryInsuredListPolicy(BigDecimal customerId){
        List<ContractMasterPO> contractMasterPOS = new ArrayList<ContractMasterPO>();
        ContractMasterPO contractMasterPOQuery = new ContractMasterPO();
        contractMasterPOQuery.setBigDecimal("customer_id",customerId);
        //查询被保人下所有保单
        contractMasterPOS =  contractMasterDao.findInsuredPolicys(contractMasterPOQuery);
        return contractMasterPOS;
    }

    /**
     * 是否保全在途
     * @param contractMasterPO
     * @return
     */
    private boolean isBq(ContractMasterPO contractMasterPO){
        CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
        csPolicyChangePO.setPolicyCode(contractMasterPO.getPolicyCode());
        //查询保单是否保全在途
        csPolicyChangePO = csPolicyChangeDao.findCsPolicyChangeByIsBQ(csPolicyChangePO);
        if(null != csPolicyChangePO && csPolicyChangePO.getPolicyChgId() != null){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 计算累计应交保费
     * @param contractProductPO
     * @return
     */
    private BigDecimal calPayTotalPremAf(ContractProductPO contractProductPO,BigDecimal customerId){
     BigDecimal payTotalPremAf = BigDecimal.ZERO;
        if(contractProductPO.getChargePeriod().equals("0")){//@invalid 无关
            payTotalPremAf = getPremFreqAmount(contractProductPO.getPremFreq(), contractProductPO.getStdPremAf()).multiply(contractProductPO.getChargeYear());
        }else if(contractProductPO.getChargePeriod().equals("1")){//@invalid 趸交
            payTotalPremAf = contractProductPO.getTotalPremAf();
        }else if (contractProductPO.getChargePeriod().equals("2")){//@invalid 按年限交
            payTotalPremAf = getPremFreqAmount(contractProductPO.getPremFreq(), contractProductPO.getStdPremAf()).multiply(contractProductPO.getChargeYear());
        }else if (contractProductPO.getChargePeriod().equals("3")){//@invalid 交至某确定年龄
            CustomerPO customerPO = new CustomerPO();
            customerPO.setCustomerId(customerId);
            customerPO = customerDao.findCustomer(customerPO);
            int age = DateUtilsEx.getAge(customerPO.getCustomerBirthday(), contractProductPO.getValidateDate());
            payTotalPremAf = getPremFreqAmount(contractProductPO.getPremFreq(), contractProductPO.getStdPremAf()).multiply(contractProductPO.getChargeYear().subtract(new BigDecimal(age)));
        }else if (contractProductPO.getChargePeriod().equals("4")){//@invalid 终身交费
            payTotalPremAf = contractProductPO.getTotalPremAf();
        }else if (contractProductPO.getChargePeriod().equals("5")){//@invalid 不定期交
            payTotalPremAf = contractProductPO.getTotalPremAf();
        }else if (contractProductPO.getChargePeriod().equals("6")){//@invalid 按月限交
            payTotalPremAf = getPremFreqAmount(contractProductPO.getPremFreq(), contractProductPO.getStdPremAf()).multiply(contractProductPO.getChargeYear());
        }else if (contractProductPO.getChargePeriod().equals("7")){//@invalid 按天限交
            payTotalPremAf = getPremFreqAmount(contractProductPO.getPremFreq(), contractProductPO.getStdPremAf()).multiply(contractProductPO.getChargeYear());
        }
     return payTotalPremAf;
    }

    /**
     * 计算已交保费
     * @param contractProductPO
     * @return
     */
    private BigDecimal calPayTotalPrem(ContractProductPO contractProductPO){
        BigDecimal payTotalPrem = BigDecimal.ZERO;
        payTotalPrem = contractProductPO.getTotalPremAf();
        return payTotalPrem;
    }

    /**
     *
     * @description 算一个缴费年期需要交的总保费
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param premFreq 交费频率
     * @param stdPremAf 标准保费
     * @return BigDecimal 处理结果
     */
    private BigDecimal getPremFreqAmount(BigDecimal premFreq,BigDecimal stdPremAf){
        BigDecimal total = stdPremAf;
        if (premFreq.compareTo(new BigDecimal(2)) == 0) { //@invalid  月缴
            total = stdPremAf.multiply(new BigDecimal(12));
        } else if (premFreq.compareTo(new BigDecimal(4)) == 0) { //@invalid  季缴
            total = stdPremAf.multiply(new BigDecimal(12));
        } else if (premFreq.compareTo(new BigDecimal(2)) == 0) { //@invalid  半年缴
            total = stdPremAf.multiply(new BigDecimal(12));
        } else if (premFreq.compareTo(new BigDecimal(5)) == 0) { //@invalid  年缴
            total = stdPremAf.multiply(new BigDecimal(1));
        }
        return total;
    }
}

