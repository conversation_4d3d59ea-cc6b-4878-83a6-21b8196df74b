package test.tool;

import com.nci.core.common.impl.util.BizToolUtils;

import weblogic.utils.encoders.BASE64Decoder;

public class textXML {
    public static void main(String args[]){
        String str = ""+"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KCjxEQVRBU0VUUz4KICAgIDwh"
            +"LS1UaGlzIGZpbGUgd2FzIGNyZWF0ZWQgb24gMjAxNi0xMC0yNSAxMDo0NTowMC0tPgogICAgPERB"
            +"VEFTRVQ+CiAgICAgICAgPENPTlRST0w+CiAgICAgICAgICAgIDxKT0JUWVBFPjE8L0pPQlRZUEU+"
            +"CiAgICAgICAgICAgIDxGb3JtSUQ+Q1VTXzAwMDAzPC9Gb3JtSUQ+CiAgICAgICAgICAgIDxVc2Vy"
            +"****************************************************************************"
            +"ICAgICAgICAgICA8U2FsZUNobmw+MTwvU2FsZUNobmw+CiAgICAgICAgICAgIDxJU1N0b3JhZ2U+"
            +"MTwvSVNTdG9yYWdlPgogICAgICAgICAgICA8U3RvcmFnZU5PPjAwMDAwMDAwMTE8L1N0b3JhZ2VO"
            +"Tz4KICAgICAgICAgICAgPElTQ0E+MTwvSVNDQT4KICAgICAgICAgICAgPExvc3RUaW1lcz4xPC9M"
            +"b3N0VGltZXM+CiAgICAgICAgICAgIDxQcmludERhdGU+MjAxNuW5tDEw5pyIMjXml6U8L1ByaW50"
            +"RGF0ZT4KICAgICAgICAgICAgPEFkZHJlc3M+54ix5Zu96LevNzg55Y+3PC9BZGRyZXNzPgogICAg"
            +"ICAgICAgICA8UG9zdENvZGU+MTM1Nzg2PC9Qb3N0Q29kZT4KICAgICAgICAgICAgPFJlY2lwaWVu"
            +"dE5hbWU+bm1zamtsNzg5PC9SZWNpcGllbnROYW1lPgogICAgICAgICAgICA8QnVzc1NvdXJjZUNv"
            +"ZGU+W0JNX0J1c3NTb3VyY2VDb2RlXTwvQnVzc1NvdXJjZUNvZGU+CiAgICAgICAgICAgIDxUZW1w"
            +"bGF0ZUNvZGU+W0JNX1RlbXBsYXRlQ29kZV08L1RlbXBsYXRlQ29kZT4KICAgICAgICAgICAgPEJ1"
            +"c3NDb2RlPltCTV9CdXNzQ29kZV08L0J1c3NDb2RlPgogICAgICAgICAgICA8SW5mbz4KICAgICAg"
            +"ICAgICAgICAgIDxFY21CdXNpbmVzc0NvZGU+NjAyMDE2MTAyNTQzNzU5NTwvRWNtQnVzaW5lc3ND"
            +"b2RlPgogICAgICAgICAgICAgICAgPEJ1c2lUeXBlTmFtZT5icTwvQnVzaVR5cGVOYW1lPgogICAg"
            +"ICAgICAgICAgICAgPEJ1c2lPcmdDb2RlPjg2NTE8L0J1c2lPcmdDb2RlPgogICAgICAgICAgICAg"
            +"ICAgPEVjbUNhdGFsb2dDb2RlPltCTV9lY21DYXRhbG9nQ29kZV08L0VjbUNhdGFsb2dDb2RlPgog"
            +"ICAgICAgICAgICAgICAgPFBhZ2VOYW1lPltCTV9wYWdlTmFtZV08L1BhZ2VOYW1lPgogICAgICAg"
            +"ICAgICA8L0luZm8+CiAgICAgICAgPC9DT05UUk9MPgogICAgICAgIDxEQVRBPgogICAgICAgICAg"
            +"ICA8RG9jdW1lbnRObz42MDIwMTYxMDI1NDM3NTk1PC9Eb2N1bWVudE5vPgogICAgICAgICAgICA8"
            +"Q3VzdFNlcnZpY2VUZWw+OTU1Njc8L0N1c3RTZXJ2aWNlVGVsPgogICAgICAgICAgICA8RWRpdGlv"
            +"bk5vPjEuMDwvRWRpdGlvbk5vPgogICAgICAgICAgICA8T2ZmaWNpYWxXZWJzaXRlPnd3dy5uZXdj"
            +"aGluYWxpZmUuY29tPC9PZmZpY2lhbFdlYnNpdGU+CiAgICAgICAgICAgIDxQcmludERhdGU+MjAx"
            +"Ni0xMC0yNTwvUHJpbnREYXRlPgogICAgICAgICAgICA8UG9saWN5Tm8+OTkwMDAzMTk1ODM5PC9Q"
            +"b2xpY3lObz4KICAgICAgICAgICAgPENVU0FjY2VwdE5vPno2MTIwMTYxMDI1NDM3NTk0Mjk8L0NV"
            +"U0FjY2VwdE5vPgogICAgICAgICAgICA8QXBwbGljYW50TmFtZT5ubXNqa2w3ODk8L0FwcGxpY2Fu"
            +"dE5hbWU+CiAgICAgICAgICAgIDxJbnN1cmVkTmFtZT56aHVsaDwvSW5zdXJlZE5hbWU+CiAgICAg"
            +"ICAgICAgIDxDVVNFZmZlY3REYXRlPjIwMTYtMTAtMzA8L0NVU0VmZmVjdERhdGU+CiAgICAgICAg"
            +"ICAgIDxQcmluY2lwYWw+PC9QcmluY2lwYWw+CiAgICAgICAgICAgIDxTYWxlU2VydmljZT48L1Nh"
            +"bGVTZXJ2aWNlPgogICAgICAgICAgICA8U2FsZURlcE5hbWU+PC9TYWxlRGVwTmFtZT4KICAgICAg"
            +"ICAgICAgPFNlcnZpY2VOYW1lPjwvU2VydmljZU5hbWU+CiAgICAgICAgICAgIDxTZXJ2aWNlQ29k"
            +"ZT48L1NlcnZpY2VDb2RlPgogICAgICAgICAgICA8RmlsaWFsZU5hbWU+MzUt5paw5Y2O5Lq65a+/"
            +"5L+d6Zmp6IKh5Lu95pyJ6ZmQ5YWs5Y+45oC75YWs5Y+4PC9GaWxpYWxlTmFtZT4KICAgICAgICAg"
            +"ICAgPENvbXBhbnlBZGRyZXNzPjwvQ29tcGFueUFkZHJlc3M+CiAgICAgICAgICAgIDxDVVNJdGVt"
            +"Q29kZT5DVzwvQ1VTSXRlbUNvZGU+CiAgICAgICAgICAgIDxDVVNJdGVtTmFtZT7kv53ljZXop6Pl"
            +"hrs8L0NVU0l0ZW1OYW1lPgogICAgICAgIDwvREFUQT4KICAgIDwvREFUQVNFVD4KPC9EQVRBU0VU"
            +"Uz4K";
        BASE64Decoder decoder = new BASE64Decoder();
       try {  
        byte[] b = decoder.decodeBuffer(str.toString());  
        String str2 = new String(b);
        System.out.println(str2);  
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
       
}
