package test.tool;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.nci.tunan.cs.common.Constants;
import com.nci.udmp.component.ftp.config.FtpServerConfig;
import com.nci.udmp.framework.exception.FrameworkRuntimeException;
import com.nci.udmp.util.ftp.FtpUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;

public class text {

	static Map<Integer,BigDecimal> retMap = new HashMap<Integer,BigDecimal>();
	public FTPClient ftpClient = null;



	public static void main(String[] args) {
		//
		/**
		 * DRQTaskExtractJob.ftpServerIp = **********
		 * DRQTaskExtractJob.ftpUserName = ncivcszj
		 * DRQTaskExtractJob.ftpPassword = ncivcszj@1234
		 * DRQTaskExtractJob.ftpServerPort = 1358
		 * DRQTaskExtractJob.ftpEncoding = UTF-8
		 * DRQTaskExtractJob.ftpBufferSize = 10240000
		 * DRQTaskExtractJob.ftpServerWorkingDirectory = /pas/ncivcs/policyInfo/senddata
		 * DRQTaskExtractJob.ftpServerBackupsDirectory = /pas/ncivcs/policyInfo/senddata_bak
		 */
		text t = new text();
		FtpServerConfig ftpServerConfig = new FtpServerConfig();
		ftpServerConfig.setFtpServerIp("**********");
		ftpServerConfig.setFtpUserName("ncivcszj");
		ftpServerConfig.setFtpPassword("ncivcszj@1234");
		ftpServerConfig.setFtpServerPort(1358);
		ftpServerConfig.setFtpEncoding("UTF-8");
		ftpServerConfig.setFtpBufferSize(10240000);
		ftpServerConfig.setFtpServerWorkingDirectory("/pas/ncivcs/policyInfo");
		FTPClient ftpClient = t.loginFtpServer(ftpServerConfig);
		try {
			ftpClient.changeWorkingDirectory(ftpServerConfig.getFtpServerWorkingDirectory());//切换当前目录
			//ftpClient.makeDirectory(ftpServerConfig.getFtpServerWorkingDirectory()+"/c");
			ftpClient.rename(ftpServerConfig.getFtpServerWorkingDirectory()+"/a.txt",ftpServerConfig.getFtpServerWorkingDirectory()+"/b.txt");//更名
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			t.logoutFtpServer(ftpClient);
		}
	}

	public FTPClient loginFtpServer(FtpServerConfig ftpServerConfig) {
		FTPClient ftpClient = new FTPClient();

		try {
			if (ftpServerConfig.getFtpServerPort() > 0) {
				ftpClient.connect(ftpServerConfig.getFtpServerIp(), ftpServerConfig.getFtpServerPort());
			} else {
				ftpClient.connect(ftpServerConfig.getFtpServerIp());
			}

			ftpClient.login(ftpServerConfig.getFtpUserName(), ftpServerConfig.getFtpPassword());
			int reply = ftpClient.getReplyCode();
			ftpClient.setDataTimeout(120000);
			ftpClient.setControlEncoding("UTF-8");
			if (!FTPReply.isPositiveCompletion(reply)) {
				return null;
			}

			ftpClient.setFileType(2);
			ftpClient.setBufferSize(ftpServerConfig.getFtpBufferSize());
		} catch (IOException var4) {
			throw new FrameworkRuntimeException(" login ftp server failed , the error info is : " + var4.getMessage());
		}

		this.ftpClient = ftpClient;
		return ftpClient;
	}

	public boolean logoutFtpServer(FTPClient ftpClientlo) {
		if (null == ftpClientlo) {
			throw new FrameworkRuntimeException(" the ftpClient is null !");
		} else {
			try {
				ftpClientlo.logout();
			} catch (IOException var10) {
				throw new FrameworkRuntimeException("  exit ftp login and close ftp connection failed ,the error info is : " + var10.getMessage());
			} finally {
				if (ftpClientlo.isConnected()) {
					try {
						ftpClientlo.disconnect();
					} catch (IOException var9) {
						throw new FrameworkRuntimeException(" close ftp connection failed ,the error info is : " + var9.getMessage());
					}
				}

			}

			return true;
		}
	}



	/**
	 * 
	 * capitalBalance 累计欠缴保费
	 * stdPrem   每期保费
	 * startDate X年宽限期结束
	 * endDate  当前时点
	 * policyYear 保单会计年度开始日期（保单缴费对应日）
	 */
	public static BigDecimal re(BigDecimal capitalBalance,BigDecimal stdPrem,Date startDate,Date endDate,Date policyYear){
		
		/*上一年度累计欠缴保费*/
		BigDecimal beforeYearBal=BigDecimal.ZERO;
		
		int starYear=DateUtilsEx.getYear(startDate);
		int endYear=DateUtilsEx.getYear(endDate);
		
		BigDecimal rate=BigDecimal.ZERO;//利率默认值
		Date mathStartDate=startDate;//计算的开始日期
		Date mathEndDate=endDate;//计算的开始日期
		int mathYear=0;//计算年份
		BigDecimal days=Constants.DAY_OF_YEAR_BIGDECIMAL_365;//计算天数 365或则366
		BigDecimal sumInterest=BigDecimal.ZERO;//利息和
		while (starYear<=endYear) {
			mathYear=starYear;
			if(mathYear%4==0&&mathYear%100!=100||mathYear%400==0){
				days=Constants.DAY_OF_YEAR_BIGDECIMAL_366;
			}else{
				days=Constants.DAY_OF_YEAR_BIGDECIMAL_365;
			}
			//rate=BigDecimal.ZERO;//利率默认值
			if(retMap.get(mathYear)!=null){
				rate=retMap.get(mathYear).setScale(Constants.FOURInt, BigDecimal.ROUND_HALF_UP);//取出计算年份利率
			}
			
			Date lastDate = getYearLast(mathYear);//获取计算年度的最后一天日期
			Date firstDate = getYearFirst(mathYear);//获取计算年度的第一天
			Calendar cal = Calendar.getInstance();
			cal.setTime(policyYear);
			cal.set(Calendar.YEAR, mathYear);
			mathStartDate=cal.getTime();
			
			
			
			
			
			
			
			/*计算本年度欠交保费
			 * 上年累积欠交保费*（1+当年度复效利率）+本年欠交保费*（1+当年度复效利率*(本年会计年度末-本年的缴费对应日）/365)
			 * 
			 * */
			BigDecimal capital=BigDecimal.ZERO;//计算所用本金
			//如果上年度欠交保费为零，则为欠交首年，计算欠计算本年欠交保费使用累计欠交保费,否则使用每期保费
			if(beforeYearBal.compareTo(BigDecimal.ZERO)==0){
				capital=capitalBalance;
				Calendar cal1 = Calendar.getInstance();
				cal.setTime(startDate);
				cal.set(Calendar.YEAR, mathYear);
				mathStartDate=cal.getTime();
			}else{
				capital=stdPrem;
			}
			/*if(starYear==DateUtilsEx.getYear(startDate)){
					mathStartDate=cal.getTime();
				}else{
					mathStartDate=firstDate;
				}*/
			if(starYear==DateUtilsEx.getYear(endDate)){
				cal.setTime(endDate);
				cal.set(Calendar.YEAR, mathYear);
				mathEndDate=cal.getTime();
				capital=BigDecimal.ZERO;
				lastDate=mathEndDate;
			}else{
				mathEndDate=lastDate;
			}
			
			
			
			
			BigDecimal dayeca = new BigDecimal (DateUtilsEx.getDayAmount(firstDate, lastDate)).add(BigDecimal.ONE);
			
			BigDecimal dayecb = new BigDecimal (DateUtilsEx.getDayAmount(mathStartDate, mathEndDate)).add(BigDecimal.ONE);
			
			/**
			 * 2016年末累计欠交保费=2015年年末累计欠交保费*（1+2.84%）+1000*（1+2.84%*（2016年12月31日-2016年5月1日）/365）
			 * 2016年末累计欠交保费=2015年年末累计欠交保费*（1+2.84%*(（2016年12月31日-2016年1月1日+1）/365)）+1000*（1+2.84%*（2016年12月31日-2016年5月1日）/365）
			 * 2016年补交利息=2.84%*2015年累计欠交保费*（2016年12月31日-2016年1月1日+1）/365+1000*2.84%*（2016年12月31日-2016年5月1日）/365 
			 * 2016年补交利息=2016年末累计欠交保费-2015年年末累计欠交保费-1000;
			 * 
			 */
			BigDecimal interest=BigDecimal.ZERO;
			
			dayecb.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP);
			rate.multiply(dayecb.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP));
			BigDecimal.ONE.add(rate.multiply(dayecb.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP)));
			beforeYearBal.multiply(BigDecimal.ONE.add(rate.multiply(dayecb.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP))));
			
			//beforeYearBal.multiply()
			BigDecimal a=beforeYearBal.multiply(BigDecimal.ONE.add(rate.multiply(dayeca.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP)))).setScale(Constants.FOURInt, BigDecimal.ROUND_HALF_UP);
			BigDecimal b=capital.multiply(BigDecimal.ONE.add(rate.multiply(dayecb.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP)))).setScale(Constants.FOURInt, BigDecimal.ROUND_HALF_UP);
			/*BigDecimal a = new BigDecimal(beforeYearBal.doubleValue()*Math.pow((rate.add(new BigDecimal(1))).doubleValue(), 
					(dayeca.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP)).doubleValue())).setScale(Constants.FOURInt, BigDecimal.ROUND_HALF_UP);
			BigDecimal b = new BigDecimal(capital.doubleValue()*Math.pow((rate.add(new BigDecimal(1))).doubleValue(), 
					(dayecb.divide(days, Constants.EIGHT_INT,BigDecimal.ROUND_HALF_UP)).doubleValue())).setScale(Constants.FOURInt, BigDecimal.ROUND_HALF_UP);
			*/
			interest=a.add(b).subtract(beforeYearBal).subtract(capital).setScale(Constants.FOURInt, BigDecimal.ROUND_HALF_UP);
			beforeYearBal=a.add(b);
			System.out.println(mathYear+"年度利率:"+rate);
			if(starYear==DateUtilsEx.getYear(endDate)){
				System.out.println(mathYear+"年度相差天数:"+dayeca);
			}else{
				System.out.println(mathYear+"年度相差天数:"+dayecb);
			}
			System.out.println(mathYear+"年度计算天数:"+days);
			System.out.println(mathYear+"年度利息:"+interest);
			System.out.println(mathYear+"年度累计欠交保费:"+beforeYearBal);
			
			starYear=starYear+1;
			sumInterest=sumInterest.add(interest);
			
		}
		
		
		return sumInterest;
		
	}
	
	/**
	 * 获取某年最后一天日期
	 * @param year 年份
	 * @return Date
	 */
	public static Date getYearLast(int year) {
		Calendar calendar = Calendar.getInstance();
		calendar.clear();
		calendar.set(Calendar.YEAR, year);
		calendar.roll(Calendar.DAY_OF_YEAR, -1);
		Date currYearLast = calendar.getTime();
		return currYearLast;
	}
	/**

     * 获取某年第一天日期

     * @param year 年份

     * @return Date

     */

    public static Date getYearFirst(int year){

        Calendar calendar = Calendar.getInstance();

        calendar.clear();

        calendar.set(Calendar.YEAR, year);

        Date currYearFirst = calendar.getTime();

        return currYearFirst;

    }
	
}
