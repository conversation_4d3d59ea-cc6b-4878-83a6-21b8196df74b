package test.tool;

import java.awt.Dimension;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.ButtonGroup;
import javax.swing.JButton;
import javax.swing.JFileChooser;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableModel;

public class FileCheckOS implements ActionListener {
	JFrame frame = new JFrame("代码内容检查");// 框架布局
	JPanel panel = new JPanel();
	JPanel panel2 = new JPanel();
	JTextField jtf = new JTextField();
	JTable table = new JTable();
	JScrollPane scrollPane1 = new JScrollPane();
	JLabel label1 = new JLabel("文件目录");
	JLabel label2 = new JLabel("用时");
	JTextField text1 = new JTextField();// 文件目录的路径
	JTextField text2 = new JTextField();// 搜索关键字
	JButton button1 = new JButton("...");// 选择
	JButton button2 = new JButton("确定");// 选择
	JButton button3 = new JButton("导出");//
	JFileChooser jfc = new JFileChooser();// 文件选择器
	JRadioButton randioButton1 = new JRadioButton("文本搜索");
	JRadioButton randioButton2 = new JRadioButton("正则表达式搜索");
	List<String> outList = new ArrayList<String>();

	private FileCheckOS() {
		frame.setSize(800, 600);
		frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		frame.setResizable(false);

		panel.setLocation(0, 0);
		panel2.setLocation(0, 100);
		// 添加面板
		frame.add(panel);
		int windowWidth = frame.getWidth(); // 获得窗口宽
		int windowHeight = frame.getHeight(); // 获得窗口高
		Toolkit kit = Toolkit.getDefaultToolkit(); // 定义工具包
		Dimension screenSize = kit.getScreenSize(); // 获取屏幕的尺寸
		int screenWidth = screenSize.width; // 获取屏幕的宽
		int screenHeight = screenSize.height; // 获取屏幕的高
		frame.setLocation(screenWidth / 2 - windowWidth / 2, screenHeight / 2
				- windowHeight / 2);// 设置窗口居中显示
		// 设置布局为 null
		panel.setLayout(null);

		// 创建 JLabel
		label1 = new JLabel("文件路径:");
		// 定义组件的位置 setBounds(x, y, width, height) x 和 y 指定左上角的新位置，由 width和
		// height 指定新的大小。
		label1.setBounds(30, 20, 80, 25);
		panel.add(label1);

		// 文本域用来显示路径
		text1.setBounds(100, 20, 600, 25);
		text1.setText("D:\\test");
		panel.add(text1);

		// 创建按钮1
		button1.addActionListener(this); // 添加事件处理
		button1.setBounds(700, 20, 60, 25);
		panel.add(button1);

		ButtonGroup group = new ButtonGroup();
		group.add(randioButton1);
		group.add(randioButton2);

		randioButton1.setBounds(25, 60, 90, 25);
		randioButton1.setSelected(true);
		panel.add(randioButton1);

		randioButton2.setBounds(120, 60, 150, 25);
		panel.add(randioButton2);

		// 创建按钮2
		button2.addActionListener(this); // 添加事件处理
		button2.setBounds(700, 60, 60, 25);
		panel.add(button2);

		// 创建按钮3
		button3.addActionListener(this); // 添加事件处理
		button3.setBounds(600, 60, 60, 25);
		panel.add(button3);

		// 文本域用来输入关键字
		text2.setBounds(300, 60, 300, 25);
		panel.add(text2);

		// 创建表格中的横标题
		String[] header = { "文件名", "路径", "行号", "内容" };
		// 创建一个表格
		DefaultTableModel tableModel = new DefaultTableModel(null, header);
		table = new JTable(tableModel);
		table.getTableHeader().setReorderingAllowed(false);
		table.setRowHeight(20);
		scrollPane1.setBounds(20, 100, windowWidth - 50, windowHeight - 200);
		scrollPane1.setViewportView(table);
		panel.add(scrollPane1);

		label2 = new JLabel("用时");
		label2.setBounds(30, windowHeight - 100, windowWidth, 25);
		panel.add(label2);
		// 设置界面可见
		frame.setVisible(true);

		Runtime runtime = Runtime.getRuntime();
		System.out.println("处理器的数目" + runtime.availableProcessors());
		System.out.println("空闲内存量：" + runtime.freeMemory() / 1024L / 1024L + "M");
		System.out.println("最大内存量：" + runtime.maxMemory() / 1024L / 1024L + "M");
		System.out.println("内存总量：" + runtime.totalMemory() / 1024L / 1024L + "M");

	}

	/**
	 * 监听的方法
	 */
	public void actionPerformed(ActionEvent e) {
		// 选择路径按钮
		if (e.getSource().equals(button1)) {
			jfc.setFileSelectionMode(1);// 设定只能选择到文件夹
			int state = jfc.showOpenDialog(null);// 此句是打开文件选择器界面的触发语句
			if (state == 1) {
				return;
			} else {
				File f = jfc.getSelectedFile();// f为选择到的目录
				text1.setText(f.getAbsolutePath());
			}
		}

		// 搜索按钮
		if (e.getSource().equals(button2)) {
			if (text1.getText() == null || text1.getText().equals("")) {
				JOptionPane.showMessageDialog(null, "请输入搜索路径", "错误", 0);
				return;
			}

			if (text2.getText() == null || text2.getText().equals("")) {
				JOptionPane.showMessageDialog(null, "请输入搜索内容", "错误", 0);
				return;
			}
			DefaultTableModel tableModel = (DefaultTableModel) table.getModel();
			tableModel.setRowCount(0);
			tableModel.fireTableDataChanged();
			label2.setText("开始搜索，请等待...");
			label2.repaint();
			outList = new ArrayList<String>();
			Date d1 = new Date();
			SimpleDateFormat formatter = new SimpleDateFormat(
					"yyyy-MM-dd HH:mm:ss.SSS");
			String dateString = formatter.format(d1);
			String filepath = text1.getText();
			// 搜索文档
			find(filepath);
			Date d2 = new Date();
			String endString = formatter.format(d2);
			long diff = d2.getTime() - d1.getTime();
			long second = diff / 1000;
			label2.setText("搜索开始时间：" + dateString + " 搜索结束时间：" + endString
					+ "   总计耗时" + second + "秒");
			label2.repaint();
			System.out.println("搜索开始时间：" + dateString + " 搜索结束时间：" + endString
					+ "   总计耗时" + second + "秒");
			

		}

		if (e.getSource().equals(button3)) {
			writeFile(outList);
			
		}
	}

	public void find(String path) {
		String pattern = text2.getText();
		File file = new File(path);
		File[] arr = file.listFiles();
		for (int i = 0; i < arr.length; i++) {
			// 判断是否是文件夹，如果是的话，再调用一下find方法
			if (arr[i].isDirectory()) {
				find(arr[i].getAbsolutePath());
			} else {
				if (arr[i].getName().endsWith(".java")) {
					Pattern p;
					Matcher m;
					p = Pattern.compile(pattern);
					m = p.matcher(getContent(arr[i]));
					if (m.find()) {
						getLnCount(arr[i], pattern);
					}
				}
			}
		}
	}

	public void getLnCount(File file, String pattern) {
		try {
			// BufferedReader是可以按行读取文件
			FileInputStream inputStream = new FileInputStream(file);
			BufferedReader bufferedReader = new BufferedReader(
					new InputStreamReader(inputStream));
			int lineNum = 0;
			String str = null;
			Pattern p;
			Matcher m;
			p = Pattern.compile(pattern);
			DefaultTableModel tableModel;
			while ((str = bufferedReader.readLine()) != null) {
				lineNum = lineNum + 1;
				m = p.matcher(str);
				if (m.find()) {
					System.out.println(file.getName() + "匹配行号：" + lineNum
							+ "，匹配行内容为：" + str.trim());
					tableModel = (DefaultTableModel) table.getModel();
					Object[] rowData = { file.getName(),
							file.getAbsolutePath(), lineNum, str.trim() };
					outList.add(file.getName() + "  位置：  " + file.getAbsolutePath() +  "  匹配行号：  " + lineNum
							+ "  ，匹配行内容为：  " + str.trim());
					tableModel.addRow(rowData);
					tableModel.fireTableDataChanged();
				}

			}
			inputStream.close();
			bufferedReader.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static String getContent(File file) {
		byte[] aa = null;
		try {
			FileInputStream in = new FileInputStream(file);
			BufferedInputStream bis = new BufferedInputStream(in);
			aa = new byte[in.available()];
			bis.read(aa);
			in.close();
			bis.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new String(aa);

	}

	public void writeFile(List<String> list) {
		try {
			File file = new File("D:/result.txt");
			if (!file.exists()) {
				file.createNewFile();
			}
			// 先读取原有文件内容，然后进行写入操作
			StringBuffer str = new StringBuffer("");
			for (int i = 0; i < outList.size(); i++) {
				str.append(outList.get(i));
				str.append("\r\n");
			}
			FileOutputStream o = null;
			o = new FileOutputStream(file);
			o.write(str.toString().getBytes("UTF-8"));
			o.close();
			JOptionPane.showMessageDialog(null, "result.txt在D:/创建完毕，请查看");
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	public static void main(String[] args) {
		new FileCheckOS();
	}
}