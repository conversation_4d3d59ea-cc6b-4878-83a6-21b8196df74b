package test.tool;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.junit.Test;

import com.nci.udmp.component.ftp.config.FtpServerConfig;
import com.nci.udmp.component.ftp.consts.FtpConstant;
import com.nci.udmp.component.ftp.downloader.Downloader;
import com.nci.udmp.component.ftp.moveloader.MoveLoader;
import com.nci.udmp.component.ftp.uploader.Uploader;
import com.nci.udmp.framework.consts.PubConstants;
import com.nci.udmp.framework.exception.FrameworkRuntimeException;
import com.nci.udmp.framework.spring.SpringContextUtils;
import com.nci.udmp.util.file.FileUtil;
import com.nci.udmp.util.ftp.FtpUtil;

public class FtpUtilTest {
	private static final String SYSTEM_ENCODING = System
			.getProperty("file.encoding");
	private FTPClient ftpClient = new FTPClient();

	/**
	 * @description 上传文件
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return
	 */
	private FtpServerConfig connectFtp() {
		FtpServerConfig ftpServerConfig;
		// FTPClient ftpClient = new FTPClient();
		// ftpClient.setControlEncoding("UTF-8");
		// try {
		// ftpClient.connect("**********",21);//
		// ftpClient.login("was", "was123");
		ftpServerConfig = (FtpServerConfig) SpringContextUtils
				.getBean("seckillFtpServerConfig");
		ftpServerConfig.setFtpServerIp("**********");
		ftpServerConfig.setFtpServerPort(21);
		ftpServerConfig.setFtpUserName("was");
		ftpServerConfig.setFtpPassword("was123");
		ftpServerConfig.setFtpEncoding("UTF-8");
		ftpServerConfig.setFtpBufferSize(10240000);
		return ftpServerConfig;
	}
	
	@Test
	public void testDownFile()throws IOException{
		ftpClient.setControlEncoding("UTF-8");
		ftpClient.connect("**********", 21);//
		ftpClient.login("extftp", "ext@1234");
		Downloader downloader = new Downloader();
		downloader.setLocalCatalog("C:\\Users\\<USER>\\Desktop\\秒杀批处理\\05\\20160805\\false");//创建本地文件夹E:\merge\pa\pa-web
//		/home/<USER>/newcore/inforce/backUp
//		/home/<USER>/newcore/inforce/back
		downloader.setRemoteFilePath("/home/<USER>/newcore/inforce/backUp/20160805/00001/A3401161000006_20160805182810.xml");//远程路径
//	    FtpUtil.downloadFile(downLoader);

        boolean result = false;
        int validateResult = 2;//downloaderValidator.validateSingleFile(downloader);
        if (FtpConstant.DOWNLOADER_SUCCESS == validateResult) {
            if (null != ftpClient) {
                String saveLocalCatalog = downloader.getLocalCatalog();
                String remoteFilePath = downloader.getRemoteFilePath();
                if (FileUtil.createLocalDir(saveLocalCatalog)) {
                    if (!isFtpServerExistDir(remoteFilePath)) {
                        String fileName = FileUtil.getFileName(remoteFilePath);
                        BufferedOutputStream bos = null;
                            // 转移到FTP服务器目录至指定的目录下
                            String remoteCatalog = FileUtil.getFilePath(remoteFilePath);
                            if (ftpClient.changeWorkingDirectory(new String(remoteCatalog.getBytes(SYSTEM_ENCODING),
                                    PubConstants.ISO_8859_1_ENCODE))) {
                                String localFileName = FileUtil.getAppendDir(saveLocalCatalog) + fileName;
                                File file = new File(localFileName);
                                bos = new BufferedOutputStream(new FileOutputStream(file));
                                String encodeName = new String(fileName.getBytes(SYSTEM_ENCODING),
                                        PubConstants.ISO_8859_1_ENCODE);
                                result = ftpClient.retrieveFile(encodeName, bos);
                            }
                            if (bos != null) {
                                    bos.flush();
                                    bos.close();
                                    bos = null;
                            }
//                    } else {
//                        Downloader dwr = new Downloader();
//                        dwr.setLocalCatalog(saveLocalCatalog);
//                        dwr.setRemoteCatalog(remoteFilePath);
//                        result = downloadCatalog(dwr);
                    }
                }
            }
        }
	}
	@Test
	public void testUploadFile() throws IOException {
		boolean result = false;
		Uploader uploader = new Uploader();
//		/home/<USER>/home/<USER>/newcore/inforce/send/60704/00001
		uploader.setSaveCatalog("/home/<USER>/newcore/inforce/send/60704/00001/");// 若不存在该文件夹则创建
		uploader.setLocalFilePath("C:\\Users\\<USER>\\Desktop\\秒杀批处理\\05\\20160805\\false\\A3401161000006_20160805182810.xml");// 本地文件路径

		ftpClient.setControlEncoding("UTF-8");
		ftpClient.connect("**********", 21);//
		ftpClient.login("extftp", "ext@1234");
		if (null != ftpClient) {
			FileInputStream inputStream = null;
			BufferedInputStream bi = null;
			// 上传文件处理
			inputStream = new FileInputStream(uploader.getLocalFilePath());
			bi = new BufferedInputStream(inputStream);
			String saveCatalog = uploader.getSaveCatalog();
			String serverPath = new String(saveCatalog.getBytes(System
					.getProperty("file.encoding")),
					PubConstants.ISO_8859_1_ENCODE);
			if (!ftpClient.changeWorkingDirectory(serverPath)) {
				createRemoteDirectory(saveCatalog);
			}
			// 转移工作目录
			boolean change = ftpClient.changeWorkingDirectory(new String(
					saveCatalog.getBytes(SYSTEM_ENCODING),
					PubConstants.ISO_8859_1_ENCODE));
			String saveFileName = FileUtil.getFileName(uploader
					.getLocalFilePath());
			if (change) {
				result = ftpClient.storeFile(
						new String(saveFileName.getBytes(SYSTEM_ENCODING),
								PubConstants.ISO_8859_1_ENCODE), bi);
			}

			inputStream.close();
			bi.close();
			result = true;
		}
	}

	/**
	 * 创建远程目录并进入到创建后的目录
	 * 
	 * @param dirPath
	 *            String
	 * @return true:创建成功,false:创建失败
	 */
	private boolean createRemoteDirectory(String dirPath) {
		boolean success = false;
		try {
			// 通过路径获取操作系统目录符号(\ or /)
			String osDirSignal = FileUtil.getOsDirSignal(dirPath);
			// 如果以(\ or /)结尾 则去掉
			if (dirPath.endsWith(osDirSignal)) {
				dirPath = dirPath.substring(0, dirPath.length() - 1);
			}
			// 如果远程目录不存在且不为根目录，则递归创建远程服务器目录
			if (!isFtpServerExistDir(dirPath)
					&& !osDirSignal.equalsIgnoreCase(dirPath)) {
				int start = 0;
				String dirPre = PubConstants.BLANK;
				if (dirPath.startsWith(osDirSignal)) {
					start = 1;
					dirPre = osDirSignal;
				} else {
					start = 0;
					int pre = dirPath.indexOf(osDirSignal);
					dirPre = dirPath.substring(0, pre);
				}
				int num = StringUtils.countMatches(dirPath, osDirSignal);
				String[] dirNames = dirPath.split(osDirSignal);
				StringBuffer sb = new StringBuffer(dirPre);
				for (int i = start; i <= num; i++) {
					String path = new String(
							dirNames[i].getBytes(SYSTEM_ENCODING),
							PubConstants.ISO_8859_1_ENCODE);
					sb.append(path);
					String subDir = sb.toString();
					if (!isFtpServerExistDir(subDir)) {
						// logger.debug(subDir + "will be created!");
						String subDirectory = new String(subDir);
						if (ftpClient.makeDirectory(subDirectory)) {
							sb.append(osDirSignal);
							// logger.debug(" create the catalog named of : " +
							// subDirectory + " success !");
							success = true;
						} else {
							// logger.debug("create the catalog named of :" +
							// subDirectory + "failed !");
						}
					} else {
						sb.append(osDirSignal);
					}
				}

			} else {
				success = true;
			}
		} catch (IOException e) {
			success = false;
			throw new FrameworkRuntimeException(
					"  create remote directory failed ,the error info is : "
							+ e.getMessage());
		}

		return success;
	}

	/**
	 * 通过路径判断ftp服务器上文件夹是否存在 changeWorkingDirectory方法为切换工作目录
	 * 如果切换成功则表示该路径的文件夹存在，如果切换失败，则表示文件夹不存在
	 * 
	 * @param dirPath
	 *            文件夹路径
	 * @return boolean true:该文件夹已经存在,false:该文件夹不存在
	 */
	private boolean isFtpServerExistDir(String dirPath) {
		boolean result = false;
		if (!StringUtils.isNotBlank(dirPath)) {
			return result;
		}
		try {
			// ftp服务器路径
			String serverPath = new String(dirPath.getBytes(SYSTEM_ENCODING),
					PubConstants.ISO_8859_1_ENCODE);
			if (ftpClient.changeWorkingDirectory(serverPath)) {
				result = true;
			} else {
				// logger.debug("转换ftp会话的工作目录失败！可能不存在路径：" + dirPath);
			}
		} catch (IOException e) {
			result = false;
			throw new FrameworkRuntimeException("转换工作目录异常：" + e.getMessage());
		}
		return result;
	}
	
	/**
     * @description 剪切文件
     * @title 剪切文件
     * <AUTHOR> <EMAIL>
     * @param moveloader 剪切装载器
     * @return true：剪切成功，false：剪切失败
     */
    public boolean removeFile(MoveLoader moveloader) {
        boolean result = false;
        // 校验剪切装载器是否成功
//        int validateResult = moveloaderValidator.validateSingleFile(moveloader);
//        if (FtpConstant.MOVELOADER_SUCCESS == validateResult) {
//            if (null != ftpClient) {
                // 源路径下的目录
                String removeFromCatalog = moveloader.getRemoveFromPath().substring(0,
                        moveloader.getRemoveFromPath().lastIndexOf("/"));
                // 目标路径下的目录
                String removeToCatalog = moveloader.getRemoveToPath().substring(0,
                        moveloader.getRemoveToPath().lastIndexOf("/"));
                try {
                    if (!isFtpServerExistDir(removeFromCatalog)) {
//                        logger.debug(" remove to [" + removeFromCatalog + " ] failed !");
                        return result;
                    }
                    if (!isFtpServerExistDir(removeToCatalog)) {
//                        logger.debug(" remove to [" + removeToCatalog + " ] failed !");
//                        return result;
                        createRemoteDirectory(removeToCatalog);
                    }
                    if (!isFtpServerExistFile(moveloader.getRemoveFromPath())) {
//                        logger.debug(" source file [" + moveloader.getRemoveFromPath() + " ] is not exist !");
                        return result;
                    }
                    // 使用rename方法实现剪切功能
                    result = ftpClient.rename(new String(moveloader.getRemoveFromPath().getBytes(SYSTEM_ENCODING),
                            PubConstants.ISO_8859_1_ENCODE),
                            new String(moveloader.getRemoveToPath().getBytes(SYSTEM_ENCODING),
                                    PubConstants.ISO_8859_1_ENCODE));
                } catch (IOException e) {
                    throw new FrameworkRuntimeException(" move file failed ,the error info is : " + e.getMessage());
                }
//            } else {
//                logger.debug("remove file failed , because ftpClient is null !");
//            }
//        } else {
//            logger.debug("remove file failed , because " + FtpConstant.VALIDATE_RESULT.get(validateResult) + " !");
//        }

        return result;
    }
    
    /**
     * 通过路径判断ftp服务器上文件是否存在 通过FtpClient的listNames方法列出路径文件夹下的所有文件
     * 
     * @param filePath 文件路径
     * @return boolean true:该文件已经存在,false:该文件不存在
     */
    private boolean isFtpServerExistFile(String filePath) {
        boolean result = false;
        // 该路径为一定不为文件夹类型路径
        if (!isFtpServerExistDir(filePath)) {
            try {
                // ftp文件路径
                String ftpFilePath = new String(filePath.getBytes(SYSTEM_ENCODING), PubConstants.ISO_8859_1_ENCODE);
                String[] fileNames = ftpClient.listNames(ftpFilePath);
                if (!ArrayUtils.isEmpty(fileNames)) {
//                    logger.debug(" the files  on " + filePath + ":" + fileNames.toString());
                    result = true;
                } else {
//                    logger.debug("no files found on " + filePath);
                }
            } catch (IOException e) {
                throw new FrameworkRuntimeException("  Directory is not found on ftp server : " + e.getMessage());
            }
        } else {
//            logger.debug("Remote directory[" + filePath + "] is not filePath!");
        }
        return result;
    }
    
    @Test
    public void testRemoveFile() throws Exception{
    	
    	FtpUtilTest fe = new FtpUtilTest();
    	MoveLoader moveloader = new MoveLoader();
    	moveloader.setRemoveFromPath("/home/<USER>/newcore/inforce/send/60704/00001/A4401141000100_20160722133055.xml");
    	moveloader.setRemoveToPath("/home/<USER>/newcore/inforce/backUp/60704/00001/A4401141000100_20160722133055.xml");;
    	fe.ftpClient.setControlEncoding("UTF-8");
		fe.ftpClient.connect("**********", 21);//
		fe.ftpClient.login("extftp", "ext@1234");
		
		fe.removeFile(moveloader);
    }
}
