package serviceinvoke.util;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.ws.Holder;

import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.xbean.spring.context.ClassPathXmlApplicationContext;
import org.junit.Test;
import org.slf4j.Logger;
import org.springframework.context.ApplicationContext;

import com.caucho.hessian.client.HessianProxyFactory;
import com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueReqVO;
import com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueResVO;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.BenefitInsuredCompVO;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.ContractAgentVO;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.ContractBusiProdCompVO;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.ContractProductCompVO;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyReqData;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyResData;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.PayerCompVO;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.PolicyCompVO;
import com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.QuestionaireCustomerVO;
import com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalResData;
import com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.PolicyMedicalVO;
import com.nci.tunan.pa.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.pa.interfaces.query.exports.queryforprint.vo.QueryForPrintReqData;
import com.nci.tunan.pa.interfaces.query.exports.queryforprint.vo.QueryForPrintResData;
import com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodReqData;
import com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodResData;
import com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckReqVOParam;
import com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.BusiProdCodesLimit;
import com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO;
import com.nci.tunan.pa.interfaces.serviceData.commonquery.CustomerInfoCheckReqVO;
import com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewReqVO;
import com.nci.tunan.pa.interfaces.serviceData.countfee.CountFeeReqData;
import com.nci.tunan.pa.interfaces.serviceData.countfee.CountFeeResData;
import com.nci.tunan.pa.interfaces.serviceData.countfee.CountFeeVO;
import com.nci.tunan.pa.interfaces.serviceData.dayTradeRevoke.DayTradeRevokeReqVo;
import com.nci.tunan.pa.interfaces.serviceData.dayTradeRevoke.DayTradeRevokeResVo;
import com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData;
import com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData;
import com.nci.tunan.pa.interfaces.serviceData.groupRisk.TransData;
import com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodReqVO;
import com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodResVO;
import com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.CrsCustomerReqVO;
import com.nci.tunan.pa.interfaces.serviceData.policyquerydelayed.PolicyQueryDelayedReqVO;
import com.nci.tunan.pa.interfaces.serviceData.queryInjured.QueryInjuredReqData;
import com.nci.tunan.pa.interfaces.serviceData.queryInjured.QueryInjuredResData;
import com.nci.tunan.pa.interfaces.serviceData.querycusfivebasicbytel.QueryCusFiveBasicByTelReqVO;
import com.nci.tunan.pa.interfaces.serviceData.querycusfivebasicbytel.QueryCusFiveBasicByTelResVO;
import com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyReqData;
import com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData;
import com.nci.tunan.pa.interfaces.serviceData.queryunderwrite.QueryUnderwriteData;
import com.nci.tunan.pa.interfaces.serviceData.queryunderwrite.QueryUnderwriteResData;
import com.nci.tunan.pa.interfaces.serviceData.riskSpecialQuery.QuerySpecialRiskReqVO;
import com.nci.tunan.pa.interfaces.serviceData.riskSpecialQuery.QuerySpecialRiskResVO;
import com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailReqVO;
import com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailResVO;
import com.nci.tunan.pa.interfaces.serviceData.riskquery.QueryRiskAmountReqVO;
import com.nci.tunan.pa.interfaces.serviceData.riskquery.QueryRiskAmountResVO;
import com.nci.tunan.pa.interfaces.serviceData.saveSalesAmount.SalesAmountVO;
import com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncReqVO;
import com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncResVO;
import com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynReqVO;
import com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynResVO;
import com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncReqVO;
import com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncResVO;
import com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestReqData;
import com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestResData;
import com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.OutputData;
import com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.PolicyList;
import com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.PolicyVO;
import com.nci.tunan.pa.interfaces.update.exports.policyacknowledgementupdate.vo.PolicyAcknowledgementUpdateReqData;
import com.nci.tunan.pa.interfaces.update.exports.policyacknowledgementupdate.vo.PolicyAcknowledgementUpdateResData;
import com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO;
import com.nci.tunan.pa.interfaces.vo.ContractMasterVO;
import com.nci.tunan.pa.interfaces.vo.ContractProductVO;
import com.nci.tunan.pa.interfaces.vo.CustomerSurveyVO;
import com.nci.tunan.pa.interfaces.vo.MedicalVO;
import com.nci.tunan.pa.interfaces.vo.PayerAccountVO;
import com.nci.tunan.pa.interfaces.vo.PolicyAcknowledgementVO;
import com.nci.tunan.pa.interfaces.vo.PolicyHolderVO;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.component.serviceinvoke.ServiceCommonMethod;
import com.nci.udmp.component.serviceinvoke.ServiceInvokeParameterBean;
import com.nci.udmp.component.serviceinvoke.message.ServiceResult;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExA;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.consts.PubConstants;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;

/**
 * 此类仅适用于保单系统调试接口
 * @description 
 * <AUTHOR> <EMAIL> 
 * @date 2017年7月15日 下午4:50:27
 */
public class PAServiceUtilsForPATest implements IPAServiceUtils {
	/**
	 * @description 查询出险人
	 * @param inputData
	 *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.queryInjured.
	 *            QueryInjuredReqData
	 * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.queryInjured.
	 *         QueryInjuredResData
	 */
	public com.nci.tunan.pa.interfaces.serviceData.queryInjured.QueryInjuredResData paiqueryinjureduccqueryInjured(
			com.nci.tunan.pa.interfaces.serviceData.queryInjured.QueryInjuredReqData inputData) {
		Map<String, Object> applicationMap = ParaDefInitConst
				.getApplicationConst();
		boolean dealSwitch = false;
		if (applicationMap.get(Constants.DEALSWITCH) != null) {
			dealSwitch = "1".equals(((ParaDefBO) applicationMap
					.get(Constants.DEALSWITCH)).getParaValue());
		}
		// hessian地址
		String hsUrl = "http://*********:8090/ls/"+ "remoting/queryInjuredUCCqueryInjuredAddr";
		// 报文参数
		// 公共处理报文的函数，将报文头赋值
        SysHeader sysheader = new SysHeader();
        BizHeader reqhead = new BizHeader();
		// 定义系统报文体
		com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.SrvReqBody();
		// 定义业务报文体
		com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.SrvReqBizBody();
		String dealTime = DateUtilsEx.getTodayTime();
		String dealNo = "pa" + "_" + "iqueryinjureduccws" + "_"
				+ "queryInjured";

		srvReqBody.setBizBody(bizBody);
		srvReqBody.setBizHeader(reqhead);
		srvReqBody.getBizBody().setInputData(inputData);
		// 添加调用前的日志输出
		try {
			logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
					+ DataSerialJSon.fromObject(sysheader));
			logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
					+ DataSerialJSon.fromObject(srvReqBody));
		} catch (Exception e3) {
			e3.printStackTrace();
			logger.debug("解析报文的JSON字符串发生异常");
			throw new RuntimeException(e3);
		}
		if (dealSwitch) {
			logger.debug("开始记录交易请求日志");
			CommonDealManagement.beforeCommonDealManagement(sysheader,
					srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
					DealTrigger.HESSIAN, dealNo, dealTime);
		}
		// 返回参数
		com.nci.tunan.pa.interfaces.serviceData.queryInjured.QueryInjuredResData result = null;
		ServiceResult tempresult = new ServiceResult();
		com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.SrvResBody();
		String dealStatus = "2";
		try {
			// 创建hessian工厂
			HessianProxyFactory factory = new HessianProxyFactory();
			// 通过工厂创建接口实例
			com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.hs.IQueryInjuredUCCHS iqueryinjureducchs = (com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.hs.IQueryInjuredUCCHS) factory
					.create(hsUrl);
			// 通过接口实例调用接口方法
			tempresult = iqueryinjureducchs.queryInjured(sysheader, srvReqBody);
			// 返回值赋值
			resultbody = (com.nci.tunan.pa.interfaces.queryinjured.exports.iqueryinjureducc.queryinjured.SrvResBody) tempresult
					.getResponse();
			result = resultbody.getBizBody().getOutputData();
			// 添加调用后的日志输出
			logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
					+ "-技术报文头："
					+ DataSerialJSon.fromObject(tempresult.getSysHeader()));
			logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
					+ "-技术报文体："
					+ DataSerialJSon.fromObject(tempresult.getResponse()));
			logger.debug("hessian调用接口成功");
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("hessian调用业务接口异常");
			dealStatus = "3";
			throw new RuntimeException(e);
		} finally {
			if (dealSwitch && resultbody != null) {
				logger.debug("开始记录交易响应日志");
				CommonDealManagement.afterCommonDealManagement(sysheader,
						resultbody.getBizHeader(), resultbody.getBizBody(),
						DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
			}

		}
		return result;
	}
	@Test
	public void testpaiqueryinjureduccqueryInjured(){
		QueryInjuredResData output = new QueryInjuredResData();
		QueryInjuredReqData inputData = new QueryInjuredReqData();
		inputData.setPolicyCode("886643694897");
		CurrentPage currentPage = new CurrentPage();
		currentPage.setPageNo(1);
		currentPage.setPageSize(20);
		inputData.setCurrentPage(currentPage);
		output = paiqueryinjureduccqueryInjured(inputData);
		System.out.println(output.toString());
	}
	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();
	 public com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueResVO prdimmscalccashvalueucccalcCashValue(com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueReqVO inputData) {
	        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
	        boolean dealSwitch = false;
	        if (applicationMap.get(Constants.DEALSWITCH) != null) {
	            dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                    .get(Constants.DEALSWITCH)).getParaValue());
	        }
	   	  //hessian地址
		String hsUrl = "http://**********:9080/prd/" + "remoting/mmsCalcCashValueUcccalcCashValueAddr";
//		String hsUrl = Constants.SERVICEENVPARAMAP.get("PRD") + "remoting/mmsCalcCashValueUcccalcCashValueAddr";
	    
	      //报文参数
	      //公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();//ServiceInvokeParameterBean.sysHeaderInit();
	      BizHeader reqhead = new BizHeader();//ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
	      // 定义系统报文体
	      com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.SrvReqBody srvReqBody = new 
	    		com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.SrvReqBizBody bizBody = new 
	    		com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.SrvReqBizBody();
	      String dealTime = DateUtilsEx.getTodayTime();
		  String dealNo = "prd" + "_" + "immscalccashvalueuccws" + "_" + "calcCashValue";
		  
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      //添加调用前的日志输出
	      try {
	          logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.info("解析报文的JSON字符串发生异常");
	          throw new RuntimeException(e3);
	      }
	      if (dealSwitch) {
		      logger.info("开始记录交易请求日志");
	          CommonDealManagement.beforeCommonDealManagement(sysheader, srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                  DealTrigger.HESSIAN, dealNo, dealTime);
		  }    
	      //返回参数
	      com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueResVO result = null;
	      ServiceResult tempresult = new ServiceResult();
	      com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.SrvResBody resultbody = new com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.SrvResBody();
	      String dealStatus = "2";
	      try {
	          //创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          //通过工厂创建接口实例
	          com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.hs.IMmsCalcCashValueUccHS immscalccashvalueucchs = (com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.hs.IMmsCalcCashValueUccHS) factory.create(hsUrl);
	          //通过接口实例调用接口方法
	          tempresult = immscalccashvalueucchs.calcCashValue(sysheader, srvReqBody);
	          //返回值赋值
	          resultbody = 
	        		  (com.nci.tunan.mms.interfaces.calc.exports.immscalccashvalueucc.calccashvalue.SrvResBody)tempresult.getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          //添加调用后的日志输出
	          logger.info(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
	                      + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.info(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
	                      + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.info("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.info("hessian调用业务接口异常");
	          dealStatus = "3";
	          throw new RuntimeException(e);
	      } finally {
	            if (dealSwitch && resultbody != null) {
	                logger.info("开始记录交易响应日志");
	                CommonDealManagement.afterCommonDealManagement(sysheader,
	                        resultbody.getBizHeader(), resultbody.getBizBody(), DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
	            }
	            
	        }
	      return result;
	  }
	 @Test
	 public void testprdimmscalccashvalueucccalcCashValue() throws ParseException{
		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		 MmsCalcCashValueReqVO inputData = new MmsCalcCashValueReqVO();
		 inputData.setProductId(new BigDecimal(320));
		 inputData.setNextYearSurvivalPayment(new BigDecimal(881.80));
		 inputData.setRenewalFlag(0);
		 inputData.setChargeMode(1);
		 inputData.setChargeYear(1);
		 inputData.setChargePeriod(2);
		 inputData.setPayPeriodFlag(new BigDecimal(0));
		 inputData.setCoverageYear(60);
		 inputData.setLiabilityStatus(1);
		 inputData.setAge(9);
		 inputData.setPolicyMonth(new BigDecimal(25.06));
		 inputData.setGender(1);
		 inputData.setPolicyEffectiveDate(sdf.parse("2016-09-08"));
		 inputData.setPayAmount(new BigDecimal(0));
		 inputData.setHealthServiceFlag(0);
		 inputData.setGrossPremium(new BigDecimal(44090));
		 inputData.setPremium(new BigDecimal(44090));
		 inputData.setCoveragePeriod("A");
//		 inputData.setUnit(new BigDecimal(1));
		 inputData.setCurYearSP(new BigDecimal(0));
		 inputData.setPolicydays(2);
		 inputData.setIsWholePolicyFlag(0);
		 inputData.setOptLiabEffectDate(sdf.parse("2016-09-08"));
		 inputData.setBonusSA(new BigDecimal(0));
		 inputData.setPolicyYearAge(11);
		 inputData.setBtopUpNYSPRate(new BigDecimal(0));
		 inputData.setBcurYearSPRate(new BigDecimal(0));
		 inputData.setNYSurvivalPaymentPerBonusSA(new BigDecimal(0));
		 inputData.setAddOptLiabFlag(0);
		 inputData.setReceiveAmountArray("");
		 inputData.setBusinessEffectDate(sdf.parse("2016-09-08"));
		 inputData.setBusinessExpiredDate(sdf.parse("2067-09-08"));
		 inputData.setFirstPeriodFlag(new BigDecimal(0));
		 inputData.setBasicChargeYear(1);
		 
		 MmsCalcCashValueResVO outputData = prdimmscalccashvalueucccalcCashValue(inputData);
		 System.out.println(XmlHelper.classToXml(outputData));
	 }
	/**
	 * @description 秒杀回执日期同步
	 * @param inputData
	 *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.
	 *            seckillacknowledgesync.SecKillAcknowledgeSyncReqVO
	 * @return 
	 *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync
	 *         .SecKillAcknowledgeSyncResVO
	 */
	public com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncResVO paiseckillacknowledgesyncuccdataSyncInfo(
			com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncReqVO inputData) {
        
        //调用的webService的wsdl
//	        String wsdl = "http://***********:9080/pa/webservice/secKillAcknowledgeSyncUCCdataSyncInfoAddr?wsdl";
	  	String wsdl = "http://***********:8111/services/P00002001387?wsdl";
        //服务接口路径
        String interfacePath = "com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.ws.ISecKillAcknowledgeSyncUCCWS";
        //报文参数
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.SrvResBody> parametersResBody = new 
                Holder<com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.SrvResBody>();
        //公共处理报文的函数，将报文头赋值
        SysHeader sysheader = new SysHeader();//ServiceInvokeParameterBean.sysHeaderInit();
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1499"));
        BizHeaderExA reqhead = new BizHeaderExA();;//ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        parametersResHeader.value = sysheader;
        // 定义系统报文体
        com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.SrvReqBody srvReqBody = new 
                 com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.SrvReqBody();
        // 定义业务报文体
        com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.SrvReqBizBody bizBody = new 
                com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.SrvReqBizBody();
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        //添加调用前的日志输出
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
            throw new RuntimeException(e3);
        }
        
        //返回参数
        com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncResVO result = null;
        try {
            //调用公共webservice工厂方法
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdl, interfacePath);
            //通过工厂创建接口实例
            com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.ws.ISecKillAcknowledgeSyncUCCWS iseckillacknowledgesyncuccws = (com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillacknowledgesyncucc.datasyncinfo.ws.ISecKillAcknowledgeSyncUCCWS) soapFactory.create();
            //调用接口的指定方法，将参数传入
            iseckillacknowledgesyncuccws.dataSyncInfo(sysheader, srvReqBody, parametersResHeader, parametersResBody);
            //将业务报文体的outputData赋值给返回值
            result = parametersResBody.value.getBizBody().getOutputData(); 
            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
            CommonHeaderDeal.setBIZHEADERTHREAD_EXA(parametersResBody.value.getBizHeader());
            //添加调用后的日志输出
            logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(sysheader));
            logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(parametersResBody));
            logger.debug("webservice调用接口成功");
        } catch (BizException e) {
            e.printStackTrace();
            logger.debug("webservice调用业务接口异常");
            throw new RuntimeException(e);
        } catch (ClassNotFoundException e1) {
            e1.printStackTrace();
            logger.debug("找不到接口类");
            throw new RuntimeException(e1);
        } catch (Exception e2) {
            e2.printStackTrace();
            logger.debug("解析返回的报文的JSON字符串发生异常");
            throw new RuntimeException(e2);
        } 
        return result;
   }

	@Test
	public void testPaiseckillacknowledgesyncuccdataSyncInfo() {
		SecKillAcknowledgeSyncReqVO inputData = new SecKillAcknowledgeSyncReqVO();
		inputData.setContNo("88608200000000161003");
		inputData.setGetPolDate(new Date());
		System.out.println(XmlHelper.classToXml(inputData));
		SecKillAcknowledgeSyncResVO output = paiseckillacknowledgesyncuccdataSyncInfo(inputData);
		System.out.println(XmlHelper.classToXml(output));
	}


	/**
	 * @description 新契约调用创建保单。
	 * @param inputData
	 *            输入参数VO:com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo
	 *            .CreatePolicyReqData
	 * @return 输出的参数VO：com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.
	 *         CreatePolicyResData
	 */
	public com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyResData paicreatepolicyuccinsertPolicy(
			com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyReqData inputData) {		Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
			boolean dealSwitch = false;
			if (applicationMap.get(Constants.DEALSWITCH) != null) {
				dealSwitch = "1".equals(((ParaDefBO) applicationMap.get(Constants.DEALSWITCH)).getParaValue());
			}
			//hessian地址
//			String hsUrl = Constants.SERVICEENVPARAMAP.get("PA") + "remoting/createPolicyUCCinsertPolicyAddr";
			String hsUrl = "http://localhost:8080/ls/remoting/createPolicyUCCinsertPolicyAddr";
			//报文参数
			//公共处理报文的函数，将报文头赋值
			SysHeader sysheader = new SysHeader();
			BizHeader reqhead = new BizHeader();   

			//因t_udmp_deal_execute_log表中TRAN_CODE为空，无法判断哪两条log为一次交互，所以在此增加使用随机数给TranCode的赋值 add by songdd 2017-04-04 16:40
			//TRAN_CODE中增加投保单号，这样比较容易查日志，但是TRAN_CODE总长度为40，所以进行了截取 modify by songdd 2017-04-05 9:21
			try {
				if (inputData.getPolicy().getContractMasterVO().getApplyCode().length() >= 14) {
					reqhead.setTranCode("489615684685167/9846578465");
				}
			} catch (Exception e) {
				logger.error("通过投保单号截取TRAN_CODE失败！但不影响程序正常运行，可能是因为投保单号长度不足引起，投保单号="+inputData.getPolicy().getContractMasterVO().getApplyCode()+"，错误信息为=" + e.getMessage(), e);
				throw new RuntimeException(e);
			}


			// 定义系统报文体
			com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBody srvReqBody = new 
					com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBody();
			// 定义业务报文体
			com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBizBody bizBody = new 
					com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBizBody();
			String dealTime = DateUtilsEx.getTodayTime();
			String dealNo = "pa" + "_" + "icreatepolicyuccws" + "_" + "insertPolicy";

			srvReqBody.setBizBody(bizBody);
			srvReqBody.setBizHeader(reqhead);
			srvReqBody.getBizBody().setInputData(inputData);
			//添加调用前的日志输出
			try {
				logger.debug("转保单接口消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
				logger.debug("转保单接口消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
			} catch (Exception e3) {
				e3.printStackTrace();
				logger.debug("解析报文的JSON字符串发生异常");
				throw new RuntimeException(e3);
			}
			//返回参数
			com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyResData result = null;
			ServiceResult tempresult = new ServiceResult();
			com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvResBody();
			String dealStatus = "2";
			try {
				//创建hessian工厂
				HessianProxyFactory factory = new HessianProxyFactory();
				//通过工厂创建接口实例
				com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.hs.ICreatePolicyUCCHS icreatepolicyucchs = (com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.hs.ICreatePolicyUCCHS) factory.create(hsUrl);
				//通过接口实例调用接口方法
				tempresult = icreatepolicyucchs.insertPolicy(sysheader, srvReqBody);
				//返回值赋值
				resultbody = (com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvResBody)tempresult.getResponse();
	result = resultbody.getBizBody().getOutputData();
				//添加调用后的日志输出
				logger.debug("转保单接口hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："+ DataSerialJSon.fromObject(tempresult.getSysHeader()));
				logger.debug("转保单接口hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："+ DataSerialJSon.fromObject(tempresult.getResponse()));
			} catch (Exception e) {
				e.printStackTrace();
				logger.error("调用新契约调用创建保单服务出现异常，异常信息="+e.getMessage(),e);
				logger.debug("转保单接口hessian调用业务接口异常"+e);
				dealStatus = "3";
				throw new RuntimeException(e);
			}
			if (result.getResultCode() == 1) {
				result.setResultMsg("消息id："+sysheader.getMsgId()+"="+Constants.SERVICEENVPARAMAP.get("PA")+"保单接口返回异常原因："+result.getResultMsg());
			}
			return result;}

	@Test
	public void paicreatepolicyuccinsertPolicyTest(){
		//88600100000000129054
		CreatePolicyReqData inputData = new CreatePolicyReqData();
		PolicyCompVO policy = new PolicyCompVO();
		ContractMasterVO contractMasterVO = new ContractMasterVO();
		contractMasterVO.setPolicyCode("990026480617");
		contractMasterVO.setApplyCode("56201235575007");
		policy.setContractMasterVO(contractMasterVO);
		inputData.setPolicy(policy);
		CreatePolicyResData output =  paicreatepolicyuccinsertPolicy(inputData);
		
	}

//	@Override
//	public SecKillPolicyDateSyncResVO paiseckillpolicydatesyncuccseckillPolicyDateInfo(
//			SecKillPolicyDateSyncReqVO inputData) {
//		// TODO Auto-generated method stub
//		return null;
//	}
	 /**
     * @description 秒杀承包数据同步
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncResVO  
    */
	@Override
  public com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncResVO paiseckillpolicydatesyncuccseckillPolicyDateInfo(com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncReqVO inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        //调用的webService的wsdl
	    String wsdl = "http://***********:9080/pa/" + "webservice/seckillPolicyDateSyncUCCseckillPolicyDateInfoAddr?wsdl";
        //服务接口路径
        String interfacePath = "com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.ws.ISeckillPolicyDateSyncUCCWS";
        //报文参数
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.SrvResBody> parametersResBody = new 
        		Holder<com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.SrvResBody>();
        //公共处理报文的函数，将报文头赋值
        SysHeader sysheader = new SysHeader();//ServiceInvokeParameterBean.sysHeaderInit();
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("2149"));
        BizHeaderExA reqhead = new BizHeaderExA();//ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        parametersResHeader.value = sysheader;
        // 定义系统报文体
        com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.SrvReqBody srvReqBody = new 
        		 com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.SrvReqBody();
        // 定义业务报文体
        com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.SrvReqBizBody bizBody = new 
        		com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
	    String dealNo = "pa" + "_" + "iseckillpolicydatesyncuccws" + "_" + "seckillPolicyDateInfo";  
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        //添加调用前的日志输出
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
            throw new RuntimeException(e3);
        }
        if (dealSwitch) {
	       logger.debug("开始记录交易请求日志");
//           CommonDealManagement.beforeCommonDealManagement(sysheader, srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
//                    DealTrigger.WEBSERVICE, dealNo, dealTime); 
	    }
        //返回参数
        com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncResVO result = null;
        String dealStatus = "2";
        try {
            //调用公共webservice工厂方法
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdl, interfacePath);
            //通过工厂创建接口实例
            com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.ws.ISeckillPolicyDateSyncUCCWS iseckillpolicydatesyncuccws = (com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillpolicydatesyncucc.seckillpolicydateinfo.ws.ISeckillPolicyDateSyncUCCWS) soapFactory.create();
            //调用接口的指定方法，将参数传入
            iseckillpolicydatesyncuccws.seckillPolicyDateInfo(sysheader, srvReqBody, parametersResHeader, parametersResBody);
            //将业务报文体的outputData赋值给返回值
            result = parametersResBody.value.getBizBody().getOutputData(); 
            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
			CommonHeaderDeal.setBIZHEADERTHREAD_EXA(parametersResBody.value.getBizHeader());
			//添加调用后的日志输出
			logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
		                + DataSerialJSon.fromObject(sysheader));
		    logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
		                + DataSerialJSon.fromObject(parametersResBody));
            logger.debug("webservice调用接口成功");
        } catch (BizException e) {
            e.printStackTrace();
            logger.debug("webservice调用业务接口异常");
            dealStatus = "3";
            throw new RuntimeException(e);
        } catch (ClassNotFoundException e1) {
            e1.printStackTrace();
            logger.debug("找不到接口类");
            dealStatus = "3";
            throw new RuntimeException(e1);
        } catch (Exception e2) {
            e2.printStackTrace();
            logger.debug("解析返回的报文的JSON字符串发生异常");
            dealStatus = "3";
            throw new RuntimeException(e2);
        } finally {  
            if (dealSwitch && parametersResBody.value == null) {
                logger.debug("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader,
                        null, null, DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            } else if (dealSwitch && parametersResBody.value != null) {
                logger.debug("开始记录交易响应日志");
//                CommonDealManagement.afterCommonDealManagement(sysheader,
//                        parametersResBody.value.getBizHeader(), parametersResBody.value.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            }
            
        }
        return result;
   }
	
	public void testSeckPolicy(){
		SecKillPolicyDateSyncResVO output = new SecKillPolicyDateSyncResVO();
		SecKillPolicyDateSyncReqVO inputData = new SecKillPolicyDateSyncReqVO();
		output =  paiseckillpolicydatesyncuccseckillPolicyDateInfo(inputData);
	}
	
	/**
	    * @description 从保单表中获取某一时点保单信息
	    * @param inputData
	    *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.querypolicy.
	    *            QueryPolicyReqData
	    * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.querypolicy.
	    *         QueryPolicyResData
	    */
	   public com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData paiquerypolicyuccqueryPolicy(
	         com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyReqData inputData) {
	      Map<String, Object> applicationMap = ParaDefInitConst
	            .getApplicationConst();
	      boolean dealSwitch = false;
	      if (applicationMap.get(Constants.DEALSWITCH) != null) {
	         dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                .get(Constants.DEALSWITCH)).getParaValue());
	      }
	      // hessian地址
	      /*String hsUrl = "http://**********:9082/ls/"
	            + "remoting/queryPolicyUCCqueryPolicyAddr";*/
	      String hsUrl = "http://**********:8080/ls/"
		            + "remoting/queryPolicyUCCqueryPolicyAddr";
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();//ServiceInvokeParameterBean.sysHeaderInit();
	      BizHeader reqhead = new BizHeader();//ServiceInvokeParameterBean.bizHeaderInit(sysheader);
	      // 定义系统报文体
	   com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.SrvReqBody();
	      // 定义业务报文体
	   com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.SrvReqBizBody();
	      String dealTime = DateUtilsEx.getTodayTime();
	      String dealNo = "pa" + "_" + "iquerypolicyuccws" + "_" + "queryPolicy";

	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	         logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
	                + DataSerialJSon.fromObject(sysheader));
	         logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
	                + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	         e3.printStackTrace();
	         logger.debug("解析报文的JSON字符串发生异常");
	         throw new RuntimeException(e3);
	      }
	      if (dealSwitch) {
	         logger.debug("开始记录交易请求日志");
	         CommonDealManagement.beforeCommonDealManagement(sysheader,
	                srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                DealTrigger.HESSIAN, dealNo, dealTime);
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData result = null;
	      ServiceResult tempresult = new ServiceResult();
	   com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.SrvResBody();
	      String dealStatus = "2";
	      try {
	         // 创建hessian工厂
	         HessianProxyFactory factory = new HessianProxyFactory();
	         // 通过工厂创建接口实例
	      com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.hs.IQueryPolicyUCCHS iquerypolicyucchs = (com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.hs.IQueryPolicyUCCHS) factory
	                .create(hsUrl);
	         // 通过接口实例调用接口方法
	         tempresult = iquerypolicyucchs.queryPolicy(sysheader, srvReqBody);
	         // 返回值赋值
	         resultbody = (com.nci.tunan.pa.interfaces.querypolicy.exports.iquerypolicyucc.querypolicy.SrvResBody) tempresult
	                .getResponse();
	         result = resultbody.getBizBody().getOutputData();
	         // 添加调用后的日志输出
	         logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                + "-技术报文头："
	                + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	         logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                + "-技术报文体："
	                + DataSerialJSon.fromObject(tempresult.getResponse()));
	         logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	         e.printStackTrace();
	         logger.debug("hessian调用业务接口异常");
	         dealStatus = "3";
	         throw new RuntimeException(e);
	      } finally {
	         if (dealSwitch && resultbody != null) {
	            logger.debug("开始记录交易响应日志");
	            CommonDealManagement.afterCommonDealManagement(sysheader,
	                   resultbody.getBizHeader(), resultbody.getBizBody(),
	                   DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
	         }

	      }
	      return result;
	   }
	   //理赔抄单接口测试方法
	   @Test
	   public void testpaiquerypolicyuccqueryPolicy() throws ParseException{
		   com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyReqData inputData = new QueryPolicyReqData();
		   SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		   inputData.setDateFlag(sdf.parse("2021-09-01"));
		   List<String> policyList = new ArrayList<String>();
		   policyList.add("990026784737");
		   inputData.setPolicyCodes(policyList);
		   QueryPolicyResData output = paiquerypolicyuccqueryPolicy(inputData);
		   System.out.println(XmlHelper.classToXml(output));
	   }
	   /**
	     * @description 网络直销平台-终止保单批量查询
	     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynReqVO
	     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynResVO  
	    */
	  public com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynResVO paiseckillexpirypolicysyncuccgetExpiryPolicyDataInfo(com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynReqVO inputData) {
	        
	        //调用的webService的wsdlhttp://**********:8080/ls/
		  String wsdl = "http://**********:8080/ls/" + "webservice/secKillExpiryPolicySyncUCCgetExpiryPolicyDataInfoAddr?wsdl";

//	    String wsdl = "http://***********:9080/pa/" + "webservice/secKillExpiryPolicySyncUCCgetExpiryPolicyDataInfoAddr?wsdl";
//	    String wsdl = "http://***********:8111/services/P00001002096?wsdl";
		   
	        //服务接口路径
	        String interfacePath = "com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.ws.ISecKillExpiryPolicySyncUCCWS";
	        //报文参数
	        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
	        Holder<com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.SrvResBody> parametersResBody = new 
	                Holder<com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.SrvResBody>();
	        //公共处理报文的函数，将报文头赋值
	        SysHeader sysheader = new SysHeader();//ServiceInvokeParameterBean.sysHeaderInit();
	        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("2293"));
	        BizHeaderExA reqhead = new BizHeaderExA();//ServiceInvokeParameterBean.bizHeaderInit(sysheader);
	        parametersResHeader.value = sysheader;
	        // 定义系统报文体
	        com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.SrvReqBody srvReqBody = new 
	                 com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.SrvReqBody();
	        // 定义业务报文体
	        com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.SrvReqBizBody bizBody = new 
	                com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.SrvReqBizBody();
	        srvReqBody.setBizBody(bizBody);
	        srvReqBody.setBizHeader(reqhead);
	        srvReqBody.getBizBody().setInputData(inputData);
	        //添加调用前的日志输出
	        try {
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	        } catch (Exception e3) {
	            e3.printStackTrace();
	            logger.debug("解析报文的JSON字符串发生异常");
	            throw new RuntimeException(e3);
	        }
	        
	        //返回参数
	        com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynResVO result = null;
	        try {
	            //调用公共webservice工厂方法
	            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdl, interfacePath);
	            //通过工厂创建接口实例
	            com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.ws.ISecKillExpiryPolicySyncUCCWS iseckillexpirypolicysyncuccws = (com.nci.tunan.pa.interfaces.seckillpolicydatesync.exports.iseckillexpirypolicysyncucc.getexpirypolicydatainfo.ws.ISecKillExpiryPolicySyncUCCWS) soapFactory.create();
	            //调用接口的指定方法，将参数传入
	            iseckillexpirypolicysyncuccws.getExpiryPolicyDataInfo(sysheader, srvReqBody, parametersResHeader, parametersResBody);
	            //将业务报文体的outputData赋值给返回值
	            result = parametersResBody.value.getBizBody().getOutputData(); 
	            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
	            CommonHeaderDeal.setBIZHEADERTHREAD_EXA(parametersResBody.value.getBizHeader());
	            //添加调用后的日志输出
	            logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
	                        + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
	                        + DataSerialJSon.fromObject(parametersResBody));
	            logger.debug("webservice调用接口成功");
	        } catch (BizException e) {
	            e.printStackTrace();
	            logger.debug("webservice调用业务接口异常");
	            throw new RuntimeException(e);
	        } catch (ClassNotFoundException e1) {
	            e1.printStackTrace();
	            logger.debug("找不到接口类");
	            throw new RuntimeException(e1);
	        } catch (Exception e2) {
	            e2.printStackTrace();
	            logger.debug("解析返回的报文的JSON字符串发生异常");
	            throw new RuntimeException(e2);
	        } 
	        return result;
	   }
	  @Test
	  public void test() throws ParseException{
		  SecKillExpiryPolicySynReqVO inputData = new SecKillExpiryPolicySynReqVO ();
		  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		  inputData.setEndDate(sdf.parse("2016-07-30"));
		  inputData.setStartDate(sdf.parse("2016-07-30"));
		  SecKillExpiryPolicySynResVO output = 
				  paiseckillexpirypolicysyncuccgetExpiryPolicyDataInfo(inputData) ;
		  System.out.println(output);
	  }
	  
	  /**
	   * 新契约回执
	   * @param inputData
	   * @return
	   */
	  public com.nci.tunan.pa.interfaces.update.exports.policyacknowledgementupdate.vo.PolicyAcknowledgementUpdateResData paipolicyacknowledgementupdateuccpolicyAcknowledgementUpdate(
	          com.nci.tunan.pa.interfaces.update.exports.policyacknowledgementupdate.vo.PolicyAcknowledgementUpdateReqData inputData) {
	      // hessian地址
	      String hsUrl = "http://localhost:8080/ls/remoting/policyAcknowledgementUpdateUCCpolicyAcknowledgementUpdateAddr";
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.debug("解析报文的JSON字符串发生异常");
	          throw new RuntimeException(e3);
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.update.exports.policyacknowledgementupdate.vo.PolicyAcknowledgementUpdateResData result = null;
	      try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          // 通过工厂创建接口实例
	          com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.hs.IPolicyAcknowledgementUpdateUCCHS ipolicyacknowledgementupdateucchs = (com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.hs.IPolicyAcknowledgementUpdateUCCHS) factory
	                  .create(hsUrl);
	          // 通过接口实例调用接口方法
	          ServiceResult tempresult = ipolicyacknowledgementupdateucchs.policyAcknowledgementUpdate(sysheader, srvReqBody);
	          // 返回值赋值
	          com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.policyacknowledgementupdate.exports.ipolicyacknowledgementupdateucc.policyacknowledgementupdate.SrvResBody) tempresult
	                  .getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.debug("hessian调用业务接口异常");
	          throw new RuntimeException(e);
	      }
	      return result;
	  }
	  @Test
	  public void testpaipolicyacknowledgementupdateuccpolicyAcknowledgementUpdate(){
		  PolicyAcknowledgementUpdateReqData inputData = new PolicyAcknowledgementUpdateReqData();
		  List<PolicyAcknowledgementVO> policyAcknowledgementVOs = new ArrayList<PolicyAcknowledgementVO>();
		  PolicyAcknowledgementVO policyAcknowledgementVO = new PolicyAcknowledgementVO();
		  policyAcknowledgementVO.setPolicyCode("990029446555");
		  policyAcknowledgementVO.setBranchReceiveDate(new Date());
		  policyAcknowledgementVOs.add(policyAcknowledgementVO);
		  inputData.setPolicyAcknowledgementVOs(policyAcknowledgementVOs);
		  
		  PolicyAcknowledgementUpdateResData outputData =  paipolicyacknowledgementupdateuccpolicyAcknowledgementUpdate(inputData);
	  }
	  
	  //paiquerypremarapuccqueryPremArapMsg  消费方代码
	  public com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapResVO paiquerypremarapuccqueryPremArapMsg(
				com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapReqVO inputData) {
			Map<String, Object> applicationMap = ParaDefInitConst
					.getApplicationConst();
			boolean dealSwitch = false;
			if (applicationMap.get(Constants.DEALSWITCH) != null) {
				dealSwitch = "1".equals(((ParaDefBO) applicationMap
						.get(Constants.DEALSWITCH)).getParaValue());
			}
			// hessian地址
			String hsUrl = "http://**********:8080/ls/remoting/queryPremArapUCCqueryPremArapMsgAddr";
			// 报文参数
			// 公共处理报文的函数，将报文头赋值
			SysHeader sysheader = new SysHeader(); //ServiceInvokeParameterBean.sysHeaderInit();
			BizHeader reqhead = new BizHeader(); //ServiceInvokeParameterBean.bizHeaderInit(sysheader);
			// 定义系统报文体
			com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.SrvReqBody();
			// 定义业务报文体
			com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.SrvReqBizBody();
			String dealTime = DateUtilsEx.getTodayTime();
			String dealNo = "pa" + "_" + "iquerypremarapuccws" + "_"
					+ "queryPremArapMsg";

			srvReqBody.setBizBody(bizBody);
			srvReqBody.setBizHeader(reqhead);
			srvReqBody.getBizBody().setInputData(inputData);
			// 添加调用前的日志输出
			try {
				logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
						+ DataSerialJSon.fromObject(sysheader));
				logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
						+ DataSerialJSon.fromObject(srvReqBody));
			} catch (Exception e3) {
				e3.printStackTrace();
				logger.debug("解析报文的JSON字符串发生异常");
				throw new RuntimeException(e3);
			}
			if (dealSwitch) {
				logger.debug("开始记录交易请求日志");
				CommonDealManagement.beforeCommonDealManagement(sysheader,
						srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
						DealTrigger.HESSIAN, dealNo, dealTime);
			}
			// 返回参数
			com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapResVO result = null;
			ServiceResult tempresult = new ServiceResult();
			com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.SrvResBody();
			String dealStatus = "2";
			try {
				// 创建hessian工厂
				HessianProxyFactory factory = new HessianProxyFactory();
				// 通过工厂创建接口实例
				com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.hs.IQueryPremArapUCCHS iquerypremarapucchs = (com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.hs.IQueryPremArapUCCHS) factory
						.create(hsUrl);
				// 通过接口实例调用接口方法
				tempresult = iquerypremarapucchs.queryPremArapMsg(sysheader,
						srvReqBody);
				// 返回值赋值
				resultbody = (com.nci.tunan.pa.interfaces.querypremarap.exports.iquerypremarapucc.querypremarapmsg.SrvResBody) tempresult
						.getResponse();
				result = resultbody.getBizBody().getOutputData();
				// 添加调用后的日志输出
				logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
						+ "-技术报文头："
						+ DataSerialJSon.fromObject(tempresult.getSysHeader()));
				logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
						+ "-技术报文体："
						+ DataSerialJSon.fromObject(tempresult.getResponse()));
				logger.debug("hessian调用接口成功");
			} catch (Exception e) {
				e.printStackTrace();
				logger.debug("hessian调用业务接口异常");
				dealStatus = "3";
				throw new RuntimeException(e);
			} finally {
				if (dealSwitch && resultbody != null) {
					logger.debug("开始记录交易响应日志");
					CommonDealManagement.afterCommonDealManagement(sysheader,
							resultbody.getBizHeader(), resultbody.getBizBody(),
							DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
				}

			}
			long end = System.currentTimeMillis();
			return result;
		}
	  @Test
	  public void testpa(){
		  com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapReqVO inputData = new com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapReqVO();
		  inputData.setPolicyCode("BJ039831281001232");
		  inputData.setBusiProdCode("00128000");
		  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		  try {
			inputData.setStartTime(sdf.parse("2001-11-01"));
			inputData.setEndTime(sdf.parse("2002-12-01"));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		  com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapResVO  outputData = paiquerypremarapuccqueryPremArapMsg(inputData);
		  XmlHelper.classToXml(outputData);
		  
	  }
	  
	     /**
       * @description 查询新契约纠错是否做过续期接口
       * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewReqVO
       * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewResVO
      */
	  @Test
      public void paiquerywhetherrenewuccqueryWhetherRenew() throws ParseException{
	      QueryWhetherRenewReqVO inputData = new QueryWhetherRenewReqVO ();
	      inputData.setPolicyCode("552001330030");
	       
	              this.paiquerywhetherrenewuccqueryWhetherRenew(inputData) ;
      }
	  
	  

	    /**
	     * @description 查询新契约纠错是否做过续期接口
	     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewReqVO
	     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewResVO
	    */
	  public com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewResVO paiquerywhetherrenewuccqueryWhetherRenew(com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewReqVO inputData) {
	        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
	        boolean dealSwitch = false;
	        if (applicationMap.get(Constants.DEALSWITCH) != null) {
	            dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                    .get(Constants.DEALSWITCH)).getParaValue());
	        }
	        //hessian地址
	        String hsUrl =  "http://127.0.0.1:8090/ls/remoting/queryWhetherRenewUCCqueryWhetherRenewAddr";
	        //报文参数
	        //公共处理报文的函数，将报文头赋值
	        SysHeader sysheader = new SysHeader();
	        BizHeader reqhead = new BizHeader();      
	        // 定义系统报文体
	        com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.SrvReqBody srvReqBody = new 
	              com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.SrvReqBody();
	        // 定义业务报文体
	        com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.SrvReqBizBody bizBody = new 
	              com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.SrvReqBizBody();
	        String dealTime = DateUtilsEx.getTodayTime();
	        String dealNo = "pa" + "_" + "iquerywhetherrenewuccws" + "_" + "queryWhetherRenew";
	      
	        srvReqBody.setBizBody(bizBody);
	        srvReqBody.setBizHeader(reqhead);
	        srvReqBody.getBizBody().setInputData(inputData);
	        //添加调用前的日志输出
	        try {
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	        } catch (Exception e3) {
	            e3.printStackTrace();
	            logger.debug("解析报文的JSON字符串发生异常");
	            throw new RuntimeException(e3);
	        }
	        if (dealSwitch) {
	            logger.debug("开始记录交易请求日志");
	            CommonDealManagement.beforeCommonDealManagement(sysheader, srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                    DealTrigger.HESSIAN, dealNo, dealTime);
	        }    
	        //返回参数
	        com.nci.tunan.pa.interfaces.serviceData.commonquery.QueryWhetherRenewResVO result = null;
	        ServiceResult tempresult = new ServiceResult();
	        com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.SrvResBody();
	        String dealStatus = "2";
	        try {
	            //创建hessian工厂
	            HessianProxyFactory factory = new HessianProxyFactory();
	            //通过工厂创建接口实例
	            com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.hs.IQueryWhetherRenewUCCHS iquerywhetherrenewucchs = (com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.hs.IQueryWhetherRenewUCCHS) factory.create(hsUrl);
	            //通过接口实例调用接口方法
	            tempresult = iquerywhetherrenewucchs.queryWhetherRenew(sysheader, srvReqBody);
	            //返回值赋值
	            resultbody = 
	                    (com.nci.tunan.pa.interfaces.commonquery.exports.iquerywhetherrenewucc.querywhetherrenew.SrvResBody)tempresult.getResponse();
	            result = resultbody.getBizBody().getOutputData();
	            //添加调用后的日志输出
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
	                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
	                        + DataSerialJSon.fromObject(tempresult.getResponse()));
	            logger.debug("hessian调用接口成功");
	         } catch (Exception e) {
	            e.printStackTrace();
	            logger.debug("hessian调用业务接口异常");
	            dealStatus = "3";
	            throw new RuntimeException(e);
	         } finally {
	            if (dealSwitch && resultbody != null) {
	                logger.debug("开始记录交易响应日志");
	                CommonDealManagement.afterCommonDealManagement(sysheader,
	                        resultbody.getBizHeader(), resultbody.getBizBody(), DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
	            }
	            
	         }
	        return result;
	       
	  }
	  
	  /**
	   * 
	   * @description 计算投连万能扣费信息接口
	   * @version
	   * @title
	   * <AUTHOR>
	   * @param inputData
	   * @return
	   */
	  public com.nci.tunan.pa.interfaces.serviceData.endcase.AboutFeeResData paiaboutfeeuccqueryAboutFee(
	            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData) {
	        Map<String, Object> applicationMap = ParaDefInitConst
	                .getApplicationConst();
	        boolean dealSwitch = false;
	        if (applicationMap.get(Constants.DEALSWITCH) != null) {
	            dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                    .get(Constants.DEALSWITCH)).getParaValue());
	        }
	        // hessian地址
//	        String hsUrl = "http://**********:9080/ls/remoting/aboutFeeUCCqueryAboutFeeAddr";
	        String hsUrl = "http://localhost:8080/ls/remoting/aboutFeeUCCqueryAboutFeeAddr";
	        // 报文参数
	        // 公共处理报文的函数，将报文头赋值
	        SysHeader sysheader = new SysHeader();
	        BizHeader reqhead = new BizHeader();
	        // 定义系统报文体
	        com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.SrvReqBody();
	        // 定义业务报文体
	        com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.SrvReqBizBody();
	        String dealTime = DateUtilsEx.getTodayTime();
	        String dealNo = "pa" + "_" + "iaboutfeeuccws" + "_" + "queryAboutFee";

	        srvReqBody.setBizBody(bizBody);
	        srvReqBody.setBizHeader(reqhead);
	        srvReqBody.getBizBody().setInputData(inputData);
	        // 添加调用前的日志输出
	        try {
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
	                    + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
	                    + DataSerialJSon.fromObject(srvReqBody));
	        } catch (Exception e3) {
	            e3.printStackTrace();
	            logger.debug("解析报文的JSON字符串发生异常");
	        }
	        if (dealSwitch) {
	            logger.debug("开始记录交易请求日志");
	            CommonDealManagement.beforeCommonDealManagement(sysheader,
	                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                    DealTrigger.HESSIAN, dealNo, dealTime);
	        }
	        // 返回参数
	        com.nci.tunan.pa.interfaces.serviceData.endcase.AboutFeeResData result = null;
	        ServiceResult tempresult = new ServiceResult();
	        com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.SrvResBody();
	        String dealStatus = "2";
	        try {
	            // 创建hessian工厂
	            HessianProxyFactory factory = new HessianProxyFactory();
	            // 通过工厂创建接口实例
	            com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.hs.IAboutFeeUCCHS iaboutfeeucchs = (com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.hs.IAboutFeeUCCHS) factory
	                    .create(hsUrl);
	            // 通过接口实例调用接口方法
	            tempresult = iaboutfeeucchs.queryAboutFee(sysheader, srvReqBody);
	            // 返回值赋值
	            resultbody = (com.nci.tunan.pa.interfaces.aboutfee.exports.iaboutfeeucc.queryaboutfee.SrvResBody) tempresult
	                    .getResponse();
	            result = resultbody.getBizBody().getOutputData();
	            // 添加调用后的日志输出
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                    + "-技术报文头："
	                    + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                    + "-技术报文体："
	                    + DataSerialJSon.fromObject(tempresult.getResponse()));
	            logger.debug("hessian调用接口成功");
	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.debug("hessian调用业务接口异常");
	            dealStatus = "3";
	        } finally {
	            if (dealSwitch && resultbody != null) {
	                logger.debug("开始记录交易响应日志");
	                CommonDealManagement.afterCommonDealManagement(sysheader,
	                        resultbody.getBizHeader(), resultbody.getBizBody(),
	                        DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
	            }

	        }
	        return result;
	    }
	  
	  @Test
	  public void paiaboutfeeuccqueryAboutFeeTest() {
	      EndCaseReqData inputDate = new EndCaseReqData();
	      inputDate.setPolicyCode("990029620706");
	      inputDate.setBusiItemId(new BigDecimal(283286));
	      inputDate.setDateFlag(DateUtilsEx.formatToDate("2020-6-1", "yyyy-MM-dd"));
	      this.paiaboutfeeuccqueryAboutFee(inputDate);
	  }
	 
	  /**
	     * @description 查询投连万能账户价值
	     * @param inputData
	     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.
	     *            EndCaseReqData
	     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.
	     *         InvestValueResData
	     */
	    public com.nci.tunan.pa.interfaces.serviceData.endcase.InvestValueResData paiinvestvalueuccqueryInvestValue(
	            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData) {
	        Map<String, Object> applicationMap = ParaDefInitConst
	                .getApplicationConst();
	        boolean dealSwitch = false;
	        if (applicationMap.get(Constants.DEALSWITCH) != null) {
	            dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                    .get(Constants.DEALSWITCH)).getParaValue());
	        }
	        // hessian地址
//	        String hsUrl = "http://*********:8090/ls/"  + "remoting/investValueUCCqueryInvestValueAddr";
	        String hsUrl = "http://10.8.28.249:8080/ls/remoting/investValueUCCqueryInvestValueAddr";
	        // 报文参数
	        // 公共处理报文的函数，将报文头赋值
	        SysHeader sysheader = new SysHeader();
	        BizHeader reqhead = new BizHeader();
	        // 定义系统报文体
	        com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.SrvReqBody();
	        // 定义业务报文体
	        com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.SrvReqBizBody();
	        String dealTime = DateUtilsEx.getTodayTime();
	        String dealNo = "pa" + "_" + "iinvestvalueuccws" + "_"
	                + "queryInvestValue";

	        srvReqBody.setBizBody(bizBody);
	        srvReqBody.setBizHeader(reqhead);
	        srvReqBody.getBizBody().setInputData(inputData);
	        // 添加调用前的日志输出
	        try {
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
	                    + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
	                    + DataSerialJSon.fromObject(srvReqBody));
	        } catch (Exception e3) {
	            e3.printStackTrace();
	            logger.debug("解析报文的JSON字符串发生异常");
	        }
	        if (dealSwitch) {
	            logger.debug("开始记录交易请求日志");
	            CommonDealManagement.beforeCommonDealManagement(sysheader,
	                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                    DealTrigger.HESSIAN, dealNo, dealTime);
	        }
	        // 返回参数
	        com.nci.tunan.pa.interfaces.serviceData.endcase.InvestValueResData result = null;
	        ServiceResult tempresult = new ServiceResult();
	        com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.SrvResBody();
	        String dealStatus = "2";
	        try {
	            // 创建hessian工厂
	            HessianProxyFactory factory = new HessianProxyFactory();
	            // 通过工厂创建接口实例
	            com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.hs.IInvestValueUCCHS iinvestvalueucchs = (com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.hs.IInvestValueUCCHS) factory
	                    .create(hsUrl);
	            // 通过接口实例调用接口方法
	            tempresult = iinvestvalueucchs.queryInvestValue(sysheader,
	                    srvReqBody);
	            // 返回值赋值
	            resultbody = (com.nci.tunan.pa.interfaces.investvalue.exports.iinvestvalueucc.queryinvestvalue.SrvResBody) tempresult
	                    .getResponse();
	            result = resultbody.getBizBody().getOutputData();
	            // 添加调用后的日志输出
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                    + "-技术报文头："
	                    + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                    + "-技术报文体："
	                    + DataSerialJSon.fromObject(tempresult.getResponse()));
	            logger.debug("hessian调用接口成功");
	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.debug("hessian调用业务接口异常");
	            dealStatus = "3";
	        } finally {
	            if (dealSwitch && resultbody != null) {
	                logger.debug("开始记录交易响应日志");
	                CommonDealManagement.afterCommonDealManagement(sysheader,
	                        resultbody.getBizHeader(), resultbody.getBizBody(),
	                        DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
	            }

	        }
	        return result;
	    }
	    
	    @Test
	    public void paiinvestvalueuccqueryInvestValue() {
	        EndCaseReqData inputDate = new EndCaseReqData();
	        inputDate.setPolicyCode("990033710555");
	        inputDate.setBusiItemId(new BigDecimal(493055));
	        inputDate.setDateFlag(DateUtilsEx.formatToDate("2033-02-01", "yyyy-MM-dd"));
	        this.paiinvestvalueuccqueryInvestValue(inputDate);
	    }
	    
	    /**
	     * @description 校验投保人联系方式接口
	     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.commonquery.CustomerInfoCheckReqVO
	     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.commonquery.CustomerInfoCheckResVO
	    */
	  public com.nci.tunan.pa.interfaces.serviceData.commonquery.CustomerInfoCheckResVO paicustomerinfocheckucccheckHolderMessage(com.nci.tunan.pa.interfaces.serviceData.commonquery.CustomerInfoCheckReqVO inputData) {
	        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
	        boolean dealSwitch = false;
	        if (applicationMap.get(Constants.DEALSWITCH) != null) {
	            dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                    .get(Constants.DEALSWITCH)).getParaValue());
	        }
	   	    //hessian地址
		    String hsUrl =  "http://localhost:8090/ls/"+ "remoting/PA_customerInfoCheckUcccheckHolderMessageAddr";
	        //报文参数
	        //公共处理报文的函数，将报文头赋值
	        SysHeader sysheader = new SysHeader();
	        BizHeader reqhead = new BizHeader();   
	        // 定义系统报文体
	        com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.SrvReqBody srvReqBody = new 
	        		com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.SrvReqBody();
	        // 定义业务报文体
	        com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.SrvReqBizBody bizBody = new 
	        		com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.SrvReqBizBody();
	        String dealTime = DateUtilsEx.getTodayTime();
		    String dealNo = "pa" + "_" + "icustomerinfocheckuccws" + "_" + "checkHolderMessage";
		  
	        srvReqBody.setBizBody(bizBody);
	        srvReqBody.setBizHeader(reqhead);
	        srvReqBody.getBizBody().setInputData(inputData);
	        //添加调用前的日志输出
	        try {
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	        } catch (Exception e3) {
	            e3.printStackTrace();
	            logger.debug("解析报文的JSON字符串发生异常");
	        }
	        if (dealSwitch) {
		        logger.debug("开始记录交易请求日志");
	            CommonDealManagement.beforeCommonDealManagement(sysheader, srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                    DealTrigger.HESSIAN, dealNo, dealTime);
		    }    
	        //返回参数
	        com.nci.tunan.pa.interfaces.serviceData.commonquery.CustomerInfoCheckResVO result = null;
	        ServiceResult tempresult = new ServiceResult();
	        com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.SrvResBody();
	        String dealStatus = "2";
	        try {
	            //创建hessian工厂
	            HessianProxyFactory factory = new HessianProxyFactory();
	            //通过工厂创建接口实例
	            com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.hs.ICustomerInfoCheckUccHS icustomerinfocheckucchs = (com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.hs.ICustomerInfoCheckUccHS) factory.create(hsUrl);
	            //通过接口实例调用接口方法
	            tempresult = icustomerinfocheckucchs.checkHolderMessage(sysheader, srvReqBody);
	            //返回值赋值
	            resultbody = 
	          		    (com.nci.tunan.pa.interfaces.commonquery.exports.icustomerinfocheckucc.checkholdermessage.SrvResBody)tempresult.getResponse();
	            result = resultbody.getBizBody().getOutputData();
	            //添加调用后的日志输出
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
	                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
	                        + DataSerialJSon.fromObject(tempresult.getResponse()));
	            logger.debug("hessian调用接口成功");
	         } catch (Exception e) {
	            e.printStackTrace();
	            logger.debug("hessian调用业务接口异常");
	            dealStatus = "3";
	         } finally {
	            if (dealSwitch && resultbody != null) {
	                logger.debug("开始记录交易响应日志");
	                CommonDealManagement.afterCommonDealManagement(sysheader,
	                        resultbody.getBizHeader(), resultbody.getBizBody(), DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
	            }
	            
	         }
	        return result;
	  }
	  
	  @Test
	  public void paicustomerinfocheckucccheckHolderMessage() {
		  CustomerInfoCheckReqVO inputVO = new CustomerInfoCheckReqVO();
		  inputVO.setAgentCode("8621");
		  inputVO.setCustomerId(new BigDecimal(72718));
		  inputVO.setMobileTel("13200011272");
		  this.paicustomerinfocheckucccheckHolderMessage(inputVO);
	  }

	   /**
	     * @description 非实时保单查询（保单）
	     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.policyquerydelayed.PolicyQueryDelayedReqVO
	     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.policyquerydelayed.PolicyQueryDelayedResVO  
	    */
	  public com.nci.tunan.pa.interfaces.serviceData.policyquerydelayed.PolicyQueryDelayedResVO paipolicyquerydelayeduccpolicyQueryDelayed(com.nci.tunan.pa.interfaces.serviceData.policyquerydelayed.PolicyQueryDelayedReqVO inputData) {
	        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
	        boolean dealSwitch = false;
	        if (applicationMap.get(Constants.DEALSWITCH) != null) {
	            dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                    .get(Constants.DEALSWITCH)).getParaValue());
	        }
	        //调用的webService的wsdl
	        String wsdl =  "http://**********:8090/ls/"+ "webservice/PA_policyQueryDelayedUccpolicyQueryDelayedAddr?wsdl";
//	        if(Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("321509") != null && Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("321509").equals("Y")) {
//	           wsdl = $esbUrl + Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("321509") + "?wsdl";
//	        } 
	        //服务接口路径
	        String interfacePath = "com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.ws.IPolicyQueryDelayedUccWS";
	        //报文参数
	        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
	        Holder<com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.SrvResBody> parametersResBody = new 
	                Holder<com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.SrvResBody>();
	        //公共处理报文的函数，将报文头赋值
	        SysHeader sysheader = new SysHeader();
	        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("321509"));
	        BizHeader reqhead = new BizHeader();
	        parametersResHeader.value = sysheader;
	        // 定义系统报文体
	        com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.SrvReqBody srvReqBody = new 
	                 com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.SrvReqBody();
	        // 定义业务报文体
	        com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.SrvReqBizBody bizBody = new 
	                com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.SrvReqBizBody();
	        String dealTime = DateUtilsEx.getTodayTime();
	        String dealNo = "pa" + "_" + "ipolicyquerydelayeduccws" + "_" + "policyQueryDelayed";  
	        srvReqBody.setBizBody(bizBody);
	        srvReqBody.setBizHeader(reqhead);
	        srvReqBody.getBizBody().setInputData(inputData);
	        //添加调用前的日志输出
	        try {
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	        } catch (Exception e3) {
	            e3.printStackTrace();
	            logger.debug("解析报文的JSON字符串发生异常");
	        }
	        if (dealSwitch) {
	           logger.debug("开始记录交易请求日志");
	           CommonDealManagement.beforeCommonDealManagement(sysheader, srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                    DealTrigger.WEBSERVICE, dealNo, dealTime); 
	        }
	        //返回参数
	        com.nci.tunan.pa.interfaces.serviceData.policyquerydelayed.PolicyQueryDelayedResVO result = null;
	        String dealStatus = "2";
	        try {
	            //调用公共webservice工厂方法
	            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdl, interfacePath);
	            //通过工厂创建接口实例
	            com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.ws.IPolicyQueryDelayedUccWS ipolicyquerydelayeduccws = (com.nci.tunan.pa.interfaces.interfaceforchannel.exports.ipolicyquerydelayeducc.policyquerydelayed.ws.IPolicyQueryDelayedUccWS) soapFactory.create();
	            //调用接口的指定方法，将参数传入
	            ipolicyquerydelayeduccws.policyQueryDelayed(sysheader, srvReqBody, parametersResHeader, parametersResBody);
	            //将业务报文体的outputData赋值给返回值
	            result = parametersResBody.value.getBizBody().getOutputData(); 
	            logger.info(XmlHelper.classToXml(result));
	            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
	            CommonHeaderDeal.setBIZHEADERTHREAD(parametersResBody.value.getBizHeader());
	            //添加调用后的日志输出
	            logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
	                        + DataSerialJSon.fromObject(sysheader));
	            logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
	                        + DataSerialJSon.fromObject(parametersResBody));
	            logger.debug("webservice调用接口成功");
	        } catch (BizException e) {
	            e.printStackTrace();
	            logger.debug("webservice调用业务接口异常");
	            dealStatus = "3";
	        } catch (ClassNotFoundException e1) {
	            e1.printStackTrace();
	            logger.debug("找不到接口类");
	            dealStatus = "3";
	        } catch (Exception e2) {
	            e2.printStackTrace();
	            logger.debug("解析返回的报文的JSON字符串发生异常");
	            dealStatus = "3";
	        } finally {  
	            if (dealSwitch && parametersResBody.value == null) {
	                logger.debug("开始记录交易响应日志");
	                CommonDealManagement.afterCommonDealManagement(sysheader,
	                        null, null, DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
	            } else if (dealSwitch && parametersResBody.value != null) {
	                logger.debug("开始记录交易响应日志");
	                CommonDealManagement.afterCommonDealManagement(sysheader,
	                        parametersResBody.value.getBizHeader(), parametersResBody.value.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
	            }
	            
	        }
	        return result;
	   }

      @Test
      public void paipolicyquerydelayeduccpolicyQueryDelayed() {
          PolicyQueryDelayedReqVO inputVO = new PolicyQueryDelayedReqVO();
//          inputVO.setBatchDate(DateUtilsEx.formatToDate("2017-6-26", "yyyy-MM-dd"));
          List<String> policyCodeList = new ArrayList<String>();
          policyCodeList.add("884851586533");
          /** 查询类型     /** 01-有效 
           * 02-犹豫期内退保(021-犹豫期内部分退保 022-犹豫期内全额退保 ) 03-犹豫期外退保(031-保障期内部分退保 032-保障期内全额退保) 
           * 04-续期缴费 05-满期给付 06-加保 07-减保 08-理赔终止 09-保单失效*/
          List<String> queryTypeList = new ArrayList<String>();
          queryTypeList.add("02");
          queryTypeList.add("03");
          inputVO.setQueryTypeList(queryTypeList);
          inputVO.setServiceBank("01");
          inputVO.setPolicyCodeList(policyCodeList);
          logger.info(XmlHelper.classToXml(inputVO));
          this.paipolicyquerydelayeduccpolicyQueryDelayed(inputVO);
      }
      /**
       * 过滤重复客户
       * @description
       * @version V1.0.0
       * @title
       * <AUTHOR>
       * @param inputData
       * @return
       */
      public com.nci.tunan.pa.interfaces.serviceData.querycusfivebasicbytel.QueryCusFiveBasicByTelResVO queryCusFiveBasicByTel(
    		  com.nci.tunan.pa.interfaces.serviceData.querycusfivebasicbytel.QueryCusFiveBasicByTelReqVO inputData) {
	      // hessian地址
	      String hsUrl = "http://localhost:8080/ls/remoting/queryCusFiveBasicByTelUCCqueryCusFiveBasicByTelAddr";
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.debug("解析报文的JSON字符串发生异常");
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.serviceData.querycusfivebasicbytel.QueryCusFiveBasicByTelResVO result = null;
	      try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          // 通过工厂创建接口实例
	          com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.hs.IQueryCusFiveBasicByTelUCCHS querycusfivebasicbytelucchs = (com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.hs.IQueryCusFiveBasicByTelUCCHS) factory
	                  .create(hsUrl);
	          // 通过接口实例调用接口方法
	          ServiceResult tempresult = querycusfivebasicbytelucchs.queryCusFiveBasicByTel(sysheader, srvReqBody);
	          // 返回值赋值
	          com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.querycusfivebasicbytel.exports.iquerycusfivebasicbytelucc.querycusfivebasicbytel.SrvResBody) tempresult
	                  .getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.debug("hessian调用业务接口异常");
	      }
	      return result;
	  }
      
      @Test
	  public void testqueryCusFiveBasicByTel(){
    	  QueryCusFiveBasicByTelReqVO inputData = new QueryCusFiveBasicByTelReqVO();
    	  inputData.setMobileTel("19029390455");
    	  Map<String, String> map = new HashMap<String, String>();
    	  map.put("customerName", "港澳台");
    	  map.put("customerBirthday", "1987-06-06");
    	  map.put("customerGender", "1");
//    	  map.put("certiType", "d");
    	  map.put("customerCertiCode", "19870606001");
    	  inputData.setCustomerEssential(map);
    	  inputData.setOrganCode("86");
    	  inputData.setAppFlag("投保人5");
    	  List<String> holderCertiTypes = new ArrayList<String>();
    	  holderCertiTypes.add("b");
    	  holderCertiTypes.add("d");
    	  holderCertiTypes.add("i");
    	  inputData.setHolderCertiTypes(holderCertiTypes);
    	  List<String> liabilityStateList = new ArrayList<String>();
    	  liabilityStateList.add("1");
    	  inputData.setLiabilityStateList(liabilityStateList);
    	  logger.info(XmlHelper.classToXml(inputData));
    	  QueryCusFiveBasicByTelResVO outData = new QueryCusFiveBasicByTelResVO();
    	  outData = this.queryCusFiveBasicByTel(inputData);
    	  logger.info(XmlHelper.classToXml(outData));
	  } 
      
      
      public com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodResVO queryGuaranteePeriodParam(
    		  com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodReqVO inputData) {
	      // hessian地址
	      String hsUrl = "http://10.8.28.249:8080/ls/remoting/guaranteePeriodParamUCCqueryGuaranteePeriodParamAddr";
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.debug("解析报文的JSON字符串发生异常");
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodResVO result = null;
	      try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          // 通过工厂创建接口实例
	          com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.hs.IGuaranteePeriodParamUCCHS guaranteeperiodparamucchs = (com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.hs.IGuaranteePeriodParamUCCHS) factory
	                  .create(hsUrl);
	          // 通过接口实例调用接口方法
	          ServiceResult tempresult = guaranteeperiodparamucchs.queryGuaranteePeriodParam(sysheader, srvReqBody);
	          // 返回值赋值
	          com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.guaranteePeriod.iguaranteeperiodparamucc.queryguaranteeperiodparam.SrvResBody) tempresult
	                  .getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.debug("hessian调用业务接口异常");
	      }
	      return result;
	  }
      
      @Test
      public void  queryGuaranteePeriodParamtest(){
    	  GuaranteePeriodReqVO input = new GuaranteePeriodReqVO();
    	  input.setBusiItemId(new BigDecimal(496355));
    	  input.setPayDueDate(DateUtilsEx.formatToDate("2082-05-01", "yyyy-MM-dd"));
    	  GuaranteePeriodResVO  outdata=this.queryGuaranteePeriodParam(input);
    	  logger.info(XmlHelper.classToXml(outdata));
      }
      
      
      
      
      public com.nci.tunan.pa.interfaces.serviceData.dayTradeRevoke.DayTradeRevokeResVo dayTradeRevoke(
    		  com.nci.tunan.pa.interfaces.serviceData.dayTradeRevoke.DayTradeRevokeReqVo inputData) {
	      // hessian地址
	      String hsUrl = "http://localhost:8080/ls/remoting/dayTradeRevokeUCCdayTradeRevokeAddr";
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.debug("解析报文的JSON字符串发生异常");
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.serviceData.dayTradeRevoke.DayTradeRevokeResVo result = null;
	      try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          // 通过工厂创建接口实例
	          com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.hs.IDayTradeRevokeUCCHS daytraderevokeucchs = (com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.hs.IDayTradeRevokeUCCHS) factory
	                  .create(hsUrl);
	          // 通过接口实例调用接口方法
	          ServiceResult tempresult = daytraderevokeucchs.dayTradeRevoke(sysheader, srvReqBody);
	          // 返回值赋值
	          com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.dayTradeRevoke.exports.idaytraderevokeucc.daytraderevoke.SrvResBody) tempresult
	                  .getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.debug("hessian调用业务接口异常");
	      }
	      return result;
	  }
      @Test
      public void  dayTradeRevoke(){
    	  DayTradeRevokeReqVo input = new DayTradeRevokeReqVo();
    	  DayTradeRevokeResVo out = new DayTradeRevokeResVo();
    	  input.setApplyCode("00000001428555");
    	  input.setExpiryDate(DateUtilsEx.formatToDate("2018-09-18", "yyyy-MM-dd"));
    	 // input.setFlag("1");
    	  out = this.dayTradeRevoke(input);
    	  logger.info(XmlHelper.classToXml(out));
      }
      @Test
      public void test1(){
    	  String dateFormat = "";
          Calendar cal = Calendar.getInstance();
          cal.set(Calendar.YEAR, 2018);
          cal.set(Calendar.MONTH, 9);
          cal.set(Calendar.DAY_OF_MONTH, -1);
          if (dateFormat == null || dateFormat.equals(PubConstants.BLANK)) {
              dateFormat = PubConstants.DATE_FORMAT;
          }
          String date = new SimpleDateFormat(dateFormat).format(cal.getTime());
           
      }
      /**
       * 风险累计
       * @param inputData
       * @return
       */
      public com.nci.tunan.pa.interfaces.serviceData.riskquery.QueryRiskAmountResVO queryRiskAmount(
    		  com.nci.tunan.pa.interfaces.serviceData.riskquery.QueryRiskAmountReqVO inputData) {
	      // hessian地址
	      String hsUrl = "http://localhost:8080/ls/remoting/riskAmountUCCqueryRiskAmountAddr";
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.debug("解析报文的JSON字符串发生异常");
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.serviceData.riskquery.QueryRiskAmountResVO result = null;
	      try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          // 通过工厂创建接口实例
	          com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.hs.IRiskAmountUCCHS querycusfivebasicbytelucchs = (com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.hs.IRiskAmountUCCHS) factory
	                  .create(hsUrl);
	          // 通过接口实例调用接口方法
	          ServiceResult tempresult = querycusfivebasicbytelucchs.queryRiskAmount(sysheader, srvReqBody);
	          // 返回值赋值
	          com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryriskamount.SrvResBody) tempresult
	                  .getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.debug("hessian调用业务接口异常");
	      }
	      return result;
	  } 
      @Test
     public void testqueryRiskAmount(){
    	  QueryRiskAmountResVO out = new QueryRiskAmountResVO();
    	  QueryRiskAmountReqVO input = new QueryRiskAmountReqVO();
    	  input.setCustomerId("20258");
    	  out = this.queryRiskAmount(input);
    	  logger.info(XmlHelper.classToXml(out));
      }
      /**
       *保单打印
       * @param inputData
       * @return
       */
       public com.nci.tunan.pa.interfaces.query.exports.queryforprint.vo.QueryForPrintResData queryForPrint(
    		   com.nci.tunan.pa.interfaces.query.exports.queryforprint.vo.QueryForPrintReqData inputData) {
 	      // hessian地址
 	      String hsUrl = "http://localhost:8080/ls/remoting/queryForPrintUCCqueryForPrintAddr";
 	      //String hsUrl = "http://**********:9082/ls/remoting/countFeeUCCcountFeeAddr";
 	      
 	      // 报文参数
 	      // 公共处理报文的函数，将报文头赋值
 	      SysHeader sysheader = new SysHeader();
 	      BizHeader reqhead = new BizHeader();
 	      // 定义系统报文体
 	     com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.SrvReqBody();
 	      // 定义业务报文体
 	    com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.SrvReqBizBody();
 	      srvReqBody.setBizBody(bizBody);
 	      srvReqBody.setBizHeader(reqhead);
 	      srvReqBody.getBizBody().setInputData(inputData);
 	      // 添加调用前的日志输出
 	      try {
 	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
 	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
 	      } catch (Exception e3) {
 	          e3.printStackTrace();
 	          logger.debug("解析报文的JSON字符串发生异常");
 	      }
 	      // 返回参数
 	     com.nci.tunan.pa.interfaces.query.exports.queryforprint.vo.QueryForPrintResData result = null;
 	      try {
 	          // 创建hessian工厂
 	          HessianProxyFactory factory = new HessianProxyFactory();
 	          // 通过工厂创建接口实例
 	         com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.hs.IQueryForPrintUCCHS querycusfivebasicbytelucchs = (com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.hs.IQueryForPrintUCCHS) factory
 	                  .create(hsUrl);
 	          // 通过接口实例调用接口方法
 	          ServiceResult tempresult = querycusfivebasicbytelucchs.queryForPrint(sysheader, srvReqBody);
 	          // 返回值赋值
 	         com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.queryforprint.exports.iqueryforprintucc.queryforprint.SrvResBody) tempresult
 	                  .getResponse();
 	          result = resultbody.getBizBody().getOutputData();
 	          // 添加调用后的日志输出
 	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
 	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
 	          logger.debug("hessian调用接口成功");
 	      } catch (Exception e) {
 	          e.printStackTrace();
 	          logger.debug("hessian调用业务接口异常");
 	      }
 	      return result;
 	  }  
      @Test
       public void testqueryForPrint(){
    	   QueryForPrintReqData inputdata = new QueryForPrintReqData();
    	   inputdata.setPolicyCode("990018080232");
    	   this.queryForPrint(inputdata);
    	   QueryForPrintResData outdata = new QueryForPrintResData();
    	   logger.info(XmlHelper.classToXml(outdata));
       }
      
      
    @Test
    public void testquerypapersupplement() {
    	com.nci.tunan.pa.interfaces.serviceData.querypapersupplement.QueryPaperSupplementReqData inputData = new com.nci.tunan.pa.interfaces.serviceData.querypapersupplement.QueryPaperSupplementReqData();
    	inputData.setPolicyCode("990040734605");
    	inputData.setOldPolicyCode("990040734605");
    	com.nci.tunan.pa.interfaces.serviceData.querypapersupplement.QueryPaperSupplementResData result = querypapersupplement(inputData);
    	System.out.println(XmlHelper.classToXml(result));
    	
    }
      
	public com.nci.tunan.pa.interfaces.serviceData.querypapersupplement.QueryPaperSupplementResData querypapersupplement(
			com.nci.tunan.pa.interfaces.serviceData.querypapersupplement.QueryPaperSupplementReqData inputData) {
		// hessian地址
		String hsUrl = "http://localhost:8080/ls/remoting/queryPaperSupplementUCCquerypapersupplementAddr";
//		String hsUrl = "http://**********:9080/ls/remoting/queryPaperSupplementUCCquerypapersupplementAddr";
		
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
  	      srvReqBody.setBizHeader(reqhead);
  	      srvReqBody.getBizBody().setInputData(inputData);
  	      // 添加调用前的日志输出
  	      try {
  	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
  	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
  	      } catch (Exception e3) {
  	          e3.printStackTrace();
  	          logger.debug("解析报文的JSON字符串发生异常");
  	      }
  	    com.nci.tunan.pa.interfaces.serviceData.querypapersupplement.QueryPaperSupplementResData result = null;
  	    try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.hs.IQueryPaperSupplementUCCHS queryPaperSupplementUCCHS = 
	        		  (com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.hs.IQueryPaperSupplementUCCHS)factory.create(hsUrl);
	          // 通过接口实例调用接口方法
  	          ServiceResult tempresult = queryPaperSupplementUCCHS.querypapersupplement(sysheader, srvReqBody);
  	          com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.SrvResBody resultbody =
  	        		(com.nci.tunan.pa.interfaces.querypapersupplement.exports.iquerypapersupplementucc.querypapersupplement.SrvResBody)tempresult.getResponse();
  	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
  	    } catch (Exception e) {
  	    	e.printStackTrace();
  	    	logger.debug("hessian调用业务接口异常");
  	    }
  	    return result;
      }
      
       /**
        *契约累计保费保额接口
        * @param inputData
        * @return
        */
        public com.nci.tunan.pa.interfaces.serviceData.countfee.CountFeeResData countFee(
      		  com.nci.tunan.pa.interfaces.serviceData.countfee.CountFeeReqData inputData) {
  	      // hessian地址
  	      String hsUrl = "http://10.8.28.249:8080/ls/remoting/countFeeUCCcountFeeAddr";
  	      //String hsUrl = "http://**********:9080/ls/remoting/countFeeUCCcountFeeAddr";
  	      
  	      // 报文参数
  	      // 公共处理报文的函数，将报文头赋值
  	      SysHeader sysheader = new SysHeader();
  	      BizHeader reqhead = new BizHeader();
  	      // 定义系统报文体
  	      com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.SrvReqBody();
  	      // 定义业务报文体
  	      com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.SrvReqBizBody();
  	      srvReqBody.setBizBody(bizBody);
  	      srvReqBody.setBizHeader(reqhead);
  	      srvReqBody.getBizBody().setInputData(inputData);
  	      // 添加调用前的日志输出
  	      try {
  	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
  	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
  	      } catch (Exception e3) {
  	          e3.printStackTrace();
  	          logger.debug("解析报文的JSON字符串发生异常");
  	      }
  	      // 返回参数
  	      com.nci.tunan.pa.interfaces.serviceData.countfee.CountFeeResData result = null;
  	      try {
  	          // 创建hessian工厂
  	          HessianProxyFactory factory = new HessianProxyFactory();
  	          // 通过工厂创建接口实例
  	          com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.hs.ICountFeeUCCHS querycusfivebasicbytelucchs = (com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.hs.ICountFeeUCCHS) factory
  	                  .create(hsUrl);
  	          // 通过接口实例调用接口方法
  	          ServiceResult tempresult = querycusfivebasicbytelucchs.countFee(sysheader, srvReqBody);
  	          // 返回值赋值
  	          com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.countfee.exports.icountfeeucc.countfee.SrvResBody) tempresult
  	                  .getResponse();
  	          result = resultbody.getBizBody().getOutputData();
  	          // 添加调用后的日志输出
  	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
  	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
  	          logger.debug("hessian调用接口成功");
  	      } catch (Exception e) {
  	          e.printStackTrace();
  	          logger.debug("hessian调用业务接口异常");
  	      }
  	      return result;
  	  } 
        @Test
        public void TestcountFee(){
			CountFeeReqData input = new CountFeeReqData();
			CountFeeResData out = new CountFeeResData();
			
//			<customerId>439784</customerId>
//			  <busiProdCode>00863000</busiProdCode>
//			  <premFreq>1</premFreq>
//			  <holderCustomerId>439784</holderCustomerId>
//			  <insuredCustomerId>439784</insuredCustomerId>
//			  <applyDate>2023-04-13 16:00:00.0 UTC</applyDate>
//			  <separateInsuranceFlag>0</separateInsuranceFlag>
			
			input.setCustomerId(new BigDecimal("439784"));
			input.setBusiProdCode("00863000");
			input.setPremFreq(BigDecimal.ONE);
			input.setHolderCustomerId(new BigDecimal("439784"));
			input.setInsuredCustomerId(new BigDecimal("439784"));
			input.setApplyDate(DateUtilsEx.formatToDate("2023-04-14", "yyyy-MM-dd"));
			input.setSeparateInsuranceFlag("0");
			
//	      	<ilogRuleCode>00Z204</ilogRuleCode>
//		      <policyRoleType>2</policyRoleType>
//		      <calBusiProdCode>1,2</calBusiProdCode>
//		      <ruleCalType>6</ruleCalType>
//		      <isCalcFlag>1</isCalcFlag>
//		      <numYear>2</numYear>
			
			CountFeeVO vo = new CountFeeVO();
			vo.setIlogRuleCode("00Z204");
			vo.setPolicyRoleType("2");
			vo.setCalBusiProdCode("1,2");
			vo.setRuleCalType("6");
			vo.setIsCalcFlag("2");
			
			List<CountFeeVO> list = new ArrayList<CountFeeVO>();
	
			list.add(vo);
			input.setCountFeeVOs(list);
			logger.info(XmlHelper.classToXml(input));
			out = this.countFee(input);
			logger.info(XmlHelper.classToXml(out));
        }
        
        /**
         * 2年内承保保额累积接口
         * @param inputData
         * @return
         */
        public com.nci.tunan.pa.interfaces.serviceData.riskSpecialQuery.QuerySpecialRiskResVO querySpecialRiskAmount(
        		  com.nci.tunan.pa.interfaces.serviceData.riskSpecialQuery.QuerySpecialRiskReqVO inputData) {
    	      // hessian地址
    	      String hsUrl = "http://**********:8080/ls/remoting/PA_riskAmountUCCquerySpecialRiskAmountAddr";
    	      
    	      // 报文参数
    	      // 公共处理报文的函数，将报文头赋值
    	      SysHeader sysheader = new SysHeader();
    	      BizHeader reqhead = new BizHeader();
    	      // 定义系统报文体
    	      com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.SrvReqBody();
    	      // 定义业务报文体
    	      com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.SrvReqBizBody();
    	      srvReqBody.setBizBody(bizBody);
    	      srvReqBody.setBizHeader(reqhead);
    	      srvReqBody.getBizBody().setInputData(inputData);
    	      // 添加调用前的日志输出
    	      try {
    	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
    	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
    	      } catch (Exception e3) {
    	          e3.printStackTrace();
    	          logger.debug("解析报文的JSON字符串发生异常");
    	      }
    	      // 返回参数
    	      com.nci.tunan.pa.interfaces.serviceData.riskSpecialQuery.QuerySpecialRiskResVO result = null;
    	      try {
    	          // 创建hessian工厂
    	          HessianProxyFactory factory = new HessianProxyFactory();
    	          // 通过工厂创建接口实例
    	          com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.hs.IRiskAmountUCCHS querycusfivebasicbytelucchs = (com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.hs.IRiskAmountUCCHS) factory
    	                  .create(hsUrl);
    	          // 通过接口实例调用接口方法
    	          ServiceResult tempresult = querycusfivebasicbytelucchs.querySpecialRiskAmount(sysheader, srvReqBody);
    	          // 返回值赋值
    	          com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryspecialriskamount.SrvResBody) tempresult
    	                  .getResponse();
    	          result = resultbody.getBizBody().getOutputData();
    	          // 添加调用后的日志输出
    	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
    	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
    	          logger.debug("hessian调用接口成功");
    	      } catch (Exception e) {
    	          e.printStackTrace();
    	          logger.debug("hessian调用业务接口异常");
    	      }
    	      return result;
    	  } 
          @Test
          public void TestquerySpecialRiskAmount(){
        	QuerySpecialRiskReqVO input = new QuerySpecialRiskReqVO();
        	QuerySpecialRiskResVO out = new QuerySpecialRiskResVO();
        	
        	input.setCustomerId("203974");
        	input.setApplyDate(DateUtilsEx.formatToDate("2021-01-02", "yyyy-MM-dd"));
        	
  			logger.info(XmlHelper.classToXml(input));
  			out = this.querySpecialRiskAmount(input);
  			logger.info(XmlHelper.classToXml(out));
          }
        
        @Test
        public void test11(){
        	try {
        		BigDecimal a = new BigDecimal("");
        		System.out.println(a);
			} catch (Exception e) {
				e.printStackTrace();
			}
        	
        }
       
        
        public com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodResData paiishesitationperioduccqueryIsExitPolicy(
        		com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodReqData inputData) {
    	      // hessian地址
    	      String hsUrl = "http://localhost:8080/ls/remoting/PA_isHesitationPeriodUCCqueryIsExitPolicyAddr";
    	      //String hsUrl = "http://**********:9082/ls/remoting/countFeeUCCcountFeeAddr";
    	      
    	      // 报文参数
    	      // 公共处理报文的函数，将报文头赋值
    	      SysHeader sysheader = new SysHeader();
    	      BizHeader reqhead = new BizHeader();
    	      // 定义系统报文体
    	      com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBody();
    	      // 定义业务报文体
    	      com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBizBody();
    	      srvReqBody.setBizBody(bizBody);
    	      srvReqBody.setBizHeader(reqhead);
    	      srvReqBody.getBizBody().setInputData(inputData);
    	      // 添加调用前的日志输出
    	      try {
    	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
    	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
    	      } catch (Exception e3) {
    	          e3.printStackTrace();
    	          logger.debug("解析报文的JSON字符串发生异常");
    	      }
    	      // 返回参数
    	      com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodResData result = null;
    	      try {
    	          // 创建hessian工厂
    	          HessianProxyFactory factory = new HessianProxyFactory();
    	          // 通过工厂创建接口实例
    	          com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.hs.IIsHesitationPeriodUCCHS iishesitationperioduccws = (com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.hs.IIsHesitationPeriodUCCHS) factory
    	                  .create(hsUrl);
    	          // 通过接口实例调用接口方法
    	          ServiceResult tempresult = iishesitationperioduccws.queryIsExitPolicy(sysheader, srvReqBody);
    	          // 返回值赋值
    	          com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvResBody) tempresult
    	                  .getResponse();
    	          result = resultbody.getBizBody().getOutputData();
    	          // 添加调用后的日志输出
    	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
    	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
    	          logger.debug("hessian调用接口成功");
    	      } catch (Exception e) {
    	          e.printStackTrace();
    	          logger.debug("hessian调用业务接口异常");
    	      }
    	      return result;
    	  } 
        @Test
        public void testPA_isHesitationPeriodUCCqueryIsExitPolicyAddr(){
        	ContractBusiProdPO  contractBusiProdPO1 = new ContractBusiProdPO();
    		contractBusiProdPO1.set("busi_item_id_nb", "12345");
    		contractBusiProdPO1.setPolicyId(new BigDecimal("12345"));
    		contractBusiProdPO1.setPolicyCode("12345");
    		contractBusiProdPO1.setApplyCode("12345");
    		contractBusiProdPO1.set("busi_item_id_pas", "12345");
    		/*contractBusiProdPO1 = contractBusiProdDao.addConBusiProdMapping(contractBusiProdPO1);
        	 logger.info(XmlHelper.classToXml(outdata));*/
        }
        
        
        
        
        
        public com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyResData insertPolicy(
        		com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyReqData inputData) {
    	      // hessian地址
    	      String hsUrl = "http://localhost:8080/ls/remoting/createPolicyUCCinsertPolicyAddr";
    	      //String hsUrl = "http://**********:9082/ls/remoting/countFeeUCCcountFeeAddr";
    	      
    	      // 报文参数
    	      // 公共处理报文的函数，将报文头赋值
    	      SysHeader sysheader = new SysHeader();
    	      BizHeader reqhead = new BizHeader();
    	      // 定义系统报文体
    	      com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBody();
    	      // 定义业务报文体
    	      com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvReqBizBody();
    	      srvReqBody.setBizBody(bizBody);
    	      srvReqBody.setBizHeader(reqhead);
    	      srvReqBody.getBizBody().setInputData(inputData);
    	      // 添加调用前的日志输出
    	      try {
    	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
    	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
    	      } catch (Exception e3) {
    	          e3.printStackTrace();
    	          logger.debug("解析报文的JSON字符串发生异常");
    	      }
    	      // 返回参数
    	      com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyResData result = null;
    	      try {
    	          // 创建hessian工厂
    	          HessianProxyFactory factory = new HessianProxyFactory();
    	          // 通过工厂创建接口实例
    	          com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.hs.ICreatePolicyUCCHS iishesitationperioduccws = (com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.hs.ICreatePolicyUCCHS) factory
    	                  .create(hsUrl);
    	          // 通过接口实例调用接口方法
    	          ServiceResult tempresult = iishesitationperioduccws.insertPolicy(sysheader, srvReqBody);
    	          // 返回值赋值
    	          com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.createpolicy.exports.icreatepolicyucc.insertpolicy.SrvResBody) tempresult
    	                  .getResponse();
    	          result = resultbody.getBizBody().getOutputData();
    	          // 添加调用后的日志输出
    	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
    	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
    	          logger.debug("hessian调用接口成功");
    	      } catch (Exception e) {
    	          e.printStackTrace();
    	          logger.debug("hessian调用业务接口异常");
    	      }
    	      return result;
    	  } 
      @Test
        public void testPA_insertPolicy(){
    	  CreatePolicyReqData inputData = new CreatePolicyReqData();
    	  CreatePolicyResData outDate =new  CreatePolicyResData();
    	  
    	  inputData.setPolicy(null);
    	  
        	// logger.info(XmlHelper.classToXml(outdata));
        }
      
      
      public com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailResVO queryProductRiskDetail(
    		  com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailReqVO inputData) {
  	      // hessian地址
  	      String hsUrl = "http://localhost:8080/ls/remoting/riskAmountUCCqueryProductRiskDetailAddr";
  	      //String hsUrl = "http://**********:9082/ls/remoting/countFeeUCCcountFeeAddr";
  	      
  	      // 报文参数
  	      // 公共处理报文的函数，将报文头赋值
  	      SysHeader sysheader = new SysHeader();
  	      BizHeader reqhead = new BizHeader();
  	      // 定义系统报文体
  	    com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvReqBody();
  	      // 定义业务报文体
  	  com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvReqBizBody();
  	      srvReqBody.setBizBody(bizBody);
  	      srvReqBody.setBizHeader(reqhead);
  	      srvReqBody.getBizBody().setInputData(inputData);
  	      // 添加调用前的日志输出
  	      try {
  	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
  	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
  	      } catch (Exception e3) {
  	          e3.printStackTrace();
  	          logger.debug("解析报文的JSON字符串发生异常");
  	      }
  	      // 返回参数
  	    com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailResVO result = null;
  	      try {
  	          // 创建hessian工厂
  	          HessianProxyFactory factory = new HessianProxyFactory();
  	          // 通过工厂创建接口实例
  	        com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.hs.IRiskAmountUCCHS iishesitationperioduccws = (com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.hs.IRiskAmountUCCHS) factory
  	                  .create(hsUrl);
  	          // 通过接口实例调用接口方法
  	          ServiceResult tempresult = iishesitationperioduccws.queryProductRiskDetail(sysheader, srvReqBody);
  	          // 返回值赋值
  	        com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvResBody) tempresult
  	                  .getResponse();
  	          result = resultbody.getBizBody().getOutputData();
  	          // 添加调用后的日志输出
  	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
  	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
  	          logger.debug("hessian调用接口成功");
  	      } catch (Exception e) {
  	          e.printStackTrace();
  	          logger.debug("hessian调用业务接口异常");
  	      }
  	      return result;
  	  } 
  @Test
      public void testPA_queryProductRiskDetail(){
	  QueryProductRiskDetailReqVO inputData = new QueryProductRiskDetailReqVO();
	  
	  QueryProductRiskDetailResVO outDate =new  QueryProductRiskDetailResVO();
	  inputData.setCustomerId("59630");
	  inputData.setFlag("1");
  	  //inputData.setPolicy(null);
	  outDate=this.queryProductRiskDetail(inputData);
      logger.info(XmlHelper.classToXml(outDate));
      }
  
  
  
  public com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodResData queryIsExitPolicy(
		  com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodReqData inputData) {
	      // hessian地址
	      String hsUrl = "http://localhost:8080/ls/remoting/PA_isHesitationPeriodUCCqueryIsExitPolicyAddr";
	      //String hsUrl = "http://**********:9082/ls/remoting/countFeeUCCcountFeeAddr"; 
	      
	      // 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.debug("解析报文的JSON字符串发生异常");
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.serviceData.IsHesitationPeriod.IsHesitationPeriodResData result = null;
	      try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          // 通过工厂创建接口实例
	          com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.hs.IIsHesitationPeriodUCCHS iishesitationperioduccws 
	          = (com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.hs.IIsHesitationPeriodUCCHS) factory
	                  .create(hsUrl);
	          // 通过接口实例调用接口方法
	          ServiceResult tempresult = iishesitationperioduccws.queryIsExitPolicy(sysheader, srvReqBody);
	          // 返回值赋值
	          com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.ishesitationperiod.exports.iishesitationperioducc.queryisexitpolicy.SrvResBody) tempresult
	                  .getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.debug("hessian调用业务接口异常");
	      }
	      return result;
	  } 
  
  @Test
  public void testqueryIsExitPolicy(){
	  IsHesitationPeriodReqData inputData = new IsHesitationPeriodReqData();
  
	  IsHesitationPeriodResData outDate =new  IsHesitationPeriodResData();
	  inputData.setAgentCode("36503416");
	  inputData.setMaxDate(DateUtilsEx.formatToDate("2018-10-31", "yyyy-MM-dd"));
	  inputData.setMinDate(DateUtilsEx.formatToDate("2016-11-01", "yyyy-MM-dd"));
	  
	  //inputData.setPolicy(null);
  outDate=this.queryIsExitPolicy(inputData);
  logger.info(XmlHelper.classToXml(outDate));
  }
  
  @Test 
  public void Testmanqizhongzhi() throws ParseException{
	  String string = "2016-10-24 09:59:06";
	  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	 Date date =  sdf.parse(string);
	 SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMddHmmss");
	  System.out.println(sdf1.format(date)); 
  }
  
  @Test
  public void Testcreatemedical() throws Exception{
	  com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalReqData inputData =
			  new com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalReqData();
	  PolicyMedicalVO policyMedicalVO = new PolicyMedicalVO();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-mm-dd HH:mm:ss");
		//保单基本信息
		ContractMasterVO contractMasterVO = new ContractMasterVO();
		contractMasterVO.setApplyCode("************");
		contractMasterVO.setOrganCode("86510304");
		contractMasterVO.setChannelType("01");
		contractMasterVO.setPolicyId(new BigDecimal("**********"));
		contractMasterVO.setDerivation("1");
		contractMasterVO.setInputDate(sdf.parse("2019-05-29 16:00:00"));
		contractMasterVO.setPolicyType("1");
		contractMasterVO.setExpiryDate(sdf.parse("2018-09-30 16:00:00"));
		contractMasterVO.setSubmitChannel(new BigDecimal("4"));
		contractMasterVO.setLiabilityState(new BigDecimal("0"));
		contractMasterVO.setPolicyCode("test1278");
		contractMasterVO.setIssueDate(sdf.parse("2019-05-29 16:00:00"));
		contractMasterVO.setDerivation("1");
		contractMasterVO.setBranchCode("8651");
		contractMasterVO.setValidateDate(sdf.parse("2018-09-30 16:00:00"));
		contractMasterVO.setMoneyCode("CNY");
		contractMasterVO.setInitialValidateDate(sdf.parse("2018-11-29 16:00:00"));
		contractMasterVO.setApplyDate(sdf.parse("2018-11-29 16:00:00"));
		contractMasterVO.setSubmissionDate(sdf.parse("2018-11-29 16:00:00"));
		contractMasterVO.setInputType("II001");
		contractMasterVO.setLangCode("211");
		contractMasterVO.setOperatorUserCode("0");
		policyMedicalVO.setContractMasterVO(contractMasterVO);
		  
		//MEDICAL
		MedicalVO medicalVO = new MedicalVO();
		medicalVO.setApply_code("190530140118");
		medicalVO.setPolicy_former_no("0000000000010");
		medicalVO.setCustomer_sequence_no("**********");
		medicalVO.setMedical_no("123456");
		policyMedicalVO.setMedicalVO(medicalVO);
		  
		//险种
		  
		List<ContractBusiProdCompVO> contractBusiProdCompVOs = new ArrayList<>();
		ContractBusiProdCompVO compVO = new ContractBusiProdCompVO();
		
		ContractProductCompVO pVo = new ContractProductCompVO();
		List<ContractProductCompVO> pVos = new ArrayList<>();
		ContractProductVO productVO = new ContractProductVO();
		productVO.setMasterProductCode("557000");
		productVO.setIsWaived(new BigDecimal("0"));
		productVO.setApplyCode("************");
		productVO.setOrganCode("86510304");
		productVO.setChargeYear(new BigDecimal("1"));
		productVO.setExtraPremAf(new BigDecimal("0"));
		productVO.setPolicyId(new BigDecimal("**********"));
		productVO.setInitialExtraPremAf(new BigDecimal("0"));
		productVO.setAmount(new BigDecimal("100000"));
		productVO.setPaidupDate(sdf.parse("2019-06-24 16:00:00"));
		productVO.setLiabilityState(new BigDecimal("0"));
		productVO.setCountWay("1");
		productVO.setProductId(new BigDecimal("45711"));
		productVO.setProductCode("557000");
		productVO.setApplyDate(sdf.parse("2019-05-29 16:00:00"));
		productVO.setCoveragePeriod("Y");
		productVO.setCoverageYear(new BigDecimal("1"));
		productVO.setItemId(new BigDecimal("7000031300"));
		productVO.setRenewalExtraPremAf(new BigDecimal("0"));
		productVO.setBusiItemId(new BigDecimal("**********"));
		productVO.setUnit(new BigDecimal("1"));
		productVO.setChargeYear(new BigDecimal("1"));
		productVO.setRenewalDiscntedPremAf(new BigDecimal("136"));
		productVO.setTotalPremAf(new BigDecimal("136"));
		productVO.setStdPremAf(new BigDecimal("136"));
		productVO.setChargePeriod("1");
		productVO.setPremFreq(new BigDecimal("1"));
		productVO.setInitialDiscntPremAf(new BigDecimal("136"));
		productVO.setValidateDate(sdf.parse("2019-05-29 16:00:00"));
		productVO.setUnitNumber("0652782051");
		productVO.setIsRiskMain(new BigDecimal("1"));
		productVO.setDueFeeType("1");
		productVO.setPaidCount(new BigDecimal("1"));
		productVO.setDueTime(sdf.parse("2019-05-29 16:00:00"));
		productVO.setFeeStatus("16");
		productVO.setHandlerName("包明涵");
		productVO.setArapFlag("1");
		
		pVo.setContractProductVO(productVO);
		pVos.add(pVo);
		compVO.setContractProductCompVOs(pVos);
		
		BenefitInsuredCompVO insuredCompVO = new BenefitInsuredCompVO();
		List<BenefitInsuredCompVO> insuredCompVOs = new ArrayList<>();
		insuredCompVO.setListId(new BigDecimal("7000026201"));
		insuredCompVO.setInsuredId(new BigDecimal("7000015948"));
		insuredCompVO.setOrderId(new BigDecimal("1"));
		insuredCompVO.setCustomerId(new BigDecimal("22601"));
		insuredCompVO.setAddressId(new BigDecimal("60582"));
		insuredCompVO.setRelationToPh("00");
		insuredCompVO.setStandLife(new BigDecimal("1"));
		insuredCompVO.setMobileTel("***********");
		insuredCompVO.setRelaToFirstIns("00");
		insuredCompVO.setInsuredAge(new BigDecimal("28"));
		insuredCompVO.setCustomerHeight(new BigDecimal("180"));
		insuredCompVO.setCustomerWeight(new BigDecimal("70"));
		insuredCompVOs.add(insuredCompVO);
		compVO.setBenefitInsuredCompVOs(insuredCompVOs);
		ContractBusiProdVO prodVO = new ContractBusiProdVO();
		prodVO.setAplPermit(new BigDecimal("0"));
		prodVO.setBusiPrdId(new BigDecimal("1347"));
		prodVO.setApplyDate(sdf.parse("2018-11-29 16:00:00"));
		prodVO.setValidateDate(sdf.parse("2018-11-29 16:00:00"));
		prodVO.setInitialValidateDate(sdf.parse("2018-11-29 16:00:00"));
		prodVO.setExpiryDate(sdf.parse("2018-11-29 16:00:00"));
		prodVO.setIssueDate(sdf.parse("2018-11-29 16:00:00"));
		prodVO.setPaidupDate(sdf.parse("2019-06-24 16:00:00"));
		prodVO.setBusiProdCode("00557000");
		prodVO.setIsWaived(new BigDecimal("0"));
		prodVO.setApplyCode("************");
		prodVO.setRenew(new BigDecimal("1"));
		prodVO.setBusiItemId(new BigDecimal("**********"));
		prodVO.setPolicyId(new BigDecimal("**********"));
		prodVO.setWaiver(new BigDecimal("0"));
		prodVO.setMasterBusiItemId(new BigDecimal("0"));
		prodVO.setLiabilityState(new BigDecimal("1"));
		prodVO.setHesitationPeriodDay(new BigDecimal("0"));
		compVO.setContractBusiProdVO(prodVO);
		contractBusiProdCompVOs.add(compVO);
		policyMedicalVO.setContractBusiProdCompVOs(contractBusiProdCompVOs);
		  
		//收付费
		PayerCompVO pCompVO = new PayerCompVO();
		List<PayerCompVO> pList = new ArrayList<>();
		PayerAccountVO accountVO = new PayerAccountVO();
	  	accountVO.setNextAccountId(new BigDecimal("1"));
	  	accountVO.setAccountBank("中国建设银行");
	  	accountVO.setPayerId(new BigDecimal("**********"));
	  	accountVO.setAccount("123456");
	  	accountVO.setPayNext("2");
	  	accountVO.setPayMode("3");
	  	accountVO.setListId(new BigDecimal("**********"));
	  	accountVO.setPolicyId(new BigDecimal("**********"));
	  	pCompVO.setPayerAccountVO(accountVO);
	  	pCompVO.setAddressId(new BigDecimal("60582"));
	  	pCompVO.setCustomerId(new BigDecimal("22601"));
	  	pCompVO.setRelationToPh("00");
	  	pCompVO.setMobileTel("***********");
	  	pCompVO.setListId(new BigDecimal("**********"));
	  	pCompVO.setPolicyId(new BigDecimal("**********"));
	  	pCompVO.setShareRate(new BigDecimal("2"));
	  	pList.add(pCompVO);
	  	policyMedicalVO.setPayerCompVOs(pList);
	  
	  	//投保人
	  	PolicyHolderVO holderVO = new PolicyHolderVO();
	  	List<PolicyHolderVO> holderVOs = new ArrayList<>();
	  	holderVO.setAddressId(new BigDecimal("60582"));
	  	holderVO.setCustomerHeight(new BigDecimal("180"));
	  	holderVO.setCustomerId(new BigDecimal("22601"));
	  	holderVO.setCustomerWeight(new BigDecimal("70"));
	  	holderVO.setApplyCode("************");
	  	holderVO.setListId(new BigDecimal("**********"));
	  	holderVO.setPolicyId(new BigDecimal("**********"));
	  	holderVOs.add(holderVO);
	  	policyMedicalVO.setPolicyHolderVOs(holderVOs);
		  
	  	//告知
	  	CustomerSurveyVO surveyVO1 = new CustomerSurveyVO();
	  	CustomerSurveyVO surveyVO2 = new CustomerSurveyVO();
	  	CustomerSurveyVO surveyVO3 = new CustomerSurveyVO();
	  	CustomerSurveyVO surveyVO4 = new CustomerSurveyVO();
	  	CustomerSurveyVO surveyVO5 = new CustomerSurveyVO();
	  	CustomerSurveyVO surveyVO6 = new CustomerSurveyVO();
	  	List<CustomerSurveyVO> surveyVOs = new ArrayList<>();
	  	QuestionaireCustomerVO question1 = new QuestionaireCustomerVO();
	  	QuestionaireCustomerVO question2 = new QuestionaireCustomerVO();
	  	QuestionaireCustomerVO question3 = new QuestionaireCustomerVO();
	  	QuestionaireCustomerVO question4 = new QuestionaireCustomerVO();
	  	QuestionaireCustomerVO question5 = new QuestionaireCustomerVO();
	  	QuestionaireCustomerVO question6 = new QuestionaireCustomerVO();
	  	question1.setSurveyModuleResult("180,70");
	  	question1.setCustomerId(new BigDecimal("22601"));
	  	question1.setQuestionaireObject("0");
	  	question1.setApplyCode("************");
	  	question1.setSurveyQuestionId(new BigDecimal("1521"));
	  	question1.setCustomerSurveyId(new BigDecimal("1351301"));
	  	question1.setPolicyId(new BigDecimal("**********"));
		  
	  	question2.setSurveyModuleResult("是");
	  	question2.setCustomerId(new BigDecimal("22601"));
	  	question2.setQuestionaireObject("0");
	  	question2.setApplyCode("************");
	  	question2.setSurveyQuestionId(new BigDecimal("1522"));
	  	question2.setCustomerSurveyId(new BigDecimal("1351302"));
		question2.setPolicyId(new BigDecimal("**********"));
		  
		question3.setSurveyModuleResult("是");
		question3.setCustomerId(new BigDecimal("22601"));
		question3.setQuestionaireObject("0");
		question3.setApplyCode("************");
		question3.setSurveyQuestionId(new BigDecimal("1523"));
		question3.setCustomerSurveyId(new BigDecimal("1351303"));
		question3.setPolicyId(new BigDecimal("**********"));
		  
		question4.setSurveyModuleResult("是");
		question4.setCustomerId(new BigDecimal("22601"));
		question4.setQuestionaireObject("0");
		question4.setApplyCode("************");
		question4.setSurveyQuestionId(new BigDecimal("1524"));
		question4.setCustomerSurveyId(new BigDecimal("1351304"));
		question4.setPolicyId(new BigDecimal("**********"));
	  
		question5.setSurveyModuleResult("是");
		question5.setCustomerId(new BigDecimal("22601"));
		question5.setQuestionaireObject("0");
		question5.setApplyCode("************");
		question5.setSurveyQuestionId(new BigDecimal("1525"));
		question5.setCustomerSurveyId(new BigDecimal("1351305"));
		question5.setPolicyId(new BigDecimal("**********"));
	  
		question6.setSurveyModuleResult("是");
		question6.setCustomerId(new BigDecimal("22601"));
		question6.setQuestionaireObject("0");
		question6.setApplyCode("************");
		question6.setSurveyQuestionId(new BigDecimal("1526"));
		question6.setCustomerSurveyId(new BigDecimal("1351306"));
		question6.setPolicyId(new BigDecimal("**********"));
		  
		surveyVO1.setQuestionaireCustomerVO(question1);
		surveyVO2.setQuestionaireCustomerVO(question2);
		surveyVO3.setQuestionaireCustomerVO(question3);
		surveyVO4.setQuestionaireCustomerVO(question4);
		surveyVO5.setQuestionaireCustomerVO(question5);
		surveyVO6.setQuestionaireCustomerVO(question6);
		  
		surveyVOs.add(surveyVO1);
		surveyVOs.add(surveyVO2);
		surveyVOs.add(surveyVO3);
		surveyVOs.add(surveyVO4);
		surveyVOs.add(surveyVO5);
		surveyVOs.add(surveyVO6);
		policyMedicalVO.setCustomerSurveyVOs(surveyVOs);
		  
		//代理人
		ContractAgentVO agentVO = new ContractAgentVO();
		List<ContractAgentVO> agentVOs = new ArrayList<>();
		agentVO.setAgentName("季梅红");
		agentVO.setAgentMobile("15062730616");
		agentVO.setListId(new BigDecimal("**********"));
		agentVO.setAgentOrganCode("360000137799");
		agentVO.setPolicyId(new BigDecimal("**********"));
	  	agentVO.setAgentCode("36785462");
	  	agentVO.setLastAgentCode("36785462");
	  	agentVO.setLastAgentName("季梅红");
	  	agentVO.setIsCurrentAgent(new BigDecimal("1"));
	  	agentVO.setIsNbAgent(new BigDecimal("1"));
	  	agentVO.setOrganCode("86510304");
	  	agentVO.setChannelType("01");
	  	agentVOs.add(agentVO);
	  	policyMedicalVO.setContractAgentVOs(agentVOs);
	  
	  inputData.setPolicy(policyMedicalVO);
	  CreateMedicalResData createMedicalResData = this.paicreatemedicaluccinsertMedical(inputData);
	  System.out.println(createMedicalResData.getResultCode());
  }
  
  /**
	 * @description 新契约调用创建保单。（上海医保）
	 * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalReqData
	 * @return 输出的参数VO:com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalResData
	 */
	public com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalResData paicreatemedicaluccinsertMedical(com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalReqData inputData) throws Exception {
		//hessian地址
		//Constants.SERVICEENVPARAMAP.put("PA", "http://127.0.0.1:8080/ls/");
		String hsUrl = Constants.SERVICEENVPARAMAP.get("PA") + "remoting/createMedicalUCCinsertPolicyAddr";
		// 报文参数
	      // 公共处理报文的函数，将报文头赋值
	      SysHeader sysheader = new SysHeader();
	      BizHeader reqhead = new BizHeader();
	      // 定义系统报文体
	      com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.SrvReqBody();
	      // 定义业务报文体
	      com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.SrvReqBizBody();
	      srvReqBody.setBizBody(bizBody);
	      srvReqBody.setBizHeader(reqhead);
	      srvReqBody.getBizBody().setInputData(inputData);
	      // 添加调用前的日志输出
	      try {
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	      } catch (Exception e3) {
	          e3.printStackTrace();
	          logger.debug("解析报文的JSON字符串发生异常");
	      }
	      // 返回参数
	      com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.vo.CreateMedicalResData result = null;
	      try {
	          // 创建hessian工厂
	          HessianProxyFactory factory = new HessianProxyFactory();
	          // 通过工厂创建接口实例
	          com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.hs.ICreateMedicalUCCHS icreatemedicalucchs = (com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.hs.ICreateMedicalUCCHS) factory.create(hsUrl);
	          // 通过接口实例调用接口方法
	          ServiceResult tempresult = icreatemedicalucchs.insertMedical(sysheader, srvReqBody);
	          // 返回值赋值
	          com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.createmedicalpolicy.exports.icreatemedicalucc.insertmedical.SrvResBody) tempresult
	                  .getResponse();
	          result = resultbody.getBizBody().getOutputData();
	          // 添加调用后的日志输出
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
	          logger.debug("hessian调用接口成功");
	      } catch (Exception e) {
	          e.printStackTrace();
	          logger.debug("hessian调用业务接口异常");
	      }
	      return result;
	}

	  public com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.NBQueryCrsCustomerResData queryCRSCustomer(
			  com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.NBQueryCrsCustomerReqData inputData) {
		      // hessian地址
		      String hsUrl = "http://localhost:8080/ls/remoting/PA_INBQueryCrsCustomerUCCcheckCustomerAddr";
		      //String hsUrl = "http://**********:9082/ls/remoting/countFeeUCCcountFeeAddr";
		      
		      // 报文参数
		      // 公共处理报文的函数，将报文头赋值
		      SysHeader sysheader = new SysHeader();
		      BizHeader reqhead = new BizHeader();
		      // 定义系统报文体
		      com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.SrvReqBody();
		      // 定义业务报文体
		      com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.SrvReqBizBody();
		      srvReqBody.setBizBody(bizBody);
		      srvReqBody.setBizHeader(reqhead);
		      srvReqBody.getBizBody().setInputData(inputData);
		      // 添加调用前的日志输出
		      try {
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
		      } catch (Exception e3) {
		          e3.printStackTrace();
		          logger.debug("解析报文的JSON字符串发生异常");
		      }
		      // 返回参数
		      com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.NBQueryCrsCustomerResData result = null;
		      try {
		          // 创建hessian工厂
		          HessianProxyFactory factory = new HessianProxyFactory();
		          // 通过工厂创建接口实例
		          com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.hs.INBQueryCrsCustomerUCCHS nbQueryCrsCustomerUCCHSImpl 
		          = (com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.hs.INBQueryCrsCustomerUCCHS) factory
		                  .create(hsUrl);
		          // 通过接口实例调用接口方法
		          ServiceResult tempresult = nbQueryCrsCustomerUCCHSImpl.checkCustomer(sysheader, srvReqBody);
		          // 返回值赋值
		          com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.nbquerypolicy.exports.inbquerycrscustomerucc.checkcustomer.SrvResBody) tempresult
		                  .getResponse();
		          result = resultbody.getBizBody().getOutputData();
		          // 添加调用后的日志输出
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
		          logger.debug("hessian调用接口成功");
		      } catch (Exception e) {
		          e.printStackTrace();
		          logger.debug("hessian调用业务接口异常");
		      }
		      return result;
		  } 
	  
	  @Test
	  public void testqueryCRSCustomer(){
		  com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.NBQueryCrsCustomerReqData inputData  
		  = new com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.NBQueryCrsCustomerReqData();
	  
		  com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.NBQueryCrsCustomerResData outDate 
		  =new  com.nci.tunan.pa.interfaces.serviceData.nbquerypolicy.NBQueryCrsCustomerResData();
		  List<CrsCustomerReqVO> customerReqlist = new ArrayList<CrsCustomerReqVO>();
		  CrsCustomerReqVO crsCustomerReq= new CrsCustomerReqVO();
		  customerReqlist.add(crsCustomerReq);
		  crsCustomerReq.setCustomerTaxBirthday(new Date());
		  crsCustomerReq.setCustomerTaxCertiCode("5646532564");
		  crsCustomerReq.setCustomerTaxCertType("0");
		  crsCustomerReq.setCustomerTaxGender(new BigDecimal("1"));
		  crsCustomerReq.setCustomerTaxName("打发的发");
		  crsCustomerReq.setFlag("1");
		  //inputData.setPolicy(null);
		  inputData.setCustomerReqlist(customerReqlist);
	  outDate=this.queryCRSCustomer(inputData);
	  logger.info(XmlHelper.classToXml(outDate));
	  }
		/**
		 * @description 新增通知书
		 * @param inputData
		 *            输入参数VO:com.nci.core.common.interfaces.vo.DocumentVO
		 * @return 输出的参数VO：com.nci.core.common.interfaces.vo.DocumentVO
		 */
		public com.nci.core.common.interfaces.vo.DocumentVO nbidocumentuccsaveDocument(
				com.nci.core.common.interfaces.vo.DocumentVO inputData) {
			boolean dealSwitch = false;
			if (ParaDefInitConst.getApplicationConst().get(Constants.DEALSWITCH) != null) {
				dealSwitch = "1".equals(((ParaDefBO) ParaDefInitConst.getApplicationConst().get(Constants.DEALSWITCH)).getParaValue());
			}
			// hessian地址
			String url = "http://**********:9082/ls/";
			String hsUrl = url+ "remoting/documentUCCsaveDocumentAddr";
			// String hsUrl = "http://**********:9080/nb/" +"remoting/documentUCCsaveDocumentAddr";
			// 报文参数
			// 公共处理报文的函数，将报文头赋值
			SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
			BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);
			// 定义系统报文体
			com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.SrvReqBody srvReqBody = new com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.SrvReqBody();
			// 定义业务报文体
			com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.SrvReqBizBody bizBody = new com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.SrvReqBizBody();
			String dealTime = DateUtilsEx.getTodayTime();
			String dealNo = "nb" + "_" + "idocumentuccws" + "_" + "saveDocument";

			srvReqBody.setBizBody(bizBody);
			srvReqBody.setBizHeader(reqhead);
			srvReqBody.getBizBody().setInputData(inputData);
			// 添加调用前的日志输出
			try {
				logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
						+ DataSerialJSon.fromObject(sysheader));
				logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
						+ DataSerialJSon.fromObject(srvReqBody));
			} catch (Exception e3) {
				e3.printStackTrace();
				logger.debug("解析报文的JSON字符串发生异常");
			}
			if (dealSwitch) {
				logger.debug("开始记录交易请求日志");
				CommonDealManagement.beforeCommonDealManagement(sysheader,
						srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
						DealTrigger.HESSIAN, dealNo, dealTime);
			}
			// 返回参数
			com.nci.core.common.interfaces.vo.DocumentVO result = null;
			ServiceResult tempresult = new ServiceResult();
			com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.SrvResBody resultbody = new com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.SrvResBody();
			String dealStatus = "2";
			try {
				// 创建hessian工厂
				HessianProxyFactory factory = new HessianProxyFactory();
				// 通过工厂创建接口实例
				com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.hs.IDocumentUCCHS idocumentucchs = (com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.hs.IDocumentUCCHS) factory
						.create(hsUrl);
				// 通过接口实例调用接口方法
				tempresult = idocumentucchs.saveDocument(sysheader, srvReqBody);
				// 返回值赋值
				resultbody = (com.nci.core.common.interfaces.document.exports.idocumentucc.savedocument.SrvResBody) tempresult
						.getResponse();
				result = resultbody.getBizBody().getOutputData();
				// 添加调用后的日志输出
				logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
						+ "-技术报文头："
						+ DataSerialJSon.fromObject(tempresult.getSysHeader()));
				logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
						+ "-技术报文体："
						+ DataSerialJSon.fromObject(tempresult.getResponse()));
				logger.debug("hessian调用接口成功");
			} catch (Exception e) {
				e.printStackTrace();
				logger.debug("hessian调用业务接口异常");
				dealStatus = "3";
			} finally {
				if (dealSwitch && resultbody != null) {
					logger.debug("开始记录交易响应日志");
					CommonDealManagement.afterCommonDealManagement(sysheader,
							resultbody.getBizHeader(), resultbody.getBizBody(),
							DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
				}

			}
			return result;
		}
		
		
		@Test
		public void test111() throws ParseException{
			/*com.nci.core.common.interfaces.vo.DocumentVO Input = new com.nci.core.common.interfaces.vo.DocumentVO();
			com.nci.core.common.interfaces.vo.DocumentVO out = new com.nci.core.common.interfaces.vo.DocumentVO();
			out = nbidocumentuccsaveDocument(Input);*/
			com.nci.tunan.mms.interfaces.calc.exports.calcfeeinit.vo.MmsCalcFeeInitReqVO prdInputData = new com.nci.tunan.mms.interfaces.calc.exports.calcfeeinit.vo.MmsCalcFeeInitReqVO();
			SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
			BigDecimal stdPremAf=new BigDecimal(0);//标准保费
			BigDecimal basicPremium = new BigDecimal(0);// 基本保费
			BigDecimal addPremium = new BigDecimal(0);// 追加保费
			BigDecimal extraPremium = new BigDecimal(0);// 额外保费
			prdInputData.setBasicPremium(basicPremium);// 基本保险费
			prdInputData.setAddPremium(addPremium);// 追加保险费
			prdInputData.setExtraPremium(extraPremium);// 额外保险费
			prdInputData.setBusinessPrdId(new BigDecimal(181476));
			prdInputData.setPolicyMonth(new BigDecimal(1));
			prdInputData.setPolicyYear(1);
			prdInputData.setChargeLink("0");
			prdInputData.setPremium(basicPremium.add(addPremium));// 当期缴费金额
			prdInputData.setPremiumSum(basicPremium.add(addPremium));// 累计缴费金额（包括当期缴费金额）
			prdInputData.setProductId(180990);
			prdInputData.setSA(new BigDecimal(0));// 保险金额
			prdInputData.setChargeMode(1);
			prdInputData.setChannel(2);// 产品销售渠道
			prdInputData.setPolicyEffectiveDate(sf.parse("2020-04-08"));
			ApplicationContext context = new ClassPathXmlApplicationContext("");
			//通过spring管理获得ucc接口实例
		    com.nci.tunan.mms.impl.calc.ucc.IMmsCalcFeeInitUcc ucc = (com.nci.tunan.mms.impl.calc.ucc.IMmsCalcFeeInitUcc)ServiceCommonMethod.getBean("PRD_mmsCalcFeeInitUcc");
		    //调用ucc的方法
		    com.nci.tunan.mms.interfaces.calc.exports.calcfeeinit.vo.MmsCalcFeeInitResVO result = null;
			try {
				result = ucc.calcFeeInit(prdInputData);
			} catch (Exception e) {
				e.printStackTrace();
			}
			System.out.println(result);
		}
		
		public com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO paiclaimquerypolicyinfouccqueryPolicyInfo(com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO inputData) {
		    long start = System.currentTimeMillis();
		    Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
		    boolean dealSwitch = false;
		    if (applicationMap.get(Constants.DEALSWITCH) != null) {
		        dealSwitch = "1".equals(((ParaDefBO) applicationMap
		                .get(Constants.DEALSWITCH)).getParaValue());
		    }
		        //hessian地址
//		    String hsUrl = "http://**********:9080/pa/remoting/claimQueryPolicyInfoqueryPolicyInfoAddr";
//		    String hsUrl = Constants.SERVICEENVPARAMAP.get("PA") + "remoting/claimQueryPolicyInfoqueryPolicyInfoAddr";
//		    String hsUrl = "http://**********:9082/ls/remoting/claimQueryPolicyInfoqueryPolicyInfoAddr";
		    String hsUrl = "http://**********:9080/ls/remoting/claimQueryPolicyInfoqueryPolicyInfoAddr";
//		    String hsUrl = "http://localhost:8080/ls/remoting/claimQueryPolicyInfoqueryPolicyInfoAddr";
		    //报文参数
		    //公共处理报文的函数，将报文头赋值
		    SysHeader sysheader = new SysHeader();
		    BizHeader reqhead = new BizHeader();
		    // 定义系统报文体
		    com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.SrvReqBody srvReqBody = new 
		          com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.SrvReqBody();
		    // 定义业务报文体
		    com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.SrvReqBizBody bizBody = new 
		          com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.SrvReqBizBody();
		    String dealTime = DateUtilsEx.getTodayTime();
		    String dealNo = "pa" + "_" + "iclaimquerypolicyinfouccws" + "_" + "queryPolicyInfo";
		  
		    srvReqBody.setBizBody(bizBody);
		    srvReqBody.setBizHeader(reqhead);
		    srvReqBody.getBizBody().setInputData(inputData);
		    //添加调用前的日志输出
		    try {
		        logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
		        logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
		    } catch (Exception e3) {
		        e3.printStackTrace();
		        logger.debug("解析报文的JSON字符串发生异常");
		    }
		    if (dealSwitch) {
		        logger.debug("开始记录交易请求日志");
		        CommonDealManagement.beforeCommonDealManagement(sysheader, srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
		                DealTrigger.HESSIAN, dealNo, dealTime);
		    }    
		    //返回参数
		    com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO result = null;
		    ServiceResult tempresult = new ServiceResult();
		    com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.SrvResBody();
		    String dealStatus = "2";
		    try {
		        //创建hessian工厂
		        HessianProxyFactory factory = new HessianProxyFactory();
		        //通过工厂创建接口实例
		        com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.hs.IClaimQueryPolicyInfoUCCHS iclaimquerypolicyinfoucchs = (com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.hs.IClaimQueryPolicyInfoUCCHS) factory.create(hsUrl);
		        //通过接口实例调用接口方法
		        tempresult = iclaimquerypolicyinfoucchs.queryPolicyInfo(sysheader, srvReqBody);
		        //返回值赋值
		        resultbody = 
		                (com.nci.tunan.pa.interfaces.claimquerypolicyinfo.exports.iclaimquerypolicyinfoucc.querypolicyinfo.SrvResBody)tempresult.getResponse();
		        result = resultbody.getBizBody().getOutputData();
		        //添加调用后的日志输出
		        logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
		                    + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		        logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
		                    + DataSerialJSon.fromObject(tempresult.getResponse()));
		        logger.debug("hessian调用接口成功");
		     } catch (Exception e) {
		        e.printStackTrace();
		        logger.debug("hessian调用业务接口异常");
		        dealStatus = "3";
		     } finally {
		        if (dealSwitch && resultbody != null) {
		            logger.debug("开始记录交易响应日志");
		            CommonDealManagement.afterCommonDealManagement(sysheader,
		                    resultbody.getBizHeader(), resultbody.getBizBody(), DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
		        }
		        
		     }
		    long end = System.currentTimeMillis();
		    return result;
		}
		
		@Test
		public void test111qqq() throws ParseException{
			ClaimQueryPolicyInfoReqVO claimQueryPolicyInfoReqVO = new ClaimQueryPolicyInfoReqVO();
			claimQueryPolicyInfoReqVO.setPolicyId(new BigDecimal(638856) );
			claimQueryPolicyInfoReqVO.setBusiItemId(new BigDecimal(307851));
			claimQueryPolicyInfoReqVO.setClaimDate(DateUtilsEx.formatToDate("2021-12-30", "yyyy-MM-dd"));
			List<String> typeFlagList = new ArrayList<>();
			typeFlagList.add("investTotalPrem");
			typeFlagList.add("pgAmountFlag");
			claimQueryPolicyInfoReqVO.setTypeFlagList(typeFlagList);
			paiclaimquerypolicyinfouccqueryPolicyInfo(claimQueryPolicyInfoReqVO);
		}
	  public com.nci.tunan.pa.interfaces.serviceData.querypolicystatus.QueryPolicyStatusResVO queryPolicyStatu(
			  com.nci.tunan.pa.interfaces.serviceData.querypolicystatus.QueryPolicyStatusReqVO inputData) {
		      // hessian地址
		      String hsUrl = "http://localhost:8080/ls/remoting/PA_queryPolicyStatusUCCqueryPolicyStatuAddr";
		      //String hsUrl = "http://**********:9082/ls/remoting/PA_queryPolicyStatusUCCqueryPolicyStatuAddr";
		      
		      // 报文参数
		      // 公共处理报文的函数，将报文头赋值
		      SysHeader sysheader = new SysHeader();
		      BizHeader reqhead = new BizHeader();
		      // 定义系统报文体
		      com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.SrvReqBody();
		      // 定义业务报文体
		      com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.SrvReqBizBody();
		      srvReqBody.setBizBody(bizBody);
		      srvReqBody.setBizHeader(reqhead);
		      srvReqBody.getBizBody().setInputData(inputData);
		      // 添加调用前的日志输出
		      try {
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
		      } catch (Exception e3) {
		          e3.printStackTrace();
		          logger.debug("解析报文的JSON字符串发生异常");
		      }
		      // 返回参数
		      com.nci.tunan.pa.interfaces.serviceData.querypolicystatus.QueryPolicyStatusResVO result = null;
		      try {
		          // 创建hessian工厂
		          HessianProxyFactory factory = new HessianProxyFactory();
		          // 通过工厂创建接口实例
		          com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.hs.IQueryPolicyStatusUCCHS queryPolicyStatusUCCHS 
		          = (com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.hs.IQueryPolicyStatusUCCHS) factory
		                  .create(hsUrl);
		          // 通过接口实例调用接口方法
		          ServiceResult tempresult = queryPolicyStatusUCCHS.queryPolicyStatu(sysheader, srvReqBody);
		          // 返回值赋值
		          com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.querypolicystatus.exports.iquerypolicystatusucc.querypolicystatu.SrvResBody) tempresult
		                  .getResponse();
		          result = resultbody.getBizBody().getOutputData();
		          // 添加调用后的日志输出
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
		          logger.debug("hessian调用接口成功");
		      } catch (Exception e) {
		          e.printStackTrace();
		          logger.debug("hessian调用业务接口异常");
		      }
		      return result;
		  } 
	  
	  @Test
	  public void testqueryPolicyStatu(){
		  com.nci.tunan.pa.interfaces.serviceData.querypolicystatus.QueryPolicyStatusReqVO inputData  
		  = new com.nci.tunan.pa.interfaces.serviceData.querypolicystatus.QueryPolicyStatusReqVO();
	  
		  com.nci.tunan.pa.interfaces.serviceData.querypolicystatus.QueryPolicyStatusResVO outDate 
		  =new com.nci.tunan.pa.interfaces.serviceData.querypolicystatus.QueryPolicyStatusResVO();
		  List<String> list = new ArrayList<String>();
		  //list.add("00388000");
		  list.add("00846000");
		  inputData.setApplyDate(new Date());
		  inputData.setPolicyCode("990027236516");
		  inputData.setBusiProdCodes(list);
	  outDate=this.queryPolicyStatu(inputData);
	  logger.info(XmlHelper.classToXml(outDate));
	  }
	  	  
	  public com.nci.tunan.pa.interfaces.serviceData.endcase.LoanSelfpayResData queryLoanSelfpay(
			  com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData) {
		      // hessian地址
		      String hsUrl = "http://localhost:8080/ls/remoting/loanSelfpayUCCqueryLoanSelfpayAddr";
		      //String hsUrl = "http://**********:9082/ls/remoting/loanSelfpayUCCqueryLoanSelfpayAddr";
		      
		      // 报文参数
		      // 公共处理报文的函数，将报文头赋值
		      SysHeader sysheader = new SysHeader();
		      BizHeader reqhead = new BizHeader();
		      // 定义系统报文体
		      com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.SrvReqBody();
		      // 定义业务报文体
		      com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.SrvReqBizBody();
		      srvReqBody.setBizBody(bizBody);
		      srvReqBody.setBizHeader(reqhead);
		      srvReqBody.getBizBody().setInputData(inputData);
		      // 添加调用前的日志输出
		      try {
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
		      } catch (Exception e3) {
		          e3.printStackTrace();
		          logger.debug("解析报文的JSON字符串发生异常");
		      }
		      // 返回参数
		      com.nci.tunan.pa.interfaces.serviceData.endcase.LoanSelfpayResData result = null;
		      try {
		          // 创建hessian工厂
		          HessianProxyFactory factory = new HessianProxyFactory();
		          // 通过工厂创建接口实例
		          com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.hs.ILoanSelfpayUCCHS queryLoanSelfpayUCCHS 
		          = (com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.hs.ILoanSelfpayUCCHS) factory
		                  .create(hsUrl);
		          // 通过接口实例调用接口方法
		          ServiceResult tempresult = queryLoanSelfpayUCCHS.queryLoanSelfpay(sysheader, srvReqBody);
		          // 返回值赋值
		          com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.loanselfpay.exports.iloanselfpayucc.queryloanselfpay.SrvResBody) tempresult
		                  .getResponse();
		          result = resultbody.getBizBody().getOutputData();
		          // 添加调用后的日志输出
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
		          logger.debug("hessian调用接口成功");
		      } catch (Exception e) {
		          e.printStackTrace();
		          logger.debug("hessian调用业务接口异常");
		      }
		      return result;
		  } 
	  
	  @Test
	  public void testqueryLoanSelfpay(){
		  com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData  
		  = new com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData();
	  
		  com.nci.tunan.pa.interfaces.serviceData.endcase.LoanSelfpayResData outDate 
		  =new com.nci.tunan.pa.interfaces.serviceData.endcase.LoanSelfpayResData();
		  inputData.setBusiItemId(new BigDecimal("11821"));
		  inputData.setPolicyCode("************");
		  inputData.setDateFlag(DateUtilsEx.formatToDate("2021-1-1 ", "yyyy-MM-dd"));
	      outDate=this.queryLoanSelfpay(inputData);
	      logger.info(XmlHelper.classToXml(outDate));
	  }
	  
	  public com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckResVO accountCheck(
			  com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckReqVO inputData) {
		      // hessian地址
		      String hsUrl = "http://localhost:8080/ls/remoting/accountCheckUCCaccountCheckUCCAddr";
		      //String hsUrl = "http://**********:9082/ls/remoting/loanSelfpayUCCqueryLoanSelfpayAddr";
		      
		      // 报文参数
		      // 公共处理报文的函数，将报文头赋值
		      SysHeader sysheader = new SysHeader();
		      BizHeader reqhead = new BizHeader();
		      // 定义系统报文体
		      com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.SrvReqBody();
		      // 定义业务报文体
		      com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.SrvReqBizBody();
		      srvReqBody.setBizBody(bizBody);
		      srvReqBody.setBizHeader(reqhead);
		      srvReqBody.getBizBody().setInputData(inputData);
		      // 添加调用前的日志输出
		      try {
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
		      } catch (Exception e3) {
		          e3.printStackTrace();
		          logger.debug("解析报文的JSON字符串发生异常");
		      }
		      // 返回参数
		      com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckResVO result = null;
		      try {
		          // 创建hessian工厂
		          HessianProxyFactory factory = new HessianProxyFactory();
		          // 通过工厂创建接口实例
		          com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.hs.IAccountCheckUCCHS accountCheckUCCHS 
		          = (com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.hs.IAccountCheckUCCHS) factory
		                  .create(hsUrl);
		          // 通过接口实例调用接口方法
		          ServiceResult tempresult = accountCheckUCCHS.accountCheckUCC(sysheader, srvReqBody);
		          // 返回值赋值
		          com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.accountcheck.exports.iaccountcheckucc.accountcheckucc.SrvResBody) tempresult
		                  .getResponse();
		          result = resultbody.getBizBody().getOutputData();
		          // 添加调用后的日志输出
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
		          logger.debug("hessian调用接口成功");
		      } catch (Exception e) {
		          e.printStackTrace();
		          logger.debug("hessian调用业务接口异常");
		      }
		      return result;
		  } 
	  
	  @Test
	  public void testaccountCheck(){
		  com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckReqVO inputData  
		  = new com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckReqVO();
	  
		  com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckResVO outDate 
		  =new com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckResVO();
		  
		  List<AccountCheckReqVOParam> list =new ArrayList<AccountCheckReqVOParam>();
		  AccountCheckReqVOParam accountCheckReqVOParam = new AccountCheckReqVOParam();
		  accountCheckReqVOParam.setPolicyCode("************");
		  list.add(accountCheckReqVOParam);
		  inputData.setAccountCheckReqVOParam(list);
	      outDate=this.accountCheck(inputData);
	      logger.info(XmlHelper.classToXml(outDate));
	  }
	  
	  
	  
	  public com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitResVO paccountCheck(
			  com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitReqVO inputData) {
		      // hessian地址
		      String hsUrl = "http://localhost:8080/ls/remoting/AccumulatedhospitalizationUCCImplcheckAccumulatedhospitalizationAddr";
		      // 报文参数
		      // 公共处理报文的函数，将报文头赋值
		      SysHeader sysheader = new SysHeader();
		      BizHeader reqhead = new BizHeader();
		      // 定义系统报文体
		      com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.SrvReqBody();
		      // 定义业务报文体
		      com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.SrvReqBizBody();
		      srvReqBody.setBizBody(bizBody);
		      srvReqBody.setBizHeader(reqhead);
		      srvReqBody.getBizBody().setInputData(inputData);
		      // 添加调用前的日志输出
		      try {
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
		      } catch (Exception e3) {
		          e3.printStackTrace();
		          logger.debug("解析报文的JSON字符串发生异常");
		      }
		      // 返回参数
		      com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitResVO result = null;
		      try {
		          // 创建hessian工厂
		          HessianProxyFactory factory = new HessianProxyFactory();
		          // 通过工厂创建接口实例
		          com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.hs.IAccumulatedhospitalizationUCCHS accountCheckUCCHS 
		          = (com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.hs.IAccumulatedhospitalizationUCCHS) factory
		                  .create(hsUrl);
		          // 通过接口实例调用接口方法
		          ServiceResult tempresult = accountCheckUCCHS.checkAccumulatedhospitalization(sysheader, srvReqBody);
		          // 返回值赋值
		          com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.accumulatedhospitalization.exports.iaccumulatedhospitalizationucc.checkaccumulatedhospitalization.SrvResBody) tempresult
		                  .getResponse();
		          result = resultbody.getBizBody().getOutputData();
		          // 添加调用后的日志输出
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		          logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
		          logger.debug("hessian调用接口成功");
		      } catch (Exception e) {
		          e.printStackTrace();
		          logger.debug("hessian调用业务接口异常");
		      }
		      return result;
		  }
	  
	  
	  @Test
	  public void paccountCheck(){
		    com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitReqVO inputData  
		    = new com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitReqVO();
	  
		    com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitResVO outDate 
		    =new com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitResVO();
		  
		    List<BusiProdCodesLimit> busiList = new ArrayList<BusiProdCodesLimit>();
			BusiProdCodesLimit busiProdCodesLimit = new BusiProdCodesLimit();
			BusiProdCodesLimit busiProdCodeslimit = new BusiProdCodesLimit();
			BusiProdCodesLimit busiProdcodesLimit = new BusiProdCodesLimit();
			BusiProdCodesLimit busiprodcodesLimit = new BusiProdCodesLimit();
			busiProdCodesLimit.setBusiProdCodeLimit("********");
			busiList.add(busiProdCodesLimit);
			busiProdCodeslimit.setBusiProdCodeLimit("********");
			busiList.add(busiProdCodeslimit);
			busiProdcodesLimit.setBusiProdCodeLimit("********");
			busiList.add(busiProdcodesLimit);
			busiprodcodesLimit.setBusiProdCodeLimit("********");
			busiList.add(busiprodcodesLimit);
		    inputData.setBusiProdCodesLimit(busiList);
		    inputData.setCustomerId(new BigDecimal(48119));
		    inputData.setSystemCode("2");
	        outDate=this.paccountCheck(inputData);
	        logger.info(XmlHelper.classToXml(outDate));
	    }
	  
   /**
     * 深圳医保更新保单系统的承保确认码、医保结算流水号
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param inputData
     * @return
     */
	public com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.OutputData updatePolicy(
			  com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.InputData inputData) {
		      // hessian地址
//		      String hsUrl = Constants.SERVICEENVPARAMAP.get("PA") + "remoting/UpdatePolicyInfoUCCImplupdatePolicyAddr";
//		      String hsUrl = "http://localhost:8080/ls/remoting/UpdatePolicyInfoUCCImplupdatePolicyAddr";
		      String hsUrl = "http://**********:9083/ls/remoting/UpdatePolicyInfoUCCImplupdatePolicyAddr";
		      // 报文参数
		      // 公共处理报文的函数，将报文头赋值
		      SysHeader sysheader = new SysHeader();
		      BizHeader reqhead = new BizHeader();
		      // 定义系统报文体
		      com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.SrvReqBody();
		      // 定义业务报文体
		      com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.SrvReqBizBody();
		      srvReqBody.setBizBody(bizBody);
		      srvReqBody.setBizHeader(reqhead);
		      srvReqBody.getBizBody().setInputData(inputData);
		      // 添加调用前的日志输出
		      try {
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
		          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
		      } catch (Exception e3) {
		          e3.printStackTrace();
		          logger.debug("解析报文的JSON字符串发生异常");
		      }
		      // 返回参数
		      com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.OutputData result = null;
		      try {
		          // 创建hessian工厂
		          HessianProxyFactory factory = new HessianProxyFactory();
		          // 通过工厂创建接口实例
		          com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.hs.IUpdatePolicyInfoUCCHS accountCheckUCCHS 
		          = (com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.hs.IUpdatePolicyInfoUCCHS) factory
		                  .create(hsUrl);
		          // 通过接口实例调用接口方法
		          ServiceResult tempresult = accountCheckUCCHS.updatePolicy(sysheader, srvReqBody);
		          // 返回值赋值
		          com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.updatePolicyForPA.exports.iupdatepolicyinfoucc.updatepolicy.SrvResBody) tempresult
		                  .getResponse();
		          result = resultbody.getBizBody().getOutputData();
		          // 添加调用后的日志输出
		          logger.debug("深圳医保更新保单系统hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		          logger.debug("深圳医保更新保单系统hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
		          logger.debug("hessian调用接口成功");
		      } catch (Exception e) {
		          e.printStackTrace();
		          logger.debug("hessian调用业务接口异常");
				  throw new BizException("深圳医保更新保单系统接口异常", e);
		      }
		      return result;
		  }
	  @Test
	  public void updatePolicy(){
		  com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.InputData inputData = new com.nci.tunan.pa.interfaces.serviceData.updatePolicyForPA.InputData();
		  inputData.setBusiType("2");
		  PolicyList policyList = new PolicyList();
		  List<PolicyVO> policyVOList = new ArrayList<PolicyVO>();
		  inputData.setPolicyList(policyList);
		  policyList.setPolicyVO(policyVOList);
		  PolicyVO policyVO = new PolicyVO();
		  policyVO.setPolicyCode("990028754567");
		  policyVO.setBusiProdCode("00958100");
		  policyVO.setNewApplyCode("01400004498172"); 
		  policyVO.setReinsured("3");
		  policyVO.setBusiScene("NB");
		  policyVOList.add(policyVO);
		  OutputData updatePolicy = updatePolicy(inputData);
		  logger.info(XmlHelper.classToXml(updatePolicy));
	  }
	
	  /**
	     * 险种销售额度同步接口
	     * @description
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param inputData
	     * @return
	     */
		public com.nci.tunan.pa.interfaces.serviceData.saveSalesAmount.SaveSalesAmountOutputData saveSalesAmount(
				com.nci.tunan.pa.interfaces.serviceData.saveSalesAmount.SaveSalesAmountInputData inputData) {
			      // hessian地址
			      String hsUrl = "http://**********:8080/ls/remoting/saveSalesAmountUccsaveSalesAmountAddr";
			      // 报文参数
			      // 公共处理报文的函数，将报文头赋值
			      SysHeader sysheader = new SysHeader();
			      BizHeader reqhead = new BizHeader();
			      // 定义系统报文体
			      com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.SrvReqBody();
			      // 定义业务报文体
			      com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.SrvReqBizBody();
			      srvReqBody.setBizBody(bizBody);
			      srvReqBody.setBizHeader(reqhead);
			      srvReqBody.getBizBody().setInputData(inputData);
			      // 添加调用前的日志输出
			      try {
			          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
			          logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
			      } catch (Exception e3) {
			          e3.printStackTrace();
			          logger.debug("解析报文的JSON字符串发生异常");
			      }
			      // 返回参数
			      com.nci.tunan.pa.interfaces.serviceData.saveSalesAmount.SaveSalesAmountOutputData result = null;
			      try {
			          // 创建hessian工厂
			          HessianProxyFactory factory = new HessianProxyFactory();
			          // 通过工厂创建接口实例
			          com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.hs.ISaveSalesAmountUCCHS accountCheckUCCHS 
			          = (com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.hs.ISaveSalesAmountUCCHS) factory
			                  .create(hsUrl);
			          // 通过接口实例调用接口方法
			          ServiceResult tempresult = accountCheckUCCHS.saveSalesAmount(sysheader, srvReqBody);
			          // 返回值赋值
			          com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.SrvResBody resultbody = (com.nci.tunan.pa.interfaces.saveSalesAmount.exports.isavesalesamountucc.savesalesamount.SrvResBody) tempresult
			                  .getResponse();
			          result = resultbody.getBizBody().getOutputData();
			          // 添加调用后的日志输出
			          logger.debug("险种销售额度同步接口hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(tempresult.getSysHeader()));
			          logger.debug("险种销售额度同步接口hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(tempresult.getResponse()));
			          logger.debug("hessian调用接口成功");
			      } catch (Exception e) {
			          e.printStackTrace();
			          logger.debug("hessian调用业务接口异常");
					  throw new BizException("险种销售额度同步接口异常", e);
			      }
			      return result;
			  }
		
		 @Test
		  public void testSaveSalesAmount() throws Exception{
			 com.nci.tunan.pa.interfaces.serviceData.saveSalesAmount.SaveSalesAmountInputData inputData = new com.nci.tunan.pa.interfaces.serviceData.saveSalesAmount.SaveSalesAmountInputData();
			 
			 List<SalesAmountVO> busiProdList = new ArrayList<SalesAmountVO>();
			 
			 SalesAmountVO salesAmountVO = new SalesAmountVO();
			 salesAmountVO.setPolicyCode("************");
			 salesAmountVO.setBankCode("01");
			 salesAmountVO.setBusiProdCode("********");
			 salesAmountVO.setOrganCode("8647");
			 salesAmountVO.setApplyCode("**************");
			 salesAmountVO.setFeeAmount(new BigDecimal(10000));
			 salesAmountVO.setScenseCode("AA");
			 salesAmountVO.setAccumDate(DateUtilsEx.toDate("2021-08-01", "yyyy-MM-dd"));
			 
			 busiProdList.add(salesAmountVO);
			 inputData.setBusiProdList(busiProdList);
			 
			  com.nci.tunan.pa.interfaces.serviceData.saveSalesAmount.SaveSalesAmountOutputData outputData = saveSalesAmount(inputData);
			  logger.info(XmlHelper.classToXml(outputData));
		  }
		 
		 public com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData paidebtpremuccqueryDebtPrem(
		            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData) {
		        long start = System.currentTimeMillis();
		        Map<String, Object> applicationMap = ParaDefInitConst
		                .getApplicationConst();
		        boolean dealSwitch = false;
		        if (applicationMap.get(Constants.DEALSWITCH) != null) {
		            dealSwitch = "1".equals(((ParaDefBO) applicationMap
		                    .get(Constants.DEALSWITCH)).getParaValue());
		        }
		        // hessian地址
//		        String hsUrl = Constants.SERVICEENVPARAMAP.get("PA") + "remoting/debtPremUCCqueryDebtPremAddr";
		        String hsUrl = "http://localhost:8080/ls/remoting/debtPremUCCqueryDebtPremAddr";
		        // 报文参数
		        // 公共处理报文的函数，将报文头赋值
		        SysHeader sysheader = new SysHeader();
		        BizHeader reqhead = new BizHeader();
		        // 定义系统报文体
		        com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.SrvReqBody();
		        // 定义业务报文体
		        com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.SrvReqBizBody();
		        String dealTime = DateUtilsEx.getTodayTime();
		        String dealNo = "pa" + "_" + "idebtpremuccws" + "_" + "queryDebtPrem";

		        srvReqBody.setBizBody(bizBody);
		        srvReqBody.setBizHeader(reqhead);
		        srvReqBody.getBizBody().setInputData(inputData);
		        // 添加调用前的日志输出
		        try {
		            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
		                    + DataSerialJSon.fromObject(sysheader));
		            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
		                    + DataSerialJSon.fromObject(srvReqBody));
		        } catch (Exception e3) {
		            e3.printStackTrace();
		            logger.debug("解析报文的JSON字符串发生异常");
		        }
		        if (dealSwitch) {
		            logger.debug("开始记录交易请求日志");
		            CommonDealManagement.beforeCommonDealManagement(sysheader,
		                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
		                    DealTrigger.HESSIAN, dealNo, dealTime);
		        }
		        // 返回参数
		        com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData result = null;
		        ServiceResult tempresult = new ServiceResult();
		        com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.SrvResBody();
		        String dealStatus = "2";
		        try {
		            // 创建hessian工厂
		            HessianProxyFactory factory = new HessianProxyFactory();
		            // 通过工厂创建接口实例
		            com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.hs.IDebtPremUCCHS idebtpremucchs = (com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.hs.IDebtPremUCCHS) factory
		                    .create(hsUrl);
		            // 通过接口实例调用接口方法
		            tempresult = idebtpremucchs.queryDebtPrem(sysheader, srvReqBody);
		            // 返回值赋值
		            resultbody = (com.nci.tunan.pa.interfaces.debtprem.exports.idebtpremucc.querydebtprem.SrvResBody) tempresult
		                    .getResponse();
		            result = resultbody.getBizBody().getOutputData();
		            // 添加调用后的日志输出
		            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
		                    + "-技术报文头："
		                    + DataSerialJSon.fromObject(tempresult.getSysHeader()));
		            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
		                    + "-技术报文体："
		                    + DataSerialJSon.fromObject(tempresult.getResponse()));
		            logger.debug("hessian调用接口成功");
		        } catch (Exception e) {
		            e.printStackTrace();
		            logger.debug("hessian调用业务接口异常");
		            dealStatus = "3";
		        } finally {
		            if (dealSwitch && resultbody != null) {
		                logger.debug("开始记录交易响应日志");
		                CommonDealManagement.afterCommonDealManagement(sysheader,
		                        resultbody.getBizHeader(), resultbody.getBizBody(),
		                        DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
		            }

		        }
		        long end = System.currentTimeMillis();
		        return result;
		    }
		 
		 @Test
		  public void testpaidebtpremuccqueryDebtPrem() throws Exception{
			 EndCaseReqData inputData = new EndCaseReqData();
			 inputData.setPolicyCode("990029868700");
			 inputData.setBusiItemId(new BigDecimal(307851));
			 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			 inputData.setDateFlag(sdf.parse("2021-12-30"));
			 DebtPremResData outputData = paidebtpremuccqueryDebtPrem(inputData);
			 logger.info(XmlHelper.classToXml(outputData));
		  }

	/**
	 * @description 投连试算/卖出
	 * @param inputData
	 *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.sellinvest.
	 *            SellInvestReqData
	 * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.sellinvest.
	 *         SellInvestResData
	 */
	public com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestResData paisellinvestuccsellInvest(
	        com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestReqData inputData) {
	    Map<String, Object> applicationMap = ParaDefInitConst
	            .getApplicationConst();
	    boolean dealSwitch = false;
	    if (applicationMap.get(Constants.DEALSWITCH) != null) {
	        dealSwitch = "1".equals(((ParaDefBO) applicationMap
	                .get(Constants.DEALSWITCH)).getParaValue());
	    }
	    // hessian地址
	    String hsUrl = "http://**********:9080/ls/"
	            + "remoting/sellInvestUCCsellInvestAddr";
	    // 报文参数
	    // 公共处理报文的函数，将报文头赋值
	    SysHeader sysheader = new SysHeader();
	    BizHeader reqhead = new BizHeader();
	    // 定义系统报文体
	    com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.SrvReqBody();
	    // 定义业务报文体
	    com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.SrvReqBizBody();
	    String dealTime = DateUtilsEx.getTodayTime();
	    String dealNo = "pa" + "_" + "isellinvestuccws" + "_" + "sellInvest";
	
	    srvReqBody.setBizBody(bizBody);
	    srvReqBody.setBizHeader(reqhead);
	    srvReqBody.getBizBody().setInputData(inputData);
	    // 添加调用前的日志输出
	    try {
	        logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
	                + DataSerialJSon.fromObject(sysheader));
	        logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
	                + DataSerialJSon.fromObject(srvReqBody));
	    } catch (Exception e3) {
	        e3.printStackTrace();
	        logger.debug("解析报文的JSON字符串发生异常");
	    }
	    if (dealSwitch) {
	        logger.debug("开始记录交易请求日志");
	        CommonDealManagement.beforeCommonDealManagement(sysheader,
	                srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
	                DealTrigger.HESSIAN, dealNo, dealTime);
	    }
	    // 返回参数
	    com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestResData result = null;
	    ServiceResult tempresult = new ServiceResult();
	    com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.SrvResBody();
	    String dealStatus = "2";
	    try {
	        // 创建hessian工厂
	        HessianProxyFactory factory = new HessianProxyFactory();
	        // 通过工厂创建接口实例
	        com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.hs.ISellInvestUCCHS isellinvestucchs = (com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.hs.ISellInvestUCCHS) factory
	                .create(hsUrl);
	        // 通过接口实例调用接口方法
	        tempresult = isellinvestucchs.sellInvest(sysheader, srvReqBody);
	        // 返回值赋值
	        resultbody = (com.nci.tunan.pa.interfaces.sellinvest.exports.isellinvestucc.sellinvest.SrvResBody) tempresult
	                .getResponse();
	        result = resultbody.getBizBody().getOutputData();
	        // 添加调用后的日志输出
	        logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                + "-技术报文头："
	                + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	        logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
	                + "-技术报文体："
	                + DataSerialJSon.fromObject(tempresult.getResponse()));
	        logger.debug("hessian调用接口成功");
	    } catch (Exception e) {
	        e.printStackTrace();
	        logger.debug("hessian调用业务接口异常");
	        dealStatus = "3";
	    } finally {
	        if (dealSwitch && resultbody != null) {
	            logger.debug("开始记录交易响应日志");
	            CommonDealManagement.afterCommonDealManagement(sysheader,
	                    resultbody.getBizHeader(), resultbody.getBizBody(),
	                    DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
	        }
	
	    }
	    return result;
	}
	
	 @Test
	  public void paisellinvestuccsellInvest() throws Exception{
		 SellInvestReqData inputData = new SellInvestReqData();
		 inputData.setApplyDate(DateUtilsEx.formatToDate("2021-11-11", "yyyy-MM-dd"));
		 inputData.setItemId(new BigDecimal(116177));
		 inputData.setOperateType("0");
		 SellInvestResData outputData = paisellinvestuccsellInvest(inputData);
		 logger.info(XmlHelper.classToXml(outputData));
	  }
			 

	/**
	 * 
	 * @description 测试类
	 * @version
	 * @title
	 * <AUTHOR>
	 * @throws Exception
	 */
	@Test
	public void queryBackPrem() throws Exception {

		com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputVO  = new EndCaseReqData();
		inputVO.setPolicyCode("990033842762");
		inputVO.setBusiItemId(new BigDecimal("496355"));
		String date = "2082-05-01";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date date1 = sdf.parse(date);
		inputVO.setDateFlag(date1);
		logger.info(XmlHelper.classToXml(inputVO));
		this.paibackpremuccqueryBackPrem(inputVO);
	}

	/**
	 * 
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData
	 * @return
	 */
	public com.nci.tunan.pa.interfaces.serviceData.endcase.BackPremResData paibackpremuccqueryBackPrem(
			com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData) {
		long start = System.currentTimeMillis();
		Map<String, Object> applicationMap = ParaDefInitConst
				.getApplicationConst();
		boolean dealSwitch = false;
		if (applicationMap.get(Constants.DEALSWITCH) != null) {
			dealSwitch = "1".equals(((ParaDefBO) applicationMap
					.get(Constants.DEALSWITCH)).getParaValue());
		}
		// hessian地址
		//String hsUrl = Constants.SERVICEENVPARAMAP.get("PA") + "remoting/backPremUCCqueryBackPremAddr";
//		String hsUrl = "http://**********:9080/ls/remoting/backPremUCCqueryBackPremAddr";     
		String hsUrl = "http://localhost:8080/ls/remoting/backPremUCCqueryBackPremAddr";
		
		// 报文参数
		// 公共处理报文的函数，将报文头赋值
		SysHeader sysheader = new SysHeader();
		BizHeader reqhead =  new BizHeader();
		// 定义系统报文体
		com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvReqBody();
		// 定义业务报文体
		com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvReqBizBody();
		String dealTime = DateUtilsEx.getTodayTime();
		String dealNo = "pa" + "_" + "ibackpremuccws" + "_" + "queryBackPrem";

		srvReqBody.setBizBody(bizBody);
		srvReqBody.setBizHeader(reqhead);
		srvReqBody.getBizBody().setInputData(inputData);

		// 返回参数
		com.nci.tunan.pa.interfaces.serviceData.endcase.BackPremResData result = null;
		ServiceResult tempresult = new ServiceResult();
		com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvResBody();
		String dealStatus = "2";
		try {
			// 创建hessian工厂
			HessianProxyFactory factory = new HessianProxyFactory();
			// 通过工厂创建接口实例
			com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.hs.IBackPremUCCHS ibackpremucchs = (com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.hs.IBackPremUCCHS) factory
					.create(hsUrl);
			// 通过接口实例调用接口方法
			tempresult = ibackpremucchs.queryBackPrem(sysheader, srvReqBody);
			logger.info("123123123123"+XmlHelper.classToXml(tempresult));
			// 返回值赋值
			resultbody = (com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvResBody) tempresult
					.getResponse();
			result = resultbody.getBizBody().getOutputData();
			// 添加调用后的日志输出
			logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
					+ "-技术报文头："
					+ DataSerialJSon.fromObject(tempresult.getSysHeader()));
			logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
					+ "-技术报文体："
					+ DataSerialJSon.fromObject(tempresult.getResponse()));
			logger.debug("hessian调用接口成功");
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("hessian调用业务接口异常");
			dealStatus = "3";
		} finally {
			if (dealSwitch && resultbody != null) {
				logger.debug("开始记录交易响应日志");
				CommonDealManagement.afterCommonDealManagement(sysheader,
						resultbody.getBizHeader(), resultbody.getBizBody(),
						DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
			}

		}
		long end = System.currentTimeMillis();
		//countTime(start, end);
		return result;
	}
	
	/**
	 * 
	 * @description 25版本24告知项测试
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData
	 * @return
	 */
	@Test
	public void queryUnderwriteUCCqueryUnderwriteAddr() {

		// 定义入参
		com.nci.tunan.pa.interfaces.serviceData.queryunderwrite.QueryUnderwriteReqData inputData = new com.nci.tunan.pa.interfaces.serviceData.queryunderwrite.QueryUnderwriteReqData();
		inputData.setPolicyCode("887236499868");
		String customerId = "5000003470999";
		inputData.setCustomerId(new BigDecimal(customerId));
		QueryUnderwriteResData outData = testQueryUnderwriteUCCqueryUnderwriteAddr(inputData);
		List<QueryUnderwriteData> queryUnderwriteDatas = outData
				.getQueryUnderwriteDatas();
	}

	/**
	 * 
	 * @description 25版本24告知项测试
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData
	 * @return
	 */
	public com.nci.tunan.pa.interfaces.serviceData.queryunderwrite.QueryUnderwriteResData testQueryUnderwriteUCCqueryUnderwriteAddr(
			com.nci.tunan.pa.interfaces.serviceData.queryunderwrite.QueryUnderwriteReqData inputData) {

		long start = System.currentTimeMillis();
		Map<String, Object> applicationMap = ParaDefInitConst
				.getApplicationConst();
		boolean dealSwitch = false;
		if (applicationMap.get(Constants.DEALSWITCH) != null) {
			dealSwitch = "1".equals(((ParaDefBO) applicationMap
					.get(Constants.DEALSWITCH)).getParaValue());
		}
		
		 // hessian地址
        String hsUrl = "http://localhost:8080/ls/"+ "remoting/queryUnderwriteUCCqueryUnderwriteAddr";
        // 报文参数
        // 公共处理报文的函数，将报文头赋值
        SysHeader sysheader = new SysHeader();
        BizHeader reqhead = new BizHeader();
		
        //定义系统报文体
        com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.SrvReqBody srvReqBody = new com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.SrvReqBody();
       // 定义业务报文体
        com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.SrvReqBizBody bizBody = new com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "PA" + "_" + "IQueryUnderwriteUCCHS" + "_" + "queryUnderwrite";
        
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        // 添加调用前的日志输出
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
                    + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        if (dealSwitch) {
            logger.debug("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(sysheader,
                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
                    DealTrigger.HESSIAN, dealNo, dealTime);
        }
        
       // 返回参数
        com.nci.tunan.pa.interfaces.serviceData.queryunderwrite.QueryUnderwriteResData resultData= null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.SrvResBody resultbody = new  com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.SrvResBody();
        
        String dealStatus = "2";
        try {
            // 创建hessian工厂
            HessianProxyFactory factory = new HessianProxyFactory();
            // 通过工厂创建接口实例
            com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.hs.IQueryUnderwriteUCCHS  iQueryUnderwriteUCCHS = (com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.hs.IQueryUnderwriteUCCHS) factory.create(hsUrl);
            // 通过接口实例调用接口方法
            tempresult= iQueryUnderwriteUCCHS.queryUnderwrite(sysheader, srvReqBody);
            // 返回值赋值
            resultbody=(com.nci.tunan.pa.interfaces.queryunderwrite.exports.iqueryunderwriteucc.queryunderwrite.SrvResBody)tempresult.getResponse();
            
            resultData=resultbody.getBizBody().getOutputData();
            
            
            // 添加调用后的日志输出
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文头："
                    + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文体："
                    + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
           
        } catch (Exception e) {
        	 dealStatus = "3";
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
           
        } finally {
        	if (dealSwitch && resultbody != null) {
                logger.debug("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader,
                        resultbody.getBizHeader(), resultbody.getBizBody(),
                        DealTrigger.HESSIAN, dealNo, dealTime,dealStatus);
            }

        }
		return resultData;
	}

	//团险风险保额累计UCC接口
    public com.nci.tunan.pa.interfaces.serviceData.groupRisk.TransData paigroupriskaccuccfindGroupRiskAcc(com.nci.tunan.pa.interfaces.serviceData.groupRisk.InputData inputData) {
        
        //hessian地址
        String hsUrl = "http://localhost:8080/ls/remoting/GroupRiskAccUCCImplfindGroupRiskAccAddr";
        
        //报文参数
        //公共处理报文的函数，将报文头赋值
        SysHeader sysheader = new SysHeader();
		com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB reqhead = new com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB();
        
        // 定义系统报文体
        com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.SrvReqBody srvReqBody = new 
              com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.SrvReqBody();
        // 定义业务报文体
        com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.SrvReqBizBody bizBody = new 
              com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.SrvReqBizBody();

        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        //添加调用前的日志输出
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        
        //返回参数
        com.nci.tunan.pa.interfaces.serviceData.groupRisk.TransData result = null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.SrvResBody resultbody = new com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.SrvResBody();
        try {
            //创建hessian工厂
            HessianProxyFactory factory = new HessianProxyFactory();
            //通过工厂创建接口实例
            com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.hs.IGroupRiskAccUCCHS igroupriskaccucchs = (com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.hs.IGroupRiskAccUCCHS) factory.create(hsUrl);
            //通过接口实例调用接口方法
            tempresult = igroupriskaccucchs.findGroupRiskAcc(sysheader, srvReqBody);
            //返回值赋值
            resultbody = 
                    (com.nci.tunan.pa.interfaces.groupRisk.exports.igroupriskaccucc.findgroupriskacc.SrvResBody)tempresult.getResponse();
            result = resultbody.getBizBody().getOutputData();
            //添加调用后的日志输出
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return result;
    }
		 
	/**
	 * 
	 * @description 25版本24告知项测试
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData
	 * @return
	 */
	@Test
	public void paigroupriskaccuccfindGroupRiskAcc() {

		// 定义入参
		com.nci.tunan.pa.interfaces.serviceData.groupRisk.InputData inputData = new com.nci.tunan.pa.interfaces.serviceData.groupRisk.InputData();
		inputData.setBirthDay("2017-04-25");
		inputData.setIdNo("350402201704256027");
		inputData.setIdType("0");
		inputData.setName("罗童兮");
		inputData.setSex("2");
		TransData paigroupriskaccuccfindGroupRiskAcc = paigroupriskaccuccfindGroupRiskAcc(inputData);
		System.out.println(XmlHelper.classToXml(paigroupriskaccuccfindGroupRiskAcc));
		
	}		
	
	/**
	 * 查询最近核保信息
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param inputData
	 * @return
	 */
	public com.nci.tunan.uw.interfaces.vo.uwtask.finduwconditionbycode.OutputDataWSVO QueryUwConditionHs(com.nci.tunan.uw.interfaces.vo.uwtask.finduwconditionbycode.InputDataWSVO inputData) {
	       //hessian地址
	      // String hsUrl = Constants.SERVICEENVPARAMAP.get("UW") + "remoting/UW_QueryUwConditionUccfindUwConditionByCodeAddr";
	        String hsUrl = "http://**********:9080/ls/remoting/UW_QueryUwConditionUccfindUwConditionByCodeAddr";
	       
	       //@invalid	   String hsUrl ="http://**********:8088/ls/remoting/UW_QueryUwConditionUccfindUwConditionByCodeAddr";
	       logger.info("----------findUwConditionByCode----------hsUrl:"+hsUrl);
	       //报文参数
	       //公共处理报文的函数，将报文头赋值
	       SysHeader sysheader = new SysHeader();
	       BizHeader reqhead = new BizHeader();      
	       // 定义系统报文体
	        
	       com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.SrvReqBody srvReqBody = new 
	    		   com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.SrvReqBody();
	       // 定义业务报文体
	       com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.SrvReqBizBody bizBody = new 
	    		   com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.SrvReqBizBody();

	       srvReqBody.setBizBody(bizBody);
	       srvReqBody.setBizHeader(reqhead);
	       srvReqBody.getBizBody().setInputData(inputData);
	       //添加调用前的日志输出
	       try {
	           logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
	           logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
	       } catch (Exception e3) {
	           e3.printStackTrace();
	           logger.debug("解析报文的JSON字符串发生异常");
	       }
	       
	       //返回参数
	       com.nci.tunan.uw.interfaces.vo.uwtask.finduwconditionbycode.OutputDataWSVO result = null;
	       ServiceResult tempresult = new ServiceResult();
	       com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.SrvResBody resultbody = new 
	    		   com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.SrvResBody();
	       try {
	           //创建hessian工厂
	           HessianProxyFactory factory = new HessianProxyFactory();
	           //通过工厂创建接口实例 
	           com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.IQueryUwConditionUccHS findUwConditionByCode = ( com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.IQueryUwConditionUccHS) factory.create(hsUrl);
	           //通过接口实例调用接口方法
	           tempresult = findUwConditionByCode.findUwConditionByCode(sysheader, srvReqBody);
	           //返回值赋值
	           resultbody = 
	                   (com.nci.tunan.uw.interfaces.exports.iuwpolicyucc.finduwconditionbycode.hs.SrvResBody)tempresult.getResponse();
	           result = resultbody.getBizBody().getOutputData();
	           result = resultbody.getBizBody().getOutputData();
	           //添加调用后的日志输出
	           logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
	                       + DataSerialJSon.fromObject(tempresult.getSysHeader()));
	           logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
	                       + DataSerialJSon.fromObject(tempresult.getResponse()));
	           logger.debug("hessian调用接口成功");
	       } catch (Exception e) {
	           e.printStackTrace();
	           logger.debug("hessian调用业务接口异常");
	       }
	       return result;
	   }
	
	@Test
	public void QueryUwConditionHstext() {
		 com.nci.tunan.uw.interfaces.vo.uwtask.finduwconditionbycode.InputDataWSVO inputData = new com.nci.tunan.uw.interfaces.vo.uwtask.finduwconditionbycode.InputDataWSVO();
         inputData.setApplyCode("03245002184517");
         inputData.setBusiProdCode("00991000");
         inputData.setSystemInfo("PA");
         com.nci.tunan.uw.interfaces.vo.uwtask.finduwconditionbycode.OutputDataWSVO outputData = QueryUwConditionHs(inputData);
		System.out.println(XmlHelper.classToXml(outputData));
	}
	
}
