package serviceinvoke.util;

/** 
 * @description 服务总接口
 * <AUTHOR>
 * @date 2016-01-14 14:37:30 
 * 
*/
public interface IPAServiceUtils {
	/**
	 * @description 计算险种现金价值
	 * @param inputData 输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueReqVO
	 * @return 输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueResVO
	*/
	public com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueResVO prdimmscalccashvalueucccalcCashValue(com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueReqVO inputData);

	  /**
     * @description 网络直销平台-终止保单批量查询
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynResVO  
    */
  public com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynResVO paiseckillexpirypolicysyncuccgetExpiryPolicyDataInfo(com.nci.tunan.pa.interfaces.serviceData.seckillexpirypolicysyn.SecKillExpiryPolicySynReqVO inputData);
   
 
   /**
     * @description 秒杀承包数据同步
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncResVO  
    */
  public com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncResVO paiseckillpolicydatesyncuccseckillPolicyDateInfo(com.nci.tunan.pa.interfaces.serviceData.seckillpolicydatesync.SecKillPolicyDateSyncReqVO inputData);
  /**
   * @description 秒杀回执日期同步
   * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncReqVO
   * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncResVO  
  */
public com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncResVO paiseckillacknowledgesyncuccdataSyncInfo(com.nci.tunan.pa.interfaces.serviceData.seckillacknowledgesync.SecKillAcknowledgeSyncReqVO inputData);


/**
 * @description 新契约调用创建保单。
 * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyReqData
 * @return 输出的参数VO：com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyResData
*/
public com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyResData paicreatepolicyuccinsertPolicy(com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo.CreatePolicyReqData inputData);


/**
 * 住院津贴校验接口
 * @description
 * @version
 * @title
 * <AUTHOR>
 * @param inputData
 * @return
 */
public com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitResVO paccountCheck(com.nci.tunan.pa.interfaces.serviceData.accumulatedhospitalization.QuerySubsidyLimitReqVO inputData);


 
}
