package serviceinvoke.util;

import java.util.Map;

import javax.xml.ws.Holder;

import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.slf4j.Logger;

import com.nci.tunan.cap.interfaces.vo.arapbusinesstransfer.ArapBusinessTransferInputData;
import com.nci.tunan.cap.interfaces.vo.arapbusinesstransfer.ArapBusinessTransferOutputData;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.component.serviceinvoke.ServiceCommonMethod;
import com.nci.udmp.component.serviceinvoke.ServiceInvokeParameterBean;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

public class CapService implements ICapService {

    
    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();

   /**
     * @description 业务转实收
     * @param inputData 输入参数VO:com.nci.tunan.cap.interfaces.vo.ArapBusinessTransferInputData
     * @return 输出的参数VO：com.nci.tunan.cap.interfaces.vo.ArapBusinessTransferOutputData  
    */
  public ArapBusinessTransferOutputData capiarapbusinesstransferuccbusinessTransfer(ArapBusinessTransferInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        //调用的webService的wsdl
	    String wsdl = "http://***********:9080/cap/" + "webservice/arapBusinessTransferUCCbusinessTransferAddr?wsdl";
        //服务接口路径
        String interfacePath = "com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.ws.IArapBusinessTransferUCCWS";
        //报文参数
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.SrvResBody> parametersResBody = new 
        		Holder<com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.SrvResBody>();
        //公共处理报文的函数，将报文头赋值
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1586"));
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        parametersResHeader.value = sysheader;
        // 定义系统报文体
        com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.SrvReqBody srvReqBody = new 
        		 com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.SrvReqBody();
        // 定义业务报文体
        com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.SrvReqBizBody bizBody = new 
        		com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
	    String dealNo = "cap" + "_" + "iarapbusinesstransferuccws" + "_" + "businessTransfer";  
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        //添加调用前的日志输出
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
            throw new RuntimeException(e3);
        }
        if (dealSwitch) {
	       logger.debug("开始记录交易请求日志");
           CommonDealManagement.beforeCommonDealManagement(sysheader, srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
                    DealTrigger.WEBSERVICE, dealNo, dealTime); 
	    }
        //返回参数
        ArapBusinessTransferOutputData result = null;
        String dealStatus = "2";
        try {
            //调用公共webservice工厂方法
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod.CommonWebServiceFactory(wsdl, interfacePath);
            //通过工厂创建接口实例
            com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.ws.IArapBusinessTransferUCCWS iarapbusinesstransferuccws = (com.nci.tunan.cap.interfaces.counterchagre.exports.iarapbusinesstransferucc.businesstransfer.ws.IArapBusinessTransferUCCWS) soapFactory.create();
            //调用接口的指定方法，将参数传入
            iarapbusinesstransferuccws.businessTransfer(sysheader, srvReqBody, parametersResHeader, parametersResBody);
            //将业务报文体的outputData赋值给返回值
            result = parametersResBody.value.getBizBody().getOutputData(); 
            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
			CommonHeaderDeal.setBIZHEADERTHREAD(parametersResBody.value.getBizHeader());
			//添加调用后的日志输出
			logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
		                + DataSerialJSon.fromObject(sysheader));
		    logger.debug(" WebService返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
		                + DataSerialJSon.fromObject(parametersResBody));
            logger.debug("webservice调用接口成功");
        } catch (BizException e) {
            e.printStackTrace();
            logger.debug("webservice调用业务接口异常");
            dealStatus = "3";
            throw new RuntimeException(e);
        } catch (ClassNotFoundException e1) {
            e1.printStackTrace();
            logger.debug("找不到接口类");
            dealStatus = "3";
            throw new RuntimeException(e1);
        } catch (Exception e2) {
            e2.printStackTrace();
            logger.debug("解析返回的报文的JSON字符串发生异常");
            dealStatus = "3";
            throw new RuntimeException(e2);
        } finally {  
            if (dealSwitch && parametersResBody.value == null) {
                logger.debug("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader,
                        null, null, DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            } else if (dealSwitch && parametersResBody.value != null) {
                logger.debug("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader,
                        parametersResBody.value.getBizHeader(), parametersResBody.value.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            }
            
        }
        return result;
   }
   
 

}
