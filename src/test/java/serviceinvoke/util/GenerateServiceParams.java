package serviceinvoke.util;

public class GenerateServiceParams {
	
	/**
	 * Y:代表生成webservice文件
	 */
	private String allowWebservice;
	/**
	 * Y:代表生成hessian文件
	 */
	private String allowHessian;
	/**
	 * 接口名称
	 */
	private String serviceName;
	/**
	 * ucc包路径（）
	 */
	private String uccPackagePath;
	/**
	 * ucc路径（不带类名）
	 */
	private String uccPath;
	/**
	 * uccImpl包路径（不带类名）
	 */
	private String uccImplPackagePath;
	/**
	 * ucc方法名
	 */
	private String uccMethod;
	/**
	 * 入参对象（全路径）
	 */
	private String inputData;
	/**
	 * 出参对象（全路径）
	 */
	private String outputData;
	/**
	 * 系统名
	 */
	private String systemName;
	/**
	 * ucc实现类名称
	 */
	private String uccImplName;
	/**
	 * 本地项目工作空间
	 */
	private String workSpace;
	/**
	 * 入参路径（不包含类名）
	 */
	private String inputPath;
	/**
	 * 出参路径（不包含类名）
	 */
	private String outputPath;
	/**
	 * ucc接口名称
	 */
	private String uccName;
	/**
	 * 入参对象名
	 */
	private String inputName;
	/**
	 * 出参对象名
	 */
	private String outputName;
	/**
	 * 入参模板地址
	 */
	private String inputTempletPath;
	/**
	 * 出参模板地址
	 */
	private String outputTempletPath;
	/**
	 * ucc接口模板地址
	 */
	private String uccTempletPath;
	/**
	 * ucc接口实现类模板地址
	 */
	private String uccImplTempletPath;

	public String getInputTempletPath() {
		return inputTempletPath;
	}
	public void setInputTempletPath(String inputTempletPath) {
		this.inputTempletPath = inputTempletPath;
	}
	public String getOutputTempletPath() {
		return outputTempletPath;
	}
	public void setOutputTempletPath(String outputTempletPath) {
		this.outputTempletPath = outputTempletPath;
	}
	public String getUccTempletPath() {
		return uccTempletPath;
	}
	public void setUccTempletPath(String uccTempletPath) {
		this.uccTempletPath = uccTempletPath;
	}
	public String getUccImplTempletPath() {
		return uccImplTempletPath;
	}
	public void setUccImplTempletPath(String uccImplTempletPath) {
		this.uccImplTempletPath = uccImplTempletPath;
	}
	public String getUccPath() {
		return uccPath;
	}
	public void setUccPath(String uccPath) {
		this.uccPath = uccPath;
	}
	public String getInputName() {
		return inputName;
	}
	public void setInputName(String inputName) {
		this.inputName = inputName;
	}
	public String getOutputName() {
		return outputName;
	}
	public void setOutputName(String outputName) {
		this.outputName = outputName;
	}
	public String getInputPath() {
		return inputPath;
	}
	public void setInputPath(String inputPath) {
		this.inputPath = inputPath;
	}
	public String getOutputPath() {
		return outputPath;
	}
	public void setOutputPath(String outputPath) {
		this.outputPath = outputPath;
	}
	public String getUccName() {
		return uccName;
	}
	public void setUccName(String uccName) {
		this.uccName = uccName;
	}
	public String getUccImplPackagePath() {
		return uccImplPackagePath;
	}
	public void setUccImplPackagePath(String uccImplPackagePath) {
		this.uccImplPackagePath = uccImplPackagePath;
	}
	public String getAllowWebservice() {
		return allowWebservice;
	}
	public void setAllowWebservice(String allowWebservice) {
		this.allowWebservice = allowWebservice;
	}
	public String getAllowHessian() {
		return allowHessian;
	}
	public void setAllowHessian(String allowHessian) {
		this.allowHessian = allowHessian;
	}
	public String getServiceName() {
		return serviceName;
	}
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}
	public String getUccPackagePath() {
		return uccPackagePath;
	}
	public void setUccPackagePath(String uccPackagePath) {
		this.uccPackagePath = uccPackagePath;
	}
	public String getUccMethod() {
		return uccMethod;
	}
	public void setUccMethod(String uccMethod) {
		this.uccMethod = uccMethod;
	}
	public String getInputData() {
		return inputData;
	}
	public void setInputData(String inputData) {
		this.inputData = inputData;
	}
	public String getOutputData() {
		return outputData;
	}
	public void setOutputData(String outputData) {
		this.outputData = outputData;
	}
	public String getSystemName() {
		return systemName;
	}
	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}
	public String getUccImplName() {
		return uccImplName;
	}
	public void setUccImplName(String uccImplName) {
		this.uccImplName = uccImplName;
	}
	public String getWorkSpace() {
		return workSpace;
	}
	public void setWorkSpace(String workSpace) {
		this.workSpace = workSpace;
	}
	
}
