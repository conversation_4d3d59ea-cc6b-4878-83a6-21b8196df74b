package serviceinvoke.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.springframework.util.ObjectUtils;

import com.nci.udmp.tools.generate.mapper.PublicUtil;
import com.nci.udmp.tools.generate.mapper.VelocityUtil;
import com.nci.util.LoggerFactory;

public class NewGenrateServiceUtil {

	private final static String FLAG = "#";

	/**
	 * @Fields logger : 引入日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	private Map<String, String> indexAndValue;

	public void genrateService(GenerateServiceParams generateServiceParams) throws Exception {
		// 1.初始化map
		initIndexAndValueMap(generateServiceParams);
		// 2.input,output,impl等业务类
		createBussniessClass(generateServiceParams);
		// 3.生成webservice相关类
		oldGenrateService(generateServiceParams);

	}

	public void createBussniessClass(GenerateServiceParams generateServiceParams) throws IOException {

		String interProjectName = generateServiceParams.getSystemName().toLowerCase() + "-" + "interface";
		String implProjectName = generateServiceParams.getSystemName().toLowerCase() + "-" + "impl";

		String inputDataPath = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				interProjectName, generateServiceParams.getInputPath());
		// 1.生成InputDate
		writeTargetFile(generateServiceParams.getInputTempletPath(), inputDataPath, generateServiceParams.getInputName());
	    logger.debug("===================InputDate生成成功===================");
		String outPutDataPath = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				interProjectName, generateServiceParams.getOutputPath());
		// 2.生成outputData
		writeTargetFile(generateServiceParams.getOutputTempletPath(), outPutDataPath,
				generateServiceParams.getOutputName());
		logger.debug("===================OutputDate生成成功===================");
		String ucc = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(), implProjectName,
				generateServiceParams.getUccPath());
		// 3.生成UCC
		writeTargetFile(generateServiceParams.getUccTempletPath(), ucc, generateServiceParams.getUccName());
		logger.debug("===================UCC生成成功===================");
		// 4.生成UCCImpl
		String uccImpl = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				implProjectName, generateServiceParams.getUccImplPackagePath());
		writeTargetFile(generateServiceParams.getUccImplTempletPath(), uccImpl, generateServiceParams.getUccImplName());
		logger.debug("===================UCCImpl生成成功===================");
		logger.debug("===================业务相关类全部生成成功===================");
	}

	private void initIndexAndValueMap(GenerateServiceParams params)
			throws IllegalArgumentException, IllegalAccessException {
		indexAndValue = new HashMap<>();
		Field[] fields = GenerateServiceParams.class.getDeclaredFields();
		List<String> needField = Arrays.asList("inputData", "outputData", "uccMethod", "uccPackagePath", "uccImplName");
		for (Field field : fields) {
			field.setAccessible(true);
			if (needField.contains(field.getName())) {
				indexAndValue.put(field.getName(), (String) field.get(params));
			}
		}
		String inputData = params.getInputData();
		String outputDate = params.getOutputData();
		String uccPakeage = params.getUccPackagePath();
		String uccName = uccPakeage.substring(uccPakeage.lastIndexOf(".") + 1);
		indexAndValue.put("InputDataPath", inputData.substring(0, inputData.lastIndexOf(".")));
		indexAndValue.put("OutputDataPath", inputData.substring(0, inputData.lastIndexOf(".")));
		indexAndValue.put("UCCPath", uccPakeage.substring(0, uccPakeage.lastIndexOf(".")));
		indexAndValue.put("UCCImplPath", uccPakeage.replace(uccName, "impl"));
		indexAndValue.put("UCCName", uccName);

		params.setUccImplPackagePath(indexAndValue.get("UCCImplPath"));
		params.setUccPath(indexAndValue.get("UCCPath"));
		params.setInputPath(indexAndValue.get("InputDataPath"));
		params.setOutputPath(indexAndValue.get("OutputDataPath"));
		params.setUccName(indexAndValue.get("UCCName"));
		params.setInputName(inputData.substring(inputData.lastIndexOf(".") + 1));
		params.setOutputName(outputDate.substring(outputDate.lastIndexOf(".") + 1));
		logger.debug("===================初始化数据成功===================");
	}

	private void oldGenrateService(GenerateServiceParams generateServiceParams) throws Exception {
		// 开启velocity模板
		VelocityUtil vel = new VelocityUtil();
		vel.init();
		// 注册服务的类路径，按照规范该路径应为srvReqBody等报文体信息的包路径
		// com.nci.tunan.cs.impl.peripheral.ucc.r00101000859.IAccountPartDrawUCC
		String uccPackPath = generateServiceParams.getUccPackagePath();
		// accountPartDraw
		String uccMethod = generateServiceParams.getUccMethod();
		// IAccountPartDrawUCC
		String uccName = uccPackPath.substring(uccPackPath.lastIndexOf(".") + 1);

		String interfacePack = uccPackPath.replace("impl", "interfaces").replace("ucc", "exports").toLowerCase() + "."
				+ uccMethod.toLowerCase();
		// 发布服务的ws接口的类路径，在上述的路径后添加ws
		String wsPackPath = interfacePack + ".ws";
		// 发布服务的hs接口的类路径,在上述的路径后添加hs
		String hsPackPath = interfacePack + ".hs";
		// 发布服务的ws实现类的类路径，用于模板的package设置
		String wsImplPath = wsPackPath.replace("interfaces", "impl");
		// 发布服务的hs实现类的类路径，用于模板的package设置
		String hsImplPath = hsPackPath.replace("interfaces", "impl");
		// 输入的参数的名称
		String inputVo = generateServiceParams.getInputData();
		// 返回值的名称
		String outputVo = generateServiceParams.getOutputData();
		// 校验服务接口应该以大写的字母I开头
		if (!uccName.startsWith("I")) {
			logger.debug(uccName + "服务类名不是以I开头，请重新输入数据！");
			throw new RuntimeException(uccName + "服务类名不是以I开头，请重新输入数据！");
		}
		// 业务ucc的接口名称
		String interProjectName = generateServiceParams.getSystemName().toLowerCase() + "-" + "interface";
		String implProjectName = generateServiceParams.getSystemName().toLowerCase() + "-" + "impl";
		// body信息的文件地址
		String bodyDir = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				interProjectName, interfacePack);
		// webservice接口的文件地址
		String wsinterfaceDir = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				interProjectName, wsPackPath);
		// hessian接口的文件地址
		String hsinterfaceDir = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				interProjectName, hsPackPath);
		// webservice实现类的文件地址
		String wsimplDir = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				implProjectName, wsPackPath.replace("interfaces", "impl"));
		// hessian实现类的文件地址
		String hsimplDir = PublicUtil.getDirPathByWorkspaceAndPackPath(generateServiceParams.getWorkSpace(),
				implProjectName, hsPackPath.replace("interfaces", "impl"));
		// 将模板所需要的参数，放到velocity的工具中
		vel.put("uccPathAndName", uccPackPath);
		vel.put("bodyPackagePath", interfacePack);
		vel.put("wsPackagePath", wsPackPath);
		vel.put("hsPackagePath", hsPackPath);
		vel.put("wsImplPackagePath", wsImplPath);
		vel.put("hsImplPackagePath", hsImplPath);
		vel.put("inputVO", inputVo);
		vel.put("outputVO", outputVo);
		vel.put("interfaceName", uccName);
		vel.put("wsinterfacePack", wsPackPath);
		vel.put("uccMethodName", uccMethod);
		vel.put("uccName", uccName);
		// vel.put("moduleName", moduleName);
		// 注释中添加的时间
		String sysDate = PublicUtil.date2String(new Date(), "yyyy-MM-dd HH:mm:ss");
		vel.put("sysDate", sysDate);
		// 调用生成package的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为package-info.java
		vel.setTemplateString("META-INF/velocity/service/packageInfo.vm", "UTF-8");
		vel.toFileWithTemplateString(bodyDir, "package-info");
		logger.debug("生成" + bodyDir + "/package-info.java成功！");
		// 调用生成SrvReqBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvReqBody
		vel.setTemplateString("META-INF/velocity/service/srvReqBody.vm", "UTF-8");
		vel.toFileWithTemplateString(bodyDir, "SrvReqBody");
		logger.debug("生成" + bodyDir + "/SrvReqBody.java成功！");
		// 调用生成SrvReqBizBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvReqBizBody
		vel.setTemplateString("META-INF/velocity/service/srvReqBizBody.vm", "UTF-8");
		vel.toFileWithTemplateString(bodyDir, "SrvReqBizBody");
		logger.debug("生成" + bodyDir + "/SrvReqBizBody.java成功！");
		// 调用生成SrvResBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvResBody
		vel.setTemplateString("META-INF/velocity/service/srvResBody.vm", "UTF-8");
		vel.toFileWithTemplateString(bodyDir, "SrvResBody");
		logger.debug("生成" + bodyDir + "/SrvResBody.java成功！");
		// 调用生成SrvResBizBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvResBizBody
		vel.setTemplateString("META-INF/velocity/service/srvResBizBody.vm", "UTF-8");
		vel.toFileWithTemplateString(bodyDir, "SrvResBizBody");
		logger.debug("生成" + bodyDir + "/SrvResBizBody.java成功！");

		if (generateServiceParams.getAllowWebservice() != null && "Y".equals(generateServiceParams.getAllowWebservice())) {
			// 调用生成webService接口的模板，设置字符集为utf-8，文件生成的地址为wsinterfaceDir，文件名为IXXXWS
			vel.setTemplateString("META-INF/velocity/service/IWS.vm", "UTF-8");
			vel.toFileWithTemplateString(wsinterfaceDir, uccName + "WS");
			logger.debug("生成" + uccName + "WS" + ".java成功！");
			// 调用生成WebService实现类的模板，设置字符集为utf-8，文件生成的地址为wsinterfaceDir，文件名为XXXWSImpl
			vel.setTemplateString("META-INF/velocity/service/WSImpl.vm", "UTF-8");
			vel.toFileWithTemplateString(wsimplDir, uccName.substring(1) + "WSImpl");
			logger.debug("生成" + uccName.substring(1) + "WSImpl" + ".java成功！");
		} else {
			logger.debug("服务( " + generateServiceParams.getServiceName() + ") 不支持webservice调用，没有生成webservice接口和实现类");
		}
		if (generateServiceParams.getAllowHessian() != null && "Y".equals(generateServiceParams.getAllowHessian())) {
			// 调用生成Hessian接口的模板，设置字符集为utf-8，文件生成的地址为hsinterfaceDir，文件名为IXXXHS
			vel.setTemplateString("META-INF/velocity/service/IHS.vm", "UTF-8");
			vel.toFileWithTemplateString(hsinterfaceDir, uccName + "HS");
			logger.debug("生成" + uccName + "HS" + ".java成功！");
			// 调用生成Hessian实现类的模板，设置字符集为utf-8，文件生成的地址为hsinterfaceDir，文件名为XXXHSImpl
			vel.setTemplateString("META-INF/velocity/service/HSImpl.vm", "UTF-8");
			vel.toFileWithTemplateString(hsimplDir, uccName.substring(1) + "HSImpl");
			logger.debug("生成" + uccName.substring(1) + "HSImpl" + ".java成功！");
		} else {
			logger.debug("服务( " + generateServiceParams.getServiceName() + ") 不支持hessian调用，没有生成hessian接口和实现类");
		}
		String springContextDir = PublicUtil.getSpringDirPathByWorkspace(generateServiceParams.getWorkSpace(),
				implProjectName, generateServiceParams.getSystemName().toLowerCase());
		vel.setTemplateString("META-INF/velocity/service/webserviceSpring.vm", "UTF-8");
		vel.toXmlFileWithTemplateString(springContextDir, "applicationContext-auto-webservice");
		logger.debug("生成applicationContext-auto-webservice.xml成功！");
		vel.setTemplateString("META-INF/velocity/service/hessianSpring.vm", "UTF-8");
		vel.toXmlFileWithTemplateString(springContextDir, "applicationContext-auto-hessian");
		logger.debug("生成applicationContext-auto-hessian.xml成功！");
		logger.debug("Nice！生成服务发布相关类成功！");
	}

	private void writeTargetFile(String sourcePath, String targetFilePath, String fileName) throws IOException {
		// 创建目录路径
		File dir = new File(targetFilePath);
		if (!dir.exists()) {
			dir.mkdirs();
		}
		// 创建文件
		File file = new File(dir.getPath(), fileName + ".java");
		if (!file.exists()) {
			file.createNewFile();
		} 
		FileOutputStream fileOutputStream = new FileOutputStream(file);
		fileOutputStream.write(printTemplet(sourcePath).getBytes());
	}

	private String printTemplet(String sourcePath) throws IOException {
		File file = new File(sourcePath);
		FileInputStream fileInputStream = new FileInputStream(file);
		InputStreamReader reader = new InputStreamReader(fileInputStream, "UTF-8");
		char[] buffer = new char[1024];
		StringWriter writer = new StringWriter();
		int bytesRead;
		while ((bytesRead = reader.read(buffer)) != -1) {
			writer.write(buffer, 0, bytesRead);
		}
		return toFindAndConvertTemplet(writer);
	}

	private List<String> initNeedIndex(String fileContent) {
		List<String> needIndex = new ArrayList<>();
		int first = 0, second = 0;
		if (!ObjectUtils.isEmpty(fileContent)) {
			while (true) {
				first = fileContent.indexOf(FLAG, first);
				if (-1 == first)
					break;
				second = fileContent.indexOf(FLAG, first + 1);
				if (-1 == second)
					break;
				needIndex.add(fileContent.substring(first + 1, second));
				first = second + 1;
			}
		}
		return needIndex;
	}

	private String toFindAndConvertTemplet(StringWriter writer) {
		String string = writer.toString();
		List<String> needIndex = initNeedIndex(string);
		for (String param : needIndex) {
			string = string.replaceAll(param, indexAndValue.get(param));
		}
		// 去除#
		string = string.replaceAll("#", "");
		return string;
	}

	/**
	 * 使用说明 执行完类后要重新编译（右击项目-》Maven-》update project 即可）
	 * 
	 * @param args
	 * @throws Exception
	 */
	public static void main(String[] args) throws Exception {
		/**
		 * 重要！！！！！：执行该类前请确认，所需生成接口相关文件是否已创建成功，以免发生覆盖丢失类
		 */
		
		NewGenrateServiceUtil genrateServiceUtil = new NewGenrateServiceUtil();
		GenerateServiceParams generateServiceParams = new GenerateServiceParams();
		// 是否使用webservice请求方式
		generateServiceParams.setAllowWebservice("Y");
		//是否使用hessian请求方式
		generateServiceParams.setAllowHessian("N");
		//配置inputdata全路径
		generateServiceParams.setInputData("com.nci.tunan.cs.interfaces.peripheral.exports.r000000000002.vo.InputData");
		//配置outputdata全路径
		generateServiceParams.setOutputData("com.nci.tunan.cs.interfaces.peripheral.exports.r000000000002.vo.OutputData");
		//配置系统名称
		generateServiceParams.setSystemName("PA");
		//配置实现类名称
		generateServiceParams.setUccImplName("PreservationInformationUploadUCCImpl");
		//配置接口方法（仅可建立一个，如后续需要可自行建立）
		generateServiceParams.setUccMethod("uploadInfomation");
		//配置ucc接口地址全路径
		generateServiceParams.setUccPackagePath(
				"com.nci.tunan.cs.impl.peripheral.ucc.r000000000002.IPreservationInformationUploadUCC");
		//配置本地项目空间地址
		generateServiceParams.setWorkSpace("D:\\pri\\p2\\pa");
		//配置inputdata模板地址
		generateServiceParams.setInputTempletPath("C:\\Users\\<USER>\\Desktop\\InputData.txt");
		//配置OutputData模板地址
		generateServiceParams.setOutputTempletPath("C:\\Users\\<USER>\\Desktop\\OutputData.txt");
		//配置UCCImpl模板地址
		generateServiceParams.setUccImplTempletPath("C:\\Users\\<USER>\\Desktop\\UCCImpl.txt");
		//配置UCC模板地址
		generateServiceParams.setUccPath("C:\\Users\\<USER>\\Desktop\\UCC.txt");
		
		//访问方法
		genrateServiceUtil.genrateService(generateServiceParams);
		
		/**
		 * 后续模仿原有格式配置俩个xml即
		 * webservice ：applicationContext-peripheral-webservice.xml
		 * webservice : applicationContext-peripheral-ucc.xml
		 * 
		 * 整个建立接口的流程完成（后续流程优化后这步可以省略）
		 */

	}

}
