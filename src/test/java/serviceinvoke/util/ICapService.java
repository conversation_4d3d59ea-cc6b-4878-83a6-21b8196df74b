package serviceinvoke.util;

import com.nci.tunan.cap.interfaces.vo.arapbusinesstransfer.ArapBusinessTransferInputData;
import com.nci.tunan.cap.interfaces.vo.arapbusinesstransfer.ArapBusinessTransferOutputData;

public interface ICapService {
   /**
     * @description 业务转实收
     * @param inputData 输入参数VO:com.nci.tunan.cap.interfaces.vo.ArapBusinessTransferInputData
     * @return 输出的参数VO：com.nci.tunan.cap.interfaces.vo.ArapBusinessTransferOutputData  
    */
  public ArapBusinessTransferOutputData capiarapbusinesstransferuccbusinessTransfer(ArapBusinessTransferInputData inputData);

}
