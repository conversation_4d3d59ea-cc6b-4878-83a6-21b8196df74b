package serviceinvoke.util;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.serviceinvoke.entity.Service;
import com.nci.serviceinvoke.entity.ServiceConsume;
import com.nci.serviceinvoke.entity.ServicePublish;
import com.nci.udmp.tools.generate.mapper.DBConfig;
import com.nci.udmp.tools.generate.mapper.MyBatisUtil;
import com.nci.udmp.tools.generate.mapper.PublicUtil;
import com.nci.udmp.tools.generate.mapper.VelocityUtil;
import com.nci.util.LoggerFactory;

/**
 * @description 生成服务调用接口类
 * <AUTHOR>
 * @date 2015年4月22日 下午5:19:29
 */
public class GenerateServiceUtil {
    /**
     * @Fields logger : 引入日志工具
     */
    private static Logger logger = LoggerFactory.getLogger();

    /**
     * @Fields MODE : 参数定义表中的模式字段
     */
    private static final String MODE = "mode";

    /**
     * @Fields SYSTEMID : 参数定义表中对应的系统编号字段
     */
    private static final String SYSTEMID = "systemId";
    
    /** SERVICEENVPARAMAP
    * @Fields WEBSERVICE_MODE : webService调用方式的标识
    */ 
    private static final String WEBSERVICE_MODE = "W";
    
    /** 
    * @Fields HESSIAN_MODE : hessian调用方式的标识
    */ 
    private static final String HESSIAN_MODE = "H";
    
    /** 
    * @Fields API_MODE : api的调用方式的标识
    */ 
    private static final String API_MODE = "A";
    
    /** 
    * @Fields USEDYNAMICURL : 使用动态读取参数表的方式拼接URL 
    */ 
    public static final boolean USE_DYNAMIC_URL = true;
    
    /** 
    * @Fields NOT_USEDYNAMICURL : 使用静态的硬编码方式拼接URL 
    */ 
    public static final boolean NOT_USE_DYNAMIC_URL = false;

    /**
     * @description 初始化公共数据库参数，该数据库存放服务发布表t_udmp_service_publish和
     *              服务消费表t_udmp_service_consume和t_udmp_service_env环境变量表的信息
     * @version 1.0
     * @title 初始化公共数据库参数
     * <AUTHOR>
     * @return DBConfig 公共数据库的配置信息
     */
    private DBConfig initCommonDB() {
        String driverClassName = "oracle.jdbc.driver.OracleDriver";
        String url = "******************************************";
        String userName = "APP___PAS__DBUSER";
        String password = "PAS_PSWD123";
        DBConfig config = new DBConfig(driverClassName, url, userName, password);
        return config;
    }

    /**
     * @description 子系统“消费方”的数据库参数，用于查询参数定义表t_udmp_para_def的mode和systemId字段
     * @version 1.0
     * @title 子系统“消费方”的数据库参数
     * <AUTHOR>
     * @return DBConfig 子系统数据库的配置信息
     */
    public DBConfig initSystemDB() {
        String driverClassName = "oracle.jdbc.driver.OracleDriver";
        String url = "******************************************";
        String userName = "APP___PAS__DBUSER";
        String password = "PAS_PSWD123";
        DBConfig config = new DBConfig(driverClassName, url, userName, password);
        return config;
    }

    /**
     * @description 查询参数定义表mode的参数值
     * @version 1.0
     * @title 查询参数定义表mode的参数值
     * <AUTHOR>
     * @param config 数据库配置信息
     * @return 参数mode的值
     */
    private String queryParaModel(DBConfig config) {
        String model = null;
        try {
            model = MyBatisUtil.getParaValueByParaName(config, MODE);
        } catch (SQLException e) {
            logger.debug("数据源：" + config.getUrl() + "查询参数定义表的mode参数发生异常！");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return model;
    }

    /**
     * @description 查询子系统信息表
     * @version 1.0
     * @title 查询子系统信息表
     * <AUTHOR>
     * @param config 数据库配置信息
     * @return systemId的值
     */
    private String querySystemIdBySystemName(DBConfig config, String sysShortName) {
        String systemId = null;
        try {
            systemId = MyBatisUtil.querySystemIdByName(config, sysShortName);
        } catch (SQLException e) {
            logger.debug("数据源：" + config.getUrl() + "查询子系统信息定义表t_udmp_sub_system_info发生异常！");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return systemId;
    }

    /**
     * @description 查询参数定义表systemId的参数值
     * @version 1.0
     * @title 查询参数定义表systemId的参数值
     * <AUTHOR>
     * @param config 数据库配置信息
     * @return 参数systemId的值
     */
    private String queryParaSystemId(DBConfig config) {
        String systemId = null;
        try {
            systemId = MyBatisUtil.getParaValueByParaName(config, SYSTEMID);
        } catch (SQLException e) {
            logger.debug("查询参数定义表的systemId参数发生异常！");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return systemId;
    }

    /**
     * @description 查询服务消费表的数据
     * @version 1.0
     * @title 查询服务消费表的数据
     * <AUTHOR>
     * @param config 数据库配置信息
     * @return 服务消费表的中的数据ServiceConsume中包含服务消费的id、服务消费的方式、服务消费方的系统名
     */
    private List<ServiceConsume> queryServiceConsumeTable(DBConfig config, String systemName,
            List<BigDecimal> serviceIds) {
        List<ServiceConsume> serviceConsumeData = new ArrayList<ServiceConsume>();
        try {
            serviceConsumeData = MyBatisUtil.getColumnDataFromServiceConsume(config, systemName, serviceIds);
        } catch (SQLException e) {
            logger.debug("数据源：" + config.getUrl() + "查询服务消费表t_udmp_service_consume数据时发生异常");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return serviceConsumeData;
    }

    /**
     * @description 查询服务发布表的数据
     * @version 1.0
     * @title 查询服务发布表的数据
     * <AUTHOR>
     * @param config 数据库配置信息
     * @param systemName 子系统名称
     * @return 服务发布表的集合, servicePublish为服务发布对象，封装了服务的信息，包括服务名、服务类路径和类名方法名等
     */
    private List<ServicePublish> queryServicePublishData(DBConfig config, String systemName, List<BigDecimal> serviceIds) {
        List<ServicePublish> serviceInfoList = new ArrayList<ServicePublish>();
        try {
            serviceInfoList = MyBatisUtil.getDataFromServicePublish(config, systemName, serviceIds);
        } catch (SQLException e) {
            logger.debug("数据源：" + config.getUrl() + "查询服务发布表t_udmp_service_publish数据时发生异常");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return serviceInfoList;
    }

    /**
     * @description 查询服务发布表的数据
     * @version 1.0
     * @title 查询服务发布表的数据
     * <AUTHOR>
     * @param config 数据库配置信息
     * @param systemName 子系统名称
     * @return 服务发布表的集合, servicePublish为服务发布对象，封装了服务的信息，包括服务名、服务类路径和类名方法名等
     */
    private List<ServicePublish> queryAllServicePublishData(DBConfig config) {
        List<ServicePublish> serviceInfoList = new ArrayList<ServicePublish>();
        try {
            serviceInfoList = MyBatisUtil.getAllDataFromServicePublish(config);
        } catch (SQLException e) {
            logger.debug("查询服务发布表t_udmp_service_publish数据时发生异常");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return serviceInfoList;
    }

    /**
     * @description 根据服务消费的调用方式校验是否支持该方式
     * @version 1.0
     * @title
     * <AUTHOR>
     * @param config DB配置
     * @param invokeType 调用方式
     * @param serviceId 服务的id
     * @return 是否支持
     */
    private static boolean checkAllow(DBConfig config, String invokeType, BigDecimal serviceId) {
        boolean result = false;
        try {
            // 根据服务id查询服务发布记录
            ServicePublish service = MyBatisUtil.getServicePublishById(config, serviceId);
            if (null == service) {
                logger.debug("根据服务id:" + serviceId + " 查询服务为空！");
                return false;
            }
            if (null == invokeType) {
                logger.debug("服务的调用方式没有配置，请指明id为 " + serviceId + "的服务是以哪种方式调用！");
                return false;
            }
            // if(null == service.getAllowWebservice() ||
            // null == service.getAllowHessian() || null ==
            // service.getAllowApi()) {
            // logger.debug("服务id:" + serviceId +
            // "的允许webservice或允许hessian或允许api调用字段至少有一个为空，请检查是否已设置！");
            // return false;
            // }
            // 如果是W，说明消费方是以webservice的方式调用，则校验服务的是否允许allowWebservice字段是否为Y，若为Y则返回true，否则返回false
            if (WEBSERVICE_MODE.equals(invokeType.toUpperCase())) {
                if (null != service.getAllowWebservice()) {
                    if (service.getAllowWebservice().toUpperCase().equals("Y")) {
                        logger.debug("服务id：" + serviceId + "支持webservice调用！");
                        result = true;
                    } else {
                        logger.debug("服务号id：" + serviceId + "不支持webservice调用！");
                        result = false;
                    }
                } else {
                    logger.debug("服务号id：" + serviceId + "不支持webservice调用！");
                    result = false;
                }
            }
            // 如果是H，说明消费方是以hessian的方式调用，则校验服务的是否允许allowHessian字段是否为Y，若为Y则返回true，否则返回false
            else if (HESSIAN_MODE.equals(invokeType.toUpperCase())) {
                if (null != service.getAllowHessian()) {
                    if (service.getAllowHessian().toUpperCase().equals("Y")) {
                        logger.debug("服务id：" + serviceId + "支持hessian调用！");
                        result = true;
                    } else {
                        logger.debug("服务id：" + serviceId + "不支持hessian调用！");
                        result = false;
                    }
                } else {
                    logger.debug("服务id：" + serviceId + "不支持hessian调用！");
                    result = false;
                }
            }
            // 如果是A，说明消费方是以api的方式调用，则校验服务的是否允许allowApi字段是否为Y，若为Y则返回true，否则返回false
            else if (API_MODE.equals(invokeType.toUpperCase())) {
                if (null != service.getAllowApi()) {
                    if (service.getAllowApi().toUpperCase().equals("Y")) {
                        logger.debug("服务id：" + serviceId + "支持api调用！");
                        result = true;
                    } else {
                        logger.debug("服务id：" + serviceId + "不支持api调用！");
                        result = false;
                    }
                } else {
                    logger.debug("服务id：" + serviceId + "不支持api调用！");
                    result = false;
                }
            } else {
                logger.debug("调用的参数方式不正确，请检查重新输入！");
                return false;
            }
        } catch (SQLException e) {
            logger.debug("数据源：" + config.getUrl() + "根据id查询服务发布表异常！");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return result;
    }

    /**
     * @description 生成服务总接口的工具类ServiceUtils的主方法
     * @version 1.0
     * @title 生成服务总接口的工具类ServiceUtils的主方法
     * <AUTHOR> <EMAIL>
     * @param systemName 系统名称简称
     * @param workspacePath 指定的生成路径
     * @param serviceIds 指定要生成服务接口的id列表
    */
    public void generateServiceUtilMethod(String systemName, String workspacePath, List<BigDecimal> serviceIds) {
        // 获得公共数据库的配置信息
        DBConfig commonConfig = initCommonDB();
        // 获得子项目的数据库配置信息
        DBConfig systemConfig = initSystemDB();
        // 根据子项目的名称在公共的数据源中查询服务消费表的数据
        List<ServiceConsume> serviceConsumeList = queryServiceConsumeTable(commonConfig, systemName, serviceIds);
        // 如果服务消费表中没有对应的子系统注册的服务，则返回
        if (null == serviceConsumeList || "".equals(serviceConsumeList) || serviceConsumeList.size() == 0) {
            logger.debug("数据源：" + commonConfig.getUrl() + "服务消费表中没有子系统" + systemName + "的数据！");
            return;
        }
        // 遍历各个子系统所要调用的服务的名称和类型，与服务发布表进行匹配，若匹配成功，则按照配置服务的调用方式生成对应的接口方法
        List<Map<String, ServicePublish>> invoketypeAndServiceInfoList = new ArrayList<Map<String, ServicePublish>>();
        for (ServiceConsume serviceConsume : serviceConsumeList) {
            // 子系统所需要调用的服务的id
            BigDecimal serviceId = serviceConsume.getServiceId();
            // 子系统调用该服务的方式，指定一种调用类型，W为webservcice，H为hessian，A为API方式
            String invokeType = serviceConsume.getServiceType();
            ServicePublish info;
            try {
                info = MyBatisUtil.getServicePublishById(commonConfig, serviceId);
                if (checkAllow(commonConfig, invokeType, info.getServiceId())) {
                    Map<String, ServicePublish> typeInfoMap = new HashMap<String, ServicePublish>();
                    //使用静态的模式，将服务调用的URL查询出来，硬编码在生成的类中
                    String mode = queryParaModel(systemConfig);
                    // String sysId = queryParaSystemId(systemConfig);
                    String sysId = querySystemIdBySystemName(systemConfig, info.getSystemName());
                    Service service = new Service();
                    // 查询该系统的ip、port、context等信息，拼接成url
                    try {
                        service = MyBatisUtil.getDataFromService(commonConfig, mode, sysId);
                        if (null == service) {
                            logger.debug("数据源：" + commonConfig.getUrl() + "根据系统当前的模式参数mode:" + mode + "和子系统systemId:"
                                    + sysId + "，查询服务环境表t_udmp_service_env表没有对应的数据！");
                            continue;
                        }
                        String url = "http://" + service.getServiceIp() + ":" + service.getServicePort() + "/"
                                + service.getServiceContext() + "/";
                        logger.debug("子系统" + service.getSystemId() + "在" + service.getServiceType() + "环境下的url为：" + url);
                        // servicePublish中添加url属性
                        info.setUrl(url);
                    } catch (SQLException e) {
                        logger.debug("数据源：" + commonConfig.getUrl() + "根据系统的当前模式" + mode + "和系统的" + sysId
                                + "，查询t_udmp_service_env表发生异常！");
                        e.printStackTrace();
                        break;
                    }
                    typeInfoMap.put(invokeType, info);
                    invoketypeAndServiceInfoList.add(typeInfoMap);
                }
            } catch (SQLException e1) {
                logger.debug("数据源：" + commonConfig.getUrl() + "根据id查询t_udmp_service_publish表发生异常！");
                e1.printStackTrace();
                break;
            }
        }
        if (null == invoketypeAndServiceInfoList || invoketypeAndServiceInfoList.size() == 0) {
            logger.debug("由于未查询到相应的服务消费记录或服务不支持指定的调用方式，生成服务工具类失败！");
            return;
        }
        useVeloToCreateConsumeCode(systemName, workspacePath, invoketypeAndServiceInfoList);
    }

    /**
     * @description 生成服务总接口的工具类ServiceUtils的主方法
     * @version 1.0
     * @title 生成服务总接口的工具类ServiceUtils的主方法
     * <AUTHOR> <EMAIL>
     * @param systemName 系统名称简称
     * @param workspacePath 指定的生成路径
     * @param serviceIds 指定要生成服务接口的id列表
     * @param useDynamicURL 是否使用动态拼接ip的方式
    */
    public void generateServiceUtilMethod(String systemName, String workspacePath, List<BigDecimal> serviceIds, boolean useDynamicURL) {
        // 获得公共数据库的配置信息
        DBConfig commonConfig = initCommonDB();
        // 获得子项目的数据库配置信息
        DBConfig systemConfig = initSystemDB();
        // 根据子项目的名称在公共的数据源中查询服务消费表的数据
        List<ServiceConsume> serviceConsumeList = queryServiceConsumeTable(commonConfig, systemName, serviceIds);
        // 如果服务消费表中没有对应的子系统注册的服务，则返回
        if (null == serviceConsumeList || "".equals(serviceConsumeList) || serviceConsumeList.size() == 0) {
            logger.debug("数据源：" + commonConfig.getUrl() + "服务消费表中没有子系统" + systemName + "的数据！");
            return;
        }
        // 遍历各个子系统所要调用的服务的名称和类型，与服务发布表进行匹配，若匹配成功，则按照配置服务的调用方式生成对应的接口方法
        List<Map<String, ServicePublish>> invoketypeAndServiceInfoList = new ArrayList<Map<String, ServicePublish>>();
        for (ServiceConsume serviceConsume : serviceConsumeList) {
            // 子系统所需要调用的服务的id
            BigDecimal serviceId = serviceConsume.getServiceId();
            // 子系统调用该服务的方式，指定一种调用类型，W为webservcice，H为hessian，A为API方式
            String invokeType = serviceConsume.getServiceType();
            ServicePublish info;
            try {
                info = MyBatisUtil.getServicePublishById(commonConfig, serviceId);
                if (checkAllow(commonConfig, invokeType, info.getServiceId())) {
                    Map<String, ServicePublish> typeInfoMap = new HashMap<String, ServicePublish>();
                    //拼接URL时使用动态方式，从Constants中的SERVICEENVPARAMAP中获取
                    if(useDynamicURL) {
                        logger.debug("===使用动态读取map中环境变量的方式，拼接服务调用的URL===");
                        String url = "useDynamicURLConstants.SERVICEENVPARAMAP.get(" + "\"" + info.getSystemName().toUpperCase() + "\"" + ")"; 
                        info.setUrl(url);
                    } else { //使用静态的模式，将服务调用的URL查询出来，硬编码在生成的类中
                        logger.debug("===使用静态硬编码的方式，拼接服务调用的URL===");
                        String mode = queryParaModel(systemConfig);
                        // String sysId = queryParaSystemId(systemConfig);
                        String sysId = querySystemIdBySystemName(systemConfig, info.getSystemName());
                        Service service = new Service();
                        // 查询该系统的ip、port、context等信息，拼接成url
                        try {
                            service = MyBatisUtil.getDataFromService(commonConfig, mode, sysId);
                            if (null == service) {
                                logger.debug("数据源：" + commonConfig.getUrl() + "根据系统当前的模式参数mode:" + mode + "和子系统systemId:"
                                        + sysId + "，查询服务环境表t_udmp_service_env表没有对应的数据！");
                                continue;
                            }
                            String url = "http://" + service.getServiceIp() + ":" + service.getServicePort() + "/"
                                    + service.getServiceContext() + "/";
                            logger.debug("子系统" + service.getSystemId() + "在" + service.getServiceType() + "环境下的url为：" + url);
                            // servicePublish中添加url属性
                            info.setUrl(url);
                        } catch (SQLException e) {
                            logger.debug("数据源：" + commonConfig.getUrl() + "根据系统的当前模式" + mode + "和系统的" + sysId
                                    + "，查询t_udmp_service_env表发生异常！");
                            e.printStackTrace();
                            break;
                        }
                    }
                    typeInfoMap.put(invokeType, info);
                    invoketypeAndServiceInfoList.add(typeInfoMap);
                }
            } catch (SQLException e1) {
                logger.debug("数据源：" + commonConfig.getUrl() + "根据id查询t_udmp_service_publish表发生异常！");
                e1.printStackTrace();
                break;
            }
        }
        if (null == invoketypeAndServiceInfoList || invoketypeAndServiceInfoList.size() == 0) {
            logger.debug("由于未查询到相应的服务消费记录或服务不支持指定的调用方式，生成服务工具类失败！");
            return;
        }
        useVeloToCreateConsumeCode(systemName, workspacePath, invoketypeAndServiceInfoList);
    }
    
    /**
     * @description 通过系统名称简称和指定的生成路径，生成服务消费方代码
     * @version 1.0
     * @title 通过系统名称简称和指定的生成路径，生成服务消费方代码
     * <AUTHOR> <EMAIL>
     * @param systemName 系统名称简称
     * @param workspacePath 指定的生成路径
     * @param invoketypeAndServiceInfoList 调用方式和服务信息列表 
    */
    private void useVeloToCreateConsumeCode(String systemName, String workspacePath,
            List<Map<String, ServicePublish>> invoketypeAndServiceInfoList) {
        try {
            // 开启velocity模板
            VelocityUtil vel = new VelocityUtil();
            vel.init();
            // 生成的总接口方法的包路径:com.nci.udmp.serviceinvoke.util
            // String packageName = JavaFileConst.SERVICE_UTIL_PATH;
            String packageName = "com.nci.tunan." + systemName.toLowerCase() + ".serviceinvoke.util";
            // 生成的总接口方法的文件地址
            // String javaFileDir =
            // PublicUtil.getJavaDirPathFromPackPath(packageName);
            String javaFileDir = PublicUtil.getDirPathByWorkspaceAndPackPath(workspacePath, systemName.toLowerCase()
                    + "-impl", packageName);
            vel.put("invoketypeAndServiceInfoList", invoketypeAndServiceInfoList);
            vel.put("destSysName", systemName);
            // 注释中添加的时间
            String sysDate = PublicUtil.date2String(new Date(), "yyyy-MM-dd HH:mm:ss");
            vel.put("sysDate", sysDate);
            // 将总接口方法的包路径添加到模板中
            // vel.put("packagePath", JavaFileConst.SERVICE_UTIL_PATH);
            vel.put("packagePath", packageName);

            vel.setTemplateString("META-INF/velocity/service/IServiceUtils.vm", "UTF-8");
            vel.toFileWithTemplateString(javaFileDir, "I" + systemName.toUpperCase() + "ServiceUtils");
            logger.debug("生成服务调用总接口" + "I" + systemName.toUpperCase() + "ServiceUtils成功！");

            // 调用服务工具类模板，设置编码为UTF-8
            vel.setTemplateString("META-INF/velocity/service/serviceUtils.vm", "UTF-8");
            // 将接口类生成到相应的路径下，第一个参数为生成的地址，第二个参数为生成的文件的名称
            vel.toFileWithTemplateString(javaFileDir, systemName.toUpperCase() + "ServiceUtils");
            logger.debug("生成服务调用总接口" + systemName.toUpperCase() + "ServiceUtils成功！");

            String springContextDir = PublicUtil.getSpringDirPathByWorkspace(workspacePath, systemName.toLowerCase()
                    + "-impl", systemName.toLowerCase());
            vel.setTemplateString("META-INF/velocity/service/consumeSpring.vm", "UTF-8");
            vel.toXmlFileWithTemplateString(springContextDir, "applicationContext-autobean");
            logger.debug("生成applicationContext-autobean.xml成功！");
        } catch (Exception e) {
            logger.debug("生成服务调用总接口失败！");
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * @description 生成服务发布方相关代码
     * @version 1.0
     * @title 生成服务发布方相关代码
     * <AUTHOR>
     */
    public void generateServicePublish(String systemName, String workspacePath, List<BigDecimal> serviceIds) {
        // 获得数据库配置信息
        DBConfig commonConfig = initCommonDB();
        // 查询t_udmp_service_publish表的数据，返回servicePublish对象的列表
        List<ServicePublish> serviceInfoList = queryServicePublishData(commonConfig, systemName, serviceIds);
        String springPath = null;
        try {
            // 开启velocity模板
            VelocityUtil vel = new VelocityUtil();
            vel.init();
            // 循环遍历查询的serviceInfo列表，生成服务发布需要的信息
            for (ServicePublish info : serviceInfoList) {
                // 注册服务的类路径，按照规范该路径应为srvReqBody等报文体信息的包路径
                String uccPackPath = info.getBizUccClassPath();
                // String moduleName =
                // uccPackPath.substring(uccPackPath.indexOf("impl") + 5,
                // uccPackPath.indexOf("ucc") - 1);
                String uccMethod = info.getBizUccMethodName();
                String uccName = uccPackPath.substring(uccPackPath.lastIndexOf(".") + 1);
                // String interfacePack = uccPackPath.substring(0,
                // uccPackPath.lastIndexOf(".")) + ".interfaces."
                // + uccName.toLowerCase() + ".exports." +
                // uccMethod.toLowerCase();
                String interfacePack = uccPackPath.replace("impl", "interfaces").replace("ucc", "exports")
                        .toLowerCase()
                        + "." + uccMethod.toLowerCase();
                // 发布服务的ws接口的类路径，在上述的路径后添加ws
                String wsPackPath = interfacePack + ".ws";
                // 发布服务的hs接口的类路径,在上述的路径后添加hs
                String hsPackPath = interfacePack + ".hs";
                // 发布服务的ws实现类的类路径，用于模板的package设置
                String wsImplPath = wsPackPath.replace("interfaces", "impl");
                // 发布服务的hs实现类的类路径，用于模板的package设置
                String hsImplPath = hsPackPath.replace("interfaces", "impl");
                // 输入的参数的名称
                String inputVo = info.getBizParamter();
                // 返回值的名称
                String outputVo = info.getBizReturnParamter();
                // 校验服务接口应该以大写的字母I开头
                if (!uccName.startsWith("I")) {
                    logger.debug(uccName + "服务类名不是以I开头，请重新输入数据！");
                    throw new RuntimeException(uccName + "服务类名不是以I开头，请重新输入数据！");
                }
                // 业务ucc的接口名称
                String uccBeanName = info.getBizUccBeanName();
                String interProjectName = info.getSystemName().toLowerCase() + "-" + "interface";
                String implProjectName = info.getSystemName().toLowerCase() + "-" + "impl";
                springPath = implProjectName;
                // body信息的文件地址
                String bodyDir = PublicUtil.getDirPathByWorkspaceAndPackPath(workspacePath, interProjectName,
                        interfacePack);
                // webservice接口的文件地址
                String wsinterfaceDir = PublicUtil.getDirPathByWorkspaceAndPackPath(workspacePath, interProjectName,
                        wsPackPath);
                // hessian接口的文件地址
                String hsinterfaceDir = PublicUtil.getDirPathByWorkspaceAndPackPath(workspacePath, interProjectName,
                        hsPackPath);
                // webservice实现类的文件地址
                String wsimplDir = PublicUtil.getDirPathByWorkspaceAndPackPath(workspacePath, implProjectName,
                        wsPackPath.replace("interfaces", "impl"));
                // hessian实现类的文件地址
                String hsimplDir = PublicUtil.getDirPathByWorkspaceAndPackPath(workspacePath, implProjectName,
                        hsPackPath.replace("interfaces", "impl"));
                // 将模板所需要的参数，放到velocity的工具中
                vel.put("uccPathAndName", uccPackPath);
                vel.put("bodyPackagePath", interfacePack);
                vel.put("wsPackagePath", wsPackPath);
                vel.put("hsPackagePath", hsPackPath);
                vel.put("wsImplPackagePath", wsImplPath);
                vel.put("hsImplPackagePath", hsImplPath);
                vel.put("inputVO", inputVo);
                vel.put("outputVO", outputVo);
                vel.put("interfaceName", uccName);
                vel.put("wsinterfacePack", wsPackPath);
                vel.put("uccMethodName", uccMethod);
                vel.put("uccName", uccName);
                // vel.put("moduleName", moduleName);
                // 注释中添加的时间
                String sysDate = PublicUtil.date2String(new Date(), "yyyy-MM-dd HH:mm:ss");
                vel.put("sysDate", sysDate);
                // 调用生成package的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为package-info.java
                vel.setTemplateString("META-INF/velocity/service/packageInfo.vm", "UTF-8");
                vel.toFileWithTemplateString(bodyDir, "package-info");
                logger.debug("生成" + bodyDir + "/package-info.java成功！");
                // 调用生成SrvReqBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvReqBody
                vel.setTemplateString("META-INF/velocity/service/srvReqBody.vm", "UTF-8");
                vel.toFileWithTemplateString(bodyDir, "SrvReqBody");
                logger.debug("生成" + bodyDir + "/SrvReqBody.java成功！");
                // 调用生成SrvReqBizBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvReqBizBody
                vel.setTemplateString("META-INF/velocity/service/srvReqBizBody.vm", "UTF-8");
                vel.toFileWithTemplateString(bodyDir, "SrvReqBizBody");
                logger.debug("生成" + bodyDir + "/SrvReqBizBody.java成功！");
                // 调用生成SrvResBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvResBody
                vel.setTemplateString("META-INF/velocity/service/srvResBody.vm", "UTF-8");
                vel.toFileWithTemplateString(bodyDir, "SrvResBody");
                logger.debug("生成" + bodyDir + "/SrvResBody.java成功！");
                // 调用生成SrvResBizBody的模板，设置字符集为utf-8，文件生成的地址为bodyDir，文件名为SrvResBizBody
                vel.setTemplateString("META-INF/velocity/service/srvResBizBody.vm", "UTF-8");
                vel.toFileWithTemplateString(bodyDir, "SrvResBizBody");
                logger.debug("生成" + bodyDir + "/SrvResBizBody.java成功！");

                if (info.getAllowWebservice() != null && "Y".equals(info.getAllowWebservice())) {
                    // 调用生成webService接口的模板，设置字符集为utf-8，文件生成的地址为wsinterfaceDir，文件名为IXXXWS
                    vel.setTemplateString("META-INF/velocity/service/IWS.vm", "UTF-8");
                    vel.toFileWithTemplateString(wsinterfaceDir, uccName + "WS");
                    logger.debug("生成" + uccName + "WS" + ".java成功！");
                    // 调用生成WebService实现类的模板，设置字符集为utf-8，文件生成的地址为wsinterfaceDir，文件名为XXXWSImpl
                    vel.setTemplateString("META-INF/velocity/service/WSImpl.vm", "UTF-8");
                    vel.toFileWithTemplateString(wsimplDir, uccName.substring(1) + "WSImpl");
                    logger.debug("生成" + uccName.substring(1) + "WSImpl" + ".java成功！");
                } else {
                    logger.debug("服务( " + info.getServiceId() + "-" + info.getServiceName()
                            + ") 不支持webservice调用，没有生成webservice接口和实现类");
                }
                if (info.getAllowHessian() != null && "Y".equals(info.getAllowHessian())) {
                    // 调用生成Hessian接口的模板，设置字符集为utf-8，文件生成的地址为hsinterfaceDir，文件名为IXXXHS
                    vel.setTemplateString("META-INF/velocity/service/IHS.vm", "UTF-8");
                    vel.toFileWithTemplateString(hsinterfaceDir, uccName + "HS");
                    logger.debug("生成" + uccName + "HS" + ".java成功！");
                    // 调用生成Hessian实现类的模板，设置字符集为utf-8，文件生成的地址为hsinterfaceDir，文件名为XXXHSImpl
                    vel.setTemplateString("META-INF/velocity/service/HSImpl.vm", "UTF-8");
                    vel.toFileWithTemplateString(hsimplDir, uccName.substring(1) + "HSImpl");
                    logger.debug("生成" + uccName.substring(1) + "HSImpl" + ".java成功！");
                } else {
                    logger.debug("服务( " + info.getServiceId() + "-" + info.getServiceName()
                            + ") 不支持hessian调用，没有生成hessian接口和实现类");
                }
            }
            vel.put("serviceInfoList", serviceInfoList);
            String springContextDir = PublicUtil.getSpringDirPathByWorkspace(workspacePath, springPath,
                    systemName.toLowerCase());
            vel.setTemplateString("META-INF/velocity/service/webserviceSpring.vm", "UTF-8");
            vel.toXmlFileWithTemplateString(springContextDir, "applicationContext-auto-webservice");
            logger.debug("生成applicationContext-auto-webservice.xml成功！");
            vel.setTemplateString("META-INF/velocity/service/hessianSpring.vm", "UTF-8");
            vel.toXmlFileWithTemplateString(springContextDir, "applicationContext-auto-hessian");
            logger.debug("生成applicationContext-auto-hessian.xml成功！");
            logger.debug("Nice！生成服务发布相关类成功！");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("生成服务发布相关类异常！");
            throw new RuntimeException(e);
        }
    }

    /**
     * @description 执行main方法，生成服务发布的代码和服务工具类
     * @version 1.0
     * @title 执行main方法，生成服务发布的代码和服务工具类
     * @param args 参数
     */
    public static void main(String[] args) {
        GenerateServiceUtil util = new GenerateServiceUtil();
        // 生成服务工具类,根据需求修改子系统名称参数
//         List consumeIdList = Arrays.asList(627);
//        List consumeIdList = new ArrayList();
//        consumeIdList.add(425);
//        consumeIdList.add(429);
//        consumeIdList.add(529);
//        consumeIdList.add(589);
//        consumeIdList.add(604);
//        consumeIdList.add(627);
//        consumeIdList.add(1198);
//        consumeIdList.add(1199);
//        consumeIdList.add(1209);
         //测试动态
//         util.generateServiceUtilMethod("css", "E:\\cssProject\\", consumeIdList, USE_DYNAMIC_URL);
        //测试静态
//        util.generateServiceUtilMethod("css", "E:\\css_project", consumeIdList,true);
        /**
         * 生成服务发布方代码, 第一个参数为：子系统名称，第二个参数为：文件生成的路径地址
         * 可以指定workspace路径，例如：“D:\\workspace_nb\\core\\nb”
         * 也可以指定临时的地址，例如：“D:\\test\\nb”
         */
         List publishIdList = Arrays.asList(321490);
////         List publishIdList = new ArrayList();E:\css_project
         util.generateServicePublish("pa", "E:\\getInterface", publishIdList);
    }
}
