import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.interfaces.serviceData.iucsuw.*;
import com.nci.tunan.cs.model.bo.CsPolicyAccountStreamBO;
import com.nci.tunan.cs.model.bo.UwDecisionReasonBO;
import com.nci.tunan.cs.model.bocomp.uwtocsbo.*;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.cs.model.po.UwMasterPO;
import com.nci.tunan.cs.model.vo.SurrenderVO;
import com.nci.tunan.pa.interfaces.model.bo.RiskAmountBO;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.DateUtilsEx;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @description test
 * @.belongToModule cs-保全子系统
 * @date 2022年03月23日  14:26
 */
public class test {
    
    public static void main(String[] args) {
        test t = new test();
        t.tt();
    }

    private  void tt() {
        /**
         * 1、看当前客户保单库是否有588产品保单
         * 2、契约在途的有没有588保单
         * 3、如果保单或契约有588保单做以下处理
         *  3.1、将契约、保单、保全在途数据进行汇总统一格式交收累计方法
         * 4、
         */
        
       //查客户已转保单的保单号对应有588产品  
        //用busi_item_id 查责任
        
        List<SurrenderVO> surrenderVOS = new ArrayList<>();
        SurrenderVO surrenderVO = new SurrenderVO();
        surrenderVO.setAcceptCode("tt");
        surrenderVOS.add(surrenderVO);
        this.tt1(surrenderVOS);
        System.out.println(surrenderVOS.get(0).getAcceptCode());
    }
    
    private  void tt1(List<SurrenderVO> surrenderSaveVOs){
        surrenderSaveVOs.get(0).setAcceptCode("tt1");
        this.tt2(surrenderSaveVOs);
    }

    private  void tt2(List<SurrenderVO> surrenderSaveVOs){
        surrenderSaveVOs.get(0).setAcceptCode("tt2");
    }
}